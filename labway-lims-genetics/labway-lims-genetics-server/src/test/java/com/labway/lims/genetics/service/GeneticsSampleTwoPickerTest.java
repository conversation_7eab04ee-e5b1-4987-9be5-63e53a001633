package com.labway.lims.genetics.service;

import com.labway.lims.apply.api.dto.SampleTwoPickDto;
import com.labway.lims.apply.api.service.ISampleTwoPicker;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest
class GeneticsSampleTwoPickerTest {
    @DubboReference
    private ISampleTwoPicker iSampleTwoPicker;

    @Test
    void twoPick() {
        SampleTwoPickDto twoPickDto = new SampleTwoPickDto();
        twoPickDto.setApplySampleId(124464133723210162L);
        twoPickDto.setSampleNo("test001");
        twoPickDto.setInstrumentGroupId(120844137851568207L);
        iSampleTwoPicker.twoPick(twoPickDto);
    }
}