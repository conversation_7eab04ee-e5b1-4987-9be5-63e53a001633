<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.genetics.mapper.GeneticsSampleMapper">

    <update id="updateByGeneticsSampleIds">
        update tb_genetics_sample
        <set>
            <if test="geneticsSampleDto.oneCheckerId != null">
                one_checker_id = #{geneticsSampleDto.oneCheckerId},
            </if>
            <if test="geneticsSampleDto.oneCheckerName != null">
                one_checker_name = #{geneticsSampleDto.oneCheckerName},
            </if>
            <if test="geneticsSampleDto.oneCheckDate != null">
                one_check_date = #{geneticsSampleDto.oneCheckDate},
            </if>
            <if test="geneticsSampleDto.twoCheckerId != null">
                two_checker_id = #{geneticsSampleDto.twoCheckerId},
            </if>
            <if test="geneticsSampleDto.twoCheckerName != null">
                two_checker_name = #{geneticsSampleDto.twoCheckerName},
            </if>
            <if test="geneticsSampleDto.twoCheckDate != null">
                two_check_date = #{geneticsSampleDto.twoCheckDate},
            </if>
        </set>
        where genetics_sample_id in
        <foreach collection="geneticsSampleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
