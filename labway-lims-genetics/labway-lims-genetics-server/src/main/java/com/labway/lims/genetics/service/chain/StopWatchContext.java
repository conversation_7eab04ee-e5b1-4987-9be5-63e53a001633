package com.labway.lims.genetics.service.chain;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import lombok.Getter;
import org.apache.commons.chain.impl.ContextBase;

/**
 * StopWatchContext
 *
 * <AUTHOR>
 */
@Getter
public abstract class StopWatchContext extends ContextBase {
    private static final String WATCH = "WATCH_" + IdUtil.objectId();

    public StopWatchContext() {
        put(WATCH, new StopWatch(getWatcherName()));
    }

    public StopWatch getWatch() {
        return (StopWatch)get(WATCH);
    }

    protected abstract String getWatcherName();
}
