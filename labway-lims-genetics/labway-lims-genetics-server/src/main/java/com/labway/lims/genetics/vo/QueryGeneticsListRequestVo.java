package com.labway.lims.genetics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * 遗传样本查询 List
 * 
 * <AUTHOR>
 * @since 2023/3/27 18:45
 */
@Data
public class QueryGeneticsListRequestVo {
    /**
     * 检验日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateEnd;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 样本状态
     *
     * @see SampleStatusEnum
     */
    private String sampleStatus;
}
