package com.labway.lims.genetics.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.FinanceSampleLockService;
import com.labway.lims.base.api.service.InstrumentReportItemResultTipService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.genetics.api.dto.GeneticsSampleAuditDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;
import com.labway.lims.genetics.api.dto.SelectGeneticsSampleDto;
import com.labway.lims.genetics.api.service.GeneticsSampleResultService;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.genetics.mapstruct.GeneticsSampleConverter;
import com.labway.lims.genetics.vo.CancelCheckRequestVo;
import com.labway.lims.genetics.vo.GeneticsSampleAuditVo;
import com.labway.lims.genetics.vo.GeneticsSampleListItemVo;
import com.labway.lims.genetics.vo.QueryGeneticsListRequestVo;
import com.labway.lims.genetics.vo.UpdateGeneticsSampleResultVo;
import com.labway.lims.routine.api.service.SampleReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.api.consts.MqConstants.EXCHANGE;
import static com.labway.lims.api.consts.MqConstants.ROUTING_KEY;

/**
 * 遗传样本 API
 */
@Slf4j
@RestController
@RequestMapping("/genetics-sample")
public class GeneticsSampleController extends BaseController {

    @Resource
    private GeneticsSampleService geneticsSampleService;
    @Resource
    private GeneticsSampleResultService geneticsSampleResultService;

    @Resource
    private GeneticsSampleConverter geneticsSampleConverter;

    @DubboReference
    private UserService userService;
    @DubboReference
    private FinanceSampleLockService financeSampleLockService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;

    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private InstrumentReportItemResultTipService instrumentReportItemResultTipService;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private EnvDetector envDetector;

    /**
     * 列表查询
     */
    @PostMapping("/genetics-list")
    public Object queryGeneticsList(@RequestBody QueryGeneticsListRequestVo vo) {
        SelectGeneticsSampleDto dto = geneticsSampleConverter.selectGeneticsSampleDtoFromRequestVo(vo);
        String sampleStatus = vo.getSampleStatus();
        final LoginUserHandler.User user = LoginUserHandler.get();
        dto.setOrgId(user.getOrgId());
        dto.setGroupId(user.getGroupId());

        // 对应遗传样本
        final List<GeneticsSampleDto> geneticsSampleDtos = geneticsSampleService.selectBySelectGeneticsSampleDto(dto);
        if (CollectionUtils.isEmpty(geneticsSampleDtos)) {
            return Collections.emptyList();
        }

        // 遗传样本 对应申请单ids 申请单样本ids
        final Set<Long> applyIds =
            geneticsSampleDtos.stream().map(GeneticsSampleDto::getApplyId).collect(Collectors.toSet());
        final Set<Long> applySampleIds =
            geneticsSampleDtos.stream().map(GeneticsSampleDto::getApplySampleId).collect(Collectors.toSet());

        // 对应申请单 信息
        final List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);
        Map<Long, ApplyDto> applyByApplyId =
            applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        // 对应 申请单样本信息
        final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);
        Map<Long, ApplySampleDto> applySampleByApplySampleId =
            applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));

        // 对应已审申请单样本
        List<Long> auditApplySampleId =
            applySampleDtos.stream().filter(obj -> Objects.equals(obj.getStatus(), SampleStatusEnum.AUDIT.getCode()))
                .map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());

        // 已审申请单样本 对应报告项目
        final List<SampleReportDto> sampleReportDtoList =
            sampleReportService.selectByApplySampleIds(auditApplySampleId);
        Map<Long, SampleReportDto> reportDtoByApplySampleId = sampleReportDtoList.stream()
            .collect(Collectors.toMap(SampleReportDto::getApplySampleId, Function.identity()));

        List<GeneticsSampleListItemVo> targetList = Lists.newArrayListWithCapacity(geneticsSampleDtos.size());

        geneticsSampleDtos.forEach(item -> {
            GeneticsSampleListItemVo temp = geneticsSampleConverter.geneticsSampleListItemVoFromTbDto(item);

            // 对应申请单信息
            ApplyDto applyDto = applyByApplyId.get(item.getApplyId());
            if (Objects.nonNull(applyDto)) {
                temp.setPatientName(applyDto.getPatientName());
                temp.setApplyType(applyDto.getApplyTypeName());
            }

            // 对应申请单样本信息
            ApplySampleDto applySampleDto = applySampleByApplySampleId.get(item.getApplySampleId());
            if (Objects.nonNull(applySampleDto)) {
                temp.setIsPrint(applySampleDto.getIsPrint());
                temp.setStatus(applySampleDto.getStatus());
                temp.setRecordDate(applySampleDto.getCreateDate());
                temp.setUrgent(applySampleDto.getUrgent());
                temp.setTwoPickDate(applySampleDto.getTwoPickDate());
                // 免疫二次分拣标记 # 1.1.3.7
                temp.setIsImmunityTwoPick(applySampleDto.getIsImmunityTwoPick());
            }

            // 对应报告
            SampleReportDto sampleReportDto = reportDtoByApplySampleId.get(item.getApplySampleId());
            if (Objects.nonNull(sampleReportDto)) {
                temp.setFileType(sampleReportDto.getFileType());
                temp.setUrl(sampleReportDto.getUrl());
            }
            if (filterByStatus(sampleStatus, temp)) {
                return;
            }
            targetList.add(temp);

        });

        // 二次分拣时间 升序
        return targetList.stream()
            .sorted(Comparator.comparing(o -> ObjectUtils.defaultIfNull(o.getTwoPickDate(), new Date())))
            .collect(Collectors.toList());
    }

    /**
     * 详情查询
     */
    @PostMapping("/genetics-detail")
    public Object queryGeneticsResultDetail(@RequestParam("geneticsSampleId") long geneticsSampleId) {

        GeneticsSampleDto geneticsSampleDto = geneticsSampleService.selectByGeneticsSampleId(geneticsSampleId);
        if (Objects.isNull(geneticsSampleDto)) {
            throw new LimsException("无效样本");
        }
        GeneticsSampleResultDto target = geneticsSampleResultService.selectByGeneticsSampleId(geneticsSampleId);
        if (Objects.isNull(target)) {
            // 此时还没有结果 将 体检样本id 放上去
            target = new GeneticsSampleResultDto();
            target.setGeneticsSampleId(geneticsSampleId);
        }
        return target;
    }

    /**
     * 更新详情
     */
    @PostMapping("/update-genetics-detail")
    public Object updateGeneticsResultDetail(@RequestBody UpdateGeneticsSampleResultVo vo) {
        // 判断入参
        validUpdateGeneticsResultParam(vo);

        final GeneticsSampleDto geneticsSampleDto =
            geneticsSampleService.selectByGeneticsSampleId(vo.getGeneticsSampleId());
        if (Objects.isNull(geneticsSampleDto)) {
            throw new IllegalStateException("遗传样本不存在");
        }

        if (applySampleService.isDisabled(geneticsSampleDto.getApplySampleId())) {
            throw new IllegalStateException("样本已禁用");
        }

        if (applySampleService.isTerminate(geneticsSampleDto.getApplySampleId())) {
            throw new IllegalStateException("样本已经终止检验");
        }

        // 对应申请单样本
        final ApplySampleDto applySampleDto =
            applySampleService.selectByApplySampleId(geneticsSampleDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("遗传样本对应申请单样本不存在");
        }
        if (!Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
            throw new IllegalStateException("只有未审的样本可以修改详情");
        }

        // 遗传样本 对应 遗传样本结果
        final GeneticsSampleResultDto geneticsSampleResultDto =
            geneticsSampleResultService.selectByGeneticsSampleId(vo.getGeneticsSampleId());

        GeneticsSampleResultDto target = new GeneticsSampleResultDto();

        // 更新项或新增项
        target.setCellCount(StringUtils.defaultString(vo.getCellCount()));
        target.setAnalyseCellCount(StringUtils.defaultString(vo.getAnalyseCellCount()));
        target.setSampleSituation(StringUtils.defaultString(vo.getSampleSituation()));
        target.setKaryotype(StringUtils.defaultString(vo.getKaryotype()));
        target.setBandingMethod(StringUtils.defaultString(vo.getBandingMethod()));
        target.setBandingLevel(StringUtils.defaultString(vo.getBandingLevel()));
        target.setAnalyticalOpinion(StringUtils.defaultString(vo.getAnalyticalOpinion()));
        target.setKaryotypeImg1(StringUtils.defaultString(vo.getKaryotypeImg1()));
        target.setKaryotypeImg2(StringUtils.defaultString(vo.getKaryotypeImg2()));
        target.setKaryotypeImg3(StringUtils.defaultString(vo.getKaryotypeImg3()));
        target.setKaryotypeOriginalImg1(StringUtils.defaultString(vo.getKaryotypeOriginalImg1()));
        target.setKaryotypeOriginalImg2(StringUtils.defaultString(vo.getKaryotypeOriginalImg2()));
        target.setKaryotypeOriginalImg3(StringUtils.defaultString(vo.getKaryotypeOriginalImg3()));

        if (Objects.isNull(geneticsSampleResultDto)) {
            // 第一次结果新增
            target.setApplyId(geneticsSampleDto.getApplyId());
            target.setGeneticsSampleId(geneticsSampleDto.getGeneticsSampleId());
            target.setApplySampleId(geneticsSampleDto.getApplySampleId());
        } else {
            // 结果已存在 更新
            target.setGeneticsSampleResultId(geneticsSampleResultDto.getGeneticsSampleResultId());
            target.setGeneticsSampleId(geneticsSampleResultDto.getGeneticsSampleId());
        }

        geneticsSampleResultService.updateGeneticsResultDetail(geneticsSampleDto, geneticsSampleResultDto, target);

        return Collections.emptyMap();
    }

    /**
     * 一审
     */
    @PostMapping("/one-check")
    public Object oneCheck(@RequestBody GeneticsSampleAuditVo vo) {
        final GeneticsSampleAuditDto dto = JSON.parseObject(JSON.toJSONString(vo), GeneticsSampleAuditDto.class);
        geneticsSampleService.oneCheck(dto);

        return Collections.emptyMap();

    }

    /**
     * 二审
     */
    @PostMapping("/two-check")
    public Object twoCheck(@RequestBody Set<Long> geneticsSampleIds) {

        geneticsSampleService.twoCheck(geneticsSampleIds);

        return Collections.emptyMap();
    }

    /**
     * 取消一审
     */
    @PostMapping("/cancel-one-check")
    public Object cancelOneCheck(@RequestBody CancelCheckRequestVo vo) {
        if (Objects.isNull(vo.getGeneticsSampleId()) || StringUtils.isAnyBlank(vo.getUsername(), vo.getPassword())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }

        final UserDto user = userService.selectByUsername(vo.getUsername());
        if (Objects.isNull(user)) {
            throw new IllegalArgumentException("工号或密码错误");
        }
        // 校验密码
        if (!userService.validPassword(user.getUsername(), user.getPassword(), vo.getPassword())) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        // 对应遗传样本
        final GeneticsSampleDto geneticsSampleDto =
            geneticsSampleService.selectByGeneticsSampleId(vo.getGeneticsSampleId());
        if (Objects.isNull(geneticsSampleDto)) {
            throw new IllegalStateException("遗传样本不存在");
        }
        if (!Objects.equals(geneticsSampleDto.getOneCheckerId(), user.getUserId())) {
            throw new IllegalStateException("一审人错误");
        }
        ApplyDto applyDto = applyService.selectByApplyId(geneticsSampleDto.getApplyId());
        if (Objects.isNull(applyDto)) {
            throw new LimsException("对应申请单不存在");
        }

        if (applySampleService.isDisabled(geneticsSampleDto.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", geneticsSampleDto.getBarcode()));
        }

        if (applySampleService.isTerminate(geneticsSampleDto.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", geneticsSampleDto.getBarcode()));
        }

        if (BooleanUtils.isTrue(financeSampleLockService.isSampleLock(geneticsSampleDto.getApplySampleId()))) {
            throw new IllegalStateException("该样本已经加锁，不能取消审核");
        }

        final ApplySampleDto applySampleDto =
            applySampleService.selectByApplySampleId(geneticsSampleDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("遗传样本对应申请单样本不存在");
        }

        if (!Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) {
            throw new IllegalStateException("只有一审状态样本可取消一审");
        }

        // 取消一审 修改数据
        geneticsSampleService.cancelOneCheck(geneticsSampleDto);

        final ApplySampleEventDto event = new ApplySampleEventDto();
        event.setEvent(ApplySampleEventDto.EventType.CancelOneCheck);
        event.setOrgId(user.getOrgId());
        event.setHspOrgId(applyDto.getHspOrgId());
        event.setHspOrgCode(applyDto.getHspOrgCode());
        event.setHspOrgName(applyDto.getHspOrgName());
        event.setApplyId(applyDto.getApplyId());
        event.setApplySampleId(geneticsSampleDto.getApplySampleId());
        event.setBarcode(geneticsSampleDto.getBarcode());
        event.setExtras(Map.of("sampleId", String.valueOf(geneticsSampleDto.getGeneticsSampleId()), "sampleNo",
            geneticsSampleDto.getSampleNo(), "outBarcode", String.valueOf(applySampleDto.getOutBarcode())));

        final String json = JSON.toJSONString(event);
        rabbitMQService.convertAndSend(EXCHANGE, ROUTING_KEY, json);

        log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", geneticsSampleDto.getApplySampleId(),
            geneticsSampleDto.getBarcode(), json, EXCHANGE, ROUTING_KEY);

        return Collections.emptyMap();
    }

    /**
     * 取消二审
     */
    @PostMapping("/cancel-two-check")
    public Object cancelTwoCheck(@RequestBody CancelCheckRequestVo vo) {
        if (Objects.isNull(vo.getGeneticsSampleId()) || StringUtils.isAnyBlank(vo.getUsername(), vo.getPassword())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        final UserDto user = userService.selectByUsername(vo.getUsername());
        if (Objects.isNull(user)) {
            throw new IllegalArgumentException("工号或密码错误");
        }
        // 校验密码
        if (!userService.validPassword(user.getUsername(), user.getPassword(), vo.getPassword())) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        // 对应遗传样本
        final GeneticsSampleDto geneticsSampleDto =
            geneticsSampleService.selectByGeneticsSampleId(vo.getGeneticsSampleId());
        if (Objects.isNull(geneticsSampleDto)) {
            throw new IllegalStateException("遗传样本不存在");
        }

        if (!Objects.equals(geneticsSampleDto.getTwoCheckerId(), user.getUserId())) {
            throw new IllegalStateException("二审人错误");
        }
        ApplyDto applyDto = applyService.selectByApplyId(geneticsSampleDto.getApplyId());
        if (Objects.isNull(applyDto)) {
            throw new LimsException("对应申请单不存在");
        }

        if (applySampleService.isDisabled(geneticsSampleDto.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", geneticsSampleDto.getBarcode()));
        }

        if (applySampleService.isTerminate(geneticsSampleDto.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", geneticsSampleDto.getBarcode()));
        }

        if (BooleanUtils.isTrue(financeSampleLockService.isSampleLock(geneticsSampleDto.getApplySampleId()))) {
            throw new IllegalStateException("该样本已经加锁，不能取消审核");
        }

        final ApplySampleDto applySampleDto =
            applySampleService.selectByApplySampleId(geneticsSampleDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("遗传样本对应申请单样本不存在");
        }
        if (!Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new IllegalStateException("只有已审状态样本可取消二审");
        }

        // 取消二审 修改数据
        geneticsSampleService.cancelTwoCheck(geneticsSampleDto);

        final ApplySampleEventDto event = new ApplySampleEventDto();
        event.setEvent(ApplySampleEventDto.EventType.CancelTwoCheck);
        event.setOrgId(user.getOrgId());
        event.setHspOrgId(applyDto.getHspOrgId());
        event.setHspOrgCode(applyDto.getHspOrgCode());
        event.setHspOrgName(applyDto.getHspOrgName());
        event.setApplyId(applyDto.getApplyId());
        event.setApplySampleId(geneticsSampleDto.getApplySampleId());
        event.setBarcode(geneticsSampleDto.getBarcode());
        event.setExtras(Map.of("sampleId", String.valueOf(geneticsSampleDto.getGeneticsSampleId()), "sampleNo",
            geneticsSampleDto.getSampleNo(), "outBarcode", String.valueOf(applySampleDto.getOutBarcode()),
            "businessCenterOrgCode", envDetector.getBusinessCenterOrgCode()));

        final String json = JSON.toJSONString(event);
        rabbitMQService.convertAndSend(EXCHANGE, ROUTING_KEY, json);

        log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", geneticsSampleDto.getApplySampleId(),
            geneticsSampleDto.getBarcode(), json, EXCHANGE, ROUTING_KEY);

        return Collections.emptyMap();
    }

    /**
     * 修改遗传样本结果 检查入参
     */
    private void validUpdateGeneticsResultParam(UpdateGeneticsSampleResultVo resultVo) {
        // 判断参数
        if (Objects.isNull(resultVo.getGeneticsSampleId())) {
            throw new IllegalArgumentException("样本ID不能为空");
        }

        // 目前仅判断长度
        if (StringUtils.length(resultVo.getCellCount()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("细胞计数长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }
        if (StringUtils.length(resultVo.getAnalyseCellCount()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("分析细胞数长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }
        if (StringUtils.length(resultVo.getBandingLevel()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("显带方法长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }
        if (StringUtils.length(resultVo.getBandingMethod()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("显带水平长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }
        if (StringUtils.length(resultVo.getSampleSituation()) > TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("标本情况长度不能超过 %s 字符", TEXTAREA_MAX_LENGTH));
        }
        if (StringUtils.length(resultVo.getKaryotype()) > TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("核型长度不能超过 %s 字符", TEXTAREA_MAX_LENGTH));
        }
        if (StringUtils.length(resultVo.getAnalyticalOpinion()) > TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("分析意见长度不能超过 %s 字符", TEXTAREA_MAX_LENGTH));
        }

    }

    /**
     * 根据 样本状态 过滤数据
     */
    private boolean filterByStatus(String sampleStatus, GeneticsSampleListItemVo temp) {
        // 终止检验数据不展示
        if (Objects.equals(temp.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
            return true;
        }

        if (StringUtils.isBlank(sampleStatus)) {
            // 未传入 过滤数据状态
            return false;
        }
        if (Objects.equals(sampleStatus, SampleStatusEnum.NOT_AUDIT.name())
            && !Objects.equals(temp.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
            // 样本状态 未审 数据 不为未审过滤掉
            return true;
        }
        if (Objects.equals(sampleStatus, SampleStatusEnum.ONE_AUDIT.name())
            && !Objects.equals(temp.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) {
            // 样本状态 一审 数据 不为一审过滤掉
            return true;
        }
        // 样本状态 已审 数据 不为已审过滤掉
        return Objects.equals(sampleStatus, SampleStatusEnum.AUDIT.name())
            && !Objects.equals(temp.getStatus(), SampleStatusEnum.AUDIT.getCode());
    }

}
