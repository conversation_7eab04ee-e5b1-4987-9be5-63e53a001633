package com.labway.lims.genetics.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 检验详情（与样本一对一）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class UpdateGeneticsSampleResultVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 遗传样本ID
     */
    private Long geneticsSampleId;

    /**
     * 细胞数
     */
    private String cellCount;

    /**
     * 分析细胞数
     */
    private String analyseCellCount;

    /**
     * 标本情况
     */
    private String sampleSituation;

    /**
     * 核型
     */
    private String karyotype;

    /**
     * 显带方法
     */
    private String bandingMethod;

    /**
     * 显带水平
     */
    private String bandingLevel;

    /**
     * 分析意见
     */
    private String analyticalOpinion;

    /**
     * 原始图像1
     */
    private String karyotypeOriginalImg1;

    /**
     * 原始图像2
     */
    private String karyotypeOriginalImg2;

    /**
     * 原始图像3
     */
    private String karyotypeOriginalImg3;

    /**
     * 分析图像1
     */
    private String karyotypeImg1;

    /**
     * 分析图像2
     */
    private String karyotypeImg2;

    /**
     * 分析图像3
     */
    private String karyotypeImg3;

}
