package com.labway.lims.genetics.service.chain.check.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * 二审 流水
 * 
 * <AUTHOR>
 * @since 2023/5/4 11:18
 */
@Slf4j
@Component
public class GeneticsTwoCheckFlowCommand implements Command {

    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context context) throws Exception {
        final GeneticsTwoCheckContext from = GeneticsTwoCheckContext.from(context);
        final List<GeneticsSampleDto> geneticsSampleList = from.getGeneticsSampleList();
        final LoginUserHandler.User user = from.getUser();
        final UserDto twoCheckUser = from.getTwoCheckUser();
        LinkedList<Long> genIds = snowflakeService.genIds(geneticsSampleList.size());

        for (GeneticsSampleDto sampleDto : geneticsSampleList) {

            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(genIds.pop());
            sampleFlow.setApplyId(sampleDto.getApplyId());
            sampleFlow.setApplySampleId(sampleDto.getApplySampleId());
            sampleFlow.setBarcode(sampleDto.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.TWO_CHECK.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.TWO_CHECK.getDesc());
            sampleFlow.setOperator(Objects.isNull(twoCheckUser) ? user.getNickname() : twoCheckUser.getNickname());
            sampleFlow.setOperatorId(Objects.isNull(twoCheckUser) ? user.getUserId() : twoCheckUser.getUserId());
            sampleFlow.setContent(BarcodeFlowEnum.TWO_CHECK.getDesc());

            sampleFlowService.addSampleFlow(sampleFlow);
        }

        return CONTINUE_PROCESSING;
    }
}
