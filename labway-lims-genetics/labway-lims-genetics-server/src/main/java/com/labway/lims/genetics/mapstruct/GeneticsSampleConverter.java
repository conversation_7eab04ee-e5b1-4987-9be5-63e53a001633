package com.labway.lims.genetics.mapstruct;

import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.SelectGeneticsSampleDto;
import com.labway.lims.genetics.model.TbGeneticsSample;
import com.labway.lims.genetics.vo.GeneticsSampleListItemVo;
import com.labway.lims.genetics.vo.GeneticsSampleResultListRequestVo;
import com.labway.lims.genetics.vo.QueryGeneticsListRequestVo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 遗传样本 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface GeneticsSampleConverter {

    GeneticsSampleDto fromTbGeneticsSample(TbGeneticsSample obj);

    TbGeneticsSample tbGeneticsSampleFromTbObjDto(GeneticsSampleDto obj);

    SelectGeneticsSampleDto selectGeneticsSampleDtoFromRequestVo(QueryGeneticsListRequestVo vo);

    SelectGeneticsSampleDto selectGeneticsSampleDtoFromRequestVo(GeneticsSampleResultListRequestVo vo);

    GeneticsSampleListItemVo geneticsSampleListItemVoFromTbDto(GeneticsSampleDto dto);

    List<GeneticsSampleDto> fromTbGeneticsSampleList(List<TbGeneticsSample> list);
}
