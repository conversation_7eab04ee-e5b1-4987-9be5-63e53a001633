package com.labway.lims.genetics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.model.TbGeneticsSample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 遗传样本 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface GeneticsSampleMapper extends BaseMapper<TbGeneticsSample> {

    /**
     * 根据ID批量修改
     */
    int updateByGeneticsSampleIds(@Param("geneticsSampleDto") GeneticsSampleDto geneticsSampleDto,
        @Param("geneticsSampleIds") Collection<Long> geneticsSampleIds);

}
