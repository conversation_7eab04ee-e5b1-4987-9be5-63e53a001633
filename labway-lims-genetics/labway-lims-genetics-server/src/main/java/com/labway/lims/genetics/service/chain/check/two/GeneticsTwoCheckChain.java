package com.labway.lims.genetics.service.chain.check.two;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 遗传一审
 *
 * <AUTHOR>
 * @since 2023/4/28 17:58
 */
@Component
public class GeneticsTwoCheckChain extends ChainBase implements InitializingBean {

    @Resource
    private GeneticsTwoCheckParamCheckCommand geneticsTwoCheckParamCheckCommand;
    @Resource
    private GeneticsTwoCheckLimitCommand geneticsTwoCheckLimitCommand;

    @Resource
    private GeneticsTwoCheckBuildReportCommand geneticsTwoCheckBuildReportCommand;

    @Resource
    private GeneticsTwoCheckUpdateStatusDataCommand geneticsTwoCheckUpdateStatusDataCommand;
    @Resource
    private GeneticsTwoCheckFlowCommand geneticsTwoCheckFlowCommand;

    @Resource
    private GeneticsTwoCheckRabbitMQCommand geneticsTwoCheckRabbitMQCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 检查参数
        addCommand(geneticsTwoCheckParamCheckCommand);

        // 加锁
        addCommand(geneticsTwoCheckLimitCommand);

        // 创建报告
        addCommand(geneticsTwoCheckBuildReportCommand);

        // 遗传样本 二审 修改样本状态、二审人
        addCommand(geneticsTwoCheckUpdateStatusDataCommand);

        // 保存流水
        addCommand(geneticsTwoCheckFlowCommand);

        // 发送到 mq
        addCommand(geneticsTwoCheckRabbitMQCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);
    }
}
