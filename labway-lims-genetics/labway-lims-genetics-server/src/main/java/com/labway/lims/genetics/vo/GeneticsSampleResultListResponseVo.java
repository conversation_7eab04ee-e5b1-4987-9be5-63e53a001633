package com.labway.lims.genetics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 遗传样本结果查询 信息
 * 
 * <AUTHOR>
 * @since 2023/4/24 13:58
 */
@Getter
@Setter
public class GeneticsSampleResultListResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    // --------------GeneticsSampleDto-------------
    /**
     * ID
     */
    private Long geneticsSampleId;
    /**
     * 申请单样本ID
     */
    private Long applySampleId;
    /**
     * 申请单ID
     */
    private Long applyId;
    /**
     * 条码号
     */
    private String barcode;
    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 专业小组
     */
    private Long instrumentGroupId;
    /**
     * 专业小组
     */
    private String instrumentGroupName;
    /**
     * 检验日期，暂定二次分拣日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDate;
    /**
     * 样本号
     */
    private String sampleNo;
    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 一次审核
     */
    private Long oneCheckerId;
    /**
     * 一次审核人名称
     */
    private String oneCheckerName;
    /**
     * 二次审核人
     */
    private Long twoCheckerId;
    /**
     * 二次审核人名称
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date twoCheckDate;

    // ----------------ApplyDto--------------
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;
    /**
     * 年龄
     */
    private Integer patientAge;
    /**
     * 子年龄
     */
    private Integer patientSubage;
    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;
    /**
     * 申请科室
     */
    private String dept;
    /**
     * 就诊卡号
     */
    private String patientVisitCard;
    /**
     * 就诊类型
     */
    private String applyType;
    /**
     * 临床诊断
     */
    private String diagnosis;

    // ------------------applySampleDto----------
    /**
     * 检验人ID
     */
    private Long testerId;
    /**
     * 检验人姓名
     */
    private String testerName;
    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 是否打印:1已打印，0未打印
     */
    private Integer isPrint;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 录入时间
     */
    private Date recordDate;
    /**
     * 样本备注
     */
    private String sampleRemark;

    /**
     * 结果备注
     */
    private String resultRemark;

    // ------------SampleReportDto-----------

    /**
     * 默认PDF
     * @see SampleReportFileTypeEnum
     */
    private String fileType;

    /**
     * 地址
     */
    private String url;

}
