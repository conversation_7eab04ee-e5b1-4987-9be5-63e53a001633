package com.labway.lims.genetics.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;
import com.labway.lims.genetics.api.service.GeneticsSampleResultService;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.genetics.mapper.GeneticsSampleResultMapper;
import com.labway.lims.genetics.mapstruct.GeneticsSampleResultConverter;
import com.labway.lims.genetics.model.TbGeneticsSampleResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 遗传样本结果 Service impl
 *
 * <AUTHOR>
 * @since 2023/4/23 11:13
 */
@Slf4j
@DubboService
public class GeneticsSampleResultServiceImpl implements GeneticsSampleResultService {
    @Resource
    private GeneticsSampleResultMapper geneticsSampleResultMapper;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private GeneticsSampleResultConverter geneticsSampleResultConverter;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private GeneticsSampleService geneticsSampleService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByGeneticsSampleResultId(GeneticsSampleResultDto dto) {

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbGeneticsSampleResult target = geneticsSampleResultConverter.tbGeneticsSampleResultFromTbObjDto(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (geneticsSampleResultMapper.updateById(target) < 1) {
            throw new LimsException("修改遗传样本结果失败");
        }

        log.info("用户 [{}] 修改遗传样本结果成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Nullable
    @Override
    public GeneticsSampleResultDto selectByGeneticsSampleResultId(long geneticsSampleResultId) {
        if (geneticsSampleResultId < 1) {
            return null;
        }
        LambdaQueryWrapper<TbGeneticsSampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGeneticsSampleResult::getGeneticsSampleResultId, geneticsSampleResultId);
        queryWrapper.eq(TbGeneticsSampleResult::getIsDelete, YesOrNoEnum.NO.getCode()).last("limit 1");
        return geneticsSampleResultConverter
                .fromTbGeneticsSampleResult(geneticsSampleResultMapper.selectOne(queryWrapper));
    }

    @Nullable
    @Override
    public GeneticsSampleResultDto selectByGeneticsSampleId(long geneticsSampleId) {
        LambdaQueryWrapper<TbGeneticsSampleResult> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TbGeneticsSampleResult::getGeneticsSampleId, geneticsSampleId);
        wrapper.eq(TbGeneticsSampleResult::getIsDelete, YesOrNoEnum.NO.getCode()).last("limit 1");
        TbGeneticsSampleResult tbGeneticsSampleResult = geneticsSampleResultMapper.selectOne(wrapper);

        return geneticsSampleResultConverter.fromTbGeneticsSampleResult(tbGeneticsSampleResult);
    }

    @Override
    public List<GeneticsSampleResultDto> selectByGeneticsSampleIds(Collection<Long> geneticsSampleIds) {

        if (CollectionUtils.isEmpty(geneticsSampleIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbGeneticsSampleResult> wrapper = Wrappers.lambdaQuery();
        wrapper.in(TbGeneticsSampleResult::getGeneticsSampleId, geneticsSampleIds);
        wrapper.eq(TbGeneticsSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());

        return geneticsSampleResultMapper.selectList(wrapper).stream()
                .map(geneticsSampleResultConverter::fromTbGeneticsSampleResult).collect(Collectors.toList());
    }

    @Override
    public Map<Long, GeneticsSampleResultDto> selectByGeneticsSampleIdsAsMap(Collection<Long> geneticsSampleIds) {
        return selectByGeneticsSampleIds(geneticsSampleIds).stream()
                .collect(Collectors.toMap(GeneticsSampleResultDto::getGeneticsSampleId, v -> v, (a, b) -> a));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addGeneticsSampleResult(GeneticsSampleResultDto dto) {
        TbGeneticsSampleResult target = geneticsSampleResultConverter.tbGeneticsSampleResultFromTbObjDto(dto);

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        if (Objects.isNull(target.getGeneticsSampleResultId())) {
            target.setGeneticsSampleResultId(snowflakeService.genId());
        }
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (geneticsSampleResultMapper.insert(target) < 1) {
            throw new IllegalStateException("添加遗传样本结果失败");
        }

        log.info("用户 [{}] 新增遗传样本结果 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(target));

        return target.getGeneticsSampleResultId();

    }

    @Override
    public boolean deleteByGeneticsSampleId(long geneticsSampleId) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        LambdaUpdateWrapper<TbGeneticsSampleResult> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(TbGeneticsSampleResult::getIsDelete, YesOrNoEnum.YES.getCode());
        updateWrapper.eq(TbGeneticsSampleResult::getGeneticsSampleId, geneticsSampleId);
        updateWrapper.eq(TbGeneticsSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());

        if (geneticsSampleResultMapper.update(null, updateWrapper) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除遗传样本结果成功 样本 [{}]", loginUser.getNickname(), geneticsSampleId);
        return true;
    }

    @Override
    public void deleteByGeneticsSampleIds(Collection<Long> geneticsSampleIds) {

        if (CollectionUtils.isEmpty(geneticsSampleIds)) {
            return;
        }

        LambdaUpdateWrapper<TbGeneticsSampleResult> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(TbGeneticsSampleResult::getIsDelete, YesOrNoEnum.YES.getCode());
        updateWrapper.in(TbGeneticsSampleResult::getGeneticsSampleId, geneticsSampleIds);
        updateWrapper.eq(TbGeneticsSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());

        geneticsSampleResultMapper.update(null, updateWrapper);

        log.info("用户 [{}] 删除遗传样本结果成功 样本 [{}]", LoginUserHandler.get().getNickname(), geneticsSampleIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateGeneticsResultDetail(GeneticsSampleDto sampleDto, GeneticsSampleResultDto oldResult,
                                           GeneticsSampleResultDto newResul) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        boolean isAdd = false;
        if (Objects.isNull(newResul.getGeneticsSampleResultId())) {
            // 第一次新增
            this.addGeneticsSampleResult(newResul);
            isAdd = true;
        } else {
            // 结果已存在 更新
            this.updateByGeneticsSampleResultId(newResul);
        }

        // 修改 遗传样本检验时间
//        GeneticsSampleDto geneticsSampleDto = new GeneticsSampleDto();
//        geneticsSampleDto.setTestDate(new Date());
//        geneticsSampleDto.setGeneticsSampleId(sampleDto.getGeneticsSampleId());
//        geneticsSampleService.updateByGeneticsSampleId(geneticsSampleDto);

        // 更新对应申请单样本 检验人取操作用户
        ApplySampleDto applySampleDto = new ApplySampleDto();
        applySampleDto.setApplySampleId(sampleDto.getApplySampleId());
        applySampleDto.setTesterId(user.getUserId());
        applySampleDto.setTesterName(user.getNickname());

        if (BooleanUtils.isNotTrue(applySampleService.updateByApplySampleId(applySampleDto))) {
            throw new IllegalStateException("修改对应申请单样本检验人失败");
        }

        String compare = new CompareUtils<GeneticsSampleResultDto>().compare(oldResult, newResul);
        if (!isAdd && StringUtils.isBlank(compare)) {
            // 更新调用 但信息没有变化
            return;
        }

        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(sampleDto.getApplyId());
        sampleFlow.setApplySampleId(sampleDto.getApplySampleId());
        sampleFlow.setBarcode(sampleDto.getBarcode());
        sampleFlow.setOperateCode(isAdd ? BarcodeFlowEnum.ADD_RESULT.name() : BarcodeFlowEnum.UPDATE_RESULT.name());
        sampleFlow
                .setOperateName(isAdd ? BarcodeFlowEnum.ADD_RESULT.getDesc() : BarcodeFlowEnum.UPDATE_RESULT.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        String content;
        if (StringUtils.isBlank(compare)) {
            content = String.format("%s",
                    isAdd ? BarcodeFlowEnum.ADD_RESULT.getDesc() : BarcodeFlowEnum.UPDATE_RESULT.getDesc());
        } else {
            content = String.format("%s:%s",
                    isAdd ? BarcodeFlowEnum.ADD_RESULT.getDesc() : BarcodeFlowEnum.UPDATE_RESULT.getDesc(), compare);
        }
        sampleFlow.setContent(content);

        sampleFlowService.addSampleFlow(sampleFlow);
    }

}
