package com.labway.lims.genetics.service.chain.check.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.service.GeneticsSampleResultService;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 二审 参数检验
 *
 * <AUTHOR>
 * @since 2023/5/4 10:09
 */
@Slf4j
@Component
public class GeneticsTwoCheckParamCheckCommand implements Command {

    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private GeneticsSampleService geneticsSampleService;
    @Resource
    private GeneticsSampleResultService geneticsSampleResultService;

    @Override
    public boolean execute(Context context) throws Exception {
        final GeneticsTwoCheckContext from = GeneticsTwoCheckContext.from(context);
        final Set<Long> geneticsSampleIds = from.getGeneticsSampleIds();

        // 对应选中 遗传样本
        final List<GeneticsSampleDto> geneticsSampleDtos =
                geneticsSampleService.selectByGeneticsSampleIds(geneticsSampleIds);

        for (GeneticsSampleDto sample : geneticsSampleDtos) {
            if (Objects.equals(sample.getOneCheckerId(), LoginUserHandler.get().getUserId())) {
                throw new IllegalStateException("一审者和二审者不能为同一用户");
            }
        }

        final Set<Long> selectGeneticsSampleIds =
                geneticsSampleDtos.stream().map(GeneticsSampleDto::getGeneticsSampleId).collect(Collectors.toSet());

        if (geneticsSampleIds.stream().anyMatch(x -> !selectGeneticsSampleIds.contains(x))) {
            throw new LimsException("存在无效遗传样本");
        }

        // 对应申请单信息
        Set<Long> applyIds = geneticsSampleDtos.stream().map(GeneticsSampleDto::getApplyId).collect(Collectors.toSet());

        List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);

        Set<Long> selectApplyIds = applyDtos.stream().map(ApplyDto::getApplyId).collect(Collectors.toSet());

        if (applyIds.stream().anyMatch(x -> !selectApplyIds.contains(x))) {
            throw new LimsException("存在无效遗传样本:没有对应申请单");
        }

        // 对应申请单样本ids
        Set<Long> applySampleIds =
                geneticsSampleDtos.stream().map(GeneticsSampleDto::getApplySampleId).collect(Collectors.toSet());

        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);

        Set<Long> selectApplySampleIds =
                applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());

        if (applySampleIds.stream().anyMatch(x -> !selectApplySampleIds.contains(x))) {
            throw new LimsException("存在无效遗传样本:没有对应申请单样本");
        }

        if (applySampleDtos.stream()
                .anyMatch(obj -> !Objects.equals(obj.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new LimsException("已选数据存在未审或已审样本，不可二审");
        }

        for (Long applySampleId : applySampleIds) {

            if (applySampleService.isDisabled(applySampleId)) {
                throw new LimsException(String.format(" [%s] 对应样本已禁用", applySampleId));
            }

            if (applySampleService.isTerminate(applySampleId)) {
                throw new LimsException(String.format(" [%s] 对应样本已经终止检验", applySampleId));
            }
        }

        from.put(GeneticsTwoCheckContext.APPLY, applyDtos);
        from.put(GeneticsTwoCheckContext.APPLY_SAMPLE, applySampleDtos);
        from.put(GeneticsTwoCheckContext.GENETICS_SAMPLE, geneticsSampleDtos);
        from.put(GeneticsTwoCheckContext.GENETICS_SAMPLE_RESULT, geneticsSampleResultService.selectByGeneticsSampleIdsAsMap(geneticsSampleDtos.stream()
                .map(GeneticsSampleDto::getGeneticsSampleId).collect(Collectors.toSet())));


        return CONTINUE_PROCESSING;
    }
}
