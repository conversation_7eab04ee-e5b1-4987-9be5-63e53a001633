package com.labway.lims.genetics.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.genetics.api.dto.GeneticsSampleAuditDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;
import com.labway.lims.genetics.api.dto.SelectGeneticsSampleDto;
import com.labway.lims.genetics.api.service.GeneticsSampleResultService;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.genetics.mapper.GeneticsSampleMapper;
import com.labway.lims.genetics.mapstruct.GeneticsSampleConverter;
import com.labway.lims.genetics.model.TbGeneticsSample;
import com.labway.lims.genetics.service.chain.check.one.GeneticsOneCheckChain;
import com.labway.lims.genetics.service.chain.check.one.GeneticsOneCheckContext;
import com.labway.lims.genetics.service.chain.check.two.GeneticsTwoCheckBuildReportCommand;
import com.labway.lims.genetics.service.chain.check.two.GeneticsTwoCheckChain;
import com.labway.lims.genetics.service.chain.check.two.GeneticsTwoCheckContext;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 遗传样本
 *
 * <AUTHOR>
 * @since 2023/3/29 10:00
 */
@Slf4j
@DubboService
public class GeneticsSampleServiceImpl implements GeneticsSampleService {

    @Resource
    private GeneticsSampleMapper geneticsSampleMapper;
    @Resource
    private GeneticsSampleConverter geneticsSampleConverter;
    @Resource
    private GeneticsOneCheckChain geneticsOneCheckChain;
    @Resource
    private GeneticsTwoCheckChain geneticsTwoCheckChain;

    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @Resource
    private GeneticsTwoCheckBuildReportCommand buildReportCommand;
    @Resource
    private GeneticsSampleResultService geneticsSampleResultService;

    @Override
    public List<GeneticsSampleDto> selectBySelectGeneticsSampleDto(SelectGeneticsSampleDto dto) {
        LambdaQueryWrapper<TbGeneticsSample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGeneticsSample::getGroupId, dto.getGroupId());
        queryWrapper.eq(Objects.nonNull(dto.getInstrumentGroupId()), TbGeneticsSample::getInstrumentGroupId,
                dto.getInstrumentGroupId());
        queryWrapper.eq(Objects.nonNull(dto.getHspOrgId()), TbGeneticsSample::getHspOrgId, dto.getHspOrgId());
        queryWrapper.ge(Objects.nonNull(dto.getTestDateStart()), TbGeneticsSample::getTestDate, dto.getTestDateStart());
        queryWrapper.le(Objects.nonNull(dto.getTestDateEnd()), TbGeneticsSample::getTestDate, dto.getTestDateEnd());
        queryWrapper.ge(Objects.nonNull(dto.getTwoCheckDateStart()), TbGeneticsSample::getTwoCheckDate,
                dto.getTwoCheckDateStart());
        queryWrapper.le(Objects.nonNull(dto.getTwoCheckDateEnd()), TbGeneticsSample::getTwoCheckDate,
                dto.getTwoCheckDateEnd());
        queryWrapper.orderByAsc(TbGeneticsSample::getTestDate);
        return geneticsSampleConverter.fromTbGeneticsSampleList(geneticsSampleMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public GeneticsSampleDto selectByGeneticsSampleId(long geneticsSampleId) {

        LambdaQueryWrapper<TbGeneticsSample> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TbGeneticsSample::getGeneticsSampleId, geneticsSampleId);
        wrapper.eq(TbGeneticsSample::getIsDelete, YesOrNoEnum.NO.getCode()).last("limit 1");
        return geneticsSampleConverter.fromTbGeneticsSample(geneticsSampleMapper.selectOne(wrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addGeneticsSample(GeneticsSampleDto dto) {

        TbGeneticsSample target = geneticsSampleConverter.tbGeneticsSampleFromTbObjDto(dto);

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        target.setGeneticsSampleId(ObjectUtils.defaultIfNull(dto.getGeneticsSampleId(), snowflakeService.genId()));
        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (geneticsSampleMapper.insert(target) < 1) {
            throw new IllegalStateException("添加遗传样本失败");
        }

        log.info("用户 [{}] 新增遗传样本 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(target));

        return target.getGeneticsSampleId();
    }

    @Override
    public List<GeneticsSampleDto> selectByGeneticsSampleIds(Collection<Long> geneticsSampleIds) {
        if (CollectionUtils.isEmpty(geneticsSampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbGeneticsSample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbGeneticsSample::getGeneticsSampleId, geneticsSampleIds);
        queryWrapper.eq(TbGeneticsSample::getIsDelete, YesOrNoEnum.NO.getCode());
        return geneticsSampleConverter.fromTbGeneticsSampleList(geneticsSampleMapper.selectList(queryWrapper));
    }

    @Override
    public void updateByGeneticsSampleId(GeneticsSampleDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbGeneticsSample target = new TbGeneticsSample();
        BeanUtils.copyProperties(dto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (geneticsSampleMapper.updateById(target) < 1) {
            throw new LimsException("修改遗传样本失败");
        }

        log.info("用户 [{}] 修改遗传样本成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    public void updateByGeneticsSampleIds(GeneticsSampleDto dto, Collection<Long> geneticsSampleIds) {
        if (CollectionUtils.isEmpty(geneticsSampleIds)) {
            return;
        }
        geneticsSampleMapper.updateByGeneticsSampleIds(dto, geneticsSampleIds);
    }

    @Nullable
    @Override
    public GeneticsSampleDto selectByApplySampleId(long applySampleId) {
        LambdaQueryWrapper<TbGeneticsSample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGeneticsSample::getApplySampleId, applySampleId);
        queryWrapper.eq(TbGeneticsSample::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");
        return geneticsSampleConverter.fromTbGeneticsSample(geneticsSampleMapper.selectOne(queryWrapper));
    }

    @Override
    public List<GeneticsSampleDto> selectByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbGeneticsSample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbGeneticsSample::getApplySampleId, applySampleIds);
        queryWrapper.eq(TbGeneticsSample::getIsDelete, YesOrNoEnum.NO.getCode());
        return geneticsSampleConverter.fromTbGeneticsSampleList(geneticsSampleMapper.selectList(queryWrapper));
    }

    @Override
    public boolean deleteByGeneticsSampleId(long geneticsSampleId) {
        if (geneticsSampleMapper.deleteById(geneticsSampleId) < 1) {
            return false;
        }
        log.info("用户 [{}] 删除遗传检验样本 [{}] 成功", LoginUserHandler.get().getNickname(), geneticsSampleId);
        return true;
    }

    @Override
    public void deleteByGeneticsSampleIds(Collection<Long> geneticsSampleIds) {
        if (CollectionUtils.isEmpty(geneticsSampleIds)) {
            return;
        }

        geneticsSampleMapper.deleteBatchIds(geneticsSampleIds);

        log.info("用户 [{}] 删除遗传检验样本 [{}] 成功", LoginUserHandler.get().getNickname(), geneticsSampleIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void oneCheck(GeneticsSampleAuditDto auditDto) {
        if (CollectionUtils.isEmpty(auditDto.getGeneticsSampleIds())) {
            return;
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        final Set<Long> geneticsSampleIds = auditDto.getGeneticsSampleIds();
        final GeneticsOneCheckContext context = new GeneticsOneCheckContext();
        context.put(GeneticsOneCheckContext.SAMPLE_AUDTI_DTO,auditDto);
        context.setGeneticsSampleIds(geneticsSampleIds);
        context.setUser(loginUser);

        try {
            if (!geneticsOneCheckChain.execute(context)) {
                throw new IllegalStateException("一审失败");
            }
        } catch (RuntimeException e) {
            log.error("一审失败 [{}]", geneticsSampleIds, e);
            throw e;
        } catch (Exception e) {
            log.error("一审失败 [{}]", geneticsSampleIds, e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("一审 [{}] 耗时\n{}", geneticsSampleIds, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void twoCheck(Set<Long> geneticsSampleIds) {
        if (CollectionUtils.isEmpty(geneticsSampleIds)) {
            return;
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final GeneticsTwoCheckContext context = new GeneticsTwoCheckContext();
        context.setGeneticsSampleIds(geneticsSampleIds);
        context.setUser(loginUser);

        try {
            if (!geneticsTwoCheckChain.execute(context)) {
                throw new IllegalStateException("二审失败");
            }
        } catch (RuntimeException e) {
            log.error("二审失败 [{}]", geneticsSampleIds, e);
            throw e;
        } catch (Exception e) {
            log.error("二审失败 [{}]", geneticsSampleIds, e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("二审 [{}] 耗时\n{}", geneticsSampleIds, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOneCheck(GeneticsSampleDto dto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        // 修改遗传样本 一审人信息
        GeneticsSampleDto geneticsSampleDto = new GeneticsSampleDto();
        geneticsSampleDto.setGeneticsSampleId(dto.getGeneticsSampleId());
        geneticsSampleDto.setOneCheckerId(NumberUtils.LONG_ZERO);
        geneticsSampleDto.setOneCheckerName(StringUtils.EMPTY);
        geneticsSampleDto.setOneCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        this.updateByGeneticsSampleId(geneticsSampleDto);

        // 修改申请单样本状态
        ApplySampleDto applySampleDto = new ApplySampleDto();
        applySampleDto.setApplySampleId(dto.getApplySampleId());
        applySampleDto.setStatus(SampleStatusEnum.NOT_AUDIT.getCode());

        if (BooleanUtils.isNotTrue(applySampleService.updateByApplySampleId(applySampleDto))) {
            throw new IllegalStateException("修改申请单样本状态失败");
        }

        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(dto.getApplyId());
        sampleFlow.setApplySampleId(dto.getApplySampleId());
        sampleFlow.setBarcode(dto.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.ONE_CHECK.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.ONE_CHECK.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        sampleFlow.setContent("取消一审");

        sampleFlowService.addSampleFlow(sampleFlow);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTwoCheck(GeneticsSampleDto dto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        // 修改遗传样本 二审人信息
        GeneticsSampleDto geneticsSampleDto = new GeneticsSampleDto();
        geneticsSampleDto.setGeneticsSampleId(dto.getGeneticsSampleId());
        geneticsSampleDto.setTwoCheckerId(NumberUtils.LONG_ZERO);
        geneticsSampleDto.setTwoCheckerName(StringUtils.EMPTY);
        geneticsSampleDto.setTwoCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        this.updateByGeneticsSampleId(geneticsSampleDto);

        // 删除样本报告
        sampleReportService.deleteBySampleIds(Collections.singleton(dto.getGeneticsSampleId()));

        // 修改申请单样本状态
        ApplySampleDto applySampleDto = new ApplySampleDto();
        applySampleDto.setApplySampleId(dto.getApplySampleId());
        applySampleDto.setStatus(SampleStatusEnum.ONE_AUDIT.getCode());
        applySampleDto.setPrintDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        applySampleDto.setPrinterId(NumberUtils.LONG_ZERO);
        applySampleDto.setPrinterName(StringUtils.EMPTY);
        applySampleDto.setIsPrint(YesOrNoEnum.NO.getCode());

        if (BooleanUtils.isNotTrue(applySampleService.updateByApplySampleId(applySampleDto))) {
            throw new IllegalStateException("修改申请单样本状态失败");
        }

        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(dto.getApplyId());
        sampleFlow.setApplySampleId(dto.getApplySampleId());
        sampleFlow.setBarcode(dto.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.TWO_CHECK.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.TWO_CHECK.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        sampleFlow.setContent("取消二审");

        sampleFlowService.addSampleFlow(sampleFlow);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SampleReportDto rebuildReport(long applySampleId) {

        ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySampleDto)) {
            throw new LimsException("样本不存在");
        }
        if (!Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new LimsException("样本未已审不可重新生成报告");
        }
        ApplyDto applyDto = applyService.selectByApplyId(applySampleDto.getApplyId());
        if (Objects.isNull(applyDto)) {
            throw new LimsException("样本对应申请单不存在");
        }
        // 对应遗传样本
        GeneticsSampleDto sampleDto = this.selectByApplySampleId(applySampleId);
        if (Objects.isNull(sampleDto)) {
            throw new LimsException("对应遗传样本不存在");
        }

        GeneticsSampleResultDto geneticsSampleResultDto =
                geneticsSampleResultService.selectByGeneticsSampleId(sampleDto.getGeneticsSampleId());

        SampleReportDto sampleReport =
                buildReportCommand.buildPDF(sampleDto, applyDto, applySampleDto, geneticsSampleResultDto);

        log.info("重新生成报告: [{}]", sampleReport.getUrl());
        return sampleReport;
    }

    @Override
    public void updateByApplyId( GeneticsSampleDto geneticsSampleDto ) {
        LambdaUpdateWrapper<TbGeneticsSample> wrapper = Wrappers.lambdaUpdate(TbGeneticsSample.class)
                .eq(TbGeneticsSample::getApplyId, geneticsSampleDto.getApplyId())
                .eq(TbGeneticsSample::getIsDelete,0)
                .set(TbGeneticsSample::getHspOrgId, geneticsSampleDto.getHspOrgId())
                .set(TbGeneticsSample::getHspOrgName,geneticsSampleDto.getHspOrgName())
                .set(TbGeneticsSample::getUpdaterId,geneticsSampleDto.getUpdaterId())
                .set(TbGeneticsSample::getUpdaterName,geneticsSampleDto.getUpdaterName())
                .set(TbGeneticsSample::getUpdateDate,geneticsSampleDto.getUpdateDate());
        geneticsSampleMapper.update(null, wrapper);
    }

    @Override
    public void updateByApplyIds(GeneticsSampleDto geneticsSampleDto, Collection<Long> applyIds) {
        for (List<Long> item : Lists.partition(new ArrayList<>(applyIds), 500)) {
            LambdaUpdateWrapper<TbGeneticsSample> wrapper = Wrappers.lambdaUpdate(TbGeneticsSample.class)
                .in(TbGeneticsSample::getApplyId, item).eq(TbGeneticsSample::getIsDelete, 0)
                .set(TbGeneticsSample::getHspOrgId, geneticsSampleDto.getHspOrgId())
                .set(TbGeneticsSample::getHspOrgName, geneticsSampleDto.getHspOrgName())
                .set(TbGeneticsSample::getUpdaterId, geneticsSampleDto.getUpdaterId())
                .set(TbGeneticsSample::getUpdaterName, geneticsSampleDto.getUpdaterName())
                .set(TbGeneticsSample::getUpdateDate, geneticsSampleDto.getUpdateDate());
            geneticsSampleMapper.update(null, wrapper);
        }
    }

}
