package com.labway.lims.genetics.service.chain.check.two;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;
import com.labway.lims.genetics.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 二审信息内容
 *
 * <AUTHOR>
 * @since 2023/5/4 9:55
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class GeneticsTwoCheckContext extends StopWatchContext {
    /**
     * 遗传样本ids
     */
    private Set<Long> geneticsSampleIds;
    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    /**
     * 获取 信息参数 从上下文中
     */
    public static GeneticsTwoCheckContext from(Context context) {
        return (GeneticsTwoCheckContext) context;
    }

    // 遗传样本
    public static final String GENETICS_SAMPLE = "GENETICS_SAMPLE_" + IdUtil.objectId();

    // 遗传样本
    public static final String GENETICS_SAMPLE_RESULT = "GENETICS_SAMPLE_RESULT" + IdUtil.objectId();

    public List<GeneticsSampleDto> getGeneticsSampleList() {
        return (List<GeneticsSampleDto>) get(GENETICS_SAMPLE);
    }

    // 对应申请单
    public static final String APPLY = "APPLY_" + IdUtil.objectId();

    public List<ApplyDto> getApplyDtoList() {
        return (List<ApplyDto>) get(APPLY);
    }

    // 对应申请单样本
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    public List<ApplySampleDto> getApplySampleList() {
        return (List<ApplySampleDto>) get(APPLY_SAMPLE);
    }

    public Map<Long, GeneticsSampleResultDto> getGeneticsSampleResults() {
        return (Map<Long, GeneticsSampleResultDto>) get(GENETICS_SAMPLE_RESULT);
    }

    @Override
    protected String getWatcherName() {
        return "遗传检验二审";
    }
}
