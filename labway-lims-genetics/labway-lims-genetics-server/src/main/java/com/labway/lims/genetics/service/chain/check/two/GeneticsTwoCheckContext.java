package com.labway.lims.genetics.service.chain.check.two;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.genetics.api.dto.AuditSampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;
import com.labway.lims.genetics.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 二审信息内容
 *
 * <AUTHOR>
 * @since 2023/5/4 9:55
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class GeneticsTwoCheckContext extends StopWatchContext {
    /**
     * 遗传样本ids
     */
    private AuditSampleDto auditSampleDto;
    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    /**
     * 二审用户
     */
    private UserDto twoCheckUser;


    /**
     * 获取 信息参数 从上下文中
     */
    public static GeneticsTwoCheckContext from(Context context) {
        return (GeneticsTwoCheckContext) context;
    }

    // 遗传样本
    public static final String GENETICS_SAMPLE = "GENETICS_SAMPLE_" + IdUtil.objectId();

    // 遗传样本
    public static final String GENETICS_SAMPLE_RESULT = "GENETICS_SAMPLE_RESULT" + IdUtil.objectId();

    public List<GeneticsSampleDto> getGeneticsSampleList() {
        return (List<GeneticsSampleDto>) get(GENETICS_SAMPLE);
    }

    // 对应申请单
    public static final String APPLY = "APPLY_" + IdUtil.objectId();

    public List<ApplyDto> getApplyDtoList() {
        return (List<ApplyDto>) get(APPLY);
    }

    // 对应申请单样本
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    public List<ApplySampleDto> getApplySampleList() {
        return (List<ApplySampleDto>) get(APPLY_SAMPLE);
    }

    // 对应申请单和报告单编号
    public static final String APPLY_SAMPLE_ID_AND_REPORT_NO_MAP = "APPLY_SAMPLE_ID_AND_REPORT_NO_MAP_" + IdUtil.objectId();

    public Map<Long, String> getApplySampleIdAndReportNoMap() {
        return (Map<Long, String>) getOrDefault(APPLY_SAMPLE_ID_AND_REPORT_NO_MAP, Collections.emptyMap());
    }

    public Map<Long, GeneticsSampleResultDto> getGeneticsSampleResults() {
        return (Map<Long, GeneticsSampleResultDto>) get(GENETICS_SAMPLE_RESULT);
    }

    @Override
    protected String getWatcherName() {
        return "遗传检验二审";
    }
}
