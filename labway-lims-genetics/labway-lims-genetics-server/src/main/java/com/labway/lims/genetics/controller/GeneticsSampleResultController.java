package com.labway.lims.genetics.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.GeneticsInspectionDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.genetics.mapstruct.GeneticsSampleConverter;
import com.labway.lims.genetics.vo.GeneticsSampleResultListRequestVo;
import com.labway.lims.genetics.vo.GeneticsSampleResultListResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 遗传检验结果查询 API
 *
 * <AUTHOR>
 * @since 2023/4/24 13:47
 */
@Slf4j
@RestController
@RequestMapping("/genetics-sample-result")
public class GeneticsSampleResultController extends BaseController {

    @Resource
    private GeneticsSampleService geneticsSampleService;

    @Resource
    private GeneticsSampleConverter geneticsSampleConverter;

    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    /**
     * 结果查询 列表
     */
    @PostMapping("/list")
    public Object geneticsSampleResultList(@RequestBody GeneticsSampleResultListRequestVo queryVo) {

        // 转为es 查询条件
        SampleEsQuery query = getSampleEsQuery(queryVo);

        final List<GeneticsInspectionDto> sampleEsModels = elasticSearchSampleService.selectSamples(query)
            // 遗传样本
            .stream().filter(GeneticsInspectionDto.class::isInstance)
            // 转成遗传样本
            .map(e -> (GeneticsInspectionDto)e).collect(Collectors.toList());

        return selectGeneticsSampleResultListResponseVo(sampleEsModels);

    }

    /**
     * 遗传结果 信息 导出
     */
    @PostMapping("/export-result-data")
    public Object exportCriticalData(@RequestBody Set<Long> geneticsSampleIds) {

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(NumberUtils.INTEGER_ONE);
        query.setSampleIds(geneticsSampleIds);

        final List<GeneticsInspectionDto> sampleEsModels = elasticSearchSampleService.selectSamples(query)
            // 遗传样本
            .stream().filter(GeneticsInspectionDto.class::isInstance)
            // 转成遗传样本
            .map(e -> (GeneticsInspectionDto)e).collect(Collectors.toList());

        // 内容数据
        final List<GeneticsSampleResultListResponseVo> contentList =
            selectGeneticsSampleResultListResponseVo(sampleEsModels);

        // 头的策略
        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("微软雅黑");
        headWriteFont.setFontHeightInPoints((short)13);
        headCellStyle.setWriteFont(headWriteFont);

        // 内容策略
        WriteCellStyle contentCellStyle = new WriteCellStyle();
        contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentCellStyle.setBorderLeft(BorderStyle.THIN);
        contentCellStyle.setBorderTop(BorderStyle.THIN);
        contentCellStyle.setBorderRight(BorderStyle.THIN);
        contentCellStyle.setBorderBottom(BorderStyle.THIN);

        HorizontalCellStyleStrategy excelStyle = new HorizontalCellStyleStrategy(headCellStyle, contentCellStyle);

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (ExcelWriter excelWriter =
            EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build()) {

            List<List<Object>> list0 = Lists.newArrayList();
            List<List<String>> header0 = Lists.newArrayList();

            // 设置表头
            List<String> headList =
                Lists.newArrayList("状态", "样本号", "检验项目", "姓名", "条码号", "检验日期", "审核日期", "送检机构", "就诊类型");
            for (String item : headList) {
                header0.add(List.of(item));
            }

            // 设置 放置数据
            fillExcelContent(list0, headList, contentList);

            // 获取sheet对象
            WriteSheet sheet0 =
                EasyExcelFactory.writerSheet(0, "遗传检验结果信息").head(header0).needHead(Boolean.TRUE).build();

            excelWriter.write(list0, sheet0);
        } catch (Exception e) {
            log.error("导出数据错误", e);
            throw new LimsException(e.getMessage(), e);
        }

        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION,
                String.format("attachment; filename=%s", URLEncoder.encode("遗传检验结果信息.xlsx", StandardCharsets.UTF_8)))
            .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
            .body(data);
    }

    /**
     * 获取结果查询 es 条件
     *
     */
    private SampleEsQuery getSampleEsQuery(GeneticsSampleResultListRequestVo queryVo) {
        LoginUserHandler.User user = LoginUserHandler.get();

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(NumberUtils.INTEGER_ONE);
        // 当前用户所属专业组
        query.setGroupIds(Collections.singleton(user.getGroupId()));

        // 已审
        query.setIsAudit(YesOrNoEnum.YES.getCode());

        // 只查询遗传样本
        query.setItemTypes(Collections.singleton(ItemTypeEnum.GENETICS.name()));

        // 检验日期
        if (Objects.nonNull(queryVo.getTestDateStart()) && Objects.nonNull(queryVo.getTestDateEnd())) {
            query.setStartTestDate(queryVo.getTestDateStart());
            query.setEndTestDate(queryVo.getTestDateEnd());
        }

        // 审核日期
        if (Objects.nonNull(queryVo.getCheckDateStart()) && Objects.nonNull(queryVo.getCheckDateEnd())) {
            query.setStartFinalCheckDate(queryVo.getCheckDateStart());
            query.setEndFinalCheckDate(queryVo.getCheckDateEnd());
        }

        // 检验者ID
        if (Objects.nonNull(queryVo.getTesterId())) {
            query.setTesterId(queryVo.getTesterId());
        }

        // 审核人ID
        if (Objects.nonNull(queryVo.getCheckerId())) {
            query.setFinalCheckerIds(Collections.singleton(queryVo.getCheckerId()));
        }

        // 检验项目
        if (Objects.nonNull(queryVo.getTestItemId())) {
            query.setTestItemIds(Collections.singleton(queryVo.getTestItemId()));
        }

        // 送检机构
        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Collections.singleton(queryVo.getHspOrgId()));
        }

        // 姓名
        if (StringUtils.isNotBlank(queryVo.getPatientName())) {
            query.setPatientName(queryVo.getPatientName());
        }

        // 性别
        if (Objects.nonNull(queryVo.getPatientSex())
            && !Objects.equals(queryVo.getPatientSex(), SexEnum.DEFAULT.getCode())) {
            query.setPatientSex(queryVo.getPatientSex());
        }
        // 门诊/住院号
        if (StringUtils.isNotBlank(queryVo.getPatientVisitCard())) {
            query.setPatientVisitCard(queryVo.getPatientVisitCard());
        }

        if (StringUtils.isNotBlank(queryVo.getApplyType())) {
            query.setApplyTypes(Collections.singleton(queryVo.getApplyType()));
        }

        if (StringUtils.isNotBlank(queryVo.getBarcode())) {
            query.setBarcodes(Collections.singleton(queryVo.getBarcode()));
        }
        if (StringUtils.isNotBlank(queryVo.getKaryotype())) {
            query.setKaryotype(queryVo.getKaryotype());
        }
        query.setSorts(
            Lists.newArrayList(SampleEsQuery.Sort.builder().filedName("finalCheckDate").order("ASC").build()));
        return query;
    }

    private static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 填充 excel 内容数据
     *
     * @param list0 excel 数据
     * @param headList 表头
     * @param contentList 内容来源
     */
    private void fillExcelContent(List<List<Object>> list0, List<String> headList,
        List<GeneticsSampleResultListResponseVo> contentList) {

        for (GeneticsSampleResultListResponseVo contentData : contentList) {
            List<Object> content = Lists.newArrayListWithCapacity(headList.size());
            if (Objects.isNull(contentData.getStatus())) {
                content.add(StringUtils.EMPTY);
            } else {
                content.add(SampleStatusEnum.getStatusByCode(contentData.getStatus()).getDesc());
            }
            content.add(contentData.getSampleNo());
            content.add(contentData.getTestItemName());
            content.add(contentData.getPatientName());
            content.add(contentData.getBarcode());
            content.add(DateFormatUtils.format(contentData.getTestDate(), DATE_PATTERN));
            content.add(DateFormatUtils.format(contentData.getTwoCheckDate(), DATE_PATTERN));
            content.add(contentData.getHspOrgName());
            content.add(contentData.getApplyType());
            list0.add(content);

        }

    }

    /**
     * 查询 检验结果列表 信息
     *
     */
    private List<GeneticsSampleResultListResponseVo>
        selectGeneticsSampleResultListResponseVo(List<GeneticsInspectionDto> sampleEsModels) {
        final List<GeneticsSampleResultListResponseVo> targetList =
            Lists.newArrayListWithCapacity(sampleEsModels.size());
        for (GeneticsInspectionDto model : sampleEsModels) {
            final GeneticsSampleResultListResponseVo sampleVo = new GeneticsSampleResultListResponseVo();
            // -------SpecialtySampleDto------------
            sampleVo.setGeneticsSampleId(model.getSampleId());
            sampleVo.setApplySampleId(model.getApplySampleId());
            sampleVo.setApplyId(model.getApplyId());
            sampleVo.setBarcode(model.getBarcode());
            sampleVo.setTestItemName(model.getTestItems().stream().map(BaseSampleEsModelDto.TestItem::getTestItemName)
                .collect(Collectors.joining(",")));
            sampleVo.setInstrumentGroupId(model.getInstrumentGroupId());
            sampleVo.setInstrumentGroupName(model.getInstrumentGroupName());
            sampleVo.setTestDate(model.getTestDate());
            sampleVo.setSampleNo(model.getSampleNo());
            sampleVo.setHspOrgId(model.getHspOrgId());
            sampleVo.setHspOrgName(model.getHspOrgName());
            sampleVo.setOneCheckerId(model.getOneCheckerId());
            sampleVo.setOneCheckerName(model.getOneCheckerName());
            sampleVo.setTwoCheckerId(model.getTwoCheckerId());
            sampleVo.setTwoCheckerName(model.getTwoCheckerName());
            sampleVo.setTwoCheckDate(model.getFinalCheckDate());


            //-------SpecialtySampleResultDto------------
            sampleVo.setResultType(model.getResultType());

            sampleVo.setCellCount(model.getCellCount());
            sampleVo.setAnalyseCellCount(model.getAnalyseCellCount());
            sampleVo.setSampleSituation(model.getSampleSituation());
            sampleVo.setKaryotype(model.getKaryotype());
            sampleVo.setBandingMethod(model.getBandingMethod());
            sampleVo.setBandingLevel(model.getBandingLevel());
            sampleVo.setAnalyticalOpinion(model.getAnalyticalOpinion());
            sampleVo.setKaryotypeOriginalImg1(model.getKaryotypeOriginalImg1());
            sampleVo.setKaryotypeOriginalImg2(model.getKaryotypeOriginalImg2());
            sampleVo.setKaryotypeOriginalImg3(model.getKaryotypeOriginalImg3());
            sampleVo.setKaryotypeImg1(model.getKaryotypeImg1());
            sampleVo.setKaryotypeImg2(model.getKaryotypeImg2());
            sampleVo.setKaryotypeImg3(model.getKaryotypeImg3());
            sampleVo.setIncubationDays(model.getIncubationDays());
            sampleVo.setMaterialsSubmittedForInspection(model.getMaterialsSubmittedForInspection());
            sampleVo.setMicroscopicExamination(model.getMicroscopicExamination());
            sampleVo.setResultInterpretation(model.getResultInterpretation());
            sampleVo.setTestResult(model.getTestResult());
            sampleVo.setGreenHistogramImg(model.getGreenHistogramImg());
            sampleVo.setRedHistogramImg(model.getRedHistogramImg());
            sampleVo.setRedScatterDiagramImg(model.getRedScatterDiagramImg());
            sampleVo.setWordContent(model.getWordContent());


            // -------------ApplyDto-------------
            sampleVo.setPatientName(model.getPatientName());
            sampleVo.setPatientSex(model.getPatientSex());
            sampleVo.setPatientAge(model.getPatientAge());
            sampleVo.setPatientSubage(model.getPatientSubage());
            sampleVo.setPatientSubageUnit(model.getPatientSubageUnit());
            sampleVo.setDept(model.getDept());
            sampleVo.setPatientVisitCard(model.getPatientVisitCard());
            sampleVo.setApplyType(model.getApplyTypeName());
            sampleVo.setDiagnosis(model.getDiagnosis());

            // ----------applySampleDto----------
            sampleVo.setTesterId(model.getTesterId());
            sampleVo.setTesterName(model.getTesterName());
            sampleVo.setSampleTypeName(model.getSampleTypeName());
            sampleVo.setIsPrint(model.getIsPrint());
            sampleVo.setStatus(model.getSampleStatus());
            sampleVo.setRecordDate(model.getCreateDate());
            sampleVo.setSampleRemark(model.getSampleRemark());
            sampleVo.setResultRemark(model.getResultRemark());

            // ----------SampleReportDto----------
            List<BaseSampleEsModelDto.Report> reports = model.getReports();
            if (CollectionUtils.isNotEmpty(reports)) {
                sampleVo.setFileType(reports.get(0).getFileType());
                sampleVo.setUrl(reports.get(0).getUrl());

            }
            targetList.add(sampleVo);
        }
        return targetList.stream()
            .sorted(Comparator
                .comparing(GeneticsSampleResultListResponseVo::getTwoCheckDate,
                    Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(GeneticsSampleResultListResponseVo::getGeneticsSampleId))
            .collect(Collectors.toList());
    }

}
