package com.labway.lims.genetics.vo;

import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * 遗传 审核前提示
 *
 * <AUTHOR>
 * @since 2023/5/17 11:28
 */
@Getter
@Setter
public class CheckTipsRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 遗传样本ids
     */
    private Set<Long> geneticsSampleIds;

    /**
     * 审核状态
     * 
     * @see SampleAuditStatusEnum 一审：ONE_CHECK 二审:TWO_CHECK
     */
    private String auditStatus;
}
