package com.labway.lims.genetics.service;

import com.alibaba.fastjson.JSONArray;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.genetics.api.dto.GeneticsResultDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleTwoUnPickInfoDto;
import com.labway.lims.genetics.api.enums.GeneticsResultEnum;
import com.labway.lims.genetics.api.service.GeneticsSampleResultService;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.genetics.api.service.GeneticsSampleTwoPickerService;
import com.labway.lims.pdfreport.api.dto.WordContentDto;
import com.labway.lims.pdfreport.api.service.ReportTemplateService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 遗传样本 实现二次分拣
 *
 * <AUTHOR>
 * @since 2023/4/23 16:09
 */
@Slf4j
@DubboService
public class GeneticsSampleTwoPicker implements GeneticsSampleTwoPickerService {

    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;

    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;

    @DubboReference
    private SampleReportItemService sampleReportItemService;

    @Resource
    private GeneticsSampleService geneticsSampleService;
    @Resource
    private GeneticsSampleResultService geneticsSampleResultService;

    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;

    @DubboReference
    private ReportTemplateBindService templateBindService;

    @DubboReference
    private SystemParamService systemParamService;

    @Resource
    private EnvDetector envDetector;

    @DubboReference
    private ReportTemplateService reportTemplateService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long twoPick(long applySampleId, long instrumentGroupId, String sampleNo) {
        LoginUserHandler.User user = LoginUserHandler.get();
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new LimsException("申请单样本不存在");
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new LimsException("样本对应申请单不存在");
        }

        final InstrumentGroupDto instrumentGroupDto =
            instrumentGroupService.selectByInstrumentGroupId(instrumentGroupId);
        if (Objects.isNull(instrumentGroupDto)) {
            throw new LimsException("专业小组不存在");
        }

        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(applySampleId)
            .stream().filter(e -> Objects.equals(e.getItemType(), ItemTypeEnum.GENETICS.name()))
            .filter(obj -> Objects.equals(obj.getGroupId(), instrumentGroupDto.getGroupId()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new LimsException("该样本无检验项目");
        }

        // 遗传正常只有一条信息 若有多条 先暂时取第一条
        final ApplySampleItemDto sampleItem = applySampleItems.get(NumberUtils.INTEGER_ZERO);

        // 检验项目下的报告项目 code
        final List<String> reportItemCodes = reportItemService.selectByTestItemId(sampleItem.getTestItemId()).stream()
            .map(ReportItemDto::getReportItemCode).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(reportItemCodes)) {
            throw new IllegalStateException("没有可以分拣的报告项目");
        }

        // 机构下 这些报告项目 对应仪器报告项目
        final List<InstrumentReportItemDto> instrumentReportItems =
            instrumentReportItemService.selectByInstrumentGroupId(instrumentGroupId).stream()
                    .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                .filter(e -> CollectionUtils.containsAny(reportItemCodes, e.getReportItemCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(instrumentReportItems)) {
            throw new IllegalStateException(String.format("仪器没有绑定 [%s] 报告项目", String.join("、", reportItemCodes)));
        }

        // 含报告项目 最多的仪器
        final Long instrumentId =
            instrumentReportItems.stream().collect(Collectors.groupingBy(InstrumentReportItemDto::getInstrumentId))
                .entrySet().stream().max((o1, o2) -> NumberUtils.compare(o1.getValue().size(), o2.getValue().size()))
                .map(Map.Entry::getKey).orElse(NumberUtils.LONG_ZERO);

        final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentId);
        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("仪器不存在");
        }


        // 【【遗传检验】1、新增多个模板，2、对应的遗传检验结果查询、报告单打印、条码综合信息查询支持显示这几个模板】
        // https://www.tapd.cn/tapd_fe/59091617/story/detail/1159091617001002512

        // 默认用类型1
        Integer type;
        //这里需要多做一步模板的匹配 新增word模板匹配
        ReportTemplateBindDto templateBindDto = templateBindService.selectWordTemplateByTestItem(sampleItem.getTestItemId(), apply.getHspOrgId());
        if (templateBindDto != null) {
            // 遗传 WORD 样本
            type = GeneticsResultEnum.WORD.getType();
            GeneticsSampleDto geneticsSampleDto = getGeneticsSampleDto(type, sampleNo, applySample, instrumentGroupDto, instrument, sampleItem, apply);
            // 构建样本
            geneticsSampleService.addGeneticsSample(geneticsSampleDto);
            // 构建word结果
            this.saveInitSampleWordTemplate(geneticsSampleDto, templateBindDto);
            return geneticsSampleDto.getGeneticsSampleId();

        } else {

            // 没有word结果， 查找配置
            JSONArray configArray = systemParamService.selectAsJsonArrayByParamName(SystemParamNameEnum.GENETICS_RESULT.getCode(), LoginUserHandler.get().getOrgId());

            // 获取配置的模板类型（key）  默认结果（value）
            final GeneticsResultDto.GeneticsResultDtoValue geneticsResultDtoValue = GeneticsResultDto.toGeneticsResultDtoValue(configArray, sampleItem.getTestItemCode(), apply.getPatientSex());
            type = geneticsResultDtoValue.getTemplateType();

            // 构建样本
            GeneticsSampleDto geneticsSampleDto = getGeneticsSampleDto(type, sampleNo, applySample, instrumentGroupDto, instrument, sampleItem, apply);
            geneticsSampleService.addGeneticsSample(geneticsSampleDto);

            // 保存初始化结果
            this.saveInitSampleResult(geneticsSampleDto, geneticsResultDtoValue.getDefaultGeneticsResult());

            return geneticsSampleDto.getGeneticsSampleId();

        }
    }

    private GeneticsSampleDto getGeneticsSampleDto(Integer resultType, String sampleNo, ApplySampleDto applySample, InstrumentGroupDto instrumentGroupDto, InstrumentDto instrument, ApplySampleItemDto sampleItem, ApplyDto apply) {
        GeneticsSampleDto geneticsSampleDto = new GeneticsSampleDto();
        geneticsSampleDto.setGeneticsSampleId(snowflakeService.genId());
        geneticsSampleDto.setApplySampleId(applySample.getApplySampleId());
        geneticsSampleDto.setApplyId(applySample.getApplyId());
        geneticsSampleDto.setBarcode(applySample.getBarcode());
        geneticsSampleDto.setSampleNo(sampleNo);
        geneticsSampleDto.setGroupId(applySample.getGroupId());
        geneticsSampleDto.setGroupName(applySample.getGroupName());
        geneticsSampleDto.setInstrumentGroupId(instrumentGroupDto.getInstrumentGroupId());
        geneticsSampleDto.setInstrumentGroupName(instrumentGroupDto.getInstrumentGroupName());
        geneticsSampleDto.setInstrumentId(instrument.getInstrumentId());
        geneticsSampleDto.setInstrumentName(instrument.getInstrumentName());
        geneticsSampleDto.setTestDate(new Date());
        geneticsSampleDto.setOneCheckerId(NumberUtils.LONG_ZERO);
        geneticsSampleDto.setOneCheckerName(StringUtils.EMPTY);
        geneticsSampleDto.setOneCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        geneticsSampleDto.setTwoCheckerId(NumberUtils.LONG_ZERO);
        geneticsSampleDto.setTwoCheckerName(StringUtils.EMPTY);
        geneticsSampleDto.setTwoCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        geneticsSampleDto.setResultType(GeneticsResultEnum.getGeneticsResultEnum(resultType).getType());
        geneticsSampleDto.setTestItemId(sampleItem.getTestItemId());
        geneticsSampleDto.setTestItemCode(sampleItem.getTestItemCode());
        geneticsSampleDto.setTestItemName(sampleItem.getTestItemName());
        geneticsSampleDto.setHspOrgId(apply.getHspOrgId());
        geneticsSampleDto.setHspOrgName(apply.getHspOrgName());
        return geneticsSampleDto;
    }


    /**
     * 初始化样本word模板
     */
    private void saveInitSampleWordTemplate(GeneticsSampleDto sampleDto, ReportTemplateBindDto templateBindDto) {
        if (sampleDto != null && templateBindDto != null) {
            //进行文件重命名 -- 规则：区域+系统+样本ID+后缀
            String newFileName = envDetector.envName() + "-lims-" + sampleDto.getGeneticsSampleId() + ".docx";

            //上传文件
            WordContentDto wordContentDto = reportTemplateService.uploadFile2OnlineEdit(templateBindDto.getFileUrl(), newFileName);
            if (wordContentDto != null) {
                //存结果
                GeneticsSampleResultDto target = new GeneticsSampleResultDto();
                target.setGeneticsSampleResultId(snowflakeService.genId());
                target.setApplyId(sampleDto.getApplyId());
                target.setApplySampleId(sampleDto.getApplySampleId());
                target.setGeneticsSampleId(sampleDto.getGeneticsSampleId());
                target.setGeneticsResult(GeneticsResultDto.builder().wordContent(wordContentDto).build());
                geneticsSampleResultService.addGeneticsSampleResult(target);
            }
        }
    }

    /**
     * 保存结果
     */
    private void saveInitSampleResult(GeneticsSampleDto sampleDto, GeneticsResultDto geneticsResultDto) {
        //存结果
        GeneticsSampleResultDto target = new GeneticsSampleResultDto();
        target.setGeneticsSampleResultId(snowflakeService.genId());
        target.setApplyId(sampleDto.getApplyId());
        target.setApplySampleId(sampleDto.getApplySampleId());
        target.setGeneticsSampleId(sampleDto.getGeneticsSampleId());

        target.setGeneticsResult(geneticsResultDto);
        geneticsSampleResultService.addGeneticsSampleResult(target);
    }

    @Override
    public GeneticsSampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode())
            || Objects.equals(e.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new IllegalStateException("该样本已经审核，不能取消二次分拣");
        }

        final List<GeneticsSampleDto> geneticsSamples = geneticsSampleService.selectByApplySampleIds(
            applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));

        if (CollectionUtils.isEmpty(geneticsSamples)) {
            throw new IllegalStateException("遗传样本不存在");
        }

        final Set<Long> ids =
            geneticsSamples.stream().map(GeneticsSampleDto::getGeneticsSampleId).collect(Collectors.toSet());

        // 删除遗传样本
        geneticsSampleService.deleteByGeneticsSampleIds(ids);

        // 删除样本报告项目
        sampleReportItemService.deleteBySampleIds(ids);

        // 删除样本结果
        geneticsSampleResultService.deleteByGeneticsSampleIds(ids);

        // 清空生清单样本的结果备注，样本备注以及检验者
        final ApplySampleDto as = new ApplySampleDto();
        as.setSampleRemark(StringUtils.EMPTY);
        as.setResultRemark(StringUtils.EMPTY);
        as.setTesterId(NumberUtils.LONG_ZERO);
        as.setTesterName(StringUtils.EMPTY);
        applySampleService.updateByApplySampleIds(as, applySampleIds);

        log.info("遗传检验取消二次分拣 样本ID [{}] 条码号 [{}] 操作人 [{}]", ids,applySamples.stream().map(ApplySampleDto::getBarcode)
                .collect(Collectors.toSet()), LoginUserHandler.get().getNickname());

        return new GeneticsSampleTwoUnPickInfoDto(geneticsSamples.stream()
            .map(e -> new GeneticsSampleTwoUnPickInfoDto.Sample().setSampleId(e.getGeneticsSampleId())
                .setGroupId(e.getGroupId()).setSampleNo(e.getSampleNo())
                .setTwoPickDate(
                    applySamples.stream().filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                        .findFirst().map(ApplySampleDto::getTwoPickDate).orElseThrow())
                .setInstrumentGroupId(e.getInstrumentGroupId()))
            .collect(Collectors.toList()));
    }
}
