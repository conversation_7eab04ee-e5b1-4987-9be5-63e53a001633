package com.labway.lims.genetics.service.chain.check.one;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 遗传一审
 *
 * <AUTHOR>
 * @since 2023/4/28 17:58
 */
@Component
public class GeneticsOneCheckChain extends ChainBase implements InitializingBean {

    @Resource
    private GeneticsOneCheckParamCheckCommand geneticsOneCheckParamCheckCommand;
    @Resource
    private GeneticsOneCheckUpdateStatusDataCommand geneticsOneCheckUpdateStatusDataCommand;

    @Resource
    private GeneticsOneCheckFlowCommand geneticsOneCheckFlowCommand;

    @Resource
    private GeneticsOneCheckRabbitMQCommand geneticsOneCheckRabbitMQCommand;

    @Resource
    private CheckSampleResultCommand checkSampleResultCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 检查参数
        addCommand(geneticsOneCheckParamCheckCommand);

        //判断异常值
        addCommand(checkSampleResultCommand);

        // 遗传样本 一审 修改样本状态、一审人
        addCommand(geneticsOneCheckUpdateStatusDataCommand);

        // 保存流水
        addCommand(geneticsOneCheckFlowCommand);

        // 发送到 mq
        addCommand(geneticsOneCheckRabbitMQCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);
    }
}
