package com.labway.lims.genetics.service.chain.check.one;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 一审 参数检验
 * 
 * <AUTHOR>
 * @since 2023/5/4 10:09
 */
@Slf4j
@Component
public class GeneticsOneCheckParamCheckCommand implements Command {
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private GeneticsSampleService geneticsSampleService;

    @Override
    public boolean execute(Context context) throws Exception {
        final GeneticsOneCheckContext from = GeneticsOneCheckContext.from(context);
        final Set<Long> geneticsSampleIds = from.getGeneticsSampleIds();

        // 对应选中 遗传样本
        final List<GeneticsSampleDto> geneticsSampleDtos =
            geneticsSampleService.selectByGeneticsSampleIds(geneticsSampleIds);

        final Set<Long> selectGeneticsSampleIds =
            geneticsSampleDtos.stream().map(GeneticsSampleDto::getGeneticsSampleId).collect(Collectors.toSet());

        if (geneticsSampleIds.stream().anyMatch(x -> !selectGeneticsSampleIds.contains(x))) {
            throw new LimsException("存在无效遗传样本");
        }

        // 对应申请单样本ids
        final Set<Long> applyIds =
            geneticsSampleDtos.stream().map(GeneticsSampleDto::getApplyId).collect(Collectors.toSet());
        final List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);
        Map<Long, ApplyDto> applyDtoByApplyId =
            applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        if (applyIds.stream().anyMatch(x -> !applyDtoByApplyId.containsKey(x))) {
            throw new LimsException("已选数据存在对应申请单不存在");
        }

        // 对应申请单样本ids
        final Set<Long> applySampleIds =
            geneticsSampleDtos.stream().map(GeneticsSampleDto::getApplySampleId).collect(Collectors.toSet());

        final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);

        if (applySampleDtos.stream()
            .anyMatch(obj -> !Objects.equals(obj.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode()))) {
            throw new LimsException("已选数据存在一审或已审样本，不可一审");
        }

        for (Long applySampleId : applySampleIds) {

            if (applySampleService.isDisabled(applySampleId)) {
                throw new LimsException(String.format(" [%s] 对应样本已禁用", applySampleId));
            }

            if (applySampleService.isTerminate(applySampleId)) {
                throw new LimsException(String.format(" [%s] 对应样本已经终止检验", applySampleId));
            }
        }

        from.put(GeneticsOneCheckContext.GENETICS_SAMPLE, geneticsSampleDtos);
        from.put(GeneticsOneCheckContext.APPLY, applyDtos);

        return CONTINUE_PROCESSING;
    }
}
