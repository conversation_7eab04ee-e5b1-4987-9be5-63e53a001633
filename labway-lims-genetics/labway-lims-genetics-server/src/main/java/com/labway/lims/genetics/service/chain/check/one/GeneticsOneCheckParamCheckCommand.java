package com.labway.lims.genetics.service.chain.check.one;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSONArray;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.ExceptionCodeEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.genetics.api.dto.GeneticsResultDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleAuditDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;
import com.labway.lims.genetics.api.enums.GeneticsResultEnum;
import com.labway.lims.genetics.api.service.GeneticsSampleResultService;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 一审 参数检验
 * 
 * <AUTHOR>
 * @since 2023/5/4 10:09
 */
@Slf4j
@Component
public class GeneticsOneCheckParamCheckCommand implements Command {
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private GeneticsSampleService geneticsSampleService;
    @Resource
    private GeneticsSampleResultService geneticsSampleResultService;
    @DubboReference
    private SystemParamService systemParamService;

    @Override
    public boolean execute(Context context) throws Exception {
        final GeneticsOneCheckContext from = GeneticsOneCheckContext.from(context);
        final Set<Long> geneticsSampleIds = from.getGeneticsSampleIds();
        final GeneticsSampleAuditDto auditDto = from.getAuditDto();

        // 对应选中 遗传样本
        final List<GeneticsSampleDto> geneticsSampleDtos =
            geneticsSampleService.selectByGeneticsSampleIds(geneticsSampleIds);

        final Set<Long> selectGeneticsSampleIds =
            geneticsSampleDtos.stream().map(GeneticsSampleDto::getGeneticsSampleId).collect(Collectors.toSet());

        if (geneticsSampleIds.stream().anyMatch(x -> !selectGeneticsSampleIds.contains(x))) {
            throw new LimsException("存在无效遗传样本");
        }

        // 对应申请单样本ids
        final Set<Long> applyIds =
            geneticsSampleDtos.stream().map(GeneticsSampleDto::getApplyId).collect(Collectors.toSet());
        final List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);
        Map<Long, ApplyDto> applyDtoByApplyId =
            applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        if (applyIds.stream().anyMatch(x -> !applyDtoByApplyId.containsKey(x))) {
            throw new LimsException("已选数据存在对应申请单不存在");
        }

        // 对应申请单样本ids
        final Set<Long> applySampleIds =
            geneticsSampleDtos.stream().map(GeneticsSampleDto::getApplySampleId).collect(Collectors.toSet());

        final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);

        if (applySampleDtos.stream()
            .anyMatch(obj -> !Objects.equals(obj.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode()))) {
            throw new LimsException("已选数据存在一审或已审样本，不可一审");
        }

        for (Long applySampleId : applySampleIds) {

            if (applySampleService.isDisabled(applySampleId)) {
                throw new LimsException(String.format(" [%s] 对应样本已禁用", applySampleId));
            }

            if (applySampleService.isTerminate(applySampleId)) {
                throw new LimsException(String.format(" [%s] 对应样本已经终止检验", applySampleId));
            }
        }

        if (BooleanUtils.isTrue(auditDto.getDefaultResultForce())) {
            final Map<Long, GeneticsSampleResultDto> geneticsSampleResultDtoMap = geneticsSampleResultService.selectByGeneticsSampleIdsAsMap(geneticsSampleDtos.stream()
                    .map(GeneticsSampleDto::getGeneticsSampleId).collect(Collectors.toSet()));

            final Set<String> barcodeSet = checkResult(applyDtos, geneticsSampleDtos, geneticsSampleResultDtoMap);
            if (CollectionUtils.isNotEmpty(barcodeSet)) {
                throw new LimsCodeException(ExceptionCodeEnum.GENETICS_RESULT_DEFAULT.getCode(), ExceptionCodeEnum.GENETICS_RESULT_DEFAULT.getDesc()).setData(barcodeSet);
            }
        }


        from.put(GeneticsOneCheckContext.GENETICS_SAMPLE, geneticsSampleDtos);
        from.put(GeneticsOneCheckContext.APPLY, applyDtos);

        return CONTINUE_PROCESSING;
    }


    private Set<String> checkResult(List<ApplyDto> applyDtos, List<GeneticsSampleDto> geneticsSampleDtos, Map<Long, GeneticsSampleResultDto> geneticsSampleResultDtoMap) {
        final Map<Long, ApplyDto> applyDtoMap = applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity(), (a, b) -> b));

        // 没有word结果， 查找配置
        JSONArray configArray = systemParamService.selectAsJsonArrayByParamName(SystemParamNameEnum.GENETICS_RESULT.getCode(), LoginUserHandler.get().getOrgId());

        final Map<GeneticsResultDto.GeneticsResultKey, GeneticsResultDto.GeneticsResultDtoValue> geneticsResultDtoMap = GeneticsResultDto.toGeneticsResultDtoMap(configArray);

        // 未校验通过的条码
        return geneticsSampleDtos.stream()
                .map(geneticsSampleDto -> {
                    // 目前只有第一个模板的三个项目会判断红色  （高分辨染色体核型分析、染色体核型分析、骨髓染色体核型分析）
                    if (Objects.equals(geneticsSampleDto.getResultType(), GeneticsResultEnum.KARYOTYPE_1.getType())) {
                        // 获取申请单
                        final ApplyDto applyDto = applyDtoMap.get(geneticsSampleDto.getApplyId());
                        if (applyDto == null) {
                            return null;
                        }
                        // 获取结果
                        final GeneticsSampleResultDto geneticsSampleResultDto = geneticsSampleResultDtoMap.get(geneticsSampleDto.getGeneticsSampleId());
                        if (geneticsSampleResultDto == null) {
                            return null;
                        }
                        // 根据项目编码和性别获取
                        final GeneticsResultDto.GeneticsResultKey geneticsResultKey = new GeneticsResultDto.GeneticsResultKey(geneticsSampleDto.getTestItemCode(), applyDto.getPatientSex());
                        // 获取的项目类型和默认结果
                        final GeneticsResultDto.GeneticsResultDtoValue geneticsResultValue = geneticsResultDtoMap.getOrDefault(geneticsResultKey,new GeneticsResultDto.GeneticsResultDtoValue(GeneticsResultEnum.KARYOTYPE_1.getType(), new GeneticsResultDto()));
                        // 默认结果
                        final GeneticsResultDto geneticsResultDto = geneticsResultValue.getDefaultGeneticsResult();

                        // 分析意见
                        final String analyticalOpinion = geneticsResultDto.getAnalyticalOpinion();
                        if (checkDefaultResult(analyticalOpinion, geneticsSampleResultDto.getGeneticsResult().getAnalyticalOpinion())) {
                            return geneticsSampleDto.getBarcode();
                        }

                        // 核型
                        final String karyotype = geneticsResultDto.getKaryotype();
                        if (checkDefaultResult(karyotype, geneticsSampleResultDto.getGeneticsResult().getKaryotype())) {
                            return geneticsSampleDto.getBarcode();
                        }
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

    }

    private boolean checkDefaultResult(String defaultResult, String result) {
        return StringUtils.isNotBlank(defaultResult) && !Objects.equals(defaultResult, result);
    }
}
