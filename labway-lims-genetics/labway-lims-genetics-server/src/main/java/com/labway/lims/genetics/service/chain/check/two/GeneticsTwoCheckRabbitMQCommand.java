package com.labway.lims.genetics.service.chain.check.two;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.api.consts.MqConstants.EXCHANGE;
import static com.labway.lims.api.consts.MqConstants.ROUTING_KEY;

/**
 * 发送消息到mq
 * 
 * <AUTHOR>
 * @since 2023/6/16 17:53
 */
@Slf4j
@Component
public class GeneticsTwoCheckRabbitMQCommand implements Command {

    @DubboReference
    private RabbitMQService rabbitMQService;

    @Override
    public boolean execute(Context context) throws Exception {
        final GeneticsTwoCheckContext from = GeneticsTwoCheckContext.from(context);
        final List<GeneticsSampleDto> geneticsSampleList = from.getGeneticsSampleList();
        final List<ApplyDto> applyDtoList = from.getApplyDtoList();
        final LoginUserHandler.User user = from.getUser();

        Map<Long, ApplyDto> applyDtoByApplyId =
            applyDtoList.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        for (GeneticsSampleDto sampleDto : geneticsSampleList) {
            ApplyDto applyDto = applyDtoByApplyId.get(sampleDto.getApplyId());
            final ApplySampleEventDto event = new ApplySampleEventDto();
            event.setEvent(ApplySampleEventDto.EventType.TwoCheck);
            event.setOrgId(user.getOrgId());
            event.setHspOrgId(applyDto.getHspOrgId());
            event.setHspOrgCode(applyDto.getHspOrgCode());
            event.setHspOrgName(applyDto.getHspOrgName());
            event.setApplyId(applyDto.getApplyId());
            event.setApplySampleId(sampleDto.getApplySampleId());
            event.setBarcode(sampleDto.getBarcode());
            event.setExtras(Map.of("sampleId", String.valueOf(sampleDto.getGeneticsSampleId()), "sampleNo",
                sampleDto.getSampleNo()));

            final String json = JSON.toJSONString(event);
            rabbitMQService.convertAndSend(EXCHANGE, ROUTING_KEY, json);

            log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", sampleDto.getApplySampleId(), sampleDto.getBarcode(),
                json, EXCHANGE, ROUTING_KEY);
        }

        return CONTINUE_PROCESSING;
    }
}
