package com.labway.lims.genetics.mapstruct;

import com.alibaba.fastjson.JSON;
import com.labway.lims.genetics.api.dto.GeneticsResultDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;
import com.labway.lims.genetics.model.TbGeneticsSampleResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 遗传样本 装换
 * 
 * <AUTHOR>
 * @since 2023/5/8 13:22
 */
@Mapper(componentModel = "spring")
public interface GeneticsSampleResultConverter {


    @Mapping(target = "geneticsResult", expression = "java(parseGeneticsResult(obj.getGeneticsResult()))")
    GeneticsSampleResultDto fromTbGeneticsSampleResult(TbGeneticsSampleResult obj);

    @Mapping(target = "geneticsResult", expression = "java(parseGeneticsResult(obj.getGeneticsResult()))")
    TbGeneticsSampleResult tbGeneticsSampleResultFromTbObjDto(GeneticsSampleResultDto obj);

    default GeneticsResultDto parseGeneticsResult(String geneticsResult) {
        return JSON.parseObject(geneticsResult, GeneticsResultDto.class);
    }

    default String parseGeneticsResult(GeneticsResultDto geneticsResultDto) {
        return JSON.toJSONString(geneticsResultDto);
    }
}
