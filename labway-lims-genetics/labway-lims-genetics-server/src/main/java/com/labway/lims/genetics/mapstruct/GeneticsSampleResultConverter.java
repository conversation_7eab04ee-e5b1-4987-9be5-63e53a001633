package com.labway.lims.genetics.mapstruct;

import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;
import com.labway.lims.genetics.model.TbGeneticsSampleResult;
import org.mapstruct.Mapper;

/**
 * 遗传样本 装换
 * 
 * <AUTHOR>
 * @since 2023/5/8 13:22
 */
@Mapper(componentModel = "spring")
public interface GeneticsSampleResultConverter {

    GeneticsSampleResultDto fromTbGeneticsSampleResult(TbGeneticsSampleResult obj);
    TbGeneticsSampleResult tbGeneticsSampleResultFromTbObjDto(GeneticsSampleResultDto obj);

}
