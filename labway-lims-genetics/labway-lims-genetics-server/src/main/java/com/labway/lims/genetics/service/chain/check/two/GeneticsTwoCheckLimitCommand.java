package com.labway.lims.genetics.service.chain.check.two;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.exception.LimsException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 加锁
 * 
 * <AUTHOR>
 * @since 2023/6/27 14:17
 */
@Slf4j
@Component
public class GeneticsTwoCheckLimitCommand implements Command, Filter {
    private static final String MARK = GeneticsTwoCheckLimitCommand.class.getName();
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @Override
    public boolean execute(Context context) throws Exception {
        final GeneticsTwoCheckContext from = GeneticsTwoCheckContext.from(context);
        log.info("mark:{}", MARK);
        from.getGeneticsSampleIds().forEach(id -> {
            final String mark = getMark(id);
            log.info("mark:{}", mark);
            if (BooleanUtils.isNotTrue(
                stringRedisTemplate.opsForValue().setIfAbsent(mark, StringUtils.EMPTY, Duration.ofSeconds(10)))) {
                throw new LimsException("存在正在审核条码");
            }
        });
        from.put(MARK, StringUtils.EMPTY);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception e) {
        final GeneticsTwoCheckContext from = GeneticsTwoCheckContext.from(context);

        if (from.containsKey(MARK)) {
            from.getGeneticsSampleIds().forEach(id -> {
                final String mark = getMark(id);
                stringRedisTemplate.delete(mark);

                log.info("删除key:{}", mark);
            });
        }
        return CONTINUE_PROCESSING;
    }


    private String getMark(long sampleId){
        return redisPrefix.getBasePrefix() + MARK + sampleId;
    }

}
