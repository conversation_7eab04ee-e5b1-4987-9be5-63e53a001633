package com.labway.lims.genetics.service.chain.check.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 遗传样本 二审 修改样本状态、二审人
 * 
 * <AUTHOR>
 * @since 2023/5/4 10:55
 */
@Slf4j
@Component
public class GeneticsTwoCheckUpdateStatusDataCommand implements Command {

    @DubboReference
    private ApplySampleService applySampleService;

    @Resource
    private GeneticsSampleService geneticsSampleService;

    @Override
    public boolean execute(Context context) throws Exception {
        final GeneticsTwoCheckContext from = GeneticsTwoCheckContext.from(context);
        final LoginUserHandler.User user = from.getUser();
        final UserDto twoCheckUser = from.getTwoCheckUser();
        final List<GeneticsSampleDto> geneticsSampleList = from.getGeneticsSampleList();
        final Map<Long, String> applySampleIdAndReportNoMap = from.getApplySampleIdAndReportNoMap();

        // 修改遗传样本二审人、二审时间
        Set<Long> geneticsSampleIdList =
            geneticsSampleList.stream().map(GeneticsSampleDto::getGeneticsSampleId).collect(Collectors.toSet());

        GeneticsSampleDto updateGeneticsSampleDto = new GeneticsSampleDto();
        updateGeneticsSampleDto.setTwoCheckerId(Objects.isNull(twoCheckUser) ? user.getUserId() : twoCheckUser.getUserId());
        updateGeneticsSampleDto.setTwoCheckerName(Objects.isNull(twoCheckUser) ? user.getNickname() : twoCheckUser.getNickname());
        updateGeneticsSampleDto.setTwoCheckDate(new Date());

        geneticsSampleService.updateByGeneticsSampleIds(updateGeneticsSampleDto, geneticsSampleIdList);

        // 修改 申请单 样本状态为已审 并写入报告编号
        final List<ApplySampleDto> updateApplySampleList = geneticsSampleList.stream()
                .map(e -> {
                    ApplySampleDto updateApplySampleDto = new ApplySampleDto();
                    updateApplySampleDto.setApplySampleId(e.getApplySampleId());
                    updateApplySampleDto.setStatus(SampleStatusEnum.AUDIT.getCode());
                    updateApplySampleDto.setReportNo(applySampleIdAndReportNoMap.get(e.getApplySampleId()));
                    return updateApplySampleDto;
                })
                .collect(Collectors.toList());
        applySampleService.updateBatchByApplySampleIds(updateApplySampleList);

        return CONTINUE_PROCESSING;
    }
}
