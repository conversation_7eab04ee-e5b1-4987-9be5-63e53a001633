package com.labway.lims.genetics.service.chain.check.one;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleAuditDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.List;
import java.util.Set;

/**
 * 一审信息内容
 *
 * <AUTHOR>
 * @since 2023/5/4 9:55
 */
@Getter
@Setter
public class GeneticsOneCheckContext extends StopWatchContext {

    /**
     * 遗传样本ids
     */
    private Set<Long> geneticsSampleIds;
    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    /**
     * 获取 信息参数 从上下文中
     */
    public static GeneticsOneCheckContext from(Context context) {
        return (GeneticsOneCheckContext)context;
    }

    // 遗传样本
    public static final String GENETICS_SAMPLE = "GENETICS_SAMPLE_" + IdUtil.objectId();

    public static final String SAMPLE_AUDTI_DTO = "SAMPLE_AUDTI_DTO" + IdUtil.objectId();

    public GeneticsSampleAuditDto getAuditDto() {
        return (GeneticsSampleAuditDto) get(SAMPLE_AUDTI_DTO);
    }

    public List<GeneticsSampleDto> getGeneticsSampleList() {
        return (List<GeneticsSampleDto>)get(GENETICS_SAMPLE);
    }

    // 申请单
    public static final String APPLY = "APPLY_" + IdUtil.objectId();

    public List<ApplyDto> getApplyDtoList() {
        return (List<ApplyDto>)get(APPLY);
    }

    @Override
    protected String getWatcherName() {
        return "遗传检验一审";
    }
}
