package com.labway.lims.genetics.api.service;

import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 遗传样本结果 Service
 *
 * <AUTHOR>
 * @since 2023/4/23 11:12
 */
public interface GeneticsSampleResultService {

    /**
     * 修改 遗传样本结果
     */
    void updateByGeneticsSampleResultId(GeneticsSampleResultDto dto);

    /**
     * 根据 遗传样本结果id 查询 信息
     */
    @Nullable
    GeneticsSampleResultDto selectByGeneticsSampleResultId(long geneticsSampleResultId);

    /**
     * 根据 遗传样本id 查询遗传样本结果
     */
    @Nullable
    GeneticsSampleResultDto selectByGeneticsSampleId(long geneticsSampleId);

    /**
     * 根据 遗传样本id 查询遗传样本结果
     */
    List<GeneticsSampleResultDto> selectByGeneticsSampleIds(Collection<Long> geneticsSampleIds);

    /**
     * 根据 遗传样本id 查询遗传样本结果
     */
    Map<Long, GeneticsSampleResultDto> selectByGeneticsSampleIdsAsMap(Collection<Long> geneticsSampleIds);

    /**
     * 添加 遗传样本结果
     */
    long addGeneticsSampleResult(GeneticsSampleResultDto dto);

    /**
     * 根据遗传样本id 记录
     */
    boolean deleteByGeneticsSampleId(long geneticsSampleId);

    /**
     * 根据遗传样本id 记录
     */
    void deleteByGeneticsSampleIds(Collection<Long> geneticsSampleIds);


    /**
     * 页面 修改 遗传样详情
     *
     * @param sampleDto 遗传样本
     * @param oldResult 对应旧结果
     * @param newResul  更新结果
     */
    void updateGeneticsResultDetail(GeneticsSampleDto sampleDto, GeneticsSampleResultDto oldResult,
                                    GeneticsSampleResultDto newResul);
}
