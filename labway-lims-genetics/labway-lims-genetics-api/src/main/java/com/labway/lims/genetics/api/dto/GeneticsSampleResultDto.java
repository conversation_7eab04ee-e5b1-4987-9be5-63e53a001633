package com.labway.lims.genetics.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 检验详情（与样本一对一）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class GeneticsSampleResultDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检验详情ID
     */
    private Long geneticsSampleResultId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 样本ID
     */
    private Long geneticsSampleId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 细胞数
     */
    @Compare("细胞计数")
    private String cellCount;

    /**
     * 分析细胞数
     */
    @Compare("分析细胞数")
    private String analyseCellCount;

    /**
     * 标本情况
     */
    @Compare("标本情况")
    private String sampleSituation;

    /**
     * 核型
     */
    @Compare("核型")
    private String karyotype;

    /**
     * 显带方法
     */
    @Compare("显带方法")
    private String bandingMethod;

    /**
     * 显带水平
     */
    @Compare("显带水平")
    private String bandingLevel;

    /**
     * 分析意见
     */
    @Compare("分析意见")
    private String analyticalOpinion;

    /**
     * 1:删除 0:未删
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 原始图像1
     */
    @Compare("原始图像1")
    private String karyotypeOriginalImg1;

    /**
     * 原始图像2
     */
    @Compare("原始图像2")
    private String karyotypeOriginalImg2;

    /**
     * 原始图像3
     */
    @Compare("原始图像3")
    private String karyotypeOriginalImg3;

    /**
     * 分析图像1
     */
    @Compare("核型分析图像1")
    private String karyotypeImg1;

    /**
     * 分析图像2
     */
    @Compare("核型分析图像2")
    private String karyotypeImg2;

    /**
     * 分析图像3
     */
    @Compare("核型分析图像3")
    private String karyotypeImg3;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
