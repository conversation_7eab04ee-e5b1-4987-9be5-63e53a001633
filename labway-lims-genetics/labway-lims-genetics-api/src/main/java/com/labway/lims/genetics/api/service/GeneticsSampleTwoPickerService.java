package com.labway.lims.genetics.api.service;

import com.labway.lims.genetics.api.dto.GeneticsSampleTwoUnPickInfoDto;

import java.util.Collection;

/**
 * 遗传二次分拣
 *
 * <AUTHOR>
 * @since 2023/4/27 17:58
 */
public interface GeneticsSampleTwoPickerService {

    /**
     * 二次分拣
     */
    long twoPick(long applySampleId, long instrumentGroupId, String sampleNo);

    /**
     * 取消二次分拣
     */
    GeneticsSampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds);
}
