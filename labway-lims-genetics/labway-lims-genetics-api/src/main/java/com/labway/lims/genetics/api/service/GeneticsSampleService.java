package com.labway.lims.genetics.api.service;

import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleAuditDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.SelectGeneticsSampleDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 遗传检验 Service
 * 
 * <AUTHOR>
 * @since 2023/3/22 19:25
 */
public interface GeneticsSampleService {

    /**
     * 根据检验时间查看
     */
    List<GeneticsSampleDto> selectBySelectGeneticsSampleDto(SelectGeneticsSampleDto dto);

    /**
     * 根据id 查询遗传样本信息
     */
    @Nullable
    GeneticsSampleDto selectByGeneticsSampleId(long geneticsSampleId);

    /**
     * 添加 遗传样本
     */
    long addGeneticsSample(GeneticsSampleDto dto);

    /**
     * 根据ids 查询遗传样本信息
     */
    List<GeneticsSampleDto> selectByGeneticsSampleIds(Collection<Long> geneticsSampleIds);

    /**
     * 根据ID修改
     */
    void updateByGeneticsSampleId(GeneticsSampleDto dto);

    /**
     * 根据ID修改
     */
    void updateByGeneticsSampleIds(GeneticsSampleDto dto, Collection<Long> geneticsSampleIds);

    /**
     * 根据 申请单样本id 查询遗传样本信息
     */
    @Nullable
    GeneticsSampleDto selectByApplySampleId(long applySampleId);

    /**
     * 根据 申请单样本id 查询遗传样本信息
     */
    List<GeneticsSampleDto> selectByApplySampleIds(Collection<Long> applySampleId);

    /**
     * 删除 遗传样本
     */
    boolean deleteByGeneticsSampleId(long geneticsSampleId);

    /**
     * 删除 遗传样本
     */
    void deleteByGeneticsSampleIds(Collection<Long> geneticsSampleId);

    /**
     * 遗传样本一审
     */
    void oneCheck(GeneticsSampleAuditDto auditDto);

    /**
     * 遗传样本二审
     */
    void twoCheck(Set<Long> geneticsSampleIds);

    /**
     * 遗传样本 取消一审
     * 
     * @param dto 遗传样本
     */
    void cancelOneCheck(GeneticsSampleDto dto);

    /**
     * 遗传样本 取消二审
     *
     * @param dto 遗传样本
     */
    void cancelTwoCheck(GeneticsSampleDto dto);

    /**
     * 重新生成报告
     *
     * @return
     */
    SampleReportDto rebuildReport(long applySampleId);

    /**
     * 根据applyId修改遗传样本信息
     */
    void updateByApplyId( GeneticsSampleDto geneticsSampleDto );

    /**
     * 根据applyIds修改遗传样本信息
     */
    void updateByApplyIds(GeneticsSampleDto geneticsSampleDto, Collection<Long> applyIds);
}
