package com.labway.lims.genetics.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询 遗传样本 参数 Dto
 *
 * <AUTHOR>
 * @since 2023/4/24 13:52
 */
@Getter
@Setter
public class SelectGeneticsSampleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检验日期开始
     */
    private Date testDateStart;
    /**
     * 检验日期结束
     */
    private Date testDateEnd;
    /**
     * 审核日期开始
     */
    private Date twoCheckDateStart;
    /**
     * 审核日期结束
     */
    private Date twoCheckDateEnd;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 专业组ID
     */
    private Long groupId;
    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;
}
