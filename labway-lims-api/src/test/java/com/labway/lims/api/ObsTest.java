package com.labway.lims.api;

import com.obs.services.ObsClient;
import com.obs.services.model.AccessControlList;
import com.obs.services.model.PutObjectRequest;
import com.obs.services.model.PutObjectResult;
import org.junit.Test;

import java.io.FileInputStream;
import java.time.LocalDate;
import java.util.UUID;

public class ObsTest {
    /**
     * 朗迦测试
     */
    @Test
    public void test() throws Exception {
        final String bucketName = "labway-obs";
        final String ak = "OLOWEFGGDOSAHPDWTUON";
        final String sk = "2Tln6mUlyIB53ellyZj7XtoLLWPO9PNFBBayAFgh";
        final String endpoint = "obs.cn-east-3.myhuaweicloud.com";
        final String baseUrl = "https://obs.labway.cn/";

        final ObsClient obsClient = new ObsClient(ak, sk, endpoint);

        final LocalDate now = LocalDate.now();
        // 区域码
        final String orgCode = "11";
        // 日期
        final String date = now.getYear() + "/" + now.getMonthValue() + "/" + now.getDayOfMonth();
        final String filename = "labway-report/langjia/" + orgCode + "/" + date + "/" + UUID.randomUUID() + ".pdf";

        final PutObjectRequest request = new PutObjectRequest();
        request.setBucketName(bucketName);
        request.setObjectKey(filename);
        // pdf file
        request.setInput(new FileInputStream("/Users/<USER>/Downloads/pdf_open_parameters_acro8.pdf"));
        request.setAutoClose(true);
        // 单独配置此对象共公读取
        request.setAcl(AccessControlList.REST_CANNED_PUBLIC_READ);

        final PutObjectResult result = obsClient.putObject(request);
        System.out.println(baseUrl + result.getObjectKey());
    }
}
