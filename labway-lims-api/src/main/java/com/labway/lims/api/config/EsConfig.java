package com.labway.lims.api.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <pre>
 * EsConfig
 * ES的一些公共配置
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/4/8 14:15
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "es")
public class EsConfig {

    /**
     * 查询ES数据每页数据量大小
     */
    Integer pageSize = 1000;

    /**
     * 每页每次最多查 maxPageSize 条数据
     */
    Integer maxPageSize = 5000;

    Map<String, Integer> pageSizeMap = new HashMap<>();

    public Integer getPageSize(String page, Integer pageSize) {
        return pageSizeMap.getOrDefault(page,
                Math.min(Objects.isNull(pageSize) ? this.pageSize : pageSize, maxPageSize));
    }
}
