package com.labway.lims.api.config;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * LimsOrgCodeConfig
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/3/10 14:07
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties("lims")
public class LimsOrgCodeConfig {

    /**
     * 使用 LIMS系统 的机构编码
     */
    private List<String> orgCodes = new ArrayList<>();

    /**
     * 实验室中配置的外送机构编码映射
     * 对于已经在用的实验室LIMS系统，其中外送到LIMS系统机构的编码有些已经在使用了，需要做一下映射。
     * 如：东莞 外送到 广州
     * 东莞实验室配置的“广州兰卫医学检验实验室有限公司(委外)” 编码为 “263401”，我们自己系统使用的编码是“00010110000000001WR5”
     */
    private Map<String, String> orgCodeMap = new HashMap<>();

    public boolean isLimsSystem(String orgCode) {
        return CollectionUtils.isNotEmpty(orgCodes) && orgCodes.contains(orgCode);
    }

    public String getMappedOrgCode(String orgCode) {
        if (MapUtils.isEmpty(orgCodeMap)) {
            return orgCode;
        }
        return orgCodeMap.getOrDefault(orgCode, orgCode);
    }

}
