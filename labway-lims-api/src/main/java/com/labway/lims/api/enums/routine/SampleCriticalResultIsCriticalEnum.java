package com.labway.lims.api.enums.routine;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 样本危机值 是否危急值
 * 
 * <AUTHOR>
 * @since 2023/4/11 10:08
 */
@Getter
@AllArgsConstructor
public enum SampleCriticalResultIsCriticalEnum {

    CRITICAL_NO(0, "否"),

    CRITICAL_YES(1, "是"),

    ;

    private final int code;

    private final String msg;

    public static SampleCriticalResultIsCriticalEnum getByCode(int code) {
        for (SampleCriticalResultIsCriticalEnum statusEnum : SampleCriticalResultIsCriticalEnum.values()) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum;
            }
        }
        return CRITICAL_NO;
    }

}
