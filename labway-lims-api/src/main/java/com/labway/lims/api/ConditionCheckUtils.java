package com.labway.lims.api;

import java.util.regex.Pattern;

/**
 * 条件 判断 工具
 *
 * <AUTHOR>
 * @since 2023/5/5 14:47
 */
public class ConditionCheckUtils {

    /**
     * 小数最多只允许两位小数
     *
     * @param str 数字字符串
     * @return true 符合条件 false 不满足
     */
    public static boolean isDecimalWithTwoDigits(String str) {
        return isDecimalWithDigits(str, 2);
    }

    /**
     * 判断位数
     */
    public static boolean isDecimalWithDigits(String str, int digit) {
        String pattern = String.format("^\\d+(\\.\\d{1,%d})?$", digit);
        return Pattern.matches(pattern, str);
    }

    /**
     * 是否为有效月份格式 yyyy-MM
     *
     * @param str 需要验证
     * @return true 符合条件 false 不满足
     */
    public static boolean isValidMonthFormat(String str) {
        String regex = "\\d{4}-\\d{2}";
        return Pattern.matches(regex, str);
    }

}
