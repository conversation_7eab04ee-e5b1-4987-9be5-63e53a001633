package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物料 需要生成的单号 类型
 * 
 * <AUTHOR>
 * @since 2023/5/9 11:59
 */
@Getter
@AllArgsConstructor
public enum MaterialNoType {

    APPLY_NO("SL", "申领单号"),

    INCOME_NO("RK", "入库单号"),

    CHECK_NO("PD", "盘点单号"),

    JH_PREFIX ("JH", "专业组物料计划"),

    ;

    private final String code;

    private final String desc;
}