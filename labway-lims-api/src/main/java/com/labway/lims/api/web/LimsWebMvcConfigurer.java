package com.labway.lims.api.web;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.exception.AuthenticationException;
import com.labway.lims.api.trace.TraceContextKeys;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

@Slf4j
@Component
@ConditionalOnWebApplication
class LimsWebMvcConfigurer implements WebMvcConfigurer {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisPrefix redisPrefix;

    /**
     * 随机字符串，代替忽略的路径 表示此接口不需要校验权限，通知后续拦截器 跳过校验 token
     */
    private static final String IGNORE_AUTHORIZATION_TOKEN = "3CF61AF5C06FD7E749EE6C84E05C929C23E321569897695F31E3731558518964";

    private static final String HEADER = "Authorization";

    @Resource
    private EnvDetector envDetector;


    @Override
    public void addInterceptors(@Nonnull InterceptorRegistry registry) {
        // 请求信息
        registry.addInterceptor(new RequestInterceptor()).addPathPatterns("/**").excludePathPatterns("/error");
        // 权限
        registry.addInterceptor(new AuthorizationInterceptor()).addPathPatterns("/**").excludePathPatterns("/error");
    }

    /**
     * long to string
     */
    @Bean
    @ConditionalOnMissingBean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> builder.serializerByType(Long.class, ToStringSerializer.instance)
                .serializerByType(Long.TYPE, ToStringSerializer.instance);
    }

    /**
     * 信息校验
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    private class AuthorizationInterceptor implements HandlerInterceptor {
        @Trace
        @Override
        public boolean preHandle(@Nonnull HttpServletRequest request, @Nonnull HttpServletResponse response,
                                 @Nonnull Object handler) throws Exception {

            if (HttpMethod.OPTIONS.matches(request.getMethod()) || handler instanceof ResourceHttpRequestHandler) {
                return true;
            }

            final String token = getAuthorization(request);

            if (StringUtils.isBlank(token)) {
                throw new AuthenticationException("Authorization 错误或已失效");
            }

            if (Objects.equals(IGNORE_AUTHORIZATION_TOKEN, token)) {
                return true;
            }

            final String user = stringRedisTemplate.opsForValue().get(redisPrefix.getTokenKey(token));
            if (StringUtils.isBlank(user)) {
                throw new AuthenticationException("Authorization 错误或已失效");
            }

            LoginUserHandler.set(JSON.parseObject(user, LoginUserHandler.User.class));

            // 用户信息存入链路
            TraceContext.putCorrelation(TraceContextKeys.OrgName.name(), LoginUserHandler.get().getOrgName());
            TraceContext.putCorrelation(TraceContextKeys.Username.name(), LoginUserHandler.get().getUsername());
            TraceContext.putCorrelation(TraceContextKeys.Nickname.name(), LoginUserHandler.get().getNickname());
            TraceContext.putCorrelation(TraceContextKeys.UserId.name(), String.valueOf(LoginUserHandler.get().getUserId()));
            TraceContext.putCorrelation(TraceContextKeys.GroupName.name(), LoginUserHandler.get().getGroupName());
            TraceContext.putCorrelation(TraceContextKeys.Token.name(), LoginUserHandler.get().getToken());


            return true;
        }

        @Override
        public void postHandle(@Nonnull HttpServletRequest request, @Nonnull HttpServletResponse response,
                               @Nonnull Object handler, ModelAndView modelAndView) throws Exception {
            LoginUserHandler.remove();
        }

        /**
         * 当被全局异常捕获器捕获时不会执行
         * {@link #postHandle(HttpServletRequest, HttpServletResponse, Object, ModelAndView)}
         * 这里会执行的
         */
        @Override
        public void afterCompletion(@Nonnull HttpServletRequest request, @Nonnull HttpServletResponse response,
                                    @Nonnull Object handler, Exception ex) {
            LoginUserHandler.remove();
        }


    }

    /**
     * 请求信息拦截
     */
    @SuppressWarnings("all")
    private static class RequestInterceptor implements HandlerInterceptor {
        @Trace
        @Override
        public boolean preHandle(@Nonnull HttpServletRequest request, @Nonnull HttpServletResponse response,
                                 @Nonnull Object handler) throws Exception {

            if (HttpMethod.OPTIONS.matches(request.getMethod()) || handler instanceof ResourceHttpRequestHandler) {
                return true;
            }

            String requestId = request.getHeader("X-Real-IP");

            if (StringUtils.isBlank(requestId)) {
                final String xForwardedForHeader = request.getHeader("X-Forwarded-For");
                // 如果经过代理 那么获取到真实IP
                if (StringUtils.isNotBlank(xForwardedForHeader)) {
                    requestId = xForwardedForHeader.split(" ")[0];
                }
            }

            if (StringUtils.isBlank(requestId)) {
                final String xForwardedHostHeader = request.getHeader("X-Forwarded-Host");
                // 如果经过代理 那么获取到真实IP
                if (StringUtils.isNotBlank(xForwardedHostHeader)) {
                    requestId = xForwardedHostHeader.split(" ")[0];
                }
            }

            if (StringUtils.isBlank(requestId)) {
                requestId = request.getRemoteAddr();
            }

            TraceContext.putCorrelation(TraceContextKeys.RemoteAddr.name(), requestId);

            return true;
        }

        @Override
        public void postHandle(@Nonnull HttpServletRequest request, @Nonnull HttpServletResponse response,
                               @Nonnull Object handler, ModelAndView modelAndView) throws Exception {
        }

        @Override
        public void afterCompletion(@Nonnull HttpServletRequest request, @Nonnull HttpServletResponse response,
                                    @Nonnull Object handler, Exception ex) {
        }


    }


    /**
     * 先从 Header 中取 , 然后 Parameter , 再然后 Cookie
     */
    @Nullable
    private static String getAuthorization(HttpServletRequest request) {
        // 参数
        String authorization = request.getParameter(HEADER);
        if (StringUtils.isNotBlank(authorization)) {
            return authorization;
        }

        // header
        authorization = request.getHeader(HEADER);
        if (StringUtils.isNotBlank(authorization)) {
            return authorization;
        }

        // cookie
        final Cookie[] cookies = request.getCookies();
        if (Objects.nonNull(cookies)) {
            for (Cookie cookie : cookies) {
                if (StringUtils.equalsIgnoreCase(cookie.getName(), HEADER) && (StringUtils.isNotBlank(cookie.getValue()))) {
                    return cookie.getValue();
                }
            }
        }

        return null;

    }
}
