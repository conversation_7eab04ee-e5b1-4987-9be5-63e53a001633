package com.labway.lims.api.enums.base;

import lombok.Getter;

@Getter
public enum InstrumentItemTypeEnum {

    // 定性
    QUALITATIVE("定性", "00001"),
    // 定量
    QUANTITATIVE("定量", "00002"),
    // 计算类型
    CALCULATE("计算类型", "00003"),
    // 其他
    OTHER("其他", "00004"),
    ;


    private final String desc;

    private final String code;

    InstrumentItemTypeEnum(String desc, String code) {
        this.desc = desc;
        this.code = code;
    }


    public static InstrumentItemTypeEnum selectByName(String name) {
        for (InstrumentItemTypeEnum item : InstrumentItemTypeEnum.values()) {
            if (item.getDesc().equals(name)) {
                return item;
            }
        }
        return null;
    }
}
