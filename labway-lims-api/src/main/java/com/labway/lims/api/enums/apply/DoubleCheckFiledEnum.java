package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DoubleCheckFiledEnum {
    /**
     * 就诊类型
     */
    APPLY_TYPE("applyType", "就诊类型"),
    /**
     * 患者名称
     */
    PATIENT_NAME("patientName", "患者名称"),
    /**
     * 患者性别
     */
    PATIENT_SEX("patientSex", "患者性别"),
    /**
     * 患者生日
     */
    PATIENT_BIRTHDAY("patientBirthday", "患者生日"),
    /**
     * 患者年龄
     */
    PATIENT_AGE("patientAge", "患者年龄"),
    /**
     * 急诊状态  1加急 0不加急
     *
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    URGENT("urgent", "急诊状态"),
    /**
     * 样本数量
     */
    SAMPLE_COUNT("sampleCount", "样本数量"),
    /**
     * 样本性状
     */
    SAMPLE_PROPERTY_CODE("samplePropertyCode", "样本性状"),
    /**
     * 申请时间
     */
    APPLY_DATE("applyDate", "申请时间"),
    /**
     * 采样时间
     */
    SAMPLING_DATE("samplingDate", "采样时间"),
    /**
     * 门诊|住院号
     */
    PATIENT_VISIT_CARD("patientVisitCard", "门诊|住院号"),
    /**
     * 科室
     */
    DEPT("dept", "科室"),
    /**
     * 床号
     */
    PATIENT_BED("patientBed", "床号"),
    /**
     * 送检医生
     */
    SEND_DOCTOR("sendDoctor", "送检医生"),
    /**
     * 临床诊断
     */
    CLINICAL_DIAGNOSIS("clinicalDiagnosis", "临床诊断"),
    /**
     * 备注
     */
    REMARK("remark", "备注"),
    /**
     * 手机号
     */
    PATIENT_MOBILE("patientMobile", "电话"),

    /**
     * 外部条码号
     */
    OUT_BARCODE("outBarcode","外部条码号"),
    /**
     * 身份证
     */
    PATIENT_CARD("patientCard", "身份证");




    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;


    public static DoubleCheckFiledEnum getByCode(String code) {
        for (DoubleCheckFiledEnum value : DoubleCheckFiledEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
