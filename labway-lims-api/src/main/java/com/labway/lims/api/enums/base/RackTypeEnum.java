package com.labway.lims.api.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 试管架 类型
 *
 * <AUTHOR>
 * @since 2023/4/13 17:06
 */
@Getter
@AllArgsConstructor
public enum RackTypeEnum {
    /**
     * 普通归档架
     */
    ARCHIVE_RACK("Archive_Rack", "普通归档架"),
    /**
     * 长期归档架
     */
    PERMANENT_ARCHIVE_RACK("Permanent_Archive_Rack","长期归档架");

    private final String code;
    private final String desc;

    /**
     * 判断试管架是否是归档试管架
     * @param code
     * @return
     */
    public static boolean isArchiveRack(String code){
        return ARCHIVE_RACK.code.equals(code) || PERMANENT_ARCHIVE_RACK.code.equals(code);
    }
}
