package com.labway.lims.api.config;

import io.github.mweirauch.micrometer.jvm.extras.ProcessMemoryMetrics;
import io.github.mweirauch.micrometer.jvm.extras.ProcessThreadMetrics;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.binder.MeterBinder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <a href="https://github.com/mweirauch/micrometer-jvm-extras">https://github.com/mweirauch/micrometer-jvm-extras</a>
 */
@Slf4j
@Configuration
@ConditionalOnClass({Gauge.class, ProcessMemoryMetrics.class, ProcessThreadMetrics.class})
class MicrometerJvmExtrasConfig implements InitializingBean {
    @Bean
    public MeterBinder processMemoryMetrics() {
        return new ProcessMemoryMetrics();
    }

    @Bean
    public MeterBinder processThreadMetrics() {
        return new ProcessThreadMetrics();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("init MicrometerJvmExtrasConfig");
    }
}
