package com.labway.lims.api.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 项目类型
 */
@Getter
@AllArgsConstructor
public enum ItemTypeEnum {
    /**
     * 遗传
     */
    GENETICS("遗传检验"),
    /**
     * 常规检验
     */
    ROUTINE("常规检验"),
    /**
     * 院感
     */
    INFECTION("院感检验"),
    /**
     * 微生物
     */
    MICROBIOLOGY("微生物检验"),
    /**
     * 特检
     */
    SPECIALTY("特检"),
    /**
     * 外送检验
     */
    OUTSOURCING("外送检验"),

    /**
     * 病理检验
     */
    PATHOLOGY("病理检验"),

    /**
     * 血培养检验
     */
    BLOOD_CULTURE("血培养检验"),

    /**
     * 其他
     */
    OTHER("其他");
    private final String desc;


    public static ItemTypeEnum getByName(String name) {
        return Arrays.stream(values()).filter(i -> i.name().equals(name)).findFirst().orElse(OTHER);
    }
}
