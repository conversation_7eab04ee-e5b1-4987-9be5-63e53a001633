package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * AdvanceQueryConditionEnum
 * 高级查询比较条件
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/8 15:11
 */
@Getter
@AllArgsConstructor
public enum AdvanceQueryConditionEnum {

    EQ("相等"),
    NE("不相等"),
    LIKE("相似查询"),
    RANGE("范围查询"),

    ;

    private final String desc;

    public static final String SEPARATOR = "，|,";

    public static AdvanceQueryConditionEnum of(String condition) {
        try {
            return AdvanceQueryConditionEnum.valueOf(condition);
        } catch (Exception e) {
            return EQ;
        }
    }
}
