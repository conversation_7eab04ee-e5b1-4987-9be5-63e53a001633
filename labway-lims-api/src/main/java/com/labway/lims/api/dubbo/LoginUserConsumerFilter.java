package com.labway.lims.api.dubbo;

import com.labway.lims.api.LoginUserHandler;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;

import java.util.Objects;

/**
 * 负责用户信息传递
 */
@Activate(group = CommonConstants.CONSUMER)
public class LoginUserConsumerFilter implements Filter {

    static final String LOGIN_USER_INFO_KEY = "LOGIN_USER_INFO_KEY";

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        final LoginUserHandler.User unsafe = LoginUserHandler.getUnsafe();
        if (Objects.nonNull(unsafe)) {
            invocation.setAttachmentIfAbsent(LOGIN_USER_INFO_KEY, unsafe);
        }
        return invoker.invoke(invocation);
    }
}
