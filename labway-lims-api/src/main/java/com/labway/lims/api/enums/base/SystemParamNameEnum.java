package com.labway.lims.api.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 送检 类型 字典之外 额外 配置
 *
 * <AUTHOR>
 * @since 2023/10/10 14:18
 */
@Getter
@AllArgsConstructor
public enum SystemParamNameEnum {

    /**
     * 分拣前置交接
     */
    SORTING_PRE_HANDOVER("SORTING_PRE_HANDOVER", "分拣前置交接"),

    /**
     * 分血前置交接
     */
    SORTING_BLOOD_PRE_HANDOVER("SORTING_BLOOD_PRE_HANDOVER", "分血前置交接"),

    /**
     * 报告单是否可以合并打印
     */
    MERGE_PRINT("MERGE-PRINT", "合并打印"),

    /**
     * 是否允许多PDF
     */
    ALLOW_SAMPLE_MULTI_PDF("ALLOW_SAMPLE_MULTI_PDF", "是否允许多PDF"),

    /**
     * his签收是否允许分管
     */
    HIS_SIGN_SPLIT_TUBE("HIS_SIGN_SPLIT_TUBE", "his签收是否允许分管"),

    /**
     * 传染病报告项目编码集合
     */
    INFECTIOUS_DISEASES_REPORT_CODES("INFECTIOUS_DISEASES_REPORT_CODES", "传染病报告项目编码集合"),

    /**
     * 微生物检验项目样本性状配置
     */
    MICROBIOLOGY_SAMPLE_PROPERTY("MICROBIOLOGY_SAMPLE_PROPERTY", "微生物检验项目样本性状配置"),

    /**
     * 危急值超时配置(单位:分钟)
     */
    CRITICAL_RESULT_TIME_OUT("CRITICAL_RESULT_TIME_OUT", "危急值超时配置"),

    /**
     * 微生物仪器结果通知是否使用仪器配置 ，
     * 如果配置，则传输结果时 单位固定（ug/ml）， 折点范围取仪器传过来的，
     */
    MICROBIOLOGY_NOTIFY_RESULT("MICROBIOLOGY_NOTIFY_RESULT", "微生物仪器通知结果，是否取仪器折点范围"),

    /**
     * 送检机构报告延迟配置（单位：小时）
     */
    HSP_ORG_REPORT_DELAY_CONFIG("HSP_ORG_REPORT_DELAY_CONFIG", "送检机构报告延迟配置"),

    /**
     * 送检机构报告延迟配置（单位：小时）
     */
    MICROBIOLOGY_ONE_AUDIT("MICROBIOLOGY_ONE_AUDIT", "微生物一次审核配置"),

    /**
     * 分血后那个专业组使用原条码
     */
    SPLIT_BLOOD_ORIGINAL_BARCODE_GROUP_ORDER("SPLIT_BLOOD_ORIGINAL_BARCODE_GROUP_ORDER", "分血后那个专业组使用原条码"),

    /**
     * 互认报告项目
     */
    TOGETHER_ACCEPT_REPORT_CODE("TOGETHER_ACCEPT_REPORT_CODE","互认报告项目"),

    /**
     * 调用外部结果时需要忽略的报告项目
     */
    IGNORE_OUTSOURCING_REPORT_ITEM("IGNORE_OUTSOURCING_REPORT_ITEM","调用外部结果时需要忽略的报告项目"),

	/**
	 * 委外组编码，OUTSOURCING默认已用
	 */
	OUTSOURCING_GROUP_CODE("OUTSOURCING", "委外组编码，OUTSOURCING默认已用"),
    ;

    private final String code;

    private final String desc;
}
