package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/3/29 16:55
 */
@Getter
@AllArgsConstructor
public enum SampleStatusEnum {
    /**
     * 已录入
     */
    ENTER(0, "已录入"),
    /**
     * 未审
     */
    NOT_AUDIT(10, "未审"),
    /**
     * 待复查
     */
    RETEST(11, "待复查"),
    /**
     * 一审
     */
    ONE_AUDIT(20, "一审"),
    /**
     * 已审
     */
    AUDIT(30, "已审"),
    /**
     * 终止检验 不可进行任何操作
     */
    STOP_TEST(99, "终止检验"),
    /**
     * 在血培养检验中，98 是阳性标记。此状态只会在学培养检验中出现
     */
    BC_POSITIVE(98, "阳性"),
    /**
     * 反审
     */
    COUNTERTRIAL(40,"反审"),
    /**
     *  重审
     */
    RETRIAL(50,"重审")
    ;
    private final int code;

    private final String desc;
    public static SampleStatusEnum getStatusByCode(int code) {
        for (SampleStatusEnum statusEnum : SampleStatusEnum.values()) {
            if (statusEnum.getCode() == code) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException("Invalid status");
    }


    public static boolean contains(Integer code) {
        for (SampleStatusEnum statusEnum : SampleStatusEnum.values()) {
            if (statusEnum.getCode() == code) {
                return true;
            }
        }
        return false;
    }

}
