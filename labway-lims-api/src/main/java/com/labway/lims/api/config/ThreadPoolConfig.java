package com.labway.lims.api.config;

import cn.hutool.core.thread.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ThreadPoolConfig implements InitializingBean, DisposableBean {

    private ThreadPoolExecutor executorService;

    /**
     * 获取线程池
     */
    public ThreadPoolExecutor getPool() {
        return executorService;
    }

    @Override
    public void destroy() throws Exception {
        executorService.shutdownNow();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        executorService = new ThreadPoolExecutor(0, Integer.MAX_VALUE,
                10L, TimeUnit.SECONDS,
                new SynchronousQueue<>(), new NamedThreadFactory("lims-", null, true,
                (t, e) -> log.error("Thread [{}]] threw exception", t.getName(), e)));
    }
}
