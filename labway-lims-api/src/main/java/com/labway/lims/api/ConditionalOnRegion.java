package com.labway.lims.api;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.type.AnnotatedTypeMetadata;

import javax.annotation.Nonnull;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.Map;
import java.util.Objects;

/**
 * 环境条件
 *
 * <AUTHOR>
 * @since 2022/5/17 16:27
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Conditional(ConditionalOnRegion.OnRegionCondition.class)
public @interface ConditionalOnRegion {

    String TEST = "test";

    String DEV = "dev";

    String UAT = "uat";

    String PROD = "prod";

    String DONGGUAN = "dongguan";

    String CHANGZHOU = "changzhou";

    String DANYANG = "danyang";

    String CHANGSHA = "changsha";

    String NANJING = "nanjing";

    String SHANGHAI = "shanghai";

    /**
     * danyang、dongguan、liwan... 如果包含当前环境那么此类可以被注册
     *
     * @see EnvDetector
     */
    String[] value();


    /**
     * OnRegionCondition
     *
     * <AUTHOR>
     * @see EnvDetector
     * @since 2022/5/17 16:26
     */
    @Configuration
    class OnRegionCondition implements Condition {

        @Override
        public boolean matches(@Nonnull ConditionContext context, @Nonnull AnnotatedTypeMetadata metadata) {
            final Map<String, Object> attributes = metadata.getAnnotationAttributes(ConditionalOnRegion.class.getName());
            if (Objects.isNull(attributes)) {
                return false;
            }

            final String[] value = (String[]) attributes.get("value");
            if (Objects.isNull(value)) {
                return false;
            }

            final String[] profiles = context.getEnvironment().getActiveProfiles();

            for (String profile : value) {
                if (ArrayUtils.contains(profiles, profile)) {
                    return true;
                }
            }

            return false;
        }
    }

}
