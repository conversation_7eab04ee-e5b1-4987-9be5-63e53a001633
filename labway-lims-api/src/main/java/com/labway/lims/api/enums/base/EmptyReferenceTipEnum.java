package com.labway.lims.api.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <pre>
 * EmptyReferenceTipEnum
 * 空参考范围提示 0:不提示，1:禁止审核，2:审核提示
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/13 15:00
 */
@Getter
@AllArgsConstructor
public enum EmptyReferenceTipEnum {

    /**
     * 不提示
     */
    NONE(0),
    /**
     * 禁止审核
     */
    FORBIDDEN(1),
    /**
     * 审核提示
     */
    WARNING(2),

    ;

    public final Integer code;

    public static boolean needInterception(Integer code) {
        return FORBIDDEN.getCode().equals(code) || WARNING.getCode().equals(code);
    }
}
