package com.labway.lims.api;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.LinkedList;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@ConditionalOnClass(StringRedisTemplate.class)
public class ReportNoUtils {

    // LABWAY:LIMS:reportNo:unique:20250610:
    private static final String keyFormat = "%s%s:%s:%s:";

    @Resource
    private EnvDetector envDetector;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisPrefix redisPrefix;


    public String genReportNo() {
        return genReportNos(1).stream().findFirst().get();
    }

    public LinkedList<String> genReportNos(int count) {
        final String date = getDate();
        final String key = redisKey(date);
        final Long increment = stringRedisTemplate.opsForValue().increment(key, count);
        stringRedisTemplate.expire(key, 1, TimeUnit.DAYS);

        final LinkedList<String> reportNos = new LinkedList<>();

        for (long i = increment; i > increment - count; i--) {
            final String s = date + String.format("%06d", i);
            reportNos.add(s);
        }
        return reportNos;
    }

    private String redisKey(String date) {
        final String env;
        if (envDetector.isTest() || envDetector.isDev()) {
            env = "unique";
        } else {
            env = envDetector.envName();
        }

        return String.format(keyFormat, redisPrefix.getBasePrefix(), "reportNo", env, date);
    }

    /**
     * year month day
     */
    public String getDate() {
        LocalDate now;

        do {

            now = LocalDate.now();

            // 如果加上一天小于 0 那么表示当前时间可能是 23:59:59.xxx
        } while (now.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
                //
                - System.currentTimeMillis() <= 0);

        // 2025-06-10
        return now.getYear() + String.format("%02d", now.getMonthValue()) + String.format("%02d", now.getDayOfMonth());
    }
}
