package com.labway.lims.api;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * redis 前缀
 */
@Component
public class RedisPrefix {

    private static final String PREFIX = "LABWAY:LIMS:";

    private static final String TOKEN = "TOKEN:";

    @Resource
    private EnvDetector envDetector;

    /**
     * 获取通用的前缀
     */
    public String getBasePrefix() {

        if (envDetector.isUat()) {
            return getBasePrefix0() + "UAT:";
        } else if (envDetector.isDongguan()) {
            return getBasePrefix0() + "DONGGUAN:";
        } else if (envDetector.isChangzhou()) {
            return getBasePrefix0() + "CHANGZHOU:";
        } else if (envDetector.isDanyang()) {
            return getBasePrefix0() + "DANYANG:";
        } else if (envDetector.isChangsha()) {
            return getBasePrefix0() + "CHANGSHA:";
        } else if (envDetector.isNanjing()) {
            return getBasePrefix0() + "NANJING:";
        } else if (envDetector.isShanghai()) {
            return getBasePrefix0() + "SHANGHAI:";
        }

        return getBasePrefix0();
    }

    private String getBasePrefix0() {
        return PREFIX;
    }

    /**
     * 获取 token 的key
     */
    public String getTokenKey(String token) {
        return getBasePrefix() + TOKEN + token;
    }
}
