package com.labway.lims.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/6/2 15:47
 */
@Getter
@AllArgsConstructor
public enum ExceptionCodeEnum {

    ROUTINE_AUDIT(20000, "审核提示中断"),

    GENETICS_RESULT_DEFAULT(20001, "核型结果或分析意见与原内容不一致"),


    REGAIN_STOP_TEST(1004,"样本加减项目恢复终止条码"),


    MICROBIOLOGY_SAMPLE_PROPERTY(9001,"请选择微生物样本项目的样本性状！"),

    //项目结果值转换存在冲突，请检查结果值转换配置
    CONFLICT_RESULT_CONVERT(9002,"项目结果值转换存在冲突[%s]，请检查结果值转换配置！"),

    /**
     * 空参考范围禁止审核
     */
    EMPTY_REFERENCE_FORBIDDEN(20000, "%s 条码号中%s空参考范围，不允许审核"),

    /**
     * 空参考范围审核提示
     */
    EMPTY_REFERENCE_WARNING(20000, "%s 条码号中%s空参考范围"), // %s 条码号中%s空参考范围，是否继续审核

    /**
     * 样本结果规则校验提示
     */
    SAMPLE_RESULTS_RULE_VERIFY_TIPS(21002, "样本结果规则校验提示"),

    /**
     * 检验项目禁用提示
     */
    TEST_ITEM_FORBIDDEN_TIPS(2025052301, "外部检验项目 [%s] 存在未启用的对照项目信息！"),

    ;

    private final Integer code;

    private final String desc;
}
