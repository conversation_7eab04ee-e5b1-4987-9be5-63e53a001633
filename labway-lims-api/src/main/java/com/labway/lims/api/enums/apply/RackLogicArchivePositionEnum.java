package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 样本 归档逻辑试管架位置
 */
@Getter
@AllArgsConstructor
public enum RackLogicArchivePositionEnum {

    /**
     * 未知 默认值
     */
    UNKNOWN(0, "空闲"),
    /**
     * 使用中
     */
    IN_USE(97, "已使用"),
    /**
     * 结束 表示不会再使用此逻辑试管架
     */
    END(99, "结束"),

    ;

    /**
     * code
     */
    private final int code;

    private final String desc;

    public static RackLogicArchivePositionEnum getByCode(int code) {
        for (RackLogicArchivePositionEnum value : RackLogicArchivePositionEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
