package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collections;
import java.util.Objects;
import java.util.Set;

/**
 * <p>
 * SampleStatusEnum
 * 样本状态：未复核、已签收、正在检验、完成检验
 *     <pre>
 *     未复核：未复核和未双输复核的数据
 *     已签收：完成双输复核、复核和样本签收的数据
 *     正在检验：二次分拣完成且未审核的数据
 *     完成检验：已审核的数据
 *     </pre>
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/9 15:01
 */
@Getter
@AllArgsConstructor
public enum ApplySampleStatusEnum {

    ///////////////////////  SampleStatusEnum
    /** 已录入 */
    // ENTER(0, "已录入"),
    /** 未审 */
    // NOT_AUDIT(10, "未审"),
    /** 待复查 */
    // RETEST(11, "待复查"),
    /** 一审 */
    // ONE_AUDIT(20, "一审"),
    /** 已审 */
    // AUDIT(30, "已审"),
    /** 终止检验 不可进行任何操作 */
    // STOP_TEST(99, "终止检验"),

    ///////////////////////  ApplyStatusEnum
    /** 待补录 */
    // PENDING_RECORDING(0,"待补录"),
    /** 双输复核 */
    // WAIT_DOUBLE_CHECK(1, "待双输复核"),
    /** 待复核 */
    // WAIT_CHECK(2, "待复核"),
    /** 已复核 */
    // CHECK(3, "已复核"),
    /** 已双输复核 */
    // DOUBLE_CHECK(4, "已双输复核"),

    /**
     * 未复核：未复核和未双输复核的数据
     * ApplyStatusEnum.WAIT_DOUBLE_CHECK
     * ApplyStatusEnum.WAIT_CHECK
     */
    NOT_CHECK(1, Set.of(ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode(), ApplyStatusEnum.WAIT_CHECK.getCode())),

    /**
     * 已签收：完成双输复核、复核和样本签收的数据
     * ApplyStatusEnum.CHECK
     * ApplyStatusEnum.DOUBLE_CHECK
     */
    SIGNED(2, Set.of(ApplyStatusEnum.CHECK.getCode(), ApplyStatusEnum.DOUBLE_CHECK.getCode())),

    /**
     * 正在检验：二次分拣完成且未审核的数据
     * SampleStatusEnum.NOT_AUDIT 未审核
     * SampleStatusEnum.RETEST    待复查
     * SampleStatusEnum.ONE_AUDIT 一审
     * isTwoPick = 1 && sampleStatus = 10
     */
    TESTING(3, Set.of(SampleStatusEnum.NOT_AUDIT.getCode(), SampleStatusEnum.RETEST.getCode(), SampleStatusEnum.ONE_AUDIT.getCode())),

    /**
     * 完成检验：已审核的数据
     * SampleStatusEnum.AUDIT
     */
    TESTED(4, Set.of(SampleStatusEnum.AUDIT.getCode())),

    /**
     * 没有对应的状态
     */
    DEFAULT(-1, Collections.emptySet())
    ;

    private final Integer code;
    /**
     * 申请单样本状态
     */
    private final Set<Integer> status;

    public static ApplySampleStatusEnum getByCode(Integer code) {
        for (ApplySampleStatusEnum statusEnum : values()) {
            if (Objects.equals(code, statusEnum.getCode())) {
                return statusEnum;
            }
        }
        return DEFAULT;
    }
}
