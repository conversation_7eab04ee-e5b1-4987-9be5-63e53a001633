package com.labway.lims.api.enums.routine;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/4/14 14:43
 */
@Getter
@AllArgsConstructor
public enum ResultStatusEnum {

    /**
     * 正常
     */
    NORMAL(0),

    /**
     * 危机
     */
    CRISIS(1),

    /**
     * 异常
     */
    EXCEPTION(2);


    private final int code;

    public static boolean isExceptionOrCrisis(int code) {
        return Objects.equals(EXCEPTION.code, code) || Objects.equals(CRISIS.code, code);
    }

    public static boolean isNormal(int code) {
        return Objects.equals(NORMAL.code, code);
    }

}
