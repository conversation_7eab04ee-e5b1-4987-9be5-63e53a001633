package com.labway.lims.api.enums.routine;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/8/20 10:31
 */
@Getter
@AllArgsConstructor
public enum SampleResultTypeEnum {

    /**
     * 报告结果
     */
    REPORT(1,"报告结果"),
    /**
     * 复查结果
     */
    RETEST(2,"复查结果"),

    /**
     * 原始结果
     */
    ORIGIN(3,"原始结果"),
    /**
     * 默认
     */
    DEFAULT(-1,"");
    private final int code;

    private final String des;

    public static String getDescByEnum(SampleResultTypeEnum typeEnum){
        if (typeEnum == null){
            return DEFAULT.getDes();
        }
        return typeEnum.getDes();
    }
}
