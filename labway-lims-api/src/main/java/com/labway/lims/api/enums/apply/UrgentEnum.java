package com.labway.lims.api.enums.apply;

import lombok.Getter;

import java.util.Objects;

/**
 * 申请单 紧急程度枚举
 *
 * <AUTHOR>
 */
@Getter
public enum UrgentEnum {
    /**
     * 普通
     */
    NORMAL(0, "普通"),
    /**
     * 加急
     */
    URGENT(1, "加急"),
    /**
     * 急诊
     */
    EMERGENCY(2, "急诊"),
    /**
     * 让步检验
     *
     */
    CONCESSION_TESTING(3, "让步检验"),
    ;


    private final Integer code;
    private final String value;

    UrgentEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据code获取枚举
     */
    public static UrgentEnum getUrgentEnum(Integer code) {
        if (code == null) {
            return null;
        }
        for (UrgentEnum urgentEnum : UrgentEnum.values()) {
            if (Objects.equals(urgentEnum.code, code)) {
                return urgentEnum;
            }
        }
        return null;
    }
}
