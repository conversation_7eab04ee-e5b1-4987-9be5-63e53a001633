package com.labway.lims.api.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "hsp-org")
public class HspOrgConfig {

    // 需要走ca认证的医院
    private List<String> hspOrgCodes = new ArrayList<>();

    // ca 签章名字
    private List<String> sealNames = new ArrayList<>();

    // pdfTemplateCode   CAPdfConfig

    // 检验者配置
    private CAPdf test = new CAPdf();

    // 审核者配置
    private CAPdf audit = new CAPdf();

    // 业务专用 公章
    private CAPdf office = new CAPdf();


}
