package com.labway.lims.api.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 试管架 孔位 列顺序
 * 
 * <AUTHOR>
 * @since 2023/4/14 10:30
 */
@Getter
@AllArgsConstructor
public enum RackHoleColumnOrderEnum {

    LEFT_TO_RIGHT(1,  "先左后右"),

    RIGHT_TO_LEFT(0, "先右后左"),


    ;

    private final Integer code;
    private final String desc;

    public static RackHoleColumnOrderEnum getByCode(Integer code) {
        for (RackHoleColumnOrderEnum value : values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return RackHoleColumnOrderEnum.LEFT_TO_RIGHT;
    }

}
