package com.labway.lims.api.enums.base;

import lombok.Getter;

/**
 * 二次分拣类型
 */
@Getter
public enum TwoPickTypeEnum {

    /**
     * 普通分拣
     */
    COMMON("二次分拣", "0"),
    /**
     * 周一
     */
    FORCE_CHECK("强制二次分拣", "1"),
    /**
     * 周二
     */
    TESTITEM_CHECK("项目强制二次分拣", "2"),
    ;

    private final String desc;

    private final String code;

    TwoPickTypeEnum(String desc, String code) {
        this.desc = desc;
        this.code = code;
    }


    public static TwoPickTypeEnum selectByCode(String code) {
        for (TwoPickTypeEnum item : TwoPickTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
