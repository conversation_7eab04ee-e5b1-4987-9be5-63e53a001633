package com.labway.lims.api.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <pre>
 * LabwayNumberUtils
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/3/25 14:29
 */
@Slf4j
public final class LabwayNumberUtils {
    public static final Map<String, BigDecimal> RESULT_SYMBOL = new HashMap<>();

    // 科学计数法
    static final String SCIENTIFIC_REGEX =
            "^([-+]?\\d*\\.?\\d+([eE][-+]?\\d+)?|[-+]?(\\d*\\.?\\d+|\\d+)\\s*[*×]\\s*10[\\^]\\s*[-+]?\\d+|\\d*\\.?\\d+\\s*\\*?×?\\s*[eE][-+]?\\d+)$";
    static final Pattern SCIENTIFIC_PATTERN = Pattern.compile(SCIENTIFIC_REGEX);

    public static boolean isParsable(String result) {
        if (StringUtils.isBlank(result)) {
            return false;
        }

        // 数字
        if (NumberUtils.isParsable(result)) {
            return true;
        }

        // 科学计数法
        if (isScientificNotation(result)) {
            return true;
        }

        // 带符号的结果
        final BigDecimal incNumber = RESULT_SYMBOL.get(String.valueOf(result.charAt(0)));
        if (Objects.nonNull(incNumber)) {
            result = result.substring(NumberUtils.INTEGER_ONE);

            return NumberUtils.isParsable(result) || isScientificNotation(result);
        }

        return false;
    }

    public static Double toDouble(String result) {
        if (StringUtils.isBlank(result)) {
            throw new IllegalArgumentException("result is blank");
        }

        // 数字
        if (NumberUtils.isParsable(result)) {
            return NumberUtils.toDouble(result);
        }

        // 科学计数法
        if (isScientificNotation(result)) {
            BigDecimal decimal = scientificToBigDecimal(result);
            return Objects.isNull(decimal) ? Double.MIN_VALUE : decimal.doubleValue();
        }

        // 带符号的结果
        final BigDecimal incNumber = RESULT_SYMBOL.get(String.valueOf(result.charAt(0)));
        if (Objects.nonNull(incNumber)) {
            final String numberResult = result.substring(NumberUtils.INTEGER_ONE);

            // 带符号的数字
            if (NumberUtils.isParsable(numberResult)) {
                return new BigDecimal(numberResult).add(incNumber).doubleValue();
            }

            // 带符号的科学计数法
            if (isScientificNotation(numberResult)) {
                BigDecimal decimal = scientificToBigDecimal(numberResult);
                return Objects.isNull(decimal) ? Double.MIN_VALUE : decimal.add(incNumber).doubleValue();
            }

            return Double.MIN_VALUE;
        }


        return Double.MIN_VALUE;
    }

    /**
     * 判断结果值是否是科学计数法 目前兼容 1.5×10^3 1.5*10^3 1.5*e3 1.5*E3 1.5e3 1.5E3 类型的科学计数法
     */
    private static boolean isScientificNotation(String str) {
        // 正则表达式用于匹配科学计数法的格式，包括带有幂符号的形式
        return SCIENTIFIC_PATTERN.matcher(str).matches();
    }

    public static BigDecimal scientificToBigDecimal(String result) {
        if (StringUtils.isBlank(result)) {
            throw new IllegalArgumentException("result is blank");
        }

        BigDecimal decimal;
        try {
            String v = result;

            if (StringUtils.contains(result, "×10^")) {
                v = result.replace("×10^", "E");
            }
            if (StringUtils.contains(result, "*10^")) {
                v = result.replace("*10^", "E");
            }
            v = v.replace("*", "");

            decimal = new BigDecimal(v);

            return decimal;
        } catch (Exception e) {
            log.info("科学计数法 转换失败：{}", result);
        }

        return null;
    }

    static {
        RESULT_SYMBOL.put("<", new BigDecimal("-0.000001"));
        RESULT_SYMBOL.put("＜", new BigDecimal("-0.000001"));

        RESULT_SYMBOL.put(">", new BigDecimal("0.000001"));
        RESULT_SYMBOL.put("＞", new BigDecimal("0.000001"));

        RESULT_SYMBOL.put("≤", BigDecimal.ZERO);
        RESULT_SYMBOL.put("≥", BigDecimal.ZERO);
    }


}
