package com.labway.lims.api.stream;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * <pre>
 * StreamUtils
 * java stream 工具
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/7/4 9:46
 */
public final class StreamUtils {

    /**
     * {@link java.util.stream.Stream#filter(Predicate)}
     * <pre>
     *     根据对象的某个字段去重
     *
     *     {@code List.of().stream().filter(StreamUtils.distinctByKey(BaseSampleEsModelDto::getApplySampleId))}
     * </pre>
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyFunction) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyFunction.apply(t), Boolean.TRUE) == null;
    }

}
