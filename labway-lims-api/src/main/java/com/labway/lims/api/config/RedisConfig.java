package com.labway.lims.api.config;

import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonRedisSerializer;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.RedisPrefix;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.CacheKeyPrefix;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.time.Duration;

/**
 * redis 缓存
 */
@Configuration
@EnableCaching
@ConditionalOnClass(RedisCache.class)
class RedisConfig {
    @Resource
    private EnvDetector envDetector;
    @Value("${spring.application.name}")
    private String appName;
    @Resource
    private RedisPrefix redisPrefix;

    @Bean
    @Primary
    public RedisCacheManager redisCacheManager(RedisConnectionFactory connectionFactory) {

        final Duration ttl;

        // 测试环境 10 秒
        if (envDetector.isDev() || envDetector.isTest() || envDetector.isUat()) {
            ttl = Duration.ofSeconds(10);
        } else {
            ttl = Duration.ofHours(1);
        }

        return RedisCacheManager.builder(connectionFactory).cacheDefaults(defaultCacheConfig().entryTtl(ttl))
                .transactionAware().build();
    }

    private RedisCacheConfiguration defaultCacheConfig() {
        return RedisCacheConfiguration.defaultCacheConfig().computePrefixWith(new CacheKeyPrefix() {
            @Override
            @Nonnull
            public String compute(@Nonnull String cacheName) {
                // prefix appName key
                return String.format("%s%s:%s", redisPrefix.getBasePrefix(), appName, cacheName);
            }
        }).serializeValuesWith(
                RedisSerializationContext.SerializationPair.fromSerializer(getFastJsonRedisSerializer()));
    }

    private RedisSerializer<Object> getFastJsonRedisSerializer() {

        ParserConfig.getGlobalInstance().setAutoTypeSupport(true);
        ParserConfig.getGlobalInstance().addAccept("com.labway.");
        ParserConfig.getGlobalInstance().addAccept("cn.labway.");
        final FastJsonConfig config = new FastJsonConfig();
        config.setSerializerFeatures(SerializerFeature.WriteClassName);
        final FastJsonRedisSerializer<Object> serializer = new FastJsonRedisSerializer<>(Object.class);
        serializer.setFastJsonConfig(config);
        return serializer;
    }
}
