package com.labway.lims.api.enums;

import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <pre>
 * WeekEnum
 * 周 枚举
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/30 15:49
 */
@Getter
@AllArgsConstructor
public enum WeekEnum {

    MONDAY(DayOfWeek.MONDAY, 1, "1", "星期一"),
    TUESDAY(DayOfWeek.TUESDAY, 2, "2", "星期二"),
    WEDNESDAY(DayOfWeek.WEDNESDAY, 3, "3", "星期三"),
    THURSDAY(DayOfWeek.THURSDAY, 4, "4", "星期四"),
    FRIDAY(DayOfWeek.FRIDAY, 5, "5", "星期五"),
    SATURDAY(DayOfWeek.SATURDAY, 6, "6", "星期六"),
    SUNDAY(DayOfWeek.SUNDAY, 7, "7", "星期日"),

    ;

    private final DayOfWeek dayOfWeek;
    private final Integer code;
    private final String strCode;
    private final String desc;

    public static final List<Integer> WEEK_DAYS = Arrays.stream(values()).map(WeekEnum::getCode).sorted().collect(Collectors.toList());

    public static WeekEnum from(Date date) {
        return getByDayOfWeek(DateUtil.toLocalDateTime(date).getDayOfWeek());
    }

    public static WeekEnum from(LocalDateTime localDateTime) {
        return getByDayOfWeek(localDateTime.getDayOfWeek());
    }

    /**
     * 根据{@code LocalDateTime#getDayOfWeek}获取
     * @param dayOfWeek DayOfWeek
     */
    public static WeekEnum getByDayOfWeek(DayOfWeek dayOfWeek) {
        Objects.requireNonNull(dayOfWeek);
        return Arrays.stream(values()).filter(e -> e.dayOfWeek.equals(dayOfWeek))
                .findAny().orElseThrow(() -> new IllegalArgumentException(dayOfWeek + " 周 不支持"));
    }

    /**
     * 根据code获取
     * @param code 1,2,3,5,6,7对应周一到周日
     */
    public static WeekEnum getByCode(int code) {
        return Arrays.stream(values()).filter(e -> e.code.equals(code))
                .findAny().orElseThrow(() -> new IllegalArgumentException(code + " 周 不支持"));
    }

    /**
     * 判断 检验项目 二次分拣日期 是否是当日
     * 未设置默认当日
     * @param twoPickDay    检验项目 二次分拣日期
     */
    public static boolean isCurrent(String twoPickDay) {
        return StringUtils.isEmpty(twoPickDay) || "0".equals(twoPickDay);
    }

    /**
     * 判断两个日期对应的星期是否相同
     * @param date
     * @param localDateTime
     * @return
     */
    public static boolean isMatchWeek(Date date, LocalDateTime localDateTime) {
        return from(date) == from(localDateTime);
    }

    /**
     * 判断两个日期对应的星期是否相同
     * @param localDateTime1
     * @param localDateTime2
     * @return
     */
    public static boolean isMatchWeek(LocalDateTime localDateTime1, LocalDateTime localDateTime2) {
        return from(localDateTime1) == from(localDateTime2);
    }

}
