package com.labway.lims.api.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 科学计数法工具类
 */
@Slf4j
public class ScientificNotationUtil {

    public static final Map<String, BigDecimal> RESULT_SYMBOL = Map.of(
            "<", new BigDecimal("-0.000001"),
            "＜", new BigDecimal("-0.000001"),
            ">", new BigDecimal("0.000001"),
            "＞", new BigDecimal("0.000001"),
            "≤", BigDecimal.ZERO,
            "≥", BigDecimal.ZERO
    );

    /**
     *
     * 科学计数法比较结果
     * result  >  result2   1
     * result  =  result2   0
     * result  <  result2  -1
     */
    @Nullable
    public static int compare(final String result, final String result2) {
        try {

            final boolean resultIsNumber = isNumber(result);
            final boolean compareResultIsNumber = isNumber(result2);
            // 两个都是数字（科学计数法 或者 数字 ）
            if (resultIsNumber && compareResultIsNumber) {
                // 格式化结果 转为科学计数法 或 不变
                String resultFormat = isScientificNotation(result) ? getScientificNotation(result) : result;
                String compareResultFormat = isScientificNotation(result2) ? getScientificNotation(result2) : result2;

                // 处理带符号的结果
                final BigDecimal value = processSymbolResult(new BigDecimal(resultFormat), result);
                final BigDecimal value2 = processSymbolResult(new BigDecimal(compareResultFormat), result2);

                return value.compareTo(value2);
            }
        } catch (Exception e) {
            log.error("结果值无法比较， result={}， result2={}", result, result2);
        }
        return 0;

    }

    private static boolean isNumber(String value) {
        // 结果  是否是数字                        是否是科学计数法
        return NumberUtils.isParsable(value) || isScientificNotation(value) ;
    }

    /**
     * 替换科学计数法写法
     */
    private static String getScientificNotation(String result) {
        if (StringUtils.isBlank(result)) {
            return result;
        }

        String v = result;

        if (StringUtils.contains(result, "×10^")) {
            v = result.replace("×10^", "E");
        }
        if (StringUtils.contains(result, "*10^")) {
            v = result.replace("*10^", "E");
        }
        v = v.replace("*", "");
        return v;
    }


    /**
     * 处理带符号的结果
     */
    private static BigDecimal processSymbolResult(BigDecimal decimal, String value) {

        value = getScientificNotation(value);

        if (StringUtils.isBlank(value)) {
            return decimal;
        }

        final BigDecimal incNumber = RESULT_SYMBOL.get(String.valueOf(value.charAt(0)));
        if (Objects.isNull(incNumber)) {
            return decimal;
        }

        // 从第一位开始获取，把符号去掉
        final String numberResult = value.substring(NumberUtils.INTEGER_ONE);
        if (!NumberUtils.isParsable(numberResult) && !isScientificNotation(numberResult)) {
            return decimal;
        }

        // 去掉符号
        return new BigDecimal(numberResult).add(incNumber);
    }


    /**
     * 判断结果值是否是科学计数法 目前兼容 1.5×10^3 1.5*10^3 1.5*e3 1.5*E3 1.5e3 1.5E3 类型的科学计数法
     */
    public static boolean isScientificNotation(String str) {
        // 如果是一个可以被解析的数字，那么就不是一个科学计数法
        if (NumberUtils.isParsable(str)) {
            return false;
        }
        // 正则表达式用于匹配科学计数法的格式，包括带有幂符号的形式
        final String regex =
                "^([-+]?\\d*\\.?\\d+([eE][-+]?\\d+)?|[-+]?(\\d*\\.?\\d+|\\d+)\\s*[*×]\\s*10[\\^]\\s*[-+]?\\d+|\\d*\\.?\\d+\\s*\\*?×?\\s*[eE][-+]?\\d+)$";
        return Pattern.compile(regex).matcher(str).matches();
    }


    public static void main(String[] args) {
        final int compare = ScientificNotationUtil.compare("2*10^3", "3*10^3");
        System.out.println(compare);
    }
}
