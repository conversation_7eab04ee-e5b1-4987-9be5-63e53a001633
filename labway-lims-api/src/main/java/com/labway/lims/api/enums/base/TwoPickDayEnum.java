package com.labway.lims.api.enums.base;

import lombok.Getter;

/**
 * 二次分拣日期枚举
 */
@Getter
public enum TwoPickDayEnum {

    /**
     * 当天
     */
    CURRENT("当天", "0"),
    /**
     * 周一
     */
    MONDAY("周一", "1"),
    /**
     * 周二
     */
    TUESDAY("周二", "2"),
    /**
     * 周三
     */
    WEDNESDAY("周三", "3"),
    /**
     * 周四
     */
    THURSDAY("周四", "4"),
    /**
     * 周五
     */
    FRIDAY("周五", "5"),
    /**
     * 周六
     */
    SATURDAY("周六", "6"),
    /**
     * 周日
     */
    SUNDAY("周日", "7"),
    ;


    private final String desc;

    private final String code;

    TwoPickDayEnum(String desc, String code) {
        this.desc = desc;
        this.code = code;
    }


    public static TwoPickDayEnum selectByCode(String code) {
        for (TwoPickDayEnum item : TwoPickDayEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
