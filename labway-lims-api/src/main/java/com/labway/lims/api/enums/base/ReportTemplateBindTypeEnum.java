package com.labway.lims.api.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 报告单模板绑定 绑定类型
 *
 * <AUTHOR>
 * @since 2023/4/14 10:30
 */
@Getter
@AllArgsConstructor
public enum ReportTemplateBindTypeEnum {

    PROFESSIONAL_GROUP(1, "专业小组模版"),

    HSP_ORG(2, "送检机构模版"),

    TEST_ITEM(3, "检验项目模版"),

    ;

    private final int code;
    private final String desc;


    public static ReportTemplateBindTypeEnum getByCode(int code) {
        for (ReportTemplateBindTypeEnum value : ReportTemplateBindTypeEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
