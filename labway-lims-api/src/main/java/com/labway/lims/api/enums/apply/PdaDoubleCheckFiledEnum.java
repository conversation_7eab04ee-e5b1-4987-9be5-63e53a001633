package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PdaDoubleCheckFiledEnum {
    /**
     * 送检机构
     */
    HSP_ORG("hspOrgId", "送检机构", 0),
    /**
     * 就诊类型
     */
    APPLY_TYPE("applyTypeCode", "就诊类型", 1),
    /**
     * 患者名称
     */
    PATIENT_NAME("patientName", "姓名", 2),
    /**
     * 患者性别
     */
    PATIENT_SEX("patientSex", "性别", 3),
    /**
     * 患者生日
     */
    PATIENT_BIRTHDAY("patientBirthday", "出生日期", 4),
    /**
     * 患者年龄
     */
    PATIENT_AGE("patientAge", "年龄", 5),
    /**
     * 急诊状态  1加急 0不加急
     *
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    URGENT("urgent", "急诊状态", 6),
    /**
     * 样本数量
     */
    SAMPLE_COUNT("sampleCount", "样本个数", 7),
    /**
     * 样本性状
     */
    SAMPLE_PROPERTY_CODE("sampleProperty", "样本性状", 8),
    /**
     * 申请时间
     */
    APPLY_DATE("applyDate", "申请时间", 9),
    /**
     * 采样时间
     */
    SAMPLING_DATE("samplingDate", "采样时间", 10),
    /**
     * 门诊|住院号
     */
    PATIENT_VISIT_CARD("patientVisitCard", "门诊/住院号", 11),
    /**
     * 科室
     */
    DEPT("dept", "科室", 12),
    /**
     * 床号
     */
    PATIENT_BED("patientBed", "床号", 13),
    /**
     * 送检医生
     */
    SEND_DOCTOR("sendDoctorName", "送检医生", 14),
    /**
     * 临床诊断
     */
    CLINICAL_DIAGNOSIS("diagnosis", "临床诊断", 15),
    /**
     * 备注
     */
    REMARK("remark", "备注", 16),
    /**
     * 手机号
     */
    PATIENT_MOBILE("patientMobile", "电话", 17),

    /**
     * 身份证
     */
    PATIENT_CARD("patientCard", "身份证", 18),

    /**
     * 外部条码号
     */
    OUT_BARCODE("outBarcode", "外部条码号", 19),
    ;


    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 顺序
     */
    private Integer sort;


    public static PdaDoubleCheckFiledEnum getByCode(String code) {
        for (PdaDoubleCheckFiledEnum value : PdaDoubleCheckFiledEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
