package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

import java.util.Objects;

/**
 * 打印状态
 * 
 * <AUTHOR>
 * @since 2023/4/19 13:22
 */
@Getter
@AllArgsConstructor
public enum PrintStatusEnum {

    UNPRINTED(0, "未打印"),

    PRINTED(1, "已打印"),

    ABOLISHED(9, "已作废"),

    ALL(-1, "全部"),

    ;

    private final Integer code;

    private final String desc;

    @NonNull
    public static PrintStatusEnum getByCode(Integer code) {
        for (PrintStatusEnum statusEnum : PrintStatusEnum.values()) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum;
            }
        }
        return UNPRINTED;
    }

}
