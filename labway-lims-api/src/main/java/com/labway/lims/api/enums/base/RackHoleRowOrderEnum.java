package com.labway.lims.api.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 试管架 孔位 行顺序
 * 
 * <AUTHOR>
 * @since 2023/4/14 10:26
 */
@Getter
@AllArgsConstructor
public enum RackHoleRowOrderEnum {

    UP_TO_DOWN(1, "先上后下"),

    DOWN_TO_UP(0, "先下后上"),

    ;

    private final Integer code;
    private final String desc;

    public static RackHoleRowOrderEnum getByCode(Integer code) {
        for (RackHoleRowOrderEnum value : values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return RackHoleRowOrderEnum.UP_TO_DOWN;
    }

}
