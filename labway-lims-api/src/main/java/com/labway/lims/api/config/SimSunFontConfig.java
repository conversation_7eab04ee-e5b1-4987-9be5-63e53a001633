package com.labway.lims.api.config;

import cn.hutool.core.io.FileUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Objects;

@Slf4j
@Component
@Getter
public class SimSunFontConfig implements InitializingBean {

    private File fontFile;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 如果操作系统存在则使用操作系统的
        if (SystemUtils.IS_OS_WINDOWS) {
            final String windir = StringUtils.defaultIfBlank(System.getenv("windir"), System.getProperty("windir"));
            final String disk = StringUtils.isBlank(windir) ? "c" : windir.split(":")[0];
            final String systemSimSun = disk + ":/windows/fonts/simsun.ttc";
            if (Files.exists(Paths.get(systemSimSun))) {
                fontFile = new File(systemSimSun);
            }
        } else if (SystemUtils.IS_OS_MAC) {
            final String systemSimSun = "/System/Library/Fonts/simsun.ttc";
            if (Files.exists(Paths.get(systemSimSun))) {
                fontFile = new File(systemSimSun);
            }
        }

        if (Objects.isNull(fontFile) || !fontFile.exists()) {
            fontFile = FileUtil.createTempFile(".ttc", true);
            ClassPathResource resource = new ClassPathResource("fonts/simsun.ttc");
            if (!resource.exists()) {
                throw new IllegalArgumentException("字体文件 [fonts/simsun.ttc] 不存在");
            }


            try (InputStream is = resource.getInputStream();
                 OutputStream os = Files.newOutputStream(fontFile.toPath())) {
                IOUtils.copy(is, os);
            }

        }


        log.info("初始化字体文件成功 [{}]", fontFile);


    }


}
