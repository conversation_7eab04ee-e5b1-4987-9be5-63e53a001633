package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SampleSourceEnum {
    INPUT("INPUT", "手动录入"),
    SIGN("SIGN", "签收"),
    PDA("PDA", "PDA录入"),
    PRESET("PRESET", "预置条码录入"),
    OTHER("OTHER", "其他");

    private final String code;
    private final String desc;


    // 根据code获取枚举
    public static SampleSourceEnum getByCode(String code) {
        for (SampleSourceEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }

        return OTHER;
    }


}
