package com.labway.lims.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * PDF 报告模版
 *
 * <AUTHOR>
 * @since 2023/5/25 17:25
 */
@Getter
@AllArgsConstructor
public enum PdfTemplateTypeEnum {

    SPECIALTY_STANDARD("SPECIALTY_STANDARD", "特检结果标准版"),

    SPECIALTY_TYPE_ONE("SPECIALTY_TYPE_ONE", "特检结果特版1"),

    SPECIALTY_TYPE_THREE("SPECIALTY_TYPE_THREE", "特检结果特版3"),

    FINANCIAL_TEST_ITEM_INCOME_SUMMARY("FINANCIAL_TEST_ITEM_INCOME_SUMMARY", "财务管理销售收入-汇总数据"),
    INSPECTION_RESULTS("INSPECTION_RESULT", "检验结果查询"),

    FINANCIAL_TEST_ITEM_INCOME_DETAIL("FINANCIAL_TEST_ITEM_INCOME_DETAIL", "财务管理销售收入-明细数据"),

    FINANCIAL_HSP_ORG_SEND_DOCTOR_STATISTICS("FINANCIAL_HSP_ORG_SEND_DOCTOR_STATISTICS", "财务管理机构送检医生统计"),

    REPORT_MERGE_PRINT_INFO("REPORT_MERGE_PRINT_INFO", "报告单合并打印"),

    WL_RK("WL_RK", "物料入库单打印模版"),

    WL_PD("WL_PD", "物料盘点单打印模版"),

    BY_PLATFORM_STATISTICS("BY_PLATFORM_STATISTICS", "分平台统计模版"),

    OUT_ORG_STATISTICS("OUT_ORG_STATISTICS", "外送机构统计模版"),

    PRINT_MICROBIOLOGY_ATTACH("PRINT_MICROBIOLOGY_ATTACH", "微生物二次分拣附件打印模版"),

    PRINT_INFECTION_FLOW("PRINT_INFECTION_FLOW", "院感二次分拣流程单打印模版"),

    GROUP_MATERIAL_PLAN("GROUP_MATERIAL_PLAN", "专业组物料计划打印模板"),

    PRINT_PASS_ON_SHEET("PRINT_PASS_ON_SHEET", "交接单打印模板"),

    LATE_REPORT_REQUEST("LATE-REPORT-REQUEST", "报告单延迟申请模板"),

    ORIGINAL_RESULT("ORIGINAL-RESULT", "酶标原始结果模板"),

    ORIGINAL_RESULT_IMMUNE("ORIGINAL-RESULT-IMMUNE", "酶标原始结果免疫模板"),

    WL_SL("WL_SL", "物料申领模板"),

    OUTSOURCING_LIST("OUTSOURCING_LIST","预览外送申请单"),
    PATHOLOGY_REPORT_DETAIL("PATHOLOGY_REPORT_DETAIL", "病理报告发送清单"),

    OUTSOURCING_REMINING_NOTIFY("OUTSOURCING_REMINING_NOTIFY", "样本异常登记重采通知"),

    ;

    private final String code;

    private final String desc;
}
