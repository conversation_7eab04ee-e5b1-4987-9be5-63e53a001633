package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 逻辑试管架位置
 */
@Getter
@AllArgsConstructor
public enum RackLogicPositionEnum {

    /**
     * 未知 默认值
     */
    UNKNOWN(0, "未知"),
    /**
     * 一次分拣中
     */
    ONE_PICKING(1, "一次分拣中"),
    /**
     * 一次分拣完成 | 一次交接中
     */
    ONE_PICKED(2, "一次分拣后交接中"),
    /**
     * 一次交接完成 | 分血中
     */
    SPLITTING_BLOOD(3, "分血中"),
    /**
     * 分血完成 ｜ 待交接 | 分血后交接中
     */
    SPLIT_BLOOD(4, "分血后交接中"),
    /**
     * 一次交接完成 | 分血交接完成 | 待二次分拣 | 二次分拣中
     */
    TWO_PICKING(5, "二次分拣中"),
    /**
     * 组间交接中
     */
    GROUP_PICKING(7, "组间交接中"),
    /**
     * 结束 表示不会再使用此逻辑试管架
     */
    END(99, "结束"),

    ;

    /**
     * code
     */
    private final int code;

    private final String desc;

    public static RackLogicPositionEnum getByCode(int code) {
        for (RackLogicPositionEnum value : RackLogicPositionEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
