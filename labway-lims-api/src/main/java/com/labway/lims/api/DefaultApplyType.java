package com.labway.lims.api;

import org.apache.commons.lang3.math.NumberUtils;

public interface DefaultApplyType {

    /**
     * 全部送检类型编码
     */
    String ALL_APPLY_TYPE_CODE = String.valueOf(NumberUtils.INTEGER_ZERO);

    /**
     * 全部送检类型名称
     */
    String ALL_APPLY_TYPE_NAME = "全部";

    /**
     * 其他不匹配的默认 /
     */
    String OTHER_APPLY_TYPE_NAME = "/";

    default boolean isDefaultApplyTypeByName(String applyTypeName) {
        return ALL_APPLY_TYPE_NAME.equals(applyTypeName);
    }

    default boolean isDefaultApplyTypeByCode(String applyTypeCode) {
        return ALL_APPLY_TYPE_CODE.equals(applyTypeCode);
    }

    default boolean isOtherApplyType(String applyType) {
        return OTHER_APPLY_TYPE_NAME.equals(applyType);
    }
}
