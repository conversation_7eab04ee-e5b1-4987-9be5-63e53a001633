package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * StopStatusEnum
 * 是否终止状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/9 16:01
 */
@Getter
@AllArgsConstructor
public enum StopStatusEnum {

    /**
     * 正常或终止检验（收费）
     */
    NORMAL(101, "正常或终止检验（收费）"),
    /**
     * 禁用或终止检验（不收费）
     */
    DISABLE_STOP(102, "禁用或终止检验（不收费）"),

    ;

    private final int code;
    private final String desc;

}
