package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <p>
 * StopStatusEnum
 * 是否终止状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/9 16:01
 */
@Getter
@AllArgsConstructor
public enum StopStatusEnum {

    /**
     * 正常或终止检验（收费）
     */
    NORMAL(101, "正常或终止检验（收费）"),
    /**
     * 禁用或终止检验（不收费）
     */
    DISABLE_STOP(102, "禁用或终止检验（不收费）"),


    NO_STOP_TEST(0, "正常"),
    STOP_TEST_CHARGE(1, "终止检验（收费）"),
    STOP_TEST_FREE(2, "终止检验（不收费）"),
    DISABLE(3, "禁用"),

    ;

    private final int code;
    private final String desc;

    public static StopStatusEnum getByCode(Integer code) {
        for (StopStatusEnum statusEnum : values()) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum;
            }
        }
        return StopStatusEnum.NO_STOP_TEST;
    }
}
