package com.labway.lims.api.enums.routine;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 样本危机值 状态
 *
 * <AUTHOR>
 * @since 2023/4/11 10:08
 */
@Getter
@AllArgsConstructor
public enum SampleCriticalResultStatusEnum {

    UNPROCESSED(0, "未处理"),

    UNDER_REVIEW(1, "复查中"),

    REVIEW(3, "已复查"),

    PROCESSED(2, "已处理"),

    ;

    private final int code;

    private final String msg;

    public static SampleCriticalResultStatusEnum getStatusByCode(int code) {
        for (SampleCriticalResultStatusEnum statusEnum : SampleCriticalResultStatusEnum.values()) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum;
            }
        }
        return UNPROCESSED;
    }

}
