package com.labway.lims.api;

import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;

/**
 * 样本号相关工具类
 *
 * <AUTHOR>
 * @since 2023/7/21 10:08
 */
public class SampleNoUtils {

    /**
     * 样本号加一
     */
    public static String addOneToLastNumber(String inputStr) {
        if (StringUtils.isBlank(inputStr)) {
            return StringUtils.EMPTY;
        }

        StringBuilder result = new StringBuilder();
        int i = inputStr.length() - 1;

        while (i >= 0 && Character.isDigit(inputStr.charAt(i))) {
            result.insert(0, inputStr.charAt(i));
            i--;
        }

        if (result.length() > 0) {
            int length = result.length();
            // 处理连续的 '0'
            while (result.length() > 1 && result.charAt(0) == '0') {
                result.deleteCharAt(0);
            }

            BigInteger lastNumber = new BigInteger(result.toString());
            String prefix = inputStr.substring(0, i + 1);

            // 增加数字 1
            lastNumber = lastNumber.add(BigInteger.ONE);

            // 重新处理 '0' 后的数字，并补齐前导 '0'
            String incrementedStr = lastNumber.toString();
            int leadingZeroes = length - incrementedStr.length();
            while (leadingZeroes > 0) {
                incrementedStr = '0' + incrementedStr;
                leadingZeroes--;
            }

            return prefix + incrementedStr;
        } else {
            return inputStr;
        }
    }


}
