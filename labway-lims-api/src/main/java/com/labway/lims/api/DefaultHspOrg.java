package com.labway.lims.api;

import org.apache.commons.lang3.math.NumberUtils;

import java.util.Objects;

/**
 * 通用送检机构
 */
public interface DefaultHspOrg {
    Long DEFAULT_HSP_ORG_ID = NumberUtils.LONG_ZERO;
    String DEFAULT_HSP_ORG_CODE = String.valueOf(NumberUtils.LONG_ZERO);
    String DEFAULT_HSP_ORG_NAME = "通用机构";

    default boolean isDefaultHspOrg(Long hspOrgId){
        return Objects.equals(hspOrgId, DEFAULT_HSP_ORG_ID);
    }
}
