package com.labway.lims.api;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

/**
 * <pre>
 * LabwayDateUtil
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/6/5 16:07
 */
public final class LabwayDateUtil {

    private static Integer[] DEFAULT_YEARS = new Integer[] {1900, 1970};

    public static boolean isDefaultDbDate(Date date) {
        if (Objects.isNull(date)) {
            return false;
        }
        int year = DateUtil.beginOfDay(date).getField(DateField.YEAR);
        return Arrays.asList(DEFAULT_YEARS).contains(year);
    }


    /**
     * 判断是否是DB默认时间，如果是则返回Null 否则返回自身
     * @param date
     * @return
     */
    public static Date getNonDefaultDbDate(Date date) {
        return isDefaultDbDate(date) ? null : date;
    }

    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

}
