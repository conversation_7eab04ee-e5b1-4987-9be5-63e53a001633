package com.labway.lims.api.field;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 比较字段
 * 
 * <AUTHOR>
 * @since 2023/5/19 15:45
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Compare {
    /**
     * 字段名称
     */
    String value();

    /**
     * 内容转换
     */
    CompareContent[] content() default {};
}
