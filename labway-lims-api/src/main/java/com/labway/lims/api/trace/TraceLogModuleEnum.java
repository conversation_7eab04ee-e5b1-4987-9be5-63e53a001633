package com.labway.lims.api.trace;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 记录日志模块
 *
 * <AUTHOR>
 * @since 2023/6/19 13:21
 */
@Getter
@AllArgsConstructor
public enum TraceLogModuleEnum {

    LOGIN("登录"),

    BASE_DICT("基础字典表->%s"),

    INSTRUMENT_GROUP_LOG("专业小组维护"),

    RACK_LOG("试管架维护"),

    HSP_ORGANIZATION_MSG_LOG("送检机构提示信息维护"),

    PHYSICAL_GROUP_LOG("体检单位维护"),

    PACKAGE_LOG("套餐维护"),

    GERM_GENUS_LOG("细菌菌属维护"),

    GERM_LOG("细菌维护"),

    MEDICINE_LOG("药物维护"),

    ARCHIVE_STORE_LOG("归档库和冰箱维护"),

    HSP_ORGANIZATION_FILED_LOG("双输对照内容维护"),

    GROUP_MATERIAL("专业组物料维护"),

    ITEM_PRICE_BASE_PACKAGE("项目价格基准包维护"),

    CUSTOMER_SPECIAL_PRICE_ITEM_MAINTENANCE("客户特价项目维护"),

    CUSTOMER_SPECIAL_MAINTENANCE("客户阶梯折扣价维护"),

    SEND_ITEM_PRICE("外送价格维护"),

    HSP_ORG("送检机构维护"),

    PROFESSIONAL_GROUP("专业组维护"),

    INSTRUMENT("仪器维护"),

    TEST_ITEM("检验项目维护"),

    REPORT_TEMPLATE("报告单模板维护"),

    INSTRUMENT_REPORT_ITEM("仪器报告项目维护"),

    INSTRUMENT_REPORT_RESULT_VALUE_TIP("仪器项目结果值提醒维护"),

    INSTRUMENT_REPORT_ITEM_REFERENCE("仪器报告项目参考值维护"),

    INSTRUMENT_REPORT_ITEM_COMMON_PHRASE("仪器报告项目常用短语维护"),

    INSTRUMENT_REPORT_ITEM_RESULT_EXCHANGE("仪器报告项目结果值转换维护"),

    HSP_DEPT_DOCTOR("送检机构科室与医生维护"),

    MEDICINE_GERM_RELATION("药物参考范围维护"),

    INSTRUMENT_CHANNEL("微生物仪器通道号维护"),

    HSP_ORG_DISCOUNT("客户折扣维护"),

    MATERIAL_SYNC("物料信息同步"),

    REPORT_MATERIAL_RELATION("报告物料关联"),

    AUTO_AUDIT("自动审核"),

    ;

    private final String desc;
}
