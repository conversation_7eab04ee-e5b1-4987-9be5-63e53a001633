package com.labway.lims.api.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 试管架 孔位 行、列 优先
 * 
 * <AUTHOR>
 * @since 2023/4/14 10:35
 */
@Getter
@AllArgsConstructor
public enum RackHolePriorityOrderEnum {

    ROW_PRIORITY(1, "行优先"),

    COLUMN_PRIORITY(0, "列优先"),

    ;

    private final Integer code;
    private final String desc;

    public static RackHolePriorityOrderEnum getByCode(Integer code) {
        for (RackHolePriorityOrderEnum value : values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return RackHolePriorityOrderEnum.COLUMN_PRIORITY;
    }
}
