package com.labway.lims.api.field;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 比较字段
 * 
 * <AUTHOR>
 * @since 2023/6/19 13:44
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CompareContent {

    /**
     * 值
     */
    String value();

    /**
     * 值描述
     */
    String valueDesc();
}
