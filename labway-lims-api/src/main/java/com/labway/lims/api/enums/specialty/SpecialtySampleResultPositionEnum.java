//package com.labway.lims.api.enums.specialty;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//import lombok.NonNull;
//
//import java.util.Objects;
//
///**
// * 特检样本结果 位置
// *
// * <AUTHOR>
// * @since 2023/4/24 17:01
// */
//@Getter
//@AllArgsConstructor
//public enum SpecialtySampleResultPositionEnum {
//
//    TYPE_ONE_FIRST_ROW_LEFT("TYPE_ONE_FIRST_ROW_LEFT", "类型一:第一行左侧图片域",
//        SpecialtySampleResultTemplateTypeEnum.SPECIALTY_STANDARD),
//
//    TYPE_ONE_FIRST_ROW_RIGHT("TYPE_ONE_FIRST_ROW_RIGHT", "类型一:第一行右侧文本域",
//        SpecialtySampleResultTemplateTypeEnum.SPECIALTY_STANDARD),
//
//    TYPE_ONE_SECOND_ROW_CENTER("TYPE_ONE_SECOND_ROW_CENTER", "类型一:第二行结论文本域",
//        SpecialtySampleResultTemplateTypeEnum.SPECIALTY_STANDARD),
//
//    TYPE_ONE_THIRD_ROW_CENTER("TYPE_ONE_THIRD_ROW_CENTER", "类型一:第三行文本域",
//        SpecialtySampleResultTemplateTypeEnum.SPECIALTY_STANDARD),
//
//    TYPE_ONE_FOURTH_ROW_CENTER("TYPE_ONE_FOURTH_ROW_CENTER", "类型一:第四行图片文本域",
//        SpecialtySampleResultTemplateTypeEnum.SPECIALTY_STANDARD),
//
//    TYPE_TWO_FIRST_ROW_CENTER("TYPE_TWO_FIRST_ROW_CENTER", "类型二:第一行图片域",
//        SpecialtySampleResultTemplateTypeEnum.SPECIALTY_TYPE_ONE),
//
//    TYPE_TWO_SECOND_ROW_CENTER("TYPE_TWO_SECOND_ROW_CENTER", "类型二:第二行文本域",
//        SpecialtySampleResultTemplateTypeEnum.SPECIALTY_TYPE_ONE),
//
//    DEFAULT("DEFAULT", "未知", null),
//
//    ;
//
//    private final String code;
//
//    private final String desc;
//
//    private final SpecialtySampleResultTemplateTypeEnum resultTemplateTypeEnum;
//
//    @NonNull
//    public static SpecialtySampleResultPositionEnum getByCode(String code) {
//        for (SpecialtySampleResultPositionEnum positionEnum : SpecialtySampleResultPositionEnum.values()) {
//            if (Objects.equals(positionEnum.getCode(), code)) {
//                return positionEnum;
//            }
//        }
//        return DEFAULT;
//    }
//
//}
