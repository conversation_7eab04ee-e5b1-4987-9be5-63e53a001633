package com.labway.lims.api.enums.base;

import lombok.Getter;

/**
 * 套餐类型
 */
@Getter
public enum PackageTypeEnum {

    /**
     * 体检类型
     */
    PHYSICAL("体检", "0"),
    /**
     * 样本录入
     */
    SAMPLE_INPUT("样本录入", "1"),
    ;


    private final String desc;

    private final String code;

    PackageTypeEnum(String desc, String code) {
        this.desc = desc;
        this.code = code;
    }


    public static PackageTypeEnum selectByCode(String code) {
        for (PackageTypeEnum item : PackageTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
