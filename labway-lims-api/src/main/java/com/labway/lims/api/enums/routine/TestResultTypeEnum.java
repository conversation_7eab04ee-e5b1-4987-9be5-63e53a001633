package com.labway.lims.api.enums.routine;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2023/4/2 18:30
 */
@Getter
@AllArgsConstructor
public enum TestResultTypeEnum {
    /**
     * 数值
     */
    NUMBER("0002", "数值"),
    /**
     * 阴阳性
     */
    YIN_YANG("0001", "阴阳性"),
    /**
     * 图文
     */
    IMAGE("0003", "图文"),
    /**
     * 字符
     */
    STR("0004", "字符");

    private final String code;

    private final String str;


    public static TestResultTypeEnum isYinYangOrStr(String code) {
        if (TestResultTypeEnum.STR.getCode().equals(code)) {
            return TestResultTypeEnum.STR;
        } else if (TestResultTypeEnum.YIN_YANG.getCode().equals(code)) {
            return TestResultTypeEnum.YIN_YANG;
        }

        return null;
    }


    public static TestResultTypeEnum getEnumByCode(String code) {
        if (TestResultTypeEnum.STR.getCode().equals(code)) {
            return TestResultTypeEnum.STR;
        }

        return TestResultTypeEnum.YIN_YANG;
    }


    public static TestResultTypeEnum getEnumByName(String name) {
        return  Arrays.stream(values()).filter(e -> e.getStr().equals(name)).findFirst().orElse(null);
    }
}
