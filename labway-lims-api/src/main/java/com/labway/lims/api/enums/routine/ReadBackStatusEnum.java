package com.labway.lims.api.enums.routine;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 回读状态枚举
 */
@Getter
public enum ReadBackStatusEnum {
    /**
     * 否
     */
    NO(0, "否"),
    /**
     * 是
     */
    YES(1, "是"),
    /**
     * 未处理
     */
    UNHANDLE(-1, "");

    ReadBackStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    private final Integer code;
    private final String value;

    public static ReadBackStatusEnum selectByCode(Integer code) {
        return Arrays.stream(values())
                .filter(i -> Objects.equals(i.getCode(), code)).findFirst().orElse(UNHANDLE);
    }

}
