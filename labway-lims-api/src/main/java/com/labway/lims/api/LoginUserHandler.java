package com.labway.lims.api;

import com.labway.lims.api.exception.AuthenticationException;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.Objects;

/**
 * 当前登录用户
 */
@Getter
@Setter
public class LoginUserHandler {

    private static final ThreadLocal<User> THREAD_LOCAL = new ThreadLocal<>();

    private LoginUserHandler() {

    }

    @Nonnull
    public static User get() {
        final User unsafe = getUnsafe();
        if (Objects.isNull(unsafe)) {
            throw new AuthenticationException("Authorization 错误或已失效");
        }
        return unsafe;
    }

    public static void set(User user) {
        THREAD_LOCAL.set(user);
    }

    @Nullable
    public static User getUnsafe() {
        return THREAD_LOCAL.get();
    }


    public static void remove() {
        THREAD_LOCAL.remove();
    }

    @Getter
    @Setter
    public static final class User implements Serializable {


        /**
         * 角色ID
         */
        private Long userId;

        /**
         * 用户名称
         */
        private String username;

        /**
         * 用户昵称
         */
        private String nickname;

        /**
         * token
         */
        private String token;

        /**
         * 专业组
         */
        private Long groupId;

        /**
         * 专业组编码
         */
        private String groupCode;
        /**
         * 专业组
         */
        private String groupName;

        /**
         * 角色 ID
         */
        private Long roleId;

        /**
         * 角色
         */
        private String roleName;

        /**
         * 机构 ID
         */
        private Long orgId;

        /**
         * 机构编码
         */
        private String orgCode;

        /**
         * 机构名称
         */
        private String orgName;

    }

}
