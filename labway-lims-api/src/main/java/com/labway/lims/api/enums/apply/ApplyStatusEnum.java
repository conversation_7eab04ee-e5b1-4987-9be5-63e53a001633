package com.labway.lims.api.enums.apply;

import java.util.List;

/**
 * 申请单 复核枚举
 *
 * <AUTHOR>
 */
public enum ApplyStatusEnum {

    /**
     * 待补录
     */
    PENDING_RECORDING(0,"待补录"),
    /**
     * 双输复核
     */
    WAIT_DOUBLE_CHECK(1, "待双输复核"),
    /**
     * 待复核
     */
    WAIT_CHECK(2, "待复核"),
    /**
     * 已复核
     */
    CHECK(3, "已复核"),
    /**
     * 已双输复核
     */
    DOUBLE_CHECK(4, "已双输复核"),
    ;

    private final String desc;
    private final Integer code;


    ApplyStatusEnum(Integer code, String desc) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }


    public Integer getCode() {
        return code;
    }


    /**
     * 是否复核 1是 0否
     */
    public static boolean isCheck(Integer status){
        return List.of(ApplyStatusEnum.CHECK.getCode(),ApplyStatusEnum.DOUBLE_CHECK.getCode()).contains(status);
    }

    /**
     * 是否未复核 1是 0否
     */
    public static boolean isUnCheck(Integer status){
        return List.of(ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode(),ApplyStatusEnum.WAIT_CHECK.getCode()).contains(status);
    }
}
