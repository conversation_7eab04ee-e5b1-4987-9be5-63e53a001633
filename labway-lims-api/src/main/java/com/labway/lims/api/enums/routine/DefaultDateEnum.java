package com.labway.lims.api.enums.routine;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;


/**
 * <AUTHOR>
 * @since 2023/4/28 13:33
 */
@Getter
@AllArgsConstructor
public enum DefaultDateEnum {

    /**
     * 默认时间
     */
    DEFAULT_DATE(Date.from(LocalDateTime.of(1970, 1, 1, 0, 0, 0)
            .atZone(ZoneId.systemDefault()).toInstant()));

    private final Date date;
}
