package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 样本审核方式
 *
 * <AUTHOR> on 2025/7/30.
 */
@Getter
@AllArgsConstructor
public enum SampleAuditMethodEnum {

	AUTO(1,"自动审核"),
	MANUAL(0,"手动审核"),
	OTHER(-1, "其他");

	private final Integer code;
	private final String desc;

	public static boolean isAuto(Integer code) {
		if (Objects.isNull(code)) {
			return false;
		}
		return code.equals(AUTO.code);
	}
}
