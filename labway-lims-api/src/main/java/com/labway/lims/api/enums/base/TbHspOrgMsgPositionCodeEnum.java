package com.labway.lims.api.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 机构提示信息 提示位置
 * 
 * <AUTHOR>
 * @since 2023/4/14 10:30
 */
@Getter
@AllArgsConstructor
public enum TbHspOrgMsgPositionCodeEnum {

    SAMPLE_INFO_INPUT("样本信息录入"),

    SAMPLE_INFO_RETRY("样本信息补录"),

    ROUTINE("常规检验"),

    MICROBIOLOGY("微生物检验"),

    INFECTION("院感检验"),

    GENETICS("遗传检验"),

    SPECIALTY("特检"),

    OUTSOURCING("外送检验"),

    REPORT_PRINTING("报告单打印"),

    ;

    private final String desc;
}
