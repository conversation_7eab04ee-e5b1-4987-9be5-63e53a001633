package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 条码环节
 */
@AllArgsConstructor
@Getter
public enum BarcodeFlowEnum {

    LOGISTICS_SAMPLE("物流取样"),
    SAMPLE_INFO_SUPPLEMENT("样本信息补录"),
    CHECK("样本复核"),
    DOUBLE_CHECK("样本复核"),
    CANCEL_CHECK("取消复核"),
    ONE_PICK("一次分拣"),
    ONE_PICK_UNLOAD("一次分拣下架"),
    ONE_PICK_HANDOVER("一次分拣交接"),
    CANCEL_ONE_PICK("取消一次分拣"),
    SAMPLE_ROLLBACK("样本回退"),
    TWO_PICK("二次分拣"),
    CANCEL_TWO_PICK("取消二次分拣"),
    RESULT_REMARK("修改结果备注"),
    CHANGE_SAMPLE_NO("修改样本号"),
    SAMPLE_REMARK("修改样本备注"),
    SPLIT_BLOOD("分血"),
    SPLIT_BLOOD_HANDOVER("分血后交接"),
    IT8000SEEN("Roche扫描"),
    IT8000SEEN_IMAGE("Roche拍照"),
    MACHINE_SEEN("仪器扫描"),
    IT8000PICK("Roche分拣"),
    CREATE_BARCODE("条码新增"),
    BARCODE_RECEIVE("接收样本"),
    TEST_ITEM_ADD("添加检验项目"),
    TEST_ITEM_DELETE("删除检验项目"),
    TEST_ITEM_UPDATE("修改检验项目"),
    REPORT_ITEM_ADD("添加报告项目"),
    REPORT_ITEM_DELETE("删除报告项目"),
    RESULT_UPDATE("结果值修改"),
    ADD_MEDICINE("添加药物"),
    DELETE_RESULT("删除微生物结果"),
    ADD_GERM("添加细菌"),
    DELETE_GERM("删除细菌"),
    DELETE_MEDICINE("删除药物"),
    UPDATE_MEDICINE("修改药物"),
    GROUP_TRANSFORM("组间交接"),
    BARCODE_AUDIT("条码审核"),
    BARCODE_CANCEL_AUDIT("取消审核"),
    APPLY_INFO_UPDATE("样本信息修改"),
    BARCODE_CANCEL_RECEIVE("撤销接收"),
    UPDATE_SAMPLE_NO("修改样本号"),
    BARCODE_DELETE("删除样本"),
    APPLY_CANCEL_SIGN("申请单撤销签收"),
    NOTIFY_HIS_RESULT("通知HIS结果"),
    HIS_SIGN("HIS签收"),
    RETEST_RESULT("结果复查"),
    COMPLETE_RETEST_RESULT("完成复查"),
    CANCEL_RETEST_RESULT("取消复查"),
    PRINT_REPORT("报告单打印"),
    PREVIEW_REPORT("报告单预览"),
    PRINT_BARCODE("补打条码"),
    PRINTER_REPORT("自主报告打印机"),
    ARCHIVE_SAMPLE("样本归档"),
    EXTRACT_ARCHIVE_SAMPLE("样本归档-提取样本"),
    ONE_CHECK("一审"),
    CANCEL_ONE_CHECK("取消一审"),
    TWO_CHECK("二审"),
    CANCEL_TWO_CHECK("取消二审"),
    SAMPLE_CHECK("审核"),
    CANCEL_SAMPLE_CHECK("取消审核"),
    AUDIT("审核"),
    UPDATE_RESULT("修改结果"),
    ADD_RESULT("新增结果"),
    DISABLE("禁用样本"),
    CANCEL_DISABLE("取消禁用样本"),
    STOP_TEST("终止检验"),
    HANDOVER("样本交接"),
    SAMPLE_IMAGE("样本图片"),
    STOP_TEST_REGAIN("终止检验恢复"),

    BC_ADD_SAMPLE_RESULT("添加结果"),
    BC_DEL_SAMPLE_RESULT("删除结果"),
    BC_ADD_SAMPLE_REMARK("添加备注"),
    BC_EDIT_SAMPLE_REMARK("修改备注"),
    BC_MARK_POSITIVE("标记阳性"),

    SAMPLE_REPORT_UPLOAD("样本上传PDF报告"),
    SAMPLE_REPORT_DELETE("样本删除PDF报告"),
    COMBINED_BILL("并单"),
    MASTER_COMBINED_BILL("并单主条码"),
    CANCEL_COMBINED_BILL("取消并单"),

    PDA_CONFIRM("PDA补录确认"),
    PDA_ROLLBACK("PDA补录回退"),
    PDA_SIGN("PDA补录样本签收"),
    PDA_CANCEL_SIGN("PDA取消签收"),

    SAMPLE_ABNORMAL_REGISTER("样本异常登记"),
    SAMPLE_ABNORMAL_HANDLE("样本异常处理"),
    SAMPLE_ABNORMAL_CONFIRM("样本异常确认"),
    SAMPLE_ABNORMAL_STOP("样本异常作废"),

    CRITICAL_RESULT_READBACK("样本危急值回读"),

    OUTSOURCING_LIST_PRINT("外送清单打印"),

    ;


    private final String desc;


}
