package com.labway.lims.api;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 所有检验机构
 */
@AllArgsConstructor
@Getter
public enum Orgs {
    TEST_LAB(1, "测试实验室"),
    SHANG_HAI_LAB(11, "上海兰卫医学检验实验室"),
    LI_WAN_LAB(19, "广州兰卫医学检验实验室"),
    DONG_GUAN_LAB(2510, "东莞兰卫医学检验实验室"),
    CHANG_ZHOU_LAB(21, "常州兰卫医学检验实验室"),
    DAN_YANG_LAB(20, "丹阳兰卫医学检验实验室"),
    CHANG_SHA_LAB(15, "长沙兰卫医学检验实验室"),
    NAN_JING_LAB(13, "南京兰卫医学检验实验室"),
    ;

    private final long orgCode;

    private final String orgName;

    public static Orgs getOrgByOrgCode(long orgCode) {
        for (Orgs e : values()) {
            if (e.getOrgCode() == orgCode) {
                return e;
            }
        }
        throw new IllegalStateException(String.format("无法识别机构编码 [%s]", orgCode));
    }

}
