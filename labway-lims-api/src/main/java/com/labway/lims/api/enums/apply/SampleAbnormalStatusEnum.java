package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

import java.util.Objects;

/**
 * 样本异常状态
 * 
 * <AUTHOR>
 * @since 2023/4/12 18:32
 */
@Getter
@AllArgsConstructor
public enum SampleAbnormalStatusEnum {
    UNPROCESSED(0, "未处理"),

    PROCESSED(1, "已处理"),

    CONFIRMED(2, "已确认"),

    ABANDONED(3, "已作废");

    private final Integer code;

    private final String desc;

    @NonNull
    public static SampleAbnormalStatusEnum getByCode(Integer code) {
        for (SampleAbnormalStatusEnum statusEnum : SampleAbnormalStatusEnum.values()) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum;
            }
        }
        return UNPROCESSED;
    }
}
