package com.labway.lims.api.dubbo;

import com.labway.lims.api.LoginUserHandler;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;

/**
 * 负责用户信息传递
 */
@Activate(group = CommonConstants.PROVIDER)
public class LoginUserProviderFilter implements Filter {
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        final Object user = invocation.getObjectAttachment(LoginUserConsumerFilter.LOGIN_USER_INFO_KEY);
        if (user instanceof LoginUserHandler.User) {
            LoginUserHandler.set((LoginUserHandler.User) user);
        }
        return invoker.invoke(invocation);
    }
}
