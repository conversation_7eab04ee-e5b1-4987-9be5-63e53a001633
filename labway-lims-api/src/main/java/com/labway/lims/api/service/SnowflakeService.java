package com.labway.lims.api.service;

import java.util.LinkedList;

/**
 * ID 生成器
 */
public interface SnowflakeService {

    /**
     * 生成一个 id
     *
     * @return id
     */
    long genId();

    /**
     * 批量生成 id
     *
     * @param count count
     * @return id
     */
    LinkedList<Long> genIds(int count);

    /**
     * 获取 ID 的生成时间
     *
     * @return 时间戳
     */
    long getGenerateDateTime(long id);
}
