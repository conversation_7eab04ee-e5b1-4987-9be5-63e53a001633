package com.labway.lims.api;

import com.labway.lims.api.exception.LimsException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.LinkedList;
import java.util.Objects;

/**
 * 生成条码号
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnClass(StringRedisTemplate.class)
public class BarcodeUtils {

    public static final int BARCODE_LENGTH = 12;

    private static final int OFFSET = 31;

    /**
     * master barcode 生成规则
     */
    private static final int MS_OFFSET = 0;

    @Resource
    private EnvDetector envDetector;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Resource
    private RedisPrefix redisPrefix;

    /**
     * 生成规则: (年-月+{@link #OFFSET}-日+{@link #OFFSET}-000001(Incr)).remove('-') 共 {@link #BARCODE_LENGTH} 位
     * <pre>
     *     例如 : 2023-03-28 月+{@link #OFFSET} 日+{@link #OFFSET}  -->   233358000001
     * </pre>
     *
     * @return 全局唯一条码
     */
    public String genBarcode() {
        return genBarcode(1).pop();
    }

    /**
     * 生成规则: (年-月+{@link #OFFSET}-日+{@link #OFFSET}-000001(Incr)).remove('-') 共 {@link #BARCODE_LENGTH} 位
     * <pre>
     *     例如 : 2023-03-28 月+{@link #OFFSET} 日+{@link #OFFSET}  -->   233358000001
     * </pre>
     *
     * @return 全局唯一条码
     */
    public LinkedList<String> genBarcode(int count) {
        return genBarcode(count, OFFSET);
    }

    /**
     * 生成主条码
     *
     * @return 全局唯一条码
     */
    public String genMasterBarcode() {
        return genBarcode(1, MS_OFFSET).pop();
    }


    /**
     * 生成规则: (年-月+{@link #OFFSET}-日+{@link #OFFSET}-000001(Incr)).remove('-') 共 {@link #BARCODE_LENGTH} 位
     * <pre>
     *     例如 : 2023-03-28 月+{@link #OFFSET} 日+{@link #OFFSET}  -->   233358000001
     * </pre>
     *
     * @return 全局唯一条码
     */
    public LinkedList<String> genBarcode(int count, int offset) {

        if (count < 1) {
            throw new IllegalArgumentException("count < 1");
        }

        final KeyInfo redisKey = getRedisKey(offset);

        final Long increment = stringRedisTemplate.opsForValue().increment(redisKey.key, count);

        if (Objects.isNull(increment)) {
            throw new LimsException("生成条码号失败");
        }

        final LinkedList<String> barcodes = new LinkedList<>();

        for (int i = count; i > 0; i--) {
            barcodes.add(String.format("%s%s%s%s", redisKey.year - 2000,
                    //
                    StringUtils.leftPad(String.valueOf(redisKey.month), 2, '0'),
                    //
                    StringUtils.leftPad(String.valueOf(redisKey.day), 2, '0'),
                    //
                    StringUtils.leftPad(String.valueOf(increment - i + 1), Math.max(increment.toString().length(), 6), '0')));
        }

        return barcodes;
    }


    private KeyInfo getRedisKey(int offset) {
        LocalDate now;

        do {

            now = LocalDate.now();

            // 如果加上一天小于 0 那么表示当前时间可能是 23:59:59.xxx
        } while (now.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
                //
                - System.currentTimeMillis() <= 0);

        final int year = now.getYear();
        final int month = now.getMonthValue() + offset;
        final int day = now.getDayOfMonth() + offset;


        final String env;
        if (envDetector.isTest() || envDetector.isDev()) {
            env = "unique";
        } else {
            env = envDetector.envName();
        }

        final String key = redisPrefix.getBasePrefix() + "Barcode:" + env + ":offset:" + offset + ":" + now;

        return new KeyInfo(year, month, day, key);
    }


    @AllArgsConstructor
    private static class KeyInfo {
        final int year;
        final int month;
        final int day;
        final String key;
    }

}
