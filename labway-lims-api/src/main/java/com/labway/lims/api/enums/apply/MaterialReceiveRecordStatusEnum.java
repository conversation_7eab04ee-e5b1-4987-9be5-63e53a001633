package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 物料领用记录 状态
 * 
 * <AUTHOR>
 * @since 2023/5/8 14:59
 */
@Getter
@AllArgsConstructor
public enum MaterialReceiveRecordStatusEnum {

    INVALID(0, "已作废"),

    RECEIVED(1, "已领用"),

    ;

    private final Integer code;

    private final String desc;

    public static MaterialReceiveRecordStatusEnum getByCode(int code) {
        for (MaterialReceiveRecordStatusEnum value : MaterialReceiveRecordStatusEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return INVALID;
    }

}
