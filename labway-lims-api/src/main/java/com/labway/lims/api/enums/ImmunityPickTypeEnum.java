package com.labway.lims.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <pre>
 * ImmunityPickTypeEnum
 * 免疫二次分拣 类型：普通，预定日期强制，项目强制
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/31 9:47
 */
@Getter
@AllArgsConstructor
public enum ImmunityPickTypeEnum {

    NORMAL("普通"),

    FORCE_BY_DATE("指定预定日期强制"),

    FORCE_BY_ITEM("指定项目强制"),

    ;

    private final String desc;

    public static ImmunityPickTypeEnum of(String type) {
        return Arrays.stream(values()).filter(e -> e.name().equalsIgnoreCase(type)).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("不支持的免疫二次分拣类型：" + type));
    }

    public static boolean isImmunityPick(String type) {
        return StringUtils.isNotBlank(type) && Arrays.stream(values()).anyMatch(e -> e.name().equalsIgnoreCase(type));
    }

}
