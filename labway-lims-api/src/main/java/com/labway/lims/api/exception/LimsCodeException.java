package com.labway.lims.api.exception;

import lombok.Getter;

import java.util.Collections;
import java.util.Objects;

/**
 * 携带自定义code异常
 */
@Getter
public class LimsCodeException extends LimsException {

    private final int code;

    /**
     * 自定义data
     */
    private Object data = Collections.emptyMap();

    public LimsCodeException(int code, String message) {
        super(message);
        this.code = code;
    }

    public LimsCodeException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public LimsCodeException(int code, Throwable cause) {
        super(cause);
        this.code = code;
    }

    public LimsCodeException setData(Object data) {
        this.data = Objects.requireNonNull(data);
        return this;
    }
}
