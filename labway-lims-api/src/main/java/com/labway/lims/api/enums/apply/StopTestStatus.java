package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 样本检验项目终止检验状态
 * 
 * <AUTHOR>
 * @since 2023/8/10 11:05
 */
@Getter
@AllArgsConstructor
public enum StopTestStatus {

    NO_STOP_TEST(0, "正常"),

    STOP_TEST_CHARGE(1, "终止收费"),

    STOP_TEST_FREE(2, "终止不收费"),

    DISABLE(3, "禁用"),

    ;

    private final int code;

    private final String desc;

    public static StopTestStatus getByCode(Integer code) {
        for (StopTestStatus statusEnum : values()) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum;
            }
        }
        return StopTestStatus.NO_STOP_TEST;
    }

}
