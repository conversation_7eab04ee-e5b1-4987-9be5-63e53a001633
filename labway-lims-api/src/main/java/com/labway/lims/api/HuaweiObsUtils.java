package com.labway.lims.api;

import cn.hutool.core.util.IdUtil;
import com.obs.services.ObsClient;
import com.obs.services.internal.Constants;
import com.obs.services.model.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.CaseInsensitiveMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;

@Slf4j
@Component
@ConditionalOnProperty("labway.obs.endPoint")
@ConditionalOnClass(ObsClient.class)
public class HuaweiObsUtils implements InitializingBean, DisposableBean {

    /**
     * 永不过期
     */
    public static final int FOREVER = -1;

    @Value("${labway.obs.ak}")
    @Getter
    private String ak;
    @Value("${labway.obs.sk}")
    private String sk;
    @Getter
    @Value("${labway.obs.endPoint}")
    private String endPoint;
    @Getter
    @Value("${labway.obs.bucketName}")
    private String bucketName;

    @Getter
    @Value("${labway.obs.baseUrl}")
    private String baseUrl;


    @Resource
    private EnvDetector envDetector;
    private ObsClient obsClient;

    /**
     * 上传文件到 obs
     *
     * @param mediaType {@link MediaType}
     */
    public String upload(InputStream is, String mediaType) {
        // 测试环境只保留180天
        final int expires = (envDetector.isDev() || envDetector.isTest()) ? 180 : FOREVER;
        return upload(is, mediaType, expires);
    }

    /**
     * 上传文件
     *
     * @param mediaType {@link MediaType}
     * @param expires   过期时间，天数。如果是 1 那么 1 天后对象会被清理掉，-1 表示永久。通常临时生成的文件会设置一个过期时间。
     */
    public String upload(InputStream is, String mediaType, int expires) {
        return upload(is, mediaType, expires, Collections.emptyMap());
    }


    /**
     * 上传文件
     *
     * @param mediaType {@link MediaType}
     * @param expires   过期时间，天数。如果是 1 那么 1 天后对象会被清理掉，-1 表示永久。通常临时生成的文件会设置一个过期时间。
     */
    @Trace
    public String upload(InputStream is, String mediaType, int expires, Map<String, String> headers) {

        final String objKey = genObjectKey();

        final ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(MediaType.parseMediaType(mediaType).toString());


        final PutObjectRequest request = new PutObjectRequest();
        request.setBucketName(bucketName);

        headers = new CaseInsensitiveMap<>(ObjectUtils.defaultIfNull(headers, Collections.emptyMap()));
        headers.put(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");

        // 跨域
        for (Map.Entry<String, String> e : headers.entrySet()) {
            request.addUserHeaders(e.getKey(), e.getValue());
        }

        request.setObjectKey(objKey);
        request.setInput(is);
        request.setAutoClose(true);
        // 单独配置此对象共公读取
        request.setAcl(AccessControlList.REST_CANNED_PUBLIC_READ);
        request.setMetadata(metadata);
        request.setExpires(expires);

        return path(obsClient.putObject(request).getObjectKey());
    }

    public String genObjectKey() {
        final LocalDateTime now = LocalDateTime.now();
        return "labway-lims/" + envDetector.envName() + "/" + now.getYear() + "/" +
                StringUtils.leftPad(String.valueOf(now.getMonthValue()), 2, "0") + "/"
                + StringUtils.leftPad(String.valueOf(now.getDayOfMonth()), 2, "0") + "/"
                + StringUtils.leftPad(String.valueOf(now.getHour()), 2, "0") + "/"
                + StringUtils.leftPad(String.valueOf(now.getMinute()), 2, "0") + "/"
                + IdUtil.simpleUUID();
    }

    public String path(String name) {
        return baseUrl + name;
    }

    public PostSignatureResponse createPostSignature(String objectKey, Duration expires) {

        final PostSignatureRequest request = new PostSignatureRequest();
        request.setBucketName(bucketName);
        request.setExpires(expires.getSeconds());
        request.setObjectKey(objectKey);

        // 公共读取
        request.setFormParams(Map.of("x-obs-acl", Constants.ACL_PUBLIC_READ));

        return obsClient.createPostSignature(request);

    }

    /**
     * 根据文件地址获取名称下载File类型的文件
     *
     * @param fileUrl
     * @return
     */
    public File downloadFileByUrl(String fileUrl) throws IOException {

        // filename就是objectKey
        String fileName = getObjectKeyByUrl(fileUrl);
        ObsObject obsObject = obsClient.getObject(bucketName, fileName);

        // 文件流保存到本地
        InputStream inputStream = obsObject.getObjectContent();
        String savePath = System.getProperty("user.dir") + "/file/" + fileName;
        File toFile = new File(savePath);
        // 如果文件已存在，则删除它
        if (toFile.exists()) {
            toFile.delete();
        }
        FileUtils.copyInputStreamToFile(inputStream, toFile);

        return toFile;
    }

	/**
	 * 下载字节数组
	 */
	public byte[] downloadBytesByUrl(String fileUrl) throws IOException{
		// filename就是objectKey
		String fileName = getObjectKeyByUrl(fileUrl);
		ObsObject obsObject = obsClient.getObject(bucketName, fileName);

		// 文件流保存到本地
		InputStream inputStream = obsObject.getObjectContent();
		return IOUtils.toByteArray(inputStream);
	}

    @Override
    public void destroy() throws Exception {
        IOUtils.closeQuietly(obsClient);
    }

    @Override
    public void afterPropertiesSet() {
        obsClient = new ObsClient(ak, sk, endPoint);
        log.info("init huawei obs success endpoint {} bucket {} baseurl {}", endPoint, bucketName, baseUrl);
    }

    /**
     * 根据下载地址url获取文件名称
     *
     * @param url
     */
    private String getObjectKeyByUrl(String url) {

        String fileName = url.replace(baseUrl, "");
        return fileName;
    }

}
