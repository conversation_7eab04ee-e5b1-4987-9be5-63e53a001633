package com.labway.lims.api.exception;

import cn.hutool.extra.spring.SpringUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.dubbo.remoting.RemotingException;
import org.apache.dubbo.rpc.RpcException;
import org.mybatis.spring.MyBatisSystemException;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.sql.DataTruncation;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 全局的的异常拦截器（拦截所有的控制器）（带有@RequestMapping注解的方法上都会拦截）
 */
@Slf4j
@RestControllerAdvice
@ConditionalOnWebApplication
@ConditionalOnClass(SqlSessionFactoryBean.class)
class GlobalExceptionHandler implements InitializingBean {

    private static final String TEXT_PLAIN_UTF_8 =
            new MediaType(MediaType.TEXT_PLAIN.getType(), MediaType.TEXT_PLAIN.getSubtype(), StandardCharsets.UTF_8)
                    .toString();

    private static final String NOTIFY_URL =
            "https://oapi.dingtalk.com/robot/send?access_token=4bcb4be027ca679791d37b194697500bb427cfcedc355ec6ea8b15d1f52cb468";

    private final LinkedBlockingDeque<ExceptionNotifyJob> deque = new LinkedBlockingDeque<>();

    private DefaultDingTalkClient dingTalkClient;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private EnvDetector envDetector;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;

    /**
     * 这里是认证失败，响应码应该是401！注意 401与403的区别！
     */
    @ExceptionHandler(AuthenticationException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Object authenticationException(AuthenticationException e) {
        return Error.builder().code(401).data(Collections.emptyMap()).message(e.getMessage()).build();
    }

    /**
     * 通用异常
     */
    @ExceptionHandler({Exception.class, RemotingException.class, RpcException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Object exception(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);

        addJob(new ExceptionNotifyJob(request.getRequestURL().toString(), LoginUserHandler.getUnsafe(), e));

        return Error.builder().code(500).message("服务器异常, 请稍后再试").stackTrace(ExceptionUtils.getStackTrace(e))
                .data(Collections.emptyMap()).build();
    }

    /**
     * 参数检验
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Object constraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        log.error(e.getMessage(), e);

        addJob(new ExceptionNotifyJob(request.getRequestURL().toString(), LoginUserHandler.getUnsafe(), e));
        return Error.builder().code(496)
                .message(e.getConstraintViolations().stream().map(p->p.getMessage()).collect(Collectors.joining(",")))
                .stackTrace(ExceptionUtils.getStackTrace(e))
                .data(Collections.emptyMap()).build();
    }

    /**
     * 参数检验
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Object noHandlerFoundException(MethodArgumentNotValidException e, HttpServletRequest request) {
        log.error(e.getMessage(), e);

        addJob(new ExceptionNotifyJob(request.getRequestURL().toString(), LoginUserHandler.getUnsafe(), e));
        return Error.builder().code(497)
                .message(e.getBindingResult().getAllErrors().stream()
                        .map(ObjectError::getDefaultMessage)
                        .collect(Collectors.joining(";")))
                .stackTrace(ExceptionUtils.getStackTrace(e))
                .data(Collections.emptyMap()).build();
    }



    /**
     * 构建pdf
     */
    @ExceptionHandler({BuildPdfLimsException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Object buildPdfLimsException(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        addJob(new ExceptionNotifyJob(request.getRequestURL().toString(), LoginUserHandler.getUnsafe(), e));
        return Error.builder().code(498).message(e.getMessage()).stackTrace(ExceptionUtils.getStackTrace(e))
                .data(Collections.emptyMap()).build();
    }

    /**
     * LimsException
     */
    @ExceptionHandler({LimsException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Object limsException(HttpServletRequest request, LimsException e) {
        log.error(e.getMessage(), e);

        addJob(new ExceptionNotifyJob(request.getRequestURL().toString(), LoginUserHandler.getUnsafe(), e));

        return Error.builder().code(500).message(e.getMessage()).stackTrace(ExceptionUtils.getStackTrace(e))
                .data(Collections.emptyMap()).build();
    }

    /**
     * 通用异常
     */
    @ExceptionHandler({IllegalStateException.class, IllegalArgumentException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Object limsException(Exception e) {
        log.error(e.getMessage(), e);
        return Error.builder().code(499).message(e.getMessage()).stackTrace(ExceptionUtils.getStackTrace(e))
                .data(Collections.emptyMap()).build();
    }

    /**
     * 通用异常
     */
    @ExceptionHandler(LimsCodeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Object limsCodeException(LimsCodeException e) {
        log.error(e.getMessage(), e);
        return Error.builder().code(e.getCode()).stackTrace(ExceptionUtils.getStackTrace(e))
                .data(ObjectUtils.defaultIfNull(e.getData(), Collections.emptyMap())).message(e.getMessage()).build();
    }

    /**
     * HttpRequestMethodNotSupportedException
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public Object httpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        return Error.builder().code(405).message(String.format("不支持 %s 请求", e.getMethod())).data(Collections.emptyMap())
                .build();
    }

    /**
     * MissingServletRequestParameterException
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public Object missingServletRequestParameterException(MissingServletRequestParameterException e) {
        return Error.builder().code(400).message(String.format("缺少 %s 请求参数 ", e.getParameterName()))
                .data(Collections.emptyMap()).build();
    }

    /**
     * 空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Object nullPointerException(HttpServletRequest request, NullPointerException e) {
        log.error("URL:{},空指针异常:{}", request.getRequestURL(), e.getMessage(), e);

        addJob(new ExceptionNotifyJob(request.getRequestURL().toString(), LoginUserHandler.getUnsafe(), e));

        return Error.builder().code(500).message("服务器异常, 请稍后再试").data(Collections.emptyMap())
                .stackTrace(ExceptionUtils.getStackTrace(e)).build();
    }

    /**
     * sql 异常
     */
    @ExceptionHandler({SQLException.class, DataIntegrityViolationException.class, DataTruncation.class,
            DataAccessException.class, MyBatisSystemException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Object sqlException(HttpServletRequest request, Exception e) {
        log.error("SQL运行时异常:", e);

        addJob(new ExceptionNotifyJob(request.getRequestURL().toString(), LoginUserHandler.getUnsafe(), e));

        return Error.builder().code(500).message("服务器异常, 请稍后再试").stackTrace(ExceptionUtils.getStackTrace(e))
                .data(Collections.emptyMap()).build();
    }

    @Override
    public void afterPropertiesSet() throws Exception {

        dingTalkClient = new DefaultDingTalkClient(NOTIFY_URL);

        threadPoolConfig.getPool().execute(() -> {
            try {
                while (!Thread.currentThread().isInterrupted()) {
                    final ExceptionNotifyJob job = deque.poll(1, TimeUnit.MINUTES);

                    if (Objects.isNull(job)) {
                        continue;
                    }

                    send(job);

                }
            } catch (InterruptedException e) {
                log.error("通知错误任务被终止", e);
                Thread.currentThread().interrupt();
            }
        });

    }

    /**
     * 添加任务
     */
    private void addJob(ExceptionNotifyJob job) {
        if (envDetector.isDev() || envDetector.isTest()) {
            return;
        }

        deque.add(job);
    }

    /**
     * 发送消息
     */
    private void send(ExceptionNotifyJob job) {

        String sb = null;

        try {

            // 堆栈上传到 obs 7 天自动过期
            final String url =
                    huaweiObsUtils.upload(new ByteArrayInputStream(ExceptionUtils.getStackTrace(job.exception).getBytes()),
                            TEXT_PLAIN_UTF_8, 7, null, Map.of(HttpHeaders.CONTENT_LANGUAGE, Locale.CHINA.toLanguageTag()));

            // 操作人
            final String nickname =
                    Optional.ofNullable(job.user).map(LoginUserHandler.User::getNickname).orElse("anonymous");

            sb = "### 实验室 LIMS 出现 " + job.exception.getClass().getSimpleName() + " 异常" + System.lineSeparator()
                    + "#### 环境: " + envDetector.envName() + System.lineSeparator() + "#### 地址: " + job.url
                    + System.lineSeparator() + "#### 方法: " + job.exception.getStackTrace()[0].getClassName() + "#"
                    + job.exception.getStackTrace()[0].getMethodName() + System.lineSeparator() + "#### 行数: "
                    + job.exception.getStackTrace()[0].getLineNumber() + System.lineSeparator() + "#### 操作人: " + nickname
                    + System.lineSeparator() + "#### 堆栈: " + url + System.lineSeparator();

            final OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
            markdown.setTitle("测试环境");
            markdown.setText(sb);

            final OapiRobotSendRequest request = new OapiRobotSendRequest();
            request.setMsgtype("markdown");
            request.setMarkdown(markdown);

            final OapiRobotSendResponse response = dingTalkClient.execute(request);
            if (!response.isSuccess()) {
                throw new LimsException("发送消息失败 " + response.getErrmsg());
            }

        } catch (Exception e) {
            log.warn("发送钉钉消息失败 消息:{}", sb, e);
        }
    }

    /**
     * 通知任务
     */
    @AllArgsConstructor
    private static class ExceptionNotifyJob {
        /**
         * 请求地址
         */
        private final String url;

        /**
         * 当前登录人
         */
        @Nullable
        private final LoginUserHandler.User user;

        /**
         * 异常
         */
        private final Exception exception;
    }

    @Builder
    @Getter
    @Setter
    private static class Error {
        /**
         * 错误码
         */
        private int code;

        /**
         * 错误信息
         */
        private String message;

        /**
         * 额外信息
         */
        private Object data;

        /**
         * 堆栈
         */
        private String stackTrace;

        /**
         * 只有在开发环境和测试环境返回堆栈信息
         */
        public String getStackTrace() {
            final EnvDetector envDetector = SpringUtil.getBean(EnvDetector.class);
            if (envDetector.isTest() || envDetector.isDev()) {
                return stackTrace;
            }
            return null;
        }
    }
}
