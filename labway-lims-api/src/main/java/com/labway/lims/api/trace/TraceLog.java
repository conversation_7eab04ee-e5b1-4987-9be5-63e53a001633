package com.labway.lims.api.trace;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;

import java.util.Date;
import java.util.Map;

/**
 * 日志
 */
@Slf4j
@Getter
@Setter
@Accessors(chain = true)
public class TraceLog {

    /**
     * 发送到mq的交换机
     */
    public static final String EXCHANGE = "labway_lims";

    /**
     * 发送到交换机的路由键
     */
    public static final String ROUTING_KEY = "lims_trace";

    private final String system = "labway_lims";

    private final String systemName = "实验室LIMS";

    /**
     * 链路ID
     */
    private String traceId;

    /**
     * 用户名
     */
    private String nickname;

    /**
     * 登录名
     */
    private String username;

    /**
     * 模块
     *
     * @see TraceLogModuleEnum
     */
    private String module;

    /**
     * 请求地址
     */
    private String remoteAddr;

    /**
     * 操作类型
     */
    private String type;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 操作日期
     */
    private Date date;

    private TraceLog() {
        init();
    }

    public static TraceLog newInstance() {
        return new TraceLog();
    }

    @Trace
    private void init() {

        setTraceId(TraceContext.traceId());

        setNickname(TraceContext.getCorrelation(TraceContextKeys.Nickname.name()).orElse(StringUtils.EMPTY));
        setUsername(TraceContext.getCorrelation(TraceContextKeys.Username.name()).orElse(StringUtils.EMPTY));
        setRemoteAddr(TraceContext.getCorrelation(TraceContextKeys.RemoteAddr.name()).orElse(StringUtils.EMPTY));

        setDate(new Date());


    }

    public String toJSONString() {
        final String json = toBusinessCenterJSONString();
        if (log.isTraceEnabled()) {
            log.trace("to businessCenterJson {}", json);
        }
        return json;
    }

    /**
     * 转成业务中台的
     */
    String toBusinessCenterJSONString() {
        return JSON.toJSONString(Map.of(
                "sendTime", System.currentTimeMillis(),
                "messageId", IdUtil.simpleUUID(),
                // com.labway.business.center.core.log.param.OperationLogRequest
                "message", JSON.toJSONString(Map.of(
                        "ipAddress", StringUtils.defaultString(getRemoteAddr()),
                        "ipLocation", StringUtils.EMPTY,
                        "operationMessage", StringUtils.defaultString(getContent()),
                        "operationTime", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"),
                        "operationTypeCode", StringUtils.defaultString(getType()),
                        "requestId", IdUtil.simpleUUID(),
                        "systemKey", StringUtils.defaultString(getSystem()),
                        "systemModule", StringUtils.defaultString(getModule()),
                        "systemName", StringUtils.defaultString(getSystemName()),
                        "userName", StringUtils.defaultString(getNickname())
                ))
        ));
    }

    @Override
    public String toString() {
        return toJSONString();
    }
}
