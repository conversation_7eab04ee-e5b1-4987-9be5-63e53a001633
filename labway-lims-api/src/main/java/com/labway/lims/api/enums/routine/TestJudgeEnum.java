package com.labway.lims.api.enums.routine;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2023/4/6 16:22
 */
@Getter

public enum TestJudgeEnum {
    /**
     * 高
     */
    UP("UP"),
    /**
     * 低
     */
    DOWN("DOWN"),

    /**
     * 无
     */
    DEFAULT(StringUtils.EMPTY);

    /**
     * 值
     */
    private final String value;

    TestJudgeEnum(String value) {
        this.value = value;
    }

    /**
     * 获取测试判断
     * <p>
     * 状态编码
     *
     * @return 状态枚举值
     */
    public static TestJudgeEnum getEnumByStr(String value) {
        return Arrays.stream(TestJudgeEnum.values()).filter(item -> item.getValue().equals(value)).findFirst()
                .orElse(DEFAULT);
    }

}
