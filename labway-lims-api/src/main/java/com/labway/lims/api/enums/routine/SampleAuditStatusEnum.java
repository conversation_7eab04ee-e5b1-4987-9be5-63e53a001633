package com.labway.lims.api.enums.routine;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/4/10 19:16
 */
@Getter
@AllArgsConstructor
public enum SampleAuditStatusEnum {

    /**
     * 一审
     */
    ONE_CHECK,

    /**
     * 二审
     */
    TWO_CHECK,

    /**
     * 取消一审
     */
    CANCEL_ONE_CHECK,

    /**
     * 取消二审
     */
    CANCEL_TWO_CHECK,

    /**
     * 审核
     */
    CHECK,

    /**
     * 取消审核
     */
    CANCEL_CHECK;


    // 根据字符串获取枚举
    public static SampleAuditStatusEnum getByName(String name) {
        for (SampleAuditStatusEnum value : values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return CHECK;
    }


}
