package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/5/9 11:28
 */
@Getter
@AllArgsConstructor
public enum SampleTrackEnum {

    /**
     * 物流取样
     */
    LOGISTICS_SAMPLE,

    /**
     * 样本信息补录
     */
    SAMPLE_INFO_SUPPLEMENT,

    /**
     * 样本复核
     */
    SAMPLE_REVIEW,

    /**
     * 一次分拣
     */
    ONE_PICK,

    /**
     * 分血
     */
    SPLIT_BLOOD,

    /**
     * 二次分拣
     */
    TWO_PICK,

    /**
     * 检验(常规检验、微生物检验、院感检验、遗传检验、外送检验、特检、、、)
     */
    INSPECTION,

    /**
     * 报告审核
     */
    REPORT_CHECK,

    /**
     * 报告打印
     */
    REPORT_PRINT;
}
