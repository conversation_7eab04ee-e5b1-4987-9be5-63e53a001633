package com.labway.lims.api.enums.routine;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/4/10 16:25
 */
@Getter
@AllArgsConstructor
public enum MissReportItemNumEnum {
    //缺项的数量(全、-1、-2、-3、-4、-5、缺、复)
    ALL("全"),
    NEG_ONE("-1"),
    NEG_TWO("-2"),
    NEG_THREE("-3"),
    NEG_FOUR("-4"),
    NEG_FIVE("-5"),
    NEG("缺");

    private final String desc;

}
