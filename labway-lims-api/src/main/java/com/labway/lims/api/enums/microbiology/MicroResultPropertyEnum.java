package com.labway.lims.api.enums.microbiology;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 微生物结果值属性
 * 
 * <AUTHOR>
 * @since 2023/10/19 13:12
 */
@Getter
@AllArgsConstructor
public enum MicroResultPropertyEnum {
    /**
     * 阴性
     */
    NEGATIVE("阴性"),
    /**
     * 阳性
     */
    POSITIVE("阳性"),
    /**
     * 涂片
     */
    SMEAR("涂片");

    private final String desc;
}
