package com.labway.lims.api.enums.microbiology;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 微生物敏感值
 * 
 * <AUTHOR>
 * @since 2023/4/13 15:12
 */
@Getter
@AllArgsConstructor
public enum MicroSusceptibilityEnum {

    SENSITIVE("敏感"),

    MID_SENSITIVE("中敏"),

    RESISTANT("耐药"),

    INSENSITIVE("非敏感"),

    WT("WT"),

    NWT("NWT"),

	// ✨feat：【微生物检验】【药物参考范围维护】敏感度下拉选择，增加选项：“SDD" https://www.tapd.cn/59091617/prong/stories/view/1159091617001002002
    SDD("SDD"),

    ;


    private final String desc;
}
