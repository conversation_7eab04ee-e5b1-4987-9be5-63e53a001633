package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/3/31 10:17
 */
@Getter
@AllArgsConstructor
public enum SexEnum {

    MAN(1, "男"),

    WOMEN(2, "女"),

    /**
     * 通用/未知
     */
    DEFAULT(0, "未知");

    private final Integer code;

    private final String desc;

    @NonNull
    public static SexEnum getByDesc(String desc) {
        for (SexEnum sexEnum : SexEnum.values()) {
            if (StringUtils.equalsIgnoreCase(desc, sexEnum.desc)) {
                return sexEnum;
            }
        }
        return DEFAULT;
    }

    @NonNull
    public static SexEnum getByCode(Integer code) {
        for (SexEnum statusEnum : SexEnum.values()) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum;
            }
        }
        return DEFAULT;
    }

}
