<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>labway-lims</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.labway.lims</groupId>
        <artifactId>labway-lims-project-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>


    <scm>
        <url>https://codeup.aliyun.com/5eb1331e38076f00011bc7d8/Labway-LIS/LIMS/lims-examine</url>
    </scm>

    <name>labway-lims</name>

    <modules>
        <module>labway-lims-apply</module>
        <module>labway-lims-genetics</module>
        <module>labway-lims-infection</module>
        <module>labway-lims-microbiology</module>
        <module>labway-lims-specialty</module>
        <module>labway-lims-routine</module>
        <module>labway-lims-outsourcing</module>
        <module>labway-lims-base</module>
        <module>labway-lims-api</module>
        <module>labway-lims-pdfreport</module>
        <module>labway-lims-job</module>
        <module>labway-lims-statistics</module>
        <module>labway-lims-qc</module>
        <module>labway-lims-meibiao</module>
        <module>labway-lims-bloodculture</module>
        <module>labway-lims-gateway</module>
    </modules>


    <properties>
        <elasticsearch.version>7.10.2</elasticsearch.version>
        <spring-data-elasticsearch.version>4.2.12</spring-data-elasticsearch.version>
        <easyexcel.version>3.2.1</easyexcel.version>
        <maven.test.skip>true</maven.test.skip>
        <itext7-core.version>8.0.0</itext7-core.version>
        <html2pdf.version>5.0.0</html2pdf.version>
        <business-center-compare-api.version>1.0.0-SNAPSHOT</business-center-compare-api.version>
        <business-center.version>1.0.0-SNAPSHOT</business-center.version>
        <alibaba-dingtalk-service-sdk.version>2.0.0</alibaba-dingtalk-service-sdk.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <swak-common.version>2.0.0</swak-common.version>
        <commons-text.version>1.10.0</commons-text.version>
        <j2html.version>1.6.0</j2html.version>
        <pdfbox.version>2.0.27</pdfbox.version>
    </properties>

    <dependencies>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-statistics-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
            <dependency>
                <groupId>com.j2html</groupId>
                <artifactId>j2html</artifactId>
                <version>${j2html.version}</version>
            </dependency>


            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>font-asian</artifactId>
                <version>${itext7-core.version}</version>
            </dependency>


            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>sign</artifactId>
                <version>${itext7-core.version}</version>
            </dependency>


            <dependency>
                <groupId>io.gitee.mcolley</groupId>
                <artifactId>swak-common</artifactId>
                <version>${swak-common.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>${alibaba-dingtalk-service-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>barcodes</artifactId>
                <version>${itext7-core.version}</version>
            </dependency>


            <dependency>
                <groupId>com.labway</groupId>
                <artifactId>business-center-compare-api</artifactId>
                <version>${business-center-compare-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.labway</groupId>
                <artifactId>business-center-common</artifactId>
                <version>${business-center-compare-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.labway</groupId>
                <artifactId>business-center-mdm-api</artifactId>
                <version>${business-center.version}</version>
            </dependency>
            <dependency>
                <groupId>com.labway</groupId>
                <artifactId>business-center-mdm-common</artifactId>
                <version>${business-center.version}</version>
            </dependency>

            <dependency>
                <groupId>com.labway</groupId>
                <artifactId>business-center-third-ncc-api</artifactId>
                <version>${business-center.version}</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext7-core</artifactId>
                <version>${itext7-core.version}</version>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>html2pdf</artifactId>
                <version>${html2pdf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-pdfreport-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-bloodculture-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-genetics-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-base-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-infection-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-microbiology-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-outsourcing-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-routine-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-apply-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.labway.lims</groupId>
                <artifactId>labway-lims-specialty-api</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!--上传私服携带源码-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
