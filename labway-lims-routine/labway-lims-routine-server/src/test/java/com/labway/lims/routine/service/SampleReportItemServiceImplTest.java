package com.labway.lims.routine.service;

import com.labway.lims.api.Snowflake;
import com.labway.lims.routine.api.service.SampleReportItemService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SampleReportItemServiceImplTest {
    @Resource
    private SampleReportItemService sampleReportItemService;

    @Test
    public void test() {
        sampleReportItemService.selectBySampleId(new Snowflake(5, 5).nextId());
    }
}