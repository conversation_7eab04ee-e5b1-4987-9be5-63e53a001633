package com.labway.lims.routine.service.chain.audit;

import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

/**
 * <pre>
 * ResultRuleVerifyTipsCommandTest
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/2/10 13:16
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
class ResultRuleVerifyTipsCommandTest {

    @Resource
    private ResultRuleVerifyTipsCommand command;

    @Test
    void execute() {

        AuditSampleContext context = new AuditSampleContext(null);

        /*
          # 3900200	游离前列腺特异性抗原测定     25040400600101	游离前列腺特异性抗原测定
          # 3900190	总前列腺特异性抗原测定      25040400500101	总前列腺特异性抗原测定
          # 2800110	肌酸激酶-MB同工酶活性测定   25030600200101	血清肌酸激酶-MB同工酶活性测定
          # 2800090	血清肌酸激酶测定           25030600100101	血清肌酸激酶测定
          # 2500030	血清高密度脂蛋白胆固醇测定   25030300400101	血清高密度脂蛋白胆固醇测定
          # 2500040	血清低密度脂蛋白胆固醇测定   25030300500101	血清低密度脂蛋白胆固醇测定
          # 2500010	血清总胆固醇测定           25030300100101	血清总胆固醇测定
          # [3900200]<[3900190]|FPSA>TPSA，不能审核！
          # [2800110]<[2800090]|CK<CK-MB，不能审核！
          # [2500030]+[2500040]<[2500010]|TC<HDL+LDL，不能审核！
         */

        SampleResultDto e1 = new SampleResultDto() {{
            setReportItemCode("25040400600101");
            setResult("3");
        }};
        SampleResultDto e2 = new SampleResultDto() {{
            setReportItemCode("25040400500101");
            setResult("4");
        }};
        SampleResultDto e3 = new SampleResultDto() {{
            setReportItemCode("25040400500101");
            setResult("3");
        }};

        try {
            context.put(AuditSampleContext.SAMPLE_REPORT_ITEM_RESULTS, List.of(e1, e2));
            boolean execute = command.execute(context);
            log.info(String.valueOf(execute));
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        try {
            context.put(AuditSampleContext.SAMPLE_REPORT_ITEM_RESULTS, List.of(e1, e3));
            boolean execute = command.execute(context);
            log.info(String.valueOf(execute));
        } catch (Exception e) {
            log.error(e.getMessage());
        }

    }
}