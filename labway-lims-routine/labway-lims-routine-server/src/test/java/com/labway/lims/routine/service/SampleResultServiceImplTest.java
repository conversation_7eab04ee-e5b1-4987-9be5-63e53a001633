package com.labway.lims.routine.service;

import com.labway.lims.api.Snowflake;
import com.labway.lims.routine.api.service.SampleResultService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SampleResultServiceImplTest {
    @Resource
    private SampleResultService sampleResultService;

    @Test
    public void test() {
        sampleResultService.selectBySampleId(new Snowflake(5, 5).nextId());
    }
}