package com.labway.lims.routine.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.junit.Test;

import java.util.Date;

public class TbSampleReportItemTest {
    @Test
    public void test() {
        final TbSampleReportItem sampleReportItem = new TbSampleReportItem();
        sampleReportItem.setSampleReportItemId(0L);
        sampleReportItem.setApplyId(0L);
        sampleReportItem.setSampleId(0L);
        sampleReportItem.setApplySampleId(0L);
        sampleReportItem.setReportItemCode("");
        sampleReportItem.setReportItemName("");
        sampleReportItem.setReportItemId(0L);
        sampleReportItem.setTestItemId(0L);
        sampleReportItem.setTestItemCode("");
        sampleReportItem.setTestItemName("");
        sampleReportItem.setIsRetest(0);
        sampleReportItem.setIsDelete(0);
        sampleReportItem.setPrintSort(0);
        sampleReportItem.setUpdateDate(new Date());
        sampleReportItem.setCreateDate(new Date());
        sampleReportItem.setCreatorId(0L);
        sampleReportItem.setCreatorName("");
        sampleReportItem.setUpdaterId(0L);
        sampleReportItem.setUpdaterName("");

        System.out.println(JSON.toJSONString(sampleReportItem, SerializerFeature.WriteMapNullValue));

    }
}