package com.labway.lims.routine.service.chain.pick.two;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.SpelNode;
import org.springframework.expression.spel.ast.VariableReference;
import org.springframework.expression.spel.standard.SpelExpression;
import org.springframework.expression.spel.standard.SpelExpressionParser;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/20 19:17
 */
class TwoPickRecalculateCommandTest {

    final ExpressionParser expressionParser = new SpelExpressionParser();

    @Test
    void test() {
        final List<Item> hasItems = new LinkedList<>();
        hasItems.add(new Item("aaa", ""));
        hasItems.add(new Item("bbb", ""));

        final List<Item> allItems = new LinkedList<>();
        allItems.add(new Item("ddd", "#ccc+#bbb"));
        allItems.add(new Item("eee", "#ccc+#ddd"));
        allItems.add(new Item("aaa", ""));
        allItems.add(new Item("bbb", ""));
        allItems.add(new Item("ccc", "#aaa+#bbb"));


        allItems.add(new Item("fff", "#ggg+#bbb"));
        allItems.add(new Item("ggg", "#fff+#bbb"));


        for (Item allItem : allItems) {
            if (getItem(hasItems, allItems, allItem, 0)) {
                hasItems.add(allItem);
            }
        }
        System.out.println("-----------");
        hasItems.forEach(e -> System.out.println(e.getCode()));
    }

    @Getter
    @Setter
    @AllArgsConstructor
    static class Item {
        private String code;
        private String cal;

    }
    public boolean getItem(List<Item> hasItems, List<Item> allItems, Item current, int loop) {

        if (loop > 10) {
            System.out.printf("[%s] 项目循环依赖%n", current.getCode());
            return false;
        }

        //待添加项目已经有了false
        if (hasItems.stream().anyMatch(x -> Objects.equals(x.getCode(), current.getCode()))) {
            return false;
        }
        //过滤计算公式是空的
        if (StringUtils.isBlank(current.getCal())) {
            return false;
        }

        final SpelExpression expression = (SpelExpression) expressionParser
                .parseExpression(StringUtils.replace(current.getCal(), "#", "#_"));

        //找到所有依赖的报告项目
        final Set<String> refCodes = getAllVariableReference(expression)
                .stream()
                .map(VariableReference::toStringAST)
                .map(e -> e.replace("#_", ""))
                .collect(Collectors.toSet());

        //为空返回false
        if (CollectionUtils.isEmpty(refCodes)) {
            return false;
        }

        //在已经添加的报告项目中遍历
        for (Item hsaItem : hasItems) {
            refCodes.removeIf(f -> Objects.equals(hsaItem.getCode(), f));
        }
        //已有的报告项目全包含直接返回
        if (CollectionUtils.isEmpty(refCodes)) {
            return true;
        }

        int count = 0;
        for (String cloneCode : refCodes) {
            //依赖的报告项目在所有报告项目不存在直接返回false
            final Item item = allItems.stream()
                    .filter(x -> Objects.equals(x.getCode(), cloneCode))
                    .findFirst()
                    .orElse(null);

            if (Objects.isNull(item)) {
                return false;
            }

            if (getItem(hasItems, allItems, item, ++loop)) {
                count++;
            }
        }

        return count == refCodes.size();
    }

    public Set<VariableReference> getAllVariableReference(SpelExpression expression) {
        final HashSet<VariableReference> list = new HashSet<>();
        doGetAllVariableReference(expression.getAST(), list);
        return list;
    }

    private void doGetAllVariableReference(SpelNode node, Set<VariableReference> list) {

        if (node instanceof VariableReference) {
            list.add((VariableReference) node);
        }

        for (int i = 0; i < node.getChildCount(); i++) {
            final SpelNode child = node.getChild(i);
            doGetAllVariableReference(child, list);
        }
    }

}