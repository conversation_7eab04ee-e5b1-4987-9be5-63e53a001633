package com.labway.lims.routine;

import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Test;

import java.math.RoundingMode;

public class NumberUtilsTest {
    @Test
    public void test() {
        System.out.println(NumberUtils.toScaledBigDecimal("0.005", 6, RoundingMode.HALF_EVEN));
        System.out.println(NumberUtils.toScaledBigDecimal("0.00005", 6, RoundingMode.HALF_EVEN)
                .compareTo(NumberUtils.toScaledBigDecimal("0.00005000", 6, RoundingMode.HALF_EVEN)));
    }
}
