package com.labway.lims.routine.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.junit.Test;

import java.util.Date;

public class TbSampleTest {
    @Test
    public void test() {
        final TbSample sample = new TbSample();
        sample.setSampleId(0L);
        sample.setApplySampleId(0L);
        sample.setApplyId(0L);
        sample.setBarcode("");
        sample.setSampleNo("");
        sample.setGroupId(0L);
        sample.setGroupName("");
        sample.setInstrumentGroupId(0L);
        sample.setInstrumentGroupName("");
        sample.setInstrumentName("");
        sample.setInstrumentId(0L);
        sample.setTestDate(new Date());
        sample.setOneCheckerName("");
        sample.setOneCheckerId(0L);
        sample.setOneCheckDate(new Date());
        sample.setTwoCheckerName("");
        sample.setTwoCheckerId(0L);
        sample.setTwoCheckDate(new Date());
        sample.setUpdateDate(new Date());
        sample.setCreateDate(new Date());
        sample.setUpdaterId(0L);
        sample.setUpdaterName("");
        sample.setCreatorId(0L);
        sample.setCreatorName("");
        sample.setHspOrgId(0L);
        sample.setHspOrgName("");
        sample.setOrgId(0L);
        sample.setOrgName("");
        sample.setIsDelete(0);

        System.out.println(JSON.toJSONString(sample, SerializerFeature.WriteMapNullValue));

    }
}