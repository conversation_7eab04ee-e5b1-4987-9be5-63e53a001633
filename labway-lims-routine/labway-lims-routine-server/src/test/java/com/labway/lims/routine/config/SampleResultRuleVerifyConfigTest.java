package com.labway.lims.routine.config;

import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * <pre>
 * SampleResultRuleVerifyConfigTest
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/2/10 10:36
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class SampleResultRuleVerifyConfigTest {

    @Autowired
    SampleResultRuleVerifyConfig verifyConfig;

    @Test
    public void getConfig() {

        System.out.println(JSON.toJSONString(verifyConfig.getVerifyRules()));

    }

}