package com.labway.lims.routine.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.junit.Test;

import java.util.Date;

public class TbSampleResultTest {
    @Test
public void test(){
        final TbSampleResult sampleResult = new TbSampleResult();
        sampleResult.setSampleResultId(0L);
        sampleResult.setSampleId(0L);
        sampleResult.setApplySampleId(0L);
        sampleResult.setApplyId(0L);
        sampleResult.setTestItemId(0L);
        sampleResult.setTestItemCode("");
        sampleResult.setTestItemName("");
        sampleResult.setReportItemId(0L);
        sampleResult.setReportItemCode("");
        sampleResult.setReportItemName("");
        sampleResult.setType("");
        sampleResult.setResult("");
        sampleResult.setUnit("");
        sampleResult.setRange("");
        sampleResult.setStatus(0);
        sampleResult.setInstrumentId(0L);
        sampleResult.setInstrumentName("");
        sampleResult.setInstrumentResult("");
        sampleResult.setJudge("");
        sampleResult.setCreateDate(new Date());
        sampleResult.setUpdateDate(new Date());
        sampleResult.setCreatorId(0L);
        sampleResult.setCreatorName("");
        sampleResult.setUpdaterId(0L);
        sampleResult.setUpdaterName("");
        sampleResult.setIsDelete(0);
        System.out.println(JSON.toJSONString(sampleResult, SerializerFeature.WriteMapNullValue));

    }
}