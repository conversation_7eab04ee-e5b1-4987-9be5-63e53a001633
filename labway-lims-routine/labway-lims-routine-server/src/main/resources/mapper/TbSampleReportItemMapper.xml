<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.routine.mapper.TbSampleReportItemMapper">

    <insert id="addBatch" parameterType="java.util.List">
        insert into tb_sample_report_item (
        sample_report_item_id,
        apply_id,
        sample_id,
        report_item_code,
        report_item_name,
        report_item_id,
        test_item_id,
        test_item_code,
        test_item_name,
        is_retest,
        is_delete,
        print_sort,
        update_date,
        create_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name,
        apply_sample_id
        ) values
        <foreach collection="items" item="item" separator=",">
            (
            #{item.sampleReportItemId},
            #{item.applyId},
            #{item.sampleId},
            #{item.reportItemCode},
            #{item.reportItemName},
            #{item.reportItemId},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.testItemName},
            #{item.isRetest},
            #{item.isDelete},
            #{item.printSort},
            #{item.updateDate},
            #{item.createDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.applySampleId}
            )
        </foreach>
    </insert>

    <update id="updateBySampleReportItemIds">
        update tb_sample_report_item
        <set>
            <if test="sampleReportItem.isRetest != null">
                is_retest = #{sampleReportItem.isRetest}
            </if>
        </set>
        where sample_report_item_id in
        <foreach collection="sampleReportItemIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach> and sample_id = #{sampleReportItem.sampleId}

    </update>

    <update id="updateIsDeleteBySampleReportItemIds">
        update tb_sample_report_item_${suffix} set is_delete = 0 where
        sample_report_item_id in
        <foreach collection="sampleReportItemIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
    <select id="selectBySampleIdsAndReportIds"
            resultType="com.labway.lims.routine.model.TbSampleReportItem">
        select sample_id, report_item_id, apply_sample_id
        from tb_sample_report_item
        where (sample_id, report_item_id) in
        <foreach collection="dtos" item="item" open="(" close=")" separator=",">
            (#{item.sampleId},#{item.reportItemId})
        </foreach>
    </select>
</mapper>
