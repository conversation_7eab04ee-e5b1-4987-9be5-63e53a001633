<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.routine.mapper.TbSampleResultMapper">
    <insert id="addBatch">
        INSERT INTO tb_sample_result (
        sample_result_id,
        sample_id,
        apply_id,
        test_item_id,
        test_item_code,
        test_item_name,
        report_item_id,
        report_item_code,
        report_item_name,
        type,
        result,
        unit,
        range,
        status,
        instrument_id,
        instrument_name,
        instrument_result,
        judge,
        create_date,
        update_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name,
        is_delete,
        apply_sample_id,
        instrument_report_item_reference_id
        )
        values
        <foreach collection="sampleResults" item="item" index="index" separator=",">
            (
            #{item.sampleResultId},
            #{item.sampleId},
            #{item.applyId},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.testItemName},
            #{item.reportItemId},
            #{item.reportItemCode},
            #{item.reportItemName},
            #{item.type},
            #{item.result},
            #{item.unit},
            #{item.range},
            #{item.status},
            #{item.instrumentId},
            #{item.instrumentName},
            #{item.instrumentResult},
            #{item.judge},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.isDelete},
            #{item.applySampleId},
            #{item.instrumentReportItemReferenceId}
            )
        </foreach>
    </insert>
    <update id="updateBySampleResultIds">
        update tb_sample_result
        <set>
            <if test="sampleResult.result != null">
                result = #{sampleResult.result},
            </if>
            <if test="sampleResult.unit != null">
                unit = #{sampleResult.unit},
            </if>
            <if test="sampleResult.range != null">
                range = #{sampleResult.range},
            </if>
            <if test="sampleResult.status != null">
                status = #{sampleResult.status},
            </if>
        </set>
        where sample_result_id in
        <foreach collection="sampleResultIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectBySampleResultIdIgnoreDelete" resultType="com.labway.lims.routine.model.TbSampleResult">
        select * from tb_sample_result where sample_result_id = #{sampleResultId} and sample_id = #{sampleId}
    </select>

    <select id="selectBySampleIdsAndReportItemIds" resultType="com.labway.lims.routine.model.TbSampleResult">
        select sample_result_id, sample_id, report_item_id
        from tb_sample_result
        where (sample_id, report_item_id)
         in 
        <foreach collection="results" item="item" open="(" close=")" separator=",">
            (#{item.sampleId}, #{item.reportItemId})
        </foreach>
                                                                                     
    </select>

    <select id="selectIgnoreDelete" resultType="com.labway.lims.routine.model.TbSampleResult">
        select * from tb_sample_result
        <where>
            sample_id in
            <foreach collection="sampleIds" item="sampleId" open="(" close=")" separator=",">
                #{sampleId}
            </foreach>
        </where>

    </select>
</mapper>
