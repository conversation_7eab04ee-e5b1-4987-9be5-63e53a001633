<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.routine.mapper.TbSampleMapper">

    <update id="updateBySampleIds">
        update tb_sample
        <set>
            <if test="sample.oneCheckerId != null">
                one_checker_id = #{sample.oneCheckerId},
            </if>
            <if test="sample.oneCheckerName != null">
                one_checker_name = #{sample.oneCheckerName},
            </if>
            <if test="sample.oneCheckDate != null">
                one_check_date = #{sample.oneCheckDate},
            </if>
            <if test="sample.twoCheckerId != null">
                two_checker_id = #{sample.twoCheckerId},
            </if>
            <if test="sample.twoCheckerName != null">
                two_checker_name = #{sample.twoCheckerName},
            </if>
            <if test="sample.twoCheckDate != null">
                two_check_date = #{sample.twoCheckDate},
            </if>
        </set>
        where sample_id in
        <foreach collection="sampleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectAllByTestDate" resultType="com.labway.lims.routine.model.TbSample">
        select tb_sample.* from tb_sample
        left join tb_apply_sample on tb_sample.apply_sample_id = tb_apply_sample.apply_sample_id
        <where>
            tb_sample.test_date >= #{dto.testDateStart} and  #{dto.testDateEnd} >= tb_sample.test_date
            and
            (tb_sample.is_delete = 0 or (tb_sample.is_delete = 1 and tb_apply_sample.is_delete = 1 and tb_apply_sample.merge_master_barcode is not null))

            <if test="dto.groupId != null">
                and tb_sample.group_id = #{dto.groupId}
            </if>
            <if test="dto.hspOrgId != null">
                and tb_sample.hsp_org_id = #{dto.hspOrgId}
            </if>
            <if test="dto.instrumentGroupId != null">
                and tb_sample.instrument_group_id = #{dto.instrumentGroupId}
            </if>
            <if test="dto.instrumentId != null">
                and tb_sample.instrument_id = #{dto.instrumentId}
            </if>
            <if test="dto.sampleNo != null and dto.sampleNo != ''">
                and tb_sample.sample_no = #{dto.sampleNo}
            </if>
        </where>
        order by tb_sample.sample_id asc
    </select>

    <select id="selectSamples" resultType="com.labway.lims.routine.model.TbSample">
        select ts.*
        from tb_sample ts
        where exists (
            <if test="dto.sampleStatus == 'ALL' or dto.isTestSample == 0">
                select 1
            </if>
            <if test="dto.sampleStatus != 'ALL' and dto.isTestSample == 1">
                select 1 from tb_apply_sample tas
                 where tas.apply_sample_id = ts.apply_sample_id
                    and tas.is_delete = 0
                    <if test="dto.sampleStatus != 'STOP_TEST'">
                        and tas.status in
                        <foreach collection="dto.sampleStatusList" item="status" open="(" separator="," close=")">
                            #{status}
                        </foreach>
                    </if>
                    <if test="dto.sampleStatus == 'STOP_TEST'">
                        and tas.status = 99
                        and tas.merge_master_barcode <![CDATA[<>]]> ''
                        and tas.merge_master_barcode is not null
                    </if>
            </if>
         )
        and ts.org_id = #{dto.orgId}
        <if test="dto.groupId != null">
            and ts.group_id = #{dto.groupId}
        </if>
        <if test="dto.hspOrgId != null">
            and ts.hsp_org_id = #{dto.hspOrgId}
        </if>
        <if test="dto.instrumentGroupId != null">
            and ts.instrument_group_id = #{dto.instrumentGroupId}
        </if>
        <if test="dto.instrumentId != null">
            and ts.instrument_id = #{dto.instrumentId}
        </if>
        <if test="dto.sampleNo != null and dto.sampleNo != ''">
            and ts.sample_no = #{dto.sampleNo}
        </if>
        <if test="dto.isTestSample == 1">
            and ts.apply_id <![CDATA[<>]]> 0 and ts.apply_sample_id <![CDATA[<>]]> 0
        </if>
        and ts.test_date >= #{dto.testDateStart} and ts.test_date <![CDATA[<=]]> #{dto.testDateEnd}
        and ts.is_delete = 0
        order by ts.sample_id
    </select>
</mapper>
