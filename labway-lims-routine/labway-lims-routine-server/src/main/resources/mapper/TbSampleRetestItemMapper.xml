<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.routine.mapper.TbSampleRetestItemMapper">

    <insert id="addBatch">
        INSERT INTO tb_sample_retest_item (
        sample_retest_item_id,
        sample_retest_main_id,
        apply_sample_id,
        sample_id,
        test_item_id,
        test_item_code,
        test_item_name,
        report_item_id,
        report_item_code,
        report_item_name,
        test_result_type,
        test_result_type_code,
        retest_mode,
        result,
        judge,
        range,
        status,
        retester_name,
        retester_id,
        org_id,
        org_name,
        is_delete,
        create_date,
        update_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name
        )
        values
        <foreach collection="retestItems" item="item" index="index" separator=",">
            (
            #{item.sampleRetestItemId},
            #{item.sampleRetestMainId},
            #{item.applySampleId},
            #{item.sampleId},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.testItemName},
            #{item.reportItemId},
            #{item.reportItemCode},
            #{item.reportItemName},
            #{item.testResultType},
            #{item.testResultTypeCode},
            #{item.retestMode},
            #{item.result},
            #{item.judge},
            #{item.range},
            #{item.status},
            #{item.retesterName},
            #{item.retesterId},
            #{item.orgId},
            #{item.orgName},
            #{item.isDelete},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName}
            )
        </foreach>
    </insert>
</mapper>
