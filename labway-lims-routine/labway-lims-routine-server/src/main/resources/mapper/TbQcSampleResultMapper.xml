<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.routine.mapper.TbQcSampleResultMapper">


    <insert id="batchAddQcQcSampleResult">
        INSERT INTO tb_qc_sample_result (
        sample_result_id,
        sample_id,
        qc_batch,
        apply_id,
        apply_sample_id,
        test_item_id,
        test_item_code,
        test_item_name,
        report_item_id,
        report_item_code,
        report_item_name,
        type,
        result,
        unit,
        range,
        status,
        instrument_id,
        instrument_name,
        instrument_result,
        judge,
        create_date,
        update_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name,
        is_delete,
        test_date,
        level
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.sampleResultId},
            #{item.sampleId},
            #{item.qcBatch},
            #{item.applyId},
            #{item.applySampleId},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.testItemName},
            #{item.reportItemId},
            #{item.reportItemCode},
            #{item.reportItemName},
            #{item.type},
            #{item.result},
            #{item.unit},
            #{item.range},
            #{item.status},
            #{item.instrumentId},
            #{item.instrumentName},
            #{item.instrumentResult},
            #{item.judge},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.isDelete},
            #{item.testDate},
            #{item.level}
            )
        </foreach>

    </insert>

    <delete id="physicsDeleteBatchIds">
        delete from tb_qc_sample_result where sample_result_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

</mapper>
