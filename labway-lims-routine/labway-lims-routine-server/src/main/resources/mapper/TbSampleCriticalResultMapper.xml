<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.routine.mapper.TbSampleCriticalResultMapper">

    <update id="updateByCriticalValueIds">
        update tb_sample_critical_result
        <set>
            <if test="criticalResult.status != null">
                status = #{criticalResult.status}
            </if>
        </set>
        where critical_value_id in
        <foreach collection="criticalValueIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
