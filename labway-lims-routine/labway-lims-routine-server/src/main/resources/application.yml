server:
  port: 12319
  tomcat:
    threads:
      max: 500

dubbo:
  provider:
    filter: loginUserProvider
  consumer:
    filter: loginUserConsumer

spring:
  datasource:
    driver-class-name: org.postgresql.Driver
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  profiles:
    active: dev
  shardingsphere:
    datasource:
      names: master
      master:
        driver-class-name: ${mysql.driver}
        jdbc-url: jdbc:postgresql://${mysql.master.host}:${mysql.master.port}/${mysql.db}
        password: ${mysql.password}
        type: ${mysql.datasource}
        username: ${mysql.username}
        minimum-idle: 5
    props:
      sql:
        show: false
    sharding:
      tables:
        tb_sample_result:
          actual-data-nodes: ms.tb_sample_result_$->{(2023..2025)}_$->{(1..12).collect{t -> t.toString().padLeft(2,'0')}},
          table-strategy:
            standard:
              sharding-column: sample_id
              precise-algorithm-class-name: com.labway.lims.routine.config.TableShardingAlgorithm
        tb_sample_report_item:
          actual-data-nodes: ms.tb_sample_report_item_$->{(2023..2025)}_$->{(1..12).collect{t -> t.toString().padLeft(2,'0')}},
          table-strategy:
            standard:
              sharding-column: sample_id
              precise-algorithm-class-name: com.labway.lims.routine.config.TableShardingAlgorithm
      default-data-source-name: master
      master-slave-rules:
        ms:
          master-data-source-name: master
          slave-data-source-names:
            - master
          load-balance-algorithm-type: round_robin

mysql:
  datasource: com.zaxxer.hikari.HikariDataSource
  db: labway-lims
  driver: org.postgresql.Driver


logging:
  level:
    "com.zaxxer.hikari.pool.ProxyConnection": error

combined-bill:
  reportItemTips:
    - reportGroupName: "葡萄糖"
      title: "糖耐量试验解释与建议title："
      tips: "糖耐量试验 tips"
    - reportGroupName: "胰岛素"
      title: "胰岛素释放试验解释与建议：title"
      tips: "胰岛素释放试验（ tips"
    - reportGroupName: "C肽"
      title: "C肽释放试验的解释与建议 title"
      tips: "C肽释放试验  tips"
  report:
    - reportGroupName: 葡萄糖
      typeName: 空腹葡萄糖测定
      reportCode: 8500910
      testTime: 0
      sort: 1
    - reportGroupName: 葡萄糖
      typeName: 餐后半小时葡萄糖测定
      reportCode: 2400025
      testTime: 30
      sort: 2
    - reportGroupName: 葡萄糖
      typeName: 餐后一小时葡萄糖测定
      reportCode: 1300390
      testTime: 60
      sort: 3
    - reportGroupName: 葡萄糖
      typeName: 餐后二小时葡萄糖测定
      reportCode: 2000560
      testTime: 120
      sort: 4
    - reportGroupName: 葡萄糖
      typeName: 餐后三小时葡萄糖测定
      reportCode: 8500112
      testTime: 180
      sort: 5

    - reportGroupName: 生长激素
      typeName: 生长激素30分钟测定
      reportCode: 7900090
      testTime: 30
      sort: 2
    - reportGroupName: 生长激素
      typeName: 生长激素60分钟测定
      reportCode: 3200490
      testTime: 60
      sort: 3