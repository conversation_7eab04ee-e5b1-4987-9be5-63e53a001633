package com.labway.lims.routine.service.chain.result.qc;

import com.labway.lims.api.enums.apply.SampleTypeEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.routine.service.chain.result.InstrumentReportReferenceCommand;
import com.labway.lims.routine.service.chain.result.SaveResultContext;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/4 10:01
 */
@Component
public class QCInstrumentReportReferenceCommand implements Command {

    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveQCResultContext context = SaveQCResultContext.from(c);

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();

        final List<InstrumentReportItemReferenceDto> refs =
                instrumentReportItemReferenceService.
                        selectByInstrumentReportItemId(instrumentReportItem.getInstrumentReportItemId());

        if (CollectionUtils.isEmpty(refs)) {
            return CONTINUE_PROCESSING;
        }

        final List<InstrumentReportItemReferenceDto> sampleRefs = refs.stream()
                .filter(e -> Objects.equals(e.getInstrumentId(), context.getInstrumentId()))
                .collect(Collectors.toList());

        //质控没有样本和申请单，new一个默认的申请单和样本去获取参考值范围
        final ApplyDto apply = new ApplyDto();
        apply.setHspOrgId(NumberUtils.LONG_ZERO);
        apply.setPatientSex(NumberUtils.INTEGER_ZERO);

        final ApplySampleDto applySample = new ApplySampleDto();
        applySample.setSampleTypeCode(SampleTypeEnum.ALL.getSampleTypeCode());

        InstrumentReportItemReferenceDto instrumentReportItemReferenceDto;

        instrumentReportItemReferenceDto =
                instrumentReportReferenceCommand.filterCustomerReportReference(apply, applySample, sampleRefs);
        //先取样本仪器上的参考范围
        if (Objects.isNull(instrumentReportItemReferenceDto)) {

            instrumentReportItemReferenceDto =
                    instrumentReportReferenceCommand.filterCustomerReportReference(apply, applySample, refs);
        }

        if (Objects.isNull(instrumentReportItemReferenceDto)) {
            return CONTINUE_PROCESSING;
        }

        context.put(SaveResultContext.INSTRUMENT_REPORT_REFERENCE, instrumentReportItemReferenceDto);

        return CONTINUE_PROCESSING;
    }
}
