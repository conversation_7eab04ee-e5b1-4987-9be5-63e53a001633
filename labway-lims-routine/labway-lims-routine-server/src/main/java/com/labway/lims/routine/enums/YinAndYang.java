package com.labway.lims.routine.enums;

/**
 * 阴阳性枚举
 */
public enum YinAndYang {


    YIN(0, "RESULT_YIN","阴"),
    YANG(1, "RESULT_YANG","阳");

    private Integer code;
    private String value;
    private String desc;


    // 根据编码获取枚举值
    public static YinAndYang getEnum(Integer code) {
        for (YinAndYang yinAndYang : YinAndYang.values()) {
            if (yinAndYang.getCode().equals(code)) {
                return yinAndYang;
            }
        }

        return null;
    }

    YinAndYang(Integer code, String value, String desc) {
        this.code = code;
        this.value = value;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }


}
