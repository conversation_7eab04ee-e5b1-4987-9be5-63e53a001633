package com.labway.lims.routine.service.chain.pick.two;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.ReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 填充信息
 */
@Slf4j
@Component
class TwoPickFillInfoCommand implements Filter, Command {
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentService instrumentService;

    @Override
    public boolean postprocess(Context c, Exception exception) {

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);
        final Long applySampleId = context.getApplySampleId();
        final Long instrumentGroupId = context.getInstrumentGroupId();

        final InstrumentGroupDto instrumentGroup = instrumentGroupService.selectByInstrumentGroupId(instrumentGroupId);
        if (Objects.isNull(instrumentGroup)) {
            throw new IllegalStateException("专业小组不存在");
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存,分拣失败");
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在,分拣失败");
        }

        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(applySampleId).stream()
                // 过滤掉非常规检验的项目
                .filter(e -> Objects.equals(e.getItemType(), ItemTypeEnum.ROUTINE.name()))
                // 过滤掉非当前专业组的项目
                .filter(e -> Objects.equals(e.getGroupId(), instrumentGroup.getGroupId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("当前样本下没有检验项目");
        }

        final List<ReportItemDto> reportItems = reportItemService.selectByTestItemIds(applySampleItems.stream()
                .map(ApplySampleItemDto::getTestItemId).collect(Collectors.toList()));
        final List<String> reportItemCodes = reportItems.stream().map(ReportItemDto::getReportItemCode)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reportItemCodes)) {
            throw new IllegalStateException("没有可以分拣的报告项目");
        }

        // 报告项目所属的检验项目
        final Map<String, Long> reportItemCode2TestItemIdMap = reportItems.stream().collect(Collectors.toMap(ReportItemDto::getReportItemCode,
                ReportItemDto::getTestItemId, (a, b) -> a));

        final List<InstrumentReportItemDto> insgReportItems;
        if (Objects.isNull(context.getInstrumentId()) || Objects.equals(context.getInstrumentId(), NumberUtils.LONG_ZERO)) {
            insgReportItems = instrumentReportItemService.selectByInstrumentGroupId(instrumentGroupId)
                    .stream().filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode())).collect(Collectors.toList());
        } else { //如果传进来的 instrumentId 不为空并且不为 0
            insgReportItems = instrumentReportItemService.selectByInstrumentId(context.getInstrumentId())
                    .stream().filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode())).collect(Collectors.toList());
        }

        final List<InstrumentReportItemDto> instrumentReportItems = insgReportItems
                .stream().filter(e -> CollectionUtils.containsAny(reportItemCodes, e.getReportItemCode()))
                .collect(Collectors.toList());

        //不传入仪器 ID 才去校验
        if (Objects.isNull(context.getInstrumentId()) || Objects.equals(context.getInstrumentId(), NumberUtils.LONG_ZERO)) {
            if (CollectionUtils.isEmpty(instrumentReportItems)) {
                throw new IllegalStateException(String.format("仪器没有绑定 [%s] 报告项目",
                        String.join("、", reportItemCodes)));
            }
        }

        Long instrumentId;
        if (Objects.isNull(context.getInstrumentId()) || Objects.equals(context.getInstrumentId(), NumberUtils.LONG_ZERO)) {
            instrumentId = instrumentReportItems.stream().collect(Collectors.groupingBy(InstrumentReportItemDto::getInstrumentId))
                    .entrySet().stream().max((o1, o2) -> NumberUtils.compare(o1.getValue().size(), o2.getValue().size()))
                    .map(Map.Entry::getKey).orElse(NumberUtils.LONG_ZERO);
        } else {
            instrumentId = context.getInstrumentId();
        }

        final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentId);
        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("仪器不存在");
        }


        context.put(TwoPickContext.APPLY, apply);
        context.put(TwoPickContext.APPLY_SAMPLE, applySample);
        context.put(TwoPickContext.INSTRUMENT_GROUP, instrumentGroup);
        context.put(TwoPickContext.INSTRUMENT, instrument);
        context.put(TwoPickContext.APPLY_SAMPLE_ITEMS, applySampleItems);
        context.put(TwoPickContext.REPORT_ITEMS, new ArrayList<>(reportItems.stream()
                .collect(Collectors.toMap(ReportItemDto::getReportItemCode, v -> v, (a, b) -> a)).values()));
        context.put(TwoPickContext.INSTRUMENT_REPORT_ITEMS, insgReportItems);
        context.put(TwoPickContext.REPORT_ITEM_CODE_2_TEST_ITEM_ID, reportItemCode2TestItemIdMap);

        return CONTINUE_PROCESSING;
    }
}
