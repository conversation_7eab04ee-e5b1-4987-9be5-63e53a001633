package com.labway.lims.routine.service.chain.auto.audit;

import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 自动审核条码环节记录
 *
 * <AUTHOR> on 2025/7/30.
 */
@Slf4j
@Component
public class AutoAuditSampleFlowCommand implements Command {

	@DubboReference
	private SampleFlowService sampleFlowService;

	@Override
	public boolean execute(Context c) throws Exception {
		AutoAuditContext context = AutoAuditContext.from(c);

		ApplySampleDto applySample = context.getSaveResultOriginContext().getApplySample();

		sampleFlowService.addSampleFlow(
				SampleFlowDto.builder().applyId(applySample.getApplyId()).applySampleId(applySample.getApplySampleId())
						.barcode(applySample.getBarcode()).operateCode(BarcodeFlowEnum.AUTO_AUDIT.name())
						.operateName(BarcodeFlowEnum.AUTO_AUDIT.getDesc())
						.content(String.format("自动审核 [%s]", context.isAutoAuditResultStatus() ? "成功" : "失败，原因："+ context.getAutoAuditErrorInfo()))
						.build());

		return CONTINUE_PROCESSING;
	}
}
