package com.labway.lims.routine.service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.routine.api.dto.*;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.routine.config.CombinedBillConfig;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.routine.api.dto.*;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.mapper.TbSampleReportItemMapper;
import com.labway.lims.routine.model.TbSampleReportItem;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023/3/29 17:35
 */
@Slf4j
@DubboService
public class SampleReportItemServiceImpl implements SampleReportItemService {
    @Resource
    private TbSampleReportItemMapper tbSampleReportItemMapper;
    @Resource
    private SampleReportItemService sampleReportItemService;
    /**  */
    @Resource
    private SampleService sampleService;
    @Resource
    private SampleResultService sampleResultService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;

    @DubboReference
    private SampleFlowService sampleFlowService;
    @Resource
    private CombinedBillConfig combinedBillConfig;

    @Nonnull
    @Override
    public List<SampleReportItemDto> selectBySampleId(long sampleId) {
        return tbSampleReportItemMapper
            .selectList(new LambdaQueryWrapper<TbSampleReportItem>().eq(TbSampleReportItem::getSampleId, sampleId))
            .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Set<Long> addSampleReportItems(List<SampleReportItemDto> sampleReportItems) {

        if (CollectionUtils.isEmpty(sampleReportItems)) {
            return Collections.emptySet();
        }

        final LoginUserHandler.User user = LoginUserHandler.get();
        final LinkedList<Long> ids = snowflakeService.genIds(sampleReportItems.size());
        final List<TbSampleReportItem> items =
            JSON.parseArray(JSON.toJSONString(sampleReportItems), TbSampleReportItem.class);
        for (TbSampleReportItem item : items) {
            item.setSampleReportItemId(ObjectUtils.defaultIfNull(item.getSampleReportItemId(), ids.pop()));
            item.setCreateDate(new Date());
            item.setCreatorId(user.getUserId());
            item.setCreatorName(user.getNickname());
            item.setUpdateDate(new Date());
            item.setUpdaterId(user.getUserId());
            item.setUpdaterName(user.getNickname());
            item.setIsDelete(YesOrNoEnum.NO.getCode());
        }

        tbSampleReportItemMapper.addBatch(items);

        log.info("用户 [{}] 新增申请单样本项目 {}", user.getNickname(), JSON.toJSONString(items));

        return new HashSet<>(ids);
    }

    @Override
    @Transactional
    public Set<Long> addSampleReportItems(List<SampleReportItemDto> sampleReportItems, String operator) {
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            return Collections.emptySet();
        }

        final LinkedList<Long> ids = snowflakeService.genIds(sampleReportItems.size());
        final List<TbSampleReportItem> items =
            JSON.parseArray(JSON.toJSONString(sampleReportItems), TbSampleReportItem.class);
        for (TbSampleReportItem item : items) {
            item.setSampleReportItemId(ObjectUtils.defaultIfNull(item.getSampleReportItemId(), ids.pop()));
            item.setCreateDate(new Date());
            item.setCreatorId(0L);
            item.setCreatorName(operator);
            item.setUpdateDate(new Date());
            item.setUpdaterId(0L);
            item.setUpdaterName(operator);
            item.setIsDelete(YesOrNoEnum.NO.getCode());
        }

        tbSampleReportItemMapper.addBatch(items);

        log.info("用户 [{}] 新增申请单样本项目 {}", operator, JSON.toJSONString(items));

        return new HashSet<>(ids);
    }

    @Override
    public long addSampleReportItem(SampleReportItemDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        TbSampleReportItem item = JSON.parseObject(JSON.toJSONString(dto), TbSampleReportItem.class);
        item.setSampleReportItemId(ObjectUtils.defaultIfNull(item.getSampleReportItemId(), snowflakeService.genId()));
        item.setCreateDate(new Date());
        item.setCreatorId(user.getUserId());
        item.setCreatorName(user.getNickname());
        item.setUpdateDate(new Date());
        item.setUpdaterId(user.getUserId());
        item.setUpdaterName(user.getNickname());
        item.setIsDelete(YesOrNoEnum.NO.getCode());
        if (tbSampleReportItemMapper.insert(item) < 1) {
            throw new IllegalStateException("添加失败");
        }

        log.info("用户 [{}] 添加样本报告项目成功 [{}]", user.getNickname(), JSON.toJSONString(item));

        return item.getSampleReportItemId();
    }

    @Override
    public List<SampleReportItemDto> selectBySampleIds(Collection<Long> sampleIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(sampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSampleReportItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSampleReportItem::getSampleId, sampleIds);
        queryWrapper.eq(TbSampleReportItem::getIsDelete, YesOrNoEnum.NO.getCode());
        return convert(tbSampleReportItemMapper.selectList(queryWrapper));

    }

    @Override
    @Nullable
    public SampleReportItemDto selectBySampleIdAndReportItemCode(long sampleId, String reportItemCode) {
        LambdaQueryWrapper<TbSampleReportItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSampleReportItem::getSampleId, sampleId);
        queryWrapper.eq(TbSampleReportItem::getReportItemCode, reportItemCode);
        queryWrapper.eq(TbSampleReportItem::getIsDelete, YesOrNoEnum.NO.getCode());
        return convert(tbSampleReportItemMapper.selectOne(queryWrapper.last("limit 1")));
    }

    @Nullable
    @Override
    public SampleReportItemDto selectBySampleReportItemId(long sampleReportItemId, long sampleId) {
        LambdaQueryWrapper<TbSampleReportItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TbSampleReportItem::getSampleReportItemId, sampleReportItemId)
            .eq(TbSampleReportItem::getSampleId, sampleId)
            .eq(TbSampleReportItem::getIsDelete, YesOrNoEnum.NO.getCode());

        return convert(tbSampleReportItemMapper.selectOne(wrapper.last("limit 1")));
    }

    @Override
    public boolean updateBySampleReportItemId(SampleReportItemDto dto) {
        final TbSampleReportItem item = JSON.parseObject(JSON.toJSONString(dto), TbSampleReportItem.class);
        item.setUpdateDate(new Date());
        item.setUpdaterId(LoginUserHandler.get().getUserId());
        item.setUpdaterName(LoginUserHandler.get().getNickname());

        if (tbSampleReportItemMapper.update(item,
            new LambdaQueryWrapper<TbSampleReportItem>()
                .eq(TbSampleReportItem::getSampleReportItemId, dto.getSampleReportItemId())
                .eq(TbSampleReportItem::getSampleId, dto.getSampleId())) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改样本报告项目 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(item));

        return true;
    }

    private SampleReportItemDto convert(TbSampleReportItem item) {
        if (Objects.isNull(item)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(item), SampleReportItemDto.class);
    }

    /**
     * TbSampleReportItem 转换 为 SampleReportItemDto
     *
     * @param list TbSampleReportItem
     * @return SampleReportItemDto
     */
    private List<SampleReportItemDto> convert(List<TbSampleReportItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public void deleteBySampleId(long sampleId) {
        final LambdaQueryWrapper<TbSampleReportItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleReportItem::getSampleId, sampleId);

        tbSampleReportItemMapper.delete(wrapper);

        log.info("用户 [{}] 删除样本报告项目成功 [{}] 成功", LoginUserHandler.get().getNickname(), sampleId);
    }

    @Override
    @Transactional
    public void deleteBySampleId(long sampleId, String operator) {
        final LambdaQueryWrapper<TbSampleReportItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleReportItem::getSampleId, sampleId);

        tbSampleReportItemMapper.delete(wrapper);

        log.info("用户 [{}] 删除样本报告项目成功 [{}] 成功", operator, sampleId);
    }

    @Override
    public void deleteBySampleIds(Collection<Long> sampleIds) {
        final LambdaQueryWrapper<TbSampleReportItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbSampleReportItem::getSampleId, sampleIds);

        tbSampleReportItemMapper.delete(wrapper);

        log.info("用户 [{}] 删除样本报告项目成功 [{}] 成功", LoginUserHandler.get().getNickname(), sampleIds);
    }

    @Override
    public void deleteBySampleReportItemId(long sampleReportItemId, long sampleId) {

        tbSampleReportItemMapper.delete(new LambdaQueryWrapper<TbSampleReportItem>()
            .eq(TbSampleReportItem::getSampleReportItemId, sampleReportItemId)
            .eq(TbSampleReportItem::getSampleId, sampleId));

        log.info("用户 [{}] 删除申请单项目 [{}] 成功", LoginUserHandler.get().getNickname(), sampleReportItemId);

    }

    @Override
    public void deleteBySampleReportItemIds(Collection<Long> sampleReportItemIds, long sampleId) {
        if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(sampleReportItemIds)){
            tbSampleReportItemMapper.delete(new LambdaQueryWrapper<TbSampleReportItem>()
                    .in(TbSampleReportItem::getSampleReportItemId, sampleReportItemIds)
                    .eq(TbSampleReportItem::getSampleId, sampleId));

            log.info("用户 [{}] 删除申请单项目 [{}] 成功", LoginUserHandler.get().getNickname(), sampleReportItemIds);

        }
    }

    @Override
    public void updateBySampleReportItemIds(SampleReportItemDto sampleReportItem,
        Collection<Long> sampleReportItemIds) {
        if (CollectionUtils.isEmpty(sampleReportItemIds)) {
            return;
        }

        tbSampleReportItemMapper.updateBySampleReportItemIds(sampleReportItem, sampleReportItemIds);

        log.info("用户 [{}] 修改样本报告项目 [{}] 成功 id {}", LoginUserHandler.get().getNickname(),
            JSON.toJSONString(sampleReportItem), sampleReportItemIds);

    }

    @Override
    public List<SampleReportItemDto> selectByTestItemIds(Map<Long, List<Long>> testItemIdsBySampleId) {

        if (testItemIdsBySampleId.isEmpty()) {
            return Collections.emptyList();
        }

        Set<Long> sampleIds = testItemIdsBySampleId.keySet();

        // 查询样本报告项目
        List<TbSampleReportItem> tbSampleReportItems = tbSampleReportItemMapper.selectList(new LambdaQueryWrapper<TbSampleReportItem>()
                .in(TbSampleReportItem::getSampleId, sampleIds));
        if (CollectionUtils.isEmpty(tbSampleReportItems)) {
            return Collections.emptyList();
        }

        // 过滤符合条件的报告项目
        tbSampleReportItems = tbSampleReportItems.stream().filter(e -> {
            List<Long> reportItemIds = testItemIdsBySampleId.get(e.getSampleId());
            return (reportItemIds != null && reportItemIds.contains(e.getTestItemId()));
        }).collect(Collectors.toList());

        return tbSampleReportItems.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByTestItemIds(Map<Long, List<Long>> testItemIdsByApplySampleId) {
        if (testItemIdsByApplySampleId.isEmpty()) {
            return;
        }

        for (Map.Entry<Long, List<Long>> entry : testItemIdsByApplySampleId.entrySet()) {
            Long sampleId = entry.getKey();
            List<Long> testItemIds = entry.getValue();
            tbSampleReportItemMapper.delete(new LambdaQueryWrapper<TbSampleReportItem>()
                .eq(TbSampleReportItem::getSampleId, sampleId).in(TbSampleReportItem::getTestItemId, testItemIds));

        }
    }

    @Override
    public List<SampleReportItemDto> selectBySampleIdsAndReportCodes(List<SampleReportItemDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) return Collections.emptyList();

        List<Long> sampleIds = dtos.stream().map(SampleReportItemDto::getSampleId).collect(Collectors.toList());

        Map<Long, List<SampleReportItemDto>> sampleIdMap = dtos.stream().collect(Collectors.groupingBy(SampleReportItemDto::getSampleId));

        LambdaQueryWrapper<TbSampleReportItem> wrapper = Wrappers.lambdaQuery(TbSampleReportItem.class)
                .in(TbSampleReportItem::getSampleId, sampleIds);

        List<TbSampleReportItem> tbSampleReportItems = tbSampleReportItemMapper.selectList(wrapper);

        return tbSampleReportItems.stream().filter(e -> {
            Long sampleId = e.getSampleId();
            List<SampleReportItemDto> sampleReportItemDtos = sampleIdMap.get(sampleId);
            List<String> reportCodes = sampleReportItemDtos.stream().map(SampleReportItemDto::getReportItemCode).collect(Collectors.toList());

            return reportCodes.contains(e.getReportItemCode());
        }).map(this::convert).collect(Collectors.toList());


//        return tbSampleReportItemMapper.selectBySampleIdsAndReportIds(dtos).stream().map(this::convert).collect(Collectors.toList());
    }




    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean combinedBill(CommitCombinedBillDTO dto) {
        // 查询申请单样本
        ApplySampleDto applySampleDto = applySampleService.selectByBarcodeAndGroupId(dto.getBarcode(), LoginUserHandler.get().getGroupId());
        Assert.notNull(applySampleDto,"合并后的条码在本专业组不存在");
        // 查询样本id
        SampleDto sampleDto = sampleService.selectBySampleId(dto.getSampleId());
        Assert.notNull(sampleDto,"样本不存在");

        // 过滤主条码的信息
        List<SampleReportItemPatDto> sampleReportItemPatDtos = dto.getSampleReportItemPatDtos()
                .stream()
                .filter(e -> e.getBarcode() != null && !e.getBarcode().equals(dto.getBarcode()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(sampleReportItemPatDtos)){
            throw new IllegalArgumentException("单个条码无法并单");
        }

        LoginUserHandler.User user = LoginUserHandler.get();

        Set<String> barCodes = sampleReportItemPatDtos.stream().map(SampleReportItemPatDto::getBarcode).collect(Collectors.toSet());
        log.info("{}合并条码， 条码{}，被合并的条码{}", user.getNickname(), dto.getBarcode(), barCodes);

        // 被删除的主键
        Set<Long> mergeSampleIds = new HashSet<>();
        Set<Long> mergeApplySampleIds = new HashSet<>();

        // 删除被合并的样本报告项目
        for (SampleReportItemPatDto sampleReportItemPatDto : sampleReportItemPatDtos) {
            // 默认都被删除了没有子数据
            mergeSampleIds.add(sampleReportItemPatDto.getSampleId());
            mergeApplySampleIds.add(sampleReportItemPatDto.getApplySampleId());
        }


        CombinedBillAddIdDto combinedBillAddIdDto = StringUtils.isBlank(applySampleDto.getMergeExtraInfo())?
                new CombinedBillAddIdDto() :
                JSON.parseObject(applySampleDto.getMergeExtraInfo(), CombinedBillAddIdDto.class);

        List<SampleFlowDto> sampleFlowDtos = new ArrayList<>();

        // 删除样本结果和样本报告项目
        List<SampleResultDto> sampleResultDtos = sampleResultService.selectBySampleIds(mergeSampleIds);
//        sampleResultService.deleteBySampleIds(delete_sampleIds);
//        combinedBillAddIdDto.setDeleteSampleResultIdAndSampleIds(sampleResultDtos.stream().map(SampleResultDto::getSampleResultId).collect(Collectors.toSet()));
//
        List<SampleReportItemDto> sampleReportItemDtos = sampleReportItemService.selectBySampleIds(mergeSampleIds);
//        sampleReportItemService.deleteBySampleIds(delete_sampleIds);
//        combinedBillAddIdDto.setDeleteSampleReportItemIdAndSampleIds(sampleReportItemDtos.stream().map(SampleReportItemDto::getSampleReportItemId).collect(Collectors.toSet()));

        // 删除样本
        List<SampleDto> sampleDtos = sampleService.selectBySampleIds(mergeSampleIds);
        Map<String, List<SampleDto>> sampleBarCodeMap = sampleDtos.stream().collect(Collectors.groupingBy(SampleDto::getBarcode));

//        sampleService.deleteBySampleIds(delete_sampleIds);
//        combinedBillAddIdDto.setDeleteSampleIds(sampleDtos.stream().map(SampleDto::getSampleId).collect(Collectors.toSet()));

        // 删除申请单样本
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(mergeApplySampleIds);
        Map<String, List<ApplySampleDto>> applySampleBarCodeMap = applySampleDtos.stream().collect(Collectors.groupingBy(ApplySampleDto::getBarcode));
        SampleFlowDto sampleFlow;
        for (ApplySampleDto applySampleDto1 : applySampleDtos) {
             sampleFlow = new SampleFlowDto();
            sampleFlow.setApplyId(applySampleDto1.getApplyId());
            sampleFlow.setApplySampleId(applySampleDto1.getApplySampleId());
            sampleFlow.setBarcode(applySampleDto1.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.COMBINED_BILL.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.COMBINED_BILL.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(String.format("检验结果被并入条码号【%s】 样本号【%s】 专业组【%s】", sampleDto.getBarcode(), sampleDto.getSampleNo(), sampleDto.getGroupName()));
            sampleFlowDtos.add(sampleFlow);
        }

//        applySampleService.deleteByApplySampleIds(delete_applySampleIds);
//        combinedBillAddIdDto.setDeleteApplySampleIds(applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));

        // 删除 被合并的 申请单 信息
//        List<ApplyDto> applyDtos = applyService.selectByApplyIds(delete_applyIds);
//        Set<Long> final_delete_applyIds = delete_applyIds;
//        delete_applyIds = applyDtos.stream().map(ApplyDto::getApplyId).filter(applyId ->!final_delete_applyIds.contains(applyId)).collect(Collectors.toSet());
//        applyService.deleteByApplyIds(delete_applyIds);
//        combinedBillAddIdDto.setDeleteApplyIds(applyDtos.stream().map(ApplyDto::getApplyId).collect(Collectors.toSet()));

        // 样本结果
//        Map<Long, List<SampleResultDto>> sampleResultDtoMap = sampleResultDtos.stream().collect(Collectors.groupingBy(SampleResultDto::getSampleId));

        Map<Long, Long> sampleResultIdOldMap = new HashMap<>();
        for (SampleResultDto sampleResultDto : sampleResultDtos) {
            long newId = snowflakeService.genId();
            sampleResultIdOldMap.put(sampleResultDto.getSampleResultId(), newId);
            sampleResultDto.setSampleResultId(newId);
            sampleResultDto.setSampleId(sampleDto.getSampleId());
            sampleResultDto.setApplyId(sampleDto.getApplyId());
            sampleResultDto.setApplySampleId(sampleDto.getApplySampleId());
        }

        Map<Long, Long> sampleReportIdOldMap = new HashMap<>();
        List<Long> sampleReportItemIds = sampleReportItemDtos.stream().map(SampleReportItemDto::getSampleReportItemId).collect(Collectors.toList());
        // 样本报告项目
//        Map<Long, List<SampleReportItemDto>> sampleReportItemDtoMap = sampleReportItemDtos.stream().collect(Collectors.groupingBy(SampleReportItemDto::getSampleId));
        for (SampleReportItemDto sampleReportItemDto : sampleReportItemDtos) {
            long newId = snowflakeService.genId();
            sampleReportIdOldMap.put(sampleReportItemDto.getSampleReportItemId(), newId);
            sampleReportItemDto.setSampleReportItemId(newId);
            sampleReportItemDto.setSampleId(sampleDto.getSampleId());
            sampleReportItemDto.setApplyId(sampleDto.getApplyId());
            sampleReportItemDto.setApplySampleId(sampleDto.getApplySampleId());
        }

        combinedBillAddIdDto.addSampleResultOldIdAndNewIdMap(sampleResultIdOldMap);
        combinedBillAddIdDto.addSampleReportOldIdAndNewIdMap(sampleReportIdOldMap);

        log.info("本次合并补充的信息添加的数据：sampleResultIdOldIdAndNewIdMap:{}，sampleReportOldIdAndNewIdMap:{}", sampleResultIdOldMap, sampleReportIdOldMap);

        // 新增数据
        sampleReportItemService.addSampleReportItems(sampleReportItemDtos);
        sampleResultService.addSampleResults(sampleResultDtos);

        // 更改applySample的补充信息
        applySampleService.updateExtraInfoByApplySampleId(applySampleDto.getApplySampleId(), JSON.toJSONString(combinedBillAddIdDto));


        sampleFlow = new SampleFlowDto();
        sampleFlow.setApplyId(applySampleDto.getApplyId());
        sampleFlow.setApplySampleId(applySampleDto.getApplySampleId());
        sampleFlow.setBarcode(applySampleDto.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.MASTER_COMBINED_BILL.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.MASTER_COMBINED_BILL.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        StringJoiner sj = new StringJoiner(" ");
        sj.add("并入");
        for (Map.Entry<String, List<ApplySampleDto>> barcodeEntry : applySampleBarCodeMap.entrySet()) {
            Set<String> sampleNos = sampleBarCodeMap.get(barcodeEntry.getKey()).stream().map(SampleDto::getSampleNo).collect(Collectors.toSet());
            sj.add(String.format("条码号【%s】 样本号【%s】", barcodeEntry.getKey(), CollUtil.join(sampleNos, ",")));
        }
        sampleFlow.setContent(sj.toString());
        sampleFlowDtos.add(sampleFlow);
        sampleFlowService.addSampleFlows(sampleFlowDtos);


        // 把被合并的中止检验
        List<Long> applySampleIds = applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());
        List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplySampleIds(applySampleIds);
        Set<Long> applySampleItemIds = applySampleItemDtos.stream().map(ApplySampleItemDto::getApplySampleItemId).collect(Collectors.toSet());

        TerminateItemDto terminateItemDto = new TerminateItemDto();
        terminateItemDto.setTerminateType(StopTestStatus.STOP_TEST_CHARGE);
        terminateItemDto.setCauseCode(BarcodeFlowEnum.MASTER_COMBINED_BILL.name());
        terminateItemDto.setCause(BarcodeFlowEnum.MASTER_COMBINED_BILL.getDesc());
        terminateItemDto.setApplySampleItemIds(applySampleItemIds);
        applySampleService.terminateItem(terminateItemDto);

        applySampleService.updateBatchByIds(mergeApplySampleIds, dto.getBarcode());

        // 中止检验后会把样本报告项目状态改为已删除， 所以要恢复  并删除redis
        List<Long> sampleIds = sampleDtos.stream().map(SampleDto::getSampleId).collect(Collectors.toList());
        sampleReportItemService.updateIsDeleteBySampleReportItemIds(sampleReportItemIds, sampleIds);

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateIsDeleteBySampleReportItemIds(Collection<Long> sampleReportItemIds, Collection<Long> sampleIds) {
        for (Long sampleId : sampleIds) {
            final Date date = new Date(snowflakeService.getGenerateDateTime(sampleId));
            final String suffix = String.format("%s_%s", DateUtil.year(date),
                    StringUtils.leftPad(String.valueOf(DateUtil.month(date) + 1), 2, '0'));
            tbSampleReportItemMapper.updateIsDeleteBySampleReportItemIds(sampleReportItemIds, suffix);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean cancelCombinedBill(BarCodeDto barCodeDto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        Long groupId = user.getGroupId();
        ApplySampleDto applySampleDto = applySampleService.selectByBarcodeAndGroupId(barCodeDto.getMasterBarCode(), groupId);
        Assert.notNull(applySampleDto,"合并后的条码在本专业组不存在");
        if(Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.AUDIT.getCode())){
            throw new IllegalArgumentException("主条码已二审无法取消并单");
        }
        SampleDto sampleDto = sampleService.selectByApplySampleId(applySampleDto.getApplySampleId());
        Assert.notNull(sampleDto,"合并后的条码样本不存在");

        List<ApplySampleDto> applySampleDtos = applySampleService.selectByBarcodes(barCodeDto.getBarCodes()).stream()
                .filter(e -> e.getGroupId().equals(groupId) &&
                        StringUtils.isNotBlank(e.getMergeMasterBarcode()) &&
                        e.getMergeMasterBarcode().equals(barCodeDto.getMasterBarCode()))
                .collect(Collectors.toList());
        Assert.notEmpty(applySampleDtos,"子条码数据不存在");

        // 恢复子条码数据
        List<Long> applySampleIds = applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());
        applySampleService.updateOneAuditStatusByApplySampleIds(applySampleIds);
        applySampleItemService.updateStopStatusByApplySampleId(applySampleIds);

        // 过滤主条码数据， 删除多余的主条码数据
        CombinedBillAddIdDto combinedBillAddIdDto = JSON.parseObject(applySampleDto.getMergeExtraInfo(), CombinedBillAddIdDto.class);
        Map<Long, Long> sampleReportIdMap = combinedBillAddIdDto.getSampleReportOldIdAndNewIdMap();
        Map<Long, Long> sampleResultIdIdMap = combinedBillAddIdDto.getSampleResultOldIdAndNewIdMap();

        List<SampleDto> sampleDtos = sampleService.selectByApplySampleIds(applySampleIds);
        Map<String, SampleDto> sampleDtoMap = sampleDtos.stream().collect(Collectors.toMap(SampleDto::getBarcode, Function.identity(), (a,b)->b));
        List<Long> sampleIds = sampleDtos.stream().map(SampleDto::getSampleId).collect(Collectors.toList());

        Map<Long, Long> newSampleReportItemIdMap = sampleReportItemService.selectBySampleIds(sampleIds).stream()
                .filter(e -> sampleReportIdMap.get(e.getSampleReportItemId()) != null)
//                .map(e-> sampleReportIdMap.get(e.getSampleReportItemId()))
                .collect(Collectors.toMap(SampleReportItemDto::getSampleReportItemId, e->sampleReportIdMap.get(e.getSampleReportItemId())));

        Map<Long, Long> newSampleResultIdMap = sampleResultService.selectBySampleIds(sampleIds).stream()
                .filter(e -> sampleResultIdIdMap.get(e.getSampleResultId()) != null)
//                .map(e -> sampleResultIdIdMap.get(e.getSampleResultId()))
                .collect(Collectors.toMap(SampleResultDto::getSampleResultId, e->sampleResultIdIdMap.get(e.getSampleResultId())));

        sampleReportItemService.deleteBySampleReportItemIds(newSampleReportItemIdMap.values(), sampleDto.getSampleId());
        sampleResultService.deleteBySampleResultIds(newSampleResultIdMap.values(), sampleDto.getSampleId());

        sampleReportIdMap.keySet().removeAll(newSampleReportItemIdMap.keySet());
        sampleResultIdIdMap.keySet().removeAll(newSampleResultIdMap.keySet());
        combinedBillAddIdDto.setSampleReportOldIdAndNewIdMap(sampleReportIdMap);
        combinedBillAddIdDto.setSampleResultOldIdAndNewIdMap(sampleResultIdIdMap);
        // 更改applySample的补充信息
        applySampleService.updateExtraInfoByApplySampleId(applySampleDto.getApplySampleId(),
                CollectionUtils.isEmpty(sampleReportIdMap) && CollectionUtils.isEmpty(sampleResultIdIdMap) ? Strings.EMPTY : JSON.toJSONString(combinedBillAddIdDto)
                );
        applySampleIds.forEach(applySampleService::regainTerminate);

        List<SampleFlowDto> sampleFlowDtos = new ArrayList<>();

        SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setApplyId(applySampleDto.getApplyId());
        sampleFlow.setApplySampleId(applySampleDto.getApplySampleId());
        sampleFlow.setBarcode(applySampleDto.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.CANCEL_COMBINED_BILL.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.CANCEL_COMBINED_BILL.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        Set<String> barcodes = applySampleDtos.stream().map(ApplySampleDto::getBarcode).collect(Collectors.toSet());
        sampleFlow.setContent(String.format("条码【%s】取消并单", CollUtil.join(barcodes, ",")));
        sampleFlowDtos.add(sampleFlow);

        for (ApplySampleDto dto : applySampleDtos) {
            sampleFlow = new SampleFlowDto();
            sampleFlow.setApplyId(dto.getApplyId());
            sampleFlow.setApplySampleId(dto.getApplySampleId());
            sampleFlow.setBarcode(dto.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.CANCEL_COMBINED_BILL.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.CANCEL_COMBINED_BILL.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(String.format("条码【%s】, 样本号【%s】 取消并单", dto.getBarcode(), sampleDtoMap.getOrDefault(dto.getBarcode(), new SampleDto()).getSampleNo()));
            sampleFlowDtos.add(sampleFlow);
        }

        sampleFlowService.addSampleFlows(sampleFlowDtos);

        return true;
    }

    @Override
    public Map<String, CombinedBillDTO> selectReportCodeMap() {
        return combinedBillConfig.getReportCodeMap();
    }
}
