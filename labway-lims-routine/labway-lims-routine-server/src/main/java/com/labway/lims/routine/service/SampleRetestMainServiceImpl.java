package com.labway.lims.routine.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.*;
import com.labway.lims.routine.api.service.*;
import com.labway.lims.routine.mapper.TbSampleRetestItemMapper;
import com.labway.lims.routine.mapper.TbSampleRetestMainMapper;
import com.labway.lims.routine.model.TbSampleRetestMain;
import com.labway.lims.routine.service.chain.result.UpdateMissItemCommand;
import com.labway.lims.routine.service.chain.retest.StartRetestChain;
import com.labway.lims.routine.service.chain.retest.StartRetestCheckSaveOriginalCommand;
import com.labway.lims.routine.service.chain.retest.StartRetestContext;
import com.labway.lims.routine.service.chain.retest.StartRetestUpdateMissItemCommand;
import com.labway.lims.routine.service.chain.retest.cancel.CancelRetestChain;
import com.labway.lims.routine.service.chain.retest.cancel.CancelRetestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2023/4/17 13:03
 */
@Slf4j
@DubboService
public class SampleRetestMainServiceImpl implements SampleRetestMainService {
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private CancelRetestChain cancelRetestChain;
    @Resource
    private SampleServiceImpl sampleService;
    @Resource
    private UpdateMissItemCommand updateMissItemCommand;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private SampleReportItemService sampleReportItemService;
    @Resource
    private SampleResultService sampleResultService;
    @Resource
    @Deprecated
    private TbSampleRetestMainMapper tbSampleRetestMainMapper;
    @Resource
    @Deprecated
    private TbSampleRetestItemMapper tbSampleRetestItemMapper;
    @Resource
    private SampleRetestMainService sampleRetestMainService;
    @Resource
    private StartRetestUpdateMissItemCommand startRetestUpdateMissItemCommand;
    @Resource
    private SampleRetestItemService sampleRetestItemService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private StartRetestChain startRetestChain;
    @Resource
    private StartRetestCheckSaveOriginalCommand startRetestCheckSaveOriginalCommand;
    @Resource
    private SampleCriticalResultService sampleCriticalResultService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startRetest(StartReTestDto dto) {
        if (Objects.isNull(dto.getSampleId())) {
            return;
        }

        final StartRetestContext context = new StartRetestContext();
        context.put(StartRetestContext.RETESET_INFO,dto);

        try {
            if (!startRetestChain.execute(context)) {
                throw new IllegalStateException("开始复查失败");
            }

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("开始复查 [{}] 时\n{}", dto.getSampleId(), context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }


    @Override
    public void cancelRetest(long sampleId) {
        final CancelRetestContext context = new CancelRetestContext();
        context.setSampleId(sampleId);

        try {
            if (!cancelRetestChain.execute(context)) {
                throw new IllegalStateException("取消复查失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelRetest(long sampleId, String reportItemCode) {
        final CancelRetestContext context = new CancelRetestContext();
        context.setSampleId(sampleId);
        context.setReportItemCode(reportItemCode);

        try {
            if (!cancelRetestChain.execute(context)) {
                throw new IllegalStateException("取消复查失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    private void retestSampleFlow(SampleDto sample, List<SampleReportItemDto> sampleReportItems) {
        final SampleFlowDto dto = new SampleFlowDto();
        final LoginUserHandler.User user = LoginUserHandler.get();
        final String cancelRetestNames = sampleReportItems.stream().map(SampleReportItemDto::getReportItemName).collect(Collectors.joining(","));
        dto.setSampleFlowId(snowflakeService.genId());
        dto.setApplyId(sample.getApplyId());
        dto.setApplySampleId(sample.getApplySampleId());
        dto.setBarcode(sample.getBarcode());
        dto.setOperateCode(BarcodeFlowEnum.CANCEL_RETEST_RESULT.name());
        dto.setOperateName(BarcodeFlowEnum.CANCEL_RETEST_RESULT.getDesc());
        dto.setOperatorId(LoginUserHandler.get().getUserId());
        dto.setOperator(LoginUserHandler.get().getNickname());
        dto.setContent(String.format("报告项目 [%s] 取消复查", cancelRetestNames));
        dto.setCreateDate(new Date());
        dto.setCreatorId(user.getUserId());
        dto.setCreatorName(user.getNickname());
        dto.setUpdaterName(user.getNickname());
        dto.setUpdaterId(user.getUserId());
        dto.setUpdateDate(new Date());
        dto.setOrgId(user.getOrgId());
        dto.setIsDelete(YesOrNoEnum.NO.getCode());
        dto.setOrgName(user.getOrgName());
        sampleFlowService.addSampleFlow(dto);
    }


    @Override
    public List<SampleRetestMainDto> selectBySampleId(long sampleId) {

        final LambdaQueryWrapper<TbSampleRetestMain> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleRetestMain::getSampleId, sampleId);

        return tbSampleRetestMainMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public CountRetestResultDto countRetestResultInfo(long sampleId) {
        final SampleRetestMainDto mainDto = selectBySampleId(sampleId)
                .stream()
                .filter(e -> Objects.equals(e.getStatus(), RetestStatusEnum.RETESTING.getCode()))
                .findFirst()
                .orElse(null);
        final CountRetestResultDto dto = new CountRetestResultDto();
        if (Objects.isNull(mainDto)) {
            return dto;
        }
        final Long sampleRetestMainId = mainDto.getSampleRetestMainId();
        final List<SampleRetestItemDto> retestItems = sampleRetestItemService.selectBySampleIds(Collections.singleton(sampleRetestMainId));

        if (CollectionUtils.isEmpty(retestItems)) {
            return dto;
        }

        final long retestingCount = retestItems.stream().map(SampleRetestItemDto::getResult).filter(StringUtils::isBlank).count();

        dto.setRetesting(Math.toIntExact(retestingCount));
        dto.setRetested(retestItems.size() - Math.toIntExact(retestingCount));
        return dto;

    }


    @Override
    public boolean updateBySampleRetestMainId(SampleRetestMainDto dto) {

        final TbSampleRetestMain main = JSON.parseObject(JSON.toJSONString(dto), TbSampleRetestMain.class);

        main.setUpdateDate(new Date());
        main.setUpdaterId(LoginUserHandler.get().getUserId());
        main.setUpdaterName(LoginUserHandler.get().getNickname());

        if (tbSampleRetestMainMapper.updateById(main) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改申请单样本 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(main));

        return true;
    }

    @Override
    public List<RetestRecordDto> selectRetestRecord(long sampleId) {

        final List<SampleReportItemDto> sampleReportItems = sampleReportItemService.selectBySampleId(sampleId);

        //如果有一个报告项目是在复查中那么不显示复查结果
        if (sampleReportItems.stream().anyMatch(e -> Objects.equals(e.getIsRetest(), RetestStatusEnum.RETESTING.getCode()))) {
            return Collections.emptyList();
        }
        //样本的报告项目都没有进行过复查
        if (sampleReportItems.stream().allMatch(e -> Objects.equals(e.getIsRetest(), RetestStatusEnum.NORMAL.getCode()))) {
            return Collections.emptyList();
        }

        final List<SampleResultDto> results = sampleResultService.selectBySampleId(sampleId);

        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }
        // 复查信息
        final Set<Long> reportItemIds = results.stream().map(SampleResultDto::getReportItemId).collect(Collectors.toSet());
        final List<SampleRetestItemDto> retestResultItems = sampleRetestItemService.selectBySampleIds(Collections.singleton(sampleId)).stream().filter(e -> reportItemIds.contains(e.getReportItemId())).collect(Collectors.toList());
        final Map<Long, List<SampleRetestItemDto>> retestResultItemMap = retestResultItems.stream().collect(Collectors.groupingBy(SampleRetestItemDto::getReportItemId));

        return results.stream().map(m -> {
            RetestRecordDto dto = new RetestRecordDto();
            dto.setStatus(m.getStatus());
            dto.setTestJudge(m.getJudge());
            dto.setOriginalResult(m.getResult());
            dto.setReportItemName(m.getReportItemName());

            // 复查信息
            final List<SampleRetestItemDto> retestItems = retestResultItemMap.get(m.getReportItemId());
            if (CollectionUtils.isEmpty(retestItems)) {
                return dto;
            }

            final List<SampleRetestItemDto> hisResults = retestItems.stream()
                    .filter(e -> Objects.equals(e.getReportItemId(), m.getReportItemId()))
                    .collect(Collectors.toList());
            LinkedList<RetestRecordDto.RetestResult> retestResults = new LinkedList<>();

            for (SampleRetestItemDto hisResult : hisResults) {
                RetestRecordDto.RetestResult result = new RetestRecordDto.RetestResult();
                result.setResult(hisResult.getResult());
                result.setSampleRetestItemId(hisResult.getSampleRetestItemId());
                result.setSampleRetestMainId(hisResult.getSampleRetestMainId());
                result.setStatus(hisResult.getStatus());
                result.setTestJudge(hisResult.getJudge());
                retestResults.add(result);
            }
            dto.setOperator(retestItems.get(0).getRetesterName());
            dto.setHistoryResult(retestResults);

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SampleRetestMainDto> selectBySampleIds(Collection<Long> sampleIds) {
        final LambdaQueryWrapper<TbSampleRetestMain> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbSampleRetestMain::getSampleId, sampleIds);

        return tbSampleRetestMainMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public long addSampleRetestMain(SampleRetestMainDto main) {
        if (tbSampleRetestMainMapper.insert(convert(main)) < 1) {
            throw new IllegalStateException("添加复查主表失败");
        }
        return main.getSampleRetestMainId();
    }

    @Override
    public void deleteBySampleRetestMainId(long sampleRetestMainId) {
        tbSampleRetestMainMapper.deleteById(sampleRetestMainId);
    }


    public SampleRetestMainDto convert(TbSampleRetestMain main) {
        if (Objects.isNull(main)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(main), SampleRetestMainDto.class);
    }


    public TbSampleRetestMain convert(SampleRetestMainDto main) {
        if (Objects.isNull(main)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(main), TbSampleRetestMain.class);
    }
}
