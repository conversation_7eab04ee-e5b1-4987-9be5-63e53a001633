package com.labway.lims.routine.service.chain.retest.cancel;

import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 补上下文
 */
@Slf4j
@Component
class CancelRetestFillCommand implements Command {
    @Resource
    private SampleRetestMainService sampleRetestMainService;
    @Resource
    private SampleReportItemService sampleReportItemService;
    @Resource
    private SampleRetestItemService sampleRetestItemService;
    @Resource
    private SampleService sampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final CancelRetestContext context = CancelRetestContext.from(c);

        // 查询到复查主表
        final SampleRetestMainDto sampleRetestMain = sampleRetestMainService.selectBySampleId(context.getSampleId()).stream()
                .filter(e -> Objects.equals(e.getStatus(), SampleRetestStatusEnum.NORMAL.getCode())).findFirst().orElse(null);
        if (Objects.isNull(sampleRetestMain)) {
            throw new IllegalStateException("当前状态不是复查中");
        }


        // 找到所有报告项目
        final List<SampleReportItemDto> sampleReportItems = sampleReportItemService.selectBySampleId(context.getSampleId());
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            throw new IllegalStateException("没有找到报告项目");
        }

        if (Objects.nonNull(context.getReportItemCode()) && (sampleReportItems.stream().noneMatch(e ->
                Objects.equals(e.getReportItemCode(), context.getReportItemCode())))) {
            throw new IllegalStateException("没有找到报告项目");
        }

        // 查询到样本
        final SampleDto sample = sampleService.selectBySampleId(context.getSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("样本不存在");
        }


        // 获取到当前复查记录
        context.put(CancelRetestContext.SAMPLE_RETEST_ITEMS, sampleRetestItemService
                .selectBySampleRetestMainId(sampleRetestMain.getSampleRetestMainId()));

        context.put(CancelRetestContext.SAMPLE_RETEST_MAIN, sampleRetestMain);
        context.put(CancelRetestContext.SAMPLE_REPORT_ITEMS, sampleReportItems);
        context.put(CancelRetestContext.SAMPLE, sample);

        if (StringUtils.isBlank(context.getReportItemCode())) {
            context.put(CancelRetestContext.SAMPLE_UN_RETEST_ITEMS, context.getSampleRetestItems()
                    .stream().map(SampleRetestItemDto::getReportItemName).collect(Collectors.toList()));
        } else {
            final SampleReportItemDto reportItem = context.getSampleReportItems()
                    .stream()
                    .filter(e -> Objects.equals(e.getReportItemCode(), context.getReportItemCode()))
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(reportItem)) {
                context.put(CancelRetestContext.SAMPLE_UN_RETEST_ITEMS, List.of(reportItem.getReportItemName()));
            }
        }

        return CONTINUE_PROCESSING;
    }
}
