package com.labway.lims.routine.service.chain.result;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.enums.SymbolEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

import static com.labway.lims.routine.service.chain.result.SaveResultContext.EXTRA_INFO;

/**
 * <AUTHOR>
 * @since 2023/3/30 16:23
 */
@Slf4j
@Component
public class SavePGSQLResultCommand implements Command {
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private SampleResultService sampleResultService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();

        final SampleReportItemDto sampleReportItem = context.getSampleReportItem();

        final SampleDto sampleDto = context.getSample();
        final InstrumentReportItemReferenceDto reportItemReference = context.getInstrumentReportItemReference();

        final SampleResultDto sampleResult = new SampleResultDto();
        sampleResult.setSampleId(sampleDto.getSampleId());
        sampleResult.setApplyId(sampleDto.getApplyId());
        sampleResult.setTestItemId(sampleReportItem.getTestItemId());
        sampleResult.setTestItemName(StringUtils.defaultString(sampleReportItem.getTestItemName()));
        sampleResult.setTestItemCode(StringUtils.defaultString(sampleReportItem.getTestItemCode()));
        sampleResult.setReportItemId(sampleReportItem.getReportItemId());
        sampleResult.setReportItemCode(StringUtils.defaultString(sampleReportItem.getReportItemCode()));
        sampleResult.setReportItemName(StringUtils.defaultString(sampleReportItem.getReportItemName()));
        sampleResult.setType(StringUtils.defaultString(instrumentReportItem.getResultTypeName()));
        sampleResult.setResult(StringUtils.defaultString(context.getResult()));
        sampleResult.setUnit(StringUtils.defaultString(instrumentReportItem.getReportItemUnitName()));

        sampleResult.setInstrumentReportItemReferenceId(NumberUtils.LONG_ZERO);
        // 检验时间
        sampleResult.setTestDate(context.getTestDate());

        sampleResult.setExtraInfo(context.getExtraInfo());
        if(Objects.nonNull(context.get(EXTRA_INFO)) && StringUtils.isBlank(sampleResult.getExtraInfo())){
            sampleResult.setExtraInfo(StringUtils.defaultString(String.valueOf(context.get(EXTRA_INFO))));
        }
        sampleResult.setIsHandeResult(context.getIsHandeResult());


        if (Objects.nonNull(reportItemReference)) {
            // 先取中文 -> 英文 -> 中英文 -> 范围参考值
            sampleResult.setRange(
                    StringUtils.defaultIfBlank(reportItemReference.getCnRefereValue(), reportItemReference.getEnRefereValue()));
            sampleResult.setRange(
                    StringUtils.defaultIfBlank(sampleResult.getRange(), reportItemReference.getCnEnRefereValue()));
            if (StringUtils.isBlank(sampleResult.getRange())) {
                if (StringUtils.isNotBlank(reportItemReference.getReferValueMin()) && StringUtils.isNotBlank(reportItemReference.getReferValueMax())) {
                    String range = reportItemReference.getReferValueMin() + "~" + reportItemReference.getReferValueMax();
                    sampleResult.setRange(range);
                } else if (StringUtils.isNotBlank(reportItemReference.getReferValueMin())) {
                    sampleResult.setRange(SymbolEnum.getSymbol(reportItemReference.getReferValueMinFormula()) + reportItemReference.getReferValueMin());
                } else if (StringUtils.isNotBlank(reportItemReference.getReferValueMax())) {
                    sampleResult.setRange(SymbolEnum.getSymbol(reportItemReference.getReferValueMaxFormula()) + reportItemReference.getReferValueMax());
                }
            }
            sampleResult.setInstrumentReportItemReferenceId(reportItemReference.getInstrumentReportItemReferenceId());
        } else {
            sampleResult.setRange(StringUtils.EMPTY);
        }

        if (context.isException()) {
            sampleResult.setStatus(ResultStatusEnum.EXCEPTION.getCode());
        } else if (context.isCritical()) {
            sampleResult.setStatus(ResultStatusEnum.CRISIS.getCode());
        } else {
            sampleResult.setStatus(ResultStatusEnum.NORMAL.getCode());
        }

        sampleResult.setInstrumentId(NumberUtils.LONG_ZERO);
        sampleResult.setInstrumentName(StringUtils.EMPTY);
        sampleResult.setInstrumentResult(StringUtils.EMPTY);

        if (Objects.equals(context.getSource(), SaveResultSourceEnum.MACHINE)) {
            if (Objects.nonNull(context.getInstrument())) {
                sampleResult.setInstrumentId(context.getInstrument().getInstrumentId());
                sampleResult.setInstrumentName(StringUtils.defaultString(context.getInstrument().getInstrumentName()));
            }
            sampleResult.setInstrumentResult(StringUtils.defaultString(context.getInstrumentResult()));
        }

        sampleResult.setJudge(StringUtils.defaultString(context.getResultJudge()));
        sampleResult.setApplySampleId(context.getApplySampleId());

        final long sampleResultId = sampleResultService.addSampleResult(sampleResult);


        // 保存到上下文
        context.getContextResults().put(sampleResult.getReportItemCode(), sampleResult.getResult());
        sampleResult.setSampleResultId(sampleResultId);
        context.put(SaveResultContext.SAMPLE_RESULT, sampleResult);

        return CONTINUE_PROCESSING;
    }

}
