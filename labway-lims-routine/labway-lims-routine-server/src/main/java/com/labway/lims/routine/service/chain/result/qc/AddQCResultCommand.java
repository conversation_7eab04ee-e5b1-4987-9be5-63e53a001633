package com.labway.lims.routine.service.chain.result.qc;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/4 10:34
 */
@Component
public class AddQCResultCommand implements Command {
    @Resource
    private SampleResultService sampleResultService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {

        final SaveQCResultContext context = SaveQCResultContext.from(c);
        final SampleDto sample = context.getSample();

        final SampleResultDto result = genSampleResult(context, sample);
        sampleResultService.addSampleResult(result);

        return CONTINUE_PROCESSING;
    }

    private SampleResultDto genSampleResult(SaveQCResultContext context, SampleDto sample) {
        final InstrumentReportItemReferenceDto ref = context.getInstrumentReportItemReference();
        final SampleResultDto result = new SampleResultDto();
        result.setSampleResultId(snowflakeService.genId());
        result.setSampleId(context.getSampleId());
        result.setApplySampleId(sample.getApplySampleId());
        result.setApplyId(sample.getApplyId());
        result.setTestItemId(NumberUtils.LONG_ZERO);
        result.setTestItemCode("");
        result.setTestItemName("");
        result.setReportItemId(NumberUtils.LONG_ZERO);
        result.setReportItemCode(StringUtils.defaultString(context.getInstrumentReportItem().getReportItemCode()));
        result.setReportItemName(StringUtils.defaultString(context.getInstrumentReportItem().getReportItemName()));
        result.setType(StringUtils.defaultString(context.getInstrumentReportItem().getResultTypeName()));
        result.setResult(StringUtils.defaultString(context.getResult()));
        result.setUnit(StringUtils.defaultString(context.getInstrumentReportItem().getReportItemUnitName()));
        result.setUnit(StringUtils.defaultString(context.getInstrumentReportItem().getReportItemUnitName()));

        if (StringUtils.isNotBlank(context.getExtraInfo())) {
            // 扩充字段
            result.setExtraInfo(context.getExtraInfo());
        }

        if (Objects.nonNull(ref)) {
            // 先取中文 -> 英文 -> 中英文
            result.setRange(
                    StringUtils.defaultString(ref.getCnRefereValue(), ref.getEnRefereValue()));
            result.setRange(
                    StringUtils.defaultIfBlank(result.getRange(), ref.getCnEnRefereValue()));
        } else {
            result.setRange(StringUtils.EMPTY);
        }
        if (context.isException()) {
            result.setStatus(ResultStatusEnum.EXCEPTION.getCode());
        } else if (context.isCritical()) {
            result.setStatus(ResultStatusEnum.CRISIS.getCode());
        } else {
            result.setStatus(ResultStatusEnum.NORMAL.getCode());
        }
        result.setInstrumentId(context.getInstrumentId());
        if (Objects.nonNull(context.getInstrument())){
            result.setInstrumentName(StringUtils.defaultString(context.getInstrument().getInstrumentName()));
        }else {
            result.setInstrumentName(StringUtils.defaultString(context.getInstrument().getInstrumentName()));
        }
        result.setInstrumentResult(StringUtils.defaultString(context.getResult()));
        result.setJudge(StringUtils.defaultString(context.getResultJudge()));

        return result;
    }
}
