package com.labway.lims.routine.service.qc;

import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.vo.SampleReportItemDetailVo;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class OldQcResultService implements QcResultService{
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;

    @Override
    public String version() {
        return "1.0";
    }

    @Override
    public List<SampleReportItemDetailVo> qcResult(SampleDto sample) {
        final Map<String, InstrumentReportItemDto> reportItemMap =
                instrumentReportItemService.selectByInstrumentId(sample.getInstrumentId()).stream()
                        .collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v -> v, (a, b) -> b));

        final List<SampleReportItemDto> reportItems = sampleReportItemService.selectBySampleId(sample.getSampleId());
        final Map<String, SampleResultDto> resultMap = sampleResultService.selectBySampleId(sample.getSampleId())
                .stream().sorted(Comparator.comparing(SampleResultDto::getCreateDate))
                .collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> b));
        final List<SampleReportItemDetailVo> list = new LinkedList<>();
        for (SampleReportItemDto item : reportItems) {
            final SampleResultDto result = resultMap.get(item.getReportItemCode());
            if (Objects.isNull(result)) {
                continue;
            }
            final SampleReportItemDetailVo vo = new SampleReportItemDetailVo();
            vo.setSampleReportItemId(item.getSampleReportItemId());
            vo.setReportItemId(item.getReportItemId());
            vo.setReportItemCode(item.getReportItemCode());
            vo.setReportItemName(item.getReportItemName());
            vo.setTestItemId(item.getTestItemId());
            vo.setTestItemName(item.getTestItemName());
            vo.setTestItemCode(item.getTestItemCode());
            vo.setResult(result.getResult());
            vo.setRange(result.getRange());
            vo.setUnit(result.getUnit());
            vo.setJudge(result.getJudge());
            vo.setInstrumentName(result.getInstrumentName());
            vo.setResentResult(new SampleReportItemDetailVo.ResentResult());
            final InstrumentReportItemDto instrumentReportItem = reportItemMap.get(item.getReportItemCode());
            if (Objects.nonNull(instrumentReportItem)) {
                vo.setEnAb(instrumentReportItem.getEnAb());
                vo.setEnName(instrumentReportItem.getEnName());
                vo.setInstrumentName(instrumentReportItem.getInstrumentName());
            }
            vo.setStatus(result.getStatus());
            vo.setIsRetest(item.getIsRetest());
            vo.setPrintSort(instrumentReportItem.getPrintSort());

            list.add(vo);
        }
        return list;
    }
}
