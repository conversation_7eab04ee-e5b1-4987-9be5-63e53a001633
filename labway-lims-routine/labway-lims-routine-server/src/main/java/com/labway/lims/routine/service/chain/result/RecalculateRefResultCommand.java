package com.labway.lims.routine.service.chain.result;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.impl.ChainBase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 计算依赖
 *
 * <AUTHOR>
 * @since 2023/3/30 16:20
 */
@Slf4j
@Component
public class RecalculateRefResultCommand implements Command {

    /**
     * 因为计算公式涉及到递归，不能一致递归下去，所以这里限制一下，递归到第5层 就强制退出
     */
    private static final String RECALCULATE_RECURSION_COUNT = "RECALCULATE_RECURSION_COUNT_" + IdUtil.objectId();

    @Resource
    private SampleReportItemService sampleReportItemService;
    @Resource
    private SaveResultChain saveResultChain;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);
        final SampleDto sample = context.getSample();
        //过滤出样本仪器的报告项目
        final List<InstrumentReportItemDto> instrumentReportItems = context.getInstrumentReportItems();
        if (CollectionUtils.isEmpty(instrumentReportItems)) {
            return CONTINUE_PROCESSING;
        }

        final int count = (int) ObjectUtils.defaultIfNull(context.get(RECALCULATE_RECURSION_COUNT), 0);
        if (count >= 5) {
            throw new IllegalStateException("计算公式依赖大于 5 层，请检查是否有循环依赖");
        }
        final Map<String, String> contextResults = context.getContextResults();
        contextResults.put(context.getReportItemCode(), context.getResult());

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();

        for (InstrumentReportItemDto e : instrumentReportItems) {
            if (Objects.equals(e.getReportItemCode(), instrumentReportItem.getReportItemCode())) {
                continue;
            }

            // 如果当前样本下不包含这个报告项目，那么则跳过
            if (context.getSampleReportItems().stream().noneMatch(k -> Objects.equals(e.getReportItemCode(), k.getReportItemCode()))) {
                continue;
            }

            // 如果引用了当前修改的项目
            if (StringUtils.contains(e.getCalcFomulation(), "#" + instrumentReportItem.getReportItemCode())) {


                try {

                    if (Objects.isNull(context.getSampleReportItems())) {
                        final List<SampleReportItemDto> sampleReportItems = sampleReportItemService.selectBySampleId(context.getSample().getSampleId());
                        if (CollectionUtils.isEmpty(sampleReportItems)) {
                            throw new IllegalStateException("样本报告项目不存在");
                        }
                        context.put(SaveResultContext.SAMPLE_REPORT_ITEMS, sampleReportItems);
                    }

                    final SampleReportItemDto sampleReportItem = context.getSampleReportItems().stream()
                            .filter(f -> Objects.equals(f.getReportItemCode(), e.getReportItemCode()))
                            .findFirst().orElse(null);
                    if (Objects.isNull(sampleReportItem)) {
                        throw new IllegalStateException(String.format("样本报告项目 [%s] 不存在", e.getInstrumentReportItemId()));
                    }

                    final SaveResultContext ctx = new SaveResultContext();
                    ctx.setApplySampleId(context.getApplySampleId());
                    ctx.setReportItemId(sampleReportItem.getReportItemId());
                    ctx.setReportItemCode(sampleReportItem.getReportItemCode());
                    ctx.setSource(SaveResultSourceEnum.CODE);
                    ctx.setTestDate(context.getTestDate());
                    // 结果上下文传递
                    ctx.getContextResults().putAll(context.getContextResults());
                    ctx.setResult(ctx.getContextResults().get(e.getReportItemCode()));
                    // 标记递归次数
                    ctx.put(RECALCULATE_RECURSION_COUNT, count + 1);

                    // 复用对象 避免多次重复查询
                    ctx.put(SaveResultContext.SAMPLE, context.getSample());
                    ctx.put(SaveResultContext.SAMPLE_TEST_ITEMS, context.getSampleTestItems());
                    ctx.put(SaveResultContext.INSTRUMENT_REPORT_ITEM, e);
                    ctx.put(SaveResultContext.INSTRUMENT_REPORT_ITEMS, instrumentReportItems);
                    ctx.put(SaveResultContext.SAMPLE_REPORT_ITEMS, context.getSampleReportItems());

                    ((ChainProvider) context.get(SaveResultContext.CHAIN_PROVIDER)).chain().execute(ctx);

                } catch (Exception ex) {
                    log.error("样本 [{}] 重新计算项目报告项目 [{}] 错误 {}", context.getSample().getSampleId(),
                            e.getReportItemName(), ex.getMessage(), ex);
                }
            }
        }

        return CONTINUE_PROCESSING;
    }

    public boolean isRecalculate(Context c) {
        return c.containsKey(RECALCULATE_RECURSION_COUNT);
    }

    public interface ChainProvider extends Command {
        ChainBase chain();
    }

    public ChainProvider getDefaultChainProvider() {
        return new ChainProvider() {
            @Override
            public ChainBase chain() {
                return saveResultChain;
            }

            @Override
            public boolean execute(Context c) throws Exception {
                final SaveResultContext from = SaveResultContext.from(c);
                from.put(SaveResultContext.CHAIN_PROVIDER, this);
                return CONTINUE_PROCESSING;
            }
        };
    }
}
