package com.labway.lims.routine.service.chain.result;

import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 小数点
 *
 * <AUTHOR>
 * @since 2023/3/30 16:22
 */
@Slf4j
@Component
public class ResultFormatCommand implements Command {
    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();

        String value = context.getResult();

        // 如果结果是数字，那么无论是什么结果类型，都进行小数点控制
        if (Objects.nonNull(instrumentReportItem.getDecimalNums()) && NumberUtils.isParsable(value)) {
            // 格式化结果
            if (instrumentReportItem.getDecimalNums() > 0) {
                value = new BigDecimal(value).setScale(instrumentReportItem.getDecimalNums(), RoundingMode.HALF_UP).toString();
            } else if (instrumentReportItem.getDecimalNums() < 0) { // 小于 0 表示不保留位数
                value = new BigDecimal(value).setScale(NumberUtils.INTEGER_ZERO, RoundingMode.HALF_UP).toString();
            }
        }

        context.setResult(value);

        return CONTINUE_PROCESSING;
    }
}
