package com.labway.lims.routine.controller;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.routine.api.dto.*;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import com.labway.lims.routine.service.chain.retest.StartRetestCheckSaveOriginalCommand;
import com.labway.lims.routine.vo.CancelSampleRetestVo;
import com.labway.lims.routine.vo.CountRetestResultVo;
import com.labway.lims.routine.vo.RetestRecordVo;
import com.labway.lims.routine.vo.StartReTestResultVo;

/**
 * <AUTHOR>
 * @since 2023/4/19 15:25
 */
@RestController
@RequestMapping("/retest")
public class SampleRetestController extends BaseController {

    @Resource
    private SampleRetestMainService sampleRetestMainService;
    @Resource
    private SampleReportItemService sampleReportItemService;
    @Resource
    private SampleResultService sampleResultService;
    @Resource
    private SampleRetestItemService sampleRetestItemService;
    @Resource
    private StartRetestCheckSaveOriginalCommand startRetestCheckSaveOriginalCommand;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @PostMapping("/start-retest-result")
    public Object startReTestResult(@RequestBody StartReTestResultVo vo) {
        final Long sampleId = vo.getSampleId();

        if (Objects.isNull(sampleId)) {
            throw new IllegalArgumentException("请选择样本");
        }

        final StartReTestDto dto = new StartReTestDto();
        dto.setSampleId(vo.getSampleId());
        dto.setReportItemId(vo.getReportItemId());
        dto.setReportItemCode(vo.getReportItemCode());
        //1.1.3新增回读处理
        dto.setIsReadBack(vo.getIsReadBack());
        dto.setRemark(vo.getRemark());
        dto.setNeedHandleReadBack(vo.isNeedHandleReadBack());

        sampleRetestMainService.startRetest(dto);

        return Collections.emptyMap();
    }

    /**
     * 取消复查
     */
    @PostMapping("/cancel-report-item-retest")
    public Object cancelReportItemReTest(@RequestBody CancelSampleRetestVo vo) {

        final String key = redisPrefix.getBasePrefix() + "cancelReportItemReTest:" + vo.getSampleId();
        if (BooleanUtils
            .isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException("正在取消复查中");
        }

        try {
            if (Objects.isNull(vo.getReportItemId())) {
                sampleRetestMainService.cancelRetest(vo.getSampleId());
            } else {
                sampleRetestMainService.cancelRetest(vo.getSampleId(), vo.getReportItemCode());
            }
        } finally {
            stringRedisTemplate.delete(key);
        }

        return Collections.emptyMap();
    }

    /**
     * 统计复查信息
     */
    @PostMapping("/count-retest-result-info")
    public Object countRetestResultInfo(@RequestParam("sampleId") Long sampleId) {
        if (Objects.isNull(sampleId)) {
            return Collections.emptyMap();
        }

        final CountRetestResultDto dto = sampleRetestMainService.countRetestResultInfo(sampleId);

        return JSON.parseObject(JSON.toJSONString(dto), CountRetestResultVo.class);
    }

    /**
     * 历史复查记录
     */
    @PostMapping("/retest-records")
    public List<RetestRecordVo> selectRetestRecord(long sampleId) {

        final List<SampleReportItemDto> sampleReportItems = sampleReportItemService.selectBySampleId(sampleId);
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            return List.of();
        }

        // 获取到复查历史
        final List<SampleRetestMainDto> sampleRetestMains = sampleRetestMainService.selectBySampleId(sampleId).stream()
            .filter(e -> Objects.equals(e.getStatus(), SampleRetestStatusEnum.RETEST.getCode()))
            .sorted(Comparator.comparing(SampleRetestMainDto::getSampleRetestMainId)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(sampleRetestMains)) {
            return Collections.emptyList();
        }

        final List<Long> sampleRetestMainIds =
            sampleRetestMains.stream().map(SampleRetestMainDto::getSampleRetestMainId).collect(Collectors.toList());

        // 原始结果
        final Map<String, SampleResultDto> originalResults = sampleResultService.selectBySampleIds(sampleRetestMainIds)
            .stream().collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a));

        // 复查历史
        final Map<Long, List<SampleRetestItemDto>> retestItems = sampleRetestItemService.selectBySampleId(sampleId)
            .stream().collect(Collectors.groupingBy(SampleRetestItemDto::getSampleRetestMainId));
        if (MapUtils.isEmpty(retestItems)) {
            return List.of();
        }

        // 复查过的报告项目
        final List<SampleReportItemDto> retestReportItems =
            sampleReportItems.stream()
                .filter(e -> Objects.equals(e.getIsRetest(), RetestStatusEnum.RETEST.getCode())
                    || Objects.equals(e.getIsRetest(), RetestStatusEnum.RETESTING.getCode()))
                .collect(Collectors.toList());

        final List<RetestRecordVo> list = new ArrayList<>();
        for (SampleReportItemDto e : retestReportItems) {
            final SampleResultDto originalResult = originalResults.get(e.getReportItemCode());
            final RetestRecordVo vo = new RetestRecordVo();
            vo.setReportItemId(e.getReportItemId());
            vo.setReportItemCode(e.getReportItemCode());
            vo.setReportItemName(e.getReportItemName());
            vo.setPrintSort(e.getPrintSort());
            vo.setOperator(StringUtils.EMPTY);
            vo.setOriginalResult(StringUtils.EMPTY);
            vo.setStatus(ResultStatusEnum.NORMAL.getCode());
            vo.setTestJudge(StringUtils.EMPTY);
            vo.setHistoryResult(new LinkedList<>());

            // 原始结果
            if (Objects.nonNull(originalResult)) {
                vo.setOriginalResult(originalResult.getResult());
                vo.setOperator(originalResult.getCreatorName());
                vo.setStatus(originalResult.getStatus());
                vo.setTestJudge(originalResult.getJudge());
            }

            // 复查历史结果
            for (SampleRetestMainDto main : sampleRetestMains) {
                final List<SampleRetestItemDto> items = retestItems.get(main.getSampleRetestMainId());
                if (Objects.isNull(items)) {
                    continue;
                }
                final RetestRecordDto.RetestResult v = new RetestRecordDto.RetestResult();
                // 从所有复查历史中查询到当前报告项目的
                for (SampleRetestItemDto item : items.stream()
                    .filter(k -> Objects.equals(k.getReportItemCode(), e.getReportItemCode()))
                    .collect(Collectors.toList())) {
                    v.setResult(item.getResult());
                    v.setStatus(item.getStatus());
                    v.setTestJudge(item.getJudge());
                    v.setSampleRetestMainId(item.getSampleRetestMainId());
                    v.setSampleRetestItemId(item.getSampleRetestItemId());
                }
                vo.getHistoryResult().add(v);
            }

            list.add(vo);

        }

        return list.stream()
            .sorted(Comparator.comparing(RetestRecordVo::getPrintSort).thenComparing(RetestRecordVo::getReportItemId))
            .collect(Collectors.toList());
    }

    /**
     * 此样本 对应 所有结果 包含 原始结果、复查结果
     */
    @PostMapping("/result-list")
    public Object resultList(@RequestParam Long sampleId) {
        List<RetestRecordVo> retestRecordVos = this.selectRetestRecord(sampleId);

        if (CollectionUtils.isEmpty(retestRecordVos)) {
            return Collections.emptyMap();
        }

        Map<String, List<ResultListDto>> targetmap = new HashMap<>();

        retestRecordVos.forEach(item -> {

            String reportItemCode = item.getReportItemCode();
            List<ResultListDto> temp = Lists.newArrayList();

            // 先 放入 原始结果
            ResultListDto originalResult = new ResultListDto();
            originalResult.setReportItemCode(reportItemCode);
            originalResult.setContentType(1);
            originalResult.setContent(item.getOriginalResult());
            originalResult.setSort(0);
            temp.add(originalResult);

            // 再 放入 复查结果
            for (int i = 0; i < item.getHistoryResult().size(); i++) {
                if (Objects.isNull(item.getHistoryResult().get(i).getResult())) {
                    //不是复查的此报告项目
                    continue;
                }
                ResultListDto retestResult = new ResultListDto();
                retestResult.setReportItemCode(reportItemCode);
                retestResult.setContentType(2);
                retestResult.setContent(item.getHistoryResult().get(i).getResult());
                retestResult.setSort(i + 1);
                temp.add(retestResult);
            }

            targetmap.put(reportItemCode, temp);
        });

        return targetmap;
    }

}
