package com.labway.lims.routine.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 * SampleResultRuleVerifyConfig
 * 样本结果规则校验配置
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/2/10 10:27
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "sample.result")
public class SampleResultRuleVerifyConfig {

    private List<Rule> verifyRules = new ArrayList<>();

    @Data
    public static class Rule {

        /**
         * 公式，如果公式计算出来的结果为true，则审核是提示信息
         */
        private String formula;

        /**
         * 审核提示
         */
        private String auditTips;

    }

}
