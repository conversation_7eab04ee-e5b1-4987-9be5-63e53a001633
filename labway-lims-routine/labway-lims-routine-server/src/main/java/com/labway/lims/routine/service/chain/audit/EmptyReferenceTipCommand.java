package com.labway.lims.routine.service.chain.audit;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.ExceptionCodeEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <pre>
 * EmptyReferenceTipCommand
 * 空参考范围提示
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/13 15:22
 */
@Component
@Slf4j
public class EmptyReferenceTipCommand implements Command {

    @Override
    public boolean execute(Context c) throws Exception {
        final AuditSampleContext context = AuditSampleContext.from(c);

        final SampleAuditDto auditVo = context.getParam();
        final SampleDto sample = context.getSample();
        final List<SampleResultDto> forbidden = context.getEmptyReferenceResultForbidden();
        final List<SampleResultDto> warning = context.getEmptyReferenceResultWarning();

        // 禁止审核
        if (CollectionUtils.isNotEmpty(forbidden)) {
            final String reportNames = forbidden.stream().map(SampleResultDto::getReportItemName)
                    .collect(Collectors.joining("、", "【", "】"));

            throw new IllegalStateException(String.format(
                    ExceptionCodeEnum.EMPTY_REFERENCE_FORBIDDEN.getDesc(), sample.getBarcode(), reportNames));
        }

        // 二审操作 或者 忽略空参考范围审核提示
        if (Objects.equals(auditVo.getAuditStatus(), SampleAuditStatusEnum.TWO_CHECK.name()) ||
                BooleanUtils.toBooleanObject(auditVo.getAuditForce()) || BooleanUtils.isTrue(auditVo.getIgnoreEmptyReferenceTip())) {
            return CONTINUE_PROCESSING;
        }

        // 审核时提示
        if (CollectionUtils.isNotEmpty(warning)) {
            final String reportNames = warning.stream().map(SampleResultDto::getReportItemName)
                    .collect(Collectors.joining("、", "【", "】"));

            final List<String> msgs = new ArrayList<>();
            msgs.add(String.format(ExceptionCodeEnum.EMPTY_REFERENCE_WARNING.getDesc(), sample.getBarcode(), reportNames));

            throw new LimsCodeException(ExceptionCodeEnum.EMPTY_REFERENCE_WARNING.getCode(), JSON.toJSONString(msgs));
        }

        return CONTINUE_PROCESSING;
    }

}
