package com.labway.lims.routine.service.chain.auto.audit;

import cn.hutool.core.collection.CollUtil;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleAuditMethodEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.routine.vo.ruleengine.AutoAuditResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 判断规则结果，调用审核
 *
 * <AUTHOR> on 2025/7/30.
 */
@Slf4j
@Component
public class AutoAuditCommand implements Command {

	@Resource
	private SampleService sampleService;

	@Override
	public boolean execute(Context c) throws Exception {
		AutoAuditContext context = AutoAuditContext.from(c);

		try {
			// 样本检验结果
	        List<AutoAuditResult> autoAuditResultList = context.getAutoAuditResultList();

	        if (CollUtil.isEmpty(autoAuditResultList)) {
	            throw new IllegalStateException("自动审核：无决策执行结果，无法审核审核样本");
	        }

	        autoAuditResultList = autoAuditResultList.stream()
	                .filter(e -> BooleanUtils.toBoolean(e.getIsAutoSuccess()))
	                .collect(Collectors.toList());

	        if (CollUtil.isEmpty(autoAuditResultList)) {
	            throw new IllegalStateException("自动审核：存在检验项目未通过决策，无法审核样本");
	        }

	        // 组装样本审核数据
	        SampleAuditDto auditDto = buildSampleAuditDto(context);

	        // 执行样本审核
			context.setAutoAuditResultStatus(Boolean.TRUE);
			sampleService.auditSamplesChain(auditDto);
		} catch (Exception e) {
			String errorMsg = ExceptionUtils.getMessage(e);
			context.setAutoAuditResultStatus(Boolean.FALSE);
			context.setAutoAuditErrorInfo(errorMsg);
		}

		return CONTINUE_PROCESSING;
	}

	private SampleAuditDto buildSampleAuditDto(AutoAuditContext context) {
		SampleAuditDto auditDto = new SampleAuditDto();
		auditDto.setSampleId(context.getSampleId());
		auditDto.setAuditStatus(SampleAuditStatusEnum.ONE_CHECK.name());
		auditDto.setAuditForce(YesOrNoEnum.NO.getCode());
		auditDto.setAuditName(StringUtils.EMPTY);
		auditDto.setAuditPwd(StringUtils.EMPTY);
		auditDto.setAutoAudit(SampleAuditMethodEnum.AUTO.getCode());
		return auditDto;
	}
}
