package com.labway.lims.routine.service.chain.retest.cancel;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.routine.api.dto.ResultStatusDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.service.chain.result.UpdateMissItemCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 刷数据
 */
@Slf4j
@Component
class CancelRetestWriteSampleResultCommand implements Command {
    @Resource
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private SampleResultService sampleResultService;
    @Resource
    private UpdateMissItemCommand updateMissItemCommand;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final CancelRetestContext context = CancelRetestContext.from(c);
        final List<SampleRetestItemDto> sampleRetestItems = context.getSampleRetestItems();

        if (CollectionUtils.isEmpty(sampleRetestItems)) {
            return CONTINUE_PROCESSING;
        }

        // 主表没有结束复查那就还不能刷数据
        if (!Objects.equals(context.getSampleRetestMain().getStatus(), SampleRetestStatusEnum.RETEST.getCode())) {
            return CONTINUE_PROCESSING;
        }

        // 获取报告项目
        final Map<String, InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService
                .selectByInstrumentGroupId(context.getSample().getInstrumentGroupId())
                .stream().collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v -> v, (a, b) -> a));

        // 先拿到原来的结果
        final Map<String, SampleResultDto> sampleResultDtoMap = sampleResultService.selectBySampleId(context.getSample().getSampleId())
            .stream()
            .collect(Collectors.toMap(SampleResultDto::getReportItemCode, Function.identity(), (a, b) -> a));

        final LinkedList<Long> ids = snowflakeService.genIds(context.getSampleRetestItems().size());
        // 删除原来的结果
        sampleResultService.deleteByReportItemCodes(context.getSampleId(), sampleRetestItems.stream().map(SampleRetestItemDto::getReportItemCode)
                .collect(Collectors.toList()));

        final List<SampleResultDto> results = sampleRetestItems.stream().map(e -> {
            final SampleResultDto dto = new SampleResultDto();
            dto.setSampleResultId(ids.pop());
            dto.setSampleId(context.getSampleId());
            dto.setApplySampleId(sampleRetestItems.iterator().next().getApplySampleId());
            dto.setApplyId(context.getSample().getApplyId());
            dto.setTestItemId(e.getTestItemId());
            dto.setTestItemCode(e.getTestItemCode());
            dto.setTestItemName(e.getTestItemName());
            dto.setReportItemId(e.getReportItemId());
            dto.setReportItemCode(e.getReportItemCode());
            dto.setReportItemName(e.getReportItemName());
            dto.setType(e.getTestResultType());
            dto.setResult(e.getResult());
            dto.setUnit(StringUtils.EMPTY);
            if (instrumentReportItems.containsKey(e.getReportItemCode())) {
                dto.setUnit(StringUtils.defaultString(instrumentReportItems.get(e.getReportItemCode()).getReportItemUnitName()));
            }
            dto.setRange(e.getRange());
            dto.setStatus(e.getStatus());
            dto.setInstrumentId(0L);
            dto.setInstrumentName(StringUtils.EMPTY);
            dto.setInstrumentResult(StringUtils.EMPTY);
            dto.setJudge(e.getJudge());
            dto.setCreateDate(new Date());
            dto.setUpdateDate(new Date());
            dto.setCreatorId(e.getCreatorId());
            dto.setCreatorName(e.getCreatorName());
            dto.setUpdaterId(e.getUpdaterId());
            dto.setUpdaterName(e.getUpdaterName());
            dto.setIsDelete(YesOrNoEnum.NO.getCode());

            SampleResultDto oldSampleResultDto = sampleResultDtoMap.getOrDefault(e.getReportItemCode(), new SampleResultDto());
            dto.setRange(StringUtils.defaultIfBlank(dto.getRange(), StringUtils.defaultString(oldSampleResultDto.getRange())));
            dto.setInstrumentReportItemReferenceId(
                Objects.requireNonNullElse(oldSampleResultDto.getInstrumentReportItemReferenceId(), NumberUtils.LONG_ZERO));

            return dto;
        }).collect(Collectors.toList());

        // 刷入复查结果进结果表
        sampleResultService.addSampleResults(results);

        // 刷新标记
        updateMissItemCommand.mark(context.getSample().getSampleId(),
                results.stream().map(e -> {
                    final ResultStatusDto k = new ResultStatusDto();
                    k.setReportItemCode(e.getReportItemCode());
                    k.setResult(e.getResult());
                    k.setIsException(Objects.equals(e.getStatus(), ResultStatusEnum.EXCEPTION.getCode())
                            ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                    k.setIsCritical(Objects.equals(e.getStatus(), ResultStatusEnum.CRISIS.getCode())
                            ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                    k.setJudge(e.getJudge());
                    k.setIsRetest(YesOrNoEnum.NO.getCode());
                    return k;
                }).collect(Collectors.toList()));

        // 修改 isRetest 字段
        final List<SampleReportItemDto> sampleReportItems = context.getSampleReportItems().stream()
                .filter(e -> sampleRetestItems.stream().anyMatch(k -> Objects.equals(e.getReportItemCode(),
                        k.getReportItemCode()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sampleRetestItems)) {
            return CONTINUE_PROCESSING;
        }

        // 修改报告项目
        final SampleReportItemDto m = new SampleReportItemDto();
        m.setIsRetest(RetestStatusEnum.RETEST.getCode());
        m.setSampleId(context.getSampleId());
        sampleReportItemService.updateBySampleReportItemIds(m, sampleReportItems.stream()
                .map(SampleReportItemDto::getSampleReportItemId).collect(Collectors.toSet()));


        return CONTINUE_PROCESSING;
    }
}
