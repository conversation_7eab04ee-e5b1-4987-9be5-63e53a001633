package com.labway.lims.routine.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.QcBatchLevelEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.TestResultTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.QcBatchDto;
import com.labway.lims.base.api.dto.QcBatchReportItemDto;
import com.labway.lims.base.api.service.QcBatchReportItemService;
import com.labway.lims.base.api.service.QcBatchService;
import com.labway.lims.routine.api.dto.AddQcSampleResultDto;
import com.labway.lims.routine.api.dto.QcSampleResultDto;
import com.labway.lims.routine.api.dto.QueryQcSampleResultDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SyncQcSampleResultDto;
import com.labway.lims.routine.api.service.QcSampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.routine.mapper.TbQcSampleResultMapper;
import com.labway.lims.routine.mapstruct.QcSampleResultConverter;
import com.labway.lims.routine.model.TbQcSampleResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 质控样本结果 service impl
 *
 * <AUTHOR>
 * @since 2023/7/5 11:22
 */
@Slf4j
@DubboService
public class QcSampleResultServiceImpl implements QcSampleResultService {

    @Resource
    private TbQcSampleResultMapper tbQcSampleResultMapper;
    @Resource
    private QcSampleResultConverter qcSampleResultConverter;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private QcBatchService qcBatchService;
    @DubboReference
    private QcBatchReportItemService qcBatchReportItemService;
    @Resource
    private SampleService sampleService;

    @Override
    public List<QcSampleResultDto> selectBySampleId(long sampleId) {

        LambdaQueryWrapper<TbQcSampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbQcSampleResult::getSampleId, sampleId);
        queryWrapper.eq(TbQcSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());

        return qcSampleResultConverter.qcSampleResultDtoListFromTbObj(tbQcSampleResultMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncQcSampleResult(SyncQcSampleResultDto sampleResultDto) {
        // 新增
        Collection<QcSampleResultDto> needAddQcSampleResultDtos = sampleResultDto.getNeedAddQcSampleResultDtos();
        if (CollectionUtils.isNotEmpty(needAddQcSampleResultDtos)) {

            // 要添加的 物料库存
            List<TbQcSampleResult> targetList =
                qcSampleResultConverter.tbQcSampleResultListFromTbObjDto(needAddQcSampleResultDtos);

            // 数量 分区批次插入
            List<List<TbQcSampleResult>> partitionList = ListUtils.partition(targetList, 500);

            partitionList.forEach(item -> tbQcSampleResultMapper.batchAddQcQcSampleResult(item));
        }
        // 删除
        Collection<QcSampleResultDto> needDeleteSampleResultDtos = sampleResultDto.getNeedDeleteSampleResultDtos();
        if (CollectionUtils.isNotEmpty(needDeleteSampleResultDtos)) {
            this.physicsDeleteByIds(needDeleteSampleResultDtos.stream()
                .map(QcSampleResultDto::getSampleResultId).distinct().collect(Collectors.toList()));
        }

        // 更新
        for (QcSampleResultDto updateQcSampleResultDto : sampleResultDto.getNeedUpdateQcSampleResultDtos()) {
            tbQcSampleResultMapper
                .updateById(qcSampleResultConverter.tbQcSampleResultFromTbObjDto(updateQcSampleResultDto));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySampleId(long sampleId) {
        LambdaUpdateWrapper<TbQcSampleResult> updateWrapper = Wrappers.lambdaUpdate();

        updateWrapper.set(TbQcSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        updateWrapper.eq(TbQcSampleResult::getSampleId, sampleId);
        updateWrapper.eq(TbQcSampleResult::getIsDelete, YesOrNoEnum.YES.getCode());

        tbQcSampleResultMapper.update(null, updateWrapper);
    }

    @Override
    public List<QcSampleResultDto> selectQcSampleResult(QueryQcSampleResultDto param) {
        LambdaQueryWrapper<TbQcSampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbQcSampleResult::getInstrumentId, param.getInstrumentId());
        queryWrapper.eq(TbQcSampleResult::getReportItemCode, param.getReportItemCode());
        queryWrapper.ge(TbQcSampleResult::getTestDate, param.getStartDate());
        queryWrapper.le(TbQcSampleResult::getTestDate, param.getEndDate());
        queryWrapper.eq(TbQcSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByAsc(TbQcSampleResult::getTestDate);

        return qcSampleResultConverter.qcSampleResultDtoListFromTbObj(tbQcSampleResultMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional
    public List<Long> addQcSampleResult(AddQcSampleResultDto param) {

        QcSampleResultDto qcSampleResultDto = new QcSampleResultDto();
        BeanUtils.copyProperties(param, qcSampleResultDto);

        qcSampleResultDto.setSampleId(NumberUtils.LONG_ZERO);
        qcSampleResultDto.setQcBatch(StringPool.EMPTY);
        qcSampleResultDto.setApplyId(NumberUtils.LONG_ZERO);
        qcSampleResultDto.setApplySampleId(NumberUtils.LONG_ZERO);
        qcSampleResultDto.setTestItemId(NumberUtils.LONG_ZERO);
        qcSampleResultDto.setTestItemCode(StringPool.EMPTY);
        qcSampleResultDto.setTestItemName(StringPool.EMPTY);
        qcSampleResultDto.setReportItemId(NumberUtils.LONG_ZERO);
        qcSampleResultDto.setType(TestResultTypeEnum.NUMBER.getStr());
        qcSampleResultDto.setUnit(StringPool.EMPTY);
        qcSampleResultDto.setRange(StringPool.EMPTY);
        qcSampleResultDto.setStatus(NumberUtils.INTEGER_ZERO);
        qcSampleResultDto.setInstrumentResult(param.getResult());
        qcSampleResultDto.setJudge(StringPool.EMPTY);

        Date now = new Date();
        qcSampleResultDto.setCreateDate(now);
        qcSampleResultDto.setCreatorName(LoginUserHandler.get().getNickname());
        qcSampleResultDto.setCreatorId(LoginUserHandler.get().getUserId());
        qcSampleResultDto.setUpdateDate(now);
        qcSampleResultDto.setUpdaterName(LoginUserHandler.get().getNickname());
        qcSampleResultDto.setUpdaterId(LoginUserHandler.get().getUserId());
        qcSampleResultDto.setIsDelete(YesOrNoEnum.NO.getCode());

        LinkedList<Long> sampleResultIds = snowflakeService.genIds(3);
        List<Long> ids = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getLow())) {
            qcSampleResultDto.setSampleResultId(sampleResultIds.pop());
            qcSampleResultDto.setResult(param.getLow());
            qcSampleResultDto.setInstrumentResult(param.getLow());
            qcSampleResultDto.setLevel(QcBatchLevelEnum.LOW.getLevel());
            matchQcBatchAndSample(qcSampleResultDto);
            TbQcSampleResult entity = qcSampleResultConverter.tbQcSampleResultFromTbObjDto(qcSampleResultDto);
            tbQcSampleResultMapper.insert(entity);
            ids.add(entity.getSampleResultId());
        }
        if (StringUtils.isNotBlank(param.getMedium())) {
            qcSampleResultDto.setSampleResultId(sampleResultIds.pop());
            qcSampleResultDto.setResult(param.getMedium());
            qcSampleResultDto.setInstrumentResult(param.getMedium());
            qcSampleResultDto.setLevel(QcBatchLevelEnum.MEDIUM.getLevel());
            matchQcBatchAndSample(qcSampleResultDto);
            TbQcSampleResult entity = qcSampleResultConverter.tbQcSampleResultFromTbObjDto(qcSampleResultDto);
            tbQcSampleResultMapper.insert(entity);
            ids.add(entity.getSampleResultId());
        }
        if (StringUtils.isNotBlank(param.getHigh())) {
            qcSampleResultDto.setSampleResultId(sampleResultIds.pop());
            qcSampleResultDto.setResult(param.getHigh());
            qcSampleResultDto.setInstrumentResult(param.getHigh());
            qcSampleResultDto.setLevel(QcBatchLevelEnum.HIGH.getLevel());
            matchQcBatchAndSample(qcSampleResultDto);
            TbQcSampleResult entity = qcSampleResultConverter.tbQcSampleResultFromTbObjDto(qcSampleResultDto);
            tbQcSampleResultMapper.insert(entity);
            ids.add(entity.getSampleResultId());
        }

        return ids;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        LambdaUpdateWrapper<TbQcSampleResult> updateWrapper = Wrappers.lambdaUpdate();

        updateWrapper.set(TbQcSampleResult::getIsDelete, YesOrNoEnum.YES.getCode());
        updateWrapper.in(TbQcSampleResult::getSampleResultId, ids);
        updateWrapper.eq(TbQcSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());

        tbQcSampleResultMapper.update(null, updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void physicsDeleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        tbQcSampleResultMapper.physicsDeleteBatchIds(ids);
    }

    private void matchQcBatchAndSample(QcSampleResultDto qcSampleResultDto) {
        // 浓度等级
        final String level = qcSampleResultDto.getLevel();
        // 报告项目编码
        final String reportItemCode = qcSampleResultDto.getReportItemCode();
        final Date testDate = qcSampleResultDto.getTestDate();
        if (StringUtils.isAnyBlank(level, reportItemCode, String.valueOf(testDate))) {
            log.error("参数为空 浓度 [{}] 报告项目编码 [{}] 时间 [{}]", level, reportItemCode, testDate);
            return;
        }

        final List<QcBatchReportItemDto> qcBatchReportItemDtos = qcBatchReportItemService.selectByReportItemCode(reportItemCode);
        if (CollectionUtils.isEmpty(qcBatchReportItemDtos)) {
            return;
        }
        final List<QcBatchDto> qcBatchDtos = qcBatchService.selectByQcBatchIds(qcBatchReportItemDtos.stream()
                .map(QcBatchReportItemDto::getQcBatchId).collect(Collectors.toSet()));
        // 删除掉未生效 或者 已过期的
        qcBatchDtos.removeIf(e -> e.getEndDate().before(testDate) || e.getBeginDate().after(testDate));

        if (CollectionUtils.isEmpty(qcBatchDtos)) {
            log.error("没有找到匹配的质控批号 报告项目编码 [{}] 报告项目名称 [{}]", reportItemCode, qcSampleResultDto.getReportItemName());
            return;
        }

        if (qcBatchDtos.size() > 1) {
            log.warn("匹配到多个质控批号 报告项目编码 [{}] 报告项目名称 [{}] 质控批号 [{}]",
                    reportItemCode, qcSampleResultDto.getReportItemName(), qcBatchDtos.stream().map(QcBatchDto::getQcBatch).collect(Collectors.joining(StringPool.COMMA)));
        }

        // 找到匹配的质控批号
        final QcBatchDto matchedQcBatch = qcBatchDtos.iterator().next();
        qcSampleResultDto.setQcBatch(matchedQcBatch.getQcBatch());

        // 拿到质控批号维护的浓度对应的样本号
        final String sampleNo = QCLEVEL_SAMPLENO_MAP.getOrDefault(level, e -> "").apply(matchedQcBatch);
        if (StringUtils.isEmpty(sampleNo)) {
            log.error("没有找到质控批号维护 匹配浓度的样本号 报告项目编码 [{}] 报告项目名称 [{}] 质控批号 [{}]",
                    reportItemCode, qcSampleResultDto.getReportItemName(), qcBatchDtos.stream().map(QcBatchDto::getQcBatch).collect(Collectors.joining(StringPool.COMMA)));
            return;
        }
        List<SampleDto> sampleDtos = sampleService.selectBySampleNos(Collections.singletonList(sampleNo),
                DateUtil.toLocalDateTime(testDate).toLocalDate(), LoginUserHandler.get().getGroupId());

        if (CollectionUtils.isNotEmpty(sampleDtos)) {
            qcSampleResultDto.setSampleId(sampleDtos.iterator().next().getSampleId());
        } else {
            log.error("没有找到匹配的样本 报告项目编码 [{}] 报告项目名称 [{}] 质控批号 [{}] 浓度 [{}] 样本号 [{}] ",
                    reportItemCode, qcSampleResultDto.getReportItemName(), matchedQcBatch.getQcBatch(), level, sampleNo);
        }

    }

    private static final Map<String, Function<QcBatchDto, String>> QCLEVEL_SAMPLENO_MAP = new HashMap<>() {{
        put("low", QcBatchDto::getLow);
        put("medium", QcBatchDto::getMedium);
        put("high", QcBatchDto::getHigh);
    }};

}
