package com.labway.lims.routine.service.chain.result;

import com.labway.lims.api.enums.routine.TestResultTypeEnum;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.Objects;

/**
 * 阴阳结果
 *
 * <AUTHOR>
 * @since 2023/3/30 16:25
 */
@Slf4j
@Component
public class YinYangResultCommand implements Command {


    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();
        final InstrumentReportItemReferenceDto ref = context.getInstrumentReportItemReference();

        final String result = context.getResult();

        if (!Objects.equals(instrumentReportItem.getResultTypeCode(), TestResultTypeEnum.YIN_YANG.getCode())
                && !Objects.equals(instrumentReportItem.getResultTypeCode(), TestResultTypeEnum.STR.getCode())) {
            return CONTINUE_PROCESSING;
        }

        @Nullable
        Boolean isException = null;

        if (Objects.nonNull(ref)) {
            if (Objects.equals(instrumentReportItem.getResultTypeCode(), TestResultTypeEnum.YIN_YANG.getCode())) {
                final String cn = ref.getCnEnRefereValue();
                final String en = ref.getEnRefereValue();

                isException = BooleanUtils.isTrue(Objects.equals(result, cn) || Objects.equals(result, en));
            }
            context.put(SaveResultContext.RESULT_IS_EXCEPTION, isException);
        }

        return CONTINUE_PROCESSING;
    }

}
