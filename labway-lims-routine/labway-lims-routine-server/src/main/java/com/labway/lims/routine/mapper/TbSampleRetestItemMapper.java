package com.labway.lims.routine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.routine.model.TbSampleRetestItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 样本结果复查子表 Mapper
 * 
 * <AUTHOR>
 * @since 2023/4/11 19:05
 */
@Mapper
public interface TbSampleRetestItemMapper extends BaseMapper<TbSampleRetestItem> {

    int addBatch(@Param("retestItems") List<TbSampleRetestItem> retestItems);
}