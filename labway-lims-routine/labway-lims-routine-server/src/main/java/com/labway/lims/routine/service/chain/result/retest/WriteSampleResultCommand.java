package com.labway.lims.routine.service.chain.result.retest;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.routine.api.dto.ResultStatusDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.service.chain.result.RecalculateRefResultCommand;
import com.labway.lims.routine.service.chain.result.SaveResultContext;
import com.labway.lims.routine.service.chain.result.UpdateMissItemCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 把复查结果刷入到结果表
 */
@Slf4j
@Component
class WriteSampleResultCommand implements Command {
    @Resource
    private SampleRetestItemService sampleRetestItemService;
    @Resource
    private SampleResultService sampleResultService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RecalculateRefResultCommand recalculateRefResultCommand;
    @Resource
    private SaveRetestResultCommand saveRetestResultCommand;
    @Resource
    private UpdateMissItemCommand updateMissItemCommand;

    @Override
    public boolean execute(Context c) throws Exception {

        final RetestResultContext context = RetestResultContext.from(c);
        final SampleReportItemDto sampleReportItem = context.getSampleReportItem();

        // 如果没有完成复查那么不需要刷入数据
        if (!context.isComplete()) {
            return CONTINUE_PROCESSING;
        }

        // 如果是在递归中，不刷数据
        if (recalculateRefResultCommand.isRecalculate(c)) {
            return CONTINUE_PROCESSING;
        }

        final List<SampleRetestItemDto> sampleRetestItems = context.getSampleRetestItems();
        if (CollectionUtils.isEmpty(sampleRetestItems)) {
            return CONTINUE_PROCESSING;
        }

        final LinkedList<Long> ids = snowflakeService.genIds(sampleRetestItems.size());

        final Map<String, InstrumentReportItemDto> instrumentReportItems = context.getInstrumentReportItems()
                .stream().collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v -> v, (a, b) -> a));

        // 先拿到原来的结果
        final Map<String, SampleResultDto> sampleResultDtoMap = sampleResultService.selectBySampleId(context.getSample().getSampleId())
                .stream()
                .collect(Collectors.toMap(SampleResultDto::getReportItemCode, Function.identity(), (a, b) -> a));

        // 删除原来的结果
        sampleResultService.deleteByReportItemCodes(context.getSample().getSampleId(),
                sampleRetestItems.stream().map(SampleRetestItemDto::getReportItemCode).collect(Collectors.toList()));


        final List<SampleResultDto> results = sampleRetestItems.stream().map(e -> {
            final SampleResultDto dto = new SampleResultDto();
            dto.setSampleResultId(ids.pop());
            dto.setSampleId(context.getSample().getSampleId());
            dto.setApplySampleId(context.getSample().getApplySampleId());
            dto.setApplyId(context.getSample().getApplyId());
            dto.setTestItemId(e.getTestItemId());
            dto.setTestItemCode(e.getTestItemCode());
            dto.setTestItemName(e.getTestItemName());
            dto.setReportItemId(e.getReportItemId());
            dto.setReportItemCode(e.getReportItemCode());
            dto.setReportItemName(e.getReportItemName());
            dto.setType(e.getTestResultType());
            dto.setResult(e.getResult());
            dto.setUnit(StringUtils.EMPTY);
            if (instrumentReportItems.containsKey(e.getReportItemCode())) {
                dto.setUnit(StringUtils.defaultString(instrumentReportItems.get(e.getReportItemCode()).getReportItemUnitName()));
            }
            dto.setRange(e.getRange());
            dto.setStatus(e.getStatus());
            dto.setInstrumentId(0L);
            dto.setInstrumentName(StringUtils.EMPTY);
            dto.setInstrumentResult(StringUtils.EMPTY);

            if (Objects.equals(context.getSource(), SaveResultSourceEnum.MACHINE)) {
                if (Objects.nonNull(context.getInstrument())) {
                    dto.setInstrumentId(context.getInstrument().getInstrumentId());
                    dto.setInstrumentName(StringUtils.defaultString(context.getInstrument().getInstrumentName()));
                }
                dto.setInstrumentResult(StringUtils.defaultString(context.getInstrumentResult()));
            }

            dto.setJudge(e.getJudge());
            dto.setCreateDate(new Date());
            dto.setUpdateDate(new Date());
            dto.setCreatorId(e.getCreatorId());
            dto.setCreatorName(e.getCreatorName());
            dto.setUpdaterId(e.getUpdaterId());
            dto.setUpdaterName(e.getUpdaterName());
            dto.setIsDelete(YesOrNoEnum.NO.getCode());

            SampleResultDto oldSampleResultDto = sampleResultDtoMap.getOrDefault(e.getReportItemCode(), new SampleResultDto());
            dto.setRange(StringUtils.defaultIfBlank(dto.getRange(), StringUtils.defaultString(oldSampleResultDto.getRange())));
            dto.setInstrumentReportItemReferenceId(
                Objects.requireNonNullElse(oldSampleResultDto.getInstrumentReportItemReferenceId(), NumberUtils.LONG_ZERO));

            return dto;
        }).collect(Collectors.toList());

        // 刷入复查结果进结果表
        sampleResultService.addSampleResults(results);
        context.put(SaveResultContext.SAMPLE_RESULT, results);

        // 刷新标记
        updateMissItemCommand.mark(context.getSample().getSampleId(),
                results.stream().map(e -> {
                    final ResultStatusDto k = new ResultStatusDto();
                    k.setReportItemCode(e.getReportItemCode());
                    k.setResult(e.getResult());
                    k.setIsException(Objects.equals(e.getStatus(), ResultStatusEnum.EXCEPTION.getCode())
                            ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                    k.setIsCritical(Objects.equals(e.getStatus(), ResultStatusEnum.CRISIS.getCode())
                            ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                    k.setJudge(e.getJudge());
                    k.setIsRetest(YesOrNoEnum.NO.getCode());
                    return k;
                }).collect(Collectors.toList()));


        return CONTINUE_PROCESSING;
    }
}
