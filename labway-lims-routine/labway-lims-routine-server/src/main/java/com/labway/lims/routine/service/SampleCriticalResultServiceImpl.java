package com.labway.lims.routine.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SelectSampleCriticalDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.mapper.TbSampleCriticalResultMapper;
import com.labway.lims.routine.mapstruct.SampleCriticalResultConverter;
import com.labway.lims.routine.model.TbSampleCriticalResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 样本危机值 Service impl
 *
 * <AUTHOR>
 * @since 2023/4/10 9:38
 */
@Slf4j
@DubboService
public class SampleCriticalResultServiceImpl implements SampleCriticalResultService {

    @Resource
    private TbSampleCriticalResultMapper tbSampleCriticalResultMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SystemParamService systemParamService;

    @Resource
    private SampleCriticalResultConverter sampleCriticalResultConverter;
    @Resource
    private SampleResultService sampleResultService;

    @Override
    public List<SampleCriticalResultDto> selectBySelectSampleCriticalDto(SelectSampleCriticalDto dto) {
        LambdaQueryWrapper<TbSampleCriticalResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(Objects.nonNull(dto.getTestDateStart()), TbSampleCriticalResult::getTestDate,
            dto.getTestDateStart());
        queryWrapper.le(Objects.nonNull(dto.getTestDateStart()), TbSampleCriticalResult::getTestDate,
            dto.getTestDateEnd());
        //新增状态
        queryWrapper.eq(Objects.nonNull(dto.getStatus()),TbSampleCriticalResult::getStatus,dto.getStatus());
        queryWrapper.eq(TbSampleCriticalResult::getOrgId, dto.getOrgId());
        queryWrapper.eq(TbSampleCriticalResult::getGroupId, dto.getGroupId());
        queryWrapper.eq(TbSampleCriticalResult::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByAsc(TbSampleCriticalResult::getCriticalValueId);
        return sampleCriticalResultConverter
            .sampleCriticalResultDtoListFromTbObj(tbSampleCriticalResultMapper.selectList(queryWrapper));
    }

    @Override
    public List<SampleCriticalResultDto> selectByCriticalValueIds(Collection<Long> criticalValueIds) {
        if (CollectionUtils.isEmpty(criticalValueIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSampleCriticalResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSampleCriticalResult::getCriticalValueId, criticalValueIds);
        queryWrapper.eq(TbSampleCriticalResult::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByAsc(TbSampleCriticalResult::getTestDate);
        return sampleCriticalResultConverter
            .sampleCriticalResultDtoListFromTbObj(tbSampleCriticalResultMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public SampleCriticalResultDto selectByCriticalValueId(long criticalValueId) {
        LambdaQueryWrapper<TbSampleCriticalResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSampleCriticalResult::getCriticalValueId, criticalValueId);
        queryWrapper.eq(TbSampleCriticalResult::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");
        return sampleCriticalResultConverter
            .sampleCriticalResultDtoFromTbObj(tbSampleCriticalResultMapper.selectOne(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByCriticalValueId(SampleCriticalResultDto sampleCriticalResultDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbSampleCriticalResult target = new TbSampleCriticalResult();
        BeanUtils.copyProperties(sampleCriticalResultDto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbSampleCriticalResultMapper.updateById(target) < 1) {
            throw new LimsException("修改危机值失败");
        }

        //如果不是危急值 这里需要将原来样本结果的状态改成异常
        if (Objects.nonNull(sampleCriticalResultDto.getIsCritical()) && YesOrNoEnum.NO.getCode() == sampleCriticalResultDto.getIsCritical()){
            SampleCriticalResultDto resultDto = selectByCriticalValueId(sampleCriticalResultDto.getCriticalValueId());
            if (resultDto != null) {
                //更新原样本结果状态值 为异常值
                SampleResultDto sampleResultDto = new SampleResultDto();
                sampleResultDto.setSampleId(resultDto.getSampleId());
                sampleResultDto.setSampleResultId(resultDto.getSampleResultId());
                sampleResultDto.setStatus(ResultStatusEnum.EXCEPTION.getCode());
                sampleResultService.updateBySampleResultId(sampleResultDto);
            }
        }
        log.info("用户 [{}] 修改危机值成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    public List<SampleCriticalResultDto> selectByGroupId(long groupId) {
        final LambdaQueryWrapper<TbSampleCriticalResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleCriticalResult::getGroupId, groupId);
        wrapper.eq(TbSampleCriticalResult::getIsDelete, YesOrNoEnum.NO.getCode());
//        wrapper.ne(TbSampleCriticalResult::getStatus, SampleStatusEnum.STOP_TEST.getCode());
        wrapper.orderByDesc(TbSampleCriticalResult::getCriticalValueId);
        return sampleCriticalResultConverter
            .sampleCriticalResultDtoListFromTbObj(tbSampleCriticalResultMapper.selectList(wrapper));
    }

    @Nullable
    @Override
    public SampleCriticalResultDto selectBySampleIdAndReportItemCode(long sampleId, String reportItemCode) {

        if (StringUtils.isBlank(reportItemCode)) {
            return null;
        }

        final LambdaQueryWrapper<TbSampleCriticalResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleCriticalResult::getSampleId, sampleId);
        wrapper.eq(TbSampleCriticalResult::getReportItemCode, reportItemCode);
        wrapper.eq(TbSampleCriticalResult::getIsDelete, YesOrNoEnum.NO.getCode());
        wrapper.last("limit 1");

        return sampleCriticalResultConverter
            .sampleCriticalResultDtoFromTbObj(tbSampleCriticalResultMapper.selectOne(wrapper));
    }

    @Override
    public List<SampleCriticalResultDto> selectBySampleId(long sampleId) {
        LambdaQueryWrapper<TbSampleCriticalResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleCriticalResult::getSampleId, sampleId);
        wrapper.eq(TbSampleCriticalResult::getIsDelete, YesOrNoEnum.NO.getCode());
        return sampleCriticalResultConverter
            .sampleCriticalResultDtoListFromTbObj(tbSampleCriticalResultMapper.selectList(wrapper));
    }

    @Override
    public List<SampleCriticalResultDto> selectBySampleIds(Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSampleCriticalResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbSampleCriticalResult::getSampleId, sampleIds);
        wrapper.eq(TbSampleCriticalResult::getIsDelete, YesOrNoEnum.NO.getCode());
        return sampleCriticalResultConverter
            .sampleCriticalResultDtoListFromTbObj(tbSampleCriticalResultMapper.selectList(wrapper));
    }

    @Override
    public long addSampleCriticalResult(SampleCriticalResultDto dto) {

        final String key = redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":addSampleCriticalResult:"
            + dto.getSampleId() + "-" + dto.getReportItemCode();

        if (BooleanUtils
            .isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalStateException("正在添加危机值中");
        }

        try {

            final LoginUserHandler.User user = LoginUserHandler.get();
            final TbSampleCriticalResult criticalResult =
                JSON.parseObject(JSON.toJSONString(dto), TbSampleCriticalResult.class);
            criticalResult.setUpdateDate(new Date());
            criticalResult.setUpdaterId(user.getUserId());
            criticalResult.setUpdaterName(user.getNickname());
            criticalResult.setCreateDate(new Date());
            criticalResult.setCreatorId(user.getUserId());
            criticalResult.setCreatorName(user.getNickname());
            criticalResult.setIsDelete(YesOrNoEnum.NO.getCode());
            criticalResult.setCriticalValueId(snowflakeService.genId());
            tbSampleCriticalResultMapper.insert(criticalResult);

            return criticalResult.getCriticalValueId();
        } finally {
            stringRedisTemplate.delete(key);
        }
    }

    @Override
    public void deleteByCriticalValueId(long criticalValueId) {
        tbSampleCriticalResultMapper.deleteById(criticalValueId);
        log.info("用户 [{}] 删除危机值成功 [{}]", LoginUserHandler.get().getNickname(), criticalValueId);
    }

    @Override
    public void deleteBySampleIdAndReportItemId(long sampleId, long reportItemId) {
        tbSampleCriticalResultMapper
            .delete(new LambdaQueryWrapper<TbSampleCriticalResult>().eq(TbSampleCriticalResult::getSampleId, sampleId)
                .eq(TbSampleCriticalResult::getReportItemId, reportItemId));

        log.info("用户 [{}] 删除危机值成功 sampleId [{}] reportItemId [{}]", LoginUserHandler.get().getNickname(), sampleId,
            reportItemId);

    }

    @Override
    public void deleteBySampleIds(Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return;
        }
        tbSampleCriticalResultMapper.delete(
            new LambdaQueryWrapper<TbSampleCriticalResult>().in(TbSampleCriticalResult::getSampleId, sampleIds));

        log.info("用户 [{}] 删除危机值成功", LoginUserHandler.get().getNickname());
    }

    @Override
    public void deleteBySampleIdAndReportItemCode(long sampleId, String reportItemCode) {
        tbSampleCriticalResultMapper
            .delete(new LambdaQueryWrapper<TbSampleCriticalResult>().eq(TbSampleCriticalResult::getSampleId, sampleId)
                .eq(TbSampleCriticalResult::getReportItemCode, reportItemCode));

        log.info("用户 [{}] 删除危机值成功 sampleId [{}] reportItemId [{}]", LoginUserHandler.get().getNickname(), sampleId,
            reportItemCode);
    }

    @Override
    public void updateByCriticalValueIds(SampleCriticalResultDto criticalResult, Collection<Long> updateCriIds) {

        if (org.springframework.util.CollectionUtils.isEmpty(updateCriIds)) {
            return;
        }

        LambdaUpdateWrapper<TbSampleCriticalResult> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(TbSampleCriticalResult::getStatus,criticalResult.getStatus());
        //根据是否处理回读 进行字段赋值
        if (criticalResult.isNeedHandleReadBack()){
            updateWrapper.set(TbSampleCriticalResult::getIsReadBack,criticalResult.getIsReadBack());
            updateWrapper.set(TbSampleCriticalResult::getReadBackTime,criticalResult.getReadBackTime());
            updateWrapper.set(TbSampleCriticalResult::getReadBackUser,criticalResult.getReadBackUser());
            updateWrapper.set(TbSampleCriticalResult::getRemark,criticalResult.getRemark());

        }
        updateWrapper.in(TbSampleCriticalResult::getCriticalValueId, updateCriIds);

        tbSampleCriticalResultMapper.update(null, updateWrapper);

//        tbSampleCriticalResultMapper.updateByCriticalValueIds(criticalResult, updateCriIds);

        log.info("用户 [{}] 修改样本报告项目危急值 [{}] 成功 id {}", LoginUserHandler.get().getNickname(),
            JSON.toJSONString(criticalResult), updateCriIds);

    }

    @Override
    public void updateByApplyId(SampleCriticalResultDto sampleCriticalResultDto) {
        LambdaUpdateWrapper<TbSampleCriticalResult> wrapper = Wrappers.lambdaUpdate(TbSampleCriticalResult.class)
                .eq(TbSampleCriticalResult::getApplyId, sampleCriticalResultDto.getApplyId())
                .eq(TbSampleCriticalResult::getIsDelete,0)
                .set(TbSampleCriticalResult::getHspOrgId, sampleCriticalResultDto.getHspOrgId())
                .set(TbSampleCriticalResult::getHspOrgName,sampleCriticalResultDto.getHspOrgName())
                .set(TbSampleCriticalResult::getUpdaterId,sampleCriticalResultDto.getUpdaterId())
                .set(TbSampleCriticalResult::getUpdaterName,sampleCriticalResultDto.getUpdaterName())
                .set(TbSampleCriticalResult::getUpdateDate,sampleCriticalResultDto.getUpdateDate());
        tbSampleCriticalResultMapper.update(null, wrapper);
    }

    @Override
    public void updateByApplyIds(SampleCriticalResultDto sampleCriticalResultDto, Collection<Long> applyIds) {
        for (List<Long> item : Lists.partition(new ArrayList<>(applyIds), 500)) {
            LambdaUpdateWrapper<TbSampleCriticalResult> wrapper = Wrappers.lambdaUpdate(TbSampleCriticalResult.class)
                .in(TbSampleCriticalResult::getApplyId, item).eq(TbSampleCriticalResult::getIsDelete, 0)
                .set(TbSampleCriticalResult::getHspOrgId, sampleCriticalResultDto.getHspOrgId())
                .set(TbSampleCriticalResult::getHspOrgName, sampleCriticalResultDto.getHspOrgName())
                .set(TbSampleCriticalResult::getUpdaterId, sampleCriticalResultDto.getUpdaterId())
                .set(TbSampleCriticalResult::getUpdaterName, sampleCriticalResultDto.getUpdaterName())
                .set(TbSampleCriticalResult::getUpdateDate, sampleCriticalResultDto.getUpdateDate());
            tbSampleCriticalResultMapper.update(null, wrapper);
        }
    }

    @Override
    public void deleteByCriticalValueIds(List<Long> criticalValueIds) {
        tbSampleCriticalResultMapper.deleteBatchIds(criticalValueIds);
        log.info("用户 [{}] 删除危机值成功 [{}]", LoginUserHandler.get().getNickname(), criticalValueIds);
    }

    @Override
    public int getTimeoutConfig() {
        //1.1.3 获取全局判断超时时间
        final SystemParamDto timeOutParam = systemParamService.selectByParamName(SystemParamNameEnum.CRITICAL_RESULT_TIME_OUT.getCode(),
                LoginUserHandler.get().getOrgId());

        if (Objects.nonNull(timeOutParam) && StringUtils.isNotEmpty(timeOutParam.getParamValue())){

            try {
                //超时配置(分钟)
                return Integer.parseInt(timeOutParam.getParamValue());
            }catch (NumberFormatException e){
                log.error("配置时间格式不对,本次判断超时跳过处理");
            }
        }
        return 0;
    }

}
