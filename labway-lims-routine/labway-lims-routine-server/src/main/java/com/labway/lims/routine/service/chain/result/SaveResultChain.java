package com.labway.lims.routine.service.chain.result;

import com.labway.lims.routine.service.chain.auto.audit.AutoAuditChain;
import com.labway.lims.routine.service.chain.result.retest.CheckIsFcCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/3/30 16:10
 */
@Slf4j
@Component
public class SaveResultChain extends ChainBase implements InitializingBean {
    @Resource
    private CheckParamsCommand checkParamsCommand;
    @Resource
    private CheckCanSaveCommand checkCanSaveCommand;
    @Resource
    private ConvertResultCommand convertResultCommand;
    @Resource
    private FillSampleCommand fillSampleCommand;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;
    @Resource
    private NumberResultCommand numberResultCommand;
    @Resource
    private RecalculateRefResultCommand recalculateRefResultCommand;
    @Resource
    private RemoveBeforeResultCommand removeBeforeResultCommand;
    @Resource
    private ConvertExceptionResultChain convertExceptionResultChain;
    @Resource
    private ResultFormatCommand resultFormatCommand;
    @Resource
    private SavePGSQLResultCommand savePGSQLResultCommand;
    @Resource
    private SaveSampleFlowCommand sampleFlowCommand;
    @Resource
    private UpdateSampleCommand updateSampleCommand;
    @Resource
    private YinYangResultCommand yinYangResultCommand;
    @Resource(name = "routine-checkIsFcCommand")
    private CheckIsFcCommand checkIsFcCommand;
    @Resource
    private GenCriticalCommand genCriticalCommand;
    @Resource
    private UpdateMissItemCommand updateMissItemCommand;

    @Resource
    private SelectResultAllChain selectResultAllChain;

	@Resource
	private AutoAuditChain autoAuditChain;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 校验参数
        addCommand(checkParamsCommand);
        // 获取样本基本信息
        addCommand(fillSampleCommand);
        // 是否复查
        addCommand(checkIsFcCommand);
        // 结果转换
        addCommand(convertResultCommand);
        // 校验参数
        addCommand(checkCanSaveCommand);
        // 获取参考范围
        addCommand(instrumentReportReferenceCommand);
        // 查询结果数据
        addCommand(numberResultCommand.getDefaultProvider());
        // 结果查询数据
        addCommand(numberResultCommand);
        // 阴阳结果
        addCommand(yinYangResultCommand);
        // 结果格式化
        addCommand(resultFormatCommand);
        // 删除之前的结果
        addCommand(removeBeforeResultCommand);
        // 如果结果包特定字符，并且 judge 为空的时候，那么给个上箭头
        addCommand(convertExceptionResultChain);
        // 保存pgsql
        addCommand(savePGSQLResultCommand);
        // 修改redis缺项的结果
        addCommand(updateMissItemCommand);
        // 添加危机值
        addCommand(genCriticalCommand);
        // 调用自己
        addCommand(recalculateRefResultCommand.getDefaultChainProvider());
        // 计算结果
        addCommand(recalculateRefResultCommand);
        // 跟新样本结果状态
        addCommand(updateSampleCommand);
        // 添加 返回的 结果信息
        addCommand(selectResultAllChain);
        // 记录修改日志
        addCommand(sampleFlowCommand);
		// 自动审核
		addCommand(autoAuditChain);

        // 结束
        addCommand(c -> PROCESSING_COMPLETE);
    }
}
