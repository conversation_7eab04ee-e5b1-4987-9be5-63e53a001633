package com.labway.lims.routine.service.chain.retest;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.routine.api.dto.*;
import com.labway.lims.routine.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/26 16:31
 * 复查上下文
 */

@Getter
@Setter
@SuppressWarnings("unchecked")
public class StartRetestContext extends StopWatchContext {

    /**
     * 申请单样本
     */
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    /**
     * 样本
     */
    public static final String SAMPLE = "SAMPLE_" + IdUtil.objectId();

    /**
     * 复查信息对象
     */
    public static final String RETESET_INFO = "RETESET_INFO_"+ IdUtil.objectId();

    /**
     * 样本报告项目集合
     */
    static final String SAMPLE_REPORT_ITEMS = "SAMPLE_REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 样本报告项目集合
     */
    static final String INSTRUMENT_REPORT_ITEMS = "INSTRUMENT_REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 样本结果
     */
    static final String SAMPLE_REPORT_ITEM_RESULTS = "SAMPLE_REPORT_ITEM_RESULTS_" + IdUtil.objectId();

    /**
     * 需要复查的样本报告项目集合
     */
    static final String RETEST_SAMPLE_REPORT_ITEMS = "RETEST_SAMPLE_REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 所有复查记录主表
     */
    static final String RETEST_MAIN_RECORDS = "RETEST_MAIN_RECORDS_" + IdUtil.objectId();


    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }

    public SampleDto getSample() {
        return (SampleDto) get(SAMPLE);
    }

    /**
     * 返回复查的样本信息
     * @return
     */
    public StartReTestDto getRetestInfo(){
        return (StartReTestDto) get(RETESET_INFO);
    }

    public List<SampleRetestMainDto> getSampleRetestMains() {
        return (List<SampleRetestMainDto>) get(RETEST_MAIN_RECORDS);
    }
    /**
     * 所有报告项目
     */
    public List<SampleReportItemDto> getSampleReportItems() {
        return (List<SampleReportItemDto>) get(SAMPLE_REPORT_ITEMS);
    }

    /**
     * 所有报告项目
     */
    public List<SampleResultDto> getSampleReportItemResults() {
        return (List<SampleResultDto>) get(SAMPLE_REPORT_ITEM_RESULTS);
    }

    /**
     * 所有仪器报告项目
     */
    public List<InstrumentReportItemDto> getInstrumentReportItems() {
        return (List<InstrumentReportItemDto>) get(INSTRUMENT_REPORT_ITEMS);
    }

    /**
     * 需要复查的报告项目
     */
    public List<SampleReportItemDto> getRetestSampleReportItems() {
        return (List<SampleReportItemDto>) get(RETEST_SAMPLE_REPORT_ITEMS);
    }


    public static StartRetestContext from(Context context) {
        return (StartRetestContext) context;
    }


    @Override
    protected String getWatchName() {
        return "样本复查";
    }
}
