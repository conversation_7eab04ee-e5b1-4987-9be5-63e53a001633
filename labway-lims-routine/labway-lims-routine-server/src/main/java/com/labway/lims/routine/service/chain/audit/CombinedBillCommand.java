package com.labway.lims.routine.service.chain.audit;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class CombinedBillCommand implements Command {


    @Override
    public boolean execute(Context c) throws Exception {
        AuditSampleContext context = AuditSampleContext.from(c);
        SampleAuditDto auditVo = context.getParam();
        ApplySampleDto applySample = context.getApplySample();

        if (!Objects.equals(auditVo.getAuditStatus(), SampleAuditStatusEnum.TWO_CHECK.name())
            && StringUtils.isNotBlank(applySample.getMergeMasterBarcode())
        ) {
            throw new IllegalStateException("被合并条码不能进行二审");
        }
        return CONTINUE_PROCESSING;
    }
}
