package com.labway.lims.routine.service.chain.result;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.ExceptionCodeEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;
import com.labway.lims.base.api.enums.RelationalOperatorEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 结果转换
 *
 * <AUTHOR>
 * @since 2023/3/30 16:16
 */
@Slf4j
@Component
public class ConvertResultCommand implements Command {

    @Resource
    private NumberResultCommand numberResultCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);
        // 获取转换规则
        List<InstrumentReportItemResultExchangeDto> instrumentReportItemResultExchangeDtos = JSON.parseArray(JSON.toJSONString(context.getResultExchange()), InstrumentReportItemResultExchangeDto.class);

        if (Objects.nonNull(context.getResultExchange())) {
            final Map<String, String> map = instrumentReportItemResultExchangeDtos.stream()
                    .collect(Collectors.toMap(InstrumentReportItemResultExchangeDto::getInstrumentResult, InstrumentReportItemResultExchangeDto::getExchangeResult,
                            (a, b) -> a));
            // 如果为空那么不转换
            context.setResult(ObjectUtils.defaultIfNull(map.get(context.getResult()), context.getResult()));
        }

        /*
         * 这里根据结果值操作运算符进行匹配转换结果值
         */
        // 结果值
        String result = context.getResult();
        List<String> convertMsgList = new ArrayList<>();

        // 如果是计算结果，先进行计算
        if (StringUtils.isBlank(result) && context.getSource() == SaveResultSourceEnum.CODE) {
            numberResultCommand.getDefaultProvider().execute(context);

            // 计算结果，在这里先计算
            final BigDecimal calcResult = numberResultCommand.calc(context, null);
            result = calcResult.toPlainString();
        }

        for (InstrumentReportItemResultExchangeDto instrumentReportItemResultExchangeDto : instrumentReportItemResultExchangeDtos) {
            curTip(instrumentReportItemResultExchangeDto, result, convertMsgList);
        }

        // 如果匹配到多个转换规则，则报错提示
        if (CollectionUtils.isNotEmpty(convertMsgList) && convertMsgList.size() > 1){
            throw new IllegalStateException(String.format(ExceptionCodeEnum.CONFLICT_RESULT_CONVERT.getDesc(),String.join(",", convertMsgList)));
        }

        context.setResult(CollectionUtils.isEmpty(convertMsgList) ? result : convertMsgList.get(0));

        return CONTINUE_PROCESSING;
    }



    private void curTip(InstrumentReportItemResultExchangeDto itemTip, String result, List<String> convertMsgList) {
        final RelationalOperatorEnum operatorMaxEnum = RelationalOperatorEnum.valueOfByOperator(itemTip.getFormulaMax());

        // 包含
        if (Objects.equals(operatorMaxEnum, RelationalOperatorEnum.CONTAINS)) {
            if (StringUtils.contains(result, itemTip.getFormulaMaxValue())) {
                convertMsgList.add(itemTip.getExchangeResult());
            }
        } else if (Objects.equals(operatorMaxEnum, RelationalOperatorEnum.DOES_NOT_CONTAINS)) { // 不包含
            if (!StringUtils.contains(result, itemTip.getFormulaMaxValue())) {
                convertMsgList.add(itemTip.getExchangeResult());
            }
        } else {

            // 如果都是数字的时候，那么转成数字去匹配
            Object value = result;
            Object maxValue = itemTip.getFormulaMaxValue();
            if (NumberUtils.isParsable(result)
                    && NumberUtils.isParsable(itemTip.getFormulaMaxValue())) {
                value = new BigDecimal(result);
                maxValue = new BigDecimal(itemTip.getFormulaMaxValue());
            }


            if (Objects.equals(operatorMaxEnum, RelationalOperatorEnum.EQ)) {
                if (Objects.equals(value, maxValue)) {
                    convertMsgList.add(itemTip.getExchangeResult());
                } else if (value instanceof BigDecimal && ((BigDecimal) value).compareTo((BigDecimal) maxValue) == NumberUtils.INTEGER_ZERO) {
                    convertMsgList.add(itemTip.getExchangeResult());
                }
                return;
            }

            // 判断大小的时候必须是数字
            if (!NumberUtils.isParsable(result) || !NumberUtils.isParsable(itemTip.getFormulaMaxValue())) {
                return;
            }
            // 第二个范围不等于空 但是不是数字就返回
            if (StringUtils.isNotBlank(itemTip.getFormulaMinValue()) && !NumberUtils.isParsable(itemTip.getFormulaMinValue())) {
                return;
            }


            if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.LT) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) < NumberUtils.INTEGER_ZERO)) {
                convertMsgList.add(itemTip.getExchangeResult());
            } else if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.LE) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) <= NumberUtils.INTEGER_ZERO)) {
                convertMsgList.add(itemTip.getExchangeResult());
            }


            BigDecimal minValue = NumberUtils.isParsable(itemTip.getFormulaMinValue()) ? new BigDecimal(itemTip.getFormulaMinValue()) : null;

            if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.GT) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) > NumberUtils.INTEGER_ZERO)) {
                curMinTip(minValue, convertMsgList, itemTip, (BigDecimal) value);
            } else if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.GE) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) >= NumberUtils.INTEGER_ZERO)) {
                curMinTip(minValue, convertMsgList, itemTip, (BigDecimal) value);
            }

        }
    }

    private void curMinTip(BigDecimal minValue, List<String> errorMsgList, InstrumentReportItemResultExchangeDto itemTip, BigDecimal value) {
        final RelationalOperatorEnum operatorMinEnum = RelationalOperatorEnum.valueOfByOperator(itemTip.getFormulaMin());

        if (Objects.isNull(operatorMinEnum) || Objects.isNull(minValue)) {
            errorMsgList.add(itemTip.getExchangeResult());
            return;
        }

        if (Objects.equals(operatorMinEnum, RelationalOperatorEnum.LT) && (value.compareTo(minValue) < NumberUtils.INTEGER_ZERO)) {
            errorMsgList.add(itemTip.getExchangeResult());
        } else if (Objects.equals(operatorMinEnum, RelationalOperatorEnum.LE) && (value.compareTo(minValue) <= NumberUtils.INTEGER_ZERO)) {
            errorMsgList.add(itemTip.getExchangeResult());
        }
    }


}
