package com.labway.lims.routine.controller;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.InstrumentReportItemCommonPhraseDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemCommonPhraseService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.routine.api.dto.ReportItemDeleteDto;
import com.labway.lims.routine.api.dto.ResultStatusDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SaveResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.service.SampleServiceImpl;
import com.labway.lims.routine.service.chain.result.UpdateMissItemCommand;
import com.labway.lims.routine.service.chain.retest.StartRetestUpdateMissItemCommand;
import com.labway.lims.routine.vo.AddSampleReportItemVo;
import com.labway.lims.routine.vo.SampleReportItemVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/30 10:18
 */
@Slf4j
@RestController
@RequestMapping("/sample-report-item")
public class SampleReportItemController extends BaseController {
    @Resource
    private SampleServiceImpl sampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private ReportItemService reportItemService;
    @Resource
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private InstrumentReportItemCommonPhraseService instrumentReportItemCommonPhraseService;
    @Resource
    private UpdateMissItemCommand updateMissItemCommand;
    @Resource
    private StartRetestUpdateMissItemCommand startRetestUpdateMissItemCommand;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private InstrumentReportItemService instrumentReportItemService;
    @Resource
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RedisPrefix redisPrefix;

    @PostMapping("/add")
    public Object addSampleReportItem(@RequestBody AddSampleReportItemVo vo) {

        final String key = redisPrefix.getBasePrefix() + getClass().getSimpleName() + vo.getSampleId();
        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException("正在添加项目,请稍后重试");
        }

        if (CollectionUtils.isEmpty(vo.getReportItems())) {
            throw new IllegalArgumentException("请选择报告项目");
        }

        //根据选择的仪器做汇总
        final List<InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService.selectByInstrumentIds(
                vo.getReportItems().stream().map(SampleReportItemVo::getInstrumentId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(instrumentReportItems)) {
            throw new IllegalStateException("仪器报告项目不存在");
        }

        try {
            final SampleDto sample = sampleService.selectBySampleId(vo.getSampleId());

            if (Objects.isNull(sample)) {
                throw new IllegalStateException("样本不存在");
            }

            final List<SampleReportItemDto> items = sampleReportItemService.selectBySampleId(sample.getSampleId());
            final List<String> addItemCodes = vo.getReportItems().stream().map(SampleReportItemVo::getReportItemCode).collect(Collectors.toList());

            for (SampleReportItemDto item : items) {
                if (addItemCodes.contains(item.getReportItemCode())) {
                    throw new IllegalStateException(String.format("报告项目 [%s] 已添加", item.getReportItemName()));
                }
            }

            // 获取到所有检验项目
            final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(sample.getApplySampleId());

            // 然后获取到这个检验项目下面的报告项目
            final Map<String, ReportItemDto> reportItems = reportItemService.selectByTestItemCodes(applySampleItems.stream()
                            .map(ApplySampleItemDto::getTestItemCode).collect(Collectors.toSet()),
                    sample.getOrgId()).stream().collect(Collectors.toMap(ReportItemDto::getReportItemCode, v -> v, (a, b) -> a));


            final Map<String, List<InstrumentReportItemDto>> instrumentReportMap = instrumentReportItems.stream().collect(Collectors.groupingBy(InstrumentReportItemDto::getReportItemCode));

            final List<SampleReportItemDto> sampleReportItems = new ArrayList<>();
            final LinkedList<Long> ids = snowflakeService.genIds(vo.getReportItems().size() + 10);
            final List<Long> instrumentReportItemIds = new ArrayList<>();
            for (SampleReportItemVo e : vo.getReportItems()) {
                final SampleReportItemDto sri = new SampleReportItemDto();
                sri.setSampleReportItemId(ids.pop());
                sri.setApplyId(sample.getApplyId());
                sri.setSampleId(sample.getSampleId());
                sri.setApplySampleId(sample.getApplySampleId());
                sri.setReportItemCode(e.getReportItemCode());
                sri.setReportItemName(e.getReportItemName());

                final ReportItemDto reportItem = reportItems.get(e.getReportItemCode());
                if (Objects.nonNull(reportItem)) {
                    sri.setReportItemId(reportItem.getReportItemId());
                    sri.setTestItemId(reportItem.getTestItemId());
                    sri.setTestItemCode(reportItem.getTestItemCode());
                    sri.setTestItemName(reportItem.getTestItemName());
                } else {
                    sri.setReportItemId(NumberUtils.LONG_ZERO);
                    sri.setTestItemId(applySampleItems.iterator().next().getTestItemId());
                    sri.setTestItemCode(applySampleItems.iterator().next().getTestItemCode());
                    sri.setTestItemName(applySampleItems.iterator().next().getTestItemName());
                }

                sri.setIsRetest(RetestStatusEnum.NORMAL.getCode());
                sri.setIsDelete(YesOrNoEnum.NO.getCode());

                final InstrumentReportItemDto instrumentReportItem = InstrumentReportItemDto.getInstrumentReportItemDto(instrumentReportMap, e.getReportItemCode(), e.getInstrumentId());
                if (Objects.nonNull(instrumentReportItem)) {
                    instrumentReportItemIds.add(instrumentReportItem.getInstrumentReportItemId());
                    sri.setPrintSort(instrumentReportItem.getPrintSort());
                }else {
                    throw new IllegalStateException(String.format("仪器报告项目编码【%s】不存在",e.getReportItemCode()));
                }

                sampleReportItems.add(sri);
            }


            // 批量保存
            sampleReportItemService.addSampleReportItems(sampleReportItems);

            // 常用短语
            final List<InstrumentReportItemCommonPhraseDto> phrases = instrumentReportItemCommonPhraseService.selectByInstrumentReportItemIds(instrumentReportItemIds);

            // 设置默认值
            for (SampleReportItemDto reportItem : sampleReportItems) {

                final InstrumentReportItemCommonPhraseDto instrumentReportItemCommonPhrase = phrases.stream().filter(e -> Objects.equals(e.getReportItemCode(), reportItem.getReportItemCode()))
                        .filter(e -> Objects.equals(e.getIsDefault(), YesOrNoEnum.YES.getCode()))
                        .findFirst().orElse(null);
                if (Objects.isNull(instrumentReportItemCommonPhrase)) {
                    final ResultStatusDto dto = new ResultStatusDto();
                    dto.setReportItemCode(reportItem.getReportItemCode());
                    dto.setResult(StringUtils.EMPTY);
                    dto.setIsCritical(YesOrNoEnum.NO.getCode());
                    dto.setIsException(YesOrNoEnum.NO.getCode());
                    dto.setIsRetest(YesOrNoEnum.NO.getCode());
                    dto.setJudge(StringUtils.EMPTY);
                    updateMissItemCommand.mark(vo.getSampleId(), dto);
                    continue;
                }

                final SaveResultDto saveResult = new SaveResultDto();
                saveResult.setSampleId(sample.getSampleId());
                saveResult.setApplySampleId(sample.getApplySampleId());
                saveResult.setApplyId(sample.getApplyId());
                saveResult.setReportItemId(reportItem.getReportItemId());
                saveResult.setReportItemCode(reportItem.getReportItemCode());
                saveResult.setResult(instrumentReportItemCommonPhrase.getContent());
                saveResult.setDate(new Date());

                try {
                    // 默认值
                    sampleResultService.saveResult(saveResult, SaveResultSourceEnum.DEFAULT_VALUE);
                } catch (Exception e) {
                    log.error("条码 [{}] 报告项目 [{}] 设置默认值失败", sample.getBarcode(), reportItem.getReportItemName(), e);
                }
            }


            // 添加报告项目流水
            final StringBuilder sb = new StringBuilder();

            sampleReportItems.forEach(e -> sb.append(String.format("编码 [%s] 名称 [%s] 检验项目 [%s]\n", e.getReportItemCode(),
                    e.getReportItemName(), e.getTestItemName())));


            final SampleFlowDto flow = new SampleFlowDto();
            flow.setApplyId(sample.getApplyId());
            flow.setSampleFlowId(ids.pop());
            flow.setApplySampleId(sample.getApplySampleId());
            flow.setOperateCode(BarcodeFlowEnum.REPORT_ITEM_ADD.name());
            flow.setOperateName(BarcodeFlowEnum.REPORT_ITEM_ADD.getDesc());
            flow.setBarcode(sample.getBarcode());
            flow.setContent(sb.toString());

            sampleFlowService.addSampleFlow(flow);
        } finally {
            stringRedisTemplate.delete(key);
        }

        return Map.of();
    }

    @PostMapping("/report-items")
    public Object getReportItems(@RequestParam("sampleId") Long sampleId) {
        if (Objects.isNull(sampleId)) {
            return Collections.emptyList();
        }
        return sampleReportItemService.selectBySampleId(sampleId);
    }

    @PostMapping("/delete")
    public Object deleteReportItem(@RequestBody ReportItemDeleteDto dto) {
        if (Objects.isNull(dto.getSampleReportItemId()) || Objects.isNull(dto.getSampleId())
                || StringUtils.isBlank(dto.getReportItemCode())) {
            throw new IllegalStateException("参数错误");
        }
        // 删除报告项目和结果， 并且增加到删除记录中
        sampleReportItemService.deleteSampleReportItem(dto, ItemTypeEnum.ROUTINE);

        // 删除缺项
        stringRedisTemplate.opsForHash().delete(startRetestUpdateMissItemCommand.getMissItemKey(dto.getSampleId()),
                dto.getReportItemCode());

        stringRedisTemplate.opsForHash().delete(updateMissItemCommand.getMissItemKey(dto.getSampleId()),
                dto.getReportItemCode());

        return Collections.emptyMap();
    }

}
