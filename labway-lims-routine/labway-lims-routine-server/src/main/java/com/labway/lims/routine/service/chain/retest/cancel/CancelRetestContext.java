package com.labway.lims.routine.service.chain.retest.cancel;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import javax.annotation.Nullable;
import java.util.List;

@Getter
@Setter
@SuppressWarnings("unchecked")
public class CancelRetestContext extends StopWatchContext {

    static final String SAMPLE_RETEST_MAIN = IdUtil.objectId();

    static final String SAMPLE_REPORT_ITEMS = IdUtil.objectId();

    /**
     * 本轮正在复查的
     */
    static final String SAMPLE_RETEST_ITEMS = IdUtil.objectId();

    /**
     * 本轮取消复查复查的
     */
    static final String SAMPLE_UN_RETEST_ITEMS = IdUtil.objectId();

    static final String SAMPLE = IdUtil.objectId();

    private long sampleId;

    /**
     * 为空表示取消复查整个样本
     */
    @Nullable
    private String reportItemCode;

    public static CancelRetestContext from(Context context) {
        return (CancelRetestContext) context;
    }

    public SampleRetestMainDto getSampleRetestMain() {
        return (SampleRetestMainDto) get(SAMPLE_RETEST_MAIN);
    }

    public List<SampleReportItemDto> getSampleReportItems() {
        return (List<SampleReportItemDto>) get(SAMPLE_REPORT_ITEMS);
    }

    public List<SampleRetestItemDto> getSampleRetestItems() {
        return (List<SampleRetestItemDto>) get(SAMPLE_RETEST_ITEMS);
    }
    public List<String> getSampleUnRetestItemNames() {
        return (List<String>) get(SAMPLE_UN_RETEST_ITEMS);
    }

    public SampleDto getSample() {
        return (SampleDto) get(SAMPLE);
    }

    @Override
    protected String getWatchName() {
        return "取消复查";
    }
}
