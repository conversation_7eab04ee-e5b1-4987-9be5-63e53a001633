package com.labway.lims.routine.vo;

import com.labway.lims.api.enums.routine.ReadBackStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 危机值回读
 * 
 * <AUTHOR>
 * @since 2024/8/14 14:34
 */
@Getter
@Setter
public class SampleCriticalReadBackVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 危急值ID
     */
    private Long criticalValueId;

    /**
     * 是否回读
     * @see ReadBackStatusEnum
     */
    private Integer isReadBack;

    /**
     * 备注
     */
    private String remark;

}
