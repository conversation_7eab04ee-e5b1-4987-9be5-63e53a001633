package com.labway.lims.routine.service.chain.result.retest;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.routine.api.dto.ResultStatusDto;
import com.labway.lims.routine.service.chain.result.UpdateMissItemCommand;
import com.labway.lims.routine.service.chain.retest.StartRetestUpdateMissItemCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class RetestResultRedisMarkCommand implements Command {


    @Resource
    private UpdateMissItemCommand updateMissItemCommand;
    @Resource
    private StartRetestUpdateMissItemCommand startRetestUpdateMissItemCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final RetestResultContext context = RetestResultContext.from(c);

        final ResultStatusDto s = new ResultStatusDto();
        s.setReportItemCode(context.getReportItemCode());
        s.setResult(context.getResult());
        s.setJudge(context.getResultJudge());
        s.setIsRetest(YesOrNoEnum.YES.getCode());
        s.setIsException(context.isException() ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        s.setIsCritical(context.isCritical() ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());

        updateMissItemCommand.mark(startRetestUpdateMissItemCommand.getMissItemKey(context.getSample().getSampleId())
                , List.of(s));

        return CONTINUE_PROCESSING;
    }

}
