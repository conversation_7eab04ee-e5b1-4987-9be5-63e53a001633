package com.labway.lims.routine.service.chain.result;

import com.labway.lims.base.api.dto.InstrumentReportItemRemarkDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <pre>
 * ResultRemarkCommand
 * 结果备注
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/11/27 9:56
 */
@Slf4j
@Component
public class ResultRemarkCommand implements Command {
    private final ExpressionParser expressionParser = new SpelExpressionParser();

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        InstrumentReportItemRemarkDto reportItemRemark = context.getInstrumentReportItemRemark();

        try {
            if (Objects.isNull(reportItemRemark)) {
                return CONTINUE_PROCESSING;
            }

            context.put(SaveResultContext.RESULT_REMARK, reportItemRemark);
        } catch (Exception e) {
            log.info("匹配结果备注异常", e);
        }

        return CONTINUE_PROCESSING;
    }
}
