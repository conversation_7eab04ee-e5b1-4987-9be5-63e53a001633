package com.labway.lims.routine.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <pre>
 * AddQcSampleResultVo
 * 新增质控结果
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/2 14:34
 */
@Getter
@Setter
public class AddQcSampleResultVo implements Serializable {

    /**
     * 检验时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDate;

    /**
     * 结果
     */
    private String result;

    /**
     * 低浓度
     */
    private String low;

    /**
     * 中浓度
     */
    private String medium;

    /**
     * 高浓度
     */
    private String high;

    /**
     * 仪器id
     */
    private Long instrumentId;
    /**
     * 仪器code
     */
    private String instrumentCode;
    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器报告项目code
     */
    private String reportItemCode;

    /**
     * 仪器报告项目名称
     */
    private String reportItemName;

    public void checkParam() {
        if (Objects.isNull(testDate)) {
            throw new IllegalArgumentException("检验时间不能为空");
        }

        if (StringUtils.isAllBlank(low, medium, high)) {
            throw new IllegalArgumentException("质控结果不能为空");
        }

        if (Objects.isNull(instrumentId)) {
            throw new IllegalArgumentException("仪器ID不能为空");
        }

        if (StringUtils.isBlank(instrumentName)) {
            throw new IllegalArgumentException("仪器名称不能为空");
        }

        if (StringUtils.isBlank(reportItemCode)) {
            throw new IllegalArgumentException("仪器报告项目编码不能为空");
        }

        if (StringUtils.isBlank(reportItemName)) {
            throw new IllegalArgumentException("仪器报告项目名称不能为空");
        }
    }
}
