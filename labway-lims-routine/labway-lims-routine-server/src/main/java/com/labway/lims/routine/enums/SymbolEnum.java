package com.labway.lims.routine.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SymbolEnum{
    LT("<", "<"),
    LE("<=","≤"),
    GT(">",">"),
    GE(">=","≥"),
    ;

    private final String symbol;

    private final String resultSymbol;

    public static String getSymbol(String symbol) {
        for (SymbolEnum symbolEnum : SymbolEnum.values()) {
            if (symbolEnum.getSymbol().equals(symbol)) {
                return symbolEnum.getResultSymbol();
            }
        }
        return "";
    }
}