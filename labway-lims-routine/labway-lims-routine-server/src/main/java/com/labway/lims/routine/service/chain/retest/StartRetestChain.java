package com.labway.lims.routine.service.chain.retest;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/5/26 16:36
 */
@Slf4j
@Component
public class StartRetestChain extends ChainBase implements InitializingBean {
    @Resource
    private StartRetestCheckCanRetestCommand startRetestCheckCanReTestCommand;
    @Resource
    private StartRetestCheckParamCommand startRetestCheckParamCommand;
    @Resource
    private StartRetestGetInfoCommand startRetestGetInfoCommand;
    @Resource
    private StartRetestSaveRetestMainCommand startRetestSaveRetestMainCommand;
    @Resource
    private StartRetestUpdateSampleStatusCommand startRetestUpdateSampleStatusCommand;
    @Resource
    private StartRetestSampleFlowCommand startRetestSampleFlowCommand;
    @Resource
    private StartRetestCheckSaveOriginalCommand startRetestCheckSaveOriginalCommand;
    @Resource
    private StartRetestUpdateCriticalStatusCommand startRetestUpdateCriticalStatusCommand;
    @Resource
    private StartRetestUpdateMissItemCommand startRetestUpdateMissItemCommand;
    @Resource
    private StartRetestSaveRetestItemCommand startRetestSaveRetestItemCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        //检验参数
        addCommand(startRetestCheckParamCommand);

        //获取上下文信息
        addCommand(startRetestGetInfoCommand);

        //检查样本是否可以复查
        addCommand(startRetestCheckCanReTestCommand);

        //保存复查记录表
        addCommand(startRetestSaveRetestMainCommand);

        // 保存要复查哪些记录
        addCommand(startRetestSaveRetestItemCommand);

        //缓存原始结果
        addCommand(startRetestCheckSaveOriginalCommand);

        //修改危急值列表的状态
        addCommand(startRetestUpdateCriticalStatusCommand);

        //修改样本状态以及样本报告项目状态
        addCommand(startRetestUpdateSampleStatusCommand);

        //设置缺项
        addCommand(startRetestUpdateMissItemCommand);

        //条码环节
        addCommand(startRetestSampleFlowCommand);

        addCommand(e -> PROCESSING_COMPLETE);
    }
}
