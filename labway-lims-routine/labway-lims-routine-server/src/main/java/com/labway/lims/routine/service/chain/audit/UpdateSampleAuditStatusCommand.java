package com.labway.lims.routine.service.chain.audit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleAuditMethodEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Component
@Slf4j
public class UpdateSampleAuditStatusCommand implements Command {

    @Resource
    private SampleService sampleService;
    @DubboReference
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditSampleContext context = AuditSampleContext.from(c);


        final SampleDto sample = context.getSample();
        final SampleAuditDto auditVo = context.getParam();
        final ApplySampleDto applySample = context.getApplySample();
        final SampleAuditDto param = context.getParam();
        final UserDto twoCheckUser = context.getTwoCheckUser();
        final LoginUserHandler.User user = LoginUserHandler.get();

        final SampleDto sampleDto = new SampleDto();
        sampleDto.setSampleId(sample.getSampleId());
        if (Objects.equals(SampleAuditStatusEnum.ONE_CHECK.name(), param.getAuditStatus())) {
            sampleDto.setOneCheckerId(user.getUserId());
            sampleDto.setOneCheckerName(user.getNickname());
            sampleDto.setOneCheckDate(new Date());
        } else if (Objects.equals(SampleAuditStatusEnum.TWO_CHECK.name(), param.getAuditStatus())) {
            sampleDto.setTwoCheckerId(Objects.nonNull(twoCheckUser) ? twoCheckUser.getUserId() : user.getUserId());
            sampleDto.setTwoCheckerName(Objects.nonNull(twoCheckUser) ? twoCheckUser.getNickname() : user.getNickname());
            sampleDto.setTwoCheckDate(new Date());
        }
		sampleDto.setAutoOneCheck(context.isAutoAudit() ? SampleAuditMethodEnum.AUTO.getCode() : SampleAuditMethodEnum.MANUAL.getCode());

        if (BooleanUtils.isNotTrue(sampleService.updateBySampleId(sampleDto))) {
            throw new IllegalStateException("审核失败,稍后重试");
        }

        //修改样本状态
        final ApplySampleDto dto = new ApplySampleDto();
        if (Objects.equals(auditVo.getAuditStatus(), SampleAuditStatusEnum.ONE_CHECK.name())) {
            //样本只有一审的时候就是已审
            dto.setStatus(SampleStatusEnum.ONE_AUDIT.getCode());
            // 一审的时候把测试者改成一审人
            dto.setTesterId(sampleDto.getOneCheckerId());
            dto.setTesterName(sampleDto.getOneCheckerName());
        } else if (Objects.equals(auditVo.getAuditStatus(), SampleAuditStatusEnum.TWO_CHECK.name())) {
            dto.setStatus(SampleStatusEnum.AUDIT.getCode());
        }

        dto.setApplySampleId(sample.getApplySampleId());
        if (Objects.equals(SampleStatusEnum.COUNTERTRIAL.getCode(), applySample.getColorMarking())) {
            dto.setColorMarking(SampleStatusEnum.RETRIAL.getCode());
        } else if (Objects.equals(SampleStatusEnum.RETRIAL.getCode(), applySample.getColorMarking())) {
            dto.setColorMarking(SampleStatusEnum.RETRIAL.getCode());
        } else {
            dto.setColorMarking(dto.getStatus());
        }
        dto.setReportNo(context.getReportNo());
        applySampleService.updateByApplySampleId(dto);

        return CONTINUE_PROCESSING;
    }
}
