package com.labway.lims.routine.vo;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/8 14:17
 */
@Getter
@Setter
public class HistoryResultsChartVo {

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目id
     */
    private Long reportItemId;


    private List<HistoryResultsChartVo.ResultVo> results;

    @Getter
    @Setter
    public static class ResultVo {

        /**
         * 日期
         */
        private String date;

        /**
         * 结果
         */
        private String result;

        /**
         * 1: 危机
         * 2: 异常
         * 0: 正常
         * @see ResultStatusEnum
         */
        private Integer status;

    }
}
