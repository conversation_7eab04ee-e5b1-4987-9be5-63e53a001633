package com.labway.lims.routine.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <pre>
 * QueryQcSampleResultVo
 * 查询质控结果
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/2 14:34
 */
@Getter
@Setter
public class QueryQcSampleResultVo implements Serializable {

    /**
     * 起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endDate;

    /**
     * 仪器id
     */
    private Long instrumentId;

    /**
     * 仪器报告项目code
     */
    private String reportItemCode;

    public void checkParam() {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            throw new IllegalArgumentException("时间不能为空");
        }
        if (Objects.isNull(instrumentId)) {
            throw new IllegalArgumentException("仪器不能为空");
        }
        if (StringUtils.isBlank(reportItemCode)) {
            throw new IllegalArgumentException("报告项目不能为空");
        }
    }
}
