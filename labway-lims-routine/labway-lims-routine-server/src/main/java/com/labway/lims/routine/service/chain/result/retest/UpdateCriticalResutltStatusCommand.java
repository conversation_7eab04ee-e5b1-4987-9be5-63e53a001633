package com.labway.lims.routine.service.chain.result.retest;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.SampleCriticalResultStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.service.chain.result.RecalculateRefResultCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2023/5/27 14:22
 */
@Slf4j
@Component
public class UpdateCriticalResutltStatusCommand implements Command {
    @Resource
    private SampleCriticalResultService sampleCriticalResultService;
    @Resource
    private RecalculateRefResultCommand recalculateRefResultCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final RetestResultContext context = RetestResultContext.from(c);
        final SampleDto sample = context.getSample();
        final ApplyDto apply = context.getApply();
        final InstrumentReportItemReferenceDto ref = context.getInstrumentReportItemReference();
        final List<SampleResultDto> sampleResults = context.getSampleResultsByReTest();

        // 是否完成复查
        if (!context.isComplete()) {
            return CONTINUE_PROCESSING;
        }


        if (recalculateRefResultCommand.isRecalculate(c)) {
            return CONTINUE_PROCESSING;
        }

        final List<SampleCriticalResultDto> sampleCriticalResults = sampleCriticalResultService.selectBySampleId(sample.getSampleId());

        final Map<String, SampleCriticalResultDto> reportCriticalResultMap = sampleCriticalResults.stream().collect(Collectors.toMap(SampleCriticalResultDto::getReportItemCode, Function.identity(), (a, b) -> a));


        for (SampleResultDto sampleResult : sampleResults) {

            // 复查结果是危急值  并且之前不存在危急值数据， 则新增
            if (Objects.equals(ResultStatusEnum.CRISIS.getCode(), sampleResult.getStatus())
                    && Objects.isNull(reportCriticalResultMap.get(sampleResult.getReportItemCode()))
            ) {

                final SampleCriticalResultDto dto = new SampleCriticalResultDto();
                dto.setSampleResultId(sampleResult.getSampleResultId());
                dto.setSampleResult(StringUtils.defaultString(sampleResult.getResult()));
                dto.setSampleId(sample.getSampleId());
                dto.setApplyId(sample.getApplyId());
                dto.setApplySampleId(sample.getApplySampleId());
                dto.setBarcode(StringUtils.defaultString(sample.getBarcode()));
                dto.setHspOrgId(String.valueOf(apply.getHspOrgId()));
                dto.setHspOrgName(StringUtils.defaultString(apply.getHspOrgName()));
                dto.setPatientAge(apply.getPatientAge());
                dto.setPatientSubageUnit(StringUtils.defaultString(apply.getPatientSubageUnit()));
                dto.setPatientSubage(apply.getPatientSubage());
                dto.setPatientName(StringUtils.defaultString(apply.getPatientName()));
                dto.setPatientSex(apply.getPatientSex());
                dto.setTestDate(sample.getTestDate());
                dto.setGroupId(sample.getGroupId());
                dto.setGroupName(StringUtils.defaultString(sample.getGroupName()));
                dto.setReportItemCode(StringUtils.defaultString(sampleResult.getReportItemCode()));
                dto.setReportItemName(StringUtils.defaultString(sampleResult.getReportItemName()));
                dto.setReportItemId(sampleResult.getReportItemId());
                if (Objects.nonNull(ref)) {
                    dto.setCriticalRange(ref.getFatalMin() + "<= 或 >=" + ref.getFatalMax());
                } else {
                    dto.setCriticalRange(StringUtils.EMPTY);
                }
                dto.setIsCritical(YesOrNoEnum.YES.getCode());
                dto.setHandleUserId(NumberUtils.LONG_ZERO);
                dto.setHandleUserName(StringUtils.EMPTY);
                dto.setHandleContent(StringUtils.EMPTY);
                dto.setHandleDate(DefaultDateEnum.DEFAULT_DATE.getDate());
                dto.setContactPhone(StringUtils.EMPTY);
                dto.setContactUserName(StringUtils.EMPTY);
                dto.setStatus(SampleCriticalResultStatusEnum.UNPROCESSED.getCode());
                dto.setTesterId(NumberUtils.LONG_ZERO);
                dto.setTesterName(StringUtils.EMPTY);
                dto.setOrgId(sample.getOrgId());
                dto.setOrgName(StringUtils.defaultString(sample.getOrgName()));

                sampleCriticalResultService.addSampleCriticalResult(dto);
            }
        }

        // 移除不是复查中的
        sampleCriticalResults.removeIf(e -> !Objects.equals(e.getStatus(), SampleCriticalResultStatusEnum.UNDER_REVIEW.getCode()));
        if (CollectionUtils.isEmpty(sampleCriticalResults)) {
            return CONTINUE_PROCESSING;
        }

        //如果完成复查那么修改这个样本的危机列表
        final SampleCriticalResultDto dto = new SampleCriticalResultDto();
        dto.setStatus(SampleCriticalResultStatusEnum.REVIEW.getCode());
        sampleCriticalResultService.updateByCriticalValueIds(dto, sampleCriticalResults.stream()
                .map(SampleCriticalResultDto::getCriticalValueId).collect(Collectors.toSet()));

        return CONTINUE_PROCESSING;
    }
}
