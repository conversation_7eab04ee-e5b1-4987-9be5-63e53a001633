package com.labway.lims.routine.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 样本结果复查主表
 *
 * <AUTHOR>
 * @since 2023/4/11 17:58
 */
@Getter
@Setter
@TableName("tb_sample_retest_main")
public class TbSampleRetestMain implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 复查结果ID
     */
    @TableId
    private Long sampleRetestMainId;
    /**
     * 申请单样本ID
     */
    private Long applySampleId;
    /**
     * 样本ID
     */
    private Long sampleId;
    /**
     * 复查状态 0 复查中 1已复查
     * @see
     */
    private Integer status;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 0:未删除，1：已删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人id
     */
    private Long creatorId;
    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 更新人id
     */
    private Long updaterId;
    /**
     * 更新人
     */
    private String updaterName;

}
