package com.labway.lims.routine.service.chain.audit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.enums.routine.SampleCriticalResultStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CheckSampleStatusCommand implements Command {

    @Resource
    private SampleService sampleService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private UserService userService;

    @Resource
    private SampleCriticalResultService sampleCriticalResultService;

    @Override
    public boolean execute(Context c) throws Exception {

        AuditSampleContext context = AuditSampleContext.from(c);

        final SampleAuditDto param = context.getParam();
        final LoginUserHandler.User user = LoginUserHandler.get();

        final SampleDto sample = sampleService.selectBySampleId(param.getSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("样本不存在");
        }
        final ApplySampleDto applySample = context.getApplySample();

        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (applySampleService.isDisabled(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", sample.getApplySampleId()));
        }

        if (applySampleService.isTerminate(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", sample.getApplySampleId()));
        }

        // 是否在复测中
        checkIsRetesting(applySample, sample);

        // 如果当前是一审，样本状态必须都是 未审
        if (Objects.equals(param.getAuditStatus(), SampleAuditStatusEnum.ONE_CHECK.name())) {
            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
                throw new IllegalStateException(String.format("条码号:%s , 不是未审核状态", applySample.getBarcode()));
            }
            // 一审 检查 是否存在 危急值的报告项目
            List<SampleCriticalResultDto> sampleCriticalResultDtos =
                sampleCriticalResultService.selectBySampleId(sample.getSampleId()).stream()
                    .filter(obj -> !Objects.equals(obj.getStatus(), SampleCriticalResultStatusEnum.PROCESSED.getCode()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(sampleCriticalResultDtos)) {
                throw new IllegalStateException("存在未处理完成的危急值项目，请处理完成再进行审核");
            }
        }

        // 如果当前是二审，样本状态必须都是 一审
        if (Objects.equals(param.getAuditStatus(), SampleAuditStatusEnum.TWO_CHECK.name())) {
            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) {
                throw new IllegalStateException(String.format("条码号:%s , 不是一审核状态", applySample.getBarcode()));
            }

            if (Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
                throw new IllegalStateException(String.format("条码号:%s , 已审核", applySample.getBarcode()));
            }

            // 【【常规检验】【微生物检验】【外送检验】1、可支持配置二审；2、二审时，选择二审人，输入密码进行审核】
            //  https://www.tapd.cn/tapd_fe/59091617/story/detail/1159091617001002010
            if (Objects.equals(sample.getOneCheckerId(), user.getUserId())) {
                // 进到这里肯定就一审了， 因为当前用户和一审人（数据库默认是 null 或者 0）

                // 获取输入账号和密码的二审人（ 一二审人一样的话必须要有密码）
                final UserDto twoCheckUser = userService.checkUser(param.getAuditName(), param.getAuditPwd());
                if (Objects.equals(sample.getOneCheckerId(), twoCheckUser.getUserId())) {
                    throw new IllegalStateException("一审人和二审人不能为同一用户");
                }
                context.setTwoCheckUser(twoCheckUser);
            }

            // 获取输入账号和密码的二审人（ 一二审人一样的话必须要有密码）
            final Long userId = Objects.nonNull(context.getTwoCheckUser()) ? context.getTwoCheckUser().getUserId() : user.getUserId();
            // 一审的时候会把检验者改为一审者，  但是更改样本备注， 样本类型， 样本性状等会修改检验者
            if (Objects.equals(context.getApplySample().getTesterId(), userId)) {
                throw new IllegalStateException("检验者和二审者不能为同一用户");
            }

        }

        return CONTINUE_PROCESSING;
    }

    /**
     * 当前样本是否存在
     */
    public void checkIsRetesting(ApplySampleDto applySample, SampleDto sample) {
        if (Objects.equals(applySample.getStatus(), SampleStatusEnum.RETEST.getCode())) {
            throw new IllegalStateException(String.format("样本 [%s] 结果正在复查，无法审核", sample.getSampleNo()));
        }
    }
}
