package com.labway.lims.routine.service.chain.audit;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.enums.routine.SampleCriticalResultStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import com.labway.lims.routine.api.service.SampleService;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class CheckSampleStatusCommand implements Command {

    @Resource
    private SampleService sampleService;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private SampleRetestMainService sampleRetestMainService;

    @Resource
    private SampleCriticalResultService sampleCriticalResultService;

    @Override
    public boolean execute(Context c) throws Exception {

        AuditSampleContext context = AuditSampleContext.from(c);

        final SampleAuditDto param = context.getParam();

        final SampleDto sample = sampleService.selectBySampleId(param.getSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("样本不存在");
        }
        final ApplySampleDto applySample = context.getApplySample();

        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (applySampleService.isDisabled(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", sample.getApplySampleId()));
        }

        if (applySampleService.isTerminate(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", sample.getApplySampleId()));
        }

        // 是否在复测中
        checkIsRetesting(applySample, sample);

        // 如果当前是一审，样本状态必须都是 未审
        if (Objects.equals(param.getAuditStatus(), SampleAuditStatusEnum.ONE_CHECK.name())) {
            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
                throw new IllegalStateException(String.format("条码号:%s , 不是未审核状态", applySample.getBarcode()));
            }
            // 一审 检查 是否存在 危急值的报告项目
            List<SampleCriticalResultDto> sampleCriticalResultDtos =
                sampleCriticalResultService.selectBySampleId(sample.getSampleId()).stream()
                    .filter(obj -> !Objects.equals(obj.getStatus(), SampleCriticalResultStatusEnum.PROCESSED.getCode()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(sampleCriticalResultDtos)) {
                throw new IllegalStateException("存在未处理完成的危急值项目，请处理完成再进行审核");
            }
        }

        // 如果当前是二审，样本状态必须都是 一审
        if (Objects.equals(param.getAuditStatus(), SampleAuditStatusEnum.TWO_CHECK.name())) {
            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) {
                throw new IllegalStateException(String.format("条码号:%s , 不是一审核状态", applySample.getBarcode()));
            }

            if (Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
                throw new IllegalStateException(String.format("条码号:%s , 已审核", applySample.getBarcode()));
            }

            if (Objects.equals(sample.getOneCheckerId(), LoginUserHandler.get().getUserId())) {
                throw new IllegalStateException("一审者和二审者不能为同一用户");
            }

            if (Objects.equals(context.getApplySample().getTesterId(), LoginUserHandler.get().getUserId())) {
                throw new IllegalStateException("检验者和二审者不能为同一用户");
            }

        }

        return CONTINUE_PROCESSING;
    }

    /**
     * 当前样本是否存在
     */
    public void checkIsRetesting(ApplySampleDto applySample, SampleDto sample) {
        if (Objects.equals(applySample.getStatus(), SampleStatusEnum.RETEST.getCode())) {
            throw new IllegalStateException(String.format("样本 [%s] 结果正在复查，无法审核", sample.getSampleNo()));
        }
    }
}
