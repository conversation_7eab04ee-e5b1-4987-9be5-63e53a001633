package com.labway.lims.routine.service.chain.auto.audit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.routine.controller.RoutineController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.impl.ChainBase;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 自动审核
 *
 * <AUTHOR> on 2025/7/29.
 */
@Slf4j
@Component
public class AutoAuditChain extends ChainBase implements InitializingBean {

	@Resource
	private AutoAuditLimitCommand autoAuditLimitCommand;

	@Resource
	private AutoAuditGetBaseInfoCommand autoAuditGetBaseInfoCommand;

	@Resource
	private AutoAuditBeforeCheckCommand autoAuditBeforeCheckCommand;

	@Resource
	private AutoAuditJVSExecCommand autoAuditJVSExecCommand;

	@Resource
	private AutoAuditSampleFlowCommand autoAuditSampleFlowCommand;

	@Resource
	private AutoAuditCommand autoAuditCommand;

	@Resource
	private ThreadPoolConfig threadPoolConfig;

	@Resource
	private RedisPrefix redisPrefix;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Override
	public void afterPropertiesSet() throws Exception {

		// 限流
		addCommand(autoAuditLimitCommand);

		// 获取基础信息
		addCommand(autoAuditGetBaseInfoCommand);

		// 审核前校验
		addCommand(autoAuditBeforeCheckCommand);

		// 调用规则引擎
		addCommand(autoAuditJVSExecCommand);

		// 审核
		addCommand(autoAuditCommand);

		// 记录审核环节
		addCommand(autoAuditSampleFlowCommand);

		addCommand(context -> PROCESSING_COMPLETE);
	}

	/**
	 * 自动审核责任链需要忽略错误信息，不能影响正常的结果保存，单独使用一个线程
	 *
	 * @param c The {@link Context} to be processed by this
	 */
	@Override
	public boolean execute(Context c) throws Exception {

		LoginUserHandler.User user = LoginUserHandler.get();
		String key = String.format(redisPrefix.getBasePrefix() + RoutineController.GROUP_AUTO_AUDIT, user.getOrgId(), user.getGroupId());

		if (BooleanUtils.toBoolean(stringRedisTemplate.opsForValue().get(key))) {
			threadPoolConfig.getPool().execute(() -> {
				try {
					super.execute(c);
				} catch (Exception e) {
					log.error("自动审核执行失败 提示信息 ", e);
				}
			});

		}

		return PROCESSING_COMPLETE;
	}
}
