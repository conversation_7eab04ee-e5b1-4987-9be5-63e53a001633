package com.labway.lims.routine.service.chain.audit;

import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.config.SampleResultRuleVerifyConfig;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationException;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParseException;
import org.springframework.expression.spel.SpelNode;
import org.springframework.expression.spel.ast.VariableReference;
import org.springframework.expression.spel.standard.SpelExpression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <pre>
 * ResultRuleVerifyTipsCommand
 * 结果规则校验提醒
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/2/10 10:23
 */
@Slf4j
@Component
public class ResultRuleVerifyTipsCommand implements Command {

    private final ExpressionParser expressionParser = new SpelExpressionParser();

    @Autowired
    private SampleResultRuleVerifyConfig sampleResultRuleVerifyConfig;

    @Override
    public boolean execute(Context c) throws Exception {
        final AuditSampleContext context = AuditSampleContext.from(c);

        final List<SampleResultRuleVerifyConfig.Rule> verifyRules = sampleResultRuleVerifyConfig.getVerifyRules();
        final List<SampleResultDto> results = context.getSampleReportItemResults();
        final StandardEvaluationContext evaluationContext;

        try {
            // 删掉不符合规则的 校验规则
            verifyRules.removeIf(e -> StringUtils.isBlank(e.getFormula()) || !e.getFormula().contains(StringPool.POUND));

            if (CollectionUtils.isEmpty(results) || CollectionUtils.isEmpty(verifyRules)) {
                return CONTINUE_PROCESSING;
            }

            // spEL执行上下文
            evaluationContext = new StandardEvaluationContext();
            // 这里会根据key值进行对value的覆盖， 例如 HashMap.put 方法的实现
            for (SampleResultDto e : results) {
                if (StringUtils.isBlank(e.getReportItemCode())) {
                    continue;
                }
                if (!NumberUtils.isParsable(e.getResult())) {
                    continue;
                }
                evaluationContext.setVariable("_" + e.getReportItemCode(), Double.parseDouble(e.getResult()));
            }

            // 当前样本包含的报告项目
            final Set<String> containReportItemCodes = results.stream().map(SampleResultDto::getReportItemCode).collect(Collectors.toSet());

            final List<SampleResultRuleVerifyConfig.Rule> validRules = verifyRules.stream().filter(rule -> {
                String formula = rule.getFormula();
                for (String code : containReportItemCodes) {
                    formula = formula.replace((StringPool.POUND + code), StringPool.EMPTY);
                }

                // 如果不包含 # 说明该项目的的报告项目可以满足计算公式
                return !formula.contains(StringPool.POUND);
            }).collect(Collectors.toList());

            outer:
            for (SampleResultRuleVerifyConfig.Rule verifyRule : validRules) {
                final SpelExpression expression;
                try {
                    expression = (SpelExpression) expressionParser
                            .parseExpression(StringUtils.replace(verifyRule.getFormula(), "#", "#_"));
                } catch (IllegalStateException e) {
                    log.error("计算公式含非法符号，请检查中英文括号、空格等 公式 [{}] ", verifyRule.getFormula(), e);
                    continue;
                } catch (ParseException e) {
                    log.error("无法解析计算公式，请检查中英文括号、空格等 公式 [{}] ", verifyRule.getFormula(), e);
                    continue;
                }

                final Set<String> refs = getAllVariableReference(expression).stream()
                        .map(VariableReference::toStringAST).collect(Collectors.toSet());

                for (String ref : refs) {
                    if (Objects.isNull(evaluationContext.lookupVariable(StringUtils.removeStart(ref, "#")))) {
                        log.warn("公式 [{}] 依赖的报告项目 [{}] 没有结果，跳过结果计算", verifyRule.getFormula(), ref);
                        continue outer;
                    }
                }

                final Boolean matched;
                try {
                    matched = expression.getValue(evaluationContext, Boolean.class);
                } catch (EvaluationException e) {
                    log.error("样本结果规则验证异常 公式 [{}] ", verifyRule.getFormula(), e);
                    continue;
                }

                if (BooleanUtils.isTrue(matched)) {
                    throw new SampleResultRuleVerifyException(verifyRule.getAuditTips());
                }
            }
        } catch (SampleResultRuleVerifyException e) {
            throw e;
        } catch (Exception e) {
            log.error("样本结果规则解析异常 ", e);
        }

        return CONTINUE_PROCESSING;
    }

    /**
     * 获取到所有引用的变量
     */
    public List<VariableReference> getAllVariableReference(SpelExpression expression) {
        final LinkedList<VariableReference> list = new LinkedList<>();
        doGetAllVariableReference(expression.getAST(), list);
        return list;
    }

    private void doGetAllVariableReference(SpelNode node, List<VariableReference> list) {

        if (node instanceof VariableReference) {
            list.add((VariableReference) node);
        }

        for (int i = 0; i < node.getChildCount(); i++) {
            final SpelNode child = node.getChild(i);
            doGetAllVariableReference(child, list);
        }
    }

    public static class SampleResultRuleVerifyException extends Exception {
        public SampleResultRuleVerifyException(String message) {
            super(message);
        }
    }
}
