package com.labway.lims.routine.service.chain.pick.two;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.base.api.dto.InstrumentReportItemCommonPhraseDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemCommonPhraseService;
import com.labway.lims.routine.api.dto.SaveResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 常用短语
 */
@Slf4j
@Component
class TwoPickCommonPhraseCommand implements Filter, Command {
    @DubboReference
    private InstrumentReportItemCommonPhraseService instrumentReportItemCommonPhraseService;
    @Resource
    private SampleResultService sampleResultService;

    @Override
    public boolean postprocess(Context c, Exception exception) {

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);
        final List<InstrumentReportItemDto> instrumentReportItems = context.getInstrumentReportItems();
        final List<ReportItemDto> reportItems = context.getReportItems();

        final List<Long> ids = instrumentReportItems.stream()
                .filter(e -> reportItems.stream().anyMatch(k -> Objects.equals(e.getReportItemCode(), k.getReportItemCode())))
                .map(InstrumentReportItemDto::getInstrumentReportItemId).collect(Collectors.toList());

        // 获取到报告项目的常用短语
        final List<InstrumentReportItemCommonPhraseDto> instrumentReportItemCommonPhrases = new ArrayList<>(instrumentReportItemCommonPhraseService
                .selectByInstrumentReportItemIds(ids));

        // 删除不是默认的
        instrumentReportItemCommonPhrases.removeIf(e -> !Objects.equals(e.getIsDefault(), YesOrNoEnum.YES.getCode()));

        if (CollectionUtils.isEmpty(instrumentReportItemCommonPhrases)) {
            return CONTINUE_PROCESSING;
        }

        final List<SaveResultDto> results = context.getReportItems().stream().map(e -> {

            // 如果已经有了原始结果，那么这里不处理默认值
            if (context.getOriginalResults().containsKey(e.getReportItemCode())) {
                return null;
            }

            InstrumentReportItemCommonPhraseDto instrumentReportItemCommonPhrase = instrumentReportItemCommonPhrases.stream()
                    .filter(k -> Objects.equals(e.getReportItemCode(), k.getReportItemCode()))
                    .findFirst().orElse(null);
            if (Objects.isNull(instrumentReportItemCommonPhrase)) {
                return null;
            }

            // 如果这个常用短语不是当前仪器的，那么再去找找有没有当前仪器下的
            if (!Objects.equals(instrumentReportItemCommonPhrase.getInstrumentId(), context.getInstrument().getInstrumentId())) {
                final InstrumentReportItemCommonPhraseDto p = instrumentReportItemCommonPhrases.stream()
                        .filter(k -> Objects.equals(context.getInstrument().getInstrumentId(), k.getInstrumentId()))
                        .filter(k -> Objects.equals(e.getReportItemCode(), k.getReportItemCode()))
                        .findFirst().orElse(null);
                if (Objects.nonNull(p)) {
                    instrumentReportItemCommonPhrase = p;
                }
            }

            final SaveResultDto sr = new SaveResultDto();
            sr.setSampleId(context.getApplySampleId());
            sr.setApplySampleId(context.getApplySampleId());
            sr.setApplyId(context.getApply().getApplyId());
            sr.setTestItemId(e.getTestItemId());
            sr.setReportItemId(e.getReportItemId());
            sr.setReportItemCode(e.getReportItemCode());
            sr.setResult(StringUtils.defaultString(instrumentReportItemCommonPhrase.getContent()));
            sr.setInstrumentId(context.getInstrument().getInstrumentId());
            sr.setInstrumentCode(context.getInstrument().getInstrumentCode());
            sr.setInstrumentName(context.getInstrument().getInstrumentName());
            sr.setDate(new Date());

            return sr;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        sampleResultService.saveResults(results, SaveResultSourceEnum.DEFAULT_VALUE);

        return CONTINUE_PROCESSING;
    }
}
