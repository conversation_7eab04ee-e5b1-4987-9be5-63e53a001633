package com.labway.lims.routine.service.chain.result;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.routine.api.dto.ResultListDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SaveResultInfoDto;
import com.labway.lims.routine.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/3/30 13:38
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class SaveResultContext extends StopWatchContext {

    /**
     * 申请单
     */
    static final String APPLY = "APPLY_" + IdUtil.objectId();

    /**
     * 仪器
     */
    static final String INSTRUMENT = "INSTRUMENT_" + IdUtil.objectId();

    /**
     * 样本
     */
    static final String SAMPLE = "SAMPLE_" + IdUtil.objectId();

    /**
     * 申请单样本
     */
    static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    /**
     * 样本检验项目
     */
    static final String SAMPLE_TEST_ITEMS = "SAMPLE_TEST_ITEMS_" + IdUtil.objectId();

    /**
     * 结果报告项目
     */
    static final String INSTRUMENT_REPORT_ITEM = "INSTRUMENT_REPORT_ITEM_" + IdUtil.objectId();

    /**
     * 报告项目
     */
    static final String REPORT_ITEM = "REPORT_ITEM_" + IdUtil.objectId();

    /**
     * 仪器报告项目
     */
    static final String INSTRUMENT_REPORT_ITEMS = "INSTRUMENT_REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 当前样本下的报告项目
     */
    static final String SAMPLE_REPORT_ITEMS = "SAMPLE_REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 当前结果报告项目
     */
    static final String SAMPLE_REPORT_ITEM = "SAMPLE_REPORT_ITEM_" + IdUtil.objectId();

    /**
     * 仪器参考值
     */
    public static final String INSTRUMENT_REPORT_REFERENCE = "INSTRUMENT_REPORT_REFERENCE_" + IdUtil.objectId();

    /**
     * 结果转换
     */
    static final String RESULT_EXCHANGE = "RESULT_EXCHANGE_" + IdUtil.objectId();

    /**
     * 结果提示
     */
    public static final String RESULT_JUDGE = "RESULT_JUDGE_" + IdUtil.objectId();

    /**
     * 结果是否异常
     */
    public static final String RESULT_IS_EXCEPTION = "RESULT_IS_EXCEPTION_" + IdUtil.objectId();

    /**
     * 是否是复查
     */
    public static final String IS_RETESTING = "IS_RETEST_" + IdUtil.objectId();

    /**
     * 结果是否危机
     */
    public static final String RESULT_IS_CRITICAL = "RESULT_IS_CRITICAL_" + IdUtil.objectId();

    /**
     * 修改前结果
     */
    static final String BEFORE_RESULT = "BEFORE_RESULT_" + IdUtil.objectId();

    /**
     * 备注扩展json
     */
    static final String EXTRA_INFO = "EXTRA_INFO" + IdUtil.objectId();

    /**
     * 当前结果
     */
    static final String CONTEXT_RESULTS = "CONTEXT_RESULTS_" + IdUtil.objectId();

    /**
     * sample_result
     */
    static final String SAMPLE_RESULT = "SAMPLE_RESULT_" + IdUtil.objectId();

    public static final String RESULT_PROVIDER = "RESULT_PROVIDER" + IdUtil.objectId();

    public static final String CHAIN_PROVIDER = "CHAIN_PROVIDER" + IdUtil.objectId();

    static final String RESULT_ALL = "RESULT_ALL" + IdUtil.objectId();

    /**
     * 兄弟结果，也就是涉及到计算公式的结果。例如：A+B=C，当A修改的时候，那么C的结果就会放到这里面
     */
    static final String BROTHERS_RESULT = "BROTHERS_RESULT_" + IdUtil.objectId();

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    private String reportItemCode;

    // ------------质控

    /**
     * sampleId
     */
    private Long sampleId;

    /**
     * 是否是质控
     */
    private Boolean isQc;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 仪器
     */
    private Long instrumentId;

    // ------------

    /**
     * result
     */
    private String result;

    /**
     * instrument_result
     */
    private String instrumentResult;

    /**
     * 结果保存来源
     */
    private SaveResultSourceEnum source;

    /**
     * 检查时间
     */
    private Date testDate;

    /**
     * 是否强制更新
     */
    private boolean applySampleUpdate;

    /**
     * 多出来的数据， json
     */
    private String extraInfo;

    /**
     * 是否手工录入结果 0否1是
     */
    private Integer isHandeResult;


    @Override
    protected String getWatchName() {
        return "结果保存";
    }

    public SaveResultContext() {
        put(CONTEXT_RESULTS, new LinkedHashMap<>(0));
    }

    public static SaveResultContext from(Context context) {
        return (SaveResultContext) context;
    }

    public ApplyDto getApply() {
        return (ApplyDto) get(APPLY);
    }

    public SampleDto getSample() {
        return (SampleDto) get(SAMPLE);
    }

    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }


    public List<ApplySampleItemDto> getSampleTestItems() {
        return (List<ApplySampleItemDto>) get(SAMPLE_TEST_ITEMS);
    }

    public SampleResultDto getSampleResult() {
        return (SampleResultDto) get(SAMPLE_RESULT);
    }

    public InstrumentReportItemDto getInstrumentReportItem() {
        return (InstrumentReportItemDto) get(INSTRUMENT_REPORT_ITEM);
    }

    public ReportItemDto getReportItem() {
        return (ReportItemDto) get(REPORT_ITEM);
    }


    public List<InstrumentReportItemDto> getInstrumentReportItems() {
        return (List<InstrumentReportItemDto>) get(INSTRUMENT_REPORT_ITEMS);
    }


    public List<SampleReportItemDto> getSampleReportItems() {
        return (List<SampleReportItemDto>) get(SAMPLE_REPORT_ITEMS);
    }

    public SampleReportItemDto getSampleReportItem() {
        return (SampleReportItemDto) get(SAMPLE_REPORT_ITEM);
    }

    @Nullable
    public InstrumentReportItemReferenceDto getInstrumentReportItemReference() {
        return (InstrumentReportItemReferenceDto) get(INSTRUMENT_REPORT_REFERENCE);
    }

    public List<InstrumentReportItemResultExchangeDto> getResultExchange() {
        return (List<InstrumentReportItemResultExchangeDto>) get(RESULT_EXCHANGE);
    }

    /**
     * 可能会为空
     */
    @Nullable
    public InstrumentDto getInstrument() {
        return (InstrumentDto) get(INSTRUMENT);
    }

    @Nullable
    public String getResultJudge() {
        return (String) get(RESULT_JUDGE);
    }

    public boolean isException() {
        return Objects.nonNull(get(RESULT_IS_EXCEPTION)) && (boolean) get(RESULT_IS_EXCEPTION);
    }

    public boolean isCritical() {
        return Objects.nonNull(get(RESULT_IS_CRITICAL)) && (boolean) get(RESULT_IS_CRITICAL);
    }

    /**
     * 获取之前的结果
     *
     * @return null 则没有
     */
    @Nullable
    public String getBeforeResult() {
        final Object value = get(BEFORE_RESULT);
        if (Objects.isNull(value)) {
            return null;
        }
        return String.valueOf(value);
    }

    public boolean isRetesting() {
        return containsKey(IS_RETESTING) && (boolean) get(IS_RETESTING);
    }

    @Nullable
    public List<ResultListDto> getResultAll() {
        return (List<ResultListDto>) get(RESULT_ALL);
    }

    /**
     * 获取兄弟结果
     *
     * @deprecated 尚未启用
     */
    @Deprecated
    public List<SaveResultInfoDto> getBrothersResult() {
        if (!containsKey(BROTHERS_RESULT)) {
            put(BROTHERS_RESULT, new ArrayList<>());
        }
        return (List<SaveResultInfoDto>) get(BROTHERS_RESULT);
    }

    /**
     * 当前上下文涉及到的报告项目结果，也就是说在整个流程中，如果整个报告项目涉及到新增、修改结果 那么都会在这里
     *
     * @return {sampleId:结果}
     */

    public Map<String, String> getContextResults() {
        return (Map<String, String>) get(CONTEXT_RESULTS);
    }

}
