package com.labway.lims.routine.service.chain.retest.cancel;

import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.service.chain.retest.StartRetestUpdateMissItemCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 删除缺项
 */
@Slf4j
@Component
class CancelRetestMissItemCommand implements Command {
    @Resource
    private SampleReportItemService sampleReportItemService;
    @Resource
    private StartRetestUpdateMissItemCommand startRetestUpdateMissItemCommand;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context c) throws Exception {
        final CancelRetestContext context = CancelRetestContext.from(c);


        // 如果是取消整个样本，那么删除这个redis key
        if (StringUtils.isBlank(context.getReportItemCode())) {
            stringRedisTemplate.delete(startRetestUpdateMissItemCommand.getMissItemKey(context.getSampleId()));
        } else {

            final SampleReportItemDto sampleReportItem = context.getSampleReportItems().stream()
                    .filter(e -> Objects.equals(e.getReportItemCode(), context.getReportItemCode()))
                    .findFirst().orElse(null);
            if (Objects.isNull(sampleReportItem)) {
                return CONTINUE_PROCESSING;
            }

            // 复查缺项删除
            stringRedisTemplate.opsForHash().delete(startRetestUpdateMissItemCommand.getMissItemKey(context.getSampleId()),
                    sampleReportItem.getReportItemCode());

        }


        return CONTINUE_PROCESSING;
    }
}
