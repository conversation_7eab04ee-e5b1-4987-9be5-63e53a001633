package com.labway.lims.routine.service.chain.result;

import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 删除之前的结果
 *
 * <AUTHOR>
 * @since 2023/3/30 16:21
 */
@Slf4j
@Component
public class RemoveBeforeResultCommand implements Command {

    @Resource
    private SampleResultService sampleResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        // 先把之前的结果保存下来
        final SampleDto sample = context.getSample();

        final SampleResultDto sampleResult = sampleResultService
                .selectBySampleIdAndReportItemCode(sample.getSampleId(), context.getReportItemCode());

        if (Objects.isNull(sampleResult)) {
            return CONTINUE_PROCESSING;
        }

        context.put(SaveResultContext.BEFORE_RESULT, sampleResult.getResult());

        context.put(SaveResultContext.EXTRA_INFO, sampleResult.getExtraInfo());

        // 删除之前的结果 mysql
        sampleResultService.deleteBySampleResultId(sampleResult.getSampleResultId(),sample.getSampleId());

        return CONTINUE_PROCESSING;
    }
}
