package com.labway.lims.routine.config;

import com.labway.lims.routine.api.dto.CombinedBillDTO;
import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.naming.ConfigurationException;
import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 合并项目配置
 */
@Component
@ConfigurationProperties(prefix = "combined-bill")
@Data
public class CombinedBillConfig implements InitializingBean {


    /**
     * 配置文件里的项目信息
     */
    private List<CombinedBillDTO> report;

    /**
     * 根据id分组
     */
    private Map<String, List<CombinedBillDTO>> reportGroupMap;

    /**
     * 根据reportCode分组
     */
    private Map<String, CombinedBillDTO> reportCodeMap;

    /**
     * 报告项目codes
     */
    private Set<String> reportCodes;

    /**
     * 项目提示
     */
    private List<ReportItemTips> reportItemTips;

    /**
     * 忽略并单提示时间
     */
    private Integer ignoreTipSecond = 60;

    @Override
    public void afterPropertiesSet() throws Exception {

        reportCodes = new HashSet<>(report.size());

        for (CombinedBillDTO combinedBillDTO : report) {
            if(!reportCodes.add(combinedBillDTO.getReportCode())){
                throw new ConfigurationException("reportCode 在配置 {combined-bill.report} 中重复");
            }
        }
        reportGroupMap = report.stream().collect(Collectors.groupingBy(CombinedBillDTO::getReportGroupName));
        reportCodeMap = report.stream().collect(Collectors.toMap(CombinedBillDTO::getReportCode, Function.identity()));
    }

    /**
     * 项目提示
     */
    @Data
    public static class ReportItemTips implements Serializable {
        /**
         * 项目分组名
         */
        private String reportGroupName;
        /**
         * 标题
         */
        private String title;
        /**
         * 项目提示
         */
        private String tips;
    }


}
