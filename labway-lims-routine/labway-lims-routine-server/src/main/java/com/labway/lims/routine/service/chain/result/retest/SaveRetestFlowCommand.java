package com.labway.lims.routine.service.chain.result.retest;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Getter
@Setter
@Component
public class SaveRetestFlowCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @Resource
    private SampleRetestMainService sampleRetestMainService;
    @Resource
    private SampleRetestItemService sampleRetestItemService;
    @Resource
    private SampleResultService sampleResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        final RetestResultContext context = RetestResultContext.from(c);

        if (BooleanUtils.isNotTrue(context.isComplete())) {
            return CONTINUE_PROCESSING;
        }

        //获取本轮的复查项目
        final List<SampleRetestItemDto> retestItems = context.getSampleRetestItems();

        // 完成复查过的报告项目
        final StringBuilder sb = new StringBuilder();

        for (SampleRetestItemDto ir : retestItems) {
            sb.append(String.format("报告项目 [%s] 复查结果 [%s] \n", ir.getReportItemName(), ir.getResult()));
        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        final SampleFlowDto s = new SampleFlowDto();
        s.setSampleFlowId(snowflakeService.genId());
        s.setApplyId(context.getSample().getApplyId());
        s.setApplySampleId(context.getSample().getApplySampleId());
        s.setBarcode(context.getSample().getBarcode());
        s.setOperateCode(BarcodeFlowEnum.COMPLETE_RETEST_RESULT.name());
        s.setOperateName(BarcodeFlowEnum.COMPLETE_RETEST_RESULT.getDesc());
        s.setOperatorId(LoginUserHandler.get().getUserId());
        s.setOperator(LoginUserHandler.get().getNickname());
        s.setContent(sb.toString());
        s.setCreateDate(new Date());
        s.setCreatorId(user.getUserId());
        s.setCreatorName(user.getNickname());
        s.setUpdaterName(user.getNickname());
        s.setUpdaterId(user.getUserId());
        s.setUpdateDate(new Date());
        s.setOrgId(user.getOrgId());
        s.setIsDelete(YesOrNoEnum.NO.getCode());
        s.setOrgName(user.getOrgName());
        sampleFlowService.addSampleFlow(s);

        return CONTINUE_PROCESSING;
    }
}
