package com.labway.lims.routine.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.base.ItemTypeEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/4/6 10:50
 */
@Getter
@Setter
public class ResultRecordComparesVo {

    /**
     * 样本ID
     */
    private Long sampleId;
    /**
     * 类型
     * 
     * @see ItemTypeEnum
     */
    private String itemType;
    /**
     * 日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date dateStart;

    /**
     * 日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date dateEnd;
}
