package com.labway.lims.routine.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.ReadBackStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 危机值处理 参数
 * 
 * <AUTHOR>
 * @since 2023/4/11 14:34
 */
@Getter
@Setter
public class SampleCriticalResultHandleRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 危急值ID
     */
    private Long criticalValueId;
    /**
     * 是否危急值:0否,1是
     * @see  YesOrNoEnum
     */
    private Integer isCritical;
    /**
     * 处理内容
     */
    private String handleContent;
    /**
     * 联系人名称
     */
    private String contactUserName;
    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 是否回读
     * @see ReadBackStatusEnum
     */
    private Integer isReadBack;

    /**
     * 备注
     */
    private String remark;

}
