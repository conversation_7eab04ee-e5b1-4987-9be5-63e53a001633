package com.labway.lims.routine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.model.TbSampleCriticalResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 危机值 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbSampleCriticalResultMapper extends BaseMapper<TbSampleCriticalResult> {

    int updateByCriticalValueIds(@Param("criticalResult") SampleCriticalResultDto criticalResult,
                                @Param("criticalValueIds") Collection<Long> criticalValueIds);
}
