package com.labway.lims.routine.service.chain.retest;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.StartReTestDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/26 17:33
 */
@Slf4j
@Component
public class StartRetestUpdateSampleStatusCommand implements Filter, Command {
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private SampleReportItemService sampleReportItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final StartRetestContext context = StartRetestContext.from(c);
        final StartReTestDto reTestDto = context.getRetestInfo();
        final ApplySampleDto applySample = context.getApplySample();
        final List<SampleReportItemDto> retestSampleReportItems = context.getRetestSampleReportItems();

        final ApplySampleDto dto = new ApplySampleDto();
        dto.setApplySampleId(applySample.getApplySampleId());
        dto.setStatus(SampleStatusEnum.RETEST.getCode());
        //修改样本未正在复查状态
        applySampleService.updateByApplySampleId(dto);

        //修改样本报告项目复查状态
        final Collection<Long> sampleReportItemIds = retestSampleReportItems.stream()
                .map(SampleReportItemDto::getSampleReportItemId).collect(Collectors.toSet());
        final SampleReportItemDto itemDto = new SampleReportItemDto();
        itemDto.setIsRetest(RetestStatusEnum.RETESTING.getCode());
        itemDto.setSampleId(reTestDto.getSampleId());
        sampleReportItemService.updateBySampleReportItemIds(itemDto, sampleReportItemIds);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
