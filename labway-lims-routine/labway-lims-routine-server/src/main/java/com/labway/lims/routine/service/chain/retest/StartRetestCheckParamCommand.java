package com.labway.lims.routine.service.chain.retest;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/5/26 16:48
 */
@Slf4j
@Component
public class StartRetestCheckParamCommand implements Filter, Command {
    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context context) throws Exception {
        return CONTINUE_PROCESSING;
    }
}
