package com.labway.lims.routine.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.routine.ReadBackStatusEnum;
import com.labway.lims.api.enums.routine.SampleCriticalResultStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 样本危机值
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Setter
@Getter
@TableName("tb_sample_critical_result")
public class TbSampleCriticalResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 危急值ID
     */
    @TableId
    private Long criticalValueId;
    /**
     * 样本结果ID
     */
    private Long sampleResultId;
    /**
     * 样本ID
     */
    private Long sampleId;
    /**
     * 样本ID
     */
    private Long applySampleId;
    /**
     * 申请单id
     */
    private Long applyId;
    /**
     * 条码
     */
    private String barcode;
    /**
     * 送检机构ID
     */
    private String hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;
    /**
     * 患者年龄
     */
    private Integer patientAge;
    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;
    /**
     * 检验时间
     */
    private Date testDate;
    /**
     * 专业组ID
     */
    private Long groupId;
    /**
     * 专业组名称
     */
    private String groupName;
    /**
     * 报告项目ID
     */
    private Long reportItemId;
    /**
     * 报告项目code
     */
    private String reportItemCode;
    /**
     * 报告项目名称
     */
    private String reportItemName;
    /**
     * 样本结果
     */
    private String sampleResult;
    /**
     * 危急值范围
     */
    private String criticalRange;
    /**
     * 是否危急值
     * @see  YesOrNoEnum
     */
    private Integer isCritical;
    /**
     * 处理人ID
     */
    private Long handleUserId;
    /**
     * 处理人姓名
     */
    private String handleUserName;
    /**
     * 处理内容
     */
    private String handleContent;
    /**
     * 处理时间
     */
    private Date handleDate;
    /**
     * 联系人名称
     */
    private String contactUserName;
    /**
     * 联系人电话
     */
    private String contactPhone;
    /**
     * 状态:0未处理，1复查中，2已处理
     * 
     * @see SampleCriticalResultStatusEnum
     */
    private Integer status;
    /**
     * 1:已删除 0：未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 检验者ID
     */
    private Long testerId;
    /**
     * 检验者姓名
     */
    private String testerName;
    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新者
     */
    private Long updaterId;
    /**
     * 更新人
     */
    private String updaterName;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 是否回读
     * @see ReadBackStatusEnum
     */
    private Integer isReadBack;

    /**
     * 回读用户
     */
    private String readBackUser;

    /**
     * 回读时间
     */
    private Date readBackTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 超时设置(单位分钟)
     */
    private Integer timeOutConfig;

    /**
     * 是否超时
     * @see YesOrNoEnum
     */
    private Integer isTimeOut;
}
