package com.labway.lims.routine.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.core.enums.ItemTypeEnum;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.enums.routine.TestResultTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.dto.ApplyUpdateBeforeCheckTipDto;
import com.labway.lims.apply.api.dto.ApplyUpdateBeforeCheckTipItemDto;
import com.labway.lims.apply.api.dto.AutoExtractArchiveSampleDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.api.service.RackArchiveService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.service.FinanceSampleLockService;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.routine.api.dto.QuerySampleDto;
import com.labway.lims.routine.api.dto.RoutineSampleTwoUnPickInfoDto;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleCancelAuditDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.routine.mapper.TbSampleMapper;
import com.labway.lims.routine.model.TbSample;
import com.labway.lims.routine.service.chain.audit.AuditSampleChain;
import com.labway.lims.routine.service.chain.audit.AuditSampleContext;
import com.labway.lims.routine.service.chain.audit.BuildReportCommand;
import com.labway.lims.routine.service.chain.pick.two.TwoPickChain;
import com.labway.lims.routine.service.chain.pick.two.TwoPickContext;
import com.labway.lims.routine.service.chain.result.InstrumentReportReferenceCommand;
import com.labway.lims.routine.service.chain.result.NumberResultCommand;
import com.labway.lims.routine.service.chain.result.UpdateMissItemCommand;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@DubboService(interfaceClass = SampleService.class)
public class SampleServiceImpl implements SampleService {
    @Resource
    private TbSampleMapper sampleMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private AuditSampleChain auditSampleChain;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleReportService sampleReportService;
    @Resource
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @Resource
    private TbSampleMapper tbSampleMapper;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @Resource
    private SampleResultService sampleResultService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private UpdateMissItemCommand updateMissItemCommand;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @Resource
    private SampleService sampleService;
    @Resource
    private TwoPickChain twoPickChain;
    @DubboReference
    private FinanceSampleLockService financeSampleLockService;
    @Resource
    private BuildReportCommand buildReportCommand;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private EnvDetector envDetector;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;
    @Resource
    private NumberResultCommand numberResultCommand;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;

    @DubboReference
    private RackLogicSpaceService rackLogicSpaceService;

    @DubboReference
    private RackArchiveService rackArchiveService;

    @Nonnull
    @Override
    public List<SampleDto> selectByTestDate(QuerySampleDto dto) {
        final LambdaQueryWrapper<TbSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSample::getOrgId, LoginUserHandler.get().getOrgId())
                .eq(Objects.nonNull(dto.getGroupId()), TbSample::getGroupId, dto.getGroupId())
                .eq(Objects.nonNull(dto.getHspOrgId()), TbSample::getHspOrgId, dto.getHspOrgId())
                .eq(Objects.nonNull(dto.getInstrumentGroupId()), TbSample::getInstrumentGroupId, dto.getInstrumentGroupId())
                .eq(Objects.nonNull(dto.getInstrumentId()), TbSample::getInstrumentId, dto.getInstrumentId())
                .eq(StringUtils.isNotBlank(dto.getSampleNo()), TbSample::getSampleNo, dto.getSampleNo())
                .ge(TbSample::getTestDate, dto.getTestDateStart()).le(TbSample::getTestDate, dto.getTestDateEnd())
                .orderByAsc(TbSample::getSampleId);
        // 仅显示检验样本
        if (Objects.equals(YesOrNoEnum.YES.getCode(), dto.getIsTestSample())) {
            wrapper.ne(TbSample::getApplyId, NumberUtils.LONG_ZERO)
                    .ne(TbSample::getApplySampleId, NumberUtils.LONG_ZERO);
        }
        return sampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public List<SampleDto> selectSamples(QuerySampleDto dto) {
        dto.setOrgId(LoginUserHandler.get().getOrgId());
        final String sampleStatus = dto.getSampleStatus();
        // 查询未审,状态不是未审或者复查的过滤
        if (Objects.equals(sampleStatus, SampleStatusEnum.NOT_AUDIT.name())) {
            dto.setSampleStatusList(Set.of(SampleStatusEnum.NOT_AUDIT.getCode(), SampleStatusEnum.RETEST.getCode()));
        }
        // 查询一审，不等于一审的过滤
        if (Objects.equals(sampleStatus, SampleStatusEnum.ONE_AUDIT.name())) {
            dto.setSampleStatusList(Set.of(SampleStatusEnum.ONE_AUDIT.getCode()));
        }
        // 查询已审，不等于已审的过滤
        if (Objects.equals(sampleStatus, SampleStatusEnum.AUDIT.name())) {
            dto.setSampleStatusList(Set.of(SampleStatusEnum.AUDIT.getCode()));
        }

        return sampleMapper.selectSamples(dto).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<SampleDto> selectAllByTestDate(QuerySampleDto dto) {
        return sampleMapper.selectAllByTestDate(dto).stream().map(this::convert).collect(Collectors.toList());
    }


    @Nonnull
    @Override
    public List<SampleDto> selectByGroupId(long groupId) {
        return sampleMapper.selectList(new LambdaQueryWrapper<TbSample>().eq(TbSample::getGroupId, groupId)
                        .eq(TbSample::getOrgId, LoginUserHandler.get().getOrgId())).stream().map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    public boolean updateBySampleId(SampleDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final TbSample tbSample = JSON.parseObject(JSON.toJSONString(dto), TbSample.class);
        if (sampleMapper.updateById(tbSample) < 1) {
            return false;
        }
        log.info("用户 [{}] 修改样本成功 [{}]", user.getNickname(), JSON.toJSONString(dto));
        return true;
    }

    @Nullable
    @Override
    public SampleDto selectByApplySampleId(long applySampleId) {
        final LambdaQueryWrapper<TbSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSample::getApplySampleId, applySampleId);
        wrapper.last("limit 1");
        return convert(sampleMapper.selectOne(wrapper));
    }

    @Override
    public List<SampleDto> selectByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbSample::getApplySampleId, applySampleIds);

        return sampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<SampleDto> selectByApplyId(long applyId) {
        return sampleMapper.selectList(new LambdaQueryWrapper<TbSample>().eq(TbSample::getApplyId, applyId)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<SampleDto> selectBySampleIds(Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSample::getSampleId, sampleIds);
        queryWrapper.eq(TbSample::getIsDelete, YesOrNoEnum.NO.getCode());
        return convert(sampleMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditSamplesChain(SampleAuditDto dto) {
        final AuditSampleContext context = new AuditSampleContext(dto);
        final Long sampleId = dto.getSampleId();
        try {
            if (!auditSampleChain.execute(context)) {
                throw new IllegalStateException("新增失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            log.info("审核样本 [{}] 耗时:\n{}", StringUtils.defaultString(sampleId.toString()),
                    context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    private static final String ROUTING_KEY = "sample_change_key";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOneAuditSample(SampleCancelAuditDto dto) {
        // key : 常规样本id value: 样本信息
        Map<Long, SampleDto> sampleDtoBySampleId = sampleService.selectBySampleIds(dto.getSampleIds()).stream()
                .collect(Collectors.toMap(SampleDto::getSampleId, Function.identity()));
        if (sampleDtoBySampleId.isEmpty()) {
            return;
        }
        if (dto.getSampleIds().stream().anyMatch(x -> !sampleDtoBySampleId.containsKey(x))) {
            throw new IllegalStateException("存在无效样本");
        }
        // 对应 申请单 样本ids
        Set<Long> applySampleIds =
                sampleDtoBySampleId.values().stream().map(SampleDto::getApplySampleId).collect(Collectors.toSet());

        Map<Long, ApplySampleDto> applySampleDtoByApplySampleId =
                applySampleService.selectByApplySampleIds(applySampleIds).stream()
                        .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));

        Set<Long> archiveSampleIds = new HashSet<>();

        for (Map.Entry<Long, SampleDto> entry : sampleDtoBySampleId.entrySet()) {
            SampleDto sample = entry.getValue();
            ApplySampleDto applySample = applySampleDtoByApplySampleId.get(sample.getApplySampleId());
            if (Objects.isNull(applySample)) {
                throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 申请单样本不存在");
            }
            if(StringUtils.isNotBlank(applySample.getMergeExtraInfo())){
                List<ApplySampleDto> applySampleDtos = applySampleService.selectMergeByBarcodes(applySample.getBarcode());
                List<String> barcodes = applySampleDtos.stream().map(ApplySampleDto::getBarcode).collect(Collectors.toList());
                throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 已并单，请取消["+ CollUtil.join(barcodes, ",") +"]并单后重试");
            }
            if(StringUtils.isNotBlank(applySample.getMergeMasterBarcode())){
                throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 申请单样本被合并, 不能取消一审");
            }
            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) {
                throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 不是一审状态");
            }

            if (applySampleService.isDisabled(sample.getApplySampleId())) {
                throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", sample.getBarcode()));
            }

            if (applySampleService.isTerminate(sample.getApplySampleId())) {
                throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", sample.getBarcode()));
            }

            if (BooleanUtils.isTrue(financeSampleLockService.isSampleLock(sample.getApplySampleId()))) {
                throw new IllegalStateException("该样本已经加锁，不能取消审核");
            }

            if (Objects.equals(applySample.getIsArchive(), YesOrNoEnum.YES.getCode())) {
                //这里变成批量自动提取归档的样本 慎用！！！
                archiveSampleIds.add(applySample.getApplySampleId());
            }
        }

        //如果有要自动提取归档的样本。在这里执行
        if (CollectionUtils.isNotEmpty(archiveSampleIds)){
            List<RackLogicSpaceDto> spaceDtos = rackLogicSpaceService.selectByApplySampleIds(archiveSampleIds);
            if (CollectionUtils.isNotEmpty(spaceDtos)){
                //调用提取服务
                AutoExtractArchiveSampleDto autoExtractArchiveSampleDto = new AutoExtractArchiveSampleDto();
                autoExtractArchiveSampleDto.setExtractDesc("取消一审自动提取");
                autoExtractArchiveSampleDto.setRackLogicSpaceDtoList(spaceDtos);
                rackArchiveService.autoExtractArchiveSample(autoExtractArchiveSampleDto);
            }
        }

        // 修改申请单 样本状态
        ApplySampleDto updateApplySample = new ApplySampleDto();
        updateApplySample.setColorMarking(SampleStatusEnum.COUNTERTRIAL.getCode());
        updateApplySample.setStatus(SampleStatusEnum.NOT_AUDIT.getCode());
        updateApplySample.setIsArchive(YesOrNoEnum.NO.getCode());
        applySampleService.updateByApplySampleIds(updateApplySample, applySampleDtoByApplySampleId.keySet());

        // 检验 样本一审人信息 取消
        SampleDto updateSampleDto = new SampleDto();
        updateSampleDto.setOneCheckerId(NumberUtils.LONG_ZERO);
        updateSampleDto.setOneCheckerName(StringUtils.EMPTY);
        updateSampleDto.setOneCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        this.updateBySampleIds(updateSampleDto, sampleDtoBySampleId.keySet());

        List<SampleFlowDto> flowDtoList = Lists.newArrayList();
        for (Map.Entry<Long, SampleDto> entry : sampleDtoBySampleId.entrySet()) {
            SampleDto sample = entry.getValue();

            SampleFlowDto flow = new SampleFlowDto();
            flow.setApplyId(sample.getApplyId());
            flow.setApplySampleId(sample.getApplySampleId());
            flow.setBarcode(sample.getBarcode());
            flow.setOperateCode(BarcodeFlowEnum.CANCEL_ONE_CHECK.name());
            flow.setOperateName(BarcodeFlowEnum.CANCEL_ONE_CHECK.getDesc());
            flow.setOperator(LoginUserHandler.get().getNickname());
            flow.setContent("取消一审");
            flow.setOrgId(LoginUserHandler.get().getOrgId());
            flow.setOrgName(LoginUserHandler.get().getOrgName());
            flow.setIsDelete(YesOrNoEnum.NO.getCode());
            flow.setCreateDate(new Date());
            flow.setCreatorId(LoginUserHandler.get().getUserId());
            flow.setCreatorName(LoginUserHandler.get().getNickname());
            flow.setUpdateDate(new Date());
            flow.setUpdaterId(LoginUserHandler.get().getUserId());
            flow.setUpdaterName(LoginUserHandler.get().getNickname());
            flowDtoList.add(flow);
        }

        sampleFlowService.addSampleFlows(flowDtoList);

        final LoginUserHandler.User user = LoginUserHandler.get();

        threadPoolConfig.getPool().submit(() -> {
            try {
                Set<Long> applyIds =
                        sampleDtoBySampleId.values().stream().map(SampleDto::getApplyId).collect(Collectors.toSet());
                Map<Long, ApplyDto> applyDtoByApplyId = applyService.selectByApplyIds(applyIds).stream()
                        .collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

                for (Map.Entry<Long, SampleDto> entry : sampleDtoBySampleId.entrySet()) {
                    SampleDto sample = entry.getValue();
                    ApplyDto apply = applyDtoByApplyId.get(sample.getApplyId());
                    ApplySampleDto applySample = applySampleDtoByApplySampleId.get(sample.getApplySampleId());
                    if (Objects.isNull(apply)) {
                        log.info("申请单不存在发送mq消息失败");
                        continue;
                    }
                    final ApplySampleEventDto event = new ApplySampleEventDto();

                    event.setOrgId(user.getOrgId());
                    event.setHspOrgId(apply.getHspOrgId());
                    event.setHspOrgCode(apply.getHspOrgCode());
                    event.setHspOrgName(apply.getHspOrgName());
                    event.setApplyId(sample.getApplyId());
                    event.setApplySampleId(sample.getApplySampleId());
                    event.setBarcode(sample.getBarcode());
                    event.setExtras(Map.of("sampleId", String.valueOf(sample.getSampleId()), "outBarcode",
                            String.valueOf(applySample.getOutBarcode()), "sampleNo", String.valueOf(sample.getSampleNo()),
							// 项目类型 常规检验
		                    ApplySampleEventDto.ExtrasConstants.ITEM_TYPE, ItemTypeEnum.ROUTINE.name()));
                    event.setEvent(ApplySampleEventDto.EventType.CancelOneCheck);

                    final String json = JSON.toJSONString(event);
                    rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

                    log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", sample.getApplySampleId(), sample.getBarcode(),
                            json, RabbitMQService.EXCHANGE, ROUTING_KEY);
                }

            } catch (Exception e) {
                log.error("发送消息失败 {}", e.getMessage(), e);
            }
        });

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTwoAuditSample(SampleCancelAuditDto dto) {
        // key : 常规样本id value: 样本信息
        Map<Long, SampleDto> sampleDtoBySampleId = sampleService.selectBySampleIds(dto.getSampleIds()).stream()
                .collect(Collectors.toMap(SampleDto::getSampleId, Function.identity()));
        if (sampleDtoBySampleId.isEmpty()) {
            return;
        }
        if (dto.getSampleIds().stream().anyMatch(x -> !sampleDtoBySampleId.containsKey(x))) {
            throw new IllegalStateException("存在无效样本");
        }
        // 对应 申请单 样本ids
        Set<Long> applySampleIds =
                sampleDtoBySampleId.values().stream().map(SampleDto::getApplySampleId).collect(Collectors.toSet());

        Map<Long, ApplySampleDto> applySampleDtoByApplySampleId =
                applySampleService.selectByApplySampleIds(applySampleIds).stream()
                        .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));

        for (Map.Entry<Long, SampleDto> entry : sampleDtoBySampleId.entrySet()) {
            SampleDto sample = entry.getValue();
            ApplySampleDto applySample = applySampleDtoByApplySampleId.get(sample.getApplySampleId());
            if (Objects.isNull(applySample)) {
                throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 申请单样本不存在");
            }

            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
                throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 不是二审状态");
            }

            if (applySampleService.isDisabled(sample.getApplySampleId())) {
                throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", sample.getBarcode()));
            }

            if (applySampleService.isTerminate(sample.getApplySampleId())) {
                throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", sample.getBarcode()));
            }

            if (BooleanUtils.isTrue(financeSampleLockService.isSampleLock(sample.getApplySampleId()))) {
                throw new IllegalStateException("该样本已经加锁，不能取消审核");
            }
        }

        // 修改申请单 样本状态
        ApplySampleDto updateApplySample = new ApplySampleDto();
        updateApplySample.setColorMarking(SampleStatusEnum.COUNTERTRIAL.getCode());
        updateApplySample.setStatus(SampleStatusEnum.ONE_AUDIT.getCode());
        updateApplySample.setPrintDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        updateApplySample.setIsPrint(YesOrNoEnum.NO.getCode());
        updateApplySample.setPrinterName(StringUtils.EMPTY);
        updateApplySample.setPrinterId(NumberUtils.LONG_ZERO);
        applySampleService.updateByApplySampleIds(updateApplySample, applySampleDtoByApplySampleId.keySet());

        // 检验 样本二审人信息 取消
        SampleDto updateSampleDto = new SampleDto();
        updateSampleDto.setTwoCheckerId(NumberUtils.LONG_ZERO);
        updateSampleDto.setTwoCheckerName(StringUtils.EMPTY);
        updateSampleDto.setTwoCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        this.updateBySampleIds(updateSampleDto, sampleDtoBySampleId.keySet());

        // 删除报告单 需要判断是否是上传pdf文件 手动上传的文件需要保留
//        sampleReportService.deleteBySampleIds(sampleDtoBySampleId.keySet());
        sampleReportService.deleteBySampleIdsAndNotUpload(sampleDtoBySampleId.keySet());
        List<SampleFlowDto> flowDtoList = Lists.newArrayList();
        for (Map.Entry<Long, SampleDto> entry : sampleDtoBySampleId.entrySet()) {
            SampleDto sample = entry.getValue();

            SampleFlowDto flow = new SampleFlowDto();
            flow.setApplyId(sample.getApplyId());
            flow.setApplySampleId(sample.getApplySampleId());
            flow.setBarcode(sample.getBarcode());
            flow.setOperateCode(BarcodeFlowEnum.CANCEL_TWO_CHECK.name());
            flow.setOperateName(BarcodeFlowEnum.CANCEL_TWO_CHECK.getDesc());
            flow.setOperator(LoginUserHandler.get().getNickname());
            flow.setContent("取消二审");
            flow.setOrgId(LoginUserHandler.get().getOrgId());
            flow.setOrgName(LoginUserHandler.get().getOrgName());
            flow.setIsDelete(YesOrNoEnum.NO.getCode());
            flow.setCreateDate(new Date());
            flow.setCreatorId(LoginUserHandler.get().getUserId());
            flow.setCreatorName(LoginUserHandler.get().getNickname());
            flow.setUpdateDate(new Date());
            flow.setUpdaterId(LoginUserHandler.get().getUserId());
            flow.setUpdaterName(LoginUserHandler.get().getNickname());
            flowDtoList.add(flow);
        }

        sampleFlowService.addSampleFlows(flowDtoList);

        final LoginUserHandler.User user = LoginUserHandler.get();

        threadPoolConfig.getPool().submit(() -> {
            try {
                Set<Long> applyIds =
                        sampleDtoBySampleId.values().stream().map(SampleDto::getApplyId).collect(Collectors.toSet());
                Map<Long, ApplyDto> applyDtoByApplyId = applyService.selectByApplyIds(applyIds).stream()
                        .collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

                for (Map.Entry<Long, SampleDto> entry : sampleDtoBySampleId.entrySet()) {
                    SampleDto sample = entry.getValue();
                    ApplyDto apply = applyDtoByApplyId.get(sample.getApplyId());
                    ApplySampleDto applySample = applySampleDtoByApplySampleId.get(sample.getApplySampleId());
                    if (Objects.isNull(apply)) {
                        log.info("申请单不存在发送mq消息失败");
                        continue;
                    }
                    final ApplySampleEventDto event = new ApplySampleEventDto();

                    event.setOrgId(user.getOrgId());
                    event.setHspOrgId(apply.getHspOrgId());
                    event.setHspOrgCode(apply.getHspOrgCode());
                    event.setHspOrgName(apply.getHspOrgName());
                    event.setApplyId(sample.getApplyId());
                    event.setApplySampleId(sample.getApplySampleId());
                    event.setBarcode(sample.getBarcode());
                    event.setExtras(Map.of("sampleId", String.valueOf(sample.getSampleId()), "outBarcode",
                            String.valueOf(applySample.getOutBarcode()), "sampleNo", String.valueOf(sample.getSampleNo()),
                            "businessCenterOrgCode", envDetector.getBusinessCenterOrgCode(),
							// 项目类型：常规
		                    ApplySampleEventDto.ExtrasConstants.ITEM_TYPE, ItemTypeEnum.ROUTINE.name()));
                    event.setEvent(ApplySampleEventDto.EventType.CancelTwoCheck);

                    final String json = JSON.toJSONString(event);
                    rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

                    log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", sample.getApplySampleId(), sample.getBarcode(),
                            json, RabbitMQService.EXCHANGE, ROUTING_KEY);
                }
            } catch (Exception e) {
                log.error("发送消息失败 {}", e.getMessage(), e);
            }

        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long twoPick(long applySampleId, long instrumentGroupId, String sampleNo, long instrumentId) {

        final TwoPickContext context = new TwoPickContext();
        context.setApplySampleId(applySampleId);
        context.setInstrumentGroupId(instrumentGroupId);
        context.setInstrumentId(instrumentId);
        context.setSampleNo(sampleNo);

        try {
            if (!twoPickChain.execute(context)) {
                throw new IllegalStateException("二次分拣失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        }

        log.info("常规检验样本二次分拣完成 样本ID [{}],条码号 [{}] 样本号 [{}] 耗时 \n{}", context.getSample().getSampleId(),
                context.getSample().getBarcode(), sampleNo, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));

        return context.getSample().getSampleId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long twoPick(long applySampleId, long instrumentGroupId, String sampleNo, long instrumentId, Date immunityTwoPickDate) {

        final TwoPickContext context = new TwoPickContext();
        context.setApplySampleId(applySampleId);
        context.setInstrumentGroupId(instrumentGroupId);
        context.setInstrumentId(instrumentId);
        context.setSampleNo(sampleNo);
        context.setImmunityTwoPickDate(immunityTwoPickDate);

        try {
            if (!twoPickChain.execute(context)) {
                throw new IllegalStateException("二次分拣失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        }

        log.info("常规检验样本二次分拣完成 样本ID [{}],条码号 [{}] 样本号 [{}] 耗时 \n{}", context.getSample().getSampleId(),
                context.getSample().getBarcode(), sampleNo, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));

        return context.getSample().getSampleId();
    }

    @Override
    public RoutineSampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode())
                || Objects.equals(e.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new IllegalStateException("该样本已经审核，不能取消二次分拣");
        }

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.RETEST.getCode()))) {
            throw new IllegalStateException("该样本正在复查，不能取消二次分拣");
        }

        final List<SampleDto> samples = selectByApplySampleIds(
                applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(samples)) {
            throw new IllegalStateException("常规检验样本不存在");
        }

        final Set<Long> ids = samples.stream().map(SampleDto::getSampleId).collect(Collectors.toSet());

        // 找到结果
        final Map<Long, List<SampleResultDto>> results = sampleResultService.selectBySamplesIdAsMap(ids);

        // 把 applySampleId 作为 sampleId 落入到库里面；然后当二次分拣的时候把applySampleId作为sampleId去查询结果，如果有那么恢复结果。
        // https://www.tapd.cn/59091617/prong/stories/view/1159091617001000672
        final List<SampleResultDto> newResults = new LinkedList<>();
        final LinkedList<Long> newIds = snowflakeService.genIds(results.values().stream().mapToInt(List::size).sum());
        for (SampleDto sample : samples) {
            for (SampleResultDto k : results.getOrDefault(sample.getSampleId(), List.of())) {
                k.setSampleResultId(newIds.pop());
                k.setSampleId(sample.getApplySampleId());
                newResults.add(k);
            }
        }

        if (CollectionUtils.isNotEmpty(newResults)) {
            // 添加结果
            sampleResultService.addSampleResults(newResults);

            log.info("条码 {} 结果保存成功", samples.stream().map(SampleDto::getBarcode).collect(Collectors.toList()));
        }

        // 删除样本
        deleteBySampleIds(ids);

        // 删除样本报告项目
        sampleReportItemService.deleteBySampleIds(ids);

        // 删除样本结果
        sampleResultService.deleteBySampleIds(ids);

        // 删除 危机值
        sampleCriticalResultService.deleteBySampleIds(ids);

        // 清空生清单样本的结果备注，样本备注以及检验者
        final ApplySampleDto as = new ApplySampleDto();
        as.setSampleRemark(StringUtils.EMPTY);
        as.setResultRemark(StringUtils.EMPTY);
        as.setTesterId(NumberUtils.LONG_ZERO);
        as.setTesterName(StringUtils.EMPTY);
        applySampleService.updateByApplySampleIds(as, applySampleIds);

        // 删除缺项 redis
        stringRedisTemplate
                .delete(ids.stream().map(updateMissItemCommand::getMissItemKey).collect(Collectors.toList()));


        log.info("常规检验检验取消二次分拣 样本ID [{}] 条码号 [{}] 操作人 [{}]", ids, applySamples.stream().map(ApplySampleDto::getBarcode)
                .collect(Collectors.toSet()), LoginUserHandler.get().getNickname());

        return new RoutineSampleTwoUnPickInfoDto(samples.stream()
                .map(e -> new RoutineSampleTwoUnPickInfoDto.Sample().setSampleId(e.getSampleId())
                        .setSampleNo(e.getSampleNo()).setGroupId(e.getGroupId())
                        .setTwoPickDate(
                                applySamples.stream().filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                                        .findFirst().map(ApplySampleDto::getTwoPickDate).orElseThrow())
                        .setInstrumentGroupId(e.getInstrumentGroupId()))
                .collect(Collectors.toList()));
    }

    @Override
    public long addSample(SampleDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final TbSample sample = JSON.parseObject(JSON.toJSONString(dto), TbSample.class);
        sample.setSampleId(ObjectUtils.defaultIfNull(dto.getSampleId(), snowflakeService.genId()));
        sample.setCreateDate(new Date());
        sample.setCreatorId(user.getUserId());
        sample.setCreatorName(user.getNickname());
        sample.setUpdateDate(new Date());
        sample.setUpdaterId(user.getUserId());
        sample.setUpdaterName(user.getNickname());
        sample.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbSampleMapper.insert(sample) < 1) {
            throw new IllegalStateException("添加失败");
        }
        log.info("用户 [{}] 添加常规检验样本项成功 [{}]", user.getNickname(), JSON.toJSONString(sample));
        return sample.getSampleId();
    }

    @Override
    public boolean deleteBySampleId(long sampleId) {

        if (tbSampleMapper.deleteById(sampleId) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除常规检验样本 [{}] 成功", LoginUserHandler.get().getNickname(), sampleId);

        return true;
    }

    @Override
    public void deleteBySampleIds(Collection<Long> sampleIds) {

        if (CollectionUtils.isEmpty(sampleIds)) {
            return;
        }

        tbSampleMapper.deleteBatchIds(sampleIds);

        log.info("用户 [{}] 删除常规检验样本 [{}] 成功", LoginUserHandler.get().getNickname(), sampleIds);

    }

    @Override
    public List<SampleDto> selectByApplyIds(Collection<Long> applyIds) {

        if (CollectionUtils.isEmpty(applyIds)) {
            return List.of();
        }
        final LambdaQueryWrapper<TbSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbSample::getApplyId, applyIds);
        return tbSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
    @SneakyThrows
    public SampleReportDto rebuildReport(long applySampleId) {
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            log.info("申请单样本不存在 [{}]", applySampleId);
            return null;
        }
        if (BooleanUtils.isFalse(Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode()))) {
            log.info("申请单样本状态不是审核状态 [{}]", applySampleId);
            return null;
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            log.info("申请单不存在 [{}]", applySample.getApplyId());
            return null;
        }

        final SampleDto sample = selectByApplySampleId(applySampleId);
        if (Objects.isNull(sample)) {
            log.info("样本不存在 [{}]", applySampleId);
            return null;
        }

        final List<SampleReportItemDto> sampleReportItems =
                sampleReportItemService.selectBySampleId(sample.getSampleId());
        final List<SampleResultDto> sampleResults = sampleResultService.selectBySampleId(sample.getSampleId());

        // 获取 对应仪器报告项目
        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentGroupId(sample.getInstrumentGroupId());

        final SampleReportDto sampleReport = buildReportCommand.buildPDF(sample, apply, applySample, sampleReportItems,
                sampleResults, instrumentReportItems, applySampleItemService.selectByApplySampleId(applySampleId));

        log.info("重新生成报告: [{}]", sampleReport.getUrl());
        return sampleReport;
    }

    @Override
    @Nullable
    public SampleDto selectByBarcode(String barcode) {

        final LambdaQueryWrapper<TbSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSample::getOrgId, LoginUserHandler.get().getOrgId()).eq(TbSample::getBarcode, barcode);
        return convert(tbSampleMapper.selectOne(wrapper.last("limit 1")));
    }

    @Override
    public List<SampleDto> selectAllByBarcode(String barcode) {
        final LambdaQueryWrapper<TbSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSample::getOrgId, LoginUserHandler.get().getOrgId()).eq(TbSample::getBarcode, barcode);
        return convert(tbSampleMapper.selectList(wrapper));
    }

    @Override
    public List<SampleDto> selectAllByBarcodes(List<String> barcodes) {
        final LambdaQueryWrapper<TbSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSample::getOrgId, LoginUserHandler.get().getOrgId()).in(TbSample::getBarcode, barcodes);
        return convert(tbSampleMapper.selectList(wrapper));
    }

    @Override
    public List<SampleDto> selectByCreateDate(Date beginCreateDate, Date endCreateDate, @Nullable Long groupId) {
        return tbSampleMapper
                .selectList(new LambdaQueryWrapper<TbSample>().ge(TbSample::getCreateDate, beginCreateDate)
                        .le(TbSample::getCreateDate, endCreateDate).eq(Objects.nonNull(groupId), TbSample::getGroupId, groupId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public void updateBySampleIds(SampleDto sampleDto, Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return;
        }
        tbSampleMapper.updateBySampleIds(sampleDto, sampleIds);
    }

    @Override
    public List<SampleDto> selectBySampleNos(Collection<String> sampleNos, LocalDate testDate) {
        if(CollectionUtils.isEmpty(sampleNos)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSample> wrapper = Wrappers.lambdaQuery(TbSample.class).in(TbSample::getSampleNo, sampleNos).between(TbSample::getTestDate, testDate, testDate.plusDays(1));
        return tbSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public SampleDto selectBySampleNo(String sampleNo, LocalDate testDate) {
        if(StringUtils.isBlank(sampleNo)){
            return null;
        }
        LambdaQueryWrapper<TbSample> wrapper = Wrappers.lambdaQuery(TbSample.class).eq(TbSample::getSampleNo, sampleNo).between(TbSample::getTestDate, testDate, testDate.plusDays(1)).last(" limit 1");
        return this.convert(tbSampleMapper.selectOne(wrapper));
    }

    @Override
    public void updateByApplyId(SampleDto sampleDto) {
        LambdaUpdateWrapper<TbSample> wrapper = Wrappers.lambdaUpdate(TbSample.class)
                .eq(TbSample::getApplyId, sampleDto.getApplyId())
                .eq(TbSample::getIsDelete, 0)
                .set(TbSample::getHspOrgId, sampleDto.getHspOrgId())
                .set(TbSample::getHspOrgName, sampleDto.getHspOrgName())
                .set(TbSample::getUpdaterId, sampleDto.getUpdaterId())
                .set(TbSample::getUpdaterName, sampleDto.getUpdaterName())
                .set(TbSample::getUpdateDate, sampleDto.getUpdateDate());
        sampleMapper.update(null, wrapper);

    }

    @Override
    public void updateByApplyIds(SampleDto sampleDto, Collection<Long> applyIds) {
        for (List<Long> item : com.google.common.collect.Lists.partition(new ArrayList<>(applyIds), 500)) {
            LambdaUpdateWrapper<TbSample> wrapper = Wrappers.lambdaUpdate(TbSample.class).in(TbSample::getApplyId, item)
                .eq(TbSample::getIsDelete, 0).set(TbSample::getHspOrgId, sampleDto.getHspOrgId())
                .set(TbSample::getHspOrgName, sampleDto.getHspOrgName())
                .set(TbSample::getUpdaterId, sampleDto.getUpdaterId())
                .set(TbSample::getUpdaterName, sampleDto.getUpdaterName())
                .set(TbSample::getUpdateDate, sampleDto.getUpdateDate());
            sampleMapper.update(null, wrapper);
        }
    }

    @Override
    public SampleDto selectBySampleId(long sampleId) {
        return convert(sampleMapper.selectById(sampleId));
    }

    @Override
    public ApplyUpdateBeforeCheckTipDto updateCheckExceptionAndCritical(List<ApplySampleDto> applySampleDtos, ApplyDto applyDto) {
        Long applyId = applyDto.getApplyId();

        List<ApplyUpdateBeforeCheckTipItemDto> criticalTips = new ArrayList<>();
        List<ApplyUpdateBeforeCheckTipItemDto> exceptionTips = new ArrayList<>();
        ApplyUpdateBeforeCheckTipDto checkTipDto = new ApplyUpdateBeforeCheckTipDto();
        checkTipDto.setCriticals(criticalTips);
        checkTipDto.setExceptions(exceptionTips);

        if (CollectionUtils.isEmpty(applySampleDtos)) {
            return checkTipDto;
        }

        Map<Long, ApplySampleDto> applySampleDtoMap = applySampleDtos.stream()
                .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity(), (a, b) -> b));

        List<SampleDto> sampleDtos = sampleService.selectByApplyId(applyId);
        sampleDtos.forEach(sampleDto -> {
            ApplySampleDto applySampleDto = applySampleDtoMap.get(sampleDto.getApplySampleId());
            // 样本报告项目
            List<SampleReportItemDto> sampleReportItemDtos = sampleReportItemService.selectBySampleId(sampleDto.getSampleId());

            // 仪器报告项目
            final List<InstrumentReportItemDto> reportItems = instrumentReportItemService.selectByInstrumentGroupId(sampleDto.getInstrumentGroupId());
            // 样本结果 - 只查询数字类型的结果
            List<SampleResultDto> sampleResultDtos = sampleResultService.selectBySampleId(sampleDto.getSampleId());

            for (SampleResultDto sampleResult : sampleResultDtos) {
                String reportItemCode = sampleResult.getReportItemCode();

                InstrumentReportItemDto instrumentReportItem;

                // 取样本仪器报告项目，取不到为空
                instrumentReportItem = reportItems.stream().filter(e -> Objects.equals(e.getInstrumentId(), sampleDto.getInstrumentId())
                                && Objects.equals(e.getReportItemCode(), reportItemCode)).findFirst().orElse(null);
                // 在取专业小组报告项目
                if (Objects.isNull(instrumentReportItem)) {
                    instrumentReportItem = reportItems.stream().filter(e -> Objects.equals(e.getReportItemCode(), reportItemCode)).findFirst().orElse(null);
                }
                if (Objects.isNull(instrumentReportItem)) {
                    throw new IllegalStateException("仪器报告项目不存在");
                }

                // 非数值结果跳过处理
                if (!instrumentReportItem.getResultTypeCode().equals(TestResultTypeEnum.NUMBER.getCode()) || !NumberUtils.isParsable(sampleResult.getResult())) {
                    continue;
                }

                final List<InstrumentReportItemReferenceDto> refs = instrumentReportItemReferenceService.selectByInstrumentReportItemId(instrumentReportItem.getInstrumentReportItemId());
                if (CollectionUtils.isEmpty(refs)) {
                    continue;
                }
                final List<InstrumentReportItemReferenceDto> sampleRefs = refs.stream()
                        .filter(e -> Objects.equals(e.getInstrumentId(), sampleDto.getInstrumentId()))
                        .collect(Collectors.toList());
                InstrumentReportItemReferenceDto instrumentReportItemReferenceDto;
                instrumentReportItemReferenceDto =
                        instrumentReportReferenceCommand.filterCustomerReportReference(applyDto, applySampleDto, sampleRefs);
                // 先取样本仪器上的参考范围
                if (Objects.isNull(instrumentReportItemReferenceDto)) {
                    instrumentReportItemReferenceDto =
                            instrumentReportReferenceCommand.filterCustomerReportReference(applyDto, applySampleDto, refs);
                }

                if (Objects.nonNull(instrumentReportItemReferenceDto)) {
                    BigDecimal result = BigDecimal.valueOf(Double.parseDouble(sampleResult.getResult()));
                    boolean isCritical = numberResultCommand.checkIsCritical(result, instrumentReportItem, instrumentReportItemReferenceDto);
                    boolean isException = numberResultCommand.checkIsException(result, instrumentReportItem, instrumentReportItemReferenceDto);

                    SampleFlowDto sampleFlowDto;
                    if (isCritical || isException) {
                        List<SampleFlowDto> sampleFlowDtos = sampleFlowService.selectByBarcode(applySampleDto.getBarcode());
                        sampleFlowDto = sampleFlowDtos.get(sampleFlowDtos.size() - 1);

                        ApplyUpdateBeforeCheckTipItemDto tipDto = new ApplyUpdateBeforeCheckTipItemDto(
                                applySampleDto.getBarcode(), applySampleDto.getGroupName(), sampleFlowDto.getOperateName(),
                                sampleResult.getReportItemName(), sampleResult.getResult(),
                                instrumentReportItemReferenceDto.getCnRefereValue());
                        if (isCritical) {
                            criticalTips.add(tipDto);
                        } else {
                            exceptionTips.add(tipDto);
                        }
                    }
                }
            }
        });

        return checkTipDto;
    }

    @Override
    public long insertTest() {
        final TbSample sample = new TbSample();
        sample.setSampleId(snowflakeService.genId());
        sample.setApplySampleId(NumberUtils.LONG_ZERO);
        sample.setApplyId(NumberUtils.LONG_ZERO);
        sample.setBarcode(IdUtil.objectId());
        sample.setSampleNo(String.valueOf(RandomUtils.nextInt(0, 100)));
        sample.setGroupId(NumberUtils.LONG_ZERO);
        sample.setGroupName(StringUtils.EMPTY);
        sample.setInstrumentGroupId(NumberUtils.LONG_ZERO);
        sample.setInstrumentGroupName(StringUtils.EMPTY);
        sample.setInstrumentName(StringUtils.EMPTY);
        sample.setInstrumentId(NumberUtils.LONG_ZERO);
        sample.setTestDate(new Date());
        sample.setOneCheckerName(StringUtils.EMPTY);
        sample.setOneCheckerId(NumberUtils.LONG_ZERO);
        sample.setTwoCheckerName(StringUtils.EMPTY);
        sample.setTwoCheckerId(NumberUtils.LONG_ZERO);
        sample.setUpdateDate(new Date());
        sample.setCreateDate(new Date());
        sample.setUpdaterId(NumberUtils.LONG_ZERO);
        sample.setUpdaterName(StringUtils.EMPTY);
        sample.setCreatorId(NumberUtils.LONG_ZERO);
        sample.setCreatorName(StringUtils.EMPTY);
        sample.setHspOrgId(NumberUtils.LONG_ZERO);
        sample.setHspOrgName(StringUtils.EMPTY);
        sample.setOrgId(1L);
        sample.setOrgName(StringUtils.EMPTY);

        sampleMapper.insert(sample);

        return sample.getSampleId();
    }

    private SampleDto convert(TbSample sample) {
        if (Objects.isNull(sample)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(sample), SampleDto.class);
    }

    /**
     * TbSample 转换 为 SampleDto
     *
     * @param list TbSample
     * @return SampleDto
     */
    private List<SampleDto> convert(List<TbSample> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }

}
