package com.labway.lims.routine.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 危机值列表 参数
 * 
 * <AUTHOR>
 * @since 2023/4/10 10:44
 */
@Getter
@Setter
public class SampleCriticalResultListRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 检验日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateEnd;

}
