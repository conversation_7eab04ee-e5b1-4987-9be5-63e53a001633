package com.labway.lims.routine.service.chain.auto.audit;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.service.chain.StopWatchContext;
import com.labway.lims.routine.service.chain.result.SaveResultContext;
import com.labway.lims.routine.vo.ruleengine.AutoAuditResult;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自动审核上下文
 *
 * <AUTHOR> on 2025/7/29.
 */
@Getter
@Setter
@NoArgsConstructor
public class AutoAuditContext extends StopWatchContext {

	@Override
	protected String getWatchName() {
		return "自动审核";
	}

	/**
	 * 获取自动审核参数
	 */
	private static final Map<String, AutoAuditContext> CONTEXT_MAP = new HashMap<>();

	public static AutoAuditContext from(Context c) {
		if (c instanceof SaveResultContext) {
			SaveResultContext saveResultContext = (SaveResultContext) c;
			return CONTEXT_MAP.computeIfAbsent(ObjectUtils.identityToString(c),
					k -> new AutoAuditContext(saveResultContext, Boolean.TRUE));
		}
		return (AutoAuditContext) c;
	}

	public AutoAuditContext(SaveResultContext saveResultOriginContext, boolean isAuto) {
		this.saveResultOriginContext = saveResultOriginContext;
		this.isAuto = isAuto;
		this.sampleId = saveResultOriginContext.getSample().getSampleId();
	}

	/**
	 * 保存结果的原始上下文
	 */
	public SaveResultContext saveResultOriginContext;

	/**
	 * 是否为自动触发
	 */
	public boolean isAuto;

    /**
     * sampleId
     */
    private Long sampleId;

	/**
	 * 自动审核结果
	 */
	private boolean autoAuditResultStatus = Boolean.FALSE;

	/**
	 * 错误信息
	 */
	private String autoAuditErrorInfo;

    /**
     * jvs 样本ID
     */
    public static final String JVS_SAMPLE_ID = "sampleId";

    /**
     * jvs 样本类型
     */
    public static final String JVS_SAMPLE_TYPE = "sampleType";

    /**
     * jvs 年龄（数值/岁）
     */
    public static final String JVS_AGE = "age";

    /**
     * jvs 年龄（数值/岁）
     */
    public static final String JVS_SEX = "sex";

    /**
     * jvs 子年龄（数值）
     */
    public static final String JVS_SUB_AGE = "subAge";

	/**
	 * jvs 年龄换算出来的天数
	 */
	public static final String JVS_AGE_DAYS = "ageDays";

    /**
     * jvs 子年龄单位（数值）
     */
    public static final String JVS_SUB_AGE_UNIT = "ageUnit";

	/**
	 * 样本下的检验项目 {@link TestItemDto}
	 */
	public static final String SAMPLE_TEST_ITEMS = "SAMPLE_TEST_ITEMS" + IdUtil.objectId();

	/**
	 * 样本下的检验项目 {@link com.labway.lims.routine.api.dto.SampleResultDto}
	 */
	public static final String SAMPLE_RESULT = "SAMPLE_RESULT" + IdUtil.objectId();

    /**
     * 自动审核执行结果
     */
    static final String AUTO_AUDIT_RESULT_LIST = "AUTO_AUDIT_RESULT_LIST" + IdUtil.objectId();

	/**
	 * 获取样本下的检验项目
	 * @return {@link TestItemDto}
	 */
	public List<TestItemDto> getSampleTestItems() {
		return (List<TestItemDto>) get(SAMPLE_TEST_ITEMS);

	}

	/**
	 * 获取样本下的检验结果
	 * @return {@link SampleResultDto}
	 */
	public List<SampleResultDto> getSampleResults() {
		return (List<SampleResultDto>) get(SAMPLE_RESULT);
	}

	/**
	 * 自动审核结果
	 * @return
	 */
    public List<AutoAuditResult> getAutoAuditResultList() {
        return (List<AutoAuditResult>) get(AUTO_AUDIT_RESULT_LIST);
    }
}
