package com.labway.lims.routine.controller;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.UploadPdfDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.routine.vo.QuerySampleByReportVo;
import com.labway.lims.routine.vo.QuerySampleResultVo;
import com.labway.lims.routine.vo.ResultSampleByReportVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/10 15:50
 */
@RestController
@RequestMapping("/sample")
public class SampleReportController extends BaseController {

    @Resource
    private SampleService sampleService;

    @DubboReference
    private ReportItemService reportItemService;

    @Resource
    private SampleResultService sampleResultService;

    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    @DubboReference
    private SampleReportService sampleReportService;


    /**
     * 返回当前专业组下的所有样本以及结果
     */
    @PostMapping("/samples")
    public Object samples(@RequestBody QuerySampleResultVo vo) {
        if (Objects.isNull(vo.getGroupId()) || Objects.isNull(vo.getOrgId())) {
            throw new IllegalArgumentException("专业组不能为空");
        }
        return sampleService.selectByGroupId(vo.getGroupId());
    }

    /**
     * 根据报告项目获取含有报告项目的样本信息
     */
    @PostMapping("/findSamplesByReport")
    public Object findSamplesByReport(@RequestBody QuerySampleByReportVo vo) {
        if (StringUtils.isBlank(vo.getReportCode())) {
            throw new IllegalArgumentException("请选择一个报告项目");
        }
        LoginUserHandler.User user = LoginUserHandler.get();

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        // query.setIsAudit(YesOrNoEnum.NO.getCode());
        query.setSampleStatus(new HashSet<>(Set.of(SampleStatusEnum.NOT_AUDIT.getCode())));
        query.setItemTypes(Collections.singleton(ItemTypeEnum.ROUTINE.name()));
        // 设置分拣时间
        if (!Objects.isNull(vo.getPickStartTime()) && !Objects.isNull(vo.getPickEndTime())) {
            query.setStartTwoPickDate(vo.getPickStartTime());
            query.setEndTwoPickDate(vo.getPickEndTime());
        }
        query.setGroupIds(Collections.singleton(user.getGroupId()));
        query.setReportItemCodes(Collections.singleton(vo.getReportCode()));
        List<BaseSampleEsModelDto> sampleEsModels = elasticSearchSampleService.selectSamples(query);
        // 筛选常规检验的样本
        final List<RoutineInspectionDto> samples =
                sampleEsModels.stream().filter(RoutineInspectionDto.class::isInstance).map(e -> (RoutineInspectionDto) e)
                        .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        // 查询实时结果，用于修改之后展示，ES中的数据可能不是最新的
        Map<Long, List<SampleResultDto>> sampleResultsAsMap = sampleResultService.selectBySamplesIdAsMap(samples.stream()
                .map(RoutineInspectionDto::getSampleId).collect(Collectors.toList()));

        // 所有的报告项目编码
        List<String> reportItemCodes = samples.stream().flatMap(e -> e.getReportItems().stream())
                .map(RoutineInspectionDto.RoutineReportItem::getReportItemCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reportItemCodes)) {
            return Collections.emptyList();
        }

        // 查询所有的报告项目信息
        List<ReportItemDto> reportItemDtos = reportItemService.selectByReportItemCodes(reportItemCodes, user.getOrgId());
        if (CollectionUtils.isEmpty(reportItemDtos)) {
            throw new IllegalStateException(String.format("报告项目 [%s] 信息不存在", String.join(StringPool.COMMA, reportItemCodes)));
        }
        Map<String, ReportItemDto> reportItemDtoMap = reportItemDtos
                .stream().collect(Collectors.toMap(ReportItemDto::getReportItemCode, Function.identity(), (a, b) -> a));

        List<ResultSampleByReportVo> resultSampleByReportVos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(samples)) {
            // 单独抽取报告项目结果
            for (RoutineInspectionDto inspectionDto : samples) {
                // 抽取每一个匹配的报告项目作为一项返回
                if (!CollectionUtils.isEmpty(inspectionDto.getReportItems())) {
                    // 找到满足的一条报告项目
                    RoutineInspectionDto.RoutineReportItem reportItem = inspectionDto.getReportItems().stream()
                            .filter(f -> Objects.equals(f.getReportItemCode(), vo.getReportCode())).findFirst().orElse(null);

                    if (!Objects.isNull(reportItem)) {
                        ReportItemDto reportItemDto = reportItemDtoMap.get(reportItem.getReportItemCode());

                        //找到后进行赋值
                        ResultSampleByReportVo sampleByReportVo = new ResultSampleByReportVo();
                        sampleByReportVo.setApplySampleId(inspectionDto.getApplySampleId());
                        sampleByReportVo.setSampleNo(inspectionDto.getSampleNo());
                        sampleByReportVo.setSampleId(inspectionDto.getSampleId());
                        sampleByReportVo.setTestDate(inspectionDto.getTestDate());

                        sampleByReportVo.setReportItemId(reportItemDto.getReportItemId());
                        sampleByReportVo.setReportItemName(reportItem.getReportItemName());
                        sampleByReportVo.setReportItemCode(reportItem.getReportItemCode());
                        sampleByReportVo.setTestItemId(reportItem.getTestItemId());
                        sampleByReportVo.setTestItemCode(reportItem.getTestItemCode());
                        sampleByReportVo.setTestItemName(reportItem.getTestItemName());
                        sampleByReportVo.setSampleResultId(reportItem.getSampleResultId());

                        sampleResultsAsMap.getOrDefault(inspectionDto.getSampleId(), List.of()).stream()
                                .filter(e -> e.getReportItemCode().equals(reportItem.getReportItemCode()))
                                .findFirst().ifPresent(e -> sampleByReportVo.setResult(e.getResult()));

                        resultSampleByReportVos.add(sampleByReportVo);
                    }
                }

            }
        }

        return resultSampleByReportVos;
    }

    /**
     * 常规上传报告
     */
    @PostMapping("/upload-report-routine")
    public Object uploadReportRoutine(@RequestBody UploadPdfDto uploadPdfDto){
        return sampleReportService.uploadReportRoutine(uploadPdfDto);
    }

}
