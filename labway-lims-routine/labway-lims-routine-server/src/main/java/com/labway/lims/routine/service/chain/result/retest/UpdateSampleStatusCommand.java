package com.labway.lims.routine.service.chain.result.retest;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.service.chain.result.RecalculateRefResultCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 把复查结果刷入到结果表
 */
@Slf4j
@Component
class UpdateSampleStatusCommand implements Command {
    @Resource
    private SampleRetestItemService sampleRetestItemService;
    @Resource
    private SampleResultService sampleResultService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RecalculateRefResultCommand recalculateRefResultCommand;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private SaveRetestResultCommand saveRetestResultCommand;

    @Override
    public boolean execute(Context c) throws Exception {

        final RetestResultContext context = RetestResultContext.from(c);

        // 如果没有完成复查那么不需要刷入数据
        if (!context.isComplete()) {
            return CONTINUE_PROCESSING;
        }

        // 如果是在递归中，不刷数据
        if (recalculateRefResultCommand.isRecalculate(c)) {
            return CONTINUE_PROCESSING;
        }


        final ApplySampleDto m = new ApplySampleDto();
        m.setApplySampleId(context.getSample().getApplySampleId());
        m.setStatus(SampleStatusEnum.NOT_AUDIT.getCode());
        applySampleService.updateByApplySampleId(m);


        return CONTINUE_PROCESSING;
    }
}
