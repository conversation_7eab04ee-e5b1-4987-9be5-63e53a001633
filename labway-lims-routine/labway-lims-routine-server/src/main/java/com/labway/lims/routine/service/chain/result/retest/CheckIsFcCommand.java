package com.labway.lims.routine.service.chain.result.retest;

import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import com.labway.lims.routine.service.chain.result.SaveResultContext;
import com.labway.lims.routine.service.chain.result.SelectResultAllChain;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component("routine-checkIsFcCommand")
public class CheckIsFcCommand implements Command, InitializingBean {

    @Resource
    private RetestResultChain retestResultChain;
    @Resource
    private SampleRetestItemService sampleRetestItemService;
    @Resource
    private SampleRetestMainService sampleRetestMainService;
    @Resource
    private SampleReportItemService sampleReportItemService;
    @Resource
    private SelectResultAllChain selectResultAllChain;
    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);


        // 如果报告项目不是复查中 那么可以跳过
        if (!Objects.equals(context.getSampleReportItem().getIsRetest(), RetestStatusEnum.RETESTING.getCode())) {
            return CONTINUE_PROCESSING;
        }

        final List<SampleRetestMainDto> sampleRetestMains = sampleRetestMainService.selectBySampleId(context.getSample().getSampleId());
        // 获取到正在复查中的
        final SampleRetestMainDto sampleRetestMain = sampleRetestMains.stream().filter(e -> Objects.equals(e.getStatus(),
                SampleRetestStatusEnum.NORMAL.getCode())).findFirst().orElse(null);
        if (Objects.isNull(sampleRetestMain)) {
            return CONTINUE_PROCESSING;
        }

        context.put(RetestResultContext.RETEST_MAIN, sampleRetestMain);
        context.put(RetestResultContext.IS_RETESTING, true);

        final RetestResultContext ctx = new RetestResultContext();
        BeanUtils.copyProperties(c, ctx);
        ctx.setSaveResultContext(context);


        // 复查逻辑
        if (!retestResultChain.execute(ctx)) {
            throw new IllegalStateException("复查失败");
        }

        // 版本1 :如果已经复查完毕 那么复查中就是 false
        // 版本2 :如果已经复查完毕 那么复查中也是为 True,复查完毕还要显示已经复查的
        if (ctx.isComplete()) {
            context.put(RetestResultContext.IS_RETESTING, Boolean.TRUE);
        }

        // 将复查上下文的核心信息传到保存结果的上下文
        context.setResult(ctx.getResult());
        context.put(SaveResultContext.RESULT_IS_EXCEPTION, ctx.isException());
        context.put(SaveResultContext.RESULT_IS_CRITICAL, ctx.isCritical());
        context.put(SaveResultContext.RESULT_JUDGE, ctx.getResultJudge());
        context.put(SaveResultContext.INSTRUMENT_REPORT_REFERENCE, ctx.getInstrumentReportItemReference());

        //返回 结果信息
        selectResultAllChain.execute(c);
        return PROCESSING_COMPLETE;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }
}
