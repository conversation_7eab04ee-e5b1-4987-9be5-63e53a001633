package com.labway.lims.routine.vo;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/6 09:37
 */
@Getter
@Setter
public class HistoryResultVo {

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 检验日期
     */
    private String testDate;

    /**
     *
     */

    private List<ResultVo> results;

    @Getter
    @Setter
    public static class ResultVo {

        /**
         * 报告项目名称
         */
        private String reportItemName;

        /**
         * 报告项目编码
         */
        private String reportItemCode;

        /**
         * 报告项目id
         */
        private Long reportItemId;

        /**
         * 结果
         */
        private String result;

        /**
         * 1: 危机
         * 2: 异常
         * 0: 正常
         *
         * @see ResultStatusEnum
         */
        private Integer status;

        /**
         * 检验判断
         *
         * @see TestJudgeEnum
         */
        private String judge;

        /**
         * 打印顺序
         */
        private Integer printSort;

    }
}
