package com.labway.lims.routine.service.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

/**
 * 危机值 信息 导出 excel 合并 数据 策略
 * 
 * <AUTHOR>
 * @since 2023/4/10 19:23
 */
@Slf4j
public class ExportCriticalDataMergeStrategy extends AbstractMergeStrategy {

    // 合并坐标集合
    private final List<CellRangeAddress> cellRangeAddresses;

    public ExportCriticalDataMergeStrategy(List<CellRangeAddress> cellRangeAddresses) {
        this.cellRangeAddresses = cellRangeAddresses;
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer integer) {
        if (cell.getRowIndex() == 1 && cell.getColumnIndex() == 0) {
            for (CellRangeAddress item : cellRangeAddresses) {
                sheet.addMergedRegion(item);
            }
        }
    }
}
