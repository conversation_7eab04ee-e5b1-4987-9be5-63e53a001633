package com.labway.lims.routine.service.chain.auto.audit;

import cn.hutool.http.HttpUtil;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自动审核前置校验
 *
 * <AUTHOR> on 2025/7/29.
 */
@Slf4j
@Component
public class AutoAuditBeforeCheckCommand implements Command {

	@Override
	public boolean execute(Context c) throws Exception {

		AutoAuditContext context = AutoAuditContext.from(c);

		checkCanSendAutoAudit(context);

		return CONTINUE_PROCESSING;
	}

	/**
	 * 校验是否可以发送给jvs进行自动审核判定
	 */
	private void checkCanSendAutoAudit(AutoAuditContext context) {
		List<TestItemDto> sampleTestItems = context.getSampleTestItems();

		List<TestItemDto> notAutoAuditTestItems = sampleTestItems.stream()
				.filter(item -> Objects.equals(YesOrNoEnum.NO.getCode(), item.getAutoAudit()))
				.collect(Collectors.toList());

		List<TestItemDto> decisionBasisError = sampleTestItems.stream()
				.filter(item -> !(HttpUtil.isHttp(item.getDecisionBasis()) || HttpUtil.isHttps(item.getDecisionBasis())))
				.collect(Collectors.toList());

		StringBuilder errInfo = new StringBuilder();

		if (CollectionUtils.isNotEmpty(notAutoAuditTestItems)) {
			errInfo.append(String.format("检验项目[%s] 未开启自动审核\n", notAutoAuditTestItems.stream().map(TestItemDto::getTestItemName).collect(Collectors.joining(","))));
		}

		if (CollectionUtils.isNotEmpty(decisionBasisError)) {
			errInfo.append(String.format("检验项目[%s]决策地址格式错误 \n", decisionBasisError.stream().map(TestItemDto::getTestItemName).collect(Collectors.joining(","))));
		}

		if (context.getSampleResults().stream().map(SampleResultDto::getResult).anyMatch(StringUtils::isBlank)) {
			errInfo.append("样本结果值存在空值，不能进行自动审核 \n");
		}

		if (StringUtils.isNotBlank(errInfo.toString())) {
			throw new IllegalStateException(errInfo.toString());
		}

	}

}
