package com.labway.lims.routine.service.chain.result.retest;


import com.labway.lims.routine.service.chain.result.CheckCanSaveCommand;
import com.labway.lims.routine.service.chain.result.ConvertResultCommand;
import com.labway.lims.routine.service.chain.result.InstrumentReportReferenceCommand;
import com.labway.lims.routine.service.chain.result.NumberResultCommand;
import com.labway.lims.routine.service.chain.result.RecalculateRefResultCommand;
import com.labway.lims.routine.service.chain.result.ResultFormatCommand;
import com.labway.lims.routine.service.chain.result.YinYangResultCommand;
import com.labway.lims.routine.service.chain.result.retest.provider.RetestChainProvider;
import com.labway.lims.routine.service.chain.result.retest.provider.RetestResultProvider;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class RetestResultChain extends ChainBase implements InitializingBean {

    @Resource
    private CheckCanSaveCommand checkCanSaveCommand;
    @Resource
    private ConvertResultCommand convertResultCommand;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;
    @Resource
    private NumberResultCommand numberResultCommand;
    @Resource
    private ResultFormatCommand resultFormatCommand;
    @Resource
    private YinYangResultCommand yinYangResultCommand;
    @Resource
    private RecalculateRefResultCommand recalculateRefResultCommand;
    @Resource
    private RetestResultProvider retestResultProvider;
    @Resource
    private RetestChainProvider retestChainProvider;
    @Resource
    private SaveRetestResultCommand saveRetestResultCommand;
    @Resource
    private SaveRetestFlowCommand saveRetestFlowCommand;
    @Resource
    private CheckIsCompleteRetestCommand checkIsCompleteRetestCommand;
    @Resource
    private UpdateCriticalResutltStatusCommand updateCriticalResutltStatusCommand;
    @Resource
    private RetestUpdateReportItemRetestStatusCommand retestUpdateReportItemRetestStatusCommand;
    @Resource
    private WriteSampleResultCommand writeSampleResultCommand;
    @Resource
    private UpdateSampleStatusCommand updateSampleStatusCommand;
    @Resource
    private RetestResultRedisMarkCommand retestResultRedisMarkCommand;
    @Override
    public void afterPropertiesSet() {

        // 结果转换
        addCommand(convertResultCommand);

        // 判断是否可以保存
        addCommand(checkCanSaveCommand);

        // 获取参考范围
        addCommand(instrumentReportReferenceCommand);

        // 查询结果信息
        addCommand(retestResultProvider);

        // 数值类型结果 参考范围等校验  & 计算结果
        addCommand(numberResultCommand);

        // 阴阳类型结果 判断是否异常
        addCommand(yinYangResultCommand);

        // 格式化结果
        addCommand(resultFormatCommand);

        // 自己调用自己的链
        addCommand(retestChainProvider);

        // 查看依赖项
        addCommand(recalculateRefResultCommand.getDefaultChainProvider());

        // 查看依赖项
        addCommand(recalculateRefResultCommand);

        // 保存复查结果信息
        addCommand(saveRetestResultCommand);

        //检查是否玩完成复查
        addCommand(checkIsCompleteRetestCommand);

        // 修改报告项目的复查状态
        addCommand(retestUpdateReportItemRetestStatusCommand);

        // 如果完成复查了，那么把数据刷入到结果表
        addCommand(writeSampleResultCommand);

        // 修改样本的复查状态
        addCommand(updateSampleStatusCommand);

        //修改危机值列表的状态
        addCommand(updateCriticalResutltStatusCommand);

        // redis 复查缺项标记
        addCommand(retestResultRedisMarkCommand);

        //  记录复查流水
        addCommand(saveRetestFlowCommand);

        // 结束
        addCommand(c -> PROCESSING_COMPLETE);
    }
}
