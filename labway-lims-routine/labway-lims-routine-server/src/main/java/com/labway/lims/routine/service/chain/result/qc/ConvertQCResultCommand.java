package com.labway.lims.routine.service.chain.result.qc;

import com.alibaba.fastjson.JSON;
import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 结果转换
 *
 * <AUTHOR>
 * @since 2023/3/30 16:16
 */
@Slf4j
@Component
public class ConvertQCResultCommand implements Command {
    @Override
    public boolean execute(Context c) throws Exception {
        final SaveQCResultContext context = SaveQCResultContext.from(c);

        // 如果是质控样本，则不需要转换
        if (context.getIsQc()) {
            return CONTINUE_PROCESSING;
        }

        if (Objects.nonNull(context.getResultExchange())) {
            final Map<String, String> map = JSON.parseArray(JSON.toJSONString(context.getResultExchange()), InstrumentReportItemResultExchangeDto.class).stream()
                    .collect(Collectors.toMap(InstrumentReportItemResultExchangeDto::getInstrumentResult, InstrumentReportItemResultExchangeDto::getExchangeResult,
                            (a, b) -> a));
            // 如果为空那么不转换
            context.setResult(ObjectUtils.defaultIfNull(map.get(context.getResult()), context.getResult()));
        }
        return CONTINUE_PROCESSING;
    }
}
