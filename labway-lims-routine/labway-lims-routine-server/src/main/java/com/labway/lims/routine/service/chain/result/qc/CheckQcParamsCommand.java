package com.labway.lims.routine.service.chain.result.qc;

import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/7/4 09:27
 */
@Component
public class CheckQcParamsCommand implements Command {
    @Override
    public boolean execute(Context c) throws Exception {

        final SaveQCResultContext context = SaveQCResultContext.from(c);
        if (StringUtils.isBlank(context.getSampleNo())){
            throw new IllegalStateException("样本号为空");
        }
        return CONTINUE_PROCESSING;
    }
}
