package com.labway.lims.routine.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.MissReportItemNumTips;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleAbnormalStatusEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.base.api.dto.InstrumentReportItemCommonPhraseDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.InstrumentReportItemCommonPhraseService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.routine.api.dto.BarCodeDto;
import com.labway.lims.routine.api.dto.CombinedBillDTO;
import com.labway.lims.routine.api.dto.CommitCombinedBillDTO;
import com.labway.lims.routine.api.dto.QuerySampleDto;
import com.labway.lims.routine.api.dto.ResultStatusDto;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleCancelAuditDto;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleReportItemPatDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.dto.SelectCombinedBillDTO;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import com.labway.lims.routine.config.CombinedBillConfig;
import com.labway.lims.routine.enums.YinAndYang;
import com.labway.lims.routine.service.SampleServiceImpl;
import com.labway.lims.routine.service.chain.result.UpdateMissItemCommand;
import com.labway.lims.routine.service.chain.retest.StartRetestUpdateMissItemCommand;
import com.labway.lims.routine.vo.AbnormalSampleVo;
import com.labway.lims.routine.vo.CheckInfoVo;
import com.labway.lims.routine.vo.CriticalSampleResultsVo;
import com.labway.lims.routine.vo.MissReportItemVo;
import com.labway.lims.routine.vo.PhraseVo;
import com.labway.lims.routine.vo.QueryRoutineSamplesVo;
import com.labway.lims.routine.vo.QuerySampleVo;
import com.labway.lims.routine.vo.RoutineSampleVo;
import com.labway.lims.routine.vo.RoutineSamplesVo;
import com.labway.lims.routine.vo.SampleAuditVo;
import com.labway.lims.routine.vo.SampleMissReportItemVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@RestController
@RequestMapping("/routine")
public class RoutineController extends BaseController {
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private SampleServiceImpl sampleService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private CombinedBillConfig combinedBillConfig;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private UpdateMissItemCommand updateMissItemCommand;
    @Resource
    private StartRetestUpdateMissItemCommand startRetestUpdateMissItemCommand;

    @DubboReference
    private UserService userService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SystemParamService systemParamService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleAbnormalService sampleAbnormalService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SampleRetestMainService sampleRetestMainService;
    @DubboReference
    private InstrumentReportItemCommonPhraseService instrumentReportItemCommonPhraseService;

    @Value("${routine.samples:v2}")
    private String routineSamples;

	/**
	 * 自动审核开关标识
	 * 实验室id + 专业组id
	 */
	public static final String GROUP_AUTO_AUDIT = "routine:groupAutoAudit:%s-%s";

	/**
	 * 自动审核人
	 */
	public static final String GROUP_AUTO_AUDIT_USER = "routine:groupAutoAuditUser:%s-%s";

    /**
     * -----------样本结果查询------------
     */
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    @PostMapping("/samples")
    public Object samples(@RequestBody QuerySampleVo vo) throws ExecutionException, InterruptedException {

        final QuerySampleDto dto = JSON.parseObject(JSON.toJSONString(vo), QuerySampleDto.class);
        dto.setGroupId(LoginUserHandler.get().getGroupId());
        dto.setSampleStatus(vo.getSampleStatus());

        List<SampleDto> samples;
        if ("v2".equals(routineSamples)) {
            samples = sampleService.selectSamples(dto);
        } else {
            samples = sampleService.selectByTestDate(dto);
        }

        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }

        // 根据applySampleIds查询 申请单样本 转化成map一一对应样本
        final Future<Map<Long, ApplySampleDto>> applySampleMap = threadPoolConfig.getPool()
                .submit(() -> applySampleService
                        .selectByApplySampleIds(samples.stream().map(SampleDto::getApplySampleId).collect(Collectors.toList()))
                        .stream()
                        .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity(), (k1, k2) -> k1)));

        // 根据applyIds查询 申请单 转化成map一一对应样本
        final Future<Map<Long, ApplyDto>> applyMap = threadPoolConfig.getPool().submit(() -> applyService
                .selectByApplyIdsAsMap(samples.stream().map(SampleDto::getApplyId).collect(Collectors.toList())));
        // 根据applySampleIds查询 检验项目 转化成map对应样本
        final Future<Map<Long, List<ApplySampleItemDto>>> applySampleItemMap = threadPoolConfig.getPool()
                .submit(() -> applySampleItemService
                        .selectAllByApplySampleIds(samples.stream().map(SampleDto::getApplySampleId).collect(Collectors.toList()))
                        .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId)));

        List<SampleDto> filterSamples = samples;

        // 是否仅显示检验样本
        if (Objects.equals(vo.getIsTestSample(), YesOrNoEnum.YES.getCode())) {
            filterSamples = samples.stream().filter(f -> !Objects.equals(f.getApplyId(), NumberUtils.LONG_ZERO)
                    && !Objects.equals(f.getApplySampleId(), NumberUtils.LONG_ZERO)).collect(Collectors.toList());
        }

        final LinkedList<RoutineSampleVo> vos = new LinkedList<>();
        for (SampleDto sample : filterSamples) {

            final RoutineSampleVo sampleVo = JSON.parseObject(JSON.toJSONString(sample), RoutineSampleVo.class);
            final ApplyDto apply = applyMap.get().get(sample.getApplyId());

            if (Objects.nonNull(apply)) {
                // 过滤 申请单紧急程度
                if (Objects.nonNull(vo.getUrgent()) && !Objects.equals(vo.getUrgent(), apply.getUrgent())) {
                    continue;
                }
                sampleVo.setApplyType(apply.getApplyTypeName());
                sampleVo.setPatientName(apply.getPatientName());
                sampleVo.setEnterDate(apply.getCreateDate());
                sampleVo.setOriginalOrgCode(apply.getOriginalOrgCode());
                sampleVo.setOriginalOrgName(apply.getOriginalOrgName());

            }
            final ApplySampleDto applySample =
                    applySampleMap.get().getOrDefault(sample.getApplySampleId(), new ApplySampleDto());
            applySample
                    .setStatus(ObjectUtils.defaultIfNull(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode()));

            // 终止检验的过滤
            // if (Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
            //     continue;
            // }
            // 查询未审 ,状态不是未审或者复查的过滤
            if (Objects.equals(vo.getSampleStatus(), SampleStatusEnum.NOT_AUDIT.name())
                    && (!Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode()))
                    && (!Objects.equals(applySample.getStatus(), SampleStatusEnum.RETEST.getCode()))) {
                continue;
            }
            // 查询一审，不等于一审的过滤
            if (Objects.equals(vo.getSampleStatus(), SampleStatusEnum.ONE_AUDIT.name())
                    && (!Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
                continue;
            }
            // 查询已审，不等于已审的过滤
            if (Objects.equals(vo.getSampleStatus(), SampleStatusEnum.AUDIT.name())
                    && (!Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode()))) {
                continue;
            }
            // 并单的话会查询终审 查询终审，不等于终审的过滤
            if (Objects.equals(vo.getSampleStatus(), SampleStatusEnum.STOP_TEST.name())) {
                if (Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
                    if (StringUtils.isBlank(applySample.getMergeMasterBarcode())) {
                        continue;
                    }
                } else {
                    continue;
                }
            } else if (Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
                continue;
            }

            sampleVo.setStatus(applySample.getStatus());
            sampleVo.setColorMarking(applySample.getColorMarking());
            sampleVo.setIsPrint(applySample.getIsPrint());
            // 免疫二次分拣标记 # 1.1.3.7
            sampleVo.setIsImmunityTwoPick(applySample.getIsImmunityTwoPick());

            // 优先显示危
            if (Objects.equals(applySample.getStatus(), SampleStatusEnum.RETEST.getCode())) {
                sampleVo.setIsRetest(YesOrNoEnum.YES.getCode());
            } else {
                sampleVo.setIsRetest(YesOrNoEnum.NO.getCode());
            }

            sampleVo.setUrgent(applySample.getUrgent());

            final List<ApplySampleItemDto> applySampleItems = applySampleItemMap.get().get(sample.getApplySampleId());
            if (Objects.nonNull(applySampleItems)) {
                final List<String> testItems =
                        applySampleItems.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList());
                sampleVo.setTestItemName(StringUtils.join(testItems, ","));
            }

            sampleVo.setOneCheckerId(sample.getOneCheckerId());
            sampleVo.setOneCheckerName(sample.getOneCheckerName());

            sampleVo.setTwoCheckerId(sample.getTwoCheckerId());
            sampleVo.setTwoCheckerName(sample.getTwoCheckerName());

            sampleVo.setMergeMasterBarcode(applySample.getMergeMasterBarcode());
            sampleVo.setMergeExtraInfo(applySample.getMergeExtraInfo());

            vos.add(sampleVo);
        }

        // 是否仅显示 结果值出全的样本
        if (BooleanUtils.isTrue(vo.getShowSampleWithAllResult())) {
            List<MissReportItemVo> missReportItemVos = vos.stream().map(e -> {
                MissReportItemVo itemVo = new MissReportItemVo();
                itemVo.setSampleId(e.getSampleId());
                itemVo.setApplySampleId(e.getApplySampleId());
                return itemVo;
            }).collect(Collectors.toList());
            List<Long> sampleIdsWithAllResult = getMissReportItem(missReportItemVos).stream()
                    .filter(e -> MissReportItemNumTips.ALL.getTips().equals(e.getMissReportItemNum()))
                    .map(SampleMissReportItemVo::getSampleId).collect(Collectors.toList());

            return vos.stream().filter(e -> sampleIdsWithAllResult.contains(e.getSampleId())).collect(Collectors.toList());
        }

        return vos;
    }

    @GetMapping("/roche-image")
    public Object getRocheImage(long applySampleId) {
        final List<SampleFlowDto> flows = sampleFlowService.selectByApplySampleIdAndOperateCode(Collections.singleton(applySampleId),
                BarcodeFlowEnum.IT8000SEEN_IMAGE);
        for (SampleFlowDto flow : flows) {
            if (Objects.equals(flow.getOperateCode(), BarcodeFlowEnum.IT8000SEEN_IMAGE.name())) {
                return Map.of("url", flow.getContent());
            }
        }
        return Map.of();
    }

    /**
     * 查询样本的缺项情况
     */
    @PostMapping("/get-miss-report-item")
    public List<SampleMissReportItemVo> getMissReportItem(@RequestBody List<MissReportItemVo> items)
            throws InterruptedException, ExecutionException {

        if (CollectionUtils.isEmpty(items)) {
            return List.of();
        }

        List<Long> sampleIds = items.stream().map(MissReportItemVo::getSampleId).collect(Collectors.toList());

        final Map<Long, List<SampleRetestMainDto>> sampleRetestMainMap = sampleRetestMainService.selectBySampleIds(sampleIds)
                .stream().collect(Collectors.groupingBy(SampleRetestMainDto::getApplySampleId));


        // 多线程跑
        final List<Callable<List<SampleMissReportItemVo>>> callables =
                ListUtils.partition(items, 20).stream().map(s -> (Callable<List<SampleMissReportItemVo>>) () -> {
                    final Map<Long, ApplySampleDto> applySamples = applySampleService.selectByApplySampleIdsAsMap(
                            s.stream().map(MissReportItemVo::getApplySampleId).collect(Collectors.toSet()));
                    final List<SampleMissReportItemVo> list = new LinkedList<>();
                    for (MissReportItemVo e : s) {

                        final SampleMissReportItemVo vo = new SampleMissReportItemVo();
                        list.add(vo);
                        vo.setSampleId(e.getSampleId());
                        vo.setIsException(YesOrNoEnum.NO.getCode());
                        vo.setIsCritical(YesOrNoEnum.NO.getCode());
                        vo.setIsRetest(YesOrNoEnum.NO.getCode());

                        final ApplySampleDto applySample = applySamples.get(e.getApplySampleId());
                        if (Objects.isNull(applySample)) {
                            continue;
                        }
                        vo.setStatus(applySample.getStatus());
                        // 设置状态颜色
                        vo.setColorMarking(applySample.getColorMarking());

                        final HashOperations<String, String, String> operations = stringRedisTemplate.opsForHash();
                        final Map<String, String> entries =
                                operations.entries(updateMissItemCommand.getMissItemKey(e.getSampleId()));
                        final Map<String, String> retestEntries;

                        // 如果是复查的 那么查询复查的缺项
                        if (Objects.equals(SampleStatusEnum.RETEST.getCode(), applySample.getStatus())) {
                            retestEntries =
                                    operations.entries(startRetestUpdateMissItemCommand.getMissItemKey(e.getSampleId()));
                        } else {
                            retestEntries = Map.of();
                        }

                        if (MapUtils.isEmpty(entries)) {
                            continue;
                        }

                        final List<ResultStatusDto> rs = entries.values().stream()
                                .map(k -> JSON.parseObject(k, ResultStatusDto.class)).collect(Collectors.toList());
                        final List<ResultStatusDto> retestRs = retestEntries.values().stream()
                                .map(k -> JSON.parseObject(k, ResultStatusDto.class)).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(rs)) {
                            continue;
                        }

                        // 没有结果的
                        final long missCount = rs.stream().filter(k -> StringUtils.isBlank(k.getResult())).count()
                                + retestRs.stream().filter(k -> StringUtils.isBlank(k.getResult())).count();

                        // 缺项的数量(全、-1、-2、-3、-4、-5、缺)
                        vo.setMissReportItemNum(MissReportItemNumTips.getTips(missCount));

                        //                        final ApplySampleDto as = applySamples.get(e.getApplySampleId());
                        // 如果样本状态是 11 待复测 状态返回复测
                        //                        if (Objects.nonNull(as) && (Objects.equals(as.getStatus(), SampleStatusEnum.RETEST.getCode()))) {
                        //                            vo.setIsRetest(YesOrNoEnum.YES.getCode());
                        //                        }
                        if (CollectionUtils.isNotEmpty(sampleRetestMainMap.get(e.getApplySampleId()))) {
                            vo.setIsRetest(YesOrNoEnum.YES.getCode());
                        }

                        for (ResultStatusDto r : rs) {
                            if (Objects.equals(r.getIsException(), YesOrNoEnum.YES.getCode())) {
                                vo.setIsException(YesOrNoEnum.YES.getCode());
                            }
                            if (Objects.equals(r.getIsCritical(), YesOrNoEnum.YES.getCode())) {
                                vo.setIsCritical(YesOrNoEnum.YES.getCode());
                            }
                            if (Objects.equals(r.getIsRetest(), YesOrNoEnum.YES.getCode())) {
                                vo.setIsRetest(YesOrNoEnum.YES.getCode());
                            }
                            if (Objects.equals(r.getIsException(), YesOrNoEnum.YES.getCode())) {
                                vo.setIsException(YesOrNoEnum.YES.getCode());
                            }
                        }

                    }
                    return list;
                }).collect(Collectors.toList());

        final List<List<SampleMissReportItemVo>> results = new ArrayList<>();

        for (Future<List<SampleMissReportItemVo>> future : threadPoolConfig.getPool().invokeAll(callables, 30,
                TimeUnit.SECONDS)) {
            results.add(future.get());
        }

        return results.stream().flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 返回审核人信息
     */
    @PostMapping("/get-check-info")
    public Object getCheckInfo(@RequestParam("sampleId") Long sampleId) {
        final SampleDto sampleDto = sampleService.selectBySampleId(sampleId);
        if (Objects.isNull(sampleDto)) {
            return Map.of();
        }

        final UserDto one = userService.selectByUserId(sampleDto.getOneCheckerId());
        final UserDto two = userService.selectByUserId(sampleDto.getTwoCheckerId());
        final CheckInfoVo vo = new CheckInfoVo();

        if (Objects.nonNull(one)) {
            vo.setOneCheckerId(one.getUserId());
            vo.setOneCheckerNick(one.getNickname());
            vo.setOneCheckerName(one.getUsername());
        }

        if (Objects.nonNull(two)) {
            vo.setTwoCheckerId(two.getUserId());
            vo.setTwoCheckerNick(two.getNickname());
            vo.setTwoCheckerName(two.getUsername());
        }

        return vo;
    }

    @PostMapping("/audit-sample")
    public Object auditSamples(@RequestBody SampleAuditVo vo) {

        if (Objects.isNull(vo.getSampleId())) {
            throw new IllegalArgumentException("请选择样本");
        }

        final SampleAuditDto dto = JSON.parseObject(JSON.toJSONString(vo), SampleAuditDto.class);
        sampleService.auditSamplesChain(dto);

        return Collections.emptyMap();
    }

    @PostMapping("/cancel-audit-sample")
    public Object cancelAuditSample(@RequestBody SampleCancelAuditDto vo) {
        if (CollectionUtils.isEmpty(vo.getSampleIds())) {
            throw new IllegalStateException("请选择样本");
        }

        if (Objects.equals(vo.getAuditStatus(), SampleAuditStatusEnum.CANCEL_ONE_CHECK.name())) {
            sampleService.cancelOneAuditSample(vo);
        } else if (Objects.equals(vo.getAuditStatus(), SampleAuditStatusEnum.CANCEL_TWO_CHECK.name())) {
            sampleService.cancelTwoAuditSample(vo);
        }

        return Collections.emptyMap();
    }

    /**
     * 查询当前专业组下的所有危急值
     */
    @PostMapping("/critical-tips")
    public Object criticalTips() {
        final LoginUserHandler.User user = LoginUserHandler.get();

        // 过滤以及复测的
        final List<SampleCriticalResultDto> criticalResults =
                sampleCriticalResultService.selectByGroupId(user.getGroupId()).stream()
                        .filter(e -> !Objects.equals(e.getStatus(), RetestStatusEnum.RETEST.getCode()))
                        .collect(Collectors.toList());

        // 删除掉已经禁用或终止的 filterTerminateApplySampleIds
        List<Long> strings = applySampleService.filterTerminateApplySampleIds(criticalResults.stream().map(SampleCriticalResultDto::getApplySampleId).collect(Collectors.toCollection(LinkedList::new)));
        criticalResults.removeIf(e -> strings.contains(e.getApplySampleId()));

        final SampleCriticalResultDto critical = criticalResults.stream().findFirst().orElse(null);
        if (Objects.isNull(critical)) {
            return Map.of();
        }

        final BaseSampleEsModelDto sample =
                elasticSearchSampleService.selectByApplySampleId(critical.getApplySampleId());
        if (Objects.isNull(sample)) {
            return Map.of();
        }
        final CriticalSampleResultsVo vo = new CriticalSampleResultsVo();
        vo.setSize(criticalResults.size());
        vo.setCriticalValueId(critical.getCriticalValueId());
        vo.setSampleId(critical.getSampleId());
        vo.setBarcode(critical.getBarcode());

        vo.setSampleNo(sample.getSampleNo());
        vo.setReportItemId(critical.getReportItemId());
        vo.setReportItemName(critical.getReportItemName());

        return vo;
    }

    /**
     * 查询当前专业组下的所有异常值
     */
    @PostMapping("/exception-tips")
    public Object selectAllException() {
        final LoginUserHandler.User user = LoginUserHandler.get();
        String key = redisPrefix.getBasePrefix() + "ignoreException:" + user.getUserId() + user.getGroupId();
        final Set<String> members =
                CollUtil.defaultIfEmpty(stringRedisTemplate.opsForSet().members(key), new HashSet<>());

        // 过滤已经忽略的
        final List<SampleAbnormalDto> abnormalDtos = sampleAbnormalService.selectByGroupId(user.getGroupId()).stream()
                // 只查看 未处理 和待确认的
                .filter(obj -> Objects.equals(obj.getStatus(), SampleAbnormalStatusEnum.UNPROCESSED.getCode())
                        || Objects.equals(obj.getStatus(), SampleAbnormalStatusEnum.PROCESSED.getCode()))
                // 待确认但无确认部门或确认部门不为当前用户所属专业组的不需要
                .filter(obj -> !(Objects.equals(obj.getStatus(), SampleAbnormalStatusEnum.PROCESSED.getCode())
                        && (Objects.equals(obj.getConfirmGroupId(), NumberUtils.LONG_ZERO)
                        || !Objects.equals(obj.getConfirmGroupId(), user.getGroupId()))))
                .filter(e -> !CollectionUtils.containsAny(members, String.valueOf(e.getSampleAbnormalId())))
                // 创建时间降序
                .sorted(Comparator.comparing(SampleAbnormalDto::getCreateDate, Comparator.reverseOrder()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(abnormalDtos)) {
            return Collections.emptyMap();
        }

        SampleAbnormalDto abnormal = abnormalDtos.get(NumberUtils.INTEGER_ZERO);
        AbnormalSampleVo target = new AbnormalSampleVo();
        target.setSampleAbnormalId(abnormal.getSampleAbnormalId());
        target.setAbnormalReasonCode(abnormal.getAbnormalReasonCode());
        target.setAbnormalReasonName(abnormal.getAbnormalReasonName());
        target.setRegistContent(abnormal.getRegistContent());
        target.setStatus(abnormal.getStatus());
        target.setCount(abnormalDtos.size());
        target.setSampleAbnormalIds(
                abnormalDtos.stream().map(SampleAbnormalDto::getSampleAbnormalId).collect(Collectors.toSet()));
        return target;
    }

    /**
     * 忽略异常解接口
     */
    @PostMapping("/ignore-critical")
    public Object ignoreCritical(@RequestParam("criticalValueId") Long criticalValueId) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        String key = redisPrefix.getBasePrefix() + "ignoreCritical:" + user.getUserId() + user.getGroupId();
        stringRedisTemplate.opsForSet().add(key, String.valueOf(criticalValueId));
        return Collections.emptyMap();
    }

    /**
     * 忽略异常解接口
     */
    @PostMapping("/ignore-exception")
    public Object ignoreException(@RequestBody Set<String> sampleAbnormalIds) {
        if (CollectionUtils.isEmpty(sampleAbnormalIds)) {
            throw new IllegalArgumentException("需要忽略的异常为空");
        }
        final LoginUserHandler.User user = LoginUserHandler.get();
        String key = redisPrefix.getBasePrefix() + "ignoreException:" + user.getUserId() + user.getGroupId();
        stringRedisTemplate.opsForSet().add(key, sampleAbnormalIds.toArray(new String[ 0 ]));
        return Collections.emptyMap();
    }

    /**
     * 常用短语
     */
    @PostMapping("/phrases")
    public Object phrases(Long sampleId) {

        if (Objects.isNull(sampleId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final SampleDto sample = sampleService.selectBySampleId(sampleId);
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        final List<InstrumentReportItemCommonPhraseDto> phrases =
                instrumentReportItemCommonPhraseService.selectByInstrumentGroupId(sample.getInstrumentGroupId());
        // dev-1.1.3.2 删除仪器不匹配的
        //        phrases.removeIf(e -> !Objects.equals(e.getInstrumentId(), sample.getInstrumentId()));
        if (CollectionUtils.isEmpty(phrases)) {
            return Map.of();
        }

        // 排序
        phrases.sort(Comparator.comparing(o -> ObjectUtils.defaultIfNull(o.getSort(), Integer.MAX_VALUE)));

        return phrases.stream().map(e -> {
            final PhraseVo v = new PhraseVo();
            BeanUtils.copyProperties(e, v);
            return v;
        }).collect(Collectors.groupingBy(PhraseVo::getReportItemCode));
    }

    @PostMapping("/samples-es")
    public Object getSamples(@RequestBody QueryRoutineSamplesVo queryVo) {

        // 转为es 查询条件
        SampleEsQuery query = getSampleEsQuery(queryVo);

        // 常规样本
        Stream<BaseSampleEsModelDto> stream = elasticSearchSampleService.selectSamples(query).stream();
        if (StringUtils.isNotBlank(query.getSampleRemark())) {
            stream = stream.filter(e -> e.getSampleRemark() == null || e.getSampleRemark().contains(query.getSampleRemark()));
        }
        if (StringUtils.isNotBlank(query.getResultRemark())) {
            stream = stream.filter(e -> e.getResultRemark() == null || e.getResultRemark().contains(query.getResultRemark()));
        }


        final List<RoutineInspectionDto> sampleEsModels = stream

                .filter(RoutineInspectionDto.class::isInstance)
                // 转成常规样本
                .map(e -> (RoutineInspectionDto) e).collect(Collectors.toList());

        return selectRoutineSamplesVo(sampleEsModels);

    }

	/**
	 * 获取当前专业组的自动审核开关状态
	 * @return boolean true-开启 false-关闭
	 */
	@GetMapping("/auto-status")
	public Boolean searchAutoAuditStatus() {
		LoginUserHandler.User user = LoginUserHandler.get();
		String key = String.format(redisPrefix.getBasePrefix() + GROUP_AUTO_AUDIT, user.getOrgId(), user.getGroupId());
		return BooleanUtils.toBoolean(stringRedisTemplate.opsForValue().get(key));
	}

	/**
	 * 开启/关闭自动审核
	 */
	@GetMapping("/operation-auto-audit")
	public Object operationAutoAudit() {

		LoginUserHandler.User user = LoginUserHandler.get();
		Boolean autoAuditStatus = searchAutoAuditStatus();
		stringRedisTemplate.opsForValue().set(String.format(redisPrefix.getBasePrefix() + GROUP_AUTO_AUDIT, user.getOrgId(), user.getGroupId()),
				String.valueOf(!autoAuditStatus));

		// 为true的时候 说明要关闭 ，删除审核人
		if (autoAuditStatus) {
			stringRedisTemplate.delete(redisPrefix.getBasePrefix() + String.format(GROUP_AUTO_AUDIT_USER, user.getOrgId(), user.getGroupId()));
		} else {
			stringRedisTemplate.opsForValue().set(redisPrefix.getBasePrefix() + String.format(GROUP_AUTO_AUDIT_USER, user.getOrgId(), user.getGroupId()), JSONUtil.toJsonStr(user));
		}

		log.info("{} {}自动审核", user.getNickname(), autoAuditStatus ? "关闭" : "开启");
        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance()
                        .setModule(TraceLogModuleEnum.AUTO_AUDIT.getDesc())
                        .setContent(String.format("【%s】 【%s】自动审核", user.getNickname(), autoAuditStatus ? "关闭" : "开启"))
                        .setNickname(user.getNickname())
                        .toJSONString());

		return Boolean.TRUE;
	}

    /**
     * 查询 检验结果列表 信息
     */
    private List<RoutineSamplesVo> selectRoutineSamplesVo(List<RoutineInspectionDto> sampleEsModels) {
        final List<RoutineSamplesVo> targetList = Lists.newArrayListWithCapacity(sampleEsModels.size());

        for (RoutineInspectionDto model : sampleEsModels) {
            final RoutineSamplesVo sampleVo = new RoutineSamplesVo();
            sampleVo.setSampleId(model.getSampleId());
            sampleVo.setSampleNo(model.getSampleNo());
            sampleVo.setApplySampleId(model.getApplySampleId());
            sampleVo.setApplyId(model.getApplyId());
            sampleVo.setStatus(model.getSampleStatus());
            sampleVo.setBarcode(model.getBarcode());
            sampleVo.setSampleNo(model.getSampleNo());
            sampleVo.setGroupId(model.getGroupId());
            sampleVo.setGroupName(model.getGroupName());
            sampleVo.setInstrumentGroupId(model.getInstrumentGroupId());
            sampleVo.setInstrumentGroupName(model.getInstrumentGroupName());
            sampleVo.setTestDate(model.getTestDate());
            sampleVo.setOneCheckerName(model.getOneCheckerName());
            sampleVo.setOneCheckerId(model.getOneCheckerId());
            sampleVo.setOneCheckDate(model.getOneCheckDate());
            sampleVo.setTwoCheckerName(model.getFinalCheckerName());
            sampleVo.setTwoCheckerId(model.getFinalCheckerId());
            sampleVo.setTwoCheckDate(model.getFinalCheckDate());
            sampleVo.setEnterDate(model.getApplyDate());
            sampleVo.setFinalCheckDate(model.getFinalCheckDate());
            sampleVo.setSampleRemark(model.getSampleRemark());
            sampleVo.setResultRemark(model.getResultRemark());
            sampleVo.setHspOrgId(model.getHspOrgId());
            sampleVo.setHspOrgName(model.getHspOrgName());
            sampleVo.setUrgent(model.getUrgent());
            sampleVo.setTestItemName(StringUtils.EMPTY);
            if (CollectionUtils.isNotEmpty(model.getTestItems())) {
                sampleVo.setTestItemName(StringUtils.join(model.getTestItems().stream()
                        .map(BaseSampleEsModelDto.TestItem::getTestItemName).collect(Collectors.toList()), ","));
            }
            sampleVo.setPatientName(model.getPatientName());
            sampleVo.setPatientSex(SexEnum.getByCode(model.getPatientSex()).getDesc());
            sampleVo.setPatientAge(model.getPatientAge());
            sampleVo.setPatientSubage(model.getPatientSubage());
            sampleVo.setPatientSubageUnit(model.getPatientSubageUnit());
            sampleVo.setIsPrint(model.getIsPrint());
            sampleVo.setApplyType(model.getApplyTypeName());
            sampleVo.setDiagnosis(model.getDiagnosis());
            sampleVo.setPatientVisitCard(model.getPatientVisitCard());
            targetList.add(sampleVo);
        }

        return targetList.stream()
                .sorted(Comparator
                        .comparing(RoutineSamplesVo::getFinalCheckDate, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(RoutineSamplesVo::getSampleId))
                .collect(Collectors.toList());
    }

    /**
     * 获取结果查询 es 条件
     */
    private SampleEsQuery getSampleEsQuery(QueryRoutineSamplesVo queryVo) {
        LoginUserHandler.User user = LoginUserHandler.get();

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(NumberUtils.INTEGER_ONE);
        // 当前用户所属专业组
        query.setGroupIds(Collections.singleton(user.getGroupId()));

        // 已审
        query.setIsAudit(YesOrNoEnum.YES.getCode());

        // 只查询常规检验
        query.setItemTypes(Collections.singleton(ItemTypeEnum.ROUTINE.name()));

        // 检验日期
        if (Objects.nonNull(queryVo.getTestDateStart()) && Objects.nonNull(queryVo.getTestDateEnd())) {
            query.setStartTestDate(queryVo.getTestDateStart());
            query.setEndTestDate(queryVo.getTestDateEnd());
        }

        // 审核日期
        if (Objects.nonNull(queryVo.getCheckDateStart()) && Objects.nonNull(queryVo.getCheckDateEnd())) {
            query.setStartFinalCheckDate(queryVo.getCheckDateStart());
            query.setEndFinalCheckDate(queryVo.getCheckDateEnd());
        }

        // 检验者ID
        if (Objects.nonNull(queryVo.getTesterId())) {
            query.setTesterId(queryVo.getTesterId());
        }

        // 审核人ID
        if (Objects.nonNull(queryVo.getCheckerId())) {
            query.setFinalCheckerIds(Collections.singleton(queryVo.getCheckerId()));
        }

        // 检验项目
        if (Objects.nonNull(queryVo.getTestItemId())) {
            query.setTestItemIds(Collections.singleton(queryVo.getTestItemId()));
        }

        // 送检机构
        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Collections.singleton(queryVo.getHspOrgId()));
        }

        // 姓名
        if (StringUtils.isNotBlank(queryVo.getPatientName())) {
            query.setPatientName(queryVo.getPatientName());
        }

        // 性别
        if (Objects.nonNull(queryVo.getPatientSex())
                && !Objects.equals(queryVo.getPatientSex(), SexEnum.DEFAULT.getCode())) {
            query.setPatientSex(queryVo.getPatientSex());
        }
        // 门诊/住院号
        if (StringUtils.isNotBlank(queryVo.getPatientVisitCard())) {
            query.setPatientVisitCard(queryVo.getPatientVisitCard());
        }

        if (StringUtils.isNotBlank(queryVo.getApplyType())) {
            query.setApplyTypes(Collections.singleton(queryVo.getApplyType()));
        }

        if (StringUtils.isNotBlank(queryVo.getBarcode())) {
            query.setBarcodes(Collections.singleton(queryVo.getBarcode()));
        }

        if (StringUtils.isNotBlank(queryVo.getResultRemark())) {
            query.setResultRemark(queryVo.getResultRemark());
        }

        if (StringUtils.isNotBlank(queryVo.getSampleRemark())) {
            query.setSampleRemark(queryVo.getSampleRemark());
        }

        // 阴阳性结果
        if (ObjectUtils.allNotNull(queryVo.getYinAndYang(), YinAndYang.getEnum(queryVo.getYinAndYang()))) {
            YinAndYang yinYangEnum = YinAndYang.getEnum(queryVo.getYinAndYang());
            final SystemParamDto param = systemParamService.selectByParamName(yinYangEnum.getValue(), user.getOrgId());
            if (Objects.nonNull(param) && StringUtils.isNotBlank(param.getParamValue())) {
                query.setYinAndYang(Arrays.stream(param.getParamValue().split(",")).collect(Collectors.toSet()));
            }
        }

        // 样本类型
        if (Objects.nonNull(queryVo.getSampleType())) {
            query.setSampleTypeCodes(Collections.singleton(queryVo.getSampleType()));
        }

        query.setSorts(
                Lists.newArrayList(SampleEsQuery.Sort.builder().filedName("finalCheckDate").order("ASC").build()));
        return query;

    }


    /**
     * 查看指定检验时间的一审数据
     */
    @PostMapping("/select-combined-bill")
    public Object selectCombinedBill(@RequestBody SelectCombinedBillDTO dto) throws ExecutionException, InterruptedException {
        QuerySampleVo querySampleVo = new QuerySampleVo();
        querySampleVo.setTestDateStart(dto.getTestDateStart());
        querySampleVo.setTestDateEnd(dto.getTestDateEnd());
        querySampleVo.setSampleStatus(SampleStatusEnum.ONE_AUDIT.name());
        // FIXME 2024/6/27 setUrgent(NumberUtils.INTEGER_TWO)
        querySampleVo.setUrgent(NumberUtils.INTEGER_TWO);
        // 只查询检验样本 不查询质控样本
        querySampleVo.setIsTestSample(YesOrNoEnum.YES.getCode());
        // 常规检验样本 VOS
        List<RoutineSampleVo> routineSampleVoList = JSON.parseArray(JSON.toJSONString(this.samples(querySampleVo)), RoutineSampleVo.class);
        if (CollectionUtils.isEmpty(routineSampleVoList)) {
            return routineSampleVoList;
        }

        routineSampleVoList = routineSampleVoList.stream()
                .filter(e -> StringUtils.isBlank(e.getMergeMasterBarcode()) &&
                        StringUtils.isBlank(e.getMergeExtraInfo()))
                .collect(Collectors.toList());

        Map<Long, RoutineSampleVo> sampleVoMap = routineSampleVoList.stream().collect(Collectors.toMap(RoutineSampleVo::getSampleId, Function.identity(), (a, b) -> b));
        List<Long> sampleIds = routineSampleVoList.stream().map(RoutineSampleVo::getSampleId).collect(Collectors.toList());

        // 报告项目信息
        List<SampleReportItemDto> sampleReportItemDtoList = new ArrayList<>();
        // key 为sampleId+reportCode
        Map<String, SampleResultDto> sampleReportItemResultMap = new LinkedHashMap<>();

        CountDownLatch countDownLatch = new CountDownLatch(2);

        // 样本报告项目信息
        threadPoolConfig.getPool().submit(() -> selectSampleReportItem(sampleIds, sampleReportItemDtoList, countDownLatch));

        // 查询结果
        threadPoolConfig.getPool().submit(() -> selectSampleResult(sampleIds, sampleReportItemResultMap, countDownLatch));

        List<SampleReportItemPatDto> sampleReportItemPatDtos = new ArrayList<>();

        countDownLatch.await();

        // reportCode 和 分组信息
        Map<String, CombinedBillDTO> reportCodeMap = combinedBillConfig.getReportCodeMap();

        for (SampleReportItemDto sampleReportItemDto : sampleReportItemDtoList) {
            RoutineSampleVo routineSampleVo = sampleVoMap.get(sampleReportItemDto.getSampleId());
            SampleResultDto sampleResultDto = sampleReportItemResultMap.get(sampleReportItemDto.getSampleId() + sampleReportItemDto.getReportItemCode());
            CombinedBillDTO combinedBillDTO = reportCodeMap.get(sampleReportItemDto.getReportItemCode());
            if (Objects.isNull(sampleResultDto) || Objects.isNull(routineSampleVo) || Objects.isNull(combinedBillDTO)) {
                continue;
            }

            SampleReportItemPatDto bean = BeanUtil.toBean(sampleReportItemDto, SampleReportItemPatDto.class);
            bean.setUuid(UUID.randomUUID().toString());
            bean.setPatientName(routineSampleVo.getPatientName());
            bean.setBarcode(routineSampleVo.getBarcode());
            bean.setHspOrgId(routineSampleVo.getHspOrgId());
            bean.setHspOrgName(routineSampleVo.getHspOrgName());
            bean.setTestDate(routineSampleVo.getTestDate());
            bean.setResult(sampleResultDto.getResult());
            bean.setCombinedBillConfig(combinedBillDTO);
            bean.setUnit(sampleResultDto.getUnit());
            bean.setSampleNo(routineSampleVo.getSampleNo());
            sampleReportItemPatDtos.add(bean);
        }
        sampleReportItemPatDtos.sort(Comparator.comparing(SampleReportItemPatDto::getPatientName));
        return sampleReportItemPatDtos;
    }

    /**
     * 预合并
     */
    @PostMapping("/previous-combined-bill")
    public Object previousCombinedBill(@RequestBody Map<String, Set<Long>> map) {
        Set<Long> applyIds = map.get("applyIds");
        Assert.notEmpty(applyIds, "申请单id不能为空");
        List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);
        // 根据 性别、送检机构、名字 判断是否同一个人
        Set<String> resultSet = applyDtos.stream()
                .filter(s -> StringUtils.isNotBlank(s.getPatientName()))
                .map(s -> s.getPatientSex() + "-" + s.getHspOrgId() + "-" + s.getPatientName())
                .collect(Collectors.toSet());
        return resultSet.size() > 1;
    }

    /**
     * 合并指定的一审数据
     */
    @PostMapping("/combined-bill")
    public Object combinedBill(@RequestBody CommitCombinedBillDTO dto) {
        checkCombinedBill(dto);
        return sampleReportItemService.combinedBill(dto);
    }


    /**
     * 查询取消合并指定的一审数据
     */
    @GetMapping("/select-master-combined-bill")
    public Object selectMasterCombinedBill(@RequestParam String masterBarcode) {
        Assert.notNull(masterBarcode, "条码号不能为空");
        ApplySampleDto masterApplySample = applySampleService.selectByBarcodeAndGroupId(masterBarcode, LoginUserHandler.get().getGroupId());
        Assert.notNull(masterApplySample, "并单条码号不存在");
        ApplyDto applyDto = applyService.selectByApplyId(masterApplySample.getApplyId());
        SampleDto sampleDto = sampleService.selectByApplySampleId(masterApplySample.getApplySampleId());

        List<BaseSampleEsModelDto> baseSampleEsModelDtos = new ArrayList<>();
        BaseSampleEsModelDto baseSampleEsModelDto = new BaseSampleEsModelDto();
        BeanUtils.copyProperties(masterApplySample, baseSampleEsModelDto);
        BeanUtils.copyProperties(applyDto, baseSampleEsModelDto);
        BeanUtils.copyProperties(sampleDto, baseSampleEsModelDto);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("master", baseSampleEsModelDto);

        List<ApplySampleDto> mergeBaseApplySampleDtos = applySampleService.selectMergeByBarcodes(masterBarcode);
        List<ApplyDto> applyDtos = applyService.selectByApplyIds(mergeBaseApplySampleDtos.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toList()));
        List<SampleDto> sampleDtos = sampleService.selectByApplySampleIds(mergeBaseApplySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList()));
        Map<Long, ApplyDto> applyDtoMap = applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity(), (a, b) -> b));
        Map<Long, SampleDto> sampleDtoMap = sampleDtos.stream().collect(Collectors.toMap(SampleDto::getApplySampleId, Function.identity(), (a, b) -> b));

        for (ApplySampleDto mergeBaseApplySampleDto : mergeBaseApplySampleDtos) {
            baseSampleEsModelDto = new BaseSampleEsModelDto();
            BeanUtils.copyProperties(mergeBaseApplySampleDto, baseSampleEsModelDto);
            BeanUtils.copyProperties(applyDtoMap.get(mergeBaseApplySampleDto.getApplyId()), baseSampleEsModelDto);
            BeanUtils.copyProperties(sampleDtoMap.get(mergeBaseApplySampleDto.getApplySampleId()), baseSampleEsModelDto);
            baseSampleEsModelDtos.add(baseSampleEsModelDto);
        }
        resultMap.put("mergeBase", baseSampleEsModelDtos);

        return resultMap;
    }

    /**
     * 取消合并指定的一审数据
     */
    @PostMapping("/cancel-combined-bill")
    public Object cancelCombinedBill(@RequestBody BarCodeDto barCodeDto) {
        barCodeDto.verifyParam();
        return sampleReportItemService.cancelCombinedBill(barCodeDto);
    }

    /**
     * 校验并单数据
     *
     * @param dto
     */
    private void checkCombinedBill(CommitCombinedBillDTO dto) {
        if (CollectionUtils.isEmpty(dto.getSampleReportItemPatDtos())) {
            throw new IllegalArgumentException("并单的数据不能为空");
        }
        if (StringUtils.isBlank(dto.getBarcode())) {
            throw new IllegalArgumentException("并单后的条码号不能为空");
        }
        Assert.notNull(dto.getSampleId(), "样本不能为空");
    }

    private void selectSampleResult(List<Long> sampleIds, Map<String, SampleResultDto> sampleReportItemResultMap, CountDownLatch countDownLatch) {
        try {
            Map<String, SampleResultDto> map = sampleResultService.selectBySampleIds(sampleIds).stream()
                    .collect(Collectors.toMap(e -> e.getSampleId() + e.getReportItemCode(), v -> v, (a, b) -> a));
            sampleReportItemResultMap.putAll(map);
        } finally {
            countDownLatch.countDown();
        }
    }

    private void selectSampleReportItem(List<Long> sampleIds, List<SampleReportItemDto> sampleReportItemDtoList, CountDownLatch countDownLatch) {
        try {
            List<SampleReportItemDto> sampleReportItemDtos = sampleReportItemService.selectBySampleIds(sampleIds);

            Map<Long, List<SampleReportItemDto>> sampleReportMap = sampleReportItemDtos.stream().collect(Collectors.groupingBy(SampleReportItemDto::getSampleId));

            Iterator<Map.Entry<Long, List<SampleReportItemDto>>> iterator = sampleReportMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Set<String> reportCodes = iterator.next().getValue().stream().map(SampleReportItemDto::getReportItemCode).collect(Collectors.toSet());
                if (!combinedBillConfig.getReportCodes().containsAll(reportCodes)) {
                    iterator.remove();
                }
            }
            sampleReportItemDtoList.addAll(sampleReportMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));
        } finally {
            countDownLatch.countDown();
        }
    }
}
