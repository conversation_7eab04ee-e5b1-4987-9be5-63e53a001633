package com.labway.lims.routine.mapstruct;

import java.util.List;

import org.mapstruct.Mapper;

import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.model.TbSampleCriticalResult;

/**
 * 危机值 相关转换
 *
 * <AUTHOR>
 * @since 2023/8/25 9:52
 */
@Mapper(componentModel = "spring")
public interface SampleCriticalResultConverter {


    SampleCriticalResultDto sampleCriticalResultDtoFromTbObj(TbSampleCriticalResult obj);

    List<SampleCriticalResultDto> sampleCriticalResultDtoListFromTbObj(List<TbSampleCriticalResult> list);

    

}
