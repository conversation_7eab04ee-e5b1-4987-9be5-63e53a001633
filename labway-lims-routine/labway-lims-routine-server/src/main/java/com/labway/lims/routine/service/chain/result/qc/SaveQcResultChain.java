package com.labway.lims.routine.service.chain.result.qc;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/7/3 19:00
 */
@Component
public class SaveQcResultChain extends ChainBase implements InitializingBean {
    @Resource
    private CheckQcParamsCommand checkQcParamsCommand;
    @Resource
    private FillSampleInfoCommand fillSampleInfoCommand;
    @Resource
    private QCInstrumentReportReferenceCommand qcInstrumentReportReferenceCommand;
    @Resource
    private AddQCResultCommand addQCResultCommand;
    @Resource
    private RemoveBeforeQCResultCommand removeBeforeQCResultCommand;
    @Resource
    private YinYangQCResultCommand yinYangQCResultCommand;
    @Resource
    private QCResultFormatCommand qcResultFormatCommand;
    @Resource
    private ConvertQCResultCommand convertQCResultCommand;
    @Resource
    private NumberQCResultCommand numberQCResultCommand;
    @Resource
    private AddQCSampleCommand addQCSampleCommand;
    @Resource
    private QCCheckCanSaveCommand qcCheckCanSaveCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        //检验参数
        addCommand(checkQcParamsCommand);
        //提前获取信息
        addCommand(fillSampleInfoCommand);
        //结果转换
        addCommand(convertQCResultCommand);
        //质控结果是否可保存
        addCommand(qcCheckCanSaveCommand);
        //参考值范围
        addCommand(qcInstrumentReportReferenceCommand);
//        //查询结果数据
        addCommand(numberQCResultCommand.getDefaultProvider());
        //结果查询数据
        addCommand(numberQCResultCommand);
        //阴阳结果
        addCommand(yinYangQCResultCommand);
        //结果格式化
        addCommand(qcResultFormatCommand);
        //添加样本
        addCommand(addQCSampleCommand);
        //删除之前结果
        addCommand(removeBeforeQCResultCommand);
        //插入质控相关数控
        addCommand(addQCResultCommand);
        // 结束
        addCommand(c -> PROCESSING_COMPLETE);
    }
}
