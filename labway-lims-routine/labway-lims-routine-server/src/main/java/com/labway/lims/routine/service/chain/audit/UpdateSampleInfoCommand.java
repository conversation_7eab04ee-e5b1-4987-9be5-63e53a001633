package com.labway.lims.routine.service.chain.audit;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class UpdateSampleInfoCommand implements Command {

    @DubboReference
    private SampleReportService sampleReportService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditSampleContext context = AuditSampleContext.from(c);
        final SampleAuditDto param = context.getParam();

        final List<SampleReportDto> sampleReports = context.getSampleReports();

        if (Objects.equals(context.getParam().getAuditStatus(), SampleAuditStatusEnum.ONE_CHECK.name())) {
            return CONTINUE_PROCESSING;
        }


        // 判断如果是上传的pdf文件 则不进行更新
        if (Objects.equals(sampleReports.iterator().next().getIsUploadPdf(), YesOrNoEnum.YES.getCode())){
            return CONTINUE_PROCESSING;
        }


        // 把之前的报告单删除
        sampleReportService.deleteBySampleIds(Collections.singleton(param.getSampleId()));

        // 批量保存
        sampleReportService.addSampleReportBatch(sampleReports);
        return CONTINUE_PROCESSING;
    }
}
