package com.labway.lims.routine.service.chain.retest;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.routine.api.dto.ResultStatusDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.service.chain.result.UpdateMissItemCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/27 17:29
 */
@Slf4j
@Component
public class StartRetestUpdateMissItemCommand implements Filter, Command {

    @Resource
    private UpdateMissItemCommand updateMissItemCommand;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @Override
    public boolean execute(Context c) throws Exception {
        final StartRetestContext context = StartRetestContext.from(c);
        final SampleDto sample = context.getSample();

        updateMissItemCommand.mark(getMissItemKey(sample.getSampleId()),   context.getRetestSampleReportItems().stream().map(e->{
            //收到结果的时候修改redis中的结果,以及结果状态
            final ResultStatusDto s = new ResultStatusDto();
            s.setReportItemCode(e.getReportItemCode());
            s.setResult(StringUtils.EMPTY);
            s.setJudge(StringUtils.EMPTY);
            s.setIsRetest(YesOrNoEnum.NO.getCode());
            s.setIsException(YesOrNoEnum.NO.getCode());
            s.setIsCritical(YesOrNoEnum.NO.getCode());
            return s;
        }).collect(Collectors.toList()));

        return CONTINUE_PROCESSING;
    }

    /**
     * 获取到缺失结果相
     */
    public String getMissItemKey(long sampleId) {
        return redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":RETEST_SAMPLE_MISS_ITEM:" + sampleId;
    }


    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
