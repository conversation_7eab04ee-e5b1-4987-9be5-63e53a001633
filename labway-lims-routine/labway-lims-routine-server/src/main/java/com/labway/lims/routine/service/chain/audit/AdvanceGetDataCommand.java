package com.labway.lims.routine.service.chain.audit;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.InstrumentGroupInstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultTipDto;
import com.labway.lims.base.api.service.InstrumentGroupInstrumentService;
import com.labway.lims.base.api.service.InstrumentReportItemResultTipService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
@Slf4j
public class AdvanceGetDataCommand implements Command {

    @DubboReference
    private ApplyService applyService;
    @Resource
    private SampleService sampleService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @Resource
    private SampleResultService sampleResultService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentGroupInstrumentService instrumentGroupInstrumentService;
    @DubboReference
    private InstrumentReportItemResultTipService instrumentReportItemResultTipService;
    @DubboReference
    private SampleReportService sampleReportService;


    @Override
    public boolean execute(Context c) throws Exception {
        final AuditSampleContext context = AuditSampleContext.from(c);
        final SampleAuditDto param = context.getParam();
        final SampleDto sample = sampleService.selectBySampleId(param.getSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("样本不存在");
        }

        final ApplyDto apply = applyService.selectByApplyId(sample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        final List<SampleReportItemDto> sampleReportItems = sampleReportItemService.selectBySampleId(sample.getSampleId());
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            throw new IllegalStateException("样本报告项不存在");
        }

        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(sample.getApplySampleId());
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("申请单项目不存在");
        }

        // 查询是否有上传的pdf文件 并缓存到上下文中
        final SampleReportDto sampleReport = sampleReportService.selectByApplySampleId(sample.getApplySampleId());

        // 如果上传了pdf则不校验结果
        final List<SampleResultDto> sampleResults = sampleResultService.selectBySampleId(param.getSampleId());
        if (CollectionUtils.isEmpty(sampleResults) && Objects.isNull(sampleReport)) {
            throw new IllegalStateException("样本结果不存在");
        }


        final List<InstrumentGroupInstrumentDto> instrumentGroupInstruments = instrumentGroupInstrumentService.selectByInstrumentGroupId(sample.getInstrumentGroupId());
        if (CollectionUtils.isEmpty(instrumentGroupInstruments)) {
            throw new IllegalStateException("专业小组不存在");
        }

        final List<InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService.selectByInstrumentIds(instrumentGroupInstruments.stream()
                .map(InstrumentGroupInstrumentDto::getInstrumentId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(instrumentReportItems)) {
            throw new IllegalStateException("仪器报告项不存在");
        }

        final List<InstrumentReportItemResultTipDto> instrumentReportItemResultTips = new ArrayList<>(instrumentReportItemResultTipService.selectByInstrumentIds(instrumentGroupInstruments.stream()
                .map(InstrumentGroupInstrumentDto::getInstrumentId).collect(Collectors.toSet())));
        // 删除禁用的
        instrumentReportItemResultTips.removeIf(e -> !Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()));

        // 删除各自审核外的
        if (Objects.equals(param.getAuditStatus(), SampleAuditStatusEnum.ONE_CHECK.name())) {
            instrumentReportItemResultTips.removeIf(e -> !Objects.equals(e.getTipType(), SampleAuditStatusEnum.ONE_CHECK.name()));
        } else if (Objects.equals(param.getAuditStatus(), SampleAuditStatusEnum.TWO_CHECK.name())) {
            instrumentReportItemResultTips.removeIf(e -> !Objects.equals(e.getTipType(), SampleAuditStatusEnum.TWO_CHECK.name()));
        }

        // 删除别的送检机构的提示
        instrumentReportItemResultTips.removeIf(e -> {
            if (Objects.isNull(e.getHspOrgId())) {
                return false;
            }
            if (Objects.equals(e.getHspOrgId(), NumberUtils.LONG_ZERO)) {
                return false;
            }
            return !Objects.equals(e.getHspOrgId(), apply.getHspOrgId());
        });

        // 有机构ID的放前面
        instrumentReportItemResultTips.sort((o1, o2) -> o2.getHspOrgId().compareTo(o1.getHspOrgId()));


        context.put(AuditSampleContext.SAMPLE, sample);
        context.put(AuditSampleContext.APPLY, apply);
        context.put(AuditSampleContext.APPLY_SAMPLES, applySampleService.selectByApplySampleId(param.getSampleId()));
        context.put(AuditSampleContext.SAMPLE_TEST_ITEMS, applySampleItems);
        context.put(AuditSampleContext.SAMPLE_REPORT_ITEMS, sampleReportItems);
        context.put(AuditSampleContext.APPLY_SAMPLE, applySample);
        context.put(AuditSampleContext.SAMPLE_REPORT_ITEM_RESULTS, sampleResults);
        context.put(AuditSampleContext.INSTRUMENT_REPORT_ITEMS, instrumentReportItems);
        context.put(AuditSampleContext.INSTRUMENT_REPORT_ITEM_TIPS, instrumentReportItemResultTips);
        if (Objects.nonNull(sampleReport)){
            context.put(AuditSampleContext.SAMPLE_REPORT_LIST, Collections.singletonList(sampleReport));
        }

        return CONTINUE_PROCESSING;
    }
}
