package com.labway.lims.routine.service.chain.result.qc;

import cn.hutool.core.date.DateUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemResultExchangeService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.routine.api.dto.QuerySampleDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/4 09:35
 */
@Component
public class FillSampleInfoCommand implements Command {

    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    @DubboReference
    private InstrumentReportItemResultExchangeService instrumentReportItemResultExchangeService;
    @Resource
    private SampleService sampleService;

    @Override
    public boolean execute(Context c) throws Exception {

        final SaveQCResultContext context = SaveQCResultContext.from(c);

        final InstrumentDto instrument = instrumentService.selectByInstrumentId(context.getInstrumentId());

        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("仪器不存在");
        }

        final List<InstrumentReportItemDto> instrumentReportItems =
            instrumentReportItemService.selectByInstrumentId(instrument.getInstrumentId());

        if (CollectionUtils.isEmpty(instrumentReportItems)) {
            throw new IllegalStateException("当前仪器报告项目为空");
        }

        final InstrumentReportItemDto instrumentReportItem = instrumentReportItems.stream()
            .filter(f -> Objects.equals(f.getReportItemCode(), context.getReportItemCode())).findFirst().orElse(null);

        if (Objects.isNull(instrumentReportItem)) {
            throw new IllegalStateException("当前仪器不存在该报告项目");
        }

        context.put(SaveQCResultContext.INSTRUMENT_REPORT_ITEMS, instrumentReportItems);
        context.put(SaveQCResultContext.INSTRUMENT_REPORT_ITEM, instrumentReportItem);
        context.put(SaveQCResultContext.INSTRUMENT, instrument);

        if (Objects.isNull(context.getResultExchange())) {
            final List<InstrumentReportItemResultExchangeDto> exchanges = instrumentReportItemResultExchangeService
                .selectByInstrumentReportItemId(context.getInstrumentReportItem().getInstrumentReportItemId());
            context.put(SaveQCResultContext.RESULT_EXCHANGE, exchanges);
        }

        final QuerySampleDto dto = new QuerySampleDto();
        dto.setSampleNo(context.getSampleNo());
        dto.setGroupId(context.getGroupId());
        dto.setOrgId(LoginUserHandler.get().getOrgId());
        dto.setInstrumentId(context.getInstrumentId());
        dto.setTestDateStart(DateUtil.beginOfDay(new Date()));
        dto.setTestDateEnd(DateUtil.endOfDay(new Date()));
        final List<SampleDto> samples = sampleService.selectByTestDate(dto).stream()
            .filter(e -> Objects.equals(e.getInstrumentId(), instrument.getInstrumentId()))
            .filter(e -> Objects.equals(e.getApplySampleId(), NumberUtils.LONG_ZERO)
                && Objects.equals(e.getApplyId(), NumberUtils.LONG_ZERO))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(samples)) {
            context.put(SaveQCResultContext.SAMPLE, samples.iterator().next());
            context.setSampleId(samples.iterator().next().getSampleId());
        }
        return CONTINUE_PROCESSING;
    }
}
