package com.labway.lims.routine.service.chain.auto.audit;

import com.labway.lims.routine.service.chain.audit.AuditSampleContext;
import com.labway.lims.routine.service.chain.audit.AuditSampleLimitCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 自动审核限流
 *
 * <AUTHOR> on 2025/7/29.
 */
@Slf4j
@Component
public class AutoAuditLimitCommand implements Filter, Command {

	private static final String MARK = AutoAuditLimitCommand.class.getName();

	@Resource
	private AuditSampleLimitCommand auditSampleLimitCommand;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Override
	public boolean execute(Context c) throws Exception {

		AutoAuditContext context = AutoAuditContext.from(c);
		if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue()
				.setIfAbsent(auditSampleLimitCommand.getSampleAuditKey(context.getSampleId()),
						StringUtils.EMPTY, Duration.ofSeconds(10)))) {
			throw new IllegalStateException("正在审核中");
		}

		// 存储锁标识
		context.put(MARK, StringUtils.EMPTY);

		return CONTINUE_PROCESSING;
	}

	@Override
	public boolean postprocess(Context c, Exception exception) {
		AuditSampleContext context = AuditSampleContext.from(c);

		// 执行完成 释放锁
		if (context.containsKey(MARK)) {
			stringRedisTemplate.delete(auditSampleLimitCommand.getSampleAuditKey(context.getParam().getSampleId()));
		}

		return CONTINUE_PROCESSING;
	}
}
