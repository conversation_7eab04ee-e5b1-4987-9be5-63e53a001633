package com.labway.lims.routine.vo;

import com.labway.lims.api.enums.base.InstrumentItemTypeEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class SampleReportItemDetailVo {

    /**
     * sampleReportItemId
     */
    private Long sampleReportItemId;

    /**
     * 样本ID
     */
    private Long sampleId;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 检验项目Id
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 结果
     */
    private String result;

    /**
     * 参考范围
     */
    private String range;

    /**
     * 单位
     */
    private String unit;

    /**
     * 结果判定
     *
     * @see TestJudgeEnum
     */
    private String judge;

    /**
     * 最近一次结果
     */
    private ResentResult resentResult;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 英文缩写
     */
    private String enAb;

    /**
     * 结果状态 1: 危机 2: 异常 0: 正常
     *
     * @see ResultStatusEnum
     */
    private Integer status;

    /**
     * 复查状态 0默认 1复查中 2已复查
     *
     * @see RetestStatusEnum
     */
    private Integer isRetest;

    /**
     * 打印顺序
     */
    private Integer printSort;

    /**
     * 仪器名称
     */
    private String instrumentName;


    /**
     * 检测结果类型
     */
    private String itemTypeName;

    /**
     * 检测结果类型编码
     *
     * @see InstrumentItemTypeEnum
     */
    private String itemTypeCode;


    /**
     * 参考值上限值
     */
    private String referValueMax;


    /**
     * 参考值下限值
     */
    private String referValueMin;

    /**
     * od值 = 原始od值 - 空白值
     */
    private String oDValue;

    /**
     * 定性的 套入cutoff 公式计算的
     * 定量的没有
     */
    private String cutoffValue;

    /**
     * S/Co = 原始OD / cutoff值
     */
    private String scoValue;

    /**
     * 是否手工录入结果 0否1是
     */
    private Integer isHandeResult;

    /**
     * 检验方法编码
     */
    private String examMethodCode;

    /**
     * 检验方法名称
     */
    private String examMethodName;

    /**
     * 最近一次结果
     */
    @Getter
    @Setter

    public static final class ResentResult implements Serializable {

        /**
         * 结果id
         */
        private Long sampleResultId;

        /**
         * 结果
         */
        private String result;

        /**
         * 结果状态 1: 危机 2: 异常 0: 正常
         *
         * @see ResultStatusEnum
         */
        private Integer status;

        /**
         * 结果判定
         *
         * @see TestJudgeEnum
         */
        private String judge;
    }

}