package com.labway.lims.routine.service.chain.auto.audit;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.service.chain.result.InstrumentReportReferenceCommand;
import com.labway.lims.routine.service.chain.result.SaveResultContext;
import com.labway.lims.routine.vo.ruleengine.AutoAuditResult;
import com.labway.lims.routine.vo.ruleengine.RulesEngineResponse;
import com.labway.lims.routine.vo.ruleengine.RulesEngineResponseData;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

/**
 * 调用规则引擎
 *
 * <AUTHOR> Tianhao on 2025/7/29.
 */
@Slf4j
@Component
public class AutoAuditJVSExecCommand implements Command {

	@Resource
	private RestTemplate restTemplate;

	@Override
	public boolean execute(Context c) throws Exception {
		AutoAuditContext context = AutoAuditContext.from(c);
		SaveResultContext saveResultOriginContext = context.getSaveResultOriginContext();
		ApplyDto apply = saveResultOriginContext.getApply();
		ApplySampleDto applySample = saveResultOriginContext.getApplySample();

		// 自动审核结果
		List<AutoAuditResult> autoAuditResultList = new ArrayList<>();

		// 格式化数据，用于调用规则引擎
		// key: reportItemCode - value：testResult
		Map<String, Object> autoAuditRuleEngineParams = new HashMap<>();

		for (SampleResultDto sampleResultDto : context.getSampleResults()) {
			autoAuditRuleEngineParams.put(StringPool.UNDERLINE + sampleResultDto.getReportItemCode(), sampleResultDto.getResult());
		}
		// 样本id 用于查询历史结果
		autoAuditRuleEngineParams.put(AutoAuditContext.JVS_SAMPLE_ID, context.getSampleId().toString());
		// 性别
		autoAuditRuleEngineParams.put(AutoAuditContext.JVS_SEX, apply.getPatientSex());
		// 年龄
		autoAuditRuleEngineParams.put(AutoAuditContext.JVS_AGE, apply.getPatientAge());
		autoAuditRuleEngineParams.put(AutoAuditContext.JVS_SUB_AGE, apply.getPatientSubage());
		autoAuditRuleEngineParams.put(AutoAuditContext.JVS_AGE_DAYS, getPatientAgeDays(apply));
		autoAuditRuleEngineParams.put(AutoAuditContext.JVS_SUB_AGE_UNIT, apply.getPatientSubageUnit());
		// 样本类型
		autoAuditRuleEngineParams.put(AutoAuditContext.JVS_SAMPLE_TYPE, applySample.getSampleTypeName());

		HttpEntity<Map> entity = new HttpEntity<>(autoAuditRuleEngineParams);

		// 根据检验项目去调用规则引擎判断是否可以进行审核
		for (TestItemDto sampleTestItem : context.getSampleTestItems()) {
			// 自动审核规则地址
			String decisionBasisUrl = sampleTestItem.getDecisionBasis();

			try {
				log.debug("自动审核：检验项目[{}] 决策执行地址 [{}] 决策执行参数：{}", sampleTestItem.getTestItemName(), decisionBasisUrl, JSON.toJSONString(autoAuditRuleEngineParams));

				RulesEngineResponse<AutoAuditResult> rulesEngineResponse = restTemplate.postForObject(decisionBasisUrl, entity, RulesEngineResponse.class);

				log.debug("自动审核：检验项目[{}] 调用决策结果 {}", sampleTestItem.getTestItemName(), JSON.toJSONString(rulesEngineResponse));

				if (rulesEngineResponse == null || !NumberUtil.equals(YesOrNoEnum.NO.getCode(), rulesEngineResponse.getCode())) {
					throw new IllegalStateException(StrUtil.format("自动审核：检验项目[{}] 决策执行失败", sampleTestItem.getTestItemName()));
				}

				// 保存决策执行结果
				RulesEngineResponseData<AutoAuditResult> rulesEngineResponseData = rulesEngineResponse.getData();

				AutoAuditResult outputData = JSON.parseObject(JSON.toJSONString(rulesEngineResponseData.getOutputData()), AutoAuditResult.class);

				log.debug("自动审核：检验项目[{}] 决策执行结果 {}", sampleTestItem.getTestItemName(), outputData.getIsAutoSuccess());

				autoAuditResultList.add(outputData);
			} catch (Exception e) {
				log.error("调用JVS异常:", e);
			}
		}

		context.put(AutoAuditContext.AUTO_AUDIT_RESULT_LIST, autoAuditResultList);

		return CONTINUE_PROCESSING;
	}

	private Integer getPatientAgeDays(ApplyDto apply) {
		// 获取到病人的年龄（天数）
        int _days = 0;

        // 如果没有输入年龄 那么根据生日计算
        if (Objects.isNull(apply.getPatientAge())) {
            // 根据生日获取此人活了多少天
            if (StringUtils.isNotBlank(apply.getPatientCard()) && IdcardUtil.isValidCard(apply.getPatientCard())) {
                try {
                    _days = (int) IdcardUtil.getBirthDate(apply.getPatientCard()).between(new Date(), DateUnit.DAY);
                } catch (Exception e) {
                    _days += (apply.getPatientAge() * InstrumentReportReferenceCommand.YEAR_DAYS);
                }
            }
        } else {
            _days += (apply.getPatientAge() * InstrumentReportReferenceCommand.YEAR_DAYS);
        }

        if (Objects.nonNull(apply.getPatientSubage())) {
            if (Objects.equals(apply.getPatientSubageUnit(), PatientSubAgeUnitEnum.MONTH.getValue())) {
                _days += (apply.getPatientSubage() * InstrumentReportReferenceCommand.MONTH_DAYS);
            } else if (Objects.equals(apply.getPatientSubageUnit(), PatientSubAgeUnitEnum.WEEK.getValue())) {
                _days += (apply.getPatientSubage() * InstrumentReportReferenceCommand.WEEK_DAYS);
            } else if (Objects.equals(apply.getPatientSubageUnit(), PatientSubAgeUnitEnum.DAY.getValue())) {
                _days += apply.getPatientSubage();
            }
        }
		return _days;
	}
}
