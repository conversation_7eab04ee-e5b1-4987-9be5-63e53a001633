package com.labway.lims.routine.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LabwayDateUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.routine.*;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.routine.api.dto.CriticalResultConnectSampleResultDto;
import com.labway.lims.routine.api.dto.ExcelMergeRowsDto;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SelectSampleCriticalDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.routine.service.excel.ExportCriticalDataMergeStrategy;
import com.labway.lims.routine.vo.CriticalResultConnectSampleResultResponseVo;
import com.labway.lims.routine.vo.SampleCriticalReadBackVo;
import com.labway.lims.routine.vo.SampleCriticalResultDetailResponseVo;
import com.labway.lims.routine.vo.SampleCriticalResultHandleRequestVo;
import com.labway.lims.routine.vo.SampleCriticalResultListRequestVo;
import com.labway.lims.routine.vo.SampleCriticalResultListResponseVo;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 样本危急值 API
 *
 * <AUTHOR>
 * @since 2023/4/10 9:37
 */
@Slf4j
@RestController
@RequestMapping("/sample-critical-result")
public class SampleCriticalResultController extends BaseController {

    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleRetestItemService sampleRetestItemService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @Resource
    private SampleCriticalResultService sampleCriticalResultService;
    @Resource
    private SampleRetestMainService sampleRetestMainService;
    @Resource
    private SampleCriticalResultController self;
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    @DubboReference
    private SampleFlowService sampleFlowService;

    private static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 格式化字符串
     */
    private static final String FORMAT_STR = "%s-%s";


    /**
     * 危急值列表
     */
    @PostMapping("/list")
    public Object sampleCriticalResultList(@RequestBody SampleCriticalResultListRequestVo vo) {
        final SelectSampleCriticalDto dto = JSON.parseObject(JSON.toJSONString(vo), SelectSampleCriticalDto.class);

        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        dto.setOrgId(loginUser.getOrgId());
        dto.setGroupId(loginUser.getGroupId());

        final List<SampleCriticalResultDto> sampleCriticalResults =
            new ArrayList<>(sampleCriticalResultService.selectBySelectSampleCriticalDto(dto));
        if (CollectionUtils.isEmpty(sampleCriticalResults)) {
            return List.of();
        }

        // 删除掉已经禁用或终止的
        sampleCriticalResults.removeIf(e -> applySampleService.isTerminate(e.getApplySampleId())
            || applySampleService.isDisabled(e.getApplySampleId()));

        if (CollectionUtils.isEmpty(sampleCriticalResults)) {
            return List.of();
        }

        // 样本id List
        final Set<Long> applySampleIds =
            sampleCriticalResults.stream().map(SampleCriticalResultDto::getApplySampleId).collect(Collectors.toSet());

        // 对应样本信息
        final SampleEsQuery query = new SampleEsQuery();
        query.setPageNo(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setApplySampleIds(applySampleIds);
        query.setOrgId(loginUser.getOrgId());
        query.setGroupIds(Collections.singleton(loginUser.getGroupId()));
        final List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(query);
        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }

        // map key:样本id value: 样本信息
        Map<Long, BaseSampleEsModelDto> baseSampleEsModelDtoBySampleId =
            samples.stream().collect(Collectors.toMap(BaseSampleEsModelDto::getSampleId, Function.identity()));

        List<SampleCriticalResultListResponseVo> targetList =
            Lists.newArrayListWithCapacity(sampleCriticalResults.size());


        //1.1.3处理判断超时时间的字段
        final int timeOutConfig = sampleCriticalResultService.getTimeoutConfig();
        Map<Long,List<SampleRetestItemDto>> restItemMap = null;
        if (timeOutConfig > 0){
            //批量获取已复查 没处理的结果  用于后面的超时判断
            List<Long> sampleIds = sampleCriticalResults.stream().filter(f -> Objects.equals(f.getStatus(), SampleCriticalResultStatusEnum.REVIEW.getCode()))
                            .map(SampleCriticalResultDto::getSampleId).collect(Collectors.toList());
            //根据ID获取到复查中的结果列表，并且只保留有结果值的。，因为有结果值才会有复查时间
            List<SampleRetestItemDto> sampleRetestItemDtos = sampleRetestItemService.selectBySampleIds(sampleIds).stream()
                    .filter(s -> StringUtils.isNotEmpty(s.getResult())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sampleRetestItemDtos)){
                //这里需要将结果 根据sampleId做group操作
               restItemMap = sampleRetestItemDtos.stream().collect(Collectors.groupingBy(SampleRetestItemDto::getSampleId));
            }
        }
        final Map<Long,List<SampleRetestItemDto>> finalRestItemMap = restItemMap;

        sampleCriticalResults.forEach(item -> {
            SampleCriticalResultListResponseVo temp =
                JSON.parseObject(JSON.toJSONString(item), SampleCriticalResultListResponseVo.class);
            BaseSampleEsModelDto sampleEsModelDto = baseSampleEsModelDtoBySampleId.get(temp.getSampleId());
            if (Objects.nonNull(sampleEsModelDto)) {
                temp.setSampleNo(sampleEsModelDto.getSampleNo());
                temp.setItemType(sampleEsModelDto.getItemType());
                temp.setSendDoctorCode(sampleEsModelDto.getSendDoctorCode());
                temp.setSendDoctorName(sampleEsModelDto.getSendDoctorName());

                //这里需要赋值 一审和二审的人。而目前只有常规检验和外送检验会有危急值。
                if (sampleEsModelDto instanceof RoutineInspectionDto){
                    RoutineInspectionDto routineInspectionDto = (RoutineInspectionDto) sampleEsModelDto;
                    temp.setOneCheckerName(routineInspectionDto.getOneCheckerName());
                    temp.setOneCheckDate(LabwayDateUtil.getNonDefaultDbDate(routineInspectionDto.getOneCheckDate()));
                    temp.setTwoCheckerName(routineInspectionDto.getTwoCheckerName());
                    temp.setTwoCheckDate(LabwayDateUtil.getNonDefaultDbDate(routineInspectionDto.getTwoCheckDate()));
                }else if (sampleEsModelDto instanceof OutsourcingInspectionDto){
                    OutsourcingInspectionDto outsourcingInspectionDto = (OutsourcingInspectionDto) sampleEsModelDto;
                    temp.setOneCheckerName(outsourcingInspectionDto.getCheckerName());
                    temp.setOneCheckDate(LabwayDateUtil.getNonDefaultDbDate(outsourcingInspectionDto.getCheckDate()));
                    temp.setTwoCheckerName(outsourcingInspectionDto.getTwoCheckerName());
                    temp.setTwoCheckDate(LabwayDateUtil.getNonDefaultDbDate(outsourcingInspectionDto.getTwoCheckDate()));
                }
            }

            //1.1.3新增危急值属性及超时判断
            //当且仅当状态是已复查并且有超时配置时再做判断
            if (timeOutConfig > 0 && SampleCriticalResultStatusEnum.REVIEW.getCode() == temp.getStatus()){
                temp.setIsTimeOut(YesOrNoEnum.NO.getCode());
                if (finalRestItemMap != null){
                    //根据样本获取所有在复查的报告信息，然后筛选匹配当前报告
                    List<SampleRetestItemDto> sampleRetestItemDtos = finalRestItemMap.get(item.getSampleId()).stream()
                            .filter(e -> Objects.equals(e.getReportItemId(), item.getReportItemId())).collect(Collectors.toList());
                    temp.setIsTimeOut(isTimeOut(timeOutConfig,sampleRetestItemDtos)?
                            YesOrNoEnum.YES.getCode():YesOrNoEnum.NO.getCode());
                }
            }

            targetList.add(temp);
        });

        return targetList;
    }

    /**
     * 危急值 关联样本 相关结果信息
     */
    @PostMapping("/select-sample-result")
    public Object selectSampleResult(@RequestParam("criticalValueId") long criticalValueId) {

        final SampleCriticalResultDto sampleCriticalResult =
            sampleCriticalResultService.selectByCriticalValueId(criticalValueId);
        if (Objects.isNull(sampleCriticalResult)) {
            throw new IllegalStateException("对应危急值数据不存在");
        }

        // 关联样本id、报告项目id
        final Long sampleId = sampleCriticalResult.getSampleId();
        final Long reportItemId = sampleCriticalResult.getReportItemId();

        // 对应样本信息
        BaseSampleEsModelDto sample =
            elasticSearchSampleService.selectByApplySampleId(sampleCriticalResult.getApplySampleId());

        if (Objects.isNull(sample)) {
            throw new IllegalStateException("样本不存在");
        }

        final List<CriticalResultConnectSampleResultDto> resultList = Lists.newArrayList();
        // 原始结果
        final CriticalResultConnectSampleResultDto dto = new CriticalResultConnectSampleResultDto();
        dto.setSampleId(sampleId);
        dto.setReportItemId(sampleCriticalResult.getReportItemId());
        dto.setResultValue(sampleCriticalResult.getSampleResult());
        dto.setResultDate(sampleCriticalResult.getCreateDate());
        dto.setResultTypeName(SampleResultTypeEnum.ORIGIN.getDes());
        dto.setResultFromName(RetestModeEnum.FRONT.getDesc());
        final SampleResultDto sampleResult =
            sampleResultService.selectBySampleResultId(sampleCriticalResult.getSampleResultId(), sampleId, true);
        if (Objects.nonNull(sampleResult) && !Objects.equals(sampleResult.getInstrumentId(), NumberUtils.LONG_ZERO)) {
            dto.setResultFromName(RetestModeEnum.MACHINE.getDesc());
        }
        resultList.add(dto);

        // 样本 复查结果
        final List<SampleRetestItemDto> sampleRetestItems =
            sampleRetestItemService.selectBySampleIdAndReportItemId(sampleId, reportItemId);

        resultList.addAll(
            sampleRetestItems.stream().filter(e -> !Objects.equals(e.getRetestMode(), RetestModeEnum.DEFAULT.getCode()))
                .map(CriticalResultConnectSampleResultDto::getBySampleRetestItemDto).collect(Collectors.toList()));

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        // 样本 报告结果
        if (Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            final SampleResultDto sr = sampleResultService.selectBySampleIdAndReportItemId(sampleId, reportItemId);
            if (Objects.nonNull(sr)) {
                resultList.add(CriticalResultConnectSampleResultDto.getBySampleResultDto(sr));
            }
        }

        return Map.of("sampleNo", sample.getSampleNo(), "results",
            resultList.stream()
                .map(obj -> JSON.parseObject(JSON.toJSONString(obj), CriticalResultConnectSampleResultResponseVo.class))
                .collect(Collectors.toList()));
    }

    /**
     * 危急值 详情
     */
    @PostMapping("/select-detail-by-critical-value-id")
    public Object selectDetailByCriticalValueId(@RequestParam("criticalValueId") long criticalValueId) {
        SampleCriticalResultDto sampleCriticalResultDto =
            sampleCriticalResultService.selectByCriticalValueId(criticalValueId);
        if (Objects.isNull(sampleCriticalResultDto)) {
            throw new LimsException("对应危急值数据不存在");
        }
        if (Objects.equals(sampleCriticalResultDto.getStatus(), SampleCriticalResultStatusEnum.PROCESSED.getCode())) {
            throw new LimsException("危急值已处理");
        }

        // 关联样本id、报告项目id
        Long sampleId = sampleCriticalResultDto.getSampleId();

        // 对应样本下 报告项目
        SampleReportItemDto sampleReportItemDto = sampleReportItemService.selectBySampleIdAndReportItemCode(sampleId,
            sampleCriticalResultDto.getReportItemCode());
        if (Objects.isNull(sampleReportItemDto)) {
            throw new LimsException("危急值关联样本下报告项目不存在");
        }
        // 样本 复查结果
        List<SampleRetestItemDto> sampleRetestItems = sampleRetestItemService.selectBySampleIdAndReportItemId(
            sampleCriticalResultDto.getSampleId(), sampleCriticalResultDto.getReportItemId());
        List<CriticalResultConnectSampleResultDto> sampleResultDtoList = Lists.newArrayList();
        sampleResultDtoList.addAll(sampleRetestItems.stream()
            .filter(obj -> !Objects.equals(obj.getRetestMode(), RetestModeEnum.DEFAULT.getCode()))
            .map(CriticalResultConnectSampleResultDto::getBySampleRetestItemDto).collect(Collectors.toList()));

        // 此刻报告项目处于复查中
        if (Objects.equals(sampleReportItemDto.getIsRetest(), YesOrNoEnum.YES.getCode())) {
            sampleResultDtoList.add(CriticalResultConnectSampleResultDto.getWhenReviewSampleReportItem());
        }
        // 组装 详细信息
        SampleCriticalResultDetailResponseVo target =
            JSON.parseObject(JSON.toJSONString(sampleCriticalResultDto), SampleCriticalResultDetailResponseVo.class);
        target.setSampleResultList(sampleResultDtoList);

        // 样本结果
        target.getSampleResultList().sort((o1, o2) -> {
            if (Objects.isNull(o1.getResultDate()) || Objects.isNull(o2.getResultDate())) {
                return 0;
            }
            return o1.getResultDate().compareTo(o2.getResultDate());
        });

        return target;
    }

    /**
     * 危急值 处理完成 结束危急
     */
    @PostMapping("/finish-critical-handle")
    public Object finishCriticalHandle(@RequestBody SampleCriticalResultHandleRequestVo vo) {
        if (Objects.isNull(vo.getCriticalValueId()) || Objects.isNull(vo.getIsCritical())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }

        if (!(Objects.equals(vo.getIsCritical(), YesOrNoEnum.NO.getCode())
            || Objects.equals(vo.getIsCritical(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("是否危急值参数错误");
        }

        if (StringUtils.length(vo.getHandleContent()) > TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException("处理内容过长");
        }

        if (StringUtils.length(vo.getContactUserName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("联系人名称过长");
        }

        if (StringUtils.length(vo.getContactPhone()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("联系人电话过长");
        }

        SampleCriticalResultDto sampleCriticalResultDtoNow =
            sampleCriticalResultService.selectByCriticalValueId(vo.getCriticalValueId());
        if (Objects.isNull(sampleCriticalResultDtoNow)) {
            throw new LimsException("对应危急值数据不存在");
        }

        if (Objects.equals(sampleCriticalResultDtoNow.getStatus(),
            SampleCriticalResultStatusEnum.PROCESSED.getCode())) {
            throw new LimsException("危急值已处理");
        }


        if (Objects.equals(sampleCriticalResultDtoNow.getStatus(),
            SampleCriticalResultStatusEnum.UNDER_REVIEW.getCode())) {
            // 复查中 查看 报告项目复查状态

            SampleReportItemDto sampleReportItemDto = sampleReportItemService.selectBySampleIdAndReportItemCode(
                sampleCriticalResultDtoNow.getSampleId(), sampleCriticalResultDtoNow.getReportItemCode());
            if (Objects.isNull(sampleReportItemDto)) {
                throw new LimsException("危急值关联样本下报告项目不存在");
            }
            if (Objects.equals(sampleReportItemDto.getIsRetest(), YesOrNoEnum.YES.getCode())) {
                throw new LimsException("报告项目正在复查中");
            }

        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final SampleCriticalResultDto target = new SampleCriticalResultDto();

        target.setCriticalValueId(sampleCriticalResultDtoNow.getCriticalValueId());
        target.setSampleId(sampleCriticalResultDtoNow.getSampleId());
        target.setApplySampleId(sampleCriticalResultDtoNow.getApplySampleId());
        target.setReportItemId(sampleCriticalResultDtoNow.getReportItemId());
        target.setIsCritical(vo.getIsCritical());
        target.setHandleContent(vo.getHandleContent());
        target.setContactUserName(vo.getContactUserName());
        target.setContactPhone(vo.getContactPhone());

        target.setHandleDate(new Date());
        target.setHandleUserId(loginUser.getUserId());
        target.setHandleUserName(loginUser.getNickname());
        target.setStatus(SampleCriticalResultStatusEnum.PROCESSED.getCode());

        //新增回读
        target.setIsReadBack(vo.getIsReadBack());
        target.setRemark(vo.getRemark());
        target.setReadBackUser(loginUser.getNickname());
        target.setReadBackTime(new Date());

        //1.1.3新增对超时的判断处理
        judgeCriticalTimeOut(target);

        sampleCriticalResultService.updateByCriticalValueId(target);

        //处理环节
        insertReadBackFlow(sampleCriticalResultDtoNow,target);
        return Collections.emptyMap();
    }

    /**
     * 处理回读情况
     */
    @PostMapping("/read-back")
    public Object readBack(@RequestBody SampleCriticalReadBackVo vo) {
        if (Objects.isNull(vo.getCriticalValueId())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }

        SampleCriticalResultDto sampleCriticalResultDtoNow =
                sampleCriticalResultService.selectByCriticalValueId(vo.getCriticalValueId());
        if (Objects.isNull(sampleCriticalResultDtoNow)) {
            throw new LimsException("对应危急值数据不存在");
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final SampleCriticalResultDto target = new SampleCriticalResultDto();

        target.setCriticalValueId(sampleCriticalResultDtoNow.getCriticalValueId());
        target.setIsReadBack(vo.getIsReadBack());
        target.setRemark(vo.getRemark());
        target.setReadBackUser(loginUser.getNickname());
        target.setReadBackTime(new Date());

        sampleCriticalResultService.updateByCriticalValueId(target);

        // 回读 变化
        insertReadBackFlow(sampleCriticalResultDtoNow,target);

        return Collections.emptyMap();
    }

    /**
     * 危急值 信息 导出
     */
    @PostMapping("/export-critical-data")
    public Object exportCriticalData(@RequestBody Set<Long> criticalValueIds) {

        // 对应危急值 信息
        List<SampleCriticalResultDto> sampleCriticalResultDtos =
            sampleCriticalResultService.selectByCriticalValueIds(criticalValueIds);

        // 危急值对应 样本id List
        Set<Long> sampleIds =
            sampleCriticalResultDtos.stream().map(SampleCriticalResultDto::getSampleId).collect(Collectors.toSet());

        // 对应 所有样本信息
        List<SampleDto> sampleDtos = sampleService.selectBySampleIds(sampleIds);

        Set<Long> applySampleIds = sampleDtos.stream().map(SampleDto::getApplySampleId).collect(Collectors.toSet());
        Map<Long, ApplySampleDto> applySampleDtoMap = applySampleService.selectByApplySampleIdsAsMap(applySampleIds);

        // 对应 所有样本结果
        List<CriticalResultConnectSampleResultDto> sampleResultDtos = Lists.newArrayList();
        // 样本复查结果
        List<SampleRetestItemDto> sampleRetestItemDtos = sampleRetestItemService.selectBySampleIds(sampleIds);
        sampleResultDtos.addAll(sampleRetestItemDtos.stream()
            .filter(obj -> !Objects.equals(obj.getRetestMode(), RetestModeEnum.DEFAULT.getCode()))
            .map(CriticalResultConnectSampleResultDto::getBySampleRetestItemDto).collect(Collectors.toList()));
        // 样本报告结果 包含已删除的
        final List<SampleResultDto> sampleResultDtoList = sampleResultService.selectBySampleIds(sampleIds, true);

        // 将复查结果和结果添加到同一个集合中
        sampleResultDtos.addAll(sampleResultDtoList.stream()
                .filter(e -> {
                    Long applySampleId = e.getApplySampleId();
                    ApplySampleDto applySample = applySampleDtoMap.get(applySampleId);
                    return !Objects.isNull(applySample) && Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode());
                })
                // 过滤掉已删除的
                .filter(e -> Objects.equals(e.getIsDelete(), YesOrNoEnum.NO.getCode()))
                .map(CriticalResultConnectSampleResultDto::getBySampleResultDto).collect(Collectors.toList()));

        ExcelWriter excelWriter = null;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            // 头的策略
            WriteCellStyle headCellStyle = new WriteCellStyle();
            headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontName("微软雅黑");
            headWriteFont.setFontHeightInPoints((short)13);
            headCellStyle.setWriteFont(headWriteFont);

            // 内容策略
            WriteCellStyle contentCellStyle = new WriteCellStyle();
            // contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // contentCellStyle.setBorderLeft(BorderStyle.THIN);
            // contentCellStyle.setBorderTop(BorderStyle.THIN);
            // contentCellStyle.setBorderRight(BorderStyle.THIN);
            // contentCellStyle.setBorderBottom(BorderStyle.THIN);

            HorizontalCellStyleStrategy excelStyle = new HorizontalCellStyleStrategy(headCellStyle, contentCellStyle);


            // 导出结果对象， 合并规则， 头对象， 数据list
            final ExportResult exportResult = this.setMergeRuleAndFillExcelContent(sampleCriticalResultDtos, sampleDtos, sampleResultDtos, sampleResultDtoList);

            excelWriter = EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle)
                    .registerWriteHandler(new ExportCriticalDataMergeStrategy(exportResult.getMergeRuleList())).build();

            // 获取sheet对象
            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "危急值信息").head(exportResult.getHeaderList()).needHead(Boolean.TRUE).build();

            excelWriter.write(exportResult.getDataList(), sheet0);
        } catch (Exception e) {
            log.error("导出数据错误", e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            if (Objects.nonNull(excelWriter)) {
                excelWriter.finish();
            }
            try {
                out.close();
            } catch (IOException e) {
                log.error("关闭 流错误", e);
            }
        }
        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION,
                String.format("attachment; filename=%s", URLEncoder.encode("危急值信息.xlsx", StandardCharsets.UTF_8)))
            .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
            .body(data);
    }

    /**
     * 设置 合并规则 放置数据
     *
     * @param sampleCriticalResultDtos 危急值列表数据
     * @param sampleDtos 样本号-样本id
     * @param sampleResultAllDtos 复查结果 + 结果
     * @param sampleResultDtoList 样本结果（已删除和未删除）
     */
    private ExportResult setMergeRuleAndFillExcelContent(List<SampleCriticalResultDto> sampleCriticalResultDtos,
                                                 List<SampleDto> sampleDtos,
                                                         List<CriticalResultConnectSampleResultDto> sampleResultAllDtos,
                                                 List<SampleResultDto> sampleResultDtoList) {

        // 合并规则
        final List<CellRangeAddress> mergeRuleList = new ArrayList<>();
        //数据
        final List<List<Object>> dataList = Lists.newArrayList();
        // 表头
        final List<List<String>> headerList = Lists.newArrayList();

        // 结果
        final ExportResult exportResult = new ExportResult(mergeRuleList, dataList, headerList);

        //1.1.3新增判断是否超时
        //获取全局判断超时时间
        String timeOutHeader = "是否超时";
        int timeout = sampleCriticalResultService.getTimeoutConfig();
        if (timeout > 0){
            timeOutHeader = timeOutHeader+"(>"+timeout+"分钟)";
        }

        // 设置表头
        List<String> headList = Lists.newArrayList("状态", "条码号", "送检机构", "报告项目名称", "患者姓名", "性别", "年龄", "检验日期", "专业组",
                "是否危急值", "处理人", "处理内容", "处理时间", "联系人", "联系方式", "检验者", "一审人", "一审时间", "二审人", "二审时间", timeOutHeader, "是否回读", "备注", "回读填写人", "填写时间","样本号", "结果", "结果时间", "结果值类型", "结果来源");
        for (String item : headList) {
            headerList.add(List.of(item));
        }

        // 样本结果 以样本id 分组
        Map<Long, List<CriticalResultConnectSampleResultDto>> sampleResultGroupingBySampleId =
                sampleResultAllDtos.stream().collect(Collectors.groupingBy(CriticalResultConnectSampleResultDto::getSampleId));

        // 过滤掉样本
        // 样本id 和 样本结果 map，  包含已删除的
        final Map<String, SampleResultDto> sampleReusltMap = sampleResultDtoList.stream().collect(Collectors.toMap(e -> String.format(FORMAT_STR, e.getSampleId(), e.getSampleResultId()), Function.identity(), (a, b) -> a));

        // map key:样本id value:样本号
        Map<Long, String> sampleNoBySampleId =
                sampleDtos.stream().collect(Collectors.toMap(SampleDto::getSampleId, SampleDto::getSampleNo));

        Map<Long, SampleDto> sampleDtoMap = sampleDtos.stream().collect(Collectors.toMap(SampleDto::getSampleId, Function.identity(), (a, b) -> b));

        List<ExcelMergeRowsDto> toMergeRows = Lists.newArrayList();
        int endMergeColumn = 24;
        int startRow = 1;
        for (SampleCriticalResultDto criticalResultDto : sampleCriticalResultDtos) {
            Long sampleId = criticalResultDto.getSampleId();
            Long reportItemId = criticalResultDto.getReportItemId();
            SampleDto sampleDto = sampleDtoMap.get(criticalResultDto.getSampleId());
            if(Objects.nonNull(sampleDto)){
                criticalResultDto.setTesterName(sampleDto.getOneCheckerName());
                //新增一审、二审人的信息
                criticalResultDto.setOneCheckerName(sampleDto.getOneCheckerName());
                criticalResultDto.setOneCheckDate(sampleDto.getOneCheckDate());
                criticalResultDto.setTwoCheckerName(sampleDto.getTwoCheckerName());
                criticalResultDto.setTwoCheckDate(sampleDto.getTwoCheckDate());
            }
            // 样本号
            String sampleNo = ObjectUtils.defaultIfNull(sampleNoBySampleId.get(sampleId), "");

            // 样本结果
            List<CriticalResultConnectSampleResultDto> sampleResultDtoListAll =
                ObjectUtils.defaultIfNull(sampleResultGroupingBySampleId.get(sampleId), new ArrayList<>());

            // 原始结果
            final CriticalResultConnectSampleResultDto dto = new CriticalResultConnectSampleResultDto();
            dto.setSampleId(sampleId);
            dto.setReportItemId(criticalResultDto.getReportItemId());
            dto.setResultValue(criticalResultDto.getSampleResult());
            dto.setResultDate(criticalResultDto.getCreateDate());
            dto.setResultTypeName(SampleResultTypeEnum.ORIGIN.getDes());
            dto.setResultFromName(RetestModeEnum.FRONT.getDesc());
            // 获取样本结果， 包含已删除的
            final SampleResultDto sampleResult = sampleReusltMap.get(String.format(FORMAT_STR, criticalResultDto.getSampleId(), criticalResultDto.getSampleResultId()));
            if (Objects.nonNull(sampleResult) && !Objects.equals(sampleResult.getInstrumentId(), NumberUtils.LONG_ZERO)) {
                dto.setResultFromName(RetestModeEnum.MACHINE.getDesc());
            }
            sampleResultDtoListAll.add(0, dto);

            // 该样本结果 以报告项目 分组
            Map<Long, List<CriticalResultConnectSampleResultDto>> sampleResultListGroupingByReportItemId =
                sampleResultDtoListAll.stream()
                    .collect(Collectors.groupingBy(CriticalResultConnectSampleResultDto::getReportItemId));

            // 本危急值 对应相关样本结果
            List<CriticalResultConnectSampleResultDto> sampleResultDtos = ObjectUtils
                .defaultIfNull(sampleResultListGroupingByReportItemId.get(reportItemId), Collections.emptyList());
            // 排序 和页面展示保持一致
            List<CriticalResultConnectSampleResultDto> sortedSampleResultDtos = sampleResultDtos.stream()
                .sorted(Comparator.comparing(CriticalResultConnectSampleResultDto::getResultDate))
                .collect(Collectors.toList());

            // 没有样本结果
            if (CollectionUtils.isEmpty(sortedSampleResultDtos)) {
                List<Object> content = Lists.newArrayListWithCapacity(headList.size());
                excelContentFill(content, criticalResultDto, sampleNo, null);
                dataList.add(content);
                startRow += 1;
            } else {
                //有样本结果 需要先判断危急值是否处理，取出复查结果 并且判断是否超时
                if (timeout > 0 &&
                        Objects.equals(criticalResultDto.getStatus(),SampleCriticalResultStatusEnum.REVIEW.getCode())){
                    // 仅把复查的结果找出来 用于时间的判断
                    CriticalResultConnectSampleResultDto criticalResult =
                            sortedSampleResultDtos.stream()
                            .filter(e-> Objects.equals(e.getResultTypeName(), SampleResultTypeEnum.RETEST.getDes())).findFirst().orElse(null);
                    if (Objects.nonNull(criticalResult)){
                        boolean isTimeOut = System.currentTimeMillis() - criticalResult.getResultDate().getTime() > timeout * 60 * 1000L;
                        criticalResultDto.setIsTimeOut(isTimeOut?YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                    }
                }

                // 存在 多行样本结果 合并
                if (!Objects.equals(sortedSampleResultDtos.size(), 1)) {
                    ExcelMergeRowsDto mergeRowsDto = new ExcelMergeRowsDto();
                    mergeRowsDto.setStartRow(startRow);
                    mergeRowsDto.setEndRow(startRow + sortedSampleResultDtos.size() - 1);
                    toMergeRows.add(mergeRowsDto);
                }

                for (CriticalResultConnectSampleResultDto resultDto : sortedSampleResultDtos) {
                    List<Object> content = Lists.newArrayListWithCapacity(headList.size());
                    excelContentFill(content, criticalResultDto, sampleNo, resultDto);
                    dataList.add(content);
                    startRow += 1;
                }

            }
        }
        for (ExcelMergeRowsDto toMergeRow : toMergeRows) {
            for (int column = 0; column <= endMergeColumn; column++) {
                mergeRuleList.add(new CellRangeAddress(toMergeRow.getStartRow(), toMergeRow.getEndRow(), column, column));
            }
        }
        return exportResult;
    }

    /**
     * excel 导出 数据 填充
     *
     * @param content 数据 对象
     * @param criticalResultDto 危急值信息
     * @param sampleNo 样本号
     * @param resultDto 样本结果
     */
    private void excelContentFill(List<Object> content, SampleCriticalResultDto criticalResultDto, String sampleNo,
        CriticalResultConnectSampleResultDto resultDto) {
        content.add(SampleCriticalResultStatusEnum.getStatusByCode(criticalResultDto.getStatus()).getMsg());
        content.add(criticalResultDto.getBarcode());
        content.add(criticalResultDto.getHspOrgName());
        content.add(criticalResultDto.getReportItemName());
        content.add(criticalResultDto.getPatientName());
        content.add(SexEnum.getByCode(criticalResultDto.getPatientSex()).getDesc());
        content.add(PatientAges.toText(criticalResultDto));
        content.add(DateFormatUtils.format(criticalResultDto.getTestDate(), DATE_PATTERN));
        content.add(criticalResultDto.getGroupName());
        content.add(SampleCriticalResultIsCriticalEnum.getByCode(criticalResultDto.getIsCritical()).getMsg());
        content.add(criticalResultDto.getHandleUserName());
        content.add(criticalResultDto.getHandleContent());
        String handleDate = "";
        if (!DefaultDateEnum.DEFAULT_DATE.getDate().equals(criticalResultDto.getHandleDate())) {
            handleDate = DateFormatUtils.format(criticalResultDto.getHandleDate(), DATE_PATTERN);
        }
        content.add(handleDate);
        content.add(criticalResultDto.getContactUserName());
        content.add(criticalResultDto.getContactPhone());
        content.add(criticalResultDto.getTesterName());
        //新增一审人、一审时间 二审人 二审时间
        content.add(StringUtils.defaultString(criticalResultDto.getOneCheckerName(),StringUtils.EMPTY));
        content.add(Objects.isNull(LabwayDateUtil.getNonDefaultDbDate(criticalResultDto.getOneCheckDate())) ? null : DateFormatUtils.format(criticalResultDto.getOneCheckDate(), DATE_PATTERN));
        content.add(StringUtils.defaultString(criticalResultDto.getTwoCheckerName(),StringUtils.EMPTY));
        content.add(Objects.isNull(LabwayDateUtil.getNonDefaultDbDate(criticalResultDto.getTwoCheckDate())) ? null : DateFormatUtils.format(criticalResultDto.getTwoCheckDate(), DATE_PATTERN));
        //1.1.3新增 是否超时、是否回读、备注、回读填写人、填写时间
        content.add(Objects.isNull(criticalResultDto.getIsTimeOut())?null:YesOrNoEnum.selectByCode(criticalResultDto.getIsTimeOut()).getDesc());
        content.add(Objects.isNull(criticalResultDto.getIsReadBack())?null:ReadBackStatusEnum.selectByCode(criticalResultDto.getIsReadBack()).getValue());
        content.add(criticalResultDto.getRemark());
        content.add(criticalResultDto.getReadBackUser());
        content.add(Objects.isNull(LabwayDateUtil.getNonDefaultDbDate(criticalResultDto.getReadBackTime())) ? null : DateFormatUtils.format(criticalResultDto.getReadBackTime(), DATE_PATTERN));
        content.add(sampleNo);
        content.add(Objects.isNull(resultDto) ? null : resultDto.getResultValue());
        content.add(Objects.isNull(resultDto) ? null : DateFormatUtils.format(resultDto.getResultDate(), DATE_PATTERN));
        content.add(Objects.isNull(resultDto) ? null : ObjectUtils.defaultIfNull(resultDto.getResultTypeName(), SampleResultTypeEnum.DEFAULT.getDes()));
        content.add(Objects.isNull(resultDto) ? null : resultDto.getResultFromName());
    }

    /**
     * 判断危急值是否超时
     * @param resultDto
     */
    private void judgeCriticalTimeOut(SampleCriticalResultDto resultDto){
        if (resultDto != null){
            resultDto.setIsTimeOut(YesOrNoEnum.NO.getCode());
            //获取全局判断超时时间
            int timeout = sampleCriticalResultService.getTimeoutConfig();
            if (timeout > 0){
                resultDto.setTimeOutConfig(timeout);
                List<SampleRetestItemDto> retestItemDtos = sampleRetestItemService.selectBySampleIdAndReportItemId(resultDto.getSampleId(),resultDto.getReportItemId());
                resultDto.setIsTimeOut(isTimeOut(timeout,retestItemDtos)?YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
            }
        }
    }

    /**
     * 判断是否超时
     * @param timeOut
     * @param retestItemDtos
     */
    private boolean isTimeOut(int timeOut, List<SampleRetestItemDto> retestItemDtos){
        if (timeOut > 0 && CollectionUtils.isNotEmpty(retestItemDtos)){
            //判断逻辑为：当前时间 - 复查结果最新时间 > 超时配置时间
            //找到最新的结果 重新排序
            retestItemDtos.sort(Comparator.comparing(SampleRetestItemDto::getUpdateDate));
            return System.currentTimeMillis() - retestItemDtos.get(0).getUpdateDate().getTime() > timeOut * 60 * 1000L;
        }

        return false;
    }


    private void insertReadBackFlow(SampleCriticalResultDto sampleCriticalResultDtoNow, SampleCriticalResultDto target){
        // 回读 变化
        String change =
                new CompareUtils<SampleCriticalResultDto>().compare(sampleCriticalResultDtoNow, target);
        if (StringUtils.isNotBlank(change)) {
            final LoginUserHandler.User loginUser = LoginUserHandler.get();
            //这里需要填入条码环节
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setApplyId(sampleCriticalResultDtoNow.getApplyId());
            sampleFlow.setApplySampleId(sampleCriticalResultDtoNow.getApplySampleId());
            sampleFlow.setBarcode(sampleCriticalResultDtoNow.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.CRITICAL_RESULT_READBACK.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.CRITICAL_RESULT_READBACK.getDesc());
            sampleFlow.setOperator(loginUser.getNickname());
            sampleFlow.setOperatorId(loginUser.getUserId());
            sampleFlow.setContent(String.format("样本危急值回读:%s", change));
            // 添加流水
            sampleFlowService.addSampleFlow(sampleFlow);
        }
    }

    @Getter
    public final class ExportResult {

        // 合并规则
        private final List<CellRangeAddress> mergeRuleList;
        //数据
        private final List<List<Object>> dataList;
        // 表头
        private final List<List<String>> headerList;

        public ExportResult(List<CellRangeAddress> mergeRuleList, List<List<Object>> dataList, List<List<String>> headerList) {
            this.mergeRuleList = mergeRuleList;
            this.dataList = dataList;
            this.headerList = headerList;
        }
    }

}
