package com.labway.lims.routine.service.chain.audit;


import com.labway.lims.routine.api.dto.SampleAuditDto;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class RequestParamCommand implements Command {
    @Override
    public boolean execute(Context c) throws Exception {
        AuditSampleContext context = AuditSampleContext.from(c);

        final SampleAuditDto auditVo = context.getParam();

        Objects.requireNonNull(auditVo, "auditVo is null");

        return CONTINUE_PROCESSING;
    }
}
