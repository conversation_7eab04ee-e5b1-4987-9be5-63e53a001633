package com.labway.lims.routine.service.chain.result.retest;

import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import com.labway.lims.routine.service.chain.result.RecalculateRefResultCommand;
import com.labway.lims.routine.service.chain.retest.StartRetestUpdateMissItemCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 2023/5/24 16:34
 */
@Slf4j
@Component
public class CheckIsCompleteRetestCommand implements Command {

    @Resource
    private SampleRetestMainService sampleRetestMainService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private StartRetestUpdateMissItemCommand startRetestUpdateMissItemCommand;
    @Resource
    private RecalculateRefResultCommand recalculateRefResultCommand;
    @Resource
    private SampleRetestItemService sampleRetestItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final RetestResultContext context = RetestResultContext.from(c);

        if (recalculateRefResultCommand.isRecalculate(c)) {
            return CONTINUE_PROCESSING;
        }

        final ArrayList<SampleRetestItemDto> sampleRetestItems = new ArrayList<>(sampleRetestItemService
                .selectBySampleRetestMainId(context.getSampleRetestMain().getSampleRetestMainId()));
        context.put(RetestResultContext.SAMPLE_RETEST_ITEMS, new ArrayList<>(sampleRetestItems));
        if (CollectionUtils.isEmpty(sampleRetestItems)) {
            return CONTINUE_PROCESSING;
        }

        // 如果有一个结果为空 那就是还在复查中
        if (sampleRetestItems.stream().anyMatch(e -> StringUtils.isBlank(e.getResult()))) {
            return CONTINUE_PROCESSING;
        }


        final SampleRetestMainDto m = new SampleRetestMainDto();
        m.setSampleRetestMainId(context.getSampleRetestMain().getSampleRetestMainId());
        m.setStatus(SampleRetestStatusEnum.RETEST.getCode());
        sampleRetestMainService.updateBySampleRetestMainId(m);

        context.put(RetestResultContext.IS_COMPLETE, true);

        // 删除缺项
        stringRedisTemplate.delete(startRetestUpdateMissItemCommand.getMissItemKey(context.getSample().getSampleId()));

        return CONTINUE_PROCESSING;
    }

}
