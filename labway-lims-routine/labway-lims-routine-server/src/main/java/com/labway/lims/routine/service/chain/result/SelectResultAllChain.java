package com.labway.lims.routine.service.chain.result;

import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.routine.api.dto.ResultListDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 查询 所有 结果
 *
 * <AUTHOR>
 * @since 2023/9/12 15:37
 */
@Slf4j
@Component
public class SelectResultAllChain implements Command {

    @Resource
    private SampleRetestMainService sampleRetestMainService;
    @Resource
    private SampleRetestItemService sampleRetestItemService;
    @Resource
    private SampleResultService sampleResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        // 如果报告项目不是复查中或已复查 那么可以跳过
        if (Objects.equals(context.getSampleReportItem().getIsRetest(), RetestStatusEnum.NORMAL.getCode())) {
            return CONTINUE_PROCESSING;
        }

        Long sampleId = context.getSample().getSampleId();
        Long reportItemId = context.getReportItemId();
        String reportItemCode = context.getReportItemCode();

        // 该 报告项目 对应 复查结果
        List<SampleRetestItemDto> retestItemDtos =
            sampleRetestItemService.selectBySampleIdAndReportItemId(sampleId, reportItemId).stream()
                .filter(obj -> Objects.equals(obj.getReportItemCode(), reportItemCode)).collect(Collectors.toList());

        // 没有 复查 结果
        if (CollectionUtils.isEmpty(retestItemDtos)) {
            return CONTINUE_PROCESSING;
        }

        // 结果 对应 复查 样本 结果id
        Set<Long> sampleRetestMainIds =
            retestItemDtos.stream().map(SampleRetestItemDto::getSampleRetestMainId).collect(Collectors.toSet());

        // 原始结果
        Map<String, SampleResultDto> originalResults = sampleResultService.selectBySampleIds(sampleRetestMainIds)
            .stream().collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a));

        List<ResultListDto> targetList = Lists.newArrayList();

        // 先 放入 原始结果
        ResultListDto originalResult = new ResultListDto();
        originalResult.setReportItemCode(reportItemCode);
        originalResult.setContentType(1);
        SampleResultDto originalResultDto = originalResults.get(reportItemCode);
        // 原始结果
        if (Objects.nonNull(originalResultDto)) {
            originalResult.setContent(originalResultDto.getResult());
        }
        originalResult.setSort(0);
        targetList.add(originalResult);

        // 再 放入 复查结果
        for (int i = 0; i < retestItemDtos.size(); i++) {
            ResultListDto retestResult = new ResultListDto();
            retestResult.setReportItemCode(reportItemCode);
            retestResult.setContentType(2);
            retestResult.setContent(retestItemDtos.get(i).getResult());
            retestResult.setSort(i + 1);
            targetList.add(retestResult);
        }

        context.put(SaveResultContext.RESULT_ALL, targetList);

        return CONTINUE_PROCESSING;
    }
}
