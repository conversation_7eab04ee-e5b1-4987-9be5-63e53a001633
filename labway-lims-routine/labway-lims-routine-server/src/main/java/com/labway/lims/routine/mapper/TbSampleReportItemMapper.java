package com.labway.lims.routine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.model.TbSampleReportItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbSampleReportItemMapper extends BaseMapper<TbSampleReportItem> {

    int addBatch(@Param("items") List<TbSampleReportItem> items);

    int updateBySampleReportItemIds(@Param("sampleReportItem") SampleReportItemDto sampleReportItemDto
            , @Param("sampleReportItemIds") Collection<Long> sampleReportItemIds);

    List<TbSampleReportItem> selectBySampleIdsAndReportIds(@Param("dtos") List<SampleReportItemDto> dtos);


    void updateIsDeleteBySampleReportItemIds(@Param("sampleReportItemIds")Collection<Long> samplerReportItemIds,
                                              @Param("suffix")String suffix);
}
