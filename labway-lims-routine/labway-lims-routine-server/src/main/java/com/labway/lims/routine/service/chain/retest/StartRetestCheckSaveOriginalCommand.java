package com.labway.lims.routine.service.chain.retest;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/26 23:22
 */
@Slf4j
@Component
public class StartRetestCheckSaveOriginalCommand implements Filter, Command {
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private SampleResultService sampleResultService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        final StartRetestContext context = StartRetestContext.from(c);
        final List<SampleReportItemDto> retestSampleReportItems = context.getRetestSampleReportItems();
        final List<SampleRetestMainDto> retestMains = context.getSampleRetestMains();

        final Set<String> reportItemCodes = new HashSet<>();
        //整个样本复查
        if (retestSampleReportItems.size() > 1) {
            final List<SampleResultDto> sampleResults = sampleResultService.selectBySampleIds(retestMains.
                    stream().map(SampleRetestMainDto::getSampleRetestMainId).collect(Collectors.toList()));
            for (SampleReportItemDto e : retestSampleReportItems) {
                if (sampleResults.stream().anyMatch(k -> Objects.equals(e.getReportItemCode(), k.getReportItemCode()))) {
                    continue;
                }
                reportItemCodes.add(e.getReportItemCode());
            }

        } else {
            final List<SampleResultDto> sampleResults = sampleResultService.selectBySampleIdsAndReportItemCode(retestMains.stream()
                            .map(SampleRetestMainDto::getSampleRetestMainId).collect(Collectors.toList()),
                    retestSampleReportItems.iterator().next().getReportItemCode());
            //为空保存原始结果
            if (CollectionUtils.isNotEmpty(sampleResults)) {
                return CONTINUE_PROCESSING;
            }

            reportItemCodes.add(retestSampleReportItems.iterator().next().getReportItemCode());
        }

        if (CollectionUtils.isEmpty(reportItemCodes)) {
            return CONTINUE_PROCESSING;
        }


        final List<SampleResultDto> results = context.getSampleReportItemResults().stream()
                .filter(e -> reportItemCodes.contains(e.getReportItemCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(results)) {
            return CONTINUE_PROCESSING;
        }


        final SampleRetestMainDto main = context.getSampleRetestMains().stream().filter(e -> Objects.equals(e.getStatus(),
                SampleRetestStatusEnum.NORMAL.getCode())).findFirst().orElse(null);
        if (Objects.isNull(main)) {
            return CONTINUE_PROCESSING;
        }

        final LinkedList<Long> ids = snowflakeService.genIds(results.size());
        final List<SampleResultDto> rs = results.stream().map(e -> {
            final SampleResultDto d = new SampleResultDto();
            BeanUtils.copyProperties(e, d);
            d.setSampleResultId(ids.pop());
            d.setSampleId(main.getSampleRetestMainId());
            return d;
        }).collect(Collectors.toList());

        // 将原始结果存入到主表
        sampleResultService.addSampleResults(rs);


        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }


    public String getOriginalKey(Long sampleId) {
        return redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":ORIGINAL:" + sampleId;
    }
}
