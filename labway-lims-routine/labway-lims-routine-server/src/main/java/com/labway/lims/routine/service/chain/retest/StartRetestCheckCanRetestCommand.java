package com.labway.lims.routine.service.chain.retest;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.StartReTestDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/5/26 17:17
 */
@Slf4j
@Component
public class StartRetestCheckCanRetestCommand implements Filter, Command {

    @Override
    public boolean execute(Context c) throws Exception {
        final StartRetestContext context = StartRetestContext.from(c);
        final StartReTestDto reTestDto = context.getRetestInfo();
        final ApplySampleDto applySample = context.getApplySample();

        if (Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode()) ||
                Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) {
            throw new IllegalStateException("样本已审核，无法复查");
        }

        final List<SampleResultDto> results = context.getSampleReportItemResults();
        if (results.stream().allMatch(e -> StringUtils.isBlank(e.getResult()))) {
            throw new IllegalStateException("当前样本结果存在空值，不能进行复查");
        }

        // 如果是整个样本开始复查，校验结果是否全
        if (Objects.isNull(reTestDto.getReportItemId())) {
            for (SampleReportItemDto e : context.getSampleReportItems()) {
                if (context.getSampleReportItemResults().stream().noneMatch(k -> Objects.equals(k.getReportItemCode(),
                        e.getReportItemCode()))) {
                    throw new IllegalStateException(String.format("项目 [%s] 结果不存在，不能进行复查", e.getReportItemName()));
                }
            }
        } else {
            final SampleReportItemDto sampleReportItem = context.getSampleReportItems().stream().filter(e -> Objects.equals(e.getReportItemId(), reTestDto.getReportItemId()))
                    .findFirst().orElse(null);
            if (Objects.isNull(sampleReportItem)) {
                throw new IllegalStateException(String.format("项目 [%s] 不存在", reTestDto.getReportItemCode()));
            }

            if (context.getSampleReportItemResults().stream().noneMatch(k -> Objects.equals(k.getReportItemCode(),
                    sampleReportItem.getReportItemCode()))) {
                throw new IllegalStateException(String.format("项目 [%s] 结果不存在，不能进行复查",
                        sampleReportItem.getReportItemName()));
            }
        }

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

}
