package com.labway.lims.routine.service.chain.audit;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultTipDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/3 15:45
 */
@SuppressWarnings("unchecked")
@Getter
@Setter
public class AuditSampleContext extends StopWatchContext {

    /**
     * 二审用户
     */
    private UserDto twoCheckUser;

	/**
	 * 是否自动审核
	 */
	private boolean autoAudit = Boolean.FALSE;

    public static final String SAMPLE = "SAMPLE_" + IdUtil.objectId();

    public static final String PARAM = "PARAM_" + IdUtil.objectId();
    public static final String APPLY = "APPLY_" + IdUtil.objectId();
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();
    public static final String APPLY_SAMPLES = "APPLY_SAMPLES_" + IdUtil.objectId();
    public static final String SAMPLE_TEST_ITEMS = "SAMPLE_TEST_ITEMS_" + IdUtil.objectId();
    public static final String SAMPLE_REPORT_ITEMS = "SAMPLE_REPORT_ITEMS_" + IdUtil.objectId();
    public static final String INSTRUMENT_REPORT_ITEMS = "INSTRUMENT_REPORT_ITEMS_" + IdUtil.objectId();
    public static final String INSTRUMENT_REPORT_ITEM_TIPS = "INSTRUMENT_REPORT_ITEM_TIPS_" + IdUtil.objectId();
    public static final String SAMPLE_REPORT_ITEM_RESULTS = "SAMPLE_REPORT_ITEM_RESULTS_" + IdUtil.objectId();
    public static final String SAMPLE_REPORT_LIST = "SAMPLE_REPORT_LIST_" + IdUtil.objectId();
    public static final String REPORT_NO = "REPORT_NO_" + IdUtil.objectId();

    // 空参考范围结果
    public static final String EMPTY_REFERENCE_RESULT_WARNING = "EMPTY_REFERENCE_RESULT_WARNING" + IdUtil.objectId();
    public static final String EMPTY_REFERENCE_RESULT_FORBIDDEN = "EMPTY_REFERENCE_RESULT_FORBIDDEN" + IdUtil.objectId();

    public List<SampleReportDto> getSampleReports() {
        return (List<SampleReportDto>) get(SAMPLE_REPORT_LIST);
    }

    public SampleDto getSample() {
        return (SampleDto) get(SAMPLE);
    }

    public SampleAuditDto getParam() {
        return (SampleAuditDto) get(PARAM);
    }

    public ApplyDto getApply() {
        return (ApplyDto) get(APPLY);
    }

    public String getReportNo() {
        return (String) get(REPORT_NO);
    }


    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }

    public List<ApplySampleItemDto> getApplySampleItems() {
        return (List<ApplySampleItemDto>) get(SAMPLE_TEST_ITEMS);
    }

    public List<SampleReportItemDto> getSampleReportItems() {
        return (List<SampleReportItemDto>) get(SAMPLE_REPORT_ITEMS);
    }

    public List<InstrumentReportItemDto> getInstrumentReportItems() {
        return (List<InstrumentReportItemDto>) get(INSTRUMENT_REPORT_ITEMS);
    }

    public List<InstrumentReportItemResultTipDto> getSampleInstrumentReportItemTips() {
        return (List<InstrumentReportItemResultTipDto>) get(INSTRUMENT_REPORT_ITEM_TIPS);
    }

    public List<SampleResultDto> getSampleReportItemResults() {
        return (List<SampleResultDto>) get(SAMPLE_REPORT_ITEM_RESULTS);
    }

    public List<SampleResultDto> getEmptyReferenceResultWarning() {
        return (List<SampleResultDto>) computeIfAbsent(EMPTY_REFERENCE_RESULT_WARNING, key -> new ArrayList<SampleResultDto>());
    }

    public List<SampleResultDto> getEmptyReferenceResultForbidden() {
        return (List<SampleResultDto>) computeIfAbsent(EMPTY_REFERENCE_RESULT_FORBIDDEN, key -> new ArrayList<SampleResultDto>());
    }

    public static AuditSampleContext from(Context c) {
        return (AuditSampleContext) c;
    }

    public AuditSampleContext(SampleAuditDto dto) {
        put(PARAM, dto);
    }

    @Override

    protected String getWatchName() {
        return "条码审核";
    }
}
