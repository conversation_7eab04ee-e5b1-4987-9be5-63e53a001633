package com.labway.lims.routine.service.chain.result.qc;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/4 17:22
 */
@Component
public class AddQCSampleCommand implements Command {
    @Resource
    private SampleService sampleService;
    @Resource
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveQCResultContext context = SaveQCResultContext.from(c);
        SampleDto sample;
        //第一次仪器传结果
        if (Objects.isNull(context.getSample())) {
            sample = genSample(context);
            final long sampleId = sampleService.addSample(sample);
            context.setSampleId(sampleId);
        } else {
            sample = sampleService.selectBySampleId(context.getSampleId());
            if (Objects.isNull(sample)) {
                throw new IllegalStateException("样本不存在");
            }
        }
        final SampleReportItemDto sampleReportItem = sampleReportItemService.selectBySampleIdAndReportItemCode(sample.getSampleId(), context.getReportItemCode());
        if (Objects.isNull(sampleReportItem)){
            final SampleReportItemDto item = genSampleReportItem(context, sample);
            sampleReportItemService.addSampleReportItem(item);
        }
        context.put(SaveQCResultContext.SAMPLE, sample);

        return CONTINUE_PROCESSING;
    }

    private SampleDto genSample(SaveQCResultContext context) {
        final ProfessionalGroupDto group = groupService.selectByGroupId(context.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException("专业组不存在");
        }
        //添加样本
        final SampleDto sample = new SampleDto();
        sample.setSampleId(snowflakeService.genId());
        sample.setApplySampleId(NumberUtils.LONG_ZERO);
        sample.setApplyId(NumberUtils.LONG_ZERO);
        sample.setBarcode("");
        sample.setSampleNo(context.getSampleNo());
        sample.setGroupId(context.getGroupId());
        sample.setGroupName(group.getGroupName());
        sample.setInstrumentGroupId(NumberUtils.LONG_ZERO);
        sample.setInstrumentGroupName("");
        if (Objects.nonNull(context.getInstrument())) {
            sample.setInstrumentName(context.getInstrument().getInstrumentName());
        } else {
            sample.setInstrumentName("");
        }
        sample.setInstrumentId(context.getInstrumentId());
        sample.setTestDate(context.getTestDate());
        sample.setOneCheckerId(NumberUtils.LONG_ZERO);
        sample.setOneCheckerName("");
        sample.setOneCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        sample.setTwoCheckerId(NumberUtils.LONG_ZERO);
        sample.setTwoCheckerName("");
        sample.setTwoCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        sample.setHspOrgId(NumberUtils.LONG_ZERO);
        sample.setHspOrgName("");
        sample.setOrgId(LoginUserHandler.get().getOrgId());
        sample.setOrgName(LoginUserHandler.get().getOrgName());
        return sample;
    }

    private SampleReportItemDto genSampleReportItem(SaveQCResultContext context, SampleDto sample) {
        final SampleReportItemDto item = new SampleReportItemDto();
        item.setSampleId(context.getSampleId());
        item.setSampleReportItemId(snowflakeService.genId());
        item.setApplyId(sample.getApplyId());
        item.setApplySampleId(sample.getApplySampleId());
        item.setReportItemCode(StringUtils.defaultString(context.getReportItemCode()));
        item.setReportItemName(StringUtils.defaultString(context.getInstrumentReportItem().getReportItemName()));
        item.setReportItemId(NumberUtils.LONG_ZERO);
        item.setTestItemId(NumberUtils.LONG_ZERO);
        item.setTestItemCode("");
        item.setTestItemName("");
        item.setIsRetest(NumberUtils.INTEGER_ZERO);
        item.setPrintSort(context.getInstrumentReportItem().getPrintSort());
        return item;
    }
}
