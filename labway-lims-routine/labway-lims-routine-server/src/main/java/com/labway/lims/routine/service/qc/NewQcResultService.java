package com.labway.lims.routine.service.qc;

import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.vo.SampleReportItemDetailVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class NewQcResultService implements QcResultService{
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;

    @Override
    public String version() {
        return "2.0";
    }

    @Override
    public List<SampleReportItemDetailVo> qcResult(SampleDto sample) {
        final Map<String, InstrumentReportItemDto> instrumentReportItemDtoMap =
                instrumentReportItemService.selectByInstrumentId(sample.getInstrumentId()).stream()
                        .collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v -> v, (a, b) -> a));

        final Map<String, SampleReportItemDto> reportItemMap = sampleReportItemService.selectBySampleId(sample.getSampleId())
                .stream().collect(Collectors.toMap(SampleReportItemDto::getReportItemCode, v -> v, (a, b) -> a));
        final List<SampleResultDto> resultList = sampleResultService.selectBySampleId(sample.getSampleId());

        List<SampleReportItemDetailVo> list = new ArrayList<>();

        for (SampleResultDto result : resultList) {
            SampleReportItemDto item = reportItemMap.get(result.getReportItemCode());
            if (Objects.isNull(item)) {
                continue;
            }
            final SampleReportItemDetailVo vo = new SampleReportItemDetailVo();
            vo.setSampleReportItemId(item.getSampleReportItemId());
            vo.setReportItemId(item.getReportItemId());
            vo.setReportItemCode(item.getReportItemCode());
            vo.setReportItemName(item.getReportItemName());
            vo.setTestItemId(item.getTestItemId());
            vo.setTestItemName(item.getTestItemName());
            vo.setTestItemCode(item.getTestItemCode());
            vo.setResult(result.getResult());
            vo.setRange(result.getRange());
            vo.setUnit(result.getUnit());
            vo.setJudge(result.getJudge());
            vo.setInstrumentName(result.getInstrumentName());
            vo.setResentResult(new SampleReportItemDetailVo.ResentResult());
            final InstrumentReportItemDto instrumentReportItem = instrumentReportItemDtoMap.get(item.getReportItemCode());
            if (Objects.nonNull(instrumentReportItem)) {
                vo.setEnAb(instrumentReportItem.getEnAb());
                vo.setEnName(instrumentReportItem.getEnName());
                vo.setInstrumentName(instrumentReportItem.getInstrumentName());
            }
            vo.setStatus(result.getStatus());
            vo.setIsRetest(item.getIsRetest());
            vo.setPrintSort(instrumentReportItem.getPrintSort());
            list.add(vo);
        }

        return list;

    }
}
