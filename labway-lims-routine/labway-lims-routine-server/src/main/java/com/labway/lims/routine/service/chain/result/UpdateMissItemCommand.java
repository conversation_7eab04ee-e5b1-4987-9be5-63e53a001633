package com.labway.lims.routine.service.chain.result;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.routine.api.dto.ResultStatusDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/23 14:10
 */
@Slf4j
@Component
public class UpdateMissItemCommand implements Command {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        //收到结果的时候修改redis中的结果,以及结果状态
        final ResultStatusDto s = new ResultStatusDto();
        s.setReportItemCode(context.getReportItemCode());
        s.setResult(context.getResult());
        s.setJudge(context.getResultJudge());
        s.setIsRetest(YesOrNoEnum.NO.getCode());
        s.setIsException(context.isException() ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        s.setIsCritical(context.isCritical() ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());

        mark(context.getSample().getSampleId(), s);

        return CONTINUE_PROCESSING;
    }


    /**
     * 获取到缺失结果相
     */
    public String getMissItemKey(long sampleId) {
        return redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":SAMPLE_MISS_ITEM:" + sampleId;
    }

    public void mark(long sampleId, List<ResultStatusDto> marks) {
        mark(getMissItemKey(sampleId), marks);
    }

    public void mark(String key, List<ResultStatusDto> marks) {
        if (CollectionUtils.isEmpty(marks)) {
            return;
        }

        final Map<String, String> map = marks.stream()
                .collect(Collectors.toMap(ResultStatusDto::getReportItemCode, JSON::toJSONString, (a, b) -> a));
        stringRedisTemplate.opsForHash().putAll(key, map);
    }

    public void mark(long sampleId, ResultStatusDto m) {
        mark(sampleId, List.of(m));
    }

}
