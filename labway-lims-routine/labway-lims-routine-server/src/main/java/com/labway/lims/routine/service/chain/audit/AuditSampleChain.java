package com.labway.lims.routine.service.chain.audit;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AuditSample<PERSON>hain extends ChainBase implements InitializingBean {

	@Resource
	private AuditSampleLimitCommand auditSampleLimitCommand;
    @Resource
    private AdvanceGetDataCommand advanceGetDataCommand;
    @Resource
    private CombinedBillCommand combinedBillCommand;
    @Resource
    private BuildReportCommand buildReportCommand;
    @Resource
    private CheckSampleResultCommand checkSampleResultCommand;
    @Resource
    private EmptyReferenceTipCommand emptyReferenceTipCommand;
    @Resource
    private CheckSampleStatusCommand checkSampleStatusCommand;
    @Resource
    private RequestParamCommand requestParamCommand;
    @Resource
    private SampleAuditFlowCommand sampleAuditFlowCommand;
    @Resource
    private TagCacheDataCommand tagCacheDataCommand;
    @Resource
    private UpdateSampleAuditStatusCommand updateSampleAuditStatusCommand;
    @Resource
    private UpdateSampleInfoCommand updateSampleInfoCommand;

    @Resource
    private SampleAuditRabbitMqCommand sampleAuditRabbitMqCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
		// 限流
	    addCommand(auditSampleLimitCommand);
        // 参数校验
        addCommand(requestParamCommand);
        // 提前先获取数据
        addCommand(advanceGetDataCommand);
        // 判断是否可以二审
        addCommand(combinedBillCommand);
        // 检查样本状态
        addCommand(checkSampleStatusCommand);
        // 检查样本结果信息 为空 危急值...
        addCommand(checkSampleResultCommand);
        // 空参考范围提示
        addCommand(emptyReferenceTipCommand);
        // 生成样本报告
        addCommand(buildReportCommand);
        // 修改样本报告信息
        addCommand(updateSampleInfoCommand);
        // 标记缓存信息
        addCommand(tagCacheDataCommand);
        // 保存流水
        addCommand(sampleAuditFlowCommand);
        // 修改样本的审核状态
        addCommand(updateSampleAuditStatusCommand);
        // 发送审核消息到 MQ
        addCommand(sampleAuditRabbitMqCommand);

        addCommand(e -> PROCESSING_COMPLETE);
    }
}
