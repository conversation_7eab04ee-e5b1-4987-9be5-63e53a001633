package com.labway.lims.routine.service.chain.audit;

import com.labway.lims.api.RedisPrefix;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class TagCacheDataCommand implements Command {
    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context c) throws Exception {


        return CONTINUE_PROCESSING;
    }
}
