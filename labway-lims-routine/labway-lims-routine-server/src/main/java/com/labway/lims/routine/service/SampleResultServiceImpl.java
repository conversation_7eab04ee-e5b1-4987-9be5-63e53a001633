package com.labway.lims.routine.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.MachineSampleResultDto;
import com.labway.lims.routine.api.dto.MeiBiaoValueDTO;
import com.labway.lims.routine.api.dto.ReceiveMeiBiaoResultDTO;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SaveQCResultDto;
import com.labway.lims.routine.api.dto.SaveResultDto;
import com.labway.lims.routine.api.dto.SaveResultInfoDto;
import com.labway.lims.routine.api.dto.SimpleApplyDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.routine.mapper.TbSampleResultMapper;
import com.labway.lims.routine.model.TbSampleResult;
import com.labway.lims.routine.service.chain.result.InstrumentReportReferenceCommand;
import com.labway.lims.routine.service.chain.result.NumberResultCommand;
import com.labway.lims.routine.service.chain.result.SaveResultChain;
import com.labway.lims.routine.service.chain.result.SaveResultContext;
import com.labway.lims.routine.service.chain.result.qc.SaveQCResultContext;
import com.labway.lims.routine.service.chain.result.qc.SaveQcResultChain;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/29 17:26
 */
@Slf4j
@DubboService(interfaceClass = SampleResultService.class)
public class SampleResultServiceImpl extends ServiceImpl<TbSampleResultMapper, TbSampleResult> implements SampleResultService {
    @Resource
    private TbSampleResultMapper sampleResultMapper;
    @Resource
    private SaveResultChain saveResultChain;
    @Resource
    private SaveQcResultChain saveQcResultChain;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    @DubboReference
    private ApplyService applyService;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;
    @Resource
    private NumberResultCommand numberResultCommand;
    @Resource
    private SampleResultServiceImpl self;

    private final Map<String, RefreshReferenceStrategy> refreshReferenceStrategy;

    public SampleResultServiceImpl() {
        refreshReferenceStrategy = new HashMap<>();
        refreshReferenceStrategy.put(ItemTypeEnum.ROUTINE.name(), new RoutineStrategy());
        refreshReferenceStrategy.put(ItemTypeEnum.OUTSOURCING.name(), new RoutineStrategy());
        refreshReferenceStrategy.put(ItemTypeEnum.INFECTION.name(), new RoutineStrategy());
    }

    @Override
    public boolean updateBySampleResultId(SampleResultDto sampleResult) {
        final TbSampleResult result = JSON.parseObject(JSON.toJSONString(sampleResult), TbSampleResult.class);
        final LoginUserHandler.User user = LoginUserHandler.get();
        result.setUpdaterId(user.getUserId());
        result.setUpdaterName(user.getNickname());

        if (sampleResultMapper.update(result,
                new LambdaQueryWrapper<TbSampleResult>().eq(TbSampleResult::getSampleId, sampleResult.getSampleId())
                        .eq(TbSampleResult::getSampleResultId, sampleResult.getSampleResultId())) < 1) {
            return false;
        }
        log.info("用户 [{}] 更新结果成功 [{}]", user.getNickname(), JSON.toJSONString(sampleResult));
        return true;
    }

    @Override
    public boolean updateBySampleResultIds(List<SampleResultDto> sampleResults) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        for (SampleResultDto sampleResult : sampleResults) {
            final TbSampleResult result = JSON.parseObject(JSON.toJSONString(sampleResult), TbSampleResult.class);
            result.setUpdaterId(user.getUserId());
            result.setUpdaterName(user.getNickname());

            if (sampleResultMapper.update(result,
                    new LambdaQueryWrapper<TbSampleResult>().eq(TbSampleResult::getSampleId, sampleResult.getSampleId())
                            .eq(TbSampleResult::getSampleResultId, sampleResult.getSampleResultId())) < 1) {
                return false;
            }
        }
        log.info("用户 [{}] 更新结果成功 [{}]", user.getNickname(), JSON.toJSONString(sampleResults));
        return true;
    }

    @Nonnull
    @Override
    public List<SampleResultDto> selectBySampleId(long sampleId) {
        return sampleResultMapper
                .selectList(new LambdaQueryWrapper<TbSampleResult>().eq(TbSampleResult::getSampleId, sampleId)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Map<String, SampleResultDto> selectBySampleIdAsMap(long sampleId) {
        return selectBySampleId(sampleId).stream()
                .collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a));
    }

    @Override
    public Map<Long, List<SampleResultDto>> selectBySamplesIdAsMap(Collection<Long> sampleIds) {
        return self.selectBySampleIds(sampleIds)
                .stream().collect(Collectors.groupingBy(SampleResultDto::getSampleId));
    }

    @Nullable
    @Override
    public SampleResultDto selectBySampleResultId(long sampleResultId, long sampleId) {
        return convert(sampleResultMapper
                .selectOne(new LambdaQueryWrapper<TbSampleResult>().eq(TbSampleResult::getSampleId, sampleId)
                        .eq(TbSampleResult::getSampleResultId, sampleResultId).last("limit 1")));
    }

    @Nullable
    @Override
    public SampleResultDto selectBySampleResultId(long sampleResultId, long sampleId, boolean ignoreDelete) {
        if (ignoreDelete) {
            return convert(sampleResultMapper.selectBySampleResultIdIgnoreDelete(sampleResultId, sampleId));
        }
        return selectBySampleResultId(sampleResultId, sampleId);
    }

    @Override
    public SaveResultInfoDto saveResult(SaveResultDto result, SaveResultSourceEnum source) {
        Long applyId = result.getApplyId();
        Long sampleId = result.getSampleId();

        if (Objects.isNull(applyId) || Objects.isNull(sampleId)) {
            throw new IllegalStateException("缺少必填参数");
        }

        return saveResults(Collections.singletonList(result), source).iterator().next();
    }

    @Override
    public List<SaveResultInfoDto> saveResults(List<SaveResultDto> results, SaveResultSourceEnum source) {

        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }

        final List<SaveResultInfoDto> saveResultInfos = new ArrayList<>(results.size());
        final List<MachineSampleResultDto> machineSampleResults = new ArrayList<>();
        for (SaveResultDto dto : results) {
            // 质控保存结果
            if (Objects.equals(dto.getApplyId(), NumberUtils.LONG_ZERO)
                    && Objects.equals(dto.getApplySampleId(), NumberUtils.LONG_ZERO)) {

                final SaveQCResultDto qcResult = new SaveQCResultDto();
                qcResult.setIsQc(true);
                qcResult.setDate(dto.getDate());
                qcResult.setInstrumentId(dto.getInstrumentId());
                qcResult.setResult(dto.getResult());
                qcResult.setReportItemCode(dto.getReportItemCode());
                qcResult.setSampleNo(dto.getSampleNo());
                qcResult.setGroupId(dto.getGroupId());
                qcResult.setSource(source);
                return receiveQCResults(Collections.singleton(qcResult));
            }

            final SaveResultContext context = new SaveResultContext();
            context.setApplySampleId(dto.getApplySampleId());
            context.setResult(dto.getResult());
            context.setInstrumentResult(dto.getResult());
            context.setTestDate(dto.getDate());
            context.setInstrumentId(dto.getInstrumentId());
            context.setReportItemId(dto.getReportItemId());
            context.setReportItemCode(dto.getReportItemCode());
            context.setSource(source);
            context.setExtraInfo(dto.getExtraInfo());
            context.setIsHandeResult(dto.getIsHandeResult());
            context.setApplySampleUpdate(dto.isApplySampleUpdate());

            try {
                if (!saveResultChain.execute(context)) {
                    throw new IllegalStateException("修改结果失败");
                }
                // 记录仪器结果
                if (Objects.equals(source, SaveResultSourceEnum.MACHINE)) {
                    machineSampleResults.add(new MachineSampleResultDto(context.getSample(), context.getResult(),
                            context.getBeforeResult(), context.getInstrumentReportItem().getReportItemName()));
                }

                final SaveResultInfoDto saveResultInfo = new SaveResultInfoDto();
                saveResultInfo.setReportItemCode(dto.getReportItemCode());
                saveResultInfo.setResult(context.getResult());
                saveResultInfo.setBeforeResult(context.getBeforeResult());
                saveResultInfo.setJudge(context.getResultJudge());
                saveResultInfo.setException(context.isException());
                saveResultInfo.setCritical(context.isCritical());
                saveResultInfo.setReference(context.getInstrumentReportItemReference());
                saveResultInfo.setRetesting(context.isRetesting());
                saveResultInfo.setResultAll(context.getResultAll());

                saveResultInfos.add(saveResultInfo);

            } catch (RuntimeException e) {
                throw e;
            } catch (Exception e) {
                throw new LimsException(e.getMessage(), e);
            } finally {
                log.info("报告项目 [{}] 保存结果耗时\n{}", dto.getReportItemId(),
                        context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
            }

        }

        saveMachineResultFlow(machineSampleResults);

        return saveResultInfos;
    }

    @Override
    public List<SaveResultInfoDto> receiveQCResults(Collection<SaveQCResultDto> qcResults) {
        if (CollectionUtils.isEmpty(qcResults)) {
            return Collections.emptyList();
        }
        final List<SaveResultInfoDto> saveResultInfos = new ArrayList<>(qcResults.size());

        for (SaveQCResultDto dto : qcResults) {

            final SaveQCResultContext qcContext = new SaveQCResultContext();
            qcContext.setIsQc(dto.getIsQc());
            qcContext.setTestDate(dto.getDate());
            qcContext.setInstrumentId(dto.getInstrumentId());
            qcContext.setInstrumentResult(dto.getResult());
            qcContext.setResult(dto.getResult());
            qcContext.setReportItemCode(dto.getReportItemCode());
            qcContext.setSampleNo(dto.getSampleNo());
            qcContext.setSampleId(dto.getSampleId());
            qcContext.setGroupId(dto.getGroupId());
            qcContext.setSource(dto.getSource());
            qcContext.setExtraInfo(dto.getExtraInfo());
            try {
                if (!saveQcResultChain.execute(qcContext)) {
                    throw new IllegalStateException("修改质控结果失败");
                }

                final SaveResultInfoDto saveResultInfo = new SaveResultInfoDto();
                saveResultInfo.setResult(qcContext.getResult());
                saveResultInfo.setBeforeResult(qcContext.getBeforeResult());
                saveResultInfo.setJudge(qcContext.getResultJudge());
                saveResultInfo.setException(qcContext.isException());
                saveResultInfo.setCritical(qcContext.isCritical());
                saveResultInfo.setReference(qcContext.getInstrumentReportItemReference());

                saveResultInfos.add(saveResultInfo);

            } catch (RuntimeException e) {
                throw e;
            } catch (Exception e) {
                throw new LimsException(e.getMessage(), e);
            } finally {
                log.info("报告质控项目 [{}] 保存结果耗时\n{}", dto.getReportItemCode(),
                        qcContext.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
            }

        }
        return saveResultInfos;
    }

    @Override
    public boolean deleteBySampleResultId(long sampleResultId, long sampleId) {
        if (sampleResultMapper.delete(new LambdaQueryWrapper<TbSampleResult>().eq(TbSampleResult::getSampleId, sampleId)
                .eq(TbSampleResult::getSampleResultId, sampleResultId)) < 1) {
            return false;
        }
        log.info("用户 [{}] 删除结果成功 [{}]", LoginUserHandler.get().getNickname(), sampleResultId);
        return true;
    }

    @Override
    public boolean deleteBySampleResultIds(Collection<Long> sampleResultIds, long sampleId) {
        if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(sampleResultIds)) {
            if (sampleResultMapper.delete(new LambdaQueryWrapper<TbSampleResult>()
                    .eq(TbSampleResult::getSampleId, sampleId)
                    .in(TbSampleResult::getSampleResultId, sampleResultIds)) < 1) {
                return false;
            }
            log.info("用户 [{}] 删除结果成功 [{}]", LoginUserHandler.get().getNickname(), sampleResultIds);
        }
        return true;
    }

    @Override
    public boolean deleteByReportItemCode(long sampleId, String reportItemCode) {
        final LambdaQueryWrapper<TbSampleResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleResult::getSampleId, sampleId).eq(TbSampleResult::getReportItemCode, reportItemCode);

        if (sampleResultMapper.delete(wrapper) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除结果成功 样本 [{}] 报告项目 [{}]", LoginUserHandler.get().getNickname(), sampleId, reportItemCode);
        return true;
    }

    @Override
    public void deleteByReportItemCodes(long sampleId, Collection<String> reportItemCodes) {

        if (CollectionUtils.isEmpty(reportItemCodes)) {
            return;
        }

        final LambdaQueryWrapper<TbSampleResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleResult::getSampleId, sampleId).in(TbSampleResult::getReportItemCode, reportItemCodes);

        sampleResultMapper.delete(wrapper);

        log.info("用户 [{}] 删除结果成功 样本 [{}] 报告项目 [{}]", LoginUserHandler.get().getNickname(), sampleId, reportItemCodes);
    }

    @Override
    public void deleteByReportItemIds(long sampleId, Collection<Long> reportItemIds) {

        if (CollectionUtils.isEmpty(reportItemIds)) {
            return;
        }

        final LambdaQueryWrapper<TbSampleResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleResult::getSampleId, sampleId).in(TbSampleResult::getReportItemId, reportItemIds);

        if (sampleResultMapper.delete(wrapper) < 1) {
            return;
        }

        log.info("用户 [{}] 删除结果成功 样本 [{}] 报告项目 {}", LoginUserHandler.get().getNickname(), sampleId, reportItemIds);
    }

    @Override
    public boolean deleteBySampleId(long sampleId) {

        final LambdaQueryWrapper<TbSampleResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleResult::getSampleId, sampleId);

        if (sampleResultMapper.delete(wrapper) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除结果成功 样本 [{}]", LoginUserHandler.get().getNickname(), sampleId);

        return true;
    }

    @Override
    @Transactional
    public boolean deleteBySampleId(long sampleId, String operator) {

        final LambdaQueryWrapper<TbSampleResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleResult::getSampleId, sampleId);

        if (sampleResultMapper.delete(wrapper) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除结果成功 样本 [{}]", operator, sampleId);

        return true;
    }

    @Override
    public void deleteBySampleIds(Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return;
        }

        final LambdaQueryWrapper<TbSampleResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbSampleResult::getSampleId, sampleIds);

        sampleResultMapper.delete(wrapper);

        log.info("用户 [{}] 删除结果成功 样本 [{}]", LoginUserHandler.get().getNickname(), sampleIds);

    }

    @Override
    public void addSampleResults(Collection<SampleResultDto> sampleResults) {
        if (CollectionUtils.isEmpty(sampleResults)) {
            return;
        }
        final LoginUserHandler.User user = LoginUserHandler.get();
        final LinkedList<Long> ids = snowflakeService.genIds(sampleResults.size());
        for (SampleResultDto dto : sampleResults) {
            dto.setSampleResultId(ObjectUtils.defaultIfNull(dto.getSampleResultId(), ids.pop()));
            dto.setCreateDate(new Date());
            dto.setCreatorId(user.getUserId());
            dto.setCreatorName(user.getNickname());
            dto.setUpdateDate(new Date());
            dto.setUpdaterId(user.getUserId());
            dto.setUpdaterName(user.getNickname());
            dto.setIsDelete(YesOrNoEnum.NO.getCode());
        }
        sampleResultMapper.addBatch(sampleResults);
    }

    @Override
    @Transactional
    public void addSampleResults(Collection<SampleResultDto> sampleResults, String operator) {
        if (CollectionUtils.isEmpty(sampleResults)) {
            return;
        }
        final LinkedList<Long> ids = snowflakeService.genIds(sampleResults.size());
        for (SampleResultDto dto : sampleResults) {
            dto.setSampleResultId(ObjectUtils.defaultIfNull(dto.getSampleResultId(), ids.pop()));
            dto.setCreateDate(new Date());
            dto.setCreatorId(0L);
            dto.setCreatorName(operator);
            dto.setUpdateDate(new Date());
            dto.setUpdaterId(0L);
            dto.setUpdaterName(operator);
            dto.setIsDelete(YesOrNoEnum.NO.getCode());
        }
        sampleResultMapper.addBatch(sampleResults);
    }

    @Override
    public List<SampleResultDto> selectBySampleIdsAndReportItemId(Collection<Long> sampleIds, long reportItemId) {

        if (CollectionUtils.isEmpty(sampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSampleResult::getSampleId, sampleIds);
        queryWrapper.eq(TbSampleResult::getReportItemId, reportItemId);
        return convert(sampleResultMapper.selectList(queryWrapper));
    }

    @Override
    public List<SampleResultDto> selectBySampleIdsAndReportItemCode(Collection<Long> sampleIds, String reportItemCode) {
        if (CollectionUtils.isEmpty(sampleIds) || StringUtils.isBlank(reportItemCode)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSampleResult::getSampleId, sampleIds);
        queryWrapper.eq(TbSampleResult::getReportItemCode, reportItemCode);
        return convert(sampleResultMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refreshReference(SimpleApplyDto simpleApply) {

        final ApplyDto apply = JSON.parseObject(JSON.toJSONString(simpleApply), ApplyDto.class);

        if (Objects.isNull(apply) || Objects.isNull(apply.getApplyId())) {
            throw new IllegalStateException("申请单不存在");
        }

        final Long applyId = simpleApply.getApplyId();
        final List<Integer> auditStatus =
                List.of(SampleStatusEnum.AUDIT.getCode(), SampleStatusEnum.ONE_AUDIT.getCode());
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplyId(applyId).stream()
                .filter(f -> Objects.equals(f.getIsTwoPick(), YesOrNoEnum.YES.getCode())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(applySamples)) {
            return;
        }

        for (final ApplySampleDto applySample : applySamples) {
            final Integer status = applySample.getStatus();
            // 一审 和 已审 不刷新
            /*if (auditStatus.contains(status)) {
                return;
            }*/

            final RefreshReferenceStrategy strategy = refreshReferenceStrategy.get(applySample.getItemType());
            if (Objects.isNull(strategy)) {
                return;
            }

            final List<SampleResultDto> updateResults = strategy.refreshReference(apply, applySample);
            if (CollectionUtils.isEmpty(updateResults)) {
                return;
            }

            for (final SampleResultDto updateResult : updateResults) {
                sampleResultService.updateBySampleResultId(updateResult);
            }
        }

        log.info("申请单 id：[{}] 年龄：[{}]", apply.getApplyId(), apply.getPatientAge());
    }

    @Override
    public long countSampleResultCriticalQuantity(long sampleId) {
        LambdaQueryWrapper<TbSampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSampleResult::getSampleId, sampleId);
        queryWrapper.eq(TbSampleResult::getStatus, ResultStatusEnum.CRISIS.getCode());

        return sampleResultMapper.selectCount(queryWrapper);
    }

    @Override
    public List<SampleResultDto> selectBySampleIds(Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSampleResult::getSampleId, sampleIds);
        queryWrapper.eq(TbSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        return convert(sampleResultMapper.selectList(queryWrapper));
    }

    @Override
    public List<SampleResultDto> selectBySampleIds(Collection<Long> sampleIds, boolean ignoreDelete) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return Collections.emptyList();
        }

        if (ignoreDelete) {
            return convert(sampleResultMapper.selectIgnoreDelete(sampleIds));
        }
        return this.selectBySampleIds(sampleIds);
    }

    @Nullable
    @Override
    public SampleResultDto selectBySampleIdAndReportItemId(long sampleId, long reportItemId) {
        LambdaQueryWrapper<TbSampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSampleResult::getSampleId, sampleId);
        queryWrapper.eq(TbSampleResult::getReportItemId, reportItemId);
        queryWrapper.eq(TbSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");
        return convert(sampleResultMapper.selectOne(queryWrapper));
    }

    @Nullable
    @Override
    public SampleResultDto selectBySampleIdAndReportItemCode(long sampleId, String reportItemCode) {
        if (StringUtils.isBlank(reportItemCode)) {
            return null;
        }

        final LambdaQueryWrapper<TbSampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSampleResult::getSampleId, sampleId);
        queryWrapper.eq(TbSampleResult::getReportItemCode, reportItemCode);
        queryWrapper.eq(TbSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");
        return convert(sampleResultMapper.selectOne(queryWrapper));
    }

    @Override
    public long addSampleResult(SampleResultDto dto) {
        final TbSampleResult sampleResult = JSON.parseObject(JSON.toJSONString(dto), TbSampleResult.class);


        sampleResult.setSampleResultId(Optional.ofNullable(dto.getSampleResultId()).orElseGet(() -> snowflakeService.genId()));
        sampleResult.setCreateDate(new Date());
        sampleResult.setUpdateDate(new Date());
        sampleResult.setCreatorId(LoginUserHandler.get().getUserId());
        sampleResult.setCreatorName(LoginUserHandler.get().getNickname());
        sampleResult.setUpdaterId(LoginUserHandler.get().getUserId());
        sampleResult.setUpdaterName(LoginUserHandler.get().getNickname());
        sampleResult.setIsDelete(YesOrNoEnum.NO.getCode());
        if (sampleResultMapper.insert(sampleResult) < 1) {
            throw new IllegalStateException("添加结果失败");
        }

        log.info("用户 [{}] 添加样本结果成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(sampleResult));

        return sampleResult.getSampleResultId();
    }

    private SampleResultDto convert(TbSampleResult result) {
        if (Objects.isNull(result)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(result), SampleResultDto.class);
    }

    /**
     * TbSampleResult 转换 为 SampleResultDto
     *
     * @param list TbSampleResult
     * @return SampleResultDto
     */
    private List<SampleResultDto> convert(List<TbSampleResult> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }

    /**
     * 保存流水
     */
    private void saveMachineResultFlow(List<MachineSampleResultDto> machineSampleResults) {

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(machineSampleResults)) {
            return;
        }

        machineSampleResults =
                machineSampleResults.stream().filter(i -> Objects.nonNull(i.getSample())).collect(Collectors.toList());

        final MachineSampleResultDto resultDto = machineSampleResults.stream().findFirst().orElse(null);

        if (Objects.isNull(resultDto)) {
            return;
        }

        final SampleDto sample = resultDto.getSample();

        final SampleFlowDto sampleFlowDto = new SampleFlowDto();
        sampleFlowDto.setSampleFlowId(snowflakeService.genId());
        sampleFlowDto.setApplyId(sample.getApplyId());
        sampleFlowDto.setApplySampleId(sample.getApplySampleId());
        sampleFlowDto.setBarcode(sample.getBarcode());
        sampleFlowDto.setOperateCode(BarcodeFlowEnum.RESULT_UPDATE.name());
        sampleFlowDto.setOperateName(BarcodeFlowEnum.RESULT_UPDATE.getDesc());
        sampleFlowDto.setOperator("仪器");
        sampleFlowDto.setOperatorId(99L);
        sampleFlowDto.setOrgName(LoginUserHandler.get().getOrgName());
        sampleFlowDto.setOrgId(LoginUserHandler.get().getOrgId());
        sampleFlowDto.setCreateDate(new Date());
        sampleFlowDto.setCreatorId(LoginUserHandler.get().getUserId());
        sampleFlowDto.setCreatorName(LoginUserHandler.get().getNickname());
        sampleFlowDto.setUpdateDate(new Date());
        sampleFlowDto.setUpdaterName(LoginUserHandler.get().getNickname());
        sampleFlowDto.setUpdaterId(LoginUserHandler.get().getUserId());
        sampleFlowDto.setIsDelete(YesOrNoEnum.NO.getCode());

        StringBuilder sbContent = new StringBuilder();

        for (MachineSampleResultDto machineSampleResult : machineSampleResults) {

            final String testResult = machineSampleResult.getTestResult();

            final String beforeResult = machineSampleResult.getBeforeResult();

            final String customerReportItemName = machineSampleResult.getReportItemName();

            if (StringUtils.isBlank(beforeResult)) {
                sbContent.append(String.format("报告项目 [%s] 新增结果 [%s] \n", customerReportItemName, testResult));

            } else {
                sbContent.append(
                        String.format("报告项目 [%s] 结果 从 [%s] 修改成 [%s] \n", customerReportItemName, beforeResult, testResult));
            }

            sbContent.append(" (仪器)");

        }

        sampleFlowDto.setContent(sbContent.toString());

        sampleFlowService.addSampleFlow(sampleFlowDto);
    }

    /**
     * 刷新参考范围
     */
    public interface RefreshReferenceStrategy {
        List<SampleResultDto> refreshReference(ApplyDto apply, ApplySampleDto applySample);
    }

    public class RoutineStrategy implements RefreshReferenceStrategy {

        @Override
        public List<SampleResultDto> refreshReference(ApplyDto apply, ApplySampleDto applySample) {
            final Long applySampleId = applySample.getApplySampleId();
            if (Objects.isNull(applySampleId)) {
                return List.of();
            }

            Long sampleId = null;
            Long instrumentId = null;
            switch (ItemTypeEnum.getByName(applySample.getItemType())) {
                case ROUTINE:
                    final SampleDto sample = sampleService.selectByApplySampleId(applySampleId);
                    if (Objects.nonNull(sample)) {
                        sampleId = sample.getSampleId();
                        instrumentId = sample.getInstrumentId();
                    }
                    break;
                case OUTSOURCING:
                    final OutsourcingSampleDto outsourcingSample =
                            SpringUtil.getBean(OutsourcingSampleService.class).selectByApplySampleId(applySampleId);
                    if (Objects.nonNull(outsourcingSample)) {
                        sampleId = outsourcingSample.getOutsourcingSampleId();
                        instrumentId = outsourcingSample.getInstrumentId();
                    }

                    break;
                case INFECTION:
                    final InfectionSampleDto infectionSample =
                            SpringUtil.getBean(InfectionSampleService.class).selectByApplySampleId(applySampleId);
                    if (Objects.nonNull(infectionSample)) {
                        sampleId = infectionSample.getInfectionSampleId();
                        instrumentId = infectionSample.getInstrumentId();
                    }
                    break;
                default:
                    //
                    break;
            }

            if (Objects.isNull(sampleId)) {
                return List.of();
            }

            // 获取当前样本的结果
            final List<SampleResultDto> sampleResults = selectBySampleId(sampleId);
            if (CollectionUtils.isEmpty(sampleResults)) {
                return List.of();
            }

            final Set<String> reportItemCodes =
                    sampleResults.stream().map(SampleResultDto::getReportItemCode).collect(Collectors.toSet());
            final Map<String, List<InstrumentReportItemReferenceDto>> referenceMap =
                    instrumentReportItemReferenceService.selectByInstrumentIdAndReportItemCodesAsMap(instrumentId,
                            reportItemCodes);

            List<SampleResultDto> updateResults = new ArrayList<>();

            for (final SampleResultDto sampleResult : sampleResults) {
                final String reportItemCode = sampleResult.getReportItemCode();

                // 获取这个报告项目符合的参考范围
                final InstrumentReportItemReferenceDto reference =
                        ObjectUtils.defaultIfNull(
                                instrumentReportReferenceCommand.filterCustomerReportReference(apply, applySample,
                                        referenceMap.get(sampleResult.getReportItemCode())),
                                new InstrumentReportItemReferenceDto());

                // cn > en > cnEn
                String range = StringUtils.defaultString(StringUtils.defaultString(reference.getCnRefereValue(),
                        StringUtils.defaultString(reference.getEnRefereValue(), reference.getCnEnRefereValue())));

                if (StringUtils.isNotBlank(reference.getReferValueMin()) && StringUtils.isNotBlank(reference.getReferValueMax())) {
                    range = reference.getReferValueMin() + "~" + reference.getReferValueMax();
                } else if (StringUtils.isNotBlank(reference.getReferValueMin())) {
                    range = InstrumentReportItemReferenceDto.SymbolEnum.getSymbol(reference.getReferValueMinFormula()) + reference.getReferValueMin();
                } else if (StringUtils.isNotBlank(reference.getReferValueMax())) {
                    range = InstrumentReportItemReferenceDto.SymbolEnum.getSymbol(reference.getReferValueMaxFormula()) + reference.getReferValueMax();
                }

                SampleResultDto result = new SampleResultDto();
                result.setSampleId(sampleResult.getSampleId());
                result.setInstrumentReportItemReferenceId(reference.getInstrumentReportItemReferenceId());
                result.setSampleResultId(sampleResult.getSampleResultId());
                result.setRange(range);

                // 如果是数字并且不是院感
                if (BooleanUtils.isFalse(Objects.equals(applySample.getItemType(), ItemTypeEnum.INFECTION.name()))
                        && NumberUtils.isParsable(sampleResult.getResult())) {
                    // 报告项目
                    final InstrumentReportItemDto instrumentReportItem =
                            instrumentReportItemService.selectByInstrumentIdAndReportItemCode(instrumentId, reportItemCode);
                    if (Objects.isNull(instrumentReportItem)) {
                        return sampleResults;
                    }

                    // 是否危机
                    final boolean isCritical = numberResultCommand
                            .checkIsCritical(new BigDecimal(sampleResult.getResult()), instrumentReportItem, reference);
                    if (BooleanUtils.isTrue(isCritical)) {
                        sampleResult.setStatus(ResultStatusEnum.CRISIS.getCode());
                    }

                    // 是否异常
                    if (BooleanUtils.isFalse(isCritical)) {
                        final boolean isException = numberResultCommand.checkIsException(
                                new BigDecimal(sampleResult.getResult()), instrumentReportItem, reference);
                        if (BooleanUtils.isTrue(isException)) {
                            sampleResult.setStatus(ResultStatusEnum.EXCEPTION.getCode());
                        }
                    }
                }

                updateResults.add(result);
            }

            return updateResults;
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean receiveMeiBiaoResult(List<ReceiveMeiBiaoResultDTO> results) {
        // 查看是否已审

        // 查看样本信息
        List<SampleReportItemDto> sampleReportItemDtos = sampleReportItemService.selectBySampleIdsAndReportCodes(JSON.parseArray(JSON.toJSONString(results), SampleReportItemDto.class));

        List<Long> applysampleIds = sampleReportItemDtos.stream().map(SampleReportItemDto::getApplySampleId).collect(Collectors.toList());

        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applysampleIds);
        Map<Long, ApplySampleDto> applySampleDtoMap = applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity(), (a,b) -> a));

        Map<String, SampleReportItemDto> sampleIdMap = sampleReportItemDtos.stream().collect(Collectors.toMap(e -> e.getSampleId() + e.getReportItemCode(), Function.identity(), (a,b) -> a));


        Iterator<ReceiveMeiBiaoResultDTO> iterator = results.iterator();
        while (iterator.hasNext()) {
            ReceiveMeiBiaoResultDTO result = iterator.next();
            SampleReportItemDto appSample = sampleIdMap.get(result.getSampleId() + result.getReportItemCode());

            if (Objects.isNull(appSample)) {
                iterator.remove();
                continue;
            }
            ApplySampleDto applySampleDto = applySampleDtoMap.get(appSample.getApplySampleId());

            if (Objects.isNull(applySampleDto) || applySampleDto.getStatus().equals(30) || applySampleDto.getStatus().equals(20)) {
                iterator.remove();
                continue;
            }
            result.setReportItemId(appSample.getReportItemId());
            result.setReportItemName(appSample.getReportItemName());
            result.setTestItemId(appSample.getTestItemId());
            result.setTestItemCode(appSample.getTestItemCode());
            result.setTestItemName(appSample.getTestItemName());
            result.setApplyId(appSample.getApplyId());
            result.setApplySampleId(appSample.getApplySampleId());
        }

        List<SampleResultDto> sampleResultDtos = sampleResultService.selectBySampleIdsAndReportItemCodes(results);
        Map<String, Long> sampleResultIdMap = sampleResultDtos.stream().collect(Collectors.toMap(e -> e.getSampleId() + e.getReportItemCode(), SampleResultDto::getSampleResultId, (a,b)->a));

        Map<Boolean, List<ReceiveMeiBiaoResultDTO>> sampleResultMap = results.stream().collect(Collectors.groupingBy(e -> sampleResultIdMap.get(e.getSampleId() + e.getReportItemCode()) != null));

        //已存在数据
        List<ReceiveMeiBiaoResultDTO> receiveMeiBiaoResultDTOS = sampleResultMap.get(Boolean.TRUE);
        if(CollectionUtils.isNotEmpty(receiveMeiBiaoResultDTOS)) {

            for (ReceiveMeiBiaoResultDTO dto : receiveMeiBiaoResultDTOS) {
                LambdaUpdateWrapper<TbSampleResult> wrapper = Wrappers.lambdaUpdate(TbSampleResult.class)
                        .eq(TbSampleResult::getSampleId, dto.getSampleId())
                        .eq(TbSampleResult::getReportItemCode, dto.getReportItemCode())
                        .set(TbSampleResult::getResult, dto.getResultValue())
                        .set(TbSampleResult::getExtraInfo, JSON.toJSONString(BeanUtil.toBean(dto,MeiBiaoValueDTO.class)));
                this.update(wrapper);
            }
        }

        receiveMeiBiaoResultDTOS = sampleResultMap.get(Boolean.FALSE);
        // 不存在数据， 新增
        if(CollectionUtils.isNotEmpty(receiveMeiBiaoResultDTOS)) {
            List<SaveResultDto> saveResultDtos = receiveMeiBiaoResultDTOS.stream().map(dto->{
                SaveResultDto saveResultDto = new SaveResultDto();
                saveResultDto.setSampleId(dto.getSampleId());
                saveResultDto.setSampleNo(dto.getSampleNo());
                saveResultDto.setResult(dto.getResultValue());
                saveResultDto.setInstrumentId(dto.getInstrumentId());
                saveResultDto.setInstrumentName(Strings.EMPTY);
                saveResultDto.setReportItemId(dto.getReportItemId());
                saveResultDto.setReportItemCode(dto.getReportItemCode());
                saveResultDto.setTestItemId(dto.getTestItemId());
                saveResultDto.setApplyId(dto.getApplyId());
                saveResultDto.setApplySampleId(dto.getApplySampleId());
                saveResultDto.setExtraInfo(JSON.toJSONString(BeanUtil.toBean(dto,MeiBiaoValueDTO.class)));
                return saveResultDto;
            }).collect(Collectors.toList());

            this.saveResults(saveResultDtos, SaveResultSourceEnum.MEI_BIAO);
        }
        return true;
    }

    @Override
    public List<SampleResultDto> selectBySampleIdsAndReportItemCodes(List<ReceiveMeiBiaoResultDTO> results) {
        if (org.springframework.util.CollectionUtils.isEmpty(results)) return Collections.emptyList();

        List<Long> sampleIds = results.stream().map(ReceiveMeiBiaoResultDTO::getSampleId).collect(Collectors.toList());

        Map<Long, List<ReceiveMeiBiaoResultDTO>> sampleIdMap = results.stream().collect(Collectors.groupingBy(ReceiveMeiBiaoResultDTO::getSampleId));

        LambdaQueryWrapper<TbSampleResult> wrapper = Wrappers.lambdaQuery(TbSampleResult.class)
                .in(TbSampleResult::getSampleId, sampleIds);

        List<TbSampleResult> tbSampleReportItems = sampleResultMapper.selectList(wrapper);

        return tbSampleReportItems.stream().filter(e -> {
            Long sampleId = e.getSampleId();
            List<ReceiveMeiBiaoResultDTO> sampleReportItemDtos = sampleIdMap.get(sampleId);

            if(CollectionUtils.isEmpty(sampleReportItemDtos)) return false;

            List<String> reportCodes = sampleReportItemDtos.stream().map(ReceiveMeiBiaoResultDTO::getReportItemCode).collect(Collectors.toList());

            return reportCodes.contains(e.getReportItemCode());
        }).map(this::convert).collect(Collectors.toList());


//        return sampleResultMapper.selectBySampleIdsAndReportItemIds(results).stream().map(this::convert).collect(Collectors.toList());
    }

}
