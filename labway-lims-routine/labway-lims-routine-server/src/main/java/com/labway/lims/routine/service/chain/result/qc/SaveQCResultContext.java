package com.labway.lims.routine.service.chain.result.qc;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import javax.annotation.Nullable;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/3/30 13:38
 */
@Getter
@Setter
public class SaveQCResultContext extends StopWatchContext {

    /**
     * 样本检验项目
     */
    static final String SAMPLE_TEST_ITEMS = "SAMPLE_TEST_ITEMS_" + IdUtil.objectId();

    /**
     * 质控样本
     */
    static final String SAMPLE = "SAMPLE_" + IdUtil.objectId();

    /**
     * 结果报告项目
     */
    static final String INSTRUMENT_REPORT_ITEM = "INSTRUMENT_REPORT_ITEM_" + IdUtil.objectId();
//
//    /**
//     * 报告项目
//     */
//    static final String REPORT_ITEM = "REPORT_ITEM_" + IdUtil.objectId();

    /**
     * 仪器报告项目
     */
    static final String INSTRUMENT_REPORT_ITEMS = "INSTRUMENT_REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 仪器
     */
    static final String INSTRUMENT = "INSTRUMENT_" + IdUtil.objectId();

    /**
     * 仪器参考值
     */
    public static final String INSTRUMENT_REPORT_REFERENCE = "INSTRUMENT_REPORT_REFERENCE_" + IdUtil.objectId();

    /**
     * 结果转换
     */
    static final String RESULT_EXCHANGE = "RESULT_EXCHANGE_" + IdUtil.objectId();

    /**
     * 结果提示
     */
    public static final String RESULT_JUDGE = "RESULT_JUDGE_" + IdUtil.objectId();

    /**
     * 结果是否异常
     */
    public static final String RESULT_IS_EXCEPTION = "RESULT_IS_EXCEPTION_" + IdUtil.objectId();

    /**
     * 结果是否危机
     */
    public static final String RESULT_IS_CRITICAL = "RESULT_IS_CRITICAL_" + IdUtil.objectId();

    /**
     * 修改前结果
     */
    static final String BEFORE_RESULT = "BEFORE_RESULT_" + IdUtil.objectId();

    /**
     * RESULT_PROVIDER
     */
    static final String RESULT_PROVIDER = "RESULT_PROVIDER_" + IdUtil.objectId();
    /**
     * 当前结果
     */
    static final String CONTEXT_RESULTS = "CONTEXT_RESULTS_" + IdUtil.objectId();

    private String reportItemCode;

    /**
     * sampleId
     */
    private Long sampleId;

    /**
     * 是否是质控
     */
    private Boolean isQc;

    /**
     * result
     */
    private String result;

    /**
     * instrument_result
     */
    private String instrumentResult;

    /**
     * 检查时间
     */
    private Date testDate;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * groupId
     */
    private Long groupId;

    /**
     * instrumentId
     */
    private Long instrumentId;

    /**
     * 来源
     *
     * @see SaveResultSourceEnum
     */
    private SaveResultSourceEnum source;

    /**
     * 多出来的数据， json
     */
    private String extraInfo;

    @Override
    protected String getWatchName() {
        return "质控结果保存";
    }

    public SaveQCResultContext() {
        put(CONTEXT_RESULTS, new LinkedHashMap<>(0));
    }

    public static SaveQCResultContext from(Context context) {
        return (SaveQCResultContext) context;
    }

    @SuppressWarnings("unchecked")
    public List<ApplySampleItemDto> getSampleTestItems() {
        return (List<ApplySampleItemDto>) get(SAMPLE_TEST_ITEMS);
    }

    public SampleDto getSample() {
        return (SampleDto) get(SAMPLE);
    }


    public InstrumentReportItemDto getInstrumentReportItem() {
        return (InstrumentReportItemDto) get(INSTRUMENT_REPORT_ITEM);
    }

//    public ReportItemDto getReportItem() {
//        return (ReportItemDto) get(REPORT_ITEM);
//    }

    @SuppressWarnings("unchecked")
    public List<InstrumentReportItemDto> getInstrumentReportItems() {
        return (List<InstrumentReportItemDto>) get(INSTRUMENT_REPORT_ITEMS);
    }

    @Nullable
    public InstrumentReportItemReferenceDto getInstrumentReportItemReference() {
        return (InstrumentReportItemReferenceDto) get(INSTRUMENT_REPORT_REFERENCE);
    }

    @Nullable
    public InstrumentDto getInstrument() {
        return (InstrumentDto) get(INSTRUMENT);
    }

    @SuppressWarnings("unchecked")
    public List<InstrumentReportItemResultExchangeDto> getResultExchange() {
        return (List<InstrumentReportItemResultExchangeDto>) get(RESULT_EXCHANGE);
    }

    @Nullable
    public String getResultJudge() {
        return (String) get(RESULT_JUDGE);
    }

    public boolean isException() {
        return Objects.nonNull(get(RESULT_IS_EXCEPTION)) && (boolean) get(RESULT_IS_EXCEPTION);
    }

    public boolean isCritical() {
        return Objects.nonNull(get(RESULT_IS_CRITICAL)) && (boolean) get(RESULT_IS_CRITICAL);
    }

    /**
     * 获取之前的结果
     *
     * @return null 则没有
     */
    @Nullable
    public String getBeforeResult() {
        final Object value = get(BEFORE_RESULT);
        if (Objects.isNull(value)) {
            return null;
        }
        return String.valueOf(value);
    }

    /**
     * 当前上下文涉及到的报告项目结果，也就是说在整个流程中，如果整个报告项目涉及到新增、修改结果 那么都会在这里
     */
    @SuppressWarnings("unchecked")
    public Map<String, String> getContextResults() {
        return (Map<String, String>) get(CONTEXT_RESULTS);
    }


}
