package com.labway.lims.routine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.routine.api.dto.QuerySampleDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.model.TbSample;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbSampleMapper extends BaseMapper<TbSample> {

    /**
     * 根据ID批量修改
     */
    int updateBySampleIds(@Param("sample") SampleDto sampleDto, @Param("sampleIds") Collection<Long> sampleIds);

    /**
     * 查询包含已删除的数据
     * @param dto
     * @return
     */
    List<TbSample> selectAllByTestDate(@Param("dto") QuerySampleDto dto);

    /**
     * 根据条件查询样本列表
     */
    List<TbSample> selectSamples(@Param("dto") QuerySampleDto dto);
}
