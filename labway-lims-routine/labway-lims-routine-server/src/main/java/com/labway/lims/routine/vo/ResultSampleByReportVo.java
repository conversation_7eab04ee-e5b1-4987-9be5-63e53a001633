package com.labway.lims.routine.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/10 15:56
 */
@Getter
@Setter
public class ResultSampleByReportVo {
    /**
     * 申请单样本id
     */
    private Long applySampleId;
    /**
     * 样本id
     */
    private Long sampleId;

    /**
     * 结果ID(可能有 可能没有)
     */
    private Long sampleResultId;

    /**
     * 结果 （可能有 可能没有）
     */
    private String result;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 报告单项目ID
     */
    private Long reportItemId;

    /**
     * 报告单项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDate;

}
