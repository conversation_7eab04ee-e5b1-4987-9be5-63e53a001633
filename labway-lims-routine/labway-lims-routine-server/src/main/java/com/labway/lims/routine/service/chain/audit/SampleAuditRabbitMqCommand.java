package com.labway.lims.routine.service.chain.audit;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 发送审核消息到mq
 */
@Slf4j
@Component
class SampleAuditRabbitMqCommand implements Command {

    @DubboReference
    private RabbitMQService rabbitMQService;


    private static final String ROUTING_KEY = "sample_change_key";

    @Override
    public boolean execute(Context c) throws Exception {
        final AuditSampleContext context = AuditSampleContext.from(c);
        final SampleDto sample = context.getSample();
        final SampleAuditDto param = context.getParam();

        final ApplySampleEventDto event = new ApplySampleEventDto();

        event.setOrgId(LoginUserHandler.get().getOrgId());
        event.setHspOrgId(context.getApply().getHspOrgId());
        event.setHspOrgCode(context.getApply().getHspOrgCode());
        event.setHspOrgName(context.getApply().getHspOrgName());
        event.setApplyId(sample.getApplyId());
        event.setApplySampleId(sample.getApplySampleId());
        event.setBarcode(sample.getBarcode());
        event.setExtras(Map.of("sampleId", String.valueOf(sample.getSampleId()),"sampleNo", String.valueOf(sample.getSampleNo())));

        if (Objects.equals(SampleAuditStatusEnum.ONE_CHECK.name(), param.getAuditStatus())) {
            event.setEvent(ApplySampleEventDto.EventType.OneCheck);
        } else if (Objects.equals(SampleAuditStatusEnum.TWO_CHECK.name(), param.getAuditStatus())) {
            event.setEvent(ApplySampleEventDto.EventType.TwoCheck);
        }

        final String json = JSON.toJSONString(event);
        rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

        log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功",
                sample.getApplySampleId(), sample.getBarcode(), json, RabbitMQService.EXCHANGE, ROUTING_KEY);

        return CONTINUE_PROCESSING;
    }
}
