package com.labway.lims.routine.vo;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collection;

/**
 * 历史结果--jvs使用
 *
 * <AUTHOR> on 2025/7/31.
 */
@Getter
@Setter
public class HistoryResultJVSVo implements Serializable {

	private Collection<JVSResultVo> historyResults;

	@Getter
	@Setter
	public static class JVSResultVo {

		/**
		 * 检验日期
		 */
		private String testDate;

		/**
		 * 报告项目名称
		 */
		private String reportItemName;

		/**
		 * 报告项目编码
		 */
		private String reportItemCode;

		/**
		 * 报告项目id
		 */
		private Long reportItemId;

		/**
		 * 结果
		 */
		private String result;

		/**
		 * 1: 危机
		 * 2: 异常
		 * 0: 正常
		 *
		 * @see ResultStatusEnum
		 */
		private Integer status;

		/**
		 * 检验判断
		 *
		 * @see TestJudgeEnum
		 */
		private String judge;

		/**
		 * 打印顺序
		 */
		private Integer printSort;

	}
}
