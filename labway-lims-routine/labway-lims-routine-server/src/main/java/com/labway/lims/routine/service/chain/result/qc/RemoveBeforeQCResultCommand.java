package com.labway.lims.routine.service.chain.result.qc;

import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Component
public class RemoveBeforeQCResultCommand implements Command {

    @Resource
    private SampleResultService sampleResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveQCResultContext context = SaveQCResultContext.from(c);

        // 先把之前的结果保存下来
        final SampleResultDto sampleResult = sampleResultService
                .selectBySampleIdAndReportItemCode(context.getSampleId(), context.getReportItemCode());

        if (Objects.isNull(sampleResult)) {
            return CONTINUE_PROCESSING;
        }

        context.put(SaveQCResultContext.BEFORE_RESULT, sampleResult.getResult());

        //  仪器的结果不删除
        SampleDto sample = context.getSample();
        if (Objects.equals(sample.getApplySampleId(), NumberUtils.LONG_ZERO)
                && Objects.equals(sample.getApplyId(), NumberUtils.LONG_ZERO)) {

            final Date testDate = context.getTestDate();
            sampleResultService.deleteByReportItemCodeSampleIdTestDate(context.getReportItemCode(), context.getSampleId(), testDate);

            return CONTINUE_PROCESSING;
        }

        // 删除之前的结果 mysql
        sampleResultService.deleteBySampleResultId(sampleResult.getSampleResultId(),context.getSampleId());

        return CONTINUE_PROCESSING;
    }
}
