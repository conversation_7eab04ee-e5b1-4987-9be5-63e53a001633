package com.labway.lims.routine.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class QuerySampleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * orgId
     */
    private Long orgId;

    /**
     * groupId
     */
    private Long groupId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 样本状态
     *
     * @see SampleStatusEnum
     */
    private String sampleStatus;

    /**
     * 急诊状态
     * 0普通  1急诊  2全部
     *
     * @see com.labway.lims.api.enums.apply.UrgentEnum
     */
    private Integer urgent;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 检验日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateEnd;

    /**
     * 是否仅显示检验样本
     */
    private Integer isTestSample;

    /**
     * 是否仅显示 结果值出全的样本
     */
    private Boolean showSampleWithAllResult;

}
