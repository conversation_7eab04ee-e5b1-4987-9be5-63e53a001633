package com.labway.lims.routine.service.chain.audit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.routine.api.dto.SampleDto;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;


@Component
public class SampleAuditFlowCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditSampleContext context = AuditSampleContext.from(c);
        final SampleDto sample = context.getSample();
        final UserDto twoCheckUser = context.getTwoCheckUser();
        final LoginUserHandler.User user = LoginUserHandler.get();

        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(sample.getApplyId());
        sampleFlow.setApplySampleId(sample.getApplySampleId());
        sampleFlow.setBarcode(sample.getBarcode());

        sampleFlow.setOperator(Objects.nonNull(twoCheckUser) ? twoCheckUser.getNickname() : user.getNickname());
        sampleFlow.setOperatorId(Objects.nonNull(twoCheckUser) ? twoCheckUser.getUserId() : user.getUserId());

	    if (context.isAutoAudit()) {
		    sampleFlow.setContent(BarcodeFlowEnum.AUTO_ONE_CHECK.getDesc());
		    sampleFlow.setOperateCode(BarcodeFlowEnum.AUTO_ONE_CHECK.name());
		    sampleFlow.setOperateName(BarcodeFlowEnum.AUTO_ONE_CHECK.getDesc());
			sampleFlow.setOperator(String.format("%s (自动审核)", sampleFlow.getOperator()));
	    } else {
			if (Objects.equals(SampleAuditStatusEnum.ONE_CHECK.name(), context.getParam().getAuditStatus())) {
			    sampleFlow.setContent(BarcodeFlowEnum.ONE_CHECK.getDesc());
			    sampleFlow.setOperateCode(BarcodeFlowEnum.ONE_CHECK.name());
			    sampleFlow.setOperateName(BarcodeFlowEnum.ONE_CHECK.getDesc());
		    } else if (Objects.equals(SampleAuditStatusEnum.TWO_CHECK.name(), context.getParam().getAuditStatus())) {
			    sampleFlow.setContent(BarcodeFlowEnum.TWO_CHECK.getDesc());
			    sampleFlow.setOperateCode(BarcodeFlowEnum.TWO_CHECK.name());
			    sampleFlow.setOperateName(BarcodeFlowEnum.TWO_CHECK.getDesc());
		    } else {
			    sampleFlow.setOperateCode("");
			    sampleFlow.setOperateName("");
			    sampleFlow.setContent("");
		    }
	    }
        sampleFlow.setIsDelete(YesOrNoEnum.NO.getCode());
        sampleFlow.setCreateDate(new Date());
        sampleFlow.setCreatorId(user.getUserId());
        sampleFlow.setCreatorName(user.getNickname());
        sampleFlow.setUpdateDate(new Date());
        sampleFlow.setUpdaterId(user.getUserId());
        sampleFlow.setUpdaterName(user.getNickname());

        sampleFlowService.addSampleFlow(sampleFlow);
        return CONTINUE_PROCESSING;
    }


}
