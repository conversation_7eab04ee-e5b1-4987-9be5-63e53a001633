package com.labway.lims.routine.service.chain.audit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.routine.api.dto.SampleDto;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;


@Component
public class SampleAuditFlowCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditSampleContext context = AuditSampleContext.from(c);
        final SampleDto sample = context.getSample();
        final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(snowflakeService.genId());
            sampleFlow.setApplyId(sample.getApplyId());
            sampleFlow.setApplySampleId(sample.getApplySampleId());
            sampleFlow.setBarcode(sample.getBarcode());

            sampleFlow.setOperator(LoginUserHandler.get().getNickname());
            sampleFlow.setOperatorId(LoginUserHandler.get().getUserId());
            if (Objects.equals(SampleAuditStatusEnum.ONE_CHECK.name(), context.getParam().getAuditStatus())) {
                sampleFlow.setContent("一审");
                sampleFlow.setOperateCode(BarcodeFlowEnum.ONE_CHECK.name());
                sampleFlow.setOperateName(BarcodeFlowEnum.ONE_CHECK.getDesc());
            } else if (Objects.equals(SampleAuditStatusEnum.TWO_CHECK.name(), context.getParam().getAuditStatus())) {
                sampleFlow.setContent("二审");
                sampleFlow.setOperateCode(BarcodeFlowEnum.TWO_CHECK.name());
                sampleFlow.setOperateName(BarcodeFlowEnum.TWO_CHECK.getDesc());
            } else {
                sampleFlow.setOperateCode("");
                sampleFlow.setOperateName("");
                sampleFlow.setContent("");
            }
            sampleFlow.setIsDelete(YesOrNoEnum.NO.getCode());
            sampleFlow.setCreateDate(new Date());
            sampleFlow.setCreatorId(LoginUserHandler.get().getUserId());
            sampleFlow.setCreatorName(LoginUserHandler.get().getNickname());
            sampleFlow.setUpdateDate(new Date());
            sampleFlow.setUpdaterId(LoginUserHandler.get().getUserId());
            sampleFlow.setUpdaterName(LoginUserHandler.get().getNickname());

        sampleFlowService.addSampleFlow(sampleFlow);
        return CONTINUE_PROCESSING;
    }


}
