package com.labway.lims.routine.service.chain.result;


import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.routine.mapper.TbSampleMapper;
import com.labway.lims.routine.model.TbSample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/3/30 16:24
 */
@Slf4j
@Component
public class UpdateSampleCommand implements Command {

    @Resource
    private TbSampleMapper sampleMapper;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final TbSample sample = new TbSample();

        sample.setSampleId(SaveResultContext.from(c).getSample().getSampleId());

        //当只有是仪器传来的时间才去修改 检验时间 (testDate), 否侧就默认和 接收时间(receiveDate) 保持一致
        //20230808各样本的检验时间，为二次分拣的时间，点击【保存】按钮时不更新检验时间，仪器传输结果也不更新检验时间
        if (!Objects.equals(context.getSource(), SaveResultSourceEnum.MACHINE)) {

            return CONTINUE_PROCESSING;
        }
//        if (Objects.nonNull(context.getTestDate())) {
//            sample.setTestDate(context.getTestDate());
//
//        } else {
//            sample.setTestDate(context.getSample().getTestDate());
//        }
//        sampleMapper.updateById(sample);

        return CONTINUE_PROCESSING;
    }
}
