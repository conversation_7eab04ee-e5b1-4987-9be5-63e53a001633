package com.labway.lims.routine.service.chain.auto.audit;

import cn.hutool.json.JSONUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.controller.RoutineController;
import com.labway.lims.routine.service.chain.result.SaveResultContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自动审核获取基础信息
 *
 * <AUTHOR> Tianhao on 2025/7/29.
 */
@Slf4j
@Component
public class AutoAuditGetBaseInfoCommand implements Command {

	@Resource
	private RedisPrefix redisPrefix;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@DubboReference
	private TestItemService testItemService;

	@Resource
	private SampleResultService sampleResultService;

	@Override
	public boolean execute(Context c) throws Exception {
		AutoAuditContext context = AutoAuditContext.from(c);
		SaveResultContext saveResultOriginContext = context.getSaveResultOriginContext();
		baseCheck(saveResultOriginContext);

		// 样本下的检验项目
		Collection<Long> testItemIds = saveResultOriginContext.getSampleTestItems()
				.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());

		if (CollectionUtils.isEmpty(testItemIds)) {
            throw new IllegalStateException("样本检验项目不存在");
		}

		List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(testItemIds);

		if (CollectionUtils.isEmpty(testItemIds)) {
            throw new IllegalStateException("样本检验项目不存在");
		}

		// 获取样本下检验项目的结果
		List<SampleResultDto> sampleResultDtos = sampleResultService.selectBySampleId(context.getSampleId());
		if (CollectionUtils.isEmpty(sampleResultDtos)) {
			throw new IllegalStateException("样本结果不存在");
		}
		// 为当前线程设置登陆人信息
		Long orgId = saveResultOriginContext.getApplySample().getOrgId();
		Long groupId = saveResultOriginContext.getApplySample().getGroupId();
		String useInfo = stringRedisTemplate.opsForValue().get(redisPrefix.getBasePrefix() + String.format(RoutineController.GROUP_AUTO_AUDIT_USER, orgId, groupId));
		if (!JSONUtil.isTypeJSON(useInfo)) {
			throw new IllegalStateException("自动审核人信息错误");
		}

		LoginUserHandler.User user = JSONUtil.toBean(useInfo, LoginUserHandler.User.class);
		LoginUserHandler.set(user);

		context.put(AutoAuditContext.SAMPLE_RESULT, sampleResultDtos);
		context.put(AutoAuditContext.SAMPLE_TEST_ITEMS, testItemDtos);
		return CONTINUE_PROCESSING;
	}

	private void baseCheck(SaveResultContext saveResultOriginContext) {
		if (Objects.isNull(saveResultOriginContext)) {
			throw new IllegalStateException("自动审核触发节点异常，请确认参数");
		}

		if (Objects.isNull(saveResultOriginContext.getApply())) {
			throw new IllegalStateException("自动审核：申请单不存在");
		}

		if (Objects.isNull(saveResultOriginContext.getApplySample())) {
			throw new IllegalStateException("自动审核：申请单样本不存在");
		}

		if (Objects.isNull(saveResultOriginContext.getSample())) {
			throw new IllegalStateException("自动审核：样本不存在");
		}
	}
}
