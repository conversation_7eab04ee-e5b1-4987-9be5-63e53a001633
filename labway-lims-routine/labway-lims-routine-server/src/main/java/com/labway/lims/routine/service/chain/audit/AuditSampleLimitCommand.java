package com.labway.lims.routine.service.chain.audit;

import com.labway.lims.api.RedisPrefix;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 对单个样本审核加锁
 *
 * <AUTHOR> on 2025/7/29.
 */
@Slf4j
@Component
public class AuditSampleLimitCommand implements Filter, Command {

    private static final String MARK = AuditSampleLimitCommand.class.getName();

    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

	@Override
	public boolean execute(Context c) throws Exception {

		AuditSampleContext context = AuditSampleContext.from(c);
		// 自动审核已经加过锁了 直接跳过
		if (context.isAutoAudit()) {
			return CONTINUE_PROCESSING;
		}
		if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue()
                .setIfAbsent(getSampleAuditKey(context.getParam().getSampleId()),
                StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalStateException("正在审核中");
        }

		// 存储锁标识
        context.put(MARK, StringUtils.EMPTY);

		return CONTINUE_PROCESSING;
	}

	@Override
	public boolean postprocess(Context c, Exception exception) {
		AuditSampleContext context = AuditSampleContext.from(c);

		// 执行完成 释放锁
		if (context.containsKey(MARK)) {
			stringRedisTemplate.delete(getSampleAuditKey(context.getParam().getSampleId()));
		}

		return CONTINUE_PROCESSING;
	}

	public String getSampleAuditKey(Long sampleId) {
		return redisPrefix.getBasePrefix() + MARK + sampleId;
	}
}
