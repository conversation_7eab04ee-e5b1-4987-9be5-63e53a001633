package com.labway.lims.routine.service.chain.pick.two;

import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SaveResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 恢复原始结果 https://www.tapd.cn/59091617/prong/stories/view/1159091617001000672
 * <br/>
 * 此处代码与取消二次分拣那里相结合
 */
@Slf4j
@Component
class TwoPickOriginalResultCommand implements Filter, Command {
    @Resource
    private SampleResultService sampleResultService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean postprocess(Context c, Exception exception) {

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);

        Map<String, SampleResultDto> results = sampleResultService.selectBySampleIdAsMap(context.getApplySampleId());
        //这里需要剔除掉不符合检验项目的结果
        final List<ApplySampleItemDto> sampleItemDtos = context.getApplySampleItems();
        final Set<String> stopItemCodes = sampleItemDtos.stream().map(ApplySampleItemDto::getTestItemCode).collect(Collectors.toSet());
        //如果检验项目不为空 ，需要剔除掉不符合检验项目的结果
        if (CollectionUtils.isNotEmpty(stopItemCodes)){
            results.entrySet().removeIf(e -> !stopItemCodes.contains(e.getValue().getTestItemCode()));
        }

        context.put(TwoPickContext.ORIGINAL_RESULTS, results);

        if (MapUtils.isEmpty(results)) {
            return CONTINUE_PROCESSING;
        }

        final SampleDto sample = context.getSample();
        final LinkedList<Long> ids = snowflakeService.genIds(results.size());

        for (SampleResultDto e : results.values()) {
            e.setSampleId(sample.getSampleId());
            e.setSampleResultId(ids.pop());
        }

        // 删除原始结果
        sampleResultService.deleteBySampleId(context.getApplySampleId());

        Map<SaveResultSourceEnum, List<SampleResultDto>> resultMapBySource =
                results.values().stream().collect(Collectors.groupingBy(e -> Objects.equals(e.getInstrumentId(), NumberUtils.LONG_ZERO) ?
                        SaveResultSourceEnum.FRONT : SaveResultSourceEnum.MACHINE));

        resultMapBySource.forEach((source, v) -> {
            List<SaveResultDto> saveResultDtos = v.stream().map(e -> {
                SaveResultDto saveResultDto = new SaveResultDto();

                saveResultDto.setSampleId(sample.getSampleId());
                saveResultDto.setApplyId(e.getApplyId());
                saveResultDto.setApplySampleId(e.getApplySampleId());
                saveResultDto.setReportItemId(e.getReportItemId());
                saveResultDto.setReportItemCode(e.getReportItemCode());
                saveResultDto.setResult(e.getResult());
                saveResultDto.setExtraInfo(e.getExtraInfo());
                saveResultDto.setDate(new Date());
                saveResultDto.setSampleNo(sample.getSampleNo());
                saveResultDto.setGroupId(sample.getGroupId());
                saveResultDto.setInstrumentId(sample.getInstrumentId());

                return saveResultDto;
            }).collect(Collectors.toList());

            sampleResultService.saveResults(saveResultDtos, source);
        });

        return CONTINUE_PROCESSING;
    }
}
