package com.labway.lims.routine.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class InstrumentResultsTrendRequest {

    /**
     * 样本ID
     */
    private Long sampleId;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 开始时间
     */
    private Date beginDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 报告项目编码
     */
    private String reportItemCode;
}
