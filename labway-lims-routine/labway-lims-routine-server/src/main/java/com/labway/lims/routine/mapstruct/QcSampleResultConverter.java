package com.labway.lims.routine.mapstruct;

import com.labway.lims.routine.api.dto.QcSampleResultDto;
import com.labway.lims.routine.model.TbQcSampleResult;
import org.mapstruct.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 特检样本 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface QcSampleResultConverter {

    QcSampleResultDto qcSampleResultDtoFromTbObj(TbQcSampleResult obj);

    List<QcSampleResultDto> qcSampleResultDtoListFromTbObj(List<TbQcSampleResult> list);

    TbQcSampleResult tbQcSampleResultFromTbObjDto(QcSampleResultDto obj);

    List<TbQcSampleResult> tbQcSampleResultListFromTbObjDto(Collection<QcSampleResultDto> list);

}
