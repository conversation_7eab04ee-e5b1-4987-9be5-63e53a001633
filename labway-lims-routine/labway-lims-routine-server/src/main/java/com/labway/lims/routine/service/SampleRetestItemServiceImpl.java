package com.labway.lims.routine.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.mapper.TbSampleReportItemMapper;
import com.labway.lims.routine.mapper.TbSampleRetestItemMapper;
import com.labway.lims.routine.model.TbSampleReportItem;
import com.labway.lims.routine.model.TbSampleRetestItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 样本结果复查子表 Service impl
 *
 * <AUTHOR>
 * @since 2023/4/11 19:07
 */
@Slf4j
@DubboService
public class SampleRetestItemServiceImpl implements SampleRetestItemService {

    @Resource
    private TbSampleRetestItemMapper tbSampleRetestItemMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private TbSampleReportItemMapper tbSampleReportItemMapper;

    @Override
    public List<SampleRetestItemDto> selectBySampleIds(Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbSampleRetestItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSampleRetestItem::getSampleId, sampleIds);
        queryWrapper.eq(TbSampleRetestItem::getIsDelete, YesOrNoEnum.NO.getCode());
        return convert(tbSampleRetestItemMapper.selectList(queryWrapper));
    }

    @Override
    public List<SampleRetestItemDto> selectBySampleId(long sampleId) {
        return selectBySampleIds(List.of(sampleId));
    }

    @Override
    public List<SampleRetestItemDto> selectBySampleIdAndReportItemId(long sampleId, long reportItemId) {
        LambdaQueryWrapper<TbSampleRetestItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSampleRetestItem::getSampleId, sampleId);
        queryWrapper.eq(TbSampleRetestItem::getReportItemId, reportItemId);
        queryWrapper.eq(TbSampleRetestItem::getIsDelete, YesOrNoEnum.NO.getCode());
        return convert(tbSampleRetestItemMapper.selectList(queryWrapper));
    }

    @Override
    public void deleteBySampleRetestMainIdAndReportItemId(long sampleRetestMainId, long reportItemId) {
        final LambdaQueryWrapper<TbSampleRetestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleRetestItem::getSampleRetestMainId, sampleRetestMainId)
                .eq(TbSampleRetestItem::getReportItemId, reportItemId);
        tbSampleRetestItemMapper.delete(wrapper);
    }

    @Override
    public void deleteBySampleRetestMainIdAndReportItemCode(long sampleRetestMainId, String reportItemCode) {

        if (StringUtils.isBlank(reportItemCode)) {
            return;
        }

        final LambdaQueryWrapper<TbSampleRetestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleRetestItem::getSampleRetestMainId, sampleRetestMainId)
                .eq(TbSampleRetestItem::getReportItemCode, reportItemCode);
        tbSampleRetestItemMapper.delete(wrapper);
    }

    @Override
    public void deleteBySampleRetestMainId(long sampleRetestMainId) {
        final LambdaQueryWrapper<TbSampleRetestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleRetestItem::getSampleRetestMainId, sampleRetestMainId);
        tbSampleRetestItemMapper.delete(wrapper);
    }

    @Override
    public long addSampleRetestItem(SampleRetestItemDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final TbSampleRetestItem item = JSON.parseObject(JSON.toJSONString(dto), TbSampleRetestItem.class);
        item.setSampleRetestItemId(ObjectUtils.defaultIfNull(item.getSampleRetestItemId(), snowflakeService.genId()));
        item.setOrgId(user.getOrgId());
        item.setOrgName(user.getOrgName());
        item.setIsDelete(YesOrNoEnum.NO.getCode());
        item.setCreateDate(new Date());
        item.setCreatorId(user.getUserId());
        item.setCreatorName(user.getNickname());
        item.setUpdateDate(new Date());
        item.setUpdaterId(user.getUserId());
        item.setUpdaterName(user.getNickname());
        if (tbSampleRetestItemMapper.insert(item) < 1) {
            throw new IllegalStateException("添加失败");
        }
        log.info("用户 [{}] 添加复查项成功 [{}]", user.getNickname(), JSON.toJSONString(item));
        return item.getSampleRetestItemId();
    }

    @Override
    public void addSampleRetestItems(Collection<SampleRetestItemDto> sampleRetestItems) {
        if (CollectionUtils.isEmpty(sampleRetestItems)) {
            return;
        }

        for (SampleRetestItemDto e : sampleRetestItems) {
            addSampleRetestItem(e);
        }
    }

    @Override
    public List<SampleRetestItemDto> selectBySampleRetestMainId(long sampleRetestItemId) {
        final LambdaQueryWrapper<TbSampleRetestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleRetestItem::getSampleRetestMainId, sampleRetestItemId);

        return convert(tbSampleRetestItemMapper.selectList(wrapper));
    }

    @Override
    public List<SampleRetestItemDto> selectBySampleRetestMainIds(Collection<Long> sampleRetestItemIds) {
        final LambdaQueryWrapper<TbSampleRetestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbSampleRetestItem::getSampleRetestMainId, sampleRetestItemIds);

        return convert(tbSampleRetestItemMapper.selectList(wrapper));
    }

    @Override
    public boolean updateById(SampleRetestItemDto item) {
        final TbSampleRetestItem retestItem = JSON.parseObject(JSON.toJSONString(item), TbSampleRetestItem.class);

        if (tbSampleRetestItemMapper.updateById(retestItem) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改复查项成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(item));

        return false;
    }

    @Override
    public List<SampleRetestItemDto> selectBySampleIdAndTestItemId(long sampleId, long testItemId) {

        final LambdaQueryWrapper<TbSampleRetestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleRetestItem::getSampleId, sampleId)
                .eq(TbSampleRetestItem::getTestItemId, testItemId);

        return tbSampleRetestItemMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    /**
     * TbSampleRetestItem 转换 为 SampleRetestItemDto
     *
     * @param list TbSampleRetestItem
     * @return SampleRetestItemDto
     */
    private List<SampleRetestItemDto> convert(List<TbSampleRetestItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert)
                .collect(Collectors.toList());
    }


    private SampleRetestItemDto convert(TbSampleRetestItem item) {
        if (Objects.isNull(item)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(item), SampleRetestItemDto.class);
    }

    @Override
    public void deleteNoResultRetestItem(Collection<Long> retestItemIds) {
        tbSampleRetestItemMapper.delete(Wrappers.lambdaQuery(TbSampleRetestItem.class).in(TbSampleRetestItem::getSampleRetestItemId, retestItemIds).eq(TbSampleRetestItem::getResult, ""));
    }

    @Override
    public void updateRetestStatus(Set<Long> reportItemIds, long sampleId, Integer retestStatus) {
        if (CollectionUtils.isEmpty(reportItemIds) || Objects.isNull(retestStatus)) {
            return;
        }

        final LambdaUpdateWrapper<TbSampleReportItem> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TbSampleReportItem::getSampleId, sampleId)
                .in(TbSampleReportItem::getSampleReportItemId, reportItemIds);

        TbSampleReportItem tb = new TbSampleReportItem();
        tb.setSampleId(sampleId);
        tb.setIsRetest(retestStatus);
        tbSampleReportItemMapper.update(tb, wrapper);
    }

    @Override
    public SampleRetestItemDto selectBySampleRetestItemId(long sampleRetestItemId) {

        return convert(tbSampleRetestItemMapper.selectById(sampleRetestItemId));
    }

    @Override
    public void deleteBySampleRetestMainIdAndReportItemCodes(long sampleRetestMainId, Collection<String> reportItemCodes) {
        final LambdaQueryWrapper<TbSampleRetestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleRetestItem::getSampleRetestMainId, sampleRetestMainId)
                .in(TbSampleRetestItem::getReportItemCode, reportItemCodes);
        tbSampleRetestItemMapper.delete(wrapper);
    }

    @Override
    public Map<String, Long> selectReportCountBySampleIds(Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return Collections.emptyMap();
        }
        // 修复， 分批
        return Lists.partition(new ArrayList<>(sampleIds), 500)
                .parallelStream()
                .flatMap(e -> tbSampleRetestItemMapper.selectList(Wrappers.lambdaQuery(TbSampleRetestItem.class)
                                .select(TbSampleRetestItem::getReportItemCode, TbSampleRetestItem::getSampleId)
                                .in(TbSampleRetestItem::getSampleId, e))
                        .stream())
                .collect(Collectors.groupingBy(e -> e.getSampleId() + "-" + e.getReportItemCode(), Collectors.counting()));
    }
}
