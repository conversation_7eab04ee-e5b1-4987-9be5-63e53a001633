package com.labway.lims.routine.service.chain.result;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemResultExchangeService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/3/30 16:26
 */
@Slf4j
@Component
public class FillSampleCommand implements Command {
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @Resource
    private SampleService sampleService;
    @Resource
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private InstrumentReportItemResultExchangeService instrumentReportItemResultExchangeService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplyService applyService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        if (Objects.isNull(context.getSampleTestItems())) {
            final List<ApplySampleItemDto> dtos = applySampleItemService.selectByApplySampleId(context.getApplySampleId());

            if (CollectionUtils.isEmpty(dtos)) {
                throw new IllegalStateException("样本检验项目不存在");
            }

            context.put(SaveResultContext.SAMPLE_TEST_ITEMS, dtos);
        }

        if (Objects.nonNull(context.getInstrumentId())) {
            final InstrumentDto instrument = instrumentService.selectByInstrumentId(context.getInstrumentId());
            if (Objects.isNull(instrument)) {
                throw new IllegalStateException("仪器不存在");
            }
            context.put(SaveResultContext.INSTRUMENT, instrument);
        }

        if (Objects.isNull(context.getSample())) {
            final SampleDto dto = sampleService.selectByApplySampleId(context.getApplySampleId());

            if (Objects.isNull(dto)) {
                throw new IllegalStateException("样本不存在");
            }

            context.put(SaveResultContext.SAMPLE, dto);
        }

        if (Objects.isNull(context.getApply())) {

            final ApplyDto applyDto = applyService.selectByApplyId(context.getSample().getApplyId());

            if (Objects.isNull(applyDto)) {
                throw new IllegalStateException("申请单不存在");
            }

            context.put(SaveResultContext.APPLY, applyDto);
        }

        if (Objects.isNull(context.getApplySample())) {
            final ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(context.getSample().getApplySampleId());

            if (Objects.isNull(applySampleDto)) {
                throw new IllegalStateException("申请单样本不存在");
            }

            context.put(SaveResultContext.APPLY_SAMPLE, applySampleDto);
        }

        if (Objects.isNull(context.getInstrumentReportItems())) {
            final List<InstrumentReportItemDto> reportItems = instrumentReportItemService.selectByInstrumentGroupId(context.getSample().getInstrumentGroupId());
            context.put(SaveResultContext.INSTRUMENT_REPORT_ITEMS, reportItems);
        }

        if (Objects.isNull(context.getSampleReportItems())) {
            final List<SampleReportItemDto> reportItems = sampleReportItemService.selectBySampleId(context.getSample().getSampleId());
            context.put(SaveResultContext.SAMPLE_REPORT_ITEMS, reportItems);
        }

        if (Objects.isNull(context.getSampleReportItem())) {
            final List<SampleReportItemDto> sampleReportItems = context.getSampleReportItems();
            final SampleReportItemDto reportItemDto = sampleReportItems.stream().filter(e -> Objects.equals(e.getReportItemCode(), context.getReportItemCode()))
                    .findFirst()
                    .orElse(null);

            if (Objects.isNull(reportItemDto)) {
                throw new IllegalStateException(String.format("当前结果【%s】对应样本报告项目【%s】不存在", context.getResult(), context.getReportItemCode()));
            }

            context.put(SaveResultContext.SAMPLE_REPORT_ITEM, reportItemDto);
        }

        if (Objects.isNull(context.getReportItem())) {
            final ReportItemDto item = reportItemService.selectByReportItemCode(context.getReportItemCode(), LoginUserHandler.get().getOrgId());
            if (Objects.isNull(item)) {
                throw new IllegalStateException(String.format("报告项目 [%s] 不存在", context.getReportItemCode()));
            }
            context.put(SaveResultContext.REPORT_ITEM, item);
        }

        if (Objects.isNull(context.getInstrumentReportItem())) {

            final ReportItemDto reportItem = context.getReportItem();
            final SampleDto sample = context.getSample();

            InstrumentReportItemDto instrumentReportItem;

            // 取结果上传仪器报告项目，取不到为空
            instrumentReportItem = context.getInstrumentReportItems().stream().filter(e -> Objects.equals(e.getInstrumentId(), context.getInstrumentId())
                            && Objects.equals(e.getReportItemCode(), reportItem.getReportItemCode()))
                    .findFirst().orElse(null);

            if (Objects.isNull(instrumentReportItem)) {
                //取样本仪器报告项目，取不到为空
                instrumentReportItem = context.getInstrumentReportItems().stream().filter(e -> Objects.equals(e.getInstrumentId(), sample.getInstrumentId())
                                && Objects.equals(e.getReportItemCode(), reportItem.getReportItemCode()))
                        .findFirst().orElse(null);
            }

            //在取专业小组报告项目
            if (Objects.isNull(instrumentReportItem)) {
                instrumentReportItem = context.getInstrumentReportItems().stream().filter(e ->
                                Objects.equals(e.getReportItemCode(), reportItem.getReportItemCode()))
                        .findFirst().orElse(null);
            }

            if (Objects.isNull(instrumentReportItem)) {
                throw new IllegalStateException("仪器报告项目不存在");
            }

            context.put(SaveResultContext.INSTRUMENT_REPORT_ITEM, instrumentReportItem);

        }

        if (Objects.isNull(context.getResultExchange())) {
            final List<InstrumentReportItemResultExchangeDto> exchanges = instrumentReportItemResultExchangeService
                    .selectByInstrumentReportItemId(context.getInstrumentReportItem().getInstrumentReportItemId());
            context.put(SaveResultContext.RESULT_EXCHANGE, exchanges);
        }

        return CONTINUE_PROCESSING;
    }
}
