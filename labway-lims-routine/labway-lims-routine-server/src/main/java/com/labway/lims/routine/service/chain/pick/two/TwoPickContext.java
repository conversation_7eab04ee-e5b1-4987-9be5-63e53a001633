package com.labway.lims.routine.service.chain.pick.two;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 二次分拣上下文
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class TwoPickContext extends StopWatchContext {


    /**
     * 申请单样本
     */
    static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();


    /**
     * 样本
     */
    static final String SAMPLE = "SAMPLE_" + IdUtil.objectId();

    /**
     * 申请单
     */
    static final String APPLY = "APPLY_" + IdUtil.objectId();

    /**
     * 专业小组
     */
    static final String INSTRUMENT_GROUP = "INSTRUMENT_GROUP_" + IdUtil.objectId();

    /**
     * 仪器
     */
    static final String INSTRUMENT = "INSTRUMENT_" + IdUtil.objectId();

    /**
     * 申请单项目
     */
    static final String APPLY_SAMPLE_ITEMS = "APPLY_SAMPLE_ITEMS_" + IdUtil.objectId();

    /**
     * 报告项目
     */
    static final String REPORT_ITEMS = "REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 分拣的仪器报告项目
     */
    static final String INSTRUMENT_REPORT_ITEMS = "INSTRUMENT_REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 报告项目所属的申请单项目
     */
    static final String REPORT_ITEM_CODE_2_TEST_ITEM_ID = "REPORT_ITEM_CODE_2_TEST_ITEM_ID_" + IdUtil.objectId();

    /**
     * 原始结果
     */
    static final String ORIGINAL_RESULTS = "ORIGINAL_RESULTS_" + IdUtil.objectId();

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 仪器 ID （IT8000分拣用）
     */
    private Long instrumentId;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 免疫二次分拣时间
     */
    private Date immunityTwoPickDate;

    public static TwoPickContext from(Context context) {
        return (TwoPickContext) context;
    }

    /**
     * 获取申请单
     */
    public ApplyDto getApply() {
        return (ApplyDto) get(APPLY);
    }


    /**
     * 获取申请单样本
     */
    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }


    /**
     * 获取样本
     */
    public SampleDto getSample() {
        return (SampleDto) get(SAMPLE);
    }


    /**
     * 获取专业小组
     */
    public InstrumentGroupDto getInstrumentGroup() {
        return (InstrumentGroupDto) get(INSTRUMENT_GROUP);
    }


    /**
     * 获取仪器
     */
    public InstrumentDto getInstrument() {
        return (InstrumentDto) get(INSTRUMENT);
    }

    /**
     * 获取仪器
     */
    public List<ApplySampleItemDto> getApplySampleItems() {
        return (List<ApplySampleItemDto>) get(APPLY_SAMPLE_ITEMS);
    }


    /**
     * 获取报告项目
     */
    public List<ReportItemDto> getReportItems() {
        return (List<ReportItemDto>) get(REPORT_ITEMS);
    }

    /**
     * 待分拣的仪器报告项目
     */
    public List<InstrumentReportItemDto> getInstrumentReportItems() {
        return (List<InstrumentReportItemDto>) get(INSTRUMENT_REPORT_ITEMS);
    }

    /**
     * 原始结果
     */
    public Map<String, SampleResultDto> getOriginalResults() {
        return ObjectUtils.defaultIfNull((Map<String, SampleResultDto>) get(ORIGINAL_RESULTS), Map.of());
    }

    /**
     * 报告项目所属的申请单项目
     */
    public Map<String, Long> getReportItemCode2TestItemIdMap() {
        return (Map<String, Long>) get(REPORT_ITEM_CODE_2_TEST_ITEM_ID);
    }

    @Override
    protected String getWatchName() {
        return "二次分拣";
    }
}
