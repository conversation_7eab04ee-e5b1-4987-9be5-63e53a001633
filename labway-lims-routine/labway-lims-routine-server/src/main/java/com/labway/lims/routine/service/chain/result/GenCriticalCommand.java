package com.labway.lims.routine.service.chain.result;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.enums.routine.SampleCriticalResultStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/5/21 19:25
 */
@Slf4j
@Component
public class GenCriticalCommand implements Command {
    @Resource
    private SampleCriticalResultService sampleCriticalResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final ApplyDto apply = context.getApply();
        final SampleDto sample = context.getSample();
        final SampleResultDto sampleResult = context.getSampleResult();
        final SampleReportItemDto sampleReportItem = context.getSampleReportItem();
        final InstrumentReportItemReferenceDto ref = context.getInstrumentReportItemReference();

        // 如果已经复查过，那么可以直接跳过了，因为复查过就表示危机值确定了，后续的危机值都不再处理
        if (!Objects.equals(sampleReportItem.getIsRetest(), RetestStatusEnum.NORMAL.getCode())) {
            return CONTINUE_PROCESSING;
        }

        final SampleCriticalResultDto sampleCriticalResult = sampleCriticalResultService.selectBySampleIdAndReportItemCode(sample.getSampleId(),
                sampleResult.getReportItemCode());
        if (Objects.nonNull(sampleCriticalResult)) {
            if (Objects.equals(sampleCriticalResult.getStatus(), SampleCriticalResultStatusEnum.UNPROCESSED.getCode())) {
                sampleCriticalResultService.deleteByCriticalValueId(sampleCriticalResult.getCriticalValueId());
            } else {
                log.info("条码 [{}] 报告项目 [{}({})] 结果 [{}] 是危机值但是不插入危机值表，因为危机值表已经存在",
                        sample.getBarcode(), sampleResult.getReportItemName(), sampleResult.getReportItemCode(),
                        sampleResult.getResult());
                return CONTINUE_PROCESSING;
            }
        }

        if (!context.isCritical()) {
            return CONTINUE_PROCESSING;
        }

        final SampleCriticalResultDto dto = new SampleCriticalResultDto();
        dto.setSampleResultId(sampleResult.getSampleResultId());
        dto.setSampleResult(StringUtils.defaultString(sampleResult.getResult()));
        dto.setSampleId(sample.getSampleId());
        dto.setApplyId(sample.getApplyId());
        dto.setApplySampleId(sample.getApplySampleId());
        dto.setBarcode(StringUtils.defaultString(sample.getBarcode()));
        dto.setHspOrgId(String.valueOf(apply.getHspOrgId()));
        dto.setHspOrgName(StringUtils.defaultString(apply.getHspOrgName()));
        dto.setPatientAge(apply.getPatientAge());
        dto.setPatientSubageUnit(StringUtils.defaultString(apply.getPatientSubageUnit()));
        dto.setPatientSubage(apply.getPatientSubage());
        dto.setPatientName(StringUtils.defaultString(apply.getPatientName()));
        dto.setPatientSex(apply.getPatientSex());
        dto.setTestDate(sample.getTestDate());
        dto.setGroupId(sample.getGroupId());
        dto.setGroupName(StringUtils.defaultString(sample.getGroupName()));
        dto.setReportItemCode(StringUtils.defaultString(sampleReportItem.getReportItemCode()));
        dto.setReportItemName(StringUtils.defaultString(sampleReportItem.getReportItemName()));
        dto.setReportItemId(sampleReportItem.getReportItemId());
        if (Objects.nonNull(ref)) {
            dto.setCriticalRange(ref.getFatalMin() + "<= 或 >=" + ref.getFatalMax());
        } else {
            dto.setCriticalRange(StringUtils.EMPTY);
        }
        dto.setIsCritical(YesOrNoEnum.YES.getCode());
        dto.setHandleUserId(NumberUtils.LONG_ZERO);
        dto.setHandleUserName(StringUtils.EMPTY);
        dto.setHandleContent(StringUtils.EMPTY);
        dto.setHandleDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        dto.setContactPhone(StringUtils.EMPTY);
        dto.setContactUserName(StringUtils.EMPTY);
        dto.setStatus(SampleCriticalResultStatusEnum.UNPROCESSED.getCode());
        dto.setTesterId(NumberUtils.LONG_ZERO);
        dto.setTesterName(StringUtils.EMPTY);
        dto.setOrgId(sample.getOrgId());
        dto.setOrgName(StringUtils.defaultString(sample.getOrgName()));

        sampleCriticalResultService.addSampleCriticalResult(dto);

        return CONTINUE_PROCESSING;
    }
}
