package com.labway.lims.routine.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/10 15:56
 */
@Getter
@Setter
public class QuerySampleByReportVo {
    /**
     * 二次分拣开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date pickStartTime;

    /**
     * 二次分拣结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date pickEndTime;

    /**
     * 报告项目编码
     */
    private String reportCode;

}
