package com.labway.lims.routine.service.chain.result.retest;

import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.service.chain.result.RecalculateRefResultCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 修改报告项目的复查状态
 */
@Slf4j
@Component
class RetestUpdateReportItemRetestStatusCommand implements Command {

    @Resource
    private SampleReportItemService sampleReportItemService;
    @Resource
    private CheckIsCompleteRetestCommand checkIsCompleteRetestCommand;
    @Resource
    private RecalculateRefResultCommand recalculateRefResultCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final RetestResultContext context = RetestResultContext.from(c);

        if (!context.isComplete()) {
            return CONTINUE_PROCESSING;
        }

        if (recalculateRefResultCommand.isRecalculate(c)) {
            return CONTINUE_PROCESSING;
        }

        // 复查的报告项目Code
        final Set<String> reportItemCodes = context.getSampleRetestItems().stream()
                .map(SampleRetestItemDto::getReportItemCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(reportItemCodes)) {
            return CONTINUE_PROCESSING;
        }

        final List<SampleReportItemDto> sampleReportItems = context.getSampleReportItems()
                .stream().filter(e -> reportItemCodes.contains(e.getReportItemCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            return CONTINUE_PROCESSING;
        }


        final SampleReportItemDto sampleReportItem = context.getSampleReportItem();

        final SampleReportItemDto m = new SampleReportItemDto();
        m.setSampleReportItemId(sampleReportItem.getSampleReportItemId());
        m.setIsRetest(RetestStatusEnum.RETEST.getCode());
        m.setSampleId(context.getSample().getSampleId());
        sampleReportItem.setIsRetest(RetestStatusEnum.RETEST.getCode());

        sampleReportItemService.updateBySampleReportItemIds(m, sampleReportItems.stream()
                .map(SampleReportItemDto::getSampleReportItemId).collect(Collectors.toSet()));


        return CONTINUE_PROCESSING;

    }
}
