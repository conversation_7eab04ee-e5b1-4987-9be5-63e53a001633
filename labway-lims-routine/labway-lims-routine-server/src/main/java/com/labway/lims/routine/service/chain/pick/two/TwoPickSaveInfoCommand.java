package com.labway.lims.routine.service.chain.pick.two;

import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 保存数据
 */
@Slf4j
@Component
class TwoPickSaveInfoCommand implements Filter, Command {
    @Resource
    private SampleService sampleService;
    @Resource
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);
        final ApplySampleDto applySample = context.getApplySample();
        final InstrumentGroupDto instrumentGroup = context.getInstrumentGroup();
        final InstrumentDto instrument = context.getInstrument();
        final ApplyDto apply = context.getApply();
        final List<ApplySampleItemDto> applySampleItems = context.getApplySampleItems();

        //添加样本
        final SampleDto sample = new SampleDto();
        sample.setSampleId(snowflakeService.genId());
        sample.setApplySampleId(context.getApplySampleId());
        sample.setApplyId(applySample.getApplyId());
        sample.setBarcode(applySample.getBarcode());
        sample.setSampleNo(context.getSampleNo());
        sample.setGroupId(instrumentGroup.getGroupId());
        sample.setGroupName(instrumentGroup.getGroupName());
        sample.setInstrumentId(instrument.getInstrumentId());
        sample.setInstrumentName(instrument.getInstrumentName());
        sample.setInstrumentGroupId(instrumentGroup.getInstrumentGroupId());
        sample.setInstrumentGroupName(instrumentGroup.getInstrumentGroupName());
        sample.setTestDate(Objects.requireNonNullElse(context.getImmunityTwoPickDate(), new Date()));
        sample.setOneCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        sample.setOneCheckerId(NumberUtils.LONG_ZERO);
        sample.setOneCheckerName(StringUtils.EMPTY);
        sample.setTwoCheckerId(NumberUtils.LONG_ZERO);
        sample.setTwoCheckerName(StringUtils.EMPTY);
        sample.setTwoCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        sample.setHspOrgId(apply.getHspOrgId());
        sample.setHspOrgName(apply.getHspOrgName());
        sample.setOrgId(applySample.getOrgId());
        sample.setOrgName(applySample.getOrgName());

        final long sampleId = sampleService.addSample(sample);

        final Map<Long, List<ReportItemDto>> reportItems = context.getReportItems()
                .stream().collect(Collectors.groupingBy(ReportItemDto::getTestItemId));

        //添加样本报告项目
        final LinkedList<SampleReportItemDto> items = new LinkedList<>();
        for (ApplySampleItemDto sampleItem : applySampleItems) {
            if (!reportItems.containsKey(sampleItem.getTestItemId())) {
                continue;
            }

            for (ReportItemDto item : reportItems.get(sampleItem.getTestItemId())) {

                InstrumentReportItemDto reportItem = context.getInstrumentReportItems().stream().filter(e ->
                        Objects.equals(e.getInstrumentId(), sample.getInstrumentId()) &&
                                Objects.equals(e.getReportItemCode(), item.getReportItemCode())).findFirst().orElse(null);

                //it8000 分仪器情况不去找专业小组下的报告项目 如果不是当前仪器下的那么直接跳过
                if (Objects.nonNull(context.getInstrumentId()) && !Objects.equals(context.getInstrumentId(), NumberUtils.LONG_ZERO)) {
                    if (Objects.isNull(reportItem)) {
                        // 因为这里被跳过了，没有落库，所以也从这里面移除 避免影响到后续流程譬如标记
                        context.getReportItems().remove(item);
                        continue;
                    }
                }

                //找不到则找专业小组下的报告项目
                if (Objects.isNull(reportItem)) {
                    reportItem = context.getInstrumentReportItems().stream().
                            filter(e -> Objects.equals(e.getReportItemCode(), item.getReportItemCode()))
                            .findFirst().orElse(null);
                }

                final SampleReportItemDto dto = new SampleReportItemDto();
                dto.setApplyId(sampleItem.getApplyId());
                dto.setApplySampleId(sampleItem.getApplySampleId());
                dto.setSampleId(sampleId);
                dto.setReportItemId(item.getReportItemId());
                dto.setReportItemCode(item.getReportItemCode());
                dto.setReportItemName(item.getReportItemName());
                dto.setTestItemId(item.getTestItemId());
                dto.setTestItemCode(item.getTestItemCode());
                dto.setTestItemName(item.getTestItemName());
                dto.setIsRetest(RetestStatusEnum.NORMAL.getCode());
                dto.setPrintSort(Objects.nonNull(reportItem) ? reportItem.getPrintSort() : NumberUtils.INTEGER_ZERO);
                items.add(dto);
            }
        }

        sampleReportItemService.addSampleReportItems(items);

        context.put(TwoPickContext.SAMPLE, sample);

        return CONTINUE_PROCESSING;
    }
}
