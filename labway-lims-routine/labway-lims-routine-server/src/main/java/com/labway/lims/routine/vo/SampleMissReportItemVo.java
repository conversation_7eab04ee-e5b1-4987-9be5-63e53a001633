package com.labway.lims.routine.vo;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/5/19 16:39
 */
@Getter
@Setter
public class SampleMissReportItemVo {

    /**
     * sampleId
     */
    private Long sampleId;

    /**
     * 缺项的数量(全、-1、-2、-3、-4、-5、缺)
     */
    private String missReportItemNum;

    /**
     * 是否异常
     */
    private Integer isException;

    /**
     * 是否危机
     */
    private Integer isCritical;

    /**
     * 是否复查
     */
    private Integer isRetest;

    /**
     * 样本状态
     *
     * @see com.labway.lims.api.enums.apply.SampleStatusEnum
     */
    private Integer status;

    /**
     * 颜色状态标记 : 10未审 20一审 30已审 40反审 50重审
     * @see SampleStatusEnum
     */
    private Integer colorMarking;
}
