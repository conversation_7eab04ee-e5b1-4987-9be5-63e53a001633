package com.labway.lims.routine.service.chain.audit;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.ExceptionCodeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.EmptyReferenceTipEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultTipDto;
import com.labway.lims.base.api.enums.RelationalOperatorEnum;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CheckSampleResultCommand implements Command {

    @DubboReference
    private SampleAbnormalService sampleAbnormalService;


    @Override
    public boolean execute(Context c) throws Exception {

        AuditSampleContext context = AuditSampleContext.from(c);
        final SampleDto sample = context.getSample();

        // 校验结果值
        checkResult(sample, context);

        return CONTINUE_PROCESSING;
    }

    /**
     * 检测结果
     */
    private void checkResult(SampleDto sample, AuditSampleContext context) {
        final List<SampleResultDto> sampleResults = context.getSampleReportItemResults();
        final List<InstrumentReportItemDto> instrumentReportItems = context.getInstrumentReportItems();
        final List<InstrumentReportItemResultTipDto> tips = context.getSampleInstrumentReportItemTips();
        final Long instrumentId = context.getSample().getInstrumentId();

        final Map<String, List<InstrumentReportItemDto>> instrumentReportItemsMap = instrumentReportItems.stream()
                .collect(Collectors.groupingBy(InstrumentReportItemDto::getReportItemCode));

        final List<String> errorMsgList = new ArrayList<>();

        //******* 新增一审异常结果提醒
        // 如果是一审 并且不是强制审核 则做查询判断
        final SampleAuditDto param = context.getParam();
        if (Objects.equals(context.getParam().getAuditForce(), 0) &&
                Objects.equals(param.getAuditStatus(), SampleAuditStatusEnum.ONE_CHECK.name())) {
            List<SampleAbnormalDto> sampleAbnormalDtoList = sampleAbnormalService.selectByBarcodes(Collections.singletonList(sample.getBarcode()));
            if (CollectionUtils.isNotEmpty(sampleAbnormalDtoList)) {
                errorMsgList.add(String.format("条码号 [%s]的样本存在异常情况！\n", sample.getBarcode()));
            }
        }

        final List<SampleReportItemDto> sampleReportItems = context.getSampleReportItems();

        for (SampleReportItemDto item : sampleReportItems) {

            final SampleResultDto result = sampleResults.stream().filter(e -> Objects.equals(e.getReportItemCode(),
                    item.getReportItemCode())).findFirst().orElse(null);

            final List<InstrumentReportItemDto> sampleInstrumentItems = instrumentReportItems.stream().filter(e -> Objects.equals(e.getInstrumentId(),
                    sample.getInstrumentId())).collect(Collectors.toList());

            //先取样本仪器报告项目
            InstrumentReportItemDto reportItem = sampleInstrumentItems.stream().filter(e -> Objects.equals(e.getReportItemCode(),
                    item.getReportItemCode())).findFirst().orElse(null);
            if (Objects.isNull(reportItem)) {
                reportItem = instrumentReportItems.stream().filter(e -> Objects.equals(e.getReportItemCode(),
                        item.getReportItemCode())).findFirst().orElse(null);
            }

            // 判断是否空参考范围提示
            emptyReferenceTips(context, result, reportItem);

            final List<SampleReportDto> sampleReports = context.getSampleReports();
            // 这里判断结果值不能为空 -- 如果是上传pdf文件那么不需要校验
            if (Objects.nonNull(reportItem) &&
                    (CollectionUtils.isEmpty(sampleReports) || CollectionUtils.isNotEmpty(sampleReports)
                    && !Objects.equals(sampleReports.iterator().next().getIsUploadPdf(), YesOrNoEnum.YES.getCode()))) {
                if (Objects.equals(reportItem.getIsResultNull(), YesOrNoEnum.NO.getCode())) {
                    //新增判断 如果没有匹配到结果值，但是该报告项目结果不能为空 同样需要报错 TAPD【ID1000806】
                    if (Objects.isNull(result) || StringUtils.isBlank(result.getResult())) {
                        throw new IllegalStateException(String.format("样本号 [%s] 报告项目 [%s] 结果值为空", sample.getSampleNo(), item.getReportItemName()));
                    }
                }

                if (!Objects.isNull(result) && NumberUtils.isParsable(result.getResult())) {
                    if (BigDecimal.ZERO.compareTo(NumberUtils.toScaledBigDecimal(result.getResult(), 6, RoundingMode.HALF_EVEN)) == 0
                            && Objects.equals(reportItem.getIsResultZero(), YesOrNoEnum.NO.getCode())) {
                        throw new IllegalStateException(String.format("样本号 [%s] 报告项目 [%s] 结果值为零", sample.getSampleNo(), item.getReportItemName()));
                    }
                }
            }

            if (Objects.isNull(result)) {
                continue;
            }

            // fix：dev-******* 优先取当前仪器的 否则专业小组下取第一个  使用和 SampleResultController.querySampleReportItemDetails 中获取仪器一样的方法
            final InstrumentReportItemDto instrumentReportItem = ObjectUtils.defaultIfNull(InstrumentReportItemDto.getInstrumentReportItemDto(instrumentReportItemsMap, item.getReportItemCode(), instrumentId), new InstrumentReportItemDto());

            final List<InstrumentReportItemResultTipDto> itemTips = tips.stream()
                    // 过滤当前仪器
                    .filter(e -> Objects.equals(e.getInstrumentId(), instrumentReportItem.getInstrumentId()))
                    .filter(e -> ArrayUtils.contains(e.getReportItemCode().split(","), item.getReportItemCode())
                            || Objects.equals(e.getReportItemCode(), "0"))
                    .collect(Collectors.toList());

            for (InstrumentReportItemResultTipDto itemTip : itemTips) {
                curTip(item, itemTip, result, errorMsgList);
            }

        }
        //常规检验审核提示异常码20000
        if (Objects.equals(context.getParam().getAuditForce(), 0) && (CollectionUtils.isNotEmpty(errorMsgList))) {
            throw new LimsCodeException(ExceptionCodeEnum.ROUTINE_AUDIT.getCode(), JSON.toJSONString(errorMsgList));
        }

    }

    /**
     * 判断空参考范围
     */
    private static void emptyReferenceTips(AuditSampleContext context, SampleResultDto result, InstrumentReportItemDto reportItem) {
        try {
            // 判断是否空参考范围提示
            if (Objects.nonNull(result) && Objects.nonNull(result.getSampleResultId()) && Objects.nonNull(reportItem) && EmptyReferenceTipEnum.needInterception(reportItem.getEmptyReferenceTip())) {
                final Long instrumentReportItemReferenceId = result.getInstrumentReportItemReferenceId();
                if (Objects.isNull(instrumentReportItemReferenceId) || Objects.equals(NumberUtils.LONG_ZERO, instrumentReportItemReferenceId)) {
                    switch (reportItem.getEmptyReferenceTip()) {
                        case 1:
                            // 禁止审核
                            context.getEmptyReferenceResultForbidden().add(result);
                            break;
                        case 2:
                            // 审核提示
                            context.getEmptyReferenceResultWarning().add(result);
                            break;
                    }
                }
            }
        } catch (Exception ignored) {
        }
    }

    private void curTip(SampleReportItemDto item, InstrumentReportItemResultTipDto itemTip, SampleResultDto result, List<String> errorMsgList) {
        final RelationalOperatorEnum operatorMaxEnum = RelationalOperatorEnum.valueOfByOperator(itemTip.getFormulaMax());

        // 包含
        if (Objects.equals(operatorMaxEnum, RelationalOperatorEnum.CONTAINS)) {
            if (StringUtils.contains(result.getResult(), itemTip.getFormulaMaxValue())) {
                errorMsgList.add(String.format("报告项目 [%s]\n提示内容 [%s]", item.getReportItemName(), itemTip.getTipContent()));
            }
        } else if (Objects.equals(operatorMaxEnum, RelationalOperatorEnum.DOES_NOT_CONTAINS)) { // 不包含
            if (!StringUtils.contains(result.getResult(), itemTip.getFormulaMaxValue())) {
                errorMsgList.add(String.format("报告项目 [%s]\n提示内容 [%s]", item.getReportItemName(), itemTip.getTipContent()));
            }
        } else {

            // 如果都是数字的时候，那么转成数字去匹配
            Object value = result.getResult();
            Object maxValue = itemTip.getFormulaMaxValue();
            if (NumberUtils.isParsable(result.getResult())
                    && NumberUtils.isParsable(itemTip.getFormulaMaxValue())) {
                value = new BigDecimal(result.getResult());
                maxValue = new BigDecimal(itemTip.getFormulaMaxValue());
            }


            if (Objects.equals(operatorMaxEnum, RelationalOperatorEnum.EQ)) {
                if (Objects.equals(value, maxValue)) {
                    errorMsgList.add(String.format("报告项目 [%s]\n提示内容 [%s]", item.getReportItemName(), itemTip.getTipContent()));
                } else if (value instanceof BigDecimal && ((BigDecimal) value).compareTo((BigDecimal) maxValue) == NumberUtils.INTEGER_ZERO) {
                    errorMsgList.add(String.format("报告项目 [%s]\n提示内容 [%s]", item.getReportItemName(), itemTip.getTipContent()));
                }
                return;
            }

            // 判断大小的时候必须是数字
            if (!NumberUtils.isParsable(result.getResult()) || !NumberUtils.isParsable(itemTip.getFormulaMaxValue())) {
                return;
            }
            // 第二个范围不等于空 但是不是数字就返回
            if (StringUtils.isNotBlank(itemTip.getFormulaMinValue()) && !NumberUtils.isParsable(itemTip.getFormulaMinValue())) {
                return;
            }


            if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.LT) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) < NumberUtils.INTEGER_ZERO)) {
                errorMsgList.add(String.format("报告项目 [%s]\n提示内容 [%s]", item.getReportItemName(), itemTip.getTipContent()));
            } else if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.LE) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) <= NumberUtils.INTEGER_ZERO)) {
                errorMsgList.add(String.format("报告项目 [%s]\n提示内容 [%s]", item.getReportItemName(), itemTip.getTipContent()));
            }


            BigDecimal minValue = NumberUtils.isParsable(itemTip.getFormulaMinValue()) ? new BigDecimal(itemTip.getFormulaMinValue()) : null;

            if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.GT) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) > NumberUtils.INTEGER_ZERO)) {
                curMinTip(item.getReportItemName(), minValue, errorMsgList, itemTip, (BigDecimal) value);
            } else if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.GE) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) >= NumberUtils.INTEGER_ZERO)) {
                curMinTip(item.getReportItemName(), minValue, errorMsgList, itemTip, (BigDecimal) value);
            }

        }
    }

    private void curMinTip(String reportItemName, BigDecimal minValue, List<String> errorMsgList, InstrumentReportItemResultTipDto itemTip, BigDecimal value) {
        final RelationalOperatorEnum operatorMinEnum = RelationalOperatorEnum.valueOfByOperator(itemTip.getFormulaMin());

        if (Objects.isNull(operatorMinEnum) || Objects.isNull(minValue)) {
            errorMsgList.add(String.format("报告项目 [%s]\n提示内容 [%s]", reportItemName, itemTip.getTipContent()));
            return;
        }

        if (Objects.equals(operatorMinEnum, RelationalOperatorEnum.LT) && (value.compareTo(minValue) < NumberUtils.INTEGER_ZERO)) {
            errorMsgList.add(String.format("报告项目 [%s]\n提示内容 [%s]", reportItemName, itemTip.getTipContent()));
        } else if (Objects.equals(operatorMinEnum, RelationalOperatorEnum.LE) && (value.compareTo(minValue) <= NumberUtils.INTEGER_ZERO)) {
            errorMsgList.add(String.format("报告项目 [%s]\n提示内容 [%s]", reportItemName, itemTip.getTipContent()));
        }
    }


}


