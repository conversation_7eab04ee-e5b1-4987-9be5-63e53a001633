package com.labway.lims.routine.vo;

import com.labway.lims.api.enums.apply.SampleAbnormalStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/3/28 09:59
 */
@Getter
@Setter
public class AbnormalSampleVo {

    /**
     * 异常值ID
     */
    private Long sampleAbnormalId;


    /**
     * 异常原因编码
     */
    private String abnormalReasonCode;

    /**
     * 异常原因描述
     */
    private String abnormalReasonName;
    /**
     * 登记内容
     */
    private String registContent;

        /**
     * 状态:0未处理，1已处理，2已确认，3已作废
     *
     * @see SampleAbnormalStatusEnum
     */
    private Integer status;

    /**
     * 异常数量
     */
    private Integer count;

    /**
     * 异常值ID集合
     */
    private Set<Long> sampleAbnormalIds;

}
