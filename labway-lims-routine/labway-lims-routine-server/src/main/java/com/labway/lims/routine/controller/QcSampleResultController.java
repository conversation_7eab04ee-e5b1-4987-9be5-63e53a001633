package com.labway.lims.routine.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.routine.api.dto.AddQcSampleResultDto;
import com.labway.lims.routine.api.dto.QueryQcSampleResultDto;
import com.labway.lims.routine.api.service.QcSampleResultService;
import com.labway.lims.routine.vo.AddQcSampleResultVo;
import com.labway.lims.routine.vo.QueryQcSampleResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * QcSampleResultController
 * 质控结果控制层
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/2 14:18
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping("/qc-sample-result")
public class QcSampleResultController extends BaseController implements InitializingBean {

    @Resource
    private QcSampleResultService qcSampleResultService;

    /**
     * 查询列表
     */
    @PostMapping("/select")
    public Object selectQcSampleResult(@RequestBody QueryQcSampleResultVo param) {
        param.checkParam();
        return qcSampleResultService.selectQcSampleResult(
                JSON.parseObject(JSON.toJSONString(param), QueryQcSampleResultDto.class));
    }

    /**
     * 添加质控结果
     */
    @PostMapping("/add")
    public Object addQcSampleResult(@RequestBody AddQcSampleResultVo param) {
        param.checkParam();
        List<Long> ids = qcSampleResultService.addQcSampleResult(
                JSON.parseObject(JSON.toJSONString(param), AddQcSampleResultDto.class));

        return Map.of("ids", ids);
    }

    /**
     * 删除质控结果
     */
    @PostMapping("/delete")
    public Object deleteQcSampleResult(@RequestBody List<Long> ids) {
        qcSampleResultService.physicsDeleteByIds(ids);
        return Collections.emptyMap();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }
}
