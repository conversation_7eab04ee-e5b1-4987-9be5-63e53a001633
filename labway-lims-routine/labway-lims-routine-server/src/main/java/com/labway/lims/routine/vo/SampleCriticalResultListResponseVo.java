package com.labway.lims.routine.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.enums.routine.ReadBackStatusEnum;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/4/10 11:01
 */
@Getter
@Setter
public class SampleCriticalResultListResponseVo extends SampleCriticalResultDto {

    private static final long serialVersionUID = 1L;

    /**
     * 样本号
     */
    private String sampleNo;
    /**
     * 项目类型 常规检验、微生物检验、院感检验 区分做什么检验的
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 送检医生名称
     */
    private String sendDoctorName;

    /**
     * 送检医生编码
     */
    private String sendDoctorCode;

    /**
     * 一审人
     */
    private String oneCheckerName;
    /**
     * 一审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date oneCheckDate;
    /**
     * 二次审核人名称
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date twoCheckDate;

    /**
     * 是否回读
     * @see ReadBackStatusEnum
     */
    private Integer isReadBack;

    /**
     * 回读用户
     */
    private String readBackUser;

    /**
     * 回读时间
     */
    private Date readBackTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否超时
     * @see YesOrNoEnum
     */
    private Integer isTimeOut;

    public Object getHandleDateText() {
        if (Objects.isNull(getHandleDate())) {
            return StringUtils.EMPTY;
        }

        if (Objects.equals(getHandleDate(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
            return StringUtils.EMPTY;
        }

        return super.getHandleDate();
    }

    public Object getTestDateText() {
        if (Objects.isNull(getTestDate())) {
            return StringUtils.EMPTY;
        }

        if (Objects.equals(getTestDate(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
            return StringUtils.EMPTY;
        }

        return DateFormatUtils.format(super.getTestDate(), "yyyy-MM-dd");
    }
}
