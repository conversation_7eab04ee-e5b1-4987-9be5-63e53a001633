package com.labway.lims.routine.service.chain.result.retest.provider;

import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import com.labway.lims.routine.service.chain.result.NumberResultCommand;
import com.labway.lims.routine.service.chain.result.SaveResultContext;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Setter
@Component
public class RetestResultProvider implements NumberResultCommand.ResultProvider, Command {

    @Resource
    private SampleRetestItemService sampleRetestItemService;
    @Resource
    private SampleRetestMainService sampleRetestMainService;


    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext from = SaveResultContext.from(c);

        from.put(SaveResultContext.RESULT_PROVIDER, this);

        return CONTINUE_PROCESSING;
    }

    /**
     * 当前项目没有复查时会调用
     * 所以查询样本复查结果会不会有关联的报告项目结果
     */
    @Override
    public List<NumberResultCommand.Result> get(SaveResultContext context) {
        final List<SampleRetestItemDto> items = sampleRetestItemService.selectBySampleIdAndTestItemId(context.getSample().getSampleId(), context.getSampleReportItem().getTestItemId());
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }
        return items.stream().map(m -> {
            NumberResultCommand.Result result = new NumberResultCommand.Result();
            result.setTestResult(m.getResult());
            result.setSampleReportItemCode(m.getReportItemCode());
            return result;
        }).collect(Collectors.toList());
    }
}
