package com.labway.lims.routine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.routine.model.TbQcSampleResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Mapper
public interface TbQcSampleResultMapper extends BaseMapper<TbQcSampleResult> {

    /**
     * 批量 插入
     */
    void batchAddQcQcSampleResult(@Param("conditions") List<TbQcSampleResult> conditions);

    /**
     * 物理删除（根据ID或实体 批量删除）
     *
     */
    int physicsDeleteBatchIds(@Param("ids") Collection<?> idList);

}
