package com.labway.lims.routine.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.routine.api.dto.MeiBiaoValueDTO;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.dto.SaveResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.routine.enums.SymbolEnum;
import com.labway.lims.routine.service.chain.result.InstrumentReportReferenceCommand;
import com.labway.lims.routine.service.qc.QcResultService;
import com.labway.lims.routine.vo.HistoryResultVo;
import com.labway.lims.routine.vo.HistoryResultsChartQueryVo;
import com.labway.lims.routine.vo.HistoryResultsChartVo;
import com.labway.lims.routine.vo.InstrumentResultsTrendRequest;
import com.labway.lims.routine.vo.InstrumentResultsTrendResponseVo;
import com.labway.lims.routine.vo.ResultRecordComparesVo;
import com.labway.lims.routine.vo.SampleReportItemDetailVo;
import com.labway.lims.routine.vo.SampleResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 样本结果 API
 *
 * <AUTHOR>
 * @since 2023/3/30 09:54
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping("/sample-result")
public class SampleResultController extends BaseController implements InitializingBean {
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private BloodCultureSampleService bloodCultureSampleService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    private SampleRetestMainService sampleRetestMainService;
    @Resource
    private SampleRetestItemService sampleRetestItemService;

    @Resource
    private ApplicationContext applicationContext;

    private static final Map<String, QcResultService> qcResultServiceMap = new HashMap<>();

    @Value("${result-service.version:1.0}")
    private String resultServiceVersion;

    @PostMapping("/results")
    public Object selectResults(@RequestParam("sampleId") Long sampleId) {
        if (Objects.isNull(sampleId)) {
            return Collections.emptyMap();
        }

        return sampleResultService.selectBySampleId(sampleId);
    }

    @PostMapping("/update-result")
    public Object updateResult(@RequestBody SampleResultVo vo) {

        final SampleDto sampleDto = sampleService.selectBySampleId(vo.getSampleId());
        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("样本不存在");
        }

        if (Objects.isNull(vo.getReportItemId())) {
            throw new IllegalArgumentException("ReportItemId 不能为空");
        }

        if (StringUtils.isBlank(vo.getReportItemCode())) {
            throw new IllegalArgumentException("ReportItemCode 不能为空");
        }

        if (StringUtils.length(vo.getResult()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("结果长度不能大于50");
        }

        final SaveResultDto dto = new SaveResultDto();
        dto.setSampleId(vo.getSampleId());
        dto.setApplySampleId(sampleDto.getApplySampleId());
        dto.setApplyId(sampleDto.getApplyId());
        dto.setReportItemId(vo.getReportItemId());
        dto.setReportItemCode(vo.getReportItemCode());
        dto.setResult(vo.getResult());
        dto.setDate(new Date());
        dto.setSampleNo(sampleDto.getSampleNo());
        dto.setGroupId(sampleDto.getGroupId());
        dto.setInstrumentId(sampleDto.getInstrumentId());
        dto.setIsHandeResult(vo.getIsHandeResult());

        final String key = redisPrefix.getBasePrefix() + "update-result:" + vo.getSampleId();

        if (BooleanUtils
                .isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofSeconds(5)))) {
            throw new IllegalArgumentException("正在修改结果中");
        }

        try {
            return sampleResultService.saveResult(dto, SaveResultSourceEnum.FRONT);
        } finally {
            stringRedisTemplate.delete(key);
        }

    }

    /**
     * 历史结果折线图
     */
    @PostMapping("/history-results-chart")
    public Object historyResultsChart(@RequestBody HistoryResultsChartQueryVo vo) {

        if (Objects.isNull(vo.getSampleId()) || Objects.isNull(vo.getReportItemId())) {
            throw new IllegalStateException("参数错误");
        }

        final SampleDto sample = sampleService.selectBySampleId(vo.getSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("当前样本不存在");
        }

        final ApplyDto apply = applyService.selectByApplyId(sample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        query.setIsAudit(YesOrNoEnum.YES.getCode());
        query.setItemTypes(Collections.singleton(ItemTypeEnum.ROUTINE.name()));
        query.setStartTwoPickDate(vo.getDateStart());
        query.setEndTwoPickDate(applySample.getTwoPickDate());

        //处理同人同天
        query.combineOneDayOnePersonParam(apply);

        final List<RoutineInspectionDto> routineSamples =
                elasticSearchSampleService.selectSamples(query).stream().filter(RoutineInspectionDto.class::isInstance)
                        .map(e -> (RoutineInspectionDto) e).collect(Collectors.toList());
        // 移除当前样本
        routineSamples.removeIf(e -> Objects.isNull(e.getSampleId()));
        routineSamples.removeIf(e -> Objects.equals(e.getSampleId(), sample.getSampleId()));
        if (CollectionUtils.isEmpty(routineSamples)) {
            return Collections.emptyList();
        }

        final HistoryResultsChartVo chartVo = new HistoryResultsChartVo();
        chartVo.setReportItemId(vo.getReportItemId());
        chartVo.setReportItemName(StringUtils.EMPTY);
        chartVo.setReportItemCode(vo.getReportItemCode());
        chartVo.setResults(new LinkedList<>());

        // 日期排序 日期越大越靠后
        routineSamples.sort((o1, o2) -> {
            if (Objects.isNull(o1.getSampleId()) || Objects.isNull(o2.getSampleId())) {
                return NumberUtils.INTEGER_ZERO;
            }
            return o1.getSampleId().compareTo(o2.getSampleId());
        });

        for (RoutineInspectionDto e : routineSamples) {
            if (Objects.isNull(e.getTestDate())) {
                continue;
            }

            if (Objects.isNull(e.getReportItems())) {
                continue;
            }

            for (RoutineInspectionDto.RoutineReportItem item : e.getReportItems()) {
                if (!Objects.equals(item.getReportItemCode(), vo.getReportItemCode())) {
                    continue;
                }

                if (!NumberUtils.isParsable(item.getResult())) {
                    continue;
                }

                final HistoryResultsChartVo.ResultVo v = new HistoryResultsChartVo.ResultVo();
                v.setDate(DateFormatUtils.format(e.getTestDate(), "yyyy-MM-dd"));
                v.setResult(item.getResult());
                v.setStatus(item.getStatus());
                chartVo.getResults().add(v);

            }
        }

        final SampleResultDto sampleResult =
                sampleResultService.selectBySampleIdAndReportItemId(vo.getSampleId(), vo.getReportItemId());
        if (Objects.nonNull(sampleResult) && (NumberUtils.isParsable(sampleResult.getResult()))) {
            final HistoryResultsChartVo.ResultVo currentResult = new HistoryResultsChartVo.ResultVo();
            currentResult.setDate("当前");
            currentResult.setResult(sampleResult.getResult());
            currentResult.setStatus(sampleResult.getStatus());
            chartVo.getResults().add(currentResult);
        }

        return chartVo;
    }

    /**
     * 历史结果对比
     */
    @PostMapping("/result-record-compares")
    public Collection<HistoryResultVo> resultRecordCompares(@RequestBody ResultRecordComparesVo vo) {

        if (Objects.isNull(vo.getSampleId())) {
            return Collections.emptyList();
        }
        Long applyId;
        Long applySampleId;
        if (Objects.equals(vo.getItemType(), ItemTypeEnum.MICROBIOLOGY.name())) {
            // 传了 微生物样本id
            MicrobiologySampleDto sample = microbiologySampleService.selectByMicrobiologySampleId(vo.getSampleId());
            if (Objects.isNull(sample)) {
                return Collections.emptyList();
            }
            applyId = sample.getApplyId();
            applySampleId = sample.getApplySampleId();
        } else if (Objects.equals(vo.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())) {
            // 传了 微生物样本id
            BloodCultureSampleDto sample = bloodCultureSampleService.selectByBloodCultureSampleId(vo.getSampleId());
            if (Objects.isNull(sample)) {
                return Collections.emptyList();
            }
            applyId = sample.getApplyId();
            applySampleId = sample.getApplySampleId();
        } else {
            SampleDto sample = sampleService.selectBySampleId(vo.getSampleId());
            if (Objects.isNull(sample)) {
                return Collections.emptyList();
            }
            applyId = sample.getApplyId();
            applySampleId = sample.getApplySampleId();
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            return Collections.emptyList();
        }

        final ApplyDto apply = applyService.selectByApplyId(applyId);
        if (Objects.isNull(apply)) {
            return Collections.emptyList();
        }

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        // 【【常规检验】历史结果-常规，一审的结果可以显示】
        // https://www.tapd.cn/tapd_fe/59091617/story/detail/1159091617001002664
        query.setSampleStatus(new HashSet<>() {{
            add(SampleStatusEnum.ONE_AUDIT.getCode());
            add(SampleStatusEnum.AUDIT.getCode());
        }});
        query.setItemTypes(Collections.singleton(ItemTypeEnum.ROUTINE.name()));

        LoginUserHandler.User user = LoginUserHandler.get();
        Long groupId = user.getGroupId();

        //处理同人同天
        query.combineOneDayOnePersonParam(apply);

        final List<RoutineInspectionDto> routineSamples = elasticSearchSampleService.selectSamples(query)

                .stream().filter(RoutineInspectionDto.class::isInstance).map(e -> (RoutineInspectionDto) e)
                .collect(Collectors.toList());

        //过滤样本ID为空的
        routineSamples.removeIf(e -> Objects.isNull(e.getSampleId()));

        if (CollectionUtils.isEmpty(routineSamples)) {
            return Collections.emptyList();
        }
        // 日期排序 日期越大越靠前
        routineSamples.sort((a, b) -> b.getApplySampleId().compareTo(a.getApplySampleId()));


        // 所有结果最终 key: 报告项目 code value: 报告项目名称
        final Map<String, String> reportItemCodeByReportItemName =
                routineSamples.stream().map(sample -> Optional.ofNullable(sample.getReportItems()))
                        .filter(Optional::isPresent).flatMap(optionalItems -> optionalItems.get().stream())
                        .collect(Collectors.toMap(BaseSampleEsModelDto.ReportItem::getReportItemCode,
                                BaseSampleEsModelDto.ReportItem::getReportItemName, (a, b) -> a));

        // 当前用户专业组 下所有仪器
        final Set<Long> instrumentIds = instrumentService.selectByGroupId(groupId).stream()
                .map(InstrumentDto::getInstrumentId).collect(Collectors.toSet());

        // key: 报告项目 code value: 排序最小
        final Map<String, Integer> printSortByReportItemCode = instrumentReportItemService
                .selectByInstrumentIdsAndReportItemCodes(instrumentIds, reportItemCodeByReportItemName.keySet())
                .stream().filter(obj -> obj.getPrintSort() != null).collect(Collectors.toMap(
                        InstrumentReportItemDto::getReportItemCode, InstrumentReportItemDto::getPrintSort, Integer::min));

        // 以打印顺序 升序 排序 相同 时以 报告项目编码排序
        final LinkedHashMap<String, String> sortReportItems = reportItemCodeByReportItemName.entrySet().stream()
                .sorted(Comparator.comparing((Map.Entry<String, String> obj) -> ObjectUtils
                                .defaultIfNull(printSortByReportItemCode.get(obj.getKey()), Integer.MAX_VALUE))
                        .thenComparing(Map.Entry::getKey)) // 在值相同时按照键升序排序
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        final List<HistoryResultVo> resultVos = Lists.newArrayList();

        for (RoutineInspectionDto hs : routineSamples) {
            hs.setReportItems(ObjectUtils.defaultIfNull(hs.getReportItems(), List.of()));
            final HistoryResultVo resultVo = new HistoryResultVo();
            resultVo.setSampleNo(hs.getSampleNo());
            resultVo.setBarcode(hs.getBarcode());
            resultVo.setTestDate(DateUtil.formatDate(hs.getTestDate()));
            resultVo.setResults(Lists.newArrayList());

            final Map<String, RoutineInspectionDto.RoutineReportItem> reportItemByCode = hs.getReportItems().stream().collect(
                    Collectors.toMap(BaseSampleEsModelDto.ReportItem::getReportItemCode, Function.identity(), (a, b) -> a));
            for (Map.Entry<String, String> entry : sortReportItems.entrySet()) {
                final HistoryResultVo.ResultVo resVo = new HistoryResultVo.ResultVo();
                resVo.setReportItemCode(entry.getKey());
                resVo.setReportItemName(entry.getValue());
                RoutineInspectionDto.RoutineReportItem routineReportItem = reportItemByCode.get(entry.getKey());
                if (Objects.nonNull(routineReportItem)) {
                    resVo.setResult(routineReportItem.getResult());
                    resVo.setStatus(routineReportItem.getStatus());
                    resVo.setJudge(routineReportItem.getJudge());
                }
                resultVo.getResults().add(resVo);
            }

            resultVos.add(resultVo);
        }

        return resultVos.stream().filter(obj -> CollectionUtils.isNotEmpty(obj.getResults()))
                .collect(Collectors.toList());
    }

    /**
     * 历史结果对比 数量
     */
    @PostMapping("/result-record-compares-count")
    public Object resultRecordComparesCount(@RequestBody ResultRecordComparesVo vo) {
        return Map.of("count", this.resultRecordCompares(vo).size());
    }

    /**
     * 仪器结果趋势图
     */
    @PostMapping("/instrument-results-trend")
    public Object instrumentResultsTrend(@RequestBody InstrumentResultsTrendRequest request) {


        request.setCount(ObjectUtils.defaultIfNull(request.getCount(), 100));

        final SampleDto sample = sampleService.selectBySampleId(request.getSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        final SampleResultDto sampleResult = sampleResultService.selectBySampleIdAndReportItemCode(request.getSampleId(),
                request.getReportItemCode());
        // 仪器ID
        long instrumentId = 0;

        if (Objects.nonNull(sampleResult)) {
            instrumentId = sampleResult.getInstrumentId();
        }

        // 如果结果为空或者是手动输入的，那么取二次分拣的仪器
        if (Objects.isNull(sampleResult) || Objects.equals(instrumentId, NumberUtils.LONG_ZERO)) {
            // 这个专业小组下所有报告项目
            final Map<String, List<InstrumentReportItemDto>> instrumentReportItems =
                    instrumentReportItemService.selectByInstrumentGroupId(sample.getInstrumentGroupId()).stream()
                            .collect(Collectors.groupingBy(InstrumentReportItemDto::getReportItemCode));
            final InstrumentReportItemDto instrumentReportItem = InstrumentReportItemDto.getInstrumentReportItemDto(instrumentReportItems, request.getReportItemCode(), sample.getInstrumentId());

            // 获取到仪器ID
            if (Objects.isNull(instrumentReportItem)) {
                instrumentId = sample.getInstrumentId();
            } else {
                instrumentId = instrumentReportItem.getInstrumentId();
            }
        }

        // 没有获取到仪器ID
        if (Objects.equals(instrumentId, NumberUtils.LONG_ZERO)) {
            return List.of();
        }


        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.itemTypes(Collections.singleton(ItemTypeEnum.ROUTINE.name()));
        builder.startTwoPickDate(request.getBeginDate());
        builder.endTwoPickDate(request.getEndDate());
        builder.pageSize(5000);
        final SampleEsQuery query = builder.build();

        List<Object> searchAfter = Collections.emptyList();

        final List<InstrumentResultsTrendResponseVo> list = new ArrayList<>();

        // 判断是否填充满了
        final Supplier<Boolean> isFull = () -> list.size() >= request.getCount();

        do {

            final ScrollPage<BaseSampleEsModelDto> scrollPage = elasticSearchSampleService.searchAfter(searchAfter,
                    query);
            searchAfter = scrollPage.getSearchAfter();
            if (CollectionUtils.isEmpty(scrollPage.getData())) {
                break;
            }

            for (BaseSampleEsModelDto e : scrollPage.getData()) {

                if (isFull.get()) {
                    break;
                }

                if (!(e instanceof RoutineInspectionDto)) {
                    continue;
                }

                final RoutineInspectionDto k = (RoutineInspectionDto) e;
                final List<RoutineInspectionDto.RoutineReportItem> reportItems = k.getReportItems();
                if (CollectionUtils.isEmpty(reportItems)) {
                    continue;
                }

                for (RoutineInspectionDto.RoutineReportItem reportItem : reportItems) {

                    if (isFull.get()) {
                        break;
                    }

                    if (!Objects.equals(reportItem.getReportItemCode(), request.getReportItemCode())) {
                        continue;
                    }

                    if (!Objects.equals(reportItem.getInstrumentId(), instrumentId)) {
                        continue;
                    }

                    if (Objects.isNull(reportItem.getSampleResultId())) {
                        continue;
                    }

                    if (!NumberUtils.isParsable(reportItem.getResult())) {
                        continue;
                    }

                    final InstrumentResultsTrendResponseVo v = new InstrumentResultsTrendResponseVo();
                    v.setSampleResultId(reportItem.getSampleResultId());
                    v.setDate(reportItem.getCreateDate());
                    v.setResult(reportItem.getResult());
                    list.add(v);


                }

            }

        } while (Objects.nonNull(searchAfter) && !isFull.get());

        return list.stream().sorted(Comparator.comparing(InstrumentResultsTrendResponseVo::getSampleResultId))
                .collect(Collectors.toList());
    }

    /**
     * 结果详情列表
     */
    @PostMapping("/query-sample-report-item-details")
    public Object querySampleReportItemDetails(@RequestParam("sampleId") Long sampleId, @RequestParam(required = false) String testItemCodes)
            throws ExecutionException, InterruptedException, TimeoutException {
        if (Objects.isNull(sampleId)) {
            return Collections.emptyList();
        }

        // 查询样本
        final SampleDto sample = sampleService.selectBySampleId(sampleId);
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        // 质控结果直接返回
        if (Objects.equals(sample.getApplySampleId(), NumberUtils.LONG_ZERO)
                && Objects.equals(sample.getApplyId(), NumberUtils.LONG_ZERO)) {
            return qcResultServiceMap.get(resultServiceVersion).qcResult(sample);
        }

        // 查询报告项目
        final Future<List<SampleReportItemDto>> reportItemsFuture =
                threadPoolConfig.getPool().submit(() -> sampleReportItemService.selectBySampleId(sampleId));

        // 查询结果
        final Future<Map<String, SampleResultDto>> resultsFuture =
                threadPoolConfig.getPool().submit(() -> sampleResultService.selectBySampleId(sampleId).stream()
                        .collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a)));

        final List<SampleReportItemDto> sampleReportItems = reportItemsFuture.get(10, TimeUnit.SECONDS);
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            resultsFuture.cancel(true);
            return Collections.emptyList();
        }

        // 结果
        final Map<String, SampleResultDto> results = resultsFuture.get(10, TimeUnit.SECONDS);

        // 申请单
        final Future<ApplyDto> apply = threadPoolConfig.getPool().submit(() -> applyService.selectByApplyId(sample.getApplyId()));
        // 申请单样本
        final Future<ApplySampleDto> applySample = threadPoolConfig.getPool()
                .submit(() -> applySampleService.selectByApplySampleId(sample.getApplySampleId()));
        // 获取历史结果
        final Future<Map<String, RoutineInspectionDto.RoutineReportItem>> lastResults =
                threadPoolConfig.getPool().submit(() -> getLastResults(apply, applySample));
        // 参考范围
        final Future<Map<String, List<InstrumentReportItemReferenceDto>>> refs = threadPoolConfig.getPool().submit(() -> instrumentReportItemReferenceService.selectByInstrumentGroupId(sample.getInstrumentGroupId())
                .stream().collect(Collectors.groupingBy(InstrumentReportItemReferenceDto::getReportItemCode)));

        // 复查子表
        final Future<Map<String, List<SampleRetestItemDto>>> sampleRetestItemFuture;
        // 如果有正在复查中的，那么需要查询到复查结果
        if (sampleReportItems.stream()
                .anyMatch(e -> Objects.equals(e.getIsRetest(), RetestStatusEnum.RETESTING.getCode()))) {
            sampleRetestItemFuture = threadPoolConfig.getPool().submit(() -> {
                final SampleRetestMainDto sampleRetestMain = sampleRetestMainService.selectBySampleId(sampleId).stream()
                        .filter(e -> Objects.equals(e.getStatus(), SampleRetestStatusEnum.NORMAL.getCode())).findFirst()
                        .orElse(null);
                if (Objects.isNull(sampleRetestMain)) {
                    return Map.of();
                }
                return sampleRetestItemService.selectBySampleRetestMainId(sampleRetestMain.getSampleRetestMainId())
                        .stream().collect(Collectors.groupingBy(SampleRetestItemDto::getReportItemCode));
            });
        } else {
            sampleRetestItemFuture = CompletableFuture.completedFuture(Map.of());
        }

        // 这个专业小组下所有报告项目
        final Map<String, List<InstrumentReportItemDto>> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentGroupId(sample.getInstrumentGroupId()).stream()
                        .collect(Collectors.groupingBy(InstrumentReportItemDto::getReportItemCode));

        final List<SampleReportItemDetailVo> list = new LinkedList<>();

        final Long instrumentId = sample.getInstrumentId();

        List<String> itemCodes = StringUtils.isNotBlank(testItemCodes) ? List.of(testItemCodes.split(",")) : List.of();
        for (SampleReportItemDto item : sampleReportItems) {
            if (StringUtils.isNotBlank(testItemCodes) && !itemCodes.contains(item.getTestItemCode())) {
                continue;
            }
            final String reportItemCode = item.getReportItemCode();

            final SampleReportItemDetailVo vo = new SampleReportItemDetailVo();
            vo.setSampleId(item.getSampleId());
            vo.setSampleReportItemId(item.getSampleReportItemId());
            vo.setReportItemId(item.getReportItemId());
            vo.setReportItemCode(reportItemCode);
            vo.setReportItemName(item.getReportItemName());
            vo.setTestItemId(item.getTestItemId());
            vo.setTestItemName(item.getTestItemName());
            vo.setTestItemCode(item.getTestItemCode());
            vo.setIsRetest(item.getIsRetest());
            vo.setPrintSort(ObjectUtils.defaultIfNull(item.getPrintSort(), Integer.MAX_VALUE));

            // 优先取当前仪器的 否则专业小组下取第一个
            // （tips：这里获取仪器报告项目逻辑修改的话， com.labway.lims.routine.service.chain.audit.CheckSampleResultCommand.checkResult() 中逻辑同样要修改）
            final InstrumentReportItemDto instrumentReportItem = ObjectUtils.defaultIfNull(InstrumentReportItemDto.getInstrumentReportItemDto(instrumentReportItems, reportItemCode, instrumentId), new InstrumentReportItemDto());

            vo.setUnit(instrumentReportItem.getReportItemUnitName());
            vo.setEnName(instrumentReportItem.getEnName());
            vo.setEnAb(instrumentReportItem.getEnAb());

            vo.setItemTypeCode(instrumentReportItem.getItemTypeCode());
            vo.setItemTypeName(instrumentReportItem.getItemTypeName());

            vo.setInstrumentName(instrumentReportItem.getInstrumentName());

            // 检验方法
            vo.setExamMethodCode(instrumentReportItem.getExamMethodCode());
            vo.setExamMethodName(instrumentReportItem.getExamMethodName());

            if (results.containsKey(reportItemCode)) {
                final SampleResultDto sampleResult = results.get(reportItemCode);
                vo.setIsHandeResult(sampleResult.getIsHandeResult());
                vo.setResult(sampleResult.getResult());
                vo.setRange(sampleResult.getRange());
                vo.setJudge(sampleResult.getJudge());
                vo.setStatus(sampleResult.getStatus());
                vo.setUnit(sampleResult.getUnit());
                if(Objects.nonNull(sampleResult.getExtraInfo())) {
                    MeiBiaoValueDTO meiBiaoValueDTO = JSON.parseObject(sampleResult.getExtraInfo(), MeiBiaoValueDTO.class);
                    vo.setODValue(meiBiaoValueDTO.getODValue());
                    vo.setCutoffValue(meiBiaoValueDTO.getCutoffValue());
                    vo.setScoValue(meiBiaoValueDTO.getScoValue());
                }
                if (StringUtils.isNotBlank(sampleResult.getInstrumentName())) {
                    vo.setInstrumentName(sampleResult.getInstrumentName());
                }

                final InstrumentReportItemReferenceDto ref = refs.get().getOrDefault(vo.getReportItemCode(), List.of())
                        .stream().filter(e -> Objects.equals(e.getInstrumentReportItemReferenceId(), sampleResult.getInstrumentReportItemReferenceId()))
                        .findFirst().orElse(null);
                if (Objects.nonNull(ref)) {
                    vo.setReferValueMax(ref.getReferValueMax());
                    vo.setReferValueMin(ref.getReferValueMin());
                }

                // 如果是在复查的那么返回给前端的是空结果的,有复查结果显示复查结果
                if (Objects.equals(item.getIsRetest(), RetestStatusEnum.RETESTING.getCode())) {
                    vo.setResult(StringUtils.EMPTY);
                    vo.setStatus(ResultStatusEnum.NORMAL.getCode());
                    vo.setJudge(StringUtils.EMPTY);
                }
            }

            list.add(vo);

        }

        // 补没有结果的参考范围
        for (SampleReportItemDetailVo vo : list) {

            // 历史结果
            final String reportItemCode = vo.getReportItemCode();
            final RoutineInspectionDto.RoutineReportItem routineReportItem =
                    lastResults.get(10, TimeUnit.SECONDS).get(reportItemCode);
            if (Objects.nonNull(routineReportItem)) {
                final SampleReportItemDetailVo.ResentResult resentResult = new SampleReportItemDetailVo.ResentResult();
                resentResult.setResult(routineReportItem.getResult());
                resentResult.setJudge(routineReportItem.getJudge());
                resentResult.setStatus(routineReportItem.getStatus());
                vo.setResentResult(resentResult);
            }

            if (results.containsKey(reportItemCode)) {
                continue;
            }

            if (Objects.nonNull(apply.get()) && Objects.nonNull(applySample.get())) {
                List<InstrumentReportItemReferenceDto> references = refs.get().get(reportItemCode);
                if (Objects.isNull(references) || CollectionUtils.isEmpty(references)) {
                    continue;
                }

                final InstrumentReportItemDto instrumentReportItem = InstrumentReportItemDto.getInstrumentReportItemDto(instrumentReportItems, reportItemCode, instrumentId);

                if (Objects.isNull(instrumentReportItem)) {
                    continue;
                }

                // 检验方法
                vo.setExamMethodCode(instrumentReportItem.getExamMethodCode());
                vo.setExamMethodName(instrumentReportItem.getExamMethodName());

                // 优先取当前样本仪器下的，否则取当前专业小组下的默认第一个
                final List<InstrumentReportItemReferenceDto> finalReferences = references.stream().filter(e -> Objects.equals(e.getInstrumentReportItemId(),
                        instrumentReportItem.getInstrumentReportItemId())).collect(Collectors.toList());

                // 获取参考范围
                final InstrumentReportItemReferenceDto ref = instrumentReportReferenceCommand
                        .filterCustomerReportReference(apply.get(), applySample.get(), finalReferences);
                if (Objects.nonNull(ref)) {
                    // 先取中文 英文 中英文  参考范围
                    vo.setRange(
                            StringUtils.defaultIfBlank(ref.getCnRefereValue(), ref.getEnRefereValue()));
                    vo.setRange(
                            StringUtils.defaultIfBlank(vo.getRange(), ref.getCnEnRefereValue()));
                    if (StringUtils.isBlank(vo.getRange())) {
                        if (StringUtils.isNotBlank(ref.getReferValueMin()) && StringUtils.isNotBlank(ref.getReferValueMax())) {
                            String range = ref.getReferValueMin() + "~" + ref.getReferValueMax();
                            vo.setRange(range);
                        } else if (StringUtils.isNotBlank(ref.getReferValueMin())) {
                            vo.setRange(SymbolEnum.getSymbol(ref.getReferValueMinFormula()) + ref.getReferValueMin());
                        } else if (StringUtils.isNotBlank(ref.getReferValueMax())) {
                            vo.setRange(SymbolEnum.getSymbol(ref.getReferValueMaxFormula()) + ref.getReferValueMax());
                        }
                    }
                    vo.setReferValueMax(ref.getReferValueMax());
                    vo.setReferValueMin(ref.getReferValueMin());
                }
            }
        }

        // 如果复查中获取复查列表
        for (SampleReportItemDetailVo vo : list) {
            if (Objects.equals(vo.getIsRetest(), RetestStatusEnum.NORMAL.getCode())) {
                continue;
            }

            final List<SampleRetestItemDto> sampleRetestItems =
                    sampleRetestItemFuture.get(10, TimeUnit.SECONDS).get(vo.getReportItemCode());
            if (CollectionUtils.isEmpty(sampleRetestItems)) {
                continue;
            }

            final SampleRetestItemDto sampleRetestItem =
                    sampleRetestItems.stream().max(Comparator.comparing(SampleRetestItemDto::getSampleRetestItemId))
                            .stream().findFirst().orElse(null);
            if (Objects.isNull(sampleRetestItem)) {
                continue;
            }

            vo.setResult(sampleRetestItem.getResult());
            vo.setStatus(sampleRetestItem.getStatus());
            vo.setJudge(sampleRetestItem.getJudge());

        }

        return list.stream().sorted(Comparator.comparing(SampleReportItemDetailVo::getPrintSort)
                .thenComparing(SampleReportItemDetailVo::getReportItemId)).collect(Collectors.toList());
    }



    /**
     * 获取最近一次历史结果
     */
    public Map<String, RoutineInspectionDto.RoutineReportItem> getLastResults(final Future<ApplyDto> applyFuture,
                                                                              final Future<ApplySampleDto> applySampleFuture)
            throws ExecutionException, InterruptedException, TimeoutException {

        final ApplySampleDto applySample = applySampleFuture.get(10, TimeUnit.SECONDS);
        final ApplyDto apply = applyFuture.get(10, TimeUnit.SECONDS);

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        query.setIsAudit(YesOrNoEnum.YES.getCode());
        query.setItemTypes(Collections.singleton(ItemTypeEnum.ROUTINE.name()));
        // 默认查3个月
        query.setStartTwoPickDate(DateUtils.addMonths(applySample.getTwoPickDate(), -3));
        query.setEndTwoPickDate(applySample.getTwoPickDate());

        List<BaseSampleEsModelDto> sampleEsModels;

        //处理同人同天
        query.combineOneDayOnePersonParam(apply);

        log.info("查询最近一次结果入参[{}]", JSON.toJSONString(query));
        // 查询 es
        sampleEsModels = elasticSearchSampleService.selectSamples(query);
        log.info("返回样本[{}]", JSON.toJSONString(sampleEsModels));

        final List<RoutineInspectionDto> samples =
                sampleEsModels.stream().filter(RoutineInspectionDto.class::isInstance).map(e -> (RoutineInspectionDto) e)
                        .filter(e -> !Objects.equals(e.getApplySampleId(), applySample.getApplySampleId()))
                        .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(samples)) {
            return Map.of();
        }

        // 日期排序 日期越大越靠前
        samples.sort((o1, o2) -> ObjectUtils.defaultIfNull(o2.getSampleId(), Long.MAX_VALUE)
                .compareTo(ObjectUtils.defaultIfNull(o1.getSampleId(), Long.MAX_VALUE)));

        final Map<String, RoutineInspectionDto.RoutineReportItem> lastResults = new HashMap<>();
        for (RoutineInspectionDto sample : samples) {
            final List<RoutineInspectionDto.RoutineReportItem> reportItems = sample.getReportItems();
            if (CollectionUtils.isEmpty(reportItems)) {
                continue;
            }
            for (RoutineInspectionDto.RoutineReportItem reportItem : reportItems) {
                if (lastResults.containsKey(reportItem.getReportItemCode())) {
                    continue;
                }
                lastResults.put(reportItem.getReportItemCode(), reportItem);
            }
        }

        return lastResults;

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        for (QcResultService value : applicationContext.getBeansOfType(QcResultService.class).values()) {
            qcResultServiceMap.put(value.version(), value);
        }
    }
}
