package com.labway.lims.routine.service.chain.retest.cancel;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 取消复查
 */
@Slf4j
@Component
public class CancelRetestChain extends ChainBase implements InitializingBean {

    @Resource
    private CancelRetestFillCommand cancelRetestFillCommand;
    @Resource
    private CancelRetestSampleCriticalResultCommand cancelRetestSampleCriticalResultCommand;
    @Resource
    private CancelRetestSampleRetestItemCommand cancelRetestSampleRetestItemCommand;
    @Resource
    private CancelRetestSampleRetestMainCommand cancelRetestSampleRetestMainCommand;
    @Resource
    private CancelRetestWriteSampleResultCommand cancelRetestWriteSampleResultCommand;

    @Resource
    private CancelRetestMissItemCommand cancelRetestMissItemCommand;
    @Resource
    private CancelRetestSampleCommand cancelRetestSampleCommand;
    @Resource
    private CancelRetestSaveSampleFlowCommand cancelRetestSaveSampleFlowCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 补上下文
        addCommand(cancelRetestFillCommand);

        // 删除复查项目
        addCommand(cancelRetestSampleRetestItemCommand);

        // 修改主复查记录状态，如果是取消最后一个那么可能要删除主复查记录
        addCommand(cancelRetestSampleRetestMainCommand);

        // 修改危机值状态
        addCommand(cancelRetestSampleCriticalResultCommand);

        // 刷结果
        addCommand(cancelRetestWriteSampleResultCommand);

        // 修改复查状态
        addCommand(cancelRetestSampleCommand);

        // 删除 redis 标记
        addCommand(cancelRetestMissItemCommand);

        //条码环节
        addCommand(cancelRetestSaveSampleFlowCommand);

        // 结束
        addCommand(c -> PROCESSING_COMPLETE);
    }
}
