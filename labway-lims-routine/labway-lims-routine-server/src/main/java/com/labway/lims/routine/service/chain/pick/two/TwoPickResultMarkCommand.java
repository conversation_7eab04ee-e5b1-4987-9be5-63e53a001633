package com.labway.lims.routine.service.chain.pick.two;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.routine.api.dto.ResultStatusDto;
import com.labway.lims.routine.service.chain.result.UpdateMissItemCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * 结果标记
 */
@Slf4j
@Component
public class TwoPickResultMarkCommand implements Filter, Command {

    @Resource
    private UpdateMissItemCommand updateMissItemCommand;

    @Override
    public boolean postprocess(Context c, Exception exception) {

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {
        final TwoPickContext context = TwoPickContext.from(c);

        updateMissItemCommand.mark(context.getSample().getSampleId(), context.getReportItems().stream().map(e -> {
            final ResultStatusDto s = new ResultStatusDto();
            s.setReportItemCode(e.getReportItemCode());
            s.setResult(StringUtils.EMPTY);
            s.setJudge(StringUtils.EMPTY);
            s.setIsRetest(YesOrNoEnum.NO.getCode());
            s.setIsException(YesOrNoEnum.NO.getCode());
            s.setIsCritical(YesOrNoEnum.NO.getCode());
            return s;
        }).collect(Collectors.toList()));


        return CONTINUE_PROCESSING;
    }
}
