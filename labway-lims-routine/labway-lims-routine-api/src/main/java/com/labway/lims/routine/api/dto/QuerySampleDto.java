package com.labway.lims.routine.api.dto;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class QuerySampleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * orgId
     */
    private Long orgId;

    /**
     * groupId
     */
    private Long groupId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 检验日期开始
     */
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    private Date testDateEnd;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * instrumentId
     */
    private Long instrumentId;

    /**
     * 是否仅显示检验样本
     */
    private Integer isTestSample;

    /**
     * 样本状态 待审核 待复查 待二审 已审核 终止（99）
     * @see SampleStatusEnum
     */
    private String sampleStatus;

    /**
     * 样本状态 待审核 待复查 待二审 已审核 终止（99）
     * @see SampleStatusEnum
     */
    private Set<Integer> sampleStatusList;

}
