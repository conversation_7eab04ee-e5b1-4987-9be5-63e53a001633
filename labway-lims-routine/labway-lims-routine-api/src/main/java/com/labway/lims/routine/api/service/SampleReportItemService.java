package com.labway.lims.routine.api.service;

import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.routine.api.dto.AddSampleReportItemDto;
import com.labway.lims.routine.api.dto.BarCodeDto;
import com.labway.lims.routine.api.dto.CombinedBillDTO;
import com.labway.lims.routine.api.dto.CommitCombinedBillDTO;
import com.labway.lims.routine.api.dto.SampleReportItemDto;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/3/29 17:35
 */
public interface SampleReportItemService {

    /**
     * 根据sampleId查询报告项目
     *
     * @param sampleId sampleId
     * @return List<SampleReportItemDto>
     */
    @Nonnull
    List<SampleReportItemDto> selectBySampleId(long sampleId);

    /**
     * 批量添加样本报告项目
     *
     * @param sampleReportItems List<SampleReportItemDto>
     * @return Set<Long>
     */
    Set<Long> addSampleReportItems(List<SampleReportItemDto> sampleReportItems);

    Set<Long> addSampleReportItems(List<SampleReportItemDto> sampleReportItems, String operator);

    /**
     * 添加样本报告项目
     */
    long addSampleReportItem(SampleReportItemDto dto);

    /**
     * 根据sampleIds查询报告项目
     */
    List<SampleReportItemDto> selectBySampleIds(Collection<Long> sampleIds);

    @Nullable
    SampleReportItemDto selectBySampleIdAndReportItemCode(long sampleId, String reportItemCode);

    @Nullable
    SampleReportItemDto selectBySampleReportItemId(long sampleReportItemId, long sampleId);

    /**
     * 必须传入 sampleId
     */
    boolean updateBySampleReportItemId(SampleReportItemDto dto);

    void deleteBySampleId(long sampleId);

    void deleteBySampleId(long sampleId, String operator);

    /**
     * 根据 样本ID 删除
     */
    void deleteBySampleIds(Collection<Long> sampleIds);

    /**
     * 根据ID删除
     */
    void deleteBySampleReportItemId(long sampleReportItemId, long sampleId);
    /**
     * 根据ID删除
     */
    void deleteBySampleReportItemIds(Collection<Long> sampleReportItemIds, long sampleId);

    /**
     * @param sampleReportItem 需要包含 sampleId
     * @param sampleReportItemIds 必须是同一个样本下的报告项目
     */
    void updateBySampleReportItemIds(SampleReportItemDto sampleReportItem, Collection<Long> sampleReportItemIds);


    /**
     * 根据检验项目id查询报告项目
     * @param testItemIdsBySampleId key: 样本id value: 需要查询的检验项目
     */
    List<SampleReportItemDto> selectByTestItemIds(Map<Long, List<Long>> testItemIdsBySampleId);

    /**
     * 根据 样本、 检验项目 删除
     * 
     * @param testItemIdsByApplySampleId key: 样本id value: 需要删除的检验项目
     */
    void deleteByTestItemIds(Map<Long, List<Long>> testItemIdsByApplySampleId);

    /**
     * 根据sampleId和reportId查询
     * @param sampleReportItemDtos .sampleId
     * @param sampleReportItemDtos . reportId
     * @return
     */
    List<SampleReportItemDto> selectBySampleIdsAndReportCodes(List<SampleReportItemDto> sampleReportItemDtos);

    /**
     * 合并报告单
     * @param dto
     * @return
     */
    boolean combinedBill(CommitCombinedBillDTO dto);

    /**
     * 将删除的恢复删除
     * @param sampleReportItemIds
     */
    void updateIsDeleteBySampleReportItemIds(Collection<Long> sampleReportItemIds, Collection<Long> applySampleIds);

    /**
     * 取消并单
     */
    boolean cancelCombinedBill(BarCodeDto barCodeDto);


    /**
     * 查询配置的项目map
     * @return
     */
    Map<String, CombinedBillDTO> selectReportCodeMap();

    /**
     * 加项带出报告项目
     */
    List<ReportItemDto> bringoutReportItem(AddSampleReportItemDto addSampleReportItemDto, List<InstrumentReportItemDto> instrumentReportItems);
}
