package com.labway.lims.routine.api.dto;

import com.labway.lims.api.enums.routine.RetestModeEnum;
import com.labway.lims.api.enums.routine.SampleResultTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 危机值关联样本 相关结果记录信息
 *
 * <AUTHOR>
 * @since 2023/4/11 17:40
 */
@Getter
@Setter
public class CriticalResultConnectSampleResultDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 样本ID
     */
    private Long sampleId;
    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 结果
     */
    private String resultValue;
    /**
     * 结果时间
     */
    private Date resultDate;

    /**
     * 样本结果值类型
     */
    private String resultTypeName;
    /**
     * 结果来源
     */
    private String resultFromName;

    public static CriticalResultConnectSampleResultDto getBySampleResultDto(SampleResultDto sampleResultDto) {
        CriticalResultConnectSampleResultDto item = new CriticalResultConnectSampleResultDto();
        item.setSampleId(sampleResultDto.getSampleId());
        item.setReportItemId(sampleResultDto.getReportItemId());
        item.setResultValue(sampleResultDto.getResult());
        item.setResultDate(sampleResultDto.getCreateDate());
        item.setResultTypeName(SampleResultTypeEnum.REPORT.getDes());
        item.setResultFromName(Objects.equals(sampleResultDto.getInstrumentId(), NumberUtils.LONG_ZERO)
            ? RetestModeEnum.FRONT.getDesc() : RetestModeEnum.MACHINE.getDesc());
        return item;
    }

    public static CriticalResultConnectSampleResultDto
        getBySampleRetestItemDto(SampleRetestItemDto sampleRetestItemDto) {
        CriticalResultConnectSampleResultDto item = new CriticalResultConnectSampleResultDto();
        item.setSampleId(sampleRetestItemDto.getSampleId());
        item.setReportItemId(sampleRetestItemDto.getReportItemId());
        item.setResultValue(sampleRetestItemDto.getResult());
        item.setResultDate(sampleRetestItemDto.getCreateDate());
        item.setResultTypeName(SampleResultTypeEnum.RETEST.getDes());
        String resultFromName;
        if (Objects.equals(sampleRetestItemDto.getRetestMode(), RetestModeEnum.FRONT.getCode())) {
            resultFromName = RetestModeEnum.FRONT.getDesc();
        } else if (Objects.equals(sampleRetestItemDto.getRetestMode(), RetestModeEnum.MACHINE.getCode())) {
            resultFromName = RetestModeEnum.MACHINE.getDesc();
        } else if (Objects.equals(sampleRetestItemDto.getRetestMode(), RetestModeEnum.MANUAL_SYNC.getCode())) {
            resultFromName = RetestModeEnum.MANUAL_SYNC.getDesc();
        } else {
            resultFromName = RetestModeEnum.DEFAULT.getDesc();
        }

        item.setResultFromName(resultFromName);
        return item;
    }

    /**
     * 当样本报告项目 处于复查中
     */
    public static CriticalResultConnectSampleResultDto getWhenReviewSampleReportItem() {
        CriticalResultConnectSampleResultDto item = new CriticalResultConnectSampleResultDto();
        item.setSampleId(NumberUtils.LONG_ZERO);
        item.setReportItemId(NumberUtils.LONG_ZERO);
        item.setResultValue("复查中");
        item.setResultDate(null);
        item.setResultTypeName(SampleResultTypeEnum.DEFAULT.getDes());
        item.setResultFromName(StringUtils.EMPTY);
        return item;
    }
}
