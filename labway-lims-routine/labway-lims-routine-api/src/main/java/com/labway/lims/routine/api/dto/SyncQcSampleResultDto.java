package com.labway.lims.routine.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collection;

/**
 * 同步 质控结果 dto
 * 
 * <AUTHOR>
 * @since 2023/7/5 11:59
 */
@Getter
@Setter
public class SyncQcSampleResultDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 需要新增的质控样本结果
     */
    private Collection<QcSampleResultDto> needAddQcSampleResultDtos;
    /**
     * 需要删除的质控样本结果
     */
    private Collection<QcSampleResultDto> needDeleteSampleResultDtos;
    /**
     * 需要更新的质控样本结果
     */
    private Collection<QcSampleResultDto> needUpdateQcSampleResultDtos;
}
