package com.labway.lims.routine.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <pre>
 * QueryQcSampleResultDto
 * 查询质控结果
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/2 14:34
 */
@Getter
@Setter
public class QueryQcSampleResultDto implements Serializable {

    /**
     * 起始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 仪器id
     */
    private Long instrumentId;

    /**
     * 仪器报告项目code
     */
    private String reportItemCode;

}
