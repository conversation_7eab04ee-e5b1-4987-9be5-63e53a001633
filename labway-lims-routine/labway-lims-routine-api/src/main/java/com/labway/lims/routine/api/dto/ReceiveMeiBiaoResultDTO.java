package com.labway.lims.routine.api.dto;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ReceiveMeiBiaoResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 样本id
     */
    private Long sampleId;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 报告项目code
     */
    private String reportItemCode;

    /**
     * 仪器id
     */
    private Long instrumentId;

    /**
     * 报告项目id
     */
    private Long reportItemId;
    /**
     * 报告项目名
     */
    private String reportItemName;


    /**
     * 测试项目id
     */
    private Long testItemId;


    /**
     * 测试项目code
     */
    private String testItemCode;


    /**
     * 测试项目name
     */
    private String testItemName;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * applySampleId
     */
    private Long applySampleId;

    /**
     * od值 = 原始od值 - 空白值
     */
    private String oDValue;

    /**
     * 定性的 套入cutoff 公式计算的
     * 定量的没有
     */
    private String cutoffValue;

    /**
     * S/Co = 原始OD / cutoff值
     */
    private String scoValue;

    /**
     * 结果
     */
    private String resultValue;
}
