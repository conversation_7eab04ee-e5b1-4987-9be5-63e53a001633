package com.labway.lims.routine.api.dto;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class RetestRecordDto implements Serializable {
    /**
     * 客商报告项目
     */
    private String reportItemName;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 原始结果
     */
    private String originalResult;

    /**
     * 1: 危机 2: 异常 0: 正常
     * @see ResultStatusEnum
     */
    private Integer status;

    /**
     * 提示符
     */
    private String testJudge;

    /**
     * 历史复查结果（复查1,复查2,复查3）
     */
    private List<RetestResult> historyResult;


    @Getter
    @Setter
    public static class RetestResult {
        /**
         * 复查结果
         */
        private String result;

        /**
         * 1: 危机 2: 异常 0: 正常
         * @see ResultStatusEnum
         */
        private Integer status;

        /**
         * 提示符
         */
        private String testJudge;

        /**
         * 正在复查的结果记录
         */
        private Long sampleRetestMainId;

        /**
         * 复查记录id
         */
        private Long sampleRetestItemId;

    }

}