package com.labway.lims.routine.api.dto;


import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class SampleReportItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long sampleReportItemId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 样本ID
     */
    private Long sampleId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 报告项目code
     */
    private String reportItemCode;

    /**
     * 报告项目
     */
    private String reportItemName;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 复查状态 0默认 1复查中 2已复查
     * @see RetestStatusEnum
     */
    private Integer isRetest;
    /**
     * 1: 已经删除 0:未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 打印顺序，越小越靠前
     */
    private Integer printSort;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
