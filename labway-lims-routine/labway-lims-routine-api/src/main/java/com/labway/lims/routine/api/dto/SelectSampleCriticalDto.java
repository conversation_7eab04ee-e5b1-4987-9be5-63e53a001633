package com.labway.lims.routine.api.dto;

import com.labway.lims.api.enums.routine.SampleCriticalResultStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/10 10:47
 */
@Getter
@Setter
public class SelectSampleCriticalDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 机构 ID
     */
    private Long orgId;
    /**
     * 专业组ID
     */
    private Long groupId;
    /**
     * 检验日期开始
     */
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    private Date testDateEnd;

    /**
     * 状态:0未处理，1复查中，2已处理
     *
     * @see SampleCriticalResultStatusEnum
     */
    private Integer status;

}
