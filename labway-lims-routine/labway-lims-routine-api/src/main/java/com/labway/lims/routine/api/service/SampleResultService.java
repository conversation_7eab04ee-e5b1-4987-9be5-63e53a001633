package com.labway.lims.routine.api.service;

import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.routine.api.dto.ReceiveMeiBiaoResultDTO;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SaveQCResultDto;
import com.labway.lims.routine.api.dto.SaveResultDto;
import com.labway.lims.routine.api.dto.SaveResultInfoDto;
import com.labway.lims.routine.api.dto.SimpleApplyDto;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/3/29 17:26
 */
public interface SampleResultService {
    /**
     * 修改样本结果 必须有 sample_id
     *
     * @param sampleResult dto
     * @return boolean
     */
    boolean updateBySampleResultId(SampleResultDto sampleResult);

    /**
     * 修改样本结果 必须有 sample_result_id
     *
     * @param sampleResults dto
     * @return boolean
     */
    boolean updateBySampleResultIds(List<SampleResultDto> sampleResults);

    /**
     * 根据sampleId查询结果
     *
     * @param sampleId sampleId
     * @return List<SampleResultDto>
     */
    @Nonnull
    List<SampleResultDto> selectBySampleId(long sampleId);

    /**
     * 根据sampleId查询结果 , {key:报告项目ID,value:value}
     */
    Map<String, SampleResultDto> selectBySampleIdAsMap(long sampleId);

    /**
     * 根据sampleId查询结果 , {key:样本ID,value:value}
     */
    Map<Long, List<SampleResultDto>> selectBySamplesIdAsMap(Collection<Long> sampleIds);

    /**
     * 根据结果ID查询结果
     */
    @Nullable
    SampleResultDto selectBySampleResultId(long sampleResultId, long sampleId);

    /**
     * 根据结果ID查询结果
     *
     * @param ignoreDelete 如果为 true 表示查询条件不加上 is_delete 条件
     */
    @Nullable
    SampleResultDto selectBySampleResultId(long sampleResultId, long sampleId, boolean ignoreDelete);

    /**
     * 批量添加结果
     */
    SaveResultInfoDto saveResult(SaveResultDto result, SaveResultSourceEnum source);

    /**
     * 批量添加结果
     *
     * @param results List<SampleResultDto>
     * @return Set<sampleResultId>
     */
    List<SaveResultInfoDto> saveResults(List<SaveResultDto> results, SaveResultSourceEnum source);

    /**
     * 根据sampleResultId删除结果
     *
     * @param sampleResultId sampleResultId
     * @return boolean
     */
    boolean deleteBySampleResultId(long sampleResultId, long sampleId);

    /**
     * 根据 样本ID,检验时间,报告项目编码 删除 该样本下 相同检验时间的质控结果
     */
    boolean deleteByReportItemCodeSampleIdTestDate(String reportItemCode, long sampleId, Date testDate);

    /**
     * 根据sampleResultId删除结果
     *
     * @param sampleResultIds sampleResultIds
     * @return boolean
     */
    boolean deleteBySampleResultIds(Collection<Long> sampleResultIds, long sampleId);

    /**
     * 查询 样本结果 根据 样本id
     */
    List<SampleResultDto> selectBySampleIds(Collection<Long> sampleIds);

    /**
     * 查询 样本结果 根据 样本id
     *
     * @param ignoreDelete 如果为 true 表示查询条件不加上 is_delete 条件
     */
    List<SampleResultDto> selectBySampleIds(Collection<Long> sampleIds, boolean ignoreDelete);

    /**
     * 查询样本结果 根据样本id 报告项目id 暂时应当只会有一条
     */
    @Nullable
    SampleResultDto selectBySampleIdAndReportItemId(long sampleId, long reportItemId);

    /**
     * 查询样本结果 根据样本id 报告项目code 暂时应当只会有一条
     */
    @Nullable
    SampleResultDto selectBySampleIdAndReportItemCode(long sampleId, String reportItemCode);

    /**
     * 添加结果
     */
    long addSampleResult(SampleResultDto dto);

    /**
     * 根据样本id和报告项目Code删除结果
     */
    boolean deleteByReportItemCode(long sampleId, String reportItemCode);

    /**
     * 根据样本id和报告项目Code删除结果
     */
    void deleteByReportItemCodes(long sampleId, Collection<String> reportItemCodes);

    /**
     * 根据样本ID和报告项目ID删除结果
     */
    void deleteByReportItemIds(long sampleId, Collection<Long> reportItemIds);

    /**
     * 根据样本 ID 删除
     */
    boolean deleteBySampleId(long sampleId);

    boolean deleteBySampleId(long sampleId, String operator);

    /**
     * 根据样本 ID 删除
     */
    void deleteBySampleIds(Collection<Long> sampleIds);


    /**
     * 批量添加
     */
    void addSampleResults(Collection<SampleResultDto> sampleResults);

    void addSampleResults(Collection<SampleResultDto> sampleResults, String operator);

    /**
     * 根据样本ID和报告项目ID查询
     */
    List<SampleResultDto> selectBySampleIdsAndReportItemId(Collection<Long> sampleIds, long reportItemId);


    /**
     * 根据样本ID和报告项目code查询
     */
    List<SampleResultDto> selectBySampleIdsAndReportItemCode(Collection<Long> sampleIds, String reportItemCode);

    /**
     * 刷新结果的参考范围
     */
    void refreshReference(SimpleApplyDto simpleApply);

    /**
     * 统计样本的危急值数量
     */
    long countSampleResultCriticalQuantity(long sampleId);

    List<SaveResultInfoDto> receiveQCResults(Collection<SaveQCResultDto> qcResults);


    /**
     * 接收酶标的结果值
     * @return
     */
    boolean receiveMeiBiaoResult(List<ReceiveMeiBiaoResultDTO> results);

    /**
     * 批量查询 根据sampleIds 和reportIds
     * @param results
     * @return
     */
    List<SampleResultDto> selectBySampleIdsAndReportItemCodes(List<ReceiveMeiBiaoResultDTO> results);
}
