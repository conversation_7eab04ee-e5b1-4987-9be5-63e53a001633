package com.labway.lims.routine.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/4/3 15:24
 */
@Getter
@Setter
public class SampleAuditDto implements Serializable {

    /**
     * 审核状态
     *
     * @see com.labway.lims.api.enums.routine.SampleAuditStatusEnum
     */
    private String auditStatus;

    /**
     * 样本 ID 集合
     */
    private Long sampleId;

    /**
     * 强制审核
     */
    private Integer auditForce;

    /**
     * 忽略空参考范围提示
     */
    private Boolean ignoreEmptyReferenceTip;

}
