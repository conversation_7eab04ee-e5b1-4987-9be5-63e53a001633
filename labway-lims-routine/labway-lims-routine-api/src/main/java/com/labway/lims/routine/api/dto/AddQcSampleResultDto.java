package com.labway.lims.routine.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <pre>
 * AddQcSampleResultDto
 * 新增质控结果
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/2 14:34
 */
@Getter
@Setter
public class AddQcSampleResultDto implements Serializable {

    /**
     * 检验时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDate;

    /**
     * 结果
     */
    private String result;

    /**
     * 低浓度
     */
    private String low;

    /**
     * 中浓度
     */
    private String medium;

    /**
     * 高浓度
     */
    private String high;

    /**
     * 仪器id
     */
    private Long instrumentId;
    /**
     * 仪器code
     */
    private String instrumentCode;
    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器报告项目code
     */
    private String reportItemCode;

    /**
     * 仪器报告项目名称
     */
    private String reportItemName;

    /**
     * 浓度
     */
    private String level;

}
