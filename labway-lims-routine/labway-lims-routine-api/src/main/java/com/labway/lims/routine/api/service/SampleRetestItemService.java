package com.labway.lims.routine.api.service;

import com.labway.lims.routine.api.dto.SampleRetestItemDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 样本结果复查子表 Service
 *
 * <AUTHOR>
 * @since 2023/4/11 19:07
 */
public interface SampleRetestItemService {

    /**
     * 根据sampleIds查询结果
     */
    List<SampleRetestItemDto> selectBySampleIds(Collection<Long> sampleIds);

    /**
     * 根据sampleIds查询结果
     */
    List<SampleRetestItemDto> selectBySampleId(long sampleId);

    /**
     * 获取 样本 复查结果 根据样本id 和 报告项目id
     */
    List<SampleRetestItemDto> selectBySampleIdAndReportItemId(long sampleId, long reportItemId);

    /**
     * 根据 sampleRetestMainId 和 reportItemId 删除
     */
    void deleteBySampleRetestMainIdAndReportItemId(long sampleRetestMainId, long reportItemId);

    /**
     * 根据 sampleRetestMainId 和 reportItemCode 删除
     */
    void deleteBySampleRetestMainIdAndReportItemCode(long sampleRetestMainId, String reportItemCode);

    /**
     * 根据 sampleRetestMainId 删除
     */
    void deleteBySampleRetestMainId(long sampleRetestMainId);

    /**
     * 添加复查item
     */
    long addSampleRetestItem(SampleRetestItemDto dto);

    /**
     * 添加复查item
     */
    void addSampleRetestItems(Collection<SampleRetestItemDto> sampleRetestItems);

    /**
     * 根据 sampleRetestItemId 查询
     */
    List<SampleRetestItemDto> selectBySampleRetestMainId(long sampleRetestItemId);

    List<SampleRetestItemDto> selectBySampleRetestMainIds(Collection<Long> sampleRetestItemIds);

    /**
     * 根据id修改
     */
    boolean updateById(SampleRetestItemDto item);

    /**
     * 根据样本ID和检验项目id查询
     */
    List<SampleRetestItemDto> selectBySampleIdAndTestItemId(long sampleId, long testItemId);

    void deleteNoResultRetestItem(Collection<Long> retestItemIds);

    void updateRetestStatus(Set<Long> reportItemIds, long sampleId, Integer retestStatus);

    @Nullable
    SampleRetestItemDto selectBySampleRetestItemId(long sampleRetestItemId);

    void deleteBySampleRetestMainIdAndReportItemCodes(long sampleRetestMainId, Collection<String> reportItemCodes);

    /**
     * 查询报告项目的复查数量
     *
     * @param sampleIds 样本ids
     * @return 报告项目code， 复查次数
     */
    Map<String, Long> selectReportCountBySampleIds(Collection<Long> sampleIds);
}
