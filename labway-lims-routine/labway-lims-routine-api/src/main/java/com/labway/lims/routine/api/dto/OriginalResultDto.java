package com.labway.lims.routine.api.dto;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/5/26 23:37
 */
@Getter
@Setter
public class OriginalResultDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * sampleId
     */
    private Long sampleId;

    private String reportItemCode;

    private String result;

    private String judge;

    /**
     * 1: 危机 2: 异常 0: 正常
     *
     * @see ResultStatusEnum
     */
    private Integer status;
}
