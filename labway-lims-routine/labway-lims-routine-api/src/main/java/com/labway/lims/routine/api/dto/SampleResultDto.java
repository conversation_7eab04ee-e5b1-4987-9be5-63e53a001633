package com.labway.lims.routine.api.dto;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class SampleResultDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long sampleResultId;

    /**
     * 样本ID
     */
    private Long sampleId;


    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 报告项目code
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 结果类型，数值、图片等
     */
    private String type;

    /**
     * 这个结果可能是经过格式化或计算过的
     */
    private String result;

    /**
     * 结果单位
     */
    private String unit;

    /**
     * 范围
     */
    private String range;

    /**
     * 1: 危机
     * 2: 异常
     * 0: 正常
     *
     * @see ResultStatusEnum
     */
    private Integer status;

    /**
     * 结果来源仪器信息
     */
    private Long instrumentId;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器原始结果
     */
    private String instrumentResult;

    /**
     * UP or DOWN
     *
     * @see TestJudgeEnum
     */
    private String judge;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 是否已经删除
     */
    private Integer isDelete;

    /**
     * 参考范围ID
     */
    private Long instrumentReportItemReferenceId;


    /**
     * 扩充字段  json {@link MeiBiaoValueDTO}
     */
    private String extraInfo;

    /**
     * 是否手工录入结果 0否1是
     */
    private Integer isHandeResult;

    /**
     * 检验时间 yyyy-MM-dd HH:mm:ss
     */
    private Date testDate;

}
