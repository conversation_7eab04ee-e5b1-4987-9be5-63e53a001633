package com.labway.lims.routine.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ResultStatusDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private String reportItemCode;

    private String result;

    private Integer isException;

    private Integer isCritical;

    private String judge;
    /**
     * 是否复查
     */
    private Integer isRetest;
}