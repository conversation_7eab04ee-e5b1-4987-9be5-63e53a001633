package com.labway.lims.routine.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <pre>
 * AddSampleReportItemDto
 * 加项
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/17 13:57
 */
@Getter
@Setter
public class AddSampleReportItemDto implements Serializable {

    /**
     * sampleID
     */
    private Long sampleId;

    /**
     * instrumentId
     */
    private Long instrumentId;

    /**
     * 添加的报告项目
     */
    List<SampleReportItemDto> reportItems;

}
