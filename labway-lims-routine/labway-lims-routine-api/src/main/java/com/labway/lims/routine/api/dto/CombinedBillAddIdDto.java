package com.labway.lims.routine.api.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
public class CombinedBillAddIdDto implements Serializable {

    /**
     * 合并新增的
     */
    private Map<Long, Long> sampleResultOldIdAndNewIdMap;

    private Map<Long, Long> sampleReportOldIdAndNewIdMap;

    public CombinedBillAddIdDto addSampleReportOldIdAndNewIdMap(Map<Long, Long> sampleReportOldIdAndNewIdMap) {
        if(CollUtil.isEmpty(this.sampleReportOldIdAndNewIdMap)){
            synchronized (this){
                if(CollUtil.isEmpty(this.sampleReportOldIdAndNewIdMap)){
                    this.sampleReportOldIdAndNewIdMap = new HashMap<>();
                }
            }
        }
        this.sampleReportOldIdAndNewIdMap.putAll(sampleReportOldIdAndNewIdMap);
        return this;
    }

    public CombinedBillAddIdDto addSampleResultOldIdAndNewIdMap(Map<Long, Long> sampleResultOldIdAndNewIdMap) {
        if(CollUtil.isEmpty(this.sampleResultOldIdAndNewIdMap)){
            synchronized (this){
                if(CollUtil.isEmpty(this.sampleResultOldIdAndNewIdMap)){
                    this.sampleResultOldIdAndNewIdMap = new HashMap<>();
                }
            }
        }
        this.sampleResultOldIdAndNewIdMap.putAll(sampleResultOldIdAndNewIdMap);
        return this;
    }


}
