package com.labway.lims.routine.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 质控样本结果 Dto
 * 
 * <AUTHOR>
 * @since 2023/7/5 11:23
 */
@Getter
@Setter
public class QcSampleResultDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 样本结果ID
     */
    private Long sampleResultId;

    /**
     * 样本ID
     */
    private Long sampleId;

    /**
     * 质控批号
     */
    private String qcBatch;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 报告项目code
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 结果类型，数值、图片等
     */
    private String type;

    /**
     * 结果(这个结果可能是经过格式化或计算过的)
     */
    private String result;

    /**
     * 结果单位
     */
    private String unit;

    /**
     * 范围
     */
    private String range;

    /**
     * 1: 危机 2: 异常 0: 正常
     */
    private Integer status;

    /**
     * 结果来源仪器信息
     */
    private Long instrumentId;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器原始结果
     */
    private String instrumentResult;

    /**
     * UP or DOWN
     */
    private String judge;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 0:未删除，1：已删除
     */
    private Integer isDelete;

    /**
     * 检验时间 yyyy-MM-dd HH:mm:ss
     */
    private Date testDate;

    /**
     * 浓度
     */
    private String level;

}
