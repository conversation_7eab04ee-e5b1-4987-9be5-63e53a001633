package com.labway.lims.routine.api.dto;

import java.io.Serializable;
import java.util.List;

import javax.annotation.Nullable;

import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/4/12 14:59
 */
@Getter
@Setter
public class SaveResultInfoDto implements Serializable {
    /**
     * 结果
     */
    private String result;

    /**
     * 旧结果，null则没有
     */
    @Nullable
    private String beforeResult;

    /**
     * 检验判断
     *
     * @see TestJudgeEnum
     */
    private String judge;

    /**
     * 是否是异常值
     */
    private Boolean exception;

    /**
     * 是否是危机值
     */
    private Boolean critical;

    /**
     * 是否是复查中，如果时 false 表示复查已经结束
     */
    private Boolean retesting;

    /**
     * 命中的参考范围，null则没有
     */
    @Nullable
    private InstrumentReportItemReferenceDto reference;

    /**
     * 存在 复查 返回 复查结果
     */
    private List<ResultListDto> resultAll;
}
