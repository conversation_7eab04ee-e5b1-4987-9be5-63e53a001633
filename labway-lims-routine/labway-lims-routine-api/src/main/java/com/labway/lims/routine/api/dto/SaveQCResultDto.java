package com.labway.lims.routine.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @since 2023/4/12 13:16
 */
@Getter
@Setter
public class SaveQCResultDto implements Serializable {

    /**
     * sampleId
     */
    private Long sampleId;

    /**
     * 报告项目Code
     */
    private String reportItemCode;

    /**
     * 这个结果可能是经过格式化或计算过的
     */
    private String result;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 是否是质控
     */
    private Boolean isQc;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 检测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date date;

    /**
     * 来源
     *
     * @see SaveResultSourceEnum
     */
    private SaveResultSourceEnum source;

    /**
     * 多出来的数据， json
     */
    private String extraInfo;

}
