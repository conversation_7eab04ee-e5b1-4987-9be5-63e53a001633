package com.labway.lims.routine.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 此样本 对应 所有结果 包含 原始结果、复查结果
 * 
 * <AUTHOR>
 * @since 2023/9/12 15:05
 */
@Getter
@Setter
public class ResultListDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 值内容
     */
    private String content;

    /**
     * 值类型：1 原始结果 2复查结果
     */
    private Integer contentType;

    /**
     * 排序 复查 使用
     */
    private Integer sort;

}
