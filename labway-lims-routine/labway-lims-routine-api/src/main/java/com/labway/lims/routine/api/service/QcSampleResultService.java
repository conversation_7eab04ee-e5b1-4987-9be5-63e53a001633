package com.labway.lims.routine.api.service;

import com.labway.lims.routine.api.dto.QcSampleResultDto;
import com.labway.lims.routine.api.dto.SyncQcSampleResultDto;

import java.util.List;

/**
 * 质控样本结果 service
 *
 * <AUTHOR>
 * @since 2023/7/5 11:22
 */
public interface QcSampleResultService {
    /**
     * 根据样本id 获取 质控样本结果
     */
    List<QcSampleResultDto> selectBySampleId(long sampleId);

    /**
     * 同步 质控结果
     */
    void syncQcSampleResult(SyncQcSampleResultDto sampleResultDto);

    /**
     * 删除 质控样本结果
     */
    void deleteBySampleId(long sampleId);

}
