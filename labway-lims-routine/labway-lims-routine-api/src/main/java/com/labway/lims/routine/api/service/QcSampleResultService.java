package com.labway.lims.routine.api.service;

import com.labway.lims.routine.api.dto.AddQcSampleResultDto;
import com.labway.lims.routine.api.dto.QcSampleResultDto;
import com.labway.lims.routine.api.dto.QueryQcSampleResultDto;
import com.labway.lims.routine.api.dto.SyncQcSampleResultDto;

import java.util.List;

/**
 * 质控样本结果 service
 *
 * <AUTHOR>
 * @since 2023/7/5 11:22
 */
public interface QcSampleResultService {
    /**
     * 根据样本id 获取 质控样本结果
     */
    List<QcSampleResultDto> selectBySampleId(long sampleId);

    /**
     * 同步 质控结果
     */
    void syncQcSampleResult(SyncQcSampleResultDto sampleResultDto);

    /**
     * 删除 质控样本结果
     */
    void deleteBySampleId(long sampleId);

    /**
     * 查询质控结果数据
     */
    List<QcSampleResultDto> selectQcSampleResult(QueryQcSampleResultDto param);

    /**
     * 新增质控结果
     */
    List<Long> addQcSampleResult(AddQcSampleResultDto param);

    /**
     * 根据结果id删除质控结果
     */
    void deleteByIds(List<Long> ids);

    /**
     * 根据结果id物理删除质控结果
     */
    void physicsDeleteByIds(List<Long> ids);

}
