#!/bin/bash

# 获取当前周一的日期
start_of_week=$(date -d "last monday" +%Y-%m-%d)
if [[ $(date +%u) -eq 1 ]]; then
    # 如果今天是周一，就从今天开始统计
    start_of_week=$(date +%Y-%m-%d)
fi

# 获取当前日期和时间
current_time=$(date +%Y-%m-%d)

# 初始化统计行数变量
declare -A author_lines
declare -A seen_commits  # 用于去重提交 ID

# 获取所有分支列表
branches=$(git branch -r | grep -v '\->' | awk '{print $1}')

# 遍历每个分支
for branch in $branches; do
    echo -e "\033[1;33m正在统计分支 $branch 的提交行数...\033[0m"

    # 获取从周一到当前时间的提交记录（不切换分支）
    commits=$(git log $branch --since="$start_of_week 00:00:00" --until="$current_time 23:59:59" --pretty=format:"%H|%an")
    for commit_info in $commits; do
        # 分割 commit 信息，提取提交 ID 和作者
        commit_id=$(echo $commit_info | cut -d '|' -f 1)
        author=$(echo $commit_info | cut -d '|' -f 2)

        # 跳过已统计的提交
        if [[ -n ${seen_commits[$commit_id]} ]]; then
            continue
        fi

        # 获取提交的变更行数
        lines=$(git show --stat $commit_id | grep "changed" | awk '{print $4}' | sed 's/[^0-9]*//g')
        if [[ -n $lines ]]; then
            # 按作者累加行数
            author_lines["$author"]=$((author_lines["$author"] + lines))
            seen_commits["$commit_id"]=1  # 标记该提交已被统计

            # 彩色打印提交信息
            echo -e "\033[1;34m$author\033[0m 在分支 \033[1;33m$branch\033[0m 提交行数：\033[1;32m$lines\033[0m"
        fi
    done

    echo -e "\033[1;36m$branch 统计完成\033[0m"
done

# 打印分隔线
echo -e "\033[1;36m---------------------------------\033[0m"

# 输出所有作者的统计结果
echo "从 $start_of_week 到 $current_time，各作者的代码提交行数统计如下："
for author in "${!author_lines[@]}"; do
    echo -e "\033[1;35m$author\033[0m: \033[1;32m${author_lines[$author]} 行\033[0m"
done
