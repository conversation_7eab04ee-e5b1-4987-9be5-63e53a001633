package com.labway.lims.gateway.filter;

import com.labway.lims.gateway.config.RedisPrefix;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.LinkedHashSet;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class AuthenticationFilter extends AbstractGatewayFilterFactory<Object> implements InitializingBean {

    private static final String HEADER = "Authorization";

    /**
     * 随机字符串，代替忽略的路径 表示此接口不需要校验权限，通知后续拦截器 跳过校验 token
     */
    private static final String IGNORE_AUTHORIZATION_TOKEN =
            "3CF61AF5C06FD7E749EE6C84E05C929C23E321569897695F31E3731558518964";

    @Resource
    private RedisPrefix redisPrefix;

    private final AntPathMatcher matcher = new AntPathMatcher();


    @Resource
    private ExcludePath excludePath;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 先从 Header 中取 , 然后 Parameter , 再然后 Cookie
     */
    @Nullable
    private static String getAuthorization(ServerHttpRequest request) {

        // 参数
        String authorization = request.getQueryParams().getFirst(HEADER);
        if (StringUtils.isNotBlank(authorization)) {
            return authorization;
        }

        // header
        authorization = request.getHeaders().getFirst(HEADER);
        if (StringUtils.isNotBlank(authorization)) {
            return authorization;
        }

        // cookie
        authorization = Optional.ofNullable(request.getCookies().getFirst(HEADER)).map(HttpCookie::getValue)
                .orElse(StringUtils.EMPTY);
        if (StringUtils.isNotBlank(authorization)) {
            return authorization;
        }

        return null;

    }

    @Override
    public GatewayFilter apply(Object config) {
        return (exchange, chain) -> {

            final ServerHttpRequest request = exchange.getRequest();

            if (request.getMethod() == HttpMethod.OPTIONS) {
                return chain.filter(exchange);
            }

            final String path = request.getPath().value();

            for (String excludePathPattern : excludePath.patterns) {
                if (matcher.match(excludePathPattern, path)) {
                    return chain.filter(exchange.mutate()
                            .request(request.mutate().header(HEADER, IGNORE_AUTHORIZATION_TOKEN).build()).build());
                }
            }

            final String token = getAuthorization(request);
            if (StringUtils.isBlank(token)) {
                exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                return exchange.getResponse().setComplete();
            }

            if (IGNORE_AUTHORIZATION_TOKEN.equals(token)) {
                return chain.filter(exchange);
            }

            final String key = redisPrefix.getTokenKey(token);

            if (BooleanUtils.isNotTrue(stringRedisTemplate.hasKey(key))) {
                exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                return exchange.getResponse().setComplete();
            }

            // token 续期
            stringRedisTemplate.expire(key, 7, TimeUnit.DAYS);

            return chain.filter(exchange.mutate().request(request.mutate().header(HEADER, token).build()).build());

        };
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("exclude-path.patterns {}", excludePath.patterns);
    }


    @Getter
    @Setter
    @Component
    @ConfigurationProperties(prefix = "exclude-path")
    public static class ExcludePath {
        /**
         * 忽略的地址，不校验token
         */
        private Set<String> patterns = new LinkedHashSet<>();
    }

}
