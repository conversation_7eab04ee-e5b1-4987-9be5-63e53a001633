package com.labway.lims.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Hello world!
 *
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableConfigurationProperties
public class LabwayLimsGatewayApplication {
    public static void main(String[] args) {
        SpringApplication.run(LabwayLimsGatewayApplication.class, args);
    }
}
