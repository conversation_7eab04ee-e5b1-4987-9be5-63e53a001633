package com.labway.lims.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> on 2022/3/11
 */
@Slf4j
@Component
public final class EnvDetector implements InitializingBean {
    @Resource
    private Environment environment;

    /**
     * 是否是开发环境 , test 是测试
     */
    public boolean isTest() {
        return ArrayUtils.contains(environment.getActiveProfiles(), ConditionalOnRegion.TEST);
    }

    /**
     * 是否是 uat
     */
    public boolean isUat() {
        return ArrayUtils.contains(environment.getActiveProfiles(), ConditionalOnRegion.UAT);
    }

    /**
     * 是否是 线上
     */
    public boolean isProd() {
        return ArrayUtils.contains(environment.getActiveProfiles(), ConditionalOnRegion.PROD);
    }

    /**
     * 是否是 东莞
     */
    public boolean isDongguan() {
        return ArrayUtils.contains(environment.getActiveProfiles(), ConditionalOnRegion.DONGGUAN);
    }

    /**
     * 是否是 常州
     */
    public boolean isChangzhou() {
        return ArrayUtils.contains(environment.getActiveProfiles(), ConditionalOnRegion.CHANGZHOU);
    }

    /**
     * 是否是 丹阳
     */
    public boolean isDanyang() {
        return ArrayUtils.contains(environment.getActiveProfiles(), ConditionalOnRegion.DANYANG);
    }


    /**
     * 是否是 长沙
     */
    public boolean isChangsha() {
        return ArrayUtils.contains(environment.getActiveProfiles(), ConditionalOnRegion.CHANGSHA);
    }


    /**
     * 是否是 南京
     */
    public boolean isNanjing() {
        return ArrayUtils.contains(environment.getActiveProfiles(), ConditionalOnRegion.NANJING);
    }

    /**
     * 是否是测试环境 , dev 是开发环境
     */
    public boolean isDev() {
        return ArrayUtils.contains(environment.getActiveProfiles(), ConditionalOnRegion.DEV);
    }

    /**
     * 获取环境名称
     * <dl>
     * <dt>开发环境</dt>
     * <dd>test</dd>
     * <dt>测试环境</dt>
     * <dd>dev</dd>
     * <dt>云线上</dt>
     * <dd>prd</dd>
     * <dt>本地化部署</dt>
     * <dd>liwan or dongguan or danyang</dd>
     * </dl>
     */
    public String envName() {
        if (isDev()) {
            return ConditionalOnRegion.DEV;
        } else if (isTest()) {
            return ConditionalOnRegion.TEST;
        } else if (isUat()) {
            return ConditionalOnRegion.UAT;
        } else if (isProd()) {
            return ConditionalOnRegion.PROD;
        } else if (isDongguan()) {
            return ConditionalOnRegion.DONGGUAN;
        } else if (isChangzhou()) {
            return ConditionalOnRegion.CHANGZHOU;
        } else if (isDanyang()) {
            return ConditionalOnRegion.DANYANG;
        } else if (isChangsha()) {
            return ConditionalOnRegion.CHANGSHA;
        } else if (isNanjing()) {
            return ConditionalOnRegion.NANJING;
        } else {
            throw new IllegalStateException("环境错误");
        }
    }

    /**
     * 业务中台的机构编码
     */
    public static final String BUSINESS_CENTER_ORG_CODE = "business-center.org-code";

    /**
     * 是否是开发环境 , test 是测试
     */
    public String getBusinessCenterOrgCode() {
        return environment.getProperty(BUSINESS_CENTER_ORG_CODE, StringUtils.EMPTY);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("当前环境: {}", envName());
    }
}
