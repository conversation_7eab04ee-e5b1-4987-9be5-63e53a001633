package com.labway.lims.gateway.config;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * redis 前缀
 */
@Component
public class RedisPrefix {

    private static final String PREFIX = "LABWAY:LIMS:";

    private static final String TOKEN = "TOKEN:";

    @Resource
    private EnvDetector envDetector;

    /**
     * 获取通用的前缀
     */
    public String getBasePrefix() {

        if (envDetector.isUat()) {
            return getBasePrefix0() + "UAT:";
        } else if (envDetector.isChangsha()) {
            return getBasePrefix0() + "CHANGSHA:";
        }

        return getBasePrefix0();
    }

    private String getBasePrefix0() {
        return PREFIX;
    }

    /**
     * 获取 token 的key
     */
    public String getTokenKey(String token) {
        return getBasePrefix() + TOKEN + token;
    }
}
