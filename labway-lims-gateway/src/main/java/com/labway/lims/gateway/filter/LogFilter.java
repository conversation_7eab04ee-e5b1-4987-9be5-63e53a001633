package com.labway.lims.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.util.Objects;

/**
 * 记录请求日志
 */
@Slf4j
@Component
public class LogFilter extends AbstractGatewayFilterFactory<Object> {


    @Override
    public GatewayFilter apply(Object config) {
        return (exchange, chain) -> {

            final ServerHttpRequest request = exchange.getRequest();

            if (request.getMethod() == HttpMethod.OPTIONS) {
                return chain.filter(exchange);
            }


            if (log.isInfoEnabled()) {

                String remoteAddr = request.getHeaders().getFirst("X-Real-IP");

                if (StringUtils.isBlank(remoteAddr)) {
                    final String xForwardedForHeader = request.getHeaders().getFirst("X-Forwarded-For");
                    // 如果经过代理 那么获取到真实IP
                    if (StringUtils.isNotBlank(xForwardedForHeader)) {
                        remoteAddr = xForwardedForHeader.split(" ")[0];
                    }
                }

                if (StringUtils.isBlank(remoteAddr)) {
                    final String xForwardedHostHeader = request.getHeaders().getFirst("X-Forwarded-Host");
                    // 如果经过代理 那么获取到真实IP
                    if (StringUtils.isNotBlank(xForwardedHostHeader)) {
                        remoteAddr = xForwardedHostHeader.split(" ")[0];
                    }
                }

                if (StringUtils.isBlank(remoteAddr)) {
                    final InetSocketAddress remoteAddress = request.getRemoteAddress();
                    if (Objects.nonNull(remoteAddress)) {
                        remoteAddr = remoteAddress.getHostString();
                    }
                }


                log.info("Request Url [{}] Method [{}] RemoteAddr [{}] User-Agent [{}]",
                        request.getURI(), request.getMethodValue(), remoteAddr,
                        request.getHeaders().getFirst(HttpHeaders.USER_AGENT));
            }


            return chain.filter(exchange);

        };
    }

}
