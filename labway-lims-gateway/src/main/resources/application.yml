spring:
  cloud:
    gateway:
      httpclient:
        pool:
          max-idle-time: 30s
      discovery:
        locator:
          enabled: true
      routes:
        - id: apply
          uri: lb://labway-lims-apply
          predicates:
            - Path=/apply/**
        - id: routine
          uri: lb://labway-lims-routine
          predicates:
            - Path=/routine/**
        - id: genetics
          uri: lb://labway-lims-genetics
          predicates:
            - Path=/genetics/**
        - id: infection
          uri: lb://labway-lims-infection
          predicates:
            - Path=/infection/**
        - id: microbiology
          uri: lb://labway-lims-microbiology
          predicates:
            - Path=/microbiology/**
        - id: outsourcing
          uri: lb://labway-lims-outsourcing
          predicates:
            - Path=/outsourcing/**
        - id: specialty
          uri: lb://labway-lims-specialty
          predicates:
            - Path=/specialty/**
        - id: base
          uri: lb://labway-lims-base
          predicates:
            - Path=/base/**
        - id: pdfreport
          uri: lb://labway-lims-pdfreport
          predicates:
            - Path=/pdfreport/**
        - id: statistics
          uri: lb://labway-lims-statistics
          predicates:
            - Path=/statistics/**
        - id: bloodculture
          uri: lb://labway-lims-bloodculture
          predicates:
            - Path=/bloodculture/**
      default-filters:
        - LogFilter
        - AuthenticationFilter
        - StripPrefix=1
        # 阻断 /actuator 禁止外部访问健康检查
        - BlockedActuatorFilter
      globalcors:
        cors-configurations:
          '[/**]':
            allow-credentials: true
            allowed-headers: "*"
            allowed-methods: "*"
            allowed-origins: "*"
  profiles:
    active: dev

server:
  port: 12312
  compression:
    enabled: true

# https://www.jianshu.com/p/e038bc51fa02
# jenkins 加上 -Dreactor.netty.pool.leasingStrategy=lifo