package com.labway.lims.meibiao.util;

/**
 * String 类型 进制判断
 */
public class HexStringChecker {

    public static boolean isHexString(String str) {
        try {
            Long.parseLong(str, 16);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static boolean colorIsHexString(String color) {
        String hex = color.replace("#", "");
        if (hex.length() == 3 || hex.length() == 6) {
            return color.startsWith("#") && isHexString(hex);
        }
        return false;
    }
}
