package com.labway.lims.meibiao.dto;

import lombok.Data;
import lombok.Getter;

import java.util.List;
import java.util.StringJoiner;

@Data
public class InstrumentResultDTO {

    private String plateCode;

    private Frequency frequency;

    private List<Result> results;

    @Getter
    public enum Frequency{
        FIRST,
        SECOND,
        ;
    }


    @Data
    public static class Result{
        private Integer col;

        private Integer row;

        private String sampleResult;
    }


    @Override
    public String toString() {
        return new StringJoiner(", ", InstrumentResultDTO.class.getSimpleName() + "[", "]")
                .add("plateCode='" + plateCode + "'")
                .add("frequency=" + frequency)
                .add("results=" + results)
                .toString();
    }
}
