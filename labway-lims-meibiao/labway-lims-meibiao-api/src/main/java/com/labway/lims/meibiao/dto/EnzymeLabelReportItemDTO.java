package com.labway.lims.meibiao.dto;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.labway.lims.api.enums.base.InstrumentItemTypeEnum;
import com.labway.lims.meibiao.emun.CheckFormulaEnum;
import com.labway.lims.meibiao.util.HexStringChecker;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 酶标仪报告项目
 */
@Data
public class EnzymeLabelReportItemDTO implements Serializable {
    /**
     * 酶标报告项目ID
     */
    private Long labelReportId;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 计算公式ID
     */
    private Long formulaId;

    /**
     * 项目颜色
     */
    private String projectColor;

    /**
     * 检测波长
     */
    private String testWaveLength;

    /**
     * 参考波长
     */
    private String referenceWaveLength;

    /**
     * 测试方法
     */
    private String testMethod;

    /**
     * 试剂批号
     */
    private String reagentBatch;

    /**
     * 试剂有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate reagentValidDate;

    /**
     * 试剂厂商
     */
    private String reagentManufacturer;

    /**
     * 灰区范围(小)计算公式
     */
    private String grayZoneMinFormula;

    /**
     * 灰区范围(大)计算公式
     */
    private String grayZoneMaxFormula;

    /**
     * 报告项目类型 00001定性 00002定量 00004其他
     */
    private String itemTypeCode;

    /**
     * 报告项目类型名称 00001定性 00002定量 00004其他
     */
    private String itemTypeName;

    /**
     * 定量设置的json
     */
    private List<RationSetting> rationSetting;

    /**
     * 定量设置结果json
     */
    private RationResult rationResult;

    /**
     * 是否生成质控
     */
    private Boolean isGenerateQc;

    /**
     * 是否生成阴性对照
     */
    private Boolean isNegativeContrast;

    /**
     * 阴性对照参考值
     */
    private Integer negativeContrastNum;

    /**
     * 是否生成阳性对照
     */
    private Boolean isPositiveContrast;

    /**
     * 阳性对照参考值
     */
    private Integer positiveContrastNum;

    /**
     * 是否留样复测
     */
    private Boolean isReservedSample;

    /**
     * 是否有空白
     */
    private Boolean isGenerateBlank;

    /**
     * 空白品数量
     */
    private Integer generateBlankNum;

    /**
     * 是否有标准品
     */
    private Boolean isGenerateStandard;

    /**
     * 标准品数量
     */
    private Integer generateStandardNum;


    @JsonIgnore
    private static final int DECIMALS_SIZE = 3;

    /**
     * 定量设置
     */
    @Data
    public static class RationSetting implements Serializable {
        /**
         * 浓度   y
         */
        private BigDecimal concentration;

        /**
         * 吸光度  x
         */
        private BigDecimal absorbency;

        public void verifyParams() {
            Assert.notNull(concentration, "标准品浓度不能为空");
            Assert.notNull(absorbency, "吸光度不能为空");
        }
    }


    /**
     * 定量结果  a b
     */
    @Data
    public static class RationResult implements Serializable {
        /**
         * a = y平均值 - b * x平均值
         */
        private BigDecimal a;

        /**
         * b = (y1*x1 + y2*x2 + yn*xn - n*y平均值 * x平均值) / (x1² + x2² + x3² - n * x平均值²)
         */
        private BigDecimal b;

        /**
         * R² = 1- ( 残差平方和 / 总平方和 )
         * 残差平方和 = (y1 - (b * x1 + a))² + (y2 - (b * x2 + a))² + (yn - (b * xn + a))²
         * 平方总和 = (y1 - y平均值)² + (y2 - y平均值)² +(yn - y平均值)²
         */
        private BigDecimal r;

    }


    /**
     * 新增参数校验
     */
    public void verifySaveParams() {
        Assert.notNull(this.getInstrumentId(), "仪器不能为空");

        Assert.notNull(this.getReportItemId(), "仪器报告项目不能为空");

        Assert.notNull(this.getItemTypeCode(), "项目类型不能为空, 00001定性 00002定量");

        Assert.isTrue(this.getItemTypeCode().equals(InstrumentItemTypeEnum.QUALITATIVE.getCode()) || this.getItemTypeCode().equals(InstrumentItemTypeEnum.QUANTITATIVE.getCode()), "项目类型异常, 00001定性 00002定量");
        this.setItemTypeName(selectNameByCode(this.getItemTypeCode()));

        // 定性判断计算公式
        if (this.getItemTypeCode().equals(InstrumentItemTypeEnum.QUALITATIVE.getCode()))
            Assert.notNull(this.getFormulaId(), "计算公式不能为空");

        // 定量判断 定量设置 至少需要两行数据
        if (this.getItemTypeCode().equals(InstrumentItemTypeEnum.QUANTITATIVE.getCode())) {
            Assert.notEmpty(this.getRationSetting(), "定量设置不能为空");
            // 数据不能小于 2
            if (this.getRationSetting().size() < 2) {
                throw new IllegalArgumentException("定量设置至少需要两行数据");
            }
            // 不能为空
            for (RationSetting setting : this.getRationSetting()) {
                setting.verifyParams();
            }
        }

        Assert.notNull(this.getIsGenerateQc(), "请选择是否生成质控");

        Assert.notNull(this.getIsReservedSample(), "请选择是否留样复测");

        Assert.notNull(this.getIsNegativeContrast(), "请选择是否生成阴性对照");
        Integer theNegativeContrastNum = this.getNegativeContrastNum();
        if (this.getIsNegativeContrast() && (theNegativeContrastNum == null || theNegativeContrastNum < 1 || theNegativeContrastNum.toString().length() > 10)) {
            throw new IllegalArgumentException("阴性对照数量不能小于1并且长度不能大于10");
        }
        if (!this.getIsNegativeContrast()) this.setNegativeContrastNum(0);

        Assert.notNull(this.getIsPositiveContrast(), "请选择是否生成阳性对照");
        Integer thePositiveContrastNum = this.getPositiveContrastNum();
        if (this.getIsPositiveContrast() && (thePositiveContrastNum == null || thePositiveContrastNum < 1 || thePositiveContrastNum.toString().length() > 10)) {
            throw new IllegalArgumentException("阳性对照数量不能小于1并且长度不能大于10");
        }
        if (!this.getIsPositiveContrast()) this.setPositiveContrastNum(0);


        Assert.notNull(this.getIsGenerateBlank(), "请选择是否生成空白");
        Integer blankNum = this.getGenerateBlankNum();
        if (this.getIsGenerateBlank() && (blankNum == null || blankNum < 1 || blankNum.toString().length() > 10)) {
            throw new IllegalArgumentException("空白数量不能小于1并且长度不能大于10");
        }
        if (!this.getIsGenerateBlank()) this.setGenerateBlankNum(0);


        Assert.notNull(this.getIsGenerateStandard(), "请选择是否生成标准品");
        Integer standardNum = this.getGenerateStandardNum();
        if (this.getIsGenerateStandard() && (standardNum == null || standardNum < 1 || standardNum.toString().length() > 10)) {
            throw new IllegalArgumentException("标准品数量不能小于1并且长度不能大于10");
        }
        if (!this.getIsGenerateStandard()) this.setGenerateStandardNum(0);

        Assert.isTrue(StringUtils.isBlank(this.getProjectColor())
                        || HexStringChecker.colorIsHexString(this.getProjectColor()),
                "项目颜色参数异常");

        // 自动生成的数量
        int num = 0;
        if(this.getIsGenerateQc()) num += 2;
        if(this.getIsReservedSample()) num += 2;
        if(this.getIsNegativeContrast()) num += this.getNegativeContrastNum();
        if(this.getIsPositiveContrast()) num += this.getPositiveContrastNum();
        if(this.getIsGenerateBlank()) num += this.getGenerateBlankNum();
        if(this.getIsGenerateStandard()) num += this.getGenerateStandardNum();
        if(num >= 96) throw new IllegalArgumentException("生成的[质控][阴性对照][阳性对照][留样复测][空白][标准品]数量相加最大95个 其中[质控][留样复测]各占用2个");



        if (StringUtils.isNotBlank(this.getTestWaveLength()) && this.getTestWaveLength().length() > 50) {
            throw new IllegalArgumentException("检测波长最长50字符");
        }
        if (StringUtils.isNotBlank(this.getReferenceWaveLength()) && this.getReferenceWaveLength().length() > 50) {
            throw new IllegalArgumentException("参考波长最长50字符");
        }
        if (StringUtils.isNotBlank(this.getTestMethod()) && this.getTestMethod().length() > 50) {
            throw new IllegalArgumentException("检测方法最长50字符");
        }
        if (StringUtils.isNotBlank(this.getReagentBatch()) && this.getReagentBatch().length() > 50) {
            throw new IllegalArgumentException("试剂批号最长50字符");
        }
        if (StringUtils.isNotBlank(this.getReagentManufacturer()) && this.getReagentManufacturer().length() > 100) {
            throw new IllegalArgumentException("试剂厂商最长100字符");
        }
        if (StringUtils.isNotBlank(this.getGrayZoneMinFormula()) &&
                (!verifyStartIsOperator(this.getGrayZoneMinFormula()) || !verifyIsNumber(this.getGrayZoneMinFormula()))) {
            throw new IllegalArgumentException("灰区范围(小)计算公式错误");
        }

        if (StringUtils.isNotBlank(this.getGrayZoneMaxFormula()) &&
                (!verifyStartIsOperator(this.getGrayZoneMaxFormula()) || !verifyIsNumber(this.getGrayZoneMaxFormula()))) {
            throw new IllegalArgumentException("灰区范围(大)计算公式错误");
        }
    }

    /**
     * 修改参数校验
     */
    public void verifyUpdateParams() {
        if (null == this.getLabelReportId()) {
            throw new IllegalArgumentException("酶标项目Id不能为空");
        }
        verifySaveParams();
    }

    private String selectNameByCode(String code) {
        InstrumentItemTypeEnum[] values = InstrumentItemTypeEnum.values();
        for (InstrumentItemTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

    /**
     * 判断计算公式开始是否是 + - * / 开始的
     */
    private boolean verifyStartIsOperator(String formula) {
        Collection<String> operators = CheckFormulaEnum.selectOperator();
        return operators.contains(formula.substring(0, 1));
    }

    private boolean verifyIsNumber(String formula) {
        try {
            formula = formula.substring(1);
            Double.parseDouble(formula);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public RationResult getRationResult() {
        if (ObjectUtils.isEmpty(rationResult) && ObjectUtils.isNotEmpty(rationSetting)) {
            synchronized (this) {
                if (ObjectUtils.isEmpty(rationResult) && ObjectUtils.isNotEmpty(rationSetting)) {
                    RationResult theRationResult = new RationResult();
                    // 先算 b  因为 a 是根据 b 来算的
                    BigDecimal b = calculateB(rationSetting);
                    BigDecimal a = calculateA(rationSetting, b);
                    BigDecimal rSquare = calculateR(rationSetting, a, b);

                    theRationResult.setA(a);
                    theRationResult.setB(b);
                    theRationResult.setR(rSquare);
                    rationResult = theRationResult;
                }
            }
        }
        return rationResult;
    }


    /**
     * 计算 b
     * b = (y1*x1 + y2*x2 + yn*xn - n*y平均值 * x平均值) / (x1² + x2² + x3² - n * x平均值²)
     */
    private BigDecimal calculateB(List<RationSetting> theRationSetting) {
        try {
            // 被除数 (y1*x1 + y2*x2 + yn*xn - n*y平均值 * x平均值)
            BigDecimal dividend;
            // 除数 (x1² + x2² + x3² - n * x平均值²)
            BigDecimal divisor;

            BigDecimal size = BigDecimal.valueOf(theRationSetting.size());

            // 1. 计算被除数
            dividend = theRationSetting.stream().map(setting -> setting.getAbsorbency().multiply(setting.getConcentration())).reduce(BigDecimal.ZERO, BigDecimal::add);

            // n * y的平均值
            BigDecimal nMultiplyYAvg = theRationSetting.stream().map(RationSetting::getConcentration).reduce(BigDecimal.ZERO, BigDecimal::add).divide(size, 10000, RoundingMode.HALF_UP).multiply(size);

            // x 的平均值
            BigDecimal xAvg = theRationSetting.stream().map(RationSetting::getAbsorbency).reduce(BigDecimal.ZERO, BigDecimal::add).divide(size, 10000, RoundingMode.HALF_UP);
            dividend = dividend.subtract(nMultiplyYAvg.multiply(xAvg));


            // 2. 计算除数
            // 平方和
            BigDecimal quadraticSum = theRationSetting.stream().map(RationSetting::getAbsorbency).map(absorbency -> absorbency.pow(2)).reduce(BigDecimal.ZERO, BigDecimal::add);

            // n * x平均值²
            BigDecimal xMultiplyXAvgQuadratic = theRationSetting.stream().map(RationSetting::getAbsorbency).reduce(BigDecimal.ZERO, BigDecimal::add).divide(size, 10000, RoundingMode.HALF_UP).pow(2).multiply(size);

            divisor = quadraticSum.subtract(xMultiplyXAvgQuadratic);

            // 四舍五入
            return dividend.divide(divisor, DECIMALS_SIZE, RoundingMode.HALF_UP);
        } catch (Exception exception) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算a
     * a = y平均值 - b * x平均值
     */
    private BigDecimal calculateA(List<RationSetting> theRationSetting, BigDecimal b) {
        try {
            BigDecimal size = BigDecimal.valueOf(theRationSetting.size());
            // x 平均
            BigDecimal xAvg = theRationSetting.stream().map(RationSetting::getAbsorbency).reduce(BigDecimal.ZERO, BigDecimal::add).divide(size, 10000, RoundingMode.HALF_UP);
            // y 平均
            BigDecimal yAvg = theRationSetting.stream().map(RationSetting::getConcentration).reduce(BigDecimal.ZERO, BigDecimal::add).divide(size, 10000, RoundingMode.HALF_UP);
            return yAvg.subtract(b.multiply(xAvg)).setScale(DECIMALS_SIZE, RoundingMode.HALF_UP);
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * R² = 1- ( 残差平方和 / 总平方和 )
     * 残差平方和 = (y1 - (b * x1 + a))² + (y2 - (b * x2 + a))² + (yn - (b * xn + a))²
     * 平方总和 = (y1 - y平均值)² + (y2 - y平均值)² +(yn - y平均值)²
     */
    private BigDecimal calculateR(List<RationSetting> theRationSetting, BigDecimal a, BigDecimal b) {
        try {
            BigDecimal size = BigDecimal.valueOf(theRationSetting.size());

            // 残差平方和
            BigDecimal residualSumOfSquares = theRationSetting.stream().map(setting -> setting.getConcentration().subtract(b.multiply(setting.getAbsorbency()).add(a)).pow(2)).reduce(BigDecimal.ZERO, BigDecimal::add);

            // 平方总和
            BigDecimal yAvg = theRationSetting.stream().map(RationSetting::getConcentration).reduce(BigDecimal.ZERO, BigDecimal::add).divide(size, 10000, RoundingMode.HALF_UP);
            BigDecimal totalSumOfSquares = theRationSetting.stream().map(setting -> setting.getConcentration().subtract(yAvg).pow(2)).reduce(BigDecimal.ZERO, BigDecimal::add);

            return BigDecimal.ONE.subtract(residualSumOfSquares.divide(totalSumOfSquares, DECIMALS_SIZE, RoundingMode.HALF_UP));
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }
}
