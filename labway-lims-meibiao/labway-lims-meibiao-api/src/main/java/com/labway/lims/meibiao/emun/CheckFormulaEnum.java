package com.labway.lims.meibiao.emun;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

@Getter
@AllArgsConstructor
public enum CheckFormulaEnum {

    CE_RESULT(1, 1, "测"), //  测量结果
    YIN_CONTRAST(1, 2, "阴"), // 阴性对照
    YANG_RESULT(1, 3, "阳"), // 阳性对照
    NULL_RESULT(1, 4, "空"), // 空白结果
    ZHI_RESULT(1, 5, "质"), //质控结果
    STANDARD(1, 6, "标"), //标准品结果
    CUTOFF(1, 7, "C"), //cutoff结果


    ADD(2, 11, "+"),
    SUBTRACT(2, 12, "-"),
    MULTIPLY(2, 13, "*"),
    DIVIDE(2, 14, "/"),
    LEFT_PAR(2, 15, "("),
    RIGHT(2, 16, ")"),
    DOT(2, 17, "."),

    ;
    /**
     * 类型 1汉字类型  2 符号类型
     */
    private final Integer type;

    /**
     * 编号
     */
    private final Integer code;

    /**
     * 符号
     */
    private final String symbol;


    /**
     * 所有code 和 name
     */
    private static final Map<Integer, String> codeSymbolMap = new TreeMap<>();

    /**
     * 汉字的code和name
     */
    private static final Map<Integer, String> ziCodeSymbolMap = new TreeMap<>();

    /**
     * 符号code 和name
     */
    private static final Map<Integer, String> operatorList =
            Map.of(6, "+",
                    7, "-",
                    8, "*",
                    9, "/");

    static {
        CheckFormulaEnum[] values = values();
        for (CheckFormulaEnum value : values) {
            Integer thisCode = value.getCode();
            String thisSymbol = value.getSymbol();
            Integer thisType = value.getType();
            codeSymbolMap.put(thisCode, thisSymbol);
            if (thisType.equals(1)) {
                ziCodeSymbolMap.put(thisCode, thisSymbol);
            }
        }
    }

    public static Collection<String> selectAllSymbol() {
        return codeSymbolMap.values();
    }

    public static Collection<String> selectAllZi() {
        return ziCodeSymbolMap.values();
    }

    public static Collection<String> selectOperator(){
        return operatorList.values();
    }

}
