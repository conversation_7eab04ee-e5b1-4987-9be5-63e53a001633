package com.labway.lims.meibiao.vo;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.meibiao.dto.EnzymeLabelLayoutDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 酶标板布局
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class EnzymeLabelLayoutVO extends EnzymeLabelLayoutDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 版号
     */
    private String plateCode;

    /**
     * 板面日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate plateDate;

    @Override
    public String toString() {
        return new StringJoiner(", ", EnzymeLabelLayoutVO.class.getSimpleName() + "[", "]")
                .add("plateCode='" + plateCode + "'")
                .add("plateDate=" + plateDate)
                .add("layoutId=" + getLayoutId())
                .add("plateId=" + getPlateId())
                .add("refTemplateId=" + getRefTemplateId())
                .add("refTemplateName='" + getRefTemplateName() + "'")
                .add("isTemplate=" + getIsTemplate())
                .add("templateName='" + getTemplateName() + "'")
                .add("sampleNoDate=" + getSampleNoDate())
                .add("sampleStartNo=" + getSampleStartNo())
                .add("remark='" + getRemark() + "'")
                .add("placeRuleCode='" + getPlaceRuleCode() + "'")
                .add("placeRule='" + getPlaceRule() + "'")
                .toString();
    }
}
