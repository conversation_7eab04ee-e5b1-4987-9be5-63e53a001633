package com.labway.lims.meibiao.emun;

import lombok.Getter;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Getter
public enum VariableReplaceEnum {

    YIN("1", "阴"),
    YANG("2", "阳"),

    ;

    private final String variableCode;
    private final String variable;

    VariableReplaceEnum(String variableCode, String variable) {
        this.variableCode = variableCode;
        this.variable = variable;
    }

    private static final List<Map<String, String>> list = new ArrayList<>();

    static {
        VariableReplaceEnum[] values = values();

        Map<String, String> map;
        for (VariableReplaceEnum value : values) {
            String variableCode1 = value.getVariableCode();
            String variable1 = value.getVariable();
            map = new HashMap<>();
            map.put("variableCode", variableCode1);
            map.put("variable", variable1);
            list.add(map);
        }
    }

    public static List<Map<String, String>> selectValues() {
        return list;
    }

    public static List<String> codeList(){
        return Arrays.stream(values()).map(VariableReplaceEnum::getVariableCode).collect(Collectors.toList());
    }

    public static List<String> nameList(){
        return Arrays.stream(values()).map(VariableReplaceEnum::getVariable).collect(Collectors.toList());
    }

    public static String getVariableName(String variableCode){
        VariableReplaceEnum[] values = values();
        for (VariableReplaceEnum value : values) {
            if(value.getVariableCode().equals(variableCode)){
                return value.variable;
            }
        }
        return "";
    }

    public static String getVariableCode(String variableName){
        VariableReplaceEnum[] values = values();
        for (VariableReplaceEnum value : values) {
            if(value.getVariable().equals(variableName)){
                return value.variableCode;
            }
        }
        return "";
    }
}
