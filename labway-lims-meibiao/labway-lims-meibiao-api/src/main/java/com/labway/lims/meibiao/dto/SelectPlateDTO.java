package com.labway.lims.meibiao.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

@Data
public class SelectPlateDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate endDate;

    public LocalDateTime getStartDate() {
        return Objects.requireNonNullElseGet(startDate, LocalDate::now).atStartOfDay();
    }

    public LocalDateTime getEndDate() {
        return Objects.requireNonNullElseGet(endDate, LocalDate::now).atStartOfDay();
    }

    public void verifyParams() {
        if (getStartDate().isAfter(getEndDate())) {
            throw new IllegalArgumentException("开始日期不能在结束日期之后");
        }
    }


}
