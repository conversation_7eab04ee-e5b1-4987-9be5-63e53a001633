package com.labway.lims.meibiao.dto;

import cn.hutool.core.lang.Assert;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class UpdateOriginalODValueDTO {

    /**
     * 板子上样本id
     */
    private Long plateSampleId;

    /**
     * 原始od
     */
    private BigDecimal originalODValue;


    public void verifyParams(){
        Assert.notNull(plateSampleId, "酶标板样本id不能为空");
        Assert.notNull(originalODValue, "原始OD不能为空");
        Assert.isTrue(originalODValue.toString().split("\\.").length <= 4, "原始OD小数点后最多4位");
    }
}
