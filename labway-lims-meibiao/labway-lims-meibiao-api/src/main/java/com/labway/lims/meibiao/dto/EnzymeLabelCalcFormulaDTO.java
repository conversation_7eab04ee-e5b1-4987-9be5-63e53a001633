package com.labway.lims.meibiao.dto;

import com.labway.lims.meibiao.emun.CheckFormulaEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.script.ScriptEngineManager;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 酶标计算公式
 * @date 2023-11-22
 */
@Data
public class EnzymeLabelCalcFormulaDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 计算公式ID
     */
    private Long formulaId;

    /**
     * 计算公式 code
     */
    private String formulaCode;

    /**
     * 计算公式
     */
    private String calcFormula;

    /**
     * 公式简称
     */
    private String formulaShort;

    /**
     * Cutoff计算公式
     */
    private String cutoffCalcFormula;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 公式描述
     */
    private String formulaDesc;

    /**
     * 判断是否是数字类型
     */
    public static final String PATTERN_NUMBER = "^\\d+$";

    /**
     * 公式正则
     */
    public static final String PATTERN_FORMULA = "^[0-9+\\-*/()]+$";


    /**
     * 新增参数校验
     */
    public void verifySaveParams() {
        String theCalcFormula = this.getCalcFormula();
        String thiCutoffCalcFormula = this.getCutoffCalcFormula();
        if (StringUtils.isBlank(this.getFormulaCode()) || this.getFormulaCode().length() > 50) {
            throw new IllegalArgumentException("计算公式编码不能为空且长度最大为50");
        }
        if (StringUtils.isBlank(theCalcFormula) || theCalcFormula.length() > 50) {
            throw new IllegalArgumentException("计算公式不能为空且长度最大为50");
        }
        if (StringUtils.isBlank(thiCutoffCalcFormula) || thiCutoffCalcFormula.length() > 50) {
            throw new IllegalArgumentException("CUTOFF公式不能为空且长度最大为50");
        }
        if (null == this.getSort() || this.getSort() < 0 || this.getSort().toString().length() > 10) {
            throw new IllegalArgumentException("显示顺序不能为空 不能为负数 长度最大10位");
        }

        if (StringUtils.isNotBlank(this.getFormulaShort()) && this.getFormulaShort().length() > 50) {
            throw new IllegalArgumentException("公式简称长度最大为50");
        }

        if (StringUtils.isNotBlank(this.getFormulaDesc()) && this.getFormulaDesc().length() > 255) {
            throw new IllegalArgumentException("公式描述长度最大为255");
        }

        //判断公式是否是包含指定字符
        Collection<String> symbolList = CheckFormulaEnum.selectAllSymbol();
        List<String> calcFormulaList = Arrays.stream(theCalcFormula.split("")).filter(e -> !e.matches(PATTERN_NUMBER)).collect(Collectors.toList());
        List<String> cutoffFormulaList = Arrays.stream(thiCutoffCalcFormula.split("")).filter(e -> !e.matches(PATTERN_NUMBER)).collect(Collectors.toList());

        if (!symbolList.containsAll(calcFormulaList) || !symbolList.containsAll(cutoffFormulaList)) {
            throw new IllegalArgumentException("计算公式只能包含：" + StringUtils.join(symbolList, " "));
        }

        // 替换，然后正则判断
        Collection<String> ziSymbolList = CheckFormulaEnum.selectAllZi();
        Collection<String> operatorList = CheckFormulaEnum.selectOperator();

        if (calcFormulaList.size() != 1) {
            String patternCalcFormula = theCalcFormula;
            for (String symbol : ziSymbolList) {
                patternCalcFormula = patternCalcFormula.replace(symbol, "1");
            }
            List<String> calcformulaCharList = Arrays.stream(patternCalcFormula.split("")).filter(operatorList::contains).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(calcformulaCharList) || !eval(patternCalcFormula)) {
                throw new IllegalArgumentException("计算公式格式错误");
            }
        } else if (!ziSymbolList.contains(calcFormulaList.get(0))) {
            throw new IllegalArgumentException("计算公式不能只存在符号");
        }

        if (cutoffFormulaList.size() != 1) {
            String patternCutoffCalcFormula = thiCutoffCalcFormula;
            for (String symbol : ziSymbolList) {
                patternCutoffCalcFormula = patternCutoffCalcFormula.replace(symbol, "1");
            }

            List<String> cutoffCalcFormulaCharList = Arrays.stream(patternCutoffCalcFormula.split("")).filter(operatorList::contains).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(cutoffCalcFormulaCharList) || !eval(patternCutoffCalcFormula)) {
                throw new IllegalArgumentException("Cutoff计算公式格式错误");
            }
        } else if (!ziSymbolList.contains(cutoffFormulaList.get(0))) {
            throw new IllegalArgumentException("计算公式不能只存在符号：");
        }
    }

    /**
     * 修改参数校验
     */
    public void verifyUpdateParams() {
        if (null == this.getFormulaId()) {
            throw new IllegalArgumentException("计算公式id不能为空");
        }
        verifySaveParams();
    }

    public String getCalcFormula() {
        return replace(calcFormula);
    }

    public String getCutoffCalcFormula() {
        return replace(cutoffCalcFormula);
    }

    private String replace(String s) {
        return s.replace("\u3000", "")
                .replace(" ", "")
                .replace("（", "(")
                .replace("）", ")")
                .replace("c", "C")
                ;
    }

    private boolean eval(String eval) {
        try {
            Object eval1 = new ScriptEngineManager().getEngineByName("JavaScript").eval(eval);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
