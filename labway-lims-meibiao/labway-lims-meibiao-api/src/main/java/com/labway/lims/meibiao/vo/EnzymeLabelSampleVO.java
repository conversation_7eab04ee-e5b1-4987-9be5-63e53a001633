package com.labway.lims.meibiao.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.meibiao.dto.EnzymeLabelReportItemDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
public class EnzymeLabelSampleVO extends EnzymeLabelTemplateSampleVO
        implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 样本id
     */
    private Long sampleId;

    /**
     * 原始od值
     */
    private BigDecimal originalODValue;

    /**
     * od值 = 原始od值 - 空白值
     */
    private BigDecimal oDValue;

    /**
     * 定性的 套入cutoff 公式计算的
     * 定量的没有
     */
    private BigDecimal cutoffValue;

    /**
     * S/Co = 原始OD / cutoff值
     */
    private BigDecimal scoValue;

    /**
     * 计算公式的值
     */
    private String resultValue;

    /**
     * 灰区 小 结果值
     */
    private BigDecimal grayAreaMinValue;

    /**
     * 灰区 大 结果值
     */
    private BigDecimal grayAreaMaxValue;


    /**
     * 显示颜色
     */
    private String colors;

    /**
     * 详细描述
     */
    private String detailDesc;

    /**
     * 简述
     */
    private String brief;


    /**
     * 酶标仪传输回来的第一次结果
     */
    private String firstSampleResult;

    /**
     * 酶标仪传输回来的第二次结果
     */
    private String secondSampleResult;


    private EnzymeLabelReportItemDTO.RationResult rationResult;

    /**
     * 版面日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate plateDate;

    /**
     * 原始od
     */
    private String originalOd;
}
