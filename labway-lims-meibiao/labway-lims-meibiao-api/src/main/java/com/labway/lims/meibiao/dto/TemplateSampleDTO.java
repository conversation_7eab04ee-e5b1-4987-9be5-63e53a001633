package com.labway.lims.meibiao.dto;

import cn.hutool.core.bean.BeanUtil;
import com.labway.lims.meibiao.vo.EnzymeLabelLayoutVO;
import com.labway.lims.meibiao.vo.EnzymeLabelSampleVO;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class TemplateSampleDTO implements Serializable {

    /**
     * 前端接受布局类
     */
    private EnzymeLabelLayoutDTO enzymeLabelLayout;

    /**
     * 前端接受的 实际布局 格式类型
     */
    private List<Map<String, Object>> enzymeLabelTemplateSample;

    private List<EnzymeLabelTemplateSampleDTO> data;

    public void verifySaveTemplateParams() {
        enzymeLabelLayout.verifySaveParams();
        for (EnzymeLabelTemplateSampleDTO datum : data) {
            datum.verifySaveTemplateParams();
        }
    }

    public List<EnzymeLabelTemplateSampleDTO> getData() {
        if (CollectionUtils.isEmpty(data)) {
            synchronized (this) {
                if (CollectionUtils.isEmpty(data)) {
                    fillData(enzymeLabelTemplateSample);
                }
            }
        }
        return data;
    }

    public void verifySaveSampleParams() {
        enzymeLabelLayout.verifySaveParams();

        Map<Long, List<EnzymeLabelTemplateSampleDTO>> sampleMap = data.stream().collect(Collectors.groupingBy(EnzymeLabelTemplateSampleDTO::getLabelReportId));
        Set<Map.Entry<Long, List<EnzymeLabelTemplateSampleDTO>>> entries = sampleMap.entrySet();
        for (Map.Entry<Long, List<EnzymeLabelTemplateSampleDTO>> entry : entries) {

            List<EnzymeLabelTemplateSampleDTO> value = entry.getValue();

            Set<String> sampleNos = new HashSet<>();
            for (EnzymeLabelTemplateSampleDTO dto : value) {
                if(StringUtils.isBlank(dto.getSampleNo())){
                    continue;
                }

                if(!sampleNos.add(dto.getSampleNo())){
                    throw new IllegalArgumentException("相同项目样本号["+dto.getSampleNo()+"]不能重复");
                }
            }
        }

        for (EnzymeLabelTemplateSampleDTO datum : data) {
            datum.verifySaveSampleParams();
        }
    }

    @Getter
    public enum ColEnum {
        ONE("one", 1),
        TWO("two", 2),
        THREE("three", 3),
        FOUR("four", 4),
        FIVE("five", 5),
        SIX("six", 6),
        SEVEN("seven", 7),
        EIGHT("eight", 8),
        NINE("nine", 9),
        TEN("ten", 10),
        ELEVEN("eleven", 11),
        TWELVE("twelve", 12),
        ;

        private final String english;

        private final int number;

        ColEnum(String english, int number) {
            this.english = english;
            this.number = number;
        }

        public static String selectEnglish(int number) {
            for (ColEnum value : values()) {
                if (value.getNumber() == number) {
                    return value.getEnglish();
                }
            }
            return "";
        }

        public static int getNumber(String english) {
            for (ColEnum value : values()) {
                if (value.getEnglish().equals(english)) {
                    return value.getNumber();
                }
            }
            return -1;
        }

        public static List<String> selectEnglish() {
            return Arrays.stream(values()).map(ColEnum::getEnglish).collect(Collectors.toList());
        }

        public static List<Integer> selectNumber() {
            return Arrays.stream(values()).map(ColEnum::getNumber).sorted().collect(Collectors.toList());
        }
    }

    @Getter
    public enum RowEnum {
        ONE("A", 1),
        TWO("B", 2),
        THREE("C", 3),
        FOUR("D", 4),
        FIVE("E", 5),
        SIX("F", 6),
        SEVEN("G", 7),
        EIGHT("H", 8),
        ;

        private final String row;

        private final int index;

        RowEnum(String row, int index) {
            this.row = row;
            this.index = index;
        }

        private static List<String> selectRow() {
            return Arrays.stream(values()).map(RowEnum::getRow).collect(Collectors.toList());
        }

        public static List<Integer> selectIndex() {
            return Arrays.stream(values()).map(RowEnum::getIndex).sorted().collect(Collectors.toList());
        }

        private static int getIndex(String row) {
            for (RowEnum value : values()) {
                if (value.getRow().equals(row)) {
                    return value.getIndex();
                }
            }
            return -1;
        }

        private static String getRow(int index) {
            for (RowEnum value : values()) {
                if (value.getIndex() == index) {
                    return value.getRow();
                }
            }
            return "Z";
        }
    }

    public TemplateSampleDTO setEnzymeLabelTemplateSample(List<Map<String, Object>> enzymeLabelTemplateSample) {
        fillData(enzymeLabelTemplateSample);
        this.enzymeLabelTemplateSample = enzymeLabelTemplateSample;
        return this;
    }

    public void setData(List<EnzymeLabelSampleVO> enzymeLabelSampleVOList, EnzymeLabelLayoutVO enzymeLabelLayoutVO) {

        data = new ArrayList<>(enzymeLabelSampleVOList);

        enzymeLabelTemplateSample = new ArrayList<>();
        Map<String, Object> colMapResult;
        Map<String, EnzymeLabelSampleVO> rowAddColMap = enzymeLabelSampleVOList.stream().collect(Collectors.toMap(e -> String.valueOf(e.getRow()) + e.getCol(), Function.identity()));

        List<String> rows = RowEnum.selectRow();
        for (String row : rows) {
            colMapResult = new HashMap<>(13);
            colMapResult.put("id", row);
            for (String english : ColEnum.selectEnglish()) {
                int number = ColEnum.getNumber(english);
                String key = RowEnum.getIndex(row) + String.valueOf(number);
                EnzymeLabelSampleVO vo = rowAddColMap.get(key);
                if (vo != null) {
                    vo.setPlateDate(enzymeLabelLayoutVO.getPlateDate() == null ? LocalDate.now() : enzymeLabelLayoutVO.getPlateDate());
                    vo.setSampleTypeName(vo.getSampleType());
                    if(!EnzymeLabelTemplateSampleDTO.SampleType.ORDINARY.getCode().equals(vo.getSampleTypeCode())){
                        vo.setSampleNo(null);
                    }
                }
                colMapResult.put(english, vo);
            }
            enzymeLabelTemplateSample.add(colMapResult);
        }
    }

    public TemplateSampleDTO setData(List<EnzymeLabelSampleVO> data) {
        this.data = new ArrayList<>(data);
        return this;
    }

    /**
     * @see {@link EnzymeLabelTemplateSampleDTO}
     */
    private void fillData(List<Map<String, Object>> enzymeLabelTemplateSample) {
        data = new ArrayList<>();
        for (Map<String, Object> enzymeLabelTemplateSampleDTOMap : enzymeLabelTemplateSample) {
            Object idObj = enzymeLabelTemplateSampleDTOMap.get("id");
            if (Objects.isNull(idObj)) {
                continue;
            }
            String id = String.valueOf(idObj);
            int index = RowEnum.getIndex(id);

            for (String english : ColEnum.selectEnglish()) {
                EnzymeLabelTemplateSampleDTO dto = BeanUtil.toBean(enzymeLabelTemplateSampleDTOMap.get(english), EnzymeLabelTemplateSampleDTO.class);
                if (dto != null) {
                    dto.setRow(index);
                    dto.setCol(ColEnum.getNumber(english));
                    data.add(dto);
                }
            }
        }
    }
}
