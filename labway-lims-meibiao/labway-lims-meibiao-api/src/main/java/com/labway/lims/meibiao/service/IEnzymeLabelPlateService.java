package com.labway.lims.meibiao.service;

import com.labway.lims.meibiao.dto.EnzymeLabelPlateDTO;
import com.labway.lims.meibiao.vo.EnzymeLabelPlateVO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public interface IEnzymeLabelPlateService {

    /**
     * 查询酶标板， 根据开始时间和结束时间
     * @param startDate
     * @param endDate
     * @return
     */
    List<EnzymeLabelPlateVO> selectByStartAmdEndDate(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 新增酶标板
     * @return
     */
    EnzymeLabelPlateVO addPlate();

    /**
     * 创建这个板面日期的板子
     * @param plateDate
     * @return
     */
    EnzymeLabelPlateVO addPlate(LocalDate plateDate);

    /**
     * 删除酶标板
     * @param plateId
     * @return
     */
    boolean deletePlate(long plateId);

    /**
     * 查询一个板子信息
     * @param plateId
     * @return
     */
    EnzymeLabelPlateVO selectById(long plateId);

    /**
     * 查询一个板子信息
     * @param plateCode
     * @return
     */
    EnzymeLabelPlateVO selectByPlateCode(String plateCode);
}
