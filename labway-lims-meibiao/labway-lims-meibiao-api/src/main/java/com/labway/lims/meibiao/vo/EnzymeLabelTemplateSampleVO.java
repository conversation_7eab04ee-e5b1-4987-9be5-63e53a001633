package com.labway.lims.meibiao.vo;

import com.labway.lims.meibiao.dto.EnzymeLabelTemplateSampleDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.StringJoiner;

@EqualsAndHashCode(callSuper = true)
@Data
public class EnzymeLabelTemplateSampleVO extends EnzymeLabelTemplateSampleDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 仪器项目Id
     */
    private Long reportItemId;

    /**
     * 仪器项目code
     */
    private String reportItemCode;

    /**
     * 仪器项目名
     */
    private String labelReportName;

    /**
     * 仪器报告英文名
     */
    private String labelReportEnName;

    /**
     * 仪器id
     */
    private Long instrumentId;

    /**
     * 样本类型名
     */
    private String sampleTypeName;

    /**
     * 计算公式
     */
    private String calcFormula;


    @Override
    public String toString() {
        return new StringJoiner(", ", EnzymeLabelTemplateSampleVO.class.getSimpleName() + "[", "]")
                .add("reportItemCode='" + reportItemCode + "'")
                .add("labelReportName='" + labelReportName + "'")
                .add("sampleTypeName='" + sampleTypeName + "'")
                .add("plateSampleId=" + getPlateSampleId())
                .add("layoutId=" + getLayoutId())
                .add("labelReportId=" + getLabelReportId())
                .add("sampleTypeCode='" + getSampleTypeCode() + "'")
                .add("sampleType='" + getSampleType() + "'")
                .add("col=" + getCol())
                .add("row=" + getRow())
                .add("plateId=" + getPlateId())
                .add("plateCode='" + getPlateCode() + "'")
                .add("sampleNo=" + getSampleNo())
                .toString();
    }
}
