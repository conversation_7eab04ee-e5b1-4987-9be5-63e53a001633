package com.labway.lims.meibiao.service;

import com.labway.lims.meibiao.dto.EnzymeLabelTemplateSampleDTO;
import com.labway.lims.meibiao.dto.TemplateSampleDTO;
import com.labway.lims.meibiao.vo.EnzymeLabelLayoutVO;
import com.labway.lims.meibiao.vo.EnzymeLabelTemplateSampleVO;

import java.util.List;
import java.util.Map;

public interface IEnzymeLabelTemplateSampleService {

    /**
     * 根据酶标布局id查询 模板样本
     * @param layoutId 布局id
     */
    List<Map<String, Object>> selectByLayoutId(long layoutId);

    /**
     * 新增模板样本
     */
    boolean insertBatchTemplateSample(List<EnzymeLabelTemplateSampleDTO> tbEnzymeLabelTemplateSamples);


    /**
     * 根据模板名查询
     * @param templateName
     * @return
     */
    EnzymeLabelLayoutVO selectByTemplateName(String templateName);
}