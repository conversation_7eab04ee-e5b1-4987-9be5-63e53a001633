package com.labway.lims.meibiao.vo;

import com.labway.lims.meibiao.dto.CalcFormulaVariableReplaceDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.StringJoiner;

@EqualsAndHashCode(callSuper = true)
@Data
public class CalcFormulaVariableReplaceVO extends CalcFormulaVariableReplaceDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @Override
    public String toString() {
        return new StringJoiner(", ", CalcFormulaVariableReplaceVO.class.getSimpleName() + "[", "]")
                .add("replaceId=" + getReplaceId())
                .add("formulaId=" + getFormulaId())
                .add("variableCode='" + getVariableCode() + "'")
                .add("variable='" + getVariable() + "'")
                .add("compareSymbol='" + getCompareSymbol() + "'")
                .add("compareValue=" + getCompareValue())
                .add("replaceValue=" + getReplaceValue())
                .add("sort=" + getSort())
                .toString();
    }
}
