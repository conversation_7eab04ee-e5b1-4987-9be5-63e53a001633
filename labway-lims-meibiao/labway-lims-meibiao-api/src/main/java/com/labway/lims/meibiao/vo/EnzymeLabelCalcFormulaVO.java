package com.labway.lims.meibiao.vo;

import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * @description 酶标计算公式
 * <AUTHOR>
 * @date 2023-11-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EnzymeLabelCalcFormulaVO extends EnzymeLabelCalcFormulaDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @Override
    public String toString() {
        return new StringJoiner(", ", EnzymeLabelCalcFormulaVO.class.getSimpleName() + "[", "]")
                .add("calcFormula='" + getCalcFormula() + "'")
                .add("cutoffCalcFormula='" + getCutoffCalcFormula() + "'")
                .add("formulaId=" + getFormulaId())
                .add("formulaShort='" + getFormulaShort() + "'")
                .add("sort=" + getSort())
                .add("formulaDesc='" + getFormulaDesc() + "'")
                .toString();
    }
}
