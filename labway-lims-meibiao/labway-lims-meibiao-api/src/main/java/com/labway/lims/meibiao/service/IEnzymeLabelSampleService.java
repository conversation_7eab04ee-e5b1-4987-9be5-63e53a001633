package com.labway.lims.meibiao.service;

import com.labway.lims.meibiao.dto.*;

import javax.script.ScriptException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public interface IEnzymeLabelSampleService {

    /**
     * 新增样本详情  (先删除之前的， 再新增)
     * @param enzymeLabelTemplateSampleDTOS
     * @return
     */
    boolean insertBatchSample(List<EnzymeLabelTemplateSampleDTO> enzymeLabelTemplateSampleDTOS, long plateId, LocalDate sampleNoDate);

    /**
     * 根据布局id查询
     * @param plateId
     * @return
     */
    TemplateSampleDTO selectByPlateId(long plateId, Boolean isSubtractBlank) throws ScriptException;


    /**
     * 修改原始od
     */
    boolean updateOriginalODValue(long plateSampleId, BigDecimal originalODValue);

    /**
     * 接受仪器结果
     * @param dto
     * @return
     */
    boolean saveInstrumentResult(InstrumentResultDTO dto);

    /**
     * 推送常规检验报告
     * @param plateId
     * @return
     */
    boolean sendReport(long plateId, Boolean isSubtractBlank) throws ScriptException;

    /**
     * 更新样本号
     * @param dto
     * @return
     */
    boolean updateSampleNo(UpdateSampleNoDTO dto);

    /**
     * 删除数据
     * @param plateSampleIds
     * @return
     */
    boolean deleteByPlateSampleId(Collection<Long> plateSampleIds);
}
