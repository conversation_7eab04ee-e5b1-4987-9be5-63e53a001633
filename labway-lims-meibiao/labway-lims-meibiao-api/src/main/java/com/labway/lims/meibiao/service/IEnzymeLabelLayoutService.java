package com.labway.lims.meibiao.service;

import com.labway.lims.meibiao.dto.EnzymeLabelLayoutDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelTemplateSampleDTO;
import com.labway.lims.meibiao.vo.EnzymeLabelLayoutVO;

import java.util.List;

public interface IEnzymeLabelLayoutService {

    /**
     * 新增模板布局 和模板详情
     * @param labelLayoutDTO 布局设置
     * @param data 布局详情
     * @return 是否添加成功
     */
    boolean addLabelLayoutAndTemplate(EnzymeLabelLayoutDTO labelLayoutDTO, List<EnzymeLabelTemplateSampleDTO> data);


    /**
     * 新增样本布局 和样本详情
     * @param labelLayoutDTO 布局设置
     * @param data 布局详情
     * @return 是否添加成功
     */
    boolean addLabelLayoutAndSample(EnzymeLabelLayoutDTO labelLayoutDTO, List<EnzymeLabelTemplateSampleDTO> data);

    /**
     * 查询所有模板信息
     * @return
     */
    List<EnzymeLabelLayoutVO> selectAllTemplate(Integer isTemplate);

    /**
     * 根据板子id查询布局id
     * @param plateId
     * @return
     */
    EnzymeLabelLayoutVO selectByPlateId(long plateId);

    /**
     * 查询布局信息
     * @param layoutId
     * @return
     */
    EnzymeLabelLayoutVO selectByLayoutId(Long layoutId);
}
