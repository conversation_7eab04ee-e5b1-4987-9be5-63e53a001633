package com.labway.lims.meibiao.dto;


import com.labway.lims.meibiao.emun.CompareEnum;

import java.time.LocalDate;

/**
 * 一些静态参数
 */
public class StateDataDTO {

    public static final LocalDate NOW_DATE = LocalDate.of(2099, 12, 30);
    public static final String FORWARD_SLASH = "/";
    public static final long LONG_ZERO = 0L;
    public static final long LONG_MINUS_ONE = -1L;
    public static final int INT_MINUS_ONE = -1;
    public static final int INT_ZERO = 0;
    public static final int INT_ONE = 1;

    public static final String GT = CompareEnum.GT.getCompare();
    public static final String GE = CompareEnum.GE.getCompare();
    public static final String EQ = CompareEnum.EQ.getCompare();
    public static final String LT = CompareEnum.LT.getCompare();
    public static final String LE = CompareEnum.LE.getCompare();




    public static final int PLATE_SAMPLE_NUM = 96;

    private StateDataDTO(){}
}
