package com.labway.lims.meibiao.dto;

import com.labway.lims.meibiao.vo.EnzymeLabelReportItemVO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
public class PrintRawRecordResultDTO implements Serializable {

    /**
     * 阳性来源
     */
    private Set<String> positiveSource;
    /**
     * 阳性批号
     */
    private Set<String> positiveBatch;
    /**
     * 试剂厂家
     */
    private Set<String> reagentManufacturer;
    /**
     * 测试方法
     */
    private Set<String> testMethod;

    /**
     * 阴性来源
     */
    private Set<String> negativeSource;
    /**
     * 阴性批号
     */
    private Set<String> negativeBatch;

    /**
     * 仪器编号
     */
    private Set<String> instrumentCode;

    /**
     * 试剂批号
     */
    private Set<String> reagentBatch;

    /**
     * 检验者
     */
    private Set<String> inspector;

    /**
     * 试剂有效期
     */
    private Set<String> reagentValidDate;

    /**
     * 复检者
     */
    private Set<String> reviewer;



    public PrintRawRecordResultDTO(List<EnzymeLabelReportItemVO> vos){
        this.reagentManufacturer = vos.stream().map(EnzymeLabelReportItemDTO::getReagentManufacturer).collect(Collectors.toSet());
        this.reagentBatch = vos.stream().map(EnzymeLabelReportItemDTO::getReagentBatch).collect(Collectors.toSet());
        this.testMethod = vos.stream().map(EnzymeLabelReportItemDTO::getTestMethod).collect(Collectors.toSet());
        this.instrumentCode = vos.stream().map(EnzymeLabelReportItemVO::getInstrumentCode).collect(Collectors.toSet());

        this.reagentValidDate = vos.stream().map(e-> e.getReagentValidDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).collect(Collectors.toSet());
    }


    public String getPositiveSource() {
        return StringUtils.join(positiveSource , ",");
    }

    public String getPositiveBatch() {
        return StringUtils.join(positiveBatch , ",");
    }

    public String getReagentManufacturer() {
        return StringUtils.join(reagentManufacturer, ",");
    }

    public String getTestMethod() {
        return StringUtils.join(testMethod, ",");
    }

    public String getNegativeSource() {
        return StringUtils.join(negativeSource, ",");
    }

    public String getNegativeBatch() {
        return StringUtils.join(negativeBatch, ",");
    }

    public String getInstrumentCode() {
        return StringUtils.join(instrumentCode, ",");
    }

    public String getReagentBatch() {
        return StringUtils.join(reagentBatch, ",");
    }

    public String getInspector() {
        return StringUtils.join(inspector, ",");
    }

    public String getReagentValidDate() {
        return StringUtils.join(reagentValidDate, ",");
    }

    public String getReviewer() {
        return StringUtils.join(reviewer, ",");
    }
}
