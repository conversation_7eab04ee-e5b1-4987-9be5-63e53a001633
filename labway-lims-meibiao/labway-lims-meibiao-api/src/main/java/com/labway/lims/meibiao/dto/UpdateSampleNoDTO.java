package com.labway.lims.meibiao.dto;

import cn.hutool.core.lang.Assert;
import lombok.Data;

import java.io.Serializable;

@Data
public class UpdateSampleNoDTO implements Serializable {

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 板子上的样本id
     */
    private Long plateSampleId;

    /**
     * 板子id
     */
    private Long plateId;

    public void verifyParams(){
        Assert.notNull(sampleNo, "样本号不能为空");
        Assert.notNull(plateSampleId, "板子样本id不能为空");
        Assert.notNull(plateId, "酶标板id不能为空");
    }
}
