package com.labway.lims.meibiao.dto;

import cn.hutool.core.lang.Assert;
import lombok.*;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EnzymeLabelTemplateSampleDTO  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酶标板样本id
     */
    private Long plateSampleId;

    /**
     * 布局ID
     */
    private Long layoutId;

    /**
     * 酶标报告项目ID
     */
    private Long labelReportId;

    /**
     * 样本类型编码(0普通样本 1阴性对照 2阳性对照 3质控品 4空白 5标准品)
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleType;

    /**
     * 列
     */
    private Integer col;

    /**
     * 行
     */
    private Integer row;

    /**
     * 板子id
     */
    private Long plateId;

    /**
     * 板子code
     */
    private String plateCode;

    /**
     * 样本号
     */
    private String sampleNo;


    @Getter
    public enum SampleType{
        ORDINARY("0", "S", "普通样本"),
        NEGATIVE("1", "NC","阴性对照"),
        POSITIVE("2","PC","阳性对照"),
        QC("3","QC", "质控"),
        BLANK("4", "BC","空白"),
        STANDARD("5", "ST","标准品"),
        ;


        private final String code;

        private final String name;

        private final String remake;

        SampleType(String code, String name, String remake) {
            this.code = code;
            this.name = name;
            this.remake = remake;
        }

        public static List<String> codeList(){
            return Arrays.stream(values()).map(SampleType::getCode).collect(Collectors.toList());
        }

        public static String selectByCode(String code){
            for (SampleType value : values()) {
                if(value.getCode().equals(code)){
                    return value.getName();
                }
            }
            return "";
        }
    }

    public void verifySaveTemplateParams() {
        Assert.notNull(getLabelReportId(), "报告项目不能为空");
        Assert.isTrue(SampleType.codeList().contains(getSampleTypeCode()), "项目类型错误 0普通样本 1阴性对照 2阳性对照 3质控 4空白 5标准品");
        this.setSampleType(SampleType.selectByCode(this.getSampleTypeCode()));
    }

    public void verifySaveSampleParams() {
        verifySaveTemplateParams();

//        Assert.notNull(getPlateId(), "酶免板id不能为空");
        if(getSampleTypeCode().equals(SampleType.ORDINARY.getCode())){
            Assert.notNull(getSampleNo(), "样本号不能为空");
        }
    }

}
