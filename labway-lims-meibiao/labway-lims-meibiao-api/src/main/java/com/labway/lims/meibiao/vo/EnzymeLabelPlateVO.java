package com.labway.lims.meibiao.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.meibiao.dto.EnzymeLabelPlateDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.StringJoiner;


@EqualsAndHashCode(callSuper = true)
@Data
public class EnzymeLabelPlateVO extends EnzymeLabelPlateDTO {

    /**
     * 版面日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate plateDate;

    /**
     * 新增人
     */
    private String creatorName;


    @Override
    public String toString() {
        return new StringJoiner(", ", EnzymeLabelPlateVO.class.getSimpleName() + "[", "]")
                .add("plateId=" + getPlateId())
                .add("plateCode='" + getPlateCode() + "'")
                .add("remark='" + getRemark() + "'")
                .toString();
    }
}
