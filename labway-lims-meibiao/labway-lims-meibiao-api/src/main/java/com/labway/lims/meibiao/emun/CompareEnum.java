package com.labway.lims.meibiao.emun;

import lombok.Getter;

@Getter
public enum CompareEnum {

    GT(">"),
    GE("≥"),
    LT("<"),
    LE("≤"),
    EQ("=")
    ;


    private final String compare;

    CompareEnum(String compare){
        this.compare = compare;
    }

    public static boolean compare(String compare){
        CompareEnum[] values = values();
        for (CompareEnum value : values) {
            if(value.getCompare().equals(compare)){
                return true;
            }
        }
        return false;
    }
}
