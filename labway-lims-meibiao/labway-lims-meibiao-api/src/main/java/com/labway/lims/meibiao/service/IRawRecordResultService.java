package com.labway.lims.meibiao.service;

import com.labway.lims.meibiao.dto.SelectRawRecordResultDTO;
import com.labway.lims.meibiao.vo.EnzymeLabelReportItemVO;
import com.labway.lims.meibiao.vo.RawRecordResultVO;

import java.util.List;

/**
 * 原始记录结果（免疫）
 */
public interface IRawRecordResultService {

    /**
     * 查询原始结果记录， （免疫）
     * @param dto
     * @return
     */
    RawRecordResultVO selectRawRecordResult(SelectRawRecordResultDTO dto);

    /**
     * 根据数据生成酶标板
     * @param dto
     * @return
     */
    boolean generatePlate(SelectRawRecordResultDTO dto, RawRecordResultVO vo);

    /**
     * 查询乙肝两对半的项目信息
     * @return
     */
    List<EnzymeLabelReportItemVO> selectHepatitisB();
}
