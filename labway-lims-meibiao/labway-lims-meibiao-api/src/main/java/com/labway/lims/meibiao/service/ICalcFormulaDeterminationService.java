package com.labway.lims.meibiao.service;

import com.labway.lims.meibiao.dto.CalcFormulaDeterminationDTO;
import com.labway.lims.meibiao.vo.CalcFormulaDeterminationVO;

import java.util.Collection;
import java.util.List;

public interface ICalcFormulaDeterminationService {

    /**
     * 根据检验公式id查询
     * @param formulaId
     * @return
     */
    List<CalcFormulaDeterminationVO> selectAllByFormulaId(long formulaId);

    /**
     * 新增定性描述
     * @param dto
     * @return
     */
    boolean addDetermination(CalcFormulaDeterminationDTO dto);

    /**
     * 跟新定性描述
     * @param dto
     * @return
     */
    boolean updateDetermination(CalcFormulaDeterminationDTO dto);

    /**
     * 根据id删除
     * @param determinationId
     * @return
     */
    boolean deleteByDeterminationId(long determinationId);

    /**
     * 查看定性描述维护  根据计算公式ids
     * @param formulaIds  计算公式ids
     * @return
     */
    List<CalcFormulaDeterminationVO> selectByFormulaIds(Collection<Long> formulaIds);
}
