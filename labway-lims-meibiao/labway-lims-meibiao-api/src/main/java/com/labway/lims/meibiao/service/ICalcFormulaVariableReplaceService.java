package com.labway.lims.meibiao.service;

import com.labway.lims.meibiao.dto.CalcFormulaVariableReplaceDTO;
import com.labway.lims.meibiao.vo.CalcFormulaVariableReplaceVO;

import java.util.Collection;
import java.util.List;

public interface ICalcFormulaVariableReplaceService {

    /**
     * 根据计算公式id查询
     * @param formulaId
     * @return
     */
    List<CalcFormulaVariableReplaceVO> selectByFormulaId(long formulaId);

    /**
     * 新增计算变量替换
     * @param dto
     * @return
     */
    boolean addVariableReplace(CalcFormulaVariableReplaceDTO dto);

    /**
     * 修改计算变量替换
     * @param dto
     * @return
     */
    boolean updateVariableReplace(CalcFormulaVariableReplaceDTO dto);

    /**
     * 删除
     * @param replace
     * @return
     */
    boolean deleteByReplaceId(long replace);

    /**
     * 根据计算公式计算变量替换
     * @param formulaIds 计算公式id
     * @return
     */
    List<CalcFormulaVariableReplaceVO> selectByFormulaIds(Collection<Long> formulaIds);
}
