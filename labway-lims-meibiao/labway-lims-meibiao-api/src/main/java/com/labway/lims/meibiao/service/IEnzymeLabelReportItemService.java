package com.labway.lims.meibiao.service;

import com.labway.lims.meibiao.dto.EnzymeLabelReportItemDTO;
import com.labway.lims.meibiao.vo.EnzymeLabelReportItemVO;

import java.util.Collection;
import java.util.List;

public interface IEnzymeLabelReportItemService {

    /**
     * 查询所有
     * @return
     */
    List<EnzymeLabelReportItemVO> selectList();

    /**
     * 新增酶标仪报告项目
     * @param dto
     * @return
     */
    boolean addEnzymeLabelReportItem(EnzymeLabelReportItemDTO dto);

    /**
     * 修改酶标仪报告项目
     * @param dto
     * @return
     */
    boolean updateEnzymeLabelReportItem(EnzymeLabelReportItemDTO dto);


    /**
     * 删除酶标仪报告项目
     * @param labelReportItemId
     * @return
     */
    boolean deleteEnzymeLabelReportItem(long labelReportItemId);

    /**
     * 查看酶标项目根据id
     * @param labelReportId
     */
    EnzymeLabelReportItemVO selectByLabelReportId(long labelReportId);

    /**
     * 查看酶标项目根据id
     * @param labelReportId
     */
    List<EnzymeLabelReportItemVO> selectByLabelReportIds(Collection<Long> labelReportId);
}
