package com.labway.lims.meibiao.dto;

import cn.hutool.core.lang.Assert;
import com.labway.lims.meibiao.emun.CompareEnum;
import com.labway.lims.meibiao.emun.VariableReplaceEnum;
import com.labway.lims.meibiao.vo.CalcFormulaVariableReplaceVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CalcFormulaVariableReplaceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 变量替换ID
     */
    private Long replaceId;

    /**
     * 计算公式ID
     */
    private Long formulaId;

    /**
     * 变量名称编码
     */
    private String variableCode;

    /**
     * 变量名称
     */
    private String variable;

    /**
     * 比较符号
     */
    private String compareSymbol;

    /**
     * 比较数值
     */
    private BigDecimal compareValue;

    /**
     * 替换数值
     */
    private BigDecimal replaceValue;

    /**
     * 顺序
     */
    private Integer sort;


    /**
     * 新增参数校验
     */
    public void verifySaveParams() {
        Assert.notNull(this.getFormulaId(), "计算公式id不能为空");

        if (null == this.getSort() || this.getSort() < 0 || this.getSort().toString().length() > 10) {
            throw new IllegalArgumentException("显示顺序不能为空 不能为负数 长度最大10位");
        }
        if (StringUtils.isBlank(this.getVariableCode()) || !VariableReplaceEnum.codeList().contains(this.getVariableCode())) {
            throw new IllegalArgumentException("变量名称不正确");
        } else {
            this.setVariable(VariableReplaceEnum.getVariableName(this.getVariableCode()));
        }
        if (StringUtils.isBlank(this.getCompareSymbol()) || !CompareEnum.compare(this.getCompareSymbol())) {
            throw new IllegalArgumentException("比较符号不正确");
        }
        if (this.getReplaceValue() == null || this.getReplaceValue().scale() > 4 || this.getReplaceValue().toString().length() > 10) {
            throw new IllegalArgumentException("替换数值不正确, 最大支持小数点后四位, 总长度最高10位");
        }
        if (this.getCompareValue() == null || this.getCompareValue().scale() > 4 || this.getCompareValue().toString().length() > 10) {
            throw new IllegalArgumentException("比较数值不正确, 最大支持小数点后四位, 总长度最高10位");
        }

    }

    /**
     * 修改参数校验
     */
    public void verifyUpdateParams() {
        Assert.notNull(this.getReplaceId(), "变量替换id不能为空");

        verifySaveParams();
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
