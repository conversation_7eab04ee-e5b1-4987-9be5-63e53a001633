package com.labway.lims.meibiao.dto;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.StringJoiner;

@Data
public class SelectRawRecordResultDTO {

    /**
     * 检验开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime startTestDate;

    /**
     * 检验结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime endTestDate;

    /**
     * 是否包含已申报告
     */
    private Boolean isAudit;

    /**
     * 酶标仪报告项目id
     */
    private Long labelReportId;

    /**
     * 放置规则 (0横向 1竖向)
     */
    private String placeRuleCode;

    /**
     * 板面日期
     */
    private LocalDate plateDate;

    /**
     * 是否生成质控
     */
    private Boolean isGenerateQc;

    /**
     * 是否生成阴性对照
     */
    private Boolean isNegativeContrast;

    /**
     * 是否生成阳性对照
     */
    private Boolean isPositiveContrast;

    /**
     * 是否留样复测
     */
    private Boolean isReservedSample;

    /**
     * 是否有空白
     */
    private Boolean isGenerateBlank;

    /**
     * 是否有标准品
     */
    private Boolean isGenerateStandard;

    public String uniqueResultKey() {
        final StringJoiner stringJoiner = new StringJoiner(":");

        // 是否生成固定类型样本
        String str = toBoolStr(isAudit) +
                toBoolStr(isGenerateQc) +
                toBoolStr(isGenerateBlank) +
                toBoolStr(isReservedSample) +
                toBoolStr(isGenerateStandard) +
                toBoolStr(isPositiveContrast) +
                toBoolStr(isNegativeContrast);

        //
        return stringJoiner
                .add(plateDate.toString())
                .add(startTestDate.toLocalDate().toString() + "-" + endTestDate.toLocalDate().toString())
                .add(String.valueOf(labelReportId))
                .add(placeRuleCode)
                .add(str)
                .toString();
    }

    private String toBoolStr(Boolean bool) {
        return BooleanUtils.isTrue(bool) ? "T" : "F";
    }


    public void verifySelectParams() {
        Assert.notNull(this.getStartTestDate(), "检验开始时间不能为空");
        Assert.notNull(this.getEndTestDate(), "检验结束时间不能为空");
        Assert.isTrue(this.getStartTestDate().isBefore(getEndTestDate()), "检验开始时间要在结束时间之前");
        Assert.notNull(this.getIsAudit(), "是否包含已审报告不能为空");
        Assert.notNull(this.getLabelReportId(), "报告项目不能为空");

        Assert.notNull(this.getIsGenerateQc(), "请选择是否生成质控");
        Assert.notNull(this.getIsReservedSample(), "请选择是否留样复测");
        Assert.notNull(this.getIsNegativeContrast(), "请选择是否生成阴性对照");
        Assert.notNull(this.getIsPositiveContrast(), "请选择是否生成阳性对照");
        Assert.notNull(this.getIsGenerateBlank(), "请选择是否生成空白");
        Assert.notNull(this.getIsGenerateStandard(), "请选择是否生成标准品");
    }

    public void verifyGeneratePlateParams() {
        if (!EnzymeLabelLayoutDTO.PlaceRule.codeList().contains(this.getPlaceRuleCode())) {
            throw new IllegalArgumentException("防止规则编码不能为空 0横向 1竖向");
        }
        Assert.notNull(getPlateDate(), "板面日期不能为空");
        verifySelectParams();
    }


    @Override
    public String toString() {
        return new StringJoiner(", ", SelectRawRecordResultDTO.class.getSimpleName() + "[", "]")
                .add("startTestDate=" + startTestDate)
                .add("endTestDate=" + endTestDate)
                .add("isAudit=" + isAudit)
                .add("labelReportId=" + labelReportId)
                .add("placeRuleCode='" + placeRuleCode + "'")
                .add("plateDate=" + plateDate)
                .add("isGenerateQc=" + isGenerateQc)
                .add("isNegativeContrast=" + isNegativeContrast)
                .add("isPositiveContrast=" + isPositiveContrast)
                .add("isReservedSample=" + isReservedSample)
                .add("isGenerateBlank=" + isGenerateBlank)
                .add("isGenerateStandard=" + isGenerateStandard)
                .toString();
    }
}
