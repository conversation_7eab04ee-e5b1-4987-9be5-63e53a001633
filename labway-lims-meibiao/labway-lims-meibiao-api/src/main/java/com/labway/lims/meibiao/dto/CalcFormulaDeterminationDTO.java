package com.labway.lims.meibiao.dto;

import cn.hutool.core.lang.Assert;
import com.labway.lims.meibiao.emun.CompareEnum;
import com.labway.lims.meibiao.util.HexStringChecker;
import com.labway.lims.meibiao.vo.CalcFormulaDeterminationVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.StringJoiner;


/**
 * <AUTHOR>
 * @description 定性描述维护
 * @date 2023-11-22
 */
@Data
public class CalcFormulaDeterminationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 定性描述ID
     */
    private Long determinationId;

    /**
     * 计算公式ID
     */
    private Long formulaId;

    /**
     * 显示颜色
     */
    private String colors;

    /**
     * 比较符号
     */
    private String compareSymbol;

    /**
     * 比较数值
     */
    private BigDecimal compareValue;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 详细描述
     */
    private String detailDesc;

    /**
     * 简述
     */
    private String brief;


    /**
     * 新增参数校验
     */
    public void verifySaveParams() {
        Assert.notNull(this.getFormulaId(), "计算公式id不能为空");

        if (null == this.getSort() || this.getSort() < 0 || this.getSort().toString().length() > 10) {
            throw new IllegalArgumentException("显示顺序不能为空 不能为负数 长度最大10位");
        }
        if (StringUtils.isBlank(this.getColors()) || !HexStringChecker.colorIsHexString(this.getColors())) {
            throw new IllegalArgumentException("请确认颜色是否正确");
        }
        if (StringUtils.isBlank(this.getCompareSymbol()) || !CompareEnum.compare(this.getCompareSymbol())) {
            throw new IllegalArgumentException("比较符号不正确");
        }
        if (this.getCompareValue() == null || this.getCompareValue().scale() > 4 || this.getCompareValue().toString().length() > 10) {
            throw new IllegalArgumentException("比较数值不正确, 最大支持小数点后四位, 总长度最高10位");
        }
        if (StringUtils.isBlank(this.getDetailDesc()) || this.getDetailDesc().length() > 255) {
            throw new IllegalArgumentException("详细描述不能为空");
        }
        if (StringUtils.isNotBlank(this.getBrief()) && this.getBrief().length() > 50) {
            throw new IllegalArgumentException("简述长度不能超过50");
        }
    }

    /**
     * 修改参数校验
     */
    public void verifyUpdateParams() {
        Assert.notNull(this.getDeterminationId(), "定性描述id不能为空");

        verifySaveParams();
    }
}