package com.labway.lims.meibiao.service;


import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;
import com.labway.lims.meibiao.vo.EnzymeLabelCalcFormulaVO;

import java.util.List;

public interface IEnzymeLabelCalcFormulaService {

    /**
     * 根据计算公式id获取一个
     * @param formulaId
     * @return
     */
    EnzymeLabelCalcFormulaVO selectByFormulaId(long formulaId);


    /**
     * 查询所有检验公式
     * @return EnzymeLabelCalcFormulaVO
     */
    List<EnzymeLabelCalcFormulaVO> selectAll();

    /**
     * 新增检验公式
     * @param dto
     * @return
     */
    boolean addCheckFormula(EnzymeLabelCalcFormulaDTO dto);

    /**
     * 修改检验公式
     * @param dto
     */
    boolean updateCheckFormula(EnzymeLabelCalcFormulaDTO dto);

    /**
     * 单删公式
     * @param formulaId
     * @return
     */
    boolean deleteCheckFormula(long formulaId);

}
