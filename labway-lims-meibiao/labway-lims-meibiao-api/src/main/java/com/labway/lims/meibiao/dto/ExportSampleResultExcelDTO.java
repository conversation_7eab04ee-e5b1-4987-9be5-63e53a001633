package com.labway.lims.meibiao.dto;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.meibiao.vo.EnzymeLabelPlateVO;
import com.labway.lims.meibiao.vo.EnzymeLabelReportItemVO;
import com.labway.lims.meibiao.vo.EnzymeLabelSampleVO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class ExportSampleResultExcelDTO {

    /**
     * 检验时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate testDate;

    /**
     * 检验方法
     */
    private Set<String> testMethod = new HashSet<>();

    /**
     * 版号
     */
    private String plateCode;

    /**
     * 仪器名称
     */
    private Set<String> instrumentName = new HashSet<>();

    /**
     * 主波长
     */
    private Set<String> testWaveLength = new HashSet<>();

    /**
     * 参考波长
     */
    private Set<String> referenceWaveLength = new HashSet<>();

    /**
     * 检验人
     */
    private String creatorName;

    /**
     * 检验项目   reportName / 计算公式：formula / cutoff： cutoffResult
     */
    private Set<String> reportItemNameAndFormula = new HashSet<>();

    /**
     * 项目名称 英文
     */
    private Set<String> itemEnName = new HashSet<>();

    /**
     * 试剂批号
     */
    private Set<String> reagentBatch = new HashSet<>();

    /**
     * 期效
     */
    private Set<String> reagentValidDate = new HashSet<>();

    /**
     * 试剂厂商
     */
    private Set<String> reagentManufacturer = new HashSet<>();

    /**
     * 换行符
     */
    private static String lineFeed;

    public String getTestDate() {
        return testDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    public String getTestMethod() {
        return StringUtils.join(testMethod, ",");
    }

    public String getInstrumentName() {
        return StringUtils.join(instrumentName, ",");
    }

    public String getTestWaveLength() {
        return StringUtils.join(testWaveLength, ",");
    }

    public String getReferenceWaveLength() {
        return StringUtils.join(referenceWaveLength, ",");
    }

    public String getReportItemNameAndFormula() {
        return StringUtils.join(reportItemNameAndFormula, lineFeed);
    }

    public String getItemEnName() {
        return StringUtils.join(itemEnName, ",");
    }

    public String getReagentBatch() {
        return StringUtils.join(reagentBatch, ",");
    }

    public String getReagentValidDate() {
        return StringUtils.join(reagentValidDate, ",");
    }

    public String getReagentManufacturer() {
        return StringUtils.join(reagentManufacturer, ",");
    }

    public ExportSampleResultExcelDTO(List<EnzymeLabelReportItemVO> vos,
                                      EnzymeLabelPlateVO enzymeLabelPlateVO,
                                      List<EnzymeLabelTemplateSampleDTO> data,
                                      String lineFeed
                                      ) {

        List<EnzymeLabelSampleVO> enzymeLabelSampleVOList = BeanUtil.copyToList(data, EnzymeLabelSampleVO.class);

        this.lineFeed = lineFeed;

        testDate = enzymeLabelPlateVO.getPlateDate();
        creatorName = enzymeLabelPlateVO.getCreatorName();
        plateCode = enzymeLabelPlateVO.getPlateCode();

        for (EnzymeLabelReportItemVO vo : vos) {
            testMethod.add(vo.getTestMethod());
            instrumentName.add(vo.getInstrumentName());
            testWaveLength.add(vo.getTestWaveLength());
            referenceWaveLength.add(vo.getReferenceWaveLength());
            itemEnName.add(vo.getReportItemEnName());
            reagentBatch.add(vo.getReagentBatch());
            reagentValidDate.add(vo.getReagentValidDate() == null ? null : vo.getReagentValidDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            reagentManufacturer.add(vo.getReagentManufacturer());
        }

        for (EnzymeLabelSampleVO vo : enzymeLabelSampleVOList) {
            // 检验项目   reportName / 计算公式：formula / cutoff： cutoffResult
            String reportInfo = vo.getLabelReportName() + " / 计算公式: ";
            if (vo.getCalcFormula() == null) {
                EnzymeLabelReportItemDTO.RationResult rationResult = vo.getRationResult();

                reportInfo = reportInfo + "A:" + rationResult.getA() + " B:" + rationResult.getB() + " R²:" + rationResult.getR();
            } else {
                reportInfo = reportInfo + vo.getCalcFormula()+" / cutoff:" + vo.getCutoffValue();
            }
            reportItemNameAndFormula.add(reportInfo);
        }


    }
}
