package com.labway.lims.meibiao.vo;

import com.labway.lims.meibiao.dto.CalcFormulaDeterminationDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @description 定性描述维护
 * <AUTHOR>
 * @date 2023-11-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CalcFormulaDeterminationVO extends CalcFormulaDeterminationDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @Override
    public String toString() {
        return new StringJoiner(", ", CalcFormulaDeterminationVO.class.getSimpleName() + "[", "]")
                .add("determinationId=" + getDeterminationId())
                .add("formulaId=" + getFormulaId())
                .add("colors='" + getColors() + "'")
                .add("compareSymbol='" + getCompareSymbol() + "'")
                .add("compareValue=" + getCompareValue())
                .add("sort=" + getSort())
                .add("detailDesc='" + getDetailDesc() + "'")
                .add("brief='" + getBrief() + "'")
                .toString();
    }
}
