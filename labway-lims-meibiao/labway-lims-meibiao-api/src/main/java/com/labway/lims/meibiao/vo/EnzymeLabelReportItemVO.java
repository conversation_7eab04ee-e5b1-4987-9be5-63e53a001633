package com.labway.lims.meibiao.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelReportItemDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.StringJoiner;

/**
 * 酶标仪项目
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EnzymeLabelReportItemVO extends EnzymeLabelReportItemDTO
        implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器通道码
     */
    private String instrumentChannel;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 报告项目 英文名
     */
    private String reportItemEnName;
    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 创建人名
     */
    private String creatorName;

    /**
     * 计算公式 dto
     */
    private EnzymeLabelCalcFormulaVO enzymeLabelCalcFormulaVO;

    /**
     * 定型描述维护
     */
    private List<CalcFormulaDeterminationVO> calcFormulaDeterminationVOS;

    /**
     * 计算变量替换
     */
    private List<CalcFormulaVariableReplaceVO> calcFormulaVariableReplaceVOS;


    @Override
    public String toString() {
        return new StringJoiner(", ", EnzymeLabelReportItemVO.class.getSimpleName() + "[", "]")
                .add("instrumentName='" + instrumentName + "'")
                .add("instrumentCode='" + instrumentCode + "'")
                .add("instrumentChannel='" + instrumentChannel + "'")
                .add("reportItemName='" + reportItemName + "'")
                .add("reportItemCode='" + reportItemCode + "'")
                .add("enzymeLabelCalcFormulaVO=" + enzymeLabelCalcFormulaVO)
                .add("calcFormulaDeterminationVOS=" + calcFormulaDeterminationVOS)
                .add("calcFormulaVariableReplaceVOS=" + calcFormulaVariableReplaceVOS)
                .add("rationResult=" + getRationResult())
                .add("labelReportId=" + getLabelReportId())
                .add("instrumentId=" + getInstrumentId())
                .add("reportItemId=" + getReportItemId())
                .add("formulaId=" + getFormulaId())
                .add("projectColor='" + getProjectColor() + "'")
                .add("testWaveLength='" + getTestWaveLength() + "'")
                .add("referenceWaveLength='" + getReferenceWaveLength() + "'")
                .add("testMethod='" + getTestMethod() + "'")
                .add("reagentBatch='" + getReagentBatch() + "'")
                .add("reagentValidDate=" + getReagentValidDate())
                .add("reagentManufacturer='" + getReagentManufacturer() + "'")
                .add("grayZoneMinFormula='" + getGrayZoneMinFormula() + "'")
                .add("grayZoneMaxFormula='" + getGrayZoneMaxFormula() + "'")
                .add("itemTypeCode='" + getItemTypeCode() + "'")
                .add("itemTypeName='" + getItemTypeName() + "'")
                .add("rationSetting=" + getRationSetting())
                .add("isGenerateQc=" + getIsGenerateQc())
                .add("isNegativeContrast=" + getIsNegativeContrast())
                .add("negativeContrastNum=" + getNegativeContrastNum())
                .add("isPositiveContrast=" + getIsPositiveContrast())
                .add("positiveContrastNum=" + getPositiveContrastNum())
                .add("isReservedSample=" + getIsReservedSample())
                .add("isGenerateBlank=" + getIsGenerateBlank())
                .add("generateBlankNum=" + getGenerateBlankNum())
                .add("isGenerateStandard=" + getIsGenerateStandard())
                .add("generateStandardNum=" + getGenerateStandardNum())
                .toString();
    }
}