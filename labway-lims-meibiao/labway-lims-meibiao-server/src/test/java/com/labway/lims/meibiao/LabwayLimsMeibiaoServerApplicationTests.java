package com.labway.lims.meibiao;

import cn.hutool.core.lang.Assert;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
//@SpringBootTest
class LabwayLimsMeibiaoServerApplicationTests {

	@Test
	void contextLoads() throws ScriptException {
		String a = " 1 / 0  / ";
		BigDecimal calculate = calculate(a);
		System.out.println(calculate);
	}


	public BigDecimal calculate(String formula) throws ScriptException {
		Object eval = null;
		try {
			ScriptEngineManager scriptEngineManager = new ScriptEngineManager();

			ScriptEngine javaScript = scriptEngineManager.getEngineByName("JavaScript");

			eval = javaScript.eval(formula);

			if (eval instanceof Number) {
				if (!"Infinity".equals(eval.toString())) {
					return BigDecimal.valueOf(Double.parseDouble(eval.toString())).setScale(3, RoundingMode.HALF_UP);
				}
			}
			throw new NumberFormatException("计算结果异常");
		} catch (ScriptException exception) {
			log.info("计算结果异常计算公式【{}】，计算结果【{}】", formula, eval);
			throw new ScriptException("计算公式异常：" + formula);
		}
	}

	private String pattern = "^[0-9\\+\\-\\*\\/\\(\\)]+$";



	@Test
	public void test1(){
		String str = "3+4*(2-1)/2";
		boolean matches1 = str.matches(pattern);
		System.out.println(matches1);

		String replace = str.replace("\u3000", "").replace("\\","/").replace(" ", "");
		System.out.println(replace);
	}

	@Test
	public void test2(){
		String str = "4";
		String str1 = "4.1234";
		String str2 = "4.12345";
		System.out.println(isNumber(str, 4));
		System.out.println(isNumber(str1, 4));
		System.out.println(isNumber(str2, 4));
	}

	public boolean isNumber(String s, int length) {
		try {
			if (s.contains(".") && (s.substring(s.lastIndexOf(".")).length()-1) > length) {
				return false;
			}
			Double.parseDouble(s);
			return true;
		} catch (NumberFormatException e) {
			return false;
		}
	}

	@Test
	public void test3(){
		BigDecimal bigDecimal = new BigDecimal("123.456");
		System.out.println(bigDecimal.unscaledValue().toString().length());
		BigDecimal bigDecimal1 = new BigDecimal("123.456");
		System.out.println();
		BigDecimal number = new BigDecimal("123.456");
		int length = number.unscaledValue().toString().length();
		System.out.println("实际值的长度：" + length);
	}


	private BigDecimal absorbency = new BigDecimal("2.2341");

	@Test
	public void test4(){

		System.out.println(absorbency.toString().length());
	}

	private boolean isNumber(String formula ){
		try {
			formula = formula.substring(1);
			double v = Double.parseDouble(formula);
			return true;
		} catch (NumberFormatException e) {
			return false;
		}
	}

	@Test
	public void test5(){
		List<Integer> array = Arrays.asList(1, 2, 3, 4, 5, 6);
		List<Integer> ints = rearrangeArray(array, 2, 3);
		System.out.println(array);
		System.out.println(ints);
	}
	public List<Integer> rearrangeArray(List<Integer> array, int row, int col) {
		ArrayList<Integer> list = new ArrayList<>(row * col);

		List<List<Integer>> rowList = Lists.partition(array, col);
		
		for (int i = 0; i < rowList.size(); i++) {
			List<Integer> colList = rowList.get(i);
			for (int j = 0; j < colList.size(); j++) {
				Integer num = rowList.get(i).get(j);
				list.add(i*j+j,num);
			}
		}
		return list;
	}

	@Test
	public void test10(){
		ThreadLocalRandom current = ThreadLocalRandom.current();
		for (int i = 0; i < 100; i++) {
			int i1 = current.nextInt(0, 11);
			System.out.println(i1);
		}
		int size = 10;
		for (int i = 0; i < 10; i++) {
			System.out.println(size++);
		}
		System.out.println(size);
	}


	@Test
	public void test11(){
		// 假设有一个二维数组 arr
		int[][] arr = {{1, 2, 3}, {4, 5, 6}, {7, 8, 9}};

// 同时进行横向和纵向遍历
		for (int i = 0; i < arr.length; i++) {
			// 横向遍历
			for (int j = 0; j < arr[i].length; j++) {
				// 输出每个元素 arr[i][j]
				System.out.println("横向遍历元素: " + arr[i][j]);
			}

			// 纵向遍历
			if (i < arr[0].length) {
				for (int[] row : arr) {
					// 输出每个元素 row[i]
					System.out.println("纵向遍历元素: " + row[i]);
				}
			}
		}
	}

}
