package com.labway.lims.meibiao.dal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.meibiao.mapper.CalcFormulaVariableReplaceMapper;
import com.labway.lims.meibiao.model.TbCalcFormulaDetermination;
import com.labway.lims.meibiao.model.TbCalcFormulaVariableReplace;
import com.labway.lims.meibiao.model.TbEnzymeLabelCalcFormula;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.CalcFormulaVariableReplaceVO;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static org.apache.tomcat.util.json.JSONParserConstants.EOF;

@Component
@CacheConfig(cacheNames = "variableReplaceDal")
public class CalcFormulaVariableReplaceDal {

    @Resource
    private CalcFormulaVariableReplaceMapper calcFormulaVariableReplaceMapper;


    /**
     * 查看变量替换， 根据公式id
     * @return List<CalcFormulaVariableReplaceVO>
     */
    @Cacheable(key = "'selectByFormulaIdOrderBySort:' + #formulaId")
    public List<CalcFormulaVariableReplaceVO> selectByFormulaIdOrderBySort(long formulaId) {
        LambdaQueryWrapper<TbCalcFormulaVariableReplace> wrapper = Wrappers.lambdaQuery(TbCalcFormulaVariableReplace.class)
                .eq(TbCalcFormulaVariableReplace::getFormulaId, formulaId)
                .orderByAsc(TbCalcFormulaVariableReplace::getSort, TbCalcFormulaVariableReplace::getCreateDate);
        List<TbCalcFormulaVariableReplace> tbCalcFormulaVariableReplaces = calcFormulaVariableReplaceMapper.selectList(wrapper);
        return JsonUtils.beanToArray(tbCalcFormulaVariableReplaces, CalcFormulaVariableReplaceVO.class);
    }


    /**
     * 新增变量替换
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean saveVariableReplace(TbCalcFormulaVariableReplace tbCalcFormulaVariableReplace) {
        if(ObjectUtils.isEmpty(tbCalcFormulaVariableReplace)){
            return Boolean.FALSE;
        }
        return calcFormulaVariableReplaceMapper.insert(tbCalcFormulaVariableReplace) > EOF;
    }

    /**
     * 修改变量替换
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean updateVariableReplace(TbCalcFormulaVariableReplace tbCalcFormulaVariableReplace) {
        if(ObjectUtils.isEmpty(tbCalcFormulaVariableReplace) || tbCalcFormulaVariableReplace.getReplaceId() == null){
            return Boolean.FALSE;
        }

        LambdaUpdateWrapper<TbCalcFormulaVariableReplace> wrapper = Wrappers.lambdaUpdate(TbCalcFormulaVariableReplace.class)
                .eq(TbCalcFormulaVariableReplace::getReplaceId, tbCalcFormulaVariableReplace.getReplaceId())

                .set(TbCalcFormulaVariableReplace::getVariableCode, tbCalcFormulaVariableReplace.getVariableCode())
                .set(TbCalcFormulaVariableReplace::getVariable, tbCalcFormulaVariableReplace.getVariable())
                .set(TbCalcFormulaVariableReplace::getCompareSymbol, tbCalcFormulaVariableReplace.getCompareSymbol())
                .set(TbCalcFormulaVariableReplace::getCompareValue, tbCalcFormulaVariableReplace.getCompareValue())
                .set(TbCalcFormulaVariableReplace::getReplaceValue, tbCalcFormulaVariableReplace.getReplaceValue());

        return calcFormulaVariableReplaceMapper.update(null, wrapper) > EOF;
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean deleteByReplaceId(long replace) {
        return calcFormulaVariableReplaceMapper.deleteById(replace) > EOF;
    }

    /**
     * 根据 计算公式id删除
     * @param formulaId
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void deleteByFormulaId(long formulaId){
        calcFormulaVariableReplaceMapper.delete(
                Wrappers.lambdaQuery(TbCalcFormulaVariableReplace.class)
                        .eq(TbCalcFormulaVariableReplace::getFormulaId, formulaId)
        );
    }

    @Cacheable(key = "'selectByFormulaIds'+#formulaIds")
    public List<CalcFormulaVariableReplaceVO> selectByFormulaIds(Collection<Long> formulaIds) {
        if(CollectionUtils.isEmpty(formulaIds)) return Collections.emptyList();

        LambdaQueryWrapper<TbCalcFormulaVariableReplace> wrapper = Wrappers.lambdaQuery(TbCalcFormulaVariableReplace.class)
                .in(TbCalcFormulaVariableReplace::getFormulaId, formulaIds)
                .orderByAsc(TbCalcFormulaVariableReplace::getSort);
        List<TbCalcFormulaVariableReplace> tbCalcFormulaVariableReplaces = calcFormulaVariableReplaceMapper.selectList(wrapper);

        if(CollectionUtils.isEmpty(tbCalcFormulaVariableReplaces)) return Collections.emptyList();

        return JsonUtils.beanToArray(tbCalcFormulaVariableReplaces, CalcFormulaVariableReplaceVO.class);
    }
}
