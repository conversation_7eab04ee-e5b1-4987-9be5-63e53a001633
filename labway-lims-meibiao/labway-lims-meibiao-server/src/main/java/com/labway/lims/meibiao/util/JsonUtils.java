package com.labway.lims.meibiao.util;

import cn.hutool.log.Log;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class JsonUtils {

    private static final Log log = Log.get();

    private final static String JSON_SEPERATOR = "%JS%";

    /*
     * 序列化的时候的特性
     *   DisableCircularReferenceDetect 消除对同一对象循环引用的问题，默认为false
     *   SortField 按字段名称排序后输出。默认为false
     *   WriteDateUseDateFormat 全局修改日期格式,默认为false。
     */
    private static SerializerFeature[] serializerFeatures = {
            SerializerFeature.DisableCircularReferenceDetect,
            SerializerFeature.MapSortField,
            SerializerFeature.WriteDateUseDateFormat
    };

    /*
     * 反序列化的时候的特性
     *   OrderedField 属性保持原来的顺序
     */
    private static Feature[] features = {
            Feature.OrderedField
    };

    public static SerializerFeature[] getSerializerFeatures() {
        return serializerFeatures;
    }

    public static void setSerializerFeatures(SerializerFeature[] serializerFeatures) {
        JsonUtils.serializerFeatures = serializerFeatures;
    }

    public static Feature[] getFeatures() {
        return features;
    }

    public static void setFeatures(Feature[] features) {
        JsonUtils.features = features;
    }

    public static JSONObject jsonToBean(String json) {
        try {
            return JSON.parseObject(json, features);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public static <T> T jsonToBean(String json, Class<T> clazz) {
        try {
            return JSON.parseObject(json, clazz, features);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }
    public static Map<String,Object> jsonToMap(String json) {
        try {
            return beanToMap(JSON.parseObject(json, features));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }



    public static <T> String beanToJson(T entity) {
        try {
            return JSON.toJSONString(entity, serializerFeatures);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    public static JSONObject beanToBean(Object entity) {
        try {
            return jsonToBean(beanToJson(entity));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    public static <T> T beanToBean(Object entity, Class<T> clazz) {
        try {
            return jsonToBean(beanToJson(entity) , clazz);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }



    public static JSONArray jsonToArray(String json) {
        try {
            return JSON.parseArray(json);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public static <T> List<T> jsonToArray(String json, Class<T> clazz) {
        try {
            return JSON.parseArray(json , clazz);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public static JSONArray beanToArray(Object entity){
        try{
            String jsonString = beanToJson(entity);
            return jsonToArray(jsonString);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public static <T> List<T> beanToArray(Object entity, Class<T> clazz){
        try{
            String jsonString = beanToJson(entity);
            return jsonToArray(jsonString , clazz);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public static Map<String, Object> beanToMap(Object entity) {
        try {
            return JSON.parseObject(JSONObject.toJSONString(entity, SerializerFeature.WriteMapNullValue)).getInnerMap();
        } catch (Exception e) {
            log.error(e);
            return Collections.emptyMap();
        }
    }

    public static Map<String, String> jsonToStrMap(String json) {
        try {
            Map<String, String> result = new HashMap<>();
            Map<String, Object> stringObjectMap = jsonToMap(json);
            if (CollectionUtils.isEmpty(stringObjectMap)) {
                return new HashMap<>(0);
            }
            for (Map.Entry<String, Object> entry : stringObjectMap.entrySet()) {
                result.put(entry.getKey(), entry.getValue() == null ? null : entry.getValue().toString());
            }
            return result;
        } catch (Exception e) {
            log.error(e);
            return new HashMap<>(0);
        }
    }

    public static Map<String, String> beanToStrMap(Object entity) {
        return jsonToStrMap(JSONObject.toJSONString(entity, serializerFeatures));
    }


    public static <T> List<Map<String, Object>> objectList2ListMap(List<T> objectList) {
        try {
            return objectList.stream().parallel().map(JsonUtils::beanToMap).collect(Collectors.toList());
        }catch (Exception e){
            log.error(e);
            return null;
        }
    }


    public static <T> T jsonToBeanWithException(String json, Class<T> clazz) {
        return JSON.parseObject(json, clazz, features);
    }

    public static <T> String beanToJsonWithNullValue(T entity) {
        try {
            List<SerializerFeature> serializerFeatures = new ArrayList<>(Arrays.asList(JsonUtils.serializerFeatures));
            serializerFeatures.add(SerializerFeature.WriteMapNullValue);
            String jsonString = JSON.toJSONString(entity, serializerFeatures.toArray(new SerializerFeature[]{}));
            return "null".equals(jsonString) ? null : jsonString;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public static JSON filePath2JSON(String filePath){
        try(InputStream inputStream = JsonUtils.class.getClassLoader().getResourceAsStream(filePath)){
            return (JSON) JSON.parse(IOUtils.toString(inputStream, StandardCharsets.UTF_8));
        }catch (Exception e){
            log.error(e.getMessage() , e);
            return null;
        }
    }

    public static Object searchNode(JSON json , String nodeName){
        if(json instanceof JSONArray){
            if(!((JSONArray) json).isEmpty()){
                Object target = ((JSONArray)json).get(0);
                if(target instanceof JSON){
                    return searchNode((JSON) target, nodeName);
                }else {
                    return null;
                }
            }else {
                return null;
            }
        }else if(json instanceof JSONObject){
            List<String> nodes = new ArrayList<>(Arrays.asList(nodeName.split(JSON_SEPERATOR)));
            String realNodeName = nodes.remove(0);
            Object target = ((JSONObject)json).get(realNodeName);
            if(target instanceof JSON && !nodes.isEmpty()){
                return searchNode((JSON) target, StringUtils.join(nodes,JSON_SEPERATOR));
            }else {
                return target;
            }
        }else {
            return null;
        }
    }

}

