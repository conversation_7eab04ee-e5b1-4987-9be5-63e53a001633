package com.labway.lims.meibiao.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelReportItemDTO;
import com.labway.lims.meibiao.model.TbEnzymeLabelReportItem;
import com.labway.lims.meibiao.service.IEnzymeLabelReportItemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 酶标项目
 * @date 2023-11-22
 */
@RequestMapping("/enzyme-label-report-item")
@RestController
public class EnzymeLabelReportItemController extends BaseController {

    @Resource
    private IEnzymeLabelReportItemService iEnzymeLabelReportItemService;


    @GetMapping("/selectAll")
    public Object selectAll(){
        return iEnzymeLabelReportItemService.selectList();
    }

    @PostMapping("/insert")
    public Object saveEnzymeLabelReportItem(@RequestBody EnzymeLabelReportItemDTO dto){
        dto.verifySaveParams();
        return iEnzymeLabelReportItemService.addEnzymeLabelReportItem(dto);
    }

    @PostMapping("/update")
    public Object updateEnzymeLabelReportItem(@RequestBody EnzymeLabelReportItemDTO dto){
        dto.verifyUpdateParams();
        return iEnzymeLabelReportItemService.updateEnzymeLabelReportItem(dto);
    }

    @PostMapping("/delete/{reportItemId}")
    public Object deleteEnzymeLabelReportItem(@PathVariable String reportItemId){
        if (StringUtils.isBlank(reportItemId) || !reportItemId.matches(EnzymeLabelCalcFormulaDTO.PATTERN_NUMBER)) {
            throw new IllegalArgumentException("参数异常！");
        }
        return iEnzymeLabelReportItemService.deleteEnzymeLabelReportItem(Long.parseLong(reportItemId));
    }

    /**
     * 定量设置计算
     * @param rationSettingList
     * @return {@link EnzymeLabelReportItemDTO.RationResult}
     */
    @PostMapping("/quantitative-calculation")
    public Object quantitativeCalculation(@RequestBody List<EnzymeLabelReportItemDTO.RationSetting> rationSettingList){
        if (rationSettingList.size() < 2) throw new IllegalArgumentException("定量设置至少需要两行数据");

        for (EnzymeLabelReportItemDTO.RationSetting rationSetting : rationSettingList) {
            rationSetting.verifyParams();
        }

        EnzymeLabelReportItemDTO enzymeLabelReportItemDTO = new EnzymeLabelReportItemDTO();
        enzymeLabelReportItemDTO.setRationSetting(rationSettingList);
        return enzymeLabelReportItemDTO.getRationResult();
    }

}
