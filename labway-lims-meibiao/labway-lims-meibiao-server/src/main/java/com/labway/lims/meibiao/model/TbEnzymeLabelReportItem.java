package com.labway.lims.meibiao.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * @description 酶标报告项目
 * <AUTHOR>
 * @date 2023-11-22
 */
@Data
@TableName("tb_enzyme_label_report_item")
public class TbEnzymeLabelReportItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酶标报告项目ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long labelReportId;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 计算公式ID
     */
    private Long formulaId;

    /**
     * 项目颜色
     */
    private String projectColor;

    /**
     * 检测波长
     */
    private String testWaveLength;

    /**
     * 参考波长
     */
    private String referenceWaveLength;

    /**
     * 测试方法
     */
    private String testMethod;

    /**
     * 试剂批号
     */
    private String reagentBatch;

    /**
     * 试剂有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate reagentValidDate;

    /**
     * 试剂厂商
     */
    private String reagentManufacturer;

    /**
     * 灰区范围(小)计算公式
     */
    private String grayZoneMinFormula;

    /**
     * 灰区范围(大)计算公式
     */
    private String grayZoneMaxFormula;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime createDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名
     */
    private String creatorName;

    /**
     * 更新id
     */
    private Long updaterId;

    /**
     * 更新人名
     */
    private String updaterName;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime updateDate;


    /**
     * 报告项目类型 00001定性 00002定量 00004其他
     */
    private String itemTypeCode;

    /**
     * 报告项目类型名称 00001定性 00002定量 00004其他
     */
    private String itemTypeName;

    /**
     * 定量设置的json
     */
    private String rationSetting;

    /**
     * 定量设置结果json
     */
    private String rationResult;

    /**
     * 是否生成质控
     */
    private Boolean isGenerateQc;

    /**
     * 是否生成阴性对照
     */
    private Boolean isNegativeContrast;

    /**
     * 阴性对照参考值
     */
    private Integer negativeContrastNum;

    /**
     * 是否生成阳性对照
     */
    private Boolean isPositiveContrast;

    /**
     * 阳性对照参考值
     */
    private Integer positiveContrastNum;

    /**
     * 是否留样复测
     */
    private Boolean isReservedSample;

    /**
     * 是否有空白
     */
    private Boolean isGenerateBlank;

    /**
     * 空白品数量
     */
    private Integer generateBlankNum;

    /**
     * 是否有标准品
     */
    private Boolean isGenerateStandard;

    /**
     * 标准品数量
     */
    private Integer generateStandardNum;

}