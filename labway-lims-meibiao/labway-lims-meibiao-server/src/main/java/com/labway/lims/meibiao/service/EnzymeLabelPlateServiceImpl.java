package com.labway.lims.meibiao.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.meibiao.dal.EnzymeLabelPlateDal;
import com.labway.lims.meibiao.dto.EnzymeLabelPlateDTO;
import com.labway.lims.meibiao.exception.LockException;
import com.labway.lims.meibiao.mapper.EnzymeLabelPlateMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelPlate;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelPlateVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class EnzymeLabelPlateServiceImpl extends ServiceImpl<EnzymeLabelPlateMapper, TbEnzymeLabelPlate>
        implements IEnzymeLabelPlateService {

    @Resource
    private EnzymeLabelPlateDal enzymeLabelPlateDal;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    // 新增酶标板锁
    private static final String ADD_PLATE_LOCK = "labway:lims:meibiao:plate:add:lock";
    // 新增酶标板编号
    private static final String ADD_PLATE_NO = "labway:lims:meibiao:plate:add:no";


    @Override
    public List<EnzymeLabelPlateVO> selectByStartAmdEndDate(LocalDateTime startDate, LocalDateTime endDate) {
        return enzymeLabelPlateDal.selectByStartAmdEndDate(startDate, endDate);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public EnzymeLabelPlateVO addPlate() {
        return addPlate(null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EnzymeLabelPlateVO addPlate(LocalDate plateDate) {
        LoginUserHandler.User user = LoginUserHandler.get();
        log.info("新增酶标板 ：userName:{}, 专业组:{}", user.getNickname(), user.getGroupName());

        LocalDateTime nowDateTime = LocalDateTime.now();
        LocalDate nowDate = plateDate == null ? nowDateTime.toLocalDate() : plateDate;

        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(ADD_PLATE_LOCK, "lock");

        try {
            if (!Boolean.TRUE.equals(lock)) throw new LockException("正在新增酶标板 请稍后再试");

            // 自增序列
            Long increment = stringRedisTemplate.opsForValue().increment(ADD_PLATE_NO + ":" + nowDate, 1);
            // 设置24小时失效
            stringRedisTemplate.expire(ADD_PLATE_NO + ":" + nowDate, 24, TimeUnit.HOURS);
            // 如果为空的话设置为 0
            if (increment == null) increment = 1L;
            String no = leftAppendZero(String.valueOf(increment), 2);
            String code = nowDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "-" + no;

            // 查询当天的板子是否冲突
            TbEnzymeLabelPlate tbEnzymeLabelPlate = enzymeLabelPlateDal.selectByPlateCode(code);
            // 如果有相同的则迭代新增重新生成
            if (tbEnzymeLabelPlate != null) {
                stringRedisTemplate.delete(ADD_PLATE_LOCK);
                return this.addPlate(nowDate);
            }

            EnzymeLabelPlateDTO dto = new EnzymeLabelPlateDTO();
            dto.setPlateCode(code);
            dto.setRemark("");
            tbEnzymeLabelPlate = fillPlateData(dto, user, nowDateTime, nowDate);
            if (!enzymeLabelPlateDal.addPlate(tbEnzymeLabelPlate)) {
                log.info("新增酶标板失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);
                throw new IllegalArgumentException("新增酶标板失败");
            }

            return BeanUtil.toBean(tbEnzymeLabelPlate, EnzymeLabelPlateVO.class);

        } catch (Exception exception) {
            if(!(exception instanceof LockException)) {
                stringRedisTemplate.opsForValue().decrement(ADD_PLATE_NO + ":" + nowDate, 1);
            }
            throw exception;
        } finally {
            stringRedisTemplate.delete(ADD_PLATE_LOCK);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deletePlate(long plateId) {
        LoginUserHandler.User user = LoginUserHandler.get();
        log.info("删除酶标板 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), plateId);

        if (!enzymeLabelPlateDal.deleteByPlateId(plateId)) {

            log.error("删除酶标板失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), plateId);

            throw new IllegalArgumentException("删除酶标板失败");
        }

        return true;
    }

    @Override
    public EnzymeLabelPlateVO selectById(long plateId) {
        return BeanUtil.toBean(enzymeLabelPlateDal.selectByPlateId(plateId), EnzymeLabelPlateVO.class);
    }

    @Override
    public EnzymeLabelPlateVO selectByPlateCode(String plateCode) {
        return BeanUtil.toBean(enzymeLabelPlateDal.selectByPlateCode(plateCode), EnzymeLabelPlateVO.class);
    }

    private TbEnzymeLabelPlate fillPlateData(EnzymeLabelPlateDTO dto, LoginUserHandler.User user, LocalDateTime now, LocalDate plateDate) {
        TbEnzymeLabelPlate tbEnzymeLabelPlate = JsonUtils.beanToBean(dto, TbEnzymeLabelPlate.class);
        Objects.requireNonNull(tbEnzymeLabelPlate, "酶标板数据异常");

        tbEnzymeLabelPlate.setIsDelete(0);
        tbEnzymeLabelPlate.setPlateDate(plateDate);

        tbEnzymeLabelPlate.setOrgId(user.getOrgId());
        tbEnzymeLabelPlate.setOrgName(user.getOrgName());

        tbEnzymeLabelPlate.setCreateDate(now);
        tbEnzymeLabelPlate.setUpdateDate(now);

        tbEnzymeLabelPlate.setCreatorId(user.getUserId());
        tbEnzymeLabelPlate.setCreatorName(user.getNickname());

        tbEnzymeLabelPlate.setUpdaterId(user.getUserId());
        tbEnzymeLabelPlate.setUpdaterName(user.getNickname());
        return tbEnzymeLabelPlate;
    }

    /**
     * 左前补0
     *
     * @param src
     * @param padLength
     * @return
     */
    public static String leftAppendZero(String src, Integer padLength) {
        int length = padLength - src.length();
        StringBuilder srcBuilder = new StringBuilder(src);
        for (int i = 0; i < length; i++) {
            srcBuilder.insert(0, "0");
        }
        src = srcBuilder.toString();
        return src;
    }
}
