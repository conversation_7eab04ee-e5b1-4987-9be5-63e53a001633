package com.labway.lims.meibiao.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.InstrumentItemTypeEnum;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.meibiao.dal.EnzymeLabelSampleDal;
import com.labway.lims.meibiao.dto.CalcFormulaDeterminationDTO;
import com.labway.lims.meibiao.dto.CalcFormulaVariableReplaceDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelReportItemDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelTemplateSampleDTO;
import com.labway.lims.meibiao.dto.InstrumentResultDTO;
import com.labway.lims.meibiao.dto.StateDataDTO;
import com.labway.lims.meibiao.dto.TemplateSampleDTO;
import com.labway.lims.meibiao.dto.UpdateSampleNoDTO;
import com.labway.lims.meibiao.emun.CheckFormulaEnum;
import com.labway.lims.meibiao.emun.VariableReplaceEnum;
import com.labway.lims.meibiao.mapper.EnzymeLabelSampleMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelSample;
import com.labway.lims.meibiao.util.FormulaUtils;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.CalcFormulaDeterminationVO;
import com.labway.lims.meibiao.vo.CalcFormulaVariableReplaceVO;
import com.labway.lims.meibiao.vo.EnzymeLabelLayoutVO;
import com.labway.lims.meibiao.vo.EnzymeLabelPlateVO;
import com.labway.lims.meibiao.vo.EnzymeLabelReportItemVO;
import com.labway.lims.meibiao.vo.EnzymeLabelSampleVO;
import com.labway.lims.routine.api.dto.ReceiveMeiBiaoResultDTO;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EnzymeLabelSampleServiceImpl extends ServiceImpl<EnzymeLabelSampleMapper, TbEnzymeLabelSample> implements IEnzymeLabelSampleService {

    @Resource
    private EnzymeLabelSampleDal enzymeLabelSampleDal;

    @Resource
    private IEnzymeLabelReportItemService iEnzymeLabelReportItemService;

    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;

    @Resource
    private IEnzymeLabelLayoutService iEnzymeLabelLayoutService;

    @Resource
    private ICalcFormulaDeterminationService iCalcFormulaDeterminationService;

    @Resource
    private ICalcFormulaVariableReplaceService iCalcFormulaVariableReplaceService;

    @Resource
    private IEnzymeLabelPlateService iEnzymeLabelPlateService;

    @DubboReference
    private SampleService sampleService;

    @DubboReference
    private SampleResultService sampleResultService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean insertBatchSample(List<EnzymeLabelTemplateSampleDTO> enzymeLabelTemplateSampleDTOS, long plateId, LocalDate sampleNoDate) {
        // 先删除之前的
        enzymeLabelSampleDal.deleteByPlateId(plateId);

        if (CollectionUtils.isEmpty(enzymeLabelTemplateSampleDTOS)) {
            return true;
        }
        List<TbEnzymeLabelSample> tbEnzymeLabelTemplateSamples = fillSampleData(enzymeLabelTemplateSampleDTOS, sampleNoDate);
        boolean result = this.saveBatch(tbEnzymeLabelTemplateSamples);
        // 新增或修改成功后清空缓存
        if (result) enzymeLabelSampleDal.clearCache();
        return result;
    }

    @Override
    public TemplateSampleDTO selectByPlateId(long plateId, Boolean isSubtractBlank) throws ScriptException {
        // 酶标结果vo
        List<EnzymeLabelSampleVO> enzymeLabelSampleVOS = enzymeLabelSampleDal.selectByPlateId(plateId);

        // 查看酶标报告项目
        List<Long> labelReportIds = enzymeLabelSampleVOS.stream().map(EnzymeLabelTemplateSampleDTO::getLabelReportId).collect(Collectors.toList());
        List<EnzymeLabelReportItemVO> enzymeLabelReportItemVOS = iEnzymeLabelReportItemService.selectByLabelReportIds(labelReportIds);
        // 报告项目id 和 报告项目对象
        Map<Long, EnzymeLabelReportItemVO> labelReportItemAndVOMap = enzymeLabelReportItemVOS.stream().collect(Collectors.toMap(EnzymeLabelReportItemDTO::getLabelReportId, Function.identity()));

        // 酶标项目id 和 仪器报告项目id
        Map<Long, Long> labelReportIdAndReportIdMap = enzymeLabelReportItemVOS.stream().collect(Collectors.toMap(EnzymeLabelReportItemDTO::getLabelReportId, EnzymeLabelReportItemDTO::getReportItemId));

        // 计算公式id
        List<Long> formulaIds = enzymeLabelReportItemVOS.stream().map(EnzymeLabelReportItemDTO::getFormulaId).collect(Collectors.toList());
        // 定性描述维护
        List<CalcFormulaDeterminationVO> calcFormulaDeterminationVOS = iCalcFormulaDeterminationService.selectByFormulaIds(formulaIds);
        Map<Long, List<CalcFormulaDeterminationVO>> formulaIdAndDeterminationMap = calcFormulaDeterminationVOS.stream().collect(Collectors.groupingBy(CalcFormulaDeterminationDTO::getFormulaId));

        // 计算变量替换
        List<CalcFormulaVariableReplaceVO> calcFormulaVariableReplaceVOS = iCalcFormulaVariableReplaceService.selectByFormulaIds(formulaIds);
        Map<Long, List<CalcFormulaVariableReplaceVO>> formulaIdAndVariableMap = calcFormulaVariableReplaceVOS.stream().collect(Collectors.groupingBy(CalcFormulaVariableReplaceDTO::getFormulaId));


        // 查看仪器报告项目
        List<Long> reportItemIds = enzymeLabelReportItemVOS.stream().map(EnzymeLabelReportItemDTO::getReportItemId).distinct().collect(Collectors.toList());
        List<InstrumentReportItemDto> instrumentReportItemDtos = instrumentReportItemService.selectByInstrumentReportItemIds(reportItemIds);
        Map<Long, InstrumentReportItemDto> reportItemDtoMap = instrumentReportItemDtos.stream().collect(Collectors.toMap(InstrumentReportItemDto::getInstrumentReportItemId, Function.identity()));

        // TODO 根据样本id查询样本结果
//        List<Long> sampleIds = enzymeLabelSampleVOS.stream().map(EnzymeLabelSampleVO::getSampleId).collect(Collectors.toList());

//        List<SampleResultDto> sampleResultDtos = sampleResultService.selectBySampleIds(sampleIds);
//        Map<Long, List<SampleResultDto>> sampleIdListMap = sampleResultDtos.stream().collect(Collectors.groupingBy(SampleResultDto::getSampleId));


        // 填充  报告项目code和name   原始od值， 因为其他值都是从原始od计算的
        for (EnzymeLabelSampleVO vo : enzymeLabelSampleVOS) {
            // 仪器报告项目id
            Long reportId = labelReportIdAndReportIdMap.get(vo.getLabelReportId());
            // 仪器报告项目
            InstrumentReportItemDto instrumentReportItemDto = reportItemDtoMap.get(reportId);

            if (instrumentReportItemDto != null) {
                vo.setReportItemId(instrumentReportItemDto.getInstrumentReportItemId());
                vo.setReportItemCode(instrumentReportItemDto.getReportItemCode());
                vo.setLabelReportName(instrumentReportItemDto.getReportItemName());
                vo.setLabelReportEnName(instrumentReportItemDto.getEnAb());
                vo.setInstrumentId(instrumentReportItemDto.getInstrumentId());
            }

            // 原始od
            if (StringUtils.isBlank(vo.getOriginalOd())) {
                BigDecimal firstSampleResult = StringUtils.isBlank(vo.getFirstSampleResult()) ? BigDecimal.ZERO : new BigDecimal(vo.getFirstSampleResult());
                BigDecimal secondSampleResult = StringUtils.isBlank(vo.getSecondSampleResult()) ? BigDecimal.ZERO : new BigDecimal(vo.getSecondSampleResult());
                vo.setOriginalODValue(firstSampleResult.subtract(secondSampleResult));
            } else {
                vo.setOriginalODValue(new BigDecimal(vo.getOriginalOd()));
            }
        }

        Map<Long, SampleTypeGroup> sampleTypeGroupMap = getSampleTypeGroupMap(enzymeLabelSampleVOS);


        // 填充od  s/co cutoff值 结果值
        for (EnzymeLabelSampleVO vo : enzymeLabelSampleVOS) {

            SampleTypeGroup sampleTypeGroup = sampleTypeGroupMap.get(vo.getLabelReportId());

            // 酶标报告项目
            EnzymeLabelReportItemVO enzymeLabelReportItemVO = labelReportItemAndVOMap.get(vo.getLabelReportId());

            // OD 值
            BigDecimal odValue = vo.getOriginalODValue();
            if (Boolean.TRUE.equals(isSubtractBlank)) {
                odValue = odValue.subtract(sampleTypeGroup.getBlankValue());
            }
            vo.setODValue(odValue);

            if (Objects.equals(enzymeLabelReportItemVO.getItemTypeCode(), InstrumentItemTypeEnum.QUALITATIVE.getCode())) {
                enzymeLabelReportItemVO.setCalcFormulaDeterminationVOS(this.verifyDetermination(formulaIdAndDeterminationMap, enzymeLabelReportItemVO));
                enzymeLabelReportItemVO.setCalcFormulaVariableReplaceVOS(this.verifyVariableReplace(formulaIdAndVariableMap, enzymeLabelReportItemVO));
                vo.setCalcFormula(enzymeLabelReportItemVO.getEnzymeLabelCalcFormulaVO().getCalcFormula());

                // 定性计算
                qualitativeCalculate(vo, sampleTypeGroup, enzymeLabelReportItemVO);
            } else {
                // 定量计算
                quantitativeCalculate(vo, enzymeLabelReportItemVO);
            }

        }

        EnzymeLabelLayoutVO enzymeLabelLayoutVO = iEnzymeLabelLayoutService.selectByPlateId(plateId);
        if (enzymeLabelLayoutVO == null) enzymeLabelLayoutVO = new EnzymeLabelLayoutVO();
        TemplateSampleDTO templateSampleDTO = new TemplateSampleDTO();
        templateSampleDTO.setData(enzymeLabelSampleVOS, enzymeLabelLayoutVO);
        return templateSampleDTO;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateOriginalODValue(long plateSampleId, BigDecimal originalODValue) {
        LoginUserHandler.User user = LoginUserHandler.get();
        return enzymeLabelSampleDal.updateOriginalODValue(plateSampleId, originalODValue, user);
    }

    @Override
    public boolean saveInstrumentResult(InstrumentResultDTO dto) {

        String plateCode = dto.getPlateCode();
        List<InstrumentResultDTO.Result> results = dto.getResults();

        log.info("接受酶标仪传输结果：{}", dto);

        EnzymeLabelPlateVO enzymeLabelPlateVO = iEnzymeLabelPlateService.selectByPlateCode(plateCode);
        if (Objects.isNull(enzymeLabelPlateVO) || Objects.isNull(enzymeLabelPlateVO.getPlateId())) {
            log.error("酶标板不存在：plateCode = {}", plateCode);
            throw new IllegalArgumentException("酶标板不存在");
        }

        //收集结果
        Map<String, InstrumentResultDTO.Result> rowAddColMap = results.stream().collect(Collectors.toMap(e -> String.valueOf(e.getRow()) + e.getCol(), Function.identity()));

        enzymeLabelSampleDal.clearCache();
        List<EnzymeLabelSampleVO> enzymeLabelSampleVOS = enzymeLabelSampleDal.selectByPlateId(enzymeLabelPlateVO.getPlateId());
        enzymeLabelSampleDal.clearCache();

        for (EnzymeLabelSampleVO vo : enzymeLabelSampleVOS) {
            InstrumentResultDTO.Result result = rowAddColMap.get(String.valueOf(vo.getRow()) + vo.getCol());
            if (Objects.isNull(result)) {
                continue;
            }
            if (InstrumentResultDTO.Frequency.FIRST.equals(dto.getFrequency())) {
                vo.setFirstSampleResult(result.getSampleResult());
            }
            if (InstrumentResultDTO.Frequency.SECOND.equals(dto.getFrequency())) {
                vo.setSecondSampleResult(result.getSampleResult());
            }
        }

        List<TbEnzymeLabelSample> list = BeanUtil.copyToList(enzymeLabelSampleVOS, TbEnzymeLabelSample.class);
        log.info("保存酶标仪传输结果--》{}", list);

        return this.updateBatchById(list);
    }

    @Override
    public boolean sendReport(long plateId, Boolean isSubtractBlank) throws ScriptException {
        EnzymeLabelPlateVO enzymeLabelPlateVO = iEnzymeLabelPlateService.selectById(plateId);

        if (Objects.isNull(enzymeLabelPlateVO)) {
            throw new IllegalArgumentException("酶标项目不存在");
        }

        TemplateSampleDTO templateSampleDTO = this.selectByPlateId(enzymeLabelPlateVO.getPlateId(), isSubtractBlank);
        List<EnzymeLabelTemplateSampleDTO> data = templateSampleDTO.getData();

        List<ReceiveMeiBiaoResultDTO> receiveMeiBiaoResultDTOS = JsonUtils.beanToArray(data, ReceiveMeiBiaoResultDTO.class);

        // 过滤掉质控等样本数据
        List<ReceiveMeiBiaoResultDTO> collect = receiveMeiBiaoResultDTOS.stream()
                .filter(e -> Objects.nonNull(e.getSampleId()) && !Objects.equals(e.getSampleId(), NumberUtils.LONG_ZERO))
                .collect(Collectors.toList());

        return sampleResultService.receiveMeiBiaoResult(collect);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateSampleNo(UpdateSampleNoDTO dto) {
        EnzymeLabelLayoutVO enzymeLabelLayoutVO = iEnzymeLabelLayoutService.selectByPlateId(dto.getPlateId());
        Assert.notNull(enzymeLabelLayoutVO, "酶标板布局不存在！");

        SampleDto sampleDto = sampleService.selectBySampleNo(dto.getSampleNo(), enzymeLabelLayoutVO.getSampleNoDate());
        Long sampleId = StateDataDTO.LONG_ZERO;
        if (Objects.nonNull(sampleDto)) {
            sampleId = sampleDto.getSampleId();
        }
        LambdaUpdateWrapper<TbEnzymeLabelSample> wrapper = Wrappers.lambdaUpdate(TbEnzymeLabelSample.class)
                .eq(TbEnzymeLabelSample::getPlateSampleId, dto.getPlateSampleId())
                .set(TbEnzymeLabelSample::getSampleId, sampleId)
                .set(TbEnzymeLabelSample::getSampleNo, dto.getSampleNo());
        enzymeLabelSampleDal.clearCache();
        return this.update(wrapper);
    }

    @Override
    public boolean deleteByPlateSampleId(Collection<Long> plateSampleIds) {
        return enzymeLabelSampleDal.deleteByPlateSampleId(plateSampleIds);
    }


    private List<TbEnzymeLabelSample> fillSampleData(List<EnzymeLabelTemplateSampleDTO> enzymeLabelTemplateSampleDTOS, LocalDate sampleNoDate) {
        LoginUserHandler.User user = LoginUserHandler.get();
        LocalDateTime now = LocalDateTime.now();
        // 根据样本no查看样本id
        List<EnzymeLabelSampleVO> enzymeLabelSampleVOS = BeanUtil.copyToList(enzymeLabelTemplateSampleDTOS, EnzymeLabelSampleVO.class);
        List<String> sampleNos = enzymeLabelTemplateSampleDTOS.stream().map(EnzymeLabelTemplateSampleDTO::getSampleNo).map(String::valueOf).collect(Collectors.toList());
        List<SampleDto> sampleDtos = sampleService.selectBySampleNos(sampleNos, sampleNoDate);
        Map<String, Long> sampleNoAndIdMap = sampleDtos.stream().collect(Collectors.toMap(SampleDto::getSampleNo, SampleDto::getSampleId, (a, b) -> b));

        return enzymeLabelSampleVOS.stream().map(dto -> TbEnzymeLabelSample.builder().plateSampleId(null)

                        .plateId(dto.getPlateId())
                        .plateCode(dto.getPlateCode())
                        .sampleId(dto.getSampleId() == null ?
                                sampleNoAndIdMap.get(String.valueOf(dto.getSampleNo())) == null ?
                                        0L
                                        : sampleNoAndIdMap.get(String.valueOf(dto.getSampleNo()))
                                : dto.getSampleId()
                        )
                        .sampleNo(dto.getSampleNo() == null ? "0" : dto.getSampleNo())

                        .col(dto.getCol()).row(dto.getRow()).labelReportId(dto.getLabelReportId()).sampleTypeCode(dto.getSampleTypeCode()).sampleType(dto.getSampleType()).orgId(user.getOrgId()).orgName(user.getOrgName()).creatorId(user.getUserId()).creatorName(user.getNickname()).createDate(now).updaterId(user.getUserId()).updaterName(user.getNickname()).updateDate(now).firstSampleResult(Strings.EMPTY).secondSampleResult(Strings.EMPTY).build())
                // TODO 质控 阴阳等没有样本号则么去二次分拣查
//                .filter(dto ->{
//                    if(dto.getSampleId() ==null){
//                        throw new IllegalArgumentException("样本号[ "+dto.getSampleNo()+" ]在二次分拣中不存在");
//                    }
//                    return true;
//                })
                .collect(Collectors.toList());
    }


    /**
     * 定性计算
     * @param vo 结果
     * @param enzymeLabelReportItemVO 计算公式
     */
    private void qualitativeCalculate(EnzymeLabelSampleVO vo, SampleTypeGroup sampleTypeGroup, EnzymeLabelReportItemVO enzymeLabelReportItemVO) throws ScriptException {
        // 计算公式
        String calcFormula = enzymeLabelReportItemVO.getEnzymeLabelCalcFormulaVO().getCalcFormula();
        String cutoffCalcFormula = enzymeLabelReportItemVO.getEnzymeLabelCalcFormulaVO().getCutoffCalcFormula();

        // 定性描述维护
        List<CalcFormulaDeterminationVO> calcFormulaDeterminationVOS = enzymeLabelReportItemVO.getCalcFormulaDeterminationVOS();
        calcFormulaDeterminationVOS.sort(Comparator.comparing(CalcFormulaDeterminationDTO::getSort));
        // 计算变量替换
        List<CalcFormulaVariableReplaceVO> calcFormulaVariableReplaceVOS = enzymeLabelReportItemVO.getCalcFormulaVariableReplaceVOS();
        calcFormulaVariableReplaceVOS.sort(Comparator.comparing(CalcFormulaVariableReplaceDTO::getSort));
        // 变量名称编码 和 对应list  1阴 2阳
        Map<String, List<CalcFormulaVariableReplaceVO>> variableCodeMap = calcFormulaVariableReplaceVOS.stream().collect(Collectors.groupingBy(CalcFormulaVariableReplaceDTO::getVariableCode));

        String realCalcFormula = calcFormula.replace(CheckFormulaEnum.CE_RESULT.getSymbol(), vo.getOriginalODValue().toString()).replace(CheckFormulaEnum.NULL_RESULT.getSymbol(), sampleTypeGroup.getBlankValue().toString()).replace(CheckFormulaEnum.ZHI_RESULT.getSymbol(), sampleTypeGroup.getQcValue().toString()).replace(CheckFormulaEnum.STANDARD.getSymbol(), sampleTypeGroup.getStandardValue().toString());


        // 计算公式包含阴， 变量替换包含阴
        List<CalcFormulaVariableReplaceVO> yinList = variableCodeMap.get(VariableReplaceEnum.YIN.getVariableCode());
        if (calcFormula.contains(VariableReplaceEnum.YIN.getVariable()) && CollectionUtils.isNotEmpty(yinList)) {
            for (CalcFormulaVariableReplaceVO calcFormulaVariableReplaceVO : yinList) {
                String realCalcFormulaBak = switchSymbol(sampleTypeGroup.getNegativeValue(), realCalcFormula, VariableReplaceEnum.YIN.getVariable(), calcFormulaVariableReplaceVO);
                if (!realCalcFormulaBak.equals(realCalcFormula)) {
                    realCalcFormula = realCalcFormulaBak;
                    break;
                }
            }
        }
        realCalcFormula = realCalcFormula.replace(VariableReplaceEnum.YIN.getVariable(), sampleTypeGroup.getNegativeValue().toString());


        List<CalcFormulaVariableReplaceVO> yangList = variableCodeMap.get(VariableReplaceEnum.YANG.getVariableCode());
        if (calcFormula.contains(VariableReplaceEnum.YANG.getVariable()) && CollectionUtils.isNotEmpty(yangList)) {
            for (CalcFormulaVariableReplaceVO calcFormulaVariableReplaceVO : yangList) {
                String realCalcFormulaBak = switchSymbol(sampleTypeGroup.getPositiveValue(), realCalcFormula, VariableReplaceEnum.YANG.getVariable(), calcFormulaVariableReplaceVO);
                if (!realCalcFormulaBak.equals(realCalcFormula)) {
                    realCalcFormula = realCalcFormulaBak;
                    break;
                }
            }
        }
        realCalcFormula = realCalcFormula.replace(VariableReplaceEnum.YANG.getVariable(), sampleTypeGroup.getPositiveValue().toString());

        // 结果
        BigDecimal result = FormulaUtils.calculate(realCalcFormula + "*1", 3);
        vo.setResultValue(result.toString());

        String realCutoffCalcFormula = cutoffCalcFormula.replace(CheckFormulaEnum.CE_RESULT.getSymbol(), vo.getOriginalODValue().toString())
                .replace(CheckFormulaEnum.NULL_RESULT.getSymbol(), sampleTypeGroup.getBlankValue().toString())
                .replace(CheckFormulaEnum.ZHI_RESULT.getSymbol(), sampleTypeGroup.getQcValue().toString())
                .replace(CheckFormulaEnum.STANDARD.getSymbol(), sampleTypeGroup.getStandardValue().toString());

//                        .replace(YIN_CONTRAST.getSymbol(), sampleTypeGroup.getNegativeValue().toString())
//                .replace(CheckFormulaEnum.YANG_RESULT.getSymbol(), sampleTypeGroup.getPositiveValue().toString())


        // 计算公式包含阴， 变量替换包含阴
        if (cutoffCalcFormula.contains(VariableReplaceEnum.YIN.getVariable()) && CollectionUtils.isNotEmpty(yinList)) {
            for (CalcFormulaVariableReplaceVO calcFormulaVariableReplaceVO : yinList) {
                String realCutoffCalcFormulaBak = switchSymbol(sampleTypeGroup.getNegativeValue(), realCutoffCalcFormula, VariableReplaceEnum.YIN.getVariable(), calcFormulaVariableReplaceVO);
                if (!realCutoffCalcFormulaBak.equals(realCutoffCalcFormula)) {
                    realCutoffCalcFormula = realCutoffCalcFormulaBak;
                    break;
                }
            }
        }
        realCutoffCalcFormula = realCutoffCalcFormula.replace(VariableReplaceEnum.YIN.getVariable(), sampleTypeGroup.getNegativeValue().toString());


        if (realCutoffCalcFormula.contains(VariableReplaceEnum.YANG.getVariable()) && CollectionUtils.isNotEmpty(yangList)) {
            for (CalcFormulaVariableReplaceVO calcFormulaVariableReplaceVO : yangList) {
                String realCutoffCalcFormulaBak = switchSymbol(sampleTypeGroup.getPositiveValue(), realCutoffCalcFormula, VariableReplaceEnum.YANG.getVariable(), calcFormulaVariableReplaceVO);
                if (!realCutoffCalcFormulaBak.equals(realCutoffCalcFormula)) {
                    realCutoffCalcFormula = realCutoffCalcFormulaBak;
                    break;
                }
            }
        }
        realCutoffCalcFormula = realCutoffCalcFormula.replace(VariableReplaceEnum.YANG.getVariable(), sampleTypeGroup.getPositiveValue().toString());


        // cutoff 结果
        BigDecimal cutoffResult = FormulaUtils.calculate(realCutoffCalcFormula + "*1", 3);
        vo.setCutoffValue(cutoffResult);
        // sco结果
        BigDecimal scoResult;
        try {
            scoResult = vo.getOriginalODValue().divide(vo.getCutoffValue(), 3, RoundingMode.HALF_UP);
        } catch (Exception exception) {
            scoResult = BigDecimal.ZERO;
        }
        vo.setScoValue(scoResult);

        if (StringUtils.isNotBlank(enzymeLabelReportItemVO.getGrayZoneMinFormula())) {
            // 灰区 小
            BigDecimal minResult = FormulaUtils.calculate(vo.getCutoffValue().toString() + enzymeLabelReportItemVO.getGrayZoneMinFormula());
            vo.setGrayAreaMinValue(minResult);

        }
        if (StringUtils.isNotBlank(enzymeLabelReportItemVO.getGrayZoneMaxFormula())) {
            // 灰区 大
            BigDecimal maxResult = FormulaUtils.calculate(vo.getCutoffValue().toString() + enzymeLabelReportItemVO.getGrayZoneMaxFormula());
            vo.setGrayAreaMaxValue(maxResult);
        }

        for (CalcFormulaDeterminationVO calcFormulaDeterminationVO : calcFormulaDeterminationVOS) {
            if (switchResultSymbol(calcFormulaDeterminationVO, vo)) break;
        }

    }


    /**
     * 定量计算
     */
    private void quantitativeCalculate(EnzymeLabelSampleVO vo, EnzymeLabelReportItemVO enzymeLabelReportItemVO) throws ScriptException {
        EnzymeLabelReportItemDTO.RationResult rationResult = enzymeLabelReportItemVO.getRationResult();
        BigDecimal originalODValue = vo.getOriginalODValue();

        BigDecimal result = rationResult.getB().multiply(originalODValue).add(rationResult.getA());
        vo.setResultValue(result.toString());

        if (StringUtils.isNotBlank(enzymeLabelReportItemVO.getGrayZoneMinFormula())) {
            // 灰区 小
            BigDecimal minResult = FormulaUtils.calculate(vo.getCutoffValue().toString() + enzymeLabelReportItemVO.getGrayZoneMinFormula());
            vo.setGrayAreaMinValue(minResult);

        }
        if (StringUtils.isNotBlank(enzymeLabelReportItemVO.getGrayZoneMaxFormula())) {
            // 灰区 大
            BigDecimal maxResult = FormulaUtils.calculate(vo.getCutoffValue().toString() + enzymeLabelReportItemVO.getGrayZoneMaxFormula());
            vo.setGrayAreaMaxValue(maxResult);
        }
        vo.setRationResult(enzymeLabelReportItemVO.getRationResult());
    }


    /**
     * 校验定性描述维护
     * @param formulaIdAndDeterminationMap
     * @param enzymeLabelReportItemVO
     */
    private List<CalcFormulaDeterminationVO> verifyDetermination(Map<Long, List<CalcFormulaDeterminationVO>> formulaIdAndDeterminationMap, EnzymeLabelReportItemVO enzymeLabelReportItemVO) {
        List<CalcFormulaDeterminationVO> calcFormulaDeterminationVOS = formulaIdAndDeterminationMap.get(enzymeLabelReportItemVO.getFormulaId());
        Assert.notEmpty(calcFormulaDeterminationVOS, "[" + enzymeLabelReportItemVO.getReportItemName() + "]项目的计算公式没有设置定性描述维护");
        return calcFormulaDeterminationVOS;
    }

    /**
     * 校验变量替换
     * @param formulaIdAndVariableMap
     * @param enzymeLabelReportItemVO
     */
    private List<CalcFormulaVariableReplaceVO> verifyVariableReplace(Map<Long, List<CalcFormulaVariableReplaceVO>> formulaIdAndVariableMap, EnzymeLabelReportItemVO enzymeLabelReportItemVO) {
        List<CalcFormulaVariableReplaceVO> calcFormulaVariableReplaceVOS = formulaIdAndVariableMap.get(enzymeLabelReportItemVO.getFormulaId());
        return CollectionUtils.isEmpty(calcFormulaVariableReplaceVOS) ? Collections.emptyList() : calcFormulaVariableReplaceVOS;
    }

    private Map<Long, SampleTypeGroup> getSampleTypeGroupMap(List<EnzymeLabelSampleVO> enzymeLabelSampleVOS) {
        Set<Long> labelReportIds = enzymeLabelSampleVOS.stream().map(EnzymeLabelTemplateSampleDTO::getLabelReportId).collect(Collectors.toSet());

        Map<Long, SampleTypeGroup> map = new HashMap<>();
        for (Long labelReportId : labelReportIds) {
            SampleTypeGroup sampleTypeGroup = new SampleTypeGroup(enzymeLabelSampleVOS, labelReportId);
            map.put(labelReportId, sampleTypeGroup);
        }
        return map;
    }

    @Getter
    private static class SampleTypeGroup {
        private BigDecimal blankValue = BigDecimal.ZERO;
        private BigDecimal negativeValue = BigDecimal.ZERO;
        private BigDecimal positiveValue = BigDecimal.ZERO;
        private BigDecimal qcValue = BigDecimal.ZERO;
        private BigDecimal standardValue = BigDecimal.ZERO;

        private final List<EnzymeLabelSampleVO> enzymeLabelSampleVOS;

        public SampleTypeGroup(List<EnzymeLabelSampleVO> enzymeLabelSampleVOS, Long labelReportId) {
            enzymeLabelSampleVOS = enzymeLabelSampleVOS.stream().filter(e -> e.getLabelReportId().equals(labelReportId)).collect(Collectors.toList());
            this.enzymeLabelSampleVOS = enzymeLabelSampleVOS;
            // 酶标样本类型 和 样本结果map
            Map<String, List<EnzymeLabelSampleVO>> sampleTypeAndVOMap = enzymeLabelSampleVOS.stream().collect(Collectors.groupingBy(EnzymeLabelTemplateSampleDTO::getSampleTypeCode));

            // 分组获取
            List<EnzymeLabelSampleVO> blankValues = sampleTypeAndVOMap.get(EnzymeLabelTemplateSampleDTO.SampleType.BLANK.getCode());
            List<EnzymeLabelSampleVO> negativeValues = sampleTypeAndVOMap.get(EnzymeLabelTemplateSampleDTO.SampleType.NEGATIVE.getCode());
            List<EnzymeLabelSampleVO> positiveValues = sampleTypeAndVOMap.get(EnzymeLabelTemplateSampleDTO.SampleType.POSITIVE.getCode());
            List<EnzymeLabelSampleVO> qcValues = sampleTypeAndVOMap.get(EnzymeLabelTemplateSampleDTO.SampleType.QC.getCode());
            List<EnzymeLabelSampleVO> standardValues = sampleTypeAndVOMap.get(EnzymeLabelTemplateSampleDTO.SampleType.STANDARD.getCode());

            // 取值
            if (CollectionUtils.isNotEmpty(blankValues)) {
                Optional<BigDecimal> reduce = blankValues.stream().map(EnzymeLabelSampleVO::getOriginalODValue).reduce(BigDecimal::add);
                reduce.ifPresent(bigDecimal -> blankValue = bigDecimal.divide(BigDecimal.valueOf(blankValues.size()), 3, RoundingMode.HALF_UP));
            }
            if (CollectionUtils.isNotEmpty(negativeValues)) {
                Optional<BigDecimal> reduce = negativeValues.stream().map(EnzymeLabelSampleVO::getOriginalODValue).reduce(BigDecimal::add);
                reduce.ifPresent(bigDecimal -> negativeValue = bigDecimal.divide(BigDecimal.valueOf(negativeValues.size()), 3, RoundingMode.HALF_UP));
            }
            if (CollectionUtils.isNotEmpty(positiveValues)) {
                Optional<BigDecimal> reduce = positiveValues.stream().map(EnzymeLabelSampleVO::getOriginalODValue).reduce(BigDecimal::add);
                reduce.ifPresent(bigDecimal -> positiveValue = bigDecimal.divide(BigDecimal.valueOf(positiveValues.size()), 3, RoundingMode.HALF_UP));
            }
            if (CollectionUtils.isNotEmpty(qcValues)) {
                Optional<BigDecimal> reduce = qcValues.stream().map(EnzymeLabelSampleVO::getOriginalODValue).reduce(BigDecimal::add);
                reduce.ifPresent(bigDecimal -> qcValue = bigDecimal.divide(BigDecimal.valueOf(qcValues.size()), 3, RoundingMode.HALF_UP));
            }
            if (CollectionUtils.isNotEmpty(standardValues)) {
                Optional<BigDecimal> reduce = standardValues.stream().map(EnzymeLabelSampleVO::getOriginalODValue).reduce(BigDecimal::add);
                reduce.ifPresent(bigDecimal -> standardValue = bigDecimal.divide(BigDecimal.valueOf(standardValues.size()), 3, RoundingMode.HALF_UP));
            }
        }
    }

    private String switchSymbol(BigDecimal resultValue, String str, String symbol, CalcFormulaVariableReplaceVO vo) {
        switch (vo.getCompareSymbol()) {
            case ">":
                if (resultValue.compareTo(vo.getCompareValue()) > 0) {
                    str = str.replace(symbol, vo.getReplaceValue().toString());
                }
                break;
            case "≥":
                if (resultValue.compareTo(vo.getCompareValue()) >= 0) {
                    str = str.replace(symbol, vo.getReplaceValue().toString());
                }
                break;
            case "=":
                if (resultValue.compareTo(vo.getCompareValue()) == 0) {
                    str = str.replace(symbol, vo.getReplaceValue().toString());
                }
                break;
            case "<":
                if (resultValue.compareTo(vo.getCompareValue()) < 0) {
                    str = str.replace(symbol, vo.getReplaceValue().toString());
                }
                break;
            case "≤":
                if (resultValue.compareTo(vo.getCompareValue()) <= 0) {
                    str = str.replace(symbol, vo.getReplaceValue().toString());
                }
                break;
            default:
        }
        return str;
    }

    private boolean switchResultSymbol(CalcFormulaDeterminationVO calcFormulaDeterminationVO, EnzymeLabelSampleVO vo) {
        boolean bool = false;
        switch (calcFormulaDeterminationVO.getCompareSymbol()) {
            case ">":
                if (new BigDecimal(vo.getResultValue()).compareTo(calcFormulaDeterminationVO.getCompareValue()) > 0) {
                    fillData(calcFormulaDeterminationVO, vo);
                    bool = true;
                }
                break;
            case "≥":
                if (new BigDecimal(vo.getResultValue()).compareTo(calcFormulaDeterminationVO.getCompareValue()) >= 0) {
                    fillData(calcFormulaDeterminationVO, vo);
                    bool = true;
                }
                break;
            case "<":
                if (new BigDecimal(vo.getResultValue()).compareTo(calcFormulaDeterminationVO.getCompareValue()) < 0) {
                    fillData(calcFormulaDeterminationVO, vo);
                    bool = true;
                }
                break;
            case "≤":
                if (new BigDecimal(vo.getResultValue()).compareTo(calcFormulaDeterminationVO.getCompareValue()) <= 0) {
                    fillData(calcFormulaDeterminationVO, vo);
                    bool = true;
                }
                break;
            default:
        }
        return bool;
    }

    private void fillData(CalcFormulaDeterminationVO calcFormulaDeterminationVO, EnzymeLabelSampleVO vo) {
        vo.setResultValue(calcFormulaDeterminationVO.getDetailDesc());
        vo.setColors(calcFormulaDeterminationVO.getColors());
        vo.setBrief(calcFormulaDeterminationVO.getBrief());
        vo.setDetailDesc(calcFormulaDeterminationVO.getDetailDesc());
    }
}
