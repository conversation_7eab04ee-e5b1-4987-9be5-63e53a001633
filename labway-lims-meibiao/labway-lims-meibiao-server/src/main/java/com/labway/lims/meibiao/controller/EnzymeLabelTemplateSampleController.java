package com.labway.lims.meibiao.controller;

import cn.hutool.core.lang.Assert;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;
import com.labway.lims.meibiao.service.IEnzymeLabelTemplateSampleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/template-sample")
public class EnzymeLabelTemplateSampleController extends BaseController {

    @Resource
    private IEnzymeLabelTemplateSampleService iEnzymeLabelTemplateSampleService;

    @GetMapping("/select-by-layoutId")
    public Object selectByLayoutId(@RequestParam Long layoutId){
        Assert.notNull(layoutId, "参数不能为空");
        return iEnzymeLabelTemplateSampleService.selectByLayoutId(layoutId);
    }

}
