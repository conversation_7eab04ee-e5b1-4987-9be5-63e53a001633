package com.labway.lims.meibiao.util;

import lombok.extern.slf4j.Slf4j;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;

/**
 * 计算公式带入类
 */
@Slf4j
public class FormulaUtils {
    private static final ScriptEngineManager scriptEngineManager = new ScriptEngineManager();

    private static final ScriptEngine javaScript = scriptEngineManager.getEngineByName("JavaScript");


    public static BigDecimal calculate(String formula) throws ScriptException {
        return calculate(formula, null);
    }

    /**
     * 计算
     *
     * @param formula 计算公式
     * @param num     小数点后几位
     * @return
     * @throws ScriptException
     */
    public static BigDecimal calculate(String formula, Integer num) throws ScriptException {

        Object eval = null;
        try {

            eval = javaScript.eval(formula);

            if (eval instanceof Number) {

                if (!"Infinity".equals(eval.toString())) {
                    if("NaN".equals(eval.toString())){
                        return BigDecimal.ZERO;
                    }

                    BigDecimal reslut = BigDecimal.valueOf(Double.parseDouble(eval.toString()));

                    if (num != null && num > -1) {
                        reslut = reslut.setScale(num, RoundingMode.HALF_UP);
                    }

                    return reslut;

                }

            }

            log.info("计算结果异常计算公式【{}】，计算结果【{}】", formula, eval);

            throw new NumberFormatException("计算异常：" + formula + " 结果："+ eval);

        } catch (ScriptException exception) {

            log.info("计算结果异常计算公式【{}】，计算结果【{}】", formula, eval);

            throw new NumberFormatException("计算异常：" + formula + " 结果："+ eval);
        }
    }
}
