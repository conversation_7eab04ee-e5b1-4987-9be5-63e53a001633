package com.labway.lims.meibiao.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * @description 计算公式变量替换
 * <AUTHOR>
 * @date 2023-11-22
 */
@Data
@TableName("tb_calc_formula_variable_replace")
public class TbCalcFormulaVariableReplace implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 变量替换ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long replaceId;

    /**
     * 计算公式ID
     */
    private Long formulaId;

    /**
     * 变量名称编码
     */
    private String variableCode;

    /**
     * 变量名称
     */
    private String variable;

    /**
     * 比较符号
     */
    private String compareSymbol;

    /**
     * 比较数值
     */
    private BigDecimal compareValue;

    /**
     * 替换数值
     */
    private BigDecimal replaceValue;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime createDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名
     */
    private String creatorName;

    /**
     * 更新id
     */
    private Long updaterId;

    /**
     * 更新人名
     */
    private String updaterName;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime updateDate;
}