package com.labway.lims.meibiao.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelPlateDTO;
import com.labway.lims.meibiao.dto.SelectPlateDTO;
import com.labway.lims.meibiao.service.IEnzymeLabelPlateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 酶标板列表
 * @date 2023-11-22
 */
@RequestMapping("/enzyme-label-plate")
@RestController
public class EnzymeLabelPlateController extends BaseController {

    @Resource
    private IEnzymeLabelPlateService iEnzymeLabelPlateService;


    @PostMapping("select-by-date")
    public Object selectByDate(@RequestBody SelectPlateDTO dto){
        dto.verifyParams();
        return iEnzymeLabelPlateService.selectByStartAmdEndDate(dto.getStartDate(), dto.getEndDate());
    }

    @PostMapping("insert")
    public Object insertPlate(){
        return iEnzymeLabelPlateService.addPlate();
    }

    @PostMapping("delete/{plateId}")
    public Object deletePlate(@PathVariable String plateId){
        if (StringUtils.isBlank(plateId) || !plateId.matches(EnzymeLabelCalcFormulaDTO.PATTERN_NUMBER)) {
            throw new IllegalArgumentException("参数异常！");
        }
        return iEnzymeLabelPlateService.deletePlate(Long.parseLong(plateId));
    }





}
