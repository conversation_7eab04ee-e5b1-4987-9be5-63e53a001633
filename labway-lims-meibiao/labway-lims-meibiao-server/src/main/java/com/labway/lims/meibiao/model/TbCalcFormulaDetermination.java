package com.labway.lims.meibiao.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * @description 定性描述维护
 * <AUTHOR>
 * @date 2023-11-22
 */
@Data
@TableName("tb_calc_formula_determination")
public class TbCalcFormulaDetermination implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 定性描述ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long determinationId;

    /**
     * 计算公式ID
     */
    private Long formulaId;

    /**
     * 显示颜色
     */
    private String colors;

    /**
     * 比较符号
     */
    private String compareSymbol;

    /**
     * 比较数值
     */
    private BigDecimal compareValue;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 详细描述
     */
    private String detailDesc;

    /**
     * 简述
     */
    private String brief;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime createDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名
     */
    private String creatorName;

    /**
     * 更新id
     */
    private Long updaterId;

    /**
     * 更新人名
     */
    private String updaterName;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime updateDate;
}