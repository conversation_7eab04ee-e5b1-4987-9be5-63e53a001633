package com.labway.lims.meibiao.dal;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.meibiao.mapper.CalcFormulaDeterminationMapper;
import com.labway.lims.meibiao.model.TbCalcFormulaDetermination;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.CalcFormulaDeterminationVO;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static org.apache.tomcat.util.json.JSONParserConstants.EOF;

/**
 * <AUTHOR>
 * @description 定性描述维护
 * @date 2023-11-23
 */
@Component
@CacheConfig(cacheNames = "determinationDal")
public class CalcFormulaDeterminationDal {

    @Resource
    private CalcFormulaDeterminationMapper calcFormulaDeterminationMapper;


    /**
     * 根据检验公式id查询 并根据sort排序
     * @param formulaId
     * @return
     */
    @Cacheable(key = "'selectByFormulaIdOrderBySort'+#formulaId")
    public List<CalcFormulaDeterminationVO> selectByFormulaIdOrderBySort(long formulaId) {

        LambdaQueryWrapper<TbCalcFormulaDetermination> wrapper = Wrappers.lambdaQuery(TbCalcFormulaDetermination.class)
                .eq(TbCalcFormulaDetermination::getFormulaId, formulaId)
                .orderByAsc(TbCalcFormulaDetermination::getSort, TbCalcFormulaDetermination::getCreateDate);

        List<TbCalcFormulaDetermination> tbCalcFormulaDeterminations = calcFormulaDeterminationMapper.selectList(wrapper);
        return JsonUtils.beanToArray(tbCalcFormulaDeterminations, CalcFormulaDeterminationVO.class);
    }


    /**
     * 新增
     * @param tbCalcFormulaDetermination
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean insertDetermination(TbCalcFormulaDetermination tbCalcFormulaDetermination) {
        if (ObjectUtils.isEmpty(tbCalcFormulaDetermination)) {
            return Boolean.FALSE;
        }
        return calcFormulaDeterminationMapper.insert(tbCalcFormulaDetermination) > EOF;
    }

    /**
     * 更新
     * @param tbCalcFormulaDetermination
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean updateDetermination(TbCalcFormulaDetermination tbCalcFormulaDetermination) {
        if(tbCalcFormulaDetermination == null || tbCalcFormulaDetermination.getDeterminationId() == null){
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<TbCalcFormulaDetermination> wrapper = Wrappers.lambdaUpdate(TbCalcFormulaDetermination.class)
                .eq(TbCalcFormulaDetermination::getDeterminationId, tbCalcFormulaDetermination.getDeterminationId())

                .set(TbCalcFormulaDetermination::getColors, tbCalcFormulaDetermination.getColors())
                .set(TbCalcFormulaDetermination::getCompareSymbol, tbCalcFormulaDetermination.getCompareSymbol())
                .set(TbCalcFormulaDetermination::getCompareValue, tbCalcFormulaDetermination.getCompareValue())
                .set(TbCalcFormulaDetermination::getDetailDesc, tbCalcFormulaDetermination.getDetailDesc())
                .set(TbCalcFormulaDetermination::getBrief, tbCalcFormulaDetermination.getBrief())
                .set(TbCalcFormulaDetermination::getUpdaterId, tbCalcFormulaDetermination.getUpdaterId())
                .set(TbCalcFormulaDetermination::getUpdaterName, tbCalcFormulaDetermination.getUpdaterName())
                .set(TbCalcFormulaDetermination::getUpdateDate, tbCalcFormulaDetermination.getUpdateDate());

        return calcFormulaDeterminationMapper.update(null, wrapper) > EOF;
    }

    /**
     * 删除  根据id
     * @param determinationId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean deleteByDeterminationId(long determinationId) {
        return calcFormulaDeterminationMapper.deleteById(determinationId) > EOF;
    }


    /**
     * 根据 计算公式id删除
     * @param formulaId
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void deleteByFormulaId(long formulaId){
        calcFormulaDeterminationMapper.delete(
                Wrappers.lambdaQuery(TbCalcFormulaDetermination.class)
                        .eq(TbCalcFormulaDetermination::getFormulaId, formulaId)
        );
    }


    @Cacheable(key = "'selectByFormulaIds'+#formulaIds")
    public List<CalcFormulaDeterminationVO> selectByFormulaIds(Collection<Long> formulaIds) {

        if (CollectionUtils.isEmpty(formulaIds)) return Collections.emptyList();

        LambdaQueryWrapper<TbCalcFormulaDetermination> wrapper = Wrappers.lambdaQuery(TbCalcFormulaDetermination.class)
                .in(TbCalcFormulaDetermination::getFormulaId, formulaIds)
                .orderByAsc(TbCalcFormulaDetermination::getSort);
        List<TbCalcFormulaDetermination> list = calcFormulaDeterminationMapper.selectList(wrapper);

        if(CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return BeanUtil.copyToList(list,  CalcFormulaDeterminationVO.class);

    }
}
