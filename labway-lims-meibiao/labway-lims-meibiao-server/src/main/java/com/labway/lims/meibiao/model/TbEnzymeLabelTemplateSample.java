package com.labway.lims.meibiao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description 酶标板模板样本
 * <AUTHOR>
 * @date 2023-11-27
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("tb_enzyme_label_template_sample")
public class TbEnzymeLabelTemplateSample implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酶标板样本ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long plateSampleId;

    /**
     * 布局ID
     */
    private Long layoutId;

    /**
     * 列
     */
    private Integer col;

    /**
     * 行
     */
    private Integer row;

    /**
     * 酶标报告项目ID
     */
    private Long labelReportId;

    /**
     * 样本类型编码(0普通样本 1阴性对照 2阳性对照 3质控品 4空白 5标准品)
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleType;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime createDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名
     */
    private String creatorName;

    /**
     * 更新id
     */
    private Long updaterId;

    /**
     * 更新人名
     */
    private String updaterName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime updateDate;

}