package com.labway.lims.meibiao.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.io.Serializable;
import java.util.Locale;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * @description 酶标计算公式
 * <AUTHOR>
 * @date 2023-11-22
 */
@Data
@TableName("tb_enzyme_label_calc_formula")
public class TbEnzymeLabelCalcFormula implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 计算公式ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long formulaId;

    /**
     * 计算公式 code
     */
    private String formulaCode;

    /**
     * 计算公式
     */
    private String calcFormula;

    /**
     * 公式简称
     */
    private String formulaShort;

    /**
     * Cutoff计算公式
     */
    private String cutoffCalcFormula;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 公式描述
     */
    private String formulaDesc;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime createDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名
     */
    private String creatorName;

    /**
     * 更新id
     */
    private Long updaterId;

    /**
     * 更新人名
     */
    private String updaterName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime updateDate;

}