package com.labway.lims.meibiao.dal;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.meibiao.dto.StateDataDTO;
import com.labway.lims.meibiao.mapper.EnzymeLabelSampleMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelSample;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelSampleVO;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Component
@CacheConfig(cacheNames = "enzymeLabelSampleDal")
public class EnzymeLabelSampleDal {

    @Resource
    private EnzymeLabelSampleMapper enzymeLabelSampleMapper;


    @CacheEvict(allEntries = true)
    public void clearCache() {
        // TODO 清除缓存用
    }

    /**
     *
     * @param plateId
     * @return
     */
    @Cacheable(key = "'selectByPlateId'+ #plateId")
    public List<EnzymeLabelSampleVO> selectByPlateId(long plateId) {
        List<TbEnzymeLabelSample> tbEnzymeLabelSamples = enzymeLabelSampleMapper.selectList(Wrappers.lambdaQuery(TbEnzymeLabelSample.class).eq(TbEnzymeLabelSample::getPlateId, plateId));
        if (CollectionUtils.isEmpty(tbEnzymeLabelSamples)) {
            return Collections.emptyList();
        }
        return JsonUtils.beanToArray(tbEnzymeLabelSamples, EnzymeLabelSampleVO.class);

    }


    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean deleteByPlateId(Long plateId) {
        enzymeLabelSampleMapper.delete(Wrappers.lambdaQuery(TbEnzymeLabelSample.class).eq(TbEnzymeLabelSample::getPlateId, plateId));
        return Boolean.TRUE;
    }

    /**
     * 修改原始od
     * @param plateSampleId
     * @param originalODValue
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean updateOriginalODValue(long plateSampleId, BigDecimal originalODValue, LoginUserHandler.User user) {

        LambdaUpdateWrapper<TbEnzymeLabelSample> wrapper = Wrappers.lambdaUpdate(TbEnzymeLabelSample.class)
                .eq(TbEnzymeLabelSample::getPlateSampleId, plateSampleId)
                .set(TbEnzymeLabelSample::getOriginalOd, originalODValue.toString())
//                .set(TbEnzymeLabelSample::getFirstSampleResult, originalODValue.toString())
//                .set(TbEnzymeLabelSample::getSecondSampleResult, originalODValue.toString())
                .set(TbEnzymeLabelSample::getUpdaterId, user.getUserId())
                .set(TbEnzymeLabelSample::getUpdaterName, user.getNickname())
                .set(TbEnzymeLabelSample::getUpdateDate, LocalDateTime.now());

        return enzymeLabelSampleMapper.update(null, wrapper) > StateDataDTO.INT_ZERO;
    }

    @Transactional
    @CacheEvict(allEntries = true)
    public boolean deleteByPlateSampleId(Collection<Long> plateSampleIds) {
        return enzymeLabelSampleMapper.deleteBatchIds(plateSampleIds) > StateDataDTO.INT_ZERO;
    }
}
