package com.labway.lims.meibiao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @description 酶标板信息
 * <AUTHOR>
 * @date 2023-11-27
 */
@Data
@TableName("tb_enzyme_label_plate")
public class TbEnzymeLabelPlate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酶标板ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long plateId;

    /**
     * 酶标板号
     */
    private String plateCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime createDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名
     */
    private String creatorName;

    /**
     * 更新id
     */
    private Long updaterId;

    /**
     * 更新人名
     */
    private String updaterName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime updateDate;

    /**
     * 版面日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate plateDate;

}