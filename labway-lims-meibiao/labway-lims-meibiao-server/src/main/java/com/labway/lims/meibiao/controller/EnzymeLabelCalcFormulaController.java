package com.labway.lims.meibiao.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;
import com.labway.lims.meibiao.service.IEnzymeLabelCalcFormulaService;
import com.labway.lims.meibiao.vo.EnzymeLabelCalcFormulaVO;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 酶标计算公式
 * @date 2023-11-22
 */
@RequestMapping("/check-formula")
@RestController
public class EnzymeLabelCalcFormulaController extends BaseController {

    @Resource
    private IEnzymeLabelCalcFormulaService iEnzymeLabelCalcFormulaService;

    @GetMapping("/get/{formulaId}")
    public Object getByFormulaId(@PathVariable String formulaId) {
        if (StringUtils.isBlank(formulaId) || !formulaId.matches(EnzymeLabelCalcFormulaDTO.PATTERN_NUMBER)) {
            throw new IllegalArgumentException("参数异常！");
        }
        EnzymeLabelCalcFormulaVO enzymeLabelCalcFormulaVO = iEnzymeLabelCalcFormulaService.selectByFormulaId(Long.parseLong(formulaId));
        return ObjectUtils.isEmpty(enzymeLabelCalcFormulaVO) ? new EnzymeLabelCalcFormulaVO() : enzymeLabelCalcFormulaVO;
    }

    @GetMapping("selectAll")
    public Object selectAll() {
        return iEnzymeLabelCalcFormulaService.selectAll();
    }

    @PostMapping("/insert")
    public Object saveCheckFormula(@RequestBody EnzymeLabelCalcFormulaDTO dto) {
        dto.verifySaveParams();
        return iEnzymeLabelCalcFormulaService.addCheckFormula(dto);
    }

    @PostMapping("/update")
    public Object updateCheckFormula(@RequestBody EnzymeLabelCalcFormulaDTO dto) {
        dto.verifyUpdateParams();
        return iEnzymeLabelCalcFormulaService.updateCheckFormula(dto);
    }

    @PostMapping("/delete/{formulaId}")
    public Object deleteCheckFormula(@PathVariable String formulaId) {
        if (StringUtils.isBlank(formulaId) || !formulaId.matches(EnzymeLabelCalcFormulaDTO.PATTERN_NUMBER)) {
            throw new IllegalArgumentException("参数异常！");
        }
        return iEnzymeLabelCalcFormulaService.deleteCheckFormula(Long.parseLong(formulaId));
    }

}
