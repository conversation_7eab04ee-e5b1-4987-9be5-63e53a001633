package com.labway.lims.meibiao.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import com.labway.lims.api.enums.base.InstrumentItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.meibiao.dto.EnzymeLabelLayoutDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelTemplateSampleDTO;
import com.labway.lims.meibiao.dto.SelectRawRecordResultDTO;
import com.labway.lims.meibiao.dto.StateDataDTO;
import com.labway.lims.meibiao.dto.TemplateSampleDTO;
import com.labway.lims.meibiao.vo.EnzymeLabelPlateVO;
import com.labway.lims.meibiao.vo.EnzymeLabelReportItemVO;
import com.labway.lims.meibiao.vo.EnzymeLabelSampleVO;
import com.labway.lims.meibiao.vo.RawRecordResultVO;
import com.labway.lims.routine.api.dto.QuerySampleDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.labway.lims.meibiao.dto.StateDataDTO.PLATE_SAMPLE_NUM;

@Slf4j
@Service
@RefreshScope
@CacheConfig(cacheNames = "raw-record-result")
public class RawRecordResultServiceImpl implements IRawRecordResultService {

    @Resource
    private IEnzymeLabelReportItemService iEnzymeLabelReportItemService;

    @Resource
    private IEnzymeLabelPlateService iEnzymeLabelPlateService;

    @Resource
    private IEnzymeLabelLayoutService iEnzymeLabelLayoutService;

    @DubboReference
    private SampleService sampleService;

    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;

    @DubboReference
    private SampleReportItemService sampleReportItemService;


    @Value("${meibiao.reportIds}")
    private String reportIds;

    //    @Cacheable(key = "'selectRawRecordResult'+#dto.toString()")
    @Override
    public RawRecordResultVO selectRawRecordResult(SelectRawRecordResultDTO dto) {
        // 报告项目id
        Long labelReportId = dto.getLabelReportId();

        // 返回前端vo
        RawRecordResultVO vo = new RawRecordResultVO();

        if (Objects.equals(labelReportId, StateDataDTO.LONG_MINUS_ONE)) {
            Assert.notNull(reportIds, "未配置[乙肝两对半]的五个项目");
            String[] split = reportIds.split(",");
            Assert.isTrue(split.length == 5, "乙肝两对半项目数量配置错误 应有:5 实际:" + split.length);

            List<Long> reportIdList = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
            List<EnzymeLabelReportItemVO> enzymeLabelReportItemVOS = iEnzymeLabelReportItemService.selectByLabelReportIds(reportIdList);
            vo = new RawRecordResultVO(enzymeLabelReportItemVOS, reportIdList);

        } else {
            EnzymeLabelReportItemVO enzymeLabelReportItemVO = iEnzymeLabelReportItemService.selectByLabelReportId(labelReportId);
            Assert.notNull(enzymeLabelReportItemVO, "报告项目不存在");
            Assert.isTrue(enzymeLabelReportItemVO.getItemTypeCode().equals(InstrumentItemTypeEnum.QUALITATIVE.getCode()) || enzymeLabelReportItemVO.getItemTypeCode().equals(InstrumentItemTypeEnum.QUANTITATIVE.getCode()), "项目类型异常 未知的项目类型：" + enzymeLabelReportItemVO.getItemTypeName());
            vo.setOneItem(enzymeLabelReportItemVO);
        }

        List<List<RawRecordResultVO.Result>> lists = this.selectRawRecordResult(dto, vo);
        vo.setResult(lists);
        return vo;

    }


    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean generatePlate(SelectRawRecordResultDTO dto, RawRecordResultVO vo) {

        List<List<RawRecordResultVO.Result>> lists = vo.getResult();
        LocalDate plateDate = dto.getPlateDate();
        // 是否是横向
        boolean isCrosswise = EnzymeLabelLayoutDTO.PlaceRule.CROSSWISE.getCode().equals(dto.getPlaceRuleCode());

        // 生成布局和详情
        TemplateSampleDTO templateSampleDTO;
        // 布局设置  （上）
        EnzymeLabelLayoutDTO enzymeLabelLayoutDTO;
        //布局详情   （下）
        List<EnzymeLabelSampleVO> enzymeLabelSampleVOList;

        // 第一块板子
        for (List<RawRecordResultVO.Result> list : lists) {

            // 板子
            EnzymeLabelPlateVO enzymeLabelPlateVO = iEnzymeLabelPlateService.addPlate(plateDate);
            // 生成布局和详情
            templateSampleDTO = new TemplateSampleDTO();
            // 布局设置  （上）
            enzymeLabelLayoutDTO = new EnzymeLabelLayoutDTO();
            enzymeLabelLayoutDTO.setPlateId(enzymeLabelPlateVO.getPlateId());
            enzymeLabelLayoutDTO.setPlaceRuleCode(dto.getPlaceRuleCode());
            enzymeLabelLayoutDTO.setPlaceRule(EnzymeLabelLayoutDTO.PlaceRule.getNameByCode(enzymeLabelLayoutDTO.getPlaceRuleCode()));
            enzymeLabelLayoutDTO.setIsTemplate(StateDataDTO.INT_ZERO);
            enzymeLabelLayoutDTO.setSampleNoDate(enzymeLabelPlateVO.getPlateDate());
            enzymeLabelLayoutDTO.setSampleStartNo(String.valueOf(StateDataDTO.INT_ONE));

            //布局详情   （下）
            enzymeLabelSampleVOList = this.getEnzymeLabelSampleVOList(list, enzymeLabelPlateVO, vo.getOneItem(), isCrosswise, TemplateSampleDTO.ColEnum.ONE.getEnglish());
            templateSampleDTO.setEnzymeLabelLayout(enzymeLabelLayoutDTO);
            templateSampleDTO.setData(enzymeLabelSampleVOList);
            iEnzymeLabelLayoutService.addLabelLayoutAndSample(templateSampleDTO.getEnzymeLabelLayout(), templateSampleDTO.getData());

            if (Objects.nonNull(vo.getTwoItem())) {
                creatorPlate(plateDate, templateSampleDTO, vo.getTwoItem(),  list, isCrosswise, TemplateSampleDTO.ColEnum.TWO.getEnglish());
                creatorPlate(plateDate, templateSampleDTO,  vo.getThreeItem(), list, isCrosswise, TemplateSampleDTO.ColEnum.THREE.getEnglish());
                creatorPlate(plateDate, templateSampleDTO,  vo.getFourItem(), list, isCrosswise,TemplateSampleDTO.ColEnum.FOUR.getEnglish());
                creatorPlate(plateDate, templateSampleDTO,  vo.getFiveItem(), list, isCrosswise,TemplateSampleDTO.ColEnum.FIVE.getEnglish());
            }
        }

        return true;
    }

    @Cacheable(key = "'selectHepatitisB'")
    @Override
    public List<EnzymeLabelReportItemVO> selectHepatitisB() {
        Assert.notNull(reportIds, "未配置[乙肝两对半]的五个项目");
        String[] split = reportIds.split(",");
        Assert.isTrue(split.length == 5, "乙肝两对半项目数量配置错误 应有:5 实际:" + split.length);

        List<Long> reportIdList = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
        return iEnzymeLabelReportItemService.selectByLabelReportIds(reportIdList);
    }


    private void creatorPlate(LocalDate plateDate,
                              TemplateSampleDTO templateSampleDTO,
                              EnzymeLabelReportItemVO reportItemVO,
                              List<RawRecordResultVO.Result> list,
                              boolean isCrosswise,
                              String itemNum) {

        EnzymeLabelLayoutDTO enzymeLabelLayoutDTO = templateSampleDTO.getEnzymeLabelLayout();


        // 板子
        EnzymeLabelPlateVO enzymeLabelPlateVO = iEnzymeLabelPlateService.addPlate(plateDate);

        // 布局设置  （上）
        enzymeLabelLayoutDTO.setPlateId(enzymeLabelPlateVO.getPlateId());
        enzymeLabelLayoutDTO.setPlateCode(enzymeLabelPlateVO.getPlateCode());
        enzymeLabelLayoutDTO.setSampleNoDate(enzymeLabelPlateVO.getPlateDate());

        //布局详情   （下）
        List<EnzymeLabelSampleVO> enzymeLabelSampleVOList = this.getEnzymeLabelSampleVOList(list, enzymeLabelPlateVO, reportItemVO, isCrosswise, itemNum);

        List<EnzymeLabelSampleVO> collect = enzymeLabelSampleVOList.stream().filter(e -> EnzymeLabelTemplateSampleDTO.SampleType.ORDINARY.getCode().equals(e.getSampleTypeCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            iEnzymeLabelPlateService.deletePlate(enzymeLabelPlateVO.getPlateId());
            return;
        }
        templateSampleDTO.setEnzymeLabelLayout(enzymeLabelLayoutDTO);
        templateSampleDTO.setData(enzymeLabelSampleVOList);

        iEnzymeLabelLayoutService.addLabelLayoutAndSample(templateSampleDTO.getEnzymeLabelLayout(), templateSampleDTO.getData());
    }

    private List<List<RawRecordResultVO.Result>> selectRawRecordResult(SelectRawRecordResultDTO dto, RawRecordResultVO vo) {

        // 返回前端分割之前
        List<RawRecordResultVO.Result> resultList = new ArrayList<>();

        // 根据检验时间查样本号  样本id  申请单id
        QuerySampleDto querySampleDto = new QuerySampleDto();
        querySampleDto.setTestDateStart(localDateTime2Date(dto.getStartTestDate()));
        querySampleDto.setTestDateEnd(localDateTime2Date(dto.getEndTestDate()));
        List<SampleDto> sampleDtos = sampleService.selectByTestDate(querySampleDto);
        List<Long> sampleIds = sampleDtos.stream().map(SampleDto::getSampleId).collect(Collectors.toList());
        log.info("样本:[{}]", sampleDtos.stream().map(SampleDto::getSampleNo).collect(Collectors.toSet()));

        // 根据applySampleIds  查看 申请单样本表是否审核
        List<Long> applySampleIds = sampleDtos.stream().map(SampleDto::getApplySampleId).collect(Collectors.toList());
        Stream<ApplySampleDto> stream = applySampleService.selectByApplySampleIds(applySampleIds).stream();
        if (Boolean.FALSE.equals(dto.getIsAudit())) {
            stream = stream.filter(e -> !e.getStatus().equals(20) || !e.getStatus().equals(30));
        }
        List<ApplySampleDto> applySampleDtos = stream.collect(Collectors.toList());
        log.info("过滤后申请单样本:[{}]", applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));

        // 将过滤后的申请单id查看申请单人员信息
        List<Long> applyIds = applySampleDtos.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toList());
        List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);
        Map<Long, ApplyDto> applyIdAndApplyMap = applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity(), (a, b) -> b));
        log.info("申请单信息:[{}]", applyDtos.stream().map(ApplyDto::getApplyId).collect(Collectors.toSet()));

        // 样本报告项目
        List<SampleReportItemDto> sampleReportItemDtos = sampleReportItemService.selectBySampleIds(sampleIds);
        Map<Long, List<SampleReportItemDto>> sampleIdReportItemMap = sampleReportItemDtos.stream().collect(Collectors.groupingBy(SampleReportItemDto::getSampleId));
        log.info("样本报告项目:[{}]", sampleReportItemDtos.stream().map(SampleReportItemDto::getReportItemId).collect(Collectors.toSet()));

        for (SampleDto sampleDto : sampleDtos) {
            ApplyDto applyDto = applyIdAndApplyMap.get(sampleDto.getApplyId());
            if (applyDto == null) continue;
            List<SampleReportItemDto> sampleIdReportItemDtos = sampleIdReportItemMap.get(sampleDto.getSampleId());

            RawRecordResultVO.Result result = new RawRecordResultVO.Result();
            try {
                result.setSampleNo(sampleDto.getSampleNo());
            } catch (Exception e) {
                result.setSampleNo("0");
            }
            result.setSampleId(sampleDto.getSampleId());
            result.setTestDate(LocalDateTimeUtil.of(sampleDto.getTestDate()));

            result.setPatientName(applyDto.getPatientName());
            result.setPatientAge(applyDto.getPatientAge());
            result.setPatientSex(String.valueOf(applyDto.getPatientSex()));
            result.setBarcode(applyDto.getMasterBarcode());
            result.setRemake(applyDto.getRemark());

            if (CollectionUtils.isEmpty(sampleIdReportItemDtos)) {
                continue;
            }
            int i = 0;
            Map<String, List<SampleReportItemDto>> reportItemIdMap = sampleIdReportItemDtos.stream().collect(Collectors.groupingBy(SampleReportItemDto::getReportItemCode));
            if (Objects.nonNull(vo.getOneItem()) && CollectionUtils.isEmpty(reportItemIdMap.get(vo.getOneItem().getReportItemCode()))) {
                // 第二个项目为空， 则不是乙肝两对半直接跳过
                if (Objects.isNull(vo.getTwoItem())) {
                    continue;
                }
                i++;
                result.setOneItem(StateDataDTO.FORWARD_SLASH);
            }
            if (Objects.nonNull(vo.getTwoItem()) && CollectionUtils.isEmpty(reportItemIdMap.get(vo.getTwoItem().getReportItemCode()))) {
                i++;
                result.setTwoItem(StateDataDTO.FORWARD_SLASH);
            }
            if (Objects.nonNull(vo.getThreeItem()) && CollectionUtils.isEmpty(reportItemIdMap.get(vo.getThreeItem().getReportItemCode()))) {
                i++;
                result.setThreeItem(StateDataDTO.FORWARD_SLASH);
            }
            if (Objects.nonNull(vo.getFourItem()) && CollectionUtils.isEmpty(reportItemIdMap.get(vo.getFourItem().getReportItemCode()))) {
                i++;
                result.setFourItem(StateDataDTO.FORWARD_SLASH);
            }
            if (Objects.nonNull(vo.getFiveItem()) && CollectionUtils.isEmpty(reportItemIdMap.get(vo.getFiveItem().getReportItemCode()))) {
                i++;
                result.setFiveItem(StateDataDTO.FORWARD_SLASH);
            }
            // 五个都没做， 跳过
            if (i == 5) continue;
            resultList.add(result);
        }
        log.info("样本报告项目:[{}]", resultList);

        resultList.sort(Comparator.comparing(RawRecordResultVO.Result::getSampleNo));
        return fillData(resultList, vo.getOneItem(), dto);
    }

    private List<EnzymeLabelSampleVO> getEnzymeLabelSampleVOList(List<RawRecordResultVO.Result> list,
                                                                 EnzymeLabelPlateVO enzymeLabelPlateVO,
                                                                 EnzymeLabelReportItemVO enzymeLabelReportItemVO,
                                                                 boolean isCrosswise,
                                                                 String itemNum
                                                                 ) {
        int count = 0;
        int rows;
        int cols;
        if (isCrosswise) {
            rows = TemplateSampleDTO.RowEnum.selectIndex().size();
            cols = TemplateSampleDTO.ColEnum.selectNumber().size();
        } else {
            rows = TemplateSampleDTO.ColEnum.selectNumber().size();
            cols = TemplateSampleDTO.RowEnum.selectIndex().size();
        }

        List<EnzymeLabelSampleVO> enzymeLabelSampleVOList = new ArrayList<>();
        for (int i = 1; i <= rows; i++) {
            for (int j = 1; j <= cols; j++) {
                RawRecordResultVO.Result vo;
                try {
                    vo = list.get(count++);
                } catch (IndexOutOfBoundsException e) {
                    continue;
                }
                if (StateDataDTO.FORWARD_SLASH.equals(vo.selectItem(itemNum))) {
                    continue;
                }
                EnzymeLabelSampleVO enzymeLabelSampleVO = new EnzymeLabelSampleVO();
                enzymeLabelSampleVO.setPlateId(enzymeLabelPlateVO.getPlateId());
                enzymeLabelSampleVO.setPlateCode(enzymeLabelPlateVO.getPlateCode());
                enzymeLabelSampleVO.setSampleId(vo.getSampleId());
                enzymeLabelSampleVO.setSampleNo(vo.getSampleNo());
                enzymeLabelSampleVO.setLabelReportId(enzymeLabelReportItemVO.getLabelReportId());
                if (isCrosswise) {
                    enzymeLabelSampleVO.setRow(i);
                    enzymeLabelSampleVO.setCol(j);
                } else {
                    enzymeLabelSampleVO.setRow(j);
                    enzymeLabelSampleVO.setCol(i);
                }

                if(EnzymeLabelTemplateSampleDTO.SampleType.QC.getCode().equals(vo.getSampleTypeCode())){
                    enzymeLabelSampleVO.setSampleTypeCode(vo.getSampleTypeCode());
                    enzymeLabelSampleVO.setSampleType(vo.getPatientName());
                }else{
                    enzymeLabelSampleVO.setSampleTypeCode(vo.getSampleTypeCode() == null ? EnzymeLabelTemplateSampleDTO.SampleType.ORDINARY.getCode() : vo.getSampleTypeCode());
                    enzymeLabelSampleVO.setSampleType(vo.getSampleType() == null ? EnzymeLabelTemplateSampleDTO.SampleType.ORDINARY.getName() : vo.getSampleType());
                }

                enzymeLabelSampleVOList.add(enzymeLabelSampleVO);
            }
        }
        return enzymeLabelSampleVOList;
    }


    /**
     * 填充 对照样本
     * @param resultList 样本库
     * @param enzymeLabelReportItemVO 报告项目信息
     */
    private List<List<RawRecordResultVO.Result>> fillData(List<RawRecordResultVO.Result> resultList, EnzymeLabelReportItemVO enzymeLabelReportItemVO, SelectRawRecordResultDTO dto) {
        // 每个板子 质控等样本占用的数量
        List<List<RawRecordResultVO.Result>> plateRawRecordResultVoss = getLists(resultList, enzymeLabelReportItemVO);
        LocalDateTime now = LocalDateTime.now();

        RawRecordResultVO.Result vo;
        // 随机数生成器
        ThreadLocalRandom current = ThreadLocalRandom.current();
        List<RawRecordResultVO.Result> resultVOS = this.creatorStandard(dto, enzymeLabelReportItemVO);
        for (List<RawRecordResultVO.Result> results : plateRawRecordResultVoss) {
            int size = results.size();
            // 生成质控品
            if (Boolean.TRUE.equals(dto.getIsGenerateQc())) {
                for (RawRecordResultVO.QcType value : RawRecordResultVO.QcType.values()) {
                    vo = new RawRecordResultVO.Result();
                    vo.setTestDate(now);
                    vo.setQcType(value.getSymbol());
                    vo.setPatientName("QC" + value.getSymbol());
                    vo.setSampleTypeCode(EnzymeLabelTemplateSampleDTO.SampleType.QC.getCode());
                    vo.setSampleType(EnzymeLabelTemplateSampleDTO.SampleType.QC.getName());
                    results.add(current.nextInt(0, size++), vo);
                }
            }
            results.addAll(resultVOS);
        }

        return plateRawRecordResultVoss;
    }


    /**
     * 板子放置样本，  超出96个进行分割
     * @param resultList 原始结果记录
     * @param enzymeLabelReportItemVO 报告项目
     * @return
     */
    private static List<List<RawRecordResultVO.Result>> getLists(List<RawRecordResultVO.Result> resultList, EnzymeLabelReportItemVO enzymeLabelReportItemVO) {
        int plateSampleNum = getPlateSampleNum(enzymeLabelReportItemVO);
        Assert.isTrue(plateSampleNum > 0, "质控品等数量总和>=96 无法生成 请去酶标项目页面修改");

        List<List<RawRecordResultVO.Result>> plateRawRecordResultVoss = new ArrayList<>();
        for (int i = 0; i < resultList.size(); i += plateSampleNum) {
            int end = Math.min(resultList.size(), i + plateSampleNum);
            List<RawRecordResultVO.Result> subList = new ArrayList<>(resultList.subList(i, end));
            plateRawRecordResultVoss.add(subList);
        }

//        List<List<RawRecordResultVO>> plateRawRecordResultVoss = Lists.partition(rawRecordResultVOS, plateSampleNum);
        return plateRawRecordResultVoss;
    }

    private static int getPlateSampleNum(EnzymeLabelReportItemVO enzymeLabelReportItemVO) {
        int num = 0;
        if (Boolean.TRUE.equals(enzymeLabelReportItemVO.getIsGenerateQc())) num += 2;
        if (Boolean.TRUE.equals(enzymeLabelReportItemVO.getIsReservedSample())) num += 2;
        // 阴性对照
        if (Boolean.TRUE.equals(enzymeLabelReportItemVO.getIsNegativeContrast()))
            num += enzymeLabelReportItemVO.getNegativeContrastNum();

        // 阳性对照
        if (Boolean.TRUE.equals(enzymeLabelReportItemVO.getIsPositiveContrast()))
            num += enzymeLabelReportItemVO.getPositiveContrastNum();

        // 空白
        if (Boolean.TRUE.equals(enzymeLabelReportItemVO.getIsGenerateBlank()))
            num += enzymeLabelReportItemVO.getGenerateBlankNum();

        // 标准品
        if (Boolean.TRUE.equals(enzymeLabelReportItemVO.getIsGenerateStandard()))
            num += enzymeLabelReportItemVO.getGenerateStandardNum();

        // 如果超过96个 - 质控样本数  则切割原始样本
        return PLATE_SAMPLE_NUM - num;
    }


    public static Date localDateTime2Date(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 初始化每个板子需要的标准品数据
     * @param dto
     * @param enzymeLabelReportItemVO
     * @return
     */
    private List<RawRecordResultVO.Result> creatorStandard(SelectRawRecordResultDTO dto, EnzymeLabelReportItemVO enzymeLabelReportItemVO) {
        LocalDateTime now = LocalDateTime.now();

        List<RawRecordResultVO.Result> plateRawRecordResultVos = new ArrayList<>();
        RawRecordResultVO.Result vo;

        // 阴性对照
        if (Boolean.TRUE.equals(dto.getIsNegativeContrast())) {
            for (int i = 0; i < enzymeLabelReportItemVO.getNegativeContrastNum(); i++) {
                vo = new RawRecordResultVO.Result();
                vo.setTestDate(now);
                vo.setQcType("阴");
                vo.setPatientName("阴性对照");
                vo.setSampleTypeCode(EnzymeLabelTemplateSampleDTO.SampleType.NEGATIVE.getCode());
                vo.setSampleType(EnzymeLabelTemplateSampleDTO.SampleType.NEGATIVE.getName());
                plateRawRecordResultVos.add(vo);
            }
        }

        // 阳性对照
        if (Boolean.TRUE.equals(dto.getIsPositiveContrast())) {
            for (int i = 0; i < enzymeLabelReportItemVO.getPositiveContrastNum(); i++) {
                vo = new RawRecordResultVO.Result();
                vo.setTestDate(now);
                vo.setQcType("阳");
                vo.setPatientName("阳性对照");
                vo.setSampleTypeCode(EnzymeLabelTemplateSampleDTO.SampleType.POSITIVE.getCode());
                vo.setSampleType(EnzymeLabelTemplateSampleDTO.SampleType.POSITIVE.getName());
                plateRawRecordResultVos.add(vo);
            }
        }

        // 空白
        if (Boolean.TRUE.equals(dto.getIsGenerateBlank())) {
            for (int i = 0; i < enzymeLabelReportItemVO.getGenerateBlankNum(); i++) {
                vo = new RawRecordResultVO.Result();
                vo.setTestDate(now);
                vo.setQcType("空");
                vo.setPatientName("空白");
                vo.setSampleTypeCode(EnzymeLabelTemplateSampleDTO.SampleType.BLANK.getCode());
                vo.setSampleType(EnzymeLabelTemplateSampleDTO.SampleType.BLANK.getName());
                plateRawRecordResultVos.add(vo);
            }
        }

        // 标准品
        if (Boolean.TRUE.equals(dto.getIsGenerateStandard())) {
            for (int i = 0; i < enzymeLabelReportItemVO.getGenerateStandardNum(); i++) {
                vo = new RawRecordResultVO.Result();
                vo.setTestDate(now);
                vo.setQcType("标");
                vo.setPatientName("标准品");
                vo.setSampleTypeCode(EnzymeLabelTemplateSampleDTO.SampleType.STANDARD.getCode());
                vo.setSampleType(EnzymeLabelTemplateSampleDTO.SampleType.STANDARD.getName());
                plateRawRecordResultVos.add(vo);
            }
        }

        //生成留样复测品
//        if (Boolean.TRUE.equals(dto.getIsReservedSample())) {
//            for (RawRecordResultVO.QcType value : RawRecordResultVO.QcType.values()) {
//                vo = new RawRecordResultVO.Result();
//                vo.setTestDate(now);
//                vo.setQcType(value.getSymbol());
//                vo.setPatientName("留样复测" + value.getSymbol());
//                vo.setSampleTypeCode(EnzymeLabelTemplateSampleDTO.SampleType.STANDARD.getCode());
//                vo.setSampleType(EnzymeLabelTemplateSampleDTO.SampleType.STANDARD.getName());
//                plateRawRecordResultVos.add(vo);
//            }
//        }
        return plateRawRecordResultVos;
    }

}
