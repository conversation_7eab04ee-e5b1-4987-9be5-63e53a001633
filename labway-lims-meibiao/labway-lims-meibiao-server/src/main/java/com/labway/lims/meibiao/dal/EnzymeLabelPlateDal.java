package com.labway.lims.meibiao.dal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.meibiao.mapper.EnzymeLabelPlateMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelPlate;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelPlateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.apache.tomcat.util.json.JSONParserConstants.EOF;

@Slf4j
@Component
@CacheConfig(cacheNames = "plateDal")
public class EnzymeLabelPlateDal {

    @Resource
    private EnzymeLabelPlateMapper enzymeLabelPlateMapper;

    /**
     * 根据时间查询
     * @param startDate <= 开始时间
     * @param endDate  >= 结束时间
     * @return EnzymeLabelPlateVO
     */
    @Cacheable(key = "'selectByStartAmdEndDate:'+ #startDate + #endDate")
    public List<EnzymeLabelPlateVO> selectByStartAmdEndDate(LocalDateTime startDate, LocalDateTime endDate) {
        if(startDate == null || endDate == null){
            log.warn("startDate is {}, endDate is {}", startDate, endDate);
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbEnzymeLabelPlate> wrapper = Wrappers.lambdaQuery(TbEnzymeLabelPlate.class)
                .select(TbEnzymeLabelPlate::getPlateId, TbEnzymeLabelPlate::getPlateCode,
                        TbEnzymeLabelPlate::getRemark, TbEnzymeLabelPlate::getPlateDate)
                .ge(TbEnzymeLabelPlate::getCreateDate, startDate)
                .le(TbEnzymeLabelPlate::getCreateDate, endDate.plusDays(1))
                .orderByDesc(TbEnzymeLabelPlate::getCreateDate);

        List<TbEnzymeLabelPlate> tbEnzymeLabelPlates = enzymeLabelPlateMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(tbEnzymeLabelPlates)) {
            return Collections.emptyList();
        }
        return JsonUtils.beanToArray(tbEnzymeLabelPlates, EnzymeLabelPlateVO.class);
    }


    /**
     * 新增酶标板
     * @param tbEnzymeLabelPlate
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean addPlate(TbEnzymeLabelPlate tbEnzymeLabelPlate) {
        if(ObjectUtils.isEmpty(tbEnzymeLabelPlate)){
            return Boolean.FALSE;
        }
        return enzymeLabelPlateMapper.insert(tbEnzymeLabelPlate) > EOF;
    }

    /**
     * 删除酶标板
     * @param plateId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean deleteByPlateId(long plateId) {
        return enzymeLabelPlateMapper.deleteById(plateId) > EOF;
    }

    /**
     * 查一天的所有板子
     * @param now
     * @return
     */
    @Cacheable(key = "'selectByPlateCodeAndNow:'+ #now")
    public List<TbEnzymeLabelPlate> selectByNow(LocalDate now) {
        LocalDateTime dateTime = now.atStartOfDay();
        LambdaQueryWrapper<TbEnzymeLabelPlate> wrapper = Wrappers.lambdaQuery(TbEnzymeLabelPlate.class)
                .ge(TbEnzymeLabelPlate::getCreateDate, dateTime)
                .le(TbEnzymeLabelPlate::getCreateDate, dateTime.plusDays(1));
        return enzymeLabelPlateMapper.selectList(wrapper);
    }
    /**
     * 根据plateCode查询
     * @param plateCode 板子编号
     * @return
     */
    @Cacheable(key = "'selectByPlateCode:'+ #plateCode")
    public TbEnzymeLabelPlate selectByPlateCode(String plateCode) {
        LambdaQueryWrapper<TbEnzymeLabelPlate> wrapper = Wrappers.lambdaQuery(TbEnzymeLabelPlate.class)
                .eq(TbEnzymeLabelPlate::getPlateCode, plateCode);
        return enzymeLabelPlateMapper.selectOne(wrapper);
    }

    /**
     * 根据id查一个
     * @param plateId
     * @return
     */
    @Cacheable(key = "'selectByPlateId:'+ #plateId")
    public TbEnzymeLabelPlate selectByPlateId(long plateId) {
        return enzymeLabelPlateMapper.selectById(plateId);
    }

    /**
     * 更新备注， 根据id
     * @param plateId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean updateRemakeByPlateId(long plateId, String remake){
        LambdaUpdateWrapper<TbEnzymeLabelPlate> wrapper = Wrappers.lambdaUpdate(TbEnzymeLabelPlate.class)
                .eq(TbEnzymeLabelPlate::getPlateId, plateId)
                .set(TbEnzymeLabelPlate::getRemark, remake);
        return enzymeLabelPlateMapper.update(null, wrapper) > EOF;
    }
}
