package com.labway.lims.meibiao.dal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.meibiao.dto.StateDataDTO;
import com.labway.lims.meibiao.mapper.EnzymeLabelLayoutMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelLayout;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelLayoutVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@CacheConfig(cacheNames = "labelLayoutDal")
public class EnzymeLabelLayoutDal {

    @Resource
    private EnzymeLabelLayoutMapper enzymeLabelLayoutMapper;
    @Resource
    @Lazy
    private EnzymeLabelLayoutDal enzymeLabelLayoutDal;


    /**
     * 新增酶标板布局
     *
     * @param tbEnzymeLabelLayout
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean insertLabelLayout(TbEnzymeLabelLayout tbEnzymeLabelLayout) {
        if (tbEnzymeLabelLayout == null) {
            return Boolean.FALSE;
        }
        return enzymeLabelLayoutMapper.insert(tbEnzymeLabelLayout) > StateDataDTO.INT_ZERO;
    }


    /**
     * 新增报告
     * @param tbEnzymeLabelLayout
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean insertOrUpdateLabelLayout(TbEnzymeLabelLayout tbEnzymeLabelLayout){
        if (tbEnzymeLabelLayout == null) {
            return Boolean.FALSE;
        }

        EnzymeLabelLayoutVO enzymeLabelLayoutVO = enzymeLabelLayoutDal.selectByPlateId(tbEnzymeLabelLayout.getPlateId());
        if(enzymeLabelLayoutVO == null){
            return insertLabelLayout(tbEnzymeLabelLayout);
        }

        LambdaUpdateWrapper<TbEnzymeLabelLayout> wrapper = Wrappers.lambdaUpdate(TbEnzymeLabelLayout.class)
                .eq(TbEnzymeLabelLayout::getPlateId, tbEnzymeLabelLayout.getPlateId())
                .set(TbEnzymeLabelLayout::getPlaceRule, tbEnzymeLabelLayout.getPlaceRule())
                .set(TbEnzymeLabelLayout::getPlaceRuleCode, tbEnzymeLabelLayout.getPlaceRuleCode())
                .set(TbEnzymeLabelLayout::getRefTemplateId, tbEnzymeLabelLayout.getRefTemplateId())
                .set(TbEnzymeLabelLayout::getRefTemplateName, tbEnzymeLabelLayout.getRefTemplateName())
                .set(TbEnzymeLabelLayout::getRemark, tbEnzymeLabelLayout.getRemark())
                .set(TbEnzymeLabelLayout::getSampleNoDate, tbEnzymeLabelLayout.getSampleNoDate())
                .set(TbEnzymeLabelLayout::getSampleStartNo, tbEnzymeLabelLayout.getSampleStartNo())
                .set(TbEnzymeLabelLayout::getUpdateDate, tbEnzymeLabelLayout.getUpdateDate())
                .set(TbEnzymeLabelLayout::getUpdaterId, tbEnzymeLabelLayout.getUpdaterId())
                .set(TbEnzymeLabelLayout::getUpdaterName, tbEnzymeLabelLayout.getUpdaterName())
                ;

        return enzymeLabelLayoutMapper.update(null, wrapper) > StateDataDTO.INT_ZERO;
    }

    /**
     * 查询板布局
     *
     * @param isTemplate 1是模板 0不是模板  null所有
     * @return
     */
    @Cacheable(key = "'selectAll' + #isTemplate")
    public List<EnzymeLabelLayoutVO> selectAll(Integer isTemplate) {
        LambdaQueryWrapper<TbEnzymeLabelLayout> wrapper = Wrappers.lambdaQuery(TbEnzymeLabelLayout.class)
                .eq(Objects.nonNull(isTemplate), TbEnzymeLabelLayout::getIsTemplate, isTemplate);
        List<TbEnzymeLabelLayout> tbEnzymeLabelLayouts = enzymeLabelLayoutMapper.selectList(wrapper);
        return JsonUtils.beanToArray(tbEnzymeLabelLayouts, EnzymeLabelLayoutVO.class);
    }

    /**
     * 根据id查一个
     *
     * @param layoutId 1是模板 0不是模板  null所有
     * @return
     */
    @Cacheable(key = "'selectByLayoutId' + #layoutId")
    public EnzymeLabelLayoutVO selectByLayoutId(Long layoutId) {
        if(layoutId == null) return null;
        TbEnzymeLabelLayout tbEnzymeLabelLayouts = enzymeLabelLayoutMapper.selectById(layoutId);
        return JsonUtils.beanToBean(tbEnzymeLabelLayouts, EnzymeLabelLayoutVO.class);
    }

    /**
     * 根据板子id查询布局id
     * @param plateId
     * @return
     */
    @Cacheable(key = "'selectByPlateId' + #plateId")
    public EnzymeLabelLayoutVO selectByPlateId(long plateId) {
        TbEnzymeLabelLayout tbEnzymeLabelLayout = enzymeLabelLayoutMapper
                .selectOne(Wrappers.lambdaQuery(TbEnzymeLabelLayout.class)
                        .eq(TbEnzymeLabelLayout::getPlateId, plateId)
                        .eq(TbEnzymeLabelLayout::getIsTemplate, 0)
                );
        return tbEnzymeLabelLayout == null ? null : JsonUtils.beanToBean(tbEnzymeLabelLayout, EnzymeLabelLayoutVO.class);
    }

    @Cacheable(key = "'selectByTemplateName' + #templateName")
    public EnzymeLabelLayoutVO selectByTemplateName(String templateName) {
        TbEnzymeLabelLayout tbEnzymeLabelLayout = enzymeLabelLayoutMapper
                .selectOne(Wrappers.lambdaQuery(TbEnzymeLabelLayout.class)
                        .eq(TbEnzymeLabelLayout::getTemplateName, templateName)
                );
        return tbEnzymeLabelLayout == null ? null :JsonUtils.beanToBean(tbEnzymeLabelLayout, EnzymeLabelLayoutVO.class);
    }
}
