package com.labway.lims.meibiao.dal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.meibiao.mapper.EnzymeLabelCalcFormulaMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelCalcFormula;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelCalcFormulaVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static org.apache.tomcat.util.json.JSONParserConstants.EOF;

/**
 * <AUTHOR>
 * @description 酶标计算公式
 * @date 2023-11-22
 */
@CacheConfig(cacheNames = "calcFormulaDal")
@Component
public class EnzymeLabelCalcFormulaDal {

    @Resource
    private EnzymeLabelCalcFormulaMapper enzymeLabelCalcFormulaMapper;

    /**
     * 查所有  按照顺序从小到大排列
     *
     * @return
     */
    @Cacheable(key = "'checkFormulaList'")
    public List<EnzymeLabelCalcFormulaVO> selectAllOrderBySortASC() {
        List<TbEnzymeLabelCalcFormula> list = enzymeLabelCalcFormulaMapper.selectList(Wrappers.lambdaQuery(TbEnzymeLabelCalcFormula.class)
                .orderByAsc(TbEnzymeLabelCalcFormula::getSort, TbEnzymeLabelCalcFormula::getCreateDate)
        );
        return JsonUtils.beanToArray(list, EnzymeLabelCalcFormulaVO.class);
    }

    /**
     * 判断库中是否有对应的值
     *
     * @param formulaCode       计算公式Code
     */
    @Cacheable(key = "'selectRepetition:'+ #formulaCode ")
    public boolean selectRepetition(String formulaCode) {
        LambdaQueryWrapper<TbEnzymeLabelCalcFormula> wrapper = Wrappers.lambdaQuery(TbEnzymeLabelCalcFormula.class)
                .eq(TbEnzymeLabelCalcFormula::getFormulaCode, formulaCode);
        return enzymeLabelCalcFormulaMapper.selectCount(wrapper) > EOF;
    }

    /**
     * 新增计算公式
     *
     * @param tbEnzymeLabelCalcFormula
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean insertCheckFormula(TbEnzymeLabelCalcFormula tbEnzymeLabelCalcFormula) {
        if (ObjectUtils.isEmpty(tbEnzymeLabelCalcFormula)) {
            return Boolean.FALSE;
        }
        return enzymeLabelCalcFormulaMapper.insert(tbEnzymeLabelCalcFormula) > EOF;
    }

    /**
     * 修改计算公式
     *
     * @param tbEnzymeLabelCalcFormula
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean updateCheckFormulaByFormulaId(TbEnzymeLabelCalcFormula tbEnzymeLabelCalcFormula) {
        if (ObjectUtils.isEmpty(tbEnzymeLabelCalcFormula) || null == tbEnzymeLabelCalcFormula.getFormulaId()) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<TbEnzymeLabelCalcFormula> wrapper = Wrappers.lambdaUpdate(TbEnzymeLabelCalcFormula.class)

                .eq(TbEnzymeLabelCalcFormula::getFormulaId, tbEnzymeLabelCalcFormula.getFormulaId())

                .set(TbEnzymeLabelCalcFormula::getCalcFormula, tbEnzymeLabelCalcFormula.getCalcFormula())
                .set(TbEnzymeLabelCalcFormula::getFormulaShort, tbEnzymeLabelCalcFormula.getFormulaShort())
                .set(TbEnzymeLabelCalcFormula::getCutoffCalcFormula, tbEnzymeLabelCalcFormula.getCutoffCalcFormula())
                .set(TbEnzymeLabelCalcFormula::getSort, tbEnzymeLabelCalcFormula.getSort())
                .set(TbEnzymeLabelCalcFormula::getFormulaDesc, tbEnzymeLabelCalcFormula.getFormulaDesc())

                .set(TbEnzymeLabelCalcFormula::getUpdaterId, tbEnzymeLabelCalcFormula.getUpdaterId())
                .set(TbEnzymeLabelCalcFormula::getUpdaterName, tbEnzymeLabelCalcFormula.getUpdaterName())
                .set(TbEnzymeLabelCalcFormula::getUpdateDate, tbEnzymeLabelCalcFormula.getUpdateDate());

        return enzymeLabelCalcFormulaMapper.update(null, wrapper) > EOF;
    }


    /**
     * 逻辑删除计算公式
     *
     * @param formulaId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean deleteByFormulaId(long formulaId) {
        return enzymeLabelCalcFormulaMapper.delete(
                Wrappers.lambdaQuery(TbEnzymeLabelCalcFormula.class)
                        .eq(TbEnzymeLabelCalcFormula::getFormulaId, formulaId)
        ) > EOF;
    }

    /**
     * 根据id获取一个
     *
     * @return EnzymeLabelCalcFormulaVO
     */
    @Transactional(rollbackFor = Exception.class)
    @Cacheable(key = "'selectByFOrmulaId:' + #formulaId")
    public EnzymeLabelCalcFormulaVO selectByFormulaId(long formulaId) {
        LambdaQueryWrapper<TbEnzymeLabelCalcFormula> wrapper = Wrappers.lambdaQuery(TbEnzymeLabelCalcFormula.class)
                .eq(TbEnzymeLabelCalcFormula::getFormulaId, formulaId);
        return JsonUtils.beanToBean(enzymeLabelCalcFormulaMapper.selectOne(wrapper), EnzymeLabelCalcFormulaVO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Cacheable(key = "'selectByFormulaIds:' + #formulaIds")
    public List<EnzymeLabelCalcFormulaVO> selectByFormulaIds(List<Long> formulaIds) {
        if(CollectionUtils.isEmpty(formulaIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbEnzymeLabelCalcFormula> wrapper = Wrappers.lambdaQuery(TbEnzymeLabelCalcFormula.class)
                .in(TbEnzymeLabelCalcFormula::getFormulaId, formulaIds);
        return JsonUtils.beanToArray(enzymeLabelCalcFormulaMapper.selectList(wrapper), EnzymeLabelCalcFormulaVO.class);
    }
}
