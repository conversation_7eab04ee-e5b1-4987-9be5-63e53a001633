package com.labway.lims.meibiao.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.meibiao.emun.VariableReplaceEnum;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 阴阳变量替换
 * @date 2023-11-22
 */
@RequestMapping("/replace")
@RestController
public class VariableReplaceController extends BaseController {

    @GetMapping("/select")
    public Object selectVariableReplace(){
        return VariableReplaceEnum.selectValues();
    }
}
