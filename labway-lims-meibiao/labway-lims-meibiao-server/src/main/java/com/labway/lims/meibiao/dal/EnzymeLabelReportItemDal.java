package com.labway.lims.meibiao.dal;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.enums.base.InstrumentItemTypeEnum;
import com.labway.lims.meibiao.dto.EnzymeLabelReportItemDTO;
import com.labway.lims.meibiao.dto.StateDataDTO;
import com.labway.lims.meibiao.mapper.EnzymeLabelReportItemMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelCalcFormula;
import com.labway.lims.meibiao.model.TbEnzymeLabelReportItem;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelReportItemVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.apache.tomcat.util.json.JSONParserConstants.EOF;

@Component
@CacheConfig(cacheNames = "reportItemDal")
public class EnzymeLabelReportItemDal {
    @Resource
    private EnzymeLabelReportItemMapper enzymeLabelReportItemMapper;


    @Cacheable(key = "'selectList'")
    public List<EnzymeLabelReportItemVO> selectList() {

        LambdaQueryWrapper<TbEnzymeLabelReportItem> wrapper = Wrappers.lambdaQuery(TbEnzymeLabelReportItem.class)
                .orderByDesc(TbEnzymeLabelReportItem::getCreateDate);

        List<TbEnzymeLabelReportItem> tbEnzymeLabelReportItems = enzymeLabelReportItemMapper.selectList(wrapper);

        return tbEnzymeLabelReportItems.stream().map( tbEnzymeLabelReportItem -> {
            EnzymeLabelReportItemVO vo = new EnzymeLabelReportItemVO();
            BeanUtils.copyProperties(tbEnzymeLabelReportItem, vo);
            vo.setRationSetting(JsonUtils.jsonToArray(tbEnzymeLabelReportItem.getRationSetting(), EnzymeLabelReportItemDTO.RationSetting.class));
            vo.setRationResult(JsonUtils.jsonToBean(tbEnzymeLabelReportItem.getRationResult(), EnzymeLabelReportItemDTO.RationResult.class));

            vo.setReagentValidDate(StateDataDTO.NOW_DATE.equals(vo.getReagentValidDate())
                    ? null : vo.getReagentValidDate()
            );

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 新增酶标报告项目
     * @param tbEnzymeLabelReportItem
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean insertEnzymeLabelReportItem(TbEnzymeLabelReportItem tbEnzymeLabelReportItem) {
        if(ObjectUtils.isEmpty(tbEnzymeLabelReportItem)){
            return Boolean.FALSE;
        }
        // 定性判断计算公式
        if (tbEnzymeLabelReportItem.getItemTypeCode().equals(InstrumentItemTypeEnum.QUALITATIVE.getCode())){
            tbEnzymeLabelReportItem.setRationSetting("");
            tbEnzymeLabelReportItem.setRationResult("");
        }

        // 定量判断
        if (tbEnzymeLabelReportItem.getItemTypeCode().equals(InstrumentItemTypeEnum.QUANTITATIVE.getCode())) {
            tbEnzymeLabelReportItem.setFormulaId(0L);
        }
        return enzymeLabelReportItemMapper.insert(tbEnzymeLabelReportItem) > EOF;
    }

    /**
     * 修改酶标报告项目
     * @param tbEnzymeLabelReportItem 酶标仪i类
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean updateEnzymeLabelReportItem(TbEnzymeLabelReportItem tbEnzymeLabelReportItem) {
        if(ObjectUtils.isEmpty(tbEnzymeLabelReportItem) || tbEnzymeLabelReportItem.getLabelReportId() == null){
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<TbEnzymeLabelReportItem> wrapper = Wrappers.lambdaUpdate(TbEnzymeLabelReportItem.class)
                .eq(TbEnzymeLabelReportItem::getLabelReportId, tbEnzymeLabelReportItem.getLabelReportId())

                .set(TbEnzymeLabelReportItem::getInstrumentId, tbEnzymeLabelReportItem.getInstrumentId())
                .set(TbEnzymeLabelReportItem::getReportItemId, tbEnzymeLabelReportItem.getReportItemId())
                .set(TbEnzymeLabelReportItem::getItemTypeCode, tbEnzymeLabelReportItem.getItemTypeCode())
                .set(TbEnzymeLabelReportItem::getItemTypeName, tbEnzymeLabelReportItem.getItemTypeName())
                .set(TbEnzymeLabelReportItem::getIsGenerateQc, tbEnzymeLabelReportItem.getIsGenerateQc())

                .set(TbEnzymeLabelReportItem::getIsNegativeContrast, tbEnzymeLabelReportItem.getIsNegativeContrast())
                .set(TbEnzymeLabelReportItem::getNegativeContrastNum, tbEnzymeLabelReportItem.getNegativeContrastNum())

                .set(TbEnzymeLabelReportItem::getIsPositiveContrast, tbEnzymeLabelReportItem.getIsPositiveContrast())
                .set(TbEnzymeLabelReportItem::getPositiveContrastNum, tbEnzymeLabelReportItem.getPositiveContrastNum())

                .set(TbEnzymeLabelReportItem::getIsGenerateBlank, tbEnzymeLabelReportItem.getIsGenerateBlank())
                .set(TbEnzymeLabelReportItem::getGenerateBlankNum, tbEnzymeLabelReportItem.getGenerateBlankNum())

                .set(TbEnzymeLabelReportItem::getIsGenerateStandard, tbEnzymeLabelReportItem.getIsGenerateStandard())
                .set(TbEnzymeLabelReportItem::getGenerateStandardNum, tbEnzymeLabelReportItem.getGenerateStandardNum())

                .set(TbEnzymeLabelReportItem::getIsReservedSample, tbEnzymeLabelReportItem.getIsReservedSample())

                .set(TbEnzymeLabelReportItem::getProjectColor, tbEnzymeLabelReportItem.getProjectColor())
                .set(TbEnzymeLabelReportItem::getTestWaveLength, tbEnzymeLabelReportItem.getTestWaveLength())
                .set(TbEnzymeLabelReportItem::getReferenceWaveLength, tbEnzymeLabelReportItem.getReferenceWaveLength())
                .set(TbEnzymeLabelReportItem::getTestMethod, tbEnzymeLabelReportItem.getTestMethod())
                .set(TbEnzymeLabelReportItem::getReagentBatch, tbEnzymeLabelReportItem.getReagentBatch())
                .set(TbEnzymeLabelReportItem::getReagentValidDate, tbEnzymeLabelReportItem.getReagentValidDate())
                .set(TbEnzymeLabelReportItem::getReagentManufacturer, tbEnzymeLabelReportItem.getReagentManufacturer())
                .set(TbEnzymeLabelReportItem::getGrayZoneMinFormula, tbEnzymeLabelReportItem.getGrayZoneMinFormula())
                .set(TbEnzymeLabelReportItem::getGrayZoneMaxFormula, tbEnzymeLabelReportItem.getGrayZoneMaxFormula())
                ;

        // 定性判断计算公式
        if (tbEnzymeLabelReportItem.getItemTypeCode().equals(InstrumentItemTypeEnum.QUALITATIVE.getCode())){
            wrapper.set(TbEnzymeLabelReportItem::getFormulaId, tbEnzymeLabelReportItem.getFormulaId());
            wrapper.set(TbEnzymeLabelReportItem::getRationSetting, "");
            wrapper.set(TbEnzymeLabelReportItem::getRationResult, "");
        }

        // 定量判断
        if (tbEnzymeLabelReportItem.getItemTypeCode().equals(InstrumentItemTypeEnum.QUANTITATIVE.getCode())) {
            wrapper.set(TbEnzymeLabelReportItem::getFormulaId, 0);
            wrapper.set(TbEnzymeLabelReportItem::getRationSetting, tbEnzymeLabelReportItem.getRationSetting());
            wrapper.set(TbEnzymeLabelReportItem::getRationResult, tbEnzymeLabelReportItem.getRationResult());

        }

        return enzymeLabelReportItemMapper.update(null,wrapper) > EOF;
    }

    /**
     * 修改酶标报告项目
     * @param labelReportItemId 报告项目id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public boolean deleteEnzymeLabelReportItem(long labelReportItemId) {
        if(ObjectUtils.isEmpty(labelReportItemId)){
            return Boolean.FALSE;
        }
        return enzymeLabelReportItemMapper.delete(
                Wrappers.lambdaQuery(TbEnzymeLabelReportItem.class)
                        .eq(TbEnzymeLabelReportItem::getLabelReportId, labelReportItemId)
        ) > EOF;
    }

    @Cacheable(key = "'selectByLabelReportIds:'+ #labelReportIds")
    public List<EnzymeLabelReportItemVO> selectByLabelReportIds(Collection<Long> labelReportIds) {
        if(CollectionUtils.isEmpty(labelReportIds)){
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbEnzymeLabelReportItem> wrapper = Wrappers.lambdaQuery(TbEnzymeLabelReportItem.class)
                .in(TbEnzymeLabelReportItem::getLabelReportId, labelReportIds)
                .orderByDesc(TbEnzymeLabelReportItem::getCreateDate);

        List<TbEnzymeLabelReportItem> tbEnzymeLabelReportItems = enzymeLabelReportItemMapper.selectList(wrapper);

        return tbEnzymeLabelReportItems.stream().map( tbEnzymeLabelReportItem -> {
            EnzymeLabelReportItemVO vo = new EnzymeLabelReportItemVO();
            BeanUtils.copyProperties(tbEnzymeLabelReportItem, vo);
            vo.setRationSetting(JsonUtils.jsonToArray(tbEnzymeLabelReportItem.getRationSetting(), EnzymeLabelReportItemDTO.RationSetting.class));
            vo.setRationResult(JsonUtils.jsonToBean(tbEnzymeLabelReportItem.getRationResult(), EnzymeLabelReportItemDTO.RationResult.class));

            vo.setReagentValidDate(StateDataDTO.NOW_DATE.equals(vo.getReagentValidDate())
                    ? null : vo.getReagentValidDate()
            );

            return vo;
        }).collect(Collectors.toList());
    }

    @Cacheable(key = "'selectByLabelReportId:'+ #labelReportId")
    public EnzymeLabelReportItemVO selectByLabelReportId(long labelReportId) {
        TbEnzymeLabelReportItem tbEnzymeLabelReportItem = enzymeLabelReportItemMapper.selectById(labelReportId);

        if (tbEnzymeLabelReportItem == null) return null;

        EnzymeLabelReportItemVO vo = new EnzymeLabelReportItemVO();
        BeanUtils.copyProperties(tbEnzymeLabelReportItem, vo);
        vo.setRationSetting(JsonUtils.jsonToArray(tbEnzymeLabelReportItem.getRationSetting(), EnzymeLabelReportItemDTO.RationSetting.class));
        vo.setRationResult(JsonUtils.jsonToBean(tbEnzymeLabelReportItem.getRationResult(), EnzymeLabelReportItemDTO.RationResult.class));

        vo.setReagentValidDate(StateDataDTO.NOW_DATE.equals(vo.getReagentValidDate())
                ? null : vo.getReagentValidDate());
        return vo;

    }
}
