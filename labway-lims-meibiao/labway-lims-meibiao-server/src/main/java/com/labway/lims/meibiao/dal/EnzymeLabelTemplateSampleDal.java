package com.labway.lims.meibiao.dal;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.meibiao.mapper.EnzymeLabelTemplateSampleMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelTemplateSample;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelTemplateSampleVO;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@CacheConfig(cacheNames = "templateSampleDal")
public class EnzymeLabelTemplateSampleDal {

    @Resource
    private EnzymeLabelTemplateSampleMapper enzymeLabelTemplateSampleMapper;


    @Cacheable(key = "'selectByLayoutId' + #layoutId")
    public List<EnzymeLabelTemplateSampleVO> selectByLayoutId(long layoutId) {
        List<TbEnzymeLabelTemplateSample> tbEnzymeLabelTemplateSamples = enzymeLabelTemplateSampleMapper.selectList(
                Wrappers.lambdaQuery(TbEnzymeLabelTemplateSample.class)
                        .eq(TbEnzymeLabelTemplateSample::getLayoutId, layoutId)
        );
        return JsonUtils.beanToArray(tbEnzymeLabelTemplateSamples, EnzymeLabelTemplateSampleVO.class);
    }

    @CacheEvict(allEntries = true)
    public void clearCache() {
        // TODO 清空缓存用
    }
}
