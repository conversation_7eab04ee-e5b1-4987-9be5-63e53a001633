package com.labway.lims.meibiao.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.meibiao.dal.CalcFormulaDeterminationDal;
import com.labway.lims.meibiao.dal.EnzymeLabelCalcFormulaDal;
import com.labway.lims.meibiao.dto.CalcFormulaDeterminationDTO;
import com.labway.lims.meibiao.mapper.CalcFormulaDeterminationMapper;
import com.labway.lims.meibiao.model.TbCalcFormulaDetermination;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.CalcFormulaDeterminationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class CalcFormulaDeterminationServiceImpl extends ServiceImpl<CalcFormulaDeterminationMapper, TbCalcFormulaDetermination>
        implements ICalcFormulaDeterminationService {

    @Resource
    private CalcFormulaDeterminationDal calcFormulaDeterminationDal;

    @Resource
    private EnzymeLabelCalcFormulaDal enzymeLabelCalcFormulaDal;


    @Override
    public List<CalcFormulaDeterminationVO> selectAllByFormulaId(long formulaId) {
        return calcFormulaDeterminationDal.selectByFormulaIdOrderBySort(formulaId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addDetermination(CalcFormulaDeterminationDTO dto) {

        LoginUserHandler.User user = LoginUserHandler.get();
        LocalDateTime now = LocalDateTime.now();

        log.info("新增定性描述 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);

        if (ObjectUtils.isEmpty(enzymeLabelCalcFormulaDal.selectByFormulaId(dto.getFormulaId()))) {
            throw new IllegalArgumentException("当前专业组下计算公式Id不存在");
        }

        TbCalcFormulaDetermination tbCalcFormulaDetermination = JsonUtils.beanToBean(dto, TbCalcFormulaDetermination.class);

        tbCalcFormulaDetermination.setDeterminationId(null);
        tbCalcFormulaDetermination.setBrief(StringUtils.defaultString(tbCalcFormulaDetermination.getBrief()));
        tbCalcFormulaDetermination.setIsDelete(0);

        tbCalcFormulaDetermination.setCreateDate(now);
        tbCalcFormulaDetermination.setUpdateDate(now);

        tbCalcFormulaDetermination.setCreatorId(user.getUserId());
        tbCalcFormulaDetermination.setCreatorName(user.getNickname());

        tbCalcFormulaDetermination.setUpdaterId(user.getUserId());
        tbCalcFormulaDetermination.setUpdaterName(user.getNickname());

        if (!calcFormulaDeterminationDal.insertDetermination(tbCalcFormulaDetermination)) {
            log.error("新增定性描述失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);
            throw new IllegalArgumentException("新增定性描述失败");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateDetermination(CalcFormulaDeterminationDTO dto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        LocalDateTime now = LocalDateTime.now();
        log.info("修改定性描述 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);

        TbCalcFormulaDetermination tbCalcFormulaDetermination = JsonUtils.beanToBean(dto, TbCalcFormulaDetermination.class);

        tbCalcFormulaDetermination.setBrief(StringUtils.defaultString(tbCalcFormulaDetermination.getBrief()));

        tbCalcFormulaDetermination.setUpdateDate(now);
        tbCalcFormulaDetermination.setUpdaterId(user.getUserId());
        tbCalcFormulaDetermination.setUpdaterName(user.getNickname());

        if (!calcFormulaDeterminationDal.updateDetermination(tbCalcFormulaDetermination)) {

            log.error("修改定性描述失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);

            throw new IllegalArgumentException("修改定性描述失败");
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteByDeterminationId(long determinationId) {
        LoginUserHandler.User user = LoginUserHandler.get();
        log.info("删除定性描述 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), determinationId);

        if (!calcFormulaDeterminationDal.deleteByDeterminationId(determinationId)) {

            log.error("删除定性描述失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), determinationId);

            throw new IllegalArgumentException("删除定性描述失败");
        }

        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CalcFormulaDeterminationVO> selectByFormulaIds(Collection<Long> formulaIds){
        return calcFormulaDeterminationDal.selectByFormulaIds(formulaIds);
    }

}
