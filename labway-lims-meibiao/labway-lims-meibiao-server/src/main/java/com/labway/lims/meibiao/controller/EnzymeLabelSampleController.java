package com.labway.lims.meibiao.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.meibiao.dto.DeletePlateSampleDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelTemplateSampleDTO;
import com.labway.lims.meibiao.dto.ExportSampleResultExcelDTO;
import com.labway.lims.meibiao.dto.InstrumentResultDTO;
import com.labway.lims.meibiao.dto.TemplateSampleDTO;
import com.labway.lims.meibiao.dto.UpdateOriginalODValueDTO;
import com.labway.lims.meibiao.dto.UpdateSampleNoDTO;
import com.labway.lims.meibiao.service.IEnzymeLabelPlateService;
import com.labway.lims.meibiao.service.IEnzymeLabelReportItemService;
import com.labway.lims.meibiao.service.IEnzymeLabelSampleService;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelPlateVO;
import com.labway.lims.meibiao.vo.EnzymeLabelReportItemVO;
import com.labway.lims.meibiao.vo.EnzymeLabelSampleVO;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.script.ScriptException;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * @description 酶标板样本
 * <AUTHOR>
 * @date 2023-11-27
 */
@RequestMapping("/sample")
@RestController
public class EnzymeLabelSampleController extends BaseController {

    @Resource
    private IEnzymeLabelSampleService iEnzymeLabelSampleService;
    @Resource
    private IEnzymeLabelReportItemService iEnzymeLabelReportItemService;
    @Resource
    private IEnzymeLabelPlateService iEnzymeLabelPlateService;

    @DubboReference
    private PdfReportService pdfReportService;


    @GetMapping("/select-by-plateId")
    public Object selectByPlateId(@RequestParam Long plateId, @RequestParam Boolean isSubtractBlank) throws ScriptException {
        Assert.notNull(plateId, "参数不能为空");
        return iEnzymeLabelSampleService.selectByPlateId(plateId, isSubtractBlank).getEnzymeLabelTemplateSample();
    }

    @PostMapping("/update-original-odValue")
    public Object updateOriginalODValue(@RequestBody UpdateOriginalODValueDTO dto) {
        dto.verifyParams();
        return iEnzymeLabelSampleService.updateOriginalODValue(dto.getPlateSampleId(), dto.getOriginalODValue());
    }

    @PostMapping("/save-instrument-result")
    public Object saveInstrumentResult(@RequestBody InstrumentResultDTO dto) {
        return iEnzymeLabelSampleService.saveInstrumentResult(dto);
    }

    @GetMapping("/send-report")
    public Object sendReport(@RequestParam Long plateId, Boolean isSubtractBlank) throws ScriptException {
        Assert.notNull(plateId, "参数不能为空");
        return iEnzymeLabelSampleService.sendReport(plateId, isSubtractBlank);
    }

    @PostMapping("/update-sampleno")
    public Object updateSampleNo(@RequestBody UpdateSampleNoDTO dto) {
        return iEnzymeLabelSampleService.updateSampleNo(dto);
    }

    @GetMapping("/export/excel")
    public void exportExcel(@RequestParam Long plateId, Boolean isSubtractBlank, HttpServletResponse response) throws ScriptException, IOException {
        Assert.notNull(plateId, "参数不能为空");

        // 板子信息
        TemplateSampleDTO templateSampleDTO = iEnzymeLabelSampleService.selectByPlateId(plateId, isSubtractBlank);

        // 报告信息 上
        ExportSampleResultExcelDTO exportSampleResultExcelDTO = fillTitle(plateId, templateSampleDTO, "\n");

        // 结果  下
        List<Map<String, String>> result = fillResult(templateSampleDTO, "\n");

        try (InputStream template = ResourceUtil.getStream("classpath:file/酶标仪原始结果.xlsx")) {
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(template)
                    .excelType(ExcelTypeEnum.XLSX)
                    .build();

            WriteSheet sheet1 = EasyExcel.writerSheet(0).build();
            // 要遍历的表格数据
            excelWriter.fill(BeanUtil.beanToMap(exportSampleResultExcelDTO), sheet1);
            excelWriter.fill(new FillWrapper("rows", result), sheet1);
            excelWriter.finish();
        }
    }

    @GetMapping("/print")
    public Object printResult(@RequestParam Long plateId, Boolean isSubtractBlank) throws ScriptException {
        Assert.notNull(plateId, "参数不能为空");

        // 板子信息
        TemplateSampleDTO templateSampleDTO = iEnzymeLabelSampleService.selectByPlateId(plateId, isSubtractBlank);

        // 报告信息 上
        ExportSampleResultExcelDTO exportSampleResultExcelDTO = fillTitle(plateId, templateSampleDTO, "|");

        // 结果  下
        List<Map<String, String>> result = fillResult(templateSampleDTO, "|");

        PdfReportParamDto paramDto = JsonUtils.beanToBean(exportSampleResultExcelDTO, PdfReportParamDto.class);
        paramDto.put("rows", result);

        String url = pdfReportService.build2Url(PdfTemplateTypeEnum.ORIGINAL_RESULT.getCode(), paramDto, 1);

        return Map.of("url", url);
    }

    private List<Map<String, String>> fillResult(TemplateSampleDTO templateSampleDTO, String lineFeed) {
        List<Map<String, Object>> enzymeLabelTemplateSamples = templateSampleDTO.getEnzymeLabelTemplateSample();
        List<Map<String, String>> result = new ArrayList<>(enzymeLabelTemplateSamples.size());
        for (Map<String, Object> enzymeLabelTemplateSample : enzymeLabelTemplateSamples) {
            Set<Map.Entry<String, Object>> entries = enzymeLabelTemplateSample.entrySet();
            Map<String, String> map = new HashMap<>();
            for (Map.Entry<String, Object> entry : entries) {
                if ("id".equals(entry.getKey())) {
                    map.put(entry.getKey(), String.valueOf(entry.getValue()));
                    continue;
                }
                EnzymeLabelSampleVO bean = BeanUtil.toBean(entry.getValue(), EnzymeLabelSampleVO.class);
                if (Objects.isNull(bean) || Objects.isNull(bean.getOriginalODValue())) {
                    map.put(entry.getKey(), "");
                    continue;
                }
                StringJoiner stringJoiner = new StringJoiner(lineFeed);
                stringJoiner.add(bean.getLabelReportEnName());
                if (Objects.nonNull(bean.getSampleNo())) {
                    stringJoiner.add(String.valueOf(bean.getSampleNo()));
                } else {
                    stringJoiner.add(bean.getSampleTypeName());
                }
                stringJoiner.add(bean.getOriginalODValue().toString())
                        .add(bean.getODValue().toString());
                if (Objects.nonNull(bean.getScoValue())) {
                    stringJoiner.add(String.valueOf(bean.getScoValue()));
                }
                if (Objects.nonNull(bean.getCutoffValue())) {
                    stringJoiner.add(String.valueOf(bean.getCutoffValue()));
                }
                if (Objects.nonNull(bean.getResultValue())) {
                    stringJoiner.add(bean.getResultValue());
                }
                map.put(entry.getKey(), stringJoiner.toString());
            }
            result.add(map);
        }
        return result;
    }

    private ExportSampleResultExcelDTO fillTitle(Long plateId, TemplateSampleDTO templateSampleDTO, String lineFeed) {
        EnzymeLabelPlateVO enzymeLabelPlateVO = iEnzymeLabelPlateService.selectById(plateId);

        List<Long> labelReportIds = templateSampleDTO.getData().stream().map(EnzymeLabelTemplateSampleDTO::getLabelReportId).collect(Collectors.toList());
        List<EnzymeLabelReportItemVO> vos = iEnzymeLabelReportItemService.selectByLabelReportIds(labelReportIds);

        return new ExportSampleResultExcelDTO(vos, enzymeLabelPlateVO, templateSampleDTO.getData(), lineFeed);
    }


    @PostMapping("/delete-plate-sample")
    public Object deletePlateSample(@RequestBody DeletePlateSampleDTO dto) {
        List<Long> plateSampleIds = dto.getPlateSampleIds();
        if (CollectionUtils.isEmpty(plateSampleIds)) {
            return true;
        }
        return iEnzymeLabelSampleService.deleteByPlateSampleId(plateSampleIds);
    }


}
