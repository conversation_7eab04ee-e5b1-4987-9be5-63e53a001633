package com.labway.lims.meibiao.service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.meibiao.dal.EnzymeLabelLayoutDal;
import com.labway.lims.meibiao.dal.EnzymeLabelReportItemDal;
import com.labway.lims.meibiao.dal.EnzymeLabelTemplateSampleDal;
import com.labway.lims.meibiao.dto.EnzymeLabelReportItemDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelTemplateSampleDTO;
import com.labway.lims.meibiao.dto.TemplateSampleDTO;
import com.labway.lims.meibiao.mapper.EnzymeLabelTemplateSampleMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelTemplateSample;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelLayoutVO;
import com.labway.lims.meibiao.vo.EnzymeLabelReportItemVO;
import com.labway.lims.meibiao.vo.EnzymeLabelSampleVO;
import com.labway.lims.meibiao.vo.EnzymeLabelTemplateSampleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class EnzymeLabelTemplateSampleServiceImpl extends ServiceImpl<EnzymeLabelTemplateSampleMapper, TbEnzymeLabelTemplateSample>
        implements IEnzymeLabelTemplateSampleService {

    @Resource
    private EnzymeLabelTemplateSampleDal enzymeLabelTemplateSampleDal;

    @Resource
    private EnzymeLabelLayoutDal enzymeLabelLayoutDal;

    @Resource
    private EnzymeLabelReportItemDal enzymeLabelReportItemDal;

    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;



    @Override
    public List<Map<String, Object>> selectByLayoutId(long layoutId) {
        List<EnzymeLabelTemplateSampleVO> enzymeLabelTemplateSampleVOS = enzymeLabelTemplateSampleDal.selectByLayoutId(layoutId);

        // 查看酶标报告项目
        List<Long> labelReportIds = enzymeLabelTemplateSampleVOS.stream().map(EnzymeLabelTemplateSampleDTO::getLabelReportId).collect(Collectors.toList());
        List<EnzymeLabelReportItemVO> enzymeLabelReportItemVOS = enzymeLabelReportItemDal.selectByLabelReportIds(labelReportIds);
        // 酶标项目id 和 仪器报告项目id
        Map<Long, Long> labelReportIdAndReportIdMap = enzymeLabelReportItemVOS.stream().collect(Collectors.toMap(EnzymeLabelReportItemDTO::getLabelReportId, EnzymeLabelReportItemDTO::getReportItemId));

        // 查看仪器报告项目
        List<Long> reportItemIds = enzymeLabelReportItemVOS.stream().map(EnzymeLabelReportItemDTO::getReportItemId).distinct().collect(Collectors.toList());
        List<InstrumentReportItemDto> instrumentReportItemDtos = instrumentReportItemService.selectByInstrumentReportItemIds(reportItemIds);
        Map<Long, InstrumentReportItemDto> reportItemDtoMap = instrumentReportItemDtos.stream().collect(Collectors.toMap(InstrumentReportItemDto::getInstrumentReportItemId, Function.identity()));

        for (EnzymeLabelTemplateSampleVO vo : enzymeLabelTemplateSampleVOS) {
            Long reportId = labelReportIdAndReportIdMap.get(vo.getLabelReportId());
            InstrumentReportItemDto instrumentReportItemDto = reportItemDtoMap.get(reportId);

            if(instrumentReportItemDto != null) {
                vo.setReportItemCode(instrumentReportItemDto.getReportItemCode());
                vo.setLabelReportName(instrumentReportItemDto.getReportItemName());
                vo.setLabelReportEnName(instrumentReportItemDto.getEnAb());
            }
        }

        EnzymeLabelLayoutVO enzymeLabelLayoutVO = enzymeLabelLayoutDal.selectByLayoutId(layoutId);
        if(enzymeLabelLayoutVO == null) enzymeLabelLayoutVO = new EnzymeLabelLayoutVO();
        TemplateSampleDTO templateSampleDTO = new TemplateSampleDTO();
        List<EnzymeLabelSampleVO> enzymeLabelSampleVOS = JsonUtils.beanToArray(enzymeLabelTemplateSampleVOS, EnzymeLabelSampleVO.class);
        templateSampleDTO.setData(enzymeLabelSampleVOS, enzymeLabelLayoutVO);
        return templateSampleDTO.getEnzymeLabelTemplateSample();
    }

    /**
     * 新增模板排版
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertBatchTemplateSample(List<EnzymeLabelTemplateSampleDTO> tbEnzymeLabelTemplateSamplesDTOs) {
        if(CollectionUtils.isEmpty(tbEnzymeLabelTemplateSamplesDTOs)){
            return true;
        }
        List<TbEnzymeLabelTemplateSample> tbEnzymeLabelTemplateSamples = fillTemplateSampleData(tbEnzymeLabelTemplateSamplesDTOs);
        boolean result = this.saveBatch(tbEnzymeLabelTemplateSamples);
        // 新增或修改成功后清空缓存
        if(result) enzymeLabelTemplateSampleDal.clearCache();
        return result;
    }

    @Override
    public EnzymeLabelLayoutVO selectByTemplateName(String templateName) {
        return enzymeLabelLayoutDal.selectByTemplateName(templateName);
    }


    private List<TbEnzymeLabelTemplateSample> fillTemplateSampleData(List<EnzymeLabelTemplateSampleDTO> enzymeLabelTemplateSampleDTOS) {
        LoginUserHandler.User user = LoginUserHandler.get();
        LocalDateTime now = LocalDateTime.now();
        return enzymeLabelTemplateSampleDTOS.stream()
                .map(dto -> TbEnzymeLabelTemplateSample.builder()
                        .plateSampleId(null)
                        .layoutId(dto.getLayoutId())
                        .col(dto.getCol())
                        .row(dto.getRow())
                        .labelReportId(dto.getLabelReportId())
                        .sampleTypeCode(dto.getSampleTypeCode())
                        .sampleType(dto.getSampleType())
                        .orgId(user.getOrgId())
                        .orgName(user.getOrgName())
                        .creatorId(user.getUserId())
                        .creatorName(user.getNickname())
                        .createDate(now)
                        .updaterId(user.getUserId())
                        .updaterName(user.getNickname())
                        .updateDate(now)
                        .build())
                .collect(Collectors.toList());
    }
}
