package com.labway.lims.meibiao.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.meibiao.dto.CalcFormulaVariableReplaceDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;
import com.labway.lims.meibiao.service.ICalcFormulaVariableReplaceService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RequestMapping("/variable-replace")
@RestController
public class CalcFormulaVariableReplaceController extends BaseController {


    @Resource
    private ICalcFormulaVariableReplaceService iCalcFormulaVariableReplaceService;


    @GetMapping("/selectAll/{formulaId}")
    public Object selectByReplaceId(@PathVariable String formulaId){
        if (StringUtils.isBlank(formulaId) || !formulaId.matches(EnzymeLabelCalcFormulaDTO.PATTERN_NUMBER)) {
            throw new IllegalArgumentException("参数异常！");
        }

        return iCalcFormulaVariableReplaceService.selectByFormulaId(Long.parseLong(formulaId));
    }

    @PostMapping("/insert")
    public Object saveVariableReplace(@RequestBody CalcFormulaVariableReplaceDTO dto){
        dto.verifySaveParams();
        return iCalcFormulaVariableReplaceService.addVariableReplace(dto);
    }

    @PostMapping("/update")
    public Object updateVariableReplace(@RequestBody CalcFormulaVariableReplaceDTO dto){
        dto.verifyUpdateParams();
        return iCalcFormulaVariableReplaceService.updateVariableReplace(dto);
    }

    @PostMapping("/delete/{replaceId}")
    public Object updateVariableReplace(@PathVariable String replaceId){
        if (StringUtils.isBlank(replaceId) || !replaceId.matches(EnzymeLabelCalcFormulaDTO.PATTERN_NUMBER)) {
            throw new IllegalArgumentException("参数异常！");
        }
        return iCalcFormulaVariableReplaceService.deleteByReplaceId(Long.parseLong(replaceId));
    }





}
