package com.labway.lims.meibiao.controller;

import cn.hutool.core.lang.Assert;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.meibiao.dto.EnzymeLabelLayoutDTO;
import com.labway.lims.meibiao.dto.TemplateSampleDTO;
import com.labway.lims.meibiao.service.IEnzymeLabelLayoutService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * @description 酶标板布局
 * <AUTHOR>
 * @date 2023-11-27
 */
@RequestMapping("/layout")
@RestController
public class EnzymeLabelLayoutController extends BaseController {

    @Resource
    private IEnzymeLabelLayoutService iEnzymeLabelLayoutService;


    @PostMapping("/insert/template")
    public Object saveLabelLayout(@RequestBody TemplateSampleDTO dto){
        dto.verifySaveTemplateParams();
        return iEnzymeLabelLayoutService.addLabelLayoutAndTemplate(dto.getEnzymeLabelLayout(), dto.getData());
    }

    @PostMapping("/insert/sample")
    public Object saveSample(@RequestBody TemplateSampleDTO dto){
        dto.verifySaveSampleParams();
        return iEnzymeLabelLayoutService.addLabelLayoutAndSample(dto.getEnzymeLabelLayout(), dto.getData());
    }

    @GetMapping("/selectAllTemplate")
    public Object selectAllTemplate(@RequestParam("isTemplate")Integer isTemplate){
        return iEnzymeLabelLayoutService.selectAllTemplate(isTemplate);
    }

    @GetMapping("/select-by-plateId")
    public Object selectByPlateId(@RequestParam Long plateId){
        Assert.notNull(plateId, "参数不能为空");
        return iEnzymeLabelLayoutService.selectByPlateId(plateId);
    }

    @GetMapping("/select-by-layoutId")
    public Object selectByLayoutId(@RequestParam Long layoutId){
        Assert.notNull(layoutId, "参数不能为空");
        return iEnzymeLabelLayoutService.selectByLayoutId(layoutId);
    }

    @PostMapping("/update-report-layout")
    public Object updateReportLayout(@RequestBody EnzymeLabelLayoutDTO layoutDTO) {
        Assert.notNull(layoutDTO, "参数不能为空");
        Assert.notNull(layoutDTO.getLayoutId(), "id不能为空");
        layoutDTO.verifyReportItemInfo(layoutDTO.getReagentManufacturer(), layoutDTO.getReagentBatch(), layoutDTO.getReagentValidDate());
        return iEnzymeLabelLayoutService.updateReportLayout(layoutDTO.getLayoutId(), layoutDTO.getReagentManufacturer(), layoutDTO.getReagentBatch(), layoutDTO.getReagentValidDate());
    }

}
