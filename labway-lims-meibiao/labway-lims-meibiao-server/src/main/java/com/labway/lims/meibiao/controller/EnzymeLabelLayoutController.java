package com.labway.lims.meibiao.controller;

import cn.hutool.core.lang.Assert;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.meibiao.dto.TemplateSampleDTO;
import com.labway.lims.meibiao.service.IEnzymeLabelLayoutService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * @description 酶标板布局
 * <AUTHOR>
 * @date 2023-11-27
 */
@RequestMapping("/layout")
@RestController
public class EnzymeLabelLayoutController extends BaseController {

    @Resource
    private IEnzymeLabelLayoutService iEnzymeLabelLayoutService;


    @PostMapping("/insert/template")
    public Object saveLabelLayout(@RequestBody TemplateSampleDTO dto){
        dto.verifySaveTemplateParams();
        return iEnzymeLabelLayoutService.addLabelLayoutAndTemplate(dto.getEnzymeLabelLayout(), dto.getData());
    }

    @PostMapping("/insert/sample")
    public Object saveSample(@RequestBody TemplateSampleDTO dto){
        dto.verifySaveSampleParams();
        return iEnzymeLabelLayoutService.addLabelLayoutAndSample(dto.getEnzymeLabelLayout(), dto.getData());
    }

    @GetMapping("/selectAllTemplate")
    public Object selectAllTemplate(@RequestParam("isTemplate")Integer isTemplate){
        return iEnzymeLabelLayoutService.selectAllTemplate(isTemplate);
    }

    @GetMapping("/select-by-plateId")
    public Object selectByPlateId(@RequestParam Long plateId){
        Assert.notNull(plateId, "参数不能为空");
        return iEnzymeLabelLayoutService.selectByPlateId(plateId);
    }

    @GetMapping("/select-by-layoutId")
    public Object selectByLayoutId(@RequestParam Long layoutId){
        Assert.notNull(layoutId, "参数不能为空");
        return iEnzymeLabelLayoutService.selectByLayoutId(layoutId);
    }

}
