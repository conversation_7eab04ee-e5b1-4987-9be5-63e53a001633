package com.labway.lims.meibiao.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.meibiao.dal.CalcFormulaVariableReplaceDal;
import com.labway.lims.meibiao.dal.EnzymeLabelCalcFormulaDal;
import com.labway.lims.meibiao.dto.CalcFormulaVariableReplaceDTO;
import com.labway.lims.meibiao.mapper.CalcFormulaVariableReplaceMapper;
import com.labway.lims.meibiao.model.TbCalcFormulaDetermination;
import com.labway.lims.meibiao.model.TbCalcFormulaVariableReplace;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.CalcFormulaVariableReplaceVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class CalcFormulaVariableReplaceServiceImpl extends ServiceImpl<CalcFormulaVariableReplaceMapper, TbCalcFormulaVariableReplace>
        implements ICalcFormulaVariableReplaceService {

    @Resource
    private CalcFormulaVariableReplaceDal calcFormulaVariableReplaceDal;

    @Resource
    private EnzymeLabelCalcFormulaDal enzymeLabelCalcFormulaDal;


    @Override
    public List<CalcFormulaVariableReplaceVO> selectByFormulaId(long formulaId) {
        return calcFormulaVariableReplaceDal.selectByFormulaIdOrderBySort(formulaId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addVariableReplace(CalcFormulaVariableReplaceDTO dto) {

        LoginUserHandler.User user = LoginUserHandler.get();
        LocalDateTime now = LocalDateTime.now();
        log.info("新增变量替换 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);

        if (ObjectUtils.isEmpty(enzymeLabelCalcFormulaDal.selectByFormulaId(dto.getFormulaId()))) {
            throw new IllegalArgumentException("当前计算公式不存在");
        }

        TbCalcFormulaVariableReplace tbCalcFormulaVariableReplace = JsonUtils.beanToBean(dto, TbCalcFormulaVariableReplace.class);
        tbCalcFormulaVariableReplace.setReplaceId(null);
        tbCalcFormulaVariableReplace.setIsDelete(0);

        tbCalcFormulaVariableReplace.setCreateDate(now);
        tbCalcFormulaVariableReplace.setUpdateDate(now);

        tbCalcFormulaVariableReplace.setCreatorId(user.getUserId());
        tbCalcFormulaVariableReplace.setCreatorName(user.getNickname());

        tbCalcFormulaVariableReplace.setUpdaterId(user.getUserId());
        tbCalcFormulaVariableReplace.setUpdaterName(user.getNickname());

        if(!calcFormulaVariableReplaceDal.saveVariableReplace(tbCalcFormulaVariableReplace)){
            log.error("新增变量替换失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);
            throw new IllegalArgumentException("新增变量替换失败");
        }
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateVariableReplace(CalcFormulaVariableReplaceDTO dto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        LocalDateTime now = LocalDateTime.now();

        log.info("修改变量替换 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);

        TbCalcFormulaVariableReplace tbCalcFormulaVariableReplace = JsonUtils.beanToBean(dto, TbCalcFormulaVariableReplace.class);
        tbCalcFormulaVariableReplace.setUpdateDate(now);
        tbCalcFormulaVariableReplace.setUpdaterId(user.getUserId());
        tbCalcFormulaVariableReplace.setUpdaterName(user.getNickname());
        if(!calcFormulaVariableReplaceDal.updateVariableReplace(tbCalcFormulaVariableReplace)){
            log.error("修改变量替换失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);
            throw new IllegalArgumentException("修改变量替换失败");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteByReplaceId(long replace) {
        LoginUserHandler.User user = LoginUserHandler.get();
        log.info("删除变量替换 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), replace);

        if(!calcFormulaVariableReplaceDal.deleteByReplaceId(replace)){
            log.info("删除变量替换失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), replace);
            throw new IllegalArgumentException("删除变量替换失败");
        }

        return true;
    }

    @Override
    public List<CalcFormulaVariableReplaceVO> selectByFormulaIds(Collection<Long> formulaIds) {
        return calcFormulaVariableReplaceDal.selectByFormulaIds(formulaIds);
    }
}
