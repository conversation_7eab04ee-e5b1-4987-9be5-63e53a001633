package com.labway.lims.meibiao.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.meibiao.dal.CalcFormulaDeterminationDal;
import com.labway.lims.meibiao.dal.CalcFormulaVariableReplaceDal;
import com.labway.lims.meibiao.dal.EnzymeLabelCalcFormulaDal;
import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;
import com.labway.lims.meibiao.mapper.EnzymeLabelCalcFormulaMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelCalcFormula;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelCalcFormulaVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 酶标计算公式
 * @date 2023-11-22
 */
@Slf4j
@DubboService
public class EnzymeLabelCalcFormulaServiceImpl extends ServiceImpl<EnzymeLabelCalcFormulaMapper, TbEnzymeLabelCalcFormula> implements IEnzymeLabelCalcFormulaService {

    @Resource
    private EnzymeLabelCalcFormulaDal enzymeLabelCalcFormulaDal;

    @Resource
    private CalcFormulaDeterminationDal calcFormulaDeterminationDal;

    @Resource
    private CalcFormulaVariableReplaceDal calcFormulaVariableReplaceDal;


    @Override
    public EnzymeLabelCalcFormulaVO selectByFormulaId(long formulaId) {
//        LoginUserHandler.User user = LoginUserHandler.get();
        return enzymeLabelCalcFormulaDal.selectByFormulaId(formulaId);
    }

    @Override
    public List<EnzymeLabelCalcFormulaVO> selectAll() {
        return enzymeLabelCalcFormulaDal.selectAllOrderBySortASC();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addCheckFormula(EnzymeLabelCalcFormulaDTO dto) {
        // 判断计算公式是否重复
        LoginUserHandler.User user = LoginUserHandler.get();
        log.info("新增计算公式 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);

        boolean bool = enzymeLabelCalcFormulaDal.selectRepetition(dto.getFormulaCode());

        if (bool) throw new IllegalArgumentException("计算公式编码已存在");

        if(!enzymeLabelCalcFormulaDal.insertCheckFormula(fillCheckFormulaData(dto))){
            log.error("新增计算公式 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);
            throw new IllegalArgumentException("新增计算公式失败");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateCheckFormula(EnzymeLabelCalcFormulaDTO dto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        log.info("修改计算公式 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);
        
        TbEnzymeLabelCalcFormula tbEnzymeLabelCalcFormula = JsonUtils.beanToBean(dto, TbEnzymeLabelCalcFormula.class);
        tbEnzymeLabelCalcFormula.setUpdateDate(LocalDateTime.now());
        tbEnzymeLabelCalcFormula.setUpdaterId(user.getUserId());
        tbEnzymeLabelCalcFormula.setUpdaterName(user.getNickname());
        if(!enzymeLabelCalcFormulaDal.updateCheckFormulaByFormulaId(tbEnzymeLabelCalcFormula)){
            log.error("修改计算公式失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);
            throw new IllegalArgumentException("修改计算公式失败");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteCheckFormula(long formulaId) {
        LoginUserHandler.User user = LoginUserHandler.get();
        log.info("删除计算公式 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), formulaId);

        if(!enzymeLabelCalcFormulaDal.deleteByFormulaId(formulaId)){

            log.error("删除计算公式失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), formulaId);

            throw new IllegalArgumentException("删除计算公式失败");
        }

        calcFormulaDeterminationDal.deleteByFormulaId(formulaId);
        calcFormulaVariableReplaceDal.deleteByFormulaId(formulaId);

        return true;
    }


    private TbEnzymeLabelCalcFormula fillCheckFormulaData(EnzymeLabelCalcFormulaDTO dto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        LocalDateTime now = LocalDateTime.now();

        TbEnzymeLabelCalcFormula tbEnzymeLabelCalcFormula = JsonUtils.beanToBean(dto, TbEnzymeLabelCalcFormula.class);

        tbEnzymeLabelCalcFormula.setFormulaId(null);
        tbEnzymeLabelCalcFormula.setFormulaCode(dto.getFormulaCode());
        tbEnzymeLabelCalcFormula.setFormulaDesc(StringUtils.defaultString(tbEnzymeLabelCalcFormula.getFormulaDesc()));
        tbEnzymeLabelCalcFormula.setFormulaShort(StringUtils.defaultString(tbEnzymeLabelCalcFormula.getFormulaShort()));

        tbEnzymeLabelCalcFormula.setGroupId(user.getGroupId());
        tbEnzymeLabelCalcFormula.setGroupName(user.getGroupName());

        tbEnzymeLabelCalcFormula.setIsDelete(0);

        tbEnzymeLabelCalcFormula.setOrgId(user.getOrgId());
        tbEnzymeLabelCalcFormula.setOrgName(user.getOrgName());

        tbEnzymeLabelCalcFormula.setCreateDate(now);
        tbEnzymeLabelCalcFormula.setUpdateDate(now);

        tbEnzymeLabelCalcFormula.setCreatorId(user.getUserId());
        tbEnzymeLabelCalcFormula.setCreatorName(user.getNickname());

        tbEnzymeLabelCalcFormula.setUpdaterId(user.getUserId());
        tbEnzymeLabelCalcFormula.setUpdaterName(user.getNickname());

        return tbEnzymeLabelCalcFormula;
    }

}
