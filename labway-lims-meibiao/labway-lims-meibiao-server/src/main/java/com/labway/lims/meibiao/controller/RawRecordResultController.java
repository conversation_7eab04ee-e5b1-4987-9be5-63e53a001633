package com.labway.lims.meibiao.controller;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.meibiao.dto.PrintRawRecordResultDTO;
import com.labway.lims.meibiao.dto.SelectRawRecordResultDTO;
import com.labway.lims.meibiao.dto.StateDataDTO;
import com.labway.lims.meibiao.service.IEnzymeLabelReportItemService;
import com.labway.lims.meibiao.service.IRawRecordResultService;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelReportItemVO;
import com.labway.lims.meibiao.vo.RawRecordResultVO;
import com.labway.lims.pdfreport.api.dto.DefaultExpiresDate;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.routine.api.dto.ReceiveMeiBiaoResultDTO;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 原始记录结果（免疫）
 */
@RestController
@RefreshScope
@RequestMapping("/raw-record-result")
public class RawRecordResultController extends BaseController {
    private static final String RAW_RECORD_RESULT_PRINT = "%sMEIBIAO:%s";
    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private IRawRecordResultService iRawRecordResultService;

    @Resource
    private IEnzymeLabelReportItemService iEnzymeLabelReportItemService;

    @DubboReference
    private SampleResultService sampleResultService;

    @DubboReference
    private PdfReportService pdfReportService;

    @Value("${meibiao.reportIds}")
    private String reportIds;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @PostMapping("/select")
    public Object selectRawRecordResult(@RequestBody SelectRawRecordResultDTO dto) {
        dto.verifySelectParams();
        return iRawRecordResultService.selectRawRecordResult(dto);
    }

    @PostMapping("/generate/plate")
    public Object generatePlate(@RequestBody SelectRawRecordResultDTO dto) {
        dto.verifyGeneratePlateParams();
        RawRecordResultVO vo = iRawRecordResultService.selectRawRecordResult(dto);
        return iRawRecordResultService.generatePlate(dto, vo);
    }

    /**
     * 查询乙肝两对半的信息
     *
     * @return
     */
    @GetMapping("/hepatitis-b")
    public Object select() {
        return iRawRecordResultService.selectHepatitisB().get(0);
    }


    @PostMapping("/print")
    public Object printResult(@RequestBody SelectRawRecordResultDTO dto) {
        dto.verifySelectParams();

        final String redisKey = String.format(RAW_RECORD_RESULT_PRINT, redisPrefix.getBasePrefix(), dto.uniqueResultKey());
        String url = stringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isBlank(url)) {
            // 报告项目
            Long labelReportId = dto.getLabelReportId();

            // 酶标仪报告项目
            List<EnzymeLabelReportItemVO> enzymeLabelReportItemVOS;

            // 生成乙肝两对半的五个项目信息
            if (Objects.equals(labelReportId, StateDataDTO.LONG_MINUS_ONE)) {
                Assert.notNull(reportIds, "未配置[乙肝两对半]的五个项目");
                String[] split = reportIds.split(",");
                Assert.isTrue(split.length == 5, "乙肝两对半项目数量配置错误 应有:5 实际:" + split.length);

                List<Long> reportIdList = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());

                enzymeLabelReportItemVOS = iEnzymeLabelReportItemService.selectByLabelReportIds(reportIdList);

                Assert.isTrue(enzymeLabelReportItemVOS.size() == 5, "请查看乙肝两对半的5个项目是否都存在");
            } else {
                enzymeLabelReportItemVOS = new ArrayList<>();
                EnzymeLabelReportItemVO enzymeLabelReportItemVO = iEnzymeLabelReportItemService.selectByLabelReportId(labelReportId);
                Assert.notNull(enzymeLabelReportItemVO, "报告项目不存在");
                enzymeLabelReportItemVOS.add(enzymeLabelReportItemVO);
            }

            // 生成的pdf的头部信息
            PrintRawRecordResultDTO printRawRecordResultDTO = new PrintRawRecordResultDTO(enzymeLabelReportItemVOS);

            // 结果
            RawRecordResultVO vo = iRawRecordResultService.selectRawRecordResult(dto);
            List<RawRecordResultVO.Result> results = vo.selectList().stream()
                    .filter(e -> !Objects.equals(NumberUtils.LONG_ZERO, e.getSampleId()))
                    .collect(Collectors.toList());

            // 收集样本和项目
            final List<ReceiveMeiBiaoResultDTO> receiveMeiBiaoResultDTOS = results.stream()
                    .flatMap(result -> enzymeLabelReportItemVOS.stream().map(reportItemVO -> {
                        final ReceiveMeiBiaoResultDTO receiveMeiBiaoResultDTO = new ReceiveMeiBiaoResultDTO()
                                .setSampleId(result.getSampleId())
                                .setReportItemCode(reportItemVO.getReportItemCode());
                        result.getReportIds().add(reportItemVO.getReportItemId());
                        return receiveMeiBiaoResultDTO;
                    }))
                    .collect(Collectors.toList());

            // 查询样本结果
            List<SampleResultDto> sampleResultDtos = sampleResultService.selectBySampleIdsAndReportItemCodes(receiveMeiBiaoResultDTOS);
            Map<Long, List<SampleResultDto>> sampleResultMap = sampleResultDtos.stream().collect(Collectors.groupingBy(SampleResultDto::getSampleId));

            for (RawRecordResultVO.Result result : results) {
                List<Long> reportItemIds = result.getReportIds();
                // 能查到样本结果将样本结果赋值， 查不到则置为空串
                List<SampleResultDto> sampleResultDtos1 = sampleResultMap.get(result.getSampleId());
                if (CollectionUtils.isEmpty(sampleResultDtos1)) {
                    result.setOneItem("");
                    result.setTwoItem("");
                    result.setThreeItem("");
                    result.setFourItem("");
                    result.setFiveItem("");
                    continue;
                }

                // 项目id和结果
                Map<Long, String> resultMap = sampleResultDtos1.stream().collect(Collectors.toMap(SampleResultDto::getReportItemId, SampleResultDto::getResult));

                // 填充项目信息
                if (reportItemIds.size() > 1) {
                    result.setOneItem(resultMap.get(reportItemIds.get(0)));
                    result.setTwoItem(resultMap.get(reportItemIds.get(1)));
                    result.setThreeItem(resultMap.get(reportItemIds.get(2)));
                    result.setFourItem(resultMap.get(reportItemIds.get(3)));
                    result.setFiveItem(resultMap.get(reportItemIds.get(4)));
                } else {
                    for (Long reportItemId : reportItemIds) {
                        final String reportItemName = resultMap.get(reportItemId);
                        if (StringUtils.isNotBlank(reportItemName)) {
                            result.setOneItem(reportItemName);
                            break;
                        }
                    }
                }
            }

            PdfReportParamDto paramDto = JsonUtils.beanToBean(printRawRecordResultDTO, PdfReportParamDto.class);
            paramDto.put("oneItem", vo.getOneItem().getReportItemEnName());
            if (vo.getTwoItem() != null) {
                paramDto.put("twoItem", vo.getTwoItem().getReportItemEnName());
                paramDto.put("threeItem", vo.getThreeItem().getReportItemEnName());
                paramDto.put("fourItem", vo.getFourItem().getReportItemEnName());
                paramDto.put("fiveItem", vo.getFiveItem().getReportItemEnName());
            }
            paramDto.put("results", results);
            // 缓存5分钟
            url = pdfReportService.build2Url(PdfTemplateTypeEnum.ORIGINAL_RESULT_IMMUNE.getCode(), paramDto, DefaultExpiresDate.DAY_ONE);
            stringRedisTemplate.opsForValue().set(redisKey, url, 5, TimeUnit.MINUTES);
        }
        return Map.of("url", url);
    }

}
