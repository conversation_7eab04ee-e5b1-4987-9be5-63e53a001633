package com.labway.lims.meibiao.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.meibiao.dto.CalcFormulaDeterminationDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;
import com.labway.lims.meibiao.service.ICalcFormulaDeterminationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description 定性描述维护
 * <AUTHOR>
 * @date 2023-11-22
 */
@RequestMapping("/determination")
@RestController
public class CalcFormulaDeterminationController extends BaseController {

    @Resource
    private ICalcFormulaDeterminationService iCalcFormulaDeterminationService;

    @GetMapping("/selectAll/{formulaId}")
    public Object selectAllByCheckFormulaId(@PathVariable String formulaId){
        if (StringUtils.isBlank(formulaId) || !formulaId.matches(EnzymeLabelCalcFormulaDTO.PATTERN_NUMBER)) {
            throw new IllegalArgumentException("参数异常！");
        }
        return iCalcFormulaDeterminationService.selectAllByFormulaId(Long.parseLong(formulaId));
    }


    @PostMapping("/insert")
    public Object insertDetermination(@RequestBody CalcFormulaDeterminationDTO dto){
        dto.verifySaveParams();

        return iCalcFormulaDeterminationService.addDetermination(dto);
    }

    @PostMapping("/update")
    public Object updateDetermination(@RequestBody CalcFormulaDeterminationDTO dto){
        dto.verifyUpdateParams();

        return iCalcFormulaDeterminationService.updateDetermination(dto);
    }


    @PostMapping("/delete/{determinationId}")
    public Object deleteDetermination(@PathVariable String determinationId){
        if (StringUtils.isBlank(determinationId) || !determinationId.matches(EnzymeLabelCalcFormulaDTO.PATTERN_NUMBER)) {
            throw new IllegalArgumentException("参数异常！");
        }
        return iCalcFormulaDeterminationService.deleteByDeterminationId(Long.parseLong(determinationId));
    }

}
