package com.labway.lims.meibiao.service;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.InstrumentItemTypeEnum;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.meibiao.dal.EnzymeLabelCalcFormulaDal;
import com.labway.lims.meibiao.dal.EnzymeLabelReportItemDal;
import com.labway.lims.meibiao.dto.EnzymeLabelCalcFormulaDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelReportItemDTO;
import com.labway.lims.meibiao.dto.StateDataDTO;
import com.labway.lims.meibiao.mapper.EnzymeLabelReportItemMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelReportItem;
import com.labway.lims.meibiao.util.JsonUtils;
import com.labway.lims.meibiao.vo.EnzymeLabelCalcFormulaVO;
import com.labway.lims.meibiao.vo.EnzymeLabelReportItemVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class EnzymeLabelReportItemServiceImpl extends ServiceImpl<EnzymeLabelReportItemMapper, TbEnzymeLabelReportItem>
        implements IEnzymeLabelReportItemService {

    @Resource
    private EnzymeLabelReportItemDal enzymeLabelReportItemDal;

    @Resource
    private EnzymeLabelCalcFormulaDal enzymeLabelCalcFormulaDal;

    @DubboReference
    private InstrumentService instrumentService;

    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;

    @Override
    public List<EnzymeLabelReportItemVO> selectList() {

        List<EnzymeLabelReportItemVO> enzymeLabelReportItemVOS = enzymeLabelReportItemDal.selectList();
        return fillSelectData(enzymeLabelReportItemVOS);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addEnzymeLabelReportItem(EnzymeLabelReportItemDTO dto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        log.info("新增酶标仪报告项目 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);

        checkParams(dto);

        TbEnzymeLabelReportItem tbEnzymeLabelReportItem = fillReportItemData(dto);

        if (!enzymeLabelReportItemDal.insertEnzymeLabelReportItem(tbEnzymeLabelReportItem)) {

            log.error("新增酶标仪报告项目失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);

            throw new IllegalArgumentException("新增酶标仪报告项目失败");
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateEnzymeLabelReportItem(EnzymeLabelReportItemDTO dto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        log.info("修改酶标仪报告项目 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);

        checkParams(dto);

        TbEnzymeLabelReportItem tbEnzymeLabelReportItem = JsonUtils.beanToBean(dto, TbEnzymeLabelReportItem.class);

        tbEnzymeLabelReportItem.setProjectColor(StringUtils.defaultString(tbEnzymeLabelReportItem.getProjectColor()));
        tbEnzymeLabelReportItem.setTestWaveLength(StringUtils.defaultString(tbEnzymeLabelReportItem.getTestWaveLength()));
        tbEnzymeLabelReportItem.setReferenceWaveLength(StringUtils.defaultString(tbEnzymeLabelReportItem.getReferenceWaveLength()));
        tbEnzymeLabelReportItem.setTestMethod(StringUtils.defaultString(tbEnzymeLabelReportItem.getTestMethod()));
        tbEnzymeLabelReportItem.setReagentBatch(StringUtils.defaultString(tbEnzymeLabelReportItem.getReagentBatch()));
        tbEnzymeLabelReportItem.setReagentValidDate(tbEnzymeLabelReportItem.getReagentValidDate() == null ?
                StateDataDTO.NOW_DATE :
                tbEnzymeLabelReportItem.getReagentValidDate()
        );
        tbEnzymeLabelReportItem.setReagentManufacturer(StringUtils.defaultString(tbEnzymeLabelReportItem.getReagentManufacturer()));
        tbEnzymeLabelReportItem.setGrayZoneMinFormula(StringUtils.defaultString(tbEnzymeLabelReportItem.getGrayZoneMinFormula()));
        tbEnzymeLabelReportItem.setGrayZoneMaxFormula(StringUtils.defaultString(tbEnzymeLabelReportItem.getGrayZoneMaxFormula()));

        tbEnzymeLabelReportItem.setUpdateDate(LocalDateTime.now());
        tbEnzymeLabelReportItem.setUpdaterId(user.getUserId());
        tbEnzymeLabelReportItem.setUpdaterName(user.getNickname());

        if (!enzymeLabelReportItemDal.updateEnzymeLabelReportItem(tbEnzymeLabelReportItem)) {

            log.error("修改酶标仪报告项目失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), dto);

            throw new IllegalArgumentException("修改酶标仪报告项目失败");
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteEnzymeLabelReportItem(long labelReportItemId) {
        LoginUserHandler.User user = LoginUserHandler.get();
        log.info("删除计算公式 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), labelReportItemId);

        if(!enzymeLabelReportItemDal.deleteEnzymeLabelReportItem(labelReportItemId)){

            log.error("删除计算公式失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), labelReportItemId);

            throw new IllegalArgumentException("删除计算公式失败");
        }

        return true;
    }

    @Override
    public EnzymeLabelReportItemVO selectByLabelReportId(long labelReportId) {
        EnzymeLabelReportItemVO vo = enzymeLabelReportItemDal.selectByLabelReportId(labelReportId);
        if(Objects.isNull(vo)) return vo;
        List<EnzymeLabelReportItemVO> enzymeLabelReportItemVOS = fillSelectData(List.of(vo));
        return enzymeLabelReportItemVOS.get(0);
    }

    @Override
    public List<EnzymeLabelReportItemVO> selectByLabelReportIds(Collection<Long> labelReportIds) {
        List<EnzymeLabelReportItemVO> enzymeLabelReportItemVOS = enzymeLabelReportItemDal.selectByLabelReportIds(labelReportIds);
        return fillSelectData(enzymeLabelReportItemVOS);
    }

    private TbEnzymeLabelReportItem fillReportItemData(EnzymeLabelReportItemDTO dto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        LocalDateTime now = LocalDateTime.now();

        TbEnzymeLabelReportItem tbEnzymeLabelReportItem = JsonUtils.beanToBean(dto, TbEnzymeLabelReportItem.class);

        tbEnzymeLabelReportItem.setLabelReportId(null);
        tbEnzymeLabelReportItem.setProjectColor(StringUtils.defaultString(tbEnzymeLabelReportItem.getProjectColor()));
        tbEnzymeLabelReportItem.setTestWaveLength(StringUtils.defaultString(tbEnzymeLabelReportItem.getTestWaveLength()));
        tbEnzymeLabelReportItem.setReferenceWaveLength(StringUtils.defaultString(tbEnzymeLabelReportItem.getReferenceWaveLength()));
        tbEnzymeLabelReportItem.setTestMethod(StringUtils.defaultString(tbEnzymeLabelReportItem.getTestMethod()));
        tbEnzymeLabelReportItem.setReagentBatch(StringUtils.defaultString(tbEnzymeLabelReportItem.getReagentBatch()));
        tbEnzymeLabelReportItem.setReagentValidDate(tbEnzymeLabelReportItem.getReagentValidDate() == null ?
                StateDataDTO.NOW_DATE :
                tbEnzymeLabelReportItem.getReagentValidDate()
        );
        tbEnzymeLabelReportItem.setReagentManufacturer(StringUtils.defaultString(tbEnzymeLabelReportItem.getReagentManufacturer()));
        tbEnzymeLabelReportItem.setGrayZoneMinFormula(StringUtils.defaultString(tbEnzymeLabelReportItem.getGrayZoneMinFormula()));
        tbEnzymeLabelReportItem.setGrayZoneMaxFormula(StringUtils.defaultString(tbEnzymeLabelReportItem.getGrayZoneMaxFormula()));

        tbEnzymeLabelReportItem.setRationSetting(StringUtils.defaultString(tbEnzymeLabelReportItem.getRationSetting()));
        tbEnzymeLabelReportItem.setRationResult(StringUtils.defaultString(tbEnzymeLabelReportItem.getRationResult()));

        tbEnzymeLabelReportItem.setIsDelete(0);

        tbEnzymeLabelReportItem.setGroupId(user.getGroupId());
        tbEnzymeLabelReportItem.setGroupName(user.getGroupName());

        tbEnzymeLabelReportItem.setOrgId(user.getOrgId());
        tbEnzymeLabelReportItem.setOrgName(user.getOrgName());

        tbEnzymeLabelReportItem.setCreateDate(now);
        tbEnzymeLabelReportItem.setUpdateDate(now);

        tbEnzymeLabelReportItem.setCreatorId(user.getUserId());
        tbEnzymeLabelReportItem.setCreatorName(user.getNickname());

        tbEnzymeLabelReportItem.setUpdaterId(user.getUserId());
        tbEnzymeLabelReportItem.setUpdaterName(user.getNickname());

        return tbEnzymeLabelReportItem;
    }

    private void checkParams(EnzymeLabelReportItemDTO dto) {
        // 仪器
        InstrumentDto instrumentDto = instrumentService.selectByInstrumentId(dto.getInstrumentId());
        Assert.notNull(instrumentDto, "仪器不存在");

        // 仪器下报告项目
        List<InstrumentReportItemDto> instrumentReportItemDtoList = instrumentReportItemService.selectByInstrumentId(dto.getInstrumentId());
        Optional<InstrumentReportItemDto> instrumentReportItemDtoOptional = instrumentReportItemDtoList.stream()
                .filter(e -> e.getInstrumentReportItemId().equals(dto.getReportItemId()))
                .findFirst();
        Assert.isTrue(instrumentReportItemDtoOptional.isPresent(), "该仪器下该报告项目不存在");

        // 计算公式
        if (dto.getItemTypeCode().equals(InstrumentItemTypeEnum.QUALITATIVE.getCode())) {
            EnzymeLabelCalcFormulaVO enzymeLabelCalcFormulaVO = enzymeLabelCalcFormulaDal.selectByFormulaId(dto.getFormulaId());
            Assert.notNull(enzymeLabelCalcFormulaVO, "计算公式不存在");
        }
    }

    private List<EnzymeLabelReportItemVO> fillSelectData(List<EnzymeLabelReportItemVO> enzymeLabelReportItemVOS){
        if(CollectionUtils.isEmpty(enzymeLabelReportItemVOS)){
            return Collections.emptyList();
        }

        // 仪器code 和name
        List<Long> instrumentIds = enzymeLabelReportItemVOS.stream().map(EnzymeLabelReportItemDTO::getInstrumentId).distinct().collect(Collectors.toList());
        List<InstrumentDto> instrumentDtos = instrumentService.selectByInstrumentIds(instrumentIds);
        Map<Long, InstrumentDto> instrumentDtoMap = instrumentDtos.stream().collect(Collectors.toMap(InstrumentDto::getInstrumentId, Function.identity(), (a, b) -> b));

        // 报告项目code 和 name
        List<Long> reportItemIds = enzymeLabelReportItemVOS.stream().map(EnzymeLabelReportItemDTO::getReportItemId).distinct().collect(Collectors.toList());
        List<InstrumentReportItemDto> instrumentReportItemDtos = instrumentReportItemService.selectByInstrumentReportItemIds(reportItemIds);
        Map<Long, InstrumentReportItemDto> reportItemDtoMap = instrumentReportItemDtos.stream().collect(Collectors.toMap(InstrumentReportItemDto::getInstrumentReportItemId, Function.identity()));

        //计算公式
        List<Long> formulaIds = enzymeLabelReportItemVOS.stream().map(EnzymeLabelReportItemDTO::getFormulaId).filter(formulaId -> !formulaId.equals(0L)).collect(Collectors.toList());
        List<EnzymeLabelCalcFormulaVO> enzymeLabelCalcFormulaVOS = enzymeLabelCalcFormulaDal.selectByFormulaIds(formulaIds);
        Map<Long, EnzymeLabelCalcFormulaVO> calcFormulaVOMap = enzymeLabelCalcFormulaVOS.stream().collect(Collectors.toMap(EnzymeLabelCalcFormulaDTO::getFormulaId, Function.identity()));

        for (EnzymeLabelReportItemVO vo : enzymeLabelReportItemVOS) {
            InstrumentDto instrumentDto = instrumentDtoMap.get(vo.getInstrumentId());
            InstrumentReportItemDto instrumentReportItemDto = reportItemDtoMap.get(vo.getReportItemId());
            EnzymeLabelCalcFormulaVO enzymeLabelCalcFormulaVO = calcFormulaVOMap.get(vo.getFormulaId());
            if(instrumentDto != null) {
                vo.setInstrumentCode(instrumentDto.getInstrumentCode());
                vo.setInstrumentName(instrumentDto.getInstrumentName());
            }
            if(instrumentReportItemDto != null) {
                vo.setReportItemCode(instrumentReportItemDto.getReportItemCode());
                vo.setReportItemName(instrumentReportItemDto.getReportItemName());
                vo.setInstrumentChannel(instrumentReportItemDto.getInstrumentChannel());
                vo.setReportItemEnName(instrumentReportItemDto.getEnAb());
            }
            if(enzymeLabelCalcFormulaVO != null){
                vo.setEnzymeLabelCalcFormulaVO(enzymeLabelCalcFormulaVO);
            }
        }
        return enzymeLabelReportItemVOS;
    }
}
