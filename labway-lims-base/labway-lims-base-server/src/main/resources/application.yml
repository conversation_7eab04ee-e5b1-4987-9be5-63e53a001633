server:
  port: 12301
  tomcat:
    threads:
      max: 500


dubbo:
  provider:
    filter: loginUserProvider
  consumer:
    filter: loginUserConsumer

spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    hikari:
      minimum-idle: 5
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 1GB
      max-request-size: 1GB

