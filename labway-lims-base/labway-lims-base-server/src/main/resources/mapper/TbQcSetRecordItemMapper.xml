<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbQcSetRecordItemMapper">

    <resultMap id="BaseResultMap" type="com.labway.lims.base.model.TbQcSetRecordItem">
            <id property="qcRecordItemId" column="qc_record_item_id" jdbcType="BIGINT"/>
            <result property="qcRecordBatch" column="qc_record_batch" jdbcType="VARCHAR"/>
            <result property="qcBatchId" column="qc_batch_id" jdbcType="BIGINT"/>
            <result property="orgId" column="org_id" jdbcType="BIGINT"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="instrumentId" column="instrument_id" jdbcType="BIGINT"/>
            <result property="instrumentName" column="instrument_name" jdbcType="VARCHAR"/>
            <result property="instrumentChannel" column="instrument_channel" jdbcType="VARCHAR"/>
            <result property="qcStartDate" column="qc_start_date" jdbcType="TIMESTAMP"/>
            <result property="qcEndDate" column="qc_end_date" jdbcType="TIMESTAMP"/>
            <result property="isUse" column="is_use" jdbcType="SMALLINT"/>
            <result property="status" column="status" jdbcType="SMALLINT"/>
            <result property="statusDesc" column="status_desc" jdbcType="VARCHAR"/>
            <result property="targetValue" column="target_value" jdbcType="BIGINT"/>
            <result property="standardDeviation" column="standard_deviation" jdbcType="BIGINT"/>
            <result property="cvValue" column="cv_value" jdbcType="BIGINT"/>
            <result property="levelCode" column="level_code" jdbcType="SMALLINT"/>
            <result property="levelCodeDesc" column="level_code_desc" jdbcType="VARCHAR"/>
            <result property="qcRulesCollection" column="qc_rules_collection" jdbcType="VARCHAR"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="upperControlLimit" column="upper_control_limit" jdbcType="NUMERIC"/>
            <result property="lowerControlLimit" column="lower_control_limit" jdbcType="NUMERIC"/>
            <result property="qualitativeType" column="qualitative_type" jdbcType="SMALLINT"/>
            <result property="createId" column="create_id" jdbcType="BIGINT"/>
            <result property="createName" column="create_name" jdbcType="VARCHAR"/>
            <result property="updateId" column="update_id" jdbcType="BIGINT"/>
            <result property="updateName" column="update_name" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
            <result property="selfAttribute1" column="self_attribute1" jdbcType="VARCHAR"/>
            <result property="selfAttribute2" column="self_attribute2" jdbcType="VARCHAR"/>
            <result property="selfAttribute3" column="self_attribute3" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="groupId" column="group_id" jdbcType="BIGINT"/>
            <result property="groupCode" column="group_code" jdbcType="VARCHAR"/>
            <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
            <result property="instrumentReportItemId" column="instrument_report_item_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        qc_record_item_id,qc_record_batch,qc_batch_id,
        org_id,org_name,instrument_id,
        instrument_name,instrument_channel,qc_start_date,
        qc_end_date,is_use,status,
        status_desc,target_value,standard_deviation,
        cv_value,level_code,level_code_desc,
        qc_rules_collection,sort,upper_control_limit,
        lower_control_limit,qualitative_type,create_id,
        create_name,update_id,update_name,
        is_delete,self_attribute1,self_attribute2,
        self_attribute3,create_date,update_date,
        group_id,group_code,group_name,
        instrument_report_item_id
    </sql>
    <insert id="insertBatchRecordItem" parameterType="list">
        insert into tb_qc_set_record_item (
        qc_record_item_id,
        qc_record_batch,
        qc_batch_id,
        org_id,
        org_name,
        instrument_id,
        instrument_name,
        instrument_channel,
        qc_start_date,
        qc_end_date,
        is_use,
        status,
        status_desc,
        target_value,
        standard_deviation,
        cv_value,
        level_code,
        level_code_desc,
        qc_rules_collection,
        sort,
        upper_control_limit,
        lower_control_limit,
        qualitative_type,
        create_id,
        create_name,
        update_id,
        update_name,
        is_delete,
        self_attribute1,
        self_attribute2,
        self_attribute3,
        create_date,
        update_date,
        group_id,
        group_code,
        group_name,
        instrument_report_item_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.qcRecordItemId},
            #{item.qcRecordBatch},
            #{item.qcBatchId},
            #{item.orgId},
            #{item.orgName},
            #{item.instrumentId},
            #{item.instrumentName},
            #{item.instrumentChannel},
            #{item.qcStartDate},
            #{item.qcEndDate},
            #{item.isUse},
            #{item.status},
            #{item.statusDesc},
            #{item.targetValue},
            #{item.standardDeviation},
            #{item.cvValue},
            #{item.levelCode},
            #{item.levelCodeDesc},
            #{item.qcRulesCollection},
            #{item.sort},

            #{item.upperControlLimit},
            #{item.lowerControlLimit},
            #{item.qualitativeType},

            #{item.createId},
            #{item.createName},
            #{item.updateId},
            #{item.updateName},
            #{item.isDelete},

            #{item.selfAttribute1},
            #{item.selfAttribute2},
            #{item.selfAttribute3},
            #{item.createDate},
            #{item.updateDate},
            #{item.groupId},
            #{item.groupCode},
            #{item.groupName},
            #{item.instrumentReportItemId},
            )
        </foreach>
    </insert>

</mapper>
