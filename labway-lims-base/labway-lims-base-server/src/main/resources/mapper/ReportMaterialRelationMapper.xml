<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.ReportMaterialRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.labway.lims.base.model.TbReportMaterialRelation">
        <id column="report_material_relation_id" property="reportMaterialRelationId" />
        <result column="report_item_code" property="reportItemCode" />
        <result column="report_item_name" property="reportItemName" />
        <result column="material_code" property="materialCode" />
        <result column="material_name" property="materialName" />
        <result column="group_name" property="groupName" />
        <result column="group_id" property="groupId" />
        <result column="group_code" property="groupCode" />
        <result column="create_date" property="createDate" />
        <result column="creator_id" property="creatorId" />
        <result column="creator_name" property="creatorName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        report_material_relation_id, report_item_code, report_item_name, material_code, material_name, group_name, group_id, group_code, create_date, creator_id, creator_name, is_delete
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tb_report_material_relation
        (report_material_relation_id, report_item_code, report_item_name, material_code, material_name, group_name, group_id, group_code, create_date, creator_id, creator_name, is_delete)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.reportMaterialRelationId}, #{item.reportItemCode}, #{item.reportItemName}, #{item.materialCode}, #{item.materialName}, #{item.groupName}, #{item.groupId}, #{item.groupCode}, #{item.createDate},
             #{item.creatorId}, #{item.creatorName}, #{item.isDelete})
        </foreach>
    </insert>

    <update id="batchDelete" parameterType="java.util.Set">
        UPDATE tb_report_material_relation
        SET is_delete = 1
        WHERE report_material_relation_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="countByReportItemCodeAndGroupName" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM tb_report_material_relation
        WHERE report_item_code = #{reportItemCode}
        AND group_name = #{groupName}
        AND is_delete = 0
    </select>



    <select id="selectAll" resultType="com.labway.lims.base.api.dto.ReportMaterialRelationDto">
        SELECT
            report_material_relation_id as reportMaterialRelationId,
            report_item_code as reportItemCode,
            report_item_name as reportItemName,
            material_code as materialCode,
            material_name as materialName,
            group_name as groupName,
            group_id as groupId,
            group_code as groupCode,
            create_date as createDate,
            creator_id as creatorId,
            creator_name as creatorName,
            is_delete as isDelete
        FROM tb_report_material_relation
        WHERE is_delete = 0
    </select>

    <select id="selectByReportItemCodesAndGroupNames" resultType="com.labway.lims.base.api.dto.ReportMaterialRelationDto">
        SELECT
            report_material_relation_id as reportMaterialRelationId,
            report_item_code as reportItemCode,
            report_item_name as reportItemName,
            material_code as materialCode,
            material_name as materialName,
            group_name as groupName,
            group_id as groupId,
            group_code as groupCode,
            create_date as createDate,
            creator_id as creatorId,
            creator_name as creatorName,
            is_delete as isDelete
        FROM tb_report_material_relation
        WHERE is_delete = 0
        <if test="reportItemCodes != null and reportItemCodes.size() > 0">
            AND report_item_code IN
            <foreach collection="reportItemCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="groupNames != null and groupNames.size() > 0">
            AND group_name IN
            <foreach collection="groupNames" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
    </select>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE tb_report_material_relation
            SET report_item_name = #{item.reportItemName},
                material_name = #{item.materialName},
                group_id = #{item.groupId},
                group_code = #{item.groupCode}
            WHERE report_material_relation_id = #{item.reportMaterialRelationId}
            AND is_delete = 0
        </foreach>
    </update>

</mapper>