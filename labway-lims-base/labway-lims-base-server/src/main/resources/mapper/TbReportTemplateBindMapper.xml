<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbReportTemplateBindMapper">

    <insert id="insertBatch">
        INSERT INTO tb_report_template_bind (report_template_bind_id, bind_type,group_id,
        group_name,instrument_group_id,instrument_group_name,biz_id,report_template_name,report_template_code,create_date,
        update_date,updater_id,updater_name,creator_id,creator_name,enable,is_delete,hsp_org_id,hsp_org_name,bind_group_id,is_combine)
        VALUES
        <foreach collection="bindDtos" item="item" separator=",">
            (
            #{item.reportTemplateBindId},
            #{item.bindType},
            #{item.groupId},
            #{item.groupName},
            #{item.instrumentGroupId},
            #{item.instrumentGroupName},
            #{item.bizId},
            #{item.reportTemplateName},
            #{item.reportTemplateCode},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.enable},
            #{item.isDelete},
            #{item.hspOrgId},
            #{item.hspOrgName},
            #{item.bindGroupId},
            #{item.isCombine}
            )
        </foreach>

    </insert>

</mapper>
