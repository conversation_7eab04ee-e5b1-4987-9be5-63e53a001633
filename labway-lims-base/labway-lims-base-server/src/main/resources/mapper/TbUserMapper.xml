<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbUserMapper">
    <select id="selectByRoleId" resultType="com.labway.lims.base.model.TbUser">
        select *
        from tb_user r
                 inner join tb_user_role tur on tur.user_id = r.user_id and tur.is_delete = 0
        where tur.role_id = #{roleId}
          and r.is_delete = 0
    </select>

    <select id="selectByGroupIdAndOrgId" resultType="com.labway.lims.base.model.TbUser">
        select r.*
        from tb_user r
                 inner join tb_user_group tug on tug.user_id = r.user_id and tug.is_delete = 0
        where tug.group_id = #{groupId}
          and r.is_delete = 0
        UNION
        select r2.*
        from tb_user r2
        where not exists(select 1 from tb_user_group tug2 where tug2.user_id = r2.user_id and tug2.is_delete = 0)
          and r2.org_id = #{orgId}
          and r2.is_delete = 0
    </select>

    <select id="selectByGroupId" resultType="java.lang.String">
        select distinct (nickname) from tb_user
        where   user_id not in (select user_id from tb_user_group where is_delete = 0)
        or user_id in (select user_id from tb_user_group where group_id = #{groupId} and is_delete = 0) and org_id = #{orgId} and is_delete = 0
    </select>
</mapper>
