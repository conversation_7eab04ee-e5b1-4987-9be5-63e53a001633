<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbInstrumentGroupInstrumentMapper">


    <insert id="addBatch">
        insert into tb_instrument_group_instrument (
        instrument_group_instrument_id,
        instrument_group_id,
        instrument_id,
        instrument_code,
        instrument_name,
        enable,
        org_id,
        create_date,
        update_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name,
        is_delete
        ) values
        <foreach collection="groupInstruments" item="item" separator=",">
            (
            #{item.instrumentGroupInstrumentId},
            #{item.instrumentGroupId},
            #{item.instrumentId},
            #{item.instrumentCode},
            #{item.instrumentName},
            #{item.enable},
            #{item.orgId},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.isDelete}
            )
        </foreach>

    </insert>
</mapper>
