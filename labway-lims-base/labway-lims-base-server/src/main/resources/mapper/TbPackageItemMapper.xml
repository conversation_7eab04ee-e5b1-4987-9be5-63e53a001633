<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbPackageItemMapper">


    <insert id="batchAddPackageItems">
        INSERT INTO tb_package_item (
        package_item_id,
        package_id,
        test_item_id,
        test_item_code,
        test_item_name,
        is_delete,
        create_date,
        update_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.packageItemId},
            #{item.packageId},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.testItemName},
            #{item.isDelete},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName}
            )
        </foreach>

    </insert>

    <select id="selectAllByOrgIdAndType"
            resultType="com.labway.lims.base.api.dto.PackageItemDto">
        select tpi.*,
               tp.package_name,
               tp.hsp_org_name,
               tp.hsp_org_id
        from tb_package_item tpi
        inner join tb_package tp on tpi.package_id = tp.package_id
        where tp.is_delete = 0
          and tp.enable = 1
          and tpi.is_delete = 0
          and tp.org_id = #{orgId} and tp.package_type_code = #{typeCode}
    </select>
</mapper>
