<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbMaterialSyncFlowMapper">
    <insert id="saveFlowRecord">
        INSERT INTO tb_material_sync_flow (item_id,
                                           modified_time_start,
                                           modified_time_end,
                                           operation_number,
                                           status,
                                           result_message,
                                           enabled,
                                           opt_user_code,
                                           opt_user_account,
                                           opt_user_name,
                                           create_time,
                                           update_time)
        values (#{itemId},
                #{modifiedTimeStart},
                #{modifiedTimeEnd},
                #{operationNumber},
                #{status},
                #{resultMessage},
                #{enabled},
                #{optUserCode},
                #{optUserAccount},
                #{optUserName},
                #{createTime},
                #{updateTime})
    </insert>
    <update id="updateFlowRecord">
        update tb_material_sync_flow
        set status           = #{status},
            result_message   = #{resultMessage},
            opt_user_code    = #{optUserCode},
            opt_user_account = #{optUserAccount},
            opt_user_name    = #{optUserName},
            update_time      = #{updateTime}
        where item_id = #{itemId}
    </update>

    <select id="recentFlowRecord"
            resultType="com.labway.lims.base.api.dto.MaterialSyncFlowDto">
        select *
        from tb_material_sync_flow
        where status = 1
          and enabled = 1
        order by modified_time_end desc limit 1
    </select>
    <select id="recentFailureFlowRecord"
            resultType="com.labway.lims.base.api.dto.MaterialSyncFlowDto">
        select *
        from tb_material_sync_flow
        where status = 0
          and create_time &gt;= #{afterTime}
          and enabled = 1
    </select>
</mapper>

