<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbRackMapper">
    <update id="updateByRackIds">
        update tb_rack
        <set>
            <if test="rackDto.status != null">
                status = #{rackDto.status},
            </if>

        </set>
        where rack_id in
        <foreach collection="rackIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
