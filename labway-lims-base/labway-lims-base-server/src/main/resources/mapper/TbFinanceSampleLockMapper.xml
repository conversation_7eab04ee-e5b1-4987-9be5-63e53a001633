<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbFinanceSampleLockMapper">


    <insert id="addBatch" parameterType="java.util.List">
        insert into tb_finance_sample_lock (
        sample_lock_record_id,
        apply_sample_id,
        hsp_org_id,
        hsp_org_name,
        status,
        sample_content,
        reason,
        org_id,
        org_name,
        create_date,
        creator_id,
        creator_name,
        is_delete,
        update_id,
        update_name,
        update_date
        ) values
        <foreach collection="locks" item="lock" index="index" separator=",">
            (
            #{lock.sampleLockRecordId},
            #{lock.applySampleId},
            #{lock.hspOrgId},
            #{lock.hspOrgName},
            #{lock.status},
            #{lock.sampleContent},
            #{lock.reason},
            #{lock.orgId},
            #{lock.orgName},
            #{lock.createDate},
            #{lock.creatorId},
            #{lock.creatorName},
            #{lock.isDelete},
            #{lock.updateId},
            #{lock.updateName},
            #{lock.updateDate}
            )
        </foreach>
    </insert>
    <update id="updateBySampleLockRecordIds">
        update tb_finance_sample_lock
        <set>
            <if test="sampleLock.status != null">
                status = #{sampleLock.status},
            </if>
        </set>
        where group_material_id in
        <foreach collection="sampleLockRecordIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
