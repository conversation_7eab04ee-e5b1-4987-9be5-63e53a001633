<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbInstrumentGermMapper">


    <insert id="batchAddTbInstrumentGerms">
        INSERT INTO tb_instrument_germ (
        instrument_germ_id,
        instrument_id,
        instrument_code,
        instrument_name,
        germ_id,
        germ_code,
        instrument_channel,
        org_id,
        org_name,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        is_delete
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.instrumentGermId},
            #{item.instrumentId},
            #{item.instrumentCode},
            #{item.instrumentName},
            #{item.germId},
            #{item.germCode},
            #{item.instrumentChannel},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete}
            )
        </foreach>

    </insert>
</mapper>
