<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbHspOrgMainMapper">

    <insert id="addBatch">
        INSERT INTO tb_hsp_org_main (
        hsp_org_main_id,
        hsp_org_id,
        hsp_org_name,
        hsp_org_code,
        create_date,
        update_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name,
        org_id,
        org_name,
        is_delete
        )
        values
        <foreach collection="hspOrgs" item="item" index="index" separator=",">
            (
            #{item.hspOrgMainId},
            #{item.hspOrgId},
            #{item.hspOrgName},
            #{item.hspOrgCode},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.orgId},
            #{item.orgName},
            #{item.isDelete}
            )
        </foreach>
    </insert>

</mapper>
