<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbInstrumentReportItemMapper">

    <select id="selectByInstrumentGroupId" resultType="com.labway.lims.base.api.dto.InstrumentReportItemDto">
        select tiri.*,tigi.instrument_group_id instrumentGroupId
        from tb_instrument_report_item tiri
        inner join tb_instrument_group_instrument tigi on tigi.instrument_id = tiri.instrument_id and tigi.is_delete = 0
        where tigi.instrument_group_id = #{instrumentGroupId} and tiri.is_delete = 0
    </select>
</mapper>
