<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbQcSetRecordMainMapper">

    <resultMap id="BaseResultMap" type="com.labway.lims.base.model.TbQcSetRecordMain">
            <id property="qcRecordMainId" column="qc_record_main_id" jdbcType="BIGINT"/>
            <result property="batchReportItemId" column="batch_report_item_id" jdbcType="BIGINT"/>
            <result property="qcBatchId" column="qc_batch_id" jdbcType="BIGINT"/>
            <result property="groupId" column="group_id" jdbcType="BIGINT"/>
            <result property="groupCode" column="group_code" jdbcType="VARCHAR"/>
            <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
            <result property="orgId" column="org_id" jdbcType="BIGINT"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="instrumentId" column="instrument_id" jdbcType="BIGINT"/>
            <result property="instrumentName" column="instrument_name" jdbcType="VARCHAR"/>
            <result property="reportItemId" column="report_item_id" jdbcType="BIGINT"/>
            <result property="reportItemCode" column="report_item_code" jdbcType="VARCHAR"/>
            <result property="reportItemName" column="report_item_name" jdbcType="VARCHAR"/>
            <result property="instrumentChannel" column="instrument_channel" jdbcType="VARCHAR"/>
            <result property="qcReagentName" column="qc_reagent_name" jdbcType="VARCHAR"/>
            <result property="qcManufactoryName" column="qc_manufactory_name" jdbcType="VARCHAR"/>
            <result property="setDate" column="set_date" jdbcType="TIMESTAMP"/>
            <result property="qcValTime" column="qc_val_time" jdbcType="TIMESTAMP"/>
            <result property="qcExpireTime" column="qc_expire_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="qcRecordStatus" column="qc_record_status" jdbcType="SMALLINT"/>
            <result property="qcRecordStatusDesc" column="qc_record_status_desc" jdbcType="VARCHAR"/>
            <result property="isUse" column="is_use" jdbcType="SMALLINT"/>
            <result property="createId" column="create_id" jdbcType="BIGINT"/>
            <result property="createName" column="create_name" jdbcType="VARCHAR"/>
            <result property="updateId" column="update_id" jdbcType="BIGINT"/>
            <result property="updateName" column="update_name" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
            <result property="selfAttribute1" column="self_attribute1" jdbcType="VARCHAR"/>
            <result property="selfAttribute2" column="self_attribute2" jdbcType="VARCHAR"/>
            <result property="selfAttribute3" column="self_attribute3" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        qc_record_main_id,batch_report_item_id,qc_batch_id,
        group_id,group_code,group_name,
        org_id,org_name,instrument_id,
        instrument_name,report_item_id,report_item_code,
        report_item_name,instrument_channel,qc_reagent_name,
        qc_manufactory_name,set_date,qc_val_time,
        qc_expire_time,remark,qc_record_status,
        qc_record_status_desc,is_use,create_id,
        create_name,update_id,update_name,
        is_delete,self_attribute1,self_attribute2,
        self_attribute3,create_date,update_date
    </sql>

    <select id="selectAllByReportItemIdAndGroupId" resultType="com.labway.lims.base.api.dto.QcSetRecordMainDto">
        select tqsrm.qc_record_main_id,
        tqsrm.batch_report_item_id,
        tqsrm.qc_batch_id,
        tqsrm.group_id,
        tqsrm.group_code,
        tqsrm.group_name,
        tqsrm.org_id,
        tqsrm.org_name,
        tqsrm.instrument_id,
        tqsrm.instrument_name,
        tqsrm.report_item_id,
        tqsrm.report_item_code,
        tqsrm.report_item_name,
        tqsrm.instrument_channel,
        tqsrm.qc_reagent_name,
        tqsrm.qc_manufactory_name,
        tqsrm.set_date,
        tqsrm.qc_val_time,
        tqsrm.qc_expire_time,
        tqsrm.remark,
        tqsrm.qc_record_status,
        tqsrm.qc_record_status_desc,
        tqsrm.is_use,
        tqsrm.create_id,
        tqsrm.create_name,
        tqsrm.update_id,
        tqsrm.update_name,
        tqsrm.is_delete,
        tqsrm.create_date,
        tqsrm.update_date,
        tqb.reagent_brand
        from tb_qc_set_record_main tqsrm
        inner join tb_qc_batch tqb on tqsrm.qc_batch_id = tqb.qc_batch_id
        <where>
            tqsrm.is_delete = 0
            and tqsrm.is_use = 1
            <if test="groupId != null">
                and tqsrm.group_id = #{groupId}
            </if>
            <if test="reportId != null">
                and tqsrm.report_item_id = #{reportId}
            </if>
        </where>
    </select>
</mapper>
