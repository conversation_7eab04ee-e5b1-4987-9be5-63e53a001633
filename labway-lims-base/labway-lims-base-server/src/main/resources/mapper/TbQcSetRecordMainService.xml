<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbGroupMaterialMapper">
    <insert id="addBatch">
        INSERT INTO tb_group_material (
        group_material_id,
        material_id,
        main_unit_inventory,
        assist_unit_inventory,
        valid_remind_day,
        group_id,
        group_name,
        org_id,
        org_name,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        is_delete
        )
        values
        <foreach collection="gms" item="item" index="index" separator=",">
            (
            #{item.groupMaterialId},
            #{item.materialId},
            #{item.mainUnitInventory},
            #{item.assistUnitInventory},
            #{item.validRemindDay},
            #{item.groupId},
            #{item.groupName},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete}
            )
        </foreach>

    </insert>


    <update id="updateByGroupMaterialIds">
        update tb_group_material
        <set>
            <if test="groupMaterialDto.validRemindDay != null">
                valid_remind_day = #{groupMaterialDto.validRemindDay},
            </if>
        </set>
        where group_material_id in
        <foreach collection="groupMaterialIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>


    <select id="selectByGroupIdAndMaterialCodes"
            resultType="com.labway.lims.base.api.dto.GroupMaterialDetailDto">
        select tgm.*,
        tm.material_code
        from tb_group_material tgm
        left join tb_material tm on tgm.material_id = tm.material_id
        where tgm.group_id = #{groupId}
        and tm.material_code in
        <foreach collection="materialCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and tgm.is_delete = 0 and tm.is_delete = 0
    </select>

</mapper>
