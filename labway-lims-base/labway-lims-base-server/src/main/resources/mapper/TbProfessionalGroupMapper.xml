<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbProfessionalGroupMapper">

    <select id="selectByUserId" resultType="com.labway.lims.base.model.TbProfessionalGroup">
        select pg.* from tb_professional_group pg
        inner join tb_user_group tug on tug.group_id = pg.group_id and tug.is_delete = 0
        where tug.user_id = #{userId} and pg.is_delete = 0 and pg.enable = 1
        order by tug.user_group_id
    </select>

    <select id="selectUserGroupByUserIds" resultType="com.labway.lims.base.api.dto.UserProfessionalGroupDto">
        select r.*,tug.is_default,tug.user_id from tb_professional_group r
        inner join tb_user_group tug on r.group_id = tug.group_id and tug.is_delete = 0
        where tug.user_id in
        <foreach open="(" close=")" collection="userIds" item="item" separator=",">
            #{item}
        </foreach>
        and r.is_delete = 0 and r.enable = 1
        order by tug.user_group_id
    </select>
</mapper>
