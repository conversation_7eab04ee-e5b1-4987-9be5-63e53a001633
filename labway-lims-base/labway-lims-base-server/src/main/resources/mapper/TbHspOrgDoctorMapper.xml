<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbHspOrgDoctorMapper">


    <insert id="addBatch">
        INSERT INTO tb_hsp_org_doctor (
        hsp_org_doctor_id,
        hsp_org_dept_id,
        hsp_org_main_id,
        hsp_org_id,
        hsp_org_name,
        hsp_org_code,
        dept_code,
        dept,
        doctor_code,
        doctor_name,
        create_date,
        update_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name,
        org_id,
        org_name,
        is_delete
        )
        values
        <foreach collection="doctors" item="item" index="index" separator=",">
            (
            #{item.hspOrgDoctorId},
            #{item.hspOrgDeptId},
            #{item.hspOrgMainId},
            #{item.hspOrgId},
            #{item.hspOrgName},
            #{item.hspOrgCode},
            #{item.deptCode},
            #{item.dept},
            #{item.doctorCode},
            #{item.doctorName},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.orgId},
            #{item.orgName},
            #{item.isDelete}
            )
        </foreach>
    </insert>
</mapper>
