<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbReportItemMapper">


    <select id="selectReportItemCountByTestItemIds"
            resultType="com.labway.lims.base.api.dto.ReportItemCountByTestItemIdDto">
        select test_item_id, count(1) as reportItemCount
        from tb_report_item
        where test_item_id in
        <foreach collection="testItemIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and is_delete = 0
        group by test_item_id
    </select>
</mapper>
