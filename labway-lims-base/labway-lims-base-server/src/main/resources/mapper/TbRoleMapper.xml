<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbRoleMapper">
    <select id="selectByUserId" resultType="com.labway.lims.base.model.TbRole">
        select r.* from tb_role r
        inner join tb_user_role tur on r.role_id = tur.role_id and tur.is_delete = 0
        where tur.user_id = #{userId} and r.is_delete = 0 and r.status = 1
    </select>

    <select id="selectUserRoleByUserIds" resultType="com.labway.lims.base.api.dto.UserRoleDto">
        select r.*,tur.is_default,tur.user_id from tb_role r
        inner join tb_user_role tur on r.role_id = tur.role_id and tur.is_delete = 0
        where tur.user_id in
        <foreach open="(" close=")" collection="userIds" item="item" separator=",">
            #{item}
        </foreach>
        and r.is_delete = 0
    </select>
</mapper>
