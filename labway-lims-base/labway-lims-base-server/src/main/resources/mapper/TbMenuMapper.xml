<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbMenuMapper">
    <select id="selectByRoleId" resultType="com.labway.lims.base.model.TbMenu">
        select m.* from tb_menu m
        inner join tb_role_menu trm on trm.menu_id = m.menu_id and trm.is_delete = 0
        where trm.role_id = #{roleId} and m.is_delete = 0
    </select>
</mapper>
