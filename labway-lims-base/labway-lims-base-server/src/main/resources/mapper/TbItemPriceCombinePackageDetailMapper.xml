<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbItemPriceCombinePackageDetailMapper">

    <resultMap type="com.labway.lims.base.model.TbItemPriceCombinePackageDetail" id="TbItemPriceCombinePackageDetailMap">
        <result property="detailId" column="detail_id" jdbcType="INTEGER"/>
        <result property="combinePackageId" column="combine_id" jdbcType="INTEGER"/>
        <result property="testItemId" column="test_item_id" jdbcType="INTEGER"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="updateId" column="update_id" jdbcType="INTEGER"/>
        <result property="updateName" column="update_name" jdbcType="VARCHAR"/>
        <result property="createId" column="create_id" jdbcType="INTEGER"/>
        <result property="createName" column="create_name" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="detailId" useGeneratedKeys="true">
        insert into public.tb_item_price_combine_package_detail(combine_id, test_item_id, create_date, update_date, update_id, update_name, create_id, create_name, is_delete)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.combineId}, #{entity.testItemId}, #{entity.createDate}, #{entity.updateDate}, #{entity.updateId}, #{entity.updateName}, #{entity.createId}, #{entity.createName}, #{entity.isDelete})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="detailId" useGeneratedKeys="true">
        insert into public.tb_item_price_combine_package_detail(combine_id, test_item_id, create_date, update_date, update_id, update_name, create_id, create_name, is_delete)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.combineId}, #{entity.testItemId}, #{entity.createDate}, #{entity.updateDate}, #{entity.updateId}, #{entity.updateName}, #{entity.createId}, #{entity.createName}, #{entity.isDelete})
        </foreach>
        on duplicate key update
         combine_id = values(combine_id) , test_item_id = values(test_item_id) , create_date = values(create_date) , update_date = values(update_date) , update_id = values(update_id) , update_name = values(update_name) , create_id = values(create_id) , create_name = values(create_name) , is_delete = values(is_delete)     </insert>

</mapper>

