<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbHspOrgSpecialOfferMapper">


    <insert id="batchAddTbHspOrgSpecialOffers">
        INSERT INTO tb_hsp_org_special_offer (
        offer_id,
        hsp_org_id,
        hsp_org_name,
        test_item_id,
        test_item_code,
        send_type_code,
        send_type,
        start_date,
        end_date,
        discount,
        org_id,
        org_name,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        is_delete,
        is_tiered_pricing,
        discount_price,
        fee_price
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.offerId},
            #{item.hspOrgId},
            #{item.hspOrgName},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.sendTypeCode},
            #{item.sendType},
            #{item.startDate},
            #{item.endDate},
            #{item.discount},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete},
            #{item.isTieredPricing},
            #{item.discountPrice},
            #{item.feePrice}
            )
        </foreach>

    </insert>

</mapper>
