<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbInstrumentReportItemReferenceMapper">

    <select id="selectByInstrumentGroupId" resultType="com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto">
        select tirir.*
        from tb_instrument_report_item_reference tirir
        inner join tb_instrument_group_instrument tigi on tirir.instrument_id = tigi.instrument_id
        where tigi.instrument_group_id = #{instrumentGroupId}
          and tirir.is_delete = 0
          and tigi.is_delete = 0
    </select>

    <select id="selectByInstrumentGroupIds" resultType="com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto">
        select tirir.*,tigi.instrument_group_id
        from tb_instrument_report_item_reference tirir
        inner join tb_instrument_group_instrument tigi on tirir.instrument_id = tigi.instrument_id
        where tigi.instrument_group_id in
        <foreach collection="instrumentGroupIds" item="instrumentGroupId" separator="," open="(" close=")">
            #{instrumentGroupId}
        </foreach>
        and tirir.is_delete = 0
        and tigi.is_delete = 0
    </select>

    <select id="selectByInstrumentGroupIdAndReportItemCode"
            resultType="com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto">
        select tirir.*
        from tb_instrument_report_item_reference tirir
                 inner join tb_instrument_group_instrument tigi on tirir.instrument_id = tigi.instrument_id
        where tigi.instrument_group_id = #{instrumentGroupId}
          and tirir.report_item_code = #{reportItemCode}
          and tirir.is_delete = 0
          and tigi.is_delete = 0
    </select>
</mapper>
