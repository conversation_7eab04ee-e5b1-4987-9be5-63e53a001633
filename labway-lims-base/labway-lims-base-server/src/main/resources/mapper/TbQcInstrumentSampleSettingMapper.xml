<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbQcInstrumentSampleSettingMapper">

    <resultMap id="BaseResultMap" type="com.labway.lims.base.model.TbQcInstrumentSampleSetting">
            <id property="qcSampleSettigId" column="qc_sample_settig_id" jdbcType="BIGINT"/>
            <result property="qcSampleNo" column="qc_sample_no" jdbcType="VARCHAR"/>
            <result property="qcSampleContent" column="qc_sample_content" jdbcType="VARCHAR"/>
            <result property="instrumentId" column="instrument_id" jdbcType="VARCHAR"/>
            <result property="instrumentName" column="instrument_name" jdbcType="VARCHAR"/>
            <result property="reportItemId" column="report_item_id" jdbcType="BIGINT"/>
            <result property="reportItemName" column="report_item_name" jdbcType="VARCHAR"/>
            <result property="reportItemCode" column="report_item_code" jdbcType="VARCHAR"/>
            <result property="instrumentChannel" column="instrument_channel" jdbcType="VARCHAR"/>
            <result property="orgId" column="org_id" jdbcType="BIGINT"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="rules" column="rules" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="SMALLINT"/>
            <result property="statusDesc" column="status_desc" jdbcType="VARCHAR"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="createId" column="create_id" jdbcType="BIGINT"/>
            <result property="createName" column="create_name" jdbcType="VARCHAR"/>
            <result property="updateId" column="update_id" jdbcType="BIGINT"/>
            <result property="updateName" column="update_name" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="SMALLINT"/>
            <result property="selfAttribute1" column="self_attribute1" jdbcType="VARCHAR"/>
            <result property="selfAttribute2" column="self_attribute2" jdbcType="VARCHAR"/>
            <result property="selfAttribute3" column="self_attribute3" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        qc_sample_settig_id,qc_sample_no,qc_sample_content,
        instrument_id,instrument_name,report_item_id,
        report_item_name,report_item_code,instrument_channel,
        org_id,org_name,rules,
        status,status_desc,sort,
        create_id,create_name,update_id,
        update_name,is_delete,self_attribute1,
        self_attribute2,self_attribute3,create_date,
        update_date
    </sql>
</mapper>
