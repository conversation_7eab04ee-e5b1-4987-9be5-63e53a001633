<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbQcBatchReportItemMapper">

    <insert id="batchAddQcBatchReportItem">
        INSERT INTO tb_qc_batch_report_item (
        batch_report_item_id,
        qc_batch_id,
        qc_batch,
        report_item_code,
        report_item_name,
        report_item_unit,
        is_delete,
        org_id,
        org_name,
        create_date,
        update_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.batchReportItemId},
            #{item.qcBatchId},
            #{item.qcBatch},
            #{item.reportItemCode},
            #{item.reportItemName},
            #{item.reportItemUnit},
            #{item.isDelete},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName}
            )
        </foreach>
    </insert>
</mapper>
