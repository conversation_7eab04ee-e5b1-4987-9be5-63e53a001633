<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbItemPriceBasePackageDetailMapper">

    <insert id="batchAddItemPriceBasePackageDetails">
        INSERT INTO tb_item_price_base_package_detail (
        detail_id,
        package_id,
        test_item_id,
        test_item_code,
        org_id,
        org_name,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        is_delete,
        fee_price
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.detailId},
            #{item.packageId},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete},
            #{item.feePrice}
            )
        </foreach>
    </insert>
</mapper>
