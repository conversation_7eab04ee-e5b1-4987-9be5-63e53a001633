<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbInstrumentGroupTestItemMapper">

    <insert id="addBatch">
        insert into tb_instrument_group_test_item (
        instrument_group_test_item_id,
        instrument_group_id,
        instrument_group_instrument_id,
        instrument_id,
        test_item_id,
        test_item_code,
        test_item_name,
        enable,
        org_id,
        create_date,
        update_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name,
        is_delete
        ) values
        <foreach collection="instrumentGroupTestItems" item="item" index="index" separator=",">
            (
            #{item.instrumentGroupTestItemId},
            #{item.instrumentGroupId},
            #{item.instrumentGroupInstrumentId},
            #{item.instrumentId},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.testItemName},
            #{item.enable},
            #{item.orgId},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.isDelete}
            )
        </foreach>
    </insert>


    <select id="selectByGroupIds" resultType="com.labway.lims.base.api.dto.InstrumentGroupTestItemDto">
        select tigti.* from tb_instrument_group_test_item tigti
        inner join tb_instrument_group tig on tig.instrument_group_id = tigti.instrument_group_id and tig.is_delete = 0
        where tigti.is_delete = 0 and tig.group_id in
        <foreach collection="groupIds" item="item"  separator="," open="(" close=")">
             #{item}
        </foreach>
    </select>

    <select id="selectByGroupId" resultType="com.labway.lims.base.api.dto.InstrumentGroupTestItemDto">
        select tigti.* from tb_instrument_group_test_item tigti
        inner join tb_instrument_group tig on tig.instrument_group_id = tigti.instrument_group_id and tig.is_delete = 0
        where tigti.is_delete = 0 and tig.group_id = #{groupId}

    </select>
</mapper>
