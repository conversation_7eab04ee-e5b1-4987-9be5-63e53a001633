<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbHspOrgDiscountMapper">
    <insert id="addBatch" parameterType="java.util.List">
        INSERT INTO tb_hsp_org_discount (
        discount_id,
        package_id,
        hsp_org_id,
        hsp_org_name,
        send_type_code,
        send_type,
        start_date,
        end_date,
        discount,
        org_id,
        org_name,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        is_delete
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.discountId},
            #{item.packageId},
            #{item.hspOrgId},
            #{item.hspOrgName},
            #{item.sendTypeCode},
            #{item.sendType},
            #{item.startDate},
            #{item.endDate},
            #{item.discount},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete}
            )
        </foreach>
    </insert>

    <select id="selectDiscountDetailByHspOrgIdsAndDateRange"
    resultType="com.labway.lims.base.api.dto.DiscountDetailDto">
        select thod.discount,
               tipbpd.package_id,
               tipbpd.test_item_id,
               thod.hsp_org_id,
               thod.start_date,
               thod.end_date,
               thod.send_type_code
        from tb_hsp_org_discount thod
                 inner join tb_item_price_base_package tipbp on tipbp.package_id = thod.package_id
                 inner join tb_item_price_base_package_detail tipbpd
                            on thod.package_id = tipbpd.package_id
        where thod.is_delete = 0
          and tipbpd.is_delete = 0
          and tipbp.is_delete = 0
          and tipbp.enable = 1
          and (thod.start_date &lt;= #{minDate} and thod.end_date &gt;= #{maxDate} or
               thod.start_date &lt;= #{minDate} and thod.end_date &gt;= #{minDate} or
               thod.start_date &lt;= #{maxDate} and thod.end_date &gt;= #{maxDate}
               )

        <if test="hspOrgIds != null and hspOrgIds.size > 0">
            and thod.hsp_org_id in
            <foreach collection="hspOrgIds" item="id" separator="," open="(" close=")">
               #{id}
            </foreach>
        </if>

        <if test="applyTypes != null and applyTypes.size > 0">
            and thod.send_type_code in
            <foreach collection="applyTypes" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>
