<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbFilePermissionMapper">

    <insert id="addBatch">
        insert into tb_file_permission (file_permission_id, file_id, group_id, group_name, org_id,
        org_name, create_date, update_date, updater_id, updater_name, creator_id, creator_name, is_delete)
        values
        <foreach collection="fps" item="item" separator=",">
            (#{item.filePermissionId}, #{item.fileId}, #{item.groupId}, #{item.groupName}, #{item.orgId},
            #{item.orgName}, #{item.createDate}, #{item.updateDate}, #{item.updaterId}, #{item.updaterName},
            #{item.creatorId}, #{item.creatorName}, #{item.isDelete})
        </foreach>
    </insert>
</mapper>
