<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbInstrumentReportItemCommonPhraseMapper">

    <select id="selectByInstrumentGroupId"
            resultType="com.labway.lims.base.api.dto.InstrumentReportItemCommonPhraseDto">
        select tiricp.*
        from tb_instrument_report_item_common_phrase tiricp
        inner join tb_instrument_group_instrument tigi on tiricp.instrument_id = tigi.instrument_id
        where tiricp.is_delete = 0
          and tigi.is_delete = 0
          and tigi.instrument_group_id = #{instrumentGroupId}
    </select>
</mapper>
