<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbFinanceOrgLockMapper">


    <insert id="addBatch">
        INSERT INTO tb_finance_org_lock (
        org_lock_record_id,
        hsp_org_id,
        hsp_org_name,
        start_date,
        end_date,
        reason,
        status,
        org_id,
        org_name,
        create_date,
        creator_id,
        creator_name,
        is_delete,
        update_date,
        update_id,
        update_name
        )
        values
        <foreach collection="orgLocks" item="item" index="index" separator=",">
            (
            #{item.orgLockRecordId},
            #{item.hspOrgId},
            #{item.hspOrgName},
            #{item.startDate},
            #{item.endDate},
            #{item.reason},
            #{item.status},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete},
            #{item.updateDate},
            #{item.updateId},
            #{item.updateName}
            )
        </foreach>
    </insert>
</mapper>
