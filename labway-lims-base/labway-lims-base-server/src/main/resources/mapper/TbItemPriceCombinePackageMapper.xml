<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbItemPriceCombinePackageMapper">
    <resultMap type="com.labway.lims.base.model.TbItemPriceCombinePackage" id="TbItemPriceCombinePackageMap">
        <result property="combinePackageId" column="combine_package_id" jdbcType="INTEGER"/>
        <result property="combinePackageName" column="combine_package_name" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="INTEGER"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="updateId" column="update_id" jdbcType="INTEGER"/>
        <result property="updateName" column="update_name" jdbcType="VARCHAR"/>
        <result property="createId" column="create_id" jdbcType="INTEGER"/>
        <result property="createName" column="create_name" jdbcType="VARCHAR"/>
        <result property="enable" column="enable" jdbcType="INTEGER"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="combinePackageCode" column="combine_package_code" jdbcType="VARCHAR"/>
        <result property="combinePackagePrice" column="combine_package_price" jdbcType="VARCHAR"/>
        <result property="itemCount" column="item_count" jdbcType="INTEGER"/>
        <result property="hspOrgCode" column="hsp_org_code" jdbcType="VARCHAR"/>
        <result property="hspOrgName" column="hsp_org_name" jdbcType="VARCHAR"/>
        <result property="hspOrgId" column="hsp_org_id" jdbcType="INTEGER"/>
        <result property="itemMd5" column="item_md5" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="combinePackageId" useGeneratedKeys="true">
        insert into tb_item_price_combine_package(combine_package_id,combine_package_name, org_id, org_name,
        create_date, update_date, update_id, update_name, create_id, create_name, enable, is_delete,
        combine_package_code, combine_package_price, item_count, hsp_org_code, hsp_org_name, hsp_org_id, item_md5)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.combinePackageId}, #{entity.combinePackageName}, #{entity.orgId}, #{entity.orgName},
            #{entity.createDate}, #{entity.updateDate}, #{entity.updateId}, #{entity.updateName}, #{entity.createId},
            #{entity.createName}, #{entity.enable}, #{entity.isDelete}, #{entity.combinePackageCode},
            #{entity.combinePackagePrice}, #{entity.itemCount}, #{entity.hspOrgCode}, #{entity.hspOrgName},
            #{entity.hspOrgId}, #{entity.itemMd5})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="combinePackageId" useGeneratedKeys="true">
        insert into public.tb_item_price_combine_package(combine_name, org_id, org_name, create_date, update_date,
        update_id, update_name, create_id, create_name, enable, is_delete, combine_code, combine_price, item_count,
        hsp_org_code, hsp_org_name, hsp_org_id, item_md5)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.combineName}, #{entity.orgId}, #{entity.orgName}, #{entity.createDate}, #{entity.updateDate},
            #{entity.updateId}, #{entity.updateName}, #{entity.createId}, #{entity.createName}, #{entity.enable},
            #{entity.isDelete}, #{entity.combineCode}, #{entity.combinePrice}, #{entity.itemCount},
            #{entity.hspOrgCode}, #{entity.hspOrgName}, #{entity.hspOrgId}, #{entity.itemMd5})
        </foreach>
        on duplicate key update
        combine_name = values(combine_name) , org_id = values(org_id) , org_name = values(org_name) , create_date =
        values(create_date) , update_date = values(update_date) , update_id = values(update_id) , update_name =
        values(update_name) , create_id = values(create_id) , create_name = values(create_name) , enable =
        values(enable) , is_delete = values(is_delete) , combine_code = values(combine_code) , combine_price =
        values(combine_price) , item_count = values(item_count) , hsp_org_code = values(hsp_org_code) , hsp_org_name =
        values(hsp_org_name) , hsp_org_id = values(hsp_org_id) , item_md5 = values(item_md5)
    </insert>

    <!-- 查询校验重复的财务套餐包 -->
    <select id="queryCheckSameCombinePackage" resultMap="TbItemPriceCombinePackageMap">
        select * from tb_item_price_combine_package
        <where>
            (
            <trim prefix="(" prefixOverrides="and|or" suffix=")">
                <if test="
                queryParam.checkCombinePackageCodes != null and queryParam.checkCombinePackageCodes.size() > 0">
                    or ( combine_package_code in
                    <foreach collection="queryParam.checkCombinePackageCodes" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    and is_delete = 0
                    )
                </if>

                <if test="queryParam.checkCombinePackageNames != null and queryParam.checkCombinePackageNames.size() > 0">
                    or ( combine_package_name in
                    <foreach collection="queryParam.checkCombinePackageNames" item="item" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                    and is_delete = 0
                    )
                </if>

                <if test="queryParam.combinePackageHspOrgCodeMd5InfoParams != null and queryParam.combinePackageHspOrgCodeMd5InfoParams.size() > 0">
                    or ((hsp_org_code, item_md5) in
                    <foreach collection="queryParam.combinePackageHspOrgCodeMd5InfoParams" item="item" open="("
                             separator="," close=")">
                        (#{item.hspOrgCode}, #{item.itemMd5})
                    </foreach>
                    and is_delete = 0
                    )
                </if>
            </trim>
            )
            <if test="queryParam.excludeCombinePackageIds != null and queryParam.excludeCombinePackageIds.size() > 0">
                and (combine_package_id not in
                <foreach collection="queryParam.excludeCombinePackageIds" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
                and is_delete = 0)
            </if>
        </where>
    </select>
</mapper>

