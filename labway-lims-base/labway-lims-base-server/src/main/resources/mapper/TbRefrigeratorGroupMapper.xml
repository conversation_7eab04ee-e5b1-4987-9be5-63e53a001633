<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbRefrigeratorGroupMapper">

    <insert id="batchAddTbRefrigeratorGroups">
        INSERT INTO tb_refrigerator_group (
        relation_id,
        refrigerator_id,
        refrigerator_code,
        group_id,
        group_code,
        is_delete,
        create_date,
        update_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.relationId},
            #{item.refrigeratorId},
            #{item.refrigeratorCode},
            #{item.groupId},
            #{item.groupCode},
            #{item.isDelete},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName}
            )
        </foreach>
    </insert>
</mapper>
