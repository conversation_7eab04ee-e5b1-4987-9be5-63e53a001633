<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbMaterialMapper">

    <insert id="addBatch" parameterType="java.util.List">
        insert into tb_material (
        material_id,
        material_code,
        material_name,
        specification,
        methodology,
        registration_number,
        registration_name,
        storage_temperature,
        manufacturers,
        main_unit_inventory,
        main_unit,
        assist_unit,
        assist_uint_inventory,
        unit_conversion_rate,
        valid_date,
        open_valid_date,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        is_delete,
        org_id,
        org_name,
        valid_remind_day,
        from_no,
        info_hash,
        type_code,
        type,
        expected_test_count
        )
        values
        <foreach collection="materials" item="item" separator=",">
            (
            #{item.materialId},
            #{item.materialCode},
            #{item.materialName},
            #{item.specification},
            #{item.methodology},
            #{item.registrationNumber},
            #{item.registrationName},
            #{item.storageTemperature},
            #{item.manufacturers},
            #{item.mainUnitInventory},
            #{item.mainUnit},
            #{item.assistUnit},
            #{item.assistUintInventory},
            #{item.unitConversionRate},
            #{item.validDate},
            #{item.openValidDate},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete},
            #{item.orgId},
            #{item.orgName},
            #{item.validRemindDay},
            #{item.fromNo},
            #{item.infoHash},
            #{item.typeCode},
            #{item.type},
            #{item.expectedTestCount}
            )
        </foreach>
    </insert>

    <update id="updateMaterialsByInfoHash">
        update tb_material
        <trim prefix="set" suffixOverrides=",">

            <trim prefix="main_unit_inventory =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.mainUnitInventory!=null">
                        when from_no=#{i.fromNo} then #{i.mainUnitInventory}
                    </if>
                </foreach>
            </trim>

            <trim prefix="assist_uint_inventory =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.assistUintInventory!=null">
                        when from_no=#{i.fromNo} then #{i.assistUintInventory}
                    </if>
                </foreach>
            </trim>

            <trim prefix="material_code =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.materialCode!=null">
                        when from_no=#{i.fromNo} then #{i.materialCode}
                    </if>
                </foreach>
            </trim>

            <trim prefix="material_name =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.materialName!=null">
                        when from_no=#{i.fromNo} then #{i.materialName}
                    </if>
                </foreach>
            </trim>

            <trim prefix="type =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.type!=null">
                        when from_no=#{i.fromNo} then #{i.type}
                    </if>
                </foreach>
            </trim>

            <trim prefix="type_code =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.typeCode!=null">
                        when from_no=#{i.fromNo} then #{i.typeCode}
                    </if>
                </foreach>
            </trim>

            <trim prefix="specification =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.specification!=null">
                        when from_no=#{i.fromNo} then #{i.specification}
                    </if>
                </foreach>
            </trim>

            <trim prefix="registration_number =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.registrationNumber!=null">
                        when from_no=#{i.fromNo} then #{i.registrationNumber}
                    </if>
                </foreach>
            </trim>

            <trim prefix="registration_name =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.registrationName!=null">
                        when from_no=#{i.fromNo} then #{i.registrationName}
                    </if>
                </foreach>
            </trim>

            <trim prefix="storage_temperature =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.storageTemperature!=null">
                        when from_no=#{i.fromNo} then #{i.storageTemperature}
                    </if>
                </foreach>
            </trim>

            <trim prefix="manufacturers =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.manufacturers!=null">
                        when from_no=#{i.fromNo} then #{i.manufacturers}
                    </if>
                </foreach>
            </trim>

            <trim prefix="main_unit =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.mainUnit!=null">
                        when from_no=#{i.fromNo} then #{i.mainUnit}
                    </if>
                </foreach>
            </trim>

            <trim prefix="assist_unit =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.assistUnit!=null">
                        when from_no=#{i.fromNo} then #{i.assistUnit}
                    </if>
                </foreach>
            </trim>

            <trim prefix="unit_conversion_rate =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.unitConversionRate!=null">
                        when from_no=#{i.fromNo} then #{i.unitConversionRate}
                    </if>
                </foreach>
            </trim>

            <trim prefix="info_hash =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.infoHash!=null">
                        when from_no=#{i.fromNo} then #{i.infoHash}
                    </if>
                </foreach>
            </trim>
            
            <trim prefix="expected_test_count =case" suffix="end,">
                <foreach collection="materials" item="i" index="index">
                    <if test="i.expectedTestCount!=null">
                        when from_no=#{i.fromNo} then #{i.expectedTestCount}
                    </if>
                </foreach>
            </trim>
        </trim>
        where from_no in
        <foreach collection="materials" item="i" index="index" open="(" separator="," close=")">
            #{i.fromNo}
        </foreach>
    </update>

</mapper>
