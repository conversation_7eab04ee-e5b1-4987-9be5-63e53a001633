package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.dto.QueryRackPageDto;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.enums.RackStatusEnum;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.api.service.RackService;
import com.labway.lims.base.vo.QueryArchiveRackPageVo;
import com.labway.lims.base.vo.RackAddRequestVo;
import com.labway.lims.base.vo.RackUpdateRequestVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/3/22 10:23
 */
@RestController
@RequestMapping("/rack")
public class RackController extends BaseController {

    @Resource
    private RackService rackService;
    @DubboReference
    private DictService dictService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @PostMapping("/add")
    public Object add(@RequestBody RackAddRequestVo vo) {
        if (StringUtils.isAnyBlank(vo.getRackCode(), vo.getRackTypeCode()) || Objects.isNull(vo.getRow())
            || Objects.isNull(vo.getColumn()) || Objects.isNull(vo.getEnable())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        if (StringUtils.length(vo.getRackCode()) > BaseController.TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException("试管架编码过长");
        }
        if (!(Objects.equals(vo.getEnable(), YesOrNoEnum.NO.getCode())
            || Objects.equals(vo.getEnable(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("是否启用参数错误");
        }
        if (Objects.equals(vo.getColumn(), NumberUtils.INTEGER_ZERO)
            || Objects.equals(vo.getRow(), NumberUtils.INTEGER_ZERO)) {
            throw new IllegalArgumentException("行与列不可输入0");
        }
        //若名称没有 则默认为编码值
        if (StringUtils.isBlank(vo.getRackName())){
            vo.setRackName(vo.getRackCode());
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        if (Objects.nonNull(rackService.selectByRackCode(vo.getRackCode(), loginUser.getOrgId()))) {
            throw new LimsException("当前试管架编码已存在");
        }

        DictItemDto dictItemDto = dictService.selectByDictCode(vo.getRackTypeCode());
        if (Objects.isNull(dictItemDto)) {
            throw new LimsException("试管架类型不存在");
        }

        final RackDto rackDto = JSON.parseObject(JSON.toJSONString(vo), RackDto.class);
        rackDto.setRackTypeName(dictItemDto.getDictName());

        long id = rackService.add(rackDto);
        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.RACK_LOG.getDesc())
                .setContent(String.format("新增 [%s] 试管架", rackDto.getRackCode())).toJSONString());

        return Map.of("id", id);
    }

    @PostMapping("/update")
    public Object update(@RequestBody RackUpdateRequestVo vo) {
        //如果没有ID 则是新增操作
        if (Objects.isNull(vo.getRackId())){
            return add(vo);
        }

        if (StringUtils.isBlank(vo.getRackTypeCode()) || Objects.isNull(vo.getRow()) || Objects.isNull(vo.getColumn())
            || Objects.isNull(vo.getEnable()) || Objects.isNull(vo.getRackId())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        if (!(Objects.equals(vo.getEnable(), YesOrNoEnum.NO.getCode())
            || Objects.equals(vo.getEnable(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("是否启用参数错误");
        }
        if (Objects.equals(vo.getColumn(), NumberUtils.INTEGER_ZERO)
            || Objects.equals(vo.getRow(), NumberUtils.INTEGER_ZERO)) {
            throw new IllegalArgumentException("行与列不可输入0");
        }



        DictItemDto dictItemDto = dictService.selectByDictCode(vo.getRackTypeCode());
        if (Objects.isNull(dictItemDto)) {
            throw new LimsException("试管架类型不存在");
        }

        RackDto rackDtoNow = rackService.selectByRackId(vo.getRackId());
        if (Objects.isNull(rackDtoNow)) {
            throw new LimsException("试管架不存在");
        }

        // 1: 被占用 0: 可以使用
        if (Objects.equals(rackDtoNow.getStatus(), RackStatusEnum.ACTIVE.getCode())) {
            throw new IllegalStateException("试管架已被占用,不能修改");
        }

        final RackDto target = new RackDto();
        target.setRackId(rackDtoNow.getRackId());

        // 更新项
        target.setRackTypeCode(dictItemDto.getDictCode());
        target.setRackTypeName(dictItemDto.getDictName());
        target.setRow(vo.getRow());
        target.setColumn(vo.getColumn());
        target.setEnable(vo.getEnable());
        target.setRackName(vo.getRackName());

        if (!rackService.updateByRackId(target)) {
            throw new IllegalStateException("修改试管架失败");
        }
        String compare = new CompareUtils<RackDto>().compare(rackDtoNow, target);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.RACK_LOG.getDesc())
                    .setContent(String.format("修改试管架: [%s]", compare)).toJSONString());
        }

        return Collections.emptyMap();
    }

    @PostMapping("/racks")
    public Object selectAll() {
        return rackService.selectAll(LoginUserHandler.get().getOrgId(),false);
    }

    @PostMapping("/archiveRacks")
    public Object archiveRacks(@RequestBody QueryArchiveRackPageVo rackPageVo) {
        LoginUserHandler.User user = LoginUserHandler.get();
        QueryRackPageDto pageDto = new QueryRackPageDto();
        BeanUtils.copyProperties(rackPageVo,pageDto);
        pageDto.setOrgId(user.getOrgId());
        pageDto.setGroupCode(user.getGroupCode());
        return rackService.selectArchiveRackPage(pageDto);
    }

}
