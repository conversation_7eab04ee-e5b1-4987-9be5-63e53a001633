package com.labway.lims.base.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class HspOrganizationVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long hspOrgId;


    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 说明
     */
    private String remark;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 是否外送
     * @see YesOrNoEnum
     */
    private Integer isExport;

    /**
     * 开票名称
     */
    private String invoice;

    /**
     * 销售区域名称
     */
    private String saleAreaName;
    /**
     * 销售区域编码
     */
    private String saleAreaCode;

    /**
     * 销售部门id
     */
    private String saleDeptCode;

    /**
     * 销售部门名称
     */
    private String saleDeptName;

    /**
     * 销售类型id
     */
    private String saleTypeCode;

    /**
     * 销售类型名称
     */
    private String saleTypeName;

    /**
     * 是否支持分血
     */
    private Integer enableSplitBlood;
    /**
     * 双输复核
     */
    private Integer enableDoubleCheck;

    /**
     * 是否打印条码
     */
    private Integer enablePrintBarcode;

    /**
     * 是否生成新条码
     */
    private Integer enableGenerateBarcode;

    /**
     * 送检类型  由,分割
     *
     * @see com.labway.lims.api.DefaultApplyType
     */
    private String applyTypeNames;

    /**
     * 是否启用(0未启用 1已启用)
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 位置(经纬度)
     */
    private String location;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系方式
     */
    private String contactPhone;
    /**
     * 机构id
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;
}
