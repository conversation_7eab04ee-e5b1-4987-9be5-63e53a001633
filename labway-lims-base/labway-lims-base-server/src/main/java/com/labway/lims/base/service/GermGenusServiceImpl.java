package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.GermGenusDto;
import com.labway.lims.base.api.service.GermGenusService;
import com.labway.lims.base.mapper.TbGermGenusMapper;
import com.labway.lims.base.model.TbGermGenus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 细菌菌属 Service impl
 * 
 * <AUTHOR>
 * @since 2023/3/20 17:42
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "germ-genus")
public class GermGenusServiceImpl implements GermGenusService {

    @Resource
    private TbGermGenusMapper tbGermGenusMapper;
    @Resource
    private GermGenusService germGenusService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public long addGermGenus(GermGenusDto germGenusDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbGermGenus germGenus = new TbGermGenus();
        germGenus.setGermGenusName(germGenusDto.getGermGenusName());
        germGenus.setGermGenusCode(germGenusDto.getGermGenusCode());
        germGenus.setEnable(germGenusDto.getEnable());

        germGenus.setGermGenusId(snowflakeService.genId());
        germGenus.setOrgId(loginUser.getOrgId());
        germGenus.setOrgName(loginUser.getOrgName());
        germGenus.setCreateDate(new Date());
        germGenus.setUpdateDate(new Date());
        germGenus.setCreatorId(loginUser.getUserId());
        germGenus.setCreatorName(loginUser.getNickname());
        germGenus.setUpdaterId(loginUser.getUserId());
        germGenus.setUpdaterName(loginUser.getNickname());
        germGenus.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbGermGenusMapper.insert(germGenus) < 1) {
            throw new LimsException("添加细菌菌属失败");
        }
        log.info("用户 [{}] 新增细菌菌属[{}]成功", loginUser.getNickname(), JSON.toJSONString(germGenus));

        return germGenus.getGermGenusId();
    }

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void deleteByGermGenusIds(Collection<Long> germGenusIds) {
        if (CollectionUtils.isEmpty(germGenusIds)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除细菌菌属成功 [{}] 结果 [{}]", loginUser.getNickname(), germGenusIds,
            tbGermGenusMapper.deleteBatchIds(germGenusIds) > 0);

    }

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void updateByGermGenusId(GermGenusDto germGenusDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbGermGenus germGenus = new TbGermGenus();
        BeanUtils.copyProperties(germGenusDto, germGenus);

        germGenus.setUpdaterId(loginUser.getUserId());
        germGenus.setUpdaterName(loginUser.getNickname());
        germGenus.setUpdateDate(new Date());

        if (tbGermGenusMapper.updateById(germGenus) < 1) {
            throw new LimsException("修改细菌菌属失败");
        }

        log.info("用户 [{}] 修改细菌菌属成功 [{}]", loginUser.getNickname(), JSON.toJSONString(germGenus));

    }

    @Override
    @Cacheable(key = "'selectByOrgId:' + #orgId")
    public List<GermGenusDto> selectByOrgId(long orgId) {
        if (orgId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbGermGenus> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGermGenus::getOrgId, orgId);
        queryWrapper.eq(TbGermGenus::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbGermGenus::getCreateDate);

        return convert(tbGermGenusMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByGermGenusId:' + #germGenusId")
    public GermGenusDto selectByGermGenusId(long germGenusId) {
        if (germGenusId < 1) {
            return null;
        }
        LambdaQueryWrapper<TbGermGenus> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGermGenus::getGermGenusId, germGenusId);
        queryWrapper.eq(TbGermGenus::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return convert(tbGermGenusMapper.selectOne(queryWrapper));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByGermGenusName:' + #germGenusName + ',' + #orgId")
    public GermGenusDto selectByGermGenusName(String germGenusName, long orgId) {
        if (StringUtils.isBlank(germGenusName)) {
            return null;
        }
        LambdaQueryWrapper<TbGermGenus> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGermGenus::getGermGenusName, germGenusName);
        queryWrapper.eq(TbGermGenus::getOrgId, orgId);
        queryWrapper.eq(TbGermGenus::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return convert(tbGermGenusMapper.selectOne(queryWrapper));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByGermGenusCode:' + #germGenusCode + ',' + #orgId")
    public GermGenusDto selectByGermGenusCode(String germGenusCode, long orgId) {
        if (StringUtils.isBlank(germGenusCode)) {
            return null;
        }
        LambdaQueryWrapper<TbGermGenus> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGermGenus::getGermGenusCode, germGenusCode);
        queryWrapper.eq(TbGermGenus::getOrgId, orgId);
        queryWrapper.eq(TbGermGenus::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return convert(tbGermGenusMapper.selectOne(queryWrapper));
    }

    @Override
    public List<GermGenusDto> selectByGermGenusIds(Collection<Long> germGenusIds) {
        if (CollectionUtils.isEmpty(germGenusIds)) {
            return Collections.emptyList();
        }

        return germGenusIds.stream().map(germGenusService::selectByGermGenusId).filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    @Override
    public List<GermGenusDto> selectByGermGenusNames(Collection<String> germGenusNames, long orgId) {
        if (CollectionUtils.isEmpty(germGenusNames)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbGermGenus> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbGermGenus::getGermGenusName, germGenusNames);
        queryWrapper.eq(TbGermGenus::getOrgId, orgId);
        queryWrapper.eq(TbGermGenus::getIsDelete, YesOrNoEnum.NO.getCode());

        return convert(tbGermGenusMapper.selectList(queryWrapper));
    }

    /**
     * TbGermGenus 转换 为 GermGenusDto
     * 
     * @param germGenus TbGermGenus
     * @return GermGenusDto
     */
    private GermGenusDto convert(TbGermGenus germGenus) {
        if (Objects.isNull(germGenus)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(germGenus), GermGenusDto.class);
    }

    /**
     * TbGermGenus 转换 为 GermGenusDto
     *
     * @param list TbGermGenus
     * @return GermGenusDto
     */
    private List<GermGenusDto> convert(List<TbGermGenus> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }
}
