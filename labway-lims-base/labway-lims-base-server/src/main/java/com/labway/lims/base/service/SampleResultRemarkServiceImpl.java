package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.SampleResultRemarkDto;
import com.labway.lims.base.api.service.SampleResultRemarkService;
import com.labway.lims.base.mapper.SampleResultRemarkMapper;
import com.labway.lims.base.mapstruct.SampleResultRemarkConvert;
import com.labway.lims.base.model.TbSampleResultRemark;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class SampleResultRemarkServiceImpl implements SampleResultRemarkService {
    @Resource
    private SampleResultRemarkMapper sampleResultRemarkMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private SampleResultRemarkConvert sampleResultRemarkConvert;

    @Override
    public List<SampleResultRemarkDto> selectByOrgId(long orgId) {
        return sampleResultRemarkMapper.selectList(new LambdaQueryWrapper<TbSampleResultRemark>()
                        .eq(TbSampleResultRemark::getOrgId, orgId)
                        .orderByDesc(TbSampleResultRemark::getSampleResultRemarkId))
                .stream().map(sampleResultRemarkConvert::convert).collect(Collectors.toList());
    }

    @Override
    public List<SampleResultRemarkDto> selectByGroupId(long groupId) {
        return sampleResultRemarkMapper.selectList(new LambdaQueryWrapper<TbSampleResultRemark>()
                        .eq(TbSampleResultRemark::getGroupId, groupId)
                        .orderByDesc(TbSampleResultRemark::getSampleResultRemarkId))
                .stream().map(sampleResultRemarkConvert::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    public SampleResultRemarkDto selectBySampleResultRemarkId(long sampleResultRemarkId) {
        return sampleResultRemarkConvert.convert(sampleResultRemarkMapper.selectById(sampleResultRemarkId));
    }

    @Override
    public void deleteBySampleResultRemarkId(long sampleResultRemarkId) {
        sampleResultRemarkMapper.deleteById(sampleResultRemarkId);
        log.info("用户 [{}] 删除样本结果备注成功 [{}]", LoginUserHandler.get().getNickname(), sampleResultRemarkId);
    }

    @Override
    public boolean updateBySampleResultRemarkId(SampleResultRemarkDto dto) {


        final SampleResultRemarkDto old = selectBySampleResultRemarkId(dto.getSampleResultRemarkId());
        if (Objects.isNull(old)) {
            throw new IllegalArgumentException("结果备注不存在");
        }

        if (selectByGroupId(old.getGroupId()).stream()
                .anyMatch(e -> !Objects.equals(e.getSampleResultRemarkId(), dto.getSampleResultRemarkId())
                        && Objects.equals(e.getResultRemark(), dto.getResultRemark()))) {
            throw new IllegalArgumentException("结果备注已存在");
        }

        if (sampleResultRemarkMapper.updateById(sampleResultRemarkConvert.convert(dto)) > 0) {
            log.info("用户 [{}] 修改结果备注成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(dto));
            return true;
        }

        log.error("用户 [{}] 修改结果备注失败 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(dto));
        return false;
    }

    @Override
    public long addSampleResultRemark(SampleResultRemarkDto dto) {

        if (selectByGroupId(dto.getGroupId()).stream()
                .anyMatch(e -> Objects.equals(e.getResultRemark(), dto.getResultRemark()))) {
            throw new IllegalArgumentException("结果备注已存在");
        }

        final TbSampleResultRemark convert = sampleResultRemarkConvert.convert(dto);

        convert.setIsDelete(YesOrNoEnum.NO.getCode());
        convert.setCreateDate(new Date());
        convert.setUpdateDate(new Date());
        convert.setCreatorId(LoginUserHandler.get().getUserId());
        convert.setCreatorName(LoginUserHandler.get().getNickname());
        convert.setUpdaterId(LoginUserHandler.get().getUserId());
        convert.setUpdaterName(LoginUserHandler.get().getNickname());

        convert.setSampleResultRemarkId(Optional.ofNullable(convert.getSampleResultRemarkId()).orElseGet(() -> snowflakeService.genId()));

        sampleResultRemarkMapper.insert(convert);

        log.info("用户 [{}] 新增结果备注成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(convert));

        return convert.getSampleResultRemarkId();
    }
}
