package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 字典明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_dict_item")
public class TbDictItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典ID
     */
    @TableId
    private Long dictId;

    /**
     * 字典名称
     */
    private String dictName;

    /**
     * 字典说明
     */
    private String dictRemark;

    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 字典类型
     *
     * @see com.labway.lims.api.enums.base.DictEnum
     */
    private String dictType;

    /**
     * 字典快捷键
     */
    private String dictKeyshort;

    /**
     * 字典快捷键
     */
    private String dictPinyin;

    /**
     * 数据来源 1： 自行添加的数据 0： 同步的数据
     */
    private Integer dataSource;

    /**
     * 是否启动(0未启动 1启动)
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 属性1
     */
    private String extraParam1;

    /**
     * 属性2
     */
    private String extraParam2;

    /**
     * 属性3
     */
    private String extraParam3;
    /**
     * 逻辑删除字段
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
