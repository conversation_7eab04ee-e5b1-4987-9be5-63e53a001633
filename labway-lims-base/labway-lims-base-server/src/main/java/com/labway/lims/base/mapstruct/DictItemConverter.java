package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.model.TbDictItem;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 数据字典 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface DictItemConverter {

    DictItemDto dictItemDtoFromTbObj(TbDictItem obj);

    List<DictItemDto> dictItemDtoListFromTbObj(List<TbDictItem> list);

}
