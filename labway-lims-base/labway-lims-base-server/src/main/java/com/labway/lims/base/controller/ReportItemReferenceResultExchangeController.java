package com.labway.lims.base.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;
import com.labway.lims.base.api.service.InstrumentReportItemResultExchangeService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.vo.AddInstrumentReportItemResultExchangeVo;
import com.labway.lims.base.vo.UpdateInstrumentReportItemResultExchangeVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 结果值转换
 *
 * <AUTHOR>
 * @since 2023/3/20 14:29
 */
@RestController
@RequestMapping("/report-item-result-exchange")
public class ReportItemReferenceResultExchangeController extends BaseController {
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentReportItemResultExchangeService instrumentReportItemResultExchangeService;
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 获取结果值转换
     */
    @GetMapping("/exchanges")
    public Object exchanges(@RequestParam Long instrumentReportItemId) {
        if (Objects.isNull(instrumentReportItemId)) {

            throw new IllegalArgumentException("参数错误");
        }
        return instrumentReportItemResultExchangeService.selectByInstrumentReportItemId(instrumentReportItemId);
    }

    /**
     * 添加结果值转换
     */
    @PostMapping("/add")
    public Object add(@RequestBody AddInstrumentReportItemResultExchangeVo vo) {
        if (Objects.isNull(vo.getInstrumentReportItemId()) || StringUtils.isAnyBlank(vo.getInstrumentResult())) {
            throw new IllegalArgumentException("参数错误");
        }

        final InstrumentReportItemResultExchangeDto instrumentReportItemResultExchange = new InstrumentReportItemResultExchangeDto();
        BeanUtils.copyProperties(vo, instrumentReportItemResultExchange);

        return Map.of("id", instrumentReportItemResultExchangeService
                .addInstrumentReportItemResultExchange(instrumentReportItemResultExchange));
    }

    /**
     * 修改结果值转换
     */
    @PostMapping("/update")
    public Object update(@RequestBody UpdateInstrumentReportItemResultExchangeVo vo) {

        if (Objects.isNull(vo.getInstrumentReportItemResultExchangeId()) ||
                StringUtils.isAnyBlank(vo.getInstrumentResult())) {
            throw new IllegalArgumentException("参数错误");
        }


        final InstrumentReportItemResultExchangeDto instrumentReportItemResultExchangeOld =
                instrumentReportItemResultExchangeService.selectByInstrumentReportItemResultExchangeId(vo.getInstrumentReportItemResultExchangeId());
        if (Objects.isNull(instrumentReportItemResultExchangeOld)) {
            throw new IllegalStateException("结果值转换不存在");
        }

        final InstrumentReportItemResultExchangeDto instrumentReportItemResultExchange = new InstrumentReportItemResultExchangeDto();
        BeanUtils.copyProperties(vo, instrumentReportItemResultExchange);
        if (!instrumentReportItemResultExchangeService.updateByInstrumentReportItemResultExchangeId(instrumentReportItemResultExchange)) {
            throw new IllegalStateException("修改结果值转换失败");
        }

        final String compare = new CompareUtils<InstrumentReportItemResultExchangeDto>()
                .compare(instrumentReportItemResultExchangeOld, instrumentReportItemResultExchange);
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_RESULT_EXCHANGE.getDesc())
                        .setContent(
                                String.format("用户 [%s] 修改了仪器[%s]-报告项目[%s]的结果值转换，修改内容为: %s",
                                        LoginUserHandler.get().getNickname(),
                                        instrumentReportItemResultExchangeOld.getInstrumentName(),
                                        instrumentReportItemResultExchangeOld.getReportItemName(),
                                        compare
                                )
                        ).toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 删除结果值转换
     */
    @DeleteMapping("/delete")
    public Object delete(@RequestBody Set<Long> instrumentReportItemResultExchangeIds) {


        if (CollectionUtils.isEmpty(instrumentReportItemResultExchangeIds)) {
            return Collections.emptyMap();
        }

        final Map<Long, InstrumentReportItemResultExchangeDto> resultExchangeMap =
                instrumentReportItemResultExchangeService.selectByInstrumentReportItemResultExchangeIds(instrumentReportItemResultExchangeIds)
                        .stream()
                        .collect(Collectors.toMap(InstrumentReportItemResultExchangeDto::getInstrumentReportItemResultExchangeId, Function.identity(), (a, b) -> a));

        for (Long instrumentReportItemResultExchangeId : instrumentReportItemResultExchangeIds) {
            instrumentReportItemResultExchangeService.deleteByInstrumentReportItemResultExchangeId(instrumentReportItemResultExchangeId);

            final InstrumentReportItemResultExchangeDto instrumentReportItemResultExchange = resultExchangeMap.get(instrumentReportItemResultExchangeId);
            if (Objects.isNull(instrumentReportItemResultExchange)) {
                continue;
            }

            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_RESULT_EXCHANGE.getDesc())
                            .setContent(
                                    String.format("用户 [%s] 删除了仪器[%s]-报告项目[%s]的结果值转换，删除内容为: 仪器结果值 [%s] 转换结果值 [%s]",
                                            LoginUserHandler.get().getNickname(),
                                            instrumentReportItemResultExchange.getInstrumentName(),
                                            instrumentReportItemResultExchange.getReportItemName(),
                                            instrumentReportItemResultExchange.getInstrumentResult(),
                                            instrumentReportItemResultExchange.getExchangeResult()
                                    )
                            ).toJSONString());

        }

        return Collections.emptyMap();
    }


}
