package com.labway.lims.base.controller;

import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentMedicineDto;
import com.labway.lims.base.api.dto.MedicineDto;
import com.labway.lims.base.api.service.InstrumentMedicineService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.MedicineService;
import com.labway.lims.base.vo.InstrumentMedicineResponseVo;
import com.labway.lims.base.vo.UpdateInstrumentMedicineRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仪器药物维护API
 *
 * <AUTHOR>
 * @since 2023/7/12 17:26
 */
@Slf4j
@RestController
@RequestMapping("/instrument-medicine")
public class InstrumentMedicineController extends BaseController {

    @Resource
    private InstrumentMedicineService instrumentMedicineService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private MedicineService medicineService;

    @Resource
    private InstrumentService instrumentService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 仪器药物查看
     */
    @PostMapping("/select-by-instrument")
    public Object selectByInstrument(@RequestParam("instrumentId") long instrumentId) {
        LoginUserHandler.User user = LoginUserHandler.get();
        // 当前机构所有药物
        List<MedicineDto> medicineDtos = medicineService.selectByOrgId(user.getOrgId()).stream()
            .filter(f -> Objects.equals(f.getEnable(), YesOrNoEnum.YES.getCode())).collect(Collectors.toList());

        // 当前仪器维护药物 一个仪器下一个药物 只维护一条
        Map<Long, InstrumentMedicineDto> instrumentMedicineDtoByMedicineId =
            instrumentMedicineService.selectByInstrumentId(instrumentId).stream().collect(
                Collectors.toMap(InstrumentMedicineDto::getMedicineId, Function.identity(), (key1, ke2) -> key1));

        List<InstrumentMedicineResponseVo> targetList = Lists.newArrayListWithCapacity(medicineDtos.size());
        medicineDtos.forEach(item -> {
            InstrumentMedicineResponseVo vo = new InstrumentMedicineResponseVo();
            vo.setMedicineId(item.getMedicineId());
            vo.setMedicineCode(item.getMedicineCode());
            vo.setMedicineName(item.getMedicineName());
            InstrumentMedicineDto instrumentMedicineDto = instrumentMedicineDtoByMedicineId.get(item.getMedicineId());
            if (Objects.nonNull(instrumentMedicineDto)) {
                vo.setInstrumentMedicineId(instrumentMedicineDto.getInstrumentMedicineId());
                vo.setInstrumentChannel(instrumentMedicineDto.getInstrumentChannel());
                vo.setSendLims(instrumentMedicineDto.getSendLims());
            }
            targetList.add(vo);
        });
        return targetList;

    }

    /**
     * 仪器药物 保存
     */
    @PostMapping("/save")
    public Object saveInstrumentMedicine(@RequestBody UpdateInstrumentMedicineRequestVo vo) {
        final InstrumentDto instrument = instrumentService.selectByInstrumentId(vo.getInstrumentId());
        if (Objects.isNull(instrument)) {
            throw new IllegalArgumentException("仪器不存在");
        }

        final MedicineDto medicine = medicineService.selectByMedicineId(vo.getMedicineId());
        if (Objects.isNull(medicine)) {
            throw new IllegalArgumentException("药物不存在");
        }

        if (StringUtils.isNotBlank(vo.getInstrumentChannel())) {
            if (instrumentMedicineService.selectByInstrumentId(vo.getInstrumentId()).stream()
                .anyMatch(e -> Objects.equals(e.getInstrumentChannel(), vo.getInstrumentChannel())
                    && !Objects.equals(e.getMedicineId(), vo.getMedicineId()))) {
                throw new IllegalArgumentException(String.format("仪器通道号 [%s] 已经存在", vo.getInstrumentChannel()));
            }
        }
        // 原来的
        InstrumentMedicineDto sourceDto =
            instrumentMedicineService.selectByMedicineId(vo.getInstrumentId(), vo.getMedicineId());

        InstrumentMedicineDto old = new InstrumentMedicineDto();

        if (Objects.isNull(sourceDto)) {
            // 新增
            final InstrumentMedicineDto m = new InstrumentMedicineDto();
            m.setInstrumentMedicineId(snowflakeService.genId());
            m.setInstrumentId(instrument.getInstrumentId());
            m.setInstrumentCode(instrument.getInstrumentCode());
            m.setInstrumentName(instrument.getInstrumentName());
            m.setMedicineId(medicine.getMedicineId());
            m.setMedicineCode(medicine.getMedicineCode());
            m.setInstrumentChannel(vo.getInstrumentChannel());
            m.setSendLims(vo.getSendLims());
            m.setOrgId(LoginUserHandler.get().getOrgId());
            m.setOrgName(LoginUserHandler.get().getOrgName());
            m.setCreateDate(new Date());
            m.setUpdateDate(new Date());
            m.setUpdaterId(LoginUserHandler.get().getUserId());
            m.setUpdaterName(LoginUserHandler.get().getNickname());
            m.setCreatorId(LoginUserHandler.get().getUserId());
            m.setCreatorName(LoginUserHandler.get().getNickname());
            m.setIsDelete(YesOrNoEnum.NO.getCode());
            instrumentMedicineService.addInstrumentMedicines(Collections.singletonList(m));

        } else {
            InstrumentMedicineDto update = new InstrumentMedicineDto();
            update.setInstrumentMedicineId(sourceDto.getInstrumentMedicineId());
            update.setInstrumentChannel(vo.getInstrumentChannel());
            update.setSendLims(vo.getSendLims());
            instrumentMedicineService.updateByInstrumentMedicineId(update);

            old.setInstrumentChannel(sourceDto.getInstrumentChannel());
            old.setSendLims(sourceDto.getSendLims());
        }
        InstrumentMedicineDto now = new InstrumentMedicineDto();
        now.setInstrumentChannel(vo.getInstrumentChannel());
        now.setSendLims(vo.getSendLims());

        String compare = new CompareUtils<InstrumentMedicineDto>().compare(old, now);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_CHANNEL.getDesc())
                    .setContent(String.format("修改 [%s] 仪器下 [%s] 药物 : [%s]", instrument.getInstrumentName(),
                        medicine.getMedicineName(), compare))
                    .toJSONString());
        }
        return Collections.emptyMap();

    }
}
