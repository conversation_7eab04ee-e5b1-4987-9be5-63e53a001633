package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.MenuTypeCodeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.MenuDto;
import com.labway.lims.base.api.service.MenuService;
import com.labway.lims.base.mapper.TbMenuMapper;
import com.labway.lims.base.model.TbMenu;
import com.labway.lims.base.model.TbRoleMenu;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "menu")
public class MenuServiceImpl implements MenuService {
    @Resource
    private TbMenuMapper menuMapper;
    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private MenuService menuService;

    @Override
    @Cacheable(key = "'selectByMenuId:' + #menuId")
    public MenuDto selectByMenuId(long menuId) {
        return convert(menuMapper.selectById(menuId));
    }

    @Override
    @CacheEvict(allEntries = true)
    public long addMenu(MenuDto dto) {

        if (!StringUtils.equals(dto.getMenuTypeCode(), MenuTypeCodeEnum.MENU.name())) {
            if (Objects.isNull(dto.getProMenuId())) {
                throw new IllegalArgumentException("父级菜单不能为空");
            }
        }

        //新建的菜单的proMenuId是null或者为0的时候新建的是顶级菜单，那么只需判断名称即可
        if (Objects.isNull(dto.getProMenuId()) || Objects.equals(dto.getProMenuId(), NumberUtils.LONG_ZERO)) {
            //顶级菜单的proMenuId为 0L
            if (selectByProMenuId(NumberUtils.LONG_ZERO).stream().anyMatch(e -> StringUtils.equalsIgnoreCase(e.getMenuName(), dto.getMenuName()))) {
                throw new IllegalStateException("菜单名称已存在");
            }

        } else {
            final MenuDto menuDto = selectByMenuId(dto.getProMenuId());
            if (Objects.isNull(menuDto)) {
                throw new IllegalStateException("父级菜单不存在");
            }

            if (StringUtils.equalsIgnoreCase(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.MENU.name())) {
                if (StringUtils.equalsIgnoreCase(dto.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name())) {
                    throw new IllegalStateException("菜单下不能添加按钮");
                }
                if (selectByProMenuId(dto.getProMenuId()).stream().anyMatch(e -> StringUtils.equalsIgnoreCase(e.getMenuName(), dto.getMenuName())
                        && Objects.equals(dto.getMenuTypeCode(), e.getMenuTypeCode()))) {
                    throw new IllegalStateException("当前菜单下菜单或者页面名称已存在");
                }
            }

            if (StringUtils.equalsIgnoreCase(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.PAGE.name())) {

                if (!StringUtils.equalsIgnoreCase(dto.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name())) {
                    throw new IllegalStateException("页面下只能添加按钮");
                }

                if (selectByProMenuId(dto.getProMenuId()).stream().anyMatch(e -> StringUtils.equalsIgnoreCase(e.getMenuCode(), dto.getMenuCode()) &&
                        Objects.equals(dto.getMenuTypeCode(), e.getMenuTypeCode()))) {
                    throw new IllegalStateException("当前页面下的按钮ID重复");
                }
            }
        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        final TbMenu tbMenu = new TbMenu();
        BeanUtils.copyProperties(dto, tbMenu);

        tbMenu.setMenuId(ObjectUtils.defaultIfNull(dto.getMenuId(), snowflakeService.genId()));
        if (Objects.isNull(dto.getProMenuId()) || Objects.equals(dto.getProMenuId(), NumberUtils.LONG_ZERO)) {
            tbMenu.setProMenuId(NumberUtils.LONG_ZERO);
        }
        tbMenu.setMenuSort(ObjectUtils.defaultIfNull(dto.getMenuSort(), 0));
        tbMenu.setMenuCode(ObjectUtils.defaultIfNull(dto.getMenuCode(), ""));
        tbMenu.setCreatorId(user.getUserId());
        tbMenu.setCreatorName(user.getNickname());
        tbMenu.setCreateDate(new Date());
        tbMenu.setUpdaterName(user.getNickname());
        tbMenu.setUpdaterId(user.getUserId());
        tbMenu.setUpdateDate(new Date());
        tbMenu.setOrgId(user.getOrgId());
        tbMenu.setIsDelete(YesOrNoEnum.NO.getCode());

        if (!StringUtils.equalsIgnoreCase(dto.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name())) {
            tbMenu.setMenuCode(StringUtils.EMPTY);
        }

        if (menuMapper.insert(tbMenu) < 1) {
            throw new IllegalStateException("菜单添加失败");
        }

        log.info("用户 [{}] 添加菜单成功 [{}]", user.getNickname(), JSON.toJSONString(dto));

        return tbMenu.getMenuId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByMenuId(MenuDto dto) {

        //新建的菜单的proMenuId是null或者为0的时候新建的是顶级菜单，那么只需判断名称即可
        if (Objects.isNull(dto.getProMenuId()) || Objects.equals(dto.getProMenuId(), NumberUtils.LONG_ZERO)) {
            //顶级菜单的proMenuId为 0L
            if (selectByProMenuId(NumberUtils.LONG_ZERO).stream().filter(e -> !Objects.equals(e.getMenuId(), dto.getMenuId()))
                    .anyMatch(e -> StringUtils.equalsIgnoreCase(e.getMenuName(), dto.getMenuName()) && Objects.equals(dto.getMenuTypeCode(), e.getMenuTypeCode()))) {
                throw new IllegalStateException("菜单名称已存在,修改失败");
            }

        } else {
            final MenuDto menuDto = selectByMenuId(dto.getProMenuId());
            if (Objects.isNull(menuDto)) {
                throw new IllegalStateException("父级菜单不存在,修改失败");
            }

            if (StringUtils.equalsIgnoreCase(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.MENU.name())) {
                if (StringUtils.equalsIgnoreCase(dto.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name())) {
                    throw new IllegalStateException("菜单下不能添加按钮,修改失败");
                }
                if (selectByProMenuId(dto.getProMenuId()).stream().filter(e -> !Objects.equals(e.getMenuId(), dto.getMenuId()))
                        .anyMatch(e -> StringUtils.equalsIgnoreCase(e.getMenuName(), dto.getMenuName()) && Objects.equals(dto.getMenuTypeCode(), e.getMenuTypeCode()))) {
                    throw new IllegalStateException("当前菜单下菜单或者页面名称已存在,修改失败");
                }
            }

            if (StringUtils.equalsIgnoreCase(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.PAGE.name())) {

                if (!StringUtils.equalsIgnoreCase(dto.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name())) {
                    throw new IllegalStateException("页面下只能添加按钮,修改失败");
                }

                if (selectByProMenuId(dto.getProMenuId()).stream().anyMatch(e -> StringUtils.equalsIgnoreCase(e.getMenuCode(), dto.getMenuCode()))) {
                    throw new IllegalStateException("当前页面下的按钮ID重复,修改失败");
                }
            }
        }


        final TbMenu menu = JSON.parseObject(JSON.toJSONString(dto), TbMenu.class);
        final LoginUserHandler.User user = LoginUserHandler.get();
        menu.setUpdaterId(user.getUserId());
        menu.setUpdaterName(user.getNickname());
        menu.setUpdateDate(new Date());

        if (menuMapper.updateById(menu) < 1) {
            return false;
        }

        log.info("用户 [{}] 更新菜单成功 [{}]", user.getNickname(), JSON.toJSONString(dto));
        return true;
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByMenuId(long menuId) {
        return menuMapper.deleteById(menuId) > 0;
    }

    @Override
    @Nonnull
    @Cacheable(key = "'selectTreeMenuByRoleId:' + #roleId")
    public List<MenuDto> selectTreeMenuByRoleId(long roleId) {

        // 获取到这个角色的菜单
        final LinkedList<MenuDto> menus = selectByRoleId(roleId).stream()
                .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode())).collect(Collectors.toCollection(LinkedList::new));
        menus.removeIf(e -> Objects.equals(e.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name()));
        if (CollectionUtils.isEmpty(menus)) {
            return Collections.emptyList();
        }

        final LinkedHashMap<Long, MenuDto> map = menuService.selectAllMenu(menus.iterator().next().getOrgId())
                .stream()
                .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toMap(MenuDto::getMenuId, v -> v, (a, b) -> a, LinkedHashMap::new));
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyList();
        }

        for (var menu : menus) {
            map.put(menu.getMenuId(), menu);
        }

        final Set<MenuDto> trees = new LinkedHashSet<>(Math.max(menus.size() >> 2, 10));

        // 待查父菜单的菜单

        while (!menus.isEmpty()) {
            // 之所以从头部开始是为了保证 seq 的有序性
            MenuDto m = menus.removeFirst();

            MenuDto p = map.get(m.getProMenuId());

            if (Objects.isNull(p)) {
                // 有可能它本身就是一级菜单。
                if (Objects.equals(m.getProMenuId(), NumberUtils.LONG_ZERO)) {
                    trees.add(m);
                }
                continue;
            }

            if (Objects.isNull(p.getChildList())) {
                p.setChildList(new LinkedList<>());
            }

            // 如果存在则跳过
            if (p.getChildList().contains(m)) {
                continue;
            }

            p.getChildList().add(m);
            // 排序
            p.getChildList().sort(Comparator.comparing(MenuDto::getMenuSort));

            if (Objects.equals(m.getProMenuId(), NumberUtils.LONG_ZERO)) {
                trees.add(p);
            } else {
                // 如果父的菜单也需要找父，那么把这个也放入待查找父里面。
                // 之所以添加在头部是为了保证 seq 的有序性
                menus.addFirst(p);
            }
        }

        // 顶级菜单排序，因为每一个层级都会有一个最小的seq（树形结构导致的），导致sql查出来的时候最前面的可能不是最顶级的，导致顺序有误
        return trees.stream().sorted(Comparator.comparing(MenuDto::getMenuSort))
                .collect(Collectors.toCollection(LinkedList::new));
    }

    @Override
    @Nonnull
    public List<MenuDto> selectAllTreeMenu(long orgId) {
        //只处理菜单的
        final LinkedList<MenuDto> menus = menuService.selectAllMenu(orgId).stream().filter(e -> Objects.equals(e.getMenuTypeCode(),
                MenuTypeCodeEnum.MENU.name())).collect(Collectors.toCollection(LinkedList::new));
        if (CollectionUtils.isEmpty(menus)) {
            return Collections.emptyList();
        }

        final LinkedList<MenuDto> mList = new LinkedList<>();
        //找到所有一级菜单
        for (MenuDto menuDto : menus) {
            if (Objects.equals(menuDto.getProMenuId(), NumberUtils.LONG_ZERO)) {
                mList.add(menuDto);
            }
        }

        //为一级菜单设置子菜单(递归)
        for (MenuDto menu : mList) {
            //获取父菜单下的所有子菜单
            List<MenuDto> childList = getChildList(String.valueOf(menu.getMenuId()), menus);
            menu.setChildList(childList);
        }

        return mList;
    }

    @Override
    @Nonnull
    @Cacheable(key = "'selectAllMenu:' + #orgId")
    public List<MenuDto> selectAllMenu(long orgId) {
        final LambdaQueryWrapper<TbMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMenu::getOrgId, orgId)
                .orderByDesc(TbMenu::getMenuId);

        final List<TbMenu> tbMenus = menuMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(tbMenus)) {
            return Collections.emptyList();
        }
        return tbMenus.stream().map(this::convert).collect(Collectors.toList());
    }

    @Nonnull
    @Cacheable(key = "'selectByProMenuId:' + #menuId")
    @Override
    public List<MenuDto> selectByProMenuId(Long menuId) {
        final LambdaQueryWrapper<TbMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMenu::getProMenuId, menuId)
                .orderByDesc(TbMenu::getMenuId);

        return menuMapper.selectList(wrapper).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public List<MenuDto> selectByProMenuId(long orgId, Long menuId) {
        final LambdaQueryWrapper<TbMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMenu::getProMenuId, menuId)
                .eq(TbMenu::getOrgId, orgId)
                .orderByDesc(TbMenu::getMenuId);

        return menuMapper.selectList(wrapper).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByProMenuId(long id) {
        return menuMapper.delete(new LambdaQueryWrapper<TbMenu>().eq(TbMenu::getProMenuId, id)) > 0;
    }

    @Override
    @CacheEvict(allEntries = true)
    public void clearCaches() {

    }

    @Override
    @Nonnull
    public List<MenuDto> selectByRoleId(long roleId) {
        final LambdaQueryWrapper<TbRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbRoleMenu::getRoleId, roleId)
                .orderByDesc(TbRoleMenu::getMenuId);

        return menuMapper.selectByRoleId(roleId).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    /**
     * 递归查询子菜单
     */
    private List<MenuDto> getChildList(String menuId, LinkedList<MenuDto> menuList) {
        //构建子菜单
        final List<MenuDto> childList = new ArrayList<>();

        //遍历传入的list
        for (MenuDto menu : menuList) {
            //所有菜单的父id与传入的根节点id比较，若相等则为该级菜单的子菜单
            if (String.valueOf(menu.getProMenuId()).equals(menuId)) {
                childList.add(menu);
            }
        }

        //递归
        for (MenuDto m : childList) {
            m.setChildList(getChildList(String.valueOf(m.getMenuId()), menuList));
        }

        if (childList.isEmpty()) {
            return Collections.emptyList();
        }

        return childList;
    }


    private MenuDto convert(TbMenu menu) {
        if (Objects.isNull(menu)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(menu), MenuDto.class);
    }
}
