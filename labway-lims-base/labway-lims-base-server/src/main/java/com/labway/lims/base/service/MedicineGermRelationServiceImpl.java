package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.MedicineGermRelationDto;
import com.labway.lims.base.api.service.MedicineGermRelationService;
import com.labway.lims.base.mapper.TbMedicineGermRelationMapper;
import com.labway.lims.base.mapstruct.MedicineGermRelationConverter;
import com.labway.lims.base.model.TbMedicineGermRelation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 药物细菌关联表 Service impl
 * 
 * <AUTHOR>
 * @since 2023/4/21 9:12
 */
@Slf4j
@DubboService
public class MedicineGermRelationServiceImpl implements MedicineGermRelationService {

    @Resource
    private TbMedicineGermRelationMapper tbMedicineGermRelationMapper;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private MedicineGermRelationConverter medicineGermRelationConverter;

    @Override
    public long addMedicineGermRelationDto(MedicineGermRelationDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbMedicineGermRelation target = JSON.parseObject(JSON.toJSONString(dto), TbMedicineGermRelation.class);

        target.setRelationId(ObjectUtils.defaultIfNull(dto.getRelationId(), snowflakeService.genId()));
        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbMedicineGermRelationMapper.insert(target) < 1) {
            throw new LimsException("添加药物细菌菌属关联失败");
        }
        log.info("用户 [{}] 新增药物细菌菌属[{}]成功", loginUser.getNickname(), JSON.toJSONString(target));

        return target.getMedicineId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByMedicineIds(Collection<Long> medicineIds) {
        if (CollectionUtils.isEmpty(medicineIds)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        LambdaUpdateWrapper<TbMedicineGermRelation> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(TbMedicineGermRelation::getIsDelete, YesOrNoEnum.YES.getCode());
        updateWrapper.in(TbMedicineGermRelation::getMedicineId, medicineIds);;

        log.info("用户 [{}] 删除药物细菌菌属成功 [{}] 结果 [{}]", loginUser.getNickname(), medicineIds,
            tbMedicineGermRelationMapper.update(null, updateWrapper) > 0);

    }

    @Override
    public void deleteByRelationIds(Collection<Long> relationIds) {
        if (CollectionUtils.isEmpty(relationIds)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除药物细菌菌属成功 [{}] 结果 [{}]", loginUser.getNickname(), relationIds,
            tbMedicineGermRelationMapper.deleteBatchIds(relationIds) > 0);
    }

    @Override
    public List<MedicineGermRelationDto> selectByMedicineId(long medicineId) {

        LambdaQueryWrapper<TbMedicineGermRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMedicineGermRelation::getMedicineId, medicineId);
        queryWrapper.eq(TbMedicineGermRelation::getIsDelete, YesOrNoEnum.NO.getCode());
        return medicineGermRelationConverter
            .medicineGermRelationDtoListTbObj(tbMedicineGermRelationMapper.selectList(queryWrapper));
    }

    @Override
    public List<MedicineGermRelationDto> selectByMedicineIds(Collection<Long> medicineIds) {
        if (CollectionUtils.isEmpty(medicineIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMedicineGermRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMedicineGermRelation::getMedicineId, medicineIds);
        queryWrapper.eq(TbMedicineGermRelation::getIsDelete, YesOrNoEnum.NO.getCode());
        return medicineGermRelationConverter
            .medicineGermRelationDtoListTbObj(tbMedicineGermRelationMapper.selectList(queryWrapper));
    }

    @Override
    public List<MedicineGermRelationDto> selectByGermGenusIds(Collection<Long> germGenusIds) {
        if (CollectionUtils.isEmpty(germGenusIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMedicineGermRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMedicineGermRelation::getGermGenusId, germGenusIds);
        queryWrapper.eq(TbMedicineGermRelation::getIsDelete, YesOrNoEnum.NO.getCode());
        return medicineGermRelationConverter
            .medicineGermRelationDtoListTbObj(tbMedicineGermRelationMapper.selectList(queryWrapper));
    }

    @Override
    public List<MedicineGermRelationDto> selectByGermGenusId(long germGenusId) {
        LambdaQueryWrapper<TbMedicineGermRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMedicineGermRelation::getGermGenusId, germGenusId);
        queryWrapper.eq(TbMedicineGermRelation::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByAsc(TbMedicineGermRelation::getCreateDate);
        return medicineGermRelationConverter
            .medicineGermRelationDtoListTbObj(tbMedicineGermRelationMapper.selectList(queryWrapper));
    }

    @Override
    public List<MedicineGermRelationDto> selectByGermGenusIdAndMedicineId(long germGenusId, long medicineId) {
        LambdaQueryWrapper<TbMedicineGermRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMedicineGermRelation::getGermGenusId, germGenusId);
        queryWrapper.eq(TbMedicineGermRelation::getMedicineId, medicineId);
        queryWrapper.eq(TbMedicineGermRelation::getIsDelete, YesOrNoEnum.NO.getCode());
        return medicineGermRelationConverter
            .medicineGermRelationDtoListTbObj(tbMedicineGermRelationMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void medicineGermSave(List<MedicineGermRelationDto> addList, List<MedicineGermRelationDto> updateList) {
        addList.forEach(this::addMedicineGermRelationDto);
        updateList.forEach(this::updateByRelationId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByRelationId(MedicineGermRelationDto dto) {

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbMedicineGermRelation target = medicineGermRelationConverter.tbMedicineGermRelationFromTbObjDto(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbMedicineGermRelationMapper.updateById(target) < 1) {
            throw new LimsException("修改细菌药物失败");
        }

        log.info("用户 [{}] 修改细菌药物成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    public List<MedicineGermRelationDto> selectByRelationIds(Collection<Long> relationIds) {
        if (CollectionUtils.isEmpty(relationIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMedicineGermRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMedicineGermRelation::getRelationId, relationIds);
        queryWrapper.eq(TbMedicineGermRelation::getIsDelete, YesOrNoEnum.NO.getCode());
        return medicineGermRelationConverter
            .medicineGermRelationDtoListTbObj(tbMedicineGermRelationMapper.selectList(queryWrapper));
    }

}
