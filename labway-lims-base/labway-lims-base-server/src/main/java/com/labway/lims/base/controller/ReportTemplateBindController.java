package com.labway.lims.base.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ReportTemplateBindTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.*;
import com.labway.lims.base.vo.InstrumentGroupTemplateQueryVo;
import com.labway.lims.base.vo.ReportTemplateBindAddVo;
import com.labway.lims.base.vo.ReportTemplateBindUpdateVo;
import com.labway.lims.base.vo.ReportTemplateBindVo;
import com.labway.lims.pdfreport.api.dto.ReportTemplateDto;
import com.labway.lims.pdfreport.api.service.ReportTemplateService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/6/15 09:43
 */
@RestController
@RequestMapping("/report-template-bind")
public class ReportTemplateBindController extends BaseController {

    @Resource
    private ReportTemplateBindService reportTemplateBindService;
    @Resource
    private HspOrganizationService hspOrganizationService;
    @Resource
    private TestItemService testItemService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private InstrumentGroupService instrumentGroupService;
    @Resource
    private GroupService groupService;
    @DubboReference
    private ReportTemplateService reportTemplateService;

    @DubboReference
    private RabbitMQService rabbitMQService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @PostMapping("/bind-templates")
    public Object instrumentGroupTemplate(@RequestBody InstrumentGroupTemplateQueryVo vo)
        throws ExecutionException, InterruptedException, TimeoutException {

        // 根据类型搜索
        final List<ReportTemplateBindDto> binds = reportTemplateBindService.selectByBindType(vo.getBindType(),null);

        //根据BindGroupId进行分组
        Map<Long,List<ReportTemplateBindDto>> bindMap = binds.stream().collect(Collectors.groupingBy(ReportTemplateBindDto::getBindGroupId));

        //查询送检机构、专业小组、检验项目集合
        Set<Long> hspOrgs = new HashSet<>();
        Set<Long> instrumentGourps = new HashSet<>();
        Set<Long> testItems = new HashSet<>();

        for (ReportTemplateBindDto bindDto:binds){
            //送检机构不为空 并且不是通用的ID 则进行ID查询
            if (Objects.nonNull(bindDto.getHspOrgId()) && !Objects.equals(NumberUtils.LONG_ZERO,bindDto.getHspOrgId())){
                hspOrgs.add(bindDto.getHspOrgId());
            }

            //专业小组不为空 并且不是通用的ID 则进行ID查询
            if (Objects.nonNull(bindDto.getInstrumentGroupId()) && !Objects.equals(NumberUtils.LONG_ZERO,bindDto.getInstrumentGroupId())){
                instrumentGourps.add(bindDto.getInstrumentGroupId());
            }

            //检验不为空 则进行ID查询
            if (Objects.equals(vo.getBindType(),ReportTemplateBindTypeEnum.TEST_ITEM) && Objects.nonNull(bindDto.getBizId())){
                testItems.add(bindDto.getBizId());
            }
        }


        // future 查询 检验项目
        final Future<List<TestItemDto>> testItemList = threadPoolConfig.getPool()
                .submit(() -> testItemService.selectByTestItemIds(testItems));

        // future 查询 模板信息
        final Future<Map<String, List<ReportTemplateDto>>> templateMap =
            threadPoolConfig.getPool().submit(() -> reportTemplateService.selectByReportTemplateCodes(
                binds.stream().map(ReportTemplateBindDto::getReportTemplateCode).collect(Collectors.toList())));

        Map<Long,TestItemDto> testItemMap = testItemList.get().stream().collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));
        final LinkedList<ReportTemplateBindVo> vos = new LinkedList<>();
        for (Long bindGroupId : bindMap.keySet()) {
            List<ReportTemplateBindDto> bindDtoList = bindMap.get(bindGroupId);

            final ReportTemplateBindVo rtb = new ReportTemplateBindVo();
            BeanUtils.copyProperties(bindDtoList.get(0), rtb);
            //只有检验项目的需要进行匹配名字 其他直接进行拼接组装即可
            //送检机构ID集合、专业小组ID集合、检验项目ID集合
            StringBuilder hspIds = new StringBuilder();
            StringBuilder instrumentGroupIds = new StringBuilder();
            StringBuilder testItemIds = new StringBuilder();
            //名称集合
            StringBuilder hspNames = new StringBuilder();
            StringBuilder instrumentGroupNames = new StringBuilder();
            StringBuilder testItemNames = new StringBuilder();
            for (ReportTemplateBindDto bindDto:bindDtoList){
                //判断送检机构
                if (Objects.equals(NumberUtils.LONG_ZERO,bindDtoList.get(0).getHspOrgId())){
                    //通用的话 不用add 直接赋值即可
                    if (hspIds.length() == 0){
                        hspIds.append(NumberUtils.LONG_ZERO).append(",");
                        hspNames.append("通用").append(",");
                    }
                }else if (!hspIds.toString().contains(String.valueOf(bindDto.getHspOrgId()))){
                    hspIds.append(bindDto.getHspOrgId()).append(",");
                    hspNames.append(bindDto.getHspOrgName()).append(",");
                }

                //判断专业小组
                if (Objects.equals(NumberUtils.LONG_ZERO,bindDtoList.get(0).getInstrumentGroupId())){
                    //通用的话 不用add 直接赋值即可
                    if (instrumentGroupIds.length() == 0){
                        instrumentGroupIds.append(NumberUtils.LONG_ZERO).append(",");
                        instrumentGroupNames.append("通用").append(",");
                    }
                }else if (!instrumentGroupIds.toString().contains(String.valueOf(bindDto.getInstrumentGroupId()))){
                    instrumentGroupIds.append(bindDto.getInstrumentGroupId()).append(",");
                    instrumentGroupNames.append(bindDto.getInstrumentGroupName()).append(",");
                }

                //判断检验项目
                if (Objects.equals(vo.getBindType(), ReportTemplateBindTypeEnum.TEST_ITEM) &&
                        !testItemIds.toString().contains(String.valueOf(bindDto.getBizId()))){
                    testItemIds.append(bindDto.getBizId()).append(",");
                    testItemNames.append(testItemMap.containsKey(bindDto.getBizId())?testItemMap.get(bindDto.getBizId()).getTestItemName()
                            :StringUtils.EMPTY).append(",");
                }
            }
            //去除最后的逗号
            if (hspIds.length() > 0){
                hspIds.deleteCharAt(hspIds.length()-1);
                hspNames.deleteCharAt(hspNames.length() - 1);
            }

            if (instrumentGroupIds.length() > 0){
                instrumentGroupIds.deleteCharAt(instrumentGroupIds.length()-1);
                instrumentGroupNames.deleteCharAt(instrumentGroupNames.length()-1);
            }

            if (testItemIds.length() > 0){
                testItemIds.deleteCharAt(testItemIds.length()-1);
                testItemNames.deleteCharAt(testItemNames.length() - 1);
            }

            rtb.setHspOrgIds(hspIds.toString());
            rtb.setInstrumentGroupIds(instrumentGroupIds.toString());
            rtb.setTestItemIds(testItemIds.toString());
            rtb.setHspOrgNames(hspNames.toString());
            rtb.setInstrumentGroupNames(instrumentGroupNames.toString());
            rtb.setTestItemNames(testItemNames.toString());

            // 模板文件
            final Map<String, List<ReportTemplateDto>> report = templateMap.get(10, TimeUnit.SECONDS);
            final List<ReportTemplateDto> templates = report.get(bindDtoList.get(0).getReportTemplateCode());
            if (CollectionUtils.isNotEmpty(templates)) {
                templates.sort((o1, o2) -> o2.getReportTemplateId().compareTo(o1.getReportTemplateId()));
                rtb.setTemplateFile(templates.iterator().next().getReportTemplateName());
            }
            rtb.setBindType(ReportTemplateBindTypeEnum.getByCode(bindDtoList.get(0).getBindType()));

            vos.add(rtb);
        }

        return vos;
    }

    @PostMapping("/add")
    public Object add(@RequestBody ReportTemplateBindAddVo vo) {

        if (Objects.isNull(vo.getEnable())) {
            throw new IllegalStateException("缺少必填参数");
        }

        if (StringUtils.isBlank(vo.getReportTemplateName())) {
            throw new IllegalStateException("模板名称不能为空");
        }
        if (StringUtils.length(vo.getReportTemplateName()) > INPUT_MAX_LENGTH) {
            throw new IllegalStateException("模板名称不能超过50个字符");
        }
        ProfessionalGroupDto group = Objects.isNull(vo.getGroupId())?null:groupService.selectByGroupId(vo.getGroupId());
        //判断唯一值
        checkUnique(vo,group);

        List<ReportTemplateBindDto> addList = createTmpBindDtoList(vo,group);
        reportTemplateBindService.addBatchReportTemplateBind(addList);
        
        // 记录日志
        final String bindType =
            Optional.ofNullable(vo.getBindType()).map(ReportTemplateBindTypeEnum::getDesc).orElse(StringUtils.EMPTY);
        final String logContent = String.format(
            "新增报告模板绑定，模板名称：[%s] 模板文件编码：[%s] 绑定类型：[%s] "
                + "专业组：[%s] 专业小组：[%s] 送检机构：[%s] 检验项目 [%s] 是否启用 [%s]",
            vo.getReportTemplateName(), vo.getReportTemplateCode(), bindType, group == null?"":group.getGroupName(),
            vo.getInstrumentGroupNames(), vo.getHspOrgNames(), vo.getTestItemNames(),
            YesOrNoEnum.selectByCode(vo.getEnable()).getDesc());

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY, TraceLog.newInstance()
            .setModule(TraceLogModuleEnum.REPORT_TEMPLATE.getDesc()).setContent(logContent).toJSONString());

        return Map.of();
    }

    @PostMapping("/update")
    public Object update(@RequestBody ReportTemplateBindUpdateVo vo) {
        if (Objects.isNull(vo.getEnable()) || Objects.isNull(vo.getBindGroupId())) {
            throw new IllegalStateException("缺少必填参数");
        }

        if (StringUtils.isBlank(vo.getReportTemplateName())) {
            throw new IllegalStateException("模板名称不能为空");
        }
        if (StringUtils.length(vo.getReportTemplateName()) > INPUT_MAX_LENGTH) {
            throw new IllegalStateException("模板名称不能超过50个字符");
        }

        ProfessionalGroupDto group = Objects.isNull(vo.getGroupId())?null:groupService.selectByGroupId(vo.getGroupId());
        //判断唯一值
        checkUnique(vo,group);

        List<ReportTemplateBindDto> addList = createTmpBindDtoList(vo,group);
        //先删除原来的结果 再新增所有新的记录
        reportTemplateBindService.updateReportTemplateBinds(addList,addList.get(0).getBindGroupId());

        // 记录日志
        final String bindType =
                Optional.ofNullable(vo.getBindType()).map(ReportTemplateBindTypeEnum::getDesc).orElse(StringUtils.EMPTY);
        final String logContent = String.format(
                "新增报告模板绑定，模板名称：[%s] 模板文件编码：[%s] 绑定类型：[%s] "
                        + "专业组：[%s] 专业小组：[%s] 送检机构：[%s] 检验项目 [%s] 是否启用 [%s]",
                vo.getReportTemplateName(), vo.getReportTemplateCode(), bindType, group == null?"":group.getGroupName(),
                vo.getInstrumentGroupNames(), vo.getHspOrgNames(), vo.getTestItemNames(),
                YesOrNoEnum.selectByCode(vo.getEnable()).getDesc());

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY, TraceLog.newInstance()
                .setModule(TraceLogModuleEnum.REPORT_TEMPLATE.getDesc()).setContent(logContent).toJSONString());

        return Map.of();
    }

    @PostMapping("/delete")
    public Object delete(@RequestBody List<Long> bindGroupIds) {
        if (CollectionUtils.isEmpty(bindGroupIds)) {
            throw new IllegalArgumentException("请选择绑定模板记录");
        }

        final List<ReportTemplateBindDto> reportTemplateBinds =
            reportTemplateBindService.selectByBindGroupIds(bindGroupIds);
        if (CollectionUtils.isEmpty(reportTemplateBinds)) {
            throw new IllegalStateException("模板不存在");
        }

        reportTemplateBindService.deleteByBindGroupIds(bindGroupIds);

        final String bindType =
            reportTemplateBinds.stream().findFirst().map(m -> ReportTemplateBindTypeEnum.getByCode(m.getBindType()))
                .map(ReportTemplateBindTypeEnum::getDesc).orElse(StringUtils.EMPTY);
        String logContent =
            String.format("用户 [%s] 删除了报告模板 [%s] 模板类型 [%s] ", LoginUserHandler.get().getNickname(), reportTemplateBinds
                .stream().map(ReportTemplateBindDto::getReportTemplateName).collect(Collectors.joining(",")), bindType);

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY, TraceLog.newInstance()
            .setModule(TraceLogModuleEnum.REPORT_TEMPLATE.getDesc()).setContent(logContent).toJSONString());

        return Map.of();
    }

    private void checkUnique(ReportTemplateBindAddVo vo,ProfessionalGroupDto group){
        List<InstrumentGroupDto> instrumentGroups = null;
        List<HspOrganizationDto> hspOrganizationDtos = null;
        //专业小组是否通用
        boolean isCommonInstrumentGroup = vo.isCommonInstrumentGroup();
        //送检机构是否通用
        boolean isCommonHspOrg = vo.isCommonHspOrg();

        // 检验项目模板绑定无需判断专业小组
        if (!Objects.equals(vo.getBindType(), ReportTemplateBindTypeEnum.TEST_ITEM)) {
            if (CollectionUtils.isEmpty(vo.getInstrumentGroupIds())) {
                throw new IllegalStateException("专业小组不能为空");
            }

            if (!isCommonInstrumentGroup){
                //查询专业小组是否存在
                instrumentGroups = instrumentGroupService.selectByInstrumentGroupIds(vo.getInstrumentGroupIds());

                if (CollectionUtils.isEmpty(instrumentGroups) || instrumentGroups.size() != vo.getInstrumentGroupIds().size()) {
                    throw new IllegalStateException("所选专业小组不存在");
                }
                if (Objects.isNull(group)) {
                    throw new IllegalStateException("所选专业组不存在");
                }
                //这里取第一个专业小组的专业组ID进行比对
                if (!Objects.equals(vo.getGroupId(), instrumentGroups.get(0).getGroupId())) {
                    throw new IllegalStateException("当前专业小组不在当前所选专业组下");
                }
            }
        }else {
            //检验项目要判断送检机构信息
            if (!isCommonHspOrg){
                hspOrganizationDtos = hspOrganizationService.selectByHspOrgIds(vo.getHspOrgIds());

                if (CollectionUtils.isEmpty(hspOrganizationDtos) || hspOrganizationDtos.size() != vo.getHspOrgIds().size()) {
                    throw new IllegalStateException("所选送检机构不存在");
                }
            }
        }

        //这里的逻辑判断要获取全部数据 来进行比对 如果是更新 则排除掉自己
        Long excludeId = vo instanceof ReportTemplateBindUpdateVo?((ReportTemplateBindUpdateVo) vo).getBindGroupId():null;
        final List<ReportTemplateBindDto> templates = reportTemplateBindService.selectByBindType(vo.getBindType(),excludeId);

        if (Objects.equals(vo.getBindType(), ReportTemplateBindTypeEnum.HSP_ORG)) {
            // 送检机构模板绑定，一个送检机构下的专业小组只能绑定一个模板
            if (CollectionUtils.isEmpty(vo.getHspOrgIds())) {
                throw new IllegalStateException("送检机构不能为空");
            }
            //非通用专业小组需要进行真实性判断
            if (!isCommonInstrumentGroup && Objects.isNull(instrumentGroups)) {
                throw new IllegalStateException("专业小组不存在");
            }

            //送检机构+专业小组 判断重复逻辑
            Long dupOrgId = null;
            Long dupInstrumentGroupId = null;
            OUTPUT:
            for (Long orgId:vo.getHspOrgIds()){
                for (Long instrumentGroupId:vo.getInstrumentGroupIds()){
                    for (ReportTemplateBindDto bindDto:templates){
                        if (orgId.equals(bindDto.getHspOrgId()) && !isCommonInstrumentGroup && instrumentGroupId.equals(bindDto.getInstrumentGroupId())){
                            dupOrgId = orgId;
                            dupInstrumentGroupId = instrumentGroupId;
                            break OUTPUT;
                        }
                    }
                }
            }
            if (!Objects.isNull(dupOrgId)){
                //报错 重复了
                HspOrganizationDto hspOrganizationDto = hspOrganizationService.selectByHspOrgId(dupOrgId);
                InstrumentGroupDto instrumentGroupDto = isCommonInstrumentGroup?null:instrumentGroupService.selectByInstrumentGroupId(dupInstrumentGroupId);
                String errMsg  = "添加失败，有重复数据";
                if (hspOrganizationDto != null && instrumentGroupDto != null && group != null){
                    errMsg  = String.format("[%s]送检机构下[%s]专业组[%s]专业小组已存在模板，不可重复维护",hspOrganizationDto.getHspOrgName(),
                            group.getGroupName(),instrumentGroupDto.getInstrumentGroupName());

                }else if (isCommonInstrumentGroup && hspOrganizationDto!=null && group != null){
                    errMsg  = String.format("[%s]送检机构下[%s]专业组[通用]专业小组已存在模板，不可重复维护",hspOrganizationDto.getHspOrgName(),
                            group.getGroupName());
                }
                throw new IllegalArgumentException(errMsg);
            }
        } else if (Objects.equals(vo.getBindType(), ReportTemplateBindTypeEnum.TEST_ITEM)) {
            if (CollectionUtils.isEmpty(vo.getTestItemIds())) {
                throw new IllegalStateException("检验项目不能为空");
            }
            //检验项目模板绑定，一个送检机构下面 一个检验项目只能绑定一个模板
            //送检机构+检验项目 判断重复逻辑
            Long dupOrgId = null;
            Long dupTestItemId = null;
            OUTPUT:
            for (Long orgId:vo.getHspOrgIds()){
                for (Long testItemId:vo.getTestItemIds()){
                    for (ReportTemplateBindDto bindDto:templates){
                        if (orgId.equals(bindDto.getHspOrgId()) && testItemId.equals(bindDto.getBizId())){
                            dupOrgId = orgId;
                            dupTestItemId = testItemId;
                            break OUTPUT;
                        }
                    }
                }
            }
            if (!Objects.isNull(dupOrgId)){
                //报错 重复了
                HspOrganizationDto hspOrganizationDto = isCommonHspOrg?null:hspOrganizationService.selectByHspOrgId(dupOrgId);
                TestItemDto testItemDto = testItemService.selectByTestItemId(dupTestItemId);
                String errMsg  = "添加失败，有重复数据";
                if (hspOrganizationDto != null && testItemDto != null){
                    errMsg  = String.format("[%s]送检机构下[%s]检验项目已存在模板，不可重复维护",hspOrganizationDto.getHspOrgName(),
                            testItemDto.getTestItemName());
                }else if (isCommonHspOrg && testItemDto != null){
                    errMsg  = String.format("[通用]送检机构下[%s]检验项目已存在模板，不可重复维护", testItemDto.getTestItemName());
                }
                throw new IllegalArgumentException(errMsg);
            }
        } else if (Objects.equals(vo.getBindType(), ReportTemplateBindTypeEnum.PROFESSIONAL_GROUP)) {
            // 专业小组模板绑定，一个专业小组只能绑定一个模板
            if (!isCommonInstrumentGroup&&Objects.isNull(instrumentGroups)) {
                throw new IllegalStateException("专业小组不能为空");
            }
            //专业组+专业小组 唯一
            Long dupInstrumentGroupId = null;
            OUTPUT:
            for (Long instrumentGourpId:vo.getInstrumentGroupIds()){
                for (ReportTemplateBindDto bindDto:templates){
                    if (instrumentGourpId.equals(bindDto.getInstrumentGroupId())
                            && bindDto.getGroupId().equals(vo.getGroupId())){
                        dupInstrumentGroupId = instrumentGourpId;
                        break OUTPUT;
                    }
                }
            }

            if (!Objects.isNull(dupInstrumentGroupId)){
                InstrumentGroupDto instrumentGroupDto = isCommonInstrumentGroup?null:instrumentGroupService.selectByInstrumentGroupId(dupInstrumentGroupId);
                String errMsg  = "添加失败，有重复数据";
                if (instrumentGroupDto!= null ){
                    errMsg  = String.format("[%s]专业组[%s]专业小组已存在模板，不可重复维护",group.getGroupName(),
                            instrumentGroupDto.getInstrumentGroupName());
                }else if (isCommonInstrumentGroup){
                    errMsg  = String.format("[%s]专业组[通用]专业小组已存在模板，不可重复维护", group.getGroupName());
                }
                throw new IllegalArgumentException(errMsg);
            }
        }
    }
    
    private List<ReportTemplateBindDto> createTmpBindDtoList(ReportTemplateBindAddVo vo,ProfessionalGroupDto group){
        //生成绑定ID  如果是更新 则直接用原来的ID
        long bindGroupId = vo instanceof ReportTemplateBindUpdateVo?((ReportTemplateBindUpdateVo) vo).getBindGroupId()
                :snowflakeService.genId();

        List<ReportTemplateBindDto> bindList = new ArrayList<>();
        //这里做笛卡尔乘积
        if (Objects.equals(vo.getBindType(), ReportTemplateBindTypeEnum.PROFESSIONAL_GROUP)){
            //多专业小组
            Map<Long,InstrumentGroupDto> groupDtoMap = instrumentGroupService.selectByInstrumentGroupIds(vo.getInstrumentGroupIds())
                    .stream().collect(Collectors.toMap(InstrumentGroupDto::getInstrumentGroupId, Function.identity()));
            for (Long instrumentGroupId:vo.getInstrumentGroupIds()){
                final ReportTemplateBindDto dto = new ReportTemplateBindDto();
                dto.setHspOrgId(NumberUtils.LONG_ZERO);
                dto.setHspOrgName(StringUtils.EMPTY);
                dto.setInstrumentGroupId(instrumentGroupId);
                if (vo.isCommonInstrumentGroup()){
                    dto.setInstrumentGroupName("通用");
                }else {
                    dto.setInstrumentGroupName(groupDtoMap.containsKey(instrumentGroupId)?
                            groupDtoMap.get(instrumentGroupId).getInstrumentGroupName():StringUtils.EMPTY);
                }

                if (Objects.isNull(group)) {
                    dto.setGroupId(NumberUtils.LONG_ZERO);
                    dto.setGroupName(StringUtils.EMPTY);
                } else {
                    dto.setGroupId(group.getGroupId());
                    dto.setGroupName(group.getGroupName());
                }

                dto.setReportTemplateName(vo.getReportTemplateName());
                dto.setReportTemplateCode(vo.getReportTemplateCode());
                dto.setBindType(vo.getBindType().getCode());
                dto.setEnable(vo.getEnable());
                dto.setBizId(instrumentGroupId);
                dto.setBindGroupId(bindGroupId);
                bindList.add(dto);
            }

            return bindList;
        }

        if (Objects.equals(vo.getBindType(), ReportTemplateBindTypeEnum.HSP_ORG)){
            //多机构
            Map<Long,HspOrganizationDto> hspOrgMap = hspOrganizationService.selectByHspOrgIds(vo.getHspOrgIds())
                    .stream().collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity()));
            //多专业小组
            Map<Long,InstrumentGroupDto> groupDtoMap = instrumentGroupService.selectByInstrumentGroupIds(vo.getInstrumentGroupIds())
                    .stream().collect(Collectors.toMap(InstrumentGroupDto::getInstrumentGroupId, Function.identity()));
            for (Long hspOrgId: vo.getHspOrgIds()){
                for (Long instrumentGroupId: vo.getInstrumentGroupIds()){
                    final ReportTemplateBindDto dto = new ReportTemplateBindDto();
                    dto.setHspOrgId(hspOrgId);
                    dto.setHspOrgName(hspOrgMap.get(hspOrgId).getHspOrgName());

                    dto.setInstrumentGroupId(instrumentGroupId);
                    if (vo.isCommonInstrumentGroup()){
                        dto.setInstrumentGroupName("通用");
                    }else {
                        dto.setInstrumentGroupName(groupDtoMap.containsKey(instrumentGroupId)?
                                groupDtoMap.get(instrumentGroupId).getInstrumentGroupName():StringUtils.EMPTY);
                    }

                    if (Objects.isNull(group)) {
                        dto.setGroupId(NumberUtils.LONG_ZERO);
                        dto.setGroupName(StringUtils.EMPTY);
                    } else {
                        dto.setGroupId(group.getGroupId());
                        dto.setGroupName(group.getGroupName());
                    }

                    dto.setReportTemplateName(vo.getReportTemplateName());
                    dto.setReportTemplateCode(vo.getReportTemplateCode());
                    dto.setBindType(vo.getBindType().getCode());
                    dto.setEnable(vo.getEnable());
                    dto.setBizId(hspOrgId);
                    dto.setBindGroupId(bindGroupId);
                    bindList.add(dto);
                }
            }

            return bindList;
        }

        if (Objects.equals(vo.getBindType(), ReportTemplateBindTypeEnum.TEST_ITEM)){
            //多机构
            Map<Long,HspOrganizationDto> hspOrgMap = hspOrganizationService.selectByHspOrgIds(vo.getHspOrgIds())
                    .stream().collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity()));
            for (Long hspOrgId: vo.getHspOrgIds()){
                for (Long testItemId: vo.getTestItemIds()){
                    final ReportTemplateBindDto dto = new ReportTemplateBindDto();
                    dto.setHspOrgId(hspOrgId);
                    if (vo.isCommonHspOrg()){
                        dto.setHspOrgName("通用");
                    }else {
                        dto.setHspOrgName(hspOrgMap.containsKey(hspOrgId)?hspOrgMap.get(hspOrgId).getHspOrgName():StringUtils.EMPTY);
                    }

                    if (Objects.isNull(group)) {
                        dto.setGroupId(NumberUtils.LONG_ZERO);
                        dto.setGroupName(StringUtils.EMPTY);
                    } else {
                        dto.setGroupId(group.getGroupId());
                        dto.setGroupName(group.getGroupName());
                    }

                    dto.setInstrumentGroupId(NumberUtils.LONG_ZERO);
                    dto.setInstrumentGroupName(StringUtils.EMPTY);
                    dto.setReportTemplateName(vo.getReportTemplateName());
                    dto.setReportTemplateCode(vo.getReportTemplateCode());
                    dto.setBindType(vo.getBindType().getCode());
                    dto.setBizId(testItemId);
                    dto.setEnable(vo.getEnable());
                    dto.setBindGroupId(bindGroupId);
                    bindList.add(dto);
                }
            }

            return bindList;
        }
        
        return bindList;
    }

}
