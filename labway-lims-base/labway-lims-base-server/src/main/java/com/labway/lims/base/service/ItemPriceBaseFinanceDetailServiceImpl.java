package com.labway.lims.base.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.CombinePackageInfoDto;
import com.labway.lims.base.api.dto.ItemPriceBaseFinanceDetailDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.ItemPriceBaseFinanceDetailService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.api.service.ItemPriceCombinePackageDetailService;
import com.labway.lims.base.api.service.ItemPriceCombinePackageService;
import com.labway.lims.base.api.vo.ItemPriceBaseFinanceDetailVo;
import com.labway.lims.base.api.vo.QueryCombinePackageListTestItemsVo;
import com.labway.lims.base.mapper.ItemPriceBaseFinanceDetailMapper;
import com.labway.lims.base.model.TbItemPriceBaseFinanceDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@DubboService
public class ItemPriceBaseFinanceDetailServiceImpl extends ServiceImpl<ItemPriceBaseFinanceDetailMapper, TbItemPriceBaseFinanceDetail> implements ItemPriceBaseFinanceDetailService {

    @Resource
    private ItemPriceBasePackageService itemPriceBasePackageService;
    @Resource
    private ItemPriceCombinePackageService itemPriceCombinePackageService;
    @Resource
    private ItemPriceCombinePackageDetailService itemPriceCombinePackageDetailService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean add(ItemPriceBaseFinanceDetailDto dto) {

        final ItemPriceBasePackageDto itemPriceBasePackageDto = itemPriceBasePackageService.selectById(dto.getPackageId());
        if (Objects.isNull(itemPriceBasePackageDto)) {
            throw new IllegalStateException("项目价格基准包不存在");
        }

        // 查询财务套餐信息
        List<String> combinePackageCodes = dto.getCombinePackageCodes();
        final Map<String, CombinePackageInfoDto> combinePackageInfoMap = itemPriceCombinePackageService.queryCombinePackageByCodes(combinePackageCodes)
                .stream().collect(Collectors.toMap(CombinePackageInfoDto::getCombinePackageCode, Function.identity(), (a, b) -> b));

        // 判断是否存在
        final List<String> collect = combinePackageCodes.stream().filter(e -> Objects.nonNull(combinePackageInfoMap.get(e))).collect(Collectors.toList());
        combinePackageCodes.removeAll(collect);
        if (CollectionUtils.isNotEmpty(combinePackageCodes)) {
            throw new IllegalArgumentException(String.format("财务套餐编码：%s 无效", CollUtil.join(combinePackageCodes, ",")));
        }

        final List<TbItemPriceBaseFinanceDetail> tbItemPriceBaseFinanceDetailList = combinePackageInfoMap.values().stream()
                .map(combinePackage -> {
                    final TbItemPriceBaseFinanceDetail tb = this.defaultTbFinanceDetail();
                    tb.setPackageId(dto.getPackageId());
                    tb.setCombinePackageCode(combinePackage.getCombinePackageCode());
                    return tb;
                }).collect(Collectors.toList());

        return super.saveBatch(tbItemPriceBaseFinanceDetailList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteByFinanceDetailIds(Collection<Long> financeDetailIds) {
        if (CollectionUtils.isEmpty(financeDetailIds)) {
            return false;
        }
        return super.removeBatchByIds(financeDetailIds);
    }

    @Override
    public List<CombinePackageInfoDto> selectByPackageId(long packageId) {
        // 查询项目下的财务套餐包的 ids
        final List<String> combinePackageCodes = baseMapper.selectList(new LambdaQueryWrapper<TbItemPriceBaseFinanceDetail>()
                        .select(TbItemPriceBaseFinanceDetail::getCombinePackageCode)
                        .eq(TbItemPriceBaseFinanceDetail::getPackageId, packageId))
                .stream().map(TbItemPriceBaseFinanceDetail::getCombinePackageCode).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(combinePackageCodes)) {
            return Collections.emptyList();
        }

        // 查询财务套餐
        return itemPriceCombinePackageService.queryCombinePackageByCodes(combinePackageCodes);

    }

    @Override
    public Map<Long, List<QueryCombinePackageListTestItemsVo>> selectByPackageIds(Collection<Long> packageIds) {
        if (CollectionUtils.isEmpty(packageIds)) {
            return Map.of();
        }
        // 查询项目下的财务套餐包的 ids
        final Map<Long, List<String>> packageIdCombinePackageCodeMap = baseMapper.selectList(new LambdaQueryWrapper<TbItemPriceBaseFinanceDetail>()
                        .select(TbItemPriceBaseFinanceDetail::getCombinePackageCode, TbItemPriceBaseFinanceDetail::getPackageId)
                        .in(TbItemPriceBaseFinanceDetail::getPackageId, packageIds))
                .stream().collect(Collectors.groupingBy(TbItemPriceBaseFinanceDetail::getPackageId,
                        Collectors.mapping(TbItemPriceBaseFinanceDetail::getCombinePackageCode, Collectors.toList())));

        return getCombinePackageMap(packageIdCombinePackageCodeMap);
    }

    @Override
    public Map<Long, List<CombinePackageInfoDto>> selectBaseFinanceDetailAllMap() {
        // 查询项目下的财务套餐包的 ids
        final Map<Long, List<String>> packageIdCombinePackageCodeMap = baseMapper.selectList(new LambdaQueryWrapper<TbItemPriceBaseFinanceDetail>()
                        .select(TbItemPriceBaseFinanceDetail::getCombinePackageCode, TbItemPriceBaseFinanceDetail::getPackageId))
                .stream().collect(Collectors.groupingBy(TbItemPriceBaseFinanceDetail::getPackageId, Collectors.mapping(TbItemPriceBaseFinanceDetail::getCombinePackageCode, Collectors.toList())));


        final List<String> combinePackageCodes = packageIdCombinePackageCodeMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
        final Map<String, CombinePackageInfoDto> combinePackageInfoDtoMap = itemPriceCombinePackageService.queryCombinePackageByCodes(combinePackageCodes)
                .stream().collect(Collectors.toMap(CombinePackageInfoDto::getCombinePackageCode, Function.identity(), (a, b) -> b));

        return packageIdCombinePackageCodeMap.entrySet().stream().map(packageIdMap -> {
            // 基准包id
            final Long packageId = packageIdMap.getKey();

            final List<CombinePackageInfoDto> collect = packageIdMap.getValue().stream()
                    .map(combinePackageInfoDtoMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return Map.entry(packageId, collect);
        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Override
    public Map<Long, List<QueryCombinePackageListTestItemsVo>> selectBaseFinanceDetailAndTestItemMap() {
        // 查询项目下的财务套餐包的 ids
        final Map<Long, List<String>> packageIdCombinePackageCodeMap = baseMapper.selectList(new LambdaQueryWrapper<TbItemPriceBaseFinanceDetail>()
                        .select(TbItemPriceBaseFinanceDetail::getCombinePackageCode, TbItemPriceBaseFinanceDetail::getPackageId))
                .stream().collect(Collectors.groupingBy(TbItemPriceBaseFinanceDetail::getPackageId,
                        Collectors.mapping(TbItemPriceBaseFinanceDetail::getCombinePackageCode, Collectors.toList())));

        return getCombinePackageMap(packageIdCombinePackageCodeMap);
    }

    @Override
    public boolean deleteByFinanceDetail(long packageId, Collection<String> combinePackageCodes) {
        if (CollectionUtils.isEmpty(combinePackageCodes)) {
            return false;
        }
        return super.remove(new LambdaUpdateWrapper<TbItemPriceBaseFinanceDetail>()
                .eq(TbItemPriceBaseFinanceDetail::getPackageId, packageId)
                .in(TbItemPriceBaseFinanceDetail::getCombinePackageCode, combinePackageCodes));
    }

    @Override
    public List<ItemPriceBaseFinanceDetailVo> selectAll() {
        return JSON.parseArray( JSON.toJSONString(super.list()), ItemPriceBaseFinanceDetailVo.class);
    }

    private Map<Long, List<QueryCombinePackageListTestItemsVo>> getCombinePackageMap(Map<Long, List<String>> packageIdCombinePackageCodeMap) {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(packageIdCombinePackageCodeMap)) {
            return Collections.emptyMap();
        }

        final List<String> combinePackageCodes = packageIdCombinePackageCodeMap.values().stream().flatMap(List::stream).collect(Collectors.toList());

        // 查询财务套餐
        final Map<String, CombinePackageInfoDto> combinePackageCodeMap = itemPriceCombinePackageService.queryCombinePackageByCodes(combinePackageCodes)
                .stream().collect(Collectors.toMap(CombinePackageInfoDto::getCombinePackageCode, Function.identity(), (a, b) -> b));


        // 财务套餐下的项目
        final Map<String, List<TestItemDto>> combinePackageDetailIdMap = itemPriceCombinePackageDetailService.queryCombinePackageDetailInfo(combinePackageCodes);

        return packageIdCombinePackageCodeMap.entrySet().stream().map(packageIdMap -> {
            // 基准包id
            final Long packageId = packageIdMap.getKey();

            final List<QueryCombinePackageListTestItemsVo> queryCombinePackageListTestItemsVoList = packageIdMap.getValue().stream()
                    .map(combinePackageCodeMap::get)
                    .filter(Objects::nonNull)
                    .map(combinePackage -> {
                        // 财务套餐
                        final QueryCombinePackageListTestItemsVo queryCombinePackageListTestItemsVo = JSON.parseObject(JSON.toJSONString(combinePackage), QueryCombinePackageListTestItemsVo.class);
                        // 检验项目
                        final List<TestItemDto> testItemDtoList = combinePackageDetailIdMap.getOrDefault(queryCombinePackageListTestItemsVo.getCombinePackageCode(), Collections.emptyList());
                        queryCombinePackageListTestItemsVo.setTestItemDtoList(testItemDtoList);
                        return queryCombinePackageListTestItemsVo;
                    })
                    .collect(Collectors.toList());
            return Map.entry(packageId, queryCombinePackageListTestItemsVoList);

        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private TbItemPriceBaseFinanceDetail defaultTbFinanceDetail() {
        final TbItemPriceBaseFinanceDetail tb = new TbItemPriceBaseFinanceDetail();
        final LoginUserHandler.User user = LoginUserHandler.get();
        final Date date = new Date();
        tb.setFinanceDetailId(snowflakeService.genId());
        tb.setCreatorId(user.getUserId());
        tb.setCreatorName(user.getNickname());
        tb.setCreateDate(date);
        tb.setUpdaterId(user.getUserId());
        tb.setUpdaterName(user.getNickname());
        tb.setUpdateDate(date);
        tb.setIsDelete(YesOrNoEnum.NO.getCode());
        return tb;
    }
}
