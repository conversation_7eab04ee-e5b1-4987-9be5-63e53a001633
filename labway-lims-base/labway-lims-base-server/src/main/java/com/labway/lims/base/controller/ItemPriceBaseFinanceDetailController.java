package com.labway.lims.base.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.ItemPriceBaseFinanceDetailDto;
import com.labway.lims.base.api.service.ItemPriceBaseFinanceDetailService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 项目价格基准包下的财务套餐
 */
@RestController
@RequestMapping("/item-price-base-finance-detail")
public class ItemPriceBaseFinanceDetailController extends BaseController {

    @Resource
    private ItemPriceBaseFinanceDetailService itemPriceBaseFinanceDetailService;

    @GetMapping("/select")
    public Object select(@RequestParam Long packageId) {
        if (Objects.isNull(packageId)) {
            throw new IllegalArgumentException("项目价格基准包不能为空");
        }
        return itemPriceBaseFinanceDetailService.selectByPackageId(packageId);
    }

    @PostMapping("/add")
    public Object add(@RequestBody ItemPriceBaseFinanceDetailDto vo) {
        if (Objects.isNull(vo.getPackageId())) {
            throw new IllegalArgumentException("项目价格基准包不能为空");
        }
        if (CollectionUtils.isEmpty(vo.getCombinePackageCodes())) {
            throw new IllegalArgumentException("财务套餐不能为空");
        }
        return itemPriceBaseFinanceDetailService.add(vo);
    }

    @PostMapping("/delete")
    public Object delete(@RequestBody ItemPriceBaseFinanceDetailDto vo) {
        if (Objects.isNull(vo.getPackageId())) {
            throw new IllegalArgumentException("项目价格基准包不能为空");
        }
        if (CollectionUtils.isEmpty(vo.getCombinePackageCodes())) {
            throw new IllegalArgumentException("财务套餐不能为空");
        }
        return itemPriceBaseFinanceDetailService.deleteByFinanceDetail(vo.getPackageId(), vo.getCombinePackageCodes());
    }

}
