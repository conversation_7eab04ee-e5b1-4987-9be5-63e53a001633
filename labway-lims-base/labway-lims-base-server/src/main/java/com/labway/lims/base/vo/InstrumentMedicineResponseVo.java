package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 仪器药物维护信息响应Vo
 *
 * <AUTHOR>
 * @since 2023/7/12 17:40
 */
@Getter
@Setter
public class InstrumentMedicineResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器药物id
     */
    private Long instrumentMedicineId;
    /**
     * 药物id
     */
    private Long medicineId;

    /**
     * 药物编码
     */
    private String medicineCode;
    /**
     * 药物名称
     */
    private String medicineName;

    /**
     * 仪器通道号
     */
    private String instrumentChannel;

    /**
     * 发送到lims 1:是 0：否
     */
    private Integer sendLims;

}
