package com.labway.lims.base.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 导入 数据 失败 信息
 *
 * <AUTHOR>
 * @since 2023/4/4 10:11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportErrorResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 错误行号
     */
    private Integer rowNo;

    /**
     * 错误信息
     */
    private String errorInfo;
}
