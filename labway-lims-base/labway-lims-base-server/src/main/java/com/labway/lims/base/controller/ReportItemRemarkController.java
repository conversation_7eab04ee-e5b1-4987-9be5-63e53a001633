package com.labway.lims.base.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentReportItemRemarkDto;
import com.labway.lims.base.api.service.InstrumentReportItemRemarkService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 仪器报告项目结果备注
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@RestController
@RequestMapping("/report-item-remark")
public class ReportItemRemarkController extends BaseController {
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentReportItemRemarkService instrumentReportItemRemarkService;
    @DubboReference
    private InstrumentService instrumentService;

    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 获取结果备注列表
     */
    @GetMapping("/remarks")
    public Object remarks(@RequestParam Long instrumentReportItemId) {
        if (Objects.isNull(instrumentReportItemId)) {

            throw new IllegalArgumentException("参数错误");
        }
        return instrumentReportItemRemarkService.selectByInstrumentReportItemId(instrumentReportItemId);
    }

    /**
     * 添加结果备注
     */
    @PostMapping("/add")
    public Object add(@RequestBody InstrumentReportItemRemarkDto remark) throws Exception {
        if (Objects.isNull(remark.getInstrumentReportItemId())) {
            throw new IllegalArgumentException("instrumentReportItemId 不能为空");
        }

        checkRefParam(remark);

        return Map.of("id", instrumentReportItemRemarkService
                .addInstrumentReportItemRemark(remark));
    }

    /**
     * 修改结果备注
     */
    @PostMapping("/update")
    public Object update(@RequestBody InstrumentReportItemRemarkDto remark) {
        if (Objects.isNull(remark.getInstrumentReportItemRemarkId())) {
            throw new IllegalArgumentException("参数错误");
        }

        checkRefParam(remark);

        InstrumentReportItemRemarkDto remarkOld =
                instrumentReportItemRemarkService.selectById(remark.getInstrumentReportItemRemarkId());
        if (Objects.isNull(remarkOld)) {
            throw new IllegalStateException("结果备注不存在");
        }

        if (!instrumentReportItemRemarkService.updateByInstrumentReportItemRemarkId(remark)) {
            throw new IllegalStateException("修改结果备注失败");
        }

        // 日志
        final String compare = new CompareUtils<InstrumentReportItemRemarkDto>().compare(remarkOld, remark);
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_REFERENCE.getDesc())
                        .setContent(String.format("用户 [%s] 修改了仪器 [%s] 下的报告项目 [%s] 的结果备注，修改信息 [%s]",
                                LoginUserHandler.get().getNickname(),
                                remarkOld.getInstrumentName(),
                                remarkOld.getReportItemName(),
                                compare
                        )).toJSONString());
        return Collections.emptyMap();
    }

    /**
     * 删除结果备注
     */
    @DeleteMapping("/delete")
    public Object delete(@RequestBody Set<Long> instrumentReportItemRemarkIds) {
        if (CollectionUtils.isEmpty(instrumentReportItemRemarkIds)) {
            return Collections.emptyMap();
        }

        final Map<Long, InstrumentReportItemRemarkDto> refrenceMap = instrumentReportItemRemarkService.selectByIds(instrumentReportItemRemarkIds)
                .stream().collect(Collectors.toMap(InstrumentReportItemRemarkDto::getInstrumentReportItemRemarkId,
                        Function.identity(), (a, b) -> a));

        if (MapUtils.isEmpty(refrenceMap)) {
            return Collections.emptyMap();
        }

        for (Long instrumentReportItemRemarkId : instrumentReportItemRemarkIds) {

            instrumentReportItemRemarkService.deleteByInstrumentReportItemRemarkId(instrumentReportItemRemarkId);

            // 记录日子
            final InstrumentReportItemRemarkDto remark = refrenceMap.get(instrumentReportItemRemarkId);
            if (Objects.isNull(remark)) {
                continue;
            }

            String logContent = String.format("用户 [%s] 删除了仪器 [%s] 下的报告项目 [%s] 的结果备注;结果备注信息：" +
                            "样本类型 [%s] 适用性别 [%s] 适用年龄下限 [%s] 适用年龄上限 [%s] 年龄单位 [%s] 结果值下限 [%s] 结果值上限 [%s]" +
                            " 结果备注 [%s] ",
                    LoginUserHandler.get().getNickname(),
                    remark.getInstrumentName(),
                    remark.getReportItemName(),
                    remark.getSampleTypeName(),
                    remark.getSexStyleName(),
                    remark.getAgeMin(),
                    remark.getAgeMax(),
                    remark.getAgeUnit(),
                    remark.getResultMin(),
                    remark.getResultMax(),
                    remark.getResultRemark()
            );
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_REFERENCE.getDesc())
                            .setContent(logContent).toJSONString());
        }

        return Collections.emptyMap();
    }

    public static void checkRefParam(InstrumentReportItemRemarkDto remark) {
        if (StringUtils.isBlank(remark.getResultRemark())) {
            throw new IllegalArgumentException("结果备注不能为空");
        }
        if (Objects.isNull(remark.getSexStyle()) || StringUtils.isBlank(remark.getSexStyleName())) {
            throw new IllegalArgumentException("性别错误");
        }

        if (StringUtils.isAnyBlank(remark.getSampleTypeCode(), remark.getSampleTypeName())) {
            throw new IllegalArgumentException("样本类型错误");
        }

        // 年龄范围
        if ((Objects.nonNull(remark.getAgeMax()) && Objects.isNull(remark.getAgeMin()))
                || (Objects.nonNull(remark.getAgeMin()) && Objects.isNull(remark.getAgeMax()))) {
            throw new IllegalArgumentException("年龄范围参数错误");
        }

        if (StringUtils.isAnyBlank(remark.getAgeMaxFormula(), remark.getAgeMinFormula())) {
            throw new IllegalArgumentException("年龄范围参数错误");
        }

        if (Objects.nonNull(remark.getAgeMax()) && (remark.getAgeMin() < 0 || remark.getAgeMax() < remark.getAgeMin())) {
            throw new IllegalArgumentException("年龄范围参数错误");
        }

        // 结果上下限
        if (StringUtils.isNotBlank(remark.getResultMax()) && (NumberUtils.toDouble(remark.getResultMax()) < NumberUtils.toDouble(remark.getResultMin()))) {
            throw new IllegalArgumentException("结果上下限错误");
        }

    }

}
