
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.HspOrgSpecialOfferDto;
import com.labway.lims.base.model.TbHspOrgSpecialOffer;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 客户特价项目信息 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 19:21
 */
@Mapper(componentModel = "spring")
public interface HspOrgSpecialOfferConverter {

    HspOrgSpecialOfferDto hspOrgSpecialOfferDtoFromTbObj(TbHspOrgSpecialOffer obj);

    List<HspOrgSpecialOfferDto> hspOrgSpecialOfferDtoListFromTbObjList(List<TbHspOrgSpecialOffer> list);

    TbHspOrgSpecialOffer tbHspOrgSpecialOfferFromTbDto(HspOrgSpecialOfferDto obj);

    List<TbHspOrgSpecialOffer> tbHspOrgSpecialOfferListFromTbDto(List<HspOrgSpecialOfferDto> list);

}
