
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.HspOrgDoctorDto;
import com.labway.lims.base.model.TbHspOrgDoctor;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 送检机构科室医生 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface HspOrgDoctorConverter {

    HspOrgDoctorDto hspOrgDoctorDtoTbObj(TbHspOrgDoctor obj);

    List<HspOrgDoctorDto> hspOrgDoctorDtoListFromTbObj(List<TbHspOrgDoctor> list);

}
