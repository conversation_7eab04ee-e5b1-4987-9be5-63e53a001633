
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.PackageItemDto;
import com.labway.lims.base.model.TbPackageItem;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 体检单位套餐项目 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface PackageItemConverter {

    PackageItemDto packageItemDtoFromTbObj(TbPackageItem obj);

    List<PackageItemDto> packageItemDtoListFromTbObj(List<TbPackageItem> list);

}
