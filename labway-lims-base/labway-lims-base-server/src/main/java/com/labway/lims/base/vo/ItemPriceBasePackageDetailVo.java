package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 项目价格基准包详情
 */
@Getter
@Setter
public class ItemPriceBasePackageDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long detailId;

    /**
     * 基准包id
     */
    private Long packageId;

    /**
     * 项目id
     */
    private Long testItemId;

    /**
     * 项目编码
     */
    private String testItemCode;

}
