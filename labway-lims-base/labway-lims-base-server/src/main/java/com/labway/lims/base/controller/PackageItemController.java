package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.PackageDto;
import com.labway.lims.base.api.dto.PackageItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.PackageItemService;
import com.labway.lims.base.api.service.PackageService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.vo.PackageItemAddRequestVo;
import com.labway.lims.base.vo.SelectByPackageResponseVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 套餐 项目 API
 *
 * <AUTHOR>
 * @since 2023/3/28 17:38
 */
@RestController
@RequestMapping("/package-item")
public class PackageItemController extends BaseController {

    @DubboReference
    private TestItemService testItemService;

    @DubboReference
    private ReportItemService reportItemService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @DubboReference
    private PackageService packageService;

    @DubboReference
    private PackageItemService packageItemService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 体检单位套餐 项目 新增
     */
    @PostMapping("/add")
    public Object packageItemAdd(@RequestBody PackageItemAddRequestVo vo) {
        if (Objects.isNull(vo.getPackageId()) || CollectionUtils.isEmpty(vo.getTestItemIds())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        PackageDto packageDto =
            packageService.selectByPackageId(vo.getPackageId());
        if (Objects.isNull(packageDto)) {
            throw new IllegalArgumentException("对应套餐不存在");
        }

        // 当前套餐下 检验项目
        List<PackageItemDto> PackageItemDtos =
            packageItemService.selectByPackageId(vo.getPackageId());
        List<Long> testItemIdsNow = PackageItemDtos.stream()
            .map(PackageItemDto::getTestItemId).collect(Collectors.toList());

        // 套餐 需要增加的检验项目
        if (vo.getTestItemIds().stream().anyMatch(testItemIdsNow::contains)) {
            throw new IllegalArgumentException("要添加的检验项目有已添加项目在内");
        }
        List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(vo.getTestItemIds());
        if (CollectionUtils.isEmpty(testItemDtos)){
            throw new IllegalArgumentException("没有找到检验项目");
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 检验单位套餐检验项目
        List<PackageItemDto> list = Lists.newArrayListWithCapacity(testItemDtos.size());
        Date date = new Date();
        testItemDtos.forEach(item -> {
            PackageItemDto build = new PackageItemDto();
            build.setPackageItemId(snowflakeService.genId());
            build.setPackageId(packageDto.getPackageId());
            build.setTestItemId(item.getTestItemId());
            build.setTestItemCode(item.getTestItemCode());
            build.setTestItemName(item.getTestItemName());
            build.setIsDelete(YesOrNoEnum.NO.getCode());
            build.setCreateDate(date);
            build.setUpdateDate(date);
            build.setCreatorId(loginUser.getUserId());
            build.setCreatorName(loginUser.getNickname());
            build.setUpdaterId(loginUser.getUserId());
            build.setUpdaterName(loginUser.getNickname());
            list.add(build);
        });
        packageItemService.addPackageItems(list);
        list.forEach(item -> {
            rabbitMQService
                .convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.PACKAGE_LOG.getDesc())
                        .setContent(String.format("[%s] 套餐下添加 [%s] 项目",
                            packageDto.getPackageName(), item.getTestItemName()))
                        .toJSONString());

        });
        return Collections.emptyMap();
    }

    /**
     * 根据 体检套餐 获取 其 对应 体检 项目
     */
    @PostMapping("/select-by-package-id")
    public Object selectByPackageId(@RequestParam long packageId) {

        PackageDto packageDto =
            packageService.selectByPackageId(packageId);
        if (Objects.isNull(packageDto)) {
            throw new IllegalArgumentException("对应套餐不存在");
        }

        // 当前套餐下 检验项目
        List<PackageItemDto> PackageItemDtos =
            packageItemService.selectByPackageId(packageId);
        List<Long> testItemIds = PackageItemDtos.stream().map(PackageItemDto::getTestItemId)
            .collect(Collectors.toList());

        // 对应检验项目
        List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(testItemIds);
        Map<Long, TestItemDto> toMapByTestItemId =
            testItemDtos.stream().collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));

        // 检验项目 对应报告项目
        List<ReportItemDto> reportItemDtos = reportItemService.selectByTestItemIds(testItemIds);
        Map<Long, List<ReportItemDto>> reportItemGroupingByTestItemId =
            reportItemDtos.stream().collect(Collectors.groupingBy(ReportItemDto::getTestItemId));

        List<SelectByPackageResponseVo> targetList = PackageItemDtos.stream()
            .map(obj -> JSON.parseObject(JSON.toJSONString(obj), SelectByPackageResponseVo.class))
            .collect(Collectors.toList());

        // 拼接 检验项目信息
        targetList.forEach(item -> {
            TestItemDto testItemDto =
                ObjectUtils.defaultIfNull(toMapByTestItemId.get(item.getTestItemId()), new TestItemDto());
            item.setSampleTypeCode(testItemDto.getSampleTypeCode());
            item.setSampleTypeName(testItemDto.getSampleTypeName());
            item.setTubeCode(testItemDto.getTubeCode());
            item.setTubeName(testItemDto.getTubeName());
            item.setGroupId(testItemDto.getGroupId());
            item.setGroupName(testItemDto.getGroupName());

            List<ReportItemDto> reportItemDtoList = ObjectUtils
                .defaultIfNull(reportItemGroupingByTestItemId.get(item.getTestItemId()), Collections.emptyList());
            item.setReportItemList(reportItemDtoList);
        });

        return targetList;
    }

    /**
     * 体检单位套餐 项目 删除
     */
    @PostMapping("/delete")
    public Object germDelete(@RequestBody Set<Long> packageItemIds) {
        if (CollectionUtils.isEmpty(packageItemIds)) {
            return Collections.emptyMap();
        }
        List<PackageItemDto> PackageItemDtos =
            packageItemService.selectByPackageItemIds(packageItemIds);
        packageItemService.deleteByPackageItemIds(packageItemIds);

        // 对应套餐
        Set<Long> physicalGroupPackageIds = PackageItemDtos.stream()
            .map(PackageItemDto::getPackageId).collect(Collectors.toSet());
        //
        Map<Long, String> physicalGroupPackageNameById =
            packageService.selectByPackageIds(physicalGroupPackageIds).stream()
                .collect(Collectors.toMap(PackageDto::getPackageId,
                    PackageDto::getPackageName, (key1, key2) -> key1));

        PackageItemDtos.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.PACKAGE_LOG.getDesc())
                    .setContent(String.format("[%s] 套餐下删除 [%s] 项目",
                        physicalGroupPackageNameById.getOrDefault(item.getPackageId(), ""),
                        item.getTestItemName()))
                    .toJSONString());
        });
        return Collections.emptyMap();
    }
}
