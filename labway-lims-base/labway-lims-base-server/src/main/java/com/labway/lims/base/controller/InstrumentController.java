package com.labway.lims.base.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.InstrumentGroupInstrumentService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.vo.AddInstrumentVo;
import com.labway.lims.base.vo.UpdateInstrumentVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Set;


/**
 * <AUTHOR>
 * @since 2023/3/20 14:29
 */
@RestController
@RequestMapping("/instrument")
public class InstrumentController extends BaseController {
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private InstrumentGroupInstrumentService instrumentGroupInstrumentService;
    @Resource
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private GroupService groupService;

    @DubboReference
    private RabbitMQService rabbitMQService;
    /**
     * 查询仪器
     */
    @GetMapping("/instruments")
    public Object instruments(Long groupId) {
        // 为空查所有
        if (Objects.isNull(groupId)) {
            return instrumentService.selectByOrgId(LoginUserHandler.get().getOrgId());
        }
        return instrumentService.selectByGroupId(groupId);
    }

    /**
     * 新增仪器
     */
    @PostMapping("/add")
    public Object add(@RequestBody AddInstrumentVo vo) {
        if (StringUtils.isAnyBlank(vo.getInstrumentCode(), vo.getInstrumentName())
                || Objects.isNull(vo.getEnable()) || Objects.isNull(vo.getGroupId())) {
            throw new IllegalArgumentException("参数错误");
        }

        final ProfessionalGroupDto professionalGroup = groupService.selectByGroupId(vo.getGroupId());
        if (Objects.isNull(professionalGroup)) {
            throw new IllegalArgumentException("专业组不存在");
        }

        final InstrumentDto instrument = new InstrumentDto();
        instrument.setInstrumentName(vo.getInstrumentName());
        instrument.setInstrumentCode(vo.getInstrumentCode());
        instrument.setGroupId(vo.getGroupId());
        instrument.setGroupName(professionalGroup.getGroupName());
        instrument.setEnable(vo.getEnable());

        final Map<String, Long> result = Map.of("id", instrumentService.addInstrument(instrument));

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT.getDesc())
                        .setContent(String.format("新增仪器 [%s] 编码 [%s] 专业组 [%s] 是否启用 [%s]"
                                , instrument.getInstrumentName(), instrument.getInstrumentCode(), instrument.getGroupName()
                                , YesOrNoEnum.selectByCode(instrument.getEnable()).getCode())).toJSONString());
        return result;
    }


    /**
     * 修改仪器
     */
    @PostMapping("/update")
    public Object update(@RequestBody UpdateInstrumentVo vo) {

        if (StringUtils.isAnyBlank(vo.getInstrumentCode(), vo.getInstrumentName())
                || Objects.isNull(vo.getEnable()) || Objects.isNull(vo.getGroupId())
                || Objects.isNull(vo.getInstrumentId())) {
            throw new IllegalArgumentException("参数错误");
        }

        ProfessionalGroupDto professionalGroup = groupService.selectByGroupId(vo.getGroupId());
        if (Objects.isNull(professionalGroup)) {
            throw new IllegalArgumentException("专业组不存在");
        }

        final InstrumentDto instrumentOld = instrumentService.selectByInstrumentId(vo.getInstrumentId());
        if ( Objects.isNull(instrumentOld) ){
            throw new IllegalArgumentException("仪器不存在");
        }

        final InstrumentDto instrument = new InstrumentDto();
        instrument.setInstrumentId(vo.getInstrumentId());
        instrument.setInstrumentName(vo.getInstrumentName());
        instrument.setGroupId(vo.getGroupId());
        instrument.setGroupName(professionalGroup.getGroupName());
        instrument.setEnable(vo.getEnable());

        if (!instrumentService.updateByInstrumentId(instrument)) {
            throw new IllegalStateException("修改仪器失败");
        }

        String compare = new CompareUtils<InstrumentDto>().compare(instrumentOld, instrument);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT.getDesc())
                            .setContent(String.format("修改仪器 [%s]", compare)).toJSONString());
        }

        return Collections.emptyMap();
    }


    /**
     * 删除仪器
     */
    @DeleteMapping("/delete")
    public Object delete(@RequestBody Set<Long> instrumentIds) {

        if (CollectionUtils.isEmpty(instrumentIds)) {
            return Collections.emptyMap();
        }

        for (Long instrumentId : instrumentIds) {

            final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentId);
            if (Objects.isNull(instrument)) {
                throw new IllegalArgumentException("仪器不存在");
            }

            if (CollectionUtils.isNotEmpty(instrumentGroupInstrumentService.selectByInstrumentId(instrumentId))) {
                throw new IllegalStateException(String.format("%s仪器被引用，不能删除", instrument.getInstrumentName()));
            }

            if (CollectionUtils.isNotEmpty(instrumentReportItemService.selectByInstrumentId(instrumentId))) {
                throw new IllegalStateException(String.format("%s下有报告项目，不能删除", instrument.getInstrumentName()));
            }


            instrumentService.deleteByInstrumentId(instrumentId);

            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT.getDesc())
                            .setContent(String.format("删除仪器 [%s],仪器id [%s]"
                                    , instrument.getInstrumentName(), instrument.getInstrumentId())).toJSONString());
        }

        return Collections.emptyMap();
    }

}
