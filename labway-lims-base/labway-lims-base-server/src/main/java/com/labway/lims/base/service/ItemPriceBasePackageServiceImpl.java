package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.SendTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.HspOrgDiscountDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDetailDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDto;
import com.labway.lims.base.api.service.HspOrgDiscountService;
import com.labway.lims.base.api.service.ItemPriceBasePackageDetailService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.mapper.TbItemPriceBasePackageMapper;
import com.labway.lims.base.mapstruct.ItemPriceBasePackageConverter;
import com.labway.lims.base.model.TbItemPriceBasePackage;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@DubboService
@Slf4j
public class ItemPriceBasePackageServiceImpl implements ItemPriceBasePackageService {
    @Resource
    private TbItemPriceBasePackageMapper tbItemPriceBasePackageMapper;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private ItemPriceBasePackageConverter itemPriceBasePackageConverter;

    @Resource
    private HspOrgDiscountService hspOrgDiscountService;
    @Resource
    private ItemPriceBasePackageDetailService itemPriceBasePackageDetailService;

    @Override
    public long add(ItemPriceBasePackageDto dto) {

        // 校验是否存在时间交集的基准包
        if (selectByDateInterval(dto.getStartDate(), dto.getEndDate()).stream()
            .anyMatch(e -> Objects.equals(dto.getPackageName(), e.getPackageName()))) {
            throw new IllegalStateException(String.format("基准包 [%s] 当前日期范围已存在，请重新选择", dto.getPackageName()));
        }

        final Date now = new Date();
        dto.setPackageId(ObjectUtils.defaultIfNull(dto.getPackageId(), snowflakeService.genId()));
        dto.setCreateDate(ObjectUtils.defaultIfNull(dto.getCreateDate(), now));
        dto.setUpdateDate(ObjectUtils.defaultIfNull(dto.getUpdateDate(), now));
        dto.setCreatorId(ObjectUtils.defaultIfNull(dto.getCreatorId(), LoginUserHandler.get().getUserId()));
        dto.setCreatorName(ObjectUtils.defaultIfNull(dto.getCreatorName(), LoginUserHandler.get().getNickname()));
        dto.setUpdaterId(ObjectUtils.defaultIfNull(dto.getUpdaterId(), LoginUserHandler.get().getUserId()));
        dto.setUpdaterName(ObjectUtils.defaultIfNull(dto.getUpdaterName(), LoginUserHandler.get().getNickname()));
        dto.setOrgId(ObjectUtils.defaultIfNull(dto.getOrgId(), LoginUserHandler.get().getOrgId()));
        dto.setOrgName(ObjectUtils.defaultIfNull(dto.getOrgName(), LoginUserHandler.get().getOrgName()));
        dto.setIsDelete(ObjectUtils.defaultIfNull(dto.getIsDelete(), YesOrNoEnum.NO.getCode()));
        final int insert =
            tbItemPriceBasePackageMapper.insert(JSON.parseObject(JSON.toJSONString(dto), TbItemPriceBasePackage.class));
        if (insert <= 0) {
            throw new IllegalStateException("添加失败");
        }

        log.info("添加基准包成功，用户: [{}] 专业组: [{}] id: [{}]", LoginUserHandler.get().getNickname(),
            LoginUserHandler.get().getGroupName(), dto.getPackageId());
        return dto.getPackageId();
    }

    @Override
    public List<ItemPriceBasePackageDto> selectByDateInterval(Date startDate, Date endDate) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return null;
        }
        LoginUserHandler.User user = LoginUserHandler.get();

        LambdaQueryWrapper<TbItemPriceBasePackage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbItemPriceBasePackage::getOrgId, user.getOrgId());
        // 时间 存在交集
        queryWrapper.and(wrapper -> wrapper
            .and(w1 -> w1.ge(TbItemPriceBasePackage::getStartDate, startDate)
                .and(w2 -> w2.le(TbItemPriceBasePackage::getStartDate, endDate)))
            .or(w3 -> w3.le(TbItemPriceBasePackage::getStartDate, startDate)
                .and(w4 -> w4.ge(TbItemPriceBasePackage::getEndDate, endDate)))
            .or(w5 -> w5.ge(TbItemPriceBasePackage::getEndDate, startDate)
                .and(w6 -> w6.le(TbItemPriceBasePackage::getEndDate, endDate))));
        List<TbItemPriceBasePackage> tbItemPriceBasePackages = tbItemPriceBasePackageMapper.selectList(queryWrapper);

        return itemPriceBasePackageConverter.itemPriceBasePackageDtoListFromTbObjList(tbItemPriceBasePackages);

    }

    @Override
    public ItemPriceBasePackageDto selectByName(String name) {
        if (Objects.isNull(name)) {
            return null;
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        LambdaQueryWrapper<TbItemPriceBasePackage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbItemPriceBasePackage::getPackageName, name);
        queryWrapper.eq(TbItemPriceBasePackage::getOrgId, user.getOrgId());
        List<TbItemPriceBasePackage> tbItemPriceBasePackages = tbItemPriceBasePackageMapper.selectList(queryWrapper);
        return JSON.parseObject(JSON.toJSONString(tbItemPriceBasePackages.stream().findFirst().orElse(null)),
            ItemPriceBasePackageDto.class);
    }

    @Override
    public List<ItemPriceBasePackageDto> list() {
        final LambdaQueryWrapper<TbItemPriceBasePackage> order = Wrappers.lambdaQuery(TbItemPriceBasePackage.class)
            .eq(TbItemPriceBasePackage::getOrgId, LoginUserHandler.get().getOrgId())
            .orderByDesc(TbItemPriceBasePackage::getPackageId);

        return JSON.parseArray(JSON.toJSONString(tbItemPriceBasePackageMapper.selectList(order)),
            ItemPriceBasePackageDto.class);
    }

    @Override
    public void update(ItemPriceBasePackageDto dto) {

        // 是否存在客户折扣
        final Long packageId = dto.getPackageId();
        final List<HspOrgDiscountDto> hspOrgDiscounts = hspOrgDiscountService.selectByPackageId(packageId);
        if (CollectionUtils.isNotEmpty(hspOrgDiscounts)) {
            throw new IllegalStateException("当前基准包已存在客户折扣，不可修改");
        }

        // 校验是否存在时间交集的基准包
        if (selectByDateInterval(dto.getStartDate(), dto.getEndDate()).stream()
            .anyMatch(e -> Objects.equals(dto.getPackageName(), e.getPackageName())
                && !Objects.equals(dto.getPackageId(), e.getPackageId()))) {
            throw new IllegalStateException("当前日期范围已存在，请重新选择");
        }

        dto.setEnable(ObjectUtils.defaultIfNull(dto.getEnable(), YesOrNoEnum.YES.getCode()));
        dto.setUpdaterName(LoginUserHandler.get().getNickname());
        dto.setUpdaterId(LoginUserHandler.get().getUserId());
        dto.setUpdateDate(new Date());

        tbItemPriceBasePackageMapper.updateById(JSON.parseObject(JSON.toJSONString(dto), TbItemPriceBasePackage.class));

        log.info("修改基准包成功，用户: [{}] 专业组: [{}] id: [{}] 修改内容: [{}]", LoginUserHandler.get().getNickname(),
            LoginUserHandler.get().getGroupName(), dto.getPackageId(), JSON.toJSON(dto));
    }

    @Override
    public void delete(List<Long> packageIds) {
        if (tbItemPriceBasePackageMapper.deleteBatchIds(packageIds) < 0) {
            throw new IllegalStateException("删除失败");
        }

        log.info("删除基准包成功，用户: [{}] 专业组: [{}] id: [{}]", LoginUserHandler.get().getNickname(),
            LoginUserHandler.get().getGroupName(), packageIds);
    }

    @Override
    public ItemPriceBasePackageDto selectById(Long packageId) {
        return JSON.parseObject(JSON.toJSONString(tbItemPriceBasePackageMapper.selectById(packageId)),
            ItemPriceBasePackageDto.class);
    }

    @Override
    public List<ItemPriceBasePackageDto> selectByDateRange(Date startDate, Date endDate) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbItemPriceBasePackage> queryWrapper = Wrappers.lambdaQuery();

        // 时间 存在交集
        queryWrapper.and(wrapper -> wrapper
            .and(w1 -> w1.ge(TbItemPriceBasePackage::getStartDate, startDate)
                .and(w2 -> w2.le(TbItemPriceBasePackage::getStartDate, endDate)))
            .or(w3 -> w3.le(TbItemPriceBasePackage::getStartDate, startDate)
                .and(w4 -> w4.ge(TbItemPriceBasePackage::getEndDate, endDate)))
            .or(w5 -> w5.ge(TbItemPriceBasePackage::getEndDate, startDate)
                .and(w6 -> w6.le(TbItemPriceBasePackage::getEndDate, endDate))));
        return itemPriceBasePackageConverter
            .itemPriceBasePackageDtoListFromTbObjList(tbItemPriceBasePackageMapper.selectList(queryWrapper));

    }

    @Override
    public List<ItemPriceBasePackageDto> selectByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return itemPriceBasePackageConverter
            .itemPriceBasePackageDtoListFromTbObjList(tbItemPriceBasePackageMapper.selectBatchIds(ids));

    }

    @Override
    public Map<Long, BigDecimal> selectActualFeePrice(Long hspOrgId, String applyTypeCode, Date sampleItemCreateDate,
        Collection<Long> testItemIds) {

        // 对应唯一一条 客户折扣
        HspOrgDiscountDto hspOrgDiscountDto =
            hspOrgDiscountService.selectByApplyTypeAndDate(hspOrgId, applyTypeCode, sampleItemCreateDate);
        if (Objects.isNull(hspOrgDiscountDto)) {
            // 通过 送检类型 找不到 通过 全部 找
            hspOrgDiscountDto = hspOrgDiscountService.selectByApplyTypeAndDate(hspOrgId,
                SendTypeEnum.SEND_TYPE_ALL.getCode(), sampleItemCreateDate);
        }
        if (Objects.isNull(hspOrgDiscountDto)) {
            return Collections.emptyMap();
        }
        // 对应基准包 详情
        List<ItemPriceBasePackageDetailDto> packageDetailDtoList =
            itemPriceBasePackageDetailService.selectByPackageId(hspOrgDiscountDto.getPackageId());

        // 返回 对应检验项目 对应实际收费价格
        return packageDetailDtoList.stream().filter(obj -> testItemIds.contains(obj.getTestItemId()))
            .collect(Collectors.toMap(ItemPriceBasePackageDetailDto::getTestItemId,
                ItemPriceBasePackageDetailDto::getFeePrice, (key1, key2) -> key1));
    }
}
