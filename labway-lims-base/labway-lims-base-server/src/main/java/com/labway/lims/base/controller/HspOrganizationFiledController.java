package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.DoubleCheckFiledEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.HspOrganizationFiledCompareDto;
import com.labway.lims.base.api.dto.HspOrganizationFiledDto;
import com.labway.lims.base.api.dto.SaveHspOrganizationFiledDto;
import com.labway.lims.base.api.service.HspOrganizationFiledService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.vo.HspOrganizationFiledVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 样本双输复核对照
 */
@Slf4j
@RestController
@RequestMapping("/hsp-organization-filed")
public class HspOrganizationFiledController extends BaseController {

    @Resource
    private HspOrganizationFiledService hspOrganizationFiledService;

    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private HspOrganizationService hspOrganizationService;

    @PostMapping("/delete")
    public Object delete(@RequestBody Set<Long> hspOrgIds) {

        if (CollectionUtils.isEmpty(hspOrgIds)) {
            throw new IllegalArgumentException("请选择需要删除的双数对照");
        }

        hspOrganizationFiledService.deleteByHspOrgIds(hspOrgIds);

        List<HspOrganizationDto> hspOrganizationDtos = hspOrganizationService.selectByHspOrgIds(hspOrgIds);
        hspOrganizationDtos.forEach(item -> {
            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_ORGANIZATION_FILED_LOG.getDesc())
                    .setContent(String.format("删除 [%s] 下双输对照内容", item.getHspOrgName())).toJSONString());
        });

        return Collections.emptyMap();
    }

    /**
     * 修改双输对照内容
     */
    @PostMapping("/update")
    public Object update(@RequestBody HspOrganizationFiledVo vo) {

        log.info("添加双输对照内容 用户 [{}] add param:{} ", LoginUserHandler.get().getNickname(), JSON.toJSONString(vo));

        checkAddParam(vo);

        HspOrganizationFiledCompareDto now = new HspOrganizationFiledCompareDto();
        String filedNames =
            vo.getFileds().stream().sorted(Comparator.comparing(HspOrganizationFiledVo.Filed::getCode)).map(obj -> {
                DoubleCheckFiledEnum byCode = DoubleCheckFiledEnum.getByCode(obj.getCode());
                if (Objects.nonNull(byCode)) {
                    return byCode.getDesc();
                }
                return obj.getCode();
            }).collect(Collectors.joining(","));
        now.setFiledNames(filedNames);

        // 现有对照内容
        List<HspOrganizationFiledDto> hspOrganizationFiledDtos =
            hspOrganizationFiledService.selectByHspOrgId(vo.getHspOrgId());
        HspOrganizationFiledCompareDto old = new HspOrganizationFiledCompareDto();

        old.setFiledNames(
            hspOrganizationFiledDtos.stream().sorted(Comparator.comparing(HspOrganizationFiledDto::getCode))
                .map(HspOrganizationFiledDto::getFiled).collect(Collectors.joining(",")));
        final SaveHspOrganizationFiledDto dto =
            JSON.parseObject(JSON.toJSONString(vo), SaveHspOrganizationFiledDto.class);

        Set<Long> update = hspOrganizationFiledService.update(dto);

        String compare = new CompareUtils<HspOrganizationFiledCompareDto>().compare(old, now);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_ORGANIZATION_FILED_LOG.getDesc())
                    .setContent(String.format("修改双输对照机构 [%s] 下对照字段: [%s]", vo.getHspOrgName(), compare))
                    .toJSONString());
        }

        return update;
    }

    /**
     * 双输对照字段下拉框
     */
    @GetMapping("/double-check-filed")
    public Object doubleCheckFiled() {
        return Arrays.stream(DoubleCheckFiledEnum.values()).map(m -> Map.of("code", m.getCode(), "desc", m.getDesc()))
            .collect(Collectors.toList());
    }

    /**
     * 查询双输对照内容
     */
    @GetMapping("/list")
    public Object list(@RequestParam(required = false) Long hspOrgId) {
        final List<HspOrganizationFiledDto> hspOrganizationFileds =
            hspOrganizationFiledService.selectByHspOrgId(hspOrgId);

        if (CollectionUtils.isEmpty(hspOrganizationFileds)) {
            return Collections.emptyList();
        }

        Map<Long, HspOrganizationFiledVo> vos = new HashMap<>(30);

        for (final HspOrganizationFiledDto h : hspOrganizationFileds) {
            final Long id = h.getHspOrgId();

            vos.computeIfPresent(id, (k, v) -> {
                v.getFileds().add(new HspOrganizationFiledVo.Filed(h.getCode(), h.getFiled()));
                return v;
            });

            final HspOrganizationFiledVo vo = new HspOrganizationFiledVo();
            vo.setFiledId(h.getFiledId());
            vo.setHspOrgId(h.getHspOrgId());
            vo.setHspOrgName(h.getHspOrgName());
            vo.setFileds(Sets.newHashSet());

            vos.putIfAbsent(h.getHspOrgId(), vo);

            Optional.ofNullable(vos.get(id))
                .ifPresent(f -> f.getFileds().add(new HspOrganizationFiledVo.Filed(h.getCode(), h.getFiled())));

        }
        return vos.values().stream().sorted(Comparator.comparing(HspOrganizationFiledVo::getHspOrgName))
            .collect(Collectors.toList());
    }

    /**
     * 添加双输对照内容
     */
    @PostMapping("/add")
    public Object add(@RequestBody HspOrganizationFiledVo vo) {
        log.info("添加双输对照内容 用户 [{}] add param:{} ", LoginUserHandler.get().getNickname(), JSON.toJSONString(vo));

        checkAddParam(vo);

        final SaveHspOrganizationFiledDto dto =
            JSON.parseObject(JSON.toJSONString(vo), SaveHspOrganizationFiledDto.class);

        if (CollectionUtils.isNotEmpty(hspOrganizationFiledService.selectByHspOrgId(vo.getHspOrgId()))) {
            throw new IllegalStateException("该外送机构已经存在双输对照内容");
        }
        final HspOrganizationDto org = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(org)) {
            throw new IllegalStateException("外送机构不存在");
        }

        Set<Long> add = hspOrganizationFiledService.addHspOrganizationFiled(dto);

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_ORGANIZATION_FILED_LOG.getDesc())
                .setContent(String.format("新增双输对照机构名称 [%s]", org.getHspOrgName())).toJSONString());

        return add;
    }

    public void checkAddParam(HspOrganizationFiledVo vo) {
        final Set<HspOrganizationFiledVo.Filed> fileds = vo.getFileds();
        if (fileds == null || fileds.isEmpty()) {
            throw new IllegalStateException("双输对照内容不能为空");
        }

        final int size = fileds.stream().map(HspOrganizationFiledVo.Filed::getCode).collect(Collectors.toSet()).size();

        if (size != fileds.size()) {
            throw new IllegalStateException("双输对照内容不能重复");
        }

        if (Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalStateException("外送机构不存在");
        }
    }
}
