package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;
import com.labway.lims.base.model.TbInstrumentReportItemResultExchange;
import org.mapstruct.Mapper;

/**
 * <p>
 * InstrumentReportItemResultExchangeConverter
 * 仪器报告项目结果值转换 转换
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 9:23
 */
@Mapper(componentModel = "spring")
public interface InstrumentReportItemResultExchangeConverter {

    TbInstrumentReportItemResultExchange convertDto2Entity(InstrumentReportItemResultExchangeDto resultExchangeDto);

}
