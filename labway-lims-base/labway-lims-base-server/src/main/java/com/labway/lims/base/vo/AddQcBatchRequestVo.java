package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 添加 质控 批号 请求 Vo
 *
 * <AUTHOR>
 * @since 2023/9/14 15:19
 */
@Getter
@Setter
public class AddQcBatchRequestVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 仪器编码
     */
    private String instrumentCode;
    /**
     * 质控批号
     */
    private String qcBatch;
    /**
     * 质控品名称
     */
    private String qcMatieralName;
    /**
     * 试剂品牌
     */
    private String reagentBrand;

    /**
     * 来源
     */
    private String source;
    /**
     * 生效时间
     */
    private Date beginDate;

    /**
     * 失效时间
     */
    private Date endDate;
    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 低浓度
     */
    private String low;

    /**
     * 中浓度
     */
    private String medium;

    /**
     * 高浓度
     */
    private String high;

}
