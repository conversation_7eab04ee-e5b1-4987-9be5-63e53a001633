
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.MedicineGermRelationDto;
import com.labway.lims.base.model.TbMedicineGermRelation;
import com.labway.lims.base.vo.GermGenusMedicineResponseVo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 药物细菌关联表 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface MedicineGermRelationConverter {

    MedicineGermRelationDto medicineGermRelationDtoTbObj(TbMedicineGermRelation obj);

    List<MedicineGermRelationDto> medicineGermRelationDtoListTbObj(List<TbMedicineGermRelation> obj);

    GermGenusMedicineResponseVo germGenusMedicineResponseVoDtoTbObjDto(MedicineGermRelationDto obj);

    TbMedicineGermRelation tbMedicineGermRelationFromTbObjDto(MedicineGermRelationDto obj);
}
