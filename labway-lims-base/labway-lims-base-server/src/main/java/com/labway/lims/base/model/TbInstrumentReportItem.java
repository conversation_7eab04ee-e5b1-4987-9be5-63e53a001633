package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 仪器报告项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Setter
@Getter
@TableName("tb_instrument_report_item")
public class TbInstrumentReportItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器报告项目ID
     */
    @TableId
    private Long instrumentReportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 英文缩写
     */
    private String enAb;

    /**
     * 别名
     */
    private String aliasName;

    /**
     * 检验方法编码
     */
    private String examMethodCode;

    /**
     * 检验方法名称
     */
    private String examMethodName;

    /**
     * 是否质控
     *
     * @see YesOrNoEnum
     */
    private Integer isQc;

    /**
     * 是否打印
     *
     * @see YesOrNoEnum
     */
    private Integer isPrint;

    /**
     * 打印顺序
     */
    private Integer printSort;

    /**
     * 是否手工录入
     *
     * @see YesOrNoEnum
     */
    private Integer isManualInput;

    /**
     * 是否结果为0
     *
     * @see YesOrNoEnum
     */
    private Integer isResultZero;

    /**
     * 检测结果类型名称
     */
    private String resultTypeName;

    /**
     * 检测结果类型编码
     */
    private String resultTypeCode;

    /**
     * 检测结果是否为空
     *
     * @see YesOrNoEnum
     */
    private Integer isResultNull;

    /**
     * 结果小数点位数
     */
    private Integer decimalNums;

    /**
     * 仪器通道编码
     */
    private String instrumentChannel;

    /**
     * 计算公式json
     */
    private String calcFomulation;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 项目单位
     */
    private String reportItemUnit;

    /**
     * 单位名称
     */
    private String reportItemUnitName;

    /**
     * 是否启用(0未启用 1启用)
     *
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 项目类型编码
     */
    private String itemTypeCode;

    /**
     * 项目类型名称
     */
    private String itemTypeName;

    /**
     * 是否自动带出报告项目
     *
     * @see YesOrNoEnum
     */
    private Integer isBringOut;

    /**
     * 1:已经删除 0:未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 空参考范围提示 0:不提示，1:禁止审核，2:审核提示
     */
    private Integer emptyReferenceTip;

}
