package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.api.service.RackArchiveService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.ArchiveStoreService;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.RefrigeratorGroupService;
import com.labway.lims.base.api.service.RefrigeratorService;
import com.labway.lims.base.mapstruct.RefrigeratorConverter;
import com.labway.lims.base.vo.RefrigeratorAddRequestVo;
import com.labway.lims.base.vo.RefrigeratorUpdateRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 冰箱 API
 *
 * <AUTHOR>
 * @since 2023/4/3 11:03
 */
@Slf4j
@RestController
@RequestMapping("/refrigerator")
public class RefrigeratorController extends BaseController {

    @DubboReference
    private GroupService groupService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private ArchiveStoreService archiveStoreService;

    @DubboReference
    private RefrigeratorService refrigeratorService;

    @DubboReference
    private RefrigeratorGroupService refrigeratorGroupService;

    @DubboReference
    private RackArchiveService rackArchiveService;

    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private RefrigeratorConverter refrigeratorConverter;

    /**
     * 冰箱 新增
     */
    @PostMapping("/add")
    public Object refrigeratorAdd(@RequestBody RefrigeratorAddRequestVo vo) {
        if (StringUtils.isAnyBlank(vo.getRefrigeratorCode(), vo.getRefrigeratorName())
            || Objects.isNull(vo.getArchiveStoreId()) || CollectionUtils.isEmpty(vo.getGroupIds())
            || Objects.isNull(vo.getEnable())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        if (StringUtils.length(vo.getRefrigeratorCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("冰箱编码过长");
        }

        // 检查 参数
        checkVoWhenAddOrUpdate(vo);
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final ArchiveStoreDto archiveStoreDto = archiveStoreService.selectByArchiveStoreId(vo.getArchiveStoreId());
        if (Objects.isNull(archiveStoreDto)) {
            throw new LimsException("归档库不存在");
        }
        final List<ProfessionalGroupDto> professionalGroupDtos = groupService.selectByGroupIds(vo.getGroupIds());
        List<Long> selectGroupIds =
            professionalGroupDtos.stream().map(ProfessionalGroupDto::getGroupId).collect(Collectors.toList());
        if (vo.getGroupIds().stream().anyMatch(x -> !selectGroupIds.contains(x))) {
            throw new IllegalArgumentException("存在无效专业组");
        }
        if (Objects
            .nonNull(refrigeratorService.selectByRefrigeratorCode(vo.getRefrigeratorCode(), loginUser.getOrgId()))) {
            throw new LimsException("当前冰箱编码已存在");
        }
        if (Objects
            .nonNull(refrigeratorService.selectByRefrigeratorName(vo.getRefrigeratorName(), loginUser.getOrgId()))) {
            throw new LimsException("当前冰箱名称已存在");
        }

        // 转换
        RefrigeratorDto target = JSON.parseObject(JSON.toJSONString(vo), RefrigeratorDto.class);

        target.setArchiveStoreName(archiveStoreDto.getArchiveStoreName());

        long refrigeratorId = refrigeratorService.addRefrigerator(target, professionalGroupDtos);

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.ARCHIVE_STORE_LOG.getDesc()).setContent(String
                .format("归档库 [%s] 下新增冰箱 [%s] ", archiveStoreDto.getArchiveStoreName(), target.getRefrigeratorName()))
                .toJSONString());

        return Map.of("id", refrigeratorId);

    }

    /**
     * 冰箱 删除
     */
    @PostMapping("/delete")
    public Object refrigeratorDelete(@RequestBody Set<Long> refrigeratorIds) {
        if (CollectionUtils.isEmpty(refrigeratorIds)) {
            return Collections.emptyMap();
        }

        // 检查冰箱 是否在 样本归档中被使用
        if (rackArchiveService.checkRefrigeratorUseByIds(refrigeratorIds)) {
            throw new LimsException("冰箱中存在归档试管架,不可删除");
        }
        List<RefrigeratorDto> refrigeratorDtos = refrigeratorService.selectByRefrigeratorIds(refrigeratorIds);

        refrigeratorService.deleteByRefrigeratorIds(refrigeratorIds);

        Set<Long> archiveStoreIds =
            refrigeratorDtos.stream().map(RefrigeratorDto::getArchiveStoreId).collect(Collectors.toSet());
        Map<Long, String> archiveStoreNameById = archiveStoreService.selectByArchiveStoreIds(archiveStoreIds).stream()
            .collect(Collectors.toMap(ArchiveStoreDto::getArchiveStoreId, ArchiveStoreDto::getArchiveStoreName));

        refrigeratorDtos.forEach(item -> {
            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.ARCHIVE_STORE_LOG.getDesc())
                    .setContent(String.format("归档库 [%s] 下删除冰箱 [%s] ",
                        archiveStoreNameById.getOrDefault(item.getArchiveStoreId(), ""), item.getRefrigeratorName()))
                    .toJSONString());

        });
        return Collections.emptyMap();
    }

    /**
     * 冰箱 修改
     */
    @PostMapping("/update")
    public Object refrigeratorUpdate(@RequestBody RefrigeratorUpdateRequestVo vo) {
        if (StringUtils.isBlank(vo.getRefrigeratorName()) || CollectionUtils.isEmpty(vo.getGroupIds())
            || Objects.isNull(vo.getEnable()) || Objects.isNull(vo.getRefrigeratorId())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        // 判断 冰箱 是否存在
        RefrigeratorDto temp = refrigeratorService.selectByRefrigeratorId(vo.getRefrigeratorId());
        if (Objects.isNull(temp)) {
            throw new LimsException("冰箱不存在");
        }
        RefrigeratorAllInfoDto refrigeratorDtoNow = refrigeratorConverter.refrigeratorAllInfoDtoFromTbDto(temp);
        ArchiveStoreDto archiveStoreDto =
            archiveStoreService.selectByArchiveStoreId(refrigeratorDtoNow.getArchiveStoreId());
        if (Objects.isNull(archiveStoreDto)) {
            throw new LimsException("冰箱关联归档库不存在");
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final List<ProfessionalGroupDto> professionalGroupDtos = groupService.selectByGroupIds(vo.getGroupIds());
        List<Long> selectGroupIds =
            professionalGroupDtos.stream().map(ProfessionalGroupDto::getGroupId).collect(Collectors.toList());
        Map<Long, ProfessionalGroupDto> groupToMapByGroupId = professionalGroupDtos.stream()
            .collect(Collectors.toMap(ProfessionalGroupDto::getGroupId, Function.identity()));
        if (vo.getGroupIds().stream().anyMatch(x -> !selectGroupIds.contains(x))) {
            throw new IllegalArgumentException("存在无效专业组");
        }
        Map<Long, String> groupNameByGroupId = new HashMap<>(professionalGroupDtos.stream()
            .collect(Collectors.toMap(ProfessionalGroupDto::getGroupId, ProfessionalGroupDto::getGroupName)));

        final RefrigeratorDto selectByRefrigeratorName =
            refrigeratorService.selectByRefrigeratorName(vo.getRefrigeratorName(), loginUser.getOrgId());
        if (Objects.nonNull(selectByRefrigeratorName)
            && !Objects.equals(vo.getRefrigeratorId(), selectByRefrigeratorName.getRefrigeratorId())) {
            throw new LimsException("当前冰箱名称已存在");
        }

        // 现有 冰箱专业组关联表
        List<RefrigeratorGroupDto> refrigeratorGroupDtoListNow =
            refrigeratorGroupService.selectByRefrigeratorId(vo.getRefrigeratorId());

        List<Long> relationGroupList =
            refrigeratorGroupDtoListNow.stream().map(RefrigeratorGroupDto::getGroupId).collect(Collectors.toList());

        // 本次 需要 删除 的专业组 连接 id
        List<Long> deleteRelationIdList =
            refrigeratorGroupDtoListNow.stream().filter(obj -> !vo.getGroupIds().contains(obj.getGroupId()))
                .map(RefrigeratorGroupDto::getRelationId).collect(Collectors.toList());
        List<Long> deleteRelationGroupIdList =
            refrigeratorGroupDtoListNow.stream().map(RefrigeratorGroupDto::getGroupId)
                .filter(groupId -> !vo.getGroupIds().contains(groupId)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(deleteRelationGroupIdList)) {
            groupNameByGroupId.putAll(groupService.selectByGroupIds(deleteRelationGroupIdList).stream()
                .collect(Collectors.toMap(ProfessionalGroupDto::getGroupId, ProfessionalGroupDto::getGroupName)));
        }

        // 本次 需要 添加 的专业组
        List<ProfessionalGroupDto> addGroupList = vo.getGroupIds().stream().filter(x -> !relationGroupList.contains(x))
            .map(groupToMapByGroupId::get).collect(Collectors.toList());

        final RefrigeratorAllInfoDto target = new RefrigeratorAllInfoDto();
        BeanUtils.copyProperties(refrigeratorDtoNow, target);

        // 更新项
        target.setRefrigeratorName(vo.getRefrigeratorName());
        target.setPosition(vo.getPosition());
        target.setTemperature(vo.getTemperature());
        target.setEnable(vo.getEnable());

        refrigeratorService.refrigeratorUpdate(refrigeratorConverter.refrigeratorDtoFromTbObj(target), addGroupList,
            deleteRelationIdList);

        target.setGroupNameAllStr(
            professionalGroupDtos.stream().sorted(Comparator.comparing(ProfessionalGroupDto::getGroupId))
                .map(ProfessionalGroupDto::getGroupName).collect(Collectors.joining(",")));
        refrigeratorDtoNow.setGroupNameAllStr(
            refrigeratorGroupDtoListNow.stream().sorted(Comparator.comparing(RefrigeratorGroupDto::getGroupId))
                .map(obj -> groupNameByGroupId.getOrDefault(obj.getGroupId(), "")).collect(Collectors.joining(",")));

        String compare = new CompareUtils<RefrigeratorAllInfoDto>().compare(refrigeratorDtoNow, target);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.ARCHIVE_STORE_LOG.getDesc())
                    .setContent(String.format("修改归档库 [%s] 下冰箱: [%s]", archiveStoreDto.getArchiveStoreName(), compare))
                    .toJSONString());
        }

        return Collections.emptyMap();

    }

    /**
     * 冰箱 获取 根据 归档库
     */
    @PostMapping("/select-by-archive-store")
    public Object selectByArchiveStore(@RequestParam long archiveStoreId) {
        final ArchiveStoreDto archiveStoreDto = archiveStoreService.selectByArchiveStoreId(archiveStoreId);
        if (Objects.isNull(archiveStoreDto)) {
            throw new LimsException("归档库不存在");
        }
        List<RefrigeratorDto> refrigeratorDtos = refrigeratorService.selectByArchiveStoreId(archiveStoreId);
        Set<Long> refrigeratorIds =
            refrigeratorDtos.stream().map(RefrigeratorDto::getRefrigeratorId).collect(Collectors.toSet());

        // 所挂 所有冰箱专业组
        List<RefrigeratorGroupDto> refrigeratorGroupDtoList =
            refrigeratorGroupService.selectByRefrigeratorIds(refrigeratorIds);
        Set<Long> groupIds =
            refrigeratorGroupDtoList.stream().map(RefrigeratorGroupDto::getGroupId).collect(Collectors.toSet());
        Map<Long, Set<Long>> groupIdsGroupingByRefrigeratorId =
            refrigeratorGroupDtoList.stream().collect(Collectors.groupingBy(RefrigeratorGroupDto::getRefrigeratorId,
                Collectors.mapping(RefrigeratorGroupDto::getGroupId, Collectors.toSet())));
        List<ProfessionalGroupDto> professionalGroupDtos = groupService.selectByGroupIds(groupIds);
        Map<Long, String> groupNameByGroupId = professionalGroupDtos.stream()
            .collect(Collectors.toMap(ProfessionalGroupDto::getGroupId, ProfessionalGroupDto::getGroupName));

        List<RefrigeratorAllInfoDto> targetList = Lists.newArrayListWithCapacity(refrigeratorDtos.size());
        for (RefrigeratorDto item : refrigeratorDtos) {
            RefrigeratorAllInfoDto temp = JSON.parseObject(JSON.toJSONString(item), RefrigeratorAllInfoDto.class);
            Set<Long> mappingGroupIds = ObjectUtils
                .defaultIfNull(groupIdsGroupingByRefrigeratorId.get(item.getRefrigeratorId()), Collections.emptySet());

            List<String> mappingGroupNames =
                mappingGroupIds.stream().map(groupNameByGroupId::get).collect(Collectors.toList());
            String groupNameAllStr = String.join(",", mappingGroupNames);
            temp.setGroupNameAllStr(groupNameAllStr);
            temp.setGroupIds(mappingGroupIds);
            targetList.add(temp);
        }

        return targetList;
    }

    /**
     * 冰箱 获取 根据登录 专业组
     */
    @PostMapping("/select-by-group")
    public Object selectByGroup() {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        final Long groupId = loginUser.getGroupId();

        List<RefrigeratorGroupDto> refrigeratorGroupDtoList = refrigeratorGroupService.selectByGroupId(groupId);
        Set<Long> refrigeratorIds =
            refrigeratorGroupDtoList.stream().map(RefrigeratorGroupDto::getRefrigeratorId).collect(Collectors.toSet());

        return refrigeratorService.selectByRefrigeratorIds(refrigeratorIds).stream()
            .filter(obj -> Objects.equals(obj.getEnable(), YesOrNoEnum.YES.getCode())).collect(Collectors.toList());
    }

    /**
     * 检查 冰箱 新增 或 修改 参数 公共部分
     *
     */
    private <T extends RefrigeratorAddRequestVo> void checkVoWhenAddOrUpdate(T vo) {
        if (StringUtils.length(vo.getRefrigeratorName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("冰箱名称过长");
        }
        if (StringUtils.isNotBlank(vo.getPosition()) && StringUtils.length(vo.getPosition()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("冰箱位置过长");
        }
        if (StringUtils.isNotBlank(vo.getTemperature()) && StringUtils.length(vo.getTemperature()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("温度过长");
        }
        if (!(Objects.equals(vo.getEnable(), YesOrNoEnum.NO.getCode())
            || Objects.equals(vo.getEnable(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("是否启用参数错误");
        }
    }

}
