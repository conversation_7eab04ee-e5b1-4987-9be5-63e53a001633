package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * 冰箱 新增 请求 Vo
 * 
 * <AUTHOR>
 * @since 2023/4/3 11:41
 */
@Getter
@Setter
public class RefrigeratorAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 归档库ID
     */
    private Long archiveStoreId;
    /**
     * 冰箱编码
     */
    private String refrigeratorCode;
    /**
     * 冰箱名称
     */
    private String refrigeratorName;
    /**
     * 位置
     */
    private String position;
    /**
     * 温度
     */
    private String temperature;
    /**
     * 专业组IDs
     */
    private Set<Long> groupIds;
    /**
     * 是否启用(0未启用 1启用)
     * @see YesOrNoEnum
     */
    private Integer enable;
}
