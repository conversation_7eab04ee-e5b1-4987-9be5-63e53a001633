package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.InstrumentMedicineDto;
import com.labway.lims.base.model.TbInstrumentMedicine;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 仪器药物 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface InstrumentMedicineConverter {

    InstrumentMedicineDto instrumentMedicineDtoFromTbObj(TbInstrumentMedicine obj);
    List<InstrumentMedicineDto> instrumentMedicineDtosFromTbObj(List<TbInstrumentMedicine> list);



    TbInstrumentMedicine tbInstrumentMedicineFromTbObjDto(InstrumentMedicineDto obj);
    List<TbInstrumentMedicine> tbInstrumentMedicinesFromTbObjDto(List<InstrumentMedicineDto> list);

}
