package com.labway.lims.base.vo;

import com.labway.lims.base.api.enums.FileTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 添加文件
 */
@Getter
@Setter
public class FileAddVo {

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 1: 文件 2: 文件夹
     *
     * @see FileTypeEnum
     */
    private Integer fileType;

    /**
     * 地址
     */
    private String url;

    /**
     * 父文件ID
     */
    private Long parentFileId;

    /**
     * 文件大小
     */
    private Long fileSize;
}
