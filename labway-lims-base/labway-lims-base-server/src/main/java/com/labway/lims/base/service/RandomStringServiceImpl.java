package com.labway.lims.base.service;

import com.labway.lims.base.api.service.RandomStringService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.ClassPathResource;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@DubboService
class RandomStringServiceImpl implements RandomStringService, InitializingBean {

    private final List<String> words = new ArrayList<>();

    @Override
    public char randomChinese() {
        final String word = words.get(RandomUtils.nextInt(0, words.size()));
        return word.charAt(RandomUtils.nextInt(0, word.length()));
    }

    @Override
    public String randomChineseString() {
        final StringBuilder sb = new StringBuilder();
        for (int i = 0; i < RandomUtils.nextInt(2, 5); i++) {
            sb.append(randomChinese());
        }
        return sb.toString();
    }

    @Override
    public void afterPropertiesSet() throws Exception {

        final ClassPathResource resource = new ClassPathResource("cncity_fulllist.txt");

        if (resource.exists()) {
            for (String e : IOUtils.readLines(resource.getInputStream(), StandardCharsets.UTF_8)) {
                words.add(e.split(" ")[1]);
            }
        } else {
            throw new IllegalStateException("cncity_fulllist.txt");
        }

    }
}
