package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.service.InstrumentGroupInstrumentService;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentGroupTestItemService;
import com.labway.lims.base.mapper.TbInstrumentGroupMapper;
import com.labway.lims.base.model.TbInstrumentGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/21 17:07
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "instrument-group")
public class InstrumentGroupServiceImpl implements InstrumentGroupService {
    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private TbInstrumentGroupMapper tbInstrumentGroupMapper;
    @Resource
    private InstrumentGroupInstrumentService instrumentGroupInstrumentService;
    @Resource
    private InstrumentGroupTestItemService instrumentGroupTestItemService;

    @Override
    @CacheEvict(allEntries = true)
    public long addInstrumentGroup(InstrumentGroupDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        if (Objects.isNull(dto.getSampleEndValue())) {
            //默认不传结束样本号的情况下，long的最大值，防止后续比较的时候报错
            dto.setSampleEndValue(String.valueOf(Integer.MAX_VALUE));
        }

        final TbInstrumentGroup tbInstrumentGroup = JSON.parseObject(JSON.toJSONString(dto), TbInstrumentGroup.class);
        tbInstrumentGroup.setInstrumentGroupId(ObjectUtils.defaultIfNull(dto.getInstrumentGroupId(), snowflakeService.genId()));
        tbInstrumentGroup.setOrgId(user.getOrgId());
        tbInstrumentGroup.setCreatorId(user.getUserId());
        tbInstrumentGroup.setCreatorName(user.getNickname());
        tbInstrumentGroup.setCreateDate(new Date());
        tbInstrumentGroup.setUpdaterId(user.getUserId());
        tbInstrumentGroup.setUpdaterName(user.getNickname());
        tbInstrumentGroup.setUpdateDate(new Date());
        tbInstrumentGroup.setIsDelete(YesOrNoEnum.NO.getCode());

        final List<InstrumentGroupDto> groups = selectByGroupId(dto.getGroupId());

        if (CollectionUtils.containsAny(groups.stream().map(InstrumentGroupDto::getSecondSortColor).collect(Collectors.toList()), dto.getSecondSortColor())) {
            throw new IllegalStateException("该专业组下的专业小组颜色重复");
        }

        if (CollectionUtils.containsAny(groups.stream().map(InstrumentGroupDto::getInstrumentGroupCode).collect(Collectors.toList()), dto.getInstrumentGroupCode())
                || CollectionUtils.containsAny(groups.stream().map(InstrumentGroupDto::getInstrumentGroupName).collect(Collectors.toList()), dto.getInstrumentGroupName())) {
            throw new IllegalStateException("该专业组下的专业小组名称或者编码重复");
        }

        for (InstrumentGroupDto groupDto : groups) {
            if (NumberUtils.toLong(groupDto.getSampleStartValue()) <= NumberUtils.toLong(dto.getSampleEndValue())
                    && NumberUtils.toLong(dto.getSampleStartValue()) <= NumberUtils.toLong(groupDto.getSampleEndValue())) {
                throw new IllegalStateException(String.format("样本号范围与专业小组 [%s] 冲突", groupDto.getInstrumentGroupName()));
            }
        }

        if (tbInstrumentGroupMapper.insert(tbInstrumentGroup) < 1) {
            throw new IllegalStateException("添加专业组失败");
        }

        log.info("用户 [{}] 添加专业小组成功 [{}]", user.getNickname(), JSON.toJSONString(tbInstrumentGroup));

        return tbInstrumentGroup.getInstrumentGroupId();

    }


    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByInstrumentGroupId(InstrumentGroupDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final TbInstrumentGroup instrumentGroup = JSON.parseObject(JSON.toJSONString(dto), TbInstrumentGroup.class);
        instrumentGroup.setUpdaterId(user.getUserId());
        instrumentGroup.setUpdaterName(user.getNickname());

        final List<InstrumentGroupDto> groupDtos = selectByGroupId(dto.getGroupId()).stream().filter(e -> !Objects.equals(e.getInstrumentGroupId(), dto.getInstrumentGroupId())).collect(Collectors.toList());

        if (CollectionUtils.containsAny(groupDtos.stream().map(InstrumentGroupDto::getSecondSortColor).collect(Collectors.toList()), dto.getSecondSortColor())) {
            throw new IllegalStateException("该专业组下的颜色重复,请重新选择");
        }
        if (CollectionUtils.containsAny(groupDtos.stream().map(InstrumentGroupDto::getInstrumentGroupCode).collect(Collectors.toList()), dto.getInstrumentGroupCode())
        ) {
            throw new IllegalStateException("该专业组下的名称重复,修改失败");
        }
        for (InstrumentGroupDto groupDto : groupDtos) {

            if (NumberUtils.toLong(groupDto.getSampleStartValue()) <= NumberUtils.toLong(dto.getSampleEndValue()) && NumberUtils.toLong(dto.getSampleStartValue()) <= NumberUtils.toLong(groupDto.getSampleEndValue())) {
                throw new IllegalStateException("该专业组下的样本号不能有交集,修改失败");
            }
        }

        if (tbInstrumentGroupMapper.updateById(instrumentGroup) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改专业小组成功 [{}]", user.getNickname(), JSON.toJSONString(dto));
        return true;
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByInstrumentGroupId(long instrumentGroupId) {

        if (CollectionUtils.isNotEmpty(instrumentGroupInstrumentService.selectByInstrumentGroupId(instrumentGroupId))) {
            throw new IllegalStateException("专业小组下有仪器，无法删除");
        }

        return tbInstrumentGroupMapper.deleteById(instrumentGroupId) > 0;
    }

    @Override
    @Nullable
    @Cacheable(key = "'selectByInstrumentGroupId:'+#instrumentGroupId")
    public InstrumentGroupDto selectByInstrumentGroupId(long instrumentGroupId) {
        return convert(tbInstrumentGroupMapper.selectById(instrumentGroupId));
    }

    @Nullable
    @Override
    public InstrumentGroupDto selectByInstrumentGroupCode(String instrumentGroupCode, long orgId) {
        if (StringUtils.isBlank(instrumentGroupCode)) {
            return null;
        }
        return convert(tbInstrumentGroupMapper.selectOne(new LambdaQueryWrapper<TbInstrumentGroup>()
                .eq(TbInstrumentGroup::getInstrumentGroupCode, instrumentGroupCode)
                .eq(TbInstrumentGroup::getOrgId, orgId)
                .last("limit 1")));
    }

    @Override
    public List<InstrumentGroupDto> selectByInstrumentGroupIds(Collection<Long> instrumentGroupIds) {
        if (CollectionUtils.isEmpty(instrumentGroupIds)) {
            return Collections.emptyList();
        }
        return tbInstrumentGroupMapper.selectList(new LambdaQueryWrapper<TbInstrumentGroup>()
                        .in(TbInstrumentGroup::getInstrumentGroupId, instrumentGroupIds)
                        .orderByDesc(TbInstrumentGroup::getInstrumentGroupId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Nonnull
    @Cacheable(key = "'selectByGroupId:'+#groupId")
    public List<InstrumentGroupDto> selectByGroupId(long groupId) {
        final LambdaQueryWrapper<TbInstrumentGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentGroup::getGroupId, groupId)
                .orderByDesc(TbInstrumentGroup::getInstrumentGroupId);
        final List<TbInstrumentGroup> list = tbInstrumentGroupMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<InstrumentGroupDto> selectByGroupIds(Collection<Long> groupIds) {

        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbInstrumentGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbInstrumentGroup::getGroupId, groupIds)
                .orderByDesc(TbInstrumentGroup::getInstrumentGroupId);
        final List<TbInstrumentGroup> list = tbInstrumentGroupMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public List<InstrumentGroupDto> selectAll() {

        final List<TbInstrumentGroup> groups = tbInstrumentGroupMapper.selectList(new LambdaQueryWrapper<TbInstrumentGroup>()
                .eq(TbInstrumentGroup::getOrgId, LoginUserHandler.get().getOrgId())
                .orderByDesc(TbInstrumentGroup::getInstrumentGroupId));

        return groups.stream().map(this::convert).collect(Collectors.toList());
    }


    private InstrumentGroupDto convert(TbInstrumentGroup tbInstrumentGroup) {
        if (Objects.isNull(tbInstrumentGroup)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(tbInstrumentGroup), InstrumentGroupDto.class);
    }

}
