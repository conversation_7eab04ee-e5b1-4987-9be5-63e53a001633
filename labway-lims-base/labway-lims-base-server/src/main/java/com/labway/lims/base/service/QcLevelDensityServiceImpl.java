package com.labway.lims.base.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.InstrumentItemTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.enums.*;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.QcLevelDensityService;
import com.labway.lims.base.api.service.QcSampleResultService;
import com.labway.lims.base.mapper.TbQcSetRecordItemMapper;
import com.labway.lims.base.model.TbQcSetRecordItem;
import com.labway.lims.base.utils.MyQCNumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/11/3 15:20
 * @Version 1.0
 */
@DubboService
@Slf4j
public class QcLevelDensityServiceImpl implements QcLevelDensityService {

    @Resource
    private InstrumentReportItemService instrumentReportItemService;

    @Resource
    private TbQcSetRecordItemMapper recordItemMapper;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private QcSampleResultService sampleResultService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveQcRecord(QcAddRecordInfoDto saveQcRecordInfoDto) {
        //1. 关键数据判断
        if (Objects.isNull(saveQcRecordInfoDto)) {
            return;
        }
        //2.业务数据判断
        //2.1 判断报告项目是否存在
        final Long reportId = saveQcRecordInfoDto.getReportId();
        if (Objects.isNull(reportId)) {
            throw new IllegalStateException("请选择质控项目");
        }
        InstrumentReportItemDto reportItemDto = instrumentReportItemService.selectByInstrumentReportItemId(saveQcRecordInfoDto.getReportId());
        if (Objects.isNull(reportItemDto)) {
            throw new IllegalStateException("报告项目不存在，操作失败");
        }

        //2.2 判断编码类型,  02 是仪器项目类型定性的编码  如果是定性检查对应的属性
        final boolean isQualitative = Objects.equals(reportItemDto.getItemTypeCode(), InstrumentItemTypeEnum.QUALITATIVE.getCode());
        if (isQualitative) {
            checkQualitativeParam(saveQcRecordInfoDto);
        }

        //TODO 在新表中该仪器质控设置 tb_instrument_qc_setting已被移除
//        InstrumentQcSettingDto qcSettingDto = qcSettingService.selectByCustomerReportItemId(saveQcRecordInfoDto.getReportId());
//        if (qcSettingDto == null) {
//            //请先设置仪器质控信息
//            throw new RegionLisWithErrorCodeException(QcErrorCode.QC100020);
//        }

        //2.3 判断当前保存的质控类型是否与仪器的质控类型匹配: 对于同一个报告项目的质控水平浓度信息中的定型或半定性只能有一种(不为空时);即多个质控类型应该保持一致
        List<QcSetRecordItemDto> qcSetRecordItems = querySetRecordItemListReportItemId(saveQcRecordInfoDto.getReportId());
        final Integer qualitativeType = qcSetRecordItems.stream()
                .filter(f -> !Objects.equals(saveQcRecordInfoDto.getQcRecordBatch(), f.getQcRecordBatch()))
                .findFirst().map(QcSetRecordItemDto::getQualitativeType).orElse(null);
        if (Objects.nonNull(qualitativeType) && isQualitative) {

            final QualitativeTypeEnum qualitativeTypeEnum = QualitativeTypeEnum.selectByCode(qualitativeType);
            final QualitativeTypeEnum saveTypeEnum =
                    ObjectUtils.defaultIfNull(QualitativeTypeEnum.selectByCode(saveQcRecordInfoDto.getQualitativeType())
                            , QualitativeTypeEnum.QUALITATIVE);

            if (Objects.nonNull(qualitativeTypeEnum) && BooleanUtils.isNotTrue(Objects.equals(qualitativeTypeEnum, saveTypeEnum))) {
                throw new IllegalStateException(String.format("不可保存类型 [%s]，请选择类型 [%s]", saveTypeEnum.getValue(),
                        qualitativeTypeEnum.getValue()));
            }
        }

        //2.4 判断是否有交集的时间段
        final boolean hasMixedDate = qcSetRecordItems.stream().anyMatch(a -> {
            ZoneId zoneId = ZoneId.systemDefault();
            final LocalDateTime itemEnd = a.getQcEndDate().toInstant().atZone(zoneId).toLocalDateTime();
            final LocalDateTime itemStart = a.getQcStartDate().toInstant().atZone(zoneId).toLocalDateTime();
            final LocalDateTime start = saveQcRecordInfoDto.getQcStartDate();
            final LocalDateTime end = saveQcRecordInfoDto.getQcEndDate();
            return hasMixedDate(itemStart, itemEnd, start, end) && (!Objects.equals(saveQcRecordInfoDto.getQcRecordBatch(), a.getQcRecordBatch()));
        });

        if (BooleanUtils.isTrue(hasMixedDate)) {
            throw new IllegalStateException("质控时间段不能重叠");
        }

        //水平只能用一个 不能重复 质控批量的有效时间需要匹配
        Set<Integer> sortSet = new HashSet<>();
        for (QcAddSetRecordItemDto saveQcSetRecordItemDto : saveQcRecordInfoDto.getRecordList()) {
            if (Objects.isNull(saveQcSetRecordItemDto.getQcBatchId())) {
                if (sortSet.contains(saveQcSetRecordItemDto.getLevelCode())) {
                    //浓度重复
                    String errMsg = "存在相同浓度【" + QcSettingLevelEnum.getEnumByInt(saveQcSetRecordItemDto.getLevelCode()).getStrCode() + "】请重新设置";
                    throw new IllegalStateException(QcErrorCode.QC100019 + " : " + errMsg);
                }
                sortSet.add(saveQcSetRecordItemDto.getLevelCode());
            }
        }

        //先判断是新增还是更新
        if (StringUtils.isBlank(saveQcRecordInfoDto.getQcRecordBatch())) {
            // todo do someting
        } else {
            //1、直接删除原来的数据
            deleteInfoByRecordBatch(saveQcRecordInfoDto.getQcRecordBatch(), false);
        }
        //赋值新增信息
        LoginUserHandler.User user = LoginUserHandler.get();
        InstrumentReportItemDto instrumentReportItemDto = instrumentReportItemService.selectByInstrumentReportItemId(saveQcRecordInfoDto.getReportId());
        List<QcSetRecordItemDto> items = createInsertInfos(saveQcRecordInfoDto, instrumentReportItemDto, user);
        recordItemMapper.insertBatchRecordItem(items);

        // 如果通道码对不上 就同步修改下通道码
//        if (!Objects.equals(qcSettingDto.getInstrumentChannel(), reportItemDto.getInstrumentChannel())) {
//            qcDal.updateChannel(qcSettingDto.getInstrumentQcId(), customerReportItem.getInstrumentChannel());
//        }
        //更新通道码
        final LambdaUpdateWrapper<TbQcSetRecordItem> updateItem = Wrappers.lambdaUpdate(TbQcSetRecordItem.class)
                .set(TbQcSetRecordItem::getInstrumentChannel, reportItemDto.getInstrumentChannel())
                .eq(TbQcSetRecordItem::getQcRecordItemId, reportItemDto.getInstrumentReportItemId());
        recordItemMapper.update(null, updateItem);
        // 添加日志
//        saveUpdateRecordFlow(saveQcRecordInfoDto);
    }


    /**
     * 删除质控设置: 根据质控记录批号来删除数据
     *
     * @param recordBatch
     * @param isAddLog
     */
    public void deleteInfoByRecordBatch(String recordBatch, boolean isAddLog) {
        // 先查询旧数据以防数据已被删除
        List<TbQcSetRecordItem> tbQcSetRecordItems = selectQcSettingRecordItem(recordBatch);
        int result = deleteSetRecordItemByRecordBatch(recordBatch);
        if(result == 0){
            throw new IllegalStateException("数据不存在 或 删除异常");
        }
        //TODO 日志信息待处理
//        if (result > CommonConstant.INT_ZERO && isAddLog) {
//            try {
//                if (!CollectionUtils.isEmpty(tbQcSetRecordItems)) {
//                    // 获取报告项目
//                    QcLogDto qcLogDto = new QcLogDto();
//                    // 浓度1、2、3的设置
//                    TbQcSetRecordItem concentrationOneSetting = tbQcSetRecordItems.stream().filter(i -> CommonConstant.INT_ONE == i.getSort()).findFirst().orElseGet(TbQcSetRecordItem::new);
//                    TbQcSetRecordItem concentrationTwoSetting = tbQcSetRecordItems.stream().filter(i -> CommonConstant.INT_TWO == i.getSort()).findFirst().orElseGet(TbQcSetRecordItem::new);
//                    TbQcSetRecordItem concentrationThreeSetting = tbQcSetRecordItems.stream().filter(i -> CommonConstant.INT_THREE == i.getSort()).findFirst().orElseGet(TbQcSetRecordItem::new);
//
//                    QcLogTypeEnum qcDataSettingsLevelConcentrationDelete = QcLogTypeEnum.QC_DATA_SETTINGS_LEVEL_CONCENTRATION_DELETE;
//                    qcDataSettingsLevelConcentrationDelete.contentFill(
//                            // 浓度1
//                            concentrationOneSetting.getQcBatchId(),
//                            // 浓度2
//                            concentrationTwoSetting.getQcBatchId(),
//                            // 浓度3
//                            concentrationThreeSetting.getQcBatchId()
//                    );
//                    qcLogDto.setQcBatchId(tbQcSetRecordItems.stream().map(TbQcSetRecordItem::getQcBatchId).collect(Collectors.joining(",")));
//                    TbQcSetRecordItem tbQcSetRecordItem = tbQcSetRecordItems.stream().findFirst().orElseGet(TbQcSetRecordItem::new);
//
//                    qcLogDto.setInstrumentId(tbQcSetRecordItem.getInstrumentId());
//                    qcLogDto.setInstrumentName(tbQcSetRecordItem.getInstrumentName());
//
//                    final CustomerReportItemDto customerReportItemDto = iCustomerReportItemService.selectByInstrumentIdAndInstrumentChannel(tbQcSetRecordItem.getInstrumentId(), tbQcSetRecordItem.getInstrumentChannel());
//                    CustomerReportItemDto one = ObjectUtils.defaultIfNull(customerReportItemDto, new CustomerReportItemDto());
//                    qcLogDto.setProjectId(one.getCustomerReportItemId());
//                    qcLogDto.setProjectId(one.getCustomerReportItem());
//                    qcLogDto.setInstrumentChannel(one.getInstrumentChannel());
//                    qcLogDto.setQcLogTypeEnum(qcDataSettingsLevelConcentrationDelete);
//
//
//                    // 日志
//                    iInstrumentQcFlowService.insertQcLogFlow(qcLogDto);
//                }
//            } catch (Exception e) {
//                log.info("保存日志出现了异常 原因：{}", e.getMessage());
//            }
//        }
    }

    @Override
    public void calculateSampleRecord(RecordCalculateDto calculateDto) {
        //TODO
        if (calculateDto != null) {
            //根据报告编码获取结果记录
//            List<QcSampleResultDto> sampleResultDtoList = sampleResultService.selectAuditResultByCustomerReportItemId(calculateDto.getCustomerReportId());
//                    calculateDto.getStartTime(), calculateDto.getEndTime());
//            //没有结果报错 或者
//            if (CollectionUtils.isEmpty(sampleResultDtoList)) {
//                throw new RegionLisWithErrorCodeException(QcErrorCode.QC100023);
//            }
//            //只有一个结果需要报错，因为这样标准差就会为0
//            if (sampleResultDtoList.size() == 1) {
//                throw new RegionLisWithErrorCodeException(QcErrorCode.QC100030);
//            }
//
//            //获取仪器当前有效的样本号
//            List<QcInstrumentSampleSettingDto> sampleSettingDtoList = sampleSettingService.querySettingListByInstrumentId(sampleResultDtoList.get(0).getInstrumentId());
//            //没有仪器样本配置要报错
//            if (CollectionUtils.isEmpty(sampleSettingDtoList)) {
//                throw new RegionLisWithErrorCodeException(QcErrorCode.QC100024);
//            }
//
//            sampleSettingDtoList = sampleSettingDtoList.stream().filter(f -> Objects.equals(f.getCustomerReportItemId(), calculateDto.getCustomerReportId())).collect(Collectors.toList());
//            //没有仪器样本配置要报错
//            if (CollectionUtils.isEmpty(sampleSettingDtoList)) {
//                throw new RegionLisWithErrorCodeException(QcErrorCode.QC100024);
//            }
//
//            //根据记录批号 获取对应的浓度设置
//            List<QcSetRecordItemDto> qcSetRecordItemDtoList = queryListByRecordBatch(calculateDto.getQcRecordBatch());
//            if (CollectionUtils.isEmpty(qcSetRecordItemDtoList)) {
//                //没有记录批号 报错
//                throw new RegionLisWithErrorCodeException(QcErrorCode.QC100025);
//            }
//
//            //当前浓度对应的样本号
//            Map<Integer, String> sampleNoMap = new HashMap<>();
//            for (QcInstrumentSampleSettingDto sampleSettingDto : sampleSettingDtoList) {
//                sampleNoMap.put(sampleSettingDto.getSort(), sampleSettingDto.getQcSampleNo());
//            }
//
//            //对结果进行筛选  浓度+样本号进行筛选
//            Map<Integer, List<QcSampleResultDto>> resultDtoMap = new HashMap<>();
//            //结果和
//            Map<Integer, Double> resultSumMap = new HashMap<>();
//            for (QcSampleResultDto sampleResultDto : sampleResultDtoList) {
//                if (sampleNoMap.containsKey(sampleResultDto.getSort()) && sampleResultDto.getQcSampleNo().equals(sampleNoMap.get(sampleResultDto.getSort()))) {
//                    //浓度+样本号都匹配上
//                    if (!resultDtoMap.containsKey(sampleResultDto.getSort())) {
//                        List<QcSampleResultDto> resultDtos = new ArrayList<>();
//                        resultDtoMap.put(sampleResultDto.getSort(), resultDtos);
//                        resultSumMap.put(sampleResultDto.getSort(), 0D);
//                    }
//                    resultDtoMap.get(sampleResultDto.getSort()).add(sampleResultDto);
//
//                    //加和
//                    double sum = resultSumMap.get(sampleResultDto.getSort()) + Double.parseDouble(sampleResultDto.getQcResult());
//                    resultSumMap.put(sampleResultDto.getSort(), sum);
//                }
//            }
//            //没有结果报错
//            if (resultDtoMap.size() == 0) {
//                throw new RegionLisWithErrorCodeException(QcErrorCode.QC100023);
//            }
//
//            //算靶值
//            Map<Integer, Double> targetValueMap = new HashMap<>();
//            for (int sort : resultSumMap.keySet()) {
//                if (resultDtoMap.containsKey(sort)) {
////                    double targetValue = resultSumMap.get(sort) / (1000 * resultDtoMap.get(sort).size());
//                    double targetValue = resultSumMap.get(sort) / (resultDtoMap.get(sort).size());
//                    targetValueMap.put(sort, targetValue);
//                }
//            }
//
//            //计算标准差
//            Map<Integer, Double> standardValueMap = new HashMap<>();
//            for (Integer sort : resultDtoMap.keySet()) {
//                if (targetValueMap.containsKey(sort)) {
//                    double sum = 0;
//                    List<QcSampleResultDto> resultDtoList = resultDtoMap.get(sort);
//                    for (QcSampleResultDto resultDto : resultDtoList) {
//                        sum += Math.pow((Double.parseDouble(resultDto.getQcResult()) - targetValueMap.get(sort)), 2);
//                    }
//                    //计算平均值并且开根号
//                    double sqrtValue = Math.sqrt(sum / resultDtoList.size());
//                    standardValueMap.put(sort, sqrtValue);
//                }
//            }
//
//            //计算变异系数
//            Map<Integer, Double> cvValueMap = new HashMap<>();
//            for (Integer sort : standardValueMap.keySet()) {
//                if (standardValueMap.get(sort) != 0 && targetValueMap.get(sort) != 0) {
//                    cvValueMap.put(sort, (standardValueMap.get(sort) / targetValueMap.get(sort)));
//                }
//            }
//
//            //匹配后更新到结果中去
//            for (QcSetRecordItemDto recordItemDto : qcSetRecordItemDtoList) {
//                int sort = recordItemDto.getSort();
//                if (targetValueMap.containsKey(sort) && standardValueMap.containsKey(sort)) {
//                    if (standardValueMap.get(sort) == 0) {
//                        //标准差不能为0
//                        throw new RegionLisWithErrorCodeException(QcErrorCode.QC100031);
//                    }
//
//                    if (targetValueMap.get(sort) == 0) {
//                        //靶值不能为0
//                        throw new RegionLisWithErrorCodeException(QcErrorCode.QC100032);
//                    }
//
//                    //靶值保留3位小数 并*1000
//                    double targetValue = NumberUtils.save3RoudDownValue(targetValueMap.get(sort)) * 1000;
//                    recordItemDto.setTargetValue(new Double(targetValue).longValue());
//                    //标准差保留3位小数 并*1000
//                    double standardValue = NumberUtils.save3RoudDownValue(standardValueMap.get(sort)) * 1000;
//                    recordItemDto.setStandardDeviation(new Double(standardValue).longValue());
//                    //变异系数保留3位小数 并*1000
//                    double cvValue = NumberUtils.save3RoudDownValue(cvValueMap.get(sort)) * 1000;
//                    recordItemDto.setCvValue(new Double(cvValue).longValue());
//                    log.info("靶值：{} 标准差：{} 变异系数：{} 原对象：{}", targetValue, standardValue, cvValue, JSONUtil.toJsonStr(recordItemDto));
//                }
//            }
//
//            //更新代码
//            qcDal.saveCalculateValue(qcSetRecordItemDtoList);

        }
    }

    /**
     * 根据质控设置批号获取数据
     *
     * @param recordBatch
     * @return
     */
    public List<TbQcSetRecordItem> selectQcSettingRecordItem(String recordBatch) {
        LambdaQueryWrapper<TbQcSetRecordItem> eq = Wrappers.lambdaQuery(TbQcSetRecordItem.class)
                .eq(TbQcSetRecordItem::getGroupId, LoginUserHandler.get().getGroupId())
                .eq(TbQcSetRecordItem::getOrgId, LoginUserHandler.get().getOrgId())
                .eq(TbQcSetRecordItem::getQcRecordBatch, recordBatch)
                .eq(TbQcSetRecordItem::getIsDelete, YesOrNoEnum.NO.getCode());
        return recordItemMapper.selectList(eq);
    }

    /**
     * 根据质控设置批号来删除数据
     * @param recordBatch
     * @return
     */
    public int deleteSetRecordItemByRecordBatch(String recordBatch) {
        if (StringUtils.isNotBlank(recordBatch)) {
            LambdaUpdateWrapper<TbQcSetRecordItem> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(TbQcSetRecordItem::getQcRecordBatch, recordBatch);
            return recordItemMapper.delete(wrapper);

        }
        return 0;
    }
    /**
     * 检查仪器类型编码为定型的仪器设置相关内容
     *
     * @param saveQcRecordInfoDto
     */
    private void checkQualitativeParam(QcAddRecordInfoDto saveQcRecordInfoDto) {
        final Integer qualitativeType = saveQcRecordInfoDto.getQualitativeType();
        final BigDecimal lowerControlLimit = saveQcRecordInfoDto.getLowerControlLimit();
        final BigDecimal upperControlLimit = saveQcRecordInfoDto.getUpperControlLimit();
        final QualitativeTypeEnum typeEnum = QualitativeTypeEnum.selectByCode(qualitativeType);
        if (Objects.isNull(typeEnum)) {
            throw new IllegalStateException("定性类型不存在");
        }

        if (Objects.isNull(lowerControlLimit) || Objects.isNull(upperControlLimit)) {
            throw new IllegalStateException("上或下控制线为空");
        }

        if (lowerControlLimit.compareTo(upperControlLimit) > 0) {
            throw new IllegalStateException("下控制线不能大于等于上控制线");
        }

        // 如果是半定性不校验具体的上下限
        if (Objects.equals(typeEnum, QualitativeTypeEnum.SEMI_QUALITATIVE)) {
            return;
        }

        final QualitativeResultValueEnum upper = QualitativeResultValueEnum.selectByCode(lowerControlLimit.intValue());

        final QualitativeResultValueEnum lower = QualitativeResultValueEnum.selectByCode(upperControlLimit.intValue());

        if (Objects.isNull(upper) || Objects.isNull(lower)) {
            throw new IllegalStateException("上或下控制线不存在");
        }
    }

    /**
     * 根据报告项目id来查询
     *
     * @param customerReportItemId 仪器报告项目id
     * @return
     */
    @Override
    public List<QcSetRecordItemDto> querySetRecordItemListReportItemId(Long customerReportItemId) {
        LambdaQueryWrapper<TbQcSetRecordItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbQcSetRecordItem::getQcRecordItemId, customerReportItemId);
        wrapper.eq(TbQcSetRecordItem::getIsDelete, YesOrNoEnum.NO.getCode());
        List<TbQcSetRecordItem> recordItemList = recordItemMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(recordItemList)) {
            return recordItemList.stream().map(this::convertRecordItemDto).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private QcSetRecordItemDto convertRecordItemDto(TbQcSetRecordItem qcSetRecordItem) {
        QcSetRecordItemDto recordItemDto = new QcSetRecordItemDto();
        BeanUtils.copyProperties(qcSetRecordItem, recordItemDto);
        return recordItemDto;
    }

    /**
     * 判断是否有交叉、包含和等于的时间
     */
    private boolean hasMixedDate(LocalDateTime itemStart, LocalDateTime itemEnd, LocalDateTime start, LocalDateTime end) {
        if (start.isEqual(itemStart) || start.isEqual(itemEnd) || end.isEqual(itemEnd) || end.isEqual(itemStart)) {
            return true;
        }


        if (start.isBefore(itemStart) && end.isAfter(itemEnd)) {
            return true;
        }


        if ((start.isBefore(itemEnd) && start.isAfter(itemStart)) || (end.isBefore(itemEnd) && end.isAfter(itemStart))) {
            return true;
        }

        return false;
    }

    /**
     * 质控设置信息数据填充
     *
     * @param saveQcRecordInfoDto     前端传递的信息
     * @param instrumentReportItemDto 仪器质控信息
     * @param user                    用户信息
     * @return
     */
    private List<QcSetRecordItemDto> createInsertInfos(QcAddRecordInfoDto saveQcRecordInfoDto, InstrumentReportItemDto instrumentReportItemDto, LoginUserHandler.User user) {
        List<QcSetRecordItemDto> recordItemDtoList = new ArrayList<>();
//        String recordBatch = serialIdUtil.getSerialNumber();      //新系统中使用snowflakeService来生成序号
        long recordBatch = snowflakeService.genId();
        int index = 1;
        for (QcAddSetRecordItemDto saveQcSetRecordItemDto : saveQcRecordInfoDto.getRecordList()) {
            //判断质控批次编号是否为空
            if (Objects.isNull(saveQcSetRecordItemDto.getQcBatchId())) {
                //质控信息数据
                QcSetRecordItemDto recordItemDto = new QcSetRecordItemDto();
                BeanUtils.copyProperties(user, recordItemDto);

                BeanUtils.copyProperties(saveQcRecordInfoDto, recordItemDto);
                //类型转换
                ZoneId zoneId = ZoneId.systemDefault();
                recordItemDto.setQcStartDate(Date.from(saveQcRecordInfoDto.getQcStartDate().atZone(zoneId).toInstant()));
                recordItemDto.setQcEndDate(Date.from(saveQcRecordInfoDto.getQcEndDate().atZone(zoneId).toInstant()));

                BeanUtils.copyProperties(saveQcSetRecordItemDto, recordItemDto);
                recordItemDto.setQcRecordItemId(snowflakeService.genId());
                recordItemDto.setQcRecordBatch(String.valueOf(recordBatch));
                recordItemDto.setSort(index);
//                recordItemDto.setInstrumentQcId(qcSettingDto.getInstrumentQcId());        已移除
//                recordItemDto.setInstrumentChannel(qcSettingDto.getInstrumentChannel());
                recordItemDto.setInstrumentChannel(instrumentReportItemDto.getInstrumentChannel());        //通过其他查询来获取

                recordItemDto.setCreateId(user.getUserId());
                recordItemDto.setCreateName(user.getNickname());
                recordItemDto.setUpdateId(user.getUserId());
                recordItemDto.setUpdateName(user.getNickname());

                recordItemDto.setIsDelete(YesOrNoEnum.NO.getCode());
                recordItemDto.setStatus(EffectiveEnum.EFFECTIVE.getIntCode());
                recordItemDto.setStatusDesc(EffectiveEnum.EFFECTIVE.getValue());

                recordItemDto.setTargetValue(MyQCNumberUtils.convert2DBDemical(saveQcSetRecordItemDto.getTargetValue()));
                recordItemDto.setStandardDeviation(MyQCNumberUtils.convert2DBDemical(saveQcSetRecordItemDto.getStandardDeviation()));
                recordItemDto.setCvValue(MyQCNumberUtils.convert2DBDemical(saveQcSetRecordItemDto.getCvValue()));

                recordItemDto.setLowerControlLimit(saveQcRecordInfoDto.getLowerControlLimit());
                recordItemDto.setUpperControlLimit(saveQcRecordInfoDto.getUpperControlLimit());
                recordItemDto.setQualitativeType(saveQcRecordInfoDto.getQualitativeType());
                recordItemDto.setLevelCodeDesc(QcSettingLevelEnum.getEnumByInt(saveQcSetRecordItemDto.getLevelCode()).getStrCode());

                recordItemDto.setIsUse(EnableEnum.ENABLE.getEnableStatusInt());

                recordItemDto.setSelfAttribute1(StringUtils.EMPTY);
                recordItemDto.setSelfAttribute2(StringUtils.EMPTY);
                recordItemDto.setSelfAttribute3(StringUtils.EMPTY);
                recordItemDto.setCreateDate(new Date());
                recordItemDto.setUpdateDate(new Date());

                //TODO 新增设置仪器报告项id
                recordItemDto.setInstrumentReportItemId(saveQcRecordInfoDto.getReportId());

                // 如果存在标准值和靶值 那么就计算变异系数
                String standardDeviation = saveQcSetRecordItemDto.getStandardDeviation();
                String targetValue = saveQcSetRecordItemDto.getTargetValue();
                String saveCvValue = saveQcSetRecordItemDto.getCvValue();
                if (StringUtils.isNotBlank(standardDeviation) && StringUtils.isNotBlank(targetValue) && StringUtils.isBlank(saveCvValue)) {
                    // 计算变异系数（标准差/靶值）
                    try {
                        Double standardDeviationDouble = Double.valueOf(standardDeviation);
                        Double targetValueDouble = Double.valueOf(targetValue);
                        double cvValue = (standardDeviationDouble / targetValueDouble);
                        Double cvValueLong = MyQCNumberUtils.save3RoudDownValue(cvValue);
                        recordItemDto.setCvValue(MyQCNumberUtils.convert2DBDemical(cvValueLong.toString()));
                    } catch (Exception e) {
                        log.error("计算变异系数异常 标准值：{} 靶值：{} 原因：{}", standardDeviation, targetValue, e.getMessage(), e);
                    }
                }

                if (StringUtils.isBlank(standardDeviation) && StringUtils.isNotBlank(targetValue) && StringUtils.isNotBlank(saveCvValue)) {
                    // 计算标准差（变异系数 * 靶值）
                    try {
                        Double targetValueDouble = Double.valueOf(targetValue);
                        // 这里因为前端传的百分比，所以这里除 100
                        Double saveCvValueDouble = Double.parseDouble(saveCvValue) / 100;
                        double standardDeviationDouble = (targetValueDouble * saveCvValueDouble);
                        Double standardDeviationLong = MyQCNumberUtils.save3RoudDownValue(standardDeviationDouble);
                        recordItemDto.setStandardDeviation(MyQCNumberUtils.convert2DBDemical(standardDeviationLong.toString()));
                        recordItemDto.setCvValue(MyQCNumberUtils.convert2DBDemical(saveCvValueDouble.toString()));
                    } catch (Exception e) {
                        log.error("计算标准差异常 变异系数：{} 靶值：{} 原因：{}", saveCvValue, targetValue, e.getMessage(), e);
                    }
                }

                recordItemDtoList.add(recordItemDto);
            }
            index++;
        }
        return recordItemDtoList;
    }
}
