package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.labway.lims.base.api.dto.ReportTemplateDto;
import com.labway.lims.base.api.service.ReportTemplateService;
import com.labway.lims.base.mapper.TbReportTemplateMapper;
import com.labway.lims.base.model.TbReportTemplate;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/4/10 13:40
 */
@DubboService
public class ReportTemplateServiceImpl implements ReportTemplateService {
    @Resource
    private TbReportTemplateMapper tbReportTemplateMapper;

    @Override
    public ReportTemplateDto selectById(long templateId) {
        return convert(tbReportTemplateMapper.selectById(templateId));
    }

    private ReportTemplateDto convert(TbReportTemplate model) {
        if (Objects.isNull(model)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(model), ReportTemplateDto.class);
    }
}
