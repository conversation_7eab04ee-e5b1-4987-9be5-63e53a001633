
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.HspOrgMainDto;
import com.labway.lims.base.model.TbHspOrgMain;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 送检机构科室、医生 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface HspOrgMainConverter {

    HspOrgMainDto hspOrgMainDtoTbObj(TbHspOrgMain obj);

    List<HspOrgMainDto> hspOrgMainDtoListFromTbObj(List<TbHspOrgMain> list);

}
