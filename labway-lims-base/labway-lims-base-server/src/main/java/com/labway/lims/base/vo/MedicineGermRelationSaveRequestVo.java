package com.labway.lims.base.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 菌属药物保存
 * 
 * <AUTHOR>
 * @since 2023/7/6 14:43
 */
@Getter
@Setter
public class MedicineGermRelationSaveRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 细菌菌属ID
     */
    private Long germGenusId;

    /**
     * 药物信息
     */
    private List<MedicineGermRelationItem> medicineItemList;

    @Getter
    @Setter
    public static class MedicineGermRelationItem implements Serializable {

        private static final long serialVersionUID = 1L;
        /**
         * 关联ID
         */
        private Long relationId;
        /**
         * 药物ID
         */
        private Long medicineId;
        /**
         * 药物ID
         */
        private String medicineName;
        /**
         * 参考值上限值
         */
        private String referValueMax;
        /**
         * 参考值下限值
         */
        private String referValueMin;
        /**
         * 参考单位
         */
        private String referUnit;
        /**
         * 检测方法编码
         */
        private String examMethodCode;
        /**
         * 检测方法name
         */
        private String examMethodName;
        /**
         * 是否耐药提醒:1是0否
         *
         * @see YesOrNoEnum
         */
        private Integer resistantWarn;
        /**
         * 报告顺序
         */
        private String reportSort;

        /**
         * 敏感度
         */
        private String susceptibility;

        /**
         * 折点范围
         */
        private String foldPointScope;
    }

}
