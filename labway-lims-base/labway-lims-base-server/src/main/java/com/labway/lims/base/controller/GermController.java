package com.labway.lims.base.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.dto.GermDto;
import com.labway.lims.base.api.dto.GermGenusDto;
import com.labway.lims.base.api.dto.GermInfoAllDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.api.service.GermGenusService;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.base.mapstruct.GermConverter;
import com.labway.lims.base.service.listener.ImportGermListener;
import com.labway.lims.base.vo.GermAddRequestVo;
import com.labway.lims.base.vo.GermImportHeadVo;
import com.labway.lims.base.vo.GermUpdateRequestVo;
import com.labway.lims.base.vo.ImportErrorResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 细菌 API
 * 
 * <AUTHOR>
 * @since 2023/3/20 17:36
 */
@Slf4j
@RestController
@RequestMapping("/germ")
public class GermController extends BaseController {

    @DubboReference
    private DictService dictService;
    @DubboReference
    private GermService germService;

    @DubboReference
    private GermGenusService germGenusService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private GermConverter germConverter;

    /**
     * 细菌 新增
     */
    @PostMapping("/add")
    public Object germAdd(@RequestBody GermAddRequestVo vo) {
        if (StringUtils.isAnyBlank(vo.getGermCode(), vo.getGermName()) || Objects.isNull(vo.getEnableStatistics())
            || Objects.isNull(vo.getEnable())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        if (StringUtils.length(vo.getGermCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("细菌编码不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }

        // 检查 参数
        checkVoWhenAddOrUpdate(vo);
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        if (Objects.nonNull(germService.selectByGermName(vo.getGermName(), loginUser.getOrgId()))) {
            throw new IllegalStateException("当前细菌名称已存在");
        }

        if (Objects.nonNull(germService.selectByGermCode(vo.getGermCode(), loginUser.getOrgId()))) {
            throw new IllegalStateException("当前细菌编码已存在");
        }
        GermGenusDto germGenusDto = null;
        if (Objects.nonNull(vo.getGermGenusId())) {
            germGenusDto = germGenusService.selectByGermGenusId(vo.getGermGenusId());
            if (Objects.isNull(germGenusDto)) {
                throw new IllegalStateException("细菌菌属不存在");
            }
        }

        DictItemDto dictItemDto = null;
        if (StringUtils.isNotBlank(vo.getWhonetGermTypeCode())) {
            dictItemDto = dictService.selectByDictCode(vo.getWhonetGermTypeCode());
            if (Objects.isNull(dictItemDto)) {
                throw new IllegalStateException("WHONET细菌类型不存在");
            }
        }
        // 转换
        GermDto germDto = JSON.parseObject(JSON.toJSONString(vo), GermDto.class);
        if (Objects.nonNull(germGenusDto)) {
            germDto.setGermGenusId(germGenusDto.getGermGenusId());
            germDto.setGermGenusCode(germGenusDto.getGermGenusCode());
        } else {
            germDto.setGermGenusId(NumberUtils.LONG_ZERO);
            germDto.setGermGenusCode(StringUtils.EMPTY);
        }

        germDto.setGermTypeCode("0L");
        germDto.setWhonetGermTypeCode(StringUtils.EMPTY);
        germDto.setWhonetGermTypeName(StringUtils.EMPTY);
        if (Objects.nonNull(dictItemDto)) {
            germDto.setWhonetGermTypeCode(dictItemDto.getDictCode());
            germDto.setWhonetGermTypeName(dictItemDto.getDictName());
        }

        long id = germService.addGerm(germDto);

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.GERM_LOG.getDesc())
                .setContent(String.format("新增 [%s] 细菌", germDto.getGermName())).toJSONString());

        return Map.of("id", id);

    }

    /**
     * 细菌 删除
     */
    @PostMapping("/delete")
    public Object germDelete(@RequestBody Set<Long> germIds) {
        if (CollectionUtils.isEmpty(germIds)) {
            return Collections.emptyMap();
        }
        List<GermDto> germDtos = germService.selectByGermIds(germIds);

        germService.deleteByGermIds(germIds);

        germDtos.forEach(item -> {
            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.GERM_LOG.getDesc())
                    .setContent(String.format("删除 [%s] 细菌", item.getGermName())).toJSONString());

        });

        return Collections.emptyMap();
    }

    /**
     * 细菌 修改
     */
    @PostMapping("/update")
    public Object germUpdate(@RequestBody GermUpdateRequestVo vo) {
        if (StringUtils.isBlank(vo.getGermName()) || Objects.isNull(vo.getGermId())
            || Objects.isNull(vo.getEnableStatistics()) || Objects.isNull(vo.getEnable())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        Long germGenusId = ObjectUtils.defaultIfNull(vo.getGermGenusId(), NumberUtils.LONG_ZERO);
        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        // 判断细菌是否存在
        GermDto germDtoTemp = germService.selectByGermId(vo.getGermId());
        if (Objects.isNull(germDtoTemp)) {
            throw new IllegalStateException("细菌不存在");
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        GermDto selectByGermName = germService.selectByGermName(vo.getGermName(), loginUser.getOrgId());
        if (Objects.nonNull(selectByGermName) && !Objects.equals(vo.getGermId(), selectByGermName.getGermId())) {
            throw new IllegalStateException("当前细菌名称已存在");
        }

        DictItemDto dictItemDto = null;
        if (StringUtils.isNotBlank(vo.getWhonetGermTypeCode())) {
            dictItemDto = dictService.selectByDictCode(vo.getWhonetGermTypeCode());
            if (Objects.isNull(dictItemDto)) {
                throw new IllegalStateException("WHONET细菌类型不存在");
            }
        }

        GermInfoAllDto germDtoNow = germConverter.germInfoAllDtoFromTbObj(germDtoTemp);

        final GermInfoAllDto germDto = new GermInfoAllDto();
        BeanUtils.copyProperties(germDtoNow, germDto);

        // 更新项
        germDto.setGermName(vo.getGermName());
        germDto.setGermEn(vo.getGermEn());

        if (!Objects.equals(germGenusId, germDtoNow.getGermGenusId())) {
            // 现有细菌菌属对应名称
            if (Objects.equals(germDtoNow.getGermGenusId(), NumberUtils.LONG_ZERO)) {
                germDtoNow.setGermGenusName(StringUtils.EMPTY);
            } else {
                GermGenusDto temp = germGenusService.selectByGermGenusId(germDtoNow.getGermGenusId());
                if (Objects.nonNull(temp)) {
                    germDtoNow.setGermGenusName(temp.getGermGenusName());
                }
            }

            // 细菌菌属发生变化
            GermGenusDto germGenusDto = null;
            if (!Objects.equals(germGenusId, NumberUtils.LONG_ZERO)) {
                germGenusDto = germGenusService.selectByGermGenusId(germGenusId);
                if (Objects.isNull(germGenusDto)) {
                    throw new IllegalStateException("细菌菌属不存在");
                }
            }
            if (Objects.nonNull(germGenusDto)) {
                germDto.setGermGenusId(germGenusDto.getGermGenusId());
                germDto.setGermGenusCode(germGenusDto.getGermGenusCode());
                germDto.setGermGenusName(germGenusDto.getGermGenusName());
            } else {
                germDto.setGermGenusId(NumberUtils.LONG_ZERO);
                germDto.setGermGenusCode(StringUtils.EMPTY);
                germDto.setGermGenusName(StringUtils.EMPTY);
            }
        }

        germDto.setGermTypeName(vo.getGermTypeName());
        germDto.setWhonetGermTypeCode(StringUtils.EMPTY);
        germDto.setWhonetGermTypeName(StringUtils.EMPTY);
        if (Objects.nonNull(dictItemDto)) {
            germDto.setWhonetGermTypeCode(dictItemDto.getDictCode());
            germDto.setWhonetGermTypeName(dictItemDto.getDictName());
        }
        germDto.setWhonetGermCode(vo.getWhonetGermCode());
        germDto.setEnableStatistics(vo.getEnableStatistics());
        germDto.setEnable(vo.getEnable());

        germService.updateByGermId(germConverter.germDtoFromTbObj(germDto));

        String compare = new CompareUtils<GermInfoAllDto>().compare(germDtoNow, germDto);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.GERM_LOG.getDesc())
                    .setContent(String.format("修改细菌: [%s]", compare)).toJSONString());
        }

        return Collections.emptyMap();

    }

    /**
     * 细菌 获取 所有 查看
     */
    @PostMapping("/select-all")
    public Object germList() {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        List<GermDto> germDtos = germService.selectByOrgId(loginUser.getOrgId());
        // 所有细菌菌属 id
        Set<Long> germGenusIdList = germDtos.stream().map(GermDto::getGermGenusId).collect(Collectors.toSet());

        List<GermGenusDto> germGenusDtos = germGenusService.selectByGermGenusIds(germGenusIdList);
        Map<Long, GermGenusDto> germGenusDtoByGermGenusId =
            germGenusDtos.stream().collect(Collectors.toMap(GermGenusDto::getGermGenusId, Function.identity()));
        List<GermInfoAllDto> targetList = Lists.newArrayListWithCapacity(germDtos.size());
        for (GermDto item : germDtos) {
            GermInfoAllDto temp = JSON.parseObject(JSON.toJSONString(item), GermInfoAllDto.class);
            GermGenusDto germGenusDto = germGenusDtoByGermGenusId.get(item.getGermGenusId());
            if (Objects.isNull(germGenusDto)) {
                // 无效菌属 ？？？
                temp.setGermGenusId(null);
            } else {
                temp.setGermGenusName(germGenusDto.getGermGenusName());
            }
            targetList.add(temp);
        }
        return targetList;
    }

    /**
     * 检查 细菌 新增 或 修改 参数 公共部分
     *
     */
    private <T extends GermAddRequestVo> void checkVoWhenAddOrUpdate(T vo) {
        if (StringUtils.length(vo.getGermName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("细菌名称不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }

        if (StringUtils.isNotBlank(vo.getGermEn()) && StringUtils.length(vo.getGermEn()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("细菌英文名称不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }

        if (StringUtils.isNotBlank(vo.getGermTypeName())
            && StringUtils.length(vo.getGermTypeName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("细菌类别不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }

        if (StringUtils.isNotBlank(vo.getWhonetGermTypeCode())
            && StringUtils.length(vo.getWhonetGermTypeCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("WHONET细菌编码不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }

        if (!(Objects.equals(vo.getEnableStatistics(), YesOrNoEnum.NO.getCode())
            || Objects.equals(vo.getEnableStatistics(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("是否统计参数错误");
        }

        if (!(Objects.equals(vo.getEnable(), YesOrNoEnum.NO.getCode())
            || Objects.equals(vo.getEnable(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("是否启用参数错误");
        }

    }

    /**
     * 导入细菌
     */
    @PostMapping("/import-germ")
    public Object germImport(@RequestParam("file") MultipartFile file) {

        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(fileName);
        if (!"xls".equalsIgnoreCase(extension) && !"xlsx".equalsIgnoreCase(extension)) {
            throw new IllegalStateException("只允许上传 xls 和 xlsx 格式的文件");
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        ImportGermListener listener =
            new ImportGermListener(germService, germGenusService, dictService, snowflakeService, user);

        try (ExcelReader excelReader = EasyExcelFactory.read(file.getInputStream()).build()) {

            ReadSheet readSheet = EasyExcelFactory.readSheet(0).head(GermImportHeadVo.class)
                .registerReadListener(listener).autoTrim(true).build();

            excelReader.read(readSheet);

            // 检查 失败 返回 对应行相关错误信息
            List<ImportErrorResponseVo> importErrorResponseVoList = listener.getImportErrorResponseVoList();
            if (CollectionUtils.isNotEmpty(importErrorResponseVoList)) {
                throw new LimsCodeException(1002, "数据检查失败").setData(Map.of("errorList", importErrorResponseVoList));
            }

            List<GermDto> addTargetList = listener.getAddTargetList();
            List<GermDto> updateTargetList = listener.getUpdateTargetList();
            for (GermDto germDto : addTargetList) {
                germService.addGerm(germDto);
            }
            for (GermDto germDto : updateTargetList) {
                germService.updateByGermId(germDto);
            }

        } catch (IOException e) {
            log.error("导入细菌出错", e);
            throw new IllegalStateException(e.getMessage(), e);
        }

        return Collections.emptyMap();
    }

}
