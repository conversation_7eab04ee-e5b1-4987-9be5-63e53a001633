package com.labway.lims.base.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class HspOrgSpecialOfferVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 优惠ID
     */
    private Long offerId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 项目ID
     */
    private Long testItemId;

    /**
     * 项目编码
     */
    private String testItemCode;
    /**
     * 项目名称
     */
    private String testItemName;

    /**
     * 送检类型编码
     */
    private String sendTypeCode;

    /**
     * 送检类型名称
     */
    private String sendType;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 折扣率
     */
    private BigDecimal discount;

    /**
     * 是否参与阶梯折扣
     */
    private Integer isTieredPricing;


    /**
     * 财务专业组
     */
    private String financeGroup;

    /**
     * 折前单价
     */
    private BigDecimal originalPrice;

    /**
     * 折后单价
     */
    private BigDecimal discountPrice;

    /**
     * 专业组
     */
    private String groupName;

    /**
     * 检验方法学
     */
    private String examMethodName;
    /**
     * 是否启用(0未启动 1启动)
     * @see YesOrNoEnum
     */
    private Integer enable;
}
