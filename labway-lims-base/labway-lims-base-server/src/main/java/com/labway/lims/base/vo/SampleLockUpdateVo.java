package com.labway.lims.base.vo;

import com.labway.lims.api.enums.finance.FinanceLockEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/15 12:57
 */
@Getter
@Setter
public class SampleLockUpdateVo {

    /**
     * applySampleId
     */
    private List<Long> applySampleIds;

    /**
     * 原因
     */
    private String reason;

    /**
     * 锁状态
     *
     * @see FinanceLockEnum
     * 0解锁  1加锁
     */
    private Integer status;
}
