package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目价格基准包
 */
@Getter
@Setter
public class ItemPriceBasePackageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long packageId;

    /**
     * 基准包名称
     */
    private String packageName;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 是否启用
     */
    private Integer enable;
}
