package com.labway.lims.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/11 16:34
 */
@Getter
@Setter
public class FreeSampleVo {

    /**
     * applySampleId
     */
    private Long applySampleId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 就诊类型编码，申请单类型
     */
    private String applyTypeCode;

    /**
     * 就诊类型名称
     */
    private String applyTypeName;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 送检医生编码
     */
    private String sendDoctorCode;
    /**
     * 送检医生名称
     */
    private String sendDoctorName;

    /**
     * 录入人
     */
    private String enterPeople;

    /**
     * 录入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date enterDate;

    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 样本个数
     */
    private Integer sampleCount;

    /**
     * 临床诊断
     */
    private String diagnosis;

    /**
     * 样本备注
     */
    private String remark;

    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 当前环节
     */
    private String currentLink;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 样本性状编码
     */
    private String samplePropertyCode;

    /**
     * 1: 急诊，0:不急
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date applyDate;

    /**
     * 采样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date samplingDate;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date patientBirthday;

    /**
     * 身份证
     */
    private String patientCard;

    /**
     * 手机号
     */
    private String patientMobile;

    /**
     * 性别
     */
    private Integer patientSex;

    /**
     * 申请科室
     */
    private String dept;

    /**
     * 床号
     */
    private String patientBed;

    /**
     * 创建时间/录入时间
     */
    private Date createDate;

}
