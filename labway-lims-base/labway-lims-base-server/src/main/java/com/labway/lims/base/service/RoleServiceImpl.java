package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.MenuTypeCodeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.MenuDto;
import com.labway.lims.base.api.dto.RoleDto;
import com.labway.lims.base.api.dto.UserRoleDto;
import com.labway.lims.base.api.service.MenuService;
import com.labway.lims.base.api.service.RoleService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.base.mapper.TbRoleMapper;
import com.labway.lims.base.mapper.TbRoleMenuMapper;
import com.labway.lims.base.model.TbRole;
import com.labway.lims.base.model.TbRoleMenu;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "role")
public class RoleServiceImpl implements RoleService {
    @Resource
    private TbRoleMapper roleMapper;
    @Resource
    private UserService userService;
    @Resource
    private TbRoleMenuMapper roleMenuMapper;
    @Resource
    private MenuService menuService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public List<RoleDto> selectByUserId(long userId) {
        return roleMapper.selectByUserId(userId).stream()
                .map(this::convert).collect(Collectors.toList());
    }


    @Override
    public List<UserRoleDto> selectUserRoleByUserId(long userId) {
        return selectUserRoleByUserIds(List.of(userId));
    }

    @Override
    public List<UserRoleDto> selectUserRoleByUserIds(Collection<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return roleMapper.selectUserRoleByUserIds(userIds);
    }

    @Override
    public List<RoleDto> selectByOrgId(long orgId) {
        return roleMapper.selectList(new LambdaQueryWrapper<TbRole>()
                        .eq(TbRole::getOrgId, orgId)
                        .orderByDesc(TbRole::getRoleId)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    public long addRole(RoleDto role) {
        for (RoleDto e : selectByOrgId(LoginUserHandler.get().getOrgId())) {
            if (StringUtils.equalsIgnoreCase(e.getRoleName(), role.getRoleName())) {
                throw new IllegalArgumentException(String.format("角色 [%s] 已存在", e.getRoleName()));
            }
        }

        final TbRole tr = new TbRole();
        BeanUtils.copyProperties(role, tr);
        tr.setRoleId(snowflakeService.genId());
        tr.setIsDelete(YesOrNoEnum.NO.getCode());

        if (roleMapper.insert(tr) < 1) {
            throw new IllegalArgumentException("新增角色失败");
        }

        log.info("用户 [{}] 新增角色 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(tr));


        return tr.getRoleId();
    }

    @Override
    public void deleteByRoleId(long roleId) {
        if (CollectionUtils.isNotEmpty(userService.selectByRoleId(roleId))) {
            throw new IllegalArgumentException("角色关联用户不为空，不能删除");
        }

        log.info("用户 [{}] 删除角色 [{}] 结果 [{}]", LoginUserHandler.get().getNickname(),
                roleId, roleMapper.deleteById(roleId) > 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByRoleIds(Set<Long> roleIds) {

        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }

        for (Long roleId : roleIds) {
            deleteByRoleId(roleId);
        }

    }

    @Override
    public boolean updateById(RoleDto role) {
        for (RoleDto e : selectByOrgId(LoginUserHandler.get().getOrgId())) {
            if (StringUtils.equalsIgnoreCase(e.getRoleName(), role.getRoleName())
                    && !Objects.equals(e.getRoleId(), role.getRoleId())) {
                throw new IllegalArgumentException(String.format("角色 [%s] 已存在", e.getRoleName()));
            }
        }

        final TbRole tr = new TbRole();
        BeanUtils.copyProperties(role, tr);

        if (roleMapper.updateById(tr) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改角色 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(tr));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMenusByRoleId(long roleId, Set<Long> menuIds) {

        final LoginUserHandler.User user = LoginUserHandler.get();

        final List<MenuDto> menus = menuService.selectByRoleId(roleId);
        menus.removeIf(e -> Objects.equals(e.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name()));
        if (CollectionUtils.isNotEmpty(menus)) {
            roleMenuMapper.delete(new LambdaQueryWrapper<TbRoleMenu>()
                    .eq(TbRoleMenu::getRoleId, roleId)
                    .in(TbRoleMenu::getMenuId, menus.stream().map(MenuDto::getMenuId)
                            .collect(Collectors.toSet())));
            menuService.clearCaches();
        }

        final LinkedList<Long> ids = snowflakeService.genIds(menuIds.size());
        for (Long menuId : menuIds) {
            final TbRoleMenu roleMenu = new TbRoleMenu();
            roleMenu.setRoleMenuId(ids.pop());
            roleMenu.setMenuId(menuId);
            roleMenu.setRoleId(roleId);
            roleMenu.setOrgId(user.getOrgId());
            roleMenu.setOrgName(user.getOrgName());
            roleMenu.setCreateDate(new Date());
            roleMenu.setUpdateDate(new Date());
            roleMenu.setUpdaterId(user.getUserId());
            roleMenu.setUpdaterName(user.getNickname());
            roleMenu.setCreatorId(user.getUserId());
            roleMenu.setCreatorName(user.getNickname());
            roleMenu.setIsDelete(YesOrNoEnum.NO.getCode());
            if (roleMenuMapper.insert(roleMenu) < 1) {
                throw new IllegalArgumentException("添加角色菜单关联失败");
            }
        }
    }

    @Override
    public void updateButtonsByRoleId(long roleId, Set<Long> buttonIds) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final List<MenuDto> menus = menuService.selectByRoleId(roleId);
        menus.removeIf(e -> !Objects.equals(e.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name()));
        if (CollectionUtils.isNotEmpty(menus)) {
            roleMenuMapper.delete(new LambdaQueryWrapper<TbRoleMenu>()
                    .eq(TbRoleMenu::getRoleId, roleId)
                    .in(TbRoleMenu::getMenuId, menus.stream().map(MenuDto::getMenuId).collect(Collectors.toSet())));
            menuService.clearCaches();
        }

        final LinkedList<Long> ids = snowflakeService.genIds(buttonIds.size());
        for (Long menuId : buttonIds) {
            final TbRoleMenu roleMenu = new TbRoleMenu();
            roleMenu.setRoleMenuId(ids.pop());
            roleMenu.setMenuId(menuId);
            roleMenu.setRoleId(roleId);
            roleMenu.setOrgId(user.getOrgId());
            roleMenu.setOrgName(user.getOrgName());
            roleMenu.setCreateDate(new Date());
            roleMenu.setUpdateDate(new Date());
            roleMenu.setUpdaterId(user.getUserId());
            roleMenu.setUpdaterName(user.getNickname());
            roleMenu.setCreatorId(user.getUserId());
            roleMenu.setCreatorName(user.getNickname());
            roleMenu.setIsDelete(YesOrNoEnum.NO.getCode());
            if (roleMenuMapper.insert(roleMenu) < 1) {
                throw new IllegalArgumentException("添加角色按钮关联失败");
            }
        }
    }

    @Nullable
    @Override
    public RoleDto selectByRoleId(long roleId) {
        return convert(roleMapper.selectById(roleId));
    }

    @Override
    public List<RoleDto> selectByRoleIds(Collection<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        return roleMapper.selectList(new LambdaQueryWrapper<TbRole>().in(TbRole::getRoleId, roleIds))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RoleDto> selectByMenuId(long menuId) {
        final List<TbRoleMenu> tbRoleMenus = roleMenuMapper.selectList(Wrappers.lambdaQuery(TbRoleMenu.class)
                .eq(TbRoleMenu::getMenuId, menuId));
        final Set<Long> roleIdSet = tbRoleMenus.stream().map(TbRoleMenu::getRoleId).collect(Collectors.toSet());

        return this.selectByRoleIds(roleIdSet);
    }

    private RoleDto convert(TbRole role) {
        if (Objects.isNull(role)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(role), RoleDto.class);
    }
}
