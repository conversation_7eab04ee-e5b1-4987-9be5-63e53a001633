package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.FinanceSampleLockDto;
import com.labway.lims.base.model.TbFinanceSampleLock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/11 20:53
 */
@Mapper
public interface TbFinanceSampleLockMapper extends BaseMapper<TbFinanceSampleLock> {
    int addBatch(@Param("locks") List<TbFinanceSampleLock> locks);

    int updateBySampleLockRecordIds(@Param("sampleLock") FinanceSampleLockDto lock, @Param("sampleLockRecordIds") Collection<Long> sampleLockRecordIds);
}
