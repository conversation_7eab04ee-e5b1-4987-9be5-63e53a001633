package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 报告项目结果值异常
 */
@Getter
@Setter
@TableName("tb_instrument_report_item_exception")
public class TbInstrumentReportItemException implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参考值范围ID
     */
    @TableId
    private Long instrumentReportItemExceptionId;

    /**
     * 仪器报告项目ID
     */
    private Long instrumentReportItemId;

    /**
     * 结果
     */
    private String result;

    /**
     * 是否异常
     *
     * @see YesOrNoEnum
     */
    private Integer isException;


    /**
     * 是否异常
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;


    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 条件
     * ≥, >, =, <, ≤
     */
    @Compare("条件")
    private String formulaMax;

    /**
     * 条件值
     */
    @Compare("条件值")
    private String formulaMaxValue;

    /**
     * 条件
     * <, ≤
     */
    @Compare("条件")
    private String formulaMin;

    /**
     * 条件值
     */
    @Compare("条件值")
    private String formulaMinValue;

}
