package com.labway.lims.base.controller;


import com.labway.lims.api.web.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/9/9
 */
@Slf4j
@RestController
@RequestMapping("qcOutOfControl")
public class QcOutOfControlController extends BaseController {

//    @Resource
//    private QcOutOfControlService outOfControlService;
//
//    /**
//     * 获取失控记录模板根据resultId
//     *
//     * @param resultId
//     * @return
//     */
//    @GetMapping("/getoutOfControl/{resultId}")
//    public ResultVo getOutOfControlRecordByResultId(@PathVariable String resultId) {
//        OutOfControlRecordDto outOfControlRecordDto = outOfControlService.getOutOfControlRecordByResultId(resultId);
//
//        if (outOfControlRecordDto != null) {
//            return ResultVo.success(JSON.parseObject(JSON.toJSONString(outOfControlRecordDto), OutOfControlRecordVo.class));
//        }
//
//        return ResultVo.success();
//    }
//
//    /**
//     * 编辑失控模板
//     *
//     * @return
//     */
//    @PostMapping("/editOutOfControlTemplateContent")
//    public ResultVo editOutOfControlTemplateContent(@RequestBody OutOfControlRecordEditVo outOfControlRecordEditVo) {
//        outOfControlRecordEditVo.checkParam();
//        outOfControlRecordEditVo.analysisJSon();
//        log.info("请求参数=====》{}", JSON.toJSONString(outOfControlRecordEditVo));
//
//        OutOfControlRecordEditDto outOfControlRecordEditDto = JSON.parseObject(JSON.toJSONString(outOfControlRecordEditVo), OutOfControlRecordEditDto.class);
//        outOfControlService.editOutOfControlTemplateContent(outOfControlRecordEditDto);
//        return ResultVo.success();
//    }
//
//    /**
//     * 删除质控失控记录
//     *
//     * @param qcRecordItemId
//     * @return
//     */
//    @PostMapping("/removeOutOfControlRecord/{qcRecordItemId}")
//    public ResultVo removeOutOfControlRecord(@PathVariable("qcRecordItemId") String qcRecordItemId) {
//        outOfControlService.removeOutOfControlRecord(qcRecordItemId);
//        return ResultVo.success();
//    }
//
//    /**
//     * 查询质控失控记录
//     *
//     * @param outOfControlRecordQueryVo
//     * @return
//     */
//    @PostMapping("/outOfControlRecordList")
//    public ResultVo outOfControlRecordList(@RequestBody OutOfControlRecordQueryVo outOfControlRecordQueryVo) {
//        outOfControlRecordQueryVo.checkParam();
//        log.info("请求参数 ====》{}", JSONUtil.toJsonStr(outOfControlRecordQueryVo));
//
//        OutOfControlRecordQueryDto outOfControlRecordQueryDto = JSON.parseObject(JSON.toJSONString(outOfControlRecordQueryVo), OutOfControlRecordQueryDto.class);
//
//        // 查询
//        PageDto<OutOfControlRecordDto> listPageDto = outOfControlService.outOfControlRecordList(outOfControlRecordQueryDto);
//
//
//        // 转换
//        PageVO<OutOfControlRecordVo> pageVO = new PageVO<>();
//        pageVO.setTotal(listPageDto.getTotal());
//        pageVO.setPageSize(listPageDto.getPageSize());
//        pageVO.setPageNum(listPageDto.getPageNum());
//        pageVO.setData(listPageDto.getData().stream()
//                .map(i -> JSON.parseObject(JSON.toJSONString(i), OutOfControlRecordVo.class))
//                .collect(Collectors.toList()));
//
//        return ResultVo.success(pageVO);
//    }
//
//    /**
//     * 添加质控失控记录
//     *
//     * @return
//     */
//    @PostMapping("/fillOutQualityControlOutOfControlReport")
//    public ResultVo fillOutQualityControlOutOfControlReport(@RequestBody FillOutQualityControlOutOfControlReportVo fillOutQualityControlOutReportVo) {
//        fillOutQualityControlOutReportVo.checkParam();
//        fillOutQualityControlOutReportVo.analysisJSon();
//        log.info("请求参数 ====》{}", JSON.toJSONString(fillOutQualityControlOutReportVo));
//        FillOutQualityControlOutOfControlReportDto fillOutQualityControlOutOfControlReportDto = JSON.parseObject(JSON.toJSONString(fillOutQualityControlOutReportVo), FillOutQualityControlOutOfControlReportDto.class);
//        outOfControlService.fillOutQualityControlOutOfControlReport(fillOutQualityControlOutOfControlReportDto);
//        return ResultVo.success();
//    }
//
//    /**
//     * 查询质控失控模板
//     *
//     * @return
//     */
//    @PostMapping("/queryQcOutOfControlTemplate")
//    public ResultVo queryQcOutOfControlTemplate() {
//        List<OutOfControlModelDto> qcOutOfControlTemplateDtoList = outOfControlService.queryQcOutOfControlTemplate();
//        List<OutOfControlModelVo> list = qcOutOfControlTemplateDtoList.stream().map(i -> JSON.parseObject(JSON.toJSONString(i), OutOfControlModelVo.class)).collect(Collectors.toList());
//        return ResultVo.success(list);
//    }
//
//
//    /**
//     * 删除质控失控模板
//     *
//     * @param templateId 模板id
//     * @return
//     */
//    @PostMapping("/deleteQcOutOfControlTemplate")
//    public ResultVo deleteQcOutOfControlTemplate(@RequestParam(value = "templateId", required = true) String templateId) {
//        outOfControlService.deleteQcOutOfControlTemplate(templateId);
//
//        return ResultVo.success();
//    }
//
//    /**
//     * 修改质控失控模板
//     *
//     */
//    @PostMapping("/editQcOutOfControlTemplate")
//    public ResultVo editQcOutOfControlTemplate(@RequestBody QcOutOfControlTemplateVo qcOutOfControlTemplateVo) {
//        if (StringUtils.isEmpty(qcOutOfControlTemplateVo.getTemplateUrl())
//                &&
//                Objects.isNull(qcOutOfControlTemplateVo.getOutOfControlTemplateStatus())) {
//            throw new IllegalArgumentException("请输入需要修改的内容");
//        }
//        if (!UrlValidator.getInstance().isValid(qcOutOfControlTemplateVo.getTemplateUrl())) {
//            throw new IllegalArgumentException("URL格式不正确");
//        }
//        if (StringUtils.isEmpty(qcOutOfControlTemplateVo.getId())) {
//            throw new IllegalArgumentException("模板id不能为空");
//        }
//        OutOfControlModelDto outOfControlModelDto = new OutOfControlModelDto();
//        outOfControlModelDto.setOutOfControlUrl(qcOutOfControlTemplateVo.getTemplateUrl());
//        outOfControlModelDto.setOutOfControlStatus(qcOutOfControlTemplateVo.getOutOfControlTemplateStatus());
//        outOfControlModelDto.setOutOfControlId(qcOutOfControlTemplateVo.getId());
//
//        outOfControlService.editQcOutOfControlTemplate(outOfControlModelDto);
//        return ResultVo.success();
//    }
//
//    /**
//     * 添加质控失控模板
//     *
//     * @return
//     */
//    @PostMapping("/addQcOutOfControlTemplate")
//    public ResultVo addQcOutOfControlTemplate(@RequestBody QcOutOfControlTemplateVo qcOutOfControlTemplateVo) {
//        qcOutOfControlTemplateVo.checkParam();
//        if (!UrlValidator.getInstance().isValid(qcOutOfControlTemplateVo.getTemplateUrl())) {
//            throw new IllegalArgumentException("URL格式不正确");
//        }
//        log.info("请求参数 ====》{}", JSON.toJSONString(qcOutOfControlTemplateVo));
//
//        OutOfControlModelDto outOfControlModelDto = new OutOfControlModelDto();
//        outOfControlModelDto.setOutOfControlUrl(qcOutOfControlTemplateVo.getTemplateUrl());
//        outOfControlModelDto.setOutOfControlStatus(qcOutOfControlTemplateVo.getOutOfControlTemplateStatus());
//        outOfControlService.addQcOutOfControlTemplate(outOfControlModelDto);
//        return ResultVo.success();
//    }

}
