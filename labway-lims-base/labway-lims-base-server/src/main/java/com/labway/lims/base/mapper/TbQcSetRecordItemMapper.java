package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.QcSetRecordItemDto;
import com.labway.lims.base.model.TbQcSetRecordItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tb_qc_set_record_item(仪器质控记录明细)】的数据库操作Mapper
* @createDate 2023-11-03 15:13:03
* @Entity com.domain.TbQcSetRecordItem
*/
@Mapper
public interface TbQcSetRecordItemMapper extends BaseMapper<TbQcSetRecordItem> {
    //TODO
    void insertBatchRecordItem(@Param("list") List<QcSetRecordItemDto> recordItemDtos);
}




