package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 机构提示信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_organization_msg")
public class TbOrganizationMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 提示信息ID
     */
    @TableId
    private Long msgId;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 提示信息内容
     */
    private String msgContent;

    /**
     * 提示位置ID
     */
    private String msgPositionCode;;

    /**
     * 提示位置描述
     */
    private String msgPosition;

    /**
     * 是否启用(0未启用 1已启用)
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
