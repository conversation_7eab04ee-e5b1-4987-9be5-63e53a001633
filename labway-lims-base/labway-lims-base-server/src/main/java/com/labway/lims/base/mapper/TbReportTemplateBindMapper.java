package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.model.TbReportTemplateBind;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 报告单模板绑定 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
@Mapper
public interface TbReportTemplateBindMapper extends BaseMapper<TbReportTemplateBind> {

    int insertBatch(@Param("bindDtos") List<ReportTemplateBindDto> bindDtos);
}
