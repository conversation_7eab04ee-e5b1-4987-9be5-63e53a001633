package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.model.TbRack;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 试管架 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbRackMapper extends BaseMapper<TbRack> {

    /**
     * 根据ID批量修改
     */
    int updateByRackIds(@Param("rackDto") RackDto rackDto, @Param("rackIds") Collection<Long> rackIds);

}
