package com.labway.lims.base.vo;

import com.labway.lims.api.enums.base.ReportTemplateBindTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/6/15 16:46
 */
@Getter
@Setter
public class ReportTemplateBindAddVo {

    /**
     * 绑定类型,1:专业小组,2:送检机构,3:检验项目
     *
     * @see ReportTemplateBindTypeEnum
     */
    private ReportTemplateBindTypeEnum bindType;

    /**
     * 模板名称
     */
    private String reportTemplateName;

    /**
     * 报告模板编码
     */
    private String reportTemplateCode;

    /**
     * 模板文件
     */
    private String templateFileName;

    /**
     * 专业小组IDs 支持多选
     */
    private List<Long> instrumentGroupIds;

    /**
     * 是否启用，1是，0不是
     */
    private Integer enable;

    /**
     * groupId
     */
    private Long groupId;

    //-----送检机构
    /**
     * 送检机构多选
     */
    private List<Long> hspOrgIds;


    //----检验项目
    /**
     * 检验项目 多选
     */
    private List<Long> testItemIds;

    /**
     * 专业小组名称集合 日志用
     */
    private String instrumentGroupNames;
    /**
     * 送检机构名称集合 日志用
     */
    private String hspOrgNames;
    /**
     * 检验项目名称集合 日志用
     */
    private String testItemNames;

    /**
     * 报告模板类型
     * @see com.labway.lims.api.enums.TemplateTypeEnum
     */
    private String reportTemplateType;

    /**
     * 文件url(word版 文件)
     */
    private String fileUrl;

    /**
     * 判断是否是通用专业小组 (不是检验项目 并且 专业小组Id为 0L)
     * @return
     */
    public boolean isCommonInstrumentGroup(){
        return !Objects.equals(getBindType(), ReportTemplateBindTypeEnum.TEST_ITEM) &&
                getInstrumentGroupIds().size() == 1 && getInstrumentGroupIds().contains(NumberUtils.LONG_ZERO);
    }

    /**
     * 判断是否是通用送检机构(是检验项目 并且 送检机构ID为 0L)
     * @return
     */
    public boolean isCommonHspOrg(){
        return Objects.equals(getBindType(), ReportTemplateBindTypeEnum.TEST_ITEM) &&
                getHspOrgIds().size() == 1 && getHspOrgIds().contains(NumberUtils.LONG_ZERO);
    }
}
