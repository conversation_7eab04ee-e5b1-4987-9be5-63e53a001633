package com.labway.lims.base.controller;


import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.CombinePackageInfoDto;
import com.labway.lims.base.api.dto.EditCombinePackageDetailDto;
import com.labway.lims.base.api.service.ItemPriceCombinePackageService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * (TbItemPriceCombinePackage)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-04 19:55:44
 */
@RestController
@RequestMapping("/item-price-combine-package")
public class ItemPriceCombinePackageController extends BaseController {
    /**
     * 服务对象
     */
    @Resource
    private ItemPriceCombinePackageService itemPriceCombinePackageService;


    /**
     * 获取所有检验套餐信息-不分页
     */
    @RequestMapping("/query")
    public Object queryCombinePackageList(@RequestBody Set<String> combinePackageCodeList) {
        return itemPriceCombinePackageService.queryCombinePackageByCodes(combinePackageCodeList);
    }

    /**
     * 新增套餐信息
     */
    @RequestMapping("/add")
    public Object addCombinePackageInfo(@RequestBody @Valid CombinePackageInfoDto combinePackageInfoDto) {
        this.checkPrice(combinePackageInfoDto);
        return itemPriceCombinePackageService.addCombinePackageInfo(combinePackageInfoDto);
    }

    private void checkPrice(CombinePackageInfoDto combinePackageInfoDto) {
        final String price = combinePackageInfoDto.getCombinePackagePrice().toString();
        String regex = "^-?\\d{1,8}(\\.\\d{1,4})?$";
        if(!price.matches(regex)){
            throw new IllegalArgumentException("套餐收费价格，小数点前最多8位， 小数点后最多四位");
        }
    }


    /**
     * 修改套餐信息
     */
    @RequestMapping("/update")
    public Object updateCombinePackageInfo(@RequestBody @Valid CombinePackageInfoDto combinePackageInfoDto) {
        this.checkPrice(combinePackageInfoDto);
        return itemPriceCombinePackageService.updateCombinePackageInfo(combinePackageInfoDto);
    }


    /**
     * 修改套餐信息
     */
    @RequestMapping("/delete")
    public Object deleteCombinePackageInfo(@RequestBody Collection<String> combinePackageCodeList) {
        if (CollectionUtils.isEmpty(combinePackageCodeList)) {
            throw new IllegalArgumentException("财务套餐code不能为空");
        }
        itemPriceCombinePackageService.deleteCombinePackageInfo(combinePackageCodeList);
        return Map.of();
    }


    /**
     * 删除套餐信息
     */

    /**
     * 获取所有财务套餐信息和下面的项目
     */
    @GetMapping("/query-all")
    public Object queryAllCombinePackageList() {
        return itemPriceCombinePackageService.queryCombinePackageAndItem();
    }

    /**
     * 查询套餐的详情信息
     */
    @PostMapping("/query-detail")
    public Object addCombinePackageInfo(@RequestBody List<String> combinePackageCodes) {
        return itemPriceCombinePackageService.queryCombinePackageDetailInfo(combinePackageCodes);
    }

    /**
     * 删除套餐项目信息
     */
    @PostMapping("/save-detail")
    public Object saveCombinePackageDetail(@RequestBody EditCombinePackageDetailDto dto) {
        if (StringUtils.isBlank(dto.getCombinePackageCode())) {
            throw new IllegalArgumentException("财务套餐code不能为空");
        }
        if (Objects.isNull(dto.getTestItemIds())) {
            dto.setTestItemIds(Collections.emptySet());
        }
        return itemPriceCombinePackageService.saveCombinePackageDetail(dto.getCombinePackageCode(), dto.getTestItemIds());
    }

    /**
     * 删除套餐项目信息
     */
    @PostMapping("/delete-detail")
    public Object deleteCombinePackageDetail(@RequestBody EditCombinePackageDetailDto dto){
        dto.verifyParams();
        return itemPriceCombinePackageService.deleteCombinePackageDetail(dto.getCombinePackageCode(), dto.getTestItemIds());
    }


}

