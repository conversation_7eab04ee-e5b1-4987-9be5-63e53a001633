package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.UserRoleDto;
import com.labway.lims.base.model.TbRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 角色 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbRoleMapper extends BaseMapper<TbRole> {

    /**
     * 根据用户id查找
     */
    List<TbRole> selectByUserId(@Param("userId") long userId);

    /**
     * 查询到用户的角色
     */
    List<UserRoleDto> selectUserRoleByUserIds(@Param("userIds") Collection<Long> userIds);
}
