package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.model.TbInstrumentGerm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仪器细菌 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Mapper
public interface TbInstrumentGermMapper extends BaseMapper<TbInstrumentGerm> {

    /**
     * 批量 插入
     */
    void batchAddTbInstrumentGerms(@Param("conditions") List<TbInstrumentGerm> conditions);

}
