package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentGroupTestItemDto;
import com.labway.lims.base.api.service.InstrumentGroupTestItemService;
import com.labway.lims.base.mapper.TbInstrumentGroupInstrumentMapper;
import com.labway.lims.base.mapper.TbInstrumentGroupMapper;
import com.labway.lims.base.mapper.TbInstrumentGroupTestItemMapper;
import com.labway.lims.base.model.TbInstrumentGroup;
import com.labway.lims.base.model.TbInstrumentGroupInstrument;
import com.labway.lims.base.model.TbInstrumentGroupTestItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/29 20:39
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "instrument-group-test-item")
public class InstrumentGroupTestItemServiceImpl implements InstrumentGroupTestItemService {

    @Resource
    private TbInstrumentGroupTestItemMapper tbInstrumentGroupTestItemMapper;
    @Resource
    private TbInstrumentGroupInstrumentMapper tbInstrumentGroupInstrumentMapper;
    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private TbInstrumentGroupMapper tbInstrumentGroupMapper;
    @Resource
    private InstrumentGroupTestItemService instrumentGroupTestItemService;

    @Override
    @CacheEvict(allEntries = true)
    public Set<Long> addBatch(List<InstrumentGroupTestItemDto> dtos) {

        final LoginUserHandler.User user = LoginUserHandler.get();
        final Long instrumentGroupId = dtos.get(0).getInstrumentGroupId();
        final TbInstrumentGroup instrumentGroup = tbInstrumentGroupMapper.selectById(instrumentGroupId);
        final TbInstrumentGroupInstrument groupInstrument =
            tbInstrumentGroupInstrumentMapper.selectById(dtos.get(0).getInstrumentGroupInstrumentId());

        if (Objects.isNull(instrumentGroup)) {
            throw new LimsException("不存在专业小组,添加失败");
        }

        if (Objects.isNull(groupInstrument)) {
            throw new LimsException("该专业小组下不存在仪器,添加失败");
        }
        final List<InstrumentGroupTestItemDto> groupTestItems =
            selectByInstrumentGroupInstrumentId(dtos.get(0).getInstrumentGroupInstrumentId());
        if (CollectionUtils.containsAny(
            groupTestItems.stream().map(InstrumentGroupTestItemDto::getTestItemId).collect(Collectors.toSet()),
            dtos.stream().map(InstrumentGroupTestItemDto::getTestItemId).collect(Collectors.toSet()))) {
            throw new LimsException("该专业小组下的仪器检验项目重复,添加失败");
        }

        final List<TbInstrumentGroupTestItem> instrumentGroupTestItems =
            JSON.parseArray(JSON.toJSONString(dtos), TbInstrumentGroupTestItem.class);
        final LinkedList<Long> ids = snowflakeService.genIds(dtos.size());
        for (TbInstrumentGroupTestItem instrumentGroupTestItem : instrumentGroupTestItems) {
            instrumentGroupTestItem.setInstrumentGroupTestItemId(ids.pop());
            instrumentGroupTestItem.setCreatorName(user.getNickname());
            instrumentGroupTestItem.setCreateDate(new Date());
            instrumentGroupTestItem.setCreatorId(user.getUserId());
            instrumentGroupTestItem.setUpdaterName(user.getNickname());
            instrumentGroupTestItem.setUpdaterId(user.getUserId());
            instrumentGroupTestItem.setUpdateDate(new Date());
            instrumentGroupTestItem.setOrgId(user.getOrgId());
            instrumentGroupTestItem.setIsDelete(YesOrNoEnum.NO.getCode());

        }

        tbInstrumentGroupTestItemMapper.addBatch(instrumentGroupTestItems);

        return Set.of();
    }

    @Override
    @Cacheable(key = "'selectByInstrumentGroupId:' + #instrumentGroupId")
    public List<InstrumentGroupTestItemDto> selectByInstrumentGroupId(long instrumentGroupId) {
        final LambdaQueryWrapper<TbInstrumentGroupTestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentGroupTestItem::getInstrumentGroupId, instrumentGroupId)
            .orderByDesc(TbInstrumentGroupTestItem::getInstrumentGroupTestItemId);
        return tbInstrumentGroupTestItemMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    public List<InstrumentGroupTestItemDto> selectByInstrumentGroupIds(Collection<Long> instrumentGroupIds) {

        if (CollectionUtils.isEmpty(instrumentGroupIds)) {
            return Collections.emptyList();
        }

        return instrumentGroupIds.stream().map(instrumentGroupTestItemService::selectByInstrumentGroupId)
            .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteInstrumentGroupTestItemId(long instrumentGroupTestItemId) {
        return tbInstrumentGroupTestItemMapper.deleteById(instrumentGroupTestItemId) > 0;
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByInstrumentGroupId(long instrumentGroupId) {
        final LambdaQueryWrapper<TbInstrumentGroupTestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentGroupTestItem::getInstrumentGroupId, instrumentGroupId);
        return tbInstrumentGroupTestItemMapper.delete(wrapper) > 0;
    }

    @Override
    @Cacheable(key = "'selectByInstrumentGroupInstrumentId:' + #instrumentGroupInstrumentId")
    public List<InstrumentGroupTestItemDto> selectByInstrumentGroupInstrumentId(long instrumentGroupInstrumentId) {
        final LambdaQueryWrapper<TbInstrumentGroupTestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentGroupTestItem::getInstrumentGroupInstrumentId, instrumentGroupInstrumentId)
            .orderByDesc(TbInstrumentGroupTestItem::getInstrumentGroupTestItemId);
        return tbInstrumentGroupTestItemMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    public List<InstrumentGroupTestItemDto> selectByTestItemId(long testItemId) {
        final LambdaQueryWrapper<TbInstrumentGroupTestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentGroupTestItem::getTestItemId, testItemId)
            .orderByDesc(TbInstrumentGroupTestItem::getInstrumentGroupTestItemId);
        return tbInstrumentGroupTestItemMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByGroupId:' + #groupId")
    public List<InstrumentGroupTestItemDto> selectByGroupId(long groupId) {

        return tbInstrumentGroupTestItemMapper.selectByGroupId(groupId);

    }

    @Override
    public List<InstrumentGroupTestItemDto> selectByGroupIds(Collection<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        return groupIds.stream().map(instrumentGroupTestItemService::selectByGroupId).flatMap(Collection::stream)
            .filter(Objects::nonNull).collect(Collectors.toList());

    }

    @Override
    public List<InstrumentGroupTestItemDto>
        selectByInstrumentGroupTestItemIds(Collection<Long> instrumentGroupTestItemIds) {
        if (CollectionUtils.isEmpty(instrumentGroupTestItemIds)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbInstrumentGroupTestItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbInstrumentGroupTestItem::getInstrumentGroupTestItemId, instrumentGroupTestItemIds)
            .orderByDesc(TbInstrumentGroupTestItem::getInstrumentGroupTestItemId);
        return tbInstrumentGroupTestItemMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    private InstrumentGroupTestItemDto convert(TbInstrumentGroupTestItem item) {
        if (Objects.isNull(item)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(item), InstrumentGroupTestItemDto.class);
    }
}
