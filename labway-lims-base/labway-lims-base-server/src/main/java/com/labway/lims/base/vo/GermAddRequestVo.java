package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 细菌 新增 请求 Vo
 * 
 * <AUTHOR>
 * @since 2023/3/20 17:59
 */
@Getter
@Setter
public class GermAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 细菌名称
     */
    private String germName;

    /**
     * 细菌英文名
     */
    private String germEn;

    /**
     * 细菌菌属ID
     */
    private Long germGenusId;

    /**
     * 细菌类别名称
     */
    private String germTypeName;

    /**
     * whonet细菌类型编码
     */
    private String whonetGermTypeCode;

    /**
     * whonet细菌编码
     */
    private String whonetGermCode;

    /**
     * 是否开启统计(0未开启 1开启)
     */
    private Integer enableStatistics;

    /**
     * 是否启用(0未启用 1启用)
     * @see YesOrNoEnum
     */
    private Integer enable;

}
