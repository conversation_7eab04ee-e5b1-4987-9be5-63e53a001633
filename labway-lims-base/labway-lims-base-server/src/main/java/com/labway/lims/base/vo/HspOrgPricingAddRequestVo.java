package com.labway.lims.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 阶梯折扣新增 请求参数Vo
 *
 * <AUTHOR>
 * @since 2023/5/4 14:00
 */

@Getter
@Setter
public class HspOrgPricingAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;
    /**
     * 折前总额上限
     */
    private String beforeMaxPrice;
    /**
     * 折前总额下限
     */
    private String beforeMinPrice;
    /**
     * 折扣率
     */
    private String discount;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date startDate;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endDate;
}
