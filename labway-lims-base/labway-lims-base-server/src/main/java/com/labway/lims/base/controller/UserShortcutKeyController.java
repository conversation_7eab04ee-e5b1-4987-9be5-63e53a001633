package com.labway.lims.base.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.SystemParamMetricEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.ShortcutKeyDTO;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.SystemParamService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 用户快捷键
 * @auther 岳鹏
 * @Date 20231214
 */
@RequestMapping("/shortcut-key")
@RestController
public class UserShortcutKeyController extends BaseController {

    @Resource
    private SystemParamService systemParamService;


    @PostMapping("/save")
    public Object saveOrUpdate(@RequestBody ShortcutKeyDTO dto){
        // dto.verifyParams();
        if(StringUtils.isBlank(dto.getShortcutKey())) throw new IllegalArgumentException("配置不能为空");
        LoginUserHandler.User user = LoginUserHandler.get();
        SystemParamDto systemParamDto = systemParamService.selectByParamMetricAndParamNameAndCreateId(
                SystemParamMetricEnum.SHORTCUT_KEY.toString(),
//                ShortcutKeyDTO.Page.PAGE_MAP.get(dto.getPageId()),
                dto.getPageId(),
                user.getUserId()
        );

        if(Objects.nonNull(systemParamDto)){
            systemParamDto.setParamValue(dto.getShortcutKey());
            return systemParamService.updateByParamId(systemParamDto);
        }

        systemParamDto = new SystemParamDto();
        systemParamDto.setParamMetric(SystemParamMetricEnum.SHORTCUT_KEY.toString());
//        systemParamDto.setParamName(ShortcutKeyDTO.Page.PAGE_MAP.get(dto.getPageId()));
        systemParamDto.setParamName(dto.getPageId());
        systemParamDto.setParamValue(dto.getShortcutKey());
        systemParamDto.setParamRemark("页面快捷键");
        systemParamDto.setMacAddr(Strings.EMPTY);
        systemParamDto.setCpuName(Strings.EMPTY);
        systemParamDto.setEnable(1);
        return systemParamService.add(systemParamDto);
    }

    @PostMapping("/select")
    public Object select(@RequestBody ShortcutKeyDTO dto){
//        dto.verifyParams();
        LoginUserHandler.User user = LoginUserHandler.get();
        SystemParamDto systemParamDto = systemParamService.selectByParamMetricAndParamNameAndCreateId(
                SystemParamMetricEnum.SHORTCUT_KEY.toString(),
//                ShortcutKeyDTO.Page.PAGE_MAP.get(dto.getPageId()),
                dto.getPageId(),
                user.getUserId()
        );
        return Objects.isNull(systemParamDto) ? null : Map.of("shortcutKey",systemParamDto.getParamValue());

    }

}
