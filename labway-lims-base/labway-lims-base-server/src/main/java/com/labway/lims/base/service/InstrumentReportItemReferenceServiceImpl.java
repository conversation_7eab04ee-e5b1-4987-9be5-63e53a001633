package com.labway.lims.base.service;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.enums.AgeUnitEnum;
import com.labway.lims.base.api.enums.RelationalOperatorEnum;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.mapper.TbInstrumentReportItemReferenceMapper;
import com.labway.lims.base.mapstruct.InstrumentReportItemReferenceConverter;
import com.labway.lims.base.model.TbInstrumentReportItemReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "instrument-report-item-reference")
public class InstrumentReportItemReferenceServiceImpl implements InstrumentReportItemReferenceService {

    @Resource
    private TbInstrumentReportItemReferenceMapper instrumentReportItemReferenceMapper;
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private InstrumentReportItemReferenceConverter referenceConverter;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Override
    @Cacheable(key = "'selectByInstrumentReportItemId:' + #instrumentReportItemId")
    public List<InstrumentReportItemReferenceDto> selectByInstrumentReportItemId(long instrumentReportItemId) {
        return instrumentReportItemReferenceMapper.selectList(new LambdaQueryWrapper<TbInstrumentReportItemReference>()
                        .orderByDesc(TbInstrumentReportItemReference::getInstrumentReportItemReferenceId)
                        .eq(TbInstrumentReportItemReference::getInstrumentReportItemId, instrumentReportItemId)
                        .eq(TbInstrumentReportItemReference::getStatus, YesOrNoEnum.YES.getCode())
                )
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByInstrumentReportItemId:' + #instrumentReportItemReferenceId")
    public InstrumentReportItemReferenceDto selectByInstrumentReportItemReferenceId(long instrumentReportItemReferenceId) {
        return convert(instrumentReportItemReferenceMapper.selectById(instrumentReportItemReferenceId));
    }

    @Nonnull
    @Override
    @Cacheable(key = "'selectByInstrumentReportItemReferenceIds:' + #instrumentReportItemReferenceIds")
    public List<InstrumentReportItemReferenceDto> selectByInstrumentReportItemReferenceIds(Collection<Long> instrumentReportItemReferenceIds) {
        if (CollectionUtils.isEmpty(instrumentReportItemReferenceIds)) {
            return Collections.emptyList();
        }
        return instrumentReportItemReferenceMapper.selectBatchIds(instrumentReportItemReferenceIds)
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public long addInstrumentReportItemReference(InstrumentReportItemReferenceDto dto) {

        final InstrumentReportItemDto instrumentReportItem = SpringUtil.getBean(InstrumentReportItemService.class)
                .selectByInstrumentReportItemId(dto.getInstrumentReportItemId());
        if (Objects.isNull(instrumentReportItem)) {
            throw new IllegalArgumentException("仪器报告项目不存在");
        }

        final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentReportItem.getInstrumentId());
        if (Objects.isNull(instrument)) {
            throw new IllegalArgumentException("仪器不存在");
        }

        final TbInstrumentReportItemReference reference = new TbInstrumentReportItemReference();

        BeanUtils.copyProperties(dto, reference);

        reference.setHspOrgId(ObjectUtils.defaultIfNull(dto.getHspOrgId(), NumberUtils.LONG_ZERO));
        reference.setHspOrgName(StringUtils.defaultString(dto.getHspOrgName(), "通用"));

        reference.setReferValueMax(StringUtils.defaultString(reference.getReferValueMax()));
        reference.setReferValueMin(StringUtils.defaultString(reference.getReferValueMin()));
        reference.setReferValueMaxFormula(StringUtils.defaultString(reference.getReferValueMaxFormula()));
        reference.setReferValueMinFormula(StringUtils.defaultString(reference.getReferValueMinFormula()));

        reference.setExcpWarningMax(StringUtils.defaultString(reference.getExcpWarningMax()));
        reference.setExcpWarningMin(StringUtils.defaultString(reference.getExcpWarningMin()));
        reference.setExcpWarningMaxFormula(StringUtils.defaultString(reference.getExcpWarningMaxFormula()));
        reference.setExcpWarningMinFormula(StringUtils.defaultString(reference.getExcpWarningMinFormula()));

        reference.setFatalMax(StringUtils.defaultString(reference.getFatalMax()));
        reference.setFatalMin(StringUtils.defaultString(reference.getFatalMin()));
        reference.setFatalMaxFormula(StringUtils.defaultString(reference.getFatalMaxFormula()));
        reference.setFatalMinFormula(StringUtils.defaultString(reference.getFatalMinFormula()));

        reference.setAgeMax(ObjectUtils.defaultIfNull(reference.getAgeMax(), -1));
        reference.setAgeMin(ObjectUtils.defaultIfNull(reference.getAgeMin(), -1));
        reference.setAgeUnit(StringUtils.defaultString(reference.getAgeUnit()));

        reference.setCnRefereValue(StringUtils.defaultString(reference.getCnRefereValue()));
        reference.setEnRefereValue(StringUtils.defaultString(reference.getEnRefereValue()));
        reference.setCnEnRefereValue(StringUtils.defaultString(reference.getCnEnRefereValue()));

        reference.setInstrumentReportItemReferenceId(snowflakeService.genId());
        reference.setInstrumentReportItemId(dto.getInstrumentReportItemId());
        reference.setReportItemCode(instrumentReportItem.getReportItemCode());
        reference.setReportItemName(instrumentReportItem.getReportItemName());
        reference.setInstrumentId(instrument.getInstrumentId());
        reference.setInstrumentCode(instrument.getInstrumentCode());
        reference.setInstrumentName(instrument.getInstrumentName());

        reference.setStatus(1);
        reference.setIsDelete(YesOrNoEnum.NO.getCode());
        reference.setCreateDate(new Date());
        reference.setUpdateDate(new Date());
        reference.setCreatorId(LoginUserHandler.get().getUserId());
        reference.setCreatorName(LoginUserHandler.get().getNickname());
        reference.setUpdaterId(LoginUserHandler.get().getUserId());
        reference.setUpdaterName(LoginUserHandler.get().getNickname());

        // 校验是否冲突
        dto.setInstrumentReportItemReferenceId(reference.getInstrumentReportItemReferenceId());
        checkMutex(dto);

        // 都为空时，给默认值
        if (StringUtils.isBlank(reference.getAgeUnit()) && Objects.equals(-1, reference.getAgeMax())
                && Objects.equals(-1, reference.getAgeMin())) {
            reference.setAgeMax(200);
            reference.setAgeMin(0);
            reference.setAgeUnit(AgeUnitEnum.YEAR.name());
        }

        if (instrumentReportItemReferenceMapper.insert(reference) < 1) {
            throw new IllegalArgumentException("新增参考范围失败");
        }


        String logContent = String.format("用户 [%s] 添加了仪器 [%s] 下的报告项目 [%s] 的参考范围;参考范围信息：" +
                        "样本类型 [%s] 适用性别 [%s] 适用年龄下限 [%s] 适用年龄上限 [%s] 年龄单位 [%s] 参考范围下限 [%s] 参考范围上限 [%s]" +
                        " 异常提示下限 [%s] 异常提示上限 [%s] 危急值下限 [%s] 危急值上限 [%s] 中文参考值 [%s] 英文参考值 [%s] 中英文参考值 [%s]",
                LoginUserHandler.get().getNickname(),
                reference.getInstrumentName(),
                reference.getReportItemName(),
                reference.getSampleTypeName(),
                reference.getSexStyleName(),
                reference.getAgeMin(),
                reference.getAgeMax(),
                reference.getAgeUnit(),
                reference.getReferValueMin(),
                reference.getReferValueMax(),
                reference.getExcpWarningMin(),
                reference.getExcpWarningMax(),
                reference.getFatalMin(),
                reference.getFatalMax(),
                reference.getCnRefereValue(),
                reference.getEnRefereValue(),
                reference.getCnEnRefereValue()
        );
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_REFERENCE.getDesc())
                        .setContent(logContent).toJSONString());

        log.info("用户 [{}] 添加参考范围 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(reference));

        return reference.getInstrumentReportItemReferenceId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByInstrumentReportItemReferenceId(InstrumentReportItemReferenceDto dto) {

        final InstrumentReportItemReferenceDto oldInstrumentReportItemReference = selectByInstrumentReportItemReferenceId(dto.getInstrumentReportItemReferenceId());
        if (Objects.isNull(oldInstrumentReportItemReference)) {
            throw new IllegalArgumentException("参考范围不存在");
        }


        final TbInstrumentReportItemReference reference = new TbInstrumentReportItemReference();

        BeanUtils.copyProperties(dto, reference);


        reference.setHspOrgId(ObjectUtils.defaultIfNull(dto.getHspOrgId(), NumberUtils.LONG_ZERO));
        reference.setHspOrgName(StringUtils.defaultString(dto.getHspOrgName(), "通用"));

        reference.setReferValueMax(StringUtils.defaultString(reference.getReferValueMax()));
        reference.setReferValueMin(StringUtils.defaultString(reference.getReferValueMin()));

        reference.setExcpWarningMax(StringUtils.defaultString(reference.getExcpWarningMax()));
        reference.setExcpWarningMin(StringUtils.defaultString(reference.getExcpWarningMin()));

        reference.setFatalMax(StringUtils.defaultString(reference.getFatalMax()));
        reference.setFatalMin(StringUtils.defaultString(reference.getFatalMin()));

        reference.setAgeMax(ObjectUtils.defaultIfNull(reference.getAgeMax(), -1));
        reference.setAgeMin(ObjectUtils.defaultIfNull(reference.getAgeMin(), -1));
        reference.setAgeUnit(StringUtils.defaultString(reference.getAgeUnit()));


        // 不可修改字段
        reference.setInstrumentReportItemId(null);
        reference.setReportItemCode(null);
        reference.setReportItemName(null);
        reference.setInstrumentId(null);
        reference.setInstrumentCode(null);
        reference.setInstrumentName(null);
        reference.setStatus(null);
        reference.setCreateDate(null);
        reference.setCreatorId(null);
        reference.setCreatorName(null);

        reference.setUpdateDate(new Date());
        reference.setUpdaterId(LoginUserHandler.get().getUserId());
        reference.setUpdaterName(LoginUserHandler.get().getNickname());

        // 校验是否冲突
        dto.setInstrumentReportItemId(oldInstrumentReportItemReference.getInstrumentReportItemId());
        checkMutex(dto);

        if (instrumentReportItemReferenceMapper.updateById(reference) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改参考范围 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(reference));

        return true;
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByInstrumentReportItemReferenceId(long instrumentReportItemReferenceId) {
        return instrumentReportItemReferenceMapper.deleteById(instrumentReportItemReferenceId) > 0;
    }

    @Nullable
    @Override
    public List<InstrumentReportItemReferenceDto> selectByReportItemCodeAndOrgId(String reportItemCode, long orgId) {
        final LambdaQueryWrapper<TbInstrumentReportItemReference> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentReportItemReference::getReportItemCode, reportItemCode);
        return instrumentReportItemReferenceMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByInstrumentId:' + #instrumentId")
    public List<InstrumentReportItemReferenceDto> selectByInstrumentId(long instrumentId) {
        final LambdaQueryWrapper<TbInstrumentReportItemReference> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentReportItemReference::getInstrumentId, instrumentId);
        return instrumentReportItemReferenceMapper
                .selectList(wrapper)
                .stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByInstrumentGroupId:' + #instrumentGroupId")
    public List<InstrumentReportItemReferenceDto> selectByInstrumentGroupId(long instrumentGroupId) {
        return instrumentReportItemReferenceMapper.selectByInstrumentGroupId(instrumentGroupId);
    }

    @Override
    @Cacheable(key = "'selectByInstrumentGroupIds:' + #instrumentGroupIds")
    public List<InstrumentReportItemReferenceDto> selectByInstrumentGroupIds(Collection<Long> instrumentGroupIds) {
        return instrumentReportItemReferenceMapper.selectByInstrumentGroupIds(instrumentGroupIds);
    }

    @Override
    @Cacheable(key = "'selectByInstrumentGroupIdAndReportItemCode:' + #instrumentGroupId + ',' + #reportItemCode")
    public List<InstrumentReportItemReferenceDto> selectByInstrumentGroupIdAndReportItemCode(long instrumentGroupId, String reportItemCode) {
        if (StringUtils.isBlank(reportItemCode)) {
            return Collections.emptyList();
        }
        return instrumentReportItemReferenceMapper.selectByInstrumentGroupIdAndReportItemCode(instrumentGroupId, reportItemCode);
    }

    @Override
    public List<InstrumentReportItemReferenceDto> selectByInstrumentIds(Collection<Long> instrumentIds) {
        if (CollectionUtils.isEmpty(instrumentIds)) {
            return Collections.emptyList();
        }

        return instrumentIds.stream().map(SpringUtil.getBean(InstrumentReportItemReferenceService.class)::selectByInstrumentId)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull).collect(Collectors.toList());

    }

    @Override
    public List<InstrumentReportItemReferenceDto> selectByInstrumentIdAndReportItemCodes(long instrumentId, Collection<String> reportItemCodes) {
        if (CollectionUtils.isEmpty(reportItemCodes)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbInstrumentReportItemReference> in = Wrappers.lambdaQuery(TbInstrumentReportItemReference.class)
                .eq(TbInstrumentReportItemReference::getInstrumentId, instrumentId)
                .in(TbInstrumentReportItemReference::getReportItemCode, reportItemCodes);

        return instrumentReportItemReferenceMapper.selectList(in).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<InstrumentReportItemReferenceDto>> selectByInstrumentIdAndReportItemCodesAsMap(long instrumentId, Collection<String> reportItemCodes) {
        return selectByInstrumentIdAndReportItemCodes(instrumentId, reportItemCodes)
                .stream().collect(Collectors.groupingBy(InstrumentReportItemReferenceDto::getReportItemCode));
    }

    @Override
    public List<InstrumentReportItemReferenceDto> selectByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return instrumentReportItemReferenceMapper.selectBatchIds(ids)
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public InstrumentReportItemReferenceDto selectById(long id) {
        return selectByIds(List.of(id)).stream().findFirst().orElse(null);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void deleteByInstrumentReportItemId(long instrumentReportItemId) {
        final LambdaQueryWrapper<TbInstrumentReportItemReference> wrapper = Wrappers.lambdaQuery(TbInstrumentReportItemReference.class)
                .eq(TbInstrumentReportItemReference::getInstrumentReportItemId, instrumentReportItemId);
        instrumentReportItemReferenceMapper.delete(wrapper);
    }

    private void checkMutex(InstrumentReportItemReferenceDto reference) {


        // 都为空时，跳过校验
        if (StringUtils.isBlank(reference.getAgeUnit()) && Objects.isNull(reference.getAgeMax())
                && Objects.isNull(reference.getAgeMin())) {
            return;
        }

        if (StringUtils.isBlank(reference.getAgeUnit()) || Objects.isNull(reference.getAgeMax())
                || Objects.isNull(reference.getAgeMin())) {
            throw new IllegalArgumentException("年龄范围错误");
        }


        // 最多200年
        if (AgeUnitEnum.YEAR.name().equals(reference.getAgeUnit()) && (reference.getAgeMax() > 200)) {
            throw new IllegalArgumentException("年龄范围过大，应当小于 200 岁");
        }

        // 最多2400月
        if (AgeUnitEnum.MONTH.name().equals(reference.getAgeUnit()) && (reference.getAgeMax() > 2400)) {
            throw new IllegalArgumentException("年龄范围过大，应当小于 2400 月");
        }

        if (Objects.isNull(reference.getHspOrgId())) {
            // 0L 代表机构通用
            reference.setHspOrgId(0L);
        }

        List<InstrumentReportItemReferenceDto> refs = selectByInstrumentReportItemId(reference.getInstrumentReportItemId());
        refs.removeIf(e -> Objects.equals(e.getInstrumentReportItemReferenceId(), reference.getInstrumentReportItemReferenceId()));

        if (CollectionUtils.isEmpty(refs)) {
            return;
        }


        // 查询和现在相同的送检机构
        refs = refs.stream()
                .filter(e -> Objects.nonNull(reference.getHspOrgId()) && Objects.equals(reference.getHspOrgId(), e.getHspOrgId()))
                .collect(Collectors.toList());


        // 查询之前所有的参考范围 , 和当前性别相同 或 通用
        refs = refs.stream()
                // 相同性别才会互斥
                .filter(e -> {
                    if (Objects.isNull(e.getSexStyle())) {
                        return false;
                    }
                    return Objects.equals(reference.getSexStyle(), e.getSexStyle());
                })
                // 样本类型过滤
                // 如果相同时，那么校验样本类型
                .filter(e -> Objects.equals(e.getSampleTypeCode(), reference.getSampleTypeCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(refs)) {
            return;
        }

        // 年龄区间
        // 判断方法是根据每个年龄范围生成区间，然后判断区间是否相交
        final List<List<Integer>> ranges = new ArrayList<>(refs.size());

        for (var ref : refs) {

            int start = ref.getAgeMin(), end = ref.getAgeMax();

            // 如果是大于那就是 +1 开始
            if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(ref.getAgeMinFormula()), RelationalOperatorEnum.GT)) {
                start = ref.getAgeMin() + 1;
            }

            // 如果是小于那就是 -1 结束
            if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(ref.getAgeMaxFormula()), RelationalOperatorEnum.LT)) {
                end = ref.getAgeMax() - 1;
            }

            // 把所有的单位转化为 天
            if (AgeUnitEnum.MONTH.name().equals(ref.getAgeUnit())) {
                start = start * MONTH_DAYS;
                end = end * MONTH_DAYS;
            } else if (AgeUnitEnum.YEAR.name().equals(ref.getAgeUnit())) {
                start = start * YEAR_DAYS;
                end = end * YEAR_DAYS;
            }


            ranges.add(IntStream.rangeClosed(start, end).boxed().collect(Collectors.toList()));

        }

        int start = reference.getAgeMin();
        int end = reference.getAgeMax();

        // 如果是大于那就是 +1 开始
        if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(reference.getAgeMinFormula()), RelationalOperatorEnum.GT)) {
            start = start + 1;
        }

        // 如果是小于那就是 -1 结束
        if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(reference.getAgeMaxFormula()), RelationalOperatorEnum.LT)) {
            end = end - 1;
        }

        // 把所有的单位转化为 天
        if (AgeUnitEnum.MONTH.name().equals(reference.getAgeUnit())) {
            start = start * MONTH_DAYS;
            end = end * MONTH_DAYS;
        } else if (AgeUnitEnum.YEAR.name().equals(reference.getAgeUnit())) {
            start = start * YEAR_DAYS;
            end = end * YEAR_DAYS;
        }

        // 判断是否有相交
        for (List<Integer> range : ranges) {
            for (int i = start; i <= end; i++) {
                // 二分查找
                if (Collections.binarySearch(range, i) >= 0) {
                    throw new IllegalStateException("年龄范围冲突 , 请检查");
                }
            }
        }
    }


    private InstrumentReportItemReferenceDto convert(TbInstrumentReportItemReference instrumentReportItemReference) {
        if (Objects.isNull(instrumentReportItemReference)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(instrumentReportItemReference), InstrumentReportItemReferenceDto.class);
    }

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public List<Long> copyReportItemReference(Long fromInstrumentReportItemId, InstrumentReportItemDto instrumentReportItemDto) {
        List<Long> ids = new ArrayList<>();
        // 仪器报告项目ID
        Long instrumentReportItemId = instrumentReportItemDto.getInstrumentReportItemId();

        {
            // 删除原有的参考范围
            LambdaQueryWrapper<TbInstrumentReportItemReference> wrapper =
                    Wrappers
                            .lambdaQuery(TbInstrumentReportItemReference.class)
                            .eq(TbInstrumentReportItemReference::getInstrumentReportItemId, instrumentReportItemId);
            instrumentReportItemReferenceMapper.delete(wrapper);
        }

        Date current = new Date();
        InstrumentReportItemReferenceService thisProxy = (InstrumentReportItemReferenceService) AopContext.currentProxy();
        List<InstrumentReportItemReferenceDto> referenceDtos = thisProxy.selectByInstrumentReportItemId(fromInstrumentReportItemId);
        for (InstrumentReportItemReferenceDto referenceDto : referenceDtos) {
            TbInstrumentReportItemReference reference = referenceConverter.convertDto2Entity(referenceDto);

            reference.setInstrumentReportItemReferenceId(snowflakeService.genId());
            reference.setInstrumentReportItemId(instrumentReportItemId);
            reference.setReportItemCode(referenceDto.getReportItemCode());
            reference.setReportItemName(referenceDto.getReportItemName());
            reference.setInstrumentId(instrumentReportItemDto.getInstrumentId());
            reference.setInstrumentCode(instrumentReportItemDto.getInstrumentCode());
            reference.setInstrumentName(instrumentReportItemDto.getInstrumentName());

            reference.setStatus(1);
            reference.setIsDelete(YesOrNoEnum.NO.getCode());
            reference.setCreateDate(current);
            reference.setCreatorId(LoginUserHandler.get().getUserId());
            reference.setCreatorName(LoginUserHandler.get().getNickname());
            reference.setUpdateDate(current);
            reference.setUpdaterId(LoginUserHandler.get().getUserId());
            reference.setUpdaterName(LoginUserHandler.get().getNickname());

            if (instrumentReportItemReferenceMapper.insert(reference) < 1) {
                throw new IllegalArgumentException("拷贝参考范围失败");
            }

            String logContent = String.format("用户 [%s] 拷贝了仪器 [%s] 下的报告项目 [%s] 的参考范围;参考范围信息：" +
                            "样本类型 [%s] 适用性别 [%s] 适用年龄下限 [%s] 适用年龄上限 [%s] 年龄单位 [%s] 参考范围下限 [%s] 参考范围上限 [%s]" +
                            " 异常提示下限 [%s] 异常提示上限 [%s] 危急值下限 [%s] 危急值上限 [%s] 中文参考值 [%s] 英文参考值 [%s] 中英文参考值 [%s]",
                    LoginUserHandler.get().getNickname(),
                    reference.getInstrumentName(),
                    reference.getReportItemName(),
                    reference.getSampleTypeName(),
                    reference.getSexStyleName(),
                    reference.getAgeMin(),
                    reference.getAgeMax(),
                    reference.getAgeUnit(),
                    reference.getReferValueMin(),
                    reference.getReferValueMax(),
                    reference.getExcpWarningMin(),
                    reference.getExcpWarningMax(),
                    reference.getFatalMin(),
                    reference.getFatalMax(),
                    reference.getCnRefereValue(),
                    reference.getEnRefereValue(),
                    reference.getCnEnRefereValue()
            );

            // 异步发送消息
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_REFERENCE.getDesc())
                            .setContent(logContent).toJSONString());

            log.info("用户 [{}] 拷贝参考范围 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(reference));

            ids.add(reference.getInstrumentReportItemReferenceId());
        }

        return ids;
    }

}
