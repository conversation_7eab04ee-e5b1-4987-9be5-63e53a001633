package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 机构信息维护
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_organization")
public class TbOrganization implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机构ID
     */
    @TableId
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 机构说明
     */
    private String orgRemark;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构类型ID
     */
    private Long orgType;

    /**
     * 机构类型名称
     */
    private String orgTypeName;

    /**
     * 是否外送机构
     * @see YesOrNoEnum
     */
    private Integer isExport;

    /**
     * 开票名称
     */
    private String invoice;

    /**
     * 销售区域
     */
    private String saleArea;

    /**
     * 销售部门ID
     */
    private String saleDeptCode;

    /**
     * 销售部门名称
     */
    private String saleDeptName;

    /**
     * 销售类型ID
     */
    private String saleTypeCode;

    /**
     * 销售类型名称
     */
    private String saleTypeName;

    /**
     * 是否支持分血
     */
    private Integer enableSplitBlood;

    /**
     * 是否启用(0未启用 1已启用)
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 位置(经纬度)
     */
    private String location;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
