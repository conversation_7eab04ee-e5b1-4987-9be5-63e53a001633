package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.HspOrgPricingDto;
import com.labway.lims.base.model.TbHspOrgPricing;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 客户阶梯折扣信息 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 19:21
 */
@Mapper(componentModel = "spring")
public interface HspOrgPricingConverter {

    HspOrgPricingDto hspOrgPricingDtoFromTbObj(TbHspOrgPricing obj);
    List<HspOrgPricingDto> hspOrgPricingDtoListFromTbObjList(List<TbHspOrgPricing> list);

}
