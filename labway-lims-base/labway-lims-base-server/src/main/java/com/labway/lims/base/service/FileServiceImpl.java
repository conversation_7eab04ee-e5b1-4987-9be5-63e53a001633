package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.FileDto;
import com.labway.lims.base.api.service.FileService;
import com.labway.lims.base.mapper.TbFileMapper;
import com.labway.lims.base.model.TbFile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "file")
public class FileServiceImpl implements FileService {
    @Resource
    private TbFileMapper fileMapper;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Cacheable(key = "'selectByParentFileId:' + #parentFileId")
    public List<FileDto> selectByParentFileId(long parentFileId) {
        return fileMapper.selectList(new LambdaQueryWrapper<TbFile>()
                        .orderByDesc(TbFile::getFileId)
                        .eq(TbFile::getOrgId,LoginUserHandler.get().getOrgId())
                        .eq(TbFile::getParentFileId, parentFileId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByFileId:' + #fileId")
    public FileDto selectByFileId(long fileId) {
        return convert(fileMapper.selectById(fileId));
    }


    @Override
    public List<FileDto> selectByFileIds(Collection<Long> fileIds) {
        return Collections.emptyList();
    }


    private FileDto convert(TbFile file) {
        if (Objects.isNull(file)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(file), FileDto.class);
    }

    @Override
    @CacheEvict(allEntries = true)
    public long addFile(FileDto f) {

        final String key = redisPrefix.getBasePrefix() + "AddFile:" + f.getParentFileId();

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofSeconds(5)))) {
            throw new IllegalStateException("正在新增文件中");
        }


        try {

            if (exists(f.getParentFileId(), f.getFileName())) {
                throw new IllegalStateException(String.format("文件 [%s] 已经存在", f.getFileName()));
            }

            final TbFile file = new TbFile();
            BeanUtils.copyProperties(f, file);

            file.setFileId(snowflakeService.genId());

            file.setIsDelete(YesOrNoEnum.NO.getCode());
            file.setCreateDate(new Date());
            file.setCreatorId(LoginUserHandler.get().getUserId());
            file.setCreatorName(LoginUserHandler.get().getNickname());
            file.setUpdateDate(new Date());
            file.setUpdaterName(LoginUserHandler.get().getNickname());
            file.setUpdaterId(LoginUserHandler.get().getUserId());
            file.setOrgId(LoginUserHandler.get().getOrgId());
            file.setOrgName(LoginUserHandler.get().getOrgName());

            if (fileMapper.insert(file) < 1) {
                throw new IllegalStateException("添加文件失败");
            }

            log.info("用户 [{}] 添加文件成功 [{}]", LoginUserHandler.get().getNickname(), file.getFileName());

            return file.getFileId();

        } finally {
            stringRedisTemplate.delete(key);
        }

    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByFileId(long fileId) {

        fileMapper.deleteById(fileId);

        log.info("用户 [{}] 删除了文件 [{}]", LoginUserHandler.get().getNickname(), fileId);

    }

    @Override
    public boolean exists(long parentFileId, String fileName) {
        return selectByParentFileId(parentFileId)
                .stream().anyMatch(e -> Objects.equals(e.getFileName(), fileName));
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByFileId(FileDto file) {

        final FileDto of = selectByFileId(file.getFileId());
        if (Objects.isNull(of)) {
            throw new IllegalStateException("文件不存在");
        }

        if (selectByParentFileId(of.getParentFileId()).stream().anyMatch(e -> Objects.equals(e.getFileName(), file.getFileName())
                && !Objects.equals(e.getFileId(), file.getFileId()))) {
            throw new IllegalStateException(String.format("文件名 [%s] 已存在", file.getFileName()));
        }

        final TbFile f = new TbFile();
        BeanUtils.copyProperties(file, f);

        f.setUpdateDate(new Date());
        f.setUpdaterName(LoginUserHandler.get().getNickname());
        f.setUpdaterId(LoginUserHandler.get().getUserId());

        log.info("用户 [{}] 修改文件成功 [{}]", LoginUserHandler.get().getNickname(), file.getFileName());

        return fileMapper.updateById(f) > 0;
    }
}
