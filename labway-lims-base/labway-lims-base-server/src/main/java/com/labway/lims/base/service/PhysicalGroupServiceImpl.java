package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.PhysicalGroupDto;
import com.labway.lims.base.api.service.PhysicalGroupService;
import com.labway.lims.base.mapper.TbPhysicalGroupMapper;
import com.labway.lims.base.mapstruct.PhysicalGroupConverter;
import com.labway.lims.base.model.TbPhysicalGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 体检团体 Service impl
 * 
 * <AUTHOR>
 * @since 2023/3/27 19:40
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "physical-group")
public class PhysicalGroupServiceImpl implements PhysicalGroupService {

    @Resource
    private TbPhysicalGroupMapper tbPhysicalGroupMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private PhysicalGroupConverter physicalGroupConverter;
    /**
     * 体检单位 信息
     */
    private static final String PHYSICAL_GROUP_INFO = "PHYSICAL_GROUP_INFO:";

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public long addPhysicalGroup(PhysicalGroupDto physicalGroupDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbPhysicalGroup tbPhysicalGroup = new TbPhysicalGroup();
        tbPhysicalGroup.setPhysicalGroupName(physicalGroupDto.getPhysicalGroupName());
        tbPhysicalGroup.setHspOrgId(physicalGroupDto.getHspOrgId());
        tbPhysicalGroup.setHspOrgName(physicalGroupDto.getHspOrgName());
        tbPhysicalGroup.setContactUser(ObjectUtils.defaultIfNull(physicalGroupDto.getContactUser(), ""));
        tbPhysicalGroup.setContactPhone(ObjectUtils.defaultIfNull(physicalGroupDto.getContactPhone(), ""));
        tbPhysicalGroup.setEnable(physicalGroupDto.getEnable());

        tbPhysicalGroup.setPhysicalGroupId(snowflakeService.genId());
        tbPhysicalGroup.setOrgId(loginUser.getOrgId());
        tbPhysicalGroup.setOrgName(loginUser.getOrgName());
        tbPhysicalGroup.setCreateDate(new Date());
        tbPhysicalGroup.setUpdateDate(new Date());
        tbPhysicalGroup.setCreatorId(loginUser.getUserId());
        tbPhysicalGroup.setCreatorName(loginUser.getNickname());
        tbPhysicalGroup.setUpdaterId(loginUser.getUserId());
        tbPhysicalGroup.setUpdaterName(loginUser.getNickname());
        tbPhysicalGroup.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbPhysicalGroupMapper.insert(tbPhysicalGroup) < 1) {
            throw new LimsException("添加体检单位失败");
        }
        log.info("用户 [{}] 新增体检单位[{}]成功", loginUser.getNickname(), JSON.toJSONString(tbPhysicalGroup));

        return tbPhysicalGroup.getPhysicalGroupId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void deleteByPhysicalGroupIds(Collection<Long> physicalGroupIds) {
        if (CollectionUtils.isEmpty(physicalGroupIds)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除体检单位成功 [{}] 结果 [{}]", loginUser.getNickname(), physicalGroupIds,
            tbPhysicalGroupMapper.deleteBatchIds(physicalGroupIds) > 0);

        Set<String> keys = physicalGroupIds.stream().map(x -> redisPrefix.getBasePrefix() + PHYSICAL_GROUP_INFO + x)
            .collect(Collectors.toSet());
        stringRedisTemplate.delete(keys);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void updateByPhysicalGroupId(PhysicalGroupDto physicalGroupDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbPhysicalGroup target = new TbPhysicalGroup();
        BeanUtils.copyProperties(physicalGroupDto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbPhysicalGroupMapper.updateById(target) < 1) {
            throw new LimsException("修改体检团体失败");
        }

        // 删除缓存
        String key = redisPrefix.getBasePrefix() + PHYSICAL_GROUP_INFO + target.getPhysicalGroupId();
        stringRedisTemplate.delete(key);

        log.info("用户 [{}] 修改体检团体成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }

    @Override
    @Cacheable(key = "'selectByOrgId:' + #orgId")
    public List<PhysicalGroupDto> selectByOrgId(long orgId) {
        if (orgId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbPhysicalGroup> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbPhysicalGroup::getOrgId, orgId);
        queryWrapper.eq(TbPhysicalGroup::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbPhysicalGroup::getCreateDate);
        return physicalGroupConverter.physicalGroupDtoListFromTbObjList(tbPhysicalGroupMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByPhysicalGroupName:' + #physicalGroupName + ',' + #orgId")
    public PhysicalGroupDto selectByPhysicalGroupName(String physicalGroupName, long orgId) {
        if (StringUtils.isBlank(physicalGroupName)) {
            return null;
        }
        LambdaQueryWrapper<TbPhysicalGroup> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TbPhysicalGroup::getPhysicalGroupName, physicalGroupName);
        wrapper.eq(TbPhysicalGroup::getOrgId, orgId);
        wrapper.eq(TbPhysicalGroup::getIsDelete, YesOrNoEnum.NO.getCode());
        wrapper.last("limit 1");

        return physicalGroupConverter.physicalGroupDtoFromTbObj(tbPhysicalGroupMapper.selectOne(wrapper));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByPhysicalGroupId:' + #physicalGroupId")
    public PhysicalGroupDto selectByPhysicalGroupId(long physicalGroupId) {
        // 先判断缓存
        PhysicalGroupDto target;
        String key = redisPrefix.getBasePrefix() + PHYSICAL_GROUP_INFO + physicalGroupId;
        String result = stringRedisTemplate.opsForValue().get(key);

        if (StringUtils.isNotBlank(result)) {
            target = JSON.parseObject(result, PhysicalGroupDto.class);
        } else {
            LambdaQueryWrapper<TbPhysicalGroup> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(TbPhysicalGroup::getPhysicalGroupId, physicalGroupId);
            queryWrapper.eq(TbPhysicalGroup::getIsDelete, YesOrNoEnum.NO.getCode()) .last("limit 1");
            target = physicalGroupConverter.physicalGroupDtoFromTbObj(tbPhysicalGroupMapper.selectOne(queryWrapper));
            if (Objects.nonNull(target)) {
                stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(target), Duration.ofHours(2));
            }
        }
        return target;
    }

    @Override
    public List<PhysicalGroupDto> selectByPhysicalGroupIds(Collection<Long> physicalGroupIds) {
        if (CollectionUtils.isEmpty(physicalGroupIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbPhysicalGroup> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbPhysicalGroup::getPhysicalGroupId, physicalGroupIds);
        queryWrapper.eq(TbPhysicalGroup::getIsDelete, YesOrNoEnum.NO.getCode());
        return physicalGroupConverter.physicalGroupDtoListFromTbObjList(tbPhysicalGroupMapper.selectList(queryWrapper));
    }

}
