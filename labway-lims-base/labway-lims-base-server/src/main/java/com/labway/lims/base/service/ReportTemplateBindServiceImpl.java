package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ReportTemplateBindTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentGroupTemplateQueryDto;
import com.labway.lims.base.api.dto.MatchBindReportDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.mapper.TbReportTemplateBindMapper;
import com.labway.lims.base.mapstruct.ReportTemplateBindConverter;
import com.labway.lims.base.model.TbReportTemplateBind;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告单模板绑定 Service impl
 *
 * <AUTHOR>
 * @since 2023/5/24 14:13
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "report-template-bind")
public class ReportTemplateBindServiceImpl implements ReportTemplateBindService {

    @Resource
    private TbReportTemplateBindMapper tbReportTemplateBindMapper;
    @Resource
    private ReportTemplateBindConverter reportTemplateBindConverter;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public List<ReportTemplateBindDto> selectByBizIdsAndBindType(Collection<Long> bizIds,
                                                                 ReportTemplateBindTypeEnum bindTypeEnum) {

        if (CollectionUtils.isEmpty(bizIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbReportTemplateBind> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbReportTemplateBind::getBindType, bindTypeEnum.getCode());
        queryWrapper.in(TbReportTemplateBind::getBizId, bizIds);
        queryWrapper.eq(TbReportTemplateBind::getIsDelete, YesOrNoEnum.NO.getCode());
        return reportTemplateBindConverter
                .reportTemplateBindDtoListFromTbObj(tbReportTemplateBindMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByBizId:' + #bizId")
    public ReportTemplateBindDto selectByBizId(long bizId) {
        LambdaQueryWrapper<TbReportTemplateBind> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbReportTemplateBind::getBizId, bizId);
        queryWrapper.eq(TbReportTemplateBind::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.eq(TbReportTemplateBind::getEnable,YesOrNoEnum.YES.getCode());
        queryWrapper.last("limit 1");
        return reportTemplateBindConverter
                .reportTemplateBindDtoFromTbObj(tbReportTemplateBindMapper.selectOne(queryWrapper));

    }

    @Override
    public List<ReportTemplateBindDto> selectByBizIds(Collection<Long> bizIds) {
        LambdaQueryWrapper<TbReportTemplateBind> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbReportTemplateBind::getBizId, bizIds);
        queryWrapper.eq(TbReportTemplateBind::getIsDelete, YesOrNoEnum.NO.getCode());
        return tbReportTemplateBindMapper.selectList(queryWrapper).stream()
                .map(reportTemplateBindConverter::reportTemplateBindDtoFromTbObj)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectMultiByBizId:' + #bizId")
    public List<ReportTemplateBindDto> selectMultiByBizId(long bizId) {
        LambdaQueryWrapper<TbReportTemplateBind> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbReportTemplateBind::getBizId, bizId);
        queryWrapper.eq(TbReportTemplateBind::getIsDelete, YesOrNoEnum.NO.getCode());
        return tbReportTemplateBindMapper.selectList(queryWrapper).stream()
                .map(reportTemplateBindConverter::reportTemplateBindDtoFromTbObj).collect(Collectors.toList());
    }

    @Override
    public List<ReportTemplateBindDto> selectByGroupId(long hspOrgId,long groupId) {
        LambdaQueryWrapper<TbReportTemplateBind> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbReportTemplateBind::getGroupId, groupId);
        queryWrapper.eq(TbReportTemplateBind::getHspOrgId,hspOrgId);
        queryWrapper.eq(TbReportTemplateBind::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.eq(TbReportTemplateBind::getEnable,YesOrNoEnum.YES.getCode());
        return tbReportTemplateBindMapper.selectList(queryWrapper).stream()
                .map(reportTemplateBindConverter::reportTemplateBindDtoFromTbObj).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public void addBatchReportTemplateBind(List<ReportTemplateBindDto> dtos) {
        if (!CollectionUtils.isEmpty(dtos)){
            //生成批量ID
            LinkedList<Long> ids = snowflakeService.genIds(dtos.size());
            final LoginUserHandler.User user = LoginUserHandler.get();
            for (ReportTemplateBindDto bindDto:dtos){
                bindDto.setReportTemplateBindId(ids.pop());
                bindDto.setIsDelete(YesOrNoEnum.NO.getCode());
                bindDto.setCreateDate(new Date());
                bindDto.setCreatorId(user.getUserId());
                bindDto.setCreatorName(user.getNickname());
                bindDto.setUpdateDate(new Date());
                bindDto.setUpdaterId(user.getUserId());
                bindDto.setUpdaterName(user.getNickname());
            }
            tbReportTemplateBindMapper.insertBatch(dtos);
        }
    }

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean updateReportTemplateBinds(List<ReportTemplateBindDto> dtos, Long bindGroupId) {
        if (!CollectionUtils.isEmpty(dtos) && !Objects.isNull(bindGroupId)){
            //先删除 在插入
            LambdaQueryWrapper<TbReportTemplateBind> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(TbReportTemplateBind::getBindGroupId, bindGroupId);
            tbReportTemplateBindMapper.delete(queryWrapper);

            //生成批量ID
            LinkedList<Long> ids = snowflakeService.genIds(dtos.size());
            final LoginUserHandler.User user = LoginUserHandler.get();
            for (ReportTemplateBindDto bindDto:dtos){
                bindDto.setReportTemplateBindId(ids.pop());
                bindDto.setIsDelete(YesOrNoEnum.NO.getCode());
                bindDto.setCreateDate(new Date());
                bindDto.setCreatorId(user.getUserId());
                bindDto.setCreatorName(user.getNickname());
                bindDto.setUpdateDate(new Date());
                bindDto.setUpdaterId(user.getUserId());
                bindDto.setUpdaterName(user.getNickname());
            }
            tbReportTemplateBindMapper.insertBatch(dtos);
        }
        return false;
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByBindGroupIds(Collection<Long> bindGroupIds) {
        if (CollectionUtils.isEmpty(bindGroupIds)) {
            return;
        }
        LambdaQueryWrapper<TbReportTemplateBind> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbReportTemplateBind::getBindGroupId, bindGroupIds);
        tbReportTemplateBindMapper.delete(queryWrapper);
        log.info("用户 [{}] 删除模板绑定记录成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(bindGroupIds));
    }

    @Override
    public List<ReportTemplateBindDto> selectByIds(Collection<Long> ids) {
        return JSON.parseArray(JSON.toJSONString(tbReportTemplateBindMapper.selectBatchIds(ids)), ReportTemplateBindDto.class);
    }

    @Override
    public List<ReportTemplateBindDto> selectByBindGroupIds(Collection<Long> bindGroupIds) {
        LambdaQueryWrapper<TbReportTemplateBind> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbReportTemplateBind::getBindGroupId, bindGroupIds);
        queryWrapper.eq(TbReportTemplateBind::getIsDelete, YesOrNoEnum.NO.getCode());
        return reportTemplateBindConverter
                .reportTemplateBindDtoListFromTbObj(tbReportTemplateBindMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public ReportTemplateBindDto selectById(long reportTemplateBindId) {
        return JSON.parseObject(JSON.toJSONString(tbReportTemplateBindMapper.selectById(reportTemplateBindId)), ReportTemplateBindDto.class);
    }

    @Override
    public List<ReportTemplateBindDto> selectByBindType(ReportTemplateBindTypeEnum bindTypeEnum,Long excludeId) {
        LambdaQueryWrapper<TbReportTemplateBind> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbReportTemplateBind::getBindType, bindTypeEnum.getCode());
        queryWrapper.eq(TbReportTemplateBind::getIsDelete, YesOrNoEnum.NO.getCode());
        if (excludeId != null){
            queryWrapper.ne(TbReportTemplateBind::getBindGroupId,excludeId);
        }
        return reportTemplateBindConverter
                .reportTemplateBindDtoListFromTbObj(tbReportTemplateBindMapper.selectList(queryWrapper));
    }

    @Override
    public ReportTemplateBindDto findMatchReportTemplate(MatchBindReportDto bindReportDto) {
        if (bindReportDto == null){
            return null;
        }
        ReportTemplateBindDto reportTemplateBind = null;
        // 如果只有一个检验项目，那么优先查询检验项目模板
        if (CollectionUtils.isNotEmpty(bindReportDto.getTestItemIds())) {
            final List<ReportTemplateBindDto> reportTemplateBinds = selectByBizIds(bindReportDto.getTestItemIds())
                    .stream().filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                    .collect(Collectors.toList());
            //1.0.7更新 优先判断同一送检机构下 项目完全一致，再判断通用送检机构下 项目完全一致
            List<ReportTemplateBindDto> hspReportBinds = reportTemplateBinds.stream().filter(e -> Objects.equals(e.getHspOrgId(), bindReportDto.getHspOrgId())).collect(Collectors.toList());
            //获取通用机构
            List<ReportTemplateBindDto> commonReportBinds = reportTemplateBinds.stream().filter(e -> Objects.equals(e.getHspOrgId(), NumberUtils.LONG_ZERO)).collect(Collectors.toList());
            if (hspReportBinds.size() == bindReportDto.getTestItemIds().size() || commonReportBinds.size() == bindReportDto.getTestItemIds().size()) {
                // 优先判断同送检机构下检验项目完全一致时
                if (hspReportBinds.size() == bindReportDto.getTestItemIds().size() && hspReportBinds.stream().map(ReportTemplateBindDto::getReportTemplateCode)
                        .distinct().count() == NumberUtils.LONG_ONE) {
                    reportTemplateBind = hspReportBinds.iterator().next();
                    log.info("条码 [{}] 下面的报告项目使用统一的报告单模板，固使用项目模版 [{}]", bindReportDto.getBarcode(),
                            reportTemplateBind.getReportTemplateCode());
                }else if (commonReportBinds.size() == bindReportDto.getTestItemIds().size()&& commonReportBinds.stream().map(ReportTemplateBindDto::getReportTemplateCode)
                        .distinct().count() == NumberUtils.LONG_ONE){
                    //在判断通用送检机构下
                    reportTemplateBind = commonReportBinds.iterator().next();
                    log.info("条码 [{}] 下面的报告项目使用统一的报告单模板，固使用项目模版 [{}]", bindReportDto.getBarcode(),
                            reportTemplateBind.getReportTemplateCode());
                }
            } else {
                log.warn("条码 [{}] 下面的报告项目没有使用统一的报告单模板，固不使用项目模版", bindReportDto.getBarcode());
            }
        }

        //1.0.7更新 判断送检机构 优先找送检机构下专业小组的
        if (Objects.isNull(reportTemplateBind)) {

            final List<ReportTemplateBindDto> reportTemplateBinds = selectMultiByBizId(bindReportDto.getHspOrgId())
                    .stream().filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                    .collect(Collectors.toList());

            reportTemplateBind = reportTemplateBinds.stream().filter(k -> Objects.equals(k.getInstrumentGroupId(), bindReportDto.getInstrumentGroupId()))
                    .findFirst().orElse(null);

            if (Objects.isNull(reportTemplateBind)) {
                log.warn("送检机构 [{}] ,专业小组 [{}] 下没有绑定报告单模板", bindReportDto.getHspOrgName(), bindReportDto.getInstrumentGroupName());

                if (!reportTemplateBinds.isEmpty()){
                    //这里优先找 送机机构+专业组+专业小组通用的情况
                    reportTemplateBind = reportTemplateBinds.stream().filter(k -> Objects.equals(k.getGroupId(), bindReportDto.getGroupId())
                            && Objects.equals(k.getInstrumentGroupId(),NumberUtils.LONG_ZERO)).findFirst().orElse(null);
                }
            }
        }

        if (Objects.isNull(reportTemplateBind)) {
            log.warn("送检机构 [{}] 没有绑定报告单模板，开始从专业小组 [{}] 查询模板", bindReportDto.getHspOrgName(), bindReportDto.getInstrumentGroupName());
            //根据专业组查 满足条件为：通用机构+专业组+专业小组 || 通用机构+专业组+通用
            final List<ReportTemplateBindDto> reportTemplateBinds = selectByGroupId(NumberUtils.LONG_ZERO,bindReportDto.getGroupId());
            reportTemplateBind = reportTemplateBinds.stream().filter(k -> Objects.equals(k.getInstrumentGroupId(), bindReportDto.getInstrumentGroupId()))
                    .findFirst().orElse(null);
            if (Objects.isNull(reportTemplateBind)) {
                log.warn("专业小组 [%s] 没有绑定模板", bindReportDto.getInstrumentGroupName());
                reportTemplateBind = reportTemplateBinds.stream().filter(k -> Objects.equals(k.getBizId(), NumberUtils.LONG_ZERO)
                                && Objects.equals(k.getBindType(),ReportTemplateBindTypeEnum.PROFESSIONAL_GROUP.getCode()))
                        .findFirst().orElse(null);
                if (Objects.isNull(reportTemplateBind)){
                    throw new IllegalStateException(String.format("专业小组 [%s] 没有绑定模板", bindReportDto.getInstrumentGroupName()));
                }
            }
        }

        return reportTemplateBind;
    }
}
