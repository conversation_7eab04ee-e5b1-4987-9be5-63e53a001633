package com.labway.lims.base.vo;

import com.labway.lims.api.enums.base.SystemParamMetricEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 系统参数
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class SystemParamVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参数ID
     * @see SystemParamMetricEnum
     */
    private Long paramId;

    /**
     * 参数分类
     */
    private String paramMetric;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 参数值
     */
    private String paramValue;

    /**
     * 参数说明
     */
    private String paramRemark;

    /**
     * mac地址
     */
    private String macAddr;

    /**
     * 电脑名称
     */
    private String cpuName;

    /**
     * 是否启动(0未启动 1启动)
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 属性1
     */
    private String extraParam1;

    /**
     * 属性2
     */
    private String extraParam2;

    /**
     * 属性3
     */
    private String extraParam3;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
