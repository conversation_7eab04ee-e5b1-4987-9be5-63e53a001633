package com.labway.lims.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/11 16:26
 */
@Getter
@Setter
public class FreeSampleQueryVo {

    /**
     * 录入开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date enterDateStart;

    /**
     * 录入结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date enterDateEnd;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * barcode
     */
    private String barcode;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * current
     */
    private Integer current;

    /**
     * size
     */
    private Integer size;

    /**
     * searchAfter
     */
    private Object searchAfter;

}
