package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.ReportItemCountByTestItemIdDto;
import com.labway.lims.base.model.TbReportItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 检验报告项目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbReportItemMapper extends BaseMapper<TbReportItem> {

    List<ReportItemCountByTestItemIdDto>
        selectReportItemCountByTestItemIds(@Param("testItemIds") Collection<Long> testItemIds);
}
