package com.labway.lims.base.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

/**
 * 仪器报告项目结果值转换sheet
 */
@Setter
@Getter
public class InstrumentReportItemResultExchangeSheet implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 仪器编码
     */
    @ExcelProperty(index = 0)
    private String instrumentCode;

    /**
     * 报告项目编码
     */
    @ExcelProperty(index = 1)
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    @ExcelProperty(index = 2)
    private String reportItemName;

    /**
     * 仪器结果值
     */
    @ExcelProperty(index = 3)
    private String instrumentResult;

    /**
     * 转换结果值
     */
    @ExcelProperty(index = 4)
    private String exchangeResult;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InstrumentReportItemResultExchangeSheet that = (InstrumentReportItemResultExchangeSheet) o;
        return Objects.equals(instrumentCode, that.instrumentCode) && Objects.equals(reportItemCode, that.reportItemCode) && Objects.equals(instrumentResult, that.instrumentResult);
    }

    @Override
    public int hashCode() {
        return Objects.hash(instrumentCode, reportItemCode, instrumentResult);
    }
}
