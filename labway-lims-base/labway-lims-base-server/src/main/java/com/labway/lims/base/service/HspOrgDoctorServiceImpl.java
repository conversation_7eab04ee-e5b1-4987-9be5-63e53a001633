package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.HspOrgDoctorDto;
import com.labway.lims.base.api.service.HspOrgDoctorService;
import com.labway.lims.base.mapper.TbHspOrgDoctorMapper;
import com.labway.lims.base.mapstruct.HspOrgDoctorConverter;
import com.labway.lims.base.model.TbHspOrgDoctor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/7/27 11:20
 */
@Slf4j
@DubboService
public class HspOrgDoctorServiceImpl implements HspOrgDoctorService {
    @Resource
    private TbHspOrgDoctorMapper tbHspOrgDoctorMapper;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private HspOrgDoctorConverter hspOrgDoctorConverter;

    @Override
    public List<HspOrgDoctorDto> selectByHspOrgDeptId(long hspOrgDeptId) {

        final LambdaQueryWrapper<TbHspOrgDoctor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbHspOrgDoctor::getHspOrgDeptId, hspOrgDeptId).orderByAsc(TbHspOrgDoctor::getHspOrgDoctorId);

        return hspOrgDoctorConverter.hspOrgDoctorDtoListFromTbObj(tbHspOrgDoctorMapper.selectList(wrapper));
    }

    @Override
    public List<HspOrgDoctorDto> selectByHspOrgId(long hspOrgId) {
        final LambdaQueryWrapper<TbHspOrgDoctor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbHspOrgDoctor::getHspOrgId, hspOrgId).orderByAsc(TbHspOrgDoctor::getHspOrgDoctorId);

        return hspOrgDoctorConverter.hspOrgDoctorDtoListFromTbObj(tbHspOrgDoctorMapper.selectList(wrapper));
    }

    @Override
    public long add(HspOrgDoctorDto dto) {

        final LoginUserHandler.User user = LoginUserHandler.get();
        final TbHspOrgDoctor doctor = JSON.parseObject(JSON.toJSONString(dto), TbHspOrgDoctor.class);
        doctor.setHspOrgDoctorId(snowflakeService.genId());
        doctor.setUpdateDate(new Date());
        doctor.setUpdaterName(user.getNickname());
        doctor.setUpdaterId(user.getUserId());
        doctor.setCreateDate(new Date());
        doctor.setCreatorName(user.getNickname());
        doctor.setCreatorId(user.getUserId());
        doctor.setOrgId(user.getOrgId());
        doctor.setOrgName(user.getOrgName());
        doctor.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbHspOrgDoctorMapper.insert(doctor) < 1) {
            throw new IllegalStateException("添加失败");
        }
        log.info("用户 [{}] 添加送检机构科室医生 [{}] 成功", user.getNickname(), JSON.toJSONString(doctor));

        return doctor.getHspOrgDoctorId();
    }

    @Override
    public void deleteByHspOrgDoctorId(Collection<Long> hspOrgDoctorIds) {
        if (CollectionUtils.isEmpty(hspOrgDoctorIds)) {
            return;
        }
        tbHspOrgDoctorMapper.deleteBatchIds(hspOrgDoctorIds);
    }

    @Override
    public void addBatch(Collection<HspOrgDoctorDto> dtos) {

        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }

        final LinkedList<Long> ids = snowflakeService.genIds(dtos.size());
        final LoginUserHandler.User user = LoginUserHandler.get();
        dtos.forEach(e -> {
            e.setHspOrgDoctorId(ObjectUtils.defaultIfNull(e.getHspOrgDoctorId(), ids.pop()));
            e.setUpdateDate(new Date());
            e.setUpdaterName(user.getNickname());
            e.setUpdaterId(user.getUserId());
            e.setCreateDate(new Date());
            e.setCreatorName(user.getNickname());
            e.setCreatorId(user.getUserId());
            e.setOrgId(user.getOrgId());
            e.setOrgName(user.getOrgName());
            e.setIsDelete(YesOrNoEnum.NO.getCode());
        });
        if (tbHspOrgDoctorMapper.addBatch(dtos) < 1) {
            throw new IllegalStateException("添加失败");
        }
        log.info("用户 [{}] 添加送检机构科室医生 [{}] 成功", user.getNickname(), JSON.toJSONString(dtos));
    }

    @Override
    public void deleteByByHspOrgMainIds(Collection<Long> hspOrgMainIds) {
        if (CollectionUtils.isEmpty(hspOrgMainIds)) {
            return;
        }
        final LambdaQueryWrapper<TbHspOrgDoctor> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbHspOrgDoctor::getHspOrgMainId, hspOrgMainIds);
        tbHspOrgDoctorMapper.delete(wrapper);
    }

    @Override
    public void deleteByHspOrgDeptIds(Collection<Long> hspOrgDeptIds) {
        if (CollectionUtils.isEmpty(hspOrgDeptIds)) {
            return;
        }
        final LambdaQueryWrapper<TbHspOrgDoctor> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbHspOrgDoctor::getHspOrgDeptId, hspOrgDeptIds);
        tbHspOrgDoctorMapper.delete(wrapper);
    }

    @Override
    public List<HspOrgDoctorDto> selectByHspOrgDoctorIds(Collection<Long> hspOrgDoctorIds) {
        if (CollectionUtils.isEmpty(hspOrgDoctorIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbHspOrgDoctor> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbHspOrgDoctor::getHspOrgDoctorId, hspOrgDoctorIds);
        queryWrapper.eq(TbHspOrgDoctor::getIsDelete, YesOrNoEnum.NO.getCode());
        return hspOrgDoctorConverter.hspOrgDoctorDtoListFromTbObj(tbHspOrgDoctorMapper.selectList(queryWrapper));
    }

}
