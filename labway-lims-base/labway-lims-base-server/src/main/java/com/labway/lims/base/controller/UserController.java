package com.labway.lims.base.controller;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.mdm.api.user.param.UserVo;
import com.labway.business.center.mdm.api.user.request.UserListRequest;
import com.labway.business.center.mdm.api.user.service.UserServerBaseService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.MenuTypeCodeEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.*;
import com.labway.lims.base.vo.*;
import com.swak.frame.dto.Response;
import com.swak.frame.enums.BasicErrCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static com.labway.lims.api.trace.TraceLogModuleEnum.LOGIN;

/**
 * 用户相关
 */
@Slf4j
@RequestMapping("/user")
@RestController
public class UserController extends BaseController {

    /**
     * 输入多少次锁定
     */
    private static final int PWD_ERROR_COUNT = 5;
    /**
     * 单位分钟
     */
    private static final int PWD_ERROR_TIME = 10;

    private static final int PWD_MIN_LEN = 8;
    private static final int ERROR_DISABLE_ROLE = 1002;

    /**
     * 需包含数字+英文大写+英文小写+符号 。from chatgpt
     */
    private static final String PWD_RULE = "^(?=.*\\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[^\\w\\d\\s:])([^\\s]){8,}$";

    /**
     * 默认密码前缀
     */
    public static final String DEFAULT_PWD_PREFIX = "123456@";

    @Resource
    private UserService userService;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @DubboReference
    private RoleService roleService;
    @DubboReference
    private GroupService groupService;
    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private MenuService menuService;
    @Resource
    private RandomStringService randomStringService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private UserServerBaseService userServerBaseService;

    /**
     * 登录
     */
    @PostMapping("/login")
    public Object login(@RequestBody UserLoginVo vo) {
        if (StringUtils.isAnyBlank(vo.getUsername(), vo.getPassword()) || Objects.isNull(vo.getGroupId())) {
            throw new IllegalArgumentException("参数错误");
        }

        final String lockKey = redisPrefix.getBasePrefix() + "USER_LOGIN_LOCK:" + vo.getUsername();
        if (NumberUtils.toInt(stringRedisTemplate.opsForValue().get(lockKey)) >= PWD_ERROR_COUNT) {
            throw new IllegalStateException(String.format("%s 次输入错误，系统锁定，请 %s 分钟后再试", PWD_ERROR_COUNT, PWD_ERROR_TIME));
        }

        final UserDto user = userService.selectByUsername(vo.getUsername());
        if (Objects.isNull(user)) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        if (!StringUtils.equalsIgnoreCase(DigestUtils.md5Hex(vo.getUsername() + vo.getPassword()),
                user.getPassword())) {
            stringRedisTemplate.opsForValue().increment(lockKey);
            stringRedisTemplate.expire(lockKey, Duration.ofMinutes(PWD_ERROR_TIME));

            throw new IllegalArgumentException("工号或密码错误");
        }

        stringRedisTemplate.delete(lockKey);

        if (!Objects.equals(user.getStatus(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalArgumentException("当前用户未启用");
        }

        // 查询到角色
        final long roleId;
        if (Objects.nonNull(vo.getRoleId())) {
            roleId = vo.getRoleId();
        } else {
            final UserRoleDto userRole = roleService.selectUserRoleByUserId(user.getUserId()).stream()
                    .filter(e -> Objects.equals(e.getIsDefault(), YesOrNoEnum.YES.getCode())).findFirst().orElse(null);
            if (Objects.isNull(userRole)) {
                throw new IllegalArgumentException("当前用户没有默认角色");
            }
            roleId = userRole.getRoleId();
        }

        // 判断角色是否启用
        final RoleDto role = roleService.selectByRoleId(roleId);
        if (Objects.isNull(role)) {
            throw new IllegalArgumentException("角色不存在");
        }

        // 没有启用此角色，那么需要弹窗选角色
        if (!Objects.equals(role.getStatus(), YesOrNoEnum.YES.getCode())) {
            final List<RoleDto> roles = roleService.selectByUserId(user.getUserId());
            roles.removeIf(e -> Objects.equals(roleId, e.getRoleId()));
            if (roles.isEmpty()) {
                throw new IllegalStateException("当前用户没有默认角色");
            }
            throw new LimsCodeException(ERROR_DISABLE_ROLE, "请选择角色")
                    .setData(roles.stream().map(e -> Map.of("roleId", e.getRoleId(), "roleName", e.getRoleName())));
        }

        final LoginUserHandler.User loginUser = new LoginUserHandler.User();

        // 查询到用户专业组
        final List<ProfessionalGroupDto> groups = new ArrayList<>(groupService.selectByUserId(user.getUserId()));
        groups.removeIf(e -> !Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()));
        if (CollectionUtils.isNotEmpty(groups)
                && groups.stream().noneMatch(e -> Objects.equals(e.getGroupId(), vo.getGroupId()))) {
            throw new IllegalArgumentException("用户专业组错误");
        } else {
            final ProfessionalGroupDto group = groupService.selectByGroupId(vo.getGroupId());
            if (Objects.isNull(group)) {
                throw new IllegalArgumentException("专业组不存在");
            } else {
                loginUser.setGroupCode(group.getGroupCode());
                loginUser.setGroupName(group.getGroupName());
            }
        }

        loginUser.setUserId(user.getUserId());
        loginUser.setUsername(user.getUsername());
        loginUser.setNickname(user.getNickname());
        loginUser.setToken(IdUtil.simpleUUID());
        loginUser.setGroupId(vo.getGroupId());
        loginUser.setGroupCode(StringUtils.defaultString(loginUser.getGroupCode()));
        loginUser.setGroupName(StringUtils.defaultString(loginUser.getGroupName()));
        loginUser.setRoleId(role.getRoleId());
        loginUser.setRoleName(role.getRoleName());
        loginUser.setOrgId(user.getOrgId());
        loginUser.setOrgName(user.getOrgName());
        loginUser.setOrgCode(user.getOrgCode());

        stringRedisTemplate.opsForValue().set(redisPrefix.getTokenKey(loginUser.getToken()),
                JSON.toJSONString(loginUser), Duration.ofDays(7));

        log.info("用户 [{}] [{}] 登录成功 专业组 [{}] 角色 [{}] Token [{}]", loginUser.getUsername(), loginUser.getNickname(),
                loginUser.getGroupName(), loginUser.getRoleName(), loginUser.getToken());

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance()
                        .setModule(LOGIN.getDesc())
                        .setContent(String.format("用户 【%s】登录系统", loginUser.getNickname()))
                        .setNickname(loginUser.getNickname())
                        .toJSONString());

        return loginUser;
    }

    /**
     * 登录时用到的专业组
     */
    @GetMapping("/login-groups")
    public Object loginGroup(@RequestParam String username) {
        if (StringUtils.isBlank(username)) {
            return Collections.emptyList();
        }

        final UserDto user = userService.selectByUsername(username);
        if (Objects.isNull(user)) {
            return Collections.emptyList();
        }

        final List<ProfessionalGroupDto> groups = new LinkedList<>();
        final List<UserProfessionalGroupDto> userGroups =
                groupService.selectUserGroupByUserIds(List.of(user.getUserId()));
        if (CollectionUtils.isNotEmpty(userGroups)) {
            groups.addAll(userGroups);
        } else {
            groups.addAll(groupService.selectByAll(user.getOrgId()));
            groups.removeIf(e -> Objects.equals(e.getEnable(), YesOrNoEnum.NO.getCode()));
        }

        groups.removeIf(e -> !Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()));

        return groups.stream()
                .map(e -> Map.of("isDefault",
                        userGroups.stream().filter(k -> Objects.equals(e.getGroupId(), k.getGroupId())).findFirst()
                                .map(UserProfessionalGroupDto::getIsDefault).orElse(YesOrNoEnum.NO.getCode()),
                        "groupId", e.getGroupId(), "groupName", e.getGroupName()))
                .collect(Collectors.toList());
    }

    /**
     * 退出，销毁token
     */
    @GetMapping("/logout")
    public Object logout() {

        final LoginUserHandler.User unsafe = LoginUserHandler.getUnsafe();
        if (Objects.isNull(unsafe)) {
            return Collections.emptyMap();
        }

        stringRedisTemplate.delete(redisPrefix.getTokenKey(unsafe.getToken()));

        return Collections.emptyMap();
    }

    /**
     * 查询用户列表
     */
    @GetMapping("/users")
    public List<UserUsersVo> users() {

        final List<UserDto> users = userService.selectByOrgId(LoginUserHandler.get().getOrgId());
        if (CollectionUtils.isEmpty(users)) {
            return Collections.emptyList();
        }

        final Set<Long> userIds = users.stream().map(UserDto::getUserId).collect(Collectors.toSet());

        final var roles = roleService.selectUserRoleByUserIds(userIds).stream()
                .collect(Collectors.groupingBy(UserRoleDto::getUserId));

        final var groups = groupService.selectUserGroupByUserIds(userIds).stream()
                .collect(Collectors.groupingBy(UserProfessionalGroupDto::getUserId));

        return users.stream().map(e -> {
            final UserUsersVo vo = new UserUsersVo();
            BeanUtils.copyProperties(e, vo);
            final UserProfessionalGroupDto upg = groups.getOrDefault(e.getUserId(), Collections.emptyList()).stream()
                    .filter(l -> Objects.equals(l.getIsDefault(), YesOrNoEnum.YES.getCode())).findFirst().orElse(null);
            if (Objects.nonNull(upg)) {
                vo.setGroupName(upg.getGroupName());
                vo.setGroupId(upg.getGroupId());
            }

            final UserRoleDto ur = roles.getOrDefault(e.getUserId(), Collections.emptyList()).stream()
                    .filter(l -> Objects.equals(l.getIsDefault(), YesOrNoEnum.YES.getCode())).findFirst().orElse(null);
            if (Objects.nonNull(ur)) {
                vo.setRoleName(ur.getRoleName());
                vo.setRoleId(ur.getRoleId());
            }

            vo.setRoleIds(roles.getOrDefault(e.getUserId(), Collections.emptyList()).stream().map(RoleDto::getRoleId)
                    .collect(Collectors.toList()));
            vo.setGroupIds(groups.getOrDefault(e.getUserId(), Collections.emptyList()).stream()
                    .map(ProfessionalGroupDto::getGroupId).collect(Collectors.toList()));

            return vo;
        }).collect(Collectors.toList());

    }

    /**
     * 主数据的用户列表
     */
    @GetMapping("/main-users")
    public List<MainUserVo> mainUsers() {
        LoginUserHandler.User user = LoginUserHandler.get();
        // 当前机构下所有 用户信息
        List<UserDto> userDtos = userService.selectByOrgId(user.getOrgId());

        // 过滤的用户名称
        List<String> filterUsernames =
                userDtos.stream().map(UserDto::getNickname).distinct().collect(Collectors.toList());

        UserListRequest userListRequest = new UserListRequest();
        userListRequest.setExcludeUserNames(filterUsernames);
        // 请求主数据 所有用户
        Response<List<UserVo>> response = userServerBaseService.getUserList(userListRequest);
        if (Objects.isNull(response)) {
            throw new LimsException("主数据接口-查询用户信息返回结果为空");
        }
        if (!Objects.equals(response.getCode(), BasicErrCode.SUCCESS.getCode())) {
            throw new LimsException(String.format("主数据接口-查询用户信息返回错误, 错误信息: [%s]", response.getMsg()));
        }
        List<UserVo> mdmUserInfo = ObjectUtils.defaultIfNull(response.getData(), Collections.emptyList());

        return mdmUserInfo.stream().map(obj -> {
            Integer sex = SexEnum.DEFAULT.getCode();
            if (Objects.equals(obj.getGender(), NumberUtils.INTEGER_ONE)) {
                sex = SexEnum.MAN.getCode();
            } else if (Objects.equals(obj.getGender(), NumberUtils.INTEGER_TWO)) {
                sex = SexEnum.WOMEN.getCode();
            }
            final MainUserVo v = new MainUserVo();
            v.setUserId(obj.getUserId());
            v.setNickname(obj.getName());
            v.setUsername(obj.getName());
            v.setSex(sex);
            v.setOrg(obj.getDepartmentName());
            return v;
        }).collect(Collectors.toList());

    }

    /**
     * 新增用户
     */
    @PostMapping("/add")
    public Object add(@RequestBody AddUserVo vo) {
        if (StringUtils.isAnyBlank(vo.getUsername(), vo.getNickname()) || CollectionUtils.isEmpty(vo.getRoleIds())
                || Objects.isNull(vo.getStatus()) || Objects.isNull(vo.getRoleId()) || Objects.isNull(vo.getSex())) {
            throw new IllegalArgumentException("参数错误");
        }

        if (StringUtils.length(vo.getNickname()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("姓名长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        if (StringUtils.length(vo.getUsername()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("登录名长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        if (!vo.getRoleIds().contains(vo.getRoleId())) {
            throw new IllegalArgumentException("参数错误");
        }

        if (CollectionUtils.isNotEmpty(vo.getGroupIds()) && Objects.nonNull(vo.getGroupId())
                && (!vo.getGroupIds().contains(vo.getGroupId()))) {
            throw new IllegalArgumentException("参数错误");
        }

        return Map.of("id", userService.addUser(JSON.parseObject(JSON.toJSONString(vo), AddUserDto.class)));
    }

    /**
     * 重置密码
     */
    @GetMapping("/reset-pwd")
    public Object resetPwd(@RequestParam Long userId) {
        if (Objects.isNull(userId)) {

            throw new IllegalArgumentException("参数错误");
        }

        final UserDto oldUser = userService.selectByUserId(userId);
        if (Objects.isNull(oldUser)) {
            throw new IllegalArgumentException("用户不存在");
        }

        final UserDto user = new UserDto();
        user.setUserId(userId);

        final String firstLetter = CharSequenceUtil
                .upperFirst(StringUtils.lowerCase(PinyinUtil.getFirstLetter(oldUser.getNickname(), StringUtils.EMPTY)));
        if (StringUtils.isBlank(firstLetter)) {
            throw new IllegalStateException("获取用户首字母拼音错误");
        }
        final String password = DEFAULT_PWD_PREFIX + firstLetter;
        user.setPassword(DigestUtils.md5Hex(oldUser.getUsername() + password));

        if (userService.updateUserByUserId(user)) {
            log.info("用户 [{}] 重置了用户 [{}] 的密码为 [{}]", LoginUserHandler.get().getNickname(), oldUser.getNickname(),
                    password);
            return Collections.emptyMap();
        }

        throw new IllegalStateException("重置密码失败");
    }

    /**
     * 更新密码
     */
    @PostMapping("/update-pwd")
    public Object updatePwd(@RequestBody UpdatePwdVo vo) {

        final String newPwd = vo.getNewPwd();
        final String oldPwd = vo.getOldPwd();

        if (StringUtils.isAnyBlank(oldPwd, newPwd)) {
            throw new IllegalArgumentException("参数错误");
        }

        if (StringUtils.equals(oldPwd, newPwd)) {
            throw new IllegalArgumentException("原密码与新密码不能一致");
        }

        if (newPwd.length() < PWD_MIN_LEN) {
            throw new IllegalArgumentException(String.format("新密码长度不能小于 %s 位", PWD_MIN_LEN));
        }

        final Long userId = LoginUserHandler.get().getUserId();

        final UserDto oldUser = userService.selectByUserId(userId);
        if (Objects.isNull(oldUser)) {
            throw new IllegalArgumentException("用户不存在");
        }

        if (!StringUtils.equalsIgnoreCase(DigestUtils.md5Hex(oldUser.getUsername() + oldPwd), oldUser.getPassword())) {
            throw new IllegalStateException("原密码输入错误");
        }

        if (!newPwd.matches(PWD_RULE)) {
            throw new IllegalStateException("密码需包含 数字 + 英文大写 + 英文小写 + 符号");
        }

        final UserDto user = new UserDto();
        user.setUserId(userId);

        user.setPassword(DigestUtils.md5Hex(oldUser.getUsername() + newPwd));

        if (userService.updateUserByUserId(user)) {
            log.info("用户 [{}] 修改了用户 [{}] 的密码为 [{}]", LoginUserHandler.get().getNickname(), oldUser.getNickname(),
                    newPwd);
            return Collections.emptyMap();
        }

        throw new IllegalStateException("修改密码失败");
    }

    /**
     * 切换角色
     */
    @GetMapping("/change-role")
    public Object changRole(@RequestParam Long roleId) {
        if (Objects.isNull(roleId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<RoleDto> roles = roleService.selectByUserId(LoginUserHandler.get().getUserId());
        if (CollectionUtils.isEmpty(roles)) {
            throw new IllegalArgumentException("用户不拥有此角色");
        }

        if (roles.stream().noneMatch(e -> Objects.equals(e.getRoleId(), roleId))) {
            throw new IllegalArgumentException("用户不拥有此角色");
        }

        final RoleDto role = roles.stream().filter(e -> Objects.equals(e.getRoleId(), roleId)).findFirst().orElse(null);
        if (Objects.isNull(role)) {
            throw new IllegalStateException("切换的角色不存在");
        }

        final String key = redisPrefix.getTokenKey(LoginUserHandler.get().getToken());

        final String userText = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(userText)) {
            throw new IllegalArgumentException("用户信息错误");
        }

        final LoginUserHandler.User user = JSON.parseObject(userText, LoginUserHandler.User.class);
        user.setRoleId(roleId);
        user.setRoleName(role.getRoleName());

        stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(user), Duration.ofDays(7));

        log.info("用户 [{}] 切换角色 [{}] 成功", user.getNickname(), roleId);

        return Collections.emptyMap();

    }

    /**
     * 切换专业组
     */
    @GetMapping("/change-group")
    public Object changGroup(@RequestParam Long groupId) {
        if (Objects.isNull(groupId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<ProfessionalGroupDto> groups = groupService.selectByUserId(LoginUserHandler.get().getUserId());
        final ProfessionalGroupDto group;

        // 不为空是校验 为空则拥有所有专业组
        if (CollectionUtils.isNotEmpty(groups)) {
            if (groups.stream().noneMatch(e -> Objects.equals(e.getGroupId(), groupId))) {
                throw new IllegalArgumentException("用户不拥有此专业组");
            }

            group = groups.stream().filter(e -> Objects.equals(e.getGroupId(), groupId)).findFirst().orElse(null);

        } else {
            group = groupService.selectByGroupId(groupId);
        }

        if (Objects.isNull(group)) {
            throw new IllegalStateException("切换的专业组不存在");
        }

        final String key = redisPrefix.getTokenKey(LoginUserHandler.get().getToken());

        final String userText = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(userText)) {
            throw new IllegalArgumentException("用户信息错误");
        }

        final LoginUserHandler.User user = JSON.parseObject(userText, LoginUserHandler.User.class);
        user.setGroupId(groupId);
        user.setGroupCode(group.getGroupCode());
        user.setGroupName(group.getGroupName());

        stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(user), Duration.ofDays(7));

        log.info("用户 [{}] 切换专业组 [{}] 成功", user.getNickname(), groupId);

        return Collections.emptyMap();

    }

    /**
     * 修改用户
     */
    @PostMapping("/update")
    public Object update(@RequestBody UpdateUserVo vo) {
        if (StringUtils.isBlank(vo.getNickname()) || CollectionUtils.isEmpty(vo.getRoleIds())
                || Objects.isNull(vo.getStatus()) || Objects.isNull(vo.getRoleId()) || Objects.isNull(vo.getUserId())) {
            throw new IllegalArgumentException("参数错误");
        }

        if (StringUtils.length(vo.getNickname()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("姓名长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        if (!vo.getRoleIds().contains(vo.getRoleId())) {
            throw new IllegalArgumentException("参数错误");
        }

        if (CollectionUtils.isNotEmpty(vo.getGroupIds()) && Objects.nonNull(vo.getGroupId())
                && (!vo.getGroupIds().contains(vo.getGroupId()))) {
            throw new IllegalArgumentException("参数错误");

        }

        userService.updateByUserId(JSON.parseObject(JSON.toJSONString(vo), UpdateUserDto.class));

        return Collections.emptyMap();
    }

    /**
     * 当前登录用户的权限
     */
    @GetMapping("/permission")
    public Object permission() {

        final Long roleId = LoginUserHandler.get().getRoleId();

        final List<MenuDto> buttons = menuService.selectByRoleId(roleId).stream()
                .filter(e -> Objects.equals(e.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name())
                        && Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());
        final List<MenuDto> menus = menuService.selectTreeMenuByRoleId(roleId).stream()
                .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode())).collect(Collectors.toList());
        return Map.of("menus", menus, "buttons", buttons);
    }

    /**
     * 当前登录用户的角色
     */
    @GetMapping("/roles")
    public Object roles() {

        return roleService.selectByUserId(LoginUserHandler.get().getUserId());
    }

    /**
     * 当前登录用户的专业组
     */
    @GetMapping("/groups")
    public Object groups() {

        List<ProfessionalGroupDto> groups = groupService.selectByUserId(LoginUserHandler.get().getUserId());
        if (CollectionUtils.isEmpty(groups)) {
            groups = new LinkedList<>(groupService.selectByOrgId(LoginUserHandler.get().getOrgId()));
            groups.removeIf(e -> !Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()));
        }

        if (CollectionUtils.isEmpty(groups)) {
            return Collections.emptyList();
        }

        return new HashSet<>(JSON.parseArray(JSON.toJSONString(groups), ProfessionalGroupVo.class));

    }

    /**
     * 根据ID查询用户
     */
    @GetMapping("/getUser")
    public Object getUser(@RequestParam("userId") Long userId) {
        final UserDto userDto = userService.selectByUserId(userId);

        if (Objects.isNull(userDto)) {
            return Map.of();

        }
        return JSON.parseObject(JSON.toJSONString(userDto), UserInfoVo.class);
    }


    /**
     * 查询用户列表-下拉选项
     */
    @GetMapping("/select-users")
    public List<UserUsersVo> selectUsers() {
        List<UserDto> users = userService.selectByOrgId(LoginUserHandler.get().getOrgId());
        if (CollectionUtils.isEmpty(users)) {
            return Collections.emptyList();
        }

        // 只返回有效的用户数据
        users =
                users.stream()
                        .filter(userDto ->
                                Objects.equals(YesOrNoEnum.YES.getCode(), userDto.getStatus())
                                        && Objects.equals(YesOrNoEnum.NO.getCode(), userDto.getIsDelete()))
                        .collect(Collectors.toList());

        return users.stream().map(e -> {
            final UserUsersVo vo = new UserUsersVo();
            BeanUtils.copyProperties(e, vo);
            return vo;
        }).collect(Collectors.toList());
    }

}
