package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.DiscountDetailDto;
import com.labway.lims.base.api.dto.HspOrgDiscountDto;
import com.labway.lims.base.model.TbHspOrgDiscount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 客户折扣信息Mapper
 */
@Mapper
public interface TbHspOrgDiscountMapper extends BaseMapper<TbHspOrgDiscount> {
    void addBatch(@Param("list") List<HspOrgDiscountDto> list);

    List<DiscountDetailDto> selectDiscountDetailByHspOrgIdsAndDateRange(@Param("hspOrgIds") Collection<Long> hspOrgIds, @Param("applyTypes") Collection<String> applyTypes, @Param("minDate") Date minDate, @Param("maxDate") Date maxDate);
}
