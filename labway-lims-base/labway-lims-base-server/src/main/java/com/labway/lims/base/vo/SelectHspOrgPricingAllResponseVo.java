package com.labway.lims.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 阶梯折扣 查看 vo
 * 
 * <AUTHOR>
 * @since 2023/5/4 15:55
 */
@Getter
@Setter
public class SelectHspOrgPricingAllResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 阶梯折扣ID
     */
    private Long tieredPriceId;
    /**
     * 送检机构ID
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date endDate;
    /**
     * 折扣率
     */
    private String discount;

    /**
     * 折前总额上限
     */
    private String beforeMaxPrice;

    /**
     * 折前总额下限
     */
    private String beforeMinPrice;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

}
