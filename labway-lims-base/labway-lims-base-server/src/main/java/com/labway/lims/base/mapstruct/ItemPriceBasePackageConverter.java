
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.ItemPriceBasePackageDto;
import com.labway.lims.base.model.TbItemPriceBasePackage;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 项目价格基准包服务层 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 19:21
 */
@Mapper(componentModel = "spring")
public interface ItemPriceBasePackageConverter {
    ItemPriceBasePackageDto itemPriceBasePackageDtoFromTbObj(TbItemPriceBasePackage obj);

    List<ItemPriceBasePackageDto> itemPriceBasePackageDtoListFromTbObjList(List<TbItemPriceBasePackage> list);

}
