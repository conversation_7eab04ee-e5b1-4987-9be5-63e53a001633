package com.labway.lims.base.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.ItemPriceCombinePackageDetailService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.mapper.TbItemPriceCombinePackageDetailMapper;
import com.labway.lims.base.model.TbItemPriceCombinePackageDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (TbItemPriceCombinePackageDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-04 19:56:44
 */
@Service("itemPriceCombinePackageDetailService")
public class ItemPriceCombinePackageDetailServiceImpl extends ServiceImpl<TbItemPriceCombinePackageDetailMapper, TbItemPriceCombinePackageDetail> implements ItemPriceCombinePackageDetailService {

    @Resource
    private TbItemPriceCombinePackageDetailMapper tbItemPriceCombinePackageDetailMapper;
    @Resource
    private TestItemService testItemService;
    @Resource
    private ReportItemService reportItemService;
    @DubboReference
    private SnowflakeService snowflakeService;


    /**
     * 查询套餐的详情信息
     */
    @Override
    public Map<String, List<TestItemDto>> queryCombinePackageDetailInfo(Collection<String> combinePackageCodes) {

        LoginUserHandler.User user = LoginUserHandler.get();

        List<TbItemPriceCombinePackageDetail> tbItemPriceCombinePackageDetails = tbItemPriceCombinePackageDetailMapper.selectList(Wrappers.lambdaQuery(TbItemPriceCombinePackageDetail.class)
                .in(TbItemPriceCombinePackageDetail::getCombinePackageCode, combinePackageCodes));

        if (CollectionUtils.isEmpty(tbItemPriceCombinePackageDetails)) {
            return Collections.emptyMap();
        }

        Set<Long> itemIds = tbItemPriceCombinePackageDetails.stream().map(TbItemPriceCombinePackageDetail::getTestItemId).collect(Collectors.toSet());

        //根据检验项目id获取检验项目信息
        List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(itemIds);

        // 查询检验项目的报告项目信息
        Map<Long, List<ReportItemDto>> reportItemMap = reportItemService.selectByTestItemIdsAsMap(itemIds, user.getOrgId());

        // 填充报告项目项
        testItemDtos.forEach(testItemDto -> {
            List<ReportItemDto> reportItemDtos = reportItemMap.getOrDefault(testItemDto.getTestItemId(), Collections.emptyList());
            testItemDto.setReportItems(reportItemDtos);
        });

        Map<String, List<TbItemPriceCombinePackageDetail>> combinePackageDetailGroup = tbItemPriceCombinePackageDetails.stream().collect(Collectors.groupingBy(TbItemPriceCombinePackageDetail::getCombinePackageCode));

        return combinePackageDetailGroup.entrySet().stream()
                .map(e -> {
                    String tempCombinePackageCode = e.getKey();
                    List<TbItemPriceCombinePackageDetail> tempDetailValue = e.getValue();
                    Set<Long> tempTestItemIds = tempDetailValue.stream().map(TbItemPriceCombinePackageDetail::getTestItemId).collect(Collectors.toSet());
                    List<TestItemDto> collect = testItemDtos.stream().filter(testItemDto -> tempTestItemIds.contains(testItemDto.getTestItemId())).collect(Collectors.toList());
                    return Map.entry(tempCombinePackageCode, collect);
                }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Override
    public boolean addCombinePackageDetailInfo(String combinePackageCode, Set<Long> testItemIds) {
        if(CollectionUtils.isEmpty(testItemIds)){
            return true;
        }

        final Map<Long, TestItemDto> testItemDtoMap = testItemService.selectByTestItemIds(testItemIds)
                .stream().collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity(), (a, b) -> b));


        final Set<Long> invalidTestItemIds = testItemIds.stream().filter(testItemId -> Objects.isNull(testItemDtoMap.get(testItemId))).collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(invalidTestItemIds)) {
            throw new IllegalArgumentException("检验项目id无效");
        }

        final LoginUserHandler.User user = LoginUserHandler.get();
        Date now = new Date();

        final List<TbItemPriceCombinePackageDetail> tbItemPriceCombinePackageDetailList = testItemIds.stream()
                .map(e -> new TbItemPriceCombinePackageDetail()
                        .setDetailId(snowflakeService.genId())
                        .setCombinePackageCode(combinePackageCode)
                        .setTestItemId(e)
                        .setCreateDate(now)
                        .setUpdateDate(now)
                        .setCreateId(user.getUserId())
                        .setCreateName(user.getNickname())
                        .setUpdateId(user.getUserId())
                        .setUpdateName(user.getNickname())
                        .setIsDelete(YesOrNoEnum.NO.getCode()))
                .collect(Collectors.toList());

        return super.saveBatch(tbItemPriceCombinePackageDetailList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteCombinePackageDetailInfo(String combinePackageCode, Set<Long> testItemIds) {
        if (CollectionUtils.isEmpty(testItemIds)) {
            return true;
        }
        final LambdaUpdateWrapper<TbItemPriceCombinePackageDetail> wrapper = new LambdaUpdateWrapper<TbItemPriceCombinePackageDetail>()
                .eq(TbItemPriceCombinePackageDetail::getCombinePackageCode, combinePackageCode)
                .in(TbItemPriceCombinePackageDetail::getTestItemId, testItemIds);
        return super.remove(wrapper);
    }

    @Override
    public boolean deleteByCombinePackageCode(String combinePackageCode) {
        final LambdaUpdateWrapper<TbItemPriceCombinePackageDetail> wrapper = new LambdaUpdateWrapper<TbItemPriceCombinePackageDetail>()
                .eq(TbItemPriceCombinePackageDetail::getCombinePackageCode, combinePackageCode);
        return super.remove(wrapper);
    }

    @Override
    public boolean deleteByCombinePackageCodes(Collection<String> combinePackageCodeList) {

        final LambdaQueryWrapper<TbItemPriceCombinePackageDetail> combinePackageDetailWrapper = new LambdaQueryWrapper<TbItemPriceCombinePackageDetail>()
                .in(TbItemPriceCombinePackageDetail::getCombinePackageCode, combinePackageCodeList);
        return super.remove(combinePackageDetailWrapper);
    }
}

