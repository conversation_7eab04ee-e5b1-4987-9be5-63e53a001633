package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.SystemParamReportDelayService;
import com.labway.lims.base.api.service.SystemParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <pre>
 * SystemParamReportDelayService
 * 系统参数 延迟报告配置
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/21 14:44
 */
@Slf4j
@DubboService
public class SystemParamReportDelayServiceImpl implements SystemParamReportDelayService {

    @Resource
    private SystemParamService systemParamService;

    @Override
    @Nonnull
    public Long getReportDelayTime(String hspOrgCode) {
        try {
            JSONObject reportDelayConfig = systemParamService.selectAsJsonByParamName(
                    SystemParamNameEnum.HSP_ORG_REPORT_DELAY_CONFIG.getCode(), LoginUserHandler.get().getOrgId());
            long delayMillis = reportDelayConfig.getLongValue(hspOrgCode);
            return TimeUnit.MINUTES.toMillis(delayMillis);
        } catch (Exception e) {
            return 0L;
        }
    }

}
