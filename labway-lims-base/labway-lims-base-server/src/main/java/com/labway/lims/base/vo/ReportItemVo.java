package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 检验报告项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class ReportItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;
    /**
     * 1:已经删除 0未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
}
