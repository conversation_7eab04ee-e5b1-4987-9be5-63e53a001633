package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentGroupInstrumentDto;
import com.labway.lims.base.api.service.InstrumentGroupInstrumentService;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentGroupTestItemService;
import com.labway.lims.base.mapper.TbInstrumentGroupInstrumentMapper;
import com.labway.lims.base.model.TbInstrumentGroupInstrument;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/29 20:18
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "instrument-group-instrument")
public class InstrumentGroupInstrumentServiceImpl implements InstrumentGroupInstrumentService {
    @Resource
    private TbInstrumentGroupInstrumentMapper tbInstrumentGroupInstrumentMapper;
    @Resource
    private InstrumentGroupTestItemService instrumentGroupTestItemService;
    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private InstrumentGroupService instrumentGroupService;

    @Override
    @CacheEvict(allEntries = true)
    public Set<Long> addBatch(List<InstrumentGroupInstrumentDto> dtos) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final List<InstrumentGroupDto> groupDtos = instrumentGroupService.selectByInstrumentGroupIds(
            dtos.stream().map(InstrumentGroupInstrumentDto::getInstrumentGroupId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(groupDtos)) {
            throw new IllegalStateException("专业组不存在,添加失败");
        }

        Set<String> instrumentCodes = new LinkedHashSet<>();
        for (InstrumentGroupInstrumentDto dto : dtos) {
            instrumentCodes.addAll(selectByInstrumentGroupId(dto.getInstrumentGroupId()).stream()
                .map(InstrumentGroupInstrumentDto::getInstrumentCode).collect(Collectors.toSet()));
        }

        if (CollectionUtils.containsAny(instrumentCodes,
            dtos.stream().map(InstrumentGroupInstrumentDto::getInstrumentCode).collect(Collectors.toList()))) {
            throw new IllegalStateException("仪器重复,添加失败");
        }

        final List<TbInstrumentGroupInstrument> groupInstruments =
            JSON.parseArray(JSON.toJSONString(dtos), TbInstrumentGroupInstrument.class);
        final LinkedList<Long> ids = snowflakeService.genIds(dtos.size());
        for (TbInstrumentGroupInstrument item : groupInstruments) {
            item.setInstrumentGroupInstrumentId(ids.pop());
            item.setCreatorName(user.getNickname());
            item.setCreatorId(user.getUserId());
            item.setCreateDate(new Date());
            item.setUpdaterName(user.getNickname());
            item.setUpdaterId(user.getUserId());
            item.setUpdateDate(new Date());
            item.setOrgId(user.getOrgId());
            item.setIsDelete(YesOrNoEnum.NO.getCode());
        }

        tbInstrumentGroupInstrumentMapper.addBatch(groupInstruments);

        return Set.of();

    }

    @Override
    @Cacheable(key = "'selectByInstrumentGroupId:' + #instrumentGroupId")
    public List<InstrumentGroupInstrumentDto> selectByInstrumentGroupId(long instrumentGroupId) {
        final LambdaQueryWrapper<TbInstrumentGroupInstrument> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentGroupInstrument::getInstrumentGroupId, instrumentGroupId)
            .eq(TbInstrumentGroupInstrument::getOrgId, LoginUserHandler.get().getOrgId())
            .orderByDesc(TbInstrumentGroupInstrument::getInstrumentGroupInstrumentId);

        return tbInstrumentGroupInstrumentMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByInstrumentId:' + #instrumentId")
    public List<InstrumentGroupInstrumentDto> selectByInstrumentId(long instrumentId) {
        final LambdaQueryWrapper<TbInstrumentGroupInstrument> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentGroupInstrument::getInstrumentId, instrumentId)
            .eq(TbInstrumentGroupInstrument::getOrgId, LoginUserHandler.get().getOrgId())
            .orderByDesc(TbInstrumentGroupInstrument::getInstrumentGroupInstrumentId);

        return tbInstrumentGroupInstrumentMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteById(long instrumentGroupInstrumentId) {

        if (CollectionUtils.isNotEmpty(
            instrumentGroupTestItemService.selectByInstrumentGroupInstrumentId(instrumentGroupInstrumentId))) {
            throw new IllegalStateException("仪器下有检验项目，无法删除");
        }

        return tbInstrumentGroupInstrumentMapper.deleteById(instrumentGroupInstrumentId) > 0;
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByInstrumentGroupId(long instrumentGroupId) {
        final LambdaQueryWrapper<TbInstrumentGroupInstrument> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentGroupInstrument::getInstrumentGroupId, instrumentGroupId);
        return tbInstrumentGroupInstrumentMapper.delete(wrapper) > 0;
    }

    @Override
    public List<InstrumentGroupInstrumentDto> selectByInstrumentGroupIds(Collection<Long> instrumentGroupIds) {
        final LambdaQueryWrapper<TbInstrumentGroupInstrument> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbInstrumentGroupInstrument::getInstrumentGroupId, instrumentGroupIds)
            .eq(TbInstrumentGroupInstrument::getOrgId, LoginUserHandler.get().getOrgId());

        return tbInstrumentGroupInstrumentMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    public List<InstrumentGroupInstrumentDto> selectByIds(Collection<Long> instrumentGroupInstrumentIds) {
        if (CollectionUtils.isEmpty(instrumentGroupInstrumentIds)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbInstrumentGroupInstrument> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbInstrumentGroupInstrument::getInstrumentGroupInstrumentId, instrumentGroupInstrumentIds)
            .orderByDesc(TbInstrumentGroupInstrument::getInstrumentGroupInstrumentId);

        return tbInstrumentGroupInstrumentMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    private InstrumentGroupInstrumentDto convert(TbInstrumentGroupInstrument instrumentGroupInstrument) {
        if (Objects.isNull(instrumentGroupInstrument)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(instrumentGroupInstrument), InstrumentGroupInstrumentDto.class);
    }

}
