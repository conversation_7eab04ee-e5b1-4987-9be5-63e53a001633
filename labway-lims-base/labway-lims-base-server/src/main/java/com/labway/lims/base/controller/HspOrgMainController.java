package com.labway.lims.base.controller;

import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrgMainDto;
import com.labway.lims.base.api.service.HspOrgDeptService;
import com.labway.lims.base.api.service.HspOrgDoctorService;
import com.labway.lims.base.api.service.HspOrgMainService;
import com.labway.lims.base.vo.HspOrgMainAddVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/27 11:31
 */
@Slf4j
@RestController
@RequestMapping("/hsp-org-main")
public class HspOrgMainController extends BaseController {
    @Resource
    private HspOrgMainService hspOrgMainService;
    @Resource
    private HspOrgDeptService hspOrgDeptService;
    @Resource
    private HspOrgDoctorService hspOrgDoctorService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @PostMapping("hsp-orgs")
    public Object hspOrgs() {
        return hspOrgMainService.selectAll();
    }

    @PostMapping("/add")
    public Object add(@RequestBody List<HspOrgMainAddVo> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            throw new IllegalArgumentException("请选择添加的机构");
        }

        final LinkedList<HspOrgMainDto> dtos = new LinkedList<>();
        for (HspOrgMainAddVo vo : vos) {
            final HspOrgMainDto dto = new HspOrgMainDto();
            dto.setHspOrgId(vo.getHspOrgId());
            dto.setHspOrgCode(vo.getHspOrgCode());
            dto.setHspOrgName(vo.getHspOrgName());
            dtos.add(dto);
        }

        hspOrgMainService.addBatch(dtos);

        // 记录操作日志
        dtos.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_DEPT_DOCTOR.getDesc())
                    .setContent(String.format("新增 [%s] 送检机构", item.getHspOrgName())).toJSONString());
        });

        return Map.of();
    }

    @PostMapping("/delete")
    public Object delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("请选择需要删除的数据");
        }
        List<HspOrgMainDto> hspOrgMainDtos = hspOrgMainService.selectByHspOrgIds(ids);

        hspOrgMainService.deleteByHspOrgMainIds(ids);
        hspOrgDeptService.deleteByByHspOrgMainIds(ids);
        hspOrgDoctorService.deleteByByHspOrgMainIds(ids);

        hspOrgMainDtos.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_DEPT_DOCTOR.getDesc())
                    .setContent(String.format("删除 [%s] 送检机构及机构下科室、医生", item.getHspOrgName())).toJSONString());
        });
        return Map.of();
    }
}
