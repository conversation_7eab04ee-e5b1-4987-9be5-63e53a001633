package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.model.TbInstrumentGroupInstrument;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/25 19:15
 */
@Mapper
public interface TbInstrumentGroupInstrumentMapper extends BaseMapper<TbInstrumentGroupInstrument> {
    int addBatch(@Param("groupInstruments") List<TbInstrumentGroupInstrument> groupInstruments);
}
