package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 冰箱
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_refrigerator")
public class TbRefrigerator implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 冰箱ID
     */
    @TableId
    private Long refrigeratorId;
    /**
     * 冰箱编码
     */
    private String refrigeratorCode;
    /**
     * 冰箱名称
     */
    private String refrigeratorName;
    /**
     * 归档库名称
     */
    private String archiveStoreName;
    /**
     * 归档库ID
     */
    private Long archiveStoreId;
    /**
     * 位置
     */
    private String position;
    /**
     * 温度
     */
    private String temperature;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 是否启用(0未启用 1启用)
     * @see YesOrNoEnum
     */
    private Integer enable;
    /**
     * 是否删除(0 未删除 1已删除)
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人名称
     */
    private String updaterName;

}
