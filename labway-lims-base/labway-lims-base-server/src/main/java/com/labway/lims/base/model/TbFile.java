package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.base.api.enums.FileTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件
 */
@Getter
@Setter
@TableName("tb_file")
public class TbFile implements Serializable {


    /**
     * 文件ID
     */
    @TableId
    private Long fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 1: 文件 2: 文件夹
     *
     * @see FileTypeEnum
     */
    private Integer fileType;

    /**
     * 父文件ID
     */
    private Long parentFileId;

    /**
     * url 文件下载地址
     */
    private String url;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 1:已经删除 0未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 文件大小，字节
     */
    private Long fileSize;
}
