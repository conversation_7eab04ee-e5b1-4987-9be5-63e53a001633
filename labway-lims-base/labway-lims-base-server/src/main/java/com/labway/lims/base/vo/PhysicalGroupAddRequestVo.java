package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 体检单位 新增 Vo
 * 
 * <AUTHOR>
 * @since 2023/3/27 15:36
 */
@Getter
@Setter
public class PhysicalGroupAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 体检单位名称
     */
    private String physicalGroupName;
    /**
     * 联系人
     */
    private String contactUser;
    /**
     * 联系方式
     */
    private String contactPhone;
    /**
     * 是否启用(0未启用 1启用)
     * @see YesOrNoEnum
     */
    private Integer enable;
}
