
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.ItemPriceBasePackageDetailDto;
import com.labway.lims.base.model.TbItemPriceBasePackageDetail;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 项目价格基准包详情 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 19:21
 */
@Mapper(componentModel = "spring")
public interface ItemPriceBasePackageDetailConverter {

    ItemPriceBasePackageDetailDto itemPriceBasePackageDetailDtoFromTbObj(TbItemPriceBasePackageDetail obj);

    TbItemPriceBasePackageDetail tbItemPriceBasePackageDetailFromTbObjDto(ItemPriceBasePackageDetailDto dto);

    List<ItemPriceBasePackageDetailDto>
        itemPriceBasePackageDetailDtoListFromTbObjList(List<TbItemPriceBasePackageDetail> list);

    List<TbItemPriceBasePackageDetail>
        tbItemPriceBasePackageDetailListFromTbObjDto(List<ItemPriceBasePackageDetailDto> list);

}
