package com.labway.lims.base.service;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemRemarkDto;
import com.labway.lims.base.api.enums.AgeUnitEnum;
import com.labway.lims.base.api.enums.RelationalOperatorEnum;
import com.labway.lims.base.api.service.InstrumentReportItemRemarkService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.mapper.TbInstrumentReportItemRemarkMapper;
import com.labway.lims.base.mapstruct.InstrumentReportItemRemarkConverter;
import com.labway.lims.base.model.TbInstrumentReportItemRemark;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "instrument-report-item-remark")
public class InstrumentReportItemRemarkServiceImpl implements InstrumentReportItemRemarkService {

    @Resource
    private TbInstrumentReportItemRemarkMapper instrumentReportItemRemarkMapper;
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private InstrumentReportItemRemarkConverter remarkConverter;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Override
    @Cacheable(key = "'selectByInstrumentReportItemId:' + #instrumentReportItemId")
    public List<InstrumentReportItemRemarkDto> selectByInstrumentReportItemId(long instrumentReportItemId) {
        return instrumentReportItemRemarkMapper.selectList(new LambdaQueryWrapper<TbInstrumentReportItemRemark>()
                        .orderByDesc(TbInstrumentReportItemRemark::getInstrumentReportItemRemarkId)
                        .eq(TbInstrumentReportItemRemark::getInstrumentReportItemId, instrumentReportItemId)
                        .eq(TbInstrumentReportItemRemark::getStatus, YesOrNoEnum.YES.getCode())
                )
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByInstrumentReportItemId:' + #instrumentReportItemRemarkId")
    public InstrumentReportItemRemarkDto selectByInstrumentReportItemRemarkId(long instrumentReportItemRemarkId) {
        return convert(instrumentReportItemRemarkMapper.selectById(instrumentReportItemRemarkId));
    }

    @Nonnull
    @Override
    @Cacheable(key = "'selectByInstrumentReportItemRemarkIds:' + #instrumentReportItemRemarkIds")
    public List<InstrumentReportItemRemarkDto> selectByInstrumentReportItemRemarkIds(Collection<Long> instrumentReportItemRemarkIds) {
        if (CollectionUtils.isEmpty(instrumentReportItemRemarkIds)) {
            return Collections.emptyList();
        }
        return instrumentReportItemRemarkMapper.selectBatchIds(instrumentReportItemRemarkIds)
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public long addInstrumentReportItemRemark(InstrumentReportItemRemarkDto dto) {

        final InstrumentReportItemDto instrumentReportItem = SpringUtil.getBean(InstrumentReportItemService.class)
                .selectByInstrumentReportItemId(dto.getInstrumentReportItemId());
        if (Objects.isNull(instrumentReportItem)) {
            throw new IllegalArgumentException("仪器报告项目不存在");
        }

        final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentReportItem.getInstrumentId());
        if (Objects.isNull(instrument)) {
            throw new IllegalArgumentException("仪器不存在");
        }

        final TbInstrumentReportItemRemark remark = new TbInstrumentReportItemRemark();

        BeanUtils.copyProperties(dto, remark);

        remark.setHspOrgId(ObjectUtils.defaultIfNull(dto.getHspOrgId(), NumberUtils.LONG_ZERO));
        remark.setHspOrgName(StringUtils.defaultString(dto.getHspOrgName(), "通用"));

        remark.setResultMax(StringUtils.defaultString(remark.getResultMax()));
        remark.setResultMin(StringUtils.defaultString(remark.getResultMin()));
        remark.setResultMaxFormula(StringUtils.defaultString(remark.getResultMaxFormula()));
        remark.setResultMinFormula(StringUtils.defaultString(remark.getResultMinFormula()));

        remark.setAgeMax(ObjectUtils.defaultIfNull(remark.getAgeMax(), 200));
        remark.setAgeMin(ObjectUtils.defaultIfNull(remark.getAgeMin(), 0));
        remark.setAgeUnit(StringUtils.defaultIfBlank(remark.getAgeUnit(), AgeUnitEnum.YEAR.name()));

        remark.setResultRemark(StringUtils.defaultString(remark.getResultRemark()));

        remark.setInstrumentReportItemRemarkId(snowflakeService.genId());
        remark.setInstrumentReportItemId(dto.getInstrumentReportItemId());
        remark.setReportItemCode(instrumentReportItem.getReportItemCode());
        remark.setReportItemName(instrumentReportItem.getReportItemName());
        remark.setInstrumentId(instrument.getInstrumentId());
        remark.setInstrumentCode(instrument.getInstrumentCode());
        remark.setInstrumentName(instrument.getInstrumentName());

        remark.setStatus(1);
        remark.setIsDelete(YesOrNoEnum.NO.getCode());
        remark.setCreateDate(new Date());
        remark.setUpdateDate(new Date());
        remark.setCreatorId(LoginUserHandler.get().getUserId());
        remark.setCreatorName(LoginUserHandler.get().getNickname());
        remark.setUpdaterId(LoginUserHandler.get().getUserId());
        remark.setUpdaterName(LoginUserHandler.get().getNickname());

        // 校验是否冲突
        dto.setInstrumentReportItemRemarkId(remark.getInstrumentReportItemRemarkId());
        dto.setAgeMax(ObjectUtils.defaultIfNull(dto.getAgeMax(), 200));
        dto.setAgeMin(ObjectUtils.defaultIfNull(dto.getAgeMin(), 0));
        dto.setAgeUnit(StringUtils.defaultIfBlank(dto.getAgeUnit(), AgeUnitEnum.YEAR.name()));
        checkMutex(dto);

        // 都为空时，给默认值
        if (StringUtils.isBlank(remark.getAgeUnit()) && Objects.equals(-1, remark.getAgeMax())
                && Objects.equals(-1, remark.getAgeMin())) {
            remark.setAgeMax(200);
            remark.setAgeMin(0);
            remark.setAgeUnit(AgeUnitEnum.YEAR.name());
        }

        if (instrumentReportItemRemarkMapper.insert(remark) < 1) {
            throw new IllegalArgumentException("新增结果备注失败");
        }

        String logContent = String.format("用户 [%s] 添加了仪器 [%s] 下的报告项目 [%s] 的结果备注;结果备注信息：" +
                        "样本类型 [%s] 适用性别 [%s] 适用年龄下限 [%s] 适用年龄上限 [%s] 年龄单位 [%s] 结果值下限 [%s] 结果值上限 [%s]" +
                        " 结果备注 [%s] ",
                LoginUserHandler.get().getNickname(),
                remark.getInstrumentName(),
                remark.getReportItemName(),
                remark.getSampleTypeName(),
                remark.getSexStyleName(),
                remark.getAgeMin(),
                remark.getAgeMax(),
                remark.getAgeUnit(),
                remark.getResultMin(),
                remark.getResultMax(),
                remark.getResultRemark()
        );
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_REFERENCE.getDesc())
                        .setContent(logContent).toJSONString());

        log.info("用户 [{}] 添加结果备注 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(remark));

        return remark.getInstrumentReportItemRemarkId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByInstrumentReportItemRemarkId(InstrumentReportItemRemarkDto dto) {

        final InstrumentReportItemRemarkDto oldInstrumentReportItemRemark = selectByInstrumentReportItemRemarkId(dto.getInstrumentReportItemRemarkId());
        if (Objects.isNull(oldInstrumentReportItemRemark)) {
            throw new IllegalArgumentException("结果备注不存在");
        }

        final TbInstrumentReportItemRemark remark = new TbInstrumentReportItemRemark();

        BeanUtils.copyProperties(dto, remark);

        remark.setHspOrgId(ObjectUtils.defaultIfNull(dto.getHspOrgId(), NumberUtils.LONG_ZERO));
        remark.setHspOrgName(StringUtils.defaultString(dto.getHspOrgName(), "通用"));

        remark.setResultMax(StringUtils.defaultString(remark.getResultMax()));
        remark.setResultMin(StringUtils.defaultString(remark.getResultMin()));

        remark.setAgeMax(ObjectUtils.defaultIfNull(remark.getAgeMax(), 200));
        remark.setAgeMin(ObjectUtils.defaultIfNull(remark.getAgeMin(), 0));
        remark.setAgeUnit(StringUtils.defaultString(remark.getAgeUnit(), AgeUnitEnum.YEAR.name()));

        // 不可修改字段
        remark.setInstrumentReportItemId(null);
        remark.setReportItemCode(null);
        remark.setReportItemName(null);
        remark.setInstrumentId(null);
        remark.setInstrumentCode(null);
        remark.setInstrumentName(null);
        remark.setStatus(null);
        remark.setCreateDate(null);
        remark.setCreatorId(null);
        remark.setCreatorName(null);

        remark.setUpdateDate(new Date());
        remark.setUpdaterId(LoginUserHandler.get().getUserId());
        remark.setUpdaterName(LoginUserHandler.get().getNickname());

        // 校验是否冲突
        dto.setInstrumentReportItemId(oldInstrumentReportItemRemark.getInstrumentReportItemId());
        dto.setAgeMax(ObjectUtils.defaultIfNull(dto.getAgeMax(), 200));
        dto.setAgeMin(ObjectUtils.defaultIfNull(dto.getAgeMin(), 0));
        dto.setAgeUnit(StringUtils.defaultIfBlank(dto.getAgeUnit(), AgeUnitEnum.YEAR.name()));
        checkMutex(dto);

        if (instrumentReportItemRemarkMapper.updateById(remark) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改结果备注 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(remark));

        return true;
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByInstrumentReportItemRemarkId(long instrumentReportItemRemarkId) {
        return instrumentReportItemRemarkMapper.deleteById(instrumentReportItemRemarkId) > 0;
    }

    @Nullable
    @Override
    public List<InstrumentReportItemRemarkDto> selectByReportItemCodeAndOrgId(String reportItemCode, long orgId) {
        final LambdaQueryWrapper<TbInstrumentReportItemRemark> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentReportItemRemark::getReportItemCode, reportItemCode);
        return instrumentReportItemRemarkMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByInstrumentId:' + #instrumentId")
    public List<InstrumentReportItemRemarkDto> selectByInstrumentId(long instrumentId) {
        final LambdaQueryWrapper<TbInstrumentReportItemRemark> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentReportItemRemark::getInstrumentId, instrumentId);
        return instrumentReportItemRemarkMapper
                .selectList(wrapper)
                .stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByInstrumentGroupId:' + #instrumentGroupId")
    public List<InstrumentReportItemRemarkDto> selectByInstrumentGroupId(long instrumentGroupId) {
        return instrumentReportItemRemarkMapper.selectByInstrumentGroupId(instrumentGroupId);
    }

    @Override
    @Cacheable(key = "'selectByInstrumentGroupIdAndReportItemCode:' + #instrumentGroupId + ',' + #reportItemCode")
    public List<InstrumentReportItemRemarkDto> selectByInstrumentGroupIdAndReportItemCode(long instrumentGroupId, String reportItemCode) {
        if (StringUtils.isBlank(reportItemCode)) {
            return Collections.emptyList();
        }
        return instrumentReportItemRemarkMapper.selectByInstrumentGroupIdAndReportItemCode(instrumentGroupId, reportItemCode);
    }

    @Override
    public List<InstrumentReportItemRemarkDto> selectByInstrumentIds(Collection<Long> instrumentIds) {
        if (CollectionUtils.isEmpty(instrumentIds)) {
            return Collections.emptyList();
        }

        return instrumentIds.stream().map(SpringUtil.getBean(InstrumentReportItemRemarkService.class)::selectByInstrumentId)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull).collect(Collectors.toList());

    }

    @Override
    public List<InstrumentReportItemRemarkDto> selectByInstrumentIdAndReportItemCodes(long instrumentId, Collection<String> reportItemCodes) {
        if (CollectionUtils.isEmpty(reportItemCodes)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbInstrumentReportItemRemark> in = Wrappers.lambdaQuery(TbInstrumentReportItemRemark.class)
                .eq(TbInstrumentReportItemRemark::getInstrumentId, instrumentId)
                .in(TbInstrumentReportItemRemark::getReportItemCode, reportItemCodes);

        return instrumentReportItemRemarkMapper.selectList(in).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<InstrumentReportItemRemarkDto>> selectByInstrumentIdAndReportItemCodesAsMap(long instrumentId, Collection<String> reportItemCodes) {
        return selectByInstrumentIdAndReportItemCodes(instrumentId, reportItemCodes)
                .stream().collect(Collectors.groupingBy(InstrumentReportItemRemarkDto::getReportItemCode));
    }

    @Override
    public List<InstrumentReportItemRemarkDto> selectByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return instrumentReportItemRemarkMapper.selectBatchIds(ids)
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public InstrumentReportItemRemarkDto selectById(long id) {
        return selectByIds(List.of(id)).stream().findFirst().orElse(null);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void deleteByInstrumentReportItemId(long instrumentReportItemId) {
        final LambdaQueryWrapper<TbInstrumentReportItemRemark> wrapper = Wrappers.lambdaQuery(TbInstrumentReportItemRemark.class)
                .eq(TbInstrumentReportItemRemark::getInstrumentReportItemId, instrumentReportItemId);
        instrumentReportItemRemarkMapper.delete(wrapper);
    }

    private void checkMutex(InstrumentReportItemRemarkDto remark) {
        // 都为空时，跳过校验
        if (StringUtils.isBlank(remark.getAgeUnit()) && Objects.isNull(remark.getAgeMax())
                && Objects.isNull(remark.getAgeMin())) {
            return;
        }

        if (StringUtils.isBlank(remark.getAgeUnit()) || Objects.isNull(remark.getAgeMax())
                || Objects.isNull(remark.getAgeMin())) {
            throw new IllegalArgumentException("年龄范围错误");
        }

        // 最多200年
        if (AgeUnitEnum.YEAR.name().equals(remark.getAgeUnit()) && (remark.getAgeMax() > 200)) {
            throw new IllegalArgumentException("年龄范围过大，应当小于 200 岁");
        }

        // 最多2400月
        if (AgeUnitEnum.MONTH.name().equals(remark.getAgeUnit()) && (remark.getAgeMax() > 2400)) {
            throw new IllegalArgumentException("年龄范围过大，应当小于 2400 月");
        }

        if (Objects.isNull(remark.getHspOrgId())) {
            // 0L 代表机构通用
            remark.setHspOrgId(0L);
        }

        List<InstrumentReportItemRemarkDto> refs = selectByInstrumentReportItemId(remark.getInstrumentReportItemId());
        refs.removeIf(e -> Objects.equals(e.getInstrumentReportItemRemarkId(), remark.getInstrumentReportItemRemarkId()));

        if (CollectionUtils.isEmpty(refs)) {
            return;
        }

        // 查询和现在相同的送检机构
        refs = refs.stream()
                .filter(e -> Objects.nonNull(remark.getHspOrgId()) && Objects.equals(remark.getHspOrgId(), e.getHspOrgId()))
                .collect(Collectors.toList());

        // 查询之前所有的结果备注, 和当前性别相同 或 通用
        refs = refs.stream()
                // 相同性别才会互斥
                .filter(e -> {
                    if (Objects.isNull(e.getSexStyle())) {
                        return false;
                    }
                    return Objects.equals(remark.getSexStyle(), e.getSexStyle());
                })
                // 样本类型过滤
                // 如果相同时，那么校验样本类型
                .filter(e -> Objects.equals(e.getSampleTypeCode(), remark.getSampleTypeCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(refs)) {
            return;
        }

        // 年龄区间
        // 判断方法是根据每个年龄范围生成区间，然后判断区间是否相交
        final List<List<Integer>> ranges = new ArrayList<>(refs.size());

        for (var ref : refs) {
            int start = ref.getAgeMin(), end = ref.getAgeMax();

            // 如果是大于那就是 +1 开始
            if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(ref.getAgeMinFormula()), RelationalOperatorEnum.GT)) {
                start = ref.getAgeMin() + 1;
            }

            // 如果是小于那就是 -1 结束
            if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(ref.getAgeMaxFormula()), RelationalOperatorEnum.LT)) {
                end = ref.getAgeMax() - 1;
            }

            // 把所有的单位转化为 天
            if (AgeUnitEnum.MONTH.name().equals(ref.getAgeUnit())) {
                start = start * MONTH_DAYS;
                end = end * MONTH_DAYS;
            } else if (AgeUnitEnum.YEAR.name().equals(ref.getAgeUnit())) {
                start = start * YEAR_DAYS;
                end = end * YEAR_DAYS;
            }

            ranges.add(IntStream.rangeClosed(start, end).boxed().collect(Collectors.toList()));
        }

        int start = remark.getAgeMin();
        int end = remark.getAgeMax();

        // 如果是大于那就是 +1 开始
        if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(remark.getAgeMinFormula()), RelationalOperatorEnum.GT)) {
            start = start + 1;
        }

        // 如果是小于那就是 -1 结束
        if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(remark.getAgeMaxFormula()), RelationalOperatorEnum.LT)) {
            end = end - 1;
        }

        // 把所有的单位转化为 天
        if (AgeUnitEnum.MONTH.name().equals(remark.getAgeUnit())) {
            start = start * MONTH_DAYS;
            end = end * MONTH_DAYS;
        } else if (AgeUnitEnum.YEAR.name().equals(remark.getAgeUnit())) {
            start = start * YEAR_DAYS;
            end = end * YEAR_DAYS;
        }

        // 判断结果值上下限范围是否冲突
        boolean rangeHasOverlap = checkResultRangeConflict(remark, refs);

        // 判断是否有相交
        for (List<Integer> range : ranges) {
            for (int i = start; i <= end; i++) {
                // 二分查找
                // perf #dev-dongguan 如果年龄冲突，要判断一下结果值上下限是否也冲突，才返回校验信息
                if ((Collections.binarySearch(range, i) >= 0) && rangeHasOverlap) {
                    throw new IllegalStateException("年龄范围冲突 , 请检查");
                }
            }
        }

    }

    private boolean checkResultRangeConflict(InstrumentReportItemRemarkDto remark, List<InstrumentReportItemRemarkDto> refs) {
        final String resultMax = remark.getResultMax();
        final String resultMin = remark.getResultMin();
        final String resultMaxFormula = remark.getResultMaxFormula();
        final String resultMinFormula = remark.getResultMinFormula();

        // 是否有重叠
        boolean hasOverlap = false;

        // 校验数字格式
        if (StringUtils.isNotBlank(resultMin) && !isNumeric(resultMin)) {
            throw new IllegalArgumentException("结果下限必须为数字");
        }
        if (StringUtils.isNotBlank(resultMax) && !isNumeric(resultMax)) {
            throw new IllegalArgumentException("结果上限必须为数字");
        }

        // 解析新增备注的结果范围
        BigDecimal newMin = StringUtils.isBlank(resultMin) ?
                new BigDecimal(Integer.MIN_VALUE) : new BigDecimal(resultMin);
        BigDecimal newMax = StringUtils.isBlank(resultMax) ?
                new BigDecimal(Integer.MAX_VALUE) : new BigDecimal(resultMax);

        // 处理大于/小于运算符
        if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(resultMinFormula),
                RelationalOperatorEnum.GT)) {
            newMin = newMin.add(EPSILON);
        }
        if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(resultMaxFormula),
                RelationalOperatorEnum.LT)) {
            newMax = newMax.subtract(EPSILON);
        }

        // 检查与已存在备注的结果范围是否重叠
        for (InstrumentReportItemRemarkDto ref : refs) {
            // 获取已存在范围的上下限
            BigDecimal existMin;
            BigDecimal existMax;

            // 设置下限
            if (StringUtils.isBlank(ref.getResultMin()) || !isNumeric(ref.getResultMin())) {
                existMin = new BigDecimal(Integer.MIN_VALUE);
            } else {
                existMin = new BigDecimal(ref.getResultMin());
                if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(ref.getResultMinFormula()),
                        RelationalOperatorEnum.GT)) {
                    existMin = existMin.add(EPSILON);
                }
            }

            // 设置上限
            if (StringUtils.isBlank(ref.getResultMax()) || !isNumeric(ref.getResultMax())) {
                existMax = new BigDecimal(Integer.MAX_VALUE);
            } else {
                existMax = new BigDecimal(ref.getResultMax());
                if (Objects.equals(RelationalOperatorEnum.valueOfByOperator(ref.getResultMaxFormula()),
                        RelationalOperatorEnum.LT)) {
                    existMax = existMax.subtract(EPSILON);
                }
            }

            // 判断区间是否重叠
            // 不重叠的条件: 新区间的最大值小于已存在区间的最小值 或 新区间的最小值大于已存在区间的最大值
            if (!(newMax.compareTo(existMin) < 0 || newMin.compareTo(existMax) > 0)) {
                // throw new IllegalStateException("结果范围存在重叠 , 请检查");
                hasOverlap = true;
            }
        }

        return hasOverlap;
    }

    private static final BigDecimal EPSILON = new BigDecimal("0.000001");

    /**
     * 判断字符串是否为数字(包括小数)
     */
    private boolean isNumeric(String str) {
        return NumberUtils.isCreatable(str);
        /*if (StringUtils.isBlank(str)) {
            return false;
        }
        try {
            new BigDecimal(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }*/
    }

    private InstrumentReportItemRemarkDto convert(TbInstrumentReportItemRemark instrumentReportItemRemark) {
        if (Objects.isNull(instrumentReportItemRemark)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(instrumentReportItemRemark), InstrumentReportItemRemarkDto.class);
    }

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public List<Long> copyReportItemRemark(Long fromInstrumentReportItemId, InstrumentReportItemDto instrumentReportItemDto) {
        List<Long> ids = new ArrayList<>();
        // 仪器报告项目ID
        Long instrumentReportItemId = instrumentReportItemDto.getInstrumentReportItemId();

        {
            // 删除原有的结果备注
            LambdaQueryWrapper<TbInstrumentReportItemRemark> wrapper =
                    Wrappers
                            .lambdaQuery(TbInstrumentReportItemRemark.class)
                            .eq(TbInstrumentReportItemRemark::getInstrumentReportItemId, instrumentReportItemId);
            instrumentReportItemRemarkMapper.delete(wrapper);
        }

        Date current = new Date();
        InstrumentReportItemRemarkService thisProxy = (InstrumentReportItemRemarkService) AopContext.currentProxy();
        List<InstrumentReportItemRemarkDto> remarkDtos = thisProxy.selectByInstrumentReportItemId(fromInstrumentReportItemId);
        for (InstrumentReportItemRemarkDto remarkDto : remarkDtos) {
            TbInstrumentReportItemRemark remark = remarkConverter.convertDto2Entity(remarkDto);

            remark.setInstrumentReportItemRemarkId(snowflakeService.genId());
            remark.setInstrumentReportItemId(instrumentReportItemId);
            remark.setReportItemCode(remarkDto.getReportItemCode());
            remark.setReportItemName(remarkDto.getReportItemName());
            remark.setInstrumentId(remarkDto.getInstrumentId());
            remark.setInstrumentCode(remarkDto.getInstrumentCode());
            remark.setInstrumentName(remarkDto.getInstrumentName());

            remark.setStatus(1);
            remark.setIsDelete(YesOrNoEnum.NO.getCode());
            remark.setCreateDate(current);
            remark.setCreatorId(LoginUserHandler.get().getUserId());
            remark.setCreatorName(LoginUserHandler.get().getNickname());
            remark.setUpdateDate(current);
            remark.setUpdaterId(LoginUserHandler.get().getUserId());
            remark.setUpdaterName(LoginUserHandler.get().getNickname());

            if (instrumentReportItemRemarkMapper.insert(remark) < 1) {
                throw new IllegalArgumentException("拷贝结果备注失败");
            }

            String logContent = String.format("用户 [%s] 拷贝了仪器 [%s] 下的报告项目 [%s] 的结果备注;结果备注信息：" +
                            "样本类型 [%s] 适用性别 [%s] 适用年龄下限 [%s] 适用年龄上限 [%s] 年龄单位 [%s] 结果值下限 [%s] 结果值上限 [%s]" +
                            " 结果备注 [%s] ",
                    LoginUserHandler.get().getNickname(),
                    remark.getInstrumentName(),
                    remark.getReportItemName(),
                    remark.getSampleTypeName(),
                    remark.getSexStyleName(),
                    remark.getAgeMin(),
                    remark.getAgeMax(),
                    remark.getAgeUnit(),
                    remark.getResultMin(),
                    remark.getResultMax(),
                    remark.getResultRemark()
            );

            // 异步发送消息
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_REFERENCE.getDesc())
                            .setContent(logContent).toJSONString());

            log.info("用户 [{}] 拷贝结果备注 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(remark));

            ids.add(remark.getInstrumentReportItemRemarkId());
        }

        return ids;
    }

}
