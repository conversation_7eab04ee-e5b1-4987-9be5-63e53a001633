package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 套餐项目
 * 
 * <AUTHOR>
 * @since 2023/3/28 17:33
 */
@Getter
@Setter
@TableName("tb_package_item")
public class TbPackageItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 套餐项目ID
     */
    @TableId
    private Long packageItemId;
    /**
     * 套餐ID
     */
    private Long packageId;
    /**
     * 检验项目ID
     */
    private Long testItemId;
    /**
     * 检验项目编码
     */
    private String testItemCode;
    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 1:已经删除 0未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人名称
     */
    private String updaterName;

}
