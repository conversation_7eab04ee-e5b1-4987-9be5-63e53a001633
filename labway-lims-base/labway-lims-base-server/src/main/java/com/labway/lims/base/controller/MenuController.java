package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.MenuTypeCodeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.CascadeMenuDto;
import com.labway.lims.base.api.dto.MenuDto;
import com.labway.lims.base.api.service.MenuService;
import com.labway.lims.base.vo.MenuVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/20 14:29
 */
@RestController
@RequestMapping("/menu")
public class MenuController extends BaseController {
    @Resource
    private MenuService menuService;

    @PostMapping("/add")
    public Object add(@RequestBody MenuVo vo) {

        if (StringUtils.isEmpty(vo.getMenuName())) {
            throw new IllegalArgumentException("菜单名称不能为空");
        }

        if (StringUtils.isEmpty(vo.getMenuTypeCode())) {
            throw new IllegalArgumentException("未知菜单类型");
        }
        // 当类型按钮的时候menuCode不能为空
        if (StringUtils.equals(vo.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name())) {
            if (Objects.isNull(vo.getMenuCode())) {
                throw new IllegalArgumentException("按钮ID不能为空");
            }
        }

        if (StringUtils.trim(vo.getMenuUrl()).length() < vo.getMenuUrl().length()) {
            throw new IllegalArgumentException("菜单url前后不能有空格");
        }

        final MenuDto dto = new MenuDto();
        BeanUtils.copyProperties(vo, dto);

        return Map.of("id", menuService.addMenu(dto));
    }

    /**
     * 查询当前菜单下的所有按钮
     */
    @PostMapping("/selectButton")
    public Object selectButton(@RequestParam("menuId") Long menuId) {
        return new ArrayList<>(menuService.selectByProMenuId(menuId));
    }

    @PostMapping("/selectAll")
    public List<CascadeMenuDto> selectAll() {
        LoginUserHandler.User user = LoginUserHandler.get();
        final List<MenuDto> menuDtos = menuService.selectAllMenu(user.getOrgId());

        final List<CascadeMenuDto> cascadeMenuDtos = JSON.parseArray(JSON.toJSONString(menuDtos), CascadeMenuDto.class);
        for (CascadeMenuDto menuDto : cascadeMenuDtos) {
            if (StringUtils.equalsIgnoreCase(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.PAGE.name())) {
                menuDto.setChildList(menuDtos.stream().filter(e -> Objects.equals(e.getProMenuId(), menuDto.getMenuId())).collect(Collectors.toList()));
            }
            if (!StringUtils.equals(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name())) {
                final LinkedList<Long> list = new LinkedList<>();
                selectProIds(menuDto.getMenuId(), list,menuDtos);
                menuDto.setMenuIds(list);
                if (Objects.equals(menuDto.getProMenuId(), NumberUtils.LONG_ZERO)) {
                    if (CollectionUtils.isNotEmpty(menuDto.getMenuIds())) {
                        menuDto.getMenuIds().removeFirst();
                    }
                }
                if (StringUtils.equals(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.PAGE.name())
                    || StringUtils.equals(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.MENU.name())) {
                    if (CollectionUtils.isNotEmpty(menuDto.getMenuIds())) {
                        menuDto.getMenuIds().removeLast();
                    }
                }
            }
        }
        return cascadeMenuDtos.stream().filter(e -> Objects.nonNull(e.getMenuSort()))
            .sorted(Comparator.comparing(MenuDto::getMenuSort)).collect(Collectors.toList());
    }

    /**
     * 递归查询当前菜单的父级菜单
     *
     * @param menuId 当前菜单id
     */
    private void selectProIds(Long menuId, LinkedList<Long> list,List<MenuDto> allMenus) {
        if (Objects.equals(menuId, NumberUtils.LONG_ZERO)) {
            return;
        }

        list.addFirst(menuId);
        final MenuDto menuDto = allMenus.stream().filter(e->Objects.equals(e.getMenuId(),menuId)).findFirst().orElse(null);
        if (Objects.isNull(menuDto)) {
            return;
        }
        selectProIds(menuDto.getProMenuId(), list,allMenus);
    }

    @PostMapping("/selectAllCascade")
    public Object selectAllCascade() {
        LoginUserHandler.User user = LoginUserHandler.get();
        final List<MenuDto> menuDtos = menuService.selectAllMenu(user.getOrgId());
        final List<CascadeMenuDto> cascadeMenuDtos = JSON.parseArray(JSON.toJSONString(menuDtos), CascadeMenuDto.class);
        for (CascadeMenuDto menuDto : cascadeMenuDtos) {
            if (StringUtils.equals(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.PAGE.name())) {
                final LinkedList<MenuDto> menus = new LinkedList<>();

                selectCascade(menuDto, menus);
                menuDto.setMenus(menus);
            }
        }
        return cascadeMenuDtos;
    }

    @PostMapping("/selectCascade")
    @Deprecated
    public Object selectCascade(@RequestParam Long roleId) {
        final List<MenuDto> menuDtos = menuService.selectByRoleId(roleId);
        final List<CascadeMenuDto> cascadeMenuDtos = JSON.parseArray(JSON.toJSONString(menuDtos), CascadeMenuDto.class);
        for (CascadeMenuDto menuDto : cascadeMenuDtos) {
            if (StringUtils.equals(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.PAGE.name())) {
                // 获取页面下的所有按钮
                menuDtos.addAll(menuService.selectByProMenuId(menuDto.getProMenuId()));
            }
        }
        for (CascadeMenuDto menuDto : cascadeMenuDtos) {
            if (StringUtils.equals(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.PAGE.name())) {
                final LinkedList<MenuDto> menus = new LinkedList<>();

                selectCascade(menuDto, menus);
                menuDto.setMenus(menus);
            }
        }

        return menuDtos;
    }

    private void selectCascade(MenuDto menu, LinkedList<MenuDto> list) {
        if (Objects.isNull(menu)) {
            return;
        }

        list.addFirst(menu);

        selectCascade(menuService.selectByMenuId(menu.getProMenuId()), list);
    }

    @PostMapping("/selectAllTreeMenu")
    public Object selectAllTreeMenu() {
        LoginUserHandler.User user = LoginUserHandler.get();
        return menuService.selectAllTreeMenu(user.getOrgId());
    }

    /**
     * 修改菜单
     */
    @PostMapping("/update")
    public Object update(@RequestBody MenuVo vo) {
        if (Objects.nonNull(vo.getMenuName())) {
            if (StringUtils.isBlank(vo.getMenuName())) {
                throw new IllegalArgumentException("菜单名称不能为空");
            }
        }

        if (Arrays.stream(MenuTypeCodeEnum.values()).noneMatch(e -> e.name().equals(vo.getMenuTypeCode()))) {
            throw new IllegalArgumentException("未知菜单类型");
        }
        final MenuDto menuDto = menuService.selectByMenuId(vo.getMenuId());
        if (Objects.isNull(menuDto)) {
            throw new IllegalArgumentException("当前菜单不存在");
        }
        if (!Objects.equals(menuDto.getMenuTypeCode(), vo.getMenuTypeCode())) {
            throw new IllegalArgumentException("菜单类型不允许修改,请删除后新建");
        }

        if (Objects.isNull(vo.getProMenuId())) {
            if (StringUtils.equalsAny(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.PAGE.name(),
                MenuTypeCodeEnum.BUTTON.name())) {
                throw new IllegalArgumentException("页面或者按钮不能修改位顶级菜单");
            }
            // 父级ID不传或者传0表示改为顶级菜单
            vo.setProMenuId(NumberUtils.LONG_ZERO);
        }

        final MenuDto dto = JSON.parseObject(JSON.toJSONString(vo), MenuDto.class);

        menuService.updateByMenuId(dto);

        return Collections.emptyMap();
    }

    /**
     * 删除菜单
     */
    @PostMapping("/delete")
    public Object delete(@RequestBody Set<Long> menuIds) {

        for (Long menuId : menuIds) {
            final MenuDto menuDto = menuService.selectByMenuId(menuId);
            if (Objects.isNull(menuDto)) {
                throw new IllegalArgumentException("当前选中菜单不存在");
            }
            if (!StringUtils.equals(menuDto.getMenuTypeCode(), MenuTypeCodeEnum.BUTTON.name())) {
                if (Objects.equals(menuDto.getEnable(), YesOrNoEnum.YES.getCode())) {
                    throw new IllegalArgumentException("当前选择菜单是已启用状态不能删除");
                }
            }
        }

        for (Long id : menuIds) {
            menuService.deleteByMenuId(id);
            // 并且删除当前菜单下的子菜单
            menuService.deleteByProMenuId(id);
        }
        return Collections.emptyMap();
    }

}
