package com.labway.lims.base.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.stream.StreamUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.dto.ExportReportItemDTO;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.vo.ReportItemAddVo;
import com.labway.lims.base.vo.ReportItemCopyVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 仪器报告项目
 *
 * <AUTHOR>
 * @since 2023/3/20 14:29
 */
@RestController
@RequestMapping("/report-item")
public class ReportItemController extends BaseController {
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    @DubboReference
    private InstrumentService instrumentService;
    @Resource
    private InstrumentGroupService instrumentGroupService;
    @Resource
    private ReportItemService reportItemService;

    @DubboReference
    private RabbitMQService rabbitMQService;

    @Resource
    private TestItemService testItemService;

    /**
     * 根据仪器id获取
     */
    @GetMapping("/items")
    public Object items(@RequestParam Long instrumentId, @RequestParam(required = false) Integer enable
            ,@RequestParam(required = false) Long groupId) {

        final Map<Long, InstrumentDto> map = new HashMap<>();

        if (Objects.isNull(instrumentId)) {
            map.putAll(instrumentService.selectByOrgId(LoginUserHandler.get().getOrgId()).stream()
                    .collect(Collectors.toMap(InstrumentDto::getInstrumentId, v -> v, (a, b) -> a)));
        } else {
            final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentId);
            if (Objects.isNull(instrument)) {
                throw new IllegalArgumentException("仪器不存在");
            }
            map.put(instrument.getInstrumentId(), instrument);
        }

        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentIds(map.keySet());
        Stream<InstrumentReportItemDto> stream = instrumentReportItems.stream();
        if (enable != null && (enable == 0 || enable == 1)) {
            stream = stream.filter(e -> e.getEnable().equals(enable));
        }
        //根据专业组筛选
        if (groupId != null) {
            stream = stream.filter(e -> groupId.equals(map.get(e.getInstrumentId()).getGroupId()));
        }
        return stream.map(e -> Dict.of("instrumentReportItemId", e.getInstrumentReportItemId(), "reportItemCode",
                        e.getReportItemCode(), "reportItemName", e.getReportItemName(), "reportItemUnit",
                        StringUtils.defaultString(e.getReportItemUnitName()), "reportItemUnitCode", e.getReportItemUnit(),
                        "instrumentName", e.getInstrumentName(), "instrumentCode", e.getInstrumentCode(), "instrumentId", e.getInstrumentId(), "groupName",
                        Optional.ofNullable(map.get(e.getInstrumentId())).map(InstrumentDto::getGroupName).orElse(StringUtils.EMPTY),
                        "enable", e.getEnable(), "isQc", e.getIsQc(), "itemTypeCode", e.getItemTypeCode(), "itemTypeName", e.getItemTypeName(),
                        "enName", e.getEnName(), "enAb", e.getEnAb(), "printSort", e.getPrintSort(),
                        "groupId", Optional.ofNullable(map.get(e.getInstrumentId())).map(InstrumentDto::getGroupId).orElse(0L)))
                .collect(Collectors.toList());
    }

    /**
     * 根据专业小组获取
     */
    @GetMapping("/group-items")
    public Object itemsByGroupId(@RequestParam Long instrumentGroupId) {

        List<InstrumentDto> instrumentDtos = instrumentService.selectByInstrumentGroupId(instrumentGroupId);
        if (CollectionUtils.isEmpty(instrumentDtos)) {
            throw new IllegalArgumentException("仪器不存在");
        }

        final Map<Long, InstrumentDto> map = instrumentDtos.stream().collect(Collectors.toMap(InstrumentDto::getInstrumentId, v -> v, (a, b) -> a));

        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentIds(map.keySet());
        Stream<InstrumentReportItemDto> stream = instrumentReportItems.stream();
        return stream.map(e -> Dict.of("instrumentReportItemId", e.getInstrumentReportItemId(), "reportItemCode",
                        e.getReportItemCode(), "reportItemName", e.getReportItemName(), "reportItemUnit",
                        StringUtils.defaultString(e.getReportItemUnitName()), "reportItemUnitCode", e.getReportItemUnit(),
                        "instrumentName", e.getInstrumentName(), "instrumentCode", e.getInstrumentCode(), "instrumentId", e.getInstrumentId(), "groupName",
                        Optional.ofNullable(map.get(e.getInstrumentId())).map(InstrumentDto::getGroupName).orElse(StringUtils.EMPTY),
                        "enable", e.getEnable(), "isQc", e.getIsQc(), "itemTypeCode", e.getItemTypeCode(), "itemTypeName", e.getItemTypeName(),
                        "enName", e.getEnName(), "enAb", e.getEnAb(), "printSort", e.getPrintSort(),
                        "groupId", Optional.ofNullable(map.get(e.getInstrumentId())).map(InstrumentDto::getGroupId).orElse(0L)))
                .collect(Collectors.toList());
    }

    @GetMapping("/distinct-items2")
    public Object distinctItems(@RequestParam Long instrumentId, @RequestParam(required = false) Integer enable
            ,@RequestParam(required = false) Long groupId) {

        final Map<Long, InstrumentDto> map = new HashMap<>();

        if (Objects.isNull(instrumentId)) {
            map.putAll(instrumentService.selectByOrgId(LoginUserHandler.get().getOrgId()).stream()
                    .collect(Collectors.toMap(InstrumentDto::getInstrumentId, v -> v, (a, b) -> a)));
        } else {
            final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentId);
            if (Objects.isNull(instrument)) {
                throw new IllegalArgumentException("仪器不存在");
            }
            map.put(instrument.getInstrumentId(), instrument);
        }

        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentIds(map.keySet());
        Stream<InstrumentReportItemDto> stream = instrumentReportItems.stream();
        if (enable != null && (enable == 0 || enable == 1)) {
            stream = stream.filter(e -> e.getEnable().equals(enable));
        }
        //根据专业组筛选
        if (groupId != null) {
            stream = stream.filter(e -> groupId.equals(map.get(e.getInstrumentId()).getGroupId()));
        }
        return stream.filter(distinctByKey(InstrumentReportItemDto::getReportItemCode)).map(e -> Dict.of("instrumentReportItemId", e.getInstrumentReportItemId(), "reportItemCode",
                        e.getReportItemCode(), "reportItemName", e.getReportItemName(), "reportItemUnit",
                        StringUtils.defaultString(e.getReportItemUnitName()), "reportItemUnitCode", e.getReportItemUnit(),
                        "instrumentName", e.getInstrumentName(), "instrumentCode", e.getInstrumentCode(), "instrumentId", e.getInstrumentId(), "groupName",
                        Optional.ofNullable(map.get(e.getInstrumentId())).map(InstrumentDto::getGroupName).orElse(StringUtils.EMPTY),
                        "enable", e.getEnable(), "isQc", e.getIsQc(), "itemTypeCode", e.getItemTypeCode(), "itemTypeName", e.getItemTypeName(),
                        "enName", e.getEnName(), "enAb", e.getEnAb(),
                        "groupId", Optional.ofNullable(map.get(e.getInstrumentId())).map(InstrumentDto::getGroupId).orElse(0L)))
                .collect(Collectors.toList());
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    /**
     * 获取当前机构下所有不重复的仪器报告项目
     */
    @GetMapping("/distinct-items")
    public Object distinctItems() {

        final Map<Long, InstrumentDto> map = new HashMap<>();


        map.putAll(instrumentService.selectByOrgId(LoginUserHandler.get().getOrgId()).stream()
                .collect(Collectors.toMap(InstrumentDto::getInstrumentId, v -> v, (a, b) -> a)));

        // 根据报告项目Code去重
        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentIds(map.keySet()).stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(p -> p.getReportItemCode()))), ArrayList::new));

        Set<Dict> distinctItems = instrumentReportItems.stream().filter(v -> v.getEnable() == YesOrNoEnum.YES.getCode()).distinct()
                .map(e -> Dict.of("reportItemCode", e.getReportItemCode(), "reportItemName", e.getReportItemName())).collect(Collectors.toSet());

        return distinctItems;
    }


    /**
     * 启用
     */
    @PostMapping("/enable")
    public Object enable(@RequestBody Set<Long> instrumentReportItemIds) {
        for (Long instrumentReportItemId : instrumentReportItemIds) {
            final InstrumentReportItemDto instrumentReportItem = new InstrumentReportItemDto();
            instrumentReportItem.setEnable(YesOrNoEnum.YES.getCode());
            instrumentReportItem.setInstrumentReportItemId(instrumentReportItemId);
            instrumentReportItem.setUpdateDate(new Date());
            instrumentReportItem.setUpdaterId(LoginUserHandler.get().getUserId());
            instrumentReportItem.setUpdaterName(LoginUserHandler.get().getNickname());
            instrumentReportItemService.updateByInstrumentReportItemId(instrumentReportItem);
        }
        return Map.of();
    }


    /**
     * 停用
     */
    @PostMapping("/disable")
    public Object disable(@RequestBody Set<Long> instrumentReportItemIds) {
        for (Long instrumentReportItemId : instrumentReportItemIds) {
            final InstrumentReportItemDto instrumentReportItem = new InstrumentReportItemDto();
            instrumentReportItem.setEnable(YesOrNoEnum.NO.getCode());
            instrumentReportItem.setInstrumentReportItemId(instrumentReportItemId);
            instrumentReportItem.setUpdateDate(new Date());
            instrumentReportItem.setUpdaterId(LoginUserHandler.get().getUserId());
            instrumentReportItem.setUpdaterName(LoginUserHandler.get().getNickname());
            instrumentReportItemService.updateByInstrumentReportItemId(instrumentReportItem);
        }
        return Map.of();
    }


    /**
     * 根据仪器code获取
     */
    @GetMapping("/items-by-code")
    public Object items(@RequestParam String instrumentCode) {

        if (Objects.isNull(instrumentCode)) {
            throw new IllegalArgumentException("缺少仪器编码");
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        final InstrumentDto instrument =
                instrumentService.selectByOrgIdAndInstrumentCode(user.getOrgId(), instrumentCode);
        if (Objects.isNull(instrument)) {
            throw new IllegalArgumentException("仪器不存在");
        }

        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentId(instrument.getInstrumentId());

        return instrumentReportItems.stream()
                .map(e -> Map.of("instrumentReportItemId", e.getInstrumentReportItemId(), "reportItemCode",
                        e.getReportItemCode(), "reportItemName", e.getReportItemName(), "reportItemUnit",
                        StringUtils.defaultString(e.getReportItemUnitName()), "reportItemUnitCode", e.getReportItemUnit(),
                        "instrumentName", e.getInstrumentName(), "instrumentCode", instrument.getInstrumentCode(), "groupName",
                        instrument.getGroupName(), "enable", e.getEnable(), "isQc", e.getIsQc()))
                .collect(Collectors.toList());
    }

    /**
     * 根据仪器id获取
     */
    @GetMapping("/instrument-group-items")
    public Object instrumentGroupItems(@RequestParam Long instrumentGroupId) {

        if (Objects.isNull(instrumentGroupId)) {
            throw new IllegalArgumentException("参数错误");
        }
        final InstrumentGroupDto instrumentGroup = instrumentGroupService.selectByInstrumentGroupId(instrumentGroupId);
        if (Objects.isNull(instrumentGroup)) {
            throw new IllegalArgumentException("专业小组不存在");
        }

        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentGroupId(instrumentGroupId);

        return instrumentReportItems.stream()
                .map(e -> Map.of("instrumentReportItemId", e.getInstrumentReportItemId(), "reportItemCode",
                        e.getReportItemCode(), "reportItemName", e.getReportItemName(), "reportItemUnit", e.getReportItemUnit(),
                        "instrumentName", e.getInstrumentName(), "enable", e.getEnable()))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有的报告项目
     */
    @GetMapping("/all-items")
    public Object allItems(Long groupId) {
        //判断是否需要进行专业组的筛选
        if (!Objects.isNull(groupId)) {
            List<TestItemDto> testItems = testItemService.selectByGroupId(groupId);
            if (!CollectionUtils.isEmpty(testItems)) {
                return reportItemService.selectByTestItemIds(testItems.stream().map(TestItemDto::getTestItemId).collect(Collectors.toSet()))
                        .stream()
                        .filter(StreamUtils.distinctByKey(ReportItemDto::getReportItemCode))
                        .collect(Collectors.toList());
            }
            return Collections.emptyList();
        } else {
            return reportItemService.selectByOrgId(LoginUserHandler.get().getOrgId()).stream()
                    .filter(StreamUtils.distinctByKey(ReportItemDto::getReportItemCode))
                    .collect(Collectors.toMap(ReportItemDto::getReportItemCode, v -> v, (a, b) -> a)).values();
        }
    }

    /**
     * 获取仪器报告项目信息
     */
    @GetMapping("/info")
    public Object info(@RequestParam Long instrumentReportItemId) {

        final InstrumentReportItemDto instrumentReportItem =
                instrumentReportItemService.selectByInstrumentReportItemId(instrumentReportItemId);
        if (Objects.isNull(instrumentReportItem)) {
            throw new IllegalStateException("仪器报告项目不存在");
        }

        return instrumentReportItem;
    }

    /**
     * 删除仪器报告项目信息
     */
    @DeleteMapping("/delete")
    public Object delete(@RequestBody Set<Long> instrumentReportItemIds) {
        if (CollectionUtils.isEmpty(instrumentReportItemIds)) {
            return Collections.emptyMap();
        }

        List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByIds(instrumentReportItemIds);
        if (CollectionUtils.isEmpty(instrumentReportItems)) {
            throw new IllegalStateException("仪器报告项目不存在");
        }
        Map<Long, InstrumentReportItemDto> reportItemById = instrumentReportItems.stream()
                .collect(Collectors.toMap(InstrumentReportItemDto::getInstrumentReportItemId, Function.identity()));

        for (Long instrumentReportItemId : instrumentReportItemIds) {
            instrumentReportItemService.deleteByInstrumentReportItemId(instrumentReportItemId);

            InstrumentReportItemDto instrumentReportItemDto = reportItemById.get(instrumentReportItemId);
            if (Objects.nonNull(instrumentReportItemDto)) {
                rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                        TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM.getDesc())
                                .setContent(String.format("用户 [%s] 删除仪器报告项目 [%s]", LoginUserHandler.get().getNickname(),
                                        instrumentReportItemDto.getReportItemName()))
                                .toJSONString());
            }

        }

        return Collections.emptyMap();
    }

    /**
     * 从主数据新增报告项目
     */
    @PostMapping("/add")
    public Object add(@RequestBody List<ReportItemAddVo> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            throw new IllegalArgumentException("请选择报告项目");
        }

        for (ReportItemAddVo vo : vos) {

            if (StringUtils.isBlank(vo.getReportItemCode()) || Objects.isNull(vo.getInstrumentId())) {
                throw new IllegalArgumentException("参数错误");
            }

            instrumentReportItemService.addReportItem(vo.getInstrumentId(), vo.getReportItemCode());
        }

        return Map.of();
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public Object update(@RequestBody InstrumentReportItemDto instrumentReportItem) {
        if (Objects.isNull(instrumentReportItem.getInstrumentReportItemId())
                || StringUtils.isAnyBlank(instrumentReportItem.getExamMethodCode(),
                instrumentReportItem.getExamMethodName(), instrumentReportItem.getResultTypeName(),
                instrumentReportItem.getResultTypeCode(), instrumentReportItem.getItemTypeCode(),
                instrumentReportItem.getItemTypeName())
                || Objects.isNull(instrumentReportItem.getIsQc()) || Objects.isNull(instrumentReportItem.getIsPrint())
                || Objects.isNull(instrumentReportItem.getIsResultZero())
                || Objects.isNull(instrumentReportItem.getIsResultNull())
                || Objects.isNull(instrumentReportItem.getIsBringOut())) {
            throw new IllegalArgumentException("参数错误");
        }

        if (Objects.equals(instrumentReportItem.getIsBringOut(), YesOrNoEnum.YES.getCode())) {
            if (StringUtils.isBlank(instrumentReportItem.getCalcFomulation())) {
                throw new IllegalArgumentException("自动带出报告项目,计算公式不能为空");
            }
        }

        final InstrumentReportItemDto instrumentReportItemOld = instrumentReportItemService
                .selectByInstrumentReportItemId(instrumentReportItem.getInstrumentReportItemId());
        if (Objects.isNull(instrumentReportItemOld)) {
            throw new IllegalStateException("仪器报告项目不存在");
        }

        if (instrumentReportItemService.updateByInstrumentReportItemId(instrumentReportItem)) {

            // 推送日志
            final var compare =
                    new CompareUtils<InstrumentReportItemDto>().compare(instrumentReportItemOld, instrumentReportItem);
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM.getDesc())
                            .setContent(String.format("修改仪器报告项目: [%s]", compare)).toJSONString());

            return Collections.emptyMap();
        }

        throw new IllegalStateException("修改仪器报告项目失败");
    }

    /**
     * 仪器报告项目检测方法 下拉框
     */
    @PostMapping("/exam-method-list")
    public Object examMethodList() {
        List<DictItemDto> items = instrumentReportItemService.selectAllExamMethod();
        return items.stream().map(m -> Dict.of("dictCode", m.getDictCode(), "dictValue", m.getDictName()))
                .collect(Collectors.toList());
    }

    /**
     * 所有分组下面的所有仪器
     *
     * @return
     */
    @GetMapping("all-group-instruments")
    public Object allGroupInstruments(@RequestParam(required = false) Long instrumentId) {
        return reportItemService.selectInstrumentsExcludeByInstrumentId(instrumentId);
    }

    /**
     * 拷贝仪器报告项目
     *
     * @return
     */
    @PostMapping("report-item-copy")
    public Object reportItemCopy(@RequestBody ReportItemCopyVo vo) {
        Long originInstrumentId = vo.getOriginInstrumentId();
        if (Objects.isNull(originInstrumentId)) {
            throw new IllegalArgumentException("请选择源仪器");
        }
        List<Long> originInstrumentReportItemIds = vo.getOriginInstrumentReportItemIds();
        if (CollectionUtils.isEmpty(originInstrumentReportItemIds)) {
            throw new IllegalArgumentException("请选择报告项目");
        }
        List<Long> targetInstrumentIds = vo.getTargetInstrumentIds();
        if (CollectionUtils.isEmpty(targetInstrumentIds)) {
            throw new IllegalArgumentException("请选择目标仪器");
        }
        return reportItemService.reportItemCopy(originInstrumentId, originInstrumentReportItemIds,
                targetInstrumentIds);
    }

    /**
     * 导出仪器报告项目
     *
     * @return
     */
    @PostMapping("/export-report-item")
    public Object exportReportItem(@RequestBody ExportReportItemDTO exportDto
    ) throws IOException {
        Long groupId = exportDto.getGroupId();
        Long instrumentId = exportDto.getInstrumentId();
        Integer enable = exportDto.getEnable();
        String itemCodeOrName = exportDto.getItemCodeOrName();

        final Map<Long, InstrumentDto> map = new HashMap<>();

        if (Objects.isNull(instrumentId)) {
            map.putAll(instrumentService.selectByOrgId(LoginUserHandler.get().getOrgId()).stream()
                    .collect(Collectors.toMap(InstrumentDto::getInstrumentId, v -> v, (a, b) -> a)));
        } else {
            final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentId);
            Assert.notNull(instrument, "仪器不存在");
            map.put(instrument.getInstrumentId(), instrument);
        }

        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentIds(map.keySet());

        Stream<InstrumentReportItemDto> stream = instrumentReportItems.stream();
        if (enable != null && (enable == 0 || enable == 1)) {
            stream = stream.filter(e -> e.getEnable().equals(enable));
        }
        if (StringUtils.isNotBlank(itemCodeOrName)) {
            stream = stream
                    .filter(e -> e.getReportItemCode().contains(itemCodeOrName)
                            || e.getReportItemName().contains(itemCodeOrName));
        }
        List<InstrumentReportItemDto> reportItemDtoList = stream.collect(Collectors.toList());

        final File tempFile = File.createTempFile("instrument-report-item-export", null);

        try (ExcelWriter writer = ExcelUtil.getBigWriter(); FileOutputStream fos = new FileOutputStream(tempFile)) {
            List<Object> builder = new LinkedList<>(List.of(
                    "专业组",
                    "报告项目编码",
                    "报告项目名称",
                    "仪器编码",
                    "仪器名称",
                    "检测方法",
                    "英文缩写",
                    "是否启用"));
            writer.writeHeadRow(builder);

            for (InstrumentReportItemDto dto : reportItemDtoList) {

                InstrumentDto instrumentDto = map.get(dto.getInstrumentId());
                if (instrumentDto != null) {
                    if (Objects.nonNull(groupId) && !groupId.equals(instrumentDto.getGroupId())) {
                        continue;
                    }
                }

                builder.clear();
                String enable1 = dto.getEnable() == null ? "" : dto.getEnable() == 0 ? "否" : "是";
                builder.add(Optional.ofNullable(map.get(dto.getInstrumentId())).map(InstrumentDto::getGroupName).orElse(StringUtils.EMPTY));
                builder.add(dto.getReportItemCode());
                builder.add(dto.getReportItemName());
                builder.add(dto.getInstrumentCode());
                builder.add(dto.getInstrumentName());
                builder.add(dto.getExamMethodName());
                builder.add(dto.getEnAb());
                builder.add(enable1);

                writer.writeRow(builder);
            }
            writer.flush(fos);
        }
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("仪器报告项目.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(new FileSystemResource(tempFile));
    }

}
