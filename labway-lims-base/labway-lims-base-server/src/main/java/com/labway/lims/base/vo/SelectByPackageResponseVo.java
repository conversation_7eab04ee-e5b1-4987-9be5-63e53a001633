package com.labway.lims.base.vo;

import com.labway.lims.base.api.dto.ReportItemDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 套餐 其 对应 项目
 * 
 * <AUTHOR>
 * @since 2023/3/29 10:39
 */
@Getter
@Setter
public class SelectByPackageResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    private Long packageItemId;
    /**
     * 套餐ID
     */
    private Long packageId;
    /**
     * 套餐名称
     */
    private String packageName;
    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 体检团体ID
     */
    private Long physicalGroupId;
    /**
     * 检验项目ID
     */
    private Long testItemId;
    /**
     * 体检项目编码
     */
    private String testItemCode;
    /**
     * 检验项目名称
     */
    private String testItemName;

    // --------------项目信息-------------------------

    /**
     * 样本类型ID
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;

    /**
     * 管型 code
     */
    private String tubeCode;

    /**
     * 管型 name
     */
    private String tubeName;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    // ------------关联报告项目----------------
    /**
     * 关联报告项目
     */
    private List<ReportItemDto> reportItemList;

}
