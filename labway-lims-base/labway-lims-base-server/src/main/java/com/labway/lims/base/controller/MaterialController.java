package com.labway.lims.base.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.IncrementalSyncMaterialDto;
import com.labway.lims.base.api.dto.MaterialQueryDto;
import com.labway.lims.base.api.dto.MaterialSyncFlowDto;
import com.labway.lims.base.api.service.MaterialService;
import com.labway.lims.base.api.service.MaterialSyncFlowService;
import com.labway.lims.base.vo.MaterialQueryVo;
import com.labway.lims.base.vo.MaterialTypeVo;
import com.labway.lims.base.api.vo.UpdateExpectedTestCountVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/8 17:13
 */
@Slf4j
@RestController
@RequestMapping("/material")
public class MaterialController extends BaseController {
    @Resource
    private MaterialService materialService;
    @Resource
    private MaterialSyncFlowService materialSyncFlowService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 从主数据添加物料数据
     */
    @PostMapping("/sync")
    public Object sync() {

        // 获取 最近 成功 同步流水 记录
        MaterialSyncFlowDto flowDto = materialSyncFlowService.recentFlowRecord();

        // 全量同步
        Date timeStart;
        Date timeEnd = new Date();
        if (Objects.nonNull(flowDto) && Objects.nonNull(flowDto.getModifiedDateEnd())) {
            timeStart = flowDto.getModifiedDateEnd();
        } else {
            timeStart = DefaultDateEnum.DEFAULT_DATE.getDate();
        }

        try {

            IncrementalSyncMaterialDto dto = new IncrementalSyncMaterialDto();
            dto.setModifiedTimeStart(timeStart);
            dto.setModifiedTimeEnd(timeEnd);

            materialService.syncMaterial(dto);

        } catch (Exception e) {
            log.info("同步错误:{}", e.getMessage());

            final MaterialSyncFlowDto dto = new MaterialSyncFlowDto();
            dto.setEnable(YesOrNoEnum.YES.getCode());
            dto.setModifiedDateStart(ObjectUtils.defaultIfNull(timeStart, new Date()));
            dto.setModifiedDateEnd(timeEnd);
            dto.setStatus(YesOrNoEnum.YES.getCode());
            dto.setOperationNumber(0);
            dto.setResultMessage("本次同步报错,错误信息为：" + e.getMessage());
            materialSyncFlowService.addFlowRecord(dto);
        }
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY, TraceLog.newInstance()
                .setModule(TraceLogModuleEnum.MATERIAL_SYNC.getDesc())
                .setContent(String.format(" [%s] 与 [%s] 进行物料信息同步,同步范围: %s - %s", LoginUserHandler.get().getNickname(),
                        DateUtil.formatDateTime(timeEnd), DateUtil.formatDateTime(timeStart), DateUtil.formatDateTime(timeEnd)))
                .toJSONString());
        return Collections.emptyMap();
    }

    @PostMapping("/materials")
    public Object materials(@RequestBody MaterialQueryVo vo) {
        final MaterialQueryDto dto = new MaterialQueryDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setCurrent(ObjectUtils.defaultIfNull(vo.getCurrent(), 1));
        dto.setSize(ObjectUtils.defaultIfNull(vo.getSize(), Integer.MAX_VALUE));
        return materialService.selectByMaterialNameOrName(dto);
    }

    /**
     * 物料类别下拉框
     */
    @PostMapping("/material-types")
    public Object types() {
        return JSON.parseArray(JSON.toJSONString(materialService.getMaterialTypes()), MaterialTypeVo.class);
    }

    /**
     * 更新物料预期测试数
     */
    @PostMapping("/update-expected-test-count")
    public Object updateExpectedTestCount(@RequestBody List<UpdateExpectedTestCountVo> vos) {
        for (UpdateExpectedTestCountVo vo : vos) {
            if (vo.getMaterialId() == null) {
                throw new IllegalArgumentException("物料ID不能为空");
            }
            if (vo.getExpectedTestCount() == null) {
                throw new IllegalArgumentException("预期测试数不能为空");
            }
            if (vo.getExpectedTestCount() <= 0) {
                throw new IllegalArgumentException("预期测试数必须为正整数");
            }
        }
        if (vos.size() > 10000) {
            throw new IllegalArgumentException("修改内容不能超过10000条");
        }
        return materialService.updateExpectedTestCount(vos);
    }
}
