package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.QcBatchDto;
import com.labway.lims.base.api.dto.QcBatchReportItemDto;
import com.labway.lims.base.api.service.QcBatchReportItemService;
import com.labway.lims.base.api.service.QcBatchService;
import com.labway.lims.base.vo.AddQcBatchRequestVo;
import com.labway.lims.base.vo.CloneQcBatchRequestVo;
import com.labway.lims.base.vo.UpdateQcBatchRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/qcBatch")
public class QcBatchController extends BaseController {
    @DubboReference
    private RabbitMQService rabbitMQService;

    @Resource
    private QcBatchService qcBatchService;

    @Resource
    private QcBatchReportItemService qcBatchReportItemService;

    /**
     * 质控 复制
     */
    @PostMapping("/clone-row")
    public Object cloneRow(@RequestBody CloneQcBatchRequestVo clone) {
        final Long qcBatchId = clone.getQcBatchId();
        if (Objects.isNull(qcBatchId)) {
            throw new IllegalArgumentException("请选择质控批号");
        }
        // 检查 参数
        checkVoWhenAddOrUpdate(clone);

        QcBatchDto qcBatchDto = qcBatchService.selectByQcBatchId(qcBatchId);
        if (Objects.isNull(qcBatchDto)) {
            throw new LimsException("无效质控批号");
        }
        // 对应质控报告项目
        List<QcBatchReportItemDto> qcBatchReportItemDtos = qcBatchReportItemService.selectByQcBatchId(qcBatchId);
        QcBatchDto cloneDto = JSON.parseObject(JSON.toJSONString(clone), QcBatchDto.class);

        qcBatchService.cloneRow(qcBatchDto, qcBatchReportItemDtos, cloneDto);

        return Map.of();
    }

    @PostMapping("/delete-qc-batch")
    public Object deleteQcBatch(@RequestBody Set<Long> qcBatchIds) {
        if (CollectionUtils.isEmpty(qcBatchIds)) {
            throw new IllegalArgumentException("请选择质控批号");
        }

        qcBatchService.deleteQcBatch(qcBatchIds);

        return Map.of();
    }

    /**
     * 质控批号列表
     */
    @GetMapping("/list")
    public Object list(@RequestParam(required = false) String instrumentCode,
        @RequestParam(required = false) String qcBatch) {
        return qcBatchService.list(instrumentCode, qcBatch);
    }

    /**
     * 修改质控品
     */
    @PostMapping("/update")
    public Object update(@RequestBody UpdateQcBatchRequestVo vo) {
        final Long qcBatchId = vo.getQcBatchId();
        if (Objects.isNull(qcBatchId)) {
            throw new IllegalArgumentException("请选择质控批号");
        }
        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        QcBatchDto qcBatchDto = JSON.parseObject(JSON.toJSONString(vo), QcBatchDto.class);

        qcBatchService.update(qcBatchDto);
        return Map.of();
    }

    /**
     * 添加质控品
     */
    @PostMapping("/add")
    public Object add(@RequestBody AddQcBatchRequestVo vo) {

        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        QcBatchDto qcBatchDto = JSON.parseObject(JSON.toJSONString(vo), QcBatchDto.class);

        return Map.of("id", qcBatchService.add(qcBatchDto));
    }

    private <T extends AddQcBatchRequestVo> void checkVoWhenAddOrUpdate(T vo) {

        final String instrumentCode = vo.getInstrumentCode();
        if (StringUtils.isBlank(instrumentCode)) {
            throw new IllegalArgumentException("请选择仪器");
        }

        final String qcBatch = vo.getQcBatch();
        if (StringUtils.isBlank(qcBatch) || StringUtils.length(qcBatch) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("质控品批号不能为空或超过 %s 字符", INPUT_MAX_LENGTH));
        }

        final String qcMatieralName = vo.getQcMatieralName();
        if (StringUtils.length(qcMatieralName) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("质控品名称不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        final String reagentBrand = vo.getReagentBrand();
        if (StringUtils.length(reagentBrand) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("试剂品牌不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        final String source = vo.getSource();
        if (StringUtils.length(source) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("来源不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        final Date beginDate = vo.getBeginDate();
        final Date endDate = vo.getEndDate();
        if (Objects.isNull(beginDate) || Objects.isNull(endDate)) {
            throw new IllegalArgumentException("生效时间和失效时间不能为空");
        }

        if (Objects.equals(beginDate, endDate)) {
            throw new IllegalArgumentException("生效时间和失效时间不能相同");
        }

        if (beginDate.after(endDate)) {
            throw new IllegalArgumentException("生效时间不能大于失效时间");
        }
    }
}
