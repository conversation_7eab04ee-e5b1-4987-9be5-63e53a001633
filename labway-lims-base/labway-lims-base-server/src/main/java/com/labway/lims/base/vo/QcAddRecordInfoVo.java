package com.labway.lims.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


@Data
public class QcAddRecordInfoVo {

    /**
     * 记录批次
     */
    private String qcRecordBatch;

    /**
     * 质控开始日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime qcStartDate;

    /**
     * 质控结束日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime qcEndDate;

    /**
     * 质控规则集合
     */
    private String qcRulesCollection;

    /**
     * 器编码ID
     */
    private Long instrumentId;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 报告ID
     */
    private Long reportId;
    /**
     * 0定性 1半定性
     * 定性类型
     */
    private Integer qualitativeType;

    /**
     * 上控制线
     */
    private BigDecimal upperControlLimit;

    /**
     * 下控制线
     */
    private BigDecimal lowerControlLimit;

    /**
     * 保存记录的对象
     */
    private List<QcAddSetRecordItemVo> recordList;
}
