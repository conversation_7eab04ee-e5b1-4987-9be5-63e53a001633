package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.HspOrgDepositGuaranteeDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrgDepositGuaranteeService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.vo.AddHspOrgDepositGuaranteeBatchVo;
import com.labway.lims.base.mapper.TbHspOrgDepositGuaranteeMapper;
import com.labway.lims.base.model.TbHspOrgDepositGuarantee;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 机构保底金维护Service实现
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "hsp-org-deposit-guarantee")
public class HspOrgDepositGuaranteeServiceImpl extends ServiceImpl<TbHspOrgDepositGuaranteeMapper, TbHspOrgDepositGuarantee> implements HspOrgDepositGuaranteeService {

    @Resource
    private TbHspOrgDepositGuaranteeMapper tbOrgDepositGuaranteeMapper;

    @Resource
    private SnowflakeService snowflakeService;

    @Resource
    private HspOrganizationService hspOrganizationService;

    /**
     * 检查日期范围是否有交集
     *
     * @param hspOrgId 送检机构编码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param excludeId 排除的ID（用于修改时排除自身）
     */
    private void checkDateRangeOverlap(Long hspOrgId, Date startDate, Date endDate, Long excludeId) {
        // 获取所有该机构的保底金记录
        List<TbHspOrgDepositGuarantee> allRecords = tbOrgDepositGuaranteeMapper.selectList(
                Wrappers.lambdaQuery(TbHspOrgDepositGuarantee.class)
                        .eq(TbHspOrgDepositGuarantee::getHspOrgId, hspOrgId)
                        .eq(TbHspOrgDepositGuarantee::getIsDelete, YesOrNoEnum.NO.getCode()));
        
        // 在内存中筛选有日期交集的记录
        List<TbHspOrgDepositGuarantee> overlappingRecords = allRecords.stream()
                .filter(record -> {
                    // 排除自身ID
                    if (Objects.nonNull(excludeId) && 
                            Objects.equals(record.getHspOrgDepositGuaranteeId(), excludeId)) {
                        return false;
                    }
                    
                    // 检查日期范围是否有交集
                    // 条件：(startDate <= 记录的endDate) AND (endDate >= 记录的startDate)
                    return startDate.compareTo(record.getEndDate()) <= 0 && 
                           endDate.compareTo(record.getStartDate()) >= 0;
                })
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(overlappingRecords)) {
            TbHspOrgDepositGuarantee record = overlappingRecords.get(0);
            String startDateStr = DateFormatUtils.format(record.getStartDate(), "yyyy-MM-dd");
            String endDateStr = DateFormatUtils.format(record.getEndDate(), "yyyy-MM-dd");
            throw new IllegalStateException(
                    String.format("%s机构生效日期已存在（%s 至 %s），请修改后重试",
                            record.getHspOrgName(), startDateStr, endDateStr));
        }
    }

    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int addHspOrgDepositGuaranteeBatch(AddHspOrgDepositGuaranteeBatchVo vo) {
        List<Long> hspOrgIds = vo.getHspOrgIds();
        Date startDate = vo.getStartDate();
        Date endDate = vo.getEndDate();
        BigDecimal guaranteeAmount = vo.getGuaranteeAmount();

        if (CollectionUtils.isEmpty(hspOrgIds)) {
            throw new IllegalArgumentException("送检机构编码不能为空");
        }

        // 查询送检机构信息
        final List<HspOrganizationDto> hspOrganizations = hspOrganizationService.selectByHspOrgIds(hspOrgIds);
        if (CollectionUtils.isEmpty(hspOrganizations) || hspOrganizations.size() != hspOrgIds.size()) {
            throw new IllegalArgumentException("部分送检机构不存在");
        }

        // 检查每个机构是否已存在日期交集
        Map<Long, HspOrganizationDto> hspOrgMap = hspOrganizations.stream()
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity()));

        for (Long hspOrgId :hspOrgMap.keySet()) {
            try {
                checkDateRangeOverlap(hspOrgId, startDate, endDate, null);
            } catch (IllegalStateException e) {
                // 捕获异常并重新抛出，添加更多上下文信息
                throw new IllegalStateException(e.getMessage());
            }
        }

        // 生成ID
        final LinkedList<Long> ids = snowflakeService.genIds(hspOrgMap.size());

        // 批量添加
        final LoginUserHandler.User user = LoginUserHandler.get();
        final Date now = new Date();
        List<TbHspOrgDepositGuarantee> insertList = hspOrgMap.entrySet().stream().map(e -> {
            final TbHspOrgDepositGuarantee add = new TbHspOrgDepositGuarantee();
            add.setHspOrgDepositGuaranteeId(ids.pop());
            add.setHspOrgId(e.getKey());
            add.setHspOrgCode(e.getValue().getHspOrgCode());
            add.setHspOrgName(e.getValue().getHspOrgName());
            add.setStartDate(startDate);
            add.setEndDate(endDate);
            add.setGuaranteeAmount(guaranteeAmount);
            add.setCreateDate(now);
            add.setCreatorId(user.getUserId());
            add.setCreatorName(user.getNickname());
            add.setUpdateDate(now);
            add.setUpdaterId(user.getUserId());
            add.setUpdaterName(user.getNickname());
            add.setIsDelete(YesOrNoEnum.NO.getCode());
            return add;
        }).collect(Collectors.toList());

        super.saveBatch(insertList);

        return insertList.size();
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectById:' + #orgDepositGuaranteeId")
    public HspOrgDepositGuaranteeDto selectById(long orgDepositGuaranteeId) {
        final TbHspOrgDepositGuarantee one = tbOrgDepositGuaranteeMapper.selectById(orgDepositGuaranteeId);
        return convert(one);
    }
    
    @Override
    public List<HspOrgDepositGuaranteeDto> selectByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbHspOrgDepositGuarantee> in = Wrappers.lambdaQuery(TbHspOrgDepositGuarantee.class)
                .in(TbHspOrgDepositGuarantee::getHspOrgDepositGuaranteeId, ids)
                .eq(TbHspOrgDepositGuarantee::getIsDelete, YesOrNoEnum.NO.getCode());
        final List<TbHspOrgDepositGuarantee> list = tbOrgDepositGuaranteeMapper.selectList(in);

        return convert(list);
    }

    @Nonnull
    @Override
    @Cacheable(key = "'selectAll:' + #hspOrgName")
    public List<HspOrgDepositGuaranteeDto> selectAll(String hspOrgName) {
        log.info("执行机构保底金查询，原始参数: [{}]", hspOrgName);

        final LambdaQueryWrapper<TbHspOrgDepositGuarantee> query = Wrappers.lambdaQuery(TbHspOrgDepositGuarantee.class)
                .eq(TbHspOrgDepositGuarantee::getIsDelete, YesOrNoEnum.NO.getCode())
                .orderByDesc(TbHspOrgDepositGuarantee::getCreateDate, TbHspOrgDepositGuarantee::getHspOrgId);

        // 如果提供了送检机构名称，添加模糊查询条件
        if (StringUtils.isNotBlank(hspOrgName)) {
            // 检查参数是否包含逗号
            if (hspOrgName.contains(",")) {
                log.warn("机构名称参数包含逗号: [{}]，这可能导致查询问题", hspOrgName);
            }

            // 直接使用参数进行模糊查询，不添加任何额外字符
            query.like(TbHspOrgDepositGuarantee::getHspOrgName, hspOrgName);
            log.info("机构保底金查询条件: hspOrgName like '%{}%'", hspOrgName);
        }

        final List<TbHspOrgDepositGuarantee> list = tbOrgDepositGuaranteeMapper.selectList(query);
        log.info("机构保底金查询结果数量: {}", list != null ? list.size() : 0);

        return convert(list);
    }

    @Override
    @CacheEvict(allEntries = true)
    public void updateById(HspOrgDepositGuaranteeDto dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getHspOrgDepositGuaranteeId())) {
            return;
        }

        // 检查日期范围是否有交集，排除自身ID
        checkDateRangeOverlap(dto.getHspOrgId(), dto.getStartDate(), dto.getEndDate(), dto.getHspOrgDepositGuaranteeId());

        final LoginUserHandler.User user = LoginUserHandler.get();

        final TbHspOrgDepositGuarantee update = JSON.parseObject(JSON.toJSONString(dto), TbHspOrgDepositGuarantee.class);
        update.setUpdaterId(user.getUserId());
        update.setUpdaterName(user.getNickname());
        update.setUpdateDate(new Date());

        tbOrgDepositGuaranteeMapper.updateById(update);
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        if (tbOrgDepositGuaranteeMapper.deleteBatchIds(ids) < 1) {
            throw new IllegalStateException("删除机构保底金额维护失败");
        }
    }


    @Override
    public List<HspOrgDepositGuaranteeDto> selectByDate(@Nonnull Date startDate, @Nonnull Date endDate) {
        return convert(tbOrgDepositGuaranteeMapper.selectList(Wrappers.lambdaQuery(TbHspOrgDepositGuarantee.class)
                .ge(TbHspOrgDepositGuarantee::getStartDate, startDate)
                .le(TbHspOrgDepositGuarantee::getEndDate, endDate)));
    }

    private HspOrgDepositGuaranteeDto convert(TbHspOrgDepositGuarantee tb) {
        return JSON.parseObject(JSON.toJSONString(tb), HspOrgDepositGuaranteeDto.class);
    }

    private List<HspOrgDepositGuaranteeDto> convert(List<TbHspOrgDepositGuarantee> list) {
        return JSON.parseArray(JSON.toJSONString(list), HspOrgDepositGuaranteeDto.class);
    }
} 