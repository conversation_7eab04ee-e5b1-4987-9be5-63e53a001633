package com.labway.lims.base.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * 服务 暴露 接口 http 不需要用户
 * 
 * <AUTHOR>
 * @since 2023/9/27 16:17
 */
@Slf4j
@RestController
@RequestMapping("/export-api")
public class ExportApiController extends BaseController {
    @Resource
    private GroupService groupService;
    @Resource
    private GermService germService;
    @Resource
    private MedicineService medicineService;
    @Resource
    private ReportItemService reportItemService;
    @Resource
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;

    /**
     * 查询机构所有的专业组
     */
    @PostMapping("/groups")
    public Object groups(@RequestParam long orgId) {
        return groupService.selectByOrgId(orgId);
    }

    /**
     * 细菌 获取 所有 查看
     */
    @PostMapping("/germs")
    public Object germList(@RequestParam long orgId) {
        return germService.selectByOrgId(orgId);
    }

    /**
     * 药物 获取 所有 查看
     */
    @PostMapping("/medicines")
    public Object medicineList(@RequestParam long orgId) {
        return medicineService.selectByOrgId(orgId);
    }

    /**
     * 获取所有的报告项目
     */
    @PostMapping("/reportItems")
    public Object reportItemList(@RequestParam long orgId) {
        return reportItemService.selectByOrgId(orgId);
    }

    /**
     * 查询仪器报告项目参考范围
     */
    @PostMapping("selectByInstrumentReportItemReferenceIds")
    public Object selectByInstrumentReportItemReferenceIds(@RequestBody Collection<Long> instrumentReportItemReferenceIds) {
        return instrumentReportItemReferenceService.selectByInstrumentReportItemReferenceIds(instrumentReportItemReferenceIds);
    }

}
