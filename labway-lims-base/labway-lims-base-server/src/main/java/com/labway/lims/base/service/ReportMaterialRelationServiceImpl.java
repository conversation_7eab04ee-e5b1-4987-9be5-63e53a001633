package com.labway.lims.base.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.base.api.dto.MaterialDto;
import com.labway.lims.base.api.dto.MaterialRelationDto;
import com.labway.lims.base.api.dto.ReportMaterialRelationDto;
import com.labway.lims.base.api.service.MaterialService;
import com.labway.lims.base.api.service.ReportMaterialRelationService;
import com.labway.lims.base.mapper.ReportMaterialRelationMapper;
import com.labway.lims.base.model.TbReportMaterialRelation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 报告物料关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11 14:16
 */
@Service
@DubboService
public class ReportMaterialRelationServiceImpl implements ReportMaterialRelationService {

    @Resource
    private ReportMaterialRelationMapper reportMaterialRelationMapper;
    
    @Resource
    private MaterialService materialService;

    /**
     * 批量保存报告物料关联
     *
     * @param reportMaterialRelationDtos 报告物料关联DTO列表
     * @return 保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "reportItemMappedCache", allEntries = true)
    public boolean batchSaves(List<ReportMaterialRelationDto> reportMaterialRelationDtos) {
        if (CollectionUtils.isEmpty(reportMaterialRelationDtos)) {
            return false;
        }
        return reportMaterialRelationMapper.batchInsert(reportMaterialRelationDtos) > 0;
    }
    
    /**
     * 批量删除报告物料关联
     *
     * @param reportMaterialRelationIds 报告物料关联ID集合
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "reportItemMappedCache", allEntries = true)
    public boolean batchDelete(Set<Long> reportMaterialRelationIds) {
        if (CollectionUtils.isEmpty(reportMaterialRelationIds)) {
            return false;
        }
        return reportMaterialRelationMapper.deleteBatchIds(reportMaterialRelationIds) > 0;
    }
    


    /**
     * 根据报告项目编码和专业组ID查询关联的物资信息
     *
     * @param reportItemCode 报告项目编码
     * @param groupId 专业组ID
     * @return 物资信息列表，包含关联ID
     */
    @Override
    public List<MaterialRelationDto> selectMaterialsByReportItemCodeAndGroupId(String reportItemCode, Long groupId) {
        // 使用MyBatis-Plus查询报告物料关联记录
        List<TbReportMaterialRelation> relations = reportMaterialRelationMapper.selectList(
                Wrappers.lambdaQuery(TbReportMaterialRelation.class)
                        .eq(TbReportMaterialRelation::getReportItemCode, reportItemCode)
                        .eq(groupId != null, TbReportMaterialRelation::getGroupId, groupId)
                        .eq(TbReportMaterialRelation::getIsDelete, YesOrNoEnum.NO.getCode()));
        
        if (CollectionUtils.isEmpty(relations)) {
            return Collections.emptyList();
        }
        
        // 提取物资编码列表
        List<String> materialCodes = relations.stream()
                .map(TbReportMaterialRelation::getMaterialCode)
                .collect(Collectors.toList());
        
        // 根据物资编码列表查询物资详细信息
        List<MaterialDto> materials = materialService.selectByOrgIdAndMaterialCodes(
                LoginUserHandler.get().getOrgId(), materialCodes);
        
        // 创建物资编码到关联ID的映射
        Map<String, Long> codeToIdMap = relations.stream()
                .collect(Collectors.toMap(
                        TbReportMaterialRelation::getMaterialCode,
                        TbReportMaterialRelation::getReportMaterialRelationId,
                        (a, b) -> a // 如果有重复键，保留第一个
                ));
        
        // 将物资信息和关联ID组合成MaterialRelationDto
        return materials.stream().map(material -> {
            MaterialRelationDto relationDto = new MaterialRelationDto();
            BeanUtils.copyProperties(material, relationDto);
            relationDto.setReportMaterialRelationId(codeToIdMap.get(material.getMaterialCode()));
            return relationDto;
        }).collect(Collectors.toList());
    }


    

    
    /**
     * 查询所有未删除的报告物料关联记录
     *
     * @return 报告物料关联DTO列表
     */
    @Override
    public List<ReportMaterialRelationDto> selectAll() {
        return reportMaterialRelationMapper.selectAll();
    }

    /**
     * 根据报告项目编码集合和专业组名称集合查询关联记录
     *
     * @param reportItemCodes 报告项目编码集合
     * @param groupNames 专业组名称集合
     * @return 报告物料关联DTO列表
     */
    @Override
    public List<ReportMaterialRelationDto> selectByReportItemCodesAndGroupNames(Set<String> reportItemCodes, Set<String> groupNames) {
        if (CollectionUtils.isEmpty(reportItemCodes) || CollectionUtils.isEmpty(groupNames)) {
            return Collections.emptyList();
        }
        return reportMaterialRelationMapper.selectByReportItemCodesAndGroupNames(reportItemCodes, groupNames);
    }

    /**
     * 获取所有已对照的项目编码和专业组名称组合
     *
     * @return 已对照的组合键集合
     */
    @Override
    @Cacheable(value = "reportItemMappedCache", key = "'all_mapped_items'")
    public Set<String> getAllMappedItemGroupKeys() {
        return reportMaterialRelationMapper.selectList(Wrappers.lambdaQuery(TbReportMaterialRelation.class).select(TbReportMaterialRelation::getReportItemCode,TbReportMaterialRelation::getGroupId))
                .stream().map(item -> item.getReportItemCode() + "_" + item.getGroupId()).collect(Collectors.toSet());

    }

    /**
     * 批量更新报告物料关联
     *
     * @param reportMaterialRelationDtos 报告物料关联DTO列表
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "reportItemMappedCache", allEntries = true)
    public boolean batchUpdates(List<ReportMaterialRelationDto> reportMaterialRelationDtos) {
        if (CollectionUtils.isEmpty(reportMaterialRelationDtos)) {
            return false;
        }
        return reportMaterialRelationMapper.batchUpdate(reportMaterialRelationDtos) > 0;
    }
} 