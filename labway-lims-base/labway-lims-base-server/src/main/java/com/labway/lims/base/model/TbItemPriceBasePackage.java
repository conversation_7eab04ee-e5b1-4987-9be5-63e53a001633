package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目价格基准包
 */
@Getter
@Setter
@TableName("tb_item_price_base_package")
public class TbItemPriceBasePackage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long packageId;

    /**
     * 基准包名称
     */
    private String packageName;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 是否启用
     */
    private Integer enable;

    /**
     * 1:删除,0:未删
     */
    private Integer isDelete;
}
