package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.finance.FinanceLockEnum;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.FinanceOrgLockDto;
import com.labway.lims.base.api.dto.FinanceSampleLockDto;
import com.labway.lims.base.api.dto.OrgLockDto;
import com.labway.lims.base.api.service.FinanceOrgLockService;
import com.labway.lims.base.api.service.FinanceSampleLockService;
import com.labway.lims.base.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/12 14:08
 */
@RestController
@RequestMapping("/sample-lock")
public class FinanceSampleLockController extends BarcodeController {

    @Resource
    private FinanceSampleLockService financeSampleLockService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private FinanceOrgLockService financeOrgLockService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @PostMapping("/list")
    public Object getLockSamples(@RequestBody SampleLockQueryVo vo) throws ExecutionException, InterruptedException, TimeoutException {

        if (Objects.isNull(vo.getEnterDateStart()) || Objects.isNull(vo.getEnterateEnd())) {
            throw new IllegalArgumentException("请选择生效日期或结束日期");
        }

        if (vo.getEnterateEnd().before(vo.getEnterDateStart())) {
            throw new IllegalArgumentException("结束日期不能早于生效日期");
        }

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        query.setIsAudit(YesOrNoEnum.YES.getCode());
        query.setStartCreateDate(vo.getEnterDateStart());
        query.setEndCreateDate(vo.getEnterateEnd());
        if (Objects.nonNull(vo.getHspOrgId())) {
            query.setHspOrgIds(Collections.singleton(vo.getHspOrgId()));
        }

        final List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(query);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }
        final Future<Map<Long, List<SampleFlowDto>>> flowMap = threadPoolConfig.getPool().submit(() ->
                sampleFlowService.selectWithOutContentByApplySampleIdsAsMap(samples.stream()
                        .map(BaseSampleEsModelDto::getApplySampleId).collect(Collectors.toList()))
        );

        final Future<Map<Long, List<FinanceSampleLockDto>>> sampleLockMap = threadPoolConfig.getPool().submit(() ->
                financeSampleLockService.selectByApplySampleIdsAsMap(samples.stream()
                        .map(BaseSampleEsModelDto::getApplySampleId).collect(Collectors.toSet()))
        );

        final LinkedList<SampleLockVo> vos = new LinkedList<>();
        for (BaseSampleEsModelDto sample : samples) {

            //查询样本加解锁操作记录
            final SampleLockVo lockVo = new SampleLockVo();
            final Map<Long, List<FinanceSampleLockDto>> map = sampleLockMap.get(10, TimeUnit.SECONDS);
            final List<FinanceSampleLockDto> sampleLocks = map.get(sample.getApplySampleId());
            if (Objects.isNull(sampleLocks)) {
                lockVo.setStatus(FinanceLockEnum.UN_OPERATE.getCode());
            } else {
                //找出最新的记录
                final FinanceSampleLockDto sampleLock = sampleLocks.stream()
                        .sorted((o1, o2) -> o2.getSampleLockRecordId().compareTo(o1.getSampleLockRecordId()))
                        .iterator().next();
                lockVo.setStatus(sampleLock.getStatus());
            }

            lockVo.setApplySampleId(sample.getApplySampleId());
            lockVo.setHspOrgId(sample.getHspOrgId());
            lockVo.setHspOrgName(sample.getHspOrgName());
            lockVo.setBarcode(sample.getBarcode());
            lockVo.setPatientName(sample.getPatientName());
            lockVo.setPatientAge(sample.getPatientAge());
            lockVo.setPatientSubage(sample.getPatientSubage());
            lockVo.setPatientSubageUnit(sample.getPatientSubageUnit());
            lockVo.setPatientSex(sample.getPatientSex());
            lockVo.setApplyTypeCode(sample.getApplyTypeCode());
            lockVo.setApplyTypeName(sample.getApplyTypeName());
            lockVo.setDept(sample.getDept());
            lockVo.setPatientBed(sample.getPatientBed());
            lockVo.setDiagnosis(sample.getDiagnosis());
            lockVo.setSendDoctorCode(sample.getSendDoctorCode());
            lockVo.setSendDoctorName(sample.getSendDoctorName());
            lockVo.setPatientVisitCard(sample.getPatientVisitCard());

            final Map<Long, List<SampleFlowDto>> longListMap = flowMap.get(10, TimeUnit.SECONDS);
            final List<SampleFlowDto> sampleFlows = longListMap.get(sample.getApplySampleId());
            if (Objects.isNull(sampleFlows)) {
                lockVo.setCurrentLink("");
            } else {
                final SampleFlowDto sampleFlowDto = sampleFlows.stream()
                        .max(Comparator.comparing(SampleFlowDto::getSampleFlowId))
                        .orElse(new SampleFlowDto());
                lockVo.setCurrentLink(sampleFlowDto.getOperateName());
            }
            lockVo.setEnterDate(sample.getCreateDate());
            lockVo.setEnterPeople(sample.getCreatorName());
            lockVo.setEnterPeople(sample.getCreatorName());
            lockVo.setSampleTypeName(sample.getSampleTypeName());
            lockVo.setSampleTypeCode(sample.getSampleTypeCode());
            vos.add(lockVo);
        }
        //时间倒序排列
        vos.sort((o1, o2) -> {
            if (Objects.isNull(o1.getEnterDate()) || Objects.isNull(o2.getEnterDate())) {
                return NumberUtils.INTEGER_ZERO;
            }
            return o2.getEnterDate().compareTo(o1.getEnterDate());
        });
        return vos;
    }

    /**
     * 操作记录
     */
    @PostMapping("/lock-details")
    public Object getLockDetails(@RequestParam("applySampleId") Long applySampleId) {
        final List<FinanceSampleLockDto> sampleLockDtos = financeSampleLockService.selectByApplySampleId(applySampleId);
        final List<FinanceSampleLockRecordVo> recordVos = new LinkedList<>();
        for (FinanceSampleLockDto fsl : sampleLockDtos) {
            final FinanceSampleLockRecordVo vo = JSON.parseObject(JSON.toJSONString(fsl), FinanceSampleLockRecordVo.class);
            vo.setOperator(fsl.getCreatorName());
            vo.setOperDate(fsl.getCreateDate());
            recordVos.add(vo);
        }
        return recordVos;
    }

    @PostMapping("/lock")
    public Object lock(@RequestBody SampleLockAddVo vo) {

        if (CollectionUtils.isEmpty(vo.getApplySampleIds())) {
            throw new IllegalArgumentException("请选择样本");
        }

        final String key = redisPrefix.getBasePrefix() + FinanceSampleLockController.class.getSimpleName() + ":" + getClass().getSimpleName() + ":SyncSampleLock";
        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofMinutes(5)))) {
            throw new IllegalArgumentException(String.format("用户 [%s] 正在进行样本加解锁,请稍后重试", LoginUserHandler.get().getNickname()));
        }

        try {

            final SampleEsQuery query = new SampleEsQuery();
            query.setPageSize(Integer.MAX_VALUE);
            query.setPageNo(1);
            query.setIsAudit(YesOrNoEnum.YES.getCode());
            query.setApplySampleIds(new HashSet<>(vo.getApplySampleIds()));

            final List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(query);

            if (CollectionUtils.isEmpty(samples)) {
                throw new IllegalArgumentException("样本不存在");
            }

            final Set<Long> hspOrgIds = samples.stream()
                    .map(BaseSampleEsModelDto::getHspOrgId).collect(Collectors.toSet());
            //添加所有机构
            hspOrgIds.add(NumberUtils.LONG_ZERO);
            final List<FinanceOrgLockDto> lockDtos = financeOrgLockService.selectByHspOrgIds(hspOrgIds);

            final List<String> orgKeys = new LinkedList<>();
            for (FinanceOrgLockDto fol : lockDtos) {
                orgKeys.add(financeOrgLockService.getOrgLockKey(fol.getHspOrgId(), fol.getStartDate().getTime(), fol.getEndDate().getTime()));
            }

            //判断样本是进行过机构加解锁
            for (BaseSampleEsModelDto sample : samples) {
                final List<String> orgLockValues = stringRedisTemplate.opsForValue().multiGet(orgKeys);
                if (Objects.nonNull(orgLockValues)) {
                    for (String v : orgLockValues) {
                        final OrgLockDto ol = JSON.parseObject(StringUtils.defaultString(v, "{}"), OrgLockDto.class);

                        if (sample.getFinalCheckDate().getTime() < ol.getEndTime()
                                && sample.getFinalCheckDate().getTime() > ol.getStartTime()) {
                            throw new IllegalStateException(String.format("样本 [%s] 已机构加锁", sample.getBarcode()));
                        }
                    }
                }
            }

            final List<FinanceSampleLockDto> sampleLocks = new LinkedList<>();

            for (BaseSampleEsModelDto sample : samples) {
                final FinanceSampleLockDto dto = new FinanceSampleLockDto();
                dto.setApplySampleId(sample.getApplySampleId());
                dto.setStatus(vo.getStatus());
                dto.setReason(vo.getReason());
                dto.setSampleContent("");
                dto.setHspOrgId(sample.getHspOrgId());
                dto.setHspOrgName(sample.getHspOrgName());
                dto.setOrgName(LoginUserHandler.get().getOrgName());
                dto.setOrgId(LoginUserHandler.get().getOrgId());
                sampleLocks.add(dto);
            }

            financeSampleLockService.addBatch(sampleLocks);

            //redis 添加标记
            final LinkedHashMap<String, String> keys = new LinkedHashMap<>();
            if (Objects.equals(vo.getStatus(), FinanceLockEnum.LOCK.getCode())) {
                for (Long applySampleId : vo.getApplySampleIds()) {
                    final String sampleLockKey = financeSampleLockService.getSampleLockKey(applySampleId);
                    keys.put(sampleLockKey, FinanceLockEnum.LOCK.name());
                }
            }
            stringRedisTemplate.opsForValue().multiSet(keys);

            final RedisSerializer<String> serializer = stringRedisTemplate.getStringSerializer();
            //批量设置过期时间1年
            stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (String k : keys.keySet()) {
                    final byte[] serialize = serializer.serialize(k);
                    if (Objects.nonNull(serialize)) {
                        connection.expire(serialize, 60 * 60 * 24 * 365);
                    }
                }
                return null;
            }, serializer);

        } finally {
            stringRedisTemplate.delete(key);
        }

        return Map.of();
    }

    @PostMapping("/un-lock")
    public Object unLock(@RequestBody SampleLockUpdateVo vo) {

        if (CollectionUtils.isEmpty(vo.getApplySampleIds())) {
            throw new IllegalArgumentException("请选择样本");
        }
        final String key = redisPrefix.getBasePrefix() + FinanceSampleLockController.class.getSimpleName() + ":" + getClass().getSimpleName() + ":SyncSampleUnLock";

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofMinutes(5)))) {
            throw new IllegalArgumentException(String.format("用户 [%s] 正在进行样本加解锁,请稍后重试", LoginUserHandler.get().getNickname()));
        }

        try {

            final SampleEsQuery query = new SampleEsQuery();
            query.setPageSize(Integer.MAX_VALUE);
            query.setPageNo(1);
            query.setIsAudit(YesOrNoEnum.YES.getCode());
            query.setApplySampleIds(new HashSet<>(vo.getApplySampleIds()));
            //查询es
            final List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(query);

            if (samples.size() != vo.getApplySampleIds().size()) {
                throw new IllegalArgumentException("样本不存在");
            }
            final Map<Long, List<FinanceSampleLockDto>> sampleLockMap = financeSampleLockService.selectByApplySampleIdsAsMap(vo.getApplySampleIds());

            for (BaseSampleEsModelDto sample : samples) {
                final List<FinanceSampleLockDto> sampleLocks = sampleLockMap.get(sample.getApplySampleId());
                if (Objects.nonNull(sampleLocks)) {
                    sampleLocks.sort((o1, o2) -> o2.getSampleLockRecordId().compareTo(o1.getSampleLockRecordId()));
                    if (!Objects.equals(sampleLocks.iterator().next().getStatus(), FinanceLockEnum.LOCK.getCode())) {
                        throw new IllegalStateException(String.format("样本 [%s] 不是加锁状态", sample.getBarcode()));
                    }
                }
            }

            final LinkedList<FinanceSampleLockDto> dtos = new LinkedList<>();
            for (BaseSampleEsModelDto sample : samples) {
                final FinanceSampleLockDto dto = new FinanceSampleLockDto();
                dto.setApplySampleId(sample.getApplySampleId());
                dto.setStatus(vo.getStatus());
                dto.setReason(vo.getReason());
                dto.setSampleContent("");
                dto.setHspOrgId(sample.getHspOrgId());
                dto.setHspOrgName(sample.getHspOrgName());
                dto.setOrgName(LoginUserHandler.get().getOrgName());
                dto.setOrgId(LoginUserHandler.get().getOrgId());
                dtos.add(dto);
            }

            financeSampleLockService.addBatch(dtos);

            //redis 标记
            if (Objects.equals(vo.getStatus(), FinanceLockEnum.UN_LOCK.getCode())) {
                final LinkedHashMap<String, String> keys = new LinkedHashMap<>();

                for (Long applySampleId : vo.getApplySampleIds()) {
                    final String sampleLockKey = financeSampleLockService.getSampleLockKey(applySampleId);
                    keys.put(sampleLockKey, FinanceLockEnum.UN_LOCK.name());
                }
                stringRedisTemplate.opsForValue().multiSet(keys);

            }

        } finally {
            stringRedisTemplate.delete(key);
        }

        return Map.of();
    }


}

