package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.MaterialDto;
import com.labway.lims.base.model.TbMaterial;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/9 18:02
 */
@Mapper
public interface TbMaterialMapper extends BaseMapper<TbMaterial> {
    void addBatch(@Param("materials") List<MaterialDto> materials);

    void updateMaterialsByInfoHash(@Param("materials") List<MaterialDto> materials);
}
