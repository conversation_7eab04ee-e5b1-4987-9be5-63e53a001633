package com.labway.lims.base.controller;

import com.labway.lims.api.BarcodeUtils;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.web.BaseController;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 条码
 */
@Slf4j
@Getter
@Setter
@RestController
@RequestMapping("/barcode")
public class BarcodeController extends BaseController {


    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private BarcodeUtils barcodeUtils;
    @Resource
    private RedisPrefix redisPrefix;

    /**
     * 生成预制条码
     */
    @PostMapping("/generate")
    public Object generate(Integer count) {
        if (Objects.isNull(count)) {
            throw new IllegalStateException("请输入需要打印条码的数量，单次最高 10000 个");
        }

        if (count > 10000) {
            throw new IllegalStateException("单次最高 10000 个");
        }


        final String lock = redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":generate";

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(lock, StringUtils.EMPTY, 1, TimeUnit.MINUTES))) {
            throw new IllegalStateException("正在生成条码，请稍后再试");
        }

        try {
            return barcodeUtils.genBarcode(count);
        } finally {
            stringRedisTemplate.delete(lock);
        }
    }


}
