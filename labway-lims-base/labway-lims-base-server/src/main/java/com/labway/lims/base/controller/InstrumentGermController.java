package com.labway.lims.base.controller;

import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.GermDto;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGermDto;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.base.api.service.InstrumentGermService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.vo.InstrumentGermResponseVo;
import com.labway.lims.base.vo.UpdateInstrumentGermRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仪器细菌维护API
 *
 * <AUTHOR>
 * @since 2023/7/12 17:26
 */
@Slf4j
@RestController
@RequestMapping("/instrument-germ")
public class InstrumentGermController extends BaseController {
    @Resource
    private InstrumentGermService instrumentGermService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private GermService germService;

    @Resource
    private InstrumentService instrumentService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 仪器细菌 查看
     */
    @PostMapping("/select-by-instrument")
    public Object selectByInstrument(@RequestParam("instrumentId") Long instrumentId) {

        LoginUserHandler.User user = LoginUserHandler.get();
        // 当前机构所有细菌
        List<GermDto> germDtos = germService.selectByOrgId(user.getOrgId()).stream()
            .filter(f -> Objects.equals(f.getEnable(), YesOrNoEnum.YES.getCode())).collect(Collectors.toList());

        // 当前仪器维护细菌 一个仪器下一个细菌 只维护一条
        Map<Long, InstrumentGermDto> instrumentGermDtoByMedicineId =
            instrumentGermService.selectByInstrumentId(instrumentId).stream()

                .collect(Collectors.toMap(InstrumentGermDto::getGermId, Function.identity(), (key1, ke2) -> key1));

        List<InstrumentGermResponseVo> targetList = Lists.newArrayListWithCapacity(germDtos.size());
        germDtos.forEach(item -> {
            InstrumentGermResponseVo vo = new InstrumentGermResponseVo();
            vo.setGermId(item.getGermId());
            vo.setGermCode(item.getGermCode());
            vo.setGermName(item.getGermName());
            InstrumentGermDto instrumentGermDto = instrumentGermDtoByMedicineId.get(item.getGermId());
            if (Objects.nonNull(instrumentGermDto)) {
                vo.setInstrumentGermId(instrumentGermDto.getInstrumentGermId());
                vo.setInstrumentChannel(instrumentGermDto.getInstrumentChannel());
            }
            targetList.add(vo);
        });
        return targetList;

    }

    /**
     * 仪器细菌 保存
     */
    @PostMapping("/save")
    public Object saveInstrumentGerm(@RequestBody UpdateInstrumentGermRequestVo vo) {

        final InstrumentDto instrument = instrumentService.selectByInstrumentId(vo.getInstrumentId());
        if (Objects.isNull(instrument)) {
            throw new IllegalArgumentException("仪器不存在");
        }

        final GermDto germ = germService.selectByGermId(vo.getGermId());
        if (Objects.isNull(germ)) {
            throw new IllegalArgumentException("细菌不存在");
        }

        if (StringUtils.isNotBlank(vo.getInstrumentChannel())) {
            if (instrumentGermService.selectByInstrumentId(vo.getInstrumentId()).stream()
                .anyMatch(e -> Objects.equals(e.getInstrumentChannel(), vo.getInstrumentChannel())
                    && !Objects.equals(e.getGermId(), vo.getGermId()))) {
                throw new IllegalArgumentException(String.format("仪器通道号 [%s] 已经存在", vo.getInstrumentChannel()));
            }
        }
        // 原来的
        InstrumentGermDto sourceDto = instrumentGermService.selectByGermId(vo.getInstrumentId(), vo.getGermId());

        InstrumentGermDto old = new InstrumentGermDto();

        if (Objects.isNull(sourceDto)) {

            final InstrumentGermDto g = new InstrumentGermDto();
            g.setInstrumentGermId(snowflakeService.genId());
            g.setInstrumentId(vo.getInstrumentId());
            g.setInstrumentCode(instrument.getInstrumentCode());
            g.setInstrumentName(instrument.getInstrumentName());
            g.setGermId(germ.getGermId());
            g.setGermCode(germ.getGermCode());
            g.setInstrumentChannel(vo.getInstrumentChannel());
            g.setOrgId(LoginUserHandler.get().getOrgId());
            g.setOrgName(LoginUserHandler.get().getOrgName());
            g.setCreateDate(new Date());
            g.setUpdateDate(new Date());
            g.setUpdaterId(LoginUserHandler.get().getUserId());
            g.setUpdaterName(LoginUserHandler.get().getNickname());
            g.setCreatorId(LoginUserHandler.get().getUserId());
            g.setCreatorName(LoginUserHandler.get().getNickname());
            g.setIsDelete(YesOrNoEnum.NO.getCode());

            instrumentGermService.addInstrumentGerms(Collections.singletonList(g));

        } else {
            InstrumentGermDto update = new InstrumentGermDto();
            update.setInstrumentGermId(sourceDto.getInstrumentGermId());
            update.setInstrumentChannel(vo.getInstrumentChannel());
            instrumentGermService.updateByInstrumentGermId(update);

            old.setInstrumentChannel(sourceDto.getInstrumentChannel());
        }

        InstrumentGermDto now = new InstrumentGermDto();
        now.setInstrumentChannel(vo.getInstrumentChannel());

        String compare = new CompareUtils<InstrumentGermDto>().compare(old, now);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_CHANNEL.getDesc()).setContent(String
                    .format("修改 [%s] 仪器下 [%s] 细菌 : [%s]", instrument.getInstrumentName(), germ.getGermName(), compare))
                    .toJSONString());
        }

        return Collections.emptyMap();

    }

}
