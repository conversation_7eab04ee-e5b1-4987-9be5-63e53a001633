package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.ItemSendPriceDto;
import com.labway.lims.base.api.service.ItemSendPriceService;
import com.labway.lims.base.mapper.TbItemSendPriceMapper;
import com.labway.lims.base.model.TbItemSendPrice;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 外送项目价格设置 Service impl
 *
 * <AUTHOR>
 * @since 2023/5/4 15:10
 */
@Slf4j
@DubboService
public class ItemSendPriceServiceImpl implements ItemSendPriceService {

    @Resource
    private TbItemSendPriceMapper tbItemSendPriceMapper;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public List<ItemSendPriceDto> selectByHspOrgId(long hspOrgId) {
        if (hspOrgId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbItemSendPrice> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbItemSendPrice::getHspOrgId, hspOrgId);
        queryWrapper.eq(TbItemSendPrice::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbItemSendPrice::getCreateDate);
        return convert(tbItemSendPriceMapper.selectList(queryWrapper));
    }

    @Override
    public Map<Long, List<ItemSendPriceDto>> selectByHspOrgIdsAsMap(Collection<Long> hspOrgIds) {
        if (CollectionUtils.isEmpty(hspOrgIds)) {
            log.warn("ItemSendPriceServiceImpl#selectByHspOrgIdsAsMap hspOrgIds is empty");
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<TbItemSendPrice> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbItemSendPrice::getHspOrgId, hspOrgIds);
        queryWrapper.eq(TbItemSendPrice::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbItemSendPrice::getCreateDate);
        return convert(tbItemSendPriceMapper.selectList(queryWrapper)).stream()
                .collect(Collectors.groupingBy(ItemSendPriceDto::getHspOrgId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addItemSendPrices(List<ItemSendPriceDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        Date now = new Date();
        for (ItemSendPriceDto dto : list) {
            final TbItemSendPrice target = new TbItemSendPrice();
            BeanUtils.copyProperties(dto, target);
            if (Objects.isNull(target.getPriceId())) {
                target.setPriceId(snowflakeService.genId());
            }
            target.setOrgId(loginUser.getOrgId());
            target.setOrgName(loginUser.getOrgName());
            target.setCreateDate(now);
            target.setUpdateDate(now);
            target.setCreatorId(loginUser.getUserId());
            target.setCreatorName(loginUser.getNickname());
            target.setUpdaterId(loginUser.getUserId());
            target.setUpdaterName(loginUser.getNickname());
            target.setIsDelete(YesOrNoEnum.NO.getCode());
            if (tbItemSendPriceMapper.insert(target) < 1) {
                throw new IllegalStateException("添加外送项目价格设置失败");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPriceIds(Collection<Long> priceIds) {
        if (CollectionUtils.isEmpty(priceIds)) {
            return;
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除外送项目价格设置成功 [{}] 结果 [{}]", loginUser.getNickname(), priceIds,
                tbItemSendPriceMapper.deleteBatchIds(priceIds) > 0);
    }

    @Nullable
    @Override
    public ItemSendPriceDto selectByPriceId(long priceId) {
        if (priceId < 1) {
            return null;
        }
        LambdaQueryWrapper<TbItemSendPrice> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbItemSendPrice::getPriceId, priceId);
        queryWrapper.eq(TbItemSendPrice::getIsDelete, YesOrNoEnum.NO.getCode());
        return convert(tbItemSendPriceMapper.selectOne(queryWrapper.last("limit 1")));
    }

    @Override
    public List<ItemSendPriceDto> selectByPriceIds(Collection<Long> priceIds) {
        if (CollectionUtils.isEmpty(priceIds)) {
            return Collections.emptyList();
        }
        return tbItemSendPriceMapper.selectBatchIds(priceIds)
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByPriceId(ItemSendPriceDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbItemSendPrice target = new TbItemSendPrice();
        BeanUtils.copyProperties(dto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbItemSendPriceMapper.updateById(target) < 1) {
            throw new LimsException("修改外送项目价格设置失败");
        }

        log.info("用户 [{}] 修改外送项目价格设置成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    public List<ItemSendPriceDto> selectByHspOrgIdsAndDateRange(Collection<Long> hspOrgIds, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(hspOrgIds) || Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbItemSendPrice> queryWrapper = Wrappers.lambdaQuery(TbItemSendPrice.class)
                .in(TbItemSendPrice::getHspOrgId, hspOrgIds)
                .and(and -> {
                    and.le(TbItemSendPrice::getStartDate, startDate);
                    and.ge(TbItemSendPrice::getEndDate, endDate);

                    and.or();

                    and.le(TbItemSendPrice::getStartDate, endDate);
                    and.ge(TbItemSendPrice::getEndDate, endDate);

                    and.or();

                    and.le(TbItemSendPrice::getStartDate, startDate);
                    and.ge(TbItemSendPrice::getEndDate, startDate);
                });

        return JSON.parseArray(JSON.toJSONString(tbItemSendPriceMapper.selectList(queryWrapper)), ItemSendPriceDto.class);
    }

    private ItemSendPriceDto convert(TbItemSendPrice tbItemSendPrice) {
        if (Objects.isNull(tbItemSendPrice)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(tbItemSendPrice), ItemSendPriceDto.class);
    }

    private List<ItemSendPriceDto> convert(List<TbItemSendPrice> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(JSON.toJSONString(list), ItemSendPriceDto.class);
    }
}
