package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 项目价格财务基准包套餐详情
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@TableName(value = "tb_item_price_base_finance_detail")
public class TbItemPriceBaseFinanceDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
    * 详情ID
    */
    private Long financeDetailId;

    /**
    * 基准包ID
    */
    private Long packageId;

    /**
    * 财务套餐价格code
    */
    private String combinePackageCode;

    /**
    * 创建时间
    */
    private Date createDate;

    /**
    * 更新时间
    */
    private Date updateDate;

    /**
    * 更新人ID
    */
    private Long updaterId;

    /**
    * 更新人名称
    */
    private String updaterName;

    /**
    * 创建人ID
    */
    private Long creatorId;

    /**
    * 创建人名称
    */
    private String creatorName;

    /**
    * 1:删除,0:未删
    */
    private Integer isDelete;

}