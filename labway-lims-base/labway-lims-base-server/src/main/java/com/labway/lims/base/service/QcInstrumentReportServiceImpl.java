package com.labway.lims.base.service;

import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.enums.EnableEnum;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.QcInstrumentReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 仪器报告项
 *
 * <AUTHOR>
 * @Date 2023/11/2 13:01
 * @Version 1.0
 */
@DubboService
@Slf4j
public class QcInstrumentReportServiceImpl implements QcInstrumentReportService {
    @Resource
    private InstrumentReportItemService instrumentReportItemService;
    @Resource
    private InstrumentService instrumentService;

    @Override
    public List<InstrumentReportItemDto> selectInstrumentReportByInstrumentId(Long instrumentId) {
        if (Objects.isNull(instrumentId)) {
            return Collections.emptyList();
        }
        return instrumentReportItemService.selectByInstrumentId(instrumentId);
    }

    @Override
    public List<InstrumentDto> queryInstrumentListByGroupId(Long groupId) {
        List<InstrumentDto> instrumentDtos = instrumentService.selectByGroupId(groupId);
        if (CollectionUtils.isEmpty(instrumentDtos)) {
            return Collections.emptyList();
        }
        //保证该仪器是启用中的
        return instrumentDtos.stream().filter(dto -> EnableEnum.ENABLE.getEnableStatusInt().equals(dto.getEnable())).collect(Collectors.toList());
    }
}
