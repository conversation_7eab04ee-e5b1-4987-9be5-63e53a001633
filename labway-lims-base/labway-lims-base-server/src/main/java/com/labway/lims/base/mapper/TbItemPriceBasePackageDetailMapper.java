package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.model.TbItemPriceBasePackageDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目价格基准包详情Mapper
 */
@Mapper
public interface TbItemPriceBasePackageDetailMapper extends BaseMapper<TbItemPriceBasePackageDetail> {

    /**
     * 批量 插入
     */
    void batchAddItemPriceBasePackageDetails(@Param("conditions") List<TbItemPriceBasePackageDetail> conditions);
}
