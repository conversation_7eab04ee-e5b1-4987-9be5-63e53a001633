package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.GroupMaterialDetailDto;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import com.labway.lims.base.model.TbGroupMaterial;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/9 18:02
 */
@Mapper
public interface TbGroupMaterialMapper extends BaseMapper<TbGroupMaterial> {

    /**
     * 根据ID批量修改
     */
    int updateByGroupMaterialIds(@Param("groupMaterialDto") GroupMaterialDto groupMaterialDto,
        @Param("groupMaterialIds") Collection<Long> groupMaterialIds);

    int addBatch(@Param("gms") List<GroupMaterialDto> gms);

    /**
     * 查找专业组下 物料编码 对应 信息
     *
     * @param groupId 专业组id
     * @param materialCodes 物料编码
     */
    List<GroupMaterialDetailDto> selectByGroupIdAndMaterialCodes(@Param("groupId") long groupId,
                                                                 @Param("materialCodes") Collection<String> materialCodes);
}
