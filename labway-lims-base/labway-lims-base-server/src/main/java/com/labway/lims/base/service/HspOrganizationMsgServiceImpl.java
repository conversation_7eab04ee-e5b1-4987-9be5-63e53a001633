package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.HspOrganizationMsgDto;
import com.labway.lims.base.api.service.HspOrganizationMsgService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.mapper.TbHspOrganizationMsgMapper;
import com.labway.lims.base.mapstruct.HspOrganizationMsgConverter;
import com.labway.lims.base.model.TbHspOrganizationMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "hsp-organization-msg")
public class HspOrganizationMsgServiceImpl implements HspOrganizationMsgService {

    @Resource
    private HspOrganizationService hspOrganizationMsgService;

    @Resource
    private TbHspOrganizationMsgMapper tbHspOrganizationMsgMapper;

    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private HspOrganizationMsgConverter hspOrganizationMsgConverter;

    @Override
    @CacheEvict(allEntries = true)
    public void addHspOrgTipMsg(HspOrganizationMsgDto msgDto) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final HspOrganizationDto hspOrganization = hspOrganizationMsgService.selectByHspOrgId(msgDto.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("机构不存在");
        }

        // 判断这个位置是否存在提示消息
        if (Objects.nonNull(selectByHspOrgIdAndPositionId(msgDto.getHspOrgId(), msgDto.getMsgPositionCode()))) {
            throw new IllegalStateException(String.format("该位置 [%s] 已存在提示消息", msgDto.getMsgPosition()));
        }

        final TbHspOrganizationMsg object = JSON.parseObject(JSON.toJSONString(msgDto), TbHspOrganizationMsg.class);
        object.setMsgId(snowflakeService.genId());
        object.setHspOrgId(hspOrganization.getHspOrgId());
        object.setHspOrgName(hspOrganization.getHspOrgName());
        object.setCreateDate(new Date());
        object.setCreatorName(user.getNickname());
        object.setCreatorId(user.getUserId());
        object.setUpdaterName(user.getNickname());
        object.setUpdateDate(new Date());
        object.setUpdaterId(user.getUserId());
        object.setIsDelete(YesOrNoEnum.NO.getCode());
        object.setEnable(ObjectUtils.defaultIfNull(object.getEnable(), YesOrNoEnum.YES.getCode()));
        if (tbHspOrganizationMsgMapper.insert(object) < 1) {
            throw new IllegalStateException("新增机构提示消息失败");
        }

        log.info("用户 [{}] 添加机构消息成功 [{}]", user.getNickname(), JSON.toJSONString(msgDto));

    }

    @Override
    @CacheEvict(allEntries = true)
    public void updateTipMsgByMsgId(HspOrganizationMsgDto msg) {

        if (Objects.isNull(msg.getMsgId())) {
            return;
        }
        final LoginUserHandler.User user = LoginUserHandler.get();

        // 当前的消息提示信息
        final HspOrganizationMsgDto hspOrganizationMsgDb = selectTipMsgByMsgId(msg.getMsgId());
        if (Objects.isNull(hspOrganizationMsgDb)) {
            throw new IllegalStateException("机构消息提示不存在，请刷新后再试");
        }

        final HspOrganizationMsgDto hspOrganizationMsg =
            selectByHspOrgIdAndPositionId(msg.getHspOrgId(), msg.getMsgPositionCode());

        if (Objects.nonNull(hspOrganizationMsg)
            && !Objects.equals(hspOrganizationMsgDb.getMsgPositionCode(), hspOrganizationMsg.getMsgPositionCode())) {
            throw new IllegalStateException(String.format("该位置 [%s] 已存在提示消息", msg.getMsgPosition()));
        }

        final TbHspOrganizationMsg update = JSON.parseObject(JSON.toJSONString(msg), TbHspOrganizationMsg.class);
        update.setHspOrgId(null);
        update.setCreateDate(null);
        update.setCreatorName(null);
        update.setCreatorId(null);
        update.setUpdateDate(new Date());
        update.setUpdaterId(user.getUserId());
        update.setUpdaterName(user.getNickname());

        if (tbHspOrganizationMsgMapper.updateById(update) < 1) {
            throw new IllegalStateException("修改机构提示消息失败");
        }

        log.info("用户 [{}] 修改机构提示消息成功 [{}]", user.getNickname(), JSON.toJSONString(msg));
    }

    @Override
    public List<HspOrganizationMsgDto> selectAllHspOrgTipMsg() {
        final List<TbHspOrganizationMsg> msgs = tbHspOrganizationMsgMapper
            .selectList(Wrappers.lambdaQuery(TbHspOrganizationMsg.class).orderByDesc(TbHspOrganizationMsg::getMsgId));
        return JSON.parseArray(JSON.toJSONString(msgs), HspOrganizationMsgDto.class);
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteHspOrgTipMsgByMsgId(long msgId) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        if (tbHspOrganizationMsgMapper.deleteById(msgId) < 1) {
            throw new IllegalStateException("删除机构消息失败");
        }

        log.info("用户 [{}] 删除机构消息", user.getNickname());
    }

    @Override
    @Cacheable(key = "'selectTipMsgByMsgId:' + #msgId")
    public HspOrganizationMsgDto selectTipMsgByMsgId(Long msgId) {
        if (Objects.isNull(msgId)) {
            return null;
        }

        return JSON.parseObject(JSON.toJSONString(tbHspOrganizationMsgMapper.selectById(msgId)),
            HspOrganizationMsgDto.class);
    }

    @Override
    public List<HspOrganizationMsgDto> selectTipMsgByMsgIds(Collection<Long> msgIds) {
        if (CollectionUtils.isEmpty(msgIds)) {
            return Collections.emptyList();
        }
        return hspOrganizationMsgConverter
            .hspOrganizationMsgDtoListFromTbObjList(tbHspOrganizationMsgMapper.selectBatchIds(msgIds));
    }

    @Override
    @Cacheable(key = "'selectTipMsgByHspOrgId:' + #hspOrgId")
    public List<HspOrganizationMsgDto> selectTipMsgByHspOrgId(Long hspOrgId) {
        final LambdaQueryWrapper<TbHspOrganizationMsg> eq =
            Wrappers.lambdaQuery(TbHspOrganizationMsg.class).eq(TbHspOrganizationMsg::getHspOrgId, hspOrgId);

        final List<TbHspOrganizationMsg> msgs = tbHspOrganizationMsgMapper.selectList(eq);

        return JSON.parseArray(JSON.toJSONString(msgs), HspOrganizationMsgDto.class);
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteHspOrgTipMsgByMsgIds(List<Long> msgIds) {

        if (CollectionUtils.isEmpty(msgIds)) {
            return;
        }

        if (tbHspOrganizationMsgMapper.deleteBatchIds(msgIds) < 1) {
            throw new IllegalStateException("删除机构提示消息失败");
        }

    }

    @Override
    public HspOrganizationMsgDto selectByHspOrgIdAndPositionId(long hspOrgId, String msgPositionCode) {
        if (StringUtils.isBlank(msgPositionCode)) {
            return null;
        }
        final LambdaQueryWrapper<TbHspOrganizationMsg> last =
            Wrappers.lambdaQuery(TbHspOrganizationMsg.class).eq(TbHspOrganizationMsg::getHspOrgId, hspOrgId)
                .eq(TbHspOrganizationMsg::getMsgPositionCode, msgPositionCode).last("limit 1");
        return JSON.parseObject(JSON.toJSONString(tbHspOrganizationMsgMapper.selectOne(last)),
            HspOrganizationMsgDto.class);
    }

}
