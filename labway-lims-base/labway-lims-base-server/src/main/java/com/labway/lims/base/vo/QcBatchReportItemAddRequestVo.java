package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * 添加 质控批号报告项目 vo
 * 
 * <AUTHOR>
 * @since 2023/7/4 14:42
 */
@Getter
@Setter
public class QcBatchReportItemAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 质控批号ID
     */
    private Long qcBatchId;

    /**
     * 添加的 仪器报告项目 code
     */
    private Set<String> reportItemCodes;

}
