package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 项目价格基准包 添加项目
 */
@Getter
@Setter
public class BasePackageContrastTestItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 基准包id
     */
    private Long packageId;

    /**
     * 选择检验下项目
     */
    private List<ContrastTestItemVo> itemList;

    /**
     * 选择 对应 检验项目
     */
    @Getter
    @Setter
    public static class ContrastTestItemVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 检验项目id
         */
        private Long testItemId;
        /**
         * 收费价格
         */
        private String feePrice;
    }
}
