package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 仪器报告项目常用短语
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Setter
@Getter
@TableName("tb_instrument_report_item_common_phrase")
public class TbInstrumentReportItemCommonPhrase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器报告常用短语ID
     */
    @TableId
    private Long instrumentReportItemCommonPhraseId;

    /**
     * 仪器报告项目ID
     */
    private Long instrumentReportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 快捷键
     */
    private String keyShort;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 短语内容
     */
    private String content;

    /**
     * 是否默认
     * @see YesOrNoEnum
     */
    private Integer isDefault;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;


    /**
     * 1: 删除 0：未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;


}
