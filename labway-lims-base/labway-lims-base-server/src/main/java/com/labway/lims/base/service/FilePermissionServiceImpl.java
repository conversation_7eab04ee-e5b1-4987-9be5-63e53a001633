package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.FilePermissionDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.FilePermissionService;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.mapper.TbFilePermissionMapper;
import com.labway.lims.base.model.TbFilePermission;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "file-permission")
public class FilePermissionServiceImpl implements FilePermissionService {
    @Resource
    private TbFilePermissionMapper filePermissionMapper;
    @Resource
    private GroupService groupService;
    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private FilePermissionService filePermissionService;


    @Override
    @Cacheable(key = "'selectByFileId:' + #fileId")
    public List<FilePermissionDto> selectByFileId(long fileId) {
        return filePermissionMapper.selectList(new LambdaQueryWrapper<TbFilePermission>()
                        .eq(TbFilePermission::getFileId, fileId)).stream().map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByFileId(long fileId) {
        filePermissionMapper.delete(new LambdaUpdateWrapper<TbFilePermission>()
                .eq(TbFilePermission::getFileId, fileId));

        log.info("用户 [{}] 删除了文件的 [{}] 权限", LoginUserHandler.get().getNickname(), fileId);

    }

    @Override
    public List<FilePermissionDto> selectByFileIds(Collection<Long> fileIds) {
        if (CollectionUtils.isEmpty(fileIds)) {
            return Collections.emptyList();
        }

        return fileIds.stream().map(e -> filePermissionService.selectByFileId(e))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }


    private FilePermissionDto convert(TbFilePermission filePermission) {
        if (Objects.isNull(filePermission)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(filePermission), FilePermissionDto.class);
    }

    @Override
    @CacheEvict(allEntries = true)
    public void addFilePermissions(long fileId, Collection<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return;
        }

        final Map<Long, ProfessionalGroupDto> groups = groupService.selectByGroupIds(groupIds)
                .stream().collect(Collectors.toMap(ProfessionalGroupDto::getGroupId, v -> v, (a, b) -> a));

        final LinkedList<Long> ids = snowflakeService.genIds(groupIds.size());

        final List<TbFilePermission> fps = groupIds.stream().map(e -> {
            if (!groups.containsKey(e)) {
                return null;
            }
            final TbFilePermission filePermission = new TbFilePermission();
            filePermission.setFilePermissionId(ids.pop());
            filePermission.setFileId(fileId);
            filePermission.setGroupId(e);
            filePermission.setGroupName(groups.get(e).getGroupName());
            filePermission.setCreateDate(new Date());
            filePermission.setUpdateDate(new Date());
            filePermission.setCreatorId(LoginUserHandler.get().getUserId());
            filePermission.setCreatorName(LoginUserHandler.get().getNickname());
            filePermission.setUpdaterId(LoginUserHandler.get().getUserId());
            filePermission.setUpdaterName(LoginUserHandler.get().getNickname());
            filePermission.setIsDelete(YesOrNoEnum.NO.getCode());
            filePermission.setOrgId(LoginUserHandler.get().getOrgId());
            filePermission.setOrgName(LoginUserHandler.get().getOrgName());
            return filePermission;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(fps)) {
            return;
        }

        log.info("用户 [{}] 给文件 [{}] 添加了权限专业组 [{}]", LoginUserHandler.get().getNickname(), fileId, fps.stream()
                .map(TbFilePermission::getGroupName).collect(Collectors.joining(",")));

        filePermissionMapper.addBatch(fps);

    }
}
