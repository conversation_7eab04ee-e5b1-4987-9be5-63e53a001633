package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.base.api.dto.RefrigeratorGroupDto;
import com.labway.lims.base.api.service.RefrigeratorGroupService;
import com.labway.lims.base.mapper.TbRefrigeratorGroupMapper;
import com.labway.lims.base.model.TbRefrigeratorGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 冰箱专业组关联表 Service impl
 *
 * <AUTHOR>
 * @since 2023/4/3 15:16
 */
@Slf4j
@DubboService
public class RefrigeratorGroupServiceImpl implements RefrigeratorGroupService {

    @Resource
    private TbRefrigeratorGroupMapper tbRefrigeratorGroupMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRefrigeratorGroups(List<RefrigeratorGroupDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 冰箱专业组关联
        List<TbRefrigeratorGroup> targetList =
                list.stream().map(obj -> JSON.parseObject(JSON.toJSONString(obj), TbRefrigeratorGroup.class))
                        .collect(Collectors.toList());
        // 数量 分区批次插入
        List<List<TbRefrigeratorGroup>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbRefrigeratorGroupMapper.batchAddTbRefrigeratorGroups(item));

        log.info("用户 [{}] 新增冰箱专业组关联[{}]成功", loginUser.getNickname(), JSON.toJSONString(targetList));

    }

    @Override
    public List<RefrigeratorGroupDto> selectByRefrigeratorId(long refrigeratorId) {
        if (refrigeratorId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRefrigeratorGroup> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbRefrigeratorGroup::getRefrigeratorId, refrigeratorId);
        queryWrapper.eq(TbRefrigeratorGroup::getIsDelete, YesOrNoEnum.NO.getCode());

        return convert(tbRefrigeratorGroupMapper.selectList(queryWrapper));
    }

    @Override
    public List<RefrigeratorGroupDto> selectByRefrigeratorIds(Collection<Long> refrigeratorId) {
        if (CollectionUtils.isEmpty(refrigeratorId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRefrigeratorGroup> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbRefrigeratorGroup::getRefrigeratorId, refrigeratorId);
        queryWrapper.eq(TbRefrigeratorGroup::getIsDelete, YesOrNoEnum.NO.getCode());

        return convert(tbRefrigeratorGroupMapper.selectList(queryWrapper));
    }

    @Override
    public void deleteByRelationIds(Collection<Long> relationIds) {
        if (CollectionUtils.isEmpty(relationIds)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除冰箱专业组关联成功 [{}] 结果 [{}]", loginUser.getNickname(), relationIds,
                tbRefrigeratorGroupMapper.deleteBatchIds(relationIds) > 0);
    }

    @Override
    public List<RefrigeratorGroupDto> selectByGroupId(long groupId) {
        if (groupId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRefrigeratorGroup> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbRefrigeratorGroup::getGroupId, groupId);
        queryWrapper.eq(TbRefrigeratorGroup::getIsDelete, YesOrNoEnum.NO.getCode());

        return convert(tbRefrigeratorGroupMapper.selectList(queryWrapper));
    }

    /**
     * TbRefrigeratorGroup 转换 为 RefrigeratorGroupDto
     *
     * @param list TbRefrigeratorGroup
     * @return RefrigeratorGroupDto
     */
    private List<RefrigeratorGroupDto> convert(List<TbRefrigeratorGroup> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert)
                .collect(Collectors.toList());
    }


    private RefrigeratorGroupDto convert(TbRefrigeratorGroup group) {
        if (Objects.isNull(group)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(group), RefrigeratorGroupDto.class);
    }
}
