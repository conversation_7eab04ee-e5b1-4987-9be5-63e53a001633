package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.PackageItemDto;
import com.labway.lims.base.model.TbPackageItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 体检单位套餐 项目 mapper
 * 
 * <AUTHOR>
 * @since 2023/3/28 17:35
 */
@Mapper
public interface TbPackageItemMapper extends BaseMapper<TbPackageItem> {

    /**
     * 批量 插入
     */
    void batchAddPackageItems(@Param("conditions") List<TbPackageItem> conditions);

    /**
     * 根据机构和类型查询所有
     * @param orgId
     * @param typeCode
     * @return
     */
    List<PackageItemDto> selectAllByOrgIdAndType(@Param("orgId")Long orgId, @Param("typeCode")String typeCode);
}
