package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 机构保底金维护
 */
@Getter
@Setter
@TableName("tb_hsp_org_deposit_guarantee")
public class TbHspOrgDepositGuarantee implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long hspOrgDepositGuaranteeId;

    /**
     * 送检机构编码
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 生效日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 保底金额
     */
    private BigDecimal guaranteeAmount;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 修改人ID
     */
    private Long updaterId;

    /**
     * 修改人名称
     */
    private String updaterName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

    /**
     * 机构编码
     */
    private String hspOrgCode;
} 