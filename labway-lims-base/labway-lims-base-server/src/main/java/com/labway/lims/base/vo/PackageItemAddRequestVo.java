package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * 体检单位套餐 项目 调整 vo
 * 
 * <AUTHOR>
 * @since 2023/3/28 17:59
 */
@Getter
@Setter
public class PackageItemAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 体检套餐ID
     */
    private Long packageId;

    /**
     * 要添加的 检验项目ids
     */
    private Set<Long> testItemIds;
}
