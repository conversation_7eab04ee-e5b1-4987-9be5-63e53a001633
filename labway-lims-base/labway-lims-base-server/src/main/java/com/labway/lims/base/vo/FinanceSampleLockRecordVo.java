package com.labway.lims.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.finance.FinanceLockEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/11 20:05
 */
@Getter
@Setter
public class FinanceSampleLockRecordVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Long sampleLockRecordId;

    /**
     * applySampleId
     */
    private Long applySampleId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 样本信息
     */
    private String sampleContent;

    /**
     * 原因
     */
    private String reason;

    /**
     * 锁状态
     * @see FinanceLockEnum
     */
    private Integer status;

    /**
     * orgID
     */
    private Long orgId;

    /**
     * orgName
     */
    private String orgName;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date operDate;

    /**
     * 操作人
     */
    private String operator;


}
