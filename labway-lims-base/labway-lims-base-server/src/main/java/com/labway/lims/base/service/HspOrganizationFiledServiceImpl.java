package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.DoubleCheckFiledEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.HspOrganizationFiledDto;
import com.labway.lims.base.api.dto.SaveHspOrganizationFiledDto;
import com.labway.lims.base.api.service.HspOrganizationFiledService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.mapper.TbHspOrganizationFiledMapper;
import com.labway.lims.base.model.TbHspOrganizationFiled;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "hsp-organization-filed")
public class HspOrganizationFiledServiceImpl implements HspOrganizationFiledService {
    @Resource
    private TbHspOrganizationFiledMapper tbHspOrganizationFiledMapper;

    @Resource
    private HspOrganizationService hspOrganizationService;

    @Resource
    private SnowflakeService snowflakeService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    @CacheEvict(allEntries = true)
    public Set<Long> addHspOrganizationFiled(SaveHspOrganizationFiledDto dto) {
        final Long hspOrgId = dto.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalStateException("外送机构不存在");
        }

        final HspOrganizationDto org = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(org)) {
            throw new IllegalStateException("外送机构不存在");
        }

        final LoginUserHandler.User user = LoginUserHandler.get();


        final List<TbHspOrganizationFiled> tbs = dto.getFileds().stream().map(m -> {

            final TbHspOrganizationFiled tb = JSON.parseObject(JSON.toJSONString(dto), TbHspOrganizationFiled.class);
            tb.setCode(m.getCode());

            final DoubleCheckFiledEnum code = DoubleCheckFiledEnum.getByCode(m.getCode());
            if (Objects.isNull(code)) {
                throw new IllegalStateException(String.format("字段 [%s] 不存在", m.getCode()));
            }
            tb.setCode(code.getCode());
            tb.setFiled(code.getDesc());
            tb.setFiledId(snowflakeService.genId());
            tb.setHspOrgCode(org.getHspOrgCode());
            tb.setHspOrgName(org.getHspOrgName());
            tb.setIsDelete(YesOrNoEnum.NO.getCode());
            tb.setCreateDate(new Date());
            tb.setCreatorId(user.getUserId());
            tb.setCreatorName(user.getNickname());
            tb.setUpdaterId(user.getUserId());
            tb.setUpdaterName(user.getNickname());
            tb.setUpdateDate(new Date());

            return tb;
        }).collect(Collectors.toList());


        for (final TbHspOrganizationFiled tb : tbs) {
            tbHspOrganizationFiledMapper.insert(tb);
        }

        log.info("新增外送机构字段成功, user: {} hspOrgId: {}, filedCodes: {}", user.getNickname(), hspOrgId, tbs.stream().map(TbHspOrganizationFiled::getCode).collect(Collectors.toSet()));

        return tbs.stream().map(TbHspOrganizationFiled::getFiledId).collect(Collectors.toSet());
    }

    @Override
    @Cacheable(key = "'selectByHspOrgId:' + #hspOrgId")
    public List<HspOrganizationFiledDto> selectByHspOrgId(Long hspOrgId) {
        final LambdaQueryWrapper<TbHspOrganizationFiled> last = Wrappers.lambdaQuery(TbHspOrganizationFiled.class)
                .eq(Objects.nonNull(hspOrgId), TbHspOrganizationFiled::getHspOrgId, hspOrgId);
        return tbHspOrganizationFiledMapper.selectList(last).stream().map(this::convert).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @CacheEvict(allEntries = true)
    public Set<Long> update(SaveHspOrganizationFiledDto dto) {
        // 先删
        final LambdaQueryWrapper<TbHspOrganizationFiled> eq = Wrappers.lambdaQuery(TbHspOrganizationFiled.class).eq(TbHspOrganizationFiled::getHspOrgId, dto.getHspOrgId());
        tbHspOrganizationFiledMapper.delete(eq);

        // 后增
        return addHspOrganizationFiled(dto);
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByHspOrgId(long hspOrgId) {
        deleteByHspOrgIds(List.of(hspOrgId));
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByHspOrgIds(Collection<Long> hspOrgIds) {

        if (CollectionUtils.isEmpty(hspOrgIds)) {
            return;
        }

        final LambdaQueryWrapper<TbHspOrganizationFiled> eq = Wrappers.lambdaQuery(TbHspOrganizationFiled.class)
                .in(TbHspOrganizationFiled::getHspOrgId, hspOrgIds);

        tbHspOrganizationFiledMapper.delete(eq);
    }


    /**
     * 将 TbHspOrganizationFiled 转换为 HspOrganizationFiledDto
     */
    private HspOrganizationFiledDto convert(TbHspOrganizationFiled tb) {
        return JSON.parseObject(JSON.toJSONString(tb), HspOrganizationFiledDto.class);
    }

}
