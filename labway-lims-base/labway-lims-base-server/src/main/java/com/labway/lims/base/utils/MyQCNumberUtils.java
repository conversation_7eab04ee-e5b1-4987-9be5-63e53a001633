package com.labway.lims.base.utils;

import com.labway.business.center.mdm.common.constants.CommonConstant;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;

/**
 * @deprecated 命名有问题
 */
public class MyQCNumberUtils {


    /**
     * 转换成百分比 自定义保留小数点
     * 注意： 会在传入值的基础上*100
     */
    public static String convertToPercentage(NumberFormatEnum numberFormat, double number) {
        return new DecimalFormat(numberFormat.format).format(number * CommonConstant.INT_ONE_HUNDRED) + "%";
    }

    /**
     * 转换成百分比  不保留小数点
     */
    public static String convertToPercentage(double number) {
        return convertToPercentage(NumberFormatEnum.ONE, number);
    }

    /**
     * 除以1000 然后在转换成百分比
     */
    public static String convertToPercentageInBeforeDividedByOneThousand(Long number) {
        return convertToPercentage(Double.valueOf(amountFormat(number)));
    }

    /**
     * 金额格式化：除以1000，并转为0.000格式
     */
    public static String amountFormat(Long amount) {
        if (null == amount) {
            return BigDecimal.ZERO.setScale(3, BigDecimal.ROUND_HALF_UP).toString();
        }
        return amountFormat(amount.toString());
    }

    /**
     * 金额格式化：除以1000，并转为0.000格式
     */
    public static String amountFormat(String amount) {
        if (StringUtils.isBlank(amount)) {
            return BigDecimal.ZERO.setScale(3, BigDecimal.ROUND_HALF_UP).toString();
        }
        return new BigDecimal(amount).divide(BigDecimal.valueOf(1000)).setScale(3, BigDecimal.ROUND_HALF_UP).toString();
    }

    public static BigDecimal amountFormatToBigDecimal(String amount) {
        return StringUtils.isBlank(amount) ? BigDecimal.ZERO : new BigDecimal(amount);
    }

    /**
     * 转long 类型
     */
    public static Long convert2DBDemical(String price) {
        try {
            if (StringUtils.isNotBlank(price)) {
                return new BigDecimal(price).multiply(new BigDecimal(1000)).longValue();
            }
        } catch (Exception e) {
            return 0L;
        }
        return 0L;
    }

    /**
     * 保留2位小数  不进行四舍五入
     */
    public static Double save2RoudDownValue(double value) {
        BigDecimal b = new BigDecimal(value);
        double f = b.setScale(2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
        return f;
    }

    /**
     * 保留3位小数  不进行四舍五入
     */
    public static Double save3RoudDownValue(double value) {
        BigDecimal b = new BigDecimal(value);
        double f = b.setScale(3, BigDecimal.ROUND_HALF_DOWN).doubleValue();
        return f;
    }


    /**
     * 计算两个数值之间的比例
     */
    public static String getScale(int num, int total) {
        // 创建一个数值格式化对象
        NumberFormat numberFormat = NumberFormat.getInstance();
        // 设置精确到小数点后2位
        numberFormat.setMaximumFractionDigits(2);
        String result = numberFormat.format((float) num / (float) total * 100);
        return result + "%";
    }

    /**
     * 保留小数点枚举
     */
    public static enum NumberFormatEnum {
        ZERO("0"),
        ONE("0.0"),
        TWO("0.00"),
        THREE("0.000"),
        FOUR("0.0000");
        private String format;

        NumberFormatEnum(String number) {
            this.format = number;
        }

        public String getFormat() {
            return format;
        }

        public void setFormat(String format) {
            this.format = format;
        }
    }
}
