package com.labway.lims.base.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.compare.dto.OrgTestItemMappingInfoDTO;
import com.labway.business.center.compare.dto.ReportItemInfoDTO;
import com.labway.business.center.compare.request.QueryItemMappingRequest;
import com.labway.business.center.compare.service.TestItemPublishService;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.TwoPickDayEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.*;
import com.labway.lims.base.controller.TestItemController;
import com.labway.lims.base.mapper.TbTestItemMapper;
import com.labway.lims.base.model.TbTestItem;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "test-item")
public class TestItemServiceImpl implements TestItemService {

    @Resource
    private TbTestItemMapper testItemMapper;
    @Resource
    private TestItemServiceImpl self;
    @Resource
    private ReportItemService reportItemService;
    @Resource
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private GroupService groupService;
    @Resource
    private RandomStringService randomStringService;
    @Resource
    private EnvDetector envDetector;

    @Resource
    private Environment environment;

    @DubboReference
    private RabbitMQService rabbitMQService;

    @Resource
    private TestItemPublishService testItemPublishService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public long addTestItem(String testItemCode, long groupId) {
        return addTestItems(List.of(testItemCode), groupId).iterator().next();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public List<Long> addTestItems(Collection<String> testItemCodes, long groupId) {

        if (CollectionUtils.isEmpty(testItemCodes)) {
            throw new IllegalArgumentException("testItemCodes 不能为空");
        }

        final Set<String> ts = self.selectByOrgId(LoginUserHandler.get().getOrgId()).stream()
                .map(TestItemDto::getTestItemCode).collect(Collectors.toSet());
        for (String testItemCode : testItemCodes) {
            if (ts.contains(testItemCode)) {
                throw new IllegalStateException(String.format("编码 [%s] 已存在", testItemCode));
            }
        }

        final QueryItemMappingRequest request = new QueryItemMappingRequest();
        final String orgCode = environment.getProperty(TestItemController.BUSINESS_CENTER_ORG_CODE);
        if (StringUtils.isBlank(orgCode)) {
            throw new IllegalArgumentException(String.format("请配置 %s 参数", TestItemController.BUSINESS_CENTER_ORG_CODE));
        }
        request.setOrgCode(orgCode);

        final Response<List<OrgTestItemMappingInfoDTO>> response = testItemPublishService.queryItemMapping(request);
        if (Objects.isNull(response)) {
            log.error("查询业务中台接口 queryItemMapping 失败，返回 null");
            throw new IllegalStateException("从主数据获取数据失败");
        }

        if (!response.isSuccess()) {
            log.error("查询业务中台接口 queryItemMapping 失败 不是 isSuccess msg: {}", response.getMsg());
            throw new IllegalStateException("从主数据获取数据失败");
        }

        final Map<String, OrgTestItemMappingInfoDTO> data = response.getData().stream()
                .collect(Collectors.toMap(OrgTestItemMappingInfoDTO::getItemTestCode, v -> v, (a, b) -> a));

        log.info("查询业务中台接口 queryItemMapping 成功 数据数量 {}", data.size());

        if (MapUtils.isEmpty(data)) {
            throw new IllegalStateException("主数据尚未下发检验项目");
        }

        for (String testItemCode : testItemCodes) {
            if (!data.containsKey(testItemCode)) {
                throw new IllegalStateException(String.format("主数据尚未下发 [%s] 检验项目", testItemCode));
            }
        }

        final ProfessionalGroupDto group = groupService.selectByGroupId(groupId);
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException("专业组不存在");
        }

        final List<Long> ids = new LinkedList<>();
        for (String testItemCode : testItemCodes) {
            final OrgTestItemMappingInfoDTO dto = data.get(testItemCode);
            ids.add(addMainDataTestItem(dto, group));

            // 日志
            final List<String> reportItemNames =
                    CollectionUtil.defaultIfEmpty(dto.getReportItems(), Collections.emptyList()).stream()
                            .map(ReportItemInfoDTO::getItemReportName).collect(Collectors.toList());
            final String logContent = String.format("新增检验项目 [%s] 编码 [%s] 英文名称 [%s] 样本类型 [%s] 报告项目 [%s]",
                    dto.getItemTestName(), dto.getItemTestCode(), StringUtils.defaultString(dto.getEnglishName()),
                    dto.getSampleTypeName(), String.join(",", reportItemNames));
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY, TraceLog.newInstance()
                    .setModule(TraceLogModuleEnum.TEST_ITEM.getDesc()).setContent(logContent).toJSONString());
        }

        return ids;
    }

    @Override
    @CacheEvict(allEntries = true)
    public void syncMainDataTestItem(String testItemCode) {
        final TestItemDto testItem = self.selectByTestItemCode(testItemCode, LoginUserHandler.get().getOrgId());
        if (Objects.isNull(testItem)) {
            throw new IllegalStateException(String.format("检验项目 [%s] 不存在", testItemCode));
        }

        final Map<String, OrgTestItemMappingInfoDTO> map = selectAllMainDataTestItem();
        if (!map.containsKey(testItemCode)) {
            throw new IllegalStateException(String.format("主数据检验项目 [%s] 不存在", testItemCode));
        }

        final OrgTestItemMappingInfoDTO mainDataTestItem = map.get(testItemCode);

        final TbTestItem tItem = new TbTestItem();
        tItem.setTestItemId(testItem.getTestItemId());
        tItem.setTestItemName(mainDataTestItem.getItemTestName());
        tItem.setSampleTypeCode(mainDataTestItem.getSampleTypeCode());
        tItem.setSampleTypeName(mainDataTestItem.getSampleTypeName());
        tItem.setEnName(mainDataTestItem.getEnglishName());
        tItem.setTestItemName(mainDataTestItem.getItemTestName());
        tItem.setStashRemark(mainDataTestItem.getSaveDescription());
        tItem.setExamMethodCode(mainDataTestItem.getTestMethod());
        tItem.setExamMethodName(mainDataTestItem.getTestMethodName());

        tItem.setUpdaterId(LoginUserHandler.get().getUserId());
        tItem.setUpdaterName(LoginUserHandler.get().getNickname());
        tItem.setUpdateDate(new Date());

        if (testItemMapper.updateById(tItem) < 1) {
            throw new IllegalStateException("同步失败，修改数据库失败");
        }

        // 删除之前的
        reportItemService.deleteByTestItemId(testItem.getTestItemId());

        final LinkedList<Long> ids = snowflakeService.genIds(mainDataTestItem.getReportItems().size());
        for (ReportItemInfoDTO e : mainDataTestItem.getReportItems()) {
            final ReportItemDto reportItem = new ReportItemDto();
            reportItem.setReportItemId(ids.pop());
            reportItem.setReportItemName(StringUtils.defaultString(e.getItemReportName()));
            reportItem.setReportItemCode(StringUtils.defaultString(e.getItemReportCode()));
            reportItem.setTestItemId(testItem.getTestItemId());
            reportItem.setTestItemName(testItem.getTestItemName());
            reportItem.setTestItemCode(testItemCode);
            reportItem.setUpdateDate(new Date());
            reportItem.setCreateDate(new Date());
            reportItem.setCreatorId(testItem.getCreatorId());
            reportItem.setCreatorName(testItem.getCreatorName());
            reportItem.setUpdaterId(testItem.getUpdaterId());
            reportItem.setUpdaterName(testItem.getUpdaterName());
            reportItem.setIsDelete(testItem.getIsDelete());
            reportItem.setOrgId(testItem.getOrgId());
            reportItem.setOrgName(testItem.getOrgName());
            reportItemService.addReportItem(reportItem);

            // 修改仪器报告项目
            final InstrumentReportItemDto uiri = new InstrumentReportItemDto();
            uiri.setReportItemName(reportItem.getReportItemName());
            instrumentReportItemService.updateByReportItemCode(uiri, reportItem.getReportItemCode());

            // 修改report item
            final ReportItemDto ri = new ReportItemDto();
            ri.setReportItemName(e.getItemReportName());
            reportItemService.updateByReportItemCodes(ri,
                    Collections.singletonList(e.getItemReportCode()));
        }

        log.info("用户 [{}] 同步检验项目 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(testItem));

    }

    private Map<String, OrgTestItemMappingInfoDTO> selectAllMainDataTestItem() {
        final QueryItemMappingRequest request = new QueryItemMappingRequest();
        final String orgCode = environment.getProperty(TestItemController.BUSINESS_CENTER_ORG_CODE);
        if (StringUtils.isBlank(orgCode)) {
            throw new IllegalArgumentException(String.format("请配置 %s 参数", TestItemController.BUSINESS_CENTER_ORG_CODE));
        }
        request.setOrgCode(orgCode);

        final Response<List<OrgTestItemMappingInfoDTO>> response = testItemPublishService.queryItemMapping(request);
        if (Objects.isNull(response)) {
            log.error("查询业务中台接口 queryItemMapping 失败，返回 null");
            throw new IllegalStateException("从主数据获取数据失败");
        }

        if (!response.isSuccess()) {
            log.error("查询业务中台接口 queryItemMapping 失败 不是 isSuccess msg: {}", response.getMsg());
            throw new IllegalStateException("从主数据获取数据失败");
        }

        final Map<String, OrgTestItemMappingInfoDTO> data = response.getData().stream()
                .collect(Collectors.toMap(OrgTestItemMappingInfoDTO::getItemTestCode, v -> v, (a, b) -> a));

        log.info("查询业务中台接口 queryItemMapping 成功 数据数量 {}", data.size());

        if (MapUtils.isEmpty(data)) {
            throw new IllegalStateException("主数据尚未下发检验项目");
        }

        return data;
    }

    @CacheEvict(allEntries = true)
    public long addMainDataTestItem(OrgTestItemMappingInfoDTO mainDataTestItem, ProfessionalGroupDto group) {
        final TbTestItem testItem = new TbTestItem();
        testItem.setTestItemId(snowflakeService.genId());
        testItem.setTestItemName(mainDataTestItem.getItemTestName());
        testItem.setTestItemCode(mainDataTestItem.getItemTestCode());
        testItem.setEnName(StringUtils.defaultString(mainDataTestItem.getEnglishName()));
        testItem.setAliasName(StringUtils.EMPTY);
        testItem.setExamMethodCode(StringUtils.defaultString(mainDataTestItem.getTestMethod()));
        testItem.setExamMethodName(StringUtils.defaultString(mainDataTestItem.getTestMethodName()));
        testItem.setShortName(StringUtils.EMPTY);
        testItem.setTubeCode(StringUtils.EMPTY);
        testItem.setTubeName(testItem.getTubeCode());
        testItem.setGroupId(group.getGroupId());
        testItem.setGroupName(group.getGroupName());
        testItem.setEnableExport(0);
        testItem.setExportOrgId(NumberUtils.LONG_ZERO);
        testItem.setExportOrgName(StringUtils.EMPTY);
        testItem.setItemType(ItemTypeEnum.ROUTINE.name());
        testItem.setEnableIt3000(0);
        testItem.setEnableFee(0);
        testItem.setFeeCode(StringUtils.EMPTY);
        testItem.setFeeName(StringUtils.EMPTY);
        testItem.setFeePrice(BigDecimal.ZERO);
        testItem.setFinanceGroupCode(StringUtils.EMPTY);
        testItem.setFinanceGroupName(StringUtils.EMPTY);
        testItem.setBasicQuantity(BigDecimal.ZERO);
        testItem.setCheckQuantity(BigDecimal.ZERO);
        testItem.setDeadSpaceQuantity(BigDecimal.ZERO);
        testItem.setStashRemark(StringUtils.defaultString(mainDataTestItem.getSaveDescription()));
        testItem.setSampleTypeCode(StringUtils.defaultString(mainDataTestItem.getSampleTypeCode()));
        testItem.setSampleTypeName(StringUtils.defaultString(mainDataTestItem.getSampleTypeName()));
        testItem.setExportDate(BigDecimal.ZERO);
        testItem.setLimitSex(SexEnum.DEFAULT.getCode());
        testItem.setCreateDate(new Date());
        testItem.setUpdateDate(new Date());
        testItem.setCreatorId(LoginUserHandler.get().getUserId());
        testItem.setCreatorName(LoginUserHandler.get().getNickname());
        testItem.setUpdaterId(LoginUserHandler.get().getUserId());
        testItem.setUpdaterName(LoginUserHandler.get().getNickname());
        testItem.setOrgId(LoginUserHandler.get().getOrgId());
        testItem.setOrgName(LoginUserHandler.get().getOrgName());
        testItem.setIsDelete(YesOrNoEnum.NO.getCode());
        //默认当天进行二次分拣
        testItem.setTwoPickDay(TwoPickDayEnum.CURRENT.getCode());
        testItem.setTwoPickTime(StringUtils.EMPTY);

        if (testItemMapper.insert(testItem) < 1) {
            throw new IllegalStateException("添加检验项目失败");
        }

        final List<ReportItemInfoDTO> reportItems = mainDataTestItem.getReportItems();
        if (CollectionUtils.isNotEmpty(reportItems)) {
            final LinkedList<Long> ids = snowflakeService.genIds(reportItems.size());

            for (ReportItemInfoDTO e : reportItems) {
                final ReportItemDto reportItem = new ReportItemDto();
                reportItem.setReportItemId(ids.pop());
                reportItem.setReportItemName(StringUtils.defaultString(e.getItemReportName()));
                reportItem.setReportItemCode(StringUtils.defaultString(e.getItemReportCode()));
                reportItem.setTestItemId(testItem.getTestItemId());
                reportItem.setTestItemName(testItem.getTestItemName());
                reportItem.setTestItemCode(mainDataTestItem.getItemTestCode());
                reportItem.setUpdateDate(new Date());
                reportItem.setCreateDate(new Date());
                reportItem.setCreatorId(testItem.getCreatorId());
                reportItem.setCreatorName(testItem.getCreatorName());
                reportItem.setUpdaterId(testItem.getUpdaterId());
                reportItem.setUpdaterName(testItem.getUpdaterName());
                reportItem.setIsDelete(testItem.getIsDelete());
                reportItem.setOrgId(testItem.getOrgId());
                reportItem.setOrgName(testItem.getOrgName());

                reportItemService.addReportItem(reportItem);
            }

        }

        log.info("用户 [{}] 新增检验项目 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(testItem));

        return testItem.getTestItemId();
    }

    @Nullable
    @Override
    public TestItemDto selectByTestItemCode(String testItemCode, long orgId) {
        if (StringUtils.isBlank(testItemCode)) {
            return null;
        }
        return convert(testItemMapper.selectOne(new LambdaQueryWrapper<TbTestItem>()
                .eq(TbTestItem::getTestItemCode, testItemCode).eq(TbTestItem::getOrgId, orgId).last("limit 1")));
    }

    @Nonnull
    @Override
    public List<TestItemDto> selectByTestItemCodes(Collection<String> testItemCodes, long orgId) {
        if (CollectionUtils.isEmpty(testItemCodes)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbTestItem> in = new LambdaQueryWrapper<TbTestItem>().eq(TbTestItem::getOrgId, orgId)
                .in(TbTestItem::getTestItemCode, testItemCodes);

        return testItemMapper.selectList(in).stream().map(this::convert).collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public List<TestItemDto> selectByTestItemCodesCached(Collection<String> testItemCodes, long orgId) {
        if (CollectionUtils.isEmpty(testItemCodes)) {
            return Collections.emptyList();
        }
        List<TestItemDto> testItemDtos = self.selectByOrgId(orgId);

        return testItemDtos.stream().filter(e -> testItemCodes.contains(e.getTestItemCode())).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByTestItemId(TestItemDto dto) {
        final TbTestItem testItem = new TbTestItem();
        BeanUtils.copyProperties(dto, testItem);

        // 这几个字段不支持修改
        testItem.setSampleTypeCode(null);
        testItem.setSampleTypeName(null);
        testItem.setEnName(null);
        testItem.setTestItemName(null);
        testItem.setTestItemCode(null);
        testItem.setStashRemark(null);
        testItem.setExamMethodCode(null);
        testItem.setExamMethodName(null);

        testItem.setUpdaterId(LoginUserHandler.get().getUserId());
        testItem.setUpdaterName(LoginUserHandler.get().getNickname());
        testItem.setUpdateDate(new Date());

        if (testItemMapper.updateById(testItem) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改检验项目 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(testItem));

        return true;
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByTestItemId(long testItemId) {
        return testItemMapper.deleteById(testItemId) > 0;
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByTestItemIds(Collection<Long> testItemIds) {
        if (CollectionUtils.isEmpty(testItemIds)) {
            return;
        }

        testItemMapper.delete(new LambdaQueryWrapper<TbTestItem>().in(TbTestItem::getTestItemId, testItemIds));
    }

    @Override
    @Cacheable(key = "'selectByGroupId:' + #groupId")
    public List<TestItemDto> selectByGroupId(long groupId) {
        return testItemMapper.selectList(new LambdaQueryWrapper<TbTestItem>().orderByDesc(TbTestItem::getTestItemId)
                .eq(TbTestItem::getGroupId, groupId)).stream().map(this::convert).collect(Collectors.toList());
    }

    @Nonnull
    @Override
    @Cacheable(key = "'selectByOrgId:' + #orgId")
    public List<TestItemDto> selectByOrgId(long orgId) {
        return testItemMapper
                .selectList(Wrappers.lambdaQuery(TbTestItem.class).eq(TbTestItem::getOrgId, orgId)
                        .eq(TbTestItem::getIsDelete, YesOrNoEnum.NO.getCode()).orderByDesc(TbTestItem::getTestItemId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public List<TestItemDto> selectByTestItemIds(Collection<Long> testItemIds) {
        if (CollectionUtils.isEmpty(testItemIds)) {
            return Collections.emptyList();
        }

        return testItemMapper.selectBatchIds(testItemIds).stream().map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByTestItemId:' + #testItemId")
    public TestItemDto selectByTestItemId(long testItemId) {
        return convert(testItemMapper.selectById(testItemId));
    }

    @Nonnull
    @Override
    public Map<Long, TestItemDto> selectByTestItemIdsAsMap(Collection<Long> testItemIds) {
        return convertListToMap(selectByTestItemIds(testItemIds));
    }

    @Override
    public Map<Long, TestItemDto> selectByOrgIdToMap(long orgId) {
        return convertListToMap(self.selectByOrgId(orgId));
    }


    public Map<Long, TestItemDto> convertListToMap(List<TestItemDto> testItems) {
        if (CollectionUtils.isEmpty(testItems)) {
            return Collections.emptyMap();
        }
        return testItems.stream().collect(Collectors.toMap(TestItemDto::getTestItemId, v -> v, (v1, v2) -> v1));
    }

    private TestItemDto convert(TbTestItem testItem) {
        if (Objects.isNull(testItem)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(testItem), TestItemDto.class);
    }

    @Override
    public List<TestItemDto> selectByGroupIds(Collection<Long> groupIds) {
        return testItemMapper.selectList(new LambdaQueryWrapper<TbTestItem>().orderByDesc(TbTestItem::getTestItemId)
                .in(TbTestItem::getGroupId, groupIds)).stream().map(this::convert).collect(Collectors.toList());
    }

}
