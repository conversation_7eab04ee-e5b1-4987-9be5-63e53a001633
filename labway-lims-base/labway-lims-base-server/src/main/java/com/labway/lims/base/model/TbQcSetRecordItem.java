package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 仪器质控记录明细
 * @TableName tb_qc_set_record_item
 */
@TableName(value ="tb_qc_set_record_item")
@Data
public class TbQcSetRecordItem implements Serializable {
    /**
     * 质控记录明细系统编号
     */
    @TableId
    private Long qcRecordItemId;

    /**
     * 记录批次
     */
    private String qcRecordBatch;

    /**
     * 质控批号id
     */
    private Long qcBatchId;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 仪器编码ID
     */
    private Long instrumentId;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器通道编码
     */
    private String instrumentChannel;

    /**
     * 质控开始日期
     */
    private Date qcStartDate;

    /**
     * 质控结束日期
     */
    private Date qcEndDate;

    /**
     * 是否有效
     */
    private Integer isUse;

    /**
     * 质控记录明细状态
     */
    private Integer status;

    /**
     * 质控记录明细状态描述
     */
    private String statusDesc;

    /**
     * 靶值
     */
    private Long targetValue;

    /**
     * 标准差
     */
    private Long standardDeviation;

    /**
     * 变异系数
     */
    private Long cvValue;

    /**
     * 水平编码
     */
    private Integer levelCode;

    /**
     * 水平编码描述
     */
    private String levelCodeDesc;

    /**
     * 质控规则集合
     */
    private String qcRulesCollection;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 上控制线
     */
    private BigDecimal upperControlLimit;

    /**
     * 下控制线
     */
    private BigDecimal lowerControlLimit;

    /**
     * 定性类型
     */
    private Integer qualitativeType;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建人名
     */
    private String createName;

    /**
     * 更新人id
     */
    private Long updateId;

    /**
     * 更新人名
     */
    private String updateName;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 自定义属性1
     */
    private String selfAttribute1;

    /**
     * 自定义属性2
     */
    private String selfAttribute2;

    /**
     * 自定义属性3
     */
    private String selfAttribute3;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 专业组id
     */
    private Long groupId;

    /**
     * 专业组编码
     */
    private String groupCode;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 仪器质控报告项id
     */
    private Long instrumentReportItemId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}