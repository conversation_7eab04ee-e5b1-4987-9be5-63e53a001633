package com.labway.lims.base.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.QcBatchDto;
import com.labway.lims.base.api.dto.QcBatchReportItemDto;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.QcBatchReportItemService;
import com.labway.lims.base.api.service.QcBatchService;
import com.labway.lims.base.mapper.TbQcBatchMapper;
import com.labway.lims.base.mapstruct.QcBatchConverter;
import com.labway.lims.base.mapstruct.QcBatchReportItemConverter;
import com.labway.lims.base.model.TbQcBatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

@Slf4j
@DubboService
public class QcBatchServiceImpl implements QcBatchService {

    @Resource
    private TbQcBatchMapper tbQcBatchMapper;

    @Resource
    private InstrumentService instrumentService;

    @Resource
    private SnowflakeService snowflakeService;

    @Resource
    private QcBatchReportItemService qcBatchReportItemService;

    @Resource
    private QcBatchConverter qcBatchConverter;

    @Resource
    private QcBatchReportItemConverter qcBatchReportItemConverter;

    @Override
    public long add(QcBatchDto dto) {

        LoginUserHandler.User user = LoginUserHandler.get();
        Long groupId = user.getGroupId();
        InstrumentDto instrument = instrumentService.selectByGroupIdAndInstrumentCode(groupId, dto.getInstrumentCode());
        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("仪器不存在");
        }

        if (this.isExistIntersectionData(groupId, instrument.getInstrumentCode(), dto.getQcBatch(), dto.getBeginDate(),
            dto.getEndDate(), 0)) {
            throw new LimsException(
                String.format("仪器 [%s] 批号 [%s] 生效时间 [%s] 至 [%s] 存在交集", instrument.getInstrumentName(), dto.getQcBatch(),
                    DateUtil.format(dto.getBeginDate(), NORM_DATETIME_PATTERN),
                    DateUtil.format(dto.getEndDate(), NORM_DATETIME_PATTERN)));
        }
        final Date now = new Date();

        TbQcBatch target = new TbQcBatch();

        target.setQcBatchId(snowflakeService.genId());
        target.setInstrumentCode(instrument.getInstrumentCode());
        target.setInstrumentName(instrument.getInstrumentName());
        target.setQcBatch(dto.getQcBatch());
        target.setQcMatieralName(StringUtils.defaultString(dto.getQcMatieralName()));
        target.setReagentBrand(StringUtils.defaultString(dto.getReagentBrand()));
        target.setBeginDate(dto.getBeginDate());
        target.setEndDate(dto.getEndDate());
        target.setSource(StringUtils.defaultString(dto.getSource()));
        target.setSampleNo(StringUtils.defaultString(dto.getSampleNo()));
        target.setLow(StringUtils.defaultString(dto.getLow()));
        target.setMedium(StringUtils.defaultString(dto.getMedium()));
        target.setHigh(StringUtils.defaultString(dto.getHigh()));
        target.setGroupName(user.getGroupName());
        target.setGroupId(groupId);
        target.setCreatorName(user.getNickname());
        target.setCreatorId(user.getUserId());
        target.setCreateDate(now);
        target.setUpdaterName(user.getNickname());
        target.setUpdaterId(user.getUserId());
        target.setUpdateDate(now);
        target.setOrgId(user.getOrgId());
        target.setOrgName(user.getOrgName());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbQcBatchMapper.insert(target) < 1) {
            throw new LimsException("添加质控批号失败");
        }
        log.info("用户 [{}] 新增质控批号[{}]成功", user.getNickname(), JSON.toJSONString(target));

        return target.getQcBatchId();
    }

    @Override
    public List<QcBatchDto> selectByInstrumentCode(long groupId, String instrumentCode) {
        return (tbQcBatchMapper
            .selectList(Wrappers.lambdaQuery(TbQcBatch.class).eq(TbQcBatch::getGroupId, groupId)
                .eq(TbQcBatch::getInstrumentCode, instrumentCode))
            .stream().map(this::convert).collect(Collectors.toList()));
    }

    @Override
    public void update(QcBatchDto qcBatchDto) {

        final Long groupId = LoginUserHandler.get().getGroupId();
        InstrumentDto instrument =
            instrumentService.selectByGroupIdAndInstrumentCode(groupId, qcBatchDto.getInstrumentCode());
        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("仪器不存在");
        }

        if (this.isExistIntersectionData(groupId, instrument.getInstrumentCode(), qcBatchDto.getQcBatch(),
            qcBatchDto.getBeginDate(), qcBatchDto.getEndDate(), qcBatchDto.getQcBatchId())) {
            throw new LimsException(
                String.format("仪器 [%s] 批号 [%s] 生效时间 [%s] 至 [%s] 存在交集", instrument.getInstrumentName(),
                    qcBatchDto.getQcBatch(), DateUtil.format(qcBatchDto.getBeginDate(), NORM_DATETIME_PATTERN),
                    DateUtil.format(qcBatchDto.getEndDate(), NORM_DATETIME_PATTERN)));
        }

        TbQcBatch update = new TbQcBatch();
        update.setQcBatchId(qcBatchDto.getQcBatchId());
        update.setInstrumentCode(instrument.getInstrumentCode());
        update.setInstrumentName(instrument.getInstrumentName());
        update.setQcBatch(qcBatchDto.getQcBatch());
        update.setSource(StringUtils.defaultString(qcBatchDto.getSource()));
        update.setReagentBrand(StringUtils.defaultString(qcBatchDto.getReagentBrand()));
        update.setQcMatieralName(StringUtils.defaultString(qcBatchDto.getQcMatieralName()));
        update.setBeginDate(qcBatchDto.getBeginDate());
        update.setEndDate(qcBatchDto.getEndDate());
        update.setSampleNo(StringUtils.defaultString(qcBatchDto.getSampleNo()));
        update.setLow(StringUtils.defaultString(qcBatchDto.getLow()));
        update.setMedium(StringUtils.defaultString(qcBatchDto.getMedium()));
        update.setHigh(StringUtils.defaultString(qcBatchDto.getHigh()));
        update.setUpdateDate(new Date());
        update.setUpdaterName(LoginUserHandler.get().getNickname());
        update.setUpdaterId(LoginUserHandler.get().getUserId());

        tbQcBatchMapper.updateById(update);

    }

    @Override
    public List<QcBatchDto> list(String instrumentCode, String qcBatch) {
        final Long groupId = LoginUserHandler.get().getGroupId();
        final LambdaQueryWrapper<TbQcBatch> eq =
                Wrappers.lambdaQuery(TbQcBatch.class).eq(TbQcBatch::getGroupId, groupId)
                        .eq(StringUtils.isNotBlank(instrumentCode), TbQcBatch::getInstrumentCode, instrumentCode)
                        .eq(StringUtils.isNotBlank(qcBatch), TbQcBatch::getQcBatch, qcBatch)
                        .orderByDesc(TbQcBatch::getUpdateDate);
        return (tbQcBatchMapper.selectList(eq).stream().map(this::convert).collect(Collectors.toList()));
    }

    @Override
    public void deleteQcBatch(Collection<Long> qcBatchIds) {
        if (CollectionUtils.isEmpty(qcBatchIds)) {
            return;
        }

        tbQcBatchMapper.deleteBatchIds(qcBatchIds);

        log.info("用户 [{}] 专业组 [{}] 删除了质控批号 {}", LoginUserHandler.get().getNickname(),
            LoginUserHandler.get().getGroupName(), qcBatchIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cloneRow(QcBatchDto source, List<QcBatchReportItemDto> sourceItems, QcBatchDto target) {

        final Long groupId = LoginUserHandler.get().getGroupId();
        target.setGroupId(groupId);
        Map<String, String> reportItemCodes = sourceItems.stream().collect(
            Collectors.toMap(QcBatchReportItemDto::getReportItemCode, QcBatchReportItemDto::getReportItemName));
        qcBatchReportItemService.checkAddQcBatchReportItemDtos(target, reportItemCodes);

        long qcBatchIdNew = this.add(target);

        if (CollectionUtils.isEmpty(sourceItems)) {
            return;
        }

        LinkedList<Long> genIds = snowflakeService.genIds(sourceItems.size());

        List<QcBatchReportItemDto> targetList = Lists.newArrayListWithCapacity(sourceItems.size());
        for (QcBatchReportItemDto sourceItem : sourceItems) {
            QcBatchReportItemDto dto = qcBatchReportItemConverter.qcBatchReportItemDtoFromTbDto(sourceItem);
            dto.setBatchReportItemId(genIds.pop());
            dto.setQcBatchId(qcBatchIdNew);
            dto.setQcBatch(target.getQcBatch());
            targetList.add(dto);
        }
        qcBatchReportItemService.addQcBatchReportItemDtos(targetList);
    }

    @Override
    @Nullable
    public QcBatchDto selectByQcBatchId(long qcBatchId) {
        LambdaQueryWrapper<TbQcBatch> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbQcBatch::getQcBatchId, qcBatchId);
        queryWrapper.eq(TbQcBatch::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return qcBatchConverter.qcBatchDtoFromTbObj(tbQcBatchMapper.selectOne(queryWrapper));
    }

    @Nullable
    @Override
    public List<QcBatchDto> selectByQcBatchIds(Collection<Long> qcBatchIds) {
        LambdaQueryWrapper<TbQcBatch> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbQcBatch::getQcBatchId, qcBatchIds);
        queryWrapper.eq(TbQcBatch::getIsDelete, YesOrNoEnum.NO.getCode());

        return qcBatchConverter.qcBatchDtoListFromTbObj(tbQcBatchMapper.selectList(queryWrapper));
    }

    @Override
    public boolean isExistIntersectionData(long groupId, String instrumentCode, String qcBatch, Date startDate,
        Date endDate, long excludeQcBatchId) {
        LambdaQueryWrapper<TbQcBatch> queryWrapper = Wrappers.lambdaQuery();

        // 排除 传入 id
        queryWrapper.ne(excludeQcBatchId > 0, TbQcBatch::getQcBatchId, excludeQcBatchId);

        // 时间 存在交集
        queryWrapper.and(wrapper -> wrapper
            .and(w1 -> w1.ge(TbQcBatch::getBeginDate, startDate).and(w2 -> w2.le(TbQcBatch::getBeginDate, endDate)))
            .or(w3 -> w3.le(TbQcBatch::getBeginDate, startDate).and(w4 -> w4.ge(TbQcBatch::getEndDate, endDate)))
            .or(w5 -> w5.ge(TbQcBatch::getEndDate, startDate).and(w6 -> w6.le(TbQcBatch::getEndDate, endDate))));

        // 专业组 仪器 批号
        queryWrapper.eq(TbQcBatch::getGroupId, groupId);
        queryWrapper.eq(TbQcBatch::getQcBatch, qcBatch);
        queryWrapper.eq(TbQcBatch::getInstrumentCode, instrumentCode);
        queryWrapper.eq(TbQcBatch::getIsDelete, YesOrNoEnum.NO.getCode());
        return tbQcBatchMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public List<QcBatchDto> selectByByInstrumentCodeAndDateRange(Collection<String> instrumentCodes, Date minDate,
        Date maxDate) {
        if (CollectionUtils.isEmpty(instrumentCodes) || Objects.isNull(minDate) || Objects.isNull(maxDate)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbQcBatch> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.in(TbQcBatch::getInstrumentCode, instrumentCodes);

        // 时间 存在交集
        queryWrapper.and(wrapper -> wrapper
            .and(w1 -> w1.ge(TbQcBatch::getBeginDate, minDate).and(w2 -> w2.le(TbQcBatch::getBeginDate, maxDate)))
            .or(w3 -> w3.le(TbQcBatch::getBeginDate, minDate).and(w4 -> w4.ge(TbQcBatch::getEndDate, maxDate)))
            .or(w5 -> w5.ge(TbQcBatch::getEndDate, minDate).and(w6 -> w6.le(TbQcBatch::getEndDate, maxDate))));

        return qcBatchConverter.qcBatchDtoListFromTbObj(tbQcBatchMapper.selectList(queryWrapper));
    }

    @Override
    public List<QcBatchDto> selectIntersectionData(long groupId, String instrumentCode, Date startDate, Date endDate) {
        LambdaQueryWrapper<TbQcBatch> queryWrapper = Wrappers.lambdaQuery();

        // 时间 存在交集
        queryWrapper.and(wrapper -> wrapper
            .and(w1 -> w1.ge(TbQcBatch::getBeginDate, startDate).and(w2 -> w2.le(TbQcBatch::getBeginDate, endDate)))
            .or(w3 -> w3.le(TbQcBatch::getBeginDate, startDate).and(w4 -> w4.ge(TbQcBatch::getEndDate, endDate)))
            .or(w5 -> w5.ge(TbQcBatch::getEndDate, startDate).and(w6 -> w6.le(TbQcBatch::getEndDate, endDate))));

        // 专业组 仪器
        queryWrapper.eq(TbQcBatch::getGroupId, groupId);
        queryWrapper.eq(TbQcBatch::getInstrumentCode, instrumentCode);
        queryWrapper.eq(TbQcBatch::getIsDelete, YesOrNoEnum.NO.getCode());
        return qcBatchConverter.qcBatchDtoListFromTbObj(tbQcBatchMapper.selectList(queryWrapper));
    }

    public QcBatchDto convert(TbQcBatch tb) {
        return JSON.parseObject(JSON.toJSONString(tb), QcBatchDto.class);
    }
}
