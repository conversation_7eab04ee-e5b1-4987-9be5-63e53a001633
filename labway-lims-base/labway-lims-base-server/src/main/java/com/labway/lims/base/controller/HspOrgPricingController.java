package com.labway.lims.base.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrgPricingDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrgPricingService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.vo.HspOrgPricingAddRequestVo;
import com.labway.lims.base.vo.HspOrgPricingUpdateRequestVo;
import com.labway.lims.base.vo.SelectHspOrgPricingAllResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.api.ConditionCheckUtils.isDecimalWithDigits;
import static com.labway.lims.api.ConditionCheckUtils.isDecimalWithTwoDigits;

/**
 * 客户阶梯折扣信息 API
 *
 * <AUTHOR>
 * @since 2023/5/4 13:46
 */
@Slf4j
@RestController
@RequestMapping("/hsp-org-pricing")
public class HspOrgPricingController extends BaseController {

    @Resource
    private HspOrgPricingService hspOrgPricingService;

    @Resource
    private HspOrganizationService hspOrganizationService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 阶梯折扣 新增
     */
    @PostMapping("/add")
    public Object hspOrgPricingAdd(@RequestBody HspOrgPricingAddRequestVo vo) {
        if (Objects.isNull(vo.getHspOrgId()) || Objects.isNull(vo.getStartDate()) || Objects.isNull(vo.getEndDate())
            || StringUtils.isAnyBlank(vo.getBeforeMinPrice(), vo.getBeforeMaxPrice(), vo.getDiscount())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        final HspOrganizationDto hspOrganizationDto = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(hspOrganizationDto)) {
            throw new LimsException("送检机构不存在");
        }

        BigDecimal beforeMinPriceInt = new BigDecimal(vo.getBeforeMinPrice());
        BigDecimal beforeMaxPriceInt = new BigDecimal(vo.getBeforeMaxPrice());
        BigDecimal discountInt = BigDecimal.valueOf(NumberUtils.toDouble(vo.getDiscount()));

        if (hspOrgPricingService.isExistIntersectionData(vo.getHspOrgId(), vo.getStartDate(), vo.getEndDate(),
            beforeMinPriceInt, beforeMaxPriceInt, 0)) {
            throw new LimsException("折前总额上下限范围已存在，请重新输入");
        }

        HspOrgPricingDto dto = new HspOrgPricingDto();
        BeanUtils.copyProperties(vo, dto);

        dto.setBeforeMinPrice(beforeMinPriceInt);
        dto.setBeforeMaxPrice(beforeMaxPriceInt);
        dto.setDiscount(discountInt);
        dto.setHspOrgName(hspOrganizationDto.getHspOrgName());

        final long id = hspOrgPricingService.addHspOrgPricing(dto);

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.CUSTOMER_SPECIAL_MAINTENANCE.getDesc()).setType("新增")
                .setContent(String.format("新增客户阶梯折扣价维护 送检机构 [%s] 折前总额下限 [%s] 折前总额上限 [%s] 折扣率 [%s] 生效日期 [%s] 结束日期 [%s]",
                    hspOrganizationDto.getHspOrgName(), vo.getBeforeMinPrice(), vo.getBeforeMaxPrice(),
                    vo.getDiscount(), DateUtil.formatDate(vo.getStartDate()), DateUtil.formatDate(vo.getEndDate())))
                .toJSONString());

        return Map.of("id", id);
    }

    /**
     * 阶梯折扣 修改
     */
    @PostMapping("/update")
    public Object hspOrgPricingUpdate(@RequestBody HspOrgPricingUpdateRequestVo vo) {
        if (Objects.isNull(vo.getStartDate()) || Objects.isNull(vo.getEndDate())
            || StringUtils.isAnyBlank(vo.getBeforeMinPrice(), vo.getBeforeMaxPrice(), vo.getDiscount())
            || Objects.isNull(vo.getTieredPriceId())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }

        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        // 判断 阶梯折扣是否存在
        final HspOrgPricingDto hspOrgPricingDtoNow = hspOrgPricingService.selectByTieredPriceId(vo.getTieredPriceId());
        if (Objects.isNull(hspOrgPricingDtoNow)) {
            throw new LimsException("对应阶梯折扣不存在");
        }
        BigDecimal beforeMinPriceInt = new BigDecimal(vo.getBeforeMinPrice());
        BigDecimal beforeMaxPriceInt = new BigDecimal(vo.getBeforeMaxPrice());

        BigDecimal discountInt = BigDecimal.valueOf(NumberUtils.toDouble(vo.getDiscount()));

        if (hspOrgPricingService.isExistIntersectionData(hspOrgPricingDtoNow.getHspOrgId(), vo.getStartDate(),
            vo.getEndDate(), beforeMinPriceInt, beforeMaxPriceInt, vo.getTieredPriceId())) {
            throw new LimsException("折前总额上下限范围已存在，请重新输入");
        }

        final HspOrgPricingDto target = new HspOrgPricingDto();
        BeanUtils.copyProperties(hspOrgPricingDtoNow, target);

        // 更新项
        target.setBeforeMinPrice(beforeMinPriceInt);
        target.setBeforeMaxPrice(beforeMaxPriceInt);
        target.setDiscount(discountInt);
        target.setStartDate(vo.getStartDate());
        target.setEndDate(vo.getEndDate());

        hspOrgPricingService.updateByTieredPriceId(target);

        @SuppressWarnings("all")
        final StringBuilder sb = new StringBuilder();
        sb.append(
            String.format("折前总额下限从 [%s] 修改成 [%s]", hspOrgPricingDtoNow.getBeforeMinPrice(), vo.getBeforeMinPrice()));
        sb.append(
            String.format("折前总额上限从 [%s] 修改成 [%s]", hspOrgPricingDtoNow.getBeforeMaxPrice(), vo.getBeforeMaxPrice()));
        sb.append(String.format("折扣率从 [%s] 修改成 [%s]", hspOrgPricingDtoNow.getDiscount(), vo.getDiscount()));
        sb.append(String.format("生效日期从 [%s] 修改成 [%s]", DateUtil.formatDate(hspOrgPricingDtoNow.getStartDate()),
            DateUtil.formatDate(vo.getStartDate())));
        sb.append(String.format("结束日期从 [%s] 修改成 [%s]", DateUtil.formatDate(hspOrgPricingDtoNow.getEndDate()),
            DateUtil.formatDate(vo.getEndDate())));

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.CUSTOMER_SPECIAL_MAINTENANCE.getDesc()).setType("修改")
                .setContent(String.format("修改客户阶梯折扣价维护 送检机构 [%s] %n%s", hspOrgPricingDtoNow.getHspOrgName(), sb))
                .toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 阶梯折扣 查看
     */
    @PostMapping("/select-all")
    public Object hspOrgPricingList() {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        List<HspOrgPricingDto> hspOrgPricingDtos = hspOrgPricingService.selectByOrgId(loginUser.getOrgId());

        // 所有 送检结构id
        final Set<Long> hspOrgIds =
            hspOrgPricingDtos.stream().map(HspOrgPricingDto::getHspOrgId).collect(Collectors.toSet());

        final List<HspOrganizationDto> hspOrganizationDtos = hspOrganizationService.selectByHspOrgIds(hspOrgIds);

        final Map<Long, HspOrganizationDto> hspOrganizationDtoByHspOrgId = hspOrganizationDtos.stream()
            .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity()));

        HspOrganizationDto ext = new HspOrganizationDto();

        List<SelectHspOrgPricingAllResponseVo> targetList = Lists.newArrayListWithCapacity(hspOrgPricingDtos.size());
        for (HspOrgPricingDto item : hspOrgPricingDtos) {
            SelectHspOrgPricingAllResponseVo temp =
                JSON.parseObject(JSON.toJSONString(item), SelectHspOrgPricingAllResponseVo.class);
            HspOrganizationDto hspOrganizationDto =
                ObjectUtils.defaultIfNull(hspOrganizationDtoByHspOrgId.get(item.getHspOrgId()), ext);
            // 金额展示
            String beforeMinPriceStr = String.valueOf(item.getBeforeMinPrice());
            String beforeMaxPriceStr = String.valueOf(item.getBeforeMaxPrice());
            String discountStr = String.valueOf(item.getDiscount());

            temp.setBeforeMinPrice(beforeMinPriceStr);
            temp.setBeforeMaxPrice(beforeMaxPriceStr);
            temp.setDiscount(discountStr);
            temp.setHspOrgCode(hspOrganizationDto.getHspOrgCode());
            targetList.add(temp);
        }
        return targetList;
    }

    /**
     * 检查 阶梯折扣 新增 或 修改 参数 公共部分
     */
    private <T extends HspOrgPricingAddRequestVo> void checkVoWhenAddOrUpdate(T vo) {
        if (!(isDecimalWithTwoDigits(vo.getBeforeMaxPrice()) && isDecimalWithTwoDigits(vo.getBeforeMinPrice()))) {
            throw new IllegalArgumentException("折前总额上下限不规范:只允许填写数字且最多允许两位小数");
        }
        if (!isDecimalWithDigits(vo.getDiscount(), 4) || NumberUtils.toDouble(vo.getDiscount()) > NumberUtils.DOUBLE_ONE
            || Objects.equals(NumberUtils.toDouble(vo.getDiscount()), NumberUtils.DOUBLE_ZERO)) {
            throw new IllegalArgumentException("折扣率不规范:只允许填写小数及1且最多允许四位小数");
        }
        if (NumberUtils.toDouble(vo.getBeforeMinPrice()) > NumberUtils.toDouble(vo.getBeforeMaxPrice())) {
            throw new IllegalArgumentException("折前总额上下限不规范:下限大于上限");
        }
        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束日期不可小于生效日期");
        }
    }

}
