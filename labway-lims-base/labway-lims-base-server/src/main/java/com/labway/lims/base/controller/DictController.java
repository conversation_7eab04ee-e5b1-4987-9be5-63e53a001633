package com.labway.lims.base.controller;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.service.TestItemPublishService;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.api.enums.base.SystemParamMetricEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.vo.DictItemVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 字典相关
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/dict")
public class DictController extends BaseController {

    @Resource
    private DictService dictService;
    @Resource
    private EnvDetector envDetector;

    @Resource
    private TestItemPublishService testItemPublishService;

    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private SystemParamService systemParamService;

    /**
     * 删除字典
     */
    @PostMapping("/delete")
    public Object deleteDictItem(@RequestBody List<Long> dictIds) {
        if (CollectionUtils.isEmpty(dictIds)) {
            throw new IllegalArgumentException("请选择需要删除的字典");
        }
        List<DictItemDto> dictItemDtos = dictService.selectByDictIds(dictIds);

        dictService.deleteByDictIds(dictIds);
        dictItemDtos.forEach(item -> {
            String message = TraceLog.newInstance()
                    .setModule(String.format(TraceLogModuleEnum.BASE_DICT.getDesc(),
                            DictEnum.selectDescByName(item.getDictType())))
                    .setContent(String.format("删除编码为 [%s] 值为 [%s] 字典", item.getDictCode(), item.getDictName()))
                    .toJSONString();
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY, message);
            log.info("发送消息：{}", message);

        });
        return Collections.emptyMap();
    }

    /**
     * 查询全部字典
     */
    @GetMapping("/group-all")
    public Object dicts() {
        final List<DictItemDto> dictItems = dictService.selectAll();

        final Map<String, List<DictItemVo>> map = new LinkedHashMap<>(dictItems.stream()
                .filter(f -> Objects.equals(f.getEnable(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.groupingBy(DictItemDto::getDictType, Collectors.mapping(v -> JSON.parseObject(JSON.toJSONString(v),
                        DictItemVo.class), Collectors.toList()))));

        map.put(DictEnum.SAMPLE_TYPE.name(), Collections.emptyList());

        if (!envDetector.isDev()) {

            log.info("用户 [{}] 开始从业务中台获取样本类型字典", LoginUserHandler.get().getNickname());
            final StopWatch watch = new StopWatch();
            watch.start();

            try {

                List<DictItemDto> sampleTypes = dictService.selectAllSampleType();
                map.put(DictEnum.SAMPLE_TYPE.name(), sampleTypes.stream().map(e -> {
                    final DictItemVo v = new DictItemVo();
                    v.setDictName(e.getDictName());
                    v.setDictCode(e.getDictCode());
                    v.setEnable(YesOrNoEnum.YES.getCode());
                    return v;
                }).collect(Collectors.toList()));

            } catch (Exception e) {
                log.error("获取业务中台字典样本类型失败", e);
            }

            watch.stop();
            log.info("用户 [{}] 从业务中台获取样本类型字典完毕 耗时 [{}ms]", LoginUserHandler.get().getNickname(),
                    watch.getTotal(TimeUnit.MILLISECONDS));
        }

        final List<SystemParamDto> systemParams = systemParamService.selectByOrgId(LoginUserHandler.get().getOrgId());
        map.put("SYSTEM_GLOBAL_PARAMS", systemParams.stream().filter(e -> Objects.equals(e.getParamMetric(), SystemParamMetricEnum.WHOLE.name())).map(e -> {
            final DictItemVo v = new DictItemVo();
            v.setDictName(e.getParamName());
            v.setDictCode(e.getParamValue());
            v.setEnable(e.getEnable());
            return v;
        }).collect(Collectors.toList()));

        map.put("SYSTEM_ALONE_PARAMS", systemParams.stream().filter(e -> Objects.equals(e.getParamMetric(), SystemParamMetricEnum.SINGLE.name())).map(e -> {
            final DictItemVo v = new DictItemVo();
            v.setDictName(e.getParamName());
            v.setDictCode(e.getParamValue());
            v.setEnable(e.getEnable());
            return v;
        }).collect(Collectors.toList()));

        return map;
    }

    /**
     * 根据字典分类查询详情
     */
    @GetMapping("/selectByDictType")
    public Object selectByDictType(@RequestParam(required = false) String dictType) {
        if (StringUtils.isBlank(dictType)) {
            return Collections.emptyList();
        }

        final List<DictItemDto> dictItems = dictService.selectByDictType(dictType);

        return JSON.parseArray(JSON.toJSONString(dictItems), DictItemVo.class);
    }

    /**
     * 修改字典信息
     */
    @PostMapping("/update")
    public Object updateDictItem(@RequestBody DictItemVo vo) {
        final Long dictId = vo.getDictId();
        if (Objects.isNull(dictId)) {
            throw new IllegalArgumentException("字典ID不能为空");
        }

        if (Objects.isNull(vo.getDictName())) {
            throw new IllegalArgumentException("字典名称不能为空");
        }

        if (StringUtils.length(vo.getDictName()) > TEXTAREA_MAX_LENGTH * 2) {
            throw new IllegalArgumentException(String.format("字典名称长度不能超过 %s 个字符", TEXTAREA_MAX_LENGTH * 2));
        }

        DictItemDto dictItemDtoOld = dictService.selectByDictId(vo.getDictId());
        if (Objects.isNull(dictItemDtoOld)) {
            throw new IllegalArgumentException("字典不存在");
        }

        final DictItemDto dictItem = JSON.parseObject(JSON.toJSONString(vo), DictItemDto.class);

        dictService.updateDictItem(dictItem);

        String compare = new CompareUtils<DictItemDto>().compare(dictItemDtoOld, dictItem);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY, TraceLog.newInstance()
                    .setModule(String.format(TraceLogModuleEnum.BASE_DICT.getDesc(),
                            DictEnum.selectDescByName(dictItemDtoOld.getDictType())))
                    .setContent(String.format("修改编码 [%s] 字典: [%s]", dictItemDtoOld.getDictCode(), compare)).toJSONString());
        }

        return Collections.emptyMap();
    }

    /**
     * 添加字典
     */
    @PostMapping("/add")
    public Object addDictItem(@RequestBody DictItemVo vo) {
        final String dictName = vo.getDictName();
        final String dictType = vo.getDictType();
        final String dictCode = vo.getDictCode();

        if (StringUtils.isBlank(dictType)) {
            throw new IllegalArgumentException("字典类型不能为空");
        }

        if (StringUtils.isBlank(dictName)) {
            throw new IllegalArgumentException("字典名称不能为空");
        }

        if (StringUtils.length(dictName) > TEXTAREA_MAX_LENGTH * 2) {
            throw new IllegalArgumentException(String.format("字典名称长度不能超过 %s 个字符", TEXTAREA_MAX_LENGTH * 2));
        }

        if (StringUtils.isBlank(dictCode)) {
            throw new IllegalArgumentException("字典编码不能为空");
        }

        if (StringUtils.length(dictCode) > TEXTAREA_MAX_LENGTH * 2) {
            throw new IllegalArgumentException(String.format("字典编码长度不能超过 %s 字符", TEXTAREA_MAX_LENGTH * 2));
        }
        final DictItemDto dictItem = JSON.parseObject(JSON.toJSONString(vo), DictItemDto.class);
        dictItem.setDataSource(1);
        long id = dictService.addDictItem(dictItem);

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance()
                        .setModule(String.format(TraceLogModuleEnum.BASE_DICT.getDesc(), DictEnum.selectDescByName(dictType)))
                        .setContent(String.format("新增编码为 [%s] 值为 [%s] 字典", dictCode, dictName)).toJSONString());

        return Map.of("id", id);
    }

    /**
     * 获取所有的字典类型
     */
    @GetMapping("/dict-types")
    public Object selectAllDictType() {
        return Arrays.stream(DictEnum.values()).map(Object::toString).collect(Collectors.toList());
    }


    /**
     * 获取所有的样本类型--包括未启用的
     */
    @GetMapping("/queryAllSampleType")
    public Object queryAllSampleType() {
        return dictService.queryAllSampleType();
    }


}
