package com.labway.lims.base.vo;

import com.labway.lims.api.enums.finance.FinanceLockEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/12 10:11
 */
@Getter
@Setter
public class FinanceOrgLockAddVo {

    /**
     * 送检机构ID
     */
    private List<Long> hspOrgIds;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 原因
     */
    private String reason;

    /**
     * 锁状态
     *
     * @see FinanceLockEnum
     * 0解锁  1加锁
     */
    private Integer status;

}
