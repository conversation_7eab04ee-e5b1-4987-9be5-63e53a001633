package com.labway.lims.base.service;

import com.labway.lims.base.api.service.QcSampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @Date 2023/11/6 11:18
 * @Version 1.0
 */
@DubboService
@Slf4j
public class QcSampleResultServiceImpl implements QcSampleResultService {
//    @Override
//    public List<QcSampleResultRespDto> qcConcentrationData(QcConcentrationQueryDto qcConcentrationQueryDto) {
//        return null;
//    }
//
//    @Override
//    public Map<String, String> receiveQcResults(ReceiveQcResultsDto dto) {
//        return null;
//    }
//
//    @Override
//    public void qcAuditSampleResultOrRemove(Integer operationType, String batchIds, String reportId) {
//
//    }
//
//    @Override
//    public String updateQcSampleResult(UpdateQcSampleResultDto updateQcSampleResultDto) {
//        return null;
//    }
//
//    @Override
//    public String addQcSampleResult(AddQcSampleResultDto addQcSampleResultDto) {
//        return null;
//    }
//
//    @Override
//    public List<QcSampleResultDto> selectByCustomerReportItemId(String customerReportItemId, Date beginTestDate, Date endTestDate) {
//        return null;
//    }
//
//    @Override
//    public List<QcSampleResultDto> selectAuditResultByCustomerReportItemId(String customerReportItemId, LocalDateTime beginTestDate, LocalDateTime endTestDate) {
//        return null;
//    }
//
//    @Override
//    public void cancelQcResultAudit(List<String> recordBatchs) {
//
//    }
}
