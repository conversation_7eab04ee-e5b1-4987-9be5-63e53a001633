package com.labway.lims.base.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.HspOrgDiscountService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.mapper.TbHspOrgDiscountMapper;
import com.labway.lims.base.mapstruct.HspOrgDiscountConverter;
import com.labway.lims.base.model.TbHspOrgDiscount;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@Slf4j
@DubboService
public class HspOrgDiscountServiceImpl implements HspOrgDiscountService {
    @Resource
    private TbHspOrgDiscountMapper tbHspOrgDiscountMapper;

    @Resource
    private ItemPriceBasePackageService itemPriceBasePackageService;

    @Resource
    private HspOrganizationService hspOrganizationService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private HspOrgDiscountConverter hspOrgDiscountConverter;

    @Override
    public List<HspOrgDiscountDto> list(@Nullable Long hspOrgId, @Nullable String applyType) {

        final LambdaQueryWrapper<TbHspOrgDiscount> order = Wrappers.lambdaQuery(TbHspOrgDiscount.class)
            //
            .eq(TbHspOrgDiscount::getOrgId, LoginUserHandler.get().getOrgId())
            //
            .eq(Objects.nonNull(hspOrgId), TbHspOrgDiscount::getHspOrgId, hspOrgId)
            //
            .eq(StringUtils.isNotBlank(applyType), TbHspOrgDiscount::getSendTypeCode, applyType)
            //
            .orderByDesc(TbHspOrgDiscount::getDiscountId);

        return JSON.parseArray(JSON.toJSONString(tbHspOrgDiscountMapper.selectList(order)), HspOrgDiscountDto.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<HspOrgDiscountDto> addHspOrgBasePackage(AddHspOrgBasePackageDto param) {
        Collection<Long> hspOrgIds = param.getHspOrgIds();
        Date startDate = param.getStartDate();
        Date endDate = param.getEndDate();
        Long packageId = param.getPackageId();
        Map<String, String> applyTypeByCode =
            param.getSendTypeList().stream().collect(Collectors.toMap(AddHspOrgBasePackageSendTypeDto::getSendTypeCode,
                AddHspOrgBasePackageSendTypeDto::getSendType, (key1, key2) -> key1));

        final ItemPriceBasePackageDto itemPriceBasePackage = itemPriceBasePackageService.selectById(packageId);
        if (Objects.isNull(itemPriceBasePackage)) {
            throw new IllegalStateException("基准包不存在");
        }

        // 选择的时间范围内在基准包的生效时间范围内
        if (itemPriceBasePackage.getStartDate().after(startDate) || itemPriceBasePackage.getEndDate().before(endDate)) {
            throw new IllegalStateException(String.format("选择的时间范围 [%s - %s] 不在基准包 [%s] 生效时间范围内 [%s - %s]",
                DateUtil.formatDate(startDate), DateUtil.formatDate(endDate), itemPriceBasePackage.getPackageName(),
                DateUtil.formatDate(itemPriceBasePackage.getStartDate()),
                DateUtil.formatDate(itemPriceBasePackage.getEndDate())));
        }

        final List<HspOrganizationDto> hspOrganizations = hspOrganizationService.selectByHspOrgIds(hspOrgIds);
        if (CollectionUtils.isEmpty(hspOrganizations)) {
            throw new IllegalStateException("送检机构不存在");
        }

        // 同一个送检机构，在同一个时间，同一送检类型只存在一个基准包
        Map<Long, Map<String, List<HspOrgDiscountDto>>> groupingByHspOrgId =
            this.selectByHspOrgIdsAndDateRange(hspOrgIds, applyTypeByCode.keySet(), startDate, endDate).stream()
                .collect(Collectors.groupingBy(HspOrgDiscountDto::getHspOrgId, // 第一级分组
                    Collectors.groupingBy(HspOrgDiscountDto::getSendTypeCode))); // 第二级分组

        // 校验是否有重复的
        for (final HspOrganizationDto hspOrganization : hspOrganizations) {
            Map<String, List<HspOrgDiscountDto>> groupingBySendTypeCode =
                groupingByHspOrgId.get(hspOrganization.getHspOrgId());
            if (Objects.isNull(groupingBySendTypeCode)) {
                continue;
            }
            for (Map.Entry<String, String> entry : applyTypeByCode.entrySet()) {
                List<HspOrgDiscountDto> hspOrgDiscountDtoList = groupingBySendTypeCode.get(entry.getKey());
                if (CollectionUtils.isNotEmpty(hspOrgDiscountDtoList)) {
                    throw new IllegalStateException(
                        String.format("送检机构 [%s] 送检类型 [%s] 日期范围已存在 [%s - %s]", hspOrganization.getHspOrgName(),
                            entry.getValue(), DateUtil.formatDate(startDate), DateUtil.formatDate(endDate)));

                }
            }

        }

        final Date now = new Date();
        final Long userId = LoginUserHandler.get().getUserId();
        final Long orgId = LoginUserHandler.get().getOrgId();
        final String orgName = LoginUserHandler.get().getOrgName();
        final String nikeName = LoginUserHandler.get().getNickname();

        final LinkedList<Long> ids = snowflakeService.genIds(hspOrganizations.size() * applyTypeByCode.size());
        List<HspOrgDiscountDto> list = Lists.newArrayList();
        for (HspOrganizationDto hspOrganization : hspOrganizations) {
            for (Map.Entry<String, String> entry : applyTypeByCode.entrySet()) {
                HspOrgDiscountDto dto = new HspOrgDiscountDto();
                dto.setDiscountId(ids.pop());
                dto.setPackageId(packageId);
                dto.setHspOrgId(hspOrganization.getHspOrgId());
                dto.setHspOrgName(hspOrganization.getHspOrgName());
                dto.setSendTypeCode(entry.getKey());
                dto.setSendType(entry.getValue());
                dto.setStartDate(startDate);
                dto.setEndDate(endDate);
                // 保留两位小数
                dto.setDiscount(param.getDiscount().setScale(4, RoundingMode.HALF_UP));
                dto.setOrgId(orgId);
                dto.setOrgName(orgName);
                dto.setCreateDate(now);
                dto.setUpdateDate(now);
                dto.setUpdaterId(userId);
                dto.setUpdaterName(nikeName);
                dto.setCreatorId(userId);
                dto.setCreatorName(nikeName);
                dto.setIsDelete(YesOrNoEnum.NO.getCode());
                list.add(dto);
            }
        }

        tbHspOrgDiscountMapper.addBatch(list);

        log.info("用户 [{}] 专业组 [{}] 新增送检机构基准包成功, param: {}", LoginUserHandler.get().getNickname(),
            LoginUserHandler.get().getGroupName(), JSON.toJSONString(param));
        return list;
    }

    @Override
    public HspOrgDiscountCompareInfo updateHspOrgDiscount(UpdateHspOrgDiscountDto param) {
        final Long discountId = param.getDiscountId();
        final String sendTypeName = param.getSendType();
        final String sendTypeCode = param.getSendTypeCode();
        final Date startDate = param.getStartDate();
        final Date endDate = param.getEndDate();
        final BigDecimal discount = param.getDiscount();
        final Long packageId = param.getPackageId();

        final HspOrgDiscountDto discountObj = selectById(discountId);
        if (Objects.isNull(discountObj)) {
            throw new IllegalStateException("送检机构基准包不存在");
        }

        final ItemPriceBasePackageDto itemPriceBasePackage =
            itemPriceBasePackageService.selectById(param.getPackageId());
        if (Objects.isNull(itemPriceBasePackage)) {
            throw new IllegalStateException("基准包不存在");
        }

        // 选择的时间范围内在基准包的生效时间范围内
        if (itemPriceBasePackage.getStartDate().after(startDate) || itemPriceBasePackage.getEndDate().before(endDate)) {
            throw new IllegalStateException(String.format("选择的时间范围 [%s - %s] 不在基准包 [%s] 生效时间范围内 [%s - %s]",
                DateUtil.formatDate(startDate), DateUtil.formatDate(endDate), itemPriceBasePackage.getPackageName(),
                DateUtil.formatDate(itemPriceBasePackage.getStartDate()),
                DateUtil.formatDate(itemPriceBasePackage.getEndDate())));
        }

        // 同一个送检机构，在同一个时间，同一送检类型只存在一个基准包
        List<HspOrgDiscountDto> hspOrgDiscountDtoList = this
            .selectByHspOrgIdsAndDateRange(Collections.singleton(discountObj.getHspOrgId()),
                Collections.singleton(sendTypeCode), startDate, endDate)
            .stream().filter(obj -> !Objects.equals(obj.getDiscountId(), discountId)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(hspOrgDiscountDtoList)) {
            throw new IllegalStateException(
                String.format("送检机构 [%s] 送检类型 [%s] 日期范围已存在 [%s - %s]", discountObj.getHspOrgName(), sendTypeName,
                    DateUtil.formatDate(startDate), DateUtil.formatDate(endDate)));

        }

        discountObj.setDiscount(discount.setScale(4, RoundingMode.HALF_UP));
        discountObj.setPackageId(packageId);
        discountObj.setStartDate(startDate);
        discountObj.setEndDate(endDate);
        discountObj.setSendTypeCode(sendTypeCode);
        discountObj.setSendType(sendTypeName);
        discountObj.setUpdateDate(new Date());
        discountObj.setUpdaterId(LoginUserHandler.get().getUserId());
        discountObj.setUpdaterName(LoginUserHandler.get().getNickname());

        tbHspOrgDiscountMapper.updateById(JSON.parseObject(JSON.toJSONString(discountObj), TbHspOrgDiscount.class));

        log.info("用户 [{}] 专业组 [{}] 修改送检机构基准包成功, param: {}", LoginUserHandler.get().getNickname(),
            LoginUserHandler.get().getGroupName(), JSON.toJSONString(param));

        HspOrgDiscountCompareInfo compareInfo = new HspOrgDiscountCompareInfo();
        compareInfo.setPackageName(itemPriceBasePackage.getPackageName());
        compareInfo.setSendType(sendTypeName);
        compareInfo.setStartDateStr(DateUtil.formatDate(startDate));
        compareInfo.setEndDateStr(DateUtil.formatDate(endDate));
        compareInfo.setDiscount(discount.setScale(4, RoundingMode.HALF_UP));
        return compareInfo;
    }

    @Override
    public HspOrgDiscountDto selectById(long discountId) {
        return JSON.parseObject(JSON.toJSONString(tbHspOrgDiscountMapper.selectById(discountId)),
            HspOrgDiscountDto.class);
    }

    @Override
    public List<HspOrgDiscountDto> selectByHspOrgIdsAndDateRange(Collection<Long> hspOrgIds,
        Collection<String> applyTypes, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(hspOrgIds) || CollectionUtils.isEmpty(applyTypes) || Objects.isNull(startDate)
            || Objects.isNull(endDate)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbHspOrgDiscount> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.in(TbHspOrgDiscount::getHspOrgId, hspOrgIds);
        queryWrapper.in(TbHspOrgDiscount::getSendTypeCode, applyTypes);

        // 时间 存在交集
        queryWrapper.and(wrapper -> wrapper
            .and(w1 -> w1.ge(TbHspOrgDiscount::getStartDate, startDate)
                .and(w2 -> w2.le(TbHspOrgDiscount::getStartDate, endDate)))
            .or(w3 -> w3.le(TbHspOrgDiscount::getStartDate, startDate)
                .and(w4 -> w4.ge(TbHspOrgDiscount::getEndDate, endDate)))
            .or(w5 -> w5.ge(TbHspOrgDiscount::getEndDate, startDate)
                .and(w6 -> w6.le(TbHspOrgDiscount::getEndDate, endDate))));

        return hspOrgDiscountConverter
            .hspOrgDiscountDtoListFromTbObjList(tbHspOrgDiscountMapper.selectList(queryWrapper));

    }

    @Override
    public List<DiscountDetailDto> selectDiscountDetailByHspOrgIdsAndDateRange(Collection<Long> hspOrgIds,
        Set<String> applyTypes, Date minDate, Date maxDate) {
        if (CollectionUtils.isEmpty(hspOrgIds) || CollectionUtils.isEmpty(applyTypes) || Objects.isNull(minDate)
            || Objects.isNull(maxDate)) {
            return List.of();
        }
        return tbHspOrgDiscountMapper.selectDiscountDetailByHspOrgIdsAndDateRange(hspOrgIds, applyTypes, minDate,
            maxDate);
    }

    @Override
    public List<HspOrgDiscountDto> selectByPackageId(long packageId) {
        return JSON.parseArray(
            JSON.toJSONString(tbHspOrgDiscountMapper.selectList(
                Wrappers.lambdaQuery(TbHspOrgDiscount.class).eq(TbHspOrgDiscount::getPackageId, packageId))),
            HspOrgDiscountDto.class);
    }

    @Nullable
    @Override
    public HspOrgDiscountDto selectByApplyTypeAndDate(Long hspOrgId, String applyTypeCode, Date sampleItemCreateDate) {
        if (Objects.isNull(hspOrgId) || StringUtils.isBlank(applyTypeCode) || Objects.isNull(sampleItemCreateDate)) {
            return null;
        }
        LambdaQueryWrapper<TbHspOrgDiscount> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(TbHspOrgDiscount::getHspOrgId, hspOrgId);
        queryWrapper.eq(TbHspOrgDiscount::getSendTypeCode, applyTypeCode);

        // 时间点 所在时间范围
        queryWrapper.ge(TbHspOrgDiscount::getEndDate, sampleItemCreateDate);
        queryWrapper.le(TbHspOrgDiscount::getStartDate, sampleItemCreateDate);

        queryWrapper.last("limit 1");
        return hspOrgDiscountConverter.hspOrgDiscountDtoFromTbObj(tbHspOrgDiscountMapper.selectOne(queryWrapper));

    }

    /**
     * 判断时间是否有交集
     */
    public static boolean compareDate(Date startDate, Date endDate, Date startDate2, Date endDate2) {
        if (Objects.isNull(startDate2) || Objects.isNull(endDate2)) {
            return Boolean.FALSE;
        }

        if (startDate2.before(startDate) && startDate2.before(endDate)) {
            return Boolean.TRUE;
        }

        if (endDate2.before(startDate) && endDate2.after(endDate)) {
            return Boolean.TRUE;
        }

        if (startDate.before(startDate2) && endDate.after(endDate2)) {
            return Boolean.TRUE;
        }

        if (startDate2.equals(startDate) || endDate2.equals(endDate) || startDate2.equals(endDate)
            || endDate2.equals(startDate)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }
}
