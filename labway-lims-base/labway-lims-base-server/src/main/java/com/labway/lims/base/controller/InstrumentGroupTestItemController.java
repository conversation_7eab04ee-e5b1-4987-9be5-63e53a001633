package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentGroupInstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGroupTestItemDto;
import com.labway.lims.base.api.service.InstrumentGroupInstrumentService;
import com.labway.lims.base.api.service.InstrumentGroupTestItemService;
import com.labway.lims.base.vo.InstrumentGroupTestItemVo;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/29 20:37
 */
@RestController
@RequestMapping("/instrument-group-test-item")
public class InstrumentGroupTestItemController extends BaseController {

    @Resource
    private InstrumentGroupTestItemService instrumentGroupTestItemService;
    @Resource
    private InstrumentGroupInstrumentService instrumentGroupInstrumentService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @PostMapping("/add")
    public Object addTestItem(@RequestBody List<InstrumentGroupTestItemVo> vos) {

        if (vos.stream().anyMatch(e -> Objects.isNull(e.getTestItemCode()))
            || vos.stream().anyMatch(e -> Objects.isNull(e.getTestItemId()))) {
            throw new IllegalArgumentException("项目不能为空");
        }

        final List<InstrumentGroupTestItemDto> dtos =
            JSON.parseArray(JSON.toJSONString(vos), InstrumentGroupTestItemDto.class);
        instrumentGroupTestItemService.addBatch(dtos);

        Set<Long> instrumentGroupInstrumentIds =
            dtos.stream().map(InstrumentGroupTestItemDto::getInstrumentGroupInstrumentId).collect(Collectors.toSet());
        Map<Long, String> instrumentNameByGroupInstrumentId =
            instrumentGroupInstrumentService.selectByIds(instrumentGroupInstrumentIds).stream()
                .collect(Collectors.toMap(InstrumentGroupInstrumentDto::getInstrumentGroupInstrumentId,
                    InstrumentGroupInstrumentDto::getInstrumentName));

        dtos.forEach(item -> {
            rabbitMQService
                .convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_GROUP_LOG.getDesc())
                        .setContent(String.format("[%s] 仪器添加 [%s] 项目 ",
                            instrumentNameByGroupInstrumentId.getOrDefault(item.getInstrumentGroupInstrumentId(),
                                item.getInstrumentGroupInstrumentId().toString()),
                            item.getTestItemName()))
                        .toJSONString());

        });

        return Map.of();
    }

    @PostMapping("/testItems")
    public Object getTestItems(@RequestParam Long instrumentGroupInstrumentId) {
        return Map.of("items",
            instrumentGroupTestItemService.selectByInstrumentGroupInstrumentId(instrumentGroupInstrumentId));
    }

    @PostMapping("/delete")
    public Object delete(@RequestBody Set<Long> instrumentGroupTestItemIds) {
        if (Objects.isNull(instrumentGroupTestItemIds)) {
            return Collections.emptyMap();
        }
        List<InstrumentGroupTestItemDto> dtos =
            instrumentGroupTestItemService.selectByInstrumentGroupTestItemIds(instrumentGroupTestItemIds);
        for (Long id : instrumentGroupTestItemIds) {
            instrumentGroupTestItemService.deleteInstrumentGroupTestItemId(id);
        }

        Set<Long> instrumentGroupInstrumentIds =
            dtos.stream().map(InstrumentGroupTestItemDto::getInstrumentGroupInstrumentId).collect(Collectors.toSet());
        Map<Long, String> instrumentNameByGroupInstrumentId =
            instrumentGroupInstrumentService.selectByIds(instrumentGroupInstrumentIds).stream()
                .collect(Collectors.toMap(InstrumentGroupInstrumentDto::getInstrumentGroupInstrumentId,
                    InstrumentGroupInstrumentDto::getInstrumentName));
        dtos.forEach(item -> {
            rabbitMQService
                .convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_GROUP_LOG.getDesc())
                        .setContent(String.format("[%s] 仪器删除 [%s] 项目 ",
                            instrumentNameByGroupInstrumentId.getOrDefault(item.getInstrumentGroupInstrumentId(),
                                item.getInstrumentGroupInstrumentId().toString()),
                            item.getTestItemName()))
                        .toJSONString());

        });

        return Collections.emptyMap();
    }

}
