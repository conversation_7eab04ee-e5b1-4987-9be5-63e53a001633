package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.vo.InstrumentGroupVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/21 17:05
 */
@Slf4j
@RestController
@RequestMapping("/instrument-group")
public class InstrumentGroupController extends BaseController {
    @Resource
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @PostMapping("/add")
    public Object add(@RequestBody InstrumentGroupVo vo) {
        if (StringUtils.isEmpty(vo.getGroupName()) || Objects.isNull(vo.getGroupId())) {
            throw new IllegalArgumentException("专业组名称不能为空");
        }

        if (StringUtils.isEmpty(vo.getInstrumentGroupCode())) {
            throw new IllegalArgumentException("专业小组编码不能为空");
        }

        if (StringUtils.isEmpty(vo.getInstrumentGroupName())) {
            throw new IllegalArgumentException("专业小组名称不能为空");
        }

        if (Objects.isNull(vo.getSampleStartValue())) {
            throw new IllegalArgumentException("样本号开始不能为空");
        }

        if (Objects.isNull(vo.getSampleEndValue())) {
            throw new IllegalArgumentException("样本号结束不能为空");
        }

        if (vo.getSampleStartValue() < 0) {
            throw new IllegalArgumentException("样本号开始号必须是正整数");
        }

        if (vo.getSampleStartValue() > vo.getSampleEndValue()) {
            throw new IllegalArgumentException("样本号开始号不能大于结束号");
        }

        if (Objects.isNull(vo.getSort())) {
            throw new IllegalArgumentException("优先序号不能为空");
        }

        if (Objects.isNull(vo.getSecondSortColor()) || StringUtils.isBlank(vo.getSecondSortColor())) {
            throw new IllegalArgumentException("颜色不能为空");
        }

        if (Objects.isNull(vo.getEnable())) {
            throw new IllegalArgumentException("是否启用不能为空");
        }
        final InstrumentGroupDto groupDto = JSON.parseObject(JSON.toJSONString(vo), InstrumentGroupDto.class);
        long id = instrumentGroupService.addInstrumentGroup(groupDto);

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_GROUP_LOG.getDesc())
                .setContent(String.format("新增 [%s] 专业小组", groupDto.getInstrumentGroupName())).toJSONString());

        return Map.of("id", id);
    }

    @PostMapping("/update")
    public Object update(@RequestBody InstrumentGroupVo vo) {
        if (StringUtils.isEmpty(vo.getGroupName()) || Objects.isNull(vo.getGroupId())) {
            throw new IllegalArgumentException("专业组名称不能为空");
        }

        if (StringUtils.isEmpty(vo.getInstrumentGroupCode())) {
            throw new IllegalArgumentException("专业小组编码不能为空");
        }

        if (StringUtils.isEmpty(vo.getInstrumentGroupName())) {
            throw new IllegalArgumentException("专业小组名称不能为空");
        }

        if (Objects.isNull(vo.getSampleStartValue())) {
            throw new IllegalArgumentException("样本号开始不能为空");
        }

        if (vo.getSampleStartValue() < 0) {
            throw new IllegalArgumentException("样本号开始号必须是正整数");
        }

        if (vo.getSampleStartValue() > vo.getSampleEndValue()) {
            throw new IllegalArgumentException("样本号开始号不能大于结束号");
        }

        if (Objects.isNull(vo.getSort())) {
            throw new IllegalArgumentException("优先序号不能为空");
        }

        if (Objects.isNull(vo.getSecondSortColor()) || StringUtils.isBlank(vo.getSecondSortColor())) {
            throw new IllegalArgumentException("颜色不能为空");
        }

        if (Objects.isNull(vo.getEnable())) {
            throw new IllegalArgumentException("是否启用不能为空");
        }

        InstrumentGroupDto instrumentGroupDtoNow =
            instrumentGroupService.selectByInstrumentGroupId(vo.getInstrumentGroupId());
        if (Objects.isNull(instrumentGroupDtoNow)) {
            throw new LimsException("专业小组不存在");
        }

        final InstrumentGroupDto dto = JSON.parseObject(JSON.toJSONString(vo), InstrumentGroupDto.class);
        instrumentGroupService.updateByInstrumentGroupId(dto);

        String compare = new CompareUtils<InstrumentGroupDto>().compare(instrumentGroupDtoNow, dto);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_GROUP_LOG.getDesc())
                    .setContent(String.format("修改专业小组: [%s]", compare)).toJSONString());
            log.info("发送mq消息成功：{},{}", TraceLog.EXCHANGE, TraceLog.ROUTING_KEY);
        }

        return Collections.emptyMap();
    }

    @PostMapping("/delete")
    public Object delete(@RequestBody Set<Long> ids) {
        if (Objects.isNull(ids)) {
            return Collections.emptyMap();
        }
        List<InstrumentGroupDto> instrumentGroupDtos = instrumentGroupService.selectByInstrumentGroupIds(ids);
        for (Long id : ids) {
            instrumentGroupService.deleteByInstrumentGroupId(id);
        }
        instrumentGroupDtos.forEach(item -> rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_GROUP_LOG.getDesc())
                .setContent(String.format("删除专业小组 [%s]", item.getInstrumentGroupName())).toJSONString()));

        return Collections.emptyMap();
    }

    @PostMapping("/instrument-groups")
    public Object selectAll() {

        final List<InstrumentGroupDto> groupDtos = instrumentGroupService.selectAll();

        return Map.of("instrumentGroup", groupDtos);
    }

    @GetMapping("/group-instrument-groups")
    public Object selectAll(@RequestParam("groupId") Long groupId) {

        if (Objects.isNull(groupId)) {
            return Collections.emptyList();
        }

        final List<InstrumentGroupDto> groupDtos = instrumentGroupService.selectByGroupId(groupId);

        return Map.of("instrumentGroup", groupDtos);
    }

    @PostMapping("/group-instrument-groups")
    public Object selectGroupInstrumentGroups() {
        return instrumentGroupService.selectAll().stream()
            .filter(e -> Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()))
            .collect(Collectors.toList());
    }
}
