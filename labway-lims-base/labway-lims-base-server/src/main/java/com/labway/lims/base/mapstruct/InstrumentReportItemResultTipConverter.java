package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.InstrumentReportItemResultTipDto;
import com.labway.lims.base.model.TbInstrumentReportItemResultTip;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 结果值提示 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface InstrumentReportItemResultTipConverter {

    InstrumentReportItemResultTipDto instrumentReportItemResultTipDtoFromTbObj(TbInstrumentReportItemResultTip obj);

    List<InstrumentReportItemResultTipDto> instrumentReportItemResultTipDtoListFromTbObjList(List<TbInstrumentReportItemResultTip> list);

}
