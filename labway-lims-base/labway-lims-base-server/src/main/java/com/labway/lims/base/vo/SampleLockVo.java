package com.labway.lims.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/11 20:50
 */
@Getter
@Setter
public class SampleLockVo {

    /**
     * applySampleId
     */
    private Long applySampleId;

    /**
     * 状态
     * 0解锁  1加锁
     *
     * @see com.labway.lims.api.enums.finance.FinanceLockEnum
     */
    private Integer status;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 就诊类型编码，申请单类型
     */
    private String applyTypeCode;

    /**
     * 就诊类型名称
     */
    private String applyTypeName;

    /**
     * 门诊/住院号  就诊卡号
     */
    private String patientVisitCard;

    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 申请科室
     */
    private String dept;

    /**
     * 床号
     */
    private String patientBed;

    /**
     * 临床诊断
     */
    private String diagnosis;

    /**
     * 送检医生名称
     */
    private String sendDoctorName;

    /**
     * 送检医生编码
     */
    private String sendDoctorCode;

    /**
     * 当前环节
     */
    private String currentLink;

    /**
     * 录入人
     */
    private String enterPeople;

    /**
     * 录入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date enterDate;

}
