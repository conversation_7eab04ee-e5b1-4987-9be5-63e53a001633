package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.ReportMaterialRelationDto;
import com.labway.lims.base.model.TbReportMaterialRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 报告物料关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11 14:16
 */
@Mapper
public interface ReportMaterialRelationMapper extends BaseMapper<TbReportMaterialRelation> {

    /**
     * 批量插入报告物料关联
     *
     * @param list 报告物料关联DTO列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<ReportMaterialRelationDto> list);
    
    /**
     * 批量删除报告物料关联
     *
     * @param ids 报告物料关联ID集合
     * @return 删除数量
     */
    int batchDelete(@Param("ids") Set<Long> ids);
    
    /**
     * 检查报告项目和专业组是否已对照
     *
     * @param reportItemCode 报告项目编码
     * @param groupName 专业组名称
     * @return 匹配的记录数
     */
    int countByReportItemCodeAndGroupName(@Param("reportItemCode") String reportItemCode, @Param("groupName") String groupName);
    

    /**
     * 根据报告项目编码集合和专业组名称集合查询关联记录
     *
     * @param reportItemCodes 报告项目编码集合
     * @param groupNames 专业组名称集合
     * @return 报告物料关联DTO列表
     */
    List<ReportMaterialRelationDto> selectByReportItemCodesAndGroupNames(@Param("reportItemCodes") Set<String> reportItemCodes, @Param("groupNames") Set<String> groupNames);
    
    /**
     * 查询所有未删除的报告物料关联记录
     *
     * @return 报告物料关联DTO列表
     */
    List<ReportMaterialRelationDto> selectAll();
    

    
    /**
     * 批量更新报告物料关联
     *
     * @param list 报告物料关联DTO列表
     * @return 更新数量
     */
    int batchUpdate(@Param("list") List<ReportMaterialRelationDto> list);
} 