package com.labway.lims.base.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * (TbItemPriceCombinePackage)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-04 19:55:48
 */
@SuppressWarnings("serial")
@TableName("tb_item_price_combine_package")
public class TbItemPriceCombinePackage extends Model<TbItemPriceCombinePackage> {
    //套餐包主键id
    @TableId(type = IdType.INPUT)
    private Long combinePackageId;
    //套餐包名称
    private String combinePackageName;
    //机构id
    private Long orgId;
    //机构名称
    private String orgName;
    //创建时间
    private Date createDate;
    //更新时间
    private Date updateDate;
    //跟新人id
    private Long updateId;
    //更新人名称
    private String updateName;
    //创建人id
    private Long createId;
    //创建人名称
    private String createName;
    //是否启用 0否1是
    private Integer enable;
    //是否删除 0否1是
    @TableLogic(value = "0", delval = "1")
    private Integer isDelete;
    //套餐包编码
    private String combinePackageCode;
    //套餐收费价格
    private BigDecimal combinePackagePrice;
    //套餐项目数量
    private Integer itemCount;
    //送检机构编码
    private String hspOrgCode;
    //送检机构名称
    private String hspOrgName;
    //送检机构id
    private Long hspOrgId;
    //检验项目MD5值
    private String itemMd5;


    public Long getCombinePackageId() {
        return combinePackageId;
    }

    public void setCombinePackageId(Long combinePackageId) {
        this.combinePackageId = combinePackageId;
    }

    public String getCombinePackageName() {
        return combinePackageName;
    }

    public void setCombinePackageName(String combinePackageName) {
        this.combinePackageName = combinePackageName;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Long getUpdateId() {
        return updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getCombinePackageCode() {
        return combinePackageCode;
    }

    public void setCombinePackageCode(String combinePackageCode) {
        this.combinePackageCode = combinePackageCode;
    }

    public BigDecimal getCombinePackagePrice() {
        return combinePackagePrice;
    }

    public void setCombinePackagePrice(BigDecimal combinePackagePrice) {
        this.combinePackagePrice = combinePackagePrice;
    }

    public Integer getItemCount() {
        return itemCount;
    }

    public void setItemCount(Integer itemCount) {
        this.itemCount = itemCount;
    }

    public String getHspOrgCode() {
        return hspOrgCode;
    }

    public void setHspOrgCode(String hspOrgCode) {
        this.hspOrgCode = hspOrgCode;
    }

    public String getHspOrgName() {
        return hspOrgName;
    }

    public void setHspOrgName(String hspOrgName) {
        this.hspOrgName = hspOrgName;
    }

    public Long getHspOrgId() {
        return hspOrgId;
    }

    public void setHspOrgId(Long hspOrgId) {
        this.hspOrgId = hspOrgId;
    }

    public String getItemMd5() {
        return itemMd5;
    }

    public void setItemMd5(String itemMd5) {
        this.itemMd5 = itemMd5;
    }


}

