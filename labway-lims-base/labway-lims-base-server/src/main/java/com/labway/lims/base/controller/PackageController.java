package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.PackageTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.PackageDto;
import com.labway.lims.base.api.dto.PackageItemDto;
import com.labway.lims.base.api.dto.PhysicalGroupDto;
import com.labway.lims.base.api.service.*;
import com.labway.lims.base.vo.PackageAddRequestVo;
import com.labway.lims.base.vo.PackageListResponseVo;
import com.labway.lims.base.vo.PackageUpdateRequestVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 套餐 API
 * 
 * <AUTHOR>
 * @since 2023/3/23 16:21
 */
@RestController
@RequestMapping("/package")
public class PackageController extends BaseController {

    @DubboReference
    private ReportItemService reportItemService;

    @DubboReference
    private PhysicalGroupService physicalGroupService;

    @DubboReference
    private PackageService packageService;

    @DubboReference
    private PackageItemService packageItemService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @Resource
    private HspOrganizationService hspOrganizationService;


    /**
     * 套餐 新增
     */
    @PostMapping("/add")
    public Object packageAdd(@RequestBody PackageAddRequestVo vo) {
        if (StringUtils.isBlank(vo.getPackageName())
            || (PackageTypeEnum.PHYSICAL.getCode().equals(vo.getPackageTypeCode()) && Objects.isNull(vo.getPhysicalGroupId()))
            || Objects.isNull(vo.getEnable()) || StringUtils.isBlank(vo.getPackageTypeCode())
            || Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        checkVoWhenAddOrUpdate(vo);
        //体检单位
        PhysicalGroupDto physicalGroupDto = null;
        if (PackageTypeEnum.PHYSICAL.getCode().equals(vo.getPackageTypeCode())){
            physicalGroupDto = physicalGroupService.selectByPhysicalGroupId(vo.getPhysicalGroupId());
            if (Objects.isNull(physicalGroupDto)) {
                throw new IllegalArgumentException("所选体检单位不存在");
            }
        }else {
            vo.setPhysicalGroupId(NumberUtils.LONG_ZERO);
        }

        HspOrganizationDto organizationDto;
        //送检机构 0是通用
        if (Objects.equals(vo.getHspOrgId(), NumberUtils.LONG_ZERO)){
            organizationDto = new HspOrganizationDto();
            organizationDto.setHspOrgId(NumberUtils.LONG_ZERO);
            organizationDto.setHspOrgName("通用机构");
        }else {
            organizationDto = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
            if (Objects.isNull(organizationDto)) {
                throw new IllegalArgumentException("所选送检机构不存在");
            }
        }

        //套餐类型
        PackageTypeEnum packageTypeEnum = PackageTypeEnum.selectByCode(vo.getPackageTypeCode());
        if (Objects.isNull(packageTypeEnum)) {
            throw new IllegalArgumentException("所选套餐类型不存在");
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        if (Objects.nonNull(packageService
            .selectByPackageName(vo.getPackageName(), loginUser.getOrgId()))) {
            throw new LimsException("当前套餐名称已存在");
        }

        // 转换
        PackageDto packageDto =
            JSON.parseObject(JSON.toJSONString(vo), PackageDto.class);

        packageDto.setPhysicalGroupName(physicalGroupDto == null?"":physicalGroupDto.getPhysicalGroupName());
        packageDto.setHspOrgName(organizationDto.getHspOrgName());
        packageDto.setPackageType(packageTypeEnum.getDesc());

        long id = packageService.addPackage(packageDto);

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.PACKAGE_LOG.getDesc())
                .setContent(String.format("新增 [%s] 下 [%s] 套餐", packageDto.getPackageType(),
                    packageDto.getPackageName()))
                .toJSONString());

        return Map.of("id", id);

    }

    /**
     * 套餐 删除
     */
    @PostMapping("/delete")
    public Object packageDelete(@RequestBody Set<Long> packageIds) {
        if (CollectionUtils.isEmpty(packageIds)) {
            return Collections.emptyMap();
        }
        List<PackageDto> packageDtos =
            packageService.selectByPackageIds(packageIds);

        packageService.deleteByPackageIds(packageIds);

        packageDtos.forEach(item -> {
            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog
                    .newInstance().setModule(TraceLogModuleEnum.PACKAGE_LOG.getDesc()).setContent(String
                        .format("删除 [%s] 下 [%s] 体检套餐", item.getPhysicalGroupName(), item.getPackageName()))
                    .toJSONString());

        });
        return Collections.emptyMap();
    }

    /**
     * 套餐 修改
     */
    @PostMapping("/update")
    public Object packageUpdate(@RequestBody PackageUpdateRequestVo vo) {
        if (StringUtils.isBlank(vo.getPackageName()) || Objects.isNull(vo.getEnable())
            || Objects.isNull(vo.getPackageId())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        // 判断套餐是否存在
        final PackageDto packageDtoNow =
            packageService.selectByPackageId(vo.getPackageId());
        if (Objects.isNull(packageDtoNow)) {
            throw new LimsException("套餐不存在");
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        PackageDto selectByPhysicalGroupPackageName = packageService
            .selectByPackageName(vo.getPackageName(), loginUser.getOrgId());
        if (Objects.nonNull(selectByPhysicalGroupPackageName) && !Objects.equals(vo.getPackageId(),
            selectByPhysicalGroupPackageName.getPackageId())) {
            throw new LimsException("当前套餐名称已存在");
        }

        final PackageDto target = new PackageDto();
        BeanUtils.copyProperties(packageDtoNow, target);

        // 更新项
        target.setPackageName(vo.getPackageName());
        target.setEnable(vo.getEnable());

        packageService.updateByPackageId(target);

        String compare = new CompareUtils<PackageDto>().compare(packageDtoNow, target);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.PACKAGE_LOG.getDesc())
                    .setContent(String.format("修改套餐: [%s]", compare)).toJSONString());
        }

        return Collections.emptyMap();

    }

    /**
     * 套餐 获取 所有 查看
     */
    @PostMapping("/select-all")
    public Object packageList() {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 对应机构下 所有套餐
        List<PackageDto> packageDtos =
            packageService.selectByOrgId(loginUser.getOrgId());

        // 对应套餐id
        Set<Long> packageIds = packageDtos.stream()
            .map(PackageDto::getPackageId).collect(Collectors.toSet());

        // 体检套餐 对应 体检套餐项目
        List<PackageItemDto> PackageItemDtos =
            packageItemService.selectByPackageIds(packageIds);

        // 体检套餐项目 对应所有检验项目
        Set<Long> testItemIds = PackageItemDtos.stream().map(PackageItemDto::getTestItemId)
            .collect(Collectors.toSet());
        // 体检套餐id 对应 检验项目ids
        Map<Long,
            Set<Long>> groupingByPackageId = PackageItemDtos.stream()
                .collect(Collectors.groupingBy(PackageItemDto::getPackageId,
                    Collectors.mapping(PackageItemDto::getTestItemId, Collectors.toSet())));

        // 检验项目对应报告项目数量
        Map<Long, Integer> selectReportItemCountByTestItemIds =
            reportItemService.selectReportItemCountByTestItemIds(testItemIds);

        List<PackageListResponseVo> targetList = packageDtos.stream()
            .map(obj -> JSON.parseObject(JSON.toJSONString(obj), PackageListResponseVo.class))
            .collect(Collectors.toList());

        // 拼接 报告项目数量
        targetList.forEach(item -> {
            Set<Long> longs = ObjectUtils.defaultIfNull(
                groupingByPackageId.get(item.getPackageId()), Collections.emptySet());
            int reportItemCount = longs.stream().filter(selectReportItemCountByTestItemIds::containsKey)
                .mapToInt(selectReportItemCountByTestItemIds::get).sum();
            item.setReportItemCount(reportItemCount);
        });

        return targetList;
    }

    /**
     * 获取 体检团体套餐 根据 体检单位
     */
    @PostMapping("/select-by-physical-group-id")
    public Object selectByPhysicalGroupId(@RequestParam("physicalGroupId") long physicalGroupId) {
        PhysicalGroupDto physicalGroupDto = physicalGroupService.selectByPhysicalGroupId(physicalGroupId);
        if (Objects.isNull(physicalGroupDto)) {
            throw new IllegalArgumentException("对应体检单位不存在");
        }
        List<PackageDto> targetList =
            packageService.selectByGroupIds(Lists.newArrayList(physicalGroupId));
        targetList.forEach(item -> {
            item.setPhysicalGroupName(physicalGroupDto.getPhysicalGroupName());
        });
        return targetList;
    }

    /**
     * 检查 体检团体套餐 新增 或 修改 参数 公共部分
     *
     */
    private <T extends PackageAddRequestVo> void checkVoWhenAddOrUpdate(T vo) {
        if (StringUtils.length(vo.getPackageName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("套餐名称不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }
        if (!(Objects.equals(vo.getEnable(), YesOrNoEnum.NO.getCode())
            || Objects.equals(vo.getEnable(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("是否启用参数错误");
        }
    }

}
