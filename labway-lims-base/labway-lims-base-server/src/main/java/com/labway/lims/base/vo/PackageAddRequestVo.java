package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 体检团体套餐  新增 Vo
 * 
 * <AUTHOR>
 * @since 2023/3/28 11:35
 */
@Getter
@Setter
public class PackageAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 套餐类型编码
     */
    private String packageTypeCode;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 体检套餐名称
     */
    private String packageName;
    /**
     * 体检单位ID
     */
    private Long physicalGroupId = 0l;
    /**
     * 是否启用(0未启用 1启用)
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer enable;
}
