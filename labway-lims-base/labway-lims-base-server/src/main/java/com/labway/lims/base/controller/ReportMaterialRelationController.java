package com.labway.lims.base.controller;

import cn.hutool.core.lang.Dict;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportMaterialRelationDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.ReportMaterialRelationService;
import com.labway.lims.base.api.vo.BatchReportMaterialRelationVo;
import com.labway.lims.base.service.listener.ImportReportMaterialRelationListener;
import com.labway.lims.base.vo.ImportErrorResponseVo;
import com.labway.lims.base.vo.excel.ImportReportMaterialRelationVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.labway.lims.api.stream.StreamUtils.distinctByKey;
import org.springframework.validation.annotation.Validated;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;

/**
 * <p>
 * 报告物料关联表 控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11 14:16
 */
@RestController
@RequestMapping("/report-material-relation")
@Slf4j
@Validated
public class ReportMaterialRelationController extends BaseController {
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @Resource
    private ReportMaterialRelationService reportMaterialRelationService;

    @DubboReference
    private RabbitMQService rabbitMQService;

    @DubboReference
    private SnowflakeService snowflakeService;
    
    @DubboReference
    private GroupService groupService;

    /**
     * 获取报告项目列表，可根据专业组ID筛选
     */
    @GetMapping("/items")
    public Object items(@RequestParam(required = false) Long groupId) {

        final Map<Long, InstrumentDto> map = (instrumentService.selectByOrgIdByGroupId(LoginUserHandler.get().getOrgId(), groupId).stream()
                .collect(Collectors.toMap(InstrumentDto::getInstrumentId, v -> v, (a, b) -> a)));

        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentIds(map.keySet());

        // 根据专业组名称和项目编码去重
        List<InstrumentReportItemDto> distinctItems = instrumentReportItems.stream()
                .filter(distinctByKey(item -> Optional.ofNullable(map.get(item.getInstrumentId()))
                        .map(InstrumentDto::getGroupId)
                        .orElse(0L) + "_" + item.getReportItemCode()))
                .collect(Collectors.toList());

        // 一次性查询所有已对照的组合
        Set<String> mappedItems = reportMaterialRelationService.getAllMappedItemGroupKeys();


        return distinctItems.stream().map(e -> {

                    InstrumentDto instrumentDto = map.getOrDefault(e.getInstrumentId(), new InstrumentDto());

                    boolean isMapped = mappedItems.contains(e.getReportItemCode() + "_" + instrumentDto.getGroupId());
                    String mappedStatus = isMapped ? "已对照" : "未对照";
                    return Dict.of("instrumentReportItemId", e.getInstrumentReportItemId(),
                            "reportItemCode", e.getReportItemCode(),
                            "reportItemName", e.getReportItemName(),
                            "reportItemUnit", StringUtils.defaultString(e.getReportItemUnitName()),
                            "reportItemUnitCode", e.getReportItemUnit(),
                            "instrumentName", e.getInstrumentName(),
                            "instrumentCode", e.getInstrumentCode(),
                            "instrumentId", e.getInstrumentId(),
                            "groupName", instrumentDto.getGroupName(),
                            "enable", e.getEnable(),
                            "isQc", e.getIsQc(),
                            "itemTypeCode", e.getItemTypeCode(),
                            "itemTypeName", e.getItemTypeName(),
                            "enName", e.getEnName(),
                            "enAb", e.getEnAb(),
                            "printSort", e.getPrintSort(),
                            "groupId", instrumentDto.getGroupId(),
                            "mappedStatus", mappedStatus);
                })
                .collect(Collectors.toList());
    }

    /**
     * 批量保存报告物料关联
     */
    @PostMapping("/batch-save")
    public Object batchSave(@RequestBody @Valid List<BatchReportMaterialRelationVo> vos) {
        // 限制批量处理数量
        if (vos.size() > 1000) {
            throw new IllegalArgumentException("批量保存数量不能超过1000条");
        }
        
        // 提取所有报告项目编码和专业组名称
        Set<String> reportItemCodes = new HashSet<>();
        Set<String> groupNames = new HashSet<>();
        Set<Long> groupIds = new HashSet<>();
        for (BatchReportMaterialRelationVo vo : vos) {
            reportItemCodes.add(vo.getReportItemCode());
            groupNames.add(vo.getGroupName());
            if (vo.getGroupId() != null) {
                groupIds.add(vo.getGroupId());
            }
        }
        
        // 查询所有相关专业组，获取groupCode
        Map<Long, ProfessionalGroupDto> groupMap = Collections.emptyMap();
        if (!groupIds.isEmpty()) {
            groupMap = groupService.selectByGroupIdsAsMap(groupIds);
        }
        
        // 只查询与当前批量保存相关的报告物料关联记录
        List<ReportMaterialRelationDto> existingRelations = reportMaterialRelationService.selectByReportItemCodesAndGroupNames(reportItemCodes, groupNames);

        // 创建一个Map，用于快速查找已存在的记录
        Map<String, ReportMaterialRelationDto> existingMap = existingRelations.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getReportItemCode() + "_" + dto.getGroupName() + "_" + dto.getMaterialCode(),
                        dto -> dto,
                        (a, b) -> a
                ));

        // 分离需要更新和需要插入的记录
        List<ReportMaterialRelationDto> toUpdate = new ArrayList<>();
        List<ReportMaterialRelationDto> toInsert = new ArrayList<>();

        LinkedList<Long> ids = snowflakeService.genIds(vos.size());

        // 转换VO为DTO，并判断是更新还是插入
        for (BatchReportMaterialRelationVo vo : vos) {
            String key = vo.getReportItemCode() + "_" + vo.getGroupName() + "_" + vo.getMaterialCode();
            ReportMaterialRelationDto dto = new ReportMaterialRelationDto();
            
            // 获取groupCode
            String groupCode = null;
            if (vo.getGroupId() != null && groupMap.containsKey(vo.getGroupId())) {
                ProfessionalGroupDto groupDto = groupMap.get(vo.getGroupId());
                if (groupDto != null) {
                    groupCode = groupDto.getGroupCode();
                }
            }

            if (existingMap.containsKey(key)) {
                // 已存在，执行更新
                ReportMaterialRelationDto existingDto = existingMap.get(key);
                dto.setReportMaterialRelationId(existingDto.getReportMaterialRelationId());
                dto.setReportItemCode(vo.getReportItemCode());
                dto.setReportItemName(vo.getReportItemName());
                dto.setMaterialCode(vo.getMaterialCode());
                dto.setMaterialName(vo.getMaterialName());
                dto.setGroupName(vo.getGroupName());
                dto.setGroupId(vo.getGroupId());
                dto.setGroupCode(groupCode);
                dto.setCreateDate(existingDto.getCreateDate());
                dto.setCreatorId(existingDto.getCreatorId());
                dto.setCreatorName(existingDto.getCreatorName());
                dto.setIsDelete(0);
                toUpdate.add(dto);
            } else {
                // 不存在，执行插入
                dto.setReportMaterialRelationId(ids.pop());
                dto.setReportItemCode(vo.getReportItemCode());
                dto.setReportItemName(vo.getReportItemName());
                dto.setMaterialCode(vo.getMaterialCode());
                dto.setMaterialName(vo.getMaterialName());
                dto.setGroupName(vo.getGroupName());
                dto.setGroupId(vo.getGroupId());
                dto.setGroupCode(groupCode); // 设置groupCode
                dto.setCreateDate(new Date());
                dto.setCreatorId(LoginUserHandler.get().getUserId());
                dto.setCreatorName(LoginUserHandler.get().getNickname());
                dto.setIsDelete(0);
                toInsert.add(dto);
            }
        }

        // 执行批量更新和插入
        boolean updateResult = true;
        boolean insertResult = true;

        if (!toUpdate.isEmpty()) {
            updateResult = reportMaterialRelationService.batchUpdates(toUpdate);
        }

        if (!toInsert.isEmpty()) {
            insertResult = reportMaterialRelationService.batchSaves(toInsert);
        }

        boolean result = updateResult && insertResult;

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.REPORT_MATERIAL_RELATION.getDesc())
                        .setContent(String.format("用户 [%s] 批量保存报告物料关联 [更新:%d条,新增:%d条]",
                                LoginUserHandler.get().getNickname(), toUpdate.size(), toInsert.size()))
                        .toJSONString());

        return vos.size();
    }

    /**
     * 批量删除报告物料关联
     */
    @PostMapping("/batch-delete")
    public Object batchDelete(@RequestBody @NotEmpty(message = "请选择要删除的数据") Set<Long> reportMaterialRelationIds) {
        // 限制批量处理数量
        if (reportMaterialRelationIds.size() > 1000) {
            throw new IllegalArgumentException("批量删除数量不能超过1000条");
        }

        // 调用服务进行批量删除
        boolean result = reportMaterialRelationService.batchDelete(reportMaterialRelationIds);

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.REPORT_MATERIAL_RELATION.getDesc())
                        .setContent(String.format("用户 [%s] 批量删除报告物料关联 [%d条]",
                                LoginUserHandler.get().getNickname(), reportMaterialRelationIds.size()))
                        .toJSONString());

        return Collections.singletonMap("success", result);
    }


    /**
     * 根据报告项目编码查询关联的物资信息
     */
    @GetMapping("/materials-by-report-item")
    public Object materialsByReportItem(@RequestParam @NotBlank(message = "报告项目编码不能为空") String reportItemCode, 
                                        @RequestParam(required = false) Long groupId) {
        return reportMaterialRelationService.selectMaterialsByReportItemCodeAndGroupId(reportItemCode, groupId);
    }

    /**
     * 导入报告物料关联
     */
    @PostMapping("/import-report-material-relation")
    public Object importReportMaterialRelation(@RequestParam("file") @NotNull(message = "文件不能为空") MultipartFile file) {
        // 校验文件格式
        String fileName = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(fileName);
        if (!"xls".equalsIgnoreCase(extension) && !"xlsx".equalsIgnoreCase(extension)) {
            throw new IllegalStateException("只允许上传 xls 和 xlsx 格式的文件");
        }

        // 校验文件大小
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new IllegalStateException("文件大小不能超过10MB");
        }

        log.info("开始导入报告物料关联，文件名: {}, 大小: {}KB", fileName, file.getSize() / 1024);

        // 获取当前登录用户
        LoginUserHandler.User user = LoginUserHandler.get();

        // 创建监听器
        ImportReportMaterialRelationListener listener = new ImportReportMaterialRelationListener(
                snowflakeService,
                user,
                reportMaterialRelationService,
                groupService
        );

        try (ExcelReader excelReader = EasyExcelFactory.read(file.getInputStream()).build()) {
            // 配置读取第一个sheet，并注册监听器
            ReadSheet readSheet = EasyExcelFactory.readSheet(0)
                    .head(ImportReportMaterialRelationVo.class)
                    .registerReadListener(listener)
                    .autoTrim(true)
                    .build();

            // 读取Excel
            excelReader.read(readSheet);

            // 检查是否有错误
            List<ImportErrorResponseVo> importErrorResponseVoList = listener.getImportErrorResponseVoList();
            if (CollectionUtils.isNotEmpty(importErrorResponseVoList)) {
                List<ImportErrorResponseVo> sortedErrorList = importErrorResponseVoList.stream()
                        .sorted(Comparator.comparing(ImportErrorResponseVo::getRowNo))
                        .collect(Collectors.toList());

                log.warn("导入数据检查失败，共{}个错误", sortedErrorList.size());
                for (ImportErrorResponseVo error : sortedErrorList) {
                    log.warn("行 {}: {}", error.getRowNo(), error.getErrorInfo());
                }

                throw new LimsCodeException(1002, "数据检查失败")
                        .setData(Map.of("errorList", sortedErrorList));
            }

            // 获取处理后的数据
            List<ReportMaterialRelationDto> targetList = listener.getTargetList();
            if (CollectionUtils.isEmpty(targetList)) {
                log.warn("导入数据为空，没有有效的数据需要保存");
                throw new IllegalArgumentException("导入数据为空，请确保Excel中包含有效数据");
            }

            // 批量保存
            log.info("开始保存导入的{}条数据", targetList.size());
            boolean result = reportMaterialRelationService.batchSaves(targetList);
            log.info("保存导入数据{}完成", result ? "成功" : "失败");

            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.REPORT_MATERIAL_RELATION.getDesc())
                            .setContent(String.format("用户 [%s] 导入报告物料关联 [%d条]",
                                    user.getNickname(), targetList.size()))
                            .toJSONString());

            return Map.of("success", result, "count", targetList.size());

        } catch (IOException e) {
            log.error("导入报告物料关联出错", e);
            throw new IllegalStateException("导入报告物料关联出错: " + e.getMessage());
        }
    }

}