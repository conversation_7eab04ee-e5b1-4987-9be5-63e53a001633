package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.model.TbInstrumentMedicine;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仪器药物 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Mapper
public interface TbInstrumentMedicineMapper extends BaseMapper<TbInstrumentMedicine> {



    /**
     * 批量 插入
     */
    void batchAddTbInstrumentMedicines(@Param("conditions") List<TbInstrumentMedicine> conditions);
}
