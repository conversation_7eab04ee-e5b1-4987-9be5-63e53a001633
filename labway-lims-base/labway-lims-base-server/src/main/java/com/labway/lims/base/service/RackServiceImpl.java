package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.RackTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.base.api.dto.QueryRackPageDto;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.RackHoleRuleDto;
import com.labway.lims.base.api.dto.RackPageDto;
import com.labway.lims.base.api.enums.RackStatusEnum;
import com.labway.lims.base.api.service.RackService;
import com.labway.lims.base.mapper.TbRackMapper;
import com.labway.lims.base.model.TbRack;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/22 11:17
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "rack")
public class RackServiceImpl implements RackService {

    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private TbRackMapper tbRackMapper;
    @DubboReference
    private RackLogicService rackLogicService;

    @Override
    @CacheEvict(allEntries = true)
    public long add(RackDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final TbRack tbRack = JSON.parseObject(JSON.toJSONString(dto), TbRack.class);

        if(Objects.isNull(tbRack.getRackId())){
            tbRack.setRackId(snowflakeService.genId());
        }
        // 默认值
        tbRack.setIsDestroyed(Objects.isNull(tbRack.getIsDestroyed()) ? YesOrNoEnum.NO.getCode() : tbRack.getIsDestroyed());
        tbRack.setStorageDays(tbRack.getStorageDays()); // 可以为空
        tbRack.setCreatorName(user.getNickname());
        tbRack.setCreatorId(user.getUserId());
        tbRack.setUpdaterId(user.getUserId());
        tbRack.setUpdaterName(user.getNickname());
        tbRack.setStatus(RackStatusEnum.IDLE.getCode());
        tbRack.setUpdateDate(new Date());
        tbRack.setCreateDate(new Date());
        tbRack.setOrgId(user.getOrgId());
        tbRack.setIsDelete(YesOrNoEnum.NO.getCode());

        //归档试管架需要有专业组信息
        if (RackTypeEnum.isArchiveRack(tbRack.getRackTypeCode())){
            tbRack.setGroupCode(user.getGroupCode());
            tbRack.setGroupName(user.getGroupName());
        }

        if (tbRackMapper.insert(tbRack) < 1) {
            throw new IllegalStateException("添加试管架失败");
        }

        log.info("用户 [{}] 添加试管架成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(tbRack));

        return tbRack.getRackId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByRackId(RackDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final TbRack tbRack = JSON.parseObject(JSON.toJSONString(dto), TbRack.class);
        tbRack.setUpdaterId(user.getUserId());
        tbRack.setUpdaterName(user.getNickname());
        tbRack.setUpdateDate(new Date());

        if (tbRackMapper.updateById(tbRack) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改试管架成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(tbRack));

        return true;
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByRackId(RackDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final TbRack tbRack = JSON.parseObject(JSON.toJSONString(dto), TbRack.class);
        tbRack.setUpdaterId(user.getUserId());
        tbRack.setUpdaterName(user.getNickname());
        tbRack.setUpdateDate(new Date());

        if (tbRackMapper.deleteById(tbRack) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除试管架成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(tbRack));

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @CacheEvict(allEntries = true)
    public void updateByRackIds(RackDto dto, Collection<Long> rackIds) {
        if (CollectionUtils.isEmpty(rackIds)) {
            return;
        }
        tbRackMapper.updateByRackIds(dto, rackIds);
    }

    @Override
    @Nullable
    @Cacheable(key = "'selectByRackId:' + #rackId")
    public RackDto selectByRackId(long rackId) {
        final TbRack tbRack = tbRackMapper.selectById(rackId);
        return convert(tbRack);
    }

    @Override
    public List<RackDto> selectByRackIds(Collection<Long> rackIds) {
        if (CollectionUtils.isEmpty(rackIds)) {
            return Collections.emptyList();
        }
        return tbRackMapper
                .selectList(new LambdaQueryWrapper<TbRack>()
                        .in(TbRack::getRackId, rackIds).orderByDesc(TbRack::getRackId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Nonnull
    @Override
    @Cacheable(key = "'selectAll:' + #orgId")
    public List<RackDto> selectAll(long orgId,boolean needArchive) {
        LambdaQueryWrapper<TbRack> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbRack::getOrgId, orgId).orderByDesc(TbRack::getRackId);
        if (!needArchive){
            queryWrapper.ne(TbRack::getRackTypeCode, RackTypeEnum.ARCHIVE_RACK.getCode());
            queryWrapper.ne(TbRack::getRackTypeCode,RackTypeEnum.PERMANENT_ARCHIVE_RACK.getCode());
        }
        final List<TbRack> racks = tbRackMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(racks)) {
            return Collections.emptyList();
        }
        return racks.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByStatus:' + #status + ',' + #orgId")
    public List<RackDto> selectByStatus(int status, long orgId) {
        return tbRackMapper
                .selectList(new LambdaQueryWrapper<TbRack>().eq(TbRack::getStatus, status)
                        .eq(TbRack::getOrgId, orgId)
                        .eq(TbRack::getEnable, YesOrNoEnum.YES.getCode()))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'countByStatus:' + #status + ',' + #orgId")
    public long countByStatus(int status, long orgId) {
        return tbRackMapper
                .selectCount(new LambdaQueryWrapper<TbRack>()
                        .eq(TbRack::getOrgId, orgId)
                        .eq(TbRack::getStatus, status)
                        .eq(TbRack::getEnable, YesOrNoEnum.YES.getCode()));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByRackCode:' + #rackCode + #orgId")
    public RackDto selectByRackCode(String rackCode, long orgId) {
        if (StringUtils.isBlank(rackCode)) {
            return null;
        }
        LambdaQueryWrapper<TbRack> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbRack::getRackCode, rackCode);
        queryWrapper.eq(TbRack::getOrgId, orgId);
        queryWrapper.eq(TbRack::getIsDelete, YesOrNoEnum.NO.getCode());
        return convert(tbRackMapper.selectOne(queryWrapper.last("limit 1")));
    }

    @Override
    @Cacheable(key = "'selectByRackTypeCodes:' + #rackTypeCode + #groupCode")
    public List<RackDto> selectByRackTypeCodes(Collection<String> rackTypeCode, long orgId, String groupCode) {
        if (CollectionUtils.isEmpty(rackTypeCode)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRack> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbRack::getRackTypeCode, rackTypeCode);
        queryWrapper.eq(TbRack::getOrgId, orgId);
        queryWrapper.eq(TbRack::getGroupCode,groupCode);
        queryWrapper.eq(TbRack::getIsDelete, YesOrNoEnum.NO.getCode());
        return convert(tbRackMapper.selectList(queryWrapper));
    }


    @Override
    @Cacheable(key = "'selectByRackTypeCodes:' + #orgId + #rackTypeCode")
    public List<RackDto> selectByRackTypeCodes(long orgId, Collection<String> rackTypeCode) {
        if (CollectionUtils.isEmpty(rackTypeCode)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRack> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbRack::getRackTypeCode, rackTypeCode);
        queryWrapper.eq(TbRack::getOrgId, orgId);
        queryWrapper.eq(TbRack::getIsDelete, YesOrNoEnum.NO.getCode());
        return convert(tbRackMapper.selectList(queryWrapper));
    }

    @Override
    public RackHoleRuleDto selectRackHoleRuleByRackId(long rackId) {
        // 目前所有试管架排序规则一致 都为默认排序规则
        return new RackHoleRuleDto();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void rackRecycle(Collection<Long> rackIds) {
        if (CollectionUtils.isEmpty(rackIds)) {
            return;
        }

        final RackDto rackDto = new RackDto();
        rackDto.setStatus(RackStatusEnum.IDLE.getCode());
        this.updateByRackIds(rackDto, rackIds);

        // 删除逻辑试管架
        rackLogicService.deleteByRackIds(rackIds);
    }

    @Override
    public RackPageDto selectArchiveRackPage(QueryRackPageDto queryRackPageDto) {
        if (queryRackPageDto != null){
            final LambdaQueryWrapper<TbRack> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Objects.nonNull(queryRackPageDto.getOrgId()), TbRack::getOrgId, queryRackPageDto.getOrgId());
            wrapper.eq(TbRack::getGroupCode,queryRackPageDto.getGroupCode());
            //判断类型为归档
            wrapper.and(and -> {
                and.eq(TbRack::getRackTypeCode,RackTypeEnum.ARCHIVE_RACK.getCode());
                and.or();

                and.eq(TbRack::getRackTypeCode,RackTypeEnum.PERMANENT_ARCHIVE_RACK.getCode());
            });
            //进行参数填充。支持编码/名称模糊检索
            if (StringUtils.isNotBlank(queryRackPageDto.getSearchParam())){
                wrapper.and(and -> {
                    and.like(TbRack::getRackCode,queryRackPageDto.getSearchParam());
                    and.or();
                    and.like(TbRack::getRackName,queryRackPageDto.getSearchParam());
                });
            }

            //降序
            wrapper.orderByDesc(TbRack::getCreateDate);

            final Page<TbRack> page = new Page<>();
            page.setSize(queryRackPageDto.getSize());
            page.setCurrent(queryRackPageDto.getCurrent());
            final Page<TbRack> datas = tbRackMapper.selectPage(page, wrapper);

            final RackPageDto rackPageDto = new RackPageDto();
            rackPageDto.setSize(datas.getSize());
            rackPageDto.setCurrent(datas.getCurrent());
            rackPageDto.setTotal(datas.getTotal());
            rackPageDto.setRackDtos(datas.getRecords().stream().map(this::convert).collect(Collectors.toList()));

            return rackPageDto;
        }
        return null;
    }

    private RackDto convert(TbRack tbRack) {
        if (Objects.isNull(tbRack)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(tbRack), RackDto.class);
    }

    private List<RackDto> convert(List<TbRack> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }

}
