package com.labway.lims.base.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentReportItemCommonPhraseDto;
import com.labway.lims.base.api.service.InstrumentReportItemCommonPhraseService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.vo.AddInstrumentReportItemCommonPhraseVo;
import com.labway.lims.base.vo.UpdateInstrumentReportItemCommonPhraseVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 常用短语
 *
 * <AUTHOR>
 * @since 2023/3/20 14:29
 */
@RestController
@RequestMapping("/report-item-common-phrase")
public class ReportItemReferenceCommonPhraseController extends BaseController {
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentReportItemCommonPhraseService instrumentReportItemCommonPhraseService;
    @DubboReference
    private InstrumentService instrumentService;

    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 获取常用短语
     */
    @GetMapping("/phrases")
    public Object refers(@RequestParam Long instrumentReportItemId) {
        if (Objects.isNull(instrumentReportItemId)) {

            throw new IllegalArgumentException("参数错误");
        }
        return instrumentReportItemCommonPhraseService.selectByInstrumentReportItemId(instrumentReportItemId);
    }

    /**
     * 添加常用短语
     */
    @PostMapping("/add")
    public Object add(@RequestBody AddInstrumentReportItemCommonPhraseVo vo) {
        if (Objects.isNull(vo.getInstrumentReportItemId())
                || Objects.isNull(vo.getIsDefault()) || StringUtils.isBlank(vo.getContent())) {
            throw new IllegalArgumentException("参数错误");
        }

        final InstrumentReportItemCommonPhraseDto instrumentReportItemCommonPhrase = new InstrumentReportItemCommonPhraseDto();
        BeanUtils.copyProperties(vo, instrumentReportItemCommonPhrase);

        return Map.of("id", instrumentReportItemCommonPhraseService
                .addInstrumentReportItemCommonPhrase(instrumentReportItemCommonPhrase));
    }

    /**
     * 修改常用短语
     */
    @PostMapping("/update")
    public Object update(@RequestBody UpdateInstrumentReportItemCommonPhraseVo vo) {
        final InstrumentReportItemCommonPhraseDto instrumentReportItemCommonPhrase = new InstrumentReportItemCommonPhraseDto();
        BeanUtils.copyProperties(vo, instrumentReportItemCommonPhrase);

        final InstrumentReportItemCommonPhraseDto instrumentReportItemCommonPhraseOld = instrumentReportItemCommonPhraseService.selectByInstrumentReportItemCommonPhraseId(vo.getInstrumentReportItemCommonPhraseId());
        if (Objects.isNull(instrumentReportItemCommonPhraseOld)) {
            throw new IllegalArgumentException("常用短语不存在");
        }

        if (!instrumentReportItemCommonPhraseService.updateByInstrumentReportItemCommonPhraseId(instrumentReportItemCommonPhrase)) {
            throw new IllegalStateException("修改常用短语失败");
        }

        // 日志
        final String compare = new CompareUtils<InstrumentReportItemCommonPhraseDto>()
                .compare(instrumentReportItemCommonPhraseOld, instrumentReportItemCommonPhrase);
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_COMMON_PHRASE.getDesc())
                        .setContent(String.format("用户 [%s] 修改了仪器[%s]-报告项目[%s]的修改常用短语，修改内容：%s",
                                LoginUserHandler.get().getNickname(),
                                instrumentReportItemCommonPhraseOld.getInstrumentName(),
                                instrumentReportItemCommonPhraseOld.getReportItemName(),
                                compare)).toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 删除常用短语
     */
    @DeleteMapping("/delete")
    public Object delete(@RequestBody Set<Long> instrumentReportItemCommonPhraseIds) {

        if (CollectionUtils.isEmpty(instrumentReportItemCommonPhraseIds)) {
            return Collections.emptyMap();
        }


        final Map<Long, InstrumentReportItemCommonPhraseDto> commonPhraseMap = instrumentReportItemCommonPhraseService.selectByIds(instrumentReportItemCommonPhraseIds)
                .stream().collect(Collectors.toMap(InstrumentReportItemCommonPhraseDto::getInstrumentReportItemCommonPhraseId, Function.identity(), (a, b) -> a));

        if (MapUtils.isEmpty(commonPhraseMap)) {
            return Collections.emptyMap();
        }

        for (Long instrumentReportItemCommonPhraseId : instrumentReportItemCommonPhraseIds) {
            instrumentReportItemCommonPhraseService.deleteByInstrumentReportItemCommonPhraseId(instrumentReportItemCommonPhraseId);

            final InstrumentReportItemCommonPhraseDto commonPhrase = commonPhraseMap.get(instrumentReportItemCommonPhraseId);
            if (Objects.isNull(commonPhrase)) {
                continue;
            }

            String logContent = String.format("用户 [%s] 删除了仪器[%s]-报告项目[%s]下的常用短语," +
                            "快捷键 [%s] 是否默认 [%s] 排序号 [%s] 短语内容 [%s]",
                    LoginUserHandler.get().getNickname(),
                    commonPhrase.getInstrumentName(),
                    commonPhrase.getReportItemName(),
                    commonPhrase.getKeyShort(),
                    YesOrNoEnum.selectByCode(commonPhrase.getIsDefault()).getDesc(),
                    commonPhrase.getSort(),
                    commonPhrase.getContent()
            );
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_COMMON_PHRASE.getDesc())
                            .setContent(logContent).toJSONString());
        }

        return Collections.emptyMap();
    }


}
