package com.labway.lims.base.controller;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/11 16:14
 */
@RestController
@RequestMapping("/financial")
public class FinancialController extends BaseController {

    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    /**
     * 免单样本列表
     */
    @PostMapping("/free-sample-list")
    public Object getFreeSampleList(@RequestBody FreeSampleQueryVo vo) {

        if (Objects.isNull(vo.getEnterDateStart()) || Objects.isNull(vo.getEnterDateEnd())) {
            throw new IllegalArgumentException("请选择生效日期或结束日期");
        }

        if (vo.getEnterDateEnd().before(vo.getEnterDateStart())) {
            throw new IllegalArgumentException("结束日期不可小于生效日期");
        }
        List<Object> searchAfter = null;
        if (Objects.nonNull(vo.getSearchAfter())) {
            searchAfter = Collections.singletonList(vo.getSearchAfter());
        }
        final SampleEsQuery query = new SampleEsQuery();
        query.setPageNo(vo.getCurrent());
        query.setPageSize(vo.getSize());
        if (Objects.nonNull(vo.getHspOrgId())) {
            query.setHspOrgIds(Collections.singleton(vo.getHspOrgId()));
        }
        if (StringUtils.isNotBlank(vo.getBarcode())) {
            query.setBarcodes(Collections.singleton(vo.getBarcode()));
        }
        if (StringUtils.isNotBlank(vo.getPatientName())) {
            query.setPatientName(vo.getPatientName());
        }
        query.setStartCreateDate(vo.getEnterDateStart());
        query.setEndCreateDate(vo.getEnterDateEnd());

        final ScrollPage<BaseSampleEsModelDto> scrollPage = elasticSearchSampleService.searchAfter(searchAfter, query);

        final List<BaseSampleEsModelDto> samples = scrollPage.getData();

        if (CollectionUtils.isEmpty(samples)) {
            return new FreeSamplePageVo();
        }
        final Map<Long, List<SampleFlowDto>> flowMap = sampleFlowService.selectWithOutContentByApplySampleIdsAsMap(
            samples.stream().map(BaseSampleEsModelDto::getApplySampleId).collect(Collectors.toList()));
        final LinkedList<FreeSampleVo> vos = new LinkedList<>();

        for (BaseSampleEsModelDto sample : samples) {

            final FreeSampleVo fs = new FreeSampleVo();

            fs.setApplySampleId(sample.getApplySampleId());
            fs.setHspOrgId(sample.getHspOrgId());
            fs.setHspOrgName(sample.getHspOrgName());
            fs.setBarcode(sample.getBarcode());
            fs.setApplyTypeName(sample.getApplyTypeName());
            fs.setApplyTypeCode(sample.getApplyTypeCode());
            fs.setPatientVisitCard(sample.getPatientVisitCard());
            fs.setPatientName(sample.getPatientName());
            fs.setPatientAge(sample.getPatientAge());
            fs.setPatientSubage(sample.getPatientSubage());
            fs.setPatientSubageUnit(sample.getPatientSubageUnit());
            fs.setSendDoctorCode(sample.getSendDoctorCode());
            fs.setSendDoctorName(sample.getSendDoctorName());
            fs.setEnterDate(sample.getCreateDate());
            fs.setEnterPeople(sample.getCreatorName());
            fs.setSampleTypeCode(sample.getSampleTypeCode());
            fs.setSampleTypeName(sample.getSampleTypeName());
            fs.setSampleCount(sample.getSampleCount());
            fs.setDiagnosis(sample.getDiagnosis());
            fs.setRemark(sample.getRemark());
            fs.setMasterBarcode(sample.getMasterBarcode());
            final List<SampleFlowDto> flows = flowMap.get(sample.getApplySampleId());
            if (Objects.isNull(flows)) {
                fs.setCurrentLink("");
            } else {
                final SampleFlowDto sampleFlowDto = flows.stream()
                    .max(Comparator.comparing(SampleFlowDto::getSampleFlowId)).orElse(new SampleFlowDto());
                fs.setCurrentLink(sampleFlowDto.getOperateName());
            }
            fs.setSampleProperty(sample.getSampleProperty());
            fs.setSamplePropertyCode(sample.getSamplePropertyCode());
            fs.setUrgent(sample.getUrgent());
            fs.setSamplingDate(sample.getSamplingDate());
            fs.setPatientBirthday(sample.getPatientBirthday());
            fs.setPatientCard(sample.getPatientCard());
            fs.setPatientMobile(sample.getPatientMobile());
            fs.setPatientSex(sample.getPatientSex());
            fs.setDept(sample.getDept());
            fs.setPatientBed(sample.getPatientBed());
            fs.setApplyDate(sample.getApplyDate());
            fs.setCreateDate(sample.getCreateDate());

            vos.add(fs);
        }
        vos.sort(Comparator.comparing(FreeSampleVo::getCreateDate));
        final FreeSamplePageVo pageVo = new FreeSamplePageVo();
        pageVo.setSearchAfter(scrollPage.getSearchAfter().iterator().next());
        pageVo.setSamples(vos);

        return pageVo;
    }

    /**
     * 明细
     */
    @PostMapping("/free-items")
    public Object getFreeItems(@RequestParam("applySampleId") Long applySampleId) {

        List<ApplySampleItemDto> sampleItems =
            applySampleItemService.selectByApplySampleIdContainStopTest(Collections.singleton(applySampleId)).stream()
                .filter(obj -> !Objects.equals(obj.getStopStatus(), StopTestStatus.STOP_TEST_FREE.getCode()))
                // 禁用的项目不显示
                .filter(obj -> !Objects.equals(obj.getIsDisabled(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());
        final LinkedList<FreeSampleItemVo> vos = new LinkedList<>();
        for (ApplySampleItemDto item : sampleItems) {
            final FreeSampleItemVo itemVo = new FreeSampleItemVo();
            itemVo.setApplySampleItemId(item.getApplySampleItemId());
            itemVo.setIsFree(item.getIsFree());
            itemVo.setTestItemId(item.getTestItemId());
            itemVo.setTestItemCode(item.getTestItemCode());
            itemVo.setTestItemName(item.getTestItemName());
            itemVo.setFeePrice(item.getFeePrice());
            itemVo.setGroupId(item.getGroupId());
            itemVo.setGroupName(item.getGroupName());
            vos.add(itemVo);
        }
        return vos;
    }

    /**
     * 是否免单操作
     */
    @PostMapping("/update")
    public Object update(@RequestBody UpdateApplySampleItemVo vo) {

        final ApplySampleItemDto dto = new ApplySampleItemDto();
        dto.setApplySampleItemId(vo.getApplySampleItemId());
        dto.setIsFree(vo.getIsFree());

        applySampleItemService.updateBatchById(Collections.singleton(dto));

        return Collections.emptyMap();
    }

}
