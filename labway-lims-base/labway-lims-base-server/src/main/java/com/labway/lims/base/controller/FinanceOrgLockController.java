package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.FinanceOrgLockDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.OrgLockDto;
import com.labway.lims.base.api.dto.OrgLockQueryDto;
import com.labway.lims.base.api.service.FinanceOrgLockService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.vo.FinanceOrgLockAddVo;
import com.labway.lims.base.vo.HspOrgUnLockVo;
import com.labway.lims.base.vo.OrgLockQueryVo;
import com.labway.lims.base.vo.OrgLockVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/11 20:47
 */
@RestController
@RequestMapping("/org-lock")
public class FinanceOrgLockController extends BaseController {

    @Resource
    private FinanceOrgLockService financeOrgLockService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @PostMapping("/list")
    public Object orgLocks(@RequestBody OrgLockQueryVo vo) {

        if (Objects.isNull(vo.getLockDateStart()) || Objects.isNull(vo.getLockDateEnd())) {
            throw new IllegalArgumentException("请选择生效日期或结束日期");
        }

        if (vo.getLockDateEnd().before(vo.getLockDateStart())) {
            throw new IllegalArgumentException("结束日期不能早于生效日期");
        }

        final OrgLockQueryDto dto = JSON.parseObject(JSON.toJSONString(vo), OrgLockQueryDto.class);
        final List<FinanceOrgLockDto> locks = financeOrgLockService.selectByLockDateAndHspOrgId(dto);
        if (CollectionUtils.isEmpty(locks)) {
            return Collections.emptyList();
        }
        final LinkedList<OrgLockVo> vos = new LinkedList<>();
        for (FinanceOrgLockDto lock : locks) {
            final OrgLockVo lockVo = JSON.parseObject(JSON.toJSONString(lock), OrgLockVo.class);
            lockVo.setOperator(lock.getCreatorName());
            lockVo.setOperDate(lock.getCreateDate());
            vos.add(lockVo);
        }
        vos.sort((o1, o2) -> {
            if (Objects.isNull(o1.getOperDate()) || Objects.isNull(o2.getOperDate())) {
                return NumberUtils.INTEGER_ZERO;
            }
            return o2.getOperDate().compareTo(o1.getOperDate());
        });
        return vos;
    }

    /**
     * 加解锁
     */
    @PostMapping("/lock")
    public Object lock(@RequestBody FinanceOrgLockAddVo vo) {

        //只有加锁的时候才需要判断开始结束时间
        if (Objects.isNull(vo.getStartDate()) || Objects.isNull(vo.getEndDate())) {
            throw new IllegalArgumentException("请选择生效日期或结束日期");
        }

        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束日期不能早于生效日期");
        }

        final String key = redisPrefix.getBasePrefix() + FinanceOrgLockController.class.getSimpleName() + ":" + getClass().getSimpleName() + ":SyncOrgLock";

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofMinutes(5)))) {
            throw new IllegalArgumentException(String.format("用户 [%s] 正在进行机构加解锁,请稍后重试", LoginUserHandler.get().getNickname()));
        }

        try {
            List<HspOrganizationDto> hsps;
            if (CollectionUtils.isEmpty(vo.getHspOrgIds())) {
                hsps = hspOrganizationService.selectAll();
            } else {
                hsps = hspOrganizationService.selectByHspOrgIds(vo.getHspOrgIds());
            }

            if (CollectionUtils.isEmpty(hsps)) {
                throw new IllegalArgumentException("机构不存在");
            }

            final Set<Long> orgIds = hsps.stream().map(HspOrganizationDto::getHspOrgId).collect(Collectors.toSet());
            //添加全部机构
            orgIds.add(NumberUtils.LONG_ZERO);
            final List<FinanceOrgLockDto> orgHisLocks = financeOrgLockService.selectByHspOrgIds(orgIds);

            if (CollectionUtils.isNotEmpty(orgHisLocks)) {
                final Set<String> orgLockKeys = new HashSet<>();
                for (FinanceOrgLockDto lock : orgHisLocks) {
                    orgLockKeys.add(financeOrgLockService.getOrgLockKey(lock.getHspOrgId(),
                            lock.getStartDate().getTime(), lock.getEndDate().getTime()));
                }

                final List<String> lockValues = stringRedisTemplate.opsForValue().multiGet(orgLockKeys);
                //如果存在一条记录的开始结束时间在范围内那么表示是机构加锁
                if (Objects.nonNull(lockValues)) {
                    for (String v : lockValues) {
                        final OrgLockDto ol = JSON.parseObject(StringUtils.defaultString(v, "{}"), OrgLockDto.class);
                        if (BooleanUtils.isNotTrue(vo.getEndDate().getTime() < ol.getStartTime()
                                || vo.getStartDate().getTime() > ol.getEndTime())) {
                            throw new IllegalStateException("已选结算日期存在加锁记录，不能再次加锁");
                        }
                    }
                }
            }

            //如果前端传入的机构集合是空，那么只有一条记录
            final List<FinanceOrgLockDto> orgLocks = new LinkedList<>();
            if (CollectionUtils.isEmpty(vo.getHspOrgIds())) {
                final FinanceOrgLockDto dto = new FinanceOrgLockDto();
                dto.setHspOrgId(NumberUtils.LONG_ZERO);
                dto.setHspOrgName("全部");
                dto.setStartDate(ObjectUtils.defaultIfNull(vo.getStartDate(), DefaultDateEnum.DEFAULT_DATE.getDate()));
                dto.setEndDate(ObjectUtils.defaultIfNull(vo.getEndDate(), DefaultDateEnum.DEFAULT_DATE.getDate()));
                dto.setReason(vo.getReason());
                dto.setStatus(vo.getStatus());
                dto.setOrgId(LoginUserHandler.get().getOrgId());
                dto.setOrgName(LoginUserHandler.get().getOrgName());
                orgLocks.add(dto);
            } else {
                for (HspOrganizationDto hsp : hsps) {
                    final FinanceOrgLockDto dto = new FinanceOrgLockDto();
                    dto.setHspOrgId(hsp.getHspOrgId());
                    dto.setHspOrgName(hsp.getHspOrgName());
                    dto.setStartDate(ObjectUtils.defaultIfNull(vo.getStartDate(), DefaultDateEnum.DEFAULT_DATE.getDate()));
                    dto.setEndDate(ObjectUtils.defaultIfNull(vo.getEndDate(), DefaultDateEnum.DEFAULT_DATE.getDate()));
                    dto.setReason(vo.getReason());
                    dto.setStatus(vo.getStatus());
                    dto.setOrgId(LoginUserHandler.get().getOrgId());
                    dto.setOrgName(LoginUserHandler.get().getOrgName());
                    orgLocks.add(dto);
                }
            }

            financeOrgLockService.addBatch(orgLocks);
            //设置机构加锁redis标记
            final Map<String, String> keys = new LinkedHashMap<>();
            //所有机构加锁
            if (CollectionUtils.isEmpty(vo.getHspOrgIds())) {
                final String orgLockKey = financeOrgLockService.getOrgLockKey(NumberUtils.LONG_ZERO, vo.getStartDate().getTime(), vo.getEndDate().getTime());
                final OrgLockDto lock = new OrgLockDto();
                lock.setHspOrgId(NumberUtils.LONG_ZERO);
                lock.setStartTime(vo.getStartDate().getTime());
                lock.setEndTime(vo.getEndDate().getTime());
                keys.put(orgLockKey, JSON.toJSONString(lock));
            } else {
                for (Long hspOrgId : vo.getHspOrgIds()) {
                    final String orgLockKey = financeOrgLockService.getOrgLockKey(hspOrgId, vo.getStartDate().getTime(), vo.getEndDate().getTime());
                    final OrgLockDto lock = new OrgLockDto();
                    lock.setHspOrgId(hspOrgId);
                    lock.setStartTime(vo.getStartDate().getTime());
                    lock.setEndTime(vo.getEndDate().getTime());
                    keys.put(orgLockKey, JSON.toJSONString(lock));
                }
            }
            //批量设置
            stringRedisTemplate.opsForValue().multiSet(keys);

            final RedisSerializer<String> serializer = stringRedisTemplate.getStringSerializer();
            //批量设置过期时间1年
            stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (String k : keys.keySet()) {
                    final byte[] serialize = serializer.serialize(k);
                    if (Objects.nonNull(serialize)) {
                        connection.expire(serialize, 60 * 60 * 24 * 365);
                    }
                }
                return null;
            }, serializer);

        } finally {
            stringRedisTemplate.delete(key);
        }

        return Map.of();
    }

    /**
     * 解锁
     */
    @PostMapping("/un-lock")
    public Object unLock(@RequestBody HspOrgUnLockVo vo) {

        if (Objects.isNull(vo.getOrgLockRecordId())) {
            throw new IllegalArgumentException("缺少必填参数");
        }

        if (Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalArgumentException("请选择送检机构");
        }

        final String key = redisPrefix.getBasePrefix() + FinanceOrgLockController.class.getSimpleName() + ":" + getClass().getSimpleName() + ":SyncOrgUnLock";

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofMinutes(5)))) {
            throw new IllegalArgumentException(String.format("用户 [%s] 正在进行机构加解锁,请稍后重试", LoginUserHandler.get().getNickname()));
        }
        try {

            final FinanceOrgLockDto orgLock = financeOrgLockService.selectByOrgLockRecordId(vo.getOrgLockRecordId());
            if (Objects.isNull(orgLock)) {
                throw new IllegalStateException("该机构未进行过加锁");
            }

            final String orgLockKey = financeOrgLockService.getOrgLockKey(vo.getHspOrgId(), orgLock.getStartDate().getTime(), orgLock.getEndDate().getTime());

            //存在过标记，表示对当前机构的开始和结束时间段加过锁
            if (BooleanUtils.isNotTrue(stringRedisTemplate.hasKey(orgLockKey))) {
                throw new IllegalStateException("该机构不是加锁状态，解锁失败");
            }

            //对全部机构进行处理也只有一条操作记录
            final FinanceOrgLockDto dto = new FinanceOrgLockDto();

            //如果接收到前端 0L 那么是全部送检机构
            if (Objects.equals(vo.getHspOrgId(), NumberUtils.LONG_ZERO)) {
                dto.setHspOrgName("全部");
            } else {
                final HspOrganizationDto hsp = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
                if (Objects.isNull(hsp)) {
                    throw new IllegalArgumentException("所选机构不存在");
                }

                dto.setHspOrgName(hsp.getHspOrgName());
            }

            dto.setHspOrgId(vo.getHspOrgId());
            dto.setStartDate(orgLock.getStartDate());
            dto.setEndDate(orgLock.getEndDate());
            dto.setReason(vo.getReason());
            dto.setStatus(vo.getStatus());
            dto.setOrgId(LoginUserHandler.get().getOrgId());
            dto.setOrgName(LoginUserHandler.get().getOrgName());
            //新增操作记录
            financeOrgLockService.add(dto);

            //解锁直接删除对应机构加解锁时间段的 redis 标记
            stringRedisTemplate.delete(orgLockKey);

        } finally {
            stringRedisTemplate.delete(key);
        }

        return Collections.emptyMap();
    }

}
