package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.FinanceOrgLockDto;
import com.labway.lims.base.api.dto.OrgLockQueryDto;
import com.labway.lims.base.api.service.FinanceOrgLockService;
import com.labway.lims.base.mapper.TbFinanceOrgLockMapper;
import com.labway.lims.base.model.TbFinanceOrgLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/11 20:48
 */
@Slf4j
@DubboService
public class FinanceOrgLockServiceImpl implements FinanceOrgLockService {
    @Resource
    private TbFinanceOrgLockMapper tbFinanceOrgLockMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RedisPrefix redisPrefix;

    @Override
    public List<FinanceOrgLockDto> selectByLockDateAndHspOrgId(OrgLockQueryDto dto) {

        final LambdaQueryWrapper<TbFinanceOrgLock> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(dto.getHspOrgId()), TbFinanceOrgLock::getHspOrgId, dto.getHspOrgId())
                .ge(Objects.nonNull(dto.getLockDateStart()), TbFinanceOrgLock::getCreateDate, dto.getLockDateStart())
                .le(Objects.nonNull(dto.getLockDateEnd()), TbFinanceOrgLock::getCreateDate, dto.getLockDateEnd())
                .orderByDesc(TbFinanceOrgLock::getCreateDate);

        return tbFinanceOrgLockMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<FinanceOrgLockDto> selectByHspOrgId(long hspOrgId) {

        final LambdaQueryWrapper<TbFinanceOrgLock> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbFinanceOrgLock::getHspOrgId, hspOrgId)
                .eq(TbFinanceOrgLock::getOrgId, LoginUserHandler.get().getOrgId())
                .orderByDesc(TbFinanceOrgLock::getCreateDate);

        return tbFinanceOrgLockMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<FinanceOrgLockDto> selectByHspOrgIds(Collection<Long> hspOrgIds) {
        if (CollectionUtils.isEmpty(hspOrgIds)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbFinanceOrgLock> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbFinanceOrgLock::getHspOrgId, hspOrgIds)
                .eq(TbFinanceOrgLock::getOrgId, LoginUserHandler.get().getOrgId())
                .orderByDesc(TbFinanceOrgLock::getCreateDate);

        return tbFinanceOrgLockMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public long add(FinanceOrgLockDto dto) {

        final TbFinanceOrgLock orgLock = JSON.parseObject(JSON.toJSONString(dto), TbFinanceOrgLock.class);
        final LoginUserHandler.User user = LoginUserHandler.get();
        orgLock.setOrgLockRecordId(snowflakeService.genId());
        orgLock.setCreateDate(new Date());
        orgLock.setCreatorId(user.getUserId());
        orgLock.setCreatorName(user.getNickname());
        orgLock.setUpdateDate(new Date());
        orgLock.setUpdateId(user.getUserId());
        orgLock.setUpdateName(user.getNickname());
        orgLock.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbFinanceOrgLockMapper.insert(orgLock) < 1) {
            throw new IllegalStateException("添加失败");
        }

        log.info("用户[{}] 添加送检机构加解锁记录成功 [{}]", user.getNickname(), JSON.toJSONString(orgLock));

        return orgLock.getOrgLockRecordId();
    }

    @Override
    public void addBatch(List<FinanceOrgLockDto> orgLocks) {
        if (CollectionUtils.isEmpty(orgLocks)) {
            return;
        }
        final LoginUserHandler.User user = LoginUserHandler.get();
        final LinkedList<Long> ids = snowflakeService.genIds(orgLocks.size());
        for (FinanceOrgLockDto fol : orgLocks) {
            fol.setOrgLockRecordId(ids.poll());
            fol.setIsDelete(YesOrNoEnum.NO.getCode());
            fol.setCreateDate(new Date());
            fol.setCreatorName(user.getNickname());
            fol.setCreatorId(user.getUserId());
            fol.setUpdateDate(new Date());
            fol.setUpdateId(user.getUserId());
            fol.setUpdateName(user.getNickname());
        }

        // 数量 分区批次插入
        List<List<FinanceOrgLockDto>> partitionList = ListUtils.partition(orgLocks, 500);

        partitionList.forEach(item -> tbFinanceOrgLockMapper.addBatch(item));
    }

    @Override
    public String getOrgLockKey(long hspOrgId, long startTime, long endTime) {
        return redisPrefix.getBasePrefix() + "HSP_ORG_LOCK:" + hspOrgId + ":" + (startTime + endTime);
    }

    @Nullable
    @Override
    public FinanceOrgLockDto selectByOrgLockRecordId(long orgLockRecordId) {

        return convert(tbFinanceOrgLockMapper.selectById(orgLockRecordId));
    }

    @Nullable
    private FinanceOrgLockDto convert(TbFinanceOrgLock lock) {
        if (Objects.isNull(lock)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(lock), FinanceOrgLockDto.class);
    }
}
