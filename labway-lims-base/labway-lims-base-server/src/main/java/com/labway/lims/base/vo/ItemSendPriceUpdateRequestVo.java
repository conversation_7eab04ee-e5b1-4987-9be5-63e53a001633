package com.labway.lims.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 外送机构-修改外送项目 参数 vo
 * 
 * <AUTHOR>
 * @since 2023/5/5 15:42
 */
@Getter
@Setter
public class ItemSendPriceUpdateRequestVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 价格ID
     */
    private Long priceId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endDate;

    /**
     * 外送价格
     */
    private String sendPrice;
}
