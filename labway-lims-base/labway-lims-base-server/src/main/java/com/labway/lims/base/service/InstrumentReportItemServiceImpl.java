package com.labway.lims.base.service;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.mdm.api.reagent.dto.SysDictDto;
import com.labway.business.center.mdm.api.reagent.service.SysDictDubboService;
import com.labway.business.center.mdm.api.user.request.QueryDictRequest;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.*;
import com.labway.lims.base.mapper.TbInstrumentReportItemMapper;
import com.labway.lims.base.mapstruct.InstrumentReportItemConverter;
import com.labway.lims.base.model.TbInstrumentReportItem;
import com.swak.frame.dto.Response;
import com.swak.frame.enums.BasicErrCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仪器项目
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "instrument-report-item")
public class InstrumentReportItemServiceImpl implements InstrumentReportItemService {
    @Resource
    private TbInstrumentReportItemMapper instrumentReportItemMapper;
    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private InstrumentService instrumentService;
    @Resource
    private ReportItemService reportItemService;
    @Resource
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private InstrumentReportItemConverter instrumentReportItemConverter;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Resource
    private SysDictDubboService sysDictDubboService;

    @Override
    @Cacheable(key = "'selectByInstrumentId:' + #instrumentId")
    public List<InstrumentReportItemDto> selectByInstrumentId(long instrumentId) {
        return instrumentReportItemMapper
            .selectList(new LambdaQueryWrapper<TbInstrumentReportItem>()
                .orderByDesc(TbInstrumentReportItem::getInstrumentReportItemId)
                .eq(TbInstrumentReportItem::getInstrumentId, instrumentId))
            .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Map<String, InstrumentReportItemDto> selectByInstrumentIdAsMap(long instrumentId) {
        return instrumentReportItemService.selectByInstrumentId(instrumentId).stream()
                .collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v->v,(a, b)->a));
    }

    @Override
    @Cacheable(key = "'selectByInstrumentGroupId:' + #instrumentGroupId")
    public List<InstrumentReportItemDto> selectByInstrumentGroupId(long instrumentGroupId) {
        return instrumentReportItemMapper.selectByInstrumentGroupId(instrumentGroupId);
    }

    @Override
    public Map<String, InstrumentReportItemDto> selectByInstrumentGroupIdAsMap(long instrumentGroupId) {
        return instrumentReportItemService.selectByInstrumentGroupId(instrumentGroupId).stream()
                .collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v->v,(a, b)->a));
    }

    @Override
    public List<InstrumentReportItemDto> selectByInstrumentGroupIds(Collection<Long> instrumentGroupIds) {
        if (CollectionUtils.isEmpty(instrumentGroupIds)) {
            return Collections.emptyList();
        }
        return instrumentGroupIds.stream().map(instrumentReportItemService::selectByInstrumentGroupId)
            .flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    @Nullable
    @Cacheable(key = "'selectByInstrumentIdAndReportItemCode:' + #instrumentId + #reportItemCode")
    public InstrumentReportItemDto selectByInstrumentIdAndReportItemCode(long instrumentId, String reportItemCode) {
        if (StringUtils.isBlank(reportItemCode)) {
            return null;
        }
        return convert(instrumentReportItemMapper.selectOne(
            new LambdaQueryWrapper<TbInstrumentReportItem>().eq(TbInstrumentReportItem::getInstrumentId, instrumentId)
                .eq(TbInstrumentReportItem::getReportItemCode, reportItemCode)));
    }

    @Override
    public List<InstrumentReportItemDto> selectByInstrumentIdsAndReportItemCodes(Collection<Long> instrumentIds,
        Collection<String> reportItemCodes) {
        if (CollectionUtils.isEmpty(instrumentIds) || CollectionUtils.isEmpty(reportItemCodes)) {
            return Collections.emptyList();
        }
        return instrumentReportItemMapper
            .selectList(new LambdaQueryWrapper<TbInstrumentReportItem>()
                .in(TbInstrumentReportItem::getInstrumentId, instrumentIds)
                .in(TbInstrumentReportItem::getReportItemCode, reportItemCodes))
            .stream().map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByInstrumentCodeAndReportItemCode:' + #instrumentCode +'_'+ #reportItemCode")
    public InstrumentReportItemDto selectByInstrumentCodeAndReportItemCode(String instrumentCode,
        String reportItemCode) {
        if (StringUtils.isBlank(reportItemCode)) {
            return null;
        }
        return convert(instrumentReportItemMapper.selectOne(new LambdaQueryWrapper<TbInstrumentReportItem>()
            .eq(TbInstrumentReportItem::getInstrumentCode, instrumentCode)
            .eq(TbInstrumentReportItem::getReportItemCode, reportItemCode)));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByInstrumentReportItemId:' + #instrumentReportItemId")
    public InstrumentReportItemDto selectByInstrumentReportItemId(long instrumentReportItemId) {
        return convert(instrumentReportItemMapper.selectById(instrumentReportItemId));
    }

    @Override
    public List<InstrumentReportItemDto> selectByInstrumentReportItemIds(Collection<Long> instrumentReportItemIds) {
        if (CollectionUtils.isEmpty(instrumentReportItemIds)) {
            return Collections.emptyList();
        }
        return instrumentReportItemMapper.selectBatchIds(instrumentReportItemIds).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByReportItemCodeAndOrgId:' + #reportItemCode + #orgId")
    public List<InstrumentReportItemDto> selectByReportItemCodeAndOrgId(String reportItemCode, long orgId) {
        final LambdaQueryWrapper<TbInstrumentReportItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentReportItem::getReportItemCode, reportItemCode).eq(TbInstrumentReportItem::getOrgId,
            orgId);

        return instrumentReportItemMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<InstrumentReportItemDto> selectByReportItemCodesAndOrgId(Collection<String> reportItemCodes,
        long orgId) {

        if (CollectionUtils.isEmpty(reportItemCodes)) {
            return Collections.emptyList();
        }

        return reportItemCodes.stream().map(e -> instrumentReportItemService.selectByReportItemCodeAndOrgId(e, orgId))
            .flatMap(Collection::stream).collect(Collectors.toList());

    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByInstrumentReportItemId(InstrumentReportItemDto dto) {
        final TbInstrumentReportItem instrumentReportItem = new TbInstrumentReportItem();

        BeanUtils.copyProperties(dto, instrumentReportItem);

        // 这几个字段不允许修改
        instrumentReportItem.setReportItemName(null);
        instrumentReportItem.setInstrumentCode(null);
        instrumentReportItem.setInstrumentCode(null);
        instrumentReportItem.setInstrumentName(null);
        instrumentReportItem.setInstrumentId(null);
        instrumentReportItem.setUpdateDate(new Date());
        instrumentReportItem.setUpdaterId(LoginUserHandler.get().getUserId());
        instrumentReportItem.setUpdaterName(LoginUserHandler.get().getNickname());

        if (instrumentReportItemMapper.updateById(instrumentReportItem) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改了仪器报告项目 [{}] 成功 [{}]", LoginUserHandler.get().getNickname(),
            dto.getInstrumentReportItemId(), JSON.toJSONString(instrumentReportItem));

        return true;
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByReportItemCode(InstrumentReportItemDto dto, String reportItemCode) {
        final TbInstrumentReportItem instrumentReportItem = new TbInstrumentReportItem();

        BeanUtils.copyProperties(dto, instrumentReportItem);

        // 这几个字段不允许修改
        instrumentReportItem.setInstrumentCode(null);
        instrumentReportItem.setInstrumentCode(null);
        instrumentReportItem.setInstrumentName(null);
        instrumentReportItem.setInstrumentId(null);
        instrumentReportItem.setUpdateDate(new Date());
        instrumentReportItem.setUpdaterId(LoginUserHandler.get().getUserId());
        instrumentReportItem.setUpdaterName(LoginUserHandler.get().getNickname());

        if (instrumentReportItemMapper.update(instrumentReportItem, new LambdaQueryWrapper<TbInstrumentReportItem>()
            .eq(TbInstrumentReportItem::getReportItemCode, reportItemCode)) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改了仪器报告项目 [{}] 成功 [{}]", LoginUserHandler.get().getNickname(),
            dto.getInstrumentReportItemId(), JSON.toJSONString(instrumentReportItem));

        return true;
    }

    @Override
    @CacheEvict(allEntries = true)
    public long addReportItem(long instrumentId, String reportItemCode) {

        if (Objects.nonNull(selectByInstrumentIdAndReportItemCode(instrumentId, reportItemCode))) {
            throw new IllegalArgumentException(String.format("报告项目 [%s] 已存在", reportItemCode));
        }

        final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentId);
        if (Objects.isNull(instrument)) {
            throw new IllegalArgumentException("仪器不存在");
        }

        final ReportItemDto reportItem =
            reportItemService.selectByReportItemCode(reportItemCode, LoginUserHandler.get().getOrgId());
        if (Objects.isNull(reportItem)) {
            throw new IllegalStateException("报告项目不存在");
        }

        final TbInstrumentReportItem instrumentReportItem = new TbInstrumentReportItem();
        instrumentReportItem.setInstrumentReportItemId(snowflakeService.genId());
        instrumentReportItem.setReportItemCode(reportItemCode);
        instrumentReportItem.setReportItemName(reportItem.getReportItemName());
        instrumentReportItem.setReportItemUnit(StringUtils.EMPTY);
        instrumentReportItem.setEnable(YesOrNoEnum.YES.getCode());
        instrumentReportItem.setInstrumentId(instrumentId);
        instrumentReportItem.setInstrumentCode(instrument.getInstrumentCode());
        instrumentReportItem.setInstrumentName(instrument.getInstrumentName());
        instrumentReportItem.setEnName(StringUtils.EMPTY);
        instrumentReportItem.setEnAb(StringUtils.EMPTY);
        instrumentReportItem.setAliasName(StringUtils.EMPTY);
        instrumentReportItem.setExamMethodCode(StringUtils.EMPTY);
        instrumentReportItem.setExamMethodName(StringUtils.EMPTY);
        instrumentReportItem.setIsQc(YesOrNoEnum.NO.getCode());
        instrumentReportItem.setIsPrint(YesOrNoEnum.YES.getCode());
        instrumentReportItem.setPrintSort(YesOrNoEnum.NO.getCode());
        instrumentReportItem.setIsManualInput(YesOrNoEnum.YES.getCode());
        instrumentReportItem.setIsResultZero(YesOrNoEnum.YES.getCode());
        instrumentReportItem.setResultTypeName(StringUtils.EMPTY);
        instrumentReportItem.setResultTypeCode(StringUtils.EMPTY);
        instrumentReportItem.setIsResultNull(YesOrNoEnum.YES.getCode());
        instrumentReportItem.setDecimalNums(2);
        instrumentReportItem.setInstrumentChannel(StringUtils.EMPTY);
        instrumentReportItem.setCalcFomulation(StringUtils.EMPTY);
        instrumentReportItem.setIsDelete(YesOrNoEnum.NO.getCode());

        instrumentReportItem.setOrgId(LoginUserHandler.get().getOrgId());
        instrumentReportItem.setOrgName(LoginUserHandler.get().getOrgName());
        instrumentReportItem.setCreateDate(new Date());
        instrumentReportItem.setUpdateDate(new Date());
        instrumentReportItem.setCreatorId(LoginUserHandler.get().getUserId());
        instrumentReportItem.setCreatorName(LoginUserHandler.get().getNickname());
        instrumentReportItem.setUpdaterId(LoginUserHandler.get().getUserId());
        instrumentReportItem.setUpdaterName(LoginUserHandler.get().getNickname());

        instrumentReportItem.setItemTypeCode(StringUtils.EMPTY);
        instrumentReportItem.setItemTypeName(StringUtils.EMPTY);
        instrumentReportItem.setIsBringOut(YesOrNoEnum.NO.getCode());

        if (instrumentReportItemMapper.insert(instrumentReportItem) < 1) {
            throw new IllegalArgumentException("新增仪器报告项目失败");
        }

        log.info("用户 [{}] 新增仪器报告项目 [{}] 成功", LoginUserHandler.get().getNickname(), reportItemCode);

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM.getDesc())
                .setContent(String.format("用户新增仪器报告项目 仪器 [%s] 报告项目 [%s] 仪器ID [%s]", instrument.getInstrumentName(),
                    reportItem.getReportItemName(), instrumentId))
                .toJSONString());

        return instrumentReportItem.getInstrumentReportItemId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByInstrumentReportItemId(long instrumentReportItemId) {
        // 删报告项目
        if (instrumentReportItemMapper.deleteById(instrumentReportItemId) <= 0) {
            return false;
        }

        // 删参考范围
        SpringUtil.getBean(InstrumentReportItemReferenceService.class)
            .deleteByInstrumentReportItemId(instrumentReportItemId);

        // 删常用短语
        SpringUtil.getBean(InstrumentReportItemCommonPhraseService.class)
            .deleteByInstrumentReportItemId(instrumentReportItemId);

        // 删结果值转换
        SpringUtil.getBean(InstrumentReportItemResultExchangeService.class)
            .deleteByInstrumentReportItemId(instrumentReportItemId);
        return true;
    }

    @Override
    public List<InstrumentReportItemDto> selectByInstrumentIds(Collection<Long> instrumentIds) {

        if (CollectionUtils.isEmpty(instrumentIds)) {
            return Collections.emptyList();
        }

        return instrumentIds.stream().map(instrumentReportItemService::selectByInstrumentId).flatMap(Collection::stream)
            .filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<InstrumentReportItemDto> selectByInstrumentIdAndReportItemCodes(long instrumentId,
        Collection<String> reportItemCodes) {
        if (CollectionUtils.isEmpty(reportItemCodes)) {
            return Collections.emptyList();
        }
        return instrumentReportItemService.selectByInstrumentId(instrumentId).stream()
            .filter(f -> reportItemCodes.contains(f.getReportItemCode())).collect(Collectors.toList());
    }

    @Override
    public List<InstrumentReportItemDto> selectByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return instrumentReportItemMapper.selectBatchIds(ids).stream().map(this::convert).collect(Collectors.toList());
    }

    private InstrumentReportItemDto convert(TbInstrumentReportItem instrumentReportItem) {
        if (Objects.isNull(instrumentReportItem)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(instrumentReportItem), InstrumentReportItemDto.class);
    }

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public List<Long> copyInstrumentReportItems(List<InstrumentReportItemDto> fromInstrumentReportItems,
        Long instrumentId) {
        // 查一下仪器信息
        final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentId);
        if (Objects.isNull(instrument)) {
            throw new IllegalArgumentException("目标仪器不存在");
        }

        // 要拷贝的报告项目编码
        List<String> fromReportItemCodes = fromInstrumentReportItems.stream()
            .map(InstrumentReportItemDto::getReportItemCode).collect(Collectors.toList());

        // 查询目标仪器 是否已经有 仪器报告项目
        List<InstrumentReportItemDto> existInstrumentReportItems =
            selectByInstrumentIdsAndReportItemCodes(List.of(instrumentId), fromReportItemCodes);
        Map<String, InstrumentReportItemDto> existReportItemCodeMap = Map.of();
        if (CollectionUtils.isNotEmpty(existInstrumentReportItems)) {
            List<String> existReportItemCodes = existInstrumentReportItems.stream()
                .map(InstrumentReportItemDto::getReportItemCode).collect(Collectors.toList());
            existReportItemCodeMap = existInstrumentReportItems.stream()
                .collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, Function.identity()));
            log.warn("机器ID [{}] 报告项目 [{}] 已存在，将其覆盖", instrumentId, existReportItemCodes);
        }

        Date current = new Date();
        List<Long> instrumentReportItemIds = new ArrayList<>();
        // 遍历拷贝过来的【仪器报告项目】
        for (InstrumentReportItemDto fromInstrumentReportItemDto : fromInstrumentReportItems) {
            // 报告项目
            String reportItemCode = fromInstrumentReportItemDto.getReportItemCode();

            Long instrumentReportItemId = null;
            TbInstrumentReportItem instrumentReportItem =
                instrumentReportItemConverter.convertDto2Entity(fromInstrumentReportItemDto);
            InstrumentReportItemDto existInstrumentReportItemDto = existReportItemCodeMap.get(reportItemCode);
            if (Objects.nonNull(existInstrumentReportItemDto)) {
                // 需要覆盖的【仪器报告项目ID】
                instrumentReportItemId = existInstrumentReportItemDto.getInstrumentReportItemId();
                // 覆盖仪器报告项目
                instrumentReportItem.setInstrumentReportItemId(instrumentReportItemId);

                instrumentReportItem.setInstrumentId(instrumentId);
                instrumentReportItem.setInstrumentCode(instrument.getInstrumentCode());
                instrumentReportItem.setInstrumentName(instrument.getInstrumentName());

                instrumentReportItem.setIsDelete(YesOrNoEnum.NO.getCode());
                instrumentReportItem.setUpdateDate(current);
                instrumentReportItem.setUpdaterId(LoginUserHandler.get().getUserId());
                instrumentReportItem.setUpdaterName(LoginUserHandler.get().getNickname());

                if (instrumentReportItemMapper.updateById(instrumentReportItem) < 1) {
                    log.error("拷贝仪器报告项目【覆盖】失败");
                    throw new IllegalArgumentException("拷贝仪器报告项目失败");
                }
            } else {
                instrumentReportItemId = snowflakeService.genId();
                // 新增仪器报告项目
                instrumentReportItem.setInstrumentReportItemId(instrumentReportItemId);

                instrumentReportItem.setInstrumentId(instrumentId);
                instrumentReportItem.setInstrumentCode(instrument.getInstrumentCode());
                instrumentReportItem.setInstrumentName(instrument.getInstrumentName());

                instrumentReportItem.setIsDelete(YesOrNoEnum.NO.getCode());
                instrumentReportItem.setCreateDate(current);
                instrumentReportItem.setCreatorId(LoginUserHandler.get().getUserId());
                instrumentReportItem.setCreatorName(LoginUserHandler.get().getNickname());
                instrumentReportItem.setUpdateDate(current);
                instrumentReportItem.setUpdaterId(LoginUserHandler.get().getUserId());
                instrumentReportItem.setUpdaterName(LoginUserHandler.get().getNickname());

                if (instrumentReportItemMapper.insert(instrumentReportItem) < 1) {
                    log.error("拷贝仪器报告项目【覆盖】失败");
                    throw new IllegalArgumentException("拷贝仪器报告项目失败");
                }
            }
            instrumentReportItemIds.add(instrumentReportItemId);

            log.info("用户 [{}] 拷贝仪器报告项目 [{}] 成功", LoginUserHandler.get().getNickname(), reportItemCode);

            // 异步发送消息
            rabbitMQService
                .convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM.getDesc())
                        .setContent(String.format("用户拷贝仪器报告项目 仪器 [%s] 报告项目 [%s] 仪器ID [%s]",
                            instrument.getInstrumentName(), instrumentReportItem.getReportItemName(), instrumentId))
                        .toJSONString());

            Long fromInstrumentReportItemId = fromInstrumentReportItemDto.getInstrumentReportItemId();
            // 拷贝参考范围
            SpringUtil.getBean(InstrumentReportItemReferenceService.class).copyReportItemReference(
                fromInstrumentReportItemId, instrumentReportItemConverter.convertEntity2Dto(instrumentReportItem));
            // 拷贝常用短语
            SpringUtil.getBean(InstrumentReportItemCommonPhraseService.class).copyReportItemCommonPhrase(
                fromInstrumentReportItemId, instrumentReportItemConverter.convertEntity2Dto(instrumentReportItem));
            // 拷贝结果值转换
            SpringUtil.getBean(InstrumentReportItemResultExchangeService.class).copyReportItemResultExchange(
                fromInstrumentReportItemId, instrumentReportItemConverter.convertEntity2Dto(instrumentReportItem));
        }

        return instrumentReportItemIds;
    }

    @Override
    public List<DictItemDto> selectAllExamMethod() {

        QueryDictRequest request = new QueryDictRequest();
        request.setEnabled(NumberUtils.INTEGER_ONE);

        Response<List<SysDictDto>> response;
        try {
            response = sysDictDubboService.listAllTestMethod(request);
        } catch (Exception e) {
            throw new LimsException(String.format("业务中台-获取检测方法返回错误: [%s]", e.getMessage()), e);
        }

        if (Objects.isNull(response)) {
            throw new LimsException("业务中台-获取检测方法返回结果为空");
        }

        if (!Objects.equals(response.getCode(), BasicErrCode.SUCCESS.getCode())) {
            throw new LimsException(String.format("业务中台-获取检测方法返回错误, 错误信息: [%s]", response.getMsg()));
        }

        if (Objects.isNull(response.getData())) {
            return Collections.emptyList();
        }
        return response.getData().stream().sorted(Comparator.comparing(SysDictDto::getDictCode)).map(obj -> {
            final DictItemDto dictItemDto = new DictItemDto();
            dictItemDto.setDictCode(obj.getDictCode());
            dictItemDto.setDictName(obj.getDictValue());
            return dictItemDto;
        }).collect(Collectors.toList());
    }

    @CacheEvict(allEntries = true)
    @Override
    public void saveBatch(List<InstrumentReportItemDto> instrumentReportItems) {
        if (CollectionUtils.isEmpty(instrumentReportItems)) {
            return;
        }

        final List<TbInstrumentReportItem> tbs =
            JSON.parseArray(JSON.toJSONString(instrumentReportItems), TbInstrumentReportItem.class);

        for (TbInstrumentReportItem tb : tbs) {
            if (instrumentReportItemMapper.insert(tb) < 1) {
                throw new LimsException("新增仪器报告项目失败");
            }
        }
    }

}
