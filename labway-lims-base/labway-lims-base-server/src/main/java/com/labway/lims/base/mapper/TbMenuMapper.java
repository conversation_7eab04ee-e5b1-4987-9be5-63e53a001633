package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.model.TbMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 菜单模块 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbMenuMapper extends BaseMapper<TbMenu> {

    /**
     * 根据角色查询菜单
     */
    List<TbMenu> selectByRoleId(@Param("roleId") long roleId);
}
