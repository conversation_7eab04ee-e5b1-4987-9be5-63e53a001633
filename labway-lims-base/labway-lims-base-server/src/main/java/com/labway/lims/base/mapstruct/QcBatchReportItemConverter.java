
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.QcBatchReportItemDto;
import com.labway.lims.base.model.TbQcBatchReportItem;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 质控批号报告项目 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface QcBatchReportItemConverter {

    QcBatchReportItemDto qcBatchReportItemDtoFromTbObj(TbQcBatchReportItem obj);

    QcBatchReportItemDto qcBatchReportItemDtoFromTbDto(QcBatchReportItemDto obj);

    TbQcBatchReportItem tbQcBatchReportItemFromTbDto(QcBatchReportItemDto obj);

    List<QcBatchReportItemDto> qcBatchReportItemDtoListFromTbObj(List<TbQcBatchReportItem> list);

    List<TbQcBatchReportItem> tbQcBatchReportItemListFromTbDto(List<QcBatchReportItemDto> list);

}
