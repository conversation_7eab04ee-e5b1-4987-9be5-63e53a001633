package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class UpdateHspOrgDiscountVo {

    /**
     * 折扣id
     */
    private Long discountId;

    /**
     * 基准包id
     */
    private Long packageId;

    /**
     * 送检类型编码
     */
    private String sendTypeCode;

    /**
     * 送检类型名称
     */
    private String sendType;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 折扣率
     */
    private BigDecimal discount;
}
