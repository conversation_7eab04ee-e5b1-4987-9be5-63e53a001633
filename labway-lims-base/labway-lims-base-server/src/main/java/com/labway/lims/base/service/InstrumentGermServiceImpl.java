package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentGermDto;
import com.labway.lims.base.api.service.InstrumentGermService;
import com.labway.lims.base.mapper.TbInstrumentGermMapper;
import com.labway.lims.base.mapstruct.InstrumentGermConverter;
import com.labway.lims.base.model.TbInstrumentGerm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <p>
 * 仪器细菌 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Slf4j
@DubboService
public class InstrumentGermServiceImpl implements InstrumentGermService {

    @Resource
    private TbInstrumentGermMapper tbInstrumentGermMapper;

    @Resource
    private InstrumentGermConverter instrumentGermConverter;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public List<InstrumentGermDto> selectByInstrumentId(long instrumentId) {
        LambdaQueryWrapper<TbInstrumentGerm> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbInstrumentGerm::getInstrumentId, instrumentId);
        queryWrapper.eq(TbInstrumentGerm::getIsDelete, YesOrNoEnum.NO.getCode());
        return instrumentGermConverter.instrumentGermDtosFromTbObj(tbInstrumentGermMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addInstrumentGerms(List<InstrumentGermDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 仪器细菌
        List<TbInstrumentGerm> targetList = instrumentGermConverter.tbInstrumentGermsFromTbObjDto(list);
        // 数量 分区批次插入
        List<List<TbInstrumentGerm>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbInstrumentGermMapper.batchAddTbInstrumentGerms(item));

        log.info("用户 [{}] 新增仪器细菌成功", loginUser.getNickname());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByInstrumentGermId(InstrumentGermDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbInstrumentGerm target = instrumentGermConverter.tbInstrumentGermFromTbObjDto(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbInstrumentGermMapper.updateById(target) < 1) {
            throw new LimsException("修改仪器细菌失败");
        }

        log.info("用户 [{}] 修改仪器细菌成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInstrumentGerm(Collection<Long> needInstrumentGermIds, List<InstrumentGermDto> addList,
        List<InstrumentGermDto> updateList) {
        LoginUserHandler.User loginUser = LoginUserHandler.get();
        if (CollectionUtils.isNotEmpty(needInstrumentGermIds)) {
            tbInstrumentGermMapper.deleteBatchIds(needInstrumentGermIds);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            LinkedList<Long> genIds = snowflakeService.genIds(addList.size());
            Date date = new Date();
            addList.forEach(item -> {
                item.setInstrumentGermId(genIds.pop());

                item.setOrgId(loginUser.getOrgId());
                item.setOrgName(loginUser.getOrgName());
                item.setCreateDate(date);
                item.setUpdateDate(date);
                item.setCreatorId(loginUser.getUserId());
                item.setCreatorName(loginUser.getNickname());
                item.setUpdaterId(loginUser.getUserId());
                item.setUpdaterName(loginUser.getNickname());
                item.setIsDelete(YesOrNoEnum.NO.getCode());
            });
            this.addInstrumentGerms(addList);
        }
        updateList.forEach(this::updateByInstrumentGermId);
    }

    @Override
    public void deleteByGermId(Long instrumentId, Long germId) {
        tbInstrumentGermMapper.delete(new LambdaQueryWrapper<TbInstrumentGerm>()
            .eq(TbInstrumentGerm::getInstrumentId, instrumentId).eq(TbInstrumentGerm::getGermId, germId));
        log.info("用户 [{}] 删除仪器 [{}] 细菌 [{}] 成功", LoginUserHandler.get().getNickname(), instrumentId, germId);
    }

    @Override
    public InstrumentGermDto selectByGermId(Long instrumentId, Long germId) {
        LambdaQueryWrapper<TbInstrumentGerm> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbInstrumentGerm::getInstrumentId, instrumentId);
        queryWrapper.eq(TbInstrumentGerm::getGermId, germId);
        queryWrapper.last("limit 1");
        return instrumentGermConverter.instrumentGermDtoFromTbObj(tbInstrumentGermMapper.selectOne(queryWrapper));
    }
}
