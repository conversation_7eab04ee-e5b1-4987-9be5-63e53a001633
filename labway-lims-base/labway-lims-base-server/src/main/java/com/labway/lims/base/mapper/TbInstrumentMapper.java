package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.model.TbInstrument;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仪器 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbInstrumentMapper extends BaseMapper<TbInstrument> {

    /**
     * 根据仪器专业小组ID
     */
    List<InstrumentDto> selectByInstrumentGroupId(@Param("instrumentGroupId") long instrumentGroupId);
}
