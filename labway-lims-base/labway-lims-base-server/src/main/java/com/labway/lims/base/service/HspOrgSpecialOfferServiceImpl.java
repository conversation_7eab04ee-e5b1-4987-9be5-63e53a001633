package com.labway.lims.base.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.AddHspOrgSpecialOfferProjectDto;
import com.labway.lims.base.api.dto.AddHspOrgSpecialOfferProjectSendTypeDto;
import com.labway.lims.base.api.dto.HspOrgSpecialOfferDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrgSpecialOfferService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.mapper.TbHspOrgSpecialOfferMapper;
import com.labway.lims.base.mapstruct.HspOrgSpecialOfferConverter;
import com.labway.lims.base.model.TbHspOrgSpecialOffer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@DubboService
@Slf4j
public class HspOrgSpecialOfferServiceImpl implements HspOrgSpecialOfferService {
    @Resource
    private TbHspOrgSpecialOfferMapper tbHspOrgSpecialOfferMapper;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private HspOrgSpecialOfferConverter hspOrgSpecialOfferConverter;

    @Override
    public List<HspOrgSpecialOfferDto> selectHspOrgSpecialOfferProject(Long hspOrgId) {
        if (Objects.isNull(hspOrgId)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbHspOrgSpecialOffer> eq = Wrappers.lambdaQuery(TbHspOrgSpecialOffer.class)

                .eq(TbHspOrgSpecialOffer::getHspOrgId, hspOrgId).orderByDesc(TbHspOrgSpecialOffer::getOfferId);

        final List<TbHspOrgSpecialOffer> tbHspOrgSpecialOffers = tbHspOrgSpecialOfferMapper.selectList(eq);

        return JSON.parseArray(JSON.toJSONString(tbHspOrgSpecialOffers),
                com.labway.lims.base.api.dto.HspOrgSpecialOfferDto.class);
    }

    @Override
    public List<HspOrgSpecialOfferDto> selectByHspOrgIds(Collection<Long> hspOrgIds) {
        if (CollectionUtils.isEmpty(hspOrgIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbHspOrgSpecialOffer> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbHspOrgSpecialOffer::getHspOrgId, hspOrgIds);
        queryWrapper.eq(TbHspOrgSpecialOffer::getIsDelete, YesOrNoEnum.NO.getCode());

        return hspOrgSpecialOfferConverter
                .hspOrgSpecialOfferDtoListFromTbObjList(tbHspOrgSpecialOfferMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<HspOrgSpecialOfferDto> addHspOrgSpecialOfferProject(AddHspOrgSpecialOfferProjectDto param) {
        BigDecimal discount = param.getDiscount().setScale(4, RoundingMode.HALF_UP);
        Date startDate = param.getStartDate();
        Date endDate = param.getEndDate();
        Map<String,
                String> applyTypeByCode = param.getSendTypeList().stream()
                .collect(Collectors.toMap(AddHspOrgSpecialOfferProjectSendTypeDto::getSendTypeCode,
                        AddHspOrgSpecialOfferProjectSendTypeDto::getSendType, (key1, key2) -> key1));

        Integer isTieredPricing = ObjectUtils.defaultIfNull(param.getIsTieredPricing(), YesOrNoEnum.YES.getCode());

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(param.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalArgumentException("送检机构不存在");
        }

        final List<Long> testItemIds = param.getTestItemIds();
        final List<TestItemDto> testItems = testItemService.selectByTestItemIds(testItemIds);
        if (CollectionUtils.isEmpty(testItems)) {
            throw new IllegalArgumentException("检验项目不存在");
        }

        // 查询已存在的特价项目
        Map<Long,
                Map<String, List<HspOrgSpecialOfferDto>>> groupingByTestItemId = selectByHspOrgIdAndTestItemIdsAndDateRange(
                param.getHspOrgId(), testItemIds, applyTypeByCode.keySet(), startDate, endDate, true).stream()
                .collect(Collectors.groupingBy(HspOrgSpecialOfferDto::getTestItemId, // 第一级分组
                        Collectors.groupingBy(HspOrgSpecialOfferDto::getSendTypeCode)));// 第二级分组

        // 判断是否出现重复的特价项目 根据 testItemId + sendTypeCode + startDate + endDate
        for (final TestItemDto testItem : testItems) {
            Map<String, List<HspOrgSpecialOfferDto>> groupingBySendTypeCode =
                    groupingByTestItemId.get(testItem.getTestItemId());
            if (Objects.isNull(groupingBySendTypeCode)) {
                continue;
            }

            for (Map.Entry<String, String> entry : applyTypeByCode.entrySet()) {
                List<HspOrgSpecialOfferDto> hspOrgSpecialOfferDtoList = groupingBySendTypeCode.get(entry.getKey());
                if (CollectionUtils.isNotEmpty(hspOrgSpecialOfferDtoList)) {
                    throw new IllegalStateException(
                            String.format("送检机构 [%s] 送检类型 [%s] 日期范围已存在 [%s - %s]", hspOrganization.getHspOrgName(),
                                    entry.getValue(), DateUtil.formatDate(startDate), DateUtil.formatDate(endDate)));

                }
            }
        }

        final LinkedList<Long> ids = snowflakeService.genIds(testItemIds.size() * applyTypeByCode.size());

        List<HspOrgSpecialOfferDto> hspOrgSpecialOffers = Lists.newArrayList();

        final Long userId = LoginUserHandler.get().getUserId();
        final Long orgId = LoginUserHandler.get().getOrgId();
        final String orgName = LoginUserHandler.get().getOrgName();
        final String nikeName = LoginUserHandler.get().getNickname();
        final Date now = new Date();
        for (final TestItemDto testItem : testItems) {
            for (Map.Entry<String, String> entry : applyTypeByCode.entrySet()) {
                final String testItemCode = testItem.getTestItemCode();
                final Long testItemId = testItem.getTestItemId();
                BigDecimal discountPrice = testItem.getFeePrice().multiply(discount).setScale(2, RoundingMode.HALF_UP);

                final HspOrgSpecialOfferDto hspOrgSpecialOffer = new HspOrgSpecialOfferDto();
                hspOrgSpecialOffer.setOfferId(ids.pop());
                hspOrgSpecialOffer.setHspOrgId(hspOrganization.getHspOrgId());
                hspOrgSpecialOffer.setHspOrgName(hspOrganization.getHspOrgName());
                hspOrgSpecialOffer.setTestItemId(testItemId);
                hspOrgSpecialOffer.setTestItemCode(testItemCode);
                hspOrgSpecialOffer.setSendTypeCode(entry.getKey());
                hspOrgSpecialOffer.setSendType(entry.getValue());
                hspOrgSpecialOffer.setStartDate(startDate);
                hspOrgSpecialOffer.setEndDate(endDate);
                hspOrgSpecialOffer.setDiscount(discount);
                hspOrgSpecialOffer.setDiscountPrice(discountPrice);
                hspOrgSpecialOffer.setFeePrice(testItem.getFeePrice());
                hspOrgSpecialOffer.setOrgId(orgId);
                hspOrgSpecialOffer.setOrgName(orgName);
                hspOrgSpecialOffer.setCreateDate(now);
                hspOrgSpecialOffer.setUpdateDate(now);
                hspOrgSpecialOffer.setUpdaterId(userId);
                hspOrgSpecialOffer.setUpdaterName(nikeName);
                hspOrgSpecialOffer.setCreatorId(userId);
                hspOrgSpecialOffer.setCreatorName(nikeName);
                hspOrgSpecialOffer.setIsDelete(YesOrNoEnum.NO.getCode());
                hspOrgSpecialOffer.setIsTieredPricing(isTieredPricing);

                hspOrgSpecialOffers.add(hspOrgSpecialOffer);
            }

        }

        this.addHspOrgSpecialOfferDtos(hspOrgSpecialOffers);

        log.info("用户 [{}] 专业组 [{}] 新增送检机构特价项目成功, param: {}", LoginUserHandler.get().getNickname(),
                LoginUserHandler.get().getNickname(), param);

        return hspOrgSpecialOffers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addHspOrgSpecialOfferDtos(List<HspOrgSpecialOfferDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 要添加的 特价项目
        List<TbHspOrgSpecialOffer> targetList = hspOrgSpecialOfferConverter.tbHspOrgSpecialOfferListFromTbDto(list);

        // 数量 分区批次插入
        List<List<TbHspOrgSpecialOffer>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbHspOrgSpecialOfferMapper.batchAddTbHspOrgSpecialOffers(item));

        log.info("新增特价项目[{}]成功", JSON.toJSONString(targetList));
    }

    @Override
    public List<HspOrgSpecialOfferDto> selectByHspOrgIdAndTestItemIds(Long hspOrgId, List<Long> testItemIds) {
        if (CollectionUtils.isEmpty(testItemIds) || Objects.isNull(hspOrgId)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbHspOrgSpecialOffer> eq = Wrappers.lambdaQuery(TbHspOrgSpecialOffer.class)
                .eq(TbHspOrgSpecialOffer::getHspOrgId, hspOrgId).in(TbHspOrgSpecialOffer::getTestItemId, testItemIds);

        final List<TbHspOrgSpecialOffer> tbHspOrgSpecialOffers = tbHspOrgSpecialOfferMapper.selectList(eq);

        return hspOrgSpecialOfferConverter.hspOrgSpecialOfferDtoListFromTbObjList(tbHspOrgSpecialOffers);

    }

    @Override
    public void deleteByHspOrgId(List<Long> hspOrgIds) {
        if (CollectionUtils.isEmpty(hspOrgIds)) {
            return;
        }

        // 删除机构

        tbHspOrgSpecialOfferMapper
                .delete(Wrappers.lambdaQuery(TbHspOrgSpecialOffer.class).in(TbHspOrgSpecialOffer::getHspOrgId, hspOrgIds));

        log.info("用户 [{}] 专业组 [{}] 删除送检机构特价项目成功, hspOrgIds: {}", LoginUserHandler.get().getNickname(),
                LoginUserHandler.get().getNickname(), hspOrgIds);
    }

    @Override
    public void deleteByOfferId(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        tbHspOrgSpecialOfferMapper.deleteBatchIds(ids);

        log.info("用户 [{}] 专业组 [{}] 删除送检机构特价项目成功, ids: {}", LoginUserHandler.get().getNickname(),
                LoginUserHandler.get().getNickname(), ids);
    }

    @Nullable
    @Override
    public HspOrgSpecialOfferDto selectByOfferId(long offerId) {
        final TbHspOrgSpecialOffer hspOrgSpecialOffer = tbHspOrgSpecialOfferMapper.selectById(offerId);
        if (Objects.isNull(hspOrgSpecialOffer)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(hspOrgSpecialOffer), HspOrgSpecialOfferDto.class);
    }

    @Override
    public List<HspOrgSpecialOfferDto> selectByOfferIds(Collection<Long> offerIds) {
        if (CollectionUtils.isEmpty(offerIds)) {
            return Collections.emptyList();
        }

        return JSON.parseArray(JSON.toJSONString(tbHspOrgSpecialOfferMapper.selectBatchIds(offerIds)),
                HspOrgSpecialOfferDto.class);
    }

    @Override
    public Collection<Long> selectHasSpecialProjectHspOrg() {
        final LambdaQueryWrapper<TbHspOrgSpecialOffer> select = Wrappers.lambdaQuery(TbHspOrgSpecialOffer.class)
                .orderByDesc(TbHspOrgSpecialOffer::getOfferId).select(TbHspOrgSpecialOffer::getHspOrgId);
        return tbHspOrgSpecialOfferMapper.selectList(select).stream().map(TbHspOrgSpecialOffer::getHspOrgId)
                .collect(Collectors.toSet());
    }

    @Override
    public List<HspOrgSpecialOfferDto> selectByByHspOrgIdsAndDateRangeAndAndApplyTypes(Collection<Long> hspOrgIds,
                                                                                       Date minDate, Date maxDate, Collection<String> applyTypes) {
        if (CollectionUtils.isEmpty(hspOrgIds) || CollectionUtils.isEmpty(applyTypes) || Objects.isNull(maxDate)
                || Objects.isNull(minDate)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbHspOrgSpecialOffer> query = Wrappers.lambdaQuery(TbHspOrgSpecialOffer.class)
                .in(TbHspOrgSpecialOffer::getHspOrgId, hspOrgIds)
                .in(CollectionUtils.isNotEmpty(applyTypes), TbHspOrgSpecialOffer::getSendTypeCode, applyTypes).and(and -> {
                    and.le(TbHspOrgSpecialOffer::getStartDate, maxDate);
                    and.ge(TbHspOrgSpecialOffer::getEndDate, minDate);
                    and.or();

                    and.le(TbHspOrgSpecialOffer::getStartDate, minDate);
                    and.ge(TbHspOrgSpecialOffer::getEndDate, minDate);
                    and.or();

                    and.le(TbHspOrgSpecialOffer::getStartDate, maxDate);
                    and.ge(TbHspOrgSpecialOffer::getEndDate, maxDate);
                });

        return JSON.parseArray(JSON.toJSONString(tbHspOrgSpecialOfferMapper.selectList(query)),
                HspOrgSpecialOfferDto.class);
    }

    @Override
    public void updateSpecialOffer(HspOrgSpecialOfferDto updateSpecialOfferDto) {
        final Long offerId = updateSpecialOfferDto.getOfferId();
        final TbHspOrgSpecialOffer specialOffer = tbHspOrgSpecialOfferMapper.selectById(offerId);
        if (Objects.isNull(specialOffer)) {
            throw new IllegalStateException("特价项目不存在");
        }
        final String testItemCode = specialOffer.getTestItemCode();
        final Long testItemId = specialOffer.getTestItemId();
        final Date startDate = updateSpecialOfferDto.getStartDate();
        final Date endDate = updateSpecialOfferDto.getEndDate();
        final String sendType = updateSpecialOfferDto.getSendType();
        final String sendTypeCode = updateSpecialOfferDto.getSendTypeCode();
        // 查询已存在的特价项目
        List<HspOrgSpecialOfferDto> hspOrgSpecialOfferDtoList =
                selectByHspOrgIdAndTestItemIdsAndDateRange(specialOffer.getHspOrgId(),
                        Collections.singletonList(testItemId), Collections.singleton(sendTypeCode), startDate, endDate, true).stream()
                        .filter(obj -> !Objects.equals(obj.getOfferId(), offerId)).collect(Collectors.toList());

        // 判断是否出现重复的特价项目 根据 testItemId + sendTypeCode + startDate + endDate
        if (CollectionUtils.isNotEmpty(hspOrgSpecialOfferDtoList)) {
            throw new IllegalArgumentException(
                    String.format("送检机构 [%s] 检验项目Code [%s] 送检类型 [%s] 生效日期 [%s - %s]已存在", specialOffer.getHspOrgName(),
                            testItemCode, sendType, DateUtil.formatDate(startDate), DateUtil.formatDate(endDate)));
        }

        specialOffer.setUpdaterId(LoginUserHandler.get().getUserId());
        specialOffer.setUpdateDate(new Date());
        specialOffer.setUpdaterName(LoginUserHandler.get().getNickname());
        specialOffer.setDiscount(updateSpecialOfferDto.getDiscount().setScale(4, RoundingMode.HALF_UP));
        specialOffer.setDiscountPrice(updateSpecialOfferDto.getDiscountPrice());
        specialOffer.setFeePrice(updateSpecialOfferDto.getFeePrice());
        specialOffer.setStartDate(startDate);
        specialOffer.setEndDate(endDate);
        specialOffer.setSendType(sendType);
        specialOffer.setSendTypeCode(sendTypeCode);
        specialOffer.setIsTieredPricing(updateSpecialOfferDto.getIsTieredPricing());
        tbHspOrgSpecialOfferMapper.updateById(specialOffer);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importHspOrgSpecialOfferProject(List<HspOrgSpecialOfferDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        final Set<Integer> errorNumSet = new HashSet<>();

        // 判断excel中是否有重复的时间范围条件
        {
            final Map<String, List<HspOrgSpecialOfferDto>> testItemIdAndSendTypeCodeMap = dtos.stream()
                    .collect(Collectors.groupingBy(e -> e.getTestItemCode() + "-" + e.getSendTypeCode()));

            for (Map.Entry<String, List<HspOrgSpecialOfferDto>> entry : testItemIdAndSendTypeCodeMap.entrySet()) {
                List<HspOrgSpecialOfferDto> values = entry.getValue();
                // 如果数量为1则直接跳过循环, 说明当前excel中没有存在的生效范围
                if (CollectionUtils.size(values) <= NumberUtils.INTEGER_ONE) {
                    continue;
                }
                // 如果数量不为1， 则代表可能有生效时间重复的数据
                // 获取当前元素(i) 和 当前元素之后的所有元素(j) 进行时间比较
                for (int i = 0; i < values.size(); i++) {

                    HspOrgSpecialOfferDto e = values.get(i);
                    for (int j = i + 1; j < values.size(); j++) {
                        HspOrgSpecialOfferDto dto = values.get(j);

                        // 时间存在交集则收集行号
                        if (DateUtil.isOverlap(e.getStartDate(), e.getEndDate(), dto.getStartDate(), dto.getEndDate())) {
                            errorNumSet.add(dto.getNum());
                        }
                    }
                }
            }
        }

        // 判断数据中是否有重复的生效时间范围
        {
            final HspOrgSpecialOfferDto hspOrgSpecialOfferDto = dtos.get(0);
            final Set<Long> testItemIdSet = dtos.stream().map(HspOrgSpecialOfferDto::getTestItemId).collect(Collectors.toSet());
            final Set<String> sendTypeCodeSet = dtos.stream().map(HspOrgSpecialOfferDto::getSendTypeCode).collect(Collectors.toSet());

            // 查询数据库中有效的送检机构， 检验项目， 送检类型
            // 并根据（检验项目code-送检类型code）分组
            final Map<String, List<HspOrgSpecialOfferDto>> testItemIdAndSendTypeCodeMap = this.selectByHspOrgIdAndTestItemIdsAndDateRange(hspOrgSpecialOfferDto.getHspOrgId(), testItemIdSet, sendTypeCodeSet, null, null, false)
                    .stream()
                    .collect(Collectors.groupingBy(e -> e.getTestItemCode() + "-" + e.getSendTypeCode()));

            // 将id生成插入
            final LinkedList<Long> ids = snowflakeService.genIds(dtos.size());

            for (HspOrgSpecialOfferDto dto : dtos) {
                dto.setOfferId(ids.pop());

                // 获取当前行的检验项目code和送检类型code 是否在库中存在
                final List<HspOrgSpecialOfferDto> hspOrgSpecialOfferDtos = testItemIdAndSendTypeCodeMap.get(dto.getTestItemCode() + "-" + dto.getSendTypeCode());
                if (CollectionUtils.isEmpty(hspOrgSpecialOfferDtos)) {
                    continue;
                }

                // 如果存在则判断生效时间是否重复  存在交集（相等也算） 则为重复生效时间
                hspOrgSpecialOfferDtos.stream().filter(e -> {
                            // （库中已存在的开始时间 <= 插入数据的开始时间 <= 库中已存在的结束时间） = true
                            // （库中已存在的开始时间 <= 插入数据的结束时间 <= 库中已存在的结束时间） = true
                            return DateUtil.isOverlap(e.getStartDate(), e.getEndDate(), dto.getStartDate(), dto.getEndDate());
                        }).findFirst()
                        // 如果有时间交集的数据则收集异常
                        .ifPresent(e -> errorNumSet.add(dto.getNum()));
            }
        }

        // 如果异常行Set中有数据， 则抛出异常
        if (CollectionUtils.isNotEmpty(errorNumSet)) {
            // 异常收集
            final String errStr = errorNumSet.stream().sorted(Integer::compareTo).map(num -> String.format("%s 行，生效范围已存在", num)).collect(Collectors.joining("\n"));
            throw new IllegalArgumentException(errStr);
        }

        this.addHspOrgSpecialOfferDtos(dtos);


    }

    /**
     * 查询 交集数据
     *
     * @param hspOrgId    送检机构
     * @param testItemIds 检验项目ids
     * @param applyTypes  就诊类型
     * @param startDate   开始时间
     * @param endDate     结束时间
     */
    private List<HspOrgSpecialOfferDto> selectByHspOrgIdAndTestItemIdsAndDateRange(Long hspOrgId,
                                                                                   Collection<Long> testItemIds, Collection<String> applyTypes, Date startDate, Date endDate, boolean notImport) {

        if (Objects.isNull(hspOrgId) || CollectionUtils.isEmpty(testItemIds) || CollectionUtils.isEmpty(applyTypes)) {
            return Collections.emptyList();
        }
        if (notImport && (Objects.isNull(startDate) || Objects.isNull(endDate))) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbHspOrgSpecialOffer> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(TbHspOrgSpecialOffer::getHspOrgId, hspOrgId);
        queryWrapper.in(TbHspOrgSpecialOffer::getSendTypeCode, applyTypes);
        queryWrapper.in(TbHspOrgSpecialOffer::getTestItemId, testItemIds);

        // 时间 存在交集
        queryWrapper.and(Objects.nonNull(startDate) && Objects.nonNull(endDate),
                wrapper -> wrapper
                        .and(w1 -> w1.ge(TbHspOrgSpecialOffer::getStartDate, startDate)
                                .and(w2 -> w2.le(TbHspOrgSpecialOffer::getStartDate, endDate)))
                        .or(w3 -> w3.le(TbHspOrgSpecialOffer::getStartDate, startDate)
                                .and(w4 -> w4.ge(TbHspOrgSpecialOffer::getEndDate, endDate)))
                        .or(w5 -> w5.ge(TbHspOrgSpecialOffer::getEndDate, startDate)
                                .and(w6 -> w6.le(TbHspOrgSpecialOffer::getEndDate, endDate))));

        return hspOrgSpecialOfferConverter
                .hspOrgSpecialOfferDtoListFromTbObjList(tbHspOrgSpecialOfferMapper.selectList(queryWrapper));

    }

}
