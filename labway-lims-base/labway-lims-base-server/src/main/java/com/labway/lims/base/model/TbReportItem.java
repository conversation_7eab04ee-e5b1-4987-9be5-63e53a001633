package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 检验报告项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_report_item")
public class TbReportItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报告项目ID
     */
    @TableId
    private Long reportItemId;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 1:已经删除 0未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;
}
