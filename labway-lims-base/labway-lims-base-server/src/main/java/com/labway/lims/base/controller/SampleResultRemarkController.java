package com.labway.lims.base.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.SampleResultRemarkDto;
import com.labway.lims.base.api.service.SampleResultRemarkService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;


/**
 * 样本结果备注
 */
@RestController
@RequestMapping("/sample-result-remark")
public class SampleResultRemarkController extends BaseController {
    @DubboReference
    private SampleResultRemarkService sampleResultRemarkService;

    /**
     * 获取异常结果
     */
    @GetMapping("/remarks")
    public Object remarks() {
        return sampleResultRemarkService.selectByGroupId(LoginUserHandler.get().getGroupId());
    }

    /**
     * 添加
     */
    @PostMapping("/add")
    public Object add(@RequestBody SampleResultRemarkDto dto) throws Exception {

        if (StringUtils.isBlank(dto.getResultRemark())) {
            throw new IllegalArgumentException("结果备注不能为空");
        }

        dto.setResultRemark(StringUtils.trim(dto.getResultRemark()));
        dto.setSampleResultRemarkId(null);
        dto.setOrgId(LoginUserHandler.get().getOrgId());
        dto.setOrgName(LoginUserHandler.get().getOrgName());

        dto.setGroupId(LoginUserHandler.get().getGroupId());
        dto.setGroupName(LoginUserHandler.get().getNickname());

        return Map.of("id", sampleResultRemarkService
                .addSampleResultRemark(dto));
    }

    /**
     * 修改参考范围
     */
    @PostMapping("/update")
    public Object update(@RequestBody SampleResultRemarkDto dto) {
        if (Objects.isNull(dto.getSampleResultRemarkId())) {
            throw new IllegalArgumentException("参数错误");
        }


        if (!sampleResultRemarkService.updateBySampleResultRemarkId(dto)) {
            throw new IllegalStateException("修改结果备注失败");
        }


        return Collections.emptyMap();
    }


    /**
     * 删除参考范围
     */
    @DeleteMapping("/delete")
    public Object delete(Long sampleResultRemarkId) {

        if (Objects.isNull(sampleResultRemarkId)) {
            throw new IllegalStateException("参数错误");
        }

        sampleResultRemarkService.deleteBySampleResultRemarkId(sampleResultRemarkId);

        return Collections.emptyMap();
    }


}
