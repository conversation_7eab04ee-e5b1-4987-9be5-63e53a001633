package com.labway.lims.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RecordCalculateVo {
    /**
     * QC批次记录
     */
    private String qcRecordBatch;
    /**
     * 客商报告项目ID
     */
    private Long customerReportId;
    /**
     * 计算记录开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime startTime;
    /**
     * 计算记录结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime endTime;
}
