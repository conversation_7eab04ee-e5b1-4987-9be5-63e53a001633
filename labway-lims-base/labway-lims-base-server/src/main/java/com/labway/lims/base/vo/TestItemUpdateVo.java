package com.labway.lims.base.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 检验项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class TestItemUpdateVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 别名
     */
    @Compare("项目别名")
    private String aliasName;

    /**
     * 检验方法ID
     */
    private String examMethodCode;

    /**
     * 检验方法名称
     */
    @Compare("检验方法")
    private String examMethodName;

    /**
     * 名称缩写
     */
    @Compare("项目名称缩写")
    private String shortName;

    /**
     * 管型 code
     */
    private String tubeCode;

    /**
     * 管型 name
     */
    @Compare("管型")
    private String tubeName;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    @Compare("专业组")
    private String groupName;

    /**
     * 是否支持外送
     *
     * @see YesOrNoEnum
     */
    private Integer enableExport;

    /**
     * 外送回传报告时间
     */
    private String exportDate;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

    /**
     * 外送机构名称
     */
    @Compare("外送机构")
    private String exportOrgName;

    /**
     * 项目类型ID
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 样本类型ID
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;

    /**
     * 是否it3000
     */
    private Integer enableIt3000;

    /**
     * 是否计费
     */
    private Integer enableFee;

    /**
     * 收费编码
     */
    private String feeCode;

    /**
     * 收费名称
     */
    private String feeName;

    /**
     * 收费价格
     */
    private BigDecimal feePrice;

    /**
     * 财务专业组code
     */
    private String financeGroupCode;

    /**
     * 财务专业组名称
     */
    private String financeGroupName;

    /**
     * 基本量
     */
    private BigDecimal basicQuantity;

    /**
     * 复查量
     */
    private BigDecimal checkQuantity;

    /**
     * 死腔量
     */
    private BigDecimal deadSpaceQuantity;

    /**
     * 存放说明
     */
    private String stashRemark;

    /**
     * 机构 ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 1:已经删除 0未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 检验日期
     */
    private Integer testDate;

    /**
     * 限制性别
     * 0 不限制，1 男，2 女
     */
    private Integer limitSex;

    /**
     * 二次分拣日期(0:当时、1~7周一到周日、多个逗号隔开)
     */
    private String twoPickDay;

    /**
     * 二次分拣时间(精确到分钟)
     */
    private String twoPickTime;

	/**
	 * 是否允许自动审核0-否，1-是，默认为0
	 * @see YesOrNoEnum
	 */
	@Compare("自动审核")
	@Range(max = 1, min = 0, message = "自动审核参数错误")
	private Integer autoAudit;

	/**
	 * 决策地址(自动审核的规则地址) 默认为''
	 */
	@Compare("决策地址")
	@Length(max = 200, message = "决策地址长度不能超过200个字符")
	private String decisionBasis;
}
