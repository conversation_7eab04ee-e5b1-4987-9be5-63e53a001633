package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.apply.PdaDoubleCheckFiledEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.SaveHspOrganizationFiledDto;
import com.labway.lims.base.api.service.SupplementalRecordFieldSettingService;
import com.labway.lims.base.vo.HspOrganizationFiledVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description 补录字段设置控制器
 * <AUTHOR>
 * @date 2024-05-24
 */
@RestController
@RequestMapping("/supplemental-record-field-setting")
public class SupplementalRecordFieldSettingController extends BaseController {

    @Resource
    private SupplementalRecordFieldSettingService supplementalRecordFieldSettingService;


    @PostMapping("/insert")
    public Object insert(@RequestBody HspOrganizationFiledVo vo) {
        checkAddParam(vo);

        final SaveHspOrganizationFiledDto dto =
                JSON.parseObject(JSON.toJSONString(vo), SaveHspOrganizationFiledDto.class);
        final List<String> codes = Arrays.stream(PdaDoubleCheckFiledEnum.values()).map(PdaDoubleCheckFiledEnum::getCode).collect(Collectors.toList());
        for (SaveHspOrganizationFiledDto.Field filed : dto.getFileds()) {
            if(!codes.contains(filed.getCode())){
                throw new IllegalStateException(String.format("%s 字段不正确", filed.getName()));
            }
        }

        return supplementalRecordFieldSettingService.insertSupplementalRecordFieldSetting(dto);
    }

    @GetMapping("/fields")
    public Object fields() {

        return Arrays.stream(PdaDoubleCheckFiledEnum.values()).sorted(Comparator.comparing(PdaDoubleCheckFiledEnum::getSort))
                .map(e-> Map.of("code", e.getCode(), "name", e.getDesc(), "sort", e.getSort()))
                .collect(Collectors.toList());

    }

    @PostMapping("/update")
    public Object update(@RequestBody HspOrganizationFiledVo vo) {
        checkAddParam(vo);

        final SaveHspOrganizationFiledDto dto =
                JSON.parseObject(JSON.toJSONString(vo), SaveHspOrganizationFiledDto.class);
        final List<String> codes = Arrays.stream(PdaDoubleCheckFiledEnum.values()).map(PdaDoubleCheckFiledEnum::getCode).collect(Collectors.toList());
        for (SaveHspOrganizationFiledDto.Field filed : dto.getFileds()) {
            if(!codes.contains(filed.getCode())){
                throw new IllegalStateException(String.format("%s 字段不正确", filed.getName()));
            }
        }

        return supplementalRecordFieldSettingService.updateSupplementalRecordFieldSetting(dto);
    }


    @PostMapping("/delete")
    public Object deleteByIds(@RequestBody List<Long> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        return supplementalRecordFieldSettingService.deleteByIds(ids);
    }

    @GetMapping("/select-all")
    public Object selectAll() {
        return supplementalRecordFieldSettingService.selectAll();
    }

    @GetMapping("/select-one")
    public Object selectOne(@RequestParam Long field) {
        SaveHspOrganizationFiledDto saveHspOrganizationFiledDto = supplementalRecordFieldSettingService.selectById(field);
        if (Objects.isNull(saveHspOrganizationFiledDto)) {
            throw new IllegalStateException("未查询到对应的对照关系");
        }
        return saveHspOrganizationFiledDto;
    }

    public void checkAddParam(HspOrganizationFiledVo vo) {
        final Set<HspOrganizationFiledVo.Filed> fileds = vo.getFileds();
        if (fileds == null || fileds.isEmpty()) {
            throw new IllegalStateException("对照内容不能为空");
        }

        final int size = fileds.stream().map(HspOrganizationFiledVo.Filed::getCode).collect(Collectors.toSet()).size();

        if (size != fileds.size()) {
            throw new IllegalStateException("对照内容不能重复");
        }

        if (Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalStateException("送检机构不存在");
        }
    }
}
