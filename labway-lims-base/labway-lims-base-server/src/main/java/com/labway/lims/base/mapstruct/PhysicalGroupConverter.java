package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.PhysicalGroupDto;
import com.labway.lims.base.model.TbPhysicalGroup;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 体检团体 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface PhysicalGroupConverter {

    PhysicalGroupDto physicalGroupDtoFromTbObj(TbPhysicalGroup obj);

    List<PhysicalGroupDto> physicalGroupDtoListFromTbObjList(List<TbPhysicalGroup> list);

}
