package com.labway.lims.base.controller;

import com.google.common.collect.Lists;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.api.enums.microbiology.MicroTestMethodEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.api.service.GermGenusService;
import com.labway.lims.base.api.service.MedicineGermRelationService;
import com.labway.lims.base.api.service.MedicineService;
import com.labway.lims.base.mapstruct.MedicineGermRelationConverter;
import com.labway.lims.base.vo.GermGenusMedicineResponseVo;
import com.labway.lims.base.vo.MedicineGermRelationSaveRequestVo;
import com.labway.lims.base.vo.SelectGenusMedicineResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 药物细菌关联 API
 *
 * <AUTHOR>
 * @since 2023/7/6 14:21
 */
@Slf4j
@RestController
@RequestMapping("/medicine-germ")
public class MedicineGermRelationController extends BaseController {
    @DubboReference
    private DictService dictService;
    @DubboReference
    private MedicineService medicineService;

    @DubboReference
    private MedicineGermRelationService medicineGermRelationService;

    @DubboReference
    private GermGenusService germGenusService;

    @DubboReference
    private RabbitMQService rabbitMQService;

    @Resource
    private MedicineGermRelationConverter medicineGermRelationConverter;
    @DubboReference
    private SnowflakeService snowflakeService;

    /**
     * 菌属药物保存
     */
    @PostMapping("/save")
    public Object medicineGermSave(@RequestBody MedicineGermRelationSaveRequestVo vo) {
        if (Objects.isNull(vo.getGermGenusId())) {
            throw new IllegalStateException("菌属不确定");
        }
        if (CollectionUtils.isEmpty(vo.getMedicineItemList())) {
            throw new IllegalStateException("药物不可为空");
        }
        if (vo.getMedicineItemList().stream()
                .anyMatch(obj -> Objects.isNull(obj.getMedicineId()) || Objects.isNull(obj.getExamMethodCode())
                        || StringUtils.isBlank(obj.getExamMethodName()) || Objects.isNull(obj.getResistantWarn()))) {
            throw new IllegalStateException("药物、检测方法、耐药提醒都不可为空");
        }

        vo.getMedicineItemList().forEach(item -> {
            // 上下限都是数值是才校验上下限的大小是否正确
            if (StringUtils.isNotBlank(item.getReferValueMax()) && StringUtils.isNotBlank(item.getReferValueMin())
                    && NumberUtils.isParsable(item.getReferValueMin()) && NumberUtils.isParsable(item.getReferValueMax())
                    && (NumberUtils.toDouble(item.getReferValueMax()) < NumberUtils.toDouble(item.getReferValueMin()))) {
                throw new IllegalStateException(String.format("[%s] 参考范围上下限错误", item.getMedicineName()));
            }
            if (StringUtils.isNotBlank(item.getReferUnit())
                    && StringUtils.length(item.getReferUnit()) > INPUT_MAX_LENGTH) {
                throw new IllegalStateException(String.format("[%s] 单位过长", item.getMedicineName()));
            }
            if (!(Objects.equals(item.getResistantWarn(), YesOrNoEnum.NO.getCode())
                    || Objects.equals(item.getResistantWarn(), YesOrNoEnum.YES.getCode()))) {
                throw new IllegalArgumentException("耐药提醒参数错误");
            }
            if (StringUtils.isNotBlank(item.getReportSort()) && !StringUtils.isNumeric(item.getReportSort())) {
                throw new IllegalArgumentException("报告顺序只可填写整数");
            }
            if (StringUtils.isNotBlank(item.getFoldPointScope()) && item.getFoldPointScope().length() > 50) {
                throw new IllegalArgumentException("不能超过50个字（包含空格）");
            }
        });

        // 字典 code ->name
        Map<String, String> dictNameByDictCode = dictService.selectByDictType(DictEnum.MICROBIOLOGY_TEST_METHOD.name())
                .stream().collect(Collectors.toMap(DictItemDto::getDictCode, DictItemDto::getDictName));

        // 传入数据检查 自身
        vo.getMedicineItemList().forEach(item -> {
            if (vo.getMedicineItemList().stream().filter(other -> other != item)
                    .anyMatch(other -> other.getMedicineId().equals(item.getMedicineId())
                            && other.getExamMethodCode().equals(item.getExamMethodCode()))) {
                throw new IllegalStateException(String.format("[%s]已存在相同检测方法，请重新维护", item.getMedicineName()));
            }
            if (Objects.isNull(dictNameByDictCode.get(item.getExamMethodCode()))) {
                throw new IllegalStateException(String.format("无效检测方法:[%s]", item.getExamMethodName()));
            }
            if (vo.getMedicineItemList().stream().filter(other -> other != item)
                    .anyMatch(other -> StringUtils.isNotBlank(other.getReportSort())
                            && Objects.equals(other.getReportSort(), item.getReportSort()))) {
                throw new IllegalStateException(String.format("报告顺序 [%s]已存在，请重新维护", item.getReportSort()));
            }

        });
        GermGenusDto germGenusDto = germGenusService.selectByGermGenusId(vo.getGermGenusId());
        if (Objects.isNull(germGenusDto)) {
            throw new IllegalStateException("无效菌属");
        }
        // 菌属现有检测方法
        List<MedicineGermRelationDto> relationDtos =
                medicineGermRelationService.selectByGermGenusId(vo.getGermGenusId());
        Map<Long, MedicineGermRelationDto> relationDtoById = relationDtos.stream()
                .collect(Collectors.toMap(MedicineGermRelationDto::getRelationId, Function.identity()));
        Map<Long, String> medicineNameById = vo.getMedicineItemList().stream()
                .collect(Collectors.toMap(MedicineGermRelationSaveRequestVo.MedicineGermRelationItem::getMedicineId,
                        MedicineGermRelationSaveRequestVo.MedicineGermRelationItem::getMedicineName, (key1, key2) -> key1));

        //
        int size = (int) vo.getMedicineItemList().stream().filter(obj -> Objects.isNull(obj.getRelationId())).count();
        LinkedList<Long> genIds = new LinkedList<>();
        if (size > 0) {
            genIds = snowflakeService.genIds(size);
        }

        List<MedicineGermRelationDto> updateList = Lists.newArrayList();
        List<MedicineGermRelationDto> addList = Lists.newArrayList();
        for (MedicineGermRelationSaveRequestVo.MedicineGermRelationItem item : vo.getMedicineItemList()) {

            MedicineGermRelationDto temp = new MedicineGermRelationDto();
            temp.setReferValueMax(StringUtils.defaultString(item.getReferValueMax()));
            temp.setReferValueMin(StringUtils.defaultString(item.getReferValueMin()));
            temp.setReferUnit(StringUtils.defaultString(item.getReferUnit()));
            temp.setExamMethodCode(item.getExamMethodCode());
            temp.setExamMethodName(dictNameByDictCode.get(item.getExamMethodCode()));
            temp.setResistantWarn(item.getResistantWarn());
            temp.setReportSort(StringUtils.defaultString(item.getReportSort()));
            temp.setSusceptibility(StringUtils.defaultString(item.getSusceptibility()));
            temp.setFoldPointScope(StringUtils.defaultString(item.getFoldPointScope()));
            if (Objects.nonNull(item.getRelationId())) {
                // 更新项
                temp.setRelationId(item.getRelationId());
                temp.setMedicineId(item.getMedicineId());
                updateList.add(temp);
            } else {
                // 新增项
                temp.setRelationId(genIds.pop());
                temp.setGermGenusId(vo.getGermGenusId());
                temp.setMedicineId(item.getMedicineId());
                addList.add(temp);
            }
        }
        medicineGermRelationService.medicineGermSave(addList, updateList);
        addList.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.MEDICINE_GERM_RELATION.getDesc())
                            .setContent(String.format(" [%s] 菌属新增药物 [%s]", germGenusDto.getGermGenusName(),
                                    medicineNameById.getOrDefault(item.getMedicineId(), "")))
                            .toJSONString());
        });
        updateList.forEach(item -> {
            String compare =
                    new CompareUtils<MedicineGermRelationDto>().compare(relationDtoById.get(item.getRelationId()), item);
            if (StringUtils.isBlank(compare)) {
                return;
            }
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.MEDICINE_GERM_RELATION.getDesc())
                            .setContent(String.format(" 修改 [%s] 菌属下 [%s] 药物: [%s]", germGenusDto.getGermGenusName(),
                                    medicineNameById.getOrDefault(item.getMedicineId(), ""), compare))
                            .toJSONString());
        });
        return Collections.emptyMap();
    }

    /**
     * 菌属药物 删除
     */
    @PostMapping("/delete")
    public Object medicineGermDelete(@RequestBody Set<Long> relationIds) {
        if (CollectionUtils.isEmpty(relationIds)) {
            return Collections.emptyMap();
        }
        List<MedicineGermRelationDto> relationDtos = medicineGermRelationService.selectByRelationIds(relationIds);

        medicineGermRelationService.deleteByRelationIds(relationIds);

        Set<Long> germGenusIds =
                relationDtos.stream().map(MedicineGermRelationDto::getGermGenusId).collect(Collectors.toSet());
        Map<Long, String> germGenusNameById = germGenusService.selectByGermGenusIds(germGenusIds).stream()
                .collect(Collectors.toMap(GermGenusDto::getGermGenusId, GermGenusDto::getGermGenusName));

        Set<Long> medicineIdS =
                relationDtos.stream().map(MedicineGermRelationDto::getMedicineId).collect(Collectors.toSet());
        Map<Long, String> medicineNameById = medicineService.selectByMedicineIds(medicineIdS).stream()
                .collect(Collectors.toMap(MedicineDto::getMedicineId, MedicineDto::getMedicineName));

        relationDtos.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.MEDICINE_GERM_RELATION.getDesc())
                            .setContent(String.format(" [%s] 菌属删除药物 [%s]",
                                    germGenusNameById.getOrDefault(item.getGermGenusId(), StringUtils.EMPTY),
                                    medicineNameById.getOrDefault(item.getMedicineId(), "")))
                            .toJSONString());
        });
        return Collections.emptyMap();
    }

    /**
     * 菌属药物 查询
     */
    @PostMapping("/select-germ-genus-medicines")
    public Object selectGermGenusMedicines(@RequestParam("germGenusId") long germGenusId) {

        List<MedicineGermRelationDto> relationDtos = medicineGermRelationService.selectByGermGenusId(germGenusId);
        // 药物ids
        Set<Long> medicineIds =
                relationDtos.stream().map(MedicineGermRelationDto::getMedicineId).collect(Collectors.toSet());
        Map<Long, MedicineDto> medicineById = medicineService.selectByMedicineIds(medicineIds).stream()
                .collect(Collectors.toMap(MedicineDto::getMedicineId, Function.identity()));
        List<GermGenusMedicineResponseVo> targetList = Lists.newArrayListWithCapacity(relationDtos.size());
        relationDtos.forEach(item -> {
            GermGenusMedicineResponseVo temp =
                    medicineGermRelationConverter.germGenusMedicineResponseVoDtoTbObjDto(item);
            MedicineDto medicineDto = medicineById.get(temp.getMedicineId());
            if (Objects.nonNull(medicineDto)) {
                temp.setMedicineCode(medicineDto.getMedicineCode());
                temp.setMedicineName(medicineDto.getMedicineName());
            }
            targetList.add(temp);
        });
        return targetList;
    }

    /**
     * 菌属 下检测方法 查看
     */
    @PostMapping("/select-exam-method")
    public Object selectExamMethod(@RequestParam(value = "germGenusId", required = false) Long germGenusId) {
        // 菌属药物 以检测方法 分组
        Map<String, List<MedicineGermRelationDto>> groupingByExamMethodCode = new HashMap<>();
        if (Objects.nonNull(germGenusId)) {
            groupingByExamMethodCode = medicineGermRelationService.selectByGermGenusId(germGenusId).stream()
                    .filter(f -> Objects.equals(f.getExamMethodCode(), MicroTestMethodEnum.MANUAL.getCode()))
                    .filter(f -> Objects.equals(f.getExamMethodCode(), MicroTestMethodEnum.INSTRUMENT.getCode()))
                    .collect(Collectors.groupingBy(MedicineGermRelationDto::getExamMethodCode));
        }

        List<GermGenusExamMethodDto> targetList = Lists.newArrayList();
        GermGenusExamMethodDto d = new GermGenusExamMethodDto();
        d.setExamMethodCode(MicroTestMethodEnum.MANUAL.getCode());
        d.setExamMethodName(MicroTestMethodEnum.MANUAL.getDesc());
        GermGenusExamMethodDto d1 = new GermGenusExamMethodDto();
        d1.setExamMethodCode(MicroTestMethodEnum.INSTRUMENT.getCode());
        d1.setExamMethodName(MicroTestMethodEnum.INSTRUMENT.getDesc());
        targetList.add(d);
        targetList.add(d1);
        for (Map.Entry<String, List<MedicineGermRelationDto>> entry : groupingByExamMethodCode.entrySet()) {
            GermGenusExamMethodDto dict = new GermGenusExamMethodDto();
            String examMethodName = entry.getValue().stream()
                    .sorted(Comparator.comparing(MedicineGermRelationDto::getUpdateDate, Comparator.reverseOrder()))
                    .collect(Collectors.toList()).get(NumberUtils.INTEGER_ZERO).getExamMethodName();
            dict.setExamMethodCode(entry.getKey());
            dict.setExamMethodName(examMethodName);
            targetList.add(dict);
        }
        return targetList;
    }

    /**
     * 菌属 下药物 查看
     */
    @PostMapping("/select-medicine")
    public Object selectMedicine(@RequestParam(value = "germGenusId", required = false) Long germGenusId) {
        if (Objects.isNull(germGenusId)) {
            return Collections.emptyList();
        }
        // 菌属下所有药物
        Map<Long, List<MedicineGermRelationDto>> groupingByMedicineId =
                medicineGermRelationService.selectByGermGenusId(germGenusId).stream()
                        .collect(Collectors.groupingBy(MedicineGermRelationDto::getMedicineId));

        List<MedicineDto> medicineDtos = medicineService.selectByMedicineIds(groupingByMedicineId.keySet());
        List<SelectGenusMedicineResponseVo> targetList = Lists.newArrayListWithCapacity(medicineDtos.size());

        for (MedicineDto medicineDto : medicineDtos) {

            Map<String,
                    String> examMethodMap = groupingByMedicineId.get(medicineDto.getMedicineId()).stream()
                    .collect(Collectors.toMap(MedicineGermRelationDto::getExamMethodCode,
                            MedicineGermRelationDto::getExamMethodName, (key1, key2) -> key1));
            List<GermGenusExamMethodDto> examMethod = examMethodMap.entrySet().stream().map(obj -> {
                GermGenusExamMethodDto dto = new GermGenusExamMethodDto();
                dto.setExamMethodCode(obj.getKey());
                dto.setExamMethodName(obj.getValue());
                return dto;
            }).collect(Collectors.toList());

            SelectGenusMedicineResponseVo vo = new SelectGenusMedicineResponseVo();
            BeanUtils.copyProperties(medicineDto, vo);
            vo.setExamMethod(examMethod);
            targetList.add(vo);
        }

        return targetList;
    }

}
