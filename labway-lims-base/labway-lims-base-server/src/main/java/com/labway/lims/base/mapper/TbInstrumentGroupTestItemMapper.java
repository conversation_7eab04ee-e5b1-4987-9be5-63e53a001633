package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.InstrumentGroupTestItemDto;
import com.labway.lims.base.model.TbInstrumentGroupTestItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 仪器专业小组项目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbInstrumentGroupTestItemMapper extends BaseMapper<TbInstrumentGroupTestItem> {


    /**
     * 根据专业组ID查询
     */
    List<InstrumentGroupTestItemDto> selectByGroupIds(@Param("groupIds") Collection<Long> groupIds);
    List<InstrumentGroupTestItemDto> selectByGroupId(@Param("groupId") long groupId);

    int addBatch(@Param("instrumentGroupTestItems") List<TbInstrumentGroupTestItem> instrumentGroupTestItems);
}
