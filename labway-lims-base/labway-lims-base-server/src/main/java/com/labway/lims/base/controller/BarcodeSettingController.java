package com.labway.lims.base.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.BarcodeSettingDto;
import com.labway.lims.base.api.service.BarcodeSettingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/barcode-setting")
public class BarcodeSettingController extends BaseController {

    @Resource
    private BarcodeSettingService barcodeSettingService;


    @GetMapping("/selectAll")
    public Object selectAll() {
        return barcodeSettingService.selectAll();
    }

    /**
     * 添加条码规则
     */
    @PostMapping("/add")
    public Object addBarcodeSetting(@RequestBody BarcodeSettingDto dto) {
        dto.verifyAddParams();
        return barcodeSettingService.addBarcodeSetting(dto);
    }

    /**
     * 修改条码规则
     */
    @PostMapping("/update")
    public Object updateBarcodeSetting(@RequestBody BarcodeSettingDto dto) {
        dto.verifyUpdateParams();
        return barcodeSettingService.updateBarcodeSetting(dto);
    }

    /**
     * 获取条码号
     */
    @GetMapping("/gen-barcodes")
    public Object genBarcodes(@RequestParam("hspOrgCode") String hspOrgCode,
                              @RequestParam(value = "barcodeType", required = false) Integer barcodeType,
                              @RequestParam(value = "num", required = false, defaultValue = "1") String num) {
        final int i = getNum(hspOrgCode, num);
        if (!Objects.equals(barcodeType, BarcodeSettingDto.BARCODE_TYPE) && !Objects.equals(barcodeType, BarcodeSettingDto.MASTER_BARCODE_TYPE)) {
            throw new IllegalArgumentException("条码类型不能为空且 条码【1】  主条码【2】");
        }
        return barcodeSettingService.genBarcodes(hspOrgCode, i, barcodeType);
    }

    private int getNum(String hspOrgCode, String num) {
        if (StringUtils.isBlank(hspOrgCode)) {
            throw new IllegalArgumentException("送检机构code不能为空");
        }
        int i;
        try {
            i = Integer.parseInt(num);
            if (i < 0) {
                throw new NumberFormatException();
            }
        } catch (NumberFormatException exception) {
            throw new IllegalArgumentException("打印数量只能是正整数");
        }
        return i;
    }

    /**
     * 根据条码号反向获取条码配置
     *
     * @param barcode
     * @return
     */
    @GetMapping("/get-hspOrg")
    public Object selectHspOrg(String barcode) {
        if(StringUtils.isBlank(barcode)){
            throw new IllegalArgumentException("条码号不能为空");
        }
        return barcodeSettingService.selectByBarcode(barcode, BarcodeSettingDto.BARCODE_TYPE);
    }

}
