package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.InstrumentReportItemRemarkDto;
import com.labway.lims.base.model.TbInstrumentReportItemRemark;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仪器报告项目结果备注 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Mapper
public interface TbInstrumentReportItemRemarkMapper extends BaseMapper<TbInstrumentReportItemRemark> {

    /**
     * 根据专业小组查询
     */
    List<InstrumentReportItemRemarkDto> selectByInstrumentGroupId(@Param("instrumentGroupId") long instrumentGroupId);

    /**
     * 根据专业小组和报告项目code查询
     */
    List<InstrumentReportItemRemarkDto> selectByInstrumentGroupIdAndReportItemCode(@Param("instrumentGroupId") long instrumentGroupId,
                                                                                      @Param("reportItemCode") String reportItemCode);
}
