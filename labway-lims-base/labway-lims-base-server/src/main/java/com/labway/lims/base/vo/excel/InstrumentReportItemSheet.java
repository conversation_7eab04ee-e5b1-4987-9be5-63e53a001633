package com.labway.lims.base.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.InstrumentItemTypeEnum;
import com.labway.lims.api.enums.routine.TestResultTypeEnum;
import com.labway.lims.base.api.dto.InstrumentDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

/**
 * 仪器报告项目sheet
 */
@Getter
@Setter
public class InstrumentReportItemSheet implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer index;

    /**
     * 仪器编码
     */
    @ExcelProperty(index = 0)
    private String instrumentCode;

    /**
     * 报告项目编码
     */
    @ExcelProperty(index = 1)
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    @ExcelProperty(index = 2)
    private String reportItemName;

    /**
     * 单位名称
     */
    @ExcelProperty(index = 3)
    private String reportItemUnitName;

    /**
     * 英文名称
     */
    @ExcelProperty(index = 4)
    private String enName;

    /**
     * 英文缩写
     */
    @ExcelProperty(index = 5)
    private String enAb;

    /**
     * 检验方法名称
     */
    @ExcelProperty(index = 6)
    private String examMethodName;

    /**
     * 是否质控
     *
     * @see YesOrNoEnum
     */
    @ExcelProperty(index = 7)
    private String isQc;

    /**
     * 是否打印
     *
     * @see YesOrNoEnum
     */
    @ExcelProperty(index = 8)
    private String isPrint;

    /**
     * 打印顺序
     */
    @ExcelProperty(index = 9)
    private String printSort;

    /**
     * 是否结果为0
     *
     * @see YesOrNoEnum
     */
    @ExcelProperty(index = 10)
    private String isResultZero;

    /**
     * 检测结果类型名称
     */
    @ExcelProperty(index = 11)
    private String resultTypeName;

    /**
     * 检测结果是否为空
     *
     * @see YesOrNoEnum
     */
    @ExcelProperty(index = 12)
    private String isResultNull;

    /**
     * 结果小数点位数
     */
    @ExcelProperty(index = 13)
    private Integer decimalNums;

    /**
     * 项目类型名称
     */
    @ExcelProperty(index = 14)
    private String itemTypeName;

    /**
     * 仪器通道编码
     */
    @ExcelProperty(index = 15)
    private String instrumentChannel;

    /**
     * 是否手工录入
     *
     * @see YesOrNoEnum
     */
    @ExcelProperty(index = 16)
    private String isManualInput;

    /**
     * 是否自动带出报告项目
     *
     * @see YesOrNoEnum
     */
    @ExcelProperty(index = 17)
    private String isBringOut;




    // --------------- 额外字段 ---------------


    private TestResultTypeEnum testResultTypeEnum;

    private String reportItemUnit;

    private InstrumentDto instrument;
    private InstrumentItemTypeEnum itemTypeEnum;
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InstrumentReportItemSheet that = (InstrumentReportItemSheet) o;
        return Objects.equals(instrumentCode, that.instrumentCode) && Objects.equals(reportItemCode, that.reportItemCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(instrumentCode, reportItemCode);
    }
}
