package com.labway.lims.base.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.dto.GermDto;
import com.labway.lims.base.api.dto.GermGenusDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.api.service.GermGenusService;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.base.vo.GermImportHeadVo;
import com.labway.lims.base.vo.ImportErrorResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 导入 细菌 人员
 *
 * <AUTHOR>
 * @since 2023/3/30 17:55
 */
@Slf4j
public class ImportGermListener extends AnalysisEventListener<GermImportHeadVo> {

    private final GermService germService;

    private final GermGenusService germGenusService;
    private final DictService dictService;
    private final SnowflakeService snowflakeService;
    private final LoginUserHandler.User loginUser;

    public ImportGermListener(GermService germService, GermGenusService germGenusService, DictService dictService,
        SnowflakeService snowflakeService, LoginUserHandler.User loginUser) {
        this.germService = germService;
        this.germGenusService = germGenusService;
        this.dictService = dictService;
        this.snowflakeService = snowflakeService;
        this.loginUser = loginUser;
    }

    private final Map<Integer, GermImportHeadVo> excelDataMap = new HashMap<>();
    private final List<ImportErrorResponseVo> importErrorResponseVoList = new ArrayList<>();

    public List<ImportErrorResponseVo> getImportErrorResponseVoList() {
        return importErrorResponseVoList;
    }

    private final List<GermDto> addList = new ArrayList<>();
    private final List<GermDto> updateList = new ArrayList<>();

    public List<GermDto> getAddTargetList() {
        return addList;
    }

    public List<GermDto> getUpdateTargetList() {
        return updateList;
    }

    @Override
    public void invoke(GermImportHeadVo data, AnalysisContext analysisContext) {
        ReadRowHolder readRowHolder = analysisContext.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex() + 1;

        // 检查数据 格式
        String errorMessage = validateData(data);

        if (StringUtils.isNotBlank(errorMessage)) {
            importErrorResponseVoList
                .add(ImportErrorResponseVo.builder().rowNo(rowIndex).errorInfo(errorMessage).build());
        }

        excelDataMap.put(rowIndex, data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (CollectionUtils.isEmpty(excelDataMap.values())) {
            // 无 导入数据
            return;
        }
        if (CollectionUtils.isNotEmpty(importErrorResponseVoList)) {
            // 存在错误数据 相关 检查 失败
            return;
        }
        Set<String> germCodes =
            excelDataMap.values().stream().map(GermImportHeadVo::getGermCode).collect(Collectors.toSet());
        Map<String, GermDto> germDtoByGermCode = germService.selectByGermCodes(germCodes, loginUser.getOrgId()).stream()
            .collect(Collectors.toMap(GermDto::getGermCode, Function.identity()));

        Set<String> germGenusNames =
            excelDataMap.values().stream().map(GermImportHeadVo::getGermGenusName).collect(Collectors.toSet());
        Map<String, GermGenusDto> germGenusDtoByGermGenusName =
            germGenusService.selectByGermGenusNames(germGenusNames, loginUser.getOrgId()).stream()
                .collect(Collectors.toMap(GermGenusDto::getGermGenusName, Function.identity()));

        Set<String> whonetGermTypeCodes =
            excelDataMap.values().stream().filter(obj -> StringUtils.isNotBlank(obj.getWhonetGermTypeCode()))
                .map(GermImportHeadVo::getWhonetGermTypeCode).collect(Collectors.toSet());
        Map<String, DictItemDto> dictItemDtoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(whonetGermTypeCodes)) {
            for (String whonetGermTypeCode : whonetGermTypeCodes) {
                DictItemDto dictItemDto = dictService.selectByDictCode(whonetGermTypeCode);
                if (Objects.nonNull(dictItemDto)) {
                    dictItemDtoMap.put(whonetGermTypeCode, dictItemDto);
                }
            }
        }

        Date date = new Date();

        LinkedList<Long> genIds = snowflakeService.genIds(excelDataMap.values().size());
        for (Map.Entry<Integer, GermImportHeadVo> entry : excelDataMap.entrySet()) {
            GermImportHeadVo obj = entry.getValue();

            GermDto germDto = germDtoByGermCode.get(obj.getGermCode());
            GermGenusDto germGenusDto = germGenusDtoByGermGenusName.get(obj.getGermGenusName());
            DictItemDto dictItemDto = dictItemDtoMap.get(obj.getWhonetGermTypeCode());
            GermDto target = new GermDto();

            target.setGermCode(obj.getGermCode());
            target.setGermName(obj.getGermName());
            target.setGermEn(StringUtils.defaultString(obj.getGermEn()));
            if (Objects.nonNull(germGenusDto)) {
                target.setGermGenusId(germGenusDto.getGermGenusId());
                target.setGermGenusCode(germGenusDto.getGermGenusCode());
            }
            target.setGermTypeCode("0L");
            target.setGermTypeName(StringUtils.defaultString(obj.getGermTypeName()));
            if (Objects.nonNull(dictItemDto)) {
                target.setWhonetGermTypeCode(obj.getWhonetGermTypeCode());
                target.setWhonetGermTypeName(dictItemDto.getDictName());
            }
            target.setWhonetGermCode(StringUtils.defaultString(obj.getWhonetGermCode()));
            target.setOrgId(loginUser.getOrgId());
            target.setOrgName(loginUser.getOrgName());
            target.setEnableStatistics(obj.getEnableStatistics());
            target.setEnable(obj.getEnable());
            target.setCreateDate(date);
            target.setUpdateDate(date);
            target.setCreatorId(loginUser.getUserId());
            target.setCreatorName(loginUser.getNickname());
            target.setUpdaterId(loginUser.getUserId());
            target.setUpdaterName(loginUser.getNickname());
            target.setIsDelete(YesOrNoEnum.NO.getCode());
            if (Objects.nonNull(germDto)) {
                target.setGermId(germDto.getGermId());
                updateList.add(target);
            } else {
                target.setGermId(genIds.pop());
                addList.add(target);
            }
        }

    }

    /**
     * 检查 导入数据
     */
    private String validateData(GermImportHeadVo data) {
        StringBuilder errorMessage = new StringBuilder();

        if (StringUtils.isBlank(data.getGermCode())) {
            errorMessage.append("细菌编码不能为空;");
        }
        if (StringUtils.isBlank(data.getGermName())) {
            errorMessage.append("细菌名称不能为空;");
        }
        if (StringUtils.isBlank(data.getGermGenusName())) {
            errorMessage.append("细菌菌属名称不能为空;");
        }
        if (Objects.isNull(data.getEnableStatistics())) {
            errorMessage.append("是否统计不能为空;");
        }
        if (Objects.isNull(data.getEnable())) {
            errorMessage.append("是否启用不能为空;");
        }

        return errorMessage.toString();
    }

}
