package com.labway.lims.base.service;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.AddUserDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.UpdateUserDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.base.controller.UserController;
import com.labway.lims.base.mapper.TbUserGroupMapper;
import com.labway.lims.base.mapper.TbUserMapper;
import com.labway.lims.base.mapper.TbUserRoleMapper;
import com.labway.lims.base.mapstruct.UserConverter;
import com.labway.lims.base.model.TbUser;
import com.labway.lims.base.model.TbUserGroup;
import com.labway.lims.base.model.TbUserRole;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "user")
public class UserServiceImpl implements UserService {
    @Resource
    private TbUserMapper userMapper;
    @Resource
    private GroupService groupService;
    @Resource
    private TbUserRoleMapper userRoleMapper;
    @Resource
    private TbUserGroupMapper userGroupMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private UserConverter userConverter;

    @Override
    @Nullable
    @Cacheable(key = "'selectByUsername:' + #username")
    public UserDto selectByUsername(String username) {
        if (StringUtils.isBlank(username)) {
            return null;
        }

        return convert(userMapper.selectOne(new LambdaQueryWrapper<TbUser>()
                .eq(TbUser::getUsername, username).last("limit 1")));
    }

    @Override
    @Nullable
    @Cacheable(key = "'selectByUsernameAndOrgId:' + #username + #orgId")
    public UserDto selectByUsernameAndOrgId(String username, Long orgId) {
        if (StringUtils.isBlank(username)) {
            return null;
        }

        return convert(userMapper.selectOne(new LambdaQueryWrapper<TbUser>()
                .eq(TbUser::getUsername, username)
                .eq(TbUser::getOrgId, orgId).last("limit 1")));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByUserId:' + #userId")
    public UserDto selectByUserId(long userId) {
        if (userId < 1) {
            return null;
        }

        return convert(userMapper.selectById(userId));
    }

    /**
     * 根据分组id查询当分组id下的用户 和 没有分组id的用户nickname(启用和未启用的用户都要查出来)
     * @param groupId
     * @return
     */
    @Override
    public List<String> selectByGroupId(Long groupId,Long orgId) {
       return userMapper.selectByGroupId(groupId,orgId);
    }

    @Override
    @Cacheable(key = "'selectByRoleId:' + #roleId")
    public List<UserDto> selectByRoleId(long roleId) {
        return userMapper.selectByRoleId(roleId).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public long addUser(AddUserDto u) {

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 判断用户是否存在
        if (Objects.nonNull(selectByUsername(u.getUsername()))) {
            throw new IllegalArgumentException(String.format("用户名 [%s] 已存在", u.getUsername()));
        }

        // 添加用户
        final TbUser user = new TbUser();
        user.setUserId(snowflakeService.genId());
        user.setUsername(u.getUsername());
        user.setNickname(u.getNickname());

        final String firstLetter = CharSequenceUtil
                .upperFirst(StringUtils.lowerCase(PinyinUtil.getFirstLetter(user.getNickname(), StringUtils.EMPTY)));
        if (StringUtils.isBlank(firstLetter)) {
            throw new IllegalStateException("获取用户首字母拼音错误");
        }
        final String password = UserController.DEFAULT_PWD_PREFIX + firstLetter;
        user.setPassword(DigestUtils.md5Hex(u.getUsername() + password));
        user.setOrgId(loginUser.getOrgId());
        user.setOrgName(loginUser.getOrgName());
        user.setOrgCode(loginUser.getOrgCode());
        user.setStatus(u.getStatus());
        user.setCreateDate(new Date());
        user.setUpdateDate(new Date());
        user.setCreatorId(loginUser.getUserId());
        user.setCreatorName(loginUser.getNickname());
        user.setUpdaterId(loginUser.getUserId());
        user.setUpdaterName(loginUser.getNickname());
        user.setEnSign(u.getEnSign());
        user.setCnSign(u.getCnSign());
        user.setIsDelete(YesOrNoEnum.NO.getCode());
        user.setSex(u.getSex());

        if (userMapper.insert(user) < 1) {
            throw new IllegalStateException("添加用户失败");
        }

        log.info("用户 [{}] 新增用户成功 [{}] 密码 [{}]", loginUser.getNickname(), JSON.toJSONString(user), password);

        final LinkedList<Long> ids = snowflakeService.genIds(3000);

        // 添加角色关联
        if (CollectionUtils.isNotEmpty(u.getRoleIds())) {
            for (Long roleId : u.getRoleIds()) {
                final TbUserRole userRole = new TbUserRole();
                userRole.setUserRoleId(ids.pop());
                userRole.setUserId(user.getUserId());
                userRole.setRoleId(roleId);
                userRole.setIsDefault(Objects.equals(u.getRoleId(), roleId) ? 1 : 0);
                userRole.setOrgId(loginUser.getOrgId());
                userRole.setOrgName(loginUser.getOrgName());
                userRole.setCreateDate(new Date());
                userRole.setUpdateDate(new Date());
                userRole.setCreatorId(loginUser.getUserId());
                userRole.setCreatorName(loginUser.getNickname());
                userRole.setUpdaterId(loginUser.getUserId());
                userRole.setUpdaterName(loginUser.getNickname());
                userRole.setIsDelete(YesOrNoEnum.NO.getCode());

                if (userRoleMapper.insert(userRole) < 1) {
                    throw new IllegalStateException("添加角色关联失败");
                }

            }
        }

        // 添加专业组关联
        if (CollectionUtils.isNotEmpty(u.getGroupIds())) {
            for (Long groupId : u.getGroupIds()) {
                final TbUserGroup userGroup = new TbUserGroup();
                userGroup.setUserGroupId(ids.pop());
                userGroup.setUserId(user.getUserId());
                userGroup.setGroupId(groupId);
                userGroup.setIsDefault(Objects.equals(u.getGroupId(), groupId) ? 1 : 0);
                userGroup.setOrgId(loginUser.getOrgId());
                userGroup.setOrgName(loginUser.getOrgName());
                userGroup.setCreateDate(new Date());
                userGroup.setUpdateDate(new Date());
                userGroup.setCreatorId(loginUser.getUserId());
                userGroup.setCreatorName(loginUser.getNickname());
                userGroup.setUpdaterId(loginUser.getUserId());
                userGroup.setUpdaterName(loginUser.getNickname());
                userGroup.setIsDelete(YesOrNoEnum.NO.getCode());

                if (userGroupMapper.insert(userGroup) < 1) {
                    throw new IllegalStateException("添加专业组关联失败");
                }
            }
        }

        return user.getUserId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateUserByUserId(UserDto u) {

        if (Objects.isNull(u.getUserId())) {
            throw new IllegalStateException("参数错误");
        }

        final TbUser user = new TbUser();
        BeanUtils.copyProperties(u, user);

        user.setUpdaterId(LoginUserHandler.get().getUserId());
        user.setUpdaterName(LoginUserHandler.get().getNickname());
        user.setUpdateDate(new Date());

        if (userMapper.updateById(user) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改了用户 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(user));

        return true;
    }

    @Override
    @CacheEvict(allEntries = true)
    public void updateByUserId(UpdateUserDto u) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 判断用户是否存在
        if (Objects.isNull(selectByUserId(u.getUserId()))) {
            throw new IllegalArgumentException("用户不存在");
        }

        // 添加用户
        final TbUser user = new TbUser();
        BeanUtils.copyProperties(u, user);

        user.setUsername(null);
        user.setUpdaterId(loginUser.getUserId());
        user.setUpdaterName(loginUser.getUsername());
        user.setUpdateDate(new Date());

        if (userMapper.updateById(user) < 1) {
            throw new IllegalStateException("修改用户失败");
        }

        log.info("用户 [{}] 修改用户成功 [{}]", loginUser.getNickname(), JSON.toJSONString(user));

        userRoleMapper.delete(new LambdaQueryWrapper<TbUserRole>().eq(TbUserRole::getUserId, user.getUserId()));

        final LinkedList<Long> ids = snowflakeService.genIds(3000);

        // 添加角色关联
        if (CollectionUtils.isNotEmpty(u.getRoleIds())) {
            for (Long roleId : u.getRoleIds()) {
                final TbUserRole userRole = new TbUserRole();
                userRole.setUserRoleId(ids.pop());
                userRole.setUserId(user.getUserId());
                userRole.setRoleId(roleId);
                userRole.setIsDefault(Objects.equals(u.getRoleId(), roleId) ? 1 : 0);
                userRole.setOrgId(loginUser.getOrgId());
                userRole.setOrgName(loginUser.getOrgName());
                userRole.setCreateDate(new Date());
                userRole.setUpdateDate(new Date());
                userRole.setCreatorId(loginUser.getUserId());
                userRole.setCreatorName(loginUser.getNickname());
                userRole.setUpdaterId(loginUser.getUserId());
                userRole.setUpdaterName(loginUser.getNickname());
                userRole.setIsDelete(YesOrNoEnum.NO.getCode());

                if (userRoleMapper.insert(userRole) < 1) {
                    throw new IllegalStateException("添加角色关联失败");
                }
            }
        }

        userGroupMapper.delete(new LambdaQueryWrapper<TbUserGroup>().eq(TbUserGroup::getUserId, user.getUserId()));

        // 添加专业组关联
        if (CollectionUtils.isNotEmpty(u.getGroupIds())) {
            for (Long groupId : u.getGroupIds()) {
                final TbUserGroup userGroup = new TbUserGroup();
                userGroup.setUserGroupId(ids.pop());
                userGroup.setUserId(user.getUserId());
                userGroup.setGroupId(groupId);
                userGroup.setIsDefault(Objects.equals(u.getGroupId(), groupId) ? 1 : 0);
                userGroup.setOrgId(loginUser.getOrgId());
                userGroup.setOrgName(loginUser.getOrgName());
                userGroup.setCreateDate(new Date());
                userGroup.setUpdateDate(new Date());
                userGroup.setCreatorId(loginUser.getUserId());
                userGroup.setCreatorName(loginUser.getNickname());
                userGroup.setUpdaterId(loginUser.getUserId());
                userGroup.setUpdaterName(loginUser.getNickname());
                userGroup.setIsDelete(YesOrNoEnum.NO.getCode());

                if (userGroupMapper.insert(userGroup) < 1) {
                    throw new IllegalStateException("添加专业组关联失败");
                }
            }
        }
    }

    @Override
    @Cacheable(key = "'selectByOrgId:' + #orgId")
    public List<UserDto> selectByOrgId(long orgId) {
        return userMapper
                .selectList(new LambdaQueryWrapper<TbUser>().eq(TbUser::getOrgId, orgId).orderByDesc(TbUser::getUserId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public boolean containsGroup(long userId, long groupId) {

        final List<ProfessionalGroupDto> groups = groupService.selectByUserId(userId);
        if (CollectionUtils.isEmpty(groups)) {
            return true;
        }

        return groups.stream().anyMatch(e -> Objects.equals(groupId, e.getGroupId()));
    }

    @Override
    public boolean validPassword(String username, String hash, String password) {
        return DigestUtils.md5Hex(username + password).equalsIgnoreCase(hash);
    }

    @Override
    @Cacheable(key = "'selectByGroupIdAndOrgId:' + #groupId")
    public List<UserDto> selectByGroupIdAndOrgId(long groupId, long orgId) {
        return userConverter.userDtoListFromTbObj(userMapper.selectByGroupIdAndOrgId(groupId, orgId));
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByNicknames(Collection<String> nicknames) {
        if (CollectionUtils.isEmpty(nicknames)) {
            return;
        }
        // 根据用户名失效员工
        LambdaUpdateWrapper<TbUser> updateWrapperUser = Wrappers.lambdaUpdate();
        updateWrapperUser.set(TbUser::getIsDelete, YesOrNoEnum.YES.getCode());
        updateWrapperUser.in(TbUser::getNickname, nicknames);
        userMapper.update(null, updateWrapperUser);
    }

    private UserDto convert(TbUser user) {
        if (Objects.isNull(user)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(user), UserDto.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public long addOutUser(AddUserDto u) {


        // 判断用户是否存在
        if (Objects.nonNull(selectByUsername(u.getUsername()))) {
            throw new IllegalArgumentException(String.format("用户名 [%s] 已存在", u.getUsername()));
        }

        // 添加用户
        final TbUser user = new TbUser();
        user.setUserId(snowflakeService.genId());
        user.setUsername(u.getUsername());
        user.setNickname(u.getNickname());

        final String firstLetter = CharSequenceUtil
                .upperFirst(StringUtils.lowerCase(PinyinUtil.getFirstLetter(user.getNickname(), StringUtils.EMPTY)));
        if (StringUtils.isBlank(firstLetter)) {
            throw new IllegalStateException("获取用户首字母拼音错误");
        }
        final String password = UserController.DEFAULT_PWD_PREFIX + firstLetter;
        user.setPassword(DigestUtils.md5Hex(u.getUsername() + password));
        user.setOrgId(u.getOrgId());
        user.setOrgName(u.getOrgName());
        user.setOrgCode(u.getOrgCode());
        user.setStatus(u.getStatus());
        user.setCreateDate(new Date());
        user.setUpdateDate(new Date());
        user.setCreatorId(-1L);
        user.setCreatorName("system");
        user.setUpdaterId(-1L);
        user.setUpdaterName("system");
        user.setEnSign(u.getEnSign());
        user.setCnSign(u.getCnSign());
        user.setIsDelete(YesOrNoEnum.NO.getCode());
        user.setSex(u.getSex());

        if (userMapper.insert(user) < 1) {
            throw new IllegalStateException("添加用户失败");
        }

        log.info("用户 [{}] 新增用户成功 [{}] 密码 [{}]", u.getNickname(), JSON.toJSONString(user), password);

        final LinkedList<Long> ids = snowflakeService.genIds(3000);

        return user.getUserId();
    }

    @Override
    public UserDto checkUser(String username, String password) {
        if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
            throw new IllegalArgumentException("账号密码不能为空");
        }
        final UserServiceImpl userService = (UserServiceImpl) AopContext.currentProxy();

        final UserDto user = userService.selectByUsername(username);
        if (Objects.isNull(user)) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        // 校验密码
        if (!userService.validPassword(user.getUsername(), user.getPassword(), password)) {
            throw new IllegalArgumentException("工号或密码错误");
        }
        return user;
    }


}
