package com.labway.lims.base.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 细菌导入 head
 *
 * <AUTHOR>
 * @since 2023/7/4 20:13
 */
@Getter
@Setter
public class GermImportHeadVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 细菌编码
     */
    @ExcelProperty(value = "细菌编码", index = 0)
    private String germCode;
    /**
     * 细菌名称
     */
    @ExcelProperty(value = "细菌名称", index = 1)
    private String germName;
    /**
     * 细菌英文名
     */
    @ExcelProperty(value = "细菌英文名称", index = 2)
    private String germEn;
    /**
     * 细菌菌属编码
     */
    @ExcelProperty(value = "细菌菌属名称", index = 3)
    private String germGenusName;
    /**
     * 细菌类别名称
     */
    @ExcelProperty(value = "细菌分类", index = 4)
    private String germTypeName;
    /**
     * whonet细菌类型编码
     */
    @ExcelProperty(value = "WHONET细菌类型", index = 5)
    private String whonetGermTypeCode;
    /**
     * whonet细菌类型编码
     */
    @ExcelProperty(value = "WHONET细菌编码", index = 6)
    private String whonetGermCode;

    /**
     * 是否开启统计(0未开启 1开启)
     */
    @ExcelProperty(value = "是否统计", index = 7)
    private Integer enableStatistics;

    /**
     * 是否启用(0未启用 1启用)
     *
     * @see YesOrNoEnum
     */
    @ExcelProperty(value = "是否启用", index = 8)
    private Integer enable;

}
