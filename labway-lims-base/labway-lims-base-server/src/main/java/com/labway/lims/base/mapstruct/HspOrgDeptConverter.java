
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.HspOrgDeptDto;
import com.labway.lims.base.model.TbHspOrgDept;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 送检机构科室 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface HspOrgDeptConverter {

    HspOrgDeptDto hspOrgDeptDtoTbObj(TbHspOrgDept obj);

    List<HspOrgDeptDto> hspOrgDeptDtoListFromTbObj(List<TbHspOrgDept> list);

}
