package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.finance.FinanceLockEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.base.api.dto.FinanceOrgLockDto;
import com.labway.lims.base.api.dto.FinanceSampleLockDto;
import com.labway.lims.base.api.dto.OrgLockDto;
import com.labway.lims.base.api.service.FinanceOrgLockService;
import com.labway.lims.base.api.service.FinanceSampleLockService;
import com.labway.lims.base.mapper.TbFinanceSampleLockMapper;
import com.labway.lims.base.model.TbFinanceSampleLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/12 14:07
 */
@Slf4j
@DubboService
public class FinanceSampleLockServiceImpl implements FinanceSampleLockService {
    @Resource
    private TbFinanceSampleLockMapper tbFinanceSampleLockMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    private FinanceOrgLockService financeOrgLockService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public List<FinanceSampleLockDto> selectByApplySampleId(long applySampleId) {
        final LambdaQueryWrapper<TbFinanceSampleLock> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbFinanceSampleLock::getApplySampleId, applySampleId)
                .orderByDesc(TbFinanceSampleLock::getSampleLockRecordId);

        return tbFinanceSampleLockMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void addBatch(Collection<FinanceSampleLockDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        final LoginUserHandler.User user = LoginUserHandler.get();
        final LinkedList<Long> ids = snowflakeService.genIds(dtos.size());

        final List<TbFinanceSampleLock> locks = JSON.parseArray(JSON.toJSONString(dtos), TbFinanceSampleLock.class);

        for (TbFinanceSampleLock lock : locks) {
            lock.setSampleLockRecordId(ids.pop());
            lock.setCreateDate(new Date());
            lock.setCreatorName(user.getNickname());
            lock.setCreatorId(user.getUserId());
            lock.setUpdateDate(new Date());
            lock.setUpdateId(user.getUserId());
            lock.setUpdateName(user.getNickname());
            lock.setIsDelete(YesOrNoEnum.NO.getCode());
        }

        tbFinanceSampleLockMapper.addBatch(locks);

    }

    @Override
    public boolean updateByApplySampleId(FinanceSampleLockDto dto) {

        final LambdaUpdateWrapper<TbFinanceSampleLock> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TbFinanceSampleLock::getApplySampleId, dto.getApplySampleId())
                .set(TbFinanceSampleLock::getUpdateId, LoginUserHandler.get().getUserId())
                .set(TbFinanceSampleLock::getUpdateDate, new Date())
                .set(TbFinanceSampleLock::getUpdateName, LoginUserHandler.get().getNickname())
                .set(TbFinanceSampleLock::getStatus, dto.getStatus());

        if (tbFinanceSampleLockMapper.update(null, updateWrapper) < 1) {
            return false;
        }
        log.info("用户 [{}] 修改样本加锁数据成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(dto));
        return true;
    }

    @Override
    public Map<Long, List<FinanceSampleLockDto>> selectByApplySampleIdsAsMap(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyMap();
        }
        final LambdaQueryWrapper<TbFinanceSampleLock> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbFinanceSampleLock::getApplySampleId, applySampleIds)
                .orderByDesc(TbFinanceSampleLock::getCreateDate);
        final List<FinanceSampleLockDto> locks = tbFinanceSampleLockMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
        return locks.stream().collect(Collectors.groupingBy(FinanceSampleLockDto::getApplySampleId));
    }

    @Override
    public String getSampleLockKey(long applySampleId) {
        return redisPrefix.getBasePrefix() + "SAMPLE_LOCK:" + applySampleId;
    }

    @Override
    public boolean isSampleLock(long applySampleId) {
        //先判断机构加锁
        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        query.setIsAudit(YesOrNoEnum.YES.getCode());
        query.setApplySampleIds(Collections.singleton(applySampleId));
        final List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(query);
        //样本不存在直接返回 false
        if (CollectionUtils.isEmpty(samples)) {
            return false;
        }

        BaseSampleEsModelDto sample = samples.iterator().next();

        //添加所有机构的记录
        final List<FinanceOrgLockDto> orgLocks = financeOrgLockService.selectByHspOrgIds(List.of(sample.getHspOrgId(), NumberUtils.LONG_ZERO));

        //在查询该样本送检机构的记录是否加解锁
        if (CollectionUtils.isEmpty(orgLocks)) {
            return false;
        }

        final Set<String> orgLockKeys = new HashSet<>();
        for (FinanceOrgLockDto lock : orgLocks) {
            orgLockKeys.add(financeOrgLockService.getOrgLockKey(lock.getHspOrgId(),
                    lock.getStartDate().getTime(), lock.getEndDate().getTime()));
        }

        final List<String> lockValues = stringRedisTemplate.opsForValue().multiGet(orgLockKeys);
        //如果存在一条记录的开始结束时间在范围内那么表示是机构加锁
        if (Objects.nonNull(lockValues)) {
            for (String v : lockValues) {
                final OrgLockDto ol = JSON.parseObject(StringUtils.defaultString(v, "{}"), OrgLockDto.class);
                if (Objects.isNull(sample.getFinalCheckDate())) {
                    return false;
                }
                if (sample.getFinalCheckDate().getTime() < ol.getEndTime()
                        && sample.getFinalCheckDate().getTime() > ol.getStartTime()) {
                    return true;
                }
            }
        }

        //判断redis 标记是否进行样本加解锁
        final String sampleLock = stringRedisTemplate.opsForValue().get(getSampleLockKey(applySampleId));
        if (Objects.nonNull(sampleLock)) {
            if (Objects.equals(sampleLock, FinanceLockEnum.LOCK.name())) {
                return true;
            } else if (Objects.equals(sampleLock, FinanceLockEnum.UN_LOCK.name())) {
                return false;
            }
        }

        return false;
    }


    private FinanceSampleLockDto convert(TbFinanceSampleLock lock) {
        if (Objects.isNull(lock)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(lock), FinanceSampleLockDto.class);
    }
}
