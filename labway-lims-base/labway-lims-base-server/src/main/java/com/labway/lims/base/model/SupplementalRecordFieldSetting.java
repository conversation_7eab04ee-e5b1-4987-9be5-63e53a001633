package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * @description 补录字段设置
 * <AUTHOR>
 * @date 2024-05-24
 */
@Data
@TableName(value = "tb_supplemental_record_field_setting")
public class SupplementalRecordFieldSetting implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 补录字段设置id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long supplementalRecordFieldSettingId;

    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
    * 送检机构code
    */
    private String hspOrgCode;

    /**
    * 送检机构名称
    */
    private String hspOrgName;

    /**
    * 对照字段
    */
    private String supplementalRecordField;

    /**
    * 更新时间
    */
    private LocalDateTime updateDate;

    /**
    * 创建时间
    */
    private LocalDateTime createDate;

    /**
    * 更新人ID
    */
    private Long updaterId;

    /**
    * 更新人名称
    */
    private String updaterName;

    /**
    * 创建人ID
    */
    private Long creatorId;

    /**
    * 创建人名称
    */
    private String creatorName;

    /**
    * 1删除。0:未删除
    */
    private Integer isDelete;

}