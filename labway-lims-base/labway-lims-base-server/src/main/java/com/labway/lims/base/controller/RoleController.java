package com.labway.lims.base.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.MenuTypeCodeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.RoleDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.dto.UserProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.MenuService;
import com.labway.lims.base.api.service.RoleService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.base.vo.AddRoleVo;
import com.labway.lims.base.vo.RoleUsersVo;
import com.labway.lims.base.vo.UpdateRoleVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色相关
 */
@RequestMapping("/role")
@RestController
class RoleController extends BaseController {
    @DubboReference
    private RoleService roleService;
    @DubboReference
    private UserService userService;
    @Resource
    private GroupService groupService;
    @DubboReference
    private MenuService menuService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 新增角色
     */
    @PostMapping("/add")
    public Object add(@RequestBody AddRoleVo vo) {
        if (StringUtils.isBlank(vo.getRoleName()) || Objects.isNull(vo.getStatus())) {
            throw new IllegalArgumentException("参数错误");
        }

        vo.setRoleDesc(StringUtils.defaultString(vo.getRoleDesc()));

        if (vo.getRoleName().length() > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("角色名称过长");
        }

        if (vo.getRoleDesc().length() > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("角色描述过长");
        }

        final RoleDto role = new RoleDto();
        role.setRoleName(vo.getRoleName());
        role.setRoleDesc(vo.getRoleDesc());
        role.setStatus(vo.getStatus());
        role.setOrgName(LoginUserHandler.get().getOrgName());
        role.setOrgId(LoginUserHandler.get().getOrgId());
        role.setCreateDate(new Date());
        role.setUpdateDate(new Date());
        role.setUpdaterId(LoginUserHandler.get().getUserId());
        role.setUpdaterName(LoginUserHandler.get().getNickname());
        role.setCreatorId(LoginUserHandler.get().getUserId());
        role.setCreatorName(LoginUserHandler.get().getNickname());

        return Map.of("id", roleService.addRole(role));
    }


    /**
     * 修改角色
     */
    @PostMapping("/update")
    public Object update(@RequestBody UpdateRoleVo vo) {
        if (StringUtils.isBlank(vo.getRoleName())
                || Objects.isNull(vo.getStatus())
                || Objects.isNull(vo.getRoleId())) {
            throw new IllegalArgumentException("参数错误");
        }

        vo.setRoleDesc(StringUtils.defaultString(vo.getRoleDesc()));

        if (vo.getRoleName().length() > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("角色名称过长");
        }

        if (vo.getRoleDesc().length() > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("角色描述过长");
        }

        final RoleDto role = new RoleDto();
        role.setRoleId(vo.getRoleId());
        role.setRoleName(vo.getRoleName());
        role.setRoleDesc(vo.getRoleDesc());
        role.setStatus(vo.getStatus());
        role.setUpdateDate(new Date());
        role.setUpdaterId(LoginUserHandler.get().getUserId());
        role.setUpdaterName(LoginUserHandler.get().getNickname());

        if (roleService.updateById(role)) {
            return Collections.emptyMap();
        }

        throw new IllegalStateException("修改角色失败");
    }


    /**
     * 获取角色下的用户
     */
    @GetMapping("/users")
    public List<RoleUsersVo> users(@RequestParam Long roleId) {
        if (Objects.isNull(roleId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<UserDto> users = userService.selectByRoleId(roleId);
        if (CollectionUtils.isEmpty(users)) {
            return Collections.emptyList();
        }


        final var groups = groupService.selectUserGroupByUserIds(users.stream().map(UserDto::getUserId).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(UserProfessionalGroupDto::getUserId));

        return users.stream().map(e -> {
            final RoleUsersVo vo = new RoleUsersVo();
            BeanUtils.copyProperties(e, vo);
            vo.setDefaultGroupName(groups.getOrDefault(e.getUserId(), Collections.emptyList())
                    .stream().filter(l -> Objects.equals(l.getIsDefault(), YesOrNoEnum.YES.getCode())).findFirst().map(ProfessionalGroupDto::getGroupName)
                    .orElse(StringUtils.EMPTY));
            return vo;
        }).collect(Collectors.toList());
    }


    /**
     * 角色列表
     */
    @GetMapping("/roles")
    public Object roles() {
        return roleService.selectByOrgId(LoginUserHandler.get().getOrgId());
    }


    /**
     * 获取角色下的菜单
     */
    @GetMapping("/menus")
    public Object menus(@RequestParam Long roleId) {
        return menuService.selectByRoleId(roleId);
    }


    /**
     * 修改角色下的菜单
     */
    @PostMapping("/permission")
    public Object permission(@RequestParam long roleId, @RequestParam String type, @RequestBody Set<Long> menuIds) {
        final MenuTypeCodeEnum m = EnumUtils.getEnum(MenuTypeCodeEnum.class, type);
        if (m == MenuTypeCodeEnum.MENU) {
            roleService.updateMenusByRoleId(roleId, menuIds);
        } else if (m == MenuTypeCodeEnum.BUTTON) {
            roleService.updateButtonsByRoleId(roleId, menuIds);
        } else {
            throw new IllegalArgumentException("参数错误");
        }
        return Collections.emptyMap();
    }

    /**
     * 根据 id 删除
     */
    @PostMapping("/delete")
    public Object delete(@RequestBody Set<Long> roleIds) {

        for (Long roleId : roleIds) {
            if (Objects.isNull(roleId)) {
                throw new IllegalArgumentException("参数错误");
            }
        }

        roleService.deleteByRoleIds(roleIds);
        return Collections.emptyMap();
    }


}
