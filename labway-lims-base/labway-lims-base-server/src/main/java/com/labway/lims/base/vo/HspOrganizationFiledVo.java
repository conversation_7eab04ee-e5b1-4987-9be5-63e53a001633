package com.labway.lims.base.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

/**
 * 双输内容对照表
 */
@Getter
@Setter
public class HspOrganizationFiledVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 对照内容id
     */
    private Long filedId;

    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 字段列表
     */
    private Set<Filed> fileds;

    @AllArgsConstructor
    @Getter
    @Setter
    @NoArgsConstructor
    public static final class Filed implements Serializable{

        private static final long serialVersionUID = 1L;

        /**
         * 字段code
         */
        private String code;

        /**
         * 字段名称
         */
        private String name;

        /**
         * 字段顺序
         */
        private Integer sort;

        public Filed(String code, String name) {
            this.code = code;
            this.name = name;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            final Filed filed = (Filed) o;
            return Objects.equals(code, filed.code);
        }

        @Override
        public int hashCode() {
            return Objects.hash(code);
        }
    }
}
