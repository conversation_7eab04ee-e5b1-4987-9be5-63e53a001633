package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 仪器细菌
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */

@Getter
@Setter
@TableName("tb_instrument_germ")
public class TbInstrumentGerm implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器细菌id
     */
    @TableId
    private Long instrumentGermId;

    /**
     * 仪器id
     */
    private Long instrumentId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 细菌id
     */
    private Long germId;

    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 仪器通道号
     */
    private String instrumentChannel;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

}
