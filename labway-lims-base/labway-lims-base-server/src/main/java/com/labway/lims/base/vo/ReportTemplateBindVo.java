package com.labway.lims.base.vo;

import com.labway.lims.api.enums.base.ReportTemplateBindTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/6/15 14:28
 */
@Getter
@Setter
public class ReportTemplateBindVo {

    /**
     * id
     */
    private Long reportTemplateBindId;

    /**
     * 模板名称
     */
    private String reportTemplateName;

    /**
     * 模板文件
     */
    private String templateFile;

    /**
     * 报告模板编码
     */
    private String reportTemplateCode;

    /**
     * 专业小组名称
     */
    private String instrumentGroupNames;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 是否启用，1是，0不是
     */
    private Integer enable;

    /**
     * 送检机构
     */
    private String hspOrgNames;

    /**
     * 检验项目
     */
    private String testItemNames;

    /**
     * 绑定类型,1:专业小组,2:送检机构,3:检验项目
     *
     * @see ReportTemplateBindTypeEnum
     */
    private ReportTemplateBindTypeEnum bindType;

    /**
     * 送检机构ID集合
     */
    private String hspOrgIds;

    /**
     * 专业小组ID集合
     */
    private String instrumentGroupIds;

    /**
     * 检验项目ID集合
     */
    private String testItemIds;
    /**
     * 绑定组ID
     */
    private Long bindGroupId;


}
