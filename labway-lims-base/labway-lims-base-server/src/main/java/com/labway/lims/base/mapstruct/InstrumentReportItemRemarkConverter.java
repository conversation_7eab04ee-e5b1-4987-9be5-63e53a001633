package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.InstrumentReportItemRemarkDto;
import com.labway.lims.base.model.TbInstrumentReportItemRemark;
import org.mapstruct.Mapper;

/**
 * <p>
 * InstrumentReportItemRemarkConverter
 * 仪器报告项目结果备注 转换
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Mapper(componentModel = "spring")
public interface InstrumentReportItemRemarkConverter {

    TbInstrumentReportItemRemark convertDto2Entity(InstrumentReportItemRemarkDto itemReferenceDto);

}
