package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 菜单模块
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class MenuVo implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 菜单ID
     */
    private Long menuId;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单Code
     */
    private String menuCode;

    /**
     * 父菜单ID
     */
    private Long proMenuId;

    /**
     * 菜单类型编码
     */
    private String menuTypeCode;

    /**
     * 菜单描述
     */
    private String menuDesc;

    /**
     * 菜单排序
     */
    private Integer menuSort;

    /**
     * 菜单icon
     */
    private String menuIcon;

    /**
     * 菜单地址
     */
    private String menuUrl;

    /**
     * 是否启用(0未启用 1启用)
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
