package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ReportTemplateBindTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 报告单模板绑定
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
@Getter
@Setter
@TableName("tb_report_template_bind")
public class TbReportTemplateBind implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Long reportTemplateBindId;

    /**
     * 绑定类型,1:专业小组,2:送检机构,3:检验项目
     * 
     * @see ReportTemplateBindTypeEnum
     */
    private Integer bindType;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;
    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 业务ID,根据 bind_type 存不同的ID
     */
    private Long bizId;

    /**
     * 报告模板名称
     */
    private String reportTemplateName;

    /**
     * 报告模板编码
     */
    private String reportTemplateCode;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 新建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 是否启用，1是，0不是
     * 
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 是否组合，1:是，0:不是
     *
     * @see YesOrNoEnum
     */
    private Integer isCombine;

    /**
     * 是否删除，1:是，0:不是
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 绑定组ID
     */
    private Long bindGroupId;
}
