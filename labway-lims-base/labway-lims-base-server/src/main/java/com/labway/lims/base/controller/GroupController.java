package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.base.vo.ProfessionalGroupVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 专业组
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/group")
public class GroupController extends BaseController {

    @Resource
    private GroupService groupService;
    @DubboReference
    private UserService userService;

    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 删除专业组
     */
    @PostMapping("/deleteGroup")
    public Object deleteGroup(@RequestBody List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            throw new IllegalArgumentException("请选择需要删除的专业组");
        }

        final List<ProfessionalGroupDto> professionalGroups = groupService.selectByGroupIds(groupIds);
        if (CollectionUtils.isEmpty(professionalGroups)) {
            throw new IllegalArgumentException("专业组不存在");
        }

        groupService.deleteByGroupIds(groupIds);


        // 记录操作日志
        final List<String> groupNames = professionalGroups.stream().map(ProfessionalGroupDto::getGroupName).collect(Collectors.toList());
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.PROFESSIONAL_GROUP.getDesc()).setContent(String.format("用户 [%s] 删除专业组 %s",
                        LoginUserHandler.get().getNickname(), groupNames)).toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 查询机构所有的专业组
     */
    @GetMapping("/groups")
    public Object groups() {
        final Long orgId = LoginUserHandler.get().getOrgId();
        List<ProfessionalGroupDto> professionalGroups = groupService.selectByOrgId(orgId);
        return JSON.parseArray(JSON.toJSONString(professionalGroups), ProfessionalGroupVo.class);
    }

    /**
     * 新增专业组
     */
    @PostMapping("/updateGroup")
    public Object updateGroup(@RequestBody ProfessionalGroupVo vo) {
        final Long groupId = vo.getGroupId();
        if (Objects.isNull(groupId)) {
            throw new IllegalArgumentException("专业组ID为空");
        }

        final String groupName = vo.getGroupName();
        if (StringUtils.isBlank(groupName) || StringUtils.length(groupName) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("专业组名称为空或大于 %s 字符", INPUT_MAX_LENGTH));
        }

        if (StringUtils.isAnyBlank(vo.getGroupTypeName(), vo.getGroupTypeCode())) {
            throw new IllegalArgumentException("请选择专业组类型");
        }

        // 去空格 然后校验字符长度
        if (StringUtils.length(StringUtils.trim(vo.getApproverName())) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("批准者姓名不能大于 %s 个字符", INPUT_MAX_LENGTH));
        }

        final ProfessionalGroupDto groupOld = groupService.selectByGroupId(groupId);
        if (Objects.isNull(groupOld)) {
            throw new IllegalArgumentException("专业组不存在");
        }

        final ProfessionalGroupDto group = JSON.parseObject(JSON.toJSONString(vo), ProfessionalGroupDto.class);
        groupService.updateByGroupId(group);

        String compare = new CompareUtils<ProfessionalGroupDto>().compare(groupOld, group);
        if (StringUtils.isNotBlank(compare)) {
            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.PROFESSIONAL_GROUP.getDesc())
                            .setContent(String.format("用户修改了专业组 [%s]", compare)).toJSONString());
        }

        return Collections.emptyMap();
    }

    /**
     * 新增专业组
     */
    @PostMapping("/addGroup")
    public Object addGroup(@RequestBody ProfessionalGroupVo vo) {
        final String groupCode = vo.getGroupCode();
        if (StringUtils.isBlank(groupCode) || StringUtils.length(groupCode) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("专业组编码为空或大于 %s 字符", INPUT_MAX_LENGTH));
        }

        final String groupName = vo.getGroupName();
        if (StringUtils.isBlank(groupName) || StringUtils.length(groupName) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("专业组名称为空或大于 %s 字符", INPUT_MAX_LENGTH));
        }

        if (StringUtils.isAnyBlank(vo.getGroupTypeName(), vo.getGroupTypeCode())) {
            throw new IllegalArgumentException("请选择专业组类型");
        }

        // 去空格 然后校验字符长度
        if (StringUtils.length(StringUtils.trim(vo.getApproverName())) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("批准者姓名不能大于 %s 个字符", INPUT_MAX_LENGTH));
        }

        // 默认已启用
        vo.setEnable(ObjectUtils.defaultIfNull(vo.getEnable(), YesOrNoEnum.YES.getCode()));

        final ProfessionalGroupDto group = JSON.parseObject(JSON.toJSONString(vo), ProfessionalGroupDto.class);

        group.setOrgId(LoginUserHandler.get().getOrgId());

        final Map<String, Long> result = Map.of("id", groupService.addGroup(group));

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.PROFESSIONAL_GROUP.getDesc())
                        .setContent(String.format("用户新增了专业组 [%s] 编码 [%s] 专业组类型 [%s] 批准者姓名 [%s] 批准者签名 [%s] 是否启用 [%s]",
                                group.getGroupName(),
                                group.getGroupName(),
                                group.getGroupTypeName(),
                                group.getApproverName(),
                                group.getApproverSign(),
                                BooleanUtils.toString(BooleanUtils.toBoolean(group.getEnable()), "是", "否"))).toJSONString());

        return result;
    }

    /**
     * 获取专业组下的用户 包含没有任何专业组关联的用户(这种用户说明具有所有专业组权限)
     */
    @PostMapping("/users")
    public Object users(@RequestParam(required = false) Long groupId) {
        LoginUserHandler.User user = LoginUserHandler.get();
        if (Objects.isNull(groupId)) {
            groupId = user.getGroupId();
        }
        return userService.selectByGroupIdAndOrgId(groupId, user.getOrgId());

    }

}
