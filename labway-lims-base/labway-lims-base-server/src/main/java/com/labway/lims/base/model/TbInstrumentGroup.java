package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 仪器专业小组
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_instrument_group")
public class TbInstrumentGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业小组ID
     */
    @TableId
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 专业小组编码
     */
    private String instrumentGroupCode;

    /**
     * 二次分拣颜色
     */
    private String secondSortColor;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 样本号开始值
     */
    private Long sampleStartValue;

    /**
     * 样本号结束值
     */
    private Long sampleEndValue;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否启动(0未启动 1启动)
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * is_delete
     * @see YesOrNoEnum
     */
    private Integer isDelete;


}
