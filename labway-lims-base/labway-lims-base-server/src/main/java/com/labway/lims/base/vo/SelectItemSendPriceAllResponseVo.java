package com.labway.lims.base.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 外送机构-查询外送项目
 * 
 * <AUTHOR>
 * @since 2023/5/5 16:16
 */
@Getter
@Setter
public class SelectItemSendPriceAllResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 价格ID
     */
    private Long priceId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 项目ID
     */
    private Long testItemId;

    /**
     * 项目编码
     */
    private String testItemCode;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    //--------------------
    /**
     * 外送价格
     */
    private String sendPrice;

    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 收费价格
     */
    private BigDecimal feePrice;
    /**
     * 是否启用(0未启动 1启动)
     * @see YesOrNoEnum
     */
    private Integer enable;

}
