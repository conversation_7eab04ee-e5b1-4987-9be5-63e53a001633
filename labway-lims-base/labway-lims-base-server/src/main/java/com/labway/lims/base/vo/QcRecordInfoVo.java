package com.labway.lims.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class QcRecordInfoVo {

    /**
     * 记录批次
     */
    private String qcRecordBatch;

    /**
     * 质控开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime qcStartDate;

    /**
     * 质控结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime qcEndDate;

    /**
     * 质控规则集合
     */
    private String qcRulesCollection;

    private List<QcSetRecordItemVo> recordList;
    /**
     * 更新时间
     */
    private LocalDateTime createDate;

    /**
     * 上控制线
     */
    private BigDecimal upperControlLimit;
    /**
     * 下控制线
     */
    private BigDecimal lowerControlLimit;
    /**
     * 定性类型
     */
    private Integer qualitativeType;

    public void addRecordItemVo(QcSetRecordItemVo recordItemVo) {
        if (recordList == null) {
            recordList = new ArrayList<>();
        }

        recordList.add(recordItemVo);
    }
}
