package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.HspOrgDeptDto;
import com.labway.lims.base.api.service.HspOrgDeptService;
import com.labway.lims.base.mapper.TbHspOrgDeptMapper;
import com.labway.lims.base.mapstruct.HspOrgDeptConverter;
import com.labway.lims.base.model.TbHspOrgDept;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/7/27 11:21
 */
@Slf4j
@DubboService
public class HspOrgDeptServiceImpl implements HspOrgDeptService {
    @Resource
    private TbHspOrgDeptMapper tbHspOrgDeptMapper;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private HspOrgDeptConverter hspOrgDeptConverter;

    @Override
    public List<HspOrgDeptDto> selectByHspOrgMainId(long hspOrgMainId) {
        final LambdaQueryWrapper<TbHspOrgDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbHspOrgDept::getHspOrgMainId, hspOrgMainId).orderByAsc(TbHspOrgDept::getHspOrgDeptId);
        return hspOrgDeptConverter.hspOrgDeptDtoListFromTbObj(tbHspOrgDeptMapper.selectList(wrapper));
    }

    @Override
    public void deleteByHspOrgDeptIds(Collection<Long> hspOrgDeptIds) {
        if (CollectionUtils.isEmpty(hspOrgDeptIds)) {
            return;
        }
        tbHspOrgDeptMapper.deleteBatchIds(hspOrgDeptIds);
    }

    @Override
    public long add(HspOrgDeptDto dto) {

        final TbHspOrgDept dept = JSON.parseObject(JSON.toJSONString(dto), TbHspOrgDept.class);
        final LoginUserHandler.User user = LoginUserHandler.get();
        dept.setHspOrgDeptId(snowflakeService.genId());
        dept.setOrgId(user.getOrgId());
        dept.setOrgName(user.getOrgName());
        dept.setUpdateDate(new Date());
        dept.setUpdaterId(user.getUserId());
        dept.setUpdaterName(user.getNickname());
        dept.setCreateDate(new Date());
        dept.setCreatorId(user.getUserId());
        dept.setCreatorName(user.getNickname());
        dept.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbHspOrgDeptMapper.insert(dept) < 1) {
            throw new IllegalStateException("添加失败");
        }
        log.info("用户 [{}] 添加送检机构科室 [{}] 成功", user.getNickname(), JSON.toJSONString(dept));
        return dept.getHspOrgDeptId();
    }

    @Override
    public void addBatch(Collection<HspOrgDeptDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        final LinkedList<Long> ids = snowflakeService.genIds(dtos.size());
        final LoginUserHandler.User user = LoginUserHandler.get();
        dtos.forEach(e -> {
            e.setHspOrgDeptId(ObjectUtils.defaultIfNull(e.getHspOrgDeptId(), ids.pop()));
            e.setOrgId(user.getOrgId());
            e.setOrgName(user.getOrgName());
            e.setUpdateDate(new Date());
            e.setUpdaterId(user.getUserId());
            e.setUpdaterName(user.getNickname());
            e.setCreateDate(new Date());
            e.setCreatorId(user.getUserId());
            e.setCreatorName(user.getNickname());
            e.setIsDelete(YesOrNoEnum.NO.getCode());
        });
        if (tbHspOrgDeptMapper.addBatch(dtos) < 1) {
            throw new IllegalStateException("添加失败");
        }
        log.info("用户 [{}] 添加送检机构科室 [{}] 成功", user.getNickname(), JSON.toJSONString(dtos));
    }

    @Override
    public void deleteByByHspOrgMainIds(Collection<Long> hspOrgMainIds) {
        if (CollectionUtils.isEmpty(hspOrgMainIds)) {
            return;
        }
        final LambdaQueryWrapper<TbHspOrgDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbHspOrgDept::getHspOrgMainId, hspOrgMainIds);
        tbHspOrgDeptMapper.delete(wrapper);
    }

    @Override
    public List<HspOrgDeptDto> selectByHspOrgDeptIds(Collection<Long> hspOrgDeptIds) {
        if (CollectionUtils.isEmpty(hspOrgDeptIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbHspOrgDept> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbHspOrgDept::getHspOrgDeptId, hspOrgDeptIds);
        queryWrapper.eq(TbHspOrgDept::getIsDelete, YesOrNoEnum.NO.getCode());
        return hspOrgDeptConverter.hspOrgDeptDtoListFromTbObj(tbHspOrgDeptMapper.selectList(queryWrapper));
    }

}
