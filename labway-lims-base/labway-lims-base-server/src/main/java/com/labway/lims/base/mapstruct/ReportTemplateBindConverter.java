
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.model.TbReportTemplateBind;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 报告单模板绑定 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface ReportTemplateBindConverter {

    ReportTemplateBindDto reportTemplateBindDtoFromTbObj(TbReportTemplateBind obj);

    List<ReportTemplateBindDto> reportTemplateBindDtoListFromTbObj(List<TbReportTemplateBind> list);

}
