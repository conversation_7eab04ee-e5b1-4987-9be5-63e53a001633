package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 体检团体套餐 获取 所有 查看 Vo
 * 
 * <AUTHOR>
 * @since 2023/3/29 11:10
 */
@Getter
@Setter
public class PackageListResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 套餐ID
     */
    private Long packageId;
    /**
     * 体检套餐名称
     */
    private String packageName;
    /**
     * 套餐类型编码
     */
    private String packageTypeCode;
    /**
     * 套餐类型名称
     */
    private String packageType;
    /**
     * 体检单位ID
     */
    private Long physicalGroupId;
    /**
     * 体检单位名称
     */
    private String physicalGroupName;
    /**
     * 送检机构ID
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 是否启用(0未启用 1启用)
     * @see YesOrNoEnum
     */
    private Integer enable;

    // ---------------统计-----------------------
    /**
     * 报告项目数量
     */
    private Integer reportItemCount;
}
