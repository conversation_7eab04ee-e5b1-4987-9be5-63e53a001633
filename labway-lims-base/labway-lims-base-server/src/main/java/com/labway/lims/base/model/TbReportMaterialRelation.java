package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 报告物料关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11 14:16
 */
@Getter
@Setter
@TableName("tb_report_material_relation")
public class TbReportMaterialRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报告物料关联ID
     */
    @TableId
    private Long reportMaterialRelationId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;
    
    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 物资编号
     */
    private String materialCode;
    
    /**
     * 物资名称
     */
    private String materialName;
    
    /**
     * 专业组名称
     */
    private String groupName;
    
    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 是否删除 1:删除 0:未删
     */
    private Integer isDelete;

    /**
     * 专业组编码
     */
    private String groupCode;
}