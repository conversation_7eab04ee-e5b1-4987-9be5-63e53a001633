package com.labway.lims.base.vo;

import com.labway.lims.base.api.enums.FileTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件
 */
@Getter
@Setter
public class FileVo implements Serializable {


    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 1: 文件 2: 文件夹
     *
     * @see FileTypeEnum
     */
    private Integer fileType;

    /**
     * 父文件ID
     */
    private Long parentFileId;

    /**
     * url 文件下载地址
     */
    private String url;


    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人名称
     */
    private String updaterName;


    /**
     * 文件大小，字节
     */
    private Long fileSize;

    /**
     * 文件大小，字节
     */
    private String fileSizeText;
}
