package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.ItemPriceBasePackageDetailService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.mapper.TbItemPriceBasePackageDetailMapper;
import com.labway.lims.base.mapstruct.ItemPriceBasePackageDetailConverter;
import com.labway.lims.base.model.TbItemPriceBasePackageDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@CacheConfig(cacheNames = "item-price-base-package-detail")
@Slf4j
@DubboService
public class ItemPriceBasePackageDetailServiceImpl implements ItemPriceBasePackageDetailService {

    @Resource
    private ItemPriceBasePackageService itemPriceBasePackageService;

    @Resource
    private TbItemPriceBasePackageDetailMapper tbItemPriceBasePackageDetailMapper;

    @DubboReference
    private TestItemService testItemService;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private ItemPriceBasePackageDetailConverter itemPriceBasePackageDetailConverter;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @CacheEvict(allEntries = true)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addItemPriceBasePackageDetails(List<ItemPriceBasePackageDetailDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 要添加的 项目价格基准包详情
        List<TbItemPriceBasePackageDetail> targetList =
            itemPriceBasePackageDetailConverter.tbItemPriceBasePackageDetailListFromTbObjDto(list);
        // 数量 项目价格基准包详情
        List<List<TbItemPriceBasePackageDetail>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbItemPriceBasePackageDetailMapper.batchAddItemPriceBasePackageDetails(item));
        log.info("基准包新增项目成功");
    }

    @CacheEvict(allEntries = true)
    @Override
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("ids不能为空");
        }

        if (tbItemPriceBasePackageDetailMapper.deleteBatchIds(ids) < 0) {
            throw new IllegalStateException("删除失败");
        }

        log.info("删除基准包对照试验项目成功，ids:{}", JSON.toJSONString(ids));
    }

    @Cacheable(key = "'packageDetails:'+ #packageId" )
    @Override
    public List<ItemPriceBasePackageDetailContrastDto> packageDetails(Long packageId) {

        if (Objects.isNull(packageId)) {
            return Collections.emptyList();
        }
        final Map<Long, ItemPriceBasePackageDetailDto> priceBasePackageDetailMap = selectByPackageId(packageId).stream()
            .collect(Collectors.toMap(ItemPriceBasePackageDetailDto::getTestItemId, Function.identity(), (a, b) -> a));

        return testItemService.selectByTestItemIds(priceBasePackageDetailMap.keySet()).stream().map(m -> {

            final ItemPriceBasePackageDetailDto itemPriceBasePackageDetail =
                priceBasePackageDetailMap.get(m.getTestItemId());
            if (Objects.isNull(itemPriceBasePackageDetail)) {
                return null;
            }
            final ItemPriceBasePackageDetailContrastDto obj = new ItemPriceBasePackageDetailContrastDto();
            obj.setTestItemId(m.getTestItemId());
            obj.setTestItemName(m.getTestItemName());
            obj.setTestItemCode(m.getTestItemCode());
            obj.setGroupName(m.getGroupName());
            obj.setPrice(itemPriceBasePackageDetail.getFeePrice());
            obj.setFinanceGroupName(m.getFinanceGroupName());
            obj.setExamMethodName(m.getExamMethodName());
            obj.setDetailId(itemPriceBasePackageDetail.getDetailId());
            obj.setEnable(m.getEnable());
            return obj;
        }).filter(Objects::nonNull).sorted(Comparator.comparing(ItemPriceBasePackageDetailContrastDto::getDetailId))
            .collect(Collectors.toList());

    }

    @Cacheable(key = "'selectByDetailId:'+ #detailId" )
    @Override
    public ItemPriceBasePackageDetailDto selectByDetailId(long detailId) {
        return itemPriceBasePackageDetailConverter
            .itemPriceBasePackageDetailDtoFromTbObj(tbItemPriceBasePackageDetailMapper.selectById(detailId));
    }

    @Override
    public List<ItemPriceBasePackageDetailDto> selectByDetailIds(Collection<Long> detailIds) {
        if (CollectionUtils.isEmpty(detailIds)) {
            return Collections.emptyList();
        }
        return itemPriceBasePackageDetailConverter.itemPriceBasePackageDetailDtoListFromTbObjList(
            tbItemPriceBasePackageDetailMapper.selectBatchIds(detailIds));
    }

    @CacheEvict(allEntries = true)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByDetailId(ItemPriceBasePackageDetailDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbItemPriceBasePackageDetail target =
            itemPriceBasePackageDetailConverter.tbItemPriceBasePackageDetailFromTbObjDto(dto);
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbItemPriceBasePackageDetailMapper.updateById(target) < 1) {
            throw new LimsException("修改项目失败");
        }

        log.info("用户 [{}] 修改项目成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }

    @Cacheable(key = "'selectItemPriceBasePackageDetailContrastList:'+ #filterType + ':' + #packageId" )
    @Override
    public List<ItemPriceBasePackageDetailContrastDto> selectItemPriceBasePackageDetailContrastList(Integer filterType,
        Long packageId) {
        if (Objects.isNull(filterType) || Objects.isNull(packageId)) {
            return Collections.emptyList();
        }

        final Set<Long> testItemIds = selectByPackageId(packageId).stream()
            .map(ItemPriceBasePackageDetailDto::getTestItemId).collect(Collectors.toSet());

        return testItemService.selectByOrgId(LoginUserHandler.get().getOrgId()).stream()
            // 1 过滤出对照了的，0 过滤出未对照的
            .filter(f -> Objects.equals(testItemIds.contains(f.getTestItemId()), BooleanUtils.toBoolean(filterType)))
            .map(m -> {
                final ItemPriceBasePackageDetailContrastDto obj = new ItemPriceBasePackageDetailContrastDto();
                obj.setTestItemId(m.getTestItemId());
                obj.setTestItemName(m.getTestItemName());
                obj.setTestItemCode(m.getTestItemCode());
                obj.setGroupName(m.getFinanceGroupName());
                obj.setPrice(m.getFeePrice());
                return obj;
            }).collect(Collectors.toList());

    }


    @Cacheable(key = "'selectByPackageId:' + #packageId" )
    @Override
    public List<ItemPriceBasePackageDetailDto> selectByPackageId(long packageId) {
        final LambdaQueryWrapper<TbItemPriceBasePackageDetail> eq = Wrappers
            .lambdaQuery(TbItemPriceBasePackageDetail.class).eq(TbItemPriceBasePackageDetail::getPackageId, packageId)
            .orderByAsc(TbItemPriceBasePackageDetail::getDetailId);

        return JSON.parseArray(JSON.toJSONString(tbItemPriceBasePackageDetailMapper.selectList(eq)),
            ItemPriceBasePackageDetailDto.class);
    }

    @Override
    public List<ItemPriceBasePackageDetailDto> selectByPackageIds(Collection<Long> packageIds) {
        if (CollectionUtils.isEmpty(packageIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbItemPriceBasePackageDetail> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbItemPriceBasePackageDetail::getPackageId, packageIds);
        queryWrapper.eq(TbItemPriceBasePackageDetail::getIsDelete, YesOrNoEnum.NO.getCode());

        return itemPriceBasePackageDetailConverter.itemPriceBasePackageDetailDtoListFromTbObjList(
            tbItemPriceBasePackageDetailMapper.selectList(queryWrapper));
    }

    @CacheEvict(allEntries = true)
    public void deleteByPackageId(Long packageId) {
        tbItemPriceBasePackageDetailMapper.delete(Wrappers.lambdaQuery(TbItemPriceBasePackageDetail.class)
            .eq(TbItemPriceBasePackageDetail::getPackageId, packageId));
    }

    @CacheEvict(allEntries = true)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int importItemPriceBasePackageDetail(Long packageId,
        List<ItemPriceBasePackageDetailSheetDto> importDetails) {
        ItemPriceBasePackageDto priceBasePackageDto = itemPriceBasePackageService.selectById(packageId);
        if (Objects.isNull(priceBasePackageDto)) {
            throw new IllegalStateException("基准包不存在");
        }

        // 基准包名称
        String packageName = priceBasePackageDto.getPackageName();
        // 当前登录的用户
        final LoginUserHandler.User user = LoginUserHandler.get();
        Date now = new Date();

        // 查询出来所有的检验项目
        Set<String> testItemCodes =
            importDetails.stream().map(ItemPriceBasePackageDetailSheetDto::getTestItemCode).collect(Collectors.toSet());
        List<TestItemDto> testItemDtos = testItemService.selectByTestItemCodes(testItemCodes, user.getOrgId());

        // key: 检验项目code value: 收费价格
        Map<String, BigDecimal> feePriceByTestItemCode =
            importDetails.stream().collect(Collectors.toMap(ItemPriceBasePackageDetailSheetDto::getTestItemCode,
                ItemPriceBasePackageDetailSheetDto::getFeePrice, (key1, key2) -> key1));

        // 现有检验项目
        Map<Long, ItemPriceBasePackageDetailDto> existItemMapById = selectByPackageId(packageId).stream()
            .collect(Collectors.toMap(ItemPriceBasePackageDetailDto::getTestItemId, Function.identity(), (a, b) -> a));

        // 新增项目
        LinkedList<Long> genIds = snowflakeService.genIds(testItemDtos.size());
        Map<Long, String> addTestItemNameById = new HashMap<>();
        List<ItemPriceBasePackageDetailDto> addList = testItemDtos.stream().filter(m -> {
            ItemPriceBasePackageDetailDto detailDto = existItemMapById.get(m.getTestItemId());
            return Objects.isNull(detailDto);
        }).map(m -> {
            ItemPriceBasePackageDetailDto obj = new ItemPriceBasePackageDetailDto();
            obj.setDetailId(genIds.pop());
            obj.setPackageId(packageId);
            obj.setTestItemId(m.getTestItemId());
            obj.setTestItemCode(m.getTestItemCode());
            obj.setOrgId(user.getOrgId());
            obj.setOrgName(user.getOrgName());
            obj.setCreateDate(now);
            obj.setUpdateDate(now);
            obj.setUpdaterId(user.getUserId());
            obj.setUpdaterName(user.getNickname());
            obj.setCreatorId(user.getUserId());
            obj.setCreatorName(user.getNickname());
            obj.setIsDelete(YesOrNoEnum.NO.getCode());
            obj.setFeePrice(feePriceByTestItemCode.get(m.getTestItemCode()));
            addTestItemNameById.put(m.getTestItemId(), m.getTestItemName());
            return obj;
        }).collect(Collectors.toList());
        // 新增
        addItemPriceBasePackageDetails(addList);
        // 记录新增日志
        addList.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.ITEM_PRICE_BASE_PACKAGE.getDesc())
                    .setContent(String.format("基准包 [%s] 导入项目 [%s]", packageName,
                        addTestItemNameById.getOrDefault(item.getTestItemId(), item.getTestItemCode())))
                    .toJSONString());
        });

        // 修改项目
        Map<Long, String> itemCompareMapById = new HashMap<>();
        List<ItemPriceBasePackageDetailDto> updateList = testItemDtos.stream().filter(m -> {
            ItemPriceBasePackageDetailDto detailDto = existItemMapById.get(m.getTestItemId());
            return Objects.nonNull(detailDto);
        }).map(m -> {
            ItemPriceBasePackageDetailDto obj = existItemMapById.get(m.getTestItemId());
            ItemPriceBasePackageDetailDto old =
                JSON.parseObject(JSON.toJSONString(obj), ItemPriceBasePackageDetailDto.class);
            obj.setPackageId(packageId);
            obj.setTestItemId(m.getTestItemId());
            obj.setTestItemCode(m.getTestItemCode());
            obj.setOrgId(user.getOrgId());
            obj.setOrgName(user.getOrgName());
            obj.setCreateDate(now);
            obj.setUpdateDate(now);
            obj.setUpdaterId(user.getUserId());
            obj.setUpdaterName(user.getNickname());
            obj.setCreatorId(user.getUserId());
            obj.setCreatorName(user.getNickname());
            obj.setIsDelete(YesOrNoEnum.NO.getCode());
            obj.setFeePrice(feePriceByTestItemCode.get(m.getTestItemCode()));
            addTestItemNameById.put(m.getTestItemId(), m.getTestItemName());
            itemCompareMapById.put(m.getTestItemId(),
                new CompareUtils<ItemPriceBasePackageDetailDto>().compare(old, obj));
            return obj;
        }).collect(Collectors.toList());
        for (ItemPriceBasePackageDetailDto detailDto : updateList) {
            updateByDetailId(detailDto);
        }
        // 记录覆盖修改日志
        updateList.forEach(item -> {
            String compare = itemCompareMapById.get(item.getTestItemId());
            String testItemName = addTestItemNameById.getOrDefault(item.getTestItemId(), item.getTestItemCode());
            if (StringUtils.isNotBlank(compare)) {
                rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.ITEM_PRICE_BASE_PACKAGE.getDesc())
                        .setContent(String.format("修改[%s]基准包下[%s]项目: [%s]", packageName, testItemName, compare))
                        .toJSONString());
            }
        });

        return testItemDtos.size();
    }

}
