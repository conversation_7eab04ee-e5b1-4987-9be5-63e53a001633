package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.InstrumentReportItemCommonPhraseDto;
import com.labway.lims.base.model.TbInstrumentReportItemCommonPhrase;
import org.mapstruct.Mapper;

/**
 * <p>
 * InstrumentReportItemCommonPhraseConverter
 * 仪器报告项目常用短语 转换
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 9:23
 */
@Mapper(componentModel = "spring")
public interface InstrumentReportItemCommonPhraseConverter {

    TbInstrumentReportItemCommonPhrase convertDto2Entity(InstrumentReportItemCommonPhraseDto commonPhraseDto);

}
