
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.MedicineDto;
import com.labway.lims.base.model.TbMedicine;
import com.labway.lims.base.vo.MedicineAddRequestVo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 药物 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface MedicineConverter {

    MedicineDto medicineDtoFromMedicineAddRequestVo(MedicineAddRequestVo obj);

    TbMedicine tbMedicineFromTbObjDto(MedicineDto obj);

    MedicineDto medicineDtoTbObj(TbMedicine obj);

    List<MedicineDto> medicineDtoListTbObj(List<TbMedicine> obj);
}
