package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.PackageDto;
import com.labway.lims.base.model.TbPackage;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 体检团体套餐 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface PackageConverter {

    PackageDto physicalGroupPackageDtoFromTbObj(TbPackage obj);

    List<PackageDto> physicalGroupPackageDtoListFromTbObj(List<TbPackage> list);

}
