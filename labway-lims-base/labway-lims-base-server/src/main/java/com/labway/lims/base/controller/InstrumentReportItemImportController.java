package com.labway.lims.base.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleTypeEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.api.enums.base.InstrumentItemTypeEnum;
import com.labway.lims.api.enums.routine.TestResultTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.*;
import com.labway.lims.base.vo.excel.InstrumentReportItemCommonPhraseSheet;
import com.labway.lims.base.vo.excel.InstrumentReportItemReferenceSheet;
import com.labway.lims.base.vo.excel.InstrumentReportItemResultExchangeSheet;
import com.labway.lims.base.vo.excel.InstrumentReportItemSheet;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/instrument-report-item-import")
public class InstrumentReportItemImportController extends BaseController {

    @Resource
    private InstrumentReportItemService instrumentReportItemService;
    @Resource
    private InstrumentReportItemCommonPhraseService instrumentReportItemCommonPhraseService;
    @Resource
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    @Resource
    private InstrumentReportItemResultExchangeService instrumentReportItemResultExchangeService;
    @Resource
    private InstrumentService instrumentService;

    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @PostMapping("/import")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<org.springframework.core.io.Resource> i(@RequestPart("file") MultipartFile file) {

        String importLock = redisPrefix.getBasePrefix() + "instrument-report-item-info-import:";

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(importLock, StringUtils.EMPTY, 5, TimeUnit.MINUTES))) {
            throw new IllegalStateException("正在导入");
        }

        try {

            if (Objects.isNull(file) || file.isEmpty()) {
                throw new IllegalStateException("文件信息为空");
            }

            if (!Objects.requireNonNull(file.getOriginalFilename(), "文件名为空").endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
                throw new IllegalStateException("文件格式不正确");
            }

            final ExcelReader build = EasyExcelFactory.read(file.getInputStream()).autoCloseStream(true).build();

            // 仪器报告项目
            final InstrumentReportItemListener instrumentReportItemListener =
                    new InstrumentReportItemListener(instrumentReportItemService, instrumentService);
            ReadSheet readSheet0 = EasyExcel.readSheet(0)
                    .head(InstrumentReportItemSheet.class)
                    .registerReadListener(instrumentReportItemListener).build();

            // 参考范围
            final InstrumentReportItemReferenceListener instrumentReportItemReferenceListener =
                    new InstrumentReportItemReferenceListener(instrumentReportItemService, instrumentReportItemReferenceService);
            ReadSheet readSheet1 = EasyExcel.readSheet(1)
                    .head(InstrumentReportItemReferenceSheet.class)
                    .registerReadListener(instrumentReportItemReferenceListener).build();

            // 仪器报告项目常用短语
            final InstrumentReportItemCommonPhraseListener instrumentReportItemCommonPhraseListener =
                    new InstrumentReportItemCommonPhraseListener(instrumentReportItemService, instrumentReportItemCommonPhraseService);
            ReadSheet readSheet2 = EasyExcel.readSheet(2)
                    .head(InstrumentReportItemCommonPhraseSheet.class)
                    .registerReadListener(instrumentReportItemCommonPhraseListener).build();

            // 仪器报告项目结果值转换
            final InstrumentReportItemResultExchangeListener instrumentReportItemResultExchangeListener =
                    new InstrumentReportItemResultExchangeListener(instrumentReportItemService, instrumentReportItemResultExchangeService);
            ReadSheet readSheet3 = EasyExcel.readSheet(3)
                    .head(InstrumentReportItemResultExchangeSheet.class)
                    .registerReadListener(instrumentReportItemResultExchangeListener).build();


            build.read(readSheet0, readSheet1, readSheet2, readSheet3);

            build.finish();

            // 清除字典数据
            BasicReadListener.dictMap.clear();

            final List<ErrorMsg> reportItemReportErrorMsgs = instrumentReportItemListener.getErrorMsgs()
                    .stream().sorted(Comparator.comparing(ErrorMsg::getIndex)).collect(Collectors.toList());

            final List<ErrorMsg> commonErrorMsgs = instrumentReportItemCommonPhraseListener.getErrorMsgs()
                    .stream().sorted(Comparator.comparing(ErrorMsg::getIndex)).collect(Collectors.toList());

            final List<ErrorMsg> resultExchangeMsgs = instrumentReportItemResultExchangeListener.getErrorMsgs()
                    .stream().sorted(Comparator.comparing(ErrorMsg::getIndex)).collect(Collectors.toList());

            final List<ErrorMsg> refMsgs = instrumentReportItemReferenceListener.getErrorMsgs()
                    .stream().sorted(Comparator.comparing(ErrorMsg::getIndex)).collect(Collectors.toList());

            List<ErrorMsg> errorMsgs = new ArrayList<>();
            errorMsgs.add(new ErrorMsg(0, "-------------------------------------- 仪器报告项目 ------------------------------------"));
            errorMsgs.addAll(reportItemReportErrorMsgs);

            errorMsgs.add(new ErrorMsg(0, "-------------------------------------- 参考范围 ------------------------------------"));
            errorMsgs.addAll(refMsgs);

            errorMsgs.add(new ErrorMsg(0, "-------------------------------------- 常用短语 ------------------------------------"));
            errorMsgs.addAll(commonErrorMsgs);

            errorMsgs.add(new ErrorMsg(0, "-------------------------------------- 结果值转换 ------------------------------------"));
            errorMsgs.addAll(resultExchangeMsgs);


            final File tempFile = File.createTempFile(IdUtil.fastSimpleUUID(), "");
            FileUtils.writeLines(tempFile, errorMsgs);

            return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=%s", URLEncoder.encode("错误消息.txt", StandardCharsets.UTF_8))).contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(MediaType.TEXT_PLAIN).body(new FileSystemResource(tempFile));

        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            stringRedisTemplate.delete(importLock);
        }
    }

    /**
     * 仪器报告项目
     */
    public static final class InstrumentReportItemListener extends BasicReadListener<InstrumentReportItemSheet> {

        private final InstrumentReportItemService instrumentReportItemService;

        private final InstrumentService instrumentService;

        final Map<String, String> examMethodMap;

        public InstrumentReportItemListener(InstrumentReportItemService instrumentReportItemService, InstrumentService instrumentService) {
            this.instrumentReportItemService = instrumentReportItemService;
            this.instrumentService = instrumentService;
            this.examMethodMap = CollectionUtil.defaultIfEmpty(instrumentReportItemService.selectAllExamMethod(), List.of())
                    .stream().collect(Collectors.toMap(DictItemDto::getDictName, DictItemDto::getDictCode));

        }

        @Override
        public void invoke(InstrumentReportItemSheet data, AnalysisContext context) {
            index++;

            final String instrumentCode = data.getInstrumentCode();
            if (StringUtils.isBlank(instrumentCode)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行仪器编码为空", index)));
                return;
            }
            final String reportItemCode = data.getReportItemCode();
            if (StringUtils.isBlank(reportItemCode)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行报告项目编码为空", index)));
                return;
            }

            final String reportItemName = data.getReportItemName();
            if (StringUtils.isBlank(reportItemName)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行报告项目名称为空", index)));
                return;
            }

            // 项目单位
            final String reportItemUnitName = data.getReportItemUnitName();
            if (StringUtils.isNotBlank(reportItemUnitName)) {
                final String unit = dictMap.getOrDefault(DictEnum.PROJECT_UNIT.name(), defaultMap).get(reportItemUnitName);
                if (StringUtils.isBlank(unit)) {
                    errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行项目单位 %s 不存在", index, reportItemUnitName)));
                    return;
                }

                data.setReportItemUnit(unit);
            }


            final String examMethodName = data.getExamMethodName();
            if (StringUtils.isBlank(examMethodName)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行检验方法名称为空", index)));
                return;
            }

            if (StringUtils.isBlank(examMethodMap.get(examMethodName))) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行检验方法名称 %s 不存在", index, examMethodName)));
                return;
            }

            // 结果类型
            final String resultTypeName = data.getResultTypeName();
            if (StringUtils.isBlank(resultTypeName)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行结果类型为空", index)));
                return;
            }
            final TestResultTypeEnum resultTypeEnum = TestResultTypeEnum.getEnumByName(resultTypeName);
            if (Objects.isNull(resultTypeEnum)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行结果类型 %s 不存在", index, resultTypeName)));
                return;
            }
            data.setTestResultTypeEnum(resultTypeEnum);

            // 项目类型
            final String itemTypeName = data.getItemTypeName();
            if (StringUtils.isBlank(itemTypeName)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行项目类型为空", index)));
                return;
            }

            final InstrumentItemTypeEnum itemTypeEnum = InstrumentItemTypeEnum.selectByName(data.getItemTypeName());
            if (Objects.isNull(itemTypeEnum)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行仪器项目类型 %s 不存在", index, itemTypeName)));
                return;
            }
            data.setItemTypeEnum(itemTypeEnum);
            data.setIndex(index);
            list.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            final Set<String> instrumentCodes = list.stream().map(InstrumentReportItemSheet::getInstrumentCode).collect(Collectors.toSet());

            final Map<String, InstrumentDto> instrumentMap = instrumentService.selectByOrgIdAndInstrumentCodes(user.getOrgId(), instrumentCodes).stream().collect(Collectors.toMap(InstrumentDto::getInstrumentCode, Function.identity()));

            if (MapUtils.isEmpty(instrumentMap)) {
                errorMsgs.add(new ErrorMsg(0, String.format("仪器 %s 不存在", instrumentCodes)));
                return;
            }

            // 获取这些仪器的所有报告项目，用来判断是否已存在。如果已经存在，则不导入
            final Set<Long> instrumentIds = instrumentMap.values().stream().map(InstrumentDto::getInstrumentId).collect(Collectors.toSet());
            final Map<Long, Map<String, String>> existInstrumentReportItemMap = instrumentReportItemService.selectByInstrumentIds(instrumentIds).stream().collect(Collectors.groupingBy(InstrumentReportItemDto::getInstrumentId, Collectors.toMap(InstrumentReportItemDto::getReportItemCode, InstrumentReportItemDto::getReportItemName, (a, b) -> a)));

            final LinkedList<Long> ids = snowflakeService.genIds(list.size());

            final List<InstrumentReportItemDto> instrumentReportItems = new ArrayList<>(list.size());
            for (InstrumentReportItemSheet item : list.stream().sorted(Comparator.comparing(InstrumentReportItemSheet::getIndex)).collect(Collectors.toList())) {

                final Integer itemIndex = item.getIndex();

                final String instrumentCode = item.getInstrumentCode();
                final InstrumentDto instrument = instrumentMap.get(instrumentCode);
                if (Objects.isNull(instrument)) {
                    errorMsgs.add(new ErrorMsg(itemIndex, String.format("第 %s 行，仪器 %s 不存在", itemIndex, instrumentCode)));
                    continue;
                }
                item.setInstrument(instrument);

                //  当前仪器是否存在该报告项目, 如果存在，则不导入
                if (Objects.nonNull(existInstrumentReportItemMap.getOrDefault(instrument.getInstrumentId(), defaultMap).get(item.getReportItemCode()))) {
                    errorMsgs.add(new ErrorMsg(itemIndex, String.format("第 %s 行，仪器 %s 已存在报告项目 %s", itemIndex, instrumentCode, item.getReportItemCode())));
                    continue;
                }
                final InstrumentReportItemDto instrumentReportItem = convertSheetToDto(item);
                instrumentReportItem.setInstrumentReportItemId(ids.pop());
                instrumentReportItems.add(instrumentReportItem);
            }

            if (CollectionUtils.isEmpty(instrumentReportItems)) {
                return;
            }

            instrumentReportItemService.saveBatch(instrumentReportItems);
        }

        public InstrumentReportItemDto convertSheetToDto(InstrumentReportItemSheet item) {
            final InstrumentDto instrument = item.getInstrument();


            final InstrumentReportItemDto instrumentReportItem = new InstrumentReportItemDto();
            instrumentReportItem.setReportItemCode(StringUtils.defaultString(item.getReportItemCode()));
            instrumentReportItem.setReportItemName(StringUtils.defaultString(item.getReportItemName()));
            instrumentReportItem.setReportItemUnit(StringUtils.defaultString(item.getReportItemUnit()));
            instrumentReportItem.setReportItemUnitName(StringUtils.defaultString(item.getReportItemUnitName()));
            instrumentReportItem.setItemTypeCode(item.getItemTypeEnum().getCode());
            instrumentReportItem.setItemTypeName(StringUtils.defaultString(item.getItemTypeName()));
            instrumentReportItem.setEnable(YesOrNoEnum.YES.getCode());
            instrumentReportItem.setInstrumentId(instrument.getInstrumentId());
            instrumentReportItem.setInstrumentCode(StringUtils.defaultString(instrument.getInstrumentCode()));
            instrumentReportItem.setInstrumentName(StringUtils.defaultString(instrument.getInstrumentName()));
            instrumentReportItem.setEnName(StringUtils.defaultString(item.getEnName()));
            instrumentReportItem.setEnAb(StringUtils.defaultString(item.getEnAb()));
            instrumentReportItem.setAliasName("");
            instrumentReportItem.setExamMethodName(StringUtils.defaultString(item.getExamMethodName()));
            instrumentReportItem.setExamMethodCode(StringUtils.defaultString(examMethodMap.get(item.getExamMethodName())));
            instrumentReportItem.setIsQc(Objects.equals(item.getIsQc(), "否") ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
            instrumentReportItem.setIsPrint(Objects.equals(item.getIsPrint(), "否") ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
            instrumentReportItem.setPrintSort(NumberUtils.toInt(item.getPrintSort(), NumberUtils.INTEGER_ZERO));
            instrumentReportItem.setIsManualInput(Objects.equals(item.getIsManualInput(), "否") ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
            instrumentReportItem.setIsResultZero(Objects.equals(item.getIsResultZero(), "否") ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
            instrumentReportItem.setResultTypeName(StringUtils.defaultString(item.getResultTypeName()));
            instrumentReportItem.setResultTypeCode(item.getTestResultTypeEnum().getCode());
            instrumentReportItem.setIsResultNull(Objects.equals(item.getIsResultNull(), "否") ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
            instrumentReportItem.setDecimalNums(ObjectUtil.defaultIfNull(item.getDecimalNums(), NumberUtils.INTEGER_TWO));
            instrumentReportItem.setInstrumentChannel(StringUtils.defaultString(item.getInstrumentChannel()));
            instrumentReportItem.setCalcFomulation("");
            instrumentReportItem.setOrgId(user.getOrgId());
            instrumentReportItem.setOrgName(user.getOrgName());
            instrumentReportItem.setCreateDate(now);
            instrumentReportItem.setUpdateDate(now);
            instrumentReportItem.setCreatorId(user.getUserId());
            instrumentReportItem.setCreatorName(user.getNickname());
            instrumentReportItem.setUpdaterId(user.getUserId());
            instrumentReportItem.setUpdaterName(user.getNickname());
            instrumentReportItem.setIsBringOut(Objects.equals(item.getIsBringOut(), "是") ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
            instrumentReportItem.setIsDelete(YesOrNoEnum.NO.getCode());
            return instrumentReportItem;
        }
    }

    /**
     * 仪器报告项目常用短语
     */
    public static final class InstrumentReportItemCommonPhraseListener extends BasicReadListener<InstrumentReportItemCommonPhraseSheet> {
        private final InstrumentReportItemService instrumentReportItemService;
        private final InstrumentReportItemCommonPhraseService instrumentReportItemCommonPhraseService;

        public InstrumentReportItemCommonPhraseListener(InstrumentReportItemService instrumentReportItemService, InstrumentReportItemCommonPhraseService instrumentReportItemCommonPhraseService) {
            this.instrumentReportItemCommonPhraseService = instrumentReportItemCommonPhraseService;
            this.instrumentReportItemService = instrumentReportItemService;
        }

        @Override
        public void invoke(InstrumentReportItemCommonPhraseSheet data, AnalysisContext context) {
            index++;
            final String instrumentCode = data.getInstrumentCode();
            if (StringUtils.isBlank(instrumentCode)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行，仪器编码为空", index)));
                return;
            }

            final String reportItemCode = data.getReportItemCode();
            if (StringUtils.isBlank(reportItemCode)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行，报告项目编码为空", index)));
                return;
            }


            if (StringUtils.isBlank(data.getKeyShort())) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行，快捷键为空", index)));
                return;
            }

            final String content = data.getContent();
            if (StringUtils.isBlank(content)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行，短语内容为空", index)));
                return;
            }


            list.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }


            final Set<String> reportItemCodes = list.stream().map(InstrumentReportItemCommonPhraseSheet::getReportItemCode)
                    .collect(Collectors.toSet());

            // Map<instrumentCode, Map<ReportItemCode, Map<KeyShort, content>>>
            final Map<String, Map<String, Map<String, String>>> commonPhraseMap = instrumentReportItemCommonPhraseService.selectByReportItemCodes(reportItemCodes)
                    .stream().collect(Collectors.groupingBy(InstrumentReportItemCommonPhraseDto::getInstrumentCode
                            , Collectors.groupingBy(InstrumentReportItemCommonPhraseDto::getReportItemCode
                                    , Collectors.toMap(InstrumentReportItemCommonPhraseDto::getKeyShort, InstrumentReportItemCommonPhraseDto::getContent, (a, b) -> a))));

            final LinkedList<Long> ids = snowflakeService.genIds(list.size());

            final List<InstrumentReportItemCommonPhraseDto> instrumentReportItemCommonPhrases = new ArrayList<>();

            for (InstrumentReportItemCommonPhraseSheet commonPhraseSheet : list) {
                final String reportItemCode = commonPhraseSheet.getReportItemCode();
                final String instrumentCode = commonPhraseSheet.getInstrumentCode();

                final InstrumentReportItemDto instrumentReportItem =
                        instrumentReportItemService.selectByInstrumentCodeAndReportItemCode(instrumentCode, reportItemCode);
                if (Objects.isNull(instrumentReportItem)) {
                    errorMsgs.add(new ErrorMsg(index,
                            String.format("第 %s 行，仪器编码 %s 报告项目 %s 不存在"
                                    , index, instrumentCode, commonPhraseSheet.getReportItemName())));
                    continue;
                }

                // 判断这个报告项目是否已存在常用短语
                if (StringUtils.isNotBlank(commonPhraseMap.getOrDefault(instrumentCode, new HashMap<>())
                        .getOrDefault(reportItemCode, defaultMap)
                        .get(commonPhraseSheet.getKeyShort()))) {
                    errorMsgs.add(new ErrorMsg(index,
                            String.format("第 %s 行，仪器编码 %s 报告项目 %s 快捷键 %s 已存在"
                                    , index, instrumentCode, commonPhraseSheet.getReportItemName(), commonPhraseSheet.getKeyShort())));
                    continue;
                }


                final InstrumentReportItemCommonPhraseDto commonPhraseDto = convertCommonPhrase(commonPhraseSheet, instrumentReportItem);
                commonPhraseDto.setInstrumentReportItemCommonPhraseId(ids.pop());
                instrumentReportItemCommonPhrases.add(commonPhraseDto);
            }

            if (CollectionUtils.isNotEmpty(instrumentReportItemCommonPhrases)) {
                for (InstrumentReportItemCommonPhraseDto instrumentReportItemCommonPhrase : instrumentReportItemCommonPhrases) {
                    instrumentReportItemCommonPhraseService.addInstrumentReportItemCommonPhrase(instrumentReportItemCommonPhrase);
                }
            }
        }

        private InstrumentReportItemCommonPhraseDto convertCommonPhrase(InstrumentReportItemCommonPhraseSheet commonPhraseSheet, InstrumentReportItemDto instrumentReportItem) {
            final InstrumentReportItemCommonPhraseDto commonPhrase = new InstrumentReportItemCommonPhraseDto();
            commonPhrase.setInstrumentReportItemId(instrumentReportItem.getInstrumentReportItemId());
            commonPhrase.setReportItemCode(StringUtils.defaultString(instrumentReportItem.getReportItemCode()));
            commonPhrase.setReportItemName(StringUtils.defaultString(instrumentReportItem.getReportItemName()));
            commonPhrase.setInstrumentId(instrumentReportItem.getInstrumentId());
            commonPhrase.setInstrumentCode(StringUtils.defaultString(instrumentReportItem.getInstrumentCode()));
            commonPhrase.setInstrumentName(StringUtils.defaultString(instrumentReportItem.getInstrumentName()));
            commonPhrase.setKeyShort(StringUtils.defaultString(commonPhraseSheet.getKeyShort()));
            commonPhrase.setSort(ObjectUtils.defaultIfNull(commonPhraseSheet.getSort(), NumberUtils.INTEGER_ONE));
            commonPhrase.setContent(StringUtils.defaultString(commonPhraseSheet.getContent()));
            commonPhrase.setIsDefault(YesOrNoEnum.NO.getCode());
            commonPhrase.setCreateDate(now);
            commonPhrase.setUpdateDate(now);
            commonPhrase.setCreatorId(user.getUserId());
            commonPhrase.setCreatorName(user.getNickname());
            commonPhrase.setUpdaterId(user.getUserId());
            commonPhrase.setUpdaterName(user.getNickname());
            commonPhrase.setIsDelete(YesOrNoEnum.NO.getCode());
            return commonPhrase;
        }
    }

    /**
     * 仪器报告项目参考范围
     */
    public static final class InstrumentReportItemReferenceListener extends BasicReadListener<InstrumentReportItemReferenceSheet> {
        private final InstrumentReportItemService instrumentReportItemService;
        private final InstrumentReportItemReferenceService instrumentReportItemReferenceService;

        public InstrumentReportItemReferenceListener(InstrumentReportItemService instrumentReportItemService, InstrumentReportItemReferenceService instrumentReportItemReferenceService) {
            this.instrumentReportItemReferenceService = instrumentReportItemReferenceService;
            this.instrumentReportItemService = instrumentReportItemService;
        }

        @Override
        public void invoke(InstrumentReportItemReferenceSheet data, AnalysisContext context) {
            index++;
            final InstrumentReportItemReferenceDto dto =
                    JSON.parseObject(JSON.toJSONString(data), InstrumentReportItemReferenceDto.class);
            dto.setSexStyleName(StringUtils.defaultString(dto.getSexStyleName(), "通用"));
            dto.setSexStyle(ObjectUtils.defaultIfNull(dto.getSexStyle(), 0));
            dto.setSampleTypeName(StringUtils.defaultString(dto.getSampleTypeName(), SampleTypeEnum.ALL.getSampleTypeName()));
            dto.setSampleTypeCode(StringUtils.defaultString(dto.getSampleTypeCode(), SampleTypeEnum.ALL.getSampleTypeCode()));

            try {
                ReportItemReferenceController.checkRefParam(dto);
            } catch (Exception e) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行，%s", index, e.getMessage())));
                return;
            }

            data.setIndex(index);

            list.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            final List<DictItemDto> items = SpringUtil.getBean(DictService.class).selectAllSampleType();

            final LinkedList<Long> ids = snowflakeService.genIds(list.size());
            for (InstrumentReportItemReferenceSheet sheet : list) {
                final String instrumentCode = sheet.getInstrumentCode();
                final String reportItemCode = sheet.getReportItemCode();

                final InstrumentReportItemDto instrumentReportItem =
                        instrumentReportItemService.selectByInstrumentCodeAndReportItemCode(instrumentCode, reportItemCode);
                if (Objects.isNull(instrumentReportItem)) {
                    errorMsgs.add(new ErrorMsg(index,
                            String.format("第 %s 行，仪器编码 %s 报告项目 %s 不存在"
                                    , index, instrumentCode, sheet.getReportItemName())));
                    continue;
                }

                try {
                    sheet.setSexStyleName(StringUtils.defaultString(sheet.getSexStyleName(), "通用"));
                    sheet.setSampleTypeName(StringUtils.defaultString(sheet.getSampleTypeName(), "通用"));

                    final InstrumentReportItemReferenceDto dto = JSON.parseObject(JSON.toJSONString(sheet), InstrumentReportItemReferenceDto.class);
                    dto.setInstrumentReportItemReferenceId(ids.pop());
                    dto.setInstrumentReportItemId(instrumentReportItem.getInstrumentReportItemId());
                    dto.setReportItemCode(StringUtils.defaultString(instrumentReportItem.getReportItemCode()));
                    dto.setReportItemName(StringUtils.defaultString(instrumentReportItem.getReportItemName()));
                    dto.setInstrumentId(instrumentReportItem.getInstrumentId());
                    dto.setInstrumentCode(StringUtils.defaultString(instrumentReportItem.getInstrumentCode()));

                    dto.setSexStyle(Objects.equals(sheet.getSexStyleName(), "通用") ?
                            0 : Objects.equals(sheet.getSexStyleName(), "男") ?
                            SexEnum.MAN.getCode() : Objects.equals(sheet.getSexStyleName(), "女") ? SexEnum.WOMEN.getCode() : 0);
                    dto.setSexStyleName(sheet.getSexStyleName());
                    dto.setSampleTypeCode(NumberUtils.INTEGER_ZERO.toString());

                    // 获取样本类型编码
                    if (!Objects.equals(sheet.getSampleTypeName(), "通用")) {
                        final DictItemDto dictItem = items
                                .stream().filter(sampleInfo -> Objects.equals(sampleInfo.getDictName(), sheet.getSampleTypeName()))
                                .findFirst().orElse(null);
                        if (Objects.isNull(dictItem)) {
                            errorMsgs.add(new ErrorMsg(sheet.getIndex(), String.format("第 %s 行，样本类型 %s 不存在", sheet.getIndex(), sheet.getSampleTypeName())));
                            continue;
                        }
                        dto.setSampleTypeCode(StringUtils.defaultString(dictItem.getDictCode()));
                        dto.setSampleTypeName(StringUtils.defaultString(dictItem.getDictName()));
                    }

                    instrumentReportItemReferenceService.addInstrumentReportItemReference(dto);
                } catch (Exception e) {
                    errorMsgs.add(new ErrorMsg(sheet.getIndex(), String.format("第 %s 行，%s", sheet.getIndex(), e.getMessage())));
                }
            }
        }
    }

    /**
     * 仪器报告项目结果转换
     */
    public static final class InstrumentReportItemResultExchangeListener extends BasicReadListener<InstrumentReportItemResultExchangeSheet> {
        private final InstrumentReportItemService instrumentReportItemService;
        private final InstrumentReportItemResultExchangeService instrumentReportItemResultExchangeService;

        public InstrumentReportItemResultExchangeListener(InstrumentReportItemService instrumentReportItemService, InstrumentReportItemResultExchangeService instrumentReportItemResultExchangeService) {
            this.instrumentReportItemResultExchangeService = instrumentReportItemResultExchangeService;
            this.instrumentReportItemService = instrumentReportItemService;
        }

        @Override
        public void invoke(InstrumentReportItemResultExchangeSheet data, AnalysisContext context) {
            index++;
            final String instrumentCode = data.getInstrumentCode();
            if (StringUtils.isBlank(instrumentCode)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行，仪器编码为空", index)));
                return;
            }

            final String reportItemCode = data.getReportItemCode();
            if (StringUtils.isBlank(reportItemCode)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行，报告项目编码为空", index)));
                return;
            }


            if (StringUtils.isBlank(data.getInstrumentResult())) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行，仪器结果值为空", index)));
                return;
            }

            final String exchangeResult = data.getExchangeResult();
            if (StringUtils.isBlank(exchangeResult)) {
                errorMsgs.add(new ErrorMsg(index, String.format("第 %s 行，转换结果值为空", index)));
                return;
            }


            list.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }


            final Set<String> reportItemCodes = list.stream().map(InstrumentReportItemResultExchangeSheet::getReportItemCode)
                    .collect(Collectors.toSet());

            // Map<instrumentCode, Map<ReportItemCode, Map<KeyShort, content>>>
            final Map<String, Map<String, Map<String, String>>> commonPhraseMap = instrumentReportItemResultExchangeService.selectByReportItemCodes(reportItemCodes)
                    .stream().collect(Collectors.groupingBy(InstrumentReportItemResultExchangeDto::getInstrumentCode
                            , Collectors.groupingBy(InstrumentReportItemResultExchangeDto::getReportItemCode
                                    , Collectors.toMap(InstrumentReportItemResultExchangeDto::getInstrumentResult, InstrumentReportItemResultExchangeDto::getExchangeResult, (a, b) -> a))));

            final LinkedList<Long> ids = snowflakeService.genIds(list.size());

            final List<InstrumentReportItemResultExchangeDto> instrumentReportItemResultExchanges = new ArrayList<>();

            for (InstrumentReportItemResultExchangeSheet resultExchangeSheet : list) {
                final String reportItemCode = resultExchangeSheet.getReportItemCode();
                final String instrumentCode = resultExchangeSheet.getInstrumentCode();

                final InstrumentReportItemDto instrumentReportItem =
                        instrumentReportItemService.selectByInstrumentCodeAndReportItemCode(instrumentCode, reportItemCode);
                if (Objects.isNull(instrumentReportItem)) {
                    errorMsgs.add(new ErrorMsg(index,
                            String.format("第 %s 行，仪器编码 %s 报告项目 %s 不存在"
                                    , index, instrumentCode, resultExchangeSheet.getReportItemName())));
                    continue;
                }

                // 判断这个报告项目是否已存在常用短语
                if (StringUtils.isNotBlank(commonPhraseMap.getOrDefault(instrumentCode, new HashMap<>())
                        .getOrDefault(reportItemCode, defaultMap)
                        .get(resultExchangeSheet.getInstrumentResult()))) {
                    errorMsgs.add(new ErrorMsg(index,
                            String.format("第 %s 行，仪器编码 %s 报告项目 %s 仪器结果值 %s 已存在"
                                    , index, instrumentCode, resultExchangeSheet.getReportItemName(), resultExchangeSheet.getInstrumentResult())));
                    continue;
                }


                final InstrumentReportItemResultExchangeDto resultExchange = convertResultExchange(resultExchangeSheet, instrumentReportItem);
                resultExchange.setInstrumentReportItemResultExchangeId(ids.pop());
                instrumentReportItemResultExchanges.add(resultExchange);
            }

            if (CollectionUtils.isNotEmpty(instrumentReportItemResultExchanges)) {
                for (InstrumentReportItemResultExchangeDto instrumentReportItemResultExchange : instrumentReportItemResultExchanges) {
                    instrumentReportItemResultExchangeService.addInstrumentReportItemResultExchange(instrumentReportItemResultExchange);
                }
            }
        }

        private InstrumentReportItemResultExchangeDto convertResultExchange(InstrumentReportItemResultExchangeSheet resultExchangeSheet, InstrumentReportItemDto instrumentReportItem) {
            final InstrumentReportItemResultExchangeDto resultExchange = new InstrumentReportItemResultExchangeDto();
            resultExchange.setInstrumentReportItemId(instrumentReportItem.getInstrumentReportItemId());
            resultExchange.setReportItemCode(StringUtils.defaultString(instrumentReportItem.getReportItemCode()));
            resultExchange.setReportItemName(StringUtils.defaultString(instrumentReportItem.getReportItemName()));
            resultExchange.setInstrumentId(instrumentReportItem.getInstrumentId());
            resultExchange.setInstrumentCode(StringUtils.defaultString(instrumentReportItem.getInstrumentCode()));
            resultExchange.setInstrumentName(StringUtils.defaultString(instrumentReportItem.getInstrumentName()));
            resultExchange.setInstrumentResult(resultExchangeSheet.getInstrumentResult());
            resultExchange.setExchangeResult(resultExchangeSheet.getExchangeResult());
            resultExchange.setCreateDate(now);
            resultExchange.setUpdateDate(now);
            resultExchange.setCreatorId(user.getUserId());
            resultExchange.setCreatorName(user.getNickname());
            resultExchange.setUpdaterId(user.getUserId());
            resultExchange.setUpdaterName(user.getNickname());
            resultExchange.setIsDelete(YesOrNoEnum.NO.getCode());


            return resultExchange;
        }

        private InstrumentReportItemCommonPhraseDto convertCommonPhrase(InstrumentReportItemCommonPhraseSheet commonPhraseSheet, InstrumentReportItemDto instrumentReportItem) {
            final InstrumentReportItemCommonPhraseDto commonPhrase = new InstrumentReportItemCommonPhraseDto();
            commonPhrase.setInstrumentReportItemId(instrumentReportItem.getInstrumentReportItemId());
            commonPhrase.setReportItemCode(StringUtils.defaultString(instrumentReportItem.getReportItemCode()));
            commonPhrase.setReportItemName(StringUtils.defaultString(instrumentReportItem.getReportItemName()));
            commonPhrase.setInstrumentId(instrumentReportItem.getInstrumentId());
            commonPhrase.setInstrumentCode(StringUtils.defaultString(instrumentReportItem.getInstrumentCode()));
            commonPhrase.setInstrumentName(StringUtils.defaultString(instrumentReportItem.getInstrumentName()));
            commonPhrase.setKeyShort(StringUtils.defaultString(commonPhraseSheet.getKeyShort()));
            commonPhrase.setSort(ObjectUtils.defaultIfNull(commonPhraseSheet.getSort(), NumberUtils.INTEGER_ONE));
            commonPhrase.setContent(StringUtils.defaultString(commonPhraseSheet.getContent()));
            commonPhrase.setIsDefault(YesOrNoEnum.NO.getCode());
            commonPhrase.setCreateDate(now);
            commonPhrase.setUpdateDate(now);
            commonPhrase.setCreatorId(user.getUserId());
            commonPhrase.setCreatorName(user.getNickname());
            commonPhrase.setUpdaterId(user.getUserId());
            commonPhrase.setUpdaterName(user.getNickname());
            commonPhrase.setIsDelete(YesOrNoEnum.NO.getCode());
            return commonPhrase;
        }
    }

    /**
     * 默认实现
     */
    @Setter
    @Getter
    public abstract static class BasicReadListener<T> extends AnalysisEventListener<T> {
        final Date now = new Date();

        protected final Set<T> list;

        protected final SnowflakeService snowflakeService;

        protected static Map<String, Map<String, String>> dictMap;

        protected static final String YES = "是";

        protected static final HashMap<String, String> defaultMap = new HashMap<>();

        protected static final LoginUserHandler.User user = LoginUserHandler.get();
        /**
         * 错误信息
         */
        protected List<ErrorMsg> errorMsgs;

        /**
         * 记录行
         */
        protected int index;

        public BasicReadListener() {
            list = new LinkedHashSet<>();
            snowflakeService = SpringUtil.getBean(SnowflakeService.class);
            errorMsgs = new LinkedList<>();
            initDictMap();
        }

        /**
         * 获取字典数据
         */
        public void initDictMap() {
            if (MapUtils.isNotEmpty(dictMap)) {
                return;
            }

            dictMap = SpringUtil.getBean(DictService.class)
                    // 获取 样本类型、管型、检验方法
                    .selectByDictTypes(Lists.newArrayList(
                            // 项目单位
                            DictEnum.PROJECT_UNIT.name(),
                            // 结果类型
                            DictEnum.RESULT_TYPE.name(),
                            // 项目类型
                            DictEnum.INSTRUMENT_PROJECT_TYPE.name()))

                    //  Map<dictType,Map<dictName, dictCode>>
                    .stream().collect(Collectors.groupingBy(DictItemDto::getDictType, Collectors.toMap(DictItemDto::getDictName, DictItemDto::getDictCode, (a, b) -> a)));
        }
    }


    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static final class ErrorMsg {
        private Integer index;

        private String msg;


        @Override
        public String toString() {
            return msg;
        }
    }
}
