package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 仪器药物更新 vo
 * 
 * <AUTHOR>
 * @since 2023/7/12 20:04
 */
@Getter
@Setter
public class UpdateInstrumentMedicineRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器id
     */
    private Long instrumentId;

    /**
     * 药物id
     */
    private Long medicineId;
    /**
     * 仪器通道号
     */
    private String instrumentChannel;

    /**
     * 发送到lims 1:是 0：否
     */
    private Integer sendLims;
}
