package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.base.api.dto.PackageItemDto;
import com.labway.lims.base.api.service.PackageItemService;
import com.labway.lims.base.mapper.TbPackageItemMapper;
import com.labway.lims.base.mapstruct.PackageItemConverter;
import com.labway.lims.base.model.TbPackageItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 体检单位套餐 项目 service impl
 * 
 * <AUTHOR>
 * @since 2023/3/28 17:37
 */
@Slf4j
@DubboService
public class PackageItemServiceImpl implements PackageItemService {

    @Resource
    private TbPackageItemMapper tbPackageItemMapper;

    @Resource
    private PackageItemConverter packageItemConverter;

    @Override
    public List<PackageItemDto> selectByPackageId(long packageId) {

        LambdaQueryWrapper<TbPackageItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbPackageItem::getPackageId, packageId);
        queryWrapper.eq(TbPackageItem::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbPackageItem::getCreateDate);
        return packageItemConverter
            .packageItemDtoListFromTbObj(tbPackageItemMapper.selectList(queryWrapper));
    }

    @Override
    public List<PackageItemDto> selectByPackageIds(Collection<Long> packageIds) {
        if (CollectionUtils.isEmpty(packageIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbPackageItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbPackageItem::getPackageId, packageIds);
        queryWrapper.eq(TbPackageItem::getIsDelete, YesOrNoEnum.NO.getCode());

        return packageItemConverter
            .packageItemDtoListFromTbObj(tbPackageItemMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPackageItems(List<PackageItemDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 检验套餐项目
        List<TbPackageItem> targetList =
            list.stream().map(obj -> JSON.parseObject(JSON.toJSONString(obj), TbPackageItem.class))
                .collect(Collectors.toList());
        // 数量 分区批次插入
        List<List<TbPackageItem>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbPackageItemMapper.batchAddPackageItems(item));

        log.info("用户 [{}] 新增检验套餐项目[{}]成功", loginUser.getNickname(), JSON.toJSONString(targetList));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPackageItemIds(Collection<Long> packageItemIds) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除检验套餐项目成功 [{}] 结果 [{}]", loginUser.getNickname(), packageItemIds,
            tbPackageItemMapper.deleteBatchIds(packageItemIds) > 0);
    }

    @Override
    public List<PackageItemDto> selectByPackageItemIds(Collection<Long> packageItemIds) {
        if (CollectionUtils.isEmpty(packageItemIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbPackageItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbPackageItem::getPackageItemId, packageItemIds);
        queryWrapper.eq(TbPackageItem::getIsDelete, YesOrNoEnum.NO.getCode());
        return packageItemConverter
            .packageItemDtoListFromTbObj(tbPackageItemMapper.selectList(queryWrapper));
    }

    @Override
    public List<PackageItemDto> selectAllByOrgIdAndType(Long orgId, String typeCode) {
        if (Objects.isNull(orgId) || StringUtils.isBlank(typeCode)){
            return Collections.emptyList();
        }

        return tbPackageItemMapper.selectAllByOrgIdAndType(orgId,typeCode);
    }

}
