package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.InstrumentReportItemCommonPhraseDto;
import com.labway.lims.base.model.TbInstrumentReportItemCommonPhrase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仪器报告项目常用短语 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Mapper
public interface TbInstrumentReportItemCommonPhraseMapper extends BaseMapper<TbInstrumentReportItemCommonPhrase> {

    /**
     * 根据仪器专业小组查询
     */
    List<InstrumentReportItemCommonPhraseDto> selectByInstrumentGroupId(@Param("instrumentGroupId") long instrumentGroupId);
}
