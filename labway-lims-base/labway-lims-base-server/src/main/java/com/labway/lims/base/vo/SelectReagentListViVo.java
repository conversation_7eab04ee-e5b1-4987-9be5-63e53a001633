
package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 主数据获取试剂耗材数据接口 Vi
 *
 * <AUTHOR>
 * @since 2023/3/2 10:13
 */
@Getter
@Setter
public class SelectReagentListViVo {
    /**
     * 最后修改时间 从 yyyy-MM-dd HH:mm:ss
     */
    private Date modifiedTimeStart;

    /**
     * 最后修改时间 至 yyyy-MM-dd HH:mm:ss
     */
    private Date modifiedTimeEnd;

    /**
     * 创建时间时间 从 yyyy-MM-dd HH:mm:ss
     */
    private Date creationtimeStart;

    /**
     * 最后修改时间 至 yyyy-MM-dd HH:mm:ss
     */
    private Date creationtimeEnd;

    /**
     * 同时根据 修改时间、创建时间一起查看
     */
    private Boolean creationAndModified = Boolean.FALSE;

    /**
     * 当前页
     */
    private int current = 1;

    /**
     * 每页显示条数
     */
    private int size = 10;

}
