package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.ArchiveStoreDto;
import com.labway.lims.base.api.service.ArchiveStoreService;
import com.labway.lims.base.api.service.RefrigeratorService;
import com.labway.lims.base.vo.ArchiveStoreAddRequestVo;
import com.labway.lims.base.vo.ArchiveStoreUpdateRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 归档库 API
 * 
 * <AUTHOR>
 * @since 2023/4/3 11:01
 */
@Slf4j
@RestController
@RequestMapping("/archive-store")
public class ArchiveStoreController extends BaseController {
    @DubboReference
    private ArchiveStoreService archiveStoreService;
    @DubboReference
    private RefrigeratorService refrigeratorService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 归档库 新增
     */
    @PostMapping("/add")
    public Object archiveStoreAdd(@RequestBody ArchiveStoreAddRequestVo vo) {
        if (StringUtils.isAnyBlank(vo.getArchiveStoreCode(), vo.getArchiveStoreName())
            || Objects.isNull(vo.getEnable())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        if (StringUtils.length(vo.getArchiveStoreCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("归档库编码过长");
        }

        checkVoWhenAddOrUpdate(vo);

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        if (Objects
            .nonNull(archiveStoreService.selectByArchiveStoreName(vo.getArchiveStoreName(), loginUser.getOrgId()))) {
            throw new LimsException("当前归档库名称已存在");
        }

        if (Objects
            .nonNull(archiveStoreService.selectByArchiveStoreCode(vo.getArchiveStoreCode(), loginUser.getOrgId()))) {
            throw new LimsException("当前归档库编码已存在");
        }

        // 转换
        ArchiveStoreDto target = JSON.parseObject(JSON.toJSONString(vo), ArchiveStoreDto.class);

        long id = archiveStoreService.addArchiveStore(target);
        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.ARCHIVE_STORE_LOG.getDesc())
                .setContent(String.format("新增 [%s] 归档库", target.getArchiveStoreName())).toJSONString());

        return Map.of("id", id);

    }

    /**
     * 归档库 删除
     */
    @PostMapping("/delete")
    public Object archiveStoreDelete(@RequestBody Set<Long> archiveStoreIds) {
        if (CollectionUtils.isEmpty(archiveStoreIds)) {
            return Collections.emptyMap();
        }

        // 检查归档库是否被冰箱使用
        if (refrigeratorService.checkArchiveStoreUseByIds(archiveStoreIds)) {
            throw new LimsException("归档库中存在冰箱,不可删除");
        }

        List<ArchiveStoreDto> archiveStoreDtos = archiveStoreService.selectByArchiveStoreIds(archiveStoreIds);
        archiveStoreService.deleteByArchiveStoreIds(archiveStoreIds);
        archiveStoreDtos.forEach(item -> {
            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.ARCHIVE_STORE_LOG.getDesc())
                    .setContent(String.format("删除 [%s] 归档库", item.getArchiveStoreName())).toJSONString());
        });

        return Collections.emptyMap();
    }

    /**
     * 归档库 修改
     */
    @PostMapping("/update")
    public Object archiveStoreUpdate(@RequestBody ArchiveStoreUpdateRequestVo vo) {
        if (StringUtils.isBlank(vo.getArchiveStoreName()) || Objects.isNull(vo.getEnable())
            || Objects.isNull(vo.getArchiveStoreId())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        // 判断归档库是否存在
        final ArchiveStoreDto archiveStoreDtoNow = archiveStoreService.selectByArchiveStoreId(vo.getArchiveStoreId());
        if (Objects.isNull(archiveStoreDtoNow)) {
            throw new LimsException("归档库不存在");
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        ArchiveStoreDto selectByArchiveStoreName =
            archiveStoreService.selectByArchiveStoreName(vo.getArchiveStoreName(), loginUser.getOrgId());
        if (Objects.nonNull(selectByArchiveStoreName)
            && !Objects.equals(vo.getArchiveStoreId(), selectByArchiveStoreName.getArchiveStoreId())) {
            throw new LimsException("当前归档库名称已存在");
        }

        final ArchiveStoreDto target = new ArchiveStoreDto();
        BeanUtils.copyProperties(archiveStoreDtoNow, target);

        // 更新项
        target.setArchiveStoreName(vo.getArchiveStoreName());
        target.setEnable(vo.getEnable());

        archiveStoreService.updateByArchiveStoreId(target);

        String compare = new CompareUtils<ArchiveStoreDto>().compare(archiveStoreDtoNow, target);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.ARCHIVE_STORE_LOG.getDesc())
                    .setContent(String.format("修改归档库: [%s]", compare)).toJSONString());
        }

        return Collections.emptyMap();

    }

    /**
     * 归档库 获取 所有 查看
     */
    @PostMapping("/select-all")
    public Object archiveStoreList() {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        return archiveStoreService.selectByOrgId(loginUser.getOrgId());
    }

    /**
     * 检查 归档库 新增 或 修改 参数 公共部分
     *
     */
    private <T extends ArchiveStoreAddRequestVo> void checkVoWhenAddOrUpdate(T vo) {
        if (StringUtils.length(vo.getArchiveStoreName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("归档库名称过长");
        }
        if (!(Objects.equals(vo.getEnable(), YesOrNoEnum.NO.getCode())
            || Objects.equals(vo.getEnable(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("是否启用参数错误");
        }
    }
}
