package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * 试剂耗材类型 Vo
 * 
 */
@Getter
@Setter
public class ReagentTypeVo {
    /**
     * 信息hash
     */
    private String infoHash;
    /**
     * ncc唯一码
     */
    private String fromNo;
    /**
     * 所属组织ncc关联id
     */
    private String ncOrgId;
    /**
     * 所属组织名称
     */
    private String orgName;
    /**
     * 上级物料分类,ncc关联id
     */
    private String parentType;
    /**
     * 物料基本分类编码
     */
    private String reagentTypeCode;
    /**
     * 物料基本分类名称
     */
    private String reagentType;
    /**
     * 启用状态:1未启用，2已启用，3已停用
     */
    private Integer enableState;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private String creationTime;
    /**
     * 最后修改人
     */
    private String modifier;
    /**
     * 最后修改时间
     */
    private String modifiedTime;
    /**
     * 雪花生成唯一id
     */
    private String itemId;
}
