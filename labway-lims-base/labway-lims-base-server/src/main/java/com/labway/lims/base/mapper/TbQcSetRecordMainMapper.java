package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.QcSetRecordMainDto;
import com.labway.lims.base.model.TbQcSetRecordMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【tb_qc_set_record_main(仪器质控设置记录)】的数据库操作Mapper
* @createDate 2023-10-31 19:18:30
* @Entity com.domain.TbQcSetRecordMain
*/
@Mapper
public interface TbQcSetRecordMainMapper extends BaseMapper<TbQcSetRecordMain> {
    //根据报告项id和查询报告项
    List<QcSetRecordMainDto> selectAllByReportItemIdAndGroupId(@Param("reportId") Long reportId, @Param("groupId") Long groupId);
}




