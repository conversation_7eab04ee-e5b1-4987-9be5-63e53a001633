package com.labway.lims.base.controller;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.OrgTestItemMappingInfoDTO;
import com.labway.business.center.compare.request.QueryItemMappingRequest;
import com.labway.business.center.compare.service.TestItemPublishService;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.PackageTypeEnum;
import com.labway.lims.api.enums.base.TwoPickDayEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.ExportTestItemsDTO;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentGroupTestItemDto;
import com.labway.lims.base.api.dto.PackageItemDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentGroupTestItemService;
import com.labway.lims.base.api.service.PackageItemService;
import com.labway.lims.base.api.service.RandomStringService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.vo.MainDataTestItemVo;
import com.labway.lims.base.vo.ReportItemVo;
import com.labway.lims.base.vo.TestItemAddVo;
import com.labway.lims.base.vo.TestItemUpdateVo;
import com.labway.lims.base.vo.TestItemVo;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023/3/20 14:29
 */
@Slf4j
@RestController
@RequestMapping("/test-item")
public class TestItemController extends BaseController {

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private TestItemService testItemService;
    @Resource
    private InstrumentGroupTestItemService instrumentGroupTestItemService;
    @Resource
    private InstrumentGroupService instrumentGroupService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private ReportItemService reportItemService;
    @Resource
    private TestItemPublishService testItemPublishService;
    @Resource
    private RandomStringService randomStringService;
    @Resource
    private EnvDetector envDetector;
    @Resource
    private Environment environment;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @Resource
    private PackageItemService packageItemService;

	@Resource
	private GroupService groupService;

    /**
     * 业务中台的机构编码
     */
    public static final String BUSINESS_CENTER_ORG_CODE = EnvDetector.BUSINESS_CENTER_ORG_CODE;

    /**
     * 获取主数据的所有检验项目
     */
    @PostMapping("/main-data/test-items")
    public List<MainDataTestItemVo> mainDataTestItems() {

        if (envDetector.isDev()) {
            final LinkedList<Long> ids = snowflakeService.genIds(10000);
            return IntStream.range(0, RandomUtils.nextInt(10, 50)).boxed().map(e -> {
                final MainDataTestItemVo vo = new MainDataTestItemVo();
                vo.setTestItemId(ids.pop());
                vo.setTestItemName(randomStringService.randomChineseString());
                vo.setTestItemCode(randomStringService.randomChineseString());
                vo.setEnName(randomStringService.randomChineseString());
                vo.setItemType(randomStringService.randomChineseString());
                vo.setStashRemark(randomStringService.randomChineseString());
                vo.setReportItems(IntStream.range(0, RandomUtils.nextInt(10, 50)).boxed().map(k -> {
                    final MainDataTestItemVo.ReportItem reportItem = new MainDataTestItemVo.ReportItem();
                    reportItem.setReportItemId(ids.pop());
                    reportItem.setReportItemCode(randomStringService.randomChineseString());
                    reportItem.setReportItemName(randomStringService.randomChineseString());
                    return reportItem;
                }).collect(Collectors.toList()));
                return vo;
            }).collect(Collectors.toList());
        }

        final QueryItemMappingRequest request = new QueryItemMappingRequest();
        final String orgCode = environment.getProperty(BUSINESS_CENTER_ORG_CODE);
        if (StringUtils.isBlank(orgCode)) {
            throw new IllegalArgumentException(String.format("请配置 %s 参数", BUSINESS_CENTER_ORG_CODE));
        }
        request.setOrgCode(orgCode);

        final Response<List<OrgTestItemMappingInfoDTO>> response = testItemPublishService.queryItemMapping(request);
        if (Objects.isNull(response)) {
            log.error("查询业务中台接口 queryItemMapping 失败，返回 null");
            return Collections.emptyList();
        }

        if (!response.isSuccess()) {
            log.error("查询业务中台接口 queryItemMapping 失败 不是 isSuccess msg: {}", response.getMsg());
            return Collections.emptyList();
        }

        final List<OrgTestItemMappingInfoDTO> data = new LinkedList<>(response.getData());

        log.info("查询业务中台接口 queryItemMapping 成功，返回项目数量 [{}]", data.size());

        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }

        final Set<String> testItemCodes = testItemService.selectByOrgId(LoginUserHandler.get().getOrgId()).stream()
                .map(TestItemDto::getTestItemCode).collect(Collectors.toSet());

        data.removeIf(e -> testItemCodes.contains(e.getItemTestCode()));

        return data.stream().map(e -> {
            final MainDataTestItemVo vo = new MainDataTestItemVo();
            vo.setTestItemId(NumberUtils.toLong(e.getItemTestId()));
            vo.setTestItemName(e.getItemTestName());
            vo.setTestItemCode(e.getItemTestCode());
            vo.setEnName(e.getEnglishName());
            vo.setItemType(e.getSampleTypeName());
            vo.setTestMethodName(e.getTestMethodName());
            vo.setStashRemark(e.getSaveDescription());
            vo.setReportItems(Collections.emptyList());
            if (CollectionUtils.isNotEmpty(e.getReportItems())) {
                vo.setReportItems(e.getReportItems().stream().map(k -> {
                    final MainDataTestItemVo.ReportItem reportItem = new MainDataTestItemVo.ReportItem();
                    reportItem.setReportItemId(NumberUtils.toLong(k.getItemReportId()));
                    reportItem.setReportItemCode(k.getItemReportCode());
                    reportItem.setReportItemName(k.getItemReportName());
                    return reportItem;
                }).collect(Collectors.toList()));
            }
            return vo;
        }).collect(Collectors.toList());
    }


    /**
     * 启用
     */
    @PostMapping("/enable")
    public Object enable(@RequestBody Set<Long> testItemIds) {
        for (Long testItemId : testItemIds) {
            final TestItemDto testItem = new TestItemDto();
            testItem.setEnable(YesOrNoEnum.YES.getCode());
            testItem.setTestItemId(testItemId);
            testItem.setUpdateDate(new Date());
            testItem.setUpdaterId(LoginUserHandler.get().getUserId());
            testItem.setUpdaterName(LoginUserHandler.get().getNickname());
            testItemService.updateByTestItemId(testItem);
        }
        return Map.of();
    }


    /**
     * 停用
     */
    @PostMapping("/disable")
    public Object disable(@RequestBody Set<Long> testItemIds) {
        for (Long testItemId : testItemIds) {
            final TestItemDto testItem = new TestItemDto();
            testItem.setEnable(YesOrNoEnum.NO.getCode());
            testItem.setTestItemId(testItemId);
            testItem.setUpdateDate(new Date());
            testItem.setUpdaterId(LoginUserHandler.get().getUserId());
            testItem.setUpdaterName(LoginUserHandler.get().getNickname());
            testItemService.updateByTestItemId(testItem);
        }
        return Map.of();
    }

    /**
     * 从主数据添加检验项目
     */
    @PostMapping("/add")
    public Object add(@RequestBody TestItemAddVo vo) {
        if (CollectionUtils.isEmpty(vo.getTestItemCodes()) || Objects.isNull(vo.getGroupId())) {
            throw new IllegalArgumentException("参数错误");
        }

        final String key = redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":add";

        if (BooleanUtils
                .isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofMinutes(1)))) {
            throw new IllegalArgumentException("正在添加中");
        }

        try {
            testItemService.addTestItems(vo.getTestItemCodes(), vo.getGroupId());
        } finally {
            stringRedisTemplate.delete(key);
        }

        return Collections.emptyMap();
    }

    /**
     * 从主数据同步
     */
    @PostMapping("/sync")
    public Object sync(String testItemCode) {
        final String key = redisPrefix.getBasePrefix() + testItemCode + ":sync";

        if (BooleanUtils
                .isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofMinutes(1)))) {
            throw new IllegalArgumentException("正在同步中");
        }

        try {
            testItemService.syncMainDataTestItem(testItemCode);
        } finally {
            stringRedisTemplate.delete(key);
        }

        return Collections.emptyMap();
    }

    /**
     * 更新
     */
    @PostMapping("/update")
    public Object update(@RequestBody TestItemUpdateVo testItem) {

        if (
            // ID
                Objects.isNull(testItem.getTestItemId()) ||
                        // 字符串
                        StringUtils.isAnyBlank(testItem.getAliasName(), testItem.getTubeCode(), testItem.getShortName(),
                                testItem.getItemType())
                        // 专业组
                        || Objects.isNull(testItem.getGroupId())
                        // 支持收费
                        || Objects.isNull(testItem.getEnableFee())
                        // 价格
                        || Objects.isNull(testItem.getFeePrice())) {
            throw new IllegalArgumentException("参数错误");
        }

        if (Objects.equals(testItem.getEnableExport(), YesOrNoEnum.YES.getCode())) {
            if (!Objects.equals(testItem.getItemType(), ItemTypeEnum.OUTSOURCING.name())) {
                throw new IllegalArgumentException("外送时项目类型必须是" + ItemTypeEnum.OUTSOURCING.getDesc());
            }

            if (Objects.isNull(testItem.getExportDate())) {
                throw new IllegalArgumentException("外送报告时间不能为空");
            }

            if (!NumberUtils.isParsable(testItem.getExportDate())) {
                throw new IllegalArgumentException("外送报告时间必须为数字");
            }
        }

        if (Objects.equals(testItem.getItemType(), ItemTypeEnum.OUTSOURCING.name())) {
            if (!Objects.equals(testItem.getEnableExport(), YesOrNoEnum.YES.getCode())) {
                throw new IllegalArgumentException("项目类型是 [" + ItemTypeEnum.OUTSOURCING.getDesc() + "] 时, 是否外送必须是 [是]");
            }

            if (Objects.isNull(testItem.getExportDate())) {
                throw new IllegalArgumentException("外送报告时间不能为空");
            }

            if (!NumberUtils.isParsable(testItem.getExportDate())) {
                throw new IllegalArgumentException("外送报告时间必须为数字");
            }

            if (StringUtils.isBlank(testItem.getExportOrgName()) || Objects.isNull(testItem.getExportOrgId())) {
                throw new IllegalArgumentException("外送机构不能为空");
            }

			// 主分支-V1.1.4.5【检验项目维护】项目类型为外送检验的，专业组只能为委外组（包含配置的委外组）
	        Map<String, ProfessionalGroupDto> professionalGroupDtoMap = groupService.selectOutsourcingGroupMapByGroupCode(LoginUserHandler.get().getOrgId());
	        if (professionalGroupDtoMap.values().stream().noneMatch(e -> Objects.equals(e.getGroupId(), testItem.getGroupId()))) {
				throw new IllegalArgumentException("项目类型是 [" + ItemTypeEnum.OUTSOURCING.getDesc() + "] 时, 专业组必须是 [委外组（包含配置的委外组）]");
	        }
        }

        final TestItemDto testItemOld = testItemService.selectByTestItemId(testItem.getTestItemId());
        if (Objects.isNull(testItemOld)) {
            throw new IllegalArgumentException("检验项目不存在");
        }

        //判断二次分拣日期的问题
        if (StringUtils.isNotBlank(testItem.getTwoPickDay()) && !TwoPickDayEnum.CURRENT.getCode().equals(testItem.getTwoPickDay())) {
            String[] days = testItem.getTwoPickDay().split(",");
            for (String day : days) {
                //如果包含除了当天之外的数据，则提示不可混用
                if (day.equals(TwoPickDayEnum.CURRENT.getCode())) {
                    throw new IllegalArgumentException("当天和其他日期不可混用");
                }
                //如果包含了其他枚举，也提示不可用
                if (Objects.isNull(TwoPickDayEnum.selectByCode(day))){
                    throw new IllegalArgumentException("您选择的时间不在有效范围");
                }
            }
        }else {
            //设置默认
            testItem.setTwoPickDay(TwoPickDayEnum.CURRENT.getCode());
            testItem.setTwoPickTime(StringUtils.EMPTY);
        }

        final TestItemDto update = JSON.parseObject(JSON.toJSONString(testItem), TestItemDto.class);
        // fix: 外送机构为空的话， mybaits-plus 不更新， 置为默认值
        update.setExportOrgName(StringUtils.defaultString(update.getExportOrgName()));

        if (StringUtils.isBlank(testItem.getExportDate())) {
            update.setExportDate(new BigDecimal(0));
        }

        if (!testItemService.updateByTestItemId(update)) {
            throw new IllegalStateException("修改检验项目失败");
        }

        // 日志
        final String compare = new CompareUtils<TestItemDto>().compare(testItemOld, update);
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.TEST_ITEM.getDesc())
                            .setContent(String.format("修改检验项目 [%s]", compare)).toJSONString());
        }

        return Collections.emptyMap();
    }

    /**
     * 获取报告项目
     */
    @GetMapping("/report-items")
    public Object reportItems(@RequestParam Long testItemId) {
        if (Objects.isNull(testItemId)) {

            throw new IllegalArgumentException("参数错误");
        }
        return reportItemService.selectByTestItemId(testItemId);
    }

    /**
     * 获取专业组对应的检验项目列表
     */
    @GetMapping("/items")
    public List<TestItemDto> items(@RequestParam(required = false, value = "groupId") Long groupId,
                                   @RequestParam(required = false, value = "itemType") String itemType,
                                   @RequestParam(required = false, value = "enable") Integer enable

    ) {
        List<TestItemDto> testItemDtos;
        if (Objects.isNull(groupId)) {
            testItemDtos = testItemService.selectByOrgId(LoginUserHandler.get().getOrgId());
        } else {
            testItemDtos = testItemService.selectByGroupId(groupId);
        }

        if (enable != null && (enable == 0 || enable == 1)) {
            testItemDtos = testItemDtos.stream().filter(e -> e.getEnable().equals(enable)).collect(Collectors.toList());
        }

        if (StringUtils.isNotBlank(itemType)) {
            testItemDtos = testItemDtos.stream().filter(obj -> Objects.equals(obj.getItemType(), itemType))
                    .collect(Collectors.toList());
        }
        return testItemDtos;
    }

    @PostMapping("/export-items")
    public Object exportItems(@RequestBody ExportTestItemsDTO exportTestItemsDTO
    ) throws IOException {
        String itemType = exportTestItemsDTO.getItemType();
        Long groupId = exportTestItemsDTO.getGroupId();
        Integer enable = exportTestItemsDTO.getEnable();
        String examMethodCode = exportTestItemsDTO.getExamMethodCode();
        String sampleTypeCode = exportTestItemsDTO.getSampleTypeCode();
        String itemCodeOrName = exportTestItemsDTO.getItemCodeOrName();

        List<TestItemDto> items = this.items(groupId, itemType, enable);

        Stream<TestItemDto> stream = items.stream();
        if (StringUtils.isNotBlank(examMethodCode)) {
            stream = stream.filter(e -> Objects.equals(e.getExamMethodCode(), examMethodCode));
        }
        if (StringUtils.isNotBlank(sampleTypeCode)) {
            stream = stream.filter(e -> Objects.equals(e.getSampleTypeCode(), sampleTypeCode));
        }
        if (StringUtils.isNotBlank(itemCodeOrName)) {
            stream = stream.filter(e -> e.getTestItemCode().contains(itemCodeOrName)
                    || e.getTestItemName().contains(itemCodeOrName)
            );
        }
        items = stream.collect(Collectors.toList());


//        Assert.isTrue(CollectionUtils.isNotEmpty(items), "未查询到检验项目");
        List<Long> testItemids = items.stream().map(TestItemDto::getTestItemId).collect(Collectors.toList());

        List<ReportItemDto> reportItemDtos = reportItemService.selectByTestItemIds(testItemids);

        Map<Long, List<ReportItemDto>> testIdMap = reportItemDtos.stream().collect(Collectors.groupingBy(ReportItemDto::getTestItemId));


        final File tempFile = File.createTempFile("test-item-export", null);

        try (ExcelWriter writer = ExcelUtil.getBigWriter(); FileOutputStream fos = new FileOutputStream(tempFile)) {
            List<Object> builder = new LinkedList<>(List.of(
                    "检验项目编码",
                    "检验项目名称",
                    "英文名称",
                    "样本类型",
                    "检验方法",
                    "专业组",
                    "是否启用",
                    "报告项目编码",
                    "报告项目名称"));
            writer.writeHeadRow(builder);

            for (TestItemDto dto : items) {
                List<ReportItemDto> testReportItemDtos = testIdMap.get(dto.getTestItemId());
                String enable1 = dto.getEnable() == null ? "" : dto.getEnable() == 0 ? "否" : "是";
                if (CollectionUtils.isEmpty(testReportItemDtos)) {
                    List<Object> objects = fillExportData(dto, null, enable1);
                    writer.writeRow(objects);
                    continue;
                }
                for (ReportItemDto testReportItemDto : testReportItemDtos) {
                    List<Object> objects = fillExportData(dto, testReportItemDto, enable1);
                    writer.writeRow(objects);
                }

            }
            writer.flush(fos);
        }
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("检验项目.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(new FileSystemResource(tempFile));
    }

    private List<Object> fillExportData(TestItemDto dto, ReportItemDto testReportItemDto, String enable) {
        List<Object> builder = new LinkedList<>();
        builder.add(dto.getTestItemCode());
        builder.add(dto.getTestItemName());
        builder.add(dto.getEnName());
        builder.add(dto.getSampleTypeName());
        builder.add(dto.getExamMethodName());
        builder.add(dto.getGroupName());
        builder.add(enable);
        if (Objects.isNull(testReportItemDto)) {
            builder.add("");
            builder.add("");
        } else {
            builder.add(testReportItemDto.getReportItemCode());
            builder.add(testReportItemDto.getReportItemName());
        }
        return builder;
    }


    /**
     * 获取机构对应的检验项目列表
     */
    @GetMapping("/org-items")
    public List<TestItemVo>
    orgItems(@RequestParam(value = "includeReportItems", required = false) Boolean includeReportItems,
             @RequestParam(value = "needPackage", required = false) Boolean needPackage) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final List<TestItemDto> testItems = testItemService.selectByOrgId(user.getOrgId());

        final Collection<Long> testItemIds =
                testItems.stream().map(TestItemDto::getTestItemId).collect(Collectors.toSet());
        Map<Long, List<ReportItemDto>> reportItemMap;
        if (BooleanUtils.isTrue(includeReportItems)) {
            reportItemMap = reportItemService.selectByTestItemIds(testItemIds).stream()
                    .collect(Collectors.groupingBy(ReportItemDto::getTestItemId));
        } else {
            reportItemMap = Collections.emptyMap();
        }

        List<TestItemVo> testItemVos = testItems.stream().map(m -> {
            final TestItemVo vo = JSON.parseObject(JSON.toJSONString(m), TestItemVo.class);

            // 如果需要包含检验项目
            if (BooleanUtils.isTrue(includeReportItems)) {
                Optional.ofNullable(reportItemMap.get(vo.getTestItemId())).ifPresent(f -> {
                    final List<ReportItemVo> reportItems =
                            f.stream().map(mItem -> JSON.parseObject(JSON.toJSONString(mItem), ReportItemVo.class))
                                    .collect(Collectors.toList());
                    vo.setReportItems(reportItems);
                });

            }
            return vo;
        }).collect(Collectors.toList());

        if (BooleanUtils.isTrue(needPackage)) {
            //这里获取组套信息
            List<PackageItemDto> PackageItemDtos = packageItemService.selectAllByOrgIdAndType(user.getOrgId(), PackageTypeEnum.SAMPLE_INPUT.getCode());
            if (!CollectionUtils.isEmpty(PackageItemDtos)) {
                //筛选出来检验项目和报告项目 赋值进去
                Map<Long, List<PackageItemDto>> packageMap = PackageItemDtos.stream()
                        .collect(Collectors.groupingBy(PackageItemDto::getPackageId));
                List<TestItemVo> packVos = new ArrayList<>();
                for (Long packId : packageMap.keySet()) {
                    TestItemVo packItemVo = new TestItemVo();
                    packItemVo.setPackage(true);

                    List<PackageItemDto> packageItemDtoList = packageMap.get(packId);
                    //这里赋值套餐名称给前端
                    packItemVo.setTestItemName(packageItemDtoList.get(0).getPackageName());
                    //专业组列表填充的是送检机构名称
                    packItemVo.setGroupName(packageItemDtoList.get(0).getHspOrgName());
                    packItemVo.setHspOrgId(packageItemDtoList.get(0).getHspOrgId());
                    packItemVo.setPackageName(packageItemDtoList.get(0).getPackageName());
                    //初始化子项目相信
                    List<TestItemVo> childItem = new ArrayList<>();
                    for (PackageItemDto itemDto:packageItemDtoList){
                        for (TestItemVo testItemVo:testItemVos){
                            if (itemDto.getTestItemId().equals(testItemVo.getTestItemId())){
                                //这里需要重新创建对象，不能直接改原始对象的值
                                TestItemVo cpItem = new TestItemVo();
                                BeanUtils.copyProperties(testItemVo,cpItem);
                                cpItem.setPackageId(packId);
                                cpItem.setPackageName(packItemVo.getPackageName());
                                childItem.add(cpItem);
                                break;
                            }
                        }
                    }
                    packItemVo.setPackageItems(childItem);
                    packVos.add(packItemVo);
                }

                testItemVos.addAll(0, packVos);
            }
        }

        return testItemVos;
    }

    /**
     * 删除检验项目
     */
    @DeleteMapping("/delete")
    public Object delete(@RequestBody Set<Long> testItemIds) {
        if (CollectionUtils.isEmpty(testItemIds)) {
            return Collections.emptyMap();
        }

        for (Long testItemId : testItemIds) {
            // 判断是否关联了仪器专业小组下的仪器
            final List<InstrumentGroupTestItemDto> instrumentGroupTestItems =
                    instrumentGroupTestItemService.selectByTestItemId(testItemId);
            if (CollectionUtils.isNotEmpty(instrumentGroupTestItems)) {
                final InstrumentGroupTestItemDto instrumentGroupTestItem = instrumentGroupTestItems.iterator().next();
                final InstrumentGroupDto instrumentGroup =
                        instrumentGroupService.selectByInstrumentGroupId(instrumentGroupTestItem.getInstrumentGroupId());
                if (Objects.nonNull(instrumentGroup)) {
                    throw new IllegalArgumentException(String.format("专业小组 [%s] 关联了 [%s] 项目，无法删除",
                            instrumentGroup.getInstrumentGroupName(), instrumentGroupTestItem.getTestItemName()));
                } else {
                    throw new IllegalArgumentException(
                            String.format("专业小组关联了 [%s] 项目，无法删除", instrumentGroupTestItem.getTestItemName()));
                }
            }
        }

        final List<TestItemDto> testItems = testItemService.selectByTestItemIds(testItemIds);
        if (CollectionUtils.isEmpty(testItems)) {
            throw new IllegalArgumentException("检验项目不存在");
        }
        // 删除检验项目
        testItemService.deleteByTestItemIds(testItemIds);

        // 删除报告项目
        reportItemService.deleteByTestItemIds(testItemIds);

        // 日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.TEST_ITEM.getDesc())
                        .setContent(String.format("删除检验项目 [%s]",
                                testItems.stream().map(TestItemDto::getTestItemName).collect(Collectors.joining(","))))
                        .toJSONString());

        return Collections.emptyMap();
    }

}
