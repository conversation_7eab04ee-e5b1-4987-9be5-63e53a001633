package com.labway.lims.base.service;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.compare.dto.SampleTypeInfoDTO;
import com.labway.business.center.compare.service.TestItemPublishService;
import com.labway.business.center.mdm.api.reagent.dto.SysDictDto;
import com.labway.business.center.mdm.api.reagent.service.SysDictDubboService;
import com.labway.business.center.mdm.api.user.request.QueryDictRequest;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.mapper.TbDictItemMapper;
import com.labway.lims.base.mapstruct.DictItemConverter;
import com.labway.lims.base.model.TbDictItem;
import com.swak.frame.dto.Response;
import com.swak.frame.enums.BasicErrCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 字典服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "dict")
public class DictServiceImpl implements DictService {

    @Resource
    private TestItemPublishService testItemPublishService;

    @Resource
    private TbDictItemMapper tbDictItemMapper;

    @Resource
    private SnowflakeService snowflakeService;

    @Resource
    private DictItemConverter dictItemConverter;

    @Resource
    private SysDictDubboService sysDictDubboService;


    @Override
    @Cacheable(key = "'selectByDictType' + #dictType")
    public List<DictItemDto> selectByDictType(String dictType) {
        if (StringUtils.isBlank(dictType)) {
            return Collections.emptyList();
        }
        return selectByDictTypes(List.of(dictType));
    }

    @Override
    public List<DictItemDto> selectByDictTypes(Collection<String> dictTypes) {
        if (CollectionUtils.isEmpty(dictTypes)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbDictItem> eq =
            Wrappers.lambdaQuery(TbDictItem.class).in(TbDictItem::getDictType, dictTypes)
                .eq(TbDictItem::getOrgId, LoginUserHandler.get().getOrgId()).orderByDesc(TbDictItem::getDictId);

        return JSON.parseArray(JSON.toJSONString(tbDictItemMapper.selectList(eq)), DictItemDto.class);
    }

    @CacheEvict(allEntries = true)
    @Override
    public long addDictItem(DictItemDto dictItem) {
        if (Objects.isNull(dictItem)) {
            return 0L;
        }
        final LoginUserHandler.User user = LoginUserHandler.get();

        final String dictName = dictItem.getDictName();
        final String dictType = dictItem.getDictType();

        List<DictItemDto> dictItems = selectByDictType(dictType);
        if (BooleanUtils
            .isTrue(dictItems.stream().map(DictItemDto::getDictName).anyMatch(p -> Objects.equals(p, dictName)))) {
            throw new IllegalArgumentException(String.format("字典名称 [%s] 已存在", dictName));
        }

        if (Objects.nonNull(selectByDictCode(dictItem.getDictCode()))) {
            throw new IllegalStateException(String.format("字典编码 [%s] 已存在", dictItem.getDictCode()));
        }

        final Date now = new Date();

        final TbDictItem object = JSON.parseObject(JSON.toJSONString(dictItem), TbDictItem.class);
        object.setDictId(snowflakeService.genId());
        object.setCreateDate(now);
        object.setCreatorName(user.getNickname());
        object.setCreatorId(user.getUserId());
        object.setUpdateDate(now);
        object.setUpdaterName(user.getNickname());
        object.setUpdaterId(user.getUserId());
        object.setOrgId(user.getOrgId());
        object.setExtraParam1(StringUtils.EMPTY);
        object.setExtraParam2(StringUtils.EMPTY);
        object.setExtraParam3(StringUtils.EMPTY);
        object.setEnable(ObjectUtils.defaultIfNull(dictItem.getEnable(), YesOrNoEnum.YES.getCode()));
        object.setDictRemark(ObjectUtils.defaultIfNull(dictItem.getDictRemark(), StringUtils.EMPTY));
        object.setDictKeyshort(ObjectUtils.defaultIfNull(dictItem.getDictKeyshort(), StringUtils.EMPTY));
        object.setDictPinyin(ObjectUtils.defaultIfNull(dictItem.getDictPinyin(), PinyinUtil.getPinyin(dictName)));
        object.setIsDelete(YesOrNoEnum.NO.getCode());
        if (tbDictItemMapper.insert(object) < 1) {
            throw new IllegalStateException("新增字典失败");
        }

        log.info("用户 [{}] 新增类型 [{}] 字典 [{}] ", user.getNickname(), dictType, dictName);

        return object.getDictId();
    }

    @CacheEvict(allEntries = true)
    @Override
    public void updateDictItem(DictItemDto dictItem) {
        if (Objects.isNull(dictItem) || Objects.isNull(dictItem.getDictId())) {
            return;
        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        final DictItemDto dictItemDb = selectByDictId(dictItem.getDictId());
        if (Objects.isNull(dictItemDb)) {
            throw new IllegalStateException("字典不存在");
        }

        final String dictName = dictItem.getDictName();

        final String dictType = dictItemDb.getDictType();

        final List<DictItemDto> dictItems = selectByDictType(dictType).stream()
            .filter(
                p -> Objects.equals(p.getDictName(), dictName) && !Objects.equals(p.getDictId(), dictItem.getDictId()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dictItems)) {
            throw new IllegalArgumentException(String.format("字典名称 [%s] 已存在", dictName));
        }

        final TbDictItem update = JSON.parseObject(JSON.toJSONString(dictItem), TbDictItem.class);
        update.setDictCode(null);
        update.setCreateDate(null);
        update.setCreatorId(null);
        update.setCreatorName(null);

        update.setUpdaterId(user.getUserId());
        update.setUpdaterName(user.getNickname());
        update.setUpdateDate(new Date());

        log.info("用户 [{}] 修改字典 [{}] 结果 [{}]", user.getNickname(), JSON.toJSONString(dictItem),
            tbDictItemMapper.updateById(update));

    }

    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByDictId(Long dictId) {
        log.info("用户 [{}] 删除字典 [{}] 结果 [{}]", LoginUserHandler.get().getNickname(), dictId,
            tbDictItemMapper.deleteById(dictId));
    }

    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByDictIds(List<Long> dictIds) {
        for (final Long dictId : dictIds) {
            deleteByDictId(dictId);
        }
    }

    @Cacheable(key = "'selectByDictCode' + #dictCode")
    @Override
    public DictItemDto selectByDictCode(String dictCode) {
        if (StringUtils.isBlank(dictCode)) {
            return null;
        }
        final LambdaQueryWrapper<TbDictItem> last =
            Wrappers.lambdaQuery(TbDictItem.class).eq(TbDictItem::getDictCode, dictCode)
                .eq(TbDictItem::getOrgId, LoginUserHandler.get().getOrgId()).last("limit 1");
        return JSON.parseObject(JSON.toJSONString(tbDictItemMapper.selectOne(last)), DictItemDto.class);
    }

    @Cacheable(key = "'selectByDictId' + #dictId")
    @Override
    public DictItemDto selectByDictId(long dictId) {
        return JSON.parseObject(JSON.toJSONString(tbDictItemMapper.selectById(dictId)), DictItemDto.class);
    }

    @Override
    public List<DictItemDto> selectByDictIds(Collection<Long> dictIds) {
        if (CollectionUtils.isEmpty(dictIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbDictItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbDictItem::getDictId, dictIds);
        return dictItemConverter.dictItemDtoListFromTbObj(tbDictItemMapper.selectList(queryWrapper));
    }

    @Override
    @Cacheable(key = "'selectAll'")
    public List<DictItemDto> selectAll() {
        return tbDictItemMapper
            .selectList(
                Wrappers.lambdaQuery(TbDictItem.class).eq(TbDictItem::getOrgId, LoginUserHandler.get().getOrgId()))
            .stream().map(p -> JSON.parseObject(JSON.toJSONString(p), DictItemDto.class)).collect(Collectors.toList());
    }

    @Override
    public List<DictItemDto> selectAllSampleType() {
        try {
            final Response<List<SampleTypeInfoDTO>> response = testItemPublishService.queryAllSampleInfo();
            if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
                return response.getData().stream().map(e -> {
                    final DictItemDto v = new DictItemDto();
                    v.setDictName(e.getSampleTypeName());
                    v.setDictCode(e.getSampleTypeCode());
                    v.setEnable(YesOrNoEnum.YES.getCode());
                    return v;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取业务中台字典样本类型失败", e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取业务中台字典样本类型--包括未启用的
     * @return
     */
    @Override
    public List<DictItemDto> queryAllSampleType() {
        try {
            final Response<List<SampleTypeInfoDTO>> response = testItemPublishService.queryAllSampleType();
            if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
                return response.getData().stream().map(e -> {
                    final DictItemDto v = new DictItemDto();
                    v.setDictName(e.getSampleTypeName());
                    v.setDictCode(e.getSampleTypeCode());
                    v.setEnable(e.getStatus());
                    return v;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取业务中台字典样本类型失败", e);
        }
        return Collections.emptyList();
    }


    @Override
    public List<DictItemDto> selectAbnormalReasonsFromBusinessCenter() {
        QueryDictRequest request = new QueryDictRequest();
        request.setEnabled(NumberUtils.INTEGER_ONE);
        Response<List<SysDictDto>> response;
        try {
            response = sysDictDubboService.listAllEReason(request);
        } catch (Exception e) {
            throw new LimsException("业务中台-获取异常原因返回错误", e);
        }

        if (Objects.isNull(response)) {
            throw new LimsException("业务中台-获取异常原因返回结果为空");
        }

        if (!Objects.equals(response.getCode(), BasicErrCode.SUCCESS.getCode())) {
            throw new LimsException(String.format("业务中台-获取异常原因返回错误, 错误信息: [%s]", response.getMsg()));
        }
        List<SysDictDto> dictDtos = response.getData();

        return dictDtos.stream().map(e -> {
            DictItemDto dictItemDto = new DictItemDto();
            dictItemDto.setDictCode(e.getDictCode());
            dictItemDto.setDictName(e.getDictValue());
            dictItemDto.setCreateDate(e.getCreateTime());
            dictItemDto.setUpdateDate(e.getUpdateTime());
            return dictItemDto;
        }).sorted(Comparator.comparing(DictItemDto::getCreateDate)).collect(Collectors.toList());
    }

}
