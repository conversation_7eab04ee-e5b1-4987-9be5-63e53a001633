package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.mapper.TbSystemParamMapper;
import com.labway.lims.base.model.TbSystemParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.aop.framework.AopContext;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/21 13:49
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "system-param")
public class SystemParamServiceImpl implements SystemParamService {
    @Resource
    private SnowflakeService snowflakeService;

    @Resource
    private TbSystemParamMapper tbSystemParamMapper;

    @Override
    @CacheEvict(allEntries = true)
    public long add(SystemParamDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final TbSystemParam systemParam = JSON.parseObject(JSON.toJSONString(dto), TbSystemParam.class);
        systemParam.setParamId(ObjectUtils.defaultIfNull(dto.getParamId(), snowflakeService.genId()));
        systemParam.setCreatorId(user.getUserId());
        systemParam.setCreatorName(user.getNickname());
        systemParam.setOrgId(user.getOrgId());
        systemParam.setUpdaterId(user.getUserId());
        systemParam.setUpdaterName(user.getNickname());
        systemParam.setUpdateDate(new Date());
        systemParam.setCreateDate(new Date());
        systemParam.setExtraParam1(ObjectUtils.defaultIfNull(dto.getExtraParam1(), ""));
        systemParam.setExtraParam2(ObjectUtils.defaultIfNull(dto.getExtraParam2(), ""));
        systemParam.setExtraParam3(ObjectUtils.defaultIfNull(dto.getExtraParam3(), ""));

        if (tbSystemParamMapper.insert(systemParam) < 1) {
            throw new IllegalStateException("添加系统参数失败");
        }

        log.info("用户 [{}] 添加系统参数成功 [{}]", user.getNickname(), JSON.toJSONString(dto));

        return systemParam.getParamId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByParamId(SystemParamDto dto) {
        final TbSystemParam systemParam = JSON.parseObject(JSON.toJSONString(dto), TbSystemParam.class);
        final LoginUserHandler.User user = LoginUserHandler.get();
        systemParam.setUpdaterId(user.getUserId());
        systemParam.setUpdaterName(user.getNickname());

        if (tbSystemParamMapper.updateById(systemParam) < 1) {
            return false;
        }
        log.info("用户 [{}] 更新系统参数成功 [{}]", user.getNickname(), JSON.toJSONString(dto));
        return true;
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByParamId(long paramId) {
        tbSystemParamMapper.deleteById(paramId);
    }

    @Override
    @Nullable
    @Cacheable(key = "'selectByParamId:' + #paramId")
    public SystemParamDto selectByParamId(long paramId) {
        final TbSystemParam tbSystemParam = tbSystemParamMapper.selectById(paramId);
        return convert(tbSystemParam);
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByParamName:' + #paramName + ',' + #orgId")
    public SystemParamDto selectByParamName(String paramName, long orgId) {
        if (StringUtils.isBlank(paramName)) {
            return null;
        }

        final TbSystemParam p = tbSystemParamMapper.selectOne(new LambdaQueryWrapper<TbSystemParam>()
                .eq(TbSystemParam::getOrgId, orgId)
                .eq(TbSystemParam::getParamName, paramName)
                .eq(TbSystemParam::getEnable, YesOrNoEnum.YES.getCode())
                .last("limit 1")
        );

        return convert(p);
    }

    @Override
    @Cacheable(key = "'selectByOrgId:'+#orgId")
    public List<SystemParamDto> selectByOrgId(long orgId) {
        final List<TbSystemParam> paramList = tbSystemParamMapper.selectList(new LambdaQueryWrapper<TbSystemParam>()
                .eq(TbSystemParam::getOrgId, orgId)
                .orderByDesc(TbSystemParam::getCreateDate));
        if (CollectionUtils.isEmpty(paramList)) {
            return Collections.emptyList();
        }

        return paramList.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public SystemParamDto selectByParamMetricAndParamNameAndCreateId(String paramMetric, String paramName, Long creatorId) {
        LambdaQueryWrapper<TbSystemParam> wrapper = Wrappers.lambdaQuery(TbSystemParam.class)
                .eq(TbSystemParam::getParamMetric, paramMetric)
                .eq(TbSystemParam::getParamName, paramName)
                .eq(TbSystemParam::getCreatorId, creatorId);
        TbSystemParam tbSystemParam = tbSystemParamMapper.selectOne(wrapper);
        return convert(tbSystemParam);
    }

    @Override
    @Nonnull
    public JSONObject selectAsJsonByParamName(String paramName, long orgId) {
        SystemParamDto systemParamDto = ((SystemParamService) AopContext.currentProxy()).selectByParamName(paramName, orgId);

        String paramValue = null;
        try {
            if (Objects.nonNull(systemParamDto) &&
                    StringUtils.isNotBlank(paramValue = systemParamDto.getParamValue())) {
                return JSON.parseObject(systemParamDto.getParamValue());
            }
        } catch (Exception ignored) {
            log.error("查询参数转换异常 参数名 [{}] 参数值 [{}]", paramName, paramValue);
        }

        return new JSONObject();
    }

    private SystemParamDto convert(TbSystemParam tbSystemParam) {
        if (Objects.isNull(tbSystemParam)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(tbSystemParam), SystemParamDto.class);
    }

}
