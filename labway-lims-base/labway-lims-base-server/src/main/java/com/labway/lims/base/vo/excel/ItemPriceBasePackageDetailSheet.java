package com.labway.lims.base.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * ItemPriceBasePackageDetailSheet
 * 项目价格基础包检验项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/29 10:58
 */
@Getter
@Setter
public class ItemPriceBasePackageDetailSheet implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer index;

    /**
     * 项目编码
     */
    @ExcelProperty(index = 0)
    private String testItemCode;

    /**
     * 检验项目编码
     */
    @ExcelProperty(index = 1)
    private String testItemName;

    /**
     * 收费价格
     */
    @ExcelProperty(index = 2)
    private String feePrice;

}
