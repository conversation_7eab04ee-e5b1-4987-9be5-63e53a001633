package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.PackageDto;
import com.labway.lims.base.api.service.PackageService;
import com.labway.lims.base.mapper.TbPackageMapper;
import com.labway.lims.base.mapstruct.PackageConverter;
import com.labway.lims.base.model.TbPackage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 体检团体套餐 Service impl
 * 
 * <AUTHOR>
 * @since 2023/3/28 16:50
 */
@Slf4j
@DubboService
public class PackageServiceImpl implements PackageService {
    @Resource
    private TbPackageMapper tbPackageMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private PackageConverter packageConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addPackage(PackageDto packageDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbPackage target = new TbPackage();
        BeanUtils.copyProperties(packageDto,target);

        target.setPackageId(snowflakeService.genId());
        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbPackageMapper.insert(target) < 1) {
            throw new LimsException("添加套餐失败");
        }
        log.info("用户 [{}] 新增套餐[{}]成功", loginUser.getNickname(), JSON.toJSONString(target));

        return target.getPackageId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPackageIds(Collection<Long> physicalGroupPackageIds) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除套餐成功 [{}] 结果 [{}]", loginUser.getNickname(), physicalGroupPackageIds,
            tbPackageMapper.deleteBatchIds(physicalGroupPackageIds) > 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByPackageId(PackageDto packageDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbPackage target = new TbPackage();
        BeanUtils.copyProperties(packageDto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbPackageMapper.updateById(target) < 1) {
            throw new LimsException("修改体检团体套餐失败");
        }

        log.info("用户 [{}] 修改套餐成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    public List<PackageDto> selectByOrgId(long orgId) {
        if (orgId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbPackage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbPackage::getOrgId, orgId);
        queryWrapper.eq(TbPackage::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbPackage::getCreateDate);
        return packageConverter
            .physicalGroupPackageDtoListFromTbObj(tbPackageMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public PackageDto selectByPackageName(String physicalGroupPackageName, long orgId) {
        if (StringUtils.isBlank(physicalGroupPackageName)) {
            return null;
        }
        LambdaQueryWrapper<TbPackage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbPackage::getPackageName, physicalGroupPackageName);
        queryWrapper.eq(TbPackage::getOrgId, orgId);
        queryWrapper.eq(TbPackage::getIsDelete, YesOrNoEnum.NO.getCode()).last("limit 1");

        return packageConverter
            .physicalGroupPackageDtoFromTbObj(tbPackageMapper.selectOne(queryWrapper));
    }

    @Nullable
    @Override
    public PackageDto selectByPackageId(long physicalGroupPackageId) {

        LambdaQueryWrapper<TbPackage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbPackage::getPackageId, physicalGroupPackageId);
        queryWrapper.eq(TbPackage::getIsDelete, YesOrNoEnum.NO.getCode());
        return packageConverter
            .physicalGroupPackageDtoFromTbObj(tbPackageMapper.selectOne(queryWrapper.last("limit 1")));
    }

    @Override
    public List<PackageDto> selectByPackageIds(Collection<Long> physicalGroupPackageIds) {
        if (CollectionUtils.isEmpty(physicalGroupPackageIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbPackage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbPackage::getPackageId, physicalGroupPackageIds);
        queryWrapper.eq(TbPackage::getIsDelete, YesOrNoEnum.NO.getCode());

        return packageConverter
            .physicalGroupPackageDtoListFromTbObj(tbPackageMapper.selectList(queryWrapper));
    }

    @Override
    public List<PackageDto> selectByGroupIds(Collection<Long> physicalGroupIds) {
        if (CollectionUtils.isEmpty(physicalGroupIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbPackage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbPackage::getPhysicalGroupId, physicalGroupIds);
        queryWrapper.eq(TbPackage::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbPackage::getCreateDate);
        return packageConverter
            .physicalGroupPackageDtoListFromTbObj(tbPackageMapper.selectList(queryWrapper));
    }

}
