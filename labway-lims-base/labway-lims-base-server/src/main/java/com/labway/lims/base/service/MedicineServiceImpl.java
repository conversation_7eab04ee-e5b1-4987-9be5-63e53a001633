package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.GermGenusDto;
import com.labway.lims.base.api.dto.MedicineDto;
import com.labway.lims.base.api.dto.MedicineGermRelationDto;
import com.labway.lims.base.api.service.MedicineGermRelationService;
import com.labway.lims.base.api.service.MedicineService;
import com.labway.lims.base.mapper.TbMedicineMapper;
import com.labway.lims.base.mapstruct.MedicineConverter;
import com.labway.lims.base.model.TbMedicine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 药物 Service impl
 *
 * <AUTHOR>
 * @since 2023/3/21 16:25
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "medicine")
public class MedicineServiceImpl implements MedicineService {
    @Resource
    private TbMedicineMapper tbMedicineMapper;
    @Resource
    private MedicineService medicineService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private MedicineGermRelationService medicineGermRelationService;
    @Resource
    private MedicineConverter medicineConverter;

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public long addMedicine(MedicineDto medicineDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbMedicine target = new TbMedicine();

        target.setMedicineCode(medicineDto.getMedicineCode());
        target.setMedicineName(medicineDto.getMedicineName());
        target.setMedicineEn(ObjectUtils.defaultIfNull(medicineDto.getMedicineEn(), ""));
        target.setWhonetMedicineCode(medicineDto.getWhonetMedicineCode());
        target.setEnable(medicineDto.getEnable());

        target.setMedicineId(ObjectUtils.defaultIfNull(medicineDto.getMedicineId(), snowflakeService.genId()));
        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbMedicineMapper.insert(target) < 1) {
            throw new LimsException("添加药物失败");
        }

        log.info("用户 [{}] 新增药物[{}]成功", loginUser.getNickname(), JSON.toJSONString(target));

        return target.getMedicineId();
    }

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void deleteByMedicineIds(Collection<Long> medicineIds) {
        if (CollectionUtils.isEmpty(medicineIds)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除药物成功 [{}] 结果 [{}]", loginUser.getNickname(), medicineIds,
            tbMedicineMapper.deleteBatchIds(medicineIds) > 0);

        // 删除 药物 细菌菌属 关联
        medicineGermRelationService.deleteByMedicineIds(medicineIds);

    }

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void updateByMedicineId(MedicineDto medicineDto) {

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbMedicine medicine = new TbMedicine();
        BeanUtils.copyProperties(medicineDto, medicine);

        medicine.setUpdaterId(loginUser.getUserId());
        medicine.setUpdaterName(loginUser.getNickname());
        medicine.setUpdateDate(new Date());

        if (tbMedicineMapper.updateById(medicine) < 1) {
            throw new LimsException("修改药物失败");
        }

        log.info("用户 [{}] 修改药物成功 [{}]", loginUser.getNickname(), JSON.toJSONString(medicine));

    }

    @Override
    @Cacheable(key = "'selectByOrgId:' + #orgId")
    public List<MedicineDto> selectByOrgId(long orgId) {
        LambdaQueryWrapper<TbMedicine> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMedicine::getOrgId, orgId);
        queryWrapper.eq(TbMedicine::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbMedicine::getCreateDate);
        return medicineConverter.medicineDtoListTbObj(tbMedicineMapper.selectList(queryWrapper));
    }

    @Override
    public List<MedicineDto> selectByGermGenusIds(Collection<Long> germGenusIds) {
        return germGenusIds.stream().map(medicineService::selectByGermGenusId).flatMap(Collection::stream)
            .filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByGermGenusId:' + #germGenusId")
    public List<MedicineDto> selectByGermGenusId(long germGenusId) {

        final List<MedicineGermRelationDto> relationDtos =
            medicineGermRelationService.selectByGermGenusIds(List.of(germGenusId));
        Set<Long> medicineIds =
            relationDtos.stream().map(MedicineGermRelationDto::getMedicineId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(medicineIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbMedicine> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMedicine::getMedicineId, medicineIds);
        queryWrapper.eq(TbMedicine::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.eq(TbMedicine::getEnable, YesOrNoEnum.YES.getCode());
        queryWrapper.orderByDesc(TbMedicine::getCreateDate);

        return medicineConverter.medicineDtoListTbObj(tbMedicineMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByMedicineId:' + #medicineId")
    public MedicineDto selectByMedicineId(long medicineId) {
        return medicineConverter.medicineDtoTbObj(tbMedicineMapper.selectById(medicineId));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByMedicineName:' + #medicineName + ',' + #orgId")
    public MedicineDto selectByMedicineName(String medicineName, long orgId) {
        if (StringUtils.isBlank(medicineName)) {
            return null;
        }
        LambdaQueryWrapper<TbMedicine> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMedicine::getMedicineName, medicineName);
        queryWrapper.eq(TbMedicine::getOrgId, orgId);
        queryWrapper.eq(TbMedicine::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return medicineConverter.medicineDtoTbObj(tbMedicineMapper.selectOne(queryWrapper));

    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByMedicineCode:' + #medicineCode + ',' + #orgId")
    public MedicineDto selectByMedicineCode(String medicineCode, long orgId) {
        if (StringUtils.isBlank(medicineCode)) {
            return null;
        }
        LambdaQueryWrapper<TbMedicine> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMedicine::getMedicineCode, medicineCode);
        queryWrapper.eq(TbMedicine::getOrgId, orgId);
        queryWrapper.eq(TbMedicine::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return medicineConverter.medicineDtoTbObj(tbMedicineMapper.selectOne(queryWrapper));
    }

    @Override
    public List<MedicineDto> selectByMedicineIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMedicine> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMedicine::getMedicineId, ids);
        queryWrapper.eq(TbMedicine::getOrgId, LoginUserHandler.get().getOrgId());

        return medicineConverter.medicineDtoListTbObj(tbMedicineMapper.selectList(queryWrapper));
    }

	@Override
	public List<MedicineDto> selectByMedicineIdsForCache(Collection<Long> ids) {
		List<MedicineDto> result = new ArrayList<>();
		for (Long id : ids) {
			MedicineDto medicineDto = ((MedicineService) AopContext.currentProxy()).selectByMedicineId(id);
			if (Objects.nonNull(medicineDto)) {
				result.add(medicineDto);
			}
		}
		return result;
	}

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void medicineUpdate(MedicineDto medicineDto, List<GermGenusDto> needAddRelationGenusList,
        List<Long> needDeleteRelationIdList) {

        // 更新药物
        this.updateByMedicineId(medicineDto);

        // 添加药物细菌菌属关联
        needAddRelationGenusList.forEach(item -> {
            MedicineGermRelationDto relationDto = new MedicineGermRelationDto();
            relationDto.setGermGenusId(item.getGermGenusId());
            relationDto.setMedicineId(medicineDto.getMedicineId());
            medicineGermRelationService.addMedicineGermRelationDto(relationDto);
        });
        medicineGermRelationService.deleteByRelationIds(needDeleteRelationIdList);
    }

}
