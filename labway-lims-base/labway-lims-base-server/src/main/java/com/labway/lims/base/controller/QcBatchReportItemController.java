package com.labway.lims.base.controller;

import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.QcBatchDto;
import com.labway.lims.base.api.dto.QcBatchReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.QcBatchReportItemService;
import com.labway.lims.base.api.service.QcBatchService;
import com.labway.lims.base.vo.QcBatchReportItemAddRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 质控批号报告项目 API
 * 
 * <AUTHOR>
 * @since 2023/7/4 14:39
 */
@Slf4j
@RestController
@RequestMapping("/qc-batch-report-item")
public class QcBatchReportItemController extends BaseController {

    @Resource
    private QcBatchService qcBatchService;
    @Resource
    private QcBatchReportItemService qcBatchReportItemService;
    @Resource
    private InstrumentReportItemService instrumentReportItemService;
    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private InstrumentService instrumentService;

    /**
     * 添加 质控批号报告项目
     */
    @PostMapping("/add")
    public Object qcBatchReportItemAdd(@RequestBody QcBatchReportItemAddRequestVo vo) {
        if (Objects.isNull(vo.getQcBatchId())) {
            throw new LimsException("未确定质控批号");
        }
        if (CollectionUtils.isEmpty(vo.getReportItemCodes())) {
            throw new LimsException("未确定添加的报告项目");
        }
        QcBatchDto qcBatchDto = qcBatchService.selectByQcBatchId(vo.getQcBatchId());
        if (Objects.isNull(qcBatchDto)) {
            throw new LimsException("对应质控批号不存在");
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        final InstrumentDto instrument =
            instrumentService.selectByOrgIdAndInstrumentCode(user.getOrgId(), qcBatchDto.getInstrumentCode());
        if (Objects.isNull(instrument)) {
            throw new IllegalArgumentException("质控批号对应仪器不存在");
        }
        // 对应报告项目
        List<InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService
            .selectByInstrumentIdAndReportItemCodes(instrument.getInstrumentId(), vo.getReportItemCodes());
        Set<String> selectReportItemCodes =
            instrumentReportItems.stream().filter(obj -> Objects.equals(obj.getIsQc(), YesOrNoEnum.YES.getCode()))
                .map(InstrumentReportItemDto::getReportItemCode).collect(Collectors.toSet());
        if (vo.getReportItemCodes().stream().anyMatch(x -> !selectReportItemCodes.contains(x))) {
            throw new LimsException("存在无效报告项目");
        }
        // 现有质控报告项目
        List<QcBatchReportItemDto> qcBatchReportItemDtos =
            qcBatchReportItemService.selectByQcBatchId(qcBatchDto.getQcBatchId());
        Set<String> reportItemCodeList =
            qcBatchReportItemDtos.stream().map(QcBatchReportItemDto::getReportItemCode).collect(Collectors.toSet());
        List<InstrumentReportItemDto> needAddReportItem = instrumentReportItems.stream()
            .filter(obj -> !reportItemCodeList.contains(obj.getReportItemCode())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needAddReportItem)) {
            return Collections.emptyMap();
        }
        // 检查报告项目是否 可以增加
        Map<String, String> reportItemCodes = needAddReportItem.stream().collect(
            Collectors.toMap(InstrumentReportItemDto::getReportItemCode, InstrumentReportItemDto::getReportItemName));
        qcBatchReportItemService.checkAddQcBatchReportItemDtos(qcBatchDto, reportItemCodes);


        LinkedList<Long> genIds = snowflakeService.genIds(needAddReportItem.size());
        List<QcBatchReportItemDto> targetList = Lists.newArrayListWithExpectedSize(needAddReportItem.size());
        Date date = new Date();
        for (InstrumentReportItemDto instrumentReportItemDto : needAddReportItem) {
            QcBatchReportItemDto dto = new QcBatchReportItemDto();
            dto.setBatchReportItemId(genIds.pop());
            dto.setQcBatchId(qcBatchDto.getQcBatchId());
            dto.setQcBatch(qcBatchDto.getQcBatch());
            dto.setReportItemCode(instrumentReportItemDto.getReportItemCode());
            dto.setReportItemName(instrumentReportItemDto.getReportItemName());
            dto.setReportItemUnit(StringUtils.defaultString(instrumentReportItemDto.getReportItemUnitName()));
            dto.setIsDelete(YesOrNoEnum.NO.getCode());
            dto.setOrgId(user.getOrgId());
            dto.setOrgName(user.getOrgName());
            dto.setCreateDate(date);
            dto.setCreatorName(user.getNickname());
            dto.setCreatorId(user.getUserId());
            dto.setUpdateDate(date);
            dto.setUpdaterName(user.getNickname());
            dto.setUpdaterId(user.getUserId());
            targetList.add(dto);
        }
        qcBatchReportItemService.addQcBatchReportItemDtos(targetList);

        return Collections.emptyMap();
    }

    /**
     * 删除 质控批号报告项目
     */
    @PostMapping("/delete")
    public Object qcBatchReportItemDelete(@RequestBody Set<Long> batchReportItemIds) {
        if (CollectionUtils.isEmpty(batchReportItemIds)) {
            return Collections.emptyMap();
        }
        qcBatchReportItemService.deleteByBatchReportItemIds(batchReportItemIds);

        return Collections.emptyMap();
    }

    /**
     * 查询 质控批号报告项目
     */
    @PostMapping("/select-by-qc-batch-id")
    public Object selectByQcBatchId(@RequestParam(value = "qcBatchId") long qcBatchId) {
        return qcBatchReportItemService.selectByQcBatchId(qcBatchId);
    }

}
