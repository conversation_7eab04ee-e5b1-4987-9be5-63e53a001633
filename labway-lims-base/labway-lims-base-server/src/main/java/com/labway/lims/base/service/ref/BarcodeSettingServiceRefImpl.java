package com.labway.lims.base.service.ref;

import com.labway.lims.base.api.dto.BarcodeSettingDto;
import com.labway.lims.base.api.service.BarcodeSettingService;
import com.labway.lims.base.api.service.ref.IBarcodeSettingServiceRef;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

@DubboService
@Slf4j
public class BarcodeSettingServiceRefImpl implements IBarcodeSettingServiceRef {

    @Resource
    private BarcodeSettingService barcodeSettingService;


    @Override
    public List<String> genBarcodes(String hspOrgCode, int number) {
        return barcodeSettingService.genBarcodes(hspOrgCode, number, BarcodeSettingDto.BARCODE_TYPE);
    }

    @Override
    public List<String> genMasterBarcodes(String hspOrgCode, int number) {
        return barcodeSettingService.genBarcodes(hspOrgCode, number, BarcodeSettingDto.MASTER_BARCODE_TYPE);
    }


    @Override
    public BarcodeSettingDto selectByBarcode(String barcode, int barcodeType) {
        return barcodeSettingService.selectByBarcode(barcode,barcodeType);
    }

}
