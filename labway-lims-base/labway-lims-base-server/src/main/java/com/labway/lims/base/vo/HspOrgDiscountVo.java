package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户折扣信息
 */
@Getter
@Setter
public class HspOrgDiscountVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long discountId;

    /**
     * 基准包id
     */
    private Long packageId;
    /**
     * 基准包名称
     */
    private String packageName;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 送检类型编码
     */
    private String sendTypeCode;

    /**
     * 项目ID
     */
    private Long testItemId;

    /**
     * 项目编码
     */
    private String testItemCode;

    /**
     * 项目名称
     */
    private String testItemName;

    /**
     * 送检类型名称
     */
    private String sendType;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 财务专业组
     */
    private String financeGroup;

    /**
     * 折前单价
     */
    private BigDecimal originalPrice;

    /**
     * 折后单价
     */
    private BigDecimal discountPrice;

    /**
     * 折扣率
     */
    private BigDecimal discount;


}
