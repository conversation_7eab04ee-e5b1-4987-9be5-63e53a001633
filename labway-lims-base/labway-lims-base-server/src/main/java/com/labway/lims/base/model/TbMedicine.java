package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 药物
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_medicine")
public class TbMedicine implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 药物ID
     */
    @TableId
    private Long medicineId;
    /**
     * 药物编码
     */
    private String medicineCode;
    /**
     * 药物名称
     */
    private String medicineName;
    /**
     * 药物英文名
     */
    private String medicineEn;

    /**
     * whonet药物编码
     */
    private String whonetMedicineCode;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 是否启用(0未启用 1启用)
     * 
     * @see YesOrNoEnum
     */
    private Integer enable;
    /**
     * 1: 删除 0:未删
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人名称
     */
    private String updaterName;

}
