package com.labway.lims.base.vo;

import com.labway.lims.base.api.dto.UserDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 用户列表
 */
@Getter
@Setter
public class UserUsersVo extends UserDto {

    /**
     * 默认角色名称
     */
    private String roleName;


    /**
     * 默认角色ID
     */
    private Long roleId;

    /**
     * 拥有的角色ID
     */
    private List<Long> roleIds;


    /**
     * 默认组名称
     */
    private String groupName;


    /**
     * 默认组Id
     */
    private Long groupId;

    /**
     * 拥有的专业组ID
     */
    private List<Long> groupIds;
}
