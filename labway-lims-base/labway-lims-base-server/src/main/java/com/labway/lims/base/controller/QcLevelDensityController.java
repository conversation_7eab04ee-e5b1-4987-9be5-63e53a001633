package com.labway.lims.base.controller;

import cn.hutool.json.JSONUtil;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.QcAddRecordInfoDto;
import com.labway.lims.base.api.dto.QcSetRecordItemDto;
import com.labway.lims.base.api.dto.RecordCalculateDto;
import com.labway.lims.base.api.enums.QcErrorCode;
import com.labway.lims.base.api.enums.QcRuleEnum;
import com.labway.lims.base.api.service.QcLevelDensityService;
import com.labway.lims.base.mapstruct.QcLevelDensityConverter;
import com.labway.lims.base.utils.MyQCNumberUtils;
import com.labway.lims.base.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 质控水平浓度信息
 *
 * <AUTHOR>
 * @Date 2023/11/3 14:30
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/qcRecord")
public class QcLevelDensityController extends BaseController {

    @Resource
    private QcLevelDensityService qcLevelDensityService;
    @Resource
    private QcLevelDensityConverter qcLevelDensityConverter;

    /**
     * 获取规则枚举
     */
    @PostMapping("/queryRuleList")
    public Object queryRuleList() {
        List<CustomerDictVO> customerDictVOS = new ArrayList<>();
        for (QcRuleEnum ruleEnum : QcRuleEnum.values()) {
            if (StringUtils.isNotBlank(ruleEnum.getDes())) {
                CustomerDictVO dictVO = new CustomerDictVO();
                dictVO.setDictCode(ruleEnum.getValue());
                dictVO.setDictName(ruleEnum.getDes());
                customerDictVOS.add(dictVO);
            }
        }
        return customerDictVOS;
    }

    /**
     * 保存质控水平浓度记录信息
     */
    @PostMapping("/saveQcRecord")
    public Object saveQcRecord(@RequestBody QcAddRecordInfoVo recordInfoVo) {
        //1. 打印记录信息
        log.info("请求参数====》{}", JSONUtil.toJsonStr(recordInfoVo));
        //2. 关键数据判断
        this.validQcAddRecordInfoVoParam(recordInfoVo);
        //3. 数据类型转换: 此处有集合的类型转变：两者变量名需要保持一致
        QcAddRecordInfoDto qcAddRecordInfoDto = qcLevelDensityConverter.covertQcAddRecordInfoVoToQcAddSetRecordItemDto(recordInfoVo);
        //4. 执行业务
        qcLevelDensityService.saveQcRecord(qcAddRecordInfoDto);
        //5. 数据返回
        return Collections.emptyMap();
    }

    /**
     * 根据仪器报告项获取记录列表
     *
     * @param instrumentReportItemId 仪器报告项
     * @return
     */
    @PostMapping("/queryRecordList")
    public Object queryRecordList(Long instrumentReportItemId) {
        List<QcSetRecordItemDto> recordItemDtos = qcLevelDensityService.querySetRecordItemListReportItemId(instrumentReportItemId);
        List<QcRecordInfoVo> infoList = null;
        if (!CollectionUtils.isEmpty(recordItemDtos)) {
            infoList = convertRecordItem2VO(recordItemDtos);
            infoList = infoList.stream().sorted(Comparator.comparing(QcRecordInfoVo::getQcStartDate).reversed()).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(infoList)){
            return Collections.emptyMap();
        }
        return infoList;
    }

    /**
     * 删除记录数据: 根据质控设置批号
     */
    @PostMapping("/deleteRecord")
    public Object deleteQcBatch(@RequestParam String qcRecordBatch) {
        qcLevelDensityService.deleteInfoByRecordBatch(qcRecordBatch, true);
        return Collections.emptyMap();
    }
    /**
     * 计算记录属性
     */
    @PostMapping("/calculateRecord")
    public Object calculateRecord(@RequestBody RecordCalculateVo calculateVo) {
        RecordCalculateDto calculateDto = new RecordCalculateDto();
        BeanUtils.copyProperties(calculateVo, calculateDto);
        qcLevelDensityService.calculateSampleRecord(calculateDto);
        return Collections.emptyMap();
    }

    /**
     * 新增质控水平浓度信息校验
     *
     * @return
     */
    private void validQcAddRecordInfoVoParam(QcAddRecordInfoVo recordInfoVo) {
        //开始日期不能为空
        if (Objects.isNull(recordInfoVo.getQcStartDate())) {
            throw new IllegalStateException(QcErrorCode.QC100010.getMsg());
        }
        //结束日期不能为空
        if (Objects.isNull(recordInfoVo.getQcEndDate())) {
            throw new IllegalStateException(QcErrorCode.QC100011.getMsg());
        }
        // 开始日期不能低于结束日期
        if (recordInfoVo.getQcEndDate().isBefore(recordInfoVo.getQcStartDate())) {
            throw new IllegalStateException(QcErrorCode.QC100027.getMsg());
        }
        //规则不能为空
        if (StringUtils.isBlank(recordInfoVo.getQcRulesCollection())) {
            throw new IllegalStateException(QcErrorCode.QC100012.getMsg());
        }
        //仪器id不能为空
        if (Objects.isNull(recordInfoVo.getInstrumentId())) {
            throw new IllegalStateException(QcErrorCode.QC100008.getMsg());
        }

        if (StringUtils.isBlank(recordInfoVo.getInstrumentName())) {
            throw new IllegalStateException(QcErrorCode.QC100008.getMsg());
        }

        //仪器报告项id   TODO 重点
        if (Objects.isNull(recordInfoVo.getReportId())) {
            throw new IllegalStateException(QcErrorCode.QC100010.getMsg());
        }

        //质控浓度信息记录不能为空
        List<QcAddSetRecordItemVo> recordList = recordInfoVo.getRecordList();
        if (CollectionUtils.isEmpty(recordList)) {
            throw new IllegalStateException(QcErrorCode.QC100013.getMsg());
        }
        //判断质控批号id是否有重复的：采用计算长度的方法；去重前与去重后
        int allSize = recordList.stream().filter(i -> Objects.nonNull(i.getQcBatchId())).map(QcAddSetRecordItemVo::getQcBatchId).collect(Collectors.toList()).size();
        int distinctSize = recordList.stream().filter(i -> Objects.nonNull(i.getQcBatchId())).map(QcAddSetRecordItemVo::getQcBatchId).distinct().collect(Collectors.toList()).size();

        //判断水平编码是否有重复的
        int allLevelSize = recordList.stream().filter(i -> Objects.nonNull(i.getLevelCode())).map(QcAddSetRecordItemVo::getLevelCode).collect(Collectors.toList()).size();
        int distinctLevelSize = recordList.stream().filter(i -> Objects.nonNull(i.getLevelCode())).map(QcAddSetRecordItemVo::getLevelCode).distinct().collect(Collectors.toList()).size();

        //判断质控批号和水平编码是否有重复的     此处是判断从前端传递的三条数据的规则
        if (allSize != distinctSize && allLevelSize != distinctLevelSize) {
            throw new IllegalStateException(QcErrorCode.QC100026.getMsg());
        }
        //关键数据 判断质控水平浓度记录信息
        for (QcAddSetRecordItemVo itemVo : recordList) {
            this.validQcAddSetRecordItemVoParam(itemVo, recordInfoVo.getQualitativeType());
        }
    }

    /**
     * 判断质控水平浓度关键数据合法性
     *
     * @param itemVo
     * @param type
     */
    private void validQcAddSetRecordItemVoParam(QcAddSetRecordItemVo itemVo, Integer type) {
        String cvValue = itemVo.getCvValue();
        if (Objects.nonNull(itemVo.getQcBatchId())) {
            throw new IllegalStateException(QcErrorCode.QC100001.getMsg());
        }
        if (StringUtils.isNotBlank(cvValue)) {
            // 换 % 替换成空
            cvValue = cvValue.replace("%", "");
            if (!NumberUtils.isParsable(cvValue)) {
                throw new IllegalArgumentException("变异系数必须为数字");
            }
            cvValue = new BigDecimal(cvValue).divide(new BigDecimal(100), 3, RoundingMode.HALF_UP).toString();
        }
        // 如果为空，那么校验靶值
        if (Objects.isNull(type)) {
            String targetValue = itemVo.getTargetValue();
            if (StringUtils.isBlank(targetValue)) {
                throw new IllegalStateException("靶值不能为空");
            }

            if (!NumberUtils.isParsable(targetValue)) {
                throw new IllegalArgumentException("靶值必须为数字");
            }
            String standardDeviation = itemVo.getStandardDeviation();
            if (StringUtils.isAllBlank(standardDeviation, cvValue)) {
                throw new IllegalStateException("标准差和变异系数不能同时为空");
            }
            if (StringUtils.isNotBlank(standardDeviation) && !NumberUtils.isParsable(standardDeviation)) {
                throw new IllegalStateException("标准差必须为数字");
            }
        }
        if (itemVo.getLevelCode() == null) {
            throw new IllegalStateException(QcErrorCode.QC100017.getMsg());
        }
    }

    /**
     * 数据类型转换
     *
     * @param recordItemDtos 记录信息内容
     * @return
     */
    private List<QcRecordInfoVo> convertRecordItem2VO(List<QcSetRecordItemDto> recordItemDtos) {

        Map<String, QcRecordInfoVo> recordInfoVoMap = new HashMap<>();
        for (QcSetRecordItemDto recordItemDto : recordItemDtos) {
            if (!recordInfoVoMap.containsKey(recordItemDto.getQcRecordBatch())) {
                QcRecordInfoVo recordInfoVo = new QcRecordInfoVo();
                BeanUtils.copyProperties(recordItemDto, recordInfoVo);
                //TODO 类型转换
                recordInfoVo.setQcStartDate(recordItemDto.getQcStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                recordInfoVo.setQcEndDate(recordItemDto.getQcEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                recordInfoVoMap.put(recordItemDto.getQcRecordBatch(), recordInfoVo);
            }
            QcRecordInfoVo recordInfoVo = recordInfoVoMap.get(recordItemDto.getQcRecordBatch());
            QcSetRecordItemVo recordItemVo = new QcSetRecordItemVo();
            BeanUtils.copyProperties(recordItemDto, recordItemVo);
            recordItemVo.setTargetValue(MyQCNumberUtils.amountFormat(recordItemDto.getTargetValue()));
            recordItemVo.setStandardDeviation(MyQCNumberUtils.amountFormat(recordItemDto.getStandardDeviation()));
            // 这里再乘以一百的原因是 前端需要呈百分比显示
            recordItemVo.setCvValue((MyQCNumberUtils.convertToPercentageInBeforeDividedByOneThousand(recordItemDto.getCvValue())));
            recordInfoVo.addRecordItemVo(recordItemVo);
        }
        return recordInfoVoMap.values().stream().sorted(Comparator.comparing(QcRecordInfoVo::getCreateDate, Comparator.reverseOrder())).collect(Collectors.toList());
    }
}