
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.QcBatchDto;
import com.labway.lims.base.model.TbQcBatch;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 质控批号 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface QcBatchConverter {

    QcBatchDto qcBatchDtoFromTbObj(TbQcBatch obj);

    TbQcBatch tbQcBatchFromTbObjDto(QcBatchDto obj);

    List<QcBatchDto> qcBatchDtoListFromTbObj(List<TbQcBatch> list);

}
