package com.labway.lims.base.service;

import com.labway.lims.base.api.service.QcSetInstrumentRuleService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/11/1 16:02
 * @Version 1.0
 */
@DubboService
public class QcSetInstrumentRuleServiceImpl implements QcSetInstrumentRuleService {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void addRules(String rules, Long instrumentId) {
        //1.关键数据判断
        if (StringUtils.isBlank(rules)) {
            throw new IllegalStateException("未选择规则");
        }
        if (Objects.isNull(instrumentId)) {
            throw new IllegalStateException("未选择仪器");
        }
        // 把质控规则编号, 存到缓存中,后续使用从缓存中拿 TODO 固化在代码中
        String rulesKey = "LABWAY:LILMS-EXAMINE:qc:rules:instrument:" + instrumentId;
        stringRedisTemplate.opsForValue().set(rulesKey, StringUtils.defaultString(rules));
    }
}
