package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrgDoctorDto;
import com.labway.lims.base.api.service.HspOrgDoctorService;
import com.labway.lims.base.vo.HspOrgDoctorAddVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/27 11:31
 */
@Slf4j
@RestController
@RequestMapping("/hsp-org-doctor")
public class HspOrgDoctorController extends BaseController {
    @Resource
    private HspOrgDoctorService hspOrgDoctorService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @PostMapping("/hsp-org-doctors")
    public Object hspOrgDoctors(@RequestParam("hspOrgDeptId") Long hspOrgDeptId) {
        return hspOrgDoctorService.selectByHspOrgDeptId(hspOrgDeptId);
    }

    @PostMapping("/hsp-all-doctors")
    public Object hspAllDoctors(@RequestParam("hspOrgId") Long hspOrgId) {
        return hspOrgDoctorService.selectByHspOrgId(hspOrgId);
    }

    @PostMapping("/add")
    public Object add(@RequestBody List<HspOrgDoctorAddVo> vos) {

        if (CollectionUtils.isEmpty(vos)) {
            return Map.of();
        }

        if (vos.stream().anyMatch(e -> Objects.isNull(e.getHspOrgDeptId()))
            || vos.stream().anyMatch(e -> Objects.isNull(e.getHspOrgMainId()))
            || vos.stream().anyMatch(e -> StringUtils.isBlank(e.getDept()))) {
            throw new IllegalArgumentException("参数错误");
        }

        if (vos.stream().anyMatch(e -> StringUtils.isBlank(e.getDoctorName()))) {
            throw new IllegalArgumentException("医生姓名不能为空");

        }

        final List<HspOrgDoctorDto> dtos = JSON.parseArray(JSON.toJSONString(vos), HspOrgDoctorDto.class);
        dtos.forEach(e -> {
            e.setDoctorCode(StringUtils.EMPTY);
            e.setDeptCode(StringUtils.EMPTY);
        });
        hspOrgDoctorService.addBatch(dtos);

        // 记录操作日志
        dtos.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_DEPT_DOCTOR.getDesc())
                    .setContent(String.format("部门 [%s] 新增医生 [%s] ", item.getDept(), item.getDoctorName()))
                    .toJSONString());
        });

        return Map.of();
    }

    @PostMapping("/delete")
    public Object delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("请选择需要删除的数据");
        }
        List<HspOrgDoctorDto> hspOrgDoctorDtos = hspOrgDoctorService.selectByHspOrgDoctorIds(ids);

        hspOrgDoctorService.deleteByHspOrgDoctorId(ids);
        hspOrgDoctorDtos.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_DEPT_DOCTOR.getDesc())
                    .setContent(String.format("部门 [%s] 删除医生 [%s] ", item.getDept(), item.getDoctorName()))
                    .toJSONString());
        });
        return Map.of();
    }
}
