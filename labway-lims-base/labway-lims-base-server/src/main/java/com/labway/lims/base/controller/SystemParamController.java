package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.SystemParamMetricEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.vo.SystemParamVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/21 11:52
 */
@RestController
@RequestMapping("/system-param")
public class SystemParamController extends BaseController {
    @Resource
    private SystemParamService systemParamService;

    @PostMapping("/add")
    public Object add(@RequestBody SystemParamVo vo) {
        if (StringUtils.isEmpty(vo.getParamMetric())) {
            throw new IllegalArgumentException("参数分类不能为空");
        }

        if (StringUtils.isEmpty(vo.getParamName())) {
            throw new IllegalArgumentException("参数名称不能为空");
        }

        if (StringUtils.isEmpty(vo.getParamValue())) {
            throw new IllegalArgumentException("参数值不能为空");
        }
        final SystemParamDto dto = JSON.parseObject(JSON.toJSONString(vo), SystemParamDto.class);

        return Map.of("id", systemParamService.add(dto));
    }

    @PostMapping("/update")
    public Object updateSystemParam(@RequestBody SystemParamVo vo) {
        if (StringUtils.isEmpty(vo.getParamMetric())) {
            throw new IllegalArgumentException("参数分类不能为空");
        }

        if (StringUtils.isEmpty(vo.getParamName())) {
            throw new IllegalArgumentException("参数名称不能为空");
        }

        if (StringUtils.isEmpty(vo.getParamValue())) {
            throw new IllegalArgumentException("参数值不能为空");
        }
        final SystemParamDto dto = JSON.parseObject(JSON.toJSONString(vo), SystemParamDto.class);
        systemParamService.updateByParamId(dto);
        return Collections.emptyMap();
    }

    @PostMapping("/delete")
    public Object delete(@RequestBody Set<Long> paramId) {
        for (Long id : paramId) {
            systemParamService.deleteByParamId(id);
        }
        return Collections.emptyMap();
    }

    @PostMapping("/params")
    public Object selectAll() {
        return systemParamService.selectByOrgId(LoginUserHandler.get().getOrgId())
                .stream()
                .filter(e -> !Objects.equals(e.getParamMetric(), SystemParamMetricEnum.SHORTCUT_KEY.toString()))
                .collect(Collectors.toList());
    }

    @PostMapping("/select-by-param-name")
    public Object selectByParamName(@RequestBody SystemParamVo vo) {
        return systemParamService.selectByParamName(vo.getParamName(), LoginUserHandler.get().getOrgId());
    }

}
