
package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.HspOrgDiscountDto;
import com.labway.lims.base.model.TbHspOrgDiscount;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 客户折扣信息服务层 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 19:21
 */
@Mapper(componentModel = "spring")
public interface HspOrgDiscountConverter {

    HspOrgDiscountDto hspOrgDiscountDtoFromTbObj(TbHspOrgDiscount obj);

    List<HspOrgDiscountDto> hspOrgDiscountDtoListFromTbObjList(List<TbHspOrgDiscount> list);

}
