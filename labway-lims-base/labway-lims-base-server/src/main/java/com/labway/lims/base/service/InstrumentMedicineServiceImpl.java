package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentMedicineDto;
import com.labway.lims.base.api.service.InstrumentMedicineService;
import com.labway.lims.base.mapper.TbInstrumentMedicineMapper;
import com.labway.lims.base.mapstruct.InstrumentMedicineConverter;
import com.labway.lims.base.model.TbInstrumentMedicine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <p>
 * 仪器药物 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Slf4j
@DubboService
public class InstrumentMedicineServiceImpl implements InstrumentMedicineService {

    @Resource
    private TbInstrumentMedicineMapper tbInstrumentMedicineMapper;

    @Resource
    private InstrumentMedicineConverter instrumentMedicineConverter;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public List<InstrumentMedicineDto> selectByInstrumentId(long instrumentId) {
        LambdaQueryWrapper<TbInstrumentMedicine> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbInstrumentMedicine::getInstrumentId, instrumentId);
        queryWrapper.eq(TbInstrumentMedicine::getIsDelete, YesOrNoEnum.NO.getCode());
        return instrumentMedicineConverter
            .instrumentMedicineDtosFromTbObj(tbInstrumentMedicineMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addInstrumentMedicines(List<InstrumentMedicineDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 仪器药物
        List<TbInstrumentMedicine> targetList = instrumentMedicineConverter.tbInstrumentMedicinesFromTbObjDto(list);
        // 数量 分区批次插入
        List<List<TbInstrumentMedicine>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbInstrumentMedicineMapper.batchAddTbInstrumentMedicines(item));

        log.info("用户 [{}] 新增仪器药物成功", loginUser.getNickname());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByInstrumentMedicineId(InstrumentMedicineDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbInstrumentMedicine target = instrumentMedicineConverter.tbInstrumentMedicineFromTbObjDto(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbInstrumentMedicineMapper.updateById(target) < 1) {
            throw new LimsException("修改仪器药物失败");
        }

        log.info("用户 [{}] 修改仪器药物成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInstrumentMedicine(Collection<Long> needDeleteInstrumentMedicineIds,
        List<InstrumentMedicineDto> addList, List<InstrumentMedicineDto> updateList) {
        LoginUserHandler.User loginUser = LoginUserHandler.get();
        if (CollectionUtils.isNotEmpty(needDeleteInstrumentMedicineIds)) {
            tbInstrumentMedicineMapper.deleteBatchIds(needDeleteInstrumentMedicineIds);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            LinkedList<Long> genIds = snowflakeService.genIds(addList.size());
            Date date = new Date();
            addList.forEach(item -> {
                item.setInstrumentMedicineId(genIds.pop());

                item.setOrgId(loginUser.getOrgId());
                item.setOrgName(loginUser.getOrgName());
                item.setCreateDate(date);
                item.setUpdateDate(date);
                item.setCreatorId(loginUser.getUserId());
                item.setCreatorName(loginUser.getNickname());
                item.setUpdaterId(loginUser.getUserId());
                item.setUpdaterName(loginUser.getNickname());
                item.setIsDelete(YesOrNoEnum.NO.getCode());
            });
            this.addInstrumentMedicines(addList);
        }
        updateList.forEach(this::updateByInstrumentMedicineId);
    }

    @Override
    public void deleteByMedicineId(Long instrumentId, Long medicineId) {
        tbInstrumentMedicineMapper.delete(
            new LambdaQueryWrapper<TbInstrumentMedicine>().eq(TbInstrumentMedicine::getInstrumentId, instrumentId)
                .eq(TbInstrumentMedicine::getMedicineId, medicineId));
        log.info("用户 [{}] 删除仪器 [{}] 药物 [{}] 成功", LoginUserHandler.get().getNickname(), instrumentId, medicineId);
    }

    @Override
    public InstrumentMedicineDto selectByMedicineId(Long instrumentId, Long medicineId) {

        LambdaQueryWrapper<TbInstrumentMedicine> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbInstrumentMedicine::getInstrumentId, instrumentId);
        queryWrapper.eq(TbInstrumentMedicine::getMedicineId, medicineId);
        queryWrapper.last("limit 1");
        return instrumentMedicineConverter
            .instrumentMedicineDtoFromTbObj(tbInstrumentMedicineMapper.selectOne(queryWrapper));
    }
}
