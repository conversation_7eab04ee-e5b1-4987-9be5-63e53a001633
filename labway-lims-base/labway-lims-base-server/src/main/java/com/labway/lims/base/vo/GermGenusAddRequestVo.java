package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 细菌菌属 新增 请求 Vo
 * 
 * <AUTHOR>
 * @since 2023/3/20 18:05
 */
@Getter
@Setter
public class GermGenusAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 细菌菌属名称
     */
    private String germGenusName;

    /**
     * 细菌菌属编码
     */
    private String germGenusCode;

    /**
     * 是否启用(0未启用 1启用)
     * @see YesOrNoEnum
     */
    private Integer enable;
}
