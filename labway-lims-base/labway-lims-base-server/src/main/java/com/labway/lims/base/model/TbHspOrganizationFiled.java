package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 双输内容对照表
 */
@TableName("tb_hsp_organization_filed")
@Getter
@Setter
public class TbHspOrganizationFiled implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 对照内容id
     */
    @TableId(type = IdType.AUTO)
    private Long filedId;
    /**
     * 字段code
     */
    private String code;

    /**
     * 字段名称
     */
    private String filed;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构code
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 创建时间
     */
    private int isDelete;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 更新时间
     */
    private Date updateDate;
}
