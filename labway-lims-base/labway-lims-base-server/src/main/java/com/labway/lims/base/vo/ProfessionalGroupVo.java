package com.labway.lims.base.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 专业组
 */
@Getter
@Setter
public class ProfessionalGroupVo implements Serializable {
    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业组说明
     */
    private String groupRemark;

    /**
     * 专业组编码
     */
    private String groupCode;

    /**
     * 专业组类别ID
     */
    private String groupTypeCode;

    /**
     * 专业组类别名称
     */
    private String groupTypeName;

    /**
     * 批准者名字
     */
    private String approverName;

    /**
     * 批准者签名图片
     */
    private String approverSign;

    /**
     * 是否启动(0未启动 1启动)
     *
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProfessionalGroupVo that = (ProfessionalGroupVo) o;
        return Objects.equals(groupId, that.groupId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(groupId);
    }
}
