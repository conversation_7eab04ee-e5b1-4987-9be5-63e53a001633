package com.labway.lims.base.service;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentReportItemResultTipDto;
import com.labway.lims.base.api.service.InstrumentReportItemResultTipService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.mapper.TbInstrumentReportItemResultTipMapper;
import com.labway.lims.base.mapstruct.InstrumentReportItemResultTipConverter;
import com.labway.lims.base.model.TbInstrumentReportItemResultTip;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "instrument-report-item-result-tip")
public class InstrumentReportItemResultTipServiceImpl implements InstrumentReportItemResultTipService {
    @Resource
    private TbInstrumentReportItemResultTipMapper instrumentReportItemResultTipMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @Resource
    private InstrumentReportItemResultTipConverter instrumentReportItemResultTipConverter;
    @Resource
    private InstrumentReportItemResultTipService instrumentReportItemResultTipService;

    @Override
    @CacheEvict(allEntries = true)
    public long addInstrumentReportItemResultTip(InstrumentReportItemResultTipDto dto) {

        final TbInstrumentReportItemResultTip instrumentReportItemResultTip = new TbInstrumentReportItemResultTip();
        instrumentReportItemResultTip.setInstrumentReportItemResultTipId(snowflakeService.genId());
        instrumentReportItemResultTip.setInstrumentReportItemId(dto.getInstrumentReportItemId());
        instrumentReportItemResultTip.setReportItemCode(dto.getReportItemCode());
        instrumentReportItemResultTip.setReportItemName(dto.getReportItemName());
        instrumentReportItemResultTip.setInstrumentId(dto.getInstrumentId());
        instrumentReportItemResultTip.setInstrumentCode(dto.getInstrumentCode());
        instrumentReportItemResultTip.setInstrumentName(dto.getInstrumentName());
        instrumentReportItemResultTip.setTipContent(dto.getTipContent());
        instrumentReportItemResultTip.setTipType(dto.getTipType());
        instrumentReportItemResultTip.setFormulaMax(dto.getFormulaMax());
        instrumentReportItemResultTip.setFormulaMaxValue(dto.getFormulaMaxValue());
        instrumentReportItemResultTip.setFormulaMin(dto.getFormulaMin());
        instrumentReportItemResultTip.setFormulaMinValue(dto.getFormulaMinValue());
        instrumentReportItemResultTip.setHspOrgId(dto.getHspOrgId());
        instrumentReportItemResultTip.setHspOrgName(dto.getHspOrgName());
        instrumentReportItemResultTip.setEnable(dto.getEnable());
        instrumentReportItemResultTip.setCreateDate(new Date());
        instrumentReportItemResultTip.setUpdateDate(new Date());
        instrumentReportItemResultTip.setCreatorId(LoginUserHandler.get().getUserId());
        instrumentReportItemResultTip.setCreatorName(LoginUserHandler.get().getNickname());
        instrumentReportItemResultTip.setUpdaterId(LoginUserHandler.get().getUserId());
        instrumentReportItemResultTip.setUpdaterName(LoginUserHandler.get().getNickname());
        instrumentReportItemResultTip.setOrgId(LoginUserHandler.get().getOrgId());
        instrumentReportItemResultTip.setOrgName(LoginUserHandler.get().getOrgName());
        instrumentReportItemResultTip.setIsDelete(YesOrNoEnum.NO.getCode());

        if (instrumentReportItemResultTipMapper.insert(instrumentReportItemResultTip) < 1) {
            throw new IllegalStateException("添加结果提示失败");
        }

        log.info("用户 [{}] 添加结果值提示 [{}] 成功", LoginUserHandler.get().getNickname(),
                JSON.toJSONString(instrumentReportItemResultTip));

        return instrumentReportItemResultTip.getInstrumentReportItemResultTipId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByInstrumentReportItemResultTipId(long instrumentReportItemResultTipId) {
        instrumentReportItemResultTipMapper.deleteById(instrumentReportItemResultTipId);
    }

    @Override
    @Cacheable(key = "'selectByOrgId:' + #orgId")
    public List<InstrumentReportItemResultTipDto> selectByOrgId(long orgId) {
        return instrumentReportItemResultTipMapper
                .selectList(new LambdaQueryWrapper<TbInstrumentReportItemResultTip>()
                        .eq(TbInstrumentReportItemResultTip::getOrgId, orgId)
                        .orderByDesc(TbInstrumentReportItemResultTip::getInstrumentReportItemResultTipId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByHspOrgId:' + #hspOrgId")
    public List<InstrumentReportItemResultTipDto> selectByHspOrgId(long hspOrgId) {
        return instrumentReportItemResultTipMapper
                .selectList(new LambdaQueryWrapper<TbInstrumentReportItemResultTip>()
                        .eq(TbInstrumentReportItemResultTip::getHspOrgId, hspOrgId)
                        .orderByDesc(TbInstrumentReportItemResultTip::getInstrumentReportItemResultTipId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectInstrumentReportItemResultTipId:' + #instrumentReportItemResultTipId")
    public InstrumentReportItemResultTipDto
    selectInstrumentReportItemResultTipId(long instrumentReportItemResultTipId) {
        return convert(instrumentReportItemResultTipMapper.selectById(instrumentReportItemResultTipId));
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByInstrumentReportItemResultTipId(InstrumentReportItemResultTipDto dto) {

        final InstrumentReportItemResultTipDto old = SpringUtil.getBean(InstrumentReportItemResultTipServiceImpl.class)
                .selectInstrumentReportItemResultTipId(dto.getInstrumentReportItemResultTipId());
        if (Objects.isNull(old)) {
            throw new IllegalStateException("结果提示不存在");
        }

        final TbInstrumentReportItemResultTip instrumentReportItemResultTip = new TbInstrumentReportItemResultTip();
        // 禁止修改
        dto.setInstrumentId(null);
        dto.setInstrumentCode(null);
        dto.setInstrumentName(null);
        dto.setHspOrgId(ObjectUtils.defaultIfNull(dto.getHspOrgId(), NumberUtils.LONG_ZERO));

        BeanUtils.copyProperties(dto, instrumentReportItemResultTip);


        instrumentReportItemResultTip.setUpdateDate(new Date());
        instrumentReportItemResultTip.setUpdaterId(LoginUserHandler.get().getUserId());
        instrumentReportItemResultTip.setUpdaterName(LoginUserHandler.get().getNickname());

        if (instrumentReportItemResultTipMapper.updateById(instrumentReportItemResultTip) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改结果值提示 [{}] 成功", LoginUserHandler.get().getNickname(),
                JSON.toJSONString(instrumentReportItemResultTip));

        return true;
    }


    @Override
    public List<InstrumentReportItemResultTipDto> selectByInstrumentIds(Collection<Long> instrumentIds) {
        if (CollectionUtils.isEmpty(instrumentIds)) {
            return Collections.emptyList();
        }

        return instrumentIds.stream().map(instrumentReportItemResultTipService::selectByInstrumentId)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream).collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByInstrumentId:' + #instrumentId")
    public List<InstrumentReportItemResultTipDto> selectByInstrumentId(long instrumentId) {
        final LambdaQueryWrapper<TbInstrumentReportItemResultTip> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInstrumentReportItemResultTip::getInstrumentId, instrumentId);
        return instrumentReportItemResultTipMapper.selectList(wrapper)
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<InstrumentReportItemResultTipDto> selectInstrumentReportItemResultTipIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return instrumentReportItemResultTipMapper.selectBatchIds(ids)
                .stream().map(this::convert).collect(Collectors.toList());
    }

    private InstrumentReportItemResultTipDto convert(TbInstrumentReportItemResultTip tip) {
        if (Objects.isNull(tip)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(tip), InstrumentReportItemResultTipDto.class);
    }
}
