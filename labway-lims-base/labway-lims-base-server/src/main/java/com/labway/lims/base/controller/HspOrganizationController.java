package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.CustomerRelationListDto;
import com.labway.business.center.compare.request.CustomerRelationListRequest;
import com.labway.business.center.compare.service.TbOrgCustomerRelationService;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.vo.HspOrganizationVo;
import com.swak.frame.dto.Response;
import com.swak.frame.enums.BasicErrCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 送检机构
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/hsp-organization")
public class HspOrganizationController extends BaseController {

    @DubboReference
    private RabbitMQService rabbitMQService;

    @Resource
    private HspOrganizationService hspOrganizationService;
    @Resource
    private Environment environment;

    @Resource
    private TbOrgCustomerRelationService tbOrgCustomerRelationService;

    /**
     * 查询业务中台所有送检机构
     */
    @GetMapping("/select-all-business-center-org")
    public Object selectAllBusinessCenterOrg() {
        CustomerRelationListRequest listRequest = new CustomerRelationListRequest();
        final String businessCenterOrgCode =
            environment.getProperty(TestItemController.BUSINESS_CENTER_ORG_CODE, StringUtils.EMPTY);
        listRequest.setOrgId(businessCenterOrgCode);
        log.info("请求业务中台参数：{}", JSON.toJSONString(listRequest));
        Response<List<CustomerRelationListDto>> response;
        try {
            response = tbOrgCustomerRelationService.queryCustomerRelationList(listRequest);
        } catch (Exception e) {
            throw new LimsException("业务中台-获取所有外送机构返回错误", e);
        }

        if (Objects.isNull(response)) {
            throw new LimsException("业务中台-获取所有外送机构返回结果为空");
        }

        if (!Objects.equals(response.getCode(), BasicErrCode.SUCCESS.getCode())) {
            throw new LimsException(String.format("业务中台-获取所有外送机构返回错误, 错误信息: [%s]", response.getMsg()));
        }

        if (Objects.isNull(response.getData())) {
            return Collections.emptyList();
        }

        return response.getData();
    }

    /**
     * 删除机构
     *
     * @param hspOrgIds 机构id
     */
    @PostMapping("/delete")
    public Object deleteHspOrganization(@RequestBody List<Long> hspOrgIds) {

        final List<HspOrganizationDto> hspOrganizations = hspOrganizationService.selectByHspOrgIds(hspOrgIds);
        if (CollectionUtils.isEmpty(hspOrganizations)) {
            throw new IllegalArgumentException("送检机构不存在，删除失败");
        }

        hspOrganizationService.deleteByHspOrgIds(hspOrgIds);

        final List<String> hspOrgNames =
            hspOrganizations.stream().map(HspOrganizationDto::getHspOrgName).collect(Collectors.toList());

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_ORG.getDesc())
                .setContent(String.format("用户删除了送检机构 %s", hspOrgNames)).toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 修改机构信息
     */
    @PostMapping("/updateHspOrganization")
    public Object updateHspOrganization(@RequestBody HspOrganizationVo vo) {
        log.info("修改机构入参 -> {}", JSON.toJSONString(vo));

        if (Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalArgumentException("机构ID为空");
        }

        // 开票名称
        if (StringUtils.length(vo.getInvoice()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("开票名称不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }
        // 销售区域
        if (StringUtils.length(vo.getSaleAreaName()) > INPUT_MAX_LENGTH
            || StringUtils.length(vo.getSaleAreaCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("销售区域不能大于 %s 个字符", INPUT_MAX_LENGTH));
        }
        // 销售部门
        if (StringUtils.length(vo.getSaleDeptName()) > INPUT_MAX_LENGTH
            || StringUtils.length(vo.getSaleDeptCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("销售部门不能大于 %s 个字符", INPUT_MAX_LENGTH));
        }
        // 销售类型
        if (StringUtils.length(vo.getSaleTypeName()) > INPUT_MAX_LENGTH
            || StringUtils.length(vo.getSaleTypeCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("销售类型不能大于 %s 个字符", INPUT_MAX_LENGTH));
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalArgumentException("送检机构不存在");
        }

        final HspOrganizationDto dto = JSON.parseObject(JSON.toJSONString(vo), HspOrganizationDto.class);
        hspOrganizationService.updateByHspOrgId(dto);

        String compare = new CompareUtils<HspOrganizationDto>().compare(hspOrganization, dto);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.RACK_LOG.getDesc())
                    .setContent(String.format("送检机构修改 [%s]", compare)).toJSONString());
        }

        return Collections.emptyMap();
    }

    /**
     * 查询所有机构
     */
    @GetMapping("/select-enable-send-org")
    public Object selectEnableSendOrg() {
        // 启用 & 未外送
        return selectAll(YesOrNoEnum.YES.getCode(), null).stream()
            .filter(f -> Objects.equals(f.getIsExport(), YesOrNoEnum.NO.getCode())).collect(Collectors.toList());
    }

    /**
     * 查询所有机构
     */
    @GetMapping("/selectAll")
    public List<HspOrganizationVo> selectAll(@RequestParam(required = false) Integer enable,
                                             @RequestParam(required = false) Integer isExport) {
        final List<HspOrganizationDto> hspOrganizations = hspOrganizationService.selectAll().stream()
                .filter(f -> Objects.isNull(isExport) || Objects.equals(isExport, f.getIsExport()))
                .filter(f -> Objects.isNull(enable) || Objects.equals(enable, f.getEnable())).collect(Collectors.toList());

        return JSON.parseArray(JSON.toJSONString(hspOrganizations), HspOrganizationVo.class);
    }

    /**
     * 新增机构
     */
    @PostMapping("/addHspOrganization")
    public Object addHspOrganization(@RequestBody HspOrganizationVo vo) {
        log.info("新增机构入参 -> {}", JSON.toJSONString(vo));

        // 编码
        if (StringUtils.isBlank(vo.getHspOrgCode())) {
            throw new IllegalArgumentException("送检机构编码不能为空");
        }
        // 名称
        if (StringUtils.isBlank(vo.getHspOrgName())) {
            throw new IllegalArgumentException("送检机构名称不能为空");
        }
        // 开票名称
        if (StringUtils.length(vo.getInvoice()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("开票名称不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }
        // 销售区域
        if (StringUtils.length(vo.getSaleAreaName()) > INPUT_MAX_LENGTH
            || StringUtils.length(vo.getSaleAreaCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("销售区域不能大于 %s 个字符", INPUT_MAX_LENGTH));
        }
        // 销售部门
        if (StringUtils.length(vo.getSaleDeptName()) > INPUT_MAX_LENGTH
            || StringUtils.length(vo.getSaleDeptCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("销售部门不能大于 %s 个字符", INPUT_MAX_LENGTH));
        }
        // 销售类型
        if (StringUtils.length(vo.getSaleTypeName()) > INPUT_MAX_LENGTH
            || StringUtils.length(vo.getSaleTypeCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("销售类型不能大于 %s 个字符", INPUT_MAX_LENGTH));
        }

        vo.setEnable(ObjectUtils.defaultIfNull(vo.getEnable(), YesOrNoEnum.YES.getCode()));
        vo.setEnableDoubleCheck(ObjectUtils.defaultIfNull(vo.getEnableDoubleCheck(), YesOrNoEnum.NO.getCode()));
        vo.setEnableSplitBlood(ObjectUtils.defaultIfNull(vo.getEnableSplitBlood(), YesOrNoEnum.YES.getCode()));
        vo.setIsExport(ObjectUtils.defaultIfNull(vo.getIsExport(), YesOrNoEnum.NO.getCode()));
        vo.setEnablePrintBarcode(ObjectUtils.defaultIfNull(vo.getEnablePrintBarcode(), YesOrNoEnum.YES.getCode()));
        vo.setEnableGenerateBarcode(
            ObjectUtils.defaultIfNull(vo.getEnableGenerateBarcode(), YesOrNoEnum.YES.getCode()));

        final HspOrganizationDto dto = JSON.parseObject(JSON.toJSONString(vo), HspOrganizationDto.class);
        final Map<String, Long> result = Map.of("id", hspOrganizationService.addHspOrganization(dto));

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_ORG.getDesc())
                .setContent(String.format(
                    "新增 [%s] 送检机构 编码 [%s] 是否外送 [%s] 是否打印条码 [%s] 是否生成新条码 [%s]"
                        + " 是否分血 [%s] 是否启用 [%s] 开票名称 [%s] 销售区域 [%s] 销售部门 [%s]",
                    vo.getHspOrgName(), vo.getHspOrgCode(),
                    Objects.equals(vo.getIsExport(), YesOrNoEnum.YES.getCode()) ? "是" : "否",
                    Objects.equals(vo.getEnablePrintBarcode(), YesOrNoEnum.YES.getCode()) ? "是" : "否",
                    Objects.equals(vo.getEnableGenerateBarcode(), YesOrNoEnum.YES.getCode()) ? "是" : "否",
                    Objects.equals(vo.getEnableSplitBlood(), YesOrNoEnum.YES.getCode()) ? "是" : "否",
                    Objects.equals(vo.getEnable(), YesOrNoEnum.YES.getCode()) ? "是" : "否", vo.getInvoice(),
                    vo.getSaleAreaName(), vo.getSaleDeptName()))
                .toJSONString());

        return result;
    }
}
