package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.model.TbInstrumentReportItemReference;
import org.mapstruct.Mapper;

/**
 * <p>
 * InstrumentReportItemReferenceConverter
 * 仪器报告项目参考值 转换
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/26 19:29
 */
@Mapper(componentModel = "spring")
public interface InstrumentReportItemReferenceConverter {

    TbInstrumentReportItemReference convertDto2Entity(InstrumentReportItemReferenceDto itemReferenceDto);

}
