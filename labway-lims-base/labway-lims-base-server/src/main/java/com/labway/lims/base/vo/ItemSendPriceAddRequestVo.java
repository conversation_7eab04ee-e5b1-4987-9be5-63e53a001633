package com.labway.lims.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 外送机构-添加外送项目 请求参数
 *
 * <AUTHOR>
 * @since 2023/5/5 10:56
 */
@Getter
@Setter
public class ItemSendPriceAddRequestVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endDate;

    /**
     * 添加的项目信息
     */
    private List<ItemSendPriceItemVo> testItemList;

    @Getter
    @Setter
    public static class ItemSendPriceItemVo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 项目ID
         */
        private Long testItemId;

        /**
         * 外送价格
         */
        private String sendPrice;

    }
}
