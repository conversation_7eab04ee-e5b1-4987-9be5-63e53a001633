package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 仪器细菌维护信息响应Vo
 * 
 * <AUTHOR>
 * @since 2023/7/12 17:57
 */
@Getter
@Setter
public class InstrumentGermResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 仪器细菌id
     */
    private Long instrumentGermId;
    /**
     * 细菌id
     */
    private Long germId;
    /**
     * 细菌编码
     */
    private String germCode;
    /**
     * 细菌名称
     */
    private String germName;
    /**
     * 仪器通道号
     */
    private String instrumentChannel;
}
