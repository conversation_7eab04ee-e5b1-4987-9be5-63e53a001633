package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.GermDto;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.base.mapper.TbGermMapper;
import com.labway.lims.base.model.TbGerm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 细菌 Service impl
 *
 * <AUTHOR>
 * @since 2023/3/20 17:42
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "germ")
public class GermServiceImpl implements GermService {

    @Resource
    private TbGermMapper tbGermMapper;
    @Resource
    private GermService germService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public long addGerm(GermDto germDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbGerm germ = new TbGerm();
        germ.setGermCode(germDto.getGermCode());
        germ.setGermName(germDto.getGermName());
        germ.setGermEn(ObjectUtils.defaultIfNull(germDto.getGermEn(), ""));
        germ.setGermGenusId(germDto.getGermGenusId());
        germ.setGermGenusCode(germDto.getGermGenusCode());
        germ.setGermTypeCode(germDto.getGermTypeCode());
        germ.setGermTypeName(ObjectUtils.defaultIfNull(germDto.getGermTypeName(), ""));
        germ.setWhonetGermTypeCode(ObjectUtils.defaultIfNull(germDto.getWhonetGermTypeCode(), ""));
        germ.setWhonetGermTypeName(ObjectUtils.defaultIfNull(germDto.getWhonetGermTypeName(), ""));
        germ.setWhonetGermCode(ObjectUtils.defaultIfNull(germDto.getWhonetGermCode(), ""));
        germ.setEnableStatistics(germDto.getEnableStatistics());
        germ.setEnable(germDto.getEnable());

        germ.setGermId(snowflakeService.genId());
        germ.setOrgId(loginUser.getOrgId());
        germ.setOrgName(loginUser.getOrgName());
        germ.setCreateDate(new Date());
        germ.setUpdateDate(new Date());
        germ.setCreatorId(loginUser.getUserId());
        germ.setCreatorName(loginUser.getNickname());
        germ.setUpdaterId(loginUser.getUserId());
        germ.setUpdaterName(loginUser.getNickname());
        germ.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbGermMapper.insert(germ) < 1) {
            throw new LimsException("添加细菌失败");
        }
        log.info("用户 [{}] 新增细菌[{}]成功", loginUser.getNickname(), JSON.toJSONString(germ));

        return germ.getGermId();
    }

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void deleteByGermIds(Collection<Long> germIds) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除细菌成功 [{}] 结果 [{}]", loginUser.getNickname(), germIds,
            tbGermMapper.deleteBatchIds(germIds) > 0);
    }

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void updateByGermId(GermDto germDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbGerm germ = new TbGerm();
        BeanUtils.copyProperties(germDto, germ);

        germ.setUpdaterId(loginUser.getUserId());
        germ.setUpdaterName(loginUser.getNickname());
        germ.setUpdateDate(new Date());

        if (tbGermMapper.updateById(germ) < 1) {
            throw new LimsException("修改细菌失败");
        }

        log.info("用户 [{}] 修改细菌成功 [{}]", loginUser.getNickname(), JSON.toJSONString(germ));

    }

    @Override
    @Cacheable(key = "'selectByOrgId:' + #orgId")
    public List<GermDto> selectByOrgId(long orgId) {
        if (orgId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbGerm> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbGerm::getOrgId, orgId);
        queryWrapper.eq(TbGerm::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbGerm::getCreateDate);
        return convert(tbGermMapper.selectList(queryWrapper));
    }

    @Override
    public List<GermDto> selectByGermGenusIds(Collection<Long> germGenusIds) {
        if (CollectionUtils.isEmpty(germGenusIds)) {
            return Collections.emptyList();
        }

        return germGenusIds.stream().map(germService::selectByGermGenusId).filter(Objects::nonNull)
            .flatMap(Collection::stream).collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByGermGenusId:' + #germGenusId")
    public List<GermDto> selectByGermGenusId(long germGenusId) {
        final LambdaQueryWrapper<TbGerm> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGerm::getGermGenusId, germGenusId);
        queryWrapper.eq(TbGerm::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbGerm::getCreateDate);
        return convert(tbGermMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByGermId:' + #germId")
    public GermDto selectByGermId(long germId) {
        if (germId < 1) {
            return null;
        }
        LambdaQueryWrapper<TbGerm> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGerm::getGermId, germId);
        queryWrapper.eq(TbGerm::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return convert(tbGermMapper.selectOne(queryWrapper));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByGermName:' + #germName + ',' + #orgId")
    public GermDto selectByGermName(String germName, long orgId) {
        if (StringUtils.isBlank(germName)) {
            return null;
        }
        LambdaQueryWrapper<TbGerm> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGerm::getGermName, germName);
        queryWrapper.eq(TbGerm::getOrgId, orgId);
        queryWrapper.eq(TbGerm::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return convert(tbGermMapper.selectOne(queryWrapper));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByGermCode:' + #germCode + ',' + #orgId")
    public GermDto selectByGermCode(String germCode, long orgId) {
        if (StringUtils.isBlank(germCode)) {
            return null;
        }
        LambdaQueryWrapper<TbGerm> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGerm::getGermCode, germCode);
        queryWrapper.eq(TbGerm::getOrgId, orgId);
        queryWrapper.eq(TbGerm::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return convert(tbGermMapper.selectOne(queryWrapper));
    }

    @Override
    public List<GermDto> selectByGermIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return tbGermMapper.selectBatchIds(ids).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<GermDto> selectByGermCodes(Collection<String> germCodes, long orgId) {
        if (CollectionUtils.isEmpty(germCodes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbGerm> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbGerm::getGermCode, germCodes);
        queryWrapper.eq(TbGerm::getOrgId, orgId);
        queryWrapper.eq(TbGerm::getIsDelete, YesOrNoEnum.NO.getCode());

        return convert(tbGermMapper.selectList(queryWrapper));
    }

    /**
     * TbGerm 转换 为 GermDto
     *
     * @param tbGerm TbGerm
     * @return GermDto
     */
    private GermDto convert(TbGerm tbGerm) {
        if (Objects.isNull(tbGerm)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(tbGerm), GermDto.class);
    }

    /**
     * TbGerm 转换 为 GermDto
     *
     * @param list TbGerm
     * @return GermDto
     */
    private List<GermDto> convert(List<TbGerm> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }
}
