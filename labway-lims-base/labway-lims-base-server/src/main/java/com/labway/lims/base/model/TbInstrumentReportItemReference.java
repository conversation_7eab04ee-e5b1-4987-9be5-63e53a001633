package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.base.api.enums.AgeUnitEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 仪器报告项目参考值
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Getter
@Setter
@TableName("tb_instrument_report_item_reference")
public class TbInstrumentReportItemReference implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参考值范围ID
     */
    @TableId
    private Long instrumentReportItemReferenceId;

    /**
     * 仪器报告项目ID
     */
    private Long instrumentReportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;


    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;


    /**
     * 性别类型(男、女、通用)
     */
    private Integer sexStyle;

    /**
     * 性别类型名称
     */
    private String sexStyleName;


    /**
     * 参考值上限值
     */
    private String referValueMax;


    /**
     * 参考值下限值
     */
    private String referValueMin;

    /**
     * 异常提示范围上限
     */
    private String excpWarningMax;

    /**
     * 异常提示范围下限
     */
    private String excpWarningMin;


    /**
     * 危急值上限
     */
    private String fatalMax;

    /**
     * 危急值下限
     */
    private String fatalMin;


    /**
     * 年龄上限
     */
    private Integer ageMax;

    /**
     * 年龄下限
     */
    private Integer ageMin;


    /**
     * 年龄上限 < or <=
     */
    private String ageMaxFormula;

    /**
     * 年龄下限 > or >=
     */
    private String ageMinFormula;

    /**
     * 年龄单位
     *
     * @see AgeUnitEnum
     */
    private String ageUnit;

    /**
     * 中文参考值
     */
    private String cnRefereValue;

    /**
     * 英文参考值
     */
    private String enRefereValue;

    /**
     * 中英文参考值
     */
    private String cnEnRefereValue;

    /**
     * 参考值状态
     *
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;


    /**
     * 样本类型编码
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;


    /**
     * 1: 删除 0：未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;


    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 参考值上限值 < or <=
     */
    private String referValueMaxFormula;

    /**
     * 参考值下限值 > or >=
     */
    private String referValueMinFormula;
    /**
     * 异常提示上限 < or <=
     */
    private String excpWarningMaxFormula;

    /**
     * 异常提示范围下限 > or >=
     */
    private String excpWarningMinFormula;
    /**
     * 异常提示上限 < or <=
     */
    private String fatalMaxFormula;
    /**
     * 异常提示上限 < or <=
     */
    private String fatalMinFormula;
}
