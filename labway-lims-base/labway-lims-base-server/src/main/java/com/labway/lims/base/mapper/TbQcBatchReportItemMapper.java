package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.model.TbQcBatchReportItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbQcBatchReportItemMapper extends BaseMapper<TbQcBatchReportItem> {


    /**
     * 批量 插入
     */
    void batchAddQcBatchReportItem(@Param("conditions") List<TbQcBatchReportItem> conditions);
}
