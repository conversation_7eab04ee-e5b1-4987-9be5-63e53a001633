package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.model.TbInstrumentReportItem;
import org.mapstruct.Mapper;

/**
 * <p>
 * InstrumentReportItemConverter
 * 仪器报告项目 转换
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/26 19:29
 */
@Mapper(componentModel = "spring")
public interface InstrumentReportItemConverter {

    TbInstrumentReportItem convertDto2Entity(InstrumentReportItemDto instrumentReportItemDto);

    InstrumentReportItemDto convertEntity2Dto(TbInstrumentReportItem instrumentReportItem);

}
