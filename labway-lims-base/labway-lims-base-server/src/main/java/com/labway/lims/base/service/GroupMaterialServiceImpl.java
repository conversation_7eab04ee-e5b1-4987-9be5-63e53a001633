package com.labway.lims.base.service;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.business.center.third.ncc.request.NccMaterialStockSearchForLimsRequest;
import com.labway.business.center.third.ncc.service.LimsApiService;
import com.labway.business.center.third.ncc.vo.material.LimsMaterialStockVo;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.consts.RedisConsts;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.GroupMaterialDetailDto;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import com.labway.lims.base.api.dto.GroupMaterialPageDto;
import com.labway.lims.base.api.dto.GroupMaterialSheetDto;
import com.labway.lims.base.api.dto.MaterialDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.MaterialService;
import com.labway.lims.base.api.service.RandomStringService;
import com.labway.lims.base.api.vo.GroupMaterialPageVo;
import com.labway.lims.base.mapper.TbGroupMaterialMapper;
import com.labway.lims.base.mapstruct.GroupMaterialConverter;
import com.labway.lims.base.model.TbGroupMaterial;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.api.EnvDetector.BUSINESS_CENTER_ORG_CODE;

/**
 * 专业组物料信息 service impl
 *
 * <AUTHOR>
 * @since 2023/5/8 17:40
 */
@Slf4j
@DubboService
public class GroupMaterialServiceImpl implements GroupMaterialService {

    @Resource
    private TbGroupMaterialMapper tbGroupMaterialMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RandomStringService randomStringService;
    @Resource
    private GroupMaterialConverter groupMaterialConverter;
    @DubboReference
    private GroupService groupService;
    @Resource
    private MaterialService materialService;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private GroupMaterialService groupMaterialService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private LimsApiService limsApiService2Impl;
    @Resource
    private Environment environment;
    @Value("${material.assist.expireTime:300}")
    private Integer materialExpireTime;


    @Override
    public List<GroupMaterialDto> selectAll() {
        final LambdaQueryWrapper<TbGroupMaterial> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbGroupMaterial::getOrgId, LoginUserHandler.get().getOrgId())
                .orderByDesc(TbGroupMaterial::getCreateDate);

        return tbGroupMaterialMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    private GroupMaterialDto convert(TbGroupMaterial material) {
        if (Objects.isNull(material)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(material), GroupMaterialDto.class);
    }

    @Override
    public List<GroupMaterialDto> seleceGroupMaterialById(Collection<Long> materialIds) {
        if (CollectionUtils.isEmpty(materialIds)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbGroupMaterial> query = Wrappers.lambdaQuery(TbGroupMaterial.class);
        query.eq(TbGroupMaterial::getGroupId, LoginUserHandler.get().getGroupId());
        query.in(TbGroupMaterial::getMaterialId, materialIds);
        return JSON.parseArray(JSON.toJSONString(tbGroupMaterialMapper.selectList(query)), GroupMaterialDto.class);
    }

    @Override
    @Transactional
    public void addGroupMaterials(List<GroupMaterialDto> gms) {

        List<List<GroupMaterialDto>> partitionList = ListUtils.partition(gms, 500);
        partitionList.forEach(item -> tbGroupMaterialMapper.addBatch(item));
    }

    @Override
    public long addGroupMaterial(long materialId, ProfessionalGroupDto group) {

        final MaterialDto materialDto = materialService.selectByMaterialId(materialId);

        if (Objects.isNull(materialDto)) {
            throw new IllegalStateException("当前物料不存在");
        }

        final TbGroupMaterial material = new TbGroupMaterial();
        material.setGroupMaterialId(snowflakeService.genId());
        material.setMaterialId(materialId);
        material.setMainUnitInventory(materialDto.getMainUnitInventory());
        material.setAssistUnitInventory(materialDto.getAssistUintInventory());
        material.setValidRemindDay(materialDto.getValidRemindDay());
        material.setGroupId(group.getGroupId());
        material.setGroupName(group.getGroupName());
        material.setOrgId(LoginUserHandler.get().getOrgId());
        material.setOrgName(LoginUserHandler.get().getOrgName());
        material.setCreateDate(new Date());
        material.setCreatorId(LoginUserHandler.get().getUserId());
        material.setCreatorName(LoginUserHandler.get().getNickname());
        material.setUpdateDate(new Date());
        material.setUpdaterName(LoginUserHandler.get().getNickname());
        material.setUpdaterId(LoginUserHandler.get().getUserId());
        material.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbGroupMaterialMapper.insert(material) < 1) {
            throw new IllegalStateException("添加失败");
        }

        log.info("用户 [{}] 新增专业组物料 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(material));
        return material.getMaterialId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByGroupMaterialId(GroupMaterialDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbGroupMaterial target = groupMaterialConverter.fromGroupMaterialDto(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbGroupMaterialMapper.updateById(target) < 1) {
            throw new LimsException("修改专业组物料信息失败");
        }

        log.info("用户 [{}] 修改专业组物料信息成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    public void updateByGroupMaterialIds(GroupMaterialDto dto, Collection<Long> groupMaterialIds) {
        if (CollectionUtils.isEmpty(groupMaterialIds)) {
            return;
        }
        tbGroupMaterialMapper.updateByGroupMaterialIds(dto, groupMaterialIds);

    }

    @Override
    public List<GroupMaterialDto> selectByGroupIdAndMaterialIds(long groupId, Collection<Long> materialIds) {
        if (CollectionUtils.isEmpty(materialIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbGroupMaterial> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGroupMaterial::getGroupId, groupId);
        queryWrapper.in(TbGroupMaterial::getMaterialId, materialIds);
        queryWrapper.eq(TbGroupMaterial::getIsDelete, YesOrNoEnum.NO.getCode());

        return groupMaterialConverter.fromTbGroupMaterialList(tbGroupMaterialMapper.selectList(queryWrapper));
    }

    @Override
    public GroupMaterialDto selectByGroupIdAndMaterialId(long groupId, long materialId) {

        LambdaQueryWrapper<TbGroupMaterial> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGroupMaterial::getGroupId, groupId);
        queryWrapper.eq(TbGroupMaterial::getMaterialId, materialId);
        queryWrapper.eq(TbGroupMaterial::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return groupMaterialConverter.fromTbGroupMaterial(tbGroupMaterialMapper.selectOne(queryWrapper));

    }

    @Override
    public List<GroupMaterialDto> selectByGroupId(long groupId) {
        LambdaQueryWrapper<TbGroupMaterial> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGroupMaterial::getGroupId, groupId);

        return groupMaterialConverter.fromTbGroupMaterialList(tbGroupMaterialMapper.selectList(queryWrapper));
    }

    @Override
    public void deleteGroupMaterials(List<Long> materialIds, long groupId) {
        final LambdaQueryWrapper<TbGroupMaterial> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbGroupMaterial::getGroupId, groupId).in(TbGroupMaterial::getMaterialId, materialIds);

        tbGroupMaterialMapper.delete(wrapper);
    }

    @Override
    public void deleteAllGroupMaterials(long groupId) {
        final LambdaQueryWrapper<TbGroupMaterial> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbGroupMaterial::getGroupId, groupId);

        tbGroupMaterialMapper.delete(wrapper);
    }

    @Override
    public List<GroupMaterialDetailDto> selectByGroupIdAndMaterialCodes(long groupId,
                                                                        Collection<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return Collections.emptyList();
        }
        return tbGroupMaterialMapper.selectByGroupIdAndMaterialCodes(groupId, materialCodes);

    }

    /**
     * 导入专业组物料维护数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer importGroupMaterial(List<GroupMaterialSheetDto> groupMaterialSheetDtos) {
        final StopWatch watch = new StopWatch();
        watch.start("查询所有的物料信息");
        List<MaterialDto> materialDtos = materialService.selectAll();
        Map<String, MaterialDto> materialMapByCode =
                materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialCode, Function.identity(), (a, b) -> a));
        watch.stop();

        watch.start("查询专业组");
        // 查询专业组
        List<ProfessionalGroupDto> professionalGroupDtos = groupService.selectByAll(LoginUserHandler.get().getOrgId());
        Map<String, ProfessionalGroupDto> groupMapByName =
                professionalGroupDtos.stream()
                        .collect(Collectors.toMap(ProfessionalGroupDto::getGroupName, Function.identity(), (a, b) -> a));
        watch.stop();

        watch.start("校验数据");
        checkGroupMaterialSheetData(groupMaterialSheetDtos, materialMapByCode, groupMapByName);
        watch.stop();

        watch.start("插入数据库");
        int count = 0;
        Date now = new Date();
        Map<String, List<GroupMaterialSheetDto>> groupMaterialSheetMapByName =
                groupMaterialSheetDtos.stream().collect(Collectors.groupingBy(GroupMaterialSheetDto::getSheetName));

        for (Map.Entry<String, List<GroupMaterialSheetDto>> entry : groupMaterialSheetMapByName.entrySet()) {
            final LinkedList<GroupMaterialDto> gms = new LinkedList<>();

            ProfessionalGroupDto group = groupMapByName.get(entry.getKey());
            List<GroupMaterialSheetDto> materialSheetDtos = entry.getValue();
            Long groupId = group.getGroupId();
            final LinkedList<Long> ids = snowflakeService.genIds(materialSheetDtos.size());

            for (GroupMaterialSheetDto materialSheet : materialSheetDtos) {
                MaterialDto material = materialMapByCode.get(materialSheet.getMaterialCode());

                final GroupMaterialDto gm = new GroupMaterialDto();
                gm.setGroupMaterialId(ids.pop());
                gm.setMaterialId(material.getMaterialId());
                gm.setMainUnitInventory(material.getMainUnitInventory());
                gm.setAssistUnitInventory(material.getAssistUintInventory());
                gm.setValidRemindDay(0);
                gm.setGroupId(groupId);
                gm.setGroupName(group.getGroupName());
                gm.setOrgId(LoginUserHandler.get().getOrgId());
                gm.setOrgName(LoginUserHandler.get().getOrgName());
                gm.setCreateDate(now);
                gm.setCreatorId(LoginUserHandler.get().getUserId());
                gm.setCreatorName(LoginUserHandler.get().getNickname());
                gm.setUpdateDate(now);
                gm.setUpdaterName(LoginUserHandler.get().getNickname());
                gm.setUpdaterId(LoginUserHandler.get().getUserId());
                gm.setIsDelete(YesOrNoEnum.NO.getCode());
                gms.add(gm);
            }

            addGroupMaterials(gms);
            count += gms.size();

            materialSheetDtos.forEach(item -> {
                // 记录操作日志
                rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                        TraceLog.newInstance().setModule(TraceLogModuleEnum.GROUP_MATERIAL.getDesc())
                                .setContent(String.format("专业组 [%s] 导入新增 [%s] 物料", group.getGroupName(), item.getMaterialName()))
                                .toJSONString());
            });
        }
        watch.stop();

        log.info("物料导入耗时：\n{}", watch.prettyPrint(TimeUnit.MILLISECONDS));

        return count;
    }

    /**
     * 专业组物料信息-分页
     * @param groupMaterialPageVo
     * @return
     */
    @Override
    public GroupMaterialPageDto groupMaterialPage(GroupMaterialPageVo groupMaterialPageVo) {

        Long groupId = groupMaterialPageVo.getGroupId();
        String searchKey = groupMaterialPageVo.getSearchKey();
        GroupMaterialPageDto groupMaterialPageDto = new GroupMaterialPageDto();

        if (Objects.isNull(groupId)) {
            return groupMaterialPageDto;
        }

        // 查询专业组下的所有物料
        List<GroupMaterialDto> groupMaterials = this.selectByGroupId(groupId);

        if (CollectionUtils.isEmpty(groupMaterials)) {
            return groupMaterialPageDto;
        }

        // 过滤物料id
        final Set<Long> materialIds =
                groupMaterials.stream().filter(e -> Objects.nonNull(e.getMaterialId()))
                        .map(GroupMaterialDto::getMaterialId).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(materialIds)) {
            return groupMaterialPageDto;
        }

        // 查询所有物料信息
        List<MaterialDto> materials = materialService.selectByIds(materialIds);

        // 关键字匹配
        if (StringUtils.isNotBlank(searchKey)) {
            materials = materials.stream().filter(e -> e.getMaterialName().contains(searchKey)
                            || e.getMaterialCode().contains(searchKey))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(materials)) {
            return groupMaterialPageDto;
        }

        // 分页处理
        List<List<MaterialDto>> partitionMaterial = ListUtils.partition(materials, groupMaterialPageVo.getPageSize());
        if (groupMaterialPageVo.getPage() > partitionMaterial.size()) {
            return groupMaterialPageDto;
        }

        // 获取分页索引
        Integer index = groupMaterialPageVo.getPage() - 1;

        if (index < 0) {
            return groupMaterialPageDto;
        }

        // 获取分页数据
        List<MaterialDto> resultMaterialDtos = partitionMaterial.get(index);
        if (CollectionUtils.isEmpty(resultMaterialDtos)) {
            return groupMaterialPageDto;
        }

        // 获取物料现存量<物料编码，库存辅数量>
        Map<String, BigDecimal> materialAssistUnitInventoryMap = queryMaterialAssistUnitInventory(resultMaterialDtos.stream().map(MaterialDto::getMaterialCode).distinct().collect(Collectors.toList()));
        resultMaterialDtos.forEach(e -> e.setCurrentAssistUnitInventory(materialAssistUnitInventoryMap.get(e.getMaterialCode())));

        // 数据转换
        List<GroupMaterialPageDto.GroupMaterialDataDto> materialDtos = JSON.parseArray(JSON.toJSONString(resultMaterialDtos), GroupMaterialPageDto.GroupMaterialDataDto.class);
        groupMaterialPageDto.setPage(groupMaterialPageVo.getPage());
        groupMaterialPageDto.setPageSize(groupMaterialPageVo.getPageSize());
        groupMaterialPageDto.setTotalPage(partitionMaterial.size());
        groupMaterialPageDto.setTotalCount(materials.size());
        groupMaterialPageDto.setDataVos(materialDtos);

        return groupMaterialPageDto;
    }

    /**
     * 获取物料的现存辅数量
     * @param materialCodes
     * @return
     */
    private Map<String, BigDecimal> queryMaterialAssistUnitInventory(List<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return Collections.emptyMap();
        }

        Map<String, BigDecimal> materialAssistUnitInventoryMap = new HashMap<>();

        List<String> redisCacheInventorys = materialCodes.stream().map(e -> RedisConsts.MATERIAL_ASSIST_INVENTORY + e).collect(Collectors.toList());
        // 调用业务中台查询物料现存辅数量
        // dev-1.1.2.1 分批从redis查， 否则会报异常： ERR the worker queue is full, and the request cannot be excecuted
        // List<String> inventorys = stringRedisTemplate.opsForValue().multiGet(redisCacheInventorys);
        List<String> inventorys = new ArrayList<>();
        final List<List<String>> partition = Lists.partition(redisCacheInventorys, 1000);
        for (List<String> list : partition) {
            inventorys.addAll(stringRedisTemplate.opsForValue().multiGet(list));
        }

        List<String> noCacheMaterialCodes = new ArrayList<>();

        // 循环处理物料现存辅数量
        for (int i = 0; i < materialCodes.size(); i++) {
            String materialCode = materialCodes.get(i);
            String materialInventory = inventorys.get(i);

            if (StringUtils.isBlank(materialInventory)) {
                noCacheMaterialCodes.add(materialCode);
                continue;
            }

            materialAssistUnitInventoryMap.put(materialCode, new BigDecimal(materialInventory));
        }

        if (CollectionUtils.isEmpty(noCacheMaterialCodes)) {
            return materialAssistUnitInventoryMap;
        }

        // 调用业务中台查询物料现存辅数量
        NccMaterialStockSearchForLimsRequest request = new NccMaterialStockSearchForLimsRequest();
        request.setOrgId(environment.getProperty(BUSINESS_CENTER_ORG_CODE));
        request.setMaterialCodes(noCacheMaterialCodes);
        Response<List<LimsMaterialStockVo>> listResponse = limsApiService2Impl.searchMaterialStockForLims(request);
        if (!listResponse.isSuccess()) {
            log.error("调用业务中台查询物料现存辅数量失败，请求参数：{}，响应结果：{}", JSON.toJSONString(request), JSON.toJSONString(listResponse));
            return materialAssistUnitInventoryMap;
        }

        // 获取业务中台查询的辅数量映射关系
        Map<String, BigDecimal> materialNumberMap = listResponse.getData().stream()
                .collect(Collectors.toMap(LimsMaterialStockVo::getMaterialCode, LimsMaterialStockVo::getAssistNumber, (o1, o2) -> o2));

        // 填充业务总台查询的辅数量
        materialAssistUnitInventoryMap.putAll(materialNumberMap);

        // 缓存业务中台查询的辅数量
        Map<String, String> cacheMaterialNumberMap = new HashMap<>();
        materialNumberMap.forEach((key, value) -> cacheMaterialNumberMap.put(RedisConsts.MATERIAL_ASSIST_INVENTORY + key, value.toString()));
        stringRedisTemplate.opsForValue().multiSet(cacheMaterialNumberMap);
        // 批量缓存数据 并设置过期时间
        //        multiSetAndExpire(cacheMaterialNumberMap);
        multiSetAndExpire(cacheMaterialNumberMap, materialExpireTime);

        return materialAssistUnitInventoryMap;
    }


    // 批量缓存数据 并设置过期时间 单位秒
    private void multiSetAndExpire(Map<String, String> cacheMaterialNumberMap, int expireTime) {

        // 使用pipeline方式批量设置值和过期时间
        stringRedisTemplate.executePipelined(new RedisCallback<Object>() {
            @Override
            public Object doInRedis(RedisConnection connection) throws DataAccessException {
                for (Map.Entry<String, String> entry : cacheMaterialNumberMap.entrySet()) {
                    connection.setEx(entry.getKey().getBytes(), expireTime, entry.getValue().getBytes());
                }
                return null;
            }
        });

    }


    public void checkGroupMaterialSheetData(
            List<GroupMaterialSheetDto> groupMaterialSheetDtos,
            Map<String, MaterialDto> materialMapByCode,
            Map<String, ProfessionalGroupDto> groupMapByName) {

        Set<String> errorSheet = new HashSet<>();
        List<String> errorMaterial = new ArrayList<>();

        Map<Integer, String> sheetInfoMap =
                groupMaterialSheetDtos
                        .stream()
                        .collect(Collectors.toMap(GroupMaterialSheetDto::getSheetNo, GroupMaterialSheetDto::getSheetName, (a, b) -> a));

        // 根据sheet名称取对应的专业组，校验是否存在
        for (Map.Entry<Integer, String> sheetInfo : sheetInfoMap.entrySet()) {
            Integer sheetNo = sheetInfo.getKey();
            String sheetName = sheetInfo.getValue();

            ProfessionalGroupDto professionalGroupDto = groupMapByName.get(sheetName);
            if (Objects.isNull(professionalGroupDto)) {
                errorSheet.add(String.format("第【%s】个Sheet名字【%s】没有对应的专业组", sheetNo, sheetName));
            }
        }

        // 校验物料数据
        for (GroupMaterialSheetDto sheetDto : groupMaterialSheetDtos) {
            Integer index = sheetDto.getIndex();
            Integer sheetNo = sheetDto.getSheetNo();
            String materialCode = sheetDto.getMaterialCode();
            String materialName = sheetDto.getMaterialName();
            if (Objects.isNull(materialMapByCode.get(materialCode))) {
                errorMaterial.add(String.format("第【%s】个Sheet，第【%s】行，物料【%s】不存在", sheetNo, index, materialName));
            }
        }

        if (CollectionUtils.isNotEmpty(errorSheet) || CollectionUtils.isNotEmpty(errorMaterial)) {
            List<String> errors = errorSheet.stream().sorted().collect(Collectors.toList());
            errors.addAll(errorMaterial);
            throw new IllegalStateException(String.join("<br>", errors));
        }
    }
}
