package com.labway.lims.base.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023/5/11 17:01
 */
@Getter
@Setter
public class FreeSampleItemVo {

    /**
     * id
     */
    private Long applySampleItemId;

    /**
     * 状态(是否免单)
     *
     * @see YesOrNoEnum
     */
    private Integer isFree;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 折前价格
     */
    private BigDecimal feePrice;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

}
