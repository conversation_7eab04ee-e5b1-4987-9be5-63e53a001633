package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDetailDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDto;
import com.labway.lims.base.api.service.ItemPriceBasePackageDetailService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.vo.ItemPriceBasePackageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目价格基准包控制器
 */
@Slf4j
@RestController
@RequestMapping("/item-price-base-package")
public class ItemPriceBasePackageController extends BaseController {

    @Resource
    private ItemPriceBasePackageService itemPriceBasePackageService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private ItemPriceBasePackageDetailService itemPriceBasePackageDetailService;
    @Resource
    private TestItemController testItemController;


    /**
     * 查询未对照的检验项目列表
     */
    @GetMapping("/not-contrast-test-item")
    public Object notContrastTestItem(@RequestParam(required = false) Long packageId) {
        if (Objects.isNull(packageId)) {
            return Collections.emptyList();
        }

        final Set<Long> testItemIds = itemPriceBasePackageDetailService.selectByPackageId(packageId)
                .stream().map(ItemPriceBasePackageDetailDto::getTestItemId).collect(Collectors.toSet());


        return testItemController.orgItems(false,false)
                .stream()
                .filter(f -> CollectionUtils.isEmpty(testItemIds) || !testItemIds.contains(f.getTestItemId())).collect(Collectors.toList());
    }


    @PostMapping("/delete")
    public Object delete(@RequestBody List<Long> packageIds) {

        if (CollectionUtils.isEmpty(packageIds)) {
            throw new IllegalStateException("请选择需要删除的基准包");
        }

        final List<ItemPriceBasePackageDto> packages = itemPriceBasePackageService.selectByIds(packageIds);

        itemPriceBasePackageService.delete(packageIds);

        final List<String> names = packages.stream().map(ItemPriceBasePackageDto::getPackageName).collect(Collectors.toList());
        final String join = StringUtils.join(names, ",");
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.ITEM_PRICE_BASE_PACKAGE.getDesc())
                        .setContent(String.format("删除项目价格基准包 [%s]", join)).toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 修改项目基准包
     */
    @PostMapping("/update")
    public Object update(@RequestBody ItemPriceBasePackageVo vo) {
        final Long packageId = vo.getPackageId();
        if (Objects.isNull(packageId)) {
            throw new IllegalArgumentException("请选择需要修改的基准包");
        }

        final String packageName = vo.getPackageName();
        if (StringUtils.isBlank(packageName)) {
            throw new IllegalArgumentException("基准包名称不能为空");
        }

        if (StringUtils.length(packageName) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("基准包名称不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }

        if (Objects.isNull(vo.getStartDate()) || Objects.isNull(vo.getEndDate())) {
            throw new IllegalArgumentException("基准包有效期不能为空");
        }

        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("基准包有效期结束时间不能早于开始时间");
        }
        final ItemPriceBasePackageDto packageNow = itemPriceBasePackageService.selectById(vo.getPackageId());

        if (Objects.isNull(packageNow)) {
            throw new IllegalArgumentException("基准包不存在修改失败");
        }
        final ItemPriceBasePackageDto target = JSON.parseObject(JSON.toJSONString(vo), ItemPriceBasePackageDto.class);
        itemPriceBasePackageService.update(target);

        String compare = new CompareUtils<ItemPriceBasePackageDto>().compare(packageNow, target);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.ITEM_PRICE_BASE_PACKAGE.getDesc())
                            .setContent(String.format("修改基准包:%s", compare)).toJSONString());
        }

        return Collections.emptyMap();
    }

    /**
     * 基准包列表
     */
    @PostMapping("/list")
    public Object list() {
        List<ItemPriceBasePackageDto> list = itemPriceBasePackageService.list();
        return JSON.parseArray(JSON.toJSONString(list), ItemPriceBasePackageVo.class);
    }


    /**
     * 添加项目基准包
     */
    @PostMapping("/add")
    public Object add(@RequestBody ItemPriceBasePackageVo vo) {
        final String packageName = vo.getPackageName();

        if (StringUtils.isBlank(packageName)) {
            throw new IllegalArgumentException("基准包名称不能为空");
        }

        if (StringUtils.length(packageName) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("基准包名称不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }

        if (Objects.isNull(vo.getStartDate()) || Objects.isNull(vo.getEndDate())) {
            throw new IllegalArgumentException("基准包有效期不能为空");
        }

        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("基准包有效期结束时间不能早于开始时间");
        }

        vo.setEnable(ObjectUtils.defaultIfNull(vo.getEnable(), YesOrNoEnum.YES.getCode()));

        final ItemPriceBasePackageDto dto = JSON.parseObject(JSON.toJSONString(vo), ItemPriceBasePackageDto.class);

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.ITEM_PRICE_BASE_PACKAGE.getDesc())
                        .setContent(String.format("新增 [%s] 基准包", dto.getPackageName())).toJSONString());

        return Map.of("id", itemPriceBasePackageService.add(dto));
    }

}
