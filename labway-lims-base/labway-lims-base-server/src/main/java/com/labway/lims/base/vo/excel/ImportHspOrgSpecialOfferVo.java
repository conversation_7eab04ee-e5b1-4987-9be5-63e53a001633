package com.labway.lims.base.vo.excel;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;

@Data
public class ImportHspOrgSpecialOfferVo implements Serializable {

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 导入的文件
     */
    private MultipartFile file;

    /**
     * 都用string接受参数， 然后转为对应的类型，
     * 抛异常时抛出合理的异常信息
     */
    @Data
    public static class ImportSpecialOfferDto {
        // 行号
        @ExcelIgnore
        private Integer num;

        @ExcelIgnore
        private Long testItemId;
        @ExcelProperty(value = "检验项目编码", index = 1)
        private String testItemCode;
        @ExcelIgnore
        private String testItemName;

        @ExcelProperty(value = "折前单价", index = 3)
        private String feePriceStr;
        @ExcelIgnore
        private BigDecimal feePrice;

        @ExcelProperty(value = "折扣率", index = 4)
        private String discountStr;
        @ExcelIgnore
        private BigDecimal discount;

        @ExcelProperty(value = "折后单价", index = 5)
        private String discountPriceStr;
        @ExcelIgnore
        private BigDecimal discountPrice;


        @ExcelProperty(value = "是否参与阶梯折扣", index = 6)
        private String isTieredPricingStr;
        @ExcelIgnore
        private Integer isTieredPricing;

        @ExcelIgnore
        private String sendTypeCode;
        @ExcelProperty(value = "送检类型", index = 7)
        private String sendType;

        @ExcelProperty(value = "生效日期", index = 8)
        private String startDateStr;
        @ExcelIgnore
        private Date startDate;

        @ExcelProperty(value = "结束日期", index = 9)
        private String endDateStr;
        @ExcelIgnore
        private Date endDate;

        public Date getStartDate() {
            Date startDate = DateUtil.parse(startDateStr);
            return this.toDate(startDate, 0, 0, 0, 0);
        }

        public Date getEndDate() {
            Date endDate = DateUtil.parse(endDateStr);
            return this.toDate(endDate, 23, 59, 59, 999);
        }

        private Date toDate(Date date, int hours, int minutes, int seconds, int minutesInSeconds) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, hours);
            calendar.set(Calendar.MINUTE, minutes);
            calendar.set(Calendar.SECOND, seconds);
            calendar.set(Calendar.MILLISECOND, minutesInSeconds);
            return calendar.getTime();
        }
    }
}
