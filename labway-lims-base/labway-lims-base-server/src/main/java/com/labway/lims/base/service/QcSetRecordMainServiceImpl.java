package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.QcInstrumentReportItemSampleNoDto;
import com.labway.lims.base.api.dto.QcSetRecordMainDto;
import com.labway.lims.base.api.enums.EffectiveEnum;
import com.labway.lims.base.api.service.*;
import com.labway.lims.base.mapper.TbQcInstrumentSampleSettingMapper;
import com.labway.lims.base.mapper.TbQcSetRecordMainMapper;
import com.labway.lims.base.model.TbQcInstrumentSampleSetting;
import com.labway.lims.base.model.TbQcSetRecordMain;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【tb_qc_set_record_main(仪器质控设置记录)】的数据库操作Service实现
 * @createDate 2023-10-31 19:18:30
 */
@Slf4j
@DubboService
public class QcSetRecordMainServiceImpl implements QcSetRecordMainService {
    @Resource
    private TbQcSetRecordMainMapper qcSetRecordMainMapper;
    @Resource
    private QcInstrumentSampleSettingService sampleSettingService;
    @Resource
    private ReportItemService reportItemService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private InstrumentService instrumentService;
    @Resource
    private InstrumentReportItemService instrumentReportItemService;
    @Resource
    private TbQcInstrumentSampleSettingMapper instrumentSampleSettingMapper;
    @Resource
    private SnowflakeService snowflakeService;

    @Resource
    private TbQcInstrumentSampleSettingMapper sampleSettingMapper;

    private static final String QC_SAMPLE_CONTENT = "浓度";

    @Override
    public List<QcSetRecordMainDto> queryDetailListByReportId(Long reportId, Long groupId) {
        if (Objects.isNull(reportId)) {
            return Collections.emptyList();
        }
        //批量查询
        List<QcSetRecordMainDto> list = qcSetRecordMainMapper.selectAllByReportItemIdAndGroupId(reportId, groupId);
        //判断是否为空
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setQcInstrumentSampleNo(QcInstrumentReportItemSampleNoDto instrumentReportItemSampleNoDto) {
        //1. 业务数据判断: 防止脏数据产生
        //1.1 只允许设置本专业组下拥有的仪器
        LoginUserHandler.User user = LoginUserHandler.get();
        List<InstrumentDto> instrumentDtos = instrumentService.selectByGroupId(user.getGroupId());
        if (CollectionUtils.isEmpty(instrumentDtos)) {
            throw new IllegalStateException("当前专业组下未拥有该仪器");
        }
        long count = instrumentDtos.stream().filter(instrumentDto -> instrumentDto.getInstrumentId().equals(instrumentReportItemSampleNoDto.getInstrumentId())).count();
        if (count <= 0) {
            throw new IllegalStateException("当前专业组下未拥有该仪器");
        }
        //1.2 判断该仪器下报告项是否存在
        List<InstrumentReportItemDto> instrumentReportItemDtos = instrumentReportItemService.selectByInstrumentId(instrumentReportItemSampleNoDto.getInstrumentId());
        List<InstrumentReportItemDto> reportItemDtos = instrumentReportItemDtos.stream().filter(instrumentReportItemDto -> instrumentReportItemDto.getInstrumentReportItemId().equals(instrumentReportItemSampleNoDto.getReportItemId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reportItemDtos)) {
            throw new IllegalStateException("不存在该报告项目，请检查");
        }
        InstrumentReportItemDto instrumentReportItemDto = reportItemDtos.stream().findFirst().get();
        //1.3 判断样本号是否重复：同一个专业组下的同一个仪器的报告项目的不同序号的样本号不允许重复
        boolean duplicateSampleNO = false;
        LambdaQueryWrapper<TbQcInstrumentSampleSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbQcInstrumentSampleSetting::getInstrumentId, instrumentReportItemSampleNoDto.getInstrumentId()).eq(TbQcInstrumentSampleSetting::getReportItemId, instrumentReportItemSampleNoDto.getReportItemId()).eq(TbQcInstrumentSampleSetting::getGroupId, user.getGroupId());
        List<TbQcInstrumentSampleSetting> instrumentSampleSettings = instrumentSampleSettingMapper.selectList(queryWrapper);

        //1.3.1 非空时判断样本号是否重复
        if (!CollectionUtils.isEmpty(instrumentSampleSettings)) {
            duplicateSampleNO = instrumentSampleSettings.stream().anyMatch(i -> {
                Integer sort = i.getSort();
                String qcSampleNo = i.getQcSampleNo();

                if (StringUtils.isBlank(instrumentReportItemSampleNoDto.getSampleNo())) {
                    return Boolean.FALSE;
                }
                return !sort.equals(instrumentReportItemSampleNoDto.getSort()) && qcSampleNo.equals(instrumentReportItemSampleNoDto.getSampleNo());
            });
        }
        if (BooleanUtils.isTrue(duplicateSampleNO)) {
            throw new IllegalStateException(String.format("一个报告项目下不允许出现相同的质控样本号【%s】", instrumentReportItemSampleNoDto.getSampleNo()));
        }

        //2. 业务操作
        //2.1 判断样本号信息是否已存在
        TbQcInstrumentSampleSetting sampleSetting = instrumentSampleSettings.stream().filter(sample -> {
            return sample.getSort().equals(instrumentReportItemSampleNoDto.getSort());
        }).findFirst().orElse(null);
        final Date now = new Date();
        if (Objects.isNull(sampleSetting)) {
            //2.2 为空则执行新增操作
            //2.2.1 获取质控规则
            // 获取质控规则

            Long instrumentId = instrumentReportItemSampleNoDto.getInstrumentId();
            String rulesKey = "LABWAY:LILMS-EXAMINE:qc:rules:instrument:" + instrumentId;
            String rules = StringUtils.defaultString(stringRedisTemplate.opsForValue().get(rulesKey));

            TbQcInstrumentSampleSetting sampleSettinEntity = TbQcInstrumentSampleSetting.builder().qcSampleSettigId(snowflakeService.genId()).qcSampleNo(instrumentReportItemSampleNoDto.getSampleNo()).qcSampleContent(QC_SAMPLE_CONTENT).instrumentId(instrumentReportItemSampleNoDto.getInstrumentId()).instrumentName(instrumentReportItemDto.getInstrumentName()).reportItemId(instrumentReportItemSampleNoDto.getReportItemId()).reportItemName(instrumentReportItemDto.getReportItemName()).reportItemCode(instrumentReportItemDto.getReportItemName()).instrumentChannel(instrumentReportItemDto.getInstrumentChannel()).orgId(user.getOrgId()).orgName(user.getOrgName()).rules(rules).status(EffectiveEnum.EFFECTIVE.getIntCode()).statusDesc(EffectiveEnum.EFFECTIVE.getValue())         //枚举类
                    .sort(instrumentReportItemSampleNoDto.getSort()).createId(user.getUserId()).createName(user.getNickname()).updateId(user.getUserId()).updateName(user.getNickname()).isDelete(YesOrNoEnum.NO.getCode()).selfAttribute1(StringUtils.EMPTY).selfAttribute2(StringUtils.EMPTY).selfAttribute3(StringUtils.EMPTY).createDate(now).updateDate(now).groupId(user.getGroupId()).groupCode(user.getGroupCode()).groupName(user.getGroupName()).build();
            int insert = sampleSettingMapper.insert(sampleSettinEntity);
            if (insert < 1) {
                log.error("用户：[{}]，插入数据异常：[{}]", user.getUserId(), JSON.toJSONString(sampleSettinEntity));
            } else {
                log.info("用户：[{}]，插入数据成功：[{}]", user.getUserId(), JSON.toJSONString(sampleSettinEntity));
            }
        } else {
            //2.3 非空则执行更新操作
            LambdaUpdateWrapper<TbQcInstrumentSampleSetting> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TbQcInstrumentSampleSetting::getInstrumentId, instrumentReportItemSampleNoDto.getInstrumentId()).eq(TbQcInstrumentSampleSetting::getReportItemId, instrumentReportItemSampleNoDto.getReportItemId()).eq(TbQcInstrumentSampleSetting::getGroupId, user.getGroupId()).eq(TbQcInstrumentSampleSetting::getSort, instrumentReportItemSampleNoDto.getSort()).set(StringUtils.isNotBlank(instrumentReportItemSampleNoDto.getSampleNo()), TbQcInstrumentSampleSetting::getQcSampleNo, instrumentReportItemSampleNoDto.getSampleNo()).set(TbQcInstrumentSampleSetting::getInstrumentChannel, instrumentReportItemDto.getInstrumentChannel()).set(TbQcInstrumentSampleSetting::getUpdateId, user.getUserId()).set(TbQcInstrumentSampleSetting::getUpdateName, user.getNickname()).set(TbQcInstrumentSampleSetting::getUpdateDate, now);
            instrumentSampleSettingMapper.update(null, updateWrapper);
        }
    }

    @Override
    public Map<Integer, String> queryInstrumentReportQcSampleNo(Long instrumentReportId) {
        LoginUserHandler.User user = LoginUserHandler.get();
        LambdaQueryWrapper<TbQcInstrumentSampleSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbQcInstrumentSampleSetting::getReportItemId, instrumentReportId);
        List<TbQcInstrumentSampleSetting> sampleSettings = sampleSettingMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(sampleSettings)) {
            return Collections.emptyMap();
        }
        return sampleSettings.stream().collect(Collectors.toMap(TbQcInstrumentSampleSetting::getSort, TbQcInstrumentSampleSetting::getQcSampleNo));

    }


    /**
     * tbQcSetRecordMain实体类转换成QcSetRecordMainDto
     *
     * @param tbQcSetRecordMain 实体类对象
     * @return dto
     */
    public QcSetRecordMainDto convert(TbQcSetRecordMain tbQcSetRecordMain) {
        return JSON.parseObject(JSON.toJSONString(tbQcSetRecordMain), QcSetRecordMainDto.class);
    }
}




