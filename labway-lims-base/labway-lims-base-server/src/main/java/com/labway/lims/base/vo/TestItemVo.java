package com.labway.lims.base.vo;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 检验项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class TestItemVo implements Serializable {


    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 别名
     */
    private String aliasName;

    /**
     * 检验方法ID
     */
    private String examMethodCode;

    /**
     * 检验方法名称
     */
    private String examMethodName;

    /**
     * 名称缩写
     */
    private String shortName;

    /**
     * 管型 code
     */
    private String tubeCode;

    /**
     * 管型 name
     */
    private String tubeName;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 是否支持外送
     */
    private Integer enableExport;

    /**
     * 外送回传报告时间
     */
    private BigDecimal exportDate;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

    /**
     * 外送机构名称
     */
    private String exportOrgName;

    /**
     * 项目类型ID
     *
     * @see ItemTypeEnum
     */
    private String itemType;


    /**
     * 样本类型ID
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;

    /**
     * 是否it3000
     */
    private Integer enableIt3000;

    /**
     * 是否计费
     */
    private Integer enableFee;

    /**
     * 收费编码
     */
    private String feeCode;

    /**
     * 收费名称
     */
    private String feeName;

    /**
     * 收费价格
     */
    private BigDecimal feePrice;

    /**
     * 财务专业组
     */
    private Long financeGroupId;

    /**
     * 财务专业组名称
     */
    private String financeGroupName;

    /**
     * 基本量
     */
    private BigDecimal basicQuantity;

    /**
     * 复查量
     */
    private BigDecimal checkQuantity;

    /**
     * 死腔量
     */
    private BigDecimal deadSpaceQuantity;

    /**
     * 存放说明
     */
    private String stashRemark;

    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 机构 ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 1:已经删除 0未删除
     *
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 报告项目
     */
    private List<ReportItemVo> reportItems;

    /**
     * 是否启用(0未启动 1启动)
     * @see YesOrNoEnum
     */
    private Integer enable;


    /**
     * 检验日期
     */
    private Integer testDate;

    /**
     * 限制性别
     * 0 不限制，1 男，2 女
     */
    @Compare("限制性别")
    private Integer limitSex;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 是否是组套
     */
    private boolean isPackage;

    /**
     * 组套ID
     */
    private Long packageId;

    /**
     * 组套名称
     */
    private String packageName;

    /**
     * 组套项目
     */
    private List<TestItemVo> packageItems;


    /**
     * 获取检验项目首字母
     */
    public String getFirstLetter() {
        if (StringUtils.isBlank(this.testItemName)) {
            return StringUtils.EMPTY;
        }
        return StringUtils.lowerCase(PinyinUtil.getFirstLetter(this.testItemName, StringUtils.EMPTY));
    }

    public BigDecimal getPrice() {
        return this.feePrice;
    }

}
