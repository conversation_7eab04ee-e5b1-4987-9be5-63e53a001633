package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrganizationMsgDto;
import com.labway.lims.base.api.service.HspOrganizationMsgService;
import com.labway.lims.base.vo.HspOrganizationMsgVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 送检机构
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/hsp-organization-msg")
public class HspOrganizationMsgController extends BaseController {

    @Resource
    private HspOrganizationMsgService hspOrganizationMsgService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 根据送检机构位置获取提示消息
     */
    @GetMapping("/hsp-org-position-tip-msg")
    public Object selectTipMsgByHspOrgPosition(@RequestParam(required = false) Long hspOrgId,
        @RequestParam(required = false) String positionCode) {
        if (Objects.isNull(hspOrgId) || StringUtils.isBlank(positionCode)) {
            return Collections.emptyMap();
        }

        final HspOrganizationMsgDto tipMsg =
            hspOrganizationMsgService.selectByHspOrgIdAndPositionId(hspOrgId, positionCode);
        if (Objects.isNull(tipMsg)) {
            return Collections.emptyMap();
        }

        final HspOrganizationMsgVo vo = JSON.parseObject(JSON.toJSONString(tipMsg), HspOrganizationMsgVo.class);

        final Integer enable = vo.getEnable();
        if (Objects.equals(enable, 0)) {
            return Collections.emptyMap();
        }

        return Map.of("tip", vo.getMsgContent());
    }

    /**
     * 新增机构提示消息
     */
    @PostMapping("/add")
    public Object addHspOrgTipMsg(@RequestBody HspOrganizationMsgVo vo) {
        final Long hspOrgId = vo.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalArgumentException("请选择机构");
        }

        final String msgContent = vo.getMsgContent();
        if (StringUtils.isBlank(msgContent)) {
            throw new IllegalArgumentException("提示消息不能为空");
        }

        if (StringUtils.length(msgContent) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("提示消息不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        if (StringUtils.isBlank(vo.getMsgPositionCode()) || StringUtils.isBlank(vo.getMsgPosition())) {
            throw new IllegalArgumentException("提示位置不能为空");
        }

        final HspOrganizationMsgDto msg = JSON.parseObject(JSON.toJSONString(vo), HspOrganizationMsgDto.class);

        hspOrganizationMsgService.addHspOrgTipMsg(msg);

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_ORGANIZATION_MSG_LOG.getDesc()).setContent(String
                .format("新增 [%s] 下 [%s] 位置提示信息:[%s]", msg.getHspOrgName(), msg.getMsgPosition(), msg.getMsgContent()))
                .toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 修改机构提示消息
     */
    @PostMapping("/update")
    public Object updateHspOrgTipMsg(@RequestBody HspOrganizationMsgVo vo) {
        if (Objects.isNull(vo.getMsgId())) {
            throw new IllegalArgumentException("消息id不能为空");
        }
        final String msgContent = vo.getMsgContent();
        if (StringUtils.isBlank(msgContent)) {
            throw new IllegalArgumentException("提示消息不能为空");
        }

        if (StringUtils.length(msgContent) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("提示消息不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        if (StringUtils.isBlank(vo.getMsgPositionCode()) || StringUtils.isBlank(vo.getMsgPosition())) {
            throw new IllegalArgumentException("提示位置不能为空");
        }
        HspOrganizationMsgDto hspOrganizationMsgDtoNow = hspOrganizationMsgService.selectTipMsgByMsgId(vo.getMsgId());
        if (Objects.isNull(hspOrganizationMsgDtoNow)) {
            throw new LimsException("提示消息不存在");
        }
        final HspOrganizationMsgDto msg = JSON.parseObject(JSON.toJSONString(vo), HspOrganizationMsgDto.class);

        hspOrganizationMsgService.updateTipMsgByMsgId(msg);

        String compare = new CompareUtils<HspOrganizationMsgDto>().compare(hspOrganizationMsgDtoNow, msg);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_ORGANIZATION_MSG_LOG.getDesc())
                    .setContent(String.format("修改送检机构 [%s] 提示信息: [%s]", hspOrganizationMsgDtoNow.getHspOrgName(), compare))
                    .toJSONString());
        }
        return Collections.emptyMap();
    }

    /**
     * 查询所有的机构提示信息
     */
    @PostMapping("/selectHspOrgTipMsg")
    public Object selectAllHspOrgTipMsg() {
        final List<HspOrganizationMsgDto> msgs = hspOrganizationMsgService.selectAllHspOrgTipMsg();
        return JSON.parseArray(JSON.toJSONString(msgs), HspOrganizationMsgVo.class);
    }

    /**
     * 删除机构提示消息
     */
    @PostMapping("/deleteHspOrgTipMsg")
    public Object deleteHspOrgTipMsg(@RequestBody List<Long> msgIds) {
        List<HspOrganizationMsgDto> hspOrganizationMsgDtos = hspOrganizationMsgService.selectTipMsgByMsgIds(msgIds);

        hspOrganizationMsgService.deleteHspOrgTipMsgByMsgIds(msgIds);

        hspOrganizationMsgDtos.forEach(item -> rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_ORGANIZATION_MSG_LOG.getDesc())
                .setContent(String.format("删除送检机构 [%s] 下 [%s] 位置提示信息", item.getHspOrgName(), item.getMsgPosition()))
                .toJSONString()));
        return Collections.emptyMap();
    }

}
