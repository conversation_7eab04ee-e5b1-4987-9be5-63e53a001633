package com.labway.lims.base.vo;

import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AddInstrumentReportItemResultExchangeVo {
    /**
     * 仪器报告项目ID
     */
    private Long instrumentReportItemId;


    /**
     * 仪器结果值
     */
    private String instrumentResult;

    /**
     * 转换结果值
     */
    private String exchangeResult;


    /**
     * 条件
     * ≥, >, =, <, ≤
     */
    @Compare("条件")
    private String formulaMax;

    /**
     * 条件值
     */
    @Compare("条件值")
    private String formulaMaxValue;

    /**
     * 条件
     * <, ≤
     */
    @Compare("条件")
    private String formulaMin;

    /**
     * 条件值
     */
    @Compare("条件值")
    private String formulaMinValue;


}
