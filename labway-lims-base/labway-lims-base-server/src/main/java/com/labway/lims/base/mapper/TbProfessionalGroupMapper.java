package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.UserProfessionalGroupDto;
import com.labway.lims.base.model.TbProfessionalGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 专业组 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbProfessionalGroupMapper extends BaseMapper<TbProfessionalGroup> {

    /**
     * 根据用户id查询
     */
    List<TbProfessionalGroup> selectByUserId(@Param("userId") long userId);

    List<UserProfessionalGroupDto> selectUserGroupByUserIds(@Param("userIds") Collection<Long> userIds);
}
