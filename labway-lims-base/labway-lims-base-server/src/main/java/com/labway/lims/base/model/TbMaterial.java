package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/8 17:26
 */
@Getter
@Setter
@TableName("tb_material")
public class TbMaterial implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物料ID
     */
    @TableId
    private Long materialId;

    /**
     * 物资编号
     */
    private String materialCode;

    /**
     * 物资类别
     */
    private String type;

    /**
     * 物料类型编码
     */
    private String typeCode;

    /**
     * 物资名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 方法学
     */
    private String methodology;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 注册证名称
     */
    private String registrationName;

    /**
     * 储存温度
     */
    private String storageTemperature;

    /**
     * 厂家
     */
    private String manufacturers;

    /**
     * 主单位总库存
     */
    private BigDecimal mainUnitInventory;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     * 辅单位总库存
     */
    private BigDecimal assistUintInventory;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 有效期
     */
    private Date validDate;

    /**
     * 开瓶有效期
     */
    private Date openValidDate;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

    /**
     * 临近效期天数
     */
    private Integer validRemindDay;

    /**
     * 同步唯一码
     */
    private String fromNo;

    /**
     * 信息hash
     */
    private String infoHash;

    /**
     * 预期测试数
     */
    private Integer expectedTestCount;

}
