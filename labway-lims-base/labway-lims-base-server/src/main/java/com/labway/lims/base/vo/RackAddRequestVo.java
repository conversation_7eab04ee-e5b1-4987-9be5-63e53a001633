package com.labway.lims.base.vo;

import com.labway.lims.api.enums.base.RackHoleColumnOrderEnum;
import com.labway.lims.api.enums.base.RackHolePriorityOrderEnum;
import com.labway.lims.api.enums.base.RackHoleRowOrderEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 试管架
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class RackAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 试管架编码
     */
    private String rackCode;


    /**
     * 试管架名称
     */
    private String rackName;

    /**
     * 试管架类型编码
     */
    private String rackTypeCode;

    /**
     * 多少行
     */
    private Integer row;

    /**
     * 多少列
     */
    private Integer column;

    /**
     * 是否启用(0未启用 1启用)
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 行顺序 1,↓先上后下 0,↑先下后上
     * @see RackHoleRowOrderEnum
     */
    private Integer rowOrder;

    /**
     * 列顺序 1,→先左后右  0,←先右后左
     * @see RackHoleColumnOrderEnum
     */
    private Integer columnOrder;

    /**
     * 顺序优先级 1,--行优先 0,|列优先
     * @see RackHolePriorityOrderEnum
     */
    private Integer orderPriority;

}
