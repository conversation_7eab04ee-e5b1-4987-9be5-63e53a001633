package com.labway.lims.base.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 试管架
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class RackAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 试管架编码
     */
    private String rackCode;


    /**
     * 试管架名称
     */
    private String rackName;

    /**
     * 试管架类型编码
     */
    private String rackTypeCode;

    /**
     * 多少行
     */
    private Integer row;

    /**
     * 多少列
     */
    private Integer column;

    /**
     * 是否自动销毁
     *
     * @see YesOrNoEnum
     */
    private Integer isDestroyed;

    /**
     * 存储多少天后销毁
     */
    private Integer storageDays;

    /**
     * 是否启用(0未启用 1启用)
     * @see YesOrNoEnum
     */
    private Integer enable;

}
