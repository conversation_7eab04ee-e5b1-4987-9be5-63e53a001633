package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 技术数据维护  条码管理
 * @description tb_barcode_setting
 * <AUTHOR>
 * @date 2024-01-22
 */
@Data
@TableName(value = "tb_barcode_setting")
public class TbBarcodeSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long barcodeSettingId;

    /**
     * 机构id
     */
    private Long hspOrgId;

    /**
     * 机构编码
     */
    private String hspOrgCode;

    /**
     * 机构名称
     */
    private String hspOrgName;

    /**
     * 起始编码
     */
    private String startCode;

    /**
     * 当前序号
     */
    private Long currentNumber;

    /**
     * 位数
     */
    private Integer barcodePlace;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 新增人id
     */
    private Long createrId;

    /**
     * 新增人名
     */
    private String createrName;

    /**
     * 修改人id
     */
    private Long updaterId;

    /**
     * 修改人名
     */
    private String updaterName;

    /**
     * 1 删除  0没有删除
     */
    private Integer isDelete;

    /**
     * 新增时间
     */
    private LocalDateTime createrDate;

    /**
     * 修改时间
     */
    private LocalDateTime updaterDate;

    /**
     * 条码类型  1条码  2主条码
     */
    private Integer barcodeType;

}