package com.labway.lims.base.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.api.enums.base.SendTypeEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.AddHspOrgSpecialOfferProjectDto;
import com.labway.lims.base.api.dto.AddHspOrgSpecialOfferProjectSendTypeDto;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.dto.HspOrgSpecialOfferDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.dto.UpdateSpecialOfferDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.api.service.HspOrgSpecialOfferService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.vo.HspOrgSpecialOfferVo;
import com.labway.lims.base.vo.excel.ImportHspOrgSpecialOfferVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户特价项目
 */
@Slf4j
@RequestMapping("/hsp-org-special-offer")
@RestController
public class HspOrgSpecialOfferController extends BaseController {

    @Resource
    private HspOrgSpecialOfferService hspOrgSpecialOfferService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @DubboReference
    private TestItemService testItemService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @Resource
    private DictService dictService;

    /**
     * 修改特价项目
     */
    @PostMapping("/update-special-offer")
    public Object updateSpecialOffer(@RequestBody UpdateSpecialOfferDto vo) {
        if (Objects.isNull(vo.getOfferId())) {
            throw new IllegalArgumentException("请选择要修改的特价项目");
        }

        final Date startDate = vo.getStartDate();
        final Date endDate = vo.getEndDate();
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            throw new IllegalArgumentException("请选择生效日期和结束日期");
        }

        if (startDate.after(endDate)) {
            throw new IllegalArgumentException("生效日期不能大于结束日期");
        }

        if (StringUtils.isAnyBlank(vo.getSendTypeCode(), vo.getSendType())) {
            throw new IllegalArgumentException("请选择送检类型");
        }

        final BigDecimal discount = vo.getDiscount();
        if (Objects.isNull(discount)) {
            throw new IllegalArgumentException("请输入折扣率");
        }

        if (discount.doubleValue() < NumberUtils.INTEGER_ZERO || discount.doubleValue() > NumberUtils.INTEGER_ONE) {
            throw new IllegalArgumentException("折扣率必须 >=0 且 <=1");
        }
        if (Objects.isNull(vo.getFeePrice())) {
            throw new IllegalArgumentException("折前价格不可为空");
        }
        if (Objects.isNull(vo.getDiscountPrice())) {
            throw new IllegalArgumentException("折后价格不可为空");
        }
        final HspOrgSpecialOfferDto hspOrgSpecialOfferNow = hspOrgSpecialOfferService.selectByOfferId(vo.getOfferId());
        if (Objects.isNull(hspOrgSpecialOfferNow)) {
            throw new IllegalArgumentException("特价项目不存在");
        }

        HspOrgSpecialOfferDto update = JSON.parseObject(JSON.toJSONString(vo), HspOrgSpecialOfferDto.class);

        hspOrgSpecialOfferService.updateSpecialOffer(update);

        String compare = new CompareUtils<HspOrgSpecialOfferDto>().compare(hspOrgSpecialOfferNow, update);

        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.CUSTOMER_SPECIAL_PRICE_ITEM_MAINTENANCE.getDesc())
                            .setContent(String.format("修改特价项目: [%s]", compare)).toJSONString());
        }
        return Map.of();
    }

    /**
     * 聚合送检机构列表
     */
    @GetMapping("/get-hsp-org-list")
    public Object getHspOrgList() {
        Collection<Long> hspOrgIds = hspOrgSpecialOfferService.selectHasSpecialProjectHspOrg();
        if (CollectionUtils.isEmpty(hspOrgIds)) {
            return Collections.emptyList();
        }

        final Map<Long, HspOrganizationDto> hspOrganizationMap =
                hspOrganizationService.selectByHspOrgIdsAsMap(hspOrgIds);

        return hspOrgIds.stream().map(m -> {
                    final HspOrganizationDto hspOrganization = hspOrganizationMap.get(m);
                    if (Objects.isNull(hspOrganization)) {
                        return null;
                    }

                    return Map.of("hspOrgId", hspOrganization.getHspOrgId(), "hspOrgCode", hspOrganization.getHspOrgCode(),
                            "hspOrgName", hspOrganization.getHspOrgName(), "createDate", hspOrganization.getCreateDate());
                }).filter(Objects::nonNull)
                .sorted(Comparator.comparing(obj -> (Date) obj.get("createDate"), Collections.reverseOrder()))
                .collect(Collectors.toList());
    }

    /**
     * 删除客户特价项目根据id
     */
    @PostMapping("/delete-by-id")
    public Object deleteById(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("请选择要删除的特价项目");
        }

        final List<HspOrgSpecialOfferDto> offers = hspOrgSpecialOfferService.selectByOfferIds(ids);
        if (CollectionUtils.isEmpty(offers)) {
            return Map.of();
        }

        hspOrgSpecialOfferService.deleteByOfferId(ids);

        rabbitMQService
                .convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                        TraceLog.newInstance().setModule(TraceLogModuleEnum.CUSTOMER_SPECIAL_PRICE_ITEM_MAINTENANCE.getDesc())
                                .setType("登录")
                                .setContent(String.format("删除阶梯折扣\n%s",
                                        offers.stream().reduce(new StringBuilder(),
                                                (sb, e) -> sb.append(String.format("送检机构 [%s]", e.getHspOrgName()))
                                                        .append(String.format(" 项目 [%s]", e.getTestItemCode())).append(System.lineSeparator()),
                                                StringBuilder::append).toString()))
                                .toJSONString());

        return Map.of();
    }

    /**
     * 删除客户特价项目根据送检机构id
     */
    @PostMapping("/delete-by-hsp-org-id")
    public Object deleteByHspOrgId(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("请选择要删除的特价项目");
        }

        final Map<Long, HspOrganizationDto> map = hspOrganizationService.selectByHspOrgIdsAsMap(ids);
        for (Long id : ids) {
            if (!map.containsKey(id)) {
                throw new IllegalArgumentException("客商不存在");
            }
        }

        hspOrgSpecialOfferService.deleteByHspOrgId(ids);

        final String names =
                map.values().stream().map(HspOrganizationDto::getHspOrgName).collect(Collectors.joining("、"));

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setType("删除")
                        .setModule(TraceLogModuleEnum.CUSTOMER_SPECIAL_PRICE_ITEM_MAINTENANCE.getDesc())
                        .setContent(String.format("删除客商 [%s] 下所有阶梯折扣", names)).toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 添加客户特价项目
     */
    @PostMapping("/add-hsp-org-special-offer-project")
    public Object addHspOrgSpecialOfferProject(@RequestBody AddHspOrgSpecialOfferProjectDto vo) {
        final Long hspOrgId = vo.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalArgumentException("请选择送检机构");
        }

        final Date startDate = vo.getStartDate();
        final Date endDate = vo.getEndDate();
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            throw new IllegalArgumentException("请选择生效日期和结束日期");
        }

        if (startDate.after(endDate)) {
            throw new IllegalArgumentException("生效日期不能大于结束日期");
        }

        final List<Long> testItemIds = vo.getTestItemIds();
        if (Objects.isNull(testItemIds) || testItemIds.isEmpty()) {
            throw new IllegalArgumentException("请选择检验项目");
        }
        List<AddHspOrgSpecialOfferProjectSendTypeDto> sendTypeList = vo.getSendTypeList();
        if (CollectionUtils.isEmpty(sendTypeList) || sendTypeList.stream()
                .anyMatch(obj -> StringUtils.isAnyBlank(obj.getSendType(), obj.getSendTypeCode()))) {
            throw new IllegalArgumentException("请选择送检类型");
        }

        final BigDecimal discount = vo.getDiscount();
        if (Objects.isNull(discount)) {
            throw new IllegalArgumentException("请输入折扣率");
        }

        if (discount.doubleValue() < NumberUtils.INTEGER_ZERO || discount.doubleValue() > NumberUtils.INTEGER_ONE) {
            throw new IllegalArgumentException("折扣率必须 >=0 且 <=1");
        }

        final HspOrganizationDto organization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(organization)) {
            throw new IllegalArgumentException("送检机构不存在");
        }

        final String lockKey = getLockKey(hspOrgId);

        try {
            hspOrgSpecialOfferService.addHspOrgSpecialOfferProject(vo);
        } finally {
            stringRedisTemplate.delete(lockKey);
        }

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.CUSTOMER_SPECIAL_PRICE_ITEM_MAINTENANCE.getDesc())
                        .setType("新增")
                        .setContent(String.format("添加特价项目 送检机构 [%s] 参与阶梯折扣 [%s] 折扣率 [%s] 生效日期 [%s] 结束日期 [%s]",
                                organization.getHspOrgName(),
                                Objects.equals(YesOrNoEnum.YES.getCode(), vo.getIsTieredPricing()) ? "是" : "否", vo.getDiscount(),
                                DateFormatUtils.format(vo.getStartDate(), "yyyy-MM-dd HH:mm:ss"),
                                DateFormatUtils.format(vo.getEndDate(), "yyyy-MM-dd HH:mm:ss")))
                        .toJSONString());

        return Collections.emptyMap();
    }

    private String getLockKey(Long hspOrgId) {
        String lockKey =
                String.format("%s%s%s", redisPrefix.getBasePrefix(), "add-hsp-org-special-offer-project:", hspOrgId);
        if (BooleanUtils.isNotTrue(
                stringRedisTemplate.opsForValue().setIfAbsent(lockKey, StringUtils.EMPTY, Duration.ofMinutes(5)))) {
            throw new IllegalArgumentException("正在添加客户特价项目，请稍后再试");
        }
        return lockKey;
    }

    /**
     * 查询客户特价项目
     */
    @GetMapping("/select-hsp-org-special-offer-project")
    public Object selectHspOrgSpecialOfferProject(@RequestParam(required = false) Long hspOrgId) {
        if (Objects.isNull(hspOrgId)) {
            return Collections.emptyList();
        }

        List<HspOrgSpecialOfferDto> specialOffers = hspOrgSpecialOfferService.selectHspOrgSpecialOfferProject(hspOrgId);
        final Collection<Long> testItemIds =
                specialOffers.stream().map(HspOrgSpecialOfferDto::getTestItemId).collect(Collectors.toSet());

        final Map<Long, TestItemDto> testItemMap = testItemService.selectByTestItemIdsAsMap(testItemIds);

        return specialOffers.stream().map(obj -> {
            HspOrgSpecialOfferVo vo = JSON.parseObject(JSON.toJSONString(obj), HspOrgSpecialOfferVo.class);
            TestItemDto testItemDto = testItemMap.get(obj.getTestItemId());
            if (Objects.nonNull(testItemDto)) {
                vo.setTestItemName(testItemDto.getTestItemName());
                vo.setTestItemCode(testItemDto.getTestItemCode());
                vo.setFinanceGroup(testItemDto.getFinanceGroupName());
                vo.setGroupName(testItemDto.getGroupName());
                vo.setExamMethodName(testItemDto.getExamMethodName());
                vo.setEnable(testItemDto.getEnable());
            }
            vo.setOriginalPrice(obj.getFeePrice());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 导出客户特价项目
     */
    @PostMapping("/export-hsp-org-special-offer-project")
    public Object exportHspOrgSpecialOfferProject(@RequestBody List<Long> hspOrgIds) throws IOException {
        if (CollectionUtils.isEmpty(hspOrgIds)) {
            throw new IllegalArgumentException("请选择送检机构导出");
        }
        final List<HspOrganizationDto> hspOrganizationDtos = hspOrganizationService.selectByHspOrgIds(hspOrgIds);

        // 查询多个送检机构的特价信息
        List<HspOrgSpecialOfferDto> specialOffers = hspOrgSpecialOfferService.selectByHspOrgIds(hspOrgIds);

        // 检验项目信息
        final Collection<Long> testItemIds =
                specialOffers.stream().map(HspOrgSpecialOfferDto::getTestItemId).collect(Collectors.toSet());
        final Map<Long, TestItemDto> testItemMap = testItemService.selectByTestItemIdsAsMap(testItemIds);

        // 填充信息
        final Map<Long, List<HspOrgSpecialOfferVo>> byHspOrgIdMap = specialOffers.stream().map(obj -> {
            HspOrgSpecialOfferVo vo = JSON.parseObject(JSON.toJSONString(obj), HspOrgSpecialOfferVo.class);
            TestItemDto testItemDto = testItemMap.get(obj.getTestItemId());
            if (Objects.nonNull(testItemDto)) {
                vo.setTestItemName(testItemDto.getTestItemName());
                vo.setTestItemCode(testItemDto.getTestItemCode());
                vo.setFinanceGroup(testItemDto.getFinanceGroupName());
                vo.setGroupName(testItemDto.getGroupName());
                vo.setExamMethodName(testItemDto.getExamMethodName());
                vo.setEnable(testItemDto.getEnable());
            }
            vo.setOriginalPrice(obj.getFeePrice());
            return vo;
        }).collect(Collectors.groupingBy(HspOrgSpecialOfferVo::getHspOrgId));

        final File tempFile = File.createTempFile("special-offer-item-export", null);

        try (ExcelWriter writer = ExcelUtil.getBigWriter(); FileOutputStream fos = new FileOutputStream(tempFile)) {

            List<Object> builder = new LinkedList<>(List.of(
                    "序号", "送检机构编码", "送检机构名称", "检验项目编码", "检验项目名称", "检验方法学",
                    "折前单价", "折扣率", "折后单价", "是否参与阶梯折扣",
                    "送检类型", "生效日期", "结束日期",
                    "专业组", "财务专业组"));
            writer.writeHeadRow(builder);

            // 按机构挨个填充
            for (HspOrganizationDto hspOrganizationDto : hspOrganizationDtos) {
                final List<HspOrgSpecialOfferVo> list = byHspOrgIdMap.getOrDefault(hspOrganizationDto.getHspOrgId(), Collections.emptyList());
                list.sort(Comparator.comparing(HspOrgSpecialOfferVo::getOfferId).reversed());

                // 填充数据
                for (int i = 0; i < list.size(); i++) {
                    final HspOrgSpecialOfferVo hspOrgSpecialOfferVo = list.get(i);
                    builder.clear();
                    builder.add(i + 1);
                    builder.add(hspOrganizationDto.getHspOrgCode());
                    builder.add(hspOrganizationDto.getHspOrgName());
                    builder.add(hspOrgSpecialOfferVo.getTestItemCode());
                    builder.add(hspOrgSpecialOfferVo.getTestItemName());
                    builder.add(hspOrgSpecialOfferVo.getExamMethodName());
                    builder.add(hspOrgSpecialOfferVo.getOriginalPrice());
                    builder.add(hspOrgSpecialOfferVo.getDiscount());
                    builder.add(hspOrgSpecialOfferVo.getDiscountPrice());
                    builder.add(YesOrNoEnum.selectByCode(hspOrgSpecialOfferVo.getIsTieredPricing()).getDesc());
                    builder.add(hspOrgSpecialOfferVo.getSendType());
                    builder.add(hspOrgSpecialOfferVo.getStartDate());
                    builder.add(hspOrgSpecialOfferVo.getEndDate());
                    builder.add(hspOrgSpecialOfferVo.getGroupName());
                    builder.add(hspOrgSpecialOfferVo.getFinanceGroup());
                    writer.writeRow(builder);
                }

            }

            writer.flush(fos);
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                            String.format("attachment; filename=%s", URLEncoder.encode("客户特价项目.xlsx", StandardCharsets.UTF_8)))
                    .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                    .body(new FileSystemResource(tempFile));

        }
    }

    /**
     * 导入客户特价项目
     */
    @PostMapping(value = "/import-hsp-org-special-offer-project", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Object importHspOrgSpecialOfferProject(@RequestParam Long hspOrgId,
                                                  @RequestParam MultipartFile file) throws IOException {
        Assert.notNull(hspOrgId, "送检机构不能为空");
        if (Objects.isNull(file) || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        String fileName = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(fileName);
        if (!"xls".equalsIgnoreCase(extension) && !"xlsx".equalsIgnoreCase(extension)) {
            throw new IllegalStateException("只允许上传 xls 和 xlsx 格式的文件");
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        // 读取文件数据
        List<ImportHspOrgSpecialOfferVo.ImportSpecialOfferDto> hspOrgSpecialOfferDtos = EasyExcelFactory.read(file.getInputStream())
                .head(ImportHspOrgSpecialOfferVo.ImportSpecialOfferDto.class)
                .sheet(0).doReadSync();
        if (CollectionUtils.isEmpty(hspOrgSpecialOfferDtos)) {
            log.info(String.format("用户 %s 导入送检机构特价项目， 内容为空", user.getNickname()));
            return List.of();
        }

        // 获取送检机构
        final HspOrganizationDto hspOrganizationDto = hspOrganizationService.selectByHspOrgId(hspOrgId);
        Assert.notNull(hspOrganizationDto, "送检机构不存在");

        final String lockKey = this.getLockKey(hspOrgId);
        try {
            // 校验并填充信息
            List<HspOrgSpecialOfferDto> dtos = this.getHspOrgSpecialOfferDtos(hspOrganizationDto, hspOrgSpecialOfferDtos, user);

            hspOrgSpecialOfferService.importHspOrgSpecialOfferProject(dtos);

        } finally {
            stringRedisTemplate.delete(lockKey);
        }


        // 发送MQ
        for (ImportHspOrgSpecialOfferVo.ImportSpecialOfferDto dto : hspOrgSpecialOfferDtos) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.CUSTOMER_SPECIAL_PRICE_ITEM_MAINTENANCE.getDesc())
                            .setType("新增")
                            .setContent(String.format("添加特价项目 送检机构 [%s] 参与阶梯折扣 [%s] 折扣率 [%s] 生效日期 [%s] 结束日期 [%s]",
                                    hspOrganizationDto.getHspOrgName(),
                                    Objects.equals(YesOrNoEnum.YES.getCode(), dto.getIsTieredPricing()) ? "是" : "否", dto.getDiscount(),
                                    DateFormatUtils.format(dto.getStartDate(), "yyyy-MM-dd HH:mm:ss"),
                                    DateFormatUtils.format(dto.getEndDate(), "yyyy-MM-dd HH:mm:ss")))
                            .toJSONString());
        }

        return List.of();

    }

    private List<HspOrgSpecialOfferDto> getHspOrgSpecialOfferDtos(HspOrganizationDto hspOrganizationDto,
                                                                  List<ImportHspOrgSpecialOfferVo.ImportSpecialOfferDto> hspOrgSpecialOfferDtos,
                                                                  LoginUserHandler.User user) {

        // 获取检验项目
        final Set<String> testItemCodes = hspOrgSpecialOfferDtos.stream().map(ImportHspOrgSpecialOfferVo.ImportSpecialOfferDto::getTestItemCode).collect(Collectors.toSet());
        final Map<String, TestItemDto> testItemCodeMap = testItemService.selectByTestItemCodes(testItemCodes, user.getOrgId())
                .stream().collect(Collectors.toMap(TestItemDto::getTestItemCode, Function.identity(), (a, b) -> a));

        // 查询送检类型， 对应的code
        final Map<String, String> applyTypeMap = dictService.selectByDictType(DictEnum.VISIT_TYPE.name())
                .stream().collect(Collectors.toMap(DictItemDto::getDictName, DictItemDto::getDictCode));
        // 填充全部类型
        applyTypeMap.put(SendTypeEnum.SEND_TYPE_ALL.getDesc(), SendTypeEnum.SEND_TYPE_ALL.getCode());

        for (int i = 0; i < hspOrgSpecialOfferDtos.size(); i++) {
            final ImportHspOrgSpecialOfferVo.ImportSpecialOfferDto dto = hspOrgSpecialOfferDtos.get(i);
            // 行号
            final int num = i + 2;
            dto.setNum(num);

            // 校验 检验项目 信息
            if (StringUtils.isBlank(dto.getTestItemCode())) {
                throw new IllegalArgumentException(String.format("%s行,检验项目编码不能为空", num));
            }
            final TestItemDto testItemDto = testItemCodeMap.get(dto.getTestItemCode());
            Assert.notNull(testItemDto, String.format("%s行,检验项目编码无效", num));
            dto.setTestItemId(testItemDto.getTestItemId());
            dto.setTestItemName(testItemDto.getTestItemName());
            // 主分支-V1.1.4.6 【【客户特价项目维护】导入时，默认折前单价取导入文件中的单价，若导入文件中为空，则取检验项目价格】
            // https://www.tapd.cn/tapd_fe/59091617/story/detail/1159091617001003144
            if (Objects.nonNull(dto.getFeePriceStr()) && NumberUtils.isCreatable(dto.getFeePriceStr())) {
                dto.setFeePrice(new BigDecimal(dto.getFeePriceStr()));
            } else {
                dto.setFeePrice(testItemDto.getFeePrice());
            }
            // 送检类型code
            if (StringUtils.isBlank(dto.getSendType())) {
                throw new IllegalArgumentException(String.format("%s行,送检类型不能为空", num));
            }
            final String sendTypeCode = applyTypeMap.get(dto.getSendType());
            Assert.notNull(sendTypeCode, String.format("%s行,送检类型无效", num));
            dto.setSendTypeCode(sendTypeCode);

            // 折扣率两位小数
            Assert.isTrue(NumberUtils.isParsable(dto.getDiscountStr()), String.format("%s行,折扣率不是数字", num));
            dto.setDiscount(new BigDecimal(dto.getDiscountStr()));
            if (dto.getDiscount().doubleValue() < NumberUtils.INTEGER_ZERO || dto.getDiscount().doubleValue() > NumberUtils.INTEGER_ONE) {
                throw new IllegalArgumentException(String.format("%s行,折扣率必须 >=0 且 <=1", num));
            }
            // 折后单价
            if (StringUtils.isNotBlank(dto.getDiscountPriceStr())) {
                Assert.isTrue(NumberUtils.isParsable(dto.getDiscountPriceStr()), String.format("%s行,折后单价不是数字", num));
                dto.setDiscountPrice(new BigDecimal(dto.getDiscountPriceStr()).setScale(2, RoundingMode.HALF_UP));
            }

            // 时间
            try {
                if (dto.getStartDate().after(dto.getEndDate())) {
                    throw new IllegalArgumentException(String.format("%s行,生效日期不能大于结束日期", num));
                }
            } catch (Exception e) {
                throw new IllegalArgumentException(String.format("%s行,请选择有效的生效日期和结束日期", num));
            }

            // 是否参与阶梯折扣
            final Optional<YesOrNoEnum> yesOrNoEnumOptional = Arrays.stream(YesOrNoEnum.values())
                    .filter(e -> Objects.equals(e.getDesc(), StringUtils.trim(dto.getIsTieredPricingStr()))).findFirst();
            if (yesOrNoEnumOptional.isEmpty()) {
                throw new IllegalArgumentException(String.format("%s行,是否参与阶梯折扣, 请填写是或否", num));
            }
            dto.setIsTieredPricing(yesOrNoEnumOptional.get().getCode());

        }
        final Date now = new Date();
        return JSON.parseArray(JSON.toJSONString(hspOrgSpecialOfferDtos), HspOrgSpecialOfferDto.class)
                .stream().peek(e -> {
                    BigDecimal discountPrice = e.getDiscountPrice();
                    if (Objects.isNull(discountPrice)) {
                        discountPrice = e.getFeePrice().multiply(e.getDiscount()).setScale(2, RoundingMode.HALF_UP);
                    }

                    e.setDiscountPrice(discountPrice);
                    e.setHspOrgId(hspOrganizationDto.getHspOrgId());
                    e.setHspOrgName(hspOrganizationDto.getHspOrgName());
                    e.setOrgId(user.getOrgId());
                    e.setOrgName(user.getOrgName());
                    e.setCreateDate(now);
                    e.setUpdateDate(now);
                    e.setUpdaterId(user.getUserId());
                    e.setUpdaterName(user.getNickname());
                    e.setCreatorId(user.getUserId());
                    e.setCreatorName(user.getNickname());
                    e.setIsDelete(YesOrNoEnum.NO.getCode());
                }).collect(Collectors.toList());
    }
}
