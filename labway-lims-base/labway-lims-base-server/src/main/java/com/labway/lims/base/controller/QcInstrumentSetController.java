package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.QcInstrumentReportItemSampleNoDto;
import com.labway.lims.base.api.service.QcSetInstrumentRuleService;
import com.labway.lims.base.api.service.QcSetRecordMainService;
import com.labway.lims.base.vo.QcInstrumentReportItemSampleNoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;

/**
 * 检验管理——质控数据——质控设置
 * 仪器质控设置
 *
 * <AUTHOR>
 * @Date 2023/10/31 16:34
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/qcSet")
public class QcInstrumentSetController extends BaseController {

    @Resource
    private QcSetRecordMainService qcSetRecordMainService;
    @Resource
    private QcSetInstrumentRuleService setInstrumentRuleService;

    /**
     * 根据报告项目id查询质控样本号
     *
     * @param instrumentReportId 仪器报告项目id
     * @return 样本信息
     */
    @GetMapping("/queryCustomerReportQcSampleNo")
    public Object queryCustomerReportQcSampleNo(@RequestParam Long instrumentReportId) {
        if (Objects.isNull(instrumentReportId)) {
            return Collections.emptyMap();
        }
        return qcSetRecordMainService.queryInstrumentReportQcSampleNo(instrumentReportId);
    }

    /**
     * 根据仪器报告项id来查询当前仪器下的报告项的质控列表信息
     *
     * @param reportId 报告项id
     * @return
     */
    @PostMapping("/queryQcReportBatchList")
    public Object queryQcReportBatchList(@RequestParam Long reportId) {
        final Long groupId = LoginUserHandler.get().getGroupId();
        return qcSetRecordMainService.queryDetailListByReportId(reportId, groupId);
    }

    /**
     * 根据仪器id和报告项目id来设置当前报告项目下所有的质控批号的样本浓度信息    ：每一个样品浓度对应一个不同的样品号，用于机器的识别
     *
     * @param vo 更新内容数据
     * @return
     */
    @PostMapping("/setQcInstrumentSampleNo")
    public Object setQcInstrumentSampleNo(@RequestBody QcInstrumentReportItemSampleNoVo vo) {
        log.info("接收到的参数===》 {}", JSON.toJSON(vo));
        //参数检查校验
        if (Objects.isNull(vo.getInstrumentId())) {
            throw new IllegalStateException("请选择仪器【instrumentId为空】");
        }
        if (Objects.isNull(vo.getReportItemId())) {
            throw new IllegalStateException("请选择报告项目【reportItemId为空】");
        }
        if (Objects.isNull(vo.getSort())) {
            throw new IllegalStateException("请选择浓度【sort为空】");
        }
        if (StringUtils.isNotBlank(vo.getSampleNo()) && !NumberUtils.isParsable(vo.getSampleNo())) {
            throw new IllegalStateException("样本号不是数字，请重新输入");
        }
        //数据类型转换
        QcInstrumentReportItemSampleNoDto instrumentReportItemSampleNoDto = JSON.parseObject(JSON.toJSONString(vo), QcInstrumentReportItemSampleNoDto.class);

        qcSetRecordMainService.setQcInstrumentSampleNo(instrumentReportItemSampleNoDto);
        //统一返回形式
        return Collections.emptyMap();
    }

//    /**           功能已更改
//     * 查询多个报告设置信息：根据机器id和报告项id来查询
//     */
//    @PostMapping("/queryQcBatchReportInfos")
//    public Object queryQcBatchReportInfos(@RequestBody QueryQcBatchInfoVo queryQcBatchInfoVo) {
//        //1. 关键数据判断
//        final String instrumentId = queryQcBatchInfoVo.getInstrumentId();
//        final String reportIds = queryQcBatchInfoVo.getReportIds();
//        if (StringUtils.isAllBlank(instrumentId, reportIds)) {
//            return Collections.emptyMap();
//        }
//
//        //2. 数据类型转换
//        boolean isNumber = StringUtils.isNumeric(instrumentId);
//        if(!isNumber){
//            throw new IllegalStateException("仪器id类型匹配异常");
//        }
//
//
//        //数据查询
//        QcBatchReportSettingInfoDto reportsSettingInfo = qcSetRecordMainService.getReportsSettingInfo(Arrays.asList(reportIds.split(",")),
//                LoginUserHandler.get().getCustomerId(), instrumentId);
//
//        //非空判断和类型转换
//        if (reportsSettingInfo != null) {
//            QcBatchReportSettingInfoVo settingInfoVo = JSON.parseObject(JSON.toJSONString(reportsSettingInfo), QcBatchReportSettingInfoVo.class);
//            //作转换
//            return ResultVo.success(settingInfoVo);
//        }
//        return ResultVo.success();
//    }
//
//    /**
//     * * 删除QC质控信息
//     */
//    @PostMapping("/deleteQcReportBatch")        //TODO X
//    public ResultVo deleteQcReportBatch(@RequestParam String mainId) {
//        qcSetRecordMainService.deleteBatchRecordByMainId(mainId);
//        return ResultVo.success();
//    }
//
//    /**
//     * 添加QC质控信息
//     */
//    @PostMapping("/addInstrumentQCBatch")       //TODO X
//    public ResultVo addInstrumentQCBatch(@RequestBody AddInstrumentQcBatchVO addInstrumentQcBatchVO) {
//
//        //直接进行判断，在VO类中，添加了判断的业务逻辑
//        ResultVo validResult = addInstrumentQcBatchVO.validParam();
//        if (validResult.isFailure()) {
//            return validResult;
//        }
//

    /**
     * 设置仪器质控规则
     *
     * @param rules 规则
     * @return
     */
    @GetMapping("/setInstrumentRules")
    public Object setInstrumentRule(@RequestParam String rules, @RequestParam Long instrumentId) {
        setInstrumentRuleService.addRules(rules, instrumentId);
        return Collections.emptyMap();
    }
}
