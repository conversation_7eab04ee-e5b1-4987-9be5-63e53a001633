package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.HspOrgPricingDto;
import com.labway.lims.base.api.service.HspOrgPricingService;
import com.labway.lims.base.mapper.TbHspOrgPricingMapper;
import com.labway.lims.base.mapstruct.HspOrgPricingConverter;
import com.labway.lims.base.model.TbHspOrgPricing;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 客户阶梯折扣信息 Service impl
 *
 * <AUTHOR>
 * @since 2023/5/4 13:48
 */
@Slf4j
@DubboService
public class HspOrgPricingServiceImpl implements HspOrgPricingService {

    @Resource
    private TbHspOrgPricingMapper tbHspOrgPricingMapper;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private HspOrgPricingConverter hspOrgPricingConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addHspOrgPricing(HspOrgPricingDto dto) {
        final TbHspOrgPricing target = new TbHspOrgPricing();

        BeanUtils.copyProperties(dto, target);
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        target.setTieredPriceId(ObjectUtils.defaultIfNull(dto.getTieredPriceId(), snowflakeService.genId()));
        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbHspOrgPricingMapper.insert(target) < 1) {
            throw new IllegalStateException("添加客户阶梯折扣失败");
        }

        log.info("用户 [{}] 新增客户阶梯折扣 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(target));

        return target.getTieredPriceId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByTieredPriceId(HspOrgPricingDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbHspOrgPricing target = new TbHspOrgPricing();
        BeanUtils.copyProperties(dto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbHspOrgPricingMapper.updateById(target) < 1) {
            throw new LimsException("修改客户阶梯折扣失败");
        }

        log.info("用户 [{}] 修改客户阶梯折扣成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }

    @Override
    public List<HspOrgPricingDto> selectByOrgId(long orgId) {
        if (orgId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbHspOrgPricing> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbHspOrgPricing::getOrgId, orgId);
        queryWrapper.eq(TbHspOrgPricing::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbHspOrgPricing::getCreateDate);
        return hspOrgPricingConverter.hspOrgPricingDtoListFromTbObjList(tbHspOrgPricingMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public HspOrgPricingDto selectByTieredPriceId(long tieredPriceId) {
        if (tieredPriceId < 1) {
            return null;
        }
        LambdaQueryWrapper<TbHspOrgPricing> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbHspOrgPricing::getTieredPriceId, tieredPriceId);
        queryWrapper.eq(TbHspOrgPricing::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return hspOrgPricingConverter.hspOrgPricingDtoFromTbObj(tbHspOrgPricingMapper.selectOne(queryWrapper));
    }

    @Override
    public boolean isExistIntersectionData(long hspOrgId, Date startDate, Date endDate, BigDecimal beforeMinPrice,
        BigDecimal beforeMaxPrice, long excludeTieredPriceId) {

        LambdaQueryWrapper<TbHspOrgPricing> queryWrapper = Wrappers.lambdaQuery();

        // 排除 传入 阶梯折扣id
        queryWrapper.ne(excludeTieredPriceId > 0, TbHspOrgPricing::getTieredPriceId, excludeTieredPriceId);

        // 时间 存在交集
        queryWrapper.and(wrapper -> wrapper
            .and(w1 -> w1.ge(TbHspOrgPricing::getStartDate, startDate)
                .and(w2 -> w2.le(TbHspOrgPricing::getStartDate, endDate)))
            .or(w3 -> w3.le(TbHspOrgPricing::getStartDate, startDate)
                .and(w4 -> w4.ge(TbHspOrgPricing::getEndDate, endDate)))
            .or(w5 -> w5.ge(TbHspOrgPricing::getEndDate, startDate)
                .and(w6 -> w6.le(TbHspOrgPricing::getEndDate, endDate))));

        // 折前总额 存在交集
        queryWrapper.and(wrapper -> wrapper
            .and(w1 -> w1.ge(TbHspOrgPricing::getBeforeMinPrice, beforeMinPrice)
                .and(w2 -> w2.le(TbHspOrgPricing::getBeforeMinPrice, beforeMaxPrice)))
            .or(w3 -> w3.le(TbHspOrgPricing::getBeforeMinPrice, beforeMinPrice)
                .and(w4 -> w4.ge(TbHspOrgPricing::getBeforeMaxPrice, beforeMaxPrice)))
            .or(w5 -> w5.ge(TbHspOrgPricing::getBeforeMaxPrice, beforeMinPrice)
                .and(w6 -> w6.le(TbHspOrgPricing::getBeforeMaxPrice, beforeMaxPrice))));
        //送检机构
        queryWrapper.eq(TbHspOrgPricing::getHspOrgId, hspOrgId);
        return tbHspOrgPricingMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public List<HspOrgPricingDto> selectByHspOrgIdsAndDateRange(Collection<Long> hspOrgIds, Date startDate,
        Date endDate) {
        if (CollectionUtils.isEmpty(hspOrgIds) || Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbHspOrgPricing> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.in(TbHspOrgPricing::getHspOrgId, hspOrgIds);

        // 时间 存在交集
        queryWrapper.and(wrapper -> wrapper
            .and(w1 -> w1.ge(TbHspOrgPricing::getStartDate, startDate)
                .and(w2 -> w2.le(TbHspOrgPricing::getStartDate, endDate)))
            .or(w3 -> w3.le(TbHspOrgPricing::getStartDate, startDate)
                .and(w4 -> w4.ge(TbHspOrgPricing::getEndDate, endDate)))
            .or(w5 -> w5.ge(TbHspOrgPricing::getEndDate, startDate)
                .and(w6 -> w6.le(TbHspOrgPricing::getEndDate, endDate))));

        return hspOrgPricingConverter.hspOrgPricingDtoListFromTbObjList(tbHspOrgPricingMapper.selectList(queryWrapper));
    }

    @Override
    public List<HspOrgPricingDto> selectByTotalDiscount(BigDecimal sum) {
        if (Objects.isNull(sum) || Objects.equals(sum, BigDecimal.ZERO)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbHspOrgPricing> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(TbHspOrgPricing::getBeforeMinPrice, sum);
        queryWrapper.le(TbHspOrgPricing::getBeforeMaxPrice, sum);
        return hspOrgPricingConverter.hspOrgPricingDtoListFromTbObjList(tbHspOrgPricingMapper.selectList(queryWrapper));
    }
}
