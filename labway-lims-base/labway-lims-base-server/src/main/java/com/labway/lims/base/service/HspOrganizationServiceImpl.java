package com.labway.lims.base.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.DefaultApplyType;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.mapper.TbHspOrganizationMapper;
import com.labway.lims.base.model.TbHspOrganization;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "hsg-org")
public class HspOrganizationServiceImpl implements HspOrganizationService {

    @Resource
    private TbHspOrganizationMapper tbHspOrganizationMapper;

    @Resource
    private SnowflakeService snowflakeService;

    @Resource
    private HspOrganizationServiceImpl self;

    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public long addHspOrganization(HspOrganizationDto dto) {

        final LoginUserHandler.User user = LoginUserHandler.get();

        final String hspOrganizationCode = dto.getHspOrgCode();
        final String hspOrganizationName = dto.getHspOrgName();

        // 是否已存在编码
        if (Objects.nonNull(self.selectByHspOrgCode(hspOrganizationCode))) {
            throw new LimsException(String.format("机构编码 [%s] 已存在", hspOrganizationCode));
        }

        // 是否已存在名称
        if (Objects.nonNull(self.selectByHspOrgName(hspOrganizationName, user.getOrgId()))) {
            throw new LimsException(String.format("机构名称 [%s] 已存在", hspOrganizationName));
        }
        final Date now = new Date();

        final TbHspOrganization add = JSON.parseObject(JSON.toJSONString(dto), TbHspOrganization.class);
        add.setApplyTypeNames(StringUtils.defaultIfBlank(add.getApplyTypeNames(), DefaultApplyType.ALL_APPLY_TYPE_NAME));
        add.setRemark(StringUtils.defaultString(dto.getRemark(), StringUtils.EMPTY));
        add.setLocation(StringUtils.defaultString(dto.getLocation(), StringUtils.EMPTY));
        add.setAddress(StringUtils.defaultString(dto.getAddress(), StringUtils.EMPTY));
        add.setContactPhone(StringUtils.defaultString(dto.getContactPhone(), StringUtils.EMPTY));
        add.setOrgId(user.getOrgId());
        add.setOrgName(user.getOrgName());
        add.setCreateDate(now);
        add.setCreatorId(user.getUserId());
        add.setCreatorName(user.getNickname());
        add.setUpdateDate(now);
        add.setUpdaterId(user.getUserId());
        add.setUpdaterName(user.getNickname());
        add.setHspOrgId(snowflakeService.genId());
        add.setSaleDeptCode(StringUtils.defaultString(add.getSaleDeptCode()));
        add.setSaleDeptName(StringUtils.defaultString(add.getSaleDeptName()));
        add.setSaleTypeCode(StringUtils.defaultString(add.getSaleTypeCode()));
        add.setSaleTypeName(StringUtils.defaultString(add.getSaleTypeName()));
        add.setSaleAreaCode(StringUtils.defaultString(add.getSaleAreaCode()));
        add.setSaleAreaName(StringUtils.defaultString(add.getSaleAreaName()));
        add.setInvoice(StringUtils.defaultString(add.getInvoice()));
        if (tbHspOrganizationMapper.insert(add) < 1) {
            throw new IllegalStateException("新建机构失败");
        }

        log.info("用户 [{}] 新建机构 [{}] 成功", user.getNickname(), JSON.toJSONString(dto));

        return add.getHspOrgId();
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByHspOrgCode:' + #hspOrgCode")
    public HspOrganizationDto selectByHspOrgCode(String hspOrgCode) {
        if (StringUtils.isBlank(hspOrgCode)) {
            return null;
        }

        final TbHspOrganization one = tbHspOrganizationMapper.selectOne(Wrappers.lambdaQuery(TbHspOrganization.class).eq(TbHspOrganization::getHspOrgCode, hspOrgCode).last("limit 1"));

        return JSON.parseObject(JSON.toJSONString(one), HspOrganizationDto.class);
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByHspOrgName:'+ #orgId + ':' + #hspOrgName")
    public HspOrganizationDto selectByHspOrgName(String hspOrgName, long orgId) {
        if (StringUtils.isBlank(hspOrgName)) {
            return null;
        }

        final TbHspOrganization one = tbHspOrganizationMapper.selectOne(Wrappers
                .lambdaQuery(TbHspOrganization.class)
                .eq(TbHspOrganization::getHspOrgName, hspOrgName)
                .last("limit 1"));

        return JSON.parseObject(JSON.toJSONString(one), HspOrganizationDto.class);
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByHspOrgId:' + #hspOrgId")
    public HspOrganizationDto selectByHspOrgId(long hspOrgId) {
        final TbHspOrganization one = tbHspOrganizationMapper.selectById(hspOrgId);
        return JSON.parseObject(JSON.toJSONString(one), HspOrganizationDto.class);
    }

    @Override
    public List<HspOrganizationDto> selectByHspOrgIds(Collection<Long> hspOrgIds) {
        if (CollectionUtils.isEmpty(hspOrgIds)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbHspOrganization> in = Wrappers.lambdaQuery(TbHspOrganization.class).in(TbHspOrganization::getHspOrgId, hspOrgIds);
        final List<TbHspOrganization> list = tbHspOrganizationMapper.selectList(in);

        return JSON.parseArray(JSON.toJSONString(list), HspOrganizationDto.class);
    }


    @Nonnull
    @Override
    public List<HspOrganizationDto> selectAll() {
        final List<TbHspOrganization> list = tbHspOrganizationMapper.selectList(Wrappers.lambdaQuery(TbHspOrganization.class)
                .eq(TbHspOrganization::getOrgId, LoginUserHandler.get().getOrgId())
                .orderByDesc(TbHspOrganization::getHspOrgId));

        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return JSON.parseArray(JSON.toJSONString(list), HspOrganizationDto.class);
    }

    @Override
    @CacheEvict(allEntries = true)
    public void updateByHspOrgId(HspOrganizationDto hspOrganization) {

        if (Objects.isNull(hspOrganization) || Objects.isNull(hspOrganization.getHspOrgId())) {
            return;
        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        final TbHspOrganization update = JSON.parseObject(JSON.toJSONString(hspOrganization), TbHspOrganization.class);

        update.setUpdaterId(user.getUserId());
        update.setUpdaterName(user.getNickname());
        update.setUpdateDate(new Date());
        // 不支持修改机构编码和名称
        update.setHspOrgCode(null);
        update.setHspOrgName(null);
        update.setRemark(StringUtils.defaultString(hspOrganization.getRemark(), StringUtils.EMPTY));
        update.setLocation(StringUtils.defaultString(hspOrganization.getLocation(), StringUtils.EMPTY));
        update.setAddress(StringUtils.defaultString(hspOrganization.getAddress(), StringUtils.EMPTY));
        update.setContactPhone(StringUtils.defaultString(hspOrganization.getContactPhone(), StringUtils.EMPTY));
        update.setApplyTypeNames(StringUtils.defaultIfBlank(update.getApplyTypeNames(), DefaultApplyType.ALL_APPLY_TYPE_NAME));

        tbHspOrganizationMapper.updateById(update);
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByHspOrgIds(List<Long> hspOrgIds) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final List<HspOrganizationDto> hspOrganizations = selectByHspOrgIds(hspOrgIds);
        if (CollectionUtils.isEmpty(hspOrganizations)) {
            throw new IllegalStateException("机构不存在");
        }

        if (tbHspOrganizationMapper.deleteBatchIds(hspOrgIds) < 1) {
            throw new IllegalStateException("删除机构失败");
        }

        log.info("用户 [{}] 删除机构 [{}] ", user.getNickname(), hspOrganizations.stream().map(HspOrganizationDto::getHspOrgName).collect(Collectors.joining(",")));

    }

    @Override
    public List<HspOrganizationDto> selectByHspOrgCodes(Collection<String> hspOrgCodes) {
        if (CollectionUtils.isEmpty(hspOrgCodes)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(JSON.toJSONString(tbHspOrganizationMapper.selectList(Wrappers.lambdaQuery(TbHspOrganization.class)
                .in(TbHspOrganization::getHspOrgCode, hspOrgCodes))), HspOrganizationDto.class);
    }

    @Override
    public Map<Long, HspOrganizationDto> selectByHspOrgIdsAsMap(Collection<Long> hspOrgIds) {
        return selectByHspOrgIds(hspOrgIds)
                .stream()
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity(), (a, b) -> a));
    }


}
