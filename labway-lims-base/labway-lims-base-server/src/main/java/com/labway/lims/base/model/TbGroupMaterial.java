package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 专业组物料信息
 * 
 * <AUTHOR>
 * @since 2023/5/8 17:26
 */
@Getter
@Setter
@TableName("tb_group_material")
public class TbGroupMaterial implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业组物料ID
     */
    @TableId
    private Long groupMaterialId;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 主单位总库存
     */
    private BigDecimal mainUnitInventory;

    /**
     * 辅单位总库存
     */
    private BigDecimal assistUnitInventory;

    /**
     * 临近效期天数
     */
    private Integer validRemindDay;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

}
