package com.labway.lims.base.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.service.QcInstrumentReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 质控仪器获取和质控报告项获取
 *
 * <AUTHOR>
 * @Date 2023/11/2 11:49
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/qcInstrumentReport")
public class QcInstrumentReportController extends BaseController {

    @Resource
    private QcInstrumentReportService qcInstrumentReportService;

    /**
     * 根据仪器id来查询当前仪器下所有的报告项
     *
     * @param instrumentId 仪器id
     * @return
     */
    @GetMapping("/select-instrument-report-by-instrument-id")
    public Object selectInstrumentReportByInstrumentId(@RequestParam Long instrumentId) {
        return qcInstrumentReportService.selectInstrumentReportByInstrumentId(instrumentId);
    }

    /**
     * 根据groupId来获取仪器数据
     *
     * @return
     */
    @GetMapping("select-instrument-by-group-id")
    public Object queryInstrumentListByGroupId(@RequestParam Long groupId) {
        if (Objects.isNull(groupId)) {
            throw new IllegalStateException("专业组信息为空");
        }
        if (!(LoginUserHandler.get().getGroupId().equals(groupId))) {
            throw new IllegalStateException("专业组信息匹配异常sss");
        }
        return qcInstrumentReportService.queryInstrumentListByGroupId(groupId);
    }
}