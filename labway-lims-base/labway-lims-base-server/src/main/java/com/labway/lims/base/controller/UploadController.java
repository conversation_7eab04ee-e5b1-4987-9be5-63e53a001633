package com.labway.lims.base.controller;

import ch.qos.logback.core.util.FileSize;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.web.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;

/**
 * 统一上传文件
 */
@Slf4j
@RequestMapping("/file")
@RestController
public class UploadController extends BaseController {

    private static final int DEV_FILE_EXPIRE_DAY = 180;

    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @Resource
    private EnvDetector envDetector;

    @PostMapping("/upload")
    public Object upload(@RequestPart("file") MultipartFile file) throws IOException {

        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        final String url = huaweiObsUtils.upload(file.getInputStream(),
                StringUtils.defaultIfBlank(file.getContentType(), MediaType.APPLICATION_OCTET_STREAM_VALUE),
                envDetector.isDev() || envDetector.isTest() ? DEV_FILE_EXPIRE_DAY : -1);

        final String nickname = Optional.ofNullable(LoginUserHandler.getUnsafe()).map(LoginUserHandler.User::getNickname)
                .orElse(StringUtils.EMPTY);

        log.info("用户 [{}] 上传了文件 [{}] 大小 [{}] 地址 [{}] 有效期 [{}]", nickname,
                file.getOriginalFilename(), FileSize.valueOf(String.valueOf(file.getSize())), url,
                envDetector.isDev() || envDetector.isTest() ? DEV_FILE_EXPIRE_DAY + "天" : "永久");

        return Map.of("url", url);
    }
}
