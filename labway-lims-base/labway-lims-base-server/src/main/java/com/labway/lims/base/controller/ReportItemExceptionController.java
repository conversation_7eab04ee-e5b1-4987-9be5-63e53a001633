package com.labway.lims.base.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentReportItemExceptionDto;
import com.labway.lims.base.api.service.InstrumentReportItemExceptionService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Set;


/**
 * 仪器报告项目异常结果
 */
@RestController
@RequestMapping("/report-item-exception")
public class ReportItemExceptionController extends BaseController {
    @DubboReference
    private InstrumentReportItemExceptionService instrumentReportItemExceptionService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 获取异常结果
     */
    @GetMapping("/exceptions")
    public Object exceptions(@RequestParam Long instrumentReportItemId) {
        if (Objects.isNull(instrumentReportItemId)) {
            throw new IllegalArgumentException("参数错误");
        }
        return instrumentReportItemExceptionService.selectByInstrumentReportItemId(instrumentReportItemId);
    }

    /**
     * 添加参考范围
     */
    @PostMapping("/add")
    public Object add(@RequestBody InstrumentReportItemExceptionDto exception) throws Exception {

        if (Objects.isNull(exception.getInstrumentReportItemId())) {
            throw new IllegalArgumentException("instrumentReportItemId 不能为空");
        }

        exception.setInstrumentReportItemExceptionId(null);

        return Map.of("id", instrumentReportItemExceptionService
                .addInstrumentReportItemException(exception));
    }

    /**
     * 修改参考范围
     */
    @PostMapping("/update")
    public Object update(@RequestBody InstrumentReportItemExceptionDto exception) {
        if (Objects.isNull(exception.getInstrumentReportItemExceptionId())) {
            throw new IllegalArgumentException("参数错误");
        }


        if (!instrumentReportItemExceptionService.updateByInstrumentReportItemExceptionId(exception)) {
            throw new IllegalStateException("修改参考范围失败");
        }


        return Collections.emptyMap();
    }


    /**
     * 删除参考范围
     */
    @DeleteMapping("/delete")
    public Object delete(@RequestBody Set<Long> instrumentReportItemExceptionIds) {


        if (CollectionUtils.isEmpty(instrumentReportItemExceptionIds)) {
            return Collections.emptyMap();
        }

        for (Long instrumentReportItemExceptionId : instrumentReportItemExceptionIds) {
            instrumentReportItemExceptionService.deleteByInstrumentReportItemExceptionId(instrumentReportItemExceptionId);
        }


        return Collections.emptyMap();
    }


}
