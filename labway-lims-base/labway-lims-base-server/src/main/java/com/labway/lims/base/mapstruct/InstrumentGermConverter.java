package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.InstrumentGermDto;
import com.labway.lims.base.model.TbInstrumentGerm;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 仪器细菌 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface InstrumentGermConverter {

    InstrumentGermDto instrumentGermDtoFromTbObj(TbInstrumentGerm obj);

    List<InstrumentGermDto> instrumentGermDtosFromTbObj(List<TbInstrumentGerm> list);


    TbInstrumentGerm tbInstrumentGermFromTbObjDto(InstrumentGermDto obj);

    List<TbInstrumentGerm> tbInstrumentGermsFromTbObjDto(List<InstrumentGermDto> list);
    
}
