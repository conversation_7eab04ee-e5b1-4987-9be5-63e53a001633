package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrgDepositGuaranteeDto;
import com.labway.lims.base.api.service.HspOrgDepositGuaranteeService;
import com.labway.lims.base.api.vo.AddHspOrgDepositGuaranteeBatchVo;
import com.labway.lims.base.vo.HspOrgDepositGuaranteeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 机构保底金维护
 */
@Slf4j
@RestController
@RequestMapping("/hsp-org-deposit-guarantee")
@Validated
public class HspOrgDepositGuaranteeController extends BaseController {

    @DubboReference
    private RabbitMQService rabbitMQService;

    @Resource
    private HspOrgDepositGuaranteeService hspOrgDepositGuaranteeService;

    /**
     * 查询所有机构保底金
     *
     * @param hspOrgName 送检机构名称（模糊查询）
     * @return 机构保底金列表
     */
    @GetMapping("/selectAll")
    public List<HspOrgDepositGuaranteeVo> selectAll(@RequestParam(required = false) String hspOrgName) {
        // 确保模糊查询时传入的参数被正确处理
        String searchName = null;
        if (StringUtils.isNotBlank(hspOrgName)) {
            // 去除首尾空格
            searchName = hspOrgName.trim();

            // 如果参数中包含逗号，可能是前端或其他地方错误添加的，去除它
            if (searchName.contains(",")) {
                log.warn("送检机构名称参数包含逗号，进行清理: [{}]", searchName);
                searchName = searchName.replace(",", "");
            }
        }

        log.info("查询机构保底金，处理后的送检机构名称参数: [{}]", searchName);

        final List<HspOrgDepositGuaranteeDto> guarantees = hspOrgDepositGuaranteeService.selectAll(searchName);

        if (CollectionUtils.isEmpty(guarantees)) {
            return Collections.emptyList();
        }

        return JSON.parseArray(JSON.toJSONString(guarantees), HspOrgDepositGuaranteeVo.class);
    }



    /**
     * 批量新增机构保底金
     */
    @PostMapping("/add-org-deposit-guarantee-batch")
    public Object addOrgDepositGuaranteeBatch(@RequestBody @Valid AddHspOrgDepositGuaranteeBatchVo vo) {
        log.info("批量新增机构保底金入参 -> {}", JSON.toJSONString(vo));

        // 检查生效日期和结束日期的先后顺序
        if (vo.getStartDate().after(vo.getEndDate())) {
            throw new IllegalArgumentException("生效日期不能大于结束日期");
        }

        // 批量添加
        return hspOrgDepositGuaranteeService.addHspOrgDepositGuaranteeBatch(vo);
    }

    /**
     * 修改机构保底金信息
     */
    @PostMapping("/update-org-deposit-guarantee")
    public Object updateOrgDepositGuarantee(@RequestBody @Valid HspOrgDepositGuaranteeVo vo) {
        log.info("修改机构保底金入参 -> {}", JSON.toJSONString(vo));

        // 检查生效日期和结束日期的先后顺序
        if (vo.getStartDate().after(vo.getEndDate())) {
            throw new IllegalArgumentException("生效日期不能大于结束日期");
        }

        final HspOrgDepositGuaranteeDto guarantee = hspOrgDepositGuaranteeService.selectById(vo.getHspOrgDepositGuaranteeId());
        if (Objects.isNull(guarantee)) {
            throw new IllegalArgumentException("机构保底金不存在");
        }

        final HspOrgDepositGuaranteeDto dto = JSON.parseObject(JSON.toJSONString(vo), HspOrgDepositGuaranteeDto.class);
        hspOrgDepositGuaranteeService.updateById(dto);

        String compare = new CompareUtils<HspOrgDepositGuaranteeDto>().compare(guarantee, dto);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_ORG.getDesc())
                    .setContent(String.format("机构保底金修改 [%s]", compare)).toJSONString());
        }

        return Collections.emptyMap();
    }

    /**
     * 删除机构保底金
     *
     * @param ids 机构保底金ID列表
     */
    @PostMapping("/delete")
    public Object deleteOrgDepositGuarantee(@RequestBody @NotEmpty(message = "机构保底金ID不能为空") List<Long> ids) {
        final List<HspOrgDepositGuaranteeDto> guarantees = hspOrgDepositGuaranteeService.selectByIds(ids);
        if (CollectionUtils.isEmpty(guarantees)) {
            return guarantees.size();
        }

        hspOrgDepositGuaranteeService.deleteByIds(ids);

        final List<String> hspOrgNames =
            guarantees.stream().map(HspOrgDepositGuaranteeDto::getHspOrgName).collect(Collectors.toList());

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_ORG.getDesc())
                .setContent(String.format("用户删除了机构保底金 %s", hspOrgNames)).toJSONString());

        return guarantees.size();
    }
}