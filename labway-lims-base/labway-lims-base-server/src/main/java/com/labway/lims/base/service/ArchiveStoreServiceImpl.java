package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.ArchiveStoreDto;
import com.labway.lims.base.api.service.ArchiveStoreService;
import com.labway.lims.base.mapper.TbArchiveStoreMapper;
import com.labway.lims.base.mapstruct.ArchiveStoreConverter;
import com.labway.lims.base.model.TbArchiveStore;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 归档库 Service impl
 * 
 * <AUTHOR>
 * @since 2023/4/3 11:01
 */
@DubboService
@Slf4j
public class ArchiveStoreServiceImpl implements ArchiveStoreService {
    @Resource
    private TbArchiveStoreMapper tbArchiveStoreMapper;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private ArchiveStoreConverter archiveStoreConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addArchiveStore(ArchiveStoreDto archiveStoreDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbArchiveStore target = new TbArchiveStore();
        target.setArchiveStoreCode(archiveStoreDto.getArchiveStoreCode());
        target.setArchiveStoreName(archiveStoreDto.getArchiveStoreName());
        target.setEnable(archiveStoreDto.getEnable());

        target.setArchiveStoreId(snowflakeService.genId());
        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbArchiveStoreMapper.insert(target) < 1) {
            throw new LimsException("添加归档库失败");
        }
        log.info("用户 [{}] 新增归档库[{}]成功", loginUser.getNickname(), JSON.toJSONString(target));

        return target.getArchiveStoreId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByArchiveStoreIds(Collection<Long> archiveStoreIds) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除归档库成功 [{}] 结果 [{}]", loginUser.getNickname(), archiveStoreIds,
            tbArchiveStoreMapper.deleteBatchIds(archiveStoreIds) > 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByArchiveStoreId(ArchiveStoreDto archiveStoreDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbArchiveStore target = new TbArchiveStore();
        BeanUtils.copyProperties(archiveStoreDto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbArchiveStoreMapper.updateById(target) < 1) {
            throw new LimsException("修改归档库失败");
        }

        log.info("用户 [{}] 修改归档库成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    public List<ArchiveStoreDto> selectByOrgId(long orgId) {
        if (orgId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbArchiveStore> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbArchiveStore::getOrgId, orgId);
        queryWrapper.eq(TbArchiveStore::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbArchiveStore::getCreateDate);
        return archiveStoreConverter.archiveStoreDtoListFromTbObj(tbArchiveStoreMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public ArchiveStoreDto selectByArchiveStoreId(long archiveStoreId) {
        if (archiveStoreId < 1) {
            return null;
        }
        LambdaQueryWrapper<TbArchiveStore> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbArchiveStore::getArchiveStoreId, archiveStoreId);
        queryWrapper.eq(TbArchiveStore::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return archiveStoreConverter.archiveStoreDtoFromTbObj(tbArchiveStoreMapper.selectOne(queryWrapper));
    }

    @Override
    public List<ArchiveStoreDto> selectByArchiveStoreIds(Collection<Long> archiveStoreIds) {
        if (CollectionUtils.isEmpty(archiveStoreIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbArchiveStore> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbArchiveStore::getArchiveStoreId, archiveStoreIds);
        queryWrapper.eq(TbArchiveStore::getIsDelete, YesOrNoEnum.NO.getCode());

        return archiveStoreConverter.archiveStoreDtoListFromTbObj(tbArchiveStoreMapper.selectList(queryWrapper));

    }

    @Nullable
    @Override
    public ArchiveStoreDto selectByArchiveStoreName(String archiveStoreName, long orgId) {
        if (StringUtils.isBlank(archiveStoreName)) {
            return null;
        }
        LambdaQueryWrapper<TbArchiveStore> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbArchiveStore::getArchiveStoreName, archiveStoreName);
        queryWrapper.eq(TbArchiveStore::getOrgId, orgId);
        queryWrapper.eq(TbArchiveStore::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return archiveStoreConverter.archiveStoreDtoFromTbObj(tbArchiveStoreMapper.selectOne(queryWrapper));
    }

    @Nullable
    @Override
    public ArchiveStoreDto selectByArchiveStoreCode(String archiveStoreCode, long orgId) {
        if (StringUtils.isBlank(archiveStoreCode)) {
            return null;
        }
        LambdaQueryWrapper<TbArchiveStore> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbArchiveStore::getArchiveStoreCode, archiveStoreCode);
        queryWrapper.eq(TbArchiveStore::getOrgId, orgId);
        queryWrapper.eq(TbArchiveStore::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return archiveStoreConverter.archiveStoreDtoFromTbObj(tbArchiveStoreMapper.selectOne(queryWrapper));
    }

}
