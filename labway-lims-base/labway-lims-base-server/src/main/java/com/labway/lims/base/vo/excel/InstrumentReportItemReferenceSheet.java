package com.labway.lims.base.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 仪器报告项目参考值sheet
 */
@Getter
@Setter
public class InstrumentReportItemReferenceSheet implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer index;

    /**
     * 仪器编码
     */
    @ExcelProperty(index = 0)
    private String instrumentCode;

    /**
     * 报告项目编码
     */
    @ExcelProperty(index = 1)
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    @ExcelProperty(index = 2)
    private String reportItemName;

    /**
     * 性别类型名称
     */
    @ExcelProperty(index = 3)
    private String sexStyleName;

    /**
     * 样本类型名称
     */
    @ExcelProperty(index = 4)
    private String sampleTypeName;

    /**
     * 年龄单位
     */
    @ExcelProperty(index = 5)
    private String ageUnit;

    /**
     * 年龄下限
     */
    @ExcelProperty(index = 6)
    private Integer ageMin;

    /**
     * 年龄上限
     */
    @ExcelProperty(index = 7)
    private Integer ageMax;


    /**
     * 参考值下限值
     */
    @ExcelProperty(index = 8)
    private String referValueMin;

    /**
     * 参考值上限值
     */
    @ExcelProperty(index = 9)
    private String referValueMax;


    /**
     * 异常提示范围下限
     */
    @ExcelProperty(index = 10)
    private String excpWarningMin;

    /**
     * 异常提示范围上限
     */
    @ExcelProperty(index = 11)
    private String excpWarningMax;


    /**
     * 危急值下限
     */
    @ExcelProperty(index = 12)
    private String fatalMin;

    /**
     * 危急值上限
     */
    @ExcelProperty(index = 13)
    private String fatalMax;

    /**
     * 中文参考值
     */
    @ExcelProperty(index = 14)
    private String cnRefereValue;

    /**
     * 英文参考值
     */
    @ExcelProperty(index = 15)
    private String enRefereValue;

    /**
     * 中英文参考值
     */
    @ExcelProperty(index = 16)
    private String cnEnRefereValue;

}
