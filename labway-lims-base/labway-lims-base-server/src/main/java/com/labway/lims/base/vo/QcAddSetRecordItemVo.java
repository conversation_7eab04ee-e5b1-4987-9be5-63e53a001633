package com.labway.lims.base.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 仪器质控记录明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Data
public class QcAddSetRecordItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 质控记录明细ID
     */
    private Long qcRecordItemId;

    /**
     * 质控批次编号
     */
    private Long qcBatchId;

    /**
     * 靶值
     */
    private String targetValue;

    /**
     * 标准差
     */
    private String standardDeviation;

    /**
     * 变异系数
     */
    private String cvValue;

    /**
     * 水平编码
     */
    private Integer levelCode;
}
