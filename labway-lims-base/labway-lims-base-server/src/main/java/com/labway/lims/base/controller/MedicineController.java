package com.labway.lims.base.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.MedicineDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.api.service.GermGenusService;
import com.labway.lims.base.api.service.MedicineGermRelationService;
import com.labway.lims.base.api.service.MedicineService;
import com.labway.lims.base.mapstruct.MedicineConverter;
import com.labway.lims.base.vo.MedicineAddRequestVo;
import com.labway.lims.base.vo.MedicineUpdateRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * 药物 API
 *
 * <AUTHOR>
 * @since 2023/3/21 16:20
 */
@Slf4j
@RestController
@RequestMapping("/medicine")
public class MedicineController extends BaseController {
    @DubboReference
    private DictService dictService;
    @DubboReference
    private MedicineService medicineService;

    @DubboReference
    private MedicineGermRelationService medicineGermRelationService;

    @DubboReference
    private GermGenusService germGenusService;

    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private MedicineConverter medicineConverter;

    /**
     * 药物 新增
     */
    @PostMapping("/add")
    public Object medicineAdd(@RequestBody MedicineAddRequestVo vo) {
        if (StringUtils.isAnyBlank(vo.getMedicineCode(), vo.getMedicineName()) || Objects.isNull(vo.getEnable())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        if (StringUtils.length(vo.getMedicineCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("药物编码不能超过 %s 个字符", INPUT_MAX_LENGTH));

        }
        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        if (Objects.nonNull(medicineService.selectByMedicineName(vo.getMedicineName(), loginUser.getOrgId()))) {
            throw new LimsException("当前药物名称已存在");
        }

        if (Objects.nonNull(medicineService.selectByMedicineCode(vo.getMedicineCode(), loginUser.getOrgId()))) {
            throw new LimsException("当前药物编码已存在");
        }
        MedicineDto medicineDto = medicineConverter.medicineDtoFromMedicineAddRequestVo(vo);

        long medicineId = medicineService.addMedicine(medicineDto);

        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.MEDICINE_LOG.getDesc())
                .setContent(String.format("新增 [%s] 药物", medicineDto.getMedicineName())).toJSONString());

        return Map.of("id", medicineId);

    }

    /**
     * 药物 删除
     */
    @PostMapping("/delete")
    public Object medicineDelete(@RequestBody Set<Long> medicineIds) {
        if (CollectionUtils.isEmpty(medicineIds)) {
            return Collections.emptyMap();
        }
        List<MedicineDto> medicineDtos = medicineService.selectByMedicineIds(medicineIds);

        medicineService.deleteByMedicineIds(medicineIds);
        medicineDtos.forEach(item -> {
            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.MEDICINE_LOG.getDesc())
                    .setContent(String.format("删除 [%s] 药物", item.getMedicineName())).toJSONString());

        });
        return Collections.emptyMap();
    }

    /**
     * 药物 修改
     */
    @PostMapping("/update")
    public Object medicineUpdate(@RequestBody MedicineUpdateRequestVo vo) {
        if (StringUtils.isAnyBlank(vo.getMedicineName()) || Objects.isNull(vo.getMedicineId())
            || Objects.isNull(vo.getEnable())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        final MedicineDto temp = medicineService.selectByMedicineId(vo.getMedicineId());
        if (Objects.isNull(temp)) {
            throw new LimsException("药物不存在");
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        MedicineDto selectByMedicineName =
            medicineService.selectByMedicineName(vo.getMedicineName(), loginUser.getOrgId());
        if (Objects.nonNull(selectByMedicineName)
            && !Objects.equals(vo.getMedicineId(), selectByMedicineName.getMedicineId())) {
            throw new LimsException("当前药物名称已存在");
        }

        final MedicineDto update = new MedicineDto();

        // 更新项
        update.setMedicineName(vo.getMedicineName());
        update.setMedicineEn(ObjectUtils.defaultIfNull(vo.getMedicineEn(), ""));
        update.setWhonetMedicineCode(vo.getWhonetMedicineCode());
        update.setEnable(vo.getEnable());

        update.setMedicineId(temp.getMedicineId());

        medicineService.updateByMedicineId(update);

        String compare = new CompareUtils<MedicineDto>().compare(temp, update);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            String message = TraceLog.newInstance().setModule(TraceLogModuleEnum.MEDICINE_LOG.getDesc())
                    .setContent(String.format("修改药物: [%s]", compare)).toJSONString();
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    message);
            log.info("mq信息data:{}", message);
        }

        return Collections.emptyMap();

    }

    /**
     * 药物 获取 所有 查看
     */
    @PostMapping("/select-all")
    public Object medicineList() {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        return medicineService.selectByOrgId(loginUser.getOrgId());
    }

    /**
     * 检查 药物 新增 或 修改 参数 公共部分
     */
    private <T extends MedicineAddRequestVo> void checkVoWhenAddOrUpdate(T vo) {
        if (StringUtils.length(vo.getMedicineName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("药物名称过长");
        }
        if (StringUtils.isNotBlank(vo.getMedicineEn()) && StringUtils.length(vo.getMedicineEn()) > 100) {
            throw new IllegalArgumentException("药物英文名称过长");
        }
        if (StringUtils.isNotBlank(vo.getWhonetMedicineCode())
            && StringUtils.length(vo.getWhonetMedicineCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("WHONET药物编码过长");
        }
        if (!(Objects.equals(vo.getEnable(), YesOrNoEnum.NO.getCode())
            || Objects.equals(vo.getEnable(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("是否启用参数错误");
        }

    }

}
