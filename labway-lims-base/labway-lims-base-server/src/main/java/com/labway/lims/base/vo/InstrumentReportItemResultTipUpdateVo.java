package com.labway.lims.base.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * <p>
 * 仪器报告项目结果提示
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Setter
@Getter
public class InstrumentReportItemResultTipUpdateVo extends InstrumentReportItemResultTipAddVo {

    /**
     * ID
     */
    private Long instrumentReportItemResultTipId;

}
