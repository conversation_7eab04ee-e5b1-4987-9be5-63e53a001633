package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.MaterialDto;
import com.labway.lims.base.model.TbMaterial;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 物料信息 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 19:21
 */
@Mapper(componentModel = "spring")
public interface MaterialConverter {
    TbMaterial tbMaterialFromTbObjDto(MaterialDto dto);

    MaterialDto materialDtoFromTbObj(TbMaterial obj);

    List<MaterialDto> materialDtoListFromTbObj(List<TbMaterial> list);
    List<TbMaterial> materialTbObjListFromDto(List<MaterialDto> list);

}
