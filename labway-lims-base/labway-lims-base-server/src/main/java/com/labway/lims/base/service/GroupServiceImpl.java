package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.UserProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.mapper.TbProfessionalGroupMapper;
import com.labway.lims.base.model.TbProfessionalGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "group")
public class GroupServiceImpl implements GroupService {

    public static final String SPLIT_BLOOD_GROUP_CODE = "SPLIT_BLOOD";
    private static final String OUTSOURCING_GROUP_CODE = "OUTSOURCING";

    @Resource
    private TbProfessionalGroupMapper professionalGroupMapper;
    @Resource
    private GroupService groupService;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private SnowflakeService snowflakeService;

    @Override
    public List<ProfessionalGroupDto> selectByUserId(long userId) {
        return professionalGroupMapper.selectByUserId(userId).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<UserProfessionalGroupDto> selectUserGroupByUserIds(Collection<Long> userIds) {
        return professionalGroupMapper.selectUserGroupByUserIds(userIds);
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByGroupId:'+#groupId")
    public ProfessionalGroupDto selectByGroupId(long groupId) {
        return convert(professionalGroupMapper.selectById(groupId));
    }

    @Override
    public List<ProfessionalGroupDto> selectByGroupIds(Collection<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }

        return professionalGroupMapper
            .selectList(new LambdaQueryWrapper<TbProfessionalGroup>().in(TbProfessionalGroup::getGroupId, groupIds))
            .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Map<Long, ProfessionalGroupDto> selectByGroupIdsAsMap(Collection<Long> groupIds) {
        return selectByGroupIds(groupIds).stream()
            .collect(Collectors.toMap(ProfessionalGroupDto::getGroupId, v -> v, (a, b) -> a));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByGroupName:'+#groupName")
    public ProfessionalGroupDto selectByGroupName(String groupName) {
        if (StringUtils.isBlank(groupName)) {
            return null;
        }
        final LambdaQueryWrapper<TbProfessionalGroup> eq = Wrappers.lambdaQuery(TbProfessionalGroup.class)
            .eq(TbProfessionalGroup::getOrgId, LoginUserHandler.get().getOrgId())
            .eq(TbProfessionalGroup::getGroupName, groupName).last("limit 1");

        final TbProfessionalGroup group = professionalGroupMapper.selectOne(eq);

        return JSON.parseObject(JSON.toJSONString(group), ProfessionalGroupDto.class);
    }

    @Override
    public List<ProfessionalGroupDto> selectByGroupNames(Collection<String> groupNames) {
        if (CollectionUtils.isEmpty(groupNames)) {
            return Collections.emptyList();
        }

        return professionalGroupMapper
            .selectList(new LambdaQueryWrapper<TbProfessionalGroup>().in(TbProfessionalGroup::getGroupName, groupNames))
            .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public long addGroup(ProfessionalGroupDto group) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        if (Objects.isNull(group)) {
            throw new IllegalStateException("添加专业组信息为空");
        }

        final String groupCode = group.getGroupCode();
        final String groupName = group.getGroupName();

        if (Objects.nonNull(selectByGroupName(groupName))) {
            throw new IllegalStateException("专业组名称已存在");
        }

        if (Objects.nonNull(selectByGroupCode(groupCode, user.getOrgId()))) {
            throw new IllegalStateException("专业组编码已存在");
        }

        final Date date = new Date();

        final TbProfessionalGroup object = JSON.parseObject(JSON.toJSONString(group), TbProfessionalGroup.class);
        object.setGroupId(snowflakeService.genId());
        object.setCreateDate(date);
        object.setCreatorId(user.getUserId());
        object.setCreatorName(user.getNickname());
        object.setUpdateDate(date);
        object.setUpdaterId(user.getUserId());
        object.setUpdaterName(user.getNickname());
        object.setOrgId(user.getOrgId());
        object.setApproverName(
            StringUtils.defaultString(object.getApproverName(), org.apache.commons.lang3.StringUtils.EMPTY));
        object.setApproverSign(
            StringUtils.defaultString(object.getApproverSign(), org.apache.commons.lang3.StringUtils.EMPTY));
        object.setIsDelete(YesOrNoEnum.NO.getCode());
        object.setGroupRemark(StringUtils.defaultString(object.getGroupRemark(), StringUtils.EMPTY));
        if (professionalGroupMapper.insert(object) < 1) {
            throw new IllegalStateException("新增专业组失败");
        }

        log.info("用户 [{}] 新增专业组 [{}]", user.getNickname(), JSON.toJSONString(group));

        return object.getGroupId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public void updateByGroupId(ProfessionalGroupDto group) {
        if (Objects.isNull(group.getGroupId())) {
            throw new IllegalStateException("专业组id为空");
        }
        final LoginUserHandler.User user = LoginUserHandler.get();

        final ProfessionalGroupDto professionalGroupDto = Optional.ofNullable(selectByGroupName(group.getGroupName()))
            .filter(m -> !Objects.equals(m.getGroupId(), group.getGroupId())).orElse(null);
        if (Objects.nonNull(professionalGroupDto)) {
            throw new IllegalStateException("专业组名称已存在");
        }

        final TbProfessionalGroup update = JSON.parseObject(JSON.toJSONString(group), TbProfessionalGroup.class);
        update.setGroupCode(null);
        update.setCreatorId(null);
        update.setCreateDate(null);
        update.setCreatorName(null);

        update.setUpdaterId(user.getUserId());
        update.setUpdaterName(user.getNickname());
        update.setUpdateDate(new Date());

        if (professionalGroupMapper.updateById(update) < 1) {
            throw new IllegalStateException("修改专业组失败");
        }

        log.info("用户 [{}] 修改专业组信息 [{}]", user.getNickname(), JSON.toJSONString(group));

    }

    @Override
    @Cacheable(key = "'selectByOrgId:'+#orgId")
    public List<ProfessionalGroupDto> selectByOrgId(long orgId) {

        final LambdaQueryWrapper<TbProfessionalGroup> wrapper = Wrappers.lambdaQuery(TbProfessionalGroup.class)
            .eq(TbProfessionalGroup::getOrgId, orgId).orderByDesc(TbProfessionalGroup::getGroupId);

        final List<TbProfessionalGroup> groups = professionalGroupMapper.selectList(wrapper);
        return JSON.parseArray(JSON.toJSONString(groups), ProfessionalGroupDto.class);
    }

    @Override
    @Cacheable(key = "'selectByAll:'+#orgId")
    public List<ProfessionalGroupDto> selectByAll(long orgId) {
        final LambdaQueryWrapper<TbProfessionalGroup> wrapper = Wrappers.lambdaQuery(TbProfessionalGroup.class)
            .eq(TbProfessionalGroup::getOrgId, orgId).orderByAsc(TbProfessionalGroup::getGroupId);
        final List<TbProfessionalGroup> groups = professionalGroupMapper.selectList(wrapper);
        return JSON.parseArray(JSON.toJSONString(groups), ProfessionalGroupDto.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @CacheEvict(allEntries = true)
    public void deleteByGroupIds(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return;
        }

        for (final Long groupId : groupIds) {
            deleteByGroupId(groupId);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @CacheEvict(allEntries = true)
    public void deleteByGroupId(long groupId) {
        log.info("用户 [{}] 删除专业组 [{}] 结果 [{}]", LoginUserHandler.get().getNickname(), groupId,
            professionalGroupMapper.deleteById(groupId) > 0);
    }

    @Override
    public ProfessionalGroupDto selectSplitBloodGroup(long orgId) {
        final ProfessionalGroupDto professionalGroup = groupService.selectByGroupCode(SPLIT_BLOOD_GROUP_CODE, orgId);
        if (Objects.isNull(professionalGroup)) {
            throw new IllegalStateException("不存在分血组");
        }
        return professionalGroup;
    }

    @Override
    @Deprecated
    public ProfessionalGroupDto selectOutsourcingGroup(long orgId) {
        final ProfessionalGroupDto professionalGroup = groupService.selectByGroupCode(OUTSOURCING_GROUP_CODE, orgId);
        if (Objects.isNull(professionalGroup)) {
            throw new IllegalStateException("不存在委外组");
        }
        return professionalGroup;
    }

    @Override
    public Map<String, ProfessionalGroupDto> selectOutsourcingGroupMapByGroupCode(long orgId) {
        ArrayList<String> outsourcingGroupCodes = Lists.newArrayList(OUTSOURCING_GROUP_CODE);
        // 获取系统参数中配置的委外组编码
        SystemParamDto systemParamDto = systemParamService.selectByParamName(OUTSOURCING_GROUP_CODE, orgId);
        if (Objects.nonNull(systemParamDto) && StringUtils.isNotBlank(systemParamDto.getParamValue())) {
            outsourcingGroupCodes.addAll(Arrays.asList(systemParamDto.getParamValue().split(StringPool.COMMA)));
        }

        Map<String, ProfessionalGroupDto> professionalGroupMapByGroupCode = groupService.selectByGroupCodes(outsourcingGroupCodes, orgId);

        if (Objects.isNull(professionalGroupMapByGroupCode) || professionalGroupMapByGroupCode.isEmpty()) {
            throw new IllegalStateException("不存在委外组");
        }
        return professionalGroupMapByGroupCode;
    }

    /**
     * 判断该专业组是不是委外组
     */
    @Override
    public boolean checkIsOutsourcingGroup(Long userGroupId, Long orgId) {
        Map<String, ProfessionalGroupDto> professionalGroupDtoMap = selectOutsourcingGroupMapByGroupCode(orgId);
        return professionalGroupDtoMap.values().stream().anyMatch(m -> Objects.equals(m.getGroupId(), userGroupId));
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByGroupCode:' + #groupCode + #orgId")
    public ProfessionalGroupDto selectByGroupCode(String groupCode, long orgId) {
        if (StringUtils.isBlank(groupCode)) {
            return null;
        }
        final LambdaQueryWrapper<TbProfessionalGroup> eq = Wrappers.lambdaQuery(TbProfessionalGroup.class)
            .eq(TbProfessionalGroup::getOrgId, orgId).eq(TbProfessionalGroup::getGroupCode, groupCode).last("limit 1");

        final TbProfessionalGroup group = professionalGroupMapper.selectOne(eq);

        return JSON.parseObject(JSON.toJSONString(group), ProfessionalGroupDto.class);
    }

    @Override
    public Map<String, ProfessionalGroupDto> selectByGroupCodes(Collection<String> groupCodes, long orgId) {
        if (CollectionUtils.isEmpty(groupCodes)) {
            return Collections.emptyMap();
        }

        final LambdaQueryWrapper<TbProfessionalGroup> eq = Wrappers.lambdaQuery(TbProfessionalGroup.class)
            .eq(TbProfessionalGroup::getOrgId, orgId).in(TbProfessionalGroup::getGroupCode, groupCodes);

        return professionalGroupMapper.selectList(eq).stream().map(this::convert).filter(Objects::nonNull)
            .collect(Collectors.toMap(ProfessionalGroupDto::getGroupCode, v -> v, (a, b) -> a));
    }

    private ProfessionalGroupDto convert(TbProfessionalGroup professionalGroup) {
        if (Objects.isNull(professionalGroup)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(professionalGroup), ProfessionalGroupDto.class);
    }

}
