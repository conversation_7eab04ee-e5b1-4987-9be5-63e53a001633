package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.model.TbInstrumentReportItemReference;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仪器报告项目参考值 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Mapper
public interface TbInstrumentReportItemReferenceMapper extends BaseMapper<TbInstrumentReportItemReference> {

    /**
     * 根据专业小组查询
     */
    List<InstrumentReportItemReferenceDto> selectByInstrumentGroupId(@Param("instrumentGroupId") long instrumentGroupId);

    /**
     * 根据专业小组和报告项目code查询
     */
    List<InstrumentReportItemReferenceDto> selectByInstrumentGroupIdAndReportItemCode(@Param("instrumentGroupId") long instrumentGroupId,
                                                                                      @Param("reportItemCode") String reportItemCode);
}
