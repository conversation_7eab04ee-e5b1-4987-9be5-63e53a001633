package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 仪器质控设置记录
 * @TableName tb_qc_set_record_main
 */
@TableName(value ="tb_qc_set_record_main")
@Data
public class TbQcSetRecordMain implements Serializable {
    /**
     * 质控记录ID
     */
    @TableId
    private Long qcRecordMainId;

    /**
     * 质控批号报告项目id  
     */
    private Long batchReportItemId;

    /**
     * 质控批次ID
     */
    private Long qcBatchId;

    /**
     * 专业组id
     */
    private Long groupId;

    /**
     * 专业组code
     */
    private String groupCode;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 仪器编码ID
     */
    private Long instrumentId;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 报告项目编码ID
     */
    private Long reportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 仪器通道编码
     */
    private String instrumentChannel;

    /**
     * 质控品名称
     */
    private String qcReagentName;


    /**
     * 质控品厂家
     */
    private String qcManufactoryName;

    /**
     * 设置日期
     */
    private Date setDate;

    /**
     * 生效日期
     */
    private Date qcValTime;

    /**
     * 失效日期
     */
    private Date qcExpireTime;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 质控记录状态
     */
    private Integer qcRecordStatus;

    /**
     * 质控记录状态描述
     */
    private String qcRecordStatusDesc;

    /**
     * 是否有效
     */
    private Integer isUse;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建人名
     */
    private String createName;

    /**
     * 更新人id
     */
    private Long updateId;

    /**
     * 更新人名
     */
    private String updateName;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 自定义属性1
     */
    private String selfAttribute1;

    /**
     * 自定义属性2
     */
    private String selfAttribute2;

    /**
     * 自定义属性3
     */
    private String selfAttribute3;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date updateDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}