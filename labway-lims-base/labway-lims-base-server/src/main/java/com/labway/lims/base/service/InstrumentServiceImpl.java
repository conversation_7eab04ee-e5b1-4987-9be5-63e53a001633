package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.mapper.TbInstrumentMapper;
import com.labway.lims.base.model.TbInstrument;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "instrument")
public class InstrumentServiceImpl implements InstrumentService {
    @Resource
    private TbInstrumentMapper instrumentMapper;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    @Cacheable(key = "'selectByGroupId:' + #groupId")
    public List<InstrumentDto> selectByGroupId(long groupId) {
        return instrumentMapper.selectList(new LambdaQueryWrapper<TbInstrument>()
                        .orderByDesc(TbInstrument::getInstrumentId).eq(TbInstrument::getGroupId, groupId)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByGroupIds:' + #cacheKey")
    public List<InstrumentDto> selectByGroupIds(List<Long> groupIds, String cacheKey) {
        return instrumentMapper.selectList(new LambdaQueryWrapper<TbInstrument>()
                        .orderByDesc(TbInstrument::getInstrumentId).in(TbInstrument::getGroupId, groupIds)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByOrgId:' + #orgId")
    public List<InstrumentDto> selectByOrgId(long orgId) {
        return instrumentMapper.selectList(new LambdaQueryWrapper<TbInstrument>()
                        .orderByDesc(TbInstrument::getInstrumentId).eq(TbInstrument::getOrgId, orgId)).stream().map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<InstrumentDto> selectByOrgIdByGroupId(long orgId, Long groupId) {
        return instrumentMapper.selectList(new LambdaQueryWrapper<TbInstrument>()
                        .orderByDesc(TbInstrument::getInstrumentId)
                        .eq(TbInstrument::getOrgId, orgId)
                        .eq(Objects.nonNull(groupId), TbInstrument::getGroupId, groupId))
                .stream().map(this::convert).collect(Collectors.toList());
    }


    @Override
    @CacheEvict(allEntries = true)
    public long addInstrument(InstrumentDto dto) {

        if (selectByOrgId(LoginUserHandler.get().getOrgId()).stream()
                .anyMatch(e -> StringUtils.equalsIgnoreCase(e.getInstrumentCode(), dto.getInstrumentCode()))) {
            throw new IllegalArgumentException(String.format("仪器编码 [%s] 已存在", dto.getInstrumentCode()));
        }

        final TbInstrument instrument = new TbInstrument();
        BeanUtils.copyProperties(dto, instrument);

        instrument.setInstrumentId(snowflakeService.genId());
        instrument.setOrgId(LoginUserHandler.get().getOrgId());
        instrument.setCreateDate(new Date());
        instrument.setUpdateDate(new Date());
        instrument.setCreatorId(LoginUserHandler.get().getUserId());
        instrument.setCreatorName(LoginUserHandler.get().getNickname());
        instrument.setUpdaterId(LoginUserHandler.get().getUserId());
        instrument.setUpdaterName(LoginUserHandler.get().getNickname());
        instrument.setIsDelete(YesOrNoEnum.NO.getCode());

        if (instrumentMapper.insert(instrument) < 1) {
            throw new IllegalStateException("添加仪器失败");
        }

        log.info("用户 [{}] 添加仪器成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(instrument));

        return instrument.getInstrumentId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByInstrumentId(InstrumentDto dto) {
        final TbInstrument instrument = new TbInstrument();
        BeanUtils.copyProperties(dto, instrument);

        instrument.setInstrumentId(dto.getInstrumentId());
        instrument.setInstrumentCode(null);
        instrument.setUpdateDate(new Date());
        instrument.setUpdaterId(LoginUserHandler.get().getUserId());
        instrument.setUpdaterName(LoginUserHandler.get().getNickname());

        if (instrumentMapper.updateById(instrument) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改仪器成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(instrument));

        return true;
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByInstrumentId(long instrumentId) {
        return instrumentMapper.deleteById(instrumentId) > 0;
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByInstrumentId:' + #instrumentId")
    public InstrumentDto selectByInstrumentId(long instrumentId) {
        return convert(instrumentMapper.selectById(instrumentId));
    }

    @Override
    public List<InstrumentDto> selectByInstrumentGroupId(long instrumentGroupId) {
        return instrumentMapper.selectByInstrumentGroupId(instrumentGroupId);
    }

    @Nullable
    @Override
    public InstrumentDto selectByInstrumentCode(String instrumentCode, long orgId) {
        return convert(instrumentMapper.selectOne(new LambdaQueryWrapper<TbInstrument>()
                .eq(TbInstrument::getInstrumentCode, instrumentCode)
                .eq(TbInstrument::getOrgId, orgId)
                .last("limit 1")));
    }

    @Override
    public List<InstrumentDto> selectByInstrumentIds(Collection<Long> instrumentIds) {
        if (CollectionUtils.isEmpty(instrumentIds)) {
            return Collections.emptyList();
        }
        return instrumentMapper.selectBatchIds(instrumentIds)
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Map<Long, InstrumentDto> selectByInstrumentIdsAsMap(Collection<Long> instrumentIds) {
        final List<InstrumentDto> instruments = selectByInstrumentIds(instrumentIds);
        return instruments.stream().collect(Collectors
                .toMap(InstrumentDto::getInstrumentId, v -> v, (a, b) -> a));
    }

    @Override
    public InstrumentDto selectByGroupIdAndInstrumentCode(long groupId, String instrumentCode) {
        if (StringUtils.isBlank(instrumentCode)) {
            return null;
        }

        final LambdaQueryWrapper<TbInstrument> eq = Wrappers.lambdaQuery(TbInstrument.class)
                .eq(TbInstrument::getGroupId, groupId).eq(TbInstrument::getInstrumentCode, instrumentCode);

        return convert(instrumentMapper.selectOne(eq));
    }

    @Override
    @Cacheable(key = "'selectByOrgIdAndInstrumentCode:' + #orgId + #instrumentCode")
    public InstrumentDto selectByOrgIdAndInstrumentCode(long orgId, String instrumentCode) {
        if (StringUtils.isBlank(instrumentCode)) {
            return null;
        }

        final LambdaQueryWrapper<TbInstrument> eq = Wrappers.lambdaQuery(TbInstrument.class)
                .eq(TbInstrument::getOrgId, orgId).eq(TbInstrument::getInstrumentCode, instrumentCode).last("limit 1");

        return convert(instrumentMapper.selectOne(eq));
    }

    @Override
    public List<InstrumentDto> selectByOrgIdAndInstrumentCodes(long orgId, Collection<String> instrumentCodes) {
        if (CollectionUtils.isEmpty(instrumentCodes)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbInstrument> eq = Wrappers.lambdaQuery(TbInstrument.class)
                .eq(TbInstrument::getOrgId, orgId).in(TbInstrument::getInstrumentCode, instrumentCodes);

        return instrumentMapper.selectList(eq).stream().map(this::convert)
                .collect(Collectors.toList());
    }

    private InstrumentDto convert(TbInstrument instrument) {
        if (Objects.isNull(instrument)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(instrument), InstrumentDto.class);
    }
}
