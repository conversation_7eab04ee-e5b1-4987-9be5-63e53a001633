package com.labway.lims.base.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 药物 新增 请求 Vo
 * 
 * <AUTHOR>
 * @since 2023/3/21 16:31
 */
@Getter
@Setter
public class MedicineAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 药物编码
     */
    private String medicineCode;
    /**
     * 药物名称
     */
    private String medicineName;
    /**
     * 药物英文名
     */
    private String medicineEn;
    /**
     * whonet药物编码
     */
    private String whonetMedicineCode;
    /**
     * 是否启用(0未启用 1启用)
     * 
     * @see YesOrNoEnum
     */
    private Integer enable;

}
