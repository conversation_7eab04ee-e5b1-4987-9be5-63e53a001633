package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 物料同步流水表
 *
 * <AUTHOR>
 * @since 2023/3/13 9:50
 */

@Getter
@Setter
public class MaterialSyncFlowVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 雪花生成唯一id
     */
    private Long materialSyncFlowId;

    /**
     * 是否有效
     * 0有效 1无效
     */
    private Integer enable;

    /**
     * 最后修改时间从
     */
    private Date modifiedDateStart;

    /**
     * 最后修改时间至
     */
    private Date modifiedDateEnd;

    /**
     * 影响操作数量
     */
    private Integer operationNumber;

    /**
     * 操作状态:0失败,1成功
     */
    private Integer status;
    /**
     * 同步结果描述
     */
    private String resultMessage;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

}
