package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.GermGenusDto;
import com.labway.lims.base.api.service.GermGenusService;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.base.api.service.MedicineService;
import com.labway.lims.base.vo.GermGenusAddRequestVo;
import com.labway.lims.base.vo.GermGenusListRequestVo;
import com.labway.lims.base.vo.GermGenusUpdateRequestVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 细菌菌属 API
 * 
 * <AUTHOR>
 * @since 2023/3/20 17:36
 */
@RestController
@RequestMapping("/germ-genus")
public class GermGenusController extends BaseController {

    @DubboReference
    private GermService germService;
    @DubboReference
    private MedicineService medicineService;
    @DubboReference
    private GermGenusService germGenusService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 细菌菌属 新增
     */
    @PostMapping("/add")
    public Object germGenusAdd(@RequestBody GermGenusAddRequestVo vo) {
        if (StringUtils.isAnyBlank(vo.getGermGenusCode(), vo.getGermGenusName()) || Objects.isNull(vo.getEnable())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        if (StringUtils.length(vo.getGermGenusCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("菌属编码不能超过 %s 个字符", INPUT_MAX_LENGTH));

        }
        // 检查 参数
        checkVoWhenAddOrUpdate(vo);
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        if (Objects.nonNull(germGenusService.selectByGermGenusName(vo.getGermGenusName(), loginUser.getOrgId()))) {
            throw new LimsException("当前细菌菌属名称已存在");
        }

        if (Objects.nonNull(germGenusService.selectByGermGenusCode(vo.getGermGenusCode(), loginUser.getOrgId()))) {
            throw new LimsException("当前细菌菌属编码已存在");
        }
        long id = germGenusService.addGermGenus(JSON.parseObject(JSON.toJSONString(vo), GermGenusDto.class));
        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.GERM_GENUS_LOG.getDesc())
                .setContent(String.format("新增 [%s] 细菌菌属", vo.getGermGenusName())).toJSONString());
        return Map.of("id", id);
    }

    /**
     * 细菌菌属 删除
     */
    @PostMapping("/delete")
    public Object germGenusDelete(@RequestBody Set<Long> germGenusIds) {
        if (CollectionUtils.isEmpty(germGenusIds)) {
            return Collections.emptyMap();
        }

        if (CollectionUtils.isNotEmpty(germService.selectByGermGenusIds(germGenusIds))) {
            throw new LimsException("已选细菌菌属存在已被细菌引用,不能删除");
        }

        if (CollectionUtils.isNotEmpty(medicineService.selectByGermGenusIds(germGenusIds))) {
            throw new LimsException("已选细菌菌属存在已被药物引用,不能删除");
        }
        List<GermGenusDto> genusDtoList = germGenusService.selectByGermGenusIds(germGenusIds);

        germGenusService.deleteByGermGenusIds(germGenusIds);

        genusDtoList.forEach(item -> {
            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.GERM_GENUS_LOG.getDesc())
                    .setContent(String.format("删除 [%s] 细菌菌属", item.getGermGenusName())).toJSONString());

        });
        return Collections.emptyMap();
    }

    /**
     * 细菌菌属 修改
     */
    @PostMapping("/update")
    public Object germGenusUpdate(@RequestBody GermGenusUpdateRequestVo vo) {
        if (StringUtils.isBlank(vo.getGermGenusName()) || Objects.isNull(vo.getGermGenusId())
            || Objects.isNull(vo.getEnable())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        // 判断细菌菌属是否存在
        final GermGenusDto germGenusDtoNow = germGenusService.selectByGermGenusId(vo.getGermGenusId());
        if (Objects.isNull(germGenusDtoNow)) {
            throw new LimsException("细菌菌属不存在");
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        GermGenusDto selectByGermGenusName =
            germGenusService.selectByGermGenusName(vo.getGermGenusName(), loginUser.getOrgId());
        if (Objects.nonNull(selectByGermGenusName)
            && !Objects.equals(vo.getGermGenusId(), selectByGermGenusName.getGermGenusId())) {
            throw new LimsException("当前细菌菌属名称已存在");
        }

        final GermGenusDto germGenusDto = new GermGenusDto();
        BeanUtils.copyProperties(germGenusDtoNow, germGenusDto);
        // 更新项
        germGenusDto.setGermGenusName(vo.getGermGenusName());
        germGenusDto.setEnable(vo.getEnable());

        germGenusService.updateByGermGenusId(germGenusDto);

        String compare = new CompareUtils<GermGenusDto>().compare(germGenusDtoNow, germGenusDto);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.GERM_GENUS_LOG.getDesc())
                    .setContent(String.format("修改细菌菌属: [%s]", compare)).toJSONString());
        }

        return Collections.emptyMap();
    }

    /**
     * 细菌菌属 获取 所有 查看
     */
    @PostMapping("/select-all")
    public Object germGenusList(@RequestBody GermGenusListRequestVo vo) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        return germGenusService.selectByOrgId(loginUser.getOrgId());
    }

    /**
     * 细菌菌属 删除前检查
     */
    @PostMapping("/delete-check")
    public Object germGenusDeleteCheck(@RequestBody Set<Long> germGenusIds) {
        final String checkResult = "checkResult";
        if (CollectionUtils.isEmpty(germGenusIds)) {
            return Map.of(checkResult, Boolean.TRUE);
        }

        if (CollectionUtils.isNotEmpty(germService.selectByGermGenusIds(germGenusIds))) {
            return Map.of(checkResult, Boolean.FALSE);
        }

        if (CollectionUtils.isNotEmpty(medicineService.selectByGermGenusIds(germGenusIds))) {
            return Map.of(checkResult, Boolean.FALSE);
        }
        return Map.of(checkResult, Boolean.TRUE);
    }

    /**
     * 检查 细菌菌属 新增 或 修改 参数 公共部分
     *
     */
    private <T extends GermGenusAddRequestVo> void checkVoWhenAddOrUpdate(T vo) {

        if (StringUtils.length(vo.getGermGenusName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("菌属名称不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }
        if (!(Objects.equals(vo.getEnable(), YesOrNoEnum.NO.getCode())
            || Objects.equals(vo.getEnable(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("是否启用参数错误");
        }
    }

}
