package com.labway.lims.base.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.MD5Utils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.DefaultHspOrg;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.CombinePackageInfoDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.ItemPriceBaseFinanceDetailService;
import com.labway.lims.base.api.service.ItemPriceCombinePackageDetailService;
import com.labway.lims.base.api.service.ItemPriceCombinePackageService;
import com.labway.lims.base.api.vo.CombinePackageInfoVo;
import com.labway.lims.base.api.vo.ItemPriceBaseFinanceDetailVo;
import com.labway.lims.base.mapper.TbItemPriceCombinePackageMapper;
import com.labway.lims.base.model.TbItemPriceCombinePackage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.aop.framework.AopContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * (TbItemPriceCombinePackage)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-04 19:55:50
 */
@Service("itemPriceCombinePackageService")
public class ItemPriceCombinePackageServiceImpl extends ServiceImpl<TbItemPriceCombinePackageMapper, TbItemPriceCombinePackage> implements ItemPriceCombinePackageService {

    @Resource
    private TbItemPriceCombinePackageMapper tbItemPriceCombinePackageMapper;
    @Resource
    private ItemPriceCombinePackageDetailService itemPriceCombinePackageDetailService;
    @Resource
    private SnowflakeService snowflakeService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private ItemPriceBaseFinanceDetailService itemPriceBaseFinanceDetailService;

    private static final String ADD_COMBINE_PACKAGE_LOCK = "ADD_COMBINE_PACKAGE_LOCK:";
    private static final String EDIT_COMBINE_PACKAGE_DETAIL_LOCK = "EDIT_COMBINE_PACKAGE_DETAIL_LOCK:";


    /**
     * 获取所有检验套餐信息-不分页
     */
    public List<CombinePackageInfoDto> queryCombinePackageByCodes(Collection<String> combinePackageCodeList) {

        final LambdaQueryWrapper<TbItemPriceCombinePackage> wrapper = new LambdaQueryWrapper<TbItemPriceCombinePackage>().in(CollectionUtils.isNotEmpty(combinePackageCodeList), TbItemPriceCombinePackage::getCombinePackageCode, combinePackageCodeList);

        final List<TbItemPriceCombinePackage> combinePackageList = super.list(wrapper);

        if (org.springframework.util.CollectionUtils.isEmpty(combinePackageList)) {
            return Collections.emptyList();
        }

        return combinePackageList.stream().collect(Collectors.groupingBy(TbItemPriceCombinePackage::getCombinePackageCode)).values().stream().map(byCombinePackageCodeList -> {
            final List<CombinePackageInfoDto.HspOrgInfo> hspOrgInfos = byCombinePackageCodeList.stream().map(e -> new CombinePackageInfoDto.HspOrgInfo(e.getHspOrgId(), e.getHspOrgCode(), e.getHspOrgName())).collect(Collectors.toList());
            return JSON.parseObject(JSON.toJSONString(byCombinePackageCodeList.get(0)), CombinePackageInfoDto.class).setHspOrgList(hspOrgInfos);
        }).sorted(Comparator.comparing(CombinePackageInfoDto::getCreateDate)).collect(Collectors.toList());
    }

    /**
     * 新增套餐信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int addCombinePackageInfo(CombinePackageInfoDto combinePackageInfoDto) {
        String redisKey = redisPrefix.getBasePrefix() + ADD_COMBINE_PACKAGE_LOCK + combinePackageInfoDto.getCombinePackageCode();
        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(redisKey, StringUtils.EMPTY, 60, TimeUnit.SECONDS))) {
            throw new IllegalStateException("正在修改中， 请稍后");
        }

        try {
            LoginUserHandler.User user = LoginUserHandler.get();
            List<CombinePackageInfoDto.HspOrgInfo> hspOrgList = combinePackageInfoDto.getHspOrgList();
            // 判断是否有通用机构和其他机构同时存在
            final CombinePackageInfoDto.HspOrgInfo defaultHspOrgInfo = hspOrgList.stream().filter(e -> Objects.equals(e.getHspOrgId(), DefaultHspOrg.DEFAULT_HSP_ORG_ID)).findFirst().orElse(null);
            if (Objects.nonNull(defaultHspOrgInfo) && hspOrgList.size() > NumberUtils.INTEGER_ONE) {
                throw new IllegalArgumentException("不能既有通用机构又有其他送检机构");
            }


            // 送检机构判重
            List<CombinePackageInfoDto.HspOrgInfo> hspOrgInfos = hspOrgList.stream().collect(Collectors.groupingBy(CombinePackageInfoDto.HspOrgInfo::getHspOrgId)).values().stream().filter(list -> list.size() > 1).findFirst().orElse(null);
            if (CollectionUtils.isNotEmpty(hspOrgInfos)) {
                throw new IllegalStateException("送检机构存在重复，机构名称：" + CollUtil.join(hspOrgInfos.stream().map(CombinePackageInfoDto.HspOrgInfo::getHspOrgName).collect(Collectors.toSet()), ","));
            }

            // 判断编码和套餐是否重复
            final TbItemPriceCombinePackage tbItemPriceCombinePackage =
                    tbItemPriceCombinePackageMapper.selectOne(new LambdaQueryWrapper<TbItemPriceCombinePackage>().and(e -> e.eq(TbItemPriceCombinePackage::getCombinePackageCode, combinePackageInfoDto.getCombinePackageCode()).or().eq(TbItemPriceCombinePackage::getCombinePackageName, combinePackageInfoDto.getCombinePackageName())).last("limit 1"));

            if (Objects.nonNull(tbItemPriceCombinePackage)) {
                throw new IllegalStateException("套餐编码【" + tbItemPriceCombinePackage.getCombinePackageCode() + "】、" +
                        "套餐名称【" + tbItemPriceCombinePackage.getCombinePackageName() + "】、套餐项目存在重复，请检查！");
            }

            final String combinePackageCode = combinePackageInfoDto.getCombinePackageCode();
            final List<TestItemDto> testItemDtoList = this.queryCombinePackageDetailInfo(List.of(combinePackageCode))
                    .getOrDefault(combinePackageCode, Collections.emptyList());
            final String md5 = this.md5(testItemDtoList.stream().map(TestItemDto::getTestItemId).collect(Collectors.toSet()));
            combinePackageInfoDto.setItemCount(testItemDtoList.size());
            combinePackageInfoDto.setItemMd5(md5);
            // 组装新增的套餐信息
            List<TbItemPriceCombinePackage> saveCombinePackages = this.getSaveCombinePackages(combinePackageInfoDto, user);

            // 保存套餐信息
            return tbItemPriceCombinePackageMapper.insertBatch(saveCombinePackages);
        } finally {
            stringRedisTemplate.delete(redisKey);
        }

    }


    /**
     * 修改套餐信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateCombinePackageInfo(CombinePackageInfoDto combinePackageInfo) {
        // 财务套餐code
        final String combinePackageCode = combinePackageInfo.getCombinePackageCode();
        // 查询套餐信息
        final LambdaQueryWrapper<TbItemPriceCombinePackage> wrapper = new LambdaQueryWrapper<>(TbItemPriceCombinePackage.class).eq(TbItemPriceCombinePackage::getCombinePackageCode, combinePackageCode);
        final List<TbItemPriceCombinePackage> tbItemPriceCombinePackages = tbItemPriceCombinePackageMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(tbItemPriceCombinePackages)) {
            throw new IllegalStateException("套餐信息不存在，请检查！");
        }

        // 校验这个财务套餐编码下的检验项目
        final Set<Long> testItemIds = this.queryCombinePackageDetailInfo(List.of(combinePackageCode)).getOrDefault(combinePackageCode, Collections.emptyList())
                .stream().map(TestItemDto::getTestItemId).collect(Collectors.toSet());
        final Set<String> hspOrgCodes = combinePackageInfo.getHspOrgList().stream().map(CombinePackageInfoDto.HspOrgInfo::getHspOrgCode).collect(Collectors.toSet());
        this.checkPackageDetail(combinePackageCode, hspOrgCodes, this.md5(testItemIds));

        final TbItemPriceCombinePackage tbItemPriceCombinePackage = tbItemPriceCombinePackages.get(NumberUtils.INTEGER_ZERO);
        combinePackageInfo.setItemCount(tbItemPriceCombinePackage.getItemCount());
        combinePackageInfo.setItemMd5(tbItemPriceCombinePackage.getItemMd5());
        // 删除之后重新添加
        tbItemPriceCombinePackageMapper.delete(wrapper);
        return ((ItemPriceCombinePackageServiceImpl) AopContext.currentProxy()).addCombinePackageInfo(combinePackageInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteCombinePackageInfo(Collection<String> combinePackageCodeList) {
        if (CollectionUtils.isEmpty(combinePackageCodeList)) {
            return;
        }
        // 查询基准包下是否有财务套餐， 有的话全部删除
        final List<Long> financeDetailIdList = itemPriceBaseFinanceDetailService.selectAll().stream()
                .filter(e -> combinePackageCodeList.contains(e.getCombinePackageCode()))
                .map(ItemPriceBaseFinanceDetailVo::getFinanceDetailId)
                .collect(Collectors.toList());
        itemPriceBaseFinanceDetailService.deleteByFinanceDetailIds(financeDetailIdList);

        // 删除套餐
        final LambdaQueryWrapper<TbItemPriceCombinePackage> combinePackageWrapper = new LambdaQueryWrapper<TbItemPriceCombinePackage>()
                .in(TbItemPriceCombinePackage::getCombinePackageCode, combinePackageCodeList);
        super.remove(combinePackageWrapper);

        // 删除套餐下的项目
        itemPriceCombinePackageDetailService.deleteByCombinePackageCodes(combinePackageCodeList);
    }


    /**
     * 查询套餐的详情信息
     */
    @Override
    public Map<String, List<TestItemDto>> queryCombinePackageDetailInfo(Collection<String> combinePackageCodes) {
        return itemPriceCombinePackageDetailService.queryCombinePackageDetailInfo(combinePackageCodes);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveCombinePackageDetail(String combinePackageCode, Set<Long> testItemIds) {
        String redisKey = redisPrefix.getBasePrefix() + EDIT_COMBINE_PACKAGE_DETAIL_LOCK + combinePackageCode;
        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(redisKey, StringUtils.EMPTY, 60, TimeUnit.SECONDS))) {
            throw new IllegalStateException("操作繁忙， 请稍后");
        }

        try {

            // 校验
            final String md5 = this.md5(testItemIds);
            this.checkPackageDetail(combinePackageCode, Set.of(), md5);

            // 将之前的数据都删掉
            itemPriceCombinePackageDetailService.deleteByCombinePackageCode(combinePackageCode);
            // 将新的数据落库
            final boolean bool = itemPriceCombinePackageDetailService.addCombinePackageDetailInfo(combinePackageCode, testItemIds);
            this.updateTestItemIdsAndMd5(combinePackageCode, testItemIds.size(), md5);
            return bool;
        } finally {
            stringRedisTemplate.delete(redisKey);
        }
    }

    /**
     * 更新
     */
    private void updateTestItemIdsAndMd5(String combinePackageCode, int testItemSize, String md5) {
        // 修改项目数量和md5值
        final LambdaUpdateWrapper<TbItemPriceCombinePackage> updateWrapper = new LambdaUpdateWrapper<TbItemPriceCombinePackage>()
                .eq(TbItemPriceCombinePackage::getCombinePackageCode, combinePackageCode)
                .set(TbItemPriceCombinePackage::getItemCount, testItemSize)
                .set(TbItemPriceCombinePackage::getItemMd5, md5);
        super.update(updateWrapper);
    }

    @Override
    public List<CombinePackageInfoVo> queryCombinePackageAndItem() {
        final List<CombinePackageInfoDto> combinePackageInfoDtos = this.queryCombinePackageByCodes(null);
        final Set<String> combinePackageCodes = combinePackageInfoDtos.stream().map(CombinePackageInfoDto::getCombinePackageCode).collect(Collectors.toSet());

        final Map<String, List<TestItemDto>> combinePackageCodeAndItemMap = queryCombinePackageDetailInfo(combinePackageCodes);

        return combinePackageInfoDtos.stream().map(e -> {
            final CombinePackageInfoVo combinePackageInfoVo = JSON.parseObject(JSON.toJSONString(e), CombinePackageInfoVo.class);
            final List<TestItemDto> testItems = combinePackageCodeAndItemMap.getOrDefault(e.getCombinePackageCode(), Collections.emptyList());
            combinePackageInfoVo.setTestItems(testItems);
            return combinePackageInfoVo;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer deleteCombinePackageDetail(String combinePackageCode, Set<Long> testItemIds) {
        // 校验库中去掉删除的检验项目id后是否和其他套餐下的项目冲突
        final Set<Long> dbTestItemIds = this.queryCombinePackageDetailInfo(List.of(combinePackageCode)).getOrDefault(combinePackageCode, Collections.emptyList())
                .stream().map(TestItemDto::getTestItemId).collect(Collectors.toSet());
        dbTestItemIds.removeAll(testItemIds);
        final String md5 = this.md5(dbTestItemIds);
        this.checkPackageDetail(combinePackageCode, Set.of(), md5);
        this.updateTestItemIdsAndMd5(combinePackageCode, dbTestItemIds.size(), md5);
        // 删除
        itemPriceCombinePackageDetailService.deleteCombinePackageDetailInfo(combinePackageCode, testItemIds);
        return dbTestItemIds.size();
    }


    /**
     * 校验combinePackageCode下的相同的送检机构下相同的项目，
     *
     * @param combinePackageCode 财务套餐code
     * @param hspOrgCodes 参数 combinePackageCode 下的 送检机构codes
     * @param md5 需要判断其是否和combinePackageCode 下的检验项目完全相同
     */
    private void checkPackageDetail(String combinePackageCode, @NonNull Set<String> hspOrgCodes, String md5) {
        if (StringUtils.isBlank(md5)) {
            return;
        }
        // 查询当前套餐下的机构
        final List<TbItemPriceCombinePackage> combinePackageList = super.list(null);

        final Set<String> hspOrgCodeSet = new HashSet<>();
        // 如果存在直接使用
        if (CollectionUtils.isNotEmpty(hspOrgCodes)) {
            hspOrgCodeSet.addAll(hspOrgCodes);
        } else {
            // 如果不存在， 根据combinePackageCode过滤下面的送检机构
            final Set<TbItemPriceCombinePackage> combinePackageSet = combinePackageList.stream().filter(e -> Objects.equals(e.getCombinePackageCode(), combinePackageCode)).collect(Collectors.toSet());
            hspOrgCodeSet.addAll(combinePackageSet.stream().map(TbItemPriceCombinePackage::getHspOrgCode).collect(Collectors.toSet()));
        }
        // 根据当前财务套餐code删除
        final boolean bool = combinePackageList.removeIf(e -> Objects.equals(e.getCombinePackageCode(), combinePackageCode));
        if (!bool) {
            throw new IllegalStateException(String.format("套餐【%s】不存在", combinePackageCode));
        }
        // 筛选相同的送检机构
        combinePackageList.stream()
                // 包含当前财务套餐机构的
                .filter(e -> hspOrgCodeSet.contains(e.getHspOrgCode()))
                .forEach(e->{
                    if (Objects.equals(e.getItemMd5(), md5)) {
                        // throw new IllegalStateException(String.format("操作失败，和套餐编码【%s】中的项目一致", e.getCombinePackageCode()));
                    }
                });
    }


    //==================================================================================================================

    // 组装需要新增的套餐信息
    private List<TbItemPriceCombinePackage> getSaveCombinePackages(CombinePackageInfoDto combinePackageInfoDto, LoginUserHandler.User user) {

        final Date now = new Date();
        final List<TbItemPriceCombinePackage> saveCombinePackages = new ArrayList<>();

        for (CombinePackageInfoDto.HspOrgInfo hspOrgInfo : combinePackageInfoDto.getHspOrgList()) {
            TbItemPriceCombinePackage tempSaveCombinePackage = new TbItemPriceCombinePackage();

            tempSaveCombinePackage.setCombinePackageId(snowflakeService.genId());
            tempSaveCombinePackage.setCombinePackageCode(combinePackageInfoDto.getCombinePackageCode());
            tempSaveCombinePackage.setCombinePackageName(combinePackageInfoDto.getCombinePackageName().trim());
            tempSaveCombinePackage.setCombinePackagePrice(combinePackageInfoDto.getCombinePackagePrice());
            tempSaveCombinePackage.setItemCount(ObjectUtils.defaultIfNull(combinePackageInfoDto.getItemCount(), NumberUtils.INTEGER_ZERO));
            tempSaveCombinePackage.setItemMd5(StringUtils.defaultString(combinePackageInfoDto.getItemMd5(), Strings.EMPTY));
            tempSaveCombinePackage.setHspOrgId(hspOrgInfo.getHspOrgId());
            tempSaveCombinePackage.setHspOrgCode(hspOrgInfo.getHspOrgCode());
            tempSaveCombinePackage.setHspOrgName(hspOrgInfo.getHspOrgName());
            tempSaveCombinePackage.setOrgId(user.getOrgId());
            tempSaveCombinePackage.setOrgName(user.getOrgName());
            tempSaveCombinePackage.setCreateId(user.getUserId());
            tempSaveCombinePackage.setCreateName(user.getNickname());
            tempSaveCombinePackage.setCreateDate(now);
            tempSaveCombinePackage.setUpdateId(user.getUserId());
            tempSaveCombinePackage.setUpdateName(user.getNickname());
            tempSaveCombinePackage.setUpdateDate(now);
            tempSaveCombinePackage.setEnable(ObjectUtils.defaultIfNull(combinePackageInfoDto.getEnable(), YesOrNoEnum.YES.getCode()));
            tempSaveCombinePackage.setIsDelete(YesOrNoEnum.NO.getCode());

            saveCombinePackages.add(tempSaveCombinePackage);
        }

        return saveCombinePackages;
    }

    private String md5(Set<Long> testItemIds) {
        String md5Hash = "";
        if (CollectionUtils.isEmpty(testItemIds)) {
            return md5Hash;
        }
        final LinkedHashSet<Long> sort = testItemIds.stream().sorted().collect(Collectors.toCollection(LinkedHashSet::new));
        final String join = CollUtil.join(sort, ",");
        try {
            md5Hash = MD5Utils.md5Hex(join.getBytes());
        } catch (NoSuchAlgorithmException e) {
            log.error("财务套餐检验项目md5计算错误，{}", e);
        }
        return md5Hash;
    }
}

