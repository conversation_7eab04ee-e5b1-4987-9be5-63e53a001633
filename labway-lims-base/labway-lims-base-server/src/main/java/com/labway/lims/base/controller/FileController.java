package com.labway.lims.base.controller;

import ch.qos.logback.core.util.FileSize;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.FileDto;
import com.labway.lims.base.api.dto.FilePermissionDto;
import com.labway.lims.base.api.enums.FileTypeEnum;
import com.labway.lims.base.api.service.FilePermissionService;
import com.labway.lims.base.api.service.FileService;
import com.labway.lims.base.vo.*;
import com.obs.services.model.PostSignatureResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件
 */
@Slf4j
@RestController
@RequestMapping("/file")
public class FileController extends BaseController {

    @Resource
    private FileService fileService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;

    @Resource
    private FilePermissionService filePermissionService;

    /**
     * 获取文件夹详情
     */
    @GetMapping("/files")
    public List<FileVo> files(@RequestParam Long fileId) {
        final Long groupId = LoginUserHandler.get().getGroupId();

        // 0 是根
        if (fileId != 0) {
            final List<FilePermissionDto> pFilePermissions = filePermissionService.selectByFileId(fileId);
            if (pFilePermissions.stream().anyMatch(e -> Objects.equals(e.getGroupId(), groupId))) {
                return Collections.emptyList();
            }
        }


        final List<FileDto> files = new LinkedList<>(fileService.selectByParentFileId(fileId));
        if (CollectionUtils.isEmpty(files)) {
            return Collections.emptyList();
        }

        final Map<Long, List<FilePermissionDto>> filePermissions = filePermissionService.selectByFileIds(files.stream().map(FileDto::getFileId)
                        .collect(Collectors.toSet())).stream()
                .collect(Collectors.groupingBy(FilePermissionDto::getFileId));

        // 删除没有权限的
        files.removeIf(e -> filePermissions.getOrDefault(e.getFileId(), Collections.emptyList()).stream()
                .anyMatch(l -> Objects.equals(l.getGroupId(), groupId)));

        files.sort((o1, o2) -> o2.getFileType().compareTo(o1.getFileType()));

        final List<FileVo> fs = JSON.parseArray(JSON.toJSONString(files), FileVo.class);
        fs.forEach(e -> {
            if (Objects.isNull(e.getFileSize())) {
                e.setFileSizeText("-");
            } else {
                e.setFileSizeText(FileSize.valueOf(String.valueOf(e.getFileSize())).toString());
            }
        });

        return fs;
    }

    /**
     * 添加
     */
    @PostMapping("/add")
    public Object add(@RequestBody FileAddVo vo) {

        if (StringUtils.isAnyBlank(vo.getFileName())
                || Objects.isNull(vo.getFileType()) || Objects.isNull(vo.getParentFileId())) {
            throw new IllegalArgumentException("参数错误");
        }

        if (Objects.equals(vo.getFileType(), FileTypeEnum.FILE.getCode())) {
            if (StringUtils.isBlank(vo.getUrl())) {
                throw new IllegalArgumentException("文件地址不能为空");
            }

            if (Objects.isNull(vo.getFileSize())) {
                throw new IllegalArgumentException("文件大小不能为空");
            }
        }

        final FileDto file = new FileDto();
        BeanUtils.copyProperties(vo, file);

        return Map.of("id", fileService.addFile(file));
    }

    /**
     * 修改权限
     */
    @PostMapping("/update-permission")
    public Object updatePermissions(@RequestBody FilePermissionVo vo) {
        if (Objects.isNull(vo.getFileId())) {
            throw new IllegalArgumentException("参数错误");
        }

        // 清空
        filePermissionService.deleteByFileId(vo.getFileId());

        // 重新设置权限
        filePermissionService.addFilePermissions(vo.getFileId(), vo.getGroupIds());

        return Collections.emptyMap();
    }

    /**
     * 获取一个可以上传文件的权限
     */
    @PostMapping("/signature")
    public Object signature() {

        final String objectKey = huaweiObsUtils.genObjectKey();
        final PostSignatureResponse response = huaweiObsUtils.createPostSignature(objectKey, Duration.ofHours(1));

        final Map<String, String> map = Map.of(
                "baseUrl", huaweiObsUtils.getBaseUrl(),
                "objectKey", objectKey,
                "accessKeyId", huaweiObsUtils.getAk(),
                "signature", response.getSignature(),
                "policy", response.getPolicy()
        );

        log.info("用户 [{}] 生成 OBS 上传 TOKEN {}", LoginUserHandler.get().getNickname()
                , JSON.toJSONString(map));

        return map;
    }


    /**
     * 获取权限列表
     */
    @GetMapping("/permission")
    public Object permission(@RequestParam Long fileId) {
        if (Objects.isNull(fileId)) {
            throw new IllegalArgumentException("参数错误");
        }
        return filePermissionService.selectByFileId(fileId);
    }


    /**
     * 获取权限列表
     */
    @PostMapping("/rename")
    public Object rename(@RequestBody FileRenameVo vo) {
        if (Objects.isNull(vo.getFileId()) || StringUtils.isBlank(vo.getFileName())) {
            throw new IllegalArgumentException("参数错误");
        }

        if (StringUtils.length(vo.getFileName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("文件名长度不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }

        final FileDto file = new FileDto();
        file.setFileId(vo.getFileId());
        file.setFileName(vo.getFileName());

        if (!fileService.updateByFileId(file)) {
            throw new IllegalStateException("修改文件名失败");
        }

        return Collections.emptyMap();
    }


    /**
     * 获取权限列表
     */
    @PostMapping("/exists")
    public Object exists(@RequestBody FileExistsVo vo) {
        if (Objects.isNull(vo.getParentFileId()) || StringUtils.isBlank(vo.getFileName())) {
            throw new IllegalArgumentException("参数错误");
        }


        if (fileService.exists(vo.getParentFileId(), vo.getFileName())) {
            throw new IllegalStateException(String.format("文件 [%s] 已经存在", vo.getFileName()));
        }

        return Collections.emptyMap();
    }


    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    public Object delete(@RequestParam Long fileId) {
        if (Objects.isNull(fileId)) {
            throw new IllegalArgumentException("参数错误");
        }

        fileService.deleteByFileId(fileId);

        filePermissionService.deleteByFileId(fileId);

        return Collections.emptyMap();
    }
}
