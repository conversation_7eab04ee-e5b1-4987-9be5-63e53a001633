package com.labway.lims.base.mapstruct;

import com.labway.lims.base.api.dto.HspOrganizationMsgDto;
import com.labway.lims.base.model.TbHspOrganizationMsg;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 送检机构提示信息
 * 
 * <AUTHOR>
 * @since 2023/5/11 10:14
 */
@Mapper(componentModel = "spring")
public interface HspOrganizationMsgConverter {

    HspOrganizationMsgDto hspOrganizationMsgDtoFromTbObj(TbHspOrganizationMsg obj);

    List<HspOrganizationMsgDto> hspOrganizationMsgDtoListFromTbObjList(List<TbHspOrganizationMsg> list);

}
