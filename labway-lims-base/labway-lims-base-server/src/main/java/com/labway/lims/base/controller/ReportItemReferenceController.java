package com.labway.lims.base.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 仪器报告项目参考范围
 *
 * <AUTHOR>
 * @since 2023/3/20 14:29
 */
@RestController
@RequestMapping("/report-item-reference")
public class ReportItemReferenceController extends BaseController {
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    @DubboReference
    private InstrumentService instrumentService;

    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 获取参考范围列表
     */
    @GetMapping("/refers")
    public Object refers(@RequestParam Long instrumentReportItemId) {
        if (Objects.isNull(instrumentReportItemId)) {

            throw new IllegalArgumentException("参数错误");
        }
        return instrumentReportItemReferenceService.selectByInstrumentReportItemId(instrumentReportItemId);
    }

    /**
     * 添加参考范围
     */
    @PostMapping("/add")
    public Object add(@RequestBody InstrumentReportItemReferenceDto reference) throws Exception {

        if (Objects.isNull(reference.getInstrumentReportItemId())) {
            throw new IllegalArgumentException("instrumentReportItemId 不能为空");
        }

        checkRefParam(reference);


        return Map.of("id", instrumentReportItemReferenceService
                .addInstrumentReportItemReference(reference));
    }

    /**
     * 修改参考范围
     */
    @PostMapping("/update")
    public Object update(@RequestBody InstrumentReportItemReferenceDto reference) {
        if (Objects.isNull(reference.getInstrumentReportItemReferenceId())) {
            throw new IllegalArgumentException("参数错误");
        }

        checkRefParam(reference);

        InstrumentReportItemReferenceDto referenceOld =
                instrumentReportItemReferenceService.selectById(reference.getInstrumentReportItemReferenceId());
        if (Objects.isNull(referenceOld)) {
            throw new IllegalStateException("参考范围不存在");
        }

        if (!instrumentReportItemReferenceService.updateByInstrumentReportItemReferenceId(reference)) {
            throw new IllegalStateException("修改参考范围失败");
        }


        // 日志
        final String compare = new CompareUtils<InstrumentReportItemReferenceDto>().compare(referenceOld, reference);
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_REFERENCE.getDesc())
                        .setContent(String.format("用户 [%s] 修改了仪器 [%s] 下的报告项目 [%s] 的参考范围，修改信息 [%s]",
                                LoginUserHandler.get().getNickname(),
                                referenceOld.getInstrumentName(),
                                referenceOld.getReportItemName(),
                                compare
                        )).toJSONString());
        return Collections.emptyMap();
    }


    /**
     * 删除参考范围
     */
    @DeleteMapping("/delete")
    public Object delete(@RequestBody Set<Long> instrumentReportItemReferenceIds) {


        if (CollectionUtils.isEmpty(instrumentReportItemReferenceIds)) {
            return Collections.emptyMap();
        }

        final Map<Long, InstrumentReportItemReferenceDto> refrenceMap = instrumentReportItemReferenceService.selectByIds(instrumentReportItemReferenceIds)
                .stream().collect(Collectors.toMap(InstrumentReportItemReferenceDto::getInstrumentReportItemReferenceId,
                        Function.identity(), (a, b) -> a));

        if (MapUtils.isEmpty(refrenceMap)) {
            return Collections.emptyMap();
        }

        for (Long instrumentReportItemReferenceId : instrumentReportItemReferenceIds) {

            instrumentReportItemReferenceService.deleteByInstrumentReportItemReferenceId(instrumentReportItemReferenceId);

            // 记录日子
            final InstrumentReportItemReferenceDto reference = refrenceMap.get(instrumentReportItemReferenceId);
            if (Objects.isNull(reference)) {
                continue;
            }

            String logContent = String.format("用户 [%s] 删除仪器 [%s] 下的报告项目 [%s] 参考范围;参考范围信息：" +
                            "样本类型 [%s] 适用性别 [%s] 适用年龄下限 [%s] 适用年龄上限 [%s] 年龄单位 [%s] 参考范围下限 [%s] 参考范围上限 [%s]" +
                            " 异常提示下限 [%s] 异常提示上限 [%s] 危急值下限 [%s] 危急值上限 [%s] 中文参考值 [%s] 英文参考值 [%s] 中英文参考值 [%s]",
                    LoginUserHandler.get().getNickname(),
                    reference.getInstrumentName(),
                    reference.getReportItemName(),
                    reference.getSampleTypeName(),
                    reference.getSexStyleName(),
                    reference.getAgeMin(),
                    reference.getAgeMax(),
                    reference.getAgeUnit(),
                    reference.getReferValueMin(),
                    reference.getReferValueMax(),
                    reference.getExcpWarningMin(),
                    reference.getExcpWarningMax(),
                    reference.getFatalMin(),
                    reference.getFatalMax(),
                    reference.getCnRefereValue(),
                    reference.getEnRefereValue(),
                    reference.getCnEnRefereValue()
            );
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_REFERENCE.getDesc())
                            .setContent(logContent).toJSONString());

        }


        return Collections.emptyMap();
    }

    public static void checkRefParam(InstrumentReportItemReferenceDto reference) {
        if (Objects.isNull(reference.getSexStyle()) || StringUtils.isBlank(reference.getSexStyleName())) {
            throw new IllegalArgumentException("性别错误");
        }

        if (StringUtils.isAnyBlank(reference.getSampleTypeCode(), reference.getSampleTypeName())) {
            throw new IllegalArgumentException("样本类型错误");
        }

        // 年龄范围
        if ((Objects.nonNull(reference.getAgeMax()) && Objects.isNull(reference.getAgeMin()))
                || (Objects.nonNull(reference.getAgeMin()) && Objects.isNull(reference.getAgeMax()))) {
            throw new IllegalArgumentException("年龄范围参数错误");
        }

        if (StringUtils.isAnyBlank(reference.getAgeMaxFormula(), reference.getAgeMinFormula())) {
            throw new IllegalArgumentException("年龄范围参数错误");
        }

        if (Objects.nonNull(reference.getAgeMax()) && (reference.getAgeMin() < 0 || reference.getAgeMax() < reference.getAgeMin())) {
            throw new IllegalArgumentException("年龄范围参数错误");
        }

        // 参考范围上下限
//        if ((StringUtils.isNotBlank(reference.getReferValueMax()) && StringUtils.isBlank(reference.getReferValueMin()))
//                || (StringUtils.isNotBlank(reference.getReferValueMin()) && StringUtils.isBlank(reference.getReferValueMax()))) {
//            throw new IllegalArgumentException("参考范围上下限错误");
//        }

        if (StringUtils.isNotBlank(reference.getReferValueMax()) && (NumberUtils.toDouble(reference.getReferValueMax()) < NumberUtils.toDouble(reference.getReferValueMin()))) {
            throw new IllegalArgumentException("参考范围上下限错误");
        }


        // 异常范围上下限
        if ((StringUtils.isNoneBlank(reference.getExcpWarningMax(), reference.getExcpWarningMin())) && (NumberUtils.toDouble(reference.getExcpWarningMax()) <
                NumberUtils.toDouble(reference.getExcpWarningMin()))) {
            throw new IllegalArgumentException("异常范围上下限错误");
        }

        // 危机范围上下限
        if ((StringUtils.isNoneBlank(reference.getFatalMax(), reference.getFatalMin())) && (NumberUtils.toDouble(reference.getFatalMax()) <
                NumberUtils.toDouble(reference.getFatalMin()))) {
            throw new IllegalArgumentException("危机范围上下限错误");
        }
    }

}
