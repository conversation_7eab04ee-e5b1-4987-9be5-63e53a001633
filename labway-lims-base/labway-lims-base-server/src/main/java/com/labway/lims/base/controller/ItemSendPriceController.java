package com.labway.lims.base.controller;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.utils.FileUtils;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.ItemSendPriceDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ItemSendPriceService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.vo.ImportItemSendPriceExcelVo;
import com.labway.lims.base.vo.ItemSendPriceAddRequestVo;
import com.labway.lims.base.vo.ItemSendPriceUpdateRequestVo;
import com.labway.lims.base.vo.SelectItemSendPriceAllResponseVo;
import com.labway.lims.base.vo.excel.ExportSelectItemSendPriceAllResponseVo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.api.ConditionCheckUtils.isDecimalWithTwoDigits;

/**
 * 外送项目价格设置 API
 *
 * <AUTHOR>
 * @since 2023/5/4 15:12
 */
@Slf4j
@RestController
@RequestMapping("/item-send-price")
public class ItemSendPriceController extends BaseController {

    @Resource
    private TestItemService testItemService;
    @Resource
    private ItemSendPriceService itemSendPriceService;
    @Resource
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    /**
     * 外送机构-添加外送项目
     */
    @PostMapping("/add")
    public Object itemSendPriceAdd(@RequestBody ItemSendPriceAddRequestVo vo) {
        if (Objects.isNull(vo.getHspOrgId()) || Objects.isNull(vo.getStartDate()) || Objects.isNull(vo.getEndDate())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        // 添加项目为空
        if (CollectionUtils.isEmpty(vo.getTestItemList())) {
            return Collections.emptyMap();
        }
        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束日期不可小于生效日期");
        }
        if (vo.getTestItemList().stream()
            .anyMatch(obj -> Objects.isNull(obj.getTestItemId()) || StringUtils.isBlank(obj.getSendPrice()))) {
            throw new LimsException("选择项目不规范:存在必填项为空");
        }
        if (vo.getTestItemList().stream().map(ItemSendPriceAddRequestVo.ItemSendPriceItemVo::getTestItemId).distinct()
            .count() < vo.getTestItemList().size()) {
            throw new LimsException("选择项目不规范:存在重复项目");
        }
        if (vo.getTestItemList().stream().anyMatch(obj -> !isDecimalWithTwoDigits(obj.getSendPrice()))) {
            throw new LimsException("选择项目不规范:项目外送价格只允许填写数字且最多允许两位小数");
        }
        // 选择外送机构
        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new LimsException("外送机构不存在");
        }
        if (!Objects.equals(hspOrganization.getIsExport(), YesOrNoEnum.YES.getCode())) {
            throw new LimsException("所选机构不为外送机构");
        }
        // 选择检验项目
        final Set<Long> testItemIds = vo.getTestItemList().stream()
            .map(ItemSendPriceAddRequestVo.ItemSendPriceItemVo::getTestItemId).collect(Collectors.toSet());

        // 当前外送机构已有外送项目
        final List<ItemSendPriceDto> itemSendPriceDtos = itemSendPriceService.selectByHspOrgId(vo.getHspOrgId());
        final Set<Long> testItemIdsNow =
            itemSendPriceDtos.stream().map(ItemSendPriceDto::getTestItemId).collect(Collectors.toSet());
        if (testItemIds.stream().anyMatch(testItemIdsNow::contains)) {
            throw new LimsException("选择项目不规范:存在已添加项目");
        }

        // 选择检验项目详细信息
        final List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(testItemIds);
        final Map<Long, TestItemDto> testItemByTestItemId =
            testItemDtos.stream().collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));
        if (testItemIds.stream().anyMatch(x -> !testItemByTestItemId.containsKey(x))) {
            throw new LimsException("选择项目不规范:存在无效检验项目");
        }
        if (testItemDtos.stream().anyMatch(obj -> !Objects.equals(obj.getEnableExport(), YesOrNoEnum.YES.getCode()))) {
            throw new LimsException("选择项目不规范:存在不支持外送项目");
        }
        if (testItemDtos.stream().anyMatch(obj -> !Objects.equals(obj.getExportOrgId(), vo.getHspOrgId()))) {
            throw new LimsException("选择项目不规范:存在项目外送机构不为当前外送机构");
        }

        // 需要新增项
        List<ItemSendPriceDto> targetList = Lists.newArrayListWithCapacity(vo.getTestItemList().size());

        vo.getTestItemList().forEach(item -> {

            BigDecimal sendPriceInt = new BigDecimal(item.getSendPrice()).setScale(2, RoundingMode.HALF_UP);

            TestItemDto testItemDto = testItemByTestItemId.get(item.getTestItemId());

            ItemSendPriceDto target = new ItemSendPriceDto();
            target.setPriceId(snowflakeService.genId());
            target.setHspOrgId(hspOrganization.getHspOrgId());
            target.setHspOrgName(hspOrganization.getHspOrgName());
            target.setTestItemId(testItemDto.getTestItemId());
            target.setTestItemCode(testItemDto.getTestItemCode());
            target.setStartDate(vo.getStartDate());
            target.setEndDate(vo.getEndDate());
            target.setSendPrice(sendPriceInt);
            targetList.add(target);
        });

        itemSendPriceService.addItemSendPrices(targetList);

        this.sendMq(targetList, hspOrganization);

        return Collections.emptyMap();
    }

    private void sendMq(List<ItemSendPriceDto> targetList, HspOrganizationDto hspOrganization) {
        final StringBuilder item = targetList.stream().reduce(new StringBuilder(),
            (sb, e) -> sb.append(String.format("检验项目 [%s] 外送价格 [%s] 生效日期 [%s] 结束日期 [%s]", e.getTestItemCode(),
                e.getSendPrice(), DateFormatUtils.format(e.getStartDate(), "yyyy-MM-dd HH:mm:ss"),
                DateFormatUtils.format(e.getEndDate(), "yyyy-MM-dd HH:mm:ss"))),
            StringBuilder::append);

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.SEND_ITEM_PRICE.getDesc()).setType("新增")
                .setContent(String.format("新增外送价格维护 外送机构 [%s]\n%s", hspOrganization.getHspOrgName(), item))
                .toJSONString());
    }

    /**
     * 外送机构-删除外送项目
     */
    @PostMapping("/delete")
    public Object itemSendPriceDelete(@RequestBody Set<Long> priceIds) {

        if (CollectionUtils.isEmpty(priceIds)) {
            return Map.of();
        }

        final List<ItemSendPriceDto> ps = itemSendPriceService.selectByPriceIds(priceIds);

        itemSendPriceService.deleteByPriceIds(priceIds);

        final StringBuilder item = ps.stream().reduce(new StringBuilder(),
            (sb, e) -> sb.append(String.format("外送机构 [%s] 检验项目 [%s]", e.getHspOrgName(), e.getTestItemCode())),
            StringBuilder::append);

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.SEND_ITEM_PRICE.getDesc()).setType("删除")
                .setContent(String.format("删除外送价格维护%n%s", item)).toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 外送机构-修改外送项目
     */
    @PostMapping("/update")
    public Object itemSendPriceUpdate(@RequestBody ItemSendPriceUpdateRequestVo vo) {
        if (Objects.isNull(vo.getPriceId()) || Objects.isNull(vo.getStartDate()) || Objects.isNull(vo.getEndDate())
            || StringUtils.isBlank(vo.getSendPrice())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        if (!isDecimalWithTwoDigits(vo.getSendPrice())) {
            throw new LimsException("外送价格不规范:只允许填写数字且最多允许两位小数");
        }
        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束日期不可小于生效日期");
        }
        final ItemSendPriceDto itemSendPriceDto = itemSendPriceService.selectByPriceId(vo.getPriceId());
        if (Objects.isNull(itemSendPriceDto)) {
            throw new LimsException("选择外送项目不存在");
        }

        ItemSendPriceDto target = new ItemSendPriceDto();
        BeanUtils.copyProperties(itemSendPriceDto, target);

        // 更新项
        BigDecimal sendPriceInt = new BigDecimal(vo.getSendPrice()).setScale(2, RoundingMode.HALF_UP);

        target.setSendPrice(sendPriceInt);
        target.setStartDate(vo.getStartDate());
        target.setEndDate(vo.getEndDate());

        itemSendPriceService.updateByPriceId(target);

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.SEND_ITEM_PRICE.getDesc()).setType("修改")
                .setContent(String.format("修改外送价格维护 外送机构 [%s] 检验项目 [%s] 外送价格 [%s] 生效日期 [%s] 结束日期 [%s]",
                    itemSendPriceDto.getHspOrgName(), itemSendPriceDto.getTestItemCode(), vo.getSendPrice(),
                    DateFormatUtils.format(vo.getStartDate(), "yyyy-MM-dd HH:mm:ss"),
                    DateFormatUtils.format(vo.getEndDate(), "yyyy-MM-dd HH:mm:ss")))
                .toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 外送机构-查询外送项目
     */
    @PostMapping("/select-all")
    public Object itemSendPriceList(@RequestParam long hspOrgId) {
        final List<ItemSendPriceDto> itemSendPriceDtos = itemSendPriceService.selectByHspOrgId(hspOrgId);

        // 对应检验项目
        final Set<Long> testItemIds =
            itemSendPriceDtos.stream().map(ItemSendPriceDto::getTestItemId).collect(Collectors.toSet());
        final List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(testItemIds);
        final Map<Long, TestItemDto> testItemDByTestItemId =
            testItemDtos.stream().collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));

        final List<SelectItemSendPriceAllResponseVo> targetList =
            Lists.newArrayListWithCapacity(itemSendPriceDtos.size());

        for (ItemSendPriceDto dto : itemSendPriceDtos) {

            TestItemDto testItemDto = testItemDByTestItemId.get(dto.getTestItemId());

            if (Objects.isNull(testItemDto)) {
                // 对应检验项目不存在 不展示？？？
                continue;
            }

            SelectItemSendPriceAllResponseVo temp = new SelectItemSendPriceAllResponseVo();
            BeanUtils.copyProperties(dto, temp);

            String sendPriceStr = String.valueOf(dto.getSendPrice());

            temp.setTestItemName(testItemDto.getTestItemName());
            temp.setSendPrice(sendPriceStr);
            temp.setFeePrice(testItemDto.getFeePrice());
            temp.setEnable(testItemDto.getEnable());
            targetList.add(temp);
        }

        return targetList;
    }

    /**
     * 外送机构-导出外送项目
     */
    @PostMapping("/export-all")
    public void exportItemSendPriceList(@RequestParam long hspOrgId, HttpServletResponse response) throws IOException {
        HspOrganizationDto hspOrganizationDto = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganizationDto)) {
            throw new IllegalArgumentException("送检机构不存在");
        }

        List<ExportSelectItemSendPriceAllResponseVo> targetList =
                JSON.parseArray(JSON.toJSONString(this.itemSendPriceList(hspOrgId)), ExportSelectItemSendPriceAllResponseVo.class);

        for (int i = 0; i < targetList.size(); i++) {
            targetList.get(i).setNum(i + 1);
        }

        try (InputStream template = ResourceUtil.getStream("classpath:template/外送价格维护.xlsx")) {
            response.setHeader("Content-disposition", "attachment; filename=" +
                    URLEncoder.encode("外送价格维护-" + hspOrganizationDto.getHspOrgName() + "外送机构.xlsx", StandardCharsets.UTF_8));
            ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream())
                    .withTemplate(template)
                    .excelType(ExcelTypeEnum.XLSX)
                    .build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();

            WriteSheet sheet1 = EasyExcelFactory.writerSheet(0).build();
            // 要遍历的表格数据
            excelWriter.fill(targetList, fillConfig, sheet1);
            excelWriter.finish();
        }
    }

    /**
     * <a href="https://www.tapd.cn/tapd_fe/59091617/story/detail/1159091617001003143">
     *     【【外送价格维护】1、增加导出功能；2、增加模板下载和导入功能】
     * </a>
     */
    @PostMapping("/import")
    public Object importItemSendPrice(@RequestParam("hspOrgId") long hspOrgId,
                                    @RequestPart("file") MultipartFile file) {
        if (!FileUtils.isExcelFile(file)){
            throw new IllegalStateException("只允许上传 xls 和 xlsx 格式的文件");
        }

        // 选择外送机构
        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            throw new LimsException("外送机构不存在");
        }
        if (!Objects.equals(hspOrganization.getIsExport(), YesOrNoEnum.YES.getCode())) {
            throw new LimsException("所选机构不为外送机构");
        }

        // 送检机构下 已维护的项目信息
        final List<ItemSendPriceDto> itemSendPriceDtos = itemSendPriceService.selectByHspOrgId(hspOrgId);

        // 检验项目信息
        final Map<String, TestItemDto> testItemMap = testItemService.selectByOrgId(LoginUserHandler.get().getOrgId())
                .stream().collect(Collectors.toMap(TestItemDto::getTestItemCode, Function.identity(), (a,b)->a));

        // 读取文件
        final ImportItemSendPriceListener listener = new ImportItemSendPriceListener(
                hspOrganization, itemSendPriceDtos, testItemMap);
        try (ExcelReader excelReader = EasyExcelFactory.read(file.getInputStream()).build()) {
            // 读取sheet页
            ReadSheet readSheet = EasyExcelFactory.readSheet(0)
                    .head(ImportItemSendPriceExcelVo.class)
                    .registerReadListener(listener).autoTrim(true).build();

            excelReader.read(readSheet);

            // 判断是否有异常信息
            if (MapUtils.isNotEmpty(listener.getErrorMap())) {
                throw new LimsCodeException(1244, JSON.toJSONString(listener.getErrorMap()));
            }

            // 表中 经过 校验成功的数据
            final List<ImportItemSendPriceExcelVo> dataList = listener.getDataList();
            // 批量获取id
            final LinkedList<Long> ids = snowflakeService.genIds(dataList.size());
            // 构建写入库中的数据
            final List<ItemSendPriceDto> targetList = dataList
                    .stream()
                    .map(e -> {
                        ItemSendPriceDto target = new ItemSendPriceDto();
                        final TestItemDto testItemDto = testItemMap.get(e.getTestItemCode());
                        target.setPriceId(ids.pop());
                        target.setHspOrgId(hspOrganization.getHspOrgId());
                        target.setHspOrgName(hspOrganization.getHspOrgName());
                        target.setTestItemId(testItemDto.getTestItemId());
                        target.setTestItemCode(testItemDto.getTestItemCode());
                        target.setStartDate(e.getStartDate());
                        target.setEndDate(e.getEndDate());
                        target.setSendPrice(e.getSendPrice());
                        return target;
                    })
                    .collect(Collectors.toList());

            itemSendPriceService.addItemSendPrices(targetList);

            this.sendMq(targetList, hspOrganization);
        } catch (IOException e) {
            log.error("导入出错", e);
            throw new IllegalStateException("导入出错:" + e.getMessage());
        }


        return listener.getDataList();
    }


    private static class ImportItemSendPriceListener extends AnalysisEventListener<ImportItemSendPriceExcelVo> {

        @Getter
        private final List<ImportItemSendPriceExcelVo> dataList = new ArrayList<>();

        // 错误信息  key = rowIndex    value = errorMsg
        @Getter
        private final Map<Integer, List<String>> errorMap = new HashMap<>();


        // 校验重复检验项目编码
        private final Set<String> testItemCodeSet = new HashSet<>();

        // 送检机构下的检验项目信息
        private final  HspOrganizationDto hspOrganization;

        // 送检机构下的检验项目信息
        private final Map<String, ItemSendPriceDto> itemSendPriceMap;

        // 送检机构下的检验项目信息
        private final Map<String, TestItemDto> testItemMap;

        public ImportItemSendPriceListener(
                @NotNull HspOrganizationDto hspOrganization,
                @NotNull List<ItemSendPriceDto> itemSendPriceDtos,
                @NotNull Map<String, TestItemDto> testItemMap) {
            this.hspOrganization = hspOrganization;
            this.itemSendPriceMap = itemSendPriceDtos
                    .stream()
                    .collect(Collectors.toMap(ItemSendPriceDto::getTestItemCode,
                            Function.identity(),
                            (a, b) -> a));

            this.testItemMap = testItemMap;
        }

        @Override
        public void invoke(ImportItemSendPriceExcelVo vo, AnalysisContext analysisContext) {
            ReadRowHolder readRowHolder = analysisContext.readRowHolder();
            Integer rowIndex = readRowHolder.getRowIndex() + 1;

            if (StringUtils.isBlank(vo.getTestItemCode())) {
                addError(rowIndex, "检验项目编码不能为空");
            }

            // 判断检验项目编码是否重复
            if (!testItemCodeSet.add(vo.getTestItemCode())) {
                addError(rowIndex, "存在重复项目");
            }
            // 判断检验项目编码是否合法
            final TestItemDto testItemDto = testItemMap.get(vo.getTestItemCode());
            if (testItemDto == null) {
                addError(rowIndex, "存在无效检验项目");
            } else {
                if (!Objects.equals(testItemDto.getEnableExport(), YesOrNoEnum.YES.getCode())) {
                    addError(rowIndex, "存在不支持外送项目");
                }
                if (!Objects.equals(testItemDto.getExportOrgId(), hspOrganization.getHspOrgId())) {
                    addError(rowIndex, "存在项目外送机构不为当前外送机构");
                }
            }

            // 判断是否已维护
            if (itemSendPriceMap.get(vo.getTestItemCode()) != null) {
                addError(rowIndex, "存在已添加项目");
            }
            // 价格是否异常
            if (StringUtils.isBlank(vo.getSendPriceStr()) || !NumberUtils.isCreatable(vo.getSendPriceStr())) {
                addError(rowIndex, "项目外送价格只允许填写数字");
            }

            // 校验时间
            try {
                if (!vo.getStartDate().before(vo.getEndDate())) {
                    addError(rowIndex, "开始时间不能大于结束时间");
                }
            } catch (Exception e) {
                addError(rowIndex, String.format("时间格式异常: [%s] [%s]", vo.getStartDateStr(), vo.getEndDateStr()));
            }

            dataList.add(vo);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {

        }

        // 收集错误信息
        private void addError(Integer rowIndex, String errorMsg) {
            errorMap.merge(rowIndex, new ArrayList<>() {{
                add(errorMsg);
            }}, (oldValue, newValue) -> {
                oldValue.addAll(newValue);
                return oldValue;
            });
        }
    }

    /**
     * 外送机构-导出外送项目, 同时导出多个送检机构的
     */
    @PostMapping("/export-hsp-org-all")
    public Object exportItemSendPriceList(@RequestBody List<Long> hspOrgIds) throws IOException {
        final List<HspOrganizationDto> hspOrganizationDtos = hspOrganizationService.selectByHspOrgIds(hspOrgIds);
        if (CollectionUtils.isEmpty(hspOrganizationDtos)) {
            throw new IllegalArgumentException("送检机构不存在");
        }

        // 送检机构map
        final Map<Long, HspOrganizationDto> hspOrganizationMap = hspOrganizationDtos.stream().collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity()));

        // 根据送检机构查询下面的外送价格维护信息
        final Map<Long, List<ItemSendPriceDto>> itemPriceAsMap = itemSendPriceService.selectByHspOrgIdsAsMap(hspOrganizationMap.keySet());

        // 检验项目ids
        final List<Long> testItemIds = itemPriceAsMap.values().stream().flatMap(Collection::stream).map(ItemSendPriceDto::getTestItemId).collect(Collectors.toList());

        // 检验项目信息
        final Map<Long, TestItemDto> testItemDByTestItemId = testItemService.selectByTestItemIds(testItemIds)
                .stream()
                .collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity(), (a, b) -> a));


        // 填充导出的数据
        final List<SelectItemSendPriceAllResponseVo> targetList = Lists.newArrayListWithCapacity(testItemIds.size());
        itemPriceAsMap.forEach((key, value) -> {
            for (ItemSendPriceDto dto : value) {
                TestItemDto testItemDto = testItemDByTestItemId.get(dto.getTestItemId());
                if (Objects.isNull(testItemDto)) {
                    // 对应检验项目不存在 不展示
                    continue;
                }

                final HspOrganizationDto hspOrganizationDto = hspOrganizationMap.get(dto.getHspOrgId());
                if (Objects.isNull(hspOrganizationDto)) {
                    // 送检机构不存在？
                    continue;
                }

                SelectItemSendPriceAllResponseVo temp = new SelectItemSendPriceAllResponseVo();
                BeanUtils.copyProperties(dto, temp);
                temp.setHspOrgCode(hspOrganizationDto.getHspOrgCode());
                temp.setTestItemName(testItemDto.getTestItemName());
                temp.setSendPrice(String.valueOf(dto.getSendPrice()));
                temp.setFeePrice(testItemDto.getFeePrice());
                temp.setEnable(testItemDto.getEnable());
                targetList.add(temp);
            }
        });

        final File tempFile = File.createTempFile("item-send-price-dto-export", null);

        // 创建到处对象
        try (cn.hutool.poi.excel.ExcelWriter writer = ExcelUtil.getBigWriter();
             FileOutputStream fos = new FileOutputStream(tempFile)) {
            List<Object> builder = new LinkedList<>(List.of(
                    "外送机构编码", "外送机构名称",
                    "检验项目编码", "检验项目名称",
                    "收费价格", "外送价格",
                    "生效日期", "结束日期"
            ));
            writer.writeHeadRow(builder);

            for (SelectItemSendPriceAllResponseVo vo : targetList) {
                writer.writeRow(List.of(vo.getHspOrgCode(), vo.getHspOrgName(),
                        vo.getTestItemCode(), vo.getTestItemName(),
                        vo.getFeePrice(), vo.getSendPrice(),
                        dateFormat.format(vo.getStartDate()), dateFormat.format(vo.getEndDate())));
            }

            writer.flush(fos);
        }
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("外送价格维护.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(new FileSystemResource(tempFile));
    }

}
