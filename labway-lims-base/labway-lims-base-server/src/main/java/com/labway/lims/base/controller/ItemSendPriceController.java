package com.labway.lims.base.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.FileUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.ItemSendPriceDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ItemSendPriceService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.vo.ItemSendPriceAddRequestVo;
import com.labway.lims.base.vo.ItemSendPriceUpdateRequestVo;
import com.labway.lims.base.vo.SelectItemSendPriceAllResponseVo;
import com.labway.lims.base.vo.excel.ExportSelectItemSendPriceAllResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.api.ConditionCheckUtils.isDecimalWithTwoDigits;

/**
 * 外送项目价格设置 API
 *
 * <AUTHOR>
 * @since 2023/5/4 15:12
 */
@Slf4j
@RestController
@RequestMapping("/item-send-price")
public class ItemSendPriceController extends BaseController {

    @Resource
    private TestItemService testItemService;
    @Resource
    private ItemSendPriceService itemSendPriceService;
    @Resource
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 外送机构-添加外送项目
     */
    @PostMapping("/add")
    public Object itemSendPriceAdd(@RequestBody ItemSendPriceAddRequestVo vo) {
        if (Objects.isNull(vo.getHspOrgId()) || Objects.isNull(vo.getStartDate()) || Objects.isNull(vo.getEndDate())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        // 添加项目为空
        if (CollectionUtils.isEmpty(vo.getTestItemList())) {
            return Collections.emptyMap();
        }
        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束日期不可小于生效日期");
        }
        if (vo.getTestItemList().stream()
            .anyMatch(obj -> Objects.isNull(obj.getTestItemId()) || StringUtils.isBlank(obj.getSendPrice()))) {
            throw new LimsException("选择项目不规范:存在必填项为空");
        }
        if (vo.getTestItemList().stream().map(ItemSendPriceAddRequestVo.ItemSendPriceItemVo::getTestItemId).distinct()
            .count() < vo.getTestItemList().size()) {
            throw new LimsException("选择项目不规范:存在重复项目");
        }
        if (vo.getTestItemList().stream().anyMatch(obj -> !isDecimalWithTwoDigits(obj.getSendPrice()))) {
            throw new LimsException("选择项目不规范:项目外送价格只允许填写数字且最多允许两位小数");
        }
        // 选择外送机构
        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new LimsException("外送机构不存在");
        }
        if (!Objects.equals(hspOrganization.getIsExport(), YesOrNoEnum.YES.getCode())) {
            throw new LimsException("所选机构不为外送机构");
        }
        // 选择检验项目
        final Set<Long> testItemIds = vo.getTestItemList().stream()
            .map(ItemSendPriceAddRequestVo.ItemSendPriceItemVo::getTestItemId).collect(Collectors.toSet());

        // 当前外送机构已有外送项目
        final List<ItemSendPriceDto> itemSendPriceDtos = itemSendPriceService.selectByHspOrgId(vo.getHspOrgId());
        final Set<Long> testItemIdsNow =
            itemSendPriceDtos.stream().map(ItemSendPriceDto::getTestItemId).collect(Collectors.toSet());
        if (testItemIds.stream().anyMatch(testItemIdsNow::contains)) {
            throw new LimsException("选择项目不规范:存在已添加项目");
        }

        // 选择检验项目详细信息
        final List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(testItemIds);
        final Map<Long, TestItemDto> testItemByTestItemId =
            testItemDtos.stream().collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));
        if (testItemIds.stream().anyMatch(x -> !testItemByTestItemId.containsKey(x))) {
            throw new LimsException("选择项目不规范:存在无效检验项目");
        }
        if (testItemDtos.stream().anyMatch(obj -> !Objects.equals(obj.getEnableExport(), YesOrNoEnum.YES.getCode()))) {
            throw new LimsException("选择项目不规范:存在不支持外送项目");
        }
        if (testItemDtos.stream().anyMatch(obj -> !Objects.equals(obj.getExportOrgId(), vo.getHspOrgId()))) {
            throw new LimsException("选择项目不规范:存在项目外送机构不为当前外送机构");
        }

        // 需要新增项
        List<ItemSendPriceDto> targetList = Lists.newArrayListWithCapacity(vo.getTestItemList().size());

        vo.getTestItemList().forEach(item -> {

            BigDecimal sendPriceInt = new BigDecimal(item.getSendPrice()).setScale(2, RoundingMode.HALF_UP);

            TestItemDto testItemDto = testItemByTestItemId.get(item.getTestItemId());

            ItemSendPriceDto target = new ItemSendPriceDto();
            target.setPriceId(snowflakeService.genId());
            target.setHspOrgId(hspOrganization.getHspOrgId());
            target.setHspOrgName(hspOrganization.getHspOrgName());
            target.setTestItemId(testItemDto.getTestItemId());
            target.setTestItemCode(testItemDto.getTestItemCode());
            target.setStartDate(vo.getStartDate());
            target.setEndDate(vo.getEndDate());
            target.setSendPrice(sendPriceInt);
            targetList.add(target);
        });

        itemSendPriceService.addItemSendPrices(targetList);

        final StringBuilder item = targetList.stream().reduce(new StringBuilder(),
            (sb, e) -> sb.append(String.format("检验项目 [%s] 外送价格 [%s] 生效日期 [%s] 结束日期 [%s]", e.getTestItemCode(),
                e.getSendPrice(), DateFormatUtils.format(e.getStartDate(), "yyyy-MM-dd HH:mm:ss"),
                DateFormatUtils.format(e.getEndDate(), "yyyy-MM-dd HH:mm:ss"))),
            StringBuilder::append);

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.SEND_ITEM_PRICE.getDesc()).setType("新增")
                .setContent(String.format("新增外送价格维护 外送机构 [%s]\n%s", hspOrganization.getHspOrgName(), item))
                .toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 外送机构-删除外送项目
     */
    @PostMapping("/delete")
    public Object itemSendPriceDelete(@RequestBody Set<Long> priceIds) {

        if (CollectionUtils.isEmpty(priceIds)) {
            return Map.of();
        }

        final List<ItemSendPriceDto> ps = itemSendPriceService.selectByPriceIds(priceIds);

        itemSendPriceService.deleteByPriceIds(priceIds);

        final StringBuilder item = ps.stream().reduce(new StringBuilder(),
            (sb, e) -> sb.append(String.format("外送机构 [%s] 检验项目 [%s]", e.getHspOrgName(), e.getTestItemCode())),
            StringBuilder::append);

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.SEND_ITEM_PRICE.getDesc()).setType("删除")
                .setContent(String.format("删除外送价格维护%n%s", item)).toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 外送机构-修改外送项目
     */
    @PostMapping("/update")
    public Object itemSendPriceUpdate(@RequestBody ItemSendPriceUpdateRequestVo vo) {
        if (Objects.isNull(vo.getPriceId()) || Objects.isNull(vo.getStartDate()) || Objects.isNull(vo.getEndDate())
            || StringUtils.isBlank(vo.getSendPrice())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        if (!isDecimalWithTwoDigits(vo.getSendPrice())) {
            throw new LimsException("外送价格不规范:只允许填写数字且最多允许两位小数");
        }
        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束日期不可小于生效日期");
        }
        final ItemSendPriceDto itemSendPriceDto = itemSendPriceService.selectByPriceId(vo.getPriceId());
        if (Objects.isNull(itemSendPriceDto)) {
            throw new LimsException("选择外送项目不存在");
        }

        ItemSendPriceDto target = new ItemSendPriceDto();
        BeanUtils.copyProperties(itemSendPriceDto, target);

        // 更新项
        BigDecimal sendPriceInt = new BigDecimal(vo.getSendPrice()).setScale(2, RoundingMode.HALF_UP);

        target.setSendPrice(sendPriceInt);
        target.setStartDate(vo.getStartDate());
        target.setEndDate(vo.getEndDate());

        itemSendPriceService.updateByPriceId(target);

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.SEND_ITEM_PRICE.getDesc()).setType("修改")
                .setContent(String.format("修改外送价格维护 外送机构 [%s] 检验项目 [%s] 外送价格 [%s] 生效日期 [%s] 结束日期 [%s]",
                    itemSendPriceDto.getHspOrgName(), itemSendPriceDto.getTestItemCode(), vo.getSendPrice(),
                    DateFormatUtils.format(vo.getStartDate(), "yyyy-MM-dd HH:mm:ss"),
                    DateFormatUtils.format(vo.getEndDate(), "yyyy-MM-dd HH:mm:ss")))
                .toJSONString());

        return Collections.emptyMap();
    }

    /**
     * 外送机构-查询外送项目
     */
    @PostMapping("/select-all")
    public Object itemSendPriceList(@RequestParam long hspOrgId) {
        final List<ItemSendPriceDto> itemSendPriceDtos = itemSendPriceService.selectByHspOrgId(hspOrgId);

        // 对应检验项目
        final Set<Long> testItemIds =
            itemSendPriceDtos.stream().map(ItemSendPriceDto::getTestItemId).collect(Collectors.toSet());
        final List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(testItemIds);
        final Map<Long, TestItemDto> testItemDByTestItemId =
            testItemDtos.stream().collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));

        final List<SelectItemSendPriceAllResponseVo> targetList =
            Lists.newArrayListWithCapacity(itemSendPriceDtos.size());

        for (ItemSendPriceDto dto : itemSendPriceDtos) {

            TestItemDto testItemDto = testItemDByTestItemId.get(dto.getTestItemId());

            if (Objects.isNull(testItemDto)) {
                // 对应检验项目不存在 不展示？？？
                continue;
            }

            SelectItemSendPriceAllResponseVo temp = new SelectItemSendPriceAllResponseVo();
            BeanUtils.copyProperties(dto, temp);

            String sendPriceStr = String.valueOf(dto.getSendPrice());

            temp.setTestItemName(testItemDto.getTestItemName());
            temp.setSendPrice(sendPriceStr);
            temp.setFeePrice(testItemDto.getFeePrice());
            temp.setEnable(testItemDto.getEnable());
            targetList.add(temp);
        }

        return targetList;
    }

    /**
     * 外送机构-导出外送项目
     */
    @PostMapping("/export-all")
    public void exportItemSendPriceList(@RequestParam long hspOrgId, HttpServletResponse response) throws IOException {
        HspOrganizationDto hspOrganizationDto = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganizationDto)) {
            throw new IllegalArgumentException("送检机构不存在");
        }

        List<ExportSelectItemSendPriceAllResponseVo> targetList =
                JSON.parseArray(JSON.toJSONString(this.itemSendPriceList(hspOrgId)), ExportSelectItemSendPriceAllResponseVo.class);

        for (int i = 0; i < targetList.size(); i++) {
            targetList.get(i).setNum(i + 1);
        }

        try (InputStream template = ResourceUtil.getStream("classpath:template/外送价格维护.xlsx")) {
            response.setHeader("Content-disposition", "attachment; filename=" +
                    URLEncoder.encode("外送价格维护-" + hspOrganizationDto.getHspOrgName() + "外送机构.xlsx", StandardCharsets.UTF_8));
            ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream())
                    .withTemplate(template)
                    .excelType(ExcelTypeEnum.XLSX)
                    .build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();

            WriteSheet sheet1 = EasyExcelFactory.writerSheet(0).build();
            // 要遍历的表格数据
            excelWriter.fill(targetList, fillConfig, sheet1);
            excelWriter.finish();
        }
    }

}
