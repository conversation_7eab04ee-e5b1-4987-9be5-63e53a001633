package com.labway.lims.base.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.ItemPriceBasePackageDetailService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.vo.BasePackageContrastTestItemVo;
import com.labway.lims.base.vo.ItemPriceBasePackageDetailContrastVo;
import com.labway.lims.base.vo.UpdateItemPriceBasePackageDetailRequestVo;
import com.labway.lims.base.vo.excel.ItemPriceBasePackageDetailSheet;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.api.ConditionCheckUtils.isDecimalWithTwoDigits;

@Getter
@Setter
@Slf4j
@RestController
@RequestMapping("/item-price-base-package-detail")
public class ItemPriceBasePackageDetailController extends BaseController {

    @Resource
    private ItemPriceBasePackageDetailService itemPriceBasePackageDetailService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private ItemPriceBasePackageService itemPriceBasePackageService;
    @Resource
    private TestItemService testItemService;
    @DubboReference
    private SnowflakeService snowflakeService;

    /**
     * 基准包描述
     */
    @GetMapping("/package-details")
    public Object packageDetails(@RequestParam(required = false) Long packageId) {
        if (Objects.isNull(packageId)) {
            return Collections.emptyList();
        }
        List<ItemPriceBasePackageDetailContrastDto> itemPriceBasePackageDetailContrasts =
            itemPriceBasePackageDetailService.packageDetails(packageId);

        return JSON.parseArray(JSON.toJSONString(itemPriceBasePackageDetailContrasts),
            ItemPriceBasePackageDetailContrastVo.class);
    }

    /**
     * 根据id删除
     */
    @PostMapping("/delete")
    public Object delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("请选择需要删除的检验项目");
        }
        final List<ItemPriceBasePackageDetailDto> details = itemPriceBasePackageDetailService.selectByDetailIds(ids);

        itemPriceBasePackageDetailService.deleteByIds(ids);

        Set<Long> packageIds =
            details.stream().map(ItemPriceBasePackageDetailDto::getPackageId).collect(Collectors.toSet());
        Map<Long, String> packageNameById = itemPriceBasePackageService.selectByIds(packageIds).stream()
            .collect(Collectors.toMap(ItemPriceBasePackageDto::getPackageId, ItemPriceBasePackageDto::getPackageName));
        Set<Long> testItemIds =
            details.stream().map(ItemPriceBasePackageDetailDto::getTestItemId).collect(Collectors.toSet());
        Map<Long, String> testItemNameById = testItemService.selectByTestItemIds(testItemIds).stream()
            .collect(Collectors.toMap(TestItemDto::getTestItemId, TestItemDto::getTestItemName));
        details.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.ITEM_PRICE_BASE_PACKAGE.getDesc())
                    .setContent(String.format("删除项目价格基准包 [%s] 下项目 [%s]",
                        packageNameById.getOrDefault(item.getPackageId(), item.getPackageId().toString()),
                        testItemNameById.getOrDefault(item.getTestItemId(), item.getTestItemCode())))
                    .toJSONString());
        });

        return Collections.emptyMap();
    }

    /**
     * 基准包-检验项目对照
     */
    @PostMapping("/base-package-contrast-test-item")
    public Object basePackageContrastTestItem(@RequestBody BasePackageContrastTestItemVo vo) {
        final Long packageId = vo.getPackageId();
        if (Objects.isNull(packageId)) {
            throw new IllegalArgumentException("请选择基准包");
        }
        if (CollectionUtils.isEmpty(vo.getItemList())) {
            throw new IllegalArgumentException("请选择项目");
        }
        if (vo.getItemList().stream().anyMatch(obj -> Objects.isNull(obj.getTestItemId())
            || Objects.isNull(obj.getFeePrice()) || !isDecimalWithTwoDigits(obj.getFeePrice()))) {
            throw new IllegalArgumentException("项目收费价格不可为空,并且只允许填写数字且最多允许两位小数");
        }

        // key: 检验项目id value: 收费价格
        Map<Long,
            BigDecimal> feePriceByTestItemId = vo.getItemList().stream()
                .collect(Collectors.toMap(BasePackageContrastTestItemVo.ContrastTestItemVo::getTestItemId,
                    obj -> new BigDecimal(obj.getFeePrice()).setScale(2, RoundingMode.HALF_UP), (key1, key2) -> key1));

        ItemPriceBasePackageDto priceBasePackageDto = itemPriceBasePackageService.selectById(packageId);
        if (Objects.isNull(priceBasePackageDto)) {
            throw new IllegalStateException("基准包不存在");
        }

        // 现有检验项目
        Set<Long> testItemIds = itemPriceBasePackageDetailService.selectByPackageId(packageId).stream()
            .map(ItemPriceBasePackageDetailDto::getTestItemId).collect(Collectors.toSet());

        if (feePriceByTestItemId.keySet().stream().anyMatch(testItemIds::contains)) {
            throw new IllegalStateException("部分项目已存在,不可重复添加");
        }

        // 新增的 项目
        LoginUserHandler.User user = LoginUserHandler.get();
        Date now = new Date();
        LinkedList<Long> genIds = snowflakeService.genIds(feePriceByTestItemId.keySet().size());
        Map<Long, String> testItemNameById = new HashMap<>();
        List<ItemPriceBasePackageDetailDto> list =
            testItemService.selectByTestItemIds(feePriceByTestItemId.keySet()).stream().map(m -> {
                ItemPriceBasePackageDetailDto obj = new ItemPriceBasePackageDetailDto();
                obj.setDetailId(genIds.pop());
                obj.setPackageId(packageId);
                obj.setTestItemId(m.getTestItemId());
                obj.setTestItemCode(m.getTestItemCode());
                obj.setOrgId(user.getOrgId());
                obj.setOrgName(user.getOrgName());
                obj.setCreateDate(now);
                obj.setUpdateDate(now);
                obj.setUpdaterId(user.getUserId());
                obj.setUpdaterName(user.getNickname());
                obj.setCreatorId(user.getUserId());
                obj.setCreatorName(user.getNickname());
                obj.setIsDelete(YesOrNoEnum.NO.getCode());
                obj.setFeePrice(feePriceByTestItemId.get(m.getTestItemId()));
                testItemNameById.put(m.getTestItemId(), m.getTestItemName());
                return obj;
            }).collect(Collectors.toList());
        itemPriceBasePackageDetailService.addItemPriceBasePackageDetails(list);

        list.forEach(item -> {
            rabbitMQService
                .convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.ITEM_PRICE_BASE_PACKAGE.getDesc())
                        .setContent(String.format("基准包 [%s] 新增项目 [%s]", priceBasePackageDto.getPackageName(),
                            testItemNameById.getOrDefault(item.getTestItemId(), item.getTestItemCode())))
                        .toJSONString());
        });

        return Collections.emptyMap();
    }

    /**
     * 基准包-检验项目对照列表
     */
    @Deprecated
    @GetMapping("/select-item-price-base-package-detail-contrast-list")
    public Object selectItemPriceBasePackageDetailContrastList(@RequestParam(required = false) Integer filterType,
        @RequestParam(required = false) Long packageId) {

        if (Objects.isNull(filterType) || Objects.isNull(packageId)) {
            return Collections.emptyList();
        }

        List<ItemPriceBasePackageDetailContrastDto> items =
            itemPriceBasePackageDetailService.selectItemPriceBasePackageDetailContrastList(filterType, packageId);

        return JSON.parseArray(JSON.toJSONString(items), ItemPriceBasePackageDetailContrastVo.class);
    }

    /**
     * 基准包-检验项目修改信息
     */
    @PostMapping("/update")
    public Object updateItemPriceBasePackageDetail(@RequestBody UpdateItemPriceBasePackageDetailRequestVo vo) {
        if (Objects.isNull(vo.getDetailId())) {
            throw new IllegalArgumentException("请选择项目");
        }
        if (!isDecimalWithTwoDigits(vo.getPrice())) {
            throw new IllegalArgumentException("项目收费价格不可为空,并且只允许填写数字且最多允许两位小数");
        }

        ItemPriceBasePackageDetailDto detailDtoOld =
            itemPriceBasePackageDetailService.selectByDetailId(vo.getDetailId());
        if (Objects.isNull(detailDtoOld)) {
            throw new IllegalArgumentException("项目不存在");
        }

        ItemPriceBasePackageDto priceBasePackageDto =
            itemPriceBasePackageService.selectById(detailDtoOld.getPackageId());
        if (Objects.isNull(priceBasePackageDto)) {
            throw new IllegalArgumentException("对应基准包不存在");
        }

        TestItemDto testItemDto = testItemService.selectByTestItemId(detailDtoOld.getTestItemId());
        if (Objects.isNull(testItemDto)) {
            throw new IllegalArgumentException("对应检验项目不存在");
        }

        ItemPriceBasePackageDetailDto update = new ItemPriceBasePackageDetailDto();
        update.setDetailId(vo.getDetailId());
        update.setFeePrice(new BigDecimal(vo.getPrice()).setScale(2, RoundingMode.HALF_UP));
        itemPriceBasePackageDetailService.updateByDetailId(update);

        String compare = new CompareUtils<ItemPriceBasePackageDetailDto>().compare(detailDtoOld, update);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.ITEM_PRICE_BASE_PACKAGE.getDesc())
                    .setContent(String.format("修改[%s]基准包下[%s]项目: [%s]", priceBasePackageDto.getPackageName(),
                        testItemDto.getTestItemName(), compare))
                    .toJSONString());
        }

        return Collections.emptyMap();
    }

    /**
     * 样本信息导入-导入上传文件
     */
    @PostMapping("/import")
    public Object importFile(@RequestParam Long packageId, @RequestParam("file") MultipartFile file) {
        if (Objects.isNull(packageId)) {
            throw new IllegalArgumentException("基准包不能为空");
        }

        if (Objects.isNull(file) || file.isEmpty()) {
            throw new IllegalStateException("文件信息为空");
        }

        String fileName = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(fileName);
        if (!"xls".equalsIgnoreCase(extension) && !"xlsx".equalsIgnoreCase(extension)) {
            throw new LimsException("只允许上传 xls 和 xlsx 格式的文件");
        }

        if (!Objects.requireNonNull(file.getOriginalFilename(), "文件名为空").endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            throw new IllegalStateException("文件格式不正确");
        }

        // 读取表格中导入的内容
        final List<ItemPriceBasePackageDetailSheetDto> importDetails = readExcelData(file);

        int count = itemPriceBasePackageDetailService.importItemPriceBasePackageDetail(packageId, importDetails);

        return Map.of("importCount", count);
    }

    /**
     * 下载模板
     */
    @PostMapping("/download-template")
    public Object downloadTemplate() {
        // 先直接取 resources 下 模版
        ClassPathResource resource = new ClassPathResource("template/项目价格基准包导入模板.xlsx");

        if (resource.exists()) {
            try {
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_DISPOSITION,
                                String.format("attachment; filename=%s",
                                        URLEncoder.encode("项目价格基准包导入模板.xlsx", StandardCharsets.UTF_8)))
                        .contentType(MediaType.APPLICATION_OCTET_STREAM)
                        .contentType(new MediaType("application", "vnd.ms-excel"))
                        .body(resource.getInputStream().readAllBytes());
            } catch (Exception e) {
                log.error("导出资源下模版失败", e);
            }
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        // -------定义些简单excel样式----------------
        // 头的策略
        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        // 内容策略
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("微软雅黑");
        headWriteFont.setFontHeightInPoints((short) 13);
        headCellStyle.setWriteFont(headWriteFont);
        HorizontalCellStyleStrategy excelStyle = new HorizontalCellStyleStrategy(headCellStyle, (List<WriteCellStyle>) null);
        // 设置列宽度
        AbstractColumnWidthStyleStrategy columnWidthStyleStrategy = new AbstractColumnWidthStyleStrategy() {
            @Override
            protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head,
                                          Integer integer, Boolean isHead) {
                if (Boolean.TRUE.equals(isHead)) {
                    int columnWidth = cell.getStringCellValue().length();
                    columnWidth = Math.max(columnWidth * 5, 20);
                    if (columnWidth > 255) {
                        columnWidth = 255;
                    }
                    writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
                }
            }
        };
        try (ExcelWriter excelWriter = EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(excelStyle).registerWriteHandler(columnWidthStyleStrategy).build()) {
            List<List<Object>> list0 = Collections.emptyList();
            List<List<String>> headers = List.of(List.of("检验项目编码"), List.of("检验项目名称"), List.of("收费价格"));

            // 获取sheet对象
            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "项目价格基准包").head(headers)
                    .needHead(Boolean.TRUE).build();

            excelWriter.write(list0, sheet0);
        } catch (Exception e) {
            log.error("下载模板错误", e);
            throw new IllegalStateException(e.getMessage(), e);
        }
        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("项目价格基准包导入模板.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(data);
    }

    public List<ItemPriceBasePackageDetailSheetDto> readExcelData(MultipartFile file) {
        try {
            final ExcelReader build = EasyExcelFactory.read(file.getInputStream()).autoCloseStream(true).build();

            Map<String, Integer> itemCodeIndexMap = new HashMap<>();
            List<String> codeErrorMessages = new ArrayList<>();
            List<String> priceErrorMessages = new ArrayList<>();
            final List<ItemPriceBasePackageDetailSheetDto> details = new ArrayList<>();
            ReadSheet sheet0 = EasyExcel.readSheet(0)
                    .head(ItemPriceBasePackageDetailSheet.class)
                    .registerReadListener(new AnalysisEventListener<ItemPriceBasePackageDetailSheet>() {
                        private Integer index = 1;
                        @Override
                        public void invoke(ItemPriceBasePackageDetailSheet data, AnalysisContext context) {
                            index++;
                            ItemPriceBasePackageDetailSheetDto detailSheetDto = new ItemPriceBasePackageDetailSheetDto();

                            // 判断项目编码是否重复
                            String testItemCode = data.getTestItemCode();
                            if (Objects.nonNull(itemCodeIndexMap.get(testItemCode))) {
                                codeErrorMessages.add(String.format("第【%s】行与第【%s】行，项目编码【%s】重复", itemCodeIndexMap.get(testItemCode), index, testItemCode));
                            }
                            BigDecimal feePrice = null;
                            if (StringUtils.isBlank(data.getFeePrice())) {
                                priceErrorMessages.add(String.format("第【%s】行，收费价格错误【收费价格为空】", index));
                            } else {
                                // 判断费用是否正确
                                feePrice = feePriceConvert(data.getFeePrice());
                                if (Objects.isNull(feePrice)) {
                                    priceErrorMessages.add(String.format("第【%s】行，收费价格错误【%s】", index, data.getFeePrice()));
                                }
                            }

                            detailSheetDto.setIndex(index);
                            detailSheetDto.setTestItemCode(data.getTestItemCode());
                            detailSheetDto.setTestItemName(data.getTestItemName());
                            detailSheetDto.setFeePrice(feePrice);
                            details.add(detailSheetDto);
                            itemCodeIndexMap.put(testItemCode, index);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                        }
                    }).build();
            build.read(sheet0);
            build.finish();

            // 查询出来所有的检验项目
            Set<String> testItemCodes = details.stream().map(ItemPriceBasePackageDetailSheetDto::getTestItemCode).collect(Collectors.toSet());
            List<TestItemDto> testItemDtos = testItemService.selectByTestItemCodes(testItemCodes, LoginUserHandler.get().getOrgId());
            Map<String, TestItemDto> itemCodeMap =
                    testItemDtos.stream()
                            .collect(Collectors.toMap(TestItemDto::getTestItemCode, Function.identity(), (a, b) -> a));

            // 判断检验项目是否存在
            List<String> errorMessages = new ArrayList<>();
            details.forEach(detail -> {
                if (Objects.isNull(itemCodeMap.get(detail.getTestItemCode()))) {
                    errorMessages.add(String.format("第【%s】行，检验项目【%s】不存在", detail.getIndex(), detail.getTestItemName()));
                }
            });
            errorMessages.addAll(codeErrorMessages);
            errorMessages.addAll(priceErrorMessages);

            if (CollectionUtils.isNotEmpty(errorMessages)) {
                throw new IllegalStateException(String.join("<br>", errorMessages));
            }

            return details;
        } catch (IOException e) {
            throw new IllegalStateException("读取文件异常");
        }
    }

    private BigDecimal feePriceConvert(String feePrice) {
        try {
            return new BigDecimal(feePrice).setScale(2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            return null;
        }
    }
}
