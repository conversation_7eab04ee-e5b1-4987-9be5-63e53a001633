package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.BarcodeSettingDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.enums.BarcodeSettingEnum;
import com.labway.lims.base.api.service.BarcodeSettingService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.mapper.BarcodeSettingMapper;
import com.labway.lims.base.model.TbBarcodeSetting;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

@DubboService
@Slf4j
@CacheConfig(cacheNames = "barcode-setting")
public class BarcodeSettingServiceImpl extends ServiceImpl<BarcodeSettingMapper, TbBarcodeSetting>
        implements BarcodeSettingService {

    @Resource
    private BarcodeSettingMapper barcodeSettingMapper;

    @Resource
    private BarcodeSettingService barcodeSettingService;

    @Resource
    private HspOrganizationService hspOrganizationService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisPrefix redisPrefix;

    @DubboReference
    private ApplySampleService applySampleService;

    @DubboReference
    private ApplyService applyService;


    @Cacheable(key = "'selectById' + #id")
    @Override
    public BarcodeSettingDto selectById(Serializable id) {
        return convert(baseMapper.selectById(id));
    }

    @Cacheable(key = "'selectAll'")
    @Override
    public List<BarcodeSettingDto> selectAll() {
        return this.list(Wrappers.lambdaQuery(TbBarcodeSetting.class)
                        .orderByDesc(TbBarcodeSetting::getHspOrgCode, TbBarcodeSetting::getBarcodeType))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Cacheable(key = "'selectAllBarCode'")
    @Override
    public List<BarcodeSettingDto> selectAllBarCode() {
        return barcodeSettingService.selectAll().stream()
                .filter(e -> Objects.equals(BarcodeSettingDto.BARCODE_TYPE, e.getBarcodeType()))
                .collect(Collectors.toList());
    }

    @Cacheable(key = "'selectAllMasterBarCode'")
    @Override
    public List<BarcodeSettingDto> selectAllMasterBarCode() {
        return barcodeSettingService.selectAll().stream()
                .filter(e -> Objects.equals(BarcodeSettingDto.MASTER_BARCODE_TYPE, e.getBarcodeType()))
                .collect(Collectors.toList());
    }

    @Override
    public List<BarcodeSettingDto> selectByHspOrgCodeAndBarcodeType(String hspOrgCode, int barcodeType) {
        List<BarcodeSettingDto> barcodeSettingDtos = new ArrayList<>();
        // 判断是条码规则还是主条码规则
        if (barcodeType == BarcodeSettingDto.BARCODE_TYPE) {
            barcodeSettingDtos = barcodeSettingService.selectAllBarCode();
        } else if (barcodeType == BarcodeSettingDto.MASTER_BARCODE_TYPE) {
            barcodeSettingDtos = barcodeSettingService.selectAllMasterBarCode();
        }


        return StringUtils.isBlank(hspOrgCode) ?
                barcodeSettingDtos :

                barcodeSettingDtos.stream()
                        .filter(e -> Objects.equals(e.getHspOrgCode(), hspOrgCode))
                        .collect(Collectors.toList());

    }


    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    @Override
    public boolean addBarcodeSetting(BarcodeSettingDto dto) {
        final String key = redisPrefix.getBasePrefix() + "labway-lims-base-server:barcode-setting:add:" + dto.getBarcodeType() + ":" + dto.getHspOrgCode();

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key,
                StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalArgumentException("条码规则新增繁忙，请稍后再试");
        }

        try {
            List<BarcodeSettingDto> barcodeSettingDtos = barcodeSettingService.selectAll();

            // 判断 送检机构+条码规则类型 是否重复
            BarcodeSettingDto barcodeSettingDto = barcodeSettingDtos.stream()
                    .filter(e -> Objects.equals(e.getBarcodeType(), dto.getBarcodeType())
                            && Objects.equals(e.getHspOrgCode(), dto.getHspOrgCode()))
                    .findFirst().orElse(null);

            if (Objects.nonNull(barcodeSettingDto)) {
                log.error("{} 已有启用的条码规则", barcodeSettingDto.getHspOrgName());
                throw new IllegalArgumentException(String.format("%s 已有启用的条码规则", barcodeSettingDto.getHspOrgName()));
            }

            // 判断起始条码重复
            barcodeSettingDto = barcodeSettingDtos.stream()
                    .filter(e -> Objects.equals(e.getStartCode(), dto.getStartCode()))
                    .findFirst().orElse(null);

            if (Objects.nonNull(barcodeSettingDto)) {
                throw new IllegalArgumentException("条码规则起始编码重复");
            }

            LoginUserHandler.User user = LoginUserHandler.get();
            LocalDateTime now = LocalDateTime.now();
            TbBarcodeSetting tbBarcodeSetting = JSON.parseObject(JSON.toJSONString(dto), TbBarcodeSetting.class);
            tbBarcodeSetting.setCreaterId(user.getUserId());
            tbBarcodeSetting.setCreaterName(user.getNickname());
            tbBarcodeSetting.setUpdaterId(user.getUserId());
            tbBarcodeSetting.setUpdaterName(user.getNickname());
            tbBarcodeSetting.setCreaterDate(now);
            tbBarcodeSetting.setUpdaterDate(now);
            tbBarcodeSetting.setCurrentNumber(NumberUtils.LONG_ZERO);
            tbBarcodeSetting.setIsDelete(NumberUtils.INTEGER_ZERO);

            return barcodeSettingMapper.insert(tbBarcodeSetting) > 0;
        } finally {
            stringRedisTemplate.delete(key);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    @Override
    public boolean updateBarcodeSetting(BarcodeSettingDto dto) {
        // 如果是改成启用
        if (BooleanUtils.isTrue(dto.getEnable())) {
            BarcodeSettingDto barcodeSettingDto = barcodeSettingService.selectById(dto.getBarcodeSettingId());
            // 查询送检机构下所有配置是否有启用的
            List<BarcodeSettingDto> barcodeSettingDtos = barcodeSettingService.selectByHspOrgCodeAndBarcodeType(barcodeSettingDto.getHspOrgCode(), barcodeSettingDto.getBarcodeType())
                    .stream()
                    .filter(e -> !Objects.equals(barcodeSettingDto.getBarcodeSettingId(), e.getBarcodeSettingId()))
                    .filter(e -> BooleanUtils.isTrue(e.getEnable()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(barcodeSettingDtos)) {
                throw new IllegalArgumentException(String.format("%s 已有启用的条码规则", barcodeSettingDto.getHspOrgName()));
            }
        }

        LoginUserHandler.User user = LoginUserHandler.get();
        LambdaUpdateWrapper<TbBarcodeSetting> wrapper = Wrappers.lambdaUpdate(TbBarcodeSetting.class)
                .eq(TbBarcodeSetting::getBarcodeSettingId, dto.getBarcodeSettingId())
                .set(TbBarcodeSetting::getBarcodePlace, dto.getBarcodePlace())
                .set(TbBarcodeSetting::getEnable, dto.getEnable())
                .set(TbBarcodeSetting::getUpdaterId, user.getUserId())
                .set(TbBarcodeSetting::getUpdaterName, user.getNickname())
                .set(TbBarcodeSetting::getUpdaterDate, LocalDate.now());
        return barcodeSettingMapper.update(null, wrapper) > 0;
    }

    @Override
    public List<String> genMasterBarcodes(String hspOrgCode, int number) {
        return barcodeSettingService.genBarcodes(hspOrgCode, number, BarcodeSettingDto.MASTER_BARCODE_TYPE);
    }

    @Transactional(rollbackFor = Exception.class, propagation = REQUIRES_NEW)
    @CacheEvict(allEntries = true)
    @Override
    public List<String> genBarcodes(String hspOrgCode, int number, int barcodeType) {
        if (number <= NumberUtils.INTEGER_ZERO) {
            return Collections.emptyList();
        }
        final String key = String.format(BarcodeSettingEnum.SAVE_BARCODE_SETTING.getRedisKey(), redisPrefix.getBasePrefix(), barcodeType, hspOrgCode);

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key,
                StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalArgumentException("条码号生成繁忙，请稍后再试");
        }
        try {
            // 条码规则
            BarcodeSettingDto barcodeSettingDto = barcodeSettingService.selectByHspOrgCodeAndBarcodeType(hspOrgCode, barcodeType).stream()
                    .filter(e -> Objects.equals(e.getHspOrgCode(), hspOrgCode))
                    .findFirst().orElse(null);

            if (Objects.isNull(barcodeSettingDto)) {
                // 送检机构信息
                HspOrganizationDto hspOrganizationDto = hspOrganizationService.selectByHspOrgCode(hspOrgCode);
                String hspOrgName = Objects.nonNull(hspOrganizationDto) ? hspOrganizationDto.getHspOrgName() : Strings.EMPTY;

                log.error("{}条码规则没有维护", hspOrgName);
                return Collections.emptyList();
            }

            if (!Boolean.TRUE.equals(barcodeSettingDto.getEnable())) {
                log.error("{}条码规则没有启用", barcodeSettingDto.getHspOrgName());
                return Collections.emptyList();
            }

            List<String> barcodes = new LinkedList<>();

            // 主条码 和 条码  各自匹配规则
            long currentNumber;
            if (Objects.equals(barcodeType, BarcodeSettingDto.BARCODE_TYPE)) {
                // 条码规则
                currentNumber = currentBarcodeNumber(number, barcodeSettingDto, barcodes);
            } else if (Objects.equals(barcodeType, BarcodeSettingDto.MASTER_BARCODE_TYPE)) {
                // 主条码规则
                currentNumber = currentMaterBarcodeNumber(number, barcodeSettingDto, barcodes);
            } else {
                throw new IllegalArgumentException("条码类型不能为空且 条码【1】  主条码【2】");
            }

            LambdaUpdateWrapper<TbBarcodeSetting> wrapper = Wrappers.lambdaUpdate(TbBarcodeSetting.class)
                    .eq(TbBarcodeSetting::getBarcodeSettingId, barcodeSettingDto.getBarcodeSettingId())
                    .set(TbBarcodeSetting::getCurrentNumber, currentNumber);
            barcodeSettingMapper.update(null, wrapper);

            log.info("{} 批量生成条码{}个：{}", barcodeSettingDto.getHspOrgName(), number, barcodes);

            final String barcodeKey = String.format(BarcodeSettingEnum.BARCODE_CACHE.getRedisKey(), redisPrefix.getBasePrefix(), barcodeType, hspOrgCode);

            stringRedisTemplate.opsForSet().add(barcodeKey, barcodes.toArray(new String[ 0 ]));

            return barcodes;
        } finally {
            stringRedisTemplate.delete(key);
        }
    }

    /**
     * 条码生成
     */
    private long currentBarcodeNumber(int number, BarcodeSettingDto barcodeSettingDto, List<String> barcodes) {
        int numberLength = barcodeSettingDto.getBarcodePlace() - barcodeSettingDto.getStartCode().length();

        // 现在生成的数量游标
        long cursorCurrentNumber = barcodeSettingDto.getCurrentNumber();

        // 最大生成到的数量 = 库里已生成数量 + 将要生成的数量
        long currentNumber = cursorCurrentNumber + number;

        do {
            // 先生成指定的数量
            for (long i = cursorCurrentNumber; i < currentNumber; i++) {
                String barcode = barcodeSettingDto.getStartCode() + String.format("%0" + numberLength + "d", (i + 1));
                barcodes.add(barcode);
            }

            // 查看库中是否存在
            List<ApplySampleDto> applySampleDtos = applySampleService.selectByBarcodes(barcodes);
            if (CollectionUtils.isEmpty(applySampleDtos)) {
                // 不存在直接返回
                break;
            }
            // 删除存在的条码
            barcodes.removeAll(applySampleDtos.stream().map(ApplySampleDto::getBarcode).collect(Collectors.toList()));

            // 更新游标
            cursorCurrentNumber = currentNumber;

            // 更新入库当前最大位数
            currentNumber = currentNumber + applySampleDtos.size();

        } while (true);
        return currentNumber;
    }

    /**
     * 主条码生成
     */
    private long currentMaterBarcodeNumber(int number, BarcodeSettingDto barcodeSettingDto, List<String> barcodes) {
        int numberLength = barcodeSettingDto.getBarcodePlace() - barcodeSettingDto.getStartCode().length();

        // 现在生成的数量游标
        long cursorCurrentNumber = barcodeSettingDto.getCurrentNumber();

        // 最大生成到的数量 = 库里已生成数量 + 将要生成的数量
        long currentNumber = cursorCurrentNumber + number;

        do {
            // 先生成指定的数量
            for (long i = cursorCurrentNumber; i < currentNumber; i++) {
                String barcode = barcodeSettingDto.getStartCode() + String.format("%0" + numberLength + "d", (i + 1));
                barcodes.add(barcode);
            }

            // 查看库中是否存在
            List<ApplyDto> applyDtos = applyService.selectByMasterBarcodes(new HashSet<>(barcodes));
            if (CollectionUtils.isEmpty(applyDtos)) {
                // 不存在直接返回
                break;
            }
            // 删除存在的条码
            barcodes.removeAll(applyDtos.stream().map(ApplyDto::getMasterBarcode).collect(Collectors.toList()));

            // 更新游标
            cursorCurrentNumber = currentNumber;

            // 更新入库当前最大位数
            currentNumber = currentNumber + applyDtos.size();

        } while (true);
        return currentNumber;
    }


    @Override
    public BarcodeSettingDto selectByBarcode(String barcode, int barcodeType) {
        BarcodeSettingDto barcodeSettingDto = barcodeSettingService.selectByHspOrgCodeAndBarcodeType(null, barcodeType)
                .stream()
                .filter(e -> BooleanUtils.isTrue(e.getEnable()) && barcode.startsWith(e.getStartCode()))
                .max(Comparator.comparing(e -> e.getStartCode().length()))
                .orElse(null);
        if (Objects.isNull(barcodeSettingDto)) {
            throw new IllegalStateException("该机构未配置或未启用");
        }

        // 1.1.3.8【预制条码信息录入】扫码时校验条码号是否存在 https://www.tapd.cn/59091617/prong/stories/view/1159091617001001974
        final String barcodeKey = String.format(BarcodeSettingEnum.BARCODE_CACHE.getRedisKey(), redisPrefix.getBasePrefix(), BarcodeSettingDto.BARCODE_TYPE, barcodeSettingDto.getHspOrgCode());
        Boolean member = stringRedisTemplate.opsForSet().isMember(barcodeKey, barcode);
        if (BooleanUtils.isFalse(member)) {
            throw new IllegalArgumentException(String.format("[%s] 不是预制条码", barcode));
        }
        if (applySampleService.existsByBarcode(barcode)) {
            throw new IllegalArgumentException(String.format("[%s] 条码号已存在，不能重复录入", barcode));
        }
        return barcodeSettingDto;
    }


    private BarcodeSettingDto convert(TbBarcodeSetting tbBarcodeSetting) {
        return JSON.parseObject(JSON.toJSONString(tbBarcodeSetting), BarcodeSettingDto.class);
    }

}
