package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.RefrigeratorDto;
import com.labway.lims.base.api.dto.RefrigeratorGroupDto;
import com.labway.lims.base.api.service.RefrigeratorGroupService;
import com.labway.lims.base.api.service.RefrigeratorService;
import com.labway.lims.base.mapper.TbRefrigeratorMapper;
import com.labway.lims.base.model.TbRefrigerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 冰箱 Service impl
 * 
 * <AUTHOR>
 * @since 2023/4/3 11:00
 */
@Slf4j
@DubboService
public class RefrigeratorServiceImpl implements RefrigeratorService {

    @Resource
    private TbRefrigeratorMapper tbRefrigeratorMapper;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RefrigeratorGroupService refrigeratorGroupService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addRefrigerator(RefrigeratorDto refrigeratorDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbRefrigerator target = new TbRefrigerator();
        target.setRefrigeratorCode(refrigeratorDto.getRefrigeratorCode());
        target.setRefrigeratorName(refrigeratorDto.getRefrigeratorName());
        target.setArchiveStoreName(refrigeratorDto.getArchiveStoreName());
        target.setArchiveStoreId(refrigeratorDto.getArchiveStoreId());
        target.setPosition(refrigeratorDto.getPosition());
        target.setTemperature(refrigeratorDto.getTemperature());
        target.setEnable(refrigeratorDto.getEnable());

        target.setRefrigeratorId(snowflakeService.genId());
        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbRefrigeratorMapper.insert(target) < 1) {
            throw new LimsException("添加冰箱失败");
        }
        log.info("用户 [{}] 新增冰箱[{}]成功", loginUser.getNickname(), JSON.toJSONString(target));

        return target.getRefrigeratorId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addRefrigerator(RefrigeratorDto refrigeratorDto, List<ProfessionalGroupDto> professionalGroupDtos) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        LinkedList<Long> genIds = snowflakeService.genIds(professionalGroupDtos.size() + 1);

        TbRefrigerator target = new TbRefrigerator();
        target.setRefrigeratorCode(refrigeratorDto.getRefrigeratorCode());
        target.setRefrigeratorName(refrigeratorDto.getRefrigeratorName());
        target.setArchiveStoreName(refrigeratorDto.getArchiveStoreName());
        target.setArchiveStoreId(refrigeratorDto.getArchiveStoreId());
        target.setPosition(refrigeratorDto.getPosition());
        target.setTemperature(refrigeratorDto.getTemperature());
        target.setEnable(refrigeratorDto.getEnable());

        target.setRefrigeratorId(genIds.pop());
        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbRefrigeratorMapper.insert(target) < 1) {
            throw new LimsException("添加冰箱失败");
        }
        log.info("用户 [{}] 新增冰箱[{}]成功", loginUser.getNickname(), JSON.toJSONString(target));

        // 创建 冰箱专业组关联 信息
        List<RefrigeratorGroupDto> refrigeratorGroupDtoList =
            Lists.newArrayListWithCapacity(professionalGroupDtos.size());
        Date date = new Date();
        for (ProfessionalGroupDto groupDto : professionalGroupDtos) {
            RefrigeratorGroupDto temp = new RefrigeratorGroupDto();
            temp.setRefrigeratorId(target.getRefrigeratorId());
            temp.setRefrigeratorCode(target.getRefrigeratorCode());
            temp.setGroupId(groupDto.getGroupId());
            temp.setGroupCode(groupDto.getGroupCode());

            temp.setRelationId(genIds.pop());
            temp.setCreateDate(date);
            temp.setUpdateDate(date);
            temp.setCreatorId(loginUser.getUserId());
            temp.setCreatorName(loginUser.getNickname());
            temp.setUpdaterId(loginUser.getUserId());
            temp.setUpdaterName(loginUser.getNickname());
            temp.setIsDelete(YesOrNoEnum.NO.getCode());

            refrigeratorGroupDtoList.add(temp);
        }

        refrigeratorGroupService.addRefrigeratorGroups(refrigeratorGroupDtoList);

        return target.getRefrigeratorId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByRefrigeratorIds(Collection<Long> refrigeratorIds) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除冰箱成功 [{}] 结果 [{}]", loginUser.getNickname(), refrigeratorIds,
            tbRefrigeratorMapper.deleteBatchIds(refrigeratorIds) > 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByRefrigeratorId(RefrigeratorDto refrigeratorDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbRefrigerator target = new TbRefrigerator();
        BeanUtils.copyProperties(refrigeratorDto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbRefrigeratorMapper.updateById(target) < 1) {
            throw new LimsException("修改冰箱失败");
        }

        log.info("用户 [{}] 修改冰箱成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    public List<RefrigeratorDto> selectByOrgId(long orgId) {
        if (orgId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRefrigerator> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbRefrigerator::getOrgId, orgId);
        queryWrapper.eq(TbRefrigerator::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbRefrigerator::getCreateDate);
        return convert(tbRefrigeratorMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public RefrigeratorDto selectByRefrigeratorId(long refrigeratorId) {
        if (refrigeratorId < 1) {
            return null;
        }
        LambdaQueryWrapper<TbRefrigerator> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbRefrigerator::getRefrigeratorId, refrigeratorId);
        queryWrapper.eq(TbRefrigerator::getIsDelete, YesOrNoEnum.NO.getCode());

        return convert(tbRefrigeratorMapper.selectOne(queryWrapper.last("limit 1")));
    }

    @Nullable
    @Override
    public RefrigeratorDto selectByRefrigeratorName(String refrigeratorName, long orgId) {
        if (StringUtils.isBlank(refrigeratorName)) {
            return null;
        }
        LambdaQueryWrapper<TbRefrigerator> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbRefrigerator::getRefrigeratorName, refrigeratorName);
        queryWrapper.eq(TbRefrigerator::getOrgId, orgId);
        queryWrapper.eq(TbRefrigerator::getIsDelete, YesOrNoEnum.NO.getCode()).last("limit 1");

        return convert(tbRefrigeratorMapper.selectOne(queryWrapper));
    }

    @Nullable
    @Override
    public RefrigeratorDto selectByRefrigeratorCode(String refrigeratorCode, long orgId) {
        if (StringUtils.isBlank(refrigeratorCode)) {
            return null;
        }
        LambdaQueryWrapper<TbRefrigerator> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbRefrigerator::getRefrigeratorCode, refrigeratorCode);
        queryWrapper.eq(TbRefrigerator::getOrgId, orgId);
        queryWrapper.eq(TbRefrigerator::getIsDelete, YesOrNoEnum.NO.getCode()).last("limit 1");

        return convert(tbRefrigeratorMapper.selectOne(queryWrapper));
    }

    @Override
    public List<RefrigeratorDto> selectByArchiveStoreId(long archiveStoreId) {
        if (archiveStoreId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRefrigerator> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbRefrigerator::getArchiveStoreId, archiveStoreId);
        queryWrapper.eq(TbRefrigerator::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbRefrigerator::getCreateDate);
        return convert(tbRefrigeratorMapper.selectList(queryWrapper));
    }

    @Override
    public List<RefrigeratorDto> selectByRefrigeratorIds(Collection<Long> refrigeratorIds) {
        if (CollectionUtils.isEmpty(refrigeratorIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRefrigerator> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbRefrigerator::getRefrigeratorId, refrigeratorIds);
        queryWrapper.eq(TbRefrigerator::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbRefrigerator::getCreateDate);
        return convert(tbRefrigeratorMapper.selectList(queryWrapper));
    }

    @Override
    public boolean checkArchiveStoreUseByIds(Collection<Long> archiveStoreIds) {
        LambdaQueryWrapper<TbRefrigerator> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbRefrigerator::getArchiveStoreId, archiveStoreIds);
        queryWrapper.eq(TbRefrigerator::getIsDelete, YesOrNoEnum.NO.getCode());

        return tbRefrigeratorMapper.selectCount(queryWrapper) > NumberUtils.LONG_ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refrigeratorUpdate(RefrigeratorDto refrigeratorDto, List<ProfessionalGroupDto> needAddRelationGroupList,
        Collection<Long> needDeleteRelationIdList) {

        // 更新
        this.updateByRefrigeratorId(refrigeratorDto);

        LoginUserHandler.User loginUser = LoginUserHandler.get();
        if (CollectionUtils.isNotEmpty(needDeleteRelationIdList)) {
            refrigeratorGroupService.deleteByRelationIds(needDeleteRelationIdList);
        }

        if (CollectionUtils.isEmpty(needAddRelationGroupList)) {
            return;
        }

        // 创建 冰箱专业组关联 信息
        List<RefrigeratorGroupDto> refrigeratorGroupDtoList =
            Lists.newArrayListWithCapacity(needAddRelationGroupList.size());
        Date date = new Date();
        LinkedList<Long> genIds = snowflakeService.genIds(needAddRelationGroupList.size());
        for (ProfessionalGroupDto groupDto : needAddRelationGroupList) {
            RefrigeratorGroupDto temp = new RefrigeratorGroupDto();
            temp.setRefrigeratorId(refrigeratorDto.getRefrigeratorId());
            temp.setRefrigeratorCode(refrigeratorDto.getRefrigeratorCode());
            temp.setGroupId(groupDto.getGroupId());
            temp.setGroupCode(groupDto.getGroupCode());

            temp.setRelationId(genIds.pop());
            temp.setCreateDate(date);
            temp.setUpdateDate(date);
            temp.setCreatorId(loginUser.getUserId());
            temp.setCreatorName(loginUser.getNickname());
            temp.setUpdaterId(loginUser.getUserId());
            temp.setUpdaterName(loginUser.getNickname());
            temp.setIsDelete(YesOrNoEnum.NO.getCode());

            refrigeratorGroupDtoList.add(temp);
        }

        refrigeratorGroupService.addRefrigeratorGroups(refrigeratorGroupDtoList);
    }

    /**
     * TbRefrigerator 转换 为 RefrigeratorDto
     *
     * @param tbRefrigerator TbRefrigerator
     * @return RefrigeratorDto
     */
    private RefrigeratorDto convert(TbRefrigerator tbRefrigerator) {
        if (Objects.isNull(tbRefrigerator)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(tbRefrigerator), RefrigeratorDto.class);
    }

    /**
     * TbRefrigerator 转换 为 RefrigeratorDto
     *
     * @param list TbRefrigerator
     * @return RefrigeratorDto
     */
    private List<RefrigeratorDto> convert(List<TbRefrigerator> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }

}
