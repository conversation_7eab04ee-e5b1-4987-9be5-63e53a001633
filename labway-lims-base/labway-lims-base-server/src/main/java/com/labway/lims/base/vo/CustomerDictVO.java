package com.labway.lims.base.vo;

import lombok.Data;

/**
 * <p>
 * 字典分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-23
 */
@Data
public class CustomerDictVO {

    /**
     * 字典ID
     */
    private Long dictId;

    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 字典名称
     */
    private String dictName;

    /**
     * 编码类型描述
     */
    private String codeTypeDes;

    /**
     * 标准值
     */
    private String standardValue;

    /**
     * 字典内容
     */
    private String dicContent;

    /**
     * 字典内容
     */
    private String dictContent;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态(0 停用,1 启用)
     */
    private Integer status;

    /**
     * 专业组id
     */
    private Long groupId;

    /**
     * 如果字符内容为default那么他就是默认值
     */
    private String isDefaultValue;

    /**
     * 拼音
     */
    private String py;
    /**
     * 快捷键
     */
    private String shortcut;
}