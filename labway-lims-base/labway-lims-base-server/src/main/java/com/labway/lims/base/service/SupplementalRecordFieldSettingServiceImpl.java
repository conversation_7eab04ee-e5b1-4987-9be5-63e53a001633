package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.DefaultHspOrg;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.SaveHspOrganizationFiledDto;
import com.labway.lims.base.api.service.SupplementalRecordFieldSettingService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.mapper.SupplementalRecordFieldSettingMapping;
import com.labway.lims.base.model.SupplementalRecordFieldSetting;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description 补录字段设置服务层
 * <AUTHOR>
 * @date 2024-05-24
 */
@CacheConfig(cacheNames = "supplemental_record_field_setting")
@Slf4j
@DubboService
public class SupplementalRecordFieldSettingServiceImpl extends ServiceImpl<SupplementalRecordFieldSettingMapping, SupplementalRecordFieldSetting> implements SupplementalRecordFieldSettingService {

    @Resource
    private SupplementalRecordFieldSettingService supplementalRecordFieldSettingService;

    @Resource
    private HspOrganizationService hspOrganizationService;

    @CacheEvict(allEntries = true)
    @Override
    public int insertSupplementalRecordFieldSetting(SaveHspOrganizationFiledDto dto) {
        SaveHspOrganizationFiledDto hspOrganizationFiledDto = supplementalRecordFieldSettingService.selectByHspOrgId(dto.getHspOrgId());
        if (Objects.nonNull(hspOrganizationFiledDto)) {
            throw new IllegalArgumentException("送检机构已存在对照关系");
        }
        HspOrganizationDto hspOrganizationDto;
        if (Objects.equals(dto.getHspOrgId(), NumberUtils.LONG_ZERO)) {
            hspOrganizationDto = new HspOrganizationDto();
            hspOrganizationDto.setHspOrgId(DefaultHspOrg.DEFAULT_HSP_ORG_ID);
            hspOrganizationDto.setHspOrgCode(DefaultHspOrg.DEFAULT_HSP_ORG_CODE);
            hspOrganizationDto.setHspOrgName(DefaultHspOrg.DEFAULT_HSP_ORG_NAME);
        } else {
            hspOrganizationDto = hspOrganizationService.selectByHspOrgId(dto.getHspOrgId());
            if (Objects.isNull(hspOrganizationDto)) {
                throw new IllegalArgumentException("送检机构不存在");
            }
        }

        LoginUserHandler.User user = LoginUserHandler.get();
        LocalDateTime now = LocalDateTime.now();

        String filedJson = JSON.toJSONString(dto.getFileds());

        SupplementalRecordFieldSetting tb = new SupplementalRecordFieldSetting();
        tb.setHspOrgId(hspOrganizationDto.getHspOrgId());
        tb.setHspOrgCode(hspOrganizationDto.getHspOrgCode());
        tb.setHspOrgName(hspOrganizationDto.getHspOrgName());
        tb.setSupplementalRecordField(filedJson);
        tb.setCreateDate(now);
        tb.setCreatorId(user.getUserId());
        tb.setCreatorName(user.getNickname());
        tb.setUpdaterId(user.getUserId());
        tb.setUpdaterName(user.getNickname());
        tb.setUpdateDate(now);
        tb.setIsDelete(YesOrNoEnum.NO.getCode());
        return baseMapper.insert(tb);
    }


    @Cacheable(key = "'selectById:' + #id")
    @Override
    public SaveHspOrganizationFiledDto selectById(Serializable id) {
        return convert(baseMapper.selectById(id));
    }

    @Cacheable(key = "'selectByHspOrgId:' + #hspOrgId")
    @Override
    public SaveHspOrganizationFiledDto selectByHspOrgId(Serializable hspOrgId) {
        return convert(baseMapper.selectOne(new LambdaQueryWrapper<SupplementalRecordFieldSetting>().eq(SupplementalRecordFieldSetting::getHspOrgId, hspOrgId).last(" limit 1")));
    }

    @Override
    public SaveHspOrganizationFiledDto selectByHspOrgIdOrDefaultOrg(Serializable id) {
        SaveHspOrganizationFiledDto saveHspOrganizationFiledDto = supplementalRecordFieldSettingService.selectByHspOrgId(id);
        if(Objects.isNull(saveHspOrganizationFiledDto)){
            saveHspOrganizationFiledDto = supplementalRecordFieldSettingService.selectByHspOrgId(DefaultHspOrg.DEFAULT_HSP_ORG_ID);
        }
        return saveHspOrganizationFiledDto;
    }

    @Cacheable(key = "'selectAll'")
    @Override
    public List<SaveHspOrganizationFiledDto> selectAll() {
        return super.list().stream().map(this::convert).collect(Collectors.toList());
    }

    @CacheEvict(allEntries = true)
    @Override
    public int updateSupplementalRecordFieldSetting(SaveHspOrganizationFiledDto dto) {
        SaveHspOrganizationFiledDto hspOrganizationFiledDto = supplementalRecordFieldSettingService.selectById(dto.getFiledId());
        if (Objects.isNull(hspOrganizationFiledDto)) {
            throw new IllegalArgumentException("未查询到数据");
        }

//        HspOrganizationDto hspOrganizationDto = hspOrganizationService.selectByHspOrgId(dto.getHspOrgId());
//        if (Objects.isNull(hspOrganizationDto)) {
//            throw new IllegalArgumentException("送检机构不存在");
//        }

        LoginUserHandler.User user = LoginUserHandler.get();
        LambdaUpdateWrapper<SupplementalRecordFieldSetting> wrapper = new LambdaUpdateWrapper<SupplementalRecordFieldSetting>()
                .eq(SupplementalRecordFieldSetting::getSupplementalRecordFieldSettingId, dto.getFiledId())
                .set(SupplementalRecordFieldSetting::getSupplementalRecordField, JSON.toJSONString(dto.getFileds()))
                .set(SupplementalRecordFieldSetting::getUpdateDate, LocalDateTime.now())
                .set(SupplementalRecordFieldSetting::getUpdaterId, user.getUserId())
                .set(SupplementalRecordFieldSetting::getUpdaterName, user.getUsername());
        return baseMapper.update(null, wrapper);
    }


    @CacheEvict(allEntries = true)
    @Override
    public int deleteSupplementalRecordFieldSetting(Serializable id) {
        return baseMapper.deleteById(id);
    }

    @CacheEvict(allEntries = true)
    @Override
    public int deleteByIds(Collection<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return NumberUtils.INTEGER_ZERO;
        }
        return baseMapper.deleteBatchIds(ids);
    }

    private SaveHspOrganizationFiledDto convert(SupplementalRecordFieldSetting supplementalRecordFieldSetting) {
        if (Objects.isNull(supplementalRecordFieldSetting)) {
            return null;
        }
        String fieldJson = supplementalRecordFieldSetting.getSupplementalRecordField();

        SaveHspOrganizationFiledDto saveHspOrganizationFiledDto = JSON.parseObject(JSON.toJSONString(supplementalRecordFieldSetting), SaveHspOrganizationFiledDto.class);
        saveHspOrganizationFiledDto.setFiledId(supplementalRecordFieldSetting.getSupplementalRecordFieldSettingId());
        saveHspOrganizationFiledDto.setFileds(JSON.parseArray(fieldJson, SaveHspOrganizationFiledDto.Field.class));
        return saveHspOrganizationFiledDto;
    }
}