package com.labway.lims.base.service;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.mapper.TbReportItemMapper;
import com.labway.lims.base.model.TbReportItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告项目编码
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "report-item")
public class ReportItemServiceImpl implements ReportItemService {
    @Resource
    private TbReportItemMapper reportItemMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private ReportItemService reportItemService;
    @Resource
    private GroupService groupService;
    @Resource
    private InstrumentService instrumentService;
    @Resource
    private InstrumentReportItemService instrumentReportItemService;

    @Override
    @Cacheable(key = "'selectByTestItemId:' + #testItemId")
    public List<ReportItemDto> selectByTestItemId(long testItemId) {
        return reportItemMapper
                .selectList(new LambdaQueryWrapper<TbReportItem>().eq(TbReportItem::getTestItemId, testItemId)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<ReportItemDto>> selectByTestItemCodesAsMap(Collection<String> testItemCodes, long orgId) {
        if (CollectionUtils.isEmpty(testItemCodes)) {
            return Collections.emptyMap();
        }
        return reportItemMapper
                .selectList(new LambdaQueryWrapper<TbReportItem>().eq(TbReportItem::getOrgId, orgId)
                        .in(TbReportItem::getTestItemCode, testItemCodes)).stream()
                .map(this::convert).filter(Objects::nonNull)
                .collect(Collectors.groupingBy(ReportItemDto::getTestItemCode));
    }

    @Override
    public List<ReportItemDto> selectByTestItemCodes(Collection<String> testItemCodes, long orgId) {
        if (CollectionUtils.isEmpty(testItemCodes)) {
            return Collections.emptyList();
        }
        return reportItemMapper
                .selectList(new LambdaQueryWrapper<TbReportItem>().eq(TbReportItem::getOrgId, orgId)
                        .in(TbReportItem::getTestItemCode, testItemCodes)).stream()
                .map(this::convert).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<ReportItemDto>> selectByTestItemIdsAsMap(Collection<Long> testItemIds, long orgId) {
        if (CollectionUtils.isEmpty(testItemIds)) {
            return Collections.emptyMap();
        }
        return reportItemMapper
                .selectList(new LambdaQueryWrapper<TbReportItem>().eq(TbReportItem::getOrgId, orgId)
                        .in(TbReportItem::getTestItemId, testItemIds)).stream()
                .map(this::convert).filter(Objects::nonNull)
                .collect(Collectors.groupingBy(ReportItemDto::getTestItemId));
    }

    @Override
    public Map<Long, Integer> selectReportItemCountByTestItemIds(Collection<Long> testItemIds) {
        if (CollectionUtils.isEmpty(testItemIds)) {
            return Collections.emptyMap();
        }
        List<ReportItemCountByTestItemIdDto> reportItemCountByTestItemIdDtos =
                reportItemMapper.selectReportItemCountByTestItemIds(testItemIds);
        return reportItemCountByTestItemIdDtos.stream().collect(Collectors
                .toMap(ReportItemCountByTestItemIdDto::getTestItemId, ReportItemCountByTestItemIdDto::getReportItemCount));
    }

    @Override
    public List<ReportItemDto> selectByTestItemIds(Collection<Long> testItemIds) {
        if (CollectionUtils.isEmpty(testItemIds)) {
            return Collections.emptyList();
        }

        return reportItemMapper
                .selectList(new LambdaQueryWrapper<TbReportItem>().in(TbReportItem::getTestItemId, testItemIds)).stream()
                .map(this::convert).collect(Collectors.toList());

    }

    @Override
    @Cacheable(key = "'selectByReportItemId:' + #reportItemId")
    public ReportItemDto selectByReportItemId(long reportItemId) {
        final LambdaQueryWrapper<TbReportItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbReportItem::getReportItemId, reportItemId).last("limit 1");
        return convert(reportItemMapper.selectOne(wrapper));
    }

    @Override
    @Cacheable(key = "'selectByOrgId:' + #orgId")
    public List<ReportItemDto> selectByOrgId(long orgId) {
        final LambdaQueryWrapper<TbReportItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbReportItem::getOrgId, orgId);
        return convert(reportItemMapper.selectList(queryWrapper));
    }

    @Override
    @CacheEvict(allEntries = true)
    public long addReportItem(ReportItemDto reportItem) {
        final TbReportItem item = Objects.requireNonNull(convert(reportItem), "参数错误");
        if (Objects.isNull(item.getReportItemId())) {
            item.setReportItemId(snowflakeService.genId());
        }

        if (reportItemMapper.insert(item) < 1) {
            throw new IllegalStateException("添加报告项目失败");
        }

        log.info("用户 [{}] 新增报告项目 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(reportItem));

        return item.getReportItemId();
    }

    @Nullable
    @Override
    public ReportItemDto selectByReportItemCode(String reportItemCode, Long orgId) {

        final LambdaQueryWrapper<TbReportItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbReportItem::getOrgId, orgId);
        queryWrapper.eq(TbReportItem::getReportItemCode, reportItemCode);
        final List<TbReportItem> list = reportItemMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return convert(list.iterator().next());
    }

    @Nullable
    @Override
    public List<ReportItemDto> selectByReportItemCodes(List<String> reportItemCodes, Long orgId) {

        final LambdaQueryWrapper<TbReportItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbReportItem::getOrgId, orgId);
        queryWrapper.in(TbReportItem::getReportItemCode, reportItemCodes);
        final List<TbReportItem> list = reportItemMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByTestItemId(long testItemId) {
        deleteByTestItemIds(List.of(testItemId));
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByTestItemIds(Collection<Long> testItemIds) {
        if (CollectionUtils.isEmpty(testItemIds)) {
            return;
        }
        reportItemMapper.delete(new LambdaQueryWrapper<TbReportItem>()
                .in(TbReportItem::getTestItemId, testItemIds));
    }

    private ReportItemDto convert(TbReportItem reportItem) {
        if (Objects.isNull(reportItem)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(reportItem), ReportItemDto.class);
    }

    private TbReportItem convert(ReportItemDto reportItem) {
        if (Objects.isNull(reportItem)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(reportItem), TbReportItem.class);
    }

    /**
     * TbReportItem 转换 为 ReportItemDto
     *
     * @param list TbReportItem
     * @return ReportItemDto
     */
    private List<ReportItemDto> convert(List<TbReportItem> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<InstrumentDto> selectInstrumentsExcludeByInstrumentId(Long instrumentId) {

        Long orgId = LoginUserHandler.get().getOrgId();
        List<ProfessionalGroupDto> professionalGroups = groupService.selectByOrgId(orgId);

        List<Long> groupIds =
                professionalGroups.stream()
                        .filter(e -> Objects.equals(YesOrNoEnum.YES.getCode(), e.getEnable()))
                        .map(ProfessionalGroupDto::getGroupId)
                        .collect(Collectors.toList());
        String cacheKey = MD5.create().digestHex(groupIds.stream().map(String::valueOf).collect(Collectors.joining("")));

        log.info("根据分组查询仪器列表【groupIds：{}, cacheKey：{}】", groupIds, cacheKey);
        List<InstrumentDto> instrumentDtos = instrumentService.selectByGroupIds(groupIds, cacheKey);
        instrumentDtos.sort(Comparator.comparing(InstrumentDto::getGroupId).thenComparing(InstrumentDto::getInstrumentId));

        return instrumentDtos.stream()
                // 过滤掉当前的仪器
                .filter(e ->
                        !Objects.equals(e.getInstrumentId(), instrumentId) &&
                                Objects.equals(YesOrNoEnum.YES.getCode(), e.getEnable()))
                .collect(Collectors.toList());
    }

    /**
     * 仪器报告项目拷贝
     */
    @Override
    public List<Long> reportItemCopy(Long originInstrumentId, List<Long> originInstrumentReportItemIds, List<Long> targetInstrumentIds) {
        // 源仪器信息
        InstrumentDto originInstrument = instrumentService.selectByInstrumentId(originInstrumentId);
        if (Objects.isNull(originInstrument)) {
            throw new IllegalArgumentException("仪器不存在");
        }
        // 目标仪器信息
        List<InstrumentDto> targetInstruments = instrumentService.selectByInstrumentIds(targetInstrumentIds);
        if (CollectionUtils.isEmpty(targetInstruments)) {
            throw new IllegalArgumentException("目标仪器不存在");
        }

        List<Long> ids = new ArrayList<>();
        // 查询要拷贝的【仪器报告项目】
        List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentId(originInstrumentId);

        // 过滤指定的仪器报告项目ID
        instrumentReportItems =
                instrumentReportItems
                        .stream()
                        .filter(e -> originInstrumentReportItemIds.contains(e.getInstrumentReportItemId()))
                        .collect(Collectors.toList());

        for (Long targetInstrumentId : targetInstrumentIds) {
            // 拷贝【仪器报告项目（基本信息，参考范围，常用短语，结果值转换）】到【目标仪器】
            ids.addAll(instrumentReportItemService.copyInstrumentReportItems(instrumentReportItems, targetInstrumentId));
        }

        return ids;
    }

    @Override
    @CacheEvict(allEntries = true)
    public void updateByReportItemCodes(ReportItemDto reportItem, Collection<String> reportItemCodes) {
        if (CollectionUtils.isEmpty(reportItemCodes)) {
            return;
        }

        final TbReportItem tb = new TbReportItem();
        BeanUtils.copyProperties(reportItem, tb);

        tb.setReportItemCode(null);
        tb.setReportItemId(null);

        reportItemMapper.update(tb, new LambdaQueryWrapper<TbReportItem>()
                .in(TbReportItem::getReportItemCode, reportItemCodes));
    }

}
