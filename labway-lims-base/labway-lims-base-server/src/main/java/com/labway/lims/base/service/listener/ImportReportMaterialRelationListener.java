package com.labway.lims.base.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.ReportMaterialRelationDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.ReportMaterialRelationService;
import com.labway.lims.base.vo.ImportErrorResponseVo;
import com.labway.lims.base.vo.excel.ImportReportMaterialRelationVo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告物料关联导入监听器
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
public class ImportReportMaterialRelationListener extends AnalysisEventListener<ImportReportMaterialRelationVo> {

    private final SnowflakeService snowflakeService;
    private final LoginUserHandler.User loginUser;
    private final ReportMaterialRelationService reportMaterialRelationService;
    private final GroupService groupService;

    public ImportReportMaterialRelationListener(SnowflakeService snowflakeService, LoginUserHandler.User loginUser,
                                          ReportMaterialRelationService reportMaterialRelationService,
                                          GroupService groupService) {
        this.snowflakeService = snowflakeService;
        this.loginUser = loginUser;
        this.reportMaterialRelationService = reportMaterialRelationService;
        this.groupService = groupService;
    }

    @Getter
    private final List<ImportErrorResponseVo> importErrorResponseVoList = new ArrayList<>();
    
    @Getter
    private final Map<Integer, ImportReportMaterialRelationVo> excelDataMap = new HashMap<>();
    
    @Getter
    private final List<ReportMaterialRelationDto> targetList = new ArrayList<>();
    
    // 用于检查Excel内部完全重复的数据
    private final Map<String, Integer> duplicateCheckMap = new HashMap<>();
    
    // 数据库中已存在的记录
    private List<ReportMaterialRelationDto> existingRecords;
    
    // 缓存专业组编码到专业组ID的映射
    private Map<String, ProfessionalGroupDto> groupCodeToGroupMap;

    @Override
    public void invoke(ImportReportMaterialRelationVo data, AnalysisContext analysisContext) {
        Integer rowIndex = analysisContext.readRowHolder().getRowIndex() + 1;
        log.debug("处理第{}行数据", rowIndex);

        // 检查数据格式
        Map<String, String> errorMessages = validateData(data);

        if (!errorMessages.isEmpty()) {
            // 将每个字段的错误信息分别添加到错误列表
            errorMessages.forEach((field, errorMsg) -> {
                importErrorResponseVoList.add(ImportErrorResponseVo.builder()
                        .rowNo(rowIndex)
                        .errorInfo("第" + rowIndex + "行 " + field + ": " + errorMsg)
                        .build());
            });
        } else {
            // 初步检查通过，检查Excel内部是否有完全重复的数据
            String duplicateKey = generateDuplicateKey(data);
            if (duplicateCheckMap.containsKey(duplicateKey)) {
                Integer existingRowIndex = duplicateCheckMap.get(duplicateKey);
                importErrorResponseVoList.add(ImportErrorResponseVo.builder()
                        .rowNo(rowIndex)
                        .errorInfo("第" + rowIndex + "行与第" + existingRowIndex + "行数据完全相同")
                        .build());
            } else {
                duplicateCheckMap.put(duplicateKey, rowIndex);
                excelDataMap.put(rowIndex, data);
            }
        }
    }
    
    /**
     * 生成用于检查重复的键
     * 将所有字段拼接成一个字符串作为键
     */
    private String generateDuplicateKey(ImportReportMaterialRelationVo data) {
        return StringUtils.trimToEmpty(String.valueOf(data.getGroupCode())) + "|" +
               StringUtils.trimToEmpty(data.getGroupName()) + "|" +
               StringUtils.trimToEmpty(data.getReportItemCode()) + "|" +
               StringUtils.trimToEmpty(data.getReportItemName()) + "|" +
               StringUtils.trimToEmpty(data.getMaterialCode()) + "|" +
               StringUtils.trimToEmpty(data.getMaterialName());
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (CollectionUtils.isEmpty(Collections.singleton(excelDataMap))) {
            log.info("没有有效的Excel数据需要处理");
            return;
        }

        log.info("开始处理Excel数据，共{}行", excelDataMap.size());
        
        try {
            // 获取数据库中已存在的记录
            fetchExistingRecords();
            
            // 收集所有的专业组编码
            Set<String> groupCodes = excelDataMap.values().stream()
                    .map(ImportReportMaterialRelationVo::getGroupCode)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
            
            // 查询所有相关的专业组信息
            fetchGroupsByGroupCodes(groupCodes);
            
            // 进一步验证并转换数据
            for (Map.Entry<Integer, ImportReportMaterialRelationVo> entry : excelDataMap.entrySet()) {
                Integer rowIndex = entry.getKey();
                ImportReportMaterialRelationVo vo = entry.getValue();
                
                try {
                    // 检查是否与数据库中的记录完全重复
                    if (isDuplicateWithExistingRecord(vo)) {
                        importErrorResponseVoList.add(ImportErrorResponseVo.builder()
                                .rowNo(rowIndex)
                                .errorInfo("第" + rowIndex + "行数据在数据库中已存在完全相同的记录")
                                .build());
                        continue;
                    }
                    
                    // 根据groupCode获取groupId
                    Long groupId = null;
                    if (StringUtils.isNotBlank(vo.getGroupCode()) && groupCodeToGroupMap.containsKey(vo.getGroupCode())) {
                        ProfessionalGroupDto groupDto = groupCodeToGroupMap.get(vo.getGroupCode());
                        groupId = groupDto.getGroupId();
                    } else {
                        importErrorResponseVoList.add(ImportErrorResponseVo.builder()
                                .rowNo(rowIndex)
                                .errorInfo("第" + rowIndex + "行专业组编码[" + vo.getGroupCode() + "]在系统中不存在")
                                .build());
                        continue;
                    }
                    
                    // 转换为DTO
                    ReportMaterialRelationDto dto = new ReportMaterialRelationDto();
                    dto.setReportMaterialRelationId(snowflakeService.genId());
                    dto.setReportItemCode(vo.getReportItemCode());
                    dto.setReportItemName(vo.getReportItemName());
                    dto.setMaterialCode(vo.getMaterialCode());
                    dto.setMaterialName(vo.getMaterialName());
                    dto.setGroupName(vo.getGroupName());
                    dto.setGroupCode(vo.getGroupCode());
                    dto.setGroupId(groupId); // 设置查询到的groupId
                    dto.setCreateDate(new Date());
                    dto.setCreatorId(loginUser.getUserId());
                    dto.setCreatorName(loginUser.getNickname());
                    dto.setIsDelete(0);
                    
                    targetList.add(dto);
                    log.debug("第{}行数据验证通过，已添加到目标列表", rowIndex);
                    
                } catch (Exception e) {
                    log.error("处理第{}行数据异常", rowIndex, e);
                    importErrorResponseVoList.add(ImportErrorResponseVo.builder()
                            .rowNo(rowIndex)
                            .errorInfo("第" + rowIndex + "行处理异常: " + e.getMessage())
                            .build());
                }
            }
            
        } catch (Exception e) {
            log.error("处理Excel数据异常", e);
            // 如果发生异常，给所有行添加错误信息
            for (Integer rowIndex : excelDataMap.keySet()) {
                importErrorResponseVoList.add(ImportErrorResponseVo.builder()
                        .rowNo(rowIndex)
                        .errorInfo("数据处理异常: " + e.getMessage())
                        .build());
            }
        }
        
        log.info("Excel数据处理完成，有效数据{}行，错误数据{}行", targetList.size(), importErrorResponseVoList.size());
        if (!importErrorResponseVoList.isEmpty()) {
            log.info("错误信息列表: {}", importErrorResponseVoList.stream()
                    .map(e -> "行" + e.getRowNo() + ": " + e.getErrorInfo())
                    .collect(Collectors.joining(", ")));
        }
    }

    /**
     * 获取数据库中已存在的记录
     */
    private void fetchExistingRecords() {
        log.info("获取数据库中已存在的记录");
        existingRecords = reportMaterialRelationService.selectAll();
        if (existingRecords == null) {
            existingRecords = new ArrayList<>();
        }
        log.info("从数据库获取到{}条记录", existingRecords.size());
    }
    
    /**
     * 根据专业组编码查询专业组信息
     */
    private void fetchGroupsByGroupCodes(Set<String> groupCodes) {
        log.info("根据专业组编码查询专业组信息，共{}个编码", groupCodes.size());
        groupCodeToGroupMap = new HashMap<>();
        if (!groupCodes.isEmpty()) {
            // 查询当前机构下的所有专业组信息
            Map<String, ProfessionalGroupDto> groupMap = groupService.selectByGroupCodes(groupCodes, loginUser.getOrgId());
            if (groupMap != null) {
                groupCodeToGroupMap = groupMap;
            }
        }
        log.info("查询到{}个专业组信息", groupCodeToGroupMap.size());
    }
    
    /**
     * 检查数据是否与数据库中的记录完全重复
     */
    private boolean isDuplicateWithExistingRecord(ImportReportMaterialRelationVo vo) {
        for (ReportMaterialRelationDto record : existingRecords) {
            if (StringUtils.equals(StringUtils.trimToEmpty(record.getGroupName()), StringUtils.trimToEmpty(vo.getGroupName())) &&
                StringUtils.equals(StringUtils.trimToEmpty(record.getReportItemCode()), StringUtils.trimToEmpty(vo.getReportItemCode())) &&
                StringUtils.equals(StringUtils.trimToEmpty(record.getReportItemName()), StringUtils.trimToEmpty(vo.getReportItemName())) &&
                StringUtils.equals(StringUtils.trimToEmpty(record.getMaterialCode()), StringUtils.trimToEmpty(vo.getMaterialCode())) &&
                StringUtils.equals(StringUtils.trimToEmpty(record.getMaterialName()), StringUtils.trimToEmpty(vo.getMaterialName()))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证基础数据格式，返回字段名和错误信息的映射
     */
    private Map<String, String> validateData(ImportReportMaterialRelationVo data) {
        Map<String, String> errorMessages = new HashMap<>();
        
        if (data == null) {
            errorMessages.put("整行", "数据为空");
            return errorMessages;
        }
        
        // 检查必填字段
        if (StringUtils.isBlank(data.getGroupName())) {
            errorMessages.put("专业组名称", "不能为空");
        }

        if (StringUtils.isBlank(data.getGroupCode())) {
            errorMessages.put("专业组编码", "不能为空");
        }

        if (StringUtils.isBlank(data.getReportItemCode())) {
            errorMessages.put("报告项目编码", "不能为空");
        }
        
        if (StringUtils.isBlank(data.getReportItemName())) {
            errorMessages.put("报告项目名称", "不能为空");
        }
        
        if (StringUtils.isBlank(data.getMaterialCode())) {
            errorMessages.put("物资编号", "不能为空");
        }
        
        if (StringUtils.isBlank(data.getMaterialName())) {
            errorMessages.put("物资名称", "不能为空");
        }
        
        return errorMessages;
    }
}