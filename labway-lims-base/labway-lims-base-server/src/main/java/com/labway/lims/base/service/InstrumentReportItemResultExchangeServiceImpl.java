package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;
import com.labway.lims.base.api.service.InstrumentReportItemResultExchangeService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.mapper.TbInstrumentReportItemResultExchangeMapper;
import com.labway.lims.base.mapstruct.InstrumentReportItemResultExchangeConverter;
import com.labway.lims.base.model.TbInstrumentReportItemResultExchange;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "instrument-report-item-result-exchange")
public class InstrumentReportItemResultExchangeServiceImpl implements InstrumentReportItemResultExchangeService {
    @Resource
    private TbInstrumentReportItemResultExchangeMapper instrumentReportItemResultExchangeMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;

    @DubboReference
    private RabbitMQService rabbitMQService;

    @Resource
    private InstrumentReportItemResultExchangeConverter converter;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Override
    @CacheEvict(allEntries = true)
    public long addInstrumentReportItemResultExchange(InstrumentReportItemResultExchangeDto dto) {

        final InstrumentReportItemDto instrumentReportItem =
            instrumentReportItemService.selectByInstrumentReportItemId(dto.getInstrumentReportItemId());
        if (Objects.isNull(instrumentReportItem)) {
            throw new IllegalStateException("报告项目不存在");
        }

        final TbInstrumentReportItemResultExchange exchange = new TbInstrumentReportItemResultExchange();
        exchange.setInstrumentReportItemResultExchangeId(snowflakeService.genId());
        exchange.setInstrumentReportItemId(dto.getInstrumentReportItemId());
        exchange.setReportItemCode(instrumentReportItem.getReportItemCode());
        exchange.setReportItemName(instrumentReportItem.getReportItemName());
        exchange.setInstrumentId(instrumentReportItem.getInstrumentId());
        exchange.setInstrumentCode(instrumentReportItem.getInstrumentCode());
        exchange.setInstrumentName(instrumentReportItem.getInstrumentName());
        exchange.setInstrumentResult(StringUtils.defaultString(dto.getInstrumentResult()));
        exchange.setExchangeResult(StringUtils.defaultString(dto.getExchangeResult()));
        exchange.setIsDelete(YesOrNoEnum.NO.getCode());
        exchange.setCreateDate(new Date());
        exchange.setUpdateDate(new Date());
        exchange.setCreatorId(LoginUserHandler.get().getUserId());
        exchange.setCreatorName(LoginUserHandler.get().getNickname());
        exchange.setUpdaterId(LoginUserHandler.get().getUserId());
        exchange.setUpdaterName(LoginUserHandler.get().getNickname());
        exchange.setFormulaMax(dto.getFormulaMax());
        exchange.setFormulaMaxValue(dto.getFormulaMaxValue());
        exchange.setFormulaMin(dto.getFormulaMin());
        exchange.setFormulaMinValue(dto.getFormulaMinValue());

        // 新的结果值转换，不能根据仪器结果值判断重复，要根据操作符进行判断
        if (selectByInstrumentReportItemId(dto.getInstrumentReportItemId()).stream()
//            .anyMatch(e -> Objects.equals(e.getInstrumentResult(), dto.getInstrumentResult()))) {
            .anyMatch(e -> Objects.equals(e.getFormulaMax(), dto.getFormulaMax())
                && Objects.equals(e.getFormulaMaxValue(), dto.getFormulaMaxValue())
                && Objects.equals(e.getFormulaMin(), dto.getFormulaMin())
                && Objects.equals(e.getFormulaMinValue(), dto.getFormulaMinValue())
        )) {
            throw new IllegalStateException("仪器结果值重复");
        }

        if (instrumentReportItemResultExchangeMapper.insert(exchange) < 1) {
            throw new IllegalStateException("新增结果值转换失败");
        }

        log.info("用户 [{}] 新增结果值转换 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(exchange));

        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_RESULT_EXCHANGE.getDesc())
                .setContent(String.format("用户 [%s] 新增了仪器[%s]-报告项目[%s]的结果值转换，新增内容为: " + "仪器结果值 [%s] 转换结果值 [%s]",
                    LoginUserHandler.get().getNickname(), exchange.getInstrumentName(), exchange.getReportItemName(),
                    exchange.getInstrumentResult(), exchange.getExchangeResult()))
                .toJSONString());

        return exchange.getInstrumentReportItemResultExchangeId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByInstrumentReportItemResultExchangeId(long instrumentReportItemResultExchangeId) {
        return instrumentReportItemResultExchangeMapper.deleteById(instrumentReportItemResultExchangeId) > 0;
    }

    @Override
    @Cacheable(key = "'selectByInstrumentReportItemId:' + #instrumentReportItemId")
    public List<InstrumentReportItemResultExchangeDto> selectByInstrumentReportItemId(long instrumentReportItemId) {
        return instrumentReportItemResultExchangeMapper
            .selectList(new LambdaQueryWrapper<TbInstrumentReportItemResultExchange>()
                .orderByDesc(TbInstrumentReportItemResultExchange::getInstrumentReportItemResultExchangeId)
                .eq(TbInstrumentReportItemResultExchange::getInstrumentReportItemId, instrumentReportItemId))
            .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByInstrumentReportItemResultExchangeId(InstrumentReportItemResultExchangeDto dto) {

        final InstrumentReportItemResultExchangeDto old =
            selectByInstrumentReportItemResultExchangeId(dto.getInstrumentReportItemResultExchangeId());
        if (Objects.isNull(old)) {
            throw new IllegalArgumentException("结果值转换不存在");
        }

        final TbInstrumentReportItemResultExchange exchange = new TbInstrumentReportItemResultExchange();
        exchange.setInstrumentReportItemResultExchangeId(dto.getInstrumentReportItemResultExchangeId());
        exchange.setInstrumentResult(dto.getInstrumentResult());
        exchange.setExchangeResult(dto.getExchangeResult());
        exchange.setUpdateDate(new Date());
        exchange.setUpdaterId(LoginUserHandler.get().getUserId());
        exchange.setUpdaterName(LoginUserHandler.get().getNickname());
        exchange.setFormulaMin(dto.getFormulaMin());
        exchange.setFormulaMinValue(dto.getFormulaMinValue());
        exchange.setFormulaMax(dto.getFormulaMax());
        exchange.setFormulaMaxValue(dto.getFormulaMaxValue());


        if (selectByInstrumentReportItemId(old.getInstrumentReportItemId()).stream()
            .anyMatch(e -> Objects.equals(e.getFormulaMax(), dto.getFormulaMax())
                    && Objects.equals(e.getFormulaMaxValue(), dto.getFormulaMaxValue())
                    && Objects.equals(e.getFormulaMin(), dto.getFormulaMin())
                    && Objects.equals(e.getFormulaMinValue(), dto.getFormulaMinValue())
                    && !Objects.equals(e.getInstrumentReportItemResultExchangeId(), dto.getInstrumentReportItemResultExchangeId()))) {
            throw new IllegalStateException("仪器结果值重复");
        }

        if (instrumentReportItemResultExchangeMapper.updateById(exchange) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改结果值转换 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(exchange));

        return true;
    }

    @Override
    @Cacheable(key = "'selectByInstrumentReportItemResultExchangeId:' + #instrumentReportItemResultExchangeId")
    public InstrumentReportItemResultExchangeDto
        selectByInstrumentReportItemResultExchangeId(long instrumentReportItemResultExchangeId) {
        return convert(instrumentReportItemResultExchangeMapper.selectById(instrumentReportItemResultExchangeId));
    }

    @Override
    public List<InstrumentReportItemResultExchangeDto>
        selectByInstrumentReportItemResultExchangeIds(Collection<Long> instrumentReportItemResultExchangeIds) {
        if (CollectionUtils.isEmpty(instrumentReportItemResultExchangeIds)) {
            return Collections.emptyList();
        }

        return instrumentReportItemResultExchangeMapper.selectBatchIds(instrumentReportItemResultExchangeIds).stream()
            .map(this::convert).collect(Collectors.toList());
    }

    @CacheEvict(allEntries = true)
    @Override
    public void deleteByInstrumentReportItemId(long instrumentReportItemId) {
        instrumentReportItemResultExchangeMapper.delete(new LambdaQueryWrapper<TbInstrumentReportItemResultExchange>()
            .eq(TbInstrumentReportItemResultExchange::getInstrumentReportItemId, instrumentReportItemId));
    }

    private InstrumentReportItemResultExchangeDto convert(TbInstrumentReportItemResultExchange exchange) {
        if (Objects.isNull(exchange)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(exchange), InstrumentReportItemResultExchangeDto.class);
    }

    /**
     * 拷贝仪器项目结果值转换
     * 
     * @param fromInstrumentReportItemId
     * @param instrumentReportItemDto
     * @return
     */
    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public List<Long> copyReportItemResultExchange(Long fromInstrumentReportItemId,
        InstrumentReportItemDto instrumentReportItemDto) {
        List<Long> ids = new ArrayList<>();
        // 仪器报告项目ID
        Long instrumentReportItemId = instrumentReportItemDto.getInstrumentReportItemId();

        {
            // 删除原有的结果值转换
            LambdaQueryWrapper<TbInstrumentReportItemResultExchange> wrapper =
                Wrappers.lambdaQuery(TbInstrumentReportItemResultExchange.class)
                    .eq(TbInstrumentReportItemResultExchange::getInstrumentReportItemId, instrumentReportItemId);
            instrumentReportItemResultExchangeMapper.delete(wrapper);
        }

        String nickname = LoginUserHandler.get().getNickname();
        Date current = new Date();
        List<InstrumentReportItemResultExchangeDto> resultExchangeDtos =
            selectByInstrumentReportItemId(fromInstrumentReportItemId);
        for (InstrumentReportItemResultExchangeDto resultExchangeDto : resultExchangeDtos) {
            TbInstrumentReportItemResultExchange resultExchange = converter.convertDto2Entity(resultExchangeDto);

            resultExchange.setInstrumentReportItemResultExchangeId(snowflakeService.genId());
            resultExchange.setInstrumentReportItemId(instrumentReportItemId);

            resultExchange.setReportItemCode(instrumentReportItemDto.getReportItemCode());
            resultExchange.setReportItemName(instrumentReportItemDto.getReportItemName());
            resultExchange.setInstrumentId(instrumentReportItemDto.getInstrumentId());
            resultExchange.setInstrumentCode(instrumentReportItemDto.getInstrumentCode());
            resultExchange.setInstrumentName(instrumentReportItemDto.getInstrumentName());

            resultExchange.setIsDelete(YesOrNoEnum.NO.getCode());
            resultExchange.setCreateDate(current);
            resultExchange.setCreatorId(LoginUserHandler.get().getUserId());
            resultExchange.setCreatorName(nickname);
            resultExchange.setUpdateDate(current);
            resultExchange.setUpdaterId(LoginUserHandler.get().getUserId());
            resultExchange.setUpdaterName(nickname);

            if (selectByInstrumentReportItemId(instrumentReportItemId).stream()
                    .anyMatch(e -> Objects.equals(e.getFormulaMax(), resultExchange.getFormulaMax())
                            && Objects.equals(e.getFormulaMaxValue(), resultExchange.getFormulaMaxValue())
                            && Objects.equals(e.getFormulaMin(), resultExchange.getFormulaMin())
                            && Objects.equals(e.getFormulaMinValue(), resultExchange.getFormulaMinValue())
                            && !Objects.equals(e.getInstrumentReportItemResultExchangeId(), resultExchange.getInstrumentReportItemResultExchangeId()))) {
                throw new IllegalStateException("仪器结果值重复");
            }

            if (instrumentReportItemResultExchangeMapper.insert(resultExchange) < 1) {
                throw new IllegalStateException("拷贝结果值转换失败");
            }

            log.info("用户 [{}] 拷贝结果值转换 [{}] 成功", nickname, JSON.toJSONString(resultExchange));

            // 异步发送消息
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_RESULT_EXCHANGE.getDesc())
                    .setContent(String.format("用户 [%s] 拷贝了仪器[%s]-报告项目[%s]的结果值转换，拷贝内容为: " + "仪器结果值 [%s] 转换结果值 [%s]",
                        nickname, resultExchange.getInstrumentName(), resultExchange.getReportItemName(),
                        resultExchange.getInstrumentResult(), resultExchange.getExchangeResult()))
                    .toJSONString());

            ids.add(resultExchange.getInstrumentReportItemResultExchangeId());
        }

        return ids;
    }

    @Override
    public Collection<InstrumentReportItemResultExchangeDto>
        selectByReportItemCodes(Collection<String> reportItemCodes) {

        if (CollectionUtils.isEmpty(reportItemCodes)) {
            return Collections.emptyList();
        }

        return instrumentReportItemResultExchangeMapper
            .selectList(Wrappers.<TbInstrumentReportItemResultExchange>lambdaQuery()
                .in(TbInstrumentReportItemResultExchange::getReportItemCode, reportItemCodes))
            .stream().map(this::convert).collect(Collectors.toList());
    }

}
