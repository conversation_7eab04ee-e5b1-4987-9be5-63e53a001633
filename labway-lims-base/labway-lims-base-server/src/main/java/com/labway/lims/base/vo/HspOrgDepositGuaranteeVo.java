package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 机构保底金维护
 */
@Getter
@Setter
public class HspOrgDepositGuaranteeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull(message = "机构保底金ID为空")
    private Long hspOrgDepositGuaranteeId;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 生效日期
     */
    @NotNull(message = "请选择生效日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "请选择结束日期")
    private Date endDate;

    /**
     * 保底金额
     */
    @NotNull(message = "保底金额不能为空")
    @DecimalMin(value = "0.01", message = "保底金额必须大于0")
    private BigDecimal guaranteeAmount;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 修改人ID
     */
    private Long updaterId;

    /**
     * 修改人名称
     */
    private String updaterName;
}