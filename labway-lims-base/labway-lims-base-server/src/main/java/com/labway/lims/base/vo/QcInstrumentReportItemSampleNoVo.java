package com.labway.lims.base.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 设置报告项目样本号：即该样品号默认为质控测试，否则为一般化检查
 */
@Data
public class QcInstrumentReportItemSampleNoVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 仪器id
     */
    private Long instrumentId;
    /**
     * 报告项目id
     */
    private Long reportItemId;
    /**
     * 浓度
     */
    private Integer sort;
    /**
     * 样本号
     */
    private String sampleNo;
}
