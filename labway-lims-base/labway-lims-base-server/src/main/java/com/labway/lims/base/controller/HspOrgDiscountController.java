package com.labway.lims.base.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.AddHspOrgBasePackageDto;
import com.labway.lims.base.api.dto.AddHspOrgBasePackageSendTypeDto;
import com.labway.lims.base.api.dto.HspOrgDiscountCompareInfo;
import com.labway.lims.base.api.dto.HspOrgDiscountDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDto;
import com.labway.lims.base.api.dto.UpdateHspOrgDiscountDto;
import com.labway.lims.base.api.service.HspOrgDiscountService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.vo.HspOrgDiscountVo;
import com.labway.lims.base.vo.UpdateHspOrgDiscountVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户折扣
 */
@Slf4j
@RestController
@RequestMapping("/hsp-org-discount")
public class HspOrgDiscountController extends BaseController {

    @Resource
    private HspOrgDiscountService hspOrgDiscountService;

    @Resource
    private ItemPriceBasePackageService itemPriceBasePackageService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 修改客户折扣
     */
    @PostMapping("/update-hsp-org-discount")
    public Object updateHspOrgDiscount(@RequestBody UpdateHspOrgDiscountVo vo) {
        if (Objects.isNull(vo.getDiscountId())) {
            throw new IllegalArgumentException("请选择客户折扣");
        }

        if (StringUtils.isAnyBlank(vo.getSendTypeCode(), vo.getSendType())) {
            throw new IllegalArgumentException("请选择送检类型");
        }

        final Date startDate = vo.getStartDate();
        final Date endDate = vo.getEndDate();
        // 日期校验
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            throw new IllegalArgumentException("请选择生效日期");
        }

        if (startDate.after(endDate)) {
            throw new IllegalArgumentException("生效日期不能大于失效日期");
        }

        final BigDecimal discount = vo.getDiscount();
        if (Objects.isNull(discount)) {
            throw new IllegalArgumentException("请输入折扣率");
        }

        if (discount.doubleValue() < 0 || discount.doubleValue() > 1) {
            throw new IllegalArgumentException("折扣率必须 >=0 且 <=1");
        }

        final HspOrgDiscountDto now = hspOrgDiscountService.selectById(vo.getDiscountId());
        if (Objects.isNull(now)) {
            throw new IllegalArgumentException("客户折扣率不存在修改失败");
        }
        ItemPriceBasePackageDto priceBasePackageDto = itemPriceBasePackageService.selectById(now.getPackageId());

        HspOrgDiscountCompareInfo compareInfo = new HspOrgDiscountCompareInfo();
        if (Objects.nonNull(priceBasePackageDto)) {
            compareInfo.setPackageName(priceBasePackageDto.getPackageName());
        }
        compareInfo.setSendType(now.getSendType());
        compareInfo.setStartDateStr(DateUtil.formatDate(now.getStartDate()));
        compareInfo.setEndDateStr(DateUtil.formatDate(now.getEndDate()));
        compareInfo.setDiscount(now.getDiscount().setScale(4, RoundingMode.HALF_UP));

        final UpdateHspOrgDiscountDto target = JSON.parseObject(JSON.toJSONString(vo), UpdateHspOrgDiscountDto.class);
        HspOrgDiscountCompareInfo hspOrgDiscountCompareInfo = hspOrgDiscountService.updateHspOrgDiscount(target);

        String compare = new CompareUtils<HspOrgDiscountCompareInfo>().compare(compareInfo, hspOrgDiscountCompareInfo);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_ORG_DISCOUNT.getDesc())
                    .setContent(String.format("[%s] 机构修改客户折扣: %s", now.getHspOrgName(), compare)).toJSONString());
        }

        return Collections.emptyMap();
    }

    /**
     * 添加机构基准包
     */
    @PostMapping("/add-hsp-org-base-package")
    public Object addHspOrgBasePackage(@RequestBody AddHspOrgBasePackageDto vo) {

        if (CollectionUtils.isEmpty(vo.getHspOrgIds())) {
            throw new IllegalArgumentException("请选择送检机构");
        }

        final Long packageId = vo.getPackageId();
        if (Objects.isNull(packageId)) {
            throw new IllegalArgumentException("请选择基准包");
        }

        List<AddHspOrgBasePackageSendTypeDto> sendTypeList = vo.getSendTypeList();
        if (CollectionUtils.isEmpty(sendTypeList) || sendTypeList.stream()
            .anyMatch(obj -> StringUtils.isAnyBlank(obj.getSendType(), obj.getSendTypeCode()))) {
            throw new IllegalArgumentException("请选择送检类型");
        }

        final Date startDate = vo.getStartDate();
        final Date endDate = vo.getEndDate();

        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            throw new IllegalArgumentException("请选择生效日期或结束日期");
        }

        if (endDate.before(startDate)) {
            throw new IllegalArgumentException("结束日期不能早于生效日期");
        }

        final BigDecimal discount = vo.getDiscount();
        if (Objects.isNull(discount)) {
            throw new IllegalArgumentException("折扣率不能为空");
        }

        if (discount.doubleValue() < 0 || discount.doubleValue() > 1) {
            throw new IllegalArgumentException("折扣率必须 >=0 且 <=1");
        }
        ItemPriceBasePackageDto itemPriceBasePackage = itemPriceBasePackageService.selectById(packageId);
        if (Objects.isNull(itemPriceBasePackage)) {
            throw new IllegalStateException("基准包不存在");
        }

        List<HspOrgDiscountDto> hspOrgDiscountDtoList = hspOrgDiscountService.addHspOrgBasePackage(vo);

        hspOrgDiscountDtoList.forEach(item -> {
            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY, TraceLog.newInstance()
                .setModule(TraceLogModuleEnum.HSP_ORG_DISCOUNT.getDesc())
                .setContent(String.format("[%s] 机构新增客户折扣: 基准包名称: [%s] ,送检类型: [%s] ,生效时间: [%s] ,结束时间: [%s] ,折扣率: [%s]",
                    item.getHspOrgName(), itemPriceBasePackage.getPackageName(), item.getSendType(),
                    DateUtil.formatDate(startDate), DateUtil.formatDate(endDate), discount))
                .toJSONString());
        });

        return Collections.emptyMap();
    }

    /**
     * 客户折扣列表
     */
    @GetMapping("/list")
    public Object list(@RequestParam(value = "hspOrgId", required = false) Long hspOrgId,
        @RequestParam(value = "applyTypeCode", required = false) String applyTypeCode) {

        List<HspOrgDiscountDto> discounts = hspOrgDiscountService.list(hspOrgId, applyTypeCode);

        if (CollectionUtils.isEmpty(discounts)) {
            return List.of();
        }

        final Map<Long, ItemPriceBasePackageDto> packageMap = itemPriceBasePackageService
            .selectByIds(discounts.stream().map(HspOrgDiscountDto::getPackageId).collect(Collectors.toSet())).stream()
            .collect(Collectors.toMap(ItemPriceBasePackageDto::getPackageId, Function.identity(), (a, b) -> a));

        return JSON.parseArray(JSON.toJSONString(discounts), HspOrgDiscountVo.class).stream().peek(c -> {
            final ItemPriceBasePackageDto basePackage = packageMap.get(c.getPackageId());
            if (Objects.nonNull(basePackage)) {
                c.setPackageName(basePackage.getPackageName());
            }
        }).collect(Collectors.toList());
    }

}
