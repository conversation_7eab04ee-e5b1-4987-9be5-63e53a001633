package com.labway.lims.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 仪器质控记录明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Data
public class QcSetRecordItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 质控记录明细ID
     */
    private Long qcRecordItemId;

    /**
     * 记录批次
     */
    private String qcRecordBatch;

    /**
     * 质控批次编号
     */
    private Long qcBatchId;

    /**
     * 仪器质控设置编码
     */
//    private String instrumentQcId;

    /**
     * 专业组id
     */
    private Long groupId;   //1

    /**
     * 专业组编码
     */
    private String groupCode;   //1

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 仪器质控报告项id
     */
    private Long instrumentReportItemId;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 客商仪器编码ID
     */
    private String instrumentId;

    /**
     * 客商仪器名称
     */
    private String instrumentName;

    /**
     * 仪器通道编码
     */
    private String instrumentChannel;

    /**
     * 质控开始日期
     */
    private LocalDateTime qcStartDate;

    /**
     * 质控结束日期
     */
    private LocalDateTime qcEndDate;

    /**
     * 是否有效
     */
    private Boolean isUse;

    /**
     * 质控记录明细状态
     */
    private Integer status;

    /**
     * 质控记录明细状态描述
     */
    private String statusDesc;

    /**
     * 靶值
     */
    private String targetValue;

    /**
     * 标准差
     */
    private String standardDeviation;

    /**
     * 变异系数
     */
    private String cvValue;

    /**
     * 水平编码
     */
    private Integer levelCode;

    /**
     * 水平编码描述
     */
    private String levelCodeDesc;

    /**
     * 质控规则集合
     */
    private String qcRulesCollection;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 上控制线
     */
    private BigDecimal upperControlLimit;
    /**
     * 下控制线
     */
    private BigDecimal lowerControlLimit;
    /**
     * 定性类型
     */
    private Integer qualitativeType;
}
