package com.labway.lims.base.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryCheckSameCombinePackageParam implements Serializable {

    // 检测套餐编码
    Set<String> checkCombinePackageCodes;
    // 检测套餐名称
    Set<String> checkCombinePackageNames;
    // 检测套餐项目
    Set<CombinePackageHspOrgCodeMd5InfoParam> combinePackageHspOrgCodeMd5InfoParams;
    // 忽略的套餐id
    private Set<Long> excludeCombinePackageIds;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CombinePackageHspOrgCodeMd5InfoParam implements Serializable {

        // 送检机构编码
        private String hspOrgCode;

        // MD5值
        private String itemMd5;

    }

}
