package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.model.TbInstrumentReportItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 仪器报告项目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Mapper
public interface TbInstrumentReportItemMapper extends BaseMapper<TbInstrumentReportItem> {

    /**
     * 根据专业小组ID查询
     */
    List<InstrumentReportItemDto> selectByInstrumentGroupId(@Param("instrumentGroupId") long instrumentGroupId);
}
