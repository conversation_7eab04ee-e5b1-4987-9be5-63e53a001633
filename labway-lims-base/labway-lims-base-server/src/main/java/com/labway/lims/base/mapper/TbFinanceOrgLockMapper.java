package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.FinanceOrgLockDto;
import com.labway.lims.base.model.TbFinanceOrgLock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2023/5/11 20:51
 */
@Mapper
public interface TbFinanceOrgLockMapper extends BaseMapper<TbFinanceOrgLock> {
    int addBatch(@Param("orgLocks") Collection<FinanceOrgLockDto> orgLocks);
}
