package com.labway.lims.base.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultTipDto;
import com.labway.lims.base.api.enums.RelationalOperatorEnum;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.InstrumentReportItemResultTipService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.vo.InstrumentReportItemResultTipAddVo;
import com.labway.lims.base.vo.InstrumentReportItemResultTipUpdateVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 结果值提示
 *
 * <AUTHOR>
 * @since 2023/3/20 14:29
 */
@RestController
@RequestMapping("/report-item-result-tip")
public class ReportItemReferenceResultTipController extends BaseController {
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentReportItemResultTipService instrumentReportItemResultTipService;
    @DubboReference
    private InstrumentService instrumentService;
    @Resource
    private HspOrganizationService hspOrganizationService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private RabbitMQService rabbitMQService;

    /**
     * 获取结果值提示
     */
    @GetMapping("/tips")
    public Object tips() {
        final List<InstrumentReportItemResultTipDto> tips = instrumentReportItemResultTipService.selectByOrgId(LoginUserHandler.get().getOrgId());
        final Map<Long, List<InstrumentReportItemDto>> allInstrumentReportItem = instrumentReportItemService.selectByInstrumentIds(tips.stream()
                        .map(InstrumentReportItemResultTipDto::getInstrumentId).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(InstrumentReportItemDto::getInstrumentId));

        return tips.stream().map(e -> {
            final Map<String, InstrumentReportItemDto> items = allInstrumentReportItem.getOrDefault(e.getInstrumentId(), List.of())
                    .stream().collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v -> v, (a, b) -> a));
            final List<InstrumentReportItemDto> instrumentReportItems = Arrays.stream(e.getReportItemCode().split(","))
                    .map(items::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 包含0拼接全部
            if (ArrayUtils.contains(e.getReportItemCode().split(","), "0")) {
                final InstrumentReportItemDto k = new InstrumentReportItemDto();
                k.setInstrumentReportItemId(NumberUtils.LONG_ZERO);
                instrumentReportItems.add(k);
            }

            if (CollectionUtils.isEmpty(instrumentReportItems)) {
                return null;
            }

            final InstrumentReportItemResultTipUpdateVo k = new InstrumentReportItemResultTipUpdateVo();
            k.setInstrumentReportItemResultTipId(e.getInstrumentReportItemResultTipId());
            k.setInstrumentReportItemIds(instrumentReportItems.stream().map(InstrumentReportItemDto::getInstrumentReportItemId)
                    .collect(Collectors.toList()));
            k.setInstrumentId(e.getInstrumentId());
            k.setInstrumentCode(e.getInstrumentCode());
            k.setInstrumentName(e.getInstrumentName());
            k.setReportItemName(e.getReportItemName());
            k.setReportItemCode(e.getReportItemCode());
            k.setTipContent(e.getTipContent());
            k.setTipType(e.getTipType());
            k.setFormulaMax(e.getFormulaMax());
            k.setFormulaMaxValue(e.getFormulaMaxValue());
            k.setFormulaMin(e.getFormulaMin());
            k.setFormulaMinValue(e.getFormulaMinValue());
            k.setHspOrgId(e.getHspOrgId());
            k.setHspOrgName(e.getHspOrgName());
            k.setEnable(e.getEnable());
            return k;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 添加结果值提示
     */
    @PostMapping("/add")
    public Object add(@RequestBody InstrumentReportItemResultTipAddVo vo) {

        if (Objects.isNull(vo.getHspOrgId())) {
            vo.setHspOrgId(NumberUtils.LONG_ZERO);
            vo.setHspOrgName(StringUtils.EMPTY);
        }

        if (CollectionUtils.isEmpty(vo.getInstrumentReportItemIds()) || Objects.isNull(vo.getEnable()) ||
                StringUtils.isAnyBlank(vo.getTipType(), vo.getTipContent(), vo.getFormulaMax(), vo.getFormulaMaxValue())
                || Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalArgumentException("参数错误");
        }
        this.verifyParams(vo);


        final String key = redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":add:" + vo.getInstrumentId();

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalArgumentException("正在添加中");
        }


        final List<InstrumentReportItemDto> instrumentReportItems = new LinkedList<>();
        if (CollectionUtils.containsAny(vo.getInstrumentReportItemIds(), NumberUtils.LONG_ZERO)) {
            final InstrumentReportItemDto dto = new InstrumentReportItemDto();
            dto.setReportItemCode("0");
            dto.setReportItemName("全部");
            instrumentReportItems.add(dto);
        }

        instrumentReportItems.addAll(instrumentReportItemService.selectByInstrumentReportItemIds(vo.getInstrumentReportItemIds()));


        if (CollectionUtils.isEmpty(instrumentReportItems)) {
            throw new IllegalArgumentException("参数错误");
        }

        final InstrumentDto instrument = instrumentService.selectByInstrumentId(vo.getInstrumentId());
        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("仪器不存在");
        }


        final InstrumentReportItemResultTipDto dto = new InstrumentReportItemResultTipDto();
        dto.setInstrumentReportItemId(NumberUtils.LONG_ZERO);
        dto.setReportItemCode(instrumentReportItems.stream().map(InstrumentReportItemDto::getReportItemCode).collect(Collectors.joining(",")));
        dto.setReportItemName(instrumentReportItems.stream().map(InstrumentReportItemDto::getReportItemName).collect(Collectors.joining(",")));
        dto.setInstrumentId(vo.getInstrumentId());
        dto.setInstrumentCode(instrument.getInstrumentCode());
        dto.setInstrumentName(instrument.getInstrumentName());
        dto.setTipContent(vo.getTipContent());
        dto.setTipType(vo.getTipType());
        dto.setFormulaMax(vo.getFormulaMax());
        dto.setFormulaMaxValue(vo.getFormulaMaxValue());
        dto.setFormulaMin(vo.getFormulaMin());
        dto.setFormulaMinValue(vo.getFormulaMinValue());
        dto.setHspOrgId(vo.getHspOrgId());
        dto.setHspOrgName(vo.getHspOrgName());
        dto.setEnable(vo.getEnable());


        try {
            return Map.of("id", instrumentReportItemResultTipService
                    .addInstrumentReportItemResultTip(dto));
        } finally {
            stringRedisTemplate.delete(key);

            String content = String.format("用户 [%s] 添加仪器结果值提醒到仪器[%s]->报告项目[%s] 提醒类型 [%s] " +
                            "提示内容 [%s] 提示公式 [%s] " +
                            "第一个提示公式单位 [%s] 第一个提示公式单位值 [%s] " +
                            "第二个提示公式单位 [%s] 第二个提示公式单位值 [%s] " +
                            "送检机构 [%s] 是否启用 [%s]",
                    LoginUserHandler.get().getNickname(),
                    dto.getInstrumentName(),
                    dto.getReportItemName(),
                    vo.getTipType(),
                    vo.getTipContent(),
                    dto.getReportItemCode(),
                    vo.getFormulaMax(),
                    vo.getFormulaMaxValue(),
                    vo.getFormulaMin(),
                    vo.getFormulaMinValue(),
                    vo.getHspOrgName(),
                    YesOrNoEnum.selectByCode(vo.getEnable()).getDesc()
            );
//            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
//                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_RESULT_VALUE_TIP.getDesc())
//                            .setContent(content).toJSONString());
        }
    }

    /**
     * 修改结果值提示
     */
    @PostMapping("/update")
    public Object update(@RequestBody InstrumentReportItemResultTipUpdateVo vo) {

        if (Objects.isNull(vo.getInstrumentReportItemResultTipId()) || Objects.isNull(vo.getEnable()) ||
                StringUtils.isAnyBlank(vo.getTipType(), vo.getTipContent(), vo.getFormulaMax(), vo.getFormulaMaxValue())) {
            throw new IllegalArgumentException("参数错误");
        }

        this.verifyParams(vo);

        final String key = redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":update:" + vo.getInstrumentId();

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalArgumentException("正在修改中");
        }

        final InstrumentReportItemResultTipDto resultOld = instrumentReportItemResultTipService.selectInstrumentReportItemResultTipId(vo.getInstrumentReportItemResultTipId());
        if (Objects.isNull(resultOld)) {
            throw new IllegalArgumentException("结果提示不存在");
        }

        final List<InstrumentReportItemDto> instrumentReportItems = new LinkedList<>();
        if (CollectionUtils.containsAny(vo.getInstrumentReportItemIds(), NumberUtils.LONG_ZERO)) {
            final InstrumentReportItemDto dto = new InstrumentReportItemDto();
            dto.setReportItemCode("0");
            dto.setReportItemName("全部");
            instrumentReportItems.add(dto);
        }

        instrumentReportItems.addAll(instrumentReportItemService.selectByInstrumentReportItemIds(vo.getInstrumentReportItemIds()));


        final InstrumentReportItemResultTipDto dto = new InstrumentReportItemResultTipDto();
        dto.setInstrumentReportItemResultTipId(vo.getInstrumentReportItemResultTipId());
        dto.setInstrumentReportItemId(NumberUtils.LONG_ZERO);
        dto.setReportItemCode(instrumentReportItems.stream().map(InstrumentReportItemDto::getReportItemCode).collect(Collectors.joining(",")));
        dto.setReportItemName(instrumentReportItems.stream().map(InstrumentReportItemDto::getReportItemName).collect(Collectors.joining(",")));
        dto.setTipContent(vo.getTipContent());
        dto.setTipType(vo.getTipType());
        dto.setFormulaMax(vo.getFormulaMax());
        dto.setFormulaMaxValue(vo.getFormulaMaxValue());
        dto.setFormulaMin(vo.getFormulaMin());
        dto.setFormulaMinValue(vo.getFormulaMinValue());
        dto.setHspOrgId(vo.getHspOrgId());
        dto.setHspOrgName(vo.getHspOrgName());
        dto.setEnable(vo.getEnable());

        try {

            if (!instrumentReportItemResultTipService.updateByInstrumentReportItemResultTipId(dto)) {
                throw new IllegalArgumentException("修改结果提示失败");
            }
        } finally {
            stringRedisTemplate.delete(key);

            final String compare = new CompareUtils<InstrumentReportItemResultTipDto>()
                    .compare(resultOld, dto);
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_RESULT_VALUE_TIP.getDesc())
                            .setContent(compare).toJSONString());
        }

        return Collections.emptyMap();
    }

    private void verifyParams(InstrumentReportItemResultTipAddVo vo) {
        if (!RelationalOperatorEnum.getFormatList().contains(vo.getFormulaMax())) {
            throw new IllegalArgumentException("参数错误");
        }
        // 第一个符号是 > >=
        if ((Objects.equals(vo.getFormulaMax(), RelationalOperatorEnum.GT.getFormula()) || Objects.equals(vo.getFormulaMax(), RelationalOperatorEnum.GE.getFormula()))) {

            if (StringUtils.isNotBlank(vo.getFormulaMin()) && StringUtils.isBlank(vo.getFormulaMinValue())) {
                throw new IllegalArgumentException("第二个范围值没有填写");
            }
            if (StringUtils.isBlank(vo.getFormulaMin()) && StringUtils.isNotBlank(vo.getFormulaMinValue())) {
                throw new IllegalArgumentException("第二个范围符号没有填写");
            }

            // 第二个符号不为空， 并且第二个值不为空
            if (StringUtils.isNotBlank(vo.getFormulaMin())) {
                // 第二个符号必须是 < <=
                if (!Objects.equals(vo.getFormulaMin(), RelationalOperatorEnum.LT.getFormula())
                        && !Objects.equals(vo.getFormulaMin(), RelationalOperatorEnum.LE.getFormula())) {
                    throw new IllegalArgumentException("参数错误");
                }
            }

        }
        // 如果第二个参数不为空  &&  （两个比较值不是数字 || 第一个比第二个大） 则报错
        if (StringUtils.isNotBlank(vo.getFormulaMinValue()) && (!NumberUtils.isParsable(vo.getFormulaMinValue()) || !NumberUtils.isParsable(vo.getFormulaMaxValue())
                || new BigDecimal(vo.getFormulaMaxValue()).compareTo(new BigDecimal(vo.getFormulaMinValue())) > NumberUtils.INTEGER_MINUS_ONE)) {
            throw new IllegalArgumentException("结果值范围错误");
        }
    }

    /**
     * 删除结果值提示
     */
    @DeleteMapping("/delete")
    public Object delete(@RequestBody Set<Long> instrumentReportItemResultTipIds) {


        if (CollectionUtils.isEmpty(instrumentReportItemResultTipIds)) {
            return Collections.emptyMap();
        }

        final Map<Long, InstrumentReportItemResultTipDto> reusltTipMap = instrumentReportItemResultTipService.selectInstrumentReportItemResultTipIds(instrumentReportItemResultTipIds)
                .stream().collect(Collectors.toMap(InstrumentReportItemResultTipDto::getInstrumentReportItemResultTipId
                        , Function.identity(), (a, b) -> a));

        for (Long instrumentReportItemResultTipId : instrumentReportItemResultTipIds) {
            instrumentReportItemResultTipService.deleteByInstrumentReportItemResultTipId(instrumentReportItemResultTipId);

            final InstrumentReportItemResultTipDto dto = reusltTipMap.get(instrumentReportItemResultTipId);
            if (Objects.nonNull(dto)) {

                String content = String.format("用户 [%s] 添加仪器结果值提醒到仪器[%s]->报告项目[%s] 提醒类型 [%s] " +
                                "提示内容 [%s] 提示公式 [%s] " +
                                "第一个提示公式单位 [%s] 第一个提示公式单位值 [%s] " +
                                "第二个提示公式单位 [%s] 第二个提示公式单位值 [%s] " +
                                "送检机构 [%s] 是否启用 [%s]",
                        LoginUserHandler.get().getNickname(),
                        dto.getInstrumentName(),
                        dto.getReportItemName(),
                        dto.getTipType(),
                        dto.getTipContent(),
                        dto.getReportItemCode(),
                        dto.getFormulaMax(),
                        dto.getFormulaMaxValue(),
                        dto.getFormulaMin(),
                        dto.getFormulaMinValue(),
                        dto.getHspOrgName(),
                        YesOrNoEnum.selectByCode(dto.getEnable()).getDesc()
                );
                rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                        TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_RESULT_VALUE_TIP.getDesc())
                                .setContent(content).toJSONString());

            }
        }

        return Collections.emptyMap();
    }


}
