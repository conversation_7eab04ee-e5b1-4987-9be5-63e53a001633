package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.business.center.mdm.api.reagent.param.reagent.ReagentFullVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.ReagentTypeVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.SelectFullReagentListVi;
import com.labway.business.center.mdm.api.reagent.param.reagent.SelectReagentTypeListVi;
import com.labway.business.center.mdm.api.reagent.service.ReagentMaterialService;
import com.labway.business.center.mdm.api.reagent.service.ReagentMaterialTypeService;
import com.labway.business.center.mdm.common.base.MdmResponse;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ReagenEnabledEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.MaterialService;
import com.labway.lims.base.api.service.MaterialSyncFlowService;
import com.labway.lims.base.mapper.TbMaterialMapper;
import com.labway.lims.base.mapstruct.MaterialConverter;
import com.labway.lims.base.model.TbMaterial;
import com.labway.lims.base.api.vo.UpdateExpectedTestCountVo;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/8 17:40
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = "material")
public class MaterialServiceImpl extends ServiceImpl<TbMaterialMapper,TbMaterial> implements MaterialService {
    @Resource
    private TbMaterialMapper tbMaterialMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private MaterialSyncFlowService materialSyncFlowService;
    // @DubboReference(url = "10.11.25.182:20991")
    @Resource
    private ReagentMaterialService reagentMaterialService;
    @Resource
    private MaterialService materialService;
    // @DubboReference(url = "10.11.25.182:20991")
    @Resource
    private ReagentMaterialTypeService reagentMaterialTypeService;
    @Resource
    private MaterialConverter materialConverter;

    @Override
    public List<MaterialDto> selectAll() {
        final LambdaQueryWrapper<TbMaterial> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMaterial::getOrgId, LoginUserHandler.get().getOrgId()).orderByDesc(TbMaterial::getCreateDate);

        return tbMaterialMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    private MaterialDto convert(TbMaterial material) {
        if (Objects.isNull(material)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(material), MaterialDto.class);
    }

    @Override
    public List<MaterialDto> selectByIds(Collection<Long> materialIds) {
        if (CollectionUtils.isEmpty(materialIds)) {
            return Collections.emptyList();
        }

        final List<MaterialDto> list = new LinkedList<>();

        for (List<Long> ids : ListUtils.partition(new LinkedList<>(materialIds), 1000)) {
            list.addAll(tbMaterialMapper.selectBatchIds(ids).stream().map(this::convert).collect(Collectors.toList()));
        }

        return list;
    }

    @Override
    public MaterialDto selectByMaterialId(long materialId) {
        return convert(tbMaterialMapper.selectById(materialId));
    }

    @Override
    public long addMaterial(MaterialDto dto) {
        final TbMaterial material = JSON.parseObject(JSON.toJSONString(dto), TbMaterial.class);
        material.setMaterialId(ObjectUtils.defaultIfNull(dto.getMaterialId(), snowflakeService.genId()));
        material.setOrgId(LoginUserHandler.get().getOrgId());
        material.setOrgName(LoginUserHandler.get().getOrgName());
        material.setCreateDate(new Date());
        material.setCreatorId(LoginUserHandler.get().getUserId());
        material.setCreatorName(LoginUserHandler.get().getNickname());
        material.setUpdateDate(new Date());
        material.setUpdaterName(LoginUserHandler.get().getNickname());
        material.setUpdaterId(LoginUserHandler.get().getUserId());
        material.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbMaterialMapper.insert(material) < 1) {
            throw new IllegalStateException("添加失败");
        }

        log.info("用户 [{}] 新增物料 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(material));
        return material.getMaterialId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncMaterial(IncrementalSyncMaterialDto condition) {

        Date timeStart = condition.getModifiedTimeStart();
        Date timeEnd = condition.getModifiedTimeEnd();

        // 主数据查询物料
        List<ReagentDto> mdmMaterials = selectMdmMaterials(timeStart, timeEnd);

        List<String> fromNoList = mdmMaterials.stream().map(ReagentDto::getFromNo).collect(Collectors.toList());

        final MaterialSyncFlowDto dto = new MaterialSyncFlowDto();
        dto.setEnable(YesOrNoEnum.YES.getCode());
        dto.setModifiedDateStart(timeStart);
        dto.setModifiedDateEnd(timeEnd);
        dto.setStatus(YesOrNoEnum.YES.getCode());

        if (CollectionUtils.isEmpty(fromNoList)) {
            // 本次 增量 同步 无 数据
            dto.setOperationNumber(0);
            dto.setResultMessage("本次增量同步无处理数据");
            log.info("本次增量同步无处理数据");
            materialSyncFlowService.addFlowRecord(dto);
            return;
        }

        // 获取 LIMS 的所有物料
        List<MaterialDto> materialList = materialService.selectAll();

        // 物料 新增项、更新项
        List<MaterialDto> addMaterials = new ArrayList<>();
        List<MaterialDto> updateMaterials = new ArrayList<>();
        List<MaterialDto> deleteMaterials = new ArrayList<>();

        this.addHandleTbMaterialDtoList(mdmMaterials, materialList, addMaterials, updateMaterials, deleteMaterials);

        // 保存数据
        if (CollectionUtils.isNotEmpty(addMaterials)) {
            materialService.addMaterials(addMaterials);
        }
        // 修改数据
        if (CollectionUtils.isNotEmpty(updateMaterials)) {
            materialService.updateMaterialsByInfoHash(updateMaterials);
        }
        // 删除的物料
        if (CollectionUtils.isNotEmpty(deleteMaterials)) {
            materialService.deleteByMaterialIds(
                deleteMaterials.stream().map(MaterialDto::getMaterialId).collect(Collectors.toList()));
        }

        dto.setOperationNumber(fromNoList.size());
        String msg = String.format("本次增量同步共处理 [%s] 条数据,其中新增 [%s] 条数据，更新 [%s] 条数据,删除数据 [%s]", fromNoList.size(),
            addMaterials.size(), updateMaterials.size(), deleteMaterials.size());
        dto.setResultMessage(msg);
        materialSyncFlowService.addFlowRecord(dto);
    }

    @Override
    public void updateMaterialsByInfoHash(List<MaterialDto> materials) {
        if (CollectionUtils.isEmpty(materials)) {
            return;
        }
        List<List<MaterialDto>> partitionList = ListUtils.partition(materials, 1000);
        partitionList.forEach(item -> tbMaterialMapper.updateMaterialsByInfoHash(item));

    }

    @Override
    public void addMaterials(List<MaterialDto> materials) {
        if (CollectionUtils.isEmpty(materials)) {
            return;
        }

        final LinkedList<Long> ids = snowflakeService.genIds(materials.size());
        for (MaterialDto material : materials) {
            material.setMaterialId(ids.pop());
            material.setOrgId(LoginUserHandler.get().getOrgId());
            material.setOrgName(LoginUserHandler.get().getOrgName());
            material.setCreateDate(new Date());
            material.setCreatorId(LoginUserHandler.get().getUserId());
            material.setCreatorName(LoginUserHandler.get().getNickname());
            material.setUpdateDate(new Date());
            material.setUpdaterName(LoginUserHandler.get().getNickname());
            material.setUpdaterId(LoginUserHandler.get().getUserId());
            material.setIsDelete(YesOrNoEnum.NO.getCode());
        }
        // 数量 分区批次插入
        List<List<MaterialDto>> partitionList = ListUtils.partition(materials, 500);
        partitionList.forEach(item -> tbMaterialMapper.addBatch(item));
    }

    @Override
    public List<MaterialTypeDto> getMaterialTypes() {

        final SelectReagentTypeListVi vi = new SelectReagentTypeListVi();
        // 启用状态:1未启用，2已启用，3已停用
        vi.setPages(1);
        vi.setSize(Integer.MAX_VALUE);
        final Response<List<ReagentTypeVo>> listResponse = reagentMaterialTypeService.selectReagentTypeList(vi);
        return listResponse.getData().stream().map(e -> {
            final MaterialTypeDto dto = new MaterialTypeDto();
            dto.setType(e.getReagentType());
            dto.setTypeCode(e.getReagentTypeCode());
            return dto;
        }).collect(Collectors.toList());

    }

    @Override
    public List<MaterialDto> selectByOrgIdAndMaterialCodes(long orgId, Collection<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMaterial> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMaterial::getOrgId, orgId);
        queryWrapper.in(TbMaterial::getMaterialCode, materialCodes);
        queryWrapper.eq(TbMaterial::getIsDelete, YesOrNoEnum.NO.getCode());
        return materialConverter.materialDtoListFromTbObj(tbMaterialMapper.selectList(queryWrapper));
    }

    @Override
    public List<MaterialDto> selectByGroupIdAndMaterialCodes( Collection<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMaterial> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMaterial::getMaterialCode, materialCodes);
        queryWrapper.eq(TbMaterial::getIsDelete, YesOrNoEnum.NO.getCode());
        return materialConverter.materialDtoListFromTbObj(tbMaterialMapper.selectList(queryWrapper));
    }

    @Override
    public MaterialPageInfoDto selectByMaterialNameOrName(MaterialQueryDto dto) {

        final LambdaQueryWrapper<TbMaterial> wrapper = new LambdaQueryWrapper<>();

        wrapper.like(StringUtils.isNotBlank(dto.getMaterialName()), TbMaterial::getMaterialName, dto.getMaterialName())
            .eq(StringUtils.isNotBlank(dto.getTypeCode()), TbMaterial::getTypeCode, dto.getTypeCode())
            .eq(TbMaterial::getOrgId, LoginUserHandler.get().getOrgId());
        final Page<TbMaterial> page = new Page<>();
        page.setCurrent(dto.getCurrent());
        page.setSize(dto.getSize());
        final Page<TbMaterial> data = tbMaterialMapper.selectPage(page, wrapper);

        final MaterialPageInfoDto mPages = new MaterialPageInfoDto();
        mPages.setCurrent(data.getCurrent());
        mPages.setTotal(data.getTotal());
        mPages.setSize(data.getSize());
        mPages.setMaterials(materialConverter.materialDtoListFromTbObj(data.getRecords()));

        return mPages;
    }

    @Override
    public List<MaterialDto> selectByMaterialIds(Collection<Long> materialIds) {
        LambdaQueryWrapper<TbMaterial> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMaterial::getMaterialId, materialIds);
        return materialConverter.materialDtoListFromTbObj(tbMaterialMapper.selectList(queryWrapper));
    }

    @Override
    public int deleteByMaterialIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return tbMaterialMapper.deleteBatchIds(ids);
    }



    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExpectedTestCount(List<UpdateExpectedTestCountVo> materialList) {

        List<TbMaterial> collect = materialList.stream().map(e -> {
                    TbMaterial tbMaterial = new TbMaterial();
                    tbMaterial.setMaterialId(e.getMaterialId());
                    tbMaterial.setExpectedTestCount(e.getExpectedTestCount());
                    tbMaterial.setUpdateDate(new Date());
                    tbMaterial.setUpdaterId(LoginUserHandler.get().getUserId());
                    tbMaterial.setUpdaterName(LoginUserHandler.get().getNickname());
                    return tbMaterial;
                })
                .collect(Collectors.toList());
        return super.updateBatchById(collect);
    }

    private List<ReagentDto> selectMdmMaterials(Date timeStart, Date timeEnd) {

        final SelectFullReagentListVi vo = new SelectFullReagentListVi();
        vo.setUpdateTimeStart(DateFormatUtils.format(timeStart, "yyyy-MM-dd HH:mm:ss"));
        vo.setUpdateTimeEnd(DateFormatUtils.format(timeEnd, "yyyy-MM-dd HH:mm:ss"));
        vo.setCreationAndModified(Boolean.TRUE);
        vo.setCurrent(1);
        vo.setSize(1000);
        // 先查询总页数
        ;
        final MdmResponse<List<ReagentFullVo>> listResponse =
            JSONObject.parseObject(JSON.toJSONString(reagentMaterialService.selectAllReagentList(vo)),
                new TypeReference<MdmResponse<List<ReagentFullVo>>>() {});

        final List<ReagentDto> reagents = new ArrayList<>();
        int page = (int)(listResponse.getTotal() / 1000);
        // 分页每页大小1000查询
        for (int i = 1; i <= page + 1; i++) {
            final SelectFullReagentListVi selectReagentListVi = new SelectFullReagentListVi();
            selectReagentListVi.setUpdateTimeStart(DateFormatUtils.format(timeStart, "yyyy-MM-dd HH:mm:ss"));
            selectReagentListVi.setUpdateTimeEnd(DateFormatUtils.format(timeEnd, "yyyy-MM-dd HH:mm:ss"));
            selectReagentListVi.setCreationAndModified(Boolean.TRUE);
            selectReagentListVi.setCurrent(i);
            selectReagentListVi.setSize(1000);
            final MdmResponse<List<ReagentFullVo>> res = JSONObject.parseObject(
                JSON.toJSONString(reagentMaterialService.selectAllReagentList(selectReagentListVi)),
                new TypeReference<MdmResponse<List<ReagentFullVo>>>() {});
            reagents.addAll(JSON.parseArray(JSON.toJSONString(res.getData()), ReagentDto.class));
        }
        return reagents;

    }

    @Nonnull
    private MaterialDto convertToLIMSMaterial(ReagentDto reagent) {

        final MaterialDto dto = new MaterialDto();
        dto.setMaterialCode(reagent.getNcReagentNo());
        dto.setType(reagent.getReagentType());
        dto.setTypeCode(reagent.getReagentTypeCode());
        dto.setMaterialName(reagent.getReagentName());
        dto.setSpecification(reagent.getSpecification());
        dto.setMethodology(reagent.getDef11());
        dto.setRegistrationNumber(reagent.getDef7());
        dto.setRegistrationName(reagent.getDef10());
        dto.setStorageTemperature(reagent.getDef6());
        dto.setManufacturers(reagent.getDef20());
        dto.setMainUnitInventory(BigDecimal.ZERO);
        dto.setMainUnit(reagent.getPrimaryUnit());
        final Optional<ReagentDto.ReagentAssistDto> assistDto = reagent.getReagentAssistVoList().stream().findFirst();
        dto.setAssistUnit(assistDto.map(ReagentDto.ReagentAssistDto::getUnitName).orElse(""));
        dto.setAssistUintInventory(new BigDecimal(0));
        dto.setUnitConversionRate(assistDto.map(ReagentDto.ReagentAssistDto::getUnitTransRate).orElse(""));
        dto.setValidDate(new Date());
        dto.setOpenValidDate(new Date());
        dto.setOrgId(LoginUserHandler.get().getOrgId());
        dto.setOrgName(LoginUserHandler.get().getOrgName());
        dto.setValidRemindDay(0);
        dto.setFromNo(reagent.getFromNo());
        dto.setInfoHash(reagent.getInfoHash());
        dto.setIsDelete(
            Boolean.TRUE.equals(reagent.getEnabled()) ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());

        return dto;
    }

    private void addHandleTbMaterialDtoList(List<ReagentDto> reagentDtos, List<MaterialDto> materialList,
        List<MaterialDto> addMaterialDtoList, List<MaterialDto> updateMaterialDtoList,
        List<MaterialDto> deleteMaterialDtoList) {

        Map<String, MaterialDto> toMapByFromNo =
            materialList.stream().collect(Collectors.toMap(MaterialDto::getFromNo, v -> v, (a, b) -> a));

        for (ReagentDto dto : reagentDtos) {
            String fromNo = dto.getFromNo();

            MaterialDto tbMaterialDto = toMapByFromNo.get(fromNo);

            final MaterialDto materialDto = convertToLIMSMaterial(dto);

            if (Objects.isNull(tbMaterialDto) && Objects.equals(dto.getEnabled(), ReagenEnabledEnum.VALID.getCode())) {
                // 新增项目
                addMaterialDtoList.add(materialDto);
                continue;
            }

            if (Objects.nonNull(tbMaterialDto) && !Objects.equals(dto.getInfoHash(), tbMaterialDto.getInfoHash())
                && Objects.equals(dto.getEnabled(), ReagenEnabledEnum.VALID.getCode())) {
                // 修改项目
                updateMaterialDtoList.add(materialDto);
            }

            if (Objects.nonNull(tbMaterialDto)
                && Objects.equals(dto.getEnabled(), ReagenEnabledEnum.INVALID.getCode())) {
                // 删除项目
                deleteMaterialDtoList.add(tbMaterialDto);
            }

        }
    }

}
