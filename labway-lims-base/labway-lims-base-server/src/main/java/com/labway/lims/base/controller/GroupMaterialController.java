package com.labway.lims.base.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.GroupMaterialService;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.MaterialService;
import com.labway.lims.base.vo.GroupMaterialAddVo;
import com.labway.lims.base.api.vo.GroupMaterialPageVo;
import com.labway.lims.base.vo.GroupMaterialSetValidRemindDayRequestVo;
import com.labway.lims.base.vo.MaterialVo;
import com.labway.lims.base.vo.excel.GroupMaterialSheet;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 专业组物料信息
 *
 * <AUTHOR>
 * @since 2023/5/8 17:13
 */
@RestController
@RequestMapping("/group-material")
public class GroupMaterialController extends BaseController {
    @Resource
    private GroupMaterialService groupMaterialService;
    @Resource
    private MaterialService materialService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 专业组物料信息
     */
    @GetMapping("/list")
    public Object groupMaterial(@RequestParam("groupId") Long groupId,
        @RequestParam(value = "materialName", required = false) String materialName) {

        if (Objects.isNull(groupId)) {
            return Collections.emptyList();
        }

        List<GroupMaterialDto> groupMaterials = groupMaterialService.selectByGroupId(groupId);

        final Set<Long> materialIds =
            groupMaterials.stream().map(GroupMaterialDto::getMaterialId).collect(Collectors.toSet());

        List<MaterialDto> materials = materialService.selectByIds(materialIds);

        if (StringUtils.isNotBlank(materialName)) {
            materials = materials.stream().filter(e -> Objects.equals(e.getMaterialName(), materialName))
                .collect(Collectors.toList());
        }

        return JSON.parseArray(JSON.toJSONString(materials), MaterialVo.class);
    }

    /**
     * 添加物料（批量）
     */
    @PostMapping("/add-group-materials")
    public Object addGroupMaterials(@RequestBody GroupMaterialAddVo vo) {

        if (Objects.isNull(vo.getGroupId())) {
            throw new IllegalArgumentException("参数错误");
        }

        final ProfessionalGroupDto group = groupService.selectByGroupId(vo.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException("专业组不存在,添加失败");
        }

        // 添加专业组物料
        if (Objects.nonNull(vo.getMaterialIds()) && CollectionUtils.isNotEmpty(vo.getMaterialIds())) {
            final List<MaterialDto> materials = materialService.selectByMaterialIds(vo.getMaterialIds());
            final LinkedList<Long> ids = snowflakeService.genIds(materials.size());
            final LinkedList<GroupMaterialDto> gms = new LinkedList<>();
            for (MaterialDto material : materials) {
                final GroupMaterialDto gm = new GroupMaterialDto();
                gm.setGroupMaterialId(ids.pop());
                gm.setMaterialId(material.getMaterialId());
                gm.setMainUnitInventory(material.getMainUnitInventory());
                gm.setAssistUnitInventory(material.getAssistUintInventory());
                gm.setValidRemindDay(0);
                gm.setGroupId(vo.getGroupId());
                gm.setGroupName(group.getGroupName());
                gm.setOrgId(LoginUserHandler.get().getOrgId());
                gm.setOrgName(LoginUserHandler.get().getOrgName());
                gm.setCreateDate(new Date());
                gm.setCreatorId(LoginUserHandler.get().getUserId());
                gm.setCreatorName(LoginUserHandler.get().getNickname());
                gm.setUpdateDate(new Date());
                gm.setUpdaterName(LoginUserHandler.get().getNickname());
                gm.setUpdaterId(LoginUserHandler.get().getUserId());
                gm.setIsDelete(YesOrNoEnum.NO.getCode());
                gms.add(gm);
            }
            groupMaterialService.addGroupMaterials(gms);

            materials.forEach(item -> {
                // 记录操作日志
                rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.GROUP_MATERIAL.getDesc())
                        .setContent(String.format("专业组 [%s] 新增 [%s] 物料", group.getGroupName(), item.getMaterialName()))
                        .toJSONString());
            });
        }

        return Collections.emptyMap();
    }

    /**
     * 删除专业组物料
     */
    @PostMapping("/delete-group-materials")
    public Object deleteGroupMaterials(@RequestBody GroupMaterialAddVo vo) {

        if (Objects.isNull(vo.getGroupId())) {
            throw new IllegalArgumentException("参数错误");
        }
        final ProfessionalGroupDto group = groupService.selectByGroupId(vo.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException("专业组不存在,添加失败");
        }

        if (CollectionUtils.isEmpty(vo.getMaterialIds())) {
            return Collections.emptyMap();
        }

        groupMaterialService.deleteGroupMaterials(vo.getMaterialIds(), vo.getGroupId());

        List<MaterialDto> materials = materialService.selectByMaterialIds(vo.getMaterialIds());

        materials.forEach(item -> {
            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.GROUP_MATERIAL.getDesc())
                    .setContent(String.format("专业组 [%s] 删除关联物料 [%s]", group.getGroupName(), item.getMaterialName()))
                    .toJSONString());
        });

        return Collections.emptyMap();
    }

    /**
     * 设置专业组物料 效期 提醒
     */
    @PostMapping("/set-valid-remind-day")
    public Object groupMaterialSetValidRemindDay(@RequestBody GroupMaterialSetValidRemindDayRequestVo vo) {
        if (CollectionUtils.isEmpty(vo.getMaterialIds()) || Objects.isNull(vo.getValidRemindDay())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        LoginUserHandler.User user = LoginUserHandler.get();

        // 专业组物料信息
        List<GroupMaterialDto> groupMaterialDtos =
            groupMaterialService.selectByGroupIdAndMaterialIds(user.getGroupId(), vo.getMaterialIds());
        Set<Long> selectMaterialIs =
            groupMaterialDtos.stream().map(GroupMaterialDto::getMaterialId).collect(Collectors.toSet());

        Set<Long> groupMaterialIds =
            groupMaterialDtos.stream().map(GroupMaterialDto::getGroupMaterialId).collect(Collectors.toSet());
        if (vo.getMaterialIds().stream().anyMatch(x -> !selectMaterialIs.contains(x))) {
            throw new LimsException("选择物料存在不在用户专业组下");
        }

        GroupMaterialDto update = new GroupMaterialDto();
        update.setValidRemindDay(vo.getValidRemindDay());
        groupMaterialService.updateByGroupMaterialIds(update, groupMaterialIds);

        return Collections.emptyMap();
    }

    /**
     * 专业组物料维护导入
     */
    @PostMapping("import")
    public Object importMaterial(@RequestParam("file") MultipartFile file) {
        if (Objects.isNull(file) || file.isEmpty()) {
            throw new IllegalStateException("文件信息为空");
        }

        String fileName = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(fileName);
        if (!"xls".equalsIgnoreCase(extension) && !"xlsx".equalsIgnoreCase(extension)) {
            throw new LimsException("只允许上传 xls 和 xlsx 格式的文件");
        }

        if (!Objects.requireNonNull(file.getOriginalFilename(), "文件名为空").endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            throw new IllegalStateException("文件格式不正确");
        }

        return Map.of("importCount", groupMaterialService.importGroupMaterial(analyzeExcelData(file)));
    }

    public List<GroupMaterialSheetDto> analyzeExcelData(MultipartFile file) {
        try {
            List<GroupMaterialSheetDto> sheetDtos = new ArrayList<>();
            Map<String, AtomicInteger> sheetIndexMap = new HashMap<>();
            Map<Integer, Set<String>> existingMaterialCodeMap = new HashMap<>();

            final ExcelReader build =
                    EasyExcelFactory
                            .read(file.getInputStream())
                            .autoCloseStream(true)
                            .head(GroupMaterialSheet.class)
                            .registerReadListener(new ReadListener<GroupMaterialSheet>() {
                                @Override
                                public void invoke(GroupMaterialSheet data, AnalysisContext context) {
                                    // 获取当前的sheet
                                    String sheetName = context.readSheetHolder().getSheetName();
                                    Integer sheetNo = context.readSheetHolder().getSheetNo();
                                    System.out.println(sheetName);
                                    int index = sheetIndexMap.computeIfAbsent(sheetName, k -> new AtomicInteger(1)).incrementAndGet();

                                    // 读取表格中的物料信息，判断物料信息是否存在
                                    String materialCode = data.getMaterialCode();
                                    String materialName = data.getMaterialName();

                                    if (existingMaterialCodeMap.computeIfAbsent(sheetNo, k -> new HashSet<>()).add(materialCode)) {
                                        GroupMaterialSheetDto materialSheetDto = new GroupMaterialSheetDto();
                                        materialSheetDto.setIndex(index);
                                        materialSheetDto.setSheetNo(sheetNo + 1);
                                        materialSheetDto.setSheetName(sheetName);
                                        materialSheetDto.setMaterialCode(materialCode);
                                        materialSheetDto.setMaterialName(materialName);
                                        sheetDtos.add(materialSheetDto);
                                    }
                                }

                                @Override
                                public void doAfterAllAnalysed(AnalysisContext context) {

                                }
                            }).build();

            build.readAll();
            build.finish();

            return sheetDtos;
        } catch (IOException e) {
            throw new IllegalStateException("读取文件异常");
        }
    }



    /**
     * 专业组物料信息-分页
     */
    @PostMapping("/groupMaterialPage")
    public GroupMaterialPageDto groupMaterialPage(@RequestBody @Valid GroupMaterialPageVo groupMaterialPageVo) {
        return groupMaterialService.groupMaterialPage(groupMaterialPageVo);
    }


}
