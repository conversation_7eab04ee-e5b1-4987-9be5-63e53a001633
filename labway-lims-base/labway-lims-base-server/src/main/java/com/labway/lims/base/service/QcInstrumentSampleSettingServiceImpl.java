package com.labway.lims.base.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.base.api.dto.QcInstrumentReportItemSampleNoDto;
import com.labway.lims.base.api.dto.QcInstrumentSampleSettingDto;
import com.labway.lims.base.api.service.QcInstrumentSampleSettingService;
import com.labway.lims.base.mapper.TbQcInstrumentSampleSettingMapper;
import com.labway.lims.base.model.TbQcInstrumentSampleSetting;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/11/1 10:50
 * @Version 1.0
 */
@Slf4j
@DubboService
public class QcInstrumentSampleSettingServiceImpl implements QcInstrumentSampleSettingService {
    @Resource
    private TbQcInstrumentSampleSettingMapper sampleSettingMapper;

    @Override
    public List<QcInstrumentSampleSettingDto> queryQcInstrumentSampleSetting(QcInstrumentReportItemSampleNoDto dto) {

        LambdaQueryWrapper<TbQcInstrumentSampleSetting> queryWrapper = new LambdaQueryWrapper<>();
        //TODO 此处在迁移时, 把根据客商id转换成了专业组id 已完成，用于标记
        //根据报告项id和专业组id获取质控样本设置信息
        LoginUserHandler.User user = LoginUserHandler.get();
        queryWrapper.eq(TbQcInstrumentSampleSetting::getReportItemId, dto.getReportItemId())
                .eq(TbQcInstrumentSampleSetting::getGroupId, user.getGroupId());                 //专业组id
        List<TbQcInstrumentSampleSetting> list = sampleSettingMapper.selectList(queryWrapper);

        //返回空集合
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        //数据类型转换
        return list.stream().map(this::convertQcSampleSettingDto).collect(Collectors.toList());
    }

    @Override
    public void deleteSettingByInstrumentId(Long instrumentId) {

    }

    @Override
    public void insertSampleSetting(QcInstrumentSampleSettingDto insertItems) {
        TbQcInstrumentSampleSetting sampleSetting = TbQcInstrumentSampleSetting.builder().build();
        BeanUtils.copyProperties(insertItems, sampleSetting);
        sampleSettingMapper.insert(sampleSetting);
    }

    @Override
    public List<QcInstrumentSampleSettingDto> querySettingListByInstrumentId(Long instrumentId) {
        return null;
    }

    @Override
    public List<QcInstrumentSampleSettingDto> queryRulesByInstrumentId(Long instrumentId, Long reportId) {
        return null;
    }

    //类型转换
    private QcInstrumentSampleSettingDto convertQcSampleSettingDto(TbQcInstrumentSampleSetting pojo) {
        QcInstrumentSampleSettingDto dto = new QcInstrumentSampleSettingDto();
        BeanUtils.copyProperties(pojo, dto);
        return dto;
    }
}
