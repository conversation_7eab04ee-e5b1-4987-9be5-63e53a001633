package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.model.TbUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbUserMapper extends BaseMapper<TbUser> {

    /**
     * 根据角色id查找
     */
    List<TbUser> selectByRoleId(@Param("roleId") long roleId);

    /**
     * 根据专业组id查找 用户 包含没有任何专业组关联的用户(这种用户说明具有所有专业组权限)
     */
    List<TbUser> selectByGroupIdAndOrgId(@Param("groupId") long groupId, @Param("orgId") long orgId);

    /**
     *  根据专业组id查找 用户昵称 包含没有任何专业组关联的用户(这种用户说明具有所有专业组权限)，停用和未停用的都要包含
     * @param groupId
     * @return
     */
    List<String> selectByGroupId(@Param("groupId") Long groupId,@Param("orgId") Long orgId);
}
