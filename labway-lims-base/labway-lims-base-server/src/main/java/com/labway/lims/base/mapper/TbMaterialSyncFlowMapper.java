package com.labway.lims.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.base.api.dto.MaterialSyncFlowDto;
import com.labway.lims.base.model.TbMaterialSyncFlow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 物料同步流水表 mapper
 * 
 * <AUTHOR>
 * @since 2023/3/14 15:07
 */
@Mapper
public interface TbMaterialSyncFlowMapper extends BaseMapper<TbMaterialSyncFlow> {
    /**
     * 获取 最近一条成功的流水记录
     */

    MaterialSyncFlowDto recentFlowRecord();

    /**
     * 保存 流水记录
     *
     * @param condition 参数
     */
    void saveFlowRecord(MaterialSyncFlowDto condition);

    /**
     * 获取 失败物料流水记录
     *
     * @param afterTime 此时间之后
     * @return list
     */
    List<MaterialSyncFlowDto> recentFailureFlowRecord(@Param("afterTime") Date afterTime);

    /**
     * 更新 流水记录
     *
     * @param condition 参数
     */
    void updateFlowRecord(MaterialSyncFlowDto condition);
}
