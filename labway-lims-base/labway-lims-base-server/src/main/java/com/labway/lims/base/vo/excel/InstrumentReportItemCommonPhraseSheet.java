package com.labway.lims.base.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

/**
 * 仪器报告项目常用短语 sheet
 */
@Setter
@Getter
public class InstrumentReportItemCommonPhraseSheet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器编码
     */
    @ExcelProperty(index = 0)
    private String instrumentCode;

    /**
     * 报告项目编码
     */
    @ExcelProperty(index = 1)
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    @ExcelProperty(index = 2)
    private String reportItemName;

    /**
     * 快捷键
     */
    @ExcelProperty(index = 3)
    private String keyShort;

    /**
     * 排序号
     */
    @ExcelProperty(index = 4)
    private Integer sort;

    /**
     * 短语内容
     */
    @ExcelProperty(index = 5)
    private String content;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InstrumentReportItemCommonPhraseSheet that = (InstrumentReportItemCommonPhraseSheet) o;
        return Objects.equals(instrumentCode, that.instrumentCode) && Objects.equals(reportItemCode, that.reportItemCode) && Objects.equals(keyShort, that.keyShort);
    }

    @Override
    public int hashCode() {
        return Objects.hash(instrumentCode, reportItemCode, keyShort);
    }
}
