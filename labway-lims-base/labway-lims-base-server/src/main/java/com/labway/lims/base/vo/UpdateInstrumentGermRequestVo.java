package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 仪器细菌更新 vo
 *
 * <AUTHOR>
 * @since 2023/7/12 20:03
 */
@Getter
@Setter
public class UpdateInstrumentGermRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器id
     */
    private Long instrumentId;

    /**
     * 细菌id
     */
    private Long germId;

    /**
     * 仪器通道号
     */
    private String instrumentChannel;
}
