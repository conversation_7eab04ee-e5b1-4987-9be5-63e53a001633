package com.labway.lims.base.model;

import org.junit.Test;

import java.math.BigDecimal;
import java.util.Date;

public class TbTestItemTest {
    @Test
    public void test() {
        final TbTestItem testItem = new TbTestItem();
        testItem.setTestItemId(0L);
        testItem.setTestItemName("");
        testItem.setTestItemCode("");
        testItem.setEnName("");
        testItem.setAliasName("");
        testItem.setExamMethodCode("");
        testItem.setExamMethodName("");
        testItem.setShortName("");
        testItem.setTubeCode("");
        testItem.setTubeName("");
        testItem.setGroupId(0L);
        testItem.setGroupName("");
        testItem.setEnableExport(0);
        testItem.setExportOrgId(0L);
        testItem.setExportOrgName("");
        testItem.setItemType("");
        testItem.setEnableIt3000(0);
        testItem.setSampleTypeCode("");
        testItem.setSampleTypeName("");
        testItem.setEnableFee(0);
        testItem.setFeeCode("");
        testItem.setFeeName("");
        testItem.setFeePrice(new BigDecimal("0"));
        testItem.setFinanceGroupCode("");
        testItem.setFinanceGroupName("");
        testItem.setBasicQuantity(new BigDecimal("0"));
        testItem.setCheckQuantity(new BigDecimal("0"));
        testItem.setDeadSpaceQuantity(new BigDecimal("0"));
        testItem.setStashRemark("");
        testItem.setCreateDate(new Date());
        testItem.setUpdateDate(new Date());
        testItem.setCreatorId(0L);
        testItem.setCreatorName("");
        testItem.setUpdaterId(0L);
        testItem.setUpdaterName("");
        testItem.setOrgId(0L);
        testItem.setOrgName("");
        testItem.setIsDelete(0);


    }
}