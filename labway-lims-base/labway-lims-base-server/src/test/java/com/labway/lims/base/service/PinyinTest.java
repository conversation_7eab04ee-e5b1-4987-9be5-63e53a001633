package com.labway.lims.base.service;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;

import java.nio.charset.StandardCharsets;
import java.util.List;

public class PinyinTest {
    @Test
    public void test() throws Exception {
        final ClassPathResource resource = new ClassPathResource("cncity_fulllist.txt");
        Assert.assertTrue(resource.exists());
        final List<String> lines = IOUtils.readLines(resource.getInputStream(), StandardCharsets.UTF_8);
        for (int i = 0; i < 100; i++) {
            System.out.println(lines.get(RandomUtils.nextInt(0, lines.size())).split(StringUtils.SPACE)[1]);
        }
    }
}
