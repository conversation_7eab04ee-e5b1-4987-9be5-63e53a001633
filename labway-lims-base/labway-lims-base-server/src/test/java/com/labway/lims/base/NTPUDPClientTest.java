package com.labway.lims.base;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.net.ntp.NTPUDPClient;
import org.apache.commons.net.ntp.TimeInfo;
import org.junit.Test;

import java.net.InetAddress;

public class NTPUDPClientTest {
    @Test
    public void test() throws Exception {
        final NTPUDPClient client = new NTPUDPClient();
        client.setDefaultTimeout(5000);
        final TimeInfo time = client.getTime(InetAddress.getByName("time1.aliyun.com"));
        System.out.println(DateFormatUtils.format(time.getMessage().getTransmitTimeStamp()
                .getDate(), "yyyy-MM-dd HH:mm:ss"));

    }
}
