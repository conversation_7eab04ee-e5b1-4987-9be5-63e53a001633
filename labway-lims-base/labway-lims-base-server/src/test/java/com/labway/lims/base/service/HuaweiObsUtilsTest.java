package com.labway.lims.base.service;

import com.labway.lims.api.HuaweiObsUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.Duration;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class HuaweiObsUtilsTest {


    @Resource
    private HuaweiObsUtils huaweiObsUtils;

    @Test
    public void testEmpty() {
        huaweiObsUtils.createPostSignature(huaweiObsUtils.genObjectKey(), Duration.ofDays(1));
    }


}
