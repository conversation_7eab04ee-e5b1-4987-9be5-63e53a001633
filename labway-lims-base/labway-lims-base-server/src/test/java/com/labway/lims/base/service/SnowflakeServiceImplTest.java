package com.labway.lims.base.service;


import com.labway.lims.api.service.SnowflakeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.LinkedList;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class SnowflakeServiceImplTest {

    @Resource
    private SnowflakeService snowflakeService;

    @Test
    public void test() {
//        final SnowflakeServiceImpl.Snowflake snowflake = new SnowflakeServiceImpl.Snowflake(1, 1);
//        System.out.println(snowflake.nextId());
//        System.out.println(DateFormatUtils.format(new Date(snowflake.getGenerateDateTime(snowflake.nextId())), "yyyy-MM-dd HH:mm:ss"));
    }

    @Test
    public void test_genIds() {
        LinkedList<Long> ids = snowflakeService.genIds(25);

        log.info("ids: {}", ids);

        assert ids.size() == 25;

    }
}
