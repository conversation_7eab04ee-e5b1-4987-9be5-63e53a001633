package com.labway.lims.base.service;

import com.labway.lims.base.api.service.ReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;
import java.util.Set;

@Slf4j
@SpringBootTest
class ReportItemServiceImplTest {

    @DubboReference
    private ReportItemService reportItemService;

    @Test
    void selectReportItemCountByTestItemIds() {
        Map<Long, Integer> selectReportItemCountByTestItemIds =
            reportItemService.selectReportItemCountByTestItemIds(Set.of(1L));
        System.out.println(selectReportItemCountByTestItemIds);
    }
}