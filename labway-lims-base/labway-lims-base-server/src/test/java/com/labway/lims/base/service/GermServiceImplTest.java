package com.labway.lims.base.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.base.api.dto.GermDto;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.base.mapper.TbDictItemMapper;
import com.labway.lims.base.model.TbDictItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Collections;

@Slf4j
@SpringBootTest
class GermServiceImplTest {

    @DubboReference
    private GermService germService;

    @Resource
    private TbDictItemMapper tbDictItemMapper;

    @Test
    public void testEmpty(){
        final LambdaQueryWrapper<TbDictItem> in = Wrappers.lambdaQuery(TbDictItem.class)
                .in(TbDictItem::getOrgId, Collections.emptyList());
        tbDictItemMapper.selectList(in);
    }
    @Test
    void selectByGermName() {
        GermDto germDto = germService.selectByGermName("sd", 1);
        System.out.println(germDto);
    }
}
