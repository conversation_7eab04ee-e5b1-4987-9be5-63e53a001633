package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.MenuDto;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.List;

public interface MenuService {
    /**
     * 根据菜单id查询菜单
     *
     * @param menuId menuId
     * @return 菜单
     */
    @Nullable
    MenuDto selectByMenuId(long menuId);

    /**
     * 添加菜单
     *
     * @param dto MenuDto
     */
    long addMenu(MenuDto dto);

    /**
     * 更新菜单
     *
     * @param dto MenuDto
     */
    boolean updateByMenuId(MenuDto dto);

    /**
     * 根据menuId删除菜单
     *
     * @param menuId menuId
     */
    boolean deleteByMenuId(long menuId);

    /**
     * 查询当前角色下的菜单
     *
     * @param roleId roleId
     * @return 回该角色下的所有菜单
     */
    @Nonnull
    List<MenuDto> selectByRoleId(long roleId);

    /**
     * 查询当前角色下的菜单
     *
     * @param roleId roleId
     * @return 以树型结构返回该角色下的所有菜单
     */
    @Nonnull
    List<MenuDto> selectTreeMenuByRoleId(long roleId);

    /**
     * @return 以树型结构返回所有菜单
     */
    @Nonnull
    List<MenuDto> selectAllTreeMenu(long orgId);

    /**
     * 查询当前角色下的菜单
     *
     * @return 以树型结构返回该角色下的所有菜单
     */
    @Nonnull
    List<MenuDto> selectAllMenu(long orgId);

    /**
     * 查询当前 menuId 下的所有菜单(包括按钮)
     *
     * @param menuId menuId
     * @return List<MenuDto>
     */
    @Nonnull
    List<MenuDto> selectByProMenuId(Long menuId);

    /**
     * 查询当前 menuId 下的所有菜单(包括按钮)
     *
     * @param menuId menuId
     * @return List<MenuDto>
     */
    @Nonnull
    List<MenuDto> selectByProMenuId(long orgId, Long menuId);

    /**
     * 根据父ID删除
     */
    boolean deleteByProMenuId(long id);


    /**
     * 根据按钮code获取拥有按钮权限的用户
     */
    MenuDto selectByButtonMenuCode(String menuCode);

    /**
     * 清除 redis 缓存
     */
    void clearCaches();
}
