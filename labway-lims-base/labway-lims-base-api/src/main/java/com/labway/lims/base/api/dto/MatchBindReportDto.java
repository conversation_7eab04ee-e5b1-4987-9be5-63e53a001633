package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collection;

@Getter
@Setter
public class MatchBindReportDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 检验项目ID集合
     */
    private Collection<Long> testItemIds;
    /**
     * 送检机构ID
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 条码号
     */
    private String barcode;
}
