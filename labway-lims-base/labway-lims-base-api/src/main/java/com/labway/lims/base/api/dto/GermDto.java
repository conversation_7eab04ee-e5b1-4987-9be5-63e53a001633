package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 细菌信息 Dto
 *
 * <AUTHOR>
 * @since 2023/3/21 14:33
 */
@Getter
@Setter
public class GermDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 细菌ID
     */
    private Long germId;

    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 细菌名称
     */
    private String germName;

    /**
     * 细菌英文名
     */
    private String germEn;

    /**
     * 细菌菌属ID
     */
    private Long germGenusId;
    /**
     * 细菌菌属编码
     */
    private String germGenusCode;
    /**
     * 细菌类别编码
     */
    private String germTypeCode;

    /**
     * 细菌类别名称
     */
    private String germTypeName;

    /**
     * whonet细菌类型编码
     */
    private String whonetGermTypeCode;

    /**
     * whonet细菌类型名称
     */
    private String whonetGermTypeName;

    /**
     * whonet细菌编码
     */
    private String whonetGermCode;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 是否开启统计(0未开启 1开启)
     */
    private Integer enableStatistics;

    /**
     * 是否启用(0未启用 1启用)
     * 
     * @see YesOrNoEnum
     */
    private Integer enable;
    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 1:已经删除 0未删除
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;
}
