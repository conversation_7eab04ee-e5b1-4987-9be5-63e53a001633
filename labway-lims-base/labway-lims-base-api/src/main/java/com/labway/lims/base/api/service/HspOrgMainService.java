package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.HspOrgMainDto;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/27 11:19
 * <p>
 * 送检机构科室医生维护
 */
public interface HspOrgMainService {
    List<HspOrgMainDto> selectAll();

    List<Long> addBatch(Collection<HspOrgMainDto> dtos);

    void deleteByHspOrgMainIds(Collection<Long> hspOrgMainIds);

    List<HspOrgMainDto> selectByHspOrgId(long hspOrgId);

    /**
     * 根据 id 获取信息
     */
    List<HspOrgMainDto> selectByHspOrgIds(Collection<Long> hspOrgMainIds);

}
