package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.*;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/8 17:40
 */
public interface MaterialService {
    List<MaterialDto> selectAll();

    /**
     * 根据id查询
     */
    List<MaterialDto> selectByIds(Collection<Long> materialIds);

    MaterialDto selectByMaterialId(long materialId);

    /**
     * 添加物料
     */
    long addMaterial(MaterialDto dto);

    /**
     * 同步物料
     */
    void syncMaterial(IncrementalSyncMaterialDto incrementalSyncMaterialDto);

    void updateMaterialsByInfoHash(List<MaterialDto> materials);

    void addMaterials(List<MaterialDto> materials);

    List<MaterialTypeDto> getMaterialTypes();

    /**
     * 通过机构编码 、物料编码获取物料信息
     */
    List<MaterialDto> selectByOrgIdAndMaterialCodes(long orgId, Collection<String> materialCodes);

    /**
     * 通过分组id和物料编码查询物料信息
     * @param materialCodes
     * @return
     */
    List<MaterialDto> selectByGroupIdAndMaterialCodes( Collection<String> materialCodes);

    MaterialPageInfoDto selectByMaterialNameOrName(MaterialQueryDto dto);

    List<MaterialDto> selectByMaterialIds(Collection<Long> materialIds);

    int deleteByMaterialIds(Collection<Long> ids);
}
