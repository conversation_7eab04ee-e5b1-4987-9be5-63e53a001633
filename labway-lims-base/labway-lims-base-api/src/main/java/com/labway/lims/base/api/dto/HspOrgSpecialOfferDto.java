package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class HspOrgSpecialOfferDto implements Serializable {

    private static final long serialVersionUID = 1L;
    // 导入的时候会用到这个行号
    private Integer num;

    /**
     * 优惠ID
     */
    private Long offerId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 项目ID
     */
    private Long testItemId;

    /**
     * 项目编码
     */
    private String testItemCode;

    /**
     * 送检类型编码
     */
    private String sendTypeCode;

    /**
     * 送检类型名称
     */
    @Compare("送检类型")
    private String sendType;

    /**
     * 开始时间
     */
    @Compare("生效时间")
    private Date startDate;

    /**
     * 结束时间
     */
    @Compare("结束时间")
    private Date endDate;

    /**
     * 折扣率
     */
    @Compare("折扣率")
    private BigDecimal discount;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

    /**
     * 是否参与阶梯折扣
     * 
     * @see YesOrNoEnum
     */
    @Compare(value = "参与阶梯折扣",
        content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer isTieredPricing;

    /**
     * 折前价格
     */
    @Compare("折前价格")
    private BigDecimal feePrice;

    /**
     * 折后价格
     */
    @Compare("折后价格")
    private BigDecimal discountPrice;
}
