package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 仪器报告项目
 */
public interface InstrumentReportItemService {

    /**
     * 根据仪器id查询
     */
    List<InstrumentReportItemDto> selectByInstrumentId(long instrumentId);

    /**
     * 根据仪器id查询 key：reportItemCode
     */
    Map<String, InstrumentReportItemDto> selectByInstrumentIdAsMap(long instrumentId);

    /**
     * 根据专业小组查询
     */
    List<InstrumentReportItemDto> selectByInstrumentGroupId(long instrumentGroupId);

    /**
     * 根据专业小组查询 key：reportItemCode
     */
    Map<String, InstrumentReportItemDto> selectByInstrumentGroupIdAsMap(long instrumentGroupId);

    /**
     * 根据专业小组查询
     */
    List<InstrumentReportItemDto> selectByInstrumentGroupIds(Collection<Long> instrumentGroupIds);

    /**
     * 根据仪器id和报告项目编码查询
     */
    @Nullable
    InstrumentReportItemDto selectByInstrumentIdAndReportItemCode(long instrumentId, String reportItemCode);

    /**
     * 根据仪器ids和报告项目s编码查询
     */
    List<InstrumentReportItemDto> selectByInstrumentIdsAndReportItemCodes(Collection<Long> instrumentIds,
                                                                          Collection<String> reportItemCodes);

    /**
     * 根据仪器Code和报告项目编码查询
     */
    @Nullable
    InstrumentReportItemDto selectByInstrumentCodeAndReportItemCode(String instrumentCode, String reportItemCode);

    /**
     * 根据id查询
     */
    @Nullable
    InstrumentReportItemDto selectByInstrumentReportItemId(long instrumentReportItemId);

    /**
     * 通过ids 查询
     */
    List<InstrumentReportItemDto> selectByInstrumentReportItemIds(Collection<Long> instrumentReportItemIds);

    /**
     * 根据reportItemcode和orgId查询
     *
     * @deprecated 查询哪台仪器的？ todo wsl
     */
    @Deprecated
    List<InstrumentReportItemDto> selectByReportItemCodeAndOrgId(String reportItemCode, long orgId);

    /**
     * 根据报告项目编码查询仪器报告项目信息
     */
    List<InstrumentReportItemDto> selectByReportItemCodesAndOrgId(Collection<String> reportItemCodes, long orgId);

    /**
     * 根据id修改
     */
    boolean updateByInstrumentReportItemId(InstrumentReportItemDto instrumentReportItem);

    /**
     * 根据报告项目编码修改
     */
    boolean updateByReportItemCode(InstrumentReportItemDto instrumentReportItem, String reportItemCode);

    /**
     * 从主数据添加
     */
    long addReportItem(long instrumentId, String reportItemCode);

    /**
     * 删除报告项目
     */
    boolean deleteByInstrumentReportItemId(long instrumentReportItemId);

    List<InstrumentReportItemDto> selectByInstrumentIds(Collection<Long> instrumentIds);

    /**
     * 批量查询仪器报告项目
     */
    List<InstrumentReportItemDto> selectByInstrumentIdAndReportItemCodes(long instrumentId,
                                                                         Collection<String> reportItemCodes);

    /**
     * 根据 id 查询
     */
    List<InstrumentReportItemDto> selectByIds(Collection<Long> ids);

    /**
     * 拷贝报告项目到目标仪器
     */
    List<Long> copyInstrumentReportItems(List<InstrumentReportItemDto> fromInstrumentReportItems, Long instrumentId);

    /**
     * 所有的检验方法
     */
    List<DictItemDto> selectAllExamMethod();


    /**
     * 批量保存
     */
    void saveBatch(List<InstrumentReportItemDto> instrumentReportItems);


    /**
     * 查询系统所有的仪器报告项目信息
     */
    List<InstrumentReportItemDto> selectAllInstrumentReportItemWithGroup();

}
