package com.labway.lims.base.api.dto;

import cn.hutool.core.lang.Assert;
import lombok.Data;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 页面配置dto
 */
@Data
public class ShortcutKeyDTO {
    /**
     * 页面key
     */
    private String pageId;

    /**
     * 配置json
     */
    private String shortcutKey;

    public void verifyParams() {
        Assert.isTrue(Page.PAGE_MAP.containsKey(pageId), "未知的页面");
    }


    @Getter
    public enum Page{
        //样本信息录入
        SAMPLE_INFO_TYPE_IN("SmapleEntry", "SAMPLE_INFO_TYPE_IN"),
        //样本录入双输复核
        SAMPLE_TYPE_IN_DOUBLE("EntryReview", "SAMPLE_TYPE_IN_DOUBLE"),
        //常规检验
        ROUTINE_TEST("RoutineInspection", "ROUTINE_TEST"),
        //院感检验
        INFECTION_TEST("HospitalInspect", "INFECTION_TEST"),
        //微生物检验
        MICROBIOLOGY_TEST("MicrobeInspect", "MICROBIOLOGY_TEST"),
        //外送检验
        OUTSOURCING_TEST("OutsideInspect", "OUTSOURCING_TEST"),

        ;
        private final String pageId;
        private final String pageName;
        Page(String pageId, String pageName){
            this.pageId = pageId;
            this.pageName = pageName;
        }
        public static final Map<String, String> PAGE_MAP = new HashMap<>(6);
        static {
            for (Page value : values()) {
                PAGE_MAP.put(value.getPageId(), value.getPageName());
            }
        }
    }

}
