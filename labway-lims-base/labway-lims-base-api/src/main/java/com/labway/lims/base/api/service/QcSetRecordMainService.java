package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.QcInstrumentReportItemSampleNoDto;
import com.labway.lims.base.api.dto.QcSetRecordMainDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【tb_qc_set_record_main(仪器质控设置记录)】的数据库操作Service
 * @createDate 2023-10-31 19:18:30
 */
public interface QcSetRecordMainService {

    /**
     * 根据报告项id和组id获取质控列表信息
     *
     * @param reportId 报告项id
     * @param groupId  组id 可以从threadlocal中获取
     * @return
     */
    List<QcSetRecordMainDto> queryDetailListByReportId(Long reportId, Long groupId);

    /**
     * 设置报告项目样本号：即该样品号默认为质控测试，否则为一般化检查
     *
     * @param instrumentReportItemSampleNoDto
     */
    void setQcInstrumentSampleNo(QcInstrumentReportItemSampleNoDto instrumentReportItemSampleNoDto);

    /**
     * 根据报告项目id查询质控样本号
     *
     * @param instrumentReportId 仪器质控报告项目
     * @return
     */
    Map<Integer, String> queryInstrumentReportQcSampleNo(Long instrumentReportId);
}
