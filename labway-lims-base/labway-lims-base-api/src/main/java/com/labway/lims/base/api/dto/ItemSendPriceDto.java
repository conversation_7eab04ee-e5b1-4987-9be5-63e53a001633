package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 外送项目价格设置 Dto
 *
 * <AUTHOR>
 * @since 2023/5/4 15:09
 */
@Getter
@Setter
public class ItemSendPriceDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 价格ID
     */
    private Long priceId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 项目ID
     */
    private Long testItemId;

    /**
     * 项目编码
     */
    private String testItemCode;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 外送价格
     */
    private BigDecimal sendPrice;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

}
