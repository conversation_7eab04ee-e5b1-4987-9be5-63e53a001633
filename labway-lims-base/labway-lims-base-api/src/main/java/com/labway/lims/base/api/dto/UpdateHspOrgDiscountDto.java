package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class UpdateHspOrgDiscountDto implements Serializable {

    /**
     * 折扣id
     */
    private Long discountId;

    /**
     * 基准包id
     */
    private Long packageId;

    /**
     * 就诊类型编码
     */
    private String sendTypeCode;

    /**
     * 就诊类型名称
     */
    private String sendType;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 折扣率
     */
    private BigDecimal discount;
}
