package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.ItemSendPriceDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 外送项目价格设置 Service
 *
 * <AUTHOR>
 * @since 2023/5/4 15:10
 */
public interface ItemSendPriceService {

    /**
     * 查询外送机构 下所有外送项目
     */
    List<ItemSendPriceDto> selectByHspOrgId(long hspOrgId);

    /**
     * 添加外送机构下外送项目
     */
    void addItemSendPrices(List<ItemSendPriceDto> list);

    /**
     * 删除 外送项目价格设置
     */
    void deleteByPriceIds(Collection<Long> priceIds);

    /**
     * 根据 价格ID 查找 外送项目价格设置
     */
    @Nullable
    ItemSendPriceDto selectByPriceId(long priceId);

    /**
     * 根据 价格ID 查找 外送项目价格设置
     */
    List<ItemSendPriceDto> selectByPriceIds(Collection<Long> priceIds);

    /**
     * 修改 外送项目价格设置
     */
    void updateByPriceId(ItemSendPriceDto dto);


    /**
     * 机构 + 时间范围查询
     */
    List<ItemSendPriceDto> selectByHspOrgIdsAndDateRange(Collection<Long> hspOrgIds, Date startDate, Date endDate);
}
