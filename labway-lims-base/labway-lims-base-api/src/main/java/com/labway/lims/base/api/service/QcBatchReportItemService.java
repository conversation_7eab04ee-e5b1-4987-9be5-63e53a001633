package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.QcBatchDto;
import com.labway.lims.base.api.dto.QcBatchReportItemDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 质控批号报告项目 service
 * 
 * <AUTHOR>
 * @since 2023/7/4 14:38
 */

public interface QcBatchReportItemService {

    /**
     * 检查 此质控批号下 这些报告项目是否可以新增
     */
    void checkAddQcBatchReportItemDtos(QcBatchDto qcBatchDto, Map<String, String> reportItemCodes);

    /**
     * 添加 质控报告项目
     */
    void addQcBatchReportItemDtos(List<QcBatchReportItemDto> list);

    /**
     * 根据质控批号id 获取其质控报告项目
     */
    List<QcBatchReportItemDto> selectByQcBatchId(long qcBatchId);

    /**
     * 删除 质控报告项目
     */
    void deleteByBatchReportItemIds(Collection<Long> batchReportItemIds);

    /**
     * 查询 质控报告项目
     */
    List<QcBatchReportItemDto> selectByQcBatchIds(Collection<Long> qcBatchIds);

    /**
     * 根据报告项目编码查询 质控报告项目
     */
    List<QcBatchReportItemDto> selectByReportItemCode(String reportItemCode);

}
