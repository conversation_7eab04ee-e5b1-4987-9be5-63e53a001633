package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class DiscountDetailDto implements Serializable {

    private final static long serialVersionUID = 1L;

    private BigDecimal discount;
    private Long packageId;
    private Long testItemId;
    private Long hspOrgId;
    private Date startDate;
    private Date endDate;
    private String sendTypeCode;
}
