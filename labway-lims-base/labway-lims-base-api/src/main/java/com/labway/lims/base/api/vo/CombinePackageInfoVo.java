package com.labway.lims.base.api.vo;

import com.labway.lims.base.api.dto.CombinePackageInfoDto;
import com.labway.lims.base.api.dto.TestItemDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CombinePackageInfoVo extends CombinePackageInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检验项目
     */
    private List<TestItemDto> testItems;
}
