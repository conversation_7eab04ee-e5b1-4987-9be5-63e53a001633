package com.labway.lims.base.api.enums;

/**
 * QC质控错误列表
 */
public enum QcErrorCode {

    QC100001("QC100001", "质控批号不能为空"),
    QC100002("QC100002", "生效日期不能为空"),
    QC100003("QC100003", "失效日期不能为空"),
    QC100004("QC100004", "质控样本号列表不能为空"),
    QC100005("QC100005", "客商报告列表不能为空"),
    QC100006("QC100006", "质控批号列表不能为空"),
    QC100007("QC100007", "所选规则不正确"),
    QC100008("QC100008", "所选仪器不能为空"),
    QC100009("QC100009", "质控批号没有查到"),
    QC100010("QC100010", "质控开始日期不能为空"),
    QC100011("QC100011", "质控结束日期不能为空"),
    QC100012("QC100012", "质控规则不能为空"),
    QC100013("QC100013", "记录的对象不能为空"),
    QC100014("QC100014", "靶值不能为空"),
    QC100015("QC100015", "标准差不能为空"),
    QC100016("QC100016", "变异系数不能为空"),
    QC100017("QC100017", "水平编码不能为空"),
    QC100018("QC100018", "仪器报告编码不能为空"),
    QC100019("QC100019", "保存质控浓度信息失败"),
    QC100020("QC100020", "请先设置项目质控批号"),
    QC100021("QC100021", "未找到有效时间内的质控批号"),
    QC100022("QC100022", "该质控批号已被使用，不能删除"),
    QC100023("QC100023", "未找到时间范围内的结果值"),
    QC100024("QC100022", "所选仪器未设置有效样本号"),
    QC100025("QC100025", "请先设置对应报告的水平浓度配置"),
    QC100026("QC100026", "质控批号与水平浓度出现重复，请检查"),
    QC100027("QC100027", "开始时间不得低于结束时间"),
    QC100028("QC100028", "出现了重复的样本号，请检查"),
    QC100029("QC100029", "失效日期不得低于创建日期或者相同"),
    QC100030("QC100030", "时间范围内有效的结果值至少需要有2个"),
    QC100031("QC100031", "标准差结果有误，不可为0"),
    QC100032("QC100032", "靶值结果有误，不可为0"),
    QC100033("QC100033", "没有查询到靶值等信息"),
    QC100034("QC100034", "没有查询到浓度信息"),
    QC100035("QC100035", "不存在该质控选项"),
    QC100036("QC100036", "靶值数据格式错误，只能是数字"),
    QC100037("QC100037", "标准差数据格式错误，只能是数字"),
    ;

    QcErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    private String code;

    private String message;


    public String getCode() {
        return code;
    }

    public String getMsg() {
        return message;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setMsg(String message) {
        this.message = message;
    }
}
