package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class AddHspOrgSpecialOfferProjectDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 检验项目ids
     */
    private List<Long> testItemIds;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 折扣率
     */
    private BigDecimal discount;

    /**
     * 是否阶梯折扣 1是 0否
     *
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer isTieredPricing;

    /**
     * 添加的就诊类型
     */
    private List<AddHspOrgSpecialOfferProjectSendTypeDto> sendTypeList;

}
