package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.GermGenusDto;
import com.labway.lims.base.api.dto.MedicineDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 药物 Service
 * 
 * <AUTHOR>
 * @since 2023/3/21 16:22
 */
public interface MedicineService {

    /**
     * 添加药物
     */
    long addMedicine(MedicineDto medicineDto);

    /**
     * 删除药物
     */
    void deleteByMedicineIds(Collection<Long> medicineIds);

    /**
     * 修改药物
     */
    void updateByMedicineId(MedicineDto medicineDto);

    /**
     * 查询 药物 通过 机构
     */
    List<MedicineDto> selectByOrgId(long orgId);

    /**
     * 查询 药物 通过细菌菌属ids
     */
    List<MedicineDto> selectByGermGenusIds(Collection<Long> germGenusIds);

    /**
     * 查询 药物 通过细菌菌属id
     */
    List<MedicineDto> selectByGermGenusId(long germGenusId);

    /**
     * 根据药物ID查找药物ID
     */
    @Nullable
    MedicineDto selectByMedicineId(long medicineId);

    /**
     * 根据药物名称查找药物
     */
    @Nullable
    MedicineDto selectByMedicineName(String medicineName, long orgId);

    /**
     * 根据药物编码查找药物
     */
    @Nullable
    MedicineDto selectByMedicineCode(String medicineCode, long orgId);

    /**
     * 根据药物IDs查找药物ID
     */
    List<MedicineDto> selectByMedicineIds(Collection<Long> ids);

    /**
     * 更新 药物
     * 
     * @param medicineDto 药物
     * @param needAddRelationGenusList 需要新增的药物菌属关联
     * @param needDeleteRelationIdList 需要删除的药物菌属关联
     */
    void medicineUpdate(MedicineDto medicineDto, List<GermGenusDto> needAddRelationGenusList,
        List<Long> needDeleteRelationIdList);
}
