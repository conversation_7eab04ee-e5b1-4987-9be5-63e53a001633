package com.labway.lims.base.api.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/1 14:01
 * @Version 1.0
 */
@Data
//TODO 作用待处理
public class AddInstrumentQcBatchDto {
    private static final long serialVersionUID = 1L;
    /**
     * 仪器ID
     */
    private Long instrumentId;
    /**
     * 质控样本号列表
     */
    private List<String> sampleNoList;
    /**
     * 客商报告ID列表
     */
    private List<Long> customerReportIds;
    /**
     * 选中质控批号列表
     */
    private List<Long> batchIds;
    /**
     * 规则集合
     */
    private String rules;
}
