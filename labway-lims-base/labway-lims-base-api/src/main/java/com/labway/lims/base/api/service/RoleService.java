package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.RoleDto;
import com.labway.lims.base.api.dto.UserRoleDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 角色
 */
public interface RoleService {
    /**
     * 根据用户查找角色
     */
    List<RoleDto> selectByUserId(long userId);


    /**
     * 根据用户查找角色
     */
    List<UserRoleDto> selectUserRoleByUserId(long userId);


    /**
     * 根据用户查找角色
     */
    List<UserRoleDto> selectUserRoleByUserIds(Collection<Long> userIds);

    /**
     * 查找所有角色
     */
    List<RoleDto> selectByOrgId(long orgId);

    /**
     * 添加角色
     */
    long addRole(RoleDto role);

    /**
     * 根据角色ID删除
     */
    void deleteByRoleId(long roleId);

    /**
     * 根据角色ID删除
     */
    void deleteByRoleIds(Set<Long> roleIds);

    /**
     * 根据id修改
     */
    boolean updateById(RoleDto role);

    /**
     * 修改菜单，会删除之前的菜单，但不会删除按钮
     *
     * @param roleId  角色id
     * @param menuIds 菜单id
     */
    void updateMenusByRoleId(long roleId, Set<Long> menuIds);

    /**
     * 修改按钮，会删除之前的按钮，但不会删除菜单
     *
     * @param roleId    角色id
     * @param buttonIds 按钮id
     */
    void updateButtonsByRoleId(long roleId, Set<Long> buttonIds);

    /**
     * 根据ID查询角色
     */
    @Nullable
    RoleDto selectByRoleId(long roleId);

    /**
     * 根据ID查询角色
     */
    List<RoleDto> selectByRoleIds(Collection<Long> roleIds);

    /**
     * 根据菜单ID查询角色
     */
    List<RoleDto> selectByMenuId(long menuId);
}
