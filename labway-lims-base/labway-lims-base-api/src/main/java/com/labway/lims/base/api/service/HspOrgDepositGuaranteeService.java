package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.HspOrgDepositGuaranteeDto;
import com.labway.lims.base.api.vo.AddHspOrgDepositGuaranteeBatchVo;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 机构保底金维护Service
 */
public interface HspOrgDepositGuaranteeService {

    /**
     * 批量新增机构保底金
     *
     * @param vo 批量新增参数
     * @return 保底金DTO列表
     */
    int addHspOrgDepositGuaranteeBatch(AddHspOrgDepositGuaranteeBatchVo vo);

    /**
     * 根据ID查询机构保底金
     *
     * @param hspOrgDepositGuaranteeId 保底金ID
     * @return 保底金DTO
     */
    @Nullable
    HspOrgDepositGuaranteeDto selectById(long hspOrgDepositGuaranteeId);

    /**
     * 根据ID集合查询机构保底金
     *
     * @param ids ID集合
     * @return 保底金DTO列表
     */
    List<HspOrgDepositGuaranteeDto> selectByIds(Collection<Long> ids);

    /**
     * 查询全部机构保底金
     *
     * @param hspOrgName 送检机构名称（模糊查询）
     * @return 保底金DTO列表
     */
    @Nonnull
    List<HspOrgDepositGuaranteeDto> selectAll(String hspOrgName);

    /**
     * 修改机构保底金信息
     *
     * @param dto 修改参数
     */
    void updateById(HspOrgDepositGuaranteeDto dto);

    /**
     * 删除机构保底金
     *
     * @param ids ID集合
     */
    void deleteByIds(List<Long> ids);

    /**
     * 根据时间范围查询
     */
    List<HspOrgDepositGuaranteeDto> selectByDate(@Nonnull Date startDate, @Nonnull Date endDate);
} 