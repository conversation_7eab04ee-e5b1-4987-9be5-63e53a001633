package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 质控批号报告项目
 * 
 * <AUTHOR>
 * @since 2023/7/4 15:09
 */
@Getter
@Setter
public class QcBatchReportItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 质控批号报告项目id
     */
    private Long batchReportItemId;

    /**
     * 质控批号id
     */
    private Long qcBatchId;

    /**
     * 质控批号
     */
    private String qcBatch;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 单位
     */
    private String reportItemUnit;

    /**
     * 是否删除,1删除0未删除
     */
    private Integer isDelete;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名
     */
    private String creatorName;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人名
     */
    private String updaterName;

}
