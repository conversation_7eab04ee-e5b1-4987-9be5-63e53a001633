package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.PackageDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 套餐 Service
 * 
 * <AUTHOR>
 * @since 2023/3/28 11:33
 */
public interface PackageService {

    /**
     * 添加 套餐
     */
    long addPackage(PackageDto packageDto);

    /**
     * 删除 体检团体套餐
     */
    void deleteByPackageIds(Collection<Long> physicalGroupPackageIds);

    /**
     * 修改 体检团体套餐
     */
    void updateByPackageId(PackageDto packageDto);

    /**
     * 查询 套餐 通过 机构
     */
    List<PackageDto> selectByOrgId(long orgId);

    /**
     * 根据 体检套餐名称 获取 体检团体套餐
     */
    @Nullable
    PackageDto selectByPackageName(String physicalGroupPackageName, long orgId);

    /**
     * 根据 体检套餐id 查找 体检团体套餐
     */
    @Nullable
    PackageDto selectByPackageId(long physicalGroupPackageId);

    /**
     * 根据 体检套餐ids 查找 体检团体套餐
     */
    List<PackageDto> selectByPackageIds(Collection<Long> physicalGroupPackageIds);

    /**
     * 根据 体检单位IDs 获取 体检团体套餐
     */
    List<PackageDto> selectByGroupIds(Collection<Long> physicalGroupIds);

}
