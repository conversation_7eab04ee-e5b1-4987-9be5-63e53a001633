package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.HspOrgPricingDto;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 客户阶梯折扣信息 Service
 *
 * <AUTHOR>
 * @since 2023/5/4 13:48
 */
public interface HspOrgPricingService {

    /**
     * 新增 客户阶梯折扣
     */
    long addHspOrgPricing(HspOrgPricingDto dto);

    /**
     * 修改 客户阶梯折扣
     */
    void updateByTieredPriceId(HspOrgPricingDto dto);

    /**
     * 查询 客户阶梯折扣 通过 机构
     */
    List<HspOrgPricingDto> selectByOrgId(long orgId);

    /**
     * 根据 阶梯折扣ID 查找 客户阶梯折扣
     */
    @Nullable
    HspOrgPricingDto selectByTieredPriceId(long tieredPriceId);

    /**
     * 检查是否存在交集数据 排除
     */
    boolean isExistIntersectionData(long hspOrgId, Date startDate, Date endDate, BigDecimal beforeMinPrice,
        BigDecimal beforeMaxPrice, long excludeTieredPriceId);

    /**
     * 查询 在指定机构下 与时间范围存在交集 的折扣数据
     */
    List<HspOrgPricingDto> selectByHspOrgIdsAndDateRange(Collection<Long> hspOrgIds, Date startDate, Date endDate);

    List<HspOrgPricingDto> selectByTotalDiscount(BigDecimal sum);
}
