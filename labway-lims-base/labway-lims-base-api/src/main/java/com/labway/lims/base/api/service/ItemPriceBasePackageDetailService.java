package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.ItemPriceBasePackageDetailContrastDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDetailDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDetailSheetDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 项目价格基准包详情服务层
 */
public interface ItemPriceBasePackageDetailService {

    /**
     * 查询基准包项目对照信息
     *
     * @param filterType 过滤类型 1过滤出对照了的项目 0过滤出未对照的项目
     * @param packageId 基准包id
     */
    List<ItemPriceBasePackageDetailContrastDto> selectItemPriceBasePackageDetailContrastList(Integer filterType,
        Long packageId);

    /**
     * 根据基准包id查询基准包详情
     */
    List<ItemPriceBasePackageDetailDto> selectByPackageId(long packageId);

    /**
     * 根据基准包ids查询基准包详情
     */
    List<ItemPriceBasePackageDetailDto> selectByPackageIds(Collection<Long> packageIds);

    /**
     * 添加 基准包下检验项目
     */
    void addItemPriceBasePackageDetails(List<ItemPriceBasePackageDetailDto> list);

    /**
     * 批量删除
     */
    void deleteByIds(List<Long> ids);

    /**
     * 获取基准包明细
     */
    List<ItemPriceBasePackageDetailContrastDto> packageDetails(Long packageId);

    /**
     * 查询详情
     */
    @Nullable
    ItemPriceBasePackageDetailDto selectByDetailId(long detailId);

    /**
     * 查询详情
     */
    List<ItemPriceBasePackageDetailDto> selectByDetailIds(Collection<Long> detailIds);

    /**
     * 修改详情
     */
    void updateByDetailId(ItemPriceBasePackageDetailDto dto);

    /**
     * 导入
     */
    int importItemPriceBasePackageDetail(Long packageId, List<ItemPriceBasePackageDetailSheetDto> importDetails);
}
