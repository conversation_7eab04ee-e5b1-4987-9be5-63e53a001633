package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 仪器专业小组检验项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class InstrumentGroupTestItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业小组检验项目ID
     */
    private Long instrumentGroupTestItemId;

    /**
     * 仪器专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 仪器专业小组仪器ID
     */
    private Long instrumentGroupInstrumentId;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 报告项目编码
     */
    private Long testItemId;

    /**
     * 报告项目编码
     */
    private String testItemCode;

    /**
     * 报告项目名称
     */
    private String testItemName;

    /**
     * 是否启动(0未启动 1启动)
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 1: 删除 0:未删
     * @see YesOrNoEnum
     */
    private Integer isDelete;

}
