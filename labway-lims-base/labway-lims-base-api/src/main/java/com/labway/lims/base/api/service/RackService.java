package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.QueryRackPageDto;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.RackHoleRuleDto;
import com.labway.lims.base.api.dto.RackPageDto;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/22 11:17
 */
public interface RackService {
    /**
     * 添加试管架
     *
     * @param dto RackDto
     */
    long add(RackDto dto);

    /**
     * 根据rackId修改
     *
     * @param dto RackDto
     */
    boolean updateByRackId(RackDto dto);

    /**
     * 根据rackId删除
     * @param dto
     * @return
     */
    boolean deleteByRackId(RackDto dto);

    /**
     * 根据rackId修改
     */
    void updateByRackIds(RackDto dto, Collection<Long> rackIds);

    /**
     * 根据rackId查询
     *
     * @param rackId rackId
     * @return RackDto
     */
    @Nullable
    RackDto selectByRackId(long rackId);

    /**
     * 根据rackId查询
     *
     * @param rackIds rackId
     * @return RackDto
     */
    List<RackDto> selectByRackIds(Collection<Long> rackIds);

    /**
     * 查询所有试管架
     * @param needArchive 是否包含归档
     *
     * @return List<RackDto>
     */
    @Nonnull
    List<RackDto> selectAll(long orgId,boolean needArchive);

    /**
     * 根据状态查询管架
     */
    List<RackDto> selectByStatus(int status,long orgId);

    /**
     * 根据状态查询管架
     */
    long countByStatus(int status,long orgId);

    /**
     * 根据 试管架编码 获取试管架
     */
    @Nullable
    RackDto selectByRackCode(String rackCode, long orgId);

    /**
     * 根据试管架类型查询 试管架
     */
    List<RackDto> selectByRackTypeCodes(Collection<String> rackTypeCode, long orgId, String groupCode);

    /**
     * 查询 试管架 排序规则
     */
    RackHoleRuleDto selectRackHoleRuleByRackId(long rackId);

    /**
     * 试管架 回收
     */
    void rackRecycle(Collection<Long> rackIds);

    RackPageDto selectArchiveRackPage(QueryRackPageDto queryRackPageDto);
}
