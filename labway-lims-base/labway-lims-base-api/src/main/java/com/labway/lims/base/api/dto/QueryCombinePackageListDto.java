package com.labway.lims.base.api.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class QueryCombinePackageListDto implements Serializable {

    //套餐包主键id
    private List<Long> combinePackageIds;
    //套餐包名称
    private String combinePackageName;
    //机构id
    private Long orgId;
    //机构名称
    private String orgName;
    //是否启用 0否1是
    private Integer enable;
    //套餐包编码
    private String combinePackageCode;
    //套餐收费价格
    private BigDecimal combinePackagePrice;
    //套餐项目数量
    private Long itemCount;
    //送检机构编码
    private String hspOrgCode;
    //送检机构名称
    private String hspOrgName;
    //送检机构id
    private Long hspOrgId;
    //检验项目MD5值
    private String itemMd5;


}
