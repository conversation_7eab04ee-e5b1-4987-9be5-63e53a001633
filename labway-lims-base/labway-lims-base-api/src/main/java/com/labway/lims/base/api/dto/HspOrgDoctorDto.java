package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/7/27 11:07
 */
@Getter
@Setter
public class HspOrgDoctorDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long hspOrgDoctorId;

    /**
     * 关联ID
     */
    private Long hspOrgDeptId;

    /**
     * 关联ID
     */
    private Long hspOrgMainId;

    /**
     * id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 科室编码
     */
    private String deptCode;

    /**
     * 科室名称
     */
    private String dept;

    /**
     * 医生编码
     */
    private String doctorCode;

    /**
     * 医生名称
     */
    private String doctorName;

    /**
     * 机构id
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 1:删除,0:未删
     */
    private Integer isDelete;
}
