package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 仪器报告项目参考值
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Getter
@Setter
public class InstrumentReportItemReferenceDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参考值范围ID
     */
    private Long instrumentReportItemReferenceId;

    /**
     * 仪器报告项目ID
     */
    private Long instrumentReportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;


    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 性别类型(男1、女2、通用0)
     */
    private Integer sexStyle;

    /**
     * 性别类型名称
     */
    @Compare("适用性别")
    private String sexStyleName;

    /**
     * 参考值上限值
     */
    @Compare("参考值上限")
    private String referValueMax;

    /**
     * 参考值下限值
     */
    @Compare("参考值下限")
    private String referValueMin;



    /**
     * 参考值上限值 < or <=
     */
    @Compare("参考值上限值单位")
    private String referValueMaxFormula;

    /**
     * 参考值下限值 > or >=
     */
    @Compare("参考值下限值单位")
    private String referValueMinFormula;

    /**
     * 异常提示范围上限
     */
    @Compare("异常提示上限")
    private String excpWarningMax;

    /**
     * 异常提示范围下限
     */
    @Compare("异常提示下限")
    private String excpWarningMin;


    /**
     * 异常提示上限 < or <=
     */
    @Compare("异常提示上限单位")
    private String excpWarningMaxFormula;

    /**
     * 异常提示范围下限 > or >=
     */
    @Compare("异常提示范围下限单位")
    private String excpWarningMinFormula;

    /**
     * 危急值上限
     */
    @Compare("危急值上限")
    private String fatalMax;

    /**
     * 危急值下限
     */
    @Compare("危急值下限")
    private String fatalMin;

    /**
     * 异常提示上限 < or <=
     */
    @Compare("危急值上限单位")
    private String fatalMaxFormula;

    /**
     * 异常提示范围下限 > or >=
     */
    @Compare("危急值下限单位")
    private String fatalMinFormula;

    /**
     * 年龄上限
     */
    @Compare("年龄上限")
    private Integer ageMax;

    /**
     * 年龄下限
     */
    @Compare("年龄下限")
    private Integer ageMin;

    /**
     * 年龄上限 < or <=
     */
    @Compare("年龄上限单位")
    private String ageMaxFormula;

    /**
     * 年龄下限 > or >=
     */
    @Compare("年龄下限单位")
    private String ageMinFormula;

    /**
     * 年龄单位
     */
    @Compare("年龄单位")
    private String ageUnit;

    /**
     * 中文参考值
     */
    @Compare("中文参考值")
    private String cnRefereValue;

    /**
     * 英文参考值
     */
    @Compare("英文参考值")
    private String enRefereValue;

    /**
     * 中英文参考值
     */
    @Compare("中英文参考值")
    private String cnEnRefereValue;

    /**
     * 参考值状态
     *
     * @see YesOrNoEnum
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 样本类型编码
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    @Compare("样本类型")
    private String sampleTypeName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;


    /**
     * 1: 删除 0：未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 参考范围
     */
    private String range;

    /**
     * 仪器专业小组ID
     */
    private Long instrumentGroupId;

    public String getRange() {
        // 先取中文 英文 中英文 再取参考范围
        range = StringUtils.defaultIfBlank(this.getCnRefereValue(), this.getEnRefereValue());
        range = StringUtils.defaultIfBlank(range, this.getCnEnRefereValue());
        if(StringUtils.isBlank(range)) {
            if (StringUtils.isNotBlank(this.getReferValueMin()) && StringUtils.isNotBlank(this.getReferValueMax())) {
                range = this.getReferValueMin() + "~" + this.getReferValueMax();
            } else if (StringUtils.isNotBlank(this.getReferValueMin())) {
                range = SymbolEnum.getSymbol(this.getReferValueMinFormula()) + this.getReferValueMin();
            } else if (StringUtils.isNotBlank(this.getReferValueMax())) {
                range = SymbolEnum.getSymbol(this.getReferValueMaxFormula()) + this.getReferValueMax();
            }
        }
        return range;
    }

    @Getter
    @AllArgsConstructor
    public enum SymbolEnum {
        LT("<", "<"),
        LE("<=", "≤"),
        GT(">", ">"),
        GE(">=", "≥"),
        ;

        private final String symbol;

        private final String resultSymbol;

        public static String getSymbol(String symbol) {
            for (SymbolEnum symbolEnum : SymbolEnum.values()) {
                if (symbolEnum.getSymbol().equals(symbol)) {
                    return symbolEnum.getResultSymbol();
                }
            }
            return "";
        }
    }
}
