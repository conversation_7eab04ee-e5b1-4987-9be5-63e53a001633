package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;

import java.util.List;

/**
 * 仪器报告项
 *
 * <AUTHOR>
 * @Date 2023/11/2 13:00
 * @Version 1.0
 */
public interface QcInstrumentReportService {

    List<InstrumentReportItemDto> selectInstrumentReportByInstrumentId(Long instrumentId);

    /**
     * 根据groupId来获取当前启用的仪器
     * @param groupId groupId
     *
     * @return
     */
    List<InstrumentDto> queryInstrumentListByGroupId(Long groupId);
}
