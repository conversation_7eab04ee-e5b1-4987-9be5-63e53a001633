package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.BarcodeSettingDto;

import java.io.Serializable;
import java.util.List;

public interface BarcodeSettingService {

    /**
     * 根据id查询
     * @return
     */
    BarcodeSettingDto selectById(Serializable id);

    /**
     * 查询所有机构条码和主条码配置  启用and未启用
     * @return
     */
    List<BarcodeSettingDto> selectAll();

    /**
     * 查询所有机构条码配置  启用and未启用
     */
    List<BarcodeSettingDto> selectAllBarCode();

    /**
     * 查询所有机构主条码配置  启用and未启用
     */
    List<BarcodeSettingDto> selectAllMasterBarCode();

    /**
     * 查询所有 启用 机构条码配置
     * 只是查询当前机构的信息， 并不修改当前样本号
     */
    List<BarcodeSettingDto> selectByHspOrgCodeAndBarcodeType(String hspOrgCode, int barcodeType);

    /**
     * 添加机构条码配置
     */
    boolean addBarcodeSetting(BarcodeSettingDto dto);

    /**
     * 添加机构条码配置
     */
    boolean updateBarcodeSetting(BarcodeSettingDto dto);


    /**
     * 根据 送检机构Code， 获取 n 个 master barcode
     * @param hspOrgCode  送检机构Code
     * @param number 获取多少个
     * @return 条码号
     */
    List<String> genMasterBarcodes(String hspOrgCode, int number);

    /**
     * 根据 送检机构Code， 获取 n 个barcode
     * @param hspOrgCode  送检机构Code
     * @param number 获取多少个
     * @param barcodeType 1 条码，  2主条码 {@link BarcodeSettingDto.barcodeType }
     * @return 条码号
     */
    List<String> genBarcodes(String hspOrgCode, int number, int barcodeType);


    /**
     * 根据条码解析条码规则
     * @param barcode
     * @param barcodeType 1 条码，  2主条码 {@link BarcodeSettingDto.barcodeType }
     * @return
     */
    BarcodeSettingDto selectByBarcode(String barcode, int barcodeType);
}
