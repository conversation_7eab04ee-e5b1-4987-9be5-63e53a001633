package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 质控批号维护
 */
@Getter
@Setter
public class QcBatchDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 质控批号ID
     */
    private Long qcBatchId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 质控批号
     */
    private String qcBatch;

    /**
     * 质控品名称
     */
    private String qcMatieralName;

    /**
     * 试剂品牌
     */
    private String reagentBrand;

    /**
     * 生效时间
     */
    private Date beginDate;

    /**
     * 失效时间
     */
    private Date endDate;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名
     */
    private String creatorName;

    /**
     * 更新id
     */
    private Long updaterId;

    /**
     * 更新人名
     */
    private String updaterName;

    /**
     * 更新时间
     */
    private Date updateDate;


    /**
     * 来源
     */
    private String source;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业组id
     */
    private Long groupId;
    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 低浓度
     */
    private String low;

    /**
     * 中浓度
     */
    private String medium;

    /**
     * 高浓度
     */
    private String high;

}