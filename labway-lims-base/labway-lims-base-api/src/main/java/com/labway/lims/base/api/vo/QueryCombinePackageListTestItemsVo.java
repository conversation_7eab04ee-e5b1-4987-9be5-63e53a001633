package com.labway.lims.base.api.vo;

import com.labway.lims.base.api.dto.CombinePackageInfoDto;
import com.labway.lims.base.api.dto.TestItemDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryCombinePackageListTestItemsVo extends CombinePackageInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 财务套餐下的项目
     */
    private List<TestItemDto> testItemDtoList;
}
