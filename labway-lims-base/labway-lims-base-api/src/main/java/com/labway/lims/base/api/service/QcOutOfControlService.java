package com.labway.lims.base.api.service;


/**
 * <AUTHOR>
 * tb_out_of_control_model服务层
 */
public interface QcOutOfControlService {

//    /**
//     * 添加质控失控模板
//     */
//    void addQcOutOfControlTemplate(OutOfControlModelDto outOfControlModelDto);
//
//    /**
//     * 添加质控失控模板
//     */
//    void editQcOutOfControlTemplate(OutOfControlModelDto outOfControlModelDto);
//
//    /**
//     * 删除模板
//     *
//     * @param templateId 模板id
//     */
//    void deleteQcOutOfControlTemplate(String templateId);
//
//    /**
//     * 查询质控失控模板
//     */
//    List<OutOfControlModelDto> queryQcOutOfControlTemplate();
//
//    /**
//     * 填写质控失控模板
//     */
//    void fillOutQualityControlOutOfControlReport(FillOutQualityControlOutOfControlReportDto fillOutQualityControlOutOfControlReportDto);
//
//    /**
//     * 失控记录列表
//     */
//    PageDto<OutOfControlRecordDto> outOfControlRecordList(OutOfControlRecordQueryDto outOfControlRecordQueryDto);
//
//    /**
//     * 删除质控失控记录
//     */
//    void removeOutOfControlRecord(String qcRecordItemId);
//
//    /**
//     * 编辑失控记录
//     */
//    void editOutOfControlTemplateContent(OutOfControlRecordEditDto outOfControlRecordEditDto);
//
//    /**
//     * 根据结果id获取
//     */
//    OutOfControlRecordDto getOutOfControlRecordByResultId(String resultId);
}
