package com.labway.lims.base.api.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class ExportTestItemsDTO implements Serializable {
    /**
     * 组id
     */
    private Long groupId;

    /**
     * 项目类型id
     */
    private String itemType;

    /**
     * 检验方法
     */
    private String examMethodCode;

    /**
     * 样本类型code
     */
    private String sampleTypeCode;

    /**
     * 项目code或者name
     */
    private String itemCodeOrName;

    /**
     * 是否启用 否启用  0否 1是 null所有
     */
    private Integer enable;
}
