package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.HspOrgDoctorDto;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/27 11:20
 */
public interface HspOrgDoctorService {
    List<HspOrgDoctorDto> selectByHspOrgDeptId(long hspOrgDeptId);

    /**
     * 根据机构获取医生列表
     * @param hspOrgId
     * @return
     */
    List<HspOrgDoctorDto> selectByHspOrgId(long hspOrgId);

    long add(HspOrgDoctorDto dto);

    void deleteByHspOrgDoctorId(Collection<Long> hspOrgDoctorIds);

    void addBatch(Collection<HspOrgDoctorDto> dtos);

    void deleteByByHspOrgMainIds(Collection<Long> hspOrgMainIds);

    void deleteByHspOrgDeptIds(Collection<Long> hspOrgDeptIds);

    /**
     * 根据ids 获取
     */
    List<HspOrgDoctorDto> selectByHspOrgDoctorIds(Collection<Long> hspOrgDoctorIds);

}
