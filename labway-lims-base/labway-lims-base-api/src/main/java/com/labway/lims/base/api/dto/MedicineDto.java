package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 药物 信息 Dto
 * 
 * <AUTHOR>
 * @since 2023/3/21 16:50
 */
@Getter
@Setter
public class MedicineDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 药物ID
     */
    private Long medicineId;
    /**
     * 药物编码
     */
    private String medicineCode;
    /**
     * 药物名称
     */
    @Compare("药物名称")
    private String medicineName;
    /**
     * 药物英文名
     */
    @Compare("药物英文名称")
    private String medicineEn;

    /**
     * whonet药物编码
     */
    @Compare("WHONET药物编码")
    private String whonetMedicineCode;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 是否启用(0未启用 1启用)
     * 
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
        content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enable;
    /**
     * 1: 删除 0:未删
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人名称
     */
    private String updaterName;

}
