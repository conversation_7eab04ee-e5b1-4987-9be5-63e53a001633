package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 双输内容对照表
 */
@Getter
@Setter
public class HspOrganizationFiledDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 对照内容id
     */
    private Long filedId;
    /**
     * 字段code
     */
    private String code;

    /**
     * 字段名称
     */
    private String filed;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构code
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 创建时间
     */
    private int isDelete;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 更新时间
     */
    private Date updateDate;
}
