package com.labway.lims.base.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2023/11/6 2:27
 * @Version 1.0
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecordCalculateDto {
    /**
     * QC批次记录
     */
    private String qcRecordBatch;
    /**
     * 客商报告项目ID
     */
    private Long customerReportId;
    /**
     * 计算记录开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime startTime;
    /**
     * 计算记录结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime endTime;
}
