package com.labway.lims.base.api.service;

/**
 * <AUTHOR>
 * @Date 2023/11/6 11:17
 * @Version 1.0
 */
public interface QcSampleResultService {
    /**
     * 质控浓度数据列表
     */
//    List<QcSampleResultRespDto> qcConcentrationData( QcConcentrationQueryDto qcConcentrationQueryDto);
//
//    /**
//     * 接收仪器质控结果
//     */
//    Map<String, String> receiveQcResults( ReceiveQcResultsDto dto);
//
//    /**
//     * @param operationType 操作类型 1 审核 2 删除
//     * @param batchIds      质控接收批号
//     * @param reportId      报告id
//     */
//    void qcAuditSampleResultOrRemove( Integer operationType,  String batchIds,  String reportId);
//
//    /**
//     * 修改质控样本结果值 仅支持单行
//     */
//    String updateQcSampleResult( UpdateQcSampleResultDto updateQcSampleResultDto);
//
//    /**
//     * 新增浓度 table中新增
//     */
//    String addQcSampleResult( AddQcSampleResultDto addQcSampleResultDto);
//
//    /**
//     * 根据客商报告项目ID查询结果
//     *
//     * @param customerReportItemId customerReportItemId
//     * @param beginTestDate        开始范围
//     * @param endTestDate          结束范围
//     * @return List<QcSampleResultRespDto>
//     */
//    List<QcSampleResultDto> selectByCustomerReportItemId( String customerReportItemId,  Date beginTestDate,
//                                                          Date endTestDate);
//
//    /**
//     * 根据客商报告项目ID查询已审核的结果
//     */
//    List<QcSampleResultDto> selectAuditResultByCustomerReportItemId( Long instrumentReportItemId,
//                                                                     LocalDateTime beginTestDate,  LocalDateTime endTestDate);
//
//    /**
//     * 浓度值取消审核
//     */
//    void cancelQcResultAudit( List<String> recordBatchs);
}
