package com.labway.lims.base.api.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.base.api.dto.ShortcutKeyDTO;
import com.labway.lims.base.api.dto.SystemParamDto;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/3/21 13:49
 */
public interface SystemParamService {
    /**
     * 新增系统参数
     *
     * @param systemParamDto SystemParamDto
     */
    long add(SystemParamDto systemParamDto);

    /**
     * 更新系统参数
     *
     * @param dto SystemParamDto
     */
    boolean updateByParamId(SystemParamDto dto);

    /**
     * 根据paramId删除
     *
     * @param paramId paramId
     */
    void deleteByParamId(long paramId);

    /**
     * g根据paramId查询
     *
     * @param paramId paramId
     * @return SystemParamDto
     */
    @Nullable
    SystemParamDto selectByParamId(long paramId);

    /**
     * 根据paramname查询已启用的
     * @param paramName {@link com.labway.lims.api.enums.base.SystemParamNameEnum}
     */
    @Nullable
    SystemParamDto selectByParamName(String paramName, long orgId);

    /**
     * 查询所有系统参数
     *
     * @return List<SystemParamDto>
     */
    List<SystemParamDto> selectByOrgId(long orgId);

    /**
     *
     * @param paramMetric {@link com.labway.lims.api.enums.base.SystemParamMetricEnum}
     * @param paramName {@link ShortcutKeyDTO.Page.pageMap.value }
     * @param creatorId
     * @return
     */
    SystemParamDto selectByParamMetricAndParamNameAndCreateId(String paramMetric, String paramName, Long creatorId);

    /**
     * 根据paramname查询已启用的 将值转为 JSONObject
     *
     * @param paramName {@link com.labway.lims.api.enums.base.SystemParamNameEnum}
     */
    JSONObject selectAsJsonByParamName(String paramName, long orgId);

    /**
     * 根据paramname查询已启用的  将值转为 JSONArray
     *
     * @param paramName {@link com.labway.lims.api.enums.base.SystemParamNameEnum}
     */
    JSONArray selectAsJsonArrayByParamName(String paramName, long orgId);

    /**
     * 查询配置的血培养检验项目编码
     * @see SystemParamNameEnum#BLOOD_CULTURE_TEST_ITEM_CODE
     */
    Set<String> selectBloodCultureTestItemCodes();
}
