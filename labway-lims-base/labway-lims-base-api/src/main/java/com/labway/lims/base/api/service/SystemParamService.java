package com.labway.lims.base.api.service;

import com.alibaba.fastjson.JSONObject;
import com.labway.lims.base.api.dto.ShortcutKeyDTO;
import com.labway.lims.base.api.dto.SystemParamDto;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/21 13:49
 */
public interface SystemParamService {
    /**
     * 新增系统参数
     *
     * @param systemParamDto SystemParamDto
     */
    long add(SystemParamDto systemParamDto);

    /**
     * 更新系统参数
     *
     * @param dto SystemParamDto
     */
    boolean updateByParamId(SystemParamDto dto);

    /**
     * 根据paramId删除
     *
     * @param paramId paramId
     */
    void deleteByParamId(long paramId);

    /**
     * g根据paramId查询
     *
     * @param paramId paramId
     * @return SystemParamDto
     */
    @Nullable
    SystemParamDto selectByParamId(long paramId);

    /**
     * 根据paramname查询已启用的
     * @param paramName {@link com.labway.lims.api.enums.base.SystemParamNameEnum}
     */
    @Nullable
    SystemParamDto selectByParamName(String paramName, long orgId);

    /**
     * 查询所有系统参数
     *
     * @return List<SystemParamDto>
     */
    List<SystemParamDto> selectByOrgId(long orgId);

    /**
     *
     * @param paramMetric {@link com.labway.lims.api.enums.base.SystemParamMetricEnum}
     * @param paramName {@link ShortcutKeyDTO.Page.pageMap.value }
     * @param creatorId
     * @return
     */
    SystemParamDto selectByParamMetricAndParamNameAndCreateId(String paramMetric, String paramName, Long creatorId);

    JSONObject selectAsJsonByParamName(String paramName, long orgId);
}
