package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 仪器报告项目结果提示
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Setter
@Getter
public class InstrumentReportItemResultTipDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long instrumentReportItemResultTipId;

    /**
     * 仪器报告项目ID
     */
    private Long instrumentReportItemId;

    /**
     * 报告项目编码 如果是：0 表示全部
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 提示内容
     */
    @Compare("提示内容")
    private String tipContent;

    /**
     * ONE_CHECK、TWO_CHECK
     */
    @Compare("提示类型")
    private String tipType;

    /**
     * 条件
     * ≥, >, =, <, ≤
     */
    @Compare("条件")
    private String formulaMax;

    /**
     * 条件值
     */
    @Compare("条件值")
    private String formulaMaxValue;

    /**
     * 条件
     * <, ≤
     */
    @Compare("条件")
    private String formulaMin;

    /**
     * 条件值
     */
    @Compare("条件值")
    private String formulaMinValue;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检医院名称
     */
    @Compare("送检机构")
    private String hspOrgName;

    /**
     * 是否启动(0未启动 1启动)
     * @see YesOrNoEnum
     */
    @Compare(
            value = "是否启用",
            content = {@CompareContent(value = "1", valueDesc = "启用"), @CompareContent(value = "0", valueDesc = "未启用")}
    )
    private Integer enable;


    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;


    /**
     * 1: 删除 0：未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;
}
