package com.labway.lims.base.api.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class GroupMaterialPageVo implements Serializable {

    @NotNull(message = "专业组id不能为空!")
    private Long groupId;

    // 搜索关键字(根据物料编码和名称模糊匹配)
    private String searchKey;

    @NotNull(message = "页码不能为空!")
    private Integer page = 1;

    @NotNull(message = "每页条数不能为空!")
    private Integer pageSize = 10;

}
