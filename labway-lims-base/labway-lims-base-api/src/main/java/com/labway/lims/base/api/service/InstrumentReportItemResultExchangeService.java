package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;

import java.util.Collection;
import java.util.List;

public interface InstrumentReportItemResultExchangeService {
    /**
     * 新增结果值转换
     */
    long addInstrumentReportItemResultExchange(InstrumentReportItemResultExchangeDto dto);


    /**
     * 根据 id 删除
     */
    boolean deleteByInstrumentReportItemResultExchangeId(long instrumentReportItemResultExchangeId);

    /**
     * 根据报告项目ID查询
     */
    List<InstrumentReportItemResultExchangeDto> selectByInstrumentReportItemId(long instrumentReportItemId);

    /**
     * 根据ID修改
     */
    boolean updateByInstrumentReportItemResultExchangeId(InstrumentReportItemResultExchangeDto dto);

    /**
     * 根据ID修改
     */
    InstrumentReportItemResultExchangeDto selectByInstrumentReportItemResultExchangeId(long instrumentReportItemResultExchangeId);

    /**
     * 根据 id 查询
     *
     * @return
     */
    List<InstrumentReportItemResultExchangeDto> selectByInstrumentReportItemResultExchangeIds(Collection<Long> instrumentReportItemResultExchangeIds);

    /**
     * 根据仪器报告项目ID删除
     */
    void deleteByInstrumentReportItemId(long instrumentReportItemId);

    /**
     * 拷贝仪器项目结果值转换
     */
    List<Long> copyReportItemResultExchange(Long fromInstrumentReportItemId, InstrumentReportItemDto instrumentReportItemDto);

    /**
     * 根据报告项目编码查询，没有区分仪器，使用时请注意
     */
    Collection<InstrumentReportItemResultExchangeDto> selectByReportItemCodes(Collection<String> reportItemCodes);
}
