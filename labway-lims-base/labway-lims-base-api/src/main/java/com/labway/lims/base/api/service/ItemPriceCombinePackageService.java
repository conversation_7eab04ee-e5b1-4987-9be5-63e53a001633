package com.labway.lims.base.api.service;


import com.labway.lims.base.api.dto.CombinePackageInfoDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.vo.CombinePackageInfoVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * (TbItemPriceCombinePackage)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-04 19:55:50
 */
public interface ItemPriceCombinePackageService {

    /**
     * 获取所有检验套餐信息-不分页
     */
    List<CombinePackageInfoDto> queryCombinePackageByCodes(Collection<String> combinePackageCodeList);

    /**
     * 新增套餐信息
     */
    int addCombinePackageInfo(CombinePackageInfoDto combinePackageInfoDto);

    /**
     * 修改套餐信息
     */
    int updateCombinePackageInfo(CombinePackageInfoDto combinePackageInfoDto);

    /**
     * 删除套餐信息
     */
    void deleteCombinePackageInfo(Collection<String> combinePackageCodeList);

    /**
     * 查询套餐的详情信息
     * @return combinePackageCode  项目信息
     */
    Map<String, List<TestItemDto>> queryCombinePackageDetailInfo(Collection<String> combinePackageCodes);

    /**
     * 保存
     */
    boolean saveCombinePackageDetail(String combinePackageCode, Set<Long> testItemIds);

    List<CombinePackageInfoVo> queryCombinePackageAndItem();

    /**
     * 删除套餐详情
     * @return 还剩多少检验项目
     */
    Integer deleteCombinePackageDetail(String combinePackageCode, Set<Long> testItemIds);
}

