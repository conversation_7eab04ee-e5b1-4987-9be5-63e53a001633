package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.PackageItemDto;

import java.util.Collection;
import java.util.List;

/**
 * 体检单位套餐 项目 service
 * 
 * <AUTHOR>
 * @since 2023/3/28 17:37
 */
public interface PackageItemService {

    /**
     * 根据套餐ID 获取 对应项目
     * 
     */
    List<PackageItemDto> selectByPackageId(long packageId);

    /**
     * 根据套餐IDs 获取 对应项目
     *
     */
    List<PackageItemDto> selectByPackageIds(Collection<Long> packageIds);

    /**
     * 批量添加检验套餐项目id
     */
    void addPackageItems(List<PackageItemDto> list);

    /**
     * 删除套餐项目 根据 套餐项目 id
     */
    void deleteByPackageItemIds(Collection<Long> packageItemIds);

    /**
     * 根据 套餐项目IDs
     *
     */
    List<PackageItemDto> selectByPackageItemIds(Collection<Long> packageItemIds);


    /**
     * 根据套餐信息、机构信息获取全部项目信息
     *
     */
    List<PackageItemDto> selectAllByOrgIdAndType(Long orgId, String typeCode);
}
