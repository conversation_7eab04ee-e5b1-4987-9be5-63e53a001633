package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 仪器报告项目结果值转换
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Setter
@Getter
public class InstrumentReportItemResultExchangeDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器报告参考值ID
     */
    private Long instrumentReportItemResultExchangeId;

    /**
     * 仪器报告项目ID
     */
    private Long instrumentReportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器结果值
     */
    @Compare("仪器结果值")
    private String instrumentResult;

    /**
     * 转换结果值
     */
    @Compare("转换结果值")
    private String exchangeResult;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;


    /**
     * 1: 删除 0：未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 条件
     * ≥, >, =, <, ≤
     */
    @Compare("条件")
    private String formulaMax;

    /**
     * 条件值
     */
    @Compare("条件值")
    private String formulaMaxValue;

    /**
     * 条件
     * <, ≤
     */
    @Compare("条件")
    private String formulaMin;

    /**
     * 条件值
     */
    @Compare("条件值")
    private String formulaMinValue;

}
