package com.labway.lims.base.api.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2021/8/11 16:07
 * 质控水平浓度
 */
public enum QcSettingLevelEnum {

    /**
     * 低
     */
    LOW(1, "L", "低"),
    /**
     * 中
     */
    MIDDLE(2, "M", "中"),
    /**
     * 高
     */
    HIGH(3, "H", "高"),
    /**
     * 默认值
     */
    DEFAULT(-1, "-1", "默认值");


    /**
     * integer类型 key
     */
    private Integer intCode;

    /**
     * string类型 key
     */
    private String strCode;

    /**
     * 值说明
     */
    private String value;

    QcSettingLevelEnum(Integer intCode, String strCode, String value) {
        this.intCode = intCode;
        this.strCode = strCode;
        this.value = value;
    }

    /**
     * 有效状态
     *
     * @param code 状态编码
     * @return 状态枚举值
     */
    public static QcSettingLevelEnum getEnumByInt(Integer code) {
        return Arrays.stream(QcSettingLevelEnum.values()).filter(item -> item.getIntCode().equals(code)).findFirst().orElse(DEFAULT);
    }

    /**
     * 有效状态
     *
     * @param code 状态编码
     * @return 状态枚举值
     */
    public static QcSettingLevelEnum getEnumByStr(String code) {
        return Arrays.stream(QcSettingLevelEnum.values()).filter(item -> item.getStrCode().equals(code)).findFirst().orElse(DEFAULT);
    }

    public Integer getIntCode() {
        return intCode;
    }

    public String getStrCode() {
        return strCode;
    }

    public String getValue() {
        return value;
    }
}
