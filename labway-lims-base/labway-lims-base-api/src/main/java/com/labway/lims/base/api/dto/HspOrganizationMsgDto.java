package com.labway.lims.base.api.dto;

import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class HspOrganizationMsgDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息id
     */
    private Long msgId;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 提示信息内容
     */
    @Compare("提示内容")
    private String msgContent;

    /**
     * 提示位置id
     */
    private String msgPositionCode;;

    /**
     * 提示位置描述
     */
    @Compare("提示位置")
    private String msgPosition;

    /**
     * 是否启用(0未启用 1已启用)
     * 
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
        content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})

    private Integer enable;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;
}
