package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.SaveHspOrganizationFiledDto;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

public interface SupplementalRecordFieldSettingService {
    /**
     * 添加补录对比字段设置
     */
    int insertSupplementalRecordFieldSetting(SaveHspOrganizationFiledDto dto);

    /**
     * 根据id查询
     */
    SaveHspOrganizationFiledDto selectById(Serializable id);
    /**
     * 根据送检机构id查询
     */
    SaveHspOrganizationFiledDto selectByHspOrgId(Serializable id);
    /**
     * 根据送检机构id查询
     */
    SaveHspOrganizationFiledDto selectByHspOrgIdOrDefaultOrg(Serializable id);

    /**
     * 根据id查询
     */
    List<SaveHspOrganizationFiledDto> selectAll();

    /**
     * 修改补录对比字段设置
     */
    int updateSupplementalRecordFieldSetting(SaveHspOrganizationFiledDto dto);

    /**
     * 删除补录对比字段设置
     */
    int deleteSupplementalRecordFieldSetting(Serializable id);

    /**
     * 批量删除
     */
    int deleteByIds(Collection<Long> ids);
}
