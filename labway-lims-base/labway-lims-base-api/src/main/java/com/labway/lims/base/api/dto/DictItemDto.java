package com.labway.lims.base.api.dto;

import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 字典明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class DictItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典ID
     */
    private Long dictId;

    /**
     * 字典名称
     */
    @Compare("字典内容")
    private String dictName;

    /**
     * 字典说明
     */
    private String dictRemark;

    /**
     * 字典编码
     */
    private String dictCode;
    /**
     * 字典类型
     * 
     * @see com.labway.lims.api.enums.base.DictEnum
     */
    private String dictType;

    /**
     * 字典快捷键
     */
    private String dictKeyshort;

    /**
     * 字典快捷键
     */
    private String dictPinyin;

    /**
     * 数据来源
     * 0： 同步的数据
     * 1： 自行添加的数据
     * 2： 手工单添加的数据
     */
    private Integer dataSource;

    /**
     * 是否启动(0未启动 1启动)
     * 
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    @Compare(value = "是否启用",
        content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 属性1
     */
    private String extraParam1;

    /**
     * 属性2
     */
    private String extraParam2;

    /**
     * 属性3
     */
    private String extraParam3;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
