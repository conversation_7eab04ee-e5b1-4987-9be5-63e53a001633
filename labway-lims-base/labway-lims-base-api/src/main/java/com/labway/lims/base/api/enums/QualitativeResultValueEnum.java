package com.labway.lims.base.api.enums;


import org.apache.commons.collections4.MapUtils;

import javax.annotation.Nullable;
import java.util.*;

/**
 * 定性结果值
 *
 * <AUTHOR>
 */

public enum QualitativeResultValueEnum {

    // value 前端展示   result 后端存储的值
    MINUS("-", -1),
    MINUS_PLUS("+-", 0),
    ONE_PLUS("1(+)", 1),
    TWO_PLUS("2(+)", 2),
    THREE_PLUS("3(+)", 3),
    FOUR_PLUS("4(+)", 4),
    FIVE_PLUS("5(+)", 5),
    ;

    private static Map<String, Integer> QUALITATIVE_RESULT_VALUE_MAP;


    private String value;
    private Integer result;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    QualitativeResultValueEnum(String value, Integer result) {
        this.value = value;
        this.result = result;
    }

    public static QualitativeResultValueEnum selectByCode(Integer code) {
        return Arrays.stream(QualitativeResultValueEnum.values()).filter(i -> Objects.equals(i.getResult(), code)).findFirst().orElse(null);
    }

    public static QualitativeResultValueEnum selectByValue(String value) {
        return Arrays.stream(QualitativeResultValueEnum.values()).filter(i -> Objects.equals(i.getValue(), value)).findFirst().orElse(null);
    }

    @Nullable
    public static Integer selectResultByValue(String value) {
        initResultMap();
        return QUALITATIVE_RESULT_VALUE_MAP.get(value);
    }

    public static synchronized void initResultMap() {
        if (MapUtils.isEmpty(QUALITATIVE_RESULT_VALUE_MAP)) {
            QUALITATIVE_RESULT_VALUE_MAP = new HashMap<>();
        }

        /**
         * 循环创建尿机的结果值
         * - Normal | -1
         * +- ± | 0
         * + 1+ +1 | 1
         * ++ 2+ +2 | 2
         * +++ 3+ +3 | 3
         * ++++ 4+ +4 | 4
         * +++++ 5+ +5 | 5
         */
        for (int i = 1; i <= selectMaxResult(); i++) {
            //i+
            QUALITATIVE_RESULT_VALUE_MAP.put(i + "+", i);
            //+i
            QUALITATIVE_RESULT_VALUE_MAP.put("+" + i, i);

            StringBuilder resultBuilder = new StringBuilder();
            for (int item = 0; item < i; item++) {
                resultBuilder.append("+");
            }

            QUALITATIVE_RESULT_VALUE_MAP.put(resultBuilder.toString(), i);
        }

        QUALITATIVE_RESULT_VALUE_MAP.put("-", MINUS.getResult());
        QUALITATIVE_RESULT_VALUE_MAP.put("Normal", MINUS.getResult());

        QUALITATIVE_RESULT_VALUE_MAP.put("+-", MINUS_PLUS.getResult());
        QUALITATIVE_RESULT_VALUE_MAP.put("±", MINUS_PLUS.getResult());
    }

    public static Integer selectMaxResult() {
        return Arrays.stream(QualitativeResultValueEnum.values()).sorted(Comparator.comparing(QualitativeResultValueEnum::getResult,Comparator.reverseOrder())).map(QualitativeResultValueEnum::getResult).findFirst().orElse(FIVE_PLUS.result);
    }
}
