package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.AddUserDto;
import com.labway.lims.base.api.dto.UpdateUserDto;
import com.labway.lims.base.api.dto.UserDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 用户
 */
public interface UserService {
    /**
     * 根据用户名查找用户
     */
    @Nullable
    UserDto selectByUsername(String username);

    /**
     * 根据用户名和机构ID查找用户
     */
    @Nullable
    UserDto selectByUsernameAndOrgId(String username, Long orgId);

    /**
     * 根据用户id查找用户
     */
    @Nullable
    UserDto selectByUserId(long userId);

    /**
     * 根据用户分组id查找当前分组和未分组的用户nickname
     */
    List<String> selectByGroupId(Long groupId,Long orgId);

    /**
     * 根据角色id查找用户
     */
    List<UserDto> selectByRoleId(long roleId);

    /**
     * 添加用户
     */
    long addUser(AddUserDto user);

    /**
     * 修改用户，仅仅修改用户信息，非null 字段
     */
    boolean updateUserByUserId(UserDto user);

    /**
     * 修改用户，以及专业组、角色等
     */
    void updateByUserId(UpdateUserDto user);

    /**
     * 获取所有用户
     */
    List<UserDto> selectByOrgId(long orgId);

    /**
     * 判断此用户是否拥有这个专业组
     */
    boolean containsGroup(long userId, long nextGroupId);

    /**
     * 校验密码是否正确
     *
     * @param username 用户名
     * @param hash 加密后的密码
     * @param password 明文密码
     * @return true：正确
     */
    boolean validPassword(String username, String hash, String password);

    /**
     * 据专业组id查找 用户 包含没有任何专业组关联的用户(这种用户说明具有所有专业组权限)
     */
    List<UserDto> selectByGroupIdAndOrgId(long groupId, long orgId);

    /**
     * 根据用户名失效员工
     */
    void deleteByNicknames(Collection<String> nicknames);


    /**
     * 添加外部用户
     * @param u
     * @return
     */
    long addOutUser(AddUserDto u);
}
