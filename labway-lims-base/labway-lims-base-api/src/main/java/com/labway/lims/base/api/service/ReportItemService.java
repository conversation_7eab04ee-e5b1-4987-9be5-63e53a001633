package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.ReportItemDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 报告项目
 */
public interface ReportItemService {

    /**
     * 根据检验项目id查询
     */
    List<ReportItemDto> selectByTestItemId(long testItemId);

    /**
     * 根据检验项目code查询
     */
    Map<String, List<ReportItemDto>> selectByTestItemCodesAsMap(Collection<String> testItemCodes, long orgId);

    /**
     * 根据检验项目code查询
     */
    List<ReportItemDto> selectByTestItemCodes(Collection<String> testItemCodes, long orgId);

    /**
     * 根据检验项目 id 查询
     */
    Map<Long, List<ReportItemDto>> selectByTestItemIdsAsMap(Collection<Long> testItemIds, long orgId);

    /**
     * 根据检验项目ids 查询 对应报告项目 数量
     *
     * @return Map key:检验项目ids value: 对应报告项目数量
     */
    Map<Long, Integer> selectReportItemCountByTestItemIds(Collection<Long> testItemIds);

    /**
     * 根据检验项目ids查询
     */
    List<ReportItemDto> selectByTestItemIds(Collection<Long> testItemIds);

    /**
     * 根据 reportItemId 查询
     */
    ReportItemDto selectByReportItemId(long reportItemId);

    /**
     * 根据机构ID查询
     */
    List<ReportItemDto> selectByOrgId(long orgId);

    /**
     * 添加报告项目
     */
    long addReportItem(ReportItemDto reportItem);

    /**
     * 根据 code 查询
     */
    @Nullable
    ReportItemDto selectByReportItemCode(String reportItemCode, Long orgId);

    /**
     * 根据 code 批量查询
     */
    @Nullable
    List<ReportItemDto> selectByReportItemCodes(List<String> reportItemCodes, Long orgId);

    /**
     * 根据检验项目ID删除
     */
    void deleteByTestItemId(long testItemId);

    /**
     * 根据检验项目ID删除
     */
    void deleteByTestItemIds(Collection<Long> testItemIds);

    /**
     * 查询所有 专员组下仪器 列表，排除所选的仪器
     */
    List<InstrumentDto> selectInstrumentsExcludeByInstrumentId(Long instrumentId);

    /**
     * 仪器报告项目拷贝
     */
    List<Long> reportItemCopy(Long originInstrumentId, List<Long> originInstrumentReportItemIds, List<Long> targetInstrumentIds);

    /**
     * 根据编码修改
     */
    void updateByReportItemCodes(ReportItemDto reportItem, Collection<String> strings);
}
