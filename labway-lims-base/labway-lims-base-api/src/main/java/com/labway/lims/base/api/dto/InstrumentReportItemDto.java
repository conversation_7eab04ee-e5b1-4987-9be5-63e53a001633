package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.TestResultTypeEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 仪器报告项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Setter
@Getter
public class InstrumentReportItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器报告项目ID
     */
    private Long instrumentReportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 项目单位
     */
    private String reportItemUnit;

    /**
     * 单位名称
     */
    @Compare("项目单位")
    private String reportItemUnitName;

    /**
     * 项目类型编码
     */
    private String itemTypeCode;

    /**
     * 项目类型名称
     */
    @Compare("项目类型")
    private String itemTypeName;

    /**
     * 是否启用(0未启用 1启用)
     *
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",content = {
            @CompareContent(value = "0",valueDesc = "否"),
            @CompareContent(value = "1",valueDesc = "是")
    })
    private Integer enable;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 英文名称
     */
    @Compare("英文名称")
    private String enName;

    /**
     * 英文缩写
     */
    @Compare("英文缩写")
    private String enAb;

    /**
     * 别名
     */
    @Compare("别名")
    private String aliasName;

    /**
     * 检验方法编码
     */
    private String examMethodCode;

    /**
     * 检验方法名称
     */
    @Compare("检验方法")
    private String examMethodName;

    /**
     * 是否质控
     *
     * @see YesOrNoEnum
     */
    @Compare(value = "是否质控",content = {
            @CompareContent(value = "0",valueDesc = "否"),
            @CompareContent(value = "1",valueDesc = "是")
    })
    private Integer isQc;

    /**
     * 是否打印
     *
     * @see YesOrNoEnum
     */
    @Compare(value = "是否打印",content = {
            @CompareContent(value = "0",valueDesc = "否"),
            @CompareContent(value = "1",valueDesc = "是")})
    private Integer isPrint;

    /**
     * 打印顺序
     */
    @Compare("打印顺序")
    private Integer printSort;

    /**
     * 是否手工录入
     *
     * @see YesOrNoEnum
     */
    @Compare(value = "是否手工录入",content = {
            @CompareContent(value = "0",valueDesc = "否"),
            @CompareContent(value = "1",valueDesc = "是")})
    private Integer isManualInput;

    /**
     * 是否结果为0
     *
     * @see YesOrNoEnum
     */
    @Compare(value = "是否结果为0",content = {
            @CompareContent(value = "0",valueDesc = "否"),
            @CompareContent(value = "1",valueDesc = "是")})
    private Integer isResultZero;

    /**
     * 检测结果类型名称
     */
    @Compare("检测结果类型")
    private String resultTypeName;

    /**
     * 检测结果类型编码
     * @see TestResultTypeEnum
     */
    private String resultTypeCode;

    /**
     * 检测结果是否为空
     *
     * @see YesOrNoEnum
     */
    @Compare(value = "检测结果是否为空",content = {
            @CompareContent(value = "0",valueDesc = "否"),
            @CompareContent(value = "1",valueDesc = "是")})
    private Integer isResultNull;

    /**
     * 结果小数点位数
     */
    @Compare("结果小数点位数")
    private Integer decimalNums;

    /**
     * 仪器通道编码
     */
    @Compare("仪器通道编码")
    private String instrumentChannel;

    /**
     * 计算公式json
     */
    @Compare("计算公式")
    private String calcFomulation;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 是否自动带出报告项目
     *
     * @see YesOrNoEnum
     */
    private Integer isBringOut;

    /**
     * 是否删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 仪器分组ID
     */
    private Long instrumentGroupId;

    /**
     * 空参考范围提示 0:不提示，1:禁止审核，2:审核提示
     */
    @Compare("空参考范围提示")
    private Integer emptyReferenceTip;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;


    /**
     * 获取项目的仪器数据
     *
     * @param instrumentReportItems 仪器报告项目Map k = ReportItemCode  values = 报告项目在那些仪器下维护了
     * @param reportItemCode        报告项目
     * @param instrumentId          样本id
     * @return 仪器报告项目
     */
    @Nullable
    public static InstrumentReportItemDto getInstrumentReportItemDto(@Nonnull Map<String, List<InstrumentReportItemDto>> instrumentReportItems, @Nonnull String reportItemCode, long instrumentId) {
        final List<InstrumentReportItemDto> items = instrumentReportItems.getOrDefault(reportItemCode, List.of());
        return items.stream().filter(e -> Objects.equals(instrumentId, e.getInstrumentId())).findFirst()
                .orElse(items.stream().findFirst().orElse(null));
    }
}
