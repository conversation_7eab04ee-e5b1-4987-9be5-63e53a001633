package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.HspOrganizationDto;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface HspOrganizationService {


    /**
     * 新增机构
     *
     * @param dto 新增参数
     */
    long addHspOrganization(HspOrganizationDto dto);


    /**
     * 根据机构编码查询机构
     *
     * @param hspOrgCode 机构编码
     * @return 机构dto
     */
    @Nullable
    HspOrganizationDto selectByHspOrgCode(String hspOrgCode);

    /**
     * 根据机构名称查询机构
     */
    @Nullable
    HspOrganizationDto selectByHspOrgName(String hspOrgName,long orgId);


    /**
     * 根据机构Id查询机构
     *
     * @param hspOrgId 机构id
     * @return 机构dto
     */
    @Nullable
    HspOrganizationDto selectByHspOrgId(long hspOrgId);

    /**
     * 根据机构Ids查询机构
     */
     List<HspOrganizationDto> selectByHspOrgIds(Collection<Long> hspOrgIds);

    /**
     * 查询全部机构
     *
     * @return 机构列表
     */
    @Nonnull
    List<HspOrganizationDto> selectAll();

    /**
     * 修改机构信息
     */
    void updateByHspOrgId(HspOrganizationDto hspOrganization);

    /**
     * 删除机构
     */
    void deleteByHspOrgIds(List<Long> hspOrgIds);


    /**
     * 根据编码查询
     */
    List<HspOrganizationDto> selectByHspOrgCodes(Collection<String> hspOrgCodes);

    Map<Long, HspOrganizationDto> selectByHspOrgIdsAsMap(Collection<Long> hspOrgIds);
}
