package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 样本结果备注
 */
@Getter
@Setter
public class SampleResultRemarkDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long sampleResultRemarkId;

    /**
     * 结果备注
     */
    private String resultRemark;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 是否异常
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;


    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;


    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

}
