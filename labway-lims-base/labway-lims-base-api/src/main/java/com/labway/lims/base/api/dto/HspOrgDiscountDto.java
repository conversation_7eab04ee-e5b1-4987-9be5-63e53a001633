package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户折扣信息
 */
@Getter
@Setter
public class HspOrgDiscountDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long discountId;

    /**
     * 基准包id
     */
    private Long packageId;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 送检类型编码
     */
    private String sendTypeCode;

    /**
     * 送检类型名称
     */
    private String sendType;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 折扣率
     */
    private BigDecimal discount;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除,0:未删
     */
    private Integer isDelete;

}
