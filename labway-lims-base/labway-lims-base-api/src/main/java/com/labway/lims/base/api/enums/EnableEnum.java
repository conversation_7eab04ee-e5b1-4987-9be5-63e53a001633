package com.labway.lims.base.api.enums;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Arrays;

@NoArgsConstructor
@AllArgsConstructor
/**
 * 启用状态描述
 * <AUTHOR>
 */
public enum EnableEnum {
    /**
     * 禁用
     */
    FORBIDDEN("禁用", 0),
    /**
     * 启用
     */
    ENABLE("启用", 1),
    ;


    /**
     * 启用状态 描述
     * 启用|禁用
     */
    private String enableStatusDesc;
    /**
     * 启用状态
     * 1启用 0禁用
     */
    private Integer enableStatusInt;

    /**
     * 根据int类型的编码查找枚举
     *
     * @param intStatus
     * @return
     */
    public static EnableEnum getEnableEnumInt(Integer intStatus) {
        return Arrays.stream(EnableEnum.values()).filter(i -> i.getEnableStatusInt().equals(intStatus)).findFirst().orElse(FORBIDDEN);
    }

    /**
     * 是否存在枚举
     *
     * @param intStatus
     * @return
     */
    public static boolean isExistStatusInt(Integer intStatus) {
        return Arrays.stream(EnableEnum.values()).anyMatch(i -> i.getEnableStatusInt().equals(intStatus));
    }

    public String getEnableStatusDesc() {
        return enableStatusDesc;
    }

    public void setEnableStatusDesc(String enableStatusDesc) {
        this.enableStatusDesc = enableStatusDesc;
    }

    public Integer getEnableStatusInt() {
        return enableStatusInt;
    }

    public void setEnableStatusInt(Integer enableStatusInt) {
        this.enableStatusInt = enableStatusInt;
    }

}
