package com.labway.lims.base.api.service.ref;

import com.labway.lims.base.api.dto.BarcodeSettingDto;

import java.io.Serializable;
import java.util.List;

public interface IBarcodeSettingServiceRef {

    /**
     * 根据 送检机构Code， 获取 n 个barcode
     * @param hspOrgCode  送检机构Code
     * @param number 获取多少个
     * @return 条码号
     */
    List<String> genBarcodes(String hspOrgCode, int number);

    /**
     * 根据 送检机构Code， 获取 n 个 master barcode
     * @param hspOrgCode  送检机构Code
     * @param number 获取多少个
     * @return 条码号
     */
    List<String> genMasterBarcodes(String hspOrgCode, int number);


    /**
     * 根据条码解析条码规则
     * @param barcode
     * @param barcodeType 1 条码，  2主条码 {@link BarcodeSettingDto.barcodeType }
     * @return
     */
    BarcodeSettingDto selectByBarcode(String barcode, int barcodeType);
}
