package com.labway.lims.base.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ItemPriceBaseFinanceDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 详情ID
     */
    private Long financeDetailId;

    /**
     * 基准包ID
     */
    private Long packageId;

    /**
     * 财务套餐价格code
     */
    private String combinePackageCode;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

}
