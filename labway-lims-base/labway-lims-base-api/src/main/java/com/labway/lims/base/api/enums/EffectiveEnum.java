package com.labway.lims.base.api.enums;

import java.util.Arrays;

/**
 * 有效性
 *
 * <AUTHOR>
 */
public enum EffectiveEnum {

    NOT_EFFECTIVE(0, "0", "无效"),
    EFFECTIVE(1, "1", "有效"),
    ALL(-1, "-1", "全部"),
    ;

    /**
     * integer类型 key
     */
    private Integer intCode;

    /**
     * string类型 key
     */
    private String strCode;

    /**
     * 值说明
     */
    private String value;


    EffectiveEnum(Integer intCode, String strCode, String value) {
        this.intCode = intCode;
        this.strCode = strCode;
        this.value = value;
    }

    /**
     * 通过Code获取枚举值
     *
     * @param code
     * @return
     */
    public static EffectiveEnum getValueByStrCode(Integer code) {
        return Arrays.stream(EffectiveEnum.values()).filter(item -> item.getIntCode().equals(code)).findFirst().orElse(ALL);
    }

    public Integer getIntCode() {
        return intCode;
    }

    public String getStrCode() {
        return strCode;
    }

    public String getValue() {
        return value;
    }
}
