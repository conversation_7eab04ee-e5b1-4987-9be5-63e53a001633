package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentReportItemExceptionDto;

import java.util.List;

public interface InstrumentReportItemExceptionService {
    /**
     * 根据仪器报告项目ID查询
     */
    List<InstrumentReportItemExceptionDto> selectByInstrumentReportItemId(long instrumentReportItemId);


    /**
     * 根据ID删除
     */
    void deleteByInstrumentReportItemExceptionId(long instrumentReportItemExceptionId);


    /**
     * 根据ID修改
     */
    boolean updateByInstrumentReportItemExceptionId(InstrumentReportItemExceptionDto dto);

    /**
     * 新增
     */
    long addInstrumentReportItemException(InstrumentReportItemExceptionDto dto);
}
