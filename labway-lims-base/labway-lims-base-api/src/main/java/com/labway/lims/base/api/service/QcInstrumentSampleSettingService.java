package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.QcInstrumentReportItemSampleNoDto;
import com.labway.lims.base.api.dto.QcInstrumentSampleSettingDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/1 10:49
 * @Version 1.0
 */
public interface QcInstrumentSampleSettingService {

    /**
     * 获取质控仪器下报告项的样本设置信息
     *
     * @param instrumentReportItemSampleNoDto
     * @return
     */
    List<QcInstrumentSampleSettingDto> queryQcInstrumentSampleSetting(QcInstrumentReportItemSampleNoDto instrumentReportItemSampleNoDto);

    /**
     * 查询仪器质控数据接收规则
     */
//    List<QcInstrumentSampleRuleDto> queryRulesByInstrumentInfo(List<QcInstrumentSampleRuleDto> dtos);

    /**
     * 统一置某个仪器设置为删除
     */
    void deleteSettingByInstrumentId(Long instrumentId);

    /**
     * 插入设置信息
     */
    void insertSampleSetting(QcInstrumentSampleSettingDto insertItems);

    /**
     * 根据仪器查找配置信息
     */
    List<QcInstrumentSampleSettingDto> querySettingListByInstrumentId(Long instrumentId);

    /**
     * 查询规则
     */
    List<QcInstrumentSampleSettingDto> queryRulesByInstrumentId(Long instrumentId, Long reportId);

}
