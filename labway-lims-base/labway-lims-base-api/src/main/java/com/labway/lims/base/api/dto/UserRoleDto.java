package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户角色
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class UserRoleDto extends RoleDto {

    private static final long serialVersionUID = 1L;


    /**
     * 是否是默认角色，1:是，0不是
     * @see YesOrNoEnum
     */
    private Integer isDefault;

    /**
     * 用户id
     */
    private Long userId;

}
