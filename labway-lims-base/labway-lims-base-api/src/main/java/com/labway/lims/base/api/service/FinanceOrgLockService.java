package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.FinanceOrgLockDto;
import com.labway.lims.base.api.dto.OrgLockQueryDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2023/5/11 20:48
 */
public interface FinanceOrgLockService {

    List<FinanceOrgLockDto> selectByLockDateAndHspOrgId(OrgLockQueryDto dto);

    /**
     * 根据机构ID查询加解锁记录
     */
    List<FinanceOrgLockDto> selectByHspOrgId(long hspOrgId);

    List<FinanceOrgLockDto> selectByHspOrgIds(Collection<Long> hspOrgIds);

    long add(FinanceOrgLockDto dto);

    void addBatch(List<FinanceOrgLockDto> orgLocks);

    /**
     * @param hspOrgId  hspOrgId
     * @param startTime startTime加锁开始时间戳
     * @param endTime   endTime 加锁结束时间戳
     * @return 返回的key是 redisPrefix.getBasePrefix() + "HSP_ORG_LOCK:" + hspOrgId + ":" + (startTime + endTime)
     */
    String getOrgLockKey(long hspOrgId, long startTime, long endTime);

    /**
     * 根据ID查询
     */
    @Nullable
    FinanceOrgLockDto selectByOrgLockRecordId(long orgLockRecordId);

}
