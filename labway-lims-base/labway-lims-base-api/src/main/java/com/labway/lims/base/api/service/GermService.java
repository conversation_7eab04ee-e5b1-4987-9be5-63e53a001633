package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.GermDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 细菌 Service
 * 
 * <AUTHOR>
 * @since 2023/3/20 17:40
 */
public interface GermService {
    /**
     * 添加细菌
     */
    long addGerm(GermDto germDto);

    /**
     * 删除细菌
     */
    void deleteByGermIds(Collection<Long> germIds);

    /**
     * 修改细菌
     */
    void updateByGermId(GermDto germDto);

    /**
     * 查询 细菌 通过 机构
     */
    List<GermDto> selectByOrgId(long orgId);

    /**
     * 查询 细菌 通过 细菌菌属 id List
     */
    List<GermDto> selectByGermGenusIds(Collection<Long> germGenusIds);

    /**
     * 查询 细菌 通过 细菌菌属 id List
     */
    List<GermDto> selectByGermGenusId(long germGenusId);

    /**
     * 根据细菌id查找细菌
     */
    @Nullable
    GermDto selectByGermId(long germId);

    /**
     * 根据细菌名称查找细菌
     */
    @Nullable
    GermDto selectByGermName(String germName, long orgId);

    /**
     * 根据细菌编码查找细菌
     */
    @Nullable
    GermDto selectByGermCode(String germCode, long orgId);

    List<GermDto> selectByGermIds(Collection<Long> ids);

    /**
     * 通过细菌编码获取
     */
    List<GermDto> selectByGermCodes(Collection<String> germCodes, long orgId);
}
