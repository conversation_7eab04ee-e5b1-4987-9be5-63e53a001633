package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 药物细菌关联表 Dto
 *
 * <AUTHOR>
 * @since 2023/4/20 19:27
 */
@Getter
@Setter
public class MedicineGermRelationDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 关联ID
     */
    private Long relationId;
    /**
     * 细菌菌属ID
     */
    private Long germGenusId;
    /**
     * 药物ID
     */
    private Long medicineId;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 1: 删除 0:未删
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人名称
     */
    private String updaterName;
    /**
     * 参考值上限值
     */
    @Compare("参考值上限")
    private String referValueMax;
    /**
     * 参考值下限值
     */
    @Compare("参考值下限")
    private String referValueMin;
    /**
     * 参考单位
     */
    @Compare("单位")
    private String referUnit;
    /**
     * 检测方法编码
     */
    private String examMethodCode;
    /**
     * 检测方法名称
     */
    @Compare("检测方法")
    private String examMethodName;

    /**
     * 是否耐药提醒:1是0否
     *
     * @see YesOrNoEnum
     */
    @Compare(value = "是否耐药提醒", content = {@CompareContent(value = "1", valueDesc = "是"), @CompareContent(value = "0", valueDesc = "否")})
    private Integer resistantWarn;
    /**
     * 报告顺序
     */
    @Compare("报告顺序")
    private String  reportSort;

    /**
     * 敏感度
     */
    @Compare("敏感度")
    private String susceptibility;

    /**
     * 折点范围
     */
    private String foldPointScope;
}
