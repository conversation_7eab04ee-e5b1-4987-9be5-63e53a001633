package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.MedicineGermRelationDto;

import java.util.Collection;
import java.util.List;

/**
 * 药物细菌关联表 Service
 * 
 * <AUTHOR>
 * @since 2023/4/21 9:10
 */
public interface MedicineGermRelationService {

    /**
     * 添加 药物细菌关联表
     */
    long addMedicineGermRelationDto(MedicineGermRelationDto dto);

    /**
     * 删除 药物细菌关联 根据药物
     */
    void deleteByMedicineIds(Collection<Long> medicineIds);

    /**
     * 删除 药物细菌关联 根据关联ids
     */
    void deleteByRelationIds(Collection<Long> relationIds);

    /**
     * 查询药物对应 细菌菌属
     */
    List<MedicineGermRelationDto> selectByMedicineId(long medicineId);

    /**
     * 查询药物对应 细菌菌属
     */
    List<MedicineGermRelationDto> selectByMedicineIds(Collection<Long> medicineIds);

    /**
     * 查询细菌菌属 对应药物
     */
    List<MedicineGermRelationDto> selectByGermGenusIds(Collection<Long> germGenusIds);

    /**
     * 查询细菌菌属 对应药物
     */
    List<MedicineGermRelationDto> selectByGermGenusId(long germGenusId);

    /**
     * 查询 细菌菌属 药物 下 数据
     */
    List<MedicineGermRelationDto> selectByGermGenusIdAndMedicineId(long germGenusId, long medicineId);

    /**
     * 细菌药物 保存
     */
    void medicineGermSave(List<MedicineGermRelationDto> addList, List<MedicineGermRelationDto> updateList);

    /**
     * 修改 细菌药物
     */
    void updateByRelationId(MedicineGermRelationDto dto);

    /**
     * 根据关联ids
     */
    List<MedicineGermRelationDto> selectByRelationIds(Collection<Long> relationIds);

}
