package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/10 13:42
 */
@Getter
@Setter
public class ReportTemplateDto  implements Serializable {


    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板类型ID
     */
    private Long templateTypeId;

    /**
     * 模板类型名称
     */
    private String templateTypeName;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 模板文件
     */
    private String templateFile;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 机构项目名称
     */
    private String orgItemName;

    /**
     * 参数1
     */
    private String param1;

    /**
     * 参数2
     */
    private String param2;

    /**
     * 参数3
     */
    private String param3;

    /**
     * 是否启用
     * @see YesOrNoEnum
     */
    private Integer enable;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;
}
