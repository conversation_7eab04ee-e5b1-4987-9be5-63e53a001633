package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.QcAddRecordInfoDto;
import com.labway.lims.base.api.dto.QcSetRecordItemDto;
import com.labway.lims.base.api.dto.RecordCalculateDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/3 15:19
 * @Version 1.0
 */
public interface QcLevelDensityService {

    /**
     * 保存质控水平浓度信息
     * @param qcAddSetRecordItemDto
     */
    void saveQcRecord(QcAddRecordInfoDto qcAddSetRecordItemDto);


    /**
     * 根据仪器报告项获取记录列表
     *
     * @param instrumentReportItemId    仪器报告项
     * @return
     */
    List<QcSetRecordItemDto> querySetRecordItemListReportItemId(Long instrumentReportItemId);

    /**
     * 根据质控设置批号来删除数据
     * @param qcRecordBatch
     * @param b
     */
    void deleteInfoByRecordBatch(String qcRecordBatch, boolean b);

    /**
     * 进行浓度计算
     */
    void calculateSampleRecord(RecordCalculateDto calculateDto);
}
