package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 仪器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class InstrumentDto implements Serializable {


    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 仪器名称
     */
    @Compare("仪器名称")
    private String instrumentName;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    @Compare("专业组名称")
    private String groupName;


    /**
     * 是否启动(0未启动 1启动)
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
            content = {@CompareContent(value = "1", valueDesc = "是"), @CompareContent(value = "0", valueDesc = "否")})
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
