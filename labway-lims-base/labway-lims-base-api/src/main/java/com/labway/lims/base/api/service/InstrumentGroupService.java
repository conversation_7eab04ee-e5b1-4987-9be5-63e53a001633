package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentGroupDto;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/21 17:06
 */
public interface InstrumentGroupService {
    /**
     * 新增专业小组
     * @param groupDto groupDto
     */
    long addInstrumentGroup(InstrumentGroupDto groupDto);

    /**
     * 更具id修改专业小组
     * @param dto InstrumentGroupDto
     */
    boolean updateByInstrumentGroupId(InstrumentGroupDto dto);

    /**
     * 根据ID删除专业小组
     * @param instrumentGroupId instrumentGroupId
     */
    boolean deleteByInstrumentGroupId(long instrumentGroupId);

    /**
     * 根据ID查询
     *
     * @param instrumentGroupId instrumentGroupId
     * @return InstrumentGroupDto
     */
    @Nullable
    InstrumentGroupDto selectByInstrumentGroupId(long instrumentGroupId);

    /**
     * 根据ID查询
     *
     * @param instrumentGroupCode instrumentGroupCode
     * @return InstrumentGroupDto
     */
    @Nullable
    InstrumentGroupDto selectByInstrumentGroupCode(String instrumentGroupCode,long orgId);

    /**
     * 根据ID查询
     */
    List<InstrumentGroupDto> selectByInstrumentGroupIds(Collection<Long> instrumentGroupIds);

    /**
     * 根据专业组ID查询专业小组
     *
     * @param groupId groupId
     * @return List<InstrumentGroupDto>
     */
    @Nonnull
    List<InstrumentGroupDto> selectByGroupId(long groupId);

    /**
     * 根据专业组ID查询专业小组
     *
     * @param groupIds groupId
     * @return List<InstrumentGroupDto>
     */
    List<InstrumentGroupDto> selectByGroupIds(Collection<Long> groupIds);

    /**
     * 查询全部
     *
     * @return List<InstrumentGroupDto>
     */
    @Nonnull
    List<InstrumentGroupDto> selectAll();

}
