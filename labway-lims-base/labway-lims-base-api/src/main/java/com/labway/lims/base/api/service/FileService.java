package com.labway.lims.base.api.service;


import com.labway.lims.base.api.dto.FileDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 文件
 */
public interface FileService {

    /**
     * 根据父文件ID查询
     */
    List<FileDto> selectByParentFileId(long parentFileId);


    /**
     * 根据文件ID查询
     */
    @Nullable
    FileDto selectByFileId(long fileId);


    /**
     * 根据文件ID查询
     */
    List<FileDto> selectByFileIds(Collection<Long> fileIds);


    /**
     * 添加文件
     */
    long addFile(FileDto file);

    /**
     * 根据文件ID删除
     */
    void deleteByFileId(long fileId);

    /**
     * 文件名是否存在
     */
    boolean exists(long parentFileId, String fileName);

    /**
     * 根据ID修改文件
     */
    boolean updateByFileId(FileDto file);
}
