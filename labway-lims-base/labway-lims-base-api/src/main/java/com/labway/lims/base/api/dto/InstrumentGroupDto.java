package com.labway.lims.base.api.dto;

import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 仪器专业小组
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class InstrumentGroupDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    @Compare("专业小组名称")
    private String instrumentGroupName;

    /**
     * 专业小组编码
     */
    private String instrumentGroupCode;

    /**
     * 二次分拣颜色
     */
    @Compare("二次分拣颜色")
    private String secondSortColor;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 样本号开始值
     */
    @Compare("样本号开始值")
    private String sampleStartValue;

    /**
     * 样本号结束值
     */
    @Compare("样本号结束值")
    private String sampleEndValue;

    /**
     * 排序
     */
    @Compare("优先序号")
    private Integer sort;

    /**
     * 是否启动(0未启动 1启动)
     * 
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
        content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
