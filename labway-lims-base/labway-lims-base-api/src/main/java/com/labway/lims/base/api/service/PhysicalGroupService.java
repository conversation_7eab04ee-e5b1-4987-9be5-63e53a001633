package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.PhysicalGroupDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 体检团体 Service
 * 
 * <AUTHOR>
 * @since 2023/3/27 15:55
 */
public interface PhysicalGroupService {
    /**
     * 添加 体检团体
     */
    long addPhysicalGroup(PhysicalGroupDto physicalGroupDto);

    /**
     * 删除 体检团体
     */
    void deleteByPhysicalGroupIds(Collection<Long> physicalGroupIds);

    /**
     * 修改 体检团体
     */
    void updateByPhysicalGroupId(PhysicalGroupDto physicalGroupDto);

    /**
     * 查询 体检团体 通过 机构
     */
    List<PhysicalGroupDto> selectByOrgId(long orgId);

    /**
     * 根据 体检团体名称 获取 体检团体信息
     */
    @Nullable
    PhysicalGroupDto selectByPhysicalGroupName(String physicalGroupName, long orgId);

    /**
     * 根据体检团体id查找体检团体
     */
    @Nullable
    PhysicalGroupDto selectByPhysicalGroupId(long physicalGroupId);

    /**
     * 根据体检团体ids查找体检团体
     */
    List<PhysicalGroupDto> selectByPhysicalGroupIds(Collection<Long> physicalGroupIds);

}
