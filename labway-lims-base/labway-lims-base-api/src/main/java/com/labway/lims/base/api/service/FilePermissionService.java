package com.labway.lims.base.api.service;


import com.labway.lims.base.api.dto.FilePermissionDto;

import java.util.Collection;
import java.util.List;

/**
 * 文件权限
 */
public interface FilePermissionService {
    /**
     * 根据文件ID查询
     */
    List<FilePermissionDto> selectByFileId(long fileId);

    /**
     * 根据ID删除
     */
    void deleteByFileId(long fileId);

    /**
     * 根据文件ID查询
     */
    List<FilePermissionDto> selectByFileIds(Collection<Long> fileIds);

    /**
     * 添加黑名单
     */
    void addFilePermissions(long fileId, Collection<Long> groupIds);
}
