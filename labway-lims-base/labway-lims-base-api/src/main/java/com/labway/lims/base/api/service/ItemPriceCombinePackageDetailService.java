package com.labway.lims.base.api.service;


import com.labway.lims.base.api.dto.TestItemDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * (TbItemPriceCombinePackageDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-04 19:56:44
 */
public interface ItemPriceCombinePackageDetailService {

    /**
     * 查询套餐的详情信息
     */
    Map<String, List<TestItemDto>> queryCombinePackageDetailInfo(Collection<String> combinePackageCodes);

    /**
     * 财务套餐下新增检验项目
     */
    boolean addCombinePackageDetailInfo(String combinePackageCode, Set<Long> testItemIds);
    /**
     * 删除套餐项目信息
     */
    boolean deleteCombinePackageDetailInfo(String combinePackageCode, Set<Long> testItemIds);

    /**
     * 根据套餐编码删除
     */
    boolean deleteByCombinePackageCode(String combinePackageCode);

    /**
     * 批量删除
     */
    boolean deleteByCombinePackageCodes(Collection<String> combinePackageCodeList);
}

