package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.CombinePackageInfoDto;
import com.labway.lims.base.api.dto.ItemPriceBaseFinanceDetailDto;
import com.labway.lims.base.api.vo.ItemPriceBaseFinanceDetailVo;
import com.labway.lims.base.api.vo.QueryCombinePackageListTestItemsVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface ItemPriceBaseFinanceDetailService {

    /**
     * 添加财务套餐基准包
     */
    boolean add(ItemPriceBaseFinanceDetailDto vo);

    /**
     * 批量删除关联关系
     */
    boolean deleteByFinanceDetailIds(Collection<Long> financeDetailIds);

    /**
     * 根据基准包id查询财务套餐项目
     */
    List<CombinePackageInfoDto> selectByPackageId(long packageId);

    /**
     * 根据基准包ids查询财务套餐项目
     * @return key = packageId(基准包详情)  value = 财务包详情
     */
    Map<Long, List<QueryCombinePackageListTestItemsVo>> selectByPackageIds(Collection<Long> packageIds);

    /**
     * 查询所有基准包下的所有套餐信息
     * @return key = packageId(基准包详情)  value = 财务包详情
     */
    Map<Long, List<CombinePackageInfoDto>> selectBaseFinanceDetailAllMap();

    /**
     * 查询所有基准包下的所有套餐信息
     * @return key = packageId(基准包详情)  value = 财务包详情 + 检验项目详情
     */
    Map<Long, List<QueryCombinePackageListTestItemsVo>> selectBaseFinanceDetailAndTestItemMap();


    /**
     * 批量删除
     */
    boolean deleteByFinanceDetail(long packageId, Collection<String> combinePackageCodes);

    /**
     * 查所有对照关系
     */
    List<ItemPriceBaseFinanceDetailVo> selectAll();
}
