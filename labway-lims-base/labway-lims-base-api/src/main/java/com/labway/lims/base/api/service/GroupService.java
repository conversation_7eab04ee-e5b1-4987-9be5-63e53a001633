
package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.UserProfessionalGroupDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 专业组
 */
public interface GroupService {
    /**
     * 根据用户查询到专业组
     */
    List<ProfessionalGroupDto> selectByUserId(long userId);

    /**
     * 根据用户查找专业组
     */
    List<UserProfessionalGroupDto> selectUserGroupByUserIds(Collection<Long> userIds);

    /**
     * 根据ID查询到专业组
     */
    @Nullable
    ProfessionalGroupDto selectByGroupId(long groupId);

    /**
     * 根据ID查询到专业组
     */
    List<ProfessionalGroupDto> selectByGroupIds(Collection<Long> groupIds);

    /**
     * 根据ID查询到专业组
     */
    Map<Long, ProfessionalGroupDto> selectByGroupIdsAsMap(Collection<Long> groupIds);

    /**
     * 查询委外专业组信息，根据系统设置中委外组code，可以取出来多个，以code分
     */
    Map<String, ProfessionalGroupDto> selectOutsourcingGroupMapByGroupCode(long orgId);

    /**
     * 判断该专业组是不是委外组
     */
    boolean checkIsOutsourcingGroup(Long userGroupId, Long orgId);

    /**
     * 根据编码查询专业组
     */
    @Nullable
    ProfessionalGroupDto selectByGroupCode(String groupCode, long orgId);

    /**
     * 根据编码查询专业组
     */
    Map<String, ProfessionalGroupDto> selectByGroupCodes(Collection<String> groupCodes, long orgId);

    /**
     * 根据名称查询专业组
     */
    @Nullable
    ProfessionalGroupDto selectByGroupName(String groupName);

    /**
     * 根据名称s查询专业组
     */
    List<ProfessionalGroupDto> selectByGroupNames(Collection<String> groupNames);

    /**
     * 添加专业组
     */
    long addGroup(ProfessionalGroupDto group);

    /**
     * 根据groupId修改group
     */
    void updateByGroupId(ProfessionalGroupDto group);

    /**
     * 查询所有的专业组
     */
    List<ProfessionalGroupDto> selectByOrgId(long orgId);

    /**
     * 查询所有的专业组
     */
    List<ProfessionalGroupDto> selectByAll(long orgId);

    /**
     * 批量删除专业组
     */
    void deleteByGroupIds(List<Long> groupIds);

    /**
     * 根据ID删除专业组
     */
    void deleteByGroupId(long groupId);

    /**
     * 获取到分血组。分血组在系统中固定，所以不需要任何参数
     */
    ProfessionalGroupDto selectSplitBloodGroup(long orgId);

    /**
     * 获取到委外组。委外组在系统中固定，所以不需要任何参数
     * @see #selectOutsourcingGroupMapByGroupCode
     * @deprecated
     */
    @Deprecated
    ProfessionalGroupDto selectOutsourcingGroup(long orgId);

}
