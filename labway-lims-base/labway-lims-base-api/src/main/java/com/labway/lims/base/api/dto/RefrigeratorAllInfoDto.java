package com.labway.lims.base.api.dto;

import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 冰箱 所有信息
 * 
 * <AUTHOR>
 * @since 2023/6/19 19:06
 */
@Getter
@Setter
public class RefrigeratorAllInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 冰箱ID
     */
    private Long refrigeratorId;
    /**
     * 冰箱编码
     */
    @Compare("冰箱编码")
    private String refrigeratorCode;
    /**
     * 冰箱名称
     */
    @Compare("冰箱名称")
    private String refrigeratorName;
    /**
     * 归档库名称
     */
    private String archiveStoreName;
    /**
     * 归档库ID
     */
    private Long archiveStoreId;
    /**
     * 位置
     */
    @Compare("冰箱位置")
    private String position;
    /**
     * 温度
     */
    @Compare("温度")
    private String temperature;

    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 是否启用(0未启用 1启用)
     *
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
        content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enable;
    /**
     * 是否删除(0 未删除 1已删除)
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人名称
     */
    private String updaterName;
    /**
     * 冰箱 归属 所有专业组 名称 以 、分割
     */
    @Compare("所属专业组")
    private String groupNameAllStr;

    /**
     * 专业组IDs
     */
    private Set<Long> groupIds;
}