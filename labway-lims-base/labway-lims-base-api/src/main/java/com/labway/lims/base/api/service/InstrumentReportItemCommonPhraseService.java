package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentReportItemCommonPhraseDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

public interface InstrumentReportItemCommonPhraseService {
    /**
     * 新增常用短语
     */
    long addInstrumentReportItemCommonPhrase(InstrumentReportItemCommonPhraseDto dto);


    /**
     * 根据 id 删除
     */
    boolean deleteByInstrumentReportItemCommonPhraseId(long instrumentReportItemCommonPhraseId);

    /**
     * 根据报告项目ID查询
     */
    List<InstrumentReportItemCommonPhraseDto> selectByInstrumentReportItemId(long instrumentReportItemId);

    /**
     * 根据报告项目ID查询
     */
    List<InstrumentReportItemCommonPhraseDto> selectByInstrumentReportItemIds(Collection<Long> instrumentReportItemIds);

    /**
     * 根据ID修改
     */
    boolean updateByInstrumentReportItemCommonPhraseId(InstrumentReportItemCommonPhraseDto dto);

    /**
     * 根据ID查询
     */
    @Nullable
    InstrumentReportItemCommonPhraseDto selectByInstrumentReportItemCommonPhraseId(long instrumentReportItemCommonPhraseId);

    /**
     * 根据仪器专业小组查询
     */
    List<InstrumentReportItemCommonPhraseDto> selectByInstrumentGroupId(long instrumentGroupId);

    /**
     * 根据 id 查询
     *
     * @return
     */
    List<InstrumentReportItemCommonPhraseDto> selectByIds(Collection<Long> ids);

    /**
     * 根据仪器专业小组查询
     */
    void deleteByInstrumentReportItemId(long instrumentReportItemId);

    List<Long> copyReportItemCommonPhrase(Long fromInstrumentReportItemId, InstrumentReportItemDto instrumentReportItemDto);

    /**
     * 根据报告项目编码查询，注意不会区分仪器，使用时请注意
     */
    List<InstrumentReportItemCommonPhraseDto> selectByReportItemCodes(Collection<String> reportItemCodes);
}
