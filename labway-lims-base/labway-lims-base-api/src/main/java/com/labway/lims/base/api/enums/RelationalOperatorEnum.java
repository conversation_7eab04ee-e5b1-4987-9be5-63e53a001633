package com.labway.lims.base.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 关系运算符
 *
 * <AUTHOR>
 * @since 2022/1/10 4:38 PM
 */
@Getter
@AllArgsConstructor
public enum RelationalOperatorEnum {
    /**
     * 等于
     */
    EQ("="),
    /**
     * 小于
     */
    LT("<"),
    /**
     * 大于
     */
    GT(">"),
    /**
     * 大于等于
     */
    GE(">="),
    /**
     * 小于等于
     */
    LE("<="),
    // 1.1.1 不需要下面这些类型
    /**
     * 不等于
     */
    //    NE,
    /**
     * 包含
     */
    CONTAINS("⊂"),
    /**
     * 不包含
     */
    DOES_NOT_CONTAINS("⊄"),

    /**
     * 默认， 不进行操作
     */
    DEFAULT(""),
    ;

    private final String formula;

    /**
     * 根据操作符获取
     *
     * @param operator > , < , <= , >= , = ,
     * @return RelationalOperatorEnum
     */
    public static RelationalOperatorEnum valueOfByOperator(String operator) {
        String operatorBak = StringUtils.defaultString(operator);
        switch (operatorBak) {
            case ">":
                return GT;
            case "<":
                return LT;
            case "<=":
                return LE;
            case ">=":
                return GE;
            case "=":
                return EQ;
            //            case "!=":
            //                return NE;
            case "⊂":
                return CONTAINS;
            case "⊄":
                return DOES_NOT_CONTAINS;
            default:
                return DEFAULT;
        }
    }

    public boolean compare(BigDecimal a, BigDecimal b) {
        final int i = a.compareTo(b);
        if (this == EQ) {
            return i == 0;
            //        } else if (this == NE) {
            //            return i != 0;
        } else if (this == LT) {
            return i < 0;
        } else if (this == GT) {
            return i > 0;
        } else if (this == LE) {
            return i < 1;
        } else if (this == GE) {
            return i > -1;
        }
        return false;
    }

    public boolean compare(double a, double b) {
        return compare(BigDecimal.valueOf(a), BigDecimal.valueOf(b));
    }

    public boolean compare(long a, long b) {
        return compare(BigDecimal.valueOf(a), BigDecimal.valueOf(b));
    }

    public boolean compare(int a, int b) {
        return compare(BigDecimal.valueOf(a), BigDecimal.valueOf(b));
    }

    public boolean compare(String a, String b) {
        if (this == EQ) {
            return Objects.equals(a, b);
            //        } else if (this == NE) {
            //            return !Objects.equals(a, b);
            //        } else if (this == DOES_NOT_CONTAINS) {
            //            return !a.contains(b);
            //        } else if (this == CONTAINS) {
            //            return a.contains(b);
        }
        throw new UnsupportedOperationException();
    }

    public static List<String> getFormatList() {
        return Arrays.stream(RelationalOperatorEnum.values()).map(RelationalOperatorEnum::getFormula)
                .filter(e -> !Objects.equals(e, DEFAULT.formula))
                .collect(Collectors.toList());
    }


}
