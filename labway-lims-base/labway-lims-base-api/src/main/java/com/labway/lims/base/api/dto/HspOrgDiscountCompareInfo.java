package com.labway.lims.base.api.dto;

import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 客户折扣维护 比较信息
 *
 * <AUTHOR>
 * @since 2023/8/7 16:53
 */
@Getter
@Setter
public class HspOrgDiscountCompareInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 基准包名称
     */
    @Compare(value = "基准包名称")
    private String packageName;

    /**
     * 送检类型名称
     */
    @Compare(value = "送检类型")
    private String sendType;

    /**
     * 开始时间
     */
    @Compare(value = "生效时间")
    private String startDateStr;

    /**
     * 结束时间
     */
    @Compare(value = "结束时间")
    private String endDateStr;
    /**
     * 折扣率
     */
    @Compare(value = "折扣率")
    private BigDecimal discount;

}
