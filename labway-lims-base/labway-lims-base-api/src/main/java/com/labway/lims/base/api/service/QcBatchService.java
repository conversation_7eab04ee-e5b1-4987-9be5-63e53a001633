package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.QcBatchDto;
import com.labway.lims.base.api.dto.QcBatchReportItemDto;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 质控批号维护服务层
 */
public interface QcBatchService {

    /**
     * 添加质控批号
     */
    long add(QcBatchDto qcBatchDto);

    /**
     * 根据仪器编码查询
     */
    List<QcBatchDto> selectByInstrumentCode(long groupId, String instrumentCode);

    void update(QcBatchDto qcBatchDto);

    /**
     * 质控批号列表
     */
    List<QcBatchDto> list(String instrumentCode, String qcBatch);

    /**
     * 删除质控批号
     */
    void deleteQcBatch(Collection<Long> qcBatchIds);

    /**
     *
     * 复制质控批号
     *
     * @param source 源质控批号
     * @param sourceItems 源质控批号下报告项目
     * @param target 新的质控批号
     */
    void cloneRow(QcBatchDto source, List<QcBatchReportItemDto> sourceItems, QcBatchDto target);

    /**
     * 通过质控 批号id
     */
    QcBatchDto selectByQcBatchId(long qcBatchId);

    /**
     * 通过质控 批号id 批量查询
     */
    List<QcBatchDto> selectByQcBatchIds(Collection<Long> qcBatchIds);

    /**
     * 检查是否存在交集数据 排除
     */
    boolean isExistIntersectionData(long groupId, String instrumentCode, String qcBatch, Date startDate, Date endDate,
        long excludeQcBatchId);

    /**
     * 根据 仪器code + 时间范围 查询质控维护
     */
    List<QcBatchDto> selectByByInstrumentCodeAndDateRange(Collection<String> instrumentCodes, Date minDate,
        Date maxDate);

    /**
     * 获取 此专业组 下 此仪器 此时间段 有交集的 质控批号
     */
    List<QcBatchDto> selectIntersectionData(long groupId, String instrumentCode, Date startDate, Date endDate);
}