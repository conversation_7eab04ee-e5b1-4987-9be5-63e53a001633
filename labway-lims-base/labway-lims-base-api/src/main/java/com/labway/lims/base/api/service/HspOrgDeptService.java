package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.HspOrgDeptDto;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/27 11:21
 */
public interface HspOrgDeptService {
    List<HspOrgDeptDto> selectByHspOrgMainId(long hspOrgMainId);

    void deleteByHspOrgDeptIds(Collection<Long> hspOrgDeptIds);

    long add(HspOrgDeptDto dto);

    void addBatch(Collection<HspOrgDeptDto> dtos);

    void deleteByByHspOrgMainIds(Collection<Long> hspOrgMainIds);

    /**
     * 
     * 通过部门ids 获取
     */
    List<HspOrgDeptDto> selectByHspOrgDeptIds(Collection<Long> hspOrgDeptIds);

}
