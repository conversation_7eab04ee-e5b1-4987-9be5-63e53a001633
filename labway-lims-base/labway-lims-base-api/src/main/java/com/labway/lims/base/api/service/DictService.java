package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.DictItemDto;

import java.util.Collection;
import java.util.List;

/**
 * 字典服务
 */
public interface DictService {

    /**
     * 根据字典类型和字典名称查询
     * 
     * @see com.labway.lims.api.enums.base.DictEnum
     */
    List<DictItemDto> selectByDictType(String dictType);

    /**
     * 根据字典类型和字典名称查询
     * 
     * @see com.labway.lims.api.enums.base.DictEnum
     */
    List<DictItemDto> selectByDictTypes(Collection<String> dictType);

    /**
     * 添加字典
     */
    long addDictItem(DictItemDto dictItem);

    /**
     * 修改字典
     */
    void updateDictItem(DictItemDto dictItem);

    /**
     * 删除字典根据字典ID
     */
    void deleteByDictId(Long dictId);

    /**
     * 批量删除字典
     */
    void deleteByDictIds(List<Long> dictIds);

    /**
     * 根据字典编码查询
     */
    DictItemDto selectByDictCode(String dictCode);

    /**
     * 根据字典编码查询
     */
    DictItemDto selectByDictId(long dictId);

    /**
     * 根据字典id查询
     */
    List<DictItemDto> selectByDictIds(Collection<Long> dictIds);

    /**
     * 获取当前机构的全部字典
     */
    List<DictItemDto> selectAll();

    /**
     * 查询所有样本类型
     */
    List<DictItemDto> selectAllSampleType();

    /**
     * 从业务中台获取异常原因
     */
    List<DictItemDto> selectAbnormalReasonsFromBusinessCenter();
}
