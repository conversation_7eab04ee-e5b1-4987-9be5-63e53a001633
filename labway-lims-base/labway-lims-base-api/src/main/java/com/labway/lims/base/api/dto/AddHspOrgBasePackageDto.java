package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 添加机构基准包折扣
 */

@Getter
@Setter
public class AddHspOrgBasePackageDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 送检机构id
     */
    private Set<Long> hspOrgIds;

    /**
     * 基准包id
     */
    private Long packageId;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 折扣率
     */
    private BigDecimal discount;

    /**
     * 添加的就诊类型
     */
    private List<AddHspOrgBasePackageSendTypeDto> sendTypeList;
}
