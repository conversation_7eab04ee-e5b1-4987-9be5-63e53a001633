package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.RefrigeratorDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 冰箱 Service
 *
 * <AUTHOR>
 * @since 2023/4/3 10:59
 */
public interface RefrigeratorService {
    /**
     * 添加 冰箱
     */
    long addRefrigerator(RefrigeratorDto refrigeratorDto);

    /**
     * 添加 冰箱
     * 
     * @param refrigeratorDto 冰箱
     * @param professionalGroupDtos 与之关联的专业组
     * @return
     */
    long addRefrigerator(RefrigeratorDto refrigeratorDto, List<ProfessionalGroupDto> professionalGroupDtos);

    /**
     * 删除 冰箱
     */
    void deleteByRefrigeratorIds(Collection<Long> refrigeratorIds);

    /**
     * 修改 冰箱
     */
    void updateByRefrigeratorId(RefrigeratorDto refrigeratorDto);

    /**
     * 查询 冰箱 通过 机构
     */
    List<RefrigeratorDto> selectByOrgId(long orgId);

    /**
     * 根据 冰箱id 查找 冰箱
     */
    @Nullable
    RefrigeratorDto selectByRefrigeratorId(long refrigeratorId);

    /**
     * 根据 冰箱名称 查找 冰箱
     */
    @Nullable
    RefrigeratorDto selectByRefrigeratorName(String refrigeratorName, long orgId);

    /**
     * 根据 冰箱编码 查找 冰箱
     */
    @Nullable
    RefrigeratorDto selectByRefrigeratorCode(String refrigeratorCode, long orgId);

    /**
     * 查询 冰箱 通过 归档库
     */
    List<RefrigeratorDto> selectByArchiveStoreId(long archiveStoreId);

    /**
     * 根据 冰箱ids 查找 冰箱
     */
    List<RefrigeratorDto> selectByRefrigeratorIds(Collection<Long> refrigeratorIds);

    /**
     * 检查归档库使用情况 中 是否存在 冰箱
     *
     * @param archiveStoreIds 归档库id
     * @return true 存在冰箱 false 不存在冰箱
     */
    boolean checkArchiveStoreUseByIds(Collection<Long> archiveStoreIds);

    /**
     * 冰箱信息修改
     * 
     * @param refrigeratorDto 冰箱
     * @param needAddRelationGroupList 需要新增的专业组关联
     * @param needDeleteRelationIdList 需要删除的关联id
     */
    void refrigeratorUpdate(RefrigeratorDto refrigeratorDto, List<ProfessionalGroupDto> needAddRelationGroupList,
        Collection<Long> needDeleteRelationIdList);

}
