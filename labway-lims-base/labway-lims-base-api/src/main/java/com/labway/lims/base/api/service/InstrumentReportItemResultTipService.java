package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentReportItemResultTipDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 结果值提示
 */
public interface InstrumentReportItemResultTipService {
    /**
     * 新增结果值提示
     */
    long addInstrumentReportItemResultTip(InstrumentReportItemResultTipDto dto);

    /**
     * 根据 id 删除
     */
    void deleteByInstrumentReportItemResultTipId(long instrumentReportItemResultTipId);

    /**
     * 查询所有
     */
    List<InstrumentReportItemResultTipDto> selectByOrgId(long orgId);

    /**
     * 根据送检机构查询
     */
    List<InstrumentReportItemResultTipDto> selectByHspOrgId(long hspOrgId);

    /**
     * 根据ID查询
     */
    @Nullable
    InstrumentReportItemResultTipDto selectInstrumentReportItemResultTipId(long instrumentReportItemResultTipId);

    /**
     * 根据ID修改
     */
    boolean updateByInstrumentReportItemResultTipId(InstrumentReportItemResultTipDto dto);


    /**
     * 根据仪器查询
     */
    List<InstrumentReportItemResultTipDto> selectByInstrumentIds(Collection<Long> instrumentIds);

    /**
     * 根据仪器查询
     */
    List<InstrumentReportItemResultTipDto> selectByInstrumentId(long instrumentId);


    List<InstrumentReportItemResultTipDto> selectInstrumentReportItemResultTipIds(Collection<Long> ids);
}
