package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.SampleResultRemarkDto;

import javax.annotation.Nullable;
import java.util.List;

public interface SampleResultRemarkService {
    /**
     * 根据 org ID查询
     */
    List<SampleResultRemarkDto> selectByOrgId(long orgId);


    /**
     * 根据 group ID查询
     */
    List<SampleResultRemarkDto> selectByGroupId(long groupId);


    /**
     * 根据 ID查询
     */
    @Nullable
    SampleResultRemarkDto selectBySampleResultRemarkId(long sampleResultRemarkId);


    /**
     * 根据ID删除
     */
    void deleteBySampleResultRemarkId(long sampleResultRemarkId);


    /**
     * 根据ID修改
     */
    boolean updateBySampleResultRemarkId(SampleResultRemarkDto dto);

    /**
     * 新增
     */
    long addSampleResultRemark(SampleResultRemarkDto dto);
}
