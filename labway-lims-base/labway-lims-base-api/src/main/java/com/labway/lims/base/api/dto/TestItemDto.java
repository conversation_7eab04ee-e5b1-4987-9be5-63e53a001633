package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 检验项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class TestItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 别名
     */
    @Compare("项目别名")
    private String aliasName;

    /**
     * 检验方法ID
     */
    private String examMethodCode;

    /**
     * 检验方法名称
     */
    @Compare("检验方法")
    private String examMethodName;

    /**
     * 名称缩写
     */
    @Compare("项目名称缩写")
    private String shortName;

    /**
     * 管型 code
     */
    private String tubeCode;

    /**
     * 管型 name
     */
    @Compare("管型")
    private String tubeName;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    @Compare("专业组")
    private String groupName;

    /**
     * 是否支持外送
     *
     * @see YesOrNoEnum
     */
    @Compare(value = "是否外送",
            content = {@CompareContent(value = "1", valueDesc = "是"), @CompareContent(value = "0", valueDesc = "否")})
    private Integer enableExport;

    /**
     * 外送回传报告时间
     */
    @Compare("外送回传报告时间")
    private BigDecimal exportDate;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

    /**
     * 外送机构名称
     */
    @Compare("外送机构")
    private String exportOrgName;

    /**
     * 项目类型ID
     *
     * @see ItemTypeEnum
     */
    @Compare("项目类型")
    private String itemType;

    /**
     * 样本类型ID
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;

    /**
     * 是否it3000
     */
    @Compare(value = "是否IT3000",
            content = {@CompareContent(value = "1", valueDesc = "是"), @CompareContent(value = "0", valueDesc = "否")})
    private Integer enableIt3000;

    /**
     * 是否计费
     */
    @Compare(value = "是否计费",
            content = {@CompareContent(value = "1", valueDesc = "是"), @CompareContent(value = "0", valueDesc = "否")})
    private Integer enableFee;

    /**
     * 收费编码
     */
    @Compare("收费编码")
    private String feeCode;

    /**
     * 收费名称
     */
    @Compare("收费名称")
    private String feeName;

    /**
     * 收费价格
     */
    @Compare("收费价格")
    private BigDecimal feePrice;

    /**
     * 财务专业组code
     */
    private String financeGroupCode;

    /**
     * 财务专业组名称
     */
    @Compare("财务专业组")
    private String financeGroupName;

    /**
     * 基本量
     */
    @Compare("基本量")
    private BigDecimal basicQuantity;

    /**
     * 复查量
     */
    @Compare("复查量")
    private BigDecimal checkQuantity;

    /**
     * 死腔量
     */
    @Compare("死腔量")
    private BigDecimal deadSpaceQuantity;

    /**
     * 存放说明
     */
    private String stashRemark;

    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 机构 ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 1:已经删除 0未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;


    /**
     * 是否启用(0未启动 1启动)
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 检验日期
     */
    private Integer testDate;

    /**
     * 限制性别
     * 0 不限制，1 男，2 女
     */
    @Compare("限制性别")
    private Integer limitSex;

    /**
     * 检验项目报告项
     */
    List<ReportItemDto> reportItems;

    /**
     * 二次分拣日期(0:当时、1~7周一到周日、多个逗号隔开)
     */
    private String twoPickDay;

    /**
     * 二次分拣时间(精确到分钟)
     */
    private String twoPickTime;

}
