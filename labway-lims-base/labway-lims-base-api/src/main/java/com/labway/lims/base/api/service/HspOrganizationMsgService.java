package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.HspOrganizationMsgDto;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface HspOrganizationMsgService {
    /**
     * 添加机构提示消息
     */
    void addHspOrgTipMsg(HspOrganizationMsgDto msg);

    /**
     * 修改机构提示消息根据消息ID
     */
    void updateTipMsgByMsgId(HspOrganizationMsgDto msg);

    /**
     * 查询所有的机构提示消息
     */
    List<HspOrganizationMsgDto> selectAllHspOrgTipMsg();

    /**
     * 删除提示消息
     */
    void deleteHspOrgTipMsgByMsgId(long msgId);

    /**
     * 根据消息id查询提示消息
     */
    HspOrganizationMsgDto selectTipMsgByMsgId(Long msgId);

    /**
     * 根据消息ids查询提示消息
     */
    List<HspOrganizationMsgDto> selectTipMsgByMsgIds(Collection<Long> msgIds);

    /**
     * 根据送检机构id查询提示消息
     */
    List<HspOrganizationMsgDto> selectTipMsgByHspOrgId(Long hspOrgId);

    /**
     * 删除机构提示消息
     */
    void deleteHspOrgTipMsgByMsgIds(List<Long> msgIds);

    /**
     * 根据机构id+位置id查询提示消息
     */
    HspOrganizationMsgDto selectByHspOrgIdAndPositionId(long hspOrgId, String msgPositionCode);
}
