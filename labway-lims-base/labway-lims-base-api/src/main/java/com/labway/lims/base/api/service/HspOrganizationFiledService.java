package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.HspOrganizationFiledDto;
import com.labway.lims.base.api.dto.SaveHspOrganizationFiledDto;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface HspOrganizationFiledService {
    /**
     * 添加双输对照内容
     */
    Set<Long> addHspOrganizationFiled(SaveHspOrganizationFiledDto dto);

    /**
     * 根据送检机构查询双输对照内容
     */
    List<HspOrganizationFiledDto> selectByHspOrgId(Long hspOrgId);

    /**
     * 修改双输对照字段 先删后增
     */
    Set<Long> update(SaveHspOrganizationFiledDto dto);

    /**
     * 删除双输对照字段
     */
    void deleteByHspOrgId(long hspOrgId);

    /**
     * 删除双输对照字段
     */
    void deleteByHspOrgIds(Collection<Long> hspOrgIds);
}
