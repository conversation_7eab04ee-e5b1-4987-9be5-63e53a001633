package com.labway.lims.base.api.dto;

import cn.hutool.core.lang.Assert;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.Serializable;
import java.util.Objects;

@Data
public class BarcodeSettingDto implements Serializable {

    private static final long serialVersionUID = -6849794470754667710L;


    private Long barcodeSettingId;

    /**
     * 机构id
     */
    private Long hspOrgId;

    /**
     * 机构编码
     */
    private String hspOrgCode;

    /**
     * 机构名称
     */
    private String hspOrgName;

    /**
     * 起始编码
     */
    private String startCode;

    /**
     * 当前序号
     */
    private Long currentNumber;

    /**
     * 位数
     */
    private Integer barcodePlace;

    /**
     * 是否启用
     */
    private Boolean enable;


    /**
     * 条码类型  1条码  2主条码
     */
    private Integer barcodeType;

    /**
     *
     */// 条码
    public static final Integer BARCODE_TYPE = NumberUtils.INTEGER_ONE;
    // 主条码
    public static final Integer MASTER_BARCODE_TYPE = NumberUtils.INTEGER_TWO;

    public void verifyAddParams() {
        if (Objects.isNull(hspOrgId) || StringUtils.isBlank(hspOrgCode) || StringUtils.isBlank(hspOrgName)) {
            throw new IllegalArgumentException("送检机构不能为空！");
        }
        if (StringUtils.isBlank(startCode)) {
            throw new IllegalArgumentException("起始编码不能为空");
        }
        Assert.notNull(barcodePlace, "位数不能为空");
        Assert.notNull(enable, "是否启用不能为空");

        if (barcodePlace <= startCode.length()) {
            throw new IllegalArgumentException("条码位数必须大于起始编码的长度");
        }

        if(Objects.isNull(barcodeType) || (!Objects.equals(barcodeType, BARCODE_TYPE) && !Objects.equals(barcodeType, MASTER_BARCODE_TYPE))){
            throw new IllegalArgumentException("条码类型不能为空且 条码【1】  主条码【2】");
        }
    }

    public void verifyUpdateParams() {
        Assert.notNull(barcodeSettingId, "条码维护id不能为空");
        verifyAddParams();
    }
}
