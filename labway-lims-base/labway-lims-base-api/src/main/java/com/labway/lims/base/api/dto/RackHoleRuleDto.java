package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.base.RackHoleColumnOrderEnum;
import com.labway.lims.api.enums.base.RackHolePriorityOrderEnum;
import com.labway.lims.api.enums.base.RackHoleRowOrderEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 试管架 孔位排序规则
 * 
 * <AUTHOR>
 * @since 2023/4/14 10:24
 */
@Getter
@Setter
public class RackHoleRuleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 行顺序
     */
    private RackHoleRowOrderEnum rowOrder = RackHoleRowOrderEnum.UP_TO_DOWN;

    /**
     * 列顺序
     */
    private RackHoleColumnOrderEnum columnOrder = RackHoleColumnOrderEnum.LEFT_TO_RIGHT;

    /**
     * 优先顺序 默认 列有限
     */
    private RackHolePriorityOrderEnum priority = RackHolePriorityOrderEnum.COLUMN_PRIORITY;

}
