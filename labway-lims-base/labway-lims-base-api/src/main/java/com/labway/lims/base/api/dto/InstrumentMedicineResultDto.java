package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仪器药物
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
@Getter
@Setter
public class InstrumentMedicineResultDto extends InstrumentMedicineDto {

    /**
     * 组别
     */
    private String group;

    /**
     * 药物简称
     */
    private String ab;

    /**
     * 药物名称
     */
    private String name;

    /**
     * 药物结果前缀
     */
    private String formula;

    /**
     * 敏感度
     */
    private String susceptibility;

    /**
     * 结果
     */
    private String result;

    /**
     * 折点范围
     */
    private String foldPointScope;

    /**
     * 单位（仪器过来， 默认是 ug/ml）
     * ug/ml  仪器法
     * mm  手工法
     */
    private String unit;

}
