package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 冰箱专业组关联表 Dto
 * 
 * <AUTHOR>
 * @since 2023/4/3 15:14
 */
@Getter
@Setter
public class RefrigeratorGroupDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 关联关系ID
     */
    private Long relationId;
    /**
     * 冰箱ID
     */
    private Long refrigeratorId;
    /**
     * 冰箱编码
     */
    private String refrigeratorCode;
    /**
     * 专业组ID
     */
    private Long groupId;
    /**
     * 专业组编码
     */
    private String groupCode;
    /**
     * 是否删除(0 未删除 1已删除)
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人名称
     */
    private String updaterName;

}
