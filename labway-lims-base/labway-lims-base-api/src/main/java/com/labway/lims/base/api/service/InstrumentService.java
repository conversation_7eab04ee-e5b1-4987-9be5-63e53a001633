package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 仪器
 */
public interface InstrumentService {

    /**
     * 根据专业组查询
     */
    List<InstrumentDto> selectByGroupId(long groupId);

    /**
     * 根据专业组s查询仪器项目
     * @param groupIds
     * @return
     */
    List<InstrumentDto> selectByGroupIds(List<Long> groupIds, String cacheKey);

    /**
     * 根据机构查询
     */
    List<InstrumentDto> selectByOrgId(long orgId);

    /**
     * 添加仪器
     */
    long addInstrument(InstrumentDto instrument);

    /**
     * 修改仪器
     */
    boolean updateByInstrumentId(InstrumentDto instrument);

    /**
     * 根据id删除
     */
    boolean deleteByInstrumentId(long instrumentId);

    /**
     * 根据仪器id查询
     */
    @Nullable
    InstrumentDto selectByInstrumentId(long instrumentId);

    /**
     * 根据专业小组ID查询
     */
    List<InstrumentDto> selectByInstrumentGroupId(long instrumentGroupId);

    /**
     * 根据仪器code查询
     */
    @Nullable
    InstrumentDto selectByInstrumentCode(String instrumentCode,long orgId);

    /**
     * 根据仪器id查询
     */
    List<InstrumentDto> selectByInstrumentIds(Collection<Long> instrumentIds);

    /**
     * 根据仪器id查询
     */
    Map<Long, InstrumentDto> selectByInstrumentIdsAsMap(Collection<Long> instrumentIds);

    /**
     * 根据专业组和仪器编码查询
     */
    InstrumentDto selectByGroupIdAndInstrumentCode(long groupId, String instrumentCode);

    /**
     * 根据机构id 与 仪器编码 获取 仪器
     */
    InstrumentDto selectByOrgIdAndInstrumentCode(long orgId, String instrumentCode);


    /**
     * 根据机构id 与 仪器编码 获取 仪器
     *
     */
    List<InstrumentDto> selectByOrgIdAndInstrumentCodes(long orgId, Collection<String> instrumentCodes);

    /**
     * 查询所有仪器
     * @return
     */
    List<InstrumentDto> selectAllInstrument();
}
