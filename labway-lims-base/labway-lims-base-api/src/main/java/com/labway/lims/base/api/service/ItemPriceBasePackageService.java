package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.ItemPriceBasePackageDto;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 项目价格基准包服务层
 */
public interface ItemPriceBasePackageService {

    /**
     * 添加基准包
     */
    long add(ItemPriceBasePackageDto dto);

    /**
     * 根据时间区间查询基准包，
     */
    List<ItemPriceBasePackageDto> selectByDateInterval(Date start, Date end);

    /**
     * 根据基准包名称查询基准包
     */
    ItemPriceBasePackageDto selectByName(String name);

    /**
     * 基准包列表
     */
    List<ItemPriceBasePackageDto> list();

    /**
     * 修改项目基准包
     */
    void update(ItemPriceBasePackageDto parseObject);

    /**
     * 删除基准包根据id
     */
    void delete(List<Long> packageIds);

    /**
     * 根据id查询基准包
     *
     * @return
     */
    ItemPriceBasePackageDto selectById(Long packageId);

    /**
     * 查询与时间范围存在交集的基准包
     */
    List<ItemPriceBasePackageDto> selectByDateRange(Date startDate, Date endDate);

    /**
     * 根据id集合查询基准包
     *
     * @return
     */
    List<ItemPriceBasePackageDto> selectByIds(Collection<Long> ids);

    /**
     * 获取 项目 实际收费价格
     * 
     * @param hspOrgId 送检机构
     * @param applyTypeCode 就诊类型
     * @param sampleItemCreateDate 样本检验项目创建时间
     * @param testItemIds 检验项目ids
     * @return key: 检验项目id value: 实际收费价格
     */
    Map<Long, BigDecimal> selectActualFeePrice(Long hspOrgId, String applyTypeCode, Date sampleItemCreateDate,
        Collection<Long> testItemIds);

}
