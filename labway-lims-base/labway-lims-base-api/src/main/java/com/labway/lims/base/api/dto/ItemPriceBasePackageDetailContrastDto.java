package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class ItemPriceBasePackageDetailContrastDto implements Serializable {

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 专业组
     */
    private String groupName;

    /**
     * 收费价格
     */
    private BigDecimal price;

    /**
     * 明细id
     */
    private Long detailId;

    /**
     * 财务专业组名称
     */
    private String financeGroupName;


    /**
     * 检验方法名称
     */
    private String examMethodName;
    /**
     * 是否启用(0未启动 1启动)
     * @see YesOrNoEnum
     */
    private Integer enable;
}
