package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class UpdateSpecialOfferDto {

    /**
     * 折扣id
     */
    private Long offerId;

    /**
     * 送检类型编码
     */
    private String sendTypeCode;

    /**
     * 送检类型
     */
    private String sendType;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 折扣率
     */
    private BigDecimal discount;

    /**
     * 是否阶梯折扣 1是 0否
     *
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer isTieredPricing;
    /**
     * 折后价格
     */
    private BigDecimal discountPrice;
    /**
     * 折前价格
     */
    private BigDecimal feePrice;
}
