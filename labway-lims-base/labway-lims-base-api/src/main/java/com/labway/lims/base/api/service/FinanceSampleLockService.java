package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.FinanceSampleLockDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/5/12 14:07
 */
public interface FinanceSampleLockService {
    List<FinanceSampleLockDto> selectByApplySampleId(long applySampleId);

    void addBatch(Collection<FinanceSampleLockDto> dtos);

    boolean updateByApplySampleId(FinanceSampleLockDto dto);

    Map<Long,List<FinanceSampleLockDto>> selectByApplySampleIdsAsMap(Collection<Long> applySampleIds);

    /**
     * 获取样本加解锁redis key
     */
    String getSampleLockKey(long applySampleId);

    /**
     * 是否进行样本加解锁
     */
    boolean isSampleLock(long applySampleId);
}
