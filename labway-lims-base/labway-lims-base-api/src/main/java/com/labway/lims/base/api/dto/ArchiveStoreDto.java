package com.labway.lims.base.api.dto;

import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 归档库 Dto
 * 
 * <AUTHOR>
 * @since 2023/4/3 11:12
 */
@Getter
@Setter
public class ArchiveStoreDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 归档库ID
     */
    private Long archiveStoreId;
    /**
     * 归档库编码
     */
    private String archiveStoreCode;

    /**
     * 归档库名称
     */
    @Compare("归档库名称")
    private String archiveStoreName;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 是否启用(0未启用 1启用)
     * 
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
        content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enable;
    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 1:已经删除 0未删除
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;
}
