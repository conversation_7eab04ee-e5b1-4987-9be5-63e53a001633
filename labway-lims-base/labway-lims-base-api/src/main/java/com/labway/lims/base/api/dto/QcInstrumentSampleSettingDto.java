package com.labway.lims.base.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 和QcInstrumentSampleSetting实体类保持一一对应
 * <AUTHOR>
 * @Date 2023/11/1 10:22
 * @Version 1.0
 */
@Data
public class QcInstrumentSampleSettingDto implements Serializable {
    /**
     * 质控样本设置ID
     */
    private Long qcSampleSettigId;

    /**
     * 质控样品号
     */
    private String qcSampleNo;

    /**
     * 样本内容
     */
    private String qcSampleContent;

    /**
     * 仪器编码ID
     */
    private Long instrumentId;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 报告项目id
     */
    private Long reportItemId;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 仪器通道编码：用于机器识别报告项；在仪器表中有维护
     */
    private String instrumentChannel;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 规则集合
     */
    private String rules;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建人名
     */
    private String createName;

    /**
     * 更新人id
     */
    private Long updateId;

    /**
     * 更新人名
     */
    private String updateName;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 自定义属性1
     */
    private String selfAttribute1;

    /**
     * 自定义属性2
     */
    private String selfAttribute2;

    /**
     * 自定义属性3
     */
    private String selfAttribute3;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 专业组id
     */
    private Long groupId;

    /**
     * 专业组编码
     */
    private String groupCode;
    /**
     * 专业组名称
     */
    private String groupName;

    private static final long serialVersionUID = 1L;
}