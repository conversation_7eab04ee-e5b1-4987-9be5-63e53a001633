package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.base.RackTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 查询分页 参数
 * 
 * <AUTHOR>
 * @since 2023/10/11 19:26
 */
@Getter
@Setter
public class QueryRackPageDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 专业组编码
     */
    private String groupCode;

    /**
     * 试管架类型编码
     *
     * @see RackTypeEnum
     */
    private String rackTypeCode;

    /**
     * 编码或名称
     */
    private String searchParam;

    /**
     * 检验机构
     */
    private Long orgId;
    /**
     * current
     */
    private Long current;

    /**
     * size
     */
    private Long size;

}
