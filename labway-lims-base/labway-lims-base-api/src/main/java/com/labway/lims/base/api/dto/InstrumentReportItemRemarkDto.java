package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Date;

/**
 * 仪器报告项目结果备注
 *
 * <AUTHOR>
 * @data 2024/11/26
 */
@Getter
@Setter
public class InstrumentReportItemRemarkDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参考值范围ID
     */
    private Long instrumentReportItemRemarkId;

    /**
     * 仪器报告项目ID
     */
    private Long instrumentReportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 样本类型编码
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    @Compare("样本类型")
    private String sampleTypeName;

    /**
     * 性别类型(男1、女2、通用0)
     */
    private Integer sexStyle;

    /**
     * 性别类型名称
     */
    @Compare("适用性别")
    private String sexStyleName;

    /**
     * 年龄上限
     */
    @Compare("年龄上限")
    private Integer ageMax;

    /**
     * 年龄下限
     */
    @Compare("年龄下限")
    private Integer ageMin;

    /**
     * 年龄上限 < or <=
     */
    @Compare("年龄上限单位")
    private String ageMaxFormula;

    /**
     * 年龄下限 > or >=
     */
    @Compare("年龄下限单位")
    private String ageMinFormula;

    /**
     * 年龄单位
     */
    @Compare("年龄单位")
    private String ageUnit;

    /**
     * 结果值上限值
     */
    @Compare("结果值上限")
    private String resultMax;

    /**
     * 结果值下限值
     */
    @Compare("结果值下限")
    private String resultMin;

    /**
     * 结果值上限值 < or <=
     */
    @Compare("结果值上限值单位")
    private String resultMaxFormula;

    /**
     * 结果值下限值 > or >=
     */
    @Compare("参考值下限值单位")
    private String resultMinFormula;

    /**
     * 结果备注
     */
    @Compare("结果备注")
    private String resultRemark;

    /**
     * 参考值状态
     *
     * @see YesOrNoEnum
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 1: 删除 0：未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    @Getter
    @AllArgsConstructor
    public enum SymbolEnum {
        LT("<", "<"),
        LE("<=", "≤"),
        GT(">", ">"),
        GE(">=", "≥"),
        ;

        private final String symbol;

        private final String resultSymbol;

        public static String getSymbol(String symbol) {
            for (SymbolEnum symbolEnum : SymbolEnum.values()) {
                if (symbolEnum.getSymbol().equals(symbol)) {
                    return symbolEnum.getResultSymbol();
                }
            }
            return "";
        }
    }
}
