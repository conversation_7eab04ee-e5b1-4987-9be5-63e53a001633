package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentMedicineDto;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 仪器药物 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
public interface InstrumentMedicineService {

    /**
     * 查询 仪器药物 通过 仪器id
     */
    List<InstrumentMedicineDto> selectByInstrumentId(long instrumentId);

    /**
     * 批量添加 仪器药物
     */
    void addInstrumentMedicines(List<InstrumentMedicineDto> list);

    /**
     * 修改 仪器药物
     */
    void updateByInstrumentMedicineId(InstrumentMedicineDto dto);

    /**
     * 仪器药物保存
     * 
     * @param needDeleteInstrumentMedicineIds 需要删除
     * @param addList 需要新增
     * @param updateList 需要更新的
     */
    void saveInstrumentMedicine(Collection<Long> needDeleteInstrumentMedicineIds, List<InstrumentMedicineDto> addList,
        List<InstrumentMedicineDto> updateList);

    /**
     * 根据药物ID删除
     */
    void deleteByMedicineId(Long instrumentId, Long medicineId);

    /**
     * 查询 仪器下 该药物
     */
    InstrumentMedicineDto selectByMedicineId(Long instrumentId, Long medicineId);
}
