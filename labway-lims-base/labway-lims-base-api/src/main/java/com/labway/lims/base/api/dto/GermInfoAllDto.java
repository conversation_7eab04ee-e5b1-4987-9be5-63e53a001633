package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 细菌信息 所有
 * 
 * <AUTHOR>
 * @since 2023/6/19 16:02
 */
@Getter
@Setter
public class GermInfoAllDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 细菌ID
     */
    private Long germId;

    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 细菌名称
     */
    @Compare("细菌名称")
    private String germName;

    /**
     * 细菌英文名
     */
    @Compare("细菌英文名称")
    private String germEn;

    /**
     * 细菌菌属ID
     */
    private Long germGenusId;
    /**
     * 细菌菌属编码
     */
    private String germGenusCode;
    /**
     * 细菌类别编码
     */
    private String germTypeCode;

    /**
     * 细菌类别名称
     */
    @Compare("细菌分类")
    private String germTypeName;

    /**
     * whonet细菌类型编码
     */
    private String whonetGermTypeCode;

    /**
     * whonet细菌类型名称
     */
    @Compare("WHONET细菌类型")
    private String whonetGermTypeName;

    /**
     * whonet细菌编码
     */
    @Compare("WHONET细菌编码")
    private String whonetGermCode;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 是否开启统计(0未开启 1开启)
     */
    @Compare(value = "是否统计",
            content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enableStatistics;

    /**
     * 是否启用(0未启用 1启用)
     *
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
            content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enable;
    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 1:已经删除 0未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 细菌菌属名称
     */
    @Compare("细菌菌属")
    private String germGenusName;
}
