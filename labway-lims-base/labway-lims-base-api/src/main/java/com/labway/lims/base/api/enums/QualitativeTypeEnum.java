package com.labway.lims.base.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 定性类型
 */
@Getter
@AllArgsConstructor
public enum QualitativeTypeEnum {

    QUALITATIVE(0,"定性"),
    SEMI_QUALITATIVE(1,"半定性"),
    ;


    public static QualitativeTypeEnum selectByCode(Integer code) {
        return Arrays.stream(QualitativeTypeEnum.values()).filter(i -> Objects.equals(i.getCode(), code)).findFirst().orElse(null);
    }

    private Integer code;
    private String value;
}
