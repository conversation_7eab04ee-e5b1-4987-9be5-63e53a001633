package com.labway.lims.base.api.service;

import com.labway.lims.api.enums.base.ReportTemplateBindTypeEnum;
import com.labway.lims.base.api.dto.MatchBindReportDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 报告单模板绑定 Service
 *
 * <AUTHOR>
 * @since 2023/5/24 14:13
 */
public interface ReportTemplateBindService {

    /**
     * 查询 指定绑定类型 的 对应id 的 报告绑定
     */
    List<ReportTemplateBindDto> selectByBizIdsAndBindType(Collection<Long> bizIds,
                                                          ReportTemplateBindTypeEnum bindTypeEnum);
    /**
     * 查询 指定绑定类型 的 对应id 绑定的报告。当 bizId 命中多个时只会返回一个，
     * 如果要根据 bizId 查询多个，请使用 {@link #selectMultiByBizId}
     */
    @Nullable
    ReportTemplateBindDto selectByBizId(long bizId);

    /**
     * 查询 指定绑定类型 的 对应id 绑定的报告
     */
    List<ReportTemplateBindDto> selectByBizIds(Collection<Long> bizIds);

    /**
     * 查询 指定绑定类型 的 对应id 绑定的报告（组合）
     */
    List<ReportTemplateBindDto> selectCombineByBizIds(Collection<Long> bizIds);

    /**
     * 根据 bizId 查询
     */
    List<ReportTemplateBindDto> selectMultiByBizId(long bizId);

    /**
     * 根据 送检机构+专业组Id查询
     */
    List<ReportTemplateBindDto> selectByGroupId(long hspOrgId,long groupId);

    /**
     * 批量新增报告模板
     * @param dtos
     */
    void addBatchReportTemplateBind(List<ReportTemplateBindDto> dtos);

    /**
     * 批量更新 (这里必须唯一bindId)
     * @param dtos
     * @return
     */
    boolean updateReportTemplateBinds(List<ReportTemplateBindDto> dtos,Long bindGroupId);

    void deleteByBindGroupIds(Collection<Long> reportTemplateBindIds);

    List<ReportTemplateBindDto> selectByIds(Collection<Long> ids);

    /**
     * 根据bindGroupId来获取报告模板列表
     * @param bindGroupIds
     * @return
     */
    List<ReportTemplateBindDto> selectByBindGroupIds(Collection<Long> bindGroupIds);

    @Nullable
    ReportTemplateBindDto selectById(long reportTemplateBindId);

    List<ReportTemplateBindDto> selectByBindType(ReportTemplateBindTypeEnum bindTypeEnum,Long excludeId);

    /**
     * 通过参数 找到最合适的报告模板
     * @param bindReportDto
     * @return
     */
    ReportTemplateBindDto findMatchReportTemplate(MatchBindReportDto bindReportDto);
}
