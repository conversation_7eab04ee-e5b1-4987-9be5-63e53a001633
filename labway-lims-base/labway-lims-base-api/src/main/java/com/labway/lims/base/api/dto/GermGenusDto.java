package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 细菌菌属 信息 Dto
 * 
 * <AUTHOR>
 * @since 2023/3/21 11:30
 */
@Getter
@Setter
public class GermGenusDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 细菌菌属ID
     */
    private Long germGenusId;

    /**
     * 细菌菌属名称
     */
    @Compare("菌属名称")
    private String germGenusName;

    /**
     * 细菌菌属编码
     */
    private String germGenusCode;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 是否启用(0未启用 1启用)
     * 
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
        content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enable;
    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 1:已经删除 0未删除
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;
}
