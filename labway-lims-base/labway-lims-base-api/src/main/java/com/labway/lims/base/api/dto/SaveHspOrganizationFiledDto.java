package com.labway.lims.base.api.dto;

import com.labway.lims.api.DefaultHspOrg;
import com.labway.lims.api.enums.apply.PdaDoubleCheckFiledEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 双输内容对照表
 */
@Getter
@Setter
public class SaveHspOrganizationFiledDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 对照内容id
     */
    private Long filedId;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构code
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 字段列表
     */
    private List<Field> fileds;

    @Getter
    @Setter
    public static final class Field implements Serializable{

        private static final long serialVersionUID = 1L;
        /**
         * 字段code
         */
        private String code;

        /**
         * 字段名称
         */
        private String name;

        /**
         * 字段顺序
         */
        private Integer sort;

    }

    public static SaveHspOrganizationFiledDto defaultFieldSetting() {
        final SaveHspOrganizationFiledDto saveHspOrganizationFiledDto = new SaveHspOrganizationFiledDto();
        saveHspOrganizationFiledDto.setHspOrgId(DefaultHspOrg.DEFAULT_HSP_ORG_ID);
        saveHspOrganizationFiledDto.setHspOrgId(DefaultHspOrg.DEFAULT_HSP_ORG_ID);
        saveHspOrganizationFiledDto.setHspOrgCode(DefaultHspOrg.DEFAULT_HSP_ORG_CODE);
        saveHspOrganizationFiledDto.setHspOrgName(DefaultHspOrg.DEFAULT_HSP_ORG_NAME);

        final List<Field> filedList = Arrays.stream(PdaDoubleCheckFiledEnum.values()).map(e -> {
            final Field filed = new Field();
            filed.setCode(e.getCode());
            filed.setName(e.getDesc());
            filed.setSort(e.getSort());
            return filed;
        }).sorted(Comparator.comparing(Field::getSort)).collect(Collectors.toList());
        saveHspOrganizationFiledDto.setFileds(filedList);
        return saveHspOrganizationFiledDto;
    }
}
