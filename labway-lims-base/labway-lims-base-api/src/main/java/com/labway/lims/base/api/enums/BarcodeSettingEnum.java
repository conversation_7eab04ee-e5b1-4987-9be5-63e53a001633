package com.labway.lims.base.api.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BarcodeSettingEnum {

    SAVE_BARCODE_SETTING("%slabway-lims-base-server:barcode-setting:create-barcode:%s:%s", "条码规则添加"),

    BARCODE_CACHE("%slabway-lims-base-server:barcode-setting:barcode:%s:%s", "条码缓存");


    /**
     * 条码设置
     */
    private final String redisKey;

    /**
     * 描述
     */
    private final String desc;

}
