package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.TestItemDto;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 检验项目
 */
public interface TestItemService {


    /**
     * 从主数据添加检验项目
     */
    long addTestItem(String testItemCode, long groupId);

    /**
     * 从主数据添加检验项目
     */
    List<Long> addTestItems(Collection<String> testItemCodes, long groupId);


    /**
     * 同步主数据
     */
    void syncMainDataTestItem(String testItemCode);

    /**
     * 根据编码查询
     */
    @Nullable
    TestItemDto selectByTestItemCode(String testItemCode, long orgId);

    /**
     * 根据编码查询
     */
    @Nonnull
    List<TestItemDto> selectByTestItemCodes(Collection<String> testItemCodes, long orgId);

    /**
     * 根据编码查询缓存
     */
    @Nonnull
    List<TestItemDto> selectByTestItemCodesCached(Collection<String> testItemCodes, long orgId);

    /**
     * 根据id修改
     */
    boolean updateByTestItemId(TestItemDto testItem);

    /**
     * 根据id删除
     */
    boolean deleteByTestItemId(long testItemId);

    /**
     * 根据id删除
     */
    void deleteByTestItemIds(Collection<Long> testItemIds);

    /**
     * 根据专业组查询
     */
    List<TestItemDto> selectByGroupId(long groupId);

    /**
     * 根据机构查询
     */
    @Nonnull
    List<TestItemDto> selectByOrgId(long orgId);

    /**
     * 根据id查询
     */
    @Nonnull
    List<TestItemDto> selectByTestItemIds(Collection<Long> testItemIds);

    /**
     * 根据id查询
     */
    @Nullable
    TestItemDto selectByTestItemId(long testItemId);

    /**
     * 根据id查询 转成map
     */
    @Nonnull
    Map<Long, TestItemDto> selectByTestItemIdsAsMap(Collection<Long> testItemIds);

    /**
     * 根据机构查询 转成map
     */
    Map<Long, TestItemDto> selectByOrgIdToMap(long orgId);

    /**
     * 根据多個专业组查询
     */
    List<TestItemDto> selectByGroupIds(Collection<Long> groupIds);

}
