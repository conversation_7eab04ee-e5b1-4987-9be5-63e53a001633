package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentGroupInstrumentDto;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/3/29 20:18
 */
public interface InstrumentGroupInstrumentService {
    /**
     * 给专业小组添加仪器
     * 
     * @param dtos List<InstrumentGroupInstrumentDto>
     */
    Set<Long> addBatch(List<InstrumentGroupInstrumentDto> dtos);

    /**
     * 根据专业小组id查询仪器
     * 
     * @param instrumentGroupId 专业小组id
     * @return List<InstrumentGroupInstrumentDto>
     */
    List<InstrumentGroupInstrumentDto> selectByInstrumentGroupId(long instrumentGroupId);

    /**
     * 根据仪器ID查询
     */
    List<InstrumentGroupInstrumentDto> selectByInstrumentId(long instrumentId);

    /**
     * 删除专业小组下的仪器
     * 
     * @param instrumentGroupInstrumentId instrumentGroupInstrumentId
     */
    boolean deleteById(long instrumentGroupInstrumentId);

    /**
     * 根据专业小组id删除仪器
     * 
     * @param instrumentGroupId 专业小组id
     */
    boolean deleteByInstrumentGroupId(long instrumentGroupId);

    List<InstrumentGroupInstrumentDto> selectByInstrumentGroupIds(Collection<Long> instrumentGroupIds);

    /**
     * 通过id 获取信息
     */
    List<InstrumentGroupInstrumentDto> selectByIds(Collection<Long> instrumentGroupInstrumentIds);
}
