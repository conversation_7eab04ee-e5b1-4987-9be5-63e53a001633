package com.labway.lims.base.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class CombinePackageInfoDto implements Serializable {

    //财务套餐包id
    private Long combinePackageId;
    //机构id
    @NotNull(message = "机构id不能为空!")
    private Long orgId;
    //机构名称
    @NotBlank(message = "机构名称不能为空!")
    private String orgName;
    //套餐包编码
    @NotBlank(message = "套餐包编码不能为空!")
    private String combinePackageCode;
    //套餐包名称
    @NotBlank(message = "套餐包名称不能为空!")
    private String combinePackageName;
    //套餐收费价格
    @NotNull(message = "套餐收费价格不能为空!")
    private BigDecimal combinePackagePrice;
    //套餐项目数量
    private Integer itemCount = 0;
    // 送检机构信息
    @Valid
    @NotEmpty(message = "送检机构信息不能为空!")
    private List<HspOrgInfo> hspOrgList;
    //是否启用 0否1是
    private Integer enable;
    //检验项目MD5值
    private String itemMd5;
    // 创建时间
    private String createDate;
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HspOrgInfo implements Serializable{
        //送检机构id
        @NotNull(message = "送检机构id不能为空!")
        private Long hspOrgId;
        //送检机构编码
        @NotBlank(message = "送检机构编码不能为空!")
        private String hspOrgCode;
        //送检机构名称
        @NotBlank(message = "送检机构名称不能为空!")
        private String hspOrgName;
    }

}
