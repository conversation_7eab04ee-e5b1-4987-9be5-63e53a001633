package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.MenuTypeCodeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * 菜单
 */
@Getter
@Setter
public class MenuDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    private Long menuId;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单Code
     */
    private String menuCode;

    /**
     * 父菜单ID
     */
    private Long proMenuId;

    /**
     * 菜单类型编码 (MENU,PAGE,BUTTON)
     *
     * @see MenuTypeCodeEnum
     */
    private String menuTypeCode;

    /**
     * 菜单描述
     */
    private String menuDesc;

    /**
     * 菜单排序
     */
    private Integer menuSort;

    /**
     * 菜单icon
     */
    private String menuIcon;

    /**
     * 菜单地址
     */
    private String menuUrl;

    /**
     * 是否启用(0未启用 1启用)
     *
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * is_delete
     * @see YesOrNoEnum
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 子菜单
     */
    private List<MenuDto> childList = new LinkedList<>();

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        final MenuDto menuDto = (MenuDto) o;
        return Objects.equals(menuId, menuDto.menuId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(menuId);
    }
}
