package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 仪器报告项目参考范围
 */
public interface InstrumentReportItemReferenceService {

    int MONTH_DAYS = 31;
    int YEAR_DAYS = MONTH_DAYS * 12;

    /**
     * 根据仪器报告项目查询参考范围
     */
    List<InstrumentReportItemReferenceDto> selectByInstrumentReportItemId(long instrumentReportItemId);

    /**
     * 根据id查询
     */
    @Nullable
    InstrumentReportItemReferenceDto selectByInstrumentReportItemReferenceId(long instrumentReportItemReferenceId);

    /**
     * 根据id查询
     */
    @Nonnull
    List<InstrumentReportItemReferenceDto> selectByInstrumentReportItemReferenceIds(Collection<Long> instrumentReportItemReferenceIds);

    /**
     * 添加参考范围
     */
    long addInstrumentReportItemReference(InstrumentReportItemReferenceDto reference);

    /**
     * 修改参考范围
     */
    boolean updateByInstrumentReportItemReferenceId(InstrumentReportItemReferenceDto reference);

    /**
     * 根据id删除
     */
    boolean deleteByInstrumentReportItemReferenceId(long instrumentReportItemReferenceId);

    /**
     * 根据reportitemcode和orgid查询
     *
     * @deprecated 查询哪台仪器的？ todo wsl
     */
    @Deprecated
    @Nullable
    List<InstrumentReportItemReferenceDto> selectByReportItemCodeAndOrgId(String reportItemCode, long orgId);

    /**
     * 根据仪器ID查询
     */
    List<InstrumentReportItemReferenceDto> selectByInstrumentId(long instrumentId);

    /**
     * 根据专业小组查询
     */
    List<InstrumentReportItemReferenceDto> selectByInstrumentGroupId(long instrumentGroupId);

    /**
     * 根据专业小组和报告项目code查询
     */
    List<InstrumentReportItemReferenceDto> selectByInstrumentGroupIdAndReportItemCode(long instrumentGroupId,
                                                                                      String reportItemCode);


    List<InstrumentReportItemReferenceDto> selectByInstrumentIds(Collection<Long> instrumentIds);

    /**
     * 根据仪器id和报告项目code查询参考范围
     */
    List<InstrumentReportItemReferenceDto> selectByInstrumentIdAndReportItemCodes(long instrumentId, Collection<String> reportItemCodes);

    /**
     * 根据仪器id和报告项目code查询参考范围 转成 map
     */
    Map<String, List<InstrumentReportItemReferenceDto>> selectByInstrumentIdAndReportItemCodesAsMap(long instrumentId, Collection<String> reportItemCodes);

    /**
     * 根据 id 查询
     */
    List<InstrumentReportItemReferenceDto> selectByIds(Collection<Long> ids);

    /**
     * 根据 id 查询
     */
    InstrumentReportItemReferenceDto selectById(long id);

    /**
     * 删参考范围
     */
    void deleteByInstrumentReportItemId(long instrumentReportItemId);

    List<Long> copyReportItemReference(Long originInstrumentReportItemId, InstrumentReportItemDto instrumentReportItemDto);
}
