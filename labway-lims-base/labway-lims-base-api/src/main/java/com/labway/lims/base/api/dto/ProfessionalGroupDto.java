package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 专业组
 */
@Getter
@Setter
public class ProfessionalGroupDto implements Serializable {
    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    @Compare("专业组名称")
    private String groupName;

    /**
     * 专业组说明
     */
    private String groupRemark;

    /**
     * 专业组编码
     */
    private String groupCode;

    /**
     * 专业组类别ID
     */
    private String groupTypeCode;

    /**
     * 专业组类别名称
     */
    @Compare("专业组类型")
    private String groupTypeName;

    /**
     * 批准者名字
     */
    @Compare("批准者姓名")
    private String approverName;

    /**
     * 批准者签名图片
     */
    @Compare("批准者签名")
    private String approverSign;

    /**
     * 是否启动(0未启动 1启动)
     *
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
            content = {
                    @CompareContent(value = "1", valueDesc = "是"),
                    @CompareContent(value = "0", valueDesc = "否")
            }
    )
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;
    /**
     * 逻辑删除字段
     * @see YesOrNoEnum
     */
    private Integer isDelete;
}
