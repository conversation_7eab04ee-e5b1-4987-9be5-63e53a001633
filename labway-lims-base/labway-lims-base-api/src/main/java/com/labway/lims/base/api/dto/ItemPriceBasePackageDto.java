package com.labway.lims.base.api.dto;

import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目价格基准包
 */
@Getter
@Setter
public class ItemPriceBasePackageDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long packageId;

    /**
     * 基准包名称
     */
    @Compare(value = "基准包名称")
    private String packageName;

    /**
     * 开始时间
     */
    @Compare(value = "开始时间")
    private Date startDate;

    /**
     * 结束时间
     */
    @Compare(value = "结束时间")
    private Date endDate;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 是否启用
     */
    @Compare(value = "是否启用",
            content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enable;

    /**
     * 1:删除,0:未删
     */
    private Integer isDelete;
}
