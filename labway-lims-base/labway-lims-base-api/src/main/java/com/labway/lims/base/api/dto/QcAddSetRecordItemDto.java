package com.labway.lims.base.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 仪器质控记录明细
 *
 *
 */
@Data
public class QcAddSetRecordItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 质控记录明细ID
     */
    private Long qcRecordItemId;

    /**
     * 质控批次编号
     */
    private Long qcBatchId;

    /**
     * 靶值
     */
    private String targetValue;

    /**
     * 标准差
     */
    private String standardDeviation;

    /**
     * 变异系数
     */
    private String cvValue;

    /**
     * 水平编码
     */
    private Integer levelCode;
}