package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.RackTypeEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import com.labway.lims.base.api.enums.RackStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 试管架
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class RackDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    private Long rackId;

    /**
     * 试管架类型编码
     * 
     * @see RackTypeEnum
     */
    private String rackTypeCode;
    /**
     * 试管架类型名称
     */
    @Compare("试管架类型")
    private String rackTypeName;

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 试管架名称
     */
    private String rackName;

    /**
     * 多少行
     */
    @Compare("行")
    private Integer row;

    /**
     * 多少列
     */
    @Compare("列")
    private Integer column;

    /**
     * 1: 被占用 0: 可以使用
     * 
     * @see RackStatusEnum
     */
    private Integer status;

    /**
     * 是否启用(0未启用 1启用)
     * 
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
        content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enable;

    /**
     * org_id
     */
    private Long orgId;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 1:已删除 0：未删除
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 专业组编码
     */
    private String groupCode;
    /**
     * 专业组
     */
    private String groupName;

}
