package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemRemarkDto;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 仪器报告项目结果备注
 */
public interface InstrumentReportItemRemarkService {

    int MONTH_DAYS = 31;
    int YEAR_DAYS = MONTH_DAYS * 12;

    /**
     * 根据仪器报告项目查询结果备注
     */
    List<InstrumentReportItemRemarkDto> selectByInstrumentReportItemId(long instrumentReportItemId);

    /**
     * 根据id查询
     */
    @Nullable
    InstrumentReportItemRemarkDto selectByInstrumentReportItemRemarkId(long instrumentReportItemRemarkId);

    /**
     * 根据id查询
     */
    @Nonnull
    List<InstrumentReportItemRemarkDto> selectByInstrumentReportItemRemarkIds(Collection<Long> instrumentReportItemRemarkIds);

    /**
     * 添加结果备注
     */
    long addInstrumentReportItemRemark(InstrumentReportItemRemarkDto remark);

    /**
     * 修改结果备注
     */
    boolean updateByInstrumentReportItemRemarkId(InstrumentReportItemRemarkDto remark);

    /**
     * 根据id删除
     */
    boolean deleteByInstrumentReportItemRemarkId(long instrumentReportItemRemarkId);

    /**
     * 根据reportitemcode和orgid查询
     *
     * @deprecated 查询哪台仪器的？ todo wsl
     */
    @Deprecated
    @Nullable
    List<InstrumentReportItemRemarkDto> selectByReportItemCodeAndOrgId(String reportItemCode, long orgId);

    /**
     * 根据仪器ID查询
     */
    List<InstrumentReportItemRemarkDto> selectByInstrumentId(long instrumentId);

    /**
     * 根据专业小组查询
     */
    List<InstrumentReportItemRemarkDto> selectByInstrumentGroupId(long instrumentGroupId);

    /**
     * 根据专业小组和报告项目code查询
     */
    List<InstrumentReportItemRemarkDto> selectByInstrumentGroupIdAndReportItemCode(long instrumentGroupId,
                                                                                      String reportItemCode);


    List<InstrumentReportItemRemarkDto> selectByInstrumentIds(Collection<Long> instrumentIds);

    /**
     * 根据仪器id和报告项目code查询结果备注
     */
    List<InstrumentReportItemRemarkDto> selectByInstrumentIdAndReportItemCodes(long instrumentId, Collection<String> reportItemCodes);

    /**
     * 根据仪器id和报告项目code查询结果备注 转成 map
     */
    Map<String, List<InstrumentReportItemRemarkDto>> selectByInstrumentIdAndReportItemCodesAsMap(long instrumentId, Collection<String> reportItemCodes);

    /**
     * 根据 id 查询
     */
    List<InstrumentReportItemRemarkDto> selectByIds(Collection<Long> ids);

    /**
     * 根据 id 查询
     */
    InstrumentReportItemRemarkDto selectById(long id);

    /**
     * 删结果备注
     */
    void deleteByInstrumentReportItemId(long instrumentReportItemId);

    /**
     * 拷贝仪器报告项目结果备注
     * @param originInstrumentReportItemId
     * @param instrumentReportItemDto
     * @return
     */
    List<Long> copyReportItemRemark(Long originInstrumentReportItemId, InstrumentReportItemDto instrumentReportItemDto);
}
