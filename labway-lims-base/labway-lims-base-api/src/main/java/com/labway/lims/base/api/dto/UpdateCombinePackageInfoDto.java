package com.labway.lims.base.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class UpdateCombinePackageInfoDto implements Serializable {
//    // 套餐包id
//    @NotNull(message = "套餐id不能为空!")
//    private Long combinePackageId;
    //机构id
    @NotNull(message = "机构id不能为空!")
    private Long orgId;
    //机构名称
    @NotBlank(message = "机构名称不能为空!")
    private String orgName;
    //套餐包编码
    @NotBlank(message = "套餐包编码不能为空!")
    private String combinePackageCode;
    //套餐包名称
    @NotBlank(message = "套餐包名称不能为空!")
    private String combinePackageName;
    //套餐收费价格
    @NotNull(message = "套餐收费价格不能为空!")
    private BigDecimal combinePackagePrice;
    //是否启用 0否1是
    @NotNull(message = "是否启用不能为空!")
    @Min(value = 0, message = "是否启用只能为0或1!")
    @Max(value = 1, message = "是否启用只能为0或1!")
    private Integer enable;

    // 送检机构信息
    @Valid
    @NotEmpty(message = "送检机构信息不能为空!")
    private List<UpdateCombinePackageInfoDto.HspOrgInfo> hspOrgList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HspOrgInfo implements Serializable{
        //送检机构id
        @NotNull(message = "送检机构id不能为空!")
        private Long hspOrgId;
        //送检机构编码
        @NotBlank(message = "送检机构编码不能为空!")
        private String hspOrgCode;
        //送检机构名称
        @NotBlank(message = "送检机构名称不能为空!")
        private String hspOrgName;
    }




}
