package com.labway.lims.base.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 质控规则
 * <pre>
 * 1、12s(1-2s)：一个质控测定值超过X±2s质控限。作为质控图上的 警告限。即：出现一个超过X±2s的数据，质控图上该点呈警告提示。
 *
 * 2、13s(1-3s)：一个质控测定值超过X±3s质控限。作为质控图上的 失控限。即：出现一个超过X±3s的数据，该点呈失控提示。
 *
 * 3、22s(2-2s)：两个连续的质控测定值同时超过X-2s 或X+2s质控限。即：连续出现2个超过X±2s的数据，第2个点呈失控提示。
 *
 * 4、R4s(R-4s)：在同一浓度内，连续2个质控值的差值±4SD，第2个点呈失控提示。如果第一个点1，第二个点是6，那么第二个点失控；如果第一个点是6，第二个点是1，那么第二个点失控；
 *
 * 5、【31s、41s】同类质控规则，第一个数字越小，代表质控越严格：
 *
 *    31s(3-1s)：三个连续的质控测定值同时超过X-1s 或X+1s。则：第三个值呈失控提示。
 *
 *    41s(4-1s)：四个连续的质控测定值同时超过X-1s 或X+1s。
 *
 * 6、【7X、8X、9X、10X、12X】同类质控规则，数字越小，代表质控越严格：
 *
 *    7X(7-X)：七个连续的质控测定值落在平均数(X)的同一侧。则：第7个数据呈失控提示。
 *
 *    8X(8-X)：八个连续的质控测定值落在平均数(X)的同一侧。
 *
 *    9X(9-X)：九个连续的质控测定值落在平均数(X)的同一侧。
 *
 *    10X(10-X)：十个连续的质控测定值落在平均数(X)的同一侧。
 *
 *    12X(12-X)：十二个连续的质控测定值落在平均数(X)的同一侧。
 *
 * 7、7T(7-T)：七个连续的质控测定值呈现出向上或向下的趋势。
 * </pre>
 *
 * <AUTHOR>
 * @since 2022/7/19 13:19
 */
@Getter
@AllArgsConstructor
public enum QcRuleEnum {

    /**
     * 一个质控测定值超过X±1s质控限。作为质控图上的 警告限。即：出现一个超过X±1s的数据，质控图上该点呈警告提示。
     */
    QC_1_1S("13", ""),
    /**
     * 一个质控测定值超过X±2s质控限。作为质控图上的 警告限。即：出现一个超过X±2s的数据，质控图上该点呈警告提示。
     */
    QC_1_2S("1", "1-2S"),
    /**
     * 一个质控测定值超过X±3s质控限。作为质控图上的 失控限。即：出现一个超过X±3s的数据，该点呈失控提示。
     */
    QC_1_3S("2", "1-3S"),
    /**
     * 两个连续的质控测定值同时超过X-2s 或X+2s质控限。即：连续出现2个超过X±2s的数据，第2个点呈失控提示。
     */
    QC_2_2S("3", "2-2S"),
    /**
     * 在同一浓度内，连续2个质控值的差值±4SD，第2个点呈失控提示。如果第一个点1，第二个点是6，那么第二个点失控；如果第一个点是6，第二个点是1，那么第二个点失控；
     */
    QC_R_4S("4", "R-4S"),
    /**
     * 三个连续的质控测定值同时超过X-1s 或X+1s。则：第三个值呈失控提示。
     */
    QC_3_1S("5", "3-1S"),
    /**
     * 四个连续的质控测定值同时超过X-1s 或X+1s。
     */
    QC_4_1S("6", "4-1S"),
    /**
     * 七个连续的质控测定值落在平均数(X)的同一侧。则：第7个数据呈失控提示。
     */
    QC_7_X("7", "7-X"),
    /**
     * 八个连续的质控测定值落在平均数(X)的同一侧。
     */
    QC_8_X("8", "8-X"),
    /**
     * 九个连续的质控测定值落在平均数(X)的同一侧。
     */
    QC_9_X("9", "9-X"),
    /**
     * 十个连续的质控测定值落在平均数(X)的同一侧。
     */
    QC_10_X("10", "10-X"),
    /**
     * 十二个连续的质控测定值落在平均数(X)的同一侧。
     */
    QC_12_X("11", "12-X"),
    /**
     * 七个连续的质控测定值呈现出向上或向下的趋势，第七个点定为失控
     */
    QC_7_T("12", "7-T"),
    ;

    public static final Map<String, QcRuleEnum> values;

    static {
        final Map<String, QcRuleEnum> map = new HashMap<>(QcRuleEnum.values().length);
        for (QcRuleEnum e : QcRuleEnum.values()) {
            map.put(e.getValue(), e);
        }
        values = Collections.unmodifiableMap(map);
    }

    /**
     * 内部值
     */
    private final String value;

    /**
     * 前端展示描述
     */
    private final String des;

}
