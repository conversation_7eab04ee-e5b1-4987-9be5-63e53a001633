package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.MaterialRelationDto;
import com.labway.lims.base.api.dto.ReportMaterialRelationDto;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 报告物料关联表 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11 14:16
 */
public interface ReportMaterialRelationService {

    /**
     * 批量保存报告物料关联
     * 
     * @param reportMaterialRelationDtos 报告物料关联DTO列表
     * @return 保存结果
     */
    boolean batchSaves(List<ReportMaterialRelationDto> reportMaterialRelationDtos);
    
    /**
     * 批量删除报告物料关联
     * 
     * @param reportMaterialRelationIds 报告物料关联ID集合
     * @return 删除结果
     */
    boolean batchDelete(Set<Long> reportMaterialRelationIds);
    
    /**
     * 根据报告项目编码和专业组ID查询关联的物资信息
     * 
     * @param reportItemCode 报告项目编码
     * @param groupId 专业组ID
     * @return 物资信息列表，包含关联ID
     */
    List<MaterialRelationDto> selectMaterialsByReportItemCodeAndGroupId(String reportItemCode, Long groupId);
    
    /**
     * 查询所有未删除的报告物料关联记录
     * 
     * @return 报告物料关联DTO列表
     */
    List<ReportMaterialRelationDto> selectAll();
    
    /**
     * 根据报告项目编码集合和专业组名称集合查询关联记录
     * 
     * @param reportItemCodes 报告项目编码集合
     * @param groupNames 专业组名称集合
     * @return 报告物料关联DTO列表
     */
    List<ReportMaterialRelationDto> selectByReportItemCodesAndGroupNames(Set<String> reportItemCodes, Set<String> groupNames);
    
    /**
     * 获取所有已对照的项目编码和专业组名称组合
     * 
     * @return 已对照的组合键集合
     */
    Set<String> getAllMappedItemGroupKeys();

    /**
     * 批量更新报告物料关联
     * 
     * @param reportMaterialRelationDtos 报告物料关联DTO列表
     * @return 更新结果
     */
    boolean batchUpdates(List<ReportMaterialRelationDto> reportMaterialRelationDtos);
} 