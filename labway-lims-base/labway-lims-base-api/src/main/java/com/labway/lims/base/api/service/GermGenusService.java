package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.GermGenusDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 细菌菌属 Service
 * 
 * <AUTHOR>
 * @since 2023/3/20 17:41
 */
public interface GermGenusService {

    /**
     * 添加细菌菌属
     */
    long addGermGenus(GermGenusDto germGenusDto);

    /**
     * 删除细菌菌属
     */
    void deleteByGermGenusIds(Collection<Long> germGenusIds);

    /**
     * 修改细菌菌属
     */
    void updateByGermGenusId(GermGenusDto germGenusDto);

    /**
     * 查询 细菌菌属 通过 机构
     */
    List<GermGenusDto> selectByOrgId(long orgId);

    /**
     * 根据细菌菌属id查找细菌菌属
     */
    @Nullable
    GermGenusDto selectByGermGenusId(long germGenusId);

    /**
     * 根据细菌菌属名称查找细菌菌属
     */
    @Nullable
    GermGenusDto selectByGermGenusName(String germGenusName, long orgId);

    /**
     * 根据细菌菌属编码查找细菌菌属
     */
    @Nullable
    GermGenusDto selectByGermGenusCode(String germGenusCode, long orgId);

    /**
     * 查询细菌菌属
     */
    List<GermGenusDto> selectByGermGenusIds(Collection<Long> germGenusIds);

    /**
     * 根据细菌菌属名称s 查找细菌菌属
     */
    List<GermGenusDto> selectByGermGenusNames(Collection<String> germGenusNames, long orgId);

}
