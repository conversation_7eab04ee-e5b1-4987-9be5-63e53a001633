package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 体检单位 Dto
 * 
 * <AUTHOR>
 * @since 2023/3/27 15:56
 */
@Getter
@Setter
public class PhysicalGroupDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 体检单位ID
     */
    private Long physicalGroupId;
    /**
     * 体检单位名称
     */
    @Compare("体检单位名称")
    private String physicalGroupName;
    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    @Compare("送检机构名称")
    private String hspOrgName;
    /**
     * 联系人
     */
    @Compare("单位联系人")
    private String contactUser;
    /**
     * 联系方式
     */
    @Compare("联系电话")
    private String contactPhone;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 是否启用(0未启用 1启用)
     * 
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
        content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enable;
    /**
     * 是否删除
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人名称
     */
    private String updaterName;
}
