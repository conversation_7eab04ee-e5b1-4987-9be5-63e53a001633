package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.*;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 客户折扣信息服务层
 */
public interface HspOrgDiscountService {
    /**
     * 客户折扣列表
     */
    List<HspOrgDiscountDto> list(@Nullable Long hspOrgId, @Nullable String applyType);

    /**
     * 添加机构基准包折扣
     */
    List<HspOrgDiscountDto> addHspOrgBasePackage(AddHspOrgBasePackageDto param);

    /**
     * 修改客户折扣
     */
    HspOrgDiscountCompareInfo updateHspOrgDiscount(UpdateHspOrgDiscountDto param);

    /**
     * 根据id查询客户折扣
     */
    HspOrgDiscountDto selectById(long discountId);

    /**
     * 查询 在指定机构、送检类型名称下 与时间范围存在交集 的折扣数据
     */
    List<HspOrgDiscountDto> selectByHspOrgIdsAndDateRange(Collection<Long> hspOrgIds, Collection<String> applyTypes,
        Date startDate, Date endDate);

    /**
     * 查询折扣项目详情
     */
    List<DiscountDetailDto> selectDiscountDetailByHspOrgIdsAndDateRange(Collection<Long> hspOrgIds,
        Set<String> applyTypes, Date minDate, Date maxDate);

    /**
     * 根据基准包id查询
     */
    List<HspOrgDiscountDto> selectByPackageId(long packageId);

    /**
     * 查询 送检机构下 指定就诊类型 、 时间点 的客户折扣
     * 
     * @param hspOrgId 送检机构
     * @param applyTypeCode 就诊类型
     * @param sampleItemCreateDate 样本检验项目创建时间
     * @return 客户折扣
     */
    @Nullable
    HspOrgDiscountDto selectByApplyTypeAndDate(Long hspOrgId, String applyTypeCode, Date sampleItemCreateDate);
}
