package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.vo.GroupMaterialPageVo;

import java.util.Collection;
import java.util.List;

/**
 * 专业组物料信息 service
 *
 * <AUTHOR>
 * @since 2023/5/8 17:40
 */
public interface GroupMaterialService {
    List<GroupMaterialDto> selectAll();

    /**
     * 根据id查询
     */
    List<GroupMaterialDto> seleceGroupMaterialById(Collection<Long> materialIds);

    void addGroupMaterials(List<GroupMaterialDto> gms);

    long addGroupMaterial(long materialId, ProfessionalGroupDto group);

    /**
     * 修改 专业组 物料关联信息
     */
    void updateByGroupMaterialId(GroupMaterialDto dto);

    /**
     * 批量修改 专业组物料信息
     */
    void updateByGroupMaterialIds(GroupMaterialDto dto, Collection<Long> groupMaterialIds);

    /**
     * 查询 专业组下 这些物料ids 对应的关联信息
     *
     * @param groupId 专业组id
     * @param materialIds 物料ids
     */
    List<GroupMaterialDto> selectByGroupIdAndMaterialIds(long groupId, Collection<Long> materialIds);

    /**
     * 查询 专业组下 此物料id 对应的关联信息 只会有一条
     *
     */
    GroupMaterialDto selectByGroupIdAndMaterialId(long groupId, long materialId);

    /**
     * 根据专业组 id 查询
     */
    List<GroupMaterialDto> selectByGroupId(long groupId);

    void deleteGroupMaterials(List<Long> materialIds, long groupId);

    void deleteAllGroupMaterials(long groupId);

    /**
     * 查找专业组下 物料编码 对应 信息
     * 
     * @param groupId 专业组id
     * @param materialCodes 物料编码
     */
    List<GroupMaterialDetailDto> selectByGroupIdAndMaterialCodes(long groupId, Collection<String> materialCodes);

    /**
     * 导入专业组物料维护数据
     */
    Integer importGroupMaterial(List<GroupMaterialSheetDto> groupMaterialSheetDtos);

    /**
     * 专业组物料信息-分页
     * @param groupMaterialPageVo
     * @return
     */
    GroupMaterialPageDto groupMaterialPage(GroupMaterialPageVo groupMaterialPageVo);
}
