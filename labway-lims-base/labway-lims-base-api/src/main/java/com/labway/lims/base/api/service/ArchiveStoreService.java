package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.ArchiveStoreDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 归档库 Service
 * 
 * <AUTHOR>
 * @since 2023/4/3 10:59
 */
public interface ArchiveStoreService {

    /**
     * 添加 归档库
     */
    long addArchiveStore(ArchiveStoreDto archiveStoreDto);

    /**
     * 删除 归档库
     */
    void deleteByArchiveStoreIds(Collection<Long> archiveStoreIds);

    /**
     * 修改 归档库
     */
    void updateByArchiveStoreId(ArchiveStoreDto archiveStoreDto);

    /**
     * 查询 归档库 通过 机构
     */
    List<ArchiveStoreDto> selectByOrgId(long orgId);

    /**
     * 根据 归档库id 查找 归档库
     */
    @Nullable
    ArchiveStoreDto selectByArchiveStoreId(long archiveStoreId);

    /**
     * 根据 归档库ids 查找 归档库
     */
    List<ArchiveStoreDto> selectByArchiveStoreIds(Collection<Long> archiveStoreIds);

    /**
     * 根据 归档库名称 查找 归档库
     */
    @Nullable
    ArchiveStoreDto selectByArchiveStoreName(String archiveStoreName, long orgId);

    /**
     * 根据 归档库编码 查找 归档库
     */
    @Nullable
    ArchiveStoreDto selectByArchiveStoreCode(String archiveStoreCode, long orgId);

}
