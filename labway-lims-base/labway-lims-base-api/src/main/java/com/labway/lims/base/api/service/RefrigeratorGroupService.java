package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.RefrigeratorGroupDto;

import java.util.Collection;
import java.util.List;

/**
 * 冰箱专业组关联表 Service
 * 
 * <AUTHOR>
 * @since 2023/4/3 15:15
 */
public interface RefrigeratorGroupService {

    /**
     * 批量添加 冰箱专业组关联表
     */
    void addRefrigeratorGroups(List<RefrigeratorGroupDto> list);

    /**
     * 根据 冰箱 获取 对应专业组
     *
     */
    List<RefrigeratorGroupDto> selectByRefrigeratorId(long refrigeratorId);

    /**
     * 根据 冰箱 获取 对应专业组
     */
    List<RefrigeratorGroupDto> selectByRefrigeratorIds(Collection<Long> refrigeratorId);

    /**
     * 删除 冰箱专业组关联表
     */
    void deleteByRelationIds(Collection<Long> relationIds);

    /**
     * 根据 专业组 获取 对应冰箱
     *
     */
    List<RefrigeratorGroupDto> selectByGroupId(long groupId);

}
