package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 套餐项目 Dto
 * 
 * <AUTHOR>
 * @since 2023/3/28 19:31
 */
@Getter
@Setter
public class PackageItemDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    private Long packageItemId;
    /**
     * 套餐ID
     */
    private Long packageId;

    /**
     * 套餐名称
     */
    private String packageName;
    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 项目ID
     */
    private Long testItemId;
    /**
     * 项目编码
     */
    private String testItemCode;
    /**
     * 项目名称
     */
    private String testItemName;
    /**
     * 1:已经删除 0未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人名称
     */
    private String updaterName;
}
