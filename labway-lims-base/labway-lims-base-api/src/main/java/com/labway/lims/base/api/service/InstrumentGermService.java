package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentGermDto;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 仪器细菌 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 */
public interface InstrumentGermService {

    /**
     * 查询 仪器细菌 通过 仪器id
     */
    List<InstrumentGermDto> selectByInstrumentId(long instrumentId);

    /**
     * 批量添加 仪器细菌
     */
    void addInstrumentGerms(List<InstrumentGermDto> list);

    /**
     * 修改 仪器细菌
     */
    void updateByInstrumentGermId(InstrumentGermDto dto);

    /**
     * 仪器细菌 保存
     *
     * @param needInstrumentGermIds 需要删除
     * @param addList 需要新增
     * @param updateList 需要更新的
     */
    void saveInstrumentGerm(Collection<Long> needInstrumentGermIds, List<InstrumentGermDto> addList,
        List<InstrumentGermDto> updateList);

    /**
     * 根据细菌ID删除
     * 
     * @param instrumentId 仪器ID
     * @param germId 细菌ID
     */
    void deleteByGermId(Long instrumentId, Long germId);

    /**
     * 查询 仪器下 细菌
     */
    InstrumentGermDto selectByGermId(Long instrumentId, Long germId);

}
