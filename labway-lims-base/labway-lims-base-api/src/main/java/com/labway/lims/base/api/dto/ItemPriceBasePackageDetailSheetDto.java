package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * ItemPriceBasePackageDetailSheet
 * 项目价格基础包检验项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/29 10:58
 */
@Getter
@Setter
public class ItemPriceBasePackageDetailSheetDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer index;

    /**
     * 项目编码
     */
    private String testItemCode;

    /**
     * 检验项目编码
     */
    private String testItemName;

    /**
     * 收费价格
     */
    private BigDecimal feePrice;

}
