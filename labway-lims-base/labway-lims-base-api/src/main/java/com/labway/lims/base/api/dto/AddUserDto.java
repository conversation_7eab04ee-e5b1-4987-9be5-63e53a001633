package com.labway.lims.base.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * 添加用户
 */
@Getter
@Setter
public class AddUserDto implements Serializable {
    /**
     * 登录名
     */
    private String username;

    /**
     * 用户名
     */
    private String nickname;


    /**
     * 状态，1:可用，0:不可用
     */
    private Integer status;


    /**
     * 英文签名图
     */
    private String enSign;

    /**
     * 中文签名图
     */
    private String cnSign;

    /**
     * 拥有的角色
     */
    private Set<Long> roleIds;

    /**
     * 默认角色
     */
    private Long roleId;

    /**
     * 拥有的专业组
     */
    private Set<Long> groupIds;


    /**
     * 默认专业组
     */
    private Long groupId;


    /**
     * 性别 1:男 2女
     */
    private Integer sex;


    /**
     * 机构 ID
     */
    private Long orgId;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

}
