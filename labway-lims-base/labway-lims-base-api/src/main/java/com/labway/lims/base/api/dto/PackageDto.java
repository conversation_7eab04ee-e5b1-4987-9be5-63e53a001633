package com.labway.lims.base.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.api.field.CompareContent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 套餐 Dto
 * 
 * <AUTHOR>
 * @since 2023/3/28 16:49
 */
@Getter
@Setter
public class PackageDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 套餐ID
     */
    private Long packageId;
    /**
     * 体检套餐名称
     */
    @Compare("套餐名称")
    private String packageName;
    /**
     * 套餐类型编码
     */
    private String packageTypeCode;
    /**
     * 套餐类型名称
     */
    private String packageType;
    /**
     * 体检单位ID
     */
    private Long physicalGroupId;
    /**
     * 体检单位名称
     */
    private String physicalGroupName;
    /**
     * 送检机构ID
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 1:已经删除 0未删除
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 是否启用(0未启用 1启用)
     * 
     * @see YesOrNoEnum
     */
    @Compare(value = "是否启用",
        content = {@CompareContent(value = "0", valueDesc = "否"), @CompareContent(value = "1", valueDesc = "是")})
    private Integer enable;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人名称
     */
    private String updaterName;
}
