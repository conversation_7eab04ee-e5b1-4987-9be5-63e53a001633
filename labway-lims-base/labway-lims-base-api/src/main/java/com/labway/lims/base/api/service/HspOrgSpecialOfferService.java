package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.AddHspOrgSpecialOfferProjectDto;
import com.labway.lims.base.api.dto.HspOrgSpecialOfferDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface HspOrgSpecialOfferService {

    /**
     * 根据送检机构ID查询送检机构特价项目
     */
    List<HspOrgSpecialOfferDto> selectHspOrgSpecialOfferProject(Long hspOrgId);

    /**
     * 根据送检机构查询 对应机构特价项目
     */
    List<HspOrgSpecialOfferDto> selectByHspOrgIds(Collection<Long> hspOrgIds);

    /**
     * 添加客户（送检机构）特价项目
     */
    List<HspOrgSpecialOfferDto> addHspOrgSpecialOfferProject(AddHspOrgSpecialOfferProjectDto parseObject);

    /**
     * 添加 特价项目
     */
    void addHspOrgSpecialOfferDtos(List<HspOrgSpecialOfferDto> list);

    /**
     * 根据送检机构ID和检验项目ID查询送检机构特价项目
     */
    List<HspOrgSpecialOfferDto> selectByHspOrgIdAndTestItemIds(Long hspOrgId, List<Long> testItemIds);

    /**
     * 根据送检机构删除特价项目
     */
    void deleteByHspOrgId(List<Long> ids);

    /**
     * 根据特价项目ID删除特价项目
     */
    void deleteByOfferId(List<Long> ids);

    /**
     * 根据ID查询
     */
    @Nullable
    HspOrgSpecialOfferDto selectByOfferId(long offerId);

    /**
     * 根据ID查询
     */
    List<HspOrgSpecialOfferDto> selectByOfferIds(Collection<Long> offerIds);

    /**
     * 获取有特价项目的送检机构
     */
    Collection<Long> selectHasSpecialProjectHspOrg();

    /**
     * 根据 送检机构 + 时间范围 + 就诊类型 查询特价项目
     */
    List<HspOrgSpecialOfferDto> selectByByHspOrgIdsAndDateRangeAndAndApplyTypes(Collection<Long> hspOrgIds,
        Date minDate, Date maxDate, Collection<String> applyTypes);

    /**
     * 修改
     */
    void updateSpecialOffer(HspOrgSpecialOfferDto updateSpecialOfferDto);

    /**
     * 导入送检机构特价项目
     */
    void importHspOrgSpecialOfferProject(List<HspOrgSpecialOfferDto> dtos);
}
