package com.labway.lims.microbiology.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleResultDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleResultService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.microbiology.mapper.TbMicrobiologySampleResultMapper;
import com.labway.lims.microbiology.mapstruct.MicrobiologySampleResultConverter;
import com.labway.lims.microbiology.model.TbMicrobiologySampleResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 15:29
 */
@Slf4j
@DubboService
public class MicrobiologySampleResultServiceImpl implements MicrobiologySampleResultService {
    @Resource
    private TbMicrobiologySampleResultMapper tbMicrobiologySampleResultMapper;
    @Resource
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private MicrobiologySampleResultConverter microbiologySampleResultConverter;

    @Override
    public List<MicrobiologySampleResultDto> selectByMicrobiologySampleId(long microbiologySampleId) {
        final LambdaQueryWrapper<TbMicrobiologySampleResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMicrobiologySampleResult::getMicrobiologySampleId, microbiologySampleId);

        return tbMicrobiologySampleResultMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    public List<MicrobiologySampleResultDto> selectByMicrobiologySampleIds(Collection<Long> microbiologySampleIds) {
        if (CollectionUtils.isEmpty(microbiologySampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMicrobiologySampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMicrobiologySampleResult::getMicrobiologySampleId, microbiologySampleIds);
        queryWrapper.eq(TbMicrobiologySampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        return microbiologySampleResultConverter
            .microbiologySampleResultDtoListFromTbObjList(tbMicrobiologySampleResultMapper.selectList(queryWrapper));
    }

    private MicrobiologySampleResultDto convert(TbMicrobiologySampleResult result) {
        if (Objects.isNull(result)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(result), MicrobiologySampleResultDto.class);
    }

    @Override
    public long addMicroResult(MicrobiologySampleResultDto dto) {
        final MicrobiologySampleDto sample =
            microbiologySampleService.selectByMicrobiologySampleId(dto.getMicrobiologySampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("当前样本不存在,添加结果失败");
        }
        final LoginUserHandler.User user = LoginUserHandler.get();
        final TbMicrobiologySampleResult result = new TbMicrobiologySampleResult();
        result.setResult(dto.getResult());
        result.setResultDesc(dto.getResultDesc());
        result.setResultProperty(dto.getResultProperty());
        result.setMicrobiologySampleResultId(
            ObjectUtils.defaultIfNull(dto.getMicrobiologySampleResultId(), snowflakeService.genId()));
        result.setMicrobiologySampleId(dto.getMicrobiologySampleId());
        result.setApplyId(dto.getApplyId());
        result.setCreateDate(new Date());
        result.setCreatorId(user.getUserId());
        result.setCreatorName(user.getNickname());
        result.setUpdateDate(new Date());
        result.setUpdaterId(user.getUserId());
        result.setUpdaterName(user.getNickname());
        result.setIsDelete(YesOrNoEnum.NO.getCode());
        result.setApplySampleId(dto.getApplySampleId());

        if (tbMicrobiologySampleResultMapper.insert(result) < 1) {
            throw new IllegalStateException("微生物结果添加失败");
        }
        log.info("用户 [{}] 添加微生物结果成功 [{}]", user.getNickname(), JSON.toJSONString(dto));
        return result.getMicrobiologySampleResultId();
    }

    @Override
    @Transactional
    public void addBatchMicroResult(Collection<Long>sampleIds, Collection<MicrobiologySampleResultDto> results) {
        if (CollectionUtils.isNotEmpty(results)) {
            //批量生成新ID
            LinkedList<Long> ids = snowflakeService.genIds(results.size());
            //匹配赋值
            List<MicrobiologySampleResultDto> updateList = new ArrayList<>();
            for (MicrobiologySampleResultDto resultDto : results) {
                //生成新的结果ID
                resultDto.setMicrobiologySampleResultId(ids.pop());
                updateList.add(resultDto);
            }

            //先删除原来的所有结果
            deleteByMicrobiologySampleIds(sampleIds);

            //再批量插入新结果
            tbMicrobiologySampleResultMapper.addBatch(updateList);
        }
    }

    @Override
    public boolean updateById(MicrobiologySampleResultDto dto) {
        final TbMicrobiologySampleResult result =
            JSON.parseObject(JSON.toJSONString(dto), TbMicrobiologySampleResult.class);
        result.setUpdaterId(LoginUserHandler.get().getUserId());
        result.setUpdaterName(LoginUserHandler.get().getNickname());
        result.setUpdateDate(new Date());

        if (tbMicrobiologySampleResultMapper.updateById(result) < 1) {
            return false;
        }
        log.info("用户 [{}] 修改微生物结果成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(dto));
        return true;
    }

    @Override
    public boolean deleteById(long microbiologySampleResultId) {
        if (tbMicrobiologySampleResultMapper.deleteById(microbiologySampleResultId) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除微生物样本结果样本 [{}] 成功", LoginUserHandler.get().getNickname(), microbiologySampleResultId);
        return true;

    }

    @Override
    public boolean deleteByMicrobiologySampleId(long microbiologySampleId) {
        final LambdaQueryWrapper<TbMicrobiologySampleResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMicrobiologySampleResult::getMicrobiologySampleId, microbiologySampleId);

        if (tbMicrobiologySampleResultMapper.delete(wrapper) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除微生物样本 [{}] 结果成功", LoginUserHandler.get().getNickname(), microbiologySampleId);
        return true;
    }

    @Override
    public void deleteByMicrobiologySampleIds(Collection<Long> microbiologySampleIds) {

        if (CollectionUtils.isEmpty(microbiologySampleIds)) {
            return;
        }

        final LambdaQueryWrapper<TbMicrobiologySampleResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbMicrobiologySampleResult::getMicrobiologySampleId, microbiologySampleIds);

        tbMicrobiologySampleResultMapper.delete(wrapper);

        log.info("用户 [{}] 删除微生物样本 {} 结果成功", LoginUserHandler.get().getNickname(), microbiologySampleIds);
    }

    @Nullable
    @Override
    public MicrobiologySampleResultDto selectByMicrobiologySampleResultId(long microbiologySampleResultId) {
        return convert(tbMicrobiologySampleResultMapper.selectById(microbiologySampleResultId));
    }

}
