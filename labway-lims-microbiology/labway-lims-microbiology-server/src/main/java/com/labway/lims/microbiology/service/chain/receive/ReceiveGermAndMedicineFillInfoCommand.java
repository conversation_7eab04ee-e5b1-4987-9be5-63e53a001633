package com.labway.lims.microbiology.service.chain.receive;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.microbiology.MicroTestMethodEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.GermDto;
import com.labway.lims.base.api.dto.InstrumentGermDto;
import com.labway.lims.base.api.dto.InstrumentMedicineResultDto;
import com.labway.lims.base.api.dto.MedicineDto;
import com.labway.lims.base.api.dto.MedicineGermRelationDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.base.api.service.MedicineGermRelationService;
import com.labway.lims.base.api.service.MedicineService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleGermService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/17 14:44
 */
@Slf4j
@Component
public class ReceiveGermAndMedicineFillInfoCommand implements Filter, Command {

    @Resource
    private MicrobiologySampleService microbiologySampleService;
    @Resource
    private MicrobiologySampleGermService microbiologySampleGermService;
    @Resource
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private GermService germService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private MedicineService medicineService;
    @DubboReference
    private MedicineGermRelationService medicineGermRelationService;

    @DubboReference
    private SystemParamService systemParamService;

    @Override
    public boolean execute(Context c) throws Exception {
        final ReceiveGermAndMedicineContext context = ReceiveGermAndMedicineContext.from(c);
        final Long sampleId = context.getMicrobiologySampleId();
        final MicrobiologySampleDto sample = microbiologySampleService.selectByMicrobiologySampleId(sampleId);
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("当前样本为空通知失败");
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本为空保存结果失败");
        }

        final List<ApplySampleItemDto> items = applySampleItemService.selectByApplySampleId(applySample.getApplySampleId());
        if (CollectionUtils.isEmpty(items)) {
            throw new IllegalStateException("当前样本检验项目为空保存结果失败");
        }
        //样本下的细菌
        final List<MicrobiologySampleGermDto> sampleGerms = microbiologySampleGermService.selectByMicrobiologySampleId(sampleId);
        if (CollectionUtils.isNotEmpty(sampleGerms)) {

            final List<MicrobiologyGermMedicineDto> microbiologyGermMedicines = microbiologyGermMedicineService.selectByMicrobiologySampleId(sampleId);

            context.put(ReceiveGermAndMedicineContext.MICRO_SAMPLE_GERMS, sampleGerms);
            context.put(ReceiveGermAndMedicineContext.MICRO_SAMPLE_GERM_MEDICINES, microbiologyGermMedicines);
        }

        //接收到的细菌
        final Map<InstrumentGermDto, List<InstrumentMedicineResultDto>> germMap = context.getGermMap();
        final List<Long> germIds = germMap.keySet().stream().map(InstrumentGermDto::getGermId).collect(Collectors.toList());
        final List<GermDto> germs = germService.selectByGermIds(germIds);

        final Map<Long, MedicineDto> medMap = medicineService.selectByOrgId(LoginUserHandler.get().getOrgId()).stream()
                .collect(Collectors.toMap(MedicineDto::getMedicineId, v -> v, (a, b) -> a));

        final List<Long> genusIds = germs.stream().map(GermDto::getGermGenusId).collect(Collectors.toList());
        final List<MedicineGermRelationDto> medicineGermRelations = medicineGermRelationService.selectByGermGenusIds(genusIds);

        final Map<Long, List<MedicineGermRelationDto>> relateMap = medicineGermRelations.stream()
                .collect(Collectors.groupingBy(MedicineGermRelationDto::getMedicineId));

        //仪器通知的细菌
        final List<MicrobiologySampleGermDto> instrumentSampleGerms = convertGerm(germs, sample, items);
        final List<MicrobiologyGermMedicineDto> instrumentMedicines = converMedicine(germMap, instrumentSampleGerms, medMap, relateMap, sample, items);

        context.put(ReceiveGermAndMedicineContext.INSTRUMENT_MICRO_SAMPLE_GERMS, instrumentSampleGerms);
        context.put(ReceiveGermAndMedicineContext.INSTRUMENT_MICRO_SAMPLE_GERM_MEDICINES, instrumentMedicines);
        context.put(ReceiveGermAndMedicineContext.APPLY_SAMPLE, applySample);
        context.put(ReceiveGermAndMedicineContext.MICRO_SAMPLE, sample);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    private List<MicrobiologySampleGermDto> convertGerm(List<GermDto> germs, MicrobiologySampleDto sample,
                                                        List<ApplySampleItemDto> items) {
        final LinkedList<Long> ids = snowflakeService.genIds(germs.size());
        final LinkedList<MicrobiologySampleGermDto> instrumentSampleGerms = new LinkedList<>();
        for (GermDto germ : germs) {
            final MicrobiologySampleGermDto germSample = new MicrobiologySampleGermDto();
            germSample.setMicrobiologySampleGermId(ids.pop());
            germSample.setApplyId(sample.getApplyId());
            germSample.setMicrobiologySampleId(sample.getMicrobiologySampleId());
            germSample.setTestItemId(items.iterator().next().getTestItemId());
            germSample.setTestItemCode(items.iterator().next().getTestItemCode());
            germSample.setTestItemName(items.iterator().next().getTestItemName());
            germSample.setGermId(germ.getGermId());
            germSample.setGermCode(germ.getGermCode());
            germSample.setGermName(germ.getGermName());
            germSample.setGermCount(StringUtils.EMPTY);
            germSample.setGermCountCode(StringUtils.EMPTY);
            germSample.setTestMethod(MicroTestMethodEnum.INSTRUMENT.getDesc());
            germSample.setTestMethodCode(MicroTestMethodEnum.INSTRUMENT.getCode());
            germSample.setGermRemark(StringUtils.EMPTY);
            germSample.setGermRemarkCode(StringUtils.EMPTY);
            germSample.setApplySampleId(sample.getApplySampleId());
            germSample.setGermGenusId(germ.getGermGenusId());
            germSample.setGermGenusCode(germ.getGermGenusCode());
            instrumentSampleGerms.add(germSample);
        }
        return instrumentSampleGerms;
    }

    private List<MicrobiologyGermMedicineDto> converMedicine(Map<InstrumentGermDto, List<InstrumentMedicineResultDto>> germMap
            , List<MicrobiologySampleGermDto> sampleGerms, Map<Long, MedicineDto> medMap
            , Map<Long, List<MedicineGermRelationDto>> relateMap, MicrobiologySampleDto sample, List<ApplySampleItemDto> items
    ) {
        final Optional<SystemParamDto> systemParamDto = Optional.ofNullable(systemParamService.selectByParamName(SystemParamNameEnum.MICROBIOLOGY_NOTIFY_RESULT.getCode(), LoginUserHandler.get().getOrgId()));
        final List<String> instrumentCodes = Arrays.asList(systemParamDto.map(SystemParamDto::getParamValue).orElse(Strings.EMPTY).split(","));

        final LinkedList<MicrobiologyGermMedicineDto> sampleMedicines = new LinkedList<>();

        final LinkedList<Long> genIds = snowflakeService.genIds(1000);
        final Map<Long, MicrobiologySampleGermDto> sampleGermMap = sampleGerms.stream()
                .collect(Collectors.toMap(MicrobiologySampleGermDto::getGermId, v -> v, (a, b) -> a));

        for (InstrumentGermDto e : germMap.keySet()) {
            final List<InstrumentMedicineResultDto> instrumentMedicines = germMap.get(e);
            if (CollectionUtils.isEmpty(instrumentMedicines)) {
                log.info("当前细菌下药物结果为空");
                continue;
            }

            // 配置是否包含该仪器
            final boolean containsInstrument = instrumentCodes.contains(e.getInstrumentCode());

            for (InstrumentMedicineResultDto medicine : instrumentMedicines) {
                final MicrobiologySampleGermDto sampleGerm = sampleGermMap.get(e.getGermId());
                if (Objects.isNull(sampleGerm)) {
                    continue;
                }
                final MedicineDto med = medMap.get(medicine.getMedicineId());
                if (Objects.isNull(med)) {
                    log.info("当前药物不存在跳过通知");
                    continue;
                }
                final MicrobiologyGermMedicineDto m = new MicrobiologyGermMedicineDto();
                m.setMicrobiologyGermMedicineId(genIds.pop());
                m.setMicrobiologySampleId(sample.getMicrobiologySampleId());
                m.setApplyId(sample.getApplyId());
                m.setApplySampleId(sample.getApplySampleId());
                m.setMicrobiologySampleGermId(sampleGerm.getMicrobiologySampleGermId());
                m.setTestItemId(items.iterator().next().getTestItemId());
                m.setTestItemCode(items.iterator().next().getTestItemCode());
                m.setTestItemName(items.iterator().next().getTestItemName());
                m.setGermId(sampleGerm.getGermId());
                m.setGermCode(sampleGerm.getGermCode());
                m.setGermName(sampleGerm.getGermName());
                m.setMedicineId(medicine.getMedicineId());
                m.setMedicineCode(medicine.getMedicineCode());
                m.setMedicineName(med.getMedicineName());
                m.setMedicineMethod(MicroTestMethodEnum.INSTRUMENT.getDesc());
                m.setMedicineMethodCode(MicroTestMethodEnum.INSTRUMENT.getCode());

                m.setFormula(StringUtils.defaultString(medicine.getFormula()));
                m.setResult(StringUtils.defaultString(medicine.getResult()));

                final List<MedicineGermRelationDto> relations = relateMap.get(medicine.getMedicineId());

                m.setSusceptibility(StringUtils.defaultString(medicine.getSusceptibility()));
                m.setUnit(StringUtils.EMPTY);

                if (CollectionUtils.isNotEmpty(relations)) {
                    final MedicineGermRelationDto relation = relations.stream()
                            .filter(k ->
                                    // 当前细菌菌属相同 && 检测方法相同（MIC/KB）
                                    Objects.equals(k.getGermGenusId(), sampleGerm.getGermGenusId()) && Objects.equals(sampleGerm.getTestMethodCode(), k.getExamMethodCode()))
                            .findFirst().orElse(null);
                    if (Objects.nonNull(relation)) {
                        m.setUnit(relation.getReferUnit());
                        m.setRange(relation.getReferValueMin(), relation.getReferValueMax());
                        // 写入系统维护的折点范围
                        m.setFoldPointScope(relation.getFoldPointScope());
                    }
                }

                // 包含该仪器则， 则使用仪器折点范围覆盖参考范围折点范围
                if (containsInstrument) {
                    m.setFoldPointScope(StringUtils.defaultString(medicine.getFoldPointScope(), StringUtils.defaultString(m.getFoldPointScope())));
                }
                m.setGroup(medicine.getGroup());
                sampleMedicines.add(m);
            }
        }
        return sampleMedicines;
    }
}
