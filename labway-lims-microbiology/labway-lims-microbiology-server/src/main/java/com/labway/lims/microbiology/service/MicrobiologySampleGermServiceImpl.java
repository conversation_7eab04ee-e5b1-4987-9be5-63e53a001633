package com.labway.lims.microbiology.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.GermDto;
import com.labway.lims.base.api.dto.MedicineDto;
import com.labway.lims.base.api.dto.MedicineGermRelationDto;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.base.api.service.MedicineGermRelationService;
import com.labway.lims.base.api.service.MedicineService;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermUpdateDto;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleGermService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.microbiology.mapper.TbMicrobiologySampleGermMapper;
import com.labway.lims.microbiology.model.TbMicrobiologySampleGerm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 15:29
 */
@Slf4j
@DubboService
public class MicrobiologySampleGermServiceImpl implements MicrobiologySampleGermService {
    @Resource
    private TbMicrobiologySampleGermMapper tbMicrobiologySampleGermMapper;
    @Resource
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private GermService germService;
    @DubboReference
    private MedicineGermRelationService medicineGermRelationService;
    @Resource
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;
    @DubboReference
    private MedicineService medicineService;

    @Override
    public List<MicrobiologySampleGermDto> selectByMicrobiologySampleId(long microbiologySampleId) {
        final LambdaQueryWrapper<TbMicrobiologySampleGerm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMicrobiologySampleGerm::getMicrobiologySampleId, microbiologySampleId);
        wrapper.orderByAsc(TbMicrobiologySampleGerm::getMicrobiologySampleGermId);
        return tbMicrobiologySampleGermMapper.selectList(wrapper).stream().map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<MicrobiologySampleGermDto> selectByMicrobiologySampleIds(Collection<Long> microbiologySampleIds) {
        final LambdaQueryWrapper<TbMicrobiologySampleGerm> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbMicrobiologySampleGerm::getMicrobiologySampleId, microbiologySampleIds);
        wrapper.orderByAsc(TbMicrobiologySampleGerm::getMicrobiologySampleGermId);
        return tbMicrobiologySampleGermMapper.selectList(wrapper).stream().map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    public long addGerm(MicrobiologySampleGermDto dto) {

        final MicrobiologySampleDto sampleDto =
                microbiologySampleService.selectByMicrobiologySampleId(dto.getMicrobiologySampleId());
        if (Objects.isNull(sampleDto)) {
            throw new IllegalStateException("当前样本不存在,添加失败");
        }

        final LoginUserHandler.User user = LoginUserHandler.get();
        final TbMicrobiologySampleGerm germ = JSON.parseObject(JSON.toJSONString(dto), TbMicrobiologySampleGerm.class);
        germ.setMicrobiologySampleGermId(
                ObjectUtils.defaultIfNull(dto.getMicrobiologySampleGermId(), snowflakeService.genId()));
        germ.setCreateDate(new Date());
        germ.setCreatorName(user.getNickname());
        germ.setCreatorId(user.getUserId());
        germ.setUpdateDate(new Date());
        germ.setUpdaterName(user.getNickname());
        germ.setUpdaterId(user.getUserId());
        germ.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbMicrobiologySampleGermMapper.insert(germ) < 1) {
            throw new IllegalStateException("微生物细菌添加失败");
        }

        log.info("用户 [{}] 添加微生物细菌成功 [{}]", user.getNickname(), JSON.toJSONString(dto));
        return germ.getMicrobiologySampleGermId();
    }

    @Override
    public void addGermBatch(Collection<MicrobiologySampleGermDto> dtos) {

        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }

        final LoginUserHandler.User user = LoginUserHandler.get();
        for (MicrobiologySampleGermDto germ : dtos) {
            germ.setMicrobiologySampleGermId(
                    ObjectUtils.defaultIfNull(germ.getMicrobiologySampleGermId(), snowflakeService.genId()));
            germ.setCreateDate(new Date());
            germ.setCreatorName(user.getNickname());
            germ.setCreatorId(user.getUserId());
            germ.setUpdateDate(new Date());
            germ.setUpdaterName(user.getNickname());
            germ.setUpdaterId(user.getUserId());
            germ.setIsDelete(YesOrNoEnum.NO.getCode());
        }
        tbMicrobiologySampleGermMapper.addBatch(dtos);

    }

    @Override
    public boolean updateById(MicrobiologySampleGermDto dto) {

        final TbMicrobiologySampleGerm germ = JSON.parseObject(JSON.toJSONString(dto), TbMicrobiologySampleGerm.class);
        germ.setUpdaterId(LoginUserHandler.get().getUserId());
        germ.setUpdaterName(LoginUserHandler.get().getNickname());
        germ.setUpdateDate(new Date());

        if (tbMicrobiologySampleGermMapper.updateById(germ) < 1) {
            return false;
        }
        log.info("用户 [{}] 修改微生物细菌成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(dto));
        return true;
    }

    @Override
    public boolean deleteByMicrobiologySampleGermId(long microbiologySampleGermId) {
        if (tbMicrobiologySampleGermMapper.deleteById(microbiologySampleGermId) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除微生物样本细菌 [{}] 成功", LoginUserHandler.get().getNickname(), microbiologySampleGermId);
        return true;
    }

    @Override
    public void deleteByMicrobiologySampleGermIds(Collection<Long> microbiologySampleGermIds) {
        if (CollectionUtils.isEmpty(microbiologySampleGermIds)) {
            return;
        }
        tbMicrobiologySampleGermMapper.deleteBatchIds(microbiologySampleGermIds);

        log.info("用户 [{}] 删除微生物样本细菌 [{}] 成功", LoginUserHandler.get().getNickname(), microbiologySampleGermIds);

    }

    @Override
    public boolean deleteByMicrobiologySampleId(long microbiologySampleId) {
        final LambdaQueryWrapper<TbMicrobiologySampleGerm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMicrobiologySampleGerm::getMicrobiologySampleId, microbiologySampleId);
        if (tbMicrobiologySampleGermMapper.delete(wrapper) < 1) {
            return false;
        }
        log.info("用户 [{}] 删除微生物样本 [{}] 细菌成功", LoginUserHandler.get().getNickname(), microbiologySampleId);
        return true;
    }

    @Override
    public void deleteByMicrobiologySampleIds(Collection<Long> microbiologySampleIds) {

        if (CollectionUtils.isEmpty(microbiologySampleIds)) {
            return;
        }

        final LambdaQueryWrapper<TbMicrobiologySampleGerm> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbMicrobiologySampleGerm::getMicrobiologySampleId, microbiologySampleIds);
        tbMicrobiologySampleGermMapper.delete(wrapper);
        log.info("用户 [{}] 删除微生物样本 {} 细菌成功", LoginUserHandler.get().getNickname(), microbiologySampleIds);
    }

    @Nullable
    @Override
    public MicrobiologySampleGermDto selectByMicrobiologySampleGermId(long microbiologySampleGermId) {
        final LambdaQueryWrapper<TbMicrobiologySampleGerm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMicrobiologySampleGerm::getMicrobiologySampleGermId, microbiologySampleGermId).last("limit 1");

        return convert(tbMicrobiologySampleGermMapper.selectOne(wrapper));
    }

    @Nullable
    @Override
    public List<MicrobiologySampleGermDto> selectByMicrobiologySampleGermIds(List<Long> microbiologySampleGermIds) {
        final LambdaQueryWrapper<TbMicrobiologySampleGerm> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbMicrobiologySampleGerm::getMicrobiologySampleGermId, microbiologySampleGermIds);
        return tbMicrobiologySampleGermMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public boolean isExitMicrobiologySampleGerm(long microbiologySampleId, long germId,
                                                long excludeMicrobiologySampleGermId) {
        LambdaQueryWrapper<TbMicrobiologySampleGerm> queryWrapper = Wrappers.lambdaQuery();
        // 排除 传入 微生物细菌id
        queryWrapper.ne(excludeMicrobiologySampleGermId > 0, TbMicrobiologySampleGerm::getMicrobiologySampleGermId,
                excludeMicrobiologySampleGermId);

        queryWrapper.eq(TbMicrobiologySampleGerm::getMicrobiologySampleId, microbiologySampleId);
        queryWrapper.eq(TbMicrobiologySampleGerm::getGermId, germId);
        queryWrapper.eq(TbMicrobiologySampleGerm::getIsDelete, YesOrNoEnum.NO.getCode());
        return tbMicrobiologySampleGermMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSampleGerm(MicrobiologySampleDto sampleDto, MicrobiologySampleGermDto sampleGermDto,
                                 MicrobiologySampleGermUpdateDto updateDto) {

        // 修改细菌
        if (Objects.equals(updateDto.getUpdateType(), 1)) {
            updateSampleGermGermId(sampleDto, sampleGermDto, updateDto.getGermId());
        }

        // 修改细菌数量
        if (Objects.equals(updateDto.getUpdateType(), 2)) {
            MicrobiologySampleGermDto update = new MicrobiologySampleGermDto();
            update.setMicrobiologySampleGermId(sampleGermDto.getMicrobiologySampleGermId());
            update.setGermCount(updateDto.getGermCount());
            update.setGermCountCode(updateDto.getGermCountCode());
            this.updateById(update);
        }

        // 修改 细菌备注
        if (Objects.equals(updateDto.getUpdateType(), 3)) {
            MicrobiologySampleGermDto update = new MicrobiologySampleGermDto();
            update.setMicrobiologySampleGermId(sampleGermDto.getMicrobiologySampleGermId());
            update.setGermRemark(updateDto.getGermRemark());
            update.setGermRemarkCode(updateDto.getGermRemarkCode());
            this.updateById(update);
        }

        // 修改 检测方法
        if (Objects.equals(updateDto.getUpdateType(), 4)) {
            updateSampleGermTestMethod(sampleDto, sampleGermDto, StringUtils.defaultString(updateDto.getTestMethod()),
                    StringUtils.defaultString(updateDto.getTestMethodCode()));
        }

    }

    @Override
    public void updateByMicrobiologySampleGermIds(Collection<MicrobiologySampleGermDto> germs) {
        if (CollectionUtils.isEmpty(germs)) {
            return;
        }
        tbMicrobiologySampleGermMapper.updateByMicrobiologySampleGermIds(germs);
    }

    /**
     * 修改 检测方法
     */

    private void updateSampleGermTestMethod(MicrobiologySampleDto sampleDto, MicrobiologySampleGermDto sampleGermDto,
                                            String testMethod, String testMethodCode) {
        if (Objects.equals(sampleGermDto.getTestMethod(), testMethod)) {
            return;
        }
        MicrobiologySampleGermDto update = new MicrobiologySampleGermDto();
        update.setMicrobiologySampleGermId(sampleGermDto.getMicrobiologySampleGermId());
        update.setTestMethod(testMethod);
        update.setTestMethodCode(testMethodCode);
        this.updateById(update);

        // 细菌 关联 细菌菌属 下 所有药物
        List<MedicineGermRelationDto> relationDtos =
                medicineGermRelationService.selectByGermGenusId(sampleGermDto.getGermGenusId());

        Set<Long> medicineIds =
                relationDtos.stream().map(MedicineGermRelationDto::getMedicineId).collect(Collectors.toSet());
        Map<Long, MedicineDto> medicineDtoById = medicineService.selectByMedicineIds(medicineIds).stream()
                .collect(Collectors.toMap(MedicineDto::getMedicineId, Function.identity()));
        // 以药物id 分组
        Map<Long, List<MedicineGermRelationDto>> groupingByMedicineId =
                relationDtos.stream().collect(Collectors.groupingBy(MedicineGermRelationDto::getMedicineId));

        // 最新的 微生物药物
        List<MicrobiologyGermMedicineDto> germMedicineDtoList = Lists.newArrayList();

        for (Map.Entry<Long, List<MedicineGermRelationDto>> entry : groupingByMedicineId.entrySet()) {
            MedicineGermRelationDto relationDto = entry.getValue().stream()
                    // // 检测方法没有 药物 检测方法随机取一条 或检测方法存在 找与其相同的药物 若存在只会有一条
                    .filter(obj -> StringUtils.isBlank(testMethodCode)
                            || Objects.equals(obj.getExamMethodCode(), testMethodCode))
                    .collect(Collectors.toList()).stream().findFirst().orElse(null);
            if (Objects.isNull(relationDto)) {
                // 菌属药物没有
                continue;
            }
            // 对应药物信息
            MedicineDto medicineDto = medicineDtoById.get(relationDto.getMedicineId());
            if (Objects.isNull(medicineDto)) {
                // 药物无效
                continue;
            }
            MicrobiologyGermMedicineDto germMedicineDto = new MicrobiologyGermMedicineDto();
            germMedicineDto.setMicrobiologySampleId(sampleDto.getMicrobiologySampleId());
            germMedicineDto.setApplyId(sampleDto.getApplyId());
            germMedicineDto.setApplySampleId(sampleDto.getApplySampleId());
            germMedicineDto.setMicrobiologySampleGermId(sampleGermDto.getMicrobiologySampleGermId());
            germMedicineDto.setTestItemId(sampleGermDto.getTestItemId());
            germMedicineDto.setTestItemCode(sampleGermDto.getTestItemCode());
            germMedicineDto.setTestItemName(sampleGermDto.getTestItemName());
            germMedicineDto.setGermId(sampleGermDto.getGermId());
            germMedicineDto.setGermCode(sampleGermDto.getGermCode());
            germMedicineDto.setGermName(sampleGermDto.getGermName());
            germMedicineDto.setMedicineId(relationDto.getMedicineId());
            germMedicineDto.setMedicineCode(medicineDto.getMedicineCode());
            germMedicineDto.setMedicineName(medicineDto.getMedicineName());
            germMedicineDto.setMedicineMethod(relationDto.getExamMethodName());
            germMedicineDto.setMedicineMethodCode(relationDto.getExamMethodCode());
            germMedicineDto.setFormula(StringUtils.EMPTY);
            germMedicineDto.setResult(StringUtils.EMPTY);
            germMedicineDto.setSusceptibility(StringUtils.EMPTY);
            germMedicineDto.setUnit(relationDto.getReferUnit());
            germMedicineDto.setSusceptibility(relationDto.getSusceptibility());
            germMedicineDto.setRange(relationDto.getReferValueMin(), relationDto.getReferValueMax());
            germMedicineDto.setGroup(StringUtils.EMPTY);
            germMedicineDto.setFoldPointScope(relationDto.getFoldPointScope());
            germMedicineDtoList.add(germMedicineDto);
        }

        // 删除原来微生物细菌 对应 微生物药物
        microbiologyGermMedicineService.deleteByMicrobiologySampleGermId(sampleGermDto.getMicrobiologySampleGermId());

        // 新增新的微生物药物
        microbiologyGermMedicineService.addGermMedicineBatch(germMedicineDtoList);

    }

    /**
     * 修改 细菌
     */
    private void updateSampleGermGermId(MicrobiologySampleDto sampleDto, MicrobiologySampleGermDto sampleGermDto,
                                        Long germId) {
        if (Objects.equals(sampleGermDto.getGermId(), germId)) {
            // 细菌没有变化
            return;
        }
        GermDto germDto = germService.selectByGermId(germId);
        if (Objects.isNull(germDto)) {
            throw new LimsException("当前细菌不存在");
        }
        // 微生物细菌是否已存在
        if (this.isExitMicrobiologySampleGerm(sampleDto.getMicrobiologySampleId(), germId,
                sampleGermDto.getMicrobiologySampleGermId())) {
            throw new IllegalArgumentException("细菌不可重复");
        }

        MicrobiologySampleGermDto update = new MicrobiologySampleGermDto();
        update.setMicrobiologySampleGermId(sampleGermDto.getMicrobiologySampleGermId());
        update.setGermId(germDto.getGermId());
        update.setGermCode(germDto.getGermCode());
        update.setGermName(germDto.getGermName());
        update.setGermGenusCode(germDto.getGermGenusCode());
        update.setGermGenusId(germDto.getGermGenusId());
        update.setTestMethod(StringUtils.EMPTY);
        update.setTestMethodCode(StringUtils.EMPTY);
        this.updateById(update);
        // 细菌 关联 细菌菌属 下 所有药物
        List<MedicineGermRelationDto> relationDtos =
                medicineGermRelationService.selectByGermGenusId(germDto.getGermGenusId());

        Set<Long> medicineIds =
                relationDtos.stream().map(MedicineGermRelationDto::getMedicineId).collect(Collectors.toSet());
        Map<Long, MedicineDto> medicineDtoById = medicineService.selectByMedicineIds(medicineIds).stream()
                .collect(Collectors.toMap(MedicineDto::getMedicineId, Function.identity()));
        // 以药物id 分组
        Map<Long, List<MedicineGermRelationDto>> groupingByMedicineId =
                relationDtos.stream().collect(Collectors.groupingBy(MedicineGermRelationDto::getMedicineId));

        // 最新的 微生物药物
        List<MicrobiologyGermMedicineDto> germMedicineDtoList = Lists.newArrayList();

        for (Map.Entry<Long, List<MedicineGermRelationDto>> entry : groupingByMedicineId.entrySet()) {
            MedicineGermRelationDto relationDto = entry.getValue().get(NumberUtils.INTEGER_ZERO);

            // 对应药物信息
            MedicineDto medicineDto = medicineDtoById.get(relationDto.getMedicineId());
            if (Objects.isNull(medicineDto)) {
                // 药物无效
                continue;
            }
            MicrobiologyGermMedicineDto germMedicineDto = new MicrobiologyGermMedicineDto();
            germMedicineDto.setMicrobiologySampleId(sampleDto.getMicrobiologySampleId());
            germMedicineDto.setApplyId(sampleDto.getApplyId());
            germMedicineDto.setApplySampleId(sampleDto.getApplySampleId());
            germMedicineDto.setMicrobiologySampleGermId(sampleGermDto.getMicrobiologySampleGermId());
            germMedicineDto.setTestItemId(sampleGermDto.getTestItemId());
            germMedicineDto.setTestItemCode(sampleGermDto.getTestItemCode());
            germMedicineDto.setTestItemName(sampleGermDto.getTestItemName());
            germMedicineDto.setGermId(germDto.getGermId());
            germMedicineDto.setGermCode(germDto.getGermCode());
            germMedicineDto.setGermName(germDto.getGermName());
            germMedicineDto.setMedicineId(relationDto.getMedicineId());
            germMedicineDto.setMedicineCode(medicineDto.getMedicineCode());
            germMedicineDto.setMedicineName(medicineDto.getMedicineName());
            germMedicineDto.setMedicineMethod(relationDto.getExamMethodName());
            germMedicineDto.setMedicineMethodCode(relationDto.getExamMethodCode());
            germMedicineDto.setFormula(StringUtils.EMPTY);
            germMedicineDto.setResult(StringUtils.EMPTY);
            germMedicineDto.setSusceptibility(StringUtils.EMPTY);
            germMedicineDto.setUnit(relationDto.getReferUnit());
            germMedicineDto.setSusceptibility(relationDto.getSusceptibility());
            germMedicineDto.setRange(relationDto.getReferValueMin(), relationDto.getReferValueMax());
            germMedicineDto.setGroup(StringUtils.EMPTY);
            germMedicineDto.setFoldPointScope(relationDto.getFoldPointScope());
            germMedicineDtoList.add(germMedicineDto);
        }
        // 删除原来微生物细菌 对应 微生物药物
        microbiologyGermMedicineService.deleteByMicrobiologySampleGermId(sampleGermDto.getMicrobiologySampleGermId());

        // 新增新的微生物药物
        microbiologyGermMedicineService.addGermMedicineBatch(germMedicineDtoList);
    }

    private MicrobiologySampleGermDto convert(TbMicrobiologySampleGerm germ) {
        if (Objects.isNull(germ)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(germ), MicrobiologySampleGermDto.class);
    }
}
