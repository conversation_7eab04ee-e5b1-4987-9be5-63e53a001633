package com.labway.lims.microbiology.vo;

import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import com.labway.lims.base.api.dto.GermGenusExamMethodDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class MicroGermMedicineVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long microbiologyGermMedicineId;

    /**
     * 样本ID
     */
    private Long microbiologySampleId;

    /**
     * 申请单
     */
    private Long applyId;

    /**
     * 样本细菌
     */
    private Long microbiologySampleGermId;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 细菌
     */
    private Long germId;

    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 细菌名称
     */
    private String germName;

    /**
     * 药物
     */
    private Long medicineId;

    /**
     * 药物编码
     */
    private String medicineCode;

    /**
     * 药物名称
     */
    private String medicineName;

    /**
     * 药物方法
     */
    private String medicineMethod;

    /**
     * 药物结果前缀
     */
    private String formula;

    /**
     * 结果
     */
    private String result;

    /**
     * 敏感度
     *
     * @see MicroSusceptibilityEnum
     */
    private String susceptibility;

    /**
     * 单位
     */
    private String unit;

    /**
     * 参考值
     */
    private String range;

    /**
     * 检测方法
     */
    private List<GermGenusExamMethodDto> examMethod;
    /**
     * 分组
     */
    private String group;

    /**
     * 报告顺序
     */
    private Integer  reportSort;

    /**
     * 折点范围
     */
    private String foldPointScope;
}