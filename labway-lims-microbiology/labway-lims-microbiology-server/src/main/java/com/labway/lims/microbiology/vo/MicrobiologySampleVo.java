package com.labway.lims.microbiology.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 微生物样本
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class MicrobiologySampleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long microbiologySampleId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单
     */
    private Long applyId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组
     */
    private String groupName;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 专业小组
     */
    private String instrumentGroupName;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器信息
     */
    private Long instrumentId;

    /**
     * 检验日期，暂定二次分拣日期
     */
    private Date testDate;

    /**
     * 一次审核
     */
    private Long checkerId;

    /**
     * 一次审核人
     */
    private String checkerName;

    /**
     * 审核时间
     */
    private Date checkDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    private String isDelete;
}
