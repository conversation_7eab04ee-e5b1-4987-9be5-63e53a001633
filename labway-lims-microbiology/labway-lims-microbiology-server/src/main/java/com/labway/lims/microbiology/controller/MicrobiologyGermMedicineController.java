package com.labway.lims.microbiology.controller;

import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.microbiology.MicroTestMethodEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.GermGenusExamMethodDto;
import com.labway.lims.base.api.dto.MedicineDto;
import com.labway.lims.base.api.dto.MedicineGermRelationDto;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.base.api.service.MedicineGermRelationService;
import com.labway.lims.base.api.service.MedicineService;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineUpdateDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleGermService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.microbiology.mapstruct.MicrobiologyGermMedicineConverter;
import com.labway.lims.microbiology.vo.MicroGermMedicineVo;
import com.labway.lims.microbiology.vo.SaveMicrobiologyGermMedicineRequestVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 16:14
 */
@RestController
@RequestMapping("/germ-medicine")
public class MicrobiologyGermMedicineController extends BaseController {
    @Resource
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;
    @Resource
    private MicrobiologySampleService microbiologySampleService;

    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private MedicineService medicineService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private GermService germService;
    @Resource
    private MicrobiologySampleGermService microbiologySampleGermService;
    @Resource
    private MicrobiologyGermMedicineConverter microbiologyGermMedicineConverter;
    @DubboReference
    private MedicineGermRelationService medicineGermRelationService;

    @PostMapping("/germMedicines")
    public Object germMedicines(@RequestParam("microbiologySampleGermId") Long microbiologySampleGermId) {
        if (Objects.isNull(microbiologySampleGermId)) {
            return Collections.emptyList();
        }
        MicrobiologySampleGermDto sampleGermDto =
                microbiologySampleGermService.selectByMicrobiologySampleGermId(microbiologySampleGermId);
        if (Objects.isNull(sampleGermDto)) {
            throw new LimsException("微生物细菌不存在");
        }
        // 菌属下所有药物
        Map<Long, List<MedicineGermRelationDto>> groupingByMedicineId =
                medicineGermRelationService.selectByGermGenusId(sampleGermDto.getGermGenusId()).stream()
                        .collect(Collectors.groupingBy(MedicineGermRelationDto::getMedicineId));

        final List<MicrobiologyGermMedicineDto> medicineDtos = microbiologyGermMedicineService
                .selectByMicrobiologySampleGermId(microbiologySampleGermId).stream()
                .sorted(Comparator.comparing(MicrobiologyGermMedicineDto::getMedicineCode)).collect(Collectors.toList());
        List<MicroGermMedicineVo> targetList = Lists.newArrayListWithCapacity(medicineDtos.size());
        medicineDtos.forEach(item -> {
            MicroGermMedicineVo vo = new MicroGermMedicineVo();
            BeanUtils.copyProperties(item, vo);
            List<MedicineGermRelationDto> relationDtos =
                    ObjectUtils.defaultIfNull(groupingByMedicineId.get(item.getMedicineId()), Collections.emptyList());

            Map<String, String> examMethodMap =
                    relationDtos.stream().collect(Collectors.toMap(MedicineGermRelationDto::getExamMethodCode,
                            MedicineGermRelationDto::getExamMethodName, (key1, key2) -> key1));
            List<GermGenusExamMethodDto> examMethod = examMethodMap.entrySet().stream().map(obj -> {
                GermGenusExamMethodDto dto = new GermGenusExamMethodDto();
                dto.setExamMethodCode(obj.getKey());
                dto.setExamMethodName(obj.getValue());
                return dto;
            }).collect(Collectors.toList());
            if (examMethod.stream()
                    .noneMatch(e -> Objects.equals(e.getExamMethodCode(), MicroTestMethodEnum.INSTRUMENT.getCode()))) {
                GermGenusExamMethodDto dto = new GermGenusExamMethodDto();
                dto.setExamMethodCode(MicroTestMethodEnum.INSTRUMENT.getCode());
                dto.setExamMethodName(MicroTestMethodEnum.INSTRUMENT.getDesc());
                examMethod.add(dto);
            }
            vo.setExamMethod(examMethod);
            // 同一个药物多个检验方法去最小一个
            Optional<Integer> min = relationDtos.stream().filter(e -> StringUtils.isNotBlank(e.getReportSort()))
                .map(e -> Integer.parseInt(e.getReportSort())).min(Comparator.comparing(Integer::intValue));
            vo.setReportSort(min.orElse(null));
            targetList.add(vo);
        });

        Comparator<MicroGermMedicineVo> comparator = (o1, o2) -> {
            Integer s1 = o1.getReportSort();
            Integer s2 = o2.getReportSort();
            if (Objects.nonNull(s1) && Objects.nonNull(s2)) {
                return s1 - s2;
            } else if (Objects.nonNull(s1)) {
                return -1;
            } else if (Objects.nonNull(s2)) {
                return 1;
            } else {
                return 0;
            }
        };
        targetList.sort(comparator.thenComparing(MicroGermMedicineVo::getMedicineCode));

        return targetList;
    }

    /**
     * 修改获取新增 微生物细菌下 药物
     */
    @PostMapping("/save-medicine")
    public Object saveMedicine(@RequestBody SaveMicrobiologyGermMedicineRequestVo vo) {
        if (Objects.isNull(vo.getMicrobiologySampleGermId())) {
            throw new IllegalArgumentException("样本细菌不能为空");
        }
        if (Objects.isNull(vo.getMedicineId())) {
            throw new IllegalArgumentException("药物不能为空");
        }

        MicrobiologyGermMedicineDto germMedicineDto = null;
        // 传入了 微生物细菌药物 id
        if (Objects.nonNull(vo.getMicrobiologyGermMedicineId())) {
            germMedicineDto =
                    microbiologyGermMedicineService.selectByMicrobiologyGermMedicineId(vo.getMicrobiologyGermMedicineId());
            if (Objects.isNull(germMedicineDto)) {
                throw new LimsException("传入细菌药物不存在");
            }
        }

        // 对应微生物细菌
        MicrobiologySampleGermDto sampleGermDto =
                microbiologySampleGermService.selectByMicrobiologySampleGermId(vo.getMicrobiologySampleGermId());
        if (Objects.isNull(sampleGermDto)) {
            throw new IllegalArgumentException("样本细菌不存在");
        }

        // 对应微生物样本
        MicrobiologySampleDto microbiologySampleDto =
                microbiologySampleService.selectByMicrobiologySampleId(sampleGermDto.getMicrobiologySampleId());
        if (Objects.isNull(microbiologySampleDto)) {
            throw new IllegalArgumentException("对应微生物样本不存在");
        }

        // 对应微生物细菌 菌属 此 药物 信息
        List<MedicineGermRelationDto> relationDtos = medicineGermRelationService
                .selectByGermGenusIdAndMedicineId(sampleGermDto.getGermGenusId(), vo.getMedicineId());
        if (CollectionUtils.isEmpty(relationDtos)) {
            throw new LimsException("所选药物不在细菌对应菌属药物下");
        }

        // 修改的药物
        MedicineDto medicineDto = medicineService.selectByMedicineId(vo.getMedicineId());
        if (Objects.isNull(medicineDto)) {
            throw new IllegalArgumentException("药物不存在");
        }

        // 微生物细菌下 的细菌药物
        List<MicrobiologyGermMedicineDto> medicineDtos = microbiologyGermMedicineService
                .selectByMicrobiologySampleGermId(sampleGermDto.getMicrobiologySampleGermId());

        // 传入了微生物细菌药物 检验药物是否重复时过滤当前的
        if (Objects.nonNull(germMedicineDto)) {
            Long microbiologyGermMedicineId = germMedicineDto.getMicrobiologyGermMedicineId();
            medicineDtos = medicineDtos.stream()
                    .filter(obj -> !Objects.equals(obj.getMicrobiologyGermMedicineId(), microbiologyGermMedicineId))
                    .collect(Collectors.toList());
        }
        // 如果药物已经添加
        if (medicineDtos.stream().anyMatch(e -> Objects.equals(e.getMedicineId(), vo.getMedicineId()))) {
            throw new IllegalStateException("当前药物在细菌下已添加");
        }

        long microbiologyGermMedicineId;
        if (Objects.isNull(germMedicineDto)) {
            microbiologyGermMedicineId =
                    microbiologyGermMedicineService.saveMedicineWhenAdd(microbiologySampleDto, sampleGermDto, medicineDto);
        } else {
            microbiologyGermMedicineId = microbiologyGermMedicineService.saveMedicineWhenUpdate(microbiologySampleDto,
                    sampleGermDto, medicineDto, relationDtos, germMedicineDto);
        }
        Map<String, String> examMethodMap =
                relationDtos.stream().collect(Collectors.toMap(MedicineGermRelationDto::getExamMethodCode,
                        MedicineGermRelationDto::getExamMethodName, (key1, key2) -> key1));
        List<GermGenusExamMethodDto> examMethod = examMethodMap.entrySet().stream().map(obj -> {
            GermGenusExamMethodDto dto = new GermGenusExamMethodDto();
            dto.setExamMethodCode(obj.getKey());
            dto.setExamMethodName(obj.getValue());
            return dto;
        }).collect(Collectors.toList());
        return Map.of("microbiologyGermMedicineId", microbiologyGermMedicineId, "examMethod", examMethod);
    }

    @PostMapping("/updateResult")
    public Object updateResult(@RequestBody MicrobiologyGermMedicineUpdateDto vo) {

        if (Objects.isNull(vo.getMicrobiologyGermMedicineId())) {
            throw new IllegalArgumentException("细菌药物不能为空");
        }
        if (Objects.isNull(vo.getUpdateType()) || !Set.of(1, 2, 3, 4, 5).contains(vo.getUpdateType())) {
            throw new LimsException("修改内容类型不确定");
        }
        if (Objects.equals(vo.getUpdateType(), 1) && StringUtils.isBlank(vo.getMedicineMethod())) {
            throw new LimsException("修改药物方法不存在");
        }
        if (Objects.equals(vo.getUpdateType(), 2)) {
            if (Objects.isNull(vo.getFormula())) {
                vo.setFormula(StringUtils.EMPTY);
            }
        }
        if (Objects.equals(vo.getUpdateType(), 3) && StringUtils.isBlank(vo.getResult())) {
            if (Objects.isNull(vo.getResult())) {
                vo.setFormula(StringUtils.EMPTY);
            }
        }
        if (Objects.equals(vo.getUpdateType(), 4) && StringUtils.isBlank(vo.getSusceptibility())) {
            if (Objects.isNull(vo.getSusceptibility())) {
                vo.setFormula(StringUtils.EMPTY);
            }
        }
        if (Objects.equals(vo.getUpdateType(), 5) && StringUtils.length(vo.getGroup()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("分组不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }

        // 对应 微生物细菌药物
        MicrobiologyGermMedicineDto germMedicineDto =
                microbiologyGermMedicineService.selectByMicrobiologyGermMedicineId(vo.getMicrobiologyGermMedicineId());
        if (Objects.isNull(germMedicineDto)) {
            throw new LimsException("微生物细菌药物不存在");
        }

        MicrobiologySampleDto sample =
                microbiologySampleService.selectByMicrobiologySampleId(germMedicineDto.getMicrobiologySampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("当前微生物样本不存在");
        }

        ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("对应申请单样本不存在");
        }
        if (Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new IllegalStateException("已审核样本无法修改结果");
        }
        MicrobiologySampleGermDto sampleGermDto = microbiologySampleGermService
                .selectByMicrobiologySampleGermId(germMedicineDto.getMicrobiologySampleGermId());
        if (Objects.isNull(sampleGermDto)) {
            throw new LimsException("对应微生物细菌不存在");
        }
        // 此菌属 下 此药物 相关 菌属药物
        List<MedicineGermRelationDto> relationDtos = medicineGermRelationService
                .selectByGermGenusIdAndMedicineId(sampleGermDto.getGermGenusId(), germMedicineDto.getMedicineId());

        return microbiologyGermMedicineService.updateMedicineResult(sample, sampleGermDto, germMedicineDto,
                relationDtos, vo);
    }

    @PostMapping("/delete")
    public Object delete(@RequestBody List<Long> microbiologyGermMedicineIds) {
        if (CollectionUtils.isEmpty(microbiologyGermMedicineIds)) {
            return Collections.emptyMap();
        }
        final List<MicrobiologyGermMedicineDto> microbiologyGermMedicines =
                microbiologyGermMedicineService.selectByMicrobiologyGermMedicineIds(microbiologyGermMedicineIds);
        microbiologyGermMedicineService.deleteByIds(microbiologyGermMedicineIds);

        if (CollectionUtils.isNotEmpty(microbiologyGermMedicines)) {
            final MicrobiologySampleDto sample = microbiologySampleService
                    .selectByMicrobiologySampleId(microbiologyGermMedicines.iterator().next().getMicrobiologySampleId());
            final LinkedList<SampleFlowDto> flows = new LinkedList<>();
            if (Objects.nonNull(sample)) {
                final SampleFlowDto flow = new SampleFlowDto();
                flow.setSampleFlowId(snowflakeService.genId());
                flow.setApplyId(sample.getApplyId());
                flow.setApplySampleId(sample.getApplySampleId());
                flow.setBarcode(sample.getBarcode());
                flow.setOperateCode(BarcodeFlowEnum.DELETE_MEDICINE.name());
                flow.setOperateName(BarcodeFlowEnum.DELETE_MEDICINE.getDesc());
                final LoginUserHandler.User user = LoginUserHandler.get();
                flow.setOperator(user.getNickname());
                flow.setOperatorId(user.getUserId());
                flow.setContent("删除药物");
                flow.setCreateDate(new Date());
                flow.setUpdateDate(new Date());
                flow.setUpdaterId(user.getUserId());
                flow.setUpdaterName(user.getNickname());
                flow.setOrgId(user.getOrgId());
                flow.setOrgName(user.getOrgName());
                flow.setIsDelete(YesOrNoEnum.NO.getCode());
                flows.add(flow);
            }
            sampleFlowService.addSampleFlows(flows);
        }

        return Collections.emptyMap();
    }

}
