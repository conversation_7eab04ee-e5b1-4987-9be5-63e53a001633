package com.labway.lims.microbiology.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 检查耐药 响应
 *
 * <AUTHOR>
 * @since 2023/9/4 14:40
 */
@Getter
@Setter
public class CheckResistantResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long microbiologySampleId;

    /**
     * 样本号
     */
    private String sampleNo;
    /**
     * 条码
     */
    private String barcode;

    /**
     * 警告消息
     */
    private List<String> warningMsg;

}
