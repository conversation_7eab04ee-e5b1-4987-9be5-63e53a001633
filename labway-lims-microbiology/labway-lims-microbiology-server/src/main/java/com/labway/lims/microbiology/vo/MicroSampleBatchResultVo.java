package com.labway.lims.microbiology.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 微生物样本结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Setter
@Getter
public class MicroSampleBatchResultVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 样本IDs
     */
    private List<Long> microbiologySampleIds;

    /**
     * 结果列表罗列
     */
    private List<MicroSampleBatchResultItemVo> resultList;
}
