package com.labway.lims.microbiology.vo;

import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/13 16:24
 */
@Getter
@Setter
public class UpdateMicroGermMedicineVo extends MicrobiologyGermMedicineVo {
    /**
     * 日期
     */
    private Date resultDate;

    /**
     * 通道号
     */
    private String instrumentChannel;

    /**
     * 来源
     */
    private SaveResultSourceEnum source;
}
