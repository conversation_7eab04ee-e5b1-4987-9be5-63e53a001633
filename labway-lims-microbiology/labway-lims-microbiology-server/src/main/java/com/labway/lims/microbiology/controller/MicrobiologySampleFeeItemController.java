package com.labway.lims.microbiology.controller;

import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySampleItemSourceEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleFeeItemInfoDto;
import com.labway.lims.microbiology.api.dto.SaveFeeTestItemDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.microbiology.vo.SaveTestItemRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 微生物费用明细项目 API
 * 
 * <AUTHOR>
 * @since 2023/8/28 10:41
 */
@Slf4j
@RestController
@RequestMapping("/sample-fee-item")
public class MicrobiologySampleFeeItemController extends BaseController {

    @Resource
    private MicrobiologySampleService microbiologySampleService;

    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private TestItemService testItemService;

    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ItemPriceBasePackageService itemPriceBasePackageService;
    @DubboReference
    private SnowflakeService snowflakeService;

    /**
     * 查询 费用明细项目
     * 
     */
    @PostMapping("/select-all")
    public Map<String, Object> selectAll(@RequestParam("microbiologySampleId") Long microbiologySampleId) {
        if (Objects.isNull(microbiologySampleId)) {
            return Collections.emptyMap();
        }
        MicrobiologySampleDto sampleDto = microbiologySampleService.selectByMicrobiologySampleId(microbiologySampleId);
        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("样本不存在");
        }
        // 对应检验项目
        List<ApplySampleItemDto> sampleItemDtoList =
            applySampleItemService.selectByApplySampleId(sampleDto.getApplySampleId());

        // 根据创建时间升序
        sampleItemDtoList.sort(Comparator.comparing(ApplySampleItemDto::getCreateDate));
        List<MicrobiologySampleFeeItemInfoDto> targetList = Lists.newArrayList();

        sampleItemDtoList.forEach(item -> {
            MicrobiologySampleFeeItemInfoDto temp = new MicrobiologySampleFeeItemInfoDto();
            temp.setMicrobiologySampleId(microbiologySampleId);
            temp.setTestItemId(item.getTestItemId());
            temp.setTestItemCode(item.getTestItemCode());
            temp.setTestItemName(item.getTestItemName());
            temp.setFeePrice(item.getFeePrice());
            temp.setFeeCount(item.getCount());
            temp.setCreatorId(item.getCreatorId());
            temp.setCreatorName(item.getCreatorName());
            temp.setCreateDate(item.getCreateDate());
            temp.setItemSource(item.getItemSource());
            targetList.add(temp);
        });

        BigDecimal feeAmountSum = BigDecimal.ZERO;
        for (MicrobiologySampleFeeItemInfoDto item : targetList) {
            BigDecimal feeAmount = item.getFeePrice().multiply(BigDecimal.valueOf(item.getFeeCount()));
            item.setFeeAmount(feeAmount);
            feeAmountSum = feeAmountSum.add(feeAmount);
        }

        return Map.of("feeAmountSum", feeAmountSum, "feeItemList", targetList);
    }

    /**
     * 查询 费用明细项目金额汇总
     *
     */
    @PostMapping("/select-fee-amount-sum")
    public Object selectFeeAmountSum(@RequestParam("microbiologySampleId") Long microbiologySampleId) {
        Map<String, Object> result = this.selectAll(microbiologySampleId);
        return result.get("feeAmountSum");
    }

    /**
     * 可增加 费用 项目信息列表
     *
     */
    @PostMapping("/add-test-item-list")
    public Object addTestItemList(@RequestParam("microbiologySampleId") Long microbiologySampleId) {
        if (Objects.isNull(microbiologySampleId)) {
            return Collections.emptyList();
        }
        MicrobiologySampleDto sampleDto = microbiologySampleService.selectByMicrobiologySampleId(microbiologySampleId);
        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("样本不存在");
        }

        // 已经添加的费用项目
        List<ApplySampleItemDto> sampleItemDtoList = applySampleItemService
            .selectByApplySampleId(sampleDto.getApplySampleId()).stream().filter(obj -> Objects
                .equals(obj.getItemSource(), ApplySampleItemSourceEnum.MICROBIOLOGY_SAMPLE_FEE_ITEM.getCode()))
            .collect(Collectors.toList());

        // 已添加的项目
        Set<Long> testItemIds =
            sampleItemDtoList.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());

        LoginUserHandler.User user = LoginUserHandler.get();
        // 返回 过滤掉已添加的项目
        List<TestItemDto> waitItemList = testItemService.selectByGroupId(user.getGroupId()).stream()
            .filter(obj -> CollectionUtils.isEmpty(testItemIds) || !testItemIds.contains(obj.getTestItemId()))
            .collect(Collectors.toList());
        // 待选择 已选择
        return Map.of("waitItemList", waitItemList, "selectedItemList", sampleItemDtoList);
    }

    /**
     * 增加&修改&删除 费用 项目信息
     *
     */
    @PostMapping("/save")
    public Object saveTestItem(@RequestBody SaveTestItemRequestVo vo) {
        Long microbiologySampleId = vo.getMicrobiologySampleId();
        List<SaveFeeTestItemDto> itemList = ObjectUtils.defaultIfNull(vo.getItemList(), Collections.emptyList());
        if (Objects.isNull(microbiologySampleId)) {
            throw new IllegalArgumentException("样本不存在");
        }
        if (itemList.stream()
            .anyMatch(obj -> Objects.isNull(obj.getTestItemId()) || Objects.isNull(obj.getFeeCount()))) {
            throw new IllegalArgumentException("检验项目与收费次数不可为空");
        }

        // key 检验项目id value: 收费次数
        Map<Long, Integer> feeCountByTestItemId = itemList.stream().collect(
            Collectors.toMap(SaveFeeTestItemDto::getTestItemId, SaveFeeTestItemDto::getFeeCount, (key1, key2) -> key1));

        MicrobiologySampleDto sampleDto = microbiologySampleService.selectByMicrobiologySampleId(microbiologySampleId);
        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("样本不存在");
        }

        ApplyDto applyDto = applyService.selectByApplyId(sampleDto.getApplyId());
        if (Objects.isNull(applyDto)) {
            throw new IllegalArgumentException("样本对应申请单不存在");
        }
        Date now = new Date();
        // key: 检验项目id value: 实际收费价格
        Map<Long, BigDecimal> actualFeePriceByTestItemId = itemPriceBasePackageService.selectActualFeePrice(
            applyDto.getHspOrgId(), applyDto.getApplyTypeCode(), now, feeCountByTestItemId.keySet());

        // 对应检验项目 key 检验项目id value 检验项目信息
        Map<Long, TestItemDto> testItemDtoById = testItemService.selectByTestItemIds(feeCountByTestItemId.keySet())
            .stream().collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));

        // 已经添加的费用项目
        Map<Long, ApplySampleItemDto> feeItemDtoMap =
            applySampleItemService.selectByApplySampleId(sampleDto.getApplySampleId()).stream()
                .filter(obj -> Objects.equals(obj.getItemSource(),
                    ApplySampleItemSourceEnum.MICROBIOLOGY_SAMPLE_FEE_ITEM.getCode()))
                .collect(Collectors.toMap(ApplySampleItemDto::getTestItemId, Function.identity()));

        // 需要删除的 费用项目ids
        Set<Long> needDeleteSampleFeeItemIds =
            feeItemDtoMap.entrySet().stream().filter(obj -> !feeCountByTestItemId.containsKey(obj.getKey()))
                .map(obj -> obj.getValue().getApplySampleItemId()).collect(Collectors.toSet());

        // 需要修改收费次数的费用项目
        List<ApplySampleItemDto> needUpdateFeeItems = feeItemDtoMap.entrySet().stream()
            // 检验项目存在的
            .filter(obj -> feeCountByTestItemId.containsKey(obj.getKey()))
            // 判断 金额、数量是否变化
            .filter(obj -> {
                // 检验项目 此刻信息
                TestItemDto testItemDto = testItemDtoById.get(obj.getKey());
                if (!Objects.equals(testItemDto.getFeePrice().compareTo(obj.getValue().getFeePrice()), 0)) {
                    // 金额出现变化 需要更新
                    return true;
                }
                // 数量出现变化 需要更新
                return !Objects.equals(obj.getValue().getCount(), feeCountByTestItemId.get(obj.getKey()));
            }).map(obj -> {
                TestItemDto testItemDto = testItemDtoById.get(obj.getKey());
                ApplySampleItemDto value = obj.getValue();
                ApplySampleItemDto update = new ApplySampleItemDto();
                update.setApplySampleItemId(value.getApplySampleItemId());
                update.setFeePrice(testItemDto.getFeePrice());
                update.setCount(feeCountByTestItemId.get(obj.getKey()));
                return update;
            }).collect(Collectors.toList());

        // 需要新增的 收费次数的费用项目
        List<Long> needAddTestItemIds = feeCountByTestItemId.keySet().stream()
            .filter(integer -> !feeItemDtoMap.containsKey(integer)).collect(Collectors.toList());
        LoginUserHandler.User user = LoginUserHandler.get();
        List<ApplySampleItemDto> needAddFeeTestItems = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(needAddTestItemIds)) {
            LinkedList<Long> ids = snowflakeService.genIds(needAddTestItemIds.size());
            for (Long needAddTestItemId : needAddTestItemIds) {
                TestItemDto testItem = testItemDtoById.get(needAddTestItemId);
                if (Objects.isNull(testItem)) {
                    continue;
                }
                BigDecimal actualFeePrice =
                    actualFeePriceByTestItemId.getOrDefault(testItem.getTestItemId(), testItem.getFeePrice());

                Integer feeCount = feeCountByTestItemId.get(needAddTestItemId);

                ApplySampleItemDto applySampleItem = new ApplySampleItemDto();
                applySampleItem.setApplySampleItemId(ids.pop());
                applySampleItem.setApplySampleId(sampleDto.getApplySampleId());
                applySampleItem.setApplyId(sampleDto.getApplyId());
                applySampleItem.setTestItemId(testItem.getTestItemId());
                applySampleItem.setTestItemCode(testItem.getTestItemCode());
                applySampleItem.setTestItemName(testItem.getTestItemName());
                applySampleItem.setItemType(testItem.getItemType());
                applySampleItem.setOutTestItemId(NumberUtils.LONG_ZERO);
                applySampleItem.setOutTestItemCode(StringUtils.EMPTY);
                applySampleItem.setOutTestItemName(StringUtils.EMPTY);
                applySampleItem.setSampleTypeName(testItem.getSampleTypeName());
                applySampleItem.setSampleTypeCode(testItem.getSampleTypeCode());
                applySampleItem.setTubeName(testItem.getTubeName());
                applySampleItem.setTubeCode(testItem.getTubeCode());
                applySampleItem.setGroupId(testItem.getGroupId());
                applySampleItem.setGroupName(testItem.getGroupName());
                applySampleItem.setUrgent(YesOrNoEnum.NO.getCode());
                applySampleItem.setRemark(StringUtils.EMPTY);
                applySampleItem.setCreateDate(now);
                applySampleItem.setCount(feeCount);
                applySampleItem.setUpdateDate(now);
                applySampleItem.setCreatorId(user.getUserId());
                applySampleItem.setCreatorName(user.getNickname());
                applySampleItem.setUpdaterId(user.getUserId());
                applySampleItem.setUpdaterName(user.getNickname());
                applySampleItem.setIsDelete(YesOrNoEnum.NO.getCode());
                applySampleItem.setSplitCode(StringUtils.EMPTY);
                applySampleItem.setIsOutsourcing(ObjectUtils.defaultIfNull(testItem.getEnableExport(), 0));
                applySampleItem
                    .setExportOrgId(ObjectUtils.defaultIfNull(testItem.getExportOrgId(), NumberUtils.LONG_ZERO));
                applySampleItem.setExportOrgName(StringUtils.defaultString(testItem.getExportOrgName()));
                applySampleItem.setIsFree(YesOrNoEnum.NO.getCode());
                applySampleItem.setFeePrice(testItem.getFeePrice());
                applySampleItem.setActualFeePrice(actualFeePrice);
                applySampleItem.setStopStatus(StopTestStatus.NO_STOP_TEST.getCode());
                applySampleItem.setStopReasonCode(StringUtils.EMPTY);
                applySampleItem.setStopReasonName(StringUtils.EMPTY);
                applySampleItem.setItemSource(ApplySampleItemSourceEnum.MICROBIOLOGY_SAMPLE_FEE_ITEM.getCode());
                needAddFeeTestItems.add(applySampleItem);
            }
        }

        applySampleItemService.saveMicrobiologySampleFeeItem(needDeleteSampleFeeItemIds, needUpdateFeeItems,
            needAddFeeTestItems);

        return Collections.emptyMap();
    }

}
