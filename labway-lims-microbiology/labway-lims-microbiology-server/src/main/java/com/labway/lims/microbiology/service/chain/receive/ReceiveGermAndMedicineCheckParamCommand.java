package com.labway.lims.microbiology.service.chain.receive;

import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/7/17 14:57
 */
@Component
public class ReceiveGermAndMedicineCheckParamCommand implements Filter, Command {

    @Override
    public boolean execute(Context c) throws Exception {

        final ReceiveGermAndMedicineContext context = ReceiveGermAndMedicineContext.from(c);
        if (CollectionUtils.isEmpty(context.getGermMap().keySet())) {
            throw new IllegalStateException("当前细菌不存在保存结果失败");
        }
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }

}
