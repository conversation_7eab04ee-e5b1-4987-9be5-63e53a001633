package com.labway.lims.microbiology.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 保存 微生物细菌药物
 *
 * <AUTHOR>
 * @since 2023/5/26 21:06
 */
@Getter
@Setter
public class SaveMicrobiologyGermMedicineRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    private Long microbiologyGermMedicineId;

    /**
     * 样本细菌ID
     */
    private Long microbiologySampleGermId;

    /**
     * 药物
     */
    private Long medicineId;

}
