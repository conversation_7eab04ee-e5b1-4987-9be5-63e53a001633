package com.labway.lims.microbiology.service.chain.receive;

import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.base.api.dto.InstrumentGermDto;
import com.labway.lims.base.api.dto.InstrumentMedicineResultDto;
import com.labway.lims.base.api.dto.MedicineGermRelationDto;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/17 13:45
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class ReceiveGermAndMedicineContext extends StopWatchContext {

    /**
     * 微生物样本
     */
    public static final String MICRO_SAMPLE = "MICRO_SAMPLE_" + IdUtil.objectId();

    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    /**
     * 微生物样本细菌
     */
    public static final String MICRO_SAMPLE_GERMS = "MICRO_SAMPLE_GERMS_" + IdUtil.objectId();

    /**
     * 微生物样本细菌药物
     */
    public static final String MICRO_SAMPLE_GERM_MEDICINES = "MICRO_SAMPLE_GERM_MEDICINES_" + IdUtil.objectId();

    /**
     * 仪器微生物样本细菌
     */
    public static final String INSTRUMENT_MICRO_SAMPLE_GERMS = "INSTRUMENT_MICRO_SAMPLE_GERMS" + IdUtil.objectId();

    /**
     * 仪器微生物样本细菌药物
     */
    public static final String INSTRUMENT_MICRO_SAMPLE_GERM_MEDICINES = "INSTRUMENT_MICRO_SAMPLE_GERM_MEDICINES" + IdUtil.objectId();

    /**
     * 细菌菌属 对应药物 MedicineGermRelation
     */
    public static final String MEDICINE_GERM_RELATION = "MEDICINE_GERM_RELATION_" + IdUtil.objectId();

    /**
     * 需要添加的细菌
     */
    public static final String ADD_GERMS = "ADD_GERMS_" + IdUtil.objectId();

    /**
     * 修改的细菌
     */
    public static final String UPDATE_GERMS = "UPDATE_GERMS" + IdUtil.objectId();

    /**
     * 需要添加的药物
     */
    public static final String ADD_GERM_MEDICINES = "ADD_GERM_MEDICINES_" + IdUtil.objectId();

    /**
     * 需要修改的药物
     */
    public static final String UPDATE_GERM_MEDICINES = "UPDATE_GERM_MEDICINES_" + IdUtil.objectId();

    /**
     * 删除的药物
     */
    public static final String DELETE_GERM_MEDICINES = "DELETE_GERM_MEDICINES_" + IdUtil.objectId();

    /**
     * 原始药物信息
     */
    public static final String ORI_GERM_MEDICINES = "ORI_GERM_MEDICINES_" + IdUtil.objectId();

    /**
     * germRemark Info
     */
    public static final String GERM_REMARK_INFO = "GERM_REMARK_INFO_" + IdUtil.objectId();

    /**
     * 微生物样本ID
     */
    private Long microbiologySampleId;

    /**
     * 接收的细菌和药物
     */
    private Map<InstrumentGermDto, List<InstrumentMedicineResultDto>> germMap;

    /**
     * 检验时间 yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date date;
    /**
     * 检测的仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器
     */
    private Long instrumentId;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组code
     */
    private String groupCode;

    /**
     * orgId
     */
    private Long orgId;

    public MicrobiologySampleDto getMicroSample() {
        return (MicrobiologySampleDto) get(MICRO_SAMPLE);
    }

    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }

    public List<MicrobiologySampleGermDto> getMicroSampleGerms() {
        return (List<MicrobiologySampleGermDto>) get(MICRO_SAMPLE_GERMS);
    }

    public List<MicrobiologyGermMedicineDto> getMicroSampleGermMedicines() {
        return (List<MicrobiologyGermMedicineDto>) get(MICRO_SAMPLE_GERM_MEDICINES);
    }

    public Map<String, String> getGermRemarkMap() {
        return (Map<String, String>) get(GERM_REMARK_INFO);
    }

    public List<MicrobiologySampleGermDto> getInstrumentMicroSampleGerms() {
        return (List<MicrobiologySampleGermDto>) get(INSTRUMENT_MICRO_SAMPLE_GERMS);
    }

    public List<MicrobiologyGermMedicineDto> getInstrumentMicroSampleGermMedicines() {
        return (List<MicrobiologyGermMedicineDto>) get(INSTRUMENT_MICRO_SAMPLE_GERM_MEDICINES);
    }

    public List<MedicineGermRelationDto> getMedicineGermRelation() {
        return (List<MedicineGermRelationDto>) get(MEDICINE_GERM_RELATION);
    }

    public List<MicrobiologySampleGermDto> getAddGerms() {
        return (List<MicrobiologySampleGermDto>) get(ADD_GERMS);
    }

    public List<MicrobiologySampleGermDto> getUpdateGerms() {
        return (List<MicrobiologySampleGermDto>) get(UPDATE_GERMS);
    }

    public List<MicrobiologyGermMedicineDto> getAddMedicines() {
        return (List<MicrobiologyGermMedicineDto>) get(ADD_GERM_MEDICINES);
    }

    public List<MicrobiologyGermMedicineDto> getUpdateMedicines() {
        return (List<MicrobiologyGermMedicineDto>) get(UPDATE_GERM_MEDICINES);
    }

    public List<MicrobiologyGermMedicineDto> getDeleteMedicines() {
        return (List<MicrobiologyGermMedicineDto>) get(DELETE_GERM_MEDICINES);
    }

    public List<MicrobiologyGermMedicineDto> getOriMedicines() {
        return (List<MicrobiologyGermMedicineDto>) get(ORI_GERM_MEDICINES);
    }

    public static ReceiveGermAndMedicineContext from(Context c) {
        return (ReceiveGermAndMedicineContext) c;
    }

    @Override
    protected String getWatchName() {
        return "微生物仪器结果接收";
    }
}
