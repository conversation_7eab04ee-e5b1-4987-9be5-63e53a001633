package com.labway.lims.microbiology.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/21 14:30
 */
@Data
public class BarcodeFlowVo {

    /**
     * ID
     */
    private Long sampleFlowId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 操作类型
     */
    private String operateCode;

    /**
     * 操作类型
     */
    private String operateName;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;

    /**
     * 检验人ID
     */
    private Long orgId;

    /**
     * 检验人
     */
    private String orgName;

}
