package com.labway.lims.microbiology.service.chain.receive;

import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/17 17:07
 */
@Component
public class ReceiveGermAndMedicineUpdateMedicineCommand implements Filter, Command {
    @Resource
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {
        final ReceiveGermAndMedicineContext context = ReceiveGermAndMedicineContext.from(c);
        final List<MicrobiologyGermMedicineDto> updateMedicines = context.getUpdateMedicines();

        if (CollectionUtils.isNotEmpty(updateMedicines)) {
            microbiologyGermMedicineService.updateByMicrobiologyGermMedicineIds(updateMedicines);
        }

        return CONTINUE_PROCESSING;
    }
}
