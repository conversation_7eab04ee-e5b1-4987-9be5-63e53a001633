package com.labway.lims.microbiology.service.chain.receive;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/7/17 13:46
 *
 * <p> 微生物细菌药物结果接收 </p>
 */
@Slf4j
@Component
public class ReceiveGermAndMedicineChain extends ChainBase implements InitializingBean {

    @Resource
    private ReceiveGermAndMedicineAddGermCommand receiveGermAndMedicineAddGermCommand;
    @Resource
    private ReceiveGermAndMedicineAddMedicineCommand receiveGermAndMedicineAddMedicineCommand;
    @Resource
    private ReceiveGermAndMedicineCheckParamCommand receiveGermAndMedicineCheckParamCommand;
    @Resource
    private ReceiveGermAndMedicineFillInfoCommand receiveGermAndMedicineFillInfoCommand;
    @Resource
    private ReceiveGermAndMedicineFilterAddAndUpdateCommand receiveGermAndMedicineFilterAddAndUpdateCommand;
    @Resource
    private ReceiveGermAndMedicineUpdateMedicineCommand receiveGermAndMedicineUpdateMedicineCommand;
    @Resource
    private ReceiveGermAndMedicineSampleFlowCommand receiveGermAndMedicineSampleFlowCommand;
    @Resource
    private ReceiveGermAndMedicineUpdateGermCommand receiveGermAndMedicineUpdateGermCommand;
    @Resource
    private ReceiveGermAndMedicineUpdateSampleCommand receiveGermAndMedicineUpdateSampleCommand;
    @Resource
    private ReceiveGermAndMedicineDeleteMedicineCommand receiveGermAndMedicineDeleteMedicineCommand;
    @Resource
    private ReceiveGermAndMedicineCheckCanSaveCommand receiveGermAndMedicineCheckCanSaveCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        //检验参数
        addCommand(receiveGermAndMedicineCheckParamCommand);

        //填充信息
        addCommand(receiveGermAndMedicineFillInfoCommand);

        //是否可以保存
        addCommand(receiveGermAndMedicineCheckCanSaveCommand);

        //筛选新增和修改的细菌和药物
        addCommand(receiveGermAndMedicineFilterAddAndUpdateCommand);

        //新增细菌
        addCommand(receiveGermAndMedicineAddGermCommand);

        //修改细菌
        addCommand(receiveGermAndMedicineUpdateGermCommand);

        //删除的药物
        addCommand(receiveGermAndMedicineDeleteMedicineCommand);

        //新增药物
        addCommand(receiveGermAndMedicineAddMedicineCommand);

        //修改药物
//        addCommand(receiveGermAndMedicineUpdateMedicineCommand);

        //修改样本检测时间
        addCommand(receiveGermAndMedicineUpdateSampleCommand);

        //条码环节
        addCommand(receiveGermAndMedicineSampleFlowCommand);

        addCommand(e -> PROCESSING_COMPLETE);
    }
}
