package com.labway.lims.microbiology.service.chain.audit;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 微生物 审核样本 发送审核消息
 * <AUTHOR>
 * @since 2023/6/16 13:28
 */
@Slf4j
@Component
public class SamplesAuditRabbitMqCommand implements Command {

    private static final String ROUTING_KEY = "sample_change_key";
    @DubboReference
    private RabbitMQService rabbitMQService;

    @Override
    public boolean execute(Context c) throws Exception {
        final AuditMicroSampleContext context = AuditMicroSampleContext.from(c);
        final Map<Long, ApplyDto> applyMap = context.getApplyDtoList()
                .stream().collect(Collectors.toMap(ApplyDto::getApplyId
                        , v -> v, (a, b) -> a));
        for (MicrobiologySampleDto sample : context.getSamples()) {
            final ApplyDto apply = applyMap.get(sample.getApplyId());
            if (Objects.isNull(apply)) {
                continue;
            }
            final ApplySampleEventDto event = new ApplySampleEventDto();
            event.setOrgId(LoginUserHandler.get().getOrgId());

            event.setHspOrgId(apply.getHspOrgId());
            event.setHspOrgCode(apply.getHspOrgCode());
            event.setHspOrgName(apply.getHspOrgName());
            event.setApplyId(sample.getApplyId());
            event.setApplySampleId(sample.getApplySampleId());
            event.setBarcode(sample.getBarcode());
            event.setExtras(Map.of("sampleId", String.valueOf(sample.getMicrobiologySampleId()), "sampleNo", String.valueOf(sample.getSampleNo())));
            event.setEvent(ApplySampleEventDto.EventType.TwoCheck);

            final String json = JSON.toJSONString(event);
            rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

            log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功",
                    sample.getApplySampleId(), sample.getBarcode(), json, RabbitMQService.EXCHANGE, ROUTING_KEY);
        }

        return CONTINUE_PROCESSING;
    }
}
