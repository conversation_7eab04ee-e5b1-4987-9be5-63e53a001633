package com.labway.lims.microbiology.service.chain.receive;

import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/18 14:57
 */
@Component
public class ReceiveGermAndMedicineDeleteMedicineCommand implements Filter, Command {
    @Resource
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {
        final ReceiveGermAndMedicineContext context = ReceiveGermAndMedicineContext.from(c);
        final List<MicrobiologyGermMedicineDto> deleteMedicines = context.getDeleteMedicines();

        if (CollectionUtils.isNotEmpty(deleteMedicines)) {
            final Set<Long> deleteIds = deleteMedicines.stream().map(MicrobiologyGermMedicineDto::getMicrobiologyGermMedicineId).collect(Collectors.toSet());
            microbiologyGermMedicineService.deleteByIds(deleteIds);
        }

        return CONTINUE_PROCESSING;
    }
}
