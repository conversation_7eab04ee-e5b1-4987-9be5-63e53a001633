package com.labway.lims.microbiology.service.chain.oneAudit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.service.chain.audit.AuditMicroSampleContext;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;

@Component
public class OneSampleAuditFlowCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditMicroSampleContext context = AuditMicroSampleContext.from(c);
        List<MicrobiologySampleDto> samples = context.getSamples();
        LoginUserHandler.User user = context.getUser();

        final LinkedList<Long> ids = snowflakeService.genIds(samples.size());
        final LinkedList<SampleFlowDto> flows = new LinkedList<>();
        for (MicrobiologySampleDto sample : context.getSamples()) {
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(ids.pop());
            sampleFlow.setApplyId(sample.getApplyId());
            sampleFlow.setApplySampleId(sample.getApplySampleId());
            sampleFlow.setBarcode(sample.getBarcode());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setOperateCode(BarcodeFlowEnum.ONE_CHECK.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.ONE_CHECK.getDesc());
            sampleFlow.setContent("一次审核");
            flows.add(sampleFlow);
        }
        sampleFlowService.addSampleFlows(flows);
        return CONTINUE_PROCESSING;
    }

}
