package com.labway.lims.microbiology.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class MicroResultRecordComparesVo {

    /**
     * 样本ID
     */
    private Long sampleId;
    /**
     * 类型
     *
     * @see ItemTypeEnum
     */
    private String itemType;
    /**
     * 日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date dateStart;

    /**
     * 日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date dateEnd;

}