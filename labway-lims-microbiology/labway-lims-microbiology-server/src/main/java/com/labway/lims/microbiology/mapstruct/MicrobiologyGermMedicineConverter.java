
package com.labway.lims.microbiology.mapstruct;

import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.dto.UpdateMicroGermMedicineDto;
import com.labway.lims.microbiology.model.TbMicrobiologyGermMedicine;
import com.labway.lims.microbiology.vo.UpdateMicrobiologyGermMedicineRequestVo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 微生物细菌药物 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface MicrobiologyGermMedicineConverter {

    MicrobiologyGermMedicineDto microbiologyGermMedicineDtoFromTbObj(TbMicrobiologyGermMedicine obj);

    TbMicrobiologyGermMedicine tbMicrobiologyGermMedicineFromTbObjDto(MicrobiologyGermMedicineDto dto);

    UpdateMicroGermMedicineDto updateMicroGermMedicineDtoFromTbObj(UpdateMicrobiologyGermMedicineRequestVo vo);

    MicrobiologyGermMedicineDto microbiologyGermMedicineDtoDtoFromTbObjDto(MicrobiologyGermMedicineDto obj);

    List<MicrobiologyGermMedicineDto>
        microbiologyGermMedicineDtoListFromTbObjList(List<TbMicrobiologyGermMedicine> list);
}
