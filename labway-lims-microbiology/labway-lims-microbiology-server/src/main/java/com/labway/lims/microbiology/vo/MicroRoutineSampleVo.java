package com.labway.lims.microbiology.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/11 14:44
 */
@Getter
@Setter
public class MicroRoutineSampleVo {

    /**
     * ID
     */
    private Long microbiologySampleId;

    /**
     * 样本申请单ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 样本状态
     */
    private Integer status;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 检验日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDate;

    /**
     * 录入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date enterDate;

    /**
     * 审核人ID
     */
    private Long checkerId;

    /**
     * 一次审核工号
     */
    private String checkerName;


    /**
     * 审核日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date checkDate;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 样本是否加急 (1急诊 0不急)
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 检验项目名称（检验目的）
     */
    private String testItemName;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 是否打印
     */
    private Integer isPrint;

    /**
     * 就诊类型
     */
    private String applyType;
    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;

    /**
     * 检验者id
     */
    private Long testerId;

    /**
     * 检验者名称
     */
    private String testerName;

    /**
     * 是否是免疫二次分拣 1：是 0：否
     */
    private Integer isImmunityTwoPick;

    /**
     * 一次审核人ID
     */
    private Long oneCheckerId;

    /**
     * 一次审核工号
     */
    private String oneCheckerName;


    /**
     * 一次审核日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date oneCheckerDate;


}
