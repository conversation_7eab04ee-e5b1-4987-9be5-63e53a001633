package com.labway.lims.microbiology.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 微生物样本
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class MicrobiologyBatchRecordSampleItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long microbiologySampleId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;
    /**
     * 检验项目ID
     */

    private Long testItemId;
    /**
     * 检验项目编码
     */

    private String testItemCode;
    /**
     * 检验项目名称
     */

    private String testItemName;

    /**
     * 患者名称
     */
    private String patientName;
}
