package com.labway.lims.microbiology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleResultDto;
import com.labway.lims.microbiology.model.TbMicrobiologySampleResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 微生物样本结果 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbMicrobiologySampleResultMapper extends BaseMapper<TbMicrobiologySampleResult> {

    void addBatch(@Param("list") Collection<MicrobiologySampleResultDto> germs);
}
