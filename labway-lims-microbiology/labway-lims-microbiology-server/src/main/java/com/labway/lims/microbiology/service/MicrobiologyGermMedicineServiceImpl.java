package com.labway.lims.microbiology.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.microbiology.MicroFormulaEnum;
import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import com.labway.lims.api.enums.microbiology.MicroTestMethodEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.MedicineDto;
import com.labway.lims.base.api.dto.MedicineGermRelationDto;
import com.labway.lims.base.api.service.*;
import com.labway.lims.microbiology.api.dto.*;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleGermService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.microbiology.mapper.TbMicrobiologyGermMedicineMapper;
import com.labway.lims.microbiology.mapstruct.MicrobiologyGermMedicineConverter;
import com.labway.lims.microbiology.model.TbMicrobiologyGermMedicine;
import com.labway.lims.microbiology.service.chain.receive.ReceiveGermAndMedicineChain;
import com.labway.lims.microbiology.service.chain.receive.ReceiveGermAndMedicineContext;
import com.labway.lims.routine.api.dto.MachineSampleResultDto;
import com.labway.lims.routine.api.dto.SampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 14:50
 */
@Slf4j
@DubboService
public class MicrobiologyGermMedicineServiceImpl implements MicrobiologyGermMedicineService {
    @Resource
    private TbMicrobiologyGermMedicineMapper tbMicrobiologyGermMedicineMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private GermService germService;
    @DubboReference
    private GermGenusService germGenusService;
    @DubboReference
    private MedicineService medicineService;
    @Resource
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @Resource
    private MicrobiologySampleGermService microbiologySampleGermService;
    @Resource
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;
    @Resource
    private MicrobiologyGermMedicineConverter microbiologyGermMedicineConverter;

    @DubboReference
    private MedicineGermRelationService medicineGermRelationService;
    @DubboReference
    private InstrumentGermService instrumentGermService;
    @DubboReference
    private InstrumentMedicineService instrumentMedicineService;
    @Resource
    private ReceiveGermAndMedicineChain receiveGermAndMedicineChain;

    @Override
    public List<MicrobiologyGermMedicineDto> selectByMicrobiologySampleGermId(long microbiologySampleGermId) {
        final LambdaQueryWrapper<TbMicrobiologyGermMedicine> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMicrobiologyGermMedicine::getMicrobiologySampleGermId, microbiologySampleGermId);
        return tbMicrobiologyGermMedicineMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    public List<MicrobiologyGermMedicineDto>
        selectByMicrobiologySampleGermIds(Collection<Long> microbiologySampleGermIds) {

        if (CollectionUtils.isEmpty(microbiologySampleGermIds)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbMicrobiologyGermMedicine> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbMicrobiologyGermMedicine::getMicrobiologySampleGermId, microbiologySampleGermIds);
        return tbMicrobiologyGermMedicineMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    public long addGermMedicine(MicrobiologyGermMedicineDto dto) {

        TbMicrobiologyGermMedicine target =
            microbiologyGermMedicineConverter.tbMicrobiologyGermMedicineFromTbObjDto(dto);

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        if (Objects.isNull(target.getMicrobiologyGermMedicineId())) {
            target.setMicrobiologyGermMedicineId(snowflakeService.genId());
        }
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbMicrobiologyGermMedicineMapper.insert(target) < 1) {
            throw new IllegalStateException("微生物药物添加失败");
        }

        log.info("用户 [{}] 添加微生物药物成功 [{}]", loginUser.getNickname(), JSON.toJSONString(dto));

        return target.getMicrobiologyGermMedicineId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long updateByMicrobiologyGermMedicineId(MicrobiologyGermMedicineDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbMicrobiologyGermMedicine target =
            microbiologyGermMedicineConverter.tbMicrobiologyGermMedicineFromTbObjDto(dto);
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbMicrobiologyGermMedicineMapper.updateById(target) < 1) {
            throw new LimsException("修改微生物细菌药物结果失败");
        }

        log.info("用户 [{}] 修改微生物药物成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
        return target.getMicrobiologyGermMedicineId();
    }

    @Override
    public void updateByMicrobiologyGermMedicineIds(Collection<MicrobiologyGermMedicineDto> germMedicines) {

        if (CollectionUtils.isEmpty(germMedicines)) {
            return;
        }
        tbMicrobiologyGermMedicineMapper.updateByMicrobiologyGermMedicineIds(germMedicines);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Collection<Long> addGermMedicineBatch(Collection<MicrobiologyGermMedicineDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        final LoginUserHandler.User user = LoginUserHandler.get();

        final List<TbMicrobiologyGermMedicine> medicines =
            JSON.parseArray(JSON.toJSONString(dtos), TbMicrobiologyGermMedicine.class);
        final LinkedList<Long> ids = snowflakeService.genIds(dtos.size());

        for (TbMicrobiologyGermMedicine medicine : medicines) {
            String group = StringUtils.defaultString(medicine.getGroup());
            medicine.setGroup(group);
            medicine.setMicrobiologyGermMedicineId(ids.pop());
            medicine.setCreateDate(new Date());
            medicine.setCreatorId(user.getUserId());
            medicine.setCreatorName(user.getNickname());
            medicine.setUpdateDate(new Date());
            medicine.setUpdaterId(user.getUserId());
            medicine.setUpdaterName(user.getNickname());
            medicine.setIsDelete(YesOrNoEnum.NO.getCode());
        }

        tbMicrobiologyGermMedicineMapper.addBatch(medicines);
        return ids;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MicrobiologyGermMedicineUpdateResultDto updateMedicineResult(MicrobiologySampleDto sample,
        MicrobiologySampleGermDto sampleGermDto, MicrobiologyGermMedicineDto germMedicineDto,
        List<MedicineGermRelationDto> relationDtos, MicrobiologyGermMedicineUpdateDto updateDto) {

        // 修改药物方法
        if (Objects.equals(updateDto.getUpdateType(), 1)) {
            MedicineGermRelationDto relationDto = relationDtos.stream()
                .filter(obj -> Objects.equals(obj.getExamMethodCode(), updateDto.getMedicineMethod())).findFirst()
                .orElse(null);
            if (Objects.isNull(relationDto)) {
                throw new LimsException("该药物没有维护此检测方法");
            }

            // 重新计算敏感度
            String susceptibilityByResult = getSusceptibilityByResult(germMedicineDto.getResult(), relationDto);

            MicrobiologyGermMedicineDto update = new MicrobiologyGermMedicineDto();
            update.setMicrobiologyGermMedicineId(germMedicineDto.getMicrobiologyGermMedicineId());
            update.setMedicineMethod(relationDto.getExamMethodName());
            update.setMedicineMethodCode(relationDto.getExamMethodCode());
            update.setUnit(relationDto.getReferUnit());
            update.setSusceptibility(StringUtils.defaultIfBlank(susceptibilityByResult, relationDto.getSusceptibility()));
            update.setFoldPointScope(relationDto.getFoldPointScope());
            update.setRange(relationDto.getReferValueMin(), relationDto.getReferValueMax());
            this.updateByMicrobiologyGermMedicineId(update);
        }

        // 原始检测方法 对应
        MedicineGermRelationDto relationDtoOld = relationDtos.stream()
            .filter(obj -> Objects.equals(obj.getExamMethodCode(), germMedicineDto.getMedicineMethodCode())).findFirst()
            .orElse(null);

        // 修改结果值前缀
        if (Objects.equals(updateDto.getUpdateType(), 2) && Objects.nonNull(updateDto.getFormula())) {

            MicrobiologyGermMedicineDto update = new MicrobiologyGermMedicineDto();
            update.setMicrobiologyGermMedicineId(germMedicineDto.getMicrobiologyGermMedicineId());
            update.setFormula(updateDto.getFormula());
            // 结果值前缀 阴性、阳性
            if (StringUtils.equalsAnyIgnoreCase(updateDto.getFormula(), MicroFormulaEnum.YANG.getDesc(),
                MicroFormulaEnum.YANG.getDesc())) {
                update.setResult(StringUtils.EMPTY);
                // 重新计算敏感度
                update.setSusceptibility(getSusceptibilityByResult(StringUtils.EMPTY, relationDtoOld));
            }
            this.updateByMicrobiologyGermMedicineId(update);
        }

        // 修改结果值
        if (Objects.equals(updateDto.getUpdateType(), 3) && Objects.nonNull(updateDto.getResult())) {
            MicrobiologyGermMedicineDto update = new MicrobiologyGermMedicineDto();
            update.setMicrobiologyGermMedicineId(germMedicineDto.getMicrobiologyGermMedicineId());
            update.setResult(updateDto.getResult());
            // 重新计算敏感度
            update.setSusceptibility(getSusceptibilityByResult(updateDto.getResult(), relationDtoOld));
            this.updateByMicrobiologyGermMedicineId(update);
        }
        // 修改敏感度
        if (Objects.equals(updateDto.getUpdateType(), 4) && Objects.nonNull(updateDto.getSusceptibility())) {
            MicrobiologyGermMedicineDto update = new MicrobiologyGermMedicineDto();
            update.setMicrobiologyGermMedicineId(germMedicineDto.getMicrobiologyGermMedicineId());
            update.setSusceptibility(updateDto.getSusceptibility());
            this.updateByMicrobiologyGermMedicineId(update);
        }
        // 修改分组
        if (Objects.equals(updateDto.getUpdateType(), 5)) {
            MicrobiologyGermMedicineDto update = new MicrobiologyGermMedicineDto();
            update.setMicrobiologyGermMedicineId(germMedicineDto.getMicrobiologyGermMedicineId());
            update.setGroup(StringUtils.defaultString(updateDto.getGroup()));
            this.updateByMicrobiologyGermMedicineId(update);
        }

        // 修改微生物样本 检验时间
        // MicrobiologySampleDto microbiologySampleDto = new MicrobiologySampleDto();
        // microbiologySampleDto.setMicrobiologySampleId(sample.getMicrobiologySampleId());
        // microbiologySampleDto.setTestDate(new Date());
        // microbiologySampleService.updateByMicrobiologySampleId(microbiologySampleDto);

        // 修改后细菌药物
        MicrobiologyGermMedicineDto germMedicineDtoNew =
            this.selectByMicrobiologyGermMedicineId(germMedicineDto.getMicrobiologyGermMedicineId());
        if (Objects.isNull(germMedicineDtoNew)) {
            throw new LimsException("修改后细菌药物不存在");
        }

        // 构造响应结果
        MicrobiologyGermMedicineUpdateResultDto result = new MicrobiologyGermMedicineUpdateResultDto();
        result.setUnit(germMedicineDtoNew.getUnit());
        result.setRange(germMedicineDtoNew.getRange());
        result.setSusceptibility(germMedicineDtoNew.getSusceptibility());

        String compare = new CompareUtils<MicrobiologyGermMedicineDto>().compare(germMedicineDto, germMedicineDtoNew);

        final LoginUserHandler.User user = LoginUserHandler.get();
        if (StringUtils.isNotBlank(compare)) {
            // 信息没有变化
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(snowflakeService.genId());
            sampleFlow.setApplyId(sample.getApplyId());
            sampleFlow.setApplySampleId(sample.getApplySampleId());
            sampleFlow.setBarcode(sample.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.RESULT_UPDATE.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.RESULT_UPDATE.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(String.format("修改药物项目 [%s] 结果: %s", germMedicineDto.getMedicineName(), compare));
            sampleFlowService.addSampleFlow(sampleFlow);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long saveMedicineWhenAdd(MicrobiologySampleDto sampleDto, MicrobiologySampleGermDto sampleGermDto,
        MedicineDto medicineDto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        // 新增 细菌药物
        final MicrobiologyGermMedicineDto add = new MicrobiologyGermMedicineDto();
        add.setMicrobiologyGermMedicineId(snowflakeService.genId());
        add.setMicrobiologySampleId(sampleGermDto.getMicrobiologySampleId());
        add.setApplyId(sampleGermDto.getApplyId());
        add.setApplySampleId(sampleGermDto.getApplySampleId());
        add.setMicrobiologySampleGermId(sampleGermDto.getMicrobiologySampleGermId());
        add.setTestItemId(sampleGermDto.getTestItemId());
        add.setTestItemCode(sampleGermDto.getTestItemCode());
        add.setTestItemName(sampleGermDto.getTestItemName());
        add.setGermId(sampleGermDto.getGermId());
        add.setGermCode(sampleGermDto.getGermCode());
        add.setGermName(sampleGermDto.getGermName());
        add.setMedicineId(medicineDto.getMedicineId());
        add.setMedicineCode(medicineDto.getMedicineCode());
        add.setMedicineName(medicineDto.getMedicineName());
        add.setMedicineMethod(StringUtils.EMPTY);
        add.setMedicineMethodCode(StringUtils.EMPTY);
        add.setFormula(StringUtils.EMPTY);
        add.setResult(StringUtils.EMPTY);
        add.setSusceptibility(StringUtils.EMPTY);
        add.setUnit(StringUtils.EMPTY);
        add.setRange(StringUtils.EMPTY);
        add.setFoldPointScope(StringUtils.EMPTY);

        long microbiologyGermMedicineId = this.addGermMedicine(add);

        // 信息没有变化
        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(sampleDto.getApplyId());
        sampleFlow.setApplySampleId(sampleDto.getApplySampleId());
        sampleFlow.setBarcode(sampleDto.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.ADD_MEDICINE.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.ADD_MEDICINE.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());

        sampleFlow
            .setContent(String.format("[%s] 细菌下添加[%s]药物", sampleGermDto.getGermName(), medicineDto.getMedicineName()));

        sampleFlowService.addSampleFlow(sampleFlow);
        return microbiologyGermMedicineId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long saveMedicineWhenUpdate(MicrobiologySampleDto sampleDto, MicrobiologySampleGermDto sampleGermDto,
        MedicineDto medicineDto, List<MedicineGermRelationDto> relationDtos,
        MicrobiologyGermMedicineDto germMedicineDto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        // 检测方法存在 找与检测方法一样的
        MedicineGermRelationDto relationDto =
            relationDtos.stream()
                .filter(obj -> StringUtils.isBlank(sampleGermDto.getTestItemCode())
                    || Objects.equals(obj.getExamMethodCode(), sampleGermDto.getTestItemCode()))
                .findFirst().orElse(null);
        if (Objects.isNull(relationDto)) {
            // 找不到用第一条
            relationDto = relationDtos.get(NumberUtils.INTEGER_ZERO);
        }

        // 更新 此 细菌药物 药物信息
        MicrobiologyGermMedicineDto update =
            microbiologyGermMedicineConverter.microbiologyGermMedicineDtoDtoFromTbObjDto(germMedicineDto);

        update.setMicrobiologyGermMedicineId(germMedicineDto.getMicrobiologyGermMedicineId());

        // 修改药物相关信息
        update.setMedicineId(medicineDto.getMedicineId());
        update.setMedicineCode(medicineDto.getMedicineCode());
        update.setMedicineName(medicineDto.getMedicineName());
        update.setUnit(relationDto.getReferUnit());
        update.setFoldPointScope(relationDto.getFoldPointScope());
        update.setRange(relationDto.getReferValueMin(), relationDto.getReferValueMax());
        // 入库
        this.updateByMicrobiologyGermMedicineId(update);

        String compare = new CompareUtils<MicrobiologyGermMedicineDto>().compare(germMedicineDto, update);

        if (StringUtils.isNotBlank(compare)) {
            // 信息没有变化
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(snowflakeService.genId());
            sampleFlow.setApplyId(sampleDto.getApplyId());
            sampleFlow.setApplySampleId(sampleDto.getApplySampleId());
            sampleFlow.setBarcode(sampleDto.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.UPDATE_MEDICINE.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.UPDATE_MEDICINE.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());

            sampleFlow.setContent(String.format("修改 [%s] 细菌下药物: %s", germMedicineDto.getGermName(), compare));

            sampleFlowService.addSampleFlow(sampleFlow);
        }
        return germMedicineDto.getMicrobiologyGermMedicineId();
    }

    @Override
    @Nullable
    public MicrobiologyGermMedicineDto selectByMicrobiologyGermMedicineId(long microbiologyGermMedicineId) {
        return convert(tbMicrobiologyGermMedicineMapper.selectById(microbiologyGermMedicineId));
    }

    @Override
    public List<MicrobiologyGermMedicineDto>
        selectByMicrobiologyGermMedicineIds(Collection<Long> microbiologyGermMedicineIds) {
        if (CollectionUtils.isEmpty(microbiologyGermMedicineIds)) {
            return Collections.emptyList();
        }
        return tbMicrobiologyGermMedicineMapper.selectBatchIds(microbiologyGermMedicineIds).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    public List<MicrobiologyGermMedicineDto> selectByMicrobiologySampleId(long microbiologySampleId) {
        final LambdaQueryWrapper<TbMicrobiologyGermMedicine> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMicrobiologyGermMedicine::getMicrobiologySampleId, microbiologySampleId);
        return tbMicrobiologyGermMedicineMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    public void deleteByIds(Collection<Long> ids) {
        tbMicrobiologyGermMedicineMapper.deleteBatchIds(ids);
        log.info("用户 [{}] 删除微生物样本细菌药物结果 [{}] 成功", LoginUserHandler.get().getNickname(), ids);
    }

    @Override
    public boolean deleteByMicrobiologySampleId(long microbiologySampleId) {
        final LambdaQueryWrapper<TbMicrobiologyGermMedicine> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMicrobiologyGermMedicine::getMicrobiologySampleId, microbiologySampleId);

        if (tbMicrobiologyGermMedicineMapper.delete(wrapper) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除微生物样本 [{}] 药物成功", LoginUserHandler.get().getNickname(), microbiologySampleId);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receive(InstrumentResultReceiverDto dto) {
        final ReceiveGermAndMedicineContext medicineContext = new ReceiveGermAndMedicineContext();
        medicineContext.setDate(dto.getDate());
        medicineContext.setMicrobiologySampleId(dto.getMicrobiologySampleId());
        medicineContext.setGermMap(dto.getGermMap());

        try {
            if (!receiveGermAndMedicineChain.execute(medicineContext)) {
                throw new IllegalStateException("微生物接收结果");
            }

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("微生物接收结果 保存结果耗时\n{}", medicineContext.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    public void deleteByMicrobiologySampleIds(Collection<Long> microbiologySampleIds) {

        if (CollectionUtils.isEmpty(microbiologySampleIds)) {
            return;
        }

        final LambdaQueryWrapper<TbMicrobiologyGermMedicine> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbMicrobiologyGermMedicine::getMicrobiologySampleId, microbiologySampleIds);

        tbMicrobiologyGermMedicineMapper.delete(wrapper);

        log.info("用户 [{}] 删除微生物药物 {} 药物成功", LoginUserHandler.get().getNickname(), microbiologySampleIds);
    }

    @Override
    public void deleteByMicrobiologySampleGermId(long microbiologySampleGermId) {

        final LambdaQueryWrapper<TbMicrobiologyGermMedicine> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbMicrobiologyGermMedicine::getMicrobiologySampleGermId, microbiologySampleGermId);

        tbMicrobiologyGermMedicineMapper.delete(wrapper);

        log.info("用户 [{}] 删除微生物药物 {} 药物成功", LoginUserHandler.get().getNickname(), microbiologySampleGermId);
    }

    @Override
    public void deleteByMicrobiologySampleGermIds(Collection<Long> microbiologySampleGermIds) {
        if (CollectionUtils.isEmpty(microbiologySampleGermIds)) {
            return;
        }
        final LambdaQueryWrapper<TbMicrobiologyGermMedicine> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbMicrobiologyGermMedicine::getMicrobiologySampleGermId, microbiologySampleGermIds);

        tbMicrobiologyGermMedicineMapper.delete(wrapper);

        log.info("用户 [{}] 删除微生物药物 {} 药物成功", LoginUserHandler.get().getNickname(), microbiologySampleGermIds);
    }

    @Override
    public List<MicrobiologyGermMedicineDto> selectByMicrobiologySampleIds(Collection<Long> microbiologySampleIds) {
        final LambdaQueryWrapper<TbMicrobiologyGermMedicine> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbMicrobiologyGermMedicine::getMicrobiologySampleId, microbiologySampleIds);
        return tbMicrobiologyGermMedicineMapper.selectList(wrapper).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    private void saveMachineResultFlow(List<MachineSampleResultDto> machineSampleResults) {

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(machineSampleResults)) {
            return;
        }

        machineSampleResults =
            machineSampleResults.stream().filter(i -> Objects.nonNull(i.getSample())).collect(Collectors.toList());

        final MachineSampleResultDto resultDto = machineSampleResults.stream().findFirst().orElse(null);

        if (Objects.isNull(resultDto)) {
            return;
        }

        final SampleDto sample = resultDto.getSample();

        final SampleFlowDto sampleFlowDto = new SampleFlowDto();
        sampleFlowDto.setSampleFlowId(snowflakeService.genId());
        sampleFlowDto.setApplyId(sample.getApplyId());
        sampleFlowDto.setApplySampleId(sample.getApplySampleId());
        sampleFlowDto.setBarcode(sample.getBarcode());
        sampleFlowDto.setOperateCode(BarcodeFlowEnum.RESULT_UPDATE.name());
        sampleFlowDto.setOperateName(BarcodeFlowEnum.RESULT_UPDATE.getDesc());
        sampleFlowDto.setOperator("仪器");
        sampleFlowDto.setOperatorId(99L);
        sampleFlowDto.setOrgName(LoginUserHandler.get().getOrgName());
        sampleFlowDto.setOrgId(LoginUserHandler.get().getOrgId());
        sampleFlowDto.setCreateDate(new Date());
        sampleFlowDto.setCreatorId(LoginUserHandler.get().getUserId());
        sampleFlowDto.setCreatorName(LoginUserHandler.get().getNickname());
        sampleFlowDto.setUpdateDate(new Date());
        sampleFlowDto.setUpdaterName(LoginUserHandler.get().getNickname());
        sampleFlowDto.setUpdaterId(LoginUserHandler.get().getUserId());
        sampleFlowDto.setIsDelete(YesOrNoEnum.NO.getCode());

        StringBuilder sbContent = new StringBuilder();

        for (MachineSampleResultDto machineSampleResult : machineSampleResults) {

            final String testResult = machineSampleResult.getTestResult();

            final String beforeResult = machineSampleResult.getBeforeResult();

            final String customerReportItemName = machineSampleResult.getReportItemName();

            if (StringUtils.isBlank(beforeResult)) {
                sbContent.append(String.format("药物项目 [%s] 新增结果 [%s] \n", customerReportItemName, testResult));

            } else {
                sbContent.append(
                    String.format("药物项目 [%s] 结果 从 [%s] 修改成 [%s] \n", customerReportItemName, beforeResult, testResult));
            }

            sbContent.append(" (仪器)");

        }

        sampleFlowDto.setContent(sbContent.toString());

        sampleFlowService.addSampleFlow(sampleFlowDto);

    }

    private MicrobiologyGermMedicineDto convert(TbMicrobiologyGermMedicine medicine) {
        if (Objects.isNull(medicine)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(medicine), MicrobiologyGermMedicineDto.class);
    }

    /**
     * 根据结果 获取 敏感度
     *
     * @param result 结果值
     */
    private String getSusceptibilityByResult(String result, MedicineGermRelationDto relationDto) {
        BigDecimal value = NumberUtils.isParsable(result) ? new BigDecimal(result) : null;
        if (Objects.isNull(value) || Objects.isNull(relationDto)) {
            return StringUtils.EMPTY;
        }

        // 上下限不为数字
        if (!(NumberUtils.isParsable(relationDto.getReferValueMin())
            && NumberUtils.isParsable(relationDto.getReferValueMax()))) {
            return StringUtils.EMPTY;
        }

        // 检测方法 若检测方法是MIC，则小于是敏感，
        String examMethodCode = relationDto.getExamMethodCode();

        MicroSusceptibilityEnum susceptibility;

        final BigDecimal min = new BigDecimal(relationDto.getReferValueMin());
        final BigDecimal max = new BigDecimal(relationDto.getReferValueMax());

        // 默认 中敏
        susceptibility = MicroSusceptibilityEnum.MID_SENSITIVE;

        // 结果值 小于 下限 若检测方法为KB，则小于是耐药， 若检测方法是MIC，则小于是敏感 其他也为 敏感
        if (min.compareTo(value) > 0) {
            if (Objects.equals(examMethodCode, MicroTestMethodEnum.MANUAL.getCode())) {
                susceptibility = MicroSusceptibilityEnum.RESISTANT;
            } else if (Objects.equals(examMethodCode, MicroTestMethodEnum.INSTRUMENT.getCode())) {
                susceptibility = MicroSusceptibilityEnum.SENSITIVE;
            } else {
                susceptibility = MicroSusceptibilityEnum.SENSITIVE;
            }
        }

        // 结果值 大于上限 若检测方法为KB，大于是敏感 若检测方法是MIC， 大于是耐药 其他也为 耐药
        if (value.compareTo(max) > 0) {
            if (Objects.equals(examMethodCode, MicroTestMethodEnum.MANUAL.getCode())) {
                susceptibility = MicroSusceptibilityEnum.SENSITIVE;
            } else if (Objects.equals(examMethodCode, MicroTestMethodEnum.INSTRUMENT.getCode())) {
                susceptibility = MicroSusceptibilityEnum.RESISTANT;
            } else {
                susceptibility = MicroSusceptibilityEnum.RESISTANT;
            }
        }
        return susceptibility.getDesc();
    }

}
