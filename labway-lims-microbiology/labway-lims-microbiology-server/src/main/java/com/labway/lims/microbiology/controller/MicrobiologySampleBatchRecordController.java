package com.labway.lims.microbiology.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyInspectionDto;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleResultDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleResultService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.microbiology.vo.MicrobiologyBatchRecordSampleItemVo;
import com.labway.lims.microbiology.vo.QueryMicrobiologySamplesVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 16:38
 */
@RestController
@RequestMapping("/microbiology-sample-batch-record")
public class MicrobiologySampleBatchRecordController extends BaseController {
    @Resource
    private MicrobiologySampleResultService microbiologySampleResultService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    @PostMapping("/samples-es")
    public Object getSamples(@RequestBody QueryMicrobiologySamplesVo queryVo) {
        if (Objects.isNull(queryVo.getTestDateStart()) || Objects.isNull(queryVo.getTestDateEnd())) {
            throw new IllegalStateException("检测日期不能为空");
        }

        // 转为es 查询条件
        SampleEsQuery query = new SampleEsQuery();
        query.setStartTestDate(queryVo.getTestDateStart());
        query.setEndTestDate(queryVo.getTestDateEnd());
        query.setGroupIds(new HashSet<>(Set.of(LoginUserHandler.get().getGroupId())));
        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(new HashSet<>(Set.of(queryVo.getHspOrgId())));
        }
        if (Objects.nonNull(queryVo.getTestItemId())) {
            query.setTestItemIds(new HashSet<>(Set.of(queryVo.getTestItemId())));
        }
        //未审状态
        query.setSampleStatus(new HashSet<>(Set.of(SampleStatusEnum.NOT_AUDIT.getCode())));

        List<BaseSampleEsModelDto> sampleEsModelDtos = elasticSearchSampleService.selectSamples(query);

        Set<Long> sampleIds = sampleEsModelDtos.stream().filter(e -> e instanceof MicrobiologyInspectionDto)
                .map(e -> (MicrobiologyInspectionDto) e).map(MicrobiologyInspectionDto::getSampleId).collect(Collectors.toSet());

        // 微生物结果判定从数据库中拉取结果数据
        Map<Long, List<MicrobiologySampleResultDto>> microbiologySampleResultMap;
        if (queryVo.isFilterNoResult()) {
            microbiologySampleResultMap = microbiologySampleResultService.selectByMicrobiologySampleIds(sampleIds)
                    .stream().collect(Collectors.groupingBy(MicrobiologySampleResultDto::getMicrobiologySampleId));
        } else {
            microbiologySampleResultMap = Map.of();
        }

        final List<MicrobiologyInspectionDto> sampleEsModels = sampleEsModelDtos
                // 微生物样本
                .stream().filter(e -> {
                    if (e instanceof MicrobiologyInspectionDto) {
                        if (queryVo.isFilterNoResult()) {
                            List<MicrobiologySampleResultDto> microbiologySampleResults = microbiologySampleResultMap.get(e.getSampleId());
                            // 无结果记录，或者结果记录的结果字段为空，皆为《没有结果的样本》
                            return CollectionUtils.isEmpty(microbiologySampleResults) ||
                                    microbiologySampleResults.stream().allMatch(i -> StringUtils.isBlank(i.getResult()));
                        }
                        return true;
                    }

                    return false;
                })
                // 转成微生物样本
                .map(e -> (MicrobiologyInspectionDto) e).collect(Collectors.toList());

        //转对象
        return sampleEsModels.stream().map(this::convertItem2Vo).collect(Collectors.toList());
    }

    private MicrobiologyBatchRecordSampleItemVo convertItem2Vo(MicrobiologyInspectionDto inspectionDto) {
        MicrobiologyBatchRecordSampleItemVo itemVo = new MicrobiologyBatchRecordSampleItemVo();
        itemVo.setMicrobiologySampleId(inspectionDto.getSampleId());
        itemVo.setApplySampleId(inspectionDto.getApplySampleId());
        itemVo.setSampleNo(inspectionDto.getSampleNo());
        itemVo.setBarcode(inspectionDto.getBarcode());
        itemVo.setHspOrgId(inspectionDto.getHspOrgId());
        itemVo.setHspOrgName(inspectionDto.getHspOrgName());
        itemVo.setPatientName(inspectionDto.getPatientName());
        if (CollectionUtils.isNotEmpty(inspectionDto.getTestItems())) {
            //目前只取第一个
            itemVo.setTestItemId(inspectionDto.getTestItems().get(0).getTestItemId());
            itemVo.setTestItemCode(inspectionDto.getTestItems().get(0).getTestItemCode());
            itemVo.setTestItemName(inspectionDto.getTestItems().get(0).getTestItemName());
        }
        return itemVo;
    }
}
