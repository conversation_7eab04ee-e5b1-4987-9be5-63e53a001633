package com.labway.lims.microbiology.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class MicroSampleResultVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long microbiologySampleResultId;

    /**
     * 样本ID
     */
    private Long microbiologySampleId;

    /**
     * 申请单
     */
    private Long applyId;

    /**
     * 结果
     */
    private String result;

    /**
     * 结果描述
     */
    private String resultDesc;

    /**
     * 结果
     */
    private String resultCode;

    /**
     * 结果属性
     */
    private String resultProperty;

}
