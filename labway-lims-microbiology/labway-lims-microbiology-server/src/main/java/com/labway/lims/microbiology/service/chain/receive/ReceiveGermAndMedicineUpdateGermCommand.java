package com.labway.lims.microbiology.service.chain.receive;

import com.labway.lims.api.enums.microbiology.MicroTestMethodEnum;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleGermService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/18 10:50
 */
@Component
public class ReceiveGermAndMedicineUpdateGermCommand implements Filter, Command {

    @Resource
    private MicrobiologySampleGermService microbiologySampleGermService;

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {
        final ReceiveGermAndMedicineContext context = ReceiveGermAndMedicineContext.from(c);
        final List<MicrobiologySampleGermDto> updateGerms = context.getUpdateGerms();

        if (CollectionUtils.isNotEmpty(updateGerms)) {

            final Map<String, String> germRemarkMap = context.getGermRemarkMap();

            final List<MicrobiologySampleGermDto> updates = new LinkedList<>();
            for (MicrobiologySampleGermDto updateGerm : updateGerms) {
                final MicrobiologySampleGermDto update = new MicrobiologySampleGermDto();
                final String remark = germRemarkMap.get(updateGerm.getGermCode());
                if (Objects.nonNull(remark)) {
                    update.setGermRemark(remark);
                    update.setGermRemarkCode(StringUtils.EMPTY);
                }
                update.setTestMethodCode(MicroTestMethodEnum.INSTRUMENT.getCode());
                update.setTestMethod(MicroTestMethodEnum.INSTRUMENT.getDesc());
                update.setMicrobiologySampleGermId(updateGerm.getMicrobiologySampleGermId());
                updates.add(update);
            }

            microbiologySampleGermService.updateByMicrobiologySampleGermIds(updates);
        }

        return CONTINUE_PROCESSING;
    }
}
