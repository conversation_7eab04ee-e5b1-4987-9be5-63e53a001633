package com.labway.lims.microbiology.service.chain.receive;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/17 19:20
 */
@Component
public class ReceiveGermAndMedicineSampleFlowCommand implements Filter, Command {

    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {
        final ReceiveGermAndMedicineContext context = ReceiveGermAndMedicineContext.from(c);
        final List<MicrobiologySampleGermDto> addGerms = context.getAddGerms();
        final List<MicrobiologyGermMedicineDto> addMedicines = context.getAddMedicines();
        final List<MicrobiologyGermMedicineDto> updateMedicines = context.getUpdateMedicines();
        final LinkedList<Long> ids = snowflakeService.genIds(1000);
        final LinkedList<SampleFlowDto> flows = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(addGerms)) {
            for (MicrobiologySampleGermDto germ : addGerms) {

                final SampleFlowDto flow = new SampleFlowDto();
                flow.setSampleFlowId(ids.pop());
                flow.setApplyId(context.getMicroSample().getApplyId());
                flow.setApplySampleId(context.getMicroSample().getApplySampleId());
                flow.setBarcode(context.getMicroSample().getBarcode());
                flow.setOperateCode(BarcodeFlowEnum.ADD_GERM.name());
                flow.setOperateName(BarcodeFlowEnum.ADD_GERM.getDesc());
                final LoginUserHandler.User user = LoginUserHandler.get();
                flow.setOperator(user.getNickname());
                flow.setOperatorId(user.getUserId());
                flow.setContent(String.format("新增细菌 [%s] ", germ.getGermName()));
                flow.setCreateDate(new Date());
                flow.setUpdateDate(new Date());
                flow.setUpdaterId(user.getUserId());
                flow.setUpdaterName(user.getNickname());
                flow.setOrgId(user.getOrgId());
                flow.setOrgName(user.getOrgName());
                flow.setIsDelete(YesOrNoEnum.NO.getCode());
                flows.add(flow);
            }
        }
        if (CollectionUtils.isNotEmpty(addMedicines)) {
            for (MicrobiologyGermMedicineDto medicine : addMedicines) {
                final SampleFlowDto flow = new SampleFlowDto();
                flow.setSampleFlowId(ids.pop());
                flow.setApplyId(context.getMicroSample().getApplyId());
                flow.setApplySampleId(context.getMicroSample().getApplySampleId());
                flow.setBarcode(context.getMicroSample().getBarcode());
                flow.setOperateCode(BarcodeFlowEnum.ADD_MEDICINE.name());
                flow.setOperateName(BarcodeFlowEnum.ADD_MEDICINE.getDesc());
                final LoginUserHandler.User user = LoginUserHandler.get();
                flow.setOperator(user.getNickname());
                flow.setOperatorId(user.getUserId());
                flow.setContent(String.format("细菌 [%s] 新增药物 [%s] 结果值 [%s] 敏感度 [%s]",
                        medicine.getGermName(), medicine.getMedicineName(),
                        medicine.getFormula() + medicine.getResult(), medicine.getSusceptibility()));
                flow.setCreateDate(new Date());
                flow.setUpdateDate(new Date());
                flow.setUpdaterId(user.getUserId());
                flow.setUpdaterName(user.getNickname());
                flow.setOrgId(user.getOrgId());
                flow.setOrgName(user.getOrgName());
                flow.setIsDelete(YesOrNoEnum.NO.getCode());
                flows.add(flow);
            }
        }

        if (CollectionUtils.isNotEmpty(updateMedicines)) {
            final List<MicrobiologyGermMedicineDto> oriMedicines = context.getOriMedicines();
            for (MicrobiologyGermMedicineDto medicine : updateMedicines) {
                final MicrobiologyGermMedicineDto oriMedicine = oriMedicines.stream()
                        .filter(f -> Objects.equals(f.getMedicineId(), medicine.getMedicineId())
                                && Objects.equals(f.getGermId(), medicine.getGermId()))
                        .findFirst()
                        .orElse(null);
                if (Objects.isNull(oriMedicine)) {
                    continue;
                }

                final SampleFlowDto flow = new SampleFlowDto();
                flow.setSampleFlowId(ids.pop());
                flow.setApplyId(context.getMicroSample().getApplyId());
                flow.setApplySampleId(context.getMicroSample().getApplySampleId());
                flow.setBarcode(context.getMicroSample().getBarcode());
                flow.setOperateCode(BarcodeFlowEnum.UPDATE_MEDICINE.name());
                flow.setOperateName(BarcodeFlowEnum.UPDATE_MEDICINE.getDesc());
                final LoginUserHandler.User user = LoginUserHandler.get();
                flow.setOperator(user.getNickname());
                flow.setOperatorId(user.getUserId());

                flow.setContent(String.format("细菌 [%s] 药物 [%s] 结果 从 [%s] 修改成 [%s] 敏感度 从[%s] 修改成 [%s]",
                        oriMedicine.getGermName(), oriMedicine.getMedicineName(), oriMedicine.getFormula() + oriMedicine.getResult(),
                        medicine.getFormula() + medicine.getResult(), oriMedicine.getSusceptibility(), medicine.getSusceptibility()));
                flow.setCreateDate(new Date());
                flow.setUpdateDate(new Date());
                flow.setUpdaterId(user.getUserId());
                flow.setUpdaterName(user.getNickname());
                flow.setOrgId(user.getOrgId());
                flow.setOrgName(user.getOrgName());
                flow.setIsDelete(YesOrNoEnum.NO.getCode());
                flows.add(flow);
            }
        }
        sampleFlowService.addSampleFlows(flows);

        return CONTINUE_PROCESSING;
    }
}
