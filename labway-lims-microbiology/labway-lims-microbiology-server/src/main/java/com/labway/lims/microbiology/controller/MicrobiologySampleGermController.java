package com.labway.lims.microbiology.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermUpdateDto;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleGermService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.microbiology.vo.MicroSampleGermVo;
import com.labway.lims.microbiology.vo.MicrobiologySampleGermVo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/4/11 16:19
 */
@RestController
@RequestMapping("/sample-germ")
public class MicrobiologySampleGermController extends BaseController {
    @DubboReference
    private MicrobiologySampleGermService microbiologySampleGermService;
    @Resource
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private GermService germService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @PostMapping("/sampleGerms")
    public Object sampleGerms(@RequestParam("microbiologySampleId") Long microbiologySampleId) {
        if (Objects.isNull(microbiologySampleId)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(
            JSON.toJSONString(microbiologySampleGermService.selectByMicrobiologySampleId(microbiologySampleId)),
            MicroSampleGermVo.class);
    }

    @PostMapping("/add")
    public Object addGerm(@RequestBody MicrobiologySampleGermVo vo) {
        if (Objects.isNull(vo.getMicrobiologySampleId())) {
            throw new IllegalArgumentException("微生物样本不能为空");
        }

        final MicrobiologySampleGermDto dto = JSON.parseObject(JSON.toJSONString(vo), MicrobiologySampleGermDto.class);
        final MicrobiologySampleDto sampleDto =
            microbiologySampleService.selectByMicrobiologySampleId(vo.getMicrobiologySampleId());

        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("当前微生物样本不存在");
        }
        final ApplySampleItemDto sampleItemDto = applySampleItemService
            .selectByApplySampleId(sampleDto.getApplySampleId()).stream().findFirst().orElse(null);
        if (Objects.isNull(sampleItemDto)) {
            throw new IllegalArgumentException("当前微生物样本检验项目不存在");
        } else {
            dto.setTestItemCode(sampleItemDto.getTestItemCode());
            dto.setTestItemName(sampleItemDto.getTestItemName());
            dto.setTestItemId(sampleItemDto.getTestItemId());
        }

        dto.setGermId(ObjectUtils.defaultIfNull(vo.getGermId(), NumberUtils.LONG_ZERO));
        dto.setMicrobiologySampleId(ObjectUtils.defaultIfNull(vo.getMicrobiologySampleId(), NumberUtils.LONG_ZERO));
        dto.setGermCode(StringUtils.defaultString(vo.getGermCode()));
        dto.setGermName(StringUtils.defaultString(vo.getGermName()));
        dto.setGermCount(StringUtils.defaultString(vo.getGermCount()));
        dto.setGermCountCode(StringUtils.defaultString(vo.getGermCountCode()));
        dto.setTestMethod(StringUtils.defaultString(vo.getTestMethod()));
        dto.setTestMethodCode(StringUtils.defaultString(vo.getTestMethodCode()));
        dto.setGermRemark(StringUtils.defaultString(vo.getGermRemark()));
        dto.setGermRemarkCode(StringUtils.defaultString(vo.getGermRemarkCode()));
        dto.setApplyId(sampleDto.getApplyId());
        dto.setApplySampleId(sampleDto.getApplySampleId());
        dto.setGermGenusId(ObjectUtils.defaultIfNull(vo.getGermGenusId(), NumberUtils.LONG_ZERO));
        dto.setGermGenusCode(ObjectUtils.defaultIfNull(vo.getGermGenusCode(), StringUtils.EMPTY));

        return Map.of("id", microbiologySampleGermService.addGerm(dto));
    }

    /**
     * 微生物 细菌 信息更新
     */
    @PostMapping("/update")
    public Object updateMicrobiologySampleGerm(@RequestBody MicrobiologySampleGermUpdateDto vo) {
        if (Objects.isNull(vo.getMicrobiologySampleGermId())) {
            throw new IllegalArgumentException("微生物样本细菌ID不能为空");
        }
        if (Objects.isNull(vo.getUpdateType()) || !Set.of(1, 2, 3, 4).contains(vo.getUpdateType())) {
            throw new LimsException("修改内容类型不确定");
        }
        if (Objects.equals(vo.getUpdateType(), 1) && Objects.isNull(vo.getGermId())) {
            throw new LimsException("修改细菌不存在");
        }
        if (Objects.equals(vo.getUpdateType(), 2)) {
            if (StringUtils.isAnyBlank(vo.getGermCountCode(), vo.getGermCount())) {
                vo.setGermCount(StringUtils.EMPTY);
                vo.setGermCountCode(StringUtils.EMPTY);
            }
        }
        if (Objects.equals(vo.getUpdateType(), 3)
            && StringUtils.isAnyBlank(vo.getGermRemark(), vo.getGermRemarkCode())) {
            if (StringUtils.isAnyBlank(vo.getGermRemark(), vo.getGermRemarkCode())) {
                vo.setGermRemark(StringUtils.EMPTY);
                vo.setGermRemarkCode(StringUtils.EMPTY);
            }
        }

        MicrobiologySampleGermDto sampleGermDto =
            microbiologySampleGermService.selectByMicrobiologySampleGermId(vo.getMicrobiologySampleGermId());
        if (Objects.isNull(sampleGermDto)) {
            throw new LimsException("微生物细菌不存在");
        }

        final MicrobiologySampleDto sampleDto =
            microbiologySampleService.selectByMicrobiologySampleId(sampleGermDto.getMicrobiologySampleId());
        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("微生物细菌对应样本不存在");
        }

        microbiologySampleGermService.updateSampleGerm(sampleDto, sampleGermDto, vo);

        return Collections.emptyMap();
    }

    @PostMapping("/delete")
    public Object deleteGerm(@RequestParam("microbiologySampleGermId") Long microbiologySampleGermId) {
        final MicrobiologySampleGermDto sampleGerm =
            microbiologySampleGermService.selectByMicrobiologySampleGermId(microbiologySampleGermId);

        // 删除细菌
        microbiologySampleGermService.deleteByMicrobiologySampleGermId(microbiologySampleGermId);

        // 删除药物
        microbiologyGermMedicineService.deleteByMicrobiologySampleGermId(microbiologySampleGermId);

        if (Objects.nonNull(sampleGerm)) {
            final MicrobiologySampleDto sample =
                microbiologySampleService.selectByMicrobiologySampleId(sampleGerm.getMicrobiologySampleId());
            if (Objects.nonNull(sample)) {
                final SampleFlowDto flow = new SampleFlowDto();
                flow.setSampleFlowId(snowflakeService.genId());
                flow.setApplyId(sample.getApplyId());
                flow.setApplySampleId(sample.getApplySampleId());
                flow.setBarcode(sample.getBarcode());
                flow.setOperateCode(BarcodeFlowEnum.DELETE_GERM.name());
                flow.setOperateName(BarcodeFlowEnum.DELETE_GERM.getDesc());
                final LoginUserHandler.User user = LoginUserHandler.get();
                flow.setOperator(user.getNickname());
                flow.setOperatorId(user.getUserId());
                flow.setContent("删除细菌");
                flow.setCreateDate(new Date());
                flow.setUpdateDate(new Date());
                flow.setUpdaterId(user.getUserId());
                flow.setUpdaterName(user.getNickname());
                flow.setOrgId(user.getOrgId());
                flow.setOrgName(user.getOrgName());
                flow.setIsDelete(YesOrNoEnum.NO.getCode());
                sampleFlowService.addSampleFlow(flow);
            }
        }

        return Collections.emptyMap();
    }
}
