package com.labway.lims.microbiology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.model.TbMicrobiologySampleGerm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 微生物细菌 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbMicrobiologySampleGermMapper extends BaseMapper<TbMicrobiologySampleGerm> {

    void addBatch(@Param("germs") Collection<MicrobiologySampleGermDto> germs);

    int updateByMicrobiologySampleGermIds(@Param("germs") Collection<MicrobiologySampleGermDto> germ);

}
