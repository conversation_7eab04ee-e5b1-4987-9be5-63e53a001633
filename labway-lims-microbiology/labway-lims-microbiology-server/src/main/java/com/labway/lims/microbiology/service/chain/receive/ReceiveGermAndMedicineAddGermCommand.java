package com.labway.lims.microbiology.service.chain.receive;

import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleGermService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/17 17:02
 */
@Component
public class ReceiveGermAndMedicineAddGermCommand implements Filter, Command {

    @Resource
    private MicrobiologySampleGermService microbiologySampleGermService;

    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {
        final ReceiveGermAndMedicineContext context = ReceiveGermAndMedicineContext.from(c);

        final List<MicrobiologySampleGermDto> addGerms = context.getAddGerms();
        if (CollectionUtils.isNotEmpty(addGerms)) {

            final Map<String, String> germRemarkMap = context.getGermRemarkMap();

            for (MicrobiologySampleGermDto addGerm : addGerms) {
                final String remark = germRemarkMap.get(addGerm.getGermCode());
                if (Objects.nonNull(remark)) {
                    addGerm.setGermRemark(remark);
                    addGerm.setGermRemarkCode(StringUtils.EMPTY);
                }
            }
            microbiologySampleGermService.addGermBatch(addGerms);
        }

        return CONTINUE_PROCESSING;
    }
}
