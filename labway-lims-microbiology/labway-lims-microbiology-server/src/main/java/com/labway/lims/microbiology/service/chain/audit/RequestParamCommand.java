package com.labway.lims.microbiology.service.chain.audit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleResultDto;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleGermService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleResultService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 微生物审核检查 参数
 */
@Slf4j
@Component
public class RequestParamCommand implements Command {

    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private MicrobiologySampleService microbiologySampleService;
    @Resource
    private MicrobiologySampleResultService microbiologySampleResultService;
    @Resource
    private MicrobiologySampleGermService microbiologySampleGermService;
    @Resource
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditMicroSampleContext context = AuditMicroSampleContext.from(c);

//        LoginUserHandler.User user = context.getUser();
        // 要 审核的微生物 样本ids
        Collection<Long> microbiologySampleIds = context.getMicrobiologySampleIds();
        // 是否需要一审
        boolean oneCheck = context.isOneCheck();

        // 要审核的微生物样本 信息
        List<MicrobiologySampleDto> microbiologySampleDtos =
            microbiologySampleService.selectByMicrobiologySampleIds(microbiologySampleIds);
//
//        // 对应申请单样本ids
//        Set<Long> applySampleIds =
//                microbiologySampleDtos.stream().map(MicrobiologySampleDto::getApplySampleId).collect(Collectors.toSet());
//
//        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);
//
//        applySampleDtos = applySampleDtos.stream().filter(e -> !e.getTesterId().equals(user.getUserId())).collect(Collectors.toList());
//        applySampleIds = applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());
//        final Set<Long> finalApplySampleIds = applySampleIds;
//        microbiologySampleDtos = microbiologySampleDtos.stream().filter(e-> finalApplySampleIds.contains(e.getApplySampleId())).collect(Collectors.toList());
//        microbiologySampleIds = microbiologySampleDtos.stream().map(MicrobiologySampleDto::getMicrobiologySampleId).collect(Collectors.toList());
//        context.setMicrobiologySampleIds(microbiologySampleIds);
//

        final Set<Long> selectMicrobiologySampleIds = microbiologySampleDtos.stream()
            .map(MicrobiologySampleDto::getMicrobiologySampleId).collect(Collectors.toSet());

        if (microbiologySampleIds.stream().anyMatch(x -> !selectMicrobiologySampleIds.contains(x))) {
            throw new IllegalStateException("存在无效微生物样本");
        }

        // 对应申请单信息
        Set<Long> applyIds =
            microbiologySampleDtos.stream().map(MicrobiologySampleDto::getApplyId).collect(Collectors.toSet());

        List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);

        Set<Long> selectApplyIds = applyDtos.stream().map(ApplyDto::getApplyId).collect(Collectors.toSet());

        if (applyIds.stream().anyMatch(x -> !selectApplyIds.contains(x))) {
            throw new IllegalStateException("存在无效微生物样本:没有对应申请单");
        }

        // 对应申请单样本ids
        final Set<Long> applySampleIds =
            microbiologySampleDtos.stream().map(MicrobiologySampleDto::getApplySampleId).collect(Collectors.toSet());

        final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);

        if (!oneCheck && applySampleDtos.stream().anyMatch(e -> LoginUserHandler.get().getUserId().equals(e.getTesterId()))) {
            throw new IllegalStateException("检验者与审核者不能为同一用户");
        }

        if (oneCheck && microbiologySampleDtos.stream().anyMatch(e -> LoginUserHandler.get().getUserId().equals(e.getOneCheckerId()))) {
            throw new IllegalStateException("一审和二审不能为同一用户");
        }

        Set<Long> selectApplySampleIds =
            applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());
        if (applySampleIds.stream().anyMatch(x -> !selectApplySampleIds.contains(x))) {
            throw new IllegalStateException("存在无效外送样本:没有对应申请单样本");
        }

        if (!oneCheck && applySampleDtos.stream()
            .anyMatch(obj -> !Objects.equals(obj.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode()))) {
            throw new IllegalStateException("已选数据存在已审样本，不可审核");
        }

        if (oneCheck && applySampleDtos.stream()
                .anyMatch(obj -> !Objects.equals(obj.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new IllegalStateException("已选数据存在未一审样本，不可审核");
        }

        for (MicrobiologySampleDto sample : microbiologySampleDtos) {
            if (applySampleService.isDisabled(sample.getApplySampleId())) {
                throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", sample.getBarcode()));
            }

            if (applySampleService.isTerminate(sample.getApplySampleId())) {
                throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", sample.getBarcode()));
            }
        }

        // 样本检验项目
        List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplySampleIds(applySampleIds);

        // 结果
        List<MicrobiologySampleResultDto> microbiologySampleResults =
            microbiologySampleResultService.selectByMicrobiologySampleIds(microbiologySampleIds);

        // 细菌
        List<MicrobiologySampleGermDto> microbiologySampleGerms =
            microbiologySampleGermService.selectByMicrobiologySampleIds(microbiologySampleIds);

        // 抗生素
        List<MicrobiologyGermMedicineDto> microbiologyGermMedicines =
            microbiologyGermMedicineService.selectByMicrobiologySampleIds(microbiologySampleIds);

        context.put(AuditMicroSampleContext.MICRO_SAMPLE_LIST, microbiologySampleDtos);
        context.put(AuditMicroSampleContext.APPLY, applyDtos);
        context.put(AuditMicroSampleContext.APPLY_SAMPLE, applySampleDtos);
        context.put(AuditMicroSampleContext.APPLY_SAMPLE_ITEM, applySampleItemDtos);
        context.put(AuditMicroSampleContext.MICROBIOLOGY_SAMPLE_RESULT, microbiologySampleResults);
        context.put(AuditMicroSampleContext.MICROBIOLOGY_SAMPLE_GERM, microbiologySampleGerms);
        context.put(AuditMicroSampleContext.MICROBIOLOGY_GERM_MEDICINE, microbiologyGermMedicines);
        return CONTINUE_PROCESSING;
    }
}
