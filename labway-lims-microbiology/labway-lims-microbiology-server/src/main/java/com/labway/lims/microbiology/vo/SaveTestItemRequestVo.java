package com.labway.lims.microbiology.vo;

import com.labway.lims.microbiology.api.dto.SaveFeeTestItemDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 增加&修改&删除 费用 项目信息 项目信息
 * 
 * <AUTHOR>
 * @since 2023/8/28 11:27
 */
@Getter
@Setter
public class SaveTestItemRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 微生物样本id
     */
    private Long microbiologySampleId;

    /**
     * 添加的检验项目
     */
    private List<SaveFeeTestItemDto> itemList;

}
