package com.labway.lims.microbiology.service.chain.audit;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.microbiology.api.dto.MicroSampleAuditDto;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleResultDto;
import com.labway.lims.microbiology.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/3 15:45
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class AuditMicroSampleContext extends StopWatchContext {

    /**
     * 审核微生物 样本id
     */
    private Collection<Long> microbiologySampleIds;
    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    /**
     * 审核类型
     * @see com.labway.lims.api.enums.routine.SampleAuditStatusEnum
     */
    private SampleAuditStatusEnum auditType;

    /**
     * 是都需要一次审核
     */
    private boolean oneCheck;

    public static final String SAMPLE_AUDTI_DTO = "SAMPLE_AUDTI_DTO" + IdUtil.objectId();

    public MicroSampleAuditDto getAuditDto() {
        return (MicroSampleAuditDto) get(SAMPLE_AUDTI_DTO);
    }


    // 微生物 检验
    public static final String MICRO_SAMPLE_LIST = "SAMPLE_LIST_" + IdUtil.objectId();

    public List<MicrobiologySampleDto> getSamples() {
        return (List<MicrobiologySampleDto>)get(MICRO_SAMPLE_LIST);
    }

    // 对应申请单
    public static final String APPLY = "APPLY_" + IdUtil.objectId();

    public List<ApplyDto> getApplyDtoList() {
        return (List<ApplyDto>)get(APPLY);
    }

    // 对应申请单样本
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    public List<ApplySampleDto> getApplySampleList() {
        return (List<ApplySampleDto>)get(APPLY_SAMPLE);
    }

    // 对应申请单样本检验项目
    public static final String APPLY_SAMPLE_ITEM = "APPLY_SAMPLE_ITEM_" + IdUtil.objectId();

    public List<ApplySampleItemDto> getApplySampleItemDtos() {
        return (List<ApplySampleItemDto>)get(APPLY_SAMPLE_ITEM);
    }

    // 微生物 样本结果
    public static final String MICROBIOLOGY_SAMPLE_RESULT = "MICROBIOLOGY_SAMPLE_RESULT_" + IdUtil.objectId();

    public List<MicrobiologySampleResultDto> getMicrobiologySampleResultDtos() {
        return (List<MicrobiologySampleResultDto>)get(MICROBIOLOGY_SAMPLE_RESULT);
    }

    // 微生物 细菌
    public static final String MICROBIOLOGY_SAMPLE_GERM = "MICROBIOLOGY_SAMPLE_GERM_" + IdUtil.objectId();

    public List<MicrobiologySampleGermDto> getMicrobiologySampleGermDtos() {
        return (List<MicrobiologySampleGermDto>)get(MICROBIOLOGY_SAMPLE_GERM);
    }

    // 微生物 细菌
    public static final String MICROBIOLOGY_GERM_MEDICINE = "MICROBIOLOGY_GERM_MEDICINE_" + IdUtil.objectId();

    public List<MicrobiologyGermMedicineDto> getMicrobiologyGermMedicineDtos() {
        return (List<MicrobiologyGermMedicineDto>)get(MICROBIOLOGY_GERM_MEDICINE);
    }

    public static AuditMicroSampleContext from(Context c) {
        return (AuditMicroSampleContext)c;
    }

    public boolean isOneCheck() {
        return oneCheck;
    }

    @Override
    protected String getWatchName() {
        return "微生物审核";
    }
}
