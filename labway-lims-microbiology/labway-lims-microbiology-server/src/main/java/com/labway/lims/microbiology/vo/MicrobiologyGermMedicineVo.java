package com.labway.lims.microbiology.vo;

import com.labway.lims.api.enums.microbiology.MicroFormulaEnum;
import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 微生物细菌药物
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class MicrobiologyGermMedicineVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    private Long microbiologyGermMedicineId;

    /**
     * 样本ID
     */
    private Long microbiologySampleId;

    /**
     * applySampleId
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 样本细菌ID
     */
    private Long microbiologySampleGermId;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 细菌
     */
    private Long germId;

    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 细菌名称
     */
    private String germName;

    /**
     * 药物
     */
    private Long medicineId;

    /**
     * 药物编码
     */
    private String medicineCode;

    /**
     * 药物名称
     */
    private String medicineName;

    /**
     * 药物方法
     */
    private String medicineMethod;

    /**
     * 药物结果前缀
     * @see MicroFormulaEnum
     */
    private String formula;

    /**
     * 结果
     */
    private String result;

    /**
     * 敏感度
     *
     * @see MicroSusceptibilityEnum
     */
    private String susceptibility;

    /**
     * 单位
     */
    private String unit;

    /**
     * 参考值
     */
    private String range;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    private Integer isDelete;
}
