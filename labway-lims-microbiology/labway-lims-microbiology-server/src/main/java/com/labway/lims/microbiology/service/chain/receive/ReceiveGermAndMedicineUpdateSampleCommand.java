package com.labway.lims.microbiology.service.chain.receive;


import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @since 2023/7/17 19:02
 */
@Component
public class ReceiveGermAndMedicineUpdateSampleCommand implements Filter, Command {


    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {

        return CONTINUE_PROCESSING;
    }
}
