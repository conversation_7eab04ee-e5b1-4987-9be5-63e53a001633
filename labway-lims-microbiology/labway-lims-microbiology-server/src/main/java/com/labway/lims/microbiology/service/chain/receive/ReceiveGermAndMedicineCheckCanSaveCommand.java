package com.labway.lims.microbiology.service.chain.receive;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/18 15:18
 */
@Component
public class ReceiveGermAndMedicineCheckCanSaveCommand implements Filter, Command {
    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {
        final ReceiveGermAndMedicineContext context = ReceiveGermAndMedicineContext.from(c);
        final ApplySampleDto applySample = context.getApplySample();

        if (Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()) || Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new IllegalStateException("已审核样本无法修改结果");
        }

        return CONTINUE_PROCESSING;
    }
}
