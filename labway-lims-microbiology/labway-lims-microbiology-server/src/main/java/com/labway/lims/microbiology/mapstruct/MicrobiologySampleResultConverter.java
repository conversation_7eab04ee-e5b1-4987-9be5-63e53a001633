package com.labway.lims.microbiology.mapstruct;

import com.labway.lims.microbiology.api.dto.MicrobiologySampleResultDto;
import com.labway.lims.microbiology.model.TbMicrobiologySampleResult;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 微生物样本结果 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface MicrobiologySampleResultConverter {

    MicrobiologySampleResultDto microbiologySampleResultDtoFromTbObj(TbMicrobiologySampleResult obj);

    List<MicrobiologySampleResultDto> microbiologySampleResultDtoListFromTbObjList(List<TbMicrobiologySampleResult> list);
}
