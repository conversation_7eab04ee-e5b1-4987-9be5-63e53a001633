package com.labway.lims.microbiology.mapstruct;

import com.alibaba.fastjson.JSON;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleResultDto;
import com.labway.lims.microbiology.model.TbMicrobiologySampleResult;
import com.labway.lims.pdfreport.api.dto.WordContentDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 微生物样本结果 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface MicrobiologySampleResultConverter {

    @Mapping(target = "wordContent", expression = "java(parseMicrobiologyResult(obj.getWordContent()))")
    MicrobiologySampleResultDto microbiologySampleResultDtoFromTbObj(TbMicrobiologySampleResult obj);

    @Mapping(target = "wordContent", expression = "java(parseMicrobiologyResult(obj.getWordContent()))")
    TbMicrobiologySampleResult tbObjFromMicrobiologySampleResultDto(MicrobiologySampleResultDto obj);

    default WordContentDto parseMicrobiologyResult(String wordResult) {
        return JSON.parseObject(wordResult, WordContentDto.class);
    }

    default String parseMicrobiologyResult(WordContentDto dto) {
        return JSON.toJSONString(dto);
    }
}
