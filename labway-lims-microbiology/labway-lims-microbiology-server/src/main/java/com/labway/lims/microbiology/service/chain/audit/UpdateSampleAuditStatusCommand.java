package com.labway.lims.microbiology.service.chain.audit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UpdateSampleAuditStatusCommand implements Command {

    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private MicrobiologySampleService microbiologySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditMicroSampleContext context = AuditMicroSampleContext.from(c);

        List<MicrobiologySampleDto> samples = context.getSamples();
        LoginUserHandler.User user = context.getUser();

        Set<Long> microbiologySampleIds =
            samples.stream().map(MicrobiologySampleDto::getMicrobiologySampleId).collect(Collectors.toSet());

        // 修改审核人
        final MicrobiologySampleDto sampleDto = new MicrobiologySampleDto();
        sampleDto.setCheckDate(new Date());
        sampleDto.setCheckerId(user.getUserId());
        sampleDto.setCheckerName(user.getNickname());

        microbiologySampleService.updateByMicrobiologySampleIds(sampleDto, microbiologySampleIds);

        // 修改 申请单 样本状态为已审
        Set<Long> applySampleIdList =
            samples.stream().map(MicrobiologySampleDto::getApplySampleId).collect(Collectors.toSet());

        ApplySampleDto updateApplySampleDto = new ApplySampleDto();
        updateApplySampleDto.setStatus(SampleStatusEnum.AUDIT.getCode());

        applySampleService.updateByApplySampleIds(updateApplySampleDto, applySampleIdList);

        return CONTINUE_PROCESSING;
    }
}
