package com.labway.lims.microbiology.service;

import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentGroupInstrumentDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.*;
import com.labway.lims.microbiology.api.dto.*;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleGermService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleResultService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.microbiology.mapper.TbMicrobiologySampleMapper;
import com.labway.lims.microbiology.model.TbMicrobiologySample;
import com.labway.lims.microbiology.service.chain.audit.AuditMicroSampleChain;
import com.labway.lims.microbiology.service.chain.audit.AuditMicroSampleContext;
import com.labway.lims.microbiology.service.chain.audit.BuildReportCommand;
import com.labway.lims.microbiology.service.chain.oneAudit.OneAuditMicroSampleChain;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 14:21
 */
@Slf4j
@DubboService
public class MicrobiologySampleServiceImpl implements MicrobiologySampleService {
    @Resource
    private TbMicrobiologySampleMapper tbMicrobiologySampleMapper;
    @Resource
    private AuditMicroSampleChain auditMicroSampleChain;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private InstrumentGroupInstrumentService instrumentGroupInstrumentService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private EnvDetector envDetector;
    @Resource
    private MicrobiologySampleResultService microbiologySampleResultService;
    @Resource
    private MicrobiologySampleGermService microbiologySampleGermService;
    @Resource
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;
    @Resource
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @DubboReference
    private FinanceSampleLockService financeSampleLockService;
    @Resource
    private OneAuditMicroSampleChain oneAuditMicroSampleChain;
    @DubboReference
    private SystemParamService systemParamService;

    @Resource
    private BuildReportCommand buildReportCommand;

    private static final String ROUTING_KEY = "sample_change_key";

    @Override
    public boolean updateByMicrobiologySampleId(MicrobiologySampleDto dto) {
        final TbMicrobiologySample microbiologySample =
            JSON.parseObject(JSON.toJSONString(dto), TbMicrobiologySample.class);
        final LoginUserHandler.User user = LoginUserHandler.get();
        if (tbMicrobiologySampleMapper.updateById(microbiologySample) < 1) {
            return false;
        }
        log.info("用户 [{}] 更新微生物样本成功 [{}]", user.getNickname(), JSON.toJSONString(dto));
        return true;
    }

    @Override
    public List<MicrobiologySampleDto> selectByTestDate(QueryMicrobiologySampleDto dto) {
        final LambdaQueryWrapper<TbMicrobiologySample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(dto.getGroupId()), TbMicrobiologySample::getGroupId, dto.getGroupId())
            .eq(TbMicrobiologySample::getOrgId, LoginUserHandler.get().getOrgId())
            .eq(Objects.nonNull(dto.getHspOrgId()), TbMicrobiologySample::getHspOrgId, dto.getHspOrgId())
            .eq(StringUtils.isNotBlank(dto.getSampleNo()), TbMicrobiologySample::getSampleNo, dto.getSampleNo())
            .eq(Objects.nonNull(dto.getInstrumentGroupId()), TbMicrobiologySample::getInstrumentGroupId,
                dto.getInstrumentGroupId())
            .ge(Objects.nonNull(dto.getTestDateStart()), TbMicrobiologySample::getTestDate, dto.getTestDateStart())
            .le(Objects.nonNull(dto.getTestDateEnd()), TbMicrobiologySample::getTestDate, dto.getTestDateEnd())
            .orderByAsc(TbMicrobiologySample::getMicrobiologySampleId);
        return tbMicrobiologySampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    public MicrobiologySampleDto selectByMicrobiologySampleId(long microbiologySampleId) {
        return convert(tbMicrobiologySampleMapper.selectById(microbiologySampleId));
    }

    @Override
    public List<MicrobiologySampleDto> selectByMicrobiologySampleIds(Collection<Long> microbiologySampleIds) {

        if (CollectionUtils.isEmpty(microbiologySampleIds)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbMicrobiologySample> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbMicrobiologySample::getMicrobiologySampleId, microbiologySampleIds)
            .eq(TbMicrobiologySample::getIsDelete, YesOrNoEnum.NO.getCode());

        return tbMicrobiologySampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public void auditSamplesChain(Collection<Long> microbiologySampleIds) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditByMicrobiologySampleIds(MicroSampleAuditDto auditDto,String auditType) {

        if (CollectionUtils.isEmpty(auditDto.getMicrobiologySampleIds())) {
            log.info("审核样本id为空");
            return;
        }
        final Collection<Long> microbiologySampleIds = auditDto.getMicrobiologySampleIds();
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        // 微生物一次审核配置
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.MICROBIOLOGY_ONE_AUDIT.getCode(), loginUser.getOrgId());

        final AuditMicroSampleContext context = new AuditMicroSampleContext();
        context.setMicrobiologySampleIds(microbiologySampleIds);
        context.setUser(loginUser);
        context.put(AuditMicroSampleContext.SAMPLE_AUDTI_DTO,auditDto);
        SampleAuditStatusEnum auditStatusEnum = SampleAuditStatusEnum.getByName(auditType);
        context.setAuditType(auditStatusEnum);
        context.setOneCheck(Objects.equals(ObjectUtils.defaultIfNull(param, new SystemParamDto()).getParamValue(), String.valueOf(YesOrNoEnum.YES.getCode())));

        try {
            switch (auditStatusEnum) {
                case ONE_CHECK:
                    if (!oneAuditMicroSampleChain.execute(context)) {
                        throw new IllegalStateException("微生物一审失败");
                    }
                    break;
                case TWO_CHECK:
                    if (!auditMicroSampleChain.execute(context)) {
                        throw new IllegalStateException("新增失败");
                    }
                    break;
                default:
                    if (!auditMicroSampleChain.execute(context)) {
                        throw new IllegalStateException("新增失败");
                    }
            }
        } catch (RuntimeException e) {
            log.error("微生物审核失败 [{}]", microbiologySampleIds, e);
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            log.info("微生物审核 [{}] 耗时\n{}", microbiologySampleIds, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelAuditSamples(MicroSampleCancelAuditDto dto) {
        final MicrobiologySampleDto sample = selectByMicrobiologySampleId(dto.getMicrobiologySampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本信息不存在，请稍后再试");
        }

        if (applySampleService.isDisabled(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", sample.getBarcode()));
        }

        if (applySampleService.isTerminate(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", sample.getBarcode()));
        }

        if (BooleanUtils.isTrue(financeSampleLockService.isSampleLock(sample.getApplySampleId()))) {
            throw new IllegalStateException("该样本已经加锁，不能取消审核");
        }

        // 样本信息
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 申请单样本不存在");
        }


        SampleAuditStatusEnum auditStatusEnum = SampleAuditStatusEnum.getByName(dto.getAuditType());
        boolean isCancelOneCheck = Objects.equals(auditStatusEnum, SampleAuditStatusEnum.CANCEL_ONE_CHECK);

        // 微生物增加取消一审按钮，这里需要判断是否配置了微生物一次审核配置
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.MICROBIOLOGY_ONE_AUDIT.getCode(), LoginUserHandler.get().getOrgId());
        boolean isEnableOnCheck = Objects.equals(ObjectUtils.defaultIfNull(param, new SystemParamDto()).getParamValue(), String.valueOf(YesOrNoEnum.YES.getCode()));

        if (!isEnableOnCheck && isCancelOneCheck){
            throw new IllegalStateException("未配置微生物一次审核配置，不能取消一审!");
        }

        if (isCancelOneCheck){
            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())){
                throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 不是一审状态,不能取消一审！");
            }
        } else {
            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
                throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 不是审核状态,无法取消审核！");
            }
        }

//        if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode()) && !Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) {
//            throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 不是审核状态");
//        }

        // 审核人信息
        checkAuditorInfo(dto);

        // 修改样本状态
        final ApplySampleDto as = new ApplySampleDto();
        as.setStatus(isCancelOneCheck || !isEnableOnCheck  ? SampleStatusEnum.NOT_AUDIT.getCode():SampleStatusEnum.ONE_AUDIT.getCode());
        as.setApplySampleId(sample.getApplySampleId());
        as.setPrintDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        as.setIsPrint(YesOrNoEnum.NO.getCode());
        as.setPrinterName(StringUtils.EMPTY);
        as.setPrinterId(NumberUtils.LONG_ZERO);
        applySampleService.updateByApplySampleId(as);

        // 二审人设置为空
        final MicrobiologySampleDto sampleDto = new MicrobiologySampleDto();
        sampleDto.setMicrobiologySampleId(sample.getMicrobiologySampleId());
        sampleDto.setCheckerId(NumberUtils.LONG_ZERO);
        sampleDto.setCheckerName(StringUtils.EMPTY);
        sampleDto.setCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());

        if (isCancelOneCheck || !isEnableOnCheck ){
            // 一审人置空
            sampleDto.setOneCheckerId(NumberUtils.LONG_ZERO);
            sampleDto.setOneCheckerName(StringUtils.EMPTY);
            sampleDto.setOneCheckerDate(DefaultDateEnum.DEFAULT_DATE.getDate());
            microbiologySampleService.updateByMicrobiologySampleId(sampleDto);
        }

        // 删除报告单
        sampleReportService.deleteBySampleIds(Collections.singleton(dto.getMicrobiologySampleId()));

        final SampleFlowDto flowDto = new SampleFlowDto();
        flowDto.setApplyId(applySample.getApplyId());
        flowDto.setApplySampleId(sample.getApplySampleId());
        flowDto.setBarcode(applySample.getBarcode());
        flowDto.setOperateCode(isCancelOneCheck ? BarcodeFlowEnum.CANCEL_ONE_CHECK.name():BarcodeFlowEnum.CANCEL_SAMPLE_CHECK.name());
        flowDto.setOperateName(isCancelOneCheck ? BarcodeFlowEnum.CANCEL_ONE_CHECK.getDesc():BarcodeFlowEnum.CANCEL_SAMPLE_CHECK.getDesc());
        flowDto.setOperator(LoginUserHandler.get().getNickname());
        flowDto.setContent("取消审核");
        flowDto.setOrgId(LoginUserHandler.get().getOrgId());
        flowDto.setOrgName(LoginUserHandler.get().getOrgName());
        flowDto.setIsDelete(YesOrNoEnum.NO.getCode());
        flowDto.setCreateDate(new Date());
        flowDto.setCreatorId(LoginUserHandler.get().getUserId());
        flowDto.setCreatorName(LoginUserHandler.get().getNickname());
        flowDto.setUpdateDate(new Date());
        flowDto.setUpdaterId(LoginUserHandler.get().getUserId());
        flowDto.setUpdaterName(LoginUserHandler.get().getNickname());

        sampleFlowService.addSampleFlow(flowDto);

        final LoginUserHandler.User user = LoginUserHandler.get();

        threadPoolConfig.getPool().submit(() -> {
            try {
                final ApplyDto apply = applyService.selectByApplyId(sample.getApplyId());
                if (Objects.isNull(apply)) {
                    log.info("申请单不存在发送mq消息失败");
                    return;
                }
                final ApplySampleEventDto event = new ApplySampleEventDto();

                event.setOrgId(user.getOrgId());
                event.setHspOrgId(apply.getHspOrgId());
                event.setHspOrgCode(apply.getHspOrgCode());
                event.setHspOrgName(apply.getHspOrgName());
                event.setApplyId(sample.getApplyId());
                event.setApplySampleId(sample.getApplySampleId());
                event.setBarcode(sample.getBarcode());
                event.setExtras(Map.of("sampleId", String.valueOf(sample.getMicrobiologySampleId()), "outBarcode",
                    String.valueOf(applySample.getOutBarcode()), "sampleNo", String.valueOf(sample.getSampleNo()),
                    "businessCenterOrgCode", envDetector.getBusinessCenterOrgCode()));
                event.setEvent(isCancelOneCheck?ApplySampleEventDto.EventType.CancelOneCheck:ApplySampleEventDto.EventType.CancelTwoCheck);

                final String json = JSON.toJSONString(event);
                rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

                log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", sample.getApplySampleId(), sample.getBarcode(), json,
                    RabbitMQService.EXCHANGE, ROUTING_KEY);
            } catch (Exception e) {
                log.error("发送消息失败", e.getMessage(), e);
            }
        });

    }

    @Override
    public List<MicrobiologySampleDto> selectByApplyId(long applyId) {
        final LambdaQueryWrapper<TbMicrobiologySample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMicrobiologySample::getApplyId, applyId);
        return tbMicrobiologySampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @SneakyThrows
    @Override
    public long twoPick(long applySampleId, long instrumentGroupId, String sampleNo, Date twoPickDate) {
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存,分拣失败");
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在,分拣失败");
        }

        final InstrumentGroupDto instrumentGroup = instrumentGroupService.selectByInstrumentGroupId(instrumentGroupId);
        if (Objects.isNull(instrumentGroup)) {
            throw new IllegalStateException("专业小组不存在");
        }

        final ApplySampleItemDto sampleItem = applySampleItemService.selectByApplySampleId(applySampleId).stream()
            .filter(e -> Objects.equals(e.getItemType(), ItemTypeEnum.MICROBIOLOGY.name()))
            .filter(obj -> Objects.equals(obj.getGroupId(), instrumentGroup.getGroupId())).findFirst().orElse(null);
        if (Objects.isNull(sampleItem)) {
            throw new IllegalStateException("当前样本下没有检验项目,分拣失败");
        }

        final InstrumentGroupInstrumentDto instrument =
            instrumentGroupInstrumentService.selectByInstrumentGroupId(instrumentGroupId).stream()
                .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                    .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                    .findFirst().orElse(null);

        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("微生物检验下没有对应仪器,二次分拣失败");
        }

        final MicrobiologySampleDto sample = new MicrobiologySampleDto();
        sample.setApplySampleId(applySampleId);
        sample.setApplyId(apply.getApplyId());
        sample.setBarcode(applySample.getBarcode());
        sample.setSampleNo(sampleNo);
        sample.setGroupName(applySample.getGroupName());
        sample.setGroupId(applySample.getGroupId());
        sample.setInstrumentId(instrument.getInstrumentId());
        sample.setInstrumentCode(instrument.getInstrumentCode());
        sample.setInstrumentName(instrument.getInstrumentName());
        sample.setInstrumentGroupId(instrumentGroupId);
        sample.setInstrumentGroupName(instrumentGroup.getInstrumentGroupName());
        sample.setTestDate(ObjectUtils.defaultIfNull(twoPickDate, new Date()));
        sample.setCheckerId(NumberUtils.LONG_ZERO);
        sample.setCheckerName(StringUtils.EMPTY);
        sample.setCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        sample.setHspOrgId(apply.getHspOrgId());
        sample.setHspOrgName(apply.getHspOrgName());
        sample.setOrgId(apply.getOrgId());
        sample.setOrgName(apply.getOrgName());

        final long sampleId = addMicrobiologySample(sample);

        log.info("微生物样本二次分拣完成 样本ID [{}],条码号 [{}] 样本号 [{}]", sampleId, sample.getBarcode(), sampleNo);

        return sampleId;

    }

    @Override
    public MicrobiologySampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode())
            || Objects.equals(e.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new IllegalStateException("该条码下存在已审核样本");
        }

        final List<MicrobiologySampleDto> microbiologySamples = selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(microbiologySamples)) {
            throw new IllegalStateException("微生物样本不存在");
        }

        final Set<Long> ids = microbiologySamples.stream().map(MicrobiologySampleDto::getMicrobiologySampleId)
            .collect(Collectors.toSet());

        // 删除样本
        deleteByMicrobiologySampleIds(ids);

        // 删除样本结果
        microbiologySampleResultService.deleteByMicrobiologySampleIds(ids);

        // 删除样本细菌
        microbiologySampleGermService.deleteByMicrobiologySampleIds(ids);

        // 删除样本细菌下的药物
        microbiologyGermMedicineService.deleteByMicrobiologySampleIds(ids);

        // 清空生清单样本的结果备注，样本备注以及检验者
        final ApplySampleDto as = new ApplySampleDto();
        as.setSampleRemark(StringUtils.EMPTY);
        as.setResultRemark(StringUtils.EMPTY);
        as.setTesterId(NumberUtils.LONG_ZERO);
        as.setTesterName(StringUtils.EMPTY);
        applySampleService.updateByApplySampleIds(as, applySampleIds);


        log.info("微生物检验取消二次分拣 样本ID [{}] 条码号 [{}] 操作人 [{}]", ids,applySamples.stream().map(ApplySampleDto::getBarcode)
                .collect(Collectors.toSet()), LoginUserHandler.get().getNickname());

        return new MicrobiologySampleTwoUnPickInfoDto(microbiologySamples.stream()
            .map(e -> new MicrobiologySampleTwoUnPickInfoDto.Sample().setSampleId(e.getMicrobiologySampleId())
                .setSampleNo(e.getSampleNo()).setGroupId(e.getGroupId())
                .setTwoPickDate(
                    applySamples.stream().filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                        .findFirst().map(ApplySampleDto::getTwoPickDate).orElseThrow())
                .setInstrumentGroupId(e.getInstrumentGroupId()))
            .collect(Collectors.toList()));
    }

    @Override
    public boolean deleteByMicrobiologySampleId(long microbiologySampleId) {
        if (tbMicrobiologySampleMapper.deleteById(microbiologySampleId) < 1) {
            return false;
        }
        log.info("用户 [{}] 删除微生物检验样本 [{}] 成功", LoginUserHandler.get().getNickname(), microbiologySampleId);
        return true;
    }

    @Override
    public void deleteByMicrobiologySampleIds(Collection<Long> microbiologySampleIds) {

        if (CollectionUtils.isEmpty(microbiologySampleIds)) {
            return;
        }

        tbMicrobiologySampleMapper.deleteBatchIds(microbiologySampleIds);

        log.info("用户 [{}] 删除微生物检验样本 {} 成功", LoginUserHandler.get().getNickname(), microbiologySampleIds);

    }

    @Override
    public void updateByMicrobiologySampleIds(MicrobiologySampleDto dto, Collection<Long> microbiologySampleIds) {
        if (CollectionUtils.isEmpty(microbiologySampleIds)) {
            return;
        }
        tbMicrobiologySampleMapper.updateByMicrobiologySampleIds(dto, microbiologySampleIds);
    }

    @Override
    public List<FrontMicrobiologySampleInfoDto> selectMicrobiologySamples(MicrobiologySampleCondition condition) {
        return tbMicrobiologySampleMapper.selectMicrobiologySamples(condition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public SampleReportDto rebuildReport(long applySampleId) {
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            log.info("申请单样本不存在 [{}]", applySampleId);
            return null;
        }
        if (BooleanUtils.isFalse(Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode()))) {
            log.info("申请单样本状态不是审核状态 [{}]", applySampleId);
            return null;
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            log.info("申请单不存在 [{}]", applySample.getApplyId());
            return null;
        }

        final MicrobiologySampleDto sample = selectByApplySampleId(applySampleId);
        if (Objects.isNull(sample)) {
            log.info("样本不存在 [{}]", applySampleId);
            return null;
        }

        // 样本检验项目
        List<ApplySampleItemDto> sampleItemDtoList = applySampleItemService.selectByApplySampleId(applySampleId);

        // 结果
        List<MicrobiologySampleResultDto> sampleResultDtoList =
            microbiologySampleResultService.selectByMicrobiologySampleId(sample.getMicrobiologySampleId());
        // 细菌
        List<MicrobiologySampleGermDto> sampleGermDtoList =
            microbiologySampleGermService.selectByMicrobiologySampleId(sample.getMicrobiologySampleId());

        // 抗生素
        List<MicrobiologyGermMedicineDto> germMedicineDtoList =
            microbiologyGermMedicineService.selectByMicrobiologySampleId(sample.getMicrobiologySampleId());

        SampleReportDto sampleReport = buildReportCommand.buildPDF(sample, apply, applySample, sampleItemDtoList,
            sampleResultDtoList, sampleGermDtoList, germMedicineDtoList);

        log.info("重新生成报告: [{}]", sampleReport.getUrl());
        return sampleReport;
    }

    @Override
    public void updateByApplyId(MicrobiologySampleDto microbiologySampleDto) {

        LambdaUpdateWrapper<TbMicrobiologySample> wrapper = Wrappers.lambdaUpdate(TbMicrobiologySample.class)
                .eq(TbMicrobiologySample::getApplyId, microbiologySampleDto.getApplyId())
                .eq(TbMicrobiologySample::getIsDelete,0)
                .set(TbMicrobiologySample::getHspOrgId, microbiologySampleDto.getHspOrgId())
                .set(TbMicrobiologySample::getHspOrgName,microbiologySampleDto.getHspOrgName())
                .set(TbMicrobiologySample::getUpdaterId,microbiologySampleDto.getUpdaterId())
                .set(TbMicrobiologySample::getUpdaterName,microbiologySampleDto.getUpdaterName())
                .set(TbMicrobiologySample::getUpdateDate,microbiologySampleDto.getUpdateDate());
       tbMicrobiologySampleMapper.update(null, wrapper);

    }

    @Override
    public void updateByApplyIds(MicrobiologySampleDto microbiologySampleDto, Collection<Long> applyIds) {

        for (List<Long> item : Lists.partition(new ArrayList<>(applyIds), 500)) {
            LambdaUpdateWrapper<TbMicrobiologySample> wrapper = Wrappers.lambdaUpdate(TbMicrobiologySample.class)
                .in(TbMicrobiologySample::getApplyId, item).eq(TbMicrobiologySample::getIsDelete, 0)
                .set(TbMicrobiologySample::getHspOrgId, microbiologySampleDto.getHspOrgId())
                .set(TbMicrobiologySample::getHspOrgName, microbiologySampleDto.getHspOrgName())
                .set(TbMicrobiologySample::getUpdaterId, microbiologySampleDto.getUpdaterId())
                .set(TbMicrobiologySample::getUpdaterName, microbiologySampleDto.getUpdaterName())
                .set(TbMicrobiologySample::getUpdateDate, microbiologySampleDto.getUpdateDate());
            tbMicrobiologySampleMapper.update(null, wrapper);
        }
    }

    @Override
    public long addMicrobiologySample(MicrobiologySampleDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final TbMicrobiologySample sample = JSON.parseObject(JSON.toJSONString(dto), TbMicrobiologySample.class);
        sample.setMicrobiologySampleId(
            ObjectUtils.defaultIfNull(dto.getMicrobiologySampleId(), snowflakeService.genId()));
        sample.setUpdaterName(user.getNickname());
        sample.setUpdaterId(user.getUserId());
        sample.setUpdateDate(new Date());
        sample.setCreateDate(new Date());
        sample.setCreatorName(user.getNickname());
        sample.setUpdaterId(user.getUserId());
        sample.setCreatorId(user.getUserId());
        sample.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbMicrobiologySampleMapper.insert(sample) < 1) {
            throw new IllegalStateException("添加失败");
        }
        log.info("用户 [{}] 添加微生物检验样本项成功 [{}]", user.getNickname(), JSON.toJSONString(sample));
        return sample.getMicrobiologySampleId();
    }

    @Override
    public MicrobiologySampleDto selectByApplySampleId(long applySampleId) {
        final LambdaQueryWrapper<TbMicrobiologySample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMicrobiologySample::getApplySampleId, applySampleId);
        wrapper.last("limit 1");
        return convert(tbMicrobiologySampleMapper.selectOne(wrapper));
    }

    @Override
    public List<MicrobiologySampleDto> selectByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbMicrobiologySample> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbMicrobiologySample::getApplySampleId, applySampleIds);
        return tbMicrobiologySampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    private void checkAuditorInfo(MicroSampleCancelAuditDto dto) {
        String auditorPwd = dto.getAuditPwd();
        Long auditorId = dto.getAuditId();

        if (Objects.isNull(auditorId)) {
            throw new IllegalStateException("当前审核人为空,请重新选择");
        }
        if (Objects.isNull(auditorPwd)) {
            throw new IllegalStateException("当前审核人密码为空");
        }

        final UserDto userDto = userService.selectByUserId(dto.getAuditId());
        if (Objects.isNull(userDto)) {
            throw new IllegalStateException("审核人账号不存在，请联系管理员");
        }

        if (StringUtils.isBlank(auditorPwd)) {
            throw new IllegalStateException("请输入审核人密码");
        }

        if (!userService.validPassword(userDto.getUsername(), userDto.getPassword(), dto.getAuditPwd())) {
            throw new IllegalStateException("输入的审核人密码错误，请重新输入");
        }

    }

    private MicrobiologySampleDto convert(TbMicrobiologySample sample) {
        if (Objects.isNull(sample)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(sample), MicrobiologySampleDto.class);
    }

    /**
     * 生成 pdf 参数
     *
     * @param item 特检样本
     * @param applyDto 对应申请单
     * @param applySampleDto 对应申请单样本
     * @return 参数
     */
    private PdfReportParamDto getPdfReportParamDto(MicrobiologySampleDto item, ApplyDto applyDto,
        ApplySampleDto applySampleDto, LoginUserHandler.User user) {
        final PdfReportParamDto param = new PdfReportParamDto();
        param.put("apply", Dict.of("masterBarcode", applyDto.getMasterBarcode(), "patientName",
            applyDto.getPatientName(), "patientAge", applyDto.getPatientAge(), "hspOrgName", applyDto.getHspOrgName()));
        param.put("applySample",
            Dict.of("barcode", applySampleDto.getBarcode(), "tubeName", applySampleDto.getTubeName(), "sampleTypeName",
                applySampleDto.getSampleTypeName(), "groupName", applySampleDto.getGroupName(), "onePickerName",
                applySampleDto.getOnePickerName(), "onePickDate", applySampleDto.getOnePickDate(), "twoPickerName",
                applySampleDto.getTwoPickerName(), "twoPickDate", applySampleDto.getTwoPickDate()));
        param.put("sample",
            Dict.of("sampleNo", item.getSampleNo(), "instrumentGroupName", item.getInstrumentGroupName(),
                "instrumentName", item.getInstrumentName(), "testerName", applySampleDto.getTesterName(), "checkerName",
                user.getNickname(), "sampleRemark", applySampleDto.getSampleRemark(), "resultRemark",
                applySampleDto.getResultRemark()));
        return param;
    }

}
