package com.labway.lims.microbiology.vo;

import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 修改 微生物细菌药物
 * 
 * <AUTHOR>
 * @since 2023/5/26 20:36
 */
@Getter
@Setter
public class UpdateMicrobiologyGermMedicineRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 微生物细菌药物 Id
     */
    private Long microbiologyGermMedicineId;

    /**
     * 药物方法
     */
    private String medicineMethod;

    /**
     * 药物结果前缀
     */
    private String formula;

    /**
     * 结果
     */
    private String result;

    /**
     * 敏感度
     *
     * @see MicroSusceptibilityEnum
     */
    private String susceptibility;

}
