package com.labway.lims.microbiology.mapstruct;

import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.model.TbMicrobiologySampleGerm;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 微生物细菌 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface MicrobiologySampleGermConverter {

    MicrobiologySampleGermDto microbiologySampleGermDtoFromTbObj(TbMicrobiologySampleGerm obj);
    MicrobiologySampleGermDto microbiologySampleGermDtoFromTbDto(MicrobiologySampleGermDto dto);

    List<MicrobiologySampleGermDto> microbiologySampleGermDtoListFromTbObjList(List<TbMicrobiologySampleGerm> list);
}
