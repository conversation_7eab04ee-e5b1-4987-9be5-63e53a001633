package com.labway.lims.microbiology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.microbiology.api.dto.FrontMicrobiologySampleInfoDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleCondition;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.model.TbMicrobiologySample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 微生物样本 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbMicrobiologySampleMapper extends BaseMapper<TbMicrobiologySample> {

    int updateByMicrobiologySampleIds(@Param("microbiologySample") MicrobiologySampleDto microbiologySample,
                                      @Param("microbiologySampleIds") Collection<Long> microbiologySampleIds);

    /**
     * 根据条件查询
     */
    List<FrontMicrobiologySampleInfoDto> selectMicrobiologySamples(@Param("condition") MicrobiologySampleCondition condition);
}
