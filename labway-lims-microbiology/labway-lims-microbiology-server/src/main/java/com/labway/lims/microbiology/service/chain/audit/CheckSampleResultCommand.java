package com.labway.lims.microbiology.service.chain.audit;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.ExceptionCodeEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.microbiology.api.dto.MicroSampleAuditDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleResultDto;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 检查样本 结果
 */
@Component
@Slf4j
public class CheckSampleResultCommand implements Command {

    @Resource
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;
    @Resource
    private MicrobiologySampleResultService microbiologySampleResultService;
    @DubboReference
    private SampleAbnormalService sampleAbnormalService;

    @Override
    public boolean execute(Context c) throws Exception {

        AuditMicroSampleContext context = AuditMicroSampleContext.from(c);
        final List<MicrobiologySampleDto> samples = context.getSamples();
        //1.1.3.7 新增异常结果提醒
        // 如果不是强制审核 则做查询判断
        final MicroSampleAuditDto param = context.getAuditDto();
        if (!param.getAuditForce()) {
            List<SampleAbnormalDto> sampleAbnormalDtoList = sampleAbnormalService.selectByBarcodes(samples.stream().map(MicrobiologySampleDto::getBarcode).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(sampleAbnormalDtoList)) {
                List<String> errorMsgList = new ArrayList<>();
                Set<String> barcodes = new HashSet<>();
                sampleAbnormalDtoList.forEach(sampleAbnormalDto -> {
                    if (!barcodes.contains(sampleAbnormalDto.getBarcode())) {
                        errorMsgList.add(String.format("条码号 [%s]的样本存在异常情况！\n", sampleAbnormalDto.getBarcode()));
                        barcodes.add(sampleAbnormalDto.getBarcode());
                    }
                });
                throw new LimsCodeException(ExceptionCodeEnum.ROUTINE_AUDIT.getCode(),
                        JSON.toJSONString(errorMsgList));
            }
        }

//
//        // 检验的微生物样本
//        final List<MicrobiologySampleDto> samples = context.getSamples();
//
//        // 微生物样本ids
//        final Collection<Long> microbiologySampleIds = context.getMicrobiologySampleIds();
//
//        // 微生物 样本结果
//        final List<MicrobiologySampleResultDto> microbiologySampleResultDtos =
//                microbiologySampleResultService.selectByMicrobiologySampleIds(microbiologySampleIds);
//
//        // key: 微生物样本id value: 微生物结果
//        final Map<Long, List<MicrobiologySampleResultDto>> sampleResultByMicrobiologySampleId = microbiologySampleResultDtos
//                .stream().collect(Collectors.groupingBy(MicrobiologySampleResultDto::getMicrobiologySampleId));
//
//        // 校验结果值
//
//        for (MicrobiologySampleDto dto : samples) {
//
//            // 结果
//            final List<MicrobiologySampleResultDto> sampleResultDtos =
//                    sampleResultByMicrobiologySampleId.get(dto.getMicrobiologySampleId());
//
//            if (CollectionUtils.isEmpty(sampleResultDtos)) {
//                continue;
//            }
//
//            for (MicrobiologySampleResultDto r : sampleResultDtos) {
//
//                if (Objects.isNull(r.getResult()) || StringUtils.isBlank(r.getResult())) {
//                    throw new LimsException(String.format("样本号 [%s] 微生物样本结果值为空", dto.getSampleNo()));
//                }
//            }
//
//        }

        return CONTINUE_PROCESSING;
    }

}
