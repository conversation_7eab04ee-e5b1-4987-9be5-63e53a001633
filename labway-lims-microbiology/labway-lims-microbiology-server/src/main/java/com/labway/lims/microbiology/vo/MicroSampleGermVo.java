package com.labway.lims.microbiology.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class MicroSampleGermVo implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private Long microbiologySampleGermId;

    /**
     * 申请单
     */
    private Long applyId;

    /**
     * 样本ID
     */
    private Long microbiologySampleId;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 1:删除 0:未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 细菌
     */
    private Long germId;

    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 细菌名称
     */
    private String germName;

    /**
     * 细菌数量
     */
    private String germCount;

    /**
     * 细菌数量
     */
    private String germCountCode;

    /**
     * 检验方法
     */
    private String testMethod;

    /**
     * 检验方法
     */
    private String testMethodCode;

    /**
     * 细菌备注
     */
    private String germRemark;

    /**
     * 细菌备注
     */
    private String germRemarkCode;

}
