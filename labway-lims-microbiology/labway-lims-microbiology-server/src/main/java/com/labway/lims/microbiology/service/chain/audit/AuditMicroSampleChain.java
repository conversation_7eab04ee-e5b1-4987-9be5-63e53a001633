package com.labway.lims.microbiology.service.chain.audit;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class AuditMicroSampleChain extends ChainBase implements InitializingBean {

    @Resource
    private RequestParamCommand requestParamCommand;
    @Resource
    private CheckSampleResultCommand checkSampleResultCommand;

    @Resource
    private BuildReportCommand buildReportCommand;

    @Resource
    private SampleAuditFlowCommand sampleAuditFlowCommand;

    @Resource
    private UpdateSampleAuditStatusCommand updateSampleAuditStatusCommand;
    @Resource
    private SamplesAuditRabbitMqCommand samplesAuditRabbitMqCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 参数校验
        addCommand(requestParamCommand);

        // 检查样本结果信息 为空 ...
         addCommand(checkSampleResultCommand);

        // 生成样本报告
        addCommand(buildReportCommand);

        // 修改样本的审核状态
        addCommand(updateSampleAuditStatusCommand);

        // 保存流水
        addCommand(sampleAuditFlowCommand);

        //发mq
        addCommand(samplesAuditRabbitMqCommand);

        addCommand(e -> PROCESSING_COMPLETE);
    }
}
