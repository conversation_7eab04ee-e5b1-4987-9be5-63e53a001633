package com.labway.lims.microbiology.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 微生物样本结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Setter
@Getter
public class MicrobiologySampleResultVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long microbiologySampleResultId;

    /**
     * 样本ID
     */
    private Long microbiologySampleId;

    /**
     * 结果
     */
    private String result;

    /**
     * 结果描述
     */
    private String resultDesc;

    /**
     * 结果
     */
    private String resultCode;

    /**
     * 结果属性
     */
    private String resultProperty;

}
