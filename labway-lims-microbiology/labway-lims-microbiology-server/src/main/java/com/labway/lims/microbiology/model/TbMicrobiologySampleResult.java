package com.labway.lims.microbiology.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 微生物样本结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Setter
@Getter
@TableName("tb_microbiology_sample_result")
public class TbMicrobiologySampleResult implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId
    private Long microbiologySampleResultId;

    /**
     * 样本ID
     */
    private Long microbiologySampleId;

    /**
     * 申请单
     */
    private Long applyId;

    /**
     * 结果
     */
    private String result;

    /**
     * 结果描述
     */
    private String resultDesc;

    /**
     * 结果
     */
    private String resultCode;

    /**
     * 结果属性
     */
    private String resultProperty;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    private Integer isDelete;

    /**
     * 申请单样本id
     */
    private Long applySampleId;
}
