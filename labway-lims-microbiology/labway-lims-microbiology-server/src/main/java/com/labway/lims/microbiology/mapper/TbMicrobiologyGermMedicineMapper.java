package com.labway.lims.microbiology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.model.TbMicrobiologyGermMedicine;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 微生物细菌药物 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbMicrobiologyGermMedicineMapper extends BaseMapper<TbMicrobiologyGermMedicine> {

    int addBatch(@Param("medicines") List<TbMicrobiologyGermMedicine> medicines);

    int updateByMicrobiologyGermMedicineIds(@Param("germMedicines") Collection<MicrobiologyGermMedicineDto> germMedicines);
}
