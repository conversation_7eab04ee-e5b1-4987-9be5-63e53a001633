package com.labway.lims.microbiology.service.chain.receive;

import com.labway.lims.base.api.dto.InstrumentGermDto;
import com.labway.lims.base.api.dto.InstrumentMedicineResultDto;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/17 16:19
 */
@Slf4j
@Component
public class ReceiveGermAndMedicineFilterAddAndUpdateCommand implements Filter, Command {

    @Override
    public boolean execute(Context c) throws Exception {
        final ReceiveGermAndMedicineContext context = ReceiveGermAndMedicineContext.from(c);

        final LinkedList<MicrobiologySampleGermDto> addGerms = new LinkedList<>();
        final LinkedList<MicrobiologySampleGermDto> updateGerms = new LinkedList<>();
        final LinkedList<MicrobiologyGermMedicineDto> addMedicines = new LinkedList<>();
        final LinkedList<MicrobiologyGermMedicineDto> deleteMedicines = new LinkedList<>();
        final LinkedList<MicrobiologyGermMedicineDto> oriMedicines = new LinkedList<>();

        final List<MicrobiologySampleGermDto> sampleGermDtos = context.getMicroSampleGerms();
        final List<MicrobiologyGermMedicineDto> instrumentMicroSampleGermMedicines = context.getInstrumentMicroSampleGermMedicines();
        final List<MicrobiologySampleGermDto> instrumentMicroSampleGerms = context.getInstrumentMicroSampleGerms();

        //如果细菌已经存在那么跳过新增细菌 修该细菌下的药物
        if (CollectionUtils.isNotEmpty(sampleGermDtos)) {

            final Map<Long, List<MicrobiologyGermMedicineDto>> medicineMap = context.getMicroSampleGermMedicines()
                    .stream().collect(Collectors.groupingBy(MicrobiologyGermMedicineDto::getGermId));

            final Map<Long, MicrobiologySampleGermDto> germDtoMap = sampleGermDtos.stream().collect(Collectors.toMap(MicrobiologySampleGermDto::getGermId, v -> v, (a, b) -> a));

            final Map<Long, List<MicrobiologyGermMedicineDto>> germMedicineMap = instrumentMicroSampleGermMedicines.stream().collect(Collectors.groupingBy(MicrobiologyGermMedicineDto::getGermId));

            for (MicrobiologySampleGermDto germ : instrumentMicroSampleGerms) {

                //当前样本下已有的药物
                final MicrobiologySampleGermDto sampleGerm = germDtoMap.get(germ.getGermId());

                if (Objects.nonNull(sampleGerm)) {
                    //修改细菌的检验方法为仪器检测方法(MIC)
                    updateGerms.add(sampleGerm);
                    //当前细菌下需要通知的药物
                    final List<MicrobiologyGermMedicineDto> sgs = germMedicineMap.get(germ.getGermId());
                    if (CollectionUtils.isEmpty(sgs)) {
                        continue;
                    }
                    final List<MicrobiologyGermMedicineDto> ms = medicineMap.get(germ.getGermId());
                    //删除之前的药物
                    if (CollectionUtils.isNotEmpty(ms)) {
                        deleteMedicines.addAll(ms);
                    }
                    //只新增仪器传来的药物
                    sgs.forEach(e -> e.setMicrobiologySampleGermId(sampleGerm.getMicrobiologySampleGermId()));
                    addMedicines.addAll(sgs);

                } else {
                    addGerms.add(germ);
                    final List<MicrobiologyGermMedicineDto> adds = germMedicineMap.get(germ.getGermId());
                    if (CollectionUtils.isNotEmpty(adds)) {
                        addMedicines.addAll(adds);
                    }
                }
            }

        } else {
            addGerms.addAll(instrumentMicroSampleGerms);
            addMedicines.addAll(instrumentMicroSampleGermMedicines);
        }

        //获取细菌备注信息
        final Map<String, String> germRemarkMap = new LinkedHashMap<>();
        for (Map.Entry<InstrumentGermDto, List<InstrumentMedicineResultDto>> entry : context.getGermMap().entrySet()) {
            if (Objects.isNull(entry.getValue())) {
                continue;
            }
            for (InstrumentMedicineResultDto result : entry.getValue()) {
                if (StringUtils.equalsIgnoreCase(result.getName(), "GermRemark")) {
                    germRemarkMap.put(entry.getKey().getGermCode(), result.getResult());
                }
            }

        }

        context.put(ReceiveGermAndMedicineContext.ADD_GERMS, addGerms);
        context.put(ReceiveGermAndMedicineContext.UPDATE_GERMS, updateGerms);
        context.put(ReceiveGermAndMedicineContext.ADD_GERM_MEDICINES, addMedicines);
        context.put(ReceiveGermAndMedicineContext.DELETE_GERM_MEDICINES, deleteMedicines);
        context.put(ReceiveGermAndMedicineContext.ORI_GERM_MEDICINES, oriMedicines);
        context.put(ReceiveGermAndMedicineContext.GERM_REMARK_INFO, germRemarkMap);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
