package com.labway.lims.microbiology.service.chain.oneAudit;

import com.labway.lims.microbiology.service.chain.audit.CheckSampleResultCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class OneAuditMicroSampleChain extends ChainBase implements InitializingBean {

    @Resource
    private OneRequestParamCommand oneRequestParamCommand;

    @Resource
    private OneBuildReportCommand oneBuildReportCommand;

    @Resource
    private OneSampleAuditFlowCommand oneSampleAuditFlowCommand;

    @Resource
    private OneUpdateSampleAuditStatusCommand oneUpdateSampleAuditStatusCommand;
    @Resource
    private OneSamplesAuditRabbitMqCommand oneSamplesAuditRabbitMqCommand;

    @Resource
    private CheckSampleResultCommand checkSampleResultCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 参数校验
        addCommand(oneRequestParamCommand);

        // 检查样本结果信息 为空 ...
        addCommand(checkSampleResultCommand);

        // 生成样本报告
//        addCommand(oneBuildReportCommand);

        // 修改样本的审核状态
        addCommand(oneUpdateSampleAuditStatusCommand);

        // 保存流水
        addCommand(oneSampleAuditFlowCommand);

        //发mq
        addCommand(oneSamplesAuditRabbitMqCommand);

        addCommand(e -> PROCESSING_COMPLETE);
    }
}
