package com.labway.lims.microbiology.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySampleItemSourceEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.microbiology.MicroResultPropertyEnum;
import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.MicrobiologySampleEsQuery;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyGermDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyInspectionDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyResultDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultTipDto;
import com.labway.lims.base.api.dto.MedicineGermRelationDto;
import com.labway.lims.base.api.enums.RelationalOperatorEnum;
import com.labway.lims.base.api.service.InstrumentReportItemResultTipService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.MedicineGermRelationService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.microbiology.api.dto.FrontMicrobiologySampleInfoDto;
import com.labway.lims.microbiology.api.dto.MicroSampleAuditDto;
import com.labway.lims.microbiology.api.dto.MicroSampleCancelAuditDto;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleCondition;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleGermService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleResultService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.microbiology.vo.CheckResistantResponseVo;
import com.labway.lims.microbiology.vo.MicroHistoryResultVo;
import com.labway.lims.microbiology.vo.MicroResultRecordComparesVo;
import com.labway.lims.microbiology.vo.MicroRoutineSampleVo;
import com.labway.lims.microbiology.vo.MicroSampleAuditVo;
import com.labway.lims.microbiology.vo.MicroSampleCancelAuditVo;
import com.labway.lims.microbiology.vo.MicrobiologySampleVo;
import com.labway.lims.microbiology.vo.QueryMicrobiologySamplesVo;
import com.labway.lims.microbiology.vo.QueryResultMicrobiologySamplesVo;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 14:13
 */
@RestController
@RequestMapping("/microbiology")
public class MicrobiologySampleController extends BaseController {
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private SampleService sampleService;
    @Resource
    private MicrobiologySampleService microbiologySampleService;
    @Resource
    private MicrobiologySampleGermService microbiologySampleGermService;
    @DubboReference
    private BloodCultureSampleService bloodCultureSampleService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @Resource
    private MicrobiologySampleResultService microbiologySampleResultService;
    @Resource
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;
    @DubboReference
    private InstrumentReportItemResultTipService instrumentReportItemResultTipService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private UserService userService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private MedicineGermRelationService medicineGermRelationService;

    private static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";

    @PostMapping("/samples")
    public Object samples(@RequestBody QueryMicrobiologySamplesVo vo)
            throws ExecutionException, InterruptedException, TimeoutException {

        final MicrobiologySampleCondition condition = new MicrobiologySampleCondition();
        condition.setBeginTestDate(vo.getTestDateStart());
        condition.setEndTestDate(vo.getTestDateEnd());
        condition.setSampleStatus(vo.getSampleStatus());
        condition.setInstrumentGroupId(vo.getInstrumentGroupId());
        condition.setHspOrgId(vo.getHspOrgId());
        condition.setOrgId(LoginUserHandler.get().getOrgId());
        condition.setGroupId(LoginUserHandler.get().getGroupId());

        final List<FrontMicrobiologySampleInfoDto> samples =
                microbiologySampleService.selectMicrobiologySamples(condition);

        // 线程延迟
        final int timeout = 10;

        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }

        // 申请单项目
        final Future<Map<Long, List<ApplySampleItemDto>>> applySampleItems =
                threadPoolConfig.getPool().submit(() -> applySampleItemService.selectByApplySampleIdsAsMap(
                        samples.stream().map(FrontMicrobiologySampleInfoDto::getApplySampleId).collect(Collectors.toSet())));

        List<Long> applySampleId = samples.stream().map(FrontMicrobiologySampleInfoDto::getApplySampleId).collect(Collectors.toList());
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleId);
        Map<Long, ApplySampleDto> applySampleDtoMap = applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));


        // 对比已出的结果和当前样本下的报告项目数量，来显示缺项信息
        final LinkedList<MicroRoutineSampleVo> vos = new LinkedList<>();

        for (FrontMicrobiologySampleInfoDto sample : samples) {
            final MicroRoutineSampleVo v = JSON.parseObject(JSON.toJSONString(sample), MicroRoutineSampleVo.class);
            v.setEnterDate(sample.getCreateDate());
            v.setApplyType(sample.getApplyTypeName());

            final List<ApplySampleItemDto> sampleItems =
                    applySampleItems.get(timeout, TimeUnit.SECONDS).get(sample.getApplySampleId());

            if (CollectionUtils.isNotEmpty(sampleItems)) {
                v.setTestItemName(sampleItems.stream()
                        .filter(obj -> !Objects.equals(obj.getItemSource(),
                                ApplySampleItemSourceEnum.MICROBIOLOGY_SAMPLE_FEE_ITEM.getCode()))
                        .map(ApplySampleItemDto::getTestItemName).collect(Collectors.joining(",")));
            }

            ApplySampleDto applySampleDto = applySampleDtoMap.get(sample.getApplySampleId());
            if (applySampleDto != null) {
                v.setTesterId(applySampleDto.getTesterId());
                v.setTesterName(applySampleDto.getTesterName());
                // 免疫二次分拣标记 # 1.1.3.7
                v.setIsImmunityTwoPick(applySampleDto.getIsImmunityTwoPick());
            }

            vos.add(v);
        }

        return vos;
    }

    @PostMapping("/update")
    public Object update(@RequestBody MicrobiologySampleVo vo) {
        if (Objects.isNull(vo.getMicrobiologySampleId())) {
            throw new IllegalArgumentException("微生物样本不能为空");
        }
        final MicrobiologySampleDto dto = JSON.parseObject(JSON.toJSONString(vo), MicrobiologySampleDto.class);
        microbiologySampleService.updateByMicrobiologySampleId(dto);
        return Map.of("id", vo.getMicrobiologySampleId());
    }

    /**
     * 微生物 审核样本
     */
    @PostMapping("/auditSamples")
    public Object audit(@RequestBody MicroSampleAuditVo vo) {
        Collection<Long> microbiologySampleIds = vo.getMicrobiologySampleIds();
        if (CollectionUtils.isEmpty(microbiologySampleIds)) {
            throw new IllegalArgumentException("样本不能为空");
        }
        final MicroSampleAuditDto dto = JSON.parseObject(JSON.toJSONString(vo), MicroSampleAuditDto.class);
        microbiologySampleService.auditByMicrobiologySampleIds(dto,vo.getAuditType());
        return Collections.emptyMap();
    }

    @PostMapping("/cancelAuditSample")
    public Object cancelAuditSample(@RequestBody MicroSampleCancelAuditVo vo) {
        Objects.requireNonNull(vo, "sampleAuditVo 不能为空");

        if (Objects.isNull(vo.getMicrobiologySampleId())) {
            throw new IllegalArgumentException("样本为空");
        }
        final MicroSampleCancelAuditDto dto = JSON.parseObject(JSON.toJSONString(vo), MicroSampleCancelAuditDto.class);

        microbiologySampleService.cancelAuditSamples(dto);

        return Collections.emptyMap();
    }

    /**
     * 历史结果对比
     */
    @PostMapping("/micro-result-record-compares")
    public Collection<MicroHistoryResultVo> microResultRecordCompares(@RequestBody MicroResultRecordComparesVo vo) {

        if (Objects.isNull(vo.getSampleId())) {
            return Collections.emptyList();
        }

        Long applyId;
        Long applySampleId;
        if (Objects.equals(vo.getItemType(), ItemTypeEnum.ROUTINE.name())) {
            // 传了 常规检验样本id
            final SampleDto sample = sampleService.selectBySampleId(vo.getSampleId());
            if (Objects.isNull(sample)) {
                return Collections.emptyList();
            }
            applyId = sample.getApplyId();
            applySampleId = sample.getApplySampleId();
        } else if (Objects.equals(vo.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())) {
            // 传了 常规检验样本id
            final BloodCultureSampleDto sample = bloodCultureSampleService.selectByBloodCultureSampleId(vo.getSampleId());
            if (Objects.isNull(sample)) {
                return Collections.emptyList();
            }
            applyId = sample.getApplyId();
            applySampleId = sample.getApplySampleId();
        } else {
            final MicrobiologySampleDto sample = microbiologySampleService.selectByMicrobiologySampleId(vo.getSampleId());
            if (Objects.isNull(sample)) {
                return Collections.emptyList();
            }
            applyId = sample.getApplyId();
            applySampleId = sample.getApplySampleId();
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            return Collections.emptyList();
        }

        final ApplyDto apply = applyService.selectByApplyId(applyId);
        if (Objects.isNull(apply)) {
            return Collections.emptyList();
        }

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        query.setIsAudit(YesOrNoEnum.YES.getCode());
        query.setItemTypes(Collections.singleton(ItemTypeEnum.MICROBIOLOGY.name()));
        //query.setStartTwoPickDate(vo.getDateStart());
        //query.setEndTwoPickDate(applySample.getTwoPickDate());

        //处理同人同天
        query.combineOneDayOnePersonParam(apply);

        final List<MicrobiologyInspectionDto> sampleEsModels = elasticSearchSampleService.selectSamples(query)
                .stream().filter(MicrobiologyInspectionDto.class::isInstance).map(e -> (MicrobiologyInspectionDto) e)
                .collect(Collectors.toList());

        sampleEsModels.removeIf(e -> Objects.isNull(e.getSampleId()));

        if (CollectionUtils.isEmpty(sampleEsModels)) {
            return Collections.emptyList();
        }

        // 日期排序 日期越大越靠前
        sampleEsModels.sort((a, b) -> b.getApplySampleId().compareTo(a.getApplySampleId()));

        if (CollectionUtils.isEmpty(sampleEsModels)) {
            return Collections.emptyList();
        }

        final Map<Long, List<ApplySampleItemDto>> applySampleItems = applySampleItemService.selectByApplySampleIdsAsMap(
                sampleEsModels.stream().map(BaseSampleEsModelDto::getApplySampleId).collect(Collectors.toSet()));

        final LinkedHashMap<Long, MicroHistoryResultVo> reportItems = new LinkedHashMap<>();

        for (BaseSampleEsModelDto hs : sampleEsModels) {
            final List<ApplySampleItemDto> sampleItems =
                    ObjectUtils.defaultIfNull(applySampleItems.get(hs.getApplySampleId()), List.of());
            final MicroHistoryResultVo resultVo = new MicroHistoryResultVo();
            resultVo.setMicrobiologySampleId(hs.getSampleId());
            resultVo.setApplySampleId(hs.getApplySampleId());
            resultVo.setSampleNo(hs.getSampleNo());
            resultVo.setBarcode(hs.getBarcode());
            resultVo.setTestDate(DateFormatUtils.format(hs.getTestDate(), "yyyy-MM-dd"));
            resultVo.setTestItemName(
                    sampleItems.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.joining(",")));
            reportItems.put(hs.getSampleId(), resultVo);

        }

        return reportItems.values();
    }

    /**
     * 历史结果对比 数量
     */
    @PostMapping("/micro-result-record-compares-count")
    public Object microResultRecordComparesCount(@RequestBody MicroResultRecordComparesVo vo) {
        return Map.of("count", this.microResultRecordCompares(vo).size());
    }

    @PostMapping("/cancelTwoPick")
    public Object cancelTwoPick(@RequestParam("microbiologySampleId") Long microbiologySampleId) {

        final MicrobiologySampleDto sample =
                microbiologySampleService.selectByMicrobiologySampleId(microbiologySampleId);
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("当前样本不存在");
        }
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }
        if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
            throw new IllegalStateException("只有未审核样本才可以取消二次分拣");
        }
        applySampleService.cancelTwoPick(sample.getBarcode());

        return Collections.emptyMap();
    }

    /**
     * 审核前提示
     */
    @PostMapping("/auditTips")
    public Object auditTips(@RequestBody MicroSampleAuditVo auditVo) {
        if (CollectionUtils.isEmpty(auditVo.getMicrobiologySampleIds())) {
            throw new IllegalArgumentException("所选样本为空");
        }
        // 微生物样本ids
        Collection<Long> microbiologySampleIds = auditVo.getMicrobiologySampleIds();

        // 微生物只有一审
        String auditStatus = SampleAuditStatusEnum.ONE_CHECK.name();
        // 微生物 样本
        List<MicrobiologySampleDto> samples =
                microbiologySampleService.selectByMicrobiologySampleIds(microbiologySampleIds);

        // key: 微生物样本id value:微生物细菌
        final Map<Long, List<MicrobiologySampleGermDto>> germMap =
                microbiologySampleGermService.selectByMicrobiologySampleIds(microbiologySampleIds).stream()
                        .collect(Collectors.groupingBy(MicrobiologySampleGermDto::getMicrobiologySampleId));

        // 微生物 对应所有仪器报告项目
        List<InstrumentReportItemResultTipDto> itemTips = instrumentReportItemResultTipService.selectByInstrumentIds(
                samples.stream().map(MicrobiologySampleDto::getInstrumentId).collect(Collectors.toSet()));

        // key:微生物样本 细菌id value:微生物细菌药物
        final Map<Long, List<MicrobiologyGermMedicineDto>> medicineMap =
                microbiologyGermMedicineService.selectByMicrobiologySampleIds(microbiologySampleIds).stream()
                        .collect(Collectors.groupingBy(MicrobiologyGermMedicineDto::getMicrobiologySampleGermId));

        StringBuilder sb = new StringBuilder();

        for (MicrobiologySampleDto dto : samples) {

            // 对应 微生物细菌
            final List<MicrobiologySampleGermDto> germDtos = germMap.get(dto.getMicrobiologySampleId());
            if (Objects.isNull(germDtos)) {
                // 没有微生物细菌 ？？？
                continue;
            }
            // 对应 微生物细菌药物
            List<MicrobiologyGermMedicineDto> microbiologyGermMedicineDtos =
                    medicineMap.get(dto.getMicrobiologySampleId());
            if (Objects.isNull(microbiologyGermMedicineDtos)) {
                // 没有微生物细菌药物 ？？？
                continue;
            }

            // kye: 微生物细菌 id value:微生物细菌药物
            Map<Long, List<MicrobiologyGermMedicineDto>> germMedicineMap = microbiologyGermMedicineDtos.stream()
                    .collect(Collectors.groupingBy(MicrobiologyGermMedicineDto::getMicrobiologySampleGermId));

            for (MicrobiologySampleGermDto itemDto : germDtos) {

                final List<MicrobiologyGermMedicineDto> medicineDtos =
                        germMedicineMap.get(itemDto.getMicrobiologySampleGermId());

                for (MicrobiologyGermMedicineDto medicineDto : medicineDtos) {

                    BigDecimal decimal = NumberUtils.isParsable(medicineDto.getResult())
                            ? new BigDecimal(medicineDto.getResult()) : null;

                    // 为空 不处理
                    if (Objects.isNull(decimal)) {
                        continue;
                    }

                    // 微生物仪器
                    final InstrumentReportItemDto reportItemDto = instrumentReportItemService
                            .selectByInstrumentIdAndReportItemCode(dto.getInstrumentId(), medicineDto.getMedicineCode());
                    if (Objects.isNull(reportItemDto)) {
                        continue;
                    }
                    final List<InstrumentReportItemResultTipDto> resultTipDtos = itemTips.stream()
                            .filter(e -> ArrayUtils.contains(e.getReportItemCode().split(","), reportItemDto.getReportItemCode())
                                    || Objects.equals(e.getReportItemCode(), "0"))
                            .collect(Collectors.toList());

                    for (InstrumentReportItemResultTipDto tip : resultTipDtos) {

                        if(Objects.equals(auditStatus, tip.getTipType())){
                            this.curTips(dto.getSampleNo(), tip, decimal, sb);
                        }

                    }

                }

            }

        }

        return Map.of("tip", sb.toString());
    }


    private void curTips(String sampleNo, InstrumentReportItemResultTipDto resultTipDto, BigDecimal value, StringBuilder sb) {
        final BigDecimal tipsMaxDecimal = NumberUtils.isParsable(resultTipDto.getFormulaMaxValue()) ? new BigDecimal(resultTipDto.getFormulaMaxValue()) : null;
        if (Objects.isNull(tipsMaxDecimal)) {
            return;
        }
        final BigDecimal tipsMinDecimal = NumberUtils.isParsable(resultTipDto.getFormulaMinValue()) ? new BigDecimal(resultTipDto.getFormulaMinValue()) : null;

        RelationalOperatorEnum operatorMaxEnum = RelationalOperatorEnum.valueOfByOperator(resultTipDto.getFormulaMax());
        RelationalOperatorEnum operatorMinEnum = RelationalOperatorEnum.valueOfByOperator(resultTipDto.getFormulaMin());

        final String tipContent = resultTipDto.getTipContent();

        switch (operatorMaxEnum) {
            case EQ:
                // result = tips
                if (value.compareTo(tipsMaxDecimal) == NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            case LT:
                // result < tips
                if (value.compareTo(tipsMaxDecimal) < NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            case LE:
                // result <= tips
                if (value.compareTo(tipsMaxDecimal) <= NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            case GT:
                // result > tips
                if (value.compareTo(tipsMaxDecimal) > NumberUtils.INTEGER_ZERO) {
                    getMinTips(sampleNo, tipContent, operatorMinEnum, value, tipsMinDecimal, sb);
                }
                break;
            case GE:
                // result >= tips
                if (value.compareTo(tipsMaxDecimal) >= NumberUtils.INTEGER_ZERO) {
                    getMinTips(sampleNo, tipContent, operatorMinEnum, value, tipsMinDecimal, sb);
                }
                break;
        }
    }

    private void getMinTips(String sampleNo, String tipContent, RelationalOperatorEnum operatorMinEnum, BigDecimal value, BigDecimal tipsMinDecimal, StringBuilder sb) {
        switch (operatorMinEnum) {
            case LT:
                if (value.compareTo(tipsMinDecimal) < NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            case LE:
                if (value.compareTo(tipsMinDecimal) <= NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            default:
                sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
        }
    }

    /**
     * 检查 耐药
     */
    @PostMapping("/check-resistant")
    public Object checkResistant(@RequestBody MicroSampleAuditVo auditVo) {
        if (CollectionUtils.isEmpty(auditVo.getMicrobiologySampleIds())) {
            throw new IllegalArgumentException("所选样本为空");
        }
        // 微生物样本ids
        Collection<Long> microbiologySampleIds = auditVo.getMicrobiologySampleIds();

        // 微生物 样本
        Map<Long, MicrobiologySampleDto> sampleById =
                microbiologySampleService.selectByMicrobiologySampleIds(microbiologySampleIds).stream()
                        .collect(Collectors.toMap(MicrobiologySampleDto::getMicrobiologySampleId, Function.identity()));
        if (microbiologySampleIds.stream().anyMatch(x -> !sampleById.containsKey(x))) {
            throw new IllegalArgumentException("存在无效样本");
        }
        // 对应 申请单样本
        Set<Long> applySampleIds =
                sampleById.values().stream().map(MicrobiologySampleDto::getApplySampleId).collect(Collectors.toSet());

        Map<Long, ApplySampleDto> applySampleByApplySampleId = applySampleService.selectByApplySampleIds(applySampleIds)
                .stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));
        // 微生物 细菌
        List<MicrobiologySampleGermDto> sampleGermDtoList =
                microbiologySampleGermService.selectByMicrobiologySampleIds(microbiologySampleIds);

        // 对应细菌菌属ids
        Set<Long> germGenusIds =
                sampleGermDtoList.stream().map(MicrobiologySampleGermDto::getGermGenusId).collect(Collectors.toSet());

        List<CheckResistantResponseVo> targetList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(germGenusIds)) {
            return targetList;
        }

        // key : 微生物样本id value ：样本细菌
        Map<Long, List<MicrobiologySampleGermDto>> sampleGermBySampleId = sampleGermDtoList.stream()
                .collect(Collectors.groupingBy(MicrobiologySampleGermDto::getMicrobiologySampleId));

        List<MedicineGermRelationDto> germRelationDtoList =
                medicineGermRelationService.selectByGermGenusIds(germGenusIds);

        // 需要 耐药提醒的 细菌药物参考范围维护 key : 细菌均属id value.key 检测方法 value.value: 药物ids
        Map<Long,
                Map<String, List<Long>>> groupingByGermGenusId = germRelationDtoList.stream()
                .filter(obj -> Objects.equals(obj.getResistantWarn(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.groupingBy(MedicineGermRelationDto::getGermGenusId,
                        Collectors.groupingBy(MedicineGermRelationDto::getExamMethodCode,
                                Collectors.mapping(MedicineGermRelationDto::getMedicineId, Collectors.toList()))));

        if (groupingByGermGenusId.isEmpty()) {
            return Collections.emptyList();
        }
        // key:微生物样本id value 细菌药物
        Map<Long, List<MicrobiologyGermMedicineDto>> germMedicineBySampleId =
                microbiologyGermMedicineService.selectByMicrobiologySampleIds(microbiologySampleIds).stream()
                        .collect(Collectors.groupingBy(MicrobiologyGermMedicineDto::getMicrobiologySampleId));

        microbiologySampleIds.forEach(x -> {
            MicrobiologySampleDto microbiologySampleDto = sampleById.get(x);
            // 对应细菌
            List<MicrobiologySampleGermDto> sampleGermList =
                    sampleGermBySampleId.get(microbiologySampleDto.getMicrobiologySampleId());
            // 对应细菌药物
            List<MicrobiologyGermMedicineDto> germMedicineDtoList =
                    germMedicineBySampleId.get(microbiologySampleDto.getMicrobiologySampleId());
            if (CollectionUtils.isEmpty(sampleGermList) || CollectionUtils.isEmpty(germMedicineDtoList)) {
                return;
            }
            // 需要 提醒 的细菌
            List<MicrobiologySampleGermDto> filterSampleGermList = sampleGermList.stream()
                    .filter(obj -> groupingByGermGenusId.containsKey(obj.getGermGenusId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterSampleGermList)) {
                return;
            }
            // key: 微生物 细菌id value: 细菌药物
            Map<Long, List<MicrobiologyGermMedicineDto>> germMedicineBySampleGermId = germMedicineDtoList.stream()
                    .collect(Collectors.groupingBy(MicrobiologyGermMedicineDto::getMicrobiologySampleGermId));
            List<String> warningMsg = Lists.newArrayList();
            filterSampleGermList.forEach(item -> {
                // 该细菌 下药物
                List<MicrobiologyGermMedicineDto> germMedicineList =
                        germMedicineBySampleGermId.get(item.getMicrobiologySampleGermId());
                if (CollectionUtils.isEmpty(germMedicineList)) {
                    return;
                }
                // 该细菌 对应细菌菌属 下 需要告警的 药物 key: 检测方法 value： 药物id
                Map<String, List<Long>> medicineIdsByExamMethodCode = groupingByGermGenusId.get(item.getGermGenusId());
                if (Objects.isNull(medicineIdsByExamMethodCode) || medicineIdsByExamMethodCode.isEmpty()) {
                    return;
                }

                // 需要告警 并且出现了耐药 的细菌药物
                List<MicrobiologyGermMedicineDto> warningGermMedicine = germMedicineList.stream()
                        .filter(obj -> MicroSusceptibilityEnum.RESISTANT.getDesc().equals(obj.getSusceptibility()))
                        .filter(obj -> CollectionUtils
                                .isNotEmpty(medicineIdsByExamMethodCode.get(obj.getMedicineMethodCode()))
                                && medicineIdsByExamMethodCode.get(obj.getMedicineMethodCode()).contains(obj.getMedicineId()))
                        .collect(Collectors.toList());
                warningGermMedicine.forEach(tem -> {
                    warningMsg.add(String.format("细菌[%s]对应的抗生素[%s]敏感度为耐药", item.getGermName(), tem.getMedicineName()));
                });
            });
            // 此样本 不需要 告警
            if (CollectionUtils.isEmpty(warningMsg)) {
                return;
            }

            ApplySampleDto applySampleDto = applySampleByApplySampleId.get(microbiologySampleDto.getApplySampleId());

            CheckResistantResponseVo temp = new CheckResistantResponseVo();
            temp.setMicrobiologySampleId(microbiologySampleDto.getMicrobiologySampleId());
            temp.setSampleNo(microbiologySampleDto.getSampleNo());
            if (Objects.nonNull(applySampleDto)) {
                temp.setBarcode(applySampleDto.getBarcode());
            }
            temp.setWarningMsg(warningMsg);
            targetList.add(temp);
        });

        return targetList;
    }

    /**
     * -----------样本结果查询------------
     */
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    @PostMapping("/samples-es")
    public Object getSamples(@RequestBody QueryResultMicrobiologySamplesVo queryVo) {

        // 转为es 查询条件
        SampleEsQuery query = getSampleEsQuery(queryVo);

        final List<MicrobiologyInspectionDto> sampleEsModels = elasticSearchSampleService.selectSamples(query)
                // 微生物样本
                .stream().filter(MicrobiologyInspectionDto.class::isInstance)
                // 转成微生物样本
                .map(e -> (MicrobiologyInspectionDto) e).collect(Collectors.toList());

        return selectMicroRoutineSampleVo(sampleEsModels, queryVo.getResultProperty());

    }


    @PostMapping("/export-samples-es")
    public Object exportSampleEs(@RequestBody QueryResultMicrobiologySamplesVo queryVo) {

        // 转为es 查询条件
        SampleEsQuery query = getSampleEsQuery(queryVo);

        final List<MicrobiologyInspectionDto> sampleEsModels = elasticSearchSampleService.selectSamples(query)
                // 微生物样本
                .stream().filter(MicrobiologyInspectionDto.class::isInstance)
                // 转成微生物样本
                .map(e -> (MicrobiologyInspectionDto) e).collect(Collectors.toList());

        //处理 过滤阴阳性
        if (StringUtils.isNotBlank(queryVo.getResultProperty())) {
            sampleEsModels.removeIf(model -> {
                //默认没有结果筛选的时候 是需要加进来的
                boolean needAdd = StringUtils.isBlank(queryVo.getResultProperty());
                //处理 过滤阴阳性
                if (StringUtils.isNotBlank(queryVo.getResultProperty())) {
                    //阳性:培养出细菌 || 微生物检验结果中是阳性结果的数据
                    if (MicroResultPropertyEnum.POSITIVE.getDesc().equals(queryVo.getResultProperty())) {
                        needAdd = CollectionUtils.isNotEmpty(model.getGerms());
                        if (CollectionUtils.isEmpty(model.getGerms()) && CollectionUtils.isNotEmpty(model.getResults())) {
                            needAdd = model.getResults().stream().anyMatch(e -> MicroResultPropertyEnum.POSITIVE.getDesc().equals(e.getResultProperty()));
                        }
                    }

                    //阴性：未培养出细菌的样本 && 微生物检验结果中结果属性中为阴性的结果
                    if (MicroResultPropertyEnum.NEGATIVE.getDesc().equals(queryVo.getResultProperty())) {
                        needAdd = CollectionUtils.isEmpty(model.getGerms()) && (CollectionUtils.isNotEmpty(model.getResults())
                                && model.getResults().stream().anyMatch(e -> MicroResultPropertyEnum.NEGATIVE.getDesc().equals(e.getResultProperty())));
                    }

                    //涂片：微生物检验结果中为涂片的结果
                    if (MicroResultPropertyEnum.SMEAR.getDesc().equals(queryVo.getResultProperty())) {
                        needAdd = CollectionUtils.isNotEmpty(model.getResults())
                                && model.getResults().stream().anyMatch(e -> MicroResultPropertyEnum.SMEAR.getDesc().equals(e.getResultProperty()));
                    }
                }

                return !needAdd;
            });
        }

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("微生物检验结果查询.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(exportMicroSamples(sampleEsModels));

    }


    /**
     * 查询 检验结果列表 信息
     */
    private List<MicroRoutineSampleVo> selectMicroRoutineSampleVo(List<MicrobiologyInspectionDto> sampleEsModels, String resultPorperty) {
        final List<MicroRoutineSampleVo> targetList = Lists.newArrayListWithCapacity(sampleEsModels.size());

        for (MicrobiologyInspectionDto model : sampleEsModels) {
            //默认没有结果筛选的时候 是需要加进来的
            boolean needAdd = StringUtils.isBlank(resultPorperty);
            //处理 过滤阴阳性
            if (StringUtils.isNotBlank(resultPorperty)) {
                //阳性:培养出细菌 || 微生物检验结果中是阳性结果的数据
                if (MicroResultPropertyEnum.POSITIVE.getDesc().equals(resultPorperty)) {
                    needAdd = CollectionUtils.isNotEmpty(model.getGerms());
                    if (CollectionUtils.isEmpty(model.getGerms()) && CollectionUtils.isNotEmpty(model.getResults())) {
                        needAdd = model.getResults().stream().anyMatch(e -> MicroResultPropertyEnum.POSITIVE.getDesc().equals(e.getResultProperty()));
                    }
                }

                //阴性：未培养出细菌的样本 && 微生物检验结果中结果属性中为阴性的结果
                if (MicroResultPropertyEnum.NEGATIVE.getDesc().equals(resultPorperty)) {
                    needAdd = CollectionUtils.isEmpty(model.getGerms()) && (CollectionUtils.isNotEmpty(model.getResults())
                            && model.getResults().stream().anyMatch(e -> MicroResultPropertyEnum.NEGATIVE.getDesc().equals(e.getResultProperty())));
                }

                //涂片：微生物检验结果中为涂片的结果
                if (MicroResultPropertyEnum.SMEAR.getDesc().equals(resultPorperty)) {
                    needAdd = CollectionUtils.isNotEmpty(model.getResults())
                            && model.getResults().stream().anyMatch(e -> MicroResultPropertyEnum.SMEAR.getDesc().equals(e.getResultProperty()));
                }
            }
            if (!needAdd) {
                continue;
            }
            final MicroRoutineSampleVo sampleVo = new MicroRoutineSampleVo();
            sampleVo.setMicrobiologySampleId(model.getSampleId());
            sampleVo.setApplySampleId(model.getApplySampleId());
            sampleVo.setApplyId(model.getApplyId());
            sampleVo.setStatus(model.getSampleStatus());
            sampleVo.setBarcode(model.getBarcode());
            sampleVo.setSampleNo(model.getSampleNo());
            sampleVo.setGroupId(model.getGroupId());
            sampleVo.setGroupName(model.getGroupName());
            sampleVo.setInstrumentGroupId(model.getInstrumentGroupId());
            sampleVo.setInstrumentGroupName(model.getInstrumentGroupName());
            sampleVo.setTestDate(model.getTestDate());
            sampleVo.setEnterDate(model.getApplyDate());
            sampleVo.setCheckerId(model.getFinalCheckerId());
            sampleVo.setCheckerName(model.getFinalCheckerName());
            sampleVo.setCheckDate(model.getFinalCheckDate());
            sampleVo.setHspOrgId(model.getHspOrgId());
            sampleVo.setHspOrgName(model.getHspOrgName());
            sampleVo.setUrgent(model.getUrgent());
            sampleVo.setTestItemName(StringUtils.EMPTY);
            if (CollectionUtils.isNotEmpty(model.getTestItems())) {
                List<BaseSampleEsModelDto.TestItem> testItems = model.getTestItems().stream()
                        .filter(
                                obj -> Objects.equals(obj.getItemSource(), ApplySampleItemSourceEnum.DEFAULT_SOURCE.getCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(testItems)) {
                    // 正常只有1个
                    sampleVo.setTestItemName(testItems.get(NumberUtils.INTEGER_ZERO).getTestItemName());
                }
            }
            sampleVo.setPatientName(model.getPatientName());
            sampleVo.setIsPrint(model.getIsPrint());
            sampleVo.setApplyType(model.getApplyTypeName());
            targetList.add(sampleVo);
        }

        return targetList;
    }

    private byte[] exportMicroSamples(List<MicrobiologyInspectionDto> sampleEsModels) {
        // 头的策略
        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("Calibri");
        headWriteFont.setFontHeightInPoints((short) 11);
        headCellStyle.setWriteFont(headWriteFont);

        // 内容策略
        WriteCellStyle contentCellStyle = new WriteCellStyle();
        contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentCellStyle.setBorderLeft(BorderStyle.THIN);
        contentCellStyle.setBorderTop(BorderStyle.THIN);
        contentCellStyle.setBorderRight(BorderStyle.THIN);
        contentCellStyle.setBorderBottom(BorderStyle.THIN);

        HorizontalCellStyleStrategy excelStyle = new HorizontalCellStyleStrategy(headCellStyle, contentCellStyle);

        ByteArrayOutputStream out = new ByteArrayOutputStream();

        try (ExcelWriter excelWriter =
                     EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle)
                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build()) {
            List<List<Object>> list0 = Lists.newArrayList();
            List<List<String>> header0 = Lists.newArrayList();

            // 设置表头
            List<String> headList =
                    Lists.newArrayList("状态", "样本号", "检验目的", "样本类型", "姓名", "条码号", "检验日期", "审核时间",
                            "送检机构", "就诊类型", "科室", "送检医生", "送检时间", "审核人", "检验结果", "细菌");
            for (String item : headList) {
                header0.add(List.of(item));
            }

            for (MicrobiologyInspectionDto model : sampleEsModels) {
                // 设置 合并规则 放置数据
                fillExcelContent(list0, headList, model);
            }

            // 获取sheet对象
            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "微生物结果信息").head(header0).needHead(Boolean.TRUE).build();

            excelWriter.write(list0, sheet0);
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        }
        return out.toByteArray();
    }

    /**
     * 填充 excel 内容数据
     *
     * @param list0       excel 数据
     * @param headList    表头
     * @param contentData 内容来源
     */
    private void fillExcelContent(List<List<Object>> list0, List<String> headList,
                                  MicrobiologyInspectionDto contentData) {
        List<Object> content = Lists.newArrayListWithCapacity(headList.size());
        if (Objects.isNull(contentData.getSampleStatus())) {
            content.add(StringUtils.EMPTY);
        } else {
            content.add(SampleStatusEnum.getStatusByCode(contentData.getSampleStatus()).getDesc());
        }
        content.add(contentData.getSampleNo());
        String testName = "";
        if (CollectionUtils.isNotEmpty(contentData.getTestItems())) {
            List<BaseSampleEsModelDto.TestItem> testItems = contentData.getTestItems().stream()
                    .filter(
                            obj -> Objects.equals(obj.getItemSource(), ApplySampleItemSourceEnum.DEFAULT_SOURCE.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(testItems)) {
                // 正常只有1个
                testName = testItems.get(NumberUtils.INTEGER_ZERO).getTestItemName();
            }
        }
        //检验目的
        content.add(testName);
        //样本类型
        content.add(contentData.getSampleTypeName());
        content.add(contentData.getPatientName());
        content.add(contentData.getBarcode());
        content.add(DateFormatUtils.format(contentData.getTestDate(), DATE_PATTERN));
        content.add(DateFormatUtils.format(contentData.getTwoCheckDate(), DATE_PATTERN));
        content.add(contentData.getHspOrgName());
        content.add(contentData.getApplyTypeName());
        content.add(contentData.getDept());
        content.add(contentData.getSendDoctorName());
        content.add(DateFormatUtils.format(contentData.getSignDate(), DATE_PATTERN));
        content.add(contentData.getTwoCheckerName());
        String results = "";
        String germsName = "";

        // 检验结果列
        if (CollectionUtils.isNotEmpty(contentData.getResults())) {
            List<String> resultList = contentData.getResults().stream()
                    .map(MicrobiologyResultDto::getResult)
                    .collect(Collectors.toList());
            results = String.join(",", resultList);
        }

        // 细菌名称列
        if (CollectionUtils.isNotEmpty(contentData.getGerms())) {
            List<String> germNameList = contentData.getGerms().stream()
                    .map(MicrobiologyGermDto::getGermName)
                    .collect(Collectors.toList());
            germsName = String.join(",", germNameList);
        }
        content.add(results);
        content.add(germsName);
        list0.add(content);
    }

    /**
     * 获取结果查询 es 条件
     */
    private SampleEsQuery getSampleEsQuery(QueryResultMicrobiologySamplesVo queryVo) {
        LoginUserHandler.User user = LoginUserHandler.get();

        final MicrobiologySampleEsQuery query = new MicrobiologySampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(NumberUtils.INTEGER_ONE);
        // 当前用户所属专业组
        query.setGroupIds(Collections.singleton(user.getGroupId()));

        // 已审
        query.setIsAudit(YesOrNoEnum.YES.getCode());

        // 只查询遗传样本
        query.setItemTypes(Collections.singleton(ItemTypeEnum.MICROBIOLOGY.name()));

        // 检验日期
        if (Objects.nonNull(queryVo.getTestDateStart()) && Objects.nonNull(queryVo.getTestDateEnd())) {
            query.setStartTestDate(queryVo.getTestDateStart());
            query.setEndTestDate(queryVo.getTestDateEnd());
        }

        // 审核日期
        if (Objects.nonNull(queryVo.getCheckDateStart()) && Objects.nonNull(queryVo.getCheckDateEnd())) {
            query.setStartFinalCheckDate(queryVo.getCheckDateStart());
            query.setEndFinalCheckDate(queryVo.getCheckDateEnd());
        }

        // 检验者ID
        if (Objects.nonNull(queryVo.getTesterId())) {
            query.setTesterId(queryVo.getTesterId());
        }

        // 审核人ID
        if (Objects.nonNull(queryVo.getCheckerId())) {
            query.setFinalCheckerIds(Collections.singleton(queryVo.getCheckerId()));
        }

        // 检验项目
        if (Objects.nonNull(queryVo.getTestItemId())) {
            query.setTestItemIds(Collections.singleton(queryVo.getTestItemId()));
        }

        // 送检机构
        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Collections.singleton(queryVo.getHspOrgId()));
        }

        // 姓名
        if (StringUtils.isNotBlank(queryVo.getPatientName())) {
            query.setPatientName(queryVo.getPatientName());
        }

        // 性别
        if (Objects.nonNull(queryVo.getPatientSex())
                && !Objects.equals(queryVo.getPatientSex(), SexEnum.DEFAULT.getCode())) {
            query.setPatientSex(queryVo.getPatientSex());
        }
        // 门诊/住院号
        if (StringUtils.isNotBlank(queryVo.getPatientVisitCard())) {
            query.setPatientVisitCard(queryVo.getPatientVisitCard());
        }

        if (StringUtils.isNotBlank(queryVo.getApplyType())) {
            query.setApplyTypes(Collections.singleton(queryVo.getApplyType()));
        }

        if (StringUtils.isNotBlank(queryVo.getBarcode())) {
            query.setBarcodes(Collections.singleton(queryVo.getBarcode()));
        }
        if (CollectionUtils.isNotEmpty(queryVo.getResultCodes())) {
            query.setResultCodes(queryVo.getResultCodes());
        }
        //样本过滤 结果属性过滤
        if (StringUtils.isNotBlank(queryVo.getResultProperty())) {
            query.setResultProperty(queryVo.getResultProperty());
        }

        if (CollectionUtils.isNotEmpty(queryVo.getGermIds())) {
            query.setGermIds(queryVo.getGermIds());
        }
        if (CollectionUtils.isNotEmpty(queryVo.getMedicineIds())) {
            query.setMedicineIds(queryVo.getMedicineIds());
        }
        if (CollectionUtils.isNotEmpty(queryVo.getGermRemarkCodes())) {
            query.setGermRemarkCodes(queryVo.getGermRemarkCodes());
        }
        if (StringUtils.isNotBlank(queryVo.getSampleRemarkLike())) {
            query.setSampleRemark(queryVo.getSampleRemarkLike());
        }

        query.setSorts(
                Lists.newArrayList(SampleEsQuery.Sort.builder().filedName("finalCheckDate").order("ASC").build()));
        return query;
    }

}
