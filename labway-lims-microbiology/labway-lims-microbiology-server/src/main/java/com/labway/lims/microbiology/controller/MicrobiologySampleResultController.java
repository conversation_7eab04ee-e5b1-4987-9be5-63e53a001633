package com.labway.lims.microbiology.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleResultDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleResultService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.microbiology.vo.MicroSampleBatchResultItemVo;
import com.labway.lims.microbiology.vo.MicroSampleBatchResultVo;
import com.labway.lims.microbiology.vo.MicroSampleResultVo;
import com.labway.lims.microbiology.vo.MicrobiologySampleResultVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 16:38
 */
@RestController
@RequestMapping("/microbiology-sample-result")
public class MicrobiologySampleResultController extends BaseController {
    @Resource
    private MicrobiologySampleResultService microbiologySampleResultService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @DubboReference
    private ApplySampleService applySampleService;

    @PostMapping("/results")
    public Object results(@RequestParam("microbiologySampleId") Long microbiologySampleId) {
        if (Objects.isNull(microbiologySampleId)) {
            return Collections.emptyList();
        }

        return JSON.parseArray(
            JSON.toJSONString(microbiologySampleResultService.selectByMicrobiologySampleId(microbiologySampleId)),
            MicroSampleResultVo.class);
    }

    @PostMapping("/add")
    public Object addResult(@RequestBody MicrobiologySampleResultVo vo) {
        if (Objects.isNull(vo.getMicrobiologySampleId())) {
            throw new IllegalArgumentException("微生物样本不能为空");
        }
        final MicrobiologySampleDto sampleDto =
            microbiologySampleService.selectByMicrobiologySampleId(vo.getMicrobiologySampleId());
        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("微生物样本不存在");
        }
        final MicrobiologySampleResultDto dto =
            JSON.parseObject(JSON.toJSONString(vo), MicrobiologySampleResultDto.class);
        dto.setApplyId(sampleDto.getApplyId());
        dto.setApplySampleId(sampleDto.getApplySampleId());
        return Map.of("id", microbiologySampleResultService.addMicroResult(dto));
    }

    @PostMapping("/update")
    public Object updateMicroResult(@RequestBody MicrobiologySampleResultVo vo) {
        if (Objects.isNull(vo.getMicrobiologySampleResultId())) {
            throw new IllegalArgumentException("样本结果ID不能为空");
        }
        if (Objects.isNull(vo.getMicrobiologySampleId())) {
            throw new IllegalArgumentException("样本ID不能为空");
        }
        final MicrobiologySampleDto sampleDto =
            microbiologySampleService.selectByMicrobiologySampleId(vo.getMicrobiologySampleId());

        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("当前样本不存在,修改失败");
        }
        // 修改 检验结果
        if (Objects.nonNull(vo.getResultCode())) {
            MicrobiologySampleResultDto dto = new MicrobiologySampleResultDto();
            dto.setMicrobiologySampleResultId(vo.getMicrobiologySampleResultId());
            dto.setResultCode(vo.getResultCode());
            dto.setResult(StringUtils.defaultString(vo.getResult()));
            dto.setResultProperty(vo.getResultProperty());
            microbiologySampleResultService.updateById(dto);
        }

        // 修改 检验结果描述
        if (Objects.nonNull(vo.getResultDesc())) {
            MicrobiologySampleResultDto dto = new MicrobiologySampleResultDto();
            dto.setMicrobiologySampleResultId(vo.getMicrobiologySampleResultId());
            dto.setResultDesc(vo.getResultDesc());
            microbiologySampleResultService.updateById(dto);
        }
        return Collections.emptyMap();
    }

    @PostMapping("/delete")
    public Object delete(@RequestParam("microbiologySampleResultId") Long microbiologySampleResultId) {
        final MicrobiologySampleResultDto microbiologySampleResult =
            microbiologySampleResultService.selectByMicrobiologySampleResultId(microbiologySampleResultId);
        if (Objects.nonNull(microbiologySampleResult)) {
            final MicrobiologySampleDto sample = microbiologySampleService
                .selectByMicrobiologySampleId(microbiologySampleResult.getMicrobiologySampleId());
            if (Objects.nonNull(sample)) {
                final SampleFlowDto flow = new SampleFlowDto();
                flow.setSampleFlowId(snowflakeService.genId());
                flow.setApplyId(sample.getApplyId());
                flow.setApplySampleId(sample.getApplySampleId());
                flow.setBarcode(sample.getBarcode());
                flow.setOperateCode(BarcodeFlowEnum.DELETE_RESULT.name());
                flow.setOperateName(BarcodeFlowEnum.DELETE_RESULT.getDesc());
                final LoginUserHandler.User user = LoginUserHandler.get();
                flow.setOperator(user.getNickname());
                flow.setOperatorId(user.getUserId());
                flow.setContent("删除结果");
                flow.setCreateDate(new Date());
                flow.setUpdateDate(new Date());
                flow.setUpdaterId(user.getUserId());
                flow.setUpdaterName(user.getNickname());
                flow.setOrgId(user.getOrgId());
                flow.setOrgName(user.getOrgName());
                flow.setIsDelete(YesOrNoEnum.NO.getCode());
                sampleFlowService.addSampleFlow(flow);
            }
        }
        microbiologySampleResultService.deleteById(microbiologySampleResultId);

        return Collections.emptyMap();
    }

    /**
     * 批量录入结果
     * @param vo
     * @return
     */
    @PostMapping("/add-batch-result")
    public Object addBatchResult(@RequestBody MicroSampleBatchResultVo vo) {
        if (CollectionUtils.isEmpty(vo.getMicrobiologySampleIds())) {
            throw new IllegalArgumentException("微生物样本不能为空");
        }

        if (CollectionUtils.isEmpty(vo.getResultList())){
            throw new IllegalArgumentException("微生物样本结果不能为空");
        }

        List<MicrobiologySampleDto> microbiologySampleDtos = microbiologySampleService.selectByMicrobiologySampleIds(vo.getMicrobiologySampleIds());
        List<Long> applySampleIds = microbiologySampleDtos
                .stream().map(MicrobiologySampleDto::getApplySampleId).collect(Collectors.toList());

        Map<Long, MicrobiologySampleDto> microbiologySampleDtoMap = microbiologySampleDtos.stream()
                .collect(Collectors.toMap(MicrobiologySampleDto::getMicrobiologySampleId, Function.identity(), (a, b) -> b));

        List<String> auditBarcodes = applySampleService.selectByApplySampleIds(applySampleIds)
                .stream().filter(e -> !Objects.equals(e.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode()))
                .map(ApplySampleDto::getBarcode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(auditBarcodes)) {
            throw new IllegalStateException(String.format("条码[%s]非未审状态", CollUtil.join(auditBarcodes, ",")));
        }

        LoginUserHandler.User user = LoginUserHandler.get();
        //生成要插入的每一个条目
        List<MicrobiologySampleResultDto> results = new ArrayList<>();
        for (Long sampleId:vo.getMicrobiologySampleIds()){
            MicrobiologySampleDto microbiologySampleDto = microbiologySampleDtoMap.get(sampleId);
            for (MicroSampleBatchResultItemVo itemVo: vo.getResultList()){
                MicrobiologySampleResultDto resultDto = new MicrobiologySampleResultDto();
                BeanUtils.copyProperties(itemVo,resultDto);
                resultDto.setMicrobiologySampleId(sampleId);
                resultDto.setCreateDate(new Date());
                resultDto.setCreatorId(user.getUserId());
                resultDto.setCreatorName(user.getNickname());
                resultDto.setUpdateDate(new Date());
                resultDto.setUpdaterId(user.getUserId());
                resultDto.setUpdaterName(user.getNickname());
                resultDto.setIsDelete(YesOrNoEnum.NO.getCode());
                resultDto.setApplyId(microbiologySampleDto.getApplyId());
                resultDto.setApplySampleId(microbiologySampleDto.getApplySampleId());
                results.add(resultDto);
            }
        }
        microbiologySampleResultService.addBatchMicroResult(vo.getMicrobiologySampleIds(),results);
        return Collections.emptyMap();
    }

}
