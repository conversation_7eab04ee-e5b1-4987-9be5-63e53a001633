<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.lims.microbiology.mapper.TbMicrobiologySampleResultMapper">

    <insert id="addBatch" parameterType="java.util.List">
        insert into tb_microbiology_sample_result (
        microbiology_sample_result_id,
        microbiology_sample_id,
        apply_id,
        apply_sample_id,
        result,
        result_code,
        result_desc,
        result_property,
        is_delete,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.microbiologySampleResultId},
            #{item.microbiologySampleId},
            #{item.applyId},
            #{item.applySampleId},
            #{item.result},
            #{item.resultCode},
            #{item.resultDesc},
            #{item.resultProperty},
            #{item.isDelete},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName}
            )
        </foreach>
    </insert>

</mapper>
