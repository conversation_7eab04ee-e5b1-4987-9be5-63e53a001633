<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.lims.microbiology.mapper.TbMicrobiologyGermMedicineMapper">
    <insert id="addBatch" parameterType="java.util.List">
        insert into tb_microbiology_germ_medicine (
        microbiology_germ_medicine_id,
        microbiology_sample_id,
        apply_id,
        microbiology_sample_germ_id,
        test_item_id,
        test_item_code,
        test_item_name,
        germ_id,
        germ_code,
        germ_name,
        medicine_id,
        medicine_code,
        medicine_name,
        medicine_method,
        result,
        susceptibility,
        unit,
        range,
        update_date,
        create_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        is_delete,
        formula,
        apply_sample_id,
        medicine_method_code,
        "group",
        fold_point_scope
        ) values
        <foreach collection="medicines" item="item" separator=",">
            (
            #{item.microbiologyGermMedicineId},
            #{item.microbiologySampleId},
            #{item.applyId},
            #{item.microbiologySampleGermId},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.testItemName},
            #{item.germId},
            #{item.germCode},
            #{item.germName},
            #{item.medicineId},
            #{item.medicineCode},
            #{item.medicineName},
            #{item.medicineMethod},
            #{item.result},
            #{item.susceptibility},
            #{item.unit},
            #{item.range},
            #{item.updateDate},
            #{item.createDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete},
            #{item.formula},
            #{item.applySampleId},
            #{item.medicineMethodCode},
            #{item.group},
            #{item.foldPointScope}
            )
        </foreach>
    </insert>

    <update id="updateByMicrobiologyGermMedicineIds">
        update tb_microbiology_germ_medicine
        <trim prefix="set" suffixOverrides=",">

            <trim prefix="result =case" suffix="end,">
                <foreach collection="germMedicines" item="i" index="index">
                    <if test="i.result!=null">
                        when microbiology_germ_medicine_id=#{i.microbiologyGermMedicineId} then #{i.result}
                    </if>
                </foreach>
            </trim>

            <trim prefix="susceptibility =case" suffix="end,">
                <foreach collection="germMedicines" item="i" index="index">
                    <if test="i.susceptibility!=null">
                        when microbiology_germ_medicine_id=#{i.microbiologyGermMedicineId} then #{i.susceptibility}
                    </if>
                </foreach>
            </trim>

            <trim prefix="formula =case" suffix="end,">
                <foreach collection="germMedicines" item="i" index="index">
                    <if test="i.formula!=null">
                        when microbiology_germ_medicine_id=#{i.microbiologyGermMedicineId} then #{i.formula}
                    </if>
                </foreach>
            </trim>
        </trim>
        where microbiology_germ_medicine_id in
        <foreach collection="germMedicines" item="i" index="index" open="(" separator="," close=")">
            #{i.microbiologyGermMedicineId}
        </foreach>
    </update>
</mapper>
