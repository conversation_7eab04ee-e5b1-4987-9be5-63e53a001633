<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.lims.microbiology.mapper.TbMicrobiologySampleMapper">
    <update id="updateByMicrobiologySampleIds">
        update tb_microbiology_sample
        <set>
            <if test="microbiologySample.checkDate != null">
                check_date = #{microbiologySample.checkDate},
            </if>
            <if test="microbiologySample.checkerName != null">
                checker_name = #{microbiologySample.checkerName},
            </if>
            <if test="microbiologySample.checkerId != null">
                checker_id = #{microbiologySample.checkerId},
            </if>
            <if test="microbiologySample.testDate != null">
                test_date = #{microbiologySample.testDate},
            </if>

            <if test="microbiologySample.oneCheckerDate != null">
                one_checker_date = #{microbiologySample.oneCheckerDate},
            </if>
            <if test="microbiologySample.oneCheckerName != null">
                one_checker_name = #{microbiologySample.oneCheckerName},
            </if>
            <if test="microbiologySample.oneCheckerId != null">
                one_checker_id = #{microbiologySample.oneCheckerId},
            </if>
        </set>
        where microbiology_sample_id in
        <foreach collection="microbiologySampleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectMicrobiologySamples"
            resultType="com.labway.lims.microbiology.api.dto.FrontMicrobiologySampleInfoDto">
        select
        tms.microbiology_sample_id,
        tas.apply_sample_id,
        ta.apply_id,
        tas.status,
        tas.barcode,
        tms.sample_no,
        tas.group_id,
        tas.group_name,
        tms.instrument_group_id,
        tms.instrument_group_name,
        tms.instrument_id,
        tms.instrument_name,
        tms.test_date,
        ta.create_date,
        tms.checker_id,
        tms.checker_name,
        tms.check_date,
        tms.one_checker_name,
        tms.one_checker_id,
        tms.one_checker_date,
        ta.hsp_org_id,
        ta.hsp_org_name,
        tas.urgent,
        ta.patient_name,
        tas.is_print,
        ta.apply_type_name,
        ta.apply_type_code,
        ta.original_org_code,
        ta.original_org_name
        from tb_microbiology_sample tms
        inner join tb_apply_sample tas on tms.apply_sample_id = tas.apply_sample_id
        inner join tb_apply ta on tas.apply_id = ta.apply_id
        where tms.test_date >= #{condition.beginTestDate}
        and tms.test_date &lt;= #{condition.endTestDate}
        and tms.is_delete = 0
        and tas.status != 99
        and ta.org_id = #{condition.orgId}
        <if test="condition.instrumentGroupId != null">
            and tms.instrument_group_id = #{condition.instrumentGroupId}
        </if>
        <if test="condition.hspOrgId != null">
            and tms.hsp_org_id = #{condition.hspOrgId}
        </if>
        <if test="condition.groupId != null">
            and tas.group_id = #{condition.groupId}
        </if>
        <if test="condition.sampleStatus == 'NOT_AUDIT'">
            and tas.status = 10
        </if>
        <if test="condition.sampleStatus == 'ONE_AUDIT'">
            and tas.status = 20
        </if>
        <if test="condition.sampleStatus == 'AUDIT'">
            and tas.status = 30
        </if>
        order by microbiology_sample_id asc

    </select>
</mapper>
