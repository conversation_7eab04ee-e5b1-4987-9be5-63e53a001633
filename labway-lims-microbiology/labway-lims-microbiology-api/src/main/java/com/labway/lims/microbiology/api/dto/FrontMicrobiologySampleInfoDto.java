package com.labway.lims.microbiology.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 前端页面对象
 */
@Getter
@Setter
public class FrontMicrobiologySampleInfoDto implements Serializable {

    /**
     * ID
     */
    private Long microbiologySampleId;

    /**
     * 样本申请单ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 样本状态
     */
    private Integer status;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 仪器信息名称
     */
    private String instrumentName;

    /**
     * 仪器信息
     */
    private Long instrumentId;

    /**
     * 检验日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date testDate;

    /**
     * 录入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;

    /**
     * 审核人ID
     */
    private Long checkerId;

    /**
     * 一次审核工号
     */
    private String checkerName;


    /**
     * 审核日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date checkDate;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 样本是否加急 (1急诊 0不急)
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 是否打印
     */
    private Integer isPrint;

    /**
     * 就诊类型
     */
    private String applyTypeCode;
    /**
     * 就诊类型
     */
    private String applyTypeName;
    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;

    /**
     * 一次审核id
     */
    private Long oneCheckerId;

    /**
     * 一次审核人
     */
    private String oneCheckerName;

    /**
     * 一次审核时间
     */
    private Date oneCheckerDate;

}
