package com.labway.lims.microbiology.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.microbiology.MicroTestMethodEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 微生物细菌
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class MicrobiologySampleGermDto implements Serializable {
    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private Long microbiologySampleGermId;

    /**
     * 申请单
     */
    private Long applyId;

    /**
     * 样本ID
     */
    private Long microbiologySampleId;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 细菌
     */
    private Long germId;

    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 细菌名称
     */
    private String germName;

    /**
     * 细菌数量
     */
    private String germCount;

    /**
     * 细菌数量
     */
    private String germCountCode;

    /**
     * 检验方法
     * @see MicroTestMethodEnum
     */
    private String testMethod;

    /**
     * 检验方法
     *
     * @see MicroTestMethodEnum
     */
    private String testMethodCode;

    /**
     * 细菌备注
     */
    private String germRemark;

    /**
     * 细菌备注
     */
    private String germRemarkCode;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 1:删除 0:未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * applySampleId
     */
    private Long applySampleId;

    /**
     * 细菌菌属ID
     */
    private Long germGenusId;

    /**
     * 细菌菌属编码
     */
    private String germGenusCode;
}
