package com.labway.lims.microbiology.api.service;

import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.microbiology.api.dto.*;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/11 14:21
 */
public interface MicrobiologySampleService {
    boolean updateByMicrobiologySampleId(MicrobiologySampleDto dto);

    /**
     * 根据检验日期查询所有微生物样本
     */
    List<MicrobiologySampleDto> selectByTestDate(QueryMicrobiologySampleDto dto);

    /**
     * 根据 microbiologySampleId 查询
     */
    @Nullable
    MicrobiologySampleDto selectByMicrobiologySampleId(long microbiologySampleId);

    /**
     * microbiologySampleIds
     */
    List<MicrobiologySampleDto> selectByMicrobiologySampleIds(Collection<Long> microbiologySampleIds);

    /**
     * 审核
     */
    void auditSamplesChain(Collection<Long> microbiologySampleIds);

    /**
     * 审核
     * 
     * @param sampleAuditDto 微生物
     *
     * @see com.labway.lims.api.enums.routine.SampleAuditStatusEnum
     * @param auditType 审核类型
     */
    void auditByMicrobiologySampleIds(MicroSampleAuditDto sampleAuditDto, String auditType);

    /**
     * 取消审核
     */
    void cancelAuditSamples(MicroSampleCancelAuditDto dto);

    /**
     * 根据申请单查询样本
     */
    List<MicrobiologySampleDto> selectByApplyId(long applyId);

    /**
     * 二次分拣
     */
    long twoPick(long applySampleId, long instrumentGroupId, String sampleNo, Date twoPickDate);

    /**
     * 取消二次分拣
     */
    MicrobiologySampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds);

    /**
     * 添加样本
     */
    long addMicrobiologySample(MicrobiologySampleDto dto);

    /**
     * 根据applySampleId查询
     */
    @Nullable
    MicrobiologySampleDto selectByApplySampleId(long applySampleId);

    /**
     * 根据applySampleId查询
     */
    List<MicrobiologySampleDto> selectByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据 microbiologySampleId 删除
     */
    boolean deleteByMicrobiologySampleId(long microbiologySampleId);

    /**
     * 根据 microbiologySampleId 删除
     */
    void deleteByMicrobiologySampleIds(Collection<Long> microbiologySampleIds);

    void updateByMicrobiologySampleIds(MicrobiologySampleDto dto, Collection<Long> microbiologySampleIds);

    /**
     * 根据条件查询，提供给前端页面
     */
    List<FrontMicrobiologySampleInfoDto> selectMicrobiologySamples(MicrobiologySampleCondition condition);

    /**
     * 重新生成报告
     *
     * @return
     */
    SampleReportDto rebuildReport(long applySampleId);

    /**
     *  根据applyId修改样本信息
     * @param microbiologySampleDto
     */
    void updateByApplyId(MicrobiologySampleDto microbiologySampleDto);

    void updateByApplyIds(MicrobiologySampleDto microbiologySampleDto, Collection<Long> applyIds);
}
