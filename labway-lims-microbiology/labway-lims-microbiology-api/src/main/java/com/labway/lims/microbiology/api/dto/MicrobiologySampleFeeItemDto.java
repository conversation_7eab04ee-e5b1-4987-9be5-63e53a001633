
package com.labway.lims.microbiology.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 微生物费用明细项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Getter
@Setter
public class MicrobiologySampleFeeItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 微生物费用明细项目id
     */
    private Long microbiologySampleFeeItemId;

    /**
     * 微生物样本id
     */
    private Long microbiologySampleId;

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 收费价格
     */
    private BigDecimal feePrice;
    /**
     * 收费次数
     */
    private Integer feeCount;
    /**
     * 实际收费价格
     */
    private BigDecimal actualFeePrice;
    /**
     * 是否免单
     *
     * @see YesOrNoEnum
     */
    private Integer isFree;
    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 1:删除,0:未删
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;

}
