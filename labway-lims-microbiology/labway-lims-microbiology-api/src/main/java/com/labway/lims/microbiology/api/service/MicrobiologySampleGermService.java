package com.labway.lims.microbiology.api.service;

import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermUpdateDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/11 14:49
 */
public interface MicrobiologySampleGermService {
    /**
     * 根据 microbiologySampleId 查询微生物细菌
     */
    List<MicrobiologySampleGermDto> selectByMicrobiologySampleId(long microbiologySampleId);

    List<MicrobiologySampleGermDto> selectByMicrobiologySampleIds(Collection<Long> microbiologySampleIds);

    /**
     * 微生物样本添加细菌
     */
    long addGerm(MicrobiologySampleGermDto dto);

    void addGermBatch(Collection<MicrobiologySampleGermDto> dtos);

    /**
     * 根据ID修改
     */
    boolean updateById(MicrobiologySampleGermDto dto);

    /**
     * 根据id删除
     */
    boolean deleteByMicrobiologySampleGermId(long microbiologySampleGermId);
    void deleteByMicrobiologySampleGermIds(Collection<Long> microbiologySampleGermIds);

    /**
     * 根据 microbiologySampleId 删除
     */
    boolean deleteByMicrobiologySampleId(long microbiologySampleId);

    /**
     * 根据 microbiologySampleId 删除
     */
    void deleteByMicrobiologySampleIds(Collection<Long> microbiologySampleIds);

    @Nullable
    MicrobiologySampleGermDto selectByMicrobiologySampleGermId(long microbiologySampleGermId);

    @Nullable
    List<MicrobiologySampleGermDto> selectByMicrobiologySampleGermIds(List<Long> microbiologySampleGermIds);

    /**
     * 对应 微生物样本下 是否 已存在 此细菌
     *
     * @param microbiologySampleId            微生物样本
     * @param germId                          细菌id
     * @param excludeMicrobiologySampleGermId 排除的微生物细菌id
     */
    boolean isExitMicrobiologySampleGerm(long microbiologySampleId, long germId, long excludeMicrobiologySampleGermId);

    /**
     * 更新 微生物样本细菌
     *
     * @param sampleDto     对应微生物样本
     * @param sampleGermDto 对应微生物细菌
     * @param updateDto     需要更新的信息
     */
    void updateSampleGerm(MicrobiologySampleDto sampleDto, MicrobiologySampleGermDto sampleGermDto,
                          MicrobiologySampleGermUpdateDto updateDto);

    void updateByMicrobiologySampleGermIds(Collection<MicrobiologySampleGermDto> germs);
}
