package com.labway.lims.microbiology.api.dto;

import com.labway.lims.api.enums.apply.ApplySampleItemSourceEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 微生物样本费用项目 信息 Dto
 *
 * <AUTHOR>
 * @since 2023/8/28 10:55
 */
@Getter
@Setter
public class MicrobiologySampleFeeItemInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 微生物样本id
     */
    private Long microbiologySampleId;

    /**
     * 检验项目id
     */
    private Long testItemId;
    /**
     * 检验项目code
     */
    private String testItemCode;
    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 收费价格
     */
    private BigDecimal feePrice;
    /**
     * 收费次数
     */
    private Integer feeCount;
    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 项目来源:0默认，1微生物费用项目
     *
     * @see ApplySampleItemSourceEnum
     */
    private Integer itemSource;

    // ----------检验项目 上-------



    /**
     * 收费金额
     */
    private BigDecimal feeAmount;
}
