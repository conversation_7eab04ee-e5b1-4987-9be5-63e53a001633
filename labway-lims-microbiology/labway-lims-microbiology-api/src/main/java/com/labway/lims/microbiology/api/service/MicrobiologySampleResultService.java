package com.labway.lims.microbiology.api.service;

import com.labway.lims.microbiology.api.dto.MicrobiologySampleResultDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 微生物样本结果
 *
 * <AUTHOR>
 * @since 2023/4/11 14:48
 */
public interface MicrobiologySampleResultService {

    /**
     * 根据 microbiologySampleId 查询微生物结果
     */
    List<MicrobiologySampleResultDto> selectByMicrobiologySampleId(long microbiologySampleId);

    /**
     * 根据微生物样本id 查询结果
     */
    List<MicrobiologySampleResultDto> selectByMicrobiologySampleIds(Collection<Long> microbiologySampleIds);

    /**
     * 添加微生物结果
     */
    long addMicroResult(MicrobiologySampleResultDto dto);

    void addBatchMicroResult(Collection<Long> sampleIds,Collection<MicrobiologySampleResultDto> results);

    /**
     * 根据ID修改
     */
    boolean updateById(MicrobiologySampleResultDto dto);

    /**
     * 根据 microbiologySampleResultId 删除
     */
    boolean deleteById(long microbiologySampleResultId);

    /**
     * 根据 microbiologySampleId 删除
     */
    boolean deleteByMicrobiologySampleId(long microbiologySampleId);

    /**
     * 根据 microbiologySampleId 删除
     */
    void deleteByMicrobiologySampleIds(Collection<Long> microbiologySampleIds);

    @Nullable
    MicrobiologySampleResultDto selectByMicrobiologySampleResultId(long microbiologySampleResultId);
}
