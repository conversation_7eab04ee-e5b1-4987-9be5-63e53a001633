package com.labway.lims.microbiology.api.dto;

import com.labway.lims.api.enums.microbiology.MicroFormulaEnum;
import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 修改 微生物药物结果 信息
 * 
 * <AUTHOR>
 * @since 2023/4/13 16:24
 */
@Getter
@Setter
public class UpdateMicroGermMedicineDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 药物方法
     */
    private String medicineMethod;

    /**
     * 药物结果前缀
     *
     * @see MicroFormulaEnum
     */
    private String formula;

    /**
     * 结果
     */
    private String result;

    /**
     * 敏感度
     *
     * @see MicroSusceptibilityEnum
     */
    private String susceptibility;

    /**
     * 结果时间
     */
    private Date resultDate;
    /**
     * 来源
     */
    private SaveResultSourceEnum source;
}
