package com.labway.lims.microbiology.api.service;

import com.labway.lims.base.api.dto.MedicineDto;
import com.labway.lims.base.api.dto.MedicineGermRelationDto;
import com.labway.lims.microbiology.api.dto.*;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/11 14:49
 */
public interface MicrobiologyGermMedicineService {
    /**
     * microbiologySampleGermId
     */
    List<MicrobiologyGermMedicineDto> selectByMicrobiologySampleGermId(long microbiologySampleGermId);

    List<MicrobiologyGermMedicineDto> selectByMicrobiologySampleGermIds(Collection<Long> microbiologySampleGermIds);

    /**
     * 添加药物
     */
    long addGermMedicine(MicrobiologyGermMedicineDto dto);

    /**
     * 修改药物
     */
    long updateByMicrobiologyGermMedicineId(MicrobiologyGermMedicineDto dto);

    void updateByMicrobiologyGermMedicineIds(Collection<MicrobiologyGermMedicineDto> germMedicines);

    /**
     * 批量添加
     */
    Collection<Long> addGermMedicineBatch(Collection<MicrobiologyGermMedicineDto> dtos);

    /**
     * 更新 微生物 药物结果
     *
     * @param sample          微生物样本
     * @param germMedicineDto 微生物细菌药物
     * @param updateDto       更新信息
     */
    MicrobiologyGermMedicineUpdateResultDto updateMedicineResult(MicrobiologySampleDto sample,
                                                                 MicrobiologySampleGermDto sampleGermDto, MicrobiologyGermMedicineDto germMedicineDto,
                                                                 List<MedicineGermRelationDto> relationDtos, MicrobiologyGermMedicineUpdateDto updateDto);

    /**
     * 根据 microbiologySampleId 查询
     */
    List<MicrobiologyGermMedicineDto> selectByMicrobiologySampleId(long microbiologySampleId);

    /**
     * 保存药物 -新增类型
     */
    long saveMedicineWhenAdd(MicrobiologySampleDto sampleDto, MicrobiologySampleGermDto sampleGermDto,
                             MedicineDto medicineDto);

    /**
     * 保存药物 -更新类型
     */
    long saveMedicineWhenUpdate(MicrobiologySampleDto sampleDto, MicrobiologySampleGermDto sampleGermDto,
                                MedicineDto medicineDto, List<MedicineGermRelationDto> relationDtos,
                                MicrobiologyGermMedicineDto germMedicineDto);

    @Nullable
    MicrobiologyGermMedicineDto selectByMicrobiologyGermMedicineId(long microbiologyGermMedicineId);

    List<MicrobiologyGermMedicineDto> selectByMicrobiologyGermMedicineIds(Collection<Long> microbiologyGermMedicineIds);

    void deleteByIds(Collection<Long> ids);

    /**
     * 根据 microbiologySampleId 删除
     */
    boolean deleteByMicrobiologySampleId(long microbiologySampleId);

    /**
     * 微生物样本接收结果
     */
    void receive(InstrumentResultReceiverDto dto);

    /**
     * 根据 microbiologySampleId 删除
     */
    void deleteByMicrobiologySampleIds(Collection<Long> microbiologySampleIds);

    /**
     * 根据样本细菌ID删除其下的所有药物
     */
    void deleteByMicrobiologySampleGermId(long microbiologySampleGermId);

    void deleteByMicrobiologySampleGermIds(Collection<Long> microbiologySampleGermIds);

    List<MicrobiologyGermMedicineDto> selectByMicrobiologySampleIds(Collection<Long> microbiologySampleIds);
}
