package com.labway.lims.microbiology.api.dto;

import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 微生物细菌药物更新
 *
 * <AUTHOR>
 * @since 2023/7/6 19:40
 */
@Getter
@Setter
public class MicrobiologyGermMedicineUpdateDto implements Serializable {
    /**
     * 微生物细菌药物 Id
     */
    private Long microbiologyGermMedicineId;

    /**
     * 药物方法
     */
    private String medicineMethod;

    /**
     * 药物结果前缀
     */
    private String formula;

    /**
     * 结果
     */
    private String result;

    /**
     * 敏感度
     *
     * @see MicroSusceptibilityEnum
     */
    private String susceptibility;

    /**
     * 修改类型 : 1 方法、2 结果值前缀 3 结果值 4敏感度 5分组
     */
    private Integer updateType;

    /**
     * 分组
     */
    private String group;
}
