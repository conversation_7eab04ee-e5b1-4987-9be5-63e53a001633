package com.labway.lims.microbiology.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 微生物 细菌 信息更新
 * 
 * <AUTHOR>
 * @since 2023/7/6 17:38
 */
@Getter
@Setter
public class MicrobiologySampleGermUpdateDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long microbiologySampleGermId;
    /**
     * 细菌
     */
    private Long germId;
    /**
     * 细菌数量
     */
    private String germCount;
    /**
     * 细菌数量
     */
    private String germCountCode;

    /**
     * 细菌备注
     */
    private String germRemark;
    /**
     * 细菌备注
     */
    private String germRemarkCode;

    /**
     * 检验方法
     */
    private String testMethod;
    /**
     * 检验方法
     */
    private String testMethodCode;

    /**
     * 修改类型 : 1 细菌 2细菌数量 3 细菌备注 4 检测方法
     */
    private Integer updateType;
}
