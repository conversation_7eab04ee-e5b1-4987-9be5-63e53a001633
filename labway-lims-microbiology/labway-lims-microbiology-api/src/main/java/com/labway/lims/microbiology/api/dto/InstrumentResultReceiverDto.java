package com.labway.lims.microbiology.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.base.api.dto.InstrumentGermDto;
import com.labway.lims.base.api.dto.InstrumentMedicineResultDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class InstrumentResultReceiverDto implements Serializable {

    /**
     * 样本号/条码号
     */
    private String barcode;

    /**
     * ID
     */
    private Long microbiologySampleId;

    /**
     * 检验时间 yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date date;

    /**
     * 检测的仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器
     */
    private Long instrumentId;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组code
     */
    private String groupCode;

    /**
     * orgId
     */
    private Long orgId;

    /**
     * 接收的细菌和药物
     */
    private Map<InstrumentGermDto, List<InstrumentMedicineResultDto>> germMap;

}
