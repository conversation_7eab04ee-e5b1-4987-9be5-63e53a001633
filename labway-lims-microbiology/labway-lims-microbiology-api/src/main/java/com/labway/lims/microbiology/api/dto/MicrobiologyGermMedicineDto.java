package com.labway.lims.microbiology.api.dto;

import com.alibaba.excel.util.StringUtils;
import com.labway.lims.api.enums.microbiology.MicroFormulaEnum;
import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.base.api.dto.MedicineGermRelationDto;
import com.labway.lims.base.api.service.MedicineGermRelationService;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 微生物细菌药物
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class MicrobiologyGermMedicineDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long microbiologyGermMedicineId;

    /**
     * 样本ID
     */
    private Long microbiologySampleId;

    /**
     * 申请单
     */
    private Long applyId;

    /**
     * applySampleId
     */
    private Long applySampleId;

    /**
     * 样本细菌
     */
    private Long microbiologySampleGermId;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 细菌
     */
    private Long germId;

    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 细菌名称
     */
    private String germName;

    /**
     * 药物
     */
    private Long medicineId;

    /**
     * 药物编码
     */
    private String medicineCode;

    /**
     * 药物名称
     */
    @Compare("药物名称")
    private String medicineName;

    /**
     * 药物方法
     */
    @Compare("方法")
    private String medicineMethod;

    /**
     * 药物检测方法 code
     */
    private String medicineMethodCode;
    /**
     * 药物结果前缀
     * 
     * @see MicroFormulaEnum
     */
    @Compare("结果值前缀")
    private String formula;

    /**
     * 结果
     */
    @Compare("结果值")
    private String result;

    /**
     * 敏感度
     *
     * @see MicroSusceptibilityEnum
     */
    @Compare("敏感度")
    private String susceptibility;

    /**
     * 单位
     */
    @Compare("单位")
    private String unit;

    /**
     * 参考值
     */
    @Compare("参考值")
    private String range;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    private Integer isDelete;
    /**
     * 分组
     */
    @Compare("分组")
    private String group;

    /**
     * 报告顺序
     */
    private Integer  reportSort;
    /**
     * 折点范围
     */
    private String foldPointScope;


    public void setRange(String range) {
        this.range = range;
    }

    /**
     * 设置参考值范围
     *
     * @param referValueMin 范围最小值，字符串类型。
     * @param referValueMax 范围最大值，字符串类型。
     */
    public void setRange(String referValueMin, String referValueMax) {
        if (StringUtils.isNotBlank(referValueMin) && StringUtils.isNotBlank(referValueMax)) {
            // 都不为空时，使用"-"拼接
            this.range = referValueMin + "-" + referValueMax;
        } else if (StringUtils.isNotBlank(referValueMin)) {
            // 只有referValueMin不为空
            this.range = referValueMin;
        } else if (StringUtils.isNotBlank(referValueMax)) {
            // 只有referValueMax不为空
            this.range = referValueMax;
        } else {
            this.range = Strings.EMPTY;
        }
    }


    public static Map<Long, List<MicrobiologyGermMedicineDto>> sortGermMedicine(
            List<MicrobiologySampleGermDto> sampleGermDtoList, List<MicrobiologyGermMedicineDto> germMedicineDtoList, MedicineGermRelationService medicineGermRelationService) {

        Map<Long, List<MicrobiologyGermMedicineDto>> sortedGermMedicineMap = new HashMap<>();

        // 细菌 对应 药物List
        Map<Long, List<MicrobiologyGermMedicineDto>> germMedicineMap =
                germMedicineDtoList.stream().collect(Collectors.groupingBy(MicrobiologyGermMedicineDto::getGermId));

        // 细菌 对应 菌属ID
        Map<Long, Long> germGenusIdMap = sampleGermDtoList.stream()
                .collect(Collectors.toMap(MicrobiologySampleGermDto::getGermId, MicrobiologySampleGermDto::getGermGenusId, (a, b) -> a));

        // 查询细菌菌属下的药物关系
        List<Long> germGenusIds = sampleGermDtoList.stream().map(MicrobiologySampleGermDto::getGermGenusId).collect(Collectors.toList());
        List<MedicineGermRelationDto> medicineGermRelationDtos = medicineGermRelationService.selectByGermGenusIds(germGenusIds);
        // 根据菌属ID分组
        Map<Long, List<MedicineGermRelationDto>> groupingByGermGenusId =
                medicineGermRelationDtos.stream().collect(Collectors.groupingBy(MedicineGermRelationDto::getGermGenusId));

        for (Map.Entry<Long, List<MicrobiologyGermMedicineDto>> entry : germMedicineMap.entrySet()) {
            Long germId = entry.getKey();
            List<MicrobiologyGermMedicineDto> germMedicineDtos = entry.getValue();

            // 菌属下所有药物
            Map<Long, List<MedicineGermRelationDto>> groupingByMedicineId =
                    groupingByGermGenusId.getOrDefault(germGenusIdMap.get(germId), Collections.emptyList()).stream()
                            .collect(Collectors.groupingBy(MedicineGermRelationDto::getMedicineId));

            // 先把排序字段值塞进去
            for (MicrobiologyGermMedicineDto germMedicineDto : germMedicineDtos) {
                Long medicineId = germMedicineDto.getMedicineId();
                List<MedicineGermRelationDto> relationDtos = groupingByMedicineId.get(medicineId);
                if (CollectionUtils.isNotEmpty(relationDtos)) {
                    // 同一个药物多个检验方法去最小一个
                    Optional<Integer> min = relationDtos.stream().filter(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getReportSort()))
                            .map(e -> Integer.parseInt(e.getReportSort())).min(Comparator.comparing(Integer::intValue));
                    germMedicineDto.setReportSort(min.orElse(null));
                }
            }

            // 进行排序
            Comparator<MicrobiologyGermMedicineDto> comparator = (o1, o2) -> {
                Integer s1 = o1.getReportSort();
                Integer s2 = o2.getReportSort();
                if (Objects.nonNull(s1) && Objects.nonNull(s2)) {
                    return s1 - s2;
                } else if (Objects.nonNull(s1)) {
                    return -1;
                } else if (Objects.nonNull(s2)) {
                    return 1;
                } else {
                    return 0;
                }
            };
            germMedicineDtos.sort(comparator.thenComparing(MicrobiologyGermMedicineDto::getMedicineCode));
            for (int i = 0; i < germMedicineDtos.size(); i++) {
                germMedicineDtos.get(i).setReportSort(i);
            }
            sortedGermMedicineMap.put(germId, germMedicineDtos);
        }

        return sortedGermMedicineMap;
    }


}
