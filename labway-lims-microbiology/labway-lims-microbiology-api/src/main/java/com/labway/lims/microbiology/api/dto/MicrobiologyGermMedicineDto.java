package com.labway.lims.microbiology.api.dto;

import com.alibaba.excel.util.StringUtils;
import com.labway.lims.api.enums.microbiology.MicroFormulaEnum;
import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 微生物细菌药物
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class MicrobiologyGermMedicineDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long microbiologyGermMedicineId;

    /**
     * 样本ID
     */
    private Long microbiologySampleId;

    /**
     * 申请单
     */
    private Long applyId;

    /**
     * applySampleId
     */
    private Long applySampleId;

    /**
     * 样本细菌
     */
    private Long microbiologySampleGermId;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 细菌
     */
    private Long germId;

    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 细菌名称
     */
    private String germName;

    /**
     * 药物
     */
    private Long medicineId;

    /**
     * 药物编码
     */
    private String medicineCode;

    /**
     * 药物名称
     */
    @Compare("药物名称")
    private String medicineName;

    /**
     * 药物方法
     */
    @Compare("方法")
    private String medicineMethod;

    /**
     * 药物检测方法 code
     */
    private String medicineMethodCode;
    /**
     * 药物结果前缀
     * 
     * @see MicroFormulaEnum
     */
    @Compare("结果值前缀")
    private String formula;

    /**
     * 结果
     */
    @Compare("结果值")
    private String result;

    /**
     * 敏感度
     *
     * @see MicroSusceptibilityEnum
     */
    @Compare("敏感度")
    private String susceptibility;

    /**
     * 单位
     */
    @Compare("单位")
    private String unit;

    /**
     * 参考值
     */
    @Compare("参考值")
    private String range;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    private Integer isDelete;
    /**
     * 分组
     */
    @Compare("分组")
    private String group;

    /**
     * 报告顺序
     */
    private Integer  reportSort;
    /**
     * 折点范围
     */
    private String foldPointScope;


    public void setRange(String range) {
        this.range = range;
    }

    /**
     * 设置参考值范围
     *
     * @param referValueMin 范围最小值，字符串类型。
     * @param referValueMax 范围最大值，字符串类型。
     */
    public void setRange(String referValueMin, String referValueMax) {
        if (StringUtils.isNotBlank(referValueMin) && StringUtils.isNotBlank(referValueMax)) {
            // 都不为空时，使用"-"拼接
            this.range = referValueMin + "-" + referValueMax;
        } else if (StringUtils.isNotBlank(referValueMin)) {
            // 只有referValueMin不为空
            this.range = referValueMin;
        } else if (StringUtils.isNotBlank(referValueMax)) {
            // 只有referValueMax不为空
            this.range = referValueMax;
        } else {
            this.range = Strings.EMPTY;
        }
    }

}
