package com.labway.lims.microbiology.api.dto;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询条件
 */
@Getter
@Setter
public class MicrobiologySampleCondition implements Serializable {
    /**
     * 开始检验日期
     */
    private Date beginTestDate;

    /**
     * 结束检验日期
     */
    private Date endTestDate;


    /**
     * 样本状态
     * @see SampleStatusEnum
     */
    private String sampleStatus;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 专业组过滤
     */
    private Long groupId;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 检验机构
     */
    private Long orgId;
}
