package com.labway.lims.apply.pgsql;

import org.junit.Test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class PgSqlTest {
    @Test
    public void test() throws Exception {
        String url = "*****************************************";
        String user = "root";
        String password = "root";

        Connection conn = DriverManager.getConnection(url, user, password);
        Statement stmt = conn.createStatement();
        ResultSet rs = stmt.executeQuery("SELECT * FROM tb_apply");
        while (rs.next()) {
            System.out.println(rs.getObject(1));
        }
        rs.close();
        stmt.close();
        conn.close();
    }
}
