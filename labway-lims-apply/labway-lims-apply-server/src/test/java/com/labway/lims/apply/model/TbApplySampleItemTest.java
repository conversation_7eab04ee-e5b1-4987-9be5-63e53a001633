package com.labway.lims.apply.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Date;

public class TbApplySampleItemTest {
    @Test
    public void test() {
        final TbApplySampleItem applySampleItem = new TbApplySampleItem();
        applySampleItem.setApplySampleItemId(0L);
        applySampleItem.setApplySampleId(0L);
        applySampleItem.setApplyId(0L);
        applySampleItem.setTestItemId(0L);
        applySampleItem.setTestItemCode("");
        applySampleItem.setTestItemName("");
        applySampleItem.setItemType("");
        applySampleItem.setOutTestItemId(0L);
        applySampleItem.setOutTestItemCode("");
        applySampleItem.setOutTestItemName("");
        applySampleItem.setSampleTypeCode("");
        applySampleItem.setSampleTypeName("");
        applySampleItem.setTubeCode("");
        applySampleItem.setTubeName("");
        applySampleItem.setGroupId(0L);
        applySampleItem.setGroupName("");
        applySampleItem.setRemark("");
        applySampleItem.setCreateDate(new Date());
        applySampleItem.setUpdateDate(new Date());
        applySampleItem.setCreatorId(0L);
        applySampleItem.setCreatorName("");
        applySampleItem.setUpdaterId(0L);
        applySampleItem.setUpdaterName("");
        applySampleItem.setIsDelete(0);
        applySampleItem.setCount(0);
        applySampleItem.setUrgent(0);
        applySampleItem.setSplitCode("");
        applySampleItem.setIsOutsourcing(0);
        applySampleItem.setExportOrgId(0L);
        applySampleItem.setExportOrgName("");
        applySampleItem.setIsFree(0);
        applySampleItem.setFeePrice(new BigDecimal("0"));

        System.out.println(JSON.toJSONString(applySampleItem, SerializerFeature.WriteMapNullValue));

    }
}