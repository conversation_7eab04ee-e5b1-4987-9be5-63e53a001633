package com.labway.lims.apply;

import com.google.common.collect.Lists;
import com.labway.lims.api.BarcodeUtils;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.RandomStringService;
import com.labway.lims.base.api.service.TestItemService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@SpringBootTest
@Slf4j
public class ElasticSearchTest {
    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @Resource
    private SnowflakeService snowflakeService;

    @Test
    public void queryEsBaseModel() {
        elasticSearchSampleService.deleteByApplySampleId(143570119499882611L);
    }

    @Test
    public void query() {}

    @Resource
    private BarcodeUtils barcodeUtils;

    @DubboReference
    private RandomStringService randomStringService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @DubboReference
    private TestItemService testItemService;

    @Test
    public void testBatch() {

        for (int i = 0; i < 20; i++) {

            test();
        }
    }

    @Test
    public void test() {
        final List<HspOrganizationDto> hspOrganizationDtos = hspOrganizationService.selectAll();
        final HspOrganizationDto hspOrganizationDto =
            hspOrganizationDtos.get(RandomUtils.nextInt(0, hspOrganizationDtos.size() - 1));

        final List<TestItemDto> testItems1 = testItemService.selectByOrgId(1);
        final TestItemDto testItemDto = testItems1.get(RandomUtils.nextInt(0, testItems1.size() - 1));

        OutsourcingInspectionDto routineInspectionDto = new OutsourcingInspectionDto();
        routineInspectionDto.setExportBarcode("2131232131321");
        routineInspectionDto.setExportOrgId(136051886548817935L);
        routineInspectionDto.setExportOrgName("迪安");
        routineInspectionDto.setIsPrintList(0);
        routineInspectionDto.setPrintListDate(new Date());
        routineInspectionDto.setInspectionDate(new Date());

        routineInspectionDto.setSampleStatus(List.of(10, 20, 30, 40, 50).get(RandomUtils.nextInt(0, 5)));
        routineInspectionDto.setRackId(NumberUtils.LONG_ZERO);
        routineInspectionDto.setOnePickerId(NumberUtils.LONG_ZERO);
        routineInspectionDto.setOnePickerName(randomStringService.randomChineseString());
        routineInspectionDto.setOnePickDate(new Date());
        routineInspectionDto.setIsOnePick(List.of(0, 1).get(RandomUtils.nextInt(0, 2)));
        routineInspectionDto.setTwoPickerId(NumberUtils.LONG_ZERO);
        routineInspectionDto.setTwoPickerName(randomStringService.randomChineseString());
        routineInspectionDto.setTwoPickDate(new Date());
        routineInspectionDto.setIsTwoPick(List.of(0, 1).get(RandomUtils.nextInt(0, 2)));
        routineInspectionDto.setIsSplitBlood(List.of(0, 1).get(RandomUtils.nextInt(0, 2)));
        routineInspectionDto.setSplitterId(snowflakeService.genId());
        routineInspectionDto.setSplitterName(randomStringService.randomChineseString());
        routineInspectionDto.setSplitDate(new Date());
        routineInspectionDto.setApplyId(snowflakeService.genId());
        routineInspectionDto.setMasterBarcode(barcodeUtils.genBarcode());
        routineInspectionDto.setDept("科室");
        routineInspectionDto.setUrgent(RandomUtils.nextInt(0, 1));
        routineInspectionDto.setApplyStatus(Arrays.stream(ApplyStatusEnum.values()).map(ApplyStatusEnum::getCode)
            .collect(Collectors.toList()).get(RandomUtils.nextInt(0, ApplyStatusEnum.values().length)));
        routineInspectionDto.setPatientName(randomStringService.randomChineseString());
        routineInspectionDto.setPatientAge(RandomUtils.nextInt(0, 100));
        routineInspectionDto.setPatientSubage(RandomUtils.nextInt(0, 10));
        routineInspectionDto.setPatientSubageUnit(List.of("岁", "月", "天").get(RandomUtils.nextInt(0, 3)));
        routineInspectionDto.setPatientBirthday(new Date());
        routineInspectionDto.setPatientCard("MANUAL001");
        routineInspectionDto.setPatientCardType("身份证");
        routineInspectionDto.setPatientBed(String.valueOf(RandomUtils.nextInt(0, 1000)));
        routineInspectionDto.setPatientSex(RandomUtils.nextInt(0, 1));
        routineInspectionDto.setPatientVisitCard("MANUAL001");
        routineInspectionDto.setPatientMobile("**********");
        routineInspectionDto.setPatientAddress("MANUAL001");
        // 提供一个数组 随机返回其中一个值
        routineInspectionDto.setApplyTypeCode(List.of("MZ", "ZY", "LNTJ", "TJ").get(RandomUtils.nextInt(0, 3)));
        routineInspectionDto.setApplyTypeName(List.of("MZ", "ZY", "LNTJ", "TJ").get(RandomUtils.nextInt(0, 3)));
        routineInspectionDto.setSampleCount(3);
        routineInspectionDto.setSampleProperty("MANUAL001");
        routineInspectionDto.setDiagnosis("MANUAL001");
        routineInspectionDto.setSendDoctorName("MANUAL001");
        routineInspectionDto.setSendDoctorCode("MANUAL001");
        routineInspectionDto.setApplyDate(new Date());
        routineInspectionDto.setSamplingDate(new Date());
        routineInspectionDto.setRemark("MANUAL001");
        routineInspectionDto.setSource(
            List.of(ApplySourceEnum.MANUAL.name(), ApplySourceEnum.SUPPLEMENTARY.name(), ApplySourceEnum.HIS.name())
                .get(RandomUtils.nextInt(0, 2)));
        routineInspectionDto.setSupplier("MANUAL001");
        routineInspectionDto.setHspOrgId(hspOrganizationDto.getHspOrgId());
        routineInspectionDto.setHspOrgCode(hspOrganizationDto.getHspOrgCode());
        routineInspectionDto.setHspOrgName(hspOrganizationDto.getHspOrgName());
        routineInspectionDto.setApplySampleId(snowflakeService.genId());
        routineInspectionDto.setOutBarcode("MANUAL001");
        routineInspectionDto.setBarcode(barcodeUtils.genBarcode());
        routineInspectionDto.setTubeName("血液");
        routineInspectionDto.setTubeCode(List.of("XY", "N", "XN", "SHI", "FENG").get(RandomUtils.nextInt(0, 4)));
        routineInspectionDto.setSampleTypeCode("MANUAL001");
        routineInspectionDto.setSampleTypeName("MANUAL001");
        routineInspectionDto.setGroupId(NumberUtils.LONG_ZERO);
        routineInspectionDto.setGroupName("MANUAL001");
        routineInspectionDto.setSampleStatus(
            List.of(Arrays.stream(SampleStatusEnum.values()).map(SampleStatusEnum::getCode).toArray(Integer[]::new))
                .get(RandomUtils.nextInt(0, SampleStatusEnum.values().length - 1)));
        routineInspectionDto.setRackId(NumberUtils.LONG_ZERO);
        routineInspectionDto.setSampleId(NumberUtils.LONG_ZERO);
        routineInspectionDto.setSampleNo("MANUAL001");
        routineInspectionDto.setInstrumentGroupId(NumberUtils.LONG_ZERO);
        routineInspectionDto.setInstrumentGroupName("MANUAL001");
        routineInspectionDto.setTestDate(new Date());
        routineInspectionDto.setSampleRemark("MANUAL001");
        routineInspectionDto.setResultRemark("MANUAL001");
        routineInspectionDto.setFinalCheckerId(21314132131L);
        routineInspectionDto.setFinalCheckerName("终审人");
        routineInspectionDto.setFinalCheckDate(new Date());
        routineInspectionDto.setItemType(ItemTypeEnum.OUTSOURCING.name());

        final ArrayList<BaseSampleEsModelDto.TestItem> testItems = Lists.newArrayList();
        final BaseSampleEsModelDto.TestItem testItem = new BaseSampleEsModelDto.TestItem();
        testItem.setTestItemId(131512321512313213L);
        testItem.setTestItemCode("23133232582");
        testItem.setTestItemName("体检");
        testItem.setOutTestItemId(NumberUtils.LONG_ZERO);
        testItem.setOutTestItemCode(StringUtils.EMPTY);
        testItem.setOutTestItemName(StringUtils.EMPTY);
        testItem.setGroupId(123L);
        testItem.setCreateDate(new Date());
        testItem.setPrice(new BigDecimal(RandomUtils.nextInt(0, 199)));
        testItem.setGroupName("专业组名称");
        testItems.add(testItem);

        final BaseSampleEsModelDto.TestItem testItem2 = new BaseSampleEsModelDto.TestItem();
        testItem2.setTestItemId(testItemDto.getTestItemId());
        testItem2.setTestItemCode(testItemDto.getTestItemCode());
        testItem2.setTestItemName(testItemDto.getTestItemName());
        testItem2.setOutTestItemId(NumberUtils.LONG_ZERO);
        testItem2.setCreateDate(new Date());
        testItem2.setPrice(testItemDto.getFeePrice());
        testItem2.setOutTestItemCode(StringUtils.EMPTY);
        testItem2.setOutTestItemName(StringUtils.EMPTY);
        testItem2.setGroupId(123L);
        testItem2.setGroupName("专业组名称");
        testItems.add(testItem2);
        routineInspectionDto.setTestItems(testItems);

        List<BaseSampleEsModelDto.Report> reports = Lists.newArrayList();
        BaseSampleEsModelDto.Report report1 = new BaseSampleEsModelDto.Report();
        report1.setUrl("url");
        report1.setFileType("type");
        reports.add(report1);
        routineInspectionDto.setReports(reports);
        routineInspectionDto.setOrgId(NumberUtils.LONG_ZERO);
        routineInspectionDto.setOrgName("MANUAL001");
        routineInspectionDto.setCreatorId(123456L);
        routineInspectionDto.setCreatorName("MANUAL001");
        routineInspectionDto.setCreateDate(new Date());
        routineInspectionDto.setIsDelete(YesOrNoEnum.NO.getCode());

        elasticSearchSampleService.insert(routineInspectionDto);
    }
}
