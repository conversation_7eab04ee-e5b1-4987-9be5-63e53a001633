package com.labway.lims.apply.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.junit.Test;

import java.util.Date;

public class TbApplySampleTest {
    @Test
public void test(){
        final TbApplySample applySample = new TbApplySample();
        applySample.setApplySampleId(0L);
        applySample.setApplyId(0L);
        applySample.setBarcode("");
        applySample.setOutBarcode("");
        applySample.setTubeName("");
        applySample.setTubeCode("");
        applySample.setSampleTypeName("");
        applySample.setSampleTypeCode("");
        applySample.setGroupId(0L);
        applySample.setGroupName("");
        applySample.setRackId(0L);
        applySample.setOnePickerId(0L);
        applySample.setOnePickerName("");
        applySample.setUrgent(0);
        applySample.setCreateDate(new Date());
        applySample.setUpdateDate(new Date());
        applySample.setCreatorId(0L);
        applySample.setCreatorName("");
        applySample.setUpdaterId(0L);
        applySample.setUpdaterName("");
        applySample.setIsDelete(0);
        applySample.setIsOnePick(0);
        applySample.setOnePickDate(new Date());
        applySample.setTwoPickerId(0L);
        applySample.setTwoPickerName("");
        applySample.setTwoPickDate(new Date());
        applySample.setIsTwoPick(0);
        applySample.setIsSplitBlood(0);
        applySample.setSplitterId(0L);
        applySample.setSplitterName("");
        applySample.setSplitDate(new Date());
        applySample.setStatus(0);
        applySample.setIsDisabled(0);
        applySample.setPrinterId(0L);
        applySample.setPrinterName("");
        applySample.setPrintDate(new Date());
        applySample.setTesterId(0L);
        applySample.setTesterName("");
        applySample.setOrgId(0L);
        applySample.setOrgName("");
        applySample.setItemType("");
        applySample.setIsOutsourcing(0);
        applySample.setIsArchive(0);
        applySample.setIsPrint(0);
        applySample.setSampleRemark("");
        applySample.setResultRemark("");

        System.out.println(JSON.toJSONString(applySample, SerializerFeature.WriteMapNullValue));

    }
}