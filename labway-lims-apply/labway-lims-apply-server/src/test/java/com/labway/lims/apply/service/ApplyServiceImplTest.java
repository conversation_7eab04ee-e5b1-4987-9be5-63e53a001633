package com.labway.lims.apply.service;

import com.labway.lims.apply.api.service.ApplyService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ApplyServiceImplTest {
    @Resource
    private ApplyService applyService;

    @Test
    public void test() {
        final long id = 118954115319403347L;
        System.out.println(applyService.selectByApplyId(id));
    }

    @Test
    public void test2() {
        final long id = 118954115319403347L;
        System.out.println(applyService.selectByApplyId(id));
    }
}