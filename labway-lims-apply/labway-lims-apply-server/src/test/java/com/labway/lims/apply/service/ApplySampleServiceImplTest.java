package com.labway.lims.apply.service;


import com.labway.lims.apply.api.service.ApplySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ApplySampleServiceImplTest {
    @Resource
    private ApplySampleService applySampleService;

    @Test
    public void test() {
        final HintManager instance = HintManager.getInstance();
        instance.addDatabaseShardingValue("tb_apply", 1);
//        applySampleService.selectOnePickedSamplesByNextGroupId(new Date(), new Date(), 1L);
    }

    @Test
    public void test2() {
        applySampleService.selectBySplitDate(new Date(), new Date());
    }

}
