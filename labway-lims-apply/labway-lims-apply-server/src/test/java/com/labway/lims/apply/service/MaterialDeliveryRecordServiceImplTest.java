package com.labway.lims.apply.service;

import com.labway.lims.apply.api.dto.BusinessCenterDeliveryDto;
import com.labway.lims.apply.api.service.MaterialDeliveryRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@SpringBootTest
class MaterialDeliveryRecordServiceImplTest {

    @Resource
    private MaterialDeliveryRecordService materialDeliveryRecordService;

    @Test
    void businessCenterDelivery() {
        BusinessCenterDeliveryDto dto = new BusinessCenterDeliveryDto();
        dto.setOrgId(1L);
        dto.setApplyNo("001");
        dto.setDeliveryNo("CK001");
        dto.setDeliveryDate(new Date());
        dto.setDeliveryUser("test出库人");

        List<BusinessCenterDeliveryDto.BusinessCenterDeliveryItemDto> deliveryItemList = new ArrayList<>();

        for (int i = 1; i <= 3; i++) {
            BusinessCenterDeliveryDto.BusinessCenterDeliveryItemDto temp =
                new BusinessCenterDeliveryDto.BusinessCenterDeliveryItemDto();
//            temp.setMaterialId((long)i);
            temp.setMaterialCode("test-code-" + i);
            temp.setMaterialName("test-name-" + i);
            temp.setSpecification(StringUtils.EMPTY);
            temp.setBatchNo("ba-" + i);
            temp.setManufacturers("ff");
            temp.setMainUnit("主");
            temp.setDeliveryMainNumber(null);
            temp.setAssistUnit("辅");
            temp.setDeliveryAssistNumber(BigDecimal.valueOf(NumberUtils.LONG_ZERO));
            temp.setUnitConversionRate("1/2");
            temp.setValidDate(new Date());
            deliveryItemList.add(temp);
        }
        dto.setDeliveryItemList(deliveryItemList);
        try {

            materialDeliveryRecordService.businessCenterDelivery(dto);
        } catch (Exception e) {
            log.error(e + "");

        }

    }
}
