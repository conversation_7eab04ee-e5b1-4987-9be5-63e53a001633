package com.labway.lims.apply;

import cn.hutool.core.util.IdUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Getter
@Setter
@Slf4j
@SpringBootTest
public class RedisTest {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final String ID = IdUtil.objectId();

    @Test
    public void testSetIfAbsent() {
        String id = "123456";

        for (int i = 0; i < 3; i++) {
            new Thread(() -> {
                final Boolean aBoolean = stringRedisTemplate.opsForValue().setIfAbsent(ID + id, StringUtils.EMPTY, 10, TimeUnit.MINUTES);
                System.out.println("result: " + aBoolean);
            }).start();
        }

    }

    @Test
    public void testMember() {
        stringRedisTemplate.opsForSet().add("test-01", "1");
        stringRedisTemplate.opsForSet().add("test-01", "2");
        stringRedisTemplate.opsForSet().add("test-01", "3");


        final Set<String> members = stringRedisTemplate.opsForSet().members("test-01");
        log.info("members:{}", members);


        stringRedisTemplate.opsForSet().members("test-01").forEach(s -> {
            log.info("s:{}", s);
        });

    }

    @Test
    public void test01() {

        final Boolean aBoolean = stringRedisTemplate.opsForValue().setIfPresent("123123123213", StringUtils.EMPTY);

        log.info("BooleanUtils.isNotTrue:{}", BooleanUtils.isNotTrue(aBoolean));
        log.info("aBoolean:{}", aBoolean);
    }

    @Test
    public void test() {
        String incrementKey = "mykey";
        List<String> keys = Arrays.asList(incrementKey);
        Long increment = 10000L;
        String script = "local value = tonumber(redis.call('GET', KEYS[1])) or 0\n" +
                "local result = {}\n" +
                "for i=1, ARGV[1] do\n" +
                "    value = value + 1\n" +
                "    table.insert(result, value)\n" +
                "end\n" +
                "redis.call('SET', KEYS[1], value)\n" +
                "return result";
        List<String> values = Arrays.asList(increment.toString());
        List<Long> increments = stringRedisTemplate.execute(new DefaultRedisScript<>(script, List.class), keys, increment.toString());
        System.out.println(increments);
    }

}
