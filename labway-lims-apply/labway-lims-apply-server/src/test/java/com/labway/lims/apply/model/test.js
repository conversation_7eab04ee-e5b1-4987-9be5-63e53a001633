const params = {
    // 申请单
    "apply": {
        // 申请日期
        "applyDate": 1686736957250,
        // 申请单ID
        "applyId": 0,
        // 就诊类型编码
        "applyTypeCode": "",
        // 就诊类型名称
        "applyTypeName": "",
        // 复合时间
        "checkDate": 1686736957250,
        // 复合人
        "checkerId": 0,
        // 复合人名称
        "checkerName": "",
        // 创建日期
        "createDate": 1686736957250,
        // 创建人
        "creatorId": 0,
        // 创建人名称
        "creatorName": "",
        // 部门
        "dept": "",
        // 临床诊断
        "diagnosis": "",
        // 送检机构编码
        "hspOrgCode": "",
        // 送检机构ID
        "hspOrgId": 0,
        // 送检机构名称
        "hspOrgName": "",
        // 主条码
        "masterBarcode": "",
        // 送检机构ID
        "orgId": 0,
        // 送检机构名称
        "orgName": "",
        // 病人名称
        "patientAddress": "",
        // 病人年龄
        "patientAge": 0,
        // 床号
        "patientBed": "",
        // 生日
        "patientBirthday": 1686736957250,
        // 证件号
        "patientCard": "",
        // 证件类型
        "patientCardType": "",
        // 手机号
        "patientMobile": "",
        // 病人名称
        "patientName": "",
        // 性别，1男，2女
        "patientSex": 0,
        // 子年龄
        "patientSubage": 0,
        // 子年龄单位
        "patientSubageUnit": "",
        // 就诊卡号
        "patientVisitCard": "",
        // 备注
        "remark": "",
        // 样本数量
        "sampleCount": 0,
        // 性状
        "sampleProperty": "",
        // 性状编码
        "samplePropertyCode": "",
        // 采样日期
        "samplingDate": 1686736957250,
        // 送检医生编码
        "sendDoctorCode": "",
        // 送检医生名称
        "sendDoctorName": "",
        // 签收时间
        "signDate": 1686736957250,
        // 来源
        "source": "",
        // 供应商
        "supplier": "",
        // 是否加急，1是，0不是
        "urgent": 0
    },
    // 申请单样本
    "applySample": {
        // 申请单ID
        "applySampleId": 0,
        // 条码号
        "barcode": "",
        // 专业组ID
        "groupId": 0,
        // 专业组名称
        "groupName": "",
        // 外部条码
        "outBarcode": "",
        // 打印时间
        "printDate": 1686737318633,
        // 结果备注
        "resultRemark": "",
        // 样本备注
        "sampleRemark": "",
        // 样本类型编码
        "sampleTypeCode": "",
        // 样本类型名称
        "sampleTypeName": "",
        // 测试人ID
        "testerId": 0,
        // 测试人
        "testerName": "",
        // 管型编码
        "tubeCode": "",
        // 管型名称
        "tubeName": "",
        // 二次分拣日期
        "twoPickDate": 1686737318633,
        // 二次分拣人ID
        "twoPickerId": 0,
        // 二次分拣人名称
        "twoPickerName": "",
        // 一次分拣人ID
        "onePickerId": 0,
        // 一次分拣人名称
        "onePickerName": "",
        // 是否加急，1是，0不是
        "urgent": 0
    },
    // 样本
    "sample": {
        // 仪器专业小组ID
        "instrumentGroupId": 0,
        //仪器专业小组名称
        "instrumentGroupName": "",
        // 仪器ID
        "instrumentId": 0,
        // 仪器名称
        "instrumentName": "",
        // 一次审核日期
        "oneCheckDate": 1686737559942,
        // 一次审核人ID
        "oneCheckerId": 0,
        //一次审核人名称
        "oneCheckerName": "",
        // 样本号
        "sampleNo": "",
        // 测试日期
        "testDate": 1686737559942,
        // 二次审核人日期
        "twoCheckDate": 1686737559942,
        //二次审核人ID
        "twoCheckerId": 0,
        //二次审核人名称，没有二次审核人那就是一次审核人
        "twoCheckerName": ""
    },
    // 检验项目
    "applySampleItems": {
        // 数量
        "count": 0,
        // 外送机构ID
        "exportOrgId": 0,
        // 外送机构名称
        "exportOrgName": "",
        // 单价
        "feePrice": 0,
        // 专业组ID
        "groupId": 0,
        // 专业组名称
        "groupName": "",
        // 是否免费
        "isFree": 0,
        // 是否外送
        "isOutsourcing": 0,
        // 项目类型
        "itemType": "",
        // 外部项目编码
        "outTestItemCode": "",
        // 外部项目ID
        "outTestItemId": 0,
        // 外部项目名称
        "outTestItemName": "",
        // 备注
        "remark": "",
        // 样本类型编码
        "sampleTypeCode": "",
        // 样本类型名称
        "sampleTypeName": "",
        // 检验项目编码
        "testItemCode": "",
        // 检验项目ID
        "testItemId": 0,
        // 检验项目名称
        "testItemName": "",
        // 管型编码
        "tubeCode": "",
        // 管型名称
        "tubeName": "",
        // 是否加急，1是，0不是
        "urgent": 0
    },
    // 结果
    "sampleResults": {
        // 仪器结果
        "instrumentResult": "",
        // UP or DOWN
        "judge": "",
        // 参考范围
        "range": "",
        // 报告项目编码
        "reportItemCode": "",
        // 报告项目ID
        "reportItemId": 0,
        // 报告项目名称
        "reportItemName": "",
        // 结果
        "result": "",
        // 主键
        "sampleResultId": 0,
        // 1: 危机 2: 异常 0: 正常
        "status": 0,
        // 检验项目编码
        "testItemCode": "",
        // 检验项目ID
        "testItemId": 0,
        // 检验项目名称
        "testItemName": "",
        // 结果类型，数值、图片等
        "type": "",
        // 英文名称
        "enName": "",
        // 英文缩写
        "enAb": ""
    },
    // 原始对象
    "_apply": {},
    // 可以加更多对象进来，上面的只是公共的。
}