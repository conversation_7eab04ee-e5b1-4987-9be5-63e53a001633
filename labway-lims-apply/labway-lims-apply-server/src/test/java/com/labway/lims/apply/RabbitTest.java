package com.labway.lims.apply;

import com.alibaba.fastjson.JSON;
import com.labway.business.center.core.enums.OperationTypeEnum;
import com.labway.business.center.core.log.param.MessageDTO;
import com.labway.business.center.core.log.param.OperationLogRequest;
import lombok.Getter;
import lombok.Setter;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Getter
@Setter
@SpringBootTest
public class RabbitTest {

    public static final String OPERATION_OUT_EXCHANGE = "labway.business.center.operation.log";

    @Resource
    private RabbitTemplate rabbitTemplate;


    @Test
    public void sendLogMsgToCenter(){

        OperationLogRequest log = new OperationLogRequest();
        log.setRequestId("131421");
        log.setUserName("李博辉");
        log.setSystemKey("labway_lims");
        log.setSystemName("实验室");
        log.setSystemModule("专业组创建");
        log.setIpAddress("127.0.0.1");
        log.setIpLocation("上海");
        log.setOperationTypeCode(OperationTypeEnum.MAKE.getCode());
        log.setOperationMessage(OperationTypeEnum.MAKE.getType());
        log.setOperationTime(LocalDateTime.now());


        final MessageDTO build = MessageDTO.builder().messageId("12345").message(JSON.toJSONString(log)).sendTime(System.currentTimeMillis()).build();


        rabbitTemplate.convertAndSend(OPERATION_OUT_EXCHANGE, "", JSON.toJSONString(build));
    }
}
