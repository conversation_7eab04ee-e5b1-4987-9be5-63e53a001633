package com.labway.lims.apply.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.junit.Test;

import java.util.Date;

public class TbApplyTest {
    @Test
    public void test() {
        final TbApply apply = new TbApply();
        apply.setApplyId(0L);
        apply.setMasterBarcode("");
        apply.setPatientName("");
        apply.setPatientAge(0);
        apply.setPatientSubage(0);
        apply.setPatientSubageUnit("");
        apply.setPatientBirthday(new Date());
        apply.setPatientCard("");
        apply.setPatientCardType("");
        apply.setPatientBed("");
        apply.setPatientSex(0);
        apply.setPatientVisitCard("");
        apply.setPatientMobile("");
        apply.setPatientAddress("");
        apply.setSource("");
        apply.setApplyTypeCode("");
        apply.setApplyTypeName("");
        apply.setRemark("");
        apply.setSampleCount(0);
        apply.setSampleProperty("");
        apply.setSamplePropertyCode("");
        apply.setDept("");
        apply.setDiagnosis("");
        apply.setSendDoctorName("");
        apply.setSendDoctorCode("");
        apply.setHspOrgId(0L);
        apply.setHspOrgCode("");
        apply.setHspOrgName("");
        apply.setOrgId(0L);
        apply.setOrgName("");
        apply.setUrgent(0);
        apply.setSupplier("");
        apply.setApplyDate(new Date());
        apply.setSamplingDate(new Date());
        apply.setCreateDate(new Date());
        apply.setUpdateDate(new Date());
        apply.setCreatorName("");
        apply.setCreatorId(0L);
        apply.setUpdaterName("");
        apply.setUpdaterId(0L);
        apply.setStatus(0);
        apply.setCheckerId(0L);
        apply.setCheckerName("");
        apply.setCheckDate(new Date());
        apply.setSignDate(new Date());
        apply.setIsDelete(0);

        System.out.println(JSON.toJSONString(apply, SerializerFeature.WriteMapNullValue));

    }

}