package com.labway.lims.apply;

import com.labway.lims.api.BarcodeUtils;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyLogisticsDto;
import com.labway.lims.apply.api.dto.ApplyLogisticsSampleDto;
import com.labway.lims.apply.api.dto.ApplyLogisticsSampleItemDto;
import com.labway.lims.apply.api.service.ApplyLogisticsSampleItemService;
import com.labway.lims.apply.api.service.ApplyLogisticsSampleService;
import com.labway.lims.apply.api.service.ApplyLogisticsService;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.RandomStringService;
import com.labway.lims.base.api.service.TestItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Slf4j
@SpringBootTest
public class MockDataTest {


    @DubboReference
    private TestItemService testItemService;

    @Resource
    private ApplyLogisticsService applyLogisticsService;

    @Resource
    private ApplyLogisticsSampleService applyLogisticsSampleService;

    @Resource
    private ApplyLogisticsSampleItemService applyLogisticsSampleItemService;

    @Resource
    private BarcodeUtils barcodeUtils;

    @Resource
    private SnowflakeService snowflakeService;

    @DubboReference
    private RandomStringService randomStringService;

    @Test
    public void testGenBarcode() {
        for (int i = 0; i < 10; i++) {

            final String masterBarcode = barcodeUtils.genBarcode();
            final String sampleBarcode = barcodeUtils.genBarcode();

            log.info("masterBarcode: {}, sampleBarcode: {}", masterBarcode, sampleBarcode);
        }
    }

    @Test
    public void test() {
        for (int i = 0; i < 100; i++) {
            mockLogisticsData();
        }
    }

    @Test
    public void mockLogisticsData() {
        ApplyLogisticsDto applyLogisticsDto = new ApplyLogisticsDto();
        applyLogisticsDto.setApplyLogisticsId(snowflakeService.genId());
        applyLogisticsDto.setMasterBarcode(barcodeUtils.genBarcode());
        applyLogisticsDto.setCreateDate(new Date());
        applyLogisticsDto.setUpdateDate(new Date());
        applyLogisticsDto.setUpdaterId(1234L);
        applyLogisticsDto.setUpdaterName(randomStringService.randomChineseString());
        applyLogisticsDto.setCreatorId(1234L);
        applyLogisticsDto.setCreatorName(randomStringService.randomChineseString());
        applyLogisticsDto.setReceiveDate(new Date());
        applyLogisticsDto.setLogisticsUserId(12345L);
        applyLogisticsDto.setLogisticsUserName(randomStringService.randomChineseString());

        applyLogisticsDto.setApplyImage("https://imgcps.jd.com/ling-cubic/ling4/lab/amZzL3QxLzIyMzAxNS85LzE3NzY0LzMyNjY1LzYzZjQ2OGIyRmMyOTVjMmNhLzc4MDJiZWUwNTVjN2NiYTcucG5n/5Lqs6YCJ5aW96LSn/5L2g5YC85b6X5oul5pyJ/1635185337375584258/cr/s/q.jpg,https://imgcps.jd.com/img-cubic/creative_server_cia_jdcloud/v2/2000367/10162122906/FocusFullshop/CkRqZnMvdDEvMTc5NjE5LzM1LzMzMTk1LzEwMzkzMC82NDNjNGI5MEY1ZTg3NDNjNy9jMjg3YmNjOWEyZTc4YzZmLnBuZxIJNC10eV8wXzU1MAI474t6QhkKFeiIquWQkeiAhee7hOijheeUteiEkRABQhEKDea7oTEwMDDlh480NzAQAkIQCgznq4vljbPmiqLotK0QBkIKCgbotoXlgLwQB1ia4dbtJQ/cr/s/q.jpg");
        applyLogisticsDto.setIsDelete(YesOrNoEnum.NO.getCode());
        applyLogisticsDto.setHspOrgId(124023365440774156L);
        applyLogisticsDto.setHspOrgName("我是送检机构");
        applyLogisticsDto.setOrgId(1L);
        applyLogisticsDto.setOrgName("机构1");


        ApplyLogisticsSampleDto applyLogisticsSample = new ApplyLogisticsSampleDto();
        applyLogisticsSample.setApplyLogisticsSampleId(snowflakeService.genId());
        applyLogisticsSample.setBarcode(barcodeUtils.genBarcode());
        applyLogisticsSample.setStatus(0);
        applyLogisticsSample.setApplyId(NumberUtils.LONG_ZERO);
        applyLogisticsSample.setCreateDate(new Date());
        applyLogisticsSample.setUpdateDate(new Date());
        applyLogisticsSample.setUpdaterId(applyLogisticsDto.getUpdaterId());
        applyLogisticsSample.setUpdaterName(applyLogisticsDto.getUpdaterName());
        applyLogisticsSample.setCreatorId(applyLogisticsDto.getCreatorId());
        applyLogisticsSample.setCreatorName(applyLogisticsDto.getCreatorName());
        applyLogisticsSample.setIsDelete(YesOrNoEnum.NO.getCode());
        applyLogisticsSample.setApplyLogisticsId(applyLogisticsDto.getApplyLogisticsId());

        final List<TestItemDto> testItems = testItemService.selectByOrgId(1);
        final TestItemDto testItemDto = testItems.get(RandomUtils.nextInt(0, testItems.size()));

        ApplyLogisticsSampleItemDto applyLogisticsSampleItem = new ApplyLogisticsSampleItemDto();
        applyLogisticsSampleItem.setApplyLogisticsApplyItemId(snowflakeService.genId());
        applyLogisticsSampleItem.setApplyLogisticsSampleId(applyLogisticsSample.getApplyLogisticsSampleId());
        applyLogisticsSampleItem.setApplyLogisticsId(applyLogisticsDto.getApplyLogisticsId());
        applyLogisticsSampleItem.setTestItemId(testItemDto.getTestItemId());
        applyLogisticsSampleItem.setTestItemCode(testItemDto.getTestItemCode());
        applyLogisticsSampleItem.setTestItemName(testItemDto.getTestItemName());
        applyLogisticsSampleItem.setCreateDate(new Date());
        applyLogisticsSampleItem.setUpdateDate(new Date());
        applyLogisticsSampleItem.setCreatorId(applyLogisticsDto.getCreatorId());
        applyLogisticsSampleItem.setCreatorName(applyLogisticsDto.getCreatorName());
        applyLogisticsSampleItem.setUpdaterId(applyLogisticsDto.getUpdaterId());
        applyLogisticsSampleItem.setUpdaterName(applyLogisticsDto.getUpdaterName());
        applyLogisticsSampleItem.setIsDelete(YesOrNoEnum.NO.getCode());


        applyLogisticsService.add(applyLogisticsDto);
        applyLogisticsSampleService.add(applyLogisticsSample);
        applyLogisticsSampleItemService.add(applyLogisticsSampleItem);


    }
}
