package com.labway.lims.apply;

import org.apache.commons.io.IOUtils;
import org.jasypt.util.text.BasicTextEncryptor;
import org.junit.Test;

import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;

public class BasicTextEncryptorTest {
    @Test
    public void test() throws Exception {
        final BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
        textEncryptor.setPassword(IOUtils.toString(new FileInputStream("/Users/<USER>/Software/projects/labway/zl/jasypt.encryptor.password"),
                StandardCharsets.UTF_8));
        String password = textEncryptor.encrypt("vP5QqmDqUPJ58L");
        System.out.println("解密:" + textEncryptor.decrypt(password));
        System.out.println("加密:" + password);
    }
}
