package com.labway.lims.apply;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class StringUtilsTest {
    @Test
    public void test() {
        final String original = "000001";
        final String no = "1103";


        System.out.println(StringUtils.left(original, original.length() - no.length()) + no);


    }

    @Test
    public void test2() {
        String format = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
        System.out.println(format);
    }

    @Test
    public void test3() {
        Integer str = 12345; // 原始字符串

        // 使用格式化字符串进行补齐
        String paddedStr = String.format("%04d", str);

        System.out.println("补齐后的字符串：" + paddedStr);
    }
}
