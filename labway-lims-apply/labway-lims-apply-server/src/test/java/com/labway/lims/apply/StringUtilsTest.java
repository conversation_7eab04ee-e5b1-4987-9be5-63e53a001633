package com.labway.lims.apply;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StringUtilsTest {
    @Test
    public void test() {
        final String original = "000001";
        final String no = "1103";


        System.out.println(StringUtils.left(original, original.length() - no.length()) + no);


    }

    @Test
    public void test2() {
        String format = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
        System.out.println(format);
    }

    @Test
    public void test3() {
        Integer str = 12345; // 原始字符串

        // 使用格式化字符串进行补齐
        String paddedStr = String.format("%04d", str);

        System.out.println("补齐后的字符串：" + paddedStr);
    }

	@Test
	public void test4() {
		String url = "https://dy-lims.labway.cn/LimsGateway/statistics/report/list";

		// 构建请求体JSON
		String requestBody = buildRequestBody();

		// 生成文件名（包含时间戳）
		String fileName = generateFileName();

		try {
			// 执行请求并保存到文件
			saveResponseToFile(url, requestBody, fileName);
			System.out.println("数据已成功保存到文件: " + fileName);
		} catch (Exception e) {
			System.err.println("保存数据时发生错误: " + e.getMessage());
			e.printStackTrace();
		}
	}

	/**
	 * 直接从API获取数据并解析机构报告信息（适用于数据量不大的情况）
	 */
	@Test
	public void getOrgReportInfoDirectly() {
		String url = "https://dy-lims.labway.cn/LimsGateway/statistics/report/list";
		String requestBody = buildRequestBody();

		try {
			System.out.println("正在获取数据...");

			// 直接获取响应内容
			String responseBody = HttpUtil.createPost(url)
					.header("Authorization", "b12e0eeba6da4a2dbf0dc8e644a6f73e")
					.header("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
					.header("Content-Type", "application/json")
					.header("Accept", "*/*")
					.header("Host", "dy-lims.labway.cn")
					.header("Connection", "keep-alive")
					.body(requestBody)
					.execute()
					.body();

			// 解析并提取数据
			List<OrgReportInfo> orgReportInfos = extractOrgReportInfo(responseBody);

			// 输出结果
			printOrgReportInfo(orgReportInfos);

		} catch (Exception e) {
			System.err.println("获取数据时发生错误: " + e.getMessage());
			e.printStackTrace();
		}
	}

	/**
	 * 构建请求体JSON
	 */
	private String buildRequestBody() {
		return "{\r\n" +
				"    \"beginSignDate\": \"2025-07-01 00:00:00\",\r\n" +
				"    \"endSignDate\": \"2025-07-31 23:59:59\",\r\n" +
				"    \"beginCheckDate\": \"2025-07-01 00:00:00\",\r\n" +
				"    \"endCheckDate\": \"2025-07-31 23:59:59\",\r\n" +
				"    \"hspOrgId\": \"\",\r\n" +
				"    \"physicalGroupId\": \"\",\r\n" +
				"    \"dept\": \"\",\r\n" +
				"    \"groupId\": \"\",\r\n" +
				"    \"testItemIds\": [],\r\n" +
				"    \"patientName\": \"\",\r\n" +
				"    \"patientSex\": \"\",\r\n" +
				"    \"applyTypes\": [\r\n" +
				"        \"00000012-00000005\",\r\n" +
				"        \"00000012-00000002\",\r\n" +
				"        \"0000012-00000004\"\r\n" +
				"    ],\r\n" +
				"    \"barcode\": \"\",\r\n" +
				"    \"patientVisitCard\": \"\",\r\n" +
				"    \"isPrint\": \"\",\r\n" +
				"    \"sortData\": []\r\n" +
				"}";
	}

	/**
	 * 生成带时间戳的文件名
	 */
	private String generateFileName() {
		String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
		return "lims_report_data_" + timestamp + ".json";
	}

	/**
	 * 执行HTTP请求并将响应保存到文件
	 */
	private void saveResponseToFile(String url, String requestBody, String fileName) throws IOException {
		System.out.println("开始请求数据，预计数据量较大，请耐心等待...");

		// 使用流式处理保存大文件，避免内存溢出
		try (FileOutputStream fos = new FileOutputStream(fileName);
			 BufferedOutputStream bos = new BufferedOutputStream(fos, 8192)) {

			HttpResponse response = HttpUtil.createPost(url)
					.header("Authorization", "b12e0eeba6da4a2dbf0dc8e644a6f73e")
					.header("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
					.header("Content-Type", "application/json")
					.header("Accept", "*/*")
					.header("Host", "dy-lims.labway.cn")
					.header("Connection", "keep-alive")
					.body(requestBody)
					.execute();

			// 检查响应状态
			if (!response.isOk()) {
				throw new RuntimeException("HTTP请求失败，状态码: " + response.getStatus());
			}

			// 流式写入文件
			try (InputStream inputStream = response.bodyStream()) {
				byte[] buffer = new byte[8192]; // 8KB缓冲区
				int bytesRead;
				long totalBytes = 0;

				while ((bytesRead = inputStream.read(buffer)) != -1) {
					bos.write(buffer, 0, bytesRead);
					totalBytes += bytesRead;

					// 每10MB打印一次进度
					if (totalBytes % (10 * 1024 * 1024) == 0) {
						System.out.println("已下载: " + (totalBytes / 1024 / 1024) + " MB");
					}
				}

				System.out.println("下载完成，总大小: " + (totalBytes / 1024 / 1024) + " MB");
			}
		}

	/**
	 * 解析保存的JSON文件，提取机构名称和报告份数
	 */
	@Test
	public void parseJsonData() {
		String fileName = "lims_report_data_20250812_143025.json"; // 替换为实际的文件名

		try {
			// 读取JSON文件
			String jsonContent = readJsonFile(fileName);

			// 解析并提取数据
			List<OrgReportInfo> orgReportInfos = extractOrgReportInfo(jsonContent);

			// 输出结果
			printOrgReportInfo(orgReportInfos);

		} catch (Exception e) {
			System.err.println("解析JSON数据时发生错误: " + e.getMessage());
			e.printStackTrace();
		}
	}

	/**
	 * 读取JSON文件内容
	 */
	private String readJsonFile(String fileName) throws IOException {
		return new String(Files.readAllBytes(Paths.get(fileName)), StandardCharsets.UTF_8);
	}

	/**
	 * 从JSON内容中提取机构名称和报告份数
	 */
	private List<OrgReportInfo> extractOrgReportInfo(String jsonContent) {
		List<OrgReportInfo> result = new ArrayList<>();

		try {
			// 解析JSON
			JSONObject jsonObject = JSONUtil.parseObj(jsonContent);

			// 检查响应状态
			Integer code = jsonObject.getInt("code");
			if (code == null || code != 0) {
				throw new RuntimeException("API响应错误，code: " + code + ", message: " + jsonObject.getStr("message"));
			}

			// 获取data对象
			JSONObject data = jsonObject.getJSONObject("data");
			if (data == null) {
				throw new RuntimeException("响应数据中没有data字段");
			}

			// 获取hspOrgReports数组
			JSONArray hspOrgReports = data.getJSONArray("hspOrgReports");
			if (hspOrgReports == null || hspOrgReports.isEmpty()) {
				System.out.println("没有找到机构报告数据");
				return result;
			}

			// 遍历每个机构
			for (int i = 0; i < hspOrgReports.size(); i++) {
				JSONObject orgReport = hspOrgReports.getJSONObject(i);

				// 提取机构信息
				String hspOrgId = orgReport.getStr("hspOrgId");
				String hspOrgName = orgReport.getStr("hspOrgName");
				JSONArray reportItemList = orgReport.getJSONArray("reportItemList");

				// 计算报告份数
				int reportCount = reportItemList != null ? reportItemList.size() : 0;

				// 创建结果对象
				OrgReportInfo orgInfo = new OrgReportInfo(hspOrgId, hspOrgName, reportCount);
				result.add(orgInfo);
			}

		} catch (Exception e) {
			throw new RuntimeException("解析JSON数据失败: " + e.getMessage(), e);
		}

		return result;
	}

	/**
	 * 打印机构报告信息
	 */
	private void printOrgReportInfo(List<OrgReportInfo> orgReportInfos) {
		System.out.println("=== 机构报告统计信息 ===");
		System.out.println("总机构数量: " + orgReportInfos.size());
		System.out.println();

		int totalReports = 0;
		for (int i = 0; i < orgReportInfos.size(); i++) {
			OrgReportInfo info = orgReportInfos.get(i);
			System.out.printf("%d. 机构名称: %s%n", (i + 1), info.getHspOrgName());
			System.out.printf("   机构ID: %s%n", info.getHspOrgId());
			System.out.printf("   报告份数: %d%n", info.getReportCount());
			System.out.println();

			totalReports += info.getReportCount();
		}

		System.out.println("=== 汇总信息 ===");
		System.out.println("总报告份数: " + totalReports);
	}

	/**
	 * 机构报告信息实体类
	 */
	public static class OrgReportInfo {
		private String hspOrgId;
		private String hspOrgName;
		private int reportCount;

		public OrgReportInfo(String hspOrgId, String hspOrgName, int reportCount) {
			this.hspOrgId = hspOrgId;
			this.hspOrgName = hspOrgName;
			this.reportCount = reportCount;
		}

		public String getHspOrgId() {
			return hspOrgId;
		}

		public String getHspOrgName() {
			return hspOrgName;
		}

		public int getReportCount() {
			return reportCount;
		}

		@Override
		public String toString() {
			return String.format("机构: %s, 报告份数: %d", hspOrgName, reportCount);
		}
	}
	}

}
