package com.labway.lims.apply.controller;

import com.labway.lims.apply.api.dto.PhysicalBatchDto;
import com.labway.lims.apply.api.service.PhysicalBatchService;
import com.labway.lims.apply.vo.utils.PhysicalBatchNumberUtil;
import com.labway.lims.base.api.dto.PhysicalGroupDto;
import com.labway.lims.base.api.service.PhysicalGroupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@SpringBootTest
class PhysicalBatchControllerTest {
    @Resource
    private PhysicalBatchNumberUtil physicalBatchNumberUtil;

    @DubboReference
    private PhysicalBatchService physicalBatchService;
    @DubboReference
    private PhysicalGroupService physicalGroupService;

    @Test
    void physicalBatchAdd() {
        PhysicalGroupDto physicalGroupDto = physicalGroupService.selectByPhysicalGroupId(118233752546443266L);

        // 转换
      
        for (int i = 0; i < 999; i++) {
            PhysicalBatchDto target = new PhysicalBatchDto();
            target.setPhysicalBatchNumber(physicalBatchNumberUtil.getPhysicalBatchNumber());
            target.setPhysicalCompanyId(physicalGroupDto.getPhysicalGroupId());
            target.setPhysicalCompanyName(physicalGroupDto.getPhysicalGroupName());
            target.setImportDate(new Date());
            physicalBatchService.addPhysicalBatch(target);
        }
     
    }
}