<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbPhysicalSampleItemMapper">

    <insert id="batchAddPhysicalSampleItems">
        INSERT INTO tb_physical_sample_item (
        physical_sample_item_id,
        physical_sample_id,
        physical_register_id,
        physical_batch_id,
        test_item_id,
        test_item_code,
        test_item_name,
        org_id,
        org_name,
        create_date,
        update_date,
        creator_name,
        creator_id,
        updater_name,
        updater_id,
        is_delete
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.physicalSampleItemId},
            #{item.physicalSampleId},
            #{item.physicalRegisterId},
            #{item.physicalBatchId},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.testItemName},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorName},
            #{item.creatorId},
            #{item.updaterName},
            #{item.updaterId},
            #{item.isDelete}
            )
        </foreach>

    </insert>
</mapper>
