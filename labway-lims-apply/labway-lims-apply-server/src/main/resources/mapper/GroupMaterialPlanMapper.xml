<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.lims.apply.mapper.GroupMaterialPlanMapper">
    <update id="updateRemarkByGroupMaterialPlanId">
        <foreach collection="list" item="item" separator=";">
            update tb_group_material_plan
            set remark = #{item.remark}
            where group_material_plan_id = #{item.groupMaterialPlanId}

        </foreach>
    </update>
</mapper>