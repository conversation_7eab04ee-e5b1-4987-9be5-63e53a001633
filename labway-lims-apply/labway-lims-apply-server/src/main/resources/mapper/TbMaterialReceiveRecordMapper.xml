<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbMaterialReceiveRecordMapper">


    <insert id="batchAddMaterialReceiveRecords">
        INSERT INTO tb_material_receive_record (
        receive_id,
        inventory_id,
        material_id,
        material_code,
        material_name,
        specification,
        batch_no,
        manufacturers,
        main_unit,
        receive_main_number,
        assist_unit,
        receive_assist_number,
        unit_conversion_rate,
        valid_date,
        receiver_id,
        receiver_name,
        receiver_date,
        status,
        group_id,
        group_name,
        org_id,
        org_name,
        invalid_user_id,
        invalid_user_name,
        invalid_date,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        is_delete,
        material_barcode
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.receiveId},
            #{item.inventoryId},
            #{item.materialId},
            #{item.materialCode},
            #{item.materialName},
            #{item.specification},
            #{item.batchNo},
            #{item.manufacturers},
            #{item.mainUnit},
            #{item.receiveMainNumber},
            #{item.assistUnit},
            #{item.receiveAssistNumber},
            #{item.unitConversionRate},
            #{item.validDate},
            #{item.receiverId},
            #{item.receiverName},
            #{item.receiverDate},
            #{item.status},
            #{item.groupId},
            #{item.groupName},
            #{item.orgId},
            #{item.orgName},
            #{item.invalidUserId},
            #{item.invalidUserName},
            #{item.invalidDate},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete},
            #{item.materialBarcode}
            )
        </foreach>
    </insert>
</mapper>
