<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbRackLogicSpaceMapper">


    <select id="selectOccupiedNumByRackLogicIds"
            resultType="com.labway.lims.apply.api.dto.SampleOccupiedNumByRackLogicId">
        select rack_logic_id, count(1) as occupiedNum
        from tb_rack_logic_space
        where rack_logic_id in
        <foreach collection="rackLogicIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and is_delete = 0
        group by rack_logic_id
    </select>


    <select id="selectSamplePositionByApplySampleIds"
            resultType="com.labway.lims.apply.api.dto.SampleRackPositionDto">
        select trl."position", trls.apply_sample_id, trl.current_group_id,trl.current_group_name
        from tb_rack_logic trl
        inner join tb_rack_logic_space trls on trl.rack_logic_id = trls.rack_logic_id
        where trl.is_delete = 0 and trls.is_delete = 0
          and trls.apply_sample_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectByAllApplySampleId" resultType="com.labway.lims.apply.api.dto.RackLogicSpaceDto">
        select * from tb_rack_logic_space where apply_sample_id = #{applySampleId}
    </select>
</mapper>
