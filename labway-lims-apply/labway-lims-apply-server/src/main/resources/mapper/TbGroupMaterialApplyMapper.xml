<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbGroupMaterialApplyMapper">


    <resultMap id="tbGroupMaterialApplyAndDetailMap" type="com.labway.lims.apply.api.dto.GroupMaterialApplyDto">
        <id column="apply_id" property="applyId"/>
        <result column="apply_no" property="applyNo"/>
        <result column="apply_time" property="applyTime"/>
        <result column="apply_user_id" property="applyUserId"/>
        <result column="apply_user_name" property="applyUserName"/>
        <result column="check_date" property="checkDate"/>
        <result column="checker" property="checker"/>
        <result column="checker_id" property="checkerId"/>
        <result column="create_date" property="createDate"/>
        <result column="creator_id" property="creatorId"/>
        <result column="creator_name" property="creatorName"/>
        <result column="group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="is_delete" property="isDelete"/>
        <result column="org_id" property="orgId"/>
        <result column="org_name" property="orgName"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_no" property="planNo"/>
        <result column="status" property="status"/>
        <result column="update_date" property="updateDate"/>
        <result column="updater_id" property="updaterId"/>
        <result column="updater_name" property="updaterName"/>
        <result column="return_reason" property="returnReason"/>
        <collection property="groupMaterialApplyDetailList" ofType="com.labway.lims.apply.api.dto.GroupMaterialApplyDetailDto">
            <id column="detail_id" property="detailId"/>
            <result column="d_apply_no" property="applyNo"/>
            <result column="material_id" property="materialId"/>
            <result column="material_code" property="materialCode"/>
            <result column="material_name" property="materialName"/>
            <result column="specification" property="specification"/>
            <result column="batch_no" property="batchNo"/>
            <result column="manufacturers" property="manufacturers"/>
            <result column="main_unit" property="mainUnit"/>
            <result column="apply_main_number" property="applyMainNumber"/>
            <result column="assist_unit" property="assistUnit"/>
            <result column="apply_assist_number" property="applyAssistNumber"/>
            <result column="unit_conversion_rate" property="unitConversionRate"/>
            <result column="d_status" property="status"/>
            <result column="d_creator_id" property="creatorId"/>
            <result column="d_creator_name" property="creatorName"/>
            <result column="d_create_date" property="createDate"/>
            <result column="d_update_date" property="updateDate"/>
            <result column="d_updater_name" property="updaterName"/>
            <result column="d_updater_id" property="updaterId"/>
        </collection>

    </resultMap>

    <!-- 查询申领单信息以及明细信息 -->
    <select id="queryTbGroupMaterialApplyAndDetail"
            resultMap = "tbGroupMaterialApplyAndDetailMap">

        select a.apply_id,
               a.apply_no,
               a.apply_time,
               a.apply_user_id,
               a.apply_user_name,
               a.plan_id,
               a.plan_no,
               a.status,
               a.is_delete,
               a.group_id,
               a.group_name,
               a.org_id,
               a.org_name,
               a.create_date,
               a.update_date,
               a.updater_id,
               a.updater_name,
               a.creator_id,
               a.creator_name,
               a.checker_id,
               a.checker,
               a.check_date,
               a.return_reason,

               d.detail_id,
               d.apply_no as d_apply_no,
               d.material_id,
               d.material_code,
               d.material_name,
               d.specification,
               d.batch_no,
               d.manufacturers,
               d.main_unit,
               d.apply_main_number,
               d.assist_unit,
               d.apply_assist_number,
               d.unit_conversion_rate,
        d.status as d_status,
               d.create_date as d_create_date,
               d.update_date as d_update_date,
               d.updater_id as d_updater_id,
               d.updater_name as d_updater_name,
               d.creator_id as d_creator_id,
               d.creator_name as d_creator_name
        from tb_group_material_apply a
                 inner join tb_group_material_apply_detail d on a.apply_no = d.apply_no

        <where>
            a.is_delete = 0 and d.is_delete = 0
            <if test="queryApprovalPlanPageDto.status != null and queryApprovalPlanPageDto.status.size() > 0">
                and a.status in
                <foreach collection="queryApprovalPlanPageDto.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="queryApprovalPlanPageDto.startAuditDate != null">
                and a.check_date >= #{queryApprovalPlanPageDto.startAuditDate}
            </if>
            <if test="queryApprovalPlanPageDto.endAuditDate != null">
                and a.check_date &lt;= #{queryApprovalPlanPageDto.endAuditDate}
            </if>
            <if test="queryApprovalPlanPageDto.groupId != null">
                and a.group_id = #{queryApprovalPlanPageDto.groupId}
            </if>
            <if test="queryApprovalPlanPageDto.planNo != null and queryApprovalPlanPageDto.planNo != ''">
                and a.apply_no like concat('%',#{queryApprovalPlanPageDto.planNo},'%')
            </if>

            <if test="queryApprovalPlanPageDto.materialCodeOrName != null and queryApprovalPlanPageDto.materialCodeOrName != ''">
                and (d.material_code like concat('%',#{queryApprovalPlanPageDto.materialCodeOrName},'%')
                    or d.material_name like concat('%',#{queryApprovalPlanPageDto.materialCodeOrName},'%') )
            </if>

        </where>

        order by a.apply_time asc

    </select>



</mapper>
