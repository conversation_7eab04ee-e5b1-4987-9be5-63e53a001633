<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbMaterialIncomeRecordMapper">


    <insert id="batchAddMaterialIncomeRecords">
        INSERT INTO tb_material_income_record (
        income_id,
        income_no,
        delivery_no,
        delivery_detail_id,
        material_id,
        material_code,
        material_name,
        specification,
        batch_no,
        manufacturers,
        main_unit,
        delivery_main_number,
        income_main_number,
        assist_unit,
        delivery_assist_number,
        income_assist_number,
        unit_conversion_rate,
        valid_date,
        group_id,
        group_name,
        org_id,
        org_name,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        is_delete,
        material_barcode,
        if_storage_qualified,
        if_spec_quantity_consistent,
        if_package_damaged,
        if_valid_date_qualified,
        acceptance_conclusion
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.incomeId},
            #{item.incomeNo},
            #{item.deliveryNo},
            #{item.deliveryDetailId},
            #{item.materialId},
            #{item.materialCode},
            #{item.materialName},
            #{item.specification},
            #{item.batchNo},
            #{item.manufacturers},
            #{item.mainUnit},
            #{item.deliveryMainNumber},
            #{item.incomeMainNumber},
            #{item.assistUnit},
            #{item.deliveryAssistNumber},
            #{item.incomeAssistNumber},
            #{item.unitConversionRate},
            #{item.validDate},
            #{item.groupId},
            #{item.groupName},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete},
            #{item.materialBarcode},
            #{item.ifStorageQualified},
            #{item.ifSpecQuantityConsistent},
            #{item.ifPackageDamaged},
            #{item.ifValidDateQualified},
            #{item.acceptanceConclusion}
            )
        </foreach>

    </insert>

</mapper>
