<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbPhysicalRegisterMapper">

    <insert id="batchAddPhysicalRegisters">
        INSERT INTO tb_physical_register (
        physical_register_id,
        patient_name,
        patient_age,
        patient_subage,
        patient_subage_unit,
        patient_birthday,
        patient_card,
        patient_card_type,
        patient_sex,
        patient_mobile,
        patient_address,
        sample_sort,
        test_package,
        remark,
        dept,
        patient_bed,
        physical_company_id,
        physical_company_name,
        physical_batch_id,
        org_id,
        org_name,
        create_date,
        update_date,
        creator_name,
        creator_id,
        updater_name,
        updater_id,
        is_delete,
        applicant,
        is_print,
        apply_type_code,
        apply_type_name,
        patient_visit_card,
        test_package_desc,
        diagnosis
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.physicalRegisterId},
            #{item.patientName},
            #{item.patientAge},
            #{item.patientSubage},
            #{item.patientSubageUnit},
            #{item.patientBirthday},
            #{item.patientCard},
            #{item.patientCardType},
            #{item.patientSex},
            #{item.patientMobile},
            #{item.patientAddress},
            #{item.sampleSort},
            #{item.testPackage},
            #{item.remark},
            #{item.dept},
            #{item.patientBed},
            #{item.physicalCompanyId},
            #{item.physicalCompanyName},
            #{item.physicalBatchId},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorName},
            #{item.creatorId},
            #{item.updaterName},
            #{item.updaterId},
            #{item.isDelete},
            #{item.applicant},
            #{item.isPrint},
            #{item.applyTypeCode},
            #{item.applyTypeName},
            #{item.patientVisitCard},
            #{item.testPackageDesc},
            #{item.diagnosis}
            )
        </foreach>
    </insert>
</mapper>
