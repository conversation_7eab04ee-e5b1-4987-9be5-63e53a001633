<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbSampleAbnormalMapper">

    <insert id="batchAddTbSampleAbnormals">
        INSERT INTO tb_sample_abnormal (
        sample_abnormal_id,
        barcode,
        apply_id,
        hsp_org_id,
        hsp_org_name,
        test_item_name,
        send_doctor_name,
        patient_name,
        patient_sex,
        patient_age,
        regist_group_id,
        regist_group_name,
        regist_date,
        register_id,
        register_name,
        abnormal_reason_code,
        abnormal_reason_name,
        regist_content,
        handle_group_id,
        handle_group_name,
        handle_user_id,
        handle_user_name,
        handle_content,
        handle_date,
        confirm_group_id,
        confirm_group_name,
        confirm_user_id,
        confirm_user_name,
        confirm_content,
        confirm_date,
        status,
        is_delete,
        create_date,
        update_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name,
        org_id,
        org_name,
        images
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.sampleAbnormalId},
            #{item.barcode},
            #{item.applyId},
            #{item.hspOrgId},
            #{item.hspOrgName},
            #{item.testItemName},
            #{item.sendDoctorName},
            #{item.patientName},
            #{item.patientSex},
            #{item.patientAge},
            #{item.registGroupId},
            #{item.registGroupName},
            #{item.registDate},
            #{item.registerId},
            #{item.registerName},
            #{item.abnormalReasonCode},
            #{item.abnormalReasonName},
            #{item.registContent},
            #{item.handleGroupId},
            #{item.handleGroupName},
            #{item.handleUserId},
            #{item.handleUserName},
            #{item.handleContent},
            #{item.handleDate},
            #{item.confirmGroupId},
            #{item.confirmGroupName},
            #{item.confirmUserId},
            #{item.confirmUserName},
            #{item.confirmContent},
            #{item.confirmDate},
            #{item.status},
            #{item.isDelete},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.orgId},
            #{item.orgName},
            #{item.images}
            )
        </foreach>
    </insert>
    <update id="updateBySampleAbnormalIds">
        update tb_sample_abnormal
        <set>
            <if test="sampleAbnormalDto.status != null">
                status = #{sampleAbnormalDto.status},
            </if>
        </set>
        where sample_abnormal_id in
        <foreach collection="sampleAbnormalIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectSampleAbnormalStatistics" resultType="com.labway.lims.apply.api.dto.SampleAbnormalStatisticsDto">
        select tsa.*
        from tb_sample_abnormal tsa
        inner join tb_apply_sample tas on tsa.barcode = tas.barcode
        inner join tb_apply ta on ta.apply_id = tas.apply_id
        where ta.sign_date >= #{beginSignDate}
          and ta.sign_date &lt;= #{endSignDate}
          and tas.org_id = #{orgId}
    </select>


    <select id="selectSampleAbnormalStatisticsBySendDate"
            resultType="com.labway.lims.apply.api.dto.SampleAbnormalStatisticsDto">
        select tsa.*,
               tas.sample_property_code,
               tas.sample_property,
               tas.group_id,
               tas.group_name,
               tas.sample_type_code,
               tas.sample_type_name,
               ta.apply_type_code,
               ta.apply_type_name,
               tas.create_date as send_date,
               ta.patient_visit_card,
               ta.patient_bed,
                ta.dept
        from tb_sample_abnormal tsa
                 inner join tb_apply_sample tas on tsa.barcode = tas.barcode
                 inner join tb_apply ta on ta.apply_id = tas.apply_id
        where ta.create_date >= #{beginSendDate}
          and ta.create_date &lt;= #{endSendDate}
          and tas.org_id = #{orgId}
        order by ta.create_date
    </select>
</mapper>
