<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbPhysicalSampleMapper">

    <insert id="batchAddPhysicalSample">
        INSERT INTO tb_physical_sample (
        physical_sample_id,
        physical_register_id,
        physical_batch_id,
        sample_type,
        barcode,
        tube,
        physical_company_id,
        physical_company_name,
        sample_count,
        org_id,
        org_name,
        status,
        apply_id,
        create_date,
        update_date,
        creator_name,
        creator_id,
        updater_name,
        updater_id,
        is_delete,
        sampling_date
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.physicalSampleId},
            #{item.physicalRegisterId},
            #{item.physicalBatchId},
            #{item.sampleType},
            #{item.barcode},
            #{item.tube},
            #{item.physicalCompanyId},
            #{item.physicalCompanyName},
            #{item.sampleCount},
            #{item.orgId},
            #{item.orgName},
            #{item.status},
            #{item.applyId},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorName},
            #{item.creatorId},
            #{item.updaterName},
            #{item.updaterId},
            #{item.isDelete},
            #{item.samplingDate}
            )
        </foreach>
    </insert>
</mapper>
