<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbRackLogicMapper">

    <select id="selectOnePickWaitingHandoverRackLogics"
            resultType="com.labway.lims.apply.bo.OnePickWaitingHandoverRackLogicBo">
        select
            tas.apply_sample_id,
            tas.is_disabled,
            trl.rack_id,
            trl.rack_logic_id,
            trl.rack_code,
            trl.next_group_id,
            trl.update_date,
            trl.next_group_name,
            tas.one_picker_name,
            tas.one_pick_date
        from tb_rack_logic trl
        inner join tb_rack_logic_space trls on trl.rack_logic_id = trls.rack_logic_id and trls.is_delete = 0
        inner join tb_apply_sample tas on trls.apply_sample_id = tas.apply_sample_id and tas.is_delete = 0
        where trl.is_delete = 0
        and tas.is_one_pick = 1
        and trl."position" = 2
        and tas.one_pick_date >= #{beginOnePickDate}
        and tas.one_pick_date &lt;= #{endOnePickDate}
        <if test="groupId != null and groupId > 0">
            and trl.next_group_id = #{groupId}
        </if>
    </select>

    <select id="selectOnePickWaitingHandoverRackLogicsByGroupIds"
            resultType="com.labway.lims.apply.bo.OnePickWaitingHandoverRackLogicBo">
        select
            tas.apply_sample_id,
            tas.is_disabled,
            trl.rack_id,
            trl.rack_logic_id,
            trl.rack_code,
            trl.next_group_id,
            trl.update_date,
            trl.next_group_name,
            tas.one_picker_name,
            tas.one_pick_date
        from tb_rack_logic trl
        inner join tb_rack_logic_space trls on trl.rack_logic_id = trls.rack_logic_id and trls.is_delete = 0
        inner join tb_apply_sample tas on trls.apply_sample_id = tas.apply_sample_id and tas.is_delete = 0
        where trl.is_delete = 0
        and tas.is_one_pick = 1
        and trl."position" = 2
        and tas.one_pick_date >= #{beginOnePickDate}
        and tas.one_pick_date &lt;= #{endOnePickDate}
        <if test="groupIds != null">
            and trl.next_group_id in
            <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
    </select>

    <select id="selectByApplySampleId" resultType="com.labway.lims.apply.api.dto.RackLogicDto">
        select trl.* from tb_rack_logic trl
        inner join tb_rack_logic_space trls on trl.rack_logic_id = trls.rack_logic_id and trls.is_delete = 0
        inner join tb_apply_sample tas on trls.apply_sample_id = tas.apply_sample_id and tas.is_delete = 0
        where trl.is_delete = 0 and trls.apply_sample_id = #{applySampleId}
    </select>
</mapper>
