<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbGroupMaterialApplyDetailMapper">


    <insert id="addBatch" parameterType="java.util.List">
        insert into tb_group_material_apply_detail (
        detail_id, apply_no, material_id, material_code, material_name, specification,
        batch_no, manufacturers, main_unit, apply_main_number, assist_unit, apply_assist_number,
        unit_conversion_rate, org_id, org_name, create_date, update_date, updater_id, updater_name,
        creator_id, creator_name, is_delete
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.detailId}, #{item.applyNo}, #{item.materialId}, #{item.materialCode}, #{item.materialName},
            #{item.specification}, #{item.batchNo}, #{item.manufacturers}, #{item.mainUnit}, #{item.applyMainNumber},
            #{item.assistUnit}, #{item.applyAssistNumber}, #{item.unitConversionRate}, #{item.orgId},
            #{item.orgName}, #{item.createDate}, #{item.updateDate}, #{item.updaterId}, #{item.updaterName},
            #{item.creatorId}, #{item.creatorName}, #{item.isDelete}
            )
        </foreach>
    </insert>


</mapper>
