<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.ApplySampleImageMapper">

    <resultMap type="com.labway.lims.apply.model.ApplySampleImage" id="ApplySampleImageMap">
        <result property="applySampleImageId" column="apply_sample_image_id" jdbcType="INTEGER"/>
        <result property="applyId" column="apply_id" jdbcType="INTEGER"/>
        <result property="applySampleId" column="apply_sample_id" jdbcType="INTEGER"/>
        <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="INTEGER"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updaterName" column="updater_name" jdbcType="VARCHAR"/>
        <result property="imageName" column="image_name" jdbcType="VARCHAR"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ApplySampleImageMap">
        select
apply_sample_image_idapply_idapply_sample_idimage_urlis_deletecreate_datecreator_idupdate_dateupdater_idupdater_nameimage_namecreator_name
        from tb_apply_sample_image
        where apply_sample_image_id = #{applySampleImageId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ApplySampleImageMap">
        select
apply_sample_image_idapply_idapply_sample_idimage_urlis_deletecreate_datecreator_idupdate_dateupdater_idupdater_nameimage_namecreator_name
        from tb_apply_sample_image
        <where>
            <if test="applySampleImageId != null">
                and apply_sample_image_id = #{applySampleImageId}
            </if>
            <if test="applyId != null">
                and apply_id = #{applyId}
            </if>
            <if test="applySampleId != null">
                and apply_sample_id = #{applySampleId}
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                and image_url = #{imageUrl}
            </if>
            <if test="isDelete != null">
                and is_delete = #{isDelete}
            </if>
            <if test="createDate != null">
                and create_date = #{createDate}
            </if>
            <if test="creatorId != null">
                and creator_id = #{creatorId}
            </if>
            <if test="updateDate != null">
                and update_date = #{updateDate}
            </if>
            <if test="updaterId != null">
                and updater_id = #{updaterId}
            </if>
            <if test="updaterName != null and updaterName != ''">
                and updater_name = #{updaterName}
            </if>
            <if test="imageName != null and imageName != ''">
                and image_name = #{imageName}
            </if>
            <if test="creatorName != null and creatorName != ''">
                and creator_name = #{creatorName}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from tb_apply_sample_image
        <where>
            <if test="applySampleImageId != null">
                and apply_sample_image_id = #{applySampleImageId}
            </if>
            <if test="applyId != null">
                and apply_id = #{applyId}
            </if>
            <if test="applySampleId != null">
                and apply_sample_id = #{applySampleId}
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                and image_url = #{imageUrl}
            </if>
            <if test="isDelete != null">
                and is_delete = #{isDelete}
            </if>
            <if test="createDate != null">
                and create_date = #{createDate}
            </if>
            <if test="creatorId != null">
                and creator_id = #{creatorId}
            </if>
            <if test="updateDate != null">
                and update_date = #{updateDate}
            </if>
            <if test="updaterId != null">
                and updater_id = #{updaterId}
            </if>
            <if test="updaterName != null and updaterName != ''">
                and updater_name = #{updaterName}
            </if>
            <if test="imageName != null and imageName != ''">
                and image_name = #{imageName}
            </if>
            <if test="creatorName != null and creatorName != ''">
                and creator_name = #{creatorName}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="applySampleImageId" useGeneratedKeys="true">
        insert into tb_apply_sample_image(apply_id, apply_sample_id, image_url, is_delete, create_date, creator_id, update_date, updater_id, updater_name, image_name, creator_name)
        values (#{applyId},#{applySampleId},#{imageUrl},#{isDelete},#{createDate},#{creatorId},#{updateDate},#{updaterId},#{updaterName},#{imageName},#{creatorName})
    </insert>

    <insert id="insertBatch" keyProperty="applySampleImageId" useGeneratedKeys="true">
        insert into
        tb_apply_sample_image(apply_sample_image_id,apply_id,apply_sample_id,image_url,is_delete,create_date,creator_id,update_date,updater_id,updater_name,image_name,creator_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.applySampleImageId},#{entity.applyId},#{entity.applySampleId},#{entity.imageUrl},#{entity.isDelete},#{entity.createDate},#{entity.creatorId},#{entity.updateDate},#{entity.updaterId},#{entity.updaterName},#{entity.imageName},#{entity.creatorName})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="applySampleImageId" useGeneratedKeys="true">
        insert into tb_apply_sample_image(apply_id,apply_sample_id,image_url,is_delete,create_date,creator_id,update_date,updater_id,updater_name,image_name,creator_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.applyId},#{entity.applySampleId},#{entity.imageUrl},#{entity.isDelete},#{entity.createDate},#{entity.creatorId},#{entity.updateDate},#{entity.updaterId},#{entity.updaterName},#{entity.imageName},#{entity.creatorName})
        </foreach>
        on duplicate key update
apply_id = values(apply_id)apply_sample_id = values(apply_sample_id)image_url = values(image_url)is_delete = values(is_delete)create_date = values(create_date)creator_id = values(creator_id)update_date = values(update_date)updater_id = values(updater_id)updater_name = values(updater_name)image_name = values(image_name)creator_name = values(creator_name)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tb_apply_sample_image
        <set>
            <if test="applyId != null">
                apply_id = #{applyId},
            </if>
            <if test="applySampleId != null">
                apply_sample_id = #{applySampleId},
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                image_url = #{imageUrl},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId},
            </if>
            <if test="updaterName != null and updaterName != ''">
                updater_name = #{updaterName},
            </if>
            <if test="imageName != null and imageName != ''">
                image_name = #{imageName},
            </if>
            <if test="creatorName != null and creatorName != ''">
                creator_name = #{creatorName},
            </if>
        </set>
        where apply_sample_image_id = #{applySampleImageId}
    </update>


</mapper>

