<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbApplyLogisticsMapper">

<select id="selectByApplyIds" resultType="com.labway.lims.apply.api.dto.SimpleLogisticsSampleDto">
    select tal.apply_logistics_id,
    tal.receive_date,
    tal.master_barcode,
    tal.apply_image,
    tal.hsp_org_name,
    tal.logistics_user_name,
    tals.apply_id,
    tals.barcode,
    tals.apply_logistics_sample_id,
    tals.status
    from tb_apply_logistics tal
    inner join tb_apply_logistics_sample tals
    on tal.apply_logistics_id = tals.apply_logistics_id
    where tal.is_delete = 0
    and tals.is_delete = 0
    and tals.apply_id in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
        #{item}
    </foreach>
</select>
</mapper>
