<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbMaterialInventoryMapper">


    <insert id="batchAddMaterialInventory">
        INSERT INTO tb_material_inventory (
        inventory_id,
        material_id,
        material_code,
        material_name,
        specification,
        batch_no,
        manufacturers,
        main_unit,
        main_unit_inventory,
        assist_unit,
        assist_unit_inventory,
        unit_conversion_rate,
        valid_date,
        group_id,
        group_name,
        org_id,
        org_name,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        is_delete,
        material_barcode
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.inventoryId},
            #{item.materialId},
            #{item.materialCode},
            #{item.materialName},
            #{item.specification},
            #{item.batchNo},
            #{item.manufacturers},
            #{item.mainUnit},
            #{item.mainUnitInventory},
            #{item.assistUnit},
            #{item.assistUnitInventory},
            #{item.unitConversionRate},
            #{item.validDate},
            #{item.groupId},
            #{item.groupName},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete},
            #{item.materialBarcode}
            )
        </foreach>
    </insert>
</mapper>
