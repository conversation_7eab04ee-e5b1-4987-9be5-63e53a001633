<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbApplySampleMapper">

    <insert id="insertBatch">
        insert into tb_apply_sample (apply_sample_id, apply_id, barcode, out_barcode, tube_code,tube_name,
        sample_type_code,sample_type_name, group_id, group_name, rack_id, one_picker_id, one_picker_name, one_pick_date,
        is_one_pick,
        is_split_blood,
        is_delete, `status`, urgent, create_date, update_date, creator_id, creator_name,
        updater_id,
        updater_name,printer_id,printer_name,print_date,splitter_id,splitter_name,split_date,tester_id,tester_name
        ,two_picker_id,two_picker_name,two_pick_date,is_two_pick,is_immunity_two_pick
        ,org_id,org_name
        )
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.applySampleId},
            #{item.applyId},
            #{item.barcode},
            #{item.outBarcode},
            #{item.tubeCode},
            #{item.tubeName},
            #{item.sampleTypeCode},
            #{item.sampleTypeName},
            #{item.groupId},
            #{item.groupName},
            #{item.rackId},
            #{item.onePickerId},
            #{item.onePickerName},
            #{item.onePickDate},
            #{item.isOnePick},
            #{item.isSplitBlood},
            #{item.isDelete},
            #{item.status},
            #{item.urgent},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.printerId},
            #{item.printerName},
            #{item.printDate},
            #{item.splitterId},
            #{item.splitterName},
            #{item.splitDate},
            #{item.testerId},
            #{item.testerName},

            #{item.twoPickerId},
            #{item.twoPickerName},
            #{item.twoPickDate},
            #{item.isTwoPick},
            #{item.isImmunityTwoPick},

            #{item.orgId},
            #{item.orgName}
            )
        </foreach>
    </insert>

    <select id="selectWaitingOnePickSamples" resultType="com.labway.lims.apply.api.dto.WaitingOnePickApplySampleDto">
        select tsa.*,ta.hsp_org_id,ta.hsp_org_name
        from tb_apply_sample tsa
        inner join tb_apply ta on tsa.apply_id = ta.apply_id
        where ta.sign_date >= #{beginSignDate}
        and ta.sign_date &lt;= #{endSignDate}
        <!--已复核和已双输复核都是已经复核-->
        and (ta.status = 3 or ta.status = 4)
        and(tsa.status != 99 )
        and tsa.is_one_pick = 0
        and tsa.is_delete = 0
        and ta.org_id = #{orgId}
    </select>

    <select id="selectWaitingSplitBloodSamples"
            resultType="com.labway.lims.apply.api.dto.WaitingSplitBloodApplySampleDto">
        select trl.rack_code,
               trl.rack_logic_id,
               tsa.barcode,
               tsa.sample_type_code,
               tsa.sample_type_name,
               tsa.tube_code,
               tsa.one_pick_date,
               tsa.tube_name,
               ta.patient_name,
               ta.patient_age,
               ta.patient_sex,
               ta.patient_subage,
               ta.patient_subage_unit,
               ta.hsp_org_name,
               ta.apply_id,
               tsa.apply_sample_id
        from tb_apply_sample tsa
                 inner join tb_rack_logic_space trls
                            on tsa.apply_sample_id = trls.apply_sample_id and trls.is_delete = 0
                 inner join tb_rack_logic trl on trls.rack_logic_id = trl.rack_logic_id and trls.is_delete = 0
                 inner join tb_apply ta on tsa.apply_id = ta.apply_id
        where trl.update_date >= #{beginReceiveDate}
          and trl.update_date &lt;= #{endReceiveDate}
          and tsa.is_delete = 0
          and trl.current_group_id = #{groupId}
          and tsa.is_split_blood = 0
          and trl."position" = 3
          and tsa.status != 99
    </select>

    <select id="selectSplitBloodSamples" resultType="com.labway.lims.apply.api.dto.SplitBloodApplySampleDto">
        select tas.apply_sample_id,
               tas.barcode,
               tas.group_id,
               tas.group_name,
               tas.sample_type_code,
               tas.sample_type_name,
               tas.tube_code,
               tas.tube_name,
               tas.splitter_id,
               tas.splitter_name,
               tas.split_date,
               ta.patient_subage_unit,
               ta.patient_subage,
               ta.patient_age,
               ta.patient_name,
               ta.patient_sex,
               ta.hsp_org_id,
               ta.hsp_org_name,
               trl.rack_code,
               trl.next_group_id,
               trl.rack_logic_id
        from tb_apply ta
                 inner join tb_apply_sample tas on tas.apply_id = ta.apply_id and tas.is_delete = 0
                 inner join tb_rack_logic_space trls
                            on tas.apply_sample_id = trls.apply_sample_id and trls.is_delete = 0
                 inner join tb_rack_logic trl on trls.rack_logic_id = trl.rack_logic_id and trl.is_delete = 0
        where tas.is_split_blood = 1
          and trl."position" = 4
          and tas.split_date >= #{beginSplitDate}
          and tas.split_date &lt;= #{endSplitDate}
          and ta.is_delete = 0

    </select>
    <select id="selectAfterSplitBloodSamples" resultType="com.labway.lims.apply.api.dto.SplitBloodApplySampleDto">
        select tas.apply_sample_id,
               tas.barcode,
               tas.group_id,
               tas.group_name,
               tas.sample_type_code,
               tas.sample_type_name,
               tas.tube_code,
               tas.tube_name,
               tas.splitter_id,
               tas.splitter_name,
               tas.split_date,
               ta.patient_subage_unit,
               ta.patient_subage,
               ta.patient_age,
               ta.patient_name,
               ta.patient_sex,
               ta.hsp_org_id,
               ta.hsp_org_name,
               trl.rack_code,
               trl.next_group_id,
               trl.rack_logic_id,
               trls.update_date operate_date
        from tb_apply ta
                 inner join tb_apply_sample tas on tas.apply_id = ta.apply_id and tas.is_delete = 0
                 inner join tb_rack_logic_space trls
                            on tas.apply_sample_id = trls.apply_sample_id
                 inner join tb_rack_logic trl on trls.rack_logic_id = trl.rack_logic_id
        where tas.is_split_blood = 1
          <!--查询已分血 和 状态流转后的数据-->
          and trl."position" > 4
          and tas.split_date >= #{beginSplitDate}
          and tas.split_date &lt;= #{endSplitDate}
          and ta.is_delete = 0
    </select>

    <select id="selectWaitingTwoPickSamples" resultType="com.labway.lims.apply.api.dto.WaitingTwoPickApplySampleDto">
        select
        tas.*,
        ta.patient_name,
        ta.patient_sex,
        ta.patient_age,
        ta.patient_subage,
        ta.patient_subage_unit,
        trl.rack_code,
        trl.rack_logic_id,
        trl.update_date as rack_logic_update_date
        from tb_apply_sample tas
        inner join tb_apply ta on tas.apply_id = ta.apply_id
        inner join tb_rack_logic_space trls on tas.apply_sample_id = trls.apply_sample_id and trls.is_delete = 0
        inner join tb_rack_logic trl on trls.rack_logic_id = trl.rack_logic_id and trls.is_delete = 0
        where tas.is_delete = 0
        and trl."position" = 5
        and tas."is_two_pick" = 0
        and tas.is_outsourcing = 0
        <!--过滤掉终止检验-->
        and tas.status != 99
        <if test="itemType != null and itemType != ''">
            and tas.item_type = #{itemType}
        </if>
        <if test="excludeItemTypes != null and excludeItemTypes.size() > 0">
            and tas.item_type not in
            <foreach collection="excludeItemTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="beginReceiveDate != null">
        and trl.update_date >= #{beginReceiveDate}
        </if>
        <if test="endReceiveDate != null">
        and trl.update_date &lt;= #{endReceiveDate}
        </if>
        and tas.org_id = #{orgId} and trl.current_group_id = #{groupId}
    </select>

    <select id="selectTwoPickedSamples" resultType="com.labway.lims.apply.api.dto.TwoPickedApplySampleDto">
        select tas.barcode,
               tas.apply_sample_id,
               tas.apply_id,
               tas.group_id,
               tas.group_name,
               ta.patient_subage_unit,
               ta.patient_subage,
               ta.patient_age,
               ta.patient_sex,
               ta.patient_name,

               tas.two_pick_date,
               tas.two_picker_id,
               tas.two_picker_name,
               tas.is_immunity_two_pick,

               tas.tube_code,
               tas.tube_name,
               tas.sample_type_code,
               tas.sample_type_name,
               tas.item_type,
               tas.status,
               ta.hsp_org_name,
               ta.hsp_org_id
        from tb_apply_sample tas
                 inner join tb_apply ta on tas.apply_id = ta.apply_id and ta.is_delete = 0
        where tas.is_delete = 0
          and tas.is_two_pick = 1
        and tas.is_outsourcing = 0
          <if test="itemType != null and itemType != ''">
              and tas.item_type = #{itemType}
          </if>
          <if test="excludeItemTypes != null and excludeItemTypes.size() > 0">
              and tas.item_type not in
              <foreach collection="excludeItemTypes" item="item" open="(" close=")" separator=",">
                  #{item}
              </foreach>
          </if>
          and tas.two_pick_date >= #{beginTwoPickedDate}
          and tas.two_pick_date &lt;= #{endTwoPickedDate}
          and ta.org_id = #{orgId}
          and tas.group_id = #{groupId}
    </select>

    <select id="selectByRackLogicIds" resultType="com.labway.lims.apply.api.dto.RackLogicApplySampleDto">
        select tas.*, trl.rack_id, trl.rack_logic_id, trl."position", trl.rack_code, trls."column", trls."row"
        from tb_apply_sample tas
        inner join tb_rack_logic_space trls on tas.apply_sample_id = trls.apply_sample_id and trls.is_delete = 0
        inner join tb_rack_logic trl on trls.rack_logic_id = trl.rack_logic_id and trl.is_delete = 0
        where tas.is_delete = 0 and tas.status != 99 and trl.rack_logic_id in (
        <foreach collection="rackLogicIds" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

    <select id="selectOutsourcingUnPickApplySamples"
            resultType="com.labway.lims.apply.api.dto.OutsourcingApplySampleDto">
        select tas.*,
               ta.patient_name,
               ta.patient_age,
               ta.patient_subage,
               ta.patient_sex,
               ta.patient_subage_unit,
               ta.hsp_org_name,
               ta.hsp_org_id
        from tb_apply_sample tas
                 inner join tb_apply ta on tas.apply_id = ta.apply_id
                 inner join tb_rack_logic_space trls on tas.apply_sample_id = trls.apply_sample_id
                 inner join tb_rack_logic trl on trls.rack_logic_id = trl.rack_logic_id
        where tas.is_delete = 0
          and ta.is_delete = 0
          and tas.is_outsourcing = 1
          and tas.is_two_pick = 0
          and tas.status != 99
          and trl."position" = 5
          and trl.update_date >= #{beginReceiveDate}
          and trl.update_date &lt;= #{endReceiveDate}
          and trl.current_group_id = #{groupId}
    </select>

    <select id="selectOutsourcingPickedApplySamples"
            resultType="com.labway.lims.apply.api.dto.OutsourcingApplySampleDto">
        select tas.*,
               ta.patient_name,
               ta.patient_age,
               ta.patient_subage,
               ta.patient_subage_unit,
               ta.hsp_org_name,
               ta.hsp_org_id
        from tb_apply_sample tas
                 inner join tb_apply ta on tas.apply_id = ta.apply_id and ta.is_delete = 0
        where tas.is_delete = 0
          and tas.is_outsourcing = 1
          and tas.is_two_pick = 1
          and tas.two_pick_date >= #{beginPickDate}
          and tas.two_pick_date &lt;= #{endPickDate}
          and ta.org_id = #{orgId}
    </select>

    <select id="selectOutsourcingListSamples"
            resultType="com.labway.lims.apply.api.dto.OutsourcingApplySampleDto">
        select tas.*,
               ta.patient_name,
               ta.patient_age,
               ta.patient_subage,
               ta.patient_subage_unit,
               ta.hsp_org_name,
               ta.hsp_org_id
        from tb_apply_sample tas
                 inner join tb_apply ta on tas.apply_id = ta.apply_id and ta.is_delete = 0
        where tas.is_delete = 0
          and tas.is_outsourcing = 1
          and tas.is_two_pick = 1
          <if test="groupId != null">
              and tas.group_id = #{groupId}
          </if>
          and ta.create_date >= #{beginPickDate}
          and ta.create_date &lt;= #{endPickDate}
          and ta.org_id = #{orgId}
        union all
        <!--V1.1.0.3 查询 完成 分拣后交接 或者 分血后交接的数据-->
        select tas.*,
               ta.patient_name,
               ta.patient_age,
               ta.patient_subage,
               ta.patient_subage_unit,
               ta.hsp_org_name,
               ta.hsp_org_id
        from tb_apply_sample tas
                 inner join tb_apply ta on tas.apply_id = ta.apply_id and ta.is_delete = 0
        where exists (select trls.apply_sample_id
                      from tb_rack_logic_space trls
                               inner join tb_rack_logic trl on trls.rack_logic_id = trl.rack_logic_id and trl.is_delete = 0
                      where trls.apply_sample_id = tas.apply_sample_id
                        and trls.is_delete = 0
                        <!--RackLogicPositionEnum 5,二次分拣中" 7,组间交接中 -->
                        and trl."position" in (5, 7))
          and tas.is_delete = 0
          and tas.is_outsourcing = 1
          and tas.is_one_pick = 1
          and tas.is_two_pick = 0
          and tas.create_date >= #{beginPickDate}
          and tas.create_date &lt;= #{endPickDate}
          <if test="groupId != null">
              and tas.group_id = #{groupId}
          </if>
          and ta.org_id = #{orgId}
    </select>

    <update id="updateByApplySampleIds">
        update tb_apply_sample
        <set>
            <if test="applySample.printerId != null">
                printer_id = #{applySample.printerId},
            </if>
            <if test="applySample.printerName != null">
                printer_name = #{applySample.printerName},
            </if>
            <if test="applySample.printDate != null">
                print_date = #{applySample.printDate},
            </if>
            <if test="applySample.isPrint != null">
                is_print = #{applySample.isPrint},
            </if>
            <if test="applySample.groupId != null">
                group_id = #{applySample.groupId},
            </if>
            <if test="applySample.groupName != null">
                group_name = #{applySample.groupName},
            </if>
            <if test="applySample.isTwoPick != null">
                is_two_pick = #{applySample.isTwoPick},
            </if>
            <if test="applySample.twoPickDate != null">
                two_pick_date = #{applySample.twoPickDate},
            </if>
            <if test="applySample.twoPickerId != null">
                two_picker_id = #{applySample.twoPickerId},
            </if>
            <if test="applySample.twoPickerName != null">
                two_picker_name = #{applySample.twoPickerName},
            </if>
            <if test="applySample.isImmunityTwoPick != null">
                is_immunity_two_pick = #{applySample.isImmunityTwoPick},
            </if>
            <if test="applySample.status != null">
                status = #{applySample.status},
            </if>
            <if test="applySample.isArchive != null">
                is_archive = #{applySample.isArchive},
            </if>
            <if test="applySample.sampleRemark != null">
                sample_remark = #{applySample.sampleRemark},
            </if>
            <if test="applySample.resultRemark != null">
                result_remark = #{applySample.resultRemark},
            </if>
            <if test="applySample.testerId != null">
                tester_id = #{applySample.testerId},
            </if>
            <if test="applySample.testerName != null">
                tester_name = #{applySample.testerName},
            </if>
            <if test="applySample.itemType != null">
                item_type = #{applySample.itemType},
            </if>
            <if test="applySample.rackId != null">
                rack_id = #{applySample.rackId},
            </if>
            <if test="applySample.colorMarking != null">
                color_marking = #{applySample.colorMarking},
            </if>
            <if test="applySample.isPrintList != null">
                is_print_list = #{applySample.isPrintList},
            </if>
            <if test="applySample.printListDate != null">
                print_list_date = #{applySample.printListDate},
            </if>
            <if test="applySample.isOutsourcing != null">
                is_outsourcing = #{applySample.isOutsourcing},
            </if>
        </set>
        where apply_sample_id in
        <foreach collection="applySampleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectOnePickedSamples" resultType="com.labway.lims.apply.api.dto.OnePickedApplySampleDto">
        select tas.*, trl.rack_code, trls."row", trls."column"
        from tb_apply_sample tas
                 inner join tb_rack_logic_space trls
                            on tas.apply_sample_id = trls.apply_sample_id and trls.is_delete = 0
                 inner join tb_rack_logic trl on trls.rack_logic_id = trl.rack_logic_id and trl.is_delete = 0
        where tas.is_delete = 0
          and tas.org_id = #{orgId}
          and tas.is_one_pick = 1
          and trl."position" = 2
          and tas.one_pick_date >= #{beginOnePickDate}
          and tas.one_pick_date &lt;= #{endOnePickDate}
          and tas.org_id = #{orgId}
    </select>

    <select id="selectAfterOnePickedSamples" resultType="com.labway.lims.apply.api.dto.OnePickedApplySampleDto">
        select tas.*, trl.rack_code, trls."row", trls."column", trls.update_date operate_date
        from tb_apply_sample tas
                 inner join tb_rack_logic_space trls
                            on tas.apply_sample_id = trls.apply_sample_id
                 inner join tb_rack_logic trl on trls.rack_logic_id = trl.rack_logic_id
        where tas.is_delete = 0
          and tas.org_id = #{orgId}
          and tas.is_one_pick = 1
          <!--and trl."position" = 2-->
          <!--查询已分拣 和 状态流转后的数据-->
          and trl."position" > 2
          and tas.one_pick_date >= #{beginOnePickDate}
          and tas.one_pick_date &lt;= #{endOnePickDate}
          and tas.org_id = #{orgId}
    </select>
    <select id="selectByOutBarcodeAndHspOrgId" resultType="com.labway.lims.apply.api.dto.ApplySampleDto">

        select ta.apply_id, tas.*
        from tb_apply ta
                 inner join tb_apply_sample tas on ta.apply_id = tas.apply_id
        where tas.out_barcode = #{outBarcode}
          and ta.hsp_org_id = #{hspOrgId}
          and ta.is_delete = 0
          and tas.is_delete = 0

    </select>

    <select id="selectByRackIds" resultType="com.labway.lims.apply.api.dto.RackLogicApplySampleDto">
        select tas.*, trl.rack_id, trl.rack_logic_id, trl."position", trl.rack_code, trls."column", trls."row"
        from tb_apply_sample tas
        inner join tb_rack_logic_space trls on tas.apply_sample_id = trls.apply_sample_id and trls.is_delete = 0
        inner join tb_rack_logic trl on trls.rack_logic_id = trl.rack_logic_id and trl.is_delete = 0
        where tas.is_delete = 0 and tas.status != 99 and trl.rack_id in (
        <foreach collection="rackIds" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="selectByOutBarcodeAndHspOrgCode" resultType="com.labway.lims.apply.api.dto.ApplySampleDto">
        select ta.apply_id, tas.*
        from tb_apply ta
                 inner join tb_apply_sample tas on ta.apply_id = tas.apply_id
        where tas.out_barcode = #{outBarcode}
          and ta.hsp_org_code = #{hspOrgCode}
          and ta.is_delete = 0
          and tas.is_delete = 0

    </select>
    <select id="selectAllByApplySampleIdsAndMasterBarCodeNotNull"
            resultType="com.labway.lims.apply.model.TbApplySample">
        select * from tb_apply_sample
        where (is_delete = 0 or (is_delete=1 and merge_master_barcode is not null))
          and
            apply_sample_id in
        <foreach collection="applySampleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectApplySampleItemTwoPickDetailByBarcode"
            resultType="com.labway.lims.apply.api.dto.ApplySampleItemTwoPickDetailDto">
        select tas.apply_sample_id,
               tas.barcode,
               tasi.test_item_code,
               tasi.test_item_name,
               tasi.stop_status,
               tas.is_two_pick,
               tas.two_pick_date,
               tas.status
        from tb_apply_sample tas,
             tb_apply_sample_item tasi
        where tas.apply_sample_id = tasi.apply_sample_id
          and tasi.is_delete = 0
          and tas.barcode = #{barcode}
          and tas.is_delete = 0;
    </select>
</mapper>
