<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbUploadFileMapper">

    <resultMap id="BaseResultMap" type="com.labway.lims.apply.model.TbUploadFile">
            <id property="fileId" column="file_id" jdbcType="BIGINT"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="SMALLINT"/>
            <result property="status" column="status" jdbcType="SMALLINT"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="info" column="info" jdbcType="VARCHAR"/>
            <result property="uploaderName" column="uploader_name" jdbcType="VARCHAR"/>
            <result property="handleTime" column="handle_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="typeDesc" column="type_desc" jdbcType="VARCHAR"/>
            <result property="hspOrgCode" column="hsp_org_code" jdbcType="VARCHAR"/>
            <result property="hspOrgName" column="hsp_org_name" jdbcType="VARCHAR"/>
            <result property="resource" column="resource" jdbcType="VARCHAR"/>
    </resultMap>
</mapper>
