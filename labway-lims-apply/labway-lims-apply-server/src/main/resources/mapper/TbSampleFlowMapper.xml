<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbSampleFlowMapper">
    <insert id="addBatch">
        INSERT INTO tb_sample_flow (
            sample_flow_id, apply_id, apply_sample_id, barcode, operate_code, operate_name,
            operator, operator_id, content, create_date, update_date, creator_id, creator_name,
            updater_id, updater_name, org_id, org_name, is_delete
        )
        values
        <foreach collection="flows" item="item" index="index" separator=",">
            (
            #{item.sampleFlowId},
            #{item.applyId},
            #{item.applySampleId},
            #{item.barcode},
            #{item.operateCode},
            #{item.operateName},
            #{item.operator},
            #{item.operatorId},
            #{item.content},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.orgId},
            #{item.orgName},
            #{item.isDelete}
            )
        </foreach>
    </insert>
</mapper>
