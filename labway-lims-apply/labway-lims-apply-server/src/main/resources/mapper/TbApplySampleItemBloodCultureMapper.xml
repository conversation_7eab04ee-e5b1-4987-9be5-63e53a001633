<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbApplySampleItemBloodCultureMapper">

    <insert id="insertBatch">
        INSERT INTO tb_apply_sample_item_blood_culture (apply_sample_item_blood_culture_id, apply_sample_id, apply_id,
                                                        apply_sample_item_id, test_item_id, test_item_code,
                                                        test_item_name, lul_anaerobic, lul_aerobic, lul_pediatric_bottle,
                                                        lll_anaerobic, lll_aerobic, lll_pediatric_bottle, rul_anaerobic,
                                                        rul_aerobic, rul_pediatric_bottle, rll_anaerobic, rll_aerobic,
                                                        rll_pediatric_bottle, anaerobic, aerobic, pediatric_bottle,
                                                        create_date, creator_id, creator_name, update_date, updater_id,
                                                        updater_name)
        VALUES
            <foreach collection="applySampleItemBloodCultures" item="item" separator=",">
                (
                #{item.applySampleItemBloodCultureId},
                #{item.applySampleId},
                #{item.applyId},
                #{item.applySampleItemId},
                #{item.testItemId},
                #{item.testItemCode},
                #{item.testItemName},
                #{item.lulAnaerobic},
                #{item.lulAerobic},
                #{item.lulPediatricBottle},
                #{item.lllAnaerobic},
                #{item.lllAerobic},
                #{item.lllPediatricBottle},
                #{item.rulAnaerobic},
                #{item.rulAerobic},
                #{item.rulPediatricBottle},
                #{item.rllAnaerobic},
                #{item.rllAerobic},
                #{item.rllPediatricBottle},
                #{item.anaerobic},
                #{item.aerobic},
                #{item.pediatricBottle},
                #{item.createDate},
                #{item.creatorId},
                #{item.creatorName},
                #{item.updateDate},
                #{item.updaterId},
                #{item.updaterName}
                )
            </foreach>

    </insert>
</mapper>
