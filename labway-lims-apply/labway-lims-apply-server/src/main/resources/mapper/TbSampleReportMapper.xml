<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbSampleReportMapper">
    <insert id="addBatch">
        INSERT INTO tb_sample_report (
        sample_report_id,
        apply_sample_id,
        apply_id,
        sample_id,
        barcode,
        file_type,
        url,
        group_name,
        group_id,
        org_id,
        org_name,
        update_date,
        create_date,
        creator_id,
        creator_name,
        updater_id,
        updater_name,
        hsp_org_id,
        hsp_org_name,
        is_delete
        )
        values
        <foreach collection="reports" item="item" index="index" separator=",">
            (
            #{item.sampleReportId},
            #{item.applySampleId},
            #{item.applyId},
            #{item.sampleId},
            #{item.barcode},
            #{item.fileType},
            #{item.url},
            #{item.groupName},
            #{item.groupId},
            #{item.orgId},
            #{item.orgName},
            #{item.updateDate},
            #{item.createDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.hspOrgId},
            #{item.hspOrgName},
            #{item.isDelete}
            )
        </foreach>
    </insert>
</mapper>
