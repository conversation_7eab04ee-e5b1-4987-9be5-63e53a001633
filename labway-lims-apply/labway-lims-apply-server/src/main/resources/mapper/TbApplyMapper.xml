<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbApplyMapper">
    <select id="selectUnreviewedApplySampleByQuery" parameterType="com.labway.lims.apply.api.dto.UnreviewedApplyQueryDto"
            resultType="com.labway.lims.apply.api.dto.SampleApplyDto">
        select ta.apply_id,
        ta.master_barcode,
        ta.patient_name,
        ta.patient_age,
        ta.patient_subage,
        ta.patient_subage_unit,
        ta.patient_birthday,
        ta.patient_card,
        ta.patient_card_type,
        ta.patient_bed,
        ta.patient_sex,
        ta.patient_visit_card,
        ta.patient_mobile,
        ta.patient_address,
        ta.apply_type_name,
        ta.apply_type_code,
        ta.remark,
        ta.sample_count,
        ta.sample_property,
        ta.dept,
        ta.diagnosis,
        ta.send_doctor_name,
        ta.send_doctor_code,
        ta.hsp_org_id,
        ta.hsp_org_code,
        ta.hsp_org_name,
        ta.org_name,
        ta.urgent,
        ta.apply_date,
        ta.sampling_date,
        ta.status,
        ta.create_date,
        ta.creator_name,
        ta.checker_name,
        ta.check_date,
        ta.sample_property_code,
        tas.barcode,
        tas.apply_sample_id,
        ta.source,
        tas.sample_type_name,
        tas.sample_type_code,
        tas.status sampleStatusCode,
        tas.out_barcode,
        tas.patient_part
        from tb_apply ta
        inner join tb_apply_sample tas
        on ta.apply_id = tas.apply_id
        where ta.status in
        <foreach collection="query.statusList" item="status" separator="," open="(" close=")">
            #{status}
        </foreach>

        <if test="query.startDate != null and query.endDate != null">
            and ta.create_date between #{query.startDate} and #{query.endDate}
        </if>

        <if test="query.hspOrgId != null">
            and ta.hsp_org_id = #{query.hspOrgId}
        </if>

        <if test="query.source != null and query.source != ''">
            and ta.source = #{query.source}
        </if>
        and ta.is_delete = 0
        and tas.is_delete = 0
        and ta.org_id = #{query.orgId}
        <!-- 如果查询已复核列表，需要查询全部 -->
        <if test="!query.queryChecked" >
            and tas.is_one_pick = 0
            and tas.status != 99
        </if>
        ORDER BY ta.apply_id
    </select>
    <select id="selectAllByApplyIds" resultType="com.labway.lims.apply.model.TbApply">
        select * from tb_apply where apply_id in
       <foreach collection="applyIds" separator="," item="item" open="(" close=")">
           #{item}
       </foreach>
    </select>
</mapper>
