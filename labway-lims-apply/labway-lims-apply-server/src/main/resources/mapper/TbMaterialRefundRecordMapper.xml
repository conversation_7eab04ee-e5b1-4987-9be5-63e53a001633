<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbMaterialRefundRecordMapper">
    <resultMap type="com.labway.lims.apply.model.entity.TbMaterialRefundRecord" id="TbMaterialRefundRecordMap">
        <result property="refundId" column="refund_id" jdbcType="INTEGER"/>
        <result property="refundNo" column="refund_no" jdbcType="VARCHAR"/>
        <result property="incomeNo" column="income_no" jdbcType="VARCHAR"/>
        <result property="deliveryNo" column="delivery_no" jdbcType="VARCHAR"/>
        <result property="deliveryDetailId" column="delivery_detail_id" jdbcType="INTEGER"/>
        <result property="materialId" column="material_id" jdbcType="INTEGER"/>
        <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
        <result property="materialName" column="material_name" jdbcType="VARCHAR"/>
        <result property="specification" column="specification" jdbcType="VARCHAR"/>
        <result property="batchNo" column="batch_no" jdbcType="VARCHAR"/>
        <result property="manufacturers" column="manufacturers" jdbcType="VARCHAR"/>
        <result property="mainUnit" column="main_unit" jdbcType="VARCHAR"/>
        <result property="deliveryMainNumber" column="delivery_main_number" jdbcType="INTEGER"/>
        <result property="incomeMainNumber" column="income_main_number" jdbcType="INTEGER"/>
        <result property="assistUnit" column="assist_unit" jdbcType="VARCHAR"/>
        <result property="deliveryAssistNumber" column="delivery_assist_number" jdbcType="VARCHAR"/>
        <result property="incomeAssistNumber" column="income_assist_number" jdbcType="VARCHAR"/>
        <result property="unitConversionRate" column="unit_conversion_rate" jdbcType="VARCHAR"/>
        <result property="validDate" column="valid_date" jdbcType="TIMESTAMP"/>
        <result property="groupId" column="group_id" jdbcType="INTEGER"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="INTEGER"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="INTEGER"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updaterName" column="updater_name" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="refundType" column="refund_type" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="refundId" useGeneratedKeys="true">
        insert into public.tb_material_refund_record(refund_id,refund_no, income_no, delivery_no, delivery_detail_id, material_id,
        material_code, material_name, specification, batch_no, manufacturers, main_unit, delivery_main_number,
        income_main_number, assist_unit, delivery_assist_number, income_assist_number, unit_conversion_rate, valid_date,
        group_id, group_name, org_id, org_name, create_date, creator_id, creator_name, update_date, updater_id,
        updater_name, is_delete, refund_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.refundId}, #{entity.refundNo}, #{entity.incomeNo}, #{entity.deliveryNo}, #{entity.deliveryDetailId},
            #{entity.materialId}, #{entity.materialCode}, #{entity.materialName}, #{entity.specification},
            #{entity.batchNo}, #{entity.manufacturers}, #{entity.mainUnit}, #{entity.deliveryMainNumber},
            #{entity.incomeMainNumber}, #{entity.assistUnit}, #{entity.deliveryAssistNumber},
            #{entity.incomeAssistNumber}, #{entity.unitConversionRate}, #{entity.validDate}, #{entity.groupId},
            #{entity.groupName}, #{entity.orgId}, #{entity.orgName}, #{entity.createDate}, #{entity.creatorId},
            #{entity.creatorName}, #{entity.updateDate}, #{entity.updaterId}, #{entity.updaterName}, #{entity.isDelete},
            #{entity.refundType})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="refundId" useGeneratedKeys="true">
        insert into public.tb_material_refund_record(refund_no, income_no, delivery_no, delivery_detail_id, material_id,
        material_code, material_name, specification, batch_no, manufacturers, main_unit, delivery_main_number,
        income_main_number, assist_unit, delivery_assist_number, income_assist_number, unit_conversion_rate, valid_date,
        group_id, group_name, org_id, org_name, create_date, creator_id, creator_name, update_date, updater_id,
        updater_name, is_delete, refund_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.refundNo}, #{entity.incomeNo}, #{entity.deliveryNo}, #{entity.deliveryDetailId},
            #{entity.materialId}, #{entity.materialCode}, #{entity.materialName}, #{entity.specification},
            #{entity.batchNo}, #{entity.manufacturers}, #{entity.mainUnit}, #{entity.deliveryMainNumber},
            #{entity.incomeMainNumber}, #{entity.assistUnit}, #{entity.deliveryAssistNumber},
            #{entity.incomeAssistNumber}, #{entity.unitConversionRate}, #{entity.validDate}, #{entity.groupId},
            #{entity.groupName}, #{entity.orgId}, #{entity.orgName}, #{entity.createDate}, #{entity.creatorId},
            #{entity.creatorName}, #{entity.updateDate}, #{entity.updaterId}, #{entity.updaterName}, #{entity.isDelete},
            #{entity.refundType})
        </foreach>
        on duplicate key update
        refund_no = values(refund_no) , income_no = values(income_no) , delivery_no = values(delivery_no) ,
        delivery_detail_id = values(delivery_detail_id) , material_id = values(material_id) , material_code =
        values(material_code) , material_name = values(material_name) , specification = values(specification) , batch_no
        = values(batch_no) , manufacturers = values(manufacturers) , main_unit = values(main_unit) ,
        delivery_main_number = values(delivery_main_number) , income_main_number = values(income_main_number) ,
        assist_unit = values(assist_unit) , delivery_assist_number = values(delivery_assist_number) ,
        income_assist_number = values(income_assist_number) , unit_conversion_rate = values(unit_conversion_rate) ,
        valid_date = values(valid_date) , group_id = values(group_id) , group_name = values(group_name) , org_id =
        values(org_id) , org_name = values(org_name) , create_date = values(create_date) , creator_id =
        values(creator_id) , creator_name = values(creator_name) , update_date = values(update_date) , updater_id =
        values(updater_id) , updater_name = values(updater_name) , is_delete = values(is_delete) , refund_type =
        values(refund_type)
    </insert>
</mapper>

