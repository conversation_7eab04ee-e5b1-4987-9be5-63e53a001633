<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbApplyLogisticsSampleMapper">
    <select id="selectApplyLogisticsDetail" resultType="com.labway.lims.apply.api.dto.SimpleLogisticsSampleDto"
            parameterType="com.labway.lims.apply.api.dto.HspOrgDateQueryDto">
        select *
        from tb_apply_logistics_sample tals
        inner join tb_apply_logistics tal
        on tals.apply_logistics_id = tal.apply_logistics_id
        left join tb_apply_sample tas on tals.barcode = tas.barcode
        where tals.is_delete = 0
        and tal.is_delete = 0
        and tal.hsp_org_id = #{dto.hspOrgId}
        and tal.receive_date >= #{dto.startDate}
            and tal.receive_date &lt;= #{dto.endDate}
        and tals.status in
        <foreach collection="dto.statusList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectSupplementList" resultType="com.labway.lims.apply.api.dto.LogisticsApplyDto"
            parameterType="com.labway.lims.apply.api.dto.HspOrgDateQueryDto">
        select tals.barcode,
        tals.apply_logistics_sample_id,
        tal.master_barcode logisticsApplyNo,
        tal.logistics_user_name,
        tal.receive_date,
        tal.apply_image,
        tas.apply_sample_id,
        tas.sample_type_name,
        ta.*
        from tb_apply_logistics_sample tals
        inner join tb_apply_logistics tal
        on tals.apply_logistics_id = tal.apply_logistics_id
        left join tb_apply ta on tals.apply_id = ta.apply_id
        left join tb_apply_sample tas on  tas.barcode = tals.barcode
        where tals.is_delete = 0
        and tal.is_delete = 0
        and tal.hsp_org_id = #{dto.hspOrgId}
        and tal.receive_date >= #{dto.startDate}
        and tal.receive_date &lt;= #{dto.endDate}
        and tals.status in
        <foreach collection="dto.statusList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
