<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.lims.apply.mapper.TbReportDelayMapper">


    <resultMap id="BaseResultMap" type="com.labway.lims.apply.model.TbReportDelay" >
        <id column="delay_id" property="delayId" jdbcType="BIGINT" />
        <result column="apply_sample_id" property="applySampleId" jdbcType="BIGINT" />
        <result column="apply_id" property="applyId" jdbcType="BIGINT" />
        <result column="barcode" property="barcode" jdbcType="VARCHAR" />
        <result column="test_item_codes" property="testItemCodes" jdbcType="VARCHAR" />
        <result column="reason" property="reason" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="send_report_date" property="sendReportDate" jdbcType="TIMESTAMP" />
        <result column="cancel_user_id" property="cancelUserId" jdbcType="BIGINT" />
        <result column="cancel_user_name" property="cancelUserName" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="group_id" property="groupId" jdbcType="BIGINT" />
        <result column="group_name" property="groupName" jdbcType="VARCHAR" />
        <result column="org_id" property="orgId" jdbcType="BIGINT" />
        <result column="org_name" property="orgName" jdbcType="VARCHAR" />
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
        <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
        <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR" />
        <result column="updater_id" property="updaterId" jdbcType="BIGINT" />
        <result column="updater_name" property="updaterName" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        delay_id, apply_sample_id, apply_id, barcode, test_item_codes, reason, remark, send_report_date, 
        cancel_user_id, cancel_user_name, `status`, group_id, group_name, org_id, org_name, 
        create_date, update_date, creator_id, creator_name, updater_id, updater_name
    </sql>


    <update id="updateByIds">
        update tb_report_delay
        <set>
            <if test="status != null">
                status = #{status}
            </if>
            <if test="cancelUserId != null ">
                ,cancel_user_id = #{cancelUserId }
            </if>
            <if test="cancelUserName != null &amp;&amp; cancelUserName != ''">
                ,cancel_user_name = #{cancelUserName }
            </if>
        </set>
        where delay_id in
        <foreach collection="delayIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>


    </update>


</mapper>