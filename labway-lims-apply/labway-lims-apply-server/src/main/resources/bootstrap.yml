spring:
  application:
    name: labway-lims-apply
  cloud:
    nacos:
      server-addr: ${NACOS_CONFIG_SERVERADDR:10.136.0.33:8848}
      discovery:
        group: ${NACOS_CONFIG_GROUP:labway-lims-yz}
        namespace: ${NACOS_CONFIG_NAMEPSACE:dev}
      config:
        group: ${spring.cloud.nacos.discovery.group}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        file-extension: yaml
        shared-configs:
          - data-id: redis.yaml
            group: public
            refresh: true

          - data-id: dubbo.yaml
            group: public
            refresh: true

          - data-id: actuator.yaml
            group: public
            refresh: true

          - data-id: mybatis-plus.yaml
            group: public
            refresh: true

          - data-id: es.yaml
            group: public
            refresh: true

          - data-id: obs.yaml
            group: public
            refresh: true

          - data-id: rabbitmq.yaml
            group: public
            refresh: true

          - data-id: labway-lims-common.yaml
            group: public
            refresh: true