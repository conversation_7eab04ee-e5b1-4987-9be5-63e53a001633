server:
  port: 12318
  tomcat:
    threads:
      max: 500

dubbo:
  provider:
    filter: loginUserProvider
  consumer:
    filter: loginUserConsumer

spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  profiles:
    active: dev,sharding
  servlet:
    multipart:
      max-file-size: 128MB
      max-request-size: 128MB
  shardingsphere:
    datasource:
      names: master
      master:
        driver-class-name: ${mysql.driver}
        jdbc-url: jdbc:postgresql://${mysql.master.host}:${mysql.master.port}/${mysql.db}
        password: ${mysql.password}
        type: ${mysql.datasource}
        username: ${mysql.username}
        minimum-idle: 5
    props:
      sql:
        show: false
    sharding:
      default-data-source-name: master
      master-slave-rules:
        ms:
          master-data-source-name: master
          slave-data-source-names:
            - master
          load-balance-algorithm-type: round_robin

mysql:
  datasource: com.zaxxer.hikari.HikariDataSource
  db: labway-lims
  driver: org.postgresql.Driver

logging:
  level:
    "com.zaxxer.hikari.pool.ProxyConnection": error
    com.alibaba.nacos.client.config.impl: warn

business:
  # 配置机构编码对应业务中台机构编码
  org-code-map:
    "19": "00010110000000001WR5"
