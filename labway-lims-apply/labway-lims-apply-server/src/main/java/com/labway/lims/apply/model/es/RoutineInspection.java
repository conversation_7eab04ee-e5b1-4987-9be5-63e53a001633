package com.labway.lims.apply.model.es;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;
import java.util.List;

/**
 * 常规检验
 */
@Getter
@Setter
public final class RoutineInspection extends BaseSampleEsModel {
    /**
     * 一审人id
     */
    @Field(type = FieldType.Long)
    private Long oneCheckerId;
    /**
     * 一审人
     */
    @Field(type = FieldType.Keyword)
    private String oneCheckerName;
    /**
     * 一审时间
     */
    @Field(type = FieldType.Date)
    private Date oneCheckDate;
    /**
     * 报告项目
     */
    @Field(type = FieldType.Nested)
    private List<ReportItem> reportItems;


    /**
     * 报告项目
     */
    @Setter
    @Getter
    private static final class ReportItem {
        /**
         * 报告单项目编码
         */
        @Field(type = FieldType.Keyword)
        private String reportItemCode;
        /**
         * 报告项目名称
         */
        @Field(type = FieldType.Keyword)
        private String reportItemName;
        /**
         * 检验项目ID
         */
        @Field(type = FieldType.Long)
        private Long testItemId;
        /**
         * 检验项目编码
         */
        @Field(type = FieldType.Keyword)
        private String testItemCode;
        /**
         * 检验项目名称
         */
        @Field(type = FieldType.Keyword)
        private String testItemName;
        /**
         * 打印顺序
         */
        @Field(type = FieldType.Integer)
        private Integer printSort;
        /**
         * 结果类型
         */
        @Field(type = FieldType.Keyword)
        private String resultType;
        /**
         * 单位
         */
        @Field(type = FieldType.Keyword)
        private String unit;
        /**
         * 结果范围
         */
        @Field(type = FieldType.Keyword)
        private String range;
        /**
         * 状态 1:危机 2:异常 0:正常
         *
         * @see ResultStatusEnum
         */
        @Field(type = FieldType.Integer)
        private Integer status;
        /**
         * 来源仪器
         */
        @Field(type = FieldType.Long)
        private Long instrumentId;
        /**
         * 仪器结果
         */
        @Field(type = FieldType.Keyword)
        private String instrumentResult;
        /**
         * 结果 （经过一系列的计算 转换最终得到的结果值）
         */
        @Field(type = FieldType.Keyword)
        private String result;
        /**
         * 检验判定 UP  DOWN  NORMAL
         *
         * @see TestJudgeEnum
         */
        @Field(type = FieldType.Keyword)
        private String judge;
    }

}
