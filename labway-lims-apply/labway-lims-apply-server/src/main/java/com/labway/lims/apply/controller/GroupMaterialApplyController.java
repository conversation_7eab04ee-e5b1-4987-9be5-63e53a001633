package com.labway.lims.apply.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.apply.MaterialApplyStatusEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.BusinessCenterRejectApplyDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDetailAddDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDetailDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDto;
import com.labway.lims.apply.api.dto.MaterialApplyExportDto;
import com.labway.lims.apply.api.dto.MaterialApplyNoDto;
import com.labway.lims.apply.api.dto.MaterialRejectDto;
import com.labway.lims.apply.api.service.GroupMaterialApplyDetailService;
import com.labway.lims.apply.api.service.GroupMaterialApplyService;
import com.labway.lims.apply.api.vo.GroupMaterialApplyAuditVo;
import com.labway.lims.apply.vo.*;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import com.labway.lims.base.api.dto.MaterialDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import com.labway.lims.base.api.service.MaterialService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 物料申领
 */
@Getter
@Setter
@RequestMapping("/group-material-apply")
@RestController
public class GroupMaterialApplyController extends BaseController {

    @Resource
    private GroupMaterialApplyService groupMaterialApplyService;

    @Resource
    private GroupMaterialApplyDetailService groupMaterialApplyDetailService;

    @DubboReference
    private PdfReportService pdfReportService;

    @DubboReference
    private MaterialService materialService;

    @DubboReference
    private GroupMaterialService groupMaterialService;

    /**
     * 打印申请单
     */
    @GetMapping("/print")
    public Object print(@RequestParam(required = false) Long applyId) {
        if (Objects.isNull(applyId)) {
            throw new IllegalArgumentException("请选择申领单");
        }
        final GroupMaterialApplyDto groupMaterialApply = groupMaterialApplyService.selectById(applyId);
        if (Objects.isNull(groupMaterialApply)) {
            throw new IllegalStateException("申领单不存在");
        }

        final List<GroupMaterialApplyDetailDto> groupMaterialApplyDetails = groupMaterialApplyDetailService
                .selectByApplyNo(groupMaterialApply.getApplyNo(), groupMaterialApply.getOrgId());
        if (CollectionUtils.isEmpty(groupMaterialApplyDetails)) {
            throw new IllegalStateException("申领单明细不存在");
        }

        final PdfReportParamDto param = new PdfReportParamDto();

        param.put("groupName", LoginUserHandler.get().getGroupName());
        param.put("apply", groupMaterialApply);
        param.put("details", groupMaterialApplyDetails);

        final String s = pdfReportService.build2Url(PdfTemplateTypeEnum.WL_SL.getCode(), param);

        return Map.of("url", s);
    }

    /**
     * 申领单状态
     */
    @GetMapping("/apply-status")
    public Object applyStatus() {
        return Arrays.stream(MaterialApplyStatusEnum.values())
                .map(m -> Map.of("code", m.getCode(), "desc", m.getDesc())).collect(Collectors.toList());
    }

    /**
     * 回退
     */
    @PostMapping("/rollbackMaterialApply")
    public Object rollbackMaterialApply(@RequestParam String applyNo, @RequestParam(required = false) String returnReason ) {
        if (StringUtils.isBlank(applyNo)) {
            throw new IllegalArgumentException("请选择申领单");
        }

        final GroupMaterialApplyDto groupMaterialApply = groupMaterialApplyService.selectByGroupIdAndApplyNo(NumberUtils.LONG_ZERO, applyNo);
        if (Objects.isNull(groupMaterialApply)) {
            throw new IllegalArgumentException("当前专业组不存在该申领单");
        }

        final Integer status = groupMaterialApply.getStatus();
        if (!Objects.equals(status, MaterialApplyStatusEnum.SUBMIT.getCode())) {
            throw new IllegalArgumentException("该申领单不是已提交状态，不能回退");
        }

        final BusinessCenterRejectApplyDto rollback = new BusinessCenterRejectApplyDto();
        rollback.setGroupId(groupMaterialApply.getGroupId());
        rollback.setApplyNo(applyNo);
        rollback.setReturnReason(returnReason);

        groupMaterialApplyService.businessCenterRejectApply(rollback);

        return Map.of();
    }

    /**
     * 审核申领单
     */
    @PostMapping("/audit")
    public Object audit(@RequestBody GroupMaterialApplyAuditVo auditVo) {
		if (Objects.isNull(auditVo)) {
			throw new IllegalArgumentException("参数异常");
		}

	    Validate.notBlank(auditVo.getApplyNo(), "请选择申请单");
		Validate.notNull(auditVo.getDetailVos(), "参数异常");

        groupMaterialApplyService.audit(auditVo);

        return Map.of();
    }

    /**
     * 创建申请单
     */
    @PostMapping("/add")
    public Object add(@RequestBody LinkedHashSet<GroupMaterialApplyDetailAddVo> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            throw new IllegalArgumentException("请选择物料信息");
        }

        // 检查参数
        vos.forEach(this::checkParam);

        return groupMaterialApplyService.add(JSON.parseArray(JSON.toJSONString(vos), GroupMaterialApplyDetailAddDto.class));
    }

    private void checkParam(GroupMaterialApplyDetailAddVo vo) {
        final Long materialId = vo.getMaterialId();
        if (Objects.isNull(materialId)) {
            throw new IllegalArgumentException("请选择物料信息");
        }

        final String materialName = vo.getMaterialName();
        if (StringUtils.isBlank(materialName)) {
            throw new IllegalArgumentException("请选择物料名称不能为空");
        }

        final BigDecimal applyMainNumber = vo.getApplyMainNumber();
        if (Objects.isNull(applyMainNumber)) {
            throw new IllegalArgumentException("申领主单位数量不能为空");
        }

        final BigDecimal applyAssistNumber = vo.getApplyAssistNumber();
        if (Objects.isNull(applyAssistNumber)) {
            throw new IllegalArgumentException("申领辅单位数量不能为空");
        }

        if (applyAssistNumber.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("申领辅单位数量不能小于等于0");
        }

        if (applyMainNumber.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("申领辅单位数量不能小于等于0");
        }
    }

    /**
     * 查询物料申领单明细
     */
    @GetMapping("/detail")
    public Object detail(@RequestParam(required = false) String applyNo) {
        if (StringUtils.isBlank(applyNo)) {
            return Collections.emptyList();
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        GroupMaterialApplyDto groupMaterialApplyDto = groupMaterialApplyService.selectByOrgIdAndApplyNo(user.getOrgId(), applyNo);

        final List<GroupMaterialApplyDetailDto> groupMaterialApplyDetails = groupMaterialApplyDetailService.selectByApplyNo(LoginUserHandler.get().getOrgId(), applyNo);

        for (GroupMaterialApplyDetailDto groupMaterialApplyDetail : groupMaterialApplyDetails) {
            groupMaterialApplyDetail.setStatus(ObjectUtils.defaultIfNull(groupMaterialApplyDetail.getStatus(), groupMaterialApplyDto.getStatus()));
        }
        return JSON.parseArray(JSON.toJSONString(groupMaterialApplyDetails), GroupMaterialApplyDetailVo.class);
    }

    @PostMapping("/reject")
    public Object reject(@RequestBody MaterialRejectDto rejectDto){
        rejectDto.verifyParams();
        return groupMaterialApplyDetailService.reject(rejectDto);
    }


    /**
     * 查询当前专业组的物料申领单
     */
    @PostMapping("/select-current-group-material-apply")
    public Object selectCurrentGroupMaterialApply(@RequestBody DateQueryVo vo,
                                                  @RequestParam(required = false) String applyNo) {
        Date startDate = vo.getStartDate();
        Date endDate = vo.getEndDate();
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            final Date now = new Date();
            startDate = DateUtil.beginOfDay(ObjectUtils.defaultIfNull(startDate, now));
            endDate = DateUtil.endOfDay(ObjectUtils.defaultIfNull(endDate, now));
        }

        List<GroupMaterialApplyDto> groupMaterialApplys = groupMaterialApplyService.selectCurrentGroupMaterialApply(applyNo, startDate, endDate, LoginUserHandler.get().getGroupId());

        return JSON.parseArray(JSON.toJSONString(groupMaterialApplys), GroupMaterialApplyVo.class)
                .stream().sorted(Comparator.comparing(GroupMaterialApplyVo::getApplyTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
    }

    /**
     *  导入物料申请
     */
    @PostMapping("/import-material-apply")
    public Object importMaterialApply(@RequestParam("file")MultipartFile file) throws IOException {
        LoginUserHandler.User user = LoginUserHandler.get();
        if (Objects.isNull(file) || file.isEmpty()){
            throw new IllegalArgumentException("文件不能为空");
        }
        String fileName = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(fileName);
        if (!"xls".equalsIgnoreCase(extension) && !"xlsx".equalsIgnoreCase(extension)) {
            throw new IllegalStateException("只允许上传 xls 和 xlsx 格式的文件");
        }
        List<MaterialApplyVo> materialApplyVos = EasyExcel.read(file.getInputStream())
                .head(MaterialApplyVo.class)
                .sheet(0).doReadSync();

        List<String> materialCodes = new ArrayList<>();
        for (int i = 0; i < materialApplyVos.size(); i++) {
            MaterialApplyVo materialApplyVo = materialApplyVos.get(i);
            if (Objects.isNull(materialApplyVo.getMaterialCode())){
                throw new IllegalStateException(String.format("第[%s]行,物料编码不能为空",(i+2)));
            }

            if (Objects.isNull(materialApplyVo.getApplyAssistNumberStr()) ){
                throw new IllegalStateException(String.format("第[%s]行,申领辅数量不能为空",(i+2)));
            }

            try {
                BigDecimal bigDecimal = new BigDecimal(materialApplyVo.getApplyAssistNumberStr());
                materialApplyVo.setApplyAssistNumber(bigDecimal);
            }catch (Exception e){
                throw new IllegalStateException(String.format("第[%s]行,申领辅数量格式错误",(i+2)));
            }

            if(new BigDecimal(materialApplyVo.getApplyAssistNumberStr()).compareTo(BigDecimal.ZERO) < 0 ){
                throw new IllegalArgumentException(String.format("第[%s]行,申领辅数量不能为负数",(i+2)));
            }

/*
            if (materialApplyVo.getApplyAssistNumberStr().length() > 6){
                throw new IllegalArgumentException("第[%s]行,申领辅数量超出最大范围");
            }*/
            materialCodes.add(materialApplyVo.getMaterialCode());
        }

        List<MaterialDto> materialDtos = materialService.selectByGroupIdAndMaterialCodes(materialCodes);
//        Map<String, Long> materialMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialCode, MaterialDto::getMaterialId, (a, b) -> b));
        Map<String, MaterialDto> materialDtoMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialCode, Function.identity(),(a,b) ->b));
        Set<Long> materialIds = materialDtos.stream().map(MaterialDto::getMaterialId).collect(Collectors.toSet());

        List<GroupMaterialDto> groupMaterialDtos = groupMaterialService.selectByGroupIdAndMaterialIds(user.getGroupId(), materialIds);
        Map<Long, GroupMaterialDto> gruopMaterialMap = groupMaterialDtos.stream().collect(Collectors.toMap(GroupMaterialDto::getMaterialId, Function.identity()));

        for (int i = 0; i < materialApplyVos.size(); i++) {
            MaterialDto materialDto = materialDtoMap.get(materialApplyVos.get(i).getMaterialCode());
            if (Objects.isNull(materialDto)){
                throw new IllegalStateException(String.format("第[%s]行,物料编码不存在",(i+2)));
            }
            GroupMaterialDto groupMaterialDto = gruopMaterialMap.get(materialDto.getMaterialId());
            if (Objects.isNull(groupMaterialDto)){
                throw new IllegalStateException(String.format("[%s]行物料,该专业无申领权限",(i+2)));
            }
        }

        materialApplyVos = new ArrayList<>(materialApplyVos.stream().collect(Collectors.toMap(MaterialApplyVo::getMaterialCode, Function.identity(), (a, b) -> {
            BigDecimal add = a.getApplyAssistNumber().add(b.getApplyAssistNumber());
            b.setApplyAssistNumber(add);
            return b;
        })).values());

        for (int i = 0; i < materialApplyVos.size(); i++) {
            MaterialDto materialDto = materialDtoMap.get(materialApplyVos.get(i).getMaterialCode());
            MaterialApplyVo materialApplyVo = materialApplyVos.get(i);
            materialDto.setApplyAssistNumber(materialApplyVo.getApplyAssistNumber());

            String[] numbers = materialDto.getUnitConversionRate().split("/");
            BigDecimal mainNumber = materialApplyVo.getApplyAssistNumber().multiply(new BigDecimal(numbers[0]));
            materialDto.setApplyMainNumber(mainNumber);
        }
        return materialDtos;
    }

    /**
     * 导出物料申领
     */
    @PostMapping("/export-apply")
    public void exportApply(@RequestBody MaterialApplyNoDto exportDto, HttpServletResponse response) throws Exception{
        exportDto.verifyParams();

        LoginUserHandler.User user = LoginUserHandler.get();

        //获取申领单信息
        final List<GroupMaterialApplyDto> groupMaterialApplyDto = groupMaterialApplyService.selectByOrgIdAndApplyNos(user.getOrgId(), exportDto.getApplyNos());

        //获取申领物料数据
        final List<GroupMaterialApplyDetailDto> groupMaterialApplyDetails = groupMaterialApplyDetailService.selectByApplyNos(LoginUserHandler.get().getOrgId(), exportDto.getApplyNos());

        //组装导出数据
        List<MaterialApplyExportDto> exportDatas = buildExportData(groupMaterialApplyDto, groupMaterialApplyDetails);

        //导出
        try (InputStream template = ResourceUtil.getStream("classpath:template/物料申领.xlsx")) {
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(template)
                    .excelType(ExcelTypeEnum.XLSX)
                    .build();

            WriteSheet sheet = EasyExcel.writerSheet(0).build();
            excelWriter.fill(new FillWrapper("row", exportDatas), sheet);
            excelWriter.finish();
        }
    }

    /**
     * 组装导出数据
     * @param groupMaterialApplyDtos
     * @param groupMaterialApplyDetails
     * @return
     */
    private List<MaterialApplyExportDto> buildExportData(List<GroupMaterialApplyDto> groupMaterialApplyDtos, List<GroupMaterialApplyDetailDto> groupMaterialApplyDetails) {
        List<MaterialApplyExportDto> result = new ArrayList<>();
        int index = 0;
        if(CollectionUtils.isNotEmpty(groupMaterialApplyDtos)){
            //物料转map
            Map<String, List<GroupMaterialApplyDetailDto>> collectMap = groupMaterialApplyDetails.stream()
                    .collect(Collectors.groupingBy(GroupMaterialApplyDetailDto::getApplyNo));
            for (GroupMaterialApplyDto groupMaterialApplyDto : groupMaterialApplyDtos) {
                List<GroupMaterialApplyDetailDto> detailDtos = collectMap.getOrDefault(groupMaterialApplyDto.getApplyNo(),Collections.emptyList());

                if(CollectionUtils.isNotEmpty(detailDtos)){
                    for (GroupMaterialApplyDetailDto detailDto : detailDtos) {
                        index++;

                        MaterialApplyExportDto materialApplyExportDto = BeanUtil.copyProperties(detailDto, MaterialApplyExportDto.class);

                        materialApplyExportDto.setOrderNo(index);

                        //补充申领单字段
                        materialApplyExportDto.setApplyNo(groupMaterialApplyDto.getApplyNo());
                        materialApplyExportDto.setStatusValue(MaterialApplyStatusEnum.getByCode(groupMaterialApplyDto.getStatus()).getDesc());
                        materialApplyExportDto.setApplyTime(groupMaterialApplyDto.getApplyTime());
                        materialApplyExportDto.setApplyUserName(groupMaterialApplyDto.getApplyUserName());
                        materialApplyExportDto.setChecker(groupMaterialApplyDto.getChecker());
                        materialApplyExportDto.setCheckDate(groupMaterialApplyDto.getCheckDate());

                        //处理申领物料字段
                        materialApplyExportDto.setMaterialStatusValue(MaterialApplyStatusEnum.getByCode(detailDto.getStatus()==null?groupMaterialApplyDto.getStatus():detailDto.getStatus()).getDesc());
                        //日期毫秒向下取整 防止excel四舍五入
                        if(Objects.equals(materialApplyExportDto.getCheckDate(), DefaultDateEnum.DEFAULT_DATE.getDate())){
                            materialApplyExportDto.setCheckDate(null);
                        }
                        materialApplyExportDto.setApplyTime(detailDate(materialApplyExportDto.getApplyTime()));
                        materialApplyExportDto.setCheckDate(detailDate(materialApplyExportDto.getCheckDate()));
                        result.add(materialApplyExportDto);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 处理日期
     * @param date
     * @return
     */
    private Date detailDate(Date date) {
        if (!Objects.nonNull(date)) {
            return null;
        }
        return new Date((date.getTime() / 1000) * 1000);
    }

}
