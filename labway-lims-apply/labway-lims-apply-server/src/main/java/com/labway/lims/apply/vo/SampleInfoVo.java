package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <pre>
 * SampleInfoVo
 * 仪器根据条码获取样本信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/6/25 10:10
 */
@Getter
@Setter
public class SampleInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 条码号
     */
    private List<String> barcodes;

    /**
     * 仪器编号
     */
    private String machineCode;

    /**
     * 元数据
     */
    private String metadata;

}
