package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.PhysicalSampleItemDto;
import com.labway.lims.apply.model.TbPhysicalSampleItem;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 体检样本项目 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 15:15
 */
@Mapper(componentModel = "spring")
public interface PhysicalSampleItemConverter {

    PhysicalSampleItemDto fromTbPhysicalSampleItem(TbPhysicalSampleItem obj);

    List<PhysicalSampleItemDto> fromTbPhysicalSampleItemList(List<TbPhysicalSampleItem> list);

}
