package com.labway.lims.apply.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.util.ListUtils;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.vo.ImportSample;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

@Getter
@Setter
@Slf4j
public class ImportSampleListener extends AnalysisEventListener<ImportSample> {
    /**
     * 缓存的数据
     */
    private List<ImportSample> cachedDataList = ListUtils.newArrayListWithExpectedSize(1000);

    List<String> errorList = ListUtils.newArrayListWithExpectedSize(cachedDataList.size());

    @Override
    public void invoke(ImportSample importSample, AnalysisContext context) {
        final Integer index = context.readRowHolder().getRowIndex();
        if (Objects.isNull(importSample)) {
            return;
        }

        // 条码号
        if (StringUtils.isBlank(importSample.getOutBarcode())) {
            errorList.add(String.format("第 %s 行条码号为空", index));
            return;
        }
        // 最大不能超过50个字符
        if (StringUtils.length(importSample.getOutBarcode()) > BaseController.INPUT_MAX_LENGTH) {
            errorList.add(String.format("第 %s 行条码号长度不能超过 50 个字符", index));
            return;
        }

        // 患者名称
        if (StringUtils.isBlank(importSample.getPatientName())) {
            errorList.add(String.format("第 %s 行姓名为空", index));
        }
        if (StringUtils.length(importSample.getPatientName()) > BaseController.INPUT_MAX_LENGTH) {
            errorList.add(String.format("第 %s 行姓名长度不能超过 50 个字符", index));
            return;
        }

        // 性别
        if (StringUtils.isBlank(importSample.getPatientSex())) {
            errorList.add(String.format("第 %s 行性别为空", index));
        }

        if (Objects.isNull(importSample.getPatientAge())) {
            errorList.add(String.format("第 %s 行年龄为空", index));
        }

//        importSample.setIndex(index);

        cachedDataList.add(importSample);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("doAfterAllAnalysed........");
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        log.error("解析失败，但是继续解析下一行:{}", exception.getMessage());
        // 如果是某一个单元格的转换异常 能获取到具体行号
        // 如果要获取头的信息 配合invokeHeadMap使用
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;

            log.error("第{}行，第{}列解析异常，数据为:{}", excelDataConvertException.getRowIndex(),
                    excelDataConvertException.getColumnIndex(), excelDataConvertException.getCellData());

            errorList.add(String.format("在 %s 行 %s 列数据解析异常", excelDataConvertException.getRowIndex(), excelDataConvertException.getColumnIndex()));
        }
    }
}
