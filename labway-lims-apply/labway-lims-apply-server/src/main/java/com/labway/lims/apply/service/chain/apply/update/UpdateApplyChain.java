package com.labway.lims.apply.service.chain.apply.update;

import com.labway.lims.apply.service.chain.apply.add.AddDiagnosisCommand;
import com.labway.lims.apply.service.chain.apply.add.AddHspOrgDeptOrDoctorCommand;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


@Component
public class UpdateApplyChain extends ChainBase implements InitializingBean {

    @Resource
    private UpdateCheckParamCommand updateCheckParamCommand;
    @Resource
    private UpdateFlowCommand updateFlowCommand;
    @Resource
    private UpdateGroupSampleCommand updateGroupSampleCommand;
    @Resource
    private UpdateSaveApplyCommand updateSaveApplyCommand;
    @Resource
    private UpdateFillApplyAndDiffInfoCommand updateFillApplyAndDiffInfoCommand;
    @Resource
    private UpdatePostCommand updatePostCommand;

    @Resource
    private CheckSamePersonDayItemCommand checkSamePersonDayItemCommand;
    @Resource
    private CheckTestItemLimitSexCommand checkTestItemLimitSexCommand;
    @Resource
    private AddHspOrgDeptOrDoctorCommand addHspOrgDeptOrDoctorCommand;
    @Resource
    private AddDiagnosisCommand addDiagnosisCommand;

    @Transactional(rollbackFor = Exception.class)
    public boolean execute(Context context) throws Exception {
        return super.execute(context);
    }

    @Override
    public void afterPropertiesSet() throws Exception {

        // 检查参数
        addCommand(updateCheckParamCommand);

        // 分组样本
        addCommand(updateGroupSampleCommand);

        // 校验是否同人同天同项目
        addCommand(checkSamePersonDayItemCommand);

        // 校验 检验项目限制性别
        addCommand(checkTestItemLimitSexCommand);

        // 填充申请信息
        addCommand(updateFillApplyAndDiffInfoCommand);

        //新增送检机构部门或医生基础数据
        addCommand(addHspOrgDeptOrDoctorCommand);

        // 新增临床诊断
        addCommand(addDiagnosisCommand);

        // 保存申请
        addCommand(updateSaveApplyCommand);

        // 流水
        addCommand(updateFlowCommand);

        // 后置处理
        addCommand(updatePostCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);

    }
}
