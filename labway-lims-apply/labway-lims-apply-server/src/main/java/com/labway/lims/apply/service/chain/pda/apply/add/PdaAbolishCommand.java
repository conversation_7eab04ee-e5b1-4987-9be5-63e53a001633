package com.labway.lims.apply.service.chain.pda.apply.add;

import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.service.chain.apply.add.AddApplyContext;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class PdaAbolishCommand implements Command {

    @Resource
    private PdaApplyService pdaApplyService;

    @Override
    public boolean execute (Context c) throws Exception {
        final AddApplyContext from = AddApplyContext.from(c);

        pdaApplyService.masterBarcodeIsAbolish(from.getApply().getMasterBarcode());

        return CONTINUE_PROCESSING;
    }


}
