package com.labway.lims.apply.service.chain.apply.add;

import cn.hutool.core.date.DateUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.dto.ApplyRecordDto;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.HisTestApplyDto;
import com.labway.lims.apply.api.dto.HisTestApplyItemDto;
import com.labway.lims.apply.api.dto.InformationEntryTestApplyDto;
import com.labway.lims.apply.api.dto.PdaEntryTestApplyDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.dto.UpdateTestApplyDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.TestItemService;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Component
public class CheckParamCommand implements Command {

    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private ReportItemService reportItemService;
    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private SystemParamService systemParamService;


    @Override
    public boolean execute(Context c) throws Exception {
        final AddApplyContext from = AddApplyContext.from(c);
        final TestApplyDto testApplyDto = from.getTestApply();

        // 校验信息
        if (Objects.isNull(testApplyDto)) {
            throw new IllegalStateException("申请单信息为空");
        }

        // 页面传输的项目不能为空
        final List<TestApplyDto.Item> addApplyItems = testApplyDto.getItems();
        if (CollectionUtils.isEmpty(addApplyItems)) {
            throw new IllegalStateException("申请单检验项目信息为空");
        }

        // 外送机构
        final Long hspOrgId = testApplyDto.getHspOrgId();
        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("送检机构不存在");
        }
        // 送检机构未停用
        if (Objects.equals(hspOrganization.getEnable(), YesOrNoEnum.NO.getCode())) {
            throw new IllegalStateException("送检机构已停用");
        }


        // 校验要保存的项目是否有重复项
        final int toSetCount = addApplyItems.stream()
                .map(TestApplyDto.Item::getTestItemId).collect(Collectors.toSet()).size();

        if (!Objects.equals(toSetCount, addApplyItems.size())) {
            throw new IllegalArgumentException("检验项目出现重复");
        }

        // 检验项目
        final Collection<Long> testItemIds = addApplyItems.stream().map(TestApplyDto.Item::getTestItemId).collect(Collectors.toSet());
        List<TestItemDto> testItems = testItemService.selectByTestItemIds(testItemIds);
        if (CollectionUtils.isEmpty(testItems)) {
            throw new IllegalStateException("检验项目不存在");
        }

        // 校验保存的检验项目是否存在
        final Collection<Long> existTestItemIds = testItems.stream().map(TestItemDto::getTestItemId).collect(Collectors.toSet());
        for (final TestApplyDto.Item addApplyItem : addApplyItems) {
            if (!existTestItemIds.contains(addApplyItem.getTestItemId())) {
                throw new IllegalStateException(String.format("检验项目 [%s] 不存在", addApplyItem.getTestItemId()));
            }
        }

        // 保存的检验项目Map key = testItemId   value = 保存的检验项目
        final Map<Long, TestApplyDto.Item> testApplyItemMap = addApplyItems.stream().collect(Collectors.toMap(TestApplyDto.Item::getTestItemId, Function.identity(), (a, b) -> a));

        final boolean skipBloodCulture = from.getSkipBloodCulture();
        if (!skipBloodCulture) {
            // 如果包含血培养项目则校验血培养信息
            for (TestItemDto testItem : testItems) {
                // dev-*******-shanghai  【血培养及鉴定这个项目，项目类型为微生物检验，也能弹窗填写瓶数】 https://www.tapd.cn/tapd_fe/59091617/story/detail/1159091617001003151
                final Set<String> testItemCodes = from.getBloodCultureTestItemCodes();
                // 两种都兼容一下
                if (testItemCodes.contains(testItem.getTestItemCode()) || Objects.equals(testItem.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())) {
                    Optional.ofNullable(testApplyItemMap.get(testItem.getTestItemId())).ifPresent(a -> {
                        final ApplySampleItemBloodCultureDto bloodCulture = a.getBloodCulture();
                        // 检查参数
                        checkBloodCultureInfo(bloodCulture);
                    });
                }
            }
        }

        // 校验检验项目 限制性别
        if (BooleanUtils.isFalse(testApplyDto.getIgnoreItemLimitSex())) {
            log.info("检验项目限制性别标识：{}", testApplyDto.getIgnoreItemLimitSex());
            checkTestItemLimitSex(testApplyDto, testItems);
        }

        // 校验病人 同人同天同项目提示
        if (testApplyDto.getIgnoreSameItem() == null || Objects.equals(testApplyDto.getIgnoreSameItem(), 0)) {
            log.info("同人同天同项目标识：{}", testApplyDto.getIgnoreSameItem());
            checkSameItem(testApplyDto);
        }

        // 查询检验项目下的报告项目去重
        final Map<String, List<ReportItemDto>> reportItems = reportItemService.selectByTestItemCodesAsMap(testItems.stream()
                .map(TestItemDto::getTestItemCode)
                .collect(Collectors.toSet()), LoginUserHandler.get().getOrgId());

        // 样本签收时可以修改管型和样本类型
        updateTubeAndSampleType(testApplyDto, testItems);
        // 样本录入的时候可以修改样本类型
        updateSampleType(testApplyDto, testItems);

        from.put(AddApplyContext.HSP_ORG, hspOrganization);
        from.put(AddApplyContext.TEST_ITEMS, testItems);
        from.put(AddApplyContext.REPORT_ITEMS, reportItems);

        return CONTINUE_PROCESSING;
    }

    /**
     * 样本录入 or  PDA样本录入修改 的时候可以修改样本类型 和 管型
     * @param testApplyDto  保存的申请单
     * @param testItems db检验项目
     */
    public void updateSampleType(TestApplyDto testApplyDto, List<TestItemDto> testItems) {
        if (testApplyDto instanceof InformationEntryTestApplyDto
                || testApplyDto instanceof PdaEntryTestApplyDto
                || testApplyDto instanceof UpdateTestApplyDto) {
            List<TestApplyDto.Item> informationEntryTestApplyItems = testApplyDto.getItems();
            //Map<Long, InformationEntryTestApplyDto> testItemParamMapByTestItemId = hisTestApplyItems.stream().collect(Collectors.toMap(HisTestApplyItemDto::getTestItemId, Function.identity(), (a, b) -> a));
            Map<Long, TestApplyDto.Item> itemMap = informationEntryTestApplyItems.stream().collect(Collectors.toMap(TestApplyDto.Item::getTestItemId, Function.identity(), (a, b) -> a));
            for (TestItemDto testItem : testItems) {
                Long testItemId = testItem.getTestItemId();
                TestApplyDto.Item testApplyItemDto = itemMap.get(testItemId);
                testItem.setSampleTypeCode(testApplyItemDto.getSampleTypeCode());
                testItem.setSampleTypeName(testApplyItemDto.getSampleTypeName());
            }
        }
    }

    /**
     * 样本签收 的时候可以修改样本类型 和 管型
     * @param testApplyDto  保存的申请单
     * @param testItems db检验项目
     */
    public void updateTubeAndSampleType(TestApplyDto testApplyDto, List<TestItemDto> testItems) {
        if (testApplyDto instanceof HisTestApplyDto) {
            List<HisTestApplyItemDto> hisTestApplyItems = ((HisTestApplyDto) testApplyDto).getHisTestApplyItems();
            Map<Long, HisTestApplyItemDto> testItemParamMapByTestItemId = hisTestApplyItems.stream().collect(Collectors.toMap(HisTestApplyItemDto::getTestItemId, Function.identity(), (a, b) -> a));

            for (TestItemDto testItem : testItems) {
                Long testItemId = testItem.getTestItemId();
                HisTestApplyItemDto testApplyItemDto = testItemParamMapByTestItemId.get(testItemId);
                testItem.setTubeCode(testApplyItemDto.getTubeCode());
                testItem.setTubeName(testApplyItemDto.getTubeName());
                testItem.setSampleTypeCode(testApplyItemDto.getSampleTypeCode());
                testItem.setSampleTypeName(testApplyItemDto.getSampleTypeName());
            }
        }
    }

    /**
     * 检查通过
     */
    public void checkBloodCultureInfo(ApplySampleItemBloodCultureDto bloodCulture) {

        if (Objects.isNull(bloodCulture)) {
            throw new IllegalStateException("血培养信息为空");
        }

        // 左上肢
        final Integer lulAnaerobic = bloodCulture.getLulAnaerobic();
        final Integer lulAerobic = bloodCulture.getLulAerobic();
        final Integer lulPediatricBottle = bloodCulture.getLulPediatricBottle();
        Boolean lulAllNull = Objects.isNull(lulAnaerobic) &&
                Objects.isNull(lulAerobic) &&
                Objects.isNull(lulPediatricBottle);

        // 右上肢
        final Integer rulAnaerobic = bloodCulture.getRulAnaerobic();
        final Integer rulAerobic = bloodCulture.getRulAerobic();
        final Integer rulPediatricBottle = bloodCulture.getRulPediatricBottle();
        Boolean ruAllNull = Objects.isNull(rulAnaerobic) &&
                Objects.isNull(rulAerobic) &&
                Objects.isNull(rulPediatricBottle);

        // 左下肢
        final Integer lllAnaerobic = bloodCulture.getLllAnaerobic();
        final Integer lllAerobic = bloodCulture.getLllAerobic();
        final Integer lllPediatricBottle = bloodCulture.getLllPediatricBottle();
        Boolean lllAllNull = Objects.isNull(lllAnaerobic) &&
                Objects.isNull(lllAerobic) &&
                Objects.isNull(lllPediatricBottle);

        // 右下肢
        final Integer rllAnaerobic = bloodCulture.getRllAnaerobic();
        final Integer rllAerobic = bloodCulture.getRllAerobic();
        final Integer rllPediatricBottle = bloodCulture.getRllPediatricBottle();
        Boolean rllAllNull = Objects.isNull(rllAnaerobic) &&
                Objects.isNull(rllAerobic) &&
                Objects.isNull(rllPediatricBottle);

        // 普通
        final Integer anaerobic = bloodCulture.getAnaerobic();
        final Integer aerobic = bloodCulture.getAerobic();
        final Integer pediatricBottle = bloodCulture.getPediatricBottle();
        Boolean normalAllNull = Objects.isNull(anaerobic) &&
                Objects.isNull(aerobic) &&
                Objects.isNull(pediatricBottle);

        if (BooleanUtils.isTrue(lulAllNull)
                && BooleanUtils.isTrue(ruAllNull)
                && BooleanUtils.isTrue(lllAllNull)
                && BooleanUtils.isTrue(rllAllNull)
                && BooleanUtils.isTrue(normalAllNull)) {
            throw new IllegalStateException("请填写血培养信息");
        }

        // 左上肢
        if (Objects.nonNull(lulPediatricBottle) && (Objects.nonNull(lulAnaerobic) || Objects.nonNull(lulAerobic))) {
            throw new IllegalStateException("左上肢儿童瓶与其他瓶不能同时存在");
        }

        // 右上肢
        if (Objects.nonNull(rulPediatricBottle) && (Objects.nonNull(rulAnaerobic) || Objects.nonNull(rulAerobic))) {
            throw new IllegalStateException("右上肢儿童瓶与其他瓶不能同时存在");
        }

        // 左下肢
        if (Objects.nonNull(lllPediatricBottle) && (Objects.nonNull(lllAnaerobic) || Objects.nonNull(lllAerobic))) {
            throw new IllegalStateException("左下肢儿童瓶与其他瓶不能同时存在");
        }

        // 左上肢
        if (Objects.nonNull(rllPediatricBottle) && (Objects.nonNull(rllAnaerobic) || Objects.nonNull(rllAerobic))) {
            throw new IllegalStateException("右下肢儿童瓶与其他瓶不能同时存在");
        }


        // 默认
        if (Objects.nonNull(pediatricBottle) && (Objects.nonNull(anaerobic) || Objects.nonNull(aerobic))) {
            throw new IllegalStateException("儿童瓶与其他瓶不能同时存在");
        }

    }

    // 校验同人同天同项目 （根据送检机构 姓名 性别判断）
    public void checkSameItem(TestApplyDto testApplyDto) {
        this.checkSameItem(testApplyDto, null);
    }

    // 校验同人同天同项目 （根据送检机构 姓名 性别判断）
    public void checkSameItem(TestApplyDto testApplyDto, List<ApplySampleItemDto> applySampleItems) {
        Long hspOrgId = testApplyDto.getHspOrgId();
        String patientName = testApplyDto.getPatientName();
        Integer patientSex = testApplyDto.getPatientSex();
        String patientCard = testApplyDto.getPatientCard();
        List<TestApplyDto.Item> items = testApplyDto.getItems();

        final Date now = new Date();
        final ApplyRecordDto applyRecord = new ApplyRecordDto();
        applyRecord.setDateStart(DateUtil.beginOfDay(now));
        applyRecord.setDateEnd(DateUtil.endOfDay(now));
        applyRecord.setPatientName(patientName);
        applyRecord.setPatientSex(patientSex);
        applyRecord.setPatientCard(patientCard);
        applyRecord.setHspOrgId(hspOrgId);
        applyRecord.setPatientAge(testApplyDto.getPatientAge());
        applyRecord.setApplyId(testApplyDto.getApplyId());

        // 查询同人同天的申请单
        final Collection<Long> applyIds = applyService.selectByHspOrgIdAndPatientNameAndSex(applyRecord);
        if (CollectionUtils.isEmpty(applyIds)) {
            return;
        }

        // 查询申请单的检验项目信息
        List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplyIds(applyIds);
        if (CollectionUtils.isEmpty(applySampleItemDtos)){
            return;
        }
        List<Long> existTestItemIds = applySampleItemDtos.stream().map(e -> e.getTestItemId()).collect(Collectors.toList());

        String existItemStr = "";
        if (CollectionUtils.isEmpty(applySampleItems)) {
            // 判断是否有重复的项目
            existItemStr = items.stream().filter(e -> existTestItemIds.contains(e.getTestItemId()))
                    .map(TestApplyDto.Item::getTestItemName).collect(Collectors.joining(","));
        } else {
            existItemStr = applySampleItems.stream().filter(e -> existTestItemIds.contains(e.getTestItemId()))
                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.joining(","));
        }

        if (StringUtils.isBlank(existItemStr)) {
            return;
        }

        LimsCodeException exception = new LimsCodeException(1119, "患者【" + patientName+ "】，今天已经录入过检验项目：【" + existItemStr + "】，请确认是否再次录入！！！");
        exception.setData(Map.of(
                "patientName", StringUtils.defaultString(patientName),
                "outItemNames", existItemStr));
        throw exception;
    }

    public void checkTestItemLimitSex(TestApplyDto testApplyDto, List<TestItemDto> testItemDtos) {
        Integer patientSex = testApplyDto.getPatientSex();
        String patientName = testApplyDto.getPatientName();

        if (CollectionUtils.isEmpty(testItemDtos)) {
            return;
        }

        // 如果性别不是 男 或者 女，跳过
        if (!Arrays.asList(SexEnum.MAN.getCode(), SexEnum.WOMEN.getCode()).contains(patientSex)) {
            return;
        }

        // 筛选出来 未匹配 限制性别的项目
        List<TestItemDto> testItemsNonmatch = testItemDtos.stream()
                .filter(e -> Objects.nonNull(e.getLimitSex()) && !Objects.equals(SexEnum.DEFAULT.getCode(), e.getLimitSex()))
                .filter(e -> !Objects.equals(patientSex, e.getLimitSex()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(testItemsNonmatch)) {
            String testItemNames = testItemsNonmatch.stream().map(TestItemDto::getTestItemName).collect(Collectors.joining(StringPool.COMMA));
            String sex = SexEnum.getByCode(testItemsNonmatch.stream().findFirst().map(TestItemDto::getLimitSex).get()).getDesc();
            LimsCodeException exception = new LimsCodeException(1122, String.format("【%s】项目限制性别为：%s，是否继续保存？", testItemNames, sex));
            exception.setData(Map.of(
                    "patientName", StringUtils.defaultString(patientName),
                    "outItemNames", testItemNames));
            throw exception;
        }
    }
}
