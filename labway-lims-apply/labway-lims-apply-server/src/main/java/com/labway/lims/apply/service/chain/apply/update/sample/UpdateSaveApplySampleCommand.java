package com.labway.lims.apply.service.chain.apply.update.sample;

import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleItemBloodCultureService;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import lombok.Getter;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 保存信息
 */
@Getter
@Component
public class UpdateSaveApplySampleCommand implements Command {
    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private ApplySampleItemBloodCultureService applySampleItemBloodCultureService;

    @Override
    public boolean execute(Context c) throws Exception {

        UpdateApplyContext context = UpdateApplyContext.from(c);
        ApplyDto apply = context.getApply();
        context.getHspOrganization();
        ApplySampleDto updateApplySample = context.getUpdateApplySample();
        if (Objects.nonNull(updateApplySample)) {
            applySampleService.updateByApplySampleId(updateApplySample);
        }
        if (Objects.nonNull(apply)){
            applySampleService.updateHospByApplyId(apply);
        }
        return CONTINUE_PROCESSING;
    }
}
