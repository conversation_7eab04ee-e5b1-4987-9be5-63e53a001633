package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.model.TbSampleImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * TbSampleImageMapper
 * 样本图片
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/28 10:11
 */
@Mapper
public interface TbSampleImageMapper extends BaseMapper<TbSampleImage> {

    int insertBatch(@Param("sampleImageDtos") List<SampleImageDto> sampleImageDtos);

}
