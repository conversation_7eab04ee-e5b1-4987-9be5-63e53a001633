package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.apply.api.dto.PhysicalRegisterDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 获取 体检花名册 Vo
 * 
 * <AUTHOR>
 * @since 2023/4/4 17:54
 */
@Getter
@Setter
public class SelectByPhysicalBatchResponseVo extends PhysicalRegisterDto {

    /**
     * 条码 当 体检人打印状态为已打印时 存在
     */
    private List<String> barcodeList;

    /**
     * 采样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Shanghai")
    private Date samplingDate;
}
