package com.labway.lims.apply.model.es;

import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.*;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Document(indexName = "lims-sample")
public class BaseSampleEsModel {
    /**
     * 申请单样本id 主键
     */
    @Id
    @Field(type = FieldType.Long)
    private Long applySampleId;

    // 申请单信息

    /**
     * 申请单id
     */
    @Field(type = FieldType.Long)
    private Long applyId;

    /**
     * 主条码
     */
    @Field(type = FieldType.Keyword)
    private String masterBarcode;

    /**
     * 科室
     */
    @Field(type = FieldType.Keyword)
    private String dept;

    /**
     * 是否加急 1是 0否
     *
     * @see UrgentEnum
     */
    @Field(type = FieldType.Keyword)
    private Integer urgent;
    /**
     * 1: 急诊，0:不急
     *
     * @see UrgentEnum
     */
    @Field(type = FieldType.Keyword)
    private Integer applyUrgent;
    /**
     * 申请单状态
     *
     * @see ApplyStatusEnum
     */
    @Field(type = FieldType.Integer)
    private Integer applyStatus;

    /**
     * 患者名称
     */
    @Field(type = FieldType.Keyword)
    private String patientName;

    /**
     * 患者年龄
     */
    @Field(type = FieldType.Integer)
    private Integer patientAge;

    /**
     * 患者子年龄
     */
    @Field(type = FieldType.Integer)
    private Integer patientSubage;

    /**
     * 患者子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    @Field(type = FieldType.Keyword)
    private String patientSubageUnit;

    /**
     * 患者出生日期
     */
    @Field(type = FieldType.Date)
    private Date patientBirthday;

    /**
     * 身份证
     */
    @Field(type = FieldType.Keyword)
    private String patientCard;

    /**
     * 证件类型
     *
     * @see PatientCardTypeEnum
     */
    @Field(type = FieldType.Keyword)
    private String patientCardType;

    /**
     * 床号
     */
    @Field(type = FieldType.Keyword)
    private String patientBed;

    /**
     * 性别
     *
     * @see com.labway.lims.api.enums.apply.SexEnum
     */
    @Field(type = FieldType.Integer)
    private Integer patientSex;

    /**
     * 就诊卡号 (门诊|住院号)
     */
    @Field(type = FieldType.Keyword)
    private String patientVisitCard;

    /**
     * 手机号
     */
    @Field(type = FieldType.Keyword)
    private String patientMobile;

    /**
     * 患者地址
     */
    @Field(type = FieldType.Keyword)
    private String patientAddress;

    /**
     * 就诊类型 (申请类型)
     */
    @Field(type = FieldType.Keyword)
    private String applyTypeCode;

    /**
     * 就诊类型 (申请类型)
     */
    @Field(type = FieldType.Keyword)
    private String applyTypeName;

    /**
     * 样本个数
     */
    @Field(type = FieldType.Integer)
    private Integer sampleCount;

    /**
     * 样本性状
     */
    @Field(type = FieldType.Keyword)
    private String sampleProperty;

    /**
     * 样本性状编码
     */
    @Field(type = FieldType.Keyword)
    private String samplePropertyCode;

    /**
     * 临床诊断
     */
    @Field(type = FieldType.Keyword)
    private String diagnosis;

    /**
     * 送检医生名称
     */
    @Field(type = FieldType.Keyword)
    private String sendDoctorName;

    /**
     * 送检医生编码
     */
    @Field(type = FieldType.Keyword)
    private String sendDoctorCode;

    /**
     * 申请时间
     */
    @Field(type = FieldType.Date)
    private Date applyDate;

    /**
     * 采样时间
     */
    @Field(type = FieldType.Date)
    private Date samplingDate;

    /**
     * 备注
     */
    @Field(type = FieldType.Text)
    private String remark;

    /**
     * 申请单来源
     *
     * @see ApplySourceEnum
     */
    @Field(type = FieldType.Keyword)
    private String source;

    /**
     * 供应商
     *
     * @see ApplySupplierEnum
     */
    @Field(type = FieldType.Keyword)
    private String supplier;

    /**
     * 送检机构
     */
    @Field(type = FieldType.Long)
    private Long hspOrgId;

    /**
     * 送检机构编码
     */
    @Field(type = FieldType.Keyword)
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    @Field(type = FieldType.Keyword)
    private String hspOrgName;

    // 申请单样本信息
    /**
     * 外部条码
     */
    @Field(type = FieldType.Keyword)
    private String outBarcode;

    /**
     * 样本条码
     */
    @Field(type = FieldType.Keyword)
    private String barcode;

    /**
     * 管型
     */
    @Field(type = FieldType.Keyword)
    private String tubeName;

    /**
     * 管型编码
     */
    @Field(type = FieldType.Keyword)
    private String tubeCode;

    /**
     * 样本类型
     */
    @Field(type = FieldType.Keyword)
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    @Field(type = FieldType.Keyword)
    private String sampleTypeName;

    /**
     * 专业组id
     */
    @Field(type = FieldType.Long)
    private Long groupId;


    /**
     * 专业组名称
     */
    @Field(type = FieldType.Keyword)
    private String groupName;

    /**
     * 样本状态
     *
     * @see SampleStatusEnum
     */
    @Field(type = FieldType.Integer)
    private Integer sampleStatus;

    /**
     * 试管架id
     */
    @Field(type = FieldType.Long)
    private Long rackId;

    /**
     * 一次分拣人id
     */
    @Field(type = FieldType.Long)
    private Long onePickerId;

    /**
     * 一次分拣人
     */
    @Field(type = FieldType.Keyword)
    private String onePickerName;

    /**
     * 一次分拣日期
     */
    @Field(type = FieldType.Date)
    private Date onePickDate;

    /**
     * 是否已经一次分拣 1是，0不是
     */
    @Field(type = FieldType.Integer)
    private Integer isOnePick;

    /**
     * 二次分拣人id
     */
    @Field(type = FieldType.Long)
    private Long twoPickerId;

    /**
     * 二次分拣人
     */
    @Field(type = FieldType.Keyword)
    private String twoPickerName;

    /**
     * 二次分拣日期
     */
    @Field(type = FieldType.Date)
    private Date twoPickDate;

    /**
     * 是否已经二次分拣 1是，0不是
     */
    @Field(type = FieldType.Integer)
    private Integer isTwoPick;

    /**
     * 是否是免疫二次分拣 1：是 0：否
     */
    @Field(type = FieldType.Integer)
    private Integer isImmunityTwoPick;

    /**
     * 是否已经分血，1：是，0：不是
     */
    @Field(type = FieldType.Integer)
    private Integer isSplitBlood;

    /**
     * 分血人ID
     */
    @Field(type = FieldType.Long)
    private Long splitterId;

    /**
     * 分血人
     */
    @Field(type = FieldType.Keyword)
    private String splitterName;

    /**
     * 分血时间
     */
    @Field(type = FieldType.Date)
    private Date splitDate;

    // 检验样本信息 （常规检验、微生物检验、院感......）
    /**
     * 样本id （可能是常规检验|微生物|院感...）
     */
    @Field(type = FieldType.Long)
    private Long sampleId;

    /**
     * 样本号
     */
    @Field(type = FieldType.Keyword)
    private String sampleNo;

    /**
     * 专业小组id
     */
    @Field(type = FieldType.Long)
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    @Field(type = FieldType.Keyword)
    private String instrumentGroupName;

    /**
     * 检验时间
     */
    @Field(type = FieldType.Date)
    private Date testDate;

    /**
     * 样本备注
     */
    @Field(type = FieldType.Text)
    private String sampleRemark;

    /**
     * 结果备注
     */
    @Field(type = FieldType.Text)
    private String resultRemark;

    /**
     * 终审人id
     */
    @Field(type = FieldType.Long)
    private Long finalCheckerId;

    /**
     * 终审人
     */
    @Field(type = FieldType.Keyword)
    private String finalCheckerName;

    /**
     * 终审时间
     */
    @Field(type = FieldType.Date)
    private Date finalCheckDate;

    /**
     * 项目类型 常规检验、微生物检验、院感检验 区分做什么检验的
     *
     * @see ItemTypeEnum
     */
    @Field(type = FieldType.Keyword)
    private String itemType;

    /**
     * 检验项目
     */
    @Field(type = FieldType.Nested)
    private List<TestItem> testItems;

    /**
     * 通用报告信息
     */
    @Field(type = FieldType.Nested)
    private List<Report> reports;

    /**
     * 机构id
     */
    @Field(type = FieldType.Long)
    private Long orgId;

    /**
     * 机构名称
     */
    @Field(type = FieldType.Keyword)
    private String orgName;

    /**
     * 创建人id
     */
    @Field(type = FieldType.Long)
    private Long creatorId;

    /**
     * 创建人
     */
    @Field(type = FieldType.Keyword)
    private String creatorName;

    /**
     * 创建时间|录入时间
     */
    @Field(type = FieldType.Date)
    private Date createDate;

    /**
     * 是否删除 1是 0否
     */
    @Field(type = FieldType.Integer)
    private Integer isDelete;

    /**
     * 修改人id
     */
    @Field(type = FieldType.Long)
    private Long updaterId;

    /**
     * 修改人
     */
    @Field(type = FieldType.Keyword)
    private String updaterName;

    /**
     * 修改人时间|录入时间
     */
    @Field(type = FieldType.Date)
    private Date updateDate;

    /**
     * 是否打印:1已打印，0未打印
     */
    @Field(type = FieldType.Integer)
    private Integer isPrint;

    /**
     * 是否外送
     *
     * @see YesOrNoEnum
     */
    @Field(type = FieldType.Integer)
    private Integer isOutsourcing;

    /**
     * 打印人ID
     */
    @Field(type = FieldType.Long)
    private Long printerId;

    /**
     * 打印人姓名
     */
    @Field(type = FieldType.Keyword)
    private String printerName;

    /**
     * 打印时间
     */
    @Field(type = FieldType.Date)
    private Date printDate;
    /**
     * 检验人ID
     */
    @Field(type = FieldType.Long)
    private Long testerId;
    /**
     * 检验人姓名
     */
    @Field(type = FieldType.Keyword)
    private String testerName;

    /**
     * 体检单位
     */
    @Field(type = FieldType.Long)
    private Long physicalCompanyId;

    /**
     * 体检单位名称
     */
    @Field(type = FieldType.Keyword)
    private String physicalCompanyName;
    /**
     * 原始机构编码
     */
    @Field(type = FieldType.Keyword)
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    @Field(type = FieldType.Keyword)
    private String originalOrgName;

    /**
     * 复核人id
     */
    @Field(type = FieldType.Long)
    private Long checkerId;
    /**
     * 复核人
     */
    @Field(type = FieldType.Keyword)
    private String checkerName;
    /**
     * 复核时间
     */
    @Field(type = FieldType.Date)
    private Date checkDate;
	/**
	 * 标本部位
	 */
	@Field(type = FieldType.Keyword)
	private String patientPart;

    /**
     * 报告单
     */
    @Getter
    @Setter
    public static final class Report {
        /**
         * 报告url
         */
        private String url;
        /**
         * 报告类型
         *
         * @see SampleReportFileTypeEnum
         */
        private String fileType;
    }

    @Getter
    @Setter
    public static final class TestItem {
        /**
         * 样本检验项目id
         */
        @Field(type = FieldType.Long)
        private Long applySampleItemId;
        /**
         * 检验项目ID
         */
        @Field(type = FieldType.Long)
        private Long testItemId;
        /**
         * 检验项目编码
         */
        @Field(type = FieldType.Keyword)
        private String testItemCode;
        /**
         * 检验项目名称
         */
        @Field(type = FieldType.Keyword)
        private String testItemName;
        /**
         * 外部项目编码
         */
        @Field(type = FieldType.Long)
        private Long outTestItemId;
        /**
         * 外部项目名称
         */
        @Field(type = FieldType.Keyword)
        private String outTestItemCode;
        /**
         * 外部项目名称
         */
        @Field(type = FieldType.Keyword)
        private String outTestItemName;
        /**
         * 专业组id
         */
        @Field(type = FieldType.Long)
        private Long groupId;
        /**
         * 专业组名称
         */
        @Field(type = FieldType.Keyword)
        private String groupName;

        /**
         * @see com.labway.lims.api.enums.apply.UrgentEnum
         */
        @Field(type = FieldType.Integer)
        private Integer urgent;

        /**
         * 收费数量
         */
        @Field(type = FieldType.Integer)
        private Integer count;

        /**
         * 创建时间
         */
        @Field(type = FieldType.Date)
        private Date createDate;

        /**
         * 单价
         */
        @Field(type = FieldType.Scaled_Float, scalingFactor = 100)
        private BigDecimal price;
        /**
         * 是否免单
         *
         * @see YesOrNoEnum
         */
        @Field(type = FieldType.Integer)
        private Integer isFree;
        /**
         * 终止检验状态：0正常，1终止收费，2终止不收费
         *
         * @see StopTestStatus
         */
        @Field(type = FieldType.Integer)
        private Integer stopStatus;

        /**
         * 终止检验原因code
         */
        @Field(type = FieldType.Keyword)
        private String stopReasonCode;
        /**
         * 终止检验原因value
         */
        @Field(type = FieldType.Keyword)
        private String stopReasonName;
    }

}
