package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/6/8 10:30
 */
@Getter
@Setter
public class DisplayImportDataRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件保存的路径
     */
    private String url;

    /**
     * 业务单元编码
     */
    private String orgCode;

    /**
     * 业务单元名称
     */
    private String orgName;

    /**
     * 客商编码
     */
    private String customerCode;

    /**
     * 客商名称
     */
    private String customerName;


}
