package com.labway.lims.apply.controller.pda;


import cn.hutool.core.lang.Assert;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyMasterBarcodeDto;
import com.labway.lims.apply.api.dto.BaseDataQueryDto;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.PdaTobeConfirmedApplyDto;
import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.api.service.pda.PdaTobeConfirmedApplyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/pda/tobe-confirmed")
public class PdaTobeConfirmedApplyController extends BaseController {

    @Resource
    private PdaTobeConfirmedApplyService pdaTobeConfirmedApplyService;

    @Resource
    private PdaApplyService pdaApplyService;

    @PostMapping("/select")
    public Object selectPdaApply(@RequestBody BaseDataQueryDto dto) {
        Assert.notNull(dto.getStartDate(), "起始时间不能为空");
        Assert.notNull(dto.getEndDate(), "结束时间不能为空");
        if (dto.getStartDate().after(dto.getEndDate())) {
            throw new IllegalArgumentException("起始时间不能晚于结束时间");
        }

        if (dto.getStatus() < 0) {
            List<PdaApplyDto> pdaApplyDtos = pdaApplyService.selectByDate(dto, null);

            Set<String> addedMasterBarcodes = pdaTobeConfirmedApplyService
                    .selectByMasterBarcodes(pdaApplyDtos.stream().map(PdaApplyDto::getMasterBarcode).collect(Collectors.toSet()))
                    .stream().map(PdaTobeConfirmedApplyDto::getMasterBarcode).collect(Collectors.toSet());

            // 过滤出来只单录过的返回
            return pdaApplyDtos.stream().filter(pdaApplyDto -> !addedMasterBarcodes.contains(pdaApplyDto.getMasterBarcode()));
        }

        return pdaTobeConfirmedApplyService.selectByDate(dto);
    }

    /**
     * 确认pda申请单
     */
    @PostMapping("/confirm")
    public Object confirmPdaApply(@RequestBody PdaApplyDto dto) {
        if (Objects.isNull(dto.getPdaApplyId())) {
            throw new IllegalArgumentException("参数错误");
        }
        return pdaTobeConfirmedApplyService.confirmApply(dto.getPdaApplyId());
    }

    /**
     * 取消确认（回退）pda申请单
     */
    @PostMapping("/rollback")
    public Object rollbackPdaApply(@RequestBody ApplyMasterBarcodeDto dto) {
        if (Objects.isNull(dto.getMasterBarcode())) {
            throw new IllegalArgumentException("主条码不能为空");
        }
        return pdaTobeConfirmedApplyService.rollbackApply(dto.getMasterBarcode());
    }


}
