package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.PhysicalBatchDto;
import com.labway.lims.apply.model.TbPhysicalBatch;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 体检批次 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface PhysicalBatchConverter {

    PhysicalBatchDto fromTbPhysicalBatch(TbPhysicalBatch obj);
    TbPhysicalBatch tbPhysicalBatchFromTbDto(PhysicalBatchDto obj);

    List<PhysicalBatchDto> fromTbPhysicalBatchList(List<TbPhysicalBatch> list);
}
