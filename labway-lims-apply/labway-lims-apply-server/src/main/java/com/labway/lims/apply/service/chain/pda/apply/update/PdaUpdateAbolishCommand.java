package com.labway.lims.apply.service.chain.pda.apply.update;

import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class PdaUpdateAbolishCommand implements Command {

    @Resource
    private PdaApplyService pdaApplyService;

    @Override
    public boolean execute (Context c) throws Exception {
        final UpdateApplyContext from = UpdateApplyContext.from(c);
        pdaApplyService.masterBarcodeIsAbolish(from.getApply().getMasterBarcode());
        return CONTINUE_PROCESSING;
    }
}
