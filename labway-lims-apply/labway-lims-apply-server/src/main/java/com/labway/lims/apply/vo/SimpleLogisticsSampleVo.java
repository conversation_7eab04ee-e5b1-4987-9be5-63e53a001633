package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class SimpleLogisticsSampleVo {

    /**
     * 样本id
     */
    private Long applySampleId;

    /**
     * 物流申请单Id
     */
    private Long applyLogisticsId;

    /**
     * 物流样本id
     */
    private Long applyLogisticsSampleId;

    /**
     * 申请单id
     */
    private Long applyId;
    /**
     * 样本条码
     */
    private String barcode;

    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 取货日期
     */
    private Date receiveDate;

    /**
     * 物流人
     */
    private Long logisticsUserId;

    /**
     * 物流人员
     */
    private String logisticsUserName;

    /**
     * 申请单图片
     */
    private String applyImage;


}
