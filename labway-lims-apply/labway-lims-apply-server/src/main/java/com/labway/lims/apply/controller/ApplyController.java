package com.labway.lims.apply.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.SampleSourceEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.DeleteImagesDto;
import com.labway.lims.apply.api.dto.InformationEntryTestApplyDto;
import com.labway.lims.apply.api.dto.PrefabricateTestApplyDto;
import com.labway.lims.apply.api.dto.SampleApplyDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SimpleTestApplyDto;
import com.labway.lims.apply.api.dto.UnreviewedApplyQueryDto;
import com.labway.lims.apply.api.dto.UpdateTestApplyDto;
import com.labway.lims.apply.api.dto.UploadImagesDto;
import com.labway.lims.apply.api.service.ApplySampleImageService;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.vo.ApplyInfoVo;
import com.labway.lims.apply.vo.ApplyVo;
import com.labway.lims.apply.vo.DeleteImagesVo;
import com.labway.lims.apply.vo.InformationEntryTestApplyVo;
import com.labway.lims.apply.vo.PrefabricateTestApplyVo;
import com.labway.lims.apply.vo.ReportItemVo;
import com.labway.lims.apply.vo.SampleApplyVo;
import com.labway.lims.apply.vo.SampleItemVo;
import com.labway.lims.apply.vo.SimpleTestApplyVo;
import com.labway.lims.apply.vo.TestApplyVo;
import com.labway.lims.apply.vo.UpdateTestApplyVo;
import com.labway.lims.apply.vo.UploadImagesVo;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.TestItemService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 申请单
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/apply")
public class ApplyController extends BaseController {

    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private TestItemService testItemService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    SampleFlowService sampleFlowService;
    @Resource
    private ApplySampleImageService applySampleImageService;

    /**
     * 申请单录入套餐缓存RedisKey
     */
    public static final String APPLAY_PACKAGE_CACHE_KEY = "%sAPPLAYPACKAGE:%s";


    /**
     * 申请单修改
     */
    @PostMapping("/simple-update")
    public Object simpleUpdate(@RequestBody SimpleTestApplyVo testApplyVo) {
        final Long applyId = testApplyVo.getApplyId();
        if (Objects.isNull(applyId)) {
            throw new IllegalArgumentException("申请单id不能为空");
        }

        // 检查申请单参数
        checkApplyParam(testApplyVo);

        final String lockKey = redisPrefix.getBasePrefix() + "apply:lock:" + applyId;
        if (BooleanUtils.isNotTrue(
                stringRedisTemplate.opsForValue().setIfAbsent(lockKey, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException("申请单正在被修改,请稍后再试");
        }

        try {
            final SimpleTestApplyDto updateTestApply =
                    JSON.parseObject(JSON.toJSONString(testApplyVo), SimpleTestApplyDto.class);

            return Map.of("apply", applyService.updateBasicApplyInfo(updateTestApply));
        } finally {
            stringRedisTemplate.delete(lockKey);
        }
    }

    /**
     * 根据 applyId 查询申请单
     */
    @GetMapping("/get")
    public Object get(@RequestParam(required = false) Long applyId) {
        if (Objects.isNull(applyId)) {
            return Collections.emptyMap();
        }

        final ApplyDto apply = applyService.selectByApplyId(applyId);

        if (Objects.isNull(apply)) {
            return Collections.emptyMap();
        }
        // 获取申请单id对应的条码号
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplyId(applyId);
        String outBarcode = applySampleDtos.stream().map(ApplySampleDto::getOutBarcode).findFirst().orElse(null);

        final ApplyVo vo = JSON.parseObject(JSON.toJSONString(apply), ApplyVo.class);
        vo.setOutBarcode(outBarcode);

        ApplySampleDto applySampleDto = applySampleService.selectByApplyId(apply.getApplyId())
                .stream().findFirst().orElse(new ApplySampleDto());

        return Map.of("apply", vo,

                "barcode", StringUtils.defaultString(applySampleDto.getBarcode()),

                "outBarcode", StringUtils.defaultString(applySampleDto.getOutBarcode()));
    }

    /**
     * 提供给各个检验最左侧的检验样本信息
     */
    @GetMapping("/info")
    public Object info(@RequestParam Long applySampleId) {
        if (Objects.isNull(applySampleId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        final ApplyInfoVo applyInfo = new ApplyInfoVo();
        BeanUtils.copyProperties(apply, applyInfo);
        BeanUtils.copyProperties(applySample, applyInfo);

        applyInfo.setOneCheckerId(NumberUtils.LONG_ZERO);
        applyInfo.setOneCheckerName(LoginUserHandler.get().getNickname());
        applyInfo.setOneCheckDate(new Date());

        applyInfo.setTwoCheckerId(NumberUtils.LONG_ZERO);
        applyInfo.setTwoCheckerName(LoginUserHandler.get().getNickname());
        applyInfo.setTwoCheckDate(new Date());

        return applyInfo;

    }

    /**
     * 申请单检验项目列表
     */
    @GetMapping("/apply-test-items")
    public Object applyItems(@RequestParam(required = false) Long applyId, @RequestParam(required = false) Boolean all)
            throws ExecutionException, InterruptedException {
        if (Objects.isNull(applyId)) {
            return Collections.emptyList();
        }

        // 根据申请单id查询申请单项目
        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplyIds(List.of(applyId), all);
        final Collection<Long> testItemIds =
                applySampleItems.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());

        final ThreadPoolExecutor pool = threadPoolConfig.getPool();

        // 申请单样本
        final Map<Long, ApplySampleDto> applySampleDtoMap = applySampleService.selectByApplySampleIdsAsMap(applySampleItems.stream().map(ApplySampleItemDto::getApplySampleId).collect(Collectors.toList()));
        // 查检验项目 key->检验项目id value->检验项目
        final CompletableFuture<Map<Long, TestItemDto>> testItemMapFuture = CompletableFuture.supplyAsync(() -> {
            final List<TestItemDto> testItems = testItemService.selectByTestItemIds(testItemIds);
            if (CollectionUtils.isEmpty(testItems)) {
                return Collections.emptyMap();
            }
            return testItems.stream().collect(Collectors.toMap(TestItemDto::getTestItemId, v -> v, (a, b) -> a));
        }, pool);

        // 根据检验项目id查询报告项目
        final CompletableFuture<Map<Long, List<ReportItemDto>>> reportItemFuture =
                CompletableFuture.supplyAsync(() -> reportItemService.selectByTestItemIds(testItemIds).stream()
                        .collect(Collectors.groupingBy(ReportItemDto::getTestItemId)), pool);

        // 等待 future 全部完成
        CompletableFuture.allOf(reportItemFuture, testItemMapFuture).join();

        // 获取结果
        final Map<Long, TestItemDto> testItemMap = testItemMapFuture.get();
        final Map<Long, List<ReportItemDto>> reportItemMap = reportItemFuture.get();

        //获取套餐信息
        String packageKey = String.format(APPLAY_PACKAGE_CACHE_KEY, redisPrefix.getBasePrefix(), applyId);
        String result = stringRedisTemplate.opsForValue().get(packageKey);
        Map<Long, TestApplyVo.Item> itemPacakgeNameMap = new HashMap<>();
        if (StringUtils.isNotBlank(result)) {
            List<TestApplyVo.Item> applyItems = JSON.parseArray(result, TestApplyVo.Item.class);
            for (TestApplyVo.Item item : applyItems) {
                itemPacakgeNameMap.put(item.getTestItemId(), item);
            }
        }

        // 转换成功 SampleItemVo
        List<SampleItemVo> resultList = applySampleItems.stream().map(m -> {
            final SampleItemVo vo = JSON.parseObject(JSON.toJSONString(m), SampleItemVo.class);
            Optional.ofNullable(testItemMap.get(m.getTestItemId())).map(TestItemDto::getFeePrice)
                    .ifPresent(c -> vo.setPrice(m.getFeePrice()));
            Optional.ofNullable(reportItemMap.get(m.getTestItemId()))
                    .ifPresent(c -> vo.setReportItems(JSON.parseArray(JSON.toJSONString(c), ReportItemVo.class)));
            //新增英文名
            Optional.ofNullable(testItemMap.get(m.getTestItemId()))
                    .ifPresent(c -> {
                        //单价
                        vo.setPrice(m.getFeePrice());
                        //新增英文名
                        vo.setEnName(c.getEnName());
                        //检验方法
                        vo.setExamMethodName(c.getExamMethodName());
                    });
            // 新增条码号
            Optional.ofNullable(applySampleDtoMap.get(vo.getApplySampleId()))
                    .ifPresent(c -> vo.setBarcode(c.getBarcode()));

            //套餐项目信息
            if (!itemPacakgeNameMap.isEmpty()) {
                TestApplyVo.Item item = itemPacakgeNameMap.get(m.getTestItemId());
                if (item != null) {
                    vo.setPackageId(item.getPackageId());
                    vo.setPackageName(item.getPackageName());
                }
            }

            return vo;
        }).collect(Collectors.toList());

        if (!itemPacakgeNameMap.isEmpty()) {
            //根据套餐排序
            resultList = resultList.stream().sorted(Comparator.comparing(SampleItemVo::getPackageId)).collect(Collectors.toList());
        }

        return resultList;
    }

    /**
     * 修改申请单信息
     */
    @PostMapping("/update")
    public Object update(@RequestBody UpdateTestApplyVo testApplyVo) {
        final Long applyId = testApplyVo.getApplyId();
        if (Objects.isNull(applyId)) {
            throw new IllegalArgumentException("申请单id不能为空");
        }

        // 检查申请单参数
        checkApplyParam(testApplyVo);

        final String lockKey = redisPrefix.getBasePrefix() + "apply:lock:" + applyId;
        if (BooleanUtils.isNotTrue(
                stringRedisTemplate.opsForValue().setIfAbsent(lockKey, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException("申请单正在修改,请稍后再试");
        }

        try {
            final UpdateTestApplyDto updateTestApply =
                    JSON.parseObject(JSON.toJSONString(testApplyVo), UpdateTestApplyDto.class);

            return Map.of("applyInfo", applyService.update(updateTestApply));
        } finally {
            stringRedisTemplate.delete(lockKey);
        }
    }

    /**
     * 创建申请单 & 申请单样本
     */
    @PostMapping("/add")
    public Object add(@RequestBody InformationEntryTestApplyVo vo) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final String key = redisPrefix.getBasePrefix() + "addaApply:" + user.getOrgId() + ":" + vo.hashCode();
        if (BooleanUtils
                .isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, 1, TimeUnit.MINUTES))) {
            throw new IllegalArgumentException("操作频繁，请稍后再试");
        }

        try {
            // 检查申请单参数
            checkApplyParam(vo);

            final InformationEntryTestApplyDto addApply =
                    JSON.parseObject(JSON.toJSONString(vo), InformationEntryTestApplyDto.class);

            addApply.setPatientCardType(PatientCardTypeEnum.ID_CARD.name());
            addApply.setApplySource(ApplySourceEnum.MANUAL);
            addApply.setSupplier("样本信息录入");
            addApply.setSampleSource(SampleSourceEnum.INPUT.name());

            // 添加申请单
            ApplyInfo applyInfo = applyService.addApply(addApply);
            //这里将套餐信息存到redis中 applyId为主键
            cacheApplySampleItems(vo, applyInfo.getApplyId());
            return applyInfo;

        } finally {
            stringRedisTemplate.delete(key);
        }
    }

    private void cacheApplySampleItems(InformationEntryTestApplyVo vo, long applyId) {
        String packageKey = String.format(APPLAY_PACKAGE_CACHE_KEY, redisPrefix.getBasePrefix(), applyId);
        stringRedisTemplate.opsForValue().set(packageKey, JSON.toJSONString(vo.getItems()));
    }


    /**
     * 创建申请单 & 申请单样本
     */
    @PostMapping("/add-prefabricate")
    public Object addPrefabricate(@RequestBody PrefabricateTestApplyVo vo) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final String key = redisPrefix.getBasePrefix() + "addApply-prefabricate:" + user.getOrgId() + ":" + vo.hashCode();
        if (BooleanUtils
                .isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, 1, TimeUnit.MINUTES))) {
            throw new IllegalArgumentException("操作频繁，请稍后再试");
        }

        try {
            // 检查申请单参数
            checkPrefabricateApplyParam(vo);
            checkApplyParam(vo);

            final PrefabricateTestApplyDto addApply =
                    JSON.parseObject(JSON.toJSONString(vo), PrefabricateTestApplyDto.class);

            addApply.setPatientCardType(PatientCardTypeEnum.ID_CARD.name());
            addApply.setApplySource(ApplySourceEnum.MANUAL);
            addApply.setSupplier("预制条码信息录入");

            // 添加申请单
            final ApplyInfo applyInfo = applyService.addApply(addApply);
            cacheApplySampleItems(vo, applyInfo.getApplyId());
            return applyInfo;

        } finally {
            stringRedisTemplate.delete(key);

        }
    }


    /**
     * 删除样本检验项目
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/delete-sample-item")
    public Object deleteSampleItem(@RequestBody List<Long> applySampleItemIds,
                                   @RequestParam(value = "isNeedCheckStatus", required = false) Integer isNeedCheckStatus,
                                   @RequestParam(value = "isBatchDelete", required = false) Integer isBatchDelete,
                                   @RequestParam(value = "noDeleteApply", required = false) Integer noDeleteApply
    ) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        if (CollectionUtils.isEmpty(applySampleItemIds)) {
            throw new IllegalArgumentException("申请单项目ID不能为空");
        }

        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByIds(applySampleItemIds);
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalArgumentException("申请单项目不存在");
        }

        final Collection<Long> applySampleIds =
                applySampleItems.stream().map(ApplySampleItemDto::getApplySampleId).collect(Collectors.toSet());

        final Long applyId = applySampleItems.stream().map(ApplySampleItemDto::getApplyId).findFirst().orElse(null);
        if (Objects.isNull(applyId)) {
            throw new IllegalArgumentException("申请单不存在");
        }

        final ApplyDto apply = applyService.selectByApplyId(applyId);
        if (Objects.isNull(apply)) {
            throw new IllegalArgumentException("申请单不存在");
        }

        if (Objects.equals(apply.getStatus(), ApplyStatusEnum.CHECK.getCode())
                && !Objects.equals(isBatchDelete, YesOrNoEnum.YES.getCode())
                && Objects.equals(isNeedCheckStatus, YesOrNoEnum.YES.getCode())) {
            throw new IllegalArgumentException("申请单已复核，不能删除检验项目");
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException("申请单样本不存在");
        }

        for (final ApplySampleDto applySample : applySamples) {

            if (Objects.equals(applySample.getIsOnePick(), YesOrNoEnum.YES.getCode())) {
                throw new IllegalArgumentException(
                        String.format("申请单样本 [%s] 已一次分拣，不能删除检验项目", applySample.getBarcode()));
            }

            if (Objects.equals(applySample.getIsTwoPick(), YesOrNoEnum.YES.getCode())) {
                throw new IllegalArgumentException(
                        String.format("申请单样本 [%s] 已二次分拣，不能删除检验项目", applySample.getBarcode()));
            }
        }

        if (BooleanUtils.isNotTrue(applySampleItemService.deleteByApplySampleItemIds(applySampleItemIds))) {
            throw new IllegalStateException("删除检验项目失败");
        }

        final HashMap<String, Boolean> flagMap = new HashMap<>();

        //只有项目加减项 == false 不删
        if (!Objects.equals(noDeleteApply, YesOrNoEnum.YES.getCode())) {
            // 如果样本检验项目数量为0 删除样本
            deleteApplySample(applySamples, flagMap, applyId);
        }

        // 删除检验项目记录操作流水
        sampleFlowService.addSampleFlows(applySamples.stream().map(e -> SampleFlowDto.builder()
                .applyId(e.getApplyId())
                .applySampleId(e.getApplySampleId())
                .barcode(e.getBarcode())
                .operator(user.getNickname())
                .operatorId(user.getUserId())
                .operateCode(BarcodeFlowEnum.TEST_ITEM_DELETE.name())
                .operateName(BarcodeFlowEnum.TEST_ITEM_DELETE.getDesc())
                .content(String.format("删除项目，项目编码： [%s] ,项目名称： [%s] 项目",
                        applySampleItems.stream().filter(applySampleItemDto -> applySampleItemDto.getApplySampleId().equals(e.getApplySampleId()))
                                .map(ApplySampleItemDto::getTestItemCode).collect(Collectors.joining(",")),
                        applySampleItems.stream().filter(applySampleItemDto -> applySampleItemDto.getApplySampleId().equals(e.getApplySampleId()))
                                .map(ApplySampleItemDto::getTestItemName).collect(Collectors.joining(","))

                )).build()).collect(Collectors.toList()));


        log.info("删除申请单样本检验项目成功, 用户 [{}] 专业组 [{}] applySampleItemIds: {}", user.getNickname(), user.getGroupName(),
                applySampleItemIds);
        return flagMap;
    }

    private void deleteApplySample(List<ApplySampleDto> applySamples, HashMap<String, Boolean> flagMap, Long applyId) {
        for (final ApplySampleDto applySample : applySamples) {

            if (applySampleItemService.countByApplySampleId(applySample.getApplySampleId()) < 1) {

                applySampleService.deleteByApplySampleId(applySample.getApplySampleId());
                flagMap.put("applySample", true);
            }
        }
        //
        // 申请单样本数量为0 删除申请单
        if (applySampleItemService.countByApplyId(applyId) < 1) {
            applyService.deleteByApplyId(applyId);
            String packageKey = String.format(APPLAY_PACKAGE_CACHE_KEY, redisPrefix.getBasePrefix(), applyId);
            stringRedisTemplate.delete(packageKey);
            flagMap.put("apply", true);
        }
    }

    /**
     * 查询未复核的申请单
     */
    @GetMapping("/select-unreviewed-apply")
    public Object selectUnreviewedApply() {

        UnreviewedApplyQueryDto query = new UnreviewedApplyQueryDto();
        query.setOrgId(LoginUserHandler.get().getOrgId());
        query.setSource(ApplySourceEnum.MANUAL.name());
        query.setStatusList(List.of(ApplyStatusEnum.WAIT_CHECK.getCode(), ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode()));
        final Date date = new Date();
        query.setStartDate(DateUtils.addDays(date, -180));
        query.setEndDate(date);

        List<SampleApplyDto> sampleApplys = applyService.selectUnreviewedApplySampleByQuery(query);
        if (CollectionUtils.isEmpty(sampleApplys)) {
            return Collections.emptyList();
        }

        return sampleApplys.stream().map(m -> {
            final SampleApplyVo vo = JSON.parseObject(JSON.toJSONString(m), SampleApplyVo.class);
            vo.setSampleStatusDesc(SampleStatusEnum.getStatusByCode(m.getSampleStatusCode()).getDesc());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 检查申请单参数 通用
     */
    public static void checkApplyParam(@NonNull TestApplyVo vo) {
        boolean isNotBatchUpdate = true;
        if (vo instanceof UpdateTestApplyVo) {
            isNotBatchUpdate = ((UpdateTestApplyVo) vo).isBatchUpdateItem();
        }
        final Long hspOrgId = vo.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalArgumentException("请选择送检机构");
        }

        // 判断就诊类型是否为空
        final String visitType = vo.getApplyTypeCode();
        if (!isNotBatchUpdate && StringUtils.isBlank(visitType)) {
            throw new IllegalArgumentException("请选择就诊类型");
        }

        // 判断姓名是否为空
        final String patientName = vo.getPatientName();
        if (StringUtils.isBlank(patientName)) {
            throw new IllegalArgumentException("请输入姓名");
        }

        // 判断病人姓名长度是否大于 50 字符
        if (StringUtils.length(vo.getPatientName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("病人姓名长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        // 判断性别是否为空
        final Integer patientSex = vo.getPatientSex();
        if (Objects.isNull(patientSex)) {
            vo.setPatientSex(SexEnum.DEFAULT.getCode());
        }

        // 判断年龄是否为空
        if (Objects.isNull(vo.getPatientAge())) {
            vo.setPatientAge(NumberUtils.INTEGER_ZERO);
        }
        Integer patientAge = vo.getPatientAge();

        int minAge = 0;
        if (patientAge < minAge) {
            throw new IllegalArgumentException(String.format("年龄不能小于 %s 岁", minAge));
        }

        int maxAge = 150;
        if (patientAge > maxAge) {
            throw new IllegalArgumentException(String.format("年龄不能超过 %s 岁", maxAge));
        }

        // 判断急诊状态是否为空
        final Integer urgentType = vo.getUrgent();
        if (!isNotBatchUpdate && Objects.isNull(urgentType)) {
            throw new IllegalArgumentException("急诊状态不能为空");
        }

        // 判断样本个数是否不为空 是整数且大于0
        if (Objects.isNull(vo.getSampleCount())) {
            vo.setSampleCount(NumberUtils.INTEGER_ONE);
        }

        if (vo.getSampleCount() <= 0) {
            throw new IllegalArgumentException("样本个数不能为空且必须大于 0");
        }

        int maxSampleCount = 10000;
        if (vo.getSampleCount() >= maxSampleCount) {
            throw new IllegalArgumentException(String.format("样本个数不能超过 %s 个", maxSampleCount));
        }

        // 判断样本性状是否为空
        final String sampleProperty = vo.getSampleProperty();
        final String samplePropertyCode = vo.getSamplePropertyCode();
        if (!isNotBatchUpdate && (StringUtils.isBlank(sampleProperty) || StringUtils.isBlank(samplePropertyCode))) {
            throw new IllegalArgumentException("样本性状不能为空");
        }

        // 判断申请时间是否为空
        final Date applyDate = vo.getApplyDate();
        if (Objects.isNull(applyDate)) {
            throw new IllegalArgumentException("申请时间不能为空");
        }

        // 判断采样时间是否为空
        final Date samplingDate = vo.getSamplingDate();
        if (Objects.isNull(samplingDate)) {
            throw new IllegalArgumentException("采样时间不能为空");
        }

        // 判断门诊/住院号长度是否大于 50 字符
        if (StringUtils.length(vo.getPatientVisitCard()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("门诊/住院号长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        // 判断病人身份证号长度是否大于 50 字符
        if (StringUtils.length(vo.getPatientCard()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("病人身份证号长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        // 判断病人电话长度是否大于 50 字符
        if (StringUtils.length(vo.getPatientMobile()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("病人电话长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        // 判断送检医生长度是否大于 50 字符
        if (StringUtils.length(vo.getSendDoctor()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("送检医生长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        // 判断所选的申请项目中自定义样本类型的长度是否大于50字符
        List<TestApplyVo.Item> items = vo.getItems();
        if (CollectionUtils.isNotEmpty(items)) {
            boolean isBeyond = items.stream()
                    .map(TestApplyVo.Item::getSampleTypeCode)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet())
                    .stream().anyMatch(i -> StringUtils.length(i) > INPUT_MAX_LENGTH);
            if (isBeyond) {
                throw new IllegalArgumentException(String.format("样本类型长度不能超过 %s 字符", INPUT_MAX_LENGTH));
            }
        }

        // 备注长度不能超过255字符
        if (StringUtils.length(vo.getRemark()) > TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("备注长度不能超过 %s 字符", TEXTAREA_MAX_LENGTH));
        }

        // 临床诊断长度不能超过255字符
        if (StringUtils.length(vo.getClinicalDiagnosis()) > TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("临床诊断长度不能超过 %s 字符", TEXTAREA_MAX_LENGTH));
        }

        // 判断科室长度是否大于 50 字符
        if (StringUtils.length(vo.getDept()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("科室长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        // 判断外部条码号长度是否大于 50 字符
        if (StringUtils.length(vo.getOutBarcode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("外部条码号长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

    }


    private void checkPrefabricateApplyParam(@NonNull PrefabricateTestApplyVo vo) {
        List<PrefabricateTestApplyVo.BarcodeInfo> barcodeInfos = vo.getBarcodeInfos();

        Assert.notEmpty(barcodeInfos, "预制条码信息不能为空");

        Set<String> barcodes = new HashSet<>();
        Set<String> tubeCodes = new HashSet<>();

        for (PrefabricateTestApplyVo.BarcodeInfo barcodeInfo : barcodeInfos) {
            String barcode = barcodeInfo.getBarcode();
            String tubeCode = barcodeInfo.getTubeCode();

            if (StringUtils.isBlank(barcode)) {
                throw new IllegalArgumentException("条码不能为空");
            } else {
                if (!barcodes.add(barcode)) {
                    throw new IllegalArgumentException(String.format("%s条码号重复", barcode));
                }
            }

            if (StringUtils.isBlank(tubeCode) || StringUtils.isBlank(barcodeInfo.getTubeName())) {
                throw new IllegalArgumentException("管型不能为空");
            } else {
                if (!tubeCodes.add(tubeCode)) {
                    throw new IllegalArgumentException(String.format("%s 管型重复", barcodeInfo.getTubeName()));
                }
            }
        }

        Set<String> itemTubeCodes = vo.getItems().stream().map(TestApplyVo.Item::getTubeCode).collect(Collectors.toSet());

        if (tubeCodes.size() != itemTubeCodes.size() || !tubeCodes.containsAll(itemTubeCodes)) {
            throw new IllegalArgumentException("预制条码管型 和 项目管型无法完全匹配");
        }

        Map<String, List<TestApplyVo.Item>> tubeCodeMap =
                vo.getItems().stream().collect(Collectors.groupingBy(TestApplyVo.Item::getTubeCode));

        for (Map.Entry<String, List<TestApplyVo.Item>> entry : tubeCodeMap.entrySet()) {
            Set<String> sampleTypeCodes = entry.getValue().stream().map(TestApplyVo.Item::getSampleTypeCode).collect(Collectors.toSet());
            if (sampleTypeCodes.size() > 1) {
                String join = CollUtil.join(sampleTypeCodes, ",");
                throw new IllegalArgumentException(String.format("%s 管型有不同的样本类型 %s", entry.getKey(), join));
            }
//            Map<Long, String> groupMap = entry.getValue().stream().collect(Collectors.toMap(TestApplyVo.Item::getGroupId, TestApplyVo.Item::getGroupName));
//            if(groupMap.size() > 1){
//                String join = CollUtil.join(groupMap.values(), ",");
//                throw new IllegalArgumentException(String.format("%s 管型有不同的专业组 %s", entry.getKey(), join));
//            }
        }

        List<ApplySampleDto> applySampleDtos = applySampleService.selectByBarcodes(barcodes);
        if (CollectionUtils.isNotEmpty(applySampleDtos)) {
            barcodes = applySampleDtos.stream().map(ApplySampleDto::getBarcode).collect(Collectors.toSet());
            throw new IllegalArgumentException(String.format("%s 条码号已存在，不能重复录入", barcodes));
        }
    }


    /**
     * 上传手录单申请单样本图片信息--同步更新申请单所有样本的图片
     */
    @PostMapping("/uploadImages")
    public Object uploadImages(@RequestBody @Valid UploadImagesVo vo) {

        final UploadImagesDto imagesDto = JSON.parseObject(JSON.toJSONString(vo), UploadImagesDto.class);

        return applyService.uploadImages(imagesDto);
    }


    /**
     * 删除手录单申请单样本图片信息--同步删除申请单下所有的样本图片
     */
    @PostMapping("/deleteImages")
    public Object deleteImages(@RequestBody @Valid DeleteImagesVo vo) {

        final DeleteImagesDto imagesDto = JSON.parseObject(JSON.toJSONString(vo), DeleteImagesDto.class);

        return applyService.deleteImages(imagesDto);
    }

    /**
     * 查询申请单样本图片信息
     */
    @GetMapping("/selectImages")
    public Object selectImages(@RequestParam Long applySampleId) {
        return applySampleImageService.queryByApplySampleIds(Collections.singletonList(applySampleId));
    }



}
