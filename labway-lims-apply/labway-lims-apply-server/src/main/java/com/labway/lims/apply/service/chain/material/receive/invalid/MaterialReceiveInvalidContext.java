package com.labway.lims.apply.service.chain.material.receive.invalid;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRecordDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

/**
 * 物料 登记 作废 信息上下文
 *
 * <AUTHOR>
 * @since 2023/5/9 10:00
 */
@Getter
@Setter
public class MaterialReceiveInvalidContext extends StopWatchContext {
    /**
     * 领用记录 id
     */
    private long receiveId;

    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    /**
     * 获取 物料 登记 作废 从上下文中
     */
    public static MaterialReceiveInvalidContext from(Context context) {
        return (MaterialReceiveInvalidContext)context;
    }

    // 对应 物料 领用记录
    public static final String MATERIAL_RECEIVE_RECORD = "MATERIAL_RECEIVE_RECORD_" + IdUtil.objectId();

    public MaterialReceiveRecordDto getMaterialReceiveRecordDto() {
        return (MaterialReceiveRecordDto)get(MATERIAL_RECEIVE_RECORD);
    }

    // 对应 物料 库存记录
    public static final String MATERIAL_INVENTORY = "MATERIAL_INVENTORY_" + IdUtil.objectId();

    public MaterialInventoryDto getMaterialInventoryDto() {
        return (MaterialInventoryDto)get(MATERIAL_INVENTORY);
    }

    // 对应 专业组 物料信息
    public static final String GROUP_MATERIAL = "GROUP_MATERIAL_" + IdUtil.objectId();

    public GroupMaterialDto getGroupMaterialDto() {
        return (GroupMaterialDto)get(GROUP_MATERIAL);
    }

    @Override
    protected String getWatcherName() {
        return "领用作废";
    }
}
