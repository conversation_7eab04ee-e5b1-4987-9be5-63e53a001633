package com.labway.lims.apply.service.chain.sample.archive.add;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.base.api.dto.RackDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 样本归档存储加锁
 *
 * <AUTHOR>
 * @since 2023/5/25 13:25
 */
@Slf4j
@Component
public class SampleArchiveAddLimitCommand implements Command, Filter {
    private static final String MARK = SampleArchiveAddLimitCommand.class.getName();

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @Override
    public boolean execute(Context context) throws Exception {
        SampleArchiveAddContext from = SampleArchiveAddContext.from(context);
        RackDto rack = from.getRack();

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(getMark(rack.getRackId()),
            StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalStateException("试管架正在归档中");
        }

        from.put(MARK, StringUtils.EMPTY);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception e) {
        SampleArchiveAddContext from = SampleArchiveAddContext.from(context);
        if (from.containsKey(MARK)) {
            stringRedisTemplate.delete(getMark(from.getRack().getRackId()));
        }
        return CONTINUE_PROCESSING;
    }

    private String getMark(long rackId){
        return redisPrefix.getBasePrefix() + MARK + rackId;
    }

}
