package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class SimpleSampleVo {

    /**
     * 申请样本id
     */
    private Long applySampleId;

    /**
     * 申请id
     */
    private Long applyId;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 条码
     */
    private String barcode;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 性别
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 子年龄单位
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 管型
     */
    private String tubeName;

    /**
     * 门诊 | 住院 号
     */
    private String patientVisitCard;

    /**
     * 签收时间 | 录入时间 | 创建时间
     */
    private Date createDate;

}
