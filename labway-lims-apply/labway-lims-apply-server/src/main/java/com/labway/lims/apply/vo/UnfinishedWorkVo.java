package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class UnfinishedWorkVo {

    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 条码
     */
    private String barcode;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 患者性别
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 子年龄单位
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 就诊类型
     */
    private String applyType;

    /**
     * 就诊类型
     */
    private String applyTypeName;

    /**
     * 操作专业组
     */
    private String groupName;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 当前环节
     */
    private String currentLink;

    /**
     * 送检时间
     */
    private Date createTime;
}
