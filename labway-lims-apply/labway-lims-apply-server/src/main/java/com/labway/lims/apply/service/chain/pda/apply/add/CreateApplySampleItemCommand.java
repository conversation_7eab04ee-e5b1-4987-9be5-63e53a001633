package com.labway.lims.apply.service.chain.pda.apply.add;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.PdaApplySampleItemDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.service.chain.apply.add.AddApplyContext;
import com.labway.lims.apply.service.chain.apply.add.GroupSampleCommand;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class CreateApplySampleItemCommand implements Command {

    @Resource
    private GroupSampleCommand groupSampleCommand;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {

        final AddApplyContext from = AddApplyContext.from(c);

        final TestApplyDto testApply = from.getTestApply();

        final List<TestApplyDto.Item> items = from.getTestApply().getItems();
        if (CollectionUtils.isEmpty(items)) {
            throw new IllegalStateException("检验项目为空");
        }
        final ApplyDto apply = from.getApply();

        final Map<Long, TestApplyDto.Item> applyItemMap = from.getTestApply().getItems().stream()
                .collect(Collectors.toMap(TestApplyDto.Item::getTestItemId, v -> v, (a, b) -> a));

        // 根据管型 + 样本类型 + 自定义码 分组
        final Map<String, List<ApplySampleItemDto>> groupTestItemMap =
                groupSampleCommand.groupTestItem(apply, from.getTestItems(), applyItemMap, false);
        // 如果分出来的项目为空，报错
        final List<PdaApplySampleItemDto> applySampleItems = groupTestItemMap.values().stream()
                .flatMap(Collection::stream)
                .map(e-> {
                    PdaApplySampleItemDto pdaApplySampleItemDto = JSON.parseObject(JSON.toJSONString(e), PdaApplySampleItemDto.class);
                    pdaApplySampleItemDto.setApplyId(apply.getApplyId());
                    pdaApplySampleItemDto.setApplySampleItemId(snowflakeService.genId());
                    pdaApplySampleItemDto.setBloodcultureItem(StringUtils.EMPTY);
                    return pdaApplySampleItemDto;
                })
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("样本检验项目为空");
        }

        // 申请单样本项目
        from.put(AddApplyContext.APPLY_SAMPLE_ITEMS, applySampleItems);

        return CONTINUE_PROCESSING;
    }

}
