package com.labway.lims.apply.service.chain.apply.update.batch;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleService;

import lombok.extern.slf4j.Slf4j;

/**
 * 修改具体检验样本信息
 * 
 * <AUTHOR>
 * @since 2024/2/21 15:37
 */
@Slf4j
@Component
public class BatchUpdateSampleCommand implements Command {

    @DubboReference
    private GeneticsSampleService geneticsSampleService;

    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;

    @DubboReference
    private MicrobiologySampleService microbiologySampleService;

    @DubboReference
    private InfectionSampleService infectionSampleService;

    @DubboReference
    private SpecialtySampleService specialtySampleService;

    @DubboReference
    private SampleService sampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        BatchUpdateApplyContext from = BatchUpdateApplyContext.from(c);

        LoginUserHandler.User user = from.getUser();

        List<ApplySampleDto> applySamples = from.getApplySamples();

        HspOrganizationDto hspOrganization = from.getHspOrganization();
        Long hspOrgId = hspOrganization.getHspOrgId();
        String hspOrgName = hspOrganization.getHspOrgName();
        Long userId = user.getUserId();
        String nickname = user.getNickname();
        Date date = new Date();

        Set<Long> genetics =
            applySamples.stream().filter(obj -> Objects.equals(obj.getItemType(), ItemTypeEnum.GENETICS.name()))
                .map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(genetics)) {
            GeneticsSampleDto geneticsSampleDto = new GeneticsSampleDto();
            geneticsSampleDto.setHspOrgName(hspOrgName);
            geneticsSampleDto.setHspOrgId(hspOrgId);
            geneticsSampleDto.setUpdaterId(userId);
            geneticsSampleDto.setUpdaterName(nickname);
            geneticsSampleDto.setUpdateDate(date);
            geneticsSampleService.updateByApplyIds(geneticsSampleDto, genetics);
        }
        Set<Long> routine =
            applySamples.stream().filter(obj -> Objects.equals(obj.getItemType(), ItemTypeEnum.ROUTINE.name()))
                .map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(routine)) {
            SampleDto sampleDto = new SampleDto();
            sampleDto.setHspOrgName(hspOrgName);
            sampleDto.setHspOrgId(hspOrgId);
            sampleDto.setUpdaterId(userId);
            sampleDto.setUpdaterName(nickname);
            sampleDto.setUpdateDate(date);
            sampleService.updateByApplyIds(sampleDto, routine);
        }

        Set<Long> infection =
            applySamples.stream().filter(obj -> Objects.equals(obj.getItemType(), ItemTypeEnum.INFECTION.name()))
                .map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(infection)) {
            InfectionSampleDto infectionSampleDto = new InfectionSampleDto();
            infectionSampleDto.setHspOrgName(hspOrgName);
            infectionSampleDto.setHspOrgId(hspOrgId);
            infectionSampleDto.setUpdaterId(userId);
            infectionSampleDto.setUpdaterName(nickname);
            infectionSampleDto.setUpdateDate(date);
            infectionSampleService.updateByApplyIds(infectionSampleDto, infection);
        }

        Set<Long> microbiology =
            applySamples.stream().filter(obj -> Objects.equals(obj.getItemType(), ItemTypeEnum.MICROBIOLOGY.name()))
                .map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(microbiology)) {
            MicrobiologySampleDto microbiologySampleDto = new MicrobiologySampleDto();
            microbiologySampleDto.setHspOrgName(hspOrgName);
            microbiologySampleDto.setHspOrgId(hspOrgId);
            microbiologySampleDto.setUpdaterId(userId);
            microbiologySampleDto.setUpdaterName(nickname);
            microbiologySampleDto.setUpdateDate(date);
            microbiologySampleService.updateByApplyIds(microbiologySampleDto, microbiology);
        }

        Set<Long> specialty =
            applySamples.stream().filter(obj -> Objects.equals(obj.getItemType(), ItemTypeEnum.SPECIALTY.name()))
                .map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(specialty)) {
            SpecialtySampleDto specialtySampleDto = new SpecialtySampleDto();
            specialtySampleDto.setHspOrgName(hspOrgName);
            specialtySampleDto.setHspOrgId(hspOrgId);
            specialtySampleDto.setUpdaterId(userId);
            specialtySampleDto.setUpdaterName(nickname);
            specialtySampleDto.setUpdateDate(date);
            specialtySampleService.updateByApplyIds(specialtySampleDto, specialty);
        }

        Set<Long> outsourcing =
            applySamples.stream().filter(obj -> Objects.equals(obj.getItemType(), ItemTypeEnum.OUTSOURCING.name()))
                .map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(outsourcing)) {
            OutsourcingSampleDto outsourcingSampleDto = new OutsourcingSampleDto();
            outsourcingSampleDto.setHspOrgName(hspOrgName);
            outsourcingSampleDto.setHspOrgId(hspOrgId);
            outsourcingSampleDto.setUpdaterId(userId);
            outsourcingSampleDto.setUpdaterName(nickname);
            outsourcingSampleDto.setUpdateDate(date);
            outsourcingSampleService.updateByApplyIds(outsourcingSampleDto, outsourcing);
        }


        return CONTINUE_PROCESSING;
    }
}
