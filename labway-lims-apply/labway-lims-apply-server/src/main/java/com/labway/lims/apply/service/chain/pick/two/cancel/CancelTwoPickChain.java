package com.labway.lims.apply.service.chain.pick.two.cancel;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 取消二次分拣
 */
@Component
public class CancelTwoPick<PERSON>hain extends ChainBase implements InitializingBean {

    @Resource
    private CancelTwoPickLimitCommand cancelTwoPickLimitCommand;
    @Resource
    private CancelTwoPickCopyApplySampleItemCommand cancelTwoPickCopyApplySampleItemCommand;
    @Resource
    private CancelTwoPickCheckParamCommand cancelTwoPickCheckParamCommand;
    @Resource
    private CancelTwoPickCommand cancelTwoPickCommand;
    @Resource
    private CancelTwoPickRemoveApplySampleCommand cancelTwoPickRemoveApplySampleCommand;
    @Resource
    private CancelTwoPickUpdateApplySampleCommand cancelTwoPickUpdateApplySampleCommand;
    @Resource
    private CancelTwoPickFlowCommand cancelTwoPickFlowCommand;
    @Resource
    private CancelTwoPickDetachedRackCommand cancelTwoPickDetachedRackCommand;
    @Resource
    private CancelTwoPickBloodCultureCommand cancelTwoPickBloodCultureCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 限流
        addCommand(cancelTwoPickLimitCommand);

        // 参数校验
        addCommand(cancelTwoPickCheckParamCommand);

        // 取消二次分拣
        addCommand(cancelTwoPickCommand);

        // 复制项目信息
        addCommand(cancelTwoPickCopyApplySampleItemCommand);

        // 删除多余的申请单样本
        addCommand(cancelTwoPickRemoveApplySampleCommand);

        // 更新申请单样本
        addCommand(cancelTwoPickUpdateApplySampleCommand);

        // 更新血培养申请单项目名称
        addCommand(cancelTwoPickBloodCultureCommand);

        // 游离逻辑试管架
        addCommand(cancelTwoPickDetachedRackCommand);

        // 条码环节
        addCommand(cancelTwoPickFlowCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);

    }
}
