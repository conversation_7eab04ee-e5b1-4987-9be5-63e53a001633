package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentGroupTestItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentGroupTestItemService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.ReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 获取要分拣到哪个专业小组
 */
@Slf4j
@Component
class MultiTwoPickInstrumentGroupCommand implements Command {
    @DubboReference
    private InstrumentGroupTestItemService instrumentGroupTestItemService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private ReportItemService reportItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final MultiTwoPickContext context = MultiTwoPickContext.from(c);

        final List<ApplySampleDto> applySamples = context.getApplySamples();

        for (ApplySampleDto e : applySamples) {
            if (!Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId())) {
                throw new IllegalArgumentException(String.format("条码 [%s] 不可分拣到当前专业组", e.getBarcode()));
            }
        }

        // 查询到此专业组下面的所有项目
        final Map<Long, List<InstrumentGroupTestItemDto>> instrumentGroupTestItems = instrumentGroupTestItemService.selectByGroupIds(applySamples.stream()
                        .map(ApplySampleDto::getGroupId).collect(Collectors.toSet())).stream()
                .collect(Collectors.groupingBy(InstrumentGroupTestItemDto::getInstrumentGroupId));
        if (MapUtils.isEmpty(instrumentGroupTestItems)) {
            throw new IllegalArgumentException("没有可分拣的专业小组");
        }

        final List<InstrumentGroupDto> instrumentGroups = instrumentGroupService.selectByInstrumentGroupIds(instrumentGroupTestItems.keySet());
        if (CollectionUtils.isEmpty(instrumentGroups)) {
            throw new IllegalArgumentException("没有可分拣的专业小组");
        }

        //判断仪器报告项目
        if (Objects.nonNull(context.getInstrumentId())) {
            List<String> reportItemCodes = instrumentReportItemService.selectByInstrumentId(context.getInstrumentId())
                    .stream().map(InstrumentReportItemDto::getReportItemCode).collect(Collectors.toList());
            List<ApplySampleItemDto> sampleItemDtoList = context.getApplySampleItems().values().stream().flatMap(List::stream).collect(Collectors.toList());
            List<Long> testItemIds = sampleItemDtoList.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toList());
            List<ReportItemDto> reportItemDtos = reportItemService.selectByTestItemIds(testItemIds);
            for (ReportItemDto reportItemDto : reportItemDtos) {
                if(!reportItemCodes.contains(reportItemDto.getReportItemCode())){
                    throw new IllegalStateException(String.format("报告项目 %s[%s] 在此仪器下没有维护", reportItemDto.getReportItemName(), reportItemDto.getReportItemCode()));
                }
            }
        }

        // 每个要分拣到的专业小组
        context.put(MultiTwoPickContext.INSTRUMENT_GROUPS, new LinkedHashMap<>());

        for (ApplySampleDto e : applySamples) {

            // 获取到匹配的，根据检验项目匹配，一致的那就是可以在这个专业小组下做
            final var filteredInstrumentGroups = instrumentGroupTestItems.entrySet().stream().filter(k -> {
                for (ApplySampleItemDto item : context.getApplySampleItems().get(e.getApplySampleId())
                        .stream().filter(l -> Objects.equals(l.getGroupId(), LoginUserHandler.get().getGroupId()))
                        .collect(Collectors.toList())) {
                    // 如果有一个不匹配那么这个专业小组就不可以做
                    if (k.getValue().stream().noneMatch(l -> Objects.equals(item.getTestItemCode(), l.getTestItemCode()))) {
                        return false;
                    }

                    //如果外部指定了仪器，那么一定要仪器都匹配才行
                    if (Objects.nonNull(context.getInstrumentId())
                            && !k.getValue().stream().map(InstrumentGroupTestItemDto::getInstrumentId).collect(Collectors.toList()).contains(context.getInstrumentId())) {
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(filteredInstrumentGroups)) {
                throw new IllegalStateException("没有可分拣到的专业小组");
            }

            // 取得要分拣到的专业小组列表
            final List<InstrumentGroupDto> igs = instrumentGroups.stream().filter(k -> filteredInstrumentGroups.stream()
                            .anyMatch(l -> Objects.equals(k.getInstrumentGroupId(), l.getKey())))
                    .sorted(Comparator.comparing(o -> ObjectUtils.defaultIfNull(o.getSort(), Integer.MAX_VALUE)))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(igs)) {
                throw new IllegalStateException("没有可分拣到的专业小组");
            }

            // 放入上下文
            context.getInstrumentGroups().put(e.getApplySampleId(), igs.iterator().next());
        }


        return CONTINUE_PROCESSING;
    }
}
