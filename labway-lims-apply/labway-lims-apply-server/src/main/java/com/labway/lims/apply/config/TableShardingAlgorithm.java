package com.labway.lims.apply.config;

import cn.hutool.core.date.DateUtil;
import com.labway.lims.api.Snowflake;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.util.Collection;
import java.util.Date;

public class TableShardingAlgorithm implements PreciseShardingAlgorithm<Long> {


    private final Snowflake snowflake = new Snowflake(5, 5);


    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Long> shardingValue) {
        final Date date = new Date(snowflake.getGenerateDateTime(shardingValue.getValue()));
        final String suffix = String.format("%s_%s", DateUtil.year(date),
                StringUtils.leftPad(String.valueOf(DateUtil.month(date) + 1), 2, '0'));
        for (String availableTargetName : availableTargetNames) {
            if (availableTargetName.endsWith(suffix)) {
                return availableTargetName;
            }
        }

        throw new IllegalStateException(String.format("无法找到分表 [%s]", suffix));
    }
}
