package com.labway.lims.apply.vo;

import com.labway.lims.apply.api.dto.MaterialDeliveryIncomeItemDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 物料入库 根据出库单号 入库
 *
 * <AUTHOR>
 * @since 2023/5/9 9:40
 */
@Getter
@Setter
public class MaterialDeliveryIncomeRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 出库单号
     */
    private String deliveryNo;

    /**
     * 入库信息
     */
    private List<MaterialDeliveryIncomeItemDto> incomeItemList;

}
