package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.RackLogicFlowDto;
import com.labway.lims.apply.api.service.RackLogicFlowService;
import com.labway.lims.apply.mapper.TbRackLogicFlowMapper;
import com.labway.lims.apply.model.TbRackLogicFlow;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;


@Slf4j
@DubboService
public class RackLogicFlowServiceImpl implements RackLogicFlowService {
    @Resource
    private TbRackLogicFlowMapper tbRackLogicFlowMapper;

    @Override
    public void add(RackLogicFlowDto rackLogicFlow) {

        final TbRackLogicFlow flow = JSON.parseObject(JSON.toJSONString(rackLogicFlow), TbRackLogicFlow.class);

        tbRackLogicFlowMapper.insert(flow);

        log.info("新增试管架交接记录成功 用户 [{}] 专业组 [{}] {}", LoginUserHandler.get().getNickname(), LoginUserHandler.get().getGroupName(), JSON.toJSON(flow));
    }
}
