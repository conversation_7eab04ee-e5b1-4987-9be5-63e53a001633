package com.labway.lims.apply.model.es;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 微生物费用项目
 * 
 * <AUTHOR>
 * @since 2023/8/30 18:55
 */
@Getter
@Setter
public final class MicrobiologyFeeItem {

    /**
     * 微生物费用明细项目id
     */
    @Field(type = FieldType.Long)
    private Long microbiologySampleFeeItemId;

    /**
     * 微生物样本id
     */
    @Field(type = FieldType.Long)
    private Long microbiologySampleId;

    /**
     * 检验项目id
     */
    @Field(type = FieldType.Long)
    private Long testItemId;

    /**
     * 检验项目code
     */
    @Field(type = FieldType.Keyword)
    private String testItemCode;

    /**
     * 检验项目名称
     */
    @Field(type = FieldType.Keyword)
    private String testItemName;
    /**
     * 收费价格
     */
    @Field(type = FieldType.Scaled_Float, scalingFactor = 100)
    private BigDecimal feePrice;
    /**
     * 收费次数
     */
    @Field(type = FieldType.Integer)
    private Integer feeCount;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date)
    private Date createDate;

    /**
     * 更新时间
     */
    @Field(type = FieldType.Date)
    private Date updateDate;

    /**
     * 创建人
     */
    @Field(type = FieldType.Long)
    private Long creatorId;

    /**
     * 创建人
     */
    @Field(type = FieldType.Keyword)
    private String creatorName;

    /**
     * 更新人ID
     */
    @Field(type = FieldType.Long)
    private Long updaterId;

    /**
     * 更新人名称
     */
    @Field(type = FieldType.Keyword)
    private String updaterName;
}
