package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class BloodRackSampleVo {

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 逻辑试管架ID
     */
    private Long rackLogicId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 管型
     */
    private String tubeName;

    /**
     * 病人名称
     */
    private String patientName;


    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;


    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 检验项目
     */
    private List<String> testItemNames;

    /**
     * 专业组名称
     */
    private List<String> groupNames;

    /**
     * 基本量
     */
    private BigDecimal basicQuantity;
}
