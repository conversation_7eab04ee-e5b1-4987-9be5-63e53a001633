package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 样本报告
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_sample_report")
public class TbSampleReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long sampleReportId;

    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 泛指，微生物样本、特检等样本id
     */
    private Long sampleId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 默认PDF
     * @see SampleReportFileTypeEnum
     */
    private String fileType;

    /**
     * 地址
     */
    private String url;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 1: 已经删除 0:未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 1: 是文件上传的pdf，  0默认
     */
    private Integer isUploadPdf;

}
