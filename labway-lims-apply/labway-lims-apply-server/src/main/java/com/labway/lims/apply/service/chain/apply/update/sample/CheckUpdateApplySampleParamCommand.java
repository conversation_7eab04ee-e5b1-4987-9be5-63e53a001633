package com.labway.lims.apply.service.chain.apply.update.sample;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Slf4j
@Component
public class CheckUpdateApplySampleParamCommand implements Command {

    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Override
    public boolean execute(Context c) throws Exception {
        final UpdateApplyContext from = UpdateApplyContext.from(c);
        final TestApplyDto testApply = from.getTestApply();

        final LoginUserHandler.User user = LoginUserHandler.get();

        final ApplyDto apply = applyService.selectByApplyId(from.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }
        from.put(UpdateApplyContext.APPLY, apply);

        // 申请单录入修改 已经复核的申请单不能修改(在申请单录入页面)
        checkApplyStatus(testApply, apply);

        // 样本信息修改，禁用或者终止的样本不能修改 （仅针对样本信息修改页面）
        checkApplyAndSampleInfoAndFillSampleInfo(from, testApply, apply);

        final Long hspOrgId = testApply.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalArgumentException("请选择送检机构");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("选择的送检机构为空");
        }
        if (!Objects.equals(hspOrganization.getEnable(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalStateException(String.format("送检机构 [%s] 已停用", hspOrganization.getHspOrgName()));
        }

        from.put(UpdateApplyContext.HSP_ORG, hspOrganization);
        from.setUser(user);

        return CONTINUE_PROCESSING;
    }

    private void checkApplyAndSampleInfoAndFillSampleInfo(UpdateApplyContext from, TestApplyDto testApply, ApplyDto apply) {
        if (BooleanUtils.isTrue(ApplyStatusEnum.isUnCheck(apply.getStatus()))) {
            throw new IllegalStateException("当前条码未完成复核，请至对应页面进行信息修改");
        }

        Long applySampleId = ((UpdateTestApplySampleDto) testApply).getApplySampleId();
        if (Objects.isNull(applySampleId)) {
            throw new IllegalArgumentException("申请单样本id不能为空");
        }
        ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }
        // 是否修改了性别，年龄，送检机构，样本类型
        final boolean isUpdateSexOrAgeOrHspOrgOrSampleType = isUpdateSexOrAgeOrHspOrgOrSampleType(testApply, apply, applySample);
        {
            if (Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
                // throw new IllegalStateException(String.format("样本 [%s] 已终止检验", applySample.getBarcode()));
            }

            if (BooleanUtils.isTrue(isUpdateSexOrAgeOrHspOrgOrSampleType) &&
                    (Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())
                    || Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
                // 1.0.7 审核状态下可以修改性别，年龄等
                // throw new IllegalStateException(String.format("当前申请单下 条码 [%s] 已审核，不能修改性别/年龄/送检机构/样本类型", applySample.getBarcode()));
            }
        }
        from.put(UpdateApplyContext.UPDATE_APPLY_SAMPLE, applySample);
        from.setIsUpdateSexOrAgeOrHspOrgOrSampleType(isUpdateSexOrAgeOrHspOrgOrSampleType);
    }

    /**
     * 检查申请单状态 复核了的不能修改（指针对申请单信息录入）
     */
    private static void checkApplyStatus(TestApplyDto testApply, ApplyDto apply) {
        if ((testApply instanceof UpdateTestApplyDto) &&
                List.of(ApplyStatusEnum.CHECK.getCode(), ApplyStatusEnum.DOUBLE_CHECK.getCode()).contains(apply.getStatus())) {
            throw new IllegalStateException("申请单已复核，不能修改");
        }
    }

    /**
     * 是否修改了性别或者年龄或者送检机构或者样本类型
     */
    public static boolean isUpdateSexOrAgeOrHspOrgOrSampleType(TestApplyDto testApply, ApplyDto apply, ApplySampleDto applySample) {
        return BooleanUtils.isFalse(
                Objects.equals(testApply.getPatientSex(), apply.getPatientSex())
                && Objects.equals(testApply.getPatientAge(), apply.getPatientAge())
                && Objects.equals(testApply.getPatientSubage(), apply.getPatientSubage())
                && Objects.equals(testApply.getPatientSubageUnit(), apply.getPatientSubageUnit())
                && Objects.equals(testApply.getHspOrgId(), apply.getHspOrgId())
                && Objects.equals(((UpdateTestApplySampleDto) testApply).getSampleTypeCode(), applySample.getSampleTypeCode()));
    }
}
