package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.model.TbApplySample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 申请单样本 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbApplySampleMapper extends BaseMapper<TbApplySample> {

    void insertBatch(@Param("list") List<TbApplySample> samples);

    /**
     * 查询待分拣的样本
     */
    List<WaitingOnePickApplySampleDto> selectWaitingOnePickSamples(@Param("beginSignDate") Date beginSignDate,
                                                                   @Param("endSignDate") Date endSignDate, @Param("orgId") Long orgId);

    /**
     * 查询待分血的样本
     */
    List<WaitingSplitBloodApplySampleDto> selectWaitingSplitBloodSamples(@Param("beginReceiveDate") Date beginReceiveDate,
                                                                         @Param("endReceiveDate") Date endReceiveDate,
                                                                         @Param("groupId") Long groupId);

    /**
     * 已分血样本
     */
    List<SplitBloodApplySampleDto> selectSplitBloodSamples(@Param("beginSplitDate") Date beginSplitDate,
                                                           @Param("endSplitDate") Date endSplitDate,
                                                           @Param("orgId") Long orgId);
    /**
     * 已分血 后的样本
     */
    List<SplitBloodApplySampleDto> selectAfterSplitBloodSamples(@Param("beginSplitDate") Date beginSplitDate,
                                                           @Param("endSplitDate") Date endSplitDate,
                                                           @Param("orgId") Long orgId);

    /**
     * 获取待二次分拣的样本
     */
    List<WaitingTwoPickApplySampleDto> selectWaitingTwoPickSamples(@Param("beginReceiveDate") Date beginReceiveDate,
                                                                   @Param("endReceiveDate") Date endReceiveDate,
                                                                   @Param("orgId") Long orgId,
                                                                   @Param("groupId") Long groupId,
                                                                   @Param("itemType") String itemType,
                                                                   @Param("excludeItemTypes") List<String> excludeItemTypes);

    /**
     * 获取已经二次分拣的样本
     */
    List<TwoPickedApplySampleDto> selectTwoPickedSamples(@Param("beginTwoPickedDate") Date beginTwoPickedDate,
                                                         @Param("endTwoPickedDate") Date endTwoPickedDate,
                                                         @Param("orgId") Long orgId,
                                                         @Param("groupId") Long groupId,
                                                         @Param("itemType") String itemType,
                                                         @Param("excludeItemTypes") List<String> excludeItemTypes);

    /**
     * 根据逻辑试管架ID查询
     */
    List<RackLogicApplySampleDto> selectByRackLogicIds(@Param("rackLogicIds") Collection<Long> rackLogicIds);

    /**
     * 外送未分拣的样本
     */
    List<OutsourcingApplySampleDto> selectOutsourcingUnPickApplySamples(@Param("beginReceiveDate") Date beginReceiveDate,
                                                                        @Param("endReceiveDate") Date endReceiveDate,
                                                                        @Param("orgId") Long orgId,
                                                                        @Param("groupId") Long groupId);

    /**
     * 外送已分拣的样本
     */
    List<OutsourcingApplySampleDto> selectOutsourcingPickedApplySamples(@Param("beginPickDate") Date beginPickDate,
                                                                        @Param("endPickDate") Date endPickDate,
                                                                        @Param("orgId") Long orgId);

    /**
     * 外送清单打印列表
     */
    List<OutsourcingApplySampleDto> selectOutsourcingListSamples(@Param("beginPickDate") Date beginPickDate,
                                                                  @Param("endPickDate") Date endPickDate,
                                                                  @Param("orgId") Long orgId,
                                                                  @Param("groupId") Long groupId,
                                                                  @Param("showUnTwoPickSample") Integer showUnTwoPickSample);

    /**
     * 根据ID批量修改
     */
    int updateByApplySampleIds(@Param("applySample") ApplySampleDto applySample,
                               @Param("applySampleIds") Collection<Long> applySampleIds);

    /**
     * 查询已经一次分拣的
     */
    List<OnePickedApplySampleDto> selectOnePickedSamples(@Param("beginOnePickDate") Date beginOnePickDate,
                                                @Param("endOnePickDate") Date endOnePickDate,
                                                @Param("orgId") Long orgId);

    /**
     * 查询已经一次分拣的后的样本
     */
    List<OnePickedApplySampleDto> selectAfterOnePickedSamples(@Param("beginOnePickDate") Date beginOnePickDate,
                                                @Param("endOnePickDate") Date endOnePickDate,
                                                @Param("orgId") Long orgId);

    /**
     * 根据 送检机构id + 外部条码 查询样本
     */
    List<ApplySampleDto> selectByOutBarcodeAndHspOrgId(@Param("hspOrgId") long hspOrgId, @Param("outBarcode") String outBarcode);

    /**
     * 根据试管架ID查询
     */
    List<RackLogicApplySampleDto> selectByRackIds(@Param("rackIds") Collection<Long> rackIds);

    List<ApplySampleDto> selectByOutBarcodeAndHspOrgCode(@Param("hspOrgCode") String hspOrgCode, @Param("outBarcode") String outBarcode);

    /**
     * 查询没有被删除的 和 已被删除的但是合并主条码号存在
     * @param applySampleIds
     * @return
     */
    List<TbApplySample> selectAllByApplySampleIdsAndMasterBarCodeNotNull(@Param("applySampleIds") Collection<Long> applySampleIds);

    /**
     * 条码项目二次分拣明细
     */
    List<ApplySampleItemTwoPickDetailDto> selectApplySampleItemTwoPickDetailByBarcode(@Param("barcode") String barcode);


    /**
     * 查询今日已签收的样本
     */
    List<ApplySampleDto> selectTodaySignedApplySamples(@Param("beginSignDate") Date beginSignDate,
                                                       @Param("endSignDate") Date endSignDate, @Param("orgId") Long orgId);

}
