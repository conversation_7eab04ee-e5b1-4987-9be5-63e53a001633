package com.labway.lims.apply.service.chain.apply.update.sample;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import lombok.Getter;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  修改样本信息
 */
@Getter
@Component
public class UpdateSaveSampleCommand implements Command {
    @Resource
    private ApplySampleService applySampleService;
    @DubboReference
    private GeneticsSampleService geneticsSampleService;

    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;

    @DubboReference
    private MicrobiologySampleService microbiologySampleService;

    @DubboReference
    private InfectionSampleService infectionSampleService;

    @DubboReference
    private SpecialtySampleService specialtySampleService;

    @DubboReference
    private SampleService sampleService;

    @Resource
    private SampleAbnormalService sampleAbnormalService;

   @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;

    @Resource
    private SampleReportService sampleReportService;
    /**
     *  根据样本的 item_type去更新对应的样本信息
     * @param c
     * @return
     * @throws Exception
     */
    @Override
    public boolean execute(Context c) throws Exception {
        UpdateApplyContext context = UpdateApplyContext.from(c);
        ApplyDto apply = context.getApply();
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplyId(apply.getApplyId());
        // 获取项目类型
        List<String> itemTypes = applySampleDtos.stream().filter(a -> StringUtils.isNotBlank(a.getItemType())).map(ApplySampleDto::getItemType).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemTypes)){
            return  CONTINUE_PROCESSING;
        }

        LoginUserHandler.User user = LoginUserHandler.get();
        Long hspOrgId = apply.getHspOrgId() == null ? 0  : apply.getHspOrgId();
        String hspOrgName = StringUtils.isBlank(apply.getHspOrgName()) ? Strings.EMPTY : apply.getHspOrgName();
        // 更新时间、人id、人昵称
        Long userId = user.getUserId();
        String nickname = user.getNickname();
        //OUTSOURCING
        Date date = new Date();
        // 根据项目类型去修改对应的样本信息
        for (String itemType : itemTypes) {
            switch (ItemTypeEnum.getByName(itemType)) {
                case GENETICS:
                    GeneticsSampleDto geneticsSampleDto = new GeneticsSampleDto();
                    geneticsSampleDto.setApplyId(apply.getApplyId());
                    geneticsSampleDto.setHspOrgName(hspOrgName);
                    geneticsSampleDto.setHspOrgId(hspOrgId);
                    geneticsSampleDto.setUpdaterId(userId);
                    geneticsSampleDto.setUpdaterName(nickname);
                    geneticsSampleDto.setUpdateDate(date);
                    geneticsSampleService.updateByApplyId(geneticsSampleDto);
                    break;
                case ROUTINE:
                    SampleDto sampleDto = new SampleDto();
                    sampleDto.setApplyId(apply.getApplyId());
                    sampleDto.setHspOrgName(hspOrgName);
                    sampleDto.setHspOrgId(hspOrgId);
                    sampleDto.setUpdaterId(userId);
                    sampleDto.setUpdaterName(nickname);
                    sampleDto.setUpdateDate(date);
                    sampleService.updateByApplyId(sampleDto);
                    break;
                case INFECTION:
                    InfectionSampleDto infectionSampleDto = new InfectionSampleDto();
                    infectionSampleDto.setApplyId(apply.getApplyId());
                    infectionSampleDto.setHspOrgName(hspOrgName);
                    infectionSampleDto.setHspOrgId(hspOrgId);
                    infectionSampleDto.setUpdaterId(userId);
                    infectionSampleDto.setUpdaterName(nickname);
                    infectionSampleDto.setUpdateDate(date);
                    infectionSampleService.updateByApplyId(infectionSampleDto);
                    break;
                case MICROBIOLOGY:
                    MicrobiologySampleDto microbiologySampleDto = new MicrobiologySampleDto();
                    microbiologySampleDto.setApplyId(apply.getApplyId());
                    microbiologySampleDto.setHspOrgName(hspOrgName);
                    microbiologySampleDto.setHspOrgId(hspOrgId);
                    microbiologySampleDto.setUpdaterId(userId);
                    microbiologySampleDto.setUpdaterName(nickname);
                    microbiologySampleDto.setUpdateDate(date);
                    microbiologySampleService.updateByApplyId(microbiologySampleDto);
                    break;
                case SPECIALTY:
                    SpecialtySampleDto specialtySampleDto = new SpecialtySampleDto();
                    specialtySampleDto.setApplyId(apply.getApplyId());
                    specialtySampleDto.setHspOrgName(hspOrgName);
                    specialtySampleDto.setHspOrgId(hspOrgId);
                    specialtySampleDto.setUpdaterId(userId);
                    specialtySampleDto.setUpdaterName(nickname);
                    specialtySampleDto.setUpdateDate(date);
                    specialtySampleService.updateByApplyId(specialtySampleDto);
                    break;
                case OUTSOURCING:
                    OutsourcingSampleDto outsourcingSampleDto = new OutsourcingSampleDto();
                    outsourcingSampleDto.setApplyId(apply.getApplyId());
                    outsourcingSampleDto.setHspOrgName(hspOrgName);
                    outsourcingSampleDto.setHspOrgId(hspOrgId);
                    outsourcingSampleDto.setUpdaterId(userId);
                    outsourcingSampleDto.setUpdaterName(nickname);
                    outsourcingSampleDto.setUpdateDate(date);
                    outsourcingSampleService.updateByApplyId(outsourcingSampleDto);
                    break;
            }
        }
        return  CONTINUE_PROCESSING;
    }
}
