package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Objects;

/**
 * 外送清单
 */
@Getter
@Setter
public class OutsourcingTwoPickSamplesRequestVo {
    /**
     * 开始分拣时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginPickDate;

    /**
     * 结束分拣时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endPickDate;

    public void check() {
        Objects.requireNonNull(beginPickDate, "开始时间不能为空");
        Objects.requireNonNull(endPickDate, "结束时间不能为空");
    }
}
