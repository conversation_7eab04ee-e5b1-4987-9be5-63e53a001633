package com.labway.lims.apply.service.chain.pick.two.immunity;

import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.service.chain.pick.two.TwoPickContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

/**
 * 删除逻辑试管架占用
 */
@Slf4j
@Component
public class ImmunityTwoPickRemoveRackSpaceCommand implements Command {

    @Resource
    private RackLogicSpaceService rackLogicSpaceService;

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);

        final Set<Long> ids = new HashSet<>();

        for (ApplySampleTwoPickDto e : context.getApplySampleTwoPicks()) {
            ids.add(e.getApplySampleId());
        }

        rackLogicSpaceService.deleteByApplySampleIds(ids);

        return CONTINUE_PROCESSING;
    }


}
