package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.RackHoleColumnOrderEnum;
import com.labway.lims.api.enums.base.RackHolePriorityOrderEnum;
import com.labway.lims.api.enums.base.RackHoleRowOrderEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.mapper.TbRackLogicSpaceMapper;
import com.labway.lims.apply.model.TbRackLogicSpace;
import com.labway.lims.base.api.dto.RackHoleRuleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class RackLogicSpaceServiceImpl implements RackLogicSpaceService {
    @Resource
    private TbRackLogicSpaceMapper rackLogicSpaceMapper;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public List<RackLogicSpaceDto> selectByRackLogicId(long rackLogicId) {
        return rackLogicSpaceMapper
                .selectList(new LambdaQueryWrapper<TbRackLogicSpace>().eq(TbRackLogicSpace::getRackLogicId, rackLogicId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicSpaceDto> selectByRackLogicIds(Collection<Long> rackLogicIds) {
        if (CollectionUtils.isEmpty(rackLogicIds)) {
            return Collections.emptyList();
        }
        return rackLogicSpaceMapper
                .selectList(new LambdaQueryWrapper<TbRackLogicSpace>().in(TbRackLogicSpace::getRackLogicId, rackLogicIds))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicSpaceDto> selectByRackIds(Collection<Long> rackIds) {
        if (CollectionUtils.isEmpty(rackIds)) {
            return Collections.emptyList();
        }
        return rackLogicSpaceMapper.selectList(new LambdaQueryWrapper<TbRackLogicSpace>()
                        .eq(TbRackLogicSpace::getIsDelete, YesOrNoEnum.NO.getCode())
                        .in(TbRackLogicSpace::getRackId, rackIds))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<RackLogicSpaceDto>> selectByRackIdsAsMap(Collection<Long> rackIds) {
        return selectByRackIds(rackIds).stream()
                .collect(Collectors.groupingBy(RackLogicSpaceDto::getRackId));
    }

    @Override
    public long countByRackLogicIds(Collection<Long> rackLogicIds) {
        if (CollectionUtils.isEmpty(rackLogicIds)) {
            return 0;
        }
        return rackLogicSpaceMapper
                .selectCount(new LambdaQueryWrapper<TbRackLogicSpace>().in(TbRackLogicSpace::getRackLogicId, rackLogicIds));
    }

    @Override
    public long addRackLogicSpace(RackLogicSpaceDto dto) {
        final TbRackLogicSpace space = new TbRackLogicSpace();

        BeanUtils.copyProperties(dto, space);

        space.setRackLogicSpaceId(ObjectUtils.defaultIfNull(dto.getRackLogicSpaceId(), snowflakeService.genId()));
        space.setCreateDate(new Date());
        space.setUpdateDate(new Date());
        space.setCreatorId(LoginUserHandler.get().getUserId());
        space.setCreatorName(LoginUserHandler.get().getNickname());
        space.setUpdaterId(LoginUserHandler.get().getUserId());
        space.setUpdaterName(LoginUserHandler.get().getNickname());
        space.setIsDelete(YesOrNoEnum.NO.getCode());

        if (rackLogicSpaceMapper.insert(space) < 1) {
            throw new IllegalStateException("添加逻辑试管架占用失败");
        }

        log.info("用户 [{}] 添加逻辑试管架占用 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(space));

        return space.getRackLogicSpaceId();
    }

    @Override
    public List<RackLogicSpaceDto> selectByApplySampleId(long applySampleId) {
        return rackLogicSpaceMapper
                .selectList(
                        new LambdaQueryWrapper<TbRackLogicSpace>().eq(TbRackLogicSpace::getApplySampleId, applySampleId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicSpaceDto> selectByAllApplySampleId(long applySampleId) {
        return rackLogicSpaceMapper.selectByAllApplySampleId(applySampleId);
    }

    @Override
    public List<RackLogicSpaceDto> selectByApplySampleIds(Collection<Long> applySampleIds) {

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        return rackLogicSpaceMapper
                .selectList(
                        new LambdaQueryWrapper<TbRackLogicSpace>().in(TbRackLogicSpace::getApplySampleId, applySampleIds))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public boolean deleteByApplySampleId(long applySampleId) {
        return rackLogicSpaceMapper.delete(
                new LambdaQueryWrapper<TbRackLogicSpace>().eq(TbRackLogicSpace::getApplySampleId, applySampleId)) > 0;
    }

    @Override
    public void deleteByRackLogicSpaceIds(Collection<Long> rackLogicSpaceIds) {
        if (CollectionUtils.isEmpty(rackLogicSpaceIds)) {
            return;
        }
        rackLogicSpaceMapper.delete(
                new LambdaQueryWrapper<TbRackLogicSpace>().in(TbRackLogicSpace::getRackLogicSpaceId, rackLogicSpaceIds));
    }

    @Override
    public void deleteByRackLogicSpaceId(long rackLogicSpaceId) {
        rackLogicSpaceMapper.deleteById(rackLogicSpaceId);
    }

    @Override
    public void deleteByRackLogicId(long rackLogicId) {

        rackLogicSpaceMapper.delete(
                new LambdaQueryWrapper<TbRackLogicSpace>()
                        .eq(TbRackLogicSpace::getRackLogicId, rackLogicId));
    }

    @Nullable
    @Override
    public RackLogicEffectiveSpaceDto selectNextSpaceByRackLogicAndHoleRule(RackLogicDto rackLogic, int startRow,
                                                                            int startColumn, RackHoleRuleDto holeRule) {
        RackHolePriorityOrderEnum priority = holeRule.getPriority();
        RackHoleRowOrderEnum rowOrder = holeRule.getRowOrder();
        RackHoleColumnOrderEnum columnOrder = holeRule.getColumnOrder();
        if (Objects.isNull(priority) || Objects.isNull(rowOrder) || Objects.isNull(columnOrder)) {
            log.error("规则信息不全");
            return null;
        }

        // 二维坐标系
        final int[][] xy = new int[rackLogic.getRow()][rackLogic.getColumn()];
        int x = -1;
        int y = -1;

        // 填充坐标系
        for (RackLogicSpaceDto e : this.selectByRackLogicId(rackLogic.getRackLogicId())) {
            xy[e.getRow()][e.getColumn()] = 1;
        }

        int numRows = xy.length;
        int numCols = xy[0].length;
        int rowStep = (rowOrder == RackHoleRowOrderEnum.UP_TO_DOWN) ? 1 : -1;
        int colStep = (columnOrder == RackHoleColumnOrderEnum.LEFT_TO_RIGHT) ? 1 : -1;
        int rowStart = (rowOrder == RackHoleRowOrderEnum.UP_TO_DOWN) ? startRow : numRows - 1 - startRow;
        int colStart = (columnOrder == RackHoleColumnOrderEnum.LEFT_TO_RIGHT) ? startColumn : numCols - 1 - startColumn;

        if (priority == RackHolePriorityOrderEnum.ROW_PRIORITY) {
            for (int i = rowStart; i >= 0 && i < numRows; i += rowStep) {
                for (int j = colStart; j >= 0 && j < numCols; j += colStep) {
                    if (xy[i][j] == 0) {
                        x = i;
                        y = j;
                        break;
                    }

                }
                if (x != -1) {
                    break;
                }
                colStart = (columnOrder == RackHoleColumnOrderEnum.LEFT_TO_RIGHT) ? 0 : numCols - 1;
            }
        } else {
            for (int j = colStart; j >= 0 && j < numCols; j += colStep) {
                for (int i = rowStart; i >= 0 && i < numRows; i += rowStep) {
                    if (xy[i][j] == 0) {
                        x = i;
                        y = j;
                        break;
                    }
                }
                if (x != -1) {
                    break;
                }
                rowStart = (rowOrder == RackHoleRowOrderEnum.UP_TO_DOWN) ? 0 : numRows - 1;
            }
        }
        if (x == -1) {
            return null;
        }
        RackLogicEffectiveSpaceDto target = new RackLogicEffectiveSpaceDto();
        target.setRow(x);
        target.setColumn(y);
        return target;
    }

    @Override
    public List<RackLogicSpaceDto> selectByRackLogicSpaceIds(Collection<Long> rackLogicSpaceIds) {
        if (CollectionUtils.isEmpty(rackLogicSpaceIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRackLogicSpace> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbRackLogicSpace::getRackLogicSpaceId, rackLogicSpaceIds);
        queryWrapper.eq(TbRackLogicSpace::getIsDelete, YesOrNoEnum.NO.getCode());

        return convert(rackLogicSpaceMapper.selectList(queryWrapper));
    }

    @Override
    public Map<Long, Integer> selectOccupiedNumByRackLogicIds(Collection<Long> rackLogicIds) {
        if (CollectionUtils.isEmpty(rackLogicIds)) {
            return Collections.emptyMap();
        }
        List<SampleOccupiedNumByRackLogicId> sampleOccupiedNumByRackLogicIds =
                rackLogicSpaceMapper.selectOccupiedNumByRackLogicIds(rackLogicIds);
        return sampleOccupiedNumByRackLogicIds.stream().collect(Collectors
                .toMap(SampleOccupiedNumByRackLogicId::getRackLogicId, SampleOccupiedNumByRackLogicId::getOccupiedNum));

    }

    @Nullable
    @Override
    public RackLogicSpaceDto selectByRackLogicSpaceId(long rackLogicSpaceId) {
        if (rackLogicSpaceId < 1) {
            return null;
        }
        LambdaQueryWrapper<TbRackLogicSpace> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbRackLogicSpace::getRackLogicSpaceId, rackLogicSpaceId);
        queryWrapper.eq(TbRackLogicSpace::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return convert(rackLogicSpaceMapper.selectOne(queryWrapper));
    }

    @Override
    public List<SampleRackPositionDto> selectSamplePositionByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }
        return rackLogicSpaceMapper.selectSamplePositionByApplySampleIds(applySampleIds);
    }

    @Override
    public void deleteByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return;
        }
        rackLogicSpaceMapper.delete(new LambdaQueryWrapper<TbRackLogicSpace>()
                .in(TbRackLogicSpace::getApplySampleId, applySampleIds));
    }

    @Override
    public void deleteByApplySampleIdsAndNotRackLogicId(Collection<Long> applySampleIds, long rackLogicId) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return;
        }
        rackLogicSpaceMapper.delete(new LambdaQueryWrapper<TbRackLogicSpace>()
                .in(TbRackLogicSpace::getApplySampleId, applySampleIds)
                .ne(TbRackLogicSpace::getRackLogicId, rackLogicId));
    }

    @Override
    public void deleteByRackIds(Collection<Long> rackIds) {
        if (CollectionUtils.isEmpty(rackIds)) {
            return;
        }

        rackLogicSpaceMapper.delete(new LambdaQueryWrapper<TbRackLogicSpace>()
                .in(TbRackLogicSpace::getRackId, rackIds));
    }

    private RackLogicSpaceDto convert(TbRackLogicSpace rackLogicSpace) {
        if (Objects.isNull(rackLogicSpace)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(rackLogicSpace), RackLogicSpaceDto.class);
    }

    private List<RackLogicSpaceDto> convert(List<TbRackLogicSpace> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert)
                .collect(Collectors.toList());
    }
}
