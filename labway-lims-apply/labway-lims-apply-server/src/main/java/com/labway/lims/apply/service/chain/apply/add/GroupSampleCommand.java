package com.labway.lims.apply.service.chain.apply.add;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.labway.lims.api.BarcodeUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySampleItemSourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleImageDto;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.HisTestApplyDto;
import com.labway.lims.apply.api.dto.HisTestApplyItemDto;
import com.labway.lims.apply.api.dto.InformationEntryTestApplyDto;
import com.labway.lims.apply.api.dto.LogisticsTestApplyDto;
import com.labway.lims.apply.api.dto.PdaEntryTestApplyDto;
import com.labway.lims.apply.api.dto.PhysicalSampleTestApplyDto;
import com.labway.lims.apply.api.dto.PrefabricateTestApplyDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.BarcodeSettingDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.ref.IBarcodeSettingServiceRef;
import com.swak.frame.util.StringPool;
import joptsimple.internal.Strings;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 根据检验项目分条码
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GroupSampleCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private BarcodeUtils barcodeUtils;
    @DubboReference
    private ItemPriceBasePackageService itemPriceBasePackageService;
    @DubboReference
    private IBarcodeSettingServiceRef barcodeSettingService;
    @DubboReference
    private SystemParamService systemParamService;


    // 申请单策略
    private final Map<Class<? extends TestApplyDto>, Strategy> strategies;

    public GroupSampleCommand() {

        this.strategies = Map.of(
                // 申请单录入
                InformationEntryTestApplyDto.class, new InformationEntryStrategy(),
                // PDA样本签收
                PdaEntryTestApplyDto.class, new InformationEntryStrategy(),
                // 体检样本签收
                PhysicalSampleTestApplyDto.class, new PhysicalStrategy(),
                // 物流补录
                LogisticsTestApplyDto.class, new LogisticsStrategy(),
                // 预制条码
                PrefabricateTestApplyDto.class, new PrefabricateStrategy(),
                // 样本签收
                HisTestApplyDto.class, new HisStrategy());
    }

    @Override
    public boolean execute(Context c) throws Exception {
        final AddApplyContext from = AddApplyContext.from(c);
        final TestApplyDto testApply = from.getTestApply();

        // 根据 TestApplyDto 实际类型选择具体的样本策略
        final Strategy strategy = strategies.get(testApply.getClass());

        if (Objects.isNull(strategy)) {
            throw new IllegalArgumentException("未知的检验申请单类型");
        }

        final SampleInfo sampleInfo = strategy.get(from);
        final List<ApplySampleItemDto> applySampleItems = sampleInfo.getApplySampleItems();
        final List<ApplySampleDto> applySamples = sampleInfo.getApplySamples();
        final List<ApplySampleImageDto> applySampleImageDtos = sampleInfo.getApplySampleImageDtos();

        // 校验血培养信息
        this.checkBloodCulture(applySampleItems, from.getBloodCultureTestItemCodes());

        // 标记样本加急状态
        tayApplySampleUrgentStatus(applySampleItems, applySamples);

        // 标记样本外送状态
        tagApplySampleOutsourcingStatus(applySampleItems, applySamples);

        // 设置样本来源信息
        setApplySampleSource(testApply, applySamples);

        // 设置血培养信息
        setBloodCultureInfo(from, testApply, applySampleItems);

        from.put(AddApplyContext.APPLY_SAMPLE_ITEMS, applySampleItems);
        from.put(AddApplyContext.APPLY_SAMPLES, applySamples);
        from.put(AddApplyContext.APPLY_SAMPLE_IMAGES, applySampleImageDtos);

        return CONTINUE_PROCESSING;
    }

    /**
     * 校验分条码后血培养项目  同一个条码下不能由多个血培养项目
     * @param applySampleItems 申请单样本的项目
     * @param bloodCultureTestItemCodes 配置的血培养项目
     */
    public void checkBloodCulture(List<ApplySampleItemDto> applySampleItems, Set<String> bloodCultureTestItemCodes) {

        if (CollectionUtils.isEmpty(bloodCultureTestItemCodes) || CollectionUtils.isEmpty(bloodCultureTestItemCodes)) {
            return;
        }

        // 根据申请单样本分组
        final Map<Long, List<ApplySampleItemDto>> applySampleItemByApplySampleIdMap = applySampleItems.stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        for (Map.Entry<Long, List<ApplySampleItemDto>> entry : applySampleItemByApplySampleIdMap.entrySet()) {
            // 过滤是血培养的类型  || 是配置的血培养项目
            final List<ApplySampleItemDto> bloodCultureItems = entry.getValue().stream().filter(e -> Objects.equals(e.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())
                    || bloodCultureTestItemCodes.contains(e.getTestItemCode())).collect(Collectors.toList());
            if (bloodCultureItems.size() > NumberUtils.INTEGER_ONE) {
                throw new IllegalStateException("一个条码下不能有多个血培养项目：" + bloodCultureItems.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.joining(StringPool.COMMA)));
            }
            if (bloodCultureItems.size() != NumberUtils.INTEGER_ZERO && bloodCultureItems.size() != entry.getValue().size()) {
                throw new IllegalStateException("一个条码下不能同时存在血培养项目和其他项目");
            }
        }
    }

    /**
     * <a href="https://www.tapd.cn/tapd_fe/59091617/story/detail/1159091617001002459">【【样本信息录入】增加样本图片tab 页】</a>
     * 组装申请单样本图片
     * @param imgUrls 图片url
     * @param applySamples 申请单样本
     * @return 申请单图片
     */
    private List<ApplySampleImageDto> createApplySampleImageDtos(List<String> imgUrls, List<ApplySampleDto> applySamples) {

        if (CollectionUtils.isEmpty(applySamples) || CollectionUtils.isEmpty(imgUrls)) {
            return Collections.emptyList();
        }


        List<ApplySampleImageDto> applySampleImageDtos = new ArrayList<>();

        applySamples.forEach(e -> {
            imgUrls.forEach(imgUrl -> {
                ApplySampleImageDto dto = new ApplySampleImageDto();
                dto.setApplyId(e.getApplyId());
                dto.setApplySampleId(e.getApplySampleId());
                dto.setImageUrl(imgUrl);
                applySampleImageDtos.add(dto);
            });

        });

        return applySampleImageDtos;
    }


    // 填充样本来源信息
    private void setApplySampleSource(TestApplyDto testApply, List<ApplySampleDto> applySamples) {
        if (CollectionUtils.isEmpty(applySamples)){
            return;
        }
        applySamples.forEach(e->{
            e.setSampleSource(testApply.getSampleSource());
        });
    }

    private void tayApplySampleUrgentStatus(List<ApplySampleItemDto> applySampleItems,
                                            List<ApplySampleDto> applySamples) {
        // 如果这个样本下的样本全是加急的，那么这个样本也是加急的
        for (final ApplySampleDto applySample : applySamples) {
            final boolean isUrgent =
                    applySampleItems.stream().allMatch(a -> Objects.equals(a.getUrgent(), UrgentEnum.URGENT.getCode()));
            applySample.setUrgent(isUrgent ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        }
    }

    private void setBloodCultureInfo(AddApplyContext from, TestApplyDto testApply,
                                     List<ApplySampleItemDto> applySampleItems) {
        final Set<String> bloodCultureTestItemCodes = from.getBloodCultureTestItemCodes();
        final ApplySampleItemDto applySampleItem = applySampleItems.stream()
                // 过滤出血培养的项目
                .filter(a -> Objects.equals(a.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())
                        || bloodCultureTestItemCodes.contains(a.getTestItemCode()))
                .findFirst()
                .orElse(null);

        // 如果存在设置信息
        if (Objects.isNull(applySampleItem)) {
            return;
        }

        final ApplySampleItemBloodCultureDto bloodCulture = testApply.getItems().stream()
                .filter(f -> Objects.equals(applySampleItem.getTestItemId(), f.getTestItemId())).findFirst()
                .orElse(new TestApplyDto.Item()).getBloodCulture();

        if (Objects.isNull(bloodCulture)) {
            throw new IllegalStateException("血培养信息错误");
        }
        bloodCulture.setApplySampleItemBloodCultureId(snowflakeService.genId());
        bloodCulture.setApplySampleItemId(applySampleItem.getApplySampleItemId());
        bloodCulture.setApplySampleId(applySampleItem.getApplySampleId());
        bloodCulture.setApplyId(applySampleItem.getApplyId());
        bloodCulture.setTestItemId(applySampleItem.getTestItemId());
        bloodCulture.setTestItemCode(applySampleItem.getTestItemCode());
        bloodCulture.setTestItemName(applySampleItem.getTestItemName());
        bloodCulture.setCreateDate(applySampleItem.getCreateDate());
        bloodCulture.setUpdateDate(applySampleItem.getUpdateDate());
        bloodCulture.setCreatorId(applySampleItem.getCreatorId());
        bloodCulture.setCreatorName(applySampleItem.getCreatorName());
        bloodCulture.setUpdaterName(applySampleItem.getCreatorName());
        bloodCulture.setUpdaterId(applySampleItem.getUpdaterId());

        if (StringUtils.isBlank(applySampleItem.getRemark())) {
            applySampleItem.setRemark(generateRemark(bloodCulture));
        }

        from.put(AddApplyContext.BLOOD_CULTURE, bloodCulture);
    }

    private static void tagApplySampleOutsourcingStatus(List<ApplySampleItemDto> applySampleItems,
                                                        List<ApplySampleDto> applySamples) {
        for (final ApplySampleDto applySample : applySamples) {
            if (applySampleItems.stream()
                    // 过滤出当前样本的项目
                    .filter(f -> Objects.equals(applySample.getApplySampleId(), f.getApplySampleId()))
                    // 如果全是外送
                    .allMatch(item -> Objects.equals(YesOrNoEnum.YES.getCode(), item.getIsOutsourcing()))) {

                // 那么样本标记成外送
                applySample.setIsOutsourcing(YesOrNoEnum.YES.getCode());

            }
        }
    }

    /**
     * 生成血培养备注信息
     */
    private static String generateRemark(ApplySampleItemBloodCultureDto bloodCulture) {
        return String.format(
                "左上肢:厌氧(%s),左上肢需氧(%s),左上肢儿童瓶(%s)\n" + "右上肢:厌氧(%s),右上肢需氧(%s),右上肢儿童瓶(%s)\n"
                        + "左下肢:厌氧(%s),左下肢需氧(%s),左下肢儿童瓶(%s)\n" + "右下肢:厌氧(%s),右下肢需氧(%s),右下肢儿童瓶(%s)\n" + "厌氧(%s),需氧(%s),儿童瓶(%s)",
                ObjectUtils.defaultIfNull(bloodCulture.getLulAnaerobic(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getLulAerobic(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getLulPediatricBottle(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getRulAnaerobic(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getRulAerobic(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getRulPediatricBottle(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getLllAnaerobic(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getLllAerobic(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getLllPediatricBottle(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getRllAnaerobic(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getRllAerobic(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getRllPediatricBottle(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getAnaerobic(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getAerobic(), NumberUtils.INTEGER_ZERO),
                ObjectUtils.defaultIfNull(bloodCulture.getPediatricBottle(), NumberUtils.INTEGER_ZERO));
    }

    /**
     * 此方法不设置这些信息 applyId、管型、样本类型、条码号 需要自己设置
     */
    public ApplySampleDto createApplySample() {
        final Date now = new Date();
        final LoginUserHandler.User user = LoginUserHandler.get();

        final ApplySampleDto applySample = new ApplySampleDto();
        applySample.setApplySampleId(snowflakeService.genId());
        applySample.setOutBarcode(StringUtils.EMPTY);
        applySample.setGroupId(NumberUtils.LONG_ZERO);
        applySample.setGroupName(StringUtils.EMPTY);
        applySample.setRackId(NumberUtils.LONG_ZERO);
        applySample.setOnePickerId(NumberUtils.LONG_ZERO);
        applySample.setOnePickerName(StringUtils.EMPTY);
        applySample.setUrgent(UrgentEnum.NORMAL.getCode());
        applySample.setCreateDate(now);
        applySample.setUpdateDate(now);
        applySample.setCreatorId(user.getUserId());
        applySample.setCreatorName(user.getNickname());
        applySample.setUpdaterId(user.getUserId());
        applySample.setUpdaterName(user.getNickname());

        applySample.setTesterId(NumberUtils.LONG_ZERO);
        applySample.setTesterName(StringUtils.EMPTY);

        applySample.setIsDisabled(YesOrNoEnum.NO.getCode());
        applySample.setIsDelete(YesOrNoEnum.NO.getCode());
        applySample.setIsOnePick(YesOrNoEnum.NO.getCode());
        applySample.setOnePickDate(DefaultDateEnum.DEFAULT_DATE.getDate());

        applySample.setIsSplitBlood(YesOrNoEnum.NO.getCode());
        applySample.setSplitterId(NumberUtils.LONG_ZERO);
        applySample.setSplitterName(StringUtils.EMPTY);
        applySample.setSplitDate(DefaultDateEnum.DEFAULT_DATE.getDate());

        applySample.setStatus(SampleStatusEnum.ENTER.getCode());
        applySample.setPrinterId(NumberUtils.LONG_ZERO);
        applySample.setPrinterName(StringUtils.EMPTY);
        applySample.setPrintDate(DefaultDateEnum.DEFAULT_DATE.getDate());

        applySample.setTesterId(NumberUtils.LONG_ZERO);
        applySample.setTesterName(StringUtils.EMPTY);

        applySample.setTwoPickDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        applySample.setTwoPickerId(NumberUtils.LONG_ZERO);
        applySample.setTwoPickerName(StringUtils.EMPTY);
        applySample.setIsTwoPick(YesOrNoEnum.NO.getCode());

        applySample.setOrgId(LoginUserHandler.get().getOrgId());
        applySample.setOrgName(LoginUserHandler.get().getOrgName());

        applySample.setIsOutsourcing(YesOrNoEnum.NO.getCode());

        applySample.setSampleRemark(StringUtils.EMPTY);
        applySample.setResultRemark(StringUtils.EMPTY);

        applySample.setItemType(StringUtils.EMPTY);

        applySample.setReportNo(StringUtils.EMPTY);

        return applySample;
    }

    /**
     * 按照 管型 + 样本类型 + 自定义分割码 + 专业组（由group判定） 分组
     *
     * @param group 如果为true 则加上专业组进行分组
     */
    public Map<String, List<ApplySampleItemDto>>
    groupTestItem(ApplyDto apply, List<TestItemDto> testItems,
                  Map<Long, ? extends TestApplyDto.Item> applyItemMap, boolean group) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final Date now = new Date();
        // key: 检验项目id value: 实际收费价格
        Map<Long, BigDecimal> actualFeePriceByTestItemIdMap = itemPriceBasePackageService
                .selectActualFeePrice(apply.getHspOrgId(), apply.getApplyTypeCode(), now, applyItemMap.keySet());

        // 根据 样本类型 + 管型 + 自定义分割码 分条码
        return testItems.stream().collect(Collectors.groupingBy(
                // key  样本分组：  样本类型-管型-检验项目id-自定义码-（专业组id， 是检验项目加减项的话）
                g -> {
                    StringBuilder splitRule = new StringBuilder();
                    // 样本类型
                    splitRule.append(StringUtils.defaultString(g.getSampleTypeCode()));
                    // 管型
                    splitRule.append(StringUtils.defaultString(g.getTubeCode()));
                    // 自定义码
                    Optional.ofNullable(applyItemMap.get(g.getTestItemId()))
                            .ifPresent(applyItem -> splitRule.append(StringUtils.defaultString(applyItem.getCustomCode())));
                    // 专业组
                    if (BooleanUtils.isTrue(group)) {
                        splitRule.append(g.getGroupId());
                    }

                    return splitRule.toString();
                },

                // value 对应样本下的项目信息
                Collectors.mapping(f -> {
                    final TestApplyDto.Item item = applyItemMap.get(f.getTestItemId());
                    final Optional<TestApplyDto.Item> optionalItem = Optional.ofNullable(item);
                    final Integer urgent =
                            optionalItem.map(TestApplyDto.Item::getUrgent).orElse(UrgentEnum.NORMAL.getCode());
                    final Integer count = optionalItem.map(TestApplyDto.Item::getCount).orElse(1);
                    final String customCode = optionalItem.map(TestApplyDto.Item::getCustomCode).orElse(StringUtils.EMPTY);
                    final String remark = optionalItem.map(TestApplyDto.Item::getRemark).orElse(StringUtils.EMPTY);
                    final ApplySampleItemDto applySampleItem = new ApplySampleItemDto();
                    applySampleItem.setTestItemId(f.getTestItemId());
                    applySampleItem.setTestItemCode(f.getTestItemCode());
                    applySampleItem.setTestItemName(f.getTestItemName());
                    applySampleItem.setItemType(f.getItemType());
                    applySampleItem.setSampleTypeName(StringUtils.defaultString(f.getSampleTypeName()));
                    applySampleItem.setSampleTypeCode(StringUtils.defaultString(f.getSampleTypeCode()));
                    applySampleItem.setTubeName(StringUtils.defaultString(f.getTubeName()));
                    applySampleItem.setTubeCode(StringUtils.defaultString(f.getTubeCode()));
                    applySampleItem.setGroupId(f.getGroupId());
                    applySampleItem.setGroupName(f.getGroupName());
                    applySampleItem.setUrgent(urgent);
                    applySampleItem.setCount(count);
                    applySampleItem.setRemark(remark);
                    applySampleItem.setSplitCode(customCode);
                    applySampleItem.setCreateDate(now);
                    applySampleItem.setUpdateDate(now);
                    applySampleItem.setCreatorId(user.getUserId());
                    applySampleItem.setCreatorName(user.getNickname());
                    applySampleItem.setUpdaterId(user.getUserId());
                    applySampleItem.setUpdaterName(user.getNickname());
                    applySampleItem.setIsDelete(YesOrNoEnum.NO.getCode());

                    applySampleItem
                            .setIsOutsourcing(ObjectUtils.defaultIfNull(f.getEnableExport(), YesOrNoEnum.NO.getCode()));
                    applySampleItem.setExportOrgId(ObjectUtils.defaultIfNull(f.getExportOrgId(), NumberUtils.LONG_ZERO));
                    applySampleItem.setExportOrgName(ObjectUtils.defaultIfNull(f.getExportOrgName(), StringUtils.EMPTY));

                    // 计费的话写入db项目价格， 不计费的话给默认值0
                    f.setEnableFee(ObjectUtils.defaultIfNull(f.getEnableFee(), YesOrNoEnum.NO.getCode()));
                    if (Objects.equals(f.getEnableFee(), YesOrNoEnum.YES.getCode())) {
                        applySampleItem.setFeePrice(f.getFeePrice());
                    } else {
                        applySampleItem.setFeePrice(BigDecimal.ZERO);
                    }
                    // 实际收费价格， 项目价格基准包的价格
                    applySampleItem.setActualFeePrice(actualFeePriceByTestItemIdMap
                            .getOrDefault(applySampleItem.getTestItemId(), applySampleItem.getFeePrice()));
                    // 是否免单， 默认不免单
                    applySampleItem.setIsFree(YesOrNoEnum.NO.getCode());
                    applySampleItem.setItemSource(ApplySampleItemSourceEnum.DEFAULT_SOURCE.getCode());
                    String outTestItemCode = null;
                    String outTestItemName = null;

                    // 如果是签收进来的话 给对照的外部项目
                    if (item instanceof HisTestApplyItemDto) {
                        outTestItemCode = ((HisTestApplyItemDto) item).getOutTestItemCode();
                        outTestItemName = ((HisTestApplyItemDto) item).getOutTestItemName();
                    }

                    applySampleItem.setOutTestItemId(NumberUtils.LONG_ZERO);
                    applySampleItem.setOutTestItemName(StringUtils.defaultString(outTestItemName));
                    applySampleItem.setOutTestItemCode(StringUtils.defaultString(outTestItemCode));

                    return applySampleItem;
                }, Collectors.toList())));
    }

    private interface Strategy {
        SampleInfo get(AddApplyContext context);
    }

    public final class InformationEntryStrategy implements Strategy {

        @Override
        public SampleInfo get(AddApplyContext from) {
            final TestApplyDto testApply = from.getTestApply();
            final List<TestApplyDto.Item> items = testApply.getItems();
            if (CollectionUtils.isEmpty(items)) {
                throw new IllegalStateException("检验项目为空");
            }
            // 要保存的申请单
            final ApplyDto apply = from.getApply();
            // 保存的检验项目
            final Map<Long, TestApplyDto.Item> addApplyItemMap = testApply.getItems().stream()
                    .collect(Collectors.toMap(TestApplyDto.Item::getTestItemId, v -> v, (a, b) -> a));

            // 根据管型 + 样本类型 + 自定义码 分组   size = 样本数量
            final Map<String, List<ApplySampleItemDto>> groupTestItemMap = groupTestItem(apply, from.getTestItems(), addApplyItemMap, false);

            // 如果分出来的项目为空，报错， 理论上不会， 因为要保存的检验项目不为空， 上面判断过了
            final Collection<List<ApplySampleItemDto>> applySampleItems = groupTestItemMap.values();
            if (CollectionUtils.isEmpty(applySampleItems)) {
                throw new IllegalStateException("样本检验项目为空");
            }
            // 校验如果是his签收是否允许分管
            this.checkHisSplitTube(testApply, applySampleItems.size());

            // 获取条码
            final LinkedList<String> barcodes = getBarcodeList(from.getUseOutBarcode(), from.getOutBarcode(), apply, applySampleItems.size());

            final List<ApplySampleDto> applySamples = new ArrayList<>();
            // 创建样本
            for (final List<ApplySampleItemDto> m : applySampleItems) {

                final ApplySampleItemDto applySampleItem = m.stream().findFirst().orElse(null);
                if (Objects.isNull(applySampleItem)) {
                    throw new IllegalStateException("样本检验项目为空");
                }
                // 创建样本
                final ApplySampleDto applySample = createApplySample();
                applySample.setApplyId(apply.getApplyId());
                applySample.setBarcode(barcodes.pop());
                applySample.setOriginalBarcode(applySample.getBarcode());
                applySample.setTubeCode(applySampleItem.getTubeCode());
                applySample.setTubeName(applySampleItem.getTubeName());
                applySample.setSampleTypeCode(StringUtils.defaultString(applySampleItem.getSampleTypeCode()));
                applySample.setSampleTypeName(StringUtils.defaultString(applySampleItem.getSampleTypeName()));
                applySample.setItemType(applySampleItem.getItemType());
                applySample.setHspOrgCode(apply.getHspOrgCode());
                applySample.setHspOrgName(apply.getHspOrgName());
                applySample.setSampleProperty(testApply.getSampleProperty());
                applySample.setSamplePropertyCode(testApply.getSamplePropertyCode());
                // 页面录入的外部条码号
                applySample.setOutBarcode(apply.getOutBarcode());

				// ✨feat：【1.1.4】增加标本部位字段 此字段用于签收病理的样本时，落库  https://www.tapd.cn/59091617/prong/stories/view/1159091617001001756?from_iteration_id=1159091617001000242
	            // 从业务中台获取的数据中修改
	            if (testApply instanceof HisTestApplyDto) {
		            applySample.setPatientPart(((HisTestApplyDto) testApply).getPatientPart());
	            }

                // 设置关联id
                for (final ApplySampleItemDto applySampleItemDto : m) {
                    applySampleItemDto.setApplyId(apply.getApplyId());
                    applySampleItemDto.setApplySampleId(applySample.getApplySampleId());
                    applySampleItemDto.setApplySampleItemId(snowflakeService.genId());
                    applySample.setItemType(
                            StringUtils.defaultString(applySample.getItemType(), applySampleItemDto.getItemType()));
                }

                applySamples.add(applySample);
            }

            if (CollectionUtils.isEmpty(applySamples)) {
                throw new IllegalStateException("样本为空");
            }

            // 上海-V1.1.4.2 【样本信息录入】增加样本图片tab--保存录入样本 https://www.tapd.cn/tapd_fe/59091617/story/detail/1159091617001002459?from_iteration_id=1159091617001000331
            List<ApplySampleImageDto> applySampleImageDtos = new ArrayList<>();
            if (testApply instanceof InformationEntryTestApplyDto){
                applySampleImageDtos = createApplySampleImageDtos(((InformationEntryTestApplyDto) testApply).getImgUrls(), applySamples);
            }

            // 返回前端的数据
            SampleInfo sampleInfo = new SampleInfo();
            sampleInfo.setApplySampleItems(CollUtil.defaultIfEmpty(
                    applySampleItems.stream().flatMap(Collection::stream).collect(Collectors.toList()),
                    Collections.emptyList()));

            sampleInfo.setApplySamples(applySamples);
            sampleInfo.setApplySampleImageDtos(applySampleImageDtos);

            return sampleInfo;
        }

        /**
         * 校验his签收的样本是否允许分管
         * 允许分管， 则样本数量必须为1
         * @param testApply 申请单
         * @param applySampleSize 样本数量
          */
        private void checkHisSplitTube(TestApplyDto testApply, int applySampleSize) {
            if (testApply instanceof HisTestApplyDto) {
                HisTestApplyDto hisTestApply = (HisTestApplyDto) testApply;
                //如果不允许分管 则提示报错
                if (!hisTestApply.isCanSplitSample() && !Objects.equals(applySampleSize, NumberUtils.INTEGER_ONE)) {
                    throw new IllegalStateException(String.format("外部条码 [%s] 存在项目管型、样本类型、自定义码不一致的项目", hisTestApply.getOutBarcode()));
                }
            }
        }


    }

    public final class HisStrategy implements Strategy {

        @Override
        public SampleInfo get(AddApplyContext from) {
            HisTestApplyDto hisTestApply = (HisTestApplyDto) from.getTestApply();
            final HspOrganizationDto hspOrganization = from.getHspOrganization();
            final boolean generateBarcode = BooleanUtils.toBoolean(hspOrganization.getEnableGenerateBarcode());

            // 如果没有使用过外部条码，则 barcode 替换成 outBarcode, 否则使用原有的 barcode
            final String outBarcode = hisTestApply.getOutBarcode();
            int count = SpringUtil.getBean(ApplySampleService.class).countByBarcode(outBarcode);
            if (count == NumberUtils.INTEGER_ZERO && BooleanUtils.isFalse(generateBarcode)) {
                // 标识使用外部条码
                from.put(AddApplyContext.USE_OUT_BARCODE, Boolean.TRUE);
                from.put(AddApplyContext.OUT_BARCODE, outBarcode);
            }

            // 执行策略
            final SampleInfo sampleInfo = new InformationEntryStrategy().get(from);

            // 赋值外部条码和原始条码号
            sampleInfo.getApplySamples().forEach(v -> {
                v.setOutBarcode(outBarcode);
                v.setOriginalBarcode(v.getBarcode());
            });

            return sampleInfo;
        }
    }

    public final class LogisticsStrategy implements Strategy {

        @Override
        public SampleInfo get(AddApplyContext context) {
            final TestApplyDto testApply = context.getTestApply();
            final ApplyDto apply = context.getApply();

            final Map<Long, TestItemDto> testItemMap = context.getTestItems().stream()
                    .collect(Collectors.toMap(TestItemDto::getTestItemId, v -> v, (a, b) -> a));

            LogisticsTestApplyDto logisticsTestApply = (LogisticsTestApplyDto) testApply;

            final LogisticsTestApplyDto.Sample sample = logisticsTestApply.getSample();
            if (Objects.isNull(sample)) {
                throw new IllegalStateException("物流样本为空");
            }

            // 创建申请单样本
            ApplySampleDto applySample = createApplySample();
            applySample.setApplyId(apply.getApplyId());
            applySample.setBarcode(sample.getBarcode());
            applySample.setOriginalBarcode(sample.getBarcode());
            applySample.setItemType(testItemMap.values().iterator().next().getItemType());
            applySample.setHspOrgCode(apply.getHspOrgCode());
            applySample.setHspOrgName(apply.getHspOrgName());
            applySample.setSampleProperty(testApply.getSampleProperty());
            applySample.setSamplePropertyCode(testApply.getSamplePropertyCode());

            // 创建申请单检验项目 这个方法会给 applySample 设置管型、样本类型
            List<ApplySampleItemDto> applySampleItems =
                    createLogisticsApplySampleItems(apply, applySample, testItemMap, sample.getItems());

            SampleInfo sampleInfo = new SampleInfo();
            sampleInfo.setApplySamples(CollUtil.defaultIfEmpty(List.of(applySample), Collections.emptyList()));
            sampleInfo.setApplySampleItems(CollUtil.defaultIfEmpty(applySampleItems, Collections.emptyList()));
            sampleInfo.setApplySampleImageDtos(Collections.emptyList());

            return sampleInfo;
        }

        /**
         * 获取样本检验项目信息
         */
        private List<ApplySampleItemDto> createLogisticsApplySampleItems(ApplyDto apply, ApplySampleDto applySample,
                                                                         Map<Long, TestItemDto> testItemMap, List<TestApplyDto.Item> items) {
            final Date now = new Date();
            // key: 检验项目id value: 实际收费价格
            Map<Long, BigDecimal> actualFeePriceByTestItemId = itemPriceBasePackageService
                    .selectActualFeePrice(apply.getHspOrgId(), apply.getApplyTypeCode(), now, testItemMap.keySet());

            final LoginUserHandler.User user = LoginUserHandler.get();

            List<ApplySampleItemDto> applySampleItems = new ArrayList<>();
            final LinkedList<Long> ids = snowflakeService.genIds(items.size());
            for (final TestApplyDto.Item item : items) {
                ApplySampleItemDto applySampleItem = new ApplySampleItemDto();
                applySampleItem.setApplySampleItemId(ids.pop());
                applySampleItem.setApplySampleId(applySample.getApplySampleId());
                applySampleItem.setApplyId(applySample.getApplyId());

                final TestItemDto testItem = testItemMap.get(item.getTestItemId());
                if (Objects.isNull(testItem)) {
                    throw new IllegalStateException(String.format("物流条码 [%s] 检验项目不存在", applySample.getBarcode()));
                }

                applySampleItem.setTestItemId(testItem.getTestItemId());
                applySampleItem.setTestItemCode(testItem.getTestItemCode());
                applySampleItem.setTestItemName(testItem.getTestItemName());
                applySampleItem.setItemType(testItem.getItemType());
                applySampleItem.setOutTestItemId(NumberUtils.LONG_ZERO);
                applySampleItem.setOutTestItemCode(StringUtils.EMPTY);
                applySampleItem.setOutTestItemName(StringUtils.EMPTY);
                applySampleItem.setSampleTypeName(testItem.getSampleTypeName());
                applySampleItem.setSampleTypeCode(testItem.getSampleTypeCode());
                applySampleItem.setTubeName(testItem.getTubeName());
                applySampleItem.setTubeCode(testItem.getTubeCode());
                applySampleItem.setGroupId(testItem.getGroupId());
                applySampleItem.setGroupName(testItem.getGroupName());
                applySampleItem.setUrgent(YesOrNoEnum.NO.getCode());
                applySampleItem.setRemark(StringUtils.EMPTY);
                applySampleItem.setCreateDate(now);
                applySampleItem.setCount(NumberUtils.INTEGER_ONE);
                applySampleItem.setUpdateDate(now);
                applySampleItem.setCreatorId(user.getUserId());
                applySampleItem.setCreatorName(user.getNickname());
                applySampleItem.setUpdaterId(user.getUserId());
                applySampleItem.setUpdaterName(user.getNickname());
                applySampleItem.setIsDelete(YesOrNoEnum.NO.getCode());
                applySampleItem.setSplitCode(StringUtils.defaultString(item.getCustomCode()));
                applySampleItem.setIsOutsourcing(YesOrNoEnum.NO.getCode());

                // 设置样本的样本类型 管型
                applySample.setTubeName(testItem.getTubeName());
                applySample.setTubeCode(testItem.getTubeCode());
                applySample.setSampleTypeCode(testItem.getSampleTypeCode());
                applySample.setSampleTypeName(testItem.getSampleTypeName());
                applySample
                        .setItemType(StringUtils.defaultString(applySample.getItemType(), applySampleItem.getItemType()));

                applySampleItem
                        .setIsOutsourcing(ObjectUtils.defaultIfNull(testItem.getEnableExport(), YesOrNoEnum.NO.getCode()));
                applySampleItem
                        .setExportOrgId(ObjectUtils.defaultIfNull(testItem.getExportOrgId(), NumberUtils.LONG_ZERO));
                applySampleItem
                        .setExportOrgName(ObjectUtils.defaultIfNull(testItem.getExportOrgName(), StringUtils.EMPTY));
                applySampleItem.setFeePrice(testItem.getFeePrice());
                applySampleItem.setActualFeePrice(
                        actualFeePriceByTestItemId.getOrDefault(testItem.getTestItemId(), applySampleItem.getFeePrice()));
                applySampleItem.setIsFree(YesOrNoEnum.NO.getCode());
                applySampleItem.setItemSource(ApplySampleItemSourceEnum.DEFAULT_SOURCE.getCode());
                applySampleItems.add(applySampleItem);
            }
            return applySampleItems;
        }

    }

    @Getter
    @Setter
    public static final class SampleInfo {

        /**
         * 样本信息 不要返回 null 出来，返回空列表 Collections.emptyList()
         */
        @Nonnull
        private List<ApplySampleDto> applySamples;
        /**
         * 样本检验项目信息 不要返回 null 出来，返回空列表 Collections.emptyList()
         */
        @Nonnull
        private List<ApplySampleItemDto> applySampleItems;

        /**
         * 样本图片信息 不要返回 null 出来，返回空列表 Collections.emptyList()
         */
        List<ApplySampleImageDto> applySampleImageDtos;

    }

    /**
     * 体检样本签收
     */
    public final class PhysicalStrategy implements Strategy {

        @Override
        public SampleInfo get(AddApplyContext context) {
            final TestApplyDto testApply = context.getTestApply();
            final ApplyDto apply = context.getApply();
            apply.setStatus(ApplyStatusEnum.DOUBLE_CHECK.getCode());

            final Map<Long, TestItemDto> testItemMap = context.getTestItems().stream()
                    .collect(Collectors.toMap(TestItemDto::getTestItemId, v -> v, (a, b) -> a));

            PhysicalSampleTestApplyDto testApplyDto = (PhysicalSampleTestApplyDto) testApply;

            String barcode = testApplyDto.getBarcode();
            List<TestApplyDto.Item> items = testApplyDto.getItems();
            if (StringUtils.isBlank(barcode)) {
                throw new IllegalStateException("体检样本条码为空");
            }

            // 创建申请单样本
            ApplySampleDto applySample = createApplySample();
            applySample.setApplyId(apply.getApplyId());
            applySample.setBarcode(barcode);
            applySample.setOriginalBarcode(barcode);
            applySample.setOutBarcode(StringUtils.EMPTY);
            applySample.setSampleTypeName(testApplyDto.getSampleType());
            applySample.setTubeName(testApplyDto.getTube());
            applySample.setItemType(testItemMap.values().iterator().next().getItemType());
            applySample.setHspOrgCode(apply.getHspOrgCode());
            applySample.setHspOrgName(apply.getHspOrgName());
            applySample.setSampleProperty(testApply.getSampleProperty());
            applySample.setSamplePropertyCode(testApply.getSamplePropertyCode());

            // 创建申请单检验项目
            List<ApplySampleItemDto> applySampleItems =
                    createPhysicalApplySampleItems(apply, applySample, testItemMap, items);

            SampleInfo sampleInfo = new SampleInfo();
            sampleInfo.setApplySamples(List.of(applySample));
            sampleInfo.setApplySampleItems(applySampleItems);
            sampleInfo.setApplySampleImageDtos(Collections.emptyList());

            return sampleInfo;
        }

        /**
         * 获取样本检验项目信息
         */
        private List<ApplySampleItemDto> createPhysicalApplySampleItems(ApplyDto apply, ApplySampleDto applySample,
                                                                        Map<Long, TestItemDto> testItemMap, List<TestApplyDto.Item> items) {
            final LoginUserHandler.User user = LoginUserHandler.get();
            Date now = new Date();
            List<ApplySampleItemDto> applySampleItems = new ArrayList<>();
            // key: 检验项目id value: 实际收费价格
            Map<Long, BigDecimal> actualFeePriceByTestItemId = itemPriceBasePackageService
                    .selectActualFeePrice(apply.getHspOrgId(), apply.getApplyTypeCode(), now, testItemMap.keySet());

            for (final TestApplyDto.Item item : items) {
                ApplySampleItemDto applySampleItem = new ApplySampleItemDto();
                applySampleItem.setApplySampleItemId(snowflakeService.genId());
                applySampleItem.setApplySampleId(applySample.getApplySampleId());
                applySampleItem.setApplyId(applySample.getApplyId());

                final TestItemDto testItem = testItemMap.get(item.getTestItemId());
                if (Objects.isNull(testItem)) {
                    throw new IllegalStateException(String.format("条码 [%s] 检验项目不存在", applySample.getBarcode()));
                }

                applySampleItem.setTestItemId(testItem.getTestItemId());
                applySampleItem.setTestItemCode(testItem.getTestItemCode());
                applySampleItem.setTestItemName(testItem.getTestItemName());
                applySampleItem.setItemType(testItem.getItemType());
                applySampleItem.setOutTestItemId(NumberUtils.LONG_ZERO);
                applySampleItem.setOutTestItemCode(StringUtils.EMPTY);
                applySampleItem.setOutTestItemName(StringUtils.EMPTY);
                applySampleItem.setSampleTypeCode(testItem.getSampleTypeCode());
                applySampleItem.setSampleTypeName(testItem.getSampleTypeName());
                applySampleItem.setTubeCode(testItem.getTubeCode());
                applySampleItem.setTubeName(testItem.getTubeName());
                applySampleItem.setGroupId(testItem.getGroupId());
                applySampleItem.setGroupName(testItem.getGroupName());
                applySampleItem.setUrgent(YesOrNoEnum.NO.getCode());
                applySampleItem.setRemark(StringUtils.EMPTY);
                applySampleItem.setCreateDate(now);
                applySampleItem.setCount(NumberUtils.INTEGER_ONE);
                applySampleItem.setUpdateDate(now);
                applySampleItem.setCreatorId(user.getUserId());
                applySampleItem.setCreatorName(user.getNickname());
                applySampleItem.setUpdaterId(user.getUserId());
                applySampleItem.setUpdaterName(user.getNickname());
                applySampleItem.setIsDelete(YesOrNoEnum.NO.getCode());
                applySampleItem.setSplitCode(StringUtils.EMPTY);

                // 设置样本的样本类型 管型
                applySample.setTubeCode(testItem.getTubeCode());
                applySample.setTubeName(testItem.getTubeName());
                applySample.setSampleTypeCode(testItem.getSampleTypeCode());
                applySample.setSampleTypeName(testItem.getSampleTypeName());
                applySample
                        .setItemType(StringUtils.defaultString(applySample.getItemType(), applySampleItem.getItemType()));

                applySampleItem.setIsOutsourcing(ObjectUtils.defaultIfNull(testItem.getEnableExport(), 0));
                applySampleItem
                        .setExportOrgId(ObjectUtils.defaultIfNull(testItem.getExportOrgId(), NumberUtils.LONG_ZERO));
                applySampleItem
                        .setExportOrgName(ObjectUtils.defaultIfNull(testItem.getExportOrgName(), StringUtils.EMPTY));
                applySampleItem.setFeePrice(testItem.getFeePrice());
                applySampleItem.setActualFeePrice(
                        actualFeePriceByTestItemId.getOrDefault(testItem.getTestItemId(), applySampleItem.getFeePrice()));
                applySampleItem.setIsFree(YesOrNoEnum.NO.getCode());
                applySampleItem.setItemSource(ApplySampleItemSourceEnum.DEFAULT_SOURCE.getCode());
                applySampleItems.add(applySampleItem);
            }
            return applySampleItems;
        }
    }


    /**
     * 预制条码信息录入
     */
    public final class PrefabricateStrategy implements Strategy {

        @Override
        public SampleInfo get(AddApplyContext from) {
            final List<TestApplyDto.Item> items = from.getTestApply().getItems();
            if (CollectionUtils.isEmpty(items)) {
                throw new IllegalStateException("检验项目为空");
            }
            final ApplyDto apply = from.getApply();

            PrefabricateTestApplyDto testApply = (PrefabricateTestApplyDto) from.getTestApply();

            // 预制条码信息
            List<PrefabricateTestApplyDto.BarcodeInfo> barcodeInfos = testApply.getBarcodeInfos();

            // redis 预制条码hset key
//            final String barcodeKey = String.format(BarcodeSettingEnum.BARCODE_CACHE.getRedisKey(), redisPrefix.getBasePrefix(), BarcodeSettingDto.BARCODE_TYPE, apply.getHspOrgCode());

            for (PrefabricateTestApplyDto.BarcodeInfo barcodeInfo : barcodeInfos) {
                // 判断预制条码是否该机构下的
                BarcodeSettingDto barcodeSettingDto = barcodeSettingService.selectByBarcode(barcodeInfo.getBarcode(), BarcodeSettingDto.BARCODE_TYPE);
                if (!Objects.equals(barcodeSettingDto.getHspOrgCode(), apply.getHspOrgCode())) {
                    throw new IllegalStateException(String.format("该机构和条码[%s]不匹配", barcodeInfo.getBarcode()));
                }
//                // 判断预制条码是否可用（在redis hset中）
//                Boolean member = stringRedisTemplate.opsForSet().isMember(barcodeKey, barcodeInfo.getBarcode());
//                if (BooleanUtils.isFalse(member)) {
//                    throw new IllegalArgumentException(String.format("[%s] 不是预制条码", barcodeInfo.getBarcode()));
//                }
            }

            // 保存的检验项目
            Map<Long, TestApplyDto.Item> testItemMap = testApply.getItems().stream().collect(Collectors.toMap(TestApplyDto.Item::getTestItemId, Function.identity(), (a, b) -> b));

            // 保存的检验项目 根据管型分组
            Map<String, List<TestApplyDto.Item>> pageTubeCodeItemsMap = testApply.getItems().stream().collect(Collectors.groupingBy(TestApplyDto.Item::getTubeCode));

            final List<ApplySampleItemDto> applySampleItemDtoList = new LinkedList<>();
            final List<ApplySampleDto> applySampleDtoList = new ArrayList<>();

            // 循环条码（样本）
            for (PrefabricateTestApplyDto.BarcodeInfo barcodeInfo : barcodeInfos) {
                List<TestItemDto> testItems = from.getTestItems().stream()
                        .peek(e -> {
                            TestApplyDto.Item testItem = testItemMap.get(e.getTestItemId());
                            e.setTubeCode(testItem.getTubeCode());
                            e.setTubeName(testItem.getTubeName());
                            e.setSampleTypeCode(testItem.getSampleTypeCode());
                            e.setSampleTypeName(testItem.getSampleTypeName());
                        })
                        .filter(e -> Objects.equals(e.getTubeCode(), barcodeInfo.getTubeCode())).collect(Collectors.toList());

                Map<Long, TestApplyDto.Item> applyItemMap = pageTubeCodeItemsMap.get(barcodeInfo.getTubeCode()).stream()
                        .collect(Collectors.toMap(TestApplyDto.Item::getTestItemId, v -> v, (a, b) -> a));
                // 创建样本项目
                List<ApplySampleItemDto> applySampleItems = this.getApplySampleItems(apply, testItems, applyItemMap);


                final ApplySampleItemDto applySampleItem = applySampleItems.stream().findFirst().orElse(null);
                if (Objects.isNull(applySampleItem)) {
                    throw new IllegalStateException("样本检验项目为空");
                }

                // 创建样本
                final ApplySampleDto applySample = createApplySample();
                applySample.setApplyId(apply.getApplyId());
                applySample.setBarcode(barcodeInfo.getBarcode());
                applySample.setOriginalBarcode(barcodeInfo.getBarcode());
                applySample.setTubeCode(applySampleItem.getTubeCode());
                applySample.setTubeName(applySampleItem.getTubeName());
                applySample.setSampleTypeCode(StringUtils.defaultString(applySampleItem.getSampleTypeCode()));
                applySample.setSampleTypeName(StringUtils.defaultString(applySampleItem.getSampleTypeName()));
                applySample.setItemType(applySampleItem.getItemType());
                applySample.setHspOrgCode(apply.getHspOrgCode());
                applySample.setHspOrgName(apply.getHspOrgName());
                applySample.setSampleProperty(from.getTestApply().getSampleProperty());
                applySample.setSamplePropertyCode(from.getTestApply().getSamplePropertyCode());
                applySample.setOutBarcode(StringUtils.defaultString(testApply.getOutBarcode()));

                // 设置关联id
                for (final ApplySampleItemDto applySampleItemDto : applySampleItems) {
                    applySampleItemDto.setApplyId(apply.getApplyId());
                    applySampleItemDto.setApplySampleId(applySample.getApplySampleId());
                    applySampleItemDto.setApplySampleItemId(snowflakeService.genId());
                    applySample.setItemType(
                            StringUtils.defaultString(applySample.getItemType(), applySampleItemDto.getItemType()));
                }

                // 子条码填充信息
                applySampleItemDtoList.addAll(applySampleItems);
                applySampleDtoList.add(applySample);
            }


            // 填充信息
            SampleInfo sampleInfo = new SampleInfo();
            sampleInfo.setApplySampleItems(applySampleItemDtoList);
            sampleInfo.setApplySamples(applySampleDtoList);
            sampleInfo.setApplySampleImageDtos(Collections.emptyList());

            return sampleInfo;
        }

        private List<ApplySampleItemDto> getApplySampleItems(ApplyDto apply,
                                                             List<TestItemDto> testItems,
                                                             Map<Long, ? extends TestApplyDto.Item> applyItemMap
        ) {
            final LoginUserHandler.User user = LoginUserHandler.get();
            final Date now = new Date();

            Map<Long, BigDecimal> actualFeePriceByTestItemId = itemPriceBasePackageService
                    .selectActualFeePrice(apply.getHspOrgId(), apply.getApplyTypeCode(), now, applyItemMap.keySet());

            return testItems.stream().map(testItem -> {
                final ApplySampleItemDto applySampleItem = new ApplySampleItemDto();
                final TestApplyDto.Item item = applyItemMap.get(testItem.getTestItemId());
                final Optional<TestApplyDto.Item> optionalItem = Optional.ofNullable(item);
                final Integer urgent =
                        optionalItem.map(TestApplyDto.Item::getUrgent).orElse(UrgentEnum.NORMAL.getCode());
                final Integer count = optionalItem.map(TestApplyDto.Item::getCount).orElse(1);
                final String customCode = optionalItem.map(TestApplyDto.Item::getCustomCode).orElse(StringUtils.EMPTY);
                final String remark = optionalItem.map(TestApplyDto.Item::getRemark).orElse(StringUtils.EMPTY);

                applySampleItem.setTestItemId(testItem.getTestItemId());
                applySampleItem.setTestItemCode(testItem.getTestItemCode());
                applySampleItem.setTestItemName(testItem.getTestItemName());
                applySampleItem.setItemType(testItem.getItemType());
                applySampleItem.setSampleTypeName(StringUtils.defaultString(testItem.getSampleTypeName()));
                applySampleItem.setSampleTypeCode(StringUtils.defaultString(testItem.getSampleTypeCode()));
                applySampleItem.setTubeName(StringUtils.defaultString(testItem.getTubeName()));
                applySampleItem.setTubeCode(StringUtils.defaultString(testItem.getTubeCode()));
                applySampleItem.setGroupId(testItem.getGroupId());
                applySampleItem.setGroupName(testItem.getGroupName());
                applySampleItem.setUrgent(urgent);
                applySampleItem.setCount(count);
                applySampleItem.setRemark(remark);
                applySampleItem.setSplitCode(customCode);
                applySampleItem.setCreateDate(now);
                applySampleItem.setUpdateDate(now);
                applySampleItem.setCreatorId(user.getUserId());
                applySampleItem.setCreatorName(user.getNickname());
                applySampleItem.setUpdaterId(user.getUserId());
                applySampleItem.setUpdaterName(user.getNickname());
                applySampleItem.setIsDelete(YesOrNoEnum.NO.getCode());

                applySampleItem
                        .setIsOutsourcing(ObjectUtils.defaultIfNull(testItem.getEnableExport(), YesOrNoEnum.NO.getCode()));
                applySampleItem.setExportOrgId(ObjectUtils.defaultIfNull(testItem.getExportOrgId(), NumberUtils.LONG_ZERO));
                applySampleItem.setExportOrgName(ObjectUtils.defaultIfNull(testItem.getExportOrgName(), StringUtils.EMPTY));

                testItem.setEnableFee(ObjectUtils.defaultIfNull(testItem.getEnableFee(), YesOrNoEnum.NO.getCode()));
                if (Objects.equals(testItem.getEnableFee(), YesOrNoEnum.YES.getCode())) {
                    applySampleItem.setFeePrice(testItem.getFeePrice());
                } else {
                    applySampleItem.setFeePrice(BigDecimal.ZERO);
                }
                applySampleItem.setActualFeePrice(actualFeePriceByTestItemId
                        .getOrDefault(applySampleItem.getTestItemId(), applySampleItem.getFeePrice()));
                applySampleItem.setIsFree(YesOrNoEnum.NO.getCode());
                applySampleItem.setItemSource(ApplySampleItemSourceEnum.DEFAULT_SOURCE.getCode());

                applySampleItem.setOutTestItemId(NumberUtils.LONG_ZERO);
                applySampleItem.setOutTestItemName(Strings.EMPTY);
                applySampleItem.setOutTestItemCode(Strings.EMPTY);

                return applySampleItem;
            }).collect(Collectors.toList());
        }
    }

    /**
     * 获取条码
     * @param useOutBarcode 是否使用外部条码
     * @param apply 申请单信息
     * @param size 获取多少个
     * @return 条码list
     */
    public LinkedList<String> getBarcodeList(Boolean useOutBarcode, String outBarcode, ApplyDto apply, int size) {
        final LinkedList<String> barcodes = new LinkedList<>();

        // 需要使用外部条码
        if (BooleanUtils.isTrue(useOutBarcode)) {
            barcodes.add(outBarcode);
            --size;
        }
        if (size < 1) {
            return barcodes;
        }
        final List<String> barcodesBySettingList = barcodeSettingService.genBarcodes(apply.getHspOrgCode(), size);
        if (CollectionUtils.isEmpty(barcodesBySettingList) && size >= NumberUtils.INTEGER_ONE) {
            barcodesBySettingList.addAll(barcodeUtils.genBarcode(size));
        }
        barcodes.addAll(barcodesBySettingList);
        return barcodes;
    }
}
