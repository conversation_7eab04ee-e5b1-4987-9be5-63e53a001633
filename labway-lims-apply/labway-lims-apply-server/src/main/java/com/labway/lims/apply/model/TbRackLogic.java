package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 逻辑试管架
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Setter
@Getter
@TableName("tb_rack_logic")
public class TbRackLogic implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    private Long rackLogicId;

    /**
     * 试管架ID
     */
    private Long rackId;

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 多少行
     */
    @TableField("\"row\"")
    private Integer row;

    /**
     * 多少列
     */
    @TableField("\"column\"")
    private Integer column;

    /**
     * 所在环节或位置。
     * @see RackLogicPositionEnum
     */
    @TableField("\"position\"")
    private Integer position;

    /**
     * 当前专业组
     */
    private Long currentGroupId;

    /**
     * 当前专业组
     */
    private String currentGroupName;

    /**
     * 下一个专业组
     */
    private Long nextGroupId;

    /**
     * 下一个专业组
     */
    private String nextGroupName;

    /**
     * 最后一个交接人
     */
    private String lastHandover;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 更新时间
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 被回收时删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

}
