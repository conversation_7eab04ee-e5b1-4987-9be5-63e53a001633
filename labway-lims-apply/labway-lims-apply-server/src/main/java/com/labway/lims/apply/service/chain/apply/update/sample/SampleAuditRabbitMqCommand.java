package com.labway.lims.apply.service.chain.apply.update.sample;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.dto.UpdateTestApplySampleDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 发送审核消息到mq，报告平台重新同步报告
 */
@Slf4j
@Component
class SampleAuditRabbitMqCommand implements Command {

    @DubboReference
    private RabbitMQService rabbitMQService;

    private static final String ROUTING_KEY = "sample_change_key";

    @Override
    public boolean execute(Context c) throws Exception {
        final UpdateApplyContext from = UpdateApplyContext.from(c);
        final TestApplyDto testApply = from.getTestApply();

        // 样本信息查询 - 修改信息 判断请求来源
        if (!(testApply instanceof UpdateTestApplySampleDto)) {
            return CONTINUE_PROCESSING;
        }

        ApplySampleDto applySampleDto = from.getUpdateApplySample();

        final ApplySampleEventDto event = new ApplySampleEventDto();
        event.setOrgId(LoginUserHandler.get().getOrgId());
        event.setHspOrgId(from.getApply().getHspOrgId());
        event.setHspOrgCode(from.getApply().getHspOrgCode());
        event.setHspOrgName(from.getApply().getHspOrgName());
        event.setApplyId(applySampleDto.getApplyId());
        event.setApplySampleId(applySampleDto.getApplySampleId());
        event.setBarcode(applySampleDto.getBarcode());
        // event.setExtras(Map.of("sampleId", String.valueOf(sample.getSampleId()),"sampleNo", String.valueOf(sample.getSampleNo())));

        if (Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            event.setEvent(ApplySampleEventDto.EventType.TwoCheck);

            final String json = JSON.toJSONString(event);
            rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

            log.info("样本信息查询-修改信息 样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功",
                    applySampleDto.getApplySampleId(), applySampleDto.getBarcode(), json, RabbitMQService.EXCHANGE, ROUTING_KEY);
        }

        return CONTINUE_PROCESSING;
    }
}
