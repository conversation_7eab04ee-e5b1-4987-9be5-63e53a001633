package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.api.service.MaterialDeliveryDetailService;
import com.labway.lims.apply.api.service.MaterialDeliveryRecordService;
import com.labway.lims.apply.mapper.TbMaterialDeliveryDetailMapper;
import com.labway.lims.apply.mapstruct.MaterialDeliveryConverter;
import com.labway.lims.apply.model.TbMaterialDeliveryDetail;
import com.labway.lims.base.api.service.MaterialService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 物料出库详情 Service impl
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Slf4j
@DubboService
public class MaterialDeliveryDetailServiceImpl extends ServiceImpl<TbMaterialDeliveryDetailMapper, TbMaterialDeliveryDetail> implements MaterialDeliveryDetailService {

    @Resource
    private TbMaterialDeliveryDetailMapper tbMaterialDeliveryDetailMapper;
    @Resource
    private MaterialDeliveryRecordService materialDeliveryRecordService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private MaterialDeliveryConverter materialDeliveryConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMaterialDeliveryDetails(List<MaterialDeliveryDetailDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 要添加的 出库记录
        List<TbMaterialDeliveryDetail> targetList =
            materialDeliveryConverter.deliveryDetailFromMaterialDeliveryDetailDtoList(list);

        // 数量 分区批次插入
        List<List<TbMaterialDeliveryDetail>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbMaterialDeliveryDetailMapper.batchAddMaterialDeliveryDetails(item));

        log.info("新增出库记录[{}]成功", JSON.toJSONString(targetList));
    }

    @Override
    public List<MaterialDeliveryDetailDto> selectByDeliveryNo(String deliveryNo, long orgId) {
        if (StringUtils.isBlank(deliveryNo)) {
            return Collections.emptyList();
        }
        return selectByDeliveryNos(List.of(deliveryNo),orgId);
    }

    @Override
    public List<MaterialDeliveryDetailDto> selectByDeliveryNos(Collection<String> deliveryNos, long orgId) {

        if (CollectionUtils.isEmpty(deliveryNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMaterialDeliveryDetail> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMaterialDeliveryDetail::getDeliveryNo, deliveryNos);
        queryWrapper.eq(TbMaterialDeliveryDetail::getOrgId, orgId);

        return materialDeliveryConverter
                .fromTbMaterialDeliveryDetailList(tbMaterialDeliveryDetailMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteBydetailIds(Collection<Long> detailIds) {
        if (CollectionUtils.isEmpty(detailIds)) {
            return;
        }
        TbMaterialDeliveryDetail tbMaterialDeliveryDetail = tbMaterialDeliveryDetailMapper.selectBatchIds(detailIds).stream().findFirst().orElse(null);
        if(Objects.isNull(tbMaterialDeliveryDetail)){
            return;
        }
        tbMaterialDeliveryDetailMapper.deleteBatchIds(detailIds);


        List<MaterialDeliveryDetailDto> list = this.selectByDeliveryNo(tbMaterialDeliveryDetail.getDeliveryNo(), LoginUserHandler.get().getOrgId());
        if(CollectionUtils.isEmpty(list)) {
            materialDeliveryRecordService.deleteByDeliveryNo(tbMaterialDeliveryDetail.getDeliveryNo());
        }
    }
}
