package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.model.ApplySampleImage;
import com.labway.lims.apply.model.TbApplyLogistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 申请单样本图片表(ApplySampleImage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-04 11:14:13
 */
@Mapper
public interface ApplySampleImageMapper extends BaseMapper<ApplySampleImage> {

    /**
     * 通过ID查询单条数据
     *
     * @param applySampleImageId 主键
     * @return 实例对象
     */
    ApplySampleImage queryById(Long applySampleImageId);

    /**
     * 查询指定行数据
     *
     * @param applySampleImage 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<ApplySampleImage> queryAllByLimit(ApplySampleImage applySampleImage, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param applySampleImage 查询条件
     * @return 总行数
     */
    long count(ApplySampleImage applySampleImage);

    /**
     * 新增数据
     *
     * @param applySampleImage 实例对象
     * @return 影响行数
     */
    int insert(ApplySampleImage applySampleImage);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ApplySampleImage> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ApplySampleImage> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ApplySampleImage> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ApplySampleImage> entities);

    /**
     * 修改数据
     *
     * @param applySampleImage 实例对象
     * @return 影响行数
     */
    int update(ApplySampleImage applySampleImage);


}

