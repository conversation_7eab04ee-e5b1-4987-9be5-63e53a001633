package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 检验项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_apply_logistics_sample_item")
public class TbApplyLogisticsSampleItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @TableId
    private Long applyLogisticsApplyItemId;

    /**
     * 物流样本ID
     */
    private Long applyLogisticsSampleId;

    /**
     * 物流申请单ID
     */
    private Long applyLogisticsId;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 1:删除 0 ：未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

}
