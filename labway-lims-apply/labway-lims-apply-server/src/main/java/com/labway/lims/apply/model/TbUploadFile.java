package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName tb_upload_file
 */
@TableName(value ="tb_upload_file")
public class TbUploadFile implements Serializable {
    /**
     * 文件编码
     */
    @TableId
    private Long fileId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 文件类型
     */
    private Integer type;

    /**
     * 文件状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 文件详情
     */
    private String info;

    /**
     * 创建人姓名
     */
    private String uploaderName;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 文件类型描述
     */
    private String typeDesc;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 文件来源
     */
    private String resource;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 文件编码
     */
    public Long getFileId() {
        return fileId;
    }

    /**
     * 文件编码
     */
    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    /**
     * 文件名称
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 文件名称
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 文件地址
     */
    public String getFileUrl() {
        return fileUrl;
    }

    /**
     * 文件地址
     */
    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    /**
     * 文件类型
     */
    public Integer getType() {
        return type;
    }

    /**
     * 文件类型
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 文件状态
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 文件状态
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 文件详情
     */
    public String getInfo() {
        return info;
    }

    /**
     * 文件详情
     */
    public void setInfo(String info) {
        this.info = info;
    }

    /**
     * 创建人姓名
     */
    public String getUploaderName() {
        return uploaderName;
    }

    /**
     * 创建人姓名
     */
    public void setUploaderName(String uploaderName) {
        this.uploaderName = uploaderName;
    }

    /**
     * 处理时间
     */
    public Date getHandleTime() {
        return handleTime;
    }

    /**
     * 处理时间
     */
    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 文件类型描述
     */
    public String getTypeDesc() {
        return typeDesc;
    }

    /**
     * 文件类型描述
     */
    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }

    /**
     * 送检机构编码
     */
    public String getHspOrgCode() {
        return hspOrgCode;
    }

    /**
     * 送检机构编码
     */
    public void setHspOrgCode(String hspOrgCode) {
        this.hspOrgCode = hspOrgCode;
    }

    /**
     * 送检机构名称
     */
    public String getHspOrgName() {
        return hspOrgName;
    }

    /**
     * 送检机构名称
     */
    public void setHspOrgName(String hspOrgName) {
        this.hspOrgName = hspOrgName;
    }

    /**
     * 文件来源
     */
    public String getResource() {
        return resource;
    }

    /**
     * 文件来源
     */
    public void setResource(String resource) {
        this.resource = resource;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TbUploadFile other = (TbUploadFile) that;
        return (this.getFileId() == null ? other.getFileId() == null : this.getFileId().equals(other.getFileId()))
            && (this.getFileName() == null ? other.getFileName() == null : this.getFileName().equals(other.getFileName()))
            && (this.getFileUrl() == null ? other.getFileUrl() == null : this.getFileUrl().equals(other.getFileUrl()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getInfo() == null ? other.getInfo() == null : this.getInfo().equals(other.getInfo()))
            && (this.getUploaderName() == null ? other.getUploaderName() == null : this.getUploaderName().equals(other.getUploaderName()))
            && (this.getHandleTime() == null ? other.getHandleTime() == null : this.getHandleTime().equals(other.getHandleTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getTypeDesc() == null ? other.getTypeDesc() == null : this.getTypeDesc().equals(other.getTypeDesc()))
            && (this.getHspOrgCode() == null ? other.getHspOrgCode() == null : this.getHspOrgCode().equals(other.getHspOrgCode()))
            && (this.getHspOrgName() == null ? other.getHspOrgName() == null : this.getHspOrgName().equals(other.getHspOrgName()))
            && (this.getResource() == null ? other.getResource() == null : this.getResource().equals(other.getResource()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getFileId() == null) ? 0 : getFileId().hashCode());
        result = prime * result + ((getFileName() == null) ? 0 : getFileName().hashCode());
        result = prime * result + ((getFileUrl() == null) ? 0 : getFileUrl().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getInfo() == null) ? 0 : getInfo().hashCode());
        result = prime * result + ((getUploaderName() == null) ? 0 : getUploaderName().hashCode());
        result = prime * result + ((getHandleTime() == null) ? 0 : getHandleTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getTypeDesc() == null) ? 0 : getTypeDesc().hashCode());
        result = prime * result + ((getHspOrgCode() == null) ? 0 : getHspOrgCode().hashCode());
        result = prime * result + ((getHspOrgName() == null) ? 0 : getHspOrgName().hashCode());
        result = prime * result + ((getResource() == null) ? 0 : getResource().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fileId=").append(fileId);
        sb.append(", fileName=").append(fileName);
        sb.append(", fileUrl=").append(fileUrl);
        sb.append(", type=").append(type);
        sb.append(", status=").append(status);
        sb.append(", remark=").append(remark);
        sb.append(", info=").append(info);
        sb.append(", uploaderName=").append(uploaderName);
        sb.append(", handleTime=").append(handleTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", typeDesc=").append(typeDesc);
        sb.append(", hspOrgCode=").append(hspOrgCode);
        sb.append(", hspOrgName=").append(hspOrgName);
        sb.append(", resource=").append(resource);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}