package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.apply.api.dto.ApplyLogisticsSampleDto;
import com.labway.lims.apply.api.dto.HspOrgDateQueryDto;
import com.labway.lims.apply.api.dto.LogisticsApplyDto;
import com.labway.lims.apply.api.dto.SimpleLogisticsSampleDto;
import com.labway.lims.apply.api.service.ApplyLogisticsSampleService;
import com.labway.lims.apply.mapper.TbApplyLogisticsSampleMapper;
import com.labway.lims.apply.model.TbApplyLogisticsSample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@DubboService
public class ApplyLogisticsSampleServiceImpl implements ApplyLogisticsSampleService {
    @Resource
    private TbApplyLogisticsSampleMapper tbApplyLogisticsSampleMapper;

    @Override
    public List<ApplyLogisticsSampleDto> selectByApplyLogisticsId(long applyLogisticsId) {
        final LambdaQueryWrapper<TbApplyLogisticsSample> wrapper = Wrappers.lambdaQuery(TbApplyLogisticsSample.class)
                .eq(TbApplyLogisticsSample::getApplyLogisticsId, applyLogisticsId);

        return tbApplyLogisticsSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    public ApplyLogisticsSampleDto selectById(long applyLogisticsSampleId) {
        return convert(tbApplyLogisticsSampleMapper.selectById(applyLogisticsSampleId));
    }

    @Override
    public List<SimpleLogisticsSampleDto> selectApplyLogisticsDetail(HspOrgDateQueryDto dto) {

        if (Objects.isNull(dto.getHspOrgId()) || Objects.isNull(dto.getStartDate()) || Objects.isNull(dto.getEndDate()) || CollectionUtils.isEmpty(dto.getStatusList())) {
            return Collections.emptyList();
        }
        return tbApplyLogisticsSampleMapper.selectApplyLogisticsDetail(dto);
    }

    @Override
    public List<ApplyLogisticsSampleDto> selectByApplyIds(Collection<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Collections.emptyList();
        }
        return tbApplyLogisticsSampleMapper.selectList(Wrappers.lambdaQuery(TbApplyLogisticsSample.class)
                        .in(TbApplyLogisticsSample::getApplyId, applyIds)).stream().map(this::convert)
                .collect(Collectors.toList());
    }

    @Nullable
    @Override
    public ApplyLogisticsSampleDto selectByApplyId(long applyId) {
        final List<ApplyLogisticsSampleDto> list = selectByApplyIds(List.of(applyId));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.iterator().next();
    }

    @Override
    public void updateStatusByApplyId(Long applyId, Integer status) {
        updateStatusByApplyIds(List.of(applyId), status);
    }

    @Override
    public List<LogisticsApplyDto> selectSupplementList(HspOrgDateQueryDto dto) {
        dto.setStatusList(List.of(ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode(), ApplyStatusEnum.WAIT_CHECK.getCode()));
        return tbApplyLogisticsSampleMapper.selectSupplementList(dto);
    }

    @Override
    public void updateById(ApplyLogisticsSampleDto update) {
        if (Objects.isNull(update.getApplyLogisticsSampleId())) {
            return;
        }
        tbApplyLogisticsSampleMapper.updateById(convert(update));
    }

    @Override
    public void updateStatusByApplyIds(List<Long> applyIds, Integer status) {
        if (Objects.isNull(status) || CollectionUtils.isEmpty(applyIds)) {
            return;
        }
        TbApplyLogisticsSample tb = new TbApplyLogisticsSample();
        tb.setStatus(status);
        Optional.ofNullable(LoginUserHandler.getUnsafe()).ifPresent(c -> {
            tb.setUpdaterId(c.getUserId());
            tb.setUpdateDate(new Date());
            tb.setUpdaterName(c.getNickname());
        });

        tbApplyLogisticsSampleMapper.update(tb, Wrappers.lambdaQuery(TbApplyLogisticsSample.class).in(TbApplyLogisticsSample::getApplyId, applyIds));

    }

    @Override
    public void add(ApplyLogisticsSampleDto applyLogisticsSample) {
        final TbApplyLogisticsSample object = JSON.parseObject(JSON.toJSONString(applyLogisticsSample), TbApplyLogisticsSample.class);
        tbApplyLogisticsSampleMapper.insert(object);
    }

    @Override
    public void updateStatusById(long applyLogisticsSampleId, int code) {
        final TbApplyLogisticsSample tb = new TbApplyLogisticsSample();
        tb.setApplyLogisticsSampleId(applyLogisticsSampleId);
        tb.setStatus(code);
        tbApplyLogisticsSampleMapper.updateById(tb);
    }


    /**
     * 将 TbApplyLogisticsSample 转换为 ApplyLogisticsSampleDto
     */
    private ApplyLogisticsSampleDto convert(TbApplyLogisticsSample tb) {
        return JSON.parseObject(JSON.toJSONString(tb), ApplyLogisticsSampleDto.class);
    }

    /**
     * 将 ApplyLogisticsSampleDto 转换为 TbApplyLogisticsSample
     */
    private TbApplyLogisticsSample convert(ApplyLogisticsSampleDto dto) {
        return JSON.parseObject(JSON.toJSONString(dto), TbApplyLogisticsSample.class);
    }
}
