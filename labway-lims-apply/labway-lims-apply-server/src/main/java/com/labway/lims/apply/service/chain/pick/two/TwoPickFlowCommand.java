package com.labway.lims.apply.service.chain.pick.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.stream.Collectors;

/**
 * 条码环节
 */
@Slf4j
@Component
public class TwoPickFlowCommand implements Command {
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        final TwoPickContext context = TwoPickContext.from(c);

        final LinkedList<Long> ids = snowflakeService.genIds(context.getApplySampleTwoPicks().size());
        sampleFlowService.addSampleFlows(context.getApplySampleTwoPicks()
                .stream().map(e -> {
                    final SampleFlowDto sampleFlow = new SampleFlowDto();
                    sampleFlow.setSampleFlowId(ids.pop());
                    sampleFlow.setApplyId(context.getApplySample().getApplyId());
                    sampleFlow.setApplySampleId(e.getApplySampleId());
                    sampleFlow.setBarcode(context.getApplySample().getBarcode());
                    sampleFlow.setOperateCode(BarcodeFlowEnum.TWO_PICK.name());
                    sampleFlow.setOperateName(BarcodeFlowEnum.TWO_PICK.getDesc());
                    sampleFlow.setOperator(LoginUserHandler.get().getNickname());
                    sampleFlow.setOperatorId(LoginUserHandler.get().getUserId());
                    sampleFlow.setContent(String.format("专业小组 [%s] 样本号 [%s]", e.getInstrumentGroupName(),
                            e.getSampleNo()));
                    return sampleFlow;
                }).collect(Collectors.toList()));


        return CONTINUE_PROCESSING;
    }

}
