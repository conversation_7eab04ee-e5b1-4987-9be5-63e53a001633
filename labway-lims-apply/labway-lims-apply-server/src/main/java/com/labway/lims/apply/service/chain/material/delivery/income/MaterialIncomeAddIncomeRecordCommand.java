package com.labway.lims.apply.service.chain.material.delivery.income;

import com.google.common.collect.Lists;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryIncomeItemDto;
import com.labway.lims.apply.api.dto.MaterialIncomeRecordDto;
import com.labway.lims.apply.api.service.MaterialIncomeRecordService;
import com.labway.lims.apply.mapstruct.MaterialDeliveryConverter;
import com.labway.lims.apply.vo.utils.MaterialNoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料 入库 添加入库记录
 *
 * <AUTHOR>
 * @since 2023/5/9 10:47
 */
@Slf4j
@Component
public class MaterialIncomeAddIncomeRecordCommand implements Command {

    @Resource
    private MaterialIncomeRecordService materialIncomeRecordService;
    @Resource
    private MaterialDeliveryConverter materialDeliveryConverter;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private MaterialNoUtils materialNoUtils;

    @Override
    public boolean execute(Context context) throws Exception {
        final MaterialIncomeContext from = MaterialIncomeContext.from(context);
        var user = from.getUser();
        var incomeItemList = from.getIncomeItemList();
        var materialDeliveryDetails = from.getMaterialDeliveryDetails();

        // key: 入库物料信息id value: 入库物料信息
        final Map<Long, MaterialDeliveryIncomeItemDto> incomeItemByDetailId = incomeItemList.stream()
            .collect(Collectors.toMap(MaterialDeliveryIncomeItemDto::getDetailId, Function.identity()));

        // 构造 入库记录
        List<MaterialIncomeRecordDto> targetList = Lists.newArrayListWithCapacity(materialDeliveryDetails.size());

        // 入库记录id
        LinkedList<Long> incomeIds = snowflakeService.genIds(materialDeliveryDetails.size());

        // 入库单号
        String incomeNo = materialNoUtils.genIncomeNo(user.getOrgId());

        Date date = new Date();
        for (MaterialDeliveryDetailDto deliveryDetail : materialDeliveryDetails) {
            MaterialIncomeRecordDto target =
                materialDeliveryConverter.incomeRecordFromMaterialDeliveryDetailDto(deliveryDetail);

            // 入库信息
            MaterialDeliveryIncomeItemDto incomeItemDto = incomeItemByDetailId.get(deliveryDetail.getDetailId());

            target.setIncomeId(incomeIds.pop());
            target.setIncomeNo(incomeNo);

            target.setIncomeMainNumber(BigDecimal.ZERO);
            target.setIncomeAssistNumber(BigDecimal.ZERO);

            //入库记录新加字段
            target.setIfStorageQualified(YesOrNoEnum.YES.getCode());
            target.setIfSpecQuantityConsistent(YesOrNoEnum.YES.getCode());
            target.setIfPackageDamaged(YesOrNoEnum.NO.getCode());
            target.setIfValidDateQualified(YesOrNoEnum.YES.getCode());
            target.setAcceptanceConclusion(YesOrNoEnum.YES.getCode());

            if (Objects.nonNull(incomeItemDto)) {
                // 传入 了入库数量 以传入为主
                target.setIncomeMainNumber(incomeItemDto.getIncomeMainNumber());
                target.setIncomeAssistNumber(incomeItemDto.getIncomeAssistNumber());

                //以传入为主
                if(!Objects.isNull(incomeItemDto.getIfStorageQualified())){
                    target.setIfStorageQualified(incomeItemDto.getIfStorageQualified());
                }
                if(!Objects.isNull(incomeItemDto.getIfSpecQuantityConsistent())){
                    target.setIfSpecQuantityConsistent(incomeItemDto.getIfSpecQuantityConsistent());
                }
                if(!Objects.isNull(incomeItemDto.getIfPackageDamaged())){
                    target.setIfPackageDamaged(incomeItemDto.getIfPackageDamaged());
                }
                if(!Objects.isNull(incomeItemDto.getIfValidDateQualified())){
                    target.setIfValidDateQualified(incomeItemDto.getIfValidDateQualified());
                }
                if(!Objects.isNull(incomeItemDto.getAcceptanceConclusion())){
                    target.setAcceptanceConclusion(incomeItemDto.getAcceptanceConclusion());
                }

            }

            target.setGroupId(user.getGroupId());
            target.setGroupName(user.getGroupName());
            target.setOrgId(user.getOrgId());
            target.setOrgName(user.getOrgName());
            // 此时间为入库时间
            target.setCreateDate(date);
            target.setUpdateDate(date);
            target.setCreatorId(user.getUserId());
            target.setCreatorName(user.getNickname());
            target.setUpdaterId(user.getUserId());
            target.setUpdaterName(user.getNickname());
            target.setIsDelete(YesOrNoEnum.NO.getCode());

            targetList.add(target);
        }

        materialIncomeRecordService.addMaterialIncomeRecords(targetList);

        return CONTINUE_PROCESSING;
    }
}
