package com.labway.lims.apply.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 样本 导入
 * 
 * <AUTHOR>
 * @since 2024/9/04 11:08
 */
@Getter
@Setter
public class ImportSampleExcelVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final String OUT_BARCODE_COLUMN = "外部条码号";
    private static final String OUT_TEST_CODE_COLUMN = "外部项目编码";
    private static final String OUT_TEST_NAME_COLUMN = "外部项目名称";
    private static final String SAMPLE_TYPE_COLUMN = "样本类型";
    private static final String PATIENT_NAME_COLUMN = "姓名";
    private static final String PATIENT_SEX_COLUMN = "性别";
    private static final String PATIENT_AGE_COLUMN = "年龄";
    private static final String PATIENT_VISIT_CARD_COLUMN = "门诊/住院号";
    private static final String DEPT_COLUMN = "科室";
    private static final String PATIENT_BED_COLUMN = "床号";
    private static final String CLINICAL_DIAGNOSIS_COLUMN = "临床诊断";
    private static final String SEND_DOCTOR_COLUMN = "送检医生";
    private static final String APPLY_TIME_COLUMN = "申请时间";
    private static final String SAMPLE_TIME_COLUMN = "采样时间";
    private static final String PATIENT_MOBILE_COLUMN = "联系电话";
    private static final String PATIENT_CARD_COLUMN = "身份证号";
    private static final String REMARK_COLUMN = "备注";

    /**
     * 外部条码号
     */
    @ExcelProperty(value = OUT_BARCODE_COLUMN, index = 0)
    private String outBarcode;
    /**
     * 外部项目编码
     */
    @ExcelProperty(value = OUT_TEST_CODE_COLUMN, index = 1)
    private String outTestCode;
    /**
     * 外部项目名称
     */
    @ExcelProperty(value = OUT_TEST_NAME_COLUMN, index = 2)
    private String outTestName;
    /**
     * 样本类型
     */
    @ExcelProperty(value = SAMPLE_TYPE_COLUMN, index = 3)
    private String sampleType;
    /**
     * 名称
     */
    @ExcelProperty(value = PATIENT_NAME_COLUMN, index = 4)
    private String patientName;
    /**
     * 性别
     */
    @ExcelProperty(value = PATIENT_SEX_COLUMN, index = 5)
    private String patientSex;
    /**
     * 年龄
     */
    @ExcelProperty(value = PATIENT_AGE_COLUMN, index = 6)
    private String patientAge;
    /**
     * 门诊/住院号
     */
    @ExcelProperty(value = PATIENT_VISIT_CARD_COLUMN, index = 7)
    private String patientVisitCard;
    /**
     * 科室
     */
    @ExcelProperty(value = DEPT_COLUMN, index = 8)
    private String dept;
    /**
     * 床号
     */
    @ExcelProperty(value = PATIENT_BED_COLUMN, index = 9)
    private String patientBed;
    /**
     * 临床诊断
     */
    @ExcelProperty(value = CLINICAL_DIAGNOSIS_COLUMN, index = 10)
    private String clinicalDiagnosis;
    /**
     * 送检医生
     */
    @ExcelProperty(value = SEND_DOCTOR_COLUMN, index = 11)
    private String sendDoctor;
    /**
     * 申请时间
     */
    @ExcelProperty(value = APPLY_TIME_COLUMN, index = 12)
    private String applyTime;
    /**
     * 采样时间
     */
    @ExcelProperty(value = SAMPLE_TIME_COLUMN, index = 13)
    private String sampleTime;
    /**
     * 手机号
     */
    @ExcelProperty(value = PATIENT_MOBILE_COLUMN, index = 14)
    private String patientMobile;
    /**
     * 身份证号
     */
    @ExcelProperty(value = PATIENT_CARD_COLUMN, index = 15)
    private String patientCard;
    /**
     * 备注
     */
    @ExcelProperty(value = REMARK_COLUMN, index = 16)
    private String remark;




    public static List<String> getHeadList() {
        return Lists.newArrayList(OUT_BARCODE_COLUMN, OUT_TEST_CODE_COLUMN, OUT_TEST_NAME_COLUMN, SAMPLE_TYPE_COLUMN,PATIENT_NAME_COLUMN
                ,PATIENT_SEX_COLUMN,PATIENT_AGE_COLUMN,PATIENT_VISIT_CARD_COLUMN,DEPT_COLUMN,PATIENT_BED_COLUMN,CLINICAL_DIAGNOSIS_COLUMN
                ,SEND_DOCTOR_COLUMN,APPLY_TIME_COLUMN,SAMPLE_TIME_COLUMN,PATIENT_MOBILE_COLUMN,PATIENT_CARD_COLUMN,REMARK_COLUMN);
    }

    public ImportSampleExcelVo(Map<Integer, String> dataMap, Map<String, Integer> reverseHeadMap) {
        this.outBarcode = reverseHeadMap.containsKey(OUT_BARCODE_COLUMN)
                ? dataMap.get(reverseHeadMap.get(OUT_BARCODE_COLUMN)) : null;

        this.outTestCode = reverseHeadMap.containsKey(OUT_TEST_CODE_COLUMN)
                ? dataMap.get(reverseHeadMap.get(OUT_TEST_CODE_COLUMN)) : null;

        this.outTestName = reverseHeadMap.containsKey(OUT_TEST_NAME_COLUMN)
                ? dataMap.get(reverseHeadMap.get(OUT_TEST_NAME_COLUMN)) : null;

        this.sampleType = reverseHeadMap.containsKey(SAMPLE_TYPE_COLUMN)
                ? dataMap.get(reverseHeadMap.get(SAMPLE_TYPE_COLUMN)) : null;

        this.patientName = reverseHeadMap.containsKey(PATIENT_NAME_COLUMN)
            ? dataMap.get(reverseHeadMap.get(PATIENT_NAME_COLUMN)) : null;

        this.patientSex =
            reverseHeadMap.containsKey(PATIENT_SEX_COLUMN) ? dataMap.get(reverseHeadMap.get(PATIENT_SEX_COLUMN)) : null;

        this.patientAge =
            reverseHeadMap.containsKey(PATIENT_AGE_COLUMN) ? dataMap.get(reverseHeadMap.get(PATIENT_AGE_COLUMN)) : null;

        this.patientVisitCard = reverseHeadMap.containsKey(PATIENT_VISIT_CARD_COLUMN)
            ? dataMap.get(reverseHeadMap.get(PATIENT_VISIT_CARD_COLUMN)) : null;

        this.dept = reverseHeadMap.containsKey(DEPT_COLUMN) ? dataMap.get(reverseHeadMap.get(DEPT_COLUMN)) : null;

        this.patientBed =
                reverseHeadMap.containsKey(PATIENT_BED_COLUMN) ? dataMap.get(reverseHeadMap.get(PATIENT_BED_COLUMN)) : null;

        this.patientBed = reverseHeadMap.containsKey(PATIENT_BED_COLUMN)
                ? dataMap.get(reverseHeadMap.get(PATIENT_BED_COLUMN)) : null;

        this.clinicalDiagnosis = reverseHeadMap.containsKey(CLINICAL_DIAGNOSIS_COLUMN)
                ? dataMap.get(reverseHeadMap.get(CLINICAL_DIAGNOSIS_COLUMN)) : null;

        this.sendDoctor = reverseHeadMap.containsKey(SEND_DOCTOR_COLUMN)
                ? dataMap.get(reverseHeadMap.get(SEND_DOCTOR_COLUMN)) : null;

        this.applyTime = reverseHeadMap.containsKey(APPLY_TIME_COLUMN)
                ? dataMap.get(reverseHeadMap.get(APPLY_TIME_COLUMN)) : null;

        this.sampleTime = reverseHeadMap.containsKey(SAMPLE_TIME_COLUMN)
                ? dataMap.get(reverseHeadMap.get(SAMPLE_TIME_COLUMN)) : null;

        this.patientMobile = reverseHeadMap.containsKey(PATIENT_MOBILE_COLUMN)
            ? dataMap.get(reverseHeadMap.get(PATIENT_MOBILE_COLUMN)) : null;

        this.patientCard = reverseHeadMap.containsKey(PATIENT_CARD_COLUMN)
                ? dataMap.get(reverseHeadMap.get(PATIENT_CARD_COLUMN)) : null;

        this.remark = reverseHeadMap.containsKey(REMARK_COLUMN) ? dataMap.get(reverseHeadMap.get(REMARK_COLUMN)) : null;



    }

}
