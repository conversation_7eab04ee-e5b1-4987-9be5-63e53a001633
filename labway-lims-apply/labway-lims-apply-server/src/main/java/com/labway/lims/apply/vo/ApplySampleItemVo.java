package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 检验项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class ApplySampleItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long applySampleItemId;

    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 英文名称
     */
    private String enName;


    /**
     * 项目类型
     *
     * @see ItemTypeEnum
     */
    private String itemType;


    /**
     * 外部项目
     */
    private Long outTestItemId;

    /**
     * 外部项目编码
     */
    private String outTestItemCode;

    /**
     * 外部项目名称
     */
    private String outTestItemName;

    /**
     * 管型
     */
    private String tubeName;
    /**
     * 管型CODE
     */
    private String tubeCode;

    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;
    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;
    /**
     * @see com.labway.lims.api.enums.apply.UrgentEnum
     */
    private Integer urgent;
    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 收费数量
     */
    private Integer count;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 1:已经删除 0:未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 自定义码 | 分割码
     */
    private String splitCode;

    /**
     * 是否免单
     * @see YesOrNoEnum
     */
    private Integer isFree;

    /**
     * 金额
     */
    private BigDecimal feePrice;

    /**
     * 终止检验状态：0正常，1终止收费，2终止不收费
     *
     * @see StopTestStatus
     */
    private Integer stopStatus;

    /**
     * 申请单样本项目是否禁用：1：禁用，0：正常
     */
    private Integer isDisabled;

}
