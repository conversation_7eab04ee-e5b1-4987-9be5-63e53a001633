package com.labway.lims.apply.service.chain.pick.one;

import com.labway.lims.apply.api.service.ApplySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 校验申请单样本状态
 */
@Slf4j
@Component
class OnePickCheckApplySampleStatusCommand implements Command {

    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OnePickContext context = OnePickContext.from(c);


        applySampleService.assertApplySampleUsability(context.getApplySampleId());


        return CONTINUE_PROCESSING;
    }

}
