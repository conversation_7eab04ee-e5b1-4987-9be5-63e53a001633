package com.labway.lims.apply.service.chain.apply.update;

import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.service.ApplySampleItemBloodCultureService;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import lombok.Getter;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 保存信息
 */
@Getter
@Component
public class UpdateSaveApplyCommand implements Command {
    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private ApplySampleItemBloodCultureService applySampleItemBloodCultureService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Override
    public boolean execute(Context c) throws Exception {

        UpdateApplyContext context = UpdateApplyContext.from(c);

        final List<ApplySampleDto> addApplySamples = context.getAddApplySamples();

        final List<ApplySampleItemDto> addApplySampleItems = context.getAddApplySampleItems();

        final List<ApplySampleItemDto> updateApplySampleItems = context.getUpdateApplySampleItems();

        // 新增样本
        if (CollectionUtils.isNotEmpty(addApplySamples)) {
            applySampleService.addApplySamples(addApplySamples);
        }

        // 新增样本检验项目
        if (CollectionUtils.isNotEmpty(addApplySampleItems)) {
            applySampleItemService.addApplySampleItems(addApplySampleItems);
        }

        // 修改检验项目
        if (CollectionUtils.isNotEmpty(updateApplySampleItems)) {
            applySampleItemService.updateBatchById(updateApplySampleItems);
        }

        // 新增血培养
        final ApplySampleItemBloodCultureDto bloodCulture = context.getBloodCulture();
        if (Objects.nonNull(bloodCulture)) {
            applySampleItemBloodCultureService.addApplySampleItemBloodCultures(Collections.singletonList(bloodCulture));
        }
        if (Objects.nonNull(context.getTestApply().getOutBarcode())) {
            applySampleService.updateOutBarcodeByApplyId(context.getApplyId(), context.getTestApply().getOutBarcode());
        }

        // 修改申请单
        final ApplyDto apply = context.getApply();
        if (Objects.nonNull(context.getApply())) {
            applyService.updateByApplyId(apply);

            // 同步送检机构&外部条码信息
            final TestApplyDto testApplyDto = context.getTestApply();
            ApplySampleDto applySample = new ApplySampleDto();
            applySample.setApplyId(apply.getApplyId());
            applySample.setHspOrgName(apply.getHspOrgName());
            applySample.setHspOrgCode(apply.getHspOrgCode());
            applySample.setOutBarcode(testApplyDto.getOutBarcode());
            applySample.setPatientPart(testApplyDto.getPatientPart());
            applySampleService.updateByApplyId(applySample);

            // 🐛fix：修复：加减项目时修改加急类型未修改样本加急类型
            List<ApplySampleItemDto> newSampleItems = Lists.newArrayList();
            newSampleItems.addAll(Objects.nonNull(addApplySampleItems) ? addApplySampleItems : Collections.emptyList());
            newSampleItems.addAll(Objects.nonNull(updateApplySampleItems) ? updateApplySampleItems : Collections.emptyList());

            if (CollectionUtils.isNotEmpty(newSampleItems)) {
                Map<Integer, Set<Long>> urgentApplySampleIdMap = newSampleItems.stream().collect(Collectors.groupingBy(ApplySampleItemDto::getUrgent, Collectors.mapping(ApplySampleItemDto::getApplySampleId, Collectors.toSet())));
                Set<Long> normalApplySampleIds = urgentApplySampleIdMap.getOrDefault(UrgentEnum.NORMAL.getCode(), Collections.emptySet());
                Set<Long> urgentApplySampleIds = urgentApplySampleIdMap.getOrDefault(UrgentEnum.URGENT.getCode(), Collections.emptySet());
                if (CollectionUtils.isNotEmpty(normalApplySampleIds) && CollectionUtils.isNotEmpty(urgentApplySampleIds)) {
                    normalApplySampleIds.removeAll(urgentApplySampleIds);
                }
                if (CollectionUtils.isNotEmpty(urgentApplySampleIds)) {
                    applySampleService.update2UrgentByApplySampleIds(urgentApplySampleIds, UrgentEnum.URGENT);
                }
                if (CollectionUtils.isNotEmpty(normalApplySampleIds)) {
                    applySampleService.update2UrgentByApplySampleIds(normalApplySampleIds, UrgentEnum.NORMAL);
                }
            }
        }

        // 数据增加完毕后, 异步删除旧数据
        asyncDeleteApplySampleAndItems(context);

        return CONTINUE_PROCESSING;
    }

    // 数据增加完毕后, 异步删除旧数据
    private void asyncDeleteApplySampleAndItems(UpdateApplyContext context) {
        final LoginUserHandler.User user = context.getUser();

        threadPoolConfig.getPool().submit(() -> {
            LoginUserHandler.set(user);
            try {

                final Set<Long> deleteApplySampleItemIdSet = context.getDeleteApplySampleItemIdList();

                final Set<Long> deleteApplySampleIdSet = context.getDeleteApplySampleIds();
                // 删除旧的数据
                if (CollectionUtils.isNotEmpty(deleteApplySampleItemIdSet)) {
                    applySampleItemService.deleteByApplySampleItemIds(deleteApplySampleItemIdSet);
                }

                // 将旧条码以及条码下的项目全都删除
                if (CollectionUtils.isNotEmpty(deleteApplySampleIdSet)) {
                    applySampleService.deleteByApplySampleIds(deleteApplySampleIdSet);
                    applySampleItemService.deleteByApplySampleIds(deleteApplySampleIdSet);
                }
            } finally {
                LoginUserHandler.remove();
            }
        });
    }
}