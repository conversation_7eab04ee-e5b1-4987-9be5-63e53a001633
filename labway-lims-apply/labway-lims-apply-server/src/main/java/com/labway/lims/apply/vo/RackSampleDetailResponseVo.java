package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 试管架 样本详情
 *
 * <AUTHOR>
 * @since 2023/4/6 17:58
 */
@Getter
@Setter
public class RackSampleDetailResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    // --------------------------TbApplySample--------------

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 病人名称
     */
    private String patientName;
    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 性别
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    // --------------------------TbApplySample--------------
    /**
     * 申请单样本信息id
     */
    private Long applySampleId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 管型
     */
    private String tubeName;

    /**
     * 样本类型
     */
    private String sampleType;

    /**
     * 行数
     */
    private Integer row;

    /**
     * 列数
     */
    private Integer column;

}
