package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.MaterialInventoryCheckStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.MaterialInventoryCheckDetailDto;
import com.labway.lims.apply.api.dto.MaterialInventoryCheckDto;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.SelectMaterialInventoryCheckDto;
import com.labway.lims.apply.api.service.MaterialInventoryCheckDetailService;
import com.labway.lims.apply.api.service.MaterialInventoryCheckService;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import com.labway.lims.apply.mapper.TbMaterialInventoryCheckMapper;
import com.labway.lims.apply.mapstruct.MaterialInventoryCheckConverter;
import com.labway.lims.apply.model.TbMaterialInventoryCheck;
import com.labway.lims.apply.service.chain.material.inventory.start.MaterialInventoryStartCheckChain;
import com.labway.lims.apply.service.chain.material.inventory.start.MaterialInventoryStartCheckContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 物料盘点 Service impl
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Slf4j
@DubboService
public class MaterialInventoryCheckServiceImpl implements MaterialInventoryCheckService {
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private TbMaterialInventoryCheckMapper tbMaterialInventoryCheckMapper;

    @Resource
    private MaterialInventoryStartCheckChain materialInventoryStartCheckChain;

    @Resource
    private MaterialInventoryCheckConverter materialInventoryCheckConverter;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private MaterialInventoryCheckDetailService materialInventoryCheckDetailService;
    @Resource
    private MaterialInventoryService materialInventoryService;

    private static final String REDIS_KEY_PREFIX = "MATERIAL_INVENTORY:START_CHECK:";

    @Nullable
    @Override
    public MaterialInventoryCheckDto selectByCheckId(long checkId) {
        return materialInventoryCheckConverter
            .materialInventoryCheckDtoFromTbObj(tbMaterialInventoryCheckMapper.selectById(checkId));

    }

    @Override
    public List<MaterialInventoryCheckDto>
        selectBySelectMaterialInventoryCheckDto(SelectMaterialInventoryCheckDto dto) {
        LambdaQueryWrapper<TbMaterialInventoryCheck> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.ge(Objects.nonNull(dto.getBeginCheckTime()), TbMaterialInventoryCheck::getCheckTime,
            dto.getBeginCheckTime());
        queryWrapper.le(Objects.nonNull(dto.getEndCheckTime()), TbMaterialInventoryCheck::getCheckTime,
            dto.getEndCheckTime());

        queryWrapper.eq(TbMaterialInventoryCheck::getGroupId, dto.getGroupId());
        queryWrapper.eq(TbMaterialInventoryCheck::getIsDelete, YesOrNoEnum.NO.getCode());

        queryWrapper.eq(StringUtils.isNoneBlank(dto.getCheckNo()), TbMaterialInventoryCheck::getCheckNo,
            dto.getCheckNo());

        queryWrapper.orderByDesc(TbMaterialInventoryCheck::getCheckTime);

        return materialInventoryCheckConverter
            .materialInventoryCheckDtoListFromTbObjList(tbMaterialInventoryCheckMapper.selectList(queryWrapper));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startCheck() {
        LoginUserHandler.User user = LoginUserHandler.get();

        // 获取当前日期并转换为Date类型
        Date currentDate = new Date();

        String currentDateStr = DateFormatUtils.format(currentDate, "yyyy-MM-dd");

        final MaterialInventoryStartCheckContext context = new MaterialInventoryStartCheckContext();
        context.setCheckTime(currentDate);
        context.setUser(user);

        try {
            if (!materialInventoryStartCheckChain.execute(context)) {
                throw new IllegalStateException("开始盘点失败");
            }
        } catch (RuntimeException e) {
            log.error("开始盘点失败 [{}]", currentDateStr, e);
            throw e;
        } catch (Exception e) {
            log.error("开始盘点失败 [{}]", currentDateStr, e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("开始盘点 [{}] 耗时\n{}", currentDateStr, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelCheck(long checkId) {
        LoginUserHandler.User user = LoginUserHandler.get();

        // 更新 盘点状态 为已取消
        MaterialInventoryCheckDto update = new MaterialInventoryCheckDto();
        update.setCheckId(checkId);
        update.setStatus(MaterialInventoryCheckStatusEnum.CANCEL_CHECK.getCode());
        this.updateByCheckId(update);

        // 删除 当前专业组是否存在盘点中记录缓存
        this.removeIsCheckCache(user.getGroupId());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishCheck(long checkId, List<MaterialInventoryCheckDetailDto> updateCheckDetailList) {
        LoginUserHandler.User user = LoginUserHandler.get();

        // 更新 盘点状态 为已盘
        MaterialInventoryCheckDto update = new MaterialInventoryCheckDto();
        update.setCheckId(checkId);
        update.setStatus(MaterialInventoryCheckStatusEnum.FINISH_CHECK.getCode());
        this.updateByCheckId(update);

        // 更新盘点详情
        updateCheckDetailList.forEach(item -> materialInventoryCheckDetailService.updateByDetailId(item));

        // 更新对应库存
        updateCheckDetailList.forEach(item -> {
            MaterialInventoryDto inventoryDto = new MaterialInventoryDto();
            inventoryDto.setInventoryId(item.getInventoryId());
            inventoryDto.setMainUnitInventory(item.getActualMainInventory());
            inventoryDto.setAssistUnitInventory(item.getActualAssistInventory());
            materialInventoryService.updateByInventoryId(inventoryDto);
        });

        // 删除 当前专业组是否存在盘点中记录缓存
        this.removeIsCheckCache(user.getGroupId());
    }

    @Nullable
    @Override
    public String isCheck(long groupId) {

        String redisKey = String.format("%s%s%s", redisPrefix.getBasePrefix(), REDIS_KEY_PREFIX, groupId);

        // 缓存中存在 记录
        if (BooleanUtils.isTrue(stringRedisTemplate.hasKey(redisKey))) {
            return stringRedisTemplate.opsForValue().get(redisKey);
        }

        // 查询 数据中是否存在 处于盘点中数据
        LambdaQueryWrapper<TbMaterialInventoryCheck> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMaterialInventoryCheck::getStatus, MaterialInventoryCheckStatusEnum.IN_CHECK.getCode());
        queryWrapper.eq(TbMaterialInventoryCheck::getGroupId, groupId);
        queryWrapper.eq(TbMaterialInventoryCheck::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        MaterialInventoryCheckDto checkDto = materialInventoryCheckConverter
            .materialInventoryCheckDtoFromTbObj(tbMaterialInventoryCheckMapper.selectOne(queryWrapper));

        String checkTime = StringUtils.EMPTY;
        if (Objects.nonNull(checkDto)) {
            checkTime = DateFormatUtils.format(checkDto.getCheckTime(), "yyyy-MM-dd");
        }
        stringRedisTemplate.opsForValue().set(redisKey, checkTime, Duration.ofHours(1));
        return checkTime;
    }

    @Override
    public void removeIsCheckCache(long groupId) {
        String redisKey = String.format("%s%s%s", redisPrefix.getBasePrefix(), REDIS_KEY_PREFIX, groupId);
        stringRedisTemplate.delete(redisKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addMaterialInventoryCheck(MaterialInventoryCheckDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbMaterialInventoryCheck target = materialInventoryCheckConverter.tbMaterialInventoryCheckFromTbObjDto(dto);

        if (Objects.isNull(target.getCheckId())) {
            target.setCheckId(snowflakeService.genId());
        }

        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbMaterialInventoryCheckMapper.insert(target) < 1) {
            throw new LimsException("添加物料盘点失败");
        }

        log.info("用户 [{}] 新增物料盘点[{}]成功", loginUser.getNickname(), JSON.toJSONString(target));

        return target.getCheckId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByCheckId(MaterialInventoryCheckDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        TbMaterialInventoryCheck target = materialInventoryCheckConverter.tbMaterialInventoryCheckFromTbObjDto(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbMaterialInventoryCheckMapper.updateById(target) < 1) {
            throw new LimsException("修改物料盘点失败");
        }

        log.info("用户 [{}] 修改物料盘点成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }
}
