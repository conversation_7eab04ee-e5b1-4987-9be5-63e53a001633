package com.labway.lims.apply.service.chain.pick.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.RocheTwoPickDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.ReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 判断专业小组能否做这个样本
 */
@Slf4j
@Component
public class TwoPickCheckInstrumentGroupCandoCommand implements Command {
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private ReportItemService reportItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final TwoPickContext context = TwoPickContext.from(c);

        // roche 流水线不校验是否可以做
        if (context.getTwoPick() instanceof RocheTwoPickDto) {
            // nothing
        } else {
            checkCanDo(context);
        }


        final ApplySampleTwoPickDto stp = new ApplySampleTwoPickDto();
        stp.setApplySampleId(context.getApplySample().getApplySampleId());
        stp.setInstrumentGroupId(context.getInstrumentGroup().getInstrumentGroupId());
        stp.setInstrumentGroupName(context.getInstrumentGroup().getInstrumentGroupName());
        stp.setSampleNo(context.getTwoPick().getSampleNo());
        stp.setBarcode(context.getApplySample().getBarcode());
        stp.setGroupId(context.getApplySample().getGroupId());
        stp.setIsTransform(false);
        stp.setIsUrgent(false);
        context.getApplySampleTwoPicks().add(stp);


        return CONTINUE_PROCESSING;
    }


    private void checkCanDo(TwoPickContext context) {
		checkCanDo(context.getInstrumentGroup(),context.getApplySampleItems(), List.of());
    }

    /**
     * 判断专业小组能否做这个样本
     * @param instrumentGroup 仪器专业组
     * @param applySampleItems 申请单样本项目
     * @param reportItemDtos 报告项目
     */
    public void checkCanDo(InstrumentGroupDto instrumentGroup, List<ApplySampleItemDto> applySampleItems, List<ReportItemDto> reportItemDtos) {
        // 过滤当前专业组的检验项目
        final Set<Long> testItemIds = applySampleItems.stream()
                .filter(e -> Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()))
                .map(ApplySampleItemDto::getTestItemId)
                .collect(Collectors.toSet());

        // 专业小组下的报告项目
        final List<InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService
                .selectByInstrumentGroupId(instrumentGroup.getInstrumentGroupId())
                .stream()
                .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());

        // 检验项目下的报告项目
        final List<ReportItemDto> reportItems = CollectionUtils.isEmpty(reportItemDtos) ? reportItemService.selectByTestItemIds(testItemIds) : reportItemDtos;
        if (CollectionUtils.isEmpty(reportItems)) {
            throw new IllegalArgumentException("检验项目下没有报告项目");
        }

        // 没有绑定仪器报告项目 则抛出异常
        final List<ReportItemDto> noBindReportItems = reportItems.stream().filter(e -> instrumentReportItems.stream().noneMatch(l ->
                Objects.equals(l.getReportItemCode(), e.getReportItemCode()))).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(noBindReportItems)) {
            throw new IllegalStateException(String.format("专业小组 [%s] 没有维护报告项目 [%s]", instrumentGroup.getInstrumentGroupName(),
                    noBindReportItems.stream().map(e -> e.getReportItemName() + String.format("<%s>", e.getReportItemCode()))
                            .collect(Collectors.joining("、"))));
        }
    }



}
