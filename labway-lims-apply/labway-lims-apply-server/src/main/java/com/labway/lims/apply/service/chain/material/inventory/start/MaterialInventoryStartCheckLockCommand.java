package com.labway.lims.apply.service.chain.material.inventory.start;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.RedisPrefix;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 开始盘点上锁
 *
 * <AUTHOR>
 * @since 2023/5/11 19:13
 */
@Slf4j
@Component
public class MaterialInventoryStartCheckLockCommand implements Command, Filter {

    private static final String MARK = IdUtil.objectId();
    private static final String REDIS_KEY_PREFIX = "MATERIAL_INVENTORY:START_CHECK:";

    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context context) throws Exception {
        final MaterialInventoryStartCheckContext from = MaterialInventoryStartCheckContext.from(context);
        var user = from.getUser();

        String redisKey =
            String.format("%s%s%s:%s", redisPrefix.getBasePrefix(), REDIS_KEY_PREFIX, user.getGroupId(), MARK);

        if (BooleanUtils.isNotTrue(
            stringRedisTemplate.opsForValue().setIfAbsent(redisKey, StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalStateException("正在开始盘点");
        }

        from.put(MARK, StringUtils.EMPTY);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception e) {
        final MaterialInventoryStartCheckContext from = MaterialInventoryStartCheckContext.from(context);
        var user = from.getUser();

        if (from.containsKey(MARK)) {
            String redisKey =
                String.format("%s%s%s:%s", redisPrefix.getBasePrefix(), REDIS_KEY_PREFIX, user.getGroupId(), MARK);
            stringRedisTemplate.delete(redisKey);
        }
        return CONTINUE_PROCESSING;
    }
}
