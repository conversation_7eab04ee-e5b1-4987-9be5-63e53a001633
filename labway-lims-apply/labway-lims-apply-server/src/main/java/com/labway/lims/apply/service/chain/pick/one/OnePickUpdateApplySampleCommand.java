package com.labway.lims.apply.service.chain.pick.one;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 修改样本信息
 */
@Slf4j
@Component
class OnePickUpdateApplySampleCommand implements Command {
    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {

        final OnePickContext context = OnePickContext.from(c);
        final ApplySampleDto applySample = context.getApplySample();
        final RackLogicDto rackLogic = context.getRackLogic();
        final ProfessionalGroupDto group = context.getProfessionalGroup();

        final ApplySampleDto modifyApplySample = new ApplySampleDto();
        modifyApplySample.setApplySampleId(applySample.getApplySampleId());
        if (context.isOursourcing()) {
            // fix：如果样本包含非委外组 和 委外组项目，非委外组项目都被终止，外送分拣提示《当前样本不是外送，无法分拣》
            modifyApplySample.setIsOutsourcing(YesOrNoEnum.YES.getCode());
            modifyApplySample.setItemType(ItemTypeEnum.OUTSOURCING.name());
        }
        modifyApplySample.setIsOnePick(YesOrNoEnum.YES.getCode());
        modifyApplySample.setRackId(rackLogic.getRackId());
        modifyApplySample.setOnePickDate(new Date());
        modifyApplySample.setOnePickerName(LoginUserHandler.get().getNickname());
        modifyApplySample.setOnePickerId(LoginUserHandler.get().getUserId());
        modifyApplySample.setGroupName(group.getGroupName());
        modifyApplySample.setGroupId(group.getGroupId());

        if (!applySampleService.updateByApplySampleId(modifyApplySample)) {
            throw new IllegalArgumentException("一次分拣失败");
        }

        return CONTINUE_PROCESSING;
    }
}
