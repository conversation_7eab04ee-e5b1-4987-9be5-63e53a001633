package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 申请单信息
 */
@Getter
@Setter
public class ApplyInfoVo {
    /**
     * 条码
     */
    private String barcode;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;


    /**
     * 身份证
     */
    private String patientCard;

    /**
     * 证件类型
     *
     * @see PatientCardTypeEnum
     */
    private String patientCardType;

    /**
     * 性别
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 就诊卡号
     */
    private String patientVisitCard;
    /**
     * 申请科室
     */
    private String dept;

    /**
     * 采样时间
     */
    private Date samplingDate;

    /**
     * 样本备注
     */
    private String sampleRemark;

    /**
     * 样本类型ID
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;


    /**
     * 就诊类型 (申请类型)
     */
    private String applyType;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 审核状态
     */
    private Integer status;

    /**
     * 检验者ID
     */
    private Long testerId;
    /**
     * 检验者姓名
     */
    private String testerName;

    /**
     * 一审人id
     */

    private Long oneCheckerId;

    /**
     * 一审人
     */

    private String oneCheckerName;

    /**
     * 一审时间
     */

    private Date oneCheckDate;

    /**
     * 二审人id
     */

    private Long twoCheckerId;

    /**
     * 二审人
     */

    private String twoCheckerName;

    /**
     * 二审时间
     */

    private Date twoCheckDate;

}
