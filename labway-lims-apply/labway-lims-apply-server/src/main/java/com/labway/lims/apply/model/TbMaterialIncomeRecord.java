package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物料入库记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Getter
@Setter
@TableName("tb_material_income_record")
public class TbMaterialIncomeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 入库ID
     */
    @TableId
    private Long incomeId;
    /**
     * 入库单号
     */
    private String incomeNo;
    /**
     * 出库单号
     */
    private String deliveryNo;

    /**
     * 物料出库详情id
     */
    private Long deliveryDetailId;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物资编号
     */
    private String materialCode;

    /**
     * 物资名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 厂家
     */
    private String manufacturers;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 出库主单位数量
     */
    private BigDecimal deliveryMainNumber;

    /**
     * 入库主单位数量
     */
    private BigDecimal incomeMainNumber;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     * 出库辅单位数量
     */
    private BigDecimal deliveryAssistNumber;

    /**
     * 入库辅单位数量
     */
    private BigDecimal incomeAssistNumber;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 有效期
     */
    private Date validDate;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

    /**
     * 物料条码号
     */
    private String materialBarcode;

    /**
     * 存放是否合格 1是 0否   默认1
     */
    private Integer ifStorageQualified;

    /**
     * 规格数量是否一致  1是 0否   默认1
     */
    private Integer ifSpecQuantityConsistent;

    /**
     * 包装有无破损 1有0无   默认0
     */
    private Integer ifPackageDamaged;

    /**
     * 效期是否合格  1是 0否   默认1
     */
    private Integer ifValidDateQualified;

    /**
     * 验收结论  1合格0不合格  默认1
     */
    private Integer acceptanceConclusion;
}
