package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物料申领详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
@TableName("tb_group_material_apply_detail")
public class TbGroupMaterialApplyDetail implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 详细ID
     */
    @TableId
    private Long detailId;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物资编号
     */
    private String materialCode;

    /**
     * 物资名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 厂家
     */
    private String manufacturers;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 申领主单位数量
     */
    private BigDecimal applyMainNumber;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     * 申领辅单位数量
     */
    private BigDecimal applyAssistNumber;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

    /**
     * 物料状态，默认物料申请单的状态， 99驳回
     */
    private Integer status;



}
