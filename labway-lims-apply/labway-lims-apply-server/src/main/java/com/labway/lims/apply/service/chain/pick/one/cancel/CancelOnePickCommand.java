package com.labway.lims.apply.service.chain.pick.one.cancel;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 分拣
 */
@Slf4j
@Component
public class CancelOnePickCommand implements Command, InitializingBean {
    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {

        final CancelOnePickContext context = CancelOnePickContext.from(c);


        final ApplySampleDto modifyApplySample = new ApplySampleDto();
        modifyApplySample.setApplySampleId(context.getApplySampleId());
        modifyApplySample.setIsOnePick(YesOrNoEnum.NO.getCode());
        modifyApplySample.setOnePickerId(NumberUtils.LONG_ZERO);
        modifyApplySample.setOnePickerName(StringUtils.EMPTY);
        modifyApplySample.setOnePickDate(DefaultDateEnum.DEFAULT_DATE.getDate());

        if (!applySampleService.updateByApplySampleId(modifyApplySample)) {
            throw new IllegalStateException("取消一次分拣失败");
        }

        return CONTINUE_PROCESSING;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
