package com.labway.lims.apply.vo;

import cn.hutool.core.date.DateUtil;
import com.labway.lims.api.enums.apply.MaterialApplyStatusEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 物料申领单 信息Dto
 *
 * <AUTHOR>
 * @since 2023/5/6 15:10
 */
@Getter
@Setter
public class GroupMaterialApplyVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业组物料申领id
     */
    private Long applyId;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 申请人ID
     */
    private Long applyUserId;

    /**
     * 申请人姓名
     */
    private String applyUserName;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 计划单号
     */
    private Long planNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组姓名
     */
    private String groupName;

    /**
     * 审核人
     */
    private String checker;

    /**
     * 审核时间
     */
    private Date checkDate;


    public String getCheckDate() {
        return Objects.equals(checkDate, DefaultDateEnum.DEFAULT_DATE.getDate()) ?
                StringUtils.EMPTY : DateUtil.formatDateTime(checkDate);
    }



    /**
     * 状态描述
     */
    public String getStatusDesc(){
        return MaterialApplyStatusEnum.getByCode(status).getDesc();
    }
}
