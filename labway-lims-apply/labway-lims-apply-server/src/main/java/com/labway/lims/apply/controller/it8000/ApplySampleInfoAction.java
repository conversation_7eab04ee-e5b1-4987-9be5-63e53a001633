package com.labway.lims.apply.controller.it8000;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.vo.IT8000HandleVo;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ReportItemService;
import lombok.Getter;
import lombok.Setter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 获取样本信息
 */
@Component
class ApplySampleInfoAction implements ActionStrategy {
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private ApplyService applyService;
    @Resource
    private ReportItemService reportItemService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Override
    public Object action(IT8000HandleVo vo) throws Exception {
        final long applySampleId = vo.getExtras().getLongValue("applySampleId");
        final ApplySampleDto applySample = applySampleService
                .selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalArgumentException("申请单样本不存在");
        }

        // 申请单
        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalArgumentException("申请单不存在");
        }

        // 检验项目
        final List<ApplySampleItemDto> items = applySampleItemService.selectByApplySampleId(applySampleId);

        // 报告项目
        final Set<ReportItem> reportItems = reportItemService.selectByTestItemIds(items.stream().map(ApplySampleItemDto::getTestItemId)
                .collect(Collectors.toSet())).stream().map(e -> {
            final ReportItem reportItem = new ReportItem();
            reportItem.setReportItemCode(e.getReportItemCode());
            reportItem.setReportItemName(e.getReportItemName());

            return reportItem;
        }).collect(Collectors.toSet());

        final HspOrganizationDto organization = hspOrganizationService.selectByHspOrgId(apply.getHspOrgId());
        if (Objects.isNull(organization)) {
            throw new IllegalStateException("送检机构不存在");
        }


        return Map.of("applySample", applySample,
                "applySampleItems", items,
                "reportItems", reportItems,
                "hspOrg", Map.of(
                        "enableSplitBlood", Objects.equals(organization.getEnableSplitBlood(), YesOrNoEnum.YES.getCode())
                ),
                "apply", apply);
    }

    @Override
    public IT8000HandleVo.Action action() {
        return IT8000HandleVo.Action.APPLY_SAMPLE_INFO;
    }


    @Getter
    @Setter
    private static class ReportItem {
        /**
         * 报告项目名称
         */
        private String reportItemName;

        /**
         * 报告项目编码
         */
        private String reportItemCode;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ReportItem that = (ReportItem) o;
            return Objects.equals(reportItemCode, that.reportItemCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(reportItemCode);
        }
    }
}
