package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 根据样本条码号 获取打印信息 响应Vo
 *
 * <AUTHOR>
 * @since 2023/4/6 10:12
 */
@Getter
@Setter
public class SelectPrintInfoByBarcodeResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 条码
     */
    private String barcode;
    /**
     * 样本类型
     */
    private String sampleType;
    /**
     * 管型
     */
    private String tube;
    /**
     * 签收状态：0未签收，1已签收
     */
    private Integer status;
    /**
     * 体检人ID
     */
    private Long physicalRegisterId;
    /**
     * 名称
     */
    private String patientName;
    /**
     * 年龄
     */
    private Integer patientAge;
    /**
     * 子年龄
     */
    private Integer patientSubage;
    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;
    /**
     * 性别 1男 2女
     * 
     * @see SexEnum
     */
    private Integer patientSex;
    /**
     * 地址
     */
    private String patientAddress;
    /**
     * 体检单位
     */
    private Long physicalCompanyId;
    /**
     * 体检单位
     */
    private String physicalCompanyName;

    /**
     * 检验项目编码 所有拼接
     */
    private String testItemCodeAll;

    /**
     * 检验项目名称 所有拼接
     */
    private String testItemNameAll;

    /**
     * 打印 样本 检验项目 信息
     */
    private List<PrintInfoSampleItemResponseVo> printInfoSampleItemList;

    /**
     * 送检人
     */
    private String examiner;

    /**
     * 送检 时间
     */
    private Date deliveryDate;

    /**
     * 样本个数
     */
    private Integer sampleCount;

    /**
     * 检验项目英文名  ,拼接
     */
    private List<String> testItemEnNameAll;

    /**
     * 送检机构name
     */
    private String hspOrgName;

    /**
     * 门诊/住院号  就诊卡号
     */
    private String patientVisitCard;

    /**
     * 床号
     */
    private String patientBed;
}
