package com.labway.lims.apply.controller;

import com.google.common.collect.Lists;
import com.labway.lims.api.BarcodeUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.PhysicalSampleStatusEnum;
import com.labway.lims.api.enums.apply.PrintStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.PhysicalRegisterDto;
import com.labway.lims.apply.api.dto.PhysicalSampleDto;
import com.labway.lims.apply.api.dto.PhysicalSampleItemDto;
import com.labway.lims.apply.api.dto.PhysicalSampleTestApplyDto;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.PhysicalRegisterService;
import com.labway.lims.apply.api.service.PhysicalSampleItemService;
import com.labway.lims.apply.api.service.PhysicalSampleService;
import com.labway.lims.apply.vo.PhysicalSamplePrintBarcodeBeforeCheckRequestVo;
import com.labway.lims.apply.vo.PhysicalSamplePrintBarcodeRequestVo;
import com.labway.lims.apply.vo.PrintInfoSampleItemResponseVo;
import com.labway.lims.apply.vo.SelectPrintInfoByBarcodeResponseVo;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.PackageDto;
import com.labway.lims.base.api.dto.PackageItemDto;
import com.labway.lims.base.api.dto.PhysicalGroupDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.BarcodeSettingService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.PackageItemService;
import com.labway.lims.base.api.service.PackageService;
import com.labway.lims.base.api.service.PhysicalGroupService;
import com.labway.lims.base.api.service.TestItemService;
import com.swak.frame.util.StringPool;
import com.labway.lims.base.api.service.ref.IBarcodeSettingServiceRef;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 体检样本 API
 *
 * <AUTHOR>
 * @since 2023/4/4 13:50
 */
@Slf4j
@RestController
@RequestMapping("/physical-sample")
public class PhysicalSampleController extends BaseController {
    @Resource
    private BarcodeUtils barcodeUtils;
    @DubboReference
    private SnowflakeService snowflakeService;

    @DubboReference
    private TestItemService testItemService;

    @DubboReference
    private PhysicalGroupService physicalGroupService;

    @Resource
    private PhysicalSampleService physicalSampleService;

    @Resource
    private PhysicalSampleItemService physicalSampleItemService;

    @Resource
    private PhysicalRegisterService physicalRegisterService;

    @DubboReference
    private PackageService packageService;

    @DubboReference
    private PackageItemService packageItemService;

    @DubboReference
    private ApplyService applyService;

    @DubboReference
    private IBarcodeSettingServiceRef barcodeSettingService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    /**
     * 体检样本 打印条码前置判断
     */
    @PostMapping("/print-barcode-before-check")
    public Object printBarcodeBeforeCheck(@RequestBody PhysicalSamplePrintBarcodeBeforeCheckRequestVo requestVo) {
        List<PhysicalSamplePrintBarcodeRequestVo> printBarcodeRequestVos = requestVo.getPrintBarcodeRequestVos();
        if (CollectionUtils.isEmpty(printBarcodeRequestVos)) {
            return true;
        }

        printBarcodeRequestVos = printBarcodeRequestVos.stream().filter(e ->
                        (Objects.nonNull(e.getPhysicalRegisterId())
                                && Objects.nonNull(e.getSamplingDate())
                                && CollectionUtils.isNotEmpty(e.getPackageItemList())
                                && CollectionUtils.isNotEmpty(e.getPackageItemList().stream().filter(obj -> StringUtils.isNotBlank(obj.getTestPackageDesc())).collect(Collectors.toList()))))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(printBarcodeRequestVos)) {
            return true;
        }

        // 查出所有 体检人
        Set<Long> physicalRegisterIds = printBarcodeRequestVos.stream().map(PhysicalSamplePrintBarcodeRequestVo::getPhysicalRegisterId).collect(Collectors.toSet());
        Map<Long, PhysicalRegisterDto> physicalRegisterMapById = physicalRegisterService.selectByPhysicalRegisterIds(physicalRegisterIds)
                .stream().collect(Collectors.toMap(PhysicalRegisterDto::getPhysicalRegisterId, Function.identity(), (a, b) -> a));

        // 查出所有的 套餐
        Set<Long> physicalGroupPackageIdSet = printBarcodeRequestVos.stream()
                .flatMap(e -> e.getPackageItemList().stream())
                .map(PhysicalSamplePrintBarcodeRequestVo.PhysicalSamplePackageItem::getPhysicalGroupPackageId)
                .collect(Collectors.toSet());
        Map<Long, PackageDto> packageByPackageId = packageService.selectByPackageIds(physicalGroupPackageIdSet).stream()
                .collect(Collectors.toMap(PackageDto::getPackageId, Function.identity()));

        // 查出所有体检套餐项目
        Map<Long, List<PackageItemDto>> packageItemMapByPackageId =
                packageItemService.selectByPackageIds(physicalGroupPackageIdSet).stream().collect(Collectors.groupingBy(PackageItemDto::getPackageId));

        // 查出所有的 检验项目
        Set<Long> testItemIdSet = packageItemMapByPackageId.values().stream()
                .flatMap(Collection::stream).map(PackageItemDto::getTestItemId).collect(Collectors.toSet());
        Map<Long, TestItemDto> testItemMapById = testItemService.selectByTestItemIds(testItemIdSet).stream()
                .collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));

        List<Triple<String, String, String>> errors = new ArrayList<>();
        for (PhysicalSamplePrintBarcodeRequestVo vo : printBarcodeRequestVos) {
            // 填写了 套餐说明的 体检套餐
            List<PhysicalSamplePrintBarcodeRequestVo.PhysicalSamplePackageItem> packageItemList = vo.getPackageItemList()
                    .stream().filter(obj -> StringUtils.isNotBlank(obj.getTestPackageDesc())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(packageItemList)) {
                // 没有要做的体检套餐
                // throw new IllegalArgumentException("打印失败：无可生成条码");
                continue;
            }

            // key 体检套餐id value 套餐说明
            Map<Long, String> packageDescByPackageId = packageItemList.stream().collect(Collectors.toMap(
                    PhysicalSamplePrintBarcodeRequestVo.PhysicalSamplePackageItem::getPhysicalGroupPackageId,
                    PhysicalSamplePrintBarcodeRequestVo.PhysicalSamplePackageItem::getTestPackageDesc, (o1, o2) -> o1));

            if (packageDescByPackageId.entrySet().stream().anyMatch(obj -> !packageByPackageId.containsKey(obj.getKey()))) {
                // throw new IllegalStateException("存在无效套餐,请重新选择");
                continue;
            }
            // 选择 套餐 对应 体检单位
            Set<Long> physicalGroupIds = packageByPackageId.values().stream()
                    .map(PackageDto::getPhysicalGroupId).collect(Collectors.toSet());
            if (physicalGroupIds.size() > 1) {
                // throw new IllegalStateException("选择套餐不在同一个体检单位下");
                continue;
            }
            Long physicalGroupId = physicalGroupIds.stream().findFirst().orElse(0L);

            PhysicalRegisterDto registerDto = physicalRegisterMapById.get(vo.getPhysicalRegisterId());
            if (Objects.isNull(registerDto)) {
                // throw new IllegalStateException("体检人不存在,请重新选择");
                continue;
            }
            if (Objects.equals(registerDto.getIsPrint(), PrintStatusEnum.PRINTED.getCode())) {
                // throw new IllegalStateException("体检人已打印,不可重复打印");
                continue;
            }
            if (!Objects.equals(registerDto.getPhysicalCompanyId(), physicalGroupId)) {
                // throw new LimsException("体检人与体检套餐对应体检单位不一致");
                continue;
            }

            // 通过体检人 上的套餐说明 获取 需要生成的 体检套餐
            String testPackageDesc = registerDto.getTestPackageDesc();
            Set<Long> physicalGroupPackageIds =
                    packageDescByPackageId.entrySet().stream().filter(obj -> Objects.equals(obj.getValue(), testPackageDesc))
                            .map(Map.Entry::getKey).collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(physicalGroupPackageIds)) {
                // 没有要做的体检套餐
                // throw new IllegalArgumentException("打印失败：无可生成条码");
                continue;
            }

            // 体检套餐 下项目
            List<PackageItemDto> packageItemDtos = physicalGroupPackageIds.stream()
                    .flatMap(packageId -> packageItemMapByPackageId.get(packageId).stream()).collect(Collectors.toList());

            // 套餐 下所有检验项目ids
            List<Long> testItemIds = packageItemDtos.stream().map(PackageItemDto::getTestItemId)
                    .collect(Collectors.toList());

            // 对应检验项目
            List<TestItemDto> testItemDtos = testItemIds.stream().map(testItemMapById::get).collect(Collectors.toList());

            String patientName = registerDto.getPatientName();
            Integer patientSex = registerDto.getPatientSex();

            // 筛选出来 未匹配 限制性别的项目
            List<TestItemDto> testItemsNonmatch = testItemDtos.stream()
                    .filter(e -> Objects.nonNull(e.getLimitSex()) && !Objects.equals(SexEnum.DEFAULT.getCode(), e.getLimitSex()))
                    .filter(e -> !Objects.equals(patientSex, e.getLimitSex()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(testItemsNonmatch)) {
                String testItemNames = testItemsNonmatch.stream().map(TestItemDto::getTestItemName).collect(Collectors.joining(StringPool.COMMA));
                String sex = SexEnum.getByCode(testItemsNonmatch.stream().findFirst().map(TestItemDto::getLimitSex).get()).getDesc();
                errors.add(Triple.of(patientName, sex, testItemNames));
            }
        }

        if (CollectionUtils.isNotEmpty(errors)) {
            Set<String> names = new HashSet<>();
            List<String> errorTips = new ArrayList<>();
            for (Triple<String, String, String> error : errors) {
                String patientName = error.getLeft();
                String sex = error.getMiddle();
                String testItemNames = error.getRight();
                if (names.add(testItemNames)) {
                    errorTips.add(String.format("【%s】项目限制性别为：%s", testItemNames, sex));
                }
            }
            errorTips.add("是否继续打印？");
            throw new LimsCodeException(1122, String.join("<br />", errorTips));
        }

        return true;
    }

    /**
     * 体检样本 打印条码
     */
    @PostMapping("/print-barcode")
    public Object printBarcode(@RequestBody PhysicalSamplePrintBarcodeRequestVo vo) {

        if (Objects.isNull(vo.getPhysicalRegisterId())) {
            throw new IllegalArgumentException("请选择体检人");
        }

        if (Objects.isNull(vo.getSamplingDate())) {
            throw new IllegalArgumentException("请选择采集时间");
        }

        if (CollectionUtils.isEmpty(vo.getPackageItemList())) {
            throw new IllegalArgumentException("请填写完整套餐信息");
        }
        Date samplingDate = vo.getSamplingDate();

        // 填写了 套餐说明的 体检套餐
        List<PhysicalSamplePrintBarcodeRequestVo.PhysicalSamplePackageItem> packageItemList = vo.getPackageItemList()
            .stream().filter(obj -> StringUtils.isNotBlank(obj.getTestPackageDesc())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(packageItemList)) {
            // 没有要做的体检套餐
            throw new IllegalArgumentException("打印失败：无可生成条码");
        }

        // key 体检套餐id value 套餐说明
        Map<Long, String> packageDescByPackageId = packageItemList.stream().collect(Collectors.toMap(
            PhysicalSamplePrintBarcodeRequestVo.PhysicalSamplePackageItem::getPhysicalGroupPackageId,
            PhysicalSamplePrintBarcodeRequestVo.PhysicalSamplePackageItem::getTestPackageDesc, (key1, key2) -> key1));

        // key 体检套餐id value
        Map<Long, PackageDto> packageByPackageId =
            packageService.selectByPackageIds(packageDescByPackageId.keySet()).stream()
                .collect(Collectors.toMap(PackageDto::getPackageId, Function.identity()));

        if (packageDescByPackageId.entrySet().stream().anyMatch(obj -> !packageByPackageId.containsKey(obj.getKey()))) {
            throw new IllegalStateException("存在无效套餐,请重新选择");
        }
        // 选择 套餐 对应 体检单位
        Set<Long> physicalGroupIds = packageByPackageId.values().stream()
            .map(PackageDto::getPhysicalGroupId).collect(Collectors.toSet());
        if (physicalGroupIds.size() > 1) {
            throw new IllegalStateException("选择套餐不在同一个体检单位下");
        }
        Long physicalGroupId = physicalGroupIds.stream().findFirst().orElse(0L);

        PhysicalRegisterDto registerDto =
            physicalRegisterService.selectByPhysicalRegisterId(vo.getPhysicalRegisterId());
        if (Objects.isNull(registerDto)) {
            throw new IllegalStateException("体检人不存在,请重新选择");
        }
        if (Objects.equals(registerDto.getIsPrint(), PrintStatusEnum.PRINTED.getCode())) {
            throw new IllegalStateException("体检人已打印,不可重复打印");
        }
        if (!Objects.equals(registerDto.getPhysicalCompanyId(), physicalGroupId)) {
            throw new LimsException("体检人与体检套餐对应体检单位不一致");
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 通过体检人 上的套餐说明 获取 需要生成的 体检套餐
        String testPackageDesc = registerDto.getTestPackageDesc();
        Set<Long> physicalGroupPackageIds =
            packageDescByPackageId.entrySet().stream().filter(obj -> Objects.equals(obj.getValue(), testPackageDesc))
                .map(Map.Entry::getKey).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(physicalGroupPackageIds)) {
            // 没有要做的体检套餐
            throw new IllegalArgumentException("打印失败：无可生成条码");
        }

        // 体检套餐 下项目
        List<PackageItemDto> packageItemDtos =
            packageItemService.selectByPackageIds(physicalGroupPackageIds);

        // key: 体检套餐id value: 体检套餐下的项目
        Map<Long,
            Set<Long>> testItemByPackageId = packageItemDtos.stream()
                .collect(Collectors.groupingBy(PackageItemDto::getPackageId,
                    Collectors.mapping(PackageItemDto::getTestItemId, Collectors.toSet())));

        // 套餐 下所有检验项目ids
        List<Long> testItemIds = packageItemDtos.stream().map(PackageItemDto::getTestItemId)
            .collect(Collectors.toList());

        // 对应检验项目
        List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(testItemIds);

        // key: 检验项目id value: 检验项目
        Map<Long, TestItemDto> testItemDtoById =
            testItemDtos.stream().collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));

        // 先查下 需要生成多少 条码 多少id

        // 此人 可做那些检验项目

        Set<Long> itemIds = physicalGroupPackageIds.stream()
                .flatMap(item -> Optional.ofNullable(testItemByPackageId.get(item)).orElse(Collections.emptySet()).stream())
                .collect(Collectors.toSet());
        List<TestItemDto> itemDtos = itemIds.stream().map(testItemDtoById::get).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(itemDtos)) {
            // 没有要做的检验项目
            throw new IllegalArgumentException("打印失败：无可生成条码");
        }

        // 样本类型 + 管型一致 的为一个 条码
        Map<String, List<TestItemDto>> itemGroupingBySampleTypeAndTube =
            itemDtos.stream().collect(Collectors.groupingBy(
                testItemDto -> testItemDto.getSampleTypeCode() + "_" + testItemDto.getTubeCode(), Collectors.toList()));

        int barcodeCount = itemGroupingBySampleTypeAndTube.size();
        int genIdCount = barcodeCount + itemDtos.size();

        LinkedList<Long> genIds = snowflakeService.genIds(genIdCount);

        PhysicalGroupDto physicalGroupDto = physicalGroupService.selectByPhysicalGroupId(physicalGroupId);
        if(Objects.isNull(physicalGroupDto)){
            throw new IllegalArgumentException("体检机构不存在");
        }
        HspOrganizationDto hspOrganizationDto = hspOrganizationService.selectByHspOrgId(physicalGroupDto.getHspOrgId());
        if(Objects.isNull(hspOrganizationDto)){
            throw new IllegalArgumentException("送检机构不存在");
        }
        LinkedList<String> barcodeList = new LinkedList<>(barcodeSettingService.genBarcodes(hspOrganizationDto.getHspOrgCode(), barcodeCount));
        if(CollectionUtils.isEmpty(barcodeList)) {
            barcodeList.addAll(barcodeUtils.genBarcode(barcodeCount));
        }

        // 响应的条码号
        List<String> targetList = barcodeList.stream().map(String::new).collect(Collectors.toList());

        // 要插入的 体检样本、体检样本项目 要更改的 体检人信息
        Date date = new Date();
        List<PhysicalSampleDto> sampleDtoList = Lists.newArrayList();
        List<PhysicalSampleItemDto> sampleItemDtoList = Lists.newArrayList();
        List<PhysicalRegisterDto> registerDtoList = Lists.newArrayList();

        for (Map.Entry<String, List<TestItemDto>> entry : itemGroupingBySampleTypeAndTube.entrySet()) {
            List<TestItemDto> value = entry.getValue();
            TestItemDto testItemDto = value.get(0);

            // 体检样本
            PhysicalSampleDto sampleDto = new PhysicalSampleDto();
            sampleDto.setPhysicalSampleId(genIds.pop());
            sampleDto.setPhysicalRegisterId(registerDto.getPhysicalRegisterId());
            sampleDto.setPhysicalBatchId(registerDto.getPhysicalBatchId());
            sampleDto.setSampleType(testItemDto.getSampleTypeName());
            sampleDto.setBarcode(barcodeList.pop());
            sampleDto.setTube(testItemDto.getTubeName());
            sampleDto.setPhysicalCompanyId(registerDto.getPhysicalCompanyId());
            sampleDto.setPhysicalCompanyName(registerDto.getPhysicalCompanyName());
            sampleDto.setSampleCount(NumberUtils.INTEGER_ONE);
            sampleDto.setOrgId(loginUser.getOrgId());
            sampleDto.setOrgName(loginUser.getOrgName());
            sampleDto.setCreateDate(date);
            sampleDto.setUpdateDate(date);
            sampleDto.setCreatorName(loginUser.getNickname());
            sampleDto.setCreatorId(loginUser.getUserId());
            sampleDto.setUpdaterName(loginUser.getNickname());
            sampleDto.setUpdaterId(loginUser.getUserId());
            sampleDto.setIsDelete(YesOrNoEnum.NO.getCode());
            sampleDto.setStatus(PhysicalSampleStatusEnum.UNSIGNED.getCode());
            sampleDto.setApplyId(NumberUtils.LONG_ZERO);
            sampleDto.setSamplingDate(samplingDate);

            // 要新增的体检样本
            sampleDtoList.add(sampleDto);

            for (TestItemDto itemDto : value) {

                // 体检样本项目
                PhysicalSampleItemDto sampleItemDto = new PhysicalSampleItemDto();
                sampleItemDto.setPhysicalSampleItemId(genIds.pop());
                sampleItemDto.setPhysicalSampleId(sampleDto.getPhysicalSampleId());
                sampleItemDto.setPhysicalRegisterId(registerDto.getPhysicalRegisterId());
                sampleItemDto.setPhysicalBatchId(registerDto.getPhysicalBatchId());
                sampleItemDto.setTestItemId(itemDto.getTestItemId());
                sampleItemDto.setTestItemCode(itemDto.getTestItemCode());
                sampleItemDto.setTestItemName(itemDto.getTestItemName());
                sampleItemDto.setOrgId(loginUser.getOrgId());
                sampleItemDto.setOrgName(loginUser.getOrgName());
                sampleItemDto.setCreateDate(date);
                sampleItemDto.setUpdateDate(date);
                sampleItemDto.setCreatorName(loginUser.getNickname());
                sampleItemDto.setCreatorId(loginUser.getUserId());
                sampleItemDto.setUpdaterName(loginUser.getNickname());
                sampleItemDto.setUpdaterId(loginUser.getUserId());
                sampleItemDto.setIsDelete(YesOrNoEnum.NO.getCode());

                // 要新增的体检样本检验项目
                sampleItemDtoList.add(sampleItemDto);
            }
        }

        // 体检人 调整为已打印条码
        PhysicalRegisterDto update = new PhysicalRegisterDto();
        update.setIsPrint(PrintStatusEnum.PRINTED.getCode());
        update.setPhysicalRegisterId(registerDto.getPhysicalRegisterId());
        registerDtoList.add(update);

        // 体检 打印条码 保存数据
        physicalSampleService.printBarcodeHandleData(sampleDtoList, sampleItemDtoList, registerDtoList);
        return targetList;
    }

    /**
     * 体检样本签收 获取条码对应信息
     * <p>
     * 只查询未签收条码
     */
    @PostMapping("/select-sample-info-by-barcode")
    public Object selectPrintInfoByBarcode(@RequestParam("barcode") String barcode) {
        if (StringUtils.isBlank(barcode)) {
            throw new LimsException("条码号不能为空");
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        // 条码 对应 体检样本
        final PhysicalSampleDto physicalSampleDto = physicalSampleService.selectByBarcode(barcode, user.getOrgId());
        if (Objects.isNull(physicalSampleDto)) {
            throw new LimsException("条码对应体检样本不存在");
        }
        if (Objects.equals(physicalSampleDto.getStatus(), PhysicalSampleStatusEnum.SIGNED.getCode())) {
            throw new LimsException("该条码已签收");
        }

        // 样本 关联体检人
        final PhysicalRegisterDto physicalRegisterDto =
            physicalRegisterService.selectByPhysicalRegisterId(physicalSampleDto.getPhysicalRegisterId());
        if (Objects.isNull(physicalRegisterDto)) {
            throw new LimsException("体检样本对应体检人不存在");
        }

        // 样本 对应 样本项目
        List<PhysicalSampleItemDto> sampleItemDtoList =
            physicalSampleItemService.selectByPhysicalSampleId(physicalSampleDto.getPhysicalSampleId());

        // 响应数据
        SelectPrintInfoByBarcodeResponseVo target = new SelectPrintInfoByBarcodeResponseVo();
        target.setBarcode(physicalSampleDto.getBarcode());
        target.setSampleType(physicalSampleDto.getSampleType());
        target.setTube(physicalSampleDto.getTube());
        target.setStatus(physicalSampleDto.getStatus());

        target.setPhysicalRegisterId(physicalRegisterDto.getPhysicalRegisterId());
        target.setPatientName(physicalRegisterDto.getPatientName());
        target.setPatientAge(physicalRegisterDto.getPatientAge());
        target.setPatientSubage(physicalRegisterDto.getPatientSubage());
        target.setPatientSubageUnit(physicalRegisterDto.getPatientSubageUnit());
        target.setPatientSex(physicalRegisterDto.getPatientSex());
        target.setPhysicalCompanyId(physicalRegisterDto.getPhysicalCompanyId());
        target.setPhysicalCompanyName(physicalRegisterDto.getPhysicalCompanyName());

        String testItemCodeAll =
            sampleItemDtoList.stream().map(PhysicalSampleItemDto::getTestItemCode).collect(Collectors.joining(","));
        target.setTestItemCodeAll(testItemCodeAll);

        String testItemNameAll =
            sampleItemDtoList.stream().map(PhysicalSampleItemDto::getTestItemName).collect(Collectors.joining(","));
        target.setTestItemNameAll(testItemNameAll);

        List<PrintInfoSampleItemResponseVo> printInfoSampleItemList =
            Lists.newArrayListWithCapacity(sampleItemDtoList.size());
        for (PhysicalSampleItemDto sampleItemDto : sampleItemDtoList) {
            PrintInfoSampleItemResponseVo printInfoSampleItemResponseVo = new PrintInfoSampleItemResponseVo();
            printInfoSampleItemResponseVo.setTestItemId(sampleItemDto.getTestItemId());
            printInfoSampleItemResponseVo.setTestItemCode(sampleItemDto.getTestItemCode());
            printInfoSampleItemResponseVo.setTestItemName(sampleItemDto.getTestItemName());
            printInfoSampleItemList.add(printInfoSampleItemResponseVo);
        }
        target.setPrintInfoSampleItemList(printInfoSampleItemList);

        // 样本个数暂时写死1
        target.setSampleCount(NumberUtils.INTEGER_ONE);
        target.setExaminer(physicalRegisterDto.getApplicant());
        target.setDeliveryDate(physicalSampleDto.getSamplingDate());

        return target;
    }

    /**
     * 体检 样本签收
     */
    @PostMapping("/sample-signature")
    public Object sampleSignature(@RequestParam("barcode") String barcode) {

        LoginUserHandler.User user = LoginUserHandler.get();

        // 条码 对应 体检样本
        final PhysicalSampleDto sampleDto = physicalSampleService.selectByBarcode(barcode, user.getOrgId());
        if (Objects.isNull(sampleDto)) {
            throw new LimsException("条码对应体检样本不存在");
        }

        if (Objects.equals(sampleDto.getStatus(), PhysicalSampleStatusEnum.SIGNED.getCode())) {
            throw new LimsException("该条码已签收");
        }

        // 体检样本 对应体检单位信息
        final PhysicalGroupDto physicalGroupDto =
            physicalGroupService.selectByPhysicalGroupId(sampleDto.getPhysicalCompanyId());
        if (Objects.isNull(physicalGroupDto)) {
            throw new LimsException("体检样本对应体检单位不存在");
        }

        // 体检样本 对应体检人信息
        final PhysicalRegisterDto physicalRegisterDto =
            physicalRegisterService.selectByPhysicalRegisterId(sampleDto.getPhysicalRegisterId());
        if (Objects.isNull(physicalRegisterDto)) {
            throw new LimsException("体检样本对应体检人不存在");
        }

        // 体检样本 对应体检项目信息
        final List<PhysicalSampleItemDto> sampleItemDtoList =
            physicalSampleItemService.selectByPhysicalSampleIds(Lists.newArrayList(sampleDto.getPhysicalSampleId()));

        // 签收
        PhysicalSampleTestApplyDto temp = new PhysicalSampleTestApplyDto();
        temp.setHspOrgId(physicalGroupDto.getHspOrgId());
        temp.setApplyTypeCode(physicalRegisterDto.getApplyTypeCode());
        temp.setApplyTypeName(physicalRegisterDto.getApplyTypeName());
        temp.setPatientName(physicalRegisterDto.getPatientName());
        temp.setPatientSex(physicalRegisterDto.getPatientSex());
        temp.setPatientBirthday(physicalRegisterDto.getPatientBirthday());
        temp.setPatientAge(physicalRegisterDto.getPatientAge());
        temp.setPatientSubage(physicalRegisterDto.getPatientSubage());
        temp.setPatientSubageUnit(physicalRegisterDto.getPatientSubageUnit());
        temp.setUrgent(UrgentEnum.NORMAL.getCode());
        temp.setSampleCount(sampleDto.getSampleCount());
        temp.setSampleProperty(StringUtils.EMPTY);
        temp.setSamplePropertyCode(StringUtils.EMPTY);
        temp.setApplyDate(new Date());
        temp.setSamplingDate(sampleDto.getSamplingDate());
        temp.setPatientVisitCard(physicalRegisterDto.getPatientVisitCard());
        temp.setDept(physicalRegisterDto.getDept());
        temp.setPatientBed(physicalRegisterDto.getPatientBed());
        temp.setClinicalDiagnosis(physicalRegisterDto.getDiagnosis());
        temp.setRemark(physicalRegisterDto.getRemark());
        temp.setPatientMobile(physicalRegisterDto.getPatientMobile());
        temp.setPatientCard(physicalRegisterDto.getPatientCard());
        temp.setPatientCardType(physicalRegisterDto.getPatientCardType());
        temp.setSendDoctor(physicalRegisterDto.getApplicant());
        temp.setPatientAddress(physicalRegisterDto.getPatientAddress());
        temp.setApplySource(ApplySourceEnum.PHYSICAL_SIGN);
        temp.setSupplier("实验室LIMS");

        temp.setBarcode(sampleDto.getBarcode());
        temp.setSampleType(sampleDto.getSampleType());
        temp.setTube(sampleDto.getTube());
        temp.setSampleItemDtoList(sampleItemDtoList);
        // 同人同天同项目 忽略校验
        temp.setIgnoreSameItem(YesOrNoEnum.YES.getCode());
        // 检验项目限制性别 忽略校验
        temp.setIgnoreItemLimitSex(true);

        ApplyInfo applyInfo = applyService.addApply(temp);

        if (Objects.isNull(applyInfo)) {
            throw new LimsException("体检样本签收失败");
        }

        // 修改 体检样本 签收状态、申请单id
        PhysicalSampleDto updateSampleDto = new PhysicalSampleDto();
        updateSampleDto.setPhysicalSampleId(sampleDto.getPhysicalSampleId());
        updateSampleDto.setStatus(PhysicalSampleStatusEnum.SIGNED.getCode());
        updateSampleDto.setApplyId(applyInfo.getApplyId());
        physicalSampleService.updateByPhysicalSampleId(updateSampleDto);

        return Collections.emptyMap();
    }

    /**
     * 打印条码 获取条码对应信息
     * <p>
     * 只查询未签收条码
     */
    @PostMapping("/select-sample-info-by-barcodes")
    public Object selectPrintInfoByBarcodes(@RequestBody List<String> barcodes) {
        if (CollectionUtils.isEmpty(barcodes)) {
            throw new LimsException("条码号不能为空");
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        // 条码 对应 体检样本
        List<PhysicalSampleDto> physicalSampleDtoList =
            physicalSampleService.selectByBarcodes(barcodes, user.getOrgId());

        // key: 条码 value: 体检样本
        Map<String, PhysicalSampleDto> physicalSampleDtoByBarcode = physicalSampleDtoList.stream()
            .collect(Collectors.toMap(PhysicalSampleDto::getBarcode, Function.identity()));

        // 体检单位id
        Set<Long> physicaCompanyIds = physicalSampleDtoList.stream().map(PhysicalSampleDto::getPhysicalCompanyId).collect(Collectors.toSet());
        // 体检样本 对应体检单位信息
        final List<PhysicalGroupDto> physicalGroupDtos =
                physicalGroupService.selectByPhysicalGroupIds(physicaCompanyIds);

        // 条码 对应体检样本不存在
        List<String> isNullBarcodes = barcodes.stream().filter(x -> Objects.isNull(physicalSampleDtoByBarcode.get(x)))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(isNullBarcodes)) {
            throw new LimsException(String.format("[%s] 条码对应体检样本不存在", isNullBarcodes));
        }

        // 对应体检人
        Set<Long> physicalRegisterIds =
            physicalSampleDtoList.stream().map(PhysicalSampleDto::getPhysicalRegisterId).collect(Collectors.toSet());

        // 样本 关联体检人
        List<PhysicalRegisterDto> registerDtoList =
            physicalRegisterService.selectByPhysicalRegisterIds(physicalRegisterIds);

        // key: 体检人id value:体检人信息
        Map<Long, PhysicalRegisterDto> physicalRegisterDtoById = registerDtoList.stream()
            .collect(Collectors.toMap(PhysicalRegisterDto::getPhysicalRegisterId, Function.identity()));

        // 对应体检样本ids
        Set<Long> physicalSampleIds =
            physicalSampleDtoList.stream().map(PhysicalSampleDto::getPhysicalSampleId).collect(Collectors.toSet());

        // 样本 对应 样本项目
        List<PhysicalSampleItemDto> sampleItemDtoListAll =
            physicalSampleItemService.selectByPhysicalSampleIds(physicalSampleIds);

        Set<Long> testItemIds = sampleItemDtoListAll.stream().map(PhysicalSampleItemDto::getTestItemId).collect(Collectors.toSet());
        // 查询检验项目
        Map<Long, TestItemDto> testItemDtoMap = testItemService.selectByTestItemIdsAsMap(testItemIds);

        // key: 体检样本id value： 样本检验项目
        Map<Long, List<PhysicalSampleItemDto>> sampleItemDtoGroupingBySampleId =
            sampleItemDtoListAll.stream().collect(Collectors.groupingBy(PhysicalSampleItemDto::getPhysicalSampleId));

        // 响应数据
        List<SelectPrintInfoByBarcodeResponseVo> targetList = Lists.newArrayListWithCapacity(barcodes.size());
        for (String barcode : barcodes) {

            SelectPrintInfoByBarcodeResponseVo target = new SelectPrintInfoByBarcodeResponseVo();

            PhysicalSampleDto physicalSampleDto = physicalSampleDtoByBarcode.get(barcode);
            target.setBarcode(physicalSampleDto.getBarcode());
            target.setSampleType(physicalSampleDto.getSampleType());
            target.setTube(physicalSampleDto.getTube());
            target.setStatus(physicalSampleDto.getStatus());

            // 体检人
            PhysicalRegisterDto physicalRegisterDto =
                physicalRegisterDtoById.get(physicalSampleDto.getPhysicalRegisterId());
            if (Objects.nonNull(physicalRegisterDto)) {
                target.setPhysicalRegisterId(physicalRegisterDto.getPhysicalRegisterId());
                target.setPatientName(physicalRegisterDto.getPatientName());
                target.setPatientSubage(physicalRegisterDto.getPatientSubage());
                target.setPatientSubageUnit(physicalRegisterDto.getPatientSubageUnit());
                target.setPatientAge(physicalRegisterDto.getPatientAge());
                target.setPatientSex(physicalRegisterDto.getPatientSex());
                target.setPhysicalCompanyId(physicalRegisterDto.getPhysicalCompanyId());
                target.setPhysicalCompanyName(physicalRegisterDto.getPhysicalCompanyName());
                target.setExaminer(physicalRegisterDto.getApplicant());

                target.setPatientVisitCard(physicalRegisterDto.getPatientVisitCard());
                target.setPatientBed(physicalRegisterDto.getPatientBed());
            }

            // 体检样本
            List<PhysicalSampleItemDto> sampleItemDtoList =
                sampleItemDtoGroupingBySampleId.get(physicalSampleDto.getPhysicalSampleId());
            String testItemCodeAll =
                sampleItemDtoList.stream().map(PhysicalSampleItemDto::getTestItemCode).collect(Collectors.joining(","));
            target.setTestItemCodeAll(testItemCodeAll);

            String testItemNameAll =
                sampleItemDtoList.stream().map(PhysicalSampleItemDto::getTestItemName).collect(Collectors.joining(","));
            target.setTestItemNameAll(testItemNameAll);

            List<String> testItemEnNameAll = new ArrayList<>();
            testItemIds = sampleItemDtoList.stream().map(PhysicalSampleItemDto::getTestItemId).collect(Collectors.toSet());
            Set<Map.Entry<Long, TestItemDto>> entries = testItemDtoMap.entrySet();
            for (Map.Entry<Long, TestItemDto> entry : entries) {
                if(testItemIds.contains(entry.getKey())){
                    testItemEnNameAll.add(entry.getValue().getEnName());
                }
            }
            target.setTestItemEnNameAll(testItemEnNameAll);

            List<PrintInfoSampleItemResponseVo> printInfoSampleItemList =
                Lists.newArrayListWithCapacity(sampleItemDtoList.size());
            for (PhysicalSampleItemDto sampleItemDto : sampleItemDtoList) {
                PrintInfoSampleItemResponseVo printInfoSampleItemResponseVo = new PrintInfoSampleItemResponseVo();
                printInfoSampleItemResponseVo.setTestItemId(sampleItemDto.getTestItemId());
                printInfoSampleItemResponseVo.setTestItemCode(sampleItemDto.getTestItemCode());
                printInfoSampleItemResponseVo.setTestItemName(sampleItemDto.getTestItemName());
                printInfoSampleItemList.add(printInfoSampleItemResponseVo);
            }
            target.setPrintInfoSampleItemList(printInfoSampleItemList);

            // 样本个数暂时写死1
            target.setSampleCount(NumberUtils.INTEGER_ONE);


            target.setDeliveryDate(physicalSampleDto.getSamplingDate());

            if(CollectionUtils.isNotEmpty(physicalGroupDtos)){
                PhysicalGroupDto physicalGroupDto = physicalGroupDtos.get(0);
                target.setHspOrgName(physicalGroupDto.getHspOrgName());
            }

            targetList.add(target);
        }

        return targetList;
    }

}
