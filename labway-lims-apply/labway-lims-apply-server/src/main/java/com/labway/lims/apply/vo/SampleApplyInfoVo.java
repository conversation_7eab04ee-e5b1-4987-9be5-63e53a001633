package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 申请单信息
 */
@Getter
@Setter
public class SampleApplyInfoVo {

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;


    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 年龄 xx岁
     */
    private Integer patientAge;

    /**
     * 子年龄    xxx天|xxx周|xxx月
     */
    private Integer patientSubage;

    /**
     * 子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 科室
     */
    private String dept;
    /**
     * 送检医生名称
     */
    private String sendDoctorName;

    /**
     * 采样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date samplingDate;
    /**
     * 样本备注
     */
    private String sampleRemark;
    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 样本性状编码
     */
    private String samplePropertyCode;

    /**
     * 审核状态
     *
     * @see SampleStatusEnum
     */
    private String statusText;

    /**
     * 检验人ID
     */
    private Long testerId;
    /**
     * 检验者姓名
     */
    private String testerName;

    /**
     * 样本是否加急 (1急诊 0不急)
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 一次审核人Id
     */
    private Long oneCheckerId;

    /**
     * 一次审核人
     */
    private String oneCheckerName;


    /**
     * 二次审核人Id
     */
    private Long twoCheckerId;

    /**
     * 二次审核人
     */
    private String twoCheckerName;


    /**
     * 门诊|住院 号
     */
    private String patientVisitCard;
    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 就诊类型
     */
    private String applyTypeName;

    /**
     * 就诊类型编码
     */
    private String applyTypeCode;
    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 检验日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date testDate;


    /**
     * 临床诊断
     */
    private String clinicalDiagnosis;


    /**
     * 最终审核日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date checkDate;
    /**
     * 结果备注
     */
    private String resultRemark;
    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;
    /**
     * 床号
     */
    private String patientBed;

    /**
     * 样本异常ID
     */
    private Long sampleAbnormalId;
}
