package com.labway.lims.apply.service.chain.splitblood;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 校验参数
 *
 * <AUTHOR>
 */
@Component
public class CheckParamsCommand implements Command, Filter {

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private TestItemService testItemService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private RackLogicService rackLogicService;

    @Override
    public boolean execute(Context c) throws Exception {

        final SplitBloodContext context = SplitBloodContext.from(c);

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(context.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalArgumentException("申请单样本不存在");
        }
        context.put(SplitBloodContext.APPLY_SAMPLE, applySample);

        final List<RackLogicSpaceDto> rackLogicSpaces = rackLogicSpaceService.selectByApplySampleId(applySample.getApplySampleId());
        if (CollectionUtils.isEmpty(rackLogicSpaces) || rackLogicSpaces.size() != 1) {
            throw new IllegalStateException("分血失败，逻辑试管架错误");
        }
        context.put(SplitBloodContext.RACK_LOGIC_SPACE, rackLogicSpaces.iterator().next());

        final RackLogicDto rackLogic = rackLogicService.selectByRackLogicId(rackLogicSpaces.iterator().next().getRackLogicId());
        if (Objects.isNull(rackLogic)) {
            throw new IllegalStateException("分血失败，逻辑试管架不存在");
        }
        context.put(SplitBloodContext.RACK_LOGIC, rackLogic);

        if (!Objects.equals(rackLogic.getCurrentGroupId(), LoginUserHandler.get().getGroupId())) {
            throw new IllegalStateException(String.format("样本 [%s] 无法分血，因为当前样本试管架不在 [%s] 专业组", applySample.getBarcode()
                    , LoginUserHandler.get().getGroupName()));
        }

        if (!Objects.equals(rackLogic.getPosition(), RackLogicPositionEnum.SPLITTING_BLOOD.getCode())) {
            throw new IllegalStateException(String.format("样本 [%s] 无法分血，因为尚未到分血环节", applySample.getBarcode()));
        }


        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(applySample.getApplySampleId());
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("条码下没有检验项目");
        }
        context.put(SplitBloodContext.APPLY_SAMPLE_ITEMS, applySampleItems);


        final Map<String, TestItemDto> testItems = testItemService.selectByTestItemCodes(applySampleItems.stream()
                        .map(ApplySampleItemDto::getTestItemCode).collect(Collectors.toSet()), applySample.getOrgId())
                .stream().collect(Collectors.toMap(TestItemDto::getTestItemCode, v -> v, (a, b) -> a));

        for (ApplySampleItemDto e : applySampleItems) {
            if (!testItems.containsKey(e.getTestItemCode())) {
                throw new IllegalStateException(String.format("检验项目 [%s<%s>] 不存在，请检查基础数据是否维护",
                        e.getTestItemName(), e.getTestItemCode()));
            }
        }
        context.put(SplitBloodContext.TEST_ITEMS, testItems);


        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
