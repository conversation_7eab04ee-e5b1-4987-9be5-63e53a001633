package com.labway.lims.apply.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.labway.business.center.compare.dto.TbOrgApplySampleMainImportResultDTO;
import com.labway.business.center.compare.request.ImportItemAndMappingRequest;
import com.labway.business.center.compare.request.SendApplyFormInfoRequest;
import com.labway.business.center.compare.request.SendApplySampleInfoRequest;
import com.labway.business.center.compare.service.TbOrgApplySampleMainService;
import com.labway.business.center.core.enums.SexEnum;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.utils.FileUtils;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.enums.ImportFileEnum;
import com.labway.lims.apply.api.enums.ImportFileResourceEnum;
import com.labway.lims.apply.api.enums.ImportFileStatusEnum;
import com.labway.lims.apply.api.service.UploadFileService;
import com.labway.lims.apply.service.listener.AnalyzeExcelFileListener;
import com.labway.lims.apply.service.listener.ImportPhysicalRegisterListener;
import com.labway.lims.apply.service.listener.ImportSampleListener;
import com.labway.lims.apply.service.listener.ImportSampleRegisterListener;
import com.labway.lims.apply.vo.*;
import com.labway.lims.apply.vo.utils.ExcelFileHeadMappingVo;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.swak.frame.dto.Response;
import com.swak.frame.enums.BasicErrCode;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 样本信息导入
 * 
 * <AUTHOR>
 * @since 2023/6/8 9:36
 */
@Slf4j
@RestController
@RequestMapping("/sample-import")
public class SampleImportController extends BaseController {

    @Value("${business-center.org-code}")
    private String orgCode;
    @Value("${business-center.org-name}")
    private String orgName;

    @Resource
    private TbOrgApplySampleMainService tbOrgApplySampleMainService;

    @Resource
    private UploadFileService uploadFileService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Resource
    private HuaweiObsUtils huaweiObsUtils;

    // 导入人员 错误
    private static final int REGISTER_ERROR_CODE = 10001;

    // ---------------业务中台样本信息导入------------------------------

    /**
     * 样本信息导入-导入上传文件
     */
    @PostMapping("/import-file")
    public Object importFile(@RequestParam("file") MultipartFile file) {

        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(fileName);
        if (!"xls".equalsIgnoreCase(extension) && !"xlsx".equalsIgnoreCase(extension)) {
            throw new LimsException("只允许上传 xls 和 xlsx 格式的文件");
        }

        String uploadUrl;
        try {
            uploadUrl = huaweiObsUtils.upload(file.getInputStream(), "application/vnd.ms-excel", -1);
        } catch (Exception e) {
            throw new IllegalStateException("上传文件失败", e);
        }

        return Map.of("uploadUrl", uploadUrl);
    }

    /**
     * 解析 样本导入文件
     */
    @PostMapping("/analyze-excel-file")
    public Object analyzeExcelFile(@RequestParam("file") MultipartFile file) {

        if (!FileUtils.isExcelFile(file)){
            throw new IllegalStateException("只允许上传 xls 和 xlsx 格式的文件");
        }

        AnalyzeExcelFileListener listener = new AnalyzeExcelFileListener(ImportSampleExcelVo.getHeadList());
        try (ExcelReader excelReader = EasyExcelFactory.read(file.getInputStream()).build()) {

            ReadSheet readSheet = EasyExcelFactory.readSheet(0).registerReadListener(listener).autoTrim(true).build();

            excelReader.read(readSheet);

            return listener.getTargetList();

        } catch (IOException e) {
            log.error("解析导入人员出错", e);
            throw new IllegalStateException("解析导入人员出错:" + e.getMessage());
        }
    }


    /**
     * 导入样本
     */
    @PostMapping("/import-samples")
    public Object physicalRegisterImportRegister(@RequestParam("hspOrgCode") String hspOrgCode,
                                                 @RequestPart("file") MultipartFile file,
                                                 @RequestPart("headSetting") List<ExcelFileHeadMappingVo> headSetting) {

        if (!FileUtils.isExcelFile(file)){
            throw new IllegalStateException("只允许上传 xls 和 xlsx 格式的文件");
        }

        //校验头文件
        headSetting.forEach(e -> {
            if (Objects.isNull(e.getExcelHead()) || Objects.isNull(e.getExcelIndex())){
                throw new IllegalStateException("请完成列【"+e.getTemplateHead()+"】的对照");
            }
        });

        //判断送检机构是否存在
        HspOrganizationDto hspOrg = hspOrganizationService.selectByHspOrgCode(hspOrgCode);
        if (Objects.isNull(hspOrg)) {
            throw new IllegalStateException("送检机构不存在!!");
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        ImportSampleRegisterListener listener = new ImportSampleRegisterListener(hspOrg, headSetting);
        listener.setOrgInfo(orgCode, orgName);
        try (ExcelReader excelReader = EasyExcelFactory.read(file.getInputStream()).build()) {

            ReadSheet readSheet = EasyExcelFactory.readSheet(0).registerReadListener(listener).autoTrim(true).build();

            excelReader.read(readSheet);

            // 检查 失败 返回 对应行相关错误信息
            List<ImportErrorResponseVo> importErrorResponseVoList = listener.getImportErrorResponseVoList();
            if (CollectionUtils.isNotEmpty(importErrorResponseVoList)) {
                throw new LimsCodeException(REGISTER_ERROR_CODE, "数据检查失败")
                        .setData(Map.of("errorList", importErrorResponseVoList.stream()
                                .sorted(Comparator.comparing(ImportErrorResponseVo::getRowNo)).collect(Collectors.toList())));
            }
            if (CollectionUtils.isEmpty(listener.getExcelDataList())){
                throw new LimsCodeException(REGISTER_ERROR_CODE, "导入数据为空");
            }
            //获取组装好的数据
            SendApplyFormInfoRequest targetRequest = listener.getFormInfoRequest();
            if (targetRequest == null){
                throw new IllegalArgumentException("数据组装失败！");
            }

            //请求业务中台接口
            Response<?> response = tbOrgApplySampleMainService.sendApplySampleInfo(targetRequest);
            if (!response.isSuccess()){
                //报错
                throw new LimsCodeException(response.getCode(), response.getMsg());
            }

            //上传到OBS 同时记录到数据库记录中
            try {
                String uploadUrl = huaweiObsUtils.upload(file.getInputStream(), "application/vnd.ms-excel", -1);
                UploadFileDto  fileDto = new UploadFileDto();
                fileDto.setFileUrl(uploadUrl);
                fileDto.setFileName(file.getOriginalFilename());
                fileDto.setFileId(IdWorker.getId());
                fileDto.setHspOrgCode(hspOrg.getHspOrgCode());
                fileDto.setHspOrgName(hspOrg.getHspOrgName());
                fileDto.setType(ImportFileEnum.EXCEL.getCode());
                fileDto.setTypeDesc(ImportFileEnum.EXCEL.getDesc());
                fileDto.setResource(ImportFileResourceEnum.SAMPLE_IMPORT.getDesc());
                fileDto.setStatus(ImportFileStatusEnum.IMPORT.getCode());
                fileDto.setHandleTime(new Date());
                fileDto.setUploaderName(loginUser.getNickname());
                uploadFileService.insertFile(fileDto);
            } catch (Exception e) {
                throw new IllegalStateException("导入样本出错,上传文件失败", e);
            }

        } catch (IOException e) {
            log.error("导入样本出错", e);
            throw new IllegalStateException("导入样本出错:" + e.getMessage());
        }

        return listener.getExcelDataList();
    }

    /**
     * 获取导入文件列表
     */
    @PostMapping("/file-list")
    public Object getImportFileList(@RequestParam("hspOrgCode") String hspOrgCode) {
        return uploadFileService.getFiles(hspOrgCode,ImportFileResourceEnum.SAMPLE_IMPORT.getDesc());
    }

    /**
     * 样本信息导入-临时
     */
    @PostMapping("/importTmpSample")
    public Object importTmpSample(@RequestBody ImportSampleDataRequestVo requestVo) {
        if (StringUtils.isAnyBlank(requestVo.getUrl(), requestVo.getOrgCode(), requestVo.getOrgName(),
                requestVo.getHspOrgCode(), requestVo.getHspOrgName())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }

        // 下载文件
        final File tempFile = FileUtil.createTempFile();
        HttpUtil.downloadFile(requestVo.getUrl(), tempFile, 15000);

        ImportSampleListener listener = new ImportSampleListener();
        //先解析
        try (ExcelReader excelReader = EasyExcelFactory.read(tempFile).build()) {
            ReadSheet readSheet = EasyExcelFactory.readSheet(0).head(ImportSample.class).registerReadListener(listener).autoTrim(true).build();

            excelReader.read(readSheet);

            // 检查 失败 返回 对应行相关错误信息
            List<String> errMsgs = listener.getErrorList();

            if (CollectionUtils.isNotEmpty(errMsgs)) {
                log.error("解析失败", JSONObject.toJSONString(errMsgs));
                throw new IllegalArgumentException("解析失败");
            }

            List<ImportSample> importSamples = listener.getCachedDataList();

            if (CollectionUtils.isEmpty(importSamples)) {
                throw new IllegalArgumentException("导入数据为空");
            }

            //再封装
            log.info("执行调用业务中台外送检接口!!!");
            // 调用接口，外送到业务中台
//            String url="http://121.36.199.164/9993/lims/sendApplySampleInfo";
            HttpRequest request = HttpUtil.createPost(requestVo.getPostUrl()).body(JSON.toJSONString(buildSendApplyFormInfo(requestVo,importSamples)));
            HttpResponse execute = request.execute();
            log.info("结果===》", JSONObject.toJSONString(execute));
        } catch (Exception e) {
            log.error("导入人员出错", e);
            throw new IllegalStateException("导入人员出错:" + e.getMessage());
        }


        return Map.of();
    }

    private SendApplyFormInfoRequest buildSendApplyFormInfo(ImportSampleDataRequestVo requestVo,List<ImportSample> importSamples) {

        String formCode = String.valueOf(IdWorker.getId());

        List<SendApplySampleInfoRequest> sendApplySampleInfoRequestList = new ArrayList<>();
        for (ImportSample importSample: importSamples) {
            // 检验项目  这里固定
            List<SendApplySampleInfoRequest.MainItem> mainItems = new ArrayList<>();
            SendApplySampleInfoRequest.MainItem tempItem = new SendApplySampleInfoRequest.MainItem();
            tempItem.setBarcode(importSample.getOutBarcode());
            tempItem.setOutTestItemCode(requestVo.getOutTestItemCode());
            tempItem.setOutTestItemName(requestVo.getOutTestItemName());
            mainItems.add(tempItem);

            // 样本信息
            SendApplySampleInfoRequest sendApplySampleInfoRequest = SendApplySampleInfoRequest.builder()
                    .barcode(importSample.getOutBarcode())
                    .hspOrgCode(requestVo.getHspOrgCode())
                    .hspOrgName(requestVo.getHspOrgName())
                    .orgCode(requestVo.getOrgCode())
                    .orgName(requestVo.getOrgName())
                    .urgent(YesOrNoEnum.NO.getCode())
                    .applyType("门诊")
                    .inpatientArea(StringPool.EMPTY)
                    .patientName(importSample.getPatientName())
                    .patientSex(SexEnum.getEnumByValue(importSample.getPatientSex()).getIntCode())
                    .patientAge(importSample.getPatientAge())
                    .samplingDate(requestVo.getSamplingDate())
                    .applyDate(requestVo.getSamplingDate())
                    .sampleSource("手动推送")
                    .targetOrgCode(requestVo.getOrgCode())
                    .targetOrgName(requestVo.getOrgName())
                    .formCode(formCode)
                    .sampleNum(1)
                    .lisCustomerCode("")
                    .lisCustomerName("")
                    .mainItems(mainItems)
                    .build();

            sendApplySampleInfoRequestList.add(sendApplySampleInfoRequest);
        }



        // 申请单信息
        return SendApplyFormInfoRequest.builder()
                .formCode(formCode)
                .hspOrgCode(requestVo.getHspOrgCode())
                .hspOrgName(requestVo.getHspOrgName())
                .orgCode(requestVo.getOrgCode())
                .orgName(requestVo.getOrgName())
                .formSource("手动推送")
                .targetOrgCode(requestVo.getOrgCode())
                .targetOrgName(requestVo.getOrgName())
                .sendSampleCount(sendApplySampleInfoRequestList.size())
                .takeSampleTime(null)
                .staffId("")
                .staffName("")
                .deliveryCode("")
                .imageUrl("")
                .handReceivePicCode("")
                .lisCustomerCode("")
                .lisCustomerName("")
                .sendRequest(sendApplySampleInfoRequestList)
                .build();
    }

}
