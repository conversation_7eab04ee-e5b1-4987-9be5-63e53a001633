package com.labway.lims.apply.service.chain.pda.apply.add;

import com.google.common.collect.Sets;
import com.labway.lims.apply.api.dto.PdaApplySampleItemDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.service.pda.PdaApplySampleItemService;
import com.labway.lims.apply.service.chain.apply.add.AddApplyContext;
import com.labway.lims.apply.service.chain.apply.add.CheckParamCommand;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <pre>
 * PdaCheckTestItemLimitSexCommand
 * 检验项目限制性别
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/10 17:52
 */
@Slf4j
@Component
public class PdaCheckTestItemLimitSexCommand implements Command {
    @Resource
    private CheckParamCommand checkParamCommand;
    @DubboReference
    private TestItemService testItemService;
    @Resource
    private PdaApplySampleItemService pdaApplySampleItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        TestApplyDto testApply = null;
        List<TestApplyDto.Item> items = List.of();
        Set<Long> existTestItemIds = Sets.newHashSet();

        if (c instanceof UpdateApplyContext) {
            final UpdateApplyContext from = UpdateApplyContext.from(c);
            testApply = from.getTestApply();
            items = testApply.getItems();

            // 查询原来的项目
            List<PdaApplySampleItemDto> sampleItemDtos = pdaApplySampleItemService.selectByPdaApplyId(testApply.getApplyId());
            existTestItemIds.addAll(sampleItemDtos.stream().map(PdaApplySampleItemDto::getTestItemId).collect(Collectors.toSet()));
        } else if (c instanceof AddApplyContext) {
            final AddApplyContext from = AddApplyContext.from(c);
            testApply = from.getTestApply();
            items = testApply.getItems();
        }

        if (Objects.isNull(testApply) || CollectionUtils.isEmpty(items)) {
            return CONTINUE_PROCESSING;
        }

        // 筛选出来新增的项目，即数据库里面没有的项目
        items = items.stream().filter(e -> !existTestItemIds.contains(e.getTestItemId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(items)) {
            // 并没有新加检验项目，跳过校验
            return CONTINUE_PROCESSING;
        }

        // 校验检验项目 限制性别 跳过
        if (BooleanUtils.isTrue(testApply.getIgnoreItemLimitSex())) {
            log.info("检验项目限制性别标识：{}", testApply.getIgnoreItemLimitSex());
            return CONTINUE_PROCESSING;
        }

        List<TestItemDto> testItems = testItemService.selectByTestItemIds(items.stream().map(TestApplyDto.Item::getTestItemId).collect(Collectors.toList()));

        checkParamCommand.checkTestItemLimitSex(testApply, testItems);

        return CONTINUE_PROCESSING;
    }

}
