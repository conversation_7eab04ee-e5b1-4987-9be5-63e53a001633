package com.labway.lims.apply.service.chain.splitblood;

import com.labway.lims.apply.api.service.ApplySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 校验申请单样本状态
 */
@Slf4j
@Component
public class SplitBloodCheckApplySampleStatusCommand implements Command {

    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SplitBloodContext context = SplitBloodContext.from(c);

        if (applySampleService.isDisabled(context.getApplySampleId())) {
            throw new IllegalStateException("样本已禁用");
        }

        if (applySampleService.isTerminate(context.getApplySampleId())) {
            throw new IllegalStateException("样本已经终止检验");
        }

        return CONTINUE_PROCESSING;
    }

}
