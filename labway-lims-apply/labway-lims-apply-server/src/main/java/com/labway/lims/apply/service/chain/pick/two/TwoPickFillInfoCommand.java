
package com.labway.lims.apply.service.chain.pick.two;

import com.alibaba.fastjson.JSONObject;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.BloodCultureTwoPickDto;
import com.labway.lims.apply.api.dto.MicrobiologyTwoPickDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.TestItemService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.labway.lims.api.enums.ExceptionCodeEnum.MICROBIOLOGY_SAMPLE_PROPERTY;

@Getter
@Component
@Slf4j
public class TwoPickFillInfoCommand implements Command {
    @DubboReference
    private TestItemService testItemService;
    @Resource
    private RackLogicService rackLogicService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @DubboReference
    private SystemParamService systemParamService;

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);

        final List<ApplySampleItemDto> applySampleItems = context.getApplySampleItems();

        // 如果是微生物or血培养二次分拣，则需要校验样本性状属性
        if ((context.getTwoPick() instanceof MicrobiologyTwoPickDto || context.getTwoPick() instanceof BloodCultureTwoPickDto)
                && Objects.equals((context.getTwoPick()).getIsValidProperty(), YesOrNoEnum.NO.getCode())) {

            // 样本信息
            ApplySampleDto applySample = context.getApplySample();
            // 申请单信息
            ApplyDto apply = context.getApply();

            // 查询微生物项目样本性状配置
            final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.MICROBIOLOGY_SAMPLE_PROPERTY.getCode(), applySample.getOrgId());

            // 判断是否配置微生物样本性状
            if (param != null && StringUtils.isNotBlank(param.getParamValue())) {
                List<String> sampleProperty = getSampleProperty(param.getParamValue(), applySample.getSampleTypeCode());

                // 判断当前样本类型是否配置微生物样本性状
                if (CollectionUtils.isNotEmpty(sampleProperty)) {
                    List<ApplySampleItemDto> itemCollect = applySampleItems.stream().filter(applySampleItem -> sampleProperty.contains(applySampleItem.getTestItemCode())).collect(Collectors.toList());

                    // 判断当前样本的项目是否包含在微生物样本性状项目配置中
                    if (CollectionUtils.isNotEmpty(itemCollect)) {

                        // 当前样本的项目属于微生物项目性状配置，需要提醒页面选择项目的样本性状
                        List<MicrobiologyItemProperty> propertyItems = itemCollect.stream().map(e -> this.convertProperty(e, applySample)).collect(Collectors.toList());
                        throw new LimsCodeException(MICROBIOLOGY_SAMPLE_PROPERTY.getCode(), MICROBIOLOGY_SAMPLE_PROPERTY.getDesc())
                                .setData(JSONObject.toJSONString(propertyItems));
                    }
                }

            }

        }

        // 获取逻辑试管架
        final List<RackLogicDto> rackLogics = rackLogicService.selectByApplySampleId(context.getTwoPick().getApplySampleId());
        if (CollectionUtils.isEmpty(rackLogics)) {
            throw new IllegalStateException("获取逻辑试管架失败");
        }

        final List<RackLogicSpaceDto> rackLogicSpaces = rackLogicSpaceService.selectByApplySampleId(context.getTwoPick().getApplySampleId());
        if (CollectionUtils.isEmpty(rackLogicSpaces)) {
            throw new IllegalStateException("获取逻辑试管架位置失败");
        }

        context.put(TwoPickContext.RACK_LOGIC_SPACE, rackLogicSpaces.iterator().next());
        context.put(TwoPickContext.RACK_LOGIC, rackLogics.iterator().next());


        return CONTINUE_PROCESSING;
    }



    // 根据字典值获取样本性状配置
    private List<String> getSampleProperty(String paramValue, String sampleType) {
        if (StringUtils.isBlank(paramValue) || StringUtils.isBlank(sampleType)){
            return Collections.emptyList();
        }

        // 转化微生物项目性状配置
        List<microbiologySampleProperty> microbiologySampleProperties = JSONObject.parseArray(paramValue, microbiologySampleProperty.class);

        // 获取指定样本类型的性状配置
        String itemStr = microbiologySampleProperties.stream()
                .filter(microbiologySampleProperty -> Objects.equals(microbiologySampleProperty.getSampleTypeCode(), sampleType))
                .findFirst()
                .map(microbiologySampleProperty::getTestItemCode)
                .orElse(StringUtils.EMPTY);

        if (StringUtils.isBlank(itemStr)){
            return Collections.emptyList();
        }

        return Arrays.asList(itemStr.split(","));
    }


    private MicrobiologyItemProperty convertProperty(ApplySampleItemDto applySampleItemDto,ApplySampleDto applySample) {
        return new MicrobiologyItemProperty(applySampleItemDto.getTestItemId(),applySampleItemDto.getTestItemCode(),applySampleItemDto.getTestItemName(),applySample.getSamplePropertyCode(),applySample.getSampleProperty());
    }



    @Data
    public static final class microbiologySampleProperty implements Serializable{

        // 样本类型编码
        private String sampleTypeCode;

        // 检验项目编码
        private String testItemCode;

    }

    @Data
    @AllArgsConstructor
    public static final class MicrobiologyItemProperty implements Serializable{

        // 项目id
        private Long itemId;

        // 项目编码
        private String itemCode;

        // 项目名称
        private String itemName;

        // 项目性状属性编码
        private String propertyCode;

        // 项目性状属性名称
        private String propertyName;

    }


}
