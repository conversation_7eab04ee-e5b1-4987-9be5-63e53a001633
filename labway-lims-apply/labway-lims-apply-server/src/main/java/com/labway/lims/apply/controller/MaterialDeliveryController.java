package com.labway.lims.apply.controller;

import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.MaterialDeliveryRecordStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.MaterialDeliveryDetailService;
import com.labway.lims.apply.api.service.MaterialDeliveryRecordService;
import com.labway.lims.apply.api.service.MaterialIncomeRecordService;
import com.labway.lims.apply.api.service.MaterialRefundRecordService;
import com.labway.lims.apply.mapstruct.MaterialDeliveryConverter;
import com.labway.lims.apply.vo.*;
import com.labway.lims.base.api.dto.MaterialDto;
import com.labway.lims.base.api.service.MaterialService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料出入库 API
 *
 * <AUTHOR>
 * @since 2023/5/6 15:57
 */

@Slf4j
@RestController
@RequestMapping("/material-delivery")
public class MaterialDeliveryController extends BaseController {

    @Resource
    private MaterialDeliveryRecordService materialDeliveryRecordService;

    @Resource
    private MaterialDeliveryDetailService materialDeliveryDetailService;
    @Resource
    private MaterialIncomeRecordService materialIncomeRecordService;
    @Resource
    private MaterialDeliveryConverter materialDeliveryConverter;
    @DubboReference
    private PdfReportService pdfReportService;
    @DubboReference
    private MaterialService materialService;

    @Resource
    private MaterialRefundRecordService materialRefundRecordService;

    /***
     * 出库详情
     */
    @GetMapping("/out-bound-order-details")
    public Object outBoundOrderDetails(@RequestParam(required = false) String deliveryNo) {
        if (StringUtils.isBlank(deliveryNo)) {
            return Collections.emptyList();
        }

        return materialDeliveryDetailService.selectByDeliveryNo(deliveryNo, LoginUserHandler.get().getOrgId());
    }

    /**
     * 根据申请单查询出库单
     */
    @GetMapping("/out-bound-orders")
    public Object outBoundOrders(@RequestParam(required = false) Long applyId) {
        if (Objects.isNull(applyId)) {
            return Collections.emptyList();
        }

        return materialDeliveryRecordService.outBoundOrders(applyId);
    }

    /**
     * 待入库 列表
     */
    @PostMapping("/wait-list")
    public Object materialDeliveryWaitList(@RequestBody MaterialDeliveryWaitListRequestVo vo) {

        SelectMaterialDeliveryRecordDto selectMaterialDeliveryRecordDto =
            materialDeliveryConverter.fromMaterialDeliveryWaitListRequestVo(vo);

        LoginUserHandler.User user = LoginUserHandler.get();
        selectMaterialDeliveryRecordDto.setGroupId(user.getGroupId());
        selectMaterialDeliveryRecordDto.setStatus(MaterialDeliveryRecordStatusEnum.NOT_WAREHOUSED.getCode());

        return materialDeliveryRecordService.selectBySelectMaterialDeliveryRecordDto(selectMaterialDeliveryRecordDto);
    }

    /**
     * 已入库 列表 完成
     */
    @PostMapping("/finish-list")
    public Object materialDeliveryFinishList(@RequestBody MaterialDeliveryFinishListRequestVo vo) {

        SelectMaterialIncomeInfoDto selectMaterialIncomeInfoDto =
            materialDeliveryConverter.fromMaterialDeliveryFinishListRequestVo(vo);

        LoginUserHandler.User user = LoginUserHandler.get();
        selectMaterialIncomeInfoDto.setGroupId(user.getGroupId());
        selectMaterialIncomeInfoDto.setStatus(MaterialDeliveryRecordStatusEnum.NOT_WAREHOUSED.getCode());

        // 入库信息
        List<MaterialIncomeInfoDto> materialIncomeInfoDtos =
            materialIncomeRecordService.selectBySelectMaterialIncomeInfoDto(selectMaterialIncomeInfoDto);

        // 所有出库单号
        Set<String> deliveryNoList =
            materialIncomeInfoDtos.stream().map(MaterialIncomeInfoDto::getDeliveryNo).collect(Collectors.toSet());

        // 对应出库信息
        List<MaterialDeliveryRecordDto> materialDeliveryRecordDtos =
            materialDeliveryRecordService.selectByDeliveryNos(deliveryNoList, user.getOrgId());

        // 正常数据 一个出库单号 对应一条信息
        Map<String, MaterialDeliveryRecordDto> materialDeliveryRecordByDeliveryNo = materialDeliveryRecordDtos.stream()
            .collect(Collectors.toMap(MaterialDeliveryRecordDto::getDeliveryNo, Function.identity()));

        // 构造响应数据
        List<MaterialDeliveryFinishListResponseVo> targetList =
            Lists.newArrayListWithCapacity(materialIncomeInfoDtos.size());
        materialIncomeInfoDtos.forEach(item -> {
            MaterialDeliveryFinishListResponseVo target = materialDeliveryConverter.fromMaterialIncomeInfoDto(item);

            MaterialDeliveryRecordDto deliveryRecordDto = materialDeliveryRecordByDeliveryNo.get(item.getDeliveryNo());

            // 拼接 出库信息
            if (Objects.nonNull(deliveryRecordDto)) {
                target.setRecordId(deliveryRecordDto.getRecordId());
                target.setDeliveryDate(deliveryRecordDto.getDeliveryDate());
                target.setDeliveryUser(deliveryRecordDto.getDeliveryUser());
            }

            targetList.add(target);

        });

        // 按照入库时间倒序
        return targetList.stream()
            .sorted(Comparator.comparing(MaterialDeliveryFinishListResponseVo::getIncomeDate, Comparator.reverseOrder())
                .thenComparing(MaterialDeliveryFinishListResponseVo::getRecordId))
            .collect(Collectors.toList());

    }

    /**
     * 出入库详情
     */
    @PostMapping("/detail")
    public Object materialDeliveryDetail(@RequestParam("recordId") long recordId) {

        MaterialDeliveryRecordDto deliveryRecordDto = materialDeliveryRecordService.selectByRecordId(recordId);
        if (Objects.isNull(deliveryRecordDto)) {
            throw new LimsException("无效记录id");
        }
        // 出库单号
        String deliveryNo = deliveryRecordDto.getDeliveryNo();

        LoginUserHandler.User user = LoginUserHandler.get();
        if (Objects.equals(deliveryRecordDto.getStatus(), MaterialDeliveryRecordStatusEnum.NOT_WAREHOUSED.getCode())) {
            // 未入库
            List<MaterialDeliveryDetailDto> deliveryDetailDtoList =
                materialDeliveryDetailService.selectByDeliveryNo(deliveryNo, user.getOrgId());

            //从物料表中补充存储温度 注册证号 注册证名称
            Set<Long> materialIds = deliveryDetailDtoList.stream().map(MaterialDeliveryDetailDto::getMaterialId).collect(Collectors.toSet());
            Map<Long, MaterialDto> materialDtoMap = getMaterialMapByIds(materialIds);
            return materialDeliveryConverter.fromMaterialDeliveryDetailDtoList(deliveryDetailDtoList).stream()
                    .peek(m -> {

                        MaterialDto materialDto = materialDtoMap.getOrDefault(m.getMaterialId(),null);
                        if (Objects.nonNull(materialDto)){
                            m.setStorageTemperature(materialDto.getStorageTemperature());
                            m.setRegistrationName(materialDto.getRegistrationName());
                            m.setRegistrationNumber(materialDto.getRegistrationNumber());
                        }
                        // 默认 入库主数量
                        if (Objects.isNull(m.getIncomeMainNumber())) {
                            m.setIncomeMainNumber(m.getDeliveryMainNumber());
                        }
                        // 默认 入库辅数量
                        if (Objects.isNull(m.getIncomeAssistNumber())) {
                            m.setIncomeAssistNumber(m.getDeliveryAssistNumber());
                        }

                        //其他字段默认值
                        if(Objects.isNull(m.getIfStorageQualified())){
                            m.setIfStorageQualified(YesOrNoEnum.YES.getCode());
                        }
                        if(Objects.isNull(m.getIfSpecQuantityConsistent())){
                            m.setIfSpecQuantityConsistent(YesOrNoEnum.YES.getCode());
                        }
                        if(Objects.isNull(m.getIfPackageDamaged())){
                            m.setIfPackageDamaged(YesOrNoEnum.NO.getCode());
                        }
                        if(Objects.isNull(m.getIfValidDateQualified())){
                            m.setIfValidDateQualified(YesOrNoEnum.YES.getCode());
                        }
                        if(Objects.isNull(m.getAcceptanceConclusion())){
                            m.setAcceptanceConclusion(YesOrNoEnum.YES.getCode());
                        }
                    })
                    .sorted(Comparator.comparing(MaterialDeliveryDetailItemVo::getMaterialCode))
                    .collect(Collectors.toList());
        }

        // 已入库
        List<MaterialIncomeRecordDto> materialIncomeRecordDtos =
            materialIncomeRecordService.selectByDeliveryNo(deliveryNo, user.getOrgId());

        // 根据materialIds去查询物料信息中的温度、注册证号、注册证名称，然后返回并设置进去
        Set<Long> materialIds = materialIncomeRecordDtos.stream().map(MaterialIncomeRecordDto::getMaterialId).collect(Collectors.toSet());
        Map<Long, MaterialDto> materialDtoMap = getMaterialMapByIds(materialIds);

        materialIncomeRecordDtos.forEach(m ->{

            MaterialDto materialDto = materialDtoMap.getOrDefault(m.getMaterialId(),null);
            if (Objects.nonNull(materialDto)){
                m.setStorageTemperature(materialDto.getStorageTemperature());
                m.setRegistrationName(materialDto.getRegistrationName());
                m.setRegistrationNumber(materialDto.getRegistrationNumber());
            }

            // 默认 入库主数量
            if (Objects.isNull(m.getIncomeMainNumber())) {
                m.setIncomeMainNumber(m.getDeliveryMainNumber());
            }
            // 默认 入库辅数量
            if (Objects.isNull(m.getIncomeAssistNumber())) {
                m.setIncomeAssistNumber(m.getDeliveryAssistNumber());
            }

            //其他字段默认值
            if(Objects.isNull(m.getIfStorageQualified())){
                m.setIfStorageQualified(YesOrNoEnum.YES.getCode());
            }
            if(Objects.isNull(m.getIfSpecQuantityConsistent())){
                m.setIfSpecQuantityConsistent(YesOrNoEnum.YES.getCode());
            }
            if(Objects.isNull(m.getIfPackageDamaged())){
                m.setIfPackageDamaged(YesOrNoEnum.NO.getCode());
            }
            if(Objects.isNull(m.getIfValidDateQualified())){
                m.setIfValidDateQualified(YesOrNoEnum.YES.getCode());
            }
            if(Objects.isNull(m.getAcceptanceConclusion())){
                m.setAcceptanceConclusion(YesOrNoEnum.YES.getCode());
            }
        });

        return materialDeliveryConverter.fromMaterialIncomeRecordDtoList(materialIncomeRecordDtos).stream()
            .sorted(Comparator.comparing(MaterialDeliveryDetailItemVo::getMaterialCode)).collect(Collectors.toList());
    }

    /**
     * 根据materialIds查询物料信息
     * @param materialIds
     * @return
     */
    private Map<Long, MaterialDto> getMaterialMapByIds(Set<Long> materialIds) {
        if (CollectionUtils.isEmpty(materialIds)){
            return Collections.emptyMap();
        }
        List<MaterialDto> materialDtos = materialService.selectByIds(materialIds);
        return materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, Function.identity()));
    }

    /**
     * 待入库 - 入库操作
     */
    @PostMapping("/material-income")
    public Object materialDeliveryIncome(@RequestBody MaterialDeliveryIncomeRequestVo vo) {

        materialIncomeRecordService.materialIncomeByRecordId(vo.getDeliveryNo(), vo.getIncomeItemList());

        return Collections.emptyMap();
    }

    /**
     * 打印入库单
     */
    @PostMapping("/print")
    public Object materialDeliveryPrint(@RequestParam(value = "recordId") long recordId) {

        MaterialDeliveryRecordDto deliveryRecordDto = materialDeliveryRecordService.selectByRecordId(recordId);
        if (Objects.isNull(deliveryRecordDto)) {
            throw new LimsException("无效记录id");
        }
        if (!Objects.equals(deliveryRecordDto.getStatus(), MaterialDeliveryRecordStatusEnum.WAREHOUSED.getCode())) {
            throw new LimsException("只有已入库单可打印");
        }
        // 出库单号
        String deliveryNo = deliveryRecordDto.getDeliveryNo();

        LoginUserHandler.User user = LoginUserHandler.get();
        // 已入库
        List<MaterialIncomeRecordDto> materialIncomeRecordDtos =
            materialIncomeRecordService.selectByDeliveryNo(deliveryNo, user.getOrgId());
        List<MaterialDeliveryDetailItemVo> detailItemVos = materialDeliveryConverter
            .fromMaterialIncomeRecordDtoList(materialIncomeRecordDtos).stream()
            .sorted(Comparator.comparing(MaterialDeliveryDetailItemVo::getMaterialCode)).collect(Collectors.toList());

        final PdfReportParamDto param = new PdfReportParamDto();

        param.put("deliveryRecord", deliveryRecordDto);
        param.put("detailItems", detailItemVos);
        param.put("incomeNo", materialIncomeRecordDtos.get(NumberUtils.INTEGER_ZERO).getIncomeNo());
        param.put("incomeUser", materialIncomeRecordDtos.get(NumberUtils.INTEGER_ZERO).getCreatorName());
        param.put("incomeDate", materialIncomeRecordDtos.get(NumberUtils.INTEGER_ZERO).getCreateDate());

        final String url = pdfReportService.build2Url(PdfTemplateTypeEnum.WL_RK.getCode(), param);

        return Map.of("url", url);
    }

    /**
     * 入库物料拒收
     * @param dto
     * @return
     */
    @PostMapping("/rejection")
    public Object rejection(@RequestBody MaterialRecordIdDto dto){
        dto.verifyParams();
        materialRefundRecordService.rejection(dto);
        return true;
    }
}
