package com.labway.lims.apply.vo;

import com.labway.lims.apply.api.dto.RackArchiveDto;
import lombok.Getter;
import lombok.Setter;

/**
 * 查询冰箱下 归档试管架相关信息
 *
 * <AUTHOR>
 * @since 2023/4/17 16:17
 */
@Getter
@Setter
public class SelectRackArchiveResponseVo extends RackArchiveDto {
    /**
     * 试管架类型编码
     */
    private String rackTypeCode;
    /**
     * 试管架类型名称
     */
    private String rackTypeName;

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 试管架名称
     */
    private String rackName;

    /**
     * 多少行
     */
    private Integer row;

    /**
     * 多少列
     */
    private Integer column;

    /**
     * 可存放数量
     */
    private Integer availableNum;
    /**
     * 已存放 数量
     */
    private Integer havingNum;
    /**
     * 规格
     */
    private String specDesc;

}
