
package com.labway.lims.apply.service.chain.sample.archive.add;

import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicEffectiveSpaceDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.vo.RackArchiveAddRequestVo;
import com.labway.lims.base.api.dto.RackHoleRuleDto;
import com.labway.lims.base.api.service.RackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 填充 试管架样本占用
 * 
 * <AUTHOR>
 * @since 2023/4/14 13:18
 */
@Slf4j
@Component
public class SampleArchiveAddRackLogicSpaceCommand implements Command {

    @DubboReference
    private RackService rackService;

    @DubboReference
    private RackLogicSpaceService rackLogicSpaceService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SampleArchiveAddContext from = SampleArchiveAddContext.from(context);
        final RackLogicDto rackLogic = from.getRackLogic();
        final List<ApplySampleDto> applySamples = from.getApplySamples();
        RackArchiveAddRequestVo vo = from.getRackArchiveAddRequestVo();

        // 当前试管架 对应 孔位排序 规则
        final RackHoleRuleDto rackHoleRuleDto = rackService.selectRackHoleRuleByRackId(rackLogic.getRackId());

        int startRow = 0;
        int startColumn = 0;
        if (vo.isFromAssign()) {
            startRow = vo.getRow();
            startColumn = vo.getColumn();
        }
        // 根据 孔位规则 获取 下一个 试管架可用孔位空间
        final RackLogicEffectiveSpaceDto nextSpaceByHoleRule = rackLogicSpaceService
            .selectNextSpaceByRackLogicAndHoleRule(rackLogic, startRow, startColumn, rackHoleRuleDto);
        if (Objects.isNull(nextSpaceByHoleRule)) {
            throw new LimsException("试管架已无可用孔位");
        }

        final List<RackLogicSpaceDto> rackLogicSpaceDtoList = applySamples.stream().map(applySample -> {

            RackLogicSpaceDto rackLogicSpaceDto = new RackLogicSpaceDto();
            rackLogicSpaceDto.setRackLogicSpaceId(snowflakeService.genId());
            rackLogicSpaceDto.setRackLogicId(rackLogic.getRackLogicId());
            rackLogicSpaceDto.setRackId(rackLogic.getRackId());
            rackLogicSpaceDto.setRow(nextSpaceByHoleRule.getRow());
            rackLogicSpaceDto.setColumn(nextSpaceByHoleRule.getColumn());
            rackLogicSpaceDto.setApplySampleId(applySample.getApplySampleId());
            rackLogicSpaceDto.setStatus(1);
            return rackLogicSpaceDto;
        }).collect(Collectors.toList());

        from.put(SampleArchiveAddContext.RACK_LOGIC_SPACES, rackLogicSpaceDtoList);

        return CONTINUE_PROCESSING;
    }
}
