package com.labway.lims.apply.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.business.center.third.ncc.request.MaterialPurchasePlanCreateRequest;
import com.labway.business.center.third.ncc.service.LimsApiService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.MaterialNoType;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.AddGroupMaterialPlanDto;
import com.labway.lims.apply.api.dto.ApprovalPlanDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDto;
import com.labway.lims.apply.api.dto.GroupMaterialPlanDto;
import com.labway.lims.apply.api.dto.QueryAllUnApprovalPlanListDto;
import com.labway.lims.apply.api.dto.QueryApprovalPlanPageDto;
import com.labway.lims.apply.api.dto.ReturnPlanDto;
import com.labway.lims.apply.api.dto.SelectGroupMaterialPlanDto;
import com.labway.lims.apply.api.enums.GroupMaterialPlanEnum;
import com.labway.lims.apply.api.enums.MaterialApplyTypeEnum;
import com.labway.lims.apply.api.service.GroupMaterialApplyService;
import com.labway.lims.apply.api.service.GroupMaterialPlanService;
import com.labway.lims.apply.api.vo.ApprovalPlanVo;
import com.labway.lims.apply.api.vo.GroupMaterialPlanApprovalVo;
import com.labway.lims.apply.api.vo.GroupMaterialPlanVo;
import com.labway.lims.apply.api.vo.ReturnPlanVo;
import com.labway.lims.apply.mapper.GroupMaterialPlanMapper;
import com.labway.lims.apply.model.TbGroupMaterialPlan;
import com.labway.lims.base.api.dto.GroupMaterialDetailDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.aop.framework.AopContext;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.NonNull;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.api.EnvDetector.BUSINESS_CENTER_ORG_CODE;

/**
 * @description 物料专业组计划
 * <AUTHOR>
 * @date 2024-07-10
 */
@Slf4j
@DubboService
public class GroupMaterialPlanServiceImpl extends ServiceImpl<GroupMaterialPlanMapper, TbGroupMaterialPlan> implements GroupMaterialPlanService {

    @Resource
    private Environment environment;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private GroupMaterialPlanMapper groupMaterialPlanMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private GroupMaterialService groupMaterialService;

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyMMdd");
    @Resource
    private LimsApiService limsApiService2Impl;
    @Resource
    private GroupMaterialApplyService groupMaterialApplyService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addGroupMaterialPlan(List<AddGroupMaterialPlanDto> addGroupMaterialPlanDtoList) {
        final String redisKey = redisPrefix.getBasePrefix() + "material:group:plan:add";
        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(redisKey, Strings.EMPTY))) {
            throw new IllegalStateException("正在新增物料专业组计划中， 请稍后");
        }
        try {
            final LoginUserHandler.User user = LoginUserHandler.get();

            // 校验是否在本专业存在
            final Set<String> materialCodes = addGroupMaterialPlanDtoList.stream().map(AddGroupMaterialPlanDto::getMaterialCode).collect(Collectors.toSet());
            this.checkMaterialExist(materialCodes, user);

            // 计划单号
            final String planNo = this.createPlanNo(user.getOrgId());

            final LinkedList<Long> ids = snowflakeService.genIds(addGroupMaterialPlanDtoList.size());

            // 构建数据
            final List<TbGroupMaterialPlan> tbGroupMaterialPlanList = JSON.parseArray(JSON.toJSONString(addGroupMaterialPlanDtoList), TbGroupMaterialPlan.class);
            for (TbGroupMaterialPlan tbGroupMaterialPlan : tbGroupMaterialPlanList) {
                tbGroupMaterialPlan.setGroupMaterialPlanId(ids.pop());
                tbGroupMaterialPlan.setPlanNo(planNo);
            }

            final boolean saveBool = this.saveMaterialGroupPlanList(tbGroupMaterialPlanList);

            log.info("专业组物料计划新增，保存人：{}, 计划单号：{}, 保存物料codes：{}， 保存是否成功：{}", user.getNickname(), planNo, CollUtil.join(materialCodes, ","), saveBool);

            return saveBool;
        } finally {
            this.deleteRedisPlanLock(redisKey);
        }

    }

    private boolean saveMaterialGroupPlanList(List<TbGroupMaterialPlan> tbGroupMaterialPlanList) {
        final Date date = new Date();
        final LoginUserHandler.User user = LoginUserHandler.get();
        if (CollectionUtils.isEmpty(tbGroupMaterialPlanList)) {
            log.warn("用户：{}， 保存了 0 个物料计划", user.getNickname());
            return true;
        }
        final LinkedList<Long> ids = snowflakeService.genIds(tbGroupMaterialPlanList.size());

        for (TbGroupMaterialPlan tbGroupMaterialPlan : tbGroupMaterialPlanList) {
            tbGroupMaterialPlan.setGroupMaterialPlanId(ids.pop());

            tbGroupMaterialPlan.setGroupId((Long) ObjectUtils.defaultIfNull(tbGroupMaterialPlan.getGroupId(), user.getGroupId()));
            tbGroupMaterialPlan.setGroupCode(StringUtils.defaultString(tbGroupMaterialPlan.getGroupCode(), user.getGroupCode()));
            tbGroupMaterialPlan.setGroupName(StringUtils.defaultString(tbGroupMaterialPlan.getGroupName(), user.getGroupName()));

            tbGroupMaterialPlan.setOrgId((Long) ObjectUtils.defaultIfNull(tbGroupMaterialPlan.getOrgId(), user.getOrgId()));
            tbGroupMaterialPlan.setOrgName(StringUtils.defaultString(tbGroupMaterialPlan.getOrgName(), user.getOrgName()));

            tbGroupMaterialPlan.setStatus(GroupMaterialPlanEnum.SAVE.getCode());
            tbGroupMaterialPlan.setIsDelete(YesOrNoEnum.NO.getCode());

            tbGroupMaterialPlan.setPlannerId((Long) ObjectUtils.defaultIfNull(tbGroupMaterialPlan.getPlannerId(), user.getUserId()));
            tbGroupMaterialPlan.setPlannerName(StringUtils.defaultString(tbGroupMaterialPlan.getPlannerName(), user.getNickname()));
            tbGroupMaterialPlan.setPlannerDate((Date) ObjectUtils.defaultIfNull(tbGroupMaterialPlan.getPlannerDate(), date));

            tbGroupMaterialPlan.setCreatorId((Long) ObjectUtils.defaultIfNull(tbGroupMaterialPlan.getCreatorId(), user.getUserId()));
            tbGroupMaterialPlan.setCreatorName(StringUtils.defaultString(tbGroupMaterialPlan.getCreatorName(), user.getNickname()));
            tbGroupMaterialPlan.setCreateDate((Date) ObjectUtils.defaultIfNull(tbGroupMaterialPlan.getCreateDate(), date));

            tbGroupMaterialPlan.setUpdaterId((Long) ObjectUtils.defaultIfNull(tbGroupMaterialPlan.getUpdaterId(), user.getUserId()));
            tbGroupMaterialPlan.setUpdaterName(StringUtils.defaultString(tbGroupMaterialPlan.getUpdaterName(), user.getNickname()));
            tbGroupMaterialPlan.setUpdateDate((Date) ObjectUtils.defaultIfNull(tbGroupMaterialPlan.getUpdateDate(), date));

            tbGroupMaterialPlan.setAuditId(NumberUtils.LONG_ONE);
            tbGroupMaterialPlan.setAuditName(Strings.EMPTY);
            tbGroupMaterialPlan.setAuditDate(DefaultDateEnum.DEFAULT_DATE.getDate());

            tbGroupMaterialPlan.setSubmitId(NumberUtils.LONG_ONE);
            tbGroupMaterialPlan.setSubmitName(Strings.EMPTY);
            tbGroupMaterialPlan.setSubmitDate(DefaultDateEnum.DEFAULT_DATE.getDate());

            tbGroupMaterialPlan.setReturnReason(Strings.EMPTY);
            tbGroupMaterialPlan.setReturnUserId(NumberUtils.LONG_ZERO);
            tbGroupMaterialPlan.setReturnUserName(Strings.EMPTY);
            tbGroupMaterialPlan.setReturnDate(DefaultDateEnum.DEFAULT_DATE.getDate());
            tbGroupMaterialPlan.setRemark(StringUtils.defaultString(tbGroupMaterialPlan.getRemark()));
        }
        return super.saveBatch(tbGroupMaterialPlanList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateGroupMaterialPlanByPlanNo(String planNo, List<AddGroupMaterialPlanDto> addGroupMaterialPlanDtoList) {

        // 获取锁
        final String redisKey = getRedisPlanLock(planNo);

        try {
            final LoginUserHandler.User user = LoginUserHandler.get();

            final List<GroupMaterialPlanDto> groupMaterialPlanDtos = this.selectByPlanNo(planNo);
            if (CollectionUtils.isEmpty(groupMaterialPlanDtos)) {
                throw new IllegalArgumentException("计划单号无效");
            }
            // 校验单子状态是否是已保存
            final GroupMaterialPlanDto tmpTbGroupMaterialPlan = groupMaterialPlanDtos.get(NumberUtils.INTEGER_ZERO);
            this.checkGroupMaterialPlanStatus(tmpTbGroupMaterialPlan.getStatus(), GroupMaterialPlanEnum.SAVE);

            // 校验是否在本专业存在
            final Set<String> materialCodes = addGroupMaterialPlanDtoList.stream().map(AddGroupMaterialPlanDto::getMaterialCode).collect(Collectors.toSet());
            this.checkMaterialExist(materialCodes, user);

            // 数据库中已存在的物料编码  和 计划信息
            final Map<String, GroupMaterialPlanDto> groupPlanDtoMap = groupMaterialPlanDtos.stream().collect(Collectors.toMap(GroupMaterialPlanDto::getMaterialCode, Function.identity(), (a, b) -> b));

            final List<TbGroupMaterialPlan> tbGroupMaterialPlanList = JSON.parseArray(JSON.toJSONString(addGroupMaterialPlanDtoList), TbGroupMaterialPlan.class);

            // 循环需要保存的值
            for (TbGroupMaterialPlan tbGroupMaterialPlan : tbGroupMaterialPlanList) {
                // 存在库中
                final GroupMaterialPlanDto groupMaterialPlanDto = groupPlanDtoMap.get(tbGroupMaterialPlan.getMaterialCode());
                tbGroupMaterialPlan.setPlanNo(planNo);
                // 计划人数据保持旧值
                tbGroupMaterialPlan.setPlannerId(tmpTbGroupMaterialPlan.getPlannerId());
                tbGroupMaterialPlan.setPlannerName(tmpTbGroupMaterialPlan.getPlannerName());
                tbGroupMaterialPlan.setPlannerDate(tmpTbGroupMaterialPlan.getPlannerDate());

                if (Objects.nonNull(groupMaterialPlanDto)) {
                    // 创建人保持旧值
                    tbGroupMaterialPlan.setCreatorId(tbGroupMaterialPlan.getCreatorId());
                    tbGroupMaterialPlan.setCreatorName(tbGroupMaterialPlan.getCreatorName());
                    tbGroupMaterialPlan.setCreateDate(tbGroupMaterialPlan.getCreateDate());
                }
            }
            // 先删除
            final int deleteCount = ((GroupMaterialPlanService) AopContext.currentProxy()).deleteByPlanNo(planNo);

            // 保存
            final boolean saveBool = this.saveMaterialGroupPlanList(tbGroupMaterialPlanList);

            log.info("专业组物料计划修改，修改人：{},计划单号：{}, 删除数量：{}， 保存物料codes：{}， 保存是否成功：{}", user.getNickname(), planNo, deleteCount, CollUtil.join(materialCodes, ","), saveBool);

            return saveBool;

        } finally {
            this.deleteRedisPlanLock(redisKey);
        }
    }


    /**
     * @param status 数据库条码的状态
     * @param statusEnum 状态
     */
    private void checkGroupMaterialPlanStatus(Integer status, GroupMaterialPlanEnum statusEnum) {
        final GroupMaterialPlanEnum dbStatusEnum = GroupMaterialPlanEnum.getByCode(status);
        if (Objects.isNull(dbStatusEnum)) {
            throw new IllegalArgumentException("计划单号状态错误");
        }
        if (!Objects.equals(statusEnum, dbStatusEnum)) {
            throw new IllegalArgumentException("当前单据状态已更新，请点击查询更新单据状态");
        }
    }

    @Override
    public List<GroupMaterialPlanDto> selectByPlanNo(String planNo) {
        if (StringUtils.isBlank(planNo)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbGroupMaterialPlan> wrapper = new LambdaQueryWrapper<TbGroupMaterialPlan>()
                .eq(TbGroupMaterialPlan::getPlanNo, planNo)
                .orderByAsc(TbGroupMaterialPlan::getCreateDate);

        return this.convert(groupMaterialPlanMapper.selectList(wrapper));
    }

    @Override
    public List<GroupMaterialPlanVo> selectByGroupId(long groupId) {
        final LambdaQueryWrapper<TbGroupMaterialPlan> wrapper = new LambdaQueryWrapper<TbGroupMaterialPlan>()
                .eq(TbGroupMaterialPlan::getGroupId, groupId)
                .orderByDesc(TbGroupMaterialPlan::getCreateDate);
        final List<TbGroupMaterialPlan> tbGroupMaterialPlans = groupMaterialPlanMapper.selectList(wrapper);
        // 根据计划单号分组
        return this.getGroupMaterialPlanVoList(tbGroupMaterialPlans);
    }

    @Override
    public List<GroupMaterialPlanVo> selectAll(SelectGroupMaterialPlanDto dto) {
        final LambdaQueryWrapper<TbGroupMaterialPlan> wrapper = new LambdaQueryWrapper<TbGroupMaterialPlan>()
                .eq(Objects.nonNull(dto.getGroupId()), TbGroupMaterialPlan::getGroupId, dto.getGroupId())
                .eq(Objects.nonNull(dto.getStatus()), TbGroupMaterialPlan::getStatus, dto.getStatus())
                .lt(Objects.nonNull(dto.getEndPlannerDate()), TbGroupMaterialPlan::getPlannerDate, dto.getEndPlannerDate())
                .gt(Objects.nonNull(dto.getStartPlannerDate()), TbGroupMaterialPlan::getPlannerDate, dto.getStartPlannerDate())
                .and(StringUtils.isNotBlank(dto.getMaterialCodeOrName()),
                        e -> e.like(TbGroupMaterialPlan::getMaterialCode, dto.getMaterialCodeOrName())
                                .or().like(TbGroupMaterialPlan::getMaterialName, dto.getMaterialCodeOrName()))
                .orderByDesc(TbGroupMaterialPlan::getCreateDate);

        final List<TbGroupMaterialPlan> tbGroupMaterialPlans = groupMaterialPlanMapper.selectList(wrapper);
        return this.getGroupMaterialPlanVoList(tbGroupMaterialPlans);
    }

    private List<GroupMaterialPlanVo> getGroupMaterialPlanVoList(List<TbGroupMaterialPlan> tbGroupMaterialPlans) {
        if (CollUtil.isEmpty(tbGroupMaterialPlans)) {
            return Collections.emptyList();
        }

        // 根据计划单号分组
        List<GroupMaterialPlanVo> collect = tbGroupMaterialPlans.stream().collect(Collectors.groupingBy(TbGroupMaterialPlan::getPlanNo))
                // 拿分组后的值
                .values().stream()
                .map(tbGroupMaterialPlanList -> {
                    // 计划信息
                    final GroupMaterialPlanVo groupMaterialPlanVo = JSON.parseObject(JSON.toJSONString(tbGroupMaterialPlanList.get(NumberUtils.INTEGER_ZERO)), GroupMaterialPlanVo.class);
                    groupMaterialPlanVo.clearDefaultDate();
                    // 计划中的物料信息
                    final List<GroupMaterialPlanVo.GroupMaterialPlanDetailVo> groupMaterialPlanDetailVos = JSON.parseArray(JSON.toJSONString(tbGroupMaterialPlanList), GroupMaterialPlanVo.GroupMaterialPlanDetailVo.class);
                    groupMaterialPlanVo.setGroupMaterialPlanDetailList(groupMaterialPlanDetailVos);
                    return groupMaterialPlanVo;
                })
                // 根据时间排序
                .sorted((a, b) -> b.getPlannerDate().compareTo(a.getPlannerDate()))
                .collect(Collectors.toList());
        collect.forEach(e -> e.setPlanType(MaterialApplyTypeEnum.MATERIAL_PLAN.getCode()));
        return collect;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteByPlanNo(String planNo) {
        final LambdaUpdateWrapper<TbGroupMaterialPlan> wrapper = new LambdaUpdateWrapper<TbGroupMaterialPlan>()
                .eq(TbGroupMaterialPlan::getPlanNo, planNo);

        final int deleteCount = groupMaterialPlanMapper.delete(wrapper);
        log.info("专业组物料计划删除，删除人：{},计划单号：{}, 删除数量：{}", LoginUserHandler.get().getNickname(), planNo, deleteCount);
        return deleteCount;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitPlan(String planNo) {
        // 获取锁
        final String redisKey = this.getRedisPlanLock(planNo);
        try {
            this.updateGroupMaterialPlanStatus(planNo, GroupMaterialPlanEnum.SAVE, GroupMaterialPlanEnum.SUBMIT);
        } finally {
            this.deleteRedisPlanLock(redisKey);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void repealSubmitPlan(String planNo) {
        // 获取锁
        final String redisKey = this.getRedisPlanLock(planNo);
        try {
            this.updateGroupMaterialPlanStatus(planNo, GroupMaterialPlanEnum.SUBMIT, GroupMaterialPlanEnum.SAVE);
        } finally {
            this.deleteRedisPlanLock(redisKey);
        }
    }

    /**
     * 更新计划单号状态
     * @param planNo 计划单号
     * @param verifyStatus 判断是否是当前状态
     * @param saveStatus 更新成此状态
     */
    private void updateGroupMaterialPlanStatus(@NonNull String planNo, @NonNull GroupMaterialPlanEnum verifyStatus, @NonNull GroupMaterialPlanEnum saveStatus) {

        // 获取单子信息
        final LambdaQueryWrapper<TbGroupMaterialPlan> queryWrapper = new LambdaQueryWrapper<TbGroupMaterialPlan>()
                .eq(TbGroupMaterialPlan::getPlanNo, planNo).last(" limit 1");
        final TbGroupMaterialPlan tbGroupMaterialPlan = groupMaterialPlanMapper.selectOne(queryWrapper);
        if (Objects.isNull(tbGroupMaterialPlan)) {
            throw new IllegalArgumentException("计划单号不存在");
        }

        // 校验单子状态
        this.checkGroupMaterialPlanStatus(tbGroupMaterialPlan.getStatus(), verifyStatus);

        // 更新物料计划状态
        final LoginUserHandler.User user = LoginUserHandler.get();
        final Date date = new Date();
        final LambdaUpdateWrapper<TbGroupMaterialPlan> updateWrapper = new LambdaUpdateWrapper<TbGroupMaterialPlan>()
                .eq(TbGroupMaterialPlan::getPlanNo, planNo)
                .set(TbGroupMaterialPlan::getStatus, saveStatus.getCode());

        this.fillUpdateInfo(updateWrapper, user, date);

        if (Objects.equals(saveStatus, GroupMaterialPlanEnum.SUBMIT)) {
            // 提交
            updateWrapper.set(TbGroupMaterialPlan::getSubmitId, user.getUserId())
                    .set(TbGroupMaterialPlan::getSubmitName, user.getNickname())
                    .set(TbGroupMaterialPlan::getSubmitDate, date);
        } else if (Objects.equals(saveStatus, GroupMaterialPlanEnum.SAVE)) {
            // 撤销提交
            updateWrapper.set(TbGroupMaterialPlan::getSubmitId, NumberUtils.LONG_ZERO)
                    .set(TbGroupMaterialPlan::getSubmitName, Strings.EMPTY)
                    .set(TbGroupMaterialPlan::getSubmitDate, DefaultDateEnum.DEFAULT_DATE.getDate());
        }

        groupMaterialPlanMapper.update(null, updateWrapper);

        log.info("专业组物料状态更新成功：计划单号：{}，更新人：{}， 原本状态：{}， 当前状态：{}", planNo, user.getNickname(), verifyStatus.getDesc(), saveStatus.getDesc());
    }

    @Override
    public List<GroupMaterialPlanVo> queryAllUnApprovalPlanList(QueryAllUnApprovalPlanListDto queryAllUnApprovalPlanListDto) {
        List<GroupMaterialPlanVo> result = new ArrayList<>();

        // 查询专业组计划待审批数据
        List<GroupMaterialPlanVo> groupMaterialPlanVos = queryGroupMaterialPlan(queryAllUnApprovalPlanListDto);

        // 查询物料申领待审批数据
        List<GroupMaterialPlanVo> groupMaterialApplyVos = queryGroupMaterialApply(queryAllUnApprovalPlanListDto);

        result.addAll(groupMaterialPlanVos);
        result.addAll(groupMaterialApplyVos);

        // 根据物料计划时间正序排序
        return result.stream().sorted(Comparator.comparing(GroupMaterialPlanVo::getSubmitDate)).collect(Collectors.toList());
    }

    /**
     * 审核专业组计划
     */
    @Override
    public ApprovalPlanVo approvalPlan(ApprovalPlanDto approvalPlanDto) {
        final String redisPlanLock = this.getRedisPlanLock(approvalPlanDto.getPlanNo());
        final Date date = new Date();
        try {

            LoginUserHandler.User user = LoginUserHandler.get();

            // 查询专业组物料计划信息
            List<TbGroupMaterialPlan> tbGroupMaterialPlans = groupMaterialPlanMapper.selectList(Wrappers.lambdaQuery(TbGroupMaterialPlan.class)
                    .eq(TbGroupMaterialPlan::getPlanNo, approvalPlanDto.getPlanNo())
                    .eq(TbGroupMaterialPlan::getIsDelete, YesOrNoEnum.NO.getCode()));
            if (CollectionUtils.isEmpty(tbGroupMaterialPlans)) {
                throw new IllegalArgumentException("专业组物料计划不存在!");
            }

            // 校验是否是已提交状态
            this.checkGroupMaterialPlanStatus(tbGroupMaterialPlans.get(NumberUtils.INTEGER_ZERO).getStatus(), GroupMaterialPlanEnum.SUBMIT);

            // 推送业务中台专业组计划申领单
            Response<String> materialPurchasePlan = limsApiService2Impl.createMaterialPurchasePlan(fillMaterialPurchasePlanCreateRequest(this.getGroupMaterialPlanVoList(tbGroupMaterialPlans), user, date));
            if (!materialPurchasePlan.isSuccess()) {
                throw new IllegalArgumentException(materialPurchasePlan.getMsg());
            }

            // 更新物料计划申领单状态
            final LambdaUpdateWrapper<TbGroupMaterialPlan> updateWrapper = Wrappers.lambdaUpdate(TbGroupMaterialPlan.class)
                    .eq(TbGroupMaterialPlan::getPlanNo, approvalPlanDto.getPlanNo())
                    .set(TbGroupMaterialPlan::getAuditId, user.getUserId())
                    .set(TbGroupMaterialPlan::getAuditName, user.getNickname())
                    .set(TbGroupMaterialPlan::getAuditDate, date)
                    .set(TbGroupMaterialPlan::getStatus, GroupMaterialPlanEnum.AUDIT.getCode());
            this.fillUpdateInfo(updateWrapper, user, date);
            groupMaterialPlanMapper.update(null, updateWrapper);
        } finally {
            this.deleteRedisPlanLock(redisPlanLock);
        }

        return new ApprovalPlanVo().setApplyId(approvalPlanDto.getPlanNo());
    }


    /**
     * 退回专业组计划
     */
    @Override
    public ReturnPlanVo returnPlan(ReturnPlanDto returnPlanDto) {

        final String redisPlanLock = this.getRedisPlanLock(returnPlanDto.getPlanNo());
        try {
            LoginUserHandler.User user = LoginUserHandler.get();

            // 查询专业组物料计划信息
            List<TbGroupMaterialPlan> tbGroupMaterialPlans = groupMaterialPlanMapper.selectList(Wrappers.lambdaQuery(TbGroupMaterialPlan.class)
                    .eq(TbGroupMaterialPlan::getPlanNo, returnPlanDto.getPlanNo())
                    .eq(TbGroupMaterialPlan::getIsDelete, YesOrNoEnum.NO.getCode()));
            if (CollectionUtils.isEmpty(tbGroupMaterialPlans)) {
                throw new IllegalArgumentException("专业组物料计划不存在!");
            }

            // 校验是否是已提交状态
            this.checkGroupMaterialPlanStatus(tbGroupMaterialPlans.get(NumberUtils.INTEGER_ZERO).getStatus(), GroupMaterialPlanEnum.SUBMIT);

            // 更新物料计划申领单状态
            final Date date = new Date();
            final LambdaUpdateWrapper<TbGroupMaterialPlan> updateWrapper = Wrappers.lambdaUpdate(TbGroupMaterialPlan.class)
                    .eq(TbGroupMaterialPlan::getPlanNo, returnPlanDto.getPlanNo())
                    .set(TbGroupMaterialPlan::getReturnUserId, user.getUserId())
                    .set(TbGroupMaterialPlan::getReturnUserName, user.getNickname())
                    .set(TbGroupMaterialPlan::getReturnDate, date)
                    .set(TbGroupMaterialPlan::getReturnReason, returnPlanDto.getReturnReason())
                    .set(TbGroupMaterialPlan::getStatus, GroupMaterialPlanEnum.REJECT.getCode());
            this.fillUpdateInfo(updateWrapper, user, date);

            groupMaterialPlanMapper.update(null, updateWrapper);
        } finally {
            this.deleteRedisPlanLock(redisPlanLock);
        }
        return new ReturnPlanVo().setPlanNo(returnPlanDto.getPlanNo());
    }

    /**
     * 已审批专业组计划列表
     */
    @Override
    public List<GroupMaterialPlanVo> queryApprovalPlanPage(QueryApprovalPlanPageDto dto) {
        final LambdaQueryWrapper<TbGroupMaterialPlan> wrapper = new LambdaQueryWrapper<TbGroupMaterialPlan>()
                .eq(Objects.nonNull(dto.getGroupId()), TbGroupMaterialPlan::getGroupId, dto.getGroupId())
                .like(StringUtils.isNotBlank(dto.getPlanNo()), TbGroupMaterialPlan::getPlanNo, dto.getPlanNo())
                .in(TbGroupMaterialPlan::getStatus, Arrays.asList(GroupMaterialPlanEnum.AUDIT.getCode(), GroupMaterialPlanEnum.REJECT.getCode()))
                .and(StringUtils.isNotBlank(dto.getMaterialCodeOrName()),
                        e -> e.like(TbGroupMaterialPlan::getMaterialCode, dto.getMaterialCodeOrName())
                                .or().like(TbGroupMaterialPlan::getMaterialName, dto.getMaterialCodeOrName()))
                .and(org.apache.commons.lang3.ObjectUtils.allNotNull(dto.getStartAuditDate(),dto.getEndAuditDate()),
                        e -> e.between(TbGroupMaterialPlan::getAuditDate, dto.getStartAuditDate(), dto.getEndAuditDate())
                                .or().between(TbGroupMaterialPlan::getReturnDate, dto.getStartAuditDate(), dto.getEndAuditDate()))
//                .between(org.apache.commons.lang3.ObjectUtils.allNotNull(dto.getStartAuditDate(),dto.getEndAuditDate()),
//                        TbGroupMaterialPlan::getAuditDate, dto.getStartAuditDate(), dto.getEndAuditDate() )
                .orderByAsc(TbGroupMaterialPlan::getPlannerDate);

        final List<TbGroupMaterialPlan> tbGroupMaterialPlans = groupMaterialPlanMapper.selectList(wrapper);

        if (CollectionUtils.isEmpty(tbGroupMaterialPlans)) {
            return Collections.emptyList();
        }

        return this.getGroupMaterialPlanVoList(tbGroupMaterialPlans);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteMaterialByPlanNo(String planNo, List<String> materialCodeList) {
        final String redisPlanLock = getRedisPlanLock(planNo);
        try {

            final List<GroupMaterialPlanDto> groupMaterialPlanDtos = this.selectByPlanNo(planNo);
            if (CollectionUtils.isEmpty(groupMaterialPlanDtos)) {
                throw new IllegalArgumentException("计划单号不存在");
            }

            final GroupMaterialPlanDto groupMaterialPlanDto = groupMaterialPlanDtos.get(NumberUtils.INTEGER_ZERO);
            this.checkGroupMaterialPlanStatus(groupMaterialPlanDto.getStatus(), GroupMaterialPlanEnum.SAVE);

            final LambdaUpdateWrapper<TbGroupMaterialPlan> deleteWrapper = new LambdaUpdateWrapper<TbGroupMaterialPlan>()
                    .eq(TbGroupMaterialPlan::getPlanNo, planNo)
                    .in(TbGroupMaterialPlan::getMaterialCode, materialCodeList);

            return super.remove(deleteWrapper);
        } finally {
            this.deleteRedisPlanLock(redisPlanLock);
        }
    }

    //==================================================================================================================

    // 生成计划号   JH + 当前实验室两位编码 + 当前日期（240710） + 四位自增
    private String createPlanNo(final long orgId) {

        final String dateStr = LocalDate.now().format(dateFormatter);

        // 自增编码redisKey 按照时间
        String incrementKey = redisPrefix.getBasePrefix() + "material:group:plan-no:" + dateStr;
        // 自增编码
        Long increment = stringRedisTemplate.opsForValue().increment(incrementKey);

        // 如果自增不存在
        if (Objects.isNull(increment)) {
            String str = MaterialNoType.JH_PREFIX.getCode() + String.format("%02d", orgId) + dateStr;
            // 查询出来， 根据
            String num = groupMaterialPlanMapper.selectList(null).stream()
                    .map(TbGroupMaterialPlan::getPlanNo)
                    // 过滤
                    .filter(e -> e.startsWith(str))
                    .map(e -> e.substring(str.length()))
                    .max(Comparator.comparing(Integer::valueOf))
                    .orElse(String.valueOf(NumberUtils.INTEGER_ZERO));

            // 获取最大的值 + 1
            increment = NumberUtils.toLong(num) + NumberUtils.INTEGER_ONE;

            stringRedisTemplate.opsForValue().set(incrementKey, String.valueOf(increment));
        }

        // 按天设置过期时间
        stringRedisTemplate.expire(incrementKey, 1, TimeUnit.DAYS);
        return String.format("%s%s%s%s", MaterialNoType.JH_PREFIX.getCode(), String.format("%02d", orgId), dateStr, String.format("%04d", increment));
    }


    private void checkMaterialExist(Set<String> materialCodes, LoginUserHandler.User user) {
        // 判断物料是否存在
        final Set<String> dbMaterialCodeSet = groupMaterialService.selectByGroupIdAndMaterialCodes(user.getGroupId(), materialCodes)
                .stream().map(GroupMaterialDetailDto::getMaterialCode).collect(Collectors.toSet());
        for (String materialCode : materialCodes) {
            if (!dbMaterialCodeSet.contains(materialCode)) {
                throw new IllegalArgumentException("物料【" + materialCode + "】在本专业组不存在");
            }
        }
    }

    private String getRedisPlanLock(String planNo) {
        String redisKeyFormat = "%sgroup:material:plan:lock:%s";
        final String redisKey = String.format(redisKeyFormat, redisPrefix.getBasePrefix(), planNo);
        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(redisKey, Strings.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException("当前单据状态正在更新，请稍后点击查询更新单据状态");
        }
        return redisKey;
    }

    private void deleteRedisPlanLock(String redisKey) {
        stringRedisTemplate.delete(redisKey);
    }

    private List<GroupMaterialPlanDto> convert(List<TbGroupMaterialPlan> list) {
        return JSON.parseArray(JSON.toJSONString(list), GroupMaterialPlanDto.class).stream()
                .peek(GroupMaterialPlanDto::clearDefaultDate).collect(Collectors.toList());
    }

    // 类转换
    private List<GroupMaterialPlanApprovalVo> convertGroupMaterialPlanApprovalVo(List<TbGroupMaterialPlan> tbGroupMaterialPlans) {

        List<GroupMaterialPlanApprovalVo> groupMaterialPlanApprovalVos = new ArrayList<>();

        tbGroupMaterialPlans.forEach(e -> {
            GroupMaterialPlanApprovalVo tempVo = new GroupMaterialPlanApprovalVo();
            tempVo.setApplyId(e.getGroupMaterialPlanId());
            tempVo.setApplyNo(e.getPlanNo());
            tempVo.setApplyTime(e.getPlannerDate());
            tempVo.setApplyUserId(e.getPlannerId());
            tempVo.setApplyUserName(e.getPlannerName());
            tempVo.setPlanId(null);
            tempVo.setPlanNo(null);
            tempVo.setStatus(e.getStatus());
            tempVo.setGroupId(e.getGroupId());
            tempVo.setGroupName(e.getGroupName());
            tempVo.setChecker(e.getAuditName());
            tempVo.setCheckDate(e.getAuditDate());
            groupMaterialPlanApprovalVos.add(tempVo);
        });

        return groupMaterialPlanApprovalVos;
    }


    // 封装转换专业组物料计划
    private MaterialPurchasePlanCreateRequest fillMaterialPurchasePlanCreateRequest(List<GroupMaterialPlanVo> groupMaterialPlanVoList, LoginUserHandler.User user,Date date) {

        if (CollectionUtils.isEmpty(groupMaterialPlanVoList)) {
            return null;
        }

        GroupMaterialPlanVo groupMaterialPlanVo = groupMaterialPlanVoList.get(NumberUtils.INTEGER_ZERO);

        MaterialPurchasePlanCreateRequest request = new MaterialPurchasePlanCreateRequest();
        request.setLimsPlanNo(groupMaterialPlanVo.getPlanNo());
        request.setOrgId(environment.getProperty(BUSINESS_CENTER_ORG_CODE));
        request.setPurchaseRequestUserId(String.valueOf(groupMaterialPlanVo.getPlannerId()));
        request.setPurchaseRequestUserName(groupMaterialPlanVo.getPlannerName());
        request.setPurchaseRequestDeptId(groupMaterialPlanVo.getGroupId());
        request.setPurchaseRequestDeptName(groupMaterialPlanVo.getGroupName());
        request.setPurchaseRequestTime(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()));
        List<MaterialPurchasePlanCreateRequest.MaterialPurchasePlanCreateDetailRequest> detailRequest = new ArrayList<>();
        groupMaterialPlanVo.getGroupMaterialPlanDetailList().forEach(e -> {
            MaterialPurchasePlanCreateRequest.MaterialPurchasePlanCreateDetailRequest tempDetail = new MaterialPurchasePlanCreateRequest.MaterialPurchasePlanCreateDetailRequest();
            tempDetail.setMaterialCode(e.getMaterialCode());
            tempDetail.setPlanPrimaryNumberDecimal(e.getMainApplyNumber());
            tempDetail.setPlanAssistNumber(new BigDecimal(e.getAssistApplyNumber()));
            detailRequest.add(tempDetail);
        });
        request.setDetailRequests(detailRequest);

        return request;
    }

    /**
     * 查询专业组计划申领单
     */
    private List<GroupMaterialPlanVo> queryGroupMaterialPlan(QueryAllUnApprovalPlanListDto queryAllUnApprovalPlanListDto) {

        LambdaQueryWrapper<TbGroupMaterialPlan> tbGroupMaterialPlanLambdaQueryWrapper = Wrappers.lambdaQuery(TbGroupMaterialPlan.class)
                .eq(TbGroupMaterialPlan::getStatus, GroupMaterialPlanEnum.SUBMIT.getCode())
                .eq(TbGroupMaterialPlan::getIsDelete, YesOrNoEnum.NO.getCode())
                .orderByAsc(TbGroupMaterialPlan::getPlannerDate);
        if (StringUtils.isNotBlank(queryAllUnApprovalPlanListDto.getGroupId())) {
            tbGroupMaterialPlanLambdaQueryWrapper.eq(TbGroupMaterialPlan::getGroupId, Long.valueOf(queryAllUnApprovalPlanListDto.getGroupId()));
        }

        List<TbGroupMaterialPlan> tbGroupMaterialPlans = groupMaterialPlanMapper.selectList(tbGroupMaterialPlanLambdaQueryWrapper);

        return this.getGroupMaterialPlanVoList(tbGroupMaterialPlans);
    }

    /**
     * 查询物料申领单
     */
    private List<GroupMaterialPlanVo> queryGroupMaterialApply(QueryAllUnApprovalPlanListDto queryAllUnApprovalPlanListDto) {

        Long groupId = null;
        if (StringUtils.isNotBlank(queryAllUnApprovalPlanListDto.getGroupId())) {
            groupId = Long.valueOf(queryAllUnApprovalPlanListDto.getGroupId());
        }

        List<GroupMaterialApplyDto> groupMaterialApplys = groupMaterialApplyService.selectUnApprovalMaterialApply(LoginUserHandler.get().getOrgId(), groupId);
        if (CollectionUtils.isEmpty(groupMaterialApplys)) {
            return Collections.emptyList();
        }


        // 数据转换
        List<GroupMaterialPlanVo> groupMaterialPlanVos = new ArrayList<>();
        groupMaterialApplys.forEach(e -> {
            GroupMaterialPlanVo temp = new GroupMaterialPlanVo();
            temp.setPlanNo(e.getApplyNo());
            temp.setGroupId(e.getGroupId());
            temp.setGroupCode(StringUtils.EMPTY);
            temp.setGroupName(e.getGroupName());
            temp.setOrgId(e.getOrgId());
            temp.setOrgName(e.getOrgName());
            temp.setStatus(GroupMaterialPlanEnum.SUBMIT.getCode());
            temp.setAuditId(e.getCheckerId());
            temp.setAuditName(e.getChecker());
            temp.setAuditDate(e.getCheckDate());
            temp.setPlannerId(e.getApplyUserId());
            temp.setPlannerName(e.getApplyUserName());
            temp.setPlannerDate(e.getApplyTime());
            temp.setReturnReason(StringUtils.EMPTY);
            temp.setReturnUserName(StringUtils.EMPTY);
            temp.setRemark(StringUtils.EMPTY);
            temp.setPlanType(MaterialApplyTypeEnum.MATERIAL_APPLY.getCode());
            temp.setSubmitId(e.getApplyUserId());
            temp.setSubmitName(e.getApplyUserName());
            temp.setSubmitDate(e.getApplyTime());

            groupMaterialPlanVos.add(temp);
        });

        return groupMaterialPlanVos;
    }

    /**
     * 填充更新时间
     */
    private void fillUpdateInfo(LambdaUpdateWrapper<TbGroupMaterialPlan> updateWrapper, LoginUserHandler.User user, Date date) {
        updateWrapper.set(TbGroupMaterialPlan::getUpdaterId, user.getUserId())
                .set(TbGroupMaterialPlan::getUpdaterName, user.getNickname())
                .set(TbGroupMaterialPlan::getUpdateDate, date);
    }


}