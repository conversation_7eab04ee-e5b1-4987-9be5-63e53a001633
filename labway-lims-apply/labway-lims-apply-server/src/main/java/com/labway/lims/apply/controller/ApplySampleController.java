package com.labway.lims.apply.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.ExceptionCodeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.DiffApplyDto;
import com.labway.lims.apply.api.dto.AddSamplesInfoDto;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplyUpdateBeforeCheckTipDto;
import com.labway.lims.apply.api.dto.ApplyUpdateBeforeCheckTipItemDto;
import com.labway.lims.apply.api.dto.BatchTestItemDto;
import com.labway.lims.apply.api.dto.BatchUpdateApplyDto;
import com.labway.lims.apply.api.dto.DisableOrEnableItemDto;
import com.labway.lims.apply.api.dto.PhysicalSampleDto;
import com.labway.lims.apply.api.dto.PhysicalSampleItemDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.SelectBarcodeDto;
import com.labway.lims.apply.api.dto.StopTestOrDisableSampleDto;
import com.labway.lims.apply.api.dto.TerminateItemDto;
import com.labway.lims.apply.api.dto.UpdateTestApplySampleDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ApplySampleItemBloodCultureService;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.PhysicalRegisterService;
import com.labway.lims.apply.api.service.PhysicalSampleItemService;
import com.labway.lims.apply.api.service.PhysicalSampleService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.apply.service.chain.apply.update.UpdateFillApplyAndDiffInfoCommand;
import com.labway.lims.apply.vo.ApplySampleItemBloodCultureVo;
import com.labway.lims.apply.vo.ApplyVo;
import com.labway.lims.apply.vo.BatchUpdateHspOrgVo;
import com.labway.lims.apply.vo.DisableOrEnableItemVo;
import com.labway.lims.apply.vo.ReportItemVo;
import com.labway.lims.apply.vo.SampleItemVo;
import com.labway.lims.apply.vo.SelectHspOrgSampleQueryVo;
import com.labway.lims.apply.vo.SimpleSampleVo;
import com.labway.lims.apply.vo.StopTestOrDisableSampleVo;
import com.labway.lims.apply.vo.TerminateItemInfoResponseVo;
import com.labway.lims.apply.vo.TestApplyVo;
import com.labway.lims.apply.vo.UpdateTestApplyItemVo;
import com.labway.lims.apply.vo.UpdateTestApplySampleVo;
import com.labway.lims.apply.vo.UpdateTestApplyVo;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.PhysicalGroupDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.PhysicalGroupService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.service.SampleService;
import com.swak.frame.util.StringPool;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.Diff;
import org.apache.commons.lang3.builder.DiffResult;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 申请单样本
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/apply-sample")
public class ApplySampleController extends BaseController {

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Resource
    private ApplyService applyService;

    @Resource
    private ApplySampleService applySampleService;

    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    private ReportItemService reportItemService;

    @Resource
    private ApplySampleItemBloodCultureService applySampleItemBloodCultureService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @DubboReference
    private PhysicalGroupService physicalGroupService;
    @Resource
    private PhysicalSampleService physicalSampleService;
    @Resource
    private PhysicalRegisterService physicalRegisterService;
    @Resource
    private PhysicalSampleItemService physicalSampleItemService;

    @DubboReference
    private TestItemService testItemService;

    @Resource
    private SnowflakeService snowflakeService;

    @Resource
    private ApplyController applyController;


    @Resource
    @Lazy
    private ApplySampleController applySampleController;

    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;
    @Resource
    private SampleReportService sampleReportService;

    /**
     * 统计申请单下审核的样本数量
     */
    @PostMapping("/before-update-apply-sample-tips")
    public Object selectApplySampleAuditQuantity(@RequestBody UpdateTestApplySampleVo vo) {
        final Long applyId = vo.getApplyId();
        if (Objects.isNull(applyId)) {
            throw new IllegalArgumentException("申请单id不能为空");
        }

        final ApplyDto apply = applyService.selectByApplyId(applyId);
        if (Objects.isNull(apply)) {
            throw new IllegalArgumentException("申请单不存在");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalArgumentException("送检机构不存在");
        }

        // 是否有修改信息
        final DiffApplyDto diffApply = UpdateFillApplyAndDiffInfoCommand.fillApplyDiffInfo(
                JSON.parseObject(JSON.toJSONString(vo), UpdateTestApplySampleDto.class), hspOrganization);
        final DiffResult<ApplyDto> diff = diffApply.diff(apply);
        final List<Diff<?>> diffs = diff.getDiffs();
        final Set<String> updateFiled = diffs.stream().map(Diff::getFieldName).collect(Collectors.toSet());

        // 如果为空返回零
        if (CollectionUtils.isEmpty(updateFiled)) {
            return Map.of("count", NumberUtils.INTEGER_ZERO);
        }

        final List<Integer> auditStatus =
                List.of(SampleStatusEnum.AUDIT.getCode(), SampleStatusEnum.ONE_AUDIT.getCode());

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplyId(applyId);

        // 如果包含审核了的样本 （一审和二审） 并且修改了性别或者年龄
        if (applySamples.stream().anyMatch(a -> auditStatus.contains(a.getStatus()))
                && CollectionUtils.containsAny(updateFiled, Set.of("性别", "年龄"))) {
            throw new IllegalStateException("当前申请单下已存在条码审核，不能修改性别和年龄");
        }

        final long auditCount = applySamples.stream().map(ApplySampleDto::getStatus)
                .filter(f -> Objects.equals(f, SampleStatusEnum.AUDIT.getCode())).count();
        return Map.of("count", auditCount);
    }

    /**
     * 查询送检机构样本
     */
    @PostMapping("/select-hsp-org-sample")
    public Object selectHspOrgSample(@RequestBody SelectHspOrgSampleQueryVo queryVo) {

        queryVo.defaultDate();

        final SampleEsQuery build = SampleEsQuery.builder().pageNo(1).pageSize(200)
                .startCreateDate(queryVo.getStartDate()).endCreateDate(queryVo.getEndDate()).build();

        final String barcode = queryVo.getBarcode();
        if (StringUtils.isNotBlank(barcode)) {
            build.setBarcodes(Set.of(barcode));
        }

        final String patientName = queryVo.getPatientName();
        if (StringUtils.isNotBlank(patientName)) {
            build.setPatientName(patientName);
        }

        final String patientVisitCard = queryVo.getPatientVisitCard();
        if (StringUtils.isNotBlank(patientVisitCard)) {
            build.setPatientVisitCard(patientVisitCard);
        }

        final Long hspOrgId = queryVo.getHspOrgId();
        if (Objects.nonNull(hspOrgId)) {
            build.setHspOrgIds(Set.of(hspOrgId));
        }

        // 总数
        long count = elasticSearchSampleService.count(build);
        if (Objects.equals(count, NumberUtils.LONG_ZERO)) {
            return Map.of("count", NumberUtils.LONG_ZERO, "data", List.of());
        }

        // 分页参数
        List<Object> searchAfter = null;
        final String searchAfterStr = queryVo.getSearchAfterStr();
        if (StringUtils.isNotBlank(searchAfterStr)) {
            searchAfter = deserializeObject(Base64.getDecoder().decode(searchAfterStr), Object.class);
        }

        // 查询
        final ScrollPage<BaseSampleEsModelDto> page = elasticSearchSampleService.searchAfter(searchAfter, build);
        final List<BaseSampleEsModelDto> data = page.getData();
        searchAfter = page.getSearchAfter();
        if (CollectionUtils.isEmpty(data)) {
            return Map.of("count", count, "data", List.of(), "searchAfterStr",
                    new String(Base64.getEncoder().encode(serializeList(searchAfter)), StandardCharsets.UTF_8));
        }

        final List<SimpleSampleVo> datas = data.stream()
                .map(m -> JSON.parseObject(JSON.toJSONString(m), SimpleSampleVo.class)).collect(Collectors.toList());

        return Map.of("count", count, "data", datas, "searchAfterStr",
                new String(Base64.getEncoder().encode(serializeList(searchAfter)), StandardCharsets.UTF_8));

    }

    /**
     * 取消禁用
     */
    @PostMapping("/cancel-disable")
    public Object cancelDisable(@RequestBody List<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("申请单样本id不能为空");
        }

        applySampleService.cancelDisable(applySampleIds);

        return Collections.emptyMap();
    }

    /**
     * 终止检验
     */
    @PostMapping(value = "/terminate")
    public Object stopTest(@RequestBody StopTestOrDisableSampleVo vo) {
        final List<Long> applySampleIds = vo.getApplySampleIds();
        final String causeCode = vo.getCauseCode();
        final String cause = vo.getCause();

        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("申请单样本id不能为空");
        }

        if (StringUtils.isBlank(cause) || StringUtils.isBlank(causeCode)) {
            throw new IllegalArgumentException("原因不能为空");
        }
        applySampleService.stopTest(JSON.parseObject(JSON.toJSONString(vo), StopTestOrDisableSampleDto.class));

        return Collections.emptyMap();
    }

    /**
     * 禁用检验
     */
    @PostMapping(value = "/disable")
    public Object disable(@RequestBody StopTestOrDisableSampleVo vo) {
        final List<Long> applySampleIds = vo.getApplySampleIds();
        final String causeCode = vo.getCauseCode();
        final String cause = vo.getCause();

        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("申请单样本id不能为空");
        }

        if (StringUtils.isBlank(cause) || StringUtils.isBlank(causeCode)) {
            throw new IllegalArgumentException("原因不能为空");
        }

        applySampleService.disable(JSON.parseObject(JSON.toJSONString(vo), StopTestOrDisableSampleDto.class));

        return Collections.emptyMap();
    }

    /**
     * 检验项目禁用
     */
    @PostMapping(value = "/item/disable")
    public Object itemDisable(@RequestBody DisableOrEnableItemVo vo) {
        if (CollectionUtils.isEmpty(vo.getApplySampleItems())) {
            throw new IllegalArgumentException("项目不能为空");
        }

        applySampleService.itemDisable(JSON.parseObject(JSON.toJSONString(vo), DisableOrEnableItemDto.class));

        return Collections.emptyMap();
    }

    /**
     * 检验项目取消禁用
     */
    @PostMapping(value = "/item/enable")
    public Object itemEnable(@RequestBody DisableOrEnableItemVo vo) {
        if (CollectionUtils.isEmpty(vo.getApplySampleItems())) {
            throw new IllegalArgumentException("项目不能为空");
        }

        applySampleService.itemEnable(JSON.parseObject(JSON.toJSONString(vo), DisableOrEnableItemDto.class));

        return Collections.emptyMap();
    }

    /**
     * （检验加减项目）
     * 更新样本信息
     */
    @PostMapping("/update")
    public Object update(@RequestBody UpdateTestApplySampleVo vo) {

        final Long applyId = vo.getApplyId();
        if (Objects.isNull(applyId)) {
            throw new IllegalArgumentException("申请单id不能为空");
        }

        // 检查申请单参数
        ApplyController.checkApplyParam(vo);

        final String lockKey = redisPrefix.getBasePrefix() + "apply:lock:" + applyId;
        if (BooleanUtils.isNotTrue(
                stringRedisTemplate.opsForValue().setIfAbsent(lockKey, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException("申请单正在被修改,请稍后再试");
        }

        try {
            final UpdateTestApplySampleDto updateTestApply =
                    JSON.parseObject(JSON.toJSONString(vo), UpdateTestApplySampleDto.class);
            return Map.of("applyInfo", applyService.update(updateTestApply));
        } finally {
            stringRedisTemplate.delete(lockKey);
        }
    }

    /**
     * （检验加减项目）
     * 更新样本信息的前处理确认
     */
    @PostMapping("/update/confirm")
    public Object updateConfirm(@RequestBody UpdateTestApplySampleVo vo) {

        final Long applyId = vo.getApplyId();
        if (Objects.isNull(applyId)) {
            throw new IllegalArgumentException("申请单id不能为空");
        }

        // 检查申请单参数
        ApplyController.checkApplyParam(vo);

        final String lockKey = redisPrefix.getBasePrefix() + "apply:lock:" + applyId;
        if (BooleanUtils.isNotTrue(
                stringRedisTemplate.opsForValue().setIfAbsent(lockKey, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException("申请单正在被修改,请稍后再试");
        }

        try {
            final UpdateTestApplySampleDto updateTestApply =
                    JSON.parseObject(JSON.toJSONString(vo), UpdateTestApplySampleDto.class);
            AddSamplesInfoDto addSamplesInfoDto = applyService.updateConfirm(updateTestApply);
            return Map.of("addSamplesInfo", Objects.isNull(addSamplesInfoDto) ? new AddSamplesInfoDto() : addSamplesInfoDto);
        } finally {
            stringRedisTemplate.delete(lockKey);
        }
    }

    /**
     * 更新样本信息前校验是否提示刷新报告
     */
    @PostMapping("/before-check-update-apply-sample-tips")
    public Object beforeCheckUpdateApplySampleTips(@RequestBody UpdateTestApplySampleVo vo) {
        final Long applyId = vo.getApplyId();
        final Long applySampleId = vo.getApplySampleId();
        if (Objects.isNull(applyId)) {
            throw new IllegalArgumentException("申请单id不能为空");
        }
        if (Objects.isNull(applySampleId)) {
            throw new IllegalArgumentException("申请单样本id不能为空");
        }

        final ApplyDto apply = applyService.selectByApplyId(applyId);
        if (Objects.isNull(apply)) {
            throw new IllegalArgumentException("申请单不存在");
        }
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalArgumentException("申请单样本不存在");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalArgumentException("送检机构不存在");
        }

        final UpdateTestApplySampleDto updateTestApply =
                JSON.parseObject(JSON.toJSONString(vo), UpdateTestApplySampleDto.class);
        updateTestApply.setStatus(apply.getStatus());
        // 是否有修改信息
        final DiffApplyDto diffApply = UpdateFillApplyAndDiffInfoCommand.fillApplyDiffInfo(updateTestApply, hspOrganization);
        diffApply.setApplyId(apply.getApplyId());
        final DiffResult<ApplyDto> diff = diffApply.diff(apply);
        final List<Diff<?>> diffs = diff.getDiffs();
        final Set<String> updateFiled = diffs.stream().map(Diff::getFieldName).collect(Collectors.toSet());

        ApplyUpdateBeforeCheckTipDto checkTipDto = new ApplyUpdateBeforeCheckTipDto();
        List<ApplyUpdateBeforeCheckTipItemDto> criticalTips = new ArrayList<>();
        List<ApplyUpdateBeforeCheckTipItemDto> exceptionTips = new ArrayList<>();
        checkTipDto.setCriticals(criticalTips);
        checkTipDto.setExceptions(exceptionTips);

        // 如果为空返回零
        if (CollectionUtils.isEmpty(updateFiled)) {
            return Map.of("count", NumberUtils.INTEGER_ZERO, "oneAuditCount", NumberUtils.INTEGER_ZERO, "checkTip", checkTipDto);
        }

        // 已经一审，二审的状态下，修改年龄，性别，校验是否触发异常和危急值
        final List<Integer> auditStatus =
                List.of(SampleStatusEnum.AUDIT.getCode(), SampleStatusEnum.ONE_AUDIT.getCode());
        List<ApplySampleDto> applySamples = applySampleService.selectByApplyId(applyId);
        Set<Integer> status = applySamples.stream().map(ApplySampleDto::getStatus).collect(Collectors.toSet());
        if (status.stream().anyMatch(auditStatus::contains)) {
            // 修改性别，年龄，送检机构，样本类型 会触发参考范围的变更，需要check一下先
            if (CollectionUtils.containsAny(updateFiled, Set.of("性别", "年龄", "送检机构"))
                    || !Objects.equals(vo.getSampleTypeCode(), applySample.getSampleTypeCode())) {
                List<ApplySampleDto> auditApplySampleDtos = applySamples.stream().filter(e ->
                        auditStatus.contains(e.getStatus())).collect(Collectors.toList());

                ApplyUpdateBeforeCheckTipDto routineCheckTipDto = sampleService.updateCheckExceptionAndCritical(
                        auditApplySampleDtos.stream().filter(e -> ItemTypeEnum.ROUTINE ==
                                ItemTypeEnum.valueOf(e.getItemType())).collect(Collectors.toList()), diffApply);
                criticalTips.addAll(routineCheckTipDto.getCriticals());
                exceptionTips.addAll(routineCheckTipDto.getExceptions());

                ApplyUpdateBeforeCheckTipDto outsourcingCheckTipDto = outsourcingSampleService.updateCheckExceptionAndCritical(
                        auditApplySampleDtos.stream().filter(e -> ItemTypeEnum.OUTSOURCING ==
                                ItemTypeEnum.valueOf(e.getItemType())).collect(Collectors.toList()), diffApply);
                criticalTips.addAll(outsourcingCheckTipDto.getCriticals());
                exceptionTips.addAll(outsourcingCheckTipDto.getExceptions());
            }
            /*if (CollectionUtils.containsAny(updateFiled, Set.of("性别", "年龄", "送检机构"))
                    || !Objects.equals(vo.getSampleTypeCode(), applySample.getSampleTypeCode())) {
                throw new IllegalStateException("当前申请单下已存在条码审核，不能修改性别/年龄/送检机构/样本类型");
            }*/
        }

        final int auditCount = status.contains(SampleStatusEnum.AUDIT.getCode()) ? 1 : 0;
        final int oneAuditCount = status.contains(SampleStatusEnum.ONE_AUDIT.getCode()) ? 1 : 0;

        if (auditCount > 0 || oneAuditCount > 0) {
            final List<SampleReportDto> sampleReports = sampleReportService.selectByApplySampleIds(Collections.singleton(applySampleId));
            // 如果是手动上传的PDF报告，则不再提醒重新生成PDF报告
            if (CollectionUtils.isNotEmpty(sampleReports) && sampleReports.stream().anyMatch(e -> Objects.equals(e.getIsUploadPdf(), YesOrNoEnum.YES.getCode()))) {
                return Map.of("count", NumberUtils.INTEGER_ZERO, "oneAuditCount", NumberUtils.INTEGER_ZERO, "checkTip", checkTipDto);
            }
        }

        return Map.of("count", auditCount, "oneAuditCount", oneAuditCount, "checkTip", checkTipDto);
    }

    /**
     * （样本信息查询）
     * 更新样本信息
     */
    @PostMapping("/update-apply-sample")
    public Object updateApplySample(@RequestBody UpdateTestApplySampleVo vo) {
        final Long applyId = vo.getApplyId();
        if (Objects.isNull(applyId)) {
            throw new IllegalArgumentException("申请单id不能为空");
        }

        // 检查申请单参数
        ApplyController.checkApplyParam(vo);
        // 校验申请单样本参数
        checkApplySampleParam(vo);

        final String lockKey = redisPrefix.getBasePrefix() + "apply:lock:" + applyId;
        if (BooleanUtils.isNotTrue(
                stringRedisTemplate.opsForValue().setIfAbsent(lockKey, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException("申请单正在被修改,请稍后再试");
        }

        try {
            final UpdateTestApplySampleDto updateTestApply =
                    JSON.parseObject(JSON.toJSONString(vo), UpdateTestApplySampleDto.class);
            ApplyInfo applyInfo = applyService.updateApplySample(updateTestApply);

            // 同步信息到业务中台
            applySampleService.syncApplySampleToBusinessCenter(applyInfo.getApplyId());

            return Map.of("applyInfo", applyInfo);
        } finally {
            stringRedisTemplate.delete(lockKey);
        }
    }

    /**
     * 获取样本信息 (申请单+样本项目)
     */
    @GetMapping("/get")
    public Object get(@RequestParam(required = false) String barcode,
                      @RequestParam(required = false) Boolean isUnCheck) {
        if (Objects.isNull(isUnCheck)) {
            isUnCheck = true;
        }
        if (StringUtils.isBlank(barcode)) {
            throw new IllegalStateException("请输入样本条码");
        }

        List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("条码不存在");
        }

        // 如果项目都终止了，弹窗提示，恢复终止
        if (applySamples.stream().allMatch(a -> Objects.equals(a.getStatus(), SampleStatusEnum.STOP_TEST.getCode()))) {
            List<ApplySampleItemDto> sampleItemDtoList = applySampleItemService.selectByApplySampleIdContainStopTest(
                    applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));
            List<Dict> dataList = Lists.newArrayList();
            for (ApplySampleItemDto item : sampleItemDtoList) {
                Dict dict = Dict.of();
                dict.put("applySampleItemId", item.getApplySampleItemId());
                dict.put("barcode", barcode);
                dict.put("testItemCode", item.getTestItemCode());
                dict.put("testItemName", item.getTestItemName());
                dict.put("groupName", item.getGroupName());
                dict.put("stopStatus", item.getStopStatus());
                dataList.add(dict);
            }
            throw new LimsCodeException(ExceptionCodeEnum.REGAIN_STOP_TEST.getCode(),
                    ExceptionCodeEnum.REGAIN_STOP_TEST.getDesc()).setData(Map.of("list", dataList));
        }

        applySamples =
                applySamples.stream().filter(obj -> !Objects.equals(obj.getStatus(), SampleStatusEnum.STOP_TEST.getCode()))
                        .collect(Collectors.toList());
        final Map<Long, ApplySampleDto> applySampleMap =
                applySamples.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, v -> v, (k1, k2) -> k1));

        final Long applyId =
                applySamples.stream().map(ApplySampleDto::getApplyId).findFirst().orElse(NumberUtils.LONG_ZERO);
        final ApplyDto apply = applyService.selectByApplyId(applyId);
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        if (isUnCheck && BooleanUtils.isTrue(ApplyStatusEnum.isUnCheck(apply.getStatus()))) {
            throw new IllegalStateException("当前条码未完成复核，请至对应页面进行信息修改");
        }

        final Set<Long> applySampleIds = applySampleMap.keySet();
        // 根据申请单样本查询所有的项目，包含终止的，终止的显示到前端特殊颜色标记
        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleIdsAndStopStatus(applySampleIds, null);
        //        if (CollectionUtils.isEmpty(applySampleItems)) {
        //            throw new IllegalStateException("样本检验项目为空");
        //        }

        final Collection<Long> testItemIds =
                applySampleItems.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());
        final Map<Long, TestItemDto> testItemDtoMap = testItemService.selectByTestItemIdsAsMap(testItemIds);

        final Map<Long, List<ReportItemDto>> reportItemMap = reportItemService.selectByTestItemIds(testItemIds).stream()
                .collect(Collectors.groupingBy(ReportItemDto::getTestItemId));

        String packageKey = String.format(ApplyController.APPLAY_PACKAGE_CACHE_KEY, redisPrefix.getBasePrefix(), applyId);
        String result = stringRedisTemplate.opsForValue().get(packageKey);
        Map<Long, TestApplyVo.Item> itemPacakgeNameMap = new HashMap<>();
        if (StringUtils.isNotBlank(result)) {
            List<TestApplyVo.Item> applyItems = JSON.parseArray(result, TestApplyVo.Item.class);
            applyItems.forEach(item -> {
                itemPacakgeNameMap.put(item.getTestItemId(), item);
            });
        }
        final List<SampleItemVo> vos = applySampleItems.stream().map(m -> {
            final SampleItemVo vo = JSON.parseObject(JSON.toJSONString(m), SampleItemVo.class);
            vo.setPrice(m.getFeePrice());
            Optional.ofNullable(applySampleMap.get(vo.getApplySampleId()))
                    .ifPresent(c -> vo.setBarcode(c.getBarcode()));

            Optional.ofNullable(reportItemMap.get(vo.getTestItemId())).ifPresent(f -> {
                vo.setReportItems(JSON.parseArray(JSON.toJSONString(f), ReportItemVo.class));
            });
            final TestApplyVo.Item item = itemPacakgeNameMap.get(vo.getTestItemId());
            if (Objects.nonNull(item)) {
                vo.setPackageId(item.getPackageId());
                vo.setPackageName(item.getPackageName());
            }
            TestItemDto testItemDto = testItemDtoMap.getOrDefault(vo.getTestItemId(), new TestItemDto());
            vo.setExamMethodCode(testItemDto.getExamMethodCode());
            vo.setExamMethodName(testItemDto.getExamMethodName());

            return vo;
        }).sorted(Comparator.comparing(SampleItemVo::getPackageId)).collect(Collectors.toList());

        // 终止状态排序
        vos.sort(Comparator.comparing(SampleItemVo::getStopStatus, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(SampleItemVo::getIsDisabled, Comparator.nullsLast(Comparator.naturalOrder())));

        apply.setOutBarcode(applySamples.stream().map(ApplySampleDto::getOutBarcode).findFirst().orElse(""));

        return Map.of(

                "applySampleId", applySampleIds,

                "applySamples", applySamples,
                //
                "apply", JSON.parseObject(JSON.toJSONString(apply), ApplyVo.class),
                //
                "applySampleItems", vos,
                //
                "isDisable", applySamples.stream().map(ApplySampleDto::getIsDisabled).findFirst().orElse(0));
    }

    /**
     * 获取主条码信息 根据 主条码
     */
    @PostMapping("/get-master-barcode-info-by-barcodes")
    public Object getMasterBarcodeInfoByBarcodes(@RequestBody Set<String> masterBarcodes) {
        if (CollectionUtils.isEmpty(masterBarcodes)) {
            throw new IllegalArgumentException("请选择需要打印的主条码");
        }
        final List<ApplyDto> applys = applyService.selectByMasterBarcodes(masterBarcodes);
        if (CollectionUtils.isEmpty(applys)) {
            throw new IllegalStateException("未查询到此条码");
        }

        return convertApply(applys);
    }

    /**
     * 获取主条码信息 根据 applyId
     */
    @PostMapping("/get-master-barcode-info")
    public Object getMasterBarcodeInfo(@RequestBody Set<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            throw new IllegalArgumentException("请选择需要打印的主条码");
        }
        final List<ApplyDto> applys = applyService.selectByApplyIds(applyIds);
        if (CollectionUtils.isEmpty(applys)) {
            throw new IllegalStateException("未查询到此条码");
        }

        return convertApply(applys);
    }

    @Nonnull
    private static List<Map<String, ? extends Serializable>> convertApply(List<ApplyDto> applys) {
        return applys.stream().map(m -> Map.of(
                //
                "patientName", m.getPatientName(),
                //
                "patientSex", m.getPatientSex(),
                //
                "patientAge", m.getPatientAge(),
                //
                "patientSubage", m.getPatientSubage(),
                //
                "patientSubageUnit", m.getPatientSubageUnit(),
                //
                "masterBarcode", m.getMasterBarcode(),
                //
                "hspOrgName", m.getHspOrgName())).collect(Collectors.toList());
    }

    /**
     * 获取条码信息 根据 barcode
     */
    @PostMapping("/get-sample-barcode-info-by-barcode")
    public Object getSampleBarcodeInfoByBarcode(@RequestBody Set<String> barcodes) {
        if (CollectionUtils.isEmpty(barcodes)) {
            throw new IllegalArgumentException("请选择需要打印的条码");
        }

        // 查询所有样本
        final List<ApplySampleDto> applySamples = applySampleService.selectByBarcodes(barcodes);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("未查询到此条码");
        }

        return convertToBarcodeInfo(applySamples);
    }

    /**
     * 获取条码信息 根据 applySampleId
     */
    @PostMapping("/get-sample-barcode-info")
    public Object getSampleBarcodeInfo(@RequestBody List<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("请选择需要打印的条码");
        }

        // 查询所有样本
        final List<ApplySampleDto> applySamples =
                applySampleService.selectByApplySampleIds(new LinkedHashSet<>(applySampleIds));
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("未查询到此条码");
        }

        return convertToBarcodeInfo(applySamples);
    }

    /**
     * 终止检验-检验项目信息
     */
    @PostMapping(value = "/terminate-item-info")
    public Object terminateItemInfo(@RequestBody List<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        // key: 申请单样本id value：申请单样本
        Map<Long, ApplySampleDto> applySampleByApplySampleId = applySampleService.selectByApplySampleIds(applySampleIds)
                .stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));
        if (applySampleIds.stream().anyMatch(x -> Objects.isNull(applySampleByApplySampleId.get(x)))) {
            throw new IllegalArgumentException("存在无效申请单");
        }
        List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleIds(applySampleIds, true);
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("样本检验项目为空");
        }
        List<TerminateItemInfoResponseVo> targetList = Lists.newArrayListWithCapacity(applySampleItems.size());
        applySampleItems.forEach(item -> {
            TerminateItemInfoResponseVo vo = new TerminateItemInfoResponseVo();
            BeanUtils.copyProperties(item, vo);
            vo.setBarcode(applySampleByApplySampleId.get(item.getApplySampleId()).getBarcode());
            targetList.add(vo);
        });
        return targetList;
    }

    /**
     * 终止检验-检验项目维度
     */
    @PostMapping(value = "/terminate-item")
    public Object terminateItem(@RequestBody TerminateItemDto vo) {

        applySampleService.terminateItem(vo);

        return Collections.emptyMap();
    }

    /**
     * 终止检验-恢复样本
     */
    @PostMapping(value = "/regain-terminate-barcode")
    public Object regainTerminateBarcode(@RequestBody Set<Long> applySampleItemIds) {
        if (CollectionUtils.isEmpty(applySampleItemIds)) {
            throw new IllegalArgumentException("请选择需要恢复的项目");
        }
        applySampleService.regainTerminateBarcode(applySampleItemIds);

        return Collections.emptyMap();
    }


    /**
     * 根据样本id去查询血培养信息
     *
     * @return
     */
    @GetMapping("/getBloodCulture")
    public Object getBloodCulture(@RequestParam Long applySampleId) {
        if (Objects.isNull(applySampleId)) {
            return Collections.emptyList();
        }
        ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("申请样本信息不存在");
        }
        if (ObjectUtil.equal(YesOrNoEnum.YES.getCode(), applySampleDto.getIsTwoPick())) {
            throw new IllegalStateException("该样本已经完成二次分拣,请取消二次分拣后重试");
        }

        ApplySampleItemBloodCultureDto applySampleItemBloodCultureDto = applySampleItemBloodCultureService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySampleItemBloodCultureDto)) {
            return Collections.EMPTY_LIST;
        }
        Long applySampleItemId = applySampleItemBloodCultureDto.getApplySampleItemId();
        ApplySampleItemDto applySampleItemDto = applySampleItemService.selectById(applySampleItemId);
        Integer count = applySampleItemDto.getCount();
        if (!Objects.isNull(applySampleItemDto) && !Objects.isNull(count)) {
            applySampleItemBloodCultureDto.setCount(count);
        }
        return applySampleItemBloodCultureDto;
    }

    /**
     * 更新血培养项目
     */
    @PostMapping("/updateBloodCulture")
    public Object updateBloodCulture(@RequestBody ApplySampleItemBloodCultureVo vo) {
        if (Objects.isNull(vo)) {
            throw new IllegalArgumentException("修改的血培养信息不能为空");
        }
        ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(vo.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("申请样本信息不存在");
        }
        if (ObjectUtil.equal(YesOrNoEnum.YES.getCode(), applySampleDto.getIsTwoPick())) {
            throw new IllegalStateException("该样本已经完成二次分拣,请取消二次分拣后重试");
        }
        ApplySampleItemDto applySampleItemDto = new ApplySampleItemDto();
        applySampleItemDto.setApplySampleItemId(vo.getApplySampleItemId());
        applySampleItemDto.setCount(vo.getCount());
        applySampleItemService.updateById(applySampleItemDto);
        ApplySampleItemBloodCultureDto applySampleItemBloodCultureDto = JSON.parseObject(JSON.toJSONString(vo), ApplySampleItemBloodCultureDto.class);
        return applySampleItemBloodCultureService.updateByCultureId(applySampleItemBloodCultureDto);
    }

    /**
     * 批量修改送检机构-更新样本信息前校验是否提示刷新报告
     */
    @PostMapping("/before-check-batch-update-hsp-org-tips")
    public Object beforeCheckBatchUpdateHspOrgTips(@RequestBody BatchUpdateHspOrgVo vo) {
        if (Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalArgumentException("请选择送检机构");
        }
        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalArgumentException("送检机构不存在");
        }
        if (CollectionUtils.isEmpty(vo.getApplyIds())) {
            throw new IllegalArgumentException("请选择申请单");
        }
        // 对应申请单
        List<ApplyDto> applyDtoList = applyService.selectByApplyIds(vo.getApplyIds());
        if (CollectionUtils.isEmpty(applyDtoList)) {
            throw new IllegalArgumentException("申请单不存在");
        }
        // 送检机构发生修改的申请单
        List<ApplyDto> applyList = applyDtoList.stream()
                .filter(obj -> !Objects.equals(obj.getHspOrgId(), vo.getHspOrgId())).collect(Collectors.toList());

        ApplyUpdateBeforeCheckTipDto checkTipDto = new ApplyUpdateBeforeCheckTipDto();
        List<ApplyUpdateBeforeCheckTipItemDto> criticalTips = new ArrayList<>();
        List<ApplyUpdateBeforeCheckTipItemDto> exceptionTips = new ArrayList<>();
        checkTipDto.setCriticals(criticalTips);
        checkTipDto.setExceptions(exceptionTips);

        if (CollectionUtils.isEmpty(applyList)) {
            return Map.of("count", NumberUtils.INTEGER_ZERO, "oneAuditCount", NumberUtils.INTEGER_ZERO, "checkTip",
                    checkTipDto);
        }
        // 申请单ids
        List<Long> applyIds = applyList.stream().map(ApplyDto::getApplyId).collect(Collectors.toList());
        Map<Long, List<ApplySampleDto>> applySampleByApplyId = applySampleService.selectByApplyIds(applyIds).stream()
                .collect(Collectors.groupingBy(ApplySampleDto::getApplyId));

        // 已经一审，二审的状态下，修改 送检机构 校验是否触发异常和危急值
        final List<Integer> auditStatus =
                List.of(SampleStatusEnum.AUDIT.getCode(), SampleStatusEnum.ONE_AUDIT.getCode());

        // 已审、一审 数量
        int auditCount = 0;
        int oneAuditCount = 0;
        for (ApplyDto applyDto : applyList) {
            List<ApplySampleDto> applySamples = applySampleByApplyId.get(applyDto.getApplyId());
            Set<Integer> status = applySamples.stream().map(ApplySampleDto::getStatus).collect(Collectors.toSet());
            if (status.stream().noneMatch(auditStatus::contains)) {
                // 不存在 一审，二审的状态
                continue;
            }
            if (status.contains(SampleStatusEnum.AUDIT.getCode())) {
                auditCount += 1;
            }
            if (status.contains(SampleStatusEnum.ONE_AUDIT.getCode())) {
                oneAuditCount += 1;
            }

            // 以新的送检机构为准
            applyDto.setHspOrgId(hspOrganization.getHspOrgId());
            applyDto.setHspOrgCode(hspOrganization.getHspOrgCode());
            applyDto.setHspOrgName(hspOrganization.getHspOrgName());

            List<ApplySampleDto> auditApplySampleDtos =
                    applySamples.stream().filter(e -> auditStatus.contains(e.getStatus())).collect(Collectors.toList());

            ApplyUpdateBeforeCheckTipDto routineCheckTipDto = sampleService.updateCheckExceptionAndCritical(
                    auditApplySampleDtos.stream().filter(e -> ItemTypeEnum.ROUTINE == ItemTypeEnum.valueOf(e.getItemType()))
                            .collect(Collectors.toList()),
                    applyDto);
            criticalTips.addAll(routineCheckTipDto.getCriticals());
            exceptionTips.addAll(routineCheckTipDto.getExceptions());

            ApplyUpdateBeforeCheckTipDto outsourcingCheckTipDto =
                    outsourcingSampleService.updateCheckExceptionAndCritical(auditApplySampleDtos.stream()
                            .filter(e -> ItemTypeEnum.OUTSOURCING == ItemTypeEnum.valueOf(e.getItemType()))
                            .collect(Collectors.toList()), applyDto);
            criticalTips.addAll(outsourcingCheckTipDto.getCriticals());
            exceptionTips.addAll(outsourcingCheckTipDto.getExceptions());
        }
        return Map.of("count", auditCount, "oneAuditCount", oneAuditCount, "checkTip", checkTipDto);
    }

    /**
     * 批量修改送检机构
     */
    @PostMapping("/batch-update-hsp-org")
    public Object batchUpdateHspOrg(@RequestBody BatchUpdateHspOrgVo vo) {
        Map<String, Object> tipsMap = (Map<String, Object>) beforeCheckBatchUpdateHspOrgTips(vo);
        Integer oneAuditCount = (Integer) tipsMap.get("oneAuditCount");
        Integer count = (Integer) tipsMap.get("count");
        ApplyUpdateBeforeCheckTipDto checkTipDto = (ApplyUpdateBeforeCheckTipDto) tipsMap.get("checkTip");
        if ((oneAuditCount > 0 || count > 0) && Objects.nonNull(checkTipDto)
                && CollectionUtils.isNotEmpty(checkTipDto.getCriticals())) {
            throw new IllegalStateException("存在危急值，不能修改");
        }
        List<String> lockKeys = Lists.newArrayList();
        for (Long applyId : vo.getApplyIds()) {
            final String lockKey = redisPrefix.getBasePrefix() + "apply:lock:" + applyId;
            lockKeys.add(lockKey);
            if (BooleanUtils.isNotTrue(
                    stringRedisTemplate.opsForValue().setIfAbsent(lockKey, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
                throw new IllegalStateException("所选申请单存在正在被修改项,请稍后再试");
            }
        }
        try {
            BatchUpdateApplyDto dto = new BatchUpdateApplyDto();
            dto.setApplyIds(vo.getApplyIds());
            dto.setRefreshReport(vo.getRefreshReport());
            dto.setHspOrgId(vo.getHspOrgId());
            applyService.batchUpdateApply(dto);
            return Collections.emptyMap();
        } finally {
            stringRedisTemplate.delete(lockKeys);
        }
    }

    /**
     * 校验参数是否为空和不不能为负数
     *
     * @param value
     */
    private static void validateField(Integer value) {
        if (Objects.isNull(value)) {
            throw new IllegalArgumentException("修改数据值不能为空");
        } else if (value < 0) {
            throw new IllegalStateException("修改数据值不能为负数");
        }
    }

    @SneakyThrows
    @Nonnull
    private List<Map<String, Object>> convertToBarcodeInfo(List<ApplySampleDto> applySamples) {
        final Collection<Long> applyIds =
                applySamples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet());

        // 多线程异步查询
        final ExecutorService executorService = threadPoolConfig.getPool();
        final Future<Map<Long, ApplyDto>> applyMapFuture =
                executorService.submit(() -> applyService.selectByApplyIdsAsMap(applyIds));

        // 查询检验项目
        List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService
                .selectByApplySampleIds(
                        applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));
        Map<Long, TestItemDto> testItemDtoMap = testItemService.selectByTestItemIds(applySampleItemDtos.stream()
                        .map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet())).stream()
                .collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity(), (a, b) -> a));
        // 检验项目名称
        final Map<Long, List<String>> testItemNameMap = applySampleItemDtos.stream().collect(
                Collectors.groupingBy(ApplySampleItemDto::getApplySampleId,
                        Collectors.mapping(ApplySampleItemDto::getTestItemName, Collectors.toList())));
        // 检验项目名称（优先取项目缩写，然后取英文名称，最后取项目名称）
        final Map<Long, List<String>> testItemEnNameMap = applySampleItemDtos.stream().collect(
                Collectors.groupingBy(ApplySampleItemDto::getApplySampleId,
                        Collectors.mapping(e -> {
                            TestItemDto testItemDto = testItemDtoMap.get(e.getTestItemId());
                            if (Objects.isNull(testItemDto)) {
                                return StringUtils.EMPTY;
                            }
                            // 如果shortName配置成”-“，则当做空处理
                            final String shortName = StringPool.DASH.equals(testItemDto.getShortName()) ? StringUtils.EMPTY : testItemDto.getShortName();
                            return StringUtils.defaultIfBlank(shortName, StringUtils.defaultIfBlank(testItemDto.getEnName(), testItemDto.getTestItemName()));
                        }, Collectors.toList())));

        // 申请单样本专业组 # 东莞在签收的时候会指定自定义码分条码，会分到不同专业组
        Map<Long, String> applySampleGroupMap = applySampleItemDtos.stream().collect(
                Collectors.toMap(ApplySampleItemDto::getApplySampleId, ApplySampleItemDto::getGroupName, (a, b) -> b));

        // 查询申请单
        final Map<Long, ApplyDto> applyMap = applyMapFuture.get(15, TimeUnit.SECONDS);

        return applySamples.stream().map(m -> {
            final Optional<ApplyDto> applyOptional = Optional.ofNullable(applyMap.get(m.getApplyId()));

            // 专业组
            String groupName = StringUtils.defaultIfBlank(m.getGroupName(), applySampleGroupMap.get(m.getApplySampleId()));
            // 如果是委外的，则不显示医院名称
            String hspOrgName = applyOptional.map(ApplyDto::getHspOrgName).orElse(StringUtils.EMPTY);
            if (groupName.contains("委外")) {
                hspOrgName = "";
            }
            return Dict.of("applySampleId", m.getApplySampleId(),
                    //
                    "masterBarcode", applyOptional.map(ApplyDto::getMasterBarcode).orElse(StringUtils.EMPTY),
                    //
                    "barcode", m.getBarcode(),
                    // 签收时间
                    "signDate", applyOptional.map(ApplyDto::getSignDate).orElse(new Date()),
                    //
                    "patientName", applyOptional.map(ApplyDto::getPatientName).orElse(StringUtils.EMPTY),
                    //
                    "groupName", groupName,
                    //
                    "patientSex", applyOptional.map(ApplyDto::getPatientSex).orElse(SexEnum.DEFAULT.getCode()),
                    //
                    "patientAge", applyOptional.map(ApplyDto::getPatientAge).orElse(0),
                    //
                    "patientSubage", applyOptional.map(ApplyDto::getPatientSubage).orElse(0),
                    //
                    "patientSubageUnit", applyOptional.map(ApplyDto::getPatientSubageUnit).orElse(StringUtils.EMPTY),
                    //
                    "testItemNames", testItemNameMap.get(m.getApplySampleId()),
                    //
                    "testItemEnNames", testItemEnNameMap.get(m.getApplySampleId()),
                    //
                    "hspOrgName", hspOrgName,
                    //
                    "sampleType", m.getSampleTypeName(),
                    //
                    "applyType", applyOptional.map(ApplyDto::getApplyTypeName).orElse(StringUtils.EMPTY),
                    //
                    "patientVisitCard", applyOptional.map(ApplyDto::getPatientVisitCard).orElse(StringUtils.EMPTY),
                    //
                    "patientBed", applyOptional.map(ApplyDto::getPatientBed).orElse(StringUtils.EMPTY),
                    //
                    "tube", m.getTubeName());

        }).collect(Collectors.toList());
    }

    /**
     * bytes 转成 list
     */
    private static <T> List<T> deserializeObject(byte[] bytes, Class<T> tClass) {
        try {
            ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
            ObjectInputStream ois = new ObjectInputStream(bais);
            return (List<T>) ois.readObject();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将 list 转成 byte[]
     */
    private static byte[] serializeList(List<Object> list) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            oos.writeObject(list);
            oos.flush();
            return baos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 检查申请单样本参数
     */
    public static void checkApplySampleParam(@NonNull TestApplyVo vo) {
        UpdateTestApplySampleVo updateTestApplySampleVo = (UpdateTestApplySampleVo) vo;

        // 样本类型是否为空
        final String sampleTypeName = updateTestApplySampleVo.getSampleTypeName();
        if (StringUtils.isBlank(sampleTypeName)) {
            throw new IllegalArgumentException("请选择样本类型");
        }
        if (StringUtils.isBlank(updateTestApplySampleVo.getTubeCode())) {
            throw new IllegalArgumentException("清选择管型");
        }
        if (StringUtils.isBlank(updateTestApplySampleVo.getTubeName())) {
            throw new IllegalArgumentException("清选择管型");
        }
    }

    /**
     * 批量加减项目 -- 一次分拣
     */
    @PostMapping("/select-barcodes")
    public Object selectBarcodes(@RequestBody SelectBarcodeDto dto) {
        dto.verifyParams();

        List<ApplyDto> applyDtos = applyService.selectByDate(dto.getStartDate(), dto.getEndDate());

        // 过滤送检机构
        if (Objects.nonNull(dto.getHspOrgId())) {
            applyDtos = applyDtos.stream().filter(e -> Objects.equals(e.getHspOrgId(), dto.getHspOrgId())).collect(Collectors.toList());
        }

        // 申请单样本
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplyIds(applyDtos.stream()
                        .map(ApplyDto::getApplyId).collect(Collectors.toList()))
                .stream()
                .filter(e -> Objects.equals(e.getIsOnePick(), NumberUtils.INTEGER_ZERO)
                        && Objects.equals(e.getStatus(), SampleStatusEnum.ENTER.getCode())
                        && Objects.equals(e.getTubeCode(), dto.getTubeCode()))
                .collect(Collectors.toList());

        applySampleDtos.sort(Comparator.comparing(ApplySampleDto::getCreateDate));

        Set<String> barcodes = new LinkedHashSet<>();

        for (ApplySampleDto applySampleDto : applySampleDtos) {
            barcodes.add(applySampleDto.getBarcode());
        }

        // 过滤体检机构
        if (Objects.nonNull(dto.getPhysicalGroupId())) {

            PhysicalGroupDto physicalGroupDto = physicalGroupService.selectByPhysicalGroupId(dto.getPhysicalGroupId());
            Assert.notNull(physicalGroupDto, "体检机构不存在");

            List<String> physicalBarcodes = physicalSampleService.selectByBarcodes(barcodes, physicalGroupDto.getOrgId())
                    .stream().map(PhysicalSampleDto::getBarcode).collect(Collectors.toList());

            barcodes = barcodes.stream().filter(physicalBarcodes::contains).collect(Collectors.toSet());
        }
        Stream<String> stream = barcodes.stream();
        if (StringUtils.isNotBlank(dto.getBarcode())) {
            stream = stream.filter(e -> Objects.equals(e, dto.getBarcode()));
        }
        return stream
                .map(e -> Map.of("barcode", e, "id", UUID.randomUUID().toString()))
                .collect(Collectors.toList());
    }


    /**
     * 批量加减项目
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/add-test-item")
    public Object addTestItem(@RequestBody BatchTestItemDto dto) {
        if (BooleanUtils.isTrue(dto.getIsAddItem())) {
            dto.verifyAddParams();
        }
        Set<String> barcodes = dto.getBarcodes();
        List<String> onePickBarcode = new LinkedList<>();
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByBarcodes(barcodes).stream()
                .filter(e -> Objects.equals(e.getIsOnePick(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(applySampleDtos)) {
            onePickBarcode = applySampleDtos.stream().map(ApplySampleDto::getBarcode).collect(Collectors.toList());
            onePickBarcode.forEach(barcodes::remove);
        }

        final String lockKey = redisPrefix.getBasePrefix() + "apply:batch:add:item:lock";
        if (BooleanUtils.isNotTrue(
                stringRedisTemplate.opsForValue().setIfAbsent(lockKey, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException("申请单正在被修改,请稍后再试");
        }
        try {
            LoginUserHandler.User user = LoginUserHandler.get();
            Date date = new Date();
            BatchTestItemDto.Item itemOne = dto.getItems().get(0);

            for (String barcode : barcodes) {
                Map<String, Object> map = (Map<String, Object>) this.get(barcode, false);
                ApplyVo applyVo = (ApplyVo) map.get("apply");
                List<SampleItemVo> vos = (List<SampleItemVo>) map.get("applySampleItems");
                List<ApplySampleDto> applySamples = (List<ApplySampleDto>) map.get("applySamples");
                if (CollectionUtils.isEmpty(vos)) {
                    if (BooleanUtils.isNotTrue(dto.getIsAddItem())) {
                        continue;
                    }
                    ApplySampleDto applySampleDto = applySamples.stream().findFirst().orElseThrow(() -> new IllegalArgumentException("未找到样本 :" + barcode));
                    if (!Objects.equals(applySampleDto.getTubeCode() + "-" + applySampleDto.getSampleTypeCode(),
                            itemOne.getTubeCode() + "-" + itemOne.getSampleTypeCode()
                    )) {
                        throw new IllegalArgumentException(String.format("条码[%s]已选项目管型与条码管型不一致，请修改后重试", barcode));
                    }
                    UpdateTestApplySampleVo updateTestApplyVo = new UpdateTestApplySampleVo();

                    BeanUtils.copyProperties(applyVo, updateTestApplyVo);
                    List<UpdateTestApplyItemVo> updateTestApplyItemVos = JSON.parseArray(JSON.toJSONString(dto.getItems()), UpdateTestApplyItemVo.class);
                    updateTestApplyVo.setApplySampleIds(new ArrayList<>((Set<Long>) map.get("applySampleId")));
                    updateTestApplyVo.setTestApplySampleItems(updateTestApplyItemVos);
                    updateTestApplyVo.setBarcode(barcode);
                    //                    updateTestApplyVo.setBatchUpdateItem(true);
                    // 分条码批量加减项目
                    applySampleController.updateConfirm(updateTestApplyVo);
                } else {

                    SampleItemVo sampleItemVo = vos.get(0);
                    if (BooleanUtils.isTrue(dto.getIsAddItem())) {
                        if (!Objects.equals(sampleItemVo.getTubeCode() + "-" + sampleItemVo.getSampleTypeCode(),
                                itemOne.getTubeCode() + "-" + itemOne.getSampleTypeCode()
                        )) {
                            throw new IllegalArgumentException(String.format("条码[%s]已选项目管型与条码管型不一致，请修改后重试", barcode));
                        }

                        List<UpdateTestApplyItemVo> updateTestApplyItemVoList = JSON.parseArray(JSON.toJSONString(vos), UpdateTestApplyItemVo.class);
                        List<UpdateTestApplyItemVo> updateTestApplyItemVos = JSON.parseArray(JSON.toJSONString(dto.getItems()), UpdateTestApplyItemVo.class);

                        updateTestApplyItemVos.addAll(updateTestApplyItemVoList);
                        Map<Long, UpdateTestApplyItemVo> updateTestApplyItemVoMap = updateTestApplyItemVos.stream().collect(Collectors.toMap(TestApplyVo.Item::getTestItemId, Function.identity(), (a, b) -> b));
                        UpdateTestApplyVo updateTestApplyVo = new UpdateTestApplyVo();
                        BeanUtils.copyProperties(applyVo, updateTestApplyVo);
                        updateTestApplyVo.setTestApplyItems(new ArrayList<>(updateTestApplyItemVoMap.values()));
                        updateTestApplyVo.setBatchUpdateItem(true);
                        // 分条码批量加减项目
                        applyController.update(updateTestApplyVo);
                    } else {
                        List<Long> applySampleItems = new ArrayList<>();
                        List<Long> testItemIds = dto.getItems().stream().map(BatchTestItemDto.Item::getTestItemId).collect(Collectors.toList());

                        for (SampleItemVo vo : vos) {
                            if (testItemIds.contains(vo.getTestItemId())) {
                                applySampleItems.add(vo.getApplySampleItemId());
                            }
                        }
                        if (CollectionUtils.isNotEmpty(applySampleItems)) {
                            applyController.deleteSampleItem(applySampleItems, 1, 1, 0);
                        }
                    }
                }

                // @TODO  体检检验样本项目
                PhysicalSampleDto physicalSampleDto = physicalSampleService.selectByBarcode(barcode, user.getOrgId());
                if (Objects.isNull(physicalSampleDto)) {
                    continue;
                }

                List<PhysicalSampleItemDto> physicalSampleItems = physicalSampleItemService.selectByPhysicalSampleId(physicalSampleDto.getPhysicalSampleId());
                Assert.notEmpty(physicalSampleItems, String.format("体检条码 %s 未查询到项目信息", physicalSampleDto.getBarcode()));

                Map<Long, PhysicalSampleItemDto> physicalSampleMap = physicalSampleItems.stream().collect(Collectors.toMap(PhysicalSampleItemDto::getTestItemId, Function.identity(), (a, b) -> b));

                List<Long> testItemIds = dto.getItems().stream().map(BatchTestItemDto.Item::getTestItemId).collect(Collectors.toList());
                Map<Long, TestItemDto> testItemDtoMap = testItemService.selectByTestItemIdsAsMap(testItemIds);

                if (BooleanUtils.isTrue(dto.getIsAddItem())) {
                    // 加项
                    List<PhysicalSampleItemDto> physicalSampleItemDtos = new ArrayList<>();

                    for (BatchTestItemDto.Item item : dto.getItems()) {
                        if (!physicalSampleMap.containsKey(item.getTestItemId())) {
                            TestItemDto testItemDto = testItemDtoMap.get(item.getTestItemId());
                            physicalSampleItemDtos.add(this.createPhysicalSampleItem(physicalSampleDto, testItemDto, user, date));
                        }
                    }
                    physicalSampleItemService.addPhysicalSampleItems(physicalSampleItemDtos);
                } else {
                    // 减项
                    List<Long> physicalSampleItemIds = new ArrayList<>();
                    physicalSampleMap.forEach((k, v) -> {
                        if (testItemIds.contains(k)) {
                            physicalSampleItemIds.add(v.getPhysicalSampleItemId());
                        }
                    });
                    physicalSampleItemService.deleteById(physicalSampleItemIds);
                    if (physicalSampleItemIds.size() == physicalSampleMap.size()) {
                        // 说明样本下没有项目， 需要把体检样本和申请人都删除
                        physicalSampleService.deleteById(physicalSampleDto.getPhysicalSampleId());
                        physicalRegisterService.deleteById(physicalSampleDto.getPhysicalRegisterId());
                    }
                }

            }

            return onePickBarcode;
        } finally {
            stringRedisTemplate.delete(lockKey);
        }

    }

    private PhysicalSampleItemDto createPhysicalSampleItem(PhysicalSampleDto physicalSampleDto, TestItemDto testItemDto,
                                                           LoginUserHandler.User user, Date date) {
        PhysicalSampleItemDto physicalSampleItemDto = new PhysicalSampleItemDto();
        physicalSampleItemDto.setPhysicalSampleItemId(snowflakeService.genId());
        physicalSampleItemDto.setPhysicalSampleId(physicalSampleDto.getPhysicalSampleId());
        physicalSampleItemDto.setPhysicalRegisterId(physicalSampleDto.getPhysicalRegisterId());
        physicalSampleItemDto.setPhysicalBatchId(physicalSampleDto.getPhysicalBatchId());
        physicalSampleItemDto.setTestItemId(testItemDto.getTestItemId());
        physicalSampleItemDto.setTestItemCode(testItemDto.getTestItemCode());
        physicalSampleItemDto.setTestItemName(testItemDto.getTestItemName());
        physicalSampleItemDto.setOrgId(user.getOrgId());
        physicalSampleItemDto.setOrgName(user.getOrgName());
        physicalSampleItemDto.setCreatorId(user.getUserId());
        physicalSampleItemDto.setCreatorName(user.getNickname());
        physicalSampleItemDto.setCreateDate(date);
        physicalSampleItemDto.setUpdaterId(user.getUserId());
        physicalSampleItemDto.setUpdaterName(user.getNickname());
        physicalSampleItemDto.setUpdateDate(date);
        physicalSampleItemDto.setIsDelete(0);
        return physicalSampleItemDto;
    }

}
