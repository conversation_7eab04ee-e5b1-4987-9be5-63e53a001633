package com.labway.lims.apply.service.chain.pick.one.cancel;

import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 条码环节
 */
@Slf4j
@Component
public class CancelOnePickFlowCommand implements Command, InitializingBean {
    @Resource
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {

        final CancelOnePickContext context = CancelOnePickContext.from(c);

        final ApplySampleDto applySample = context.getApplySample();

        // 添加流水
        sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                .applyId(applySample.getApplyId())
                .applySampleId(applySample.getApplySampleId())
                .barcode(applySample.getBarcode())
                .operateCode(BarcodeFlowEnum.CANCEL_ONE_PICK.name())
                .operateName(BarcodeFlowEnum.CANCEL_ONE_PICK.getDesc())
                .barcode(applySample.getBarcode())
                .content("取消一次分拣").build());

        return CONTINUE_PROCESSING;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
