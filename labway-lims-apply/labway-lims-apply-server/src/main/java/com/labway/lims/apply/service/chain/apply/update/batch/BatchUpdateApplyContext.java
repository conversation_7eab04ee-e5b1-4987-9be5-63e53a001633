package com.labway.lims.apply.service.chain.apply.update.batch;

import java.util.List;

import org.apache.commons.chain.Context;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.BatchUpdateApplyDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.HspOrganizationDto;

import cn.hutool.core.util.IdUtil;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

/**
 * 批量修改申请单信息 上下文
 * 
 * <AUTHOR>
 * @since 2024/2/21 15:01
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class BatchUpdateApplyContext extends StopWatchContext {

    @NonNull
    private BatchUpdateApplyDto batchUpdateApplyDto;

    private LoginUserHandler.User user;

    /**
     * 送检机构
     */
    public static final String HSP_ORG = "HSP_ORG_" + IdUtil.objectId();
    /**
     * 申请单
     */
    public static final String APPLY = "APPLY_" + IdUtil.objectId();

    /**
     * 申请单样本
     */
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    public List<ApplyDto> getApplys() {
        return (List<ApplyDto>)get(APPLY);
    }

    public List<ApplySampleDto> getApplySamples() {
        return (List<ApplySampleDto>)get(APPLY_SAMPLE);
    }

    public HspOrganizationDto getHspOrganization() {
        return (HspOrganizationDto)get(HSP_ORG);
    }

    public static BatchUpdateApplyContext from(Context context) {
        return (BatchUpdateApplyContext)context;
    }

    @Override
    protected String getWatcherName() {
        return "批量修改申请单";
    }
}
