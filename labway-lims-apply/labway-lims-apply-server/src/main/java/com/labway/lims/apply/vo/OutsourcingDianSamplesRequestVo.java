package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 迪安样本
 */
@Getter
@Setter
public class OutsourcingDianSamplesRequestVo {
    /**
     * 开始分拣时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginPickDate;

    /**
     * 结束分拣时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endPickDate;

}
