package com.labway.lims.apply.controller.pda;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.BaseDataQueryDto;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.PdaTobeConfirmedApplyDto;
import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.api.service.pda.PdaTobeConfirmedApplyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <pre>
 * PdaApplyUnfinishWorkController
 * PDA未完成工作 控制器
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/8 15:59
 */
@RestController
@RequestMapping("/pda/unfinish/")
public class PdaApplyUnfinishWorkController extends BaseController {

    @Resource
    private PdaTobeConfirmedApplyService pdaTobeConfirmedApplyService;
    @Resource
    private PdaApplyService pdaApplyService;

    /**
     * PDA未签收列表
     */
    @PostMapping("/select-unsigned")
    public Object selectPdaApply(@RequestBody BaseDataQueryDto dto) {
        Assert.notNull(dto.getStartDate(), "起始时间不能为空");
        Assert.notNull(dto.getEndDate(), "结束时间不能为空");
        if (dto.getStartDate().after(dto.getEndDate())) {
            throw new IllegalArgumentException("起始时间不能晚于结束时间");
        }
        dto.setConfirmed(true);
        // 查询已确认未签收的申请单
        final List<PdaTobeConfirmedApplyDto> pdaTobeConfirmedApplyDtos = pdaTobeConfirmedApplyService.selectByDate(dto);
        final Map<String, PdaTobeConfirmedApplyDto> confirmedApplyDtoMap = pdaTobeConfirmedApplyDtos.stream().collect(
                Collectors.toMap(PdaTobeConfirmedApplyDto::getMasterBarcode, Function.identity(), (a, b) -> b));

        final List<PdaApplyDto> pdaApplyDtos = pdaApplyService.selectByMasterBarcodes(pdaTobeConfirmedApplyDtos.stream()
                .map(PdaTobeConfirmedApplyDto::getMasterBarcode).collect(Collectors.toSet()));

        return pdaApplyDtos.stream().filter(e -> {
                    final String masterBarcode = e.getMasterBarcode();
                    return Objects.equals(e.getPdaApplyId(), confirmedApplyDtoMap.get(masterBarcode).getConfirmedPdaApplyId());
                }).map(e -> {
                    final String masterBarcode = e.getMasterBarcode();
                    final PdaTobeConfirmedApplyDto confirmedApplyDto = confirmedApplyDtoMap.get(masterBarcode);
                    Dict parse = Dict.parse(e);
                    parse.put("confirmUserId", confirmedApplyDto.getUpdaterId());
                    parse.put("confirmUserName", confirmedApplyDto.getUpdaterName());
                    parse.put("confirmDate", confirmedApplyDto.getUpdateDate());

                    return parse;
                })
                .collect(Collectors.toList());

        // return pdaTobeConfirmedApplyDtos;
    }

}
