package com.labway.lims.apply.controller;

import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.QueryUnHandoverSampleDto;
import com.labway.business.center.compare.request.HandoverSampleMainInfoRequest;
import com.labway.business.center.compare.request.QuerySampleInfoForHandoverRequest;
import com.labway.business.center.compare.request.QueryUnHandoverSampleRequest;
import com.labway.business.center.compare.request.ReturnApplySampleMainRequest;
import com.labway.business.center.compare.service.OutApplyInfoService;
import com.labway.business.center.compare.service.TbOrgApplySampleMainService;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.HandoverRecordQueryDto;
import com.labway.lims.apply.api.dto.PreprocessingHandoverRecordDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.PreprocessingHandoverRecordService;
import com.labway.lims.apply.vo.*;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/preprocessing-handover")
public class PreprocessingHandoverController extends BaseController {

    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @Resource
    private PreprocessingHandoverRecordService preprocessingHandoverRecordService;
    @Resource
    private TbOrgApplySampleMainService tbOrgApplySampleMainService;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private OutApplyInfoService outApplyInfoService;

    @Resource
    private Environment environment;
    @Resource
    private EnvDetector envDetector;

    @Resource
    private ApplySampleService applySampleService;


    /**
     * 交接
     */
    @PostMapping("/handover")
    public Object handover(@RequestBody HandoverVo vo) {
        final String hspOrgCode = vo.getHspOrgCode();
        if (StringUtils.isBlank(hspOrgCode)) {
            throw new IllegalArgumentException("请选择送检机构");
        }


        final String orgCode = envDetector.getBusinessCenterOrgCode();
        if (StringUtils.isBlank(orgCode)) {
            throw new IllegalArgumentException(String.format("请配置 %s 参数", EnvDetector.BUSINESS_CENTER_ORG_CODE));
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgCode(hspOrgCode);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalArgumentException("送检机构不存在");
        }

        final List<HandoverVo.Sample> rollbackSamples = vo.getSamples();
        if (CollectionUtils.isEmpty(rollbackSamples)) {
            throw new IllegalArgumentException("请选择交接的样本");
        }

        final Set<String> barcodes = rollbackSamples.stream().map(HandoverVo.Sample::getBarcode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(barcodes)) {
            throw new IllegalArgumentException("请选择交接的样本");
        }

        final QuerySampleInfoForHandoverRequest sampleRequest = new QuerySampleInfoForHandoverRequest();
        sampleRequest.setHspOrgCode(hspOrganization.getHspOrgCode());
        sampleRequest.setBarCodes(new ArrayList<>(barcodes));
        sampleRequest.setOrgId(orgCode);
        log.info("开始调用业务中台未交接样本信息查询接口, 参数: [{}]", JSON.toJSONString(sampleRequest));
        final Response<List<QueryUnHandoverSampleDto>> sampleResp = outApplyInfoService.querySampleInfoForHandover(sampleRequest);
        if (Objects.isNull(sampleResp)) {
            throw new IllegalArgumentException("调用业务中台未交接样本信息查询接口失败，业务中台返回消息为空");
        }

        if (!Objects.equals(sampleResp.getCode(), 0)) {
            throw new IllegalArgumentException("调用业务中台未交接样本信息查询接口失败，业务中台返回消息：" + sampleResp.getMsg());
        }

        final List<QueryUnHandoverSampleDto> samples = sampleResp.getData();
        if (CollectionUtils.isEmpty(samples)) {
            throw new IllegalArgumentException(String.format("条码 %s 不存在", barcodes));
        }


        log.info("样本交接-查询样本业务中台返回 samples [{}]", JSON.toJSONString(samples));

        Set<String> notExitsBarcodes = new HashSet<>();

        //校验是否缺失条码
        for (HandoverVo.Sample sample : rollbackSamples) {
            final String barcode = sample.getBarcode();
            if (samples.stream().noneMatch(x -> Objects.equals(x.getBarcode(), barcode))) {
                notExitsBarcodes.add(barcode);
            }
        }

        if (CollectionUtils.isNotEmpty(notExitsBarcodes)) {
            throw new IllegalArgumentException(String.format("条码 %s 在业务中台不存在", notExitsBarcodes));
        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        final LinkedList<Long> ids = snowflakeService.genIds(samples.size());
        final Date now = new Date();

        final List<PreprocessingHandoverRecordDto> records = samples.stream().map(m -> {
            PreprocessingHandoverRecordDto record = new PreprocessingHandoverRecordDto();
            record.setHandoverId(ids.pop());
            record.setBarcode(StringUtils.defaultString(m.getBarcode()));
            record.setOutItems(StringUtils.defaultString(m.getOutTestItemName()));
            record.setPatientName(StringUtils.defaultString(m.getPatientName()));
            record.setPatientSex(ObjectUtils.defaultIfNull(m.getPatientSex(), SexEnum.DEFAULT.getCode()));
            record.setPatientAge(ObjectUtils.defaultIfNull(m.getPatientAge(), 0));
            record.setPatientSubAge(ObjectUtils.defaultIfNull(m.getPatientSubage(), 0));
            record.setPatientSubAgeUnit(StringUtils.defaultString(m.getPatientSubageUnit()));
            record.setSamplingDate(m.getTakeSampleTime());
            record.setHandoverDate(now);
            record.setHandoverPeople(user.getNickname());
            record.setHandoverPeopleId(user.getUserId());
            record.setLogisticsPeople(StringUtils.defaultString(m.getStaffName()));
            record.setTubeName(StringUtils.defaultString(m.getTubeType()));
            record.setSampleTypeName(StringUtils.defaultString(m.getSampleType()));
            record.setHspOrgId(hspOrganization.getHspOrgId());
            record.setHspOrgName(hspOrganization.getHspOrgName());
            record.setHspOrgCode(hspOrganization.getHspOrgCode());
            record.setIsDelete(YesOrNoEnum.NO.getCode());
            record.setCreatorId(user.getUserId());
            record.setCreatorName(user.getNickname());
            record.setCreateDate(now);
            record.setUpdateDate(now);
            record.setUpdaterId(user.getUserId());
            record.setUpdaterName(user.getNickname());
            record.setOrgId(user.getOrgId());
            record.setOrgName(user.getOrgCode());
            record.setOrgName(user.getOrgName());
            record.setFormCode(StringUtils.defaultString(m.getFormCode()));
            return record;
        }).collect(Collectors.toList());

        preprocessingHandoverRecordService.addBatch(records);


        final List<HandoverSampleMainInfoRequest.HandoverSampleMainInfo> infos = samples.stream().map(m -> {
            HandoverSampleMainInfoRequest.HandoverSampleMainInfo handoverSampleMainInfo
                    = new HandoverSampleMainInfoRequest.HandoverSampleMainInfo();
            handoverSampleMainInfo.setHspOrgCode(hspOrganization.getHspOrgCode());
            handoverSampleMainInfo.setFormCode(m.getFormCode());
            handoverSampleMainInfo.setBarCode(m.getBarcode());
            return handoverSampleMainInfo;
        }).collect(Collectors.toList());

        final HandoverSampleMainInfoRequest handoverRequest = new HandoverSampleMainInfoRequest();
        handoverRequest.setHandoverSampleMainInfo(infos);
        handoverRequest.setOptId(user.getUserId().toString());
        handoverRequest.setOptName(user.getNickname());


        log.info("开始调用业务中台交接样本信息接口, 参数: [{}]", JSON.toJSONString(handoverRequest));
        final Response<?> response = outApplyInfoService.handoverSampleMainInfo(handoverRequest);
        if (Objects.isNull(response)) {
            preprocessingHandoverRecordService.deleteByHandoverIds(ids);
            throw new IllegalArgumentException("调用业务中台交接样本接口失败，业务中台返回消息为空");
        }

        if (!Objects.equals(response.getCode(), 0)) {
            preprocessingHandoverRecordService.deleteByHandoverIds(ids);
            throw new IllegalArgumentException("调用业务中台交接样本接口失败，业务中台返回消息：" + response.getMsg());
        }


        return Map.of();
    }

    /**
     * 退回
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/rollback")
    public Object rollback(@RequestBody RollbackHandoverVo vo) {
        final List<HandoverVo.Sample> samples = vo.getSamples();
        if (CollectionUtils.isEmpty(samples)) {
            throw new IllegalArgumentException("请选择退回样本");
        }
        final String hspOrgCode = vo.getHspOrgCode();
        if (Objects.isNull(hspOrgCode)) {
            throw new IllegalArgumentException("请选择送检机构");
        }

        final String reason = vo.getReason();
        if (StringUtils.isBlank(reason)) {
            throw new IllegalArgumentException("退回原因不能为空");
        }

        if (StringUtils.length(reason) > TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException("退回原因不能超过" + TEXTAREA_MAX_LENGTH + "个字符");
        }
        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgCode(hspOrgCode);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalArgumentException("送检机构不存在");
        }

        final Set<String> barcodes = samples.stream().map(HandoverVo.Sample::getBarcode)
                .collect(Collectors.toSet());

        final Set<String> exitsBarcodes = applySampleService
                .selectByOutBarcodesAndHspOrgId(hspOrganization.getHspOrgCode(), barcodes)
                .stream().map(ApplySampleDto::getOutBarcode).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(exitsBarcodes)) {
            throw new IllegalStateException("条码 " + exitsBarcodes + " 已签收，不能退回");
        }

        final String nickname = LoginUserHandler.get().getNickname();
        final String userId = LoginUserHandler.get().getUserId().toString();

        for (HandoverVo.Sample sample : samples) {
            if (StringUtils.isBlank(sample.getFormCode())) {
                throw new IllegalArgumentException("请选择退回样本的申请单号");
            }

            if (StringUtils.isBlank(sample.getBarcode())) {
                throw new IllegalArgumentException("请选择退回样本的条码");
            }
        }

        for (HandoverVo.Sample sample : samples) {
            preprocessingHandoverRecordService.deleteByHspOrgCodeAndBarcodes(hspOrganization.getHspOrgCode(), Collections.singleton(sample.getBarcode()));
            final ReturnApplySampleMainRequest request = new ReturnApplySampleMainRequest();
            request.setReturnReason(reason);
            request.setBarcode(sample.getBarcode());
            request.setHspOrgCode(hspOrganization.getHspOrgCode());
            request.setOptId(userId);
            request.setOptName(nickname);
            log.info("开始调用业务中台退回样本接口, 参数: [{}]", JSON.toJSONString(request));
            final Response<String> response = tbOrgApplySampleMainService.returnApplySampleMain(request);
            if (Objects.isNull(response)) {
                throw new IllegalArgumentException("调用业务中台退回样本接口失败，业务中台返回消息为空");
            }
            if (!Objects.equals(response.getCode(), 0)) {
                throw new IllegalArgumentException(
                        String.format("调用业务中台退回样本接口失败，业务中台返回消息 [%s]", response.getMsg()));
            }
        }

        log.info("样本交接-退回 参数 [{}]", JSON.toJSONString(vo));

        return Map.of();
    }


    /**
     * 未交接列表
     */
    @PostMapping("/un-records")
    public Object unHandoverRecords(@RequestBody UnRecordRequestVo vo) {

        final String orgCode = envDetector.getBusinessCenterOrgCode();
        if (StringUtils.isBlank(orgCode)) {
            throw new IllegalArgumentException(String.format("请配置 %s 参数", EnvDetector.BUSINESS_CENTER_ORG_CODE));
        }

        final QueryUnHandoverSampleRequest request = new QueryUnHandoverSampleRequest();
        request.setOrgId(orgCode);
        request.setTakeSampleTimeBegin(vo.getBeginDate());
        request.setTakeSampleTimeEnd(vo.getEndDate());

        if (Objects.nonNull(vo.getHspOrgId())) {
            final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
            if (Objects.isNull(hspOrganization)) {
                throw new IllegalArgumentException("送检机构不存在");
            }
            request.setHspOrgCode(hspOrganization.getHspOrgCode());
        }


        log.info("查询业务中台未交接列表, request: {}", JSON.toJSONString(request));
        final Response<List<QueryUnHandoverSampleDto>> listResponse = outApplyInfoService.queryUnHandoverSample(request);
        if (Objects.isNull(listResponse)) {
            throw new IllegalArgumentException("查询未交接列表失败，业务中台返回消息为空");
        }

        if (!Objects.equals(listResponse.getCode(), 0)) {
            throw new IllegalArgumentException("查询未交接列表失败，业务中台返回消息：" + listResponse.getMsg());
        }

        return listResponse.getData();
    }

    /**
     * 前处理交接记录
     */
    @PostMapping("/records")
    public Object handoverRecords(@RequestBody PreprocessingHandoverRecordQueryVo vo) {
        final Long hspOrgId = vo.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            return List.of();
        }
        vo.defaultDate();

        final List<PreprocessingHandoverRecordDto> dtos = preprocessingHandoverRecordService
                .handoverRecords(JSON.parseObject(JSON.toJSONString(vo), HandoverRecordQueryDto.class));

        return JSON.parseArray(JSON.toJSONString(dtos), PreprocessingHandoverRecordVo.class);
    }


}
