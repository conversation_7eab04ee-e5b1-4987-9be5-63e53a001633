package com.labway.lims.apply.service.chain.pda.apply.update;

import com.labway.lims.apply.service.chain.apply.add.AddDiagnosisCommand;
import com.labway.lims.apply.service.chain.apply.add.AddHspOrgDeptOrDoctorCommand;
import com.labway.lims.apply.service.chain.pda.apply.add.PdaCheckTestItemLimitSexCommand;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class PdaUpdateApplyChain extends ChainBase implements InitializingBean {


    @Resource
    private PdaUpdateCheckParamCommand pdaUpdateCheckParamCommand;
    @Resource
    private PdaUpdateApplyCommand pdaUpdateApplyCommand;
    @Resource
    private PdaUpdateApplySampleItemCommand pdaUpdateApplySampleItemCommand;
    @Resource
    private PdaUpdatePgsqlCommand pdaUpdatePgsqlCommand;
    @Resource
    private PdaUpdatePostCommand pdaUpdatePostCommand;
    @Resource
    private PdaUpdateAbolishCommand pdaUpdateAbolishCommand;
    @Resource
    private PdaCheckTestItemLimitSexCommand pdaCheckTestItemLimitSexCommand;
    @Resource
    private AddHspOrgDeptOrDoctorCommand addHspOrgDeptOrDoctorCommand;

    @Resource
    private AddDiagnosisCommand addDiagnosisCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 校验参数
        addCommand(pdaUpdateCheckParamCommand);

        // 更新申请单数据
        addCommand(pdaUpdateApplyCommand);

        // 判断是否作废
        addCommand(pdaUpdateAbolishCommand);

        // 更新样本项目数据
        addCommand(pdaUpdateApplySampleItemCommand);

        // 检验项目限制性别
        addCommand(pdaCheckTestItemLimitSexCommand);

        //新增送检机构部门或医生基础数据
        addCommand(addHspOrgDeptOrDoctorCommand);

        // 新增临床诊断
        addCommand(addDiagnosisCommand);

        // 更新数据库
        addCommand(pdaUpdatePgsqlCommand);

        // 后置处理
        addCommand(pdaUpdatePostCommand);

        addCommand(e -> PROCESSING_COMPLETE);

    }
}
