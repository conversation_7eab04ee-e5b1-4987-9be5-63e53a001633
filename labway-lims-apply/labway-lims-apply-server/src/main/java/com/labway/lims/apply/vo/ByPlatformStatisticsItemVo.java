package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

@Getter
@Setter
public class ByPlatformStatisticsItemVo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 送检时间
     */
    private String sendDate;

    /**
     * 病理号  | 门诊|住院 号
     */
    private String pathologyNo;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 患者性别
     */
    private String patientSex;

    /**
     * 患者年龄
     */
    private String patientAge;


    /**
     * 就诊类型
     */
    private String applyTypeName;

    /**
     *检验项目
     */
    private String testItems;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 金额
     */
    private BigDecimal price;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ByPlatformStatisticsItemVo that = (ByPlatformStatisticsItemVo) o;
        return Objects.equals(applyId, that.applyId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(applyId);
    }
}
