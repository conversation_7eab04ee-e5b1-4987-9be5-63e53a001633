package com.labway.lims.apply.config;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <p>
 * BusinessConfig
 * 配置信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/22 19:08
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "business")
public class BusinessConfig {

    /**
     * 机构code对应业务中台code
     */
    private Map<String, String> orgCodeMap;

    /**
     * 机构code对应 出库对象信息
     */
    private Map<String, OutCustomer> orgCodeCustomerMap;

    /**
     * @since 2024-11-28 dev-dongguan
     * 仪器传输结果，是否过滤质控样本数据 1：过滤，0：不过滤
     */
    private Integer filterQcSampleNo = YesOrNoEnum.YES.getCode();

}
