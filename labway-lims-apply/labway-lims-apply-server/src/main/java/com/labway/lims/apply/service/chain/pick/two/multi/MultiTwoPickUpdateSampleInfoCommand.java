
package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.service.chain.pick.two.TwoPickContext;
import com.labway.lims.apply.service.chain.pick.two.TwoPickUpdateSampleInfoCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 修改申请单样本信息
 */
@Slf4j
@Component
public class MultiTwoPickUpdateSampleInfoCommand implements Command {

    @Resource
    private TwoPickUpdateSampleInfoCommand twoPickUpdateSampleInfoCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final MultiTwoPickContext context = MultiTwoPickContext.from(c);

        final TwoPickContext ctx = new TwoPickContext(new TwoPickDto());
        ctx.getApplySampleTwoPicks().addAll(context.getApplySampleTwoPicks());
        twoPickUpdateSampleInfoCommand.execute(ctx);

        return CONTINUE_PROCESSING;
    }


}
