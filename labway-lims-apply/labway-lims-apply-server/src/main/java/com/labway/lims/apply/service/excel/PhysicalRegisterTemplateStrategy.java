package com.labway.lims.apply.service.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 体检花名册导入模板策略
 * 
 * <AUTHOR>
 * @since 2023/5/31 20:56
 */
@Component
public class PhysicalRegisterTemplateStrategy extends AbstractColumnWidthStyleStrategy {
    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell,
        Head head, Integer relativeRowIndex, Boolean isHead) {
        if (BooleanUtils.isNotTrue(isHead)) {
            return;
        }
        // 处理表头
        Drawing<?> drawing = writeSheetHolder.getSheet().createDrawingPatriarch();

        switch (cell.getColumnIndex()) {
            case 2: {
                Comment comment = drawing.createCellComment(new XSSFClientAnchor(1, 0, 0, 0, (short)2, 0, (short)4, 4));
                // 输入批注信息
                comment.setString(new XSSFRichTextString("填写【年龄】时必须填写如下单位：岁、月、周、天；例如6岁、6月、6周、6天"));
                cell.setCellComment(comment);
            }
                break;

        }
    }
}
