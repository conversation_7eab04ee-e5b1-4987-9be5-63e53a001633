package com.labway.lims.apply.vo.openreport;

import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 院感
 */
@Getter
@Setter
public class OpenReportInfectionInspectionVoOpenReport extends OpenReportBaseSampleEsModelVo {
    /**
     * 二审人id
     */
    private Long twoCheckerId;
    /**
     * 二审人
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    private Date twoCheckDate;
    /**
     * 检验标准
     */
    private String standardName;

    /**
     * standardCode
     */
    private String standardCode;
    /**
     * 报告项目
     */
    private List<InfectionReportItem> reportItems;

    /**
     * 报告项目
     */
    @Setter
    @Getter
    public static final class InfectionReportItem extends ReportItem {

        /**
         * 结果范围
         */
        private String range;
        /**
         * 结果 （经过一系列的计算 转换最终得到的结果值）
         */
        private String result;
        /**
         * 检验判定 UP  DOWN  NORMAL
         * @see TestJudgeEnum
         */
        private String judge;
        /**
         * 来源仪器
         */
        private Long instrumentId;
        /**
         * 来源仪器
         */
        private String instrumentName;
        /**
         * 仪器结果
         */
        private String instrumentResult;
    }

}
