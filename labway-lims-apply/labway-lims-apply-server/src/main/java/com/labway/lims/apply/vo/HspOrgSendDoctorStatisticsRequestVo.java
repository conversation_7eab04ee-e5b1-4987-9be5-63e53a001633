package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Set;

/**
 * 机构送检医生统计 参数Vo
 *
 * <AUTHOR>
 * @since 2023/5/18 10:20
 */
@Setter
@Getter
public class HspOrgSendDoctorStatisticsRequestVo {
    /**
     * 送检 时间 查询的就是 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date beginDeliveryDate;
    /**
     * 送检 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endDeliveryDate;
    /**
     * 财务月份 YYYY-MM
     */
    private String financialMonth;

    /**
     * 送检机构
     */
    private Set<Long> hspOrgIds;
    /**
     * 就诊类型
     */
    private String applyType;

    /**
     * 专业组id
     */
    private Long groupId;

    /**
     * 客户名称 类别:1开票名称，2单位名称
     *
     * @see CustomerNameTypeEnum
     */
    private Integer customerNameType;

}
