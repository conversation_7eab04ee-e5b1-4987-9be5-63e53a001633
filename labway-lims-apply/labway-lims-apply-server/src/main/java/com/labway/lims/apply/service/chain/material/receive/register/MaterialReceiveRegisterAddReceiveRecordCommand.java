
package com.labway.lims.apply.service.chain.material.receive.register;

import com.google.common.collect.Lists;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.MaterialReceiveRecordStatusEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRecordDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRegisterItemDto;
import com.labway.lims.apply.api.service.MaterialReceiveRecordService;
import com.labway.lims.apply.mapstruct.MaterialReceiveRecordConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 领用登记 创建 领用记录
 *
 * <AUTHOR>
 * @since 2023/5/9 10:06
 */
@Slf4j
@Component
public class MaterialReceiveRegisterAddReceiveRecordCommand implements Command {
    @Resource
    private MaterialReceiveRecordConverter materialReceiveRecordConverter;
    @Resource
    private MaterialReceiveRecordService materialReceiveRecordService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context context) throws Exception {

        final MaterialReceiveRegisterContext from = MaterialReceiveRegisterContext.from(context);
        var user = from.getUser();
        var registerItemList = from.getRegisterItemList();
        var materialInventoryDtos = from.getMaterialInventoryDtos();

        // 领用 信息 key:物料库存id value：领用信息
        final Map<Long, MaterialReceiveRegisterItemDto> registerItemByInventoryId = registerItemList.stream()
            .collect(Collectors.toMap(MaterialReceiveRegisterItemDto::getInventoryId, Function.identity()));

        List<MaterialReceiveRecordDto> targetList = Lists.newArrayListWithCapacity(materialInventoryDtos.size());
        LinkedList<Long> genIds = snowflakeService.genIds(materialInventoryDtos.size());

        Date date = new Date();
        for (MaterialInventoryDto inventoryDto : materialInventoryDtos) {
            MaterialReceiveRegisterItemDto itemDto = registerItemByInventoryId.get(inventoryDto.getInventoryId());

            MaterialReceiveRecordDto target = materialReceiveRecordConverter.fromMaterialInventoryDto(inventoryDto);

            target.setReceiveId(genIds.pop());
            target.setReceiveMainNumber(itemDto.getReceiveMainNumber());
            target.setReceiveAssistNumber(itemDto.getReceiveAssistNumber());
            target.setReceiverId(user.getUserId());
            target.setReceiverName(user.getNickname());
            target.setReceiverDate(date);
            target.setStatus(MaterialReceiveRecordStatusEnum.RECEIVED.getCode());
            target.setInvalidUserId(NumberUtils.LONG_ZERO);
            target.setInvalidUserName(StringUtils.EMPTY);
            target.setInvalidDate(DefaultDateEnum.DEFAULT_DATE.getDate());

            target.setGroupId(user.getGroupId());
            target.setGroupName(user.getGroupName());
            target.setOrgId(user.getOrgId());
            target.setOrgName(user.getOrgName());
            target.setCreateDate(date);
            target.setUpdateDate(date);
            target.setCreatorId(user.getUserId());
            target.setCreatorName(user.getNickname());
            target.setUpdaterId(user.getUserId());
            target.setUpdaterName(user.getNickname());
            target.setIsDelete(YesOrNoEnum.NO.getCode());

            targetList.add(target);
        }

        materialReceiveRecordService.addMaterialReceiveRecords(targetList);

        return CONTINUE_PROCESSING;
    }
}
