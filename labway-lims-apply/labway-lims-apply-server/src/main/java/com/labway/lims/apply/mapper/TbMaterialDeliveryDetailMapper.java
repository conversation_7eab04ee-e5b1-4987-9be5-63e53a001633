package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.model.TbMaterialDeliveryDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 物料出库详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Mapper
public interface TbMaterialDeliveryDetailMapper extends BaseMapper<TbMaterialDeliveryDetail> {

    /**
     * 批量 插入
     */
    void batchAddMaterialDeliveryDetails(@Param("conditions") List<TbMaterialDeliveryDetail> conditions);

}
