package com.labway.lims.apply.service.chain.applysampleitem.disable;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.DisableOrEnableItemDto;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <pre>
 * CheckEnableParamCommand
 * 项目取消禁用，参数校验
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/12/10 18:47
 */
@Component
public class CheckEnableParamCommand implements Command {

    @Override
    public boolean execute(Context c) throws Exception {
        final ApplySampleItemDisableContext context = ApplySampleItemDisableContext.from(c);

        final DisableOrEnableItemDto disableOrEnableItemDto = context.getDisableOrEnableItemDto();
        final List<DisableOrEnableItemDto.ApplySampleItem> applySampleItems = disableOrEnableItemDto.getApplySampleItems();

        // 禁用操作直接跳过
        if (Objects.equals(disableOrEnableItemDto.getIsDisable(), YesOrNoEnum.YES.getCode())) {
            return CONTINUE_PROCESSING;
        }

        /*
        1. 分血不能取消禁用
        2. 一审，二审不能取消禁用
        3. 终止检验不能取消禁用
         */

        final Map<Long, ApplySampleDto> applySampleMap = context.getApplySampleMap();
        final Map<Long, ApplySampleItemDto> applySampleItemMap = context.getApplySampleItemMap();

        // 过滤出来未禁用的申请单样本项目
        final List<DisableOrEnableItemDto.ApplySampleItem> canEnableItems = applySampleItems.stream().filter(e -> {
            ApplySampleItemDto applySampleItemDto = applySampleItemMap.get(e.getApplySampleItemId());
            if (Objects.isNull(applySampleItemDto)) {
                return false;
            }
            // Objects.requireNonNull(applySampleItemDto, "申请单样本项目不存在");
            return /*!Objects.equals(applySampleDto.getIsDisabled(), YesOrNoEnum.YES.getCode()) &&*/
                    Objects.equals(applySampleItemDto.getIsDisabled(), YesOrNoEnum.YES.getCode());
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(canEnableItems)) {
            throw new IllegalArgumentException("没有需要取消禁用的项目/样本项目已终止");
        }

        final List<ApplySampleItemDto> applySampleItemsEnable = new ArrayList<>();
        for (DisableOrEnableItemDto.ApplySampleItem canEnableItem : canEnableItems) {
            final ApplySampleDto applySampleDto = applySampleMap.get(canEnableItem.getApplySampleId());
            final ApplySampleItemDto applySampleItemDtos = applySampleItemMap.get(canEnableItem.getApplySampleItemId());
            applySampleItemsEnable.add(applySampleItemDtos);

            // 样本状态
            final Integer status = applySampleDto.getStatus();
            if (List.of(SampleStatusEnum.ONE_AUDIT.getCode(), SampleStatusEnum.AUDIT.getCode(),
                    SampleStatusEnum.STOP_TEST.getCode()).contains(status)) {
                throw new IllegalStateException("样本已一审/二审/终止检验，不能取消禁用，请反审/恢复之后重试");
            }

            // 已完成二次分拣
            if (Objects.equals(applySampleDto.getIsTwoPick(), YesOrNoEnum.YES.getCode())) {
                throw new IllegalStateException("该条码已经完成二次分拣，请取消二次分拣后重试");
            }

            // 分血条码
            if (Objects.equals(applySampleDto.getIsSplitBlood(), YesOrNoEnum.YES.getCode())) {
                throw new IllegalStateException("该条码已分血，无法取消禁用，请进行加项");
            }

            final Integer stopStatus = applySampleItemDtos.getStopStatus();
            if (List.of(StopTestStatus.STOP_TEST_FREE.getCode(),
                    StopTestStatus.STOP_TEST_CHARGE.getCode()).contains(stopStatus)) {
                throw new IllegalStateException("项目已终止，请恢复终止后重试");
            }

        }

        context.put(ApplySampleItemDisableContext.ENABLE_ITEMS, applySampleItemsEnable);

        return CONTINUE_PROCESSING;
    }

}
