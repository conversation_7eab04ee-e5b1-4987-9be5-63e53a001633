package com.labway.lims.apply.vo;

import com.labway.business.center.compare.dto.compare.QueryPDASampleInfoDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PDASampleInfoVo extends QueryPDASampleInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 送检类型code
     */
    private String applyTypeCode;

}
