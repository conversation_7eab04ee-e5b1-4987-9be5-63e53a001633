package com.labway.lims.apply.controller.mindrayMT8000;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.apply.service.chain.pick.two.cancel.CancelTwoPickContext;
import com.labway.lims.apply.service.chain.pick.two.cancel.CancelTwoPickDetachedRackCommand;
import com.labway.lims.apply.vo.MT8000HandleVo;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.*;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分拣
 */
@Slf4j
@Component
class MindrayPickAction implements ActionStrategy, InitializingBean {

    /**
     * 生化专业组编码
     */
    @Value("${shenghua.group.code:102}")
    private String shGroupCode;

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private ApplyService applyService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentService instrumentService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private CancelTwoPickDetachedRackCommand cancelTwoPickDetachedRackCommand;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private ReportItemService reportItemService;
    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private InstrumentGroupInstrumentService instrumentGroupInstrumentService;
    @DubboReference
    private RackLogicService rackLogicService;


    /**
     * 全局限流，同一个条码必须有序。如果获取锁失败会等10秒左右，如果10秒内没有获取到锁那么抛出异常
     */
    private void lock(String barcode) throws InterruptedException {
        final String key = redisPrefix.getBasePrefix() + MindrayPickAction.class.getName() + ":" + barcode;

        final long timestamp = System.currentTimeMillis();
        final Duration timeout = Duration.ofSeconds(10);

        do {
            // 锁专业小组
            if (BooleanUtils.isTrue(stringRedisTemplate.opsForValue()
                    .setIfAbsent(key, StringUtils.EMPTY, timeout))) {
                return;
            }

            Thread.yield();

            // 尝试加锁
            synchronized (this) {
                wait(20);
            }

        } while (System.currentTimeMillis() - timestamp < timeout.toMillis());

        throw new IllegalStateException(String.format("条码号 [%s] 正在分拣中", barcode));
    }

    private void unlock(String barcode) {
        stringRedisTemplate.delete(redisPrefix.getBasePrefix() + MindrayPickAction.class.getName() + ":" + barcode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object action(MT8000HandleVo vo) throws Exception {
        // 条码号
        final String barcode = vo.getExtras().getString("barcode");
        // 仪器编码
        final String instrumentCode = vo.getExtras().getString("instrumentCode");
        // 专业组编码
        final String groupCode = vo.getExtras().getString("groupCode");


        // 判断专业组是否存在
        final ProfessionalGroupDto group = groupService.selectByGroupCode(groupCode, LoginUserHandler.get().getOrgId());
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException(String.format("专业组编码 [%s] 不存在", groupCode));
        }

        // 判断仪器是否存在
        InstrumentDto instrumentDto = instrumentService.selectByOrgIdAndInstrumentCode(LoginUserHandler.get().getOrgId(), instrumentCode);
        if (Objects.isNull(instrumentDto)) {
            throw new IllegalArgumentException(String.format("仪器编码 [%s] 不存在", instrumentCode));
        }

        // 判断样本是否分拣
        List<SampleDto> sampleDtos = sampleService.selectAllByBarcode(barcode);
        if (CollectionUtils.isNotEmpty(sampleDtos) && sampleDtos.stream().anyMatch(s -> Objects.equals(s.getGroupId(),group.getGroupId()))) {
            log.warn("迈瑞条码 [%s] 已经分拣过了,不需要分拣操作，直接返回！", barcode);
            return Map.of();
        }

        // 根据专业组查询下面所有的专业小组
        final List<InstrumentGroupDto> instrumentGroups = instrumentGroupService.selectByGroupId(group.getGroupId());
        if (CollectionUtils.isEmpty(instrumentGroups)){
            throw new IllegalArgumentException(String.format("专业组编码 [%s] 没有关联的专业小组", groupCode));
        }

        // 专业小组排序
        instrumentGroups.sort(Comparator.comparing(InstrumentGroupDto::getSort));
        List<Long> instrumentGroupIds = instrumentGroups.stream().map(InstrumentGroupDto::getInstrumentGroupId).collect(Collectors.toList());


        // 查询仪器所在的专业小组
        List<InstrumentGroupInstrumentDto> instrumentGroupInstrumentDtos = instrumentGroupInstrumentService.selectByInstrumentId(instrumentDto.getInstrumentId());
        // 过滤出当前专业组的专业小组
        instrumentGroupInstrumentDtos = instrumentGroupInstrumentDtos.stream().filter(instrumentGroupInstrumentDto -> instrumentGroupIds.contains(instrumentGroupInstrumentDto.getInstrumentGroupId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(instrumentGroupInstrumentDtos)) {
            throw new IllegalArgumentException(String.format("仪器编码 [%s] 在专业组[%s] 没有关联的专业小组", instrumentCode, groupCode));
        }

        // 仪器所在的专业小组id
        List<Long> existInstrumentGroupId = instrumentGroupInstrumentDtos.stream().map(InstrumentGroupInstrumentDto::getInstrumentGroupId).collect(Collectors.toList());
        // 获取到当前仪器所在的专业小组
        // 根据专业组获取到 排序最小且包含当前仪器的专业小组
        InstrumentGroupDto instrumentGroupDto = instrumentGroups.stream().filter(e -> existInstrumentGroupId.contains(e.getInstrumentGroupId())).findFirst().orElse(null);

        // 查询申请单样本信息 -- 并过滤出当前专业组的样本
        final List<ApplySampleDto> applySamples = new ArrayList<>(applySampleService.selectByBarcode(barcode))
                .stream().filter(e -> Objects.equals(e.getGroupId(), group.getGroupId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException(String.format("申请单样本 条码号 [%s] 在专业组 [%s] 下不存在", barcode, group.getGroupName()));
        }

        // 如果已经分拣到过这个专业小组，那么直接报错
        if (sampleService.selectByApplySampleIds(applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()))
                .stream().anyMatch(e -> Objects.equals(e.getInstrumentGroupId(), instrumentGroupDto.getInstrumentGroupId()))) {
            throw new IllegalArgumentException(String.format("条码号 [%s] 已经分拣到专业小组 [%s]", barcode, instrumentGroupDto.getInstrumentGroupName()));
        }

        // 判断是否有检验项目
        final List<ApplySampleItemDto> applySampleItems = new ArrayList<>(applySampleItemService.selectByApplySampleIds(applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalArgumentException(String.format("条码号 [%s] 下没有检验项目", barcode));
        }

        // 锁住
        lock(barcode);

        try {

            // 正常只会有一个样本
            ApplySampleDto applySampleDto = applySamples.get(0);

            // 交接
            handover(barcode,applySampleDto);


            // 分拣
            twoPick(applySampleDto, group, instrumentGroupDto, instrumentDto, vo);

        } finally {
            unlock(barcode);
        }

        return Map.of();
    }


    /**
     * 样本交接 如果交接过了则不需要交接了
     * @param barcode
     * @param applySample
     */
    private void handover(String barcode, ApplySampleDto applySample) {

        final List<RackLogicDto> rackLogics = rackLogicService.selectByApplySampleId(applySample.getApplySampleId());
        if (CollectionUtils.isEmpty(rackLogics)){
            throw new IllegalArgumentException("当前条码不可交接，请检查条码是否已经一次分拣！");
        }

        RackLogicDto next = rackLogics.iterator().next();

        // 二次分拣中说明已经交接过了
        if (Objects.equals(next.getPosition(), RackLogicPositionEnum.TWO_PICKING.getCode())){
            log.info("当前条码 [{}] 已经交接过了，不需要再交接了,", barcode);
            return;
        }

        if (Objects.equals(next.getPosition(), RackLogicPositionEnum.ONE_PICKED.getCode())) {
            // 交接处理
            final RackLogicDto modifyRackLogic = new RackLogicDto();
            modifyRackLogic.setRackLogicId(next.getRackLogicId());

            modifyRackLogic.setPosition(RackLogicPositionEnum.TWO_PICKING.getCode());
            modifyRackLogic.setCurrentGroupId(next.getNextGroupId());
            modifyRackLogic.setCurrentGroupName(next.getNextGroupName());
            modifyRackLogic.setNextGroupId(NumberUtils.LONG_ZERO);
            modifyRackLogic.setNextGroupName(StringUtils.EMPTY);
            modifyRackLogic.setLastHandover(LoginUserHandler.get().getNickname());

            if (!rackLogicService.updateByRackLogicId(modifyRackLogic)) {
                throw new IllegalArgumentException("一次分拣后交接失败");
            }


            final List<RackLogicApplySampleDto> applySamples = applySampleService.selectByRackLogicId(next.getRackLogicId());

            final LinkedList<Long> ids = snowflakeService.genIds(applySamples.size());

            // 记录流水
            sampleFlowService
                    .addSampleFlows(
                            applySamples.stream()
                                    .map(e -> SampleFlowDto.builder().applyId(e.getApplyId()).sampleFlowId(ids.pop())
                                            .applySampleId(e.getApplySampleId()).operateCode(BarcodeFlowEnum.ONE_PICK_HANDOVER.name())
                                            .operateName(BarcodeFlowEnum.ONE_PICK_HANDOVER.getDesc())
                                            .operator(LoginUserHandler.get().getNickname()).operatorId(LoginUserHandler.get().getUserId())
                                            .barcode(e.getBarcode()).content(String.format("从 [%s] 接收到 [%s]",
                                                    next.getCurrentGroupName(), next.getNextGroupName()))
                                            .build())
                                    .collect(Collectors.toList()));
            return;
        }

        throw new IllegalArgumentException(String.format("当前条码不可交接，请检查条码是否已经一次分拣,当前逻辑试管架状态：%s！", next.getPosition()));
    }


    /**
     * 获取一个分血的的申请单样本
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public ApplySampleDto getApplySample(String barcode, ProfessionalGroupDto group,
                                         List<ApplySampleDto> applySamples,
                                         List<ApplySampleItemDto> applySampleItems,
                                         InstrumentGroupDto instrumentGroup) {

        // 只获取当前专业组的
        applySamples = applySamples.stream().filter(e -> Objects.equals(e.getGroupId(),
                instrumentGroup.getGroupId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException(String.format("数据错误，没有找到专业组 [%s] 的样本信息",
                    instrumentGroup.getGroupName()));
        }


        final LinkedList<Long> ids = snowflakeService.genIds(1000);

        // 获取一个尚分拣的条码
        ApplySampleDto applySample = applySamples.stream().filter(e -> Objects.equals(e.getIsTwoPick(),
                YesOrNoEnum.NO.getCode())).findFirst().orElse(null);


        // 如果为空 那么新增一个，此时新增的也可以理解为分血，但有别与 专业组分血，这个分血是组内分血。
        if (Objects.isNull(applySample)) {

            // 如果不是生化组 抛错，因为只有生化组是按仪器分，所以可以分裂多个样本。
            if (!Objects.equals(group.getGroupCode(), shGroupCode)) {
                throw new IllegalArgumentException(String.format("当前专业组 [%s] 下没有可用条码，只有生化组是按照仪器分血，请确认项目。",
                        group.getGroupName()));
            }

            log.info("条码 [{}] 没有找到待分拣条码，即将开始组内分血", barcode);

            final long newApplySampleId = ids.pop();

            applySample = new ApplySampleDto();
            BeanUtils.copyProperties(applySamples.iterator().next(), applySample);
            applySample.setApplySampleId(newApplySampleId);
            applySample.setIsTwoPick(YesOrNoEnum.NO.getCode());
            applySample.setTwoPickerName(StringUtils.EMPTY);
            applySample.setTwoPickerId(NumberUtils.LONG_ZERO);
            applySample.setTwoPickDate(DefaultDateEnum.DEFAULT_DATE.getDate());

            // 获取到要做的报告项目
            final Map<String, ReportItemDto> reportItems = reportItemService.selectByTestItemIds(applySampleItems.stream()
                            .map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet()))
                    .stream().collect(Collectors.toMap(ReportItemDto::getReportItemCode, v -> v, (a, b) -> a));
            // 获取到这个专业小组可以做的报告项目
            final List<InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService
                    .selectByInstrumentGroupId(instrumentGroup.getInstrumentGroupId());

            // 组内分血的样本要做哪检验项目？答：如果这个专业小组下有这个检验项目下的报告项目，那这个样本就要做这个检验项目
            final Set<Long> testItemIds = new HashSet<>();
            for (InstrumentReportItemDto e : instrumentReportItems) {
                if (reportItems.containsKey(e.getReportItemCode())) {
                    testItemIds.add(reportItems.get(e.getReportItemCode()).getTestItemId());
                }
            }

            if (CollectionUtils.isEmpty(testItemIds)) {
                log.info("条码 [{}] 根据仪器下的报告项目和检验项目下的报告项目没有匹配到检验项目，将使用所有检验项目",
                        barcode);
                testItemIds.addAll(applySampleItems.stream().filter(e -> Objects.equals(e.getGroupId(), instrumentGroup.getGroupId()))
                        .map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet()));
            }

            final Collection<ApplySampleItemDto> items = applySampleItems.stream().filter(e -> testItemIds.contains(e.getTestItemId()))
                    .map(e -> {
                        final ApplySampleItemDto target = new ApplySampleItemDto();
                        // 因为是组内分血，不计算数量
                        BeanUtils.copyProperties(e, target);
                        target.setCount(NumberUtils.INTEGER_ZERO);
                        target.setApplySampleId(newApplySampleId);
                        target.setApplySampleItemId(ids.pop());
                        return target;
                    })
                    .collect(Collectors.toMap(ApplySampleItemDto::getTestItemId, v -> v, (a, b) -> a)).values();


            // 使用仪器传入的条码
            applySample.setBarcode(barcode);

            applySampleItemService.addApplySampleItems(new ArrayList<>(items));
            applySampleService.addApplySample(applySample);


            // 复制二次分拣之前的条码环节
            final List<SampleFlowDto> flows = new ArrayList<>();
            for (SampleFlowDto e : sampleFlowService.selectByApplySampleId(applySamples.iterator().next().getApplySampleId())) {
                // 只复制二次分拣之前的环节
                if (Objects.equals(e.getOperateCode(), BarcodeFlowEnum.TWO_PICK.name())) {
                    break;
                }
                final SampleFlowDto sf = new SampleFlowDto();
                BeanUtils.copyProperties(e, sf);
                sf.setSampleFlowId(ids.pop());
                sf.setApplySampleId(newApplySampleId);
                flows.add(sf);

            }
            sampleFlowService.addSampleFlows(flows);


            log.info("条码 [{}] 组内分血成功。样本信息 [{}] 项目信息 {}", barcode,
                    JSON.toJSONString(applySample), JSON.toJSONString(items));

        } else {


            log.info("条码 [{}] 找到待分拣条码，即将修改条码号为 [{}] 原来条码号为 [{}]",
                    barcode, barcode, applySample.getBarcode());

            applySample.setBarcode(barcode);

            final ApplySampleDto as = new ApplySampleDto();
            as.setApplySampleId(applySample.getApplySampleId());
            as.setBarcode(barcode);
            applySampleService.updateByApplySampleId(as);


        }

        return applySample;
    }


    /**
     * 专业组分血，如果不需要分血 那么此方法什么都不会做。如果分血那么会把分血结果放入到参数里
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void groupSplitBlood(List<ApplySampleDto> applySamples,
                                List<ApplySampleItemDto> applySampleItems,
                                InstrumentGroupDto instrumentGroup) {

        // 如果分血过 那么跳过
        if (applySamples.size() != 1 || applySamples.stream().anyMatch(e ->
                Objects.equals(e.getIsSplitBlood(), YesOrNoEnum.YES.getCode()))) {
            log.info("条码 [{}] 的已经分过血了",
                    applySamples.iterator().next().getOriginalBarcode());
            return;
        }

        final ApplySampleDto applySample = applySamples.iterator().next();

        // 只有一个专业组 那么不需要专业组分血
        if (applySampleItems.stream().map(ApplySampleItemDto::getGroupId)
                .distinct().count() == 1) {

            // 直接修改样本专业组
            final ApplySampleDto as = new ApplySampleDto();
            as.setApplySampleId(applySample.getApplySampleId());
            as.setGroupId(instrumentGroup.getGroupId());
            as.setGroupName(instrumentGroup.getGroupName());

            applySample.setGroupId(instrumentGroup.getGroupId());
            applySample.setGroupName(instrumentGroup.getGroupName());

            applySampleService.updateByApplySampleId(as);

            log.info("条码 [{}] 的检验项目只有一个专业组，无需分血。修改专业组成功",
                    applySamples.iterator().next().getOriginalBarcode());

            return;
        }


        final Map<Long, List<ApplySampleItemDto>> groupApplySampleItems = applySampleItems.stream()
                .collect(Collectors.groupingBy(ApplySampleItemDto::getGroupId));

        final LinkedList<Long> ids = snowflakeService.genIds(1000);

        final List<ApplySampleDto> newApplySamples = new ArrayList<>();
        final List<ApplySampleItemDto> newApplySampleItems = new ArrayList<>();

        for (var e : groupApplySampleItems.entrySet()) {
            final ApplySampleDto as = new ApplySampleDto();
            BeanUtils.copyProperties(applySample, as);

            as.setApplySampleId(ids.pop());
            // 这时候条码还是不可用状态，因为只有分拣的时候才有用
            as.setBarcode(as.getBarcode());
            as.setGroupId(e.getKey());
            as.setIsSplitBlood(YesOrNoEnum.YES.getCode());
            as.setSplitDate(new Date());
            as.setSplitterName("Roche流水线");
            as.setGroupName(e.getValue().iterator().next().getGroupName());
            newApplySamples.add(as);
        }

        // 分血完毕之后再分样本
        for (ApplySampleDto e : newApplySamples) {
            final List<ApplySampleItemDto> items = groupApplySampleItems.get(e.getGroupId());

            for (ApplySampleItemDto k : items) {
                final ApplySampleItemDto j = new ApplySampleItemDto();
                BeanUtils.copyProperties(k, j);
                j.setApplySampleItemId(ids.pop());
                j.setApplySampleId(e.getApplySampleId());
                j.setGroupId(e.getGroupId());
                j.setGroupName(e.getGroupName());
                newApplySampleItems.add(j);
            }

        }


        applySampleItemService.addApplySampleItems(newApplySampleItems);
        applySampleService.addApplySamples(newApplySamples);


        log.info("分血成功 样本 {}\t 项目 {}",
                JSON.toJSONString(newApplySamples), JSON.toJSONString(newApplySampleItems));

        // 复制条码环节
        sampleFlowService.copySampleFlows(applySample.getApplySampleId(), newApplySamples.stream()
                .map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));

        log.info("条码 [{}] 复制条码环节到 [{}] 成功", applySample.getOriginalBarcode(),
                newApplySamples.stream()
                        .map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));

        // 删除原来的样本、项目
        applySampleItemService.deleteByApplySampleIds(List.of(applySample.getApplySampleId()));
        applySampleService.deleteByApplySampleId(applySample.getApplySampleId());


        applySamples.clear();
        applySamples.addAll(newApplySamples);

        applySampleItems.clear();
        applySampleItems.addAll(newApplySampleItems);

    }

    private void onePick(ApplySampleDto applySample, InstrumentGroupDto instrumentGroup) throws Exception {
        // 修改样本信息
        final ApplySampleDto as = new ApplySampleDto();
        as.setItemType(ItemTypeEnum.ROUTINE.name());
        as.setApplySampleId(applySample.getApplySampleId());
        as.setIsOnePick(YesOrNoEnum.YES.getCode());
        as.setOnePickDate(new Date());
        as.setOnePickerId(NumberUtils.LONG_ZERO);
        as.setOnePickerName("Roche流水线");
        applySampleService.updateByApplySampleId(as);


        // 删除试管架占用
        rackLogicSpaceService.deleteByApplySampleId(applySample.getApplySampleId());

        // 创建逻辑试管架和占用
        final CancelTwoPickContext ctx = new CancelTwoPickContext();
        final RackDto rack = new RackDto();
        rack.setRackCode("RocheRack");
        rack.setRow(10);
        rack.setColumn(10);

        applySample.setGroupId(instrumentGroup.getGroupId());
        applySample.setGroupName(instrumentGroup.getGroupName());

        ctx.put(CancelTwoPickContext.RACK, rack);
        ctx.put(CancelTwoPickContext.APPLY_SAMPLES, List.of(applySample));
        cancelTwoPickDetachedRackCommand.execute(ctx);
    }

    private void twoPick(ApplySampleDto applySample, ProfessionalGroupDto group, InstrumentGroupDto instrumentGroup, InstrumentDto instrument, MT8000HandleVo vo) throws Exception {

        LoginUserHandler.get().setGroupId(instrumentGroup.getGroupId());
        LoginUserHandler.get().setGroupName(instrumentGroup.getGroupName());

        try {

            final RocheTwoPickDto rtp = new RocheTwoPickDto();
            rtp.setInstrumentGroupId(instrumentGroup.getInstrumentGroupId());
            rtp.setInstrumentId(instrument.getInstrumentId());
            rtp.setApplySampleId(applySample.getApplySampleId());

            log.info("迈瑞流水线 条码 [{}] 专业组 [{}] 专业小组 [{}] 开始二次分拣", applySample.getBarcode(), group.getGroupName(), instrumentGroup.getInstrumentGroupName());

            // 二次分拣
            applySampleService.twoPick(rtp);
        } catch (Exception e) {
            log.error("迈瑞 流水线二次分拣条码 [{}] 失败 原因: [{}]", applySample.getBarcode(),
                    e.getMessage(), e);
        }

    }


    @Override
    public MT8000HandleVo.Action action() {
        return MT8000HandleVo.Action.PICK;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("生化专业组编码 [{}]", shGroupCode);
    }
}
