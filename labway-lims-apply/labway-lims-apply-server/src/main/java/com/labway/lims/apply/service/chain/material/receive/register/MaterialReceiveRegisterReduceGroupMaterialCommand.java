
package com.labway.lims.apply.service.chain.material.receive.register;

import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRegisterItemDto;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 领用登记 修改 专业组 物料总库存
 *
 * <AUTHOR>
 * @since 2023/5/9 10:06
 */
@Slf4j
@Component
public class MaterialReceiveRegisterReduceGroupMaterialCommand implements Command {

    @DubboReference
    private GroupMaterialService groupMaterialService;

    @Override
    public boolean execute(Context context) throws Exception {

        final MaterialReceiveRegisterContext from = MaterialReceiveRegisterContext.from(context);
        var registerItemList = from.getRegisterItemList();
        var materialInventoryDtos = from.getMaterialInventoryDtos();
        var groupMaterialDtos = from.getGroupMaterialDtos();

        // key:物料库存id value：库存信息
        final Map<Long, MaterialInventoryDto> materialInventoryByInventoryId = materialInventoryDtos.stream()
            .collect(Collectors.toMap(MaterialInventoryDto::getInventoryId, Function.identity()));

        // 领用 的物料id 对应 领用 相关数量
        Map<Long, BigDecimal> materialIdAndReceiveMainNumber = new HashMap<>();
        Map<Long, BigDecimal> materialIdAndReceiveAssistNumber = new HashMap<>();

        for (MaterialReceiveRegisterItemDto registerItemDto : registerItemList) {
            Long inventoryId = registerItemDto.getInventoryId();
            // 对应库存物料
            MaterialInventoryDto inventoryDto = materialInventoryByInventoryId.get(inventoryId);

            // 物料 对应领用主数量 刚开始 为0
            BigDecimal receiveMainNumber = materialIdAndReceiveMainNumber.getOrDefault(inventoryDto.getMaterialId(), BigDecimal.ZERO).add(
					registerItemDto.getReceiveMainNumber());
            materialIdAndReceiveMainNumber.put(inventoryDto.getMaterialId(), receiveMainNumber);

            // 辅数量 同 主数量逻辑
            BigDecimal receiveAssistNumber =
                materialIdAndReceiveAssistNumber.getOrDefault(inventoryDto.getMaterialId(), BigDecimal.ZERO)
                    .add(registerItemDto.getReceiveAssistNumber());
            materialIdAndReceiveAssistNumber.put(inventoryDto.getMaterialId(), receiveAssistNumber);
        }

        for (GroupMaterialDto groupMaterialDto : groupMaterialDtos) {

            // 主、辅 库存减去相应领用数量
	        BigDecimal mainUnitInventory = groupMaterialDto.getMainUnitInventory().subtract(materialIdAndReceiveMainNumber.get(groupMaterialDto.getMaterialId()));

            BigDecimal assistUnitInventory = groupMaterialDto.getAssistUnitInventory()
                .subtract(materialIdAndReceiveAssistNumber.get(groupMaterialDto.getMaterialId()));

            GroupMaterialDto update = new GroupMaterialDto();
            update.setGroupMaterialId(groupMaterialDto.getGroupMaterialId());
            update.setMainUnitInventory(mainUnitInventory);
            update.setAssistUnitInventory(assistUnitInventory);

            groupMaterialService.updateByGroupMaterialId(update);
        }

        return CONTINUE_PROCESSING;
    }
}
