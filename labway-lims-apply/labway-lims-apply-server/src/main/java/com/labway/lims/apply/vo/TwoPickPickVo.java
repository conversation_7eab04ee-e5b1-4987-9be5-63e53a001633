package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.LimbSampleDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 二次分拣
 */
@Getter
@Setter
public class TwoPickPickVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 加急样本号
     */
    private String urgentSampleNo;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 二次分拣日期
     */
    private Date twoPickDate;

    /**
     * 血培养 对应样本号
     */
    private List<LimbSampleDto> limbSampleNos;
    
    /**
     * 项目类型
     * <p>
     * 不传为默认 排除 微生物、院感
     * <p>
     * INFECTION 院感
     * <p>
     * MICROBIOLOGY
     *
     * @see ItemTypeEnum
     *
     */
    private String itemType;

    /**
     * 仪器id
     */
    private Long instrumentId;

    /***
     * 是否已经校验了项目性状
     * 0：未校验
     * 1：已校验
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private int isValidProperty;

    /**
     * 项目性状关系映射
     */
    private List<TwoPickDto.MicrobiologyItemPropertyRelationDto> itemPropertyRelationList;

    // ------------------------------ 免疫二次分拣 ------------------------------
    /**
     * 免疫二次分拣 类型
     * 正常，强制，项目强制
     */
    private String immunityPickType;

    /**
     * 指定 预定日期
     */
    private List<Date> presetDates;

    /**
     * 指定 检验项目
     */
    private List<String> testItemCodes;
    // ------------------------------ 免疫二次分拣 ------------------------------


}
