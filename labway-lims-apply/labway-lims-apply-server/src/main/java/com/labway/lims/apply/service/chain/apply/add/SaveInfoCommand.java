package com.labway.lims.apply.service.chain.apply.add;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleImageDto;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleImageService;
import com.labway.lims.apply.api.service.ApplySampleItemBloodCultureService;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Getter
@Setter
@Slf4j
@Component
public class SaveInfoCommand implements Command {

    @Resource
    private ApplyService applyService;

    @Resource
    private ApplySampleService applySampleService;

    @Resource
    private ApplySampleItemService applySampleItemService;


    @Resource
    private ApplySampleItemBloodCultureService applySampleItemBloodCultureService;

    @Resource
    private SaveInfoCommand saveInfoCommand;

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Resource
    private ApplySampleImageService applySampleImageService;


    @Override
    public boolean execute(Context c) throws Exception {
        final AddApplyContext from = AddApplyContext.from(c);

        final ApplyDto applyDto = from.getApply();

        final List<ApplySampleDto> applySamples = from.getApplySamples();

        final List<ApplySampleItemDto> applySampleItems = from.getApplySampleItems();

        final List<ApplySampleImageDto> applySampleImages = from.getApplySampleImages();


        LoginUserHandler.User user = LoginUserHandler.get();
        threadPoolConfig.getPool().submit(() -> {
            LoginUserHandler.set(user);
            try {
                // 异步保存信息
                saveInfoCommand.saveApplySample(applySampleImages,applySamples, applySampleItems, applyDto, from);
                log.info("异步创建申请单成功");
            } catch (Exception e) {
                log.error("申请单保存失败：", e);
                throw e;
            } finally {
                LoginUserHandler.remove();
            }
        }).get(5, TimeUnit.SECONDS);

        return CONTINUE_PROCESSING;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveApplySample(List<ApplySampleImageDto> applySampleImages, List<ApplySampleDto> applySamples, List<ApplySampleItemDto> applySampleItems, ApplyDto applyDto, AddApplyContext from) {

        // 申请单样本图片
        applySampleImageService.insertBatch(applySampleImages);

        // 申请单样本
        applySampleService.addApplySamples(applySamples);

        // 样本项目
        applySampleItemService.addApplySampleItems(applySampleItems);

        // 申请单
        applyService.add(applyDto);

        // 血培养信息
        final ApplySampleItemBloodCultureDto bloodCulture = from.getBloodCulture();
        if (Objects.nonNull(bloodCulture)) {
            applySampleItemBloodCultureService.addApplySampleItemBloodCultures(Collections.singletonList(bloodCulture));
        }
    }
}
