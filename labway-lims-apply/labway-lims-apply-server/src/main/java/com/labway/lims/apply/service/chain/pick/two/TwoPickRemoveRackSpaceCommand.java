package com.labway.lims.apply.service.chain.pick.two;

import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

/**
 * 删除逻辑试管架占用
 */
@Slf4j
@Component
public class TwoPickRemoveRackSpaceCommand implements Command {

    @Resource
    private RackLogicSpaceService rackLogicSpaceService;

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);

        final Set<Long> ids = new HashSet<>();
        ids.add(context.getApplySample().getApplySampleId());

        for (ApplySampleTwoPickDto e : context.getApplySampleTwoPicks()) {
            ids.add(e.getApplySampleId());
        }

        // 删除分拣样本的id 和 所有拆分出来的样本（加急，血培养，微生物）
        rackLogicSpaceService.deleteByApplySampleIds(ids);

        return CONTINUE_PROCESSING;
    }


}
