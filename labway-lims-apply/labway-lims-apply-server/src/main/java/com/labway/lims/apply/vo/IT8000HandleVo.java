package com.labway.lims.apply.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class IT8000HandleVo {
    /**
     * action
     *
     * @see Action
     */
    private String action;

    /**
     * 额外参数
     */
    private JSONObject extras = new JSONObject(0);


    public enum Action {

        /**
         * 获取已经完成复核的样本。注意只会返回当天的。（当天0点到-24点）
         */
        AUDITED_SAMPLES,

        /**
         * 根据 applySampleId 获取样本信息。尽可能返回更多信息
         */
        APPLY_SAMPLE_INFO,

        /**
         * 获取仪器下所有的报告项目
         */
        MACHINE_REPORT_ITEMS,

        /**
         * 分拣。it8000 没有一次分拣和二次分拣之说，分拣是直接分拣到某个专业小组上了
         */
        PICK,

        /**
         * it8000扫描到了条码
         */
        SEEN,

        /**
         * it8000 拍照
         */
        SEEN_IMAGE,

        /**
         * 仪器扫描到条码
         */
        MACHINE_SEEN,

        /**
         * 归档
         */
        ARCHIVE,
        ;
    }
}
