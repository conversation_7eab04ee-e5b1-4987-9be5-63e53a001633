package com.labway.lims.apply.controller.pda;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.compare.QueryPDASampleInfoDto;
import com.labway.business.center.compare.request.compare.QueryPDASampleInfoRequest;
import com.labway.business.center.compare.service.TbOrgApplySampleMainAdditionalService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PdaDoubleCheckFiledEnum;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyMasterBarcodeDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.BaseDataQueryDto;
import com.labway.lims.apply.api.dto.HisCancelSignParam;
import com.labway.lims.apply.api.dto.PdaApplyAndItemDto;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.PdaApplySampleItemDto;
import com.labway.lims.apply.api.dto.PdaEntryTestApplyDto;
import com.labway.lims.apply.api.dto.PdaTobeConfirmedApplyDto;
import com.labway.lims.apply.api.dto.PdaTobeConfirmedApplyDto.PdaConfirmEnum;
import com.labway.lims.apply.api.dto.SignPdaApplyDto;
import com.labway.lims.apply.api.dto.UpdateTestApplyDto;
import com.labway.lims.apply.api.service.pda.PdaApplySampleItemService;
import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.api.service.pda.PdaTobeConfirmedApplyService;
import com.labway.lims.apply.api.vo.ApplyInfoVo;
import com.labway.lims.apply.api.vo.CancelSignVo;
import com.labway.lims.apply.vo.PDASampleInfoVo;
import com.labway.lims.apply.vo.PdaEntryTestApplyVo;
import com.labway.lims.apply.vo.UpdateTestApplyVo;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.SaveHspOrganizationFiledDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.SupplementalRecordFieldSettingService;
import com.swak.frame.dto.Response;
import joptsimple.internal.Strings;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.apply.controller.ApplyController.checkApplyParam;

@RestController
@RequestMapping("/pda/apply")
@RefreshScope
public class PdaApplyController extends BaseController {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private PdaApplyService pdaApplyService;

    @Resource
    private PdaApplySampleItemService pdaApplySampleItemService;
    @Resource
    private PdaTobeConfirmedApplyService pdaTobeConfirmedApplyService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Resource
    private TbOrgApplySampleMainAdditionalService tbOrgApplySampleMainAdditionalService;

    @DubboReference
    private SupplementalRecordFieldSettingService supplementalRecordFieldSettingService;

    @DubboReference
    private DictService dictService;
    @Value("${business-center.org-code:00010110000000001WND}")
    private String orgCode;

    @PostMapping("/add")
    public Object addApply(@RequestBody PdaEntryTestApplyVo vo) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        if (StringUtils.isBlank(vo.getMasterBarcode())) {
            throw new IllegalArgumentException("主条码不能为空");
        }

        final String key = pdaTobeConfirmedApplyService.getAbolishLock(user.getOrgId(), vo.getMasterBarcode());

        try {
            // 检查申请单参数
            checkApplyParam(vo);

            final PdaEntryTestApplyDto addApply = JSON.parseObject(JSON.toJSONString(vo), PdaEntryTestApplyDto.class);

            addApply.setPatientCardType(PatientCardTypeEnum.ID_CARD.name());
            addApply.setApplySource(ApplySourceEnum.PDA_SIGN);
            addApply.setSupplier("PDA信息录入");

            // 添加申请单
            return pdaApplyService.addApply(addApply);

        } finally {
            stringRedisTemplate.delete(key);
        }
    }


    /**
     * 修改申请单信息
     */
    @PostMapping("/update")
    public Object update(@RequestBody UpdateTestApplyVo testApplyVo) {
        final Long applyId = testApplyVo.getApplyId();
        if (Objects.isNull(applyId)) {
            throw new IllegalArgumentException("申请单id不能为空");
        }

        if (StringUtils.isBlank(testApplyVo.getMasterBarcode())) {
            throw new IllegalArgumentException("主条码不能为空");
        }

        // 检查申请单参数
        checkApplyParam(testApplyVo);

        final LoginUserHandler.User user = LoginUserHandler.get();

        final String key = pdaTobeConfirmedApplyService.getAbolishLock(user.getOrgId(), testApplyVo.getMasterBarcode());

        try {
            final UpdateTestApplyDto updateTestApply = JSON.parseObject(JSON.toJSONString(testApplyVo), UpdateTestApplyDto.class);

            return pdaApplyService.update(updateTestApply);
        } finally {
            stringRedisTemplate.delete(key);
        }
    }

    /**
     * 待确认页面批量修改申请单信息
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/tobe-confirm-update")
    public Object tobeConfirmUpdate(@RequestBody List<UpdateTestApplyVo> testApplyVos) {

        if (CollectionUtils.isEmpty(testApplyVos) || testApplyVos.size() != NumberUtils.INTEGER_TWO) {
            throw new IllegalArgumentException("参数错误");
        }
        return testApplyVos.stream().map(this::update).collect(Collectors.toList());
    }

    /**
     * 查询业务中台主条码和图片信息
     */
    @GetMapping("/list/hsp-org")
    public Object hspOrgApply(@RequestParam(required = false) String hspOrgCode) {

        final QueryPDASampleInfoRequest request = new QueryPDASampleInfoRequest();
        request.setOrgCode(orgCode);
        request.setSize(Integer.MAX_VALUE);
        final List<String> hspOrgCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(hspOrgCode)) {
            hspOrgCodeList.add(hspOrgCode);
        }
        request.setHspOrgCodes(hspOrgCodeList);
        final Response<List<QueryPDASampleInfoDto>> listResponse = tbOrgApplySampleMainAdditionalService.queryPDASampleInfo(request);
        if (!listResponse.isSuccess()) {
            throw new IllegalStateException(listResponse.getMsg());
        }
        final List<QueryPDASampleInfoDto> data = listResponse.getData();

        final List<String> masterBarcodes = data.stream().map(QueryPDASampleInfoDto::getBarcode).collect(Collectors.toList());
        // 查询送检机构， 匹配对应id， 前端下拉框要用 id 匹配，
        final List<String> hspOrgCodes = data.stream().map(QueryPDASampleInfoDto::getHspOrgCode).collect(Collectors.toList());
        final Map<String, Long> hspOrgCodeMap = hspOrganizationService.selectByHspOrgCodes(hspOrgCodes)
                .stream().collect(Collectors.toMap(HspOrganizationDto::getHspOrgCode, HspOrganizationDto::getHspOrgId, (a, b) -> b));

        // 查询送检类型， 对应的code ， 前端下拉框要匹配
        final Map<String, String> applyTypeMap = dictService.selectByDictType(DictEnum.VISIT_TYPE.name())
                .stream().collect(Collectors.toMap(DictItemDto::getDictName, DictItemDto::getDictCode));

        // 已录入的PDA申请单
        final Map<String, List<PdaApplyDto>> pdaApplyMapByMasterBarcode = pdaApplyService.selectByMasterBarcodesAsMap(masterBarcodes);
        // 过滤当前登录用户录入的数据
        final Set<String> addedMasterBarcode = pdaApplyMapByMasterBarcode.entrySet().stream()
                .map(entry -> {
                    if (entry.getValue().stream().anyMatch(e -> Objects.equals(e.getCreatorId(), LoginUserHandler.get().getUserId()))) {
                        return entry.getKey();
                    }
                    return null;
                }).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 待确认表记录
        final List<PdaTobeConfirmedApplyDto> pdaTobeConfirmedApplyDtos = pdaTobeConfirmedApplyService.selectByMasterBarcodes(masterBarcodes);
        if (!CollectionUtils.isEmpty(pdaTobeConfirmedApplyDtos)) {
            addedMasterBarcode.addAll(pdaTobeConfirmedApplyDtos.stream().map(PdaTobeConfirmedApplyDto::getMasterBarcode).collect(Collectors.toSet()));
        }

        return data.stream().filter(e -> !addedMasterBarcode.contains(e.getBarcode()))
                .map(e -> {
                    final PDASampleInfoVo pdaSampleInfoVo = JSON.parseObject(JSON.toJSONString(e), PDASampleInfoVo.class);
                    final Long hspOrgId = hspOrgCodeMap.getOrDefault(pdaSampleInfoVo.getHspOrgCode(), NumberUtils.LONG_ZERO);
                    final String applyTypeCode = applyTypeMap.getOrDefault(pdaSampleInfoVo.getApplyType(), Strings.EMPTY);
                    pdaSampleInfoVo.setHspOrgId(hspOrgId);
                    pdaSampleInfoVo.setApplyTypeCode(applyTypeCode);
                    return pdaSampleInfoVo;
                }).collect(Collectors.toList());

    }

    /**
     * 根据主条码查询自己的填写内容
     */
    @GetMapping("/select")
    public Object select(@RequestParam String masterBarcode) {
        if (StringUtils.isBlank(masterBarcode)) {
            throw new IllegalArgumentException("主条码不能为空");
        } final LoginUserHandler.User user = LoginUserHandler.get();

        // 查询pda申请单 根据主条码获取一个
        final PdaApplyDto pdaApplyDto = pdaApplyService.selectByMasterBarcode(masterBarcode).stream().filter(e -> Objects.equals(e.getCreatorId(), user.getUserId())).findFirst().orElse(null);
        // 没有查询到直接返回
        if (Objects.isNull(pdaApplyDto)) {
            return Map.of();
        }

        // 查询申请单下的项目
        final List<PdaApplySampleItemDto> pdaApplySampleItemDtos = pdaApplySampleItemService.selectByPdaApplyId(pdaApplyDto.getPdaApplyId());

        final PdaApplyAndItemDto pdaApplyAndItemDto = BeanUtil.toBean(pdaApplyDto, PdaApplyAndItemDto.class);
        pdaApplyAndItemDto.setPdaApplySampleItemDtoList(pdaApplySampleItemDtos);

        return pdaApplyAndItemDto;
    }

    /**
     * 根据主条码查询录入人信息
     */
    @GetMapping("/get/by-master-barcode")
    public Object getByMasterBarcode(@RequestParam String masterBarcode) {
        if (StringUtils.isBlank(masterBarcode)) {
            throw new IllegalArgumentException("主条码不能为空");
        }
        pdaApplyService.masterBarcodeIsAbolish(masterBarcode);

        return pdaApplyService.selectByMasterBarcode(masterBarcode);
    }

    /**
     * 根据主条码查询申请单和项目
     */
    @GetMapping("/get-tobe-confirmed")
    public Object getTobeConfirmed(@RequestParam String masterBarcode) {
        if (StringUtils.isBlank(masterBarcode)) {
            throw new IllegalArgumentException("主条码不能为空");
        }

        // 查询pda申请单 根据主条码获取一个
        final List<PdaApplyDto> pdaApplyDtos = pdaApplyService.selectByMasterBarcode(masterBarcode);
        // 没有查询到直接返回
        if (CollectionUtils.isEmpty(pdaApplyDtos)) {
            return Map.of();
        }

        PdaApplyAndItemDto mockApplyAndItemDto = null;
        PdaTobeConfirmedApplyDto pdaTobeConfirmedApplyDto = pdaTobeConfirmedApplyService.selectByMasterBarcode(masterBarcode);
        if (Objects.isNull(pdaTobeConfirmedApplyDto)) {
            pdaTobeConfirmedApplyDto = JSON.parseObject(JSON.toJSONString(pdaApplyDtos.get(0)), PdaTobeConfirmedApplyDto.class);
            mockApplyAndItemDto = new PdaApplyAndItemDto() {{
                setPdaApplySampleItemDtoList(List.of());
            }};
            // throw new IllegalStateException("该主条码未完成双输补录");
        }
        if (Objects.nonNull(pdaTobeConfirmedApplyDto) &&
                Objects.equals(pdaTobeConfirmedApplyDto.getStatus(), PdaConfirmEnum.SIGN.getCode())) {
            throw new IllegalArgumentException("该主条码已完成签收");
        }

        // 查询申请单下的项目
        final Map<Long, List<PdaApplySampleItemDto>> pdaApplySampleItemDtoMap = pdaApplySampleItemService.selectByPdaApplyIds(pdaApplyDtos.stream().map(PdaApplyDto::getPdaApplyId).collect(Collectors.toList()));

        final List<PdaApplyAndItemDto> pdaApplyAndItemDtos = pdaApplyDtos.stream().map(e -> {
            final PdaApplyAndItemDto pdaApplyAndItemDto = BeanUtil.toBean(e, PdaApplyAndItemDto.class);
            final LinkedList<PdaApplySampleItemDto> pdaApplySampleItemDtos = new LinkedList<>(pdaApplySampleItemDtoMap.getOrDefault(pdaApplyAndItemDto.getPdaApplyId(), List.of()));
            pdaApplyAndItemDto.setPdaApplySampleItemDtoList(pdaApplySampleItemDtos);
            return pdaApplyAndItemDto;
        }).collect(Collectors.toList());

        // 只单录过的，返回一个空对象给前端
        if (Objects.nonNull(mockApplyAndItemDto)) {
            pdaApplyAndItemDtos.add(mockApplyAndItemDto);
        }

        // 排序里面的项目， 相同的放在前面， 其他的随便放
        this.sortPdaApplyItem(pdaApplyAndItemDtos);

        // 字段对比设置
        SaveHspOrganizationFiledDto filedDto = supplementalRecordFieldSettingService.selectByHspOrgIdOrDefaultOrg(pdaTobeConfirmedApplyDto.getHspOrgId());

        if (Objects.isNull(filedDto)) {
            filedDto = SaveHspOrganizationFiledDto.defaultFieldSetting();
        } else {
            // 补充对应的顺序字段
            final Map<String, PdaDoubleCheckFiledEnum> checkFiledEnumMap = Arrays.stream(PdaDoubleCheckFiledEnum.values())
                    .collect(Collectors.toMap(PdaDoubleCheckFiledEnum::getCode, Function.identity(), (a, b) -> a));
            final List<SaveHspOrganizationFiledDto.Field> collect = filedDto.getFileds().stream().peek(e -> {
                final PdaDoubleCheckFiledEnum pdaDoubleCheckFiledEnum = checkFiledEnumMap.get(e.getCode());
                if (Objects.nonNull(pdaDoubleCheckFiledEnum)) {
                    e.setSort(pdaDoubleCheckFiledEnum.getSort());
                }
                if (Objects.isNull(e.getSort())) {
                    e.setSort(0);
                }
            }).sorted(Comparator.comparing(SaveHspOrganizationFiledDto.Field::getSort)).collect(Collectors.toList());
            filedDto.setFileds(collect);
        }
        return Map.of("masterBarcode", masterBarcode,
                "pdaImgs", StringUtils.defaultString(pdaTobeConfirmedApplyDto.getPdaImgs()),
                "pdaApplys", pdaApplyAndItemDtos,
                "filedSetting", filedDto);
    }

    /**
     * 排序里面的项目， 相同的放在前面， 其他的随便放
     * <p>
     * 1. 将两次输入的检验项目取交集
     * 2. 循环交集检验项目。并将两次输入中对应的检验项目移动（remove -> add）到第一位
     * </p>
     */
    private void sortPdaApplyItem(List<PdaApplyAndItemDto> pdaApplyAndItemDtos) {

        // 都是linkedList
        // 第一个人填写的项目
        final List<PdaApplySampleItemDto> onePdaItemList = pdaApplyAndItemDtos.get(0).getPdaApplySampleItemDtoList();
        // 第二个人填写的项目
        final List<PdaApplySampleItemDto> twoPdaItemList = pdaApplyAndItemDtos.get(1).getPdaApplySampleItemDtoList();

        // 取交集， 相同的检验项目id
        final Set<Long> oneTestIdSet = onePdaItemList.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());
        final Set<Long> twoTestIdSet = twoPdaItemList.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());
        Collection<Long> inCommonTestIdSet = org.apache.commons.collections.CollectionUtils.intersection(oneTestIdSet, twoTestIdSet);

        for (Long testItemId : inCommonTestIdSet) {
            // 将里面的元素取出来， 放到第一个位置
            final Optional<PdaApplySampleItemDto> oneFirst = onePdaItemList.stream().filter(e -> Objects.equals(e.getTestItemId(), testItemId)).findFirst();
            if (oneFirst.isPresent()) {
                final PdaApplySampleItemDto pdaApplySampleItemDto = oneFirst.get();
                onePdaItemList.remove(pdaApplySampleItemDto);
                onePdaItemList.add(0, pdaApplySampleItemDto);
            }

            final Optional<PdaApplySampleItemDto> twoFirst = twoPdaItemList.stream().filter(e -> Objects.equals(e.getTestItemId(), testItemId)).findFirst();
            if (twoFirst.isPresent()) {
                final PdaApplySampleItemDto pdaApplySampleItemDto = twoFirst.get();
                twoPdaItemList.remove(pdaApplySampleItemDto);
                twoPdaItemList.add(0, pdaApplySampleItemDto);
            }

        }
    }

    /**
     * 根据日期和送检机构查询
     */
    @PostMapping("/select/pda-apply")
    public Object selectPdaApply(@RequestBody BaseDataQueryDto dto) {
        Assert.notNull(dto.getStartDate(), "起始时间不能为空"); Assert.notNull(dto.getEndDate(), "结束时间不能为空");
        if (dto.getStartDate().after(dto.getEndDate())) {
            throw new IllegalArgumentException("起始时间不能晚于结束时间");
        }

        return pdaApplyService.selectByDate(dto, LoginUserHandler.get().getUserId());

    }

    /**
     * 作废主条码
     */
    @PostMapping("/abolish/master-barcode")
    public Object abolishMasterBarcode(@RequestBody ApplyMasterBarcodeDto dto) {
        if (StringUtils.isBlank(dto.getMasterBarcode())) {
            throw new IllegalArgumentException("主条码不能为空");
        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        final String key = pdaTobeConfirmedApplyService.getAbolishLock(user.getOrgId(), dto.getMasterBarcode());

        try {
            // 调用业务中台作废条码

            pdaApplyService.abolishMasterBarcode(dto.getMasterBarcode());

            return Map.of();

        } finally {
            stringRedisTemplate.delete(key);
        }

    }

    /**
     * 获取pda签收
     */
    @GetMapping("/get")
    public Object get(@RequestParam(value = "masterBarcode", required = false) String masterBarcode,
                      @RequestParam(value = "hspOrgId", required = false) Long hspOrgId) {
        Assert.notNull(hspOrgId, "送检机构不能为空");
        if (StringUtils.isBlank(masterBarcode)) {
            throw new IllegalArgumentException("主条码不能为空");
        }

        return pdaApplyService.get(masterBarcode, hspOrgId);
    }


    /**
     * 获取pda签收
     */
    @PostMapping("/sign")
    public Object sign(@RequestBody SignPdaApplyDto dto) {
        Assert.notNull(dto.getHspOrgId(), "送检机构不能为空");
        if (StringUtils.isBlank(dto.getMasterBarcode())) {
            throw new IllegalArgumentException("主条码不能为空");
        }
        final String masterBarcode = dto.getMasterBarcode();
        final LoginUserHandler.User user = LoginUserHandler.get();
        final String abolishLock = pdaTobeConfirmedApplyService.getAbolishLock(user.getOrgId(), masterBarcode);
        try {
            final PdaTobeConfirmedApplyDto pdaTobeConfirmedApplyDto = pdaTobeConfirmedApplyService.selectByMasterBarcode(masterBarcode);
            if (Objects.isNull(pdaTobeConfirmedApplyDto)) {
                throw new IllegalArgumentException("PDA主条码确认单不存在");
            }
            if (Objects.equals(pdaTobeConfirmedApplyDto.getStatus(), PdaConfirmEnum.NO_CONFIRM.getCode())) {
                throw new IllegalArgumentException("PDA主条码未确认");
            }

            final ApplyInfoVo applyInfoVo = JSON.parseObject(JSON.toJSONString(pdaApplyService.sign(dto)), ApplyInfoVo.class);
            for (ApplyInfoVo.Sample sample : applyInfoVo.getSamples()) {
                sample.setHasPathologyTestItem(sample.getTestItems().stream()
                        .anyMatch(o -> Objects.equals(o.getItemType(), ItemTypeEnum.PATHOLOGY.name())));
            }
            return applyInfoVo;

        } finally {
            stringRedisTemplate.delete(abolishLock);
        }

    }

    /**
     * 获取pda签收
     */
    @PostMapping("/cancel-sign")
    public Object cancelSign(@RequestBody CancelSignVo vo) {
        final Set<Long> applyIds = vo.getApplyIds();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(applyIds)) {
            throw new IllegalArgumentException("请选择样本");
        }

        if (StringUtils.isAnyBlank(vo.getReasonName(), vo.getReasonCode())) {
            throw new IllegalArgumentException("请选择取消签收原因");
        }

        if (Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalArgumentException("请选择送检机构");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("送检机构不存在");
        }
        if (Objects.equals(hspOrganization.getEnable(), YesOrNoEnum.NO.getCode())) {
            throw new IllegalStateException(String.format("送检机构 [%s] 已禁用", hspOrganization.getHspOrgName()));
        }
        final HisCancelSignParam hisCancelSignParam = JSON.parseObject(JSON.toJSONString(vo), HisCancelSignParam.class);
        hisCancelSignParam.setHspOrgCode(hspOrganization.getHspOrgCode());
        pdaApplyService.cancelSign(hisCancelSignParam);

        return Collections.emptyMap();
    }

}
