
package com.labway.lims.apply.service.chain.material.delivery.income;

import com.google.common.collect.Lists;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryIncomeItemDto;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import com.labway.lims.apply.mapstruct.MaterialInventoryConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料 修改 物料 库存
 *
 * <AUTHOR>
 * @since 2023/5/9 10:47
 */
@Slf4j
@Component
public class MaterialIncomeAddMaterialInventoryCommand implements Command {

    @Resource
    private MaterialInventoryConverter materialInventoryConverter;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private MaterialInventoryService materialInventoryService;

    @Override
    public boolean execute(Context context) throws Exception {
        final MaterialIncomeContext from = MaterialIncomeContext.from(context);
        var user = from.getUser();
        var incomeItemList = from.getIncomeItemList();
        var materialDeliveryDetails = from.getMaterialDeliveryDetails();

        // key: 出库详情id value: 出库物料详情
        final Map<Long, MaterialDeliveryDetailDto> deliveryDetailDtoMap = materialDeliveryDetails.stream()
            .collect(Collectors.toMap(MaterialDeliveryDetailDto::getDetailId, Function.identity()));

        // 以 物料+批号 分下 入库 信息
        Map<String, List<MaterialDeliveryIncomeItemDto>> groupByMaterialIdAndBatchNo = new HashMap<>();
        for (MaterialDeliveryIncomeItemDto incomeItemDto : incomeItemList) {
            // 对应 出库物料详情
            MaterialDeliveryDetailDto deliveryDetailDto = deliveryDetailDtoMap.get(incomeItemDto.getDetailId());

            String key = deliveryDetailDto.getMaterialId() + "-" + deliveryDetailDto.getBatchNo();
            if (groupByMaterialIdAndBatchNo.containsKey(key)) {
                groupByMaterialIdAndBatchNo.get(key).add(incomeItemDto);
            } else {
                groupByMaterialIdAndBatchNo.put(key, Lists.newArrayList(incomeItemDto));
            }
        }

        // 查询 本次 入库 所有 物料ids 、物料批号
        Set<Long> incomeMaterialIds = incomeItemList.stream()
            .map(obj -> deliveryDetailDtoMap.get(obj.getDetailId()).getMaterialId()).collect(Collectors.toSet());
        Set<String> incomeBatchNos = incomeItemList.stream()
            .map(obj -> deliveryDetailDtoMap.get(obj.getDetailId()).getBatchNo()).collect(Collectors.toSet());

        // 对应 物料库存
        final List<MaterialInventoryDto> materialInventoryDtos = materialInventoryService
            .selectByGroupIdAndMaterialIds(user.getGroupId(), incomeMaterialIds, incomeBatchNos);
        // 对应 物料库存 转map key:物料+批号 value:库存信息
        Map<String, MaterialInventoryDto> inventoryDtoMapByMaterialIdAndBatchNo = materialInventoryDtos.stream()
            .collect(Collectors.toMap(obj -> obj.getMaterialId() + "-" + obj.getBatchNo(), Function.identity()));

        // 对应 物料+批号 库存不存在 需要新增的数量
        int count = groupByMaterialIdAndBatchNo.keySet().stream()
            .filter(x -> !inventoryDtoMapByMaterialIdAndBatchNo.containsKey(x)).collect(Collectors.toSet()).size();
        LinkedList<Long> genIds = new LinkedList<>();
        if (count > 0) {
            genIds = snowflakeService.genIds(count);
        }

        Date date = new Date();
        List<MaterialInventoryDto> addMaterialInventoryDtos = Lists.newArrayListWithCapacity(count);
        for (Map.Entry<String, List<MaterialDeliveryIncomeItemDto>> entry : groupByMaterialIdAndBatchNo.entrySet()) {

            // 物料+批号相同 的获取 第一个 出库详情以生成库存物料基本信息
            MaterialDeliveryDetailDto deliveryDetailDto =
                deliveryDetailDtoMap.get(entry.getValue().get(0).getDetailId());

            // 统计物料 主、辅 入库数量
            BigDecimal incomeMainNumber =
                entry.getValue().stream().map(MaterialDeliveryIncomeItemDto::getIncomeMainNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal incomeAssistNumber = entry.getValue().stream()
                .map(MaterialDeliveryIncomeItemDto::getIncomeAssistNumber).reduce(BigDecimal.ZERO, BigDecimal::add);

            MaterialInventoryDto inventoryDto = inventoryDtoMapByMaterialIdAndBatchNo.get(entry.getKey());
            if (Objects.nonNull(inventoryDto)) {
                // 需要更新 物料库存
                BigDecimal mainUnitInventory = inventoryDto.getMainUnitInventory().add(incomeMainNumber);
                BigDecimal assistUnitInventory = inventoryDto.getAssistUnitInventory().add(incomeAssistNumber);

                MaterialInventoryDto update = new MaterialInventoryDto();
                update.setInventoryId(inventoryDto.getInventoryId());
                update.setMainUnitInventory(mainUnitInventory);
                update.setAssistUnitInventory(assistUnitInventory);
				// 🐛fix: 物料入库不更新物料条码号 （物料库存导入没有条码号，后续从中台出库之后要把条码号更新上）
                update.setMaterialBarcode(deliveryDetailDto.getMaterialBarcode());

                materialInventoryService.updateByInventoryId(update);
                continue;
            }

            // 此物料-批号 对应库存不存在 需要创建 根据出库信息 获取 初步物料库存基本信息
            MaterialInventoryDto temp = materialInventoryConverter.fromMaterialDeliveryDetailDto(deliveryDetailDto);

            temp.setInventoryId(genIds.pop());
            temp.setMainUnitInventory(incomeMainNumber);
            temp.setAssistUnitInventory(incomeAssistNumber);

            temp.setGroupId(user.getGroupId());
            temp.setGroupName(user.getGroupName());
            temp.setOrgId(user.getOrgId());
            temp.setOrgName(user.getOrgName());
            temp.setCreateDate(date);
            temp.setUpdateDate(date);
            temp.setCreatorId(user.getUserId());
            temp.setCreatorName(user.getNickname());
            temp.setUpdaterId(user.getUserId());
            temp.setUpdaterName(user.getNickname());
            temp.setIsDelete(YesOrNoEnum.NO.getCode());

            addMaterialInventoryDtos.add(temp);

        }

        if (CollectionUtils.isNotEmpty(addMaterialInventoryDtos)) {
            materialInventoryService.addMaterialInventoryDtos(addMaterialInventoryDtos);
        }
        return CONTINUE_PROCESSING;
    }
}
