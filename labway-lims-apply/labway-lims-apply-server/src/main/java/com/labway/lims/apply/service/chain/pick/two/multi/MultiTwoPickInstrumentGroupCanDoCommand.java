package com.labway.lims.apply.service.chain.pick.two.multi;

import com.google.common.base.Joiner;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.service.chain.pick.two.TwoPickCheckInstrumentGroupCandoCommand;
import com.labway.lims.base.api.dto.ReportItemDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 校验专业小组是否可以做
 *
 * <AUTHOR> on 2025/7/15.
 */
@Slf4j
@Component
public class MultiTwoPickInstrumentGroupCanDoCommand implements Command {

	@Resource
	private TwoPickCheckInstrumentGroupCandoCommand twoPickCheckInstrumentGroupCandoCommand;

	@Override
	public boolean execute(Context c) throws Exception {
		final MultiTwoPickContext context = MultiTwoPickContext.from(c);

		Set<String> errorMessage = new HashSet<>();
		for (ApplySampleDto applySample : context.getApplySamples()) {
			Long applySampleId = applySample.getApplySampleId();
			List<ApplySampleItemDto> applySampleItemDtos = context.getApplySampleItems().get(applySampleId)
					.stream().filter(l -> Objects.equals(l.getGroupId(), LoginUserHandler.get().getGroupId())).collect(Collectors.toList());
			Set<Long> testItemIds = applySampleItemDtos.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());
			List<ReportItemDto> reportItemDtos = testItemIds.stream().flatMap(item -> context.getTestReportItemMap().get(item).stream())
					.collect(Collectors.toList());

			try {
				twoPickCheckInstrumentGroupCandoCommand.checkCanDo(context.getInstrumentGroups().get(applySampleId),
						applySampleItemDtos, reportItemDtos);
			} catch (Exception e) {
				errorMessage.add(e.getMessage());
			}
		}

		if (CollectionUtils.isNotEmpty(errorMessage)) {
			log.error("批量二次分拣专业小组项目异常：{}",Joiner.on(",").join(errorMessage));
			throw new IllegalStateException(Joiner.on("\n").join(errorMessage));
		}

		return CONTINUE_PROCESSING;
	}
}
