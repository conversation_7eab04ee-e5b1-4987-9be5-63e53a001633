package com.labway.lims.apply.service.chain.apply.update.sample;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import lombok.Getter;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Getter
@Component
public class UpdateSampleResultCommand implements Command {

        @Resource
        private SampleAbnormalService sampleAbnormalService;

        @DubboReference
        private SampleCriticalResultService sampleCriticalResultService;

        @Resource
        private SampleReportService sampleReportService;
        /**
         *  根据样本的 applyId 去更新对应的样本信息结果关联的表
         * @param c
         * @return
         * @throws Exception
         */
        @Override
        public boolean execute(Context c) throws Exception {
            UpdateApplyContext context = UpdateApplyContext.from(c);
            ApplyDto apply = context.getApply();
            LoginUserHandler.User user = LoginUserHandler.get();
            Long hspOrgId = apply.getHspOrgId() == null ? 0  : apply.getHspOrgId();
            String hspOrgName = StringUtils.isBlank(apply.getHspOrgName()) ? Strings.EMPTY : apply.getHspOrgName();
            // 更新时间、人id、人昵称
            Long userId = user.getUserId();
            String nickname = user.getNickname();
            Date date = new Date();

            // 设置对应的更新条件
            SampleAbnormalDto sampleAbnormalDto = new SampleAbnormalDto();
            sampleAbnormalDto.setApplyId(apply.getApplyId());
            sampleAbnormalDto.setHspOrgName(hspOrgName);
            sampleAbnormalDto.setHspOrgId(hspOrgId);
            sampleAbnormalDto.setUpdaterId(userId);
            sampleAbnormalDto.setUpdateDate(date);
            sampleAbnormalDto.setUpdaterName(nickname);
            sampleAbnormalService.updateByApplyId(sampleAbnormalDto);
            SampleCriticalResultDto sampleCriticalResultDto = new SampleCriticalResultDto();
            sampleCriticalResultDto.setApplyId(apply.getApplyId());
            sampleCriticalResultDto.setHspOrgId(hspOrgId.toString());
            sampleCriticalResultDto.setHspOrgName(hspOrgName);
            sampleCriticalResultDto.setUpdaterId(userId);
            sampleCriticalResultDto.setUpdateDate(date);
            sampleCriticalResultDto.setUpdaterName(nickname);
            sampleCriticalResultService.updateByApplyId(sampleCriticalResultDto);
            SampleReportDto sampleReportDto = new SampleReportDto();
            sampleReportDto.setApplyId(apply.getApplyId());
            sampleReportDto.setHspOrgId(hspOrgId);
            sampleReportDto.setHspOrgName(hspOrgName);
            sampleReportDto.setUpdateDate(date);
            sampleReportDto.setUpdaterId(userId);
            sampleReportDto.setUpdaterName(nickname);
            sampleReportService.updateByApplyId(sampleReportDto);
            return  CONTINUE_PROCESSING;
    }
}
