package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 异常 列表 请求 Vi
 * 
 * <AUTHOR>
 * @since 2023/4/12 11:21
 */
@Getter
@Setter
public class SampleAbnormalListRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 登记时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date registDateStart;

    /**
     * 登记时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date registDateEnd;

}
