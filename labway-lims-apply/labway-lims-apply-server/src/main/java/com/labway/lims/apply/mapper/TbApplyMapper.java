package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.SampleApplyDto;
import com.labway.lims.apply.api.dto.UnreviewedApplyQueryDto;
import com.labway.lims.apply.model.TbApply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 申请单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbApplyMapper extends BaseMapper<TbApply> {

    /**
     * 申请单录入 查询未复核的列表
     */
    List<SampleApplyDto> selectUnreviewedApplySampleByQuery(@Param("query") UnreviewedApplyQueryDto query);

    /**
     * 查询所有， 包含已删除的
     * @param applyIds
     * @return
     */
    List<TbApply> selectAllByApplyIds(@Param("applyIds")Collection<Long> applyIds);
}
