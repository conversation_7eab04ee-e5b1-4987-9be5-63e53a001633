package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.MaterialApplyStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 物料申领详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Getter
@Setter
public class GroupMaterialApplyDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 详细ID
     */
    private Long detailId;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物资编号
     */
    private String materialCode;

    /**
     * 物资名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 厂家
     */
    private String manufacturers;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 申领主单位数量
     */
    private BigDecimal applyMainNumber;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     * 申领辅单位数量
     */
    private BigDecimal applyAssistNumber;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 物料状态，默认物料申请单的状态， 99驳回
     * @see MaterialApplyStatusEnum
     */
    private Integer status;

}
