package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.HspOrgDateQueryDto;
import com.labway.lims.apply.api.dto.LogisticsApplyDto;
import com.labway.lims.apply.api.dto.SimpleLogisticsSampleDto;
import com.labway.lims.apply.model.TbApplyLogisticsSample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 物流申请单样本 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbApplyLogisticsSampleMapper extends BaseMapper<TbApplyLogisticsSample> {

    List<SimpleLogisticsSampleDto> selectApplyLogisticsDetail(@Param("dto") HspOrgDateQueryDto dto);

    /**
     * 查询已补录的申请单
     */
    List<LogisticsApplyDto> selectSupplementList(@Param("dto") HspOrgDateQueryDto dto);
}
