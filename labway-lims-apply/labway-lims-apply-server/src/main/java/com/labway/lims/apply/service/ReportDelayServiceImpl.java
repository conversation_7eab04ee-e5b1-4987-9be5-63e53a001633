package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.PrintStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ReportDelayDto;
import com.labway.lims.apply.api.service.ReportDelayService;
import com.labway.lims.apply.mapper.TbReportDelayMapper;
import com.labway.lims.apply.model.TbReportDelay;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description 报告单迟发表
 * @date 2023-12-15
 */

@Slf4j
@DubboService
public class ReportDelayServiceImpl implements ReportDelayService {

    @Resource
    private TbReportDelayMapper reportDelayMapper;
    @Resource
    private SnowflakeService snowflakeService;


    @Override
    public List<ReportDelayDto> selectByReportDelayInfo(ReportDelayDto reportDelayDto) {
        if (ObjectUtils.isEmpty(reportDelayDto)) {
            return Collections.emptyList();
        }
        // 状态
        Integer statusCode = PrintStatusEnum.getByCode(reportDelayDto.getStatus()).getCode();

        LambdaQueryWrapper<TbReportDelay> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByDesc(TbReportDelay::getCreateDate);

        // 不等于-1 才去查，默认查所有
        if (!PrintStatusEnum.ALL.getCode().equals(statusCode)) {
            queryWrapper.eq(TbReportDelay::getStatus, statusCode);
        }

        queryWrapper.eq(StringUtils.isNotBlank(reportDelayDto.getBarcode()),TbReportDelay::getBarcode,reportDelayDto.getBarcode());

        // 查看当前专业组的数据
        queryWrapper.eq(Objects.nonNull(reportDelayDto.getGroupId()),TbReportDelay::getGroupId, reportDelayDto.getGroupId());
        // 日期范围查询
        queryWrapper.gt(TbReportDelay::getCreateDate, reportDelayDto.getSendDateStart()).lt(TbReportDelay::getCreateDate, reportDelayDto.getSendDateEnd());


        List<TbReportDelay> tbReportDelays = reportDelayMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(tbReportDelays)) {
            return Collections.emptyList();
        }

        return tbReportDelays.stream().map(this::convert).collect(Collectors.toList());

    }

    @Override
    public ReportDelayDto selectByReportDelayId(Long delayId) {
        if (Objects.isNull(delayId)) {
            return null;
        }

        LambdaQueryWrapper<TbReportDelay> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByDesc(TbReportDelay::getCreateDate);
        queryWrapper.eq(TbReportDelay::getDelayId, delayId);
        queryWrapper.last("limit 1");
        TbReportDelay tbReportDelay = reportDelayMapper.selectOne(queryWrapper);

        return convert(tbReportDelay);
    }

    @Override
    public long addReportDelay(ReportDelayDto reportDelayDto) {
        if (Objects.isNull(reportDelayDto)) {
            return 0L;
        }

        // 封装插入数据
        TbReportDelay obj = new TbReportDelay();
        BeanUtils.copyProperties(reportDelayDto, obj);

        // 使用雪花算法做为表ID
        obj.setDelayId(snowflakeService.genId());
        final Date now = new Date();
        final LoginUserHandler.User user = LoginUserHandler.get();


        obj.setCreateDate(now);
        obj.setCreatorName(user.getNickname());
        obj.setCreatorId(user.getUserId());
        obj.setUpdateDate(now);
        obj.setUpdaterName(user.getNickname());
        obj.setUpdaterId(user.getUserId());

        // 新增申请保存条码所对应的专业组
        obj.setOrgId(user.getOrgId());
        obj.setOrgName(user.getOrgName());
        obj.setGroupId(user.getGroupId());
        obj.setGroupName(user.getGroupName());


        if (reportDelayMapper.insert(obj) < 1) {
            throw new IllegalStateException("新增字典失败");

        }
        return obj.getDelayId();


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateByDelayIds(Set<Long> delayId,Integer status,Long cancelUserId,String cancelUserName) {
        if (CollectionUtils.isEmpty(delayId)) {
            return;
        }
        reportDelayMapper.updateByIds(delayId, status, cancelUserId, cancelUserName);
    }

    @Override
    public void updateByDelayId(ReportDelayDto reportDelayDto) {
        if (reportDelayDto == null || null == reportDelayDto.getDelayId()) {
            return;
        }
        TbReportDelay update = new TbReportDelay();
        BeanUtils.copyProperties(reportDelayDto, update);

        final LoginUserHandler.User user = LoginUserHandler.get();

        update.setCreateDate(null);
        update.setCreatorName(null);
        update.setCreatorId(null);
        update.setUpdateDate(new Date());
        update.setUpdaterName(user.getNickname());
        update.setUpdaterId(user.getUserId());
        update.setOrgId(user.getOrgId());
        update.setOrgName(user.getOrgName());
        update.setGroupId(user.getGroupId());
        update.setGroupName(user.getGroupName());

        // 封装更新数据
        reportDelayMapper.updateById(update);
    }

    @Override
    public List<ReportDelayDto> selectByReportDelayIds(Collection<Long> delayIds) {
        if (CollectionUtils.isEmpty(delayIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbReportDelay> queryWrapper = Wrappers.lambdaQuery(TbReportDelay.class).in(TbReportDelay::getDelayId,delayIds);

        List<TbReportDelay> tbReportDelays = reportDelayMapper.selectList(queryWrapper);
        return tbReportDelays.stream().map(this::convert).collect(Collectors.toList());
    }


    private ReportDelayDto convert(TbReportDelay reportDelay) {
        if (Objects.isNull(reportDelay)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(reportDelay), ReportDelayDto.class);
    }
}
