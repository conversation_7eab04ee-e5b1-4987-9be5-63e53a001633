package com.labway.lims.apply.service.chain.pick.two;

import cn.hutool.core.util.IdUtil;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.service.chain.pick.two.immunity.ImmunityTwoPickContext;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.labway.lims.api.SampleNoUtils.addOneToLastNumber;

/**
 * 加急分拣
 */
@Slf4j
@Component
public class TwoPickUrgentCommand implements Command, Filter {

    /**
     * 缺少加急样本号
     */
    public static final int NO_URGENT_SAMPLE_NO_ERROR = 1002;

    private static final String URGENT_APPLY_SAMPLE_ID = "URGENT_APPLY_SAMPLE_ID_" + IdUtil.objectId();

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;


    /**
     * 涉及到修改，不进行事务处理
     */
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);
        // 是否是免疫二次分拣
        final boolean isImmunityTwoPick = context instanceof ImmunityTwoPickContext;

        // 是否需要加急处理 （同时存在加急和非加急项目）
        if (!isNeedUrgent(context)) {
            return CONTINUE_PROCESSING;
        }

        // 是免疫二次分拣 && 上面确定了有加急项目
        if (isImmunityTwoPick) {
            throw new IllegalStateException("包含加急项目，请到单个分拣、批量分拣页面进行分拣");
        }

        // 获取到加急的项目
        final List<ApplySampleItemDto> items = context.getApplySampleItems().stream()
                // 当前专业组的项目
                .filter(e -> Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()))
                // 加急项目
                .filter(e -> Objects.equals(e.getUrgent(), UrgentEnum.URGENT.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(items)) {
            throw new IllegalStateException("加急项目为空");
        }

        // 如果样本需要加急，并且自定义了样本号。那么加急样本号也要传进来
        if (StringUtils.isNotBlank(context.getTwoPick().getSampleNo()) && StringUtils.isBlank(context.getTwoPick().getUrgentSampleNo())) {
            final UrgentErrorData urgent = new UrgentErrorData()
                    // 加急类型
                    .setUrgentType(UrgentEnum.URGENT.getValue())
                    // 条码
                    .setBarcode(context.getApplySample().getBarcode())
                    // 当前专业组下加急的检验项目
                    .setTestItemNames(context.getApplySampleItems().stream()
                            .filter(e -> Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()))
                            .filter(e -> Objects.equals(e.getUrgent(), UrgentEnum.URGENT.getCode()))
                            .map(ApplySampleItemDto::getTestItemName)
                            .collect(Collectors.toList()))
                    // 根据指定的样本号 +1
                    .setSampleNo(addOneToLastNumber(context.getTwoPick().getSampleNo()));


            final UrgentErrorData normal = new UrgentErrorData()
                    .setUrgentType(UrgentEnum.NORMAL.getValue())
                    .setBarcode(context.getApplySample().getBarcode())
                    .setTestItemNames(context.getApplySampleItems().stream()
                            .filter(e -> Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()))
                            .filter(e -> !Objects.equals(e.getUrgent(), UrgentEnum.URGENT.getCode()))
                            .map(ApplySampleItemDto::getTestItemName)
                            .collect(Collectors.toList()))
                    .setSampleNo(context.getTwoPick().getSampleNo());

            throw new LimsCodeException(NO_URGENT_SAMPLE_NO_ERROR, "请输入加急样本号")
                    .setData(List.of(normal , urgent));
        }


        // 样本条码环节
        final List<SampleFlowDto> sampleFlowDtos = sampleFlowService.selectByApplySampleId(context.getTwoPick().getApplySampleId());

        // 批量获取id   样本一个 + 加急的检验项目size + 条码环节size
        final LinkedList<Long> ids = snowflakeService.genIds(NumberUtils.INTEGER_ONE + items.size() + sampleFlowDtos.size());

        // 复制样本
        final ApplySampleDto urgentApplySample = new ApplySampleDto();
        BeanUtils.copyProperties(context.getApplySample(), urgentApplySample);
        urgentApplySample.setApplySampleId(ids.pop());
        urgentApplySample.setUrgent(UrgentEnum.URGENT.getCode());
//        urgentApplySample.setIsImmunityTwoPick(isImmunityTwoPick ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        // 加急样本肯定不是免疫二次分拣
        urgentApplySample.setIsImmunityTwoPick(YesOrNoEnum.NO.getCode());
        applySampleService.addApplySample(urgentApplySample);

        // 添加检验项目
        applySampleItemService.addApplySampleItems(items.stream().map(e -> {
            final ApplySampleItemDto applySampleItem = new ApplySampleItemDto();
            BeanUtils.copyProperties(e, applySampleItem);
            applySampleItem.setApplySampleId(urgentApplySample.getApplySampleId());
            applySampleItem.setApplySampleItemId(ids.pop());
            return applySampleItem;
        }).collect(Collectors.toList()));

        // 复制条码环节
        sampleFlowService.addSampleFlows(sampleFlowDtos.stream().map(e -> {
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            BeanUtils.copyProperties(e, sampleFlow);
            sampleFlow.setApplySampleId(urgentApplySample.getApplySampleId());
            sampleFlow.setSampleFlowId(ids.pop());
            return sampleFlow;
        }).collect(Collectors.toList()));

        // 在原样本上 删除紧急项目，因为紧急项目已经到另外一个样本上了
        applySampleItemService.deleteByApplySampleItemIds(context.getApplySampleItems().stream()
                .filter(e -> Objects.equals(e.getUrgent(), UrgentEnum.URGENT.getCode()))
                .map(ApplySampleItemDto::getApplySampleItemId)
                .collect(Collectors.toSet()));


        // 将新增的申请单样本加入 二次分拣的上下文中
        final ApplySampleTwoPickDto stp = new ApplySampleTwoPickDto();
        stp.setApplyId(urgentApplySample.getApplyId());
        stp.setApplySampleId(urgentApplySample.getApplySampleId());
        stp.setInstrumentGroupId(context.getInstrumentGroup().getInstrumentGroupId());
        stp.setInstrumentGroupName(context.getInstrumentGroup().getInstrumentGroupName());
        stp.setSampleNo(context.getTwoPick().getUrgentSampleNo());
        stp.setBarcode(context.getApplySample().getBarcode());
        stp.setGroupId(urgentApplySample.getGroupId());
        stp.setIsTransform(false);
        stp.setIsUrgent(true);
        // 如果是免疫二次分拣,设置一下免疫二次分拣时间  这里是肯定是false
//        if (isImmunityTwoPick) {
//            stp.setImmunityTwoPickDate(DateUtil.toLocalDateTime(context.getCurrentDate()));
//        }

        context.getApplySampleTwoPicks().add(stp);

        context.put(URGENT_APPLY_SAMPLE_ID, urgentApplySample.getApplySampleId());

        return CONTINUE_PROCESSING;
    }

    /**
     * 是否需要加急处理
     */
    public boolean isNeedUrgent(TwoPickContext context) {
        // 外送的跳过
        if (Objects.equals(context.getApplySample().getIsOutsourcing(), YesOrNoEnum.YES.getCode())) {
            return false;
        }

        // 当前专业组的项目
        List<ApplySampleItemDto> applySampleItems = context.getApplySampleItems().stream()
                .filter(e -> Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()))
                .collect(Collectors.toList());

        // 如果只有一个项目，那么不需要加急分拣的
        if (applySampleItems.size() == 1) {
            return false;
        }

        // 加急项目数量
        long urgentCnt = applySampleItems.stream()
                .filter(e -> Objects.equals(e.getUrgent(), UrgentEnum.URGENT.getCode())).count();

        // 如果当前专业组下既有加急项目，又有非加急项目 则需要加急处理
        return urgentCnt > 0 && urgentCnt < applySampleItems.size();
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean postprocess(Context context, Exception exception) {
        if (Objects.nonNull(exception)) {
            final Object o = context.get(URGENT_APPLY_SAMPLE_ID);
            if (o instanceof Long) {
                applySampleService.deleteByApplySampleId((Long) o);
            }
        }
        return CONTINUE_PROCESSING;
    }

    public void rollback(TwoPickContext context) {
        final Object o = context.get(URGENT_APPLY_SAMPLE_ID);
        if (o instanceof Long) {
            Long applySampleId = (Long) o;

            List<ApplySampleItemDto> items = applySampleItemService.selectByApplySampleId(applySampleId);
            if (CollectionUtils.isEmpty(items)) {
                return;
            }

            // 将加急复制出来的项目还原回去
            LinkedList<Long> ids = snowflakeService.genIds(items.size());
            // 添加分出来的加急检验项目 到 原始申请单样本下面
            applySampleItemService.addApplySampleItems(items.stream().map(e -> {
                final ApplySampleItemDto applySampleItem = new ApplySampleItemDto();
                BeanUtils.copyProperties(e, applySampleItem);
                applySampleItem.setApplySampleId(context.getTwoPick().getApplySampleId());
                applySampleItem.setApplySampleItemId(ids.pop());
                return applySampleItem;
            }).collect(Collectors.toList()));

            // 删除加急申请单样本上的检验项目
            applySampleItemService.deleteByApplySampleItemIds(items.stream()
                    .map(ApplySampleItemDto::getApplySampleItemId).collect(Collectors.toList()));

            // 删除加急的申请单样本
            applySampleService.deleteByApplySampleId(applySampleId);

            // 删除加急那条条码环节
            sampleFlowService.deleteByApplySampleIds(Lists.newArrayList(applySampleId));
        }
    }

    /**
     * 缺失加急样本号错误
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class UrgentErrorData implements Serializable {
        /**
         * 加急类型
         *
         * @see UrgentEnum
         */
        private String urgentType;

        /**
         * 检验项目名称
         */
        private List<String> testItemNames;

        /**
         * 条码号
         */
        private String barcode;

        /**
         * 样本号
         */
        private String sampleNo;

    }
}
