
package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.MaterialInventoryCheckDetailDto;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.model.TbMaterialInventoryCheckDetail;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 物料盘点详情 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface MaterialInventoryCheckDetailConverter {

    TbMaterialInventoryCheckDetail tbMaterialInventoryCheckDetailFromTbObjDto(MaterialInventoryCheckDetailDto obj);

    List<TbMaterialInventoryCheckDetail>
    tbMaterialInventoryCheckDetailListFromTbObjDto(List<MaterialInventoryCheckDetailDto> TbMaterialInventoryCheckDetail);

    MaterialInventoryCheckDetailDto materialInventoryCheckDetailDtoFromTbObjDto(MaterialInventoryDto obj);
    List<MaterialInventoryCheckDetailDto>
    materialInventoryCheckDetailDtoListFromTbObjDto(List<TbMaterialInventoryCheckDetail> list);

}
