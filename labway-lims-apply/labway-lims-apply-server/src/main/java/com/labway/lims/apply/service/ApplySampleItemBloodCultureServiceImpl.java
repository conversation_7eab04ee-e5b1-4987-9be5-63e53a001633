package com.labway.lims.apply.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.service.ApplySampleItemBloodCultureService;
import com.labway.lims.apply.mapper.TbApplySampleItemBloodCultureMapper;
import com.labway.lims.apply.model.TbApplySampleItemBloodCulture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;

@Slf4j
@DubboService
public class ApplySampleItemBloodCultureServiceImpl implements ApplySampleItemBloodCultureService {

    @Resource
    private TbApplySampleItemBloodCultureMapper applySampleItemBloodCultureMapper;

    @Nullable
    @Override
    public ApplySampleItemBloodCultureDto selectByApplySampleItemId(long applySampleItemId) {
        final LambdaQueryWrapper<TbApplySampleItemBloodCulture> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbApplySampleItemBloodCulture::getApplySampleItemId, applySampleItemId);
        wrapper.last("limit 1");
        return convert(applySampleItemBloodCultureMapper.selectOne(wrapper));
    }

    @Override
    public void deleteByApplySampleItemBloodCultureId(long applySampleItemBloodCultureId) {
        applySampleItemBloodCultureMapper.deleteById(applySampleItemBloodCultureId);
    }

    @Override
    public void addApplySampleItemBloodCultures(List<ApplySampleItemBloodCultureDto> applySampleItemBloodCultures) {
        if (CollectionUtils.isEmpty(applySampleItemBloodCultures)) {
            return;
        }

        for (ApplySampleItemBloodCultureDto e : applySampleItemBloodCultures) {
            if (Objects.isNull(e.getCreateDate())) {
                e.setCreateDate(new Date());
            }
            if (Objects.isNull(e.getCreatorId())) {
                e.setCreatorId(LoginUserHandler.get().getUserId());
            }
            if (Objects.isNull(e.getCreatorName())) {
                e.setCreatorName(LoginUserHandler.get().getNickname());
            }
            if (Objects.isNull(e.getUpdateDate())) {
                e.setUpdateDate(new Date());
            }
            if (Objects.isNull(e.getUpdaterId())) {
                e.setUpdaterId(LoginUserHandler.get().getUserId());
            }
            if (Objects.isNull(e.getUpdaterName())) {
                e.setUpdaterName(LoginUserHandler.get().getNickname());
            }
            if (Objects.isNull(e.getIsDelete())) {
                e.setIsDelete(YesOrNoEnum.NO.getCode());
            }

            // 默认 暂时这样
            e.setLulAnaerobic(ObjectUtil.defaultIfNull(e.getLulAnaerobic(), 0));
            e.setLulAerobic(ObjectUtil.defaultIfNull(e.getLulAerobic(), 0));
            e.setLulPediatricBottle(ObjectUtil.defaultIfNull(e.getLulPediatricBottle(), 0));
            e.setLllAnaerobic(ObjectUtil.defaultIfNull(e.getLllAnaerobic(), 0));
            e.setLllAerobic(ObjectUtil.defaultIfNull(e.getLllAerobic(), 0));
            e.setLllPediatricBottle(ObjectUtil.defaultIfNull(e.getLllPediatricBottle(), 0));
            e.setRulAnaerobic(ObjectUtil.defaultIfNull(e.getRulAnaerobic(), 0));
            e.setRulAerobic(ObjectUtil.defaultIfNull(e.getRulAerobic(), 0));
            e.setRulPediatricBottle(ObjectUtil.defaultIfNull(e.getRulPediatricBottle(), 0));
            e.setRllAnaerobic(ObjectUtil.defaultIfNull(e.getRllAnaerobic(), 0));
            e.setRllAerobic(ObjectUtil.defaultIfNull(e.getRllAerobic(), 0));
            e.setRllPediatricBottle(ObjectUtil.defaultIfNull(e.getRllPediatricBottle(), 0));
            e.setAnaerobic(ObjectUtil.defaultIfNull(e.getAnaerobic(), 0));
            e.setAerobic(ObjectUtil.defaultIfNull(e.getAerobic(), 0));
            e.setPediatricBottle(ObjectUtil.defaultIfNull(e.getPediatricBottle(), 0));

        }

        applySampleItemBloodCultureMapper.insertBatch(applySampleItemBloodCultures);
    }

    @Override
    public ApplySampleItemBloodCultureDto selectByApplySampleIdAndItemId(Long testItemId, Long applySampleId) {
        final LambdaQueryWrapper<TbApplySampleItemBloodCulture> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbApplySampleItemBloodCulture::getApplySampleId,applySampleId);
        wrapper.eq(TbApplySampleItemBloodCulture::getTestItemId,testItemId);
        wrapper.eq(TbApplySampleItemBloodCulture::getIsDelete,0);
        wrapper.last("limit 1");
        return convert(applySampleItemBloodCultureMapper.selectOne(wrapper));
    }

    @Override
    public ApplySampleItemBloodCultureDto selectByApplySampleId(Long applySampleId) {
        TbApplySampleItemBloodCulture tbApplySampleItemBloodCulture = applySampleItemBloodCultureMapper.selectOne(Wrappers.lambdaQuery(TbApplySampleItemBloodCulture.class)
                .eq(TbApplySampleItemBloodCulture::getApplySampleId, applySampleId)
                .eq(TbApplySampleItemBloodCulture::getIsDelete, 0));
        return convert(tbApplySampleItemBloodCulture);
    }

    @Override
    public Boolean updateByCultureId(ApplySampleItemBloodCultureDto applySampleItemBloodCultureDto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        TbApplySampleItemBloodCulture tbApplySampleItemBloodCulture = JSON.parseObject(JSON.toJSONString(applySampleItemBloodCultureDto), TbApplySampleItemBloodCulture.class);
        tbApplySampleItemBloodCulture.setUpdaterId(user.getUserId());
        tbApplySampleItemBloodCulture.setUpdaterName(user.getNickname());
        tbApplySampleItemBloodCulture.setUpdateDate(new Date());
        return applySampleItemBloodCultureMapper.updateById(tbApplySampleItemBloodCulture) > 0;
        //JSON.parseObject(JSON.toJSONString(culture), ApplySampleItemBloodCultureDto.class)
    }

    @Override
    public List<ApplySampleItemBloodCultureDto> selectApplySampleIds(Set<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)){
            return Collections.emptyList();
        }
        List<TbApplySampleItemBloodCulture> tbApplySampleItemBloodCultures = applySampleItemBloodCultureMapper.selectList(
                Wrappers.lambdaQuery(TbApplySampleItemBloodCulture.class)
                        .in(TbApplySampleItemBloodCulture::getApplySampleId, applySampleIds)
                        .eq(TbApplySampleItemBloodCulture::getIsDelete, 0));
        return JSON.parseArray(JSON.toJSONString(tbApplySampleItemBloodCultures), ApplySampleItemBloodCultureDto.class);
    }


    private ApplySampleItemBloodCultureDto convert(TbApplySampleItemBloodCulture culture) {
        if (Objects.isNull(culture)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(culture), ApplySampleItemBloodCultureDto.class);
    }
}
