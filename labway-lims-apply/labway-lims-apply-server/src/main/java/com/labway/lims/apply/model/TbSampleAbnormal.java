package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleAbnormalStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 样本异常
 *
 * <AUTHOR>
 * @since 2023/4/10 9:31
 */
@Setter
@Getter
@TableName("tb_sample_abnormal")
public class TbSampleAbnormal implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 异常值ID
     */
    @TableId
    private Long sampleAbnormalId;
    /**
     * 条码
     */
    private String barcode;
    /**
     * 申请单ID
     */
    private Long applyId;
    /**
     * 送检机构ID
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 检验项目
     */
    private String testItemName;

    /**
     * 送检医生
     */
    private String sendDoctorName;

    /**
     * 名称
     */
    private String patientName;

    /**
     * 性别，1:男，2:女
     */
    private Integer patientSex;

    /**
     * 年龄
     */
    private String patientAge;
    /**
     * 登记专业组ID
     */
    private Long registGroupId;
    /**
     * 登记专业组名称
     */
    private String registGroupName;
    /**
     * 登记时间
     */
    private Date registDate;
    /**
     * 登记人ID
     */
    private Long registerId;
    /**
     * 登记人姓名
     */
    private String registerName;
    /**
     * 异常原因编码
     */
    private String abnormalReasonCode;
    /**
     * 异常原因描述
     */
    private String abnormalReasonName;
    /**
     * 登记内容
     */
    private String registContent;
    /**
     * 处理部门ID
     */
    private Long handleGroupId;
    /**
     * 处理部门名称
     */
    private String handleGroupName;
    /**
     * 处理人ID
     */
    private Long handleUserId;
    /**
     * 处理人姓名
     */
    private String handleUserName;
    /**
     * 处理内容
     */
    private String handleContent;
    /**
     * 处理时间
     */
    private Date handleDate;
    /**
     * 确认部门ID
     */
    private Long confirmGroupId;
    /**
     * 确认部门名称
     */
    private String confirmGroupName;
    /**
     * 确认人ID
     */
    private Long confirmUserId;
    /**
     * 确认人姓名
     */
    private String confirmUserName;
    /**
     * 确认内容
     */
    private String confirmContent;
    /**
     * 确认时间
     */
    private Date confirmDate;
    /**
     * 状态:0未处理，1已处理，2已确认，3已作废
     * 
     * @see SampleAbnormalStatusEnum
     */
    private Integer status;
    /**
     * 1:已删除 0：未删除
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新者
     */
    private Long updaterId;
    /**
     * 更新人
     */
    private String updaterName;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 图片集合
     */
    private String images;
}
