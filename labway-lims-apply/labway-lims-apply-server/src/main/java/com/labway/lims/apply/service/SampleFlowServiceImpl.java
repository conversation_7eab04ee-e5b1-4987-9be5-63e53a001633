package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.mapper.TbSampleFlowMapper;
import com.labway.lims.apply.model.TbSampleFlow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class SampleFlowServiceImpl implements SampleFlowService {
    @Resource
    private TbSampleFlowMapper sampleFlowMapper;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public List<SampleFlowDto> selectByApplyId(long applyId) {
        return sampleFlowMapper.selectList(new LambdaQueryWrapper<TbSampleFlow>()
                        .eq(TbSampleFlow::getApplyId, applyId)
                        .orderByAsc(TbSampleFlow::getSampleFlowId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<SampleFlowDto> selectByBarcode(String barcode) {
        if (StringUtils.isBlank(barcode)) {
            return Collections.emptyList();
        }

        return sampleFlowMapper.selectList(new LambdaQueryWrapper<TbSampleFlow>().eq(TbSampleFlow::getBarcode, barcode)
                        .orderByAsc(TbSampleFlow::getSampleFlowId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<SampleFlowDto> selectByApplySampleId(long applySampleId) {
        return sampleFlowMapper.selectList(new LambdaQueryWrapper<TbSampleFlow>()
                        .eq(TbSampleFlow::getApplySampleId, applySampleId).orderByAsc(TbSampleFlow::getSampleFlowId)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<SampleFlowDto>> selectByApplySampleIdsAsMap(Collection<Long> applySampleIds) {

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyMap();
        }
        final LambdaQueryWrapper<TbSampleFlow> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbSampleFlow::getApplySampleId, applySampleIds);
        final List<SampleFlowDto> flows = sampleFlowMapper.selectList(wrapper).stream()
                .map(this::convert).collect(Collectors.toList());
        return flows.stream().collect(Collectors.groupingBy(SampleFlowDto::getApplySampleId));
    }

    @Override
    public Map<Long, List<SampleFlowDto>> selectWithOutContentByApplySampleIdsAsMap(Collection<Long> applySampleIds) {

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyMap();
        }
        final QueryWrapper<TbSampleFlow> wrapper = new QueryWrapper<>();
        wrapper.select("sample_flow_id,apply_id,apply_sample_id,barcode,operate_code,operate_name,operator,operator_id,create_date,update_date,creator_id,creator_name,updater_id,updater_name,org_id,org_name,is_delete");
        wrapper.in("apply_sample_id", applySampleIds);

        return sampleFlowMapper.selectList(wrapper).stream()
                .map(this::convert).collect(Collectors.toList()).stream()
                .collect(Collectors.groupingBy(SampleFlowDto::getApplySampleId));
    }

    @Override
    public long addSampleFlow(SampleFlowDto dto) {

        fillDefaultValue(dto);

        final TbSampleFlow sampleFlow = new TbSampleFlow();
        BeanUtils.copyProperties(dto, sampleFlow);

        if (sampleFlowMapper.insert(sampleFlow) < 1) {
            throw new IllegalStateException("添加流水失败");
        }

        log.info("用户 [{}] 添加流水 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(sampleFlow));

        return sampleFlow.getSampleFlowId();
    }

    private void fillDefaultValue(SampleFlowDto sampleFlow) {

        sampleFlow.setIsDelete(YesOrNoEnum.NO.getCode());

        sampleFlow.setSampleFlowId(
                Optional.ofNullable(sampleFlow.getSampleFlowId()).orElseGet(() -> snowflakeService.genId()));

        sampleFlow.setOperator(
                Optional.ofNullable(sampleFlow.getOperator()).orElseGet(() -> LoginUserHandler.get().getNickname()));
        sampleFlow.setOperatorId(
                Optional.ofNullable(sampleFlow.getOperatorId()).orElseGet(() -> LoginUserHandler.get().getUserId()));

        sampleFlow.setCreateDate(Optional.ofNullable(sampleFlow.getCreateDate()).orElseGet(Date::new));
        sampleFlow.setUpdateDate(Optional.ofNullable(sampleFlow.getUpdateDate()).orElseGet(Date::new));

        sampleFlow.setCreatorId(
                Optional.ofNullable(sampleFlow.getUpdaterId()).orElseGet(() -> LoginUserHandler.get().getUserId()));
        sampleFlow.setCreatorName(
                Optional.ofNullable(sampleFlow.getCreatorName()).orElseGet(() -> LoginUserHandler.get().getNickname()));
        sampleFlow.setUpdaterId(
                Optional.ofNullable(sampleFlow.getUpdaterId()).orElseGet(() -> LoginUserHandler.get().getUserId()));
        sampleFlow.setUpdaterName(
                Optional.ofNullable(sampleFlow.getUpdaterName()).orElseGet(() -> LoginUserHandler.get().getNickname()));
        sampleFlow
                .setOrgId(Optional.ofNullable(sampleFlow.getOrgId()).orElseGet(() -> LoginUserHandler.get().getOrgId()));
        sampleFlow.setOrgName(
                Optional.ofNullable(sampleFlow.getOrgName()).orElseGet(() -> LoginUserHandler.get().getOrgName()));

    }

    @Override
    public List<SampleFlowDto> selectByApplySampleIdAndOperateCode(Collection<Long> applySampleIds,
                                                                   BarcodeFlowEnum operateCode) {
        if (CollectionUtils.isEmpty(applySampleIds) || Objects.isNull(operateCode)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSampleFlow> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSampleFlow::getApplySampleId, applySampleIds);
        queryWrapper.eq(TbSampleFlow::getOperateCode, operateCode.name());
        queryWrapper.eq(TbSampleFlow::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByAsc(TbSampleFlow::getSampleFlowId);
        return convert(sampleFlowMapper.selectList(queryWrapper));
    }

    @Override
    public void addSampleFlows(List<SampleFlowDto> flows) {
        if (CollectionUtils.isEmpty(flows)) {
            return;
        }

        flows.forEach(this::fillDefaultValue);

        sampleFlowMapper.addBatch(flows);
    }

    @Override
    public void copySampleFlows(long sourceApplySampleId, long targetApplySampleId) {
        copySampleFlows(sourceApplySampleId, List.of(targetApplySampleId));
    }

    @Override
    public void copySampleFlows(long sourceApplySampleId, Collection<Long> targetApplySampleIds) {

        if (CollectionUtils.isEmpty(targetApplySampleIds)) {
            return;
        }

        final List<SampleFlowDto> sampleFlows = selectByApplySampleId(sourceApplySampleId);
        if (CollectionUtils.isEmpty(sampleFlows)) {
            return;
        }

        final LinkedList<Long> ids = snowflakeService.genIds(sampleFlows.size() * targetApplySampleIds.size());
        final List<SampleFlowDto> list = new LinkedList<>();

        for (Long targetApplySampleId : targetApplySampleIds) {
            for (SampleFlowDto e : sampleFlows) {
                final SampleFlowDto sf = new SampleFlowDto();
                BeanUtils.copyProperties(e, sf);
                sf.setSampleFlowId(ids.pop());
                sf.setApplySampleId(targetApplySampleId);
                list.add(sf);
            }
        }


        addSampleFlows(list);
    }

    /**
     * 根据申请单样本ID删除条码环节记录
     * @param applySampleIds
     */
    @Override
    public void deleteByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return;
        }
        sampleFlowMapper.delete(
                new LambdaQueryWrapper<TbSampleFlow>().in(TbSampleFlow::getApplySampleId, applySampleIds));
    }


    /**
     * 根据申请单样本ID查询条码环节记录
     * @param applySampleIds
     * @return
     */
    @Override
    public List<SampleFlowDto> selectByApplySampleIds(Collection<Long> applySampleIds) {
        return sampleFlowMapper.selectList(new LambdaQueryWrapper<TbSampleFlow>()
                        .in(TbSampleFlow::getApplySampleId, applySampleIds)
                        .orderByAsc(TbSampleFlow::getSampleFlowId))
                .stream()
                .map(this::convert).collect(Collectors.toList());
    }


    private SampleFlowDto convert(TbSampleFlow sampleFlow) {
        if (Objects.isNull(sampleFlow)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(sampleFlow), SampleFlowDto.class);
    }

    private List<SampleFlowDto> convert(List<TbSampleFlow> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }
}
