
package com.labway.lims.apply.service.chain.material.delivery.income;

import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryIncomeItemDto;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料 修改 修改 专业组 物料总库存
 *
 * <AUTHOR>
 * @since 2023/5/9 10:47
 */
@Slf4j
@Component
public class MaterialIncomeAddGroupMaterialCommand implements Command {

    @DubboReference
    private GroupMaterialService groupMaterialService;

    @Override
    public boolean execute(Context context) throws Exception {
        final MaterialIncomeContext from = MaterialIncomeContext.from(context);
        var incomeItemList = from.getIncomeItemList();
        var materialDeliveryDetails = from.getMaterialDeliveryDetails();
        var groupMaterialDtos = from.getGroupMaterialDtos();

        // key: 出库详情id value: 出库物料详情
        final Map<Long, MaterialDeliveryDetailDto> deliveryDetailDtoMap = materialDeliveryDetails.stream()
            .collect(Collectors.toMap(MaterialDeliveryDetailDto::getDetailId, Function.identity()));

        // 入库 的物料id 对应 入库 相关数量
        Map<Long, BigDecimal> materialIdAndIncomeMainNumber = new HashMap<>();
        Map<Long, BigDecimal> materialIdAndIncomeAssistNumber = new HashMap<>();
        for (MaterialDeliveryIncomeItemDto incomeItemDto : incomeItemList) {
            Long detailId = incomeItemDto.getDetailId();

            // 入库 记录对应出库详情
            MaterialDeliveryDetailDto deliveryDetailDto = deliveryDetailDtoMap.get(detailId);

            // 物料 对应入库主数量 刚开始 为0
            BigDecimal receiveMainNumber = materialIdAndIncomeMainNumber.getOrDefault(deliveryDetailDto.getMaterialId(), BigDecimal.ZERO)
		            .add(incomeItemDto.getIncomeMainNumber());
            materialIdAndIncomeMainNumber.put(deliveryDetailDto.getMaterialId(), receiveMainNumber);

            // 辅数量 同 主数量逻辑
            BigDecimal receiveAssistNumber =
                materialIdAndIncomeAssistNumber.getOrDefault(deliveryDetailDto.getMaterialId(), BigDecimal.ZERO)
                    .add(incomeItemDto.getIncomeAssistNumber());
            materialIdAndIncomeAssistNumber.put(deliveryDetailDto.getMaterialId(), receiveAssistNumber);

        }

        for (GroupMaterialDto groupMaterialDto : groupMaterialDtos) {

            // 主、辅 库存 加上 相应入库数量
            BigDecimal mainUnitInventory = groupMaterialDto.getMainUnitInventory()
		            .add(materialIdAndIncomeMainNumber.get(groupMaterialDto.getMaterialId()));

            BigDecimal assistUnitInventory = groupMaterialDto.getAssistUnitInventory()
                .add(materialIdAndIncomeAssistNumber.get(groupMaterialDto.getMaterialId()));

            GroupMaterialDto update = new GroupMaterialDto();
            update.setGroupMaterialId(groupMaterialDto.getGroupMaterialId());
            update.setMainUnitInventory(mainUnitInventory);
            update.setAssistUnitInventory(assistUnitInventory);

            groupMaterialService.updateByGroupMaterialId(update);
        }

        return CONTINUE_PROCESSING;
    }
}
