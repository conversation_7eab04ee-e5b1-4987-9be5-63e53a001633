package com.labway.lims.apply.service.chain.splitblood;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 禁止分血的样本操作
 */
@Component
@Slf4j
public class DisableSplitBloodApplySampleCommand implements Command {
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private RackLogicService rackLogicService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private ApplySampleService applySampleService;


    @Override
    public boolean execute(Context c) throws Exception {
        final SplitBloodContext context = SplitBloodContext.from(c);

        if (context.isSupportedSplitBlood()) {
            return CONTINUE_PROCESSING;
        }

        final RackLogicSpaceDto rackLogicSpace = context.getRackLogicSpace();
        final RackLogicDto rackLogic = context.getRackLogic();
        final Map<Long, List<ApplySampleItemDto>> items = context.getApplySampleItems().stream()
                .collect(Collectors.groupingBy(ApplySampleItemDto::getGroupId));
        final LinkedList<Long> ids = snowflakeService.genIds(items.size() << 2);

        // 如果不支持分血，那么就是没有分血。检验项目多个涉及到多个专业组接收
        for (Map.Entry<Long, List<ApplySampleItemDto>> e : items.entrySet()) {
            // 获取到分血待交接有没有试管架，没有则新建
            final List<RackLogicDto> rackLogics = new LinkedList<>(rackLogicService.selectByRackCodeIdAndPosition(rackLogic.getRackCode(),
                    RackLogicPositionEnum.SPLIT_BLOOD.getCode()));
            rackLogics.removeIf(k -> !Objects.equals(e.getKey(), k.getNextGroupId()));
            final RackLogicDto rl;
            if (CollectionUtils.isEmpty(rackLogics)) {
                rl = new RackLogicDto();
                BeanUtils.copyProperties(rackLogic, rl);
                rl.setNextGroupId(e.getKey());
                rl.setNextGroupName(e.getValue().iterator().next().getGroupName());
                rl.setRackLogicId(ids.pop());
                rl.setPosition(RackLogicPositionEnum.SPLIT_BLOOD.getCode());
                // 添加一个新的逻辑试管架
                rackLogicService.addRackLogic(rl);
            } else {
                rl = rackLogics.iterator().next();
            }

            final RackLogicSpaceDto rls = new RackLogicSpaceDto();
            BeanUtils.copyProperties(rackLogicSpace, rls);
            rls.setIsDelete(YesOrNoEnum.NO.getCode());
            rls.setRackLogicId(rl.getRackLogicId());
            rls.setRackLogicSpaceId(ids.pop());

            // 添加逻辑试管架关联
            rackLogicSpaceService.addRackLogicSpace(rls);
        }

        // 修改样本的状态 改为已分血
        final ApplySampleDto as = new ApplySampleDto();
        as.setApplySampleId(context.getApplySampleId());
        as.setIsSplitBlood(YesOrNoEnum.YES.getCode());
        as.setSplitDate(new Date());
        as.setSplitterId(LoginUserHandler.get().getUserId());
        as.setSplitterName(LoginUserHandler.get().getNickname());
        applySampleService.updateByApplySampleId(as);

        return CONTINUE_PROCESSING;
    }
}
