package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 二次分拣
 */
@Getter
@Setter
public class ApplySampleTwoPickVo {
    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 批量分拣
     */
    private Date twoPickDate;
}
