package com.labway.lims.apply.service.chain.material.delivery.business.noapply;

import com.google.common.collect.Lists;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.BusinessCenterDeliveryDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryRecordDto;
import com.labway.lims.apply.api.service.MaterialDeliveryDetailService;
import com.labway.lims.apply.api.service.MaterialDeliveryRecordService;
import com.labway.lims.apply.mapstruct.MaterialDeliveryConverter;
import com.labway.lims.apply.service.chain.material.delivery.business.BusinessCenterDeliveryContext;
import com.labway.lims.base.api.dto.GroupMaterialDetailDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 接收业务中台 出库信息 添加相应数据
 *
 * <AUTHOR>
 * @since 2023/5/6 14:53
 */
@Slf4j
@RefreshScope
@Component
public class BusinessCenterNoApplyDeliveryAddDataCommand implements Command {

    @Resource
    private MaterialDeliveryRecordService materialDeliveryRecordService;

    @Resource
    private MaterialDeliveryDetailService materialDeliveryDetailService;
    @Resource
    private MaterialDeliveryConverter materialDeliveryConverter;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Value("${business-center.org-code:未知实验室编码}")
    private String orgCode;
    @Value("${business-center.org-name:未知实验室名称}")
    private String orgName;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean execute(Context context) throws Exception {
        final BusinessCenterDeliveryContext from = BusinessCenterDeliveryContext.from(context);
        final BusinessCenterDeliveryDto deliveryDto = from.getDeliveryDto();
        final ProfessionalGroupDto groupDto = from.getGroupDto();
        //        final GroupMaterialApplyDto groupMaterialApply = from.getGroupMaterialApply();
        final Map<String, GroupMaterialDetailDto> materialDtoByMaterialCode = from.getMaterialDtoByMaterialCode();

        // 业务中台 出库物料
        final List<BusinessCenterDeliveryDto.BusinessCenterDeliveryItemDto> deliveryItemList =
                deliveryDto.getDeliveryItemList();

        // 构造 出库单(待入库单记录)
        LinkedList<Long> genIds = snowflakeService.genIds(deliveryDto.getDeliveryItemList().size() + 1);

        final MaterialDeliveryRecordDto deliveryRecordDto = new MaterialDeliveryRecordDto();
        deliveryRecordDto.setRecordId(genIds.pop());
        deliveryRecordDto.setDeliveryNo(deliveryDto.getDeliveryNo());
        deliveryRecordDto.setApplyId(NumberUtils.LONG_ZERO);
        deliveryRecordDto.setDeliveryDate(deliveryDto.getDeliveryDate());
        deliveryRecordDto.setDeliveryUser(deliveryDto.getDeliveryUser());
        deliveryRecordDto.setStatus(YesOrNoEnum.NO.getCode());
        deliveryRecordDto.setGroupId(groupDto.getGroupId());
        deliveryRecordDto.setGroupName(groupDto.getGroupName());
        deliveryRecordDto.setOrgId(deliveryDto.getOrgId());
        deliveryRecordDto.setOrgName(orgName);
        deliveryRecordDto.setCreateDate(new Date());
        deliveryRecordDto.setUpdateDate(new Date());
        deliveryRecordDto.setUpdaterId(NumberUtils.LONG_ZERO);
        deliveryRecordDto.setUpdaterName(StringUtils.EMPTY);
        deliveryRecordDto.setCreatorId(NumberUtils.LONG_ZERO);
        deliveryRecordDto.setCreatorName(StringUtils.EMPTY);
        deliveryRecordDto.setIsDelete(YesOrNoEnum.NO.getCode());

        materialDeliveryRecordService.addMaterialDeliveryRecord(deliveryRecordDto);

        // 构造 出库记录详情
        List<MaterialDeliveryDetailDto> targetList = Lists.newArrayListWithCapacity(deliveryItemList.size());
        Date date = new Date();
        deliveryItemList.forEach(item -> {
            MaterialDeliveryDetailDto deliveryDetailDto =
                    materialDeliveryConverter.fromBusinessCenterDeliveryItemDto(item);
            // 对应实验室 物料信息 已校验 一定会有
            GroupMaterialDetailDto materialDto = materialDtoByMaterialCode.get(item.getMaterialCode());
            deliveryDetailDto.setMaterialId(materialDto.getMaterialId());
            deliveryDetailDto.setDetailId(genIds.pop());
            deliveryDetailDto.setDeliveryNo(deliveryDto.getDeliveryNo());
            deliveryDetailDto.setOrgId(deliveryRecordDto.getOrgId());
            deliveryDetailDto.setOrgName(deliveryRecordDto.getOrgName());
            deliveryDetailDto.setCreateDate(date);
            deliveryDetailDto.setUpdateDate(date);
            deliveryDetailDto.setUpdaterId(NumberUtils.LONG_ZERO);
            deliveryDetailDto.setUpdaterName(StringUtils.EMPTY);
            deliveryDetailDto.setCreatorId(NumberUtils.LONG_ZERO);
            deliveryDetailDto.setCreatorName(StringUtils.EMPTY);
            deliveryDetailDto.setIsDelete(YesOrNoEnum.NO.getCode());
            deliveryDetailDto.setMaterialBarcode(item.getMaterialBarcode());

            targetList.add(deliveryDetailDto);

        });
        materialDeliveryDetailService.addMaterialDeliveryDetails(targetList);

        return CONTINUE_PROCESSING;
    }
}
