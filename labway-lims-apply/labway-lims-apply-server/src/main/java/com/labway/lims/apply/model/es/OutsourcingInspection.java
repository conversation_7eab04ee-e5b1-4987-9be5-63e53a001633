package com.labway.lims.apply.model.es;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;


/**
 * 外送
 */
@Getter
@Setter
public class OutsourcingInspection extends BaseSampleEsModel {

    /**
     * 外送机构的条码号，譬如外送到迪安，那这个就是迪安的条码。注意，这个不是送检机构的条码
     */
    private String exportBarcode;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

    /**
     * 外送机构名称
     */
    private String exportOrgName;

    /**
     * 是否已打印清单，1:是，0:不是
     *
     * @see YesOrNoEnum
     */
    private Integer isPrintList;

    /**
     * 打印清单日期
     */
    private Date printListDate;

    /**
     * 送检时间
     */
    private Date inspectionDate;
}
