package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class ApplyItemVo {


    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 别名
     */
    private String aliasName;


    /**
     * 检验方法名称
     */
    private String examMethodName;

    /**
     * 名称缩写
     */
    private String shortName;


    /**
     * 管型 name
     */
    private String tubeName;
    /**
     * 管型 code
     */
    private String tubeCode;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 是否支持外送
     */
    private Integer enableExport;

    /**
     * 外送机构名称
     */
    private String exportOrgName;

    /**
     * 项目类型名称
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 收费价格
     */
    private BigDecimal feePrice;

    /**
     * 条码
     */
    private String barcode;

}
