package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.MaterialInventoryCheckDetailDto;
import com.labway.lims.apply.api.service.MaterialInventoryCheckDetailService;
import com.labway.lims.apply.mapper.TbMaterialInventoryCheckDetailMapper;
import com.labway.lims.apply.mapstruct.MaterialInventoryCheckDetailConverter;
import com.labway.lims.apply.model.TbMaterialInventoryCheckDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 物料盘点详情 Service impl
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Slf4j
@DubboService
public class MaterialInventoryCheckDetailServiceImpl implements MaterialInventoryCheckDetailService {
    @Resource
    private TbMaterialInventoryCheckDetailMapper tbMaterialInventoryCheckDetailMapper;

    @Resource
    private MaterialInventoryCheckDetailConverter materialInventoryCheckDetailConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMaterialInventoryCheckDetailDtos(List<MaterialInventoryCheckDetailDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 物料盘点详情
        List<TbMaterialInventoryCheckDetail> targetList =
            materialInventoryCheckDetailConverter.tbMaterialInventoryCheckDetailListFromTbObjDto(list);

        // 数量 分区批次插入
        List<List<TbMaterialInventoryCheckDetail>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbMaterialInventoryCheckDetailMapper.batchAddMaterialInventoryCheckDetail(item));

        log.debug("用户 [{}] 新增物料物料盘点详情[{}]成功", loginUser.getNickname(), JSON.toJSONString(targetList));
    }

    @Override
    public List<MaterialInventoryCheckDetailDto> selectByCheckId(long checkId) {
        LambdaQueryWrapper<TbMaterialInventoryCheckDetail> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(TbMaterialInventoryCheckDetail::getCheckId, checkId);
        queryWrapper.eq(TbMaterialInventoryCheckDetail::getIsDelete, YesOrNoEnum.NO.getCode());

        return materialInventoryCheckDetailConverter.materialInventoryCheckDetailDtoListFromTbObjDto(
            tbMaterialInventoryCheckDetailMapper.selectList(queryWrapper));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByDetailId(MaterialInventoryCheckDetailDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbMaterialInventoryCheckDetail target =
            materialInventoryCheckDetailConverter.tbMaterialInventoryCheckDetailFromTbObjDto(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbMaterialInventoryCheckDetailMapper.updateById(target) < 1) {
            throw new LimsException("修改物料盘点详情失败");
        }

        log.info("用户 [{}] 修改物料盘点详情成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }
}
