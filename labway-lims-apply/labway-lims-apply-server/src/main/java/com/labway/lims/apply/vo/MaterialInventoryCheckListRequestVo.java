package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 盘点记录 请求参数
 *
 * <AUTHOR>
 * @since 2023/5/11 16:59
 */
@Getter
@Setter
public class MaterialInventoryCheckListRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 盘点时间 开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date beginCheckTime;

    /**
     * 盘点时间 结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endCheckTime;

    /**
     * 盘点单号
     */
    private String checkNo;
}
