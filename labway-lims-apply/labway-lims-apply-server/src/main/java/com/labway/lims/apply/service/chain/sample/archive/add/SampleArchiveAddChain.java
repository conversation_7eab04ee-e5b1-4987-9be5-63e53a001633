package com.labway.lims.apply.service.chain.sample.archive.add;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 样本归档-存储
 *
 * <AUTHOR>
 * @since 2023/4/14 11:39
 */
@Component
public class SampleArchiveAddChain extends ChainBase implements InitializingBean {

    @Resource
    private SampleArchiveAddLimitCommand sampleArchiveAddLimitCommand;
    @Resource
    private SampleArchiveAddCheckParamCommand sampleArchiveAddCheckParamCommand;
    @Resource
    private CheckSampleResultCommand checkSampleResultCommand;
    @Resource
    private SampleArchiveAddFillRackLogicCommand sampleArchiveAddFillRackLogicCommand;

    @Resource
    private SampleArchiveAddRackLogicSpaceCommand sampleArchiveAddRackLogicSpaceCommand;

    @Resource
    private SampleArchiveAddFillRackArchiveCommand sampleArchiveAddFillRackArchiveCommand;

    @Resource
    private SampleArchiveAddHandleDataCommand sampleArchiveAddHandleDataCommand;

    @Resource
    private SampleArchiveAddFlowCommand sampleArchiveAddFlowCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 检查参数
        addCommand(sampleArchiveAddCheckParamCommand);
        // 上锁
        addCommand(sampleArchiveAddLimitCommand);
        // 获取 物理试管架 对应 归档逻辑试管架
        addCommand(sampleArchiveAddFillRackLogicCommand);
        // 获取归档试管架 信息
        addCommand(sampleArchiveAddFillRackArchiveCommand);
        // 校验是否有危急，异常判断
        addCommand(checkSampleResultCommand);
        // 获取 逻辑试管架 样本占用
        addCommand(sampleArchiveAddRackLogicSpaceCommand);

        // 处理相关数据 -新增或修改逻辑试管架 -新增或修改归档逻辑试管架信息 - 新增逻辑试管架空间占用
        addCommand(sampleArchiveAddHandleDataCommand);

        // 保存流水
        addCommand(sampleArchiveAddFlowCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);
    }
}
