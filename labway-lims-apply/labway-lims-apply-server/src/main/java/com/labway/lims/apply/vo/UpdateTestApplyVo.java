package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class UpdateTestApplyVo extends TestApplyVo implements Serializable {

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 是否忽略同人同天同项目校验 0否1是
     */
    private Integer ignoreSameItem;

    /**
     * 是否忽略检验项目限制性别校验 true:忽略 false:校验
     */
    private Boolean ignoreItemLimitSex;

    /**
     * 项目信息
     */
    private List<UpdateTestApplyItemVo> testApplyItems;

    /**
     * 外部条码号
     */
    private String outBarcode;

    /**
     * 是否一次分拣之前， fasle复核之前，  true， 一次分拣之前
     */
    private boolean batchUpdateItem = false;
}
