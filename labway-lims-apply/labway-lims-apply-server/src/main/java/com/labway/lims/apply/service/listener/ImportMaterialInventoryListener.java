package com.labway.lims.apply.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import com.labway.lims.apply.vo.ImportErrorResponseVo;
import com.labway.lims.apply.vo.MaterialInventoryImportHeadVo;
import com.labway.lims.base.api.dto.MaterialDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.MaterialService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 导入物料库存
 * 
 * <AUTHOR>
 * @since 2023/10/27 19:10
 */
@Slf4j
public class ImportMaterialInventoryListener extends AnalysisEventListener<MaterialInventoryImportHeadVo> {
    private final SnowflakeService snowflakeService;
    private final LoginUserHandler.User loginUser;
    private final GroupService groupService;
    private final MaterialService materialService;
    private final MaterialInventoryService materialInventoryService;

    public ImportMaterialInventoryListener(SnowflakeService snowflakeService, LoginUserHandler.User loginUser,
        GroupService groupService, MaterialService materialService, MaterialInventoryService materialInventoryService) {
        this.snowflakeService = snowflakeService;
        this.loginUser = loginUser;
        this.groupService = groupService;
        this.materialService = materialService;
        this.materialInventoryService = materialInventoryService;
    }

    @Getter
    private final List<ImportErrorResponseVo> importErrorResponseVoList = new ArrayList<>();
    private final Map<Integer, MaterialInventoryImportHeadVo> excelDataMap = new HashMap<>();
    private final List<MaterialInventoryDto> addList = new ArrayList<>();

    @Override
    public void invoke(MaterialInventoryImportHeadVo data, AnalysisContext analysisContext) {
        ReadRowHolder readRowHolder = analysisContext.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex() + 1;

        // 检查数据 格式
        String errorMessage = validateData(data);

        if (StringUtils.isNotBlank(errorMessage)) {
            importErrorResponseVoList
                .add(ImportErrorResponseVo.builder().rowNo(rowIndex).errorInfo(errorMessage).build());
        } else {
            // 初步检查通过
            excelDataMap.put(rowIndex, data);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (CollectionUtils.isEmpty(excelDataMap.values())) {
            // 无 导入数据
            return;
        }

        final Set<String> groupNames =
            excelDataMap.values().stream().map(MaterialInventoryImportHeadVo::getGroupName).collect(Collectors.toSet());
        final Map<String,
            ProfessionalGroupDto> groupByGroupName = groupService.selectByGroupNames(groupNames).stream()
                .filter(obj -> Objects.equals(obj.getOrgId(), loginUser.getOrgId())).collect(
                    Collectors.toMap(ProfessionalGroupDto::getGroupName, Function.identity(), (key1, key2) -> key2));

        final Set<String> materialCodes = excelDataMap.values().stream()
            .map(MaterialInventoryImportHeadVo::getMaterialCode).collect(Collectors.toSet());

        final Map<String, MaterialDto> materialDtoByMaterialCode =
            materialService.selectByOrgIdAndMaterialCodes(loginUser.getOrgId(), materialCodes).stream()
                .collect(Collectors.toMap(MaterialDto::getMaterialCode, Function.identity(), (key1, ke2) -> key1));

        // 进一步检查
        for (Map.Entry<Integer, MaterialInventoryImportHeadVo> entry : excelDataMap.entrySet()) {
            Integer row = entry.getKey();
            MaterialInventoryImportHeadVo value = entry.getValue();
            ProfessionalGroupDto professionalGroupDto = groupByGroupName.get(value.getGroupName());
            MaterialDto materialDto = materialDtoByMaterialCode.get(value.getMaterialCode());
            StringBuilder errorMessage = new StringBuilder();
            if (Objects.isNull(professionalGroupDto)) {
                errorMessage.append("无效专业组名称;");
            }

            if (Objects.isNull(materialDto)) {
                errorMessage.append("无效物料编码;");
            }
            if (StringUtils.isNotBlank(errorMessage)) {
                importErrorResponseVoList
                    .add(ImportErrorResponseVo.builder().rowNo(row).errorInfo(errorMessage.toString()).build());
            }
        }

        if (CollectionUtils.isNotEmpty(importErrorResponseVoList)) {
            // 存在错误数据 相关 检查 失败
            return;
        }
        LinkedList<Long> genIds = snowflakeService.genIds(excelDataMap.values().size());
        for (MaterialInventoryImportHeadVo value : excelDataMap.values()) {
            ProfessionalGroupDto professionalGroupDto = groupByGroupName.get(value.getGroupName());
            MaterialDto materialDto = materialDtoByMaterialCode.get(value.getMaterialCode());

            MaterialInventoryDto add = new MaterialInventoryDto();
            add.setInventoryId(genIds.pop());
            add.setMaterialId(materialDto.getMaterialId());
            add.setMaterialCode(materialDto.getMaterialCode());
            add.setMaterialName(materialDto.getMaterialName());
            add.setSpecification(materialDto.getSpecification());
            add.setBatchNo(value.getBatchNo());
            add.setManufacturers(materialDto.getManufacturers());
            add.setMainUnit(materialDto.getMainUnit());
            add.setMainUnitInventory(new BigDecimal(value.getMainUnitInventory()));
            add.setAssistUnit(materialDto.getAssistUnit());
            add.setAssistUnitInventory(new BigDecimal(value.getAssistUnitInventory()));
            add.setUnitConversionRate(materialDto.getUnitConversionRate());
            add.setValidDate(getValidDate(value.getValidDate()));
            add.setGroupId(professionalGroupDto.getGroupId());
            add.setGroupName(professionalGroupDto.getGroupName());
            add.setOrgId(professionalGroupDto.getOrgId());
            add.setOrgName(professionalGroupDto.getGroupName());
            add.setCreateDate(new Date());
            add.setUpdateDate(new Date());
            add.setUpdaterId(loginUser.getUserId());
            add.setUpdaterName(loginUser.getUsername());
            add.setCreatorId(loginUser.getUserId());
            add.setCreatorName(loginUser.getUsername());
            add.setIsDelete(YesOrNoEnum.NO.getCode());
            addList.add(add);
        }

    }

    private Date getValidDate(String validDate) {
        Date date = null;
        try {
            if (StringUtils.isBlank(validDate) || Lists.newArrayList("/").contains(validDate)) {
                date = DateUtils.parseDate("2099-01-01", "yyyy-MM-dd");
            } else {
                date = DateUtils.parseDate(validDate, "yyyy-MM-dd", "yyyy/MM/dd");
            }
        } catch (Exception e) {

        }

        return date;
    }

    public List<MaterialInventoryDto> getAddTargetList() {
        return addList;
    }

    /**
     * 检查 导入数据
     */
    private String validateData(MaterialInventoryImportHeadVo data) {
        StringBuilder errorMessage = new StringBuilder();

        if (StringUtils.isBlank(data.getGroupName())) {
            errorMessage.append("专业组名称不能为空;");
        }
        if (StringUtils.isBlank(data.getMaterialCode())) {
            errorMessage.append("物料编码不能为空;");
        }
        if (StringUtils.isBlank(data.getMainUnitInventory())) {
            errorMessage.append("主数量不能为空;");
        }
        if (StringUtils.isBlank(data.getAssistUnitInventory())) {
            errorMessage.append("辅数量不能为空;");
        }
        if (StringUtils.isBlank(data.getBatchNo())) {
            errorMessage.append("批号不能为空;");
        }
        if (StringUtils.isNotBlank(data.getValidDate()) && !Lists.newArrayList("/").contains(data.getValidDate())) {
            try {
                DateUtils.parseDate(data.getValidDate(), "yyyy-MM-dd", "yyyy/HH/mm");
            } catch (Exception e) {

                errorMessage.append("有效期格式错误,可输入:[无] 或 [yyyy-MM-dd、yyyy/HH/mm] 格式时间");
            }
        }

        return errorMessage.toString();
    }
}
