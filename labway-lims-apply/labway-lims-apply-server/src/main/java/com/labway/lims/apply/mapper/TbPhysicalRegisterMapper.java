package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.model.TbPhysicalRegister;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 体检花名册 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbPhysicalRegisterMapper extends BaseMapper<TbPhysicalRegister> {

    /**
     * 批量 插入
     */
    void batchAddPhysicalRegisters(@Param("conditions") List<TbPhysicalRegister> conditions);

}
