package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.service.chain.pick.two.TwoPickBloodCultureCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickContext;
import com.labway.lims.apply.service.chain.pick.two.TwoPickUrgentCommand;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.EventListener;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 分拣
 */
@Slf4j
@Component
class MultiTwoPickCommand implements Command, Filter, InitializingBean, DisposableBean {

    /**
     * 部分分拣失败
     */
    private static final int PART_PICK_ERROR = 1003;

    @Resource
    private TwoPickCommand twoPickCommand;
    @Resource
    private TwoPickUrgentCommand twoPickUrgentCommand;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private TwoPickBloodCultureCommand twoPickBloodCultureCommand;


    @Override
    public boolean execute(Context c) throws Exception {

        final MultiTwoPickContext context = MultiTwoPickContext.from(c);
        final Map<Long, InstrumentGroupDto> instrumentGroups = context.getInstrumentGroups();
        final Map<Long, List<ApplySampleItemDto>> applySampleItems = context.getApplySampleItems();
        final List<ApplySampleDto> applySamples = context.getApplySamples();

        for (ApplySampleDto e : applySamples) {
            if (Objects.isNull(twoPickCommand.getSampleTwoPicker(applySampleItems.get(e.getApplySampleId()).iterator().next().getItemType()))) {
                throw new IllegalStateException(String.format("条码 [%s] 无法二次分拣此样本，因为无法识别项目类型", e.getBarcode()));
            }
        }


        context.put(MultiTwoPickContext.APPLY_SAMPLES_PICK_INFO, new CopyOnWriteArrayList<>());


        final LoginUserHandler.User user = LoginUserHandler.get();

        log.info("用户 [{}] 试管架 [{}] 开始批量二次分拣  样本数量 [{}] 条码 [{}]", user.getNickname(),
                context.getRackLogic().getRackCode(), applySamples.size(),
                applySamples.stream().map(ApplySampleDto::getBarcode).collect(Collectors.toList()));

        for (ApplySampleDto applySample : applySamples) {

            try {
                final InstrumentGroupDto instrumentGroup = instrumentGroups.get(applySample.getApplySampleId());

                // 对加急样本分拣
                final TwoPickContext tpc = new TwoPickContext(new TwoPickDto() {{
                    setApplySampleId(applySample.getApplySampleId());
                    setInstrumentId(context.getInstrumentId());
                }});

                tpc.put(TwoPickContext.APPLY_SAMPLE, applySample);
                tpc.put(TwoPickContext.APPLY_SAMPLE_ITEMS, context.getApplySampleItems().get(applySample.getApplySampleId()));
                tpc.put(TwoPickContext.INSTRUMENT_GROUP, instrumentGroup);


                // 如果需要加急处理
                if (twoPickUrgentCommand.isNeedUrgent(tpc)) {
                    twoPickUrgentCommand.execute(tpc);
                }


                // 如果是血培养
                if (twoPickBloodCultureCommand.isBloodCulture(tpc)) {
                    twoPickBloodCultureCommand.execute(tpc);
                }


                final ApplySampleTwoPickDto stp = new ApplySampleTwoPickDto();
                stp.setApplySampleId(applySample.getApplySampleId());
                stp.setApplyId(applySample.getApplyId());
                stp.setInstrumentGroupId(instrumentGroup.getInstrumentGroupId());
                stp.setInstrumentGroupName(instrumentGroup.getInstrumentGroupName());
                stp.setSampleNo(StringUtils.EMPTY);
                stp.setBarcode(applySample.getBarcode());
                stp.setGroupId(applySample.getGroupId());
                stp.setIsTransform(false);
                stp.setIsUrgent(false);


                tpc.getApplySampleTwoPicks().add(stp);

                // 对分出来的这个样本进行二次分拣
                twoPickCommand.execute(tpc);

                // 二次分拣完成再添加到上下文
                //                context.getApplySampleTwoPicks().add(stp);
                context.getApplySampleTwoPicks().addAll(tpc.getApplySampleTwoPicks());

            } catch (Exception e) {

                // 记录以下错误，等整个链条结束时抛出
                context.put(MultiTwoPickContext.EXCEPTION, new LimsCodeException(PART_PICK_ERROR, e.getMessage(), e));

                log.error("批量二次分拣时出现错误, 错误条码 [{}] 。将跳过后续的条码分拣，往后走流程。", applySample.getBarcode(), e);
                return CONTINUE_PROCESSING;
            }

        }


        return CONTINUE_PROCESSING;
    }


    @Override
    public boolean postprocess(Context c, Exception exception) {

        for (PostprocessCallback listener : MultiTwoPickContext.from(c).getListenerList().getListeners(PostprocessCallback.class)) {
            try {
                listener.postprocess(exception);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        return CONTINUE_PROCESSING;
    }

    @Override
    public void destroy() throws Exception {
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }

    private interface PostprocessCallback extends EventListener {
        void postprocess(Exception exception);
    }
}
