package com.labway.lims.apply.vo;

import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PrefabricateTestApplyVo extends InformationEntryTestApplyVo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 预制条码信息
     */
    private List<BarcodeInfo> barcodeInfos;


    @Getter
    @Setter
    public static class BarcodeInfo implements Serializable {

        /**
         * 条码号
         */
        private String barcode;

        /**
         * 管型
         */
        private String tubeCode;

        /**
         * 管型名称
         */
        private String tubeName;

    }
}
