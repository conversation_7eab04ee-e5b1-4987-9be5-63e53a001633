package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 血培养项目
 */
@Getter
@Setter
@TableName("tb_apply_sample_item_blood_culture")
public class TbApplySampleItemBloodCulture implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 血培养id
     */
    @TableId
    private Long applySampleItemBloodCultureId;

    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 申请单项目id
     */
    private Long applySampleItemId;

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * left upper limb anaerobic 左上肢厌氧
     */
    private Integer lulAnaerobic;

    /**
     * left upper limb aerobic 左上肢需氧气
     */
    private Integer lulAerobic;

    /**
     * left upper limb pediatric bottle  左上肢儿童瓶子
     */
    private Integer lulPediatricBottle;

    /**
     * left upper limb anaerobic 左下肢厌氧
     */
    private Integer lllAnaerobic;

    /**
     * left upper limb aerobic 左下肢需氧
     */
    private Integer lllAerobic;

    /**
     * left lower limb pediatric bottle  左下肢儿童瓶子
     */
    private Integer lllPediatricBottle;

    /**
     * right upper limb anaerobic 右下肢厌氧
     */
    private Integer rulAnaerobic;

    /**
     * right upper limb aerobic 右上肢需氧
     */
    private Integer rulAerobic;

    /**
     * right upper limb pediatric bottle 右上肢儿童瓶
     */
    private Integer rulPediatricBottle;

    /**
     * right lower limb anaerobic 右下肢厌氧
     */
    private Integer rllAnaerobic;

    /**
     * right lower limb aerobic 右下肢需氧
     */
    private Integer rllAerobic;

    /**
     * right lower limb pediatric bottle 右下肢儿童瓶
     */
    private Integer rllPediatricBottle;

    /**
     * 厌氧
     */
    private Integer anaerobic;

    /**
     * 需氧
     */
    private Integer aerobic;

    /**
     * 儿童瓶
     */
    private Integer pediatricBottle;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 是否删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
}
