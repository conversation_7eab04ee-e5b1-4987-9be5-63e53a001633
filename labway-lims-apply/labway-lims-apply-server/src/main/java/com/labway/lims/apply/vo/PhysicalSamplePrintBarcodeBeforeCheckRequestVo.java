package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <pre>
 * PhysicalSamplePrintBarcodeBeforeCheckRequestVo
 * 体检样本 打印条码前置判断
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/10 14:42
 */
@Getter
@Setter
public class PhysicalSamplePrintBarcodeBeforeCheckRequestVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 要做的体检套餐
     */
    private List<PhysicalSamplePrintBarcodeRequestVo> printBarcodeRequestVos;

}
