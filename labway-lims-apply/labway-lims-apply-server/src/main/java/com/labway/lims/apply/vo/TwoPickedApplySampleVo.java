package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.TwoPickedApplySampleDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 已经二次分拣日期
 */
@Getter
@Setter
public class TwoPickedApplySampleVo extends TwoPickedApplySampleDto {

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 检验项目
     */
    private List<String> testItemNames;
    /**
     * 二次分拣日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date twoPickDate;

    /**
     * 项目状态
     * @see SampleStatusEnum
     */
    private Integer status;
}
