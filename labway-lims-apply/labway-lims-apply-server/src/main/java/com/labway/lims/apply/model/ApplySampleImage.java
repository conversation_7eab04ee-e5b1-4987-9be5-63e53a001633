package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.io.Serializable;

/**
 * 申请单样本图片表(ApplySampleImage)实体类
 *
 * <AUTHOR>
 * @since 2025-03-04 11:14:16
 */
@Getter
@Setter
@TableName("tb_apply_sample_image")
public class ApplySampleImage implements Serializable {
    private static final long serialVersionUID = 551461037994768047L;
    /**
     * 申请单样本图片id
     */
    @TableId
    private Long applySampleImageId;
    /**
     * 申请单id
     */
    private Long applyId;
    /**
     * 申请单样本id
     */
    private Long applySampleId;
    /**
     * 图片地址
     */
    private String imageUrl;
    /**
     * 是否删除 0否1是
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 创建人id
     */
    private Long creatorId;
    /**
     * 跟新时间
     */
    private Date updateDate;
    /**
     * 更新人id
     */
    private Long updaterId;
    /**
     * 跟新人名称
     */
    private String updaterName;
    /**
     * 图片名称
     */
    private String imageName;
    /**
     * 创建人名称
     */
    private String creatorName;


}

