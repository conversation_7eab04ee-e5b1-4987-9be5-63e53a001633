package com.labway.lims.apply.service.chain.material.inventory.start;

import com.google.common.collect.Lists;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.MaterialInventoryCheckStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.MaterialInventoryCheckDetailDto;
import com.labway.lims.apply.api.dto.MaterialInventoryCheckDto;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.service.MaterialInventoryCheckDetailService;
import com.labway.lims.apply.api.service.MaterialInventoryCheckService;
import com.labway.lims.apply.mapstruct.MaterialInventoryCheckDetailConverter;
import com.labway.lims.apply.vo.utils.MaterialNoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * 创建 盘点记录
 *
 * <AUTHOR>
 * @since 2023/5/11 19:13
 */
@Slf4j
@Component
public class MaterialInventoryStartCheckAddCheckRecordCommand implements Command {

    @Resource
    private MaterialInventoryCheckService materialInventoryCheckService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private MaterialNoUtils materialNoUtils;

    @Resource
    private MaterialInventoryCheckDetailService materialInventoryCheckDetailService;

    @Resource
    private MaterialInventoryCheckDetailConverter materialInventoryCheckDetailConverter;

    @Override
    public boolean execute(Context context) throws Exception {
        final MaterialInventoryStartCheckContext from = MaterialInventoryStartCheckContext.from(context);
        var user = from.getUser();
        var checkTime = from.getCheckTime();
        var materialInventoryDtos = from.getMaterialInventoryDtos();

        // 本次生成的盘点单号
        String checkNo = materialNoUtils.genCheckNo(user.getOrgId());

        // 需要的ids
        LinkedList<Long> genIds = snowflakeService.genIds(materialInventoryDtos.size() + 1);

        MaterialInventoryCheckDto checkDto = new MaterialInventoryCheckDto();
        checkDto.setCheckId(genIds.pop());
        checkDto.setCheckNo(checkNo);
        checkDto.setCheckTime(checkTime);
        checkDto.setGroupId(user.getGroupId());
        checkDto.setGroupName(user.getGroupName());
        checkDto.setStatus(MaterialInventoryCheckStatusEnum.IN_CHECK.getCode());

        Date date = new Date();
        List<MaterialInventoryCheckDetailDto> checkDetailList =
            Lists.newArrayListWithCapacity(materialInventoryDtos.size());
        for (MaterialInventoryDto inventoryDto : materialInventoryDtos) {

            MaterialInventoryCheckDetailDto checkDetailDto =
                materialInventoryCheckDetailConverter.materialInventoryCheckDetailDtoFromTbObjDto(inventoryDto);
            checkDetailDto.setDetailId(genIds.pop());
            checkDetailDto.setCheckId(checkDto.getCheckId());
            checkDetailDto.setCheckNo(checkNo);
            checkDetailDto.setMainInventory(inventoryDto.getMainUnitInventory());
            checkDetailDto.setActualMainInventory(BigDecimal.ZERO);
            checkDetailDto.setMainProfit(BigDecimal.ZERO);
            checkDetailDto.setAssistInventory(inventoryDto.getAssistUnitInventory());
            checkDetailDto.setActualAssistInventory(BigDecimal.ZERO);
            checkDetailDto.setAssistProfit(BigDecimal.ZERO);
            checkDetailDto.setGroupId(user.getGroupId());
            checkDetailDto.setGroupName(user.getGroupName());

            checkDetailDto.setOrgId(user.getOrgId());
            checkDetailDto.setOrgName(user.getOrgName());
            checkDetailDto.setCreateDate(date);
            checkDetailDto.setUpdateDate(date);
            checkDetailDto.setCreatorId(user.getUserId());
            checkDetailDto.setCreatorName(user.getNickname());
            checkDetailDto.setUpdaterId(user.getUserId());
            checkDetailDto.setUpdaterName(user.getNickname());
            checkDetailDto.setIsDelete(YesOrNoEnum.NO.getCode());

            // 需要新增的 盘点详情
            checkDetailList.add(checkDetailDto);

        }

        // 添加 盘点记录
        materialInventoryCheckService.addMaterialInventoryCheck(checkDto);

        // 添加 盘点详情
        materialInventoryCheckDetailService.addMaterialInventoryCheckDetailDtos(checkDetailList);

        // 删除 当前专业组是否存在盘点中记录缓存
        materialInventoryCheckService.removeIsCheckCache(user.getGroupId());

        return CONTINUE_PROCESSING;
    }

}
