package com.labway.lims.apply.model.pda;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 申请单
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
@TableName("tb_pda_apply")
public class TbPdaApply implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    /**
    * pda申请单ID
    */
    private Long pdaApplyId;

    /**
    * 主条码号
    */
    private String masterBarcode;

    /**
    * 病人名称
    */
    private String patientName;

    /**
    * 病人年龄
    */
    private Integer patientAge;

    /**
    * 子年龄
    */
    private Integer patientSubage;

    /**
    * 子年龄单位,月、周、日
    */
    private String patientSubageUnit;

    /**
    * 生日
    */
    private Date patientBirthday;

    /**
    * 卡号
    */
    private String patientCard;

    /**
    * 类型,1:身份证
    */
    private String patientCardType;

    /**
    * 床号
    */
    private String patientBed;

    /**
    * 性别,1:男,2:女
    */
    private Integer patientSex;

    /**
    * 就诊卡号
    */
    private String patientVisitCard;

    /**
    * 手机号
    */
    private String patientMobile;

    /**
    * 地址
    */
    private String patientAddress;

    /**
    * 来源
    */
    private String source;

    /**
    * 供应商
    */
    private String supplier;

    /**
    * 就诊类型
    */
    private String applyTypeCode;

    /**
    * 备注
    */
    private String remark;

    /**
    * 样本数量
    */
    private Integer sampleCount;

    /**
    * 样本性状
    */
    private String sampleProperty;

    /**
    * 部门
    */
    private String dept;

    /**
    * 临床诊断
    */
    private String diagnosis;

    /**
    * 送检医生
    */
    private String sendDoctorName;

    /**
    * 送检医生编码
    */
    private String sendDoctorCode;

    /**
    * 送检机构ID
    */
    private Long hspOrgId;

    /**
    * 送检机构编码
    */
    private String hspOrgCode;

    /**
    * 送检机构名称
    */
    private String hspOrgName;

    /**
    * 机构ID
    */
    private Long orgId;

    /**
    * 机构名称
    */
    private String orgName;

    /**
    * 是否急诊,1:是,0:不是
    */
    private Integer urgent;

    /**
    * 申请日期
    */
    private Date applyDate;

    /**
    * 采样时间
    */
    private Date samplingDate;

    /**
    * 创建时间
    */
    private Date createDate;

    /**
    * 更新时间
    */
    private Date updateDate;

    /**
    * 创建人
    */
    private String creatorName;

    /**
    * 创建人
    */
    private Long creatorId;

    /**
    * 更新人
    */
    private String updaterName;

    /**
    * 更新人
    */
    private Long updaterId;

    /**
    * 1:删除,0:没有删除
    */
    private Integer isDelete;

    /**
    * 样本性状编码
    */
    private String samplePropertyCode;

    /**
    * 就诊类型名称
    */
    private String applyTypeName;

    /**
    * 原始机构编码
    */
    private String originalOrgCode;

    /**
    * 原始机构名称
    */
    private String originalOrgName;

    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * pda图片列表
     */
    private String pdaImgs;

}