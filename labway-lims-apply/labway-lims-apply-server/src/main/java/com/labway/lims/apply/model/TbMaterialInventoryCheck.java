package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.apply.MaterialInventoryCheckStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 物料盘点
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Getter
@Setter
@TableName("tb_material_inventory_check")
public class TbMaterialInventoryCheck implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 盘点ID
     */
    @TableId
    private Long checkId;

    /**
     * 盘点单号
     */
    private String checkNo;

    /**
     * 盘点时间
     */
    private Date checkTime;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;
    /**
     * 状态
     *
     * @see MaterialInventoryCheckStatusEnum
     */
    private Integer status;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;


}
