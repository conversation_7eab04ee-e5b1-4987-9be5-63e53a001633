package com.labway.lims.apply.service.chain.pick.two;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * redis 标记一下
 */
@Slf4j
@Component
public class TwoPickRedisMarkCommand implements Command {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @Override
    public boolean execute(Context c) throws Exception {
        final TwoPickContext context = TwoPickContext.from(c);

        // 批量标记
        mark(context.getApplySampleTwoPicks());


        return CONTINUE_PROCESSING;
    }


    public void mark(ApplySampleTwoPickDto stp) {
        mark(List.of(stp));
    }


    /**
     * 标记样本已经二次分拣了
     * @param stps 二次分拣的样本
     */
    public void mark(List<ApplySampleTwoPickDto> stps) {

        final Map<String, String> map = new HashMap<>(stps.size());
        for (ApplySampleTwoPickDto stp : stps) {
            map.put(getRedisKey(stp.getApplySampleId()), JSON.toJSONString(stp));
        }

        // 永久缓存 当取消二次分拣的时候会被移除
        stringRedisTemplate.opsForValue().multiSet(map);
    }

    /**
     * 取消二次分拣标记
     */
    public void unmark(long applySampleId) {
        stringRedisTemplate.delete(getRedisKey(applySampleId));
    }

    public String getRedisKey(long applySampleId) {
        return redisPrefix.getBasePrefix() + "APPLY_SAMPLE_TWO_PICKED:" + applySampleId;
    }
}
