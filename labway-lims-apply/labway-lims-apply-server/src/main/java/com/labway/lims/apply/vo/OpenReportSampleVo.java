package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 提供给报告平台的接口
 */
@Getter
@Setter
public class OpenReportSampleVo {

    private Long applySampleId;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * orgId
     */
    private Long orgId;

    /**
     * orgName
     */
    private String orgName;

    /**
     * hspOrgName
     */
    private String hspOrgName;

    /**
     * hspOrgCode
     */
    private String hspOrgCode;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 样本ID
     */
    private Long sampleId;

    /**
     * 外部条码号
     */
    private String outBarcode;

    /**
     * 管型
     */
    private String tubeName;
    /**
     * 管型CODE
     */
    private String tubeCode;

    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;


    /**
     * 1: 急诊 0:不急
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 检验人ID
     */
    private Long testerId;
    /**
     * 检验人姓名
     */
    private String testerName;

    /**
     * 项目类型ID
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 专业小组
     */
    private String instrumentGroupName;

    /**
     * 仪器信息
     */
    private Long instrumentId;
    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 检验日期，暂定二次分拣日期
     */
    private Date testDate;

    /**
     * 一次审核
     */
    private Long oneCheckerId;

    /**
     * 一次审核人名称
     */
    private String oneCheckerName;
    /**
     * 一审时间
     */
    private Date oneCheckDate;
    /**
     * 二次审核人
     */
    private Long twoCheckerId;
    /**
     * 二次审核人名称
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    private Date twoCheckDate;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 检验项目
     */
    private List<TestItem> testItems;

    /**
     * 报告项目及结果
     */
    private List<Result> results;

    /**
     * 报告地址
     */
    private String reportUrl;

    /**
     * 样本备注
     */
    @Compare("样本备注")
    private String sampleRemark;

    /**
     * 结果备注
     */
    @Compare("结果备注")
    private String resultRemark;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 样本图片地址
     */
    private String imgUrls;

    @Getter
    @Setter
    public static class TestItem {
        /**
         * lims 检验项目编码
         */
        private String testItemCode;

        /**
         * lims 检验项目名称
         */
        private String testItemName;

        /**
         * 外部 检验项目编码
         */
        private String outTestItemCode;

        /**
         * 外部 检验项目名称
         */
        private String outTestItemName;
    }

    @Getter
    @Setter
    public static class Result {

        /**
         * 外部 检验项目编码
         */
        private String outTestItemCode;

        /**
         * 外部 检验项目名称
         */
        private String outTestItemName;

        /**
         * 报告项目编码
         */
        private String reportItemCode;

        /**
         * 报告项目
         */
        private String reportItemName;

        /**
         * 检验项目
         */
        private String testItemCode;

        /**
         * 检验项目
         */
        private String testItemName;


        /**
         * 结果类型，数值、图片等
         */
        private String type;

        /**
         * 这个结果可能是经过格式化或计算过的
         */
        private String result;

        /**
         * 结果单位
         */
        private String unit;

        /**
         * 范围
         */
        private String range;

        /**
         * 1: 危机
         * 2: 异常
         * 0: 正常
         *
         * @see ResultStatusEnum
         */
        private Integer status;


        /**
         * UP or DOWN
         *
         * @see TestJudgeEnum
         */
        private String judge;

        /**
         * 打印顺序，越小越靠前
         */
        private Integer printSort;

	    /**
	     * 参考值上限
	     */
		private String referValueMax;

	    /**
	     * 参考值下限
	     */
		private String referValueMin;

	    /**
	     * 方法学
	     */
		private String examMethodName;

    }

}
