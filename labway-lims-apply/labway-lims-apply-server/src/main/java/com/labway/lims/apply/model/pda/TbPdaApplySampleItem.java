package com.labway.lims.apply.model.pda;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;
import java.util.List;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * @description pda申请单样本项目
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
@TableName("tb_pda_apply_sample_item")
public class TbPdaApplySampleItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请单样本项目ID
     */
    @TableId
    private Long pdaApplySampleItemId;

    /**
    * 申请单ID
    */
    private Long pdaApplyId;

    /**
    * 检验项目ID
    */
    private Long testItemId;

    /**
    * 检验项目编码
    */
    private String testItemCode;

    /**
    * 检验项目
    */
    private String testItemName;

    /**
    * 项目类型
    */
    private String itemType;

    /**
    * 样本类型
    */
    private String sampleTypeName;

    /**
    * 样本类型编码
    */
    private String sampleTypeCode;

    /**
    * 管型
    */
    private String tubeName;

    /**
    * 管型编码
    */
    private String tubeCode;

    /**
    * 分隔码
    */
    private String splitCode;

    /**
    * 1:急 0:不急
    */
    private Integer urgent;

    /**
    * 数量
    */
    @TableField("\"count\"")
    private Integer count;

    /**
    * 专业组id
    */
    private Long groupId;

    /**
    * 专业组
    */
    private String groupName;

    /**
    * 创建时间
    */
    private Date createDate;

    /**
    * 修改时间
    */
    private Date updateDate;

    /**
    * 创建人id
    */
    private Long creatorId;

    /**
    * 创建人名称
    */
    private String creatorName;

    /**
    * 修改人id
    */
    private Long updaterId;

    /**
    * 修改人名称
    */
    private String updaterName;

    /**
    * 1:删除,0:不删
    */
    private Integer isDelete;

    /**
    * 备注
    */
    private String remark;

    /**
    * 血培养项目信息 json
    */
    private String bloodcultureItem;

}