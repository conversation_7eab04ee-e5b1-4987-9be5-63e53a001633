package com.labway.lims.apply.controller;

import com.labway.lims.api.enums.apply.MaterialApplyStatusEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApprovalPlanDto;
import com.labway.lims.apply.api.dto.QueryAllUnApprovalPlanListDto;
import com.labway.lims.apply.api.dto.QueryApprovalPlanPageDto;
import com.labway.lims.apply.api.dto.ReturnPlanDto;
import com.labway.lims.apply.api.enums.MaterialApplyTypeEnum;
import com.labway.lims.apply.api.service.GroupMaterialApplyService;
import com.labway.lims.apply.api.service.GroupMaterialPlanService;
import com.labway.lims.apply.api.vo.ApprovalPlanVo;
import com.labway.lims.apply.api.vo.GroupMaterialPlanVo;
import com.labway.lims.apply.api.vo.ReturnPlanVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/5/8 17:13
 */
@Slf4j
@RestController
@RequestMapping("/material/approve")
public class GroupMaterialPlanApproveController extends BaseController {

    @Resource
    private GroupMaterialPlanService groupMaterialPlanService;
    @Resource
    private GroupMaterialApplyService groupMaterialApplyService;


    /**
     * 查询待审批的专业组计划列表
     */
    @PostMapping("/queryAllUnApprovalPlanList")
    public List<GroupMaterialPlanVo> queryAllUnApprovalPlanList(@RequestBody @Valid QueryAllUnApprovalPlanListDto queryAllUnApprovalPlanListDto) {
        return groupMaterialPlanService.queryAllUnApprovalPlanList(queryAllUnApprovalPlanListDto);
    }

    /**
     * 审核专业组计划
     */
    @PostMapping("/approvalPlan")
    public ApprovalPlanVo approvalPlan(@RequestBody @Valid ApprovalPlanDto approvalPlanDto) {
        return groupMaterialPlanService.approvalPlan(approvalPlanDto);
    }

    /**
     * 退回专业组计划
     */
    @PostMapping("/returnPlan")
    public ReturnPlanVo returnPlan(@RequestBody @Valid ReturnPlanDto returnPlanDto) {
        return groupMaterialPlanService.returnPlan(returnPlanDto);
    }


    /**
     * 已审批专业组计划列表
     */
    @PostMapping("/queryApprovalPlanPage")
    public List<GroupMaterialPlanVo> queryApprovalPlanPage(@RequestBody @Valid QueryApprovalPlanPageDto queryApprovalPlanPageDto) {
        if (Objects.equals(MaterialApplyTypeEnum.getByCode(queryApprovalPlanPageDto.getMaterialApplyType()), MaterialApplyTypeEnum.MATERIAL_PLAN)) {
            return groupMaterialPlanService.queryApprovalPlanPage(queryApprovalPlanPageDto);
        } else {
            final List<String> materialStatus = List.of(String.valueOf(MaterialApplyStatusEnum.PART_OUT.getCode()),
                    String.valueOf(MaterialApplyStatusEnum.ALL_OUT.getCode()),
                    String.valueOf(MaterialApplyStatusEnum.AUDIT.getCode()),
                    String.valueOf(MaterialApplyStatusEnum.ROLLBACK.getCode()),
                    String.valueOf(MaterialApplyStatusEnum.REJECT.getCode()));
            queryApprovalPlanPageDto.setStatus(materialStatus);
            return groupMaterialApplyService.queryApprovalPlanPage(queryApprovalPlanPageDto);
        }
    }


}
