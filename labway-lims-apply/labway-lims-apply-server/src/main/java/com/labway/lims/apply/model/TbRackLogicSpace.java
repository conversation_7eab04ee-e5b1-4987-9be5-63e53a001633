package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 逻辑试管架占用
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_rack_logic_space")
public class TbRackLogicSpace implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    private Long rackLogicSpaceId;

    /**
     * 逻辑试管架
     */
    private Long rackLogicId;

    /**
     * 试管架
     */
    private Long rackId;

    /**
     * 多少行
     */
    @TableField("\"row\"")
    private Integer row;

    /**
     * 多少列
     */
    @TableField("\"column\"")
    private Integer column;

    /**
     * 放置的申请单样本
     */
    private Long applySampleId;

    /**
     * 1: 已使用 0:可使用 2: 不可使用
     */
    private Integer status;

    /**
     * 1 已删除 0 未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

}
