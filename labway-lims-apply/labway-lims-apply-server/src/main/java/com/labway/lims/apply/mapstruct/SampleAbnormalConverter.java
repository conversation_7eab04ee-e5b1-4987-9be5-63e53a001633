package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.model.TbSampleAbnormal;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 样本异常 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 19:21
 */
@Mapper(componentModel = "spring")
public interface SampleAbnormalConverter {

    SampleAbnormalDto sampleAbnormalDtoFromTbObj(TbSampleAbnormal obj);

    List<SampleAbnormalDto> sampleAbnormalDtoListFromTbObj(List<TbSampleAbnormal> list);
}
