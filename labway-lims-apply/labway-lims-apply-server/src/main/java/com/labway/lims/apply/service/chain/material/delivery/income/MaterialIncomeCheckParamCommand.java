package com.labway.lims.apply.service.chain.material.delivery.income;

import com.labway.lims.api.enums.apply.MaterialDeliveryRecordStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryIncomeItemDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryRecordDto;
import com.labway.lims.apply.api.service.MaterialDeliveryDetailService;
import com.labway.lims.apply.api.service.MaterialDeliveryRecordService;
import com.labway.lims.apply.api.service.MaterialInventoryCheckService;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料入库检查参数
 *
 * <AUTHOR>
 * @since 2023/5/9 10:06
 */
@Slf4j
@Component
public class MaterialIncomeCheckParamCommand implements Command {

    @Resource
    private MaterialDeliveryRecordService materialDeliveryRecordService;
    @Resource
    private MaterialDeliveryDetailService materialDeliveryDetailService;
    @DubboReference
    private GroupMaterialService groupMaterialService;

    @Resource
    private MaterialInventoryCheckService materialInventoryCheckService;

    @Override
    public boolean execute(Context context) throws Exception {

        final MaterialIncomeContext from = MaterialIncomeContext.from(context);
        var deliveryNo = from.getDeliveryNo();
        var incomeItemList = from.getIncomeItemList();
        var user = from.getUser();
        if (Objects.isNull(deliveryNo) || CollectionUtils.isEmpty(incomeItemList)) {
            throw new IllegalArgumentException("存在必填项未填写");
        }

        String check = materialInventoryCheckService.isCheck(user.getGroupId());
        if (StringUtils.isNotBlank(check)) {
            throw new LimsException(String.format("入库失败: [%s] 存在盘点中记录", check));
        }

        if (incomeItemList.stream().anyMatch(obj -> Objects.isNull(obj.getDetailId())
            || Objects.isNull(obj.getIncomeMainNumber()) || Objects.isNull(obj.getIncomeAssistNumber()))) {
            throw new LimsException("入库物料信息存在必填项未填写");
        }

        // 获取 待入库记录
        final MaterialDeliveryRecordDto deliveryRecordDto =
            materialDeliveryRecordService.selectByDeliveryNo(deliveryNo, user.getOrgId());
        if (Objects.isNull(deliveryRecordDto)) {
            throw new LimsException(String.format("出库单号 [%s] 对应待入库记录不存在", deliveryNo));
        }

        if (Objects.equals(deliveryRecordDto.getStatus(), MaterialDeliveryRecordStatusEnum.WAREHOUSED.getCode())) {
            throw new LimsException(String.format("出库单号 [%s] 对应记录已入库,不可重复入库", deliveryNo));
        }

        // 获取 待入库记录 对应出库物料详情
        final List<MaterialDeliveryDetailDto> deliveryDetailDtoList =
            materialDeliveryDetailService.selectByDeliveryNo(deliveryNo, user.getOrgId());

        // 出库详情转map key: 详情id value：详情信息
        final Map<Long, MaterialDeliveryDetailDto> deliveryDetailDtoMap = deliveryDetailDtoList.stream()
            .collect(Collectors.toMap(MaterialDeliveryDetailDto::getDetailId, Function.identity()));

        // 对应物料详情id
        if (incomeItemList.stream().anyMatch(obj -> !deliveryDetailDtoMap.containsKey(obj.getDetailId()))) {
            throw new LimsException(String.format("存在不属于出库单号 [%s] 下的物料信息", deliveryNo));
        }

        // key: 入库物料信息id value: 入库物料信息
        final Map<Long, MaterialDeliveryIncomeItemDto> incomeItemByDetailId = incomeItemList.stream()
            .collect(Collectors.toMap(MaterialDeliveryIncomeItemDto::getDetailId, Function.identity()));

        deliveryDetailDtoList.forEach(item -> {
            MaterialDeliveryIncomeItemDto incomeItemDto = incomeItemByDetailId.get(item.getDetailId());
            if (Objects.isNull(incomeItemDto)) {
                // 前端 未传入本物料入库信息 ？？？ 暂时认为入库数量 0
                return;
            }
            boolean flag = false;

			if (Objects.nonNull(item.getDeliveryMainNumberDecimal()) && Objects.nonNull(incomeItemDto.getIncomeMainNumber())) {
				flag = Objects.equals(incomeItemDto.getIncomeMainNumber().compareTo(item.getDeliveryMainNumberDecimal()), 1);
			}

            // 入库主单位数量 大于出库单位主数量
            if (Objects.equals(incomeItemDto.getIncomeAssistNumber().compareTo(item.getDeliveryAssistNumber()), 1)) {
                // 入库辅单位数量 大于出库辅单位数量
                flag = true;
            }
            if (flag) {
                throw new LimsException(String.format("[%s] 物料入库数量大于出库数量，请重新输入", item.getMaterialName()));
            }
        });

        // 所有入库 物料id
        final Set<Long> incomeMaterialIds = incomeItemList.stream()
            .map(obj -> deliveryDetailDtoMap.get(obj.getDetailId()).getMaterialId()).collect(Collectors.toSet());

        // 对应 专业组物料 关联信息
        final List<GroupMaterialDto> groupMaterialDtos =
            groupMaterialService.selectByGroupIdAndMaterialIds(user.getGroupId(), incomeMaterialIds);

        // 查询的物料id
        final Set<Long> selectMaterialIdList =
            groupMaterialDtos.stream().map(GroupMaterialDto::getMaterialId).collect(Collectors.toSet());

        if (incomeItemList.stream().anyMatch(
            obj -> !selectMaterialIdList.contains(deliveryDetailDtoMap.get(obj.getDetailId()).getMaterialId()))) {
            throw new LimsException("入库失败:入库物料存在非本专业组下物料");
        }

        // 补充物料待入库记录 以及其物料信息
        from.put(MaterialIncomeContext.MATERIAL_DELIVERY_RECORD, deliveryRecordDto);
        from.put(MaterialIncomeContext.MATERIAL_DELIVERY_DETAIL, deliveryDetailDtoList);
        from.put(MaterialIncomeContext.GROUP_MATERIAL, groupMaterialDtos);

        return CONTINUE_PROCESSING;
    }
}
