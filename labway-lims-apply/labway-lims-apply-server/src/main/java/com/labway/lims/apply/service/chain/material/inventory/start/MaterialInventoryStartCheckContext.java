package com.labway.lims.apply.service.chain.material.inventory.start;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.Date;
import java.util.List;

/**
 * 开始盘点 库存 上下文信息
 * 
 * <AUTHOR>
 * @since 2023/5/11 17:11
 */
@Getter
@Setter
public class MaterialInventoryStartCheckContext extends StopWatchContext {

    /**
     * 盘点时间
     */
    private Date checkTime;
    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    public static MaterialInventoryStartCheckContext from(Context context) {
        return (MaterialInventoryStartCheckContext)context;
    }



    // 对应 库存 物料信息
    public static final String MATERIAL_INVENTORY = "MATERIAL_INVENTORY_" + IdUtil.objectId();

    public List<MaterialInventoryDto> getMaterialInventoryDtos() {
        return (List<MaterialInventoryDto>)get(MATERIAL_INVENTORY);
    }


    @Override
    protected String getWatcherName() {
        return "开始盘点";
    }
}
