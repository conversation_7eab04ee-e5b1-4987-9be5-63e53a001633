package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 库存管理 物料列表 显示 Vo
 * 
 * <AUTHOR>
 * @since 2023/5/10 18:05
 */
@Getter
@Setter
public class MaterialInventoryListResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物料类型
     */
    private String type;

    /**
     * 物资编号
     */
    private String materialCode;

    /**
     * 物资名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;
    /**
     * 厂家
     */
    private String manufacturers;
    /**
     * 主单位
     */
    private String mainUnit;
    /**
     * 辅单位
     */
    private String assistUnit;
    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;
    /**
     * 主单位库存 汇总
     */
    private BigDecimal mainUnitInventorySum;

    /**
     * 辅单位库存 汇总
     */
    private BigDecimal assistUnitInventorySum;

    /**
     * 临近标签 true:临近 false:未临近 汇总
     */
    private boolean proximityFlag;
    /**
     * 过期标签 true:临近 false:未临近
     */
    private boolean expiredFlag;

    /**
     * 不同批号信息
     */
    private List<MaterialInventoryItemVo> inventoryItemVoList;

    /**
     * 储存条件
     */
    private String storageTemperature;

    /**
     * 库存数量
     */
    private BigDecimal inventorySum;

    /**
     * 临近效期天数
     */
    private Integer validRemindDay;

    /**
     * 库存上限
     */
    private BigDecimal inventoryUpperLimit;

    /**
     * 库存下限
     */
    private BigDecimal inventoryLowerLimit;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 注册证名称
     */
    private String registrationName;

    /**
     * 不同批号信息
     */
    @Getter
    @Setter
    public static class MaterialInventoryItemVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 库存ID
         */
        private Long inventoryId;
        /**
         * 批号
         */
        private String batchNo;
        /**
         * 有效期
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
        private Date validDate;
        /**
         * 主单位
         */
        private String mainUnit;

        /**
         * 主单位库存
         */
        private BigDecimal mainUnitInventory;

        /**
         * 辅单位
         */
        private String assistUnit;

        /**
         * 辅单位库存
         */
        private BigDecimal assistUnitInventory;

        /**
         * 临近标签 true:临近 false:未临近
         */
        private boolean proximityFlag;
        /**
         * 过期标签 true:临近 false:未临近
         */
        private boolean expiredFlag;
        /**
         * 物料条码号
         */
        private String materialBarcode;

        /**
         * 库存数量
         */
        private BigDecimal inventorySum;
    }
}
