package com.labway.lims.apply.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.AddGroupMaterialPlanDto;
import com.labway.lims.apply.api.dto.DeleteGroupMaterialDto;
import com.labway.lims.apply.api.dto.GroupMaterialPlanDto;
import com.labway.lims.apply.api.dto.SelectGroupMaterialPlanDto;
import com.labway.lims.apply.api.dto.UpdateGroupMaterialPlanDto;
import com.labway.lims.apply.api.service.GroupMaterialPlanService;
import com.labway.lims.pdfreport.api.dto.DefaultExpiresDate;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description 物料专业组计划控制器
 * <AUTHOR>
 * @date 2024-07-10
 */
@Slf4j
@RestController
@RequestMapping("/material-group-plan")
public class GroupMaterialPlanController extends BaseController {

    @Resource
    private GroupMaterialPlanService groupMaterialPlanService;
    @DubboReference
    private PdfReportService pdfReportService;

    /**
     * 新增
     */
    @PostMapping("/add")
    public Object add(@RequestBody List<AddGroupMaterialPlanDto> addGroupMaterialPlanDtoList) {

        // 校验参数
        this.checkMaterialPlan(addGroupMaterialPlanDtoList);

        return groupMaterialPlanService.addGroupMaterialPlan(addGroupMaterialPlanDtoList);
    }


    /**
     * 保存更新， 根据计划单号
     */
    @PostMapping("/save")
    public Object save(@RequestBody UpdateGroupMaterialPlanDto dto) {
        checkParams(dto.getPlanNo());
        // 校验参数
        this.checkMaterialPlan(dto.getAddGroupMaterialPlanDtoList());

        return groupMaterialPlanService.updateGroupMaterialPlanByPlanNo(dto.getPlanNo(), dto.getAddGroupMaterialPlanDtoList());

    }


    /**
     * 保存更新， 根据计划单号
     */
    @PostMapping("/delete-material")
    public Object deleteMaterial(@RequestBody DeleteGroupMaterialDto dto) {
        checkParams(dto.getPlanNo());
        // 校验参数
        if (CollectionUtils.isEmpty(dto.getMaterialCodeList())) {
            throw new IllegalArgumentException("物料编码不能为空");
        }

        return groupMaterialPlanService.deleteMaterialByPlanNo(dto.getPlanNo(), dto.getMaterialCodeList());
    }

    /**
     * 删除
     */
    @PostMapping("/delete/by-planNo")
    public Object deleteByPlanNo(@RequestBody Map<String, String> map) {
        final String planNo = map.get("planNo");
        this.checkParams(planNo);
        return groupMaterialPlanService.deleteByPlanNo(planNo);
    }

    private void checkParams(String planNo) {
        if (StringUtils.isBlank(planNo)) {
            throw new IllegalArgumentException("计划单号不能为空");
        }
    }

    /**
     * 根据单号查询
     */
    @GetMapping("/select/by-planNo")
    public Object selectByPlanNo(@RequestParam("planNo") String planNo) {
        return groupMaterialPlanService.selectByPlanNo(planNo);
    }


    /**
     * 根据专业组id查询
     */
    @PostMapping("/select/group")
    public Object selectByGroup(@RequestBody SelectGroupMaterialPlanDto dto) {
        dto.setGroupId(LoginUserHandler.get().getGroupId());
        return groupMaterialPlanService.selectAll(dto);
    }

    /**
     * 提交
     */
    @PostMapping("/submit-plan")
    public Object submitPlan(@RequestBody Map<String, String> map) {
        final String planNo = map.get("planNo");
        this.checkParams(planNo);
        groupMaterialPlanService.submitPlan(planNo);
        return Map.of();
    }

    /**
     * 撤销提交
     */
    @PostMapping("/repeal-submit-plan")
    public Object repealSubmitPlan(@RequestBody Map<String, String> map) {
        final String planNo = map.get("planNo");
        this.checkParams(planNo);
        groupMaterialPlanService.repealSubmitPlan(planNo);
        return Map.of();
    }

    /**
     * 撤销提交
     */
    @PostMapping("/print")
    public Object print(@RequestBody Map<String, String> map) {
        final String planNo = map.get("planNo");
        this.checkParams(planNo);
        final List<GroupMaterialPlanDto> groupMaterialPlanDtos = groupMaterialPlanService.selectByPlanNo(planNo);
        String url = Strings.EMPTY;
        if (CollectionUtils.isNotEmpty(groupMaterialPlanDtos)) {
            // 计划信息
            final GroupMaterialPlanDto groupMaterialPlanDto = groupMaterialPlanDtos.get(NumberUtils.INTEGER_ZERO);
            // 打印PDF参数
            final PdfReportParamDto paramDto = new PdfReportParamDto();

            // 计划信息
            final HashMap<String, Object> planInfoMap = new HashMap<>();
            planInfoMap.put("planNo", groupMaterialPlanDto.getPlanNo());
            planInfoMap.put("plannerName", groupMaterialPlanDto.getPlannerName());
            planInfoMap.put("planDate", groupMaterialPlanDto.getPlannerDate());
            paramDto.put("groupName", groupMaterialPlanDto.getGroupName());

            paramDto.put("plan", planInfoMap);
            paramDto.put("details", groupMaterialPlanDtos);
            url = pdfReportService.build2Url(PdfTemplateTypeEnum.GROUP_MATERIAL_PLAN.getCode(), paramDto, DefaultExpiresDate.WEEK_TWO);
        }
        return Map.of("url", url);
    }


    private void checkMaterialPlan(List<AddGroupMaterialPlanDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new IllegalArgumentException("物料信息不能为空");
        }
        // 校验参数
        list.forEach(AddGroupMaterialPlanDto::verify);
        list.stream()
                .map(AddGroupMaterialPlanDto::getMaterialCode)
                // 根据code分组， value是出现的次数
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .forEach((materialCode, size) -> {
                    if (size > NumberUtils.LONG_ONE) {
                        throw new IllegalArgumentException(String.format("物料 [%s] 存在多条记录", materialCode));
                    }
                });
    }

}