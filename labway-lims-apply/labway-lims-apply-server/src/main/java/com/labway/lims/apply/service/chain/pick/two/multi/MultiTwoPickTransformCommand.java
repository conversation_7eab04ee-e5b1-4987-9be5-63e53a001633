package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.service.chain.pick.two.TwoPickContext;
import com.labway.lims.apply.service.chain.pick.two.TwoPickTransformCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * 如果这个样本是需要组间交接的，那么传递下去
 */
@Slf4j
@Component
class MultiTwoPickTransformCommand implements Command {
    @Resource
    private TwoPickTransformCommand twoPickTransformCommand;

    @Override
    public boolean execute(Context c) throws Exception {

        final MultiTwoPickContext context = MultiTwoPickContext.from(c);
        for (ApplySampleTwoPickDto e : context.getApplySampleTwoPicks()) {

            final ApplySampleDto applySample = context.getApplySamples().stream()
                    .filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                    .findFirst().orElse(null);

            if (Objects.isNull(applySample)) {
                continue;
            }

            final TwoPickContext ctx = new TwoPickContext(new TwoPickDto()
                    .setApplySampleId(e.getApplySampleId()));
            ctx.put(TwoPickContext.APPLY_SAMPLE_ITEMS, context.getApplySampleItems()
                    .getOrDefault(e.getApplySampleId(), Collections.emptyList()));
            ctx.put(TwoPickContext.APPLY_SAMPLE, applySample);
            ctx.put(TwoPickContext.RACK_LOGIC, context.getRackLogic());
            final Optional<RackLogicSpaceDto> opt = context.getRackLogicSpaces().stream()
                    .filter(l -> Objects.equals(e.getApplySampleId(), l.getApplySampleId())).findFirst();
            if (opt.isEmpty()) {
                throw new IllegalArgumentException(String.format("条码 [%s] 获取试管架位置失败", e.getBarcode()));
            }

            ctx.put(TwoPickContext.RACK_LOGIC_SPACE, opt.get());

            twoPickTransformCommand.execute(ctx);
        }

        return CONTINUE_PROCESSING;
    }


}
