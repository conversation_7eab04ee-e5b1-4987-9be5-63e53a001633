package com.labway.lims.apply.service.chain.pick.one.cancel;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 取消二次分拣
 */
@Component
public class CancelOnePickChain extends ChainBase implements InitializingBean {

    @Resource
    private CancelOnePickCommand cancelOnePickCommand;
    @Resource
    private CancelOnePickCheckApplySampleStatusCommand cancelOnePickCheckApplySampleStatusCommand;
    @Resource
    private CancelOnePickLimitCommand cancelOnePickLimitCommand;
    @Resource
    private CancelOnePickCheckParamCommand cancelOnePickCheckParamCommand;
    @Resource
    private CancelOnePickFlowCommand cancelOnePickFlowCommand;

    @Resource
    private CancelOnePickRemoveSpaceCommand cancelOnePickRemoveSpaceCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 限流
        addCommand(cancelOnePickLimitCommand);

        // 校验状态
        addCommand(cancelOnePickCheckApplySampleStatusCommand);

        // 参数校验
        addCommand(cancelOnePickCheckParamCommand);

        // 取消一次分拣
        addCommand(cancelOnePickCommand);

        // 删除占用
        addCommand(cancelOnePickRemoveSpaceCommand);

        // 条码环节
        addCommand(cancelOnePickFlowCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);

    }
}
