package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

@Getter
@Setter
public class SendItemStatisticsVo {
    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 签收个数
     */
    private AtomicInteger signCount;

    /**
     * 审核个数
     */
    private AtomicInteger auditCount;

    /**
     * 子项统计
     */
    private Set<SendItemStatisticsItem> items;

    @Setter
    @Getter
    public static final class SendItemStatisticsItem {
        /**
         * 时间 yyyy-MM-dd
         */
        private String date;

        /**
         * 项目名称
         */
        private String testItemName;

        /**
         * 项目id
         */
        private Long testItemId;

        /**
         * 签收个数
         */
        private AtomicInteger signCount;

        /**
         * 审核个数
         */
        private AtomicInteger auditCount;


        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            final SendItemStatisticsItem that = (SendItemStatisticsItem) o;
            return Objects.equals(date, that.date) && Objects.equals(testItemId, that.testItemId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(date, testItemId);
        }
    }
}
