package com.labway.lims.apply.model.pda;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description pda申请单双属确认表
 * <AUTHOR>
 * @date 2024-05-30
 */
@Data
@TableName("tb_pda_tobe_confirmed_apply")
public class TbPdaTobeConfirmedApply implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * pda_tobe_confirmed_apply_id
     */
    @TableId
    private Long pdaTobeConfirmedApplyId;

    /**
    * 主条码号
    */
    private String masterBarcode;

    /**
     * 确认的PDA ApplyId
     */
    private Long confirmedPdaApplyId;

    /**
    * 送检机构ID
    */
    private Long hspOrgId;

    /**
    * 送检机构编码
    */
    private String hspOrgCode;

    /**
    * 送检机构名称
    */
    private String hspOrgName;

    /**
    * 状态 0 : 未确认 1 : 已确认
    */
    private Integer status;

    /**
    * create_date
    */
    private Date createDate;

    /**
    * update_date
    */
    private Date updateDate;

    /**
    * creator_name
    */
    private String creatorName;

    /**
    * creator_id
    */
    private Long creatorId;

    /**
    * updater_name
    */
    private String updaterName;

    /**
    * updater_id
    */
    private Long updaterId;

    /**
    * is_delete
    */
    private Integer isDelete;

    /**
    * pda图片列表
    */
    private String pdaImgs;

    /**
     * 确认人id
     */
    private Long conformerId;

    /**
     * 确认人姓名
     */
    private String conformerName;

    /**
     * 确认时间
     */
    private Date conformerTime;



}