package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 一次分拣完交接试管架列表
 */
@Getter
@Setter
public class OnePickRackLogicInfoVo {
    /**
     * 试管架ID
     */
    private Long rackId;

    /**
     * 逻辑试管架ID
     */
    private Long rackLogicId;


    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 一次分拣人
     */
    private String onePickerName;

    /**
     * 下面的罐子的数量
     */
    private Integer count;

    /**
     * 一次分拣日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date onePickDate;
}
