package com.labway.lims.apply.service.chain.pick.two;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 二次分拣
 */
@Component
public class TwoP<PERSON><PERSON><PERSON>n extends ChainBase implements InitializingBean {
    @Resource
    private TwoPickCheckParamCommand twoPickCheckParamCommand;
    @Resource
    private TwoPickFillInfoCommand twoPickFillInfoCommand;
    @Resource
    private TwoPickInstrumentGroupCommand twoPickInstrumentGroupCommand;
    @Resource
    private TwoPickCommand twoPickCommand;
    @Resource
    private TwoPickRedisMarkCommand twoPickRedisMarkCommand;
    @Resource
    private TwoPickFlowCommand twoPickFlowCommand;
    @Resource
    private TwoPickUpdateSampleInfoCommand twoPickUpdateSampleInfoCommand;
    @Resource
    private TwoPickTransformCommand twoPickTransformCommand;
    @Resource
    private TwoPickLimitCommand twoPickLimitCommand;
    @Resource
    private TwoPickCheckApplySampleStatusCommand twoPickCheckApplySampleStatusCommand;
    @Resource
    private TwoPickCheckInstrumentGroupCandoCommand twoPickCheckInstrumentGroupCandoCommand;
    @Resource
    private TwoPickUrgentCommand twoPickUrgentCommand;
    @Resource
    private TwoPickBloodCultureCommand twoPickBloodCultureCommand;
    @Resource
    private TwoPickIdelRackLogicCommand twoPickIdelRackLogicCommand;
    @Resource
    private TwoPickRemoveRackSpaceCommand twoPickRemoveRackSpaceCommand;
    @Resource
    private TwoPickRabbitMQCommand twoPickRabbitMQCommand;
    @Resource
    private TwoPickMicrobiologyCommand twoPickMicrobiologyCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 限流
        addCommand(twoPickLimitCommand);

        // 判断是否已停止或已终止
        addCommand(twoPickCheckApplySampleStatusCommand);

        // 校验参数
        addCommand(twoPickCheckParamCommand);

        // 填充信息
        addCommand(twoPickFillInfoCommand);

        // 获取到要分拣的专业小组
        addCommand(twoPickInstrumentGroupCommand);

        // 判断专业小组能否做这个样本
        addCommand(twoPickCheckInstrumentGroupCandoCommand);

        // 如果有加急项目，那么需要复制出一个样本出来
        addCommand(twoPickUrgentCommand);

        // 血培养
        addCommand(twoPickBloodCultureCommand);

        // 微生物多项目分血
        addCommand(twoPickMicrobiologyCommand);

        // 分拣
        addCommand(twoPickCommand);

        // 修改样本信息
        addCommand(twoPickUpdateSampleInfoCommand);

        // 删除试管架占用
        addCommand(twoPickRemoveRackSpaceCommand);

        // 如果涉及到组间交接，复制逻辑试管架 进行组间交接
        addCommand(twoPickTransformCommand);

        // redis 标记
        addCommand(twoPickRedisMarkCommand);

        // 逻辑试管架环节标记为结束
        addCommand(twoPickIdelRackLogicCommand);

        // 条码环节
        addCommand(twoPickFlowCommand);

        // 发送到 mq
        addCommand(twoPickRabbitMQCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);

    }
}
