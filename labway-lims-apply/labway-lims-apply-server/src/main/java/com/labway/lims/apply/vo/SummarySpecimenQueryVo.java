package com.labway.lims.apply.vo;

import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
public class SummarySpecimenQueryVo {

    /**
     * groupId
     */
    private Long groupId;

    /**
     * 检验项目(检验目的)ID
     */
    private Set<Long> testItemIds;

    /**
     * 检验项目(检验目的)code
     */
    private Set<String> testItemCodes;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 送检机构id
     */
    private Set<Long> hspOrgIds;


    /**
     * 默认当天
     */
    public void defaultDate() {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            final Date date = new Date();
            startDate = DateUtil.beginOfDay(date);
            endDate = DateUtil.endOfDay(date);
        }

    }
}
