package com.labway.lims.apply.service.chain.material.receive.register;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRegisterItemDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.List;

/**
 * 物料 领用登记 信息上下文
 *
 * <AUTHOR>
 * @since 2023/5/9 10:00
 */
@Getter
@Setter
public class MaterialReceiveRegisterContext extends StopWatchContext {

    /**
     * 登记物料信息
     */
    private List<MaterialReceiveRegisterItemDto> registerItemList;
    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    /**
     * 获取 物料 领用登记 从上下文中
     */
    public static MaterialReceiveRegisterContext from(Context context) {
        return (MaterialReceiveRegisterContext)context;
    }

    // 对应 库存 物料信息
    public static final String MATERIAL_INVENTORY = "MATERIAL_INVENTORY_" + IdUtil.objectId();

    public List<MaterialInventoryDto> getMaterialInventoryDtos() {
        return (List<MaterialInventoryDto>)get(MATERIAL_INVENTORY);
    }

    // 对应 专业组 物料信息
    public static final String GROUP_MATERIAL = "GROUP_MATERIAL_" + IdUtil.objectId();

    public List<GroupMaterialDto> getGroupMaterialDtos() {
        return (List<GroupMaterialDto>)get(GROUP_MATERIAL);
    }


    @Override
    protected String getWatcherName() {
        return "领用登记";
    }
}
