package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 外送机构样本
 */
@Getter
@Setter
public class OutsourcingPickedOrgSampleVo {

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 检验项目名称
     */
    private List<String> testItemNames;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;


    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;


    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 签收时间
     */
    private Date signDate;

    /**
     * 就诊类型
     */
    private String applyType;
}
