package com.labway.lims.apply.service.chain.applysampleitem.disable;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <pre>
 * ApplySampleItemDisableChain
 * 检验加急项目 禁用/取消禁用
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/12/10 18:45
 */
@Component
public class ApplySampleItemDisableChain extends ChainBase implements InitializingBean {

    @Resource(name = "ApplySampleItem_CheckLimitCommand")
    private CheckLimitCommand checkLimitCommand;

    @Resource(name = "ApplySampleItem_CheckParamCommand")
    private CheckParamCommand checkParamCommand;

    @Resource
    private CheckDisableParamCommand checkDisableParamCommand;

    @Resource
    private ApplySampleItemDisableCommand applySampleItemDisableCommand;

    @Resource
    private CheckEnableParamCommand checkEnableParamCommand;

    @Resource
    private ApplySampleItemEnableCommand applySampleItemEnableCommand;

    @Resource(name = "ApplySampleItem_SampleFlowCommand")
    private SampleFlowCommand sampleFlowCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 流控
        addCommand(checkLimitCommand);

        // 参数检查
        addCommand(checkParamCommand);

        // 项目禁用参数校验
        addCommand(checkDisableParamCommand);

        // 申请单样本项目禁用
        addCommand(applySampleItemDisableCommand);

        // 项目取消禁用参数校验
        addCommand(checkEnableParamCommand);

        // 申请单样本项目取消禁用
        addCommand(applySampleItemEnableCommand);

        // 记录条码环节
        addCommand(sampleFlowCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);

    }


}
