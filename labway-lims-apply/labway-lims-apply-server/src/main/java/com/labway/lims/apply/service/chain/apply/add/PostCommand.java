package com.labway.lims.apply.service.chain.apply.add;

import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 后置处理 处理一些后事
 */
@Component
@Slf4j
public class PostCommand implements Command {

    @Override
    public boolean execute(Context c) throws Exception {
        final AddApplyContext from = AddApplyContext.from(c);

        // 填充返回数据
        final ApplyInfo applyInfo = fillReturnData(from);
        from.put(AddApplyContext.APPLY_INFO, applyInfo);

        return CONTINUE_PROCESSING;
    }


    private static ApplyInfo fillReturnData(AddApplyContext from) {
        final ApplyDto apply = from.getApply();
        ApplyInfo applyInfo = new ApplyInfo();
        applyInfo.setApplyId(apply.getApplyId());
        applyInfo.setMasterBarcode(apply.getMasterBarcode());
        applyInfo.setSamples(Collections.emptyList());
        applyInfo.setUseOutBarcode(from.getUseOutBarcode());

        final List<ApplySampleDto> applySamples = from.getApplySamples();
        final List<ApplySampleItemDto> applySampleItems = from.getApplySampleItems();

        if (CollectionUtils.isNotEmpty(applySamples)) {
            final List<ApplyInfo.Sample> samples = applySamples.stream().map(m -> {
                ApplyInfo.Sample sample = new ApplyInfo.Sample();
                sample.setApplySampleId(m.getApplySampleId());
                sample.setBarcode(m.getBarcode());
                sample.setTestItems(applySampleItems.stream()
                        .filter(e -> Objects.equals(e.getApplySampleId(), m.getApplySampleId())).map(e -> {
                            final ApplyInfo.TestItem testItem = new ApplyInfo.TestItem();
                            testItem.setTestItemCode(e.getTestItemCode());
                            testItem.setTestItemName(e.getTestItemName());
                            testItem.setItemType(e.getItemType());
                            return testItem;
                        }).collect(Collectors.toList()));
                return sample;
            }).collect(Collectors.toList());
            applyInfo.setSamples(samples);
        }

        return applyInfo;
    }
}
