package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Date;
import java.util.List;

/**
 * 申请单信息
 */
@Getter
@Setter
public class TestApplyVo {

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 就诊类型
     */
    @JsonProperty("applyType")
    private String applyTypeCode;

    /**
     * 就诊类型名称
     */
    private String applyTypeName;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date patientBirthday;

    /**
     * 地址
     */
    private String patientAddress;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 急诊类型
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 样本个数
     */
    private Integer sampleCount;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 样本性状code
     */
    private String samplePropertyCode;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date applyDate;

    /**
     * 采样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date samplingDate;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 科室
     */
    private String dept;

    /***
     * 床号
     */
    private String patientBed;

    /**
     * 临床诊断
     */
    private String clinicalDiagnosis;

    /**
     * 临床诊断
     */
    private String diagnosis;

    /**
     * 备注
     */
    private String remark;

    /**
     * 手机号
     */
    private String patientMobile;

    /**
     * 身份证号
     */
    private String patientCard;

    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 送检医生
     */
    private String sendDoctor;
    /**
     * 送检医生
     */
    private String sendDoctorCode;
    /**
     * 送检医生
     */
    private String sendDoctorName;

    /**
     * 标本部位
     * @since 1.1.4
     */
    private String patientPart;

    /**
     * 检验项目信息
     */
    private List<Item> items;

    @Getter
    @Setter
    public static class Item {

        /**
         * 检验项目id
         */
        private Long testItemId;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 收费数量
         */
        private Integer count;

        /**
         * 急诊类型
         * 
         * @see UrgentEnum
         */
        private Integer urgent;

        /**
         * 自定义码
         */
        private String customCode;

        /**
         * 备注
         */
        private String remark;

        /**
         * 套餐ID
         */
        private Long packageId;

        /**
         * 套餐名称
         */
        private String packageName;

        /**
         * 血培养
         */
        private ApplySampleItemBloodCultureVo bloodCulture;

        /**
         *  样本类型编码
         */
        private String sampleTypeCode;

        /**
         *  样本类型名称
         */
        private String sampleTypeName;

        /**
         * 管型 code
         */
        private String tubeCode;

        /**
         * 管型
         */
        private String tubeName;

        /**
         * 专业组id
         */
        private Long groupId;

        /**
         * 专业组名称
         */
        private String groupName;

        /**
         * 申请单样本项目是否禁用：1：禁用，0：正常
         */
        private Integer isDisabled;

        /**
         * 终止检验状态：0正常，1终止收费，2终止不收费
         *
         * @see StopTestStatus
         */
        private Integer stopStatus;

    }

    public Integer getPatientSex() {
        return ObjectUtils.defaultIfNull(patientSex, SexEnum.DEFAULT.getCode());
    }

    public Integer getPatientAge() {
        return ObjectUtils.defaultIfNull(patientAge, NumberUtils.INTEGER_ZERO);

    }

    public Integer getPatientSubage() {
        return ObjectUtils.defaultIfNull(patientSubage, NumberUtils.INTEGER_ZERO);
    }

    public String getPatientSubageUnit() {
        return StringUtils.defaultIfBlank(patientSubageUnit, PatientSubAgeUnitEnum.MONTH.getValue());
    }
}
