package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 物料待入库、已入库 物料信息
 *
 * <AUTHOR>
 * @since 2023/5/8 20:19
 */
@Getter
@Setter
public class MaterialDeliveryDetailItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物资编号
     */
    private String materialCode;

    /**
     * 物资名称
     */
    private String materialName;
    /**
     * 规格
     */
    private String specification;
    /**
     * 批号
     */
    private String batchNo;

    /**
     * 厂家
     */
    private String manufacturers;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 出库主单位数量
     */
    private BigDecimal deliveryMainNumber;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     * 出库辅单位数量
     */
    private BigDecimal deliveryAssistNumber;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 有效期
     */
    private Date validDate;

    // -------------已入库---------
    /**
     * 入库主单位数量
     */
    private BigDecimal incomeMainNumber;

    /**
     * 入库辅单位数量
     */
    private BigDecimal incomeAssistNumber;

    /**
     * 详细ID-出库详情id
     */
    private Long detailId;
    /**
     * 储存温度
     */
    private String storageTemperature;
    /**
     * 物料条码号
     */
    private String materialBarcode;
    /**
     *  专业组名称
     */
    private String groupName;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 注册证名称
     */
    private String registrationName;

    /**
     * 存放是否合格 1是 0否   默认1
     */
    private Integer ifStorageQualified;

    /**
     * 规格数量是否一致  1是 0否   默认1
     */
    private Integer ifSpecQuantityConsistent;

    /**
     * 包装有无破损 1有0无   默认0
     */
    private Integer ifPackageDamaged;

    /**
     * 效期是否合格  1是 0否   默认1
     */
    private Integer ifValidDateQualified;

    /**
     * 验收结论  1合格0不合格  默认1
     */
    private Integer acceptanceConclusion;
}
