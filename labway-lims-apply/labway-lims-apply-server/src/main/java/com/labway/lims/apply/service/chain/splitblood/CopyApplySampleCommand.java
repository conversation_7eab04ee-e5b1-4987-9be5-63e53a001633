package com.labway.lims.apply.service.chain.splitblood;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.SystemParamService;
import groovy.util.MapEntry;
import lombok.AllArgsConstructor;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 复制
 *
 * <AUTHOR>
 */
@Component
public class CopyApplySampleCommand implements Command, Filter {

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private RackLogicService rackLogicService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @DubboReference
    private SystemParamService systemParamService;
    @DubboReference
    private GroupService groupService;


    @Override
    public boolean execute(Context c) throws Exception {


        final SplitBloodContext context = SplitBloodContext.from(c);


        final ApplySampleDto applySample = context.getApplySample();
        final Map<String, TestItemDto> testItems = context.getTestItems();

        // 根据专业组+外送机构分组
        final List<ApplySampleItemDto> applySampleItems = context.getApplySampleItems();
        Map<OutGroup, List<ApplySampleItemDto>> groupItems = applySampleItems.stream()
                .collect(Collectors.groupingBy(k -> {
                    Long exportOrgId = NumberUtils.LONG_ZERO;
                    final TestItemDto testItem = testItems.get(k.getTestItemCode());
                    if (Objects.nonNull(testItem) && Objects.equals(testItem.getEnableExport(), YesOrNoEnum.YES.getCode())) {
                        exportOrgId = testItem.getExportOrgId();
                    }
                    return new OutGroup(k.getGroupId(), k.getGroupName(), exportOrgId);
                }));

        final RackLogicDto rackLogic = context.getRackLogic();

        final List<Long> applySampleIds = new LinkedList<>(List.of(applySample.getApplySampleId()));
        context.put(SplitBloodContext.APPLY_SAMPLE_IDS, applySampleIds);

        // 不支持分血 那么跳过
        if (!context.isSupportedSplitBlood()) {
            return CONTINUE_PROCESSING;
        }


        // 获取使用原条码的专业组  （ps： 这里主要是applySampleItem下只有groupId和groupName， 系统参数配置为groupCode）
        long originalBarcodeGroupId = this.getOriginalBarcodeGroupId(groupItems.keySet().stream().map(e -> e.groupId).collect(Collectors.toSet()));

        // fix-1.1.3.6 同一个专业组检验项目委外不同的机构， 需要分条码， 重新进行排序
        groupItems = this.sortGroupItemsMap(groupItems, originalBarcodeGroupId);

        int i = 0;
        int j = 0;
        for (var e : groupItems.entrySet()) {
            j++;

            final Long groupId = e.getKey().groupId;
            if (Objects.equals(applySample.getGroupId(), groupId)) {
                continue;
            }

            final List<ApplySampleItemDto> items = groupItems.get(e.getKey());
            if (CollectionUtils.isEmpty(items)) {
                throw new IllegalStateException(String.format("分血失败，此样本下没有专业组 [%s] 的项目", e.getKey().groupName));
            }

            final RackLogicDto splitRackLogic;
            // 如果这个试管架已经在分血待交接那里，直接使用
            final List<RackLogicDto> rackLogics = rackLogicService
                    .selectByRackCodeIdAndPosition(rackLogic.getRackCode(), RackLogicPositionEnum.SPLIT_BLOOD.getCode())
                    .stream().filter(k -> Objects.equals(k.getNextGroupId(), groupId))
                    .collect(Collectors.toCollection(LinkedList::new));
            if (CollectionUtils.isEmpty(rackLogics)) {
                final RackLogicDto rl = new RackLogicDto();
                BeanUtils.copyProperties(rackLogic, rl);
                rl.setRackLogicId(snowflakeService.genId());
                rl.setPosition(RackLogicPositionEnum.SPLIT_BLOOD.getCode());
                rl.setNextGroupId(groupId);
                rl.setNextGroupName(applySampleItems.stream().filter(k -> Objects.equals(k.getGroupId(), groupId))
                        .findFirst().map(ApplySampleItemDto::getGroupName).orElse(StringUtils.EMPTY));
                rackLogicService.addRackLogic(rl);
                splitRackLogic = rl;
            } else {
                splitRackLogic = rackLogics.iterator().next();
            }

            // 分血 复制申请单样本
            final ApplySampleDto splitApplySample = new ApplySampleDto();
            BeanUtils.copyProperties(applySample, splitApplySample);

            // dev-1.1.3.3 判断是否是配置的原条码专业组， 不是的话进行 'barcode_N' 操作
            if (j > 1) {
                splitApplySample.setBarcode(applySample.getBarcode() +
                        "_" + StringUtils.leftPad(String.valueOf(++i), 2, '0'));
            }

            splitApplySample.setApplySampleId(snowflakeService.genId());
            splitApplySample.setGroupId(groupId);
            splitApplySample.setGroupName(items.iterator().next().getGroupName());
            splitApplySample.setIsSplitBlood(YesOrNoEnum.YES.getCode());
            splitApplySample.setItemType(items.iterator().next().getItemType());
            splitApplySample.setSplitDate(new Date());
            splitApplySample.setSplitterId(LoginUserHandler.get().getUserId());
            splitApplySample.setSplitterName(LoginUserHandler.get().getNickname());

            // 如果下面的项目全是外送 那么样本设置为外送
            if (items.stream().allMatch(k -> Objects.equals(k.getIsOutsourcing(), YesOrNoEnum.YES.getCode()))) {
                splitApplySample.setIsOutsourcing(YesOrNoEnum.YES.getCode());
            }

            final long applySampleId = applySampleService.addApplySample(splitApplySample);

            for (ApplySampleItemDto item : items) {

                // 分血 复制申请单项目
                final ApplySampleItemDto splitApplySampleItem = new ApplySampleItemDto();
                BeanUtils.copyProperties(item, splitApplySampleItem);

                splitApplySampleItem.setApplySampleId(applySampleId);
                splitApplySampleItem.setApplySampleItemId(snowflakeService.genId());

                // 添加复制的申请单样本
                applySampleItemService.addApplySampleItem(splitApplySampleItem);

                // 删除旧的申请单项目
                applySampleItemService.deleteByApplySampleItemId(item.getApplySampleItemId());

            }

            // 复制所在位置
            final RackLogicSpaceDto splitRackLogicSpace = new RackLogicSpaceDto();
            BeanUtils.copyProperties(context.getRackLogicSpace(), splitRackLogicSpace);
            splitRackLogicSpace.setRackLogicSpaceId(snowflakeService.genId());
            splitRackLogicSpace.setRackLogicId(splitRackLogic.getRackLogicId());
            splitRackLogicSpace.setApplySampleId(applySampleId);
            rackLogicSpaceService.addRackLogicSpace(splitRackLogicSpace);

            applySampleIds.add(applySampleId);
        }


        return CONTINUE_PROCESSING;
    }

    /**
     * 排序 根据使用原条码的专业组id 排序 groupItemsMap
     */
    private LinkedHashMap<OutGroup, List<ApplySampleItemDto>> sortGroupItemsMap(Map<OutGroup, List<ApplySampleItemDto>> groupItemsMap,
                                                                                long originalBarcodeGroupId
    ) {
        LinkedHashMap<OutGroup, List<ApplySampleItemDto>> map = new LinkedHashMap<>();

        // map 排序后的 key
        LinkedList<OutGroup> outGroupList = new LinkedList<>();

        for (OutGroup outGroup : groupItemsMap.keySet()) {
            // 和原条码同一个专业组则放到最前面
            if (Objects.equals(originalBarcodeGroupId, outGroup.groupId)) {
                outGroupList.addFirst(outGroup);
            } else {
                outGroupList.addLast(outGroup);
            }
        }

        for (OutGroup outGroup : outGroupList) {
            map.put(outGroup, groupItemsMap.get(outGroup));
        }

        return map;
    }

    /**
     * dev-1.1.3.3 获取系统参数中配置的分血专业组条码， 专业组在下标在前则优先使用原条码
     * <a href="https://www.tapd.cn/59091617/prong/stories/view/1159091617001001722"> 【系统参数】增加配置，配置分血后原条码所在专业组】 </a>
     *
     * @param groupIds 专业组id
     * @return 使用原条码的专业组id
     */
    @NonNull
    public long getOriginalBarcodeGroupId(Set<Long> groupIds) {
        final Optional<SystemParamDto> systemParamDtoOptional = Optional.ofNullable(systemParamService.selectByParamName(SystemParamNameEnum.SPLIT_BLOOD_ORIGINAL_BARCODE_GROUP_ORDER.getCode(), LoginUserHandler.get().getOrgId()));

        // 如果没有配置， 则直接返回第一个专业组id
        if (systemParamDtoOptional.isEmpty()) {
            return groupIds.iterator().next();
        }

        // 配置的专业组编码顺序
        final List<String> groupCodes = Arrays.asList(systemParamDtoOptional.get().getParamValue().split(","));

        // 根据专业组id查询专业组编码
        final Map<Long, String> groupIdAndCodeMap = groupService.selectByGroupIds(groupIds)
                .stream().collect(Collectors.toMap(ProfessionalGroupDto::getGroupId, ProfessionalGroupDto::getGroupCode, (a, b) -> a));

        // 获取再配置中下标， 最靠前的返回， 如果没有则随机返回一个
        return (Long) groupIdAndCodeMap.entrySet().stream()
                // 获取再配置中的下标  专业组id  专业组所在下标
                .map(e -> new MapEntry(e.getKey(), groupCodes.indexOf(e.getValue())))
                // 过滤掉配置中不存在的（下标为-1）
                .filter(e -> !Objects.equals(e.getValue(), NumberUtils.INTEGER_MINUS_ONE))
                // 比较下标
                .min(Comparator.comparing(e -> ((Integer) e.getValue())))
                // 如果为空则返回第一个专业组的id
                .orElse(new MapEntry(groupIds.iterator().next(), NumberUtils.INTEGER_ZERO))
                .getKey();
    }


    /**
     * <AUTHOR>
     * @date 2024/09/03
     */
    @AllArgsConstructor
    private static final class OutGroup {
        private Long groupId;
        private String groupName;
        private Long exportOrgId;

        @Override
        public boolean equals(Object o) {
            if (this == o)
                return true;
            if (o == null || getClass() != o.getClass())
                return false;
            OutGroup outGroup = (OutGroup) o;
            return Objects.equals(groupId, outGroup.groupId) && Objects.equals(exportOrgId, outGroup.exportOrgId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(groupId, exportOrgId);
        }
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
