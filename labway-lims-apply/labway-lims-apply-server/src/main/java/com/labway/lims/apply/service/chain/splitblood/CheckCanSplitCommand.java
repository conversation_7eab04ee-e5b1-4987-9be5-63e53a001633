package com.labway.lims.apply.service.chain.splitblood;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.DefaultApplyType;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.SystemParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 判断是否可以分血
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CheckCanSplitCommand implements Command, Filter, InitializingBean {


    /**
     * 禁止分血的前缀
     */
    private static final String FORBIDDEN_SPLIT_BLOOD_SAMPLE_TYPE = "forbidden_blood_sample_type_";

    @Resource
    private ApplyService applyService;
    @DubboReference
    private SystemParamService systemParamService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;


    @Override
    public boolean execute(Context c) throws Exception {

        final SplitBloodContext context = SplitBloodContext.from(c);


        final ApplySampleDto applySample = context.getApplySample();

        context.put(SplitBloodContext.SPLIT_BLOOD_SUPPORTED, true);

        // 判断是否支持分血
        final ApplyDto apply = applyService.
                selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(apply.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("送检机构不存在");
        }

        if (Objects.equals(hspOrganization.getEnableSplitBlood(), 0)) {
            // dev-1.1.3.3  增加不允许分血时配置【送检机构维护】增加就诊类型选择，用于配置当前送检机构下某个或某几个就诊类型不分血】
            // https://www.tapd.cn/59091617/prong/stories/view/1159091617001001725
            final List<String> list = Arrays.asList(hspOrganization.getApplyTypeNames().split(","));
            // 当前送检机构配置 contains 当前申请单送检类型 or 全部 则无法分血
            if (list.contains(apply.getApplyTypeName()) || list.contains(DefaultApplyType.ALL_APPLY_TYPE_NAME)) {
                context.put(SplitBloodContext.SPLIT_BLOOD_SUPPORTED, false);
            }
        }

        // 如果检验机构支持分血 那么判断检验项目类型是否支持分血
        if (context.isSupportedSplitBlood()) {
            // 如果样本类型禁止分血
            context.put(SplitBloodContext.SPLIT_BLOOD_SUPPORTED, sampleTypeCanSplitBlood(applySample.getSampleTypeCode(),
                    apply.getOrgId()));
        }

        // 如果涉及到多个外送机构的 那么需要分血
        // dev-1.1.3.3 委外组也可以走组间交接， 不考虑物理样本先被外送出去
        //        if (context.getApplySampleItems().stream().map(ApplySampleItemDto::getExportOrgId).distinct().count() != 1) {
        //            if (!context.isSupportedSplitBlood()) {
        //                throw new IllegalStateException("涉及到外送机构，但是送检机构禁止分血");
        //            }
        //        }

        return CONTINUE_PROCESSING;
    }


    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }


    /**
     * 样本类型是否支持分血
     */
    private boolean sampleTypeCanSplitBlood(String sampleTypeCode, long orgId) {
        final SystemParamDto systemParam = systemParamService.selectByParamName(
                FORBIDDEN_SPLIT_BLOOD_SAMPLE_TYPE + sampleTypeCode, orgId);
        if (Objects.isNull(systemParam) || Objects.equals(systemParam.getEnable(), YesOrNoEnum.NO.getCode())) {
            return true;
        }

        log.info("机构 [{}] 样本类型 [{}] 配置了分血条件 [{}]", orgId, sampleTypeCode, JSON.toJSONString(systemParam));

        // 如果是 1 那么允许分血
        return Objects.equals(systemParam.getParamValue(), String.valueOf(NumberUtils.INTEGER_ONE));
    }

}
