package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.RackArchiveDto;
import com.labway.lims.apply.model.TbRackArchive;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 试管架归档 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface RackArchiveConverter {

    RackArchiveDto rackArchiveDtoFromTbObj(TbRackArchive obj);

    List<RackArchiveDto> rackArchiveDtoListFromTbObjList(List<TbRackArchive> list);

}
