package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.model.TbSampleImage;
import org.mapstruct.Mapper;

/**
 * <p>
 * SampleImageConverter
 * 样本图片
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/28 14:29
 */
@Mapper(componentModel = "spring")
public interface SampleImageConverter {

    SampleImageDto convert(TbSampleImage obj);

    TbSampleImage convert(SampleImageDto obj);

}
