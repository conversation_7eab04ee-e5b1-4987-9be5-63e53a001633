package com.labway.lims.apply.service.chain.pick.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.RocheTwoPickDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentGroupTestItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentGroupTestItemService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.ReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 获取要分拣到哪个专业小组
 */
@Slf4j
@Component
public class TwoPickInstrumentGroupCommand implements Command {
    @DubboReference
    private InstrumentGroupTestItemService instrumentGroupTestItemService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;

    @DubboReference
    private ReportItemService reportItemService;



    @Override
    public boolean execute(Context c) throws Exception {
        final TwoPickContext context = TwoPickContext.from(c);

        // 罗氏流水线直接指定专业小组
        if (context.getTwoPick() instanceof RocheTwoPickDto) {
            final InstrumentGroupDto instrumentGroup = instrumentGroupService
                    .selectByInstrumentGroupId(((RocheTwoPickDto) context.getTwoPick()).getInstrumentGroupId());
            if (Objects.isNull(instrumentGroup)) {
                throw new IllegalStateException("专业小组不存在");
            }

            // 取到最合适的那一个专业小组
            context.put(TwoPickContext.INSTRUMENT_GROUP, instrumentGroup);

            return CONTINUE_PROCESSING;
        }

        final ApplySampleDto applySample = context.getApplySample();
        final List<ApplySampleItemDto> applySampleItems = context.getApplySampleItems()
                .stream().filter(e -> Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalArgumentException("不可分拣到当前专业组");
        }

        // 查询到此专业组下面的所有项目
        final Map<Long, List<InstrumentGroupTestItemDto>> instrumentGroupTestItems = instrumentGroupTestItemService.selectByGroupId(applySample.
                        getGroupId()).
                stream().collect(Collectors.groupingBy(InstrumentGroupTestItemDto::getInstrumentGroupId));

        TwoPickDto twoPick = context.getTwoPick();

        // 获取到匹配的，根据检验项目匹配，一致的那就是可以在这个专业小组下做
        final var filteredInstrumentGroups = instrumentGroupTestItems.entrySet().stream().filter(e -> {
            for (ApplySampleItemDto item : applySampleItems) {
                // 如果有一个不匹配那么这个专业小组就不可以做
                if (e.getValue().stream().noneMatch(l -> Objects.equals(item.getTestItemCode(), l.getTestItemCode()))) {
                    return false;
                }
            }
            if (Objects.nonNull(twoPick.getInstrumentId())
                    && !e.getValue().stream().map(InstrumentGroupTestItemDto::getInstrumentId).collect(Collectors.toList()).contains(twoPick.getInstrumentId())) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredInstrumentGroups)) {
            throw new IllegalStateException("没有可分拣到的专业小组");
        }

        final List<InstrumentGroupDto> instrumentGroups = instrumentGroupService.selectByInstrumentGroupIds(filteredInstrumentGroups.stream()
                        .map(Map.Entry::getKey).collect(Collectors.toSet())).stream().sorted(Comparator.comparing(o -> ObjectUtils.defaultIfNull(o.getSort(), Integer.MAX_VALUE)))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(instrumentGroups)) {
            throw new IllegalStateException("没有可分拣到的专业小组");
        }

        if (Objects.nonNull(twoPick.getInstrumentId())) {
            List<String> reportItemCodes = instrumentReportItemService.selectByInstrumentId(twoPick.getInstrumentId())
                    .stream().map(InstrumentReportItemDto::getReportItemCode).collect(Collectors.toList());

            List<Long> testItemIds = context.getApplySampleItems().stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toList());
            List<ReportItemDto> reportItemDtos = reportItemService.selectByTestItemIds(testItemIds);
            for (ReportItemDto reportItemDto : reportItemDtos) {
                if(!reportItemCodes.contains(reportItemDto.getReportItemCode())){
                    throw new IllegalStateException(String.format("报告项目 %s[%s] 在此仪器下没有维护", reportItemDto.getReportItemName(), reportItemDto.getReportItemCode()));
                }
            }
        }

        // 取到最合适的那一个专业小组
        context.put(TwoPickContext.INSTRUMENT_GROUP, instrumentGroups.iterator().next());

        return CONTINUE_PROCESSING;
    }
}
