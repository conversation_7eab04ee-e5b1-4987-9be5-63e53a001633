package com.labway.lims.apply.controller;

import cn.hutool.core.lang.Dict;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.business.center.mdm.api.reagent.dto.SysDictDto;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleAbnormalStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.apply.vo.*;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 样本异常 API
 *
 * <AUTHOR>
 * @since 2023/4/12 11:19
 */
@Slf4j
@RestController
@RequestMapping("/sample-abnormal")
public class SampleAbnormalController extends BaseController {

    @DubboReference
    private GroupService groupService;

    @DubboReference
    private ApplyService applyService;

    @Resource
    private ApplySampleService applySampleService;

    @Resource
    private ApplySampleItemService applySampleItemService;

    @Resource
    private SampleAbnormalService sampleAbnormalService;

    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    /**
     * 根据条码号 获取 样本相关信息
     */
    @PostMapping("/select-abnormal-info-by-barcode")
    public Object selectAbnormalInfoByBarcode(@RequestParam("barcode") String barcode) {

        if (StringUtils.isBlank(barcode)) {
            throw new LimsException("条码号不能为空");
        }

        // 对应所有申请单样本 只可能为同一个 申请单
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(applySampleDtos)) {
            throw new LimsException("无效条码号");
        }
        // 申请单id
        Long applyId = applySampleDtos.get(NumberUtils.INTEGER_ZERO).getApplyId();
        // 所有申请单样本ids
        Set<Long> applySampleIds =
                applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());

        final ApplyDto apply = applyService.selectByApplyId(applyId);
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        // 样本 对应检验项目
        List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplySampleIds(applySampleIds);
        // 所有检验项目名称
        String testItemName = String.join(",",
                applySampleItemDtos.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toSet()));
        Set<Long> testItemIds =
                applySampleItemDtos.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());

        SelectAbnormalInfoByBarcodeResponseVo target = new SelectAbnormalInfoByBarcodeResponseVo();
        target.setBarcode(barcode);
        target.setHspOrgId(apply.getHspOrgId());
        target.setHspOrgCode(apply.getHspOrgCode());
        target.setHspOrgName(apply.getHspOrgName());
        target.setSendDoctorCode(apply.getSendDoctorCode());
        target.setSendDoctorName(apply.getSendDoctorName());
        target.setPatientName(apply.getPatientName());
        target.setPatientAge(PatientAges.toText(apply));
        target.setPatientSex(apply.getPatientSex());
        target.setTestItemName(testItemName);
        target.setTestItemIds(testItemIds);
        return target;
    }

    /**
     * 异常 登记
     */
    @PostMapping("/register")
    public Object sampleAbnormalRegister(@RequestBody SampleAbnormalRegisterRequestVo vo) {

        if (StringUtils.isAnyBlank(vo.getAbnormalReasonCode(), vo.getRegistContent())
                || CollectionUtils.isEmpty(vo.getHandleGroupIdList())) {
            throw new LimsException("存在必填项未填写");
        }

        if (StringUtils.length(vo.getRegistContent()) > BaseController.TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException("异常描述过长");
        }
        // 校验输入和库中是否相同
        this.checkParams(vo);
        return sampleAbnormalService.sampleAbnormalRegister(JSON.parseObject(JSON.toJSONString(vo), SampleAbnormalRegisterRequestDto.class));
    }

    private void checkParams(SampleAbnormalRegisterRequestVo vo) {
        if (StringUtils.isNotBlank(vo.getBarcode())) {
            StringJoiner errMsg = new StringJoiner("\n");
            final SelectAbnormalInfoByBarcodeResponseVo abnormalInfoByBarcode = (SelectAbnormalInfoByBarcodeResponseVo) selectAbnormalInfoByBarcode(vo.getBarcode());
            if (!Objects.equals(vo.getHspOrgId(), abnormalInfoByBarcode.getHspOrgId())) {
                errMsg.add(String.format("该条码送检机构为：%s", abnormalInfoByBarcode.getHspOrgName()));
            }
            // 库中的检验项目 是否包含 页面传的检验项目
            final List<String> abnormalTestItemList = Arrays.asList(abnormalInfoByBarcode.getTestItemName().split(","));
            final List<String> testItemList = Arrays.asList(vo.getTestItemName().split(","));
            if (!new HashSet<>(abnormalTestItemList).containsAll(testItemList)) {
                errMsg.add(String.format("该条码检验项目为：%s", abnormalInfoByBarcode.getTestItemName()));
            }
            if (!Objects.equals(vo.getSendDoctorName(), abnormalInfoByBarcode.getSendDoctorName())) {
                errMsg.add(String.format("该条码送检医生为：%s", abnormalInfoByBarcode.getSendDoctorName()));
            }
            if (!Objects.equals(vo.getPatientName(), abnormalInfoByBarcode.getPatientName())) {
                errMsg.add(String.format("该条码病人名称为：%s", abnormalInfoByBarcode.getPatientName()));
            }
            if (!Objects.equals(vo.getPatientSex(), abnormalInfoByBarcode.getPatientSex())) {
                errMsg.add(String.format("该条码病人性别为：%s", SexEnum.getByCode(abnormalInfoByBarcode.getPatientSex()).getDesc()));
            }
            if (!Objects.equals(vo.getPatientAge(), abnormalInfoByBarcode.getPatientAge())) {
                errMsg.add(String.format("该条码病人年龄为：%s", abnormalInfoByBarcode.getPatientAge()));
            }
            if (StringUtils.isNotBlank(errMsg.toString())) {
                throw new IllegalArgumentException(errMsg.toString());
            }
        }
    }

    /**
     * 异常 列表
     */
    @PostMapping("/list")
    public Object sampleAbnormalList(@RequestBody SampleAbnormalListRequestVo vo) {
        final SelectSampleAbnormalDto dto = JSON.parseObject(JSON.toJSONString(vo), SelectSampleAbnormalDto.class);

        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        dto.setOrgId(loginUser.getOrgId());
        dto.setGroupId(loginUser.getGroupId());

        List<SampleAbnormalDto> targetList = sampleAbnormalService.selectBySelectSampleAbnormalDto(dto);
        Set<String> barcodes = new HashSet<>();
        targetList.forEach(item -> {
            if (Objects.equals(item.getHandleDate(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
                item.setHandleDate(null);
            }
            if (Objects.equals(item.getConfirmDate(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
                item.setConfirmDate(null);
            }

            //1.1.3 新增样本 科室的补充。但是异常登记可以为样本 也可以不是样本，。所以这里需要将有样本条码号的加入到科室信息查询中
            if (StringUtils.isNotEmpty(item.getBarcode())){
                barcodes.add(item.getBarcode());
            }
        });
        //这里需要转为vo
        List<SampleAbnormalVo> responseList  = targetList.stream().map(this::convert).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(barcodes)){
            // 去ES 查找样本信息
            final SampleEsQuery query = new SampleEsQuery();
            query.setPageNo(1);
            query.setPageSize(Integer.MAX_VALUE);
            query.setBarcodes(barcodes);
            query.setOrgId(loginUser.getOrgId());
//            query.setGroupIds(Collections.singleton(loginUser.getGroupId()));
            final List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(query);
            if (CollectionUtils.isNotEmpty(samples)) {
                //匹配并且赋值
                samples.forEach(item -> {
                    responseList.forEach(target -> {
                        if (item.getBarcode().equals(target.getBarcode())){
                            target.setDept(item.getDept());
                        }
                    });
                });
            }
        }


        return responseList;
    }

    /**
     * 异常 详细信息
     */
    @PostMapping("/select-detail-by-sample-abnormal-id")
    public Object selectDetailBySampleAbnormalId(@RequestParam("sampleAbnormalId") long sampleAbnormalId) {

        SampleAbnormalDto target = sampleAbnormalService.selectBySampleAbnormalId(sampleAbnormalId);
        if (Objects.isNull(target)) {
            throw new LimsException("样本异常不存在");
        }
        return target;
    }

    /**
     * 异常 处理异常
     */
    @PostMapping("/handle-abnormal")
    public Object handleAbnormal(@RequestBody SampleAbnormalHandleRequestVo vo) {
        if (StringUtils.isBlank(vo.getHandleContent()) || Objects.isNull(vo.getSampleAbnormalId())) {
            throw new LimsException("存在必填项未填写");
        }
        if (StringUtils.length(vo.getHandleContent()) > BaseController.TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException("处理意见过长");
        }
        ProfessionalGroupDto groupDto = null;
        if (Objects.nonNull(vo.getConfirmGroupId()) && !Objects.equals(vo.getConfirmGroupId(), NumberUtils.LONG_ZERO)) {
            groupDto = groupService.selectByGroupId(vo.getConfirmGroupId());
            if (Objects.isNull(groupDto)) {
                throw new LimsException("确认部门无效");
            }
        }
        SampleAbnormalDto sampleAbnormalDto = sampleAbnormalService.selectBySampleAbnormalId(vo.getSampleAbnormalId());
        if (Objects.isNull(sampleAbnormalDto)) {
            throw new LimsException("样本异常不存在");
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        if (!(Objects.equals(sampleAbnormalDto.getHandleGroupId(), NumberUtils.LONG_ZERO)
                || Objects.equals(loginUser.getGroupId(), sampleAbnormalDto.getHandleGroupId()))) {
            throw new LimsException("非本部门异常不可处理");
        }
        if (!Objects.equals(sampleAbnormalDto.getStatus(), SampleAbnormalStatusEnum.UNPROCESSED.getCode())) {
            throw new LimsException("异常状态不为未处理");
        }

        SampleAbnormalDto update = new SampleAbnormalDto();
        update.setHandleUserId(loginUser.getUserId());
        update.setHandleUserName(loginUser.getNickname());
        update.setHandleContent(vo.getHandleContent());
        update.setHandleDate(new Date());
        if (Objects.nonNull(groupDto)) {
            update.setConfirmGroupId(groupDto.getGroupId());
            update.setConfirmGroupName(groupDto.getGroupName());
        }
        update.setStatus(SampleAbnormalStatusEnum.PROCESSED.getCode());
        update.setSampleAbnormalId(sampleAbnormalDto.getSampleAbnormalId());

        sampleAbnormalService.updateBySampleAbnormalId(update);

        // 记录条码环节
        String content = BarcodeFlowEnum.SAMPLE_ABNORMAL_HANDLE.getDesc() +
                "，处理内容：" + vo.getHandleContent() + "，处理部门：" + loginUser.getGroupName();
        sampleAbnormalService.addSampleFlow(List.of(sampleAbnormalDto), BarcodeFlowEnum.SAMPLE_ABNORMAL_HANDLE, content, loginUser);

        return Collections.emptyMap();
    }

    /**
     * 异常 确认异常
     */
    @PostMapping("/confirm-abnormal")
    public Object confirmAbnormal(@RequestBody SampleAbnormalConfirmRequestVo vo) {
        if (StringUtils.isBlank(vo.getConfirmContent()) || Objects.isNull(vo.getSampleAbnormalId())) {
            throw new LimsException("存在必填项未填写");
        }
        if (StringUtils.length(vo.getConfirmContent()) > BaseController.TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException("确认意见过长");
        }

        SampleAbnormalDto sampleAbnormalDto = sampleAbnormalService.selectBySampleAbnormalId(vo.getSampleAbnormalId());
        if (Objects.isNull(sampleAbnormalDto)) {
            throw new LimsException("样本异常不存在");
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        if (Objects.equals(sampleAbnormalDto.getConfirmGroupId(), NumberUtils.LONG_ZERO)) {
            throw new LimsException("无确认部门无需确认");
        }
        if (!Objects.equals(loginUser.getGroupId(), sampleAbnormalDto.getConfirmGroupId())) {
            throw new LimsException("非本部门异常不可处理");
        }
        if (!Objects.equals(sampleAbnormalDto.getStatus(), SampleAbnormalStatusEnum.PROCESSED.getCode())) {
            throw new LimsException("异常状态不为已处理");
        }

        SampleAbnormalDto update = new SampleAbnormalDto();
        update.setConfirmUserId(loginUser.getUserId());
        update.setConfirmUserName(loginUser.getNickname());
        update.setConfirmContent(vo.getConfirmContent());
        update.setConfirmDate(new Date());
        update.setStatus(SampleAbnormalStatusEnum.CONFIRMED.getCode());
        update.setSampleAbnormalId(sampleAbnormalDto.getSampleAbnormalId());

        sampleAbnormalService.updateBySampleAbnormalId(update);

        // 记录条码环节
        String content = BarcodeFlowEnum.SAMPLE_ABNORMAL_CONFIRM.getDesc() +
                "，确认内容：" + vo.getConfirmContent() +
                "，操作部门：" + loginUser.getGroupName();
        sampleAbnormalService.addSampleFlow(List.of(sampleAbnormalDto), BarcodeFlowEnum.SAMPLE_ABNORMAL_CONFIRM, content, loginUser);

        return Collections.emptyMap();
    }

    /**
     * 异常 作废异常
     */
    @PostMapping("/stop-abnormal")
    public Object stopAbnormal(@RequestBody Set<Long> sampleAbnormalIds) {
        List<SampleAbnormalDto> sampleAbnormalDtos = sampleAbnormalService.selectBySampleAbnormalIds(sampleAbnormalIds);
        if (CollectionUtils.isEmpty(sampleAbnormalDtos)) {
            throw new LimsException("对应异常不存在");
        }
        if (sampleAbnormalDtos.stream()
                .anyMatch(obj -> Objects.equals(obj.getStatus(), SampleAbnormalStatusEnum.ABANDONED.getCode()))) {
            throw new LimsException("存在已作废项,不可重复作废");

        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        if (sampleAbnormalDtos.stream().anyMatch(obj -> !Objects.equals(obj.getRegisterId(), loginUser.getUserId()))) {
            throw new LimsException("存在非本人登记异常");
        }
        SampleAbnormalDto update = new SampleAbnormalDto();
        update.setStatus(SampleAbnormalStatusEnum.ABANDONED.getCode());
        sampleAbnormalService.updateBySampleAbnormalIds(update, sampleAbnormalIds);

        // 记录条码环节
        String content = BarcodeFlowEnum.SAMPLE_ABNORMAL_STOP.getDesc() + "，操作部门：" + loginUser.getGroupName();
        sampleAbnormalService.addSampleFlow(sampleAbnormalDtos, BarcodeFlowEnum.SAMPLE_ABNORMAL_STOP, content, loginUser);

        return Collections.emptyMap();
    }

    /**
     * 异常 信息导出
     */
    @PostMapping("/export-abnormal-data")
    public Object exportData(@RequestBody Set<Long> sampleAbnormalIds) {

        final List<SampleAbnormalDto> sampleAbnormalDtos =
                sampleAbnormalService.selectBySampleAbnormalIds(sampleAbnormalIds);

        Set<String> barcodes = new HashSet<>();
        sampleAbnormalDtos.forEach(item -> {
            //1.1.3 新增样本 科室的补充。但是异常登记可以为样本 也可以不是样本，。所以这里需要将有样本条码号的加入到科室信息查询中
            if (StringUtils.isNotEmpty(item.getBarcode())){
                barcodes.add(item.getBarcode());
            }
        });
        //这里需要转为vo
        List<SampleAbnormalVo> responseList  = sampleAbnormalDtos.stream().map(this::convert).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(barcodes)){
            // 去ES 查找样本信息
            final SampleEsQuery query = new SampleEsQuery();
            query.setPageNo(1);
            query.setPageSize(Integer.MAX_VALUE);
            query.setBarcodes(barcodes);
            final List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(query);
            if (CollectionUtils.isNotEmpty(samples)) {
                //匹配并且赋值
                samples.forEach(item -> {
                    responseList.forEach(target -> {
                        if (item.getBarcode().equals(target.getBarcode())){
                            target.setDept(item.getDept());
                        }
                    });
                });
            }
        }


        ExcelWriter excelWriter = null;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            // 头的策略
            WriteCellStyle headCellStyle = new WriteCellStyle();
            headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontName("微软雅黑");
            headWriteFont.setFontHeightInPoints((short) 13);
            headCellStyle.setWriteFont(headWriteFont);

            // 内容策略
            WriteCellStyle contentCellStyle = new WriteCellStyle();
            contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            contentCellStyle.setBorderLeft(BorderStyle.THIN);
            contentCellStyle.setBorderTop(BorderStyle.THIN);
            contentCellStyle.setBorderRight(BorderStyle.THIN);
            contentCellStyle.setBorderBottom(BorderStyle.THIN);

            HorizontalCellStyleStrategy excelStyle = new HorizontalCellStyleStrategy(headCellStyle, contentCellStyle);

            List<List<Object>> list0 = com.google.common.collect.Lists.newArrayList();
            List<List<String>> header0 = com.google.common.collect.Lists.newArrayList();

            // 设置表头
            List<String> headList =
                    com.google.common.collect.Lists.newArrayList("异常原因", "状态", "条码号", "送检机构", "科室","检验项目", "姓名", "性别", "年龄", "登记部门",
                            "登记人", "登记时间", "登记内容", "处理部门", "处理人", "处理内容", "处理时间", "确认部门", "确认人", "确认内容", "确认时间");
            for (String item : headList) {
                header0.add(List.of(item));
            }

            // 设置 放置数据
            fillExcelContent(list0, headList, responseList);

            excelWriter = EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();

            // 获取sheet对象
            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "样本异常信息").head(header0).needHead(Boolean.TRUE).build();

            excelWriter.write(list0, sheet0);
        } catch (Exception e) {
            log.error("导出数据错误", e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            if (Objects.nonNull(excelWriter)) {
                excelWriter.finish();
            }
            try {
                out.close();
            } catch (IOException e) {
                log.error("关闭 流错误", e);
            }
        }
        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("样本异常信息.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(data);
    }

    /**
     * 异常原因下拉框
     */
    @PostMapping("/abnormal-reason-list")
    public Object abnormalReasonList() {
        List<SysDictDto> abnormalReasonList = sampleAbnormalService.getAbnormalReasonList();

        if (Objects.isNull(abnormalReasonList)) {
            return Collections.emptyList();
        }
        return abnormalReasonList.stream().sorted(Comparator.comparing(SysDictDto::getDictCode))
                .map(obj -> Dict.of("dictCode", obj.getDictCode(), "dictValue", obj.getDictValue()))
                .collect(Collectors.toList());
    }


    /**
     * 样本异常 信息导出 填充数据
     *
     * @param list0 数据
     * @param headList 表头
     * @param sampleAbnormalDtos 异常数据
     */
    private void fillExcelContent(List<List<Object>> list0, List<String> headList,
                                  List<SampleAbnormalVo> sampleAbnormalDtos) {

        final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";

        for (SampleAbnormalVo abnormalVo : sampleAbnormalDtos) {
            List<Object> content = Lists.newArrayListWithCapacity(headList.size());

            content.add(abnormalVo.getAbnormalReasonName());
            content.add(SampleAbnormalStatusEnum.getByCode(abnormalVo.getStatus()).getDesc());
            content.add(abnormalVo.getBarcode());
            content.add(abnormalVo.getHspOrgName());
            content.add(abnormalVo.getDept());
            content.add(abnormalVo.getTestItemName());
            content.add(abnormalVo.getPatientName());
            content.add(SexEnum.getByCode(abnormalVo.getPatientSex()).getDesc());
            content.add(abnormalVo.getPatientAge());
            content.add(abnormalVo.getRegistGroupName());
            content.add(abnormalVo.getRegisterName());
            content.add(DateFormatUtils.format(abnormalVo.getRegistDate(), DATE_PATTERN));
            content.add(abnormalVo.getAbnormalReasonName() + "," + abnormalVo.getRegistContent());
            content.add(abnormalVo.getHandleGroupName());
            content.add(abnormalVo.getHandleUserName());
            content.add(abnormalVo.getHandleContent());
            if (Objects.equals(abnormalVo.getHandleDate(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
                content.add(Strings.EMPTY);
            } else {
                content.add(DateFormatUtils.format(abnormalVo.getHandleDate(), DATE_PATTERN));
            }
            content.add(abnormalVo.getConfirmGroupName());
            content.add(abnormalVo.getConfirmUserName());
            content.add(abnormalVo.getConfirmContent());
            if (Objects.equals(abnormalVo.getConfirmDate(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
                content.add(Strings.EMPTY);
            } else {
                content.add(DateFormatUtils.format(abnormalVo.getConfirmDate(), DATE_PATTERN));
            }
            list0.add(content);
        }
    }

    private SampleAbnormalVo convert(SampleAbnormalDto sampleAbnormalDto){
        SampleAbnormalVo vo = new SampleAbnormalVo();
        BeanUtils.copyProperties(sampleAbnormalDto,vo);
        return vo;
    }
}
