package com.labway.lims.apply.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.labway.business.center.compare.request.SendApplyFormInfoRequest;
import com.labway.business.center.compare.request.SendApplySampleInfoRequest;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.apply.vo.ImportErrorResponseVo;
import com.labway.lims.apply.vo.ImportSampleExcelVo;
import com.labway.lims.apply.vo.utils.ExcelFileHeadMappingVo;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.labway.lims.api.web.BaseController.INPUT_MAX_LENGTH;
import static com.labway.lims.api.web.BaseController.TEXTAREA_MAX_LENGTH;
import static java.util.stream.Collectors.toMap;

/**
 * 导入 体检花名册 人员
 *
 * <AUTHOR>
 * @since 2023/3/30 17:55
 */
@Slf4j
public class ImportSampleRegisterListener extends AnalysisEventListener<Map<Integer, String>> {

    private final List<ExcelFileHeadMappingVo> headSetting;


    private final HspOrganizationDto hspOrg;
    
    private String orgCode;
    
    private String orgName;

    public ImportSampleRegisterListener(HspOrganizationDto hspOrg, List<ExcelFileHeadMappingVo> headSetting) {
        this.headSetting = headSetting;
        this.hspOrg = hspOrg;
    }
    
    public void setOrgInfo(String orgCode, String orgName) {
        this.orgCode = orgCode;
        this.orgName = orgName;
    }

    private final List<ImportSampleExcelVo> excelDataList = new ArrayList<>();

    private final List<ImportErrorResponseVo> importErrorResponseVoList = new ArrayList<>();
    private SendApplyFormInfoRequest formInfoRequest = null;

    public List<ImportErrorResponseVo> getImportErrorResponseVoList() {
        return importErrorResponseVoList;
    }

    public List<ImportSampleExcelVo> getExcelDataList() {
        return excelDataList;
    }

    public SendApplyFormInfoRequest getFormInfoRequest() {
        return formInfoRequest;
    }

    private final Map<String, Integer> reverseHeadMap = new HashMap<>();

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 校验表头
        List<String> importHeadList = ImportSampleExcelVo.getHeadList();

        Map<String, ExcelFileHeadMappingVo> settingByTemplateHead =
                headSetting.stream().collect(toMap(ExcelFileHeadMappingVo::getTemplateHead,
                        Function.identity(), (key1, key2) -> key1));

        importHeadList.forEach(item -> {
            ExcelFileHeadMappingVo setting = settingByTemplateHead.get(item);
            if (Objects.nonNull(setting)) {
                reverseHeadMap.put(item, setting.getExcelIndex());
            }
        });
    }

    @Override
    public void invoke(Map<Integer, String> dataMap, AnalysisContext analysisContext) {
        ImportSampleExcelVo data = new ImportSampleExcelVo(dataMap, reverseHeadMap);

        ReadRowHolder readRowHolder = analysisContext.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex() + 1;

        // 检查数据 格式
        String errorMessage = validateData(data);

        if (StringUtils.isNotBlank(errorMessage)) {
            importErrorResponseVoList
                    .add(ImportErrorResponseVo.builder().rowNo(rowIndex).errorInfo(errorMessage).build());
        }

        excelDataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (CollectionUtils.isEmpty(excelDataList)) {
            // 无 导入数据
            return;
        }

        if (CollectionUtils.isNotEmpty(importErrorResponseVoList)) {
            // 存在错误数据 相关 检查 失败
            return;
        }

        //根据barcode进行group分组 同一个条码 有多条数据 因为项目是多个的
        Map<String,List<ImportSampleExcelVo>> excelGroupMap = excelDataList.stream().collect(
                Collectors.groupingBy(ImportSampleExcelVo::getOutBarcode));


        //开始组装数据
        String formCode = String.valueOf(IdWorker.getId());
        formInfoRequest = new SendApplyFormInfoRequest();
        Set<String> patientSubAgeUnitNameList = Arrays.stream(PatientSubAgeUnitEnum.values())
                .map(PatientSubAgeUnitEnum::getValue).collect(Collectors.toSet());
        List<SendApplySampleInfoRequest> sendApplySampleInfoRequestList = new ArrayList<>();
        for (Map.Entry<String,List<ImportSampleExcelVo>> entry : excelGroupMap.entrySet()) {
            List<ImportSampleExcelVo> valueList = entry.getValue();
            //基本信息按照第一个 只有项目是多个
            ImportSampleExcelVo value = valueList.get(0);
            SendApplySampleInfoRequest target = new SendApplySampleInfoRequest();
            //===================配置基本信息===============
            //申请单编码
            target.setFormCode(formCode);
            //条码
            target.setBarcode(value.getOutBarcode());
            //样本类型
            target.setSampleType(value.getSampleType());
            //姓名
            target.setPatientName(value.getPatientName());
            //性别
            target.setPatientSex(Objects.equals(value.getPatientSex(), SexEnum.MAN.getDesc()) ? SexEnum.MAN.getCode()
                    : SexEnum.WOMEN.getCode());
            //年龄
            setPatientAge(patientSubAgeUnitNameList, value, target);
            //门诊/住院号
            target.setPatientVisitCard(StringUtils.defaultString(value.getPatientVisitCard()));
            //科室
            target.setDept(StringUtils.defaultString(value.getDept()));
            //床号
            target.setPatientBed(StringUtils.defaultString(value.getPatientBed()));
            //临床诊断
            target.setClinicalDiagnosis(StringUtils.defaultString(value.getClinicalDiagnosis()));
            //送检医生
            target.setSendDoctor(StringUtils.defaultString(value.getSendDoctor()));
            //申请时间
            target.setApplyDate(getDate(value.getApplyTime()));
            //采样时间
            target.setSamplingDate(getDate(value.getSampleTime()));
            //联系电话
            target.setPatientMobile(StringUtils.defaultString(value.getPatientMobile()));
            //身份证号
            target.setPatientCardType(PatientCardTypeEnum.ID_CARD.name());
            target.setPatientCard(StringUtils.defaultString(value.getPatientCard()));
            //备注
            target.setRemark(StringUtils.defaultString(value.getRemark()));
            //以下是默认值
            target.setHspOrgCode(hspOrg.getHspOrgCode());
            target.setHspOrgName(hspOrg.getHspOrgName());
            target.setOrgCode(orgCode);
            target.setOrgName(orgName);
            target.setTargetOrgCode(orgCode);
            target.setTargetOrgName(orgName);
            target.setUrgent(YesOrNoEnum.NO.getCode());
            target.setApplyType("门诊");
            target.setInpatientArea(StringUtils.EMPTY);
            target.setSampleSource("实验室导入");

            target.setSampleNum(1);
            target.setLisCustomerCode(StringUtils.EMPTY);
            target.setLisCustomerName(StringUtils.EMPTY);

            //===================项目=======================
            List<SendApplySampleInfoRequest.MainItem> mainItems = new ArrayList<>();
            Set<String> testCodeSet = new HashSet<>();
            valueList.forEach(e -> {
                if (!testCodeSet.contains(e.getOutTestCode())){
                    testCodeSet.add(e.getOutTestCode());

                    //组装项目信息
                    SendApplySampleInfoRequest.MainItem tempItem = new SendApplySampleInfoRequest.MainItem();
                    tempItem.setBarcode(e.getOutBarcode());
                    tempItem.setOutTestItemCode(e.getOutTestCode());
                    tempItem.setOutTestItemName(e.getOutTestName());
                    mainItems.add(tempItem);
                }
            });
            target.setMainItems(mainItems);

            sendApplySampleInfoRequestList.add(target);
        }

        //===============申请单信息=================
        formInfoRequest.setFormCode(formCode);
        formInfoRequest.setHspOrgCode(hspOrg.getHspOrgCode());
        formInfoRequest.setHspOrgName(hspOrg.getHspOrgName());
        formInfoRequest.setOrgCode(orgCode);
        formInfoRequest.setOrgName(orgName);
        formInfoRequest.setTargetOrgCode(orgCode);
        formInfoRequest.setTargetOrgName(orgName);
        formInfoRequest.setFormSource("实验室导入");
        formInfoRequest.setSendSampleCount(sendApplySampleInfoRequestList.size());
        formInfoRequest.setSendRequest(sendApplySampleInfoRequestList);
    }

    private final Set<String> validGenders =
            new HashSet<>(Arrays.asList(SexEnum.MAN.getDesc(), SexEnum.WOMEN.getDesc()));

    /**
     * 检查 导入数据
     */
    private String validateData(ImportSampleExcelVo data) {
        StringBuilder errorMessage = new StringBuilder();

        if (StringUtils.isBlank(data.getPatientName())) {
            errorMessage.append("姓名不能为空;");
        } else if (StringUtils.length(data.getPatientName()) > TEXTAREA_MAX_LENGTH) {
            errorMessage.append(String.format("姓名不能超过 %s 字符;", TEXTAREA_MAX_LENGTH));
        }

        //申请时间及采样时间的判断
        if (StringUtils.isBlank(data.getApplyTime())||StringUtils.isBlank(data.getSampleTime())) {
            errorMessage.append("申请时间/采样时间不能为空;");
        }else if (!isValidTimeFormat(data.getApplyTime())) {
            errorMessage.append("申请时间/采样时间格式格式不正确");
        }


        // 验证性别参数是否合法
        if (StringUtils.isBlank(data.getPatientSex())) {
            errorMessage.append("性别不能为空;");
        } else if (!validGenders.contains(data.getPatientSex())) {
            errorMessage
                    .append(String.format("性别参数不规范,请输入 [%s ]或 [%s] ", SexEnum.MAN.getDesc(), SexEnum.WOMEN.getDesc()));
        }

        // 如果纯数字就是岁
        data.setPatientAge(StringUtils.trim(data.getPatientAge()));
        if (NumberUtils.isParsable(data.getPatientAge())) {
            data.setPatientAge(data.getPatientAge() + "岁");
        }

        if (Objects.isNull(data.getPatientAge())) {
            errorMessage.append("年龄不能为空;");
        } else if (!isValidAgeFormat(data.getPatientAge())) {
            errorMessage.append("年龄格式不规范");
        }

        if (StringUtils.isNotBlank(data.getPatientVisitCard())
                && StringUtils.length(data.getPatientVisitCard()) > INPUT_MAX_LENGTH) {
            errorMessage.append(String.format("门诊/住院号不能超过 %s 字符;", INPUT_MAX_LENGTH));
        }

        if (StringUtils.isNotBlank(data.getPatientCard())
                && StringUtils.length(data.getPatientCard()) > INPUT_MAX_LENGTH) {
            errorMessage.append(String.format("身份证号不能超过 %s 字符;", INPUT_MAX_LENGTH));
        }


        if (StringUtils.isNotBlank(data.getDept()) && StringUtils.length(data.getDept()) > INPUT_MAX_LENGTH) {
            errorMessage.append(String.format("床号不能超过 %s 字符;", INPUT_MAX_LENGTH));
        }

        if (StringUtils.isNotBlank(data.getRemark()) && StringUtils.length(data.getRemark()) > TEXTAREA_MAX_LENGTH) {
            errorMessage.append(String.format("备注不能超过 %s 字符;", TEXTAREA_MAX_LENGTH));
        }
        if (StringUtils.isNotBlank(data.getPatientMobile())
                && StringUtils.length(data.getPatientMobile()) > INPUT_MAX_LENGTH) {
            errorMessage.append(String.format("手机号不能超过 %s 字符;", INPUT_MAX_LENGTH));
        }
        if (StringUtils.isNotBlank(data.getClinicalDiagnosis())
                && StringUtils.length(data.getClinicalDiagnosis()) > TEXTAREA_MAX_LENGTH) {
            errorMessage.append(String.format("临床诊断不能超过 %s 字符;", TEXTAREA_MAX_LENGTH));
        }
        if (StringUtils.isNotBlank(data.getPatientBed())
                && StringUtils.length(data.getPatientBed()) > INPUT_MAX_LENGTH) {
            errorMessage.append(String.format("床号不能超过 %s 字符;", INPUT_MAX_LENGTH));
        }
        return errorMessage.toString();
    }

    private static final String ageRegex = "^(\\d+)([岁月周天])$";

    private boolean isValidAgeFormat(String str) {
        return Pattern.matches(ageRegex, str);
    }

    private Date getDate(String date){
        Date returnDate = new Date();
        try {
            returnDate = DateUtils.parseDate(date,"yyyy-MM-dd HH:mm:ss","yyyy-MM-dd HH:mm");
        } catch (ParseException e) {
        }

        return returnDate;
    }

    private boolean isValidTimeFormat(String time){
        try {
            DateUtils.parseDate(time,"yyyy-MM-dd HH:mm:ss","yyyy-MM-dd HH:mm");
        } catch (ParseException e) {
            return false;
        }

        return true;
    }

    private void setPatientAge(Set<String> patientSubAgeUnitNameList, ImportSampleExcelVo value,
                               SendApplySampleInfoRequest target) {
        Pattern pattern = Pattern.compile(ageRegex);
        Matcher matcher = pattern.matcher(value.getPatientAge());
        Integer patientAge = NumberUtils.INTEGER_ZERO;
        Integer patientSubage = NumberUtils.INTEGER_ZERO;
        String patientSubageUnit = StringUtils.EMPTY;
        if (matcher.matches()) {
            String age = matcher.group(NumberUtils.INTEGER_ONE);
            String unit = matcher.group(NumberUtils.INTEGER_TWO);
            if (patientSubAgeUnitNameList.contains(unit)) {
                patientSubage = Integer.valueOf(age);
                patientSubageUnit = unit;
            } else {
                patientAge = Integer.valueOf(age);
            }
        }

        target.setPatientAge(patientAge);
        target.setPatientSubage(patientSubage);
        target.setPatientSubageUnit(patientSubageUnit);
    }
}
