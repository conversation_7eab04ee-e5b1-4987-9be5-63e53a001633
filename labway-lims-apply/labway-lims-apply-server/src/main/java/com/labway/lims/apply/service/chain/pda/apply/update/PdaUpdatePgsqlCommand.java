package com.labway.lims.apply.service.chain.pda.apply.update;

import com.alibaba.fastjson.JSON;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.service.pda.PdaApplySampleItemService;
import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class PdaUpdatePgsqlCommand implements Command {

    @Resource
    private PdaApplyService pdaApplyService;

    @Resource
    private PdaApplySampleItemService pdaApplySampleItemService;


    @Override
    public boolean execute(Context c) throws Exception {
        final UpdateApplyContext from = UpdateApplyContext.from(c);

        // 更新后的申请单样本信息， 根据applyId(pdaApplyId)修改
        final ApplyDto apply = from.getApply();

        // 重新创建的pda申请单样本项目
        final List<ApplySampleItemDto> applySampleItems = from.getUpdateApplySampleItems();


        // 1. 删除原来的项目
        pdaApplySampleItemService.deleteByApplyId(apply.getApplyId());

        final PdaApplyDto pdaApplyDto = JSON.parseObject(JSON.toJSONString(apply), PdaApplyDto.class);
        pdaApplyDto.setPdaApplyId(apply.getApplyId());

        // 2. 更新申请单
        pdaApplyService.updateApplyById(pdaApplyDto);

        // 3. 重新保存新的项目
        pdaApplySampleItemService.addBacthApplySampleItem(applySampleItems);

        return CONTINUE_PROCESSING;
    }
}
