package com.labway.lims.apply.vo;

import cn.hutool.core.lang.Dict;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Getter
@Setter
public class PreprocessApplyTypeStatisticsVo {

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 项目名称
     */
    private String testItemName;

    /**
     * 项目id
     */
    private Long testItemId;

    /**
     * 就诊类型统计
     */
    @JsonIgnore
    private List<ApplyTypeStatistics> applyTypeStatistics;


    public Map<String, Dict> getApplyTypeStatisticsMap() {
        return applyTypeStatistics.stream().collect(Collectors.toMap(
                ApplyTypeStatistics::getApplyTypeCode,
                v -> Dict.create().set("applyTypeName", v.getApplyTypeName()).set("count", v.getCount().get()),
                (a, b) -> a));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        final PreprocessApplyTypeStatisticsVo that = (PreprocessApplyTypeStatisticsVo) o;
        return Objects.equals(hspOrgId, that.hspOrgId) && Objects.equals(testItemId, that.testItemId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(hspOrgId, testItemId);
    }

    /**
     * 就诊类型统计
     */
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static final class ApplyTypeStatistics {
        /**
         * 就诊类型编码
         */
        private String applyTypeCode;
        /**
         * 就诊类型名称
         */
        private String applyTypeName;
        /**
         * 数量
         */
        private AtomicInteger count;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            final ApplyTypeStatistics that = (ApplyTypeStatistics) o;
            return Objects.equals(applyTypeCode, that.applyTypeCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(applyTypeCode);
        }
    }
}
