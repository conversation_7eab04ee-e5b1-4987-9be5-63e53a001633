package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.PhysicalBatchDto;
import com.labway.lims.apply.api.dto.PhysicalRegisterDto;
import com.labway.lims.apply.api.service.PhysicalBatchService;
import com.labway.lims.apply.api.service.PhysicalRegisterService;
import com.labway.lims.apply.mapper.TbPhysicalBatchMapper;
import com.labway.lims.apply.mapstruct.PhysicalBatchConverter;
import com.labway.lims.apply.model.TbPhysicalBatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 体检批次 Service impl
 * 
 * <AUTHOR>
 * @since 2023/3/29 19:51
 */
@Slf4j
@DubboService
public class PhysicalBatchServiceImpl implements PhysicalBatchService {
    @Resource
    private TbPhysicalBatchMapper tbPhysicalBatchMapper;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private PhysicalBatchConverter physicalBatchConverter;

    @Resource
    private PhysicalRegisterService physicalRegisterService;

    /**
     * 体检单位 下 体检批次
     */
    private static final String PHYSICAL_BATCH_BY_PHYSICAL_COMPANY_ID = "PHYSICAL_BATCH_BY_PHYSICAL_COMPANY_ID:";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addPhysicalBatch(PhysicalBatchDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbPhysicalBatch target = physicalBatchConverter.tbPhysicalBatchFromTbDto(dto);

        if (Objects.isNull(target.getPhysicalBatchId())) {
            target.setPhysicalBatchId(snowflakeService.genId());
        }

        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbPhysicalBatchMapper.insert(target) < 1) {
            throw new LimsException("添加细菌失败");
        }
        String key =
            redisPrefix.getBasePrefix() + PHYSICAL_BATCH_BY_PHYSICAL_COMPANY_ID + target.getPhysicalCompanyId();
        stringRedisTemplate.delete(key);

        log.info("用户 [{}] 新增体检批次[{}]成功", loginUser.getNickname(), JSON.toJSONString(target));

        return target.getPhysicalBatchId();
    }

    @Override
    public List<PhysicalBatchDto> selectByPhysicalCompanyId(long physicalCompanyId) {
        if (physicalCompanyId < 1) {
            return Collections.emptyList();
        }
        // 先判断缓存
        List<PhysicalBatchDto> targetList;
        String key = redisPrefix.getBasePrefix() + PHYSICAL_BATCH_BY_PHYSICAL_COMPANY_ID + physicalCompanyId;
        String result = stringRedisTemplate.opsForValue().get(key);

        if (StringUtils.isNotBlank(result)) {
            targetList = JSON.parseArray(result, PhysicalBatchDto.class);
        } else {
            LambdaQueryWrapper<TbPhysicalBatch> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.select(TbPhysicalBatch::getPhysicalBatchId, TbPhysicalBatch::getPhysicalBatchNumber,
                TbPhysicalBatch::getPhysicalCompanyId, TbPhysicalBatch::getPhysicalCompanyName,
                TbPhysicalBatch::getImportDate);
            queryWrapper.eq(TbPhysicalBatch::getPhysicalCompanyId, physicalCompanyId);
            queryWrapper.eq(TbPhysicalBatch::getIsDelete, YesOrNoEnum.NO.getCode());
            queryWrapper.orderByDesc(TbPhysicalBatch::getPhysicalBatchNumber);
            targetList = physicalBatchConverter.fromTbPhysicalBatchList(tbPhysicalBatchMapper.selectList(queryWrapper));
            stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(targetList), Duration.ofMinutes(3));
        }
        return targetList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPhysicalBatchIds(Collection<Long> physicalBatchIds) {
        if (CollectionUtils.isEmpty(physicalBatchIds)) {
            return;
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 获取 批次 id 对应 体检单位 id
        LambdaQueryWrapper<TbPhysicalBatch> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(TbPhysicalBatch::getPhysicalBatchId, TbPhysicalBatch::getPhysicalCompanyId);
        queryWrapper.in(TbPhysicalBatch::getPhysicalBatchId, physicalBatchIds);
        queryWrapper.eq(TbPhysicalBatch::getIsDelete, YesOrNoEnum.NO.getCode());
        final List<TbPhysicalBatch> tbPhysicalBatches = tbPhysicalBatchMapper.selectList(queryWrapper);

        log.info("用户 [{}] 删除体检批次成功 [{}] 结果 [{}]", loginUser.getNickname(), physicalBatchIds,
            tbPhysicalBatchMapper.deleteBatchIds(physicalBatchIds) > 0);

        Set<String> keys = tbPhysicalBatches.stream()
            .map(
                obj -> redisPrefix.getBasePrefix() + PHYSICAL_BATCH_BY_PHYSICAL_COMPANY_ID + obj.getPhysicalCompanyId())
            .collect(Collectors.toSet());
        stringRedisTemplate.delete(keys);

    }

    @Nullable
    @Override
    public PhysicalBatchDto selectByPhysicalBatchId(long physicalBatchId) {
        return physicalBatchConverter.fromTbPhysicalBatch(tbPhysicalBatchMapper.selectById(physicalBatchId));
    }

    @Nullable
    @Override
    public PhysicalBatchDto selectByPhysicalBatchNumber(String physicalBatchNumber, long orgId) {
        if (StringUtils.isBlank(physicalBatchNumber)) {
            return null;
        }
        LambdaQueryWrapper<TbPhysicalBatch> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbPhysicalBatch::getPhysicalBatchNumber, physicalBatchNumber);
        queryWrapper.eq(TbPhysicalBatch::getOrgId, orgId);
        queryWrapper.eq(TbPhysicalBatch::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return physicalBatchConverter.fromTbPhysicalBatch(tbPhysicalBatchMapper.selectOne(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByPhysicalBatchId(PhysicalBatchDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbPhysicalBatch target = new TbPhysicalBatch();
        BeanUtils.copyProperties(dto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbPhysicalBatchMapper.updateById(target) < 1) {
            throw new LimsException("修改体检批次失败");
        }

        // 获取 批次 id 对应 体检单位 id
        TbPhysicalBatch tbPhysicalBatch = tbPhysicalBatchMapper.selectById(target.getPhysicalBatchId());
        if (Objects.isNull(tbPhysicalBatch)) {
            throw new LimsException("体检批次不存在");
        }

        String key = redisPrefix.getBasePrefix() + PHYSICAL_BATCH_BY_PHYSICAL_COMPANY_ID
            + tbPhysicalBatch.getPhysicalCompanyId();
        stringRedisTemplate.delete(key);

        log.info("用户 [{}] 修改体检批次成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importRegister(PhysicalBatchDto physicalBatchDto, List<PhysicalRegisterDto> targetList) {

        // 根据批次号删除 对应 体检人
        physicalRegisterService.deleteByPhysicalBatchId(physicalBatchDto.getPhysicalBatchId());

        // 添加新的 体检人
        physicalRegisterService.addPhysicalRegisters(targetList);

        // 修改 体检批次 导入时间
        PhysicalBatchDto update = new PhysicalBatchDto();
        update.setImportDate(new Date());
        update.setPhysicalBatchId(physicalBatchDto.getPhysicalBatchId());
        this.updateByPhysicalBatchId(update);

    }

}
