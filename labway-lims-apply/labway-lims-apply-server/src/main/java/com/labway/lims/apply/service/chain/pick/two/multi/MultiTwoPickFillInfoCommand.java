
package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.RackLogicApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
class MultiTwoPickFillInfoCommand implements Command {
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private ApplyService applyService;

    @Override
    public boolean execute(Context c) throws Exception {

        final MultiTwoPickContext context = MultiTwoPickContext.from(c);
        final LinkedList<RackLogicApplySampleDto> nosortSamples = new LinkedList<>(applySampleService.selectByRackLogicId(context.getRackLogicId()));

        // 删除已经二次分拣的
        nosortSamples.removeIf(e -> Objects.equals(e.getIsTwoPick(), YesOrNoEnum.YES.getCode()));

        nosortSamples.sort((a, b) -> {
            if (!Objects.equals(a.getColumn(), b.getColumn())) {
                return a.getColumn() - b.getColumn();
            } else {
                return a.getRow() - b.getRow();
            }
        });

        final List<RackLogicApplySampleDto> samples = new ArrayList<>(nosortSamples);

        if (CollectionUtils.isEmpty(samples)) {
            throw new IllegalArgumentException("试管架下没有样本");
        }

        context.put(MultiTwoPickContext.APPLY_SAMPLES, samples);

        // 获取到检验项目
        final var applySampleItems = applySampleItemService
                .selectByApplyIds(samples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));
        if (MapUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("申请单样本下没有检验项目");
        }

        // 校验样本下必须有项目
        for (RackLogicApplySampleDto e : samples) {
            if (CollectionUtils.isEmpty(applySampleItems.get(e.getApplySampleId()))) {
                throw new IllegalStateException(String.format("条码 [%s] 下没有检验项目", e.getBarcode()));
            }
        }
        context.put(MultiTwoPickContext.APPLY_SAMPLE_ITEMS, applySampleItems);

        // 获取样本逻辑试管架空间
        final List<RackLogicSpaceDto> spaces = rackLogicSpaceService.selectByRackLogicId(context.getRackLogicId());
        for (RackLogicApplySampleDto sample : samples) {
            if (spaces.stream().noneMatch(e -> Objects.equals(e.getApplySampleId(), sample.getApplySampleId()))) {
                throw new IllegalStateException(String.format("条码 [%s] 不存在逻辑试管架", sample.getBarcode()));
            }
        }
        context.put(MultiTwoPickContext.RACK_LOGIC_SPACES, spaces);


        // 查询申请单
        final Map<Long, ApplyDto> applies = applyService.selectByApplyIdsAsMap(samples.stream()
                .map(ApplySampleDto::getApplyId).collect(Collectors.toSet()));
        if (MapUtils.isEmpty(applies)) {
            throw new IllegalStateException("申请单不存在");
        }

        // 校验申请单是否存在
        for (RackLogicApplySampleDto sample : samples) {
            if (!applies.containsKey(sample.getApplyId())) {
                throw new IllegalStateException(String.format("条码 [%s] 申请单不存在", sample.getBarcode()));
            }
        }

        context.put(MultiTwoPickContext.APPLIES, applies);

        return CONTINUE_PROCESSING;
    }


}
