package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 送检机构申请单
 */
@Getter
@Setter
public class HspOrgStatisticsVo {
    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 样本
     */
    private List<Sample> samples;

    /**
     * 签收个数
     */
    private Integer signCount;

    /**
     * 审核个数
     */
    private Integer auditCount;

    @Getter
    @Setter
    public static class Sample  implements Serializable {
        /**
         * 病人名称
         */
        private String patientName;

        /**
         * 年龄
         */
        private Integer patientAge;

        /**
         * 子年龄
         */
        private Integer patientSubage;

        /**
         * 月、周、天
         *
         * @see PatientSubAgeUnitEnum
         */
        private String patientSubageUnit;

        /**
         * 性别  性别 1 男，2:女
         *
         * @see SexEnum
         */
        private Integer patientSex;

        /**
         * 检验项目
         */
        private List<String> testItemNames;

        /**
         * 条码号
         */
        private String barcode;

        /**
         * 申请单样本ID
         */
        private Long applySampleId;

        /**
         * 申请单ID
         */
        private Long applyId;

        /**
         * 创建日期
         */
        private Date createDate;

        /**
         * 审核时间
         */
        private Date checkDate;


        /**
         * 样本状态
         * @see SampleStatusEnum
         */
        private Integer status;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        final HspOrgStatisticsVo that = (HspOrgStatisticsVo) o;
        return Objects.equals(hspOrgId, that.hspOrgId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(hspOrgId);
    }
}
