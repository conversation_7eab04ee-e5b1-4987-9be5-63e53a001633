package com.labway.lims.apply.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.vo.GroupTransformHandoverVo;
import com.labway.lims.apply.vo.GroupTransformRackVo;
import com.labway.lims.apply.vo.GroupTransformRacksRequestVo;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组间交接
 */
@Slf4j
@RequestMapping("/group-transform")
@RestController
class GroupTransformController extends BaseController {
    @Resource
    private RackLogicService rackLogicService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private ApplySampleService applySampleService;
    @DubboReference
    private UserService userService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    /**
     * 要组间交接的试管架
     */
    @PostMapping("/racks")
    public Object racks(@RequestBody GroupTransformRacksRequestVo vo) {
        if (Objects.isNull(vo.getBeginReceiveDate()) || Objects.isNull(vo.getEndReceiveDate())) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<RackLogicDto> rackLogics = rackLogicService.selectByUpdateDateAndCurrentGroupIdAndPosition(vo.getBeginReceiveDate(),
                vo.getEndReceiveDate(), LoginUserHandler.get().getGroupId(), RackLogicPositionEnum.GROUP_PICKING.getCode());
        if (CollectionUtils.isEmpty(rackLogics)) {
            return Collections.emptyList();
        }

        final Map<Long, List<RackLogicApplySampleDto>> samples = applySampleService.selectByRackLogicIds(rackLogics.stream()
                        .map(RackLogicDto::getRackLogicId).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(RackLogicApplySampleDto::getRackLogicId));

        return rackLogics.stream().map(e -> {

            final List<RackLogicApplySampleDto> ss = samples.get(e.getRackLogicId());

            if (CollectionUtils.isEmpty(ss)) {
                return null;
            }

            final GroupTransformRackVo v = new GroupTransformRackVo();
            v.setGroupId(e.getNextGroupId());
            v.setGroupName(e.getNextGroupName());
            v.setCount(CollectionUtils.size(ss));
            v.setRackCode(e.getRackCode());
            v.setRackLogicId(e.getRackLogicId());

            if (CollectionUtils.isNotEmpty(ss)) {
                final RackLogicApplySampleDto sample = ss.stream().max(Comparator.comparing(ApplySampleDto::getTwoPickDate))
                        .stream().findFirst().orElse(ss.iterator().next());
                v.setTwoPickerId(sample.getTwoPickerId());
                v.setTwoPickerName(sample.getTwoPickerName());
                v.setTwoPickDate(sample.getTwoPickDate());
            }

            return v;

        }).filter(Objects::nonNull).collect(Collectors.toList());
    }


    /**
     * 组间交接
     */
    @PostMapping("/handover")
    public Object handover(@RequestBody GroupTransformHandoverVo vo) {

        final UserDto user = userService.selectByUsername(vo.getUsername());
        if (Objects.isNull(user)) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        // 校验密码
        if (!userService.validPassword(user.getUsername(), user.getPassword(), vo.getPassword())) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        final RackLogicDto rackLogic = rackLogicService.selectByRackLogicId(vo.getRackLogicId());
        if (Objects.isNull(rackLogic)) {
            throw new IllegalArgumentException("试管架不存在");
        }


        if (!userService.containsGroup(user.getUserId(), rackLogic.getCurrentGroupId())) {
            throw new IllegalStateException(String.format("工号 [%s] 无当前专业组权限，无法交接", vo.getUsername()));
        }


        if (!Objects.equals(rackLogic.getPosition(), RackLogicPositionEnum.GROUP_PICKING.getCode())) {
            throw new IllegalArgumentException("当前试管架不可交接");
        }

        // 锁住
        final String key = redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":HANDOVER:" + rackLogic.getRackCode();

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalStateException("交接失败，请稍后再试");
        }

        try {

            // 删除另外一个兄弟的逻辑试管架占用
            final List<RackLogicSpaceDto> rackLogicSpaces = rackLogicSpaceService.selectByRackLogicId(vo.getRackLogicId());

            // 删除兄弟的，因为涉及到组间交接
            final List<RackLogicSpaceDto> spaces = rackLogicSpaceService.selectByApplySampleIds(rackLogicSpaces.stream().map(RackLogicSpaceDto::getApplySampleId).collect(Collectors.toSet()))
                    .stream().filter(e -> !Objects.equals(e.getRackLogicId(), vo.getRackLogicId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(spaces)) {
                rackLogicSpaceService.deleteByRackLogicSpaceIds(spaces.stream()
                        .map(RackLogicSpaceDto::getRackLogicSpaceId).collect(Collectors.toSet()));
            }

            final RackLogicDto modifyRackLogic = new RackLogicDto();
            modifyRackLogic.setRackLogicId(vo.getRackLogicId());
            modifyRackLogic.setPosition(RackLogicPositionEnum.SPLIT_BLOOD.getCode());
            modifyRackLogic.setLastHandover(user.getNickname());

            if (!rackLogicService.updateByRackLogicId(modifyRackLogic)) {
                throw new IllegalArgumentException("组间交接失败");
            }



            // 记录条码环节
            final Set<Long> applySampleIds = spaces.stream().map(RackLogicSpaceDto::getApplySampleId)
                    .collect(Collectors.toSet());
            final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
            if (CollectionUtils.isEmpty(applySamples)) {

                // 修改专业组
                final ApplySampleDto as = new ApplySampleDto();
                as.setGroupId(rackLogic.getNextGroupId());
                as.setGroupName(rackLogic.getNextGroupName());
                applySampleService.updateByApplySampleIds(as, applySamples.stream()
                        .map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));

                final LinkedList<Long> ids = snowflakeService.genIds(applySamples.size());
                // 记录流水
                sampleFlowService.addSampleFlows(applySamples.stream().map(e -> SampleFlowDto.builder()
                        .applyId(e.getApplyId())
                        .sampleFlowId(ids.pop())
                        .applySampleId(e.getApplySampleId())
                        .operateCode(BarcodeFlowEnum.GROUP_TRANSFORM.name())
                        .operateName(BarcodeFlowEnum.GROUP_TRANSFORM.getDesc())
                        .barcode(e.getBarcode())
                        .content(String.format("从 [%s] 交接到 [%s]", rackLogic.getCurrentGroupName(),
                                rackLogic.getNextGroupName()))
                        .build()).collect(Collectors.toList()));
            }

        } finally {
            stringRedisTemplate.delete(key);
        }

        return Collections.emptyMap();

    }

}
