package com.labway.lims.apply.controller.it8000;

import com.labway.lims.apply.vo.IT8000HandleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * IT8000 扫描到条码
 */
@Slf4j
@Component
class MachineSeenAction implements ActionStrategy {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object action(IT8000HandleVo vo) throws Exception {
        return Map.of();
    }


    @Override
    public IT8000HandleVo.Action action() {
        return IT8000HandleVo.Action.MACHINE_SEEN;
    }


}
