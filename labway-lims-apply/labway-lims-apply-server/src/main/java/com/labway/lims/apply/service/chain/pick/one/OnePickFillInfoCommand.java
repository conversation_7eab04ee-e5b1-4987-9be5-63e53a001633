
package com.labway.lims.apply.service.chain.pick.one;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Component
@Slf4j
class OnePickFillInfoCommand implements Command {
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private TestItemService testItemService;
    @Resource
    private RackLogicService rackLogicService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {

        final OnePickContext context = OnePickContext.from(c);

        final LoginUserHandler.User user = LoginUserHandler.get();
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(context.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }


        // 获取到检验项目
        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(context.getApplySampleId());
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("条码下没有检验项目");
        }

        final Map<String, TestItemDto> testItems = testItemService.selectByTestItemCodes(applySampleItems.stream()
                        .map(ApplySampleItemDto::getTestItemCode).collect(Collectors.toSet()), applySample.getOrgId())
                .stream().collect(Collectors.toMap(TestItemDto::getTestItemCode, v -> v, (a, b) -> a));

        final Date date = new Date();
        for (ApplySampleItemDto e : applySampleItems) {
            final TestItemDto testItemDto = testItems.get(e.getTestItemCode());
            if (Objects.isNull(testItemDto)) {
                throw new IllegalStateException(String.format("检验项目 [%s<%s>] 不存在，请检查基础数据是否维护",
                        e.getTestItemName(), e.getTestItemCode()));
            }
            // dev-1.1.2  一次分拣时需要重新获取检验项目专业组， 后续样本是根据applySampleItem中的专业组进行的是否去分血组，和更新ApplySample中的样本
            e.setGroupId(testItemDto.getGroupId());
            e.setGroupName(testItemDto.getGroupName());
            e.setUpdaterId(user.getUserId());
            e.setUpdaterName(user.getNickname());
            e.setUpdateDate(date);
        }

        context.put(OnePickContext.APPLY_SAMPLE_ITEMS, applySampleItems);
        context.put(OnePickContext.APPLY_SAMPLE, applySample);
        context.put(OnePickContext.TEST_ITEMS, testItems);

        return CONTINUE_PROCESSING;
    }


}
