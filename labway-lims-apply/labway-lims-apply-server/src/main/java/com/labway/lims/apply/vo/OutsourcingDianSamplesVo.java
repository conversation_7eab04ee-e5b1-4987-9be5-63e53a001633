package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 迪安样本
 */
@Getter
@Setter
public class OutsourcingDianSamplesVo {
    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 检验项目
     */
    private List<String> testItemNames;


    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 外送机构
     */
    private String exportOrgName;

    /**
     * 迪安条码
     */
    private String exportBarcode;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private Long hspOrgName;

    /**
     * 送检医生
     */
    private String sendDoctorName;
}
