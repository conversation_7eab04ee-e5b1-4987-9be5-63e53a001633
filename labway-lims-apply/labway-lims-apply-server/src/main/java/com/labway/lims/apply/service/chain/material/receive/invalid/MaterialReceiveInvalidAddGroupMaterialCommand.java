
package com.labway.lims.apply.service.chain.material.receive.invalid;

import com.labway.lims.base.api.dto.GroupMaterialDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 领用作废 修改 专业组 物料总库存
 *
 * <AUTHOR>
 * @since 2023/5/9 10:06
 */
@Slf4j
@Component
public class MaterialReceiveInvalidAddGroupMaterialCommand implements Command {

    @DubboReference
    private GroupMaterialService groupMaterialService;

    @Override
    public boolean execute(Context context) throws Exception {
        final MaterialReceiveInvalidContext from = MaterialReceiveInvalidContext.from(context);
        var receiveRecordDto = from.getMaterialReceiveRecordDto();
        var groupMaterialDto = from.getGroupMaterialDto();

        // 主、辅 库存加上 相应领用数量
	    BigDecimal mainUnitInventory = groupMaterialDto.getMainUnitInventory().add(receiveRecordDto.getReceiveMainNumber());

        BigDecimal assistUnitInventory =
            groupMaterialDto.getAssistUnitInventory().subtract(receiveRecordDto.getReceiveAssistNumber());

        GroupMaterialDto update = new GroupMaterialDto();
        update.setGroupMaterialId(groupMaterialDto.getGroupMaterialId());
        update.setMainUnitInventory(mainUnitInventory);
        update.setAssistUnitInventory(assistUnitInventory);

        groupMaterialService.updateByGroupMaterialId(update);

        return CONTINUE_PROCESSING;
    }
}
