package com.labway.lims.apply.service.chain.material.delivery.business;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 接收业务中台 出库信息
 * 
 * <AUTHOR>
 * @since 2023/5/6 13:37
 */
@Component
public class BusinessCenterDeliveryChain extends ChainBase implements InitializingBean {

    @Resource
    private BusinessCenterDeliveryCheckParamCommand businessCenterDeliveryCheckParamCommand;

    @Resource
    private BusinessCenterDeliveryAddDataCommand businessCenterDeliveryAddDataCommand;

    @Resource
    private BusinessCenterDeliveryUpdateApplyStatusCommand businessCenterDeliveryUpdateApplyStatusCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 检查 参数
        addCommand(businessCenterDeliveryCheckParamCommand);

        // 新增 出库单(待入库单) 出库记录 入库记录
        addCommand(businessCenterDeliveryAddDataCommand);

        // 修改物料申请单 状态
        addCommand(businessCenterDeliveryUpdateApplyStatusCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);
    }
}
