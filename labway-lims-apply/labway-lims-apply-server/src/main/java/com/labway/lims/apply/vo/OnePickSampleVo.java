
package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 一次分拣的试管架
 */
@Getter
@Setter
public class OnePickSampleVo {

    /**
     * 一次分拣日期开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginOnePickDate;

    /**
     * 一次分拣日期结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endOnePickDate;

}
