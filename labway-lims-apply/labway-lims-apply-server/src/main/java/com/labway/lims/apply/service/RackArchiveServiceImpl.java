package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.base.RackTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.apply.mapper.TbRackArchiveMapper;
import com.labway.lims.apply.mapstruct.RackArchiveConverter;
import com.labway.lims.apply.model.TbRackArchive;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.RefrigeratorDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.enums.RackStatusEnum;
import com.labway.lims.base.api.service.RackService;
import com.labway.lims.base.api.service.RefrigeratorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 试管架归档 service impl
 *
 * <AUTHOR>
 * @since 2023/4/13 14:56
 */
@Slf4j
@DubboService
public class RackArchiveServiceImpl implements RackArchiveService {

    @Resource
    private TbRackArchiveMapper tbRackArchiveMapper;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RackArchiveConverter rackArchiveConverter;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private RackLogicService rackLogicService;
    @DubboReference
    private RackService rackService;

    @DubboReference
    private RefrigeratorService refrigeratorService;

    @Override
    public List<RackArchiveDto> selectByRackIds(Collection<Long> rackIds) {
        if (CollectionUtils.isEmpty(rackIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRackArchive> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbRackArchive::getRackId, rackIds);
        queryWrapper.eq(TbRackArchive::getIsDelete, YesOrNoEnum.NO.getCode());

        return rackArchiveConverter.rackArchiveDtoListFromTbObjList(tbRackArchiveMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public RackArchiveDto selectByRackId(long rackId) {
        LambdaQueryWrapper<TbRackArchive> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbRackArchive::getRackId, rackId);
        queryWrapper.eq(TbRackArchive::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return rackArchiveConverter.rackArchiveDtoFromTbObj(tbRackArchiveMapper.selectOne(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addRackArchive(RackArchiveDto dto) {

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbRackArchive target = new TbRackArchive();

        BeanUtils.copyProperties(dto, target);

        target.setRackArchiveId(ObjectUtils.defaultIfNull(dto.getRackArchiveId(), snowflakeService.genId()));
        target.setOrgId(loginUser.getOrgId());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbRackArchiveMapper.insert(target) < 1) {
            throw new IllegalStateException("添加归档试管架信息失败");
        }

        log.info("用户 [{}] 新增归档试管架信息 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(target));

        return target.getRackArchiveId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByRackArchive(RackArchiveDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbRackArchive target = new TbRackArchive();
        BeanUtils.copyProperties(dto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbRackArchiveMapper.updateById(target) < 1) {
            throw new LimsException("修改归档试管架信息失败");
        }

        log.info("用户 [{}] 修改归档试管架信息成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }

    @Override
    public List<RackArchiveDto> selectByRefrigeratorId(long refrigeratorId) {

        LambdaQueryWrapper<TbRackArchive> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbRackArchive::getRefrigeratorId, refrigeratorId);
        queryWrapper.eq(TbRackArchive::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbRackArchive::getEndEffectiveDate);

        return rackArchiveConverter.rackArchiveDtoListFromTbObjList(tbRackArchiveMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByRackLogicId(long rackLogicId) {

        LambdaUpdateWrapper<TbRackArchive> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(TbRackArchive::getIsDelete, YesOrNoEnum.YES.getCode());
        updateWrapper.eq(TbRackArchive::getRackLogicId, rackLogicId);
        updateWrapper.eq(TbRackArchive::getIsDelete, YesOrNoEnum.NO.getCode());

        if (tbRackArchiveMapper.update(null, updateWrapper) < 1) {
            throw new LimsException("删除归档试管架信息失败");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeArchiveSample(List<RackLogicSpaceDto> rackLogicSpaceDtos, RackDto rackDto,
                                    RefrigeratorDto refrigeratorDto) {

        if (CollectionUtils.isEmpty(rackLogicSpaceDtos)) {
            return;
        }
        // 清理 试管架占用
        clearByRackLogicSpaceDtos(rackLogicSpaceDtos);

        // 对应申请单样本id
        Set<Long> applySampleIds =
                rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getApplySampleId).collect(Collectors.toSet());

        // 对应申请单样本
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);
        Map<Long, ApplySampleDto> applySampleByApplySampleId =
                applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));

        LoginUserHandler.User user = LoginUserHandler.get();

        // 构造 条码环节
        List<SampleFlowDto> sampleFlowDtoList = Lists.newArrayListWithCapacity(rackLogicSpaceDtos.size());
        rackLogicSpaceDtos.forEach(item -> {
            long applySampleId = item.getApplySampleId();
            ApplySampleDto applySampleDto = applySampleByApplySampleId.get(applySampleId);
            if (Objects.isNull(applySampleDto)) {
                // 归档样本不存在 ？？？
                return;
            }
            long applyId = applySampleDto.getApplyId();

            final SampleFlowDto flow = new SampleFlowDto();
            flow.setApplyId(applyId);
            flow.setApplySampleId(applySampleId);
            flow.setBarcode(applySampleDto.getBarcode());
            flow.setOperateCode(BarcodeFlowEnum.ARCHIVE_SAMPLE.name());
            flow.setOperateName(BarcodeFlowEnum.ARCHIVE_SAMPLE.getDesc());
            flow.setOperator(user.getNickname());
            flow.setOperatorId(user.getUserId());
            flow.setContent(String.format("移除 [%s] 冰箱下 [%s] 试管架上 [%s] 行 [%s] 列的样本",
                    refrigeratorDto.getRefrigeratorName(), rackDto.getRackCode(), item.getRow() + 1, item.getColumn() + 1));
            sampleFlowDtoList.add(flow);
        });
        // 创建 样本流水记录
        sampleFlowService.addSampleFlows(sampleFlowDtoList);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void extractArchiveSample(List<RackLogicSpaceDto> rackLogicSpaceDtos, RackDto rackDto,
                                     RefrigeratorDto refrigeratorDto, UserDto extractUser, String extractDesc) {
        if (CollectionUtils.isEmpty(rackLogicSpaceDtos)) {
            return;
        }
        // 清理 试管架占用
        clearByRackLogicSpaceDtos(rackLogicSpaceDtos);

        // 对应申请单样本id
        Set<Long> applySampleIds =
                rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getApplySampleId).collect(Collectors.toSet());

        // 对应申请单样本
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);
        Map<Long, ApplySampleDto> applySampleByApplySampleId =
                applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));

        LoginUserHandler.User user = LoginUserHandler.get();

        // 构造 条码环节
        List<SampleFlowDto> sampleFlowDtoList = Lists.newArrayListWithCapacity(rackLogicSpaceDtos.size());
        rackLogicSpaceDtos.forEach(item -> {
            long applySampleId = item.getApplySampleId();
            ApplySampleDto applySampleDto = applySampleByApplySampleId.get(applySampleId);
            if (Objects.isNull(applySampleDto)) {
                // 归档样本不存在 ？？？
                return;
            }
            long applyId = applySampleDto.getApplyId();

            final SampleFlowDto flow = new SampleFlowDto();
            flow.setApplyId(applyId);
            flow.setApplySampleId(applySampleId);
            flow.setBarcode(applySampleDto.getBarcode());
            flow.setOperateCode(BarcodeFlowEnum.EXTRACT_ARCHIVE_SAMPLE.name());
            flow.setOperateName(BarcodeFlowEnum.EXTRACT_ARCHIVE_SAMPLE.getDesc());
            flow.setOperator(user.getNickname());
            flow.setOperatorId(user.getUserId());

            // 提取内容
            ExtractArchiveSampleContentDto contentDto = new ExtractArchiveSampleContentDto(extractUser == null ? user.getNickname() : extractUser.getNickname(),
                    applySampleDto.getGroupName(), refrigeratorDto.getRefrigeratorName(), rackDto.getRackCode(),
                    String.valueOf(item.getRow() + 1), String.valueOf(item.getColumn() + 1), extractDesc);
            flow.setContent(contentDto.toString());

            sampleFlowDtoList.add(flow);
        });
        // 创建 样本流水记录
        sampleFlowService.addSampleFlows(sampleFlowDtoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoExtractArchiveSample(AutoExtractArchiveSampleDto autoExtractArchiveSampleDto) {
        if (StringUtils.isBlank(autoExtractArchiveSampleDto.getExtractDesc())
                || CollectionUtils.isEmpty(autoExtractArchiveSampleDto.getRackLogicSpaceDtoList())) {
            return;
        }
        List<RackLogicSpaceDto> rackLogicSpaceDtos = autoExtractArchiveSampleDto.getRackLogicSpaceDtoList();
        //如果这里存在多个逻辑试管架 要分别处理
        Map<Long, List<RackLogicSpaceDto>> rackLogicMap = rackLogicSpaceDtos.stream().collect(Collectors.groupingBy(RackLogicSpaceDto::getRackLogicId));
        for (Long rackLogicId : rackLogicMap.keySet()) {
            Long rackId = rackLogicMap.get(rackLogicId).get(0).getRackId();

            RackDto rackDto = rackService.selectByRackId(rackId);
            if (Objects.isNull(rackDto)) {
                log.error("所选样本对应试管架不存在");
                continue;
            }
            if (!RackTypeEnum.isArchiveRack(rackDto.getRackTypeCode())) {
                log.error("所选样本对应试管架类型不为归档试管架");
                continue;
            }
            // 对应归档架
            RackArchiveDto rackArchiveDto = selectByRackId(rackId);
            if (Objects.isNull(rackArchiveDto)) {
                log.error("对应归档试管架不存在");
                continue;
            }
            // 对应冰箱
            RefrigeratorDto refrigeratorDto =
                    refrigeratorService.selectByRefrigeratorId(rackArchiveDto.getRefrigeratorId());
            if (Objects.isNull(refrigeratorDto)) {
                log.error("对应冰箱不存在");
                continue;
            }
            // 提取 归档试管架上样本
            extractArchiveSample(rackLogicMap.get(rackLogicId), rackDto, refrigeratorDto, null,
                    autoExtractArchiveSampleDto.getExtractDesc());

            // 当 试管架 上无任何归档样本 时需要 清理 数据
            clearRackWhenArchiveSampleIsEmpty(rackId, rackLogicId, false);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void destroyArchiveSample(List<RackLogicSpaceDto> rackLogicSpaceDtos, RackDto rackDto,
                                     RefrigeratorDto refrigeratorDto) {

        if (CollectionUtils.isEmpty(rackLogicSpaceDtos)) {
            return;
        }
        // 清理 试管架占用
        clearByRackLogicSpaceDtos(rackLogicSpaceDtos);

        // 对应申请单样本id
        Set<Long> applySampleIds =
                rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getApplySampleId).collect(Collectors.toSet());

        // 对应申请单样本
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);
        Map<Long, ApplySampleDto> applySampleByApplySampleId =
                applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));

        LoginUserHandler.User user = LoginUserHandler.get();

        // 构造 条码环节
        List<SampleFlowDto> sampleFlowDtoList = Lists.newArrayListWithCapacity(rackLogicSpaceDtos.size());
        rackLogicSpaceDtos.forEach(item -> {
            long applySampleId = item.getApplySampleId();
            ApplySampleDto applySampleDto = applySampleByApplySampleId.get(applySampleId);
            if (Objects.isNull(applySampleDto)) {
                // 归档样本不存在 ？？？
                return;
            }
            long applyId = applySampleDto.getApplyId();

            final SampleFlowDto flow = new SampleFlowDto();
            flow.setApplyId(applyId);
            flow.setApplySampleId(applySampleId);
            flow.setBarcode(applySampleDto.getBarcode());
            flow.setOperateCode(BarcodeFlowEnum.ARCHIVE_SAMPLE.name());
            flow.setOperateName(BarcodeFlowEnum.ARCHIVE_SAMPLE.getDesc());
            flow.setOperator(user.getNickname());
            flow.setOperatorId(user.getUserId());
            flow.setContent(String.format("销毁 [%s] 冰箱下 [%s] 试管架上 [%s] 行 [%s] 列的样本",
                    refrigeratorDto.getRefrigeratorName(), rackDto.getRackCode(), item.getRow() + 1, item.getColumn() + 1));
            sampleFlowDtoList.add(flow);
        });
        // 创建 样本流水记录
        sampleFlowService.addSampleFlows(sampleFlowDtoList);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearRackWhenArchiveSampleIsEmpty(long rackId, long rackLogicId, boolean deleteRack) {
        // 查看试管架上是否还有归档样本
        List<RackLogicSpaceDto> rackLogicSpaceDtos = rackLogicSpaceService.selectByRackLogicId(rackLogicId);
        if (CollectionUtils.isNotEmpty(rackLogicSpaceDtos)) {
            return false;
        }
        // ----- 已经不存在归档样本-------

        // 删除逻辑试管架
        rackLogicService.deleteByRackLogicId(rackLogicId);

        // 删除归档试管架信息
        this.deleteByRackLogicId(rackLogicId);

        // 修改试管架占用状态
        RackDto rackDto = new RackDto();
        rackDto.setStatus(RackStatusEnum.IDLE.getCode());
        rackDto.setRackId(rackId);

        if (deleteRack) {
            if (!rackService.deleteByRackId(rackDto)) {
                throw new IllegalStateException("删除试管架占用失败");
            }
        } else {
            if (!rackService.updateByRackId(rackDto)) {
                throw new IllegalStateException("修改试管架占用失败");
            }
        }

        return true;
    }

    @Override
    public boolean checkRefrigeratorUseByIds(Collection<Long> refrigeratorIds) {
        LambdaQueryWrapper<TbRackArchive> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbRackArchive::getRefrigeratorId, refrigeratorIds);
        queryWrapper.eq(TbRackArchive::getIsDelete, YesOrNoEnum.NO.getCode());
        return tbRackArchiveMapper.selectCount(queryWrapper) > NumberUtils.LONG_ZERO;
    }

    /**
     * 清理 试管架占用
     *
     */
    private void clearByRackLogicSpaceDtos(List<RackLogicSpaceDto> rackLogicSpaceDtos) {

        // 对应 归档样本空间占用id
        Set<Long> rackLogicSpaceIds =
            rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getRackLogicSpaceId).collect(Collectors.toSet());
        // 对应申请单样本id
        Set<Long> applySampleIds =
            rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getApplySampleId).collect(Collectors.toSet());

        // 删除 空间占用
        rackLogicSpaceService.deleteByRackLogicSpaceIds(rackLogicSpaceIds);

        // 查找多次归档剩下的
        Set<Long> ids = rackLogicSpaceService.selectByApplySampleIds(applySampleIds).stream().map(RackLogicSpaceDto::getApplySampleId).collect(Collectors.toSet());
        applySampleIds.removeIf(ids::contains);

        // 修改 是否归档状态
        ApplySampleDto update = new ApplySampleDto();
        update.setIsArchive(YesOrNoEnum.NO.getCode());
        applySampleService.updateByApplySampleIds(update, applySampleIds);
    }
}
