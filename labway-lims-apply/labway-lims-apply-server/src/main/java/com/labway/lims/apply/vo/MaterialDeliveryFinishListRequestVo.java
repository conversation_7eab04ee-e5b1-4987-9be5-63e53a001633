package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 物料入库 已入库 列表
 * 
 * <AUTHOR>
 * @since 2023/5/6 16:15
 */
@Getter
@Setter
public class MaterialDeliveryFinishListRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 入库日期 开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date beginIncomeDate;

    /**
     * 入库日期 结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endIncomeDate;

    /**
     * 入库单号
     */
    private String incomeNo;

}
