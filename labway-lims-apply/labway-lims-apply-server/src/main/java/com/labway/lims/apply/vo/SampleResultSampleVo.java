package com.labway.lims.apply.vo;

import cn.hutool.core.lang.Dict;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class SampleResultSampleVo {


    /**
     * 男
     */
    public static final int GENDER_MAN = 1;
    /**
     * 女
     */
    public static final int GENDER_FEMALE = 0;
    /**
     * 未知或没有
     */
    public static final int GENDER_UNKNOWN = -1;

    /**
     * 样本id / barcode
     */
    private String barcode;

    /**
     * 样本编号
     */
    private String sampleNo;

    /**
     * 组织id
     *
     */
    private String orgId;


    /**
     * 送检医生
     */
    private String sendDoctor;

    /**
     * 送检科室
     */
    private String sendDept;

    /**
     * 申请日期 yyyy-MM-dd HH:mm:ss
     */
    private Date sendDate;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 出生日期 , yyyy-MM-dd
     */
    private Date birthday;

    /**
     * 性别 ， 0：女 , 1男 , -1 ：其它
     *
     * @see #GENDER_MAN
     * @see #GENDER_FEMALE
     * @see #GENDER_UNKNOWN
     */
    private Integer gender;

    /**
     * 住院号
     */
    private String hospitalNumber;

    /**
     * 床号
     */
    private String hospitalBedNumber;

    /**
     * 样本类型
     * <pre>
     *     例子（不表示真实情况）：
     *      血清
     *      血浆
     *      尿液
     *      全血
     *      羊水
     * </pre>
     */
    private String sampleType;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 仪器检验项目信息
     */
    private List<SampleReportItem> sampleReportItems;

    /**
     * 检验项目信息
     */
    private List<SampleItem> sampleItems;

    /**
     * 额外参数
     */
    private Dict extras = new Dict();

    /**
     * 仪器检验项目信息
     */
    @Getter
    @Setter
    public static class SampleReportItem {
        /**
         * 报告项目名称
         */
        private String name;

        /**
         * 仪器通道号
         */
        private String channelCode;

        /**
         * 仪器编码
         */
        private String instrumentCode;

        /**
         * 报告项目结果类型，具体内容参考 lis-region 代码
         */
        private String type;

        /**
         * 额外参数
         */
        private Dict extras = new Dict();
    }

    /**
     * 检验项目信息
     */
    @Getter
    @Setter
    public static class SampleItem {
        /**
         * 检验项目名称
         */
        private String name;

        /**
         * 检验项目编码
         */
        private String code;
        /**
         * 额外参数
         */
        private Dict extras = new Dict();
    }



}
