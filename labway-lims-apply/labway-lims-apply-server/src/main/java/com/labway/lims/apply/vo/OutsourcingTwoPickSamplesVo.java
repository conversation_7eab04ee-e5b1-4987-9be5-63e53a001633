package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 外送二次分拣样本
 */
@Getter
@Setter
public class OutsourcingTwoPickSamplesVo {

    /**
     * 外送机构
     */
    private String exportOrgName;

    /**
     * 外送机构
     */
    private Long exportOrgId;


    /**
     * 数量
     */
    private Integer count = 0;


    private OutsourcingSentSamplesVo tmp;

    /**
     * 送检机构名称
     */
    private List<OutsourcingSentSamplesVo> list = new ArrayList<>();

    public OutsourcingTwoPickSamplesVo() {
    }

    public OutsourcingTwoPickSamplesVo(String exportOrgName, Long exportOrgId) {
        this.exportOrgName = exportOrgName;
        this.exportOrgId = exportOrgId;
    }

    public OutsourcingTwoPickSamplesVo(String exportOrgName, Long exportOrgId, OutsourcingSentSamplesVo tmp) {
        this.exportOrgName = exportOrgName;
        this.exportOrgId = exportOrgId;
        this.tmp = tmp;
    }

}
