package com.labway.lims.apply.service.chain.pick.one;

import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.service.ApplyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 校验申请单状态
 */
@Slf4j
@Component
class OnePickCheckApplyStatusCommand implements Command {

    @Resource
    private ApplyService applyService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OnePickContext context = OnePickContext.from(c);

        final ApplyDto apply = applyService.selectByApplyId(context.getApplySample().getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        if (!ApplyStatusEnum.isCheck(apply.getStatus())) {
            throw new IllegalStateException("申请单尚未复核");
        }

        return CONTINUE_PROCESSING;
    }

}
