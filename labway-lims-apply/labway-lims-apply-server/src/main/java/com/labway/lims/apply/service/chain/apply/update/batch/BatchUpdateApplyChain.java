package com.labway.lims.apply.service.chain.apply.update.batch;

import javax.annotation.Resource;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * 批量修改 申请单信息
 * 
 * <AUTHOR>
 * @since 2024/2/21 14:58
 */
@Component
public class BatchUpdateApply<PERSON>hain extends ChainBase implements InitializingBean {

    @Resource
    private CheckBatchUpdateApplyParamCommand checkBatchUpdateApplyParamCommand;
    @Resource
    private BatchUpdateApplyCommand batchUpdateApplyCommand;

    @Resource
    private BatchUpdateSampleCommand batchUpdateSampleCommand;

    @Resource
    private BatchUpdateResultCommand batchUpdateResultCommand;
    @Resource
    private BatchRefreshReportCommand batchRefreshReportCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 检查参数
        addCommand(checkBatchUpdateApplyParamCommand);

        // 修改申请单&申请单样本
        addCommand(batchUpdateApplyCommand);

        // 修改具体检验样本信息
        addCommand(batchUpdateSampleCommand);

        // 修改样本结果信息
        addCommand(batchUpdateResultCommand);

        // 刷新报告单
        addCommand(batchRefreshReportCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);

    }
}
