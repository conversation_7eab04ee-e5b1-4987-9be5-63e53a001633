package com.labway.lims.apply.service.chain.apply.add;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@SuppressWarnings("unchecked")
public class AddApplyContext extends StopWatchContext {

    static final String APPLY_INFO = "APPLY_INFO" + IdUtil.objectId();

    /**
     * 返回信息
     */
    public static final String PDA_APPLY_INFO = "PDA_APPLY_INFO_" + IdUtil.objectId();



    static final String USE_OUT_BARCODE = "USE_OUT_BARCODE" + IdUtil.objectId();

    static final String OUT_BARCODE = "OUT_BARCODE" + IdUtil.objectId();

    /**
     * 申请单
     */
    static final String APPLY = "APPLY" + IdUtil.objectId();
    /**
     * pda申请单列表
     */
    public static final String PDA_APPLYS = "PDA_APPLY" + IdUtil.objectId();

    /**
     * 申请单样本
     */
    static final String APPLY_SAMPLES = "APPLY_SAMPLES_" + IdUtil.objectId();
    /**
     * 血培养
     */
    public static final String BLOOD_CULTURE = "APPLY_SAMPLES_" + IdUtil.objectId();

    /**
     * 申请单样本项目
     */
    public static final String APPLY_SAMPLE_ITEMS = "APPLY_SAMPLE_ITEMS_" + IdUtil.objectId();

    /**
     * 检验项目
     */
    static final String TEST_ITEMS = "TEST_ITEMS_" + IdUtil.objectId();

    /**
     * 报告项目
     */
    static final String REPORT_ITEMS = "REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 送检机构
     */
    static final String HSP_ORG = "HSP_ORG_" + IdUtil.objectId();
    /**
     * pda 双输标记， true则第一次和第二次输入匹配成功
     */
    public static final String PDA_DOUBLE_FLAG = "PDA_DOUBLE_FLAG_" + IdUtil.objectId();
    /**
     * pda 录入标记， true则代表直接用主条码， 不用重新判断生成
     */
    public static final String SKIP_MASTER_BARCODE = "SKIP_MASTER_BARCODE_" + IdUtil.objectId();
    /**
     * 是否跳过血培养校验
     */
    public static final String SKIP_BLOOD_CULTURE = "SKIP_MASTER_BARCODE_" + IdUtil.objectId();

    private TestApplyDto testApply;

    private LoginUserHandler.User user;

    public static AddApplyContext from(Context context) {
        return (AddApplyContext) context;
    }

    public ApplyDto getApply() {
        return (ApplyDto) get(APPLY);
    }

    public List<ApplySampleDto> getApplySamples() {
        return (List<ApplySampleDto>) get(APPLY_SAMPLES);
    }

    public List<ApplySampleItemDto> getApplySampleItems() {
        return (List<ApplySampleItemDto>) get(APPLY_SAMPLE_ITEMS);
    }

    public List<TestItemDto> getTestItems() {
        return (List<TestItemDto>) get(TEST_ITEMS);
    }

    public Map<String, List<ReportItemDto>> getReportItems() {
        return (Map<String, List<ReportItemDto>>) get(REPORT_ITEMS);
    }

    public HspOrganizationDto getHspOrganization() {
        return (HspOrganizationDto) get(HSP_ORG);
    }

    public ApplyInfo getApplyInfo() {
        return (ApplyInfo) get(APPLY_INFO);
    }
    public PdaApplyInfoDto getPdaApplyInfo() {
        return (PdaApplyInfoDto) get(PDA_APPLY_INFO);
    }


    public Boolean getUseOutBarcode() {
        return ObjectUtils.defaultIfNull((Boolean) get(USE_OUT_BARCODE),Boolean.FALSE);
    }

    public String getOutBarcode() {
        return (String)get(OUT_BARCODE);
    }

    public ApplySampleItemBloodCultureDto getBloodCulture() {
        return (ApplySampleItemBloodCultureDto) get(BLOOD_CULTURE);
    }

    public List<PdaApplyDto> getPdaApply() {
        return (List<PdaApplyDto>)get(PDA_APPLYS);
    }

    public boolean getPdaDoubleFlag(){
        return BooleanUtils.toBooleanDefaultIfNull((Boolean) get(PDA_DOUBLE_FLAG), false);
    }
    public boolean getSkipMasterBarcode(){
        return BooleanUtils.toBooleanDefaultIfNull((Boolean) get(SKIP_MASTER_BARCODE), false);
    }
    public boolean getSkipBloodCulture(){
        return BooleanUtils.toBooleanDefaultIfNull((Boolean) get(SKIP_BLOOD_CULTURE), false);
    }

    @Override
    protected String getWatcherName() {
        return "添加申请单";
    }

}
