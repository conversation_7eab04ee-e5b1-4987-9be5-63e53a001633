package com.labway.lims.apply.controller;

import com.labway.lims.apply.api.dto.*;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.MaterialRefundRecordService;
import com.labway.lims.apply.api.vo.MaterialIncomeRecordVo;
import com.labway.lims.apply.api.vo.MaterialRefundRecordVo;
import com.labway.lims.apply.api.vo.RefundMaterialMaterialVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 物流退库记录表(TbMaterialRefundRecord)表控制层
 *
 * <AUTHOR>
 * @since 2024-03-04 15:53:16
 */
@Validated
@RestController
@RequestMapping("/tbMaterialRefundRecord")
public class MaterialRefundRecordController extends BaseController {
    /**
     * 服务对象
     */
    @Resource
    private MaterialRefundRecordService materialRefundRecordService;


    /**
     * 物料入库列表查询
     */
    @PostMapping("/queryMaterialMaterialIncomeRecord")
    public MaterialIncomeStaticDto queryMaterialMaterialIncomeRecord(@RequestBody @Valid MaterialIncomeRecordVo materialIncomeRecordVo){
        return materialRefundRecordService.queryMaterialMaterialIncomeRecord(materialIncomeRecordVo);
    }


    /**
     * 查询退库列表
     */
    @PostMapping("/queryMaterialRefundRecord")
    public MaterialRefundStaticDto queryMaterialRefundRecord(@RequestBody @Valid MaterialRefundRecordVo materialRefundRecordVo){
        return materialRefundRecordService.queryMaterialRefundRecord(materialRefundRecordVo);
    }


    /**
     * 物料退库
     */
    @PostMapping("/refundMaterialMaterial")
    public RefundMaterialMaterialDto refundMaterialMaterial(@RequestBody @Valid List<RefundMaterialMaterialVo> refundMaterialMaterialVos){
        return materialRefundRecordService.refundMaterialMaterial(refundMaterialMaterialVos);
    }

    /**
     * 推送物料退库/拒收至业务中台
     */
    @PostMapping("/notifyBusinessMaterialRefund")
    public String notifyBusinessMaterialRefund(@RequestBody @Valid NotifyBusinessMaterialRefundDto notifyBusinessMaterialRefundDto){
        return materialRefundRecordService.notifyBusinessMaterialRefund(notifyBusinessMaterialRefundDto);
    }


}

