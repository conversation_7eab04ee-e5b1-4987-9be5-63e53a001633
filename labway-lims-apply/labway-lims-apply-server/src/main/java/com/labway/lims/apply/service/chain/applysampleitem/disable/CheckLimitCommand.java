package com.labway.lims.apply.service.chain.applysampleitem.disable;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.apply.api.dto.DisableOrEnableItemDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.HashSet;
import java.util.Set;

/**
 * <pre>
 * CheckLimitCommand
 * 流控
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/12/10 18:49
 */
@Component("ApplySampleItem_CheckLimitCommand")
public class CheckLimitCommand implements Command, Filter {
    private static final String MARK = "ApplySampleItem_" + CheckLimitCommand.class.getName();

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context c) throws Exception {

        final ApplySampleItemDisableContext context = ApplySampleItemDisableContext.from(c);
        DisableOrEnableItemDto applySampleItemDto = context.getDisableOrEnableItemDto();

        if (CollectionUtils.isEmpty(applySampleItemDto.getApplySampleItems())) {
            throw new IllegalArgumentException("请选择需要禁用/启用的项目");
        }

        Set<Long> processingApplySampleIds = new HashSet<>();
        for (DisableOrEnableItemDto.ApplySampleItem applySampleItem : applySampleItemDto.getApplySampleItems()) {
            if (processingApplySampleIds.add(applySampleItem.getApplySampleId()) &&
                    BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(redisPrefix.getBasePrefix()
                            + MARK + applySampleItem.getApplySampleId(),
                    StringUtils.EMPTY, Duration.ofSeconds(10)))) {
                throw new IllegalStateException("正在处理中");
            }
        }

        context.put(MARK, StringUtils.EMPTY);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        final ApplySampleItemDisableContext context = ApplySampleItemDisableContext.from(c);
        DisableOrEnableItemDto applySampleItemDto = context.getDisableOrEnableItemDto();

        for (DisableOrEnableItemDto.ApplySampleItem applySampleItem : applySampleItemDto.getApplySampleItems()) {
            stringRedisTemplate.delete(redisPrefix.getBasePrefix() + MARK + applySampleItem.getApplySampleId());
        }
        return CONTINUE_PROCESSING;
    }

}
