package com.labway.lims.apply.controller.rocheIT3000;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.apply.service.chain.pick.two.cancel.CancelTwoPickContext;
import com.labway.lims.apply.service.chain.pick.two.cancel.CancelTwoPickDetachedRackCommand;
import com.labway.lims.apply.vo.IT3000HandleVo;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.*;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分拣
 */
@Slf4j
@Component
@RefreshScope
class RocheOnePickAction implements ActionStrategy, InitializingBean {

    /**
     * 生化专业组编码
     */
    @Value("${shenghua.group.code:-1}")
    private String shGroupCode;

    /**
     * it3000专业组编码
     */
    @Value("${IT3000.group.id:102}")
    private Long it3000GroupId;

    /**
     * it3000专业组名称
     */
    @Value("${IT3000.group.name:罗氏流水线前处理}")
    private String it3000GroupName;


    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private ApplyService applyService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentService instrumentService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private CancelTwoPickDetachedRackCommand cancelTwoPickDetachedRackCommand;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private ReportItemService reportItemService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private RocheOnePickAction self;
    @DubboReference
    private GroupService groupService;
    @Resource
    private RackLogicService rackLogicService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;


    /**
     * 全局限流，同一个条码必须有序。如果获取锁失败会等10秒左右，如果10秒内没有获取到锁那么抛出异常
     */
    private void lock(String barcode) throws InterruptedException {
        final String key = redisPrefix.getBasePrefix() + RocheOnePickAction.class.getName() + ":" + barcode;

        final long timestamp = System.currentTimeMillis();
        final Duration timeout = Duration.ofSeconds(10);

        do {
            // 锁专业小组
            if (BooleanUtils.isTrue(stringRedisTemplate.opsForValue()
                    .setIfAbsent(key, StringUtils.EMPTY, timeout))) {
                return;
            }

            Thread.yield();

            // 尝试加锁
            synchronized (this) {
                wait(20);
            }

        } while (System.currentTimeMillis() - timestamp < timeout.toMillis());

        throw new IllegalStateException(String.format("条码 [%s] 正在分拣中", barcode));
    }

    private void unlock(String barcode) {
        stringRedisTemplate.delete(redisPrefix.getBasePrefix() + RocheOnePickAction.class.getName() + ":" + barcode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object action(IT3000HandleVo vo) throws Exception {
        // 条码号
        final String barcode = vo.getExtras().getString("barcode");
        // 条码号
        final String originalBarcode = vo.getExtras().getString("originalBarcode");
        // 分拣到的专业小组
        final String groupCode = vo.getExtras().getString("groupCode");

        if (StringUtils.isAnyBlank(barcode, groupCode, originalBarcode)) {
            throw new IllegalArgumentException("参数错误");
        }


        // 查询专业组
        final ProfessionalGroupDto group = groupService.selectByGroupCode(groupCode, LoginUserHandler.get().getOrgId());
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException(String.format("专业组 [%s] 不存在", groupCode));
        }


        final List<ApplySampleDto> applySamples = new ArrayList<>(applySampleService.selectByOriginalBarcode(originalBarcode));
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException(String.format("条码号 [%s] 不存在", barcode));
        }

        SampleDto sampleDto = sampleService.selectByBarcode(barcode);
        if (Objects.nonNull(sampleDto) && Objects.equals(sampleDto.getGroupId(), group.getGroupId())) {
            throw new IllegalArgumentException(String.format("条码 [%s] 已经分拣过了", barcode));
        }

        // 判断是否有检验项目
        final List<ApplySampleItemDto> applySampleItems = new ArrayList<>(applySampleItemService.selectByApplySampleIds(applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalArgumentException(String.format("条码号 [%s] 下没有检验项目", barcode));
        }

        // 锁住
        lock(originalBarcode);

        try {

            // 专业组分血 todo 確認是否允许判斷机构是否允许分血
            self.groupSplitBlood(applySamples, applySampleItems, group);

            // 获取当前专业组可以使用的条码，如果没有则新增一个
            final ApplySampleDto applySample = self.getApplySample(barcode, group, applySamples, applySampleItems);

            // 一次分拣
            onePick(applySample, group);

        } finally {
            unlock(originalBarcode);
        }

        return Map.of();
    }

    //==================================================================================================================


    /**
     * 专业组分血，如果不需要分血 那么此方法什么都不会做。如果分血那么会把分血结果放入到参数里
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void groupSplitBlood(List<ApplySampleDto> applySamples, List<ApplySampleItemDto> applySampleItems, ProfessionalGroupDto group) {

        // 如果分血过 那么跳过
        if (applySamples.size() != 1 || applySamples.stream().anyMatch(e ->
                Objects.equals(e.getIsSplitBlood(), YesOrNoEnum.YES.getCode()))) {
            log.info("条码 [{}] 的已经分过血了",
                    applySamples.iterator().next().getOriginalBarcode());
            return;
        }

        final ApplySampleDto applySample = applySamples.iterator().next();

        // 只有一个专业组 那么不需要专业组分血
        if (applySampleItems.stream().map(ApplySampleItemDto::getGroupId)
                .distinct().count() == 1) {

            // 直接修改样本专业组
            final ApplySampleDto as = new ApplySampleDto();
            as.setApplySampleId(applySample.getApplySampleId());
            as.setGroupId(group.getGroupId());
            as.setGroupName(group.getGroupName());

            applySample.setGroupId(group.getGroupId());
            applySample.setGroupName(group.getGroupName());

            applySampleService.updateByApplySampleId(as);

            log.info("条码 [{}] 的检验项目只有一个专业组，无需分血。修改专业组成功",
                    applySamples.iterator().next().getOriginalBarcode());

            return;
        }

        // 判断送检机构是否允许分血
        String hspOrgCode = applySample.getHspOrgCode();
        final HspOrganizationDto organization = hspOrganizationService.selectByHspOrgCode(hspOrgCode);
        if (Objects.isNull(organization)) {
            throw new IllegalStateException("送检机构不存在");
        }
        if (Objects.equals(YesOrNoEnum.NO.getCode(), organization.getEnableSplitBlood())) {
            throw new IllegalStateException(String.format("送检机构 [%s] 不允许分血", hspOrgCode));
        }


        final Map<Long, List<ApplySampleItemDto>> groupApplySampleItems = applySampleItems.stream()
                .collect(Collectors.groupingBy(ApplySampleItemDto::getGroupId));

        final LinkedList<Long> ids = snowflakeService.genIds(1000);

        final List<ApplySampleDto> newApplySamples = new ArrayList<>();
        final List<ApplySampleItemDto> newApplySampleItems = new ArrayList<>();

        for (var e : groupApplySampleItems.entrySet()) {
            final ApplySampleDto as = new ApplySampleDto();
            BeanUtils.copyProperties(applySample, as);

            as.setApplySampleId(ids.pop());
            // 这时候条码还是不可用状态，因为只有分拣的时候才有用
            as.setBarcode(as.getBarcode());
            as.setGroupId(e.getKey());
            as.setIsSplitBlood(YesOrNoEnum.YES.getCode());
            as.setSplitDate(new Date());
            as.setSplitterName("Roche流水线");
            as.setGroupName(e.getValue().iterator().next().getGroupName());
            newApplySamples.add(as);
        }

        // 分血完毕之后再分样本
        for (ApplySampleDto e : newApplySamples) {
            final List<ApplySampleItemDto> items = groupApplySampleItems.get(e.getGroupId());

            for (ApplySampleItemDto k : items) {
                final ApplySampleItemDto j = new ApplySampleItemDto();
                BeanUtils.copyProperties(k, j);
                j.setApplySampleItemId(ids.pop());
                j.setApplySampleId(e.getApplySampleId());
                j.setGroupId(e.getGroupId());
                j.setGroupName(e.getGroupName());
                newApplySampleItems.add(j);
            }

        }


        applySampleItemService.addApplySampleItems(newApplySampleItems);
        applySampleService.addApplySamples(newApplySamples);


        log.info("分血成功 样本 {}\t 项目 {}",
                JSON.toJSONString(newApplySamples), JSON.toJSONString(newApplySampleItems));

        // 复制条码环节
        sampleFlowService.copySampleFlows(applySample.getApplySampleId(), newApplySamples.stream()
                .map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));

        log.info("条码 [{}] 复制条码环节到 [{}] 成功", applySample.getOriginalBarcode(),
                newApplySamples.stream()
                        .map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));

        // 删除原来的样本、项目
        applySampleItemService.deleteByApplySampleIds(List.of(applySample.getApplySampleId()));
        applySampleService.deleteByApplySampleId(applySample.getApplySampleId());


        applySamples.clear();
        applySamples.addAll(newApplySamples);

        applySampleItems.clear();
        applySampleItems.addAll(newApplySampleItems);

    }


    /**
     * 获取一个分血的的申请单样本
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public ApplySampleDto getApplySample(String barcode, ProfessionalGroupDto group, List<ApplySampleDto> applySamples,
                                         List<ApplySampleItemDto> applySampleItems) {

        // 只获取当前专业组的
        applySamples = applySamples.stream().filter(e -> Objects.equals(e.getGroupId(),
                group.getGroupId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException(String.format("数据错误，没有找到专业组 [%s] 的样本信息",
                    group.getGroupName()));
        }


        final LinkedList<Long> ids = snowflakeService.genIds(1000);

        // 获取一个尚分拣的条码
        ApplySampleDto applySample = applySamples.stream().filter(e -> Objects.equals(e.getIsTwoPick(),
                YesOrNoEnum.NO.getCode())).findFirst().orElse(null);


        // 如果为空 那么新增一个，此时新增的也可以理解为分血，但有别与 专业组分血，这个分血是组内分血。
        if (Objects.isNull(applySample)) {

            // 如果不是生化组 抛错，因为只有生化组是按仪器分，所以可以分裂多个样本。
            if (!Objects.equals(group.getGroupCode(), shGroupCode)) {
                throw new IllegalArgumentException(String.format("当前专业组 [%s] 下没有可用条码，只有生化组是按照仪器分血，请确认项目。", group.getGroupName()));
            }

            log.info("条码 [{}] 没有找到待分拣条码，即将开始组内分血", barcode);

            final long newApplySampleId = ids.pop();

            applySample = new ApplySampleDto();
            BeanUtils.copyProperties(applySamples.iterator().next(), applySample);
            applySample.setApplySampleId(newApplySampleId);
            applySample.setIsTwoPick(YesOrNoEnum.NO.getCode());
            applySample.setTwoPickerName(StringUtils.EMPTY);
            applySample.setTwoPickerId(NumberUtils.LONG_ZERO);
            applySample.setTwoPickDate(DefaultDateEnum.DEFAULT_DATE.getDate());

            // 获取到要做的报告项目
            final Map<String, ReportItemDto> reportItems = reportItemService.selectByTestItemIds(applySampleItems.stream()
                            .map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet()))
                    .stream().collect(Collectors.toMap(ReportItemDto::getReportItemCode, v -> v, (a, b) -> a));
            // 获取到这个专业小组可以做的报告项目
            List<InstrumentGroupDto> instrumentGroupDtos = instrumentGroupService.selectByGroupId(group.getGroupId());
            final List<InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService.selectByInstrumentGroupIds(instrumentGroupDtos.stream().map(InstrumentGroupDto::getGroupId).collect(Collectors.toList()));

            // 组内分血的样本要做哪检验项目？答：如果这个专业小组下有这个检验项目下的报告项目，那这个样本就要做这个检验项目
            final Set<Long> testItemIds = new HashSet<>();
            for (InstrumentReportItemDto e : instrumentReportItems) {
                if (reportItems.containsKey(e.getReportItemCode())) {
                    testItemIds.add(reportItems.get(e.getReportItemCode()).getTestItemId());
                }
            }

            if (CollectionUtils.isEmpty(testItemIds)) {
                log.info("条码 [{}] 根据仪器下的报告项目和检验项目下的报告项目没有匹配到检验项目，将使用所有检验项目",
                        barcode);
                testItemIds.addAll(applySampleItems.stream().filter(e -> Objects.equals(e.getGroupId(), group.getGroupId()))
                        .map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet()));
            }

            final Collection<ApplySampleItemDto> items = applySampleItems.stream().filter(e -> testItemIds.contains(e.getTestItemId()))
                    .map(e -> {
                        final ApplySampleItemDto target = new ApplySampleItemDto();
                        // 因为是组内分血，不计算数量
                        BeanUtils.copyProperties(e, target);
                        target.setCount(NumberUtils.INTEGER_ZERO);
                        target.setApplySampleId(newApplySampleId);
                        target.setApplySampleItemId(ids.pop());
                        return target;
                    })
                    .collect(Collectors.toMap(ApplySampleItemDto::getTestItemId, v -> v, (a, b) -> a)).values();


            // 使用仪器传入的条码
            applySample.setBarcode(barcode);

            applySampleItemService.addApplySampleItems(new ArrayList<>(items));
            applySampleService.addApplySample(applySample);


            // 复制二次分拣之前的条码环节
            final List<SampleFlowDto> flows = new ArrayList<>();
            for (SampleFlowDto e : sampleFlowService.selectByApplySampleId(applySamples.iterator().next().getApplySampleId())) {
                // 只复制二次分拣之前的环节
                if (Objects.equals(e.getOperateCode(), BarcodeFlowEnum.TWO_PICK.name())) {
                    break;
                }
                final SampleFlowDto sf = new SampleFlowDto();
                BeanUtils.copyProperties(e, sf);
                sf.setSampleFlowId(ids.pop());
                sf.setApplySampleId(newApplySampleId);
                flows.add(sf);

            }
            sampleFlowService.addSampleFlows(flows);


            log.info("条码 [{}] 组内分血成功。样本信息 [{}] 项目信息 {}", barcode,
                    JSON.toJSONString(applySample), JSON.toJSONString(items));

        } else {


            log.info("条码 [{}] 找到待分拣条码，即将修改条码号为 [{}] 原来条码号为 [{}]",
                    barcode, barcode, applySample.getBarcode());

            applySample.setBarcode(barcode);

            final ApplySampleDto as = new ApplySampleDto();
            as.setApplySampleId(applySample.getApplySampleId());
            as.setBarcode(barcode);
            applySampleService.updateByApplySampleId(as);


        }

        return applySample;
    }


    private void onePick(ApplySampleDto applySample, ProfessionalGroupDto group) throws Exception {

        applySample.setGroupId(group.getGroupId());
        applySample.setGroupName(group.getGroupName());

        // 修改样本信息
        final ApplySampleDto as = new ApplySampleDto();
        as.setItemType(ItemTypeEnum.ROUTINE.name());
        as.setApplySampleId(applySample.getApplySampleId());
        as.setIsOnePick(YesOrNoEnum.YES.getCode());
        as.setOnePickDate(new Date());
        as.setOnePickerId(NumberUtils.LONG_ZERO);
        as.setOnePickerName("Roche流水线");
        applySampleService.updateByApplySampleId(as);


        // 删除试管架占用
        rackLogicSpaceService.deleteByApplySampleId(applySample.getApplySampleId());


        final LinkedList<Long> ids = snowflakeService.genIds(100);

        // 添加逻辑试管架
        final RackLogicDto rackLogic = new RackLogicDto();
        rackLogic.setRackLogicId(ids.pop());
        rackLogic.setRackId(NumberUtils.LONG_ZERO);
        rackLogic.setRackCode("RocheRack");
        rackLogic.setRow(10);
        rackLogic.setColumn(10);
        rackLogic.setPosition(RackLogicPositionEnum.ONE_PICKED.getCode());
        rackLogic.setCurrentGroupId(it3000GroupId);
        rackLogic.setCurrentGroupName(it3000GroupName);
        rackLogic.setNextGroupId(applySample.getGroupId());
        rackLogic.setNextGroupName(applySample.getGroupName());
        rackLogic.setLastHandover(LoginUserHandler.get().getNickname());
        rackLogicService.addRackLogic(rackLogic);

        // 添加占用
        final RackLogicSpaceDto rackLogicSpace = new RackLogicSpaceDto();
        rackLogicSpace.setRackLogicSpaceId(ids.pop());
        rackLogicSpace.setRackLogicId(rackLogic.getRackLogicId());
        rackLogicSpace.setRackId(rackLogic.getRackId());
        rackLogicSpace.setRow(0);
        rackLogicSpace.setStatus(1);
        rackLogicSpace.setColumn(0);
        rackLogicSpace.setRow(0);
        rackLogicSpace.setColumn(0);
        rackLogicSpace.setApplySampleId(applySample.getApplySampleId());
        rackLogicSpaceService.addRackLogicSpace(rackLogicSpace);

        // 条码环节
        sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                .applyId(applySample.getApplyId())
                .applySampleId(applySample.getApplySampleId())
                .barcode(applySample.getBarcode())
                .operateCode(BarcodeFlowEnum.ONE_PICK.name())
                .operateName(BarcodeFlowEnum.ONE_PICK.getDesc())
                .content("Roche前处理一次分拣").build());
    }


    @Override
    public IT3000HandleVo.Action action() {
        return IT3000HandleVo.Action.ONE_PICK;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("生化专业组编码 [{}]", shGroupCode);
    }
}
