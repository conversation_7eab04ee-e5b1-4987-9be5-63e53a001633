package com.labway.lims.apply.controller;

import com.google.common.collect.Lists;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 院感 二次分拣
 *
 * <AUTHOR>
 * @since 2023/7/21 9:26
 */
@Slf4j
@RestController
@RequestMapping("/infection")
public class InfectionController extends BaseController {
    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private PdfReportService pdfReportService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private InfectionSampleService infectionSampleService;

    /**
     * 打印流程单
     */
    @PostMapping("/print-flow-pdf")
    public Object printFlowPdf(@RequestBody Set<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyMap();
        }
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        final Map<Long, ApplyDto> applyMap = applyService.selectByApplyIdsAsMap(applySamples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toList()));

        final Map<Long, InfectionSampleDto> sampleMap = infectionSampleService.selectByApplySampleIds(applySamples.
                        stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(InfectionSampleDto::getApplySampleId, v -> v, (a, b) -> a));

        final Map<Long, List<ApplySampleItemDto>> itemMap = applySampleItemService.selectByApplySampleIds(applySampleIds)
                .stream()
                .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 正序 - 与列表排序方式一样
        applySamples.sort(Comparator.comparing(ApplySampleDto::getTwoPickDate));

        List<String> urlS = Lists.newArrayList();
        applySamples.forEach(item -> {
            final List<ApplySampleItemDto> items = itemMap.get(item.getApplySampleId());
            final ApplyDto apply = applyMap.get(item.getApplyId());
            final InfectionSampleDto sample = sampleMap.get(item.getApplySampleId());
            if (CollectionUtils.isNotEmpty(items) && Objects.nonNull(apply) && Objects.nonNull(sample)) {
                final PdfReportParamDto param = new PdfReportParamDto();
                param.put("applySample", item);
                param.put("testItems", items);
                param.put("sample", sample);
                param.put("apply", apply);
                final String pdfUrl =
                        pdfReportService.build2Url(PdfTemplateTypeEnum.PRINT_INFECTION_FLOW.getCode(), param, 3);
                urlS.add(pdfUrl);
            }
        });

        return Map.of("url", urlS);
    }
}
