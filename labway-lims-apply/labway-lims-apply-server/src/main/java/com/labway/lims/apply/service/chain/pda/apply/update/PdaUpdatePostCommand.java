package com.labway.lims.apply.service.chain.pda.apply.update;

import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.PdaApplyInfoDto;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class PdaUpdatePostCommand implements Command {

    @Override
    public boolean execute(Context c) throws Exception {
        final UpdateApplyContext from = UpdateApplyContext.from(c);

        final ApplyDto apply = from.getApply();

        PdaApplyInfoDto applyInfo = new PdaApplyInfoDto();
        applyInfo.setApplyId(apply.getApplyId());
        applyInfo.setMasterBarcode(apply.getMasterBarcode());

        Optional.ofNullable(from.getUpdateApplySampleItems()).ifPresent(m -> {

            final List<PdaApplyInfoDto.PdaTestItem> pdaTestItems = m.stream().map(e -> {
                PdaApplyInfoDto.PdaTestItem testItem = new PdaApplyInfoDto.PdaTestItem();
                testItem.setTestItemCode(e.getTestItemCode());
                testItem.setTestItemName(e.getTestItemName());
                testItem.setItemType(e.getItemType());
                return testItem;
            }).collect(Collectors.toList());

            applyInfo.setTestItems(pdaTestItems);
        });

        from.put(UpdateApplyContext.PDA_APPLY_INFO, applyInfo);

        return CONTINUE_PROCESSING;
    }
}
