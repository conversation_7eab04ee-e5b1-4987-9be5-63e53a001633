package com.labway.lims.apply.service;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.mapper.TbApplySampleItemMapper;
import com.labway.lims.apply.mapstruct.ApplySampleItemConverter;
import com.labway.lims.apply.model.TbApplySampleItem;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@DubboService
public class ApplySampleItemServiceImpl implements ApplySampleItemService {
    @Resource
    private TbApplySampleItemMapper applySampleItemMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private ApplySampleItemConverter applySampleItemConverter;

    @Override
    public List<ApplySampleItemDto> selectByApplySampleId(long applySampleId) {
        LambdaQueryWrapper<TbApplySampleItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbApplySampleItem::getApplySampleId, applySampleId);
        queryWrapper.eq(TbApplySampleItem::getStopStatus, StopTestStatus.NO_STOP_TEST.getCode());
        return applySampleItemConverter
            .applySampleItemDtoListFromTbObjDto(applySampleItemMapper.selectList(queryWrapper));

    }

    @Override
    public List<ApplySampleItemDto> selectByBarcode(long orgId, String barcode) {
        return applySampleItemMapper.selectByBarcode(orgId, barcode);
    }

    @Override
    public List<ApplySampleItemDto> selectByApplySampleIdContainStopTest(Collection<Long> applySampleIds) {
        LambdaQueryWrapper<TbApplySampleItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbApplySampleItem::getApplySampleId, applySampleIds);
        return applySampleItemConverter
            .applySampleItemDtoListFromTbObjDto(applySampleItemMapper.selectList(queryWrapper));
    }

    @Override
    public List<ApplySampleItemDto> selectByApplySampleIds(Collection<Long> applySampleIds) {

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        final List<Callable<List<ApplySampleItemDto>>> callables =
            ListUtils.partition(new ArrayList<>(applySampleIds), 500).stream()
                .map(e -> (Callable<List<ApplySampleItemDto>>)() -> applySampleItemMapper
                    .selectList(new LambdaQueryWrapper<TbApplySampleItem>().in(TbApplySampleItem::getApplySampleId, e)
                        .eq(TbApplySampleItem::getStopStatus, StopTestStatus.NO_STOP_TEST.getCode()))
                    .stream().map(this::convert).collect(Collectors.toList()))
                .collect(Collectors.toList());

        return getApplySampleItemDtos(callables);
    }

    @Override
    public List<ApplySampleItemDto> selectAllByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        final List<Callable<List<ApplySampleItemDto>>> callables =
                ListUtils.partition(new ArrayList<>(applySampleIds), 500).stream()
                        .map(e -> (Callable<List<ApplySampleItemDto>>)() -> applySampleItemMapper
                                .selectList(new LambdaQueryWrapper<TbApplySampleItem>().in(TbApplySampleItem::getApplySampleId, e)
                                        .in(TbApplySampleItem::getStopStatus, StopTestStatus.STOP_TEST_CHARGE.getCode()
                                            , StopTestStatus.NO_STOP_TEST.getCode()))
                                .stream().map(this::convert).collect(Collectors.toList()))
                        .collect(Collectors.toList());

        return getApplySampleItemDtos(callables);
    }

    private List<ApplySampleItemDto> getApplySampleItemDtos(List<Callable<List<ApplySampleItemDto>>> callables) {
        final List<ApplySampleItemDto> list = new LinkedList<>();

        try {

            // 如果只有一个那么直接调用
            if (callables.size() == 1) {
                list.addAll(callables.iterator().next().call());
            } else {
                // 有多个开多线程查询
                final List<Future<List<ApplySampleItemDto>>> futures =
                        threadPoolConfig.getPool().invokeAll(callables, 30, TimeUnit.SECONDS);
                for (Future<List<ApplySampleItemDto>> future : futures) {
                    list.addAll(future.get());
                }
            }

        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        }

        return list;
    }

    @Override
    public Map<Long, List<ApplySampleItemDto>> selectByApplySampleIdsAsMap(Collection<Long> applySampleIds) {
        return selectByApplySampleIds(applySampleIds).stream()
            .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));
    }

    @Override
    public List<ApplySampleItemDto> selectByApplyIds(Collection<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Collections.emptyList();
        }
        return applySampleItemMapper
            .selectList(new LambdaQueryWrapper<TbApplySampleItem>().in(TbApplySampleItem::getApplyId, applyIds)
                .eq(TbApplySampleItem::getStopStatus, StopTestStatus.NO_STOP_TEST.getCode()))
            .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<ApplySampleItemDto> selectByApplyIds(Collection<Long> applyIds, Boolean all) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Collections.emptyList();
        }
        return applySampleItemMapper
            .selectList(new LambdaQueryWrapper<TbApplySampleItem>().in(TbApplySampleItem::getApplyId, applyIds)
                .eq(Objects.nonNull(all) && !all, TbApplySampleItem::getStopStatus, StopTestStatus.NO_STOP_TEST.getCode()))
            .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public long addApplySampleItem(ApplySampleItemDto dto) {
        final TbApplySampleItem applySampleItem = new TbApplySampleItem();
        BeanUtils.copyProperties(dto, applySampleItem);
        applySampleItem
            .setApplySampleItemId(ObjectUtils.defaultIfNull(dto.getApplySampleItemId(), snowflakeService.genId()));
        applySampleItem.setCreateDate(new Date());
        applySampleItem.setUpdateDate(new Date());
        applySampleItem.setCreatorId(LoginUserHandler.get().getUserId());
        applySampleItem.setCreatorName(LoginUserHandler.get().getNickname());
        applySampleItem.setUpdaterId(LoginUserHandler.get().getUserId());
        applySampleItem.setUpdaterName(LoginUserHandler.get().getNickname());
        applySampleItem.setIsDelete(YesOrNoEnum.NO.getCode());

        if (applySampleItemMapper.insert(applySampleItem) < 1) {
            throw new IllegalStateException("添加申请单项目失败");
        }

        log.info("用户 [{}] 添加申请单项目 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(applySampleItem));

        return applySampleItem.getApplySampleItemId();
    }

    @Override
    public boolean deleteByApplySampleItemId(long applySampleItemId) {
        if (applySampleItemMapper.deleteById(applySampleItemId) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除申请单项目 [{}] 成功", LoginUserHandler.get().getNickname(), applySampleItemId);

        return true;
    }

    @Override
    public void deleteByApplySampleIds(Collection<Long> applySampleIds) {

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return;
        }

        final LambdaQueryWrapper<TbApplySampleItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbApplySampleItem::getApplySampleId, applySampleIds);
        applySampleItemMapper.delete(wrapper);

        log.info("用户 [{}] 删除申请单样本 [{}] 项目成功", LoginUserHandler.get().getNickname(), applySampleIds);

    }

    @Override
    public void addApplySampleItems(List<ApplySampleItemDto> applySampleItems) {
        if (CollectionUtils.isEmpty(applySampleItems)) {
            return;
        }
        applySampleItemMapper.insertBatch(applySampleItems);
    }

    @Override
    public int countByApplySampleId(long applySampleId) {
        return applySampleItemMapper
            .selectCount(
                new LambdaQueryWrapper<TbApplySampleItem>().eq(TbApplySampleItem::getApplySampleId, applySampleId))
            .intValue();
    }

    @Override
    public int countByApplyId(long applyId) {
        return applySampleItemMapper
            .selectCount(new LambdaQueryWrapper<TbApplySampleItem>().eq(TbApplySampleItem::getApplyId, applyId)
                .eq(TbApplySampleItem::getStopStatus, StopTestStatus.NO_STOP_TEST.getCode()))
            .intValue();
    }

    @Override
    public ApplySampleItemDto selectById(long applySampleItemId) {
        return convert(applySampleItemMapper.selectById(applySampleItemId));
    }

    @Override
    public List<ApplySampleItemDto> selectByIds(Collection<Long> applySampleItemIds) {
        return applySampleItemMapper.selectList(Wrappers.lambdaQuery(TbApplySampleItem.class)
            .in(TbApplySampleItem::getApplySampleItemId, applySampleItemIds)).stream().map(this::convert)
            .collect(Collectors.toList());
    }

    @Override
    public void updateBatchById(Collection<ApplySampleItemDto> applySampleItems) {
        if (CollectionUtils.isEmpty(applySampleItems)) {
            return;
        }

        for (final ApplySampleItemDto applySampleItem : applySampleItems) {
            if (Objects.isNull(applySampleItem) || Objects.isNull(applySampleItem.getApplySampleItemId())) {
                continue;
            }
            applySampleItemMapper.updateById(convert(applySampleItem));
        }
    }

    @Override
    public boolean deleteByApplySampleItemIds(Collection<Long> applySampleItemIds) {
        if (applySampleItemMapper.deleteBatchIds(applySampleItemIds) > 0) {
            log.info("用户 [{}] 专业组 [{}] 删除申请单项目 [{}] 成功", LoginUserHandler.get().getNickname(),
                LoginUserHandler.get().getGroupName(), applySampleItemIds);
            return true;
        }
        return false;
    }

    @Override
    public void deleteByApplyIds(Collection<Long> applyIds) {
        applySampleItemMapper
            .delete(Wrappers.lambdaQuery(TbApplySampleItem.class).in(TbApplySampleItem::getApplyId, applyIds));

        log.info("用户 [{}] 专业组 [{}] 删除申请单项目 [{}] 成功", LoginUserHandler.get().getNickname(),
            LoginUserHandler.get().getGroupName(), applyIds);
    }

    @Override
    public ApplySampleItemDto selectByApplySampleIdAndTestItemId(long applySampleId, long testItemId) {
        final TbApplySampleItem applySampleItem = applySampleItemMapper.selectOne(
            Wrappers.lambdaQuery(TbApplySampleItem.class).eq(TbApplySampleItem::getApplySampleId, applySampleId)
                .eq(TbApplySampleItem::getStopStatus, StopTestStatus.NO_STOP_TEST.getCode())
                .eq(TbApplySampleItem::getTestItemId, testItemId).last("limit 1"));
        return convert(applySampleItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMicrobiologySampleFeeItem(Collection<Long> needDeleteSampleFeeItemIds,
        List<ApplySampleItemDto> needUpdateFeeItems, List<ApplySampleItemDto> needAddFeeTestItems) {
        if (CollectionUtils.isNotEmpty(needDeleteSampleFeeItemIds)) {
            this.deleteByApplySampleItemIds(needDeleteSampleFeeItemIds);
        }
        if (CollectionUtils.isNotEmpty(needUpdateFeeItems)) {
            this.updateBatchById(needUpdateFeeItems);
        }
        if (CollectionUtils.isNotEmpty(needAddFeeTestItems)) {
            this.addApplySampleItems(needAddFeeTestItems);
        }

    }

    @Override
    public void updateById(ApplySampleItemDto applySampleItemDto) {
        if (Objects.isNull(applySampleItemDto)){
            return;
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        applySampleItemDto.setUpdaterId(user.getUserId());
        applySampleItemDto.setUpdaterName(user.getNickname());
        applySampleItemDto.setUpdateDate(new Date());
        TbApplySampleItem tbApplySampleItem = JSON.parseObject(JSON.toJSONString(applySampleItemDto), TbApplySampleItem.class);
        applySampleItemMapper.updateById(tbApplySampleItem);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStopStatusByApplySampleId(List<Long> applySampleIds) {
        LambdaUpdateWrapper<TbApplySampleItem> wrapper = Wrappers.lambdaUpdate(TbApplySampleItem.class)
                .in(TbApplySampleItem::getApplySampleId, applySampleIds)
                .set(TbApplySampleItem::getStopStatus, StopTestStatus.NO_STOP_TEST.getCode());
        applySampleItemMapper.update(null, wrapper);
    }

    private ApplySampleItemDto convert(TbApplySampleItem sampleItem) {
        if (Objects.isNull(sampleItem)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(sampleItem), ApplySampleItemDto.class);
    }

    private TbApplySampleItem convert(ApplySampleItemDto applySampleItem) {
        if (Objects.isNull(applySampleItem)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(applySampleItem), TbApplySampleItem.class);
    }
}
