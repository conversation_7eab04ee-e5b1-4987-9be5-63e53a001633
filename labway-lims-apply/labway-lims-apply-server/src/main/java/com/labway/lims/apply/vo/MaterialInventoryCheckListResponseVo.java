package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 盘点记录 查看
 *
 * <AUTHOR>
 * @since 2023/5/12 10:58
 */
@Getter
@Setter
public class MaterialInventoryCheckListResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否存在盘点中记录 true 存在 false 不存在
     */
    private boolean existInCheckFlag;

    /**
     * 盘点记录
     */
    private List<MaterialInventoryCheckListItemResponseVo> checkRecords;
}
