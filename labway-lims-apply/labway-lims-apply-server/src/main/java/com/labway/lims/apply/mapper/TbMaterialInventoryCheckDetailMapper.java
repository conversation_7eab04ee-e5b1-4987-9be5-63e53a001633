package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.model.TbMaterialInventoryCheckDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 物料盘点详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Mapper
public interface TbMaterialInventoryCheckDetailMapper extends BaseMapper<TbMaterialInventoryCheckDetail> {

    /**
     * 批量 插入
     */
    void batchAddMaterialInventoryCheckDetail(@Param("conditions") List<TbMaterialInventoryCheckDetail> conditions);
}
