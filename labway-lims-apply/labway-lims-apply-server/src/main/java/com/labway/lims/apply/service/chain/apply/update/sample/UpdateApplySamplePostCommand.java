package com.labway.lims.apply.service.chain.apply.update.sample;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.dto.UpdateTestApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import com.labway.lims.outsourcing.api.dto.OutsourcingSaveResultDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleResultService;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SaveResultDto;
import com.labway.lims.routine.api.dto.SimpleApplyDto;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@Setter
@Component
@Slf4j
public class UpdateApplySamplePostCommand implements Command {

    @Resource
    private SampleReportService sampleReportService;

    @Resource
    private ApplySampleService applySampleService;

    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private OutsourcingSampleResultService outsourcingSampleResultService;

    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    private static final String ROUTING_KEY = "sample_change_key";

    @DubboReference
    private RabbitMQService rabbitMQService;

    @Override
    public boolean execute(Context c) throws Exception {
        final UpdateApplyContext from = UpdateApplyContext.from(c);

        final ApplyDto apply = from.getApply();

        LoginUserHandler.User unsafe = LoginUserHandler.getUnsafe();

        /*threadPoolConfig.getPool().submit((Callable<Void>) () -> {
            try {
                LoginUserHandler.set(unsafe);
                // 刷新报告单
                refreshReport(from);
                return null;
            } finally {
                LoginUserHandler.remove();
            }
        }).get(30, TimeUnit.SECONDS);*/
        refreshReport(from);

        ApplyInfo applyInfo = new ApplyInfo();
        applyInfo.setApplyId(apply.getApplyId());
        applyInfo.setMasterBarcode(apply.getMasterBarcode());

        Optional.ofNullable(from.getUpdateApplySample()).ifPresent(m -> {
            ApplyInfo.Sample sample = new ApplyInfo.Sample();
            sample.setApplySampleId(m.getApplySampleId());
            sample.setBarcode(m.getBarcode());
            applyInfo.setSamples(Collections.singletonList(sample));
        });

        from.put(UpdateApplyContext.APPLY_INFO, applyInfo);

        return CONTINUE_PROCESSING;
    }

    private void refreshReport(UpdateApplyContext from) {
        final ApplyDto apply = from.getApply();
        if (Objects.isNull(apply)) {
            return;
        }

        final TestApplyDto testApply = from.getTestApply();
        if (!(testApply instanceof UpdateTestApplySampleDto)) {
            return;
        }

        final UpdateTestApplySampleDto updateTestApplySample = (UpdateTestApplySampleDto) testApply;
        // 是否刷新报告
        final Boolean refreshReport = updateTestApplySample.getRefreshReport();
        final Long applyId = testApply.getApplyId();
        if (Objects.isNull(applyId)) {
            return;
        }

        String itemType = from.getUpdateApplySample().getItemType();

        // 常规 和 外送 才刷新参考范围和结果
        boolean refreshReferenceAndResult =
                ItemTypeEnum.ROUTINE.name().equals(itemType) ||
                        ItemTypeEnum.OUTSOURCING.name().equals(itemType);

        // 如果改了年龄和性别那就刷新参考范围，并且刷新一下结果
        if (refreshReferenceAndResult && BooleanUtils.isTrue(from.getIsUpdateSexOrAgeOrHspOrgOrSampleType())) {
            sampleResultService.refreshReference(JSON.parseObject(JSON.toJSONString(apply), SimpleApplyDto.class));

            // updateResult(itemType, updateTestApplySample.getApplySampleId());
            updateResultByApplyId(itemType, testApply.getApplyId());
        }

        // 如果没有修改性别或年龄，且没有审核通过的样本，不刷新报告
        List<ApplySampleDto> applySamples = applySampleService.selectByApplyId(testApply.getApplyId());

        applySamples = applySamples
                .stream()
                .filter(f -> Objects.equals(f.getStatus(), SampleStatusEnum.AUDIT.getCode()))
                .collect(Collectors.toList());

        if (BooleanUtils.isTrue(refreshReport) && CollectionUtils.isNotEmpty(applySamples)) {
            for (ApplySampleDto sample : applySamples) {
                sampleReportService.refreshReport(sample);

                final ApplySampleEventDto event = new ApplySampleEventDto();
                event.setOrgId(LoginUserHandler.get().getOrgId());

                event.setHspOrgId(apply.getHspOrgId());
                event.setHspOrgCode(apply.getHspOrgCode());
                event.setHspOrgName(sample.getHspOrgName());
                event.setApplyId(sample.getApplyId());
                event.setApplySampleId(sample.getApplySampleId());
                event.setBarcode(sample.getBarcode());
                event.setExtras(Map.of());
                event.setEvent(ApplySampleEventDto.EventType.TwoCheck);

                final String json = JSON.toJSONString(event);
                rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

                log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功",
                        sample.getApplySampleId(), sample.getBarcode(), json, RabbitMQService.EXCHANGE, ROUTING_KEY);

            }
        }
    }

    public void updateResult(String itemType, Long applySampleId) {
        if (ItemTypeEnum.ROUTINE.name().equals(itemType)) {
            Optional.ofNullable(sampleService.selectByApplySampleId(applySampleId)).ifPresent(e -> {
                SampleDto sampleDto = sampleService.selectBySampleId(e.getSampleId());
                // 查询样本结果
                updateRoutineResult(sampleResultService.selectBySampleId(e.getSampleId()), sampleDto);
            });
        } else if (ItemTypeEnum.OUTSOURCING.name().equals(itemType)) {
            Optional.ofNullable(outsourcingSampleService.selectByApplySampleId(applySampleId)).ifPresent(e -> {
                updateOutsourcingResult(sampleResultService.selectBySampleId(e.getOutsourcingSampleId()));
            });
        }
    }
    public void updateResultByApplyId(String itemType, Long applyId) {
        if (ItemTypeEnum.ROUTINE.name().equals(itemType)) {
            List<SampleDto> sampleDtos = sampleService.selectByApplyId(applyId);
            Map<Long, SampleDto> sampleDtoMap = sampleDtos.stream().collect(
                    Collectors.toMap(SampleDto::getSampleId, Function.identity(), (a, b) -> a));
            sampleDtos.forEach(e -> {
                SampleDto sampleDto = sampleDtoMap.get(e.getSampleId());
                // 查询样本结果
                updateRoutineResult(sampleResultService.selectBySampleId(e.getSampleId()), sampleDto);
            });
        } else if (ItemTypeEnum.OUTSOURCING.name().equals(itemType)) {
            outsourcingSampleService.selectByApplyId(applyId).forEach(e -> {
                updateOutsourcingResult(sampleResultService.selectBySampleId(e.getOutsourcingSampleId()));
            });
        }
    }

    public void updateRoutineResult(List<SampleResultDto> sampleResultDtos, SampleDto sampleDto) {
        List<SaveResultDto> saveResultDtos = sampleResultDtos.stream().map(m -> {
            SaveResultDto result = new SaveResultDto();
            result.setSampleId(m.getSampleId());
            result.setApplySampleId(m.getApplySampleId());
            result.setApplyId(m.getApplyId());
            result.setReportItemId(m.getReportItemId());
            result.setReportItemCode(m.getReportItemCode());
            result.setResult(m.getResult());
            result.setDate(new Date());
            // result.setSampleNo(m.getSampleNo());
            // result.setGroupId(m.getGroupId());
            if (m.getInstrumentId() < 1L) {
                result.setInstrumentId(sampleDto.getInstrumentId());
            } else {
                result.setInstrumentId(m.getInstrumentId());
            }
            result.setApplySampleUpdate(true);
            return result;
        }).collect(Collectors.toList());
        sampleResultService.saveResults(saveResultDtos, SaveResultSourceEnum.MACHINE);
        /*for (SaveResultDto saveResultDto : saveResultDtos) {
            try {
                sampleResultService.saveResult(saveResultDto, SaveResultSourceEnum.FRONT);
            } catch (Exception ignore) {
                // 此处只更新结果，主要是刷新提示内容
            }
        }*/
    }

    public void updateOutsourcingResult(List<SampleResultDto> sampleResultDtos) {
        List<OutsourcingSaveResultDto> saveResultDtos = sampleResultDtos.stream().map(m -> {
            final OutsourcingSaveResultDto dto = new OutsourcingSaveResultDto();
            dto.setOutsourcingSampleId(m.getSampleId());
            dto.setApplySampleId(m.getApplySampleId());
            dto.setApplyId(m.getApplyId());
            dto.setReportItemId(m.getReportItemId());
            dto.setReportItemCode(m.getReportItemCode());
            dto.setResult(m.getResult());
            dto.setDate(new Date());
            dto.setApplySampleUpdate(true);
            return dto;
        }).collect(Collectors.toList());
        outsourcingSampleResultService.saveResults(saveResultDtos, SaveResultSourceEnum.MACHINE);
        /*for (OutsourcingSaveResultDto saveResultDto : saveResultDtos) {
            try {
                outsourcingSampleResultService.saveResult(saveResultDto, SaveResultSourceEnum.FRONT);
            } catch (Exception ignore) {
                // 此处只更新结果，主要是刷新提示内容
            }
        }*/
    }
}
