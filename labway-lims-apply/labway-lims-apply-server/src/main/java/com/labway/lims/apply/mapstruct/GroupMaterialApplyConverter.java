package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.GroupMaterialApplyDto;
import com.labway.lims.apply.model.TbGroupMaterialApply;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 物料申领单 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface GroupMaterialApplyConverter {

    GroupMaterialApplyDto groupMaterialApplyDtoFromTbObj(TbGroupMaterialApply obj);

    TbGroupMaterialApply tbGroupMaterialApplyFromTbObjDto(GroupMaterialApplyDto obj);

    List<GroupMaterialApplyDto> groupMaterialApplyDtoListFromTbObj(List<TbGroupMaterialApply> list);

}
