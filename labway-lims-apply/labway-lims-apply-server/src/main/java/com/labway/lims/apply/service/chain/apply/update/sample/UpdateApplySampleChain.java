package com.labway.lims.apply.service.chain.apply.update.sample;

import com.labway.lims.apply.service.chain.apply.update.UpdateFlowCommand;
import com.labway.lims.apply.service.chain.apply.update.UpdateSaveApplyCommand;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class UpdateApplySampleChain extends ChainBase implements InitializingBean {

    @Resource
    private CheckUpdateApplySampleParamCommand checkUpdateApplySampleParamCommand;
    @Resource
    private UpdateFlowCommand updateFlowCommand;
    @Resource
    private UpdateSaveApplyCommand updateSaveApplyCommand;
    @Resource
    private UpdateSaveApplySampleCommand updateSaveApplySampleCommand;
    @Resource
    private FillApplySampleDiffCommand fillApplySampleDiffCommand;
    @Resource
    private UpdateApplySamplePostCommand updateApplySamplePostCommand;
    @Resource
    private SampleAuditRabbitMqCommand sampleAuditRabbitMqCommand;

    @Resource
    private UpdateSaveSampleCommand updateSaveSampleCommand;
    @Resource
    private UpdateSampleResultCommand updateSampleResultCommand;
    @Override
    public void afterPropertiesSet() throws Exception {

        // 检查参数
        addCommand(checkUpdateApplySampleParamCommand);

        // 填充申请信息
        addCommand(fillApplySampleDiffCommand);

        // 保存申请
        addCommand(updateSaveApplyCommand);

        // 修改不同的样本信息
        addCommand(updateSaveSampleCommand);

        // 修改样本结果信息
        addCommand(updateSampleResultCommand);

        // 修改申请单样本信息
        addCommand(updateSaveApplySampleCommand);

        // 流水
        addCommand(updateFlowCommand);

        // 后置处理
        addCommand(updateApplySamplePostCommand);

        // 发送重新生成报告消息
        addCommand(sampleAuditRabbitMqCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);

    }
}
