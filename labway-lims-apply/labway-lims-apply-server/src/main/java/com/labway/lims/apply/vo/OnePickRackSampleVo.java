package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * 响应体
 */
@Getter
@Setter
public class OnePickRackSampleVo {
    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 检验项目名称
     */
    private String testItemNames;

    /**
     * 样本类型
     */
    private String sampleType;


    /**
     * 容器类型
     */
    private String tube;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 患者年龄
     */
    private String patientAge;

    /**
     * 送检机构名称
     */
    private String hspOrgName;
}
