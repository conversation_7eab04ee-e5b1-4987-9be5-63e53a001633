package com.labway.lims.apply.controller.it8000;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.RackTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.service.chain.sample.archive.add.SampleArchiveAddChain;
import com.labway.lims.apply.service.chain.sample.archive.add.SampleArchiveAddContext;
import com.labway.lims.apply.vo.IT8000HandleVo;
import com.labway.lims.apply.vo.RackArchiveAddRequestVo;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.RefrigeratorDto;
import com.labway.lims.base.api.enums.RackStatusEnum;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.RackService;
import com.labway.lims.base.api.service.RefrigeratorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 归档
 */
@Slf4j
@Component
class ArchiveAction implements ActionStrategy {

    /**
     * 流水线冰箱编码
     */
    private static final String ROCHE_REFRIGERATOR_CODE = "Roche_Refrigerator";

    @Resource
    private ApplySampleService applySampleService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private RackService rackService;
    @Resource
    private SampleArchiveAddChain sampleArchiveAddChain;
    @DubboReference
    private RefrigeratorService refrigeratorService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private GroupService groupService;
    @Resource
    private ArchiveAction self;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Object action(IT8000HandleVo vo) throws Exception {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final String barcode = vo.getExtras().getString("barcode");
        String rackCode = vo.getExtras().getString("rackCode");

        if (StringUtils.isAnyBlank(barcode, rackCode)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException(String.format("条码 [%s] 不存在", barcode));
        }

        if (applySamples.size() != 1) {
            throw new IllegalArgumentException(String.format("条码 [%s] 查询到 [%s] 记录，无法确定专业组",
                    barcode, applySamples.size()));
        }

        final RefrigeratorDto refrigerator = refrigeratorService.selectByRefrigeratorCode(ROCHE_REFRIGERATOR_CODE,
                user.getOrgId());
        if (Objects.isNull(refrigerator)) {
            throw new IllegalArgumentException(String.format("请配置罗氏流水线冰箱，冰箱编码 [%s]", ROCHE_REFRIGERATOR_CODE));
        }


        final ApplySampleDto applySample = applySamples.iterator().next();
        final ProfessionalGroupDto group = groupService.selectByGroupId(applySample.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException(String.format("专业组 [%s] 不存在",
                    applySample.getGroupName()));
        }

        rackCode = DateFormatUtils.format(new Date(), "yyMMdd")
                + rackCode + "_" + group.getGroupCode();

        final String rc = rackCode;

        final RackDto rack = threadPoolConfig.getPool().submit(() -> {
            LoginUserHandler.set(user);
            try {
                return self.getRack(rc);
            } finally {
                LoginUserHandler.remove();
            }
        }).get(10, TimeUnit.SECONDS);


        final SampleArchiveAddContext context = new SampleArchiveAddContext();
        final RackArchiveAddRequestVo request = new RackArchiveAddRequestVo();
        request.setRefrigeratorId(refrigerator.getRefrigeratorId());
        request.setRackId(rack.getRackId());
        request.setBarcode(barcode);
        request.setStartEffectiveDate(DateUtil.beginOfDay(new Date()));
        request.setEndEffectiveDate(DateUtils.addMonths(request.getStartEffectiveDate(), 3));

        context.setRackArchiveAddRequestVo(request);
        context.setUser(LoginUserHandler.get());

        context.getUser().setGroupId(applySample.getGroupId());
        context.getUser().setGroupName(applySample.getGroupName());

        try {
            sampleArchiveAddChain.execute(context);
        } finally {
            log.info("样本归档-存储耗时\n{}", context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

        return Map.of();
    }

    @Override
    public IT8000HandleVo.Action action() {
        return IT8000HandleVo.Action.ARCHIVE;
    }


    /**
     * 获取一个归档试管架
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public RackDto getRack(String rackCode) throws InterruptedException {

        lock(rackCode);

        try {
            RackDto rack = rackService.selectByRackCode(rackCode, LoginUserHandler.get().getOrgId());
            if (Objects.nonNull(rack)) {
                return rack;
            }

            rack = new RackDto();
            rack.setRackTypeCode(RackTypeEnum.ARCHIVE_RACK.getCode());
            rack.setRackTypeName(RackTypeEnum.ARCHIVE_RACK.getDesc());
            rack.setRackCode(rackCode);
            rack.setRow(10);
            rack.setColumn(10);
            rack.setStatus(RackStatusEnum.IDLE.getCode());
            rack.setEnable(YesOrNoEnum.YES.getCode());
            rack.setOrgId(LoginUserHandler.get().getOrgId());
            rack.setUpdateDate(new Date());
            rack.setCreateDate(new Date());
            rack.setUpdaterId(NumberUtils.LONG_ZERO);
            rack.setUpdaterName("Roche");
            rack.setCreatorId(NumberUtils.LONG_ZERO);
            rack.setCreatorName("Roche");
            rack.setIsDelete(YesOrNoEnum.NO.getCode());

            rack.setRackId(rackService.add(rack));

            log.info("罗氏添加归档试管架 [{}]", JSON.toJSONString(rack));

            return rack;
        } finally {
            unlock(rackCode);
        }
    }

    /**
     * 全局限流，同一个条码必须有序。如果获取锁失败会等10秒左右，如果10秒内没有获取到锁那么抛出异常
     */
    private void lock(String rackCode) throws InterruptedException {
        final String key = redisPrefix.getBasePrefix() + ArchiveAction.class.getName() + ":" + rackCode;

        final long timestamp = System.currentTimeMillis();
        final Duration timeout = Duration.ofSeconds(10);

        do {
            // 锁试管架
            if (BooleanUtils.isTrue(stringRedisTemplate.opsForValue()
                    .setIfAbsent(key, StringUtils.EMPTY, timeout))) {
                return;
            }

            Thread.yield();

            // 尝试加锁
            synchronized (this) {
                wait(20);
            }

        } while (System.currentTimeMillis() - timestamp < timeout.toMillis());

        throw new IllegalStateException(String.format("试管架 [%s] 正在添加中", rackCode));
    }

    private void unlock(String rackCode) {
        stringRedisTemplate.delete(redisPrefix.getBasePrefix() + ArchiveAction.class.getName() + ":" + rackCode);
    }
}
