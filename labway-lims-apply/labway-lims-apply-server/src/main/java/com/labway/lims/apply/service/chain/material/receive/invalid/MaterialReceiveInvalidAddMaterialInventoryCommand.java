
package com.labway.lims.apply.service.chain.material.receive.invalid;

import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 领用作废 修改 库存 物料 数量
 *
 * <AUTHOR>
 * @since 2023/5/9 10:06
 */
@Slf4j
@Component
public class MaterialReceiveInvalidAddMaterialInventoryCommand implements Command {

    @Resource
    private MaterialInventoryService materialInventoryService;

    @Override
    public boolean execute(Context context) throws Exception {
        final MaterialReceiveInvalidContext from = MaterialReceiveInvalidContext.from(context);
        var receiveRecordDto = from.getMaterialReceiveRecordDto();
        var inventoryDto = from.getMaterialInventoryDto();

        // 对应 物料的库存恢复
        BigDecimal mainUnitInventory = inventoryDto.getMainUnitInventory().add(receiveRecordDto.getReceiveMainNumber());
        BigDecimal assistUnitInventory =
            inventoryDto.getAssistUnitInventory().add(receiveRecordDto.getReceiveAssistNumber());

        MaterialInventoryDto update = new MaterialInventoryDto();
        update.setInventoryId(inventoryDto.getInventoryId());
        update.setMainUnitInventory(mainUnitInventory);
        update.setAssistUnitInventory(assistUnitInventory);

        materialInventoryService.updateByInventoryId(update);

        return CONTINUE_PROCESSING;
    }
}
