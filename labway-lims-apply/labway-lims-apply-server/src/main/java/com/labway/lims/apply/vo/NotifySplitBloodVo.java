package com.labway.lims.apply.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class NotifySplitBloodVo implements Serializable {


    // 签收机构条码
    @NotBlank(message = "签收机构条码不能为空!")
    private String signOrgCode;

    // 签收条码
    @NotBlank(message = "签收条码不能为空!")
    private String signBarcode;

    // 分血条码号
    @NotEmpty(message = "分血条码号不能为空!")
    private List<String> splitBarcodes;

    // 操作人id
    private String optUserId;
    // 操作人姓名
    private String optUserName;


}
