package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.SimpleLogisticsSampleDto;
import com.labway.lims.apply.model.TbApplyLogistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 物流申请单（补录） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbApplyLogisticsMapper extends BaseMapper<TbApplyLogistics> {

    /**
     * 根据申请单id查询 物流申请单信息 + 物流样本
     */
    List<SimpleLogisticsSampleDto> selectByApplyIds(@Param("list") Collection<Long> applyIds);

}
