package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 组间交接试管架
 */
@Getter
@Setter
public class GroupTransformRackVo {
    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 二次分拣人ID
     */
    private Long twoPickerId;

    /**
     * 二次分拣人名称
     */
    private String twoPickerName;

    /**
     * 二次分拣日期
     */
    private Date twoPickDate;

    /**
     * 样本数量
     */
    private Integer count;

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 逻辑试管架ID
     */
    private Long rackLogicId;

}
