package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.dto.SampleAbnormalStatisticsDto;
import com.labway.lims.apply.model.TbSampleAbnormal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 样本异常 Mapper
 *
 * <AUTHOR>
 * @since 2023/4/10 9:33
 */
@Mapper
public interface TbSampleAbnormalMapper extends BaseMapper<TbSampleAbnormal> {

    /**
     * 批量 插入
     */
    void batchAddTbSampleAbnormals(@Param("conditions") List<TbSampleAbnormal> conditions);

    /**
     * 根据状态查询
     */
    List<SampleAbnormalStatisticsDto> selectSampleAbnormalStatistics(@Param("beginSignDate") Date beginSignDate,
        @Param("endSignDate") Date endSignDate, @Param("orgId") Long orgId);

    /**
     * 修改 异常
     */
    void updateBySampleAbnormalIds(@Param("sampleAbnormalDto") SampleAbnormalDto sampleAbnormalDto,
        @Param("sampleAbnormalIds") Collection<Long> sampleAbnormalIds);

    List<SampleAbnormalStatisticsDto> selectSampleAbnormalStatisticsBySendDate(@Param("beginSendDate") Date beginSendDate,
        @Param("endSendDate") Date endSendDate, @Param("orgId") Long orgId);
}
