package com.labway.lims.apply.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class MT8000HandleVo implements Serializable {
    /**
     * action
     *
     * @see Action
     */
    private String action;

    /**
     * 额外参数
     */
    private JSONObject extras = new JSONObject(0);


    public enum Action {

        /**
         * 分拣。mt8000 没有一次分拣和二次分拣之说，分拣是直接分拣到某个专业小组上了
         */
        PICK,

        /**
         * mt8000上机
         */
        SCN0001,

        /**
         * 楊样本位置更新
         */
        SPN0003,

        /**
         * 归档
         */
        ARCHIVE,
        ;

    }
}
