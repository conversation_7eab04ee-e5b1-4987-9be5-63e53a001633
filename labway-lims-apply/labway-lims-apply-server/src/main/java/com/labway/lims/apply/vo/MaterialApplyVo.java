package com.labway.lims.apply.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MaterialApplyVo {
    /**
     * 物资编号
     */
    @ExcelProperty("物料编码")
    private String materialCode;

    /**
     * 物资名称
     */
    @ExcelProperty("物料名称")
    private String materialName;

    private BigDecimal applyAssistNumber;
    /**
     * 申领辅单位数量
     */
    @ExcelProperty("申领辅数量")
    private String applyAssistNumberStr;
    
    private BigDecimal applyMainNumber;
    /**
     * 申领主单位数量
     */
    @ExcelProperty("申领主数量")
    private String applyMainNumberStr;
    
    /**
     * 已出库主单位数量
     */
    private BigDecimal deliveredMainNumber;
    
    /**
     * 已出库主单位数量(字符串)
     */
    @ExcelProperty("已出库主数量")
    private String deliveredMainNumberStr;
    
    /**
     * 已出库辅单位数量
     */
    private BigDecimal deliveredAssistNumber;
    
    /**
     * 已出库辅单位数量(字符串)
     */
    @ExcelProperty("已出库辅数量")
    private String deliveredAssistNumberStr;
    
    /**
     * 待出库主单位数量
     */
    private BigDecimal pendingMainNumber;
    
    /**
     * 待出库主单位数量(字符串)
     */
    @ExcelProperty("待出库主数量")
    private String pendingMainNumberStr;
    
    /**
     * 待出库辅单位数量
     */
    private BigDecimal pendingAssistNumber;
    
    /**
     * 待出库辅单位数量(字符串)
     */
    @ExcelProperty("待出库辅数量")
    private String pendingAssistNumberStr;
}
