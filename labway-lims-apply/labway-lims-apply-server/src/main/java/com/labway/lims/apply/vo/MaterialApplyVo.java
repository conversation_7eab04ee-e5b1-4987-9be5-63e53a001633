package com.labway.lims.apply.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MaterialApplyVo {
    /**
     * 物资编号
     */
    @ExcelProperty("物料编码")
    private String materialCode;

    /**
     * 物资名称
     */
    @ExcelProperty("物料名称")
    private String materialName;

    private BigDecimal applyAssistNumber;
    /**
     * 申领辅单位数量
     */
    @ExcelProperty("申领辅数量")
    private String applyAssistNumberStr;
}
