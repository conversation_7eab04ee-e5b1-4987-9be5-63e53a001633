package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class MedicineReceiveVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 组别
     */
    private String group;

    /**
     * 药物简称
     */
    private String ab;

    /**
     * 药物名称 当值为：GermRemark 的时候，表示是细菌备注
     */
    private String name;

    /**
     * 结果
     */
    private String result;

    /**
     * 药物结果前缀
     */
    private String formula;

    /**
     * 敏感度
     */
    private String susceptibility;

    /**
     * 折点范围
     */
    private String foldPointScope;

}