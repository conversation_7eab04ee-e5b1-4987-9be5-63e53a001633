package com.labway.lims.apply.service.chain.pick.one;

import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class OnePickUpdateApplySampleItemCommand implements Command {

    @Resource
    private ApplySampleItemService applySampleItemService;

    @Override
    public boolean execute(Context context) throws Exception {
        final OnePickContext from = OnePickContext.from(context);
        final List<ApplySampleItemDto> applySampleItems = from.getApplySampleItems();

        applySampleItemService.updateBatchById(applySampleItems);
        return CONTINUE_PROCESSING;
    }
}
