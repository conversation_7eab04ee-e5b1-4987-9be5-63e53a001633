package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.model.TbApplySampleItem;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 检验项目
 * 
 * <AUTHOR>
 * @since 2023/8/11 11:45
 */
@Mapper(componentModel = "spring")
public interface ApplySampleItemConverter {

    ApplySampleItemDto applySampleItemDtoFromTbObjDto(TbApplySampleItem obj);

    List<ApplySampleItemDto> applySampleItemDtoListFromTbObjDto(List<TbApplySampleItem> list);
}
