package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 响应体
 */
@Getter
@Setter
public class OnePickRackResponseVo {
    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 分拣日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date pickDate;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 分拣人
     */
    private String pickerName;


}
