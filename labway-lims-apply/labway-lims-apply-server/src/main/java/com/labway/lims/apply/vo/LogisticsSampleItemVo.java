package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class LogisticsSampleItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 别名
     */
    private String aliasName;

    /**
     * 检验方法ID
     */
    private String examMethodCode;

    /**
     * 检验方法名称
     */
    private String examMethodName;

    /**
     * 名称缩写
     */
    private String shortName;

    /**
     * 管型 code
     */
    private String tubeCode;

    /**
     * 管型 name
     */
    private String tubeName;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 是否支持外送
     */
    private Integer enableExport;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

    /**
     * 外送机构名称
     */
    private String exportOrgName;

    /**
     * 项目类型ID
     *
     * @see ItemTypeEnum
     */
    private String itemType;


    /**
     * 样本类型ID
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;

    /**
     * 是否it3000
     */
    private Integer enableIt3000;

    /**
     * 是否计费
     */
    private Integer enableFee;

    /**
     * 收费编码
     */
    private String feeCode;

    /**
     * 收费名称
     */
    private String feeName;

    /**
     * 收费价格
     */
    private BigDecimal feePrice;

    /**
     * 财务专业组
     */
    private Long financeGroupId;

    /**
     * 财务专业组名称
     */
    private String financeGroupName;

    /**
     * 基本量
     */
    private BigDecimal basicQuantity;

    /**
     * 复查量
     */
    private BigDecimal checkQuantity;

    /**
     * 死腔量
     */
    private BigDecimal deadSpaceQuantity;

    /**
     * 存放说明
     */
    private String stashRemark;

    /**
     * 物流检验项目id
     */
    private Long applyLogisticsSampleItemId;

    /**
     * 收费数量
     */
    private Integer count;
    /**
     * 是否加急 1
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 报告项目
     */
    private List<ReportItemVo> reportItems;
}
