package com.labway.lims.apply.handler.excel;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.Objects;

/**
 * 合并列 如果列头数量 + 实际的数据列数量 不等于 endCol 则合并
 */
public final class MergeColRowWriteHandler implements RowWriteHandler {


    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {


        if (BooleanUtils.isTrue(context.getHead())) {
            return;
        }

        final int headSize = context.getWriteSheetHolder().getHead().size();

        final Row row = context.getRow();
        if (row.getLastCellNum() != 1 && (Objects.equals(headSize, 0) || Objects.equals(headSize, (int) row.getLastCellNum()))) {
            return;
        }

        CellRangeAddress cellRangeAddress = new CellRangeAddress(context.getRowIndex(), context.getRowIndex(), 0, headSize - 1);
        context.getWriteSheetHolder().getSheet().addMergedRegionUnsafe(cellRangeAddress);

    }

}
