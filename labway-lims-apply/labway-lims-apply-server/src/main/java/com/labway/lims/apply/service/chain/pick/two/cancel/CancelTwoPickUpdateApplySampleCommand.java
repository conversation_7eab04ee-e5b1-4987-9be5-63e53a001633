package com.labway.lims.apply.service.chain.pick.two.cancel;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 更新样本
 */
@Slf4j
@Component
public class CancelTwoPickUpdateApplySampleCommand implements Command, InitializingBean {
    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final CancelTwoPickContext context = CancelTwoPickContext.from(c);

        final ApplySampleDto unpickApplySample = context.getUnpickApplySample();
        // 如果存在还未分拣的申请单样本，那么项目会合并过去，这里不用更新申请单样本信息
        if (Objects.nonNull(unpickApplySample)) {
            return CONTINUE_PROCESSING;
        }

        final ApplySampleDto as = new ApplySampleDto();
        as.setIsTwoPick(YesOrNoEnum.NO.getCode());
        as.setTwoPickDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        as.setTwoPickerId(NumberUtils.LONG_ZERO);
        as.setTwoPickerName(StringUtils.EMPTY);
        as.setApplySampleId(context.getApplySampleId());
        as.setStatus(SampleStatusEnum.ENTER.getCode());

        if (context.isFromBloodCulture(context.getApplySampleId())) {
            as.setItemType(ItemTypeEnum.BLOOD_CULTURE.name());
        }

        applySampleService.updateByApplySampleId(as);

        return CONTINUE_PROCESSING;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
