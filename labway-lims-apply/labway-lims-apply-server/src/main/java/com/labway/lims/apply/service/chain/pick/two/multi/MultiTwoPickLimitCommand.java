package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.api.RedisPrefix;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 限制
 */
@Slf4j
@Component
class MultiTwoPickLimitCommand implements Command, Filter {
    private static final String MARK = "MULTI-PICK:";

    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context c) throws Exception {

        final MultiTwoPickContext context = MultiTwoPickContext.from(c);

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(redisPrefix.getBasePrefix() + MARK + context.getRackLogicId(),
                StringUtils.EMPTY, Duration.ofMinutes(5)))) {
            throw new IllegalStateException("正在批量二次分拣中");
        }

        context.put(MARK, StringUtils.EMPTY);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        final MultiTwoPickContext context = MultiTwoPickContext.from(c);
        if (context.containsKey(MARK)) {
            stringRedisTemplate.delete(redisPrefix.getBasePrefix() + MARK + context.getRackLogicId());
        }
        return CONTINUE_PROCESSING;
    }
}
