package com.labway.lims.apply.service.chain.pick.two;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto.EventType;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 发送消息到mq
 */
@Slf4j
@Component
public class TwoPickRabbitMQCommand implements Command {
    @Resource
    private RabbitMQService rabbitMQService;

    public static final String EXCHANGE = RabbitMQService.EXCHANGE;
    public static final String ROUTING_KEY = "sample_change_key";

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);

        for (ApplySampleTwoPickDto e : context.getApplySampleTwoPicks()) {
            final ApplySampleEventDto event = new ApplySampleEventDto();
            event.setEvent(EventType.TwoPick);
            event.setOrgId(LoginUserHandler.get().getOrgId());
            event.setHspOrgId(context.getApply().getHspOrgId());
            event.setHspOrgCode(context.getApply().getHspOrgCode());
            event.setHspOrgName(context.getApply().getHspOrgName());
            event.setApplyId(e.getApplyId());
            event.setApplySampleId(e.getApplySampleId());
            event.setBarcode(e.getBarcode());
            event.setExtras(Map.of("sampleNo", e.getSampleNo(),
					// 项目类型：样本的项目类型
		            ApplySampleEventDto.ExtrasConstants.ITEM_TYPE, e.getItemType()));

            final String json = JSON.toJSONString(event);
            rabbitMQService.convertAndSend(EXCHANGE, ROUTING_KEY, json);

            log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功",
                    e.getApplySampleId(), e.getBarcode(), json, EXCHANGE, ROUTING_KEY);
        }


        return CONTINUE_PROCESSING;
    }

}
