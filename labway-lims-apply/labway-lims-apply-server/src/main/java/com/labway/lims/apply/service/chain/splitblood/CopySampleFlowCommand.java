package com.labway.lims.apply.service.chain.splitblood;

import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.service.SampleFlowService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 复制流水
 *
 * <AUTHOR>
 */
@Component
public class CopySampleFlowCommand implements Command, Filter {


    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private CheckCanSplitCommand checkCanSplitCommand;
    @DubboReference
    private SnowflakeService snowflakeService;


    @Override
    public boolean execute(Context c) throws Exception {

        final SplitBloodContext context = SplitBloodContext.from(c);

        // 不支持分血 那么跳过
        if (!context.isSupportedSplitBlood()) {
            return CONTINUE_PROCESSING;
        }


        final List<Long> applySampleIds = context.getApplySampleIds();
        applySampleIds.removeIf(e -> Objects.equals(e, context.getApplySampleId()));

        // 复制条环环节
        for (Long applySampleId : applySampleIds) {
            sampleFlowService.copySampleFlows(context.getApplySampleId(), applySampleId);
        }

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
