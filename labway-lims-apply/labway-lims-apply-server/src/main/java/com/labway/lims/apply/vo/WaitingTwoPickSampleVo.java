package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 等待二次分拣的样本
 */
@Getter
@Setter
public class WaitingTwoPickSampleVo {

    /**
     * 开始接收日期
     */
    private Date beginReceiveDate;

    /**
     * 结束接收日期
     */
    private Date endReceiveDate;
    /**
     * 项目类型
     * <p>
     * 不传为默认 排除 微生物、院感
     * <p>
     * INFECTION 院感
     * <p>
     * MICROBIOLOGY 微生物
     * 
     */
    private String itemType;
}
