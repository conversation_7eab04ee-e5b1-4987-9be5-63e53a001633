package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 二次分拣
 */
@Getter
@Setter
public class TwoPickVo {

    /**
     * 申请单样本
     */
    private Long applySampleId;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 专业组小组名称
     */
    private String instrumentGroupName;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 检验项目
     */
    private List<String> testItemNames;
    /**
     * 检验项目ids
     */
    private List<Long> testItemIds;

    /**
     * 二次分拣日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date twoPickDate;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 管型
     */
    private String tubeName;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 二次分拣颜色
     */
    @Compare("二次分拣颜色")
    private String secondSortColor;

    /**
     * 免疫二次分拣日期
     */
    private String immunityPickDate;
}
