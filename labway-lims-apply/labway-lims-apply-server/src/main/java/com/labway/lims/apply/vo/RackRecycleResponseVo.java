package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 试管架回收 试管架信息
 * 
 * <AUTHOR>
 * @since 2023/4/6 16:52
 */
@Getter
@Setter
public class RackRecycleResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 试管架 Id
     */
    private Long rackId;
    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 试管架类型编码
     */
    private String rackTypeCode;
    /**
     * 试管架类型名称
     */
    private String rackTypeName;
    /**
     * 多少行
     */
    private Integer row;

    /**
     * 多少列
     */
    private Integer column;

    // --------------RackLogicDto--------------------
    /**
     * 所在环节或位置。
     * 
     * @see RackLogicPositionEnum
     * 
     */
    private Integer position;

    private String positionMeaning;

    /**
     * 当前交接人
     */
    private String currenHandover;

    /**
     * 当前专业组
     */
    private Long currentGroupId;

    /**
     * 当前专业组
     */
    private String currentGroupName;

}
