package com.labway.lims.apply.service.chain.material.inventory.start;

import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.service.MaterialInventoryCheckService;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 开始盘点 检查信息
 * 
 * <AUTHOR>
 * @since 2023/5/11 20:28
 */
@Slf4j
@Component
public class MaterialInventoryStartCheckCheckParamCommand implements Command {

    @Resource
    private MaterialInventoryService materialInventoryService;

    @Resource
    private MaterialInventoryCheckService materialInventoryCheckService;

    @Override
    public boolean execute(Context context) throws Exception {

        final MaterialInventoryStartCheckContext from = MaterialInventoryStartCheckContext.from(context);
        var user = from.getUser();
        var checkTime = from.getCheckTime();

        if (Objects.isNull(checkTime)) {
            throw new LimsException("盘点失败:不确定盘点时间");
        }

        String check = materialInventoryCheckService.isCheck(user.getGroupId());
        if (StringUtils.isNotBlank(check)) {
            throw new LimsException(String.format(" [%s] 存在盘点中记录,无法开始盘点", check));
        }

        // 当前专业组所有库存
        final List<MaterialInventoryDto> materialInventoryDtos =
            materialInventoryService.selectByGroupId(user.getGroupId());

        if (CollectionUtils.isEmpty(materialInventoryDtos)) {
            throw new LimsException("盘点失败:暂无库存,没有盘点必要");
        }

        // 补充 物料库存信息
        from.put(MaterialInventoryStartCheckContext.MATERIAL_INVENTORY, materialInventoryDtos);

        return CONTINUE_PROCESSING;
    }
}
