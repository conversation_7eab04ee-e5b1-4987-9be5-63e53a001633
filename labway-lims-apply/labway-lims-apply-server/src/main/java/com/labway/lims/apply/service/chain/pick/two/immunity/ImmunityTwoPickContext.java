package com.labway.lims.apply.service.chain.pick.two.immunity;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ImmunityTwoPickDto;
import com.labway.lims.apply.service.chain.pick.two.TwoPickContext;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.chain.Context;

import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

/**
 * <pre>
 * ImmunityTwoPickContext
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/30 14:12
 */
@Getter
@Setter
@Accessors(chain = true)
public class ImmunityTwoPickContext extends TwoPickContext {

    public static final String IMMUNITY_TWO_PICK_DATE = "IMMUNITY_TWO_PICK_DATE_" + IdUtil.objectId();
    /**
     * 是否是免疫二次分拣
     */
    public static final String IS_IMMUNITY_TWO_PICK = "IS_IMMUNITY_TWO_PICK" + IdUtil.objectId();

    public ImmunityTwoPickContext(ImmunityTwoPickDto twoPick) {
        super(twoPick);
        if (Objects.nonNull(twoPick) && Objects.nonNull(twoPick.getTwoPickDate())) {
            super.setTwoPickDate(twoPick.getTwoPickDate());
        }
        put(IMMUNITY_TWO_PICK_DATE, new HashMap<Long, Date>());
    }

    public static ImmunityTwoPickContext from(Context context) {
        return (ImmunityTwoPickContext) context;
    }

    public ImmunityTwoPickDto getTwoPick() {
        return (ImmunityTwoPickDto) super.getTwoPick();
    }

    @SuppressWarnings("unchecked")
    public HashMap<Long, Date> getImmunityTwoPickDate() {
        return (HashMap<Long, Date>) super.get(IMMUNITY_TWO_PICK_DATE);
    }

    /**
     * 标记免疫二次分拣
     */
    public void markImmunityTwoPick() {
        put(IS_IMMUNITY_TWO_PICK, true);
    }

    /**
     * 是否是二次分拣
     */
    public boolean isImmunityTwoPick() {
        return Boolean.TRUE.equals(get(IS_IMMUNITY_TWO_PICK));
    }
}
