package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 物流申请单样本
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_apply_logistics_sample")
public class TbApplyLogisticsSample implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @TableId
    private Long applyLogisticsSampleId;

    /**
     * 申请单
     */
    private Long applyLogisticsId;

    /**
     * 条码
     */
    private String barcode;
    /**
     * 0: 待补录
     * 1: 待复核
     * 2: 待双输复核
     * 3: 已复核
     * 4: 已双输复核
     */
    private Integer status;
    /**
     * 一旦补录完成，就有了申请单id
     */
    private Long applyId;
    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1: 删除 0：未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

}
