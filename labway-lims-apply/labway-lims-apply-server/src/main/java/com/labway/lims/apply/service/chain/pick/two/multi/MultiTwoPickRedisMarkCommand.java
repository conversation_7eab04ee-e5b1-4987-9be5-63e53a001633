package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.service.chain.pick.two.TwoPickContext;
import com.labway.lims.apply.service.chain.pick.two.TwoPickRedisMarkCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * redis 标记一下
 */
@Slf4j
@Component
public class MultiTwoPickRedisMarkCommand implements Command {
    @Resource
    private TwoPickRedisMarkCommand twoPickRedisMarkCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final MultiTwoPickContext context = MultiTwoPickContext.from(c);

        final TwoPickContext ctx = new TwoPickContext(new TwoPickDto());
        ctx.getApplySampleTwoPicks().addAll(context.getApplySampleTwoPicks());
        twoPickRedisMarkCommand.execute(ctx);

        return CONTINUE_PROCESSING;
    }

}
