package com.labway.lims.apply.controller.pda;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.PdaApplySampleItemDto;
import com.labway.lims.apply.api.service.pda.PdaApplySampleItemService;
import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.api.service.pda.PdaTobeConfirmedApplyService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@RestController
@RequestMapping("/pda/items")
public class PdaApplySampleItemController extends BaseController {

    @Resource
    private PdaApplySampleItemService pdaApplySampleItemService;
    @Resource
    private PdaTobeConfirmedApplyService pdaTobeConfirmedApplyService;

    @Resource
    private PdaApplyService pdaApplyService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @GetMapping("/select")
    public Object selectByApplyId(@RequestParam Long pdaApplyId) {
        return pdaApplySampleItemService.selectByPdaApplyId(pdaApplyId);
    }

    /**
     * 修改申请单信息
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/delete-sample-item")
    public Object deleteSampleItem(@RequestBody List<Long> applySampleItemIds) {

        if (CollectionUtils.isEmpty(applySampleItemIds)) {
            throw new IllegalArgumentException("PDA申请单项目ID不能为空");
        }

        final List<PdaApplySampleItemDto> pdaApplySampleItemDtos = pdaApplySampleItemService.selectByPdaApplyItemIds(applySampleItemIds);

        if (CollectionUtils.isEmpty(pdaApplySampleItemDtos)) {
            throw new IllegalArgumentException("申请单项目不存在");
        }

        final Optional<Long> pdaApplyIdOptional = pdaApplySampleItemDtos.stream().map(PdaApplySampleItemDto::getPdaApplyId).findFirst();
        if (pdaApplyIdOptional.isEmpty()) {
            throw new IllegalArgumentException("申请单不存在");
        }

        final PdaApplyDto pdaApplyDto = pdaApplyService.selectById(pdaApplyIdOptional.get());

        final LoginUserHandler.User user = LoginUserHandler.get();

        final String key = pdaTobeConfirmedApplyService.getAbolishLock(user.getOrgId(), pdaApplyDto.getMasterBarcode());

        try {
            pdaApplySampleItemService.deleteByApplySampleItem(applySampleItemIds);
            if (Objects.equals(pdaApplySampleItemService.selectApplyItemCount(pdaApplyDto.getPdaApplyId()), NumberUtils.LONG_ZERO)) {
                // 删除申请单
                pdaApplyService.deleteByPdaApplyId(pdaApplyDto.getPdaApplyId());
                // 删除确认单
                pdaTobeConfirmedApplyService.deleteByMasterBarcode(pdaApplyDto.getMasterBarcode());
            }
            return pdaApplyDto.getPdaApplyId();
        } finally {
            stringRedisTemplate.delete(key);
        }
    }
}
