package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleImageDto;
import com.labway.lims.apply.api.service.ApplySampleImageService;
import com.labway.lims.apply.mapper.ApplySampleImageMapper;
import com.labway.lims.apply.model.ApplySampleImage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 申请单样本图片表(ApplySampleImage)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-04 12:32:19
 */
@Slf4j
@DubboService
public class ApplySampleImageServiceImpl implements ApplySampleImageService {
    @Resource
    private ApplySampleImageMapper applySampleImageMapper;

    @DubboReference
    private SnowflakeService snowflakeService;


    /**
     * 通过ID查询单条数据
     *
     * @param applySampleImageId 主键
     * @return 实例对象
     */
    @Override
    public ApplySampleImageDto queryById(Long applySampleImageId) {
        ApplySampleImage applySampleImage = this.applySampleImageMapper.queryById(applySampleImageId);
        return JSONObject.parseObject(JSONObject.toJSONString(applySampleImage), ApplySampleImageDto.class);
    }


    /**
     * 新增数据
     *
     * @param applySampleImageDto 实例对象
     * @return 实例对象
     */
    @Override
    public Integer insert(ApplySampleImageDto applySampleImageDto) {
        ApplySampleImage applySampleImage = JSONObject.parseObject(JSONObject.toJSONString(applySampleImageDto), ApplySampleImage.class);
        return this.applySampleImageMapper.insert(applySampleImage);
    }

    /**
     * 修改数据
     *
     * @param applySampleImageDto 实例对象
     * @return 实例对象
     */
    @Override
    public Integer update(ApplySampleImageDto applySampleImageDto) {
        ApplySampleImage applySampleImage = JSONObject.parseObject(JSONObject.toJSONString(applySampleImageDto), ApplySampleImage.class);
        return this.applySampleImageMapper.update(applySampleImage);
    }

    /**
     * 通过主键删除数据
     *
     * @param applySampleImageId 主键
     * @return 是否成功
     */
    @Override
    public Integer deleteById(Long applySampleImageId) {
        return applySampleImageMapper.deleteById(applySampleImageId);
    }

    @Override
    public Integer insertBatch(List<ApplySampleImageDto> applySampleImageDtos) {
        if (CollectionUtils.isEmpty(applySampleImageDtos)){
            return NumberUtils.INTEGER_ZERO;
        }

        Date now = new Date();
        LoginUserHandler.User user = LoginUserHandler.get();

        List<ApplySampleImage> applySampleImages = JSONObject.parseArray(JSONObject.toJSONString(applySampleImageDtos), ApplySampleImage.class);

        applySampleImages.forEach(e->{
            e.setApplySampleImageId(Optional.ofNullable(e.getApplySampleImageId()).orElseGet(() -> snowflakeService.genId()));
            e.setCreatorId(user.getUserId());
            e.setCreatorName(user.getNickname());
            e.setUpdaterId(user.getUserId());
            e.setUpdaterName(user.getNickname());
            e.setCreateDate(now);
            e.setUpdateDate(now);
            e.setIsDelete(YesOrNoEnum.NO.getCode());
        });


        return applySampleImageMapper.insertBatch(applySampleImages);
    }

    /**
     * 根据申请单样本id查询
     *
     * @param sampleIds
     * @return
     */
    @Override
    public List<ApplySampleImageDto> queryByApplySampleIds(List<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)){
            return Collections.emptyList();

        }

        List<ApplySampleImage> applySampleImages = applySampleImageMapper.selectList(Wrappers.lambdaQuery(ApplySampleImage.class)
                .in(ApplySampleImage::getApplySampleId, sampleIds)
                .eq(ApplySampleImage::getIsDelete, YesOrNoEnum.NO.getCode()));
        if (CollectionUtils.isEmpty(applySampleImages)){
            return Collections.emptyList();
        }

        return JSONObject.parseArray(JSONObject.toJSONString(applySampleImages), ApplySampleImageDto.class);
    }


}

