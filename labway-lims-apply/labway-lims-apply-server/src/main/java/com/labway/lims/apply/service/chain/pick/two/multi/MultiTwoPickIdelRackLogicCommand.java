package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.base.api.service.RackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 逻辑试管架标记废弃
 */
@Slf4j
@Component
public class MultiTwoPickIdelRackLogicCommand implements Command {

    @Resource
    private RackLogicService rackLogicService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @DubboReference
    private RackService rackService;

    @Override
    public boolean execute(Context c) throws Exception {

        final MultiTwoPickContext context = MultiTwoPickContext.from(c);
        // 实际样本，可能加急拆样本 （组件交接不在此列，为未分拣状态）
        final Set<String> applySampleTwoPicksBarcodeSet = context.getApplySampleTwoPicks().stream().map(ApplySampleTwoPickDto::getBarcode).collect(Collectors.toSet());
        // 当前试管架上的样本
        final Set<String> rackLogicTwoPicksBarcodeSet = context.getApplySamples().stream().map(ApplySampleDto::getBarcode).collect(Collectors.toSet());

        // 如果实际分拣数量小于试管架上的样本，那表示分拣过程中有错误。不修改试管架状态
        if (applySampleTwoPicksBarcodeSet.size() < rackLogicTwoPicksBarcodeSet.size()) {
            return CONTINUE_PROCESSING;
        }

        // 直接修改试管架状态
        final RackLogicDto mrl = new RackLogicDto();
        mrl.setRackLogicId(context.getRackLogic().getRackLogicId());
        mrl.setPosition(RackLogicPositionEnum.END.getCode());
        mrl.setUpdateDate(new Date());
        mrl.setUpdaterId(LoginUserHandler.get().getUserId());
        mrl.setUpdaterName(LoginUserHandler.get().getNickname());
        boolean updateRackLogic = rackLogicService.updateByRackLogicId(mrl);
        log.info("开始执行更新逻辑试管架，更新RackLogicId【{}】，更新状态【{}】",context.getRackLogic().getRackLogicId(),updateRackLogic);


        // 删除试管架占用
//        rackLogicSpaceService.deleteByRackLogicId(context.getRackLogic().getRackLogicId());


        return CONTINUE_PROCESSING;
    }


}
