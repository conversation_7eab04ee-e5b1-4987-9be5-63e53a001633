package com.labway.lims.apply.service.chain.material.delivery.business;

import com.labway.lims.api.enums.apply.MaterialApplyStatusEnum;
import com.labway.lims.apply.api.dto.BusinessCenterDeliveryDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDetailDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryRecordDetailDto;
import com.labway.lims.apply.api.service.GroupMaterialApplyDetailService;
import com.labway.lims.apply.api.service.GroupMaterialApplyService;
import com.labway.lims.apply.api.service.MaterialDeliveryRecordService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务中台物料出库修改申请单状态
 *
 * <AUTHOR>
 * @since 2023/6/8 15:35
 */
@Slf4j
@Component
public class BusinessCenterDeliveryUpdateApplyStatusCommand implements Command {

    @Resource
    private GroupMaterialApplyService groupMaterialApplyService;
    @Resource
    private GroupMaterialApplyDetailService groupMaterialApplyDetailService;

    @Resource
    private MaterialDeliveryRecordService materialDeliveryRecordService;

    @Override
    public boolean execute(Context context) throws Exception {
        final BusinessCenterDeliveryContext from = BusinessCenterDeliveryContext.from(context);
        final GroupMaterialApplyDto groupMaterialApply = from.getGroupMaterialApply();
        final BusinessCenterDeliveryDto deliveryDto = from.getDeliveryDto();

        // 是否全部出库
        final boolean isAllOut = Objects.equals(deliveryDto.getIsAllOutFlag(), Boolean.TRUE);
        GroupMaterialApplyDto update = new GroupMaterialApplyDto();
        update.setApplyId(groupMaterialApply.getApplyId());
        update.setStatus(isAllOut ? MaterialApplyStatusEnum.ALL_OUT.getCode() : MaterialApplyStatusEnum.PART_OUT.getCode());
        groupMaterialApplyService.updateByApplyId(update);

        // dev-1.1.3.2  更新物料出库详情
        // 查询申领信息
        final Map<String, GroupMaterialApplyDetailDto> groupMaterialApplyDetailDtoMap = groupMaterialApplyDetailService.selectByApplyNo(groupMaterialApply.getApplyNo(), deliveryDto.getOrgId())
                .stream().collect(Collectors.toMap(GroupMaterialApplyDetailDto::getMaterialCode, Function.identity(), (a, b) -> b));

        final Map<String, MaterialNumber> materialCodeNumberMap = new HashMap<>();
        // 如果  出库单状态为已出库 则不需要查询历史出库数据， 直接改为全部出库
        if (!isAllOut) {
            // 查询已出库信息
            final List<MaterialDeliveryRecordDetailDto> materialDeliveryRecordDetailDtos = materialDeliveryRecordService.outBoundOrders(groupMaterialApply.getApplyId(), deliveryDto.getOrgId());

            // 合并每次出库的物料数量
            for (MaterialDeliveryRecordDetailDto e : materialDeliveryRecordDetailDtos) {
                for (MaterialDeliveryDetailDto materialDeliveryDetailDto : e.getItems()) {
                    materialCodeNumberMap.merge(materialDeliveryDetailDto.getMaterialCode(),
                            new MaterialNumber(materialDeliveryDetailDto.getDeliveryMainNumberDecimal(), materialDeliveryDetailDto.getDeliveryAssistNumber()),
                            // 物料编码相同， 两次出库数量相加
                            (a, b) -> new MaterialNumber(a.getApplyMainNumber().add(b.getApplyMainNumber()), a.getApplyAssistNumber().add(b.getApplyAssistNumber())));
                }
            }
        }
        // 收集更新的状态
        final List<GroupMaterialApplyDetailDto> collect = deliveryDto.getDeliveryItemList().stream()
                .filter(e -> Objects.nonNull(groupMaterialApplyDetailDtoMap.get(e.getMaterialCode())))
                .map(e -> {
                    final GroupMaterialApplyDetailDto groupMaterialDetailDto = groupMaterialApplyDetailDtoMap.get(e.getMaterialCode());

                    final MaterialNumber materialNumber = materialCodeNumberMap.getOrDefault(e.getMaterialCode(), new MaterialNumber());

                    // 物料出库状态：
                    // 如果  出库单状态为已出库，则状态直接改为已出库
                    final Integer status = isAllOut ? MaterialApplyStatusEnum.ALL_OUT.getCode() :
                            // 否则：1.申请的主数量 == 出库的主数量 && 申请的辅数量 == 出库的辅数量  则状态为全部出库， 反之为部分出库
                            Objects.equals(groupMaterialDetailDto.getApplyAssistNumber().compareTo(materialNumber.getApplyAssistNumber()), NumberUtils.INTEGER_ZERO)
		                            && Objects.equals(groupMaterialDetailDto.getApplyMainNumber().compareTo(materialNumber.getApplyMainNumber()), NumberUtils.INTEGER_ZERO) ?
                                    MaterialApplyStatusEnum.ALL_OUT.getCode() : MaterialApplyStatusEnum.PART_OUT.getCode();

                    log.info("申领单号{}，物料编码:{}， 申请主数量：{}， 出库主数量：{}，申领辅数量：{}，出库辅数量：{}",
                            groupMaterialApply.getApplyNo(), groupMaterialDetailDto.getMaterialCode(),
                            groupMaterialDetailDto.getApplyMainNumber(), e.getDeliveryMainNumberDecimal(),
                            groupMaterialDetailDto.getApplyAssistNumber(), e.getDeliveryAssistNumber());

                    final GroupMaterialApplyDetailDto groupMaterialApplyDetailDto = new GroupMaterialApplyDetailDto();
                    groupMaterialApplyDetailDto.setDetailId(groupMaterialDetailDto.getDetailId());
                    groupMaterialApplyDetailDto.setStatus(status);
                    return groupMaterialApplyDetailDto;
                }).collect(Collectors.toList());

        groupMaterialApplyDetailService.updateBatchByDetailId(collect);

        return CONTINUE_PROCESSING;
    }

    @Getter
    @Setter
    private static class MaterialNumber {
        /**
         * 主数量
         */
        private BigDecimal applyMainNumber;
        /**
         * 辅数量
         */
        private BigDecimal applyAssistNumber;

        public MaterialNumber(BigDecimal applyMainNumber, BigDecimal applyAssistNumber) {
            this.applyMainNumber = (BigDecimal) ObjectUtils.defaultIfNull(applyMainNumber, BigDecimal.ZERO);
            this.applyAssistNumber = (BigDecimal) ObjectUtils.defaultIfNull(applyAssistNumber, BigDecimal.ZERO);
        }

        public MaterialNumber() {
            this.applyMainNumber = BigDecimal.ZERO;
            this.applyAssistNumber = BigDecimal.ZERO;
        }
    }
}
