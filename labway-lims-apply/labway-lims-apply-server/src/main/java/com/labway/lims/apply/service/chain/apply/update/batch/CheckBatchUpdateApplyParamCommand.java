package com.labway.lims.apply.service.chain.apply.update.batch;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.BatchUpdateApplyDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;

import lombok.extern.slf4j.Slf4j;

/**
 * 检查参数
 * 
 * <AUTHOR>
 * @since 2024/2/21 15:12
 */
@Slf4j
@Component
public class CheckBatchUpdateApplyParamCommand implements Command {
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        BatchUpdateApplyContext from = BatchUpdateApplyContext.from(c);
        BatchUpdateApplyDto batchUpdateApplyDto = from.getBatchUpdateApplyDto();
        Set<Long> applyIds = batchUpdateApplyDto.getApplyIds();

        Long hspOrgId = batchUpdateApplyDto.getHspOrgId();
        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("选择的送检机构不存在");
        }

        List<ApplyDto> applyDtoList = applyService.selectByApplyIds(applyIds);
        // 送检机构发生修改的申请单
        List<ApplyDto> applyList = applyDtoList.stream().filter(obj -> !Objects.equals(obj.getHspOrgId(), hspOrgId))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applyDtoList)) {
            throw new IllegalStateException("需要修改申请单不存在");
        }
        if (!Objects.equals(hspOrganization.getEnable(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalStateException(String.format("送检机构 [%s] 已停用", hspOrganization.getHspOrgName()));
        }

        List<ApplySampleDto> applySampleDtoList = applySampleService
            .selectByApplyIds(applyList.stream().map(ApplyDto::getApplyId).collect(Collectors.toSet()));

        from.put(BatchUpdateApplyContext.HSP_ORG, hspOrganization);
        from.put(BatchUpdateApplyContext.APPLY, applyList);
        from.put(BatchUpdateApplyContext.APPLY_SAMPLE, applySampleDtoList);
        return CONTINUE_PROCESSING;
    }
}
