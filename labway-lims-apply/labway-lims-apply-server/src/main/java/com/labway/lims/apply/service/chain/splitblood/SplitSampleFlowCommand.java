package com.labway.lims.apply.service.chain.splitblood;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.stream.Collectors;

/**
 * 分血流水
 */
@Slf4j
@Component
public class SplitSampleFlowCommand implements Command {
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;


    @Override
    public boolean execute(Context c) throws Exception {
        final SplitBloodContext context = SplitBloodContext.from(c);

        final LinkedList<Long> ids = snowflakeService.genIds(context.getApplySampleIds().size());

        sampleFlowService.addSampleFlows(context.getApplySampleIds().stream().map(e -> SampleFlowDto.builder()
                .applyId(context.getApplySample().getApplyId())
                .sampleFlowId(ids.pop())
                .applySampleId(e)
                .operateCode(BarcodeFlowEnum.SPLIT_BLOOD.name())
                .operateName(BarcodeFlowEnum.SPLIT_BLOOD.getDesc())
                .operatorId(LoginUserHandler.get().getUserId())
                .operator(LoginUserHandler.get().getNickname())
                .barcode(context.getApplySample().getBarcode())
                .content("分血")
                .build()).collect(Collectors.toList()));

        return CONTINUE_PROCESSING;
    }
}
