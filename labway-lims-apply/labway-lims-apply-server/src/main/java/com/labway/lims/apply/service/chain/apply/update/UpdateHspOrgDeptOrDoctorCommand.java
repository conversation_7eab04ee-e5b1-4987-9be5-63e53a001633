package com.labway.lims.apply.service.chain.apply.update;

import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.base.api.dto.HspOrgDeptDto;
import com.labway.lims.base.api.dto.HspOrgDoctorDto;
import com.labway.lims.base.api.dto.HspOrgMainDto;
import com.labway.lims.base.api.service.HspOrgDeptService;
import com.labway.lims.base.api.service.HspOrgDoctorService;
import com.labway.lims.base.api.service.HspOrgMainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/27 16:36
 * <p>
 * 添加送检机构部门和医生基础数据
 */
@Slf4j
@Component
public class UpdateHspOrgDeptOrDoctorCommand implements Command {
    @DubboReference
    private HspOrgMainService hspOrgMainService;
    @DubboReference
    private HspOrgDeptService hspOrgDeptService;
    @DubboReference
    private HspOrgDoctorService hspOrgDoctorService;

    @Override
    public boolean execute(Context c) throws Exception {
        final UpdateApplyContext context = UpdateApplyContext.from(c);
        final ApplyDto apply = context.getApply();
        try {
            addHspOrgOrDeptOrDoctor(apply);

        } catch (Exception e) {
            log.error("新增送检机构科室或医生异常[{}]", e.getMessage());
        }

        return CONTINUE_PROCESSING;
    }

    public void addHspOrgOrDeptOrDoctor(ApplyDto apply) {

        final HspOrgMainDto hspOrg = hspOrgMainService.selectByHspOrgId(apply.getHspOrgId())
                .stream().findFirst().orElse(null);

        //送检机构没有维护过，新增送检机构
        if (Objects.isNull(hspOrg)) {
            final HspOrgMainDto dto = buildHspOrg(apply);
            final List<Long> orgAddIds = hspOrgMainService.addBatch(Collections.singleton(dto));

            if (StringUtils.isBlank(apply.getDept())) {
                return;
            }

            final HspOrgDeptDto dept = buildDept(apply);
            dept.setHspOrgMainId(orgAddIds.iterator().next());

            final long deptAddId = hspOrgDeptService.add(dept);

            if (StringUtils.isBlank(apply.getSendDoctorName())) {
                return;
            }

            final HspOrgDoctorDto doctor = buildDoctor(apply);
            doctor.setHspOrgDeptId(deptAddId);
            doctor.setHspOrgMainId(orgAddIds.iterator().next());

            hspOrgDoctorService.add(doctor);
        } else {

            if (StringUtils.isBlank(apply.getDept())) {
                return;
            }

            final List<HspOrgDeptDto> depts = hspOrgDeptService.selectByHspOrgMainId(hspOrg.getHspOrgMainId())
                    .stream()
                    .filter(f -> StringUtils.equalsIgnoreCase(f.getDept(), apply.getDept()))
                    .collect(Collectors.toList());
            //送检机构下的部门没有维护过，新增
            if (CollectionUtils.isEmpty(depts)) {

                final HspOrgDeptDto dept = buildDept(apply);
                dept.setHspOrgMainId(hspOrg.getHspOrgMainId());
                final long deptAddId = hspOrgDeptService.add(dept);

                //送检医生不为空，新增
                if (StringUtils.isNotBlank(apply.getSendDoctorName())) {
                    final HspOrgDoctorDto doctor = buildDoctor(apply);
                    doctor.setHspOrgDeptId(deptAddId);
                    doctor.setHspOrgMainId(hspOrg.getHspOrgMainId());

                    hspOrgDoctorService.add(doctor);
                }

            } else {

                final List<HspOrgDoctorDto> doctors = hspOrgDoctorService.selectByHspOrgDeptId(depts.iterator().next().getHspOrgDeptId())
                        .stream()
                        .filter(f -> StringUtils.equalsIgnoreCase(f.getDoctorName(), apply.getSendDoctorName()))
                        .collect(Collectors.toList());

                //没有维护过医生那么新增
                if (CollectionUtils.isEmpty(doctors)) {
                    final HspOrgDoctorDto doctor = buildDoctor(apply);
                    doctor.setHspOrgDeptId(depts.iterator().next().getHspOrgDeptId());
                    doctor.setHspOrgMainId(hspOrg.getHspOrgMainId());

                    hspOrgDoctorService.add(doctor);
                }

            }

        }

    }

    private HspOrgMainDto buildHspOrg(ApplyDto apply) {
        final HspOrgMainDto dto = new HspOrgMainDto();
        dto.setHspOrgId(apply.getHspOrgId());
        dto.setHspOrgCode(apply.getHspOrgCode());
        dto.setHspOrgName(apply.getHspOrgName());
        return dto;
    }

    private HspOrgDeptDto buildDept(ApplyDto apply) {
        final HspOrgDeptDto dept = new HspOrgDeptDto();
        dept.setHspOrgId(apply.getHspOrgId());
        dept.setHspOrgCode(apply.getHspOrgCode());
        dept.setHspOrgName(apply.getHspOrgName());
        dept.setDept(apply.getDept());
        dept.setDeptCode(StringUtils.EMPTY);
        return dept;
    }

    private HspOrgDoctorDto buildDoctor(ApplyDto apply) {
        final HspOrgDoctorDto doctor = new HspOrgDoctorDto();
        doctor.setHspOrgId(apply.getHspOrgId());
        doctor.setHspOrgCode(apply.getHspOrgCode());
        doctor.setHspOrgName(apply.getHspOrgName());
        doctor.setDeptCode(StringUtils.EMPTY);
        doctor.setDept(apply.getDept());
        doctor.setDoctorCode(apply.getSendDoctorCode());
        doctor.setDoctorName(apply.getSendDoctorName());
        return doctor;
    }
}
