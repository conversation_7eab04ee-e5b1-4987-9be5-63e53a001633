package com.labway.lims.apply.service.chain.sample.archive.add;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.RackTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.vo.RackArchiveAddRequestVo;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.RefrigeratorDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.RackService;
import com.labway.lims.base.api.service.RefrigeratorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 样本归档 -存储 检查信息
 *
 * <AUTHOR>
 * @since 2023/4/14 11:56
 */
@Slf4j
@Component
public class SampleArchiveAddCheckParamCommand implements Command {

    @DubboReference
    private RackService rackService;
    @Resource
    private ApplySampleService applySampleService;

    @DubboReference
    private RefrigeratorService refrigeratorService;

    @DubboReference
    private GroupService groupService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SampleArchiveAddContext from = SampleArchiveAddContext.from(context);
        RackArchiveAddRequestVo vo = from.getRackArchiveAddRequestVo();
        LoginUserHandler.User user = from.getUser();

        if (StringUtils.isBlank(vo.getBarcode()) || Objects.isNull(vo.getRefrigeratorId())
            || Objects.isNull(vo.getRackId()) || Objects.isNull(vo.getStartEffectiveDate())
            || Objects.isNull(vo.getEndEffectiveDate())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }

        if (vo.isFromAssign() && (Objects.isNull(vo.getRow()) || Objects.isNull(vo.getColumn()))) {
            throw new LimsException("不确定样本指定位置");
        }

        final RefrigeratorDto refrigeratorDto = refrigeratorService.selectByRefrigeratorId(vo.getRefrigeratorId());
        if (Objects.isNull(refrigeratorDto)) {
            throw new LimsException("对应冰箱不存在");
        }

        final RackDto rackDto = rackService.selectByRackId(vo.getRackId());
        if (Objects.isNull(rackDto)) {
            throw new LimsException("对应试管架不存在");
        }
        if (!RackTypeEnum.isArchiveRack(rackDto.getRackTypeCode())) {
            throw new LimsException("试管架类型不符合归档要求");
        }
        if (vo.isFromAssign() && (vo.getRow() > rackDto.getRow() || vo.getColumn() > rackDto.getColumn())) {
            throw new LimsException("无效指定位置");
        }

        // 条码对应样本
        final ApplySampleDto applySampleDto =
            applySampleService.selectByBarcodeAndGroupId(vo.getBarcode(), user.getGroupId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("条码对应样本在本专业组内不存在");
        }

        final boolean isAudit = (Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())
                || Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.AUDIT.getCode()));
        // 没有审核 && 不是被并单样本
        if (!isAudit && StringUtils.isBlank(applySampleDto.getMergeMasterBarcode())) {

            // 外送可以再分拣之后就归档
            if (groupService.checkIsOutsourcingGroup(applySampleDto.getGroupId(), user.getOrgId())) {
                if (!Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
                    throw new IllegalStateException("当前条码未完成分拣，不可归档");
                }
            } else {
                throw new IllegalStateException("当前条码未完成一审或二审，不可归档");
            }
        }
//         需要支持一个条码多次归档。
//        if (Objects.equals(applySampleDto.getIsArchive(), YesOrNoEnum.YES.getCode())) {
//            throw new IllegalStateException("条码对应样本已归档,不可重复归档");
//        }

        from.put(SampleArchiveAddContext.RACK, rackDto);
        from.put(SampleArchiveAddContext.APPLY_SAMPLE, applySampleDto);
        from.put(SampleArchiveAddContext.REFRIGERATOR, refrigeratorDto);

        return CONTINUE_PROCESSING;
    }
}
