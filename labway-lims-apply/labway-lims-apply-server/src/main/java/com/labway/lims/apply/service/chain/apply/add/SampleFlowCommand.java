package com.labway.lims.apply.service.chain.apply.add;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.HisTestApplyDto;
import com.labway.lims.apply.api.dto.HisTestApplyItemDto;
import com.labway.lims.apply.api.dto.InformationEntryTestApplyDto;
import com.labway.lims.apply.api.dto.PdaEntryTestApplyDto;
import com.labway.lims.apply.api.dto.PhysicalSampleTestApplyDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 条码环节
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@Slf4j
public class SampleFlowCommand implements Command {
    static final String line = "\n";
    static final String separator = "-";

    @Resource
    private SampleFlowService sampleFlowService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context context) throws Exception {
        final AddApplyContext from = AddApplyContext.from(context);
        final LoginUserHandler.User user = from.getUser();
        final ApplyDto apply = from.getApply();


        final List<ApplySampleDto> applySamples = from.getApplySamples();

        final Date now = new Date();


        // 构建条码环节
        final StringBuilder sb = new StringBuilder();

        if (from.getTestApply() instanceof HisTestApplyDto) {
            sb.append("[样本签收]");
        } else if (from.getTestApply() instanceof InformationEntryTestApplyDto) {
            sb.append("[申请单录入]");
        } else if (from.getTestApply() instanceof PhysicalSampleTestApplyDto) {
            sb.append("[体检签收]");
        } else if (from.getTestApply() instanceof PdaEntryTestApplyDto) {
            sb.append(String.format("[%s]", BarcodeFlowEnum.PDA_SIGN.getDesc()));
        }


        sb.append("样本新增").append(line);

        sb.append("机构: ").append(apply.getHspOrgName()).append(line);
        sb.append("姓名: ").append(apply.getPatientName()).append(line);
        sb.append("年龄: ").append(PatientAges.toText(apply)).append(line);
        sb.append("性别: ").append(SexEnum.getByCode(apply.getPatientSex()).getDesc()).append(line);
        sb.append("就诊类型: ").append(apply.getApplyTypeName()).append(line);

        if (Objects.nonNull(apply.getPatientBirthday())) {
            sb.append("出生日期: ").append(DateFormatUtils.format(apply.getPatientBirthday(), "yyyy-MM-dd")).append(line);
        }
        if (StringUtils.isNotBlank(apply.getSampleProperty())) {
            sb.append("样本性状: ").append(apply.getSampleProperty()).append(line);
        }
        if (Objects.nonNull(apply.getUrgent())) {
            sb.append("急诊状态: ").append(Objects.equals(apply.getUrgent(),
                    YesOrNoEnum.YES.getCode()) ? "加急" : "普通").append(line);
        }
        if (Objects.nonNull(apply.getSampleCount())) {
            sb.append("样本个数: ").append(apply.getSampleCount()).append(line);
        }
        if (Objects.nonNull(apply.getApplyDate())) {
            sb.append("申请时间: ").append(DateFormatUtils.format(apply.getApplyDate(), "yyyy-MM-dd HH:mm:ss")).append(line);
        }
        if (Objects.nonNull(apply.getSamplingDate())) {
            sb.append("采样时间: ").append(DateFormatUtils.format(apply.getSamplingDate(), "yyyy-MM-dd HH:mm:ss")).append(line);
        }
        if (StringUtils.isNotBlank(apply.getPatientVisitCard())) {
            sb.append("门诊/住院号: ").append(apply.getPatientVisitCard()).append(line);
        }
        if (StringUtils.isNotBlank(apply.getDept())) {
            sb.append("科室: ").append(apply.getDept()).append(line);
        }
        if (StringUtils.isNotBlank(apply.getVisitCardNo())) {
            sb.append("医保卡号: ").append(apply.getVisitCardNo()).append(line);
        }
        if (StringUtils.isNotBlank(apply.getInpatientArea())) {
            sb.append("病区: ").append(apply.getInpatientArea()).append(line);
        }
        if (StringUtils.isNotBlank(apply.getPatientBed())) {
            sb.append("床号: ").append(apply.getPatientBed()).append(line);
        }
        if (StringUtils.isNotBlank(apply.getSendDoctorName())) {
            sb.append("送检医生: ").append(apply.getSendDoctorName()).append(line);
        }
        if (StringUtils.isNotBlank(apply.getDiagnosis())) {
            sb.append("临床诊断: ").append(apply.getDiagnosis()).append(line);
        }
        if (StringUtils.isNotBlank(apply.getRemark())) {
            sb.append("备注: ").append(apply.getRemark()).append(line);
        }
        if (StringUtils.isNotBlank(apply.getPatientMobile())) {
            sb.append("电话: ").append(apply.getPatientMobile()).append(line);
        }
        if (StringUtils.isNotBlank(apply.getPatientCard())) {
            sb.append("身份证号: ").append(apply.getPatientCard()).append(line);
        }

        final List<SampleFlowDto> flows = applySamples.stream().map(m -> {
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(snowflakeService.genId());
            sampleFlow.setApplyId(apply.getApplyId());
            sampleFlow.setApplySampleId(m.getApplySampleId());
            sampleFlow.setBarcode(m.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.CREATE_BARCODE.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.CREATE_BARCODE.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(sb + getItemFlowContent(from, m.getApplySampleId()));
            sampleFlow.setCreateDate(now);
            sampleFlow.setUpdateDate(now);
            sampleFlow.setCreatorId(user.getUserId());
            sampleFlow.setCreatorName(user.getNickname());
            sampleFlow.setUpdaterId(user.getUserId());
            sampleFlow.setUpdaterName(user.getNickname());
            sampleFlow.setOrgId(user.getOrgId());
            sampleFlow.setOrgName(user.getOrgName());
            sampleFlow.setIsDelete(YesOrNoEnum.NO.getCode());
            return sampleFlow;
        }).collect(Collectors.toList());


        sampleFlowService.addSampleFlows(flows);

        log.info("用户 [{}} 专业组 [{}] 录入申请单 [{}] ", user.getNickname(), user.getGroupName(), apply.getMasterBarcode());

        return CONTINUE_PROCESSING;
    }

    private String getItemFlowContent(final AddApplyContext context, long applySampleId) {
        final TestApplyDto testApply = context.getTestApply();
        final StringBuilder sb = new StringBuilder();
        final List<TestItemDto> testItems = ObjectUtils.defaultIfNull(context.getTestItems(), Collections.emptyList());


        final Map<Long, List<ReportItemDto>> reportItems = context.getReportItems().values().stream().flatMap(Collection::stream)
                .collect(Collectors.groupingBy(ReportItemDto::getTestItemId));

        sb.append("检验项目：").append(line);

        if (testApply instanceof HisTestApplyDto) {
            final Map<String, List<HisTestApplyItemDto>> map = testApply.getItems().stream().map(e -> (HisTestApplyItemDto) e)
                    .collect(Collectors.groupingBy(HisTestApplyItemDto::getOutTestItemCode));
            for (var e : map.entrySet()) {
                final String outTestItemName = e.getValue().iterator().next().getOutTestItemName();
                sb.append(StringUtils.repeat(separator, 2)).append(outTestItemName).append(StringUtils.SPACE)
                        .append("(").append(e.getKey()).append(")").append(line);
                for (HisTestApplyItemDto k : e.getValue()) {
                    final String testItemCode = testItems.stream().filter(t -> Objects.equals(k.getTestItemId(), t.getTestItemId()))
                            .findFirst().map(TestItemDto::getTestItemCode).orElse(StringUtils.EMPTY);
                    sb.append(StringUtils.repeat(separator, 4)).append(k.getTestItemName())
                            .append("(").append(testItemCode).append(")")
                            .append(line);
                    for (ReportItemDto t : reportItems.getOrDefault(k.getTestItemId(), List.of())) {
                        sb.append(StringUtils.repeat(separator, 6)).append(t.getReportItemName()).append(StringUtils.SPACE)
                                .append("(").append(t.getReportItemCode()).append(")").append(line);
                    }
                }
            }
        } else {
            for (TestApplyDto.Item item : testApply.getItems()) {
                final String testItemCode = testItems.stream().filter(t -> Objects.equals(item.getTestItemId(), t.getTestItemId()))
                        .findFirst().map(TestItemDto::getTestItemCode).orElse(StringUtils.EMPTY);
                sb.append(StringUtils.repeat(separator, 2)).append(item.getTestItemName())
                        .append("(").append(testItemCode).append(")")
                        .append(line);
                for (ReportItemDto t : reportItems.getOrDefault(item.getTestItemId(), List.of())) {
                    sb.append(StringUtils.repeat(separator, 4)).append(t.getReportItemName()).append(StringUtils.SPACE)
                            .append("(").append(t.getReportItemCode()).append(")").append(line);
                }
            }
        }

        return sb.toString();
    }
}
