package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/9 11:19
 */
@Getter
@Setter
public class SampleTrackVo {
    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 样本阶段状态
     */
    private List<SampleTrack> sampleTracks;

    @Getter
    @Setter
    @AllArgsConstructor
    @Builder
    @NoArgsConstructor
    public static class SampleTrack {

        /**
         * 操作人
         */
        private String operator;

        /**
         * 操作日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date operatDate;

        /**
         * 操作类型
         *
         * @see BarcodeFlowEnum
         */
        private String operateCode;

        /**
         * 操作类型
         */
        private String operateName;

        /**
         * 当前状态是否完成
         *
         * @see com.labway.lims.api.enums.YesOrNoEnum
         */
        private Integer isFinish;

        /**
         * 是否显示
         * @see com.labway.lims.api.enums.YesOrNoEnum
         */
        private Integer isShow;

    }

}
