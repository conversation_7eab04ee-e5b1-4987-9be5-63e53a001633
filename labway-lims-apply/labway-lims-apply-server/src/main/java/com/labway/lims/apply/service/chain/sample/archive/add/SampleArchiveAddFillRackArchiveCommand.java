package com.labway.lims.apply.service.chain.sample.archive.add;

import com.labway.lims.api.enums.apply.OperationTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.RackArchiveDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.service.RackArchiveService;
import com.labway.lims.apply.vo.RackArchiveAddRequestVo;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.RefrigeratorDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 填充 试管架归档信息
 * 
 * <AUTHOR>
 * @since 2023/4/14 13:18
 */
@Slf4j
@Component
public class SampleArchiveAddFillRackArchiveCommand implements Command {

    @DubboReference
    private RackArchiveService rackArchiveService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SampleArchiveAddContext from = SampleArchiveAddContext.from(context);
        final RackArchiveAddRequestVo vo = from.getRackArchiveAddRequestVo();
        final RackDto rackDto = from.getRack();
        final RackLogicDto rackLogic = from.getRackLogic();
        final RefrigeratorDto refrigerator = from.getRefrigerator();

        RackArchiveDto archiveDto = rackArchiveService.selectByRackId(rackDto.getRackId());

        OperationTypeEnum operationTypeEnum = OperationTypeEnum.UPDATE;
        if (Objects.isNull(archiveDto)) {
            // 不存在 试管架归档信息 需要创建
            archiveDto = new RackArchiveDto();
            archiveDto.setRackArchiveId(snowflakeService.genId());
            archiveDto.setRackId(rackDto.getRackId());
            archiveDto.setRackLogicId(rackLogic.getRackLogicId());
            archiveDto.setRefrigeratorId(refrigerator.getRefrigeratorId());
            archiveDto.setStartEffectiveDate(vo.getStartEffectiveDate());
            archiveDto.setEndEffectiveDate(vo.getEndEffectiveDate());
            operationTypeEnum = OperationTypeEnum.CREATE;
        }

        from.put(SampleArchiveAddContext.RACK_ARCHIVE_FLAG, operationTypeEnum);
        from.put(SampleArchiveAddContext.RACK_ARCHIVE, archiveDto);

        return CONTINUE_PROCESSING;
    }
}
