package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.PhysicalSampleDto;
import com.labway.lims.apply.model.TbPhysicalSample;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 体检样本 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 15:15
 */
@Mapper(componentModel = "spring")
public interface PhysicalSampleConverter {

    PhysicalSampleDto fromTbPhysicalSample(TbPhysicalSample obj);

    List<PhysicalSampleDto> fromTbPhysicalSampleList(List<TbPhysicalSample> list);

    TbPhysicalSample tbPhysicalSampleFromTbDto(PhysicalSampleDto obj);

}
