package com.labway.lims.apply.service.chain.material.receive.register;

import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRegisterItemDto;
import com.labway.lims.apply.api.service.MaterialInventoryCheckService;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 领用登记 检查参数
 *
 * <AUTHOR>
 * @since 2023/5/9 10:06
 */
@Slf4j
@Component
public class MaterialReceiveRegisterCheckParamCommand implements Command {

    @Resource
    private MaterialInventoryService materialInventoryService;
    @DubboReference
    private GroupMaterialService groupMaterialService;
    @Resource
    private MaterialInventoryCheckService materialInventoryCheckService;

    @Override
    public boolean execute(Context context) throws Exception {

        final MaterialReceiveRegisterContext from = MaterialReceiveRegisterContext.from(context);
        var user = from.getUser();
        var registerItemList = from.getRegisterItemList();

        if (CollectionUtils.isEmpty(registerItemList)
            || registerItemList.stream().anyMatch(obj -> Objects.isNull(obj.getInventoryId())
                || Objects.isNull(obj.getReceiveMainNumber()) || Objects.isNull(obj.getReceiveAssistNumber()))) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        if (registerItemList.stream().anyMatch(obj -> obj.getReceiveMainNumber().compareTo(BigDecimal.ZERO) <= 0)) {
            throw new IllegalStateException("领用主数量应当大于0");
        }

        String check = materialInventoryCheckService.isCheck(user.getGroupId());
        if (StringUtils.isNotBlank(check)) {
            throw new IllegalStateException(String.format("领用失败: [%s] 存在盘点中记录", check));
        }

        // 领用 信息 key:物料库存id value：领用信息
        final Map<Long, MaterialReceiveRegisterItemDto> registerItemByInventoryId = registerItemList.stream()
            .collect(Collectors.toMap(MaterialReceiveRegisterItemDto::getInventoryId, Function.identity()));

        // 领用的库存 信息
        final List<MaterialInventoryDto> materialInventoryDtos =
            materialInventoryService.selectByInventoryIds(registerItemByInventoryId.keySet());
        if (materialInventoryDtos.stream().anyMatch(obj -> !Objects.equals(obj.getGroupId(), user.getGroupId()))) {
            throw new IllegalStateException("登记失败:领用库存物料存在非本专业组下库存");
        }

        // 领用 涉及的 所有物料id
        final Set<Long> materialIdList =
            materialInventoryDtos.stream().map(MaterialInventoryDto::getMaterialId).collect(Collectors.toSet());

        // 对应 专业组物料 关联信息
        final List<GroupMaterialDto> groupMaterialDtos =
            groupMaterialService.selectByGroupIdAndMaterialIds(user.getGroupId(), materialIdList);

        // 查询的物料id
        final Set<Long> selectMaterialIdList =
            groupMaterialDtos.stream().map(GroupMaterialDto::getMaterialId).collect(Collectors.toSet());

        if (materialIdList.stream().anyMatch(x -> !selectMaterialIdList.contains(x))) {
            throw new IllegalStateException("登记失败:领用物料存在非本专业组下物料");
        }

        // 所有查出来的 库存ids
        final Set<Long> selectInventoryIds =
            materialInventoryDtos.stream().map(MaterialInventoryDto::getInventoryId).collect(Collectors.toSet());

        if (registerItemByInventoryId.keySet().stream().anyMatch(x -> !selectInventoryIds.contains(x))) {
            throw new IllegalStateException("登记失败:存在无效物料");
        }

        materialInventoryDtos.forEach(item -> {
            MaterialReceiveRegisterItemDto itemDto = registerItemByInventoryId.get(item.getInventoryId());

            // 领用主单位数量 大于 主单位库存
            boolean flag = Objects.equals(itemDto.getReceiveMainNumber().compareTo(item.getMainUnitInventory()), 1);

            if (Objects.equals(itemDto.getReceiveAssistNumber().compareTo(item.getAssistUnitInventory()), 1)) {
                // 领用辅单位数量 大于 辅单位库存
                flag = true;
            }
            if (flag) {
                throw new IllegalStateException(String.format("[%s] 物料领用数量大于库存数量，请重新输入", item.getMaterialName()));
            }

        });

        // 补充 物料库存信息、专业组物料信息
        from.put(MaterialReceiveRegisterContext.MATERIAL_INVENTORY, materialInventoryDtos);
        from.put(MaterialReceiveRegisterContext.GROUP_MATERIAL, groupMaterialDtos);
        return CONTINUE_PROCESSING;
    }
}
