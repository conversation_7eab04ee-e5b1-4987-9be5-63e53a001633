package com.labway.lims.apply.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.config.CAPdf;
import com.labway.lims.api.config.HspOrgConfig;
import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.service.SynthesisGeneticsPdfService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;
import com.labway.lims.genetics.api.enums.GeneticsResultEnum;
import com.labway.lims.genetics.api.service.GeneticsSampleResultService;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.dto.WordContentDto;
import com.labway.lims.pdfreport.api.dto.WordReportParamDto;
import com.labway.lims.pdfreport.api.dto.ca.CAPdfSignDto;
import com.labway.lims.pdfreport.api.enums.SealTypeEnum;
import com.labway.lims.pdfreport.api.service.CaPdfService;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.pdfreport.api.vo.ca.CAPdfSignVo;
import com.labway.lims.pdfreport.api.vo.ca.CASealVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class SynthesisGeneticsPdfServiceImpl implements SynthesisGeneticsPdfService {

    @DubboReference
    private GroupService groupService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private GeneticsSampleService geneticsSampleService;
    @Resource
    private HspOrgConfig hspOrgConfig;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @DubboReference
    private CaPdfService caPdfService;
    @DubboReference
    private ReportTemplateBindService reportTemplateBindService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private GeneticsSampleResultService geneticsSampleResultService;


    @Override
    public List<SampleReportDto> synthesisPdf(List<Long> unAuditApplySampleIds) {

        // 对应选中 遗传样本
        final List<GeneticsSampleDto> geneticsSampleDtos = geneticsSampleService.selectByApplySampleIds(unAuditApplySampleIds);
        final Map<Long, GeneticsSampleDto> geneticsSampleDtoMap = geneticsSampleDtos.stream().collect(Collectors.toMap(GeneticsSampleDto::getApplySampleId, e -> e, (v1, v2) -> v2));

        // 申请单
        final List<ApplyDto> applyDtos = applyService.selectByApplyIds(geneticsSampleDtos.stream().map(GeneticsSampleDto::getApplyId).collect(Collectors.toSet()));
        final Map<Long, ApplyDto> applyDtoMap = applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, e -> e, (v1, v2) -> v2));

        // 对应申请单样本ids
        final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(geneticsSampleDtos.stream().map(GeneticsSampleDto::getApplySampleId).collect(Collectors.toSet()));
        final Map<Long, ApplySampleDto> applySampleDtoMap = applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, e -> e, (v1, v2) -> v2));

        // 结果
        Map<Long, GeneticsSampleResultDto> longGeneticsSampleResultDtoMap = geneticsSampleResultService.selectByGeneticsSampleIdsAsMap(geneticsSampleDtos.stream().map(GeneticsSampleDto::getGeneticsSampleId).collect(Collectors.toSet()));

        List<SampleReportDto> sampleReportDtos = new ArrayList<>();
        for (Long applySampleId : unAuditApplySampleIds) {
            GeneticsSampleDto geneticsSampleDto = geneticsSampleDtoMap.get(applySampleId);
            if (geneticsSampleDto == null) {
                log.warn("未找到对应的 geneticSampleId:{}", applySampleId);
                continue;
            }
            ApplyDto applyDto = applyDtoMap.get(geneticsSampleDto.getApplyId());
            ApplySampleDto applySampleDto = applySampleDtoMap.get(applySampleId);
            GeneticsSampleResultDto geneticsSampleResultDto = longGeneticsSampleResultDtoMap.get(geneticsSampleDto.getGeneticsSampleId());

            SampleReportDto sampleReportDto = null;
            try {
                sampleReportDto = buildPDF(geneticsSampleDto,applyDto,applySampleDto,geneticsSampleResultDto);
            } catch (Exception e) {
                log.error("合成PDF失败",e);
                throw new RuntimeException(e);
            }
            sampleReportDtos.add(sampleReportDto);
        }

        return sampleReportDtos;
    }



    // 遗传pdf走到了这里
    public SampleReportDto buildPDF(GeneticsSampleDto sample, ApplyDto applyDto, ApplySampleDto applySampleDto,
                                    GeneticsSampleResultDto geneticsSampleResult) {

        final ReportTemplateBindDto reportTemplateBind =
                reportTemplateBindService.selectByBizId(sample.getTestItemId());
        if (Objects.isNull(reportTemplateBind)
                || Objects.equals(reportTemplateBind.getEnable(), YesOrNoEnum.NO.getCode())) {
            throw new IllegalStateException(String.format("检验项目 [%s] 没有绑定报告单模板", sample.getTestItemName()));
        }

        final ProfessionalGroupDto group = groupService.selectByGroupId(sample.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException(String.format("专业组 [%s] 不存在", sample.getGroupName()));
        }

        UserDto twoChecker = userService.selectByUserId(sample.getTwoCheckerId());
        if (Objects.isNull(twoChecker)) {
//            throw new IllegalStateException(String.format("二次审核人 [%s] 不存在", sample.getTwoCheckerName()));
            twoChecker = new UserDto();
        }

        UserDto tester = userService.selectByUserId(applySampleDto.getTesterId());
        if (Objects.isNull(tester)) {
//            throw new IllegalStateException(String.format("检验者 [%s] 不存在", applySampleDto.getTesterName()));
            tester = new UserDto();
        }

        UserDto oneChecker = userService.selectByUserId(sample.getOneCheckerId());
        if (Objects.isNull(oneChecker)) {
//            throw new IllegalStateException(String.format("一次审核人 [%s] 不存在", sample.getOneCheckerName()));
            oneChecker = new UserDto();
        }

        // 是WORD类型的话， 直接构建WORD 版 PDF
        if (Objects.equals(sample.getResultType(), GeneticsResultEnum.WORD.getType())) {
            return buildWordPDF(sample, applyDto, applySampleDto, geneticsSampleResult, group);
        }

        final PdfReportParamDto param = new PdfReportParamDto();

        param.put("reportNo", joptsimple.internal.Strings.EMPTY);

        param.put("apply",
                Dict.of("masterBarcode", applyDto.getMasterBarcode(), "patientName", applyDto.getPatientName(),
                        "patientAge", applyDto.getPatientAge(), "hspOrgName", applyDto.getHspOrgName(), "_apply",
                        Dict.parse(applyDto)));

        param.put("applySample",
                Dict.of("barcode", applySampleDto.getBarcode(), "tubeName", applySampleDto.getTubeName(), "sampleTypeName",
                        applySampleDto.getSampleTypeName(), "groupName", applySampleDto.getGroupName(), "onePickerName",
                        applySampleDto.getOnePickerName(), "onePickDate", applySampleDto.getOnePickDate(), "twoPickerName",
                        applySampleDto.getTwoPickerName(), "twoPickDate", applySampleDto.getTwoPickDate(), "_applySample",
                        Dict.parse(applySampleDto)));

        param.put("sample",
                Dict.of("sampleNo", sample.getSampleNo(), "instrumentGroupName", sample.getInstrumentGroupName(),
                        "instrumentName", sample.getInstrumentName(), "testerName", applySampleDto.getTesterName(),
                        "oneCheckerName", sample.getOneCheckerName(), "twoCheckerName", sample.getTwoCheckerName(),
                        "sampleRemark", applySampleDto.getSampleRemark(), "resultRemark", applySampleDto.getResultRemark(),
                        "_sample", Dict.parse(sample)));

        // 签名
        param.put("signature", Dict.of(
                // 检验者
                "tester", Dict.of("name", tester.getNickname(), "cnSign", tester.getCnSign(), "enSign", tester.getEnSign(), "sign",
                        StringUtils.defaultString(tester.getCnSign(), tester.getEnSign())),
                // 一次审核人
                "oneChecker",
                Dict.of("name", oneChecker.getNickname(), "cnSign", oneChecker.getCnSign(), "enSign",
                        oneChecker.getEnSign(), "sign",
                        StringUtils.defaultString(oneChecker.getCnSign(), oneChecker.getEnSign())),
                // 二次审核人
                "twoChecker",
                Dict.of("name", twoChecker.getNickname(), "cnSign", twoChecker.getCnSign(), "enSign",
                        twoChecker.getEnSign(), "sign",
                        StringUtils.defaultString(twoChecker.getCnSign(), twoChecker.getEnSign())),
                // 批准者
                "approver", Dict.of("name", group.getApproverName(), "sign", group.getApproverSign())));

        param.put("result", ObjectUtils.defaultIfNull(geneticsSampleResult, new GeneticsSampleResultDto()));

        log.info("开始使用报告单模板 [{}] 生成 条码 [{}] 的报告单。 参数 [{}]", reportTemplateBind.getReportTemplateCode(), sample.getBarcode(),
                JSON.toJSONString(param));

//        final String pdfUrl = pdfReportService.build2Url(reportTemplateBind.getReportTemplateCode(), param);

        File tempFile = FileUtil.createTempFile();
        try (final FileOutputStream fos = new FileOutputStream(tempFile)) {
            String pdfCodeBak = reportTemplateBind.getReportTemplateCode();
            String pdfCode = reportTemplateBind.getReportTemplateCode();
            boolean contains = hspOrgConfig.getHspOrgCodes().contains(applyDto.getHspOrgCode());
            if(contains){
                pdfCode = "CA_" + pdfCode;
            }
            byte[] bytes;
            try {
                bytes = pdfReportService.build(pdfCode, param);
            }catch (Exception exception){
                if(!contains){
                    throw exception;
                }
                log.error("ca模板生成失败， 生成原始模板");
                contains = false;
                bytes = pdfReportService.build(pdfCodeBak, param);
            }
            if(contains){
                try {
                    byte[] CABytes = createCAPDF(bytes, applySampleDto.getTesterName(), twoChecker.getNickname());
                    if (CABytes.length > 0) {
                        bytes = CABytes;
                    }
                } catch (Exception exception) {
                    bytes = pdfReportService.build(pdfCodeBak, param);
                    log.error("ca模板生成失败， 生成原始模板" + exception);
                }
            }

            fos.write(bytes);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        String pdfUrl;
        try(FileInputStream fileInputStream = new FileInputStream(tempFile)){
            pdfUrl = huaweiObsUtils.upload(fileInputStream, MediaType.APPLICATION_PDF_VALUE);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        final SampleReportDto sr = new SampleReportDto();
        sr.setApplySampleId(applySampleDto.getApplySampleId());
        sr.setApplyId(applyDto.getApplyId());
        sr.setSampleId(sample.getGeneticsSampleId());
        sr.setBarcode(sample.getBarcode());
        sr.setFileType(SampleReportFileTypeEnum.PDF.name());
        sr.setUrl(pdfUrl);
        sr.setGroupName(applySampleDto.getGroupName());
        sr.setGroupId(applySampleDto.getGroupId());
        sr.setHspOrgId(applyDto.getHspOrgId());
        sr.setHspOrgName(applyDto.getHspOrgName());
        return sr;

    }



    public SampleReportDto buildWordPDF(GeneticsSampleDto sample, ApplyDto applyDto, ApplySampleDto applySampleDto,
                                        GeneticsSampleResultDto geneticsSampleResult, ProfessionalGroupDto group) {
        //新的直接对doc进行下载并进行占位符替换
        WordContentDto contentDto = geneticsSampleResult.getGeneticsResult().getWordContent();
        if (Objects.isNull(contentDto)) {
            throw new IllegalStateException(String.format("[%s]在线报告不存在", sample.getBarcode()));
        }

        //获取下载链接
        String downLoadUrl = contentDto.getDownLoadUrl();
        //封装对象 并调用转换接口
        final WordReportParamDto param = new WordReportParamDto();
        param.put("[patientName]", applyDto.getPatientName());
        param.put("[patientSex]", SexEnum.getByCode(applyDto.getPatientSex()).getDesc());
        param.put("[patientAge]", PatientAges.toText(applyDto));
        param.put("[sendDoctorName]", applyDto.getSendDoctorName());
        param.put("[dept]", applyDto.getDept());
        param.put("[patientVisitCard]", applyDto.getPatientVisitCard());
        param.put("[bed]", applyDto.getPatientBed());
        param.put("[groupName]", applySampleDto.getGroupName());
        param.put("[reportNo]", Strings.EMPTY);
        param.put("[sampleType]", applySampleDto.getSampleTypeName());
        param.put("[diagnosis]", applyDto.getDiagnosis());
        param.put("[barcode]", applySampleDto.getBarcode());
        param.put("[hspOrgName]", applyDto.getHspOrgName());
        param.put("[sampleNo]", sample.getSampleNo());
        param.put("[orgName]", applyDto.getOrgName());
        param.put("[remark]", applySampleDto.getSampleRemark());
        param.put("[testerName]", applySampleDto.getTesterName());
        param.put("[testerDate]", DateUtil.formatDate(sample.getTestDate()));
        param.put("[twoCheckName]", sample.getTwoCheckerName());
        param.put("[approverName]", group.getApproverName());
        param.put("[samplingDate]", DateUtil.formatDate(applyDto.getSamplingDate()));
        param.put("[signDate]", DateUtil.formatDate(applyDto.getSignDate()));
        param.put("[checkDate]", DateUtil.formatDate(sample.getTwoCheckDate()));
        param.put("[instrumentName]", sample.getInstrumentName());
        param.put("[outBarcode]", applySampleDto.getOutBarcode());
        param.put("[inpatientArea]", applyDto.getInpatientArea());
        param.put("[visitCardNo]", applyDto.getVisitCardNo());


        String pdfUrl = pdfReportService.buildWordPdf(downLoadUrl, param);

        final SampleReportDto sr = new SampleReportDto();
        sr.setApplySampleId(applySampleDto.getApplySampleId());
        sr.setApplyId(applyDto.getApplyId());
        sr.setSampleId(sample.getGeneticsSampleId());
        sr.setBarcode(sample.getBarcode());
        sr.setFileType(SampleReportFileTypeEnum.PDF.name());
        sr.setUrl(pdfUrl);
        sr.setGroupName(applySampleDto.getGroupName());
        sr.setGroupId(applySampleDto.getGroupId());
        sr.setHspOrgId(applyDto.getHspOrgId());
        sr.setHspOrgName(applyDto.getHspOrgName());
        return sr;
    }



    //==================================================================================================================

    /**
     * 打印capdf
     * @param bytes  文件
     * @param testName 检验者
     * @param auditName 审核者
     * @return
     * @throws IOException
     */
    private byte[] createCAPDF(byte[] bytes, String testName, String auditName) throws IOException {
        List<CAPdfSignDto.Strategy> strategies = new ArrayList<>();

        //  所有章
        List<CASealVo> caSealVos = caPdfService.selectSeal(null);
        if (CollectionUtils.isEmpty(caSealVos)) {
            return new byte[0];
        }

        // 检验者章
        CAPdfSignDto.Strategy strategy = this.getStrategy(caSealVos, hspOrgConfig.getTest(), testName);
        if(Objects.nonNull(strategy)) {
            strategies.add(strategy);
        }

        //  审核者章
        CAPdfSignDto.Strategy strategy1 = this.getStrategy(caSealVos, hspOrgConfig.getAudit(), auditName);
        if(Objects.nonNull(strategy1)) {
            strategies.add(strategy1);
        }

        // 公章
        CAPdfSignDto.Strategy strategy2 = this.getStrategy(caSealVos, hspOrgConfig.getOffice(), "-1");
        if(Objects.nonNull(strategy2)) {
            strategies.add(strategy2);
        }

        CAPdfSignDto caPdfSignDto = new CAPdfSignDto();
        caPdfSignDto.setFile(bytes);
        caPdfSignDto.setSignedStrategy(strategies);

        CAPdfSignVo caPdfSignVo = caPdfService.pdfQuiesceSign(caPdfSignDto);

        return caPdfService.download(caPdfSignVo.getEnvelopeId(), null);
    }

    /**
     * 获取签章配置
     * @param caSealVos 签章列表
     * @param caPdf 签署位置配置
     * @param sealName 签署人
     * @return
     */
    private CAPdfSignDto.Strategy getStrategy(List<CASealVo> caSealVos, CAPdf caPdf, String sealName) {
        if (Objects.equals(sealName, "-1")) {
            sealName = caPdf.getSealName();
        }
        String finalSealName = sealName;
        CASealVo caSealVo = caSealVos.stream().filter(e -> e.getSealName().startsWith(finalSealName)).findFirst().orElse(null);
        if (Objects.nonNull(caSealVo)) {
            CAPdfSignDto.Strategy strategy = new CAPdfSignDto.Strategy();
            strategy.setSealId(caSealVo.getId());
            strategy.setStragegyType(1);
            strategy.setKeywords(caPdf.getKeyword());
            strategy.setPages("all");
            if (Objects.equals(caSealVo.getSealType(), String.valueOf(SealTypeEnum.PERSION_SEAL.getCode()))) {
                strategy.setReduction(caPdf.getReduction());
            }
            strategy.setOffsetDirectionX(caPdf.getX());
            strategy.setOffsetDirectionY(caPdf.getY());
            strategy.setIndex(0);
            return strategy;
        }
        return null;
    }

}
