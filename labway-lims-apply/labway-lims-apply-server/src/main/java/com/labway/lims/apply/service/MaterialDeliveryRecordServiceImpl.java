package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.BusinessCenterDeliveryDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryRecordDetailDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryRecordDto;
import com.labway.lims.apply.api.dto.SelectMaterialDeliveryRecordDto;
import com.labway.lims.apply.api.service.MaterialDeliveryDetailService;
import com.labway.lims.apply.api.service.MaterialDeliveryRecordService;
import com.labway.lims.apply.mapper.TbMaterialDeliveryRecordMapper;
import com.labway.lims.apply.mapstruct.MaterialDeliveryConverter;
import com.labway.lims.apply.model.TbMaterialDeliveryRecord;
import com.labway.lims.apply.service.chain.material.delivery.business.BusinessCenterDeliveryChain;
import com.labway.lims.apply.service.chain.material.delivery.business.BusinessCenterDeliveryContext;
import com.labway.lims.apply.service.chain.material.delivery.business.noapply.BusinessCenterNoApplyDeliveryChain;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.lang.NonNull;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 物料出库记录 Service impl
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Slf4j
@DubboService
public class MaterialDeliveryRecordServiceImpl implements MaterialDeliveryRecordService {

    @Resource
    private TbMaterialDeliveryRecordMapper tbMaterialDeliveryRecordMapper;

    @Resource
    private BusinessCenterDeliveryChain businessCenterDeliveryChain;

    @Resource
    private BusinessCenterNoApplyDeliveryChain businessCenterNoApplyDeliveryChain;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private MaterialDeliveryConverter materialDeliveryConverter;

    @Resource
    private MaterialDeliveryDetailService materialDeliveryDetailService;

    @Override
    public void businessCenterDelivery(@NonNull BusinessCenterDeliveryDto dto) {
        log.info("接受业务中台出库信息: [{}] ", JSON.toJSONString(dto));
        final BusinessCenterDeliveryContext context = new BusinessCenterDeliveryContext();
        context.setDeliveryDto(dto);

        try {
            if (StringUtils.isBlank(dto.getApplyNo())) {
                if (!businessCenterNoApplyDeliveryChain.execute(context)) {
                    throw new IllegalStateException("接收业务中台出库失败");
                }
            } else {
                if (!businessCenterDeliveryChain.execute(context)) {
                    throw new IllegalStateException("接收业务中台出库失败");
                }
            }
        } catch (LimsCodeException e) {
            log.error("接收业务中台出库失败 [{}]", dto.getApplyNo(), e);
            throw e;
        } catch (Exception e) {
            log.error("接收业务中台出库失败 [{}]", dto.getApplyNo(), e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("接收业务中台出库 [{}] 耗时\n{}", dto.getApplyNo(), context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addMaterialDeliveryRecord(MaterialDeliveryRecordDto dto) {
        final TbMaterialDeliveryRecord target = new TbMaterialDeliveryRecord();

        BeanUtils.copyProperties(dto, target);

        target.setRecordId(ObjectUtils.defaultIfNull(dto.getRecordId(), snowflakeService.genId()));

        if (tbMaterialDeliveryRecordMapper.insert(target) < 1) {
            throw new IllegalStateException("添加物料出库记录失败");
        }

        return target.getRecordId();
    }

    @Override
    public List<MaterialDeliveryRecordDto>
        selectBySelectMaterialDeliveryRecordDto(SelectMaterialDeliveryRecordDto dto) {
        LambdaQueryWrapper<TbMaterialDeliveryRecord> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.ge(Objects.nonNull(dto.getBeginDeliveryDate()), TbMaterialDeliveryRecord::getDeliveryDate,
            dto.getBeginDeliveryDate());
        queryWrapper.le(Objects.nonNull(dto.getEndDeliveryDate()), TbMaterialDeliveryRecord::getDeliveryDate,
            dto.getEndDeliveryDate());

        queryWrapper.eq(Objects.nonNull(dto.getStatus()), TbMaterialDeliveryRecord::getStatus, dto.getStatus());
        queryWrapper.eq(StringUtils.isNoneBlank(dto.getDeliveryNo()), TbMaterialDeliveryRecord::getDeliveryNo,
            dto.getDeliveryNo());
        queryWrapper.eq(TbMaterialDeliveryRecord::getGroupId, dto.getGroupId());
        queryWrapper.orderByDesc(TbMaterialDeliveryRecord::getDeliveryDate);

        return materialDeliveryConverter
            .fromTbMaterialDeliveryRecordList(tbMaterialDeliveryRecordMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public MaterialDeliveryRecordDto selectByDeliveryNo(String deliveryNo, long orgId) {
        if (StringUtils.isBlank(deliveryNo)) {
            return null;
        }
        LambdaQueryWrapper<TbMaterialDeliveryRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMaterialDeliveryRecord::getDeliveryNo, deliveryNo);
        queryWrapper.eq(TbMaterialDeliveryRecord::getOrgId, orgId);
        queryWrapper.eq(TbMaterialDeliveryRecord::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return materialDeliveryConverter
            .fromTbMaterialDeliveryRecord(tbMaterialDeliveryRecordMapper.selectOne(queryWrapper));
    }

    @Override
    public List<MaterialDeliveryRecordDto> selectByDeliveryNos(Collection<String> deliveryNos, long orgId) {
        if (CollectionUtils.isEmpty(deliveryNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMaterialDeliveryRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMaterialDeliveryRecord::getDeliveryNo, deliveryNos);
        queryWrapper.eq(TbMaterialDeliveryRecord::getOrgId, orgId);
        queryWrapper.eq(TbMaterialDeliveryRecord::getIsDelete, YesOrNoEnum.NO.getCode());

        return materialDeliveryConverter
            .fromTbMaterialDeliveryRecordList(tbMaterialDeliveryRecordMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public MaterialDeliveryRecordDto selectByRecordId(long recordId) {
        LambdaQueryWrapper<TbMaterialDeliveryRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMaterialDeliveryRecord::getRecordId, recordId);
        queryWrapper.eq(TbMaterialDeliveryRecord::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return materialDeliveryConverter
            .fromTbMaterialDeliveryRecord(tbMaterialDeliveryRecordMapper.selectOne(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByRecordId(MaterialDeliveryRecordDto dto) {
        TbMaterialDeliveryRecord target = materialDeliveryConverter.fromMaterialDeliveryRecordDto(dto);

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbMaterialDeliveryRecordMapper.updateById(target) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改出库记录 [{}] 成功", loginUser.getNickname(), JSON.toJSONString(target));

        return true;
    }

    @Override
    public List<MaterialDeliveryRecordDto> selectByApplyId(long applyId) {
        LambdaQueryWrapper<TbMaterialDeliveryRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMaterialDeliveryRecord::getApplyId, applyId);

        return tbMaterialDeliveryRecordMapper.selectList(queryWrapper).stream()
            .map(materialDeliveryConverter::fromTbMaterialDeliveryRecord).collect(Collectors.toList());
    }

    @Override
    public List<MaterialDeliveryRecordDetailDto> outBoundOrders(Long applyId) {
        return outBoundOrders(applyId, LoginUserHandler.get().getOrgId());
    }

    @Override
    public List<MaterialDeliveryRecordDetailDto> outBoundOrders(Long applyId, long orgId) {
        final List<MaterialDeliveryRecordDto> materialDeliveryRecords = selectByApplyId(applyId);
        if (CollectionUtils.isEmpty(materialDeliveryRecords)) {
            return Collections.emptyList();
        }

        final Set<String> deliveryNos =
                materialDeliveryRecords.stream().map(MaterialDeliveryRecordDto::getDeliveryNo).collect(Collectors.toSet());

        final Map<String, List<MaterialDeliveryDetailDto>> materialDeliveryDetailMap =
                materialDeliveryDetailService.selectByDeliveryNos(deliveryNos, orgId).stream()
                        .collect(Collectors.groupingBy(MaterialDeliveryDetailDto::getDeliveryNo));

        return materialDeliveryRecords.stream().map(c -> {
            final MaterialDeliveryRecordDetailDto dto =
                    JSON.parseObject(JSON.toJSONString(c), MaterialDeliveryRecordDetailDto.class);
            dto.setItems(materialDeliveryDetailMap.get(c.getDeliveryNo()));
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void deleteByDeliveryNo(String deliveryNo) {
        LoginUserHandler.User user = LoginUserHandler.get();
        if(StringUtils.isBlank(deliveryNo)){
            return;
        }
        tbMaterialDeliveryRecordMapper.delete(Wrappers.lambdaUpdate(TbMaterialDeliveryRecord.class)
                .eq(TbMaterialDeliveryRecord::getDeliveryNo, deliveryNo)
                .eq(TbMaterialDeliveryRecord::getOrgId, user.getOrgId()));
    }
}
