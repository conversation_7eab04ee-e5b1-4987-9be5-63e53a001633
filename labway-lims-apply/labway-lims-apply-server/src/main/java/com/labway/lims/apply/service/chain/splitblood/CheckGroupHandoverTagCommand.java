package com.labway.lims.apply.service.chain.splitblood;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class CheckGroupHandoverTagCommand implements Command {
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context c) throws Exception {
        final SplitBloodContext context = SplitBloodContext.from(c);

        final ApplySampleDto applySample = context.getApplySample();

        // dev-1.1.3.3 不允许分血， 那么就是组间交接， 打标记
        if (!context.isSupportedSplitBlood()) {
            this.setGroupHandoverTag(applySample.getBarcode());
            log.info("条码[{}] 样本类型 [{}] 被标记为不可以分血", applySample.getBarcode(), applySample.getSampleTypeCode());
        }
        return CONTINUE_PROCESSING;
    }

    /**
     * 组间交接标记key
     *
     * @param barcode 条码
     */
    private String getGroupHandoverRedisKey(String barcode) {
        return redisPrefix.getBasePrefix() + "SPLIT_BLOOD:GROUP_HANDOVER:TAG:" + barcode;
    }

    private void setGroupHandoverTag(String barcode) {
        stringRedisTemplate.opsForValue().set(this.getGroupHandoverRedisKey(barcode), Strings.EMPTY);
    }

    public boolean existsGroupHandover(String barcode) {
        return BooleanUtils.isTrue(stringRedisTemplate.hasKey(this.getGroupHandoverRedisKey(barcode)));
    }

}
