package com.labway.lims.apply.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PreprocessWorkloadVo {
    /**
     * 动态列
     */
    private Set<PreprocessWorkloadStatisticsVo.TubeStatistics> columns;

    /**
     * 数据列
     */
    private List<PreprocessWorkloadStatisticsVo> dataList;
}
