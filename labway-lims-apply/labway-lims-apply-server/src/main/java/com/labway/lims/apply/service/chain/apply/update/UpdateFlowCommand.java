package com.labway.lims.apply.service.chain.apply.update;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Component
@Slf4j
public class UpdateFlowCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private ApplySampleService applySampleService;

    @Resource
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {
        UpdateApplyContext context = UpdateApplyContext.from(c);

        final String diffMsg = context.getDiffMsg();

        final Long applyId = context.getApply().getApplyId();

        final TestApplyDto updateTestApply = context.getTestApply();

        final LoginUserHandler.User user = context.getUser();

        int addApplySampleCount = 0;
        final List<ApplySampleDto> addApplySamples = context.getAddApplySamples();
        if (CollectionUtils.isNotEmpty(addApplySamples)) {
            addApplySampleCount = addApplySamples.size();
        }

        int sampleItemsCount = 0;
        Map<Long, List<ApplySampleItemDto>> sampleItemMap = null;
        if (CollectionUtils.isNotEmpty(context.getAddApplySampleItems())) {
            sampleItemMap = context.getAddApplySampleItems().stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));
            sampleItemsCount = sampleItemMap.size();
        }

        final LinkedList<Long> ids = snowflakeService.genIds(sampleItemsCount + addApplySampleCount + 100);

        final Map<Long, ApplySampleDto> applySampleMap = applySampleService.selectByApplyId(applyId)
                .stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, v -> v, (a, b) -> a));

        List<SampleFlowDto> sampleFlows = new ArrayList<>();
        // 条码新增的条码环节
        final Date now = new Date();
        if (CollectionUtils.isNotEmpty(addApplySamples)) {
            for (final ApplySampleDto applySample : addApplySamples) {
                SampleFlowDto sampleFlow = createSampleFlow(applySample,
                        BarcodeFlowEnum.CREATE_BARCODE.getDesc(), BarcodeFlowEnum.CREATE_BARCODE);
                sampleFlow.setSampleFlowId(ids.pop());
                sampleFlow.setCreateDate(now);
                sampleFlow.setUpdateDate(now);
                sampleFlows.add(sampleFlow);
            }
        }

        // 修改了申请单信息的条码环节
        if (StringUtils.isNotBlank(diffMsg)) {
            for (ApplySampleDto applySampleDto : applySampleMap.values()) {
                SampleFlowDto sampleFlow = createSampleFlow(applySampleDto, diffMsg, BarcodeFlowEnum.APPLY_INFO_UPDATE);
                sampleFlow.setSampleFlowId(ids.pop());
                sampleFlow.setCreateDate(now);
                sampleFlow.setUpdateDate(now);
                sampleFlows.add(sampleFlow);
            }
        }

        // 更新样本信息
        if (Objects.nonNull(context.getUpdateApplySample()) && StringUtils.isNotBlank(context.getApplySampleDiffMsg())) {
            ApplySampleDto applySample = context.getUpdateApplySample();
            SampleFlowDto sampleFlow = createSampleFlow(applySample, context.getApplySampleDiffMsg(), BarcodeFlowEnum.APPLY_INFO_UPDATE);
            sampleFlow.setSampleFlowId(ids.pop());
            sampleFlow.setCreateDate(now);
            sampleFlow.setUpdateDate(now);
            sampleFlows.add(sampleFlow);
        }

        // 新增的检验项目条码环节
        if (CollectionUtils.isNotEmpty(context.getAddApplySampleItems()) && Objects.nonNull(sampleItemMap)) {
            for (final Map.Entry<Long, List<ApplySampleItemDto>> entry : sampleItemMap.entrySet()) {
                final ApplySampleDto applySample = applySampleMap.get(entry.getKey());
                if (Objects.isNull(applySample)) {
                    continue;
                }

                final String testItemName = entry.getValue().stream().map(ApplySampleItemDto::getTestItemName)
                        .collect(Collectors.joining(","));

                SampleFlowDto sampleFlow = createSampleFlow(applySample, String.format("新增检验项目 [%s]", testItemName), BarcodeFlowEnum.TEST_ITEM_ADD);
                sampleFlow.setSampleFlowId(ids.pop());
                sampleFlow.setCreateDate(now);
                sampleFlow.setUpdateDate(now);
                sampleFlows.add(sampleFlow);
            }
        }

        sampleFlowService.addSampleFlows(sampleFlows);

        log.info("更新申请单样本信息成功 applyId [{}] 修改信息 [{}] 修改人 [{}]", applyId, JSON.toJSONString(updateTestApply), LoginUserHandler.get().getNickname());

        return CONTINUE_PROCESSING;
    }

    @Nonnull
    public static SampleFlowDto createSampleFlow(ApplySampleDto applySample, String content, BarcodeFlowEnum type) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setApplyId(applySample.getApplyId());
        sampleFlow.setApplySampleId(applySample.getApplySampleId());
        sampleFlow.setBarcode(applySample.getBarcode());
        sampleFlow.setOperateCode(type.name());
        sampleFlow.setOperateName(type.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        sampleFlow.setContent(content);
        sampleFlow.setCreatorId(user.getUserId());
        sampleFlow.setCreatorName(user.getNickname());
        sampleFlow.setUpdaterId(user.getUserId());
        sampleFlow.setUpdaterName(user.getNickname());
        sampleFlow.setOrgId(user.getOrgId());
        sampleFlow.setOrgName(user.getOrgName());
        sampleFlow.setIsDelete(YesOrNoEnum.NO.getCode());
        return sampleFlow;
    }
}
