package com.labway.lims.apply.service.chain.material.receive.register;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 物料 领用登记 责任链
 *
 * <AUTHOR>
 * @since 2023/5/9 10:02
 */
@Component
public class MaterialReceiveRegisterChain extends ChainBase implements InitializingBean {

    @Resource
    private MaterialReceiveRegisterCheckParamCommand materialReceiveRegisterCheckParamCommand;

    @Resource
    private MaterialReceiveRegisterAddReceiveRecordCommand materialReceiveRegisterAddReceiveRecordCommand;

    @Resource
    private MaterialReceiveRegisterReduceMaterialInventoryCommand materialReceiveRegisterReduceMaterialInventoryCommand;
    @Resource
    private MaterialReceiveRegisterReduceGroupMaterialCommand materialReceiveRegisterReduceGroupMaterialCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 检查 参数
        addCommand(materialReceiveRegisterCheckParamCommand);

        // 创建 领用记录
        addCommand(materialReceiveRegisterAddReceiveRecordCommand);

        // 修改 库存 物料 数量
        addCommand(materialReceiveRegisterReduceMaterialInventoryCommand);

        // 修改 专业组 物料总库存
        addCommand(materialReceiveRegisterReduceGroupMaterialCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);
    }
}
