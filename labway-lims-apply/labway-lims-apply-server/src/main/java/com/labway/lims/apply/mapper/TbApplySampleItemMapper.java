package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.model.TbApplySampleItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 检验项目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbApplySampleItemMapper extends BaseMapper<TbApplySampleItem> {

    void insertBatch(@Param("list") List<ApplySampleItemDto> applySampleItems);

    /**
     * 根据条码查询
     */
    List<ApplySampleItemDto> selectByBarcode(@Param("orgId") long orgId, @Param("barcode") String barcode);
}
