package com.labway.lims.apply.service.chain.pick.one;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 一次分拣流水
 */
@Slf4j
@Component
class OnePickFlowCommand implements Command, Filter {

    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {

        final OnePickContext context = OnePickContext.from(c);

        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(context.getApplySample().getApplyId());
        sampleFlow.setApplySampleId(context.getApplySampleId());
        sampleFlow.setBarcode(context.getApplySample().getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.ONE_PICK.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.ONE_PICK.getDesc());
        sampleFlow.setOperator(LoginUserHandler.get().getNickname());
        sampleFlow.setOperatorId(LoginUserHandler.get().getUserId());
        sampleFlow.setContent(String.format("试管架 [%s] 行 [%s] 列 [%s] 当前专业组 [%s] 待接收专业组 [%s]", context.getRackLogic().getRackCode(),
                context.getPoint().x + 1, context.getPoint().y + 1,
                context.getRackLogic().getCurrentGroupName(),
                context.getRackLogic().getNextGroupName()));

        sampleFlowService.addSampleFlow(sampleFlow);


        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
