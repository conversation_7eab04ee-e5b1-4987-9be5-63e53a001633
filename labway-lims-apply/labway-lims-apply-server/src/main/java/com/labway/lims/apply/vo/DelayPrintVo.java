package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Getter
@Setter
public class DelayPrintVo implements Serializable {

    /**
     * 医院
     */
    private String hospitalName;
    /**
     * 专业组
     */
    private String groupName;

    /**
     * 发送申请单日期
     */
    private LocalDate sendTestDate;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 床号
     */
    private String bedNumber;

    /**
     * //条码号
     */
    private String barcode;


    /**
     * 备注
     */
    private String remake;
    /**
     * 原因
     */
    private String cause;
    /**
     * 预计发单时间
     */
    private LocalDateTime sendReportDate;

    private Long applyId;


    /**
     * 打印时间
     */
    private LocalDateTime printDate;

    public String getPrintDate() {
        return printDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
    }

    public String getSendReportDate() {
        return sendReportDate.toLocalDate().toString();
    }

    public String getSendTestDate() {
        return sendTestDate.toString();
    }

}
