package com.labway.lims.apply.controller;

import cn.hutool.core.compress.ZipWriter;
import cn.hutool.core.lang.Assert;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.dto.UploadPdfDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.apply.service.SynthesisGeneticsPdfService;
import com.labway.lims.apply.service.SynthesisInfectionPdfService;
import com.labway.lims.apply.service.SynthesisMicrobiologyPdfService;
import com.labway.lims.apply.service.SynthesisOutsourcingPdfService;
import com.labway.lims.apply.service.SynthesisRoutinePdfService;
import com.labway.lims.apply.service.SynthesisSpecialtyPdfService;
import com.labway.lims.apply.vo.ApplySampleChangeSampleNoVo;
import com.labway.lims.apply.vo.ApplySampleResultRemarkVo;
import com.labway.lims.apply.vo.ApplySampleSampleRemarkVo;
import com.labway.lims.apply.vo.SampleApplyInfoVo;
import com.labway.lims.apply.vo.SampleFlowVo;
import com.labway.lims.apply.vo.SampleTrackVo;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 样本
 */
@Slf4j
@RestController
@RequestMapping("/sample")
public class SampleController extends BaseController implements DisposableBean, InitializingBean {
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private SampleReportService sampleReportService;
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SampleService sampleService;
    @Resource
    private EnvDetector envDetector;
    @Resource
    private ApplyService applyService;
    @DubboReference
    private GeneticsSampleService geneticsSampleService;
    @DubboReference
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private SpecialtySampleService specialtySampleService;
    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private BloodCultureSampleService bloodCultureSampleService;
    @Resource
    private SynthesisRoutinePdfService synthesisRoutinePdfService;
    @Resource
    private SynthesisGeneticsPdfService synthesisGeneticsPdfService;
    @Resource
    private SynthesisInfectionPdfService synthesisInfectionPdfService;
    @Resource
    private SynthesisMicrobiologyPdfService synthesisMicrobiologyPdfService;
    @Resource
    private SynthesisSpecialtyPdfService synthesisSpecialtyPdfService;
    @Resource
    private SynthesisOutsourcingPdfService synthesisOutsourcingPdfService;

    /**
     * 样本结果查询
     */
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    private ExecutorService executorService;

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @DubboReference
    private SnowflakeService snowflakeService;

    @DubboReference
    private SampleResultService sampleResultService;

    @DubboReference
    private SampleReportItemService sampleReportItemService;

    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;

    @DubboReference
    private PdfReportService pdfReportService;

    @Resource
    private SampleAbnormalService sampleAbnormalService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 结果备注
     */
    @PostMapping("/result-remark")
    public Object resultRemark(@RequestBody ApplySampleResultRemarkVo vo) {

        if (Objects.isNull(vo.getApplySampleId())) {
            throw new IllegalArgumentException("参数错误");
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(vo.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (!Set.of(SampleStatusEnum.NOT_AUDIT.getCode(), SampleStatusEnum.ONE_AUDIT.getCode())
                .contains(applySample.getStatus())) {
            throw new IllegalStateException("当前状态无法修改结果备注");
        }

        final ApplySampleDto as = new ApplySampleDto();
        as.setApplySampleId(vo.getApplySampleId());
        as.setResultRemark(vo.getResultRemark());

        if (!applySampleService.updateByApplySampleId(as)) {
            throw new IllegalStateException("修改备注失败");
        }
        // 结果备注 变化
        String resultRemarkChange =
                new CompareUtils<ApplySampleDto>().compare(applySample, as, Lists.newArrayList("sampleRemark"));
        if (StringUtils.isNotBlank(resultRemarkChange)) {
            LoginUserHandler.User user = LoginUserHandler.get();
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setApplyId(applySample.getApplyId());
            sampleFlow.setApplySampleId(applySample.getApplySampleId());
            sampleFlow.setBarcode(applySample.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.RESULT_REMARK.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.RESULT_REMARK.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(String.format("修改结果备注:%s", resultRemarkChange));
            // 添加流水
            sampleFlowService.addSampleFlow(sampleFlow);
        }

        return Collections.emptyMap();
    }

    /**
     * 修改样本备注
     */
    @PostMapping("/sample-remark")
    public Object updateSample(@RequestBody ApplySampleSampleRemarkVo vo) {

        if (Objects.isNull(vo.getApplySampleId())) {
            throw new IllegalArgumentException("参数错误");
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(vo.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (!Set.of(SampleStatusEnum.NOT_AUDIT.getCode(), SampleStatusEnum.ONE_AUDIT.getCode(), SampleStatusEnum.RETEST.getCode())
                .contains(applySample.getStatus())) {
            throw new IllegalStateException("当前状态无法修改样本备注");
        }

        final ApplySampleDto as = new ApplySampleDto();
        as.setApplySampleId(vo.getApplySampleId());
        as.setSampleRemark(StringUtils.defaultString(vo.getSampleRemark()));
        as.setTesterId(LoginUserHandler.get().getUserId());
        as.setTesterName(LoginUserHandler.get().getNickname());
        as.setSampleTypeName(StringUtils.defaultString(vo.getSampleTypeName()));
        as.setSampleTypeCode(StringUtils.defaultString(vo.getSampleTypeCode()));
        as.setSampleProperty(StringUtils.defaultString(vo.getSampleProperty()));
        as.setSamplePropertyCode(StringUtils.defaultString(vo.getSamplePropertyCode()));

        if (!applySampleService.updateByApplySampleId(as)) {
            throw new IllegalStateException("修改备注失败");
        }

        // 样本备注 变化
        final String sampleRemarkChange = new CompareUtils<ApplySampleDto>().compare(applySample, as,
                Lists.newArrayList("resultRemark", "sampleTypeName", "sampleProperty"));

        if (StringUtils.isNotBlank(sampleRemarkChange)) {
            LoginUserHandler.User user = LoginUserHandler.get();
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setApplyId(applySample.getApplyId());
            sampleFlow.setApplySampleId(applySample.getApplySampleId());
            sampleFlow.setBarcode(applySample.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.APPLY_INFO_UPDATE.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.APPLY_INFO_UPDATE.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(String.format("检验页面修改样本信息:%s", sampleRemarkChange));
            // 添加流水
            sampleFlowService.addSampleFlow(sampleFlow);
        }

        return Collections.emptyMap();
    }

    /**
     * 修改样本号
     */
    @PostMapping("/change-sample-no")
    public Object changeSampleNo(@RequestBody ApplySampleChangeSampleNoVo vo) {

        if (Objects.isNull(vo.getApplySampleId())) {
            throw new IllegalArgumentException("参数错误");
        }

        if (StringUtils.isBlank(vo.getSampleNo())) {
            throw new IllegalArgumentException("样本号不能为空");
        }

        if (vo.getSampleNo().length() > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("样本号不能超过 " + INPUT_MAX_LENGTH + " 个字符");
        }

        applySampleService.changeSampleNo(vo.getApplySampleId(), vo.getSampleNo());

        return Collections.emptyMap();
    }

    /**
     * 申请单信息
     */
    @GetMapping("/apply-info")
    public Object applyInfo(@RequestParam Long applySampleId) {

        if (Objects.isNull(applySampleId)) {
            throw new IllegalArgumentException("参数错误");
        }
        if (Objects.equals(applySampleId, NumberUtils.LONG_ZERO)) {
            return new SampleApplyInfoVo();
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        final SampleApplyInfoVo sampleApplyInfo = new SampleApplyInfoVo();
        BeanUtils.copyProperties(apply, sampleApplyInfo);
        BeanUtils.copyProperties(applySample, sampleApplyInfo);

        sampleApplyInfo.setUrgent(apply.getUrgent());
        // 临床诊断
        sampleApplyInfo.setClinicalDiagnosis(apply.getDiagnosis());

        if (Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
            sampleApplyInfo.setStatusText(SampleStatusEnum.NOT_AUDIT.getDesc());
        } else if (Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) {
            sampleApplyInfo.setStatusText(SampleStatusEnum.ONE_AUDIT.getDesc());
        } else if (Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            sampleApplyInfo.setStatusText(SampleStatusEnum.AUDIT.getDesc());
        } else {
            sampleApplyInfo.setStatusText(StringUtils.EMPTY);
        }

        switch (ItemTypeEnum.getByName(applySample.getItemType())) {
            case GENETICS: {
                final GeneticsSampleDto sample = geneticsSampleService.selectByApplySampleId(applySampleId);
                if (Objects.nonNull(sample)) {
                    sampleApplyInfo.setOneCheckerName(sample.getOneCheckerName());
                    sampleApplyInfo.setTwoCheckerName(sample.getTwoCheckerName());
                    sampleApplyInfo.setSampleNo(sample.getSampleNo());
                    sampleApplyInfo.setTestDate(sample.getTestDate());
                    sampleApplyInfo.setCheckDate(sample.getTwoCheckDate());
                }
                break;
            }
            case ROUTINE: {
                final SampleDto sample = sampleService.selectByApplySampleId(applySampleId);
                if (Objects.nonNull(sample)) {
                    sampleApplyInfo.setOneCheckerName(sample.getOneCheckerName());
                    sampleApplyInfo.setTwoCheckerName(sample.getTwoCheckerName());
                    sampleApplyInfo.setSampleNo(sample.getSampleNo());
                    sampleApplyInfo.setCheckDate(sample.getTwoCheckDate());
                    sampleApplyInfo.setTestDate(sample.getTestDate());
                }
                break;
            }
            case INFECTION: {
                final InfectionSampleDto sample = infectionSampleService.selectByApplySampleId(applySampleId);
                if (Objects.nonNull(sample)) {
                    sampleApplyInfo.setOneCheckerName(sample.getCheckerName());
                    sampleApplyInfo.setTwoCheckerName(sample.getCheckerName());
                    sampleApplyInfo.setSampleNo(sample.getSampleNo());
                    sampleApplyInfo.setCheckDate(sample.getCheckDate());
                    sampleApplyInfo.setTestDate(sample.getTestDate());
                }
                break;
            }
            case MICROBIOLOGY: {
                final MicrobiologySampleDto sample = microbiologySampleService.selectByApplySampleId(applySampleId);
                if (Objects.nonNull(sample)) {
                    sampleApplyInfo.setOneCheckerName(sample.getOneCheckerName());
                    sampleApplyInfo.setTwoCheckerName(sample.getCheckerName());
                    sampleApplyInfo.setSampleNo(sample.getSampleNo());
                    sampleApplyInfo.setTestDate(sample.getTestDate());
                    sampleApplyInfo.setCheckDate(sample.getCheckDate());
                }
                break;
            }
            case SPECIALTY: {
                final SpecialtySampleDto sample = specialtySampleService.selectByApplySampleId(applySampleId);
                if (Objects.nonNull(sample)) {
                    sampleApplyInfo.setOneCheckerName(sample.getOneCheckerName());
                    sampleApplyInfo.setTwoCheckerName(sample.getTwoCheckerName());
                    sampleApplyInfo.setCheckDate(sample.getTwoCheckDate());
                    sampleApplyInfo.setSampleNo(sample.getSampleNo());
                    sampleApplyInfo.setTestDate(sample.getTestDate());
                }
                break;
            }
            case OUTSOURCING: {
                final OutsourcingSampleDto sample = outsourcingSampleService.selectByApplySampleId(applySampleId);
                if (Objects.nonNull(sample)) {
                    sampleApplyInfo.setOneCheckerName(sample.getCheckerName());
                    sampleApplyInfo.setTwoCheckerName(sample.getCheckerName());
                    sampleApplyInfo.setSampleNo(sample.getSampleNo());
                    sampleApplyInfo.setTestDate(sample.getTestDate());
                    sampleApplyInfo.setCheckDate(sample.getCheckDate());
                }
                break;
            }
            case BLOOD_CULTURE: {
                final BloodCultureSampleDto sample = bloodCultureSampleService.selectByApplySampleId(applySampleId);
                if (Objects.nonNull(sample)) {
                    sampleApplyInfo.setOneCheckerName(sample.getOneCheckerName());
                    sampleApplyInfo.setTwoCheckerName(sample.getTwoCheckerName());
                    sampleApplyInfo.setSampleNo(sample.getSampleNo());
                    sampleApplyInfo.setTestDate(sample.getTestDate());
                    sampleApplyInfo.setCheckDate(null);

                    if (!Objects.equals(sample.getOneCheckDate(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
                        sampleApplyInfo.setCheckDate(sample.getOneCheckDate());
                    }

                    if (!Objects.equals(sample.getTwoCheckDate(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
                        sampleApplyInfo.setCheckDate(sample.getTwoCheckDate());
                    }
                }
                break;
            }

        }

        //1.1.3.7 新增样本异常登记
        List<SampleAbnormalDto> abnormalDtoList = sampleAbnormalService.selectByBarcodes(Collections.singletonList(applySample.getBarcode()));
        if (CollectionUtils.isNotEmpty(abnormalDtoList)){
            sampleApplyInfo.setSampleAbnormalId(abnormalDtoList.get(0).getSampleAbnormalId());
        }

        return sampleApplyInfo;
    }

    /**
     * 预览报告单
     */
    @PostMapping("/preview-report-old")
    public Object previewReport(@RequestBody List<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        final Map<Long, SampleReportDto> reports = sampleReportService.selectByApplySampleIds(applySampleIds).stream()
                .collect(Collectors.toMap(SampleReportDto::getApplySampleId, v -> v, (a, b) -> a));
        if (MapUtils.isEmpty(reports)) {
            return Collections.emptyList();
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            return Collections.emptyList();
        }
        final LoginUserHandler.User user = LoginUserHandler.get();
        // 预览报告单流水记录
        sampleFlowService.addSampleFlows(applySamples.stream().map(e -> SampleFlowDto.builder()
                .applyId(e.getApplyId())
                .applySampleId(e.getApplySampleId())
                .barcode(e.getBarcode())
                .operator(user.getNickname())
                .operatorId(user.getUserId())
                .operateCode(BarcodeFlowEnum.PREVIEW_REPORT.name())
                .operateName(BarcodeFlowEnum.PREVIEW_REPORT.getDesc())
                .content(BarcodeFlowEnum.PREVIEW_REPORT.getDesc()).build()).collect(Collectors.toList()));

        final List<String> urls = new LinkedList<>();

        // 保证顺序一致
        for (Long applySampleId : applySampleIds) {
            if (reports.containsKey(applySampleId)) {
                urls.add(reports.get(applySampleId).getUrl());
            }
        }

        return urls;
    }

    /**
     * 预览报告单
     */
    @PostMapping("/preview-report")
    public Object previewReportNew(@RequestBody List<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        // 查询样本信息
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            return Collections.emptyList();
        }


        // 审核的样本id
        List<Long> auditApplySampleIds = applySamples.stream().filter(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode())).map(p -> p.getApplySampleId()).collect(Collectors.toList());
        // 未审核的样本id
        List<Long> unAuditApplySampleIds = applySamples.stream().filter(e -> !Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode())).map(p -> p.getApplySampleId()).collect(Collectors.toList());


        Map<Long,String> reportMap = new HashMap<>();

        // 已审核样本报告
        List<String> auditUrls  = (List<String>)this.previewReport(auditApplySampleIds);
        if (CollectionUtils.isNotEmpty(auditUrls)){
            for (int i = 0; i < auditUrls.size(); i++) {
                reportMap.put(auditApplySampleIds.get(i),auditUrls.get(i));
            }
        }

        // 未审核样本报告
        String itemType = null;
        if (CollectionUtils.isNotEmpty(unAuditApplySampleIds)){
            //获取项目Item类型
            itemType = applySamples.get(0).getItemType();
        }
        List<String> unAuditUrls = this.synthesisReport(unAuditApplySampleIds, itemType);
        if (CollectionUtils.isNotEmpty(unAuditUrls)) {
            for (int i = 0; i < unAuditUrls.size(); i++) {
                reportMap.put(unAuditApplySampleIds.get(i), unAuditUrls.get(i));
            }
        }


        final LoginUserHandler.User user = LoginUserHandler.get();
        // 预览报告单流水记录
        sampleFlowService.addSampleFlows(applySamples.stream().map(e -> SampleFlowDto.builder()
                .applyId(e.getApplyId())
                .applySampleId(e.getApplySampleId())
                .barcode(e.getBarcode())
                .operator(user.getNickname())
                .operatorId(user.getUserId())
                .operateCode(BarcodeFlowEnum.PREVIEW_REPORT.name())
                .operateName(BarcodeFlowEnum.PREVIEW_REPORT.getDesc())
                .content(BarcodeFlowEnum.PREVIEW_REPORT.getDesc()).build()).collect(Collectors.toList()));

        final List<String> urls = new LinkedList<>();

        // 保证顺序一致
        for (Long applySampleId : applySampleIds) {
            if (StringUtils.isNotBlank(reportMap.get(applySampleId))) {
                urls.add(reportMap.get(applySampleId));
            }
        }

        return urls;
    }


    /**
     * 打印报告单，修改一下状态
     */
    @PostMapping("/print-report")
    public Object printReport(@RequestBody List<Long> applySampleIds) throws InterruptedException, ExecutionException, TimeoutException {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        // 修改状态
        final Callable<Void> updateStatus = () -> {
            LoginUserHandler.set(user);
            final ApplySampleDto dto = new ApplySampleDto();
            dto.setPrinterId(user.getUserId());
            dto.setPrinterName(user.getNickname());
            dto.setPrintDate(new Date());
            dto.setIsPrint(YesOrNoEnum.YES.getCode());
            applySampleService.updateByApplySampleIds(dto, applySampleIds);
            return null;
        };


        // 添加条码环节
        final Callable<Void> addFlows = () -> {
            LoginUserHandler.set(user);

            final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
            if (CollectionUtils.isEmpty(applySamples)) {
                return null;
            }

            // 分片新增条码环节
            for (List<ApplySampleDto> list : ListUtils.partition(applySamples, 100)) {
                sampleFlowService.addSampleFlows(list.stream().map(e -> SampleFlowDto.builder()
                        .applyId(e.getApplyId())
                        .applySampleId(e.getApplySampleId())
                        .barcode(e.getBarcode())
                        .operator(user.getNickname())
                        .operatorId(user.getUserId())
                        .operateCode(BarcodeFlowEnum.PRINT_REPORT.name())
                        .operateName(BarcodeFlowEnum.PRINT_REPORT.getDesc())
                        .content(BarcodeFlowEnum.PRINT_REPORT.getDesc()).build()).collect(Collectors.toList()));
            }

            return null;
        };

        for (Future<Void> future : executorService.invokeAll(List.of(updateStatus, addFlows))) {
            future.get(1, TimeUnit.MINUTES);
        }

        return Collections.emptyMap();
    }

    /**
     * 打印条码，记录条码环节
     */
    @PostMapping("/print-barcode")
    public Object printBarcode(@RequestBody List<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Map.of();
        }


        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            return Map.of();
        }

        // 分片新增条码环节
        for (List<ApplySampleDto> list : ListUtils.partition(applySamples, 100)) {
            sampleFlowService.addSampleFlows(list.stream().map(e -> SampleFlowDto.builder()
                            .applyId(e.getApplyId())
                            .applySampleId(e.getApplySampleId())
                            .barcode(e.getBarcode())
                            .operator(LoginUserHandler.get().getNickname())
                            .operatorId(LoginUserHandler.get().getUserId())
                            .operateCode(BarcodeFlowEnum.PRINT_BARCODE.name())
                            .operateName(BarcodeFlowEnum.PRINT_BARCODE.getDesc())
                            .content(BarcodeFlowEnum.PRINT_BARCODE.getDesc()).build())
                    .collect(Collectors.toList()));
        }

        return Map.of();
    }

    /**
     * 条码环节
     */
    @GetMapping("/flows")
    public Object flows(@RequestParam Long applySampleId) {
        if (Objects.isNull(applySampleId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<SampleFlowDto> sampleFlows = sampleFlowService.selectByApplySampleId(applySampleId);
        if (CollectionUtils.isEmpty(sampleFlows)) {
            return Collections.emptyList();
        }

        return JSON.parseArray(JSON.toJSONString(sampleFlows), SampleFlowVo.class);
    }


    /**
     * 报告pdf导出
     */
    @PostMapping("/exportPdfReport")
    public ResponseEntity<FileSystemResource> exportPdfReport(@RequestBody List<Long> applySampleIds) throws Exception {

        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("请选择需要导出的样本");
        }

        final List<SampleReportDto> reports = sampleReportService.selectByApplySampleIds(applySampleIds);

        final File tempFile = File.createTempFile("exportPdfReport-", ".zip");

        try (ZipWriter writer = ZipWriter.of(tempFile, StandardCharsets.UTF_8)) {
            final Semaphore semaphore = new Semaphore(10);
            final Lock lock = new ReentrantLock();

            final AtomicInteger count = new AtomicInteger(0);
            final List<Future<?>> futures = new LinkedList<>();

            for (SampleReportDto report : reports) {
                semaphore.acquire();
                futures.add(executorService.submit(() -> {
                    try (HttpResponse response = HttpUtil.createGet(report.getUrl()).execute()) {
                        final byte[] bytes = response.bodyBytes();
                        lock.lock();
                        try {
                            final ByteArrayInputStream is = new ByteArrayInputStream(bytes);
                            writer.add((count.incrementAndGet()) + "-" + report.getBarcode() + "^LabResultReport.pdf",
                                    is);
                            IOUtils.closeQuietly(is);
                        } finally {
                            lock.unlock();
                        }
                    } finally {
                        semaphore.release();
                    }
                }));
            }

            for (Future<?> future : futures) {
                future.get(30, TimeUnit.SECONDS);
            }
        }

        return ResponseEntity.status(HttpStatus.OK)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE)
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment;fileName=" + URLEncoder.encode(
                                DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + "PDF导出", StandardCharsets.UTF_8) + ".zip")
                .body(new FileSystemResource(tempFile));

    }

    @PostMapping("/sampleTrack")
    public Object getSampleTrack(@RequestParam("applySampleId") Long applySampleId) {

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        query.setApplySampleIds(Collections.singleton(applySampleId));

        final List<BaseSampleEsModelDto> sampleEsModels = elasticSearchSampleService.selectSamples(query);

        final BaseSampleEsModelDto sample = sampleEsModels.stream().findFirst().orElse(null);

        if (Objects.isNull(sample)) {
            throw new IllegalStateException("样本不存在");
        }

        final ApplyDto apply = applyService.selectByApplyId(sample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("样本申请单不存在");
        }

        final List<SampleFlowDto> sampleFlows = sampleFlowService.selectByApplySampleId(sample.getApplySampleId());
        final SampleTrackVo trackVo = new SampleTrackVo();

        trackVo.setSampleNo(sample.getSampleNo());
        trackVo.setBarcode(sample.getBarcode());
        trackVo.setPatientName(apply.getPatientName());
        trackVo.setPatientSex(apply.getPatientSex());
        trackVo.setPatientAge(apply.getPatientAge());
        trackVo.setPatientSubage(apply.getPatientSubage());
        trackVo.setPatientSubageUnit(apply.getPatientSubageUnit());

        trackVo.setSampleTracks(initSampleTrack(apply, sampleFlows, sample));

        return trackVo;
    }

    private List<SampleTrackVo.SampleTrack> initSampleTrack(ApplyDto apply, List<SampleFlowDto> sampleFlows, BaseSampleEsModelDto dto) {
        final String source = apply.getSource();

        final LinkedList<SampleTrackVo.SampleTrack> tracks = new LinkedList<>();

        final int isShowLogisSample = BooleanUtils.toInteger(Objects.equals(source, ApplySourceEnum.SUPPLEMENTARY.name()));
        final int isShowManualSample = BooleanUtils.toInteger(Objects.equals(source, ApplySourceEnum.MANUAL.name()));
        final int isShowHisSample = BooleanUtils.toInteger(Objects.equals(source, ApplySourceEnum.HIS.name()));
        final int isShowPhysicalSign = BooleanUtils.toInteger(Objects.equals(source, ApplySourceEnum.PHYSICAL_SIGN.name()));

        // 物流取样
        final SampleFlowDto logisticsSampleFlow = sampleFlows.stream()
                .filter(e -> Objects.equals(e.getOperateCode(), BarcodeFlowEnum.LOGISTICS_SAMPLE.name())).findFirst()
                .orElse(null);
        final SampleTrackVo.SampleTrack.SampleTrackBuilder logisticsSampleTrack = SampleTrackVo.SampleTrack.builder()
                .isFinish(YesOrNoEnum.NO.getCode())
                .isShow(isShowLogisSample)
                .operateCode(BarcodeFlowEnum.LOGISTICS_SAMPLE.name())
                .operateName(BarcodeFlowEnum.LOGISTICS_SAMPLE.getDesc());

        if (Objects.nonNull(logisticsSampleFlow)) {
            logisticsSampleTrack
                    .isFinish(YesOrNoEnum.YES.getCode())
                    .operatDate(logisticsSampleFlow.getCreateDate())
                    .operator(logisticsSampleFlow.getOperator());
        }
        tracks.add(logisticsSampleTrack.build());


        // 样本信息补录
        final SampleFlowDto sampleInfoSupplementFlow = sampleFlows.stream()
                .filter(e -> Objects.equals(e.getOperateCode(), BarcodeFlowEnum.SAMPLE_INFO_SUPPLEMENT.name())).findFirst()
                .orElse(null);

        final SampleTrackVo.SampleTrack.SampleTrackBuilder supplementaryTrack = SampleTrackVo.SampleTrack.builder()
                .isFinish(YesOrNoEnum.NO.getCode())
                .isShow(isShowLogisSample)
                .operateCode(BarcodeFlowEnum.SAMPLE_INFO_SUPPLEMENT.name())
                .operateName(BarcodeFlowEnum.SAMPLE_INFO_SUPPLEMENT.getDesc());
        if (Objects.nonNull(sampleInfoSupplementFlow) && Objects.equals(apply.getSource(), ApplySourceEnum.SUPPLEMENTARY.name())) {
            supplementaryTrack
                    .isFinish(YesOrNoEnum.YES.getCode())
                    .operatDate(sampleInfoSupplementFlow.getCreateDate())
                    .operator(sampleInfoSupplementFlow.getOperator());
        }
        tracks.add(supplementaryTrack.build());

        // 样本信息录入
        final SampleTrackVo.SampleTrack.SampleTrackBuilder manualTrack = SampleTrackVo.SampleTrack.builder()
                .isFinish(YesOrNoEnum.NO.getCode())
                .operateCode("MANUAL")
                .operateName("样本信息录入")
                .isShow(isShowManualSample);
        if (Objects.equals(source, ApplySourceEnum.MANUAL.name())) {
            manualTrack
                    .isFinish(YesOrNoEnum.YES.getCode())
                    .operatDate(apply.getCreateDate())
                    .operator(apply.getCreatorName());
        }
        tracks.add(manualTrack.build());


        // 需要区分 补录复核 和 样本复核
        boolean isShowCheck = BooleanUtils.isFalse(Objects.equals(isShowPhysicalSign, YesOrNoEnum.YES.getCode())
                || Objects.equals(isShowHisSample, YesOrNoEnum.YES.getCode()));
        final SampleTrackVo.SampleTrack.SampleTrackBuilder check = SampleTrackVo.SampleTrack.builder()
                .isFinish(YesOrNoEnum.NO.getCode())
                .isShow(BooleanUtils.toInteger(isShowCheck))
                .operateCode("check");
        if (Objects.equals(source, ApplySourceEnum.MANUAL.name()) || Objects.equals(source, ApplySourceEnum.SUPPLEMENTARY.name())) {
            final Integer status = apply.getStatus();
            String checkType = Objects.equals(status, ApplyStatusEnum.DOUBLE_CHECK.getCode()) ? "双输复核" : "复核";

            final String content = String.format("%s%s", Objects.equals(ApplySourceEnum.MANUAL.name(), source) ? "样本" : "样本补录", checkType);
            check.operatDate(apply.getCreateDate())
                    .operator(apply.getCheckerName())
                    .isFinish(YesOrNoEnum.YES.getCode())
                    .operateName(content);
        }
        tracks.add(check.build());


        // 样本签收
        final SampleTrackVo.SampleTrack.SampleTrackBuilder sign = SampleTrackVo.SampleTrack.builder()
                .isFinish(YesOrNoEnum.NO.getCode())
                .isShow(isShowHisSample)
                .operateCode("HIS_SIGN")
                .operateName("样本签收");
        if (Objects.equals(source, ApplySourceEnum.HIS.name())) {
            sign.isFinish(YesOrNoEnum.YES.getCode())
                    .operatDate(apply.getCreateDate())
                    .operator(apply.getCreatorName());
        }
        tracks.add(sign.build());


        // 体检样本签收
        final SampleTrackVo.SampleTrack.SampleTrackBuilder physicalSign = SampleTrackVo.SampleTrack.builder()
                .isFinish(YesOrNoEnum.NO.getCode())
                .isShow(isShowPhysicalSign)
                .operateCode("PHYSICAL_SIGN")
                .operateName("体检样本签收");
        if (Objects.equals(source, ApplySourceEnum.PHYSICAL_SIGN.name())) {
            physicalSign
                    .isFinish(YesOrNoEnum.YES.getCode())
                    .operatDate(apply.getCreateDate())
                    .operator(apply.getCreatorName());
        }
        tracks.add(physicalSign.build());


        // 一次分拣
        final SampleFlowDto onePickFlow = sampleFlows.stream()
                .filter(e -> Objects.equals(e.getOperateCode(), BarcodeFlowEnum.ONE_PICK.name())).findFirst().orElse(null);
        final SampleTrackVo.SampleTrack.SampleTrackBuilder onePick = SampleTrackVo.SampleTrack.builder()
                .operateCode(BarcodeFlowEnum.ONE_PICK.name())
                .isShow(YesOrNoEnum.YES.getCode())
                .isFinish(YesOrNoEnum.YES.getCode())
                .operateName(BarcodeFlowEnum.ONE_PICK.getDesc());
        if (Objects.nonNull(onePickFlow)) {
            onePick
                    .isFinish(YesOrNoEnum.YES.getCode())
                    .operatDate(onePickFlow.getCreateDate())
                    .operator(onePickFlow.getOperator());
        }
        tracks.add(onePick.build());


        // 分血
        final SampleFlowDto splitBloodFlow =
                sampleFlows.stream().filter(e -> Objects.equals(e.getOperateCode(), BarcodeFlowEnum.SPLIT_BLOOD.name()))
                        .findFirst().orElse(null);
        if (Objects.nonNull(splitBloodFlow)) {
            final SampleTrackVo.SampleTrack splitBlood = SampleTrackVo.SampleTrack.builder()
                    .isFinish(YesOrNoEnum.YES.getCode())
                    .isShow(YesOrNoEnum.YES.getCode())
                    .operatDate(splitBloodFlow.getCreateDate())
                    .operator(splitBloodFlow.getOperator())
                    .operateCode(splitBloodFlow.getOperateCode())
                    .operateName(splitBloodFlow.getOperateName())
                    .build();
            tracks.add(splitBlood);
        }


        // 二次分拣
        final SampleFlowDto twoPickFlow = sampleFlows.stream()
                .filter(e -> Objects.equals(e.getOperateCode(), BarcodeFlowEnum.TWO_PICK.name())).findFirst().orElse(null);
        final Integer isTwoPick = dto.getIsTwoPick();
        final SampleTrackVo.SampleTrack.SampleTrackBuilder twoPick = SampleTrackVo.SampleTrack.builder()
                .isFinish(BooleanUtils.toInteger(Objects.equals(isTwoPick, YesOrNoEnum.YES.getCode())))
                .isShow(YesOrNoEnum.YES.getCode())
                .operateCode(BarcodeFlowEnum.TWO_PICK.name())
                .operateName(BarcodeFlowEnum.TWO_PICK.getDesc());
        if (Objects.nonNull(twoPickFlow)) {
            twoPick.operatDate(twoPickFlow.getCreateDate())
                    .operator(twoPickFlow.getOperator());
        }
        tracks.add(twoPick.build());


        final String itemType = dto.getItemType();
        // 检验
        SampleTrackVo.SampleTrack routine = new SampleTrackVo.SampleTrack(null, null, null,
                null, YesOrNoEnum.NO.getCode(), YesOrNoEnum.YES.getCode());
        try {
            final ItemTypeEnum itemTypeEnum = ItemTypeEnum.valueOf(itemType);
            routine.setOperateCode(itemTypeEnum.name());
            routine.setOperateName(itemTypeEnum.getDesc());
            routine.setIsFinish(YesOrNoEnum.YES.getCode());
            routine.setOperator(dto.getTesterName());
            routine.setOperatDate(dto.getTestDate());
        } catch (Exception e) {
            routine.setOperateName(StringUtils.EMPTY);
            routine.setOperateCode(StringUtils.EMPTY);
        }
        tracks.add(routine);


        // 报告审核
        final Integer sampleStatus = dto.getSampleStatus();
        final SampleTrackVo.SampleTrack.SampleTrackBuilder audit = SampleTrackVo.SampleTrack.builder()
                .isShow(YesOrNoEnum.YES.getCode())
                .operateCode("AUDIT")
                .operateName("报告审核")
                .operator(dto.getFinalCheckerName())
                .operatDate(dto.getFinalCheckDate())
                .isFinish(BooleanUtils.toInteger(Objects.equals(sampleStatus, SampleStatusEnum.AUDIT.getCode())));
        tracks.add(audit.build());


        // 报告打印
        final Integer isPrint = ObjectUtils.defaultIfNull(dto.getIsPrint(), NumberUtils.INTEGER_ZERO);
        final SampleTrackVo.SampleTrack.SampleTrackBuilder print = SampleTrackVo.SampleTrack.builder()
                .operateCode(BarcodeFlowEnum.PRINT_REPORT.name())
                .operateName(BarcodeFlowEnum.PRINT_REPORT.getDesc())
                .operator(dto.getPrinterName())
                .operatDate(dto.getPrintDate())
                .isFinish(isPrint)
                .isShow(YesOrNoEnum.YES.getCode());
        tracks.add(print.build());


        return tracks.stream().peek(p -> {
            if (Objects.equals(p.getOperatDate(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
                p.setOperatDate(null);
            }
        }).collect(Collectors.toList());
    }

    /**
     * 外送上传报告
     */
    @PostMapping("/upload-report")
    public Object uploadReport(@RequestBody UploadPdfDto uploadPdfDto){
        return sampleReportService.uploadReport(uploadPdfDto);
    }

    /**
     * 外送上传报告获取
     */
    @GetMapping("/get-report")
    public Object getReport(@RequestParam("applySampleId") Long applySampleId){
        if(Objects.isNull(applySampleId)){
            throw new IllegalArgumentException("申请单样本不能为空");
        }
        SampleReportDto oldSampleReport = sampleReportService.selectByApplySampleId(applySampleId);
        if(Objects.isNull(oldSampleReport) || Objects.equals(oldSampleReport.getIsUploadPdf(), YesOrNoEnum.NO.getCode())){
            return Map.of();
        }
        return oldSampleReport;
    }

    /**
     * 外送上传报告删除
     */
    @PostMapping("/delete-report")
    public Object deleteReport(@RequestBody SampleReportDto sampleReportDto){
        Long sampleReportId = sampleReportDto.getSampleReportId();
        if(Objects.isNull(sampleReportId)){
            throw new IllegalArgumentException("样本报告id不能为空");
        }
        SampleReportDto sampleReport = sampleReportService.selectBySampleReportId(sampleReportId);
        Assert.notNull(sampleReport, "未查询到样本报告");
        if(Objects.equals(sampleReport.getIsUploadPdf(), YesOrNoEnum.NO.getCode())){
            throw new IllegalStateException("该样本报告不是手动上传");
        }

        ApplySampleDto applySample = applySampleService.selectByApplySampleId(sampleReport.getApplySampleId());
        Assert.notNull(applySample, "样本不存在！");
        if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
            throw new IllegalStateException("样本不是未审核状态！");
        }

        boolean b = sampleReportService.deleteBySampleReportId(sampleReportId);

        LoginUserHandler.User user = LoginUserHandler.get();
        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setApplyId(applySample.getApplyId());
        sampleFlow.setApplySampleId(applySample.getApplySampleId());
        sampleFlow.setBarcode(applySample.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.SAMPLE_REPORT_DELETE.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.SAMPLE_REPORT_DELETE.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        sampleFlow.setContent(String.format("删除PDF报告:%s", sampleReport.getUrl()));
        sampleFlowService.addSampleFlow(sampleFlow);

        // 删除手动上传的缓存
        stringRedisTemplate.delete(SampleReportDto.getIsUploadPdfKey(sampleReport.getSampleId()));

        return b;
    }

    @Override
    public void destroy() throws Exception {
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        executorService = threadPoolConfig.getPool();
    }

    // 生成样本报告
    private List<String> synthesisReport(List<Long> unAuditApplySampleIds, String itemType) {
        if (CollectionUtils.isEmpty(unAuditApplySampleIds)){
            return Collections.emptyList();
        }

        final List<SampleReportDto> resultDros = new ArrayList<>();

        switch (ItemTypeEnum.getByName(itemType)) {
            case ROUTINE: {
                List<SampleReportDto> reportDtos = synthesisRoutinePdfService.synthesisPdf(unAuditApplySampleIds);
                resultDros.addAll(reportDtos);
                break;
            }
            case GENETICS: {
                List<SampleReportDto> reportDtos = synthesisGeneticsPdfService.synthesisPdf(unAuditApplySampleIds);
                resultDros.addAll(reportDtos);
                break;
            }
            case INFECTION: {
                List<SampleReportDto> reportDtos = synthesisInfectionPdfService.synthesisPdf(unAuditApplySampleIds);
                resultDros.addAll(reportDtos);
                break;
            }
            case MICROBIOLOGY: {
                List<SampleReportDto> reportDtos = synthesisMicrobiologyPdfService.synthesisPdf(unAuditApplySampleIds);
                resultDros.addAll(reportDtos);
                break;
            }
            case SPECIALTY: {
                List<SampleReportDto> reportDtos = synthesisSpecialtyPdfService.synthesisPdf(unAuditApplySampleIds);
                resultDros.addAll(reportDtos);
                break;
            }
            case OUTSOURCING: {
                List<SampleReportDto> reportDtos = synthesisOutsourcingPdfService.synthesisPdf(unAuditApplySampleIds);
                resultDros.addAll(reportDtos);
                break;
            }
            default: {
                throw new IllegalStateException("不支持的检验类型！");
            }

        }

        final List<String> resultUrls = new ArrayList<>();
        // 按照顺序返回
        for (Long unAuditApplySampleId : unAuditApplySampleIds) {
            resultUrls.add(resultDros.stream().filter(p -> Objects.equals(p.getApplySampleId(), unAuditApplySampleId)).findFirst().orElse(new SampleReportDto()).getUrl());
        }

        return resultUrls;
    }



}
