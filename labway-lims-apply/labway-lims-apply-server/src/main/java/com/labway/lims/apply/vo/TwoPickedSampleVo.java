package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 等待二次分拣的样本
 */
@Getter
@Setter
public class TwoPickedSampleVo {

    /**
     * 开始 分拣日期
     */
    private Date beginTwoPickDate;

    /**
     * 结束 分拣日期
     */
    private Date endTwoPickDate;

    /**
     * 检验项目ID / 检验目的ID
     */
    private Long testItemId;


    /**
     * 样本号 专门导出excel用
     */
    private String sampleNo;

    /**
     * 姓名 专门导出excel用
     */
    private String patientName;


    /**
     * 项目类型
     * <p>
     * 不传为默认 排除 微生物、院感
     * <p>
     * INFECTION 院感
     * <p>
     * MICROBIOLOGY
     *
     */
    private String itemType;

    /**
     * 送检机构编码
     */
    private List<String> hspOrgIds;

}
