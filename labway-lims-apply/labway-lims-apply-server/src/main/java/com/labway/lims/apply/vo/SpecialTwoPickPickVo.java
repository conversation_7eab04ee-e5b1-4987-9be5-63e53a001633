package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.base.TwoPickTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 特殊二次分拣
 */
@Getter
@Setter
public class SpecialTwoPickPickVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * @see com.labway.lims.api.enums.base.TwoPickTypeEnum
     * 二次分拣类型
     */
    private String twoPickType;

    /**
     * 指定二次分拣时间
     */
    private String twoPickDay;

    /**
     * 原始二次分拣时间
     */
    private String originPickDay;

    /**
     * 指定项目分拣
     */
    private List<String> testItemIds;

    /**
     * 校验入参
     */
    public void validParam(){
        if (StringUtils.isBlank(barcode)){
            throw new IllegalArgumentException("条码不能为空");
        }

        if (Objects.isNull(TwoPickTypeEnum.selectByCode(twoPickType))){
            throw new IllegalArgumentException("二次分拣类型不正确");
        }

        //强制二次分拣&强制项目二次分拣的 指定时间为必填
        if (StringUtils.isBlank(twoPickDay) && (TwoPickTypeEnum.selectByCode(twoPickType) != TwoPickTypeEnum.COMMON)){
            throw new IllegalArgumentException("指定分拣时间不能为空");
        }
    }

}
