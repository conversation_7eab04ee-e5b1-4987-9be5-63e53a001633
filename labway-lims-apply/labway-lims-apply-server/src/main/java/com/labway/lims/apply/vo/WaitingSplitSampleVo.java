package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 获取待分血列表
 */
@Setter
@Getter
public class WaitingSplitSampleVo {
    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 检验项目名称
     */
    private List<String> testItemNames;

    /**
     * 专业组名称
     */
    private List<String> groupNames;

    /**
     * 样本类型
     */
    private String sampleType;

    /**
     * 管型
     */
    private String tube;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 性别
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 送检机构
     */
    private String hspOrgName;

}
