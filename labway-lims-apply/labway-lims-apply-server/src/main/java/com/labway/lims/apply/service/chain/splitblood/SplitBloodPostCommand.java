package com.labway.lims.apply.service.chain.splitblood;

import cn.hutool.extra.spring.SpringUtil;
import com.labway.business.center.compare.request.result.NotifySplitBloodRequest;
import com.labway.business.center.compare.service.result.LimsService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SplitBloodPostCommand implements Command {

    @Value("${business-center.org-code:未知实验室编码}")
    private String orgCode;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private LimsService limsService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;


    @Override
    public boolean execute(Context c) throws Exception {

        final SplitBloodContext context = SplitBloodContext.from(c);
        List<Long> applySampleIds = context.getApplySampleIds();

        // 样本id为空
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return CONTINUE_PROCESSING;
        }

        final List<ApplySampleDto> samples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(samples)) {
            return CONTINUE_PROCESSING;
        }

        // 通知业务中台分血
        LoginUserHandler.User user = LoginUserHandler.get();
        threadPoolConfig.getPool().execute(() -> {
            SpringUtil.getBean(SplitBloodPostCommand.class).notifyBusinessSplitBlood(samples,user);
        });

        return CONTINUE_PROCESSING;
    }


    // 通知业务中台样本分血
    public void notifyBusinessSplitBlood(List<ApplySampleDto> samples,LoginUserHandler.User user) {

        List<String> splitBarcodes = samples.stream().map(e -> e.getBarcode()).filter(p -> p.contains("_")).collect(Collectors.toList());

        // body填充
        NotifySplitBloodRequest notifySplitBloodVo = new NotifySplitBloodRequest();
        notifySplitBloodVo.setSignOrgCode(orgCode);
        notifySplitBloodVo.setSignBarcode(samples.get(0).getBarcode().split("_")[0]);
        notifySplitBloodVo.setSplitBarcodes(splitBarcodes);
        notifySplitBloodVo.setOptUserId(String.valueOf(user.getUserId()));
        notifySplitBloodVo.setOptUserName(user.getUsername());
        limsService.notifySplitBlood(notifySplitBloodVo);
    }


}
