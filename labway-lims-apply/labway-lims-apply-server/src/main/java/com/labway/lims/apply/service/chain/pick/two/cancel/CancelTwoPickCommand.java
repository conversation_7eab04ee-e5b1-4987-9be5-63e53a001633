package com.labway.lims.apply.service.chain.pick.two.cancel;

import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.service.chain.pick.two.TwoPickCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分拣
 */
@Slf4j
@Component
public class CancelTwoPickCommand implements Command, InitializingBean {
    @Resource
    private TwoPickCommand twoPickCommand;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {

        final CancelTwoPickContext context = CancelTwoPickContext.from(c);


        final List<ApplySampleDto> ms = context.getApplySamples().stream().filter(k ->
                        Objects.equals(k.getItemType(), ItemTypeEnum.MICROBIOLOGY.name()))
                .collect(Collectors.toList());
        final Map<Long, List<SampleFlowDto>> flows = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ms)) {
            flows.putAll(sampleFlowService.selectWithOutContentByApplySampleIdsAsMap(ms.stream()
                    .map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet())));
        }

        for (ApplySampleDto e : context.getApplySamples()) {
            twoPickCommand.getSampleTwoPicker(e.getItemType())
                    .twoUnPick(Collections.singleton(e.getApplySampleId()));

            // 标记此样本是否来自血培养
            context.put(CancelTwoPickContext.FROM_BC + e.getApplySampleId(), flows.getOrDefault(e.getApplySampleId(), List.of()).stream()
                    .anyMatch(k -> Objects.equals(k.getOperateCode(), BarcodeFlowEnum.BC_MARK_POSITIVE.name())));
        }

        return CONTINUE_PROCESSING;
    }


    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
