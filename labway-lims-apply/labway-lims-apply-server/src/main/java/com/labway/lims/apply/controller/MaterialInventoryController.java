package com.labway.lims.apply.controller;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.UpdateInventoryLimitDto;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import com.labway.lims.apply.mapstruct.MaterialInventoryConverter;
import com.labway.lims.apply.service.listener.ImportMaterialInventoryListener;
import com.labway.lims.apply.vo.ImportErrorResponseVo;
import com.labway.lims.apply.vo.MaterialInventoryImportHeadVo;
import com.labway.lims.apply.vo.MaterialInventoryListResponseVo;
import com.labway.lims.apply.vo.MaterialInventoryVo;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import com.labway.lims.base.api.dto.MaterialDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.MaterialService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料库存 API
 *
 * <AUTHOR>
 * @since 2023/5/9 19:05
 */
@Slf4j
@RestController
@RequestMapping("/material-inventory")
public class MaterialInventoryController extends BaseController {

    @Resource
    private MaterialInventoryService materialInventoryService;
    @Resource
    private MaterialInventoryConverter materialInventoryConverter;
    @DubboReference
    private GroupMaterialService groupMaterialService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private MaterialService materialService;

    /**
     * 当前 用户专业组 下 物料库存 信息
     * <p>
     * 使用地方:1:物料领用登记-选择物料下拉框
     */
    @PostMapping("/current-group-list")
    public Object materialInventoryCurrentGroupList() {
        LoginUserHandler.User user = LoginUserHandler.get();
        List<MaterialInventoryVo> materialInventoryVos = materialInventoryConverter
                .materialInventoryVoListFromDto(materialInventoryService.selectByGroupId(user.getGroupId()));

        // 移除数量为零的数据
         materialInventoryVos.removeIf(materialInventoryVo -> materialInventoryVo.getMainUnitInventory()==null  || materialInventoryVo.getMainUnitInventory().compareTo(BigDecimal.ZERO) <= 0
            || materialInventoryVo.getAssistUnitInventory() == null || materialInventoryVo.getAssistUnitInventory().compareTo(BigDecimal.ZERO) <= 0
        );

         return materialInventoryVos;
    }

    /**
     * 库存管理 物料列表
     *
     */
    @PostMapping("/material-list")
    public Object materialInventoryList() {
        LoginUserHandler.User user = LoginUserHandler.get();
        List<MaterialInventoryDto> materialInventoryDtos = materialInventoryService.selectByGroupId(user.getGroupId());

        // 物料 以物料id 分组
        Map<Long, List<MaterialInventoryDto>> groupingByMaterialId =
                materialInventoryDtos.stream().collect(Collectors.groupingBy(MaterialInventoryDto::getMaterialId));

        // 获取 专业组下这些物料对应关联配置
        List<GroupMaterialDto> groupMaterialDtos =
                groupMaterialService.selectByGroupIdAndMaterialIds(user.getGroupId(), groupingByMaterialId.keySet());

        // key: 物料id value: 专业组物料信息
        Map<Long, GroupMaterialDto> groupMaterialByMaterialId =
                groupMaterialDtos.stream().collect(Collectors.toMap(GroupMaterialDto::getMaterialId, Function.identity()));

        List<MaterialInventoryListResponseVo> targetList = Lists.newArrayListWithCapacity(groupingByMaterialId.size());
        Date date = new Date();
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        for (Map.Entry<Long, List<MaterialInventoryDto>> entry : groupingByMaterialId.entrySet()) {
            // 同一物料 不同批号 排序
            List<MaterialInventoryDto> value = entry.getValue().stream()
                    .sorted(Comparator.comparing(MaterialInventoryDto::getValidDate, Comparator.reverseOrder()))
                    .collect(Collectors.toList());

            // 取最新条作为物料信息
            MaterialInventoryListResponseVo target =
                    materialInventoryConverter.materialInventoryListResponseVoFromDto(value.get(0));

            GroupMaterialDto groupMaterialDto = groupMaterialByMaterialId.get(target.getMaterialId());

            List<MaterialInventoryListResponseVo.MaterialInventoryItemVo> inventoryItemVoList =
                    Lists.newArrayListWithCapacity(value.size());
            for (MaterialInventoryDto inventoryDto : value) {
                MaterialInventoryListResponseVo.MaterialInventoryItemVo temp =
                        materialInventoryConverter.materialInventoryItemVoFromDto(inventoryDto);

                temp.setProximityFlag(false);
                if (Objects.nonNull(groupMaterialDto) && groupMaterialDto.getValidRemindDay() > 0
                        && !date.after(temp.getValidDate())) {
                    // 存在 效期提醒 且查询时间在有效时间之前
                    LocalDate localDate2 = temp.getValidDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    // 计算日期差异
                    int daysBetween = (int) ChronoUnit.DAYS.between(localDate, localDate2);
                    if (daysBetween <= groupMaterialDto.getValidRemindDay()) {
                        temp.setProximityFlag(true);
                    }
                }

                temp.setExpiredFlag(date.after(temp.getValidDate()));

                if (Objects.equals(temp.getMainUnitInventory(), 0)) {
                    // 库存主数量为0的数据不需要显示
                    temp.setProximityFlag(false);
                    temp.setExpiredFlag(false);
                }

                // 列表增加库存数量字段
                // 辅库存
                BigDecimal assist = ObjectUtils.defaultIfNull(temp.getAssistUnitInventory(), new BigDecimal(NumberUtils.INTEGER_ZERO));
                // 主库存
                BigDecimal main = ObjectUtils.defaultIfNull(temp.getMainUnitInventory(), BigDecimal.ZERO);
                temp.setInventorySum(assist.add(main));

                inventoryItemVoList.add(temp);

            }

            // 按照失效日期倒序
            inventoryItemVoList = inventoryItemVoList.stream()
                    .sorted(Comparator.comparing(MaterialInventoryListResponseVo.MaterialInventoryItemVo::getValidDate,
                            Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            // 统计物料 主、辅 库存数量
            BigDecimal mainUnitInventorySum = value.stream().map(MaterialInventoryDto::getMainUnitInventory).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal assistUnitInventorySum = value.stream().map(MaterialInventoryDto::getAssistUnitInventory)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 补充信息
            target.setMainUnitInventorySum(mainUnitInventorySum);
            target.setAssistUnitInventorySum(assistUnitInventorySum);
            target.setInventoryItemVoList(inventoryItemVoList);
            // 存在 临近标签 则物料也展示 过期
            target.setProximityFlag(inventoryItemVoList.stream().filter(item -> item.getMainUnitInventory().compareTo(BigDecimal.ZERO) > 0)
                    .anyMatch(MaterialInventoryListResponseVo.MaterialInventoryItemVo::isProximityFlag));
            // 存在 过期 则物料也展示 过期
            target.setExpiredFlag(inventoryItemVoList.stream().filter(item -> item.getMainUnitInventory().compareTo(BigDecimal.ZERO) > 0)
                    .anyMatch(MaterialInventoryListResponseVo.MaterialInventoryItemVo::isExpiredFlag));
            // 有效期提醒天数
            target.setValidRemindDay(Optional.ofNullable(groupMaterialDto).map(GroupMaterialDto::getValidRemindDay).orElse(null));
            // 库存数量
            target.setInventorySum(assistUnitInventorySum.add(mainUnitInventorySum));

            targetList.add(target);
        }

        // 根据 materialIds 查询物料信息并把从存储温度、注册证号、注册证名称设置进去
        Set<Long> materialIds = targetList.stream().map(MaterialInventoryListResponseVo::getMaterialId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(materialIds)) {
            return Collections.emptyList();
        }
        List<MaterialDto> materialDtos = materialService.selectByIds(materialIds);
        Map<Long, MaterialDto> materialDtoMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, Function.identity()));
        targetList.forEach(m -> {
            MaterialDto materialDto = materialDtoMap.getOrDefault(m.getMaterialId(),null);
            if (Objects.nonNull(materialDto)) {
                m.setType(materialDto.getType());
                m.setStorageTemperature(materialDto.getStorageTemperature());
                m.setRegistrationNumber(materialDto.getRegistrationNumber());
                m.setRegistrationName(materialDto.getRegistrationName());
            }
        });

        return targetList.stream()
                .sorted(Comparator.comparing(obj -> PinyinUtil.getFirstLetter(obj.getMaterialName(), StringUtils.EMPTY)))
                .collect(Collectors.toList());
    }

    /**
     * 导入库存-用于初始数据-模版
     */
    @PostMapping("/material-inventory-template")
    public Object materialInventoryTemplate() {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        // -------定义些简单excel样式----------------
        // 头的策略
        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        // 内容策略
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("微软雅黑");
        headWriteFont.setFontHeightInPoints((short) 13);
        headCellStyle.setWriteFont(headWriteFont);
        HorizontalCellStyleStrategy excelStyle =
                new HorizontalCellStyleStrategy(headCellStyle, (List<WriteCellStyle>) null);

        try (ExcelWriter excelWriter =
                     EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle).build()) {

            List<List<Object>> list0 = Collections.emptyList();

            // 获取sheet对象
            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "物料库存").head(MaterialInventoryImportHeadVo.class)
                    .needHead(Boolean.TRUE).build();

            excelWriter.write(list0, sheet0);
        } catch (Exception e) {
            log.error("下载模板错误", e);
            throw new IllegalStateException(e.getMessage(), e);
        }
        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("物料库存导入模板.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(data);
    }

    /**
     * 导入库存-用于初始数据
     */
    @PostMapping("/import-material-inventory")
    public Object importMaterialInventory(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(fileName);
        if (!"xls".equalsIgnoreCase(extension) && !"xlsx".equalsIgnoreCase(extension)) {
            throw new IllegalStateException("只允许上传 xls 和 xlsx 格式的文件");
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        ImportMaterialInventoryListener listener = new ImportMaterialInventoryListener(snowflakeService, user,
                groupService, materialService, materialInventoryService);

        try (ExcelReader excelReader = EasyExcelFactory.read(file.getInputStream()).build()) {

            ReadSheet readSheet = EasyExcelFactory.readSheet(0).head(MaterialInventoryImportHeadVo.class)
                    .registerReadListener(listener).autoTrim(true).build();

            excelReader.read(readSheet);

            // 检查 失败 返回 对应行相关错误信息
            List<ImportErrorResponseVo> importErrorResponseVoList = listener.getImportErrorResponseVoList();
            if (CollectionUtils.isNotEmpty(importErrorResponseVoList)) {
                throw new LimsCodeException(1002, "数据检查失败").setData(Map.of("errorList", importErrorResponseVoList));
            }

            final List<MaterialInventoryDto> addTargetList = listener.getAddTargetList();
            if (CollectionUtils.isNotEmpty(addTargetList)) {
                materialInventoryService.addMaterialInventoryDtos(addTargetList);
            }

        } catch (IOException e) {
            log.error("物料库存出错", e);
            throw new IllegalStateException(e.getMessage(), e);
        }

        return Collections.emptyMap();
    }

    /**
     * 更新物料上下限
     */
    @PostMapping("/update-limit")
    public Object updateInventoryLimit(@RequestBody List<UpdateInventoryLimitDto> dtos) {

        // 过滤重复的物料code
        Set<String> filterCodes = new HashSet<>();
        for (UpdateInventoryLimitDto dto : dtos) {
            final String materialCode = dto.getMaterialCode();
            if (StringUtils.isBlank(materialCode)) {
                throw new IllegalArgumentException("物料编码不能为空");
            }

            if (!filterCodes.add(materialCode)) {
                throw new IllegalArgumentException(String.format("物料编码不能重复:[%s]", materialCode));
            }

            // 物料上下限空判断
            final boolean isNonNullUpperLimitBool = Objects.nonNull(dto.getInventoryUpperLimit());
            final boolean isNonNullLowerLimitBool = Objects.nonNull(dto.getInventoryLowerLimit());
            // 正则判断
            if (isNonNullUpperLimitBool && !String.valueOf(dto.getInventoryUpperLimit()).matches(UpdateInventoryLimitDto.VALID_NUMBER)){
                throw new IllegalArgumentException(String.format("物料编码：%s, 上限, 小数点前最多10位, 小数点后最多4位", materialCode));
            }
            if (isNonNullLowerLimitBool && !String.valueOf(dto.getInventoryLowerLimit()).matches(UpdateInventoryLimitDto.VALID_NUMBER)){
                throw new IllegalArgumentException(String.format("物料编码：%s, 下限, 小数点前最多10位, 小数点后最多4位", materialCode));
            }

            // 都不为空， 并且下限比上限高， 则报错
            if (isNonNullUpperLimitBool && isNonNullLowerLimitBool
                    && Objects.equals(NumberUtils.INTEGER_ONE, dto.getInventoryLowerLimit().compareTo(dto.getInventoryUpperLimit()))) {
                throw new IllegalArgumentException(String.format("物料【%s】下限不能比物料上限高", materialCode));
            }
        }

        materialInventoryService.updateInventoryUpperLimitAndLowerLimit(dtos);
        return Map.of();
    }

}
