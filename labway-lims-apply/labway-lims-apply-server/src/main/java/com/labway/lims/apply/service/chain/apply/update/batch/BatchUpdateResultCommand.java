package com.labway.lims.apply.service.chain.apply.update.batch;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/2/21 16:08
 */
@Slf4j
@Component
public class BatchUpdateResultCommand implements Command {

    @Resource
    private SampleAbnormalService sampleAbnormalService;

    @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;

    @Resource
    private SampleReportService sampleReportService;

    @Override
    public boolean execute(Context c) throws Exception {
        BatchUpdateApplyContext from = BatchUpdateApplyContext.from(c);

        LoginUserHandler.User user = from.getUser();

        HspOrganizationDto hspOrganization = from.getHspOrganization();
        Long hspOrgId = hspOrganization.getHspOrgId();
        String hspOrgName = hspOrganization.getHspOrgName();
        Long userId = user.getUserId();
        String nickname = user.getNickname();
        Date date = new Date();

        List<Long> applyIds = from.getApplys().stream().map(ApplyDto::getApplyId).collect(Collectors.toList());

        // 设置对应的更新条件
        SampleAbnormalDto sampleAbnormalDto = new SampleAbnormalDto();
        sampleAbnormalDto.setHspOrgName(hspOrgName);
        sampleAbnormalDto.setHspOrgId(hspOrgId);
        sampleAbnormalDto.setUpdaterId(userId);
        sampleAbnormalDto.setUpdateDate(date);
        sampleAbnormalDto.setUpdaterName(nickname);
        sampleAbnormalService.updateByApplyIds(sampleAbnormalDto, applyIds);

        SampleCriticalResultDto sampleCriticalResultDto = new SampleCriticalResultDto();
        sampleCriticalResultDto.setHspOrgId(hspOrgId.toString());
        sampleCriticalResultDto.setHspOrgName(hspOrgName);
        sampleCriticalResultDto.setUpdaterId(userId);
        sampleCriticalResultDto.setUpdateDate(date);
        sampleCriticalResultDto.setUpdaterName(nickname);
        sampleCriticalResultService.updateByApplyIds(sampleCriticalResultDto, applyIds);

        SampleReportDto sampleReportDto = new SampleReportDto();
        sampleReportDto.setHspOrgId(hspOrgId);
        sampleReportDto.setHspOrgName(hspOrgName);
        sampleReportDto.setUpdateDate(date);
        sampleReportDto.setUpdaterId(userId);
        sampleReportDto.setUpdaterName(nickname);
        sampleReportService.updateByApplyIds(sampleReportDto, applyIds);

        return CONTINUE_PROCESSING;
    }
}
