package com.labway.lims.apply.controller;

import cn.hutool.core.lang.Assert;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.RecoveryResultDto;
import com.labway.lims.apply.api.dto.SelectDeleteSampleResultDto;
import com.labway.lims.apply.api.service.DeleteResultService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashSet;

@RestController
@RequestMapping("/have-delete-sample-result")
public class DeleteSampleResultMainController extends BaseController {

    @Resource
    private DeleteResultService deleteResultService;

    /**
     * 查询已删除的样本数据
     */
    @PostMapping("/select-deleted-sample")
    public Object selectDeletedSample(@RequestBody SelectDeleteSampleResultDto dto) {
        dto.verify();
        return deleteResultService.selectDeletedSample(dto);
    }


    /**
     * 查询已删除的样本结果数据
     */
    @GetMapping("/select-deleted-result")
    public Object selectDeletedResult(@RequestParam("deleteSampleResultMainId") Long deleteSampleResultMainId) {
        return deleteResultService.selectDeletedResult(deleteSampleResultMainId);
    }

    /**
     * 恢复结果
     */
    @PostMapping("/recovery-result")
    public Object recoveryResult(@RequestBody RecoveryResultDto dto) {
        Assert.notNull(dto.getDeleteSampleResultMainId(), "所选样本不能为空");
        if (CollectionUtils.isEmpty(dto.getReportItemCodes())) {
            throw new IllegalArgumentException("报告项目不能为空");
        }
        return deleteResultService.recoveryResult(dto.getDeleteSampleResultMainId(), new HashSet<>(dto.getReportItemCodes()));
    }
}
