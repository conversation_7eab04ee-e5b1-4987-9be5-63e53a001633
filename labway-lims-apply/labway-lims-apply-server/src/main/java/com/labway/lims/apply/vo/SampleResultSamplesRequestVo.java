package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nullable;

@Getter
@Setter
public class SampleResultSamplesRequestVo {

    /**
     * 仪器编码
     */
    @Nullable
    private String machineCode;

    /**
     * 专业组编码
     */
    @Nullable
    private String groupCode;

    /**
     * 检验类型
     */
    private ItemTypeEnum itemType;


}
