package com.labway.lims.apply.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.PrintStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.PhysicalBatchDto;
import com.labway.lims.apply.api.dto.PhysicalRegisterDto;
import com.labway.lims.apply.vo.utils.ExcelFileHeadMappingVo;
import com.labway.lims.apply.vo.ImportErrorResponseVo;
import com.labway.lims.apply.vo.ImportPhysicalRegisterVo;
import com.labway.lims.base.api.dto.DictItemDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.labway.lims.api.web.BaseController.INPUT_MAX_LENGTH;
import static com.labway.lims.api.web.BaseController.TEXTAREA_MAX_LENGTH;
import static java.util.stream.Collectors.toMap;

/**
 * 导入 体检花名册 人员
 *
 * <AUTHOR>
 * @since 2023/3/30 17:55
 */
@Slf4j
public class ImportPhysicalRegisterListener extends AnalysisEventListener<Map<Integer, String>> {

    private final PhysicalBatchDto physicalBatchDto;
    private final SnowflakeService snowflakeService;

    private final LoginUserHandler.User loginUser;

    private final List<DictItemDto> dictItemDtos;

    private final List<ExcelFileHeadMappingVo> headSetting;

    public ImportPhysicalRegisterListener(PhysicalBatchDto physicalBatchDto, SnowflakeService snowflakeService,
                                          LoginUserHandler.User loginUser, List<DictItemDto> dictItemDtos,
                                          List<ExcelFileHeadMappingVo> headSetting) {
        this.physicalBatchDto = physicalBatchDto;
        this.snowflakeService = snowflakeService;
        this.loginUser = loginUser;
        this.dictItemDtos = dictItemDtos;
        this.headSetting = headSetting;
    }

    private final Map<Integer, ImportPhysicalRegisterVo> excelDataMap = new HashMap<>();

    private final List<ImportErrorResponseVo> importErrorResponseVoList = new ArrayList<>();
    private final List<PhysicalRegisterDto> targetList = new ArrayList<>();
    private final Map<String, DictItemDto> applyTypeNameMap = new HashMap<>();

    public List<ImportErrorResponseVo> getImportErrorResponseVoList() {
        return importErrorResponseVoList;
    }

    public List<PhysicalRegisterDto> getTargetList() {
        return targetList;
    }

    private final Map<String, Integer> reverseHeadMap = new HashMap<>();

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 校验表头
        List<String> importHeadList = ImportPhysicalRegisterVo.getHeadList();

        Map<String, ExcelFileHeadMappingVo> settingByTemplateHead =
                headSetting.stream().collect(toMap(ExcelFileHeadMappingVo::getTemplateHead,
                        Function.identity(), (key1, key2) -> key1));

        importHeadList.forEach(item -> {
            ExcelFileHeadMappingVo setting = settingByTemplateHead.get(item);
            if (Objects.nonNull(setting)) {
                reverseHeadMap.put(item, setting.getExcelIndex());
            }
        });

        applyTypeNameMap.putAll(dictItemDtos.stream()
                .collect(Collectors.toMap(DictItemDto::getDictName, Function.identity(), (key1, key2) -> key1)));

    }

    @Override
    public void invoke(Map<Integer, String> dataMap, AnalysisContext analysisContext) {
        ImportPhysicalRegisterVo data = new ImportPhysicalRegisterVo(dataMap, reverseHeadMap);

        ReadRowHolder readRowHolder = analysisContext.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex() + 1;

        // 检查数据 格式
        String errorMessage = validateData(data);

        if (StringUtils.isNotBlank(errorMessage)) {
            importErrorResponseVoList
                    .add(ImportErrorResponseVo.builder().rowNo(rowIndex).errorInfo(errorMessage).build());
        }

        excelDataMap.put(rowIndex, data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (CollectionUtils.isEmpty(excelDataMap.values())) {
            // 无 导入数据
            return;
        }

        // 检查 标本序号重复性
        checkRepeatSampleSort();

        if (CollectionUtils.isNotEmpty(importErrorResponseVoList)) {
            // 存在错误数据 相关 检查 失败
            return;
        }

        Set<String> patientSubAgeUnitNameList = Arrays.stream(PatientSubAgeUnitEnum.values())
                .map(PatientSubAgeUnitEnum::getValue).collect(Collectors.toSet());
        Date date = new Date();
        LinkedList<Long> genIds = snowflakeService.genIds(excelDataMap.size());
        for (Map.Entry<Integer, ImportPhysicalRegisterVo> entry : excelDataMap.entrySet()) {
            ImportPhysicalRegisterVo value = entry.getValue();

            PhysicalRegisterDto target = new PhysicalRegisterDto();
            target.setPhysicalRegisterId(genIds.pop());
            target.setPatientName(value.getPatientName());

            // 设置年龄
            setPatientAge(patientSubAgeUnitNameList, value, target);

            target.setPatientBirthday(date);
            target.setPatientCard(StringUtils.defaultString(value.getPatientCard()));
            target.setPatientCardType(PatientCardTypeEnum.ID_CARD.name());
            target.setPatientSex(Objects.equals(value.getPatientSex(), SexEnum.MAN.getDesc()) ? SexEnum.MAN.getCode()
                    : SexEnum.WOMEN.getCode());
            target.setPatientMobile(StringUtils.defaultString(value.getPatientMobile()));
            target.setPatientAddress(StringUtils.defaultString(value.getPatientAddress()));
            target.setSampleSort(Integer.valueOf(value.getSampleSort()));
            target.setTestPackage(value.getTestPackage());
            target.setRemark(StringUtils.defaultString(value.getRemark()));
            target.setDept(StringUtils.defaultString(value.getDept()));
            target.setPatientBed(StringUtils.defaultString(value.getPatientBed()));
            target.setPhysicalCompanyId(physicalBatchDto.getPhysicalCompanyId());
            target.setPhysicalCompanyName(physicalBatchDto.getPhysicalCompanyName());
            target.setPhysicalBatchId(physicalBatchDto.getPhysicalBatchId());
            target.setOrgId(loginUser.getOrgId());
            target.setOrgName(loginUser.getOrgName());
            target.setCreateDate(date);
            target.setUpdateDate(date);
            target.setCreatorName(loginUser.getNickname());
            target.setCreatorId(loginUser.getUserId());
            target.setUpdaterName(loginUser.getNickname());
            target.setUpdaterId(loginUser.getUserId());
            target.setIsDelete(YesOrNoEnum.NO.getCode());
            target.setApplicant(StringUtils.defaultString(value.getApplicant()));
            target.setIsPrint(PrintStatusEnum.UNPRINTED.getCode());
            target.setApplyTypeCode(StringUtils.EMPTY);
            target.setApplyTypeName(StringUtils.EMPTY);
            if (StringUtils.isNotBlank(value.getApplyType())) {
                DictItemDto dictItemDto = applyTypeNameMap.get(value.getApplyType());
                target.setApplyTypeCode(dictItemDto.getDictCode());
                target.setApplyTypeName(dictItemDto.getDictName());
            }
            target.setPatientVisitCard(StringUtils.defaultString(value.getPatientVisitCard()));
            target.setTestPackageDesc(StringUtils.defaultString(value.getTestPackageDesc()));
            target.setDiagnosis(StringUtils.defaultString(value.getClinicalDiagnosis()));
            targetList.add(target);
        }
    }

    private final Set<String> validGenders =
            new HashSet<>(Arrays.asList(SexEnum.MAN.getDesc(), SexEnum.WOMEN.getDesc()));

    /**
     * 检查 导入数据
     */
    private String validateData(ImportPhysicalRegisterVo data) {
        StringBuilder errorMessage = new StringBuilder();

        if (StringUtils.isBlank(data.getPatientName())) {
            errorMessage.append("姓名不能为空;");
        } else if (StringUtils.length(data.getPatientName()) > TEXTAREA_MAX_LENGTH) {
            errorMessage.append(String.format("姓名不能超过 %s 字符;", TEXTAREA_MAX_LENGTH));
        }

        // 验证性别参数是否合法
        if (StringUtils.isBlank(data.getPatientSex())) {
            errorMessage.append("性别不能为空;");
        } else if (!validGenders.contains(data.getPatientSex())) {
            errorMessage
                    .append(String.format("性别参数不规范,请输入 [%s ]或 [%s] ", SexEnum.MAN.getDesc(), SexEnum.WOMEN.getDesc()));
        }

        // 如果纯数字就是岁
        data.setPatientAge(StringUtils.trim(data.getPatientAge()));
        if (NumberUtils.isParsable(data.getPatientAge())) {
            data.setPatientAge(data.getPatientAge() + "岁");
        }

        if (Objects.isNull(data.getPatientAge())) {
            errorMessage.append("年龄不能为空;");
        } else if (!isValidAgeFormat(data.getPatientAge())) {
            errorMessage.append("年龄格式不规范");
        }

        if (StringUtils.isNotBlank(data.getApplyType()) && !applyTypeNameMap.containsKey(data.getApplyType())) {
            errorMessage.append("无效就诊类型; 就诊类型需是: ").append(String.join("、", applyTypeNameMap.keySet()));
        }

        if (StringUtils.isNotBlank(data.getPatientVisitCard())
                && StringUtils.length(data.getPatientVisitCard()) > INPUT_MAX_LENGTH) {
            errorMessage.append(String.format("门诊/住院号不能超过 %s 字符;", INPUT_MAX_LENGTH));
        }

        if (StringUtils.isNotBlank(data.getPatientCard())
                && StringUtils.length(data.getPatientCard()) > INPUT_MAX_LENGTH) {
            errorMessage.append(String.format("身份证号不能超过 %s 字符;", INPUT_MAX_LENGTH));
        }

        if (Objects.isNull(data.getSampleSort())) {
            errorMessage.append("标本序号不能为空;");
        } else if (!data.getSampleSort().matches("\\d+")) {
            errorMessage.append("标本序号需要为数字;");
        }

        if (StringUtils.isBlank(data.getTestPackage())) {
            errorMessage.append("套餐名称不能为空;");
        } else if (StringUtils.length(data.getTestPackage()) > TEXTAREA_MAX_LENGTH) {
            errorMessage.append(String.format("套餐名称不能超过 %s 字符;", TEXTAREA_MAX_LENGTH));
        }

        if (StringUtils.isBlank(data.getTestPackageDesc())) {
            errorMessage.append("套餐说明不能为空;");
        } else if (StringUtils.length(data.getTestPackageDesc()) > TEXTAREA_MAX_LENGTH) {
            errorMessage.append(String.format("套餐说明不能超过 %s 字符;", TEXTAREA_MAX_LENGTH));
        }

        if (StringUtils.isNotBlank(data.getDept()) && StringUtils.length(data.getDept()) > INPUT_MAX_LENGTH) {
            errorMessage.append(String.format("班级不能超过 %s 字符;", INPUT_MAX_LENGTH));
        }

        if (StringUtils.isNotBlank(data.getRemark()) && StringUtils.length(data.getRemark()) > TEXTAREA_MAX_LENGTH) {
            errorMessage.append(String.format("备注不能超过 %s 字符;", TEXTAREA_MAX_LENGTH));
        }
        if (StringUtils.isNotBlank(data.getPatientMobile())
                && StringUtils.length(data.getPatientMobile()) > INPUT_MAX_LENGTH) {
            errorMessage.append(String.format("手机号不能超过 %s 字符;", INPUT_MAX_LENGTH));
        }
        if (StringUtils.isNotBlank(data.getApplicant()) && StringUtils.length(data.getApplicant()) > INPUT_MAX_LENGTH) {
            errorMessage.append(String.format("申请人不能超过 %s 字符;", INPUT_MAX_LENGTH));
        }
        if (StringUtils.isNotBlank(data.getClinicalDiagnosis())
                && StringUtils.length(data.getClinicalDiagnosis()) > TEXTAREA_MAX_LENGTH) {
            errorMessage.append(String.format("临床诊断不能超过 %s 字符;", TEXTAREA_MAX_LENGTH));
        }
        if (StringUtils.isNotBlank(data.getPatientBed())
                && StringUtils.length(data.getPatientBed()) > INPUT_MAX_LENGTH) {
            errorMessage.append(String.format("床号不能超过 %s 字符;", INPUT_MAX_LENGTH));
        }
        if (StringUtils.isNotBlank(data.getPatientAddress())
                && StringUtils.length(data.getPatientAddress()) > TEXTAREA_MAX_LENGTH) {
            errorMessage.append(String.format("地址不能超过 %s 字符;", TEXTAREA_MAX_LENGTH));
        }
        return errorMessage.toString();
    }

    /**
     * 检查 标本序号重复性
     */
    private void checkRepeatSampleSort() {
        // 目前错误信息 key：行号 value: 错误信息
        Map<Integer, String> errorInfoByRowNo = importErrorResponseVoList.stream()
                .collect(Collectors.toMap(ImportErrorResponseVo::getRowNo, ImportErrorResponseVo::getErrorInfo));

        // 以 标本序号分组 key：标本序号，value 对应行号
        Map<String, List<Integer>> groupingBySampleSort =
                excelDataMap.entrySet().stream().filter(obj -> Objects.nonNull(obj.getValue().getSampleSort()))
                        .collect(Collectors.groupingBy(obj -> obj.getValue().getSampleSort(),
                                Collectors.mapping(Map.Entry::getKey, Collectors.toList())));

        groupingBySampleSort.entrySet().stream().filter(obj -> obj.getValue().size() > 1).forEach(item -> {
            // 相同 标本序号的 行号
            List<Integer> rowIndexTempList = item.getValue();
            rowIndexTempList.forEach(row -> {
                String rowIndexTempStr = rowIndexTempList.stream().filter(x -> !Objects.equals(x, row))
                        .map(Object::toString).collect(Collectors.joining("、"));
                String rowIndexTempErrorStr = "与" + rowIndexTempStr + "行中标本序号重复;";
                String msg = errorInfoByRowNo.get(row);
                if (Objects.isNull(msg)) {
                    importErrorResponseVoList
                            .add(ImportErrorResponseVo.builder().rowNo(row).errorInfo(rowIndexTempErrorStr).build());
                } else {
                    importErrorResponseVoList.stream().filter(obj -> Objects.equals(obj.getRowNo(), row))
                            .forEach(x -> x.setErrorInfo(msg + rowIndexTempErrorStr));
                }
            });
        });
    }

    private static final String ageRegex = "^(\\d+)([岁月周天])$";

    private boolean isValidAgeFormat(String str) {
        return Pattern.matches(ageRegex, str);
    }

    private void setPatientAge(Set<String> patientSubAgeUnitNameList, ImportPhysicalRegisterVo value,
                               PhysicalRegisterDto target) {
        Pattern pattern = Pattern.compile(ageRegex);
        Matcher matcher = pattern.matcher(value.getPatientAge());
        Integer patientAge = NumberUtils.INTEGER_ZERO;
        Integer patientSubage = NumberUtils.INTEGER_ZERO;
        String patientSubageUnit = StringUtils.EMPTY;
        if (matcher.matches()) {
            String age = matcher.group(NumberUtils.INTEGER_ONE);
            String unit = matcher.group(NumberUtils.INTEGER_TWO);
            if (patientSubAgeUnitNameList.contains(unit)) {
                patientSubage = Integer.valueOf(age);
                patientSubageUnit = unit;
            } else {
                patientAge = Integer.valueOf(age);
            }
        }

        target.setPatientAge(patientAge);
        target.setPatientSubage(patientSubage);
        target.setPatientSubageUnit(patientSubageUnit);
    }
}
