package com.labway.lims.apply.service.chain.pick.two.cancel;

import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 删除多余的申请单样本
 */
@Slf4j
@Component
public class CancelTwoPickRemoveApplySampleCommand implements Command, InitializingBean, Filter {
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;

    @Override
    public boolean execute(Context c) throws Exception {

        final CancelTwoPickContext context = CancelTwoPickContext.from(c);

        final List<Long> applySampleIds = context.getApplySamples().stream().map(ApplySampleDto::getApplySampleId)
                .distinct()
                .filter(e -> !Objects.equals(e, context.getApplySampleId()))
                .collect(Collectors.toCollection(ArrayList::new));

        if (applySampleIds.isEmpty()) {
            return CONTINUE_PROCESSING;
        }

        // 删掉多处的申请单样本
        applySampleService.deleteByApplySampleIds(applySampleIds);

        // 删除项目
        applySampleItemService.deleteByApplySampleIds(applySampleIds);

        return CONTINUE_PROCESSING;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
