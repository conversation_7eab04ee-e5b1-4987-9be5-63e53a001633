package com.labway.lims.apply.service.chain.sample.archive.add;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.RackArchiveDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import lombok.Data;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class CheckSampleResultCommand implements Command {

    @DubboReference
    private SystemParamService systemParamService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SampleArchiveAddContext from = SampleArchiveAddContext.from(context);
        // 归档试管架
        final RackArchiveDto rackArchive = from.getRackArchive();
        final List<ApplySampleDto> applySamples = from.getApplySamples();


        final Set<String> exceptionReportCodes = getExceptionReportCodes();
        if (CollectionUtils.isEmpty(exceptionReportCodes)) {
            // 没有配置系统参数直接跳过
            return CONTINUE_PROCESSING;
        }

        // 试管架空间
        final List<RackLogicSpaceDto> rackLogicSpaceDtos = rackLogicSpaceService.selectByRackLogicId(rackArchive.getRackLogicId());
        if (CollectionUtils.isEmpty(rackLogicSpaceDtos)) {
            // 试管架空间没有数据， 说明是第一条， 直接跳过
            return CONTINUE_PROCESSING;
        }

        // 当前归档样本 是否包含 系统参数配置的 异常（危急）项目  && 是异常或者危急
        final List<ReportItem> archiveReportItems = this.convertReportItems(elasticSearchSampleService.selectSamples(SampleEsQuery.builder()
                .applySampleIds(applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()))
                .build()))
                .stream()
                // 过滤配置的异常项目
                .filter(e-> exceptionReportCodes.contains(e.getReportItemCode()) && TestJudgeEnum.isExceptionOrCrisis(e.getJudge()))
                .collect(Collectors.toList());
        // 1. 当前样本是否包含异常项目
        final boolean archiveContainsException = CollectionUtils.isNotEmpty(archiveReportItems);



        // 查询归档架上的样本  是否包含 系统参数配置的 异常（危急）项目
        List<ReportItem> rackReportItems = this.convertReportItems(elasticSearchSampleService.selectSamples(SampleEsQuery.builder()
                .applySampleIds(rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getApplySampleId).collect(Collectors.toSet()))
                .build()));
        // 2. 校验 并且 过滤出来的异常并且危急的项目
        rackReportItems = checkFilterRackReportItem(exceptionReportCodes, rackReportItems);
        // 当前试管架是否包含异常项目
        final boolean rackContainsException = CollectionUtils.isNotEmpty(rackReportItems);



        // 3. 当前归档样本是异常的 && 试管架上的样本正常
        if (archiveContainsException && !rackContainsException) {
            final String exceptionReportNames = archiveReportItems.stream().map(ReportItem::getReportItemName).distinct().collect(Collectors.joining(","));
            throw new IllegalStateException(String.format("当前样本中存在 %s 结果值异常或危急，请更换试管架后再归档", exceptionReportNames));
        }



        // 4. 当前归档样本是正常的 && 试管架上的样本包含异常
        if (!archiveContainsException && rackContainsException) {
            final String exceptionReportNames = rackReportItems.stream().map(ReportItem::getReportItemName).distinct().collect(Collectors.joining(","));
            throw new IllegalStateException(String.format("当前已选试管架存在 %s 结果值异常或危急，请更换试管架后再归档", exceptionReportNames));
        }

        // 当前样本项目状态 == 试管架上已存在的项目状态
        return CONTINUE_PROCESSING;
    }


    /**
     * 获取配置的异常（危急）项目编码
     *
     * @return 报告项目编码
     */
    private Set<String> getExceptionReportCodes() {
        final SystemParamDto systemParamDto = systemParamService.selectByParamName(SystemParamNameEnum.RACK_EXCEPTION_REPORT.name(), LoginUserHandler.get().getOrgId());
        if (systemParamDto == null) {
            return Collections.emptySet();
        }
        return Arrays.stream(systemParamDto.getParamValue().split(",")).collect(Collectors.toSet());
    }

    /**
     * 提取es样本中的项目
     */
    private List<ReportItem> convertReportItems(List<BaseSampleEsModelDto> sampleEsModelDtos) {
        return sampleEsModelDtos.stream()
                .flatMap(baseSampleEsModelDto -> {
                    if (baseSampleEsModelDto instanceof RoutineInspectionDto) {
                        return ((RoutineInspectionDto) baseSampleEsModelDto).getReportItems().stream().map(e -> convertReportItem(baseSampleEsModelDto, e));
                    } else if (baseSampleEsModelDto instanceof OutsourcingInspectionDto) {
                        return ((OutsourcingInspectionDto) baseSampleEsModelDto).getReportItems().stream().map(e -> convertReportItem(baseSampleEsModelDto, e));
                    }
                    return Stream.empty();
                })
                .collect(Collectors.toList());
    }

    /**
     * 校验试管架上的项目
     * @param exceptionReportCodes 系统参数配置的异常项目
     * @param reportItems 试管架上的所有项目
     */
    private List<ReportItem> checkFilterRackReportItem(Set<String> exceptionReportCodes, List<ReportItem> reportItems) {
        // true 包含配置的项目   false 不包含配置的项目
        final Map<Boolean, List<ReportItem>> reportItemBySystemParamsMap = reportItems
                .stream()
                // 项目是配置的异常项目 and 是异常的一组    其他的（不是配置的异常项目 or （是配置的异常项目 and 不是异常值））
                .collect(Collectors.groupingBy(e -> exceptionReportCodes.contains(e.getReportItemCode()) && TestJudgeEnum.isExceptionOrCrisis(e.getJudge())));

        // 异常项目列表
        final List<ReportItem> exceptionReportItemList = reportItemBySystemParamsMap.getOrDefault(Boolean.TRUE, Collections.emptyList());
        if (CollectionUtils.isEmpty(exceptionReportItemList)) {
            // 没有异常样本， 认为是正常试管架， 直接返回空列表
            return exceptionReportItemList;
        }


        // --------------------------------------------------------------------------------------
        // ------------------------  以下判断 是否既有正常样本  又有  异常样本  ------------------------
        // --------------------------------------------------------------------------------------
        final List<ReportItem> normalReportItemList = reportItemBySystemParamsMap.getOrDefault(Boolean.FALSE, Collections.emptyList());
        if (CollectionUtils.isEmpty(normalReportItemList)) {
            // 没有正常样本， 全是异常样本
            return exceptionReportItemList;
        }

        // 异常样本id列表
        final Set<Long> exceptionApplySampleIdSet = exceptionReportItemList
                .stream().map(ReportItem::getApplySampleId)
                .collect(Collectors.toSet());

        // 正常样本id列表里面删除异常样本列表重复的
        normalReportItemList.removeIf(e-> exceptionApplySampleIdSet.contains(e.getApplySampleId()));
        if (!normalReportItemList.isEmpty()) {
            // 不等于空的话，代表当前试管架既有异常样本又有正常样本， 直接报错
            final String exceptionReportNames = exceptionReportItemList.stream().map(ReportItem::getReportItemName).distinct().collect(Collectors.joining(","));
            throw new IllegalStateException(String.format("当前已选试管架存在 %s 结果值异常或危急，请更换试管架后再归档", exceptionReportNames));
        }

        // 代表试管架上的只有异常样本， 返回true
        return exceptionReportItemList;
    }


    private ReportItem convertReportItem(BaseSampleEsModelDto baseSampleEsModelDto, RoutineInspectionDto.RoutineReportItem routineReportItem) {
        final ReportItem reportItem = new ReportItem();
        reportItem.applySampleId = baseSampleEsModelDto.getApplySampleId();
        reportItem.reportItemCode = routineReportItem.getReportItemCode();
        reportItem.reportItemName = routineReportItem.getReportItemName();
        reportItem.result = StringUtils.defaultString(routineReportItem.getResult());
        reportItem.status = ObjectUtils.defaultIfNull(routineReportItem.getStatus(), ResultStatusEnum.NORMAL.getCode());
        reportItem.judge = StringUtils.defaultString(routineReportItem.getJudge(), TestJudgeEnum.DEFAULT.getValue());
        return reportItem;
    }

    private ReportItem convertReportItem(BaseSampleEsModelDto baseSampleEsModelDto, OutsourcingInspectionDto.OutsourcingReportItem outsourcingReportItem) {
        final ReportItem reportItem = new ReportItem();
        reportItem.applySampleId = baseSampleEsModelDto.getApplySampleId();
        reportItem.reportItemCode = outsourcingReportItem.getReportItemCode();
        reportItem.reportItemName = outsourcingReportItem.getReportItemName();
        reportItem.result = StringUtils.defaultString(outsourcingReportItem.getResult());
        reportItem.status = ObjectUtils.defaultIfNull(outsourcingReportItem.getStatus(), ResultStatusEnum.NORMAL.getCode());
        reportItem.judge = StringUtils.defaultString(outsourcingReportItem.getJudge(), TestJudgeEnum.DEFAULT.getValue());
        return reportItem;
    }

    @Data
    private static final class ReportItem {
        private Long applySampleId;
        private String reportItemCode;
        private String reportItemName;
        private String result;
        /**
         * 状态 1:危机 2:异常 0:正常
         *
         * @see ResultStatusEnum
         */
        private Integer status;
        /**
         * 检验判定 UP  DOWN  NORMAL
         *
         * @see TestJudgeEnum
         */
        private String judge;
    }

}
