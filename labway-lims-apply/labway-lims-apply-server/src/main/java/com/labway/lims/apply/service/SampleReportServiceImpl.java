package com.labway.lims.apply.service;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.dto.UploadPdfDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.apply.mapper.TbSampleReportMapper;
import com.labway.lims.apply.mapstruct.SampleReportConverter;
import com.labway.lims.apply.model.TbSampleReport;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 样本报告Service impl
 *
 * <AUTHOR>
 * @since 2023/4/11 13:37
 */
@Slf4j
@DubboService
public class SampleReportServiceImpl implements SampleReportService {
    @Resource
    private TbSampleReportMapper tbSampleReportMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private SampleReportConverter sampleReportConverter;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private SpecialtySampleService specialtySampleService;

    @DubboReference
    private GeneticsSampleService geneticsSampleService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean deleteBySampleReportId(long sampleReportId) {
        return tbSampleReportMapper.deleteById(sampleReportId) > 0;
    }

    @Override
    public void deleteBySampleReportIds(Collection<Long> sampleReportIds) {
        if (CollectionUtils.isEmpty(sampleReportIds)) {
            return;
        }
        tbSampleReportMapper.deleteBatchIds(sampleReportIds);
    }

    @Override
    public void deleteBySampleIds(Collection<Long> sampleIds) {

        if (CollectionUtils.isEmpty(sampleIds)) {
            return;
        }

        tbSampleReportMapper.delete(new LambdaQueryWrapper<TbSampleReport>().in(TbSampleReport::getSampleId, sampleIds)
                .eq(TbSampleReport::getOrgId, LoginUserHandler.get().getOrgId()));
    }

    @Override
    public void deleteBySampleIdsAndNotUpload(Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return;
        }

        tbSampleReportMapper.delete(new LambdaQueryWrapper<TbSampleReport>()
                .in(TbSampleReport::getSampleId, sampleIds)
                .eq(TbSampleReport::getIsUploadPdf, YesOrNoEnum.NO.getCode())
                .eq(TbSampleReport::getOrgId, LoginUserHandler.get().getOrgId()));
    }

    @Override
    public long addSampleReport(SampleReportDto dto) {
        final TbSampleReport target = new TbSampleReport();

        BeanUtils.copyProperties(dto, target);
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        target.setSampleReportId(Optional.ofNullable(dto.getSampleReportId()).orElseGet(() -> snowflakeService.genId()));
        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());
        target.setIsUploadPdf(Objects.isNull(target.getIsUploadPdf()) ? YesOrNoEnum.NO.getCode() : target.getIsUploadPdf());

        if (tbSampleReportMapper.insert(target) < 1) {
            throw new IllegalStateException("添加样本报告失败");
        }

        log.info("用户 [{}] 新增样本报告 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(target));

        return target.getSampleReportId();
    }

    @Override
    public void addSampleReportBatch(Collection<SampleReportDto> dtos) {

        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }

        final LoginUserHandler.User user = LoginUserHandler.get();
        final List<TbSampleReport> reports = JSON.parseArray(JSON.toJSONString(dtos), TbSampleReport.class);
        final LinkedList<Long> ids = snowflakeService.genIds(dtos.size());
        for (TbSampleReport report : reports) {
            report.setSampleReportId(ids.pop());
            report.setOrgId(user.getOrgId());
            report.setOrgName(user.getOrgName());
            report.setCreateDate(new Date());
            report.setUpdateDate(new Date());
            report.setCreatorId(user.getUserId());
            report.setCreatorName(user.getNickname());
            report.setUpdaterId(user.getUserId());
            report.setUpdaterName(user.getNickname());
            report.setIsDelete(YesOrNoEnum.NO.getCode());
        }

        tbSampleReportMapper.addBatch(reports);
    }

    @Override
    @Transactional
    public void addSampleReportBatch(Collection<SampleReportDto> dtos, Map<String, Object> extra) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }

        final List<TbSampleReport> reports = JSON.parseArray(JSON.toJSONString(dtos), TbSampleReport.class);
        final LinkedList<Long> ids = snowflakeService.genIds(dtos.size());
        for (TbSampleReport report : reports) {
            report.setSampleReportId(ids.pop());
            report.setOrgId((Long)extra.get("orgId"));
            report.setOrgName((String)extra.get("orgName"));
            report.setCreateDate(new Date());
            report.setUpdateDate(new Date());
            report.setCreatorId(0L);
            report.setCreatorName((String)extra.get("operator"));
            report.setUpdaterId(0L);
            report.setUpdaterName((String)extra.get("operator"));
            report.setIsDelete(YesOrNoEnum.NO.getCode());
        }

        tbSampleReportMapper.addBatch(reports);
    }

    @Override
    public List<SampleReportDto> selectByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSampleReport> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSampleReport::getApplySampleId, applySampleIds);
        queryWrapper.eq(TbSampleReport::getIsDelete, YesOrNoEnum.NO.getCode());
        return sampleReportConverter.sampleReportDtoListFromTbObjList(tbSampleReportMapper.selectList(queryWrapper));
    }

    @Override
    public List<SampleReportDto> selectBySampleReportIds(Collection<Long> sampleReportIds) {
        return sampleReportConverter.sampleReportDtoListFromTbObjList(tbSampleReportMapper.selectBatchIds(sampleReportIds));
    }

    @Nullable
    @Override
    public SampleReportDto selectBySampleReportId(Long sampleReportId) {
        return sampleReportConverter.sampleReportDtoFromTbObj(tbSampleReportMapper.selectById(sampleReportId));
    }

    @Nullable
    @Override
    public SampleReportDto selectByApplySampleId(long applySampleId) {
        if (applySampleId < 1) {
            return null;
        }
        LambdaQueryWrapper<TbSampleReport> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSampleReport::getApplySampleId, applySampleId);
        queryWrapper.eq(TbSampleReport::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return sampleReportConverter.sampleReportDtoFromTbObj(tbSampleReportMapper.selectOne(queryWrapper));
    }


    @Override
    public void updateBySampleId(SampleReportDto sampleReport) {
        if (Objects.isNull(sampleReport)) {
            return;
        }

        final LambdaQueryWrapper<TbSampleReport> eq = Wrappers.lambdaQuery(TbSampleReport.class)
                .eq(TbSampleReport::getSampleId, sampleReport.getSampleId());

        tbSampleReportMapper.update(JSON.parseObject(JSON.toJSONString(sampleReport), TbSampleReport.class), eq);
    }

    @Override
    public void refreshReport(ApplySampleDto applySample) {
        if (Objects.isNull(applySample)) {
            return;
        }

        final Long applySampleId =
                applySample.getApplySampleId();
        if (Objects.isNull(applySampleId)) {
            return;
        }

        final String itemType = applySample.getItemType();
        if (StringUtils.isBlank(itemType)) {
            log.info("申请单样本 [{}] 项目类型为空", applySampleId);
        }

        ItemTypeEnum itemTypeEnum = null;
        try {
            itemTypeEnum = ItemTypeEnum.valueOf(itemType);
        } catch (Exception e) {
            log.warn("申请单样本 [{}] 项目类型 [{}] 不支持生成报告", applySampleId, itemType);
        }

        if (Objects.isNull(itemTypeEnum)) {
            log.info("申请单样本 [{}] 项目类型 [{}] 不存在", applySampleId, itemType);
            return;
        }

        SampleReportDto sampleReportDto = null;
        switch (itemTypeEnum) {
            case ROUTINE:
                sampleReportDto = sampleService.rebuildReport(applySampleId);
                break;

            case INFECTION:
                sampleReportDto = infectionSampleService.rebuildReport(applySampleId);
                break;

            case OUTSOURCING:
                sampleReportDto = outsourcingSampleService.rebuildReport(applySampleId);
                break;

            case MICROBIOLOGY:
                sampleReportDto = microbiologySampleService.rebuildReport(applySampleId);
                break;

            case SPECIALTY:
                String rebuildReport = specialtySampleService.rebuildReport(applySampleId);
                if (StringUtils.isNotBlank(rebuildReport)) {
                    sampleReportDto = JSON.parseObject(rebuildReport, SampleReportDto.class);
                }
                break;

            case GENETICS:
                sampleReportDto = geneticsSampleService.rebuildReport(applySampleId);
                break;

            default:
                log.info("申请单样本 [{}] 项目类型 [{}] 不支持生成报告", applySampleId, itemType);
        }
        if (Objects.nonNull(sampleReportDto)) {
            updateBySampleId(sampleReportDto);
        }
    }

    @Override
    public void updateByApplyId(SampleReportDto sampleReportDto) {
        LambdaUpdateWrapper<TbSampleReport> wrapper = Wrappers.lambdaUpdate(TbSampleReport.class)
                .eq(TbSampleReport::getApplyId, sampleReportDto.getApplyId())
                .eq(TbSampleReport::getIsDelete, 0)
                .set(TbSampleReport::getHspOrgId, sampleReportDto.getHspOrgId())
                .set(TbSampleReport::getHspOrgName, sampleReportDto.getHspOrgName())
                .set(TbSampleReport::getUpdaterId, sampleReportDto.getUpdaterId())
                .set(TbSampleReport::getUpdaterName, sampleReportDto.getUpdaterName())
                .set(TbSampleReport::getUpdateDate, sampleReportDto.getUpdateDate());
        tbSampleReportMapper.update(null, wrapper);
    }

    @Override
    public void updateByApplyIds(SampleReportDto sampleReportDto, Collection<Long> applyIds) {

        for (List<Long> item : Lists.partition(new ArrayList<>(applyIds), 500)) {
            LambdaUpdateWrapper<TbSampleReport> wrapper =
                Wrappers.lambdaUpdate(TbSampleReport.class).in(TbSampleReport::getApplyId, item)
                    .eq(TbSampleReport::getIsDelete, 0).set(TbSampleReport::getHspOrgId, sampleReportDto.getHspOrgId())
                    .set(TbSampleReport::getHspOrgName, sampleReportDto.getHspOrgName())
                    .set(TbSampleReport::getUpdaterId, sampleReportDto.getUpdaterId())
                    .set(TbSampleReport::getUpdaterName, sampleReportDto.getUpdaterName())
                    .set(TbSampleReport::getUpdateDate, sampleReportDto.getUpdateDate());
            tbSampleReportMapper.update(null, wrapper);
        }
    }

    @Override
    public SampleReportDto uploadReport(UploadPdfDto uploadPdfDto) {
        uploadPdfDto.verifyParams();
        ApplySampleDto applySample = applySampleService.selectByApplySampleId(uploadPdfDto.getApplySampleId());
        Assert.notNull(applySample, "样本不存在！");

        if (!Objects.equals(applySample.getIsOutsourcing(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalStateException("不是外送样本！");
        }
        if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
            throw new IllegalStateException("样本不是未审核状态！");
        }

        ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        Assert.notNull(apply, "申请单不存在！");

        OutsourcingSampleDto outsourcingSampleDto = outsourcingSampleService.selectByApplySampleId(applySample.getApplySampleId());
        Assert.notNull(outsourcingSampleDto, "外送样本不存在！");

        SampleReportDto oldSampleReport = selectByApplySampleId(applySample.getApplySampleId());
        if (Objects.nonNull(oldSampleReport)) {
            deleteBySampleReportId(oldSampleReport.getSampleReportId());
        }

        final SampleReportDto newSampleReportDto = new SampleReportDto();
        newSampleReportDto.setSampleReportId(snowflakeService.genId());
        newSampleReportDto.setApplySampleId(applySample.getApplySampleId());
        newSampleReportDto.setApplyId(apply.getApplyId());
        newSampleReportDto.setSampleId(outsourcingSampleDto.getOutsourcingSampleId());
        newSampleReportDto.setBarcode(outsourcingSampleDto.getBarcode());
        newSampleReportDto.setFileType(SampleReportFileTypeEnum.PDF.name());
        newSampleReportDto.setUrl(uploadPdfDto.getUrl());
        newSampleReportDto.setGroupName(applySample.getGroupName());
        newSampleReportDto.setGroupId(applySample.getGroupId());
        newSampleReportDto.setHspOrgId(apply.getHspOrgId());
        newSampleReportDto.setHspOrgName(apply.getHspOrgName());
        newSampleReportDto.setIsUploadPdf(YesOrNoEnum.YES.getCode());
        addSampleReport(newSampleReportDto);

        LoginUserHandler.User user = LoginUserHandler.get();
        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setApplyId(applySample.getApplyId());
        sampleFlow.setApplySampleId(applySample.getApplySampleId());
        sampleFlow.setBarcode(applySample.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.SAMPLE_REPORT_UPLOAD.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.SAMPLE_REPORT_UPLOAD.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        sampleFlow.setContent(String.format("上传PDF报告:%s", uploadPdfDto.getUrl()));
        sampleFlowService.addSampleFlow(sampleFlow);

        // 增加手动报告单缓存标记
        stringRedisTemplate.opsForValue().set(SampleReportDto.getIsUploadPdfKey(outsourcingSampleDto.getOutsourcingSampleId()), String.valueOf(YesOrNoEnum.YES.getCode()));

        return newSampleReportDto;
    }

    @Override
    public SampleReportDto uploadReportRoutine(UploadPdfDto uploadPdfDto) {
        uploadPdfDto.verifyParams();
        ApplySampleDto applySample = applySampleService.selectByApplySampleId(uploadPdfDto.getApplySampleId());
        Assert.notNull(applySample, "申请单样本不存在！");


        if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
            throw new IllegalStateException("样本不是未审核状态！");
        }

        ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        Assert.notNull(apply, "申请单不存在！");

        SampleDto outsourcingSampleDto = sampleService.selectByApplySampleId(applySample.getApplySampleId());
        Assert.notNull(outsourcingSampleDto, "样本不存在！");

        SampleReportDto oldSampleReport = selectByApplySampleId(applySample.getApplySampleId());
        if (Objects.nonNull(oldSampleReport)) {
            deleteBySampleReportId(oldSampleReport.getSampleReportId());
        }

        final SampleReportDto newSampleReportDto = new SampleReportDto();
        newSampleReportDto.setSampleReportId(snowflakeService.genId());
        newSampleReportDto.setApplySampleId(applySample.getApplySampleId());
        newSampleReportDto.setApplyId(apply.getApplyId());
        newSampleReportDto.setSampleId(outsourcingSampleDto.getSampleId());
        newSampleReportDto.setBarcode(outsourcingSampleDto.getBarcode());
        newSampleReportDto.setFileType(SampleReportFileTypeEnum.PDF.name());
        newSampleReportDto.setUrl(uploadPdfDto.getUrl());
        newSampleReportDto.setGroupName(applySample.getGroupName());
        newSampleReportDto.setGroupId(applySample.getGroupId());
        newSampleReportDto.setHspOrgId(apply.getHspOrgId());
        newSampleReportDto.setHspOrgName(apply.getHspOrgName());
        newSampleReportDto.setIsUploadPdf(YesOrNoEnum.YES.getCode());
        addSampleReport(newSampleReportDto);

        LoginUserHandler.User user = LoginUserHandler.get();
        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setApplyId(applySample.getApplyId());
        sampleFlow.setApplySampleId(applySample.getApplySampleId());
        sampleFlow.setBarcode(applySample.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.SAMPLE_REPORT_UPLOAD.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.SAMPLE_REPORT_UPLOAD.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        sampleFlow.setContent(String.format("上传PDF报告:%s", uploadPdfDto.getUrl()));
        sampleFlowService.addSampleFlow(sampleFlow);

        return newSampleReportDto;
    }

}
