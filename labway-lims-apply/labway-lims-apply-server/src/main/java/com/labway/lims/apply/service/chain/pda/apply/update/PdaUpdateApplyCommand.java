package com.labway.lims.apply.service.chain.pda.apply.update;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
public class PdaUpdateApplyCommand implements Command {

    @Override
    public boolean execute(Context c) throws Exception {
        final UpdateApplyContext from = UpdateApplyContext.from(c);

        final TestApplyDto testApply = from.getTestApply();

        final LoginUserHandler.User user = from.getUser();
        final HspOrganizationDto hspOrganization = from.getHspOrganization();
        final Date now = new Date();

        final PdaApplyDto pdaApplyDto = from.getPdaApply();

        final ApplyDto applyDto = JSON.parseObject(JSON.toJSONString(pdaApplyDto), ApplyDto.class);
        applyDto.setApplyId(pdaApplyDto.getPdaApplyId());

        checkApplyDto(applyDto, testApply, hspOrganization, user, now);

        from.put(UpdateApplyContext.APPLY, applyDto);
        return CONTINUE_PROCESSING;
    }

    private void checkApplyDto(ApplyDto apply, TestApplyDto testApply, HspOrganizationDto hspOrganization, LoginUserHandler.User user, Date now) {

        apply.setPatientName(testApply.getPatientName());
        // 年龄
        final Integer patientAge = testApply.getPatientAge();
        apply.setPatientAge(ObjectUtils.defaultIfNull(patientAge, NumberUtils.INTEGER_ZERO));
        apply.setPatientSubage(ObjectUtils.defaultIfNull(testApply.getPatientSubage(), NumberUtils.INTEGER_ZERO));
        apply.setPatientSubageUnit(
                ObjectUtils.defaultIfNull(testApply.getPatientSubageUnit(), PatientSubAgeUnitEnum.MONTH.getValue()));
        final Date patientBirthday = testApply.getPatientBirthday();
        if (Objects.nonNull(testApply.getPatientBirthday())) {
            apply.setPatientBirthday(patientBirthday);
        }

        apply.setPatientCard(StringUtils.defaultString(testApply.getPatientCard()));
        apply.setPatientCardType(StringUtils.defaultString(testApply.getPatientCardType()));
        apply.setPatientBed(StringUtils.defaultString(apply.getPatientBed()));
        apply.setPatientSex(testApply.getPatientSex());
        apply.setPatientVisitCard(StringUtils.defaultString(testApply.getPatientVisitCard()));
        apply.setPatientMobile(StringUtils.defaultString(testApply.getPatientMobile()));
        apply.setApplyTypeCode(StringUtils.defaultString(testApply.getApplyTypeCode()));
        apply.setApplyTypeName(StringUtils.defaultString(testApply.getApplyTypeName()));
        apply.setRemark(StringUtils.defaultString(testApply.getRemark()));
        apply.setSampleCount(ObjectUtils.defaultIfNull(testApply.getSampleCount(), NumberUtils.INTEGER_ONE));
        apply.setSampleProperty(StringUtils.defaultString(testApply.getSampleProperty()));
        apply.setSamplePropertyCode(StringUtils.defaultString(testApply.getSamplePropertyCode()));
        apply.setDept(StringUtils.defaultString(testApply.getDept()));
        apply.setDiagnosis(StringUtils.defaultString(testApply.getClinicalDiagnosis()));
        apply.setSendDoctorName(StringUtils.defaultString(testApply.getSendDoctor()));
        apply.setSendDoctorCode(StringUtils.EMPTY);
        apply.setPatientAddress(StringUtils.defaultString(testApply.getPatientAddress()));
        apply.setHspOrgId(hspOrganization.getHspOrgId());
        apply.setHspOrgCode(hspOrganization.getHspOrgCode());
        apply.setHspOrgName(hspOrganization.getHspOrgName());
        apply.setOrgId(user.getOrgId());
        apply.setOrgName(user.getOrgName());
        apply.setUrgent(ObjectUtils.defaultIfNull(testApply.getUrgent(), UrgentEnum.NORMAL.getCode()));
        apply.setApplyDate(testApply.getApplyDate());
        apply.setSamplingDate(ObjectUtils.defaultIfNull(testApply.getSamplingDate(), apply.getApplyDate()));
        apply.setUpdaterName(user.getNickname());
        apply.setUpdaterId(user.getUserId());
        apply.setUpdateDate(now);
        apply.setPatientBed(StringUtils.defaultString(testApply.getPatientBed()));
        apply.setOutBarcode(StringUtils.defaultString(testApply.getOutBarcode()));

        apply.setOriginalOrgCode(StringUtils.defaultString(testApply.getOriginalOrgCode()));
        apply.setOriginalOrgName(StringUtils.defaultString(testApply.getOriginalOrgName()));
    }

}
