package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 分血后交接的试管架
 */
@Getter
@Setter
public class SplitBloodHandoverRackVo {

    /**
     * 逻辑试管架ID
     */
    private Long rackLogicId;

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 待交接专业组名称
     */
    private List<String> groupNames;

    /**
     * 分血人ID
     */
    private Long splitterId;

    /**
     * 分血人
     */
    private String splitterName;

    /**
     * 分血时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date splitDate;

    /**
     * 样本数量
     */
    private Integer count;
}
