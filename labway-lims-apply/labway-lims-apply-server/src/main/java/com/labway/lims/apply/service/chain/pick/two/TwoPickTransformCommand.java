package com.labway.lims.apply.service.chain.pick.two;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 如果这个样本是需要组间交接的，那么传递下去
 */
@Slf4j
@Component
public class TwoPickTransformCommand implements Command {

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private RackLogicService rackLogicService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);
        final ApplySampleDto applySample = context.getApplySample();
        final RackLogicDto rackLogic = context.getRackLogic();
        final Map<Long, List<ApplySampleItemDto>> applySampleItems = context.getApplySampleItems()
                .stream().filter(e -> !Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()))
                .collect(Collectors.groupingBy(ApplySampleItemDto::getGroupId));

        // 如果不为空表示有多个专业组的，那么就需要进行组间交接
        if (MapUtils.isEmpty(applySampleItems)) {
            return CONTINUE_PROCESSING;
        }

        // 复制一个样本出来
        final ApplySampleDto newApplySample = JSON.parseObject(JSON.toJSONString(applySample), ApplySampleDto.class);
        newApplySample.setApplySampleId(snowflakeService.genId());

        // 因为要重新走二次分拣 所以设置成没有分拣
        newApplySample.setIsTwoPick(YesOrNoEnum.NO.getCode());
        newApplySample.setTwoPickDate(new Date());
        newApplySample.setTwoPickerId(LoginUserHandler.get().getUserId());
        newApplySample.setTwoPickerName(LoginUserHandler.get().getNickname());
        newApplySample.setIsOnePick(YesOrNoEnum.YES.getCode());
        // 涉及到组间交接，此时还不确定这个条码属于哪个检验
        newApplySample.setItemType(StringUtils.EMPTY);
        // 组间交接，这时候还没有专业组
        newApplySample.setGroupId(NumberUtils.LONG_ZERO);
        newApplySample.setGroupName(StringUtils.EMPTY);
        applySampleService.addApplySample(newApplySample);

        // 修改剩下的样本项目的关联
        applySampleItemService.updateBatchById(applySampleItems.values().stream().flatMap(Collection::stream).map(e -> {
            final ApplySampleItemDto item = new ApplySampleItemDto();
            item.setApplySampleItemId(e.getApplySampleItemId());
            item.setApplySampleId(newApplySample.getApplySampleId());
            return item;
        }).collect(Collectors.toList()));

        // 复制条码环节
        sampleFlowService.copySampleFlows(applySample.getApplySampleId(), newApplySample.getApplySampleId());

        // 复制多个逻辑试管架
        for (Long groupId : applySampleItems.keySet()) {
            final RackLogicDto newRackLogic;

            // 获取一个正在组间交接的试管架

            final RackLogicDto nextRackLogic = rackLogicService
                    .selectByRackCodeAndNextGroupIdAndPosition(rackLogic.getRackCode(), groupId, RackLogicPositionEnum.GROUP_PICKING.getCode())
                    .stream().filter(e -> Objects.equals(e.getCurrentGroupId(), LoginUserHandler.get().getGroupId()))
                    .findFirst().orElse(null);
            if (Objects.isNull(nextRackLogic)) {
                newRackLogic = new RackLogicDto();
                BeanUtils.copyProperties(context.getRackLogic(), newRackLogic);
                newRackLogic.setPosition(RackLogicPositionEnum.GROUP_PICKING.getCode());
                newRackLogic.setNextGroupId(groupId);
                final String groupName = applySampleItems.values().stream().flatMap(Collection::stream)
                        .filter(e -> Objects.equals(e.getGroupId(), groupId)).findFirst().map(ApplySampleItemDto::getGroupName)
                        .orElse(StringUtils.EMPTY);
                newRackLogic.setNextGroupName(groupName);
                newRackLogic.setRackLogicId(snowflakeService.genId());

                // 添加新的试管架
                rackLogicService.addRackLogic(newRackLogic);
            } else {
                newRackLogic = nextRackLogic;
            }

            final RackLogicSpaceDto rackLogicSpace = new RackLogicSpaceDto();
            BeanUtils.copyProperties(context.getRackLogicSpace(), rackLogicSpace);
            rackLogicSpace.setRackLogicSpaceId(snowflakeService.genId());
            rackLogicSpace.setApplySampleId(newApplySample.getApplySampleId());
            rackLogicSpace.setRackLogicId(newRackLogic.getRackLogicId());

            rackLogicSpaceService.addRackLogicSpace(rackLogicSpace);

        }

        // 标记为组间交接
        context.getApplySampleTwoPicks().stream().filter(e -> Objects.equals(e.getApplySampleId(), context.getTwoPick().getApplySampleId()))
                .findFirst().ifPresent(e -> e.setIsTransform(true));


        return CONTINUE_PROCESSING;
    }


}
