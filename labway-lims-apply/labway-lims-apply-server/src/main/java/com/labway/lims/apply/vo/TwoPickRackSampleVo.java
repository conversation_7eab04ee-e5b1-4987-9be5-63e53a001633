package com.labway.lims.apply.vo;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 样本列表
 */
@Getter
@Setter
public class TwoPickRackSampleVo {

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 检验项目
     */
    private List<String> testItemNames;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 二次分拣日期
     */
    private Date twoPickDate;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 管型
     */
    private String tubeName;

    /**
     * 名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 所在行
     */
    private Integer row;

    /**
     * 所在列
     */
    private Integer column;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 仪器专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 仪器专业小组颜色
     */
    private String secondSortColor;


    /**
     * 是否已经二次分拣 1是，0不是
     * @see YesOrNoEnum
     */
    private Integer isTwoPick;


    public String getTwoPickDateText() {
        if (Objects.isNull(twoPickDate)) {
            return StringUtils.EMPTY;
        }

        return Objects.equals(twoPickDate, DefaultDateEnum.DEFAULT_DATE.getDate())
                ? StringUtils.EMPTY : DateUtil.format(twoPickDate, DatePattern.NORM_DATETIME_PATTERN);
    }

}
