package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 血培养项目
 */
@Getter
@Setter
public class ApplySampleItemBloodCultureVo implements Serializable {
    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 申请单项目id
     */
    private Long applySampleItemId;

    /**
     * 血培养id
     */
    private Long applySampleItemBloodCultureId;

    /**
     *  收费数量
     */
    private Integer count;

    /**
     * left upper limb anaerobic 左上肢厌氧
     */
    private Integer lulAnaerobic;

    /**
     * left upper limb aerobic 左上肢需氧气
     */
    private Integer lulAerobic;

    /**
     * left upper limb pediatric bottle  左上肢儿童瓶子
     */
    private Integer lulPediatricBottle;

    /**
     * left upper limb anaerobic 左下肢厌氧
     */
    private Integer lllAnaerobic;

    /**
     * left upper limb aerobic 左下肢需氧
     */
    private Integer lllAerobic;

    /**
     * left lower limb pediatric bottle  左下肢儿童瓶子
     */
    private Integer lllPediatricBottle;

    /**
     * right upper limb anaerobic 右下肢厌氧
     */
    private Integer rulAnaerobic;

    /**
     * right upper limb aerobic 右上肢需氧
     */
    private Integer rulAerobic;

    /**
     * right upper limb pediatric bottle 右上肢儿童瓶
     */
    private Integer rulPediatricBottle;

    /**
     * right lower limb anaerobic 右下肢厌氧
     */
    private Integer rllAnaerobic;

    /**
     * right lower limb aerobic 右下肢需氧
     */
    private Integer rllAerobic;

    /**
     * right lower limb pediatric bottle 右下肢儿童瓶
     */
    private Integer rllPediatricBottle;

    /**
     * 厌氧
     */
    private Integer anaerobic;

    /**
     * 需氧
     */
    private Integer aerobic;

    /**
     * 儿童瓶
     */
    private Integer pediatricBottle;
}
