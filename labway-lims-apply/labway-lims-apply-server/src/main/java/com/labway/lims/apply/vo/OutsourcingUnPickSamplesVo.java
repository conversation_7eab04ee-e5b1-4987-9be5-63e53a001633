package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 外送未分拣样本
 */
@Getter
@Setter
public class OutsourcingUnPickSamplesVo {

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 申请单样本
     */
    private Long applySampleId;

    /**
     * 检验项目名称
     */
    private List<String> testItemNames;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;


    /**
     * 性别  性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 送检机构名称
     */
    private String hspOrgName;
}
