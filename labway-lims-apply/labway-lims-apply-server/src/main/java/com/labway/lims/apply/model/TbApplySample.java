package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 申请单样本
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_apply_sample")
public class TbApplySample implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long applySampleId;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 外部条码号
     */
    private String outBarcode;

    /**
     * 管型名称
     */
    private String tubeName;
    /**
     * 管型编码
     */
    private String tubeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;

    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 试管架
     */
    private Long rackId;


    /**
     * 一次分拣人id
     */
    private Long onePickerId;

    /**
     * 一次分拣人
     */
    private String onePickerName;

    /**
     * 1: 急诊 0:不急
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新者
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 1:已删除 0：未删除
     * @see YesOrNoEnum
     * @see YesOrNoEnum
     */
    private Integer isDelete;


    /**
     * 是否已经一次分拣 1是，0不是
     * @see YesOrNoEnum
     */
    private Integer isOnePick;

    /**
     * 一次分拣日期
     */
    private Date onePickDate;


    /**
     * 二次分拣人id
     */
    private Long twoPickerId;

    /**
     * 二次分拣人
     */
    private String twoPickerName;

    /**
     * 二次分拣日期
     */
    private Date twoPickDate;

    /**
     * 是否已经二次分拣 1是，0不是
     * @see YesOrNoEnum
     */
    private Integer isTwoPick;

    /**
     * 是否是免疫二次分拣 1：是 0：否
     */
    private Integer isImmunityTwoPick;

    /**
     * 是否已经分血，1：是，0：不是
     * @see YesOrNoEnum
     */
    private Integer isSplitBlood;


    /**
     * 分血人ID
     */
    private Long splitterId;


    /**
     * 分血人
     */
    private String splitterName;


    /**
     * 分血时间
     */
    private Date splitDate;

    /**
     * 样本状态
     * 待审核 待二审 已审核  终止（99）
     * @see com.labway.lims.api.enums.apply.ApplyStatusEnum
     */
    private Integer status;
    /**
     * 是否禁用 1是 0 否
     * @see YesOrNoEnum
     */
    private Integer isDisabled;
    /**
     * 打印人ID
     */
    private Long printerId;

    /**
     * 打印人名称
     */
    private String printerName;

    /**
     * 打印时间
     */
    private Date printDate;
    /**
     * 检验人ID
     */
    private Long testerId;
    /**
     * 检验人姓名
     */
    private String testerName;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 项目类型ID
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 1外送，0:不是外送
     * @see YesOrNoEnum
     */
    private Integer isOutsourcing;


    /**
     * 是否归档:0未归档，1已归档
     * @see YesOrNoEnum
     */
    private Integer isArchive;
    /**
     * 是否打印:1已打印，0未打印
     * @see YesOrNoEnum
     */
    private Integer isPrint;
    /**
     * 样本备注
     */
    private String sampleRemark;

    /**
     * 结果备注
     */
    private String resultRemark;


    /**
     * 送检机构
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;


    /**
     * 原始条码号，一旦赋值 从始至终都不会改变
     */
    private String originalBarcode;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 样本性状编码
     */
    private String samplePropertyCode;

    /**
     * 颜色状态标记 : 10未审 20一审 30已审 40反审 50重审
     * @see SampleStatusEnum
     */
    private Integer colorMarking;

    /**
     * 并单主条码
     */
    private String mergeMasterBarcode;

    /**
     * 并单的额外数据
     * @see com.labway.lims.routine.api.dto.CombinedBillAddIdDto
     */
    private String mergeExtraInfo;

    /**
     * 是否已打印清单，1:是，0:不是
     * @see YesOrNoEnum
     */
    private Integer isPrintList;

    /**
     * 打印清单日期
     */
    private Date printListDate;

	/**
	 * 标本部位
	 * @since 1.1.4
	 * @Description <a href="https://www.tapd.cn/59091617/prong/stories/view/1159091617001001756?from_iteration_id=1159091617001000242">评论</a>
	 */
	private String patientPart;

    /**
     * 样本来源
     * @see com.labway.lims.api.enums.apply.SampleSourceEnum
     */
    private String sampleSource;

    /**
     * 报告编号
     */
    private String reportNo;

}
