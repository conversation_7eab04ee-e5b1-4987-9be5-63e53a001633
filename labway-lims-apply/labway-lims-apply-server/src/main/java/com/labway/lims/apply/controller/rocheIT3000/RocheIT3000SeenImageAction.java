package com.labway.lims.apply.controller.rocheIT3000;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.vo.IT3000HandleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * IT3000 拍照
 */
@Slf4j
@Component
class RocheIT3000SeenImageAction implements ActionStrategy {

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private SnowflakeService snowflakeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object action(IT3000HandleVo vo) throws Exception {
        final String barcode = vo.getExtras().getString("barcode");
        final String imageUrl = vo.getExtras().getString("url");

        final List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException(String.format("条码号 [%s] 不存在", barcode));
        }

        final LinkedList<Long> ids = snowflakeService.genIds(applySamples.size());

        sampleFlowService.addSampleFlows(applySamples.stream().map(e -> SampleFlowDto.builder()
                .applyId(e.getApplyId())
                .sampleFlowId(ids.pop())
                .applySampleId(e.getApplySampleId())
                .operateCode(BarcodeFlowEnum.IT8000SEEN_IMAGE.name())
                .operateName(BarcodeFlowEnum.IT8000SEEN_IMAGE.getDesc())
                .operatorId(LoginUserHandler.get().getUserId())
                .operator(LoginUserHandler.get().getNickname())
                .barcode(e.getBarcode())
                .content(imageUrl)
                .build()).collect(Collectors.toList()));

        log.info("条码 [{}] Roche 拍照 [{}] 记录条码环节成功",
                barcode, imageUrl);

        return Map.of();
    }


    @Override
    public IT3000HandleVo.Action action() {
        return IT3000HandleVo.Action.SEEN_IMAGE;
    }


}
