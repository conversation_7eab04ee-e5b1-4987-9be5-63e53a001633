package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 归档样本信息
 *
 * <AUTHOR>
 * @since 2023/4/17 15:04
 */
@Getter
@Setter
public class ArchiveSampleResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    // --------------RackLogicSpaceDto---------------

    /**
     * ID
     */
    private Long rackLogicSpaceId;

    /**
     * 多少行
     */
    private Integer row;

    /**
     * 多少列
     */
    private Integer column;


    /**
     * 放置的申请单样本
     */
    private Long applySampleId;

    // --------------ApplySampleDto---------------

    /**
     * 条码
     */
    private String barcode;
    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    // --------------ApplyDto---------------
    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 性别 1 男，2:女
     * @see SexEnum
     */
    private Integer patientSex;

    //---------------
    /**
     * 位置
     */
    private String positionDesc;
}
