package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 条码环节
 */
@Getter
@Setter
public class SampleFlowVo {

    /**
     * ID
     */
    private Long sampleFlowId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 操作类型
     *
     * @see BarcodeFlowEnum
     */
    private String operateCode;

    /**
     * 操作类型
     */
    private String operateName;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createDate;
}
