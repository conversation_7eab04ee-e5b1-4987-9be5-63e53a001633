package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/6/8 10:30
 * 临时导入文件
 */
@Getter
@Setter
public class ImportSampleDataRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件保存的路径
     */
    private String url;

    /**
     * 请求接口地址
     */
    private String postUrl;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 外部检验项目编码
     */
    private String outTestItemCode;

    /**
     * 检验项目名称
     */
    private String outTestItemName;

    /**
     * 采样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date samplingDate;


}
