package com.labway.lims.apply.service.chain.apply.update.batch;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.apply.service.chain.apply.update.sample.UpdateApplySamplePostCommand;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.routine.api.dto.SimpleApplyDto;
import com.labway.lims.routine.api.service.SampleResultService;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/2/21 16:28
 */

@Slf4j
@Component
public class BatchRefreshReportCommand implements Command {

    @DubboReference
    private SampleResultService sampleResultService;
    @Resource
    private SampleReportService sampleReportService;
    private static final String ROUTING_KEY = "sample_change_key";

    @DubboReference
    private RabbitMQService rabbitMQService;

    @Override
    public boolean execute(Context c) throws Exception {
        BatchUpdateApplyContext from = BatchUpdateApplyContext.from(c);
        LoginUserHandler.User user = from.getUser();
        HspOrganizationDto hspOrganization = from.getHspOrganization();
        Map<Long, ApplyDto> applyById = from.getApplys().stream().peek(apply -> {
            apply.setHspOrgId(hspOrganization.getHspOrgId());
            apply.setHspOrgCode(hspOrganization.getHspOrgCode());
            apply.setHspOrgName(hspOrganization.getHspOrgName());
        }).collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));
        List<ApplySampleDto> applySamples = from.getApplySamples();
        // 常规、外送 刷新参考范围，并且刷新一下结果
        for (Long applyId : applySamples.stream()
            .filter(obj -> Objects.equals(obj.getItemType(), ItemTypeEnum.ROUTINE.name()))
            .map(ApplySampleDto::getApplyId).collect(Collectors.toSet())) {
            ApplyDto applyDto = applyById.get(applyId);
            sampleResultService.refreshReference(JSON.parseObject(JSON.toJSONString(applyDto), SimpleApplyDto.class));
            SpringUtil.getBean(UpdateApplySamplePostCommand.class).updateResultByApplyId(ItemTypeEnum.ROUTINE.name(),
                applyId);
        }
        for (Long applyId : applySamples.stream()
            .filter(obj -> Objects.equals(obj.getItemType(), ItemTypeEnum.OUTSOURCING.name()))
            .map(ApplySampleDto::getApplyId).collect(Collectors.toSet())) {
            ApplyDto applyDto = applyById.get(applyId);
            sampleResultService.refreshReference(JSON.parseObject(JSON.toJSONString(applyDto), SimpleApplyDto.class));
            SpringUtil.getBean(UpdateApplySamplePostCommand.class)
                .updateResultByApplyId(ItemTypeEnum.OUTSOURCING.name(), applyId);
        }
        Boolean refreshReport = from.getBatchUpdateApplyDto().getRefreshReport();
        if (!BooleanUtils.isTrue(refreshReport)) {
            return CONTINUE_PROCESSING;
        }
        // 已审核样本 刷新报告
        List<ApplySampleDto> auditSample = from.getApplySamples().stream()
            .filter(obj -> Objects.equals(obj.getStatus(), SampleStatusEnum.AUDIT.getCode()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(auditSample)) {
            return CONTINUE_PROCESSING;
        }
        for (ApplySampleDto sample : auditSample) {

            sampleReportService.refreshReport(sample);

            final ApplySampleEventDto event = new ApplySampleEventDto();
            event.setOrgId(user.getOrgId());

            event.setHspOrgId(hspOrganization.getHspOrgId());
            event.setHspOrgCode(hspOrganization.getHspOrgCode());
            event.setHspOrgName(hspOrganization.getHspOrgName());
            event.setApplyId(sample.getApplyId());
            event.setApplySampleId(sample.getApplySampleId());
            event.setBarcode(sample.getBarcode());
            event.setExtras(Map.of());
            event.setEvent(ApplySampleEventDto.EventType.TwoCheck);

            final String json = JSON.toJSONString(event);
            rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

            log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", sample.getApplySampleId(), sample.getBarcode(), json,
                RabbitMQService.EXCHANGE, ROUTING_KEY);

        }

        return CONTINUE_PROCESSING;
    }
}
