package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 一次分拣试管架下的样本
 */
@Getter
@Setter
public class OnePickRacksSamplesVo {
    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;


    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码号
     */
    private String barcode;


    /**
     * 管型
     */
    private String tubeName;
    /**
     * 管型CODE
     */
    private String tubeCode;

    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;


    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 检验项目名称
     */
    private List<String> testItemNames;

    /**
     * 就诊卡号
     */
    private String patientVisitCard;


}
