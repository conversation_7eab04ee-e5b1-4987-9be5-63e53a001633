package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class HandoverVo {
    /**
     * 送检机构
     */
    private String hspOrgCode;

    /**
     * 条码
     */
    private List<Sample> samples;


    @Getter
    @Setter
    public final static class Sample {
        /**
         * 条码
         */
        private String barcode;

        /**
         * 申请单编码
         */
        private String formCode;


    }

}
