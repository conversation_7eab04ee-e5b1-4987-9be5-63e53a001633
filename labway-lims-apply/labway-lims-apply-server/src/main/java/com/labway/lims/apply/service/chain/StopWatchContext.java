package com.labway.lims.apply.service.chain;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import lombok.Getter;
import org.apache.commons.chain.impl.ContextBase;

import javax.swing.event.EventListenerList;

/**
 * StopWatchContext
 *
 * <AUTHOR>
 */
@Getter
public abstract class StopWatchContext extends ContextBase {
    private static final String WATCH = "WATCH_" + IdUtil.objectId();
    private final EventListenerList listenerList = new EventListenerList();


    public StopWatchContext() {
        put(WATCH, new StopWatch(getWatcherName()));
    }


public StopWatch getWatch() {
        return (StopWatch) get(WATCH);
    }

    protected abstract String getWatcherName();


}
