package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 样本归档-存储 请求Vo
 * 
 * <AUTHOR>
 * @since 2023/4/13 15:08
 */
@Getter
@Setter
public class RackArchiveAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 冰箱ID
     */
    private Long refrigeratorId;
    /**
     * 试管架id
     */
    private Long rackId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 从指定位置 开始
     */
    private boolean fromAssign;

    /**
     * 多少行
     */
    private Integer row;

    /**
     * 多少列
     */
    private Integer column;

    /**
     * 有效时间从
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date startEffectiveDate;
    /**
     * 有效时间至
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endEffectiveDate;
}
