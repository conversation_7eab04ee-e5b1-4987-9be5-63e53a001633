package com.labway.lims.apply.service.chain.material.delivery.business.noapply;

import cn.hutool.core.lang.Assert;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.BusinessCenterDeliveryDto;
import com.labway.lims.apply.service.chain.material.delivery.business.BusinessCenterDeliveryContext;
import com.labway.lims.base.api.dto.GroupMaterialDetailDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import com.labway.lims.base.api.service.GroupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 接收业务中台 出库信息 检查 入参 信息
 *
 * <AUTHOR>
 * @since 2023/5/6 14:30
 */
@Slf4j
@Component
public class BusinessCenterNoApplyDeliveryCheckParamCommand implements Command {

    private static final int DELIVERY_ITEM_ERROR_CODE = 10002;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private GroupMaterialService groupMaterialService;

    @Override
    public boolean execute(Context context) throws Exception {
        final BusinessCenterDeliveryContext from = BusinessCenterDeliveryContext.from(context);
        final BusinessCenterDeliveryDto deliveryDto = from.getDeliveryDto();

        if (Objects.isNull(deliveryDto)) {
            throw new LimsException("出库参数不可为空");
        }

        final Long groupId = deliveryDto.getGroupId();
        Assert.notNull(groupId, "出库专业组不能为空");
        final ProfessionalGroupDto groupDto = groupService.selectByGroupId(groupId);
        Assert.notNull(groupDto, "出库专业组不存在");

        if (Objects.isNull(deliveryDto.getOrgId()) || Objects.isNull(deliveryDto.getDeliveryDate()) || StringUtils
                .isAnyBlank(deliveryDto.getDeliveryNo(), deliveryDto.getDeliveryUser())) {
            throw new LimsException("存在必填项未填写");
        }
        if (CollectionUtils.isEmpty(deliveryDto.getDeliveryItemList())) {
            throw new LimsException(String.format("出库单号 [%s] 对应出库物料不存在", deliveryDto.getDeliveryNo()));
        }

        Map<String, String> error = new HashMap<>();
        int index = 0;
        for (BusinessCenterDeliveryDto.BusinessCenterDeliveryItemDto item : deliveryDto.getDeliveryItemList()) {
            StringBuilder errorMessage = new StringBuilder();
            if (StringUtils.isBlank(item.getMaterialCode())) {
                errorMessage.append("物资编码不可为空");
            }
            if (StringUtils.isBlank(item.getMaterialName())) {
                errorMessage.append("物资名称不可为空");
            }
            if (StringUtils.isBlank(item.getSpecification())) {
                errorMessage.append("规格不可为空");
            }
            if (StringUtils.isBlank(item.getBatchNo())) {
                errorMessage.append("批号不可为空");
            }
            if (StringUtils.isBlank(item.getManufacturers())) {
                errorMessage.append("厂家不可为空");
            }
            if (StringUtils.isBlank(item.getMainUnit())) {
                errorMessage.append("主单位不可为空");
            }
            if (Objects.isNull(item.getDeliveryMainNumberDecimal())) {
                errorMessage.append("出库主单位数量不可为空");
            }
            if (StringUtils.isBlank(item.getAssistUnit())) {
                errorMessage.append("辅单位不可为空");
            }
            if (Objects.isNull(item.getDeliveryAssistNumber())) {
                errorMessage.append("出库辅单位数量不可为空");
            }
            if (StringUtils.isBlank(item.getUnitConversionRate())) {
                errorMessage.append("主辅单位换算率不可为空");
            }
            if (Objects.isNull(item.getValidDate())) {
                errorMessage.append("有效期不可为空");
            }
            if (StringUtils.isBlank(item.getMaterialBarcode())) {
                errorMessage.append("物料条码不可为空");
            }
            if (StringUtils.isNotBlank(errorMessage)) {
                error.put(String.format("位置 [%s] ,物料名称 [%s]", index, item.getMaterialName()), errorMessage.toString());
            }
            index += 1;
        }
        if (!error.isEmpty()) {
            throw new LimsCodeException(DELIVERY_ITEM_ERROR_CODE, String.format("出库物料信息有误:[%s]", error));
        }

        // todo 机构目前写死 检查暂时忽略

        //        // 检查 申请单号
        //        GroupMaterialApplyDto materialApplyDto =
        //            groupMaterialApplyService.selectByOrgIdAndApplyNo(deliveryDto.getOrgId(), deliveryDto.getApplyNo());
        //
        //        if (Objects.isNull(materialApplyDto)) {
        //            throw new LimsException(String.format("无效申请单号 [%s] ", deliveryDto.getApplyNo()));
        //        }

        // 出库物料编码
        Set<String> materialCodes = deliveryDto.getDeliveryItemList().stream()
                .map(BusinessCenterDeliveryDto.BusinessCenterDeliveryItemDto::getMaterialCode).collect(Collectors.toSet());

        // 检查物料在专业组下是否存在
        List<GroupMaterialDetailDto> groupMaterialDtos =
                groupMaterialService.selectByGroupIdAndMaterialCodes(groupId, materialCodes);

        // 物料编码->物料信息
        Map<String, GroupMaterialDetailDto> materialDtoByMaterialCode = groupMaterialDtos.stream().collect(
                Collectors.toMap(GroupMaterialDetailDto::getMaterialCode, Function.identity(), (key1, key2) -> key1));

        // 不存在的物料编码
        List<String> noExistMaterialCodes =
                materialCodes.stream().filter(x -> !materialDtoByMaterialCode.containsKey(x)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noExistMaterialCodes)) {
            throw new LimsException(String.format("对应物料编码 [%s] 专业组下不存在", String.join(",", noExistMaterialCodes)));
        }

        // 将出库信息 对应LIMS 申领单信息 放入 上下文
        from.put(BusinessCenterDeliveryContext.MATERIAL_CODE_MATERIAL_DTO, materialDtoByMaterialCode);
        from.setGroupDto(groupDto);
        //        from.put(BusinessCenterDeliveryContext.MATERIAL_APPLY, materialApplyDto);

        return CONTINUE_PROCESSING;
    }
}
