package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @description  报告单迟发表
 * <AUTHOR>
 * @date 2023-12-15
 */
@Getter
@Setter
@TableName("tb_report_delay")
public class TbReportDelay implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * @Fields delayId 迟发ID
     */
    @TableId
    private Long delayId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 检验项目编码集合(空 为全部)
     */
    private String testItemCodes;

    /**
     * 迟发原因
     */
    private String reason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预计发布日期
     */
    private Date sendReportDate;

    /**
     * 作废人ID
     */
    private Long cancelUserId;

    /**
     * 作废人姓名
     */
    private String cancelUserName;

    /**
     * 申请单状态(0未打印 1已打印 9已作废)
     */
    private Integer status;

    /**
     * 专业组id
     */
    private Long groupId;

    /**
     * 专业组
     */
    private String groupName;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 修改人id
     */
    private Long updaterId;

    /**
     * @Fields updaterName 修改人名称
     */
    private String updaterName;

    public TbReportDelay() {}
}