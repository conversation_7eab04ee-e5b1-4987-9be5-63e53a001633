package com.labway.lims.apply.service.chain.sample.archive.add;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.RefrigeratorDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 样本 归档添加流水
 * 
 * <AUTHOR>
 * @since 2023/4/28 17:23
 */
@Slf4j
@Component
public class SampleArchiveAddFlowCommand implements Command {

    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SampleArchiveAddContext from = SampleArchiveAddContext.from(context);
        final List<ApplySampleDto> applySamples = from.getApplySamples();
        final LoginUserHandler.User user = from.getUser();

        // 对应冰箱
        final RefrigeratorDto refrigerator = from.getRefrigerator();

        // 对应物理试管架
        final RackDto rack = from.getRack();

        // 样本 空间占用
        final RackLogicSpaceDto rackLogicSpace = from.getRackLogicSpaces().iterator().next();

        final List<SampleFlowDto> sampleFlowDtoList = applySamples.stream().map(applySample -> {
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(snowflakeService.genId());
            sampleFlow.setApplyId(applySample.getApplyId());
            sampleFlow.setApplySampleId(applySample.getApplySampleId());
            sampleFlow.setBarcode(applySample.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.ARCHIVE_SAMPLE.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.ARCHIVE_SAMPLE.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(String.format("样本归档到 [%s] 冰箱下 [%s] 试管架 [%s] 行 [%s] 列", refrigerator.getRefrigeratorName(),
                    rack.getRackCode(), rackLogicSpace.getRow() + 1, rackLogicSpace.getColumn() + 1));
            return sampleFlow;
        }).collect(Collectors.toList());
        sampleFlowService.addSampleFlows(sampleFlowDtoList);

        return CONTINUE_PROCESSING;
    }
}
