package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.vo.BizMaterialBarcodeVo;
import com.labway.lims.apply.model.TbMaterialDeliveryDetail;
import com.labway.lims.apply.model.TbMaterialDeliveryRecord;
import com.labway.lims.apply.model.TbMaterialIncomeRecord;
import com.labway.lims.apply.vo.MaterialDeliveryDetailItemVo;
import com.labway.lims.apply.vo.MaterialDeliveryFinishListRequestVo;
import com.labway.lims.apply.vo.MaterialDeliveryFinishListResponseVo;
import com.labway.lims.apply.vo.MaterialDeliveryWaitListRequestVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.List;

/**
 * 物料出库 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 19:21
 */
@Mapper(componentModel = "spring")
public interface MaterialDeliveryConverter {

    SelectMaterialDeliveryRecordDto fromMaterialDeliveryWaitListRequestVo(MaterialDeliveryWaitListRequestVo vo);

    SelectMaterialIncomeInfoDto fromMaterialDeliveryFinishListRequestVo(MaterialDeliveryFinishListRequestVo vo);

    MaterialDeliveryFinishListResponseVo fromMaterialIncomeInfoDto(MaterialIncomeInfoDto dto);

    MaterialDeliveryRecordDto fromTbMaterialDeliveryRecord(TbMaterialDeliveryRecord dto);

    List<MaterialDeliveryRecordDto> fromTbMaterialDeliveryRecordList(List<TbMaterialDeliveryRecord> list);

	@Mapping(target = "deliveryMainNumberDecimal", source = "deliveryMainNumber")
    MaterialDeliveryDetailDto fromTbMaterialDeliveryDetail(TbMaterialDeliveryDetail dto);

    List<MaterialDeliveryDetailDto> fromTbMaterialDeliveryDetailList(List<TbMaterialDeliveryDetail> list);

	@Mapping(target = "deliveryMainNumber", source = "deliveryMainNumberDecimal")
	MaterialDeliveryDetailItemVo fromMaterialDeliveryDetailDto(MaterialDeliveryDetailDto obj);

    List<MaterialDeliveryDetailItemVo> fromMaterialDeliveryDetailDtoList(List<MaterialDeliveryDetailDto> list);

    MaterialDeliveryDetailItemVo fromMaterialIncomeRecordDto(MaterialIncomeRecordDto obj);

    List<MaterialDeliveryDetailItemVo> fromMaterialIncomeRecordDtoList(List<MaterialIncomeRecordDto> list);

    List<TbMaterialIncomeRecord> incomeRecordFromMaterialIncomeRecordDtoList(List<MaterialIncomeRecordDto> list);

    MaterialIncomeRecordDto fromTbMaterialIncomeRecord(TbMaterialIncomeRecord obj);

    @Mapping(target = "deliveryDetailId", source = "detailId")
    @Mapping(target = "deliveryMainNumber", source = "deliveryMainNumberDecimal")
    MaterialIncomeRecordDto incomeRecordFromMaterialDeliveryDetailDto(MaterialDeliveryDetailDto obj);

    List<MaterialIncomeRecordDto> fromTbMaterialIncomeRecordList(List<TbMaterialIncomeRecord> list);

    @Mapping(target = "incomeDate", source = "createDate")
    @Mapping(target = "incomeUser", source = "creatorName")
    MaterialIncomeInfoDto incomeInfoFromTbMaterialIncomeRecord(TbMaterialIncomeRecord obj);

    List<MaterialIncomeInfoDto> incomeInfoFromTbMaterialIncomeRecordList(List<TbMaterialIncomeRecord> list);

    TbMaterialDeliveryRecord fromMaterialDeliveryRecordDto(MaterialDeliveryRecordDto dto);

    MaterialDeliveryDetailDto
        fromBusinessCenterDeliveryItemDto(BusinessCenterDeliveryDto.BusinessCenterDeliveryItemDto dto);

	@Mapping(target = "deliveryMainNumber", source = "deliveryMainNumberDecimal")
    TbMaterialDeliveryDetail deliveryDetailFromMaterialDeliveryDetailDto(MaterialDeliveryDetailDto dto);

	default List<TbMaterialDeliveryDetail>
		deliveryDetailFromMaterialDeliveryDetailDtoList(List<MaterialDeliveryDetailDto> list) {
		if ( list == null ) {
			return null;
		}

		List<TbMaterialDeliveryDetail> list1 = new ArrayList<TbMaterialDeliveryDetail>( list.size() );
		for ( MaterialDeliveryDetailDto materialDeliveryDetailDto : list ) {
			TbMaterialDeliveryDetail tbMaterialDeliveryDetail = deliveryDetailFromMaterialDeliveryDetailDto(materialDeliveryDetailDto);
			tbMaterialDeliveryDetail.setDeliveryMainNumber( materialDeliveryDetailDto.getDeliveryMainNumberDecimal());
			list1.add( tbMaterialDeliveryDetail );
		}

		return list1;
	}

	List<BizMaterialBarcodeVo> convertIncomeRecords2BizMaterialBarcodeVoList(List<TbMaterialIncomeRecord> incomeRecords);

}
