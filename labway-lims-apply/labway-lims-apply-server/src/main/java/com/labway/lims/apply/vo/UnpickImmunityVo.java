package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <pre>
 * UnpickImmunityVo
 * 取消二次分拣（免疫）
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/11/4 14:49
 */
@Getter
@Setter
public class UnpickImmunityVo implements Serializable {

    private List<Item> items;

    @Getter
    @Setter
    public static class Item {
        /**
         * 条码号
         */
        private String barcode;

        /**
         * 申请单样本ID
         */
        private Long applySampleId;
    }

}
