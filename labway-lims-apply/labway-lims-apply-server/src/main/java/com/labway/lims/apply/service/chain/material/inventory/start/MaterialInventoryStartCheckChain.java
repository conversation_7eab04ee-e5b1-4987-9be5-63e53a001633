package com.labway.lims.apply.service.chain.material.inventory.start;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 开始盘点 库存
 * 
 * <AUTHOR>
 * @since 2023/5/11 17:10
 */
@Component
public class MaterialInventoryStartCheckChain extends ChainBase implements InitializingBean {

    @Resource
    private MaterialInventoryStartCheckLockCommand materialInventoryStartCheckLockCommand;

    @Resource
    private MaterialInventoryStartCheckCheckParamCommand materialinventorystartcheckcheckparamcommand;

    @Resource
    private MaterialInventoryStartCheckAddCheckRecordCommand materialInventoryStartCheckAddCheckRecordCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 上锁
        addCommand(materialInventoryStartCheckLockCommand);

        // 检查 参数
        addCommand(materialinventorystartcheckcheckparamcommand);

        // 创建 盘点记录
        addCommand(materialInventoryStartCheckAddCheckRecordCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);
    }
}
