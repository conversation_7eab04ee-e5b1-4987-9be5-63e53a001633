package com.labway.lims.apply.vo;

import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/4/23 17:40
 */
@Getter
@Setter
public class GermResistanceQueryVo {
    /**
     * groupId
     */
    private Long groupId;

    /**
     * 检验项目(检验目的)ID
     */
    private Set<Long> testItemIds;

    /**
     * 检验项目(检验目的)code
     */
    private Set<String> testItemCodes;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 送检机构id
     */
    private Set<Long> hspOrgIds;

    /**
     * 是否同一患者同一类型细菌只统计一次
     */
    private Boolean isStatisticsSampleType;


    /**
     * 默认当天
     */
    public void defaultDate() {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            final Date date = new Date();
            startDate = DateUtil.beginOfMonth(date);
            endDate = DateUtil.endOfDay(date);
        }

    }
}
