
package com.labway.lims.apply.service.chain.material.receive.invalid;

import com.labway.lims.api.enums.apply.MaterialReceiveRecordStatusEnum;
import com.labway.lims.apply.api.dto.MaterialReceiveRecordDto;
import com.labway.lims.apply.api.service.MaterialReceiveRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 领用作废 修改 领用记录
 *
 * <AUTHOR>
 * @since 2023/5/9 10:06
 */
@Slf4j
@Component
public class MaterialReceiveInvalidUpdateReceiveRecordCommand implements Command {

    @Resource
    private MaterialReceiveRecordService materialReceiveRecordService;

    @Override
    public boolean execute(Context context) throws Exception {
        final MaterialReceiveInvalidContext from = MaterialReceiveInvalidContext.from(context);
        var user = from.getUser();
        var receiveRecordDto = from.getMaterialReceiveRecordDto();

        // 修改领用状态、作废人、作废时间
        MaterialReceiveRecordDto updateReceiveRecordDto = new MaterialReceiveRecordDto();
        updateReceiveRecordDto.setReceiveId(receiveRecordDto.getReceiveId());
        updateReceiveRecordDto.setStatus(MaterialReceiveRecordStatusEnum.INVALID.getCode());
        updateReceiveRecordDto.setInvalidUserId(user.getUserId());
        updateReceiveRecordDto.setInvalidUserName(user.getNickname());
        updateReceiveRecordDto.setInvalidDate(new Date());

        materialReceiveRecordService.updateByReceiveId(updateReceiveRecordDto);

        return CONTINUE_PROCESSING;
    }
}
