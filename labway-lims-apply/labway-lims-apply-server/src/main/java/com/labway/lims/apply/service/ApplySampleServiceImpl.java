package com.labway.lims.apply.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.google.common.collect.Lists;
import com.labway.business.center.compare.request.compare.UpdateApplySampleInfoRequest;
import com.labway.business.center.compare.service.TbOrgApplySampleMainService;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.exception.apply.ApplySampleDisabledException;
import com.labway.lims.api.exception.apply.ApplySampleTerminateException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplySampleItemTwoPickDetailDto;
import com.labway.lims.apply.api.dto.ApplySampleOnePickDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.BloodOneSplitDto;
import com.labway.lims.apply.api.dto.DisableOrEnableItemDto;
import com.labway.lims.apply.api.dto.ImmunityTwoPickDto;
import com.labway.lims.apply.api.dto.LimbSampleDto;
import com.labway.lims.apply.api.dto.OnePickedApplySampleDto;
import com.labway.lims.apply.api.dto.OutsourcingApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SelectOutsourcingNotAuditSamplesDto;
import com.labway.lims.apply.api.dto.SplitBloodApplySampleDto;
import com.labway.lims.apply.api.dto.StopTestOrDisableSampleDto;
import com.labway.lims.apply.api.dto.TerminateItemDto;
import com.labway.lims.apply.api.dto.TodaySignedStatisticsDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.api.dto.TwoPickedApplySampleDto;
import com.labway.lims.apply.api.dto.WaitingOnePickApplySampleDto;
import com.labway.lims.apply.api.dto.WaitingSplitBloodApplySampleDto;
import com.labway.lims.apply.api.dto.WaitingTwoPickApplySampleDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.mapper.TbApplySampleMapper;
import com.labway.lims.apply.mapstruct.ApplySampleConverter;
import com.labway.lims.apply.model.TbApplySample;
import com.labway.lims.apply.service.chain.applysampleitem.disable.ApplySampleItemDisableChain;
import com.labway.lims.apply.service.chain.applysampleitem.disable.ApplySampleItemDisableContext;
import com.labway.lims.apply.service.chain.pick.one.OnePickChain;
import com.labway.lims.apply.service.chain.pick.one.OnePickContext;
import com.labway.lims.apply.service.chain.pick.one.cancel.CancelOnePickChain;
import com.labway.lims.apply.service.chain.pick.one.cancel.CancelOnePickContext;
import com.labway.lims.apply.service.chain.pick.two.TwoPickChain;
import com.labway.lims.apply.service.chain.pick.two.TwoPickCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickContext;
import com.labway.lims.apply.service.chain.pick.two.TwoPickRedisMarkCommand;
import com.labway.lims.apply.service.chain.pick.two.cancel.CancelTwoPickChain;
import com.labway.lims.apply.service.chain.pick.two.cancel.CancelTwoPickContext;
import com.labway.lims.apply.service.chain.pick.two.cancel.immunity.ImmunityCancelTwoPickChain;
import com.labway.lims.apply.service.chain.pick.two.immunity.ImmunityTwoPickChain;
import com.labway.lims.apply.service.chain.pick.two.immunity.ImmunityTwoPickContext;
import com.labway.lims.apply.service.chain.pick.two.multi.MultiTwoPickChain;
import com.labway.lims.apply.service.chain.pick.two.multi.MultiTwoPickContext;
import com.labway.lims.apply.service.chain.splitblood.CheckGroupHandoverTagCommand;
import com.labway.lims.apply.service.chain.splitblood.SplitBloodChain;
import com.labway.lims.apply.service.chain.splitblood.SplitBloodContext;
import com.labway.lims.apply.service.chain.splitblood.info.SplitBloodInfoChain;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentGroupTestItemService;
import com.labway.lims.base.api.service.RackService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import com.swak.frame.dto.Response;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.api.trace.TraceLog.EXCHANGE;
import static com.labway.lims.api.trace.TraceLog.ROUTING_KEY;
import static com.labway.lims.apply.api.dto.ApplySampleEventDto.SAMPLE_CHANGE_EXCHANGE;
import static com.labway.lims.apply.api.dto.ApplySampleEventDto.SAMPLE_CHANGE_KEY;

@Slf4j
@DubboService(interfaceClass = ApplySampleService.class)
public class ApplySampleServiceImpl extends ServiceImpl<TbApplySampleMapper, TbApplySample> implements ApplySampleService, InitializingBean {

    @Resource
    private ApplySampleConverter applySampleConverter;
    @Resource
    private TbApplySampleMapper applySampleMapper;
    @Resource
    private TwoPickChain twoPickChain;
    @Resource
    private ImmunityTwoPickChain immunityTwoPickChain;
    @Resource
    private MultiTwoPickChain multiTwoPickChain;
    @Resource
    private RackLogicService rackLogicService;
    @DubboReference
    private RackService rackService;
    @DubboReference
    private GroupService groupService;
    @Resource
    private TwoPickCommand twoPickCommand;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private CancelTwoPickChain cancelTwoPickChain;
    @Resource
    private ImmunityCancelTwoPickChain immunityCancelTwoPickChain;
    @Resource
    private CancelOnePickChain cancelOnePickChain;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private TestItemService testItemService;
    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentGroupTestItemService instrumentGroupTestItemService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private SplitBloodChain splitBloodChain;
    @Resource
    private SplitBloodInfoChain splitBloodInfoChain;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private TwoPickRedisMarkCommand twoPickRedisMarkCommand;
    @Resource
    private OnePickChain onePickChain;

    @DubboReference
    private GeneticsSampleService geneticsSampleService;
    @DubboReference
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private SpecialtySampleService specialtySampleService;
    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private BloodCultureSampleService bloodCultureSampleService;
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    private RabbitMQService rabbitMQService;
    @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;

    @Resource
    private EnvDetector envDetector;
    @Resource
    private CheckGroupHandoverTagCommand checkGroupHandoverTagCommand;

    @Resource
    private ApplySampleItemDisableChain applySampleItemDisableChain;

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    private final Map<ItemTypeEnum, IUpdateSampleNo> updateSampleNoMap = new EnumMap<>(ItemTypeEnum.class);

    @Override
    public List<ApplySampleDto> selectByBarcode(String barcode) {
        if (StringUtils.isBlank(barcode)) {
            return Collections.emptyList();
        }
        return selectByBarcodes(List.of(barcode));
    }

    @Override
    public List<ApplySampleDto> selectByOriginalBarcode(String originalBarcode) {
        if (StringUtils.isBlank(originalBarcode)) {
            return Collections.emptyList();
        }
        return applySampleMapper
                .selectList(new LambdaQueryWrapper<TbApplySample>().eq(TbApplySample::getOriginalBarcode, originalBarcode))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public int countByBarcode(String barcode) {
        if (StringUtils.isBlank(barcode)) {
            return NumberUtils.INTEGER_ZERO;
        }
        return Math.toIntExact(applySampleMapper
                .selectCount(new LambdaQueryWrapper<TbApplySample>().eq(TbApplySample::getBarcode, barcode)));
    }

    @Override
    public List<ApplySampleDto> selectByBarcodes(Collection<String> barcodes) {
        if (CollectionUtils.isEmpty(barcodes)) {
            return Collections.emptyList();
        }
        return applySampleMapper
                .selectList(new LambdaQueryWrapper<TbApplySample>().in(TbApplySample::getBarcode, barcodes)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    public ApplySampleDto selectByBarcodeAndGroupId(String barcode, long groupId) {
        if (StringUtils.isBlank(barcode)) {
            return null;
        }
        LambdaQueryWrapper<TbApplySample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbApplySample::getBarcode, barcode);
        queryWrapper.eq(TbApplySample::getGroupId, groupId);
        queryWrapper.eq(TbApplySample::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return convert(applySampleMapper.selectOne(queryWrapper));
    }

    @Nullable
    @Override
    public ApplySampleDto selectByApplySampleId(long applySampleId) {
        TbApplySample tbApplySample = applySampleMapper.selectById(applySampleId);
        return JSON.parseObject(JSON.toJSONString(tbApplySample), ApplySampleDto.class);
    }

    @Override
    public List<ApplySampleDto> selectByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        final Map<Long,
                ApplySampleDto> map = applySampleMapper
                .selectList(new LambdaQueryWrapper<TbApplySample>().in(TbApplySample::getApplySampleId, applySampleIds))
                .stream().map(this::convert).filter(Objects::nonNull)
                .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, v -> v, (a, b) -> a));
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyList();
        }

        // 保证原始顺序
        final List<ApplySampleDto> list = new ArrayList<>(map.size());
        for (Long applySampleId : applySampleIds) {
            if (map.containsKey(applySampleId)) {
                list.add(map.get(applySampleId));
            }
        }

        return list;
    }

    @Override
    public List<ApplySampleDto> selectByApplySampleIdsAndMerge(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        List<TbApplySample> tbApplySamples = applySampleMapper
                .selectAllByApplySampleIdsAndMasterBarCodeNotNull(applySampleIds);


        final Map<Long, ApplySampleDto> map = tbApplySamples.stream().map(this::convert).filter(Objects::nonNull)
                .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, v -> v, (a, b) -> a));
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyList();
        }

        // 保证原始顺序
        final List<ApplySampleDto> list = new ArrayList<>(map.size());
        for (Long applySampleId : applySampleIds) {
            if (map.containsKey(applySampleId)) {
                list.add(map.get(applySampleId));
            }
        }

        return list;
    }

    @Override
    public Map<Long, ApplySampleDto> selectByApplySampleIdsAsMap(Collection<Long> applySampleIds) {
        return selectByApplySampleIds(applySampleIds).stream()
                .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, c -> c, (a, b) -> a));
    }

    @Override
    public boolean updateByApplySampleId(ApplySampleDto dto) {

        final TbApplySample applySample = new TbApplySample();
        BeanUtils.copyProperties(dto, applySample);
        applySample.setUpdateDate(new Date());
        applySample.setUpdaterId(LoginUserHandler.get().getUserId());
        applySample.setUpdaterName(LoginUserHandler.get().getNickname());

        if (applySampleMapper.updateById(applySample) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改申请单样本 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(applySample));

        return true;

    }

    @Override
    public void updateByApplySampleIds(ApplySampleDto applySample, Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return;
        }
        applySampleMapper.updateByApplySampleIds(applySample, applySampleIds);
    }

    @Override
    public void updateInfoByApplySampleIds(ApplySampleDto applySample, Collection<Long> applySampleIds) {
        for (List<Long> item : Lists.partition(new ArrayList<>(applySampleIds), 500)) {
            final LambdaQueryWrapper<TbApplySample> in =
                    Wrappers.lambdaQuery(TbApplySample.class).in(TbApplySample::getApplySampleId, item);

            applySampleMapper.update(JSON.parseObject(JSON.toJSONString(applySample), TbApplySample.class), in);
        }
    }

    @Override
    public long addApplySample(ApplySampleDto dto) {
        final TbApplySample applySample = new TbApplySample();
        BeanUtils.copyProperties(dto, applySample);

        applySample
                .setApplySampleId(Optional.ofNullable(dto.getApplySampleId()).orElseGet(() -> snowflakeService.genId()));
        applySample.setCreateDate(new Date());
        applySample.setUpdateDate(new Date());
        applySample.setCreatorId(LoginUserHandler.get().getUserId());
        applySample.setCreatorName(LoginUserHandler.get().getNickname());
        applySample.setUpdaterId(LoginUserHandler.get().getUserId());
        applySample.setUpdaterName(LoginUserHandler.get().getNickname());
        applySample.setIsDelete(YesOrNoEnum.NO.getCode());

        if (applySampleMapper.insert(applySample) < 1) {
            throw new IllegalStateException("添加申请单样本失败");
        }

        log.info("用户 [{}] 添加申请单样本 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(applySample));

        return applySample.getApplySampleId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> splitBloodByApplySampleId(long applySampleId) {

        final SplitBloodContext context = new SplitBloodContext();
        context.setApplySampleId(applySampleId);

        try {

            if (!splitBloodChain.execute(context)) {
                throw new IllegalStateException("分血失败");
            }

            return context.getApplySampleIds();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }

            log.error("申请单样本 [{}] 分血失败", applySampleId, e);
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            log.info("申请单样本 [{}] 耗时\n{}", applySampleId, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }

    @Override
    public List<BloodOneSplitDto> splitBloodInfoByApplySampleId(long applySampleId) {
        final SplitBloodContext context = new SplitBloodContext();
        context.setApplySampleId(applySampleId);

        try {

            if (!splitBloodInfoChain.execute(context)) {
                throw new IllegalStateException("分血失败");
            }

            return context.getBloodOneSplitInfos();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }
            log.error("查询申请单样本 [{}] 分血信息失败", applySampleId, e);
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            log.info("查询申请单样本 [{}] 耗时\n{}", applySampleId, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    public List<ApplySampleDto> selectByApplyIds(Collection<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbApplySample> in =
                Wrappers.lambdaQuery(TbApplySample.class).in(TbApplySample::getApplyId, applyIds);

        return applySampleMapper.selectList(in).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<ApplySampleDto> selectByApplyId(long applyId) {
        return selectByApplyIds(List.of(applyId));
    }

    @Override
    public void addApplySamples(List<ApplySampleDto> applySamples) {
        if (CollectionUtils.isEmpty(applySamples)) {
            return;
        }

        final List<TbApplySample> samples = applySamples.stream().map(this::convert).collect(Collectors.toList());
        // 手工单外部条码是为空的情况下,就设置为空字符串
        String nickname = LoginUserHandler.get().getNickname();
        StringJoiner sj = new StringJoiner("\n");

        samples.forEach(sample -> {
            if (StringUtils.isEmpty(sample.getOutBarcode())) {
                sample.setOutBarcode(StringUtils.EMPTY);
            }
            sj.add(String.format("用户 [%s] 新增申请单样本 条码:[%s] 成功", nickname, sample.getBarcode()));
        });

        super.saveBatch(samples);

        log.info(sj.toString());

        //        for (final TbApplySample sample : samples) {
        //            applySampleMapper.insert(sample);
        //            log.info("用户 [{}] 新增申请单样本 [{}] 成功", LoginUserHandler.get().getNickname(), sample.getApplySampleId());
        //        }
    }

    @Override
    public boolean deleteByApplySampleId(long applySampleId) {
        if (applySampleMapper.deleteById(applySampleId) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除申请单样本 [{}] 成功", LoginUserHandler.get().getNickname(), applySampleId);
        return true;
    }

    @Override
    public void deleteByApplySampleIds(Collection<Long> applySampleIds) {

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return;
        }

        applySampleMapper.deleteBatchIds(applySampleIds);

        log.info("用户 [{}] 删除申请单样本 [{}] 成功", LoginUserHandler.get().getNickname(), applySampleIds);

    }

    @Override
    public List<RackLogicApplySampleDto> selectByRackLogicIds(Collection<Long> rackLogicIds) {

        if (CollectionUtils.isEmpty(rackLogicIds)) {
            return Collections.emptyList();
        }

        return applySampleMapper.selectByRackLogicIds(rackLogicIds);

    }

    @Override
    public List<RackLogicApplySampleDto> selectByRackIds(Collection<Long> rackIds) {

        if (CollectionUtils.isEmpty(rackIds)) {
            return Collections.emptyList();
        }

        return applySampleMapper.selectByRackIds(rackIds);
    }

    @Override
    public List<RackLogicApplySampleDto> selectByRackLogicId(long rackLogicId) {
        return selectByRackLogicIds(List.of(rackLogicId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOnePick(long applySampleId) {

        final CancelOnePickContext context = new CancelOnePickContext();
        context.setApplySampleId(applySampleId);

        try {
            if (!cancelOnePickChain.execute(context)) {
                throw new IllegalStateException("取消一次分拣失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("取消一次分拣 [{}] 耗时:\n{}", applySampleId, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTwoPick(String barcode) {

        final CancelTwoPickContext context = new CancelTwoPickContext();
        context.setBarcode(barcode);

        try {
            if (!cancelTwoPickChain.execute(context)) {
                throw new IllegalStateException("取消二次分拣失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("取消二次分拣 [{}] 耗时:\n{}", barcode, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTwoPick(String barcode, Long applySampleId) {
        final CancelTwoPickContext context = new CancelTwoPickContext();
        context.setBarcode(barcode);
        context.setApplySampleId(applySampleId);

        try {
            if (!immunityCancelTwoPickChain.execute(context)) {
                throw new IllegalStateException("取消二次分拣失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("取消二次分拣 [{}] 耗时:\n{}", barcode, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApplySampleOnePickDto onePick(long applySampleId) {

        final OnePickContext context = new OnePickContext(applySampleId);

        try {
            if (!onePickChain.execute(context)) {
                throw new IllegalStateException("一次分拣失败");
            }
        } catch (RuntimeException e) {
            log.error("一次分拣失败 [{}]", applySampleId, e);
            throw e;
        } catch (Exception e) {
            log.error("一次分拣失败 [{}]", applySampleId, e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("一次分拣 [{}] 耗时\n{}", applySampleId, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

        final ApplySampleOnePickDto applySampleOnePick = new ApplySampleOnePickDto();
        applySampleOnePick.setRow(context.getPoint().x);
        applySampleOnePick.setColumn(context.getPoint().y);
        applySampleOnePick.setRackId(context.getRackLogic().getRackId());
        applySampleOnePick.setRackLogicId(context.getRackLogic().getRackLogicId());
        applySampleOnePick.setApplySampleId(applySampleId);
        applySampleOnePick.setGroupName(context.getProfessionalGroup().getGroupName());
        applySampleOnePick.setGroupId(context.getProfessionalGroup().getGroupId());
        applySampleOnePick.setSpaces(context.getSpaces());

        return applySampleOnePick;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ApplySampleTwoPickDto> twoPick(long applySampleId, @Nullable String sampleNo,
                                               @Nullable String urgentSampleNo, Date twoPickDate, List<LimbSampleDto> limbSampleNos) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<ApplySampleTwoPickDto> twoPick(TwoPickDto tp) {
        final TwoPickContext context = new TwoPickContext(tp);
        try {

            if (!twoPickChain.execute(context)) {
                throw new IllegalStateException("二次分拣失败");
            }

            log.info("二次分拣成功 专业组 [{}] 操作人 [{}] 条码 [{}}", LoginUserHandler.get().getGroupName(),
                    LoginUserHandler.get().getNickname(), context.getApplySample().getBarcode());

            return context.getApplySampleTwoPicks();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }

            log.error("二次分拣失败 专业组 [{}] 操作人 [{}] 条码 [{}}", LoginUserHandler.get().getGroupName(),
                    LoginUserHandler.get().getNickname(), context.getApplySample().getBarcode(), e);

            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("二次分拣 [{}] 耗时\n{}", tp.getApplySampleId(), context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    public List<ApplySampleTwoPickDto> immunityTwoPick(ImmunityTwoPickDto tp) {
        final ImmunityTwoPickContext context = new ImmunityTwoPickContext(tp);
        try {

            if (!immunityTwoPickChain.execute(context)) {
                throw new IllegalStateException("免疫二次分拣失败");
            }

            log.info("免疫二次分拣成功 专业组 [{}] 操作人 [{}] 条码 [{}}", LoginUserHandler.get().getGroupName(),
                    LoginUserHandler.get().getNickname(), context.getApplySample().getBarcode());

            return context.getApplySampleTwoPicks();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }

            log.error("免疫二次分拣失败 专业组 [{}] 操作人 [{}] 条码 [{}}", LoginUserHandler.get().getGroupName(),
                    LoginUserHandler.get().getNickname(), context.getApplySample().getBarcode(), e);

            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("免疫二次分拣 [{}] 耗时\n{}", tp.getApplySampleId(), context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    /**
     * 不要在此方法上加事务 {@link MultiTwoPickChain} 类上已经加了
     */
    @Override
    public List<ApplySampleTwoPickDto> multiTwoPick(long rackLogicId, Long instrumentId) {
        final MultiTwoPickContext context = new MultiTwoPickContext(rackLogicId);
        context.setInstrumentId(instrumentId);
        try {

            if (!multiTwoPickChain.execute(context)) {
                throw new IllegalStateException("批量二次分拣失败");
            }

            log.info("批量二次分拣成功 专业组 [{}] 操作人 [{}] 试管架 [{}}", LoginUserHandler.get().getGroupName(),
                    LoginUserHandler.get().getNickname(), rackLogicId);

            // 当遇到错误时，要抛出。之所以不在 multiTwoPickChain 里面抛出是怕影响到事务的提交
            // 因为批量分拣时，如果分拣到一半出错了，已经分拣的不受影响
            // 所以遇到错误先保留一下，等事务提交之后再抛出错误
            if (Objects.nonNull(context.getException())) {
                // 将已分拣的样本返回出去
                throw context.getException().setData(context.getApplySampleTwoPicks().stream()
                        .filter(e -> StringUtils.isNotBlank(e.getSampleNo())).collect(Collectors.toList()));
            }

            return context.getApplySampleTwoPicks();
        } catch (Exception e) {

            log.error("批量二次分拣失败 专业组 [{}] 操作人 [{}] 试管架 [{}}", LoginUserHandler.get().getGroupName(),
                    LoginUserHandler.get().getNickname(), rackLogicId, e);

            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }

            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("批量二次分拣 [{}] 耗时\n{}", rackLogicId, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    public List<ApplySampleDto> selectBySplitDate(Date beginSplitDate, Date endSplitDate) {
        return applySampleMapper
                .selectList(new LambdaQueryWrapper<TbApplySample>().ge(TbApplySample::getSplitDate, beginSplitDate)
                        .le(TbApplySample::getSplitDate, endSplitDate)
                        .eq(TbApplySample::getIsSplitBlood, YesOrNoEnum.YES.getCode()))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<WaitingOnePickApplySampleDto> selectWaitingOnePickSamples(Date beginSignDate, Date endSignDate) {
        return applySampleMapper.selectWaitingOnePickSamples(beginSignDate, endSignDate,
                LoginUserHandler.get().getOrgId());
    }

    @Override
    public List<WaitingTwoPickApplySampleDto> selectWaitingTwoPickSamples(Date beginReceiveDate, Date endReceiveDate,
                                                                          String itemType) {
        // 排除项目类型
        List<String> excludeItemTypes = Lists.newArrayList();
        if (StringUtils.isBlank(itemType)) {
            // 不传 为默认 的 排除微生物、院感
            excludeItemTypes.add(ItemTypeEnum.INFECTION.name());
            excludeItemTypes.add(ItemTypeEnum.MICROBIOLOGY.name());
            excludeItemTypes.add(ItemTypeEnum.BLOOD_CULTURE.name());
        } else {
            // 避免非法 类型
            itemType = ItemTypeEnum.getByName(itemType).name();
        }
        return applySampleMapper.selectWaitingTwoPickSamples(beginReceiveDate, endReceiveDate,
                LoginUserHandler.get().getOrgId(), LoginUserHandler.get().getGroupId(), itemType, excludeItemTypes);
    }

    @Override
    public List<WaitingTwoPickApplySampleDto> selectWaitingTwoPickSamples(Date beginReceiveDate, Date endReceiveDate) {
        return applySampleMapper.selectWaitingTwoPickSamples(beginReceiveDate, endReceiveDate,
                LoginUserHandler.get().getOrgId(), LoginUserHandler.get().getGroupId(), StringUtils.EMPTY, List.of());
    }

    @Override
    public List<TwoPickedApplySampleDto> selectTwoPickedSamples(Date beginTwoPickedDate, Date endTwoPickedDate,
                                                                String itemType) {
        // 排除项目类型
        List<String> excludeItemTypes = Lists.newArrayList();
        if (StringUtils.isBlank(itemType)) {
            // 不传 为默认 的 排除微生物、院感
            excludeItemTypes.add(ItemTypeEnum.INFECTION.name());
            excludeItemTypes.add(ItemTypeEnum.MICROBIOLOGY.name());
        } else {
            // 避免非法 类型
            itemType = ItemTypeEnum.getByName(itemType).name();
        }
        return applySampleMapper.selectTwoPickedSamples(beginTwoPickedDate, endTwoPickedDate,
                LoginUserHandler.get().getOrgId(), LoginUserHandler.get().getGroupId(), itemType, excludeItemTypes);
    }

    @Override
    public List<TwoPickedApplySampleDto> selectTwoPickedSamples(Date beginTwoPickedDate, Date endTwoPickedDate) {
        return applySampleMapper.selectTwoPickedSamples(beginTwoPickedDate, endTwoPickedDate,
                LoginUserHandler.get().getOrgId(), LoginUserHandler.get().getGroupId(), StringUtils.EMPTY, List.of());
    }

    @Nullable
    @Override
    public ApplySampleTwoPickDto quicklyGetTwoPickInfoByApplySampleId(long applySampleId) {
        final Map<Long, ApplySampleTwoPickDto> map = quicklyGetTwoPickInfoByApplySampleIds(List.of(applySampleId));
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        return map.get(applySampleId);
    }

    @Override
    public Map<Long, ApplySampleTwoPickDto> quicklyGetTwoPickInfoByApplySampleIds(Collection<Long> applySampleIds) {

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyMap();
        }

        final List<Long> ids = new ArrayList<>(applySampleIds);

        final List<String> keys = ids.stream().map(twoPickRedisMarkCommand::getRedisKey).collect(Collectors.toList());

        final List<String> list = stringRedisTemplate.opsForValue().multiGet(keys);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        final Map<Long, ApplySampleTwoPickDto> map = new HashMap<>(list.size());

        for (int i = 0; i < keys.size(); i++) {
            if (StringUtils.isBlank(list.get(i))) {
                continue;
            }
            map.put(ids.get(i), JSON.parseObject(list.get(i), ApplySampleTwoPickDto.class));
        }

        return map;
    }

    @Override
    public List<WaitingSplitBloodApplySampleDto> selectWaitingSplitBloodSamples(Date beginReceiveDate,
                                                                                Date endReceiveDate, Long groupId) {
        return applySampleMapper.selectWaitingSplitBloodSamples(beginReceiveDate, endReceiveDate, groupId);
    }

    @Override
    public List<SplitBloodApplySampleDto> selectSplitBloodSamples(Date beginSplitDate, Date endSplitDate) {
        return applySampleMapper.selectSplitBloodSamples(beginSplitDate, endSplitDate,
                LoginUserHandler.get().getOrgId());
    }

    @Override
    public List<SplitBloodApplySampleDto> selectAfterSplitBloodSamples(Date beginSplitDate, Date endSplitDate) {
        return applySampleMapper.selectAfterSplitBloodSamples(beginSplitDate, endSplitDate,
                LoginUserHandler.get().getOrgId());
    }

    @Override
    public List<ApplySampleDto> selectNoOnePickSampleByApplyIds(Collection<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Collections.emptyList();
        }
        return applySampleMapper
                .selectList(Wrappers.lambdaQuery(TbApplySample.class).in(TbApplySample::getApplyId, applyIds)
                        .eq(TbApplySample::getIsOnePick, YesOrNoEnum.NO.getCode()))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<ApplySampleDto> selectNoStopTestSampleByApplyIds(Collection<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbApplySample> ne = Wrappers.lambdaQuery(TbApplySample.class)
                .in(TbApplySample::getApplyId, applyIds).ne(TbApplySample::getStatus, SampleStatusEnum.STOP_TEST.getCode());
        return applySampleMapper.selectList(ne).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Deprecated
    public List<OutsourcingApplySampleDto> selectOutsourcingUnPickApplySamples(Date beginReceiveDate,
                                                                               Date endReceiveDate) {
        final ProfessionalGroupDto group = groupService.selectOutsourcingGroup(LoginUserHandler.get().getOrgId());
        return applySampleMapper.selectOutsourcingUnPickApplySamples(beginReceiveDate, endReceiveDate,
                LoginUserHandler.get().getOrgId(), group.getGroupId());
    }

    @Override
    public List<OutsourcingApplySampleDto> selectOutsourcingUnPickApplySamples(Date beginReceiveDate,
                                                                               Date endReceiveDate,
                                                                               Long outsourcingGroupId) {
        return applySampleMapper.selectOutsourcingUnPickApplySamples(beginReceiveDate, endReceiveDate,
                LoginUserHandler.get().getOrgId(), outsourcingGroupId);
    }

    @Override
    public List<OutsourcingApplySampleDto> selectOutsourcingPickedApplySamples(Date beginPickDate, Date endPickDate) {
        return applySampleMapper.selectOutsourcingPickedApplySamples(beginPickDate, endPickDate,
                LoginUserHandler.get().getOrgId());
    }

    @Override
    public List<OutsourcingApplySampleDto> selectOutsourcingListSamples(Date beginPickDate, Date endPickDate, Integer showUnTwoPickSample) {
        return applySampleMapper.selectOutsourcingListSamples(beginPickDate, endPickDate,
                LoginUserHandler.get().getOrgId(), LoginUserHandler.get().getGroupId(), showUnTwoPickSample);
    }

    @Override
    public boolean isDisabled(long applySampleId) {
        final String value =
                stringRedisTemplate.opsForValue().get(redisPrefix.getBasePrefix() + "SAMPLE_DISABLE:" + applySampleId);
        if (Objects.isNull(value)) {
            return false;
        }

        return BooleanUtils.toBoolean(value);
    }

    @Override
    public boolean isTerminate(long applySampleId) {

        final String value =
                stringRedisTemplate.opsForValue().get(redisPrefix.getBasePrefix() + "SAMPLE_TERMINATE:" + applySampleId);
        if (Objects.isNull(value)) {
            return false;
        }

        return BooleanUtils.toBoolean(value);
    }

    @Override
    public List<Long> filterTerminateApplySampleIds(LinkedList<Long> applySampleIds) {
        LinkedList<String> collect = applySampleIds.stream().map(applySampleId -> redisPrefix.getBasePrefix() + "SAMPLE_TERMINATE:" + applySampleId).collect(Collectors.toCollection(LinkedList::new));
        List<String> strings = stringRedisTemplate.opsForValue().multiGet(collect);

        List<Long> noTerminateApplySampleIds = new ArrayList<>();
        for (int i = 0; i < collect.size(); i++) {
            if (Objects.nonNull(strings.get(i))) {
                noTerminateApplySampleIds.add(applySampleIds.get(i));
            }
        }
        return noTerminateApplySampleIds;
    }


    @Override
    public void regainTerminate(long applySampleId) {
        stringRedisTemplate.delete(redisPrefix.getBasePrefix() + "SAMPLE_TERMINATE:" + applySampleId);
    }

    @Override
    public void assertApplySampleUsability(long applySampleId) {
        if (isDisabled(applySampleId)) {
            throw new ApplySampleDisabledException("样本已禁用");
        } else if (isTerminate(applySampleId)) {
            throw new ApplySampleTerminateException("样本已经终止检验");
        }
    }

    @Override
    public void assertApplySampleUsability(Collection<Long> applySampleIds) {

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stopTest(StopTestOrDisableSampleDto param) {
        final List<Long> applySampleIds = param.getApplySampleIds();
        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalStateException("样本不存在");
        }

        for (Long applySampleId : param.getApplySampleIds()) {
            if (isTerminate(applySampleId)) {
                throw new ApplySampleTerminateException("样本已经终止检验");
            }
        }

        if (StringUtils.isAnyBlank(param.getCauseCode(), param.getCause())) {
            throw new IllegalStateException("终止原因不能为空");
        }

        StopWatch watch = new StopWatch(String.format("样本 %s 终止检验", applySampleIds));
        watch.start("查询样本");
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("样本不存在");
        }
        watch.stop();

        watch.start("查询试管架");
        // 获取到用到的试管架
        final Set<Long> rackIds = rackLogicSpaceService.selectByApplySampleIds(applySampleIds).stream()
                .map(RackLogicSpaceDto::getRackId).collect(Collectors.toSet());
        watch.stop();

        watch.start("样本参数校验");
        for (final ApplySampleDto applySample : applySamples) {

            final Long applySampleId = applySample.getApplySampleId();

            if (Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
                throw new IllegalStateException("样本已终止检验");
            }

            if (Objects.equals(applySample.getIsDisabled(), YesOrNoEnum.YES.getCode())) {
                throw new IllegalStateException("样本已禁用");
            }

            applySample.setStatus(SampleStatusEnum.STOP_TEST.getCode());

            if (BooleanUtils.isNotTrue(applySampleService.updateByApplySampleId(applySample))) {
                throw new IllegalStateException("操作失败");
            }

            log.info("用户 [{}] 终止检验成功 条码 [{}] 原因 [{}]", LoginUserHandler.get().getNickname(), applySampleId,
                    param.getCause());

        }
        watch.stop();

        // 并单的不释放样本号
        if (BooleanUtils.isNotTrue(param.getIsCombinedBill())) {
            watch.start("释放样本号");
            // 释放样本号
            final Map<Long, SampleDto> sampleMap = sampleService.selectByApplySampleIds(applySampleIds).stream()
                    .collect(Collectors.toMap(SampleDto::getApplySampleId, Function.identity(), (a, b) -> a));
            if (CollectionUtils.isNotEmpty(applySamples)) {
                for (ApplySampleDto applySample : applySamples) {
                    final SampleDto sample = sampleMap.get(applySample.getApplySampleId());
                    if (Objects.isNull(sample) || Objects.isNull(applySample.getTwoPickDate())) {
                        continue;
                    }

                    final String sampleNo = sample.getSampleNo();
                    final LocalDate twoPickDate =
                            applySample.getTwoPickDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    final TwoPickContext context = new TwoPickContext(new TwoPickDto());

                    // 修改样本号的时候加锁
                    try {
                        twoPickCommand.lockInstrumentGroup(context, sample.getInstrumentGroupId());
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e.getMessage(), e);
                    }

                    try {
                        // 将旧的样本号设置为失效
                        twoPickCommand.passiveSampleNo(applySample.getGroupId(), sampleNo, twoPickDate);
                    } finally {
                        twoPickCommand.unlockInstrumentGroup(context, sample.getInstrumentGroupId());
                    }
                }
            }
            watch.stop();
        }

        watch.start("释放试管架");
        // 释放试管架
        if (CollectionUtils.isNotEmpty(rackIds)) {
            final Map<Long, List<RackLogicSpaceDto>> map = rackLogicSpaceService.selectByRackIdsAsMap(rackIds);
            final Set<Long> removeRackIds = rackIds.stream().filter(e -> {
                // 如果试管架上没有样本了，就回收试管架
                List<RackLogicSpaceDto> rackLogicSpaces = map.get(e);
                if (CollectionUtils.isEmpty(rackLogicSpaces)) {
                    return true;
                }
                // 过滤掉当前样本如果还为空，就回收试管架
                rackLogicSpaces = rackLogicSpaces.stream().filter(f -> !applySampleIds.contains(f.getApplySampleId()))
                        .collect(Collectors.toList());
                return CollectionUtils.isEmpty(rackLogicSpaces);
            }).collect(Collectors.toSet());
            rackService.rackRecycle(removeRackIds);
        }
        watch.stop();

        watch.start("删除试管架占用");
        // 删除位置占用
        rackLogicSpaceService.deleteByApplySampleIds(applySampleIds);
        watch.stop();

        watch.start("条码环节");
        // 条码环节
        final LoginUserHandler.User user = LoginUserHandler.get();
        final LinkedList<Long> ids = snowflakeService.genIds(applySamples.size());
        final Date now = new Date();
        final List<SampleFlowDto> flows = applySamples.stream().map(m -> {
            SampleFlowDto flow = new SampleFlowDto();
            flow.setSampleFlowId(ids.pop());
            flow.setApplyId(m.getApplyId());
            flow.setApplySampleId(m.getApplySampleId());
            flow.setBarcode(m.getBarcode());
            flow.setOperateCode(BarcodeFlowEnum.STOP_TEST.name());
            flow.setOperateName(BarcodeFlowEnum.STOP_TEST.getDesc());
            flow.setOperator(user.getNickname());
            flow.setOperatorId(user.getUserId());
            flow.setContent(String.format("%s将条码 %s 终止检验", user.getNickname(), m.getBarcode()));
            flow.setCreateDate(now);
            flow.setUpdateDate(now);
            flow.setCreatorId(user.getUserId());
            flow.setCreatorName(user.getNickname());
            flow.setUpdaterId(user.getUserId());
            flow.setUpdaterName(user.getNickname());
            flow.setOrgId(user.getOrgId());
            flow.setOrgName(user.getOrgName());
            flow.setIsDelete(YesOrNoEnum.NO.getCode());
            return flow;
        }).collect(Collectors.toList());
        sampleFlowService.addSampleFlows(flows);
        watch.stop();

        watch.start("redis标记");
        // 批量标记
        final Map<String, String> keyMap =
                applySamples.stream().map(m -> redisPrefix.getBasePrefix() + "SAMPLE_TERMINATE:" + m.getApplySampleId())
                        .collect(Collectors.toMap(k -> k, v -> Boolean.TRUE.toString(), (k1, k2) -> k1));

        stringRedisTemplate.opsForValue().multiSet(keyMap);
        watch.stop();

        log.info(watch.prettyPrint(TimeUnit.SECONDS));

    }

    @Override
    public void disable(StopTestOrDisableSampleDto param) {
        final String cause = param.getCause();
        final String causeCode = param.getCauseCode();
        final List<Long> applySampleIds = param.getApplySampleIds();
        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalStateException("样本不能为空");
        }

        if (StringUtils.isAnyBlank(causeCode, cause)) {
            throw new IllegalStateException("禁用原因不能为空");
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("样本不存在");
        }

        for (final ApplySampleDto applySample : applySamples) {

            final Long applySampleId = applySample.getApplySampleId();

            if (Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
                throw new IllegalStateException("样本已终止检验");
            }

            if (Objects.equals(applySample.getIsDisabled(), YesOrNoEnum.YES.getCode())) {
                throw new IllegalStateException("样本已禁用");
            }

            applySample.setIsDisabled(YesOrNoEnum.YES.getCode());

            if (BooleanUtils.isNotTrue(applySampleService.updateByApplySampleId(applySample))) {
                throw new IllegalStateException("操作失败");
            }

            log.info("用户 [{}] 终止样本成功 条码 [{}] 原因 [{}]", LoginUserHandler.get().getNickname(), applySampleId, cause);
        }

        // 条码环节
        final LoginUserHandler.User user = LoginUserHandler.get();
        final LinkedList<Long> ids = snowflakeService.genIds(applySamples.size());
        final Date now = new Date();
        final List<SampleFlowDto> flows = applySamples.stream().map(m -> {
            SampleFlowDto flow = new SampleFlowDto();
            flow.setSampleFlowId(ids.pop());
            flow.setApplyId(m.getApplyId());
            flow.setApplySampleId(m.getApplySampleId());
            flow.setBarcode(m.getBarcode());
            flow.setOperateCode(BarcodeFlowEnum.DISABLE.name());
            flow.setOperateName(BarcodeFlowEnum.DISABLE.getDesc());
            flow.setOperator(user.getNickname());
            flow.setOperatorId(user.getUserId());
            flow.setContent(String.format("%s将条码 %s 禁用", user.getNickname(), m.getBarcode()));
            flow.setCreateDate(now);
            flow.setUpdateDate(now);
            flow.setCreatorId(user.getUserId());
            flow.setCreatorName(user.getNickname());
            flow.setUpdaterId(user.getUserId());
            flow.setUpdaterName(user.getNickname());
            flow.setOrgId(user.getOrgId());
            flow.setOrgName(user.getOrgName());
            flow.setIsDelete(YesOrNoEnum.NO.getCode());
            return flow;
        }).collect(Collectors.toList());
        sampleFlowService.addSampleFlows(flows);

        // 批量标记
        final Map<String, String> keyMap =
                applySamples.stream().map(m -> redisPrefix.getBasePrefix() + "SAMPLE_DISABLE:" + m.getApplySampleId())
                        .collect(Collectors.toMap(k -> k, v -> Boolean.TRUE.toString(), (k1, k2) -> k1));
        stringRedisTemplate.opsForValue().multiSet(keyMap);
    }

    @Override
    public void cancelDisable(List<Long> applySampleIds) {

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("样本不存在");
        }

        for (final ApplySampleDto applySample : applySamples) {

            if (Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
                throw new IllegalStateException("样本已终止检验");
            }

            if (Objects.equals(applySample.getIsDisabled(), YesOrNoEnum.NO.getCode())) {
                throw new IllegalStateException("样本未禁用");
            }

            applySample.setIsDisabled(YesOrNoEnum.NO.getCode());
            if (BooleanUtils.isNotTrue(applySampleService.updateByApplySampleId(applySample))) {
                throw new IllegalStateException("操作失败");
            }

            log.info("用户 [{}] 取消禁用成功 条码: [{}]", LoginUserHandler.get().getNickname(), applySample.getBarcode());
        }

        // 条码环节
        final LoginUserHandler.User user = LoginUserHandler.get();
        final LinkedList<Long> ids = snowflakeService.genIds(applySamples.size());
        final Date now = new Date();
        final List<SampleFlowDto> flows = applySamples.stream().map(m -> {
            SampleFlowDto flow = new SampleFlowDto();
            flow.setSampleFlowId(ids.pop());
            flow.setApplyId(m.getApplyId());
            flow.setApplySampleId(m.getApplySampleId());
            flow.setBarcode(m.getBarcode());
            flow.setOperateCode(BarcodeFlowEnum.CANCEL_DISABLE.name());
            flow.setOperateName(BarcodeFlowEnum.CANCEL_DISABLE.getDesc());
            flow.setOperator(user.getNickname());
            flow.setOperatorId(user.getUserId());
            flow.setContent(String.format("%s将条码 %s 取消禁用", user.getNickname(), m.getBarcode()));
            flow.setCreateDate(now);
            flow.setUpdateDate(now);
            flow.setCreatorId(user.getUserId());
            flow.setCreatorName(user.getNickname());
            flow.setUpdaterId(user.getUserId());
            flow.setUpdaterName(user.getNickname());
            flow.setOrgId(user.getOrgId());
            flow.setOrgName(user.getOrgName());
            flow.setIsDelete(YesOrNoEnum.NO.getCode());
            return flow;
        }).collect(Collectors.toList());
        sampleFlowService.addSampleFlows(flows);

        final Set<String> disableKey = applySampleIds.stream()
                .map(m -> redisPrefix.getBasePrefix() + "SAMPLE_DISABLE:" + m).collect(Collectors.toSet());
        stringRedisTemplate.delete(disableKey);

    }

    @Override
    public void itemDisable(DisableOrEnableItemDto param) {
        final ApplySampleItemDisableContext context = new ApplySampleItemDisableContext();
        param.setIsDisable(YesOrNoEnum.YES.getCode());
        context.setDisableOrEnableItemDto(param);
        try {
            if (!applySampleItemDisableChain.execute(context)) {
                throw new IllegalStateException("禁用操作失败");
            }
        } catch (Exception e) {
            log.error("禁用操作失败：", e);
            throw new LimsException(e.getMessage());
        }
    }

    @Override
    public void itemEnable(DisableOrEnableItemDto param) {
        final ApplySampleItemDisableContext context = new ApplySampleItemDisableContext();
        param.setIsDisable(YesOrNoEnum.NO.getCode());
        context.setDisableOrEnableItemDto(param);
        try {
            if (!applySampleItemDisableChain.execute(context)) {
                throw new IllegalStateException("禁用操作失败");
            }
        } catch (Exception e) {
            log.error("禁用操作失败：", e);
            throw new LimsException(e.getMessage());
        }
    }

    @Override
    public int countApplySampleAuditQuantity(long applyId) {
        final List<ApplySampleDto> applySamples = selectByApplyId(applyId);
        if (CollectionUtils.isEmpty(applySamples)) {
            return NumberUtils.INTEGER_ZERO;
        }

        // 过滤出已审核的样本 统计数量
        return Math.toIntExact(
                applySamples.stream().filter(f -> Objects.equals(f.getStatus(), SampleStatusEnum.AUDIT.getCode()))
                        .map(ApplySampleDto::getApplySampleId).count());
    }

    @Override
    public void deleteByApplyIds(Collection<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return;
        }

        applySampleMapper.delete(Wrappers.lambdaQuery(TbApplySample.class).in(TbApplySample::getApplyId, applyIds));

        log.info("用户 [{}] 专业组 [{}] 删除申请单样本成功 申请单ID: [{}]", LoginUserHandler.get().getNickname(),
                LoginUserHandler.get().getGroupName(), applyIds);
    }

    @Override
    public List<OnePickedApplySampleDto> selectOnePickedApplySamples(Date beginOnePickDate, Date endOnePickDate) {
        final Long orgId = LoginUserHandler.get().getOrgId();
        return applySampleMapper.selectOnePickedSamples(beginOnePickDate, endOnePickDate, orgId);
    }

    @Override
    public List<OnePickedApplySampleDto> selectAfterOnePickedSamples(Date beginOnePickDate, Date endOnePickDate) {
        final Long orgId = LoginUserHandler.get().getOrgId();
        return applySampleMapper.selectAfterOnePickedSamples(beginOnePickDate, endOnePickDate, orgId);
    }

    @Override
    public List<ApplySampleDto> selectByOutBarcodeAndHspOrgId(long hspOrgId, String outBarcode) {
        if (StringUtils.isBlank(outBarcode)) {
            return Collections.emptyList();
        }
        return applySampleMapper.selectByOutBarcodeAndHspOrgId(hspOrgId, outBarcode);
    }

    @Override
    public List<ApplySampleDto> selectByOutBarcodeAndHspOrgCode(String hspOrgCode, String outBarcode) {
        if (StringUtils.isBlank(outBarcode)) {
            return Collections.emptyList();
        }
        return applySampleMapper.selectByOutBarcodeAndHspOrgCode(hspOrgCode, outBarcode);
    }

    @Override
    public void changeSampleNo(long applySampleId, String sampleNo) {

        final String oldSampleNo;
        final ApplySampleTwoPickDto stp = new ApplySampleTwoPickDto();
        stp.setSampleNo(sampleNo);
        stp.setApplySampleId(applySampleId);

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (!Objects.equals(applySample.getIsTwoPick(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalStateException("样本尚未二次分拣");
        }

        final long sampleId;

        // 修改样本号
        switch (ItemTypeEnum.getByName(applySample.getItemType())) {
            case GENETICS: {
                final GeneticsSampleDto sample = geneticsSampleService.selectByApplySampleId(applySampleId);
                if (Objects.isNull(sample)) {
                    throw new IllegalStateException("遗传样本不存在");
                }
                stp.setInstrumentGroupName(sample.getInstrumentGroupName());
                stp.setInstrumentGroupId(sample.getInstrumentGroupId());
                oldSampleNo = sample.getSampleNo();
                sampleId = sample.getGeneticsSampleId();
                break;
            }
            case ROUTINE: {
                final SampleDto sample = sampleService.selectByApplySampleId(applySampleId);
                if (Objects.isNull(sample)) {
                    throw new IllegalStateException("样本不存在");
                }
                stp.setInstrumentGroupName(sample.getInstrumentGroupName());
                stp.setInstrumentGroupId(sample.getInstrumentGroupId());
                oldSampleNo = sample.getSampleNo();
                sampleId = sample.getSampleId();

                break;
            }
            case INFECTION: {
                final InfectionSampleDto sample = infectionSampleService.selectByApplySampleId(applySampleId);
                if (Objects.isNull(sample)) {
                    throw new IllegalStateException("院感样本不存在");
                }

                stp.setInstrumentGroupName(sample.getInstrumentGroupName());
                stp.setInstrumentGroupId(sample.getInstrumentGroupId());
                oldSampleNo = sample.getSampleNo();
                sampleId = sample.getInfectionSampleId();

                break;
            }
            case MICROBIOLOGY: {
                final MicrobiologySampleDto sample = microbiologySampleService.selectByApplySampleId(applySampleId);
                if (Objects.isNull(sample)) {
                    throw new IllegalStateException("微生物样本不存在");
                }

                stp.setInstrumentGroupName(sample.getInstrumentGroupName());
                stp.setInstrumentGroupId(sample.getInstrumentGroupId());
                oldSampleNo = sample.getSampleNo();
                sampleId = sample.getMicrobiologySampleId();

                break;
            }
            case SPECIALTY: {
                final SpecialtySampleDto sample = specialtySampleService.selectByApplySampleId(applySampleId);
                if (Objects.isNull(sample)) {
                    throw new IllegalStateException("特检样本不存在");
                }

                stp.setInstrumentGroupName(sample.getInstrumentGroupName());
                stp.setInstrumentGroupId(sample.getInstrumentGroupId());
                oldSampleNo = sample.getSampleNo();
                sampleId = sample.getSpecialtySampleId();

                break;
            }
            case OUTSOURCING: {
                final OutsourcingSampleDto sample = outsourcingSampleService.selectByApplySampleId(applySampleId);
                if (Objects.isNull(sample)) {
                    throw new IllegalStateException("外送样本不存在");
                }

                stp.setInstrumentGroupName(sample.getInstrumentGroupName());
                stp.setInstrumentGroupId(sample.getInstrumentGroupId());
                oldSampleNo = sample.getSampleNo();
                sampleId = sample.getOutsourcingSampleId();

                break;
            }
            case BLOOD_CULTURE: {
                final BloodCultureSampleDto sample = bloodCultureSampleService.selectByApplySampleId(applySampleId);
                if (Objects.isNull(sample)) {
                    throw new IllegalStateException("血培养样本不存在");
                }

                stp.setInstrumentGroupName(sample.getInstrumentGroupName());
                stp.setInstrumentGroupId(sample.getInstrumentGroupId());
                oldSampleNo = sample.getSampleNo();
                sampleId = sample.getBloodCultureSampleId();

                break;
            }
            default: {
                throw new IllegalStateException("样本尚未二次分拣");
            }
        }

        try {

            twoPickCommand.lockInstrumentGroup(new TwoPickContext(new TwoPickDto()), stp.getInstrumentGroupId());

            final LocalDate twoPickDate =
                    applySample.getTwoPickDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            // 再次判断是否存在
            if (!twoPickCommand.canActiveSampleNo(applySample.getGroupId(), sampleNo, twoPickDate) && BooleanUtils
                    .isFalse(Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode()))) {
                throw new IllegalStateException("当前样本号已存在，请重新输入");
            }

            // 修改样本号
            updateSampleNoMap.get(ItemTypeEnum.getByName(applySample.getItemType())).updateSampleNo(sampleId, sampleNo);

            // 将旧的样本号设置为失效
            twoPickCommand.passiveSampleNo(applySample.getGroupId(), oldSampleNo, twoPickDate);

            // 将新的样本号设置为有效
            twoPickCommand.activeSampleNo(applySample.getGroupId(), sampleNo, twoPickDate);

            // 重新标记
            twoPickRedisMarkCommand.mark(stp);

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            twoPickCommand.unlockInstrumentGroup(new TwoPickContext(new TwoPickDto()), stp.getInstrumentGroupId());
        }

        // 添加流水
        sampleFlowService.addSampleFlow(
                SampleFlowDto.builder().applyId(applySample.getApplyId()).applySampleId(applySample.getApplySampleId())
                        .barcode(applySample.getBarcode()).operateCode(BarcodeFlowEnum.UPDATE_SAMPLE_NO.name())
                        .operateName(BarcodeFlowEnum.UPDATE_SAMPLE_NO.getDesc())
                        .content(String.format("样本号从 [%s] 修改成 [%s]", oldSampleNo, sampleNo)).build());

    }

    @Override
    public void updateByApplyId(ApplySampleDto applySample) {
        if (Objects.isNull(applySample)) {
            return;
        }
        final Long applyId = applySample.getApplyId();
        if (Objects.isNull(applyId)) {
            return;
        }

        final LambdaQueryWrapper<TbApplySample> byApplyId =
                Wrappers.lambdaQuery(TbApplySample.class).eq(TbApplySample::getApplyId, applyId);

        applySampleMapper.update(JSON.parseObject(JSON.toJSONString(applySample), TbApplySample.class), byApplyId);
    }

    @Override
    public List<ApplySampleDto> selectByOutBarcodesAndHspOrgId(String hspOrgCode, Set<String> barcodes) {
        if (CollectionUtils.isEmpty(barcodes) || StringUtils.isBlank(hspOrgCode)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbApplySample> eq = Wrappers.lambdaQuery(TbApplySample.class)
                .in(TbApplySample::getOutBarcode, barcodes).eq(TbApplySample::getHspOrgCode, hspOrgCode);

        return applySampleMapper.selectList(eq).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminateItem(TerminateItemDto dto) {
        var applySampleItemIds = dto.getApplySampleItemIds();
        var causeCode = dto.getCauseCode();
        var cause = dto.getCause();
        var terminateType = dto.getTerminateType();
        LoginUserHandler.User user = LoginUserHandler.get();

        if (CollectionUtils.isEmpty(applySampleItemIds)) {
            throw new IllegalArgumentException("申请单样本检验项目不能为空");
        }
        if (StringUtils.isAnyBlank(cause, causeCode)) {
            throw new IllegalArgumentException("原因不能为空");
        }
        if (Objects.isNull(terminateType)) {
            throw new IllegalArgumentException("终止类型不能为空");
        }
        if (!(Objects.equals(terminateType, StopTestStatus.STOP_TEST_CHARGE)
                || Objects.equals(terminateType, StopTestStatus.STOP_TEST_FREE))) {
            throw new IllegalArgumentException("无效终止类型");
        }

        // key: 样本检验项目id value:样本检验项目
        Map<Long, ApplySampleItemDto> sampleItemDtoById = applySampleItemService.selectByIds(applySampleItemIds)
                .stream().collect(Collectors.toMap(ApplySampleItemDto::getApplySampleItemId, Function.identity()));
        applySampleItemIds.forEach(x -> {
            ApplySampleItemDto sampleItemDto = sampleItemDtoById.get(x);
            if (Objects.isNull(sampleItemDto)) {
                throw new IllegalStateException(String.format("[%s]对应样本检验项目不存在", x));
            }
            if (!Objects.equals(sampleItemDto.getStopStatus(), StopTestStatus.NO_STOP_TEST.getCode())) {
                throw new IllegalStateException(String.format("检验项目[%s]状态不为正常", sampleItemDto.getTestItemName()));
            }
        });
        // 对应所有申请单样本id
        Set<Long> applySampleIds =
                sampleItemDtoById.values().stream().map(ApplySampleItemDto::getApplySampleId).collect(Collectors.toSet());
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);
        applySampleDtos.forEach(e -> {
            if (StringUtils.isNotBlank(e.getMergeMasterBarcode())) {
                throw new IllegalStateException(String.format("[%s]对应样本检验项目已被并单无法终止检验", e.getBarcode()));
            }
            if (StringUtils.isNotBlank(e.getMergeExtraInfo())) {
                throw new IllegalStateException(String.format("[%s]对应样本检验项目已并单其他检验项目无法终止检验", e.getBarcode()));
            }
        });

        // key: 申请单样本id value : 对应检验id
        Map<Long, BaseSampleEsModelDto> esModelByApplySampleId =
                elasticSearchSampleService.selectSamples(SampleEsQuery.builder().applySampleIds(applySampleIds).build())
                        .stream().collect(Collectors.toMap(BaseSampleEsModelDto::getApplySampleId, Function.identity()));
        Set<Long> filterApplySampleIds = applySampleIds.stream()
                .filter(x -> Objects.isNull(esModelByApplySampleId.get(x))).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(filterApplySampleIds)) {
            throw new IllegalStateException(String.format("[%s]对应检验样本不存在",
                    filterApplySampleIds.stream().map(Object::toString).collect(Collectors.joining(","))));
        }
        if (applySampleIds.stream().anyMatch(
                x -> Objects.equals(esModelByApplySampleId.get(x).getSampleStatus(), SampleStatusEnum.AUDIT.getCode()))) {
            throw new IllegalStateException("样本已审核,不可终止");
        }

        // 需要删除 样本 下的报告项目 key: 对应各检验的id value : 需要删除的报告项目 关联的检验项目
        Map<Long, List<Long>> testItemIdsBySampleId = new HashMap<>();

        // 需要终止的 样本检验项目
        List<ApplySampleItemDto> applySampleItems = Lists.newArrayList();

        // 需要添加的 条码环节流水
        List<SampleFlowDto> flowDtoList = Lists.newArrayList();
        LinkedList<Long> genIds = snowflakeService.genIds(sampleItemDtoById.values().size());

        sampleItemDtoById.values().forEach(item -> {
            // 统计需要删除的 样本下的报告项目
            final BaseSampleEsModelDto baseSampleEsModelDto = esModelByApplySampleId.get(item.getApplySampleId());
            Long sampleId = esModelByApplySampleId.get(item.getApplySampleId()).getSampleId();
            if (Objects.nonNull(sampleId)) {
                // 还未到具体检验 ？？？
                List<Long> longs = testItemIdsBySampleId.computeIfAbsent(sampleId, k -> new ArrayList<>());
                longs.add(item.getTestItemId());
            }

            ApplySampleItemDto itemDto = new ApplySampleItemDto();
            itemDto.setApplySampleItemId(item.getApplySampleItemId());
            itemDto.setStopStatus(terminateType.getCode());
            itemDto.setStopReasonCode(causeCode);
            itemDto.setStopReasonName(cause);
            applySampleItems.add(itemDto);

            // 对应es 信息
            BaseSampleEsModelDto sampleEsModelDto = esModelByApplySampleId.get(item.getApplySampleId());

            SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(genIds.pop());
            sampleFlow.setApplyId(item.getApplyId());
            sampleFlow.setApplySampleId(item.getApplySampleId());
            sampleFlow.setBarcode(sampleEsModelDto.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.STOP_TEST.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.STOP_TEST.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(String.format("终止检验项目: [%s] ,终止类型: [%s] ,终止原因: [%s]", item.getTestItemName(),
                    terminateType.getDesc(), cause));

            flowDtoList.add(sampleFlow);

        });

        // 终止检验项目
        applySampleItemService.updateBatchById(applySampleItems);

        // 添加条码环节
        sampleFlowService.addSampleFlows(flowDtoList);

        // key: 申请单样本id value:申请单样本下 检验项目
        Map<Long, List<ApplySampleItemDto>> sampleItemDtosByApplySampleId =
                applySampleItemService.selectByApplySampleIds(applySampleIds, true).stream()
                        .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));
        // 获取 样本下检验项目 已全部终止检验了的 样本ids
        List<Long> stopTestApplySampleIds = applySampleIds.stream()
                .filter(x -> CollectionUtils.isEmpty(sampleItemDtosByApplySampleId.get(x))).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(stopTestApplySampleIds)) {

            // 终止检验-->样本维度
            StopTestOrDisableSampleDto param = new StopTestOrDisableSampleDto();
            param.setApplySampleIds(stopTestApplySampleIds);
            param.setCause(cause);
            param.setCauseCode(causeCode);
            param.setIsCombinedBill(dto.getIsCombinedBill());
            this.stopTest(param);

            // 并单不发mq
            if (BooleanUtils.isNotTrue(dto.getIsCombinedBill())) {
                stopTestApplySampleIds.forEach(x -> {
                    BaseSampleEsModelDto item = esModelByApplySampleId.get(x);
                    // 终止检验成功 状态需要回传业务中台 发送mq
                    final ApplySampleEventDto event = new ApplySampleEventDto();
                    event.setEvent(ApplySampleEventDto.EventType.TERMINATE);
                    event.setOrgId(user.getOrgId());
                    event.setHspOrgId(item.getHspOrgId());
                    event.setHspOrgCode(item.getHspOrgCode());
                    event.setHspOrgName(item.getHspOrgName());
                    event.setApplyId(item.getApplyId());
                    event.setApplySampleId(item.getApplySampleId());
                    event.setBarcode(item.getBarcode());
                    event.setExtras(Map.of("sampleId", String.valueOf(item.getSampleId()), "sampleNo",
                            StringUtils.defaultString(item.getSampleNo()), "businessCenterOrgCode",
                            envDetector.getBusinessCenterOrgCode()));
                    final String json = JSON.toJSONString(event);
                    rabbitMQService.convertAndSend(SAMPLE_CHANGE_EXCHANGE, SAMPLE_CHANGE_KEY, json);

                    log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", item.getApplySampleId(), item.getBarcode(), json,
                            EXCHANGE, ROUTING_KEY);
                });
            }
        }

        // 终止检验成功 将 这个检验项目 对应的危急值报告 删除
        if (!testItemIdsBySampleId.isEmpty()) {
            List<SampleReportItemDto> sampleReportItemDtos = sampleReportItemService.selectByTestItemIds(testItemIdsBySampleId);
            if (CollectionUtils.isNotEmpty(sampleReportItemDtos)) {

                List<Long> sampleIds = sampleReportItemDtos.stream().map(SampleReportItemDto::getSampleId).collect(Collectors.toList());

                // 查询是否存在危急值
                List<SampleCriticalResultDto> sampleCriticalResultDtos = sampleCriticalResultService.selectBySampleIds(sampleIds);
                if (CollectionUtils.isNotEmpty(sampleCriticalResultDtos)) {

                    List<SampleCriticalResultDto> deleteCriticalResults = sampleCriticalResultDtos.stream().filter(e -> {
                        return sampleReportItemDtos.stream().anyMatch(p -> Objects.equals(e.getSampleId(), p.getSampleId()) && Objects.equals(e.getReportItemCode(), p.getReportItemCode()));
                    }).collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(deleteCriticalResults)) {
                        sampleCriticalResultService.deleteByCriticalValueIds(deleteCriticalResults.stream().map(SampleCriticalResultDto::getCriticalValueId).collect(Collectors.toList()));
                    }

                }

            }
        }

        // 终止检验成功 将 这个检验项目 对应的报告项目 删除
        if (!testItemIdsBySampleId.isEmpty()) {
            sampleReportItemService.deleteByTestItemIds(testItemIdsBySampleId);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void regainTerminateBarcode(Set<Long> applySampleItemIds) {
        List<ApplySampleItemDto> sampleItemDtoList = applySampleItemService.selectByIds(applySampleItemIds);
        if (CollectionUtils.isEmpty(sampleItemDtoList)) {
            throw new IllegalArgumentException("请选择有效的项目");
        }
        // key: 申请单样本id value: 该样本下检验项目
        Map<Long, List<ApplySampleItemDto>> sampleItemBySampleId =
                sampleItemDtoList.stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));
        if (sampleItemBySampleId.values().stream().anyMatch(itemList -> itemList.size() != itemList.stream()
                .map(ApplySampleItemDto::getTestItemId).distinct().count())) {
            throw new IllegalStateException("不可恢复相同的检验项目");
        }

        LoginUserHandler.User user = LoginUserHandler.get();
        // 对应的申请单样本
        List<ApplySampleDto> sampleDtoList = applySampleService.selectByApplySampleIds(sampleItemBySampleId.keySet());
        sampleDtoList.forEach(e -> {
            if (StringUtils.isNotBlank(e.getMergeMasterBarcode())) {
                throw new IllegalStateException(String.format("[%s]对应样本检验项目已被并单无法恢复检验", e));
            }
        });
        if (sampleDtoList.stream()
                .anyMatch(obj -> !Objects.equals(obj.getStatus(), SampleStatusEnum.STOP_TEST.getCode()))) {
            throw new IllegalStateException("请选择已终止样本");
        }

        // 判断是否是分血后组间交接
        Optional.ofNullable(sampleDtoList.iterator().next()).ifPresent(e -> {
            if (this.isGroupHandoverByBarcode(e)) {
                throw new IllegalStateException("组间交接样本无法恢复终止检验");
            }
        });

        if (sampleDtoList.stream().anyMatch(e -> Objects.equals(YesOrNoEnum.YES.getCode(), e.getIsImmunityTwoPick()))) {
            throw new IllegalStateException("免疫二次分拣的样本无法恢复终止检验");
        }

        Map<Long, ApplySampleDto> sampleById =
                sampleDtoList.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));

        // 恢复 样本检验项目
        List<ApplySampleItemDto> updateSampleItems = Lists.newArrayList();
        sampleItemDtoList.forEach(item -> {
            ApplySampleItemDto itemDto = new ApplySampleItemDto();
            itemDto.setApplySampleItemId(item.getApplySampleItemId());
            itemDto.setStopStatus(StopTestStatus.NO_STOP_TEST.getCode());
            itemDto.setStopReasonCode(StringUtils.EMPTY);
            itemDto.setStopReasonName(StringUtils.EMPTY);
            updateSampleItems.add(itemDto);
        });
        applySampleItemService.updateBatchById(updateSampleItems);

        // 删除 终止检验redis
        sampleItemBySampleId.keySet().forEach(this::regainTerminate);

        // 已经处理的 条码
        List<Long> applySampleIds = Lists.newArrayList();
        List<String> barcodes = Lists.newArrayList();
        /*boolean reload = false;
        // 免疫二次分拣的先取消免疫二次分拣
        for (ApplySampleDto applySampleDto : sampleDtoList) {
            if (Objects.equals(applySampleDto.getIsTwoPick(), YesOrNoEnum.YES.getCode()) &&
                    applySampleService.selectByBarcode(applySampleDto.getBarcode()).stream()
                            .anyMatch(e -> Objects.equals(YesOrNoEnum.YES.getCode(), e.getIsImmunityTwoPick()))) {
                // 条码 未处理
                final CancelTwoPickContext cancelTwoPickContext = new CancelTwoPickContext();
                cancelTwoPickContext.setBarcode(applySampleDto.getBarcode());
                cancelTwoPickContext.setApplySampleId(applySampleDto.getApplySampleId());
                cancelTwoPickContext.setRegainTerminate(true);
                // 要保留的申请单样本
                cancelTwoPickContext.setAliveApplySampleId(applySampleDto.getApplySampleId());
                try {
                    if (!immunityCancelTwoPickChain.execute(cancelTwoPickContext)) {
                        throw new IllegalStateException("取消二次分拣失败");
                    }
                } catch (Exception e) {
                    log.info("失败原因:{}", e.getMessage());
                    throw new IllegalStateException("恢复终止样本失败请稍后重试");
                }
                barcodes.add(applySampleDto.getBarcode());
                reload = true;
            }
        }
        if (reload) {
            sampleDtoList = applySampleService.selectByApplySampleIds(sampleItemBySampleId.keySet());
        }*/
        sampleDtoList.forEach(item -> {
            Long applySampleId = item.getApplySampleId();
            String barcode = item.getBarcode();
            if (applySampleIds.contains(applySampleId)) {
                // 已经处理 了
                return;
            }
            if (Objects.equals(item.getIsTwoPick(), YesOrNoEnum.NO.getCode())) {
                // 没有二次分拣
                if (Objects.equals(item.getIsOnePick(), YesOrNoEnum.YES.getCode())) {
                    // 已经 一次分拣 需要取消一次分拣
                    final CancelOnePickContext cancelOnePickContext = new CancelOnePickContext();
                    cancelOnePickContext.setApplySampleId(applySampleId);
                    cancelOnePickContext.setRegainTerminate(true);
                    try {
                        if (!cancelOnePickChain.execute(cancelOnePickContext)) {
                            throw new IllegalStateException("取消一次分拣失败");
                        }
                    } catch (Exception e) {
                        log.info("失败原因:{}", e.getMessage());
                        throw new IllegalStateException("恢复终止样本失败请稍后重试");
                    }
                }

            } else {
                // 已经二次分拣 先取消二次分拣
                if (!barcodes.contains(barcode)) {
                    // 条码 未处理
                    final CancelTwoPickContext cancelTwoPickContext = new CancelTwoPickContext();
                    cancelTwoPickContext.setBarcode(barcode);
                    cancelTwoPickContext.setRegainTerminate(true);
                    // 要保留的申请单样本
                    cancelTwoPickContext.setAliveApplySampleId(applySampleId);
                    try {
                        if (!cancelTwoPickChain.execute(cancelTwoPickContext)) {
                            throw new IllegalStateException("取消二次分拣失败");
                        }
                    } catch (Exception e) {
                        log.info("失败原因:{}", e.getMessage());
                        throw new IllegalStateException("恢复终止样本失败请稍后重试");
                    }
                }

                // 再 取消一次分拣
                final CancelOnePickContext cancelOnePickContext = new CancelOnePickContext();
                cancelOnePickContext.setApplySampleId(applySampleId);
                cancelOnePickContext.setRegainTerminate(true);
                try {
                    if (!cancelOnePickChain.execute(cancelOnePickContext)) {
                        throw new IllegalStateException("取消一次分拣失败");
                    }
                } catch (Exception e) {
                    log.info("失败原因:{}", e.getMessage());
                    throw new IllegalStateException("恢复终止样本失败请稍后重试");
                }
            }
            applySampleIds.add(applySampleId);
            barcodes.add(barcode);
        });
        // 恢复 样本
        ApplySampleDto updateSampleDto = new ApplySampleDto();
        updateSampleDto.setGroupId(0l);
        updateSampleDto.setGroupName(StringUtils.EMPTY);
        updateSampleDto.setRackId(0L);
        updateSampleDto.setStatus(SampleStatusEnum.ENTER.getCode());

        applySampleService.updateByApplySampleIds(updateSampleDto, sampleItemBySampleId.keySet());

        // 需要添加的 条码环节流水
        List<SampleFlowDto> flowDtoList = Lists.newArrayList();
        LinkedList<Long> genIds = snowflakeService.genIds(sampleItemBySampleId.size());
        for (Map.Entry<Long, List<ApplySampleItemDto>> entry : sampleItemBySampleId.entrySet()) {
            ApplySampleDto item = sampleById.get(entry.getKey());
            if (Objects.isNull(item)) {
                // 无效样本？？？
                continue;
            }
            String testItemName =
                    entry.getValue().stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.joining(","));
            SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(genIds.pop());
            sampleFlow.setApplyId(item.getApplyId());
            sampleFlow.setApplySampleId(item.getApplySampleId());
            sampleFlow.setBarcode(item.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.STOP_TEST_REGAIN.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.STOP_TEST_REGAIN.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(String.format("恢复条码号 [%s],样本 [%s] 下检验项目: [%s] ", item.getBarcode(),
                    item.getApplySampleId(), testItemName));

            flowDtoList.add(sampleFlow);
        }

        // 添加条码环节
        sampleFlowService.addSampleFlows(flowDtoList);
    }

    @Override
    public List<ApplySampleDto> selectByCreateDate(Date beginDate, Date endDate) {
        return applySampleMapper.selectList(new LambdaQueryWrapper<TbApplySample>()
                        .ge(TbApplySample::getCreateDate, beginDate).le(TbApplySample::getCreateDate, endDate)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<ApplySampleDto> selectOutsourcingNotAuditApplySamples(SelectOutsourcingNotAuditSamplesDto selectParam) {
        LambdaQueryWrapper<TbApplySample> wrapper = new LambdaQueryWrapper<TbApplySample>()
                .eq(TbApplySample::getIsDelete, YesOrNoEnum.NO.getCode())
                .eq(TbApplySample::getIsOutsourcing, selectParam.getIsOutsourcing())
                .ne(TbApplySample::getStatus, selectParam.getExcludeStatus());
        return applySampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    /**
     * 根据applyId去修改送检机构
     *
     * @param apply
     */
    @Override
    public void updateHospByApplyId(ApplyDto apply) {
        LoginUserHandler.User user = LoginUserHandler.get();
        String nickname = user.getNickname();
        Long userId = user.getUserId();
        Date date = new Date();

        LambdaUpdateWrapper<TbApplySample> wrapper = Wrappers.lambdaUpdate(TbApplySample.class)
                .eq(TbApplySample::getApplyId, apply.getApplyId())
                .eq(TbApplySample::getIsDelete, 0)
                .set(TbApplySample::getHspOrgCode, apply.getHspOrgCode())
                .set(TbApplySample::getHspOrgName, apply.getHspOrgName())
                .set(TbApplySample::getUpdaterId, userId)
                .set(TbApplySample::getUpdaterName, nickname)
                .set(TbApplySample::getUpdateDate, date);
        applySampleMapper.update(null, wrapper);
    }

    @Override
    public void updateOutBarcodeByApplyId(Long applyId, String outBarcode) {
        LambdaUpdateWrapper<TbApplySample> wrapper = Wrappers.lambdaUpdate(TbApplySample.class)
                .eq(TbApplySample::getApplyId, applyId)
                .set(TbApplySample::getOutBarcode, outBarcode);
        applySampleMapper.update(null, wrapper);
    }

    /**
     * 根据applySampleId批量更新
     *
     * @param applySampleIds
     * @param barcode
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBatchByIds(Collection<Long> applySampleIds, String barcode) {

        LoginUserHandler.User user = LoginUserHandler.get();

        List<TbApplySample> tbApplySamples = applySampleIds.stream().map(e -> {
            TbApplySample t = new TbApplySample();
            t.setApplySampleId(e);
            t.setMergeMasterBarcode(barcode);
            t.setUpdaterId(user.getUserId());
            t.setUpdaterName(user.getNickname());
            t.setUpdateDate(new Date());
            t.setIsDelete(YesOrNoEnum.NO.getCode());
            return t;
        }).collect(Collectors.toList());

        Db.updateBatchById(tbApplySamples);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateExtraInfoByApplySampleId(Long applySampleId, String extraInfo) {
        LambdaUpdateWrapper<TbApplySample> wrapper = Wrappers.lambdaUpdate(TbApplySample.class)
                .eq(TbApplySample::getApplySampleId, applySampleId)
                .set(TbApplySample::getMergeExtraInfo, extraInfo);
        applySampleMapper.update(null, wrapper);
    }

    @Override
    public void updateOneAuditStatusByApplySampleIds(List<Long> applySampleIds) {
        LambdaUpdateWrapper<TbApplySample> wrapper = Wrappers.lambdaUpdate(TbApplySample.class)
                .in(TbApplySample::getApplySampleId, applySampleIds)
                .set(TbApplySample::getStatus, SampleStatusEnum.ONE_AUDIT.getCode())
                .set(TbApplySample::getMergeMasterBarcode, Strings.EMPTY);
        applySampleMapper.update(null, wrapper);
    }

    @Override
    public List<ApplySampleDto> selectMergeByBarcodes(String mergeMasterBarcode) {
        LambdaUpdateWrapper<TbApplySample> wrapper = Wrappers.lambdaUpdate(TbApplySample.class)
                .in(TbApplySample::getMergeMasterBarcode, mergeMasterBarcode);
        return applySampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<ApplySampleItemTwoPickDetailDto> selectApplySampleItemTwoPickDetailByBarcode(String barcode) {
        if (StringUtils.isBlank(barcode)) {
            return List.of();
        }
        return applySampleMapper.selectApplySampleItemTwoPickDetailByBarcode(barcode);
    }

    /**
     * @param applySampleDto 申请单样本
     *                       <a href="https://www.tapd.cn/59091617/prong/stories/view/1159091617001001750">如果判断不能分血， 则是组间交接</a>
     * @see CheckGroupHandoverTagCommand
     */
    @Override
    public boolean isGroupHandoverByBarcode(ApplySampleDto applySampleDto) {
        // 是分血
        return Objects.equals(YesOrNoEnum.YES.getCode(), applySampleDto.getIsSplitBlood())
                // 有不可分血标记（组间交接）
                && (checkGroupHandoverTagCommand.existsGroupHandover(applySampleDto.getBarcode()));
    }

    @Override
    public List<TodaySignedStatisticsDto> signStatisticsToday() {
        DateTime today = DateUtil.date();
        List<ApplySampleDto> applySampleDtos = applySampleMapper.selectTodaySignedApplySamples(
                DateUtil.beginOfDay(today), DateUtil.endOfDay(today), LoginUserHandler.get().getOrgId());

        if (CollectionUtils.isEmpty(applySampleDtos)) {
            return Collections.emptyList();
        }

        return applySampleDtos.stream()
                .filter(e -> StringUtils.isNotBlank(e.getOutBarcode()))
                .collect(Collectors.groupingBy(e -> Pair.of(e.getHspOrgCode(), e.getHspOrgName())))
                .entrySet().stream().map(entry -> {
                    Pair<String, String> hspOrgInfo = entry.getKey();
                    String hspOrgCode = hspOrgInfo.getKey();
                    String hspOrgName = hspOrgInfo.getValue();

                    long signCount = entry.getValue().stream().map(ApplySampleDto::getOutBarcode).distinct().count();
                    long printCount = entry.getValue().stream().map(ApplySampleDto::getBarcode).distinct().count();

                    return new TodaySignedStatisticsDto(hspOrgCode, hspOrgName, signCount, printCount);
                }).sorted(Comparator.comparing(TodaySignedStatisticsDto::getPrintCount).reversed()
                        .thenComparing(TodaySignedStatisticsDto::getHspOrgCode)).collect(Collectors.toList());
    }

    /**
     * 根据applySampleIds更新 加急状态
     *
     * @param update2UrgentSampleIds
     * @param urgentEnum
     */
    @Override
    public void update2UrgentByApplySampleIds(Collection<Long> update2UrgentSampleIds, UrgentEnum urgentEnum) {
        if (CollectionUtils.isEmpty(update2UrgentSampleIds)) {
            return;
        }
        LambdaUpdateWrapper<TbApplySample> wrapper = Wrappers.lambdaUpdate(TbApplySample.class)
                .in(TbApplySample::getApplySampleId, update2UrgentSampleIds)
                .set(TbApplySample::getUrgent, urgentEnum.getCode());
        applySampleMapper.update(null, wrapper);
    }

	private ApplySampleDto convert(TbApplySample sample) {
        return applySampleConverter.convert(sample);
    }

    private TbApplySample convert(ApplySampleDto sample) {
        return applySampleConverter.convert(sample);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        final Map<ItemTypeEnum, IUpdateSampleNo> map = updateSampleNoMap;
        map.put(ItemTypeEnum.GENETICS, (sampleId, sampleNo) -> {
            final GeneticsSampleDto g = new GeneticsSampleDto();
            g.setGeneticsSampleId(sampleId);
            g.setSampleNo(sampleNo);
            geneticsSampleService.updateByGeneticsSampleId(g);
        });
        map.put(ItemTypeEnum.ROUTINE, (sampleId, sampleNo) -> {
            final SampleDto g = new SampleDto();
            g.setSampleId(sampleId);
            g.setSampleNo(sampleNo);
            sampleService.updateBySampleId(g);
        });
        map.put(ItemTypeEnum.INFECTION, (sampleId, sampleNo) -> {
            final InfectionSampleDto g = new InfectionSampleDto();
            g.setInfectionSampleId(sampleId);
            g.setSampleNo(sampleNo);
            infectionSampleService.updateByInfectionSampleId(g);
        });
        map.put(ItemTypeEnum.OUTSOURCING, (sampleId, sampleNo) -> {
            final OutsourcingSampleDto g = new OutsourcingSampleDto();
            g.setOutsourcingSampleId(sampleId);
            g.setSampleNo(sampleNo);
            outsourcingSampleService.updateByOutsourcingSampleId(g);
        });
        map.put(ItemTypeEnum.MICROBIOLOGY, (sampleId, sampleNo) -> {
            final MicrobiologySampleDto g = new MicrobiologySampleDto();
            g.setMicrobiologySampleId(sampleId);
            g.setSampleNo(sampleNo);
            microbiologySampleService.updateByMicrobiologySampleId(g);
        });
        map.put(ItemTypeEnum.SPECIALTY, (sampleId, sampleNo) -> {
            final SpecialtySampleDto g = new SpecialtySampleDto();
            g.setSpecialtySampleId(sampleId);
            g.setSampleNo(sampleNo);
            specialtySampleService.updateBySpecialtySampleId(g);
        });
        map.put(ItemTypeEnum.BLOOD_CULTURE, (sampleId, sampleNo) -> {
            final BloodCultureSampleDto g = new BloodCultureSampleDto();
            g.setBloodCultureSampleId(sampleId);
            g.setSampleNo(sampleNo);
            bloodCultureSampleService.updateByBloodCultureSampleId(g);
        });

    }

    private interface IUpdateSampleNo {
        void updateSampleNo(long sampleId, String sampleNo);
    }

    @Resource
    private TbOrgApplySampleMainService tbOrgApplySampleMainService;

    @Override
    public void syncApplySampleToBusinessCenter(Long applyId) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        threadPoolConfig.getPool().execute(() -> {
            if (Objects.isNull(applyId)) {
                return;
            }

            final ApplyDto applyDto = applyService.selectByApplyId(applyId);
            if (Objects.isNull(applyDto)) {
                log.error("通知业务中台更新申请单信息 根据applyId查询申请单信息为空，applyId {}", applyId);
                return;
            }

            final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplyId(applyId);
            if (CollectionUtils.isEmpty(applySampleDtos)) {
                log.error("通知业务中台更新申请单信息 根据applyId查询申请单信息为空，applyId {}", applyId);
                return;
            }

            final Map<Long, List<ApplySampleItemDto>> applySampleIdsAsMap =
                    applySampleItemService.selectByApplySampleIdsAsMap(applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList()));

            // 调用业务中台更新申请单信息  因为是根据applyId查询的，所有只会有一条数据
            for (ApplySampleDto applySample : applySampleDtos) {
                final List<ApplySampleItemDto> applySampleItemDtos =
                        applySampleIdsAsMap.getOrDefault(applySample.getApplySampleId(), Collections.emptyList());
                // 判断是否是病理样本
                if (CollectionUtils.isEmpty(applySampleItemDtos)
                        || applySampleItemDtos.stream().noneMatch(e -> ItemTypeEnum.PATHOLOGY.name().equals(e.getItemType()))) {
                    log.info("通知业务中台更新申请单信息 当前申请单没有病理项目，不进行业务中台同步，申请单id：{},条码号：{}", applyId, applySample.getBarcode());
                    continue;
                }

                Response<?> response = tbOrgApplySampleMainService.updateApplySampleInfo(convertSampleInfo(applyDto, applySample, user));
                if (!response.isSuccess()) {
                    log.error("通知业务中台更新申请单信息失败,申请单id：{},条码号：{},原因：{}", applyId, applySample.getBarcode(), response.getMsg());
                }
                log.info("通知业务中台更新申请单信息成功,申请单id：{}, 条码号：{}", applyId, applySample.getBarcode());
            }
        });
    }

    // 申请单信息转换
    private UpdateApplySampleInfoRequest convertSampleInfo(ApplyDto apply, ApplySampleDto applySample, LoginUserHandler.User user) {
        UpdateApplySampleInfoRequest updateApplySampleInfoRequest = new UpdateApplySampleInfoRequest();
        updateApplySampleInfoRequest.setBarcode(applySample.getOutBarcode());
        updateApplySampleInfoRequest.setHspOrgCode(applySample.getHspOrgCode());
        updateApplySampleInfoRequest.setHspOrgName(applySample.getHspOrgName());
        updateApplySampleInfoRequest.setApplyType(apply.getApplyTypeName());
        updateApplySampleInfoRequest.setPatientVisitCard(apply.getPatientVisitCard());
        updateApplySampleInfoRequest.setUrgent(apply.getUrgent());
        updateApplySampleInfoRequest.setSampleType(applySample.getSampleTypeName());
        updateApplySampleInfoRequest.setSampleProperty(applySample.getSampleProperty());
        updateApplySampleInfoRequest.setDept(apply.getDept());
        // updateApplySampleInfoRequest.setInpatientArea(applySample.geta);
        updateApplySampleInfoRequest.setPatientName(apply.getPatientName());
        updateApplySampleInfoRequest.setPatientSex(apply.getPatientSex());
        updateApplySampleInfoRequest.setPatientAge(apply.getPatientAge());
        updateApplySampleInfoRequest.setPatientSubage(apply.getPatientSubage());
        updateApplySampleInfoRequest.setPatientSubageUnit(apply.getPatientSubageUnit());
        updateApplySampleInfoRequest.setPatientBirthday(apply.getPatientBirthday());
        updateApplySampleInfoRequest.setPatientBed(apply.getPatientBed());
        updateApplySampleInfoRequest.setClinicalDiagnosis(apply.getDiagnosis());
        updateApplySampleInfoRequest.setPatientCard(apply.getPatientCard());
        updateApplySampleInfoRequest.setPatientCardType(apply.getPatientCardType());
        updateApplySampleInfoRequest.setPatientAsddress(apply.getPatientAddress());
        updateApplySampleInfoRequest.setPatientMobile(apply.getPatientMobile());
        updateApplySampleInfoRequest.setSendDoctor(apply.getSendDoctorName());
        updateApplySampleInfoRequest.setApplyDate(apply.getApplyDate());
        updateApplySampleInfoRequest.setSamplingDate(apply.getSamplingDate());
        updateApplySampleInfoRequest.setRemark(apply.getRemark());
        updateApplySampleInfoRequest.setTubeType(applySample.getTubeName());
        // updateApplySampleInfoRequest.setVisitCardNo(apply.getPatientVisitCard());
        updateApplySampleInfoRequest.setSampleNum(apply.getSampleCount());

        updateApplySampleInfoRequest.setSignBarcode(applySample.getBarcode());
        updateApplySampleInfoRequest.setSignOrgCode(envDetector.getBusinessCenterOrgCode());
        updateApplySampleInfoRequest.setLimsUserCode(user.getUsername());
        updateApplySampleInfoRequest.setLimsUserName(user.getNickname());
        updateApplySampleInfoRequest.setPatientPart(applySample.getPatientPart());

        return updateApplySampleInfoRequest;
    }

}
