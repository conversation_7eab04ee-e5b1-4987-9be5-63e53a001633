package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDto;
import com.labway.lims.apply.api.dto.QueryApprovalPlanPageDto;
import com.labway.lims.apply.model.TbGroupMaterialApply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbGroupMaterialApplyMapper extends BaseMapper<TbGroupMaterialApply> {

    /**
     * 查询物料申请单以及明细信息
     * @param queryApprovalPlanPageDto
     * @return
     */
    List<GroupMaterialApplyDto> queryTbGroupMaterialApplyAndDetail(@Param("queryApprovalPlanPageDto") QueryApprovalPlanPageDto queryApprovalPlanPageDto);

}
