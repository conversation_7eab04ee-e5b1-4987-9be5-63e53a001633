package com.labway.lims.apply.service.chain.splitblood;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 分血责任链
 */
@Service
public class Split<PERSON><PERSON>od<PERSON>hain extends ChainBase implements InitializingBean {
    @Resource
    private SplitBloodLimitCommand splitBloodLimitCommand;

    @Resource
    private CheckParamsCommand checkParamsCommand;

    @Resource
    private CheckCanSplitCommand checkCanSplitCommand;

    @Resource
    private CopyApplySampleCommand copyApplySampleCommand;

    @Resource
    private DeleteApplySampleCommand deleteApplySampleCommand;

    @Resource
    private CopySampleFlowCommand copySampleFlowCommand;

    @Resource
    private SplitSampleFlowCommand splitSampleFlowCommand;

    @Resource
    private DeleteRackLogicSpaceCommand deleteRackLogicSpaceCommand;

    @Resource
    private SplitBloodCheckApplySampleStatusCommand splitBloodCheckApplySampleStatusCommand;

    @Resource
    private DisableSplitBloodApplySampleCommand disableSplitBloodApplySampleCommand;
    @Resource
    private SplitBloodPostCommand splitBloodPostCommand;
    @Resource
    private CheckGroupHandoverTagCommand checkGroupHandoverTagCommand;


    @Override
    public void afterPropertiesSet() throws Exception {

        // 分血限流
        addCommand(splitBloodLimitCommand);

        // 判断是否已停止或已终止
        addCommand(splitBloodCheckApplySampleStatusCommand);

        // 参数校验
        addCommand(checkParamsCommand);

        // 校验是否可以分血
        addCommand(checkCanSplitCommand);

        //组间交接打标记
        addCommand(checkGroupHandoverTagCommand);

        // 复制、分血
        addCommand(copyApplySampleCommand);

        // 复制流水
        addCommand(copySampleFlowCommand);

        // 删除旧的样本
        addCommand(deleteApplySampleCommand);

        // 删除旧的逻辑试管架占用
        addCommand(deleteRackLogicSpaceCommand);

        // 因为有些样本是禁止分血的，但是又涉及到多个专业组。那么这时候就要弄出多个逻辑试管架关联
        addCommand(disableSplitBloodApplySampleCommand);

        // 分血流水
        addCommand(splitSampleFlowCommand);

        // 通知业务中台分血
        addCommand(splitBloodPostCommand);

        // 完成
        addCommand(context -> true);
    }
}
