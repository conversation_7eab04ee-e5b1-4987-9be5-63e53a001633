package com.labway.lims.apply.service.chain.applysampleitem.disable;

import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.DisableOrEnableItemDto;
import com.labway.lims.apply.api.dto.StopTestOrDisableSampleDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <pre>
 * ApplySampleItemDisableCommand
 * 申请单样本项目禁用
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/12/10 18:51
 */
@Component
public class ApplySampleItemDisableCommand implements Command {

    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final ApplySampleItemDisableContext context = ApplySampleItemDisableContext.from(c);

        final Map<Long, ApplySampleDto> applySampleMap = context.getApplySampleMap();
        final DisableOrEnableItemDto disableOrEnableItemDto = context.getDisableOrEnableItemDto();
        final List<ApplySampleItemDto> disableItems = context.getDisableItems();

        // 取消禁用操作直接跳过
        if (!Objects.equals(disableOrEnableItemDto.getIsDisable(), YesOrNoEnum.YES.getCode())) {
            return CONTINUE_PROCESSING;
        }

        // 删除掉已经禁用的项目
        disableItems.removeIf(e -> Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()));

        if (CollectionUtils.isEmpty(disableItems)) {
            throw new IllegalStateException("已选项目已被禁用，无需操作禁用");
        }

        for (ApplySampleItemDto disableItem : disableItems) {
            disableItem.setIsDisabled(YesOrNoEnum.YES.getCode());
            disableItem.setUpdateDate(new Date());
            disableItem.setUpdaterId(LoginUserHandler.get().getUserId());
            disableItem.setUpdaterName(LoginUserHandler.get().getNickname());
        }
        applySampleItemService.updateBatchById(disableItems);

        final Map<Long, List<ApplySampleItemDto>> applySampleItemsMap = context.getApplySampleItemsMap();
        applySampleItemsMap.forEach((applySampleId, items) -> {
            // 已经禁用的样本项目
            items.removeIf(e -> Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()));
            // 本次禁用的样本项目
            items.removeIf(e -> disableItems.stream().anyMatch(k -> Objects.equals(k.getApplySampleItemId(), e.getApplySampleItemId())));

            // 为空表明该申请单样本项目全部被禁用，那么改申请单样本也禁用
            ApplySampleDto applySampleDto = applySampleMap.get(applySampleId);
            if (CollectionUtils.isEmpty(items) && !Objects.equals(applySampleDto.getIsDisabled(), YesOrNoEnum.YES.getCode())) {
                applySampleService.disable(new StopTestOrDisableSampleDto() {{
                    setApplySampleIds(Lists.newArrayList(applySampleId));
                    setCause("项目禁用");
                    setCauseCode("APPLYSAMPLE_ITEM_DISABLE");
                }});
            }
        });

        return CONTINUE_PROCESSING;
    }

}
