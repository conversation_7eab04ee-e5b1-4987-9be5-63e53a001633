package com.labway.lims.apply.service.chain.pick.one.cancel;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 校验参数
 */
@Slf4j
@Component
class CancelOnePickCheckParamCommand implements Command, Filter {

    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {

        final CancelOnePickContext context = CancelOnePickContext.from(c);

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(context.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalArgumentException("申请单样本不存在");
        }

        if (!Objects.equals(applySample.getIsOnePick(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalArgumentException(String.format("条码 [%s] 尚未一次分拣", applySample.getBarcode()));
        }

        //1.1.3 取消分血校验
//        if (Objects.equals(applySample.getIsSplitBlood(), YesOrNoEnum.YES.getCode())) {
//            throw new IllegalArgumentException(String.format("条码 [%s] 已经分血", applySample.getBarcode()));
//        }

        if (Objects.equals(applySample.getIsTwoPick(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalArgumentException(String.format("条码 [%s] 已经二次分拣", applySample.getBarcode()));
        }

        if (context.isRegainTerminate()) {
            // 如果是终止检验恢复操作，不校验后续状态
            context.put(CancelOnePickContext.APPLY_SAMPLE, applySample);

            return CONTINUE_PROCESSING;
        }

        if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.ENTER.getCode())) {
            throw new IllegalArgumentException(String.format("条码 [%s] 状态异常", applySample.getBarcode()));
        }

        context.put(CancelOnePickContext.APPLY_SAMPLE, applySample);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
