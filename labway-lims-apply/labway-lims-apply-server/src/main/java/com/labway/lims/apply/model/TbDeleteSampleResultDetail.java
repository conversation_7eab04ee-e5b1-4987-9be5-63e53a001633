package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
@TableName("tb_delete_sample_result_detail")
public class TbDeleteSampleResultDetail {

    /**
     * 删除的样本结果详情id
     */
    @TableId(type = IdType.INPUT)
    private Long deleteSampleResultDetailId;


    /**
     * 删除的样本结果主表id
     */
    private Long deleteSampleResultMainId;

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 报告项目id
     */
    private Long reportItemId;

    /**
     * 报告项目code
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 结果
     */
    private String result;

    /**
     * 仪器
     */
    private Long instrumentId;

    /**
     * 是否手工录入结果 0否1是
     */
    private Integer isHandeResult;

    /**
     * 英文简称
     */
    private String enName;

    /**
     * 创建人
     */
    private Long createId;
    private String createName;
    private Date createDate;


    /**
     * 更新人
     */
    private Long updateId;
    private String updateName;
    private Date updateDate;

    /**
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer isDelete;


    public void setInstrumentId(Long instrumentId) {
        this.instrumentId = instrumentId;
    }

    public void setInstrumentId(Long resultInstrumentId, Long sampleInstrumentId) {
        this.instrumentId = (Objects.isNull(resultInstrumentId) || resultInstrumentId == 0) ? sampleInstrumentId : resultInstrumentId;
    }
}
