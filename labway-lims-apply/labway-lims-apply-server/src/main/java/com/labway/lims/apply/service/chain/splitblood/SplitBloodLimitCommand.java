package com.labway.lims.apply.service.chain.splitblood;

import com.labway.lims.api.RedisPrefix;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 限制
 */
@Slf4j
@Component
class SplitBloodLimitCommand implements Command, Filter {
    private static final String MARK = "split-blood-limit";

    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context c) throws Exception {

        final SplitBloodContext context = SplitBloodContext.from(c);

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(this.redisKey(context.getApplySampleId()),
                StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalStateException("正在分血中");
        }

        context.put(MARK, StringUtils.EMPTY);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        final SplitBloodContext context = SplitBloodContext.from(c);
        if (context.containsKey(MARK)) {
            stringRedisTemplate.delete(this.redisKey(context.getApplySampleId()));
        }
        return CONTINUE_PROCESSING;
    }

    private String redisKey(long applySampleId) {
        return redisPrefix.getBasePrefix() + MARK + applySampleId;
    }
}
