package com.labway.lims.apply.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.MaterialDeliveryRecordStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.MaterialDeliveryDetailService;
import com.labway.lims.apply.api.service.MaterialDeliveryRecordService;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import com.labway.lims.apply.api.service.MaterialRefundRecordService;
import com.labway.lims.apply.api.vo.MaterialIncomeRecordVo;
import com.labway.lims.apply.api.vo.MaterialRefundRecordVo;
import com.labway.lims.apply.api.vo.RefundMaterialMaterialVo;
import com.labway.lims.apply.mapper.TbGroupMaterialApplyMapper;
import com.labway.lims.apply.mapper.TbMaterialIncomeRecordMapper;
import com.labway.lims.apply.mapper.TbMaterialRefundRecordMapper;
import com.labway.lims.apply.model.TbGroupMaterialApply;
import com.labway.lims.apply.model.TbMaterialIncomeRecord;
import com.labway.lims.apply.model.entity.TbMaterialRefundRecord;
import com.labway.lims.base.api.dto.MaterialDto;
import com.labway.lims.base.api.service.MaterialService;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物流退库记录表(TbMaterialRefundRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-04 15:44:37
 */
@Slf4j
@DubboService
public class MaterialRefundRecordServiceImpl extends ServiceImpl<TbMaterialRefundRecordMapper, TbMaterialRefundRecord> implements MaterialRefundRecordService {

    // 业务中台物料退库地址
    @Value("${business.refund.url:http://121.36.199.164/9995/refund/create-refund}")
    private String refundUrl;
    @Resource
    private TbMaterialRefundRecordMapper tbMaterialRefundRecordMapper;
    @Resource
    private TbMaterialIncomeRecordMapper tbMaterialIncomeRecordMapper;
    @Resource
    private TbGroupMaterialApplyMapper tbGroupMaterialApplyMapper;
    @Resource
    private MaterialDeliveryRecordService materialDeliveryRecordService;
    @DubboReference
    private MaterialService materialService;

    @Resource
    private MaterialDeliveryDetailService materialDeliveryDetailService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private MaterialRefundRecordService materialRefundRecordService;

    @Resource
    private MaterialInventoryService materialInventoryService;



    /**
     * 物料入库列表查询
     * @param materialIncomeRecordVo
     * @return
     */
    @Override
    public MaterialIncomeStaticDto queryMaterialMaterialIncomeRecord(MaterialIncomeRecordVo materialIncomeRecordVo) {

        LoginUserHandler.User user = LoginUserHandler.get();
        materialIncomeRecordVo.setGroupId(user.getGroupId());
        materialIncomeRecordVo.setStatus(MaterialDeliveryRecordStatusEnum.WAREHOUSED.getCode());

        List<TbMaterialIncomeRecord> tbMaterialIncomeRecords = tbMaterialIncomeRecordMapper.selectList(Wrappers.lambdaQuery(TbMaterialIncomeRecord.class)
                        .ge(TbMaterialIncomeRecord::getCreateDate,materialIncomeRecordVo.getIncomeDateBegin())
                        .le(TbMaterialIncomeRecord::getCreateDate,materialIncomeRecordVo.getIncomeDateEnd())
                        .eq(StringUtils.isNotBlank(materialIncomeRecordVo.getIncomeNo()),TbMaterialIncomeRecord::getIncomeNo,materialIncomeRecordVo.getIncomeNo())
                .ge(materialIncomeRecordVo.getValidDateBegin()!=null,TbMaterialIncomeRecord::getValidDate,materialIncomeRecordVo.getValidDateBegin())
                .le(materialIncomeRecordVo.getValidDateEnd()!=null,TbMaterialIncomeRecord::getValidDate,materialIncomeRecordVo.getValidDateEnd())
                        .eq(StringUtils.isNotBlank(materialIncomeRecordVo.getBatchNo()),TbMaterialIncomeRecord::getBatchNo,materialIncomeRecordVo.getBatchNo())
                        .and(StringUtils.isNotBlank(materialIncomeRecordVo.getMaterialKeyword()),e->e.like(TbMaterialIncomeRecord::getMaterialCode,materialIncomeRecordVo.getMaterialKeyword())
                                .or().like(TbMaterialIncomeRecord::getMaterialName,materialIncomeRecordVo.getMaterialKeyword()))
                .eq(TbMaterialIncomeRecord::getGroupId,materialIncomeRecordVo.getGroupId()));

        if (CollectionUtils.isEmpty(tbMaterialIncomeRecords)){
            return new MaterialIncomeStaticDto();
        }

        // 所有出库单号
        Set<String> deliveryNoList = tbMaterialIncomeRecords.stream().map(TbMaterialIncomeRecord::getDeliveryNo).collect(Collectors.toSet());
        // 所有入库单号
        Set<String> incomeNoList = tbMaterialIncomeRecords.stream().map(TbMaterialIncomeRecord::getIncomeNo).collect(Collectors.toSet());
        // 所有物料编码
        Set<Long> materialIdList = tbMaterialIncomeRecords.stream().map(TbMaterialIncomeRecord::getMaterialId).collect(Collectors.toSet());

        // 对应出库信息
        List<MaterialDeliveryRecordDto> materialDeliveryRecordDtos = materialDeliveryRecordService.selectByDeliveryNos(deliveryNoList, user.getOrgId());
        // 对应退库信息
        List<TbMaterialRefundRecord> tbMaterialRefundRecords = tbMaterialRefundRecordMapper.selectList(Wrappers.lambdaQuery(TbMaterialRefundRecord.class)
                .in(TbMaterialRefundRecord::getIncomeNo, incomeNoList).eq(TbMaterialRefundRecord::getRefundType, 1).eq(TbMaterialRefundRecord::getIsDelete, 0));
        // 物料库存信息
        List<MaterialInventoryDto> materialInventoryDtos = materialInventoryService.selectByMaterialIds(materialIdList, user.getOrgId());
        // 查询物料储存温度
        List<MaterialDto> materialDtos = materialService.selectByIds(materialIdList);


        // 正常数据 一个出库单号 对应一条信息
        Map<String, MaterialDeliveryRecordDto> materialDeliveryRecordByDeliveryNo = materialDeliveryRecordDtos.stream().collect(Collectors.toMap(MaterialDeliveryRecordDto::getDeliveryNo, Function.identity()));
        // 一个入库单号 对应多条信息
        Map<String, List<TbMaterialRefundRecord>> incomeNoGroup = tbMaterialRefundRecords.stream().collect(Collectors.groupingBy(TbMaterialRefundRecord::getIncomeNo));
        // 正常数据 一个物料id 对应一条信息
        List<MaterialInventoryDto> materialInventoryDtoMap = materialInventoryDtos.stream().filter(o -> Objects.equals(o.getGroupId(), user.getGroupId())).collect(Collectors.toList());
        // 正常数据 一个物料id 对应一条信息
        Map<Long, MaterialDto> materialDtoMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, Function.identity()));

        // 实体转换
        List<MaterialIncomeRecordDto> materialIncomeRecordDtos = JSONObject.parseArray(JSONObject.toJSONString(tbMaterialIncomeRecords), MaterialIncomeRecordDto.class);
        materialIncomeRecordDtos.forEach(e->{
            MaterialDeliveryRecordDto tempRecodrDto = materialDeliveryRecordByDeliveryNo.get(e.getDeliveryNo());
            List<TbMaterialRefundRecord> tempRefundRecords = incomeNoGroup.get(e.getIncomeNo());
            MaterialInventoryDto tempMaterialInventoryDto = materialInventoryDtoMap.stream().filter(o -> Objects.equals(o.getMaterialId(), e.getMaterialId()) && Objects.equals(o.getBatchNo(), e.getBatchNo())).findFirst().get();

            // 填充退库主辅数量
            if (CollectionUtils.isNotEmpty(tempRefundRecords)){
                Map<Long, List<TbMaterialRefundRecord>> materialCodeGroup = tempRefundRecords.stream().collect(Collectors.groupingBy(TbMaterialRefundRecord::getMaterialId));
                List<TbMaterialRefundRecord> tempMaterialRefundList = materialCodeGroup.get(e.getMaterialId());
                if (CollectionUtils.isNotEmpty(tempMaterialRefundList)){
                    BigDecimal tempAssistNum = tempMaterialRefundList.stream().map(TbMaterialRefundRecord::getRefundAssistNumber).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    BigDecimal tempMainNum = tempMaterialRefundList.stream().map(TbMaterialRefundRecord::getRefundMainNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
                    e.setTotalRefundAssistNumber(tempAssistNum);
                    e.setTotalRefundMainNumber(tempMainNum);
                }
            }

            // 填充库存信息
            if (tempMaterialInventoryDto != null){
                e.setAssistUintInventory(tempMaterialInventoryDto.getAssistUnitInventory());
                e.setMainUnitInventory(tempMaterialInventoryDto.getMainUnitInventory());
            }

            // 填充出库信息
            e.setRecordId(tempRecodrDto.getRecordId());
            e.setDeliveryDate(tempRecodrDto.getDeliveryDate());
            e.setDeliveryUser(tempRecodrDto.getDeliveryUser());

            // 填充物料存储温度
            MaterialDto materialDto = materialDtoMap.get(e.getMaterialId());
            if (Objects.nonNull(materialDto)) {
                e.setStorageTemperature(materialDto.getStorageTemperature());
            }
        });

        // 按照入库时间正序
        List<MaterialIncomeRecordDto> collect = materialIncomeRecordDtos.stream().sorted(Comparator.comparing(MaterialIncomeRecordDto::getCreateDate)
                .thenComparing(MaterialIncomeRecordDto::getIncomeId)).collect(Collectors.toList());

        BigDecimal totalMainNum = collect.stream().map(MaterialIncomeRecordDto::getIncomeMainNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalAssistNum = collect.stream().map(MaterialIncomeRecordDto::getIncomeAssistNumber).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        return new MaterialIncomeStaticDto(totalMainNum,totalAssistNum,collect);
    }

    /**
     * 查询退库列表
     * @param materialRefundRecordVo
     * @return
     */
    @Override
    public MaterialRefundStaticDto queryMaterialRefundRecord(MaterialRefundRecordVo materialRefundRecordVo) {

        LoginUserHandler.User user = LoginUserHandler.get();

        // 查询退库单信息
        List<TbMaterialRefundRecord> tbMaterialRefundRecords = tbMaterialRefundRecordMapper.selectList(Wrappers.lambdaQuery(TbMaterialRefundRecord.class)
                .ge(TbMaterialRefundRecord::getCreateDate, materialRefundRecordVo.getRefundTimeBegin())
                .le(TbMaterialRefundRecord::getCreateDate, materialRefundRecordVo.getRefundTimeEnd())
                .eq(materialRefundRecordVo.getRefundType() != null, TbMaterialRefundRecord::getRefundType, materialRefundRecordVo.getRefundType())
                .eq(TbMaterialRefundRecord::getGroupId, user.getGroupId())
                .eq(StringUtils.isNotBlank(materialRefundRecordVo.getRefundNo()), TbMaterialRefundRecord::getRefundNo, materialRefundRecordVo.getRefundNo())
                .and(StringUtils.isNotBlank(materialRefundRecordVo.getMaterialKeyword()),
                        e -> e.like(TbMaterialRefundRecord::getMaterialCode, materialRefundRecordVo.getMaterialKeyword())
                                .or().like(TbMaterialRefundRecord::getMaterialName, materialRefundRecordVo.getMaterialKeyword()))
                .eq(TbMaterialRefundRecord::getIsDelete, 0));
        if (CollectionUtils.isEmpty(tbMaterialRefundRecords)) {
            return new MaterialRefundStaticDto();
        }

        List<MaterialRefundRecordDto> materialRefundStaticDtos = JSONObject.parseArray(JSONObject.toJSONString(tbMaterialRefundRecords), MaterialRefundRecordDto.class);

        // 查询物料储存温度
        Set<Long> materialIds = tbMaterialRefundRecords.stream().map(TbMaterialRefundRecord::getMaterialId).collect(Collectors.toSet());
        List<MaterialDto> materialDtos = materialService.selectByIds(materialIds);
        Map<Long, MaterialDto> materialDtoMap = materialDtos.stream().collect(Collectors.toMap(MaterialDto::getMaterialId, Function.identity()));
        materialRefundStaticDtos.forEach(m -> {
            MaterialDto materialDto = materialDtoMap.get(m.getMaterialId());
            if (Objects.nonNull(materialDto)) {
                m.setStorageTemperature(materialDto.getStorageTemperature());
            }
        });

        List<MaterialRefundRecordDto> collect = materialRefundStaticDtos.stream().sorted(Comparator.comparing(MaterialRefundRecordDto::getCreateDate)).collect(Collectors.toList());
	    BigDecimal totalMainNum = collect.stream().map(MaterialRefundRecordDto::getRefundMainNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalAssistNum = collect.stream().map(MaterialRefundRecordDto::getRefundAssistNumber).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        return new MaterialRefundStaticDto(totalMainNum, totalAssistNum, collect);
    }

    /**
     * 物料退库
     * @param refundMaterialMaterialVos
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RefundMaterialMaterialDto refundMaterialMaterial(List<RefundMaterialMaterialVo> refundMaterialMaterialVos) {

        LoginUserHandler.User user = LoginUserHandler.get();

        // 入库单ids
        List<Long> incomeIds = refundMaterialMaterialVos.stream().map(RefundMaterialMaterialVo::getIncomeId).collect(Collectors.toList());
        Set<Long> materialIds = refundMaterialMaterialVos.stream().map(RefundMaterialMaterialVo::getMaterialId).collect(Collectors.toSet());

        // 查询入库单信息
        List<TbMaterialIncomeRecord> tbMaterialIncomeRecords = tbMaterialIncomeRecordMapper.selectList(Wrappers.lambdaQuery(TbMaterialIncomeRecord.class)
                .in(TbMaterialIncomeRecord::getIncomeId,incomeIds));

        // 物料库存信息
        List<MaterialInventoryDto> materialInventoryDtos = materialInventoryService.selectByMaterialIds(materialIds, user.getOrgId()).stream().filter(e -> Objects.equals(e.getGroupId(), user.getGroupId())).collect(Collectors.toList());

        // 校验库存数量
        List<MaterialInventoryDto> checkMaterialInventoryList = checkRefundNum(refundMaterialMaterialVos,tbMaterialIncomeRecords,materialInventoryDtos,user);

        // 保存物料退库数据
        List<TbMaterialRefundRecord> recordsForRefund = getRecordsForRefund(refundMaterialMaterialVos, tbMaterialIncomeRecords, user);
        this.saveBatch(recordsForRefund);

        // 更新物料库存数量
        doMaterialInventory(refundMaterialMaterialVos,checkMaterialInventoryList,user);

        // 通知业务中台退库
        notifyBusinessMaterialRefund(new NotifyBusinessMaterialRefundDto(recordsForRefund.get(0).getRefundNo()));

        return new RefundMaterialMaterialDto("SUCCESS");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addBatch(List<MaterialRefundRecordDto> refundRecordDtoList) {
        if(CollectionUtils.isNotEmpty(refundRecordDtoList)){
            List<TbMaterialRefundRecord> tbMaterialRefundRecords = JSON.parseArray(JSON.toJSONString(refundRecordDtoList), TbMaterialRefundRecord.class);

            this.saveBatch(tbMaterialRefundRecords);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void rejection(MaterialRecordIdDto dto) {

        MaterialDeliveryRecordDto deliveryRecordDto = materialDeliveryRecordService.selectByRecordId(dto.getRecordId());
        Assert.notNull(deliveryRecordDto, "无效记录");
        TbGroupMaterialApply tbGroupMaterialApply = tbGroupMaterialApplyMapper.selectById(deliveryRecordDto.getApplyId());
        Assert.notNull(tbGroupMaterialApply, "专业组物料申请单无效");
        // 出库单号
        String deliveryNo = deliveryRecordDto.getDeliveryNo();

        LoginUserHandler.User user = LoginUserHandler.get();
        if (!Objects.equals(deliveryRecordDto.getStatus(), MaterialDeliveryRecordStatusEnum.NOT_WAREHOUSED.getCode())) {
            throw new IllegalStateException("物料已入库， 不能拒收！");
        }

        // 未入库 && 要拒收的
        List<MaterialDeliveryDetailDto> deliveryDetailDtoList =
                materialDeliveryDetailService.selectByDeliveryNo(deliveryNo, user.getOrgId())
                        .stream()
                        .filter(e -> dto.getDetailIds().contains(e.getDetailId()))
                        .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(deliveryDetailDtoList)){
            return;
        }

        List<MaterialRefundRecordDto> refundRecordDtoList = new ArrayList<>();
        Date date = new Date();

        String refundNo = getIncrementRefundNo(user.getOrgId());
        for (MaterialDeliveryDetailDto materialDeliveryDetailDto : deliveryDetailDtoList) {
            MaterialRefundRecordDto refundRecordDto = new MaterialRefundRecordDto();
            refundRecordDto.setRefundNo(refundNo);
            refundRecordDto.setIncomeNo(Strings.EMPTY);
            refundRecordDto.setDeliveryNo(materialDeliveryDetailDto.getDeliveryNo());
            refundRecordDto.setDeliveryDetailId(materialDeliveryDetailDto.getDetailId());
            refundRecordDto.setMaterialId(materialDeliveryDetailDto.getMaterialId());
            refundRecordDto.setMaterialCode(materialDeliveryDetailDto.getMaterialCode());
            refundRecordDto.setMaterialName(materialDeliveryDetailDto.getMaterialName());
            refundRecordDto.setSpecification(materialDeliveryDetailDto.getSpecification());
            refundRecordDto.setBatchNo(materialDeliveryDetailDto.getBatchNo());
            refundRecordDto.setManufacturers(materialDeliveryDetailDto.getManufacturers());
            refundRecordDto.setMainUnit(materialDeliveryDetailDto.getMainUnit());
            refundRecordDto.setDeliveryMainNumber(materialDeliveryDetailDto.getDeliveryMainNumberDecimal());
            refundRecordDto.setIncomeMainNumber(BigDecimal.ZERO);
            refundRecordDto.setAssistUnit(materialDeliveryDetailDto.getAssistUnit());
            refundRecordDto.setDeliveryAssistNumber(materialDeliveryDetailDto.getDeliveryAssistNumber());
            refundRecordDto.setIncomeAssistNumber(BigDecimal.ZERO);
            refundRecordDto.setUnitConversionRate(materialDeliveryDetailDto.getUnitConversionRate());
            refundRecordDto.setValidDate(materialDeliveryDetailDto.getValidDate());
            refundRecordDto.setGroupId(user.getGroupId());
            refundRecordDto.setGroupName(user.getGroupName());
            refundRecordDto.setOrgId(user.getOrgId());
            refundRecordDto.setOrgName(user.getOrgName());

            refundRecordDto.setCreatorId(user.getUserId());
            refundRecordDto.setCreatorName(user.getNickname());
            refundRecordDto.setUpdaterId(user.getUserId());
            refundRecordDto.setUpdaterName(user.getNickname());
            refundRecordDto.setCreateDate(date);
            refundRecordDto.setUpdateDate(date);
            refundRecordDto.setIsDelete(0);
            refundRecordDto.setRefundType(2);
            refundRecordDto.setRefundReason(dto.getRefundReason());
            refundRecordDto.setRefundMainNumber(materialDeliveryDetailDto.getDeliveryMainNumberDecimal());
            refundRecordDto.setRefundAssistNumber(materialDeliveryDetailDto.getDeliveryAssistNumber());
            refundRecordDtoList.add(refundRecordDto);
        }

        // 添加拒收记录
        this.addBatch(refundRecordDtoList);

        // 删除出库记录和物料
        materialDeliveryDetailService.deleteBydetailIds(deliveryDetailDtoList.stream().map(MaterialDeliveryDetailDto::getDetailId).collect(Collectors.toList()));

        //  TODO  通知业务中台 物料拒收
        MaterialDeliveryDetailDto materialDeliveryDetailDto = deliveryDetailDtoList.get(0);
        NotifyBusinessMaterialRefundDto notifyBusinessMaterialRefundDto = new NotifyBusinessMaterialRefundDto();
        notifyBusinessMaterialRefundDto.setLimsOrgId(String.valueOf(user.getOrgId()));
        notifyBusinessMaterialRefundDto.setRefundApplyNo(refundNo);
        notifyBusinessMaterialRefundDto.setRefundApplyUserId(String.valueOf(user.getUserId()));
        notifyBusinessMaterialRefundDto.setRefundApplyUser(user.getNickname());
        notifyBusinessMaterialRefundDto.setRefundApplyTime(LocalDateTime.ofInstant(date.toInstant(),ZoneId.systemDefault()));
        notifyBusinessMaterialRefundDto.setRefundReason(dto.getRefundReason());
        notifyBusinessMaterialRefundDto.setSupplyNo(materialDeliveryDetailDto.getDeliveryNo());
        notifyBusinessMaterialRefundDto.setSupplyTime(LocalDateTime.ofInstant(materialDeliveryDetailDto.getCreateDate().toInstant(),ZoneId.systemDefault()));
        notifyBusinessMaterialRefundDto.setApplyNo(tbGroupMaterialApply.getApplyNo());

        AtomicInteger i = new AtomicInteger(0);
        List<NotifyBusinessMaterialRefundDto.MaterialRefundDetailDto> materialRefundDetailDtoList = deliveryDetailDtoList.stream().map(e -> {
            NotifyBusinessMaterialRefundDto.MaterialRefundDetailDto materialRefundDetailDto = new NotifyBusinessMaterialRefundDto.MaterialRefundDetailDto();
            materialRefundDetailDto.setRowNo(String.valueOf(i.getAndIncrement()));
            materialRefundDetailDto.setMaterialCode(e.getMaterialCode());
            materialRefundDetailDto.setRefundNumberDecimal(e.getDeliveryMainNumberDecimal());
            materialRefundDetailDto.setBatchNo(e.getBatchNo());
            materialRefundDetailDto.setAssistNumber(e.getDeliveryAssistNumber());
            materialRefundDetailDto.setExpirationDate(e.getValidDate()==null?"":new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(e.getValidDate()));
            return materialRefundDetailDto;
        }).collect(Collectors.toList());
        notifyBusinessMaterialRefundDto.setDetailRequests(materialRefundDetailDtoList);

        materialRefundRecordService.notifyBusinessMaterialRefund(notifyBusinessMaterialRefundDto);

    }


    /**
     * 通知业务中台物料退库/拒收
     * @param notifyBusinessMaterialRefundDto
     */
    @Override
    public String notifyBusinessMaterialRefund(NotifyBusinessMaterialRefundDto notifyBusinessMaterialRefundDto) {
        LoginUserHandler.User user = LoginUserHandler.get();

        List<NotifyBusinessMaterialRefundDto> refundList = getRefundList(notifyBusinessMaterialRefundDto);
        if (CollectionUtils.isEmpty(refundList)) {
            log.info("没有需要推送的退货/拒收单！！！");
            return "SUCCESS";
        }

        List<String> refundDSuccessNo = new ArrayList<>();

        for (NotifyBusinessMaterialRefundDto e : refundList) {
            // 推送退后单
            HttpResponse execute = HttpRequest.post(refundUrl).body(JSONObject.toJSONString(e)).execute();
            String body = execute.body();

            // 判断body是否是json格式数据
            if (!JSONUtil.isTypeJSON(body)) {
                log.error("物料退库失败，业务中台响应信息：{}", body);
                throw new LimsException("退库失败了哦，业务中台响应数据格式错误！");
            }

            if (!execute.isOk() || !Objects.equals(JSONObject.parseObject(body).getInteger("code"), YesOrNoEnum.NO.getCode())) {
                log.error("推送退库消息失败，退库单号：{},失败信息体：{}", e.getRefundApplyNo(), execute.body());
                throw new LimsException("退库失败了哈，退库单号：【" + e.getRefundApplyNo() + "】");
            }

            refundDSuccessNo.add(e.getRefundApplyNo());
        }

        log.info("退库推送成功，退库单号：【{}】", refundDSuccessNo.stream().collect(Collectors.joining(",")));

        if (CollectionUtils.isEmpty(refundDSuccessNo)){
            log.warn("没有任何退库单推送成功，不需要更新数据库！！");
            return "FAIL";
        }

        // 更新退库单推送标识
        tbMaterialRefundRecordMapper.update(null, Wrappers.lambdaUpdate(TbMaterialRefundRecord.class)
                .set(TbMaterialRefundRecord::getPushFlag, 1)
                .set(TbMaterialRefundRecord::getUpdaterId, user.getUserId())
                .set(TbMaterialRefundRecord::getUpdaterName, user.getUsername())
                .set(TbMaterialRefundRecord::getUpdateDate, new Date())
                .in(TbMaterialRefundRecord::getRefundNo, refundDSuccessNo));

        return "SUCCESS";
    }

    // 组装退库拒收通知实体
    private List<NotifyBusinessMaterialRefundDto> getRefundList(NotifyBusinessMaterialRefundDto notifyBusinessMaterialRefundDto) {
        // 退库单号
        String refundNo = notifyBusinessMaterialRefundDto.getRefundNo();

        List<NotifyBusinessMaterialRefundDto> refundList = new ArrayList<>();
        if (StringUtils.isBlank(refundNo)) {
            refundList.add(notifyBusinessMaterialRefundDto);
            return refundList;
        }

        List<TbMaterialRefundRecord> tbMaterialRefundRecords = tbMaterialRefundRecordMapper.selectList(Wrappers.lambdaQuery(TbMaterialRefundRecord.class)
                .eq(StringUtils.isNotBlank(refundNo), TbMaterialRefundRecord::getRefundNo, refundNo)
                .eq(TbMaterialRefundRecord::getPushFlag, 0)
                .eq(TbMaterialRefundRecord::getIsDelete, 0));
        if (CollectionUtils.isEmpty(tbMaterialRefundRecords)) {
            log.info("未查询到退库单信息，退库单号：【{}】",refundNo);
            return refundList;
        }
        TbMaterialRefundRecord refundRecordFirst = tbMaterialRefundRecords.get(0);

        // 入库信息
        List<TbMaterialIncomeRecord> tbMaterialIncomeRecords = tbMaterialIncomeRecordMapper.selectList(Wrappers.lambdaQuery(TbMaterialIncomeRecord.class).eq(TbMaterialIncomeRecord::getIncomeNo, refundRecordFirst.getIncomeNo()));
        TbMaterialIncomeRecord tbMaterialIncomeRecord = tbMaterialIncomeRecords.get(0);

        // 查询出库单
        MaterialDeliveryRecordDto materialDeliveryRecordDto = materialDeliveryRecordService.selectByDeliveryNo(tbMaterialIncomeRecord.getDeliveryNo(),tbMaterialIncomeRecord.getOrgId());

        // 查询申请单
        TbGroupMaterialApply tbGroupMaterialApply = tbGroupMaterialApplyMapper.selectById(materialDeliveryRecordDto.getApplyId());


        NotifyBusinessMaterialRefundDto tempDto = new NotifyBusinessMaterialRefundDto();
        List<NotifyBusinessMaterialRefundDto.MaterialRefundDetailDto> tempList = new ArrayList<>();

        // 封装退库单信息
        tempDto.setLimsOrgId(String.valueOf(refundRecordFirst.getOrgId()));
        tempDto.setRefundApplyNo(refundRecordFirst.getRefundNo());
        tempDto.setRefundApplyUserId(String.valueOf(refundRecordFirst.getCreatorId()));
        tempDto.setRefundApplyUser(refundRecordFirst.getCreatorName());
        tempDto.setRefundApplyTime(LocalDateTime.ofInstant(refundRecordFirst.getCreateDate().toInstant(), ZoneId.systemDefault()));
        tempDto.setRefundReason(refundRecordFirst.getRefundReason());
        tempDto.setSupplyNo(refundRecordFirst.getDeliveryNo());
        tempDto.setSupplyTime(LocalDateTime.ofInstant(materialDeliveryRecordDto.getDeliveryDate().toInstant(), ZoneId.systemDefault()));
        tempDto.setApplyNo(tbGroupMaterialApply.getApplyNo());
        int rowNo = 1;
        for (TbMaterialRefundRecord e : tbMaterialRefundRecords) {
            NotifyBusinessMaterialRefundDto.MaterialRefundDetailDto tempDetail = new NotifyBusinessMaterialRefundDto.MaterialRefundDetailDto();
            tempDetail.setRowNo(String.valueOf(rowNo++));
            tempDetail.setMaterialCode(e.getMaterialCode());
//            tempDetail.setMaterialName(e.getMaterialName());
//            tempDetail.setMaterialSpecification(e.getSpecification());
//            tempDetail.setMaterialManufacturer(e.getManufacturers());
            tempDetail.setRefundNumberDecimal(e.getRefundMainNumber());
            tempDetail.setBatchNo(e.getBatchNo());
            tempDetail.setAssistNumber(e.getRefundAssistNumber());
//            tempDetail.setMainUnit(e.getMainUnit());
//            tempDetail.setMainUnitCode(e.);
//            tempDetail.setAssistUnit(e.getAssistUnit());
//            tempDetail.setAssistUnitCode();
            tempDetail.setExpirationDate(e.getValidDate()==null?"":new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(e.getValidDate()));
//            tempDetail.setMainmeasrate(e.getUnitConversionRate());
            tempList.add(tempDetail);
        }
        tempDto.setDetailRequests(tempList);
        refundList.add(tempDto);

        return refundList;
    }


    //==================================================================================================================


    // 退库数量校验
    private List<MaterialInventoryDto> checkRefundNum(List<RefundMaterialMaterialVo> refundMaterialMaterialVos, List<TbMaterialIncomeRecord> tbMaterialIncomeRecords, List<MaterialInventoryDto> materialDtos,LoginUserHandler.User user) {

        Map<Long, TbMaterialIncomeRecord> incomeRecordMap = tbMaterialIncomeRecords.stream().collect(Collectors.toMap(TbMaterialIncomeRecord::getIncomeId, p -> p, (o1, o2) -> o2));
//        Map<Long, MaterialInventoryDto> materialInventoryDtoMap = materialDtos.stream().filter(o->Objects.equals(o.getGroupId(),user.getGroupId())).collect(Collectors.toMap(e -> e.getMaterialId(), p -> p, (o1, o2) -> o2));
        List<MaterialInventoryDto> materialInventoryDtoMap = materialDtos.stream().filter(o -> Objects.equals(o.getGroupId(), user.getGroupId())).collect(Collectors.toList());

        List<MaterialInventoryDto> checkMaterialInventoryList = new ArrayList<>();

        refundMaterialMaterialVos.forEach(e->{
            TbMaterialIncomeRecord tbMaterialIncomeRecord = incomeRecordMap.get(e.getIncomeId());
            MaterialInventoryDto materialInventoryDto = materialInventoryDtoMap.stream().filter(o -> Objects.equals(o.getMaterialId(), e.getMaterialId()) && Objects.equals(o.getBatchNo(), tbMaterialIncomeRecord.getBatchNo())).findFirst().get();

            if (tbMaterialIncomeRecord==null || materialInventoryDto==null){
                throw new LimsException("入库单或者物料不存在！！！");
            }

            // 退库辅数量
            BigDecimal refundAssistNumber = e.getRefundAssistNumber();
            // 退库主数量
            BigDecimal refundMainNumber = e.getRefundMainNumber();

            if (refundAssistNumber.compareTo(tbMaterialIncomeRecord.getIncomeAssistNumber())>0){
                throw new LimsException("退库数量不能大于入库单辅数量！！！");
            }

            if (refundAssistNumber.compareTo(materialInventoryDto.getAssistUnitInventory())>0){
                throw new LimsException("退库数量不能大于物料库存辅数量！！！");
            }


            if (refundMainNumber.compareTo(tbMaterialIncomeRecord.getIncomeMainNumber())>0){
                throw new LimsException("退库数量不能大于入库单主数量！！！");
            }

            if (refundMainNumber.compareTo(materialInventoryDto.getMainUnitInventory())>0){
                throw new LimsException("退库数量不能大于物料库存主数量！！！");
            }


            e.setBatchNo(tbMaterialIncomeRecord.getBatchNo());
            checkMaterialInventoryList.add(materialInventoryDto);

        });

        return checkMaterialInventoryList;
    }

    // 组装退库单数据
    private List<TbMaterialRefundRecord> getRecordsForRefund(List<RefundMaterialMaterialVo> refundMaterialMaterialVos, List<TbMaterialIncomeRecord> tbMaterialIncomeRecords,LoginUserHandler.User user) {

        List<TbMaterialRefundRecord> records = new ArrayList<>();
        Long orgId = user.getOrgId();
        Date now = new Date();
        Map<Long, TbMaterialIncomeRecord> incomeRecordMap = tbMaterialIncomeRecords.stream().collect(Collectors.toMap(e -> e.getIncomeId(), p -> p, (o1, o2) -> o2));

        String incrementRefundNo = getIncrementRefundNo(orgId);
        for (RefundMaterialMaterialVo tempVo : refundMaterialMaterialVos) {
            TbMaterialIncomeRecord tempIncome = incomeRecordMap.get(tempVo.getIncomeId());
            TbMaterialRefundRecord refundRecord = new TbMaterialRefundRecord();
            refundRecord.setRefundNo(incrementRefundNo);
            refundRecord.setIncomeNo(tempVo.getIncomeNo());
            refundRecord.setDeliveryNo(tempIncome.getDeliveryNo());
            refundRecord.setDeliveryDetailId(0L);
            refundRecord.setMaterialId(tempIncome.getMaterialId());
            refundRecord.setMaterialCode(tempIncome.getMaterialCode());
            refundRecord.setMaterialName(tempIncome.getMaterialName());
            refundRecord.setSpecification(tempIncome.getSpecification());
            refundRecord.setBatchNo(tempIncome.getBatchNo());
            refundRecord.setManufacturers(tempIncome.getManufacturers());
            refundRecord.setMainUnit(tempIncome.getMainUnit());
            refundRecord.setDeliveryMainNumber(tempIncome.getDeliveryMainNumber());
            refundRecord.setIncomeMainNumber(tempIncome.getIncomeMainNumber());
            refundRecord.setAssistUnit(tempIncome.getAssistUnit());
            refundRecord.setDeliveryAssistNumber(tempIncome.getDeliveryAssistNumber());
            refundRecord.setIncomeAssistNumber(tempIncome.getIncomeAssistNumber());
            refundRecord.setUnitConversionRate(tempIncome.getUnitConversionRate());
            refundRecord.setValidDate(tempIncome.getValidDate());
            refundRecord.setGroupId(user.getGroupId());
            refundRecord.setGroupName(user.getGroupName());
            refundRecord.setOrgId(user.getOrgId());
            refundRecord.setOrgName(user.getOrgName());

            refundRecord.setCreatorId(user.getUserId());
            refundRecord.setCreatorName(user.getNickname());
            refundRecord.setUpdaterId(user.getUserId());
            refundRecord.setUpdaterName(user.getNickname());
            refundRecord.setCreateDate(now);
            refundRecord.setUpdateDate(now);
            refundRecord.setIsDelete(0);
            refundRecord.setRefundType(1);
            refundRecord.setRefundReason(tempVo.getRefundReason());
            refundRecord.setRefundMainNumber(tempVo.getRefundMainNumber());
            refundRecord.setRefundAssistNumber(tempVo.getRefundAssistNumber());
            records.add(refundRecord);
        }

        return records;
    }

    /**
     * 获取自增退库单号
     */
    public String getIncrementRefundNo(Long orgId){
        String now = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
        String refundKey = redisPrefix.getBasePrefix() + ":material:refundNo:" + orgId + ":" + now;
        Long incrementKey = stringRedisTemplate.opsForValue().increment(refundKey);
        stringRedisTemplate.expire(refundKey, 1, TimeUnit.DAYS);
       return "TK" + (String.valueOf(orgId).length()>1?String.valueOf(orgId).substring(0,2) : orgId+"0") + new SimpleDateFormat("yyMMdd").format(new Date()) + String.format("%04d", incrementKey);
    }

    /**
     * 物料库存处理
     * @param refundMaterialMaterialVos
     * @param materialDtos
     * @return
     */
    private void doMaterialInventory(List<RefundMaterialMaterialVo> refundMaterialMaterialVos, List<MaterialInventoryDto> materialDtos,LoginUserHandler.User user) {
        Date now = new Date();
        Map<Long, MaterialInventoryDto> materialDtoMap = materialDtos.stream().collect(Collectors.toMap(e -> e.getMaterialId(), p -> p, (o1, o2) -> o2));

        for (RefundMaterialMaterialVo e : refundMaterialMaterialVos) {
            MaterialInventoryDto materialInventoryDto = materialDtoMap.get(e.getMaterialId());
            if (Objects.isNull(materialInventoryDto)) {
                continue;
            }

            MaterialInventoryDto inventoryDto = new MaterialInventoryDto();
            inventoryDto.setInventoryId(materialInventoryDto.getInventoryId());
            inventoryDto.setUpdaterId(user.getUserId());
            inventoryDto.setUpdaterName(user.getUsername());
            inventoryDto.setUpdateDate(now);
            inventoryDto.setMainUnitInventory(materialInventoryDto.getMainUnitInventory().subtract(e.getRefundMainNumber()));
            inventoryDto.setAssistUnitInventory(materialInventoryDto.getAssistUnitInventory().subtract(e.getRefundAssistNumber()));
            materialInventoryService.updateByInventoryId(inventoryDto);
        }

    }


}

