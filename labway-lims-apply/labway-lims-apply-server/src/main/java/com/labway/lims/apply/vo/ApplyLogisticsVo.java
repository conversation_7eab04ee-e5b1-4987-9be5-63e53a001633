package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 物流申请单（补录）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class ApplyLogisticsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    private Long applyLogisticsId;

    /**
     * 条码
     */
    private String masterBarcode;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 取货日期
     */
    private Date receiveDate;

    /**
     * 物流人
     */
    private Long logisticsUserId;

    /**
     * 物流人员
     */
    private String logisticsUserName;

    /**
     * 申请单图片 多个 , 隔开
     */
    private String applyImage;
    /**
     * 1: 待补录
     * 2: 待复核
     * 3: 已复核
     */
    private Integer status;

    /**
     * 1: 删除 0：未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 一旦补录完成，就有了申请单id
     */
    private Long applyId;
    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

}
