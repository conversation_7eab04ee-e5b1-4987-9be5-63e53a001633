package com.labway.lims.apply.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 物料库存导入
 * 
 * <AUTHOR>
 * @since 2023/10/27 19:12
 */
@Getter
@Setter
public class MaterialInventoryImportHeadVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业组名称
     */
    @ExcelProperty(value = "专业组", index = 0)
    private String groupName;
    /**
     * 物资编号
     */
    @ExcelProperty(value = "物料编码", index = 1)
    private String materialCode;

    /**
     * 物资名称
     */
    @ExcelProperty(value = "物料名称", index = 2)
    private String materialName;

    /**
     * 主单位库存
     */
    @ExcelProperty(value = "主数量", index = 3)
    private String mainUnitInventory;

    /**
     * 辅单位库存
     */
    @ExcelProperty(value = "辅数量", index = 4)
    private String assistUnitInventory;
    /**
     * 批号
     */
    @ExcelProperty(value = "批号", index = 5)
    private String batchNo;
    /**
     * 有效期
     */
    @ExcelProperty(value = "有效期", index = 6)
    private String validDate;
}
