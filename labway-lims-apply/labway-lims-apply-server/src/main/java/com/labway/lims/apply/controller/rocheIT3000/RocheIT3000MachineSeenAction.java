package com.labway.lims.apply.controller.rocheIT3000;

import com.labway.lims.apply.vo.IT3000HandleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * IT3000 扫描到条码
 */
@Slf4j
@Component
class RocheIT3000MachineSeenAction implements ActionStrategy {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object action(IT3000HandleVo vo) throws Exception {
        return Map.of();
    }


    @Override
    public IT3000HandleVo.Action action() {
        return IT3000HandleVo.Action.MACHINE_SEEN;
    }


}
