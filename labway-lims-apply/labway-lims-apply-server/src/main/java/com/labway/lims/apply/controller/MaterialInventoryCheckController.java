package com.labway.lims.apply.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.apply.MaterialInventoryCheckStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.MaterialInventoryCheckDetailService;
import com.labway.lims.apply.api.service.MaterialInventoryCheckService;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import com.labway.lims.apply.mapstruct.MaterialInventoryCheckConverter;
import com.labway.lims.apply.vo.MaterialInventoryCheckFinishCheckRequestVo;
import com.labway.lims.apply.vo.MaterialInventoryCheckListItemResponseVo;
import com.labway.lims.apply.vo.MaterialInventoryCheckListRequestVo;
import com.labway.lims.apply.vo.MaterialInventoryCheckListResponseVo;
import com.labway.lims.base.api.dto.MaterialDto;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 物料盘点 API
 *
 * <AUTHOR>
 * @since 2023/5/11 16:56
 */
@Slf4j
@RestController
@RequestMapping("/material-inventory-check")
public class MaterialInventoryCheckController extends BaseController {

    @Resource
    private MaterialInventoryCheckService materialInventoryCheckService;

    @Resource
    private MaterialInventoryCheckDetailService materialInventoryCheckDetailService;
    @Resource
    private MaterialInventoryCheckConverter materialInventoryCheckConverter;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private MaterialInventoryService materialInventoryService;

    /**
     * 盘点记录
     */
    @PostMapping("/list")
    public Object materialInventoryCheckList(@RequestBody MaterialInventoryCheckListRequestVo vo) {
        SelectMaterialInventoryCheckDto dto =
                materialInventoryCheckConverter.selectMaterialInventoryCheckDtoFromRequestVo(vo);
        LoginUserHandler.User user = LoginUserHandler.get();
        dto.setGroupId(user.getGroupId());

        // 盘点记录
        List<MaterialInventoryCheckDto> materialInventoryCheckDtos =
                materialInventoryCheckService.selectBySelectMaterialInventoryCheckDto(dto);

        // 转换响应结构
        MaterialInventoryCheckListResponseVo target = new MaterialInventoryCheckListResponseVo();

        // 盘点记录
        List<MaterialInventoryCheckListItemResponseVo> checkRecords =
                Lists.newArrayListWithCapacity(materialInventoryCheckDtos.size());
        materialInventoryCheckDtos.forEach(item -> {
            MaterialInventoryCheckListItemResponseVo temp =
                    materialInventoryCheckConverter.materialInventoryCheckListItemResponseVoFromTbObjDto(item);
            temp.setStatusDesc(MaterialInventoryCheckStatusEnum.getByCode(temp.getStatus()).getDesc());

            checkRecords.add(temp);
        });

        // 当前专业组是否存在盘点中记录
        String check = materialInventoryCheckService.isCheck(user.getGroupId());

        target.setExistInCheckFlag(StringUtils.isNotBlank(check));
        target.setCheckRecords(checkRecords);
        return target;
    }

    /**
     * 盘点记录-详情列表
     */
    @PostMapping("/detail-list")
    public Object materialInventoryDetailList(@RequestBody MaterialInventoryDetailDTO dto) {
        long checkId = dto.getCheckId();
        Boolean noInventoryFlag = dto.getNoInventoryFlag();
        Boolean haveProfitOrLoss = dto.getHaveProfitOrLoss();

        MaterialInventoryCheckDto checkDto = materialInventoryCheckService.selectByCheckId(checkId);
        if (Objects.isNull(checkDto)) {
            throw new LimsException("对应盘点记录不存在");
        }
        List<MaterialInventoryCheckDetailDto> targetList = materialInventoryCheckDetailService.selectByCheckId(checkId);

        Stream<MaterialInventoryCheckDetailDto> stream = targetList.stream();
        if (!Objects.equals(noInventoryFlag, Boolean.TRUE)) {
            // 需要过滤库存为0的物料 通过库存主数量判断
            stream = stream.filter(obj -> !Objects.equals(BigDecimal.ZERO.compareTo(obj.getMainInventory()), NumberUtils.INTEGER_ZERO));
        }
        if (Objects.equals(haveProfitOrLoss, Boolean.TRUE)) {
            // 盈亏数量不等于0的
            stream = stream.filter(obj -> BigDecimal.ZERO.compareTo(obj.getMainProfit()) !=  NumberUtils.INTEGER_ZERO
                    || BigDecimal.ZERO.compareTo(obj.getAssistProfit()) != 0);
        }

        targetList = stream.collect(Collectors.toList());

        // 收集库存id去查询对应的条码号,并更新到这个集合中
        Set<Long> inventoryIds = targetList.stream().map(MaterialInventoryCheckDetailDto::getInventoryId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(inventoryIds)){
            return Collections.emptyList();
        }
        List<MaterialInventoryDto> materialInventoryDtos = materialInventoryService.selectByInventoryIds(inventoryIds);
        Map<Long, MaterialInventoryDto> materialInventoryDtoMap = materialInventoryDtos.stream().collect(Collectors.toMap(MaterialInventoryDto::getInventoryId, Function.identity()));
        targetList.forEach(m ->{
            MaterialInventoryDto materialInventoryDto = materialInventoryDtoMap.get(m.getInventoryId());
            if (Objects.nonNull(materialInventoryDto)){
                m.setMaterialBarcode(materialInventoryDto.getMaterialBarcode());
            }
        });

        // 相同物料排在一起
        return targetList.stream().sorted(Comparator.comparing(MaterialInventoryCheckDetailDto::getMaterialCode))
                .collect(Collectors.toList());
    }

    /**
     * 盘点记录-导出
     */
    @PostMapping("/export")
    public void exportMaterialInventoryCheck(@RequestBody MaterialInventoryDetailDTO dto, HttpServletResponse response) throws IOException {
        List<MaterialInventoryCheckDetailDto> list = (List<MaterialInventoryCheckDetailDto>) materialInventoryDetailList(dto);

        if (StringUtils.isNotBlank(dto.getCodeOrName())) {
            list = list.stream().filter(e -> e.getMaterialCode().contains(dto.getCodeOrName()) ||
                    e.getMaterialName().contains(dto.getCodeOrName())).collect(Collectors.toList());
        }

        MaterialInventoryCheckDto checkDto = materialInventoryCheckService.selectByCheckId(dto.getCheckId());

        try (InputStream template = ResourceUtil.getStream("classpath:template/库存盘点单.xlsx")) {
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(template)
                    .excelType(ExcelTypeEnum.XLSX)
                    .build();

            WriteSheet sheet1 = EasyExcel.writerSheet(0).build();
            // 要遍历的表格数据
            excelWriter.fill(BeanUtil.beanToMap(checkDto), sheet1);
            excelWriter.fill(new FillWrapper("row", list), sheet1);
            excelWriter.finish();
        }
    }

    /**
     * 开始盘点
     */
    @PostMapping("/start-check")
    public Object startCheck() {
        materialInventoryCheckService.startCheck();
        return Collections.emptyMap();
    }

    /**
     * 取消盘点
     */
    @PostMapping("/cancel-check")
    public Object cancelCheck(@RequestParam("checkId") long checkId) {
        MaterialInventoryCheckDto checkDto = materialInventoryCheckService.selectByCheckId(checkId);
        if (Objects.isNull(checkDto)) {
            throw new LimsException("取消盘点失败:盘点记录不存在");
        }
        if (!Objects.equals(checkDto.getStatus(), MaterialInventoryCheckStatusEnum.IN_CHECK.getCode())) {
            throw new LimsException("取消盘点失败:只有盘点中记录可以取消");
        }

        materialInventoryCheckService.cancelCheck(checkId);
        return Collections.emptyMap();
    }

    /**
     * 结束盘点
     */
    @PostMapping("/finish-check")
    public Object finishCheck(@RequestBody MaterialInventoryCheckFinishCheckRequestVo vo) {
        if (Objects.isNull(vo.getCheckId()) || Objects.isNull(vo.getCheckDetails())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        var checkId = vo.getCheckId();
        var checkDetails = vo.getCheckDetails();
        if (checkDetails.stream().anyMatch(obj -> Objects.isNull(obj.getDetailId())
                || Objects.isNull(obj.getActualMainInventory()) || Objects.isNull(obj.getActualAssistInventory()))) {
            throw new IllegalArgumentException("存在必填项未填写");
        }

        MaterialInventoryCheckDto checkDto = materialInventoryCheckService.selectByCheckId(checkId);
        if (Objects.isNull(checkDto)) {
            throw new LimsException("结束盘点失败:盘点记录不存在");
        }
        if (!Objects.equals(checkDto.getStatus(), MaterialInventoryCheckStatusEnum.IN_CHECK.getCode())) {
            throw new LimsException("结束盘点失败:只有盘点中记录可以取消");
        }

        // key: 物料详情id value:物料详情
        Map<Long, MaterialInventoryCheckFinishCheckRequestVo.FinishCheckItemRequestVo> checkItemByDetailId =
                checkDetails.stream().collect(Collectors.toMap(
                        MaterialInventoryCheckFinishCheckRequestVo.FinishCheckItemRequestVo::getDetailId, Function.identity()));

        // 盘点记录详情
        List<MaterialInventoryCheckDetailDto> materialInventoryCheckDetailDtos =
                materialInventoryCheckDetailService.selectByCheckId(checkId);
        // key: 详情id value:详情
        Map<Long, MaterialInventoryCheckDetailDto> checkDetailByDetailId = materialInventoryCheckDetailDtos.stream()
                .collect(Collectors.toMap(MaterialInventoryCheckDetailDto::getDetailId, Function.identity()));

        if (checkItemByDetailId.keySet().stream().anyMatch(x -> !checkDetailByDetailId.containsKey(x))) {
            throw new LimsException("存在无效盘点物资");
        }

        List<MaterialInventoryCheckDetailDto> updateCheckDetailList =
                Lists.newArrayListWithCapacity(materialInventoryCheckDetailDtos.size());

        for (Map.Entry<Long,
                MaterialInventoryCheckFinishCheckRequestVo.FinishCheckItemRequestVo> entry : checkItemByDetailId
                .entrySet()) {
            Long detailId = entry.getKey();
            MaterialInventoryCheckFinishCheckRequestVo.FinishCheckItemRequestVo itemRequestVo = entry.getValue();

            MaterialInventoryCheckDetailDto checkDetailDto = checkDetailByDetailId.get(detailId);
            // 实际数量 若传入以传入为主
            BigDecimal actualMainInventory = itemRequestVo.getActualMainInventory();
            BigDecimal actualAssistInventory = itemRequestVo.getActualAssistInventory();

	        BigDecimal mainProfit = actualMainInventory.subtract(checkDetailDto.getMainInventory());
            BigDecimal assistProfit = actualAssistInventory.subtract(checkDetailDto.getAssistInventory());

            MaterialInventoryCheckDetailDto detailDto = new MaterialInventoryCheckDetailDto();
            detailDto.setDetailId(detailId);
            detailDto.setInventoryId(checkDetailDto.getInventoryId());
            detailDto.setActualMainInventory(actualMainInventory);
            detailDto.setMainProfit(mainProfit);
            detailDto.setActualAssistInventory(actualAssistInventory);
            detailDto.setAssistProfit(assistProfit);

            updateCheckDetailList.add(detailDto);

        }

        // 完成盘点
        materialInventoryCheckService.finishCheck(checkId, updateCheckDetailList);

        return Collections.emptyMap();
    }

    /**
     * 打印盘点单
     */
    @PostMapping("/print")
    public Object materialDeliveryPrint(@RequestParam(value = "checkId") long checkId) {

        MaterialInventoryCheckDto checkDto = materialInventoryCheckService.selectByCheckId(checkId);
        if (Objects.isNull(checkDto)) {
            throw new LimsException("对应盘点记录不存在");
        }
        List<MaterialInventoryCheckDetailDto> targetList = materialInventoryCheckDetailService.selectByCheckId(checkId)
                .stream().sorted(Comparator.comparing(MaterialInventoryCheckDetailDto::getMaterialCode))
                .collect(Collectors.toList());
        final PdfReportParamDto param = new PdfReportParamDto();

        param.put("inventoryCheck", checkDto);
        param.put("checkDetails", targetList);

        final String url = pdfReportService.build2Url(PdfTemplateTypeEnum.WL_PD.getCode(), param);

        return Map.of("url", url);
    }

}
