package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 等待二次分拣的样本
 */
@Getter
@Setter
public class AdvancePickSampleVo implements Serializable {

    /**
     * 开始 预分拣日期
     */
    private Date beginAdvanceDate;

    /**
     * 结束 预分拣日期
     */
    private Date endAdvanceDate;
    /**
     * 项目类型
     * <p>
     * 不传为默认 排除 微生物、院感
     * <p>
     * INFECTION 院感
     * <p>
     * MICROBIOLOGY 微生物
     * 
     */
    private String itemType;


    /**
     * 送检机构编码
     */
    private Set<String> hspOrgCodes;



}
