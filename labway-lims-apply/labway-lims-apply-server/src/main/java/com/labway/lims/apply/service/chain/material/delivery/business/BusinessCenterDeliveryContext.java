package com.labway.lims.apply.service.chain.material.delivery.business;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.BusinessCenterDeliveryDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.GroupMaterialDetailDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.Map;

/**
 * 接收业务中台出库 上下文
 *
 * <AUTHOR>
 * @since 2023/5/6 13:38
 */
@Getter
@Setter
public class BusinessCenterDeliveryContext extends StopWatchContext {

    /**
     * 接收业务中台 出库 参数
     */
    private BusinessCenterDeliveryDto deliveryDto;
    /**
     * 专业组
     */
    private ProfessionalGroupDto groupDto;

    /**
     * 获取 接收业务中台出库 信息参数 从上下文中
     */
    public static BusinessCenterDeliveryContext from(Context context) {
        return (BusinessCenterDeliveryContext)context;
    }

    // 对应 物料编码 ->对应物料信息
    public static final String MATERIAL_CODE_MATERIAL_DTO = "MATERIAL_CODE_MATERIAL_DTO_" + IdUtil.objectId();

    public Map<String, GroupMaterialDetailDto> getMaterialDtoByMaterialCode() {
        return (Map<String, GroupMaterialDetailDto>)get(MATERIAL_CODE_MATERIAL_DTO);
    }

    // 对应 物料申领单
    public static final String MATERIAL_APPLY = "MATERIAL_APPLY_" + IdUtil.objectId();

    public GroupMaterialApplyDto getGroupMaterialApply() {
        return (GroupMaterialApplyDto)get(MATERIAL_APPLY);
    }

    @Override
    protected String getWatcherName() {
        return "接收业务中台出库信息";
    }
}
