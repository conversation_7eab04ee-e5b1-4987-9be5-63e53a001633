package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Getter
@Setter
public class OnePickOrgsVo {

    /**
     * 签收日期开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginSignDate;

    /**
     * 签收日期结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSignDate;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

}
