package com.labway.lims.apply.service.chain.apply.update;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.dto.UpdateTestApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.routine.api.dto.SimpleApplyDto;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Getter
@Setter
@Component
@Slf4j
public class UpdatePostCommand implements Command {

    @Resource
    private SampleReportService sampleReportService;

    @Resource
    private ApplySampleService applySampleService;

    @DubboReference
    private SampleResultService sampleResultService;

    @Resource
    private ApplyService applyService;

    @DubboReference
    private RabbitMQService rabbitMQService;

    private static final String ROUTING_KEY = "sample_change_key";

    @Override
    public boolean execute(Context c) throws Exception {

        final UpdateApplyContext from = UpdateApplyContext.from(c);

        final ApplyDto apply = from.getApply();

        // 刷新报告单
        refreshReport(from);

        ApplyInfo applyInfo = new ApplyInfo();
        applyInfo.setApplyId(apply.getApplyId());
        applyInfo.setMasterBarcode(apply.getMasterBarcode());

        Optional.ofNullable(from.getAddApplySamples()).ifPresent(i -> {

            final List<ApplyInfo.Sample> samples = i.stream()
                    // 新增条码过滤掉当前修改条码
                    .filter(m -> !Objects.equals(m.getBarcode(), from.getBarcode()))
                    .map(m -> {
                ApplyInfo.Sample sample = new ApplyInfo.Sample();
                sample.setApplySampleId(m.getApplySampleId());
                sample.setBarcode(m.getBarcode());
                return sample;
            }).collect(Collectors.toList());
            applyInfo.setSamples(samples);
        });

        // 加减项目 增加病理项目推送到业务中台
        sendPathologyToBusinessCenter(applyInfo);

        from.put(UpdateApplyContext.APPLY_INFO, applyInfo);

        return CONTINUE_PROCESSING;
    }

    private void refreshReport(UpdateApplyContext from) {

        final ApplyDto apply = from.getApply();
        if (Objects.isNull(apply)) {
            return;
        }

        final TestApplyDto testApply = from.getTestApply();
        if (!(testApply instanceof UpdateTestApplySampleDto)) {
            return;
        }

        final UpdateTestApplySampleDto updateTestApplySample = (UpdateTestApplySampleDto) testApply;
        // 是否刷新报告
        final Boolean refreshReport = updateTestApplySample.getRefreshReport();
        final Long applyId = testApply.getApplyId();
        if (Objects.isNull(applyId)) {
            return;
        }


        // 如果改了年龄和性别那就刷新参考范围
        if (BooleanUtils.isTrue(from.getIsUpdateSexOrAge())) {
            sampleResultService.refreshReference(JSON.parseObject(JSON.toJSONString(apply), SimpleApplyDto.class));
        }

        // 如果没有修改性别或年龄，且没有审核通过的样本，不刷新报告
        List<ApplySampleDto> applySamples = applySampleService.selectByApplyId(testApply.getApplyId());

        applySamples = applySamples
                .stream()
                .filter(f -> Objects.equals(f.getStatus(), SampleStatusEnum.AUDIT.getCode()))
                .collect(Collectors.toList());

        if (BooleanUtils.isTrue(refreshReport) && CollectionUtils.isNotEmpty(applySamples)) {
            for (ApplySampleDto sample : applySamples) {
                sampleReportService.refreshReport(sample);

                final ApplySampleEventDto event = new ApplySampleEventDto();
                event.setOrgId(LoginUserHandler.get().getOrgId());

                event.setHspOrgId(apply.getHspOrgId());
                event.setHspOrgCode(apply.getHspOrgCode());
                event.setHspOrgName(sample.getHspOrgName());
                event.setApplyId(sample.getApplyId());
                event.setApplySampleId(sample.getApplySampleId());
                event.setBarcode(sample.getBarcode());
                event.setExtras(Map.of());
                event.setEvent(ApplySampleEventDto.EventType.TwoCheck);

                final String json = JSON.toJSONString(event);
                rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

                log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功",
                        sample.getApplySampleId(), sample.getBarcode(), json, RabbitMQService.EXCHANGE, ROUTING_KEY);

            }
        }
    }

    public void sendPathologyToBusinessCenter(ApplyInfo applyInfo) {
        Long applyId = applyInfo.getApplyId();
        List<ApplyInfo.Sample> samples = applyInfo.getSamples();

        if (Objects.nonNull(applyId) && CollectionUtils.isNotEmpty(samples)) {
            List<Long> applySampleIds = samples.stream().map(ApplyInfo.Sample::getApplySampleId).collect(Collectors.toList());
            applyService.sendPathologyToBusinessCenter(List.of(applyId), applySampleIds);
        }
    }
}
