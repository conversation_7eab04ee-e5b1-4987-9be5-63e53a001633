package com.labway.lims.apply.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.PhysicalBatchDto;
import com.labway.lims.apply.api.service.PhysicalBatchService;
import com.labway.lims.apply.vo.PhysicalBatchAddRequestVo;
import com.labway.lims.apply.vo.utils.PhysicalBatchNumberUtil;
import com.labway.lims.base.api.dto.PhysicalGroupDto;
import com.labway.lims.base.api.service.PhysicalGroupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 体检批次 API
 * 
 * <AUTHOR>
 * @since 2023/3/29 19:50
 */
@Slf4j
@RestController
@RequestMapping("/physical-batch")
public class PhysicalBatchController extends BaseController {

    @Resource
    private PhysicalBatchNumberUtil physicalBatchNumberUtil;

    @Resource
    private PhysicalBatchService physicalBatchService;
    @DubboReference
    private PhysicalGroupService physicalGroupService;

    /**
     * 体检批次 新增
     */
    @PostMapping("/add")
    public Object physicalBatchAdd(@RequestBody PhysicalBatchAddRequestVo vo) {
        if (Objects.isNull(vo.getPhysicalGroupId())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        PhysicalGroupDto physicalGroupDto = physicalGroupService.selectByPhysicalGroupId(vo.getPhysicalGroupId());
        if (Objects.isNull(physicalGroupDto)) {
            throw new LimsException("体检单位不存在");
        }
        // 转换
        PhysicalBatchDto target = new PhysicalBatchDto();
        target.setPhysicalBatchNumber(physicalBatchNumberUtil.getPhysicalBatchNumber());
        target.setPhysicalCompanyId(physicalGroupDto.getPhysicalGroupId());
        target.setPhysicalCompanyName(physicalGroupDto.getPhysicalGroupName());
        // 导入时间 为空-->以 DefaultDateEnum.DEFAULT_DATE 值做含义
        target.setImportDate(DefaultDateEnum.DEFAULT_DATE.getDate());

        return Map.of("id", physicalBatchService.addPhysicalBatch(target));
    }

    /**
     * 获取 体检单位 对应 体检批次
     */
    @PostMapping("/select-physical-batch-by-group")
    public Object selectPhysicalBatchByGroup(@RequestParam long physicalGroupId) {
        PhysicalGroupDto physicalGroupDto = physicalGroupService.selectByPhysicalGroupId(physicalGroupId);
        if (Objects.isNull(physicalGroupDto)) {
            throw new LimsException("体检单位不存在");
        }
        List<PhysicalBatchDto> physicalBatchDtos = physicalBatchService.selectByPhysicalCompanyId(physicalGroupId);
        physicalBatchDtos.forEach(item -> {
            // 当体检批次的导入时间 为 DefaultDateEnum.DEFAULT_DATE 时说明为此批次还未导入过数据 页面展示 应为空
            if (DefaultDateEnum.DEFAULT_DATE.getDate().equals(item.getImportDate())) {
                item.setImportDate(null);
            }
        });
        return physicalBatchDtos.stream()
            .sorted(Comparator.comparing(PhysicalBatchDto::getPhysicalBatchNumber, Comparator.reverseOrder()))
            .collect(Collectors.toList());
    }

    /**
     * 体检批次 删除
     */
    @PostMapping("/delete")
    public Object selectPhysicalBatchByGroup(@RequestBody Set<Long> physicalBatchIds) {
        if (CollectionUtils.isEmpty(physicalBatchIds)) {
            return Collections.emptyMap();
        }
        physicalBatchService.deleteByPhysicalBatchIds(physicalBatchIds);
        return Collections.emptyMap();
    }

    /**
     * 获取 体检批次 根据 批次号
     */
    @PostMapping("/select-by-physical-batch-number")
    public Object selectByPhysicalBatchNumber(@RequestParam("physicalBatchNumber") String physicalBatchNumber) {
        if (StringUtils.isBlank(physicalBatchNumber)) {
            throw new LimsException("批次号不可为空");
        }
        LoginUserHandler.User loginUser = LoginUserHandler.get();
        PhysicalBatchDto physicalBatchDto =
            physicalBatchService.selectByPhysicalBatchNumber(physicalBatchNumber, loginUser.getOrgId());
        if (Objects.isNull(physicalBatchDto)) {
            throw new LimsException("批次号对应体检批次不存在");
        }
        return physicalBatchDto;
    }

}
