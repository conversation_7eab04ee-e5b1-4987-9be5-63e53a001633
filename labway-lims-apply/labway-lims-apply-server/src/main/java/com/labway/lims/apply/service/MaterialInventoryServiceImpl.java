package com.labway.lims.apply.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.UpdateInventoryLimitDto;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import com.labway.lims.apply.mapper.TbMaterialInventoryMapper;
import com.labway.lims.apply.mapstruct.MaterialInventoryConverter;
import com.labway.lims.apply.model.TbMaterialInventory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料库存 Service impl
 *
 * <AUTHOR>
 * @since 2023/5/8 17:12
 */
@Slf4j
@DubboService
public class MaterialInventoryServiceImpl extends ServiceImpl<TbMaterialInventoryMapper, TbMaterialInventory> implements MaterialInventoryService {

    @Resource
    private TbMaterialInventoryMapper tbMaterialInventoryMapper;

    @Resource
    private MaterialInventoryConverter materialInventoryConverter;

    @Override
    public List<MaterialInventoryDto> selectByGroupId(long groupId) {

        LambdaQueryWrapper<TbMaterialInventory> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMaterialInventory::getGroupId, groupId);
        queryWrapper.eq(TbMaterialInventory::getIsDelete, YesOrNoEnum.NO.getCode());

        return materialInventoryConverter
                .fromTbMaterialInventoryList(tbMaterialInventoryMapper.selectList(queryWrapper));
    }

    @Override
    public List<MaterialInventoryDto> selectByInventoryIds(Collection<Long> inventoryIds) {
        if (CollectionUtils.isEmpty(inventoryIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMaterialInventory> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbMaterialInventory::getInventoryId, inventoryIds);
        queryWrapper.eq(TbMaterialInventory::getIsDelete, YesOrNoEnum.NO.getCode());

        return materialInventoryConverter
                .fromTbMaterialInventoryList(tbMaterialInventoryMapper.selectList(queryWrapper));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByInventoryId(MaterialInventoryDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbMaterialInventory target = materialInventoryConverter.fromMaterialInventoryDto(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbMaterialInventoryMapper.updateById(target) < 1) {
            throw new LimsException("修改物料库存失败");
        }

        log.info("用户 [{}] 修改物料库存成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }

    @Override
    public MaterialInventoryDto selectByInventoryId(long inventoryId) {
        return materialInventoryConverter.fromTbMaterialInventory(tbMaterialInventoryMapper.selectById(inventoryId));
    }

    @Override
    public List<MaterialInventoryDto> selectByGroupIdAndMaterialIds(long groupId, Collection<Long> materialIds,
                                                                    Collection<String> batchNos) {
        if (CollectionUtils.isEmpty(materialIds) || CollectionUtils.isEmpty(batchNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMaterialInventory> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMaterialInventory::getGroupId, groupId);
        queryWrapper.in(TbMaterialInventory::getMaterialId, materialIds);
        queryWrapper.in(TbMaterialInventory::getBatchNo, batchNos);
        queryWrapper.eq(TbMaterialInventory::getIsDelete, YesOrNoEnum.NO.getCode());

        return materialInventoryConverter
                .fromTbMaterialInventoryList(tbMaterialInventoryMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMaterialInventoryDtos(List<MaterialInventoryDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 物料库存
        List<TbMaterialInventory> targetList = materialInventoryConverter.fromMaterialInventoryDtoList(list);
        // 数量 分区批次插入
        List<List<TbMaterialInventory>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbMaterialInventoryMapper.batchAddMaterialInventory(item));

        log.info("用户 [{}] 新增物料库存[{}]成功", loginUser.getNickname(), JSON.toJSONString(targetList));
    }

    /**
     * 根据MaterialId去批量查询物料信息库存，但是历史记录的都要查出来
     */
    @Override
    public List<MaterialInventoryDto> selectByMaterialIds(Set<Long> materialIds, Long orgId) {
        if (CollectionUtils.isEmpty(materialIds)) {
            return Collections.emptyList();
        }

        List<TbMaterialInventory> tbMaterialInventories = tbMaterialInventoryMapper.selectList(Wrappers.lambdaQuery(TbMaterialInventory.class)
                .eq(TbMaterialInventory::getOrgId, orgId)
                .in(TbMaterialInventory::getMaterialId, materialIds));
        return JSON.parseArray(JSON.toJSONString(tbMaterialInventories), MaterialInventoryDto.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateInventoryUpperLimitAndLowerLimit(List<UpdateInventoryLimitDto> dtos) {

        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }

        // 修改的物料codes
        final Set<String> materialCodes = dtos.stream().map(UpdateInventoryLimitDto::getMaterialCode).collect(Collectors.toSet());
        // 库里的物料codes
        final List<TbMaterialInventory> tbMaterialInventories = tbMaterialInventoryMapper.selectList(new LambdaQueryWrapper<TbMaterialInventory>().in(TbMaterialInventory::getMaterialCode, materialCodes));
        final Set<String> databaseMaterialCodes = tbMaterialInventories.stream().map(TbMaterialInventory::getMaterialCode).collect(Collectors.toSet());
        // 库里的物料codes包含所有修改的物料codes
        materialCodes.removeAll(databaseMaterialCodes);
        if (CollectionUtils.isNotEmpty(materialCodes)) {
            throw new IllegalStateException(String.format("物料编码：【%s】无效", CollUtil.join(materialCodes, ",")));
        }

        final LoginUserHandler.User user = LoginUserHandler.get();
        final Date date = new Date();

        final Map<String, UpdateInventoryLimitDto> materialCodeMap = dtos.stream().collect(Collectors.toMap(UpdateInventoryLimitDto::getMaterialCode, Function.identity(), (a, b) -> b));

        final List<TbMaterialInventory> updateList = tbMaterialInventories.stream().map(e -> {
            final TbMaterialInventory tbMaterialInventory = new TbMaterialInventory();
            final UpdateInventoryLimitDto updateInventoryLimitDto = materialCodeMap.get(e.getMaterialCode());

            tbMaterialInventory.setInventoryId(e.getInventoryId());
            tbMaterialInventory.setInventoryUpperLimit(updateInventoryLimitDto.getInventoryUpperLimit());
            tbMaterialInventory.setInventoryLowerLimit(updateInventoryLimitDto.getInventoryLowerLimit());
            tbMaterialInventory.setUpdateDate(date);
            tbMaterialInventory.setUpdaterId(user.getUserId());
            tbMaterialInventory.setUpdaterName(user.getNickname());
            return tbMaterialInventory;
        }).collect(Collectors.toList());

        super.updateBatchById(updateList);

    }
}
