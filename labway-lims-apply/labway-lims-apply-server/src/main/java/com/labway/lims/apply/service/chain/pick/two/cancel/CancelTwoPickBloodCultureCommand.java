package com.labway.lims.apply.service.chain.pick.two.cancel;

import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 血培养项目需要更改名字
 */
@Slf4j
@Component
public class CancelTwoPickBloodCultureCommand implements Command, InitializingBean {
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private TestItemService testItemService;

    @Override
    public boolean execute(Context c) throws Exception {

        final CancelTwoPickContext context = CancelTwoPickContext.from(c);

        final List<ApplySampleItemDto> applySampleItems = context.getApplySampleItems();
        // 是血培养， 或者是血培养的项目
        final ApplySampleItemDto applySampleItem = applySampleItems.stream()
                .filter(e -> Objects.equals(e.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())
                        || context.getBloodCultureTestItemCodes().contains(e.getTestItemCode())).
                findFirst().orElse(null);
        if (Objects.isNull(applySampleItem)) {
            return CONTINUE_PROCESSING;
        }

        final TestItemDto testItem = testItemService.selectByTestItemId(applySampleItem.getTestItemId());
        if (Objects.isNull(testItem)) {
            throw new IllegalStateException(String.format("检验项目 [%s] 不存在", applySampleItem.getTestItemName()));
        }


        // 修改名称
        final ApplySampleItemDto m = new ApplySampleItemDto();
        m.setApplySampleItemId(applySampleItem.getApplySampleItemId());
        m.setTestItemName(testItem.getTestItemName());
        m.setItemType(testItem.getItemType());
        applySampleItemService.updateBatchById(List.of(m));


        return CONTINUE_PROCESSING;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
