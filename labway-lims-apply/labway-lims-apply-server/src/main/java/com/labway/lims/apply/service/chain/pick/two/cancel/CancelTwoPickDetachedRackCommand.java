package com.labway.lims.apply.service.chain.pick.two.cancel;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.Objects;

/**
 * 创建一个逻辑试管架 不和任何物理试管架关联
 */
@Slf4j
@Component
public class CancelTwoPickDetachedRackCommand implements Command, InitializingBean {
    @Resource
    private RackLogicService rackLogicService;


    @Resource
    private RackLogicSpaceService rackLogicSpaceService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        final CancelTwoPickContext context = CancelTwoPickContext.from(c);

        ApplySampleDto unpickApplySample = context.getUnpickApplySample();
        // 存在未二次分拣的申请单样本，不用绑定试管架
        if (Objects.nonNull(unpickApplySample)) {
            return CONTINUE_PROCESSING;
        }

        final ApplySampleDto applySample = context.getApplySamples().iterator().next();

        final LinkedList<Long> ids = snowflakeService.genIds(100);

        final RackLogicDto rackLogic = new RackLogicDto();
        rackLogic.setRackLogicId(ids.pop());
        rackLogic.setRackId(NumberUtils.LONG_ZERO);
        rackLogic.setRackCode(context.getRack().getRackCode());
        rackLogic.setRow(context.getRack().getRow());
        rackLogic.setColumn(context.getRack().getColumn());
        rackLogic.setPosition(RackLogicPositionEnum.TWO_PICKING.getCode());
        rackLogic.setCurrentGroupId(applySample.getGroupId());
        rackLogic.setCurrentGroupName(applySample.getGroupName());
        rackLogic.setNextGroupId(NumberUtils.LONG_ZERO);
        rackLogic.setNextGroupName(StringUtils.EMPTY);
        rackLogic.setLastHandover(LoginUserHandler.get().getNickname());
        rackLogicService.addRackLogic(rackLogic);

        // 添加占用
        final RackLogicSpaceDto rackLogicSpace = new RackLogicSpaceDto();
        rackLogicSpace.setRackLogicSpaceId(ids.pop());
        rackLogicSpace.setRackLogicId(rackLogic.getRackLogicId());
        rackLogicSpace.setRackId(rackLogic.getRackId());
        rackLogicSpace.setRow(0);
        rackLogicSpace.setStatus(1);
        rackLogicSpace.setColumn(0);
        rackLogicSpace.setRow(0);
        rackLogicSpace.setColumn(0);
        rackLogicSpace.setApplySampleId(applySample.getApplySampleId());

        rackLogicSpaceService.addRackLogicSpace(rackLogicSpace);

        return CONTINUE_PROCESSING;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
