package com.labway.lims.apply.service.chain.apply.update;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.apply.api.DiffApplyDto;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.Diff;
import org.apache.commons.lang3.builder.DiffResult;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Component
@Slf4j
public class UpdateFillApplyAndDiffInfoCommand implements Command {

    static final Map<String, Map<Object, Object>> convertValueMap;

    static {
        convertValueMap = new HashMap<>();

        // 性别
        final Map<Object, Object> sexMap = Arrays.stream(SexEnum.values()).collect(Collectors.toMap(SexEnum::getCode, SexEnum::getDesc, (a, b) -> a));
        final Map<Object, Object> urgentMap = Arrays.stream(UrgentEnum.values()).collect(Collectors.toMap(UrgentEnum::getCode, UrgentEnum::getValue, (a, b) -> a));

        convertValueMap.put("性别", sexMap);
        convertValueMap.put("急诊状态", urgentMap);
    }


    @Resource
    private ApplyService applyService;

    @Override
    public boolean execute(Context c) throws Exception {
        UpdateApplyContext context = UpdateApplyContext.from(c);
        final HspOrganizationDto hspOrganization = context.getHspOrganization();

        final ApplyDto apply = context.getApply();
        final TestApplyDto testApply = context.getTestApply();

        testApply.setStatus(apply.getStatus());

        // 设置修改信息
        final DiffApplyDto diffApply = fillApplyDiffInfo(testApply, hspOrganization);
        diffApply.setApplyId(apply.getApplyId());

        // 获取对比结果
        final DiffResult<ApplyDto> diff = diffApply.diff(apply);

        final String diffMsg = getDiffMsg(diff);
        context.put(UpdateApplyContext.DIFF_MSG, diffMsg);

        // 保存信息
        context.put(UpdateApplyContext.APPLY, diffApply);
        return CONTINUE_PROCESSING;
    }

    public static String getDiffMsg(DiffResult<ApplyDto> diff) {
        final List<Diff<?>> diffs = diff.getDiffs();
        if (CollectionUtils.isEmpty(diffs)) {
            return StringUtils.EMPTY;
        }
        final String diffMsg = diffs.stream().map(m -> {
            // 转换一下值 例如 当属性是性别时，将 1 转换成 男，2 转换成 女......
            m = covertValue(m);
            final String fieldName = m.getFieldName();
            final Object left = m.getLeft();
            final Object right = m.getRight();

            if (Objects.nonNull(left) && Objects.nonNull(right)) {
                return String.format("%s由 %s 修改为 %s", fieldName, left, right);
            }

            if (Objects.nonNull(left)) {
                return String.format("%s由 %s 修改为 空", fieldName, left);
            }

            if (Objects.nonNull(right)) {
                return String.format("%s由 空 修改为 %s", fieldName, right);
            }

            return StringUtils.EMPTY;
        }).collect(Collectors.joining("\n"));

        return diffMsg;
    }

    private static Diff<?> covertValue(Diff<?> m) {
        final String fieldName = m.getFieldName();
        final Map<Object, Object> convertValueMap = UpdateFillApplyAndDiffInfoCommand.convertValueMap
                .getOrDefault(fieldName, Collections.emptyMap());

        final Object left = convertValueMap.get(m.getLeft());
        final Object right = convertValueMap.get(m.getRight());

        return new Diff<>(fieldName) {
            @Override
            public Object getLeft() {
                return ObjectUtils.defaultIfNull(left, m.getLeft());
            }

            @Override
            public Object getRight() {
                return ObjectUtils.defaultIfNull(right, m.getRight());
            }
        };

    }


    /**
     * 填充申请单对比信息
     */
    public static DiffApplyDto fillApplyDiffInfo(TestApplyDto testApply, HspOrganizationDto hspOrganization) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        DiffApplyDto apply = new DiffApplyDto();

        apply.setStatus(testApply.getStatus());
        apply.setPatientName(testApply.getPatientName());
        final Integer patientAge = testApply.getPatientAge();
        if (Objects.nonNull(patientAge)) {
            apply.setPatientAge(patientAge);
        }
        final Integer patientSubage = testApply.getPatientSubage();
        if (Objects.nonNull(patientSubage)) {
            apply.setPatientSubage(testApply.getPatientSubage());
        }
        apply.setPatientSubageUnit(testApply.getPatientSubageUnit());

        final Date patientBirthday = testApply.getPatientBirthday();
        if (Objects.nonNull(testApply.getPatientBirthday())) {
            apply.setPatientBirthday(patientBirthday);
        }

        apply.setPatientCard(StringUtils.defaultString(testApply.getPatientCard()));
        apply.setPatientCardType(StringUtils.defaultString(testApply.getPatientCardType()));
        apply.setPatientBed(StringUtils.defaultString(testApply.getPatientBed()));
        apply.setPatientSex(testApply.getPatientSex());
        apply.setPatientVisitCard(StringUtils.defaultString(testApply.getPatientVisitCard()));
        apply.setPatientMobile(StringUtils.defaultString(testApply.getPatientMobile()));
        apply.setApplyTypeCode(StringUtils.defaultString(testApply.getApplyTypeCode()));
        apply.setApplyTypeName(StringUtils.defaultString(testApply.getApplyTypeName()));
        apply.setRemark(StringUtils.defaultString(testApply.getRemark()));
        apply.setSampleCount(ObjectUtils.defaultIfNull(testApply.getSampleCount(), 1));
        apply.setSampleProperty(StringUtils.defaultString(testApply.getSampleProperty()));
        apply.setSamplePropertyCode(StringUtils.defaultString(testApply.getSamplePropertyCode()));
        apply.setDept(StringUtils.defaultString(testApply.getDept()));
        apply.setDiagnosis(StringUtils.defaultString(testApply.getClinicalDiagnosis()));
        apply.setSendDoctorName(StringUtils.defaultString(testApply.getSendDoctor()));
        apply.setSendDoctorCode(StringUtils.EMPTY);
        apply.setPatientAddress(null);
        apply.setHspOrgId(hspOrganization.getHspOrgId());
        apply.setHspOrgCode(hspOrganization.getHspOrgCode());
        apply.setHspOrgName(hspOrganization.getHspOrgName());
        apply.setOrgId(user.getOrgId());
        apply.setOrgName(user.getOrgName());
        apply.setUrgent(ObjectUtils.defaultIfNull(testApply.getUrgent(), 0));
        apply.setApplyDate(testApply.getApplyDate());
        apply.setSamplingDate(testApply.getSamplingDate());
        apply.setUpdaterName(user.getNickname());
        apply.setOutBarcode(StringUtils.defaultString(testApply.getOutBarcode()));//外部条码号
        apply.setUpdaterId(user.getUserId());
        apply.setUpdateDate(new Date());

        // 1.启用 0.不启用 双输复核 如果已复核或者已双复核，不修改状态
        final Integer enableDoubleCheck = hspOrganization.getEnableDoubleCheck();
        if (Objects.equals(apply.getStatus(), ApplyStatusEnum.WAIT_CHECK.getCode()) || Objects.equals(apply.getStatus(), ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode())) {
            apply.setStatus(Objects.equals(enableDoubleCheck, YesOrNoEnum.YES.getCode()) ? ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode() : ApplyStatusEnum.WAIT_CHECK.getCode());
        }
        return apply;
    }

}
