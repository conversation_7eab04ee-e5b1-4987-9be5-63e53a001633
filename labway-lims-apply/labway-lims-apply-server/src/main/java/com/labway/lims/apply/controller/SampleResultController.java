package com.labway.lims.apply.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.Orgs;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.SampleImageService;
import com.labway.lims.apply.config.BusinessConfig;
import com.labway.lims.apply.vo.InstrumentResultReceiverVo;
import com.labway.lims.apply.vo.MedicineReceiveVo;
import com.labway.lims.apply.vo.SampleInfoVo;
import com.labway.lims.apply.vo.SampleResultSampleVo;
import com.labway.lims.apply.vo.SampleResultSamplesRequestVo;
import com.labway.lims.apply.vo.SampleResultSamplesVo;
import com.labway.lims.apply.vo.UploadSampleImageVo;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGermDto;
import com.labway.lims.base.api.dto.InstrumentMedicineDto;
import com.labway.lims.base.api.dto.InstrumentMedicineResultDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.InstrumentGermService;
import com.labway.lims.base.api.service.InstrumentMedicineService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.microbiology.api.dto.InstrumentResultReceiverDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.QueryMicrobiologySampleDto;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.routine.api.dto.MeiBiaoValueDTO;
import com.labway.lims.routine.api.dto.QuerySampleDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SaveQCResultDto;
import com.labway.lims.routine.api.dto.SaveResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.validator.routines.UrlValidator;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.apply.vo.SampleResultSampleVo.GENDER_FEMALE;
import static com.labway.lims.apply.vo.SampleResultSampleVo.GENDER_MAN;
import static com.labway.lims.apply.vo.SampleResultSampleVo.GENDER_UNKNOWN;
import static com.labway.lims.apply.vo.SampleResultSampleVo.SampleItem;
import static com.labway.lims.apply.vo.SampleResultSampleVo.SampleReportItem;

/**
 * <AUTHOR>
 * @since 2023/4/27 16:09
 */
@Slf4j
@RestController
@RequestMapping("/sample-result")
public class SampleResultController extends BaseController {
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private SampleResultService sampleResultService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private SampleService sampleService;
    @DubboReference
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private InstrumentGermService instrumentGermService;
    @DubboReference
    private InstrumentMedicineService instrumentMedicineService;
    @DubboReference
    private SampleImageService sampleImageService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @Resource
    private HttpServletRequest request;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private BusinessConfig businessConfig;


    private final UrlValidator urlValidator = new UrlValidator(new String[]{"https", "http"});

    @PostMapping("/receive")
    public Object receiveResults(@RequestBody List<InstrumentResultReceiverVo> vos) {
        // 处理登录用户
        processLoginUser();

        if (CollectionUtils.isEmpty(vos)) {
            throw new IllegalArgumentException("接收数据为空");
        }
        final InstrumentResultReceiverVo next = vos.iterator().next();

        final LoginUserHandler.User user = LoginUserHandler.get();

        final String key = redisPrefix.getBasePrefix() + getClass().getSimpleName()
                + user.getOrgId()
                + ":" + next.getBarcode()
                + ":" + next.getGroupId()
                + ":" + next.getMachineCode();

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, 60, TimeUnit.SECONDS))) {
            throw new IllegalStateException("正在通知结果,请稍后重试");
        }

        try {

            if (CollectionUtils.isEmpty(vos)) {
                return Collections.emptyMap();
            }

            // 处理元数据
            for (InstrumentResultReceiverVo vo : vos) {
                if (StringUtils.isBlank(vo.getMetadata())) {
                    throw new IllegalStateException("元数据为空");
                }

                final JSONObject query = ObjectUtils.defaultIfNull(JSON.parseObject(vo.getMetadata()).getJSONObject("query"),
                        new JSONObject(0));

                final String testType = query.getString("testType");
                if (StringUtils.isBlank(testType)) {
                    throw new IllegalStateException("testType 为空");
                }

                final String groupCode = query.getString("groupCode");
                if (StringUtils.isBlank(groupCode)) {
                    throw new IllegalStateException("groupCode 为空");
                }

                final ProfessionalGroupDto group = groupService.selectByGroupCode(groupCode, LoginUserHandler.get().getOrgId());
                if (Objects.isNull(group)) {
                    throw new IllegalStateException(String.format("专业组 [%s] 不存在", groupCode));
                }

                final ItemTypeEnum t = ItemTypeEnum.getByName(testType);
                if (Objects.isNull(t)) {
                    throw new IllegalStateException(String.format("[%s] 无法识别", testType));
                }

                vo.setGroupId(group.getGroupId());
                vo.setItemType(t.name());

            }

            final Map<String, String> errors = new LinkedHashMap<>();

            for (InstrumentResultReceiverVo vo : vos) {
                final String barcode = vo.getBarcode();
                if (StringUtils.isBlank(barcode)) {
                    errors.put(barcode, "条码号不存在");
                    continue;
                }

                if (Objects.equals(vo.getItemType(), ItemTypeEnum.ROUTINE.name())) {

                    // 质控
                    if (BooleanUtils.isTrue(vo.getIsQc())) {
                        try {
                            receiveQCResult(vo, errors);
                        } catch (Exception e) {
                            errors.put(barcode, e.getMessage());
                        }
                        continue;
                    }

                    SampleDto sample;
                    final SampleDto s = sampleService.selectAllByBarcode(barcode)
                            .stream().filter(e -> Objects.equals(e.getGroupId(), vo.getGroupId()))
                            .findFirst().orElse(null);

                    // 如果为空，根据样本号查询
                    if (Objects.isNull(s)) {
                        final QuerySampleDto dto = new QuerySampleDto();
                        //默认查询当天条码
                        dto.setTestDateStart(DateUtil.beginOfDay(vo.getDate()));
                        dto.setTestDateEnd(DateUtil.endOfDay(vo.getDate()));
                        dto.setSampleNo(barcode);
                        dto.setGroupId(vo.getGroupId());

                        final List<SampleDto> samples = sampleService.selectByTestDate(dto).stream()
                                .filter(e -> StringUtils.equals(e.getSampleNo(), barcode))
                                .filter(e -> Objects.nonNull(e.getApplySampleId()) && Objects.nonNull(e.getApplyId()))
                                .collect(Collectors.toList());

                        // @since 2024-11-28 #dev-dongguan 如果样本号查到多条记录，根据配置，再过滤一下质控样本进行传输
                        if (CollectionUtils.size(samples) > 1 &&
                                Objects.equals(YesOrNoEnum.YES.getCode(), businessConfig.getFilterQcSampleNo())) {
                            samples.removeIf(e -> Objects.equals(NumberUtils.LONG_ZERO, e.getApplyId()) &&
                                    Objects.equals(NumberUtils.LONG_ZERO, e.getApplySampleId()));
                        }

                        if (CollectionUtils.size(samples) > 1) {
                            errors.put(barcode, String.format("样本号 [%s] 查询到多条记录，无法处理", barcode));
                            return errors;
                        }

                        // 如果怎么都查询不到这个样本号，那么暂存
                        if (CollectionUtils.size(samples) == 0 ||
                                (Objects.equals(samples.get(0).getApplyId(), NumberUtils.LONG_ZERO)
                                        && Objects.equals(samples.get(0).getApplySampleId(), NumberUtils.LONG_ZERO))
                        ) {
                            try {
                                receiveTemporaryStorageResult(vo, errors);
                            } catch (Exception e) {
                                errors.put(barcode, e.getMessage());
                            }
                            continue;
                        }

                        sample = samples.iterator().next();

                    } else {
                        sample = s;
                    }

                    final List<InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService
                            .selectByInstrumentGroupId(sample.getInstrumentGroupId());

                    //查询该样本专业小组下的所有报告项目
                    final Map<String, List<InstrumentReportItemDto>> insgReportItems = instrumentReportItems.stream()
                            .filter(e -> StringUtils.isNotBlank(e.getInstrumentChannel()))
                            .collect(Collectors.groupingBy(InstrumentReportItemDto::getInstrumentChannel));

                    //样本仪器报告项目
                    final Map<String, List<InstrumentReportItemDto>> insReportMap = instrumentReportItems.stream()
                            .filter(f -> Objects.equals(f.getInstrumentId(), sample.getInstrumentId()))
                            .filter(e -> StringUtils.isNotBlank(e.getInstrumentChannel()))
                            .collect(Collectors.groupingBy(InstrumentReportItemDto::getInstrumentChannel));

                    final Map<String, SampleReportItemDto> reportItemMap = sampleReportItemService.selectBySampleId(sample.getSampleId())
                            .stream().collect(Collectors.toMap(SampleReportItemDto::getReportItemCode, v -> v, (a, b) -> a));

                    // image result
                    final List<InstrumentResultReceiverVo.ResultInfo> imageResults = new LinkedList<>();
                    final InstrumentDto instrument = instrumentService.selectByInstrumentCode(vo.getMachineCode(), user.getOrgId());

                    //保存结果
                    final LinkedList<SaveResultDto> srs = new LinkedList<>();
                    for (InstrumentResultReceiverVo.ResultInfo r : vo.getResults()) {

                        if (BooleanUtils.isTrue(r.getIsImage())) {
                            imageResults.add(r);
                            continue;
                        }

                        String reportItemCode = null;
                        List<InstrumentReportItemDto> items = insReportMap.getOrDefault(r.getName(), List.of());

                        // 从自己仪器取编码
                        if (items.stream().anyMatch(k -> reportItemMap.containsKey(k.getReportItemCode()))) {
                            reportItemCode = insReportMap.get(r.getName()).stream().filter(k -> reportItemMap.containsKey(k.getReportItemCode()))
                                    .findFirst().map(InstrumentReportItemDto::getReportItemCode).orElse(null);
                        } else {
                            // 从专业小组取编码
                            items = insgReportItems.getOrDefault(r.getName(), List.of());
                            if (items.stream().anyMatch(k -> reportItemMap.containsKey(k.getReportItemCode()))) {
                                reportItemCode = insgReportItems.get(r.getName()).stream().filter(k -> reportItemMap.containsKey(k.getReportItemCode()))
                                        .findFirst().map(InstrumentReportItemDto::getReportItemCode).orElse(null);
                            }
                        }

                        if (StringUtils.isBlank(reportItemCode)) {
                            log.info("条码号/样本号 [{}] 未关联此 [{}] 报告项目，跳过通知", barcode, r.getName());
                            errors.put(String.format("%s/%s", barcode, r.getName()),
                                    String.format("样本没有做 [%s] 报告项目，跳过通知", r.getName()));
                            continue;
                        }

                        final SampleReportItemDto sri = reportItemMap.get(reportItemCode);
                        if (Objects.isNull(sri)) {
                            log.info("条码号/样本号 [{}] 未关联此 [{}] 报告项目，跳过通知", barcode, r.getName());
                            errors.put(String.format("%s/%s", barcode, r.getName()),
                                    String.format("样本没有做 [%s] 报告项目，跳过通知", r.getName()));
                            continue;
                        }

                        final SaveResultDto sr = new SaveResultDto();
                        if (Objects.nonNull(instrument)) {
                            sr.setInstrumentId(instrument.getInstrumentId());
                            sr.setInstrumentName(instrument.getInstrumentName());
                            sr.setInstrumentCode(instrument.getInstrumentCode());
                        }
                        sr.setSampleId(sample.getSampleId());
                        sr.setApplySampleId(sample.getApplySampleId());
                        sr.setApplyId(sample.getApplyId());
                        sr.setReportItemId(sri.getReportItemId());
                        sr.setReportItemCode(sri.getReportItemCode());
                        sr.setResult(r.getResult());
                        // 检验时间
                        sr.setDate(ObjectUtils.defaultIfNull(r.getDate(), vo.getDate()));
                        if (StringUtils.isNotBlank(r.getOdValue()) || StringUtils.isNotBlank(r.getScoValue())) {
                            MeiBiaoValueDTO meiBiaoValueDTO = new MeiBiaoValueDTO();
                            meiBiaoValueDTO.setODValue(r.getOdValue());
                            meiBiaoValueDTO.setScoValue(r.getScoValue());
                            meiBiaoValueDTO.setCutoffValue(r.getCutoffValue());
                            sr.setExtraInfo(JSON.toJSONString(meiBiaoValueDTO));
                        }
                        if (StringUtils.isNotBlank(r.getResultDesc())) {
                            sr.setResultDesc(r.getResultDesc());
                        }
                        srs.add(sr);
                    }

                    // 图片结果
                    processRoutineImages(sample, imageResults);

                    sampleResultService.saveResults(srs, SaveResultSourceEnum.MACHINE);

                } else if (Objects.equals(vo.getItemType(), ItemTypeEnum.MICROBIOLOGY.name())) {


                    final QueryMicrobiologySampleDto dto = new QueryMicrobiologySampleDto();

                    dto.setSampleNo(barcode);
                    dto.setGroupId(vo.getGroupId());
                    //微生物默认查询近3个月样本
                    dto.setTestDateStart(DateUtils.addMonths(new Date(), -3));
                    dto.setTestDateEnd(DateUtil.endOfDay(new Date()));

                    final List<MicrobiologySampleDto> sampleDtos = microbiologySampleService.selectByTestDate(dto).stream()
                            .filter(e -> StringUtils.equals(e.getSampleNo(), barcode)).collect(Collectors.toList());

                    if (CollectionUtils.size(sampleDtos) > 1) {
                        errors.put(barcode, String.format("样本号 [%s] 查询到多条记录，无法处理", barcode));
                        return errors;
                    }

                    if (CollectionUtils.size(sampleDtos) == 0) {
                        errors.put(barcode, "条码号不存在");
                        return errors;
                    }

                    final InstrumentDto instrument = instrumentService.selectByGroupIdAndInstrumentCode(vo.getGroupId(), vo.getMachineCode());
                    if (Objects.isNull(instrument)) {
                        errors.put(barcode, String.format("仪器 [%s] 不存在", vo.getMachineCode()));
                        return errors;
                    }

                    List<InstrumentGermDto> instrumentGerms = instrumentGermService.selectByInstrumentId(instrument.getInstrumentId());
                    if (CollectionUtils.isEmpty(vo.getResults())) {
                        errors.put(barcode, "当前样本结果为空");
                        continue;
                    }

                    if (CollectionUtils.isEmpty(instrumentGerms)) {
                        errors.put(barcode, String.format("仪器 [%s] 下没有维护细菌", vo.getMachineCode()));
                        continue;
                    }

                    final List<InstrumentMedicineDto> instrumentMedicines = instrumentMedicineService.selectByInstrumentId(instrument.getInstrumentId());

                    if (CollectionUtils.isEmpty(instrumentMedicines)) {
                        errors.put(barcode, "当前仪器下没有维护药物");
                        continue;
                    }

                    final LinkedHashMap<InstrumentGermDto, List<InstrumentMedicineResultDto>> germMap = new LinkedHashMap<>();

                    final Map<String, InstrumentGermDto> instrumentGermMap = instrumentGerms.stream()
                            .collect(Collectors.toMap(InstrumentGermDto::getInstrumentChannel, v -> v, (a, b) -> a));

                    final Map<String, InstrumentMedicineDto> instrumentMedicineMap = instrumentMedicines.stream()
                            .collect(Collectors.toMap(InstrumentMedicineDto::getInstrumentChannel, v -> v, (a, b) -> a));

                    for (InstrumentResultReceiverVo.ResultInfo result : vo.getResults()) {
                        final InstrumentGermDto germ = instrumentGermMap.get(result.getName());
                        if (Objects.isNull(germ)) {
                            errors.put(barcode + "/" + result.getName(), String.format("细菌 [%s] 未维护通道号, ", result.getName()));
                            continue;
                        }
                        germMap.put(germ, new LinkedList<>());
                        final List<MedicineReceiveVo> medicineReceives = JSON.parseArray(result.getResult(), MedicineReceiveVo.class);
                        if (CollectionUtils.isEmpty(medicineReceives)) {
                            errors.put(barcode + "/" + germ.getGermCode(), String.format("细菌 [%s] 当前细菌没药物结果为空, ", germ.getGermCode()));
                            continue;
                        }

                        for (MedicineReceiveVo medicineReceive : medicineReceives) {
                            //细菌备注
                            if (StringUtils.equalsIgnoreCase(medicineReceive.getName(), "GermRemark")) {
                                final InstrumentMedicineResultDto m = new InstrumentMedicineResultDto();
                                m.setName(medicineReceive.getName());
                                m.setResult(medicineReceive.getResult());
                                germMap.get(germ).add(m);
                                continue;
                            }

                            final InstrumentMedicineDto medicine = instrumentMedicineMap.get(medicineReceive.getAb());
                            if (Objects.isNull(medicine)) {
                                errors.put(barcode + "/" + medicineReceive.getAb(), String.format("药物 [%s] 未维护通道号跳过通知, ", medicineReceive.getName()));

                                continue;
                            }
                            final InstrumentMedicineResultDto medicineResult = JSON.parseObject(JSON.toJSONString(medicine), InstrumentMedicineResultDto.class);
                            medicineResult.setGroup(medicineReceive.getGroup());
                            medicineResult.setAb(medicineReceive.getAb());
                            medicineResult.setName(medicineReceive.getName());
                            medicineResult.setFormula(medicineReceive.getFormula());
                            medicineResult.setSusceptibility(medicineReceive.getSusceptibility());
                            medicineResult.setResult(medicineReceive.getResult());
                            medicineResult.setFoldPointScope(medicineReceive.getFoldPointScope());
                            germMap.get(germ).add(medicineResult);
                        }

                    }

                    try {
                        final InstrumentResultReceiverDto resultReceiver = JSON.parseObject(JSON.toJSONString(vo), InstrumentResultReceiverDto.class);
                        resultReceiver.setGermMap(germMap);
                        resultReceiver.setMicrobiologySampleId(sampleDtos.iterator().next().getMicrobiologySampleId());
                        microbiologyGermMedicineService.receive(resultReceiver);
                    } catch (Exception e) {
                        log.error("保存微生物结果错误", e);
                        errors.put(barcode, e.getMessage());
                        return Map.of("errors", errors);
                    }
                }

            }
            return Map.of("errors", errors);

        } finally {
            stringRedisTemplate.delete(key);
        }

    }

    private void processRoutineImages(SampleDto sample, List<InstrumentResultReceiverVo.ResultInfo> imageResults) {
        if (CollectionUtils.isEmpty(imageResults)) {
            return;
        }

        try {

            // 删除重复的
            sampleImageService.deleteByImageNames(imageResults.stream().map(InstrumentResultReceiverVo.ResultInfo::getName)
                    .collect(Collectors.toSet()), sample.getSampleId());

            final LinkedList<Long> ids = snowflakeService.genIds(imageResults.size());
            final StringBuilder sb = new StringBuilder();
            final AtomicInteger count = new AtomicInteger(0);

            // 添加图片
            sampleImageService.addSampleImage(imageResults.stream().map(e -> {
                final SampleImageDto k = new SampleImageDto();
                k.setSampleImageId(ids.pop());
                k.setApplyId(sample.getApplyId());
                k.setSampleId(sample.getSampleId());
                k.setApplySampleId(sample.getApplySampleId());
                k.setItemType(ItemTypeEnum.ROUTINE.name());
                k.setImageName(e.getName());
                k.setImageUrl(e.getResult());
                k.setIsDelete(YesOrNoEnum.NO.getCode());
                k.setCreateDate(new Date());
                k.setUpdateDate(new Date());
                k.setCreatorId(LoginUserHandler.get().getUserId());
                k.setCreatorName(LoginUserHandler.get().getNickname());
                k.setUpdaterId(LoginUserHandler.get().getUserId());
                k.setUpdaterName(LoginUserHandler.get().getNickname());
                sb.append(count.incrementAndGet()).append(". ").append(e.getName()).append(" : ").append(e.getResult()).append("\n");
                return k;
            }).collect(Collectors.toList()));

            // 图片条码环节
            sampleFlowService.addSampleFlow(SampleFlowDto.builder().applyId(sample.getApplyId()).applySampleId(sample.getApplySampleId())
                    .barcode(sample.getBarcode()).operateCode(BarcodeFlowEnum.SAMPLE_IMAGE.name())
                    .operateName(BarcodeFlowEnum.SAMPLE_IMAGE.getDesc())
                    .content(String.format("仪器上传图片\n%s", sb)).build());
        } catch (Exception e) {
            log.error("保存样本 [{}] 结果图片失败,原因: {}", sample.getBarcode(), e.getMessage(), e);
        }

    }

    private void receiveQCResult(InstrumentResultReceiverVo vo, Map<String, String> errors) {

        final List<InstrumentDto> instruments = instrumentService.selectByGroupId(vo.getGroupId());

        final InstrumentDto instrument = instruments.stream()
                .filter(f -> Objects.equals(f.getInstrumentCode(), vo.getMachineCode()))
                .findFirst()
                .orElse(null);

        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("当前专业组下不存在该仪器");
        }

        // 处理不同项目，同一个通道号，不同质控批号的情况
        final Map<String, String> channel2ReportItemCode = new HashMap<>();
        vo.getResults().forEach(e -> {
            if (StringUtils.isNotBlank(e.getReportItemCode())) {
                channel2ReportItemCode.put(e.getName(), e.getReportItemCode());
            }
        });

        final Map<String, InstrumentReportItemDto> insReportMap = instrumentReportItemService.selectByInstrumentId(instrument.getInstrumentId())
                .stream()
                // 过滤质控项目
                .filter(e -> Objects.equals(YesOrNoEnum.YES.getCode(), e.getIsQc()))
                //通道号为空的跳过
                .filter(e -> StringUtils.isNotBlank(e.getInstrumentChannel()))
                .collect(Collectors.toMap(InstrumentReportItemDto::getInstrumentChannel, v -> v, (a, b) -> {
                    // 根据通道号获取到配置的报告项目编码
                    final String exceptReportItemCode = channel2ReportItemCode.get(b.getInstrumentChannel());
                    if (b.getReportItemCode().equals(exceptReportItemCode)) {
                        return b;
                    }
                    return a;
                }));

        final LinkedList<SaveQCResultDto> qcResults = new LinkedList<>();
        for (InstrumentResultReceiverVo.ResultInfo result : vo.getResults()) {

            final InstrumentReportItemDto reportItem = insReportMap.get(result.getName());
            if (Objects.isNull(reportItem)) {
                log.info("条码号/样本号 [{}] 未关联此 [{}] 报告项目，跳过通知", vo.getBarcode(), result.getName());
                errors.put(String.format("条码号/样本号 [%s] 通道号 [%s]", vo.getBarcode(), result.getName()),
                        String.format("未关联此 [%s] 报告项目，跳过通知", result.getName()));
                continue;
            }

            final SaveQCResultDto qcResult = new SaveQCResultDto();
            qcResult.setSampleId(NumberUtils.LONG_ZERO);
            qcResult.setDate(ObjectUtils.defaultIfNull(result.getDate(), vo.getDate()));
            qcResult.setInstrumentId(instrument.getInstrumentId());
            qcResult.setInstrumentCode(instrument.getInstrumentCode());
            qcResult.setInstrumentName(instrument.getInstrumentName());
            qcResult.setReportItemCode(reportItem.getReportItemCode());
            qcResult.setResult(result.getResult());
            qcResult.setSampleNo(vo.getBarcode());
            qcResult.setGroupId(vo.getGroupId());
            qcResult.setIsQc(vo.getIsQc());
            qcResult.setSource(SaveResultSourceEnum.MACHINE);
            qcResults.add(qcResult);
        }
        sampleResultService.receiveQCResults(qcResults);
    }

    /**
     * 暂存结果
     */
    private void receiveTemporaryStorageResult(InstrumentResultReceiverVo vo, Map<String, String> errors) {

        final List<InstrumentDto> instruments = instrumentService.selectByGroupId(vo.getGroupId());

        final InstrumentDto instrument = instruments.stream()
                .filter(f -> Objects.equals(f.getInstrumentCode(), vo.getMachineCode()))
                .findFirst()
                .orElse(null);

        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("当前专业组下不存在该仪器");
        }

        final Map<String, InstrumentReportItemDto> insReportMap = instrumentReportItemService.selectByInstrumentId(instrument.getInstrumentId())
                .stream()
                //通道号为空的跳过
                .filter(e -> StringUtils.isNotBlank(e.getInstrumentChannel()))
                .collect(Collectors.toMap(InstrumentReportItemDto::getInstrumentChannel, v -> v, (a, b) -> a));

        final LinkedList<SaveQCResultDto> qcResults = new LinkedList<>();
        for (InstrumentResultReceiverVo.ResultInfo result : vo.getResults()) {

            final InstrumentReportItemDto reportItem = insReportMap.get(result.getName());
            if (Objects.isNull(reportItem)) {
                log.info("条码号/样本号 [{}] 未关联此 [{}] 报告项目，跳过通知", vo.getBarcode(), result.getName());
                errors.put(String.format("条码号/样本号 [%s] 通道号 [%s]", vo.getBarcode(), result.getName()),
                        String.format("未关联此 [%s] 报告项目，跳过通知", result.getName()));
                continue;
            }

            final SaveQCResultDto qcResult = new SaveQCResultDto();
            qcResult.setSampleId(NumberUtils.LONG_ZERO);
            qcResult.setDate(ObjectUtils.defaultIfNull(result.getDate(), vo.getDate()));
            qcResult.setInstrumentId(instrument.getInstrumentId());
            qcResult.setInstrumentCode(instrument.getInstrumentCode());
            qcResult.setInstrumentName(instrument.getInstrumentName());
            qcResult.setReportItemCode(reportItem.getReportItemCode());
            qcResult.setResult(result.getResult());
            qcResult.setSampleNo(vo.getBarcode());
            qcResult.setGroupId(vo.getGroupId());
            qcResult.setIsQc(false);
            qcResult.setSource(SaveResultSourceEnum.MACHINE);
            if (StringUtils.isNotBlank(result.getOdValue()) || StringUtils.isNotBlank(result.getScoValue())) {
                MeiBiaoValueDTO meiBiaoValueDTO = new MeiBiaoValueDTO();
                meiBiaoValueDTO.setODValue(result.getOdValue());
                meiBiaoValueDTO.setScoValue(result.getScoValue());
                qcResult.setExtraInfo(JSON.toJSONString(meiBiaoValueDTO));
            }
            qcResults.add(qcResult);
        }

        sampleResultService.receiveQCResults(qcResults);
    }

    /**
     * 获取这个专业组正在检验的样本
     */
    @PostMapping("/samples")
    public List<SampleResultSamplesVo> samples(@RequestBody SampleResultSamplesRequestVo vo) {

        processLoginUser();

        if (Objects.isNull(vo.getItemType())) {
            throw new IllegalArgumentException("ItemType 不能为空");
        }

        Long groupId = null;
        if (StringUtils.isNotBlank(vo.getGroupCode())) {
            final ProfessionalGroupDto group = groupService.selectByGroupCode(vo.getGroupCode(),
                    LoginUserHandler.get().getOrgId());
            if (Objects.isNull(group)) {
                throw new IllegalArgumentException(String.format("专业组 [%s] 不存在", vo.getGroupCode()));
            }
            groupId = group.getGroupId();
        }


        final List<SampleResultSamplesVo> list = new LinkedList<>();
        final Date now = new Date();

        if (vo.getItemType() == ItemTypeEnum.ROUTINE) {
            // 近24小时
            final List<SampleDto> samples = new ArrayList<>(sampleService.selectByCreateDate(DateUtils.addDays(now, -1)
                    , now, groupId));

            final Map<Long, InstrumentDto> instruments = instrumentService.selectByInstrumentIdsAsMap(samples.stream()
                    .map(SampleDto::getInstrumentId).collect(Collectors.toSet()));
            final Map<Long, ProfessionalGroupDto> groups = groupService.selectByGroupIdsAsMap(samples.stream()
                    .map(SampleDto::getGroupId).collect(Collectors.toSet()));

            if (CollectionUtils.isNotEmpty(samples)) {
                for (SampleDto sample : samples) {
                    final SampleResultSamplesVo v = new SampleResultSamplesVo();
                    v.setBarcode(sample.getBarcode());
                    v.setMachineCode(Optional.ofNullable(instruments.get(sample.getInstrumentId()))
                            .map(InstrumentDto::getInstrumentCode).orElse(sample.getInstrumentId().toString()));
                    v.setGroupCode(Optional.ofNullable(groups.get(sample.getGroupId()))
                            .map(ProfessionalGroupDto::getGroupCode).orElse(sample.getGroupId().toString()));
                    v.setApplySampleId(sample.getApplySampleId());
                    list.add(v);
                }
            }
        }

        if (StringUtils.isNotBlank(vo.getGroupCode())) {
            list.removeIf(e -> !Objects.equals(e.getGroupCode(), vo.getGroupCode()));
        }

        if (StringUtils.isNotBlank(vo.getMachineCode())) {
            list.removeIf(e -> !Objects.equals(e.getMachineCode(), vo.getMachineCode()));
        }

        return list;
    }

    /**
     * 样本信息
     */
    @RequestMapping("/sample-info")
    public Object sampleInfo(String barcode, String machineCode, String metadata) {

        // 处理登录用户
        processLoginUser();

        if (StringUtils.isBlank(metadata)) {
            throw new IllegalStateException("缺少 metadata 参数");
        }

        if (StringUtils.isBlank(barcode)) {
            throw new IllegalStateException("缺少 barcode 参数");
        }

        metadata = URLDecoder.decode(metadata, StandardCharsets.UTF_8);

        final JSONObject query = ObjectUtils.defaultIfNull(JSON.parseObject(metadata).getJSONObject("query"),
                new JSONObject(0));

        final ProfessionalGroupDto group = groupService.selectByGroupCode(query.getString("groupCode"),
                LoginUserHandler.get().getOrgId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException(String.format("专业组 [%s] 不存在", query.getString("groupCode")));
        }

        final String testType = query.getString("testType");
        if (StringUtils.isBlank(testType)) {
            throw new IllegalStateException("testType 为空");
        }

        if (Objects.isNull(EnumUtils.getEnum(ItemTypeEnum.class, testType))) {
            throw new IllegalStateException(String.format("testType [%s] 无法识别", testType));
        }

	    final List<SampleDto> samples = sampleService.selectAllByBarcode(barcode);
	    final SampleDto sample = samples.stream().filter(e -> Objects.equals(e.getGroupId(), group.getGroupId())).findFirst().orElse(null);
//        if (samples.size() != 1) {
//            throw new IllegalStateException(String.format("根据条码号 [%s] 查询到的数量为 [%s]", barcode, samples.size()));
//        }
//
//        final SampleDto sample = samples.iterator().next();
	    if (Objects.isNull(sample)) {
		    throw new IllegalStateException(String.format("该专业组条码号 [%s] 不存在", barcode));
	    }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException(String.format("条码 [%s] 申请单样本不存在", barcode));
        }

        final ApplyDto apply = applyService.selectByApplyId(sample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException(String.format("条码 [%s] 申请单不存在", barcode));
        }


        final SampleResultSampleVo vo = new SampleResultSampleVo();
        vo.setBarcode(barcode);
        vo.setSampleNo(sample.getSampleNo());
        vo.setOrgId(String.valueOf(apply.getOrgId()));
        vo.setSendDoctor(StringUtils.defaultString(apply.getSendDoctorName()));
        vo.setSendDept(StringUtils.defaultString(apply.getDept()));
        vo.setSendDate(apply.getApplyDate());
        vo.setPatientName(StringUtils.defaultString(apply.getPatientName()));
        if (!Objects.equals(apply.getPatientBirthday(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
            vo.setBirthday(apply.getPatientBirthday());
        }
        vo.setGender(GENDER_UNKNOWN);
        if (Objects.equals(apply.getPatientSex(), SexEnum.MAN.getCode())) {
            vo.setGender(GENDER_MAN);
        } else if (Objects.equals(apply.getPatientSex(), SexEnum.WOMEN.getCode())) {
            vo.setGender(GENDER_FEMALE);
        }
        vo.setHospitalNumber(apply.getPatientVisitCard());
        vo.setHospitalBedNumber(apply.getPatientBed());
        vo.setSampleType(applySample.getSampleTypeName());
        vo.setCreateDate(applySample.getCreateDate());
        vo.setSampleReportItems(new LinkedList<>());
        vo.setSampleItems(new LinkedList<>());
        vo.getExtras().set("_apply", apply);
        vo.getExtras().set("_applySample", applySample);
        vo.getExtras().set("_sample", sample);

        // 专业小组下所有仪器的
        final Map<String, InstrumentReportItemDto> reportItems = instrumentReportItemService.selectByInstrumentGroupId(sample.getInstrumentGroupId())
                .stream().collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v -> v, (a, b) -> a));
        // 单个仪器下的
        final Map<String, InstrumentReportItemDto> instrumentReportItems = new HashMap<>(instrumentReportItemService.selectByInstrumentId(sample.getInstrumentId())
                .stream().collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v -> v, (a, b) -> a))) {
            @Override
            public InstrumentReportItemDto get(Object key) {
                if (containsKey(key)) {
                    return super.get(key);
                }
                return reportItems.get(key);
            }
        };

        // 检验项目
        for (ApplySampleItemDto e : applySampleItemService.selectByApplySampleId(sample.getApplySampleId())) {
            final SampleItem sampleItem = new SampleItem();
            sampleItem.setName(e.getTestItemName());
            sampleItem.setCode(e.getTestItemCode());
            sampleItem.getExtras().set("_applySampleItem", e);
            vo.getSampleItems().add(sampleItem);
        }

        final Map<String, SampleResultDto> results = sampleResultService.selectBySampleIdAsMap(sample.getSampleId());

        // 报告项目
        for (SampleReportItemDto e : sampleReportItemService.selectBySampleId(sample.getSampleId())) {
            final InstrumentReportItemDto instrumentReportItem = instrumentReportItems.get(e.getReportItemCode());
            if (Objects.isNull(instrumentReportItem)) {
                log.warn("条码 [{}] 在仪器专业小组或仪器中没有找到报告项目 [{}]", barcode, e.getReportItemCode());
                continue;
            }
            final SampleReportItem sampleReportItem = new SampleReportItem();
            sampleReportItem.setName(e.getReportItemName());
            sampleReportItem.setInstrumentCode(instrumentReportItem.getInstrumentCode());
            sampleReportItem.setChannelCode(instrumentReportItem.getInstrumentChannel());
            sampleReportItem.setType(instrumentReportItem.getResultTypeCode());
            sampleReportItem.getExtras().set("_instrumentReportItem", instrumentReportItem);
            sampleReportItem.getExtras().set("_sampleReportItem", e);
            sampleReportItem.getExtras().set("_result", results.get(e.getReportItemCode()));
            vo.getSampleReportItems().add(sampleReportItem);
        }

        // 添加流水
        sampleFlowService.addSampleFlow(
                SampleFlowDto.builder().applyId(applySample.getApplyId()).applySampleId(applySample.getApplySampleId())
                        .barcode(barcode).operateCode(BarcodeFlowEnum.MACHINE_SEEN.name())
                        .operateName(BarcodeFlowEnum.MACHINE_SEEN.getDesc())
                        .content(String.format("仪器 [%s] 扫描条码", machineCode)).build());


        return vo;
    }

    @PostMapping("/sample-info")
    public Object sampleInfo(@RequestBody SampleInfoVo sampleInfoVo) {
        // 处理登录用户
        processLoginUser();

        if (StringUtils.isBlank(sampleInfoVo.getMetadata())) {
            throw new IllegalStateException("缺少 metadata 参数");
        }

        if (CollectionUtils.isEmpty(sampleInfoVo.getBarcodes())) {
            throw new IllegalStateException("缺少 barcode 参数");
        }

        final String machineCode = sampleInfoVo.getMachineCode();
        final String metadata = URLDecoder.decode(sampleInfoVo.getMetadata(), StandardCharsets.UTF_8);

        final JSONObject query = ObjectUtils.defaultIfNull(JSON.parseObject(metadata).getJSONObject("query"),
                new JSONObject(0));

        // 判断登录用户专业组
        final ProfessionalGroupDto group = groupService.selectByGroupCode(query.getString("groupCode"),
                LoginUserHandler.get().getOrgId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException(String.format("专业组 [%s] 不存在", query.getString("groupCode")));
        }

        // 检验类型
        final String testType = query.getString("testType");
        if (StringUtils.isBlank(testType)) {
            throw new IllegalStateException("testType 为空");
        }

        if (Objects.isNull(EnumUtils.getEnum(ItemTypeEnum.class, testType))) {
            throw new IllegalStateException(String.format("testType [%s] 无法识别", testType));
        }

        // 根据条码号批量查询样本信息
        List<SampleDto> sampleDtos = sampleService.selectAllByBarcodes(sampleInfoVo.getBarcodes());
        Map<String, List<SampleDto>> sampleDtoMapByBarcode = sampleDtos.stream().collect(Collectors.groupingBy(SampleDto::getBarcode));
        Set<String> barcodesWithNoSample = new HashSet<>();
        for (Map.Entry<String, List<SampleDto>> entry : sampleDtoMapByBarcode.entrySet()) {
            final String barcode = entry.getKey();
            if (CollectionUtils.isEmpty(entry.getValue()) || entry.getValue().size() != 1) {
                log.error(String.format("根据条码号 [%s] 查询到的数量为 [%s]", barcode, entry.getValue().size()));
                barcodesWithNoSample.add(barcode);
            }
        }
        barcodesWithNoSample.forEach(barcode -> sampleDtoMapByBarcode.remove(barcode));

        // 有效的样本
        List<SampleDto> sampleDtos2 = sampleDtoMapByBarcode.values().stream().flatMap(e -> e.stream()).collect(Collectors.toList());
        Map<Long, List<SampleDto>> sampleDtoMapBySampleId = sampleDtos2.stream().collect(Collectors.groupingBy(SampleDto::getSampleId));
        Map<Long, List<SampleDto>> sampleDtoMapByApplySampleId = sampleDtos2.stream().collect(Collectors.groupingBy(SampleDto::getApplySampleId));
        Map<Long, List<SampleDto>> sampleDtoMapByApplyId = sampleDtos2.stream().collect(Collectors.groupingBy(SampleDto::getApplyId));
        Function<List<SampleDto>, String> getBarcodes = e -> {
            if (CollectionUtils.isEmpty(e)) {
                return "";
            }
            return e.stream().map(SampleDto::getBarcode).collect(Collectors.joining(","));
        };

        // 样本id
        Set<Long> sampleIds = sampleDtoMapByBarcode.values().stream().flatMap(e -> e.stream().map(SampleDto::getSampleId)).collect(Collectors.toSet());
        // 申请单样本id
        Set<Long> applySampleIds = sampleDtoMapByBarcode.values().stream().flatMap(e -> e.stream().map(SampleDto::getApplySampleId)).collect(Collectors.toSet());
        // 申请单id
        Set<Long> applyIds = sampleDtoMapByBarcode.values().stream().flatMap(e -> e.stream().map(SampleDto::getApplyId)).collect(Collectors.toSet());
        // 专业小组
        Set<Long> instrumentGroupIds = sampleDtoMapByBarcode.values().stream().flatMap(e -> e.stream().map(SampleDto::getInstrumentGroupId)).collect(Collectors.toSet());
        // 仪器id
        Set<Long> instrumentIds = sampleDtoMapByBarcode.values().stream().flatMap(e -> e.stream().map(SampleDto::getInstrumentId)).collect(Collectors.toSet());

        // 申请单样本
        CompletableFuture<Map<Long, ApplySampleDto>> applySampleDtoMapAsyncFuture = CompletableFuture.supplyAsync(() -> {
            Map<Long, ApplySampleDto> applySampleDtoMap = applySampleService.selectByApplySampleIdsAsMap(applySampleIds);
            for (Long applySampleId : applySampleIds) {
                if (Objects.isNull(applySampleDtoMap.get(applySampleId))) {
                    log.error(String.format("条码 [%s] 申请单样本不存在", getBarcodes.apply(sampleDtoMapByApplySampleId.get(applySampleId))));
                }
            }
            return applySampleDtoMap;
        });

        // 申请单
        CompletableFuture<Map<Long, ApplyDto>> applyDtoMapAsyncFuture = CompletableFuture.supplyAsync(() -> {
            Map<Long, ApplyDto> applyDtoMap = applyService.selectByApplyIdsAsMap(applyIds);
            for (Long applyId : applyIds) {
                if (Objects.isNull(applyDtoMap.get(applyId))) {
                    log.error(String.format("条码 [%s] 申请单不存在", getBarcodes.apply(sampleDtoMapByApplyId.get(applyId))));
                }
            }
            return applyDtoMap;
        });

        // 检验项目
        CompletableFuture<Map<Long, List<SampleItem>>> sampleItemMapAsyncFuture = CompletableFuture.supplyAsync(() -> {
            Map<Long, List<ApplySampleItemDto>> applySampleItemDtoMap = applySampleItemService.selectByApplySampleIdsAsMap(applySampleIds);

            Map<Long, List<SampleItem>> sampleItemMap = new HashMap<>();
            applySampleItemDtoMap.forEach((applySampleId, applySampleItemDtos) -> {
                sampleItemMap.put(applySampleId, applySampleItemDtos.stream().map(e -> {
                    final SampleItem sampleItem = new SampleItem();
                    sampleItem.setName(e.getTestItemName());
                    sampleItem.setCode(e.getTestItemCode());
                    sampleItem.getExtras().set("_applySampleItem", e);
                    return sampleItem;
                }).collect(Collectors.toList()));
            });
            return sampleItemMap;
        });

        // 仪器报告项目
        CompletableFuture<Map<String, InstrumentReportItemDto>> instrumentReportItemMapAsyncFuture = CompletableFuture.supplyAsync(() -> {
            // 专业小组下所有仪器的
            final Map<String, InstrumentReportItemDto> reportItems = instrumentReportItemService.selectByInstrumentGroupIds(instrumentGroupIds)
                    .stream().collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v -> v, (a, b) -> a));
            // 单个仪器下的
            final Map<String, InstrumentReportItemDto> instrumentReportItems = new HashMap<>(instrumentReportItemService.selectByInstrumentIds(instrumentIds)
                    .stream().collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v -> v, (a, b) -> a))) {
                @Override
                public InstrumentReportItemDto get(Object key) {
                    if (containsKey(key)) {
                        return super.get(key);
                    }
                    return reportItems.get(key);
                }
            };

            return instrumentReportItems;
        });

        // 报告项目
        CompletableFuture<Map<Long, List<SampleReportItem>>> sampleReportItemMapAsyncFuture = instrumentReportItemMapAsyncFuture.thenApplyAsync((instrumentReportItemMap) -> {
            final Map<String, SampleResultDto> results = sampleResultService.selectBySampleIds(sampleIds).stream()
                    .collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a));

            Map<Long, List<SampleReportItemDto>> sampleReportItemMapBySampleId =
                    sampleReportItemService.selectBySampleIds(sampleIds).stream()
                            .collect(Collectors.groupingBy(SampleReportItemDto::getSampleId));

            Map<Long, List<SampleReportItem>> sampleReportItemMap = new HashMap<>(sampleIds.size());

            sampleReportItemMapBySampleId.forEach((sampleId, sampleReportItemDtos) -> {
                // 报告项目
                for (SampleReportItemDto e : sampleReportItemDtos) {
                    final InstrumentReportItemDto instrumentReportItem = instrumentReportItemMap.get(e.getReportItemCode());
                    if (Objects.isNull(instrumentReportItem)) {
                        log.warn("条码 [{}] 在仪器专业小组或仪器中没有找到报告项目 [{}]", getBarcodes.apply(sampleDtoMapBySampleId.get(sampleId)), e.getReportItemCode());
                        continue;
                    }
                    final SampleReportItem sampleReportItem = new SampleReportItem();
                    sampleReportItem.setName(e.getReportItemName());
                    sampleReportItem.setInstrumentCode(instrumentReportItem.getInstrumentCode());
                    sampleReportItem.setChannelCode(instrumentReportItem.getInstrumentChannel());
                    sampleReportItem.setType(instrumentReportItem.getResultTypeCode());
                    sampleReportItem.getExtras().set("_instrumentReportItem", instrumentReportItem);
                    sampleReportItem.getExtras().set("_sampleReportItem", e);
                    sampleReportItem.getExtras().set("_result", results.get(e.getReportItemCode()));
                    sampleReportItemMap.computeIfAbsent(sampleId, k -> new ArrayList<>()).add(sampleReportItem);
                }
            });

            return sampleReportItemMap;
        });

        try {
            CompletableFuture.allOf(
                    applySampleDtoMapAsyncFuture,
                    applyDtoMapAsyncFuture,
                    instrumentReportItemMapAsyncFuture,
                    sampleItemMapAsyncFuture,
                    sampleReportItemMapAsyncFuture).get();

            Map<Long, ApplySampleDto> applySampleDtoMap = applySampleDtoMapAsyncFuture.get();
            Map<Long, ApplyDto> applyDtoMap = applyDtoMapAsyncFuture.get();
            Map<Long, List<SampleItem>> sampleItemMap = sampleItemMapAsyncFuture.get();
            Map<Long, List<SampleReportItem>> sampleReportItemMap = sampleReportItemMapAsyncFuture.get();

            List<SampleFlowDto> sampleFlowDtos = new ArrayList<>(sampleDtos2.size());
            List<SampleResultSampleVo> sampleResultSamples = new ArrayList<>(sampleDtos2.size());
            for (SampleDto sample : sampleDtos2) {
                String barcode = sample.getBarcode();
                Long sampleId = sample.getSampleId();
                Long applySampleId = sample.getApplySampleId();
                Long applyId = sample.getApplyId();

                ApplyDto apply = applyDtoMap.get(applyId);
                if (Objects.isNull(apply)) {
                    log.error(String.format("条码 [%s] 申请单不存在", barcode));
                    continue;
                }
                ApplySampleDto applySample = applySampleDtoMap.get(applySampleId);
                if (Objects.isNull(applySample)) {
                    log.error(String.format("条码 [%s] 申请单样本不存在", barcode));
                    continue;
                }
                List<SampleItem> sampleItems = sampleItemMap.get(applySampleId);
                if (CollectionUtils.isEmpty(sampleItems)) {
                    log.error(String.format("条码 [%s] 检验项目为空", barcode));
                    continue;
                }
                List<SampleReportItem> sampleReportItems = sampleReportItemMap.get(sampleId);

                final SampleResultSampleVo vo = new SampleResultSampleVo();
                vo.setBarcode(barcode);
                vo.setSampleNo(sample.getSampleNo());
                vo.setOrgId(String.valueOf(apply.getOrgId()));
                vo.setSendDoctor(StringUtils.defaultString(apply.getSendDoctorName()));
                vo.setSendDept(StringUtils.defaultString(apply.getDept()));
                vo.setSendDate(apply.getApplyDate());
                vo.setPatientName(StringUtils.defaultString(apply.getPatientName()));
                if (!Objects.equals(apply.getPatientBirthday(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
                    vo.setBirthday(apply.getPatientBirthday());
                }
                vo.setGender(GENDER_UNKNOWN);
                if (Objects.equals(apply.getPatientSex(), SexEnum.MAN.getCode())) {
                    vo.setGender(GENDER_MAN);
                } else if (Objects.equals(apply.getPatientSex(), SexEnum.WOMEN.getCode())) {
                    vo.setGender(GENDER_FEMALE);
                }
                vo.setHospitalNumber(apply.getPatientVisitCard());
                vo.setHospitalBedNumber(apply.getPatientBed());
                vo.setSampleType(applySample.getSampleTypeName());
                vo.setCreateDate(applySample.getCreateDate());
                vo.setSampleReportItems(new LinkedList<>());
                vo.setSampleItems(new LinkedList<>());
                vo.getExtras().set("_apply", apply);
                vo.getExtras().set("_applySample", applySample);
                vo.getExtras().set("_sample", sample);

                // 检验项目
                vo.setSampleItems(sampleItems);
                // 报告项目
                vo.setSampleReportItems(sampleReportItems);

                sampleResultSamples.add(vo);

                // 添加流水
                sampleFlowDtos.add(
                        SampleFlowDto.builder().applyId(applySample.getApplyId()).applySampleId(applySample.getApplySampleId())
                                .barcode(barcode).operateCode(BarcodeFlowEnum.MACHINE_SEEN.name())
                                .operateName(BarcodeFlowEnum.MACHINE_SEEN.getDesc())
                                .content(String.format("仪器 [%s] 扫描条码", machineCode)).build());
            }

            sampleFlowService.addSampleFlows(sampleFlowDtos);

            return sampleResultSamples;
        } catch (Exception e) {
            log.error("根据条码号 [{}}] 批量查询样本信息异常 \n", String.join(StringPool.COMMA, sampleInfoVo.getBarcodes()), e);
            throw new IllegalStateException(String.format("根据条码号 [%s] 批量查询样本信息异常 ", String.join(StringPool.COMMA, sampleInfoVo.getBarcodes())));
        }
    }

    /**
     * 样本信息
     */
    @PostMapping("/flow")
    public Object flow(@RequestBody Dict params) {

        // 处理登录用户
        processLoginUser();

        final String barcode = params.getStr("barcode");
        final String operateCode = params.getStr("operateCode");
        final String operateName = params.getStr("operateName");
        final String operateContent = params.getStr("operateContent");

        final List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(barcode);
        for (ApplySampleDto applySample : applySamples) {
            // 添加流水
            sampleFlowService.addSampleFlow(
                    SampleFlowDto.builder().applyId(applySample.getApplyId()).applySampleId(applySample.getApplySampleId())
                            .barcode(applySample.getBarcode()).operateCode(StringUtils.defaultString(operateCode))
                            .operateName(StringUtils.defaultString(operateName))
                            .content(StringUtils.defaultString(operateContent)).build());
        }


        return Map.of();
    }

    /**
     * 上传图片信息
     *
     * @param sampleImageVo
     * @return
     */
    @PostMapping("/uploadRoutineSampleImage")
    public Object uploadRoutineSampleImage(@RequestBody UploadSampleImageVo sampleImageVo) {

        ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleImageVo.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("申请单样本不存在");
        }
        Integer status = applySampleDto.getStatus();
        if (Objects.equals(SampleStatusEnum.ONE_AUDIT.getCode(), status) ||
                Objects.equals(SampleStatusEnum.AUDIT.getCode(), status)) {
            throw new IllegalStateException("样本已经审核，不能上传图片");
        }

        LoginUserHandler.User user = LoginUserHandler.get();
        //常规检验上传图片
        Date createDate = new Date();
        SampleImageDto entity = new SampleImageDto();
        entity.setSampleImageId(snowflakeService.genId());
        entity.setApplyId(sampleImageVo.getApplyId());
        entity.setSampleId(sampleImageVo.getSampleId());
        entity.setApplySampleId(sampleImageVo.getApplySampleId());
        entity.setImageUrl(sampleImageVo.getImageUrl());
        entity.setItemType(ItemTypeEnum.ROUTINE.name());
        entity.setImageName(sampleImageVo.getImageName());
        entity.setCreateDate(createDate);
        entity.setUpdateDate(createDate);
        entity.setCreatorId(user.getUserId());
        entity.setCreatorName(user.getUsername());
        entity.setUpdaterId(user.getUserId());
        entity.setUpdaterName(user.getUsername());
        entity.setIsDelete(YesOrNoEnum.NO.getCode());
        List<SampleImageDto> addList = Lists.newArrayList();
        addList.add(entity);
        sampleImageService.addSampleImage(addList);
        return Map.of("sampleImageId", entity.getSampleImageId());
    }

    /**
     * 删除图片信息
     *
     * @param sampleImageVo
     * @return
     */
    @PostMapping("/deleteSampleImage")
    public Object deleteSampleImage(@RequestBody UploadSampleImageVo sampleImageVo) {
        ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleImageVo.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("申请单样本不存在");
        }
        Integer status = applySampleDto.getStatus();
        if (Objects.equals(SampleStatusEnum.ONE_AUDIT.getCode(), status) ||
                Objects.equals(SampleStatusEnum.AUDIT.getCode(), status)) {
            throw new IllegalStateException("样本已经审核，不能删除图片");
        }
        sampleImageService.deleteSampleImage(sampleImageVo.getSampleImageId());
        return Map.of();
    }

    /**
     * 查询图片
     */
    @GetMapping("listSampleImages")
    public Object listSampleImages(@RequestParam Long sampleId) {
        if (Objects.isNull(sampleId)) {
            throw new IllegalArgumentException("样本不能为空");
        }

        List<SampleImageDto> sampleImageDtos = sampleImageService.selectSampleImageBySampleId(sampleId);

        return Map.of("images", sampleImageDtos);
    }

    private void processLoginUser() {
        final String ua = request.getHeader(HttpHeaders.USER_AGENT);
        if (StringUtils.isBlank(ua)) {
            throw new IllegalStateException("无权限访问");
        }

        final String orgId = (String) Optional.ofNullable(ArrayUtil.get(ua.split(","), 3))
                .map(e -> (String) e).map(e -> ArrayUtil.get(e.split("/"), 0)).orElse(null);
        if (StringUtils.isBlank(orgId)) {
            throw new IllegalStateException("无权限访问");
        }

        final Orgs org = Orgs.getOrgByOrgCode(NumberUtils.toInt(orgId));


        final LoginUserHandler.User user = new LoginUserHandler.User();
        user.setOrgId(org.getOrgCode());
        user.setOrgCode(String.valueOf(org.getOrgCode()));
        user.setOrgName(org.getOrgName());
        user.setUserId(NumberUtils.LONG_ZERO);
        user.setUsername("machine");
        user.setNickname("仪器");

        LoginUserHandler.set(user);
    }


}
