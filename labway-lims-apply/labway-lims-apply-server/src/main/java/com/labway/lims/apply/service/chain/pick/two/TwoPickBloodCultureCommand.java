package com.labway.lims.apply.service.chain.pick.two;

import cn.hutool.core.util.IdUtil;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.ApplySampleItemBloodCultureService;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.labway.lims.api.SampleNoUtils.addOneToLastNumber;

/**
 * 血培养分拣
 */
@Slf4j
@Component
public class TwoPickBloodCultureCommand implements Command, Filter, InitializingBean, DisposableBean {
    private static final String BC_APPLY_SAMPLE_ID = "BC_APPLY_SAMPLE_ID" + IdUtil.objectId();
    /**
     * 缺少血培养样本号
     */
    public static final int NO_LIMB_SAMPLE_NO_ERROR = 1003;

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private ApplySampleItemBloodCultureService applySampleItemBloodCultureService;

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    /**
     * 涉及到修改,不进行事务处理
     */
    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);

        if (!isBloodCulture(context)) {
            return CONTINUE_PROCESSING;
        }

        final ApplySampleItemDto item = getBloodCultureApplySampleItem(context);
        if (Objects.isNull(item)) {
            return CONTINUE_PROCESSING;
        }

        final ApplySampleItemBloodCultureDto applySampleItemBloodCulture =
                applySampleItemBloodCultureService.selectByApplySampleItemId(item.getApplySampleItemId());
        if (Objects.isNull(applySampleItemBloodCulture)) {
            throw new IllegalStateException("获取血培养项目信息失败");
        }

        final LinkedList<Limb> limbs = new LinkedList<>(limbs(applySampleItemBloodCulture));

        // 如果只有一个 那么不需要分多个样本
        if (CollectionUtils.isEmpty(limbs)) {
            return CONTINUE_PROCESSING;
        }

        // 如果需要自定义了样本号 且需要拆成多个 血培养样本 那么对应血培养样本号也需要传进来 正常只有微生物二次分拣需要
        final Map<String, String> sampleNoByLimbCode = new HashMap<>();
        if (context.getTwoPick() instanceof BloodCultureTwoPickDto) {
            BloodCultureTwoPickDto twoPick = (BloodCultureTwoPickDto) context.getTwoPick();
            if (StringUtils.isNotBlank(twoPick.getSampleNo()) && limbs.size() > 1) {
                // 未传入 血培养样本号 或存在无效样本号
                boolean flag = CollectionUtils.isEmpty(twoPick.getLimbSampleNos()) || twoPick.getLimbSampleNos()
                        .stream().anyMatch(obj -> StringUtils.isAnyBlank(obj.getLimbCode(), obj.getSampleNo())
                                || Objects.isNull(Limb.getByName(obj.getLimbCode())));
                if (!flag) {
                    // 是否缺失 样本号
                    sampleNoByLimbCode.putAll(twoPick.getLimbSampleNos().stream()
                            .collect(Collectors.toMap(LimbSampleDto::getLimbCode, LimbSampleDto::getSampleNo)));
                    flag = limbs.stream().anyMatch(obj -> !sampleNoByLimbCode.containsKey(obj.name()));
                }

                if (flag) {
                    final List<LimbErrorData> errorDataList = Lists.newArrayListWithCapacity(limbs.size());
                    String sampleNo = twoPick.getSampleNo();
                    for (Limb temp : limbs) {
                        LimbErrorData errorData = new LimbErrorData();
                        errorData.setLimbCode(temp.name());
                        errorData.setLimbName(temp.desc);
                        errorData.setBarcode(context.getApplySample().getBarcode());
                        errorData.setTestItemName(String.format("%s(%s)", item.getTestItemName(), temp.desc));
                        errorData.setSampleNo(sampleNo);
                        errorDataList.add(errorData);
                        // 样本号加1
                        sampleNo = addOneToLastNumber(sampleNo);
                    }
                    throw new LimsCodeException(NO_LIMB_SAMPLE_NO_ERROR, "请输入血培养样本号").setData(errorDataList);
                }
            }
        }

        // 专业组下 此时已有 一条样本 修改其样本号
        String firstSampleNo = sampleNoByLimbCode.get(limbs.getFirst().name());
        if (StringUtils.isNotBlank(firstSampleNo)) {
            context.getApplySampleTwoPicks().get(NumberUtils.INTEGER_ZERO).setSampleNo(firstSampleNo);
        }

        final ApplySampleItemDto m = new ApplySampleItemDto();
        m.setApplySampleItemId(applySampleItemBloodCulture.getApplySampleItemId());
        m.setTestItemName(String.format("%s(%s)", item.getTestItemName(), limbs.removeFirst().desc));
        applySampleItemService.updateBatchById(List.of(m));

        ApplySampleDto originalApplySample = context.getApplySample();
        // 设置样本性状
        setSampleType(item,originalApplySample,context.getTwoPick().getItemPropertyRelationList());

        if (CollectionUtils.isEmpty(limbs)) {
            // 如果只有一个血培养项目 更新原始样本-更新样本性状信息
            applySampleService.updateByApplySampleId(originalApplySample);
            return CONTINUE_PROCESSING;
        }

        final LinkedList<Long> ids = snowflakeService.genIds(3000);

        // 复制样本
        Map<Long, String> sampleNoByApplySampleId = new HashMap<>();

        final List<ApplySampleDto> applySamples = limbs.stream().map(e -> {
            final ApplySampleDto applySample = new ApplySampleDto();
            BeanUtils.copyProperties(originalApplySample, applySample);
            applySample.setApplySampleId(ids.pop());
            sampleNoByApplySampleId.put(applySample.getApplySampleId(), sampleNoByLimbCode.get(e.name()));

            return applySample;
        }).collect(Collectors.toList());

        // 复制项目
        final List<ApplySampleItemDto> items = new LinkedList<>();
        final List<ApplySampleItemBloodCultureDto> bloodCultureItems = new LinkedList<>();
        for (int i = 0; i < applySamples.size(); i++) {
            final ApplySampleDto applySample = applySamples.get(i);
            final Limb limb = limbs.get(i);

            final ApplySampleItemDto nItem = new ApplySampleItemDto();
            BeanUtils.copyProperties(item, nItem);
            nItem.setApplySampleItemId(ids.pop());
            nItem.setApplySampleId(applySample.getApplySampleId());
            nItem.setTestItemName(String.format("%s(%s)", nItem.getTestItemName(), limb.desc));
            items.add(nItem);

            final ApplySampleItemBloodCultureDto bc = new ApplySampleItemBloodCultureDto();
            BeanUtils.copyProperties(applySampleItemBloodCulture, bc);
            bc.setApplySampleItemBloodCultureId(ids.pop());
            bc.setApplySampleItemId(nItem.getApplySampleItemId());
            bc.setApplySampleId(nItem.getApplySampleId());
            bc.setTestItemName(nItem.getTestItemName());
            bloodCultureItems.add(bc);

        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        // 事务外执行
        threadPoolConfig.getPool().submit(() -> {
            LoginUserHandler.set(user);
            try {
                // 更新原始样本-更新样本性状信息
                applySampleService.updateByApplySampleId(originalApplySample);

                // 新增申请单样本
                applySampleService.addApplySamples(applySamples);

                // 新增项目
                applySampleItemService.addApplySampleItems(items);

                // 新增血培养
                applySampleItemBloodCultureService.addApplySampleItemBloodCultures(bloodCultureItems);

                // 复制条码环节
                sampleFlowService.copySampleFlows(context.getTwoPick().getApplySampleId(),
                        applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));
            } finally {
                LoginUserHandler.remove();
            }
        }).get(10, TimeUnit.SECONDS);

        for (ApplySampleDto applySample : applySamples) {
            final ApplySampleTwoPickDto stp = new ApplySampleTwoPickDto();
            stp.setGroupId(applySample.getGroupId());
            stp.setApplyId(applySample.getApplyId());
            stp.setApplySampleId(applySample.getApplySampleId());
            stp.setInstrumentGroupId(context.getInstrumentGroup().getInstrumentGroupId());
            stp.setInstrumentGroupName(context.getInstrumentGroup().getInstrumentGroupName());
            stp.setSampleNo(StringUtils.defaultString(sampleNoByApplySampleId.get(applySample.getApplySampleId())));
            stp.setBarcode(context.getApplySample().getBarcode());
            stp.setIsTransform(false);
            stp.setIsUrgent(false);
            context.getApplySampleTwoPicks().add(stp);
        }

        context.put(BC_APPLY_SAMPLE_ID,
                applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));

        return CONTINUE_PROCESSING;
    }

    /**
     * 是否是血培养
     */
    public boolean isBloodCulture(TwoPickContext context) {
        return Objects.nonNull(getBloodCultureApplySampleItem(context));
    }

    /**
     * 获取血培养项目
     */
    @Nullable
    public ApplySampleItemDto getBloodCultureApplySampleItem(TwoPickContext context) {
        // 外送的跳过
        if (Objects.equals(context.getApplySample().getIsOutsourcing(), YesOrNoEnum.YES.getCode())) {
            return null;
        }

        final List<ApplySampleItemDto> items = context.getApplySampleItems().stream()
                .filter(e -> Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(items)) {
            throw new IllegalStateException(
                    String.format("当前条码无法在 [%s] 专业组分拣,因为此条码下的项目没有该专业组", LoginUserHandler.get().getGroupName()));
        }

        // 获取到血培养项目
        final ApplySampleItemDto item = items.stream()
                .filter(e -> Objects.equals(e.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())).findFirst()
                .orElse(null);
        // 为空表示没有血培养
        if (Objects.isNull(item)) {
            return null;
        }

        return item;
    }

    private List<Limb> limbs(ApplySampleItemBloodCultureDto c) {
        final List<Limb> limbs = new ArrayList<>();
        if (Objects.nonNull(c.getLulAnaerobic()) && c.getLulAnaerobic() > 0) {
            limbs.add(Limb.LUL_ANAEROBIC);
        }
        if (Objects.nonNull(c.getLulAerobic()) && c.getLulAerobic() > 0) {
            limbs.add(Limb.LUL_AEROBIC);
        }
        if (Objects.nonNull(c.getLulPediatricBottle()) && c.getLulPediatricBottle() > 0) {
            limbs.add(Limb.LUL_PEDIATRIC_BOTTLE);
        }

        if (Objects.nonNull(c.getLllAnaerobic()) && c.getLllAnaerobic() > 0) {
            limbs.add(Limb.LLL_ANAEROBIC);
        }
        if (Objects.nonNull(c.getLllAerobic()) && c.getLllAerobic() > 0) {
            limbs.add(Limb.LLL_AEROBIC);
        }
        if (Objects.nonNull(c.getLllPediatricBottle()) && c.getLllPediatricBottle() > 0) {
            limbs.add(Limb.LLL_PEDIATRIC_BOTTLE);
        }

        if (Objects.nonNull(c.getRulAnaerobic()) && c.getRulAnaerobic() > 0) {
            limbs.add(Limb.RUL_ANAEROBIC);
        }
        if (Objects.nonNull(c.getRulAerobic()) && c.getRulAerobic() > 0) {
            limbs.add(Limb.RUL_AEROBIC);
        }
        if (Objects.nonNull(c.getRulPediatricBottle()) && c.getRulPediatricBottle() > 0) {
            limbs.add(Limb.RUL_PEDIATRIC_BOTTLE);
        }

        if (Objects.nonNull(c.getRllAnaerobic()) && c.getRllAnaerobic() > 0) {
            limbs.add(Limb.RLL_ANAEROBIC);
        }
        if (Objects.nonNull(c.getRllAerobic()) && c.getRllAerobic() > 0) {
            limbs.add(Limb.RLL_AEROBIC);
        }
        if (Objects.nonNull(c.getRllPediatricBottle()) && c.getRllPediatricBottle() > 0) {
            limbs.add(Limb.RLL_PEDIATRIC_BOTTLE);
        }

        if (Objects.nonNull(c.getAnaerobic()) && c.getAnaerobic() > 0) {
            limbs.add(Limb.ANAEROBIC);
        }
        if (Objects.nonNull(c.getAerobic()) && c.getAerobic() > 0) {
            limbs.add(Limb.AEROBIC);
        }
        if (Objects.nonNull(c.getPediatricBottle()) && c.getPediatricBottle() > 0) {
            limbs.add(Limb.PEDIATRIC_BOTTLE);
        }

        return limbs;
    }


    // 设置微生物样本性状
    private void setSampleType( ApplySampleItemDto item, ApplySampleDto applySample, List<MicrobiologyTwoPickDto.MicrobiologyItemPropertyRelationDto> itemPropertyRelationList) {
        if (org.springframework.util.CollectionUtils.isEmpty(itemPropertyRelationList)) {
            return;
        }

        // 匹配获取用户选择的样本性状
        MicrobiologyTwoPickDto.MicrobiologyItemPropertyRelationDto microbiologyItemPropertyRelationDto = itemPropertyRelationList.stream().filter(relation -> Objects.equals(item.getTestItemCode(), relation.getTestItemCode())).findFirst().orElse(null);
        if (Objects.isNull(microbiologyItemPropertyRelationDto)) {
            return;
        }

        applySample.setSamplePropertyCode(microbiologyItemPropertyRelationDto.getPropertyCode());
        applySample.setSampleProperty(microbiologyItemPropertyRelationDto.getPropertyName());
    }


    @Override
    @SuppressWarnings("unchecked")
    public boolean postprocess(Context c, Exception exception) {
        if (Objects.isNull(exception)) {
            return CONTINUE_PROCESSING;
        }

        final Object o = c.get(BC_APPLY_SAMPLE_ID);
        if (!(o instanceof Collection)) {
            return CONTINUE_PROCESSING;
        }

        try {
            // 事务外执行
            threadPoolConfig.getPool().submit(() -> {
                applySampleService.deleteByApplySampleIds((Collection<Long>) o);
                log.info("二次分拣血培养样本 [{}] 失败，删除生成的申请单样本 {} 成功", TwoPickContext.from(c).getApplySample().getBarcode(), o);
            }).get(1, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("二次分拣血培养样本 [{}] 失败", TwoPickContext.from(c).getApplySample().getBarcode(), e);
        }

        return CONTINUE_PROCESSING;
    }

    @Override
    public void destroy() throws Exception {
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }

    /**
     * 血培养样本号错误
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class LimbErrorData implements Serializable {
        /**
         * 血培养 肢体信息
         *
         * @see Limb
         */
        private String limbCode;
        /**
         * 血培养 肢体信息
         *
         * @see Limb
         */
        private String limbName;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 条码号
         */
        private String barcode;

        /**
         * 样本号
         */
        private String sampleNo;

    }

    /**
     * 肢体信息
     */
    @AllArgsConstructor
    public enum Limb {
        LUL_ANAEROBIC("左上肢厌氧"),

        LUL_AEROBIC("左上肢需氧"),

        LUL_PEDIATRIC_BOTTLE("左上肢儿童瓶"),

        LLL_ANAEROBIC("左下肢厌氧"),

        LLL_AEROBIC("左下肢需氧"),

        LLL_PEDIATRIC_BOTTLE("左下肢儿童瓶"),

        RUL_ANAEROBIC("右上肢厌氧"),

        RUL_AEROBIC("右上肢需氧"),

        RUL_PEDIATRIC_BOTTLE("右上肢儿童瓶"),

        RLL_ANAEROBIC("右下肢厌氧"),

        RLL_AEROBIC("右下肢需氧"),

        RLL_PEDIATRIC_BOTTLE("右下肢儿童瓶"),

        ANAEROBIC("厌氧"),

        AEROBIC("需氧"),

        PEDIATRIC_BOTTLE("儿童瓶"),
        ;

        private final String desc;

        public static Limb getByName(String name) {
            for (Limb limbEnum : Limb.values()) {
                if (Objects.equals(limbEnum.name(), name)) {
                    return limbEnum;
                }
            }
            return null;
        }
    }

}
