package com.labway.lims.apply.controller.report;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.BloodCultureInspectionDto;
import com.labway.lims.apply.api.dto.es.GeneticsInspectionDto;
import com.labway.lims.apply.api.dto.es.InfectionInspectionDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyInspectionDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.api.dto.es.SpecialtyInspectionDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleImageService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.apply.mapstruct.OpenReportConvert;
import com.labway.lims.apply.vo.OpenReportSampleVo;
import com.labway.lims.apply.vo.ReportGroupVo;
import com.labway.lims.apply.vo.openreport.OpenReportBaseSampleEsModelVo;
import com.labway.lims.apply.vo.openreport.OpenReportMicrobiologyInspectionVoOpenReport;
import com.labway.lims.apply.vo.openreport.OpenReportOutsourcingInspectionVoOpenReport;
import com.labway.lims.apply.vo.openreport.OpenReportRoutineInspectionVoOpenReport;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.dto.MatchBindReportDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.api.client.ReportService;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 提供给报告平台的接口
 */
@Slf4j
@RestController
@RequestMapping("/open-report")
public class OpenReportController extends BaseController {

    // 分血后缀标识
    private static final String SPLIT_BLOOD_SUFFIX = "_";
    // 合并文件临时目录
    private static final String MERGE_TEMP_DIR = System.getProperty("user.dir") + "/merge_temp";
    // 合并文件前缀
    private static final String MERGE_FILE_PREFIX = "lims_merge_pdf";
    // pdf文件后缀
    private static final String PDF_SUFFIX = ".pdf";

    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private SampleReportService sampleReportService;
	@Resource
	private OpenReportConvert openReportConvert;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private SpecialtySampleService specialtySampleService;
    @DubboReference
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private GeneticsSampleService geneticsSampleService;
    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private BloodCultureSampleService bloodCultureSampleService;
    @Resource
    private SampleImageService sampleImageService;
    @DubboReference
    private SystemParamService systemParamService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @DubboReference
    private ReportService reportService;
	@DubboReference
	private InstrumentReportItemReferenceService instrumentReportItemReferenceService;

	@DubboReference
	private ElasticSearchSampleService elasticSearchSampleService;

	@DubboReference
	private InstrumentService instrumentService;


    /**
     * 获取申请单信息
     */
    @GetMapping("/apply")
    public Object apply(Long orgId, String hspOrgCode, String barcode) {

        List<ApplySampleDto> applySamples = new ArrayList<>(applySampleService.selectByOriginalBarcode(barcode))
                .stream().filter(e -> Objects.equals(e.getOrgId(), orgId) &&
                        Objects.equals(e.getHspOrgCode(), hspOrgCode)).collect(Collectors.toList());

        // 校验分血合并样本
        applySamples = validCanReport(orgId, hspOrgCode, barcode, applySamples);

        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException(String.format("条码 [%s] 不存在", barcode));
        }


        final ApplySampleDto applySample = applySamples.iterator().next();
        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalArgumentException("申请单不存在");
        }

        final Dict dict = Dict.parse(apply);
        dict.put("barcode", barcode);
        dict.put("outBarcode", applySample.getOutBarcode());
        dict.put("hspOrgCode", applySample.getHspOrgCode());
        dict.put("tubeName", applySample.getTubeName());
        dict.put("tubeCode", applySample.getTubeCode());
        dict.put("sampleTypeName", applySample.getSampleTypeName());
        dict.put("sampleTypeCode", applySample.getSampleTypeCode());

        return dict;
    }

	@GetMapping("/samples-new")
	public List<OpenReportBaseSampleEsModelVo> samplesNew(Long orgId, String hspOrgCode, String barcode, String itemType) {
		log.info("时间[{}] 同步检验机构[{}]下送检机构[{}]-[{}]条码号-[{}]", DateUtil.now(), orgId, hspOrgCode, ItemTypeEnum.getByName(itemType).getDesc(), barcode);

		if (Objects.isNull(orgId)) {
			throw new IllegalArgumentException("请选择送检机构");
		}
		if (StringUtils.isAnyBlank(hspOrgCode, barcode, itemType)) {
			throw new IllegalArgumentException("请确认[送检机构][条码号][项目类型]均不为空");
		}
		//根据原始条码来查
		List<ApplySampleDto> applySamples = applySampleService.selectByOriginalBarcode(barcode)
				.stream().filter(e -> Objects.equals(e.getOrgId(), orgId) &&
						Objects.equals(e.getHspOrgCode(), hspOrgCode)).collect(Collectors.toList());

		// 校验分血合并样本
		applySamples = validCanReport(orgId, hspOrgCode, barcode, applySamples);
		if (CollectionUtils.isEmpty(applySamples)) {
			return Collections.emptyList();
		}

		// 删除未审核的
		applySamples.removeIf(e -> !Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode()));

		if (CollectionUtils.isEmpty(applySamples)) {
			return Collections.emptyList();
		}

		// 所有的样本编号
		Set<Long> applySampleIds = applySamples.stream().map(ApplySampleDto::getApplySampleId)
				.collect(Collectors.toSet());

        final List<BaseSampleEsModelDto> baseSampleEsModel = getBaseSampleEsModelDtos(orgId, hspOrgCode, applySampleIds);

        if (CollectionUtils.isEmpty(baseSampleEsModel)) {
			log.error("时间[{}] 同步检验机构[{}]下送检机构[{}]-[{}]条码号-[{}] ES中未查询到样本信息", DateUtil.now(), orgId, hspOrgCode, ItemTypeEnum.getByName(itemType).getDesc(), barcode);
			throw new IllegalStateException("ES中未查询到样本信息");
		}

		List<OpenReportBaseSampleEsModelVo> resultDto = new ArrayList<>(applySampleIds.size());
		for (BaseSampleEsModelDto baseSampleEsModelDto : baseSampleEsModel) {

			ItemTypeEnum type = ItemTypeEnum.getByName(baseSampleEsModelDto.getItemType());
			switch (type) {
				case GENETICS: {
					GeneticsInspectionDto geneticsInspectionDto = (GeneticsInspectionDto) baseSampleEsModelDto;
					break;
				}
				case ROUTINE: {
					RoutineInspectionDto routineInspectionDto = (RoutineInspectionDto) baseSampleEsModelDto;
					OpenReportRoutineInspectionVoOpenReport result = openReportConvert.convertEsRoutineResult2OpenReportResult(routineInspectionDto);
					// 需求处理 https://www.tapd.cn/tapd_fe/48047159/story/detail/1148047159001002168?menu_workitem_type_id=0
					Set<Long> instrumentIds = result.getReportItems().stream().map(OpenReportRoutineInspectionVoOpenReport.RoutineReportItem::getInstrumentId).collect(Collectors.toSet());
					Map<Long, InstrumentDto> instrumentDtoMap = instrumentService.selectByInstrumentIds(instrumentIds).stream()
							.collect(Collectors.toMap(InstrumentDto::getInstrumentId, Function.identity()));

					Set<Long> instrumentReportItemReferenceIds = result.getReportItems().stream().map(OpenReportBaseSampleEsModelVo.ReportItem::getInstrumentReportItemReferenceId).collect(Collectors.toSet());
					Map<Long, InstrumentReportItemReferenceDto> instrumentReportItemReferenceMap = instrumentReportItemReferenceService.selectByIds(instrumentReportItemReferenceIds)
							.stream().collect(Collectors.toMap(InstrumentReportItemReferenceDto::getInstrumentReportItemReferenceId, Function.identity()));

					// 样本 仪器专业小组 对应 报告项目
					final List<InstrumentReportItemDto> instrumentReportItems =
							instrumentReportItemService.selectByInstrumentGroupId(result.getInstrumentGroupId());
					Map<String, InstrumentReportItemDto> instrumentReportItemByKey = instrumentReportItems.stream().collect(
							Collectors.toMap(obj -> obj.getInstrumentId() + "-" + obj.getReportItemCode(), Function.identity(), (a, b) -> b));

					// 赋值 仪器编码
					result.getReportItems().forEach(item -> {
						item.setGroupName(result.getGroupName());
						item.setGroupId(result.getGroupId());

						InstrumentDto instrumentDto = instrumentDtoMap.get(item.getInstrumentId());
						if (Objects.nonNull(instrumentDto)) {
							item.setInstrumentCode(StringUtils.defaultString(instrumentDto.getInstrumentCode(),""));
							item.setInstrumentName(StringUtils.defaultString(instrumentDto.getInstrumentName(),""));
						} else {
							// 默认取样本的仪器
							item.setInstrumentCode(StringUtils.defaultString(result.getInstrumentId() + "", "0"));
							item.setInstrumentName(StringUtils.defaultString(result.getInstrumentName(), ""));
						}

						// 上下限
						InstrumentReportItemReferenceDto instrumentReportItemReferenceDto = instrumentReportItemReferenceMap.get(item.getInstrumentReportItemReferenceId());
						if (Objects.nonNull(instrumentReportItemReferenceDto)) {
							item.setReferValueMax(instrumentReportItemReferenceDto.getReferValueMax());
							item.setReferValueMin(instrumentReportItemReferenceDto.getReferValueMin());
						}

						// 方法学
						// 优先取仪器上
						InstrumentReportItemDto instrumentReportItemDto =
								instrumentReportItemByKey.get(result.getInstrumentId() + "-" + item.getReportItemCode());

						if (Objects.isNull(instrumentReportItemDto)) {
							instrumentReportItemDto = instrumentReportItemByKey.values().stream()
									.filter(e -> Objects.equals(e.getReportItemCode(), item.getReportItemCode()))
									.findFirst().orElse(new InstrumentReportItemDto());
						}
						item.setExamMethodName(instrumentReportItemDto.getExamMethodName());

					});
					resultDto.add(result);
					break;
				}
				case INFECTION: {
					InfectionInspectionDto infectionInspectionDto = (InfectionInspectionDto) baseSampleEsModelDto;
					break;
				}
				case MICROBIOLOGY: {
					MicrobiologyInspectionDto microbiologyInspectionDto = (MicrobiologyInspectionDto) baseSampleEsModelDto;
					OpenReportMicrobiologyInspectionVoOpenReport result = openReportConvert.convertMicrobiologyEsResult2OpenReportResult(microbiologyInspectionDto);
					result.getGerms().forEach(germ -> {
						germ.setTestMethodCode(germ.getTestMethod());
						germ.getMedicines().forEach(medicine -> {
							medicine.setMedicineMethodCode(medicine.getMedicineMethod());
						});
					});
					resultDto.add(result);
					break;
				}
				case SPECIALTY: {
					SpecialtyInspectionDto specialtyInspectionDto = (SpecialtyInspectionDto) baseSampleEsModelDto;
					break;
				}
				case OUTSOURCING: {
					OutsourcingInspectionDto outsourcingInspectionDto = (OutsourcingInspectionDto) baseSampleEsModelDto;
					OpenReportOutsourcingInspectionVoOpenReport result = openReportConvert.convertOutSourcingEsResult2OpenReportResult(outsourcingInspectionDto);
					// 需求处理 https://www.tapd.cn/tapd_fe/48047159/story/detail/1148047159001002168?menu_workitem_type_id=0
					Set<Long> instrumentIds = result.getReportItems().stream().map(OpenReportOutsourcingInspectionVoOpenReport.OutsourcingReportItem::getInstrumentId).collect(Collectors.toSet());
					Map<Long, InstrumentDto> instrumentDtoMap = instrumentService.selectByInstrumentIds(instrumentIds).stream()
							.collect(Collectors.toMap(InstrumentDto::getInstrumentId, Function.identity()));

					Set<Long> instrumentReportItemReferenceIds = result.getReportItems().stream().map(OpenReportBaseSampleEsModelVo.ReportItem::getInstrumentReportItemReferenceId).collect(Collectors.toSet());
					Map<Long, InstrumentReportItemReferenceDto> instrumentReportItemReferenceMap = instrumentReportItemReferenceService.selectByIds(instrumentReportItemReferenceIds)
							.stream().collect(Collectors.toMap(InstrumentReportItemReferenceDto::getInstrumentReportItemReferenceId, Function.identity()));

					// 样本 仪器专业小组 对应 报告项目
					final List<InstrumentReportItemDto> instrumentReportItems =
							instrumentReportItemService.selectByInstrumentGroupId(result.getInstrumentGroupId());
					Map<String, InstrumentReportItemDto> instrumentReportItemByKey = instrumentReportItems.stream().collect(
							Collectors.toMap(obj -> obj.getInstrumentId() + "-" + obj.getReportItemCode(), Function.identity(), (a, b) -> b));

					// 赋值 仪器编码
					result.getReportItems().forEach(item -> {
						item.setGroupName(result.getGroupName());
						item.setGroupId(result.getGroupId());

						InstrumentDto instrumentDto = instrumentDtoMap.get(item.getInstrumentId());
						if (Objects.nonNull(instrumentDto)) {
							item.setInstrumentCode(StringUtils.defaultString(instrumentDto.getInstrumentCode(),""));
							item.setInstrumentName(StringUtils.defaultString(instrumentDto.getInstrumentName(),""));
						} else {
							// 默认取样本的仪器
							item.setInstrumentCode(StringUtils.defaultString(result.getInstrumentId() + "", "0"));
							item.setInstrumentName(StringUtils.defaultString(result.getInstrumentName(), ""));
						}

						// 上下限
						InstrumentReportItemReferenceDto instrumentReportItemReferenceDto = instrumentReportItemReferenceMap.get(item.getInstrumentReportItemReferenceId());
						if (Objects.nonNull(instrumentReportItemReferenceDto)) {
							item.setReferValueMax(instrumentReportItemReferenceDto.getReferValueMax());
							item.setReferValueMin(instrumentReportItemReferenceDto.getReferValueMin());
						}

						// 方法学
						// 优先取仪器上
						InstrumentReportItemDto instrumentReportItemDto =
								instrumentReportItemByKey.get(result.getInstrumentId() + "-" + item.getReportItemCode());

						if (Objects.isNull(instrumentReportItemDto)) {
							instrumentReportItemDto = instrumentReportItemByKey.values().stream()
									.filter(e -> Objects.equals(e.getReportItemCode(), item.getReportItemCode()))
									.findFirst().orElse(new InstrumentReportItemDto());
						}
						item.setExamMethodName(instrumentReportItemDto.getExamMethodName());

					});
					resultDto.add(result);
					break;
				}
				case BLOOD_CULTURE: {
					BloodCultureInspectionDto bloodCultureInspectionDto = (BloodCultureInspectionDto) baseSampleEsModelDto;
					break;
				}
				default:
					throw new IllegalStateException(String.format("无法识别项目类型 [%s]", baseSampleEsModelDto.getItemType()));
			}

		}

		// 判断是否分血，分血才需要合并报告单
		Optional<ApplySampleDto> first = applySamples.stream().filter(e -> Objects.equals(e.getIsSplitBlood(),YesOrNoEnum.YES.getCode())).findFirst();

		// 判断是否需要合并
		if (isConfigMerge(orgId, hspOrgCode, barcode) && first.isPresent()) {
			List<String> urlList = resultDto.stream().flatMap(e -> e.getReports().stream()).map(OpenReportBaseSampleEsModelVo.Report::getUrl).collect(Collectors.toList());
			String mergedUrl = mergePdfs(urlList, barcode);
			resultDto.forEach(item -> {
				item.getReports().clear();
				OpenReportBaseSampleEsModelVo.Report report = new OpenReportBaseSampleEsModelVo.Report();
				report.setUrl(mergedUrl);
				item.setReports(Collections.singletonList(report));
				// 是否合并的标识
				item.setMerged(YesOrNoEnum.YES.getCode());
			});
			return resultDto;
		}

		return resultDto.stream().filter(item -> Objects.equals(item.getItemType().toLowerCase(), itemType.toLowerCase())).collect(Collectors.toList());
	}

    /**
     * 查询es的样本
     * 1. 查询参数条件的es样本
     * 2. 根据es样本中过滤并单的主样本查询被并单的样本
     * 3. 填充被并单的样本的数据改为已审核， 审核人改为主样本审核人  报告单使用主样本的pdf， 检验项目设置为为终止
     * @return es样本
     */
    private List<BaseSampleEsModelDto> getBaseSampleEsModelDtos(Long orgId, String hspOrgCode, Set<Long> applySampleIds) {
        SampleEsQuery query = new SampleEsQuery();
        query.setOrgId(orgId);
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(NumberUtils.INTEGER_ONE);
        query.setApplySampleIds(applySampleIds);
        query.setHspOrgCodes(Sets.newHashSet(hspOrgCode));

        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(query);

        // 并单 主样本 的条码
        final Set<String> mergeBarcodeSet = baseSampleEsModelDtos.stream()
                // 只有并单主样本
                .filter(e -> BooleanUtils.isTrue(e.getMergeMasterSample()))
                .map(BaseSampleEsModelDto::getBarcode)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(mergeBarcodeSet)) {
            return baseSampleEsModelDtos;
        }

        // 被并单的数据（被终止掉的）
        final List<BaseSampleEsModelDto> mergeSample = elasticSearchSampleService.selectSamples(SampleEsQuery.builder()
                .sampleStatus(Collections.singleton(SampleStatusEnum.STOP_TEST.getCode()))
                .itemTypes(Collections.singleton(ItemTypeEnum.ROUTINE.name()))
                .mergeBarcodes(mergeBarcodeSet)
                .build());

        // 填充被并单样本 数据的状态
        final Map<String, List<RoutineInspectionDto>> sampleByBarcodeMap = mergeSample
                .stream()
                .map(e->(RoutineInspectionDto)e)
                .collect(Collectors.groupingBy(BaseSampleEsModelDto::getMergeMasterBarcode));

        baseSampleEsModelDtos.stream()
                .filter(e -> BooleanUtils.isTrue(e.getMergeMasterSample()) && Objects.equals(e.getItemType(), ItemTypeEnum.ROUTINE.name()))
                .map(e-> (RoutineInspectionDto)e)
                .forEach(e -> {
                    for (RoutineInspectionDto baseSampleEsModelDto : sampleByBarcodeMap.getOrDefault(e.getBarcode(), Lists.newArrayList())) {
                        // 审核状态改为已审
                        baseSampleEsModelDto.setSampleStatus(e.getSampleStatus());
                        // 审核人直接使用已审核的主样本的
                        baseSampleEsModelDto.setTwoCheckerId(e.getTwoCheckerId());
                        baseSampleEsModelDto.setTwoCheckerName(e.getTwoCheckerName());
                        baseSampleEsModelDto.setTwoCheckDate(e.getTwoCheckDate());
                        baseSampleEsModelDto.setSampleStatus(e.getSampleStatus());
                        baseSampleEsModelDto.setFinalCheckerId(e.getFinalCheckerId());
                        baseSampleEsModelDto.setFinalCheckerName(e.getFinalCheckerName());
                        baseSampleEsModelDto.setFinalCheckDate(e.getFinalCheckDate());

                        // 报告单直接使用已审核的主样本的
                        baseSampleEsModelDto.setReports(e.getReports());

                        // 检验项目的终止状态清除
                        for (BaseSampleEsModelDto.TestItem testItem : baseSampleEsModelDto.getTestItems()) {
                            testItem.setStopStatus(StopTestStatus.NO_STOP_TEST.getCode());
                            testItem.setStopReasonCode(Strings.EMPTY);
                            testItem.setStopReasonName(Strings.EMPTY);
                        }
                    }
                });

        baseSampleEsModelDtos.addAll(mergeSample);
        return baseSampleEsModelDtos;
    }

    /**
     * 获取样本信息，如果分血将返回多个
     */
	@Deprecated
    @GetMapping("/samples")
    public List<OpenReportSampleVo> samples(Long orgId, String hspOrgCode, String barcode) {

        //根据原始条码来查
        List<ApplySampleDto> applySamples = applySampleService.selectByOriginalBarcode(barcode)
                .stream().filter(e -> Objects.equals(e.getOrgId(), orgId) &&
                        Objects.equals(e.getHspOrgCode(), hspOrgCode)).collect(Collectors.toList());

        // 校验分血合并样本
        applySamples = validCanReport(orgId, hspOrgCode, barcode, applySamples);
        if (CollectionUtils.isEmpty(applySamples)) {
            return Collections.emptyList();
        }

        // 删除未审核的
        applySamples.removeIf(e -> !Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode()));

        if (CollectionUtils.isEmpty(applySamples)) {
            return Collections.emptyList();
        }

        final Map<Long, List<ApplySampleItemDto>> items = applySampleItemService
                .selectByApplySampleIdsAsMap(applySamples.stream().map(ApplySampleDto::getApplySampleId)
                        .collect(Collectors.toSet()));
        final List<OpenReportSampleVo> list = new ArrayList<>(items.size());

        for (ApplySampleDto e : applySamples) {
            final OpenReportSampleVo v = new OpenReportSampleVo();
            BeanUtils.copyProperties(e, v);
            final ItemTypeEnum itemType = ItemTypeEnum.getByName(e.getItemType());
            switch (itemType) {
                case GENETICS: {
                    final GeneticsSampleDto sample = geneticsSampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("遗传样本不存在");
                    }
                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getOneCheckerId());
                    v.setTwoCheckerId(sample.getTwoCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getOneCheckDate());
                    v.setOneCheckerName(sample.getOneCheckerName());
                    v.setTwoCheckDate(sample.getTwoCheckDate());
                    v.setTwoCheckerName(sample.getTwoCheckerName());
                    v.setSampleId(sample.getGeneticsSampleId());
                    v.setInstrumentGroupId(sample.getInstrumentGroupId());
                    break;
                }
                case ROUTINE: {
                    final SampleDto sample = sampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("样本不存在");
                    }
                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getOneCheckerId());
                    v.setTwoCheckerId(sample.getTwoCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getOneCheckDate());
                    v.setOneCheckerName(sample.getOneCheckerName());
                    v.setTwoCheckDate(sample.getTwoCheckDate());
                    v.setTwoCheckerName(sample.getTwoCheckerName());
                    v.setSampleId(sample.getSampleId());
	                v.setInstrumentGroupId(sample.getInstrumentGroupId());
                    break;
                }
                case INFECTION: {
                    final InfectionSampleDto sample = infectionSampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("院感样本不存在");
                    }

                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getCheckerId());
                    v.setTwoCheckerId(sample.getCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getCheckDate());
                    v.setOneCheckerName(sample.getCheckerName());
                    v.setTwoCheckDate(sample.getCheckDate());
                    v.setTwoCheckerName(sample.getCheckerName());
                    v.setSampleId(sample.getInfectionSampleId());
	                v.setInstrumentGroupId(sample.getInstrumentGroupId());
                    break;
                }
                case MICROBIOLOGY: {
                    final MicrobiologySampleDto sample = microbiologySampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("微生物样本不存在");
                    }

                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getCheckerId());
                    v.setTwoCheckerId(sample.getCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getCheckDate());
                    v.setOneCheckerName(sample.getCheckerName());
                    v.setTwoCheckDate(sample.getCheckDate());
                    v.setTwoCheckerName(sample.getCheckerName());
                    v.setSampleId(sample.getMicrobiologySampleId());
	                v.setInstrumentGroupId(sample.getInstrumentGroupId());
                    break;
                }
                case SPECIALTY: {
                    final SpecialtySampleDto sample = specialtySampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("特检样本不存在");
                    }
                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getOneCheckerId());
                    v.setTwoCheckerId(sample.getTwoCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getOneCheckDate());
                    v.setOneCheckerName(sample.getOneCheckerName());
                    v.setTwoCheckDate(sample.getTwoCheckDate());
                    v.setTwoCheckerName(sample.getTwoCheckerName());
                    v.setSampleId(sample.getSpecialtySampleId());
	                v.setInstrumentGroupId(sample.getInstrumentGroupId());
                    break;
                }
                case OUTSOURCING: {
                    final OutsourcingSampleDto sample = outsourcingSampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("外送样本不存在");
                    }
                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getCheckerId());
                    v.setTwoCheckerId(sample.getCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getCheckDate());
                    v.setOneCheckerName(sample.getCheckerName());
                    v.setTwoCheckDate(sample.getCheckDate());
                    v.setTwoCheckerName(sample.getCheckerName());
                    v.setSampleId(sample.getOutsourcingSampleId());
	                v.setInstrumentGroupId(sample.getInstrumentGroupId());
                    break;
                }
                case BLOOD_CULTURE: {
                    final BloodCultureSampleDto sample = bloodCultureSampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("血培养样本不存在");
                    }
                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getOneCheckerId());
                    v.setTwoCheckerId(sample.getOneCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getOneCheckDate());
                    v.setOneCheckerName(sample.getOneCheckerName());
                    v.setTwoCheckDate(sample.getTwoCheckDate());
                    v.setTwoCheckerName(sample.getTwoCheckerName());
                    v.setSampleId(sample.getBloodCultureSampleId());
	                v.setInstrumentGroupId(sample.getInstrumentGroupId());

                    // 二审报告单
                    final SampleReportDto sampleReport = sampleReportService.selectBySampleReportId(sample.getTwoCheckSampleReportId());
                    if (Objects.isNull(sampleReport)) {
                        throw new IllegalStateException("血培养获取终审报告失败");
                    }

                    v.setReportUrl(sampleReport.getUrl());

                    break;
                }
                default: {
                    throw new IllegalStateException(String.format("无法识别项目类型 [%s]", e.getItemType()));
                }
            }

            v.setTestItems(items.getOrDefault(e.getApplySampleId(), List.of()).stream().map(k -> {
                final OpenReportSampleVo.TestItem testItem = new OpenReportSampleVo.TestItem();
                testItem.setTestItemCode(k.getTestItemCode());
                testItem.setTestItemName(k.getTestItemName());
                testItem.setOutTestItemCode(k.getOutTestItemCode());
                testItem.setOutTestItemName(k.getOutTestItemName());
                return testItem;
            }).collect(Collectors.toList()));

            final Map<String, SampleReportItemDto> sampleReportItemDtoMap = sampleReportItemService.selectBySampleId(v.getSampleId())
                    .stream().collect(Collectors.toMap(SampleReportItemDto::getReportItemCode, Function.identity(), (a, b) -> b));
            final List<ApplySampleItemDto> applyItems = items.getOrDefault(e.getApplySampleId(), List.of());
	        List<SampleResultDto> sampleResult = sampleResultService.selectBySampleId(v.getSampleId());

			// 所有关联的仪器报告项目参考值
	        Set<Long> instrumentReportItemReferenceIds = sampleResult.stream().map(SampleResultDto::getInstrumentReportItemReferenceId).collect(Collectors.toSet());
	        Map<Long, InstrumentReportItemReferenceDto> instrumentReportItemReferenceMap = instrumentReportItemReferenceService.selectByIds(instrumentReportItemReferenceIds)
			        .stream().collect(Collectors.toMap(InstrumentReportItemReferenceDto::getInstrumentReportItemReferenceId, Function.identity()));

	        // 样本 仪器专业小组 对应 报告项目
	        final List<InstrumentReportItemDto> instrumentReportItems =
			        instrumentReportItemService.selectByInstrumentGroupId(v.getInstrumentGroupId());
	        Map<String, InstrumentReportItemDto> instrumentReportItemByKey = instrumentReportItems.stream().collect(
			        Collectors.toMap(obj -> obj.getInstrumentId() + "-" + obj.getReportItemCode(), Function.identity(), (a, b) -> b));

	        List<OpenReportSampleVo.Result> openResult = sampleResult.stream().map(k -> {
		        final OpenReportSampleVo.Result result = new OpenReportSampleVo.Result();
		        final SampleReportItemDto sampleReportItemDto = sampleReportItemDtoMap.get(k.getReportItemCode());
		        result.setPrintSort(NumberUtils.INTEGER_MINUS_ONE);
		        if (Objects.nonNull(sampleReportItemDto)) {
			        result.setPrintSort(sampleReportItemDto.getPrintSort());
		        }
		        result.setReportItemCode(k.getReportItemCode());
		        result.setReportItemName(k.getReportItemName());
		        result.setTestItemCode(k.getTestItemCode());
		        result.setTestItemName(k.getTestItemName());
		        if (CollectionUtils.isNotEmpty(applyItems)) {
			        final ApplySampleItemDto itemDto = applyItems.stream().filter(f -> Objects.equals(f.getTestItemCode(),
					        k.getTestItemCode())).findFirst().orElse(new ApplySampleItemDto());
			        result.setOutTestItemCode(itemDto.getOutTestItemCode());
			        result.setOutTestItemName(itemDto.getOutTestItemName());
		        }
		        result.setType(k.getType());
		        result.setResult(k.getResult());
		        result.setUnit(k.getUnit());
		        result.setRange(k.getRange());
		        result.setStatus(k.getStatus());
		        result.setJudge(k.getJudge());

		        // 上下限
		        InstrumentReportItemReferenceDto instrumentReportItemReferenceDto = instrumentReportItemReferenceMap.get(k.getInstrumentReportItemReferenceId());
		        if (Objects.nonNull(instrumentReportItemReferenceDto)) {
			        result.setReferValueMax(instrumentReportItemReferenceDto.getReferValueMax());
			        result.setReferValueMin(instrumentReportItemReferenceDto.getReferValueMin());
		        }

		        // 方法学
		        // 优先取仪器上
		        InstrumentReportItemDto instrumentReportItemDto =
				        instrumentReportItemByKey.get(v.getInstrumentId() + "-" + result.getReportItemCode());

		        if (Objects.isNull(instrumentReportItemDto)) {
			        instrumentReportItemDto = instrumentReportItemByKey.values().stream()
					        .filter(item -> Objects.equals(item.getReportItemCode(), result.getReportItemCode()))
					        .findFirst().orElse(new InstrumentReportItemDto());
		        }
				result.setExamMethodName(instrumentReportItemDto.getExamMethodName());

		        return result;
	        }).collect(Collectors.toList());
	        v.setResults(openResult);

            // 血培养比较特殊，已经在上面获取过了
            if (itemType != ItemTypeEnum.BLOOD_CULTURE) {
                final SampleReportDto sampleReport = sampleReportService.selectByApplySampleId(e.getApplySampleId());
                if (Objects.nonNull(sampleReport) && StringUtils.isNotBlank(sampleReport.getUrl())) {
                    v.setReportUrl(sampleReport.getUrl());
                }

                // 如果pdf文件是上传文件 那么置空结果值是数据
                if (Objects.nonNull(sampleReport) && Objects.equals(sampleReport.getIsUploadPdf(), YesOrNoEnum.YES.getCode())) {
                    v.setResults(Collections.emptyList());
                }

            }

            // 查询样本图片
            List<SampleImageDto> sampleImageDtos = sampleImageService.selectSampleImageBySampleId(v.getSampleId());
            if (CollectionUtils.isNotEmpty(sampleImageDtos)) {
                v.setImgUrls(sampleImageDtos.stream().map(SampleImageDto::getImageUrl).collect(Collectors.joining(StringPool.COMMA)));
            }

            list.add(v);
        }

        // 判断是否分血，分血才需要合并报告单
        Optional<ApplySampleDto> first = applySamples.stream().filter(e -> Objects.equals(e.getIsSplitBlood(),YesOrNoEnum.YES.getCode())).findFirst();
        // 判断是否需要合并
        if (isConfigMerge(orgId, hspOrgCode, barcode) && first.isPresent()) {
            return mergeReport(orgId, hspOrgCode, barcode, list);
        }

        return list;
    }

    /**
     * 获取专业组信息
     */
    @GetMapping("/group")
    public List<ReportGroupVo> groups(long orgId) {
        List<ProfessionalGroupDto> professionalGroupDtos = groupService.selectByAll(orgId);
        if (CollectionUtils.isEmpty(professionalGroupDtos)){
            return Collections.emptyList();
        }

        List<ReportGroupVo> reportGroupVos = new ArrayList<>();
        for (ProfessionalGroupDto professionalGroupDto : professionalGroupDtos) {
            ReportGroupVo temp = new ReportGroupVo();
            temp.setGroupId(String.valueOf(professionalGroupDto.getGroupId()));
            temp.setGroupCode(professionalGroupDto.getGroupCode());
            temp.setGroupName(professionalGroupDto.getGroupName());
            temp.setOrgCode(String.valueOf(professionalGroupDto.getOrgId()));
            temp.setOrgName("");
            reportGroupVos.add(temp);
        }

        return reportGroupVos;
    }


    @DubboReference
    private GroupService groupService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private ReportTemplateBindService reportTemplateBindService;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    private String reBuildReport(SampleDto sample, ApplyDto apply, ApplySampleDto applySample,
                                 List<SampleReportItemDto> reportItems, List<SampleResultDto> results,
                                 List<InstrumentReportItemDto> instrumentReportItems, List<ApplySampleItemDto> sampleItems) {

        final ProfessionalGroupDto group = groupService.selectByGroupId(sample.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException(String.format("专业组 [%s] 不存在", sample.getGroupName()));
        }

        final UserDto twoChecker = userService.selectByUserId(sample.getTwoCheckerId());
        if (Objects.isNull(twoChecker)) {
            throw new IllegalStateException(String.format("二次审核人 [%s] 不存在", sample.getTwoCheckerName()));
        }

        final UserDto oneChecker = userService.selectByUserId(sample.getOneCheckerId());
        if (Objects.isNull(oneChecker)) {
            throw new IllegalStateException(String.format("一次审核人 [%s] 不存在", sample.getOneCheckerName()));
        }

        final UserDto tester = userService.selectByUserId(applySample.getTesterId());
        if (Objects.isNull(tester)) {
            throw new IllegalStateException(String.format("检验者 [%s] 不存在", applySample.getTesterName()));
        }

        final PdfReportParamDto param = new PdfReportParamDto();

        param.put("sample",
                Dict.of("sampleNo", sample.getSampleNo(), "instrumentGroupName", sample.getInstrumentGroupName(),
                        "instrumentName", sample.getInstrumentName(), "testDate", sample.getTestDate(), "testerName",
                        applySample.getTesterName(), "oneCheckerName", sample.getOneCheckerName(), "twoCheckerName",
                        sample.getTwoCheckerName(), "checkerName", sample.getTwoCheckerName(), "sampleRemark",
                        applySample.getSampleRemark(), "resultRemark", applySample.getResultRemark(), "_sample",
                        Dict.parse(sample)));

        param.put("apply",
                Dict.of("masterBarcode", apply.getMasterBarcode(), "patientName", apply.getPatientName(), "patientAge",
                        apply.getPatientAge(), "samplingDate", apply.getSamplingDate(), "patientMobile",
                        apply.getPatientMobile(), "createDate", apply.getCreateDate(), "sendDoctorName",
                        apply.getSendDoctorName(), "patientCard", apply.getPatientCard(), "dept", apply.getDept(), "hspOrgName",
                        apply.getHspOrgName(), "_apply", Dict.parse(apply)));
        param.put("applySample",
                Dict.of("barcode", applySample.getBarcode(), "tubeName", applySample.getTubeName(), "sampleTypeName",
                        applySample.getSampleTypeName(), "groupName", applySample.getGroupName(), "onePickerName",
                        applySample.getOnePickerName(), "onePickDate", applySample.getOnePickDate(), "twoPickerName",
                        applySample.getTwoPickerName(), "twoPickDate", applySample.getTwoPickDate(), "_applySample",
                        Dict.parse(applySample)));
        param.put("testItems", Dict.of());

        // 过滤不打印的
        List<SampleReportItemDto> printItems = Lists.newArrayList();
        for (SampleReportItemDto dto : reportItems) {
            final InstrumentReportItemDto itemDto = instrumentReportItems.stream()
                    .filter(e -> Objects.equals(e.getInstrumentId(), sample.getInstrumentId())
                            && Objects.equals(e.getReportItemCode(), dto.getReportItemCode()))
                    .findFirst().orElse(instrumentReportItems.stream().findFirst().orElse(null));
            if (Objects.nonNull(itemDto) && Objects.equals(itemDto.getIsPrint(), YesOrNoEnum.YES.getCode())) {
                printItems.add(dto);
            }
        }

        List<SampleReportItemDto> sortedReportItems = printItems.stream().sorted(
                        Comparator.comparing(SampleReportItemDto::getPrintSort).thenComparing(SampleReportItemDto::getReportItemId))
                .collect(Collectors.toList());

        Map<String, InstrumentReportItemDto> instrumentReportItemByKey = instrumentReportItems.stream().collect(
                Collectors.toMap(obj -> obj.getInstrumentId() + "-" + obj.getReportItemCode(), Function.identity()));

        final List<Dict> reportItemsParam = new ArrayList<>();
        for (SampleReportItemDto reportItem : sortedReportItems) {
            final Dict dict = Dict.create();
            dict.set("_sampleReportItem", reportItem);
            dict.set("reportItemCode", reportItem.getReportItemCode());
            dict.set("reportItemName", reportItem.getReportItemName());
            dict.set("testItemCode", reportItem.getTestItemCode());
            dict.set("testItemName", reportItem.getTestItemName());

            final SampleResultDto sampleResult =
                    results.stream().filter(e -> Objects.equals(e.getReportItemCode(), reportItem.getReportItemCode()))
                            .findFirst().orElse(null);
            if (Objects.isNull(sampleResult)) {
                continue;
            }

            dict.set("_sampleResult", sampleResult);
            dict.set("result", sampleResult.getResult());
            dict.set("unit", sampleResult.getUnit());
            dict.set("range", sampleResult.getRange());
            dict.set("status", sampleResult.getStatus());
            dict.set("judge", sampleResult.getJudge());

            if (Objects.equals(sampleResult.getJudge(), TestJudgeEnum.UP.name())) {
                dict.set("upOrDown", "↑");
            } else if (Objects.equals(sampleResult.getJudge(), TestJudgeEnum.DOWN.name())) {
                dict.set("upOrDown", "↓");
            } else {
                dict.set("upOrDown", StringUtils.EMPTY);
            }

            // 优先取结果上 仪器
            InstrumentReportItemDto instrumentReportItemDto =
                    instrumentReportItemByKey.get(sampleResult.getInstrumentId() + "-" + sampleResult.getReportItemCode());
            if (Objects.isNull(instrumentReportItemDto)) {
                instrumentReportItemDto =
                        instrumentReportItemByKey.get(sample.getInstrumentId() + "-" + sampleResult.getReportItemCode());
            }
            if (Objects.isNull(instrumentReportItemDto)) {
                instrumentReportItemDto = new InstrumentReportItemDto();
            }
            dict.set("_instrumentReportItem", instrumentReportItemDto);

            reportItemsParam.add(dict);
        }

        param.put("reportItems", reportItemsParam);

        // 签名
        param.put("signature", Dict.of(
                // 检验者
                "tester", Dict.of("name", tester.getNickname(), "cnSign", tester.getCnSign(), "enSign", tester.getEnSign(), "sign",
                        StringUtils.defaultString(tester.getCnSign(), tester.getEnSign())),
                // 一次审核人
                "oneChecker",
                Dict.of("name", oneChecker.getNickname(), "cnSign", oneChecker.getCnSign(), "enSign",
                        oneChecker.getEnSign(), "sign",
                        StringUtils.defaultString(oneChecker.getCnSign(), oneChecker.getEnSign())),
                // 二次审核人
                "twoChecker",
                Dict.of("name", twoChecker.getNickname(), "cnSign", twoChecker.getCnSign(), "enSign",
                        twoChecker.getEnSign(), "sign",
                        StringUtils.defaultString(twoChecker.getCnSign(), twoChecker.getEnSign())),
                // 批准者
                "approver", Dict.of("name", group.getApproverName(), "sign", group.getApproverSign())));

        MatchBindReportDto matchBindReportDto = new MatchBindReportDto();
        if (CollectionUtils.isNotEmpty(sampleItems)){
            matchBindReportDto.setTestItemIds(sampleItems.stream().map(ApplySampleItemDto::getTestItemId)
                    .collect(Collectors.toList()));

        }
        //匹配模板
        matchBindReportDto.setBarcode(sample.getBarcode());
        matchBindReportDto.setInstrumentGroupId(sample.getInstrumentGroupId());
        matchBindReportDto.setInstrumentGroupName(sample.getInstrumentGroupName());
        matchBindReportDto.setHspOrgId(apply.getHspOrgId());
        matchBindReportDto.setHspOrgName(apply.getHspOrgName());
        matchBindReportDto.setGroupId(sample.getGroupId());
        ReportTemplateBindDto reportTemplateBind =  reportTemplateBindService.findMatchReportTemplate(matchBindReportDto);

        log.info("开始使用报告单模板 [{}] 生成 条码 [{}] 的报告单。 参数 [{}]", reportTemplateBind.getReportTemplateCode(), sample.getBarcode(),
                JSON.toJSONString(param));

        final String url = pdfReportService.build2Url(reportTemplateBind.getReportTemplateCode(), param);
        String key = getRebuildPdfKey(applySample);
        stringRedisTemplate.opsForValue().set(key, url, 3, TimeUnit.DAYS);

        return url;
    }

    /**
     * 获取重新生成报告的缓存 redis key
     */
    private String getRebuildPdfKey(ApplySampleDto applySample) {
        return redisPrefix.getBasePrefix() + "REBUILD_REPORT:" + applySample.getBarcode()
                + applySample.getOutBarcode() + applySample.getHspOrgCode();
    }



    // 判断是否可以出报告 -- 当配置了分血合并，并且包含分血样本 并且结果没有完全审核时结果是不允许给到报告平台的
    // 如果满足推送 则返回样本信息列表（分血合并的返回所有分管分血样本，不需要合并的返回入参样本列表applySamples）
    // 如果不满足推送要求 返回报错
    private List<ApplySampleDto> validCanReport(Long orgId, String hspOrgCode, String barcode,List<ApplySampleDto> applySamples) {

        if (CollectionUtils.isEmpty(applySamples)){
            return applySamples;
        }

        boolean configMerge = isConfigMerge(orgId, hspOrgCode, barcode);

        // 如果没配置合并打印 可以直接推送结果
        if (!configMerge) {
            log.info("条码号：{},送检机构：{},未配置分血合并，允许推送报告结果！", barcode, hspOrgCode);
            return applySamples;
        }


        // 根据外部条码号查询所有的样本信息 包含分管的以及分血的样本
        ApplySampleDto next = applySamples.iterator().next();
        List<ApplySampleDto> allApplySamples = applySampleService.selectByOutBarcodeAndHspOrgCode(next.getHspOrgCode(), next.getOutBarcode());

        // 过滤掉已经删除样本
        allApplySamples = allApplySamples.stream().filter(e -> Objects.equals(YesOrNoEnum.NO.getCode(), e.getIsDelete())).collect(Collectors.toList());

        // 判断是否分血，分血才需要合并报告单
        Optional<ApplySampleDto> first = allApplySamples.stream().filter(e -> Objects.equals(e.getIsSplitBlood(),YesOrNoEnum.YES.getCode())).findFirst();
        // 没有分血样本，直接返回原始样本信息
        if (!first.isPresent()) {
            log.info("条码号：{},送检机构：{},未分血，允许推送报告结果！", barcode, hspOrgCode);
            return applySamples;
        }

        // 非终止检验
        allApplySamples = allApplySamples.stream().filter(e -> !Objects.equals(SampleStatusEnum.STOP_TEST.getCode(), e.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allApplySamples)) {
            throw new IllegalArgumentException(String.format("条码号：%s, 送检机构：%s, 全部终止检验，不允许推送报告结果！", barcode, hspOrgCode));
        }

        // 如果有分血样本，判断是否所有的样本是否都已审核
        if (allApplySamples.stream().allMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode()))) {
            // 找到最大的分血条码后缀 -- 目前用不到 暂时先注释掉
//            Integer maxBarcode = allApplySamples.stream().filter(e -> e.getBarcode().contains(SPLIT_BLOOD_SUFFIX))
//                    .map(e -> e.getBarcode().split(SPLIT_BLOOD_SUFFIX)[NumberUtils.INTEGER_ONE])
//                    .max(Comparator.comparing(Integer::parseInt))
//                    .map(Integer::valueOf)
//                    .orElse(NumberUtils.INTEGER_ZERO);
//
//            // 更新分管样本的条码号 主要更新原始条码号和分血条码号
//            for (ApplySampleDto e : allApplySamples) {
//                // 如果是分管样本，则样本条码更新为分血条码
//                if (!Objects.equals(e.getBarcode(), barcode)) {
//                    // 将 maxBarcode 自增后的值以两位数格式化（不足两位前面补零）
//                    e.setBarcode(barcode + SPLIT_BLOOD_SUFFIX + String.format("%02d", ++maxBarcode));
//                }
//            }

            return allApplySamples;
        }

        // 到这里，说明 配置了分血合并 并且有分血样本，但是有未审核的样本，不允许推送
        throw new LimsCodeException(445566, JSONUtil.toJsonStr(allApplySamples.stream()
		        .map(item->item.getBarcode() + "@" + item.getItemType())
		        .collect(Collectors.toList())));
    }


    // 判是否配置了合并打印
    private boolean isConfigMerge(Long orgId, String hspOrgCode, String barcode) {
        //判断送检机构是否配置了合并打印
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.MERGE_PRINT.getCode(), orgId);
        log.info("合并打印报告单配置：{}", JSONObject.toJSONString(param));

        // 送检机构配置了合并打印
        return param != null && StringUtils.isNotBlank(param.getParamValue()) && Arrays.asList(param.getParamValue().split(",")).contains(hspOrgCode);
    }

    // 分管分血样本合并
    private List<OpenReportSampleVo> mergeReport(Long orgId, String hspOrgCode, String barcode, List<OpenReportSampleVo> limsSamples) {

        // 获取原始样本
        OpenReportSampleVo applySampleDto = limsSamples.stream().filter(e -> e.getBarcode().equals(barcode)).findFirst().orElse(limsSamples.iterator().next());
        // 获取最后审核的样本
        OpenReportSampleVo lastAuditSampleDto = limsSamples.stream().max(Comparator.comparing(OpenReportSampleVo::getTwoCheckDate)).orElse(applySampleDto);

        // 检验项目
        List<OpenReportSampleVo.TestItem> testItemCollect = limsSamples.stream().map(OpenReportSampleVo::getTestItems).flatMap(Collection::stream).collect(Collectors.toList());
        lastAuditSampleDto.setTestItems(testItemCollect);

        // 检验结果
        List<OpenReportSampleVo.Result> resultCollect = limsSamples.stream().map(OpenReportSampleVo::getResults).flatMap(Collection::stream).collect(Collectors.toList());
        lastAuditSampleDto.setResults(resultCollect);

        // 结果备注
        List<String> resultRemarks = limsSamples.stream().map(OpenReportSampleVo::getResultRemark).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < resultRemarks.size(); i++) {
            sb.append(i + 1 + "," + resultRemarks.get(i) + "\n");
        }
        lastAuditSampleDto.setResultRemark(sb.toString());

        //报告
//        final List<String> urls = limsSamples.stream().map(OpenReportSampleVo::getReportUrl).collect(Collectors.toList());
        List<String> urlList = reportService.mergePrint(limsSamples.stream().map(e -> e.getApplySampleId()).collect(Collectors.toSet()));
        lastAuditSampleDto.setReportUrl(mergePdfs(urlList, barcode));

        // 图片
        final List<String> imgUrls = limsSamples.stream().map(OpenReportSampleVo::getImgUrls).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        lastAuditSampleDto.setImgUrls(CollectionUtils.isEmpty(imgUrls) ? StringUtils.EMPTY : StringUtils.join(imgUrls, ","));

        ArrayList<OpenReportSampleVo> resultApplySampleDto = new ArrayList<>();
        resultApplySampleDto.add(lastAuditSampleDto);
        return resultApplySampleDto;
    }


    // 多张报告单合并成一张报告单
    public String mergePdfs(List<String> pdfUrls,String barcode) {

        // 地址为空，不进行合并
        if (CollectionUtils.isEmpty(pdfUrls)) {
            return StringUtils.EMPTY;
        }

        File tempFile = FileUtil.createTempFile(MERGE_TEMP_DIR, null, null, true);

        // 合并新地址成一个pdf
        List<File> files = new ArrayList<>();
        final PDFMergerUtility merger = new PDFMergerUtility();
        try {
            for (String pdfUrl : pdfUrls) {
                merger.addSource(huaweiObsUtils.downloadFileByUrl(pdfUrl));
            }
        } catch (Exception e) {
            log.error("pdf合并成一个url下载失败！外部条码号：{}",barcode, e);
            throw new IllegalArgumentException("pdf合并成一个url时 下载失败！外部条码号：" + barcode + " 异常信息：" + e.getMessage());
        }

        final PDDocumentInformation information = new PDDocumentInformation();
//        information.setKeywords("");
        merger.setDestinationFileName(tempFile.getAbsolutePath());
        merger.setDestinationDocumentInformation(information);

        try {
            merger.mergeDocuments(MemoryUsageSetting.setupTempFileOnly());
        } catch (Exception e) {
            log.error("pdf合并成一个url合成失败，返回多个pdf文件！外部条码号：{}",barcode, e);
            throw new IllegalArgumentException("pdf合并成一个url时 合成失败！外部条码号：" + barcode + " 异常信息：" + e.getMessage());
        } finally {
            files.forEach(FileUtils::deleteQuietly);
        }

        try {
            // 上传文件到obs
//            String objectKey = MERGE_FILE_PREFIX + LocalDateTime.now().format(DateTimeFormatter.ofPattern("/yyyy/MM/dd/")) + UUID.randomUUID() + PDF_SUFFIX;
            return huaweiObsUtils.upload(new FileInputStream(tempFile), MediaType.APPLICATION_PDF_VALUE);
        } catch (Exception e) {
            log.error("pdf合并成一个url上传失败，返回多个pdf文件！外部条码号：{}",barcode, e);
            throw new IllegalArgumentException("pdf合并成一个url时 上传失败！外部条码号：" + barcode + " 异常信息：" + e.getMessage());
        } finally {
            FileUtils.deleteQuietly(tempFile);
        }

    }

}
