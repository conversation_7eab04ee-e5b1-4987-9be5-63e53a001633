package com.labway.lims.apply.controller.report;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleImageService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.apply.vo.OpenReportSampleVo;
import com.labway.lims.apply.vo.ReportGroupVo;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.MatchBindReportDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import com.labway.lims.statistics.api.client.ReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 提供给报告平台的接口
 */
@Slf4j
@RestController
@RequestMapping("/open-report")
public class OpenReportController extends BaseController {

    // 分血后缀标识
    private static final String SPLIT_BLOOD_SUFFIX = "_";
    // 合并文件临时目录
    private static final String MERGE_TEMP_DIR = System.getProperty("user.dir") + "/merge_temp";
    // 合并文件前缀
    private static final String MERGE_FILE_PREFIX = "lims_merge_pdf";
    // pdf文件后缀
    private static final String PDF_SUFFIX = ".pdf";

    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private SampleReportService sampleReportService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private SpecialtySampleService specialtySampleService;
    @DubboReference
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private GeneticsSampleService geneticsSampleService;
    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private BloodCultureSampleService bloodCultureSampleService;
    @Resource
    private SampleImageService sampleImageService;
    @DubboReference
    private SystemParamService systemParamService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @DubboReference
    private ReportService reportService;


    /**
     * 获取申请单信息
     */
    @GetMapping("/apply")
    public Object apply(Long orgId, String hspOrgCode, String barcode) {

        List<ApplySampleDto> applySamples = new ArrayList<>(applySampleService.selectByOriginalBarcode(barcode))
                .stream().filter(e -> Objects.equals(e.getOrgId(), orgId) &&
                        Objects.equals(e.getHspOrgCode(), hspOrgCode)).collect(Collectors.toList());

        // 校验分血合并样本
        applySamples = validCanReport(orgId, hspOrgCode, barcode, applySamples);

        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException(String.format("条码 [%s] 不存在", barcode));
        }


        final ApplySampleDto applySample = applySamples.iterator().next();
        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalArgumentException("申请单不存在");
        }

        final Dict dict = Dict.parse(apply);
        dict.put("applySamples", applySamples);
        dict.put("barcode", barcode);
        dict.put("outBarcode", applySample.getOutBarcode());
        dict.put("hspOrgCode", applySample.getHspOrgCode());
        dict.put("tubeName", applySample.getTubeName());
        dict.put("tubeCode", applySample.getTubeCode());
        dict.put("sampleTypeName", applySample.getSampleTypeName());
        dict.put("sampleTypeCode", applySample.getSampleTypeCode());

        return dict;
    }

    /**
     * 获取样本信息，如果分血将返回多个
     */
    @GetMapping("/samples")
    public List<OpenReportSampleVo> samples(Long orgId, String hspOrgCode, String barcode) {

        //根据原始条码来查
        List<ApplySampleDto> applySamples = applySampleService.selectByOriginalBarcode(barcode)
                .stream().filter(e -> Objects.equals(e.getOrgId(), orgId) &&
                        Objects.equals(e.getHspOrgCode(), hspOrgCode)).collect(Collectors.toList());

        // 校验分血合并样本
        applySamples = validCanReport(orgId, hspOrgCode, barcode, applySamples);
        if (CollectionUtils.isEmpty(applySamples)) {
            return Collections.emptyList();
        }

        // 删除未审核的
        applySamples.removeIf(e -> !Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode()));

        if (CollectionUtils.isEmpty(applySamples)) {
            return Collections.emptyList();
        }

        final Map<Long, List<ApplySampleItemDto>> items = applySampleItemService
                .selectByApplySampleIdsAsMap(applySamples.stream().map(ApplySampleDto::getApplySampleId)
                        .collect(Collectors.toSet()));
        final List<OpenReportSampleVo> list = new ArrayList<>(items.size());

        for (ApplySampleDto e : applySamples) {
            final OpenReportSampleVo v = new OpenReportSampleVo();
            BeanUtils.copyProperties(e, v);
            final ItemTypeEnum itemType = ItemTypeEnum.getByName(e.getItemType());
            switch (itemType) {
                case GENETICS: {
                    final GeneticsSampleDto sample = geneticsSampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("遗传样本不存在");
                    }
                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getOneCheckerId());
                    v.setTwoCheckerId(sample.getTwoCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getOneCheckDate());
                    v.setOneCheckerName(sample.getOneCheckerName());
                    v.setTwoCheckDate(sample.getTwoCheckDate());
                    v.setTwoCheckerName(sample.getTwoCheckerName());
                    v.setSampleId(sample.getGeneticsSampleId());
                    break;
                }
                case ROUTINE: {
                    final SampleDto sample = sampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("样本不存在");
                    }
                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getOneCheckerId());
                    v.setTwoCheckerId(sample.getTwoCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getOneCheckDate());
                    v.setOneCheckerName(sample.getOneCheckerName());
                    v.setTwoCheckDate(sample.getTwoCheckDate());
                    v.setTwoCheckerName(sample.getTwoCheckerName());
                    v.setSampleId(sample.getSampleId());
                    break;
                }
                case INFECTION: {
                    final InfectionSampleDto sample = infectionSampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("院感样本不存在");
                    }

                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getCheckerId());
                    v.setTwoCheckerId(sample.getCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getCheckDate());
                    v.setOneCheckerName(sample.getCheckerName());
                    v.setTwoCheckDate(sample.getCheckDate());
                    v.setTwoCheckerName(sample.getCheckerName());
                    v.setSampleId(sample.getInfectionSampleId());
                    break;
                }
                case MICROBIOLOGY: {
                    final MicrobiologySampleDto sample = microbiologySampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("微生物样本不存在");
                    }

                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getCheckerId());
                    v.setTwoCheckerId(sample.getCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getCheckDate());
                    v.setOneCheckerName(sample.getCheckerName());
                    v.setTwoCheckDate(sample.getCheckDate());
                    v.setTwoCheckerName(sample.getCheckerName());
                    v.setSampleId(sample.getMicrobiologySampleId());
                    break;
                }
                case SPECIALTY: {
                    final SpecialtySampleDto sample = specialtySampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("特检样本不存在");
                    }
                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getOneCheckerId());
                    v.setTwoCheckerId(sample.getTwoCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getOneCheckDate());
                    v.setOneCheckerName(sample.getOneCheckerName());
                    v.setTwoCheckDate(sample.getTwoCheckDate());
                    v.setTwoCheckerName(sample.getTwoCheckerName());
                    v.setSampleId(sample.getSpecialtySampleId());
                    break;
                }
                case OUTSOURCING: {
                    final OutsourcingSampleDto sample = outsourcingSampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("外送样本不存在");
                    }
                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getCheckerId());
                    v.setTwoCheckerId(sample.getCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getCheckDate());
                    v.setOneCheckerName(sample.getCheckerName());
                    v.setTwoCheckDate(sample.getCheckDate());
                    v.setTwoCheckerName(sample.getCheckerName());
                    v.setSampleId(sample.getOutsourcingSampleId());
                    break;
                }
                case BLOOD_CULTURE: {
                    final BloodCultureSampleDto sample = bloodCultureSampleService.selectByApplySampleId(e.getApplySampleId());
                    if (Objects.isNull(sample)) {
                        throw new IllegalStateException("血培养样本不存在");
                    }
                    v.setSampleNo(sample.getSampleNo());
                    v.setTubeCode(e.getTubeCode());
                    v.setTubeName(e.getTubeName());
                    v.setTestDate(sample.getTestDate());
                    v.setOneCheckerId(sample.getOneCheckerId());
                    v.setTwoCheckerId(sample.getOneCheckerId());
                    v.setInstrumentId(sample.getInstrumentId());
                    v.setInstrumentName(sample.getInstrumentName());
                    v.setOneCheckDate(sample.getOneCheckDate());
                    v.setOneCheckerName(sample.getOneCheckerName());
                    v.setTwoCheckDate(sample.getTwoCheckDate());
                    v.setTwoCheckerName(sample.getTwoCheckerName());
                    v.setSampleId(sample.getBloodCultureSampleId());

                    // 二审报告单
                    final SampleReportDto sampleReport = sampleReportService.selectBySampleReportId(sample.getTwoCheckSampleReportId());
                    if (Objects.isNull(sampleReport)) {
                        throw new IllegalStateException("血培养获取终审报告失败");
                    }

                    v.setReportUrl(sampleReport.getUrl());

                    break;
                }
                default: {
                    throw new IllegalStateException(String.format("无法识别项目类型 [%s]", e.getItemType()));
                }
            }

            v.setTestItems(items.getOrDefault(e.getApplySampleId(), List.of()).stream().map(k -> {
                final OpenReportSampleVo.TestItem testItem = new OpenReportSampleVo.TestItem();
                testItem.setTestItemCode(k.getTestItemCode());
                testItem.setTestItemName(k.getTestItemName());
                testItem.setOutTestItemCode(k.getOutTestItemCode());
                testItem.setOutTestItemName(k.getOutTestItemName());
                return testItem;
            }).collect(Collectors.toList()));

            final Map<String, SampleReportItemDto> sampleReportItemDtoMap = sampleReportItemService.selectBySampleId(v.getSampleId())
                    .stream().collect(Collectors.toMap(SampleReportItemDto::getReportItemCode, Function.identity(), (a, b) -> b));
            final List<ApplySampleItemDto> applyItems = items.getOrDefault(e.getApplySampleId(), List.of());
            v.setResults(sampleResultService.selectBySampleId(v.getSampleId()).stream().map(k -> {
                final OpenReportSampleVo.Result result = new OpenReportSampleVo.Result();
                final SampleReportItemDto sampleReportItemDto = sampleReportItemDtoMap.get(k.getReportItemCode());
                result.setPrintSort(NumberUtils.INTEGER_MINUS_ONE);
                if(Objects.nonNull(sampleReportItemDto)){
                    result.setPrintSort(sampleReportItemDto.getPrintSort());
                }
                result.setReportItemCode(k.getReportItemCode());
                result.setReportItemName(k.getReportItemName());
                result.setTestItemCode(k.getTestItemCode());
                result.setTestItemName(k.getTestItemName());
                if (CollectionUtils.isNotEmpty(applyItems)) {
                    final ApplySampleItemDto itemDto = applyItems.stream().filter(f -> Objects.equals(f.getTestItemCode(),
                            k.getTestItemCode())).findFirst().orElse(new ApplySampleItemDto());
                    result.setOutTestItemCode(itemDto.getOutTestItemCode());
                    result.setOutTestItemName(itemDto.getOutTestItemName());
                }
                result.setType(k.getType());
                result.setResult(k.getResult());
                result.setUnit(k.getUnit());
                result.setRange(k.getRange());
                result.setStatus(k.getStatus());
                result.setJudge(k.getJudge());
                return result;
            }).collect(Collectors.toList()));

            // 血培养比较特殊，已经在上面获取过了
            if (itemType != ItemTypeEnum.BLOOD_CULTURE) {
                final SampleReportDto sampleReport = sampleReportService.selectByApplySampleId(e.getApplySampleId());
                if (Objects.nonNull(sampleReport) && StringUtils.isNotBlank(sampleReport.getUrl())) {
                    v.setReportUrl(sampleReport.getUrl());
                }

                // 如果pdf文件是上传文件 那么置空结果值是数据
                if (Objects.nonNull(sampleReport) && Objects.equals(sampleReport.getIsUploadPdf(), YesOrNoEnum.YES.getCode())) {
                    v.setResults(Collections.emptyList());
                }

            }

            // 查询样本图片
            List<SampleImageDto> sampleImageDtos = sampleImageService.selectSampleImageBySampleId(v.getSampleId());
            if (CollectionUtils.isNotEmpty(sampleImageDtos)) {
                v.setImgUrls(sampleImageDtos.stream().map(SampleImageDto::getImageUrl).collect(Collectors.joining(StringPool.COMMA)));
            }

            list.add(v);
        }

        // 判断是否分血，分血才需要合并报告单
        Optional<ApplySampleDto> first = applySamples.stream().filter(e -> Objects.equals(e.getIsSplitBlood(),YesOrNoEnum.YES.getCode())).findFirst();
        // 判断是否需要合并
        if (isConfigMerge(orgId, hspOrgCode, barcode) && first.isPresent()) {
            return mergeReport(orgId, hspOrgCode, barcode, list);
        }

        return list;
    }

    /**
     * 获取专业组信息
     */
    @GetMapping("/group")
    public List<ReportGroupVo> groups(long orgId) {
        List<ProfessionalGroupDto> professionalGroupDtos = groupService.selectByAll(orgId);
        if (CollectionUtils.isEmpty(professionalGroupDtos)){
            return Collections.emptyList();
        }

        List<ReportGroupVo> reportGroupVos = new ArrayList<>();
        for (ProfessionalGroupDto professionalGroupDto : professionalGroupDtos) {
            ReportGroupVo temp = new ReportGroupVo();
            temp.setGroupId(String.valueOf(professionalGroupDto.getGroupId()));
            temp.setGroupCode(professionalGroupDto.getGroupCode());
            temp.setGroupName(professionalGroupDto.getGroupName());
            temp.setOrgCode(String.valueOf(professionalGroupDto.getOrgId()));
            temp.setOrgName("");
            reportGroupVos.add(temp);
        }

        return reportGroupVos;
    }


    @DubboReference
    private GroupService groupService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private ReportTemplateBindService reportTemplateBindService;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    private String reBuildReport(SampleDto sample, ApplyDto apply, ApplySampleDto applySample,
                                 List<SampleReportItemDto> reportItems, List<SampleResultDto> results,
                                 List<InstrumentReportItemDto> instrumentReportItems, List<ApplySampleItemDto> sampleItems) {

        final ProfessionalGroupDto group = groupService.selectByGroupId(sample.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException(String.format("专业组 [%s] 不存在", sample.getGroupName()));
        }

        final UserDto twoChecker = userService.selectByUserId(sample.getTwoCheckerId());
        if (Objects.isNull(twoChecker)) {
            throw new IllegalStateException(String.format("二次审核人 [%s] 不存在", sample.getTwoCheckerName()));
        }

        final UserDto oneChecker = userService.selectByUserId(sample.getOneCheckerId());
        if (Objects.isNull(oneChecker)) {
            throw new IllegalStateException(String.format("一次审核人 [%s] 不存在", sample.getOneCheckerName()));
        }

        final UserDto tester = userService.selectByUserId(applySample.getTesterId());
        if (Objects.isNull(tester)) {
            throw new IllegalStateException(String.format("检验者 [%s] 不存在", applySample.getTesterName()));
        }

        final PdfReportParamDto param = new PdfReportParamDto();

        param.put("sample",
                Dict.of("sampleNo", sample.getSampleNo(), "instrumentGroupName", sample.getInstrumentGroupName(),
                        "instrumentName", sample.getInstrumentName(), "testDate", sample.getTestDate(), "testerName",
                        applySample.getTesterName(), "oneCheckerName", sample.getOneCheckerName(), "twoCheckerName",
                        sample.getTwoCheckerName(), "checkerName", sample.getTwoCheckerName(), "sampleRemark",
                        applySample.getSampleRemark(), "resultRemark", applySample.getResultRemark(), "_sample",
                        Dict.parse(sample)));

        param.put("apply",
                Dict.of("masterBarcode", apply.getMasterBarcode(), "patientName", apply.getPatientName(), "patientAge",
                        apply.getPatientAge(), "samplingDate", apply.getSamplingDate(), "patientMobile",
                        apply.getPatientMobile(), "createDate", apply.getCreateDate(), "sendDoctorName",
                        apply.getSendDoctorName(), "patientCard", apply.getPatientCard(), "dept", apply.getDept(), "hspOrgName",
                        apply.getHspOrgName(), "_apply", Dict.parse(apply)));
        param.put("applySample",
                Dict.of("barcode", applySample.getBarcode(), "tubeName", applySample.getTubeName(), "sampleTypeName",
                        applySample.getSampleTypeName(), "groupName", applySample.getGroupName(), "onePickerName",
                        applySample.getOnePickerName(), "onePickDate", applySample.getOnePickDate(), "twoPickerName",
                        applySample.getTwoPickerName(), "twoPickDate", applySample.getTwoPickDate(), "_applySample",
                        Dict.parse(applySample)));
        param.put("testItems", Dict.of());

        // 过滤不打印的
        List<SampleReportItemDto> printItems = Lists.newArrayList();
        for (SampleReportItemDto dto : reportItems) {
            final InstrumentReportItemDto itemDto = instrumentReportItems.stream()
                    .filter(e -> Objects.equals(e.getInstrumentId(), sample.getInstrumentId())
                            && Objects.equals(e.getReportItemCode(), dto.getReportItemCode()))
                    .findFirst().orElse(instrumentReportItems.stream().findFirst().orElse(null));
            if (Objects.nonNull(itemDto) && Objects.equals(itemDto.getIsPrint(), YesOrNoEnum.YES.getCode())) {
                printItems.add(dto);
            }
        }

        List<SampleReportItemDto> sortedReportItems = printItems.stream().sorted(
                        Comparator.comparing(SampleReportItemDto::getPrintSort).thenComparing(SampleReportItemDto::getReportItemId))
                .collect(Collectors.toList());

        Map<String, InstrumentReportItemDto> instrumentReportItemByKey = instrumentReportItems.stream().collect(
                Collectors.toMap(obj -> obj.getInstrumentId() + "-" + obj.getReportItemCode(), Function.identity()));

        final List<Dict> reportItemsParam = new ArrayList<>();
        for (SampleReportItemDto reportItem : sortedReportItems) {
            final Dict dict = Dict.create();
            dict.set("_sampleReportItem", reportItem);
            dict.set("reportItemCode", reportItem.getReportItemCode());
            dict.set("reportItemName", reportItem.getReportItemName());
            dict.set("testItemCode", reportItem.getTestItemCode());
            dict.set("testItemName", reportItem.getTestItemName());

            final SampleResultDto sampleResult =
                    results.stream().filter(e -> Objects.equals(e.getReportItemCode(), reportItem.getReportItemCode()))
                            .findFirst().orElse(null);
            if (Objects.isNull(sampleResult)) {
                continue;
            }

            dict.set("_sampleResult", sampleResult);
            dict.set("result", sampleResult.getResult());
            dict.set("unit", sampleResult.getUnit());
            dict.set("range", sampleResult.getRange());
            dict.set("status", sampleResult.getStatus());
            dict.set("judge", sampleResult.getJudge());

            if (Objects.equals(sampleResult.getJudge(), TestJudgeEnum.UP.name())) {
                dict.set("upOrDown", "↑");
            } else if (Objects.equals(sampleResult.getJudge(), TestJudgeEnum.DOWN.name())) {
                dict.set("upOrDown", "↓");
            } else {
                dict.set("upOrDown", StringUtils.EMPTY);
            }

            // 优先取结果上 仪器
            InstrumentReportItemDto instrumentReportItemDto =
                    instrumentReportItemByKey.get(sampleResult.getInstrumentId() + "-" + sampleResult.getReportItemCode());
            if (Objects.isNull(instrumentReportItemDto)) {
                instrumentReportItemDto =
                        instrumentReportItemByKey.get(sample.getInstrumentId() + "-" + sampleResult.getReportItemCode());
            }
            if (Objects.isNull(instrumentReportItemDto)) {
                instrumentReportItemDto = new InstrumentReportItemDto();
            }
            dict.set("_instrumentReportItem", instrumentReportItemDto);

            reportItemsParam.add(dict);
        }

        param.put("reportItems", reportItemsParam);

        // 签名
        param.put("signature", Dict.of(
                // 检验者
                "tester", Dict.of("name", tester.getNickname(), "cnSign", tester.getCnSign(), "enSign", tester.getEnSign(), "sign",
                        StringUtils.defaultString(tester.getCnSign(), tester.getEnSign())),
                // 一次审核人
                "oneChecker",
                Dict.of("name", oneChecker.getNickname(), "cnSign", oneChecker.getCnSign(), "enSign",
                        oneChecker.getEnSign(), "sign",
                        StringUtils.defaultString(oneChecker.getCnSign(), oneChecker.getEnSign())),
                // 二次审核人
                "twoChecker",
                Dict.of("name", twoChecker.getNickname(), "cnSign", twoChecker.getCnSign(), "enSign",
                        twoChecker.getEnSign(), "sign",
                        StringUtils.defaultString(twoChecker.getCnSign(), twoChecker.getEnSign())),
                // 批准者
                "approver", Dict.of("name", group.getApproverName(), "sign", group.getApproverSign())));

        MatchBindReportDto matchBindReportDto = new MatchBindReportDto();
        if (CollectionUtils.isNotEmpty(sampleItems)){
            matchBindReportDto.setTestItemIds(sampleItems.stream().map(ApplySampleItemDto::getTestItemId)
                    .collect(Collectors.toList()));

        }
        //匹配模板
        matchBindReportDto.setBarcode(sample.getBarcode());
        matchBindReportDto.setInstrumentGroupId(sample.getInstrumentGroupId());
        matchBindReportDto.setInstrumentGroupName(sample.getInstrumentGroupName());
        matchBindReportDto.setHspOrgId(apply.getHspOrgId());
        matchBindReportDto.setHspOrgName(apply.getHspOrgName());
        matchBindReportDto.setGroupId(sample.getGroupId());
        ReportTemplateBindDto reportTemplateBind =  reportTemplateBindService.findMatchReportTemplate(matchBindReportDto);

        log.info("开始使用报告单模板 [{}] 生成 条码 [{}] 的报告单。 参数 [{}]", reportTemplateBind.getReportTemplateCode(), sample.getBarcode(),
                JSON.toJSONString(param));

        final String url = pdfReportService.build2Url(reportTemplateBind.getReportTemplateCode(), param);
        String key = getRebuildPdfKey(applySample);
        stringRedisTemplate.opsForValue().set(key, url, 3, TimeUnit.DAYS);

        return url;
    }

    /**
     * 获取重新生成报告的缓存 redis key
     */
    private String getRebuildPdfKey(ApplySampleDto applySample) {
        return redisPrefix.getBasePrefix() + "REBUILD_REPORT:" + applySample.getBarcode()
                + applySample.getOutBarcode() + applySample.getHspOrgCode();
    }



    // 判断是否可以出报告 -- 当配置了分血合并，并且包含分血样本 并且结果没有完全审核时结果是不允许给到报告平台的
    // 如果满足推送 则返回样本信息列表（分血合并的返回所有分管分血样本，不需要合并的返回入参样本列表applySamples）
    // 如果不满足推送要求 返回报错
    private List<ApplySampleDto> validCanReport(Long orgId, String hspOrgCode, String barcode,List<ApplySampleDto> applySamples) {

        if (CollectionUtils.isEmpty(applySamples)){
            return applySamples;
        }

        boolean configMerge = isConfigMerge(orgId, hspOrgCode, barcode);

        // 如果没配置合并打印 可以直接推送结果
        if (!configMerge) {
            log.info("条码号：{},送检机构：{},未配置分血合并，允许推送报告结果！", barcode, hspOrgCode);
            return applySamples;
        }


        // 根据外部条码号查询所有的样本信息 包含分管的以及分血的样本
        ApplySampleDto next = applySamples.iterator().next();
        List<ApplySampleDto> allApplySamples = applySampleService.selectByOutBarcodeAndHspOrgCode(next.getHspOrgCode(), next.getOutBarcode());

        // 过滤掉已经删除样本
        allApplySamples = allApplySamples.stream().filter(e -> Objects.equals(YesOrNoEnum.NO.getCode(), e.getIsDelete())).collect(Collectors.toList());

        // 判断是否分血，分血才需要合并报告单
        Optional<ApplySampleDto> first = allApplySamples.stream().filter(e -> Objects.equals(e.getIsSplitBlood(),YesOrNoEnum.YES.getCode())).findFirst();
        // 没有分血样本，直接返回原始样本信息
        if (!first.isPresent()) {
            log.info("条码号：{},送检机构：{},未分血，允许推送报告结果！", barcode, hspOrgCode);
            return applySamples;
        }

        // 非终止检验
        allApplySamples = allApplySamples.stream().filter(e -> !Objects.equals(SampleStatusEnum.STOP_TEST.getCode(), e.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allApplySamples)) {
            throw new IllegalArgumentException(String.format("条码号：%s, 送检机构：%s, 全部终止检验，不允许推送报告结果！", barcode, hspOrgCode));
        }

        // 如果有分血样本，判断是否所有的样本是否都已审核
        if (allApplySamples.stream().allMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode()))) {
            // 找到最大的分血条码后缀 -- 目前用不到 暂时先注释掉
//            Integer maxBarcode = allApplySamples.stream().filter(e -> e.getBarcode().contains(SPLIT_BLOOD_SUFFIX))
//                    .map(e -> e.getBarcode().split(SPLIT_BLOOD_SUFFIX)[NumberUtils.INTEGER_ONE])
//                    .max(Comparator.comparing(Integer::parseInt))
//                    .map(Integer::valueOf)
//                    .orElse(NumberUtils.INTEGER_ZERO);
//
//            // 更新分管样本的条码号 主要更新原始条码号和分血条码号
//            for (ApplySampleDto e : allApplySamples) {
//                // 如果是分管样本，则样本条码更新为分血条码
//                if (!Objects.equals(e.getBarcode(), barcode)) {
//                    // 将 maxBarcode 自增后的值以两位数格式化（不足两位前面补零）
//                    e.setBarcode(barcode + SPLIT_BLOOD_SUFFIX + String.format("%02d", ++maxBarcode));
//                }
//            }

            return allApplySamples;
        }

        // 到这里，说明 配置了分血合并 并且有分血样本，但是有未审核的样本，不允许推送
        throw new IllegalArgumentException("条码号：" + barcode + "，送检机构：" + hspOrgCode + "，存在未审核的分血样本，不允许推送报告结果！");
    }


    // 判是否配置了合并打印
    private boolean isConfigMerge(Long orgId, String hspOrgCode, String barcode) {
        //判断送检机构是否配置了合并打印
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.MERGE_PRINT.getCode(), orgId);
        log.info("合并打印报告单配置：{}", JSONObject.toJSONString(param));

        // 送检机构配置了合并打印
        return param != null && StringUtils.isNotBlank(param.getParamValue()) && Arrays.asList(param.getParamValue().split(",")).contains(hspOrgCode);
    }

    // 分管分血样本合并
    private List<OpenReportSampleVo> mergeReport(Long orgId, String hspOrgCode, String barcode, List<OpenReportSampleVo> limsSamples) {

        // 获取原始样本
        OpenReportSampleVo applySampleDto = limsSamples.stream().filter(e -> e.getBarcode().equals(barcode)).findFirst().orElse(limsSamples.iterator().next());
        // 获取最后审核的样本
        OpenReportSampleVo lastAuditSampleDto = limsSamples.stream().max(Comparator.comparing(OpenReportSampleVo::getTwoCheckDate)).orElse(applySampleDto);

        // 检验项目
        List<OpenReportSampleVo.TestItem> testItemCollect = limsSamples.stream().map(OpenReportSampleVo::getTestItems).flatMap(Collection::stream).collect(Collectors.toList());
        lastAuditSampleDto.setTestItems(testItemCollect);

        // 检验结果
        List<OpenReportSampleVo.Result> resultCollect = limsSamples.stream().map(OpenReportSampleVo::getResults).flatMap(Collection::stream).collect(Collectors.toList());
        lastAuditSampleDto.setResults(resultCollect);

        // 结果备注
        List<String> resultRemarks = limsSamples.stream().map(OpenReportSampleVo::getResultRemark).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < resultRemarks.size(); i++) {
            sb.append(i + 1 + "," + resultRemarks.get(i) + "\n");
        }
        lastAuditSampleDto.setResultRemark(sb.toString());

        //报告
//        final List<String> urls = limsSamples.stream().map(OpenReportSampleVo::getReportUrl).collect(Collectors.toList());
        List<String> urlList = reportService.mergePrint(limsSamples.stream().map(e -> e.getApplySampleId()).collect(Collectors.toSet()));
        lastAuditSampleDto.setReportUrl(mergePdfs(urlList, barcode));

        // 图片
        final List<String> imgUrls = limsSamples.stream().map(OpenReportSampleVo::getImgUrls).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        lastAuditSampleDto.setImgUrls(CollectionUtils.isEmpty(imgUrls) ? StringUtils.EMPTY : StringUtils.join(imgUrls, ","));

        ArrayList<OpenReportSampleVo> resultApplySampleDto = new ArrayList<>();
        resultApplySampleDto.add(lastAuditSampleDto);
        return resultApplySampleDto;
    }


    // 多张报告单合并成一张报告单
    public String mergePdfs(List<String> pdfUrls,String barcode) {

        // 地址为空，不进行合并
        if (CollectionUtils.isEmpty(pdfUrls)) {
            return StringUtils.EMPTY;
        }

        File tempFile = FileUtil.createTempFile(MERGE_TEMP_DIR, null, null, true);

        // 合并新地址成一个pdf
        List<File> files = new ArrayList<>();
        final PDFMergerUtility merger = new PDFMergerUtility();
        try {
            for (String pdfUrl : pdfUrls) {
                merger.addSource(huaweiObsUtils.downloadFileByUrl(pdfUrl));
            }
        } catch (Exception e) {
            log.error("pdf合并成一个url下载失败！外部条码号：{}",barcode, e);
            throw new IllegalArgumentException("pdf合并成一个url时 下载失败！外部条码号：" + barcode + " 异常信息：" + e.getMessage());
        }

        final PDDocumentInformation information = new PDDocumentInformation();
//        information.setKeywords("");
        merger.setDestinationFileName(tempFile.getAbsolutePath());
        merger.setDestinationDocumentInformation(information);

        try {
            merger.mergeDocuments(MemoryUsageSetting.setupTempFileOnly());
        } catch (Exception e) {
            log.error("pdf合并成一个url合成失败，返回多个pdf文件！外部条码号：{}",barcode, e);
            throw new IllegalArgumentException("pdf合并成一个url时 合成失败！外部条码号：" + barcode + " 异常信息：" + e.getMessage());
        } finally {
            files.forEach(FileUtils::deleteQuietly);
        }

        try {
            // 上传文件到obs
//            String objectKey = MERGE_FILE_PREFIX + LocalDateTime.now().format(DateTimeFormatter.ofPattern("/yyyy/MM/dd/")) + UUID.randomUUID() + PDF_SUFFIX;
            return huaweiObsUtils.upload(new FileInputStream(tempFile), MediaType.APPLICATION_PDF_VALUE);
        } catch (Exception e) {
            log.error("pdf合并成一个url上传失败，返回多个pdf文件！外部条码号：{}",barcode, e);
            throw new IllegalArgumentException("pdf合并成一个url时 上传失败！外部条码号：" + barcode + " 异常信息：" + e.getMessage());
        } finally {
            FileUtils.deleteQuietly(tempFile);
        }

    }

}
