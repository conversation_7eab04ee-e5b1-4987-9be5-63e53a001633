package com.labway.lims.apply.service.chain.applysampleitem.disable;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <pre>
 * SampleFlowCommand
 * 条码环节记录
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/12/10 18:55
 */
@Slf4j
@Component("ApplySampleItem_SampleFlowCommand")
public class SampleFlowCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {
        final ApplySampleItemDisableContext context = ApplySampleItemDisableContext.from(c);

        final List<ApplySampleItemDto> disableItems = context.getDisableItems();
        final List<ApplySampleItemDto> enableItems = context.getEnableItems();
        final Map<Long, ApplySampleDto> applySampleMap = context.getApplySampleMap();

        final LoginUserHandler.User user = LoginUserHandler.get();
        final Date now = new Date();
        final List<SampleFlowDto> flows = new ArrayList<>();

        // 项目禁用
        if (CollectionUtils.isNotEmpty(disableItems)) {
            disableItems.stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId))
                    .forEach((applySampleId, items) -> {
                        final ApplySampleDto applySampleDto = applySampleMap.get(applySampleId);
                        final String content = items.stream().map(ApplySampleItemDto::getTestItemName)
                                .collect(Collectors.joining(StringPool.COMMA)) + " 已禁用";

                        flows.add(buildSampleFlowDto(applySampleDto, BarcodeFlowEnum.APPLY_SAMPLE_ITEM_DISABLE, content, now));
                    });
            log.info("用户 [{}} 专业组 [{}] 禁用项目 [{}] ", user.getNickname(), user.getGroupName(),
                    disableItems.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.joining(StringPool.COMMA)));
        }

        // 项目取消禁用
        if (CollectionUtils.isNotEmpty(enableItems)) {
            enableItems.stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId))
                    .forEach((applySampleId, items) -> {
                        final ApplySampleDto applySampleDto = applySampleMap.get(applySampleId);
                        final String content = items.stream().map(ApplySampleItemDto::getTestItemName)
                                .collect(Collectors.joining(StringPool.COMMA)) + " 项目已取消禁用";

                        flows.add(buildSampleFlowDto(applySampleDto, BarcodeFlowEnum.APPLY_SAMPLE_ITEM_ENABLE, content, now));
                    });

            log.info("用户 [{}} 专业组 [{}] 取消禁用项目 [{}] ", user.getNickname(), user.getGroupName(),
                    enableItems.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.joining(StringPool.COMMA)));
        }

        sampleFlowService.addSampleFlows(flows);

        return CONTINUE_PROCESSING;
    }

    private SampleFlowDto buildSampleFlowDto(ApplySampleDto applySampleDto,
                                             BarcodeFlowEnum flowEnum, String content, Date now) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(applySampleDto.getApplyId());
        sampleFlow.setApplySampleId(applySampleDto.getApplySampleId());
        sampleFlow.setBarcode(applySampleDto.getBarcode());
        sampleFlow.setOperateCode(flowEnum.name());
        sampleFlow.setOperateName(flowEnum.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        sampleFlow.setContent(content);
        sampleFlow.setCreateDate(now);
        sampleFlow.setUpdateDate(now);
        sampleFlow.setCreatorId(user.getUserId());
        sampleFlow.setCreatorName(user.getNickname());
        sampleFlow.setUpdaterId(user.getUserId());
        sampleFlow.setUpdaterName(user.getNickname());
        sampleFlow.setOrgId(user.getOrgId());
        sampleFlow.setOrgName(user.getOrgName());
        sampleFlow.setIsDelete(YesOrNoEnum.NO.getCode());
        return sampleFlow;
    }

}
