package com.labway.lims.apply.service.chain.pick.two.cancel.immunity;

import com.google.common.collect.Lists;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.service.chain.pick.two.cancel.CancelTwoPickContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <pre>
 * ImmunityCancelTwoPickUnpickApplySampleUpdateCommand
 * 免疫二次分拣 取消二次分拣，项目合并至未分拣的申请单样本上
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/11/4 13:10
 */
@Slf4j
@Component
public class ImmunityCancelTwoPickUnpickApplySampleUpdateCommand implements Command, InitializingBean {
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        final CancelTwoPickContext context = CancelTwoPickContext.from(c);

        // 找出免疫分拣 还未分拣的那个样本
        final List<ApplySampleDto> unpickApplySamples = context.getApplySampleImmunitys().stream()
                .filter(e -> Objects.equals(e.getIsTwoPick(), YesOrNoEnum.NO.getCode())).collect(Collectors.toList());

        // 如果存在还未分拣的申请单样本，那么免疫二次分拣取消分拣就把项目合并到该申请单样本上
        if (CollectionUtils.isNotEmpty(unpickApplySamples)) {
            final ApplySampleDto unpickApplySampleDto = unpickApplySamples.iterator().next();

            if (unpickApplySamples.size() > 1) {
                log.error("取消二次分拣 条码数量错误 [{}] 条码号 [{}]", unpickApplySamples.size(), unpickApplySampleDto.getBarcode());
                throw new IllegalArgumentException("条码数量错误");
            }

            final long applySampleId = context.getApplySampleId();

            // 找出该申请单样本的检验项目（包含加急项目）
            final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(applySampleId);

            final LinkedList<Long> ids = snowflakeService.genIds(applySampleItems.size());
            applySampleItems.forEach(e -> {
                e.setApplySampleId(unpickApplySampleDto.getApplySampleId());
                e.setApplySampleItemId(ids.pop());
            });

            // 把当前申请单样本的检验项目 添加到这个未分拣的申请单样本上面
            applySampleItemService.addApplySampleItems(applySampleItems);

            // 删掉多处的申请单样本
            applySampleService.deleteByApplySampleId(context.getApplySampleId());
            // 删除项目
            applySampleItemService.deleteByApplySampleIds(Lists.newArrayList(context.getApplySampleId()));

            context.put(CancelTwoPickContext.APPLY_SAMPLES, List.of(unpickApplySampleDto));
            context.put(CancelTwoPickContext.UNPICK_APPLY_SAMPLE, unpickApplySampleDto);
        }

        return CONTINUE_PROCESSING;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
