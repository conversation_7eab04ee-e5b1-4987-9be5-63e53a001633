package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.apply.api.dto.RackLogicApplySampleDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 已经下架的样本
 */
@Getter
@Setter
public class UnloadSampleVo extends RackLogicApplySampleDto {

    /**
     * 检验项目名称
     */
    private List<String> testItemNames;

    /**
     * 送检机构
     */
    private String hspOrgName;


    /**
     * 病人名称
     */
    private String patientName;


    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;


    /**
     * 性别  性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 一次分拣人
     */
    private String onePickerName;

    /**
     * 一次分拣日期
     */
    private Date onePickDate;
}
