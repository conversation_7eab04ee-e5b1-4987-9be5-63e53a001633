package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/4/23 17:42
 */
@Setter
@Getter
public class PositiveRateVo {

    /**
     * 样本类型
     */
    private String sampleType;

    /**
     * 样本类型CODE
     */
    private String sampleCode;

    /**
     * testItemId
     */
    private Long testItemId;

    /**
     * 检验项目(检验目的)
     */
    private String testItemName;

    /**
     * 阳性样本数量
     */
    private Integer positiveNum;

    /**
     * 样本总数
     */
    private Integer sampleSum;

    /**
     * 细菌总数
     */
    private Integer germSum;

    /**
     * 样本阳性率
     */
    private String positiveRate;
}
