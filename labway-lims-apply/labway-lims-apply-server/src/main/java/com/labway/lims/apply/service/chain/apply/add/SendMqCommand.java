package com.labway.lims.apply.service.chain.apply.add;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Component
public class SendMqCommand implements Command {

    private static final String EVENT_ROUTING_KEY = "sample_change_key";

    @Resource
    private RabbitMQService rabbitMQService;

    @Override
    public boolean execute(Context c) throws Exception {
        final AddApplyContext from = AddApplyContext.from(c);
        final ApplyDto apply = from.getApply();
        final ApplyInfo applyInfo = from.getApplyInfo();
	    final List<ApplySampleDto> applySamples = from.getApplySamples();

	    // 发布事件 样本创建事件
        publishCreateApplySampleEvent(from, apply, applyInfo, applySamples);

        return CONTINUE_PROCESSING;
    }

    private void publishCreateApplySampleEvent(AddApplyContext from, ApplyDto apply, ApplyInfo applyInfo, List<ApplySampleDto> applySamples) {
        final LoginUserHandler.User user = from.getUser();
        for (ApplySampleDto m : applySamples) {
            final ApplySampleEventDto event = new ApplySampleEventDto();
            event.setEvent(ApplySampleEventDto.EventType.CreateApply);
            event.setOrgId(user.getOrgId());
            event.setHspOrgId(apply.getHspOrgId());
            event.setHspOrgCode(apply.getHspOrgCode());
            event.setHspOrgName(apply.getHspOrgName());
            event.setApplyId(applyInfo.getApplyId());
            event.setApplySampleId(m.getApplySampleId());
            event.setBarcode(m.getBarcode());
            event.setExtras(Map.of(
					// 项目类型 样本的项目类型
					ApplySampleEventDto.ExtrasConstants.ITEM_TYPE, m.getItemType()));
            rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, EVENT_ROUTING_KEY, JSON.toJSONString(event));
        }
    }
}
