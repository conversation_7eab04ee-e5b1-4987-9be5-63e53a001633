package com.labway.lims.apply.service.chain.apply.add;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class SendMqCommand implements Command {

    private static final String EVENT_ROUTING_KEY = "sample_change_key";

    @Resource
    private RabbitMQService rabbitMQService;

    @Override
    public boolean execute(Context c) throws Exception {
        final AddApplyContext from = AddApplyContext.from(c);
        final ApplyDto apply = from.getApply();
        final ApplyInfo applyInfo = from.getApplyInfo();

        // 发布事件 样本创建事件
        publishCreateApplySampleEvent(from, apply, applyInfo);

        return CONTINUE_PROCESSING;
    }

    private void publishCreateApplySampleEvent(AddApplyContext from, ApplyDto apply, ApplyInfo applyInfo) {
        final LoginUserHandler.User user = from.getUser();
        for (ApplyInfo.Sample m : applyInfo.getSamples()) {
            final ApplySampleEventDto event = new ApplySampleEventDto();
            event.setEvent(ApplySampleEventDto.EventType.CreateApply);
            event.setOrgId(user.getOrgId());
            event.setHspOrgId(apply.getHspOrgId());
            event.setHspOrgCode(apply.getHspOrgCode());
            event.setHspOrgName(apply.getHspOrgName());
            event.setApplyId(applyInfo.getApplyId());
            event.setApplySampleId(m.getApplySampleId());
            event.setBarcode(m.getBarcode());
            event.setExtras(Maps.newHashMap());
            rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, EVENT_ROUTING_KEY, JSON.toJSONString(event));
        }
    }
}
