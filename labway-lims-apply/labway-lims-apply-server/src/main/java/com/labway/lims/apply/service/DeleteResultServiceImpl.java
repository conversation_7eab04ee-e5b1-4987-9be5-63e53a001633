package com.labway.lims.apply.service;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.DeleteSampleResultDetailDto;
import com.labway.lims.apply.api.dto.DeleteSampleResultDto;
import com.labway.lims.apply.api.dto.DeleteSampleResultMainDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SelectDeleteSampleResultDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.DeleteResultService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.mapper.DeleteSampleResultDetailMapper;
import com.labway.lims.apply.mapper.DeleteSampleResultMainMapper;
import com.labway.lims.apply.model.TbDeleteSampleResultDetail;
import com.labway.lims.apply.model.TbDeleteSampleResultMain;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleResultService;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SaveResultInfoDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class DeleteResultServiceImpl extends ServiceImpl<DeleteSampleResultMainMapper, TbDeleteSampleResultMain> implements DeleteResultService {

    private final static String deleteSampleResultKey = "%s:delete:sample:result:%s";
    private static final Map<String, RecoveryResult> RECOVERY_RESULT_MAP = new HashMap<>();
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private DeleteSampleResultMainMapper deleteSampleResultMainMapper;
    @Resource
    private DeleteSampleResultDetailMapper deleteSampleResultDetailMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private OutsourcingSampleResultService outsourcingSampleResultService;
    @Resource
    private ApplySampleService applySampleService;
    @Autowired
    private SampleFlowService sampleFlowService;

    {
        RECOVERY_RESULT_MAP.put(ItemTypeEnum.ROUTINE.name(), new RecoveryRoutineResult());
        RECOVERY_RESULT_MAP.put(ItemTypeEnum.OUTSOURCING.name(), new RecoveryOutsourcingResult());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addDeleteSampleResult(DeleteSampleResultDto deleteSampleResultDto) {

        final Long sampleId = deleteSampleResultDto.getSampleId();
        final Date date = new Date();

        final String key = String.format(deleteSampleResultKey, redisPrefix.getBasePrefix(), sampleId);
        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, String.valueOf(sampleId), 3, TimeUnit.MINUTES))) {
            throw new IllegalStateException("样本结果正在删除中");
        }

        try {

            final LoginUserHandler.User user = LoginUserHandler.get();

            // 删除的样本主表
            TbDeleteSampleResultMain tbDeleteSampleResultMain = this.selectDeleteSampleResultMainBySampleId(sampleId);
            tbDeleteSampleResultMain = getTbDeleteSampleResultMain(deleteSampleResultDto, tbDeleteSampleResultMain, user, date);

            // 删除的样本结果
            final TbDeleteSampleResultDetail tbDeleteSampleResultDetail = getTbDeleteSampleResultDetail(deleteSampleResultDto, tbDeleteSampleResultMain, user, date);

            // 入库
            super.saveOrUpdate(tbDeleteSampleResultMain);
            deleteSampleResultDetailMapper.insert(tbDeleteSampleResultDetail);
        } finally {
            stringRedisTemplate.delete(key);
        }

    }

    @Override
    public DeleteSampleResultMainDto selectBySampleId(long sampleId) {
        return convert(this.selectDeleteSampleResultMainBySampleId(sampleId));
    }


    private List<DeleteSampleResultMainDto> selectBySampleIds(Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return Collections.emptyList();
        }
        return selectDeleteSampleResultMainBySampleIds(sampleIds)
                .stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<DeleteSampleResultMainDto> selectDeletedSample(SelectDeleteSampleResultDto dto) {
        return deleteSampleResultMainMapper.selectList(
                        Wrappers.lambdaQuery(TbDeleteSampleResultMain.class)
                                .between(TbDeleteSampleResultMain::getDeleteDate, dto.getDeleteStartDate(), dto.getDeleteEndDate())
                                .between(Objects.nonNull(dto.getTestStartDate()) && Objects.nonNull(dto.getTestEndDate()),
                                        TbDeleteSampleResultMain::getTestDate, dto.getTestStartDate(), dto.getTestEndDate())
                                .eq(Objects.nonNull(dto.getGroupId()), TbDeleteSampleResultMain::getGroupId, dto.getGroupId())
                                .eq(StringUtils.isNotBlank(dto.getSampleNo()), TbDeleteSampleResultMain::getSampleNo, dto.getSampleNo())
                                .eq(StringUtils.isNotBlank(dto.getBarcode()), TbDeleteSampleResultMain::getBarcode, dto.getBarcode())
                                .orderByAsc(TbDeleteSampleResultMain::getTestDate)
                )
                .stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<DeleteSampleResultDetailDto> selectDeletedResult(long deleteSampleResultMainId) {
        return deleteSampleResultDetailMapper.selectList(
                        Wrappers.lambdaQuery(TbDeleteSampleResultDetail.class)
                                .eq(TbDeleteSampleResultDetail::getDeleteSampleResultMainId, deleteSampleResultMainId)
                )
                .stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deletedBySampleIds(Collection<Long> sampleIds) {

        final List<DeleteSampleResultMainDto> mainDtos = selectBySampleIds(sampleIds);

        if (CollectionUtils.isEmpty(mainDtos)) {
            return;
        }

        deleteSampleResultMainMapper.delete(Wrappers.lambdaQuery(TbDeleteSampleResultMain.class)
                .in(TbDeleteSampleResultMain::getSampleId, sampleIds));
        deleteSampleResultDetailMapper.delete(Wrappers.lambdaQuery(TbDeleteSampleResultDetail.class)
                .in(TbDeleteSampleResultDetail::getDeleteSampleResultMainId, mainDtos.stream()
                        .map(DeleteSampleResultMainDto::getDeleteSampleResultMainId)
                        .collect(Collectors.toList())));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean recoveryResult(long mainId, Set<String> reportItemCodes) {

        final LoginUserHandler.User user = LoginUserHandler.get();

        final TbDeleteSampleResultMain tbDeleteSampleResultMain = deleteSampleResultMainMapper.selectById(mainId);
        Assert.notNull(tbDeleteSampleResultMain, "删除的样本不存在");


        final RecoveryResult recoveryResult = RECOVERY_RESULT_MAP.get(tbDeleteSampleResultMain.getItemType());
        Assert.notNull(recoveryResult, "项目类型异常:" + tbDeleteSampleResultMain.getItemType());

        // 校验样本状态
        final ApplySampleDto applySampleDto = recoveryResult.checkSampleStatus(tbDeleteSampleResultMain.getSampleId());

        final List<DeleteSampleResultDetailDto> deleteSampleResultDetailDtos = this.selectDeleteSampleResultDetailByMainId(tbDeleteSampleResultMain.getDeleteSampleResultMainId());
        // 是否要删除项目 true 中的都是要删除的，  false的都不删除
        final Map<Boolean, List<DeleteSampleResultDetailDto>> resultDetailMap = deleteSampleResultDetailDtos
                .stream()
                .collect(Collectors.groupingBy(e -> reportItemCodes.contains(e.getReportItemCode())));

        // 要恢复的数据
        final List<DeleteSampleResultDetailDto> recoveryResultList = resultDetailMap.getOrDefault(Boolean.TRUE, Collections.emptyList());
        // 不恢复的数据
        final List<DeleteSampleResultDetailDto> noRecoveryResultList = resultDetailMap.getOrDefault(Boolean.FALSE, Collections.emptyList());


        if (CollectionUtils.isEmpty(recoveryResultList)) {
            throw new IllegalArgumentException("没有可以删除的报告项目");
        }

        // 删除所选报告项目结果
        deleteSampleResultDetailMapper.deleteBatchIds(recoveryResultList.stream().map(DeleteSampleResultDetailDto::getDeleteSampleResultDetailId).collect(Collectors.toList()));

        // 如果没有其他结果， 则把主表删除掉
        if (CollectionUtils.isEmpty(noRecoveryResultList)) {
            deleteSampleResultMainMapper.deleteById(mainId);
        }


        // 恢复结果
        recoveryResult.recovery(tbDeleteSampleResultMain.getSampleId(),
                tbDeleteSampleResultMain.getSampleNo(),
                applySampleDto,
                recoveryResultList);


        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setApplyId(applySampleDto.getApplyId());
        sampleFlow.setApplySampleId(applySampleDto.getApplySampleId());
        sampleFlow.setBarcode(tbDeleteSampleResultMain.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.RECOVERY_RESULT.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.RECOVERY_RESULT.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        sampleFlow.setContent(String.format("恢复报告项目:\n%s", recoveryResultList.stream().map(e -> e.getReportItemName() + ":" + e.getResult()).collect(Collectors.joining("\n"))));
        sampleFlowService.addSampleFlow(sampleFlow);

        return true;
    }

    private TbDeleteSampleResultMain selectDeleteSampleResultMainBySampleId(long sampleId) {
        return deleteSampleResultMainMapper.selectOne(
                Wrappers.lambdaQuery(TbDeleteSampleResultMain.class)
                        .eq(TbDeleteSampleResultMain::getSampleId, sampleId)
                        .last("limit 1")
        );
    }

    private List<TbDeleteSampleResultMain> selectDeleteSampleResultMainBySampleIds(Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return Collections.emptyList();
        }
        return deleteSampleResultMainMapper.selectList(
                Wrappers.lambdaQuery(TbDeleteSampleResultMain.class)
                        .in(TbDeleteSampleResultMain::getSampleId, sampleIds)
        );
    }

    private List<DeleteSampleResultDetailDto> selectDeleteSampleResultDetailByMainId(long mainId) {
        return deleteSampleResultDetailMapper.selectList(
                        Wrappers.lambdaQuery(TbDeleteSampleResultDetail.class)
                                .eq(TbDeleteSampleResultDetail::getDeleteSampleResultMainId, mainId)
                )
                .stream().map(this::convert)
                .collect(Collectors.toList());
    }

    private DeleteSampleResultMainDto convert(TbDeleteSampleResultMain entity) {
        return JSON.parseObject(JSON.toJSONString(entity), DeleteSampleResultMainDto.class);
    }

    private DeleteSampleResultDetailDto convert(TbDeleteSampleResultDetail entity) {
        return JSON.parseObject(JSON.toJSONString(entity), DeleteSampleResultDetailDto.class);
    }

    private TbDeleteSampleResultDetail getTbDeleteSampleResultDetail(DeleteSampleResultDto deleteSampleResultDto, TbDeleteSampleResultMain tbDeleteSampleResultMain, LoginUserHandler.User user, Date date) {
        final Map<String, List<InstrumentReportItemDto>> instrumentReportItemsMap =
                instrumentReportItemService.selectByInstrumentGroupId(deleteSampleResultDto.getInstrumentGroupId())
                        .stream()
                        .collect(Collectors.groupingBy(InstrumentReportItemDto::getReportItemCode));

        final InstrumentReportItemDto instrumentReportItemDto = ObjectUtils.defaultIfNull(InstrumentReportItemDto.getInstrumentReportItemDto(instrumentReportItemsMap, deleteSampleResultDto.getReportItemCode(), deleteSampleResultDto.getSampleInstrumentId()), new InstrumentReportItemDto());
        final TbDeleteSampleResultDetail tbDeleteSampleResultDetail = new TbDeleteSampleResultDetail();
        tbDeleteSampleResultDetail.setDeleteSampleResultDetailId(snowflakeService.genId());
        tbDeleteSampleResultDetail.setDeleteSampleResultMainId(tbDeleteSampleResultMain.getDeleteSampleResultMainId());

        tbDeleteSampleResultDetail.setTestItemId(deleteSampleResultDto.getTestItemId());
        tbDeleteSampleResultDetail.setTestItemCode(deleteSampleResultDto.getTestItemCode());
        tbDeleteSampleResultDetail.setTestItemName(deleteSampleResultDto.getTestItemName());

        tbDeleteSampleResultDetail.setReportItemId(deleteSampleResultDto.getReportItemId());
        tbDeleteSampleResultDetail.setReportItemCode(deleteSampleResultDto.getReportItemCode());
        tbDeleteSampleResultDetail.setReportItemName(deleteSampleResultDto.getReportItemName());

        tbDeleteSampleResultDetail.setResult(deleteSampleResultDto.getResult());
        tbDeleteSampleResultDetail.setInstrumentId(deleteSampleResultDto.getResultInstrumentId(), deleteSampleResultDto.getSampleInstrumentId());
        tbDeleteSampleResultDetail.setIsHandeResult(deleteSampleResultDto.getIsHandeResult());
        tbDeleteSampleResultDetail.setEnName(StringUtils.defaultString(instrumentReportItemDto.getEnName()));
        tbDeleteSampleResultDetail.setCreateId(user.getUserId());
        tbDeleteSampleResultDetail.setCreateName(user.getNickname());
        tbDeleteSampleResultDetail.setCreateDate(date);
        tbDeleteSampleResultDetail.setUpdateId(user.getUserId());
        tbDeleteSampleResultDetail.setUpdateName(user.getNickname());
        tbDeleteSampleResultDetail.setUpdateDate(date);
        tbDeleteSampleResultDetail.setIsDelete(YesOrNoEnum.NO.getCode());
        return tbDeleteSampleResultDetail;
    }

    private TbDeleteSampleResultMain getTbDeleteSampleResultMain(DeleteSampleResultDto deleteSampleResultDto, TbDeleteSampleResultMain tbDeleteSampleResultMain, LoginUserHandler.User user, Date date) {
        if (tbDeleteSampleResultMain == null) {

            tbDeleteSampleResultMain = new TbDeleteSampleResultMain();
            tbDeleteSampleResultMain.setDeleteSampleResultMainId(snowflakeService.genId());
            tbDeleteSampleResultMain.setSampleId(deleteSampleResultDto.getSampleId());
            tbDeleteSampleResultMain.setBarcode(deleteSampleResultDto.getBarcode());
            tbDeleteSampleResultMain.setSampleNo(deleteSampleResultDto.getSampleNo());
            tbDeleteSampleResultMain.setItemType(deleteSampleResultDto.getItemTypeEnum().name());
            tbDeleteSampleResultMain.setTestDate(deleteSampleResultDto.getTestDate());
            tbDeleteSampleResultMain.setGroupId(user.getGroupId());
            tbDeleteSampleResultMain.setGroupName(user.getGroupName());
            tbDeleteSampleResultMain.setCreateId(user.getUserId());
            tbDeleteSampleResultMain.setCreateName(user.getNickname());
            tbDeleteSampleResultMain.setCreateDate(date);
            tbDeleteSampleResultMain.setIsDelete(YesOrNoEnum.NO.getCode());
        }
        // 新增其他删除结果时， 更新删除信息
        tbDeleteSampleResultMain.setDeleteUserId(user.getUserId());
        tbDeleteSampleResultMain.setDeleteUserName(user.getNickname());
        tbDeleteSampleResultMain.setDeleteDate(deleteSampleResultDto.getDeleteDate());
        tbDeleteSampleResultMain.setUpdateId(user.getUserId());
        tbDeleteSampleResultMain.setUpdateName(user.getNickname());
        tbDeleteSampleResultMain.setUpdateDate(date);
        return tbDeleteSampleResultMain;
    }

    public interface RecoveryResult {
        ApplySampleDto checkSampleStatus(long sampleId);

        void recovery(long sampleId, String sampleNo, ApplySampleDto applySampleDto, List<DeleteSampleResultDetailDto> deleteResultDtoList);
    }

    public class RecoveryRoutineResult implements RecoveryResult {
        public ApplySampleDto checkSampleStatus(long sampleId) {
            final SampleDto sampleDto = sampleService.selectBySampleId(sampleId);
            if (Objects.isNull(sampleDto)) {
                throw new IllegalArgumentException("常规样本不存在");
            }
            final ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleDto.getApplySampleId());
            if (Objects.isNull(applySampleDto)) {
                throw new IllegalArgumentException("申请单样本不存在");
            }
            // 状态不为未审 or 待复查
            if (!Set.of(SampleStatusEnum.NOT_AUDIT.getCode(), SampleStatusEnum.RETEST.getCode()).contains(applySampleDto.getStatus())) {
                throw new IllegalArgumentException("仅支持恢复未审的样本，请核实样本状态后重试");
            }
            return applySampleDto;
        }

        @Override
        public void recovery(long sampleId, String sampleNo, ApplySampleDto applySampleDto, List<DeleteSampleResultDetailDto> deleteResultDtoList) {
            final List<SaveResultInfoDto> saveResultInfoDtos = sampleReportItemService.recoveryResult(sampleId, sampleNo, applySampleDto, deleteResultDtoList);
        }
    }

    public class RecoveryOutsourcingResult implements RecoveryResult {
        public ApplySampleDto checkSampleStatus(long sampleId) {
            final OutsourcingSampleDto sampleDto = outsourcingSampleService.selectByOutsourcingSampleId(sampleId);
            if (Objects.isNull(sampleDto)) {
                throw new IllegalArgumentException("外送样本不存在");
            }
            final ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleDto.getApplySampleId());
            if (Objects.isNull(applySampleDto)) {
                throw new IllegalArgumentException("申请单样本不存在");
            }
            // 状态不为未审 or 待复查
            if (!Set.of(SampleStatusEnum.NOT_AUDIT.getCode(), SampleStatusEnum.RETEST.getCode()).contains(applySampleDto.getStatus())) {
                throw new IllegalArgumentException("仅支持恢复未审的样本，请核实样本状态后重试");
            }
            return applySampleDto;
        }

        @Override
        public void recovery(long sampleId, String sampleNo, ApplySampleDto applySampleDto, List<DeleteSampleResultDetailDto> deleteResultDtoList) {
//            outsourcingSampleResultService.recoveryResult(sampleId, sampleNo, applySampleDto, deleteResultDtoList);
        }

    }

}
