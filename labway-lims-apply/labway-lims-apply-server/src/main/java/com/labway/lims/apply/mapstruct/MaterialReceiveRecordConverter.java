package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRecordDto;
import com.labway.lims.apply.api.dto.SelectMaterialReceiveRecordDto;
import com.labway.lims.apply.model.TbMaterialReceiveRecord;
import com.labway.lims.apply.vo.MaterialReceiveRecordListRequestVo;
import com.labway.lims.apply.vo.MaterialReceiveRecordListResponseVo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 物料登记 相关转换
 * 
 * <AUTHOR>
 * @since 2023/5/9 17:03
 */
@Mapper(componentModel = "spring")
public interface MaterialReceiveRecordConverter {

    SelectMaterialReceiveRecordDto fromMaterialReceiveRecordListRequestVo(MaterialReceiveRecordListRequestVo vo);

    MaterialReceiveRecordListResponseVo recordListFromMaterialReceiveRecordListRequestVo(MaterialReceiveRecordDto dto);

    MaterialReceiveRecordDto fromTbMaterialReceiveRecord(TbMaterialReceiveRecord obj);
    TbMaterialReceiveRecord fromMaterialReceiveRecordDto(MaterialReceiveRecordDto obj);

    List<MaterialReceiveRecordDto> fromTbMaterialReceiveRecordList(List<TbMaterialReceiveRecord> list);

    MaterialReceiveRecordDto fromMaterialInventoryDto(MaterialInventoryDto obj);

    List<TbMaterialReceiveRecord> fromMaterialReceiveRecordDtoList(List<MaterialReceiveRecordDto> list);


}
