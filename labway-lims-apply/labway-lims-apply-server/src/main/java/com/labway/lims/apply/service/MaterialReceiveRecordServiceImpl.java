package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.MaterialReceiveRecordDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRegisterItemDto;
import com.labway.lims.apply.api.dto.SelectMaterialReceiveRecordDto;
import com.labway.lims.apply.api.service.MaterialReceiveRecordService;
import com.labway.lims.apply.mapper.TbMaterialReceiveRecordMapper;
import com.labway.lims.apply.mapstruct.MaterialReceiveRecordConverter;
import com.labway.lims.apply.model.TbMaterialReceiveRecord;
import com.labway.lims.apply.service.chain.material.receive.invalid.MaterialReceiveInvalidChain;
import com.labway.lims.apply.service.chain.material.receive.invalid.MaterialReceiveInvalidContext;
import com.labway.lims.apply.service.chain.material.receive.register.MaterialReceiveRegisterChain;
import com.labway.lims.apply.service.chain.material.receive.register.MaterialReceiveRegisterContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 物料领用记录 Service impl
 * 
 * <AUTHOR>
 * @since 2023/5/9 17:09
 */
@Slf4j
@DubboService
public class MaterialReceiveRecordServiceImpl implements MaterialReceiveRecordService {

    @Resource
    private TbMaterialReceiveRecordMapper tbMaterialReceiveRecordMapper;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private MaterialReceiveRecordConverter materialReceiveRecordConverter;
    @Resource
    private MaterialReceiveInvalidChain materialReceiveInvalidChain;

    @Resource
    private MaterialReceiveRegisterChain materialReceiveRegisterChain;

    @Override
    public List<MaterialReceiveRecordDto> selectBySelectMaterialReceiveRecordDto(SelectMaterialReceiveRecordDto dto) {
        LambdaQueryWrapper<TbMaterialReceiveRecord> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.ge(Objects.nonNull(dto.getBeginReceiverDate()), TbMaterialReceiveRecord::getReceiverDate,
            dto.getBeginReceiverDate());
        queryWrapper.le(Objects.nonNull(dto.getEndReceiverDate()), TbMaterialReceiveRecord::getReceiverDate,
            dto.getEndReceiverDate());

        queryWrapper.eq(TbMaterialReceiveRecord::getGroupId, dto.getGroupId());
        queryWrapper.eq(TbMaterialReceiveRecord::getOrgId, dto.getOrgId());
        queryWrapper.orderByDesc(TbMaterialReceiveRecord::getReceiverDate);

        return materialReceiveRecordConverter
            .fromTbMaterialReceiveRecordList(tbMaterialReceiveRecordMapper.selectList(queryWrapper));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReceiveRegister(List<MaterialReceiveRegisterItemDto> list) {

        Set<Long> inventoryIds =
            list.stream().map(MaterialReceiveRegisterItemDto::getInventoryId).collect(Collectors.toSet());
        LoginUserHandler.User user = LoginUserHandler.get();

        final MaterialReceiveRegisterContext context = new MaterialReceiveRegisterContext();
        context.setRegisterItemList(list);
        context.setUser(user);

        try {
            if (!materialReceiveRegisterChain.execute(context)) {
                throw new IllegalStateException("领用登记失败");
            }
        } catch (RuntimeException e) {
            log.error("领用登记失败 [{}]", inventoryIds, e);
            throw e;
        } catch (Exception e) {
            log.error("领用登记失败 [{}]", inventoryIds, e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("领用登记 [{}] 耗时\n{}", inventoryIds, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReceiveInvalid(long receiveId) {

        LoginUserHandler.User user = LoginUserHandler.get();

        final MaterialReceiveInvalidContext context = new MaterialReceiveInvalidContext();
        context.setReceiveId(receiveId);
        context.setUser(user);

        try {
            if (!materialReceiveInvalidChain.execute(context)) {
                throw new IllegalStateException("领用作废失败");
            }
        } catch (RuntimeException e) {
            log.error("领用作废失败 [{}]", receiveId, e);
            throw e;
        } catch (Exception e) {
            log.error("领用作废失败 [{}]", receiveId, e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("领用作废 [{}] 耗时\n{}", receiveId, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMaterialReceiveRecords(List<MaterialReceiveRecordDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 领用记录
        List<TbMaterialReceiveRecord> targetList =
            materialReceiveRecordConverter.fromMaterialReceiveRecordDtoList(list);
        // 数量 分区批次插入
        List<List<TbMaterialReceiveRecord>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbMaterialReceiveRecordMapper.batchAddMaterialReceiveRecords(item));

        log.info("用户 [{}] 新增入库记录[{}]成功", loginUser.getNickname(), JSON.toJSONString(targetList));

    }

    @Nullable
    @Override
    public MaterialReceiveRecordDto selectByReceiveId(long receiveId) {
        return materialReceiveRecordConverter
            .fromTbMaterialReceiveRecord(tbMaterialReceiveRecordMapper.selectById(receiveId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByReceiveId(MaterialReceiveRecordDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbMaterialReceiveRecord target = materialReceiveRecordConverter.fromMaterialReceiveRecordDto(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbMaterialReceiveRecordMapper.updateById(target) < 1) {
            throw new LimsException("修改物料领用记录失败");
        }

        log.info("用户 [{}] 修改物料领用记录成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

}
