package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.StopTestStatus;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 终止检验展示检验项目信息
 *
 * <AUTHOR>
 * @since 2023/8/10 11:47
 */
@Getter
@Setter
public class TerminateItemInfoResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 条码
     */
    private String barcode;

    /**
     * id
     */
    private Long applySampleItemId;
    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;
    /**
     * 终止检验状态：0正常，1终止收费，2终止不收费
     *
     * @see StopTestStatus
     */
    private Integer stopStatus;
}
