package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.service.chain.pick.two.TwoPickCheckApplySampleStatusCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 校验申请单样本状态
 */
@Slf4j
@Component
class MultiTwoPickCheckApplySampleStatusCommand implements Command {

    @Resource
    private TwoPickCheckApplySampleStatusCommand twoPickCheckApplySampleStatusCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final MultiTwoPickContext context = MultiTwoPickContext.from(c);

        for (ApplySampleDto applySample : context.getApplySamples()) {
            if (Objects.equals(applySample.getIsOutsourcing(), YesOrNoEnum.YES.getCode())) {
                throw new IllegalStateException(String.format("条码 [%s] 是外送样本无法批量分拣", applySample.getBarcode()));
            }
        }

        for (ApplySampleDto applySample : context.getApplySamples()) {
            twoPickCheckApplySampleStatusCommand.execute(new TwoPickContext(new TwoPickDto()
                    .setApplySampleId(applySample.getApplySampleId())));
        }

        return CONTINUE_PROCESSING;
    }

}
