package com.labway.lims.apply.service.chain.pick.two.cancel;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.RackDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.List;

@Getter
@Setter
@SuppressWarnings("unchecked")
public class CancelTwoPickContext extends StopWatchContext {

    /**
     * 要取消分拣的申请单样本
     */
    public static final String APPLY_SAMPLES = "APPLY_SAMPLES_" + IdUtil.objectId();

    /**
     * 是否来自血培养
     */
    public static final String FROM_BC = IdUtil.objectId();

    /**
     * 试管架
     */
    public static final String RACK = "RACK_" + IdUtil.objectId();

    /**
     * 要保留的申请单样本，因为涉及到加急。取消二次分拣后要把加急的样本删掉
     */
    public static final String ALIVE_APPLY_SAMPLE_ID = "ALIVE_APPLY_SAMPLE_ID_" + IdUtil.objectId();

    /**
     * 检验项目
     */
    public static final String APPLY_TEST_ITEMS = "TEST_ITEMS_" + IdUtil.objectId();

    /**
     * 关联的免疫分拣的样本
     */
    public static final String APPLY_SAMPLES_IMMUNITY = "APPLY_SAMPLES_IMMUNITY_" + IdUtil.objectId();

    /**
     * 未分拣的申请单样本
     */
    public static final String UNPICK_APPLY_SAMPLE = "UNPICK_APPLY_SAMPLE_" + IdUtil.objectId();

    /**
     * 取消标记免疫二次分拣
     */
    public static final String IS_IMMUNITY_TWO_PICK = "UNMARK_IMMUNITY_TWO_PICK_" + IdUtil.objectId();


    private String barcode;

    /**
     * 保留的申请单样本ID
     */
    private Long aliveApplySampleId;

    /**
     * 是不是恢复终止检验
     */
    private boolean regainTerminate = false;

    public static CancelTwoPickContext from(Context c) {
        return (CancelTwoPickContext) c;
    }

    public List<ApplySampleDto> getApplySamples() {
        return (List<ApplySampleDto>) get(APPLY_SAMPLES);
    }


    public RackDto getRack() {
        return (RackDto) get(RACK);
    }


    public List<ApplySampleItemDto> getApplySampleItems() {
        return (List<ApplySampleItemDto>) get(APPLY_TEST_ITEMS);
    }

    public long getApplySampleId() {
        return (long) get(ALIVE_APPLY_SAMPLE_ID);
    }

    public Long setApplySampleId(Long applySampleId) {
        return (Long) put(ALIVE_APPLY_SAMPLE_ID, applySampleId);
    }

    /**
     * 此样本是否来自血培养
     */
    public boolean isFromBloodCulture(Long applySampleId) {
        return containsKey(FROM_BC + applySampleId) && (Boolean) get(FROM_BC + applySampleId);
    }

    public List<ApplySampleDto> getApplySampleImmunitys() {
        return (List<ApplySampleDto>) get(APPLY_SAMPLES_IMMUNITY);
    }

    /**
     * 未分拣的申请单样本
     */
    public ApplySampleDto getUnpickApplySample() {
        return (ApplySampleDto) get(UNPICK_APPLY_SAMPLE);
    }

    public void setIsImmunityTwoPick(Integer isImmunityTwoPick) {
        put(IS_IMMUNITY_TWO_PICK, isImmunityTwoPick);
    }

    public Integer getIsImmunityTwoPick() {
        return (Integer) get(IS_IMMUNITY_TWO_PICK);
    }

    @Override
    protected String getWatcherName() {
        return "取消二次分拣";
    }

}
