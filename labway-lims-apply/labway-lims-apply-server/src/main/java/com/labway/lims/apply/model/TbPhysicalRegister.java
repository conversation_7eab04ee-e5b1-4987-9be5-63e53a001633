package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 体检花名册
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_physical_register")
public class TbPhysicalRegister implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    private Long physicalRegisterId;
    /**
     * 名称
     */
    private String patientName;
    /**
     * 年龄
     */
    private Integer patientAge;
    /**
     * 子年龄
     */
    private Integer patientSubage;
    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;
    /**
     * 生日
     */
    private Date patientBirthday;
    /**
     * 身份证
     */
    private String patientCard;
    /**
     * 证件类型
     *
     * @see PatientCardTypeEnum
     */
    private String patientCardType;
    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;
    /**
     * 手机号
     */
    private String patientMobile;
    /**
     * 地址
     */
    private String patientAddress;
    /**
     * 标本序号
     */
    private String sampleSort;
    /**
     * 套餐，仅说明作用
     */
    private String testPackage;
    /**
     * 备注
     */
    private String remark;
    /**
     * 部门或班级
     */
    private String dept;
    /**
     * 床号
     */
    private String patientBed;
    /**
     * 体检单位
     */
    private Long physicalCompanyId;
    /**
     * 体检单位
     */
    private String physicalCompanyName;
    /**
     * 批次号id
     */
    private Long physicalBatchId;
    /**
     * 检验机构
     */
    private Long orgId;
    /**
     * 检验机构
     */
    private String orgName;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 更新人
     */
    private String updaterName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 1:删除 0:未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 申请人
     */
    private String applicant;
    /**
     * 1是 0否已打印
     */
    private Integer isPrint;
    /**
     * 就诊类型编码
     */
    private String applyTypeCode;

    /**
     * 就诊类型名称
     */
    private String applyTypeName;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 套餐说明
     */
    private String testPackageDesc;

    /**
     * 临床诊断
     */
    private String diagnosis;
}
