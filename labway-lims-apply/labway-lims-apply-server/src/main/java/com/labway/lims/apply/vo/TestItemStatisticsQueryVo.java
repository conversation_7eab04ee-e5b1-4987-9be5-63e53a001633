package com.labway.lims.apply.vo;

import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Date;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/4/24 15:46
 */
@Setter
@Getter
public class TestItemStatisticsQueryVo {


    /**
     * groupId
     */
    private Set<Long> groupIds;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 默认当月1号和当前日期的前一天
     */
    public void defaultDate() {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            final Date date = new Date();
            startDate = DateUtil.beginOfMonth(date);
            endDate = DateUtil.endOfDay(DateUtils.addDays(date, -1));
        }

    }
}
