package com.labway.lims.apply.service.pda;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.PdaApplySampleItemDto;
import com.labway.lims.apply.api.service.pda.PdaApplySampleItemService;
import com.labway.lims.apply.mapper.pda.PdaApplySampleItemMapper;
import com.labway.lims.apply.model.pda.TbPdaApplySampleItem;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.ReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * pda 申请单项目
 */
@Slf4j
@DubboService
public class PdaApplySampleItemServiceImpl extends ServiceImpl<PdaApplySampleItemMapper, TbPdaApplySampleItem>
        implements PdaApplySampleItemService {

    @DubboReference
    private ReportItemService reportItemService;

    @Override
    public void addBacthApplySampleItem(List<ApplySampleItemDto> applySampleItems) {
        if (CollectionUtils.isEmpty(applySampleItems)) {
            return;
        }
        List<TbPdaApplySampleItem> tbPdaApplySampleItems = applySampleItems.stream().map(e -> {
            TbPdaApplySampleItem tbPdaApplySampleItem = JSON.parseObject(JSON.toJSONString(e), TbPdaApplySampleItem.class);
            tbPdaApplySampleItem.setPdaApplyId(e.getApplyId());
            tbPdaApplySampleItem.setPdaApplySampleItemId(e.getApplySampleItemId());
            return tbPdaApplySampleItem;
        }).collect(Collectors.toList());
        super.saveBatch(tbPdaApplySampleItems);
    }

    @Override
    public boolean deleteByApplyId(Serializable applyId) {
        return Objects.nonNull(applyId) && super.remove(new LambdaUpdateWrapper<TbPdaApplySampleItem>()
                .eq(TbPdaApplySampleItem::getPdaApplyId, applyId));
    }

    @Override
    public List<PdaApplySampleItemDto> selectByPdaApplyId(Serializable pdaApplyId) {
        if (Objects.isNull(pdaApplyId)) {
            return List.of();
        }
        // pda申请单下的项目
        final List<PdaApplySampleItemDto> pdaApplySampleItems = baseMapper.selectList(new LambdaQueryWrapper<TbPdaApplySampleItem>()
                        .eq(TbPdaApplySampleItem::getPdaApplyId, pdaApplyId))
                .stream().map(this::convert).collect(Collectors.toList());

        final List<Long> testItemIds = pdaApplySampleItems.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toList());
        // 项目下的报告项目
        final Map<Long, List<ReportItemDto>> reportItemMap = reportItemService.selectByTestItemIds(testItemIds)
                .stream().collect(Collectors.groupingBy(ReportItemDto::getTestItemId));

        return pdaApplySampleItems.stream()
                .peek(e -> e.setReportItemDtoList(reportItemMap.get(e.getTestItemId())))
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<PdaApplySampleItemDto>> selectByPdaApplyIds(Collection<Serializable> pdaApplyIds) {
        if (CollectionUtils.isEmpty(pdaApplyIds)) {
            return Map.of();
        }
        // pda申请单下的项目
        final Map<Long, List<PdaApplySampleItemDto>> pdaApplyIdMap = baseMapper.selectList(new LambdaQueryWrapper<TbPdaApplySampleItem>()
                        .in(TbPdaApplySampleItem::getPdaApplyId, pdaApplyIds))
                .stream().map(this::convert).collect(Collectors.groupingBy(PdaApplySampleItemDto::getPdaApplyId));

        final List<Long> testItemIds = pdaApplyIdMap.values().stream().flatMap(List::stream).map(ApplySampleItemDto::getTestItemId).collect(Collectors.toList());
        // 项目下的报告项目
        final Map<Long, List<ReportItemDto>> reportItemMap = reportItemService.selectByTestItemIds(testItemIds)
                .stream().collect(Collectors.groupingBy(ReportItemDto::getTestItemId));

        pdaApplyIdMap.forEach((k, v) -> {
            for (PdaApplySampleItemDto pdaApplySampleItemDto : v) {
                pdaApplySampleItemDto.setReportItemDtoList(reportItemMap.get(pdaApplySampleItemDto.getTestItemId()));
            }
        });

        return pdaApplyIdMap;
    }

    @Override
    public void deleteByPdaApplyIds(Collection<Long> pdaApplyIds) {
        if (CollectionUtils.isNotEmpty(pdaApplyIds)) {
            this.remove(new LambdaUpdateWrapper<TbPdaApplySampleItem>()
                    .in(TbPdaApplySampleItem::getPdaApplyId, pdaApplyIds));
        }
    }

    @Override
    public List<PdaApplySampleItemDto> selectByPdaApplyItemIds(List<Long> applySampleItemIds) {
        if (CollectionUtils.isEmpty(applySampleItemIds)) {
            return List.of();
        }
        // pda申请单下的项目
        return baseMapper.selectList(new LambdaQueryWrapper<TbPdaApplySampleItem>()
                        .in(TbPdaApplySampleItem::getPdaApplySampleItemId, applySampleItemIds))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Map<Long, PdaApplySampleItemDto> selectByPdaApplyItemIdsMap(List<Long> applySampleItemIds) {
        return this.selectByPdaApplyItemIds(applySampleItemIds).stream()
                .collect(Collectors.toMap(PdaApplySampleItemDto::getPdaApplySampleItemId, Function.identity(), (a, b) -> a));
    }

    @Override
    public Long selectApplyItemCount(Long pdaApplyId) {
        return this.count(Wrappers.lambdaQuery(TbPdaApplySampleItem.class).eq(TbPdaApplySampleItem::getPdaApplyId, pdaApplyId));
    }

    @Override
    public void deleteByApplySampleItem(Collection<Long> applySampleItemIds) {
        if (CollectionUtils.isNotEmpty(applySampleItemIds)) {
            super.removeBatchByIds(applySampleItemIds);
        }
    }

    private PdaApplySampleItemDto convert(TbPdaApplySampleItem tbPdaApplySampleItem) {
        return JSON.parseObject(JSON.toJSONString(tbPdaApplySampleItem), PdaApplySampleItemDto.class);
    }
}
