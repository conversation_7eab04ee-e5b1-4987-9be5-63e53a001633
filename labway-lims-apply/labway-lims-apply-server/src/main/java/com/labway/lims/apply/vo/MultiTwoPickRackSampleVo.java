package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 样本列表
 */
@Getter
@Setter
public class MultiTwoPickRackSampleVo {

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 检验项目
     */
    private List<String> testItemNames;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 二次分拣日期
     */
    private Date twoPickDate;

    /**
     * 样本类型
     */
    private String sampleType;

    /**
     * 管型
     */
    private String tube;

    /**
     * 名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;




}
