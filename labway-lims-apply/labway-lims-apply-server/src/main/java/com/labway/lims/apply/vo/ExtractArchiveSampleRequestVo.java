package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * 样本归档 提取样本
 * 
 * <AUTHOR>
 * @since 2023/4/17 17:27
 */
@Getter
@Setter
public class ExtractArchiveSampleRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 归档样本 占用空间ids
     */
    private Set<Long> rackLogicSpaceIds;

    /**
     * 提取原因
     */
    private String extractDesc;

    /**
     * 用户名-提取人工号
     */
    private String username;

    /**
     * 密码-提取人密码
     */
    private String password;

}
