package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.model.TbMaterialInventory;
import com.labway.lims.apply.vo.MaterialInventoryListResponseVo;
import com.labway.lims.apply.vo.MaterialInventoryVo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 物料库存 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface MaterialInventoryConverter {

    MaterialInventoryDto fromTbMaterialInventory(TbMaterialInventory obj);

    TbMaterialInventory fromMaterialInventoryDto(MaterialInventoryDto obj);

    MaterialInventoryDto fromMaterialDeliveryDetailDto(MaterialDeliveryDetailDto obj);

    List<MaterialInventoryDto> fromTbMaterialInventoryList(List<TbMaterialInventory> list);

    List<TbMaterialInventory> fromMaterialInventoryDtoList(List<MaterialInventoryDto> list);

    MaterialInventoryListResponseVo materialInventoryListResponseVoFromDto(MaterialInventoryDto obj);

    MaterialInventoryListResponseVo.MaterialInventoryItemVo materialInventoryItemVoFromDto(MaterialInventoryDto obj);

    MaterialInventoryVo materialInventoryVoFromDto(MaterialInventoryDto obj);

    List<MaterialInventoryVo> materialInventoryVoListFromDto(List<MaterialInventoryDto> list);

}
