
package com.labway.lims.apply.service.chain.material.receive.register;

import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRegisterItemDto;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 领用登记 修改 库存 物料 数量
 *
 * <AUTHOR>
 * @since 2023/5/9 10:06
 */
@Slf4j
@Component
public class MaterialReceiveRegisterReduceMaterialInventoryCommand implements Command {

    @Resource
    private MaterialInventoryService materialInventoryService;

    @Override
    public boolean execute(Context context) throws Exception {

        final MaterialReceiveRegisterContext from = MaterialReceiveRegisterContext.from(context);
        var registerItemList = from.getRegisterItemList();
        var materialInventoryDtos = from.getMaterialInventoryDtos();

        // 领用 信息 key:物料库存id value：领用信息
        final Map<Long, MaterialReceiveRegisterItemDto> registerItemByInventoryId = registerItemList.stream()
            .collect(Collectors.toMap(MaterialReceiveRegisterItemDto::getInventoryId, Function.identity()));

        for (MaterialInventoryDto inventoryDto : materialInventoryDtos) {
            MaterialReceiveRegisterItemDto itemDto = registerItemByInventoryId.get(inventoryDto.getInventoryId());

            BigDecimal mainUnitInventory = inventoryDto.getMainUnitInventory().subtract(itemDto.getReceiveMainNumber());
            BigDecimal assistUnitInventory =
                inventoryDto.getAssistUnitInventory().subtract(itemDto.getReceiveAssistNumber());

            MaterialInventoryDto update = new MaterialInventoryDto();
            update.setInventoryId(inventoryDto.getInventoryId());
            update.setMainUnitInventory(mainUnitInventory);
            update.setAssistUnitInventory(assistUnitInventory);

            materialInventoryService.updateByInventoryId(update);
        }

        return CONTINUE_PROCESSING;
    }
}
