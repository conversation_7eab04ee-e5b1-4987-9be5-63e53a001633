package com.labway.lims.apply.service.chain.applysampleitem.disable;

import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.DisableOrEnableItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <pre>
 * ApplySampleItemEnableCommand
 * 申请单样本项目取消禁用
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/12/10 18:51
 */
@Component
public class ApplySampleItemEnableCommand implements Command {

    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final ApplySampleItemDisableContext context = ApplySampleItemDisableContext.from(c);

        final Map<Long, ApplySampleDto> applySampleMap = context.getApplySampleMap();
        final DisableOrEnableItemDto disableOrEnableItemDto = context.getDisableOrEnableItemDto();
        final List<ApplySampleItemDto> enableItems = context.getEnableItems();

        // 禁用操作直接跳过
        if (Objects.equals(disableOrEnableItemDto.getIsDisable(), YesOrNoEnum.YES.getCode())) {
            return CONTINUE_PROCESSING;
        }

        // 删除掉已经取消禁用的项目
        enableItems.removeIf(e -> !Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()));

        if (CollectionUtils.isEmpty(enableItems)) {
            throw new IllegalStateException("已选项目已被取消禁用，无需操作取消禁用");
        }

        for (ApplySampleItemDto enableItem : enableItems) {
            enableItem.setIsDisabled(YesOrNoEnum.NO.getCode());
            enableItem.setUpdateDate(new Date());
            enableItem.setUpdaterId(LoginUserHandler.get().getUserId());
            enableItem.setUpdaterName(LoginUserHandler.get().getNickname());
        }
        applySampleItemService.updateBatchById(enableItems);

        for (Long applySampleId : enableItems.stream().map(ApplySampleItemDto::getApplySampleId).collect(Collectors.toSet())) {
            // 只要有项目取消禁用，该申请单样本就取消禁用
            ApplySampleDto applySampleDto = applySampleMap.get(applySampleId);
            if (Objects.equals(applySampleDto.getIsDisabled(), YesOrNoEnum.YES.getCode())) {
                applySampleService.cancelDisable(Lists.newArrayList(applySampleId));
            }
        }

        return CONTINUE_PROCESSING;
    }

}
