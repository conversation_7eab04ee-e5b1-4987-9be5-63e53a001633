package com.labway.lims.apply.controller.it8000;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSONArray;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.vo.IT8000HandleVo;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 获取已经复核的样本，也就是等待一次分拣的样本
 */
@RefreshScope
@Component
class AuditedSamplesAction implements ActionStrategy {

    @Value("${audit-sample-query-hours:1}")
    private Integer auditSampleQueryHours;
    @Resource
    private ApplySampleService applySampleService;

    @Override
    public Object action(IT8000HandleVo vo) throws Exception {
        final Date now = new Date();
        Date queryBeginTime = DateUtils.addHours(now, -auditSampleQueryHours);
        DateTime beginOfDay = DateUtil.beginOfDay(now);
        final List<ApplySampleDto> samples = applySampleService.selectByCreateDate(queryBeginTime.compareTo(beginOfDay)>0?queryBeginTime:beginOfDay, now);

        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }

        // 根据创建日期排序
        samples.sort((o1, o2) -> o2.getApplySampleId().compareTo(o1.getApplySampleId()));

        final JSONArray array = new JSONArray();
        for (ApplySampleDto sample : samples) {
            final Sample e = new Sample();
            e.setBarcode(sample.getBarcode());
            e.setApplySampleId(sample.getApplySampleId());
            e.getExtras().set("_sample", sample);
            array.add(e);
        }

        return array;
    }

    @Override
    public IT8000HandleVo.Action action() {
        return IT8000HandleVo.Action.AUDITED_SAMPLES;
    }


    @Getter
    @Setter
    private static class Sample {
        /**
         * 条码号
         */
        private String barcode;


        /**
         * 仪器样本ID
         */
        private Long applySampleId;

        /**
         * 额外参数
         */
        private Dict extras = new Dict();
    }
}
