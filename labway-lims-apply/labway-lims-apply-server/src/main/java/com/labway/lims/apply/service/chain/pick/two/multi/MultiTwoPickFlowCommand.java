
package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * 条码环节
 */
@Slf4j
@Component
public class MultiTwoPickFlowCommand implements Command {
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        final MultiTwoPickContext context = MultiTwoPickContext.from(c);

        final LinkedList<Long> ids = snowflakeService.genIds(context.getApplySampleTwoPicks().size());

        final Date now = new Date();
        final List<SampleFlowDto> flows = new ArrayList<>();

        for (int i = 0; i < context.getApplySampleTwoPicks().size(); i++) {
            final ApplySampleTwoPickDto e = context.getApplySampleTwoPicks().get(i);
            flows.add(SampleFlowDto.builder()
                    .sampleFlowId(ids.pop())
                    .applyId(e.getApplyId())
                    .applySampleId(e.getApplySampleId())
                    .barcode(e.getBarcode())
                    .operateCode(BarcodeFlowEnum.TWO_PICK.name())
                    .operateName(BarcodeFlowEnum.TWO_PICK.getDesc())
                    .createDate(DateUtils.addMilliseconds(now, i))
                    .updateDate(DateUtils.addMilliseconds(now, i))
                    .content(String.format("[批量]专业小组 [%s] 样本号 [%s]", e.getInstrumentGroupName(),
                            e.getSampleNo())).build());
        }

        sampleFlowService.addSampleFlows(flows);


        return CONTINUE_PROCESSING;
    }

}
