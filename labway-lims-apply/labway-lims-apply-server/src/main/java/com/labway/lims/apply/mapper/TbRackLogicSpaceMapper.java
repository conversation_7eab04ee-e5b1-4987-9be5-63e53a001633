package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.SampleOccupiedNumByRackLogicId;
import com.labway.lims.apply.api.dto.SampleRackPositionDto;
import com.labway.lims.apply.model.TbRackLogicSpace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 逻辑试管架占用 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */

@Mapper
public interface TbRackLogicSpaceMapper extends BaseMapper<TbRackLogicSpace> {

    List<SampleOccupiedNumByRackLogicId>
        selectOccupiedNumByRackLogicIds(@Param("rackLogicIds") Collection<Long> rackLogicIds);

    List<SampleRackPositionDto> selectSamplePositionByApplySampleIds(@Param("ids") Collection<Long> ids);

    /**
     * 根据 申请单样本ID查询 。已经删除的也会被查询出来
     */
    List<RackLogicSpaceDto> selectByAllApplySampleId(long applySampleId);
}
