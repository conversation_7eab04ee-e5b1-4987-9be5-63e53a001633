package com.labway.lims.apply.service.chain.material.delivery.income;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.service.MaterialIncomeRecordService;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 添加 物料入库信息
 */
@Slf4j
@Component
public class MaterialIncomeAddIncomeInfoCommand implements Command {
    @Resource
    private MaterialInventoryService materialInventoryService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context context) throws Exception {
        // 获取上下文存储的信息
        final MaterialIncomeContext from = MaterialIncomeContext.from(context);
        var user = from.getUser();
        Long orgId = user.getOrgId();
        List<MaterialDeliveryDetailDto> materialDeliveryDetails = from.getMaterialDeliveryDetails();

        // 如果物料信息为空，无需处理，继续下一步
        if (CollectionUtils.isEmpty(materialDeliveryDetails)) {
            return CONTINUE_PROCESSING;
        }
        // 查询已入库的信息
        Set<Long> materialIds = materialDeliveryDetails.stream().map(MaterialDeliveryDetailDto::getMaterialId).collect(Collectors.toSet());
        // 根据物料id,送检机构批量查询
        List<MaterialInventoryDto> materialInventoryDtos = materialInventoryService.selectByMaterialIds(materialIds,orgId);
   /*     // 根据 检验机构这个两个条件 查询已入库的信息
        materialInventoryDtos = materialInventoryDtos.stream()
                .filter(dto -> materialDeliveryDetails.stream()
                        .anyMatch(deliveryDto -> Objects.equals(dto.getOrgId(), deliveryDto.getOrgId()))
                )
                .collect(Collectors.toList());*/

        // 创建两个映射，分别用于MaterialId+BatchNo和MaterialId的查找
        Map<String, MaterialInventoryDto> materialInventoryDtoMap = materialInventoryDtos.stream()
                .collect(Collectors.toMap(a -> a.getMaterialId()+ "-" + a.getBatchNo(), Function.identity(),(a,b) -> b));
        Map<Long, MaterialInventoryDto> materialIdMap = materialInventoryDtos.stream()
                .collect(Collectors.toMap(MaterialInventoryDto::getMaterialId, Function.identity(), (a, b) -> a.getInventoryId() > b.getInventoryId() ? a : b));

		// TODO 物料条码号
        // 遍历物料交付细节
        materialDeliveryDetails.forEach(m -> {
            String lockKey = "materialLock:" + m.getMaterialId();
            String materialKey = m.getMaterialId() + "-" + m.getBatchNo();
            try {
                // 尝试获取锁，同时设置锁的过期时间 5S
                Boolean acquired = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "lock", 5, TimeUnit.SECONDS);
                if (Boolean.TRUE.equals(acquired)) {
                    try {
                        if (materialInventoryDtoMap.containsKey(materialKey)) {
                            // 如果存在匹配的MaterialId+BatchNo，设置物料编码
                            m.setMaterialBarcode(materialInventoryDtoMap.get(materialKey).getMaterialBarcode());
                        } else {
                            if (materialIdMap.containsKey(m.getMaterialId())) {
                                // 如果存在匹配的MaterialId，根据最大的inventoryId生成新的物料条码
                                MaterialInventoryDto materialInventoryDto = materialIdMap.get(m.getMaterialId());
                                String materialBarCode = incrementAndAppend(materialInventoryDto.getMaterialCode(), materialInventoryDto.getMaterialBarcode());
                                m.setMaterialBarcode(materialBarCode);
                                // 把最新的信息分别设置到对应的map中
                                materialInventoryDto.setMaterialBarcode(materialBarCode);
                                materialInventoryDtoMap.put(materialKey, materialInventoryDto);
                                materialIdMap.put(m.getMaterialId(), materialInventoryDto);
                            } else {
                                // 如果都没有匹配，生成新的物料条码
                                String materialBarCode = incrementAndAppend(m.getMaterialCode(), StringUtils.EMPTY);
                                m.setMaterialBarcode(materialBarCode);
                                // 同步把最新的信息分别设置到对应的map中
                                MaterialInventoryDto materialInventoryDto = new MaterialInventoryDto();
                                materialInventoryDto.setMaterialId(m.getMaterialId());
                                materialInventoryDto.setBatchNo(m.getBatchNo());
                                materialInventoryDto.setMaterialCode(m.getMaterialCode());
                                materialInventoryDto.setMaterialBarcode(materialBarCode);
                                materialInventoryDtoMap.put(materialKey, materialInventoryDto);
                                materialIdMap.put(m.getMaterialId(), materialInventoryDto);
                            }
                        }
                    } finally {
                        stringRedisTemplate.delete(lockKey); // 释放锁
                    }
                }
            } catch (Exception e) {
                log.info("物料入库失败,分布式锁key:{},错误信息堆栈信息:{}",lockKey, ExceptionUtil.stacktraceToString(e));
                throw new IllegalStateException("同机构有人正在添加此物料请稍后重试");
            }
        });

        return CONTINUE_PROCESSING;
    }

    /**
     * 生成新条码号/自增条码
     *
     * @param originalString 物料code
     * @param numberString   物料条码号
     * @return
     */
    public String incrementAndAppend(String originalString, String numberString) {
        String lastSixDigits;
        if (StringUtils.isNotBlank(numberString)) {
            lastSixDigits = numberString.substring(Math.max(numberString.length() - 6, 0)); // 取最后6位字符串
        } else {
            lastSixDigits = "000000";
        }
        int number = Integer.parseInt(lastSixDigits); // 解析为整数
        number++; // 自增
        String formattedNumber = String.format("%06d", number); // 格式化为6位数的字符串
        return originalString + formattedNumber; // 拼接到原始字符串后面
    }
}
