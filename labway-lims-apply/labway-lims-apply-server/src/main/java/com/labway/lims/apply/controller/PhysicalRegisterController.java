package com.labway.lims.apply.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.PrintStatusEnum;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.utils.FileUtils;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.PhysicalBatchDto;
import com.labway.lims.apply.api.dto.PhysicalRegisterDto;
import com.labway.lims.apply.api.dto.PhysicalSampleDto;
import com.labway.lims.apply.api.service.PhysicalBatchService;
import com.labway.lims.apply.api.service.PhysicalRegisterService;
import com.labway.lims.apply.api.service.PhysicalSampleService;
import com.labway.lims.apply.mapstruct.PhysicalRegisterConverter;
import com.labway.lims.apply.service.excel.PhysicalRegisterTemplateStrategy;
import com.labway.lims.apply.service.listener.AnalyzeExcelFileListener;
import com.labway.lims.apply.service.listener.ImportPhysicalRegisterListener;
import com.labway.lims.apply.vo.utils.ExcelFileHeadMappingVo;
import com.labway.lims.apply.vo.ImportErrorResponseVo;
import com.labway.lims.apply.vo.ImportPhysicalRegisterVo;
import com.labway.lims.apply.vo.SelectByPhysicalBatchResponseVo;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.service.DictService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 体检花名册 API
 * 
 * <AUTHOR>
 * @since 2023/3/30 15:40
 */
@Slf4j
@RestController
@RequestMapping("/physical-register")
public class PhysicalRegisterController extends BaseController {

    @DubboReference
    private PhysicalBatchService physicalBatchService;

    @DubboReference
    private PhysicalRegisterService physicalRegisterService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @DubboReference
    private PhysicalSampleService physicalSampleService;
    @DubboReference
    private DictService dictService;
    @Resource
    private PhysicalRegisterConverter physicalRegisterConverter;

    // 导入人员 错误
    private static final int REGISTER_ERROR_CODE = 10001;

    /**
     * 体检花名册 导入模板
     */
    @PostMapping("/import-template")
    public Object physicalRegisterImportTemplate() {
        // 先直接取 resources 下 模版
        ClassPathResource resource = new ClassPathResource("template/体检名册导入模板.xlsx");

        if (resource.exists()) {
            try {
                return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s",
                            URLEncoder.encode("体检名册导入模板.xlsx", StandardCharsets.UTF_8)))
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .contentType(new MediaType("application", "vnd.ms-excel"))
                    .body(resource.getInputStream().readAllBytes());
            } catch (Exception e) {
                log.error("导出资源下模版失败", e);
            }
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        // -------定义些简单excel样式----------------
        // 头的策略
        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        // 内容策略
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("微软雅黑");
        headWriteFont.setFontHeightInPoints((short)13);
        headCellStyle.setWriteFont(headWriteFont);
        HorizontalCellStyleStrategy excelStyle =
            new HorizontalCellStyleStrategy(headCellStyle, (List<WriteCellStyle>)null);

        try (ExcelWriter excelWriter = EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX)
            .registerWriteHandler(excelStyle).registerWriteHandler(new PhysicalRegisterTemplateStrategy()).build()) {

            List<List<Object>> list0 = Collections.emptyList();

            // 获取sheet对象
            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "体检名册").head(ImportPhysicalRegisterVo.class)
                .needHead(Boolean.TRUE).build();

            excelWriter.write(list0, sheet0);
        } catch (Exception e) {
            log.error("下载模板错误", e);
            throw new IllegalStateException(e.getMessage(), e);
        }
        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION,
                String.format("attachment; filename=%s", URLEncoder.encode("体检名册导入模板.xlsx", StandardCharsets.UTF_8)))
            .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
            .body(data);
    }

    /**
     * 体检花名册 导入人员
     */
    @PostMapping("/import-register")
    public Object physicalRegisterImportRegister(@RequestParam("physicalBatchId") long physicalBatchId,
        @RequestPart("file") MultipartFile file,
        @RequestPart("headSetting") List<ExcelFileHeadMappingVo> headSetting) {

        if (!FileUtils.isExcelFile(file)){
            throw new IllegalStateException("只允许上传 xls 和 xlsx 格式的文件");
        }

        if (CollectionUtils.isEmpty(headSetting)) {
            throw new IllegalArgumentException("缺少字段对照");
        }

        PhysicalBatchDto physicalBatchDto = physicalBatchService.selectByPhysicalBatchId(physicalBatchId);
        if (Objects.isNull(physicalBatchDto)) {
            throw new IllegalStateException("对应体检批次不存在");
        }

        List<PhysicalSampleDto> sampleDtoList = physicalSampleService.selectByPhysicalBatchId(physicalBatchId);
        if (CollectionUtils.isNotEmpty(sampleDtoList)) {
            throw new IllegalStateException("该体检批次下存在体检样本不可以导入人员");
        }

        // 获取就诊类型数据字典
        List<DictItemDto> dictItemDtos = dictService.selectByDictType(DictEnum.VISIT_TYPE.name());

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        ImportPhysicalRegisterListener listener = new ImportPhysicalRegisterListener(physicalBatchDto, snowflakeService,
            loginUser, dictItemDtos, headSetting);

        try (ExcelReader excelReader = EasyExcelFactory.read(file.getInputStream()).build()) {

            ReadSheet readSheet = EasyExcelFactory.readSheet(0).registerReadListener(listener).autoTrim(true).build();

            excelReader.read(readSheet);

            // 检查 失败 返回 对应行相关错误信息
            List<ImportErrorResponseVo> importErrorResponseVoList = listener.getImportErrorResponseVoList();
            if (CollectionUtils.isNotEmpty(importErrorResponseVoList)) {
                throw new LimsCodeException(REGISTER_ERROR_CODE, "数据检查失败")
                    .setData(Map.of("errorList", importErrorResponseVoList.stream()
                        .sorted(Comparator.comparing(ImportErrorResponseVo::getRowNo)).collect(Collectors.toList())));
            }

            List<PhysicalRegisterDto> targetList = listener.getTargetList();

            if (CollectionUtils.isEmpty(targetList)) {
                throw new IllegalArgumentException("导入数据为空");
            }

            // 体检批次 导入 体检人员
            physicalBatchService.importRegister(physicalBatchDto, targetList);

        } catch (IOException e) {
            log.error("导入人员出错", e);
            throw new IllegalStateException("导入人员出错:" + e.getMessage());
        }

        return Collections.emptyMap();
    }

    /**
     * 获取 体检花名册
     */
    @PostMapping("/select-by-physical-batch")
    public Object selectByPhysicalBatch(@RequestParam("physicalBatchId") long physicalBatchId) {
        PhysicalBatchDto physicalBatchDto = physicalBatchService.selectByPhysicalBatchId(physicalBatchId);
        if (Objects.isNull(physicalBatchDto)) {
            throw new IllegalArgumentException("对应体检批次不存在");
        }

        List<PhysicalRegisterDto> registerDtoList = physicalRegisterService.selectByPhysicalBatchId(physicalBatchId);

        // 打印状态 为已打印的 体检人 id
        final List<Long> isPrintPhysicalRegisterId =
            registerDtoList.stream().filter(obj -> Objects.equals(obj.getIsPrint(), PrintStatusEnum.PRINTED.getCode()))
                .map(PhysicalRegisterDto::getPhysicalRegisterId).collect(Collectors.toList());

        // 已打印条码的 所有体检样本
        final List<PhysicalSampleDto> sampleDtoList =
            physicalSampleService.selectByPhysicalRegisterIds(isPrintPhysicalRegisterId);

        // key: 体检人 id value:体检人对应条码
        final Map<Long, List<String>> barcodeGroupingByPhysicalRegisterId =
            sampleDtoList.stream().collect(Collectors.groupingBy(PhysicalSampleDto::getPhysicalRegisterId,
                Collectors.mapping(PhysicalSampleDto::getBarcode, Collectors.toList())));

        // 此体检人 做的样本的 采样时间
        Map<Long, Date> samplingDateByRegisterId = sampleDtoList.stream().collect(Collectors
            .toMap(PhysicalSampleDto::getPhysicalRegisterId, PhysicalSampleDto::getSamplingDate, (key1, key2) -> key1));

        List<SelectByPhysicalBatchResponseVo> targetList = Lists.newArrayListWithCapacity(registerDtoList.size());
        registerDtoList.forEach(item -> {
            SelectByPhysicalBatchResponseVo temp = physicalRegisterConverter.fromPhysicalRegisterDto(item);

            List<String> barcodeList =
                barcodeGroupingByPhysicalRegisterId.getOrDefault(item.getPhysicalRegisterId(), Collections.emptyList());
            temp.setBarcodeList(barcodeList);

            temp.setSamplingDate(samplingDateByRegisterId.get(item.getPhysicalRegisterId()));
            targetList.add(temp);
        });

        return targetList;
    }

    /**
     * 解析 体检导入文件
     */
    @PostMapping("/analyze-excel-file")
    public Object analyzeExcelFile(@RequestParam("file") MultipartFile file) {

        if (!FileUtils.isExcelFile(file)){
            throw new IllegalStateException("只允许上传 xls 和 xlsx 格式的文件");
        }

        AnalyzeExcelFileListener listener = new AnalyzeExcelFileListener(ImportPhysicalRegisterVo.getHeadList());
        try (ExcelReader excelReader = EasyExcelFactory.read(file.getInputStream()).build()) {

            ReadSheet readSheet = EasyExcelFactory.readSheet(0).registerReadListener(listener).autoTrim(true).build();

            excelReader.read(readSheet);

            return listener.getTargetList();

        } catch (IOException e) {
            log.error("解析导入人员出错", e);
            throw new IllegalStateException("解析导入人员出错:" + e.getMessage());
        }

    }

    /**
     * 获取 批次下 套餐说明
     */
    @PostMapping("/select-test-package-desc")
    public Object selectTestPackageDesc(@RequestParam("physicalBatchId") long physicalBatchId) {
        PhysicalBatchDto physicalBatchDto = physicalBatchService.selectByPhysicalBatchId(physicalBatchId);
        if (Objects.isNull(physicalBatchDto)) {
            throw new IllegalArgumentException("对应体检批次不存在");
        }

        return physicalRegisterService.selectByPhysicalBatchId(physicalBatchId).stream()
            .map(PhysicalRegisterDto::getTestPackageDesc).filter(StringUtils::isNotBlank).distinct()
            .collect(Collectors.toList());
    }



}
