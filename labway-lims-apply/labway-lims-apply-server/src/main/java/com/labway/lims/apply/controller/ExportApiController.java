package com.labway.lims.apply.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.BusinessCenterDeliveryDto;
import com.labway.lims.apply.api.dto.BusinessCenterMaterialBarcodeSearchDto;
import com.labway.lims.apply.api.dto.BusinessCenterRejectApplyDto;
import com.labway.lims.apply.api.service.GroupMaterialApplyService;
import com.labway.lims.apply.api.service.MaterialDeliveryRecordService;
import com.labway.lims.apply.api.service.MaterialIncomeRecordService;
import com.labway.lims.apply.api.vo.BizMaterialBarcodeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 服务 暴露 接口 http 不需要用户
 * 
 * <AUTHOR>
 * @since 2023/9/27 16:24
 */
@Slf4j
@RestController
@RequestMapping("/export-api")
public class ExportApiController extends BaseController {
    @Resource
    private GroupMaterialApplyService groupMaterialApplyService;

    @Resource
    private MaterialDeliveryRecordService materialDeliveryRecordService;

	@Resource
	private MaterialIncomeRecordService materialIncomeRecordService;

    /**
     * 处理申领单--业务中台出库 http 给业务中台调用 无登录用户
     */
    @PostMapping("/business-center-delivery")
    public Object businessCenterDelivery(@RequestBody BusinessCenterDeliveryDto dto) {
        LoginUserHandler.User user = new LoginUserHandler.User();
        user.setUserId(0L);
        user.setNickname("业务中台");
        LoginUserHandler.set(user);
        materialDeliveryRecordService.businessCenterDelivery(dto);
        return Collections.emptyMap();
    }

    /**
     * 业务中台退回申领单 http 给业务中台调用 无登录用户
     */
    @PostMapping("/business-center-reject-apply")
    public Object businessCenterRejectApply(@RequestBody BusinessCenterRejectApplyDto dto) {
        LoginUserHandler.User user = new LoginUserHandler.User();
        user.setUserId(0L);
        user.setNickname("业务中台");
        LoginUserHandler.set(user);
        groupMaterialApplyService.businessCenterRejectApply(dto);
        return Collections.emptyMap();
    }

	/**
	 * 中台批量查询物料 - 批次 - 条码号
	 * @param materialInfos 物料信息 - 物料条码 - 批次号
	 * @return {@link BizMaterialBarcodeVo}
	 */
	@PostMapping("/business-center-search-material-barcode")
	public List<BizMaterialBarcodeVo> bizSearchMaterialBarcode(@Valid @RequestBody BusinessCenterMaterialBarcodeSearchDto materialInfos) {
		return materialIncomeRecordService.selectMaterialBarcode(materialInfos);
	}
}
