package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.MaterialInventoryCheckStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 盘点记录 信息
 * 
 * <AUTHOR>
 * @since 2023/5/12 10:56
 */
@Getter
@Setter
public class MaterialInventoryCheckListItemResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 盘点ID
     */
    private Long checkId;

    /**
     * 盘点单号
     */
    private String checkNo;

    /**
     * 盘点时间
     */
    private Date checkTime;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 状态
     *
     * @see MaterialInventoryCheckStatusEnum
     */
    private Integer status;

    private String statusDesc;


    /**
     * 盘点人
     */
    private String checkName;
}
