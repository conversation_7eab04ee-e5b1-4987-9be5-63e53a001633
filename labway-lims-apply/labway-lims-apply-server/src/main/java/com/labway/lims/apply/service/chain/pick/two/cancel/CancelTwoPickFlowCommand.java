package com.labway.lims.apply.service.chain.pick.two.cancel;

import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 条码环节
 */
@Slf4j
@Component
public class CancelTwoPickFlowCommand implements Command {
    @Resource
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {
        final CancelTwoPickContext context = CancelTwoPickContext.from(c);


        // 添加流水
        sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                .applyId(context.getApplySamples().iterator().next().getApplyId())
                .applySampleId(context.getApplySampleId())
                .barcode(context.getBarcode())
                .operateCode(BarcodeFlowEnum.CANCEL_TWO_PICK.name())
                .operateName(BarcodeFlowEnum.CANCEL_TWO_PICK.getDesc())
                .content("取消二次分拣").build());


        return CONTINUE_PROCESSING;
    }

}
