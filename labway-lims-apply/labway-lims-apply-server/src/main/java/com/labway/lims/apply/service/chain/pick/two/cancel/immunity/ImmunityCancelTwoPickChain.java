package com.labway.lims.apply.service.chain.pick.two.cancel.immunity;

import com.labway.lims.apply.service.chain.pick.two.cancel.CancelTwoPickCommand;
import com.labway.lims.apply.service.chain.pick.two.cancel.CancelTwoPickDetachedRackCommand;
import com.labway.lims.apply.service.chain.pick.two.cancel.CancelTwoPickFlowCommand;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <pre>
 * ImmunityCancelTwoPickChain
 * 免疫二次分拣 - 取消分拣
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/11/4 10:05
 */
@Component
public class ImmunityCancelTwoPickChain extends ChainBase implements InitializingBean {

    @Resource
    private ImmunityCancelTwoPickLimitCommand immunityCancelTwoPickLimitCommand;
    @Resource
    private ImmunityCancelTwoPickCheckParamCommand immunityCancelTwoPickCheckParamCommand;
    @Resource
    private CancelTwoPickCommand cancelTwoPickCommand;
    // @Resource
    // private ImmunityCancelTwoPickCopyApplySampleItemCommand immunityCancelTwoPickCopyApplySampleItemCommand;
    // @Resource
    // private ImmunityCancelTwoPickRemoveApplySampleCommand immunityCancelTwoPickRemoveApplySampleCommand;
    @Resource
    private ImmunityCancelTwoPickUnpickApplySampleUpdateCommand immunityCancelTwoPickUnpickApplySampleUpdateCommand;
    @Resource
    private ImmunityCancelTwoPickUnmarkCommand immunityCancelTwoPickUnmarkCommand;
    @Resource
    private ImmunityCancelTwoPickUpdateApplySampleCommand immunityCancelTwoPickUpdateApplySampleCommand;
    @Resource
    private CancelTwoPickDetachedRackCommand cancelTwoPickDetachedRackCommand;
    @Resource
    private CancelTwoPickFlowCommand cancelTwoPickFlowCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 限流
        addCommand(immunityCancelTwoPickLimitCommand);

        // 参数校验
        addCommand(immunityCancelTwoPickCheckParamCommand);

        // 取消二次分拣
        addCommand(cancelTwoPickCommand);

        // 复制项目信息
        // addCommand(immunityCancelTwoPickCopyApplySampleItemCommand);

        // 删除多余的申请单样本
        // addCommand(immunityCancelTwoPickRemoveApplySampleCommand);

        // 取消二次分拣 合并到未二次分拣的申请单样本上
        addCommand(immunityCancelTwoPickUnpickApplySampleUpdateCommand);

        // 判断是否需要取消 免疫二次分拣标记
        addCommand(immunityCancelTwoPickUnmarkCommand);

        // 更新申请单样本
        addCommand(immunityCancelTwoPickUpdateApplySampleCommand);

        // 游离逻辑试管架
        addCommand(cancelTwoPickDetachedRackCommand);

        // 条码环节
        addCommand(cancelTwoPickFlowCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);

    }
}
