package com.labway.lims.apply.service.chain.pick.two.cancel;

import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * 加急样本项目合并
 */
@Slf4j
@Component
public class CancelTwoPickCopyApplySampleItemCommand implements Command, InitializingBean {
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {

        final CancelTwoPickContext context = CancelTwoPickContext.from(c);


        final List<ApplySampleItemDto> applySampleItems = new ArrayList<>(context.getApplySampleItems());
        applySampleItems.removeIf(e -> Objects.equals(e.getApplySampleId(), context.getApplySampleId()));

        if (CollectionUtils.isEmpty(applySampleItems)) {
            return CONTINUE_PROCESSING;
        }

        // 如果包含一个血培养的，那么跳过复制
        if (applySampleItems.stream().anyMatch(e -> Objects.equals(e.getItemType(),
                ItemTypeEnum.BLOOD_CULTURE.name()))) {
            return CONTINUE_PROCESSING;
        }

        final LinkedList<Long> ids = snowflakeService.genIds(applySampleItems.size());
        applySampleItems.forEach(e -> {
            e.setApplySampleId(context.getApplySampleId());
            e.setApplySampleItemId(ids.pop());
        });

        // 添加检验项目
        applySampleItemService.addApplySampleItems(applySampleItems);

        return CONTINUE_PROCESSING;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
