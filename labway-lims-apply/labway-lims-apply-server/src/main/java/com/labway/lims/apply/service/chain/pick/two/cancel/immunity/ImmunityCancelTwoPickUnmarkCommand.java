package com.labway.lims.apply.service.chain.pick.two.cancel.immunity;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.service.chain.pick.two.cancel.CancelTwoPickContext;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <pre>
 * ImmunityCancelTwoPickUnmarkCommand
 * 判断是否需要取消 免疫二次分拣标记
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/11/4 14:18
 */
@Component
public class ImmunityCancelTwoPickUnmarkCommand implements Command {
    @Resource
    private ApplySampleItemService applySampleItemService;


    @Override
    public boolean execute(Context c) throws Exception {
        final CancelTwoPickContext context = CancelTwoPickContext.from(c);

        // 当前专业组所有的项目
        final List<ApplySampleItemDto> applySampleItems = new ArrayList<>(context.getApplySampleItems());

        final long applySampleId;
        if (Objects.nonNull(context.getUnpickApplySample())) {
            applySampleId = context.getUnpickApplySample().getApplySampleId();
        } else {
            applySampleId = context.getApplySamples().iterator().next().getApplySampleId();
        }

        // 当前样本 经过取消二次分拣合并后的项目
        List<ApplySampleItemDto> mergedApplySampleItems = applySampleItemService.selectByApplySampleId(applySampleId);

        // 如果所有的样本项目都已经取消二次分拣处理，那么就取消免疫二次分拣标记
        applySampleItems.removeIf(e -> mergedApplySampleItems.stream().anyMatch(m -> m.getTestItemId().equals(e.getTestItemId())));

        // 取消免疫二次分拣标记
        if (CollectionUtils.isEmpty(applySampleItems)) {
            context.setIsImmunityTwoPick(YesOrNoEnum.NO.getCode());
        }

        return CONTINUE_PROCESSING;
    }

}
