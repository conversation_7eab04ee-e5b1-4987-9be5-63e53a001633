package com.labway.lims.apply.vo.utils;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.exception.LimsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 体检 批次号 生成
 *
 * <AUTHOR>
 * @since 2023/3/30 10:45
 */
@Slf4j
@Component
public class PhysicalBatchNumberUtil {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisPrefix redisPrefix;

    private static final String REDIS_PREFIX = "physicalBatchNumber:";
    private static final String DATE_FORMAT = "yyyyMMdd";

    /**
     * 日期+3位数字，后三位从001开始自增，若出现数字不够的情况，则自动扩增一位
     * 
     * <pre>
     *     例如 : 2023-03-30  -->   2023033001
     *     例如 : 2023-03-30  -->   20230331001
     * </pre>
     */
    public String getPhysicalBatchNumber() {
        final String date = LocalDate.now().format(DateTimeFormatter.ofPattern(DATE_FORMAT));
        final String redisKey = redisPrefix.getBasePrefix() + REDIS_PREFIX + date;

        Long counter = stringRedisTemplate.opsForValue().increment(redisKey);

        if (Objects.isNull(counter)) {
            throw new LimsException("生成批次号失败");
        }
        if (Objects.equals(counter, 1L)) {
            // 设置 key 生效时间 为 1天
            stringRedisTemplate.expire(redisKey, 1, TimeUnit.DAYS);
        }

        if (counter <= 999) {
            String counterStr = String.format("%03d", counter);
            return date + counterStr;
        }
        // 超过 999 自动扩增一位
        if (counter > 9999) {
            throw new LimsException("超过每天最大生成批号");
        }
        String counterStr = String.format("%04d", counter);
        return date + counterStr;

    }

}
