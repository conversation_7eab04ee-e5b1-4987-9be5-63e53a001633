package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 已经分血列表
 */
@Getter
@Setter
public class SplitSampleRequestVo {
    /**
     * 开始分血时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginSplitDate;

    /**
     * 结束分血时间
     */

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSplitDate;

}
