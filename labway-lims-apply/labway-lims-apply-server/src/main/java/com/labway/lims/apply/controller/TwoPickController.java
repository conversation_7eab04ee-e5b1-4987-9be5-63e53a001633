package com.labway.lims.apply.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.ImmunityPickTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySampleItemSourceEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplySampleItemTwoPickDetailDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.BloodCultureTwoPickDto;
import com.labway.lims.apply.api.dto.ImmunityTwoPickDto;
import com.labway.lims.apply.api.dto.InfectionTwoPickDto;
import com.labway.lims.apply.api.dto.MicrobiologyTwoPickDto;
import com.labway.lims.apply.api.dto.RackLogicApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.api.dto.TwoPickedApplySampleDto;
import com.labway.lims.apply.api.dto.WaitingTwoPickApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.service.chain.pick.two.TwoPickBloodCultureCommand;
import com.labway.lims.apply.vo.ApplySampleItemTwoPickDetailVo;
import com.labway.lims.apply.vo.ApplySampleTwoPickVo;
import com.labway.lims.apply.vo.MultiTwoPickRackSampleVo;
import com.labway.lims.apply.vo.TwoPickPickVo;
import com.labway.lims.apply.vo.TwoPickRackSampleVo;
import com.labway.lims.apply.vo.TwoPickRackVo;
import com.labway.lims.apply.vo.TwoPickRacksRequestVo;
import com.labway.lims.apply.vo.TwoPickVo;
import com.labway.lims.apply.vo.TwoPickedApplySampleVo;
import com.labway.lims.apply.vo.TwoPickedSampleVo;
import com.labway.lims.apply.vo.UnpickImmunityVo;
import com.labway.lims.apply.vo.WaitingTwoPickApplySampleVo;
import com.labway.lims.apply.vo.WaitingTwoPickSampleVo;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentGroupTestItemDto;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentGroupTestItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.labway.lims.api.SampleNoUtils.addOneToLastNumber;

/**
 * 二次分拣
 */
@Slf4j
@RestController
@RequestMapping("/two-pick")
public class TwoPickController extends BaseController {

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private InstrumentGroupTestItemService instrumentGroupTestItemService;
    @Resource
    private RackLogicService rackLogicService;

    /**
     * 二次分拣
     */
    @PostMapping("/pick")
    public Object pick(@RequestBody TwoPickPickVo vo) {

        final List<ApplySampleDto> applySamples = new ArrayList<>(applySampleService.selectByBarcode(vo.getBarcode()));

        applySamples.removeIf(e -> Objects.equals(e.getStatus(), SampleStatusEnum.STOP_TEST.getCode()));

        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException("条码不存在");
        }

        if (StringUtils.isNotBlank(vo.getUrgentSampleNo()) && StringUtils.isBlank(vo.getSampleNo())) {
            throw new IllegalArgumentException("参数错误，缺少样本号");
        }

        if (CollectionUtils.isNotEmpty(vo.getLimbSampleNos())
                && vo.getLimbSampleNos().stream()
                .anyMatch(obj -> StringUtils.isAnyBlank(obj.getLimbCode(), obj.getSampleNo())
                        || Objects.isNull(TwoPickBloodCultureCommand.Limb.getByName(obj.getLimbCode())))
                && StringUtils.isBlank(vo.getSampleNo())) {
            throw new IllegalArgumentException("血培养相关样本号信息错误");
        }

        // 删除非当前专业组的
        applySamples.removeIf(e -> !Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()));

        for (ApplySampleDto applySample : applySamples) {
            applySampleService.assertApplySampleUsability(applySample.getApplySampleId());
        }

        if (applySamples.stream().noneMatch(e -> Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()))) {
            throw new IllegalArgumentException("该条码不属于本专业组");
        }

        // 过滤掉已经免疫二次分拣的样本
        applySamples.removeIf(e -> Objects.equals(YesOrNoEnum.YES.getCode(), e.getIsImmunityTwoPick()) &&
                Objects.equals(YesOrNoEnum.YES.getCode(), e.getIsTwoPick()));

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getIsTwoPick(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("该条码已完成二次分拣");
        }

        // 在最终分拣的时候，只能有一个
        if (applySamples.size() != 1) {
            throw new IllegalArgumentException("条码数量错误或已经分拣");
        }

        // 免疫二次分拣时，分拣指定日期不能在样本创建时间之前
        if (ImmunityPickTypeEnum.isImmunityPick(vo.getImmunityPickType()) && Objects.nonNull(vo.getTwoPickDate())) {
            if (applySamples.stream().anyMatch(e -> vo.getTwoPickDate().before(e.getCreateDate()))) {
                throw new IllegalStateException("分拣指定日期不能在样本创建时间之前");
            }
        }

        final ApplySampleDto applySample = applySamples.iterator().next();
        if (Objects.equals(applySample.getIsOutsourcing(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalStateException("外送样本请到外送分拣页面分拣");
        }

        if (Objects.equals(applySample.getItemType(), ItemTypeEnum.MICROBIOLOGY.name())
                && !Objects.equals(vo.getItemType(), ItemTypeEnum.MICROBIOLOGY.name())) {
            // 微生物
            throw new IllegalStateException("微生物样本请到微生物二次分拣页面分拣");
        }

        if (Objects.equals(applySample.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())
                && !Objects.equals(vo.getItemType(), ItemTypeEnum.MICROBIOLOGY.name())) {
            // 血培养
            throw new IllegalStateException("血培养样本请到微生物二次分拣页面分拣");
        }

        if (Objects.equals(applySample.getItemType(), ItemTypeEnum.INFECTION.name())
                && !Objects.equals(vo.getItemType(), ItemTypeEnum.INFECTION.name())) {
            // 院感
            throw new IllegalStateException("院感样本请到院感二次分拣页面分拣");
        }

        // 非院感 与 微生物 应当 去单个分拣页面
        if (!(Objects.equals(applySample.getItemType(), ItemTypeEnum.INFECTION.name())
                || Objects.equals(applySample.getItemType(), ItemTypeEnum.MICROBIOLOGY.name())
                || Objects.equals(applySample.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name()))
                && (Objects.equals(vo.getItemType(), ItemTypeEnum.MICROBIOLOGY.name())
                || Objects.equals(vo.getItemType(), ItemTypeEnum.INFECTION.name())
                || Objects.equals(vo.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name()))) {
            // 院感
            throw new IllegalStateException("此条码请到单个分拣页面分拣");
        }

        final List<RackLogicDto> rackLogics = rackLogicService.selectByApplySampleId(applySample.getApplySampleId());
        if (CollectionUtils.isNotEmpty(rackLogics)) {
            if (!Objects.equals(rackLogics.iterator().next().getPosition(),
                    RackLogicPositionEnum.TWO_PICKING.getCode())) {
                throw new IllegalArgumentException("当前条码不可二次分拣，请检查条码是否已经一次分拣或交接");
            }
        }


        List<ApplySampleTwoPickDto> applySampleTwoPicks;
        if (Objects.equals(applySample.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())) {
            // 血培养
            final BloodCultureTwoPickDto tp = new BloodCultureTwoPickDto();
            tp.setApplySampleId(applySample.getApplySampleId());
            tp.setSampleNo(vo.getSampleNo());
            tp.setUrgentSampleNo(vo.getUrgentSampleNo());
            tp.setTwoPickDate(vo.getTwoPickDate());
            tp.setLimbSampleNos(vo.getLimbSampleNos());
            tp.setIsValidProperty(vo.getIsValidProperty());
            tp.setItemPropertyRelationList(vo.getItemPropertyRelationList());
            applySampleTwoPicks = applySampleService.twoPick(tp);
        } else if (Objects.equals(vo.getItemType(), ItemTypeEnum.MICROBIOLOGY.name())) {
            // 微生物
            final MicrobiologyTwoPickDto tp = new MicrobiologyTwoPickDto();
            tp.setApplySampleId(applySample.getApplySampleId());
            tp.setSampleNo(vo.getSampleNo());
            tp.setUrgentSampleNo(vo.getUrgentSampleNo());
            tp.setTwoPickDate(vo.getTwoPickDate());
            tp.setIsValidProperty(vo.getIsValidProperty());
            tp.setItemPropertyRelationList(vo.getItemPropertyRelationList());
            applySampleTwoPicks = applySampleService.twoPick(tp);
        } else if (Objects.equals(vo.getItemType(), ItemTypeEnum.INFECTION.name())) {
            // 院感
            final InfectionTwoPickDto tp = new InfectionTwoPickDto();
            tp.setApplySampleId(applySample.getApplySampleId());
            tp.setSampleNo(vo.getSampleNo());
            tp.setUrgentSampleNo(vo.getUrgentSampleNo());
            tp.setTwoPickDate(vo.getTwoPickDate());
            applySampleTwoPicks = applySampleService.twoPick(tp);
        } else if (ImmunityPickTypeEnum.isImmunityPick(vo.getImmunityPickType())) {
            // 免疫二次分拣
            final ImmunityTwoPickDto tp = new ImmunityTwoPickDto();
            tp.setApplySampleId(applySample.getApplySampleId());
            tp.setSampleNo(vo.getSampleNo());
            tp.setUrgentSampleNo(vo.getUrgentSampleNo());
            tp.setInstrumentId(vo.getInstrumentId());
            tp.setType(vo.getImmunityPickType());
            tp.setTwoPickDate(vo.getTwoPickDate());
            tp.setPresetDates(vo.getPresetDates());
            tp.setTestItemCodes(vo.getTestItemCodes());
            applySampleTwoPicks = applySampleService.immunityTwoPick(tp);
        } else {
            // 普通
            final TwoPickDto tp = new TwoPickDto();
            tp.setApplySampleId(applySample.getApplySampleId());
            tp.setSampleNo(vo.getSampleNo());
            tp.setUrgentSampleNo(vo.getUrgentSampleNo());
            tp.setInstrumentId(vo.getInstrumentId());
            applySampleTwoPicks = applySampleService.twoPick(tp);
        }

        // 检验项目
        final List<ApplySampleItemDto> applySampleItems =
                applySampleItemService.selectByApplyIds(List.of(applySample.getApplyId()));

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        return Map.of(
                // 样本信息
                "samples", applySampleTwoPicks.stream().map(e -> {
                    final TwoPickVo v = new TwoPickVo();
                    v.setApplySampleId(e.getApplySampleId());
                    v.setSampleNo(e.getSampleNo());
                    v.setInstrumentGroupId(e.getInstrumentGroupId());
                    v.setInstrumentGroupName(e.getInstrumentGroupName());
                    v.setSecondSortColor(e.getSecondSortColor());
                    v.setApplyId(applySample.getApplyId());
                    v.setBarcode(applySample.getBarcode());
                    v.setTestItemNames(
                            applySampleItems.stream().filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
                    v.setTestItemIds(
                            applySampleItems.stream().filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                                    .map(ApplySampleItemDto::getTestItemId).collect(Collectors.toList()));
                    v.setTwoPickDate(ObjectUtils.defaultIfNull(vo.getTwoPickDate(), new Date()));
                    v.setSampleTypeName(applySample.getSampleTypeName());
                    v.setTubeName(applySample.getTubeName());
                    v.setHspOrgName(apply.getHspOrgName());
                    v.setPatientName(apply.getPatientName());
                    v.setPatientAge(apply.getPatientAge());
                    v.setPatientSubage(apply.getPatientSubage());
                    v.setPatientSubageUnit(apply.getPatientSubageUnit());
                    v.setPatientSex(apply.getPatientSex());
                    // 免疫二次分拣日期
                    v.setImmunityPickDate(DateUtil.format(e.getImmunityTwoPickDate(), DatePattern.NORM_DATE_PATTERN));
                    return v;
                }).collect(Collectors.toList()),
                // 是否组间交接了
                "isTransform", applySampleTwoPicks.stream().anyMatch(e -> BooleanUtils.isTrue(e.getIsTransform())),
                // 下一个样本编号
                "nextSampleNo", addOneToLastNumber(applySampleTwoPicks
                        .get(applySampleTwoPicks.size() - 1).getSampleNo()));

    }

    /**
     * 二次分拣
     */
    @PostMapping("/immunity-pick")
    public Object immunityPick(@RequestBody TwoPickPickVo vo) {
        if (StringUtils.isBlank(vo.getImmunityPickType())) {
            throw new IllegalArgumentException("免疫二次分拣类型不能为空");
        }
        return pick(vo);
    }

    /**
     * 批量二次分拣
     */
    @PostMapping("/multi-pick")
    public Object multiPick(@RequestParam Long rackLogicId,@RequestParam(required = false) Long instrumentId) {

        if (Objects.isNull(rackLogicId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<ApplySampleTwoPickDto> applySampleTwoPicks = applySampleService.multiTwoPick(rackLogicId,instrumentId);
        final Map<String, ApplySampleTwoPickDto> map = new LinkedHashMap<>();

        for (ApplySampleTwoPickDto e : applySampleTwoPicks) {
            final ApplySampleTwoPickDto dto = map.computeIfAbsent(e.getBarcode(), v -> e);
            if (dto != e) {
                dto.setSampleNo(dto.getSampleNo() + '/' + e.getSampleNo());
            }
        }

        final List<ApplySampleTwoPickVo> list = new ArrayList<>();
        for (ApplySampleTwoPickDto e : applySampleTwoPicks) {
            e.setSampleNo(map.get(e.getBarcode()).getSampleNo());

            final ApplySampleTwoPickVo v = new ApplySampleTwoPickVo();
            BeanUtils.copyProperties(e, v);

            v.setTwoPickDate(new Date());

            list.add(v);
        }

        return list;

    }

    /**
     * 取消二次分拣
     */
    @PostMapping("/unpick")
    public Object unpick(@RequestBody Set<String> barcodes) {
        if (CollectionUtils.isEmpty(barcodes)) {
            return Collections.emptyMap();
        }

        for (String barcode : barcodes) {
            applySampleService.cancelTwoPick(barcode);
        }

        return Collections.emptyMap();

    }

    /**
     * 取消二次分拣(免疫)
     */
    @PostMapping("/unpick-immunity")
    public Object unpickImmunity(@RequestBody UnpickImmunityVo vo) {
        if (CollectionUtils.isEmpty(vo.getItems())) {
            return Collections.emptyMap();
        }

        for (UnpickImmunityVo.Item item : vo.getItems()) {
            applySampleService.cancelTwoPick(item.getBarcode(), item.getApplySampleId());
        }

        return Collections.emptyMap();
    }

    /**
     * 等待二次分拣的样本列表
     */
    @PostMapping("/waiting-pick-samples")
    public Object samples(@RequestBody WaitingTwoPickSampleVo vo) {
        if (Objects.isNull(vo.getBeginReceiveDate()) || Objects.isNull(vo.getEndReceiveDate())) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<WaitingTwoPickApplySampleDto> samples = new LinkedList<>();
        if (StringUtils.isBlank(vo.getItemType())) {
            samples.addAll(applySampleService
                    .selectWaitingTwoPickSamples(vo.getBeginReceiveDate(), vo.getEndReceiveDate(), vo.getItemType()));
        } else {
            samples.addAll(applySampleService.selectWaitingTwoPickSamples(vo.getBeginReceiveDate(), vo.getEndReceiveDate())
                    .stream().filter(e -> {
                        // 如果是微生物时，返回微生物和血培养的
                        if (Objects.equals(vo.getItemType(), ItemTypeEnum.MICROBIOLOGY.name())) {
                            return StringUtils.equalsAny(e.getItemType(), ItemTypeEnum.MICROBIOLOGY.name(),
                                    ItemTypeEnum.BLOOD_CULTURE.name());
                        }
                        return Objects.equals(e.getItemType(), vo.getItemType());
                    })
                    .collect(Collectors.toList()));
        }

        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        final Map<Long, List<ApplySampleItemDto>> items = applySampleItemService
                .selectByApplyIds(
                        samples.stream().map(WaitingTwoPickApplySampleDto::getApplyId).collect(Collectors.toSet()))
                // 已禁用的项目不显示
                .stream().filter(e -> !Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 根据创建时间排序
        samples.sort(Comparator.comparing(ApplySampleDto::getCreateDate));

        return samples.stream().map(e -> {
            final WaitingTwoPickApplySampleVo v = new WaitingTwoPickApplySampleVo();
            BeanUtils.copyProperties(e, v);
            v.setTestItemNames(items.getOrDefault(e.getApplySampleId(), Collections.emptyList()).stream()
                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            return v;
        }).collect(Collectors.toList());
    }

    /**
     * 已经二次分拣的样本列表
     */
    @PostMapping("/picked-samples")
    public Object pickedSamples(@RequestBody TwoPickedSampleVo vo) {
        if (Objects.isNull(vo.getBeginTwoPickDate()) || Objects.isNull(vo.getEndTwoPickDate())) {
            throw new IllegalArgumentException("参数错误");
        }

        List<TwoPickedApplySampleDto> samples = new LinkedList<>();

        if (Objects.equals(vo.getItemType(), ItemTypeEnum.MICROBIOLOGY.name())) {
            samples.addAll(applySampleService.selectTwoPickedSamples(vo.getBeginTwoPickDate(),
                    vo.getEndTwoPickDate()).stream().filter(e -> StringUtils.containsAny(e.getItemType(), ItemTypeEnum.MICROBIOLOGY.name(),
                    ItemTypeEnum.BLOOD_CULTURE.name())).collect(Collectors.toList()));
        } else {
            samples.addAll(applySampleService.selectTwoPickedSamples(vo.getBeginTwoPickDate(),
                    vo.getEndTwoPickDate(), vo.getItemType()));
        }

        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        // 过滤送检机构样本
        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())){
            samples = samples.stream().filter(e->vo.getHspOrgIds().contains(String.valueOf(e.getHspOrgId()))).collect(Collectors.toList());
        }

        // 正序
        samples.sort(Comparator.comparing(TwoPickedApplySampleDto::getTwoPickDate));

        final Map<Long,
                List<ApplySampleItemDto>> applySampleItems = applySampleItemService
                .selectByApplyIds(samples.stream().map(TwoPickedApplySampleDto::getApplyId).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 快速获取样本号
        final Map<Long, ApplySampleTwoPickDto> stps = applySampleService.quicklyGetTwoPickInfoByApplySampleIds(
                samples.stream().map(TwoPickedApplySampleDto::getApplySampleId).collect(Collectors.toList()));

        return samples.stream().map(e -> {
            final TwoPickedApplySampleVo v = new TwoPickedApplySampleVo();
            BeanUtils.copyProperties(e, v);
            v.setTestItemNames(Collections.emptyList());

            final ApplySampleTwoPickDto stp = stps.get(e.getApplySampleId());
            if (Objects.nonNull(stp)) {
                v.setSampleNo(stp.getSampleNo());
                v.setInstrumentGroupId(stp.getInstrumentGroupId());
                v.setInstrumentGroupName(stp.getInstrumentGroupName());
            }

            List<ApplySampleItemDto> items =
                    new LinkedList<>(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList()));

            if (Objects.nonNull(vo.getTestItemId())
                    && (items.stream().noneMatch(l -> Objects.equals(l.getTestItemId(), vo.getTestItemId())))) {
                return null;
            }

            v.setTestItemNames(items.stream()
                    .filter(item -> Objects.equals(item.getItemSource(), ApplySampleItemSourceEnum.DEFAULT_SOURCE.getCode())) // 过滤掉加项的
                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));

            return v;
        }).filter(Objects::nonNull).collect(Collectors.toList());

    }


    /**
     * 已分拣列表导出
     */
    @PostMapping("/export-samples")
    public Object exportResults(@RequestBody TwoPickedSampleVo vo) throws Exception {
        @SuppressWarnings("unchecked") final List<TwoPickedApplySampleVo> vos =
                (List<TwoPickedApplySampleVo>) pickedSamples(vo);
        if (CollectionUtils.isEmpty(vos)) {
            throw new IllegalArgumentException("没有要导出的数据");
        }

        // 过滤前端传来的样本号和姓名
        Stream<TwoPickedApplySampleVo> stream = vos.stream();
        if (StringUtils.isNotBlank(vo.getSampleNo())) {
            stream = stream.filter(s -> vo.getSampleNo().equals(s.getSampleNo()));
        }

        if (StringUtils.isNotBlank(vo.getPatientName())) {
            stream = stream.filter(s -> vo.getPatientName().equals(s.getPatientName()));
        }

        final List<TwoPickedApplySampleVo> filterList = stream.collect(Collectors.toList());
        final File tempFile = File.createTempFile("send-item-and-apply-type-export-", null);
        String beginDate = DateFormatUtils.format(vo.getBeginTwoPickDate(), "yyyy-MM-dd");
        String endDate = DateFormatUtils.format(vo.getEndTwoPickDate(), "yyyy-MM-dd");
        String firstLine = LoginUserHandler.get().getGroupName() + "——" + "二次分拣清单（" + beginDate + "—" + endDate + ")";

        try (final ExcelWriter writer = ExcelUtil.getBigWriter(); final FileOutputStream fos = new FileOutputStream(tempFile)) {
            // 第一行数据
            writer.merge(13, firstLine);
            LinkedList<Object> builder = new LinkedList<>(List.of(
                    "序号",
                    "样本号",
                    "条码号",
                    "专业小组",
                    "患者姓名 ",
                    "检验项目",
                    "分拣人",
                    "分拣时间 ",
                    "样本类型",
                    "管型",
                    "性别",
                    "年龄",
                    "送检机构"));

            writer.writeHeadRow(builder);
            for (int i = 0; i <= 13; i++) {
                //设置样式，宽度
                writer.setColumnWidth(i, i == 5 ? 30 : 20);
            }


            //序号
            int num = 0;
            for (final TwoPickedApplySampleVo obj : filterList) {

                num++;
                builder.clear();
                builder.add(num);
                builder.add(obj.getSampleNo());
                builder.add(obj.getBarcode());
                builder.add(obj.getInstrumentGroupName());
                builder.add(obj.getPatientName());
                builder.add(CollectionUtils.isNotEmpty(obj.getTestItemNames()) ? StringUtils.join(obj.getTestItemNames(), ",") : "");
                builder.add(obj.getTwoPickerName());
                builder.add(obj.getTwoPickDate() != null ? DateFormatUtils.format(obj.getTwoPickDate(), "yyyy-MM-dd") : null);
                builder.add(obj.getSampleTypeName());
                builder.add(obj.getTubeName());
                builder.add(SexEnum.getByCode(obj.getPatientSex()).getDesc());
                String formatAge = String.format("%s%s", Objects.nonNull(obj.getPatientAge()) ? obj.getPatientAge() + "岁" : "", (obj.getPatientSubage() == 0 || null == obj.getPatientSubage()) ? "" : obj.getPatientSubage() + obj.getPatientSubageUnit());
                builder.add(formatAge);
                builder.add(obj.getHspOrgName());

                writer.writeRow(builder);
            }

            writer.flush(fos);
        }
        String encode = URLEncoder.encode(firstLine + ".xlsx", StandardCharsets.UTF_8);

        return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", encode))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel")).body(new FileSystemResource(tempFile));


    }


    /**
     * 批量分拣
     */
    @PostMapping("/racks")
    public Object racks(@RequestBody TwoPickRacksRequestVo vo) {
        if (Objects.isNull(vo.getBeginReceiveDate()) || Objects.isNull(vo.getEndReceiveDate())) {
            throw new IllegalArgumentException("参数错误");
        }

        final var samples =
                applySampleService.selectWaitingTwoPickSamples(vo.getBeginReceiveDate(), vo.getEndReceiveDate(), "")
                        .stream().collect(Collectors.groupingBy(WaitingTwoPickApplySampleDto::getRackLogicId));
        if (MapUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        return samples.entrySet().stream().map(e -> {
            final WaitingTwoPickApplySampleDto sample = e.getValue().iterator().next();

            final TwoPickRackVo v = new TwoPickRackVo();
            v.setRackLogicId(e.getKey());
            v.setRackCode(sample.getRackCode());
            v.setCount(e.getValue().size());
            v.setReceiveDate(sample.getRackLogicUpdateDate());
            return v;
        }).sorted(Comparator.comparing(TwoPickRackVo::getReceiveDate)).collect(Collectors.toList());

    }

    /**
     * 批量分拣的样本信息
     */
    @PostMapping("/racks/samples")
    public Object racksSamples(@RequestParam Long rackLogicId) {
        if (Objects.isNull(rackLogicId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<RackLogicApplySampleDto> nosortSamples = applySampleService.selectByRackLogicId(rackLogicId);
        if (CollectionUtils.isEmpty(nosortSamples)) {
            return Collections.emptyList();
        }

        final RackLogicDto rackLogic = rackLogicService.selectByRackLogicId(rackLogicId);
        if (Objects.isNull(rackLogic)) {
            throw new IllegalArgumentException("逻辑试管架不存在");
        }

        // 申请单
        final Map<Long,
                ApplyDto> applies = applyService
                .selectByApplyIds(nosortSamples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));

        // 项目
        final Map<Long,
                List<ApplySampleItemDto>> applySampleItems = applySampleItemService.selectByApplyIds(applies.keySet())
                .stream().filter(e -> Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()))
                .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 查找到专业小组
        final Map<Long,
                InstrumentGroupDto> instrumentGroups = instrumentGroupService
                .selectByGroupIds(applySampleItems.values().stream().flatMap(Collection::stream)
                        .map(ApplySampleItemDto::getGroupId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(InstrumentGroupDto::getInstrumentGroupId, v -> v, (a, b) -> a));

        // 查找到专业小组下的项目
        final var instrumentGroupTestItems =
                instrumentGroupTestItemService.selectByInstrumentGroupIds(instrumentGroups.keySet()).stream()
                        .collect(Collectors.groupingBy(InstrumentGroupTestItemDto::getInstrumentGroupId));

        // 要分拣到的专业小组
        // key: applySampleId
        // value: instrumentGroupId
        final Map<Long, Long> sampleInstrumentGroups = nosortSamples.stream().map(e -> {

            // 获取到匹配的，根据检验项目匹配，一致的那就是可以在这个专业小组下做
            final var filteredInstrumentGroups = instrumentGroupTestItems.entrySet().stream().filter(k -> {
                for (ApplySampleItemDto item : applySampleItems.get(e.getApplySampleId())) {
                    // 如果有一个不匹配那么这个专业小组就不可以做
                    if (k.getValue().stream()
                            .noneMatch(l -> Objects.equals(item.getTestItemCode(), l.getTestItemCode()))) {
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(filteredInstrumentGroups)) {
                throw new IllegalStateException(String.format("条码 [%s] 没有可分拣到的专业小组", e.getBarcode()));
            }

            return new AbstractMap.SimpleImmutableEntry<>(e.getApplySampleId(),
                    filteredInstrumentGroups.iterator().next().getKey());
        }).collect(Collectors.toMap(AbstractMap.SimpleImmutableEntry::getKey,
                AbstractMap.SimpleImmutableEntry::getValue, (a, b) -> a));

        final Set<Long> instrumentGroupIds = new LinkedHashSet<>(sampleInstrumentGroups.values());

        final List<InstrumentGroupDto> targetInstrumentGroups = instrumentGroups.values().stream()
                .filter(e -> instrumentGroupIds.contains(e.getInstrumentGroupId())).collect(Collectors.toList());
        if (targetInstrumentGroups.size() != instrumentGroupIds.size()) {
            throw new IllegalStateException("专业小组错误");
        }

        final List<RackLogicApplySampleDto> samples = new ArrayList<>(nosortSamples.size());
        // 根据坐标系排序
        for (int y = 0; y < rackLogic.getColumn(); y++) {
            for (int x = 0; x < rackLogic.getRow(); x++) {
                for (final Iterator<RackLogicApplySampleDto> iterator = nosortSamples.iterator(); iterator.hasNext(); ) {
                    final RackLogicApplySampleDto sample = iterator.next();
                    // 之所以匹配到之后没有 break 是因为怕有多个相同位置的
                    if (Objects.equals(sample.getRow(), x) && Objects.equals(sample.getColumn(), y)) {
                        iterator.remove();
                        samples.add(sample);
                    }
                }
            }
        }

        // 坐标系以外的随机顺序
        samples.addAll(nosortSamples);

        // 快速获取样本号
        final Map<Long, ApplySampleTwoPickDto> sampleNos = applySampleService.quicklyGetTwoPickInfoByApplySampleIds(
                samples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList()));

        return Map.of(
                // 专业组
                "samples", samples.stream().map(e -> {
                    @Nullable final ApplyDto apply = applies.get(e.getApplyId());
                    final TwoPickRackSampleVo v = new TwoPickRackSampleVo();

                    if (Objects.nonNull(apply)) {
                        BeanUtils.copyProperties(apply, v);
                    }

                    v.setApplySampleId(e.getApplySampleId());
                    v.setIsTwoPick(e.getIsTwoPick());
                    v.setTestItemNames(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList()).stream()
                            .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
                    v.setBarcode(e.getBarcode());
                    v.setTwoPickDate(e.getTwoPickDate());
                    v.setSampleTypeName(e.getSampleTypeName());
                    v.setTubeName(e.getTubeName());
                    v.setInstrumentGroupId(sampleInstrumentGroups.get(e.getApplySampleId()));
                    v.setSecondSortColor(instrumentGroups.get(v.getInstrumentGroupId()).getSecondSortColor());

                    v.setColumn(e.getColumn());
                    v.setRow(e.getRow());

                    // 快速获取样本号
                    v.setSampleNo(Optional.ofNullable(sampleNos.get(e.getApplySampleId()))
                            .map(ApplySampleTwoPickDto::getSampleNo).orElse(StringUtils.EMPTY));

                    return v;
                }).collect(Collectors.toList()),
                // 专业小组
                "instrumentGroups",
                targetInstrumentGroups.stream()
                        .map(e -> Map.of("instrumentGroupId", e.getInstrumentGroupId(), "instrumentGroupName",
                                e.getInstrumentGroupName(), "secondSortColor", e.getSecondSortColor()))
                        .collect(Collectors.toList()),
                // 试管架信息
                "rack", Map.of("rackCode", rackLogic.getRackCode(),
                        "row", rackLogic.getRow(),
                        "column", rackLogic.getColumn()));

    }

    /**
     * 批量分拣 已分拣的样本信息
     */
    @PostMapping("/racks/picked-samples")
    public Object racksPickedSamples(@RequestParam Long rackLogicId) {
        if (Objects.isNull(rackLogicId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<RackLogicApplySampleDto> samples =
                new LinkedList<>(applySampleService.selectByRackLogicId(rackLogicId));
        // 删除没有二次分拣的
        samples.removeIf(e -> Objects.equals(e.getIsTwoPick(), YesOrNoEnum.NO.getCode()));
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        // 申请单
        final Map<Long,
                ApplyDto> applies = applyService
                .selectByApplyIds(samples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet())).stream()
                .collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));

        // 项目
        final Map<Long, List<ApplySampleItemDto>> applySampleItems =
                applySampleItemService.selectByApplyIds(applies.keySet()).stream()
                        .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        return samples.stream().map(e -> {
            @Nullable final ApplyDto apply = applies.get(e.getApplyId());
            final MultiTwoPickRackSampleVo v = new MultiTwoPickRackSampleVo();

            if (Objects.nonNull(apply)) {
                BeanUtils.copyProperties(apply, v);
            }

            v.setApplySampleId(e.getApplySampleId());
            v.setTestItemNames(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList()).stream()
                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            v.setBarcode(e.getBarcode());
            v.setTwoPickDate(e.getTwoPickDate());
            v.setSampleType(e.getSampleTypeName());
            v.setTube(e.getTubeName());

            return v;
        }).collect(Collectors.toList());

    }

    /**
     * 条码项目二次分拣明细
     */
    @PostMapping("/test-item/detail")
    public Object twoPickSampleDetail(@RequestParam String barcode) {
        if (StringUtils.isBlank(barcode)) {
            throw new IllegalArgumentException("参数错误");
        }

        List<ApplySampleItemTwoPickDetailDto> sampleItems = applySampleService.selectApplySampleItemTwoPickDetailByBarcode(barcode);

        // 删除项目已终止的
        sampleItems.removeIf(e -> !Objects.equals(StopTestStatus.NO_STOP_TEST.getCode(), e.getStopStatus()));
        // 删除已禁用的项目
        sampleItems.removeIf(e -> Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()));
        // 删除申请单样本已终止的
        sampleItems.removeIf(e -> Objects.equals(SampleStatusEnum.STOP_TEST.getCode(), e.getStatus()));

        if (CollectionUtils.isEmpty(sampleItems)) {
            return Collections.emptyList();
        }

        // 快速获取样本号
        final Map<Long, ApplySampleTwoPickDto> stps = applySampleService.quicklyGetTwoPickInfoByApplySampleIds(
                sampleItems.stream().map(ApplySampleItemTwoPickDetailDto::getApplySampleId).collect(Collectors.toList()));

        return sampleItems.stream().map(e -> {
            final ApplySampleItemTwoPickDetailVo v = new ApplySampleItemTwoPickDetailVo();

            BeanUtils.copyProperties(e, v);

            if (stps.containsKey(e.getApplySampleId()) && StringUtils.isNotBlank(stps.get(e.getApplySampleId()).getSampleNo())) {
                v.setSampleNo(stps.get(e.getApplySampleId()).getSampleNo());
            }

            if (Objects.equals(YesOrNoEnum.NO.getCode(), e.getIsTwoPick())) {
                v.setTwoPickDate(null);
            }

            return v;
        }).collect(Collectors.toList());

    }

}
