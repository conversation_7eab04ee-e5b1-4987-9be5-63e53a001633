package com.labway.lims.apply.service.pda;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.business.center.compare.request.compare.DiscardedPDASampleInfoRequest;
import com.labway.business.center.compare.service.TbOrgApplySampleMainAdditionalService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.SampleSourceEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.ApplySampleImageService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.pda.PdaApplySampleItemService;
import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.api.service.pda.PdaTobeConfirmedApplyService;
import com.labway.lims.apply.mapper.pda.PdaApplyMapper;
import com.labway.lims.apply.model.pda.TbPdaApply;
import com.labway.lims.apply.service.chain.apply.add.AddApplyContext;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import com.labway.lims.apply.service.chain.pda.apply.add.PdaAddApplyChain;
import com.labway.lims.apply.service.chain.pda.apply.update.PdaUpdateApplyChain;
import com.labway.lims.apply.util.PdaApplyCacheUtil;
import com.swak.frame.dto.Response;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.labway.lims.apply.api.dto.PdaTobeConfirmedApplyDto.PdaConfirmEnum;

@Slf4j
@DubboService
@RefreshScope
public class PdaApplyServiceImpl extends ServiceImpl<PdaApplyMapper, TbPdaApply> implements PdaApplyService {

    @Resource
    private PdaAddApplyChain pdaAddApplyChain;
    @Resource
    private PdaUpdateApplyChain pdaUpdateApplyChain;
    @Resource
    private PdaApplySampleItemService pdaApplySampleItemService;
    @Resource
    private PdaTobeConfirmedApplyService pdaTobeConfirmedApplyService;
    @Resource
    private PdaApplyCacheUtil pdaApplyCacheUtil;
    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private TbOrgApplySampleMainAdditionalService tbOrgApplySampleMainAdditionalService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @Value("${business-center.org-code:00010110000000001WND}")
    private String orgCode;
    @DubboReference
    private ApplySampleImageService ApplySampleImageService;


    /**
     * 作废条码Redis key
     * 占位符顺序： redisPrefix.getBasePrefix()   masterBarcode
     */
    private static final String ABOLISH_MASTER_BARCODE_REDIS_KEY = "%sPDA:abolish:master-barcode:%s";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PdaApplyInfoDto addApply(PdaEntryTestApplyDto addApply) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        AddApplyContext context = new AddApplyContext();
        context.setTestApply(addApply);
        context.setUser(user);
        // 跳过血培养校验
        context.put(AddApplyContext.SKIP_BLOOD_CULTURE, true);
        try {
            if (!pdaAddApplyChain.execute(context)) {
                throw new IllegalStateException("添加PDA双输补录失败");
            }
            final String masterBarcode = context.getApply().getMasterBarcode();
            log.info("新建PDA双输补录成功 专业组 [{}] 操作人 [{}] 主条码 [{}}", user.getGroupName(), user.getNickname(), masterBarcode);

            return context.getPdaApplyInfo();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }
            log.error("新建PDA双输补录失败 专业组 [{}] 操作人 [{}]", user.getGroupName(), user.getNickname(), e);
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            log.info("用户 [{}] PDA双输补录耗时 {}", user.getNickname(), context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    public boolean addApply(PdaApplyDto applyDto) {
        return super.save(convert(applyDto));
    }

    @Override
    public List<PdaApplyDto> selectByMasterBarcode(String masterBarcode) {
        // 这个list最多两个需要控制
        List<TbPdaApply> list = super.list(new LambdaUpdateWrapper<TbPdaApply>()
                .eq(TbPdaApply::getMasterBarcode, masterBarcode));

        return list.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<PdaApplyDto> selectByMasterBarcodes(Collection<String> masterBarcodes) {
        if (CollectionUtils.isEmpty(masterBarcodes)) {
            return Collections.emptyList();
        }
        // 这个list最多两个需要控制
        List<TbPdaApply> list = super.list(new LambdaUpdateWrapper<TbPdaApply>()
                .in(TbPdaApply::getMasterBarcode, masterBarcodes));

        return list.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<PdaApplyDto>> selectByMasterBarcodesAsMap(Collection<String> masterBarcodes) {
        if (CollectionUtils.isEmpty(masterBarcodes)) {
            return Map.of();
        }
        // 这个list最多两个需要控制
        List<TbPdaApply> list = super.list(new LambdaUpdateWrapper<TbPdaApply>()
                .in(TbPdaApply::getMasterBarcode, masterBarcodes));

        return list.stream().map(this::convert).collect(Collectors.groupingBy(PdaApplyDto::getMasterBarcode));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PdaApplyInfoDto update(TestApplyDto update) {
        Long applyId = null;
        try {
            UpdateApplyContext context = new UpdateApplyContext();
            context.setTestApply(update);
            if (update instanceof UpdateTestApplyDto) {
                context.setBatchUpdateItem(((UpdateTestApplyDto) update).isBatchUpdateItem());
            }
            applyId = context.getApplyId();
            LoginUserHandler.User user = LoginUserHandler.get();
            context.setUser(user);

            if (!pdaUpdateApplyChain.execute(context)) {
                throw new IllegalStateException("更新申请单失败");
            }
            return context.getPdaApplyInfo();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }

            log.error("更新申请单失败 专业组 [{}] 操作人 [{}] 申请单ID [{}]", LoginUserHandler.get().getGroupName(),
                    LoginUserHandler.get().getNickname(), applyId, e);
            throw new IllegalStateException(e.getMessage(), e);
        }

    }

    @Override
    public PdaApplyDto selectById(Serializable pdaApplyId) {
        return convert(super.getById(pdaApplyId));
    }

    @Override
    public void updateApplyById(PdaApplyDto apply) {
        baseMapper.updateById(convert(apply));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void abolishMasterBarcode(String masterBarcode) {

        final PdaTobeConfirmedApplyDto pdaTobeConfirmedApplyDto = pdaTobeConfirmedApplyService.selectByMasterBarcode(masterBarcode);
        if (Objects.nonNull(pdaTobeConfirmedApplyDto) && Objects.equals(pdaTobeConfirmedApplyDto.getStatus(), PdaConfirmEnum.CONFIRM.getCode())) {
            throw new IllegalStateException("PDA申请单已确认");
        }

        final List<PdaApplyDto> pdaApplyDtos = this.selectByMasterBarcode(masterBarcode);
        final List<Long> pdaApplyIds = pdaApplyDtos.stream().map(PdaApplyDto::getPdaApplyId).collect(Collectors.toList());

        // 删除本地待确认申请单
        pdaTobeConfirmedApplyService.deleteByMasterBarcode(masterBarcode);
        // 删除本地申请单项目
        pdaApplySampleItemService.deleteByPdaApplyIds(pdaApplyIds);
        // 删除本地申请单
        ((PdaApplyService) AopContext.currentProxy()).deleteByMasterBarcode(masterBarcode);

        // 调用业务中台废除条码
        final LoginUserHandler.User user = LoginUserHandler.get();
        final DiscardedPDASampleInfoRequest request = new DiscardedPDASampleInfoRequest();
        request.setOrgCode(orgCode);
        request.setDiscardedStatus(NumberUtils.INTEGER_ONE);
        request.setLimsBarcodes(new ArrayList<>(List.of(masterBarcode)));
        request.setDiscardedUserCode(String.valueOf(user.getUserId()));
        request.setDiscardedUserName(user.getNickname());
        final Response<?> response = tbOrgApplySampleMainAdditionalService.discardedPDASampleInfo(request);
        if (!response.isSuccess()) {
            throw new IllegalStateException(response.getMsg());
        }
        // 作废条码信息加入缓存
        final String redisKey = String.format(ABOLISH_MASTER_BARCODE_REDIS_KEY, redisPrefix.getBasePrefix(), masterBarcode);
        stringRedisTemplate.opsForValue().set(redisKey, user.getNickname());
    }

    @Override
    public void deleteByMasterBarcode(String masterBarcode) {
        if (StringUtils.isBlank(masterBarcode)) {
            return;
        }

        this.remove(new LambdaUpdateWrapper<TbPdaApply>()
                .eq(TbPdaApply::getMasterBarcode, masterBarcode));
    }

    @Override
    public List<PdaApplyDto> selectByDate(BaseDataQueryDto dto, Long userId) {
        return this.list(new LambdaUpdateWrapper<TbPdaApply>()
                        .eq(StringUtils.isNotBlank(dto.getMasterBarcode()), TbPdaApply::getMasterBarcode, dto.getMasterBarcode())
                        .eq(Objects.nonNull(userId), TbPdaApply::getCreatorId, userId)
                        .eq(Objects.nonNull(dto.getHspOrgId()), TbPdaApply::getHspOrgId, dto.getHspOrgId())
                        .between(TbPdaApply::getCreateDate, dto.getStartDate(), dto.getEndDate()))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    /**
     * 主条码是否废除
     */
    public void masterBarcodeIsAbolish(String masterBarcode) {
        if (StringUtils.isBlank(masterBarcode)) {
            throw new IllegalArgumentException("主条码不能为空");
        }
        final String redisKey = String.format(ABOLISH_MASTER_BARCODE_REDIS_KEY, redisPrefix.getBasePrefix(), masterBarcode);
        final String nickName = stringRedisTemplate.opsForValue().get(redisKey);
        if (Objects.nonNull(nickName)) {
            throw new IllegalStateException(String.format("保存失败，该主条码号已被 %s 作废", nickName));
        }

    }

    @Override
    public ApplyInfo sign(SignPdaApplyDto signPdaApplyDto) {
        final String masterBarcode = signPdaApplyDto.getMasterBarcode();
        final Integer ignoreSameItem = signPdaApplyDto.getIgnoreSameItem();
        final Boolean ignoreItemLimitSex = signPdaApplyDto.getIgnoreItemLimitSex();
        final Long hspOrgId = signPdaApplyDto.getHspOrgId();
        final List<SignPdaApplyDto.PdaSignItem> signItems = ObjectUtils.defaultIfNull(signPdaApplyDto.getSignItems(), new ArrayList<>());
        final Map<Long, String> testItemCodeAndCustomCodeMap = signItems.stream().collect(Collectors.toMap(SignPdaApplyDto.PdaSignItem::getTestItemId, SignPdaApplyDto.PdaSignItem::getCustomCode));

        final PdaApplyAndItemDto pdaApplyAndItemDto = get(masterBarcode, hspOrgId);

        final PdaEntryTestApplyDto testApplyDto = JSON.parseObject(JSON.toJSONString(pdaApplyAndItemDto), PdaEntryTestApplyDto.class);
        testApplyDto.setItems(new ArrayList<>());
        final List<PdaApplySampleItemDto> pdaApplySampleItemDtoList = pdaApplyAndItemDto.getPdaApplySampleItemDtoList();
        for (PdaApplySampleItemDto dto : pdaApplySampleItemDtoList) {
            final TestApplyDto.Item item = JSON.parseObject(JSON.toJSONString(dto), TestApplyDto.Item.class);
            //            item.setBloodCulture(dto.getApplySampleItemBloodCultureDto());
            if (Objects.equals(dto.getTestItemCode(), signPdaApplyDto.getBloodCultureItemCode())) {
                item.setBloodCulture(signPdaApplyDto.getBloodCulture());
            }
            item.setCustomCode(testItemCodeAndCustomCodeMap.getOrDefault(item.getTestItemId(), Strings.EMPTY));
            testApplyDto.getItems().add(item);
        }
        testApplyDto.setPatientCardType(PatientCardTypeEnum.ID_CARD.name());
        testApplyDto.setApplySource(ApplySourceEnum.PDA_SIGN);
        testApplyDto.setSupplier("PDA样本信息签收");
        testApplyDto.setIgnoreSameItem(ignoreSameItem);
        testApplyDto.setIgnoreItemLimitSex(ignoreItemLimitSex);
        testApplyDto.setSampleSource(SampleSourceEnum.PDA.getCode());
        final ApplyInfo applyInfo = applyService.addApply(testApplyDto);

        // 确认表状态改为已确认状态
        pdaTobeConfirmedApplyService.updateStatusByMasterBarcode(masterBarcode);

        // 把签收之前的条码环节修改进去
        updatePdaSampleFlow(applyInfo);

        // 病理样本推送业务中台
        applyService.sendPathologyToBusinessCenter(List.of(applyInfo.getApplyId()), Collections.emptyList());

        // 保存pda样本的图片信息
        if (StringUtils.isNotBlank(testApplyDto.getPdaImgs())){
            log.info("保存pda样本的图片信息,申请单id{},图片地址：{}", applyInfo.getApplyId(), testApplyDto.getPdaImgs());
            ApplySampleImageService.insertBatch(getApplySampleImageDtos(testApplyDto, applyInfo));
        }

        // 删除缓存
        pdaApplyCacheUtil.deletePdaApplyCache(masterBarcode);

        return applyInfo;
    }


    private static List<ApplySampleImageDto> getApplySampleImageDtos(PdaEntryTestApplyDto testApplyDto, ApplyInfo applyInfo) {
        String[] splitImgs = testApplyDto.getPdaImgs().split(",");
        List<ApplySampleImageDto> ApplySampleImageDtos = new ArrayList<>();
        applyInfo.getSamples().forEach(e -> {
            Arrays.stream(splitImgs).forEach(imgUrl -> {
                ApplySampleImageDto dto = new ApplySampleImageDto();
                dto.setApplyId(applyInfo.getApplyId());
                dto.setApplySampleId(e.getApplySampleId());
                dto.setImageUrl(imgUrl);
                ApplySampleImageDtos.add(dto);
            });
        });

        return ApplySampleImageDtos;
    }

    private void updatePdaSampleFlow(ApplyInfo applyInfo) {
        final Long applyId = applyInfo.getApplyId();
        final String masterBarcode = applyInfo.getMasterBarcode();

        // 过滤是主条码的
        final List<SampleFlowDto> sampleFlowDtos = sampleFlowService.selectByBarcode(masterBarcode)
                .stream()
                .filter(e -> Objects.equals(e.getApplyId(), NumberUtils.LONG_ZERO) && Objects.equals(e.getApplySampleId(), NumberUtils.LONG_ZERO))
                .collect(Collectors.toList());

        final int sampleFlowNum = applyInfo.getSamples().size() * sampleFlowDtos.size();
        final LinkedList<Long> ids = snowflakeService.genIds(sampleFlowNum);
        final List<SampleFlowDto> newSampleFlowDtoList = applyInfo.getSamples().stream()
                .map(sample -> JSON.parseArray(JSON.toJSONString(sampleFlowDtos), SampleFlowDto.class).stream()
                        .peek(e -> {
                            e.setSampleFlowId(ids.pop());
                            e.setApplyId(applyId);
                            e.setApplySampleId(sample.getApplySampleId());
                            e.setBarcode(sample.getBarcode());
                        })
                        .collect(Collectors.toList()))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        sampleFlowService.addSampleFlows(newSampleFlowDtoList);
    }

    @Override
    public PdaApplyAndItemDto get(String masterBarcode, Long hspOrgId) {
        if (StringUtils.isBlank(masterBarcode)) {
            throw new IllegalArgumentException("主条码不能为空");
        }
        final PdaApplyAndItemDto pdaApplyCache = pdaApplyCacheUtil.getPdaApplyCache(masterBarcode);
        if (Objects.nonNull(pdaApplyCache)) {
            return pdaApplyCache;
        }

        // 查询pda申请单 根据主条码获取一个
        final PdaTobeConfirmedApplyDto pdaTobeConfirmedApplyDto = pdaTobeConfirmedApplyService.selectByMasterBarcodeAndHspOrgCode(masterBarcode, hspOrgId);

        // 没有查询到直接返回
        if (Objects.isNull(pdaTobeConfirmedApplyDto)) {
            throw new IllegalStateException("该机构下未查询到该主条码确认信息");
        }

        if (Objects.equals(PdaConfirmEnum.SIGN.getCode(), pdaTobeConfirmedApplyDto.getStatus())) {
            throw new IllegalStateException("PDA申请单已签收");
        }

        if (Objects.equals(PdaConfirmEnum.NO_CONFIRM.getCode(), pdaTobeConfirmedApplyDto.getStatus())) {
            throw new IllegalStateException("PDA双输补录未确认");
        }

        final PdaApplyDto pdaApplyDto = this.selectById(pdaTobeConfirmedApplyDto.getConfirmedPdaApplyId());
        if (Objects.isNull(pdaApplyDto)) {
            throw new IllegalStateException("未查询到PDA申请单信息");
        }

        // 查询申请单下的项目
        final List<PdaApplySampleItemDto> pdaApplySampleItemDtos = pdaApplySampleItemService.selectByPdaApplyId(pdaApplyDto.getPdaApplyId());
        if (CollectionUtils.isEmpty(pdaApplySampleItemDtos)) {
            throw new IllegalStateException("未查询到PDA申请单的项目");
        }

        final PdaApplyAndItemDto pdaApplyAndItemDto = BeanUtil.toBean(pdaApplyDto, PdaApplyAndItemDto.class);
        pdaApplyAndItemDto.setPdaApplySampleItemDtoList(pdaApplySampleItemDtos);
        pdaApplyAndItemDto.setPdaImgs(pdaTobeConfirmedApplyDto.getPdaImgs());

        // 缓存
        pdaApplyCacheUtil.setPdaApplyCache(pdaApplyAndItemDto);

        return pdaApplyAndItemDto;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelSign(HisCancelSignParam cancelSignParam) {
        final Set<Long> applyIds = cancelSignParam.getApplyIds();
        if (CollectionUtils.isEmpty(applyIds)) {
            throw new IllegalStateException("未找到相关申请单信息");
        }

        final Map<Long, ApplyDto> applyMap = applyService.selectByApplyIdsAsMap(applyIds);
        if (MapUtils.isEmpty(applyMap)) {
            throw new IllegalStateException("未找到相关申请单信息");
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplyIds(applyMap.keySet());
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("未找到相关条码信息");
        }

        Map<Long, Boolean> cancelOnePickMap = new HashMap<>(applySamples.size());
        for (final ApplySampleDto applySample : applySamples) {
            final ApplyDto apply = applyMap.get(applySample.getApplyId());
            if (Objects.isNull(apply)) {
                throw new IllegalStateException(String.format("条码号 [%s] 外部条码 [%s] 未找到相关申请单信息", applySample.getBarcode(),
                        applySample.getOutBarcode()));
            }

            // 已完成二次分拣（包含一审、二审、已归档）不能撤销接收
            if (Objects.equals(applySample.getIsTwoPick(), PdaConfirmEnum.CONFIRM.getCode())) {
                throw new IllegalStateException("该条码已完成二次分拣，不能取消签收，请取消二次分拣");
            }

            // 样本是不是已经一次分拣过了
            cancelOnePickMap.put(
                    applySample.getApplySampleId(),
                    Objects.equals(applySample.getIsOnePick(), PdaConfirmEnum.CONFIRM.getCode()));
        }

        StopWatch watch = new StopWatch();
        watch.start("取消签收");
        // 已完成一次分拣的，取消签收之前先取消分拣，回收试管架先
        applySamples.stream()
                .map(ApplySampleDto::getApplySampleId)
                .forEach(applySampleId -> {
                    if (cancelOnePickMap.getOrDefault(applySampleId, false)) {
                        applySampleService.cancelOnePick(applySampleId);
                    }
                });

        final Set<String> outBarcodes =
                applySamples.stream().map(ApplySampleDto::getOutBarcode).collect(Collectors.toSet());

        watch.stop();


        // 删除申请单+样本+检验项目
        applyService.deleteApply(applySamples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet()));

        // 签收状态改为确认状态
        final Set<String> masterBarcodes = applyMap.values().stream().map(ApplyDto::getMasterBarcode).collect(Collectors.toSet());
        pdaTobeConfirmedApplyService.cancelSignByMasterCodes(masterBarcodes);

        log.info("用户 [{}] 专业组 [{}] 条码取消签收成功，条码号：[{}] 耗时 [{}]", LoginUserHandler.get().getNickname(),
                LoginUserHandler.get().getGroupName(), outBarcodes, watch.getTotalTimeMillis());
    }

    @Override
    public void deleteByPdaApplyId(Long pdaApplyId) {
        if (Objects.nonNull(pdaApplyId)) {
            super.removeById(pdaApplyId);
        }
    }

    private PdaApplyDto convert(TbPdaApply tbPdaApply) {
        return JSON.parseObject(JSON.toJSONString(tbPdaApply), PdaApplyDto.class);
    }

    private TbPdaApply convert(PdaApplyDto pdaApplyDto) {
        return JSON.parseObject(JSON.toJSONString(pdaApplyDto), TbPdaApply.class);
    }
}
