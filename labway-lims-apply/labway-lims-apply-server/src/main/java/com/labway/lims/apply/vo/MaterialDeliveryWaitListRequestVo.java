package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 物料入库 待入库 列表
 *
 * <AUTHOR>
 * @since 2023/5/6 16:11
 */
@Getter
@Setter
public class MaterialDeliveryWaitListRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 出库日期 开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date beginDeliveryDate;

    /**
     * 出库日期 结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endDeliveryDate;

    /**
     * 出库单号 精确查询
     */
    private String deliveryNo;

}
