package com.labway.lims.apply.controller;

import com.alibaba.fastjson.JSONObject;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.BloodOneSplitDto;
import com.labway.lims.apply.api.dto.RackLogicApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SplitBloodApplySampleDto;
import com.labway.lims.apply.api.dto.WaitingSplitBloodApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.vo.BloodOneSplitVo;
import com.labway.lims.apply.vo.BloodRackSampleVo;
import com.labway.lims.apply.vo.HandoverRacksRequestVo;
import com.labway.lims.apply.vo.NotifySplitBloodVo;
import com.labway.lims.apply.vo.SplitBloodApplySampleVo;
import com.labway.lims.apply.vo.SplitBloodHandoverRackSampleVo;
import com.labway.lims.apply.vo.SplitBloodHandoverRackVo;
import com.labway.lims.apply.vo.SplitBloodHandoverVo;
import com.labway.lims.apply.vo.SplitSampleRequestVo;
import com.labway.lims.apply.vo.WaitingSplitBloodApplySampleVo;
import com.labway.lims.apply.vo.WaitingSplitSampleRequestVo;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.api.service.UserService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 血
 */
@Slf4j
@RestController
@RequestMapping("/blood")
public class BloodController extends BaseController {
    @Value("${business-center.org-code:未知实验室编码}")
    private String orgCode;
    @Value("${business-center.org-name:未知实验室名称}")
    private String orgName;
    @Value("${business-center.url.notify-split-blood:http://121.36.199.164/9901/lims/notifySplitBlood}")
    private String notifySplitBloodUrl;

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private ApplyService applyService;
    @Resource
    private RackLogicService rackLogicService;
    @DubboReference
    private UserService userService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @DubboReference
    private GroupService groupService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private RestTemplate restTemplate;


    @DubboReference
    private SystemParamService systemParamService;

    /**
     * 分血
     */
    @PostMapping("/one-split")
    public Object split(@RequestParam String barcode) {

        final Long applySampleId = this.genApplySampleId(barcode);

        final Set<Long> applySampleIds = new HashSet<>(applySampleService
                // 分血
                .splitBloodByApplySampleId(applySampleId));

        if (CollectionUtils.isEmpty(applySampleIds)) {
            applySampleIds.add(applySampleId);
        }

        final List<ApplySampleDto> samples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        final Map<Long, ApplyDto> applies = applyService.selectByApplyIds(samples.stream().map(ApplySampleDto::getApplyId)
                .collect(Collectors.toSet())).stream().collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));

        final Map<Long, List<ApplySampleItemDto>> applySampleItems = applySampleItemService.selectByApplySampleIds(samples.stream()
                        .map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        final Map<Long, TestItemDto> testItems = testItemService.selectByTestItemIdsAsMap(applySampleItems.values().stream().flatMap(Collection::stream)
                .map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet()));

        return samples.stream().map(e -> {
            final BloodOneSplitVo v = new BloodOneSplitVo();
            BeanUtils.copyProperties(e, v);

            if (applies.containsKey(e.getApplyId())) {
                BeanUtils.copyProperties(applies.get(e.getApplyId()), v);
            }

            // 当样本数量为 1 的时候，那么可能有多个专业组
            if (samples.size() == 1) {
                v.setGroupNames(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList())
                        .stream().map(ApplySampleItemDto::getGroupName).distinct().collect(Collectors.toList()));
            } else {
                v.setGroupNames(List.of(e.getGroupName()));
            }

            // 和分血前不一样的ID，那就是新生成的
            v.setIsNewSplitSample(v.getBarcode().contains("_"));

            // 检验项目
            v.setTestItemNames(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList())
                    .stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));


            // 分血量
            v.setBasicQuantity(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList()).stream().map(l -> testItems.get(l.getTestItemId()))
                    .filter(Objects::nonNull).max(Comparator.comparing(TestItemDto::getBasicQuantity))
                    .map(TestItemDto::getBasicQuantity).orElse(BigDecimal.ZERO));


            return v;
        }).sorted((o1, o2) -> StringUtils.compare(o1.getBarcode(), o2.getBarcode())).collect(Collectors.toList());

    }

    /**
     * 分血信息
     * 【单个分血】增加分血按钮，此处增加系统参数配置】
     * https://www.tapd.cn/59091617/prong/stories/view/1159091617001001695
     */
    @PostMapping("/one-split-info")
    public Object splitInfo(@RequestParam String barcode) {

        final Long applySampleId = this.genApplySampleId(barcode);

        // 分血信息
        final List<BloodOneSplitDto> bloodOneSplitDtos = applySampleService.splitBloodInfoByApplySampleId(applySampleId);

        return bloodOneSplitDtos.stream().map(dto -> {
            final BloodOneSplitVo vo = new BloodOneSplitVo();
            BeanUtils.copyProperties(dto, vo);
            return vo;
        }).collect(Collectors.toList());

    }


    private Long genApplySampleId(String barcode) {
        final ProfessionalGroupDto splitBloodGroup = groupService.selectSplitBloodGroup(LoginUserHandler.get().getOrgId());
        // 如果当前登录人的专业组不是分血组，那么无权分血
        if (!Objects.equals(splitBloodGroup.getGroupId(), LoginUserHandler.get().getGroupId())) {
            throw new IllegalArgumentException("当前所在专业组无法分血");
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException("条码不存在");
        }

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getIsSplitBlood(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("该条码已分血");
        }

        // 分血的时候，只能有一个
        if (applySamples.size() != 1) {
            throw new IllegalArgumentException("条码数量错误或已经分血");
        }
        return applySamples.iterator().next().getApplySampleId();
    }


    /**
     * 等待分血的列表
     */
    @PostMapping("/waiting-split-samples")
    public Object waitingSplitSamples(@RequestBody WaitingSplitSampleRequestVo vo) {
        if (Objects.isNull(vo.getBeginReceiveDate()) || Objects.isNull(vo.getEndReceiveDate())) {
            throw new IllegalArgumentException("参数错误");
        }

        // 分血组
        final ProfessionalGroupDto group = groupService.selectSplitBloodGroup(LoginUserHandler.get().getOrgId());


        // 不是分血组的人看不到
        if (!Objects.equals(LoginUserHandler.get().getGroupId(), group.getGroupId())) {
            return Collections.emptyList();
        }

        // 待分血的列表
        final List<WaitingSplitBloodApplySampleDto> waitingSplitBloodApplySamples = applySampleService.selectWaitingSplitBloodSamples(vo.getBeginReceiveDate(),
                vo.getEndReceiveDate(), group.getGroupId());

        if (CollectionUtils.isEmpty(waitingSplitBloodApplySamples)) {
            return Collections.emptyList();
        }

        // 找到项目
        final List<ApplySampleItemDto> items = applySampleItemService.selectByApplyIds(waitingSplitBloodApplySamples.stream()
                .map(WaitingSplitBloodApplySampleDto::getApplyId).collect(Collectors.toSet()));

        // https://www.tapd.cn/59091617/bugtrace/bugs/view/1159091617001001344
        waitingSplitBloodApplySamples.sort(Comparator.comparing(WaitingSplitBloodApplySampleDto::getOnePickDate));

        return waitingSplitBloodApplySamples.stream().map(e -> {
            final WaitingSplitBloodApplySampleVo v = new WaitingSplitBloodApplySampleVo();
            BeanUtils.copyProperties(e, v);
            v.setTestItemNames(items.stream().filter(l -> Objects.equals(l.getApplySampleId(), e.getApplySampleId()))
                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));

            v.setGroupNames(items.stream().filter(l -> Objects.equals(l.getApplySampleId(), e.getApplySampleId()))
                    .map(ApplySampleItemDto::getGroupName).distinct().collect(Collectors.toList()));

            return v;
        }).collect(Collectors.toList());


    }


    /**
     * 已经分血的列表
     */
    @PostMapping("/split-samples")
    public Object splitSamples(@RequestBody SplitSampleRequestVo vo) {


        if (Objects.isNull(vo.getBeginSplitDate()) || Objects.isNull(vo.getEndSplitDate()) || vo.getBeginSplitDate().after(vo.getEndSplitDate())) {
            throw new IllegalArgumentException("时间范围错误");
        }


        final ProfessionalGroupDto group = groupService.selectSplitBloodGroup(LoginUserHandler.get().getOrgId());

        // 不是分血组的人看不到
        if (!Objects.equals(LoginUserHandler.get().getGroupId(), group.getGroupId())) {
            return Collections.emptyList();
        }


        // 会查询到重复的，因为有禁止分血的样本
        final LinkedHashMap<Long, List<SplitBloodApplySampleDto>> samples = applySampleService.selectSplitBloodSamples(vo.getBeginSplitDate(), vo.getEndSplitDate())
                .stream().collect(Collectors.groupingBy(SplitBloodApplySampleDto::getApplySampleId, LinkedHashMap::new, Collectors.toList()));
        final LinkedHashMap<Long, List<SplitBloodApplySampleDto>> afterSplitBloodSamples = applySampleService.selectAfterSplitBloodSamples(vo.getBeginSplitDate(), vo.getEndSplitDate())
                .stream()
                .collect(Collectors.groupingBy(SplitBloodApplySampleDto::getApplySampleId, LinkedHashMap::new, Collectors.toList()));
        samples.putAll(afterSplitBloodSamples);

        if (MapUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }


        // 申请单项目
        final Map<Long, List<ApplySampleItemDto>> items = applySampleItemService.selectByApplySampleIds(samples.keySet()).stream()
                .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));


        // 检验项目
        final List<TestItemDto> testItems = testItemService.selectByTestItemIds(items.values().stream()
                .flatMap(Collection::stream).map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet()));


        return samples.entrySet().stream().map(e -> {
                    final SplitBloodApplySampleVo v = new SplitBloodApplySampleVo();
                    BeanUtils.copyProperties(e.getValue().iterator().next(), v);

                    final List<ApplySampleItemDto> applyItems = items.getOrDefault(e.getKey(), Collections.emptyList());
                    if (CollectionUtils.isEmpty(applyItems)) {
                        return v;
                    }

                    v.setTestItemNames(applyItems.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
                    v.setGroupNames(applyItems.stream().map(ApplySampleItemDto::getGroupName).distinct().collect(Collectors.toList()));

                    final TestItemDto testItem = testItems.stream().filter(l -> testItems.stream()
                                    .anyMatch(k -> Objects.equals(k.getTestItemId(), l.getTestItemId())))
                            .max(Comparator.comparing(TestItemDto::getBasicQuantity)).stream().findFirst().orElse(null);
                    if (Objects.isNull(testItem)) {
                        return v;
                    }

                    v.setBasicQuantity(testItem.getBasicQuantity());

                    return v;
                })
                // 根据分血时间正序
                .sorted(Comparator.comparing(SplitBloodApplySampleDto::getSplitDate))
                .collect(Collectors.toList());


    }


    /**
     * 要分血的试管架
     */
    @PostMapping("/racks")
    public Object racks(@RequestBody WaitingSplitSampleRequestVo vo) {

        if (Objects.isNull(vo.getBeginReceiveDate()) || Objects.isNull(vo.getEndReceiveDate())) {
            throw new IllegalArgumentException("参数错误");
        }

        final ProfessionalGroupDto group = groupService.selectSplitBloodGroup(LoginUserHandler.get().getOrgId());

        // 不是分血组的人看不到
        if (!Objects.equals(LoginUserHandler.get().getGroupId(), group.getGroupId())) {
            return Collections.emptyList();
        }


        // 待分血的列表
        final var waitingSplitBloodApplySamples = applySampleService.selectWaitingSplitBloodSamples(vo.getBeginReceiveDate(),
                vo.getEndReceiveDate(), group.getGroupId()).stream().collect(Collectors.groupingBy(WaitingSplitBloodApplySampleDto::getRackLogicId));

        if (MapUtils.isEmpty(waitingSplitBloodApplySamples)) {
            return Collections.emptyList();
        }


        return waitingSplitBloodApplySamples.keySet().stream().map(e -> Map.of(
                "rackCode", waitingSplitBloodApplySamples.get(e).iterator().next().getRackCode(),
                "rackLogicId", e,
                "count", waitingSplitBloodApplySamples.get(e).size()
        )).collect(Collectors.toList());

    }


    /**
     * 要分血的试管架下面的样本
     */
    @GetMapping("/racks/samples")
    public Object racksSamples(@RequestParam Long rackLogicId) {

        if (Objects.isNull(rackLogicId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final ProfessionalGroupDto group = groupService.selectSplitBloodGroup(LoginUserHandler.get().getOrgId());

        // 不是分血组的人看不到
        if (!Objects.equals(LoginUserHandler.get().getGroupId(), group.getGroupId())) {
            return Collections.emptyList();
        }


        // 获取到这个试管架下面的样本
        final List<RackLogicApplySampleDto> samples = applySampleService.selectByRackLogicId(rackLogicId).stream()
                .filter(e -> Objects.equals(e.getPosition(), RackLogicPositionEnum.SPLITTING_BLOOD.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        final Map<Long, ApplyDto> applies = applyService.selectByApplyIds(samples.stream()
                        .map(ApplySampleDto::getApplyId).collect(Collectors.toSet())).stream()
                .collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));
        if (MapUtils.isEmpty(applies)) {
            return Collections.emptyList();
        }

        // 获取到项目
        final Map<Long, List<ApplySampleItemDto>> items = applySampleItemService.selectByApplyIds(applies.keySet())
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));
        if (MapUtils.isEmpty(items)) {
            return Collections.emptyList();
        }

        // 送检机构
        final Map<String, HspOrganizationDto> orgs = hspOrganizationService.selectByHspOrgIds(applies.values().stream()
                        .map(ApplyDto::getHspOrgId).collect(Collectors.toSet())).stream()
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgCode, v -> v, (a, b) -> a));


        // 获取到检验项目
        final Map<String, TestItemDto> testItems = testItemService.selectByTestItemCodes(items.values().stream()
                        .flatMap(Collection::stream).map(ApplySampleItemDto::getTestItemCode).collect(Collectors.toSet()), LoginUserHandler.get().getOrgId())
                .stream().collect(Collectors.toMap(TestItemDto::getTestItemCode, v -> v, (a, b) -> a));


        final List<BloodRackSampleVo> list = new LinkedList<>();
        for (RackLogicApplySampleDto e : samples) {

            final Map<String, List<ApplySampleItemDto>> applySampleItems = items.getOrDefault(e.getApplySampleId(), Collections.emptyList())
                    .stream().collect(Collectors.groupingBy(k -> String.valueOf(k.getGroupId()) + Optional.ofNullable(testItems.get(k.getTestItemCode()))
                            .map(TestItemDto::getExportOrgId).orElse(Long.MIN_VALUE)));
            if (MapUtils.isEmpty(applySampleItems)) {
                throw new IllegalStateException(String.format("样本 [%s] 没有检验项目", e.getBarcode()));
            }

            final var entries = new ArrayList<>(applySampleItems.entrySet());
            final boolean enableSplitBlood = Objects.equals(Optional.ofNullable(orgs.get(e.getHspOrgCode()))
                            .map(HspOrganizationDto::getEnableSplitBlood).orElse(YesOrNoEnum.NO.getCode()),
                    YesOrNoEnum.YES.getCode());
            final int size = enableSplitBlood ? entries.size() : 1;
            for (int i = 0; i < size; i++) {

                final var entry = entries.get(i);

                final BloodRackSampleVo vo = new BloodRackSampleVo();
                BeanUtils.copyProperties(e, vo);

                BeanUtils.copyProperties(applies.get(vo.getApplyId()), vo);

                if (i > 0) {
                    vo.setBarcode(vo.getBarcode() + "_" + StringUtils.leftPad(String.valueOf(i), 2, '0'));
                }

                if (enableSplitBlood) {
                    vo.setTestItemNames(entry.getValue().stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
                    vo.setGroupNames(entry.getValue().stream().map(ApplySampleItemDto::getGroupName).distinct().collect(Collectors.toList()));
                } else {
                    final List<ApplySampleItemDto> its = items.getOrDefault(e.getApplySampleId(), Collections.emptyList());
                    vo.setTestItemNames(its.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
                    vo.setGroupNames(its.stream().map(ApplySampleItemDto::getGroupName).distinct().collect(Collectors.toList()));
                }


                // 分血量
                vo.setBasicQuantity(entry.getValue().stream().map(l -> testItems.values().stream().filter(k -> Objects.equals(l.getTestItemId(), k.getTestItemId())).findFirst().orElse(null))
                        .filter(Objects::nonNull).max(Comparator.comparing(TestItemDto::getBasicQuantity))
                        .map(TestItemDto::getBasicQuantity).orElse(BigDecimal.ZERO));


                list.add(vo);
            }
        }


        return Map.of(
                "origin", samples.size(),
                "samples", list,
                "count", list.size()
        );

    }

    /**
     * 分血后交接的试管架
     */
    @PostMapping("/handover-racks")
    public Object handoverRacks(@RequestBody HandoverRacksRequestVo vo) {
        if (Objects.isNull(vo.getBeginSplitDate()) || Objects.isNull(vo.getEndSplitDate())) {
            throw new IllegalArgumentException("参数错误");
        }

        Map<Long, List<SplitBloodApplySampleDto>> samples;
        //判断是否需要将分拣提前
        boolean sortingBloodPreHandover;
        SystemParamDto systemParamDto = systemParamService.selectByParamName(SystemParamNameEnum.SORTING_BLOOD_PRE_HANDOVER.getCode()
                , LoginUserHandler.get().getOrgId());
        if (systemParamDto != null && YesOrNoEnum.YES.getDesc().equals(systemParamDto.getParamValue())) {
            samples = applySampleService.selectSplitBloodSamples(vo.getBeginSplitDate(), vo.getEndSplitDate())
                    .stream().collect(Collectors.groupingBy(SplitBloodApplySampleDto::getRackLogicId));
            sortingBloodPreHandover = true;
        } else {
            sortingBloodPreHandover = false;
            samples = applySampleService.selectSplitBloodSamples(vo.getBeginSplitDate(), vo.getEndSplitDate())
                    .stream().filter(e -> Objects.equals(e.getNextGroupId(), LoginUserHandler.get().getGroupId()))
                    .collect(Collectors.groupingBy(SplitBloodApplySampleDto::getRackLogicId));
        }
        if (MapUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        // 排序
        final Collection<List<SplitBloodApplySampleDto>> ss = samples.values().stream().sorted((o1, o2) -> Long.compare(o1.stream().mapToLong(e -> e.getSplitDate().getTime()).sum(),
                o2.stream().mapToLong(e -> e.getSplitDate().getTime()).sum())).collect(Collectors.toList());

        // 获取到检验项目然后获取到哪些专业组可以接收
        final Map<Long, List<ApplySampleItemDto>> applySampleItems;
        // 获取所有的下一个专业组信息
        final Map<Long, ProfessionalGroupDto> groupDtoMap;
        if (sortingBloodPreHandover) {
            applySampleItems = Map.of();
            groupDtoMap = groupService.selectByGroupIdsAsMap(ss.stream().flatMap(e -> e.stream().map(SplitBloodApplySampleDto::getNextGroupId)).distinct().collect(Collectors.toList()));
        } else {
            // 获取到检验项目然后获取到哪些专业组可以接收
            applySampleItems = applySampleItemService.selectByApplySampleIds(samples.values().stream().flatMap(Collection::stream)
                            .map(SplitBloodApplySampleDto::getApplySampleId).collect(Collectors.toSet()))
                    .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));
            groupDtoMap = Map.of();
        }

        return ss.stream().map(e -> {
            final SplitBloodHandoverRackVo v = new SplitBloodHandoverRackVo();
            final SplitBloodApplySampleDto dto = e.stream().max(Comparator.comparing(SplitBloodApplySampleDto::getSplitDate))
                    .orElse(e.iterator().next());

            v.setRackLogicId(dto.getRackLogicId());
            v.setRackCode(dto.getRackCode());

            if (sortingBloodPreHandover) {
                List<String> groupNames = Collections.emptyList();
                if (Objects.nonNull(groupDtoMap.get(dto.getNextGroupId()))) {
                    groupNames = List.of(groupDtoMap.get(dto.getNextGroupId()).getGroupName());
                }
                v.setGroupNames(groupNames);
            } else {
                // 获取到检验项目然后获取到哪些专业组可以接收
                final List<ApplySampleItemDto> items = e.stream().map(k -> applySampleItems.getOrDefault(k.getApplySampleId(), Collections.emptyList()))
                        .flatMap(Collection::stream).collect(Collectors.toList());
                v.setGroupNames(items.stream().map(ApplySampleItemDto::getGroupName)
                        .distinct().collect(Collectors.toList()));
            }

            v.setSplitterId(dto.getSplitterId());
            v.setSplitterName(dto.getSplitterName());
            v.setSplitDate(dto.getSplitDate());
            v.setCount(e.size());
            return v;
        }).collect(Collectors.toList());

    }


    /**
     * 分血后交接的试管架
     */
    @GetMapping("/handover-racks/samples")
    public Object handoverRacksSamples(@RequestParam Long rackLogicId) {
        if (Objects.isNull(rackLogicId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<RackLogicApplySampleDto> samples = applySampleService.selectByRackLogicId(rackLogicId);


        final Map<Long, ApplyDto> applies = applyService.selectByApplyIds(samples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));

        final Map<Long, List<ApplySampleItemDto>> applySampleItems = applySampleItemService.selectByApplyIds(applies.keySet())
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        return samples.stream().map(e -> {
            final SplitBloodHandoverRackSampleVo v = new SplitBloodHandoverRackSampleVo();
            BeanUtils.copyProperties(e, v);
            BeanUtils.copyProperties(applies.get(e.getApplyId()), v);

            final List<ApplySampleItemDto> items = applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList());
            v.setTestItemNames(items.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            v.setGroupNames(items.stream().map(ApplySampleItemDto::getGroupName).distinct().collect(Collectors.toList()));

            return v;
        });

    }

    /**
     * 交接
     */
    @PostMapping("/handover")
    public Object handover(@RequestBody SplitBloodHandoverVo vo) {

        final UserDto user = userService.selectByUsername(vo.getUsername());
        if (Objects.isNull(user)) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        // 校验密码
        if (!userService.validPassword(user.getUsername(), user.getPassword(), vo.getPassword())) {
            throw new IllegalArgumentException("工号或密码错误");
        }


        final RackLogicDto rackLogic = rackLogicService.selectByRackLogicId(vo.getRackLogicId());
        if (Objects.isNull(rackLogic)) {
            throw new IllegalArgumentException("试管架不存在");
        }

        if (!userService.containsGroup(user.getUserId(), rackLogic.getNextGroupId())) {
            throw new IllegalStateException(String.format("工号 [%s] 无法接收专业组 [%s] 的试管架", vo.getUsername(), rackLogic.getNextGroupName()));
        }


        if (!Objects.equals(rackLogic.getPosition(), RackLogicPositionEnum.SPLIT_BLOOD.getCode())) {
            throw new IllegalArgumentException("当前试管架不可交接");
        }

        // 查询到试管架下的申请单样本
        final List<RackLogicApplySampleDto> samples = applySampleService.selectByRackLogicId(vo.getRackLogicId());
        if (CollectionUtils.isEmpty(samples)) {
            throw new IllegalArgumentException("逻辑试管架上没有申请单样本");
        }
        final Set<Long> applySampleIdSet = samples.stream()
                .map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());
        // dev-1.1.3.3 获取这个样本要接受的专业组的样本类型上， 更新到申请单样本上，
        // 否则院感组间交接的情况下， 拥有各自分拣页面的检验根据项目类型可能无法查询到该条码
        final Long applySampleId = applySampleIdSet.iterator().next();
        final String itemType = this.genBloodItemType(applySampleId, rackLogic.getNextGroupId());

        final RackLogicDto modifyRackLogic = new RackLogicDto();
        modifyRackLogic.setRackLogicId(vo.getRackLogicId());
        modifyRackLogic.setPosition(RackLogicPositionEnum.TWO_PICKING.getCode());
        modifyRackLogic.setCurrentGroupId(rackLogic.getNextGroupId());
        modifyRackLogic.setCurrentGroupName(rackLogic.getNextGroupName());
        modifyRackLogic.setNextGroupId(NumberUtils.LONG_ZERO);
        modifyRackLogic.setNextGroupName(StringUtils.EMPTY);
        modifyRackLogic.setLastHandover(user.getNickname());

        if (!rackLogicService.updateByRackLogicId(modifyRackLogic)) {
            throw new IllegalArgumentException("分血后交接失败");
        }

        // 获取到试管架占用
        final List<RackLogicSpaceDto> rackLogicSpaces = new ArrayList<>(rackLogicSpaceService.selectByApplySampleIds(applySampleIdSet));
        if (CollectionUtils.isNotEmpty(rackLogicSpaces)) {
            rackLogicSpaces.removeIf(e -> Objects.equals(e.getRackLogicId(), vo.getRackLogicId()));

            // 删除多余的试管架占用
            rackLogicSpaceService.deleteByRackLogicSpaceIds(rackLogicSpaces.stream()
                    .map(RackLogicSpaceDto::getRackLogicSpaceId).collect(Collectors.toSet()));

            final Set<Long> rackLogicIds = rackLogicSpaces.stream().map(RackLogicSpaceDto::getRackLogicId)
                    .collect(Collectors.toSet());

            final Map<Long, List<RackLogicSpaceDto>> map = rackLogicSpaceService.selectByRackLogicIds(rackLogicIds)
                    .stream().collect(Collectors.groupingBy(RackLogicSpaceDto::getRackLogicId));

            // 删除逻辑试管架下没有样本的
            rackLogicService.deleteByRackLogicIds(rackLogicIds.stream().filter(e -> CollectionUtils.isEmpty(map.get(e)))
                    .collect(Collectors.toSet()));

        }

        // 修改专业组
        final ApplySampleDto as = new ApplySampleDto();
        as.setGroupId(rackLogic.getNextGroupId());
        as.setGroupName(rackLogic.getNextGroupName());
        // dev-1.1.3.3 因为委外组也可以组间交接， 所以这里要把外送标记打上， 不是外送的话要把外送标记取消
        Integer isOutSourcing = YesOrNoEnum.selectByBool(groupService.checkIsOutsourcingGroup(rackLogic.getNextGroupId(), LoginUserHandler.get().getOrgId())).getCode();
        as.setIsOutsourcing(isOutSourcing);
        as.setItemType(itemType);
        applySampleService.updateByApplySampleIds(as, applySampleIdSet);

        final LinkedList<Long> ids = snowflakeService.genIds(samples.size());

        // 记录流水
        sampleFlowService.addSampleFlows(samples.stream().map(e -> SampleFlowDto.builder()
                .applyId(e.getApplyId())
                .sampleFlowId(ids.pop())
                .applySampleId(e.getApplySampleId())
                .operateCode(BarcodeFlowEnum.SPLIT_BLOOD_HANDOVER.name())
                .operateName(BarcodeFlowEnum.SPLIT_BLOOD_HANDOVER.getDesc())
                .barcode(e.getBarcode())
                .content(String.format("从 [%s] 接收到 [%s]", rackLogic.getCurrentGroupName(), rackLogic.getNextGroupName()))
                .build()).collect(Collectors.toList()));

        return Collections.emptyMap();


    }

    public String genBloodItemType(long applySampleId, long nextGroupId) {
        return applySampleItemService.selectByApplySampleId(applySampleId).stream()
                // 流程上来说， 该样本的项目肯定有此专业组的项目， 因为该applySample为试管架下的的这个专业组下能做的样本
                .filter(e -> Objects.equals(nextGroupId, e.getGroupId()))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("样本下没有接收专业组的检验项目"))
                .getItemType();
    }


    //==================================================================================================================

    // 通知业务中台样本分血
    private void notifyBusinessSplitBlood(List<ApplySampleDto> samples) {
        if (CollectionUtils.isEmpty(samples)) {
            return;
        }

        LoginUserHandler.User user = LoginUserHandler.get();

        List<String> splitBarcodes = samples.stream().map(e -> e.getBarcode()).filter(p -> p.contains("_")).collect(Collectors.toList());

        // 请求头填充
        LinkedMultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.put("Content-Type", Collections.singletonList("application/json;charset=UTF-8"));
        // body填充
        NotifySplitBloodVo notifySplitBloodVo = new NotifySplitBloodVo();
        notifySplitBloodVo.setSignOrgCode(orgCode);
        notifySplitBloodVo.setSignBarcode(samples.get(0).getBarcode().split("_")[ 0 ]);
        notifySplitBloodVo.setSplitBarcodes(splitBarcodes);
        notifySplitBloodVo.setOptUserId(String.valueOf(user.getUserId()));
        notifySplitBloodVo.setOptUserName(user.getUsername());

        ResponseEntity<Response> stringResponseEntity = restTemplate.postForEntity(notifySplitBloodUrl, new HttpEntity<String>(JSONObject.toJSONString(notifySplitBloodVo), headers), Response.class);
        if (!stringResponseEntity.getStatusCode().is2xxSuccessful()) {
            throw new IllegalStateException("HTTP通知业务中台样本分血失败,HTTP状态码：" + stringResponseEntity.getStatusCode().value());
        }

        if (!stringResponseEntity.getBody().isSuccess()) {
            throw new IllegalStateException("通知业务中台样本分血响应失败，错误编码：" + stringResponseEntity.getBody().getCode() + "，错误信息：" + stringResponseEntity.getBody().getMsg());
        }
    }


}
