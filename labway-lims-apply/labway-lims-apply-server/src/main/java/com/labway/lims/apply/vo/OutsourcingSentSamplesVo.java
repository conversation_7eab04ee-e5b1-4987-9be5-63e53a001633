package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 外送样本
 */
@Getter
@Setter
public class OutsourcingSentSamplesVo {
    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 检验项目
     */
    private List<String> testItemNames;



    /**
     * 就诊类型编码，申请单类型
     */
    private String applyTypeCode;

    /**
     * 就诊类型名称
     */
    private String applyTypeName;

    /**
     * 外送机构
     */
    private String exportOrgName;


    /**
     * 外送机构
     */
    private Long exportOrgId;

    /**
     * 迪安条码
     */
    private String exportBarcode;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 外送分拣日期
     */
    private Date outsourcingPickDate;

    /**
     * 签收日期
     */
    private Date createDate;

    /**
     * 是否已打印清单，1:是，0:不是
     * @see YesOrNoEnum
     */
    private Integer isPrintList;

    /**
     * 外部条码号
     */
    private String outBarcode;

    /**
     * 外送状态  false未外送，  true 外送
     *
     * @see YesOrNoEnum
     */
    private Boolean outSourcing = false;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 检验方法
     */
    private String testMethod;

    /**
     * 报告项目
     */
    private String reportItem;
}
