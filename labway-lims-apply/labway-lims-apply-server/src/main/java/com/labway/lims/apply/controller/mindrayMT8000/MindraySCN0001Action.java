package com.labway.lims.apply.controller.mindrayMT8000;

import cn.hutool.core.date.DateUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.apply.vo.MT8000HandleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分拣
 */
@Slf4j
@Component
class MindraySCN0001Action implements ActionStrategy, InitializingBean {


    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object action(MT8000HandleVo vo) throws Exception {
        // 条码号
        final String barcode = vo.getExtras().getString("barcode");
        // 仪器编码
        final String instrumentCode = vo.getExtras().getString("instrumentCode");
        // 专业组编码
        final String groupCode = vo.getExtras().getString("groupCode");
        // 上机时间
        Date optTime = vo.getExtras().getDate("optTime");
        // 上机节点名称
        String testInstrumentName = vo.getExtras().getString("testInstrumentName");
        // 检验模块序列号
        String testModeCode = vo.getExtras().getString("testModeCode");
        // 检验模块名称
        String testModeName = vo.getExtras().getString("testModeName");
        // 试管架编码
        String rack = vo.getExtras().getString("rack");
        // 试管架位置
        String position = vo.getExtras().getString("position");



        final List<ApplySampleDto> applySamples = new ArrayList<>(applySampleService.selectByBarcode(barcode));
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException(String.format("申请单样本 条码号 [%s] 不存在", barcode));
        }


        // 记录流水
        sampleFlowService.addSampleFlows(
                        applySamples.stream()
                                .map(e -> SampleFlowDto.builder()
                                        .applyId(e.getApplyId())
                                        .sampleFlowId(snowflakeService.genId())
                                        .applySampleId(e.getApplySampleId())
                                        .operateCode(BarcodeFlowEnum.MT8000SCN0001.name())
                                        .operateName(BarcodeFlowEnum.MT8000SCN0001.getDesc())
                                        .operator(LoginUserHandler.get().getNickname())
                                        .operatorId(LoginUserHandler.get().getUserId())
                                        .barcode(e.getBarcode())
                                        .content(String.format("样本上机 上机时间[%s] 节点名称[%s] 检验模块序列号[%s] 检验模块名称 [%s] 位置[%s]",
                                                DateUtil.formatDateTime(optTime), testInstrumentName, testModeCode, testModeName, rack+"^"+position))
                                        .build())
                                .collect(Collectors.toList()));

        return Map.of();
    }


    @Override
    public MT8000HandleVo.Action action() {
        return MT8000HandleVo.Action.SCN0001;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
    }

}
