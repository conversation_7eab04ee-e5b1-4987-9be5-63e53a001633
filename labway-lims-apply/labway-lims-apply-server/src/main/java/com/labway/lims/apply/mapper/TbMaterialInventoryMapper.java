package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.model.TbMaterialInventory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 物料盘点 Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Mapper
public interface TbMaterialInventoryMapper extends BaseMapper<TbMaterialInventory> {

    /**
     * 批量 插入
     */
    void batchAddMaterialInventory(@Param("conditions") List<TbMaterialInventory> conditions);

}
