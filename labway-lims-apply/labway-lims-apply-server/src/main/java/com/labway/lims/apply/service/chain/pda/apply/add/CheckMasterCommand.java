package com.labway.lims.apply.service.chain.pda.apply.add;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.service.chain.apply.add.AddApplyContext;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class CheckMasterCommand implements Command {

    @Resource
    private PdaApplyService pdaApplyService;
    @Resource
    private ApplyService applyService;

    @Override
    public boolean execute(Context context) throws Exception {
        AddApplyContext from = AddApplyContext.from(context);
        final TestApplyDto testApply = from.getTestApply();
        LoginUserHandler.User user = from.getUser();
        String masterBarcode = testApply.getMasterBarcode();
        List<PdaApplyDto> pdaApplyDtos = pdaApplyService.selectByMasterBarcode(masterBarcode);
        if (CollectionUtils.isNotEmpty(pdaApplyDtos) && pdaApplyDtos.size() >= 2) {
            throw new IllegalStateException("该主条码已完成双输补录");
        }

        List<PdaApplyDto> userPdaApplyDto = pdaApplyDtos.stream()
                .filter(e -> Objects.equals(e.getCreatorId(), user.getUserId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userPdaApplyDto)) {
            throw new IllegalStateException("您已录入过该主条码信息");
        }

        final List<ApplyDto> applyDtos = applyService.selectByMasterBarcodes(Set.of(masterBarcode));
        if (CollectionUtils.isNotEmpty(applyDtos)) {
            throw new IllegalStateException("该主条码已在申请单中存在");
        }

        from.put(AddApplyContext.PDA_APPLYS, pdaApplyDtos);
        from.put(AddApplyContext.SKIP_MASTER_BARCODE, Boolean.TRUE);
        return CONTINUE_PROCESSING;
    }
}
