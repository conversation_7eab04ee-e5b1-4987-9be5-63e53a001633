package com.labway.lims.apply.service.chain.pick.one.cancel;

import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.enums.RackStatusEnum;
import com.labway.lims.base.api.service.RackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 删除试管架关联
 */
@Slf4j
@Component
class CancelOnePickRemoveSpaceCommand implements Command, Filter {

    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private RackLogicService rackLogicService;

    @DubboReference
    private RackService rackService;

    @Override
    public boolean execute(Context c) throws Exception {

        final CancelOnePickContext context = CancelOnePickContext.from(c);

        final List<RackLogicSpaceDto> rackLogicSpaces = rackLogicSpaceService.selectByApplySampleId(context.getApplySampleId());
        if (CollectionUtils.isEmpty(rackLogicSpaces)) {
            return CONTINUE_PROCESSING;
        }

        final Set<Long> rackLogicIds = rackLogicSpaces.stream()
                .map(RackLogicSpaceDto::getRackLogicId).collect(Collectors.toSet());

        // 删除试管架关联
        rackLogicSpaceService.deleteByApplySampleId(context.getApplySampleId());

        // 如果试管架下面的空间空了，因为此时试管架状态是一次交接中。那么逻辑试管架直接删除，并且试管架自动回收
        if (rackLogicSpaceService.countByRackLogicIds(rackLogicIds) < 1) {

            // 删除逻辑试管架
            rackLogicService.deleteByRackLogicIds(rackLogicIds);

            final Set<Long> rackIds = rackLogicSpaces.stream()
                    .map(RackLogicSpaceDto::getRackId).collect(Collectors.toSet());

            // 设置为空闲状态
            for (Long rackId : rackIds) {
                final RackDto rack = new RackDto();
                rack.setRackId(rackId);
                rack.setStatus(RackStatusEnum.IDLE.getCode());
                rackService.updateByRackId(rack);
            }
        }

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
