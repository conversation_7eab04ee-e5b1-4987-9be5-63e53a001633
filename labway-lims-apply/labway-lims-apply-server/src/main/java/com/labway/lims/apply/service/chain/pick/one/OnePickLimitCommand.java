package com.labway.lims.apply.service.chain.pick.one;

import com.labway.lims.api.RedisPrefix;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 限制
 */
@Slf4j
@Component
class OnePickLimitCommand implements Command, Filter {
    private static final String MARK = OnePickLimitCommand.class.getName();

    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context c) throws Exception {

        final OnePickContext context = OnePickContext.from(c);

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(getMark(context.getApplySampleId()),
                StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalStateException("正在一次分拣中");
        }

        context.put(MARK, StringUtils.EMPTY);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        final OnePickContext context = OnePickContext.from(c);
        if (context.containsKey(MARK)) {
            stringRedisTemplate.delete(getMark(context.getApplySampleId()));
        }
        return CONTINUE_PROCESSING;
    }

    private String getMark(long applySampleId){
        return redisPrefix.getBasePrefix() + MARK + applySampleId;
    }
}
