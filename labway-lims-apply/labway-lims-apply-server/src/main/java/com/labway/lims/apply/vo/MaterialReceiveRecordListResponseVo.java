package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.MaterialReceiveRecordStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 物料领用记录信息
 * 
 * <AUTHOR>
 * @since 2023/5/9 17:21
 */
@Getter
@Setter
public class MaterialReceiveRecordListResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 领用ID
     */
    private Long receiveId;

    /**
     * 库存ID
     */
    private Long inventoryId;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物资编号
     */
    private String materialCode;

    /**
     * 物资名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 厂家
     */
    private String manufacturers;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 领用主单位数量
     */
    private BigDecimal receiveMainNumber;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     * 领用辅单位数量
     */
    private BigDecimal receiveAssistNumber;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date validDate;

    /**
     * 领用人ID
     */
    private Long receiverId;

    /**
     * 领用人姓名
     */
    private String receiverName;

    /**
     * 领用时间
     */
    private Date receiverDate;

    /**
     * 领用状态:0已作废,1已领用
     *
     * @see MaterialReceiveRecordStatusEnum
     */
    private Integer status;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 作废人ID
     */
    private Long invalidUserId;

    /**
     * 作废人姓名
     */
    private String invalidUserName;

    /**
     * 作废时间
     */
    private Date invalidDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

    /**
     * 状态描述
     */
    private String statusDesc;
    /**
     * 物料条码号
     */
    private String materialBarcode;
}
