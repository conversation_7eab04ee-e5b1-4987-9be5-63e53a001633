package com.labway.lims.apply.service.chain.splitblood.info;

import com.labway.lims.apply.service.chain.splitblood.CheckCanSplitCommand;
import com.labway.lims.apply.service.chain.splitblood.CheckParamsCommand;
import com.labway.lims.apply.service.chain.splitblood.SplitBloodCheckApplySampleStatusCommand;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 分血责任链
 */
@Service
public class SplitBloodInfoChain extends ChainBase implements InitializingBean {
    @Resource
    private CheckParamsCommand checkParamsCommand;

    @Resource
    private CheckCanSplitCommand checkCanSplitCommand;

    @Resource
    private SplitBloodInfoCommand splitBloodInfoCommand;

    @Resource
    private SplitBloodCheckApplySampleStatusCommand splitBloodCheckApplySampleStatusCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 判断是否已停止或已终止
        addCommand(splitBloodCheckApplySampleStatusCommand);

        // 参数校验
        addCommand(checkParamsCommand);

        // 校验是否可以分血
        addCommand(checkCanSplitCommand);

        // 复制、分血
        addCommand(splitBloodInfoCommand);

        // 完成
        addCommand(context -> true);
    }
}