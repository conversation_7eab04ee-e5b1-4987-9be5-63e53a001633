package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.apply.api.dto.ApplyLogisticsSampleItemDto;
import com.labway.lims.apply.api.service.ApplyLogisticsSampleItemService;
import com.labway.lims.apply.mapper.TbApplyLogisticsSampleItemMapper;
import com.labway.lims.apply.model.TbApplyLogisticsSampleItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@DubboService
public class ApplyLogisticsSampleItemServiceImpl implements ApplyLogisticsSampleItemService {

    @Resource
    private TbApplyLogisticsSampleItemMapper tbApplyLogisticsSampleItemMapper;

    @Override
    public List<ApplyLogisticsSampleItemDto> selectByApplyLogisticsId(long applyLogisticsId) {
        final LambdaQueryWrapper<TbApplyLogisticsSampleItem> eq = Wrappers.lambdaQuery(TbApplyLogisticsSampleItem.class).eq(TbApplyLogisticsSampleItem::getApplyLogisticsId, applyLogisticsId);
        return tbApplyLogisticsSampleItemMapper.selectList(eq).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<ApplyLogisticsSampleItemDto> selectByApplyLogisticsSampleId(long applyLogisticsSampleId) {
        final LambdaQueryWrapper<TbApplyLogisticsSampleItem> eq = Wrappers.lambdaQuery(TbApplyLogisticsSampleItem.class).eq(TbApplyLogisticsSampleItem::getApplyLogisticsSampleId, applyLogisticsSampleId);
        return tbApplyLogisticsSampleItemMapper.selectList(eq).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public void add(ApplyLogisticsSampleItemDto applyLogisticsSampleItem) {
        final TbApplyLogisticsSampleItem object = JSON.parseObject(JSON.toJSONString(applyLogisticsSampleItem), TbApplyLogisticsSampleItem.class);
        tbApplyLogisticsSampleItemMapper.insert(object);
    }

    @Override
    public void addBatch(List<ApplyLogisticsSampleItemDto> sampleItems) {
        if (CollectionUtils.isEmpty(sampleItems)) {
            return;
        }

        for (ApplyLogisticsSampleItemDto sampleItem : sampleItems) {
            add(sampleItem);
        }
    }

    /**
     * 将 TbApplyLogisticsSampleItem 转换为 ApplyLogisticsSampleItemDto
     */
    private ApplyLogisticsSampleItemDto convert(TbApplyLogisticsSampleItem tb) {
        return JSON.parseObject(JSON.toJSONString(tb), ApplyLogisticsSampleItemDto.class);
    }
}
