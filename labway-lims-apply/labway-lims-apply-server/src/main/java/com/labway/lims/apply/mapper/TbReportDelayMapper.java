/*
 * @ClassName TbReportDelayMapper
 * @Description 
 * @version 1.0
 * @Date 2023-12-15 14:28:49
 */
package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.model.TbReportDelay;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;


@Mapper
public interface TbReportDelayMapper  extends BaseMapper<TbReportDelay> {


    void updateByIds(@Param("delayIds") Set<Long> delayIds, @Param("status") Integer status,@Param("cancelUserId") Long cancelUserId, @Param("cancelUserName") String cancelUserName);
}