package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.MaterialDeliveryRecordStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 物料出库记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Getter
@Setter
@TableName("tb_material_delivery_record")
public class TbMaterialDeliveryRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId
    private Long recordId;

    /**
     * 出库单号
     */
    private String deliveryNo;

    /**
     * 专业组物料申领id
     */
    private Long applyId;

    /**
     * 出库日期
     */
    private Date deliveryDate;

    /**
     * 出库人
     */
    private String deliveryUser;

    /**
     * 是否已入库:0未入库,1已入库
     *
     * @see MaterialDeliveryRecordStatusEnum
     */
    private Integer status;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     * 
     * @see YesOrNoEnum
     */
    private Integer isDelete;

}
