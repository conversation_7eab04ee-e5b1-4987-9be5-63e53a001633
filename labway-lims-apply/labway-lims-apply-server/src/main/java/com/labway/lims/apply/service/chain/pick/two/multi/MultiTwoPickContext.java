package com.labway.lims.apply.service.chain.pick.two.multi;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@SuppressWarnings("unchecked")
public class MultiTwoPickContext extends StopWatchContext {

    /**
     * 逻辑试管架
     */
    static final String RACK_LOGIC = "RACK_LOGIC_" + IdUtil.objectId();

    /**
     * 逻辑试管架位置
     */
    static final String RACK_LOGIC_SPACES = "RACK_LOGIC_SPACES_" + IdUtil.objectId();

    /**
     * 所有申请单样本
     */
    static final String APPLY_SAMPLES = "APPLY_SAMPLES_" + IdUtil.objectId();

    /**
     * 所有申请单
     */
    static final String APPLIES = "APPLY_" + IdUtil.objectId();

    /**
     * 所有申请单样本的项目
     */
    static final String APPLY_SAMPLE_ITEMS = "APPLY_SAMPLE_ITEMS" + IdUtil.objectId();

    /**
     * 要分拣到哪个专业小组
     */
    static final String INSTRUMENT_GROUPS = "INSTRUMENT_GROUPS_" + IdUtil.objectId();

    /**
     * 申请单样本二次分拣后的信息
     */
    static final String APPLY_SAMPLES_PICK_INFO = "APPLY_SAMPLES_PICK_INFO_" + IdUtil.objectId();

    /**
     * 整个链条过程中是否遇到过错误 ， 因为批量分拣的时候遇到错误也会往下执行。
     */
    static final String EXCEPTION = "EXCEPTION_" + IdUtil.objectId();


    private final long rackLogicId;

    private Long instrumentId;

    public MultiTwoPickContext(long rackLogicId) {
        this.rackLogicId = rackLogicId;
    }

    public static MultiTwoPickContext from(Context context) {
        return (MultiTwoPickContext) context;
    }

    public RackLogicDto getRackLogic() {
        return (RackLogicDto) get(RACK_LOGIC);
    }

    public List<RackLogicSpaceDto> getRackLogicSpaces() {
        return (List<RackLogicSpaceDto>) get(RACK_LOGIC_SPACES);
    }

    public List<ApplySampleDto> getApplySamples() {
        return (List<ApplySampleDto>) get(APPLY_SAMPLES);
    }

    /**
     * key : 申请单样本ID
     */
    public Map<Long, List<ApplySampleItemDto>> getApplySampleItems() {
        return (Map<Long, List<ApplySampleItemDto>>) get(APPLY_SAMPLE_ITEMS);
    }

    /**
     * key ： 申请单样本ID
     */
    public Map<Long, InstrumentGroupDto> getInstrumentGroups() {
        return (Map<Long, InstrumentGroupDto>) get(INSTRUMENT_GROUPS);
    }


    /**
     * key ： 申请单ID
     */
    public Map<Long, ApplyDto> getApplies() {
        return (Map<Long, ApplyDto>) get(APPLIES);
    }

    @Nullable
    public LimsCodeException getException() {
        return (LimsCodeException) get(EXCEPTION);
    }

    /**
     * key ： 申请单样本ID
     */
    public List<ApplySampleTwoPickDto> getApplySampleTwoPicks() {
        return (List<ApplySampleTwoPickDto>) get(APPLY_SAMPLES_PICK_INFO);
    }

    @Override
    protected String getWatcherName() {
        return "批量二次分拣";
    }

}
