package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.business.center.mdm.api.reagent.dto.SysDictDto;
import com.labway.business.center.mdm.api.reagent.service.SysDictDubboService;
import com.labway.business.center.mdm.api.user.request.QueryDictRequest;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleAbnormalStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.dto.SampleAbnormalRegisterRequestDto;
import com.labway.lims.apply.api.dto.SampleAbnormalStatisticsDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SelectSampleAbnormalDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.mapper.TbSampleAbnormalMapper;
import com.labway.lims.apply.mapstruct.SampleAbnormalConverter;
import com.labway.lims.apply.model.TbSampleAbnormal;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.swak.frame.dto.Response;
import com.swak.frame.enums.BasicErrCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 样本异常 Service impl
 *
 * <AUTHOR>
 * @since 2023/4/12 11:18
 */
@Slf4j
@DubboService
public class SampleAbnormalServiceImpl implements SampleAbnormalService {

    @Resource
    private TbSampleAbnormalMapper tbSampleAbnormalMapper;
    @Resource
    private SampleAbnormalConverter sampleAbnormalConverter;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private SampleFlowService sampleFlowService;

    @Resource
    private SysDictDubboService sysDictDubboService;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public List<Long> sampleAbnormalRegister(SampleAbnormalRegisterRequestDto requestDto) {
        HspOrganizationDto hspOrganizationDto = hspOrganizationService.selectByHspOrgId(requestDto.getHspOrgId());
        if (Objects.isNull(hspOrganizationDto)) {
            throw new LimsException("送检机构不存在");
        }

        List<SysDictDto> abnormalReasonList = getAbnormalReasonList();
        List<SysDictDto> dictDtoList = ObjectUtils.defaultIfNull(abnormalReasonList, Collections.emptyList());

        List<SysDictDto> dictItemDtos = dictDtoList.stream()
                .filter(obj -> Arrays.asList(requestDto.getAbnormalReasonCode().split(",")).contains(obj.getDictCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(dictItemDtos)) {
            throw new LimsException("异常原因不存在");
        }

        // 处理部门 key 部门id value 部门名称
        final List<ProfessionalGroupDto> professionalGroupDtos =
                groupService.selectByGroupIds(requestDto.getHandleGroupIdList());
        Map<Long, String> handleGroupIdAndName = professionalGroupDtos.stream()
                .collect(Collectors.toMap(ProfessionalGroupDto::getGroupId, ProfessionalGroupDto::getGroupName));
        if (requestDto.getHandleGroupIdList().stream().anyMatch(x -> !handleGroupIdAndName.containsKey(x))) {
            throw new IllegalArgumentException("存在无效专业组");
        }

        Long applyId = NumberUtils.LONG_ZERO;
        if (StringUtils.isNotBlank(requestDto.getBarcode())) {
            // 条码号存在
            // 对应所有申请单样本 只可能为同一个 申请单
            List<ApplySampleDto> applySampleDtos = applySampleService.selectByBarcode(requestDto.getBarcode());
            if (CollectionUtils.isEmpty(applySampleDtos)) {
                throw new LimsException("无效条码号");
            }
            // 申请单id
            applyId = applySampleDtos.get(NumberUtils.INTEGER_ZERO).getApplyId();
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        Date date = new Date();
        List<SampleAbnormalDto> targetList = Lists.newArrayList();
        final LinkedList<Long> ids = snowflakeService.genIds(handleGroupIdAndName.entrySet().size());
        for (Map.Entry<Long, String> entry : handleGroupIdAndName.entrySet()) {
            Long groupId = entry.getKey();
            String groupName = entry.getValue();
            SampleAbnormalDto target = new SampleAbnormalDto();
            target.setSampleAbnormalId(ids.pop());
            target.setBarcode(StringUtils.defaultString(requestDto.getBarcode()));
            target.setApplyId(applyId);
            target.setHspOrgId(hspOrganizationDto.getHspOrgId());
            target.setHspOrgName(hspOrganizationDto.getHspOrgName());
            target.setTestItemName(StringUtils.defaultString(requestDto.getTestItemName()));
            target.setSendDoctorName(StringUtils.defaultString(requestDto.getSendDoctorName()));
            target.setPatientName(StringUtils.defaultString(requestDto.getPatientName()));
            target.setPatientSex(ObjectUtils.defaultIfNull(requestDto.getPatientSex(), SexEnum.DEFAULT.getCode()));
            target.setPatientAge(StringUtils.defaultString(requestDto.getPatientAge()));
            target.setRegistGroupId(loginUser.getGroupId());
            target.setRegistGroupName(loginUser.getGroupName());
            target.setRegistDate(new Date());
            target.setRegisterId(loginUser.getUserId());
            target.setRegisterName(loginUser.getNickname());
            target.setAbnormalReasonCode(dictItemDtos.stream().map(SysDictDto::getDictCode).collect(Collectors.joining(StringPool.COMMA)));
            target.setAbnormalReasonName(dictItemDtos.stream().map(SysDictDto::getDictValue).collect(Collectors.joining(StringPool.COMMA)));
            target.setRegistContent(requestDto.getRegistContent());
            target.setImages(requestDto.getImages());
            target.setHandleGroupId(groupId);
            target.setHandleGroupName(groupName);
            target.setHandleUserId(NumberUtils.LONG_ZERO);
            target.setHandleUserName(StringUtils.EMPTY);
            target.setHandleContent(StringUtils.EMPTY);
            target.setHandleDate(DefaultDateEnum.DEFAULT_DATE.getDate());
            target.setConfirmGroupId(NumberUtils.LONG_ZERO);
            target.setConfirmGroupName(StringUtils.EMPTY);
            target.setConfirmUserId(NumberUtils.LONG_ZERO);
            target.setConfirmUserName(StringUtils.EMPTY);
            target.setConfirmContent(StringUtils.EMPTY);
            target.setConfirmDate(DefaultDateEnum.DEFAULT_DATE.getDate());
            target.setStatus(SampleAbnormalStatusEnum.UNPROCESSED.getCode());
            target.setIsDelete(YesOrNoEnum.NO.getCode());
            target.setCreateDate(date);
            target.setUpdateDate(date);

            target.setCreatorId(loginUser.getUserId());
            target.setCreatorName(loginUser.getNickname());
            target.setUpdaterId(loginUser.getUserId());
            target.setUpdaterName(loginUser.getNickname());
            target.setOrgId(loginUser.getOrgId());
            target.setOrgName(loginUser.getOrgName());


            targetList.add(target);
        }

        ((SampleAbnormalService) AopContext.currentProxy()).addSampleAbnormals(targetList);

        // 记录条码环节
        String content = BarcodeFlowEnum.SAMPLE_ABNORMAL_REGISTER.getDesc() +
                "，异常原因：" + dictItemDtos.stream().map(SysDictDto::getDictValue).collect(Collectors.joining(StringPool.COMMA)) +
                "，处理部门：" + String.join(StringPool.COMMA, handleGroupIdAndName.values());
        addSampleFlow(List.of(targetList.get(0)), BarcodeFlowEnum.SAMPLE_ABNORMAL_REGISTER, content, loginUser);

        return targetList.stream().map(SampleAbnormalDto::getSampleAbnormalId).collect(Collectors.toList());
    }

    @Override
    public List<SysDictDto> getAbnormalReasonList() {
        QueryDictRequest request = new QueryDictRequest();
        request.setEnabled(NumberUtils.INTEGER_ONE);
        Response<List<SysDictDto>> response;
        try {
            response = sysDictDubboService.listAllEReason(request);
        } catch (Exception e) {
            throw new LimsException("业务中台-获取异常原因返回错误", e);
        }

        if (Objects.isNull(response)) {
            throw new LimsException("业务中台-获取异常原因返回结果为空");
        }

        if (!Objects.equals(response.getCode(), BasicErrCode.SUCCESS.getCode())) {
            throw new LimsException(String.format("业务中台-获取异常原因返回错误, 错误信息: [%s]", response.getMsg()));
        }
        return response.getData();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSampleAbnormals(List<SampleAbnormalDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 异常
        List<TbSampleAbnormal> targetList = list.stream()
            .map(obj -> JSON.parseObject(JSON.toJSONString(obj), TbSampleAbnormal.class)).collect(Collectors.toList());
        // 数量 分区批次插入
        List<List<TbSampleAbnormal>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbSampleAbnormalMapper.batchAddTbSampleAbnormals(item));

        log.info("用户 [{}] 新增异常[{}]成功", loginUser.getNickname(), JSON.toJSONString(targetList));

    }

    @Override
    public List<SampleAbnormalDto> selectBySelectSampleAbnormalDto(SelectSampleAbnormalDto dto) {
        LambdaQueryWrapper<TbSampleAbnormal> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(Objects.nonNull(dto.getRegistDateStart()), TbSampleAbnormal::getRegistDate,
            dto.getRegistDateStart());
        queryWrapper.le(Objects.nonNull(dto.getRegistDateEnd()), TbSampleAbnormal::getRegistDate,
            dto.getRegistDateEnd());
        queryWrapper.eq(TbSampleAbnormal::getOrgId, dto.getOrgId());
        queryWrapper.eq(TbSampleAbnormal::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbSampleAbnormal::getRegistDate);
        return sampleAbnormalConverter.sampleAbnormalDtoListFromTbObj(tbSampleAbnormalMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public SampleAbnormalDto selectBySampleAbnormalId(long sampleAbnormalId) {
        if (sampleAbnormalId < 1) {
            return null;
        }
        LambdaQueryWrapper<TbSampleAbnormal> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSampleAbnormal::getSampleAbnormalId, sampleAbnormalId);
        queryWrapper.eq(TbSampleAbnormal::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return sampleAbnormalConverter.sampleAbnormalDtoFromTbObj(tbSampleAbnormalMapper.selectOne(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBySampleAbnormalId(SampleAbnormalDto sampleAbnormalDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbSampleAbnormal target = new TbSampleAbnormal();
        BeanUtils.copyProperties(sampleAbnormalDto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbSampleAbnormalMapper.updateById(target) < 1) {
            throw new LimsException("修改异常失败");
        }

        log.info("用户 [{}] 修改异常成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBySampleAbnormalIds(SampleAbnormalDto sampleAbnormalDto, Collection<Long> sampleAbnormalId) {
        if (CollectionUtils.isEmpty(sampleAbnormalId)) {
            return;
        }
        tbSampleAbnormalMapper.updateBySampleAbnormalIds(sampleAbnormalDto, sampleAbnormalId);
    }

    @Override
    public List<SampleAbnormalDto> selectBySampleAbnormalIds(Collection<Long> sampleAbnormalIds) {
        if (CollectionUtils.isEmpty(sampleAbnormalIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSampleAbnormal> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSampleAbnormal::getSampleAbnormalId, sampleAbnormalIds);
        queryWrapper.eq(TbSampleAbnormal::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbSampleAbnormal::getRegistDate);
        return sampleAbnormalConverter.sampleAbnormalDtoListFromTbObj(tbSampleAbnormalMapper.selectList(queryWrapper));
    }

    @Override
    public List<SampleAbnormalStatisticsDto> selectSampleAbnormalStatistics(Date beginSignDate, Date endSignDate,
        Long orgId) {
        return tbSampleAbnormalMapper.selectSampleAbnormalStatistics(beginSignDate, endSignDate, orgId);

    }

    @Override
    public List<SampleAbnormalStatisticsDto> selectSampleAbnormalStatisticsBySendDate(Date beginSendDate, Date endSendDate, Long orgId) {
        return tbSampleAbnormalMapper.selectSampleAbnormalStatisticsBySendDate(beginSendDate, endSendDate, orgId);
    }

    @Override
    public List<SampleAbnormalDto> selectByGroupId(long groupId) {
        LambdaQueryWrapper<TbSampleAbnormal> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.and(qr -> qr.eq(TbSampleAbnormal::getHandleGroupId, groupId).or()
            .eq(TbSampleAbnormal::getConfirmGroupId, groupId));
        queryWrapper.eq(TbSampleAbnormal::getIsDelete, YesOrNoEnum.NO.getCode());
        return sampleAbnormalConverter.sampleAbnormalDtoListFromTbObj(tbSampleAbnormalMapper.selectList(queryWrapper));
    }

    @Override
    public void updateByApplyId(SampleAbnormalDto sampleAbnormalDto) {
        LambdaUpdateWrapper<TbSampleAbnormal> wrapper = Wrappers.lambdaUpdate(TbSampleAbnormal.class)
                .eq(TbSampleAbnormal::getApplyId, sampleAbnormalDto.getApplyId())
                .eq(TbSampleAbnormal::getIsDelete,0)
                .set(TbSampleAbnormal::getHspOrgId, sampleAbnormalDto.getHspOrgId())
                .set(TbSampleAbnormal::getHspOrgName,sampleAbnormalDto.getHspOrgName())
                .set(TbSampleAbnormal::getUpdaterId,sampleAbnormalDto.getUpdaterId())
                .set(TbSampleAbnormal::getUpdaterName,sampleAbnormalDto.getUpdaterName())
                .set(TbSampleAbnormal::getUpdateDate,sampleAbnormalDto.getUpdateDate());
       tbSampleAbnormalMapper.update(null, wrapper);
    }

    @Override
    public void updateByApplyIds(SampleAbnormalDto sampleAbnormalDto, Collection<Long> applyIds) {
        for (List<Long> item : Lists.partition(new ArrayList<>(applyIds), 500)) {
            LambdaUpdateWrapper<TbSampleAbnormal> wrapper = Wrappers.lambdaUpdate(TbSampleAbnormal.class)
                .in(TbSampleAbnormal::getApplyId, item).eq(TbSampleAbnormal::getIsDelete, 0)
                .set(TbSampleAbnormal::getHspOrgId, sampleAbnormalDto.getHspOrgId())
                .set(TbSampleAbnormal::getHspOrgName, sampleAbnormalDto.getHspOrgName())
                .set(TbSampleAbnormal::getUpdaterId, sampleAbnormalDto.getUpdaterId())
                .set(TbSampleAbnormal::getUpdaterName, sampleAbnormalDto.getUpdaterName())
                .set(TbSampleAbnormal::getUpdateDate, sampleAbnormalDto.getUpdateDate());
            tbSampleAbnormalMapper.update(null, wrapper);
        }
    }

    @Async
    @Override
    public void addSampleFlow(List<SampleAbnormalDto> sampleAbnormalDtos, BarcodeFlowEnum barcodeFlow,
                              String content, LoginUserHandler.User user) {
        if (CollectionUtils.isEmpty(sampleAbnormalDtos)) {
            return;
        }

        LoginUserHandler.set(user);

        try {
            Set<String> barcodes = sampleAbnormalDtos.stream().map(SampleAbnormalDto::getBarcode).collect(Collectors.toSet());
            Map<String, ApplySampleDto> applySampleMapByBarcode = applySampleService.selectByBarcodes(barcodes)
                    .stream().collect(Collectors.toMap(ApplySampleDto::getBarcode, Function.identity(), (a, b) -> a));

            final LinkedList<SampleFlowDto> flows = new LinkedList<>();

            final LinkedList<Long> ids = snowflakeService.genIds(sampleAbnormalDtos.size());
            for (SampleAbnormalDto sample : sampleAbnormalDtos) {
                final SampleFlowDto sampleFlow = new SampleFlowDto();
                sampleFlow.setSampleFlowId(ids.pop());
                sampleFlow.setApplyId(sample.getApplyId());
                sampleFlow.setApplySampleId(Optional.ofNullable(applySampleMapByBarcode.get(sample.getBarcode()))
                        .map(ApplySampleDto::getApplySampleId).orElse(NumberUtils.LONG_ZERO));
                sampleFlow.setBarcode(sample.getBarcode());
                sampleFlow.setOperator(user.getNickname());
                sampleFlow.setOperatorId(user.getUserId());
                sampleFlow.setContent(content);
                sampleFlow.setOperateCode(barcodeFlow.name());
                sampleFlow.setOperateName(barcodeFlow.getDesc());
                sampleFlow.setIsDelete(YesOrNoEnum.NO.getCode());
                sampleFlow.setCreateDate(new Date());
                sampleFlow.setCreatorId(user.getUserId());
                sampleFlow.setCreatorName(user.getNickname());
                sampleFlow.setUpdateDate(new Date());
                sampleFlow.setUpdaterId(user.getUserId());
                sampleFlow.setUpdaterName(user.getNickname());
                flows.add(sampleFlow);
            }

            sampleFlowService.addSampleFlows(flows);
        } catch (Exception e) {
            log.error("样本异常登记，记录条码环节异常 {} {} {} {}", user.getNickname(), barcodeFlow,
                    content, JSON.toJSONString(sampleAbnormalDtos), e);
        }
    }

    @Override
    public List<SampleAbnormalDto> selectByBarcodes(List<String> barcodes) {
        if (CollectionUtils.isNotEmpty(barcodes)){
            LambdaQueryWrapper<TbSampleAbnormal> queryWrapper = Wrappers.lambdaQuery();
            if (barcodes.size() > 1){
                queryWrapper.in(TbSampleAbnormal::getBarcode, barcodes);
            }else {
                queryWrapper.eq(TbSampleAbnormal::getBarcode, barcodes.get(0));
            }
            queryWrapper.eq(TbSampleAbnormal::getIsDelete, YesOrNoEnum.NO.getCode());
            //去除已作废的
            queryWrapper.ne(TbSampleAbnormal::getStatus, SampleAbnormalStatusEnum.ABANDONED.getCode());
            queryWrapper.orderByDesc(TbSampleAbnormal::getRegistDate);
            return sampleAbnormalConverter.sampleAbnormalDtoListFromTbObj(tbSampleAbnormalMapper.selectList(queryWrapper));
        }

        return Collections.emptyList();
    }

}
