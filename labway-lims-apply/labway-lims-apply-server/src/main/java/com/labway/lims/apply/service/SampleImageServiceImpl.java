package com.labway.lims.apply.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.api.service.SampleImageService;
import com.labway.lims.apply.mapper.TbSampleImageMapper;
import com.labway.lims.apply.mapstruct.SampleImageConverter;
import com.labway.lims.apply.model.TbSampleImage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * SampleImageServiceImpl
 * 样本图片
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/29 16:55
 */
@Slf4j
@DubboService(interfaceClass = SampleImageService.class)
public class SampleImageServiceImpl implements SampleImageService {

    @Resource
    private TbSampleImageMapper sampleImageMapper;
    @Resource
    private SampleImageConverter sampleImageConverter;

    /**
     * 添加图片
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSampleImage(List<SampleImageDto> sampleImageDtos) {
        if (CollectionUtils.isEmpty(sampleImageDtos)) {
            return;
        }

        sampleImageMapper.insertBatch(sampleImageDtos);
    }

    /**
     * 删除图片
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSampleImage(Long sampleImageId) {
        return sampleImageMapper.deleteById(sampleImageId) < 1;
    }

    /**
     * 根据样本ID删除图片
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSampleImageBySampleIds(Set<Long> sampleIds) {
        return sampleImageMapper.delete(new LambdaQueryWrapper<TbSampleImage>().in(TbSampleImage::getSampleId, sampleIds)) < 1;
    }

    /**
     * 根据ID查询图片
     */
    @Override
    public SampleImageDto selectSampleImageById(Long sampleImageId) {
        return sampleImageConverter.convert(sampleImageMapper.selectById(sampleImageId));
    }

    /**
     * 查询图片
     */
    @Override
    public List<SampleImageDto> selectSampleImageBySampleId(Long sampleId) {
        final LambdaQueryWrapper<TbSampleImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleImage::getSampleId, sampleId);
        wrapper.orderByAsc(TbSampleImage::getCreateDate);
        final List<TbSampleImage> tbSampleImages = sampleImageMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(tbSampleImages)) {
            return Collections.emptyList();
        }
        return tbSampleImages.stream().map(sampleImageConverter::convert).collect(Collectors.toList());
    }

    /**
     * 查询图片
     */
    @Override
    public List<SampleImageDto> selectSampleImageByApplySampleId(Long applySampleId) {
        final LambdaQueryWrapper<TbSampleImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbSampleImage::getApplySampleId, applySampleId);
        final List<TbSampleImage> tbSampleImages = sampleImageMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(tbSampleImages)) {
            return Collections.emptyList();
        }
        return tbSampleImages.stream().map(sampleImageConverter::convert).collect(Collectors.toList());
    }

    @Override
    public void deleteByImageNames(Collection<String> imageNames, long sampleId) {
        if (CollectionUtils.isEmpty(imageNames)) {
            return;
        }

        sampleImageMapper.delete(new LambdaQueryWrapper<TbSampleImage>()
                .in(TbSampleImage::getImageName, imageNames)
                .eq(TbSampleImage::getSampleId, sampleId));
    }

}
