package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.bo.OnePickWaitingHandoverRackLogicBo;
import com.labway.lims.apply.model.TbRackLogic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 逻辑试管架 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbRackLogicMapper extends BaseMapper<TbRackLogic> {

    List<OnePickWaitingHandoverRackLogicBo> selectOnePickWaitingHandoverRackLogics(@Param("beginOnePickDate") Date beginOnePickDate,
                                                                                   @Param("endOnePickDate") Date endOnePickDate,
                                                                                   @Param("groupId") long groupId);

    /**
     * 根据申请单样本ID查询
     */
    List<RackLogicDto> selectByApplySampleId(@Param("applySampleId") long applySampleId);
}
