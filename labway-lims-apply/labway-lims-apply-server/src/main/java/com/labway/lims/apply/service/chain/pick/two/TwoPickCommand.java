package com.labway.lims.apply.service.chain.pick.two;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LabwayDateUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.PositiveMicrobiologyTwoPickDto;
import com.labway.lims.apply.api.dto.RocheTwoPickDto;
import com.labway.lims.apply.api.dto.SampleTwoPickDto;
import com.labway.lims.apply.api.dto.SampleTwoUnPickInfoDto;
import com.labway.lims.apply.api.service.ISampleTwoPicker;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.genetics.api.service.GeneticsSampleTwoPickerService;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.specialty.api.service.SpecialtySampleTwoPickerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Date;
import java.util.EnumMap;
import java.util.EventListener;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 分拣
 */
@Slf4j
@Component
public class TwoPickCommand implements Command, InitializingBean, Filter {

    /**
     * 是否已经加锁
     */
    static final String LOCK_INSTRUMENT_GROUP = "LOCKED_INSTRUMENT_GROUP_" + IdUtil.objectId();

    /**
     * 是否已经解锁
     */
    static final String UNLOCK_INSTRUMENT_GROUP = "UNLOCK_INSTRUMENT_GROUP_" + IdUtil.objectId();

    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private GeneticsSampleTwoPickerService geneticsSampleTwoPickerService;
    @DubboReference
    private SpecialtySampleTwoPickerService specialtySampleTwoPickerService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private BloodCultureSampleService bloodCultureSampleService;
    /**
     * 二次分拣
     */
    private final Map<ItemTypeEnum, ISampleTwoPicker> pickers = new EnumMap<>(ItemTypeEnum.class);
    @Resource
    private TwoPickRedisMarkCommand twoPickRedisMarkCommand;

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);

        final InstrumentGroupDto instrumentGroup = context.getInstrumentGroup();
        final ApplySampleDto applySample = context.getApplySample();

        for (ApplySampleItemDto applySampleItem : context.getApplySampleItems()) {
            if (Objects.isNull(getSampleTwoPicker(applySampleItem.getItemType()))) {
                throw new IllegalStateException("无法二次分拣此样本，因为无法识别项目类型");
            }
        }

        // 锁住专业小组
        lockInstrumentGroup(context, instrumentGroup.getInstrumentGroupId());

        try {

            for (ApplySampleTwoPickDto applySampleTwoPick : context.getApplySampleTwoPicks()) {

                final StopWatch watch = new StopWatch(String.format("条码 [%s] 二次分拣", applySample.getBarcode()));
                final SampleTwoPickDto stp = new SampleTwoPickDto();
                stp.setSampleNo(applySampleTwoPick.getSampleNo());
                stp.setTwoPickDate(context.getTwoPickDate());
                if (Objects.nonNull(applySampleTwoPick.getImmunityTwoPickDate())) {
                    // 免疫二次分拣时间
                    stp.setImmunityTwoPickDate(LabwayDateUtil.toDate(applySampleTwoPick.getImmunityTwoPickDate()));

                    // 如果是加急的，那么免疫二次分拣时间和分拣时间要设置成当前时间
                    if (applySampleTwoPick.getIsUrgent()) {
                        stp.setTwoPickDate(stp.getImmunityTwoPickDate());
                    }
                }

                // 获取到分拣器
                final String itemType = context.getApplySampleItems().stream()
                        .filter(e -> Objects.equals(e.getGroupId(), applySampleTwoPick.getGroupId())).findFirst()
                        .map(ApplySampleItemDto::getItemType).orElse(null);

                final ISampleTwoPicker picker = getSampleTwoPicker(itemType);

                // 如果样本号为空，那么生成样本号
                if (StringUtils.isBlank(stp.getSampleNo())) {
                    watch.start("生成样本号");
                    // 样本号生成，如果是免疫二次分拣，分拣时间可能不是当天，要根据具体的免疫分拣时间生成样本号
                    stp.setSampleNo(genSampleNo(instrumentGroup, ObjectUtils.defaultIfNull(stp.getImmunityTwoPickDate(), context.getTwoPickDate())));
                    watch.stop();

                    // 如果是自动生成的则判断是否超出
                    if (NumberUtils.toLong(stp.getSampleNo()) > NumberUtils
                            .toLong(instrumentGroup.getSampleEndValue())) {
                        throw new IllegalStateException("样本号超出范围");
                    }

                    if (NumberUtils.toLong(stp.getSampleNo()) < NumberUtils
                            .toLong(instrumentGroup.getSampleStartValue())) {
                        throw new IllegalStateException("样本号超出范围");
                    }
                }

                watch.start("判断这个样本号是否可以使用");
                // 血培养检验的 “标记阳性” 一定会重复，这里跳过检验
                if (!(context.getTwoPick() instanceof PositiveMicrobiologyTwoPickDto)) {
                    // 判断这个样本号是否可以使用
                    if (!canActiveSampleNo(instrumentGroup.getGroupId(), stp.getSampleNo(),
                            ObjectUtils.defaultIfNull(stp.getImmunityTwoPickDate(), context.getTwoPickDate()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate())) {
                        throw new IllegalStateException(String.format("样本号 [%s] 已存在", stp.getSampleNo()));
                    }
                }
                watch.stop();

                stp.setApplySampleId(applySampleTwoPick.getApplySampleId());
                stp.setInstrumentGroupId(applySampleTwoPick.getInstrumentGroupId());
                stp.setGroupId(applySampleTwoPick.getGroupId());
                stp.setInstrumentId(context.getTwoPick().getInstrumentId());

                if (context.getTwoPick() instanceof RocheTwoPickDto) {
                    stp.setInstrumentId(((RocheTwoPickDto) context.getTwoPick()).getInstrumentId());
                }

                watch.start("分拣");

                final long sampleId = picker.twoPick(stp);

                watch.stop();

                log.info("用户 [{}] 二次分拣了条码 [{}] 专业组 [{}] 专业小组 [{}] 信息 [{}] 样本ID [{}] 耗时 \n{}",
                        LoginUserHandler.get().getNickname(), applySample.getBarcode(), instrumentGroup.getGroupName(),
                        instrumentGroup.getInstrumentGroupName(), JSON.toJSONString(stp), sampleId,
                        watch.prettyPrint(TimeUnit.MILLISECONDS));

                context.getTwoPick().setSampleNo(stp.getSampleNo());
                applySampleTwoPick.setSampleNo(stp.getSampleNo());
                applySampleTwoPick.setItemType(itemType);
                // 微生物二次分拣后,需要显示对应专业小组的颜色
                applySampleTwoPick.setSecondSortColor(instrumentGroup.getSecondSortColor());

                context.getListenerList().add(PostprocessCallback.class, exception -> {
                    if (Objects.isNull(exception)) {
                        return;
                    }
                    picker.twoUnPick(List.of(applySample.getApplySampleId()));
                });
            }

        } finally {
            unlockInstrumentGroup(context, instrumentGroup.getInstrumentGroupId());
        }

        return CONTINUE_PROCESSING;
    }

    private interface PostprocessCallback extends EventListener {
        void postprocess(Exception exception);
    }

    public ISampleTwoPicker getSampleTwoPicker(String itemTypeCode) {
        if (StringUtils.isBlank(itemTypeCode)) {
            throw new IllegalStateException(String.format("项目类型 [%s] 错误", itemTypeCode));
        }

        final ItemTypeEnum itemType = ItemTypeEnum.getByName(itemTypeCode);
        if (Objects.isNull(itemType)) {
            throw new IllegalStateException(String.format("项目类型 [%s] 错误", itemTypeCode));
        }

        final ISampleTwoPicker picker = pickers.get(itemType);
        if (Objects.isNull(picker)) {
            throw new IllegalStateException(String.format("无法处理 [%s] 项目类型", itemTypeCode));
        }

        return picker;
    }

    public void lockInstrumentGroup(TwoPickContext context, long instrumentGroupId) throws InterruptedException {
        if (context.containsKey(LOCK_INSTRUMENT_GROUP)) {
            return;
        }

        final long timestamp = System.currentTimeMillis();
        final Duration timeout = Duration.ofSeconds(30);

        do {
            // 锁专业小组
            if (BooleanUtils.isTrue(stringRedisTemplate.opsForValue()
                    .setIfAbsent(getLockInstrumentGroupKey(instrumentGroupId), StringUtils.EMPTY, timeout))) {
                return;
            }

            Thread.yield();

            // 尝试加锁
            synchronized (this) {
                wait(20);
            }

        } while (System.currentTimeMillis() - timestamp < timeout.toMillis());

        throw new IllegalStateException("专业组小组正在分拣中，请稍后再试");

    }

    public void unlockInstrumentGroup(TwoPickContext context, long instrumentGroupId) {
        if (context.containsKey(UNLOCK_INSTRUMENT_GROUP)) {
            return;
        }

        stringRedisTemplate.delete(getLockInstrumentGroupKey(instrumentGroupId));

    }

    /**
     * 生成一个样本号
     */
    public String genSampleNo(InstrumentGroupDto instrumentGroup, Date twoPickDate) {

        final Set<String> activeSampleNos = getActiveSampleNos(instrumentGroup.getGroupId(),
                twoPickDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());

        final long sampleStartValue = NumberUtils.toLong(instrumentGroup.getSampleStartValue());
        final long sampleEndValue = NumberUtils.toLong(instrumentGroup.getSampleEndValue());

        for (long i = sampleStartValue; i <= sampleEndValue; i++) {
            final String s = padSampleNo(instrumentGroup, String.valueOf(i));
            if (activeSampleNos.contains(s)) {
                continue;
            }
            return s;
        }

        log.warn("专业小组编码 [{}] 专业小组名称 [{}] 样本号 [{} - {}] 已无可用样本号", instrumentGroup.getInstrumentGroupCode(),
                instrumentGroup.getInstrumentGroupName(), sampleStartValue, sampleEndValue);

        throw new IllegalStateException(String.format("专业小组 [%s] 无可用样本号", instrumentGroup.getInstrumentGroupName()));
    }

    /**
     * 补齐 样本号
     */
    private String padSampleNo(InstrumentGroupDto instrumentGroup, String sampleNo) {
        // 填充样本号
        if (sampleNo.length() < instrumentGroup.getSampleStartValue().length()) {
            sampleNo = StringUtils.left(instrumentGroup.getSampleStartValue(),
                    instrumentGroup.getSampleStartValue().length() - sampleNo.length()) + sampleNo;
        }
        return sampleNo;
    }

    /**
     * 激活一个样本号，表示这个样本号当天已经被使用
     */
    public void activeSampleNo(long groupId, String sampleNo, LocalDate date) {
        stringRedisTemplate.opsForSet().add(getSampleNoHistoryKey(groupId, date), sampleNo);
    }


    /**
     * 释放一个样本号
     */
    public void passiveSampleNo(long groupId, String sampleNo, LocalDate date) {
        stringRedisTemplate.opsForSet().remove(getSampleNoHistoryKey(groupId, date), sampleNo);
    }

    /**
     * 是否可以使用这个样本号
     */
    public boolean canActiveSampleNo(long groupId, String sampleNo, LocalDate date) {
        return BooleanUtils
                .isFalse(stringRedisTemplate.opsForSet().isMember(getSampleNoHistoryKey(groupId, date), sampleNo));
    }

    Set<String> getActiveSampleNos(long groupId, LocalDate date) {
        return stringRedisTemplate.opsForSet().members(getSampleNoHistoryKey(groupId, date));
    }

    String getSampleNoHistoryKey(long groupId, LocalDate date) {
        return redisPrefix.getBasePrefix() + "TWO_PICK:" + date + ":" + groupId + ":SampleNoHistory";
    }

    public String getLockInstrumentGroupKey(long instrumentGroupId) {
        return redisPrefix.getBasePrefix() + "TWO_PICK_LOCK:" + instrumentGroupId;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 常规检验
        pickers.put(ItemTypeEnum.ROUTINE, new TwoPickerFace(new ISampleTwoPicker() {
            @Override
            public long twoPick(SampleTwoPickDto stp) {
                return sampleService.twoPick(stp.getApplySampleId(),
                        stp.getInstrumentGroupId(), stp.getSampleNo(),
                        ObjectUtils.defaultIfNull(stp.getInstrumentId(), 0L), stp.getImmunityTwoPickDate());
            }

            @Override
            public SampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
                return JSON.parseObject(JSON.toJSONString(sampleService.twoUnPick(applySampleIds)),
                        SampleTwoUnPickInfoDto.class);
            }
        }));

        // 外送检验
        pickers.put(ItemTypeEnum.OUTSOURCING, new TwoPickerFace(new ISampleTwoPicker() {
            @Override
            public long twoPick(SampleTwoPickDto stp) {
                return outsourcingSampleService.twoPick(stp.getApplySampleId(), stp.getInstrumentGroupId(),
                        stp.getSampleNo());
            }

            @Override
            public SampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
                return JSON.parseObject(JSON.toJSONString(outsourcingSampleService.twoUnPick(applySampleIds)),
                        SampleTwoUnPickInfoDto.class);
            }
        }));

        // 遗传检验
        pickers.put(ItemTypeEnum.GENETICS, new TwoPickerFace(new ISampleTwoPicker() {
            @Override
            public long twoPick(SampleTwoPickDto stp) {
                return geneticsSampleTwoPickerService.twoPick(stp.getApplySampleId(), stp.getInstrumentGroupId(),
                        stp.getSampleNo());
            }

            @Override
            public SampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
                return JSON.parseObject(JSON.toJSONString(geneticsSampleTwoPickerService.twoUnPick(applySampleIds)),
                        SampleTwoUnPickInfoDto.class);
            }

        }));

        // 特检
        pickers.put(ItemTypeEnum.SPECIALTY, new TwoPickerFace(new ISampleTwoPicker() {
            @Override
            public long twoPick(SampleTwoPickDto stp) {
                return specialtySampleTwoPickerService.twoPick(stp.getApplySampleId(), stp.getInstrumentGroupId(),
                        stp.getSampleNo());
            }

            @Override
            public SampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
                return JSON.parseObject(JSON.toJSONString(specialtySampleTwoPickerService.twoUnPick(applySampleIds)),
                        SampleTwoUnPickInfoDto.class);
            }

        }));

        // 微生物
        pickers.put(ItemTypeEnum.MICROBIOLOGY, new TwoPickerFace(new ISampleTwoPicker() {
            @Override
            public long twoPick(SampleTwoPickDto stp) {
                return microbiologySampleService.twoPick(stp.getApplySampleId(), stp.getInstrumentGroupId(),
                        stp.getSampleNo(), stp.getTwoPickDate());
            }

            @Override
            public SampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
                return JSON.parseObject(JSON.toJSONString(microbiologySampleService.twoUnPick(applySampleIds)),
                        SampleTwoUnPickInfoDto.class);
            }

        }));

        // 院感
        pickers.put(ItemTypeEnum.INFECTION, new TwoPickerFace(new ISampleTwoPicker() {
            @Override
            public long twoPick(SampleTwoPickDto stp) {
                return infectionSampleService.twoPick(stp.getApplySampleId(), stp.getInstrumentGroupId(),
                        stp.getSampleNo(), stp.getTwoPickDate());
            }

            @Override
            public SampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
                return JSON.parseObject(JSON.toJSONString(infectionSampleService.twoUnPick(applySampleIds)),
                        SampleTwoUnPickInfoDto.class);
            }

        }));


        // 血培养
        pickers.put(ItemTypeEnum.BLOOD_CULTURE, new TwoPickerFace(new ISampleTwoPicker() {
            @Override
            public long twoPick(SampleTwoPickDto stp) {
                return bloodCultureSampleService.twoPick(stp.getApplySampleId(), stp.getInstrumentGroupId(),
                        stp.getSampleNo());
            }

            @Override
            public SampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
                return JSON.parseObject(JSON.toJSONString(bloodCultureSampleService.twoUnPick(applySampleIds)),
                        SampleTwoUnPickInfoDto.class);
            }

        }));


    }

    @Override
    public boolean postprocess(Context c, Exception exception) {

        for (PostprocessCallback listener : TwoPickContext.from(c).getListenerList()
                .getListeners(PostprocessCallback.class)) {
            try {
                listener.postprocess(exception);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        return CONTINUE_PROCESSING;
    }

    private class TwoPickerFace implements ISampleTwoPicker {

        private final ISampleTwoPicker picker;

        public TwoPickerFace(ISampleTwoPicker picker) {
            this.picker = picker;
        }

        @Override
        public long twoPick(SampleTwoPickDto stp) {

            final String key = redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":" + stp.getApplySampleId();
            if (BooleanUtils.isNotTrue(
                    stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofSeconds(10)))) {
                throw new IllegalStateException("正在二次分拣中");
            }

            try {

                final long sampleId = picker.twoPick(stp);

                // 标记这个样本号已经被使用
                activeSampleNo(stp.getGroupId(), stp.getSampleNo(),
                        ObjectUtils.defaultIfNull(stp.getImmunityTwoPickDate(), stp.getTwoPickDate()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate());

                return sampleId;
            } finally {
                stringRedisTemplate.delete(key);
            }
        }

        @Override
        public SampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {

            if (CollectionUtils.isEmpty(applySampleIds)) {
                throw new IllegalArgumentException("applySampleIds is empty");
            }

            final SampleTwoUnPickInfoDto stup = picker.twoUnPick(applySampleIds);

            // 释放样本
            stup.getSamples().forEach(e -> passiveSampleNo(e.getGroupId(), e.getSampleNo(),
                    e.getTwoPickDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()));

            // 删除 redis 二次分拣 标记
            applySampleIds.forEach(twoPickRedisMarkCommand::unmark);

            return stup;
        }

    }
}
