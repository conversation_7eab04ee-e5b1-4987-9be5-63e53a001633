package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 待分血后交接的试管架列表
 */
@Getter
@Setter
public class HandoverRacksRequestVo {
    /**
     * 开始分血时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginSplitDate;

    /**
     * 结束分血时间
     */

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSplitDate;
}
