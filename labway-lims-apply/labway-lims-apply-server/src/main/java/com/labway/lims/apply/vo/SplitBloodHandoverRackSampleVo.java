package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 分血后交接的试管架下的样本
 */
@Getter
@Setter
public class SplitBloodHandoverRackSampleVo {

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 检验项目
     */
    private List<String> testItemNames;

    /**
     * 专业组
     */
    private List<String> groupNames;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 管型
     */
    private String tubeName;

    /**
     * 病人名称
     */
    private String patientName;


    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;


    /**
     * 性别  性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

}
