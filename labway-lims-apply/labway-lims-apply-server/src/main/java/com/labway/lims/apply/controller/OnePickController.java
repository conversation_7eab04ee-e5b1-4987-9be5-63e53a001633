package com.labway.lims.apply.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.RackTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplySampleOnePickDto;
import com.labway.lims.apply.api.dto.NotOnePickApplySampleDto;
import com.labway.lims.apply.api.dto.OnePickRackLogicInfoDto;
import com.labway.lims.apply.api.dto.OnePickedApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.WaitingOnePickApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.service.chain.pick.one.OnePickChooseGroupCommand;
import com.labway.lims.apply.vo.OnePickHandoverVo;
import com.labway.lims.apply.vo.OnePickOrgsVo;
import com.labway.lims.apply.vo.OnePickRackLogicInfoVo;
import com.labway.lims.apply.vo.OnePickRackVo;
import com.labway.lims.apply.vo.OnePickRacksSamplesVo;
import com.labway.lims.apply.vo.OnePickSamplesVo;
import com.labway.lims.apply.vo.PrintPdfOnePickSamplesVo;
import com.labway.lims.apply.vo.SampleRollbackVo;
import com.labway.lims.apply.vo.UnloadSampleRequestVo;
import com.labway.lims.apply.vo.UnloadSampleVo;
import com.labway.lims.apply.vo.UseRackVo;
import com.labway.lims.apply.vo.UsedRackVo;
import com.labway.lims.base.api.dto.BarcodeSettingDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.enums.BarcodeSettingEnum;
import com.labway.lims.base.api.enums.RackStatusEnum;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.RackService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 一次分拣
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping("/one-pick")
public class OnePickController extends BaseController {

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private RackLogicService rackLogicService;
    @DubboReference
    private RackService rackService;
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private UserService userService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private GroupService getGroupService;
    @Resource
    private Environment environment;
    @DubboReference
    private SystemParamService systemParamService;

    @DubboReference
    private PdfReportService pdfReportService;

    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ThreadPoolConfig threadPoolConfig;


    /**
     * 一次分拣
     */
    @PostMapping("/pick")
    @Transactional(rollbackFor = Exception.class)
    public Object pick(@RequestParam String barcode) {

        // 添加幂等校验
        String key = redisPrefix.getBasePrefix() + barcode;
        if (!Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(key, barcode, Duration.ofSeconds(2)))) {
            throw new IllegalArgumentException("请勿重复扫码，请稍后再试！");
        }


        final List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException("条码不存在");
        }

        // 第一次分拣的时候，只能有一个
        if (applySamples.size() != 1) {
            throw new IllegalArgumentException("条码数量错误或已经分拣");
        }

        final Long applySampleId = applySamples.iterator().next().getApplySampleId();
        applySampleService.assertApplySampleUsability(applySampleId);

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getIsOnePick(), YesOrNoEnum.YES.getCode()))) {
            ApplySampleDto applySampleDto = applySamples.get(NumberUtils.INTEGER_ZERO);
            RackDto rackDto = rackService.selectByRackId(applySampleDto.getRackId());
            if (Objects.isNull(rackDto)) {
                stringRedisTemplate.expire(key,1, TimeUnit.MILLISECONDS);
                throw new IllegalStateException("样本分拣试管架不存在");
            }
            // dev-1.1.3.3 获取逻辑试管架进行异常返回
            final List<RackLogicSpaceDto> rackLogicSpaceDtos = rackLogicSpaceService.selectByApplySampleId(applySampleDto.getApplySampleId());
            if (CollectionUtils.isNotEmpty(rackLogicSpaceDtos)) {
                final RackLogicSpaceDto rackLogicSpaceDto = rackLogicSpaceDtos.iterator().next();
                throw new IllegalStateException(
                        String.format("该条码已分拣至[%s]的[%s][%s]行[%s]列", applySampleDto.getGroupName(), rackDto.getRackCode(),
                                rackLogicSpaceDto.getRow() + 1, rackLogicSpaceDto.getColumn() + 1));
            }
            throw new IllegalStateException(
                    String.format("该条码已分拣至[%s]的[%s]试管架", applySampleDto.getGroupName(), rackDto.getRackCode()));
        }

        try {
            ApplySampleOnePickDto applySampleOnePickDto = applySampleService.onePick(applySamples.iterator().next().getApplySampleId());

            this.removeBarcodeCache(applySamples.get(0));

            return applySampleOnePickDto;
        } catch (LimsCodeException e) {
            stringRedisTemplate.expire(key,1, TimeUnit.MILLISECONDS);
            // 如果没有逻辑试管架 那么判断是否有可用试管架
            if (e.getCode() == OnePickChooseGroupCommand.NO_RACK_LOGIC
                    && (rackService.countByStatus(RackStatusEnum.IDLE.getCode(), LoginUserHandler.get().getOrgId()) < 1)) {
                throw new IllegalStateException("已无空闲试管架，请回收后再分拣");
            }
            throw e;
        }

    }

    /**
     * 删除条码规则生成的条码和主条码
     */
    private void removeBarcodeCache(ApplySampleDto applySampleDto) {
        threadPoolConfig.getPool().submit(() -> {
            final String barcodeKey = String.format(BarcodeSettingEnum.BARCODE_CACHE.getRedisKey(), redisPrefix.getBasePrefix(), BarcodeSettingDto.BARCODE_TYPE, applySampleDto.getHspOrgCode());
            final String masterBarcodeKey = String.format(BarcodeSettingEnum.BARCODE_CACHE.getRedisKey(), redisPrefix.getBasePrefix(), BarcodeSettingDto.MASTER_BARCODE_TYPE, applySampleDto.getHspOrgCode());

            ApplyDto applyDto = applyService.selectByApplyId(applySampleDto.getApplyId());

            stringRedisTemplate.opsForSet().remove(barcodeKey, applySampleDto.getBarcode());
            if (Objects.nonNull(applyDto)) {
                stringRedisTemplate.opsForSet().remove(masterBarcodeKey, applyDto.getMasterBarcode());
            }
        });
    }

    /**
     * 取消一次分拣
     */
    @PostMapping("/unpick")
    public Object unpick(@RequestBody Set<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("参数错误");
        }

        for (Long applySampleId : applySampleIds) {
            applySampleService.cancelOnePick(applySampleId);
        }

        return Collections.emptyMap();

    }

    /**
     * 分拣且未下架样本
     */
    @GetMapping("/samples")
    public List<OnePickSamplesVo> samples(Long rackLogicId) {
        final List<RackLogicDto> rackLogics = rackLogicService.selectByCreatorIdAndPosition(
                LoginUserHandler.get().getUserId(), RackLogicPositionEnum.ONE_PICKING.getCode());

        final List<RackLogicApplySampleDto> list = new ArrayList<>(applySampleService
                .selectByRackLogicIds(rackLogics.stream().map(RackLogicDto::getRackLogicId).collect(Collectors.toSet())));

        // 根据一次分拣日期排序
        list.sort(Comparator.comparing(ApplySampleDto::getOnePickDate));

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        if (Objects.nonNull(rackLogicId)) {
            list.removeIf(e -> !Objects.equals(e.getRackLogicId(), rackLogicId));
        }

        final Map<Long, ApplyDto> applies =
                applyService.selectByApplyIds(list.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet()))
                        .stream().collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));

        final Map<Long, List<ApplySampleItemDto>> applySampleItems =
                applySampleItemService.selectByApplyIds(applies.keySet()).stream()
                        .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        return list.stream().map(e -> {
            final OnePickSamplesVo v = new OnePickSamplesVo();
            BeanUtils.copyProperties(applies.get(e.getApplyId()), v);
            BeanUtils.copyProperties(e, v);

            v.setTestItemNames(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList()).stream()
                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            return v;
        }).collect(Collectors.toList());
    }

    /**
     * 机构下面待一次分拣统计
     */
    @PostMapping("/orgs")
    public Object orgs(@RequestBody OnePickOrgsVo vo) {
        if (Objects.isNull(vo.getBeginSignDate()) || Objects.isNull(vo.getEndSignDate())) {
            throw new IllegalArgumentException("参数错误");
        }

        // 获取到所有没有 一次分拣 的样本
        final List<WaitingOnePickApplySampleDto> samples =
                applySampleService.selectWaitingOnePickSamples(vo.getBeginSignDate(), vo.getEndSignDate());

        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        // 根据送检机构分组
        final Map<Long, List<WaitingOnePickApplySampleDto>> hspSamples =
                samples.stream().collect(Collectors.groupingBy(WaitingOnePickApplySampleDto::getHspOrgId));

        final Map<Long, Long> hspTimes = new LinkedHashMap<>();
        for (var e : hspSamples.values()) {
            for (var w : e) {
                final Long time = hspTimes.computeIfAbsent(w.getHspOrgId(), k -> NumberUtils.LONG_ZERO);
                hspTimes.put(w.getHspOrgId(), time + w.getCreateDate().getTime());
            }
        }

        final List<Long> hspIds = hspTimes.entrySet().stream().sorted(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey).collect(Collectors.toList());

        return hspIds.stream().map(e -> {
            final WaitingOnePickApplySampleDto sample = hspSamples.get(e).iterator().next();
            return Map.of("hspOrgId", e, "hspOrgName", StringUtils.defaultString(sample.getHspOrgName()), "count",
                    hspSamples.get(e).size());
        }).collect(Collectors.toList());

    }

    /**
     * 查询机构下面未一次分拣的样本数据
     */
    @PostMapping("/select-not-one-pick-apply-samples")
    public Object selectNotOnePickApplySamples(@RequestBody OnePickOrgsVo vo) {
        if (Objects.isNull(vo.getBeginSignDate()) || Objects.isNull(vo.getEndSignDate())
                || Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalArgumentException("参数错误");
        }

        // 获取到所有没有 一次分拣 的样本
        List<WaitingOnePickApplySampleDto> samples =
                applySampleService.selectWaitingOnePickSamples(vo.getBeginSignDate(), vo.getEndSignDate());

        if (CollectionUtils.isEmpty(samples)) {
            return Map.of("records", Collections.emptyList(), "totalCount", 0);
        }

        List<WaitingOnePickApplySampleDto> waitingOnePickApplySampleDtos =
                samples.stream()
                        .sorted(Comparator.comparing(WaitingOnePickApplySampleDto::getCreateDate))
                        .filter(e -> vo.getHspOrgId().equals(e.getHspOrgId()))
                        .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(waitingOnePickApplySampleDtos)) {
            return Map.of("records", Collections.emptyList(), "totalCount", 0);
        }

        List<ApplyDto> applyDtos = applyService.selectByApplyIds(
                waitingOnePickApplySampleDtos.stream().map(WaitingOnePickApplySampleDto::getApplyId).collect(Collectors.toList()));

        Map<Long, ApplyDto> applyDtoMap = applyDtos.stream()
                .collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity(), (a, b) -> a));

        List<NotOnePickApplySampleDto> records = waitingOnePickApplySampleDtos.stream().map(e -> {
            ApplyDto applyDto = applyDtoMap.get(e.getApplyId());
            if (Objects.isNull(applyDto)) {
                throw new IllegalStateException("没有找到申请单信息");
            }
            NotOnePickApplySampleDto result = new NotOnePickApplySampleDto();
            // 送检机构
            result.setHspOrgName(e.getHspOrgName());
            // 急诊状态
            result.setUrgent(UrgentEnum.getUrgentEnum(e.getUrgent()).getValue());
            // 条码号
            result.setBarcode(e.getBarcode());
            // 患者姓名
            result.setPatientName(applyDto.getPatientName());
            // 性别
            result.setPatientSex(SexEnum.getByCode(applyDto.getPatientSex()).getDesc());
            // 年龄
            result.setPatientAge(PatientAges.toText(applyDto));
            // 样本类型
            result.setSampleTypeName(e.getSampleTypeName());
            // 管型
            result.setTubeName(e.getTubeName());
            // 就诊类型
            result.setApplyTypeName(applyDto.getApplyTypeName());
            // 临床诊断
            result.setDiagnosis(applyDto.getDiagnosis());
            // 送检时间
            result.setApplyDate(applyDto.getApplyDate());
            // 外部条码号
            result.setOutBarcode(e.getOutBarcode());
            // 主条码号
            result.setMasterBarcode(applyDto.getMasterBarcode());
            return result;
        }).collect(Collectors.toList());
        return Map.of("records", records, "totalCount", records.size());
    }

    /**
     * 已经下架的样本
     */
    @PostMapping("/unload-samples")
    public Object unloadSamples(@RequestBody UnloadSampleRequestVo vo) {

        final List<OnePickedApplySampleDto> applySamples =
                applySampleService.selectOnePickedApplySamples(vo.getBeginOnePickDate(), vo.getEndOnePickDate());

        Set<Long> distinct = new HashSet<>();
        final List<OnePickedApplySampleDto> afterOnePickedSamples =
                applySampleService.selectAfterOnePickedSamples(vo.getBeginOnePickDate(), vo.getEndOnePickDate());
        afterOnePickedSamples.sort(Comparator.comparing(OnePickedApplySampleDto::getOperateDate, Comparator.reverseOrder()));
        applySamples.addAll(afterOnePickedSamples.stream().filter(e -> distinct.add(e.getApplySampleId())).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(applySamples)) {
            return Collections.emptyList();
        }

        final Map<Long,
                ApplyDto> applies = applyService
                .selectByApplyIds(applySamples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));
        if (MapUtils.isEmpty(applies)) {
            return Collections.emptyList();
        }

        final List<ApplySampleItemDto> items = applySampleItemService
                .selectByApplyIds(applySamples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet()));

        final List<UnloadSampleVo> list = new ArrayList<>();

        for (OnePickedApplySampleDto applySample : applySamples) {

            final ApplyDto apply = applies.get(applySample.getApplyId());
            if (Objects.isNull(apply)) {
                continue;
            }

            final UnloadSampleVo usv = new UnloadSampleVo();
            BeanUtils.copyProperties(applySample, usv);

            usv.setTestItemNames(
                    items.stream().filter(e -> Objects.equals(e.getApplySampleId(), applySample.getApplySampleId()))
                            .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            usv.setHspOrgName(apply.getHspOrgName());
            usv.setPatientName(apply.getPatientName());
            usv.setPatientAge(apply.getPatientAge());
            usv.setPatientSubage(apply.getPatientSubage());
            usv.setPatientSubageUnit(apply.getPatientSubageUnit());
            usv.setPatientSex(apply.getPatientSex());

            list.add(usv);

        }
        list.sort(Comparator.comparing(UnloadSampleVo::getOnePickDate));
        return list;
    }

    /**
     * 使用试管架
     */
    @PostMapping("/use-rack")
    public Object useRack(@RequestBody UseRackVo vo) {

        if (Objects.isNull(vo.getRackId()) || Objects.isNull(vo.getGroupId())) {
            throw new IllegalArgumentException("参数错误");
        }

        final RackDto rack = rackService.selectByRackId(vo.getRackId());
        if (Objects.isNull(rack)) {
            throw new IllegalArgumentException("试管架不存在");
        }

        if (Objects.equals(rack.getStatus(), RackStatusEnum.ACTIVE.getCode())) {
            throw new IllegalArgumentException("试管架已被使用");
        }

        final ProfessionalGroupDto group = groupService.selectByGroupId(vo.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException("专业组不存在");
        }

        if (Objects
                .nonNull(rackLogicService.selectAvailableRackLogic(vo.getGroupId(), LoginUserHandler.get().getUserId()))) {
            throw new IllegalArgumentException("请勿重复使用试管架");

        }

        final RackLogicDto rackLogic = new RackLogicDto();
        rackLogic.setRackId(vo.getRackId());
        rackLogic.setRackCode(rack.getRackCode());
        rackLogic.setRow(rack.getRow());
        rackLogic.setColumn(rack.getColumn());
        rackLogic.setPosition(RackLogicPositionEnum.ONE_PICKING.getCode());
        rackLogic.setCurrentGroupId(LoginUserHandler.get().getGroupId());
        rackLogic.setCurrentGroupName(LoginUserHandler.get().getGroupName());
        rackLogic.setNextGroupId(vo.getGroupId());
        rackLogic.setNextGroupName(group.getGroupName());
        rackLogic.setLastHandover(LoginUserHandler.get().getNickname());

        // 修改是试管架的占用
        final RackDto mr = new RackDto();
        mr.setRackId(rack.getRackId());
        mr.setStatus(RackStatusEnum.ACTIVE.getCode());
        if (!rackService.updateByRackId(mr)) {
            throw new IllegalStateException("修改试管架占用失败");
        }

        return Map.of("rackLogicId", rackLogicService.addRackLogic(rackLogic));
    }

    /**
     * 使用中的试管架
     */
    @GetMapping("/used-racks")
    public Object usedRacks() {
        final List<RackLogicDto> rackLogics =
                rackLogicService.selectByCreatorIdAndPosition(LoginUserHandler.get().getUserId(), RackLogicPositionEnum.ONE_PICKING.getCode());
        if (CollectionUtils.isEmpty(rackLogics)) {
            return Collections.emptyList();
        }

        final List<RackLogicSpaceDto> rackLogicSpaces = rackLogicSpaceService
                .selectByRackLogicIds(rackLogics.stream().map(RackLogicDto::getRackLogicId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(rackLogicSpaces)) {
            return Collections.emptyList();
        }

        return rackLogics.stream().map(e -> {
            final UsedRackVo vo = new UsedRackVo();
            vo.setRackId(e.getRackId());
            vo.setRackCode(e.getRackCode());
            vo.setRackLogicId(e.getRackLogicId());
            vo.setGroupId(e.getNextGroupId());
            vo.setGroupName(e.getNextGroupName());
            vo.setCount((int) rackLogicSpaces.stream()
                    .filter(l -> Objects.equals(e.getRackLogicId(), l.getRackLogicId())).count());
            vo.setRow(e.getRow());
            vo.setColumn(e.getColumn());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 一次分拣下架
     */
    @PostMapping("/unload")
    public Object unload(@RequestBody Set<Long> rackLogicIds) {
        if (CollectionUtils.isEmpty(rackLogicIds)) {
            return Collections.emptyMap();
        }

        for (Long rackLogicId : rackLogicIds) {
            final RackLogicDto rackLogic = rackLogicService.selectByRackLogicId(rackLogicId);
            if (Objects.isNull(rackLogic)) {
                throw new IllegalStateException("试管架不存在，无法下架");
            }

            if (rackLogic.getPosition() != RackLogicPositionEnum.ONE_PICKING.getCode()) {
                throw new IllegalStateException("试管架状态错误或已下架");
            }

            if (CollectionUtils.isEmpty(applySampleService.selectByRackLogicId(rackLogicId))) {
                throw new IllegalStateException("试管架下没有样本，无法下架");
            }

            final RackLogicDto modifyRackLogic = new RackLogicDto();
            modifyRackLogic.setRackLogicId(rackLogicId);
            modifyRackLogic.setPosition(RackLogicPositionEnum.ONE_PICKED.getCode());

            if (!rackLogicService.updateByRackLogicId(modifyRackLogic)) {
                throw new IllegalStateException("试管架下架失败");
            }

            final List<RackLogicApplySampleDto> applySamples = applySampleService.selectByRackLogicId(rackLogicId);
            final LinkedList<Long> ids = snowflakeService.genIds(applySamples.size());

            // 记录流水
            sampleFlowService.addSampleFlows(applySamples.stream()
                    .map(e -> SampleFlowDto.builder().applyId(e.getApplyId()).sampleFlowId(ids.pop())
                            .applySampleId(e.getApplySampleId()).operateCode(BarcodeFlowEnum.ONE_PICK_UNLOAD.name())
                            .operateName(BarcodeFlowEnum.ONE_PICK_UNLOAD.getDesc())
                            .operator(LoginUserHandler.get().getNickname()).operatorId(LoginUserHandler.get().getUserId())
                            .barcode(e.getBarcode()).content(String.format("试管架 [%s] 下架 数量 [%s]",
                                    rackLogic.getRackCode(), applySamples.size())).build())
                    .collect(Collectors.toList()));
        }

        return Collections.emptyMap();

    }

    /**
     * 交接
     */
    @PostMapping("/handover")
    public Object handover(@RequestBody OnePickHandoverVo vo) {
        final UserDto user = userService.selectByUsername(vo.getUsername());


        if (Objects.isNull(user)) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        // 校验密码
        if (!userService.validPassword(user.getUsername(), user.getPassword(), vo.getPassword())) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        final RackLogicDto rackLogic = rackLogicService.selectByRackLogicId(vo.getRackLogicId());
        if (Objects.isNull(rackLogic)) {
            throw new IllegalArgumentException("试管架不存在");
        }

        if (!userService.containsGroup(user.getUserId(), rackLogic.getNextGroupId())) {
            throw new IllegalStateException(
                    String.format("工号 [%s] 无法接收专业组 [%s] 的试管架", vo.getUsername(), rackLogic.getNextGroupName()));
        }

        if (rackLogic.getPosition() != RackLogicPositionEnum.ONE_PICKED.getCode()) {
            throw new IllegalArgumentException("当前试管架未下架或已经交接");
        }

        final ProfessionalGroupDto group = groupService.selectSplitBloodGroup(LoginUserHandler.get().getOrgId());

        final RackLogicDto modifyRackLogic = new RackLogicDto();
        modifyRackLogic.setRackLogicId(vo.getRackLogicId());
        // 如果是分血组 那么会多几个环节
        if (Objects.equals(rackLogic.getNextGroupId(), group.getGroupId())) {
            modifyRackLogic.setPosition(RackLogicPositionEnum.SPLITTING_BLOOD.getCode());
        } else {
            modifyRackLogic.setPosition(RackLogicPositionEnum.TWO_PICKING.getCode());
        }
        modifyRackLogic.setCurrentGroupId(rackLogic.getNextGroupId());
        modifyRackLogic.setCurrentGroupName(rackLogic.getNextGroupName());
        modifyRackLogic.setNextGroupId(NumberUtils.LONG_ZERO);
        modifyRackLogic.setNextGroupName(StringUtils.EMPTY);
        modifyRackLogic.setLastHandover(user.getNickname());

        if (!rackLogicService.updateByRackLogicId(modifyRackLogic)) {
            throw new IllegalArgumentException("一次分拣后交接失败");
        }

        final List<RackLogicApplySampleDto> applySamples = applySampleService.selectByRackLogicId(vo.getRackLogicId());


        // 根据逻辑试管架中当前专业组的id查对应专业组的code，然后和nacos配置的病理组code比较，如果为true则回收试管架
        ProfessionalGroupDto professionalGroupDto = getGroupService.selectByGroupId(LoginUserHandler.get().getGroupId());


        Long rackId = rackLogic.getRackId();
        // 判断是否是病理组，是的话自动回收试管架
        boolean isPathologyGroup = false;
        if (professionalGroupDto != null) {
            final String[] pathologyGroupCodes = environment.getProperty("pathology.group-code", StringUtils.EMPTY).split(",");
            isPathologyGroup = ArrayUtils.contains(pathologyGroupCodes, professionalGroupDto.getGroupCode());
        }
        if (Boolean.TRUE.equals(isPathologyGroup)) {
            // 删除逻辑试管架
            rackLogicService.deleteByRackIds(Collections.singleton(rackId));

            final RackDto rackDto = new RackDto();
            rackDto.setUpdaterId(LoginUserHandler.get().getUserId());
            rackDto.setUpdaterName(LoginUserHandler.get().getNickname());
            rackDto.setStatus(RackStatusEnum.IDLE.getCode());
            // 更新物理试管架状态为空闲
            rackService.updateByRackIds(rackDto, Collections.singleton(rackId));
            log.info("病理组一次分拣后交接要回收的物理试管架ID：[{}]：", JSONObject.toJSON(rackId));

        }

        final LinkedList<Long> ids = snowflakeService.genIds(applySamples.size());

        // 记录流水
        sampleFlowService
                .addSampleFlows(
                        applySamples.stream()
                                .map(e -> SampleFlowDto.builder().applyId(e.getApplyId()).sampleFlowId(ids.pop())
                                        .applySampleId(e.getApplySampleId()).operateCode(BarcodeFlowEnum.ONE_PICK_HANDOVER.name())
                                        .operateName(BarcodeFlowEnum.ONE_PICK_HANDOVER.getDesc())
                                        .operator(LoginUserHandler.get().getNickname()).operatorId(LoginUserHandler.get().getUserId())
                                        .barcode(e.getBarcode()).content(String.format("从 [%s] 接收到 [%s]",
                                                rackLogic.getCurrentGroupName(), rackLogic.getNextGroupName()))
                                        .build())
                                .collect(Collectors.toList()));

        return Collections.emptyMap();

    }

    /**
     * 要交接的试管架
     */
    @PostMapping("/racks")
    public Object racks(@RequestBody OnePickRackVo vo) {

        if (Objects.isNull(vo.getBeginOnePickDate()) || Objects.isNull(vo.getEndOnePickDate())) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }

        List<OnePickRackLogicInfoDto> list;
        //判断是否需要将分拣提前
        SystemParamDto systemParamDto = systemParamService.selectByParamName(SystemParamNameEnum.SORTING_PRE_HANDOVER.getCode()
                , LoginUserHandler.get().getOrgId());

        if (systemParamDto != null && YesOrNoEnum.YES.getDesc().equals(systemParamDto.getParamValue())) {
            list = rackLogicService.selectOnePickWaitingHandoverRackLogics(
                    vo.getBeginOnePickDate(), vo.getEndOnePickDate(), NumberUtils.LONG_ZERO);
        } else {
            list = rackLogicService.selectOnePickWaitingHandoverRackLogics(
                    vo.getBeginOnePickDate(), vo.getEndOnePickDate(), LoginUserHandler.get().getGroupId());
        }

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return JSON.parseArray(JSON.toJSONString(list), OnePickRackLogicInfoVo.class);

    }

    /**
     * 空闲的试管架
     */
    @PostMapping("/idle-racks")
    public Object idleRacks() {
        final List<RackDto> racks = new ArrayList<>(
                rackService.selectByStatus(RackStatusEnum.IDLE.getCode(), LoginUserHandler.get().getOrgId()));

        // 移除归档架
        racks.removeIf(e -> RackTypeEnum.isArchiveRack(e.getRackTypeCode()));

        if (CollectionUtils.isEmpty(racks)) {
            throw new IllegalStateException("已无空闲试管架，请回收后再分拣");
        }

        return racks;
    }

    /**
     * 要交接的试管架下的样本
     */
    @GetMapping("/racks/samples")
    public Object racksSamples(@RequestParam Long rackLogicId) {

        if (Objects.isNull(rackLogicId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<RackLogicApplySampleDto> samples = applySampleService.selectByRackLogicId(rackLogicId);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        final Map<Long, List<ApplySampleItemDto>> applySampleItems = applySampleItemService
                .selectByApplySampleIds(samples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        final Map<Long,
                ApplyDto> applies = applyService
                .selectByApplyIds(samples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet())).stream()
                .collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));

        return samples.stream().map(e -> {
            final OnePickRacksSamplesVo v = new OnePickRacksSamplesVo();
            BeanUtils.copyProperties(e, v);
            if (applies.containsKey(e.getApplyId())) {
                BeanUtils.copyProperties(applies.get(e.getApplyId()), v);
            }
            v.setTestItemNames(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList()).stream()
                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            return v;
        }).collect(Collectors.toList());

    }

    /**
     * 样本回退
     *
     * @param sampleRollbackVo
     * @return
     */
    @PostMapping("/sample/rollback")
    public Object sampleRollback(@RequestBody SampleRollbackVo sampleRollbackVo) {
        Long applySampleId = sampleRollbackVo.getApplySampleId();

        if (Objects.isNull(applySampleId)) {
            throw new IllegalArgumentException("申请单样本ID不能为空");
        }

        ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        // 一次分拣
        Integer isOnePick = applySampleDto.getIsOnePick();
        if (!Objects.equals(YesOrNoEnum.YES.getCode(), isOnePick)) {
            throw new IllegalStateException("样本未一次分拣，无需执行回退操作");
        }
        // 是否已经二次分拣
        Integer isTwoPick = applySampleDto.getIsTwoPick();
        if (Objects.equals(YesOrNoEnum.YES.getCode(), isTwoPick)) {
            throw new IllegalStateException("仅可回退二次分拣之前的样本");
        }

        // 查询相关的免疫二次分拣样本是否都已取消二次分拣
        if (applySampleService.selectByBarcode(applySampleDto.getBarcode()).stream()
                // .filter(e -> !Objects.equals(e.getStatus(), SampleStatusEnum.STOP_TEST.getCode()))
                // .filter(e -> Objects.equals(e.getIsImmunityTwoPick(), YesOrNoEnum.YES.getCode()))
                .anyMatch(e -> Objects.equals(e.getIsImmunityTwoPick(), YesOrNoEnum.YES.getCode()))) {
            // throw new IllegalStateException("仅可回退二次分拣之前的样本（免疫二次分拣）");
            throw new IllegalStateException("免疫二次分拣的样本不支持回退");
        }

        // 是否已经分血
        if (applySampleService.isGroupHandoverByBarcode(applySampleDto)) {
            throw new IllegalStateException("暂不支持不可分血条码回退");
        }

        // 调用取消一次分拣
        applySampleService.cancelOnePick(applySampleId);

        // 记录流水
        sampleFlowService
                .addSampleFlows(
                        Stream.of(applySampleDto)
                                .map(e ->
                                        SampleFlowDto.builder()
                                                .applyId(e.getApplyId())
                                                .sampleFlowId(snowflakeService.genId())
                                                .applySampleId(e.getApplySampleId())
                                                .operateCode(BarcodeFlowEnum.SAMPLE_ROLLBACK.name())
                                                .operateName(BarcodeFlowEnum.SAMPLE_ROLLBACK.getDesc())
                                                .operator(LoginUserHandler.get().getNickname())
                                                .operatorId(LoginUserHandler.get().getUserId())
                                                .barcode(e.getBarcode()).content("样本回退至待一次分拣")
                                                .build()
                                ).collect(Collectors.toList()));

        return Collections.emptyMap();
    }


    /**
     * 打印交接单
     *
     * @param rackLogicIds
     * @return
     */
    @PostMapping("/print-pass-on-sheet")
    public Object printPassOnSheet(@RequestBody List<Long> rackLogicIds) {
        if (CollectionUtils.isEmpty(rackLogicIds)) {
            throw new IllegalArgumentException("未选择试管架");
        }

        final List<RackLogicApplySampleDto> samples = applySampleService.selectByRackLogicIds(rackLogicIds);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        Map<Long, List<RackLogicApplySampleDto>> samplesMap = samples.stream().collect(Collectors.groupingBy(RackLogicApplySampleDto::getRackLogicId));
        LinkedList<RackLogicApplySampleDto> samplesList = new LinkedList<>();
        samplesMap.forEach((k, v) -> samplesList.addAll(v));

        final Map<Long, List<ApplySampleItemDto>> applySampleItems = applySampleItemService
                .selectByApplySampleIds(samples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        final Map<Long,
                ApplyDto> applies = applyService
                .selectByApplyIds(samples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet())).stream()
                .collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));

        List<PrintPdfOnePickSamplesVo> samplesVos = samplesList.stream().map(e -> {
            final PrintPdfOnePickSamplesVo v = new PrintPdfOnePickSamplesVo();
            BeanUtils.copyProperties(e, v);
            if (applies.containsKey(e.getApplyId())) {
                BeanUtils.copyProperties(applies.get(e.getApplyId()), v);
            }
            v.setTestName(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList()).stream()
                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            return v;
        }).collect(Collectors.toList());

        PdfReportParamDto pdfData = new PdfReportParamDto();

        pdfData.put("groupName", LoginUserHandler.get().getGroupName());
        pdfData.put("printDate", LocalDate.now());
        pdfData.put("count", samplesVos.size());
        pdfData.put("data", samplesVos);

        return Map.of("url", pdfReportService.build2Url(PdfTemplateTypeEnum.PRINT_PASS_ON_SHEET.getCode(), pdfData, 1));
    }

}
