package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class InstrumentResultReceiverVo {

    /**
     * 样本号/条码号
     */
    private String barcode;

    /**
     * 检验时间 yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date date;

    /**
     * 检测的仪器编码
     */
    private String machineCode;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 检验类型(当前仪器属于哪个检验的)
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 接收器的元数据
     */
    private String metadata;

    /**
     * 是否是质控结果
     */
    private Boolean isQc;

    /**
     * 结果
     */
    private List<ResultInfo> results;


    @Getter
    @Setter
    public static class ResultInfo {
        /**
         * 名称 仪器通道编码
         */
        private String name;

        /**
         * 结果
         */
        private String result;

        /**
         * 如果是 true 那么 result 是图片地址
         */
        private Boolean isImage;

        /**
         * OD 值（酶标）
         */
        private String odValue;

        /**
         * C/CO 值（酶标）
         */
        private String scoValue;

        /**
         * cutoff 值（酶标）
         */
        private String cutoffValue;

        /**
         * 结果描述
         */
        private String resultDesc;

        /**
         * 检验时间 yyyy-MM-dd HH:mm:ss
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date date;

        /**
         * 仪器报告项目编码
         */
        private String reportItemCode;

    }


}
