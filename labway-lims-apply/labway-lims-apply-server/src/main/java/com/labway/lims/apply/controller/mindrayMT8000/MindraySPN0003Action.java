package com.labway.lims.apply.controller.mindrayMT8000;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.mdm.common.util.SpringUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.base.RackTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.service.chain.sample.archive.add.SampleArchiveAddChain;
import com.labway.lims.apply.service.chain.sample.archive.add.SampleArchiveAddContext;
import com.labway.lims.apply.vo.MT8000HandleVo;
import com.labway.lims.apply.vo.RackArchiveAddRequestVo;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.RefrigeratorDto;
import com.labway.lims.base.api.enums.RackStatusEnum;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.RackService;
import com.labway.lims.base.api.service.RefrigeratorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.aop.framework.AopContext;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 分拣
 */
@Slf4j
@Component
class MindraySPN0003Action implements ActionStrategy, InitializingBean {

    /**
     * 流水线冰箱编码
     */
    private static final String MINDRAY_REFRIGERATOR_CODE = "Mindray_Refrigerator";

    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private RackService rackService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private RefrigeratorService refrigeratorService;
    @DubboReference
    private GroupService groupService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private SampleArchiveAddChain sampleArchiveAddChain;




    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object action(MT8000HandleVo vo) throws Exception {
        // 条码号
        final String barcode = vo.getExtras().getString("barcode");
        // 仪器编码
        final String instrumentCode = vo.getExtras().getString("instrumentCode");
        // 专业组编码
        final String groupCode = vo.getExtras().getString("groupCode");
        // 上机时间
        Date optTime = vo.getExtras().getDate("optTime");
        //填充样本位置信息 文本描述详细位置
        //设备^序号^架号^位置
        //序号以流水线布局的序号分类排序生成
        String samplePosition = vo.getExtras().getString("samplePosition");
        // 分区名称
        String areaName = vo.getExtras().getString("areaName");
        // 样本架在设备内的位置
        //层序号^横向序号^纵向序号
        //冰箱标记第几层(由上而下),IO/SDM 固定为1 横向序号：从左到右 纵向序号：从上到下
        String rackPosition = vo.getExtras().getString("rackPosition");



        final LoginUserHandler.User user = LoginUserHandler.get();

        // 试管架号
        String rackCode =samplePosition.split("\\^")[3];

        if (StringUtils.isAnyBlank(barcode, samplePosition, rackCode)) {
            throw new IllegalArgumentException("参数错误");
        }

         List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException(String.format("条码 [%s] 不存在", barcode));
        }

        // 判断专业组是否存在
        final ProfessionalGroupDto group = groupService.selectByGroupCode(groupCode, LoginUserHandler.get().getOrgId());
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException(String.format("专业组编码 [%s] 不存在", groupCode));
        }

        // 匹配专业组
        applySamples = applySamples.stream().filter(e -> Objects.equals(e.getGroupId(), group.getGroupId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException(String.format("专业组 [%s] 不存在条码 [%s]",group.getGroupName(), barcode));
        }


        if (applySamples.size() != 1) {
            throw new IllegalArgumentException(String.format("条码 [%s] 查询到 [%s] 记录，无法确定专业组", barcode, applySamples.size()));
        }

        // 获取冰箱
        final RefrigeratorDto refrigerator = refrigeratorService.selectByRefrigeratorCode(MINDRAY_REFRIGERATOR_CODE, user.getOrgId());
        if (Objects.isNull(refrigerator)) {
            throw new IllegalArgumentException(String.format("请配置迈瑞流水线冰箱，冰箱编码 [%s]", MINDRAY_REFRIGERATOR_CODE));
        }


        final ApplySampleDto applySample = applySamples.iterator().next();


        rackCode = DateFormatUtils.format(new Date(), "yyMMdd") + rackCode + "_" + group.getGroupCode();

        final String rc = rackCode;

        final RackDto rack = threadPoolConfig.getPool().submit(() -> {
            LoginUserHandler.set(user);
            try {
                    return SpringUtil.getBean(MindraySPN0003Action.class).getRack(rc);
            } finally {
                LoginUserHandler.remove();
            }
        }).get(10, TimeUnit.SECONDS);


        final SampleArchiveAddContext context = new SampleArchiveAddContext();
        final RackArchiveAddRequestVo request = new RackArchiveAddRequestVo();
        request.setRefrigeratorId(refrigerator.getRefrigeratorId());
        request.setRackId(rack.getRackId());
        request.setBarcode(barcode);
        request.setStartEffectiveDate(DateUtil.beginOfDay(new Date()));
        request.setEndEffectiveDate(DateUtils.addMonths(request.getStartEffectiveDate(), 3));

        context.setRackArchiveAddRequestVo(request);
        context.setUser(LoginUserHandler.get());

        context.getUser().setGroupId(applySample.getGroupId());
        context.getUser().setGroupName(applySample.getGroupName());

        try {
            sampleArchiveAddChain.execute(context);
        }catch (Exception ex){
            log.error("迈瑞样本归档-存储异常",ex);

            sampleFlowService.addSampleFlows(
                    applySamples.stream()
                            .map(e -> SampleFlowDto.builder()
                                    .applyId(e.getApplyId())
                                    .sampleFlowId(snowflakeService.genId())
                                    .applySampleId(e.getApplySampleId())
                                    .operateCode(BarcodeFlowEnum.MT8000SPN0003.name())
                                    .operateName(BarcodeFlowEnum.MT8000SPN0003.getDesc())
                                    .operator(LoginUserHandler.get().getNickname())
                                    .operatorId(LoginUserHandler.get().getUserId())
                                    .barcode(e.getBarcode())
                                    .content(String.format("样本位置变更【归档失败】 变更时间[%s] 分区名称[%s] 详细位置[设备名:%s 序号:%s 架号:%s 位置:%s]",
                                            DateUtil.formatDateTime(optTime),
                                            areaName,
                                            samplePosition.split("\\^")[0],samplePosition.split("\\^")[1],
                                            samplePosition.split("\\^")[2],samplePosition.split("\\^")[3]))
                                    .build())
                            .collect(Collectors.toList()));

            return Map.of();
        } finally {
            log.info("迈瑞样本归档-存储耗时\n{}", context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }


        // 记录流水
        sampleFlowService.addSampleFlows(
                        applySamples.stream()
                                .map(e -> SampleFlowDto.builder()
                                        .applyId(e.getApplyId())
                                        .sampleFlowId(snowflakeService.genId())
                                        .applySampleId(e.getApplySampleId())
                                        .operateCode(BarcodeFlowEnum.MT8000SPN0003.name())
                                        .operateName(BarcodeFlowEnum.MT8000SPN0003.getDesc())
                                        .operator(LoginUserHandler.get().getNickname())
                                        .operatorId(LoginUserHandler.get().getUserId())
                                        .barcode(e.getBarcode())
                                        .content(String.format("样本位置变更 变更时间[%s] 冰箱位置[层序号:%s 横向序号:%s 纵向序号:%s] " + "分区名称[%s] 详细位置[设备名:%s 序号:%s 架号:%s 位置:%s]",
                                                DateUtil.formatDateTime(optTime),
                                                rackPosition.split("\\^")[0], rackPosition.split("\\^")[1], rackPosition.split("\\^")[2],
                                                areaName,
                                                samplePosition.split("\\^")[0],samplePosition.split("\\^")[1],
                                                samplePosition.split("\\^")[2],samplePosition.split("\\^")[3]))
                                        .build())
                                .collect(Collectors.toList()));

        return Map.of();
    }


    @Override
    public MT8000HandleVo.Action action() {
        return MT8000HandleVo.Action.SPN0003;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
    }


    /**
     * 获取一个归档试管架
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public RackDto getRack(String rackCode) throws InterruptedException {

        lock(rackCode);

        try {
            RackDto rack = rackService.selectByRackCode(rackCode, LoginUserHandler.get().getOrgId());
            if (Objects.nonNull(rack)) {
                return rack;
            }

            rack = new RackDto();
            rack.setRackTypeCode(RackTypeEnum.ARCHIVE_RACK.getCode());
            rack.setRackTypeName(RackTypeEnum.ARCHIVE_RACK.getDesc());
            rack.setRackCode(rackCode);
            rack.setRow(10);
            rack.setColumn(10);
            rack.setStatus(RackStatusEnum.IDLE.getCode());
            rack.setEnable(YesOrNoEnum.YES.getCode());
            rack.setOrgId(LoginUserHandler.get().getOrgId());
            rack.setUpdateDate(new Date());
            rack.setCreateDate(new Date());
            rack.setUpdaterId(NumberUtils.LONG_ZERO);
            rack.setUpdaterName("Roche");
            rack.setCreatorId(NumberUtils.LONG_ZERO);
            rack.setCreatorName("Roche");
            rack.setIsDelete(YesOrNoEnum.NO.getCode());

            rack.setRackId(rackService.add(rack));

            log.info("罗氏添加归档试管架 [{}]", JSON.toJSONString(rack));

            return rack;
        } finally {
            unlock(rackCode);
        }
    }

    /**
     * 全局限流，同一个条码必须有序。如果获取锁失败会等10秒左右，如果10秒内没有获取到锁那么抛出异常
     */
    private void lock(String rackCode) throws InterruptedException {
        final String key = redisPrefix.getBasePrefix() + MindraySPN0003Action.class.getName() + ":" + rackCode;

        final long timestamp = System.currentTimeMillis();
        final Duration timeout = Duration.ofSeconds(10);

        do {
            // 锁试管架
            if (BooleanUtils.isTrue(stringRedisTemplate.opsForValue()
                    .setIfAbsent(key, StringUtils.EMPTY, timeout))) {
                return;
            }

            Thread.yield();

            // 尝试加锁
            synchronized (this) {
                wait(20);
            }

        } while (System.currentTimeMillis() - timestamp < timeout.toMillis());

        throw new IllegalStateException(String.format("试管架 [%s] 正在添加中", rackCode));
    }

    private void unlock(String rackCode) {
        stringRedisTemplate.delete(redisPrefix.getBasePrefix() + MindraySPN0003Action.class.getName() + ":" + rackCode);
    }

}
