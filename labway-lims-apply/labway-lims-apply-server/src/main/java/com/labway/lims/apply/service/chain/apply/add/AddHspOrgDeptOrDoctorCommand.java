package com.labway.lims.apply.service.chain.apply.add;

import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.service.chain.apply.update.UpdateHspOrgDeptOrDoctorCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/7/27 16:36
 * <p>
 * 添加送检机构部门和医生基础数据
 */
@Slf4j
@Component
public class AddHspOrgDeptOrDoctorCommand implements Command {

    @Resource
    private UpdateHspOrgDeptOrDoctorCommand updateHspOrgDeptOrDoctorCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final AddApplyContext context = AddApplyContext.from(c);
        final ApplyDto apply = context.getApply();

        try {
            updateHspOrgDeptOrDoctorCommand.addHspOrgOrDeptOrDoctor(apply);
        } catch (Exception e) {
            log.error("新增送检机构科室或医生异常[{}]", e.getMessage());
        }

        return CONTINUE_PROCESSING;
    }

}
