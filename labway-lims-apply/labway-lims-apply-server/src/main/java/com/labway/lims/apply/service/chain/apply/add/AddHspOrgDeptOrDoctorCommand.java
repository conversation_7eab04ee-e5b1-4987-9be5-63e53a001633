package com.labway.lims.apply.service.chain.apply.add;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.UpdateTestApplySampleDto;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import com.labway.lims.apply.service.chain.apply.update.UpdateHspOrgDeptOrDoctorCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/7/27 16:36
 * <p>
 * 添加送检机构部门和医生基础数据
 */
@Slf4j
@Component
public class AddHspOrgDeptOrDoctorCommand implements Command {

    @Resource
    private UpdateHspOrgDeptOrDoctorCommand updateHspOrgDeptOrDoctorCommand;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Override
    public boolean execute(Context c) throws Exception {
        final ApplyDto apply;
        final LoginUserHandler.User user;
        if (c instanceof AddApplyContext) {
            final AddApplyContext context = AddApplyContext.from(c);
            apply = context.getApply();
            user = context.getUser();
        } else {
            UpdateApplyContext context = UpdateApplyContext.from(c);

            if (context.getTestApply() instanceof UpdateTestApplySampleDto) {
                return CONTINUE_PROCESSING;
            }

            apply = context.getApply();
            user = context.getUser();
        }

        // 异步处理， 不影响正常业务录入
        threadPoolConfig.getPool().submit(() -> {
            LoginUserHandler.set(user);
            try {
                updateHspOrgDeptOrDoctorCommand.addHspOrgOrDeptOrDoctor(apply);
            } catch (Exception e) {
                log.error("新增送检机构科室或医生异常[{}]", e.getMessage());
            } finally {
                LoginUserHandler.remove();
            }
        });

        return CONTINUE_PROCESSING;
    }

}
