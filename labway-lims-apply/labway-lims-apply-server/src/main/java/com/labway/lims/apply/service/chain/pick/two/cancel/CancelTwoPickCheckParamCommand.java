package com.labway.lims.apply.service.chain.pick.two.cancel;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.service.RackService;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 校验参数
 */
@Slf4j
@Component
class CancelTwoPickCheckParamCommand implements Command {
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private RackService rackService;

    @Override
    public boolean execute(Context c) throws Exception {
        final CancelTwoPickContext context = CancelTwoPickContext.from(c);
        final List<ApplySampleDto> applySamples = new LinkedList<>(applySampleService.selectByBarcode(context.getBarcode()));
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        // 移除非当前专业组的
        applySamples.removeIf(e -> !Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId()));

        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException(String.format("条码 [%s] 不属于当前专业组", context.getBarcode()));
        }

        if (applySamples.stream().anyMatch(e -> !Objects.equals(e.getIsTwoPick(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalStateException(String.format("条码 [%s] 尚未二次分拣", context.getBarcode()));
        }

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())
                || Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode()))) {
            throw new IllegalStateException("该样本已经审核，不能取消二次分拣");
        }

        final ApplySampleDto applySample = applySamples.iterator().next();

        final RackDto rack;
        // 如果是 0 那表示没有试管架，可能是 it8000、it3000等仪器分拣
        if (Objects.equals(applySample.getRackId(), NumberUtils.LONG_ZERO)) {
            rack = new RackDto();
            rack.setRackId(NumberUtils.LONG_ZERO);
            rack.setRow(10);
            rack.setColumn(rack.getRow());
            rack.setRackCode("AutoSorting");
            rack.setOrgId(LoginUserHandler.get().getOrgId());
            log.info("条码 [{}] 专业组 [{}] 取消二次分拣 ，没有试管架使用0作为试管架，IT8000分拣的样本是没有试管架的",
                    applySamples.iterator().next().getBarcode(), LoginUserHandler.get().getGroupName());
        } else {
            rack = rackService.selectByRackId(applySamples.iterator().next().getRackId());
            if (Objects.isNull(rack)) {
                throw new IllegalStateException("试管架不存在");
            }
        }

        List<Long> terminateApplySampleIds = new ArrayList<>();
        for (ApplySampleDto e : applySamples) {
            if (applySampleService.isDisabled(e.getApplySampleId())) {
                throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", e.getBarcode()));
            }

            if (applySampleService.isTerminate(e.getApplySampleId())) {
                terminateApplySampleIds.add(e.getApplySampleId());
                // throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", e.getBarcode()));
            }
        }

        // 过滤掉 终止检验的样本
        String barcode = applySamples.stream().filter(e -> terminateApplySampleIds.contains(e.getApplySampleId()))
                .map(ApplySampleDto::getBarcode).distinct().collect(Collectors.joining(StringPool.COMMA));
        applySamples.removeIf(e -> terminateApplySampleIds.contains(e.getApplySampleId()));
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", barcode));
        }

        context.put(CancelTwoPickContext.APPLY_TEST_ITEMS, applySampleItemService.selectByApplySampleIds(applySamples.stream()
                .map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet())));
        context.put(CancelTwoPickContext.RACK, rack);
        context.put(CancelTwoPickContext.APPLY_SAMPLES, applySamples);
        // 要保留的申请单样本
        if (Objects.isNull(context.getAliveApplySampleId())){
            context.put(CancelTwoPickContext.ALIVE_APPLY_SAMPLE_ID,
                    applySamples.iterator().next().getApplySampleId());
        }else {
            context.put(CancelTwoPickContext.ALIVE_APPLY_SAMPLE_ID,
                    context.getAliveApplySampleId());
        }

        return CONTINUE_PROCESSING;
    }

}
