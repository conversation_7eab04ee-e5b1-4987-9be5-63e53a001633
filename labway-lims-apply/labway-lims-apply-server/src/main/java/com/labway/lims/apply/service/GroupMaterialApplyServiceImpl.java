package com.labway.lims.apply.service;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.business.center.third.ncc.request.MaterialApplyFormDetailRequest;
import com.labway.business.center.third.ncc.request.MaterialApplyFormRequest;
import com.labway.business.center.third.ncc.service.MaterialApplyFormService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.MaterialApplyStatusEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.BusinessCenterRejectApplyDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDetailAddDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDetailDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDto;
import com.labway.lims.apply.api.dto.QueryApprovalPlanPageDto;
import com.labway.lims.apply.api.enums.MaterialApplyTypeEnum;
import com.labway.lims.apply.api.service.GroupMaterialApplyDetailService;
import com.labway.lims.apply.api.service.GroupMaterialApplyService;
import com.labway.lims.apply.api.vo.GroupMaterialPlanVo;
import com.labway.lims.apply.config.BusinessConfig;
import com.labway.lims.apply.config.OutCustomer;
import com.labway.lims.apply.mapper.TbGroupMaterialApplyMapper;
import com.labway.lims.apply.mapstruct.GroupMaterialApplyConverter;
import com.labway.lims.apply.model.TbGroupMaterialApply;
import com.labway.lims.apply.vo.utils.MaterialNoUtils;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import com.labway.lims.base.api.dto.MaterialDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import com.labway.lims.base.api.service.MaterialService;
import com.swak.frame.dto.Response;
import com.swak.frame.enums.BasicErrCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class GroupMaterialApplyServiceImpl implements GroupMaterialApplyService {
    @Resource
    private TbGroupMaterialApplyMapper tbGroupMaterialApplyMapper;

    @DubboReference
    private GroupMaterialService groupMaterialService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private GroupMaterialApplyDetailService groupMaterialApplyDetailService;

    @Resource
    private MaterialNoUtils materialNoUtils;

    @DubboReference
    private MaterialService materialService;

    @Resource
    private MaterialApplyFormService materialApplyFormService;

    @Resource
    private GroupMaterialApplyConverter groupMaterialApplyConverter;
    @Resource
    private BusinessConfig businessConfig;

    @Override
    public GroupMaterialApplyDto selectById(long id) {
        return convert(tbGroupMaterialApplyMapper.selectById(id));
    }

    @Override
    public GroupMaterialApplyDto selectByOrgIdAndApplyNo(long orgId, String applyNo) {
        if (StringUtils.isBlank(applyNo)) {
            return null;
        }

        LambdaQueryWrapper<TbGroupMaterialApply> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGroupMaterialApply::getApplyNo, applyNo);
        queryWrapper.eq(TbGroupMaterialApply::getOrgId, orgId);
        queryWrapper.last("limit 1");

        return groupMaterialApplyConverter
            .groupMaterialApplyDtoFromTbObj(tbGroupMaterialApplyMapper.selectOne(queryWrapper));
    }

    @Override
    public GroupMaterialApplyDto selectByGroupIdAndApplyNo(long groupId, String applyNo) {
        if (StringUtils.isBlank(applyNo)) {
            return null;
        }

        LambdaQueryWrapper<TbGroupMaterialApply> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbGroupMaterialApply::getApplyNo, applyNo);
        queryWrapper.eq(groupId != NumberUtils.LONG_ZERO , TbGroupMaterialApply::getGroupId, groupId);
        queryWrapper.last("limit 1");

        return groupMaterialApplyConverter
            .groupMaterialApplyDtoFromTbObj(tbGroupMaterialApplyMapper.selectOne(queryWrapper));
    }

    @Override
    public List<GroupMaterialApplyDto> selectCurrentGroupMaterialApply(String applyNo, Date startDate, Date endDate,
        Long groupId) {
        final LambdaQueryWrapper<TbGroupMaterialApply> between = Wrappers.lambdaQuery(TbGroupMaterialApply.class)
            .eq(StringUtils.isNotBlank(applyNo), TbGroupMaterialApply::getApplyNo, applyNo)
            .eq(groupId != null, TbGroupMaterialApply::getGroupId, groupId)
            .between(ObjectUtils.allNotNull(startDate, endDate), TbGroupMaterialApply::getApplyTime, startDate, endDate)
            .orderByAsc(TbGroupMaterialApply::getApplyTime);
        return tbGroupMaterialApplyMapper.selectList(between).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> add(List<GroupMaterialApplyDetailAddDto> param) {
        if (CollectionUtils.isEmpty(param)) {
            throw new IllegalArgumentException("物料信息为空");
        }

        final Map<Long, GroupMaterialApplyDetailAddDto> groupMaterialApplyDetailAddMap = param.stream()
            .collect(Collectors.toMap(GroupMaterialApplyDetailAddDto::getMaterialId, Function.identity(), (a, b) -> a));
        List<GroupMaterialDto> groupMaterials =
            groupMaterialService.seleceGroupMaterialById(groupMaterialApplyDetailAddMap.keySet());
        if (CollectionUtils.isEmpty(groupMaterials)) {
            throw new IllegalStateException("物料不存在");
        }

        final Set<Long> materialIds =
            groupMaterials.stream().map(GroupMaterialDto::getMaterialId).collect(Collectors.toSet());
        final Map<Long, MaterialDto> materialMap = materialService.selectByIds(materialIds).stream()
            .collect(Collectors.toMap(MaterialDto::getMaterialId, Function.identity(), (a, b) -> a));
        if (MapUtils.isEmpty(materialMap)) {
            throw new IllegalStateException("物料不存在");
        }

        LoginUserHandler.User user = LoginUserHandler.get();

        // 创建申领单
        final GroupMaterialApplyDto materialApply = createMaterialApply();

        // 批量拿id
        final LinkedList<Long> ids = snowflakeService.genIds(groupMaterials.size());

        AtomicInteger addSeconds = new AtomicInteger(1);
        final Date now = new Date();

        final List<GroupMaterialApplyDetailDto> details = param.stream().map(m -> {
            final GroupMaterialApplyDetailAddDto groupMaterialApplyDetailAdd =
                groupMaterialApplyDetailAddMap.get(m.getMaterialId());
            if (Objects.isNull(groupMaterialApplyDetailAdd)) {
                throw new IllegalStateException("数据错误");
            }
            final MaterialDto material = materialMap.get(m.getMaterialId());
            if (Objects.isNull(material)) {
                throw new IllegalStateException("数据错误");
            }

            GroupMaterialApplyDetailDto groupMaterialApplyDetail = new GroupMaterialApplyDetailDto();
            groupMaterialApplyDetail.setDetailId(ids.pop());
            groupMaterialApplyDetail.setApplyNo(materialApply.getApplyNo());
            groupMaterialApplyDetail.setMaterialId(m.getMaterialId());

            groupMaterialApplyDetail.setMaterialCode(material.getMaterialCode());
            groupMaterialApplyDetail.setMaterialName(material.getMaterialName());
            groupMaterialApplyDetail.setSpecification(material.getSpecification());
            groupMaterialApplyDetail.setManufacturers(material.getManufacturers());
            groupMaterialApplyDetail.setAssistUnit(material.getAssistUnit());
            groupMaterialApplyDetail.setUnitConversionRate(material.getUnitConversionRate());

            groupMaterialApplyDetail.setBatchNo(StringUtils.EMPTY);
            groupMaterialApplyDetail.setMainUnit(material.getMainUnit());
            groupMaterialApplyDetail.setApplyMainNumber(groupMaterialApplyDetailAdd.getApplyMainNumber());
            groupMaterialApplyDetail.setApplyAssistNumber(groupMaterialApplyDetailAdd.getApplyAssistNumber());
            groupMaterialApplyDetail.setOrgId(materialApply.getOrgId());
            groupMaterialApplyDetail.setOrgName(materialApply.getOrgName());
            groupMaterialApplyDetail.setCreateDate(DateUtils.addSeconds(now, addSeconds.getAndIncrement()));
            groupMaterialApplyDetail.setUpdateDate(now);
            groupMaterialApplyDetail.setUpdaterId(materialApply.getUpdaterId());
            groupMaterialApplyDetail.setUpdaterName(materialApply.getUpdaterName());
            groupMaterialApplyDetail.setCreatorId(materialApply.getCreatorId());
            groupMaterialApplyDetail.setCreatorName(materialApply.getCreatorName());
            groupMaterialApplyDetail.setIsDelete(materialApply.getIsDelete());
            return groupMaterialApplyDetail;
        }).collect(Collectors.toList());

        // save
        tbGroupMaterialApplyMapper
            .insert(JSON.parseObject(JSON.toJSONString(materialApply), TbGroupMaterialApply.class));

        groupMaterialApplyDetailService.addBatch(details);

        log.info("用户 [{}] 专业组 [{}] 创建申领单成功 申领单号 [{}] 物料信息 [{}]", LoginUserHandler.get().getNickname(),
            LoginUserHandler.get().getGroupName(), materialApply.getApplyNo(), JSON.toJSONString(details));

        return Map.of(
            //
            "applyNo", materialApply.getApplyNo(),
            //
            "applyId", materialApply.getApplyId());
    }

    /**
     * 发送申领单到业务中台
     */
    public void sendMaterialApplyToBizCenter(String applyNo, long groupId) {
        if (StringUtils.isBlank(applyNo)) {
            throw new IllegalStateException("申领单号为空");
        }
        final GroupMaterialApplyDto materialApply = selectByGroupIdAndApplyNo(groupId, applyNo);
        if (Objects.isNull(materialApply)) {
            throw new IllegalStateException("申领单不存在");
        }

        final List<GroupMaterialApplyDetailDto> details =
            groupMaterialApplyDetailService.selectByApplyNo(LoginUserHandler.get().getOrgId(), applyNo);
        if (CollectionUtils.isEmpty(details)) {
            throw new IllegalStateException("申领明细为空");
        }
        Map<String, String> orgCodeMap = businessConfig.getOrgCodeMap();
        if (Objects.isNull(orgCodeMap) || Objects.isNull(orgCodeMap.get(LoginUserHandler.get().getOrgCode()))) {
            log.info("请配置实验室:{}", LoginUserHandler.get().getOrgCode());
            throw new IllegalStateException("请配置实验室");
        }
        String businessOrgCode = orgCodeMap.get(LoginUserHandler.get().getOrgCode());

        Map<String, OutCustomer> orgCodeCustomerMap = businessConfig.getOrgCodeCustomerMap();
        if (Objects.isNull(orgCodeCustomerMap)
            || Objects.isNull(orgCodeCustomerMap.get(LoginUserHandler.get().getOrgCode()))) {
            log.info("请配置出库对象:{}", LoginUserHandler.get().getOrgCode());
            throw new IllegalStateException("请配置出库对象");
        }
        OutCustomer outCustomer = orgCodeCustomerMap.get(LoginUserHandler.get().getOrgCode());

        // 通知业务中台
        Response<String> response;
        try {
            // 申领详情
            List<MaterialApplyFormDetailRequest> detailRequestList = Lists.newArrayList();
            details.forEach(item -> {
                MaterialApplyFormDetailRequest detailRequest = new MaterialApplyFormDetailRequest();
                detailRequest.setApplyFormCode(materialApply.getApplyNo());
                detailRequest.setMaterialCode(item.getMaterialCode());
                detailRequest.setMaterialName(item.getMaterialName());
                detailRequest.setMaterialAssistUnit(item.getAssistUnit());
                detailRequest.setMaterialAssistCountApply(item.getApplyAssistNumber());
                detailRequest.setMaterialAssistCountApproval(null);
                detailRequest.setMaterialUnit(item.getMainUnit());
                detailRequest.setMaterialCountApply(null);
                detailRequest.setMaterialCountApplyDecimal(item.getApplyMainNumber());
                detailRequest.setMaterialCountApproval(null);
                detailRequest.setMaterialSpecification(item.getSpecification());
                detailRequest.setEnabled(Boolean.TRUE);
                detailRequestList.add(detailRequest);
            });

            MaterialApplyFormRequest request = new MaterialApplyFormRequest();
            request.setApplyCode(materialApply.getApplyNo());
            request.setApplyTime(materialApply.getApplyTime());
            request.setApplyUserId(String.valueOf(materialApply.getApplyUserId()));
            request.setAppleUserName(materialApply.getApplyUserName());
            request.setApplyOrgCode(String.valueOf(materialApply.getGroupId()));
            request.setApplyOrgName(materialApply.getGroupName());
            request.setApplyCustomerPk(outCustomer.getApplyCustomerPk());
            request.setApplyCustomerName(outCustomer.getApplyCustomerName());
            request.setApplyStatus(1);
            request.setEnabled(Boolean.TRUE);
            request.setApplyRemark(StringUtils.EMPTY);
            request.setSystemName("lims");
            request.setSystemCode("LIMS");
            request.setMaterialApplyFormDetailRequests(detailRequestList);
            request.setLabOrgCode(businessOrgCode);
            request.setLabOrgName(LoginUserHandler.get().getOrgName());
            response = materialApplyFormService.insertApplyForm(request);
        } catch (Exception e) {
            throw new LimsException("业务中台创建申领单失败", e);
        }
        if (Objects.isNull(response)) {
            throw new LimsException("业务中台创建申领单返回结果为空");
        }
        if (!Objects.equals(response.getCode(), BasicErrCode.SUCCESS.getCode())) {
            throw new LimsException(String.format("业务中台创建申领单返回错误, 错误信息: [%s]", response.getMsg()));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByApplyId(GroupMaterialApplyDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbGroupMaterialApply target = groupMaterialApplyConverter.tbGroupMaterialApplyFromTbObjDto(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());
        target.setReturnReason(dto.getReturnReason());

        if (tbGroupMaterialApplyMapper.updateById(target) < 1) {
            throw new LimsException("修改专业组物料失败");
        }

        log.info("用户 [{}] 修改专业组物料成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void businessCenterRejectApply(BusinessCenterRejectApplyDto dto) {
        // 检查 申请单号
        GroupMaterialApplyDto materialApplyDto = this.selectByGroupIdAndApplyNo(dto.getGroupId(), dto.getApplyNo());

        if (Objects.isNull(materialApplyDto)) {
            throw new LimsException(String.format("无效申请单号 [%s] ", dto.getApplyNo()));
        }
        LoginUserHandler.User unsafe = LoginUserHandler.getUnsafe();
        GroupMaterialApplyDto update = new GroupMaterialApplyDto();
        update.setApplyId(materialApplyDto.getApplyId());
        update.setStatus(MaterialApplyStatusEnum.ROLLBACK.getCode());
        update.setChecker(Objects.isNull(unsafe)?StringUtils.EMPTY:unsafe.getNickname());
        update.setCheckerId(Objects.isNull(unsafe)?NumberUtils.LONG_ZERO:unsafe.getUserId());
        update.setCheckDate(new Date());
        update.setReturnReason(dto.getReturnReason());
        this.updateByApplyId(update);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void audit(String applyNo) {
        if (StringUtils.isBlank(applyNo)) {
            throw new IllegalStateException("申领单号为空");
        }

        final GroupMaterialApplyDto materialApply =
            selectByGroupIdAndApplyNo(NumberUtils.LONG_ZERO, applyNo);
        if (Objects.isNull(materialApply)) {
            throw new IllegalStateException("申领单不存在");
        }
        final Integer status = materialApply.getStatus();
        if (!Objects.equals(status, MaterialApplyStatusEnum.SUBMIT.getCode())) {
            throw new IllegalStateException("仅已提交的申领单才可审核");
        }

        if (Objects.equals(status, MaterialApplyStatusEnum.AUDIT.getCode())) {
            throw new IllegalStateException("该申领单已审核");
        }

        final Date now = new Date();
        final GroupMaterialApplyDto update = new GroupMaterialApplyDto();
        update.setApplyId(materialApply.getApplyId());
        update.setStatus(MaterialApplyStatusEnum.AUDIT.getCode());
        update.setCheckDate(now);
        update.setChecker(LoginUserHandler.get().getNickname());
        update.setCheckerId(LoginUserHandler.get().getUserId());

        SpringUtil.getBean(GroupMaterialApplyServiceImpl.class).updateByApplyId(update);

        sendMaterialApplyToBizCenter(applyNo, materialApply.getGroupId());

        log.info("审核 物料申领单 [{}]  审核人 [{}] 专业组 [{}]成功", applyNo, LoginUserHandler.get().getNickname(),
                materialApply.getGroupName());
    }

    /**
     * 已审批专业组计划列表
     * @param queryApprovalPlanPageDto
     * @return
     */
    @Override
    public List<GroupMaterialPlanVo> queryApprovalPlanPage(QueryApprovalPlanPageDto queryApprovalPlanPageDto) {

//        LoginUserHandler.User user = LoginUserHandler.get();
//        queryApprovalPlanPageDto.setGroupId(user.getGroupId());

        List<GroupMaterialApplyDto> groupMaterialApplyDtos = tbGroupMaterialApplyMapper.queryTbGroupMaterialApplyAndDetail(queryApprovalPlanPageDto);
        if (CollectionUtils.isEmpty(groupMaterialApplyDtos)){
            return Collections.emptyList();
        }

        // 数据转换
        return groupMaterialApplyDtos.stream().map(this::convertGroupMaterialPlanVo).collect(Collectors.toList());
    }

    /**
     *
     * @param groupId
     * @return
     */
    @Override
    public List<GroupMaterialApplyDto> selectUnApprovalMaterialApply(Long orgId, Long groupId) {
        final LambdaQueryWrapper<TbGroupMaterialApply> between = Wrappers.lambdaQuery(TbGroupMaterialApply.class)
                .eq(TbGroupMaterialApply::getOrgId,orgId)
                .eq(groupId != null, TbGroupMaterialApply::getGroupId, groupId)
                .eq(TbGroupMaterialApply::getStatus, String.valueOf(MaterialApplyStatusEnum.SUBMIT.getCode()))
                .eq(TbGroupMaterialApply::getIsDelete, YesOrNoEnum.NO.getCode())
                .orderByAsc(TbGroupMaterialApply::getApplyTime);
        List<TbGroupMaterialApply> tbGroupMaterialApplies = tbGroupMaterialApplyMapper.selectList(between);
        if (CollectionUtils.isEmpty(tbGroupMaterialApplies)){
            return Collections.emptyList();
        }
        return tbGroupMaterialApplies.stream().map(this::convert).collect(Collectors.toList());
    }


    @Override
    public List<GroupMaterialApplyDto> selectByOrgIdAndApplyNos(long orgId, List<String> applyNos) {
        if(CollectionUtils.isEmpty(applyNos)){
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TbGroupMaterialApply> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbGroupMaterialApply::getApplyNo, applyNos);
        queryWrapper.eq(TbGroupMaterialApply::getOrgId, orgId);
        queryWrapper.orderByDesc(TbGroupMaterialApply::getApplyTime);

        return groupMaterialApplyConverter
                .groupMaterialApplyDtoListFromTbObj(tbGroupMaterialApplyMapper.selectList(queryWrapper));
    }

    public GroupMaterialApplyDto createMaterialApply() {
        final Date now = new Date();
        final Long groupId = LoginUserHandler.get().getGroupId();
        final String groupName = LoginUserHandler.get().getGroupName();
        final String nickname = LoginUserHandler.get().getNickname();
        final Long orgId = LoginUserHandler.get().getOrgId();
        final Long userId = LoginUserHandler.get().getUserId();
        final String orgName = LoginUserHandler.get().getOrgName();

        GroupMaterialApplyDto groupMaterialApply = new GroupMaterialApplyDto();
        groupMaterialApply.setApplyId(snowflakeService.genId());
        groupMaterialApply.setApplyNo(materialNoUtils.genApplyNo(LoginUserHandler.get().getOrgId()));
        groupMaterialApply.setApplyTime(now);
        groupMaterialApply.setApplyUserId(LoginUserHandler.get().getUserId());
        groupMaterialApply.setApplyUserName(nickname);
        groupMaterialApply.setPlanId(NumberUtils.LONG_ZERO);
        groupMaterialApply.setPlanNo(NumberUtils.LONG_ZERO);
        groupMaterialApply.setStatus(MaterialApplyStatusEnum.SUBMIT.getCode());
        groupMaterialApply.setIsDelete(YesOrNoEnum.NO.getCode());
        groupMaterialApply.setGroupId(groupId);
        groupMaterialApply.setGroupName(groupName);
        groupMaterialApply.setOrgId(orgId);
        groupMaterialApply.setOrgName(orgName);
        groupMaterialApply.setCreateDate(now);
        groupMaterialApply.setUpdateDate(now);
        groupMaterialApply.setUpdaterId(LoginUserHandler.get().getUserId());
        groupMaterialApply.setUpdaterName(nickname);
        groupMaterialApply.setCreatorId(userId);
        groupMaterialApply.setCreatorName(nickname);
        groupMaterialApply.setCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        groupMaterialApply.setChecker(StringUtils.EMPTY);
        groupMaterialApply.setCheckerId(NumberUtils.LONG_ZERO);
        return groupMaterialApply;
    }

    public GroupMaterialApplyDto convert(TbGroupMaterialApply tb) {
        if (Objects.isNull(tb)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(tb), GroupMaterialApplyDto.class);
    }


    // 转换物料申领单信息
    public GroupMaterialPlanVo convertGroupMaterialPlanVo(GroupMaterialApplyDto dto) {
        GroupMaterialPlanVo vo = new GroupMaterialPlanVo();

        vo.setPlanNo(dto.getApplyNo());
        vo.setGroupId(dto.getGroupId());
//        vo.setGroupCode(dto.getGroupCode());
        vo.setGroupName(dto.getGroupName());
        vo.setOrgId(dto.getOrgId());
        vo.setOrgName(dto.getOrgName());
        vo.setStatus(dto.getStatus());
        vo.setAuditId(dto.getCheckerId());
        vo.setAuditName(dto.getChecker());
        vo.setAuditDate(dto.getCheckDate());
        vo.setPlannerId(dto.getApplyUserId());
        vo.setPlannerName(dto.getApplyUserName());
        vo.setPlannerDate(dto.getApplyTime());
        vo.setReturnReason(StringUtils.EMPTY);
        vo.setReturnUserName(StringUtils.EMPTY);
        vo.setRemark(StringUtils.EMPTY);
        vo.setPlanType(MaterialApplyTypeEnum.MATERIAL_APPLY.getCode());
        vo.setReturnReason(dto.getReturnReason());

        List<GroupMaterialPlanVo.GroupMaterialPlanDetailVo> detailVos = new ArrayList<>();
        // 处理明细信息
        dto.getGroupMaterialApplyDetailList().forEach(e->{
            GroupMaterialPlanVo.GroupMaterialPlanDetailVo detailVo = new GroupMaterialPlanVo.GroupMaterialPlanDetailVo();
            detailVo.setGroupMaterialPlanId(e.getDetailId());
            detailVo.setMaterialCode(e.getMaterialCode());
            detailVo.setMaterialName(e.getMaterialName());
            detailVo.setSpecification(e.getSpecification());
            detailVo.setManufacturers(e.getManufacturers());
            detailVo.setMainUnit(e.getMainUnit());
            detailVo.setMainApplyNumber(e.getApplyMainNumber());
            detailVo.setAssistUnit(e.getAssistUnit());
            detailVo.setAssistApplyNumber(e.getApplyAssistNumber().toString());
            detailVo.setUnitConversionRate(e.getUnitConversionRate());
            detailVo.setCreatorId(e.getCreatorId());
            detailVo.setCreatorName(e.getCreatorName());
            detailVo.setCreateDate(e.getCreateDate());
            detailVo.setUpdaterId(e.getUpdaterId());
            detailVo.setUpdaterName(e.getUpdaterName());
            detailVo.setUpdateDate(e.getUpdateDate());
            // 如果自己有状态用自己的， 没有的话用申领单的
            detailVo.setStatus(ObjectUtils.defaultIfNull(e.getStatus(), dto.getStatus()));

            detailVos.add(detailVo);
        });

        vo.setGroupMaterialPlanDetailList(detailVos);
        return vo;
    }

}
