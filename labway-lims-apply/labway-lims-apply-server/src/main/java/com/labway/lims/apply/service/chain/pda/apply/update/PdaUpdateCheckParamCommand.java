package com.labway.lims.apply.service.chain.pda.apply.update;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PdaApplyStatusEnum;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.PdaTobeConfirmedApplyDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.api.service.pda.PdaTobeConfirmedApplyService;
import com.labway.lims.apply.service.chain.apply.add.CheckParamCommand;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.TestItemService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class PdaUpdateCheckParamCommand implements Command {

    @DubboReference
    private TestItemService testItemService;

    @Resource
    private PdaApplyService pdaApplyService;
    @Resource
    private CheckParamCommand checkParamCommand;

    @Resource
    private PdaTobeConfirmedApplyService pdaTobeConfirmedApplyService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;


    @Override
    public boolean execute(Context c) throws Exception {
        final UpdateApplyContext from = UpdateApplyContext.from(c);
        final TestApplyDto testApply = from.getTestApply();

        PdaApplyDto pdaApplyDto = pdaApplyService.selectById(testApply.getApplyId());

        if (Objects.isNull(pdaApplyDto)){
            throw new IllegalStateException("pda申请单不存在");
        }
        from.put(UpdateApplyContext.PDA_APPLY, pdaApplyDto);

        // 已确认的不能修改
        PdaTobeConfirmedApplyDto pdaTobeConfirmedApplyDto = pdaTobeConfirmedApplyService.selectByMasterBarcode(pdaApplyDto.getMasterBarcode());
        if(Objects.nonNull(pdaTobeConfirmedApplyDto) && Objects.equals(pdaTobeConfirmedApplyDto.getStatus(), PdaApplyStatusEnum.YES_CONFIRMED.getCode())){
            throw new IllegalStateException("该申请单已确认，不能修改");
        }

        // 校验项目信息
        checkApplyTestItemInfo(from, testApply);

        checkHspOrgInfo(testApply, from);

        return CONTINUE_PROCESSING;
    }

    private void checkHspOrgInfo(TestApplyDto testApply, UpdateApplyContext from) {
        final Long hspOrgId = testApply.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalArgumentException("请选择送检机构");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("选择的送检机构为空");
        }
        if (!Objects.equals(hspOrganization.getEnable(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalStateException(String.format("送检机构 [%s] 已停用", hspOrganization.getHspOrgName()));
        }

        from.put(UpdateApplyContext.HSP_ORG, hspOrganization);
    }

    private void checkApplyTestItemInfo(UpdateApplyContext from, TestApplyDto testApply) {

        final List<TestApplyDto.Item> items = testApply.getItems();

        if (CollectionUtils.isEmpty(items)) {
            throw new IllegalArgumentException("检验项目信息不能为空");
        }

        final int toSetCount = items.stream().map(TestApplyDto.Item::getTestItemId).collect(Collectors.toSet()).size();

        if (!Objects.equals(toSetCount, items.size())) {
            throw new IllegalArgumentException("检验项目出现重复");
        }

        final Map<Long, TestApplyDto.Item> updateTestApplyItemMap = items.stream().collect(Collectors.toMap(TestApplyDto.Item::getTestItemId, v -> v, (a, b) -> a));

        // 获取相关的检验项目信息
        final List<TestItemDto> testItems = testItemService.selectByTestItemIds(items.stream().map(TestApplyDto.Item::getTestItemId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(testItems)) {
            throw new IllegalStateException("检验项目不存在");
        }

        final Collection<Long> testItemIds = testItems.stream().map(TestItemDto::getTestItemId).collect(Collectors.toSet());
        updateTestApplyItemMap.forEach((k, v) -> {
            if (!testItemIds.contains(k)) {
                throw new IllegalStateException(String.format("检验项目 [%s] 不存在", v.getTestItemName()));
            }
        });

        checkParamCommand.updateSampleType(testApply, testItems);

        from.put(UpdateApplyContext.TEST_ITEMS, testItems);
    }
}
