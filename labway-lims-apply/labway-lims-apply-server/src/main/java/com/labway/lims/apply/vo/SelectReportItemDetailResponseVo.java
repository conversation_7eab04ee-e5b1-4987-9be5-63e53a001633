package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/4/18 15:53
 */
@Getter
@Setter
public class SelectReportItemDetailResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报告单项目编码
     */
    private String reportItemCode;
    /**
     * 报告项目名称
     */
    private String reportItemName;
    /**
     * 结果 （经过一系列的计算 转换最终得到的结果值）
     */
    private String result;
    /**
     * 单位
     */
    private String unit;
    /**
     * 检验判定 UP DOWN NORMAL
     * @see TestJudgeEnum
     */
    private String judge;
    /**
     * 结果范围
     */
    private String range;

}
