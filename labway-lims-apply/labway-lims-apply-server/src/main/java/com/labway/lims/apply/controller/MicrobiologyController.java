package com.labway.lims.apply.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.nacos.common.utils.Objects;
import com.google.common.collect.Lists;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.PrintMicrobiologyAttachPdfDto;
import com.labway.lims.apply.api.dto.PrintSampleNoInfoDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.config.MicrobiologyTwoPickerAttachConfig;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 微生物 二次分拣
 *
 * <AUTHOR>
 * @since 2023/7/20 20:01
 */
@Slf4j
@RestController
@RequestMapping("/microbiology")
public class MicrobiologyController extends BaseController {

    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private PdfReportService pdfReportService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private BloodCultureSampleService bloodCultureSampleService;
    @Resource
    private MicrobiologyTwoPickerAttachConfig attachConfig;
    private SystemParamService systemParamService;

    /**
     * 附件打印
     */
    @PostMapping("/print-attach-pdf")
    public Object printAttachPdf(@RequestBody Set<Long> applySampleIds) throws Exception {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Map.of("url", Collections.emptyList());
        }
        List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            return Map.of("url", Collections.emptyList());
        }
        // 正序 - 与列表排序方式相同
        applySamples.sort(Comparator.comparing(ApplySampleDto::getTwoPickDate));

        // 申请单样本项目
        final Map<Long, List<ApplySampleItemDto>> applySampleItemGroupingByApplySampleIdMap =
                applySampleItemService.selectByApplySampleIds(applySampleIds).stream()
                        .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 微生物样本
        final Map<Long, MicrobiologySampleDto> microbiologySamples = microbiologySampleService.selectByApplySampleIds(applySamples.stream().filter(e -> Objects.equals(e.getItemType(),
                ItemTypeEnum.MICROBIOLOGY.name())).map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet())).stream().collect(
                Collectors.toMap(MicrobiologySampleDto::getApplySampleId, Function.identity(), (key1, key2) -> key2));


        // 获取血培养样本
        final Set<String> bloodCultureTestItemCodes = systemParamService.selectBloodCultureTestItemCodes();
        final Map<Long, BloodCultureSampleDto> cultures = ((Callable<Map<Long, BloodCultureSampleDto>>) () -> {
            try {
                return bloodCultureSampleService.selectByApplySampleIds(applySamples.stream()
                                // 过滤血培养的样本   是血培养样本  或者是 配置的血培养项目
                                .filter(e -> {
                                    if (Objects.equals(e.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())) {
                                        return true;
                                    }
                                    final List<ApplySampleItemDto> applySampleItemDtos = applySampleItemGroupingByApplySampleIdMap.getOrDefault(e.getApplySampleId(), Collections.emptyList());
                                    // 项目不为空 并且全部都是血培养项目
                                    return CollectionUtils.isNotEmpty(applySampleItemDtos)
                                            && applySampleItemDtos.stream().allMatch(a -> bloodCultureTestItemCodes.contains(a.getTestItemCode()));
                                })
                                // 手机applySampleId
                                .map(ApplySampleDto::getApplySampleId)
                                .collect(Collectors.toSet()))
                        .stream().collect(Collectors.toMap(BloodCultureSampleDto::getApplySampleId, v -> v, (a, b) -> a));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            return Map.of();
        }).call();


        // 获取申请单信息
        final Set<Long> applyIds = applySamples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        final Map<Long, ApplyDto> applyDtoByApplyId = applyService.selectByApplyIds(applyIds).stream()
                .collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        final List<Dict> targetList = Lists.newArrayListWithCapacity(applySamples.size());
        // 循环样本 构建样本结果
        for (ApplySampleDto e : applySamples) {
            final ApplyDto applyDto = applyDtoByApplyId.get(e.getApplyId());

            // 获取微生物样本
            Object sample = microbiologySamples.get(e.getApplySampleId());
            // 是空的话， 获取血培养样本
            if (Objects.isNull(sample)) {
                sample = cultures.get(e.getApplySampleId());
            }

            // 样本项目
            List<ApplySampleItemDto> sampleItemDtoList =
                    applySampleItemGroupingByApplySampleIdMap.get(e.getApplySampleId());
            String testItemName = StringUtils.EMPTY;
            String testItemCode = StringUtils.EMPTY;
            if (Objects.nonNull(sampleItemDtoList)) {
                testItemName = sampleItemDtoList.stream().map(ApplySampleItemDto::getTestItemName)
                        .collect(Collectors.joining(","));
                testItemCode = sampleItemDtoList.get(NumberUtils.INTEGER_ZERO).getTestItemCode();
            }

            // 打印pdf结构对象
            PrintMicrobiologyAttachPdfDto temp = new PrintMicrobiologyAttachPdfDto();
            if (Objects.nonNull(applyDto)) {
                temp.setPatientName(applyDto.getPatientName());
                temp.setPatientSex(SexEnum.getByCode(applyDto.getPatientSex()).getDesc());
                temp.setPatientAge(PatientAges.toText(applyDto));
                temp.setPatientVisitCard(applyDto.getPatientVisitCard());
                temp.setDept(applyDto.getDept());
                temp.setPatientBed(applyDto.getPatientBed());
                temp.setHspOrgName(applyDto.getHspOrgName());
                temp.setDiagnosis(applyDto.getDiagnosis());
                temp.setRemark(applyDto.getRemark());
                temp.setSendDoctorName(applyDto.getSendDoctorName());
            }
            temp.setBarcode(e.getBarcode());
            temp.setSampleTypeName(e.getSampleTypeName());
            temp.setReceiveTime(
                    DateUtil.format(e.getCreateDate(), DatePattern.NORM_DATETIME_MINUTE_PATTERN));

            if (sample instanceof MicrobiologySampleDto) {
                temp.setSampleNo(((MicrobiologySampleDto) sample).getSampleNo());
            }

            if (sample instanceof BloodCultureSampleDto) {
                temp.setSampleNo(((BloodCultureSampleDto) sample).getSampleNo());
            }

            temp.setTestItemName(testItemName);
            temp.setTestItemCode(testItemCode);

            targetList.add(Dict.parse(temp)
                    .set("_apply", applyDto)
                    .set("_sample", sample)
                    .set("_sampleItems", sampleItemDtoList)
                    .set("_applySample", e));
        }

        List<String> url = Lists.newArrayList();
        targetList.forEach(item -> {
            final PdfReportParamDto param = new PdfReportParamDto();
            String code = PdfTemplateTypeEnum.PRINT_MICROBIOLOGY_ATTACH.getCode();
            param.put("target", item);
            final String testItemCode = item.getStr("testItemCode");
            if (Objects.nonNull(attachConfig.getAttachMap())
                    && StringUtils.isNotBlank(attachConfig.getAttachMap().get(testItemCode))) {
                code = attachConfig.getAttachMap().get(testItemCode);
            }
            final String pdfUrl = pdfReportService.build2Url(code, param, 3);
            url.add(pdfUrl);
        });

        return Map.of("url", url);
    }

    /**
     * 样本号打印
     */
    @PostMapping("/print-sample-no")
    public Object printSampleNo(@RequestBody List<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }
        List<Long> distinctApplySampleIds = applySampleIds.stream().distinct().collect(Collectors.toList());
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(distinctApplySampleIds);
        Map<Long, ApplySampleDto> applySampleDtoByApplySampleId =
                applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));
        Map<Long, MicrobiologySampleDto> microbiologySampleDtoByApplySampleId =
                microbiologySampleService.selectByApplySampleIds(distinctApplySampleIds).stream().collect(
                        Collectors.toMap(MicrobiologySampleDto::getApplySampleId, Function.identity(), (key1, key2) -> key2));

        Set<Long> applyIds = applySampleDtos.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        Map<Long, ApplyDto> applyDtoByApplyId = applyService.selectByApplyIds(applyIds).stream()
                .collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        Map<Long, List<ApplySampleItemDto>> applySampleItemGroupingByApplySampleId =
                applySampleItemService.selectByApplySampleIds(distinctApplySampleIds).stream()
                        .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 正序 - 与列表排序方式一样
        applySampleDtos.sort(Comparator.comparing(ApplySampleDto::getTwoPickDate));
        distinctApplySampleIds = applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());

        List<PrintSampleNoInfoDto> targetList = Lists.newArrayList();
        for (Long applySampleId : distinctApplySampleIds) {
            ApplySampleDto applySampleDto = applySampleDtoByApplySampleId.get(applySampleId);
            if (Objects.isNull(applySampleDto)) {
                // 无效样本 id ？？？
                continue;
            }
            MicrobiologySampleDto microbiologySampleDto = microbiologySampleDtoByApplySampleId.get(applySampleId);

            ApplyDto applyDto = applyDtoByApplyId.get(applySampleDto.getApplyId());

            List<ApplySampleItemDto> sampleItemDtoList = ObjectUtils
                    .defaultIfNull(applySampleItemGroupingByApplySampleId.get(applySampleId), Collections.emptyList());

            PrintSampleNoInfoDto temp = new PrintSampleNoInfoDto();
            if (Objects.nonNull(microbiologySampleDto)) {
                temp.setSampleNo(microbiologySampleDto.getSampleNo());
            }
            if (Objects.nonNull(applyDto)) {
                temp.setPatientName(applyDto.getPatientName());
            }
            temp.setTestItemNames(
                    sampleItemDtoList.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            targetList.add(temp);
        }

        return targetList;
    }

}
