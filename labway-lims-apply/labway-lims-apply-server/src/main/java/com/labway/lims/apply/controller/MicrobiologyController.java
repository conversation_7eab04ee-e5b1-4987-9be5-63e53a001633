package com.labway.lims.apply.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.nacos.common.utils.Objects;
import com.google.common.collect.Lists;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.PrintMicrobiologyAttachPdfDto;
import com.labway.lims.apply.api.dto.PrintSampleNoInfoDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.config.MicrobiologyTwoPickerAttachConfig;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 微生物 二次分拣
 *
 * <AUTHOR>
 * @since 2023/7/20 20:01
 */
@Slf4j
@RestController
@RequestMapping("/microbiology")
public class MicrobiologyController extends BaseController {

    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private PdfReportService pdfReportService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private BloodCultureSampleService bloodCultureSampleService;

    @Resource
    private MicrobiologyTwoPickerAttachConfig attachConfig;

    /**
     * 附件打印
     */
    @PostMapping("/print-attach-pdf")
    public Object printAttachPdf(@RequestBody Set<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Map.of("url", Collections.emptyList());
        }
        List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            return Map.of("url", Collections.emptyList());
        }
        // 正序 - 与列表排序方式相同
        applySamples.sort(Comparator.comparing(ApplySampleDto::getTwoPickDate));

        final Map<Long, Object> applySampleIdSampleMap = getSampleMap(applySamples);

        Set<Long> applyIds = applySamples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        Map<Long, ApplyDto> applyDtoByApplyId = applyService.selectByApplyIds(applyIds).stream()
                .collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        Map<Long, List<ApplySampleItemDto>> applySampleItemGroupingByApplySampleId =
                applySampleItemService.selectByApplySampleIds(applySampleIds).stream()
                        .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        List<Dict> targetList = Lists.newArrayListWithCapacity(applySamples.size());
        for (ApplySampleDto e : applySamples) {
            ApplyDto applyDto = applyDtoByApplyId.get(e.getApplyId());

            Object sample = applySampleIdSampleMap.get(e.getApplySampleId());
            if (Objects.isNull(sample)) {
                continue;
            }

            List<ApplySampleItemDto> sampleItemDtoList =
                    applySampleItemGroupingByApplySampleId.get(e.getApplySampleId());
            String testItemName = StringUtils.EMPTY;
            String testItemCode = StringUtils.EMPTY;
            if (Objects.nonNull(sampleItemDtoList)) {
                testItemName = sampleItemDtoList.stream().map(ApplySampleItemDto::getTestItemName)
                        .collect(Collectors.joining(","));
                testItemCode = sampleItemDtoList.get(NumberUtils.INTEGER_ZERO).getTestItemCode();
            }

            PrintMicrobiologyAttachPdfDto temp = new PrintMicrobiologyAttachPdfDto();
            if (Objects.nonNull(applyDto)) {
                temp.setPatientName(applyDto.getPatientName());
                temp.setPatientSex(SexEnum.getByCode(applyDto.getPatientSex()).getDesc());
                temp.setPatientAge(PatientAges.toText(applyDto));
                temp.setPatientVisitCard(applyDto.getPatientVisitCard());
                temp.setDept(applyDto.getDept());
                temp.setPatientBed(applyDto.getPatientBed());
                temp.setHspOrgName(applyDto.getHspOrgName());
                temp.setDiagnosis(applyDto.getDiagnosis());
                temp.setRemark(applyDto.getRemark());
                temp.setSendDoctorName(applyDto.getSendDoctorName());
            }
            temp.setBarcode(e.getBarcode());
            temp.setSampleTypeName(e.getSampleTypeName());
            temp.setReceiveTime(
                    DateUtil.format(e.getCreateDate(), DatePattern.NORM_DATETIME_MINUTE_PATTERN));

            if (sample instanceof MicrobiologySampleDto) {
                temp.setSampleNo(((MicrobiologySampleDto) sample).getSampleNo());
            }

            if (sample instanceof BloodCultureSampleDto) {
                temp.setSampleNo(((BloodCultureSampleDto) sample).getSampleNo());
            }

            if (sample instanceof SampleDto) {
                temp.setSampleNo(((SampleDto) sample).getSampleNo());
            }

            temp.setTestItemName(testItemName);
            temp.setTestItemCode(testItemCode);

            targetList.add(Dict.parse(temp)
                    .set("_apply", applyDto)
                    .set("_sample", sample)
                    .set("_sampleItems", sampleItemDtoList)
                    .set("_applySample", e));
        }

        List<String> url = Lists.newArrayList();
        targetList.forEach(item -> {
            final PdfReportParamDto param = new PdfReportParamDto();
            String code = PdfTemplateTypeEnum.PRINT_MICROBIOLOGY_ATTACH.getCode();
            param.put("target", item);
            final String testItemCode = item.getStr("testItemCode");
            if (Objects.nonNull(attachConfig.getAttachMap())
                    && StringUtils.isNotBlank(attachConfig.getAttachMap().get(testItemCode))) {
                code = attachConfig.getAttachMap().get(testItemCode);
            }
            final String pdfUrl = pdfReportService.build2Url(code, param, 3);
            url.add(pdfUrl);
        });

        return Map.of("url", url);
    }

    /**
     * 根据项目类型进行分组后去各个检验类型表中查询数据
     */
    private Map<Long, Object> getSampleMap(List<ApplySampleDto> applySamples) {
        // 根据项目类型分组
        final Map<String, List<Long>> itemTypeMap = applySamples.stream()
                .collect(Collectors.groupingBy(ApplySampleDto::getItemType, Collectors.mapping(ApplySampleDto::getApplySampleId, Collectors.toList())));

        Map<Long, Object> applySampleIdSampleMap = new HashMap<>();
        // 微生物
        applySampleIdSampleMap.putAll(microbiologySampleService.selectByApplySampleIds(itemTypeMap.get(ItemTypeEnum.MICROBIOLOGY.name()))
                .stream().collect(Collectors.toMap(MicrobiologySampleDto::getApplySampleId, Function.identity(), (a, b) -> a)));

        // 血培养
        applySampleIdSampleMap.putAll(bloodCultureSampleService.selectByApplySampleIds(itemTypeMap.get(ItemTypeEnum.BLOOD_CULTURE.name()))
                .stream().collect(Collectors.toMap(BloodCultureSampleDto::getApplySampleId, Function.identity(), (a, b) -> a)));

        // 常规
        applySampleIdSampleMap.putAll(sampleService.selectByApplySampleIds(itemTypeMap.get(ItemTypeEnum.ROUTINE.name()))
                .stream().collect(Collectors.toMap(SampleDto::getApplySampleId, Function.identity(), (a, b) -> a)));
        return applySampleIdSampleMap;
    }


    /**
     * 样本号打印
     */
    @PostMapping("/print-sample-no")
    public Object printSampleNo(@RequestBody List<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }
        List<Long> distinctApplySampleIds = applySampleIds.stream().distinct().collect(Collectors.toList());
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(distinctApplySampleIds);
        Map<Long, ApplySampleDto> applySampleDtoByApplySampleId =
                applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));
        Map<Long, MicrobiologySampleDto> microbiologySampleDtoByApplySampleId =
                microbiologySampleService.selectByApplySampleIds(distinctApplySampleIds).stream().collect(
                        Collectors.toMap(MicrobiologySampleDto::getApplySampleId, Function.identity(), (key1, key2) -> key2));

        Set<Long> applyIds = applySampleDtos.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        Map<Long, ApplyDto> applyDtoByApplyId = applyService.selectByApplyIds(applyIds).stream()
                .collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        Map<Long, List<ApplySampleItemDto>> applySampleItemGroupingByApplySampleId =
                applySampleItemService.selectByApplySampleIds(distinctApplySampleIds).stream()
                        .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 正序 - 与列表排序方式一样
        applySampleDtos.sort(Comparator.comparing(ApplySampleDto::getTwoPickDate));
        distinctApplySampleIds = applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());

        List<PrintSampleNoInfoDto> targetList = Lists.newArrayList();
        for (Long applySampleId : distinctApplySampleIds) {
            ApplySampleDto applySampleDto = applySampleDtoByApplySampleId.get(applySampleId);
            if (Objects.isNull(applySampleDto)) {
                // 无效样本 id ？？？
                continue;
            }
            MicrobiologySampleDto microbiologySampleDto = microbiologySampleDtoByApplySampleId.get(applySampleId);

            ApplyDto applyDto = applyDtoByApplyId.get(applySampleDto.getApplyId());

            List<ApplySampleItemDto> sampleItemDtoList = ObjectUtils
                    .defaultIfNull(applySampleItemGroupingByApplySampleId.get(applySampleId), Collections.emptyList());

            PrintSampleNoInfoDto temp = new PrintSampleNoInfoDto();
            if (Objects.nonNull(microbiologySampleDto)) {
                temp.setSampleNo(microbiologySampleDto.getSampleNo());
            }
            if (Objects.nonNull(applyDto)) {
                temp.setPatientName(applyDto.getPatientName());
            }
            temp.setTestItemNames(
                    sampleItemDtoList.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            targetList.add(temp);
        }

        return targetList;
    }

}
