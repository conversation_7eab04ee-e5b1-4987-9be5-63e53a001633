package com.labway.lims.apply.service.chain.pick.two.immunity;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.labway.lims.api.LabwayDateUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.ImmunityPickTypeEnum;
import com.labway.lims.api.enums.WeekEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.ImmunityTwoPickDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.service.chain.pick.two.TwoPickCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickContext;
import com.labway.lims.apply.service.chain.pick.two.TwoPickUrgentCommand;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <pre>
 * ImmunityTwoPickCommand
 * 免疫二次分拣（根据指定日期，预定日期，指定项目二次分拣）
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/30 13:56
 */
@Slf4j
@Component
public class ImmunityTwoPickCommand implements Command {

    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;

    @Resource
    private TwoPickCommand twoPickCommand;
    @Resource
    private TwoPickUrgentCommand twoPickUrgentCommand;

    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        if (!(c instanceof ImmunityTwoPickContext)) {
            return CONTINUE_PROCESSING;
        }
        final ImmunityTwoPickContext context = ImmunityTwoPickContext.from(c);
        final ImmunityTwoPickDto twoPickDto = context.getTwoPick();

        // 过滤掉加急的样本，这里只会有一个
        final List<ApplySampleTwoPickDto> applySampleTwoPickDtos = context.getApplySampleTwoPicks().stream()
                .filter(e -> BooleanUtils.isFalse(e.getIsUrgent())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(applySampleTwoPickDtos)) {
            return CONTINUE_PROCESSING;
        }

        // 原始的申请单样本
        final ApplySampleTwoPickDto applySampleTwoPickDto = context.getApplySampleTwoPicks().iterator().next();
        // 拿到非加急的项目
        final List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplySampleId(applySampleTwoPickDto.getApplySampleId());
        final Set<Long> testItemIds = applySampleItemDtos.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());
        final List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(
                applySampleItemDtos.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet()));

        // 根据样本检验项目 和 检验项目二次分拣时间配置进行拆分
        final LocalDateTime current = LocalDateTime.now();
        final Map<LocalDateTime, Set<Long>> testItemByTestDate = new HashMap<>();
        for (TestItemDto testItemDto : testItemDtos) {
            LocalDateTime pickDate = checkAndGetPickDate(current, testItemDto, twoPickDto);
            if (Objects.nonNull(pickDate)) {
                testItemByTestDate.computeIfAbsent(pickDate, k -> new HashSet<>()).add(testItemDto.getTestItemId());
            }
        }

        if (CollectionUtils.isEmpty(testItemByTestDate.keySet())) {
            // testItemByTestDate.put(current, testItemIds);
            throw new IllegalStateException("免疫二次分拣，没有可分拣的项目");
        }

        try {
            // 先校验一下样本号是不是已经被占用了
            checkSampleNoAvailable(testItemByTestDate, context);
        } catch (Exception e) {
            // 如果加急的样本已经复制出来，那么这里需要回滚掉
            twoPickUrgentCommand.rollback(context);
            throw e;
        }

        final LinkedList<Long> ids = snowflakeService.genIds(3000);

        testItemByTestDate.forEach((pickDate, itemIds) -> {
            if (CollectionUtils.isEmpty(applySampleItemDtos)) {
                log.error("样本 [{}] 项目已分拣完 不能继续分拣 分拣时间 [{}] 项目 [{}]",
                        applySampleTwoPickDto.getBarcode(), pickDate,
                        testItemDtos.stream().filter(e -> itemIds.contains(e.getTestItemId()))
                                .map(TestItemDto::getTestItemName).collect(Collectors.joining(StringPool.COMMA)));
                throw new IllegalStateException("样本项目已分拣完，无法继续分拣");
            }

            final List<ApplySampleItemDto> itemDtos = applySampleItemDtos.stream()
                    .filter(e -> itemIds.contains(e.getTestItemId())).collect(Collectors.toList());

            applySampleItemDtos.removeIf(e -> itemIds.contains(e.getTestItemId()));
            if (CollectionUtils.isNotEmpty(applySampleItemDtos)) {
                // 不为空，说明样本还有项目没有分拣，则将本次分拣的项目复制到新的申请单样本上
                final ApplySampleDto applySampleDto = copyApplySampleDto(ids, context, itemDtos);

                final ApplySampleTwoPickDto stp = new ApplySampleTwoPickDto();
                stp.setApplySampleId(applySampleDto.getApplySampleId());
                stp.setInstrumentGroupId(context.getInstrumentGroup().getInstrumentGroupId());
                stp.setInstrumentGroupName(context.getInstrumentGroup().getInstrumentGroupName());
                stp.setSampleNo(context.getTwoPick().getSampleNo());
                stp.setBarcode(applySampleDto.getBarcode());
                stp.setGroupId(applySampleDto.getGroupId());
                stp.setIsTransform(false);
                stp.setIsUrgent(false);
                stp.setImmunityTwoPickDate(pickDate);
                context.getApplySampleTwoPicks().add(stp);

                context.getImmunityTwoPickDate().put(stp.getApplySampleId(), LabwayDateUtil.toDate(pickDate));
            } else {
                // 如果剩余项目为空，则说明项目已经分拣完，分拣到了 pickDate
                applySampleTwoPickDto.setImmunityTwoPickDate(pickDate);
                context.getImmunityTwoPickDate().put(applySampleTwoPickDto.getApplySampleId(), LabwayDateUtil.toDate(pickDate));
            }
        });

        // 如果还有项目，则说明本地免疫二次分拣，还有不匹配的项目，等后续分拣，本次分拣不分拣这个原始样本
        if (CollectionUtils.isNotEmpty(applySampleItemDtos)) {
            context.getApplySampleTwoPicks().removeIf(e -> Objects.equals(e.getApplySampleId(), applySampleTwoPickDto.getApplySampleId()));

            // 标记这个样本是免疫二次分拣
            final ApplySampleDto as = new ApplySampleDto();
            as.setIsImmunityTwoPick(YesOrNoEnum.YES.getCode());
            as.setApplySampleId(applySampleTwoPickDto.getApplySampleId());
            applySampleService.updateByApplySampleId(as);
        }

        // 标记免疫二次分拣
        context.markImmunityTwoPick();

        return CONTINUE_PROCESSING;
    }

    /**
     * 检查样本号是否可用
     * @param testItemByTestDate
     * @param context
     */
    private void checkSampleNoAvailable(final Map<LocalDateTime, Set<Long>> testItemByTestDate, final ImmunityTwoPickContext context) {
        final ImmunityTwoPickDto twoPickDto = context.getTwoPick();
        final InstrumentGroupDto instrumentGroup = context.getInstrumentGroup();

        // 判断免疫分拣的样本 指定的样本号是否已存在
        if (StringUtils.isNotBlank(twoPickDto.getSampleNo()) && MapUtil.isNotEmpty(testItemByTestDate)) {
            final String sampleNo = twoPickDto.getSampleNo();
            testItemByTestDate.forEach((pickDate, itemIds) -> {
                if (!twoPickCommand.canActiveSampleNo(instrumentGroup.getGroupId(), sampleNo,
                        ObjectUtils.defaultIfNull(LabwayDateUtil.toDate(pickDate), context.getTwoPickDate()).toInstant()
                                .atZone(ZoneId.systemDefault()).toLocalDate())) {
                    throw new IllegalStateException(String.format("样本号 [%s] 已存在", sampleNo));
                }
            });
        }

        // 急诊样本 样本号判断
        if (StringUtils.isNotBlank(twoPickDto.getUrgentSampleNo()) &&
                context.getApplySampleTwoPicks().stream().anyMatch(ApplySampleTwoPickDto::getIsUrgent)) {
            if (!twoPickCommand.canActiveSampleNo(instrumentGroup.getGroupId(), twoPickDto.getUrgentSampleNo(),
                    context.getTwoPickDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())) {
                throw new IllegalStateException(String.format("样本号 [%s] 已存在", twoPickDto.getUrgentSampleNo()));
            }
        }
    }

    private ApplySampleDto copyApplySampleDto(LinkedList<Long> ids, TwoPickContext context, List<ApplySampleItemDto> items) {
        // 复制样本
        final ApplySampleDto applySampleDto = new ApplySampleDto();
        BeanUtils.copyProperties(context.getApplySample(), applySampleDto);
        applySampleDto.setApplySampleId(ids.pop());
        applySampleDto.setUrgent(YesOrNoEnum.NO.getCode());
        // 免疫二次分拣标记
        applySampleDto.setIsImmunityTwoPick(YesOrNoEnum.YES.getCode());
        applySampleDto.setUpdateDate(new Date());
        applySampleDto.setUpdaterId(LoginUserHandler.get().getUserId());
        applySampleDto.setUpdaterName(LoginUserHandler.get().getNickname());
        applySampleService.addApplySamples(List.of(applySampleDto));

        // 复制一个逻辑试管架位置
        final RackLogicSpaceDto rackLogicSpace = new RackLogicSpaceDto();
        BeanUtils.copyProperties(context.getRackLogicSpace(), rackLogicSpace);
        rackLogicSpace.setRackLogicSpaceId(snowflakeService.genId());
        rackLogicSpace.setApplySampleId(applySampleDto.getApplySampleId());
        rackLogicSpace.setRackLogicId(context.getRackLogic().getRackLogicId());
        rackLogicSpaceService.addRackLogicSpace(rackLogicSpace);

        // 添加检验项目
        applySampleItemService.addApplySampleItems(items.stream().map(e -> {
            final ApplySampleItemDto applySampleItem = new ApplySampleItemDto();
            BeanUtils.copyProperties(e, applySampleItem);
            applySampleItem.setApplySampleId(applySampleDto.getApplySampleId());
            applySampleItem.setApplySampleItemId(ids.pop());
            return applySampleItem;
        }).collect(Collectors.toList()));

        // 复制条码环节
        sampleFlowService.addSampleFlows(sampleFlowService.selectByApplySampleId(context.getTwoPick().getApplySampleId()).stream().map(e -> {
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            BeanUtils.copyProperties(e, sampleFlow);
            sampleFlow.setApplySampleId(applySampleDto.getApplySampleId());
            sampleFlow.setSampleFlowId(ids.pop());
            return sampleFlow;
        }).collect(Collectors.toList()));

        final Set<Long> copiedTestItemIds = items.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());
        // 在原样本上 删除紧急项目，因为紧急项目已经到另外一个样本上了
        applySampleItemService.deleteByApplySampleItemIds(context.getApplySampleItems().stream()
                .filter(e -> copiedTestItemIds.contains(e.getTestItemId()))
                .map(ApplySampleItemDto::getApplySampleItemId)
                .collect(Collectors.toSet()));

        return applySampleDto;
    }

    public LocalDateTime checkAndGetPickDate(LocalDateTime current, TestItemDto testItemDto, ImmunityTwoPickDto twoPickDto) {
        return TwoPickDateFacade.getPicker(twoPickDto.getType()).getTwoPickDate(current, testItemDto, twoPickDto);
    }

    interface TwoPickDateFacade {

        Map<ImmunityPickTypeEnum, TwoPickDateFacade> PICKER = new HashMap<>() {{
            // 免疫二次分拣
            put(ImmunityPickTypeEnum.NORMAL, new TwoPickNormal());
            // 免疫二次分拣（指定预定日期）
            put(ImmunityPickTypeEnum.FORCE_BY_DATE, new TwoPickForceByDate());
            // 免疫二次分拣（指定检验项目）
            put(ImmunityPickTypeEnum.FORCE_BY_ITEM, new TwoPickForceByItem());
        }};

        static TwoPickDateFacade getPicker(String type) {
            return PICKER.get(ImmunityPickTypeEnum.of(type));
        }

        default LocalDateTime getTwoPickDate(LocalDateTime current, TestItemDto testItemDto, ImmunityTwoPickDto twoPickDto) {
            // 检验项目指定 二次分拣时间（周）
            final String twoPickDay = ObjectUtils.defaultIfNull(testItemDto.getTwoPickDay(), StringPool.EMPTY);
            final List<Integer> weeks = StringUtils.isEmpty(twoPickDay) ? List.of() :
                    Arrays.stream(twoPickDay.split(StringPool.COMMA)).map(Integer::parseInt).collect(Collectors.toList());

            // 自定义分拣日期
            final Date twoPickDate = twoPickDto.getTwoPickDate();
            // 预定日期
            final List<Date> presetDates = twoPickDto.getPresetDates();

            if (Objects.isNull(twoPickDate)) {
                // 没有自定义分拣日期
                // 没有指定预定日期 如果检验项目设置了当日，则分拣到当天
                if (WeekEnum.isCurrent(twoPickDay)) {
                    return current;
                }

                final LocalTime twoPickTime = LocalTime.parse(testItemDto.getTwoPickTime());
                // 检验项目指定 二次分拣时间，并且匹配 指定周 和 指定时间
                if (weeks.contains(WeekEnum.getByDayOfWeek(current.getDayOfWeek()).getCode()) && current.toLocalTime().isBefore(twoPickTime)) {
                    return current;
                }
                // 分拣不到当天，则当天往后推7天，进行匹配（此时不用校验时间，只需要校验日期）
                for (Integer weekDays : WeekEnum.WEEK_DAYS) {
                    final LocalDate localDate = current.toLocalDate().plusDays(weekDays);
                    // 检验项目指定 二次分拣时间，并且匹配 指定周 和 指定时间
                    if (weeks.contains(WeekEnum.getByDayOfWeek(localDate.getDayOfWeek()).getCode())) {
                        return LocalDateTime.of(localDate, current.toLocalTime());
                    }
                }
                throw new IllegalArgumentException(String.format("检验项目 [%s] 配置二次分拣时间有误", testItemDto.getTestItemName()));
            } else {
                // 分拣到指定日期
                final LocalDateTime twoPickLocalDateTime = DateUtil.date(twoPickDate).toLocalDateTime();

                if (WeekEnum.isCurrent(twoPickDay)) {
                    return twoPickLocalDateTime;
                }

                // 指定了预定日期，检验项目二次分拣日期 需要匹配 预定日期
                if (CollectionUtils.isNotEmpty(presetDates)) {
                    final List<Integer> presetWeeks = presetDates.stream().map(WeekEnum::from).map(WeekEnum::getCode).collect(Collectors.toList());
                    if (weeks.stream().anyMatch(presetWeeks::contains)) {
                        return twoPickLocalDateTime;
                    }
                    return null;
                }

                // 未指定日期，检验项目二次分拣日期 需要匹配 分拣日期
                if (weeks.contains(WeekEnum.getByDayOfWeek(twoPickLocalDateTime.getDayOfWeek()).getCode())) {
                    return twoPickLocalDateTime;
                }

                return null;
            }
        }
    }

    /**
     * 免疫二次分拣
     */
    static class TwoPickNormal implements TwoPickDateFacade {
        @Override
        public LocalDateTime getTwoPickDate(LocalDateTime current, TestItemDto testItemDto, ImmunityTwoPickDto twoPickDto) {
            return TwoPickDateFacade.super.getTwoPickDate(current, testItemDto, twoPickDto);
        }
    }

    /**
     * 免疫二次分拣（指定预定日期）
     */
    static class TwoPickForceByDate implements TwoPickDateFacade {
        @Override
        public LocalDateTime getTwoPickDate(LocalDateTime current, TestItemDto testItemDto, ImmunityTwoPickDto twoPickDto) {
            // 检验项目指定 二次分拣时间（周）
            final String twoPickDay = ObjectUtils.defaultIfNull(testItemDto.getTwoPickDay(), StringPool.EMPTY);
            // 预定日期
            final List<Date> presetDates = twoPickDto.getPresetDates();
            // 指定二次分拣日期
            final Date twoPickDate = twoPickDto.getTwoPickDate();

            final LocalDateTime pickDateTime = TwoPickDateFacade.super.getTwoPickDate(current, testItemDto, twoPickDto);
            if (Objects.isNull(pickDateTime)) {
                return null;
            }
            if (CollectionUtils.isEmpty(presetDates)) {
                return pickDateTime;
            }

            // 如果检验项目设置的当日，那么需要 指定预定日期（星期） 与 分拣日期（星期匹配）
            if (WeekEnum.isCurrent(twoPickDay)) {
                if (presetDates.stream().map(WeekEnum::from).anyMatch(week -> Objects.equals(week, WeekEnum.from(twoPickDate)))) {
                    return pickDateTime;
                }
                return null;
            }
            return pickDateTime;
        }
    }

    /**
     * 免疫二次分拣（指定检验项目）
     */
    static class TwoPickForceByItem implements TwoPickDateFacade {
        @Override
        public LocalDateTime getTwoPickDate(LocalDateTime current, TestItemDto testItemDto, ImmunityTwoPickDto twoPickDto) {
            final List<String> testItemCodes = twoPickDto.getTestItemCodes();

            // 如果未指定检验项目，走正常免疫二次分拣的逻辑
            if (CollectionUtils.isEmpty(testItemCodes)) {
                return TwoPickDateFacade.super.getTwoPickDate(current, testItemDto, twoPickDto);
            }

            // 自定义分拣日期
            final Date twoPickDate = twoPickDto.getTwoPickDate();

            if (testItemCodes.contains(testItemDto.getTestItemCode())) {
                return ObjectUtils.defaultIfNull(DateUtil.date(twoPickDate).toLocalDateTime(), current);
            }

            return null;
        }
    }

}
