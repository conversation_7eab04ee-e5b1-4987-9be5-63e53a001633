package com.labway.lims.apply.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 体检花名册 导入
 * 
 * <AUTHOR>
 * @since 2023/3/31 11:08
 */
@Getter
@Setter
public class ImportPhysicalRegisterVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final String PATIENT_NAME_COLUMN = "姓名";
    private static final String PATIENT_SEX_COLUMN = "性别";
    private static final String PATIENT_AGE_COLUMN = "年龄";
    private static final String APPLY_TYPE_COLUMN = "就诊类型";
    private static final String PATIENT_VISIT_CARD_COLUMN = "门诊/住院号";
    private static final String PATIENT_CARD_COLUMN = "身份证号";
    private static final String SAMPLE_SORT_COLUMN = "标本序号";
    private static final String TEST_PACKAGE_COLUMN = "套餐名称";
    private static final String TEST_PACKAGE_DESC_COLUMN = "套餐说明";
    private static final String DEPT_COLUMN = "班级";
    private static final String REMARK_COLUMN = "备注";
    private static final String PATIENT_MOBILE_COLUMN = "电话";
    private static final String APPLICANT_COLUMN = "申请人";
    private static final String CLINICAL_DIAGNOSIS_COLUMN = "临床诊断";
    private static final String PATIENT_BED_COLUMN = "床号";
    private static final String PATIENT_ADDRESS_COLUMN = "地址";

    /**
     * 名称
     */
    @ExcelProperty(value = PATIENT_NAME_COLUMN, index = 0)
    private String patientName;
    /**
     * 性别
     */
    @ExcelProperty(value = PATIENT_SEX_COLUMN, index = 1)
    private String patientSex;
    /**
     * 年龄
     */
    @ExcelProperty(value = PATIENT_AGE_COLUMN, index = 2)
    private String patientAge;
    /**
     * 就诊类型
     */
    @ExcelProperty(value = APPLY_TYPE_COLUMN, index = 3)
    private String applyType;
    /**
     * 门诊/住院号
     */
    @ExcelProperty(value = PATIENT_VISIT_CARD_COLUMN, index = 4)
    private String patientVisitCard;
    /**
     * 身份证号
     */
    @ExcelProperty(value = PATIENT_CARD_COLUMN, index = 5)
    private String patientCard;
    /**
     * 标本序号
     */
    @ExcelProperty(value = SAMPLE_SORT_COLUMN, index = 6)
    private String sampleSort;
    /**
     * 套餐，仅说明作用
     */
    @ExcelProperty(value = TEST_PACKAGE_COLUMN, index = 7)
    private String testPackage;
    /**
     * 套餐说明
     */
    @ExcelProperty(value = TEST_PACKAGE_DESC_COLUMN, index = 8)
    private String testPackageDesc;
    /**
     * 部门或班级
     */
    @ExcelProperty(value = DEPT_COLUMN, index = 9)
    private String dept;
    /**
     * 备注
     */
    @ExcelProperty(value = REMARK_COLUMN, index = 10)
    private String remark;
    /**
     * 手机号
     */
    @ExcelProperty(value = PATIENT_MOBILE_COLUMN, index = 11)
    private String patientMobile;
    /**
     * 申请人
     */
    @ExcelProperty(value = APPLICANT_COLUMN, index = 12)
    private String applicant;
    /**
     * 临床诊断
     */
    @ExcelProperty(value = CLINICAL_DIAGNOSIS_COLUMN, index = 13)
    private String clinicalDiagnosis;
    /**
     * 床号
     */
    @ExcelProperty(value = PATIENT_BED_COLUMN, index = 14)
    private String patientBed;
    /**
     * 地址
     */
    @ExcelProperty(value = PATIENT_ADDRESS_COLUMN, index = 15)
    private String patientAddress;

    public static List<String> getHeadList() {
        return Lists.newArrayList(PATIENT_NAME_COLUMN, PATIENT_SEX_COLUMN, PATIENT_AGE_COLUMN, APPLY_TYPE_COLUMN,
            PATIENT_VISIT_CARD_COLUMN, PATIENT_CARD_COLUMN, SAMPLE_SORT_COLUMN, TEST_PACKAGE_COLUMN,
            TEST_PACKAGE_DESC_COLUMN, DEPT_COLUMN, REMARK_COLUMN, PATIENT_MOBILE_COLUMN, APPLICANT_COLUMN,
            CLINICAL_DIAGNOSIS_COLUMN, PATIENT_BED_COLUMN, PATIENT_ADDRESS_COLUMN);
    }

    public ImportPhysicalRegisterVo(Map<Integer, String> dataMap, Map<String, Integer> reverseHeadMap) {
        this.patientName = reverseHeadMap.containsKey(PATIENT_NAME_COLUMN)
            ? dataMap.get(reverseHeadMap.get(PATIENT_NAME_COLUMN)) : null;

        this.patientSex =
            reverseHeadMap.containsKey(PATIENT_SEX_COLUMN) ? dataMap.get(reverseHeadMap.get(PATIENT_SEX_COLUMN)) : null;

        this.patientAge =
            reverseHeadMap.containsKey(PATIENT_AGE_COLUMN) ? dataMap.get(reverseHeadMap.get(PATIENT_AGE_COLUMN)) : null;

        this.applyType =
            reverseHeadMap.containsKey(APPLY_TYPE_COLUMN) ? dataMap.get(reverseHeadMap.get(APPLY_TYPE_COLUMN)) : null;

        this.patientVisitCard = reverseHeadMap.containsKey(PATIENT_VISIT_CARD_COLUMN)
            ? dataMap.get(reverseHeadMap.get(PATIENT_VISIT_CARD_COLUMN)) : null;

        this.patientCard = reverseHeadMap.containsKey(PATIENT_CARD_COLUMN)
            ? dataMap.get(reverseHeadMap.get(PATIENT_CARD_COLUMN)) : null;

        this.sampleSort =
            reverseHeadMap.containsKey(SAMPLE_SORT_COLUMN) ? dataMap.get(reverseHeadMap.get(SAMPLE_SORT_COLUMN)) : null;

        this.testPackage = reverseHeadMap.containsKey(TEST_PACKAGE_COLUMN)
            ? dataMap.get(reverseHeadMap.get(TEST_PACKAGE_COLUMN)) : null;

        this.testPackageDesc = reverseHeadMap.containsKey(TEST_PACKAGE_DESC_COLUMN)
            ? dataMap.get(reverseHeadMap.get(TEST_PACKAGE_DESC_COLUMN)) : null;

        this.dept = reverseHeadMap.containsKey(DEPT_COLUMN) ? dataMap.get(reverseHeadMap.get(DEPT_COLUMN)) : null;

        this.remark = reverseHeadMap.containsKey(REMARK_COLUMN) ? dataMap.get(reverseHeadMap.get(REMARK_COLUMN)) : null;

        this.patientMobile = reverseHeadMap.containsKey(PATIENT_MOBILE_COLUMN)
            ? dataMap.get(reverseHeadMap.get(PATIENT_MOBILE_COLUMN)) : null;

        this.applicant =
            reverseHeadMap.containsKey(APPLICANT_COLUMN) ? dataMap.get(reverseHeadMap.get(APPLICANT_COLUMN)) : null;

        this.clinicalDiagnosis = reverseHeadMap.containsKey(CLINICAL_DIAGNOSIS_COLUMN)
            ? dataMap.get(reverseHeadMap.get(CLINICAL_DIAGNOSIS_COLUMN)) : null;

        this.patientBed =
            reverseHeadMap.containsKey(PATIENT_BED_COLUMN) ? dataMap.get(reverseHeadMap.get(PATIENT_BED_COLUMN)) : null;

        this.patientAddress = reverseHeadMap.containsKey(PATIENT_ADDRESS_COLUMN)
            ? dataMap.get(reverseHeadMap.get(PATIENT_ADDRESS_COLUMN)) : null;
    }

}
