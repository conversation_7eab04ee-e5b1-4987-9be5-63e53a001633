package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.OnePickRackLogicInfoDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.bo.OnePickWaitingHandoverRackLogicBo;
import com.labway.lims.apply.mapper.TbRackLogicMapper;
import com.labway.lims.apply.model.TbRackLogic;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "rack-logic")
public class RackLogicServiceImpl extends ServiceImpl<TbRackLogicMapper, TbRackLogic> implements RackLogicService {
    @Resource
    private TbRackLogicMapper rackLogicMapper;

    @Resource
    private RackLogicSpaceService rackLogicSpaceService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Nullable
    @Override
    public RackLogicDto selectAvailableRackLogic(long groupId, long userId) {
        final List<TbRackLogic> list =
                rackLogicMapper.selectList(new LambdaQueryWrapper<TbRackLogic>().eq(TbRackLogic::getNextGroupId, groupId)
                        .eq(TbRackLogic::getCreatorId, userId).eq(TbRackLogic::getPosition, RackLogicPositionEnum.ONE_PICKING.getCode()));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return convert(list.iterator().next());
    }

    @Override
    @CacheEvict(allEntries = true)
    public long addRackLogic(RackLogicDto dto) {
        final TbRackLogic rackLogic = new TbRackLogic();

        BeanUtils.copyProperties(dto, rackLogic);

        rackLogic.setRackLogicId(ObjectUtils.defaultIfNull(dto.getRackLogicId(), snowflakeService.genId()));
        rackLogic.setCreateDate(new Date());
        rackLogic.setUpdateDate(new Date());
        rackLogic.setCreatorId(LoginUserHandler.get().getUserId());
        rackLogic.setCreatorName(LoginUserHandler.get().getNickname());
        rackLogic.setUpdaterId(LoginUserHandler.get().getUserId());
        rackLogic.setUpdaterName(LoginUserHandler.get().getNickname());
        rackLogic.setIsDelete(YesOrNoEnum.NO.getCode());

        if (rackLogicMapper.insert(rackLogic) < 1) {
            throw new IllegalStateException("添加逻辑试管架失败");
        }

        log.info("用户 [{}] 新增逻辑试管架 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(rackLogic));

        return rackLogic.getRackLogicId();
    }

    @Override
    public long addRackLogics(Collection<RackLogicDto> dtos) {
        if(CollectionUtils.isEmpty(dtos)) {
            return NumberUtils.LONG_ZERO;
        }
        final List<TbRackLogic> rackLogicList = dtos.stream()
                .map(dto -> {
                    final TbRackLogic rackLogic = new TbRackLogic();

                    BeanUtils.copyProperties(dto, rackLogic);
                    if (Objects.isNull(rackLogic.getRackLogicId())) {
                        rackLogic.setRackLogicId(snowflakeService.genId());
                    }
                    rackLogic.setCreateDate(new Date());
                    rackLogic.setUpdateDate(new Date());
                    rackLogic.setCreatorId(LoginUserHandler.get().getUserId());
                    rackLogic.setCreatorName(LoginUserHandler.get().getNickname());
                    rackLogic.setUpdaterId(LoginUserHandler.get().getUserId());
                    rackLogic.setUpdaterName(LoginUserHandler.get().getNickname());
                    rackLogic.setIsDelete(YesOrNoEnum.NO.getCode());
                    return rackLogic;
                })
                .collect(Collectors.toList());
        // 批量添加逻辑试管架
        super.saveBatch(rackLogicList);
        return rackLogicList.size();
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByRackLogicId:' + #rackLogicId")
    public RackLogicDto selectByRackLogicId(long rackLogicId) {
        return convert(rackLogicMapper.selectById(rackLogicId));
    }

    @Override
    public List<RackLogicDto> selectByRackId(long rackId) {
        if (rackId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRackLogic> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbRackLogic::getRackId, rackId);
        queryWrapper.eq(TbRackLogic::getIsDelete, YesOrNoEnum.NO.getCode());
        return convert(rackLogicMapper.selectList(queryWrapper));
    }

    @Override
    public List<RackLogicDto> selectByRackIds(Collection<Long> rackIds) {
        if (CollectionUtils.isEmpty(rackIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbRackLogic> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbRackLogic::getRackId, rackIds);
        queryWrapper.eq(TbRackLogic::getIsDelete, YesOrNoEnum.NO.getCode());

        return convert(rackLogicMapper.selectList(queryWrapper));
    }

    @Override
    public List<RackLogicDto> selectByRackLogicIds(Collection<Long> rackLogicIds) {
        return rackLogicMapper
                .selectList(new LambdaQueryWrapper<TbRackLogic>().in(TbRackLogic::getRackLogicId, rackLogicIds)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByRackLogicId(RackLogicDto dto) {

        final TbRackLogic rackLogic = new TbRackLogic();
        BeanUtils.copyProperties(dto, rackLogic);
        rackLogic.setUpdateDate(new Date());
        rackLogic.setUpdaterId(LoginUserHandler.get().getUserId());
        rackLogic.setUpdaterName(LoginUserHandler.get().getNickname());

        if (rackLogicMapper.updateById(rackLogic) < 1) {
            return false;
        }

        log.info("用户 [{}] 更新逻辑试管架 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(rackLogic));

        return true;
    }

    @Override
    @Cacheable(key = "'selectByNextGroupId:' + #nextGroupId")
    public List<RackLogicDto> selectByNextGroupId(long nextGroupId) {
        return rackLogicMapper
                .selectList(new LambdaQueryWrapper<TbRackLogic>().eq(TbRackLogic::getNextGroupId, nextGroupId)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Cacheable(key = "'selectByCurrentGroupId:' + #currentGroupId")
    public List<RackLogicDto> selectByCurrentGroupId(long currentGroupId) {
        return rackLogicMapper
                .selectList(new LambdaQueryWrapper<TbRackLogic>().eq(TbRackLogic::getCurrentGroupId, currentGroupId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicDto> selectByCurrentGroupIdAndPosition(long currentGroupId, int position) {
        return rackLogicMapper.selectList(new LambdaQueryWrapper<TbRackLogic>()
                        .eq(TbRackLogic::getCurrentGroupId, currentGroupId).eq(TbRackLogic::getPosition, position)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicDto> selectByNextGroupIdAndPosition(long nextGroupId, int position) {
        return rackLogicMapper.selectList(new LambdaQueryWrapper<TbRackLogic>()
                        .eq(TbRackLogic::getNextGroupId, nextGroupId).eq(TbRackLogic::getPosition, position)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicDto> selectByRackCodeAndNextGroupIdAndPosition(String rackCode, long nextGroupId,
                                                                        int position) {
        return rackLogicMapper
                .selectList(new LambdaQueryWrapper<TbRackLogic>().eq(TbRackLogic::getRackCode, rackCode)
                        .eq(TbRackLogic::getNextGroupId, nextGroupId).eq(TbRackLogic::getPosition, position))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicDto> selectByRackCodeIdAndPosition(String rackCode, int position) {
        return rackLogicMapper.selectList(new LambdaQueryWrapper<TbRackLogic>().eq(TbRackLogic::getRackCode, rackCode)
                .eq(TbRackLogic::getPosition, position)).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicDto> selectByCreatorIdAndPosition(long userId, int position) {
        return rackLogicMapper.selectList(new LambdaQueryWrapper<TbRackLogic>().eq(TbRackLogic::getCreatorId, userId)
                .eq(TbRackLogic::getPosition, position)).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicDto> selectByPositions(List<Integer> positions) {
        return rackLogicMapper.selectList(new LambdaQueryWrapper<TbRackLogic>()
                .in(TbRackLogic::getPosition, positions)).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<OnePickRackLogicInfoDto> selectOnePickWaitingHandoverRackLogics(Date beginOnePickDate,
                                                                                Date endOnePickDate, long groupId) {
        final Map<Long, List<OnePickWaitingHandoverRackLogicBo>> groupRackLogicSamples =
                rackLogicMapper.selectOnePickWaitingHandoverRackLogics(beginOnePickDate, endOnePickDate, groupId).stream()
                        .collect(Collectors.groupingBy(OnePickWaitingHandoverRackLogicBo::getRackLogicId));
        if (MapUtils.isEmpty(groupRackLogicSamples)) {
            return Collections.emptyList();
        }


        return groupRackLogicSamples.keySet().stream()
                .map(e -> {
                    final OnePickRackLogicInfoDto dto = new OnePickRackLogicInfoDto();
                    final List<OnePickWaitingHandoverRackLogicBo> bos = groupRackLogicSamples.get(e);
                    final OnePickWaitingHandoverRackLogicBo bo = bos.iterator().next();

                    // 获取最后一个操作样本的
                    final OnePickWaitingHandoverRackLogicBo last =
                            bos.stream().max(Comparator.comparing(OnePickWaitingHandoverRackLogicBo::getOnePickDate)).stream()
                                    .findFirst().orElse(bo);

                    dto.setRackId(bo.getRackId());
                    dto.setRackLogicId(bo.getRackLogicId());
                    dto.setRackCode(bo.getRackCode());
                    dto.setGroupId(bo.getNextGroupId());
                    dto.setGroupName(bo.getNextGroupName());
                    dto.setOnePickerName(last.getOnePickerName());
                    dto.setCount(bos.size());
                    dto.setOnePickDate(last.getOnePickDate());

                    return dto;
                })
                // 根据更新日期正序
                .sorted(Comparator.comparing(o -> groupRackLogicSamples.get(o.getRackLogicId()).iterator().next().getUpdateDate()))
                .collect(Collectors.toList());
    }

    @Override
    public List<RackLogicDto> selectByUpdateDateAndNextGroupId(Date beginUpdateDate, Date endUpdateDate,
                                                               long nextGroupId) {
        return rackLogicMapper
                .selectList(new LambdaQueryWrapper<TbRackLogic>().ge(TbRackLogic::getUpdateDate, beginUpdateDate)
                        .le(TbRackLogic::getUpdateDate, endUpdateDate).eq(TbRackLogic::getNextGroupId, nextGroupId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicDto> selectByUpdateDateAndCurrentGroupId(Date beginUpdateDate, Date endUpdateDate,
                                                                  long currentGroupId) {
        return rackLogicMapper
                .selectList(new LambdaQueryWrapper<TbRackLogic>().ge(TbRackLogic::getUpdateDate, beginUpdateDate)
                        .le(TbRackLogic::getUpdateDate, endUpdateDate).eq(TbRackLogic::getCurrentGroupId, currentGroupId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicDto> selectByUpdateDateAndCurrentGroupIdAndPosition(Date beginUpdateDate, Date endUpdateDate,
                                                                             long currentGroupId, int position) {
        return rackLogicMapper.selectList(new LambdaQueryWrapper<TbRackLogic>()
                        .ge(TbRackLogic::getUpdateDate, beginUpdateDate).le(TbRackLogic::getUpdateDate, endUpdateDate)
                        .eq(TbRackLogic::getCurrentGroupId, currentGroupId).eq(TbRackLogic::getPosition, position)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<RackLogicDto> selectByApplySampleId(long applySampleId) {
        return rackLogicMapper.selectByApplySampleId(applySampleId);
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByRackLogicId(long rackLogicId) {

        rackLogicSpaceService.deleteByRackLogicSpaceId(rackLogicId);

        return rackLogicMapper.deleteById(rackLogicId) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @CacheEvict(allEntries = true)
    public void deleteByRackIds(Collection<Long> rackIds) {
        if (CollectionUtils.isEmpty(rackIds)) {
            return;
        }

        // 删除占用
        rackLogicSpaceService.deleteByRackIds(rackIds);

        final LambdaQueryWrapper<TbRackLogic> wrapper = Wrappers.lambdaQuery();
        wrapper.in(TbRackLogic::getRackId, rackIds);

        rackLogicMapper.delete(wrapper);
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByRackLogicIds(Collection<Long> rackLogicIds) {
        if (CollectionUtils.isEmpty(rackLogicIds)) {
            return;
        }

        rackLogicMapper.deleteBatchIds(rackLogicIds);
    }

    private RackLogicDto convert(TbRackLogic rackLogic) {
        if (Objects.isNull(rackLogic)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(rackLogic), RackLogicDto.class);
    }

    private List<RackLogicDto> convert(List<TbRackLogic> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }
}
