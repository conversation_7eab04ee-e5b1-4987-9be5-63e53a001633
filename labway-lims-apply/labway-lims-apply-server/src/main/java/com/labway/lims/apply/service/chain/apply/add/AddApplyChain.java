package com.labway.lims.apply.service.chain.apply.add;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

// 簽收添加申請單chain
@Component
public class AddApply<PERSON>hain extends ChainBase implements InitializingBean {
    @Resource
    private CheckParamCommand checkParamCommand;
    @Resource
    private CreateApplyCommand createApplyCommand;
    @Resource
    private GroupSampleCommand groupSampleCommand;
    @Resource
    private SampleFlowCommand sampleFlowCommand;
    @Resource
    private SaveInfoCommand saveInfoCommand;
    @Resource
    private PostCommand postCommand;
    @Resource
    private AddHspOrgDeptOrDoctorCommand addHspOrgDeptOrDoctorCommand;
    @Resource
    private SendMqCommand sendMqCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 检查参数
        addCommand(checkParamCommand);

        // 创建申请单
        addCommand(createApplyCommand);

        // 分条码
        addCommand(groupSampleCommand);

        // 保存信息
        addCommand(saveInfoCommand);

        // 生成流水
        addCommand(sampleFlowCommand);

        // 后置处理
        addCommand(postCommand);
        // 后置处理
        addCommand(sendMqCommand);

        //新增送检机构部门或医生基础数据
//        addCommand(addHspOrgDeptOrDoctorCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);

    }
}
