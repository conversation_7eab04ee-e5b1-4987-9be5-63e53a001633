package com.labway.lims.apply.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LabwayDateUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.outsourcing.OutsourcingOrgEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.OutsourcingApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.vo.OutsourcingBindDianBarcodeVo;
import com.labway.lims.apply.vo.OutsourcingDianSamplesRequestVo;
import com.labway.lims.apply.vo.OutsourcingDianSamplesVo;
import com.labway.lims.apply.vo.OutsourcingPickVo;
import com.labway.lims.apply.vo.OutsourcingPickedOrgSampleVo;
import com.labway.lims.apply.vo.OutsourcingPickedOrgVo;
import com.labway.lims.apply.vo.OutsourcingPickedSamplesRequestVo;
import com.labway.lims.apply.vo.OutsourcingSamplesRequestVo;
import com.labway.lims.apply.vo.OutsourcingSentSamplesVo;
import com.labway.lims.apply.vo.OutsourcingTwoPickSamplesRequestVo;
import com.labway.lims.apply.vo.OutsourcingTwoPickSamplesVo;
import com.labway.lims.apply.vo.OutsourcingUnPickSamplesRequestVo;
import com.labway.lims.apply.vo.OutsourcingUnPickSamplesVo;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 外送
 */
@Slf4j
@RestController
@RequestMapping("/outsourcing")
@RefreshScope
public class OutsourcingController extends BaseController {
    @Resource
    private ApplySampleService applySampleService;
    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private ApplyService applyService;
    @DubboReference
    private GroupService groupService;
    @Resource
    private RackLogicService rackLogicService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private ReportItemService reportItemService;
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Value("${showUnTwoPickSample:false}")
    private boolean showUnTwoPickSample;

    /**
     * 外送分拣
     */
    @PostMapping("/pick")
    public Object pick(@RequestParam String barcode) {
        if (StringUtils.isBlank(barcode)) {
            throw new IllegalArgumentException("参数错误");
        }

        // 不是委外组不返回列表
        if (!groupService.checkIsOutsourcingGroup(LoginUserHandler.get().getGroupId(), LoginUserHandler.get().getOrgId())) {
            throw new IllegalArgumentException("不是委外组不能外送分拣");
        }

        final List<ApplySampleDto> samples = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(samples)) {
            throw new IllegalArgumentException("条码号不存在");
        }

        final ApplySampleDto sample =
                samples.stream().filter(e -> Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId())).findFirst().orElse(null);
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("该条码不属于本专业组");
        }

        // 校验是否可用
        applySampleService.assertApplySampleUsability(sample.getApplySampleId());

        if (!Objects.equals(sample.getIsOutsourcing(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalArgumentException("当前样本不是外送，无法分拣");
        }

        if (Objects.equals(sample.getIsTwoPick(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalArgumentException("样本已经分拣");
        }

        final List<RackLogicDto> rackLogics = rackLogicService.selectByApplySampleId(sample.getApplySampleId());
        if (rackLogics.size() != 1) {
            throw new IllegalStateException("当前条码所在环节无法外送分拣");
        }

        if (!Objects.equals(rackLogics.iterator().next().getPosition(), RackLogicPositionEnum.TWO_PICKING.getCode())) {
            throw new IllegalStateException("当前条码所在环节无法外送分拣");
        }

        final List<ApplySampleItemDto> applySampleItems =
                applySampleItemService.selectByApplySampleId(sample.getApplySampleId());
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("申请单样本项目为空");
        }

        if (applySampleItems.stream().filter(e -> Objects.equals(e.getIsOutsourcing(), YesOrNoEnum.YES.getCode()))
                .map(ApplySampleItemDto::getExportOrgId).distinct().count() != 1) {
            throw new IllegalArgumentException("外送项目涉及到多家检验机构");
        }

        final ApplyDto apply = applyService.selectByApplyId(sample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalArgumentException("申请单不存在");
        }

        // 二次分拣
        TwoPickDto tp = new TwoPickDto();
        tp.setApplySampleId(sample.getApplySampleId());
        applySampleService.twoPick(tp);

        final OutsourcingPickVo v = new OutsourcingPickVo();
        BeanUtils.copyProperties(apply, v);
        BeanUtils.copyProperties(sample, v);
        v.setTestItemNames(
                applySampleItems.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
        v.setOutsourcingPickDate(new Date());
        v.setExportOrgName(applySampleItems.iterator().next().getExportOrgName());

        return v;
    }

    /**
     * 外送未分拣的样本
     */
    @PostMapping("/unpick-samples")
    public Object unpickSamples(@RequestBody OutsourcingUnPickSamplesRequestVo vo) {
        if (Objects.isNull(vo.getBeginReceiveDate()) || Objects.isNull(vo.getEndReceiveDate())) {
            throw new IllegalArgumentException("参数错误");
        }

        // 不是委外组不返回列表
        if (!groupService.checkIsOutsourcingGroup(LoginUserHandler.get().getGroupId(), LoginUserHandler.get().getOrgId())) {
            return Collections.emptyList();
        }

        final List<OutsourcingApplySampleDto> outsourcingApplySamples =
                applySampleService.selectOutsourcingUnPickApplySamples(
                        vo.getBeginReceiveDate(), vo.getEndReceiveDate(), LoginUserHandler.get().getGroupId());

        // 过滤掉已经禁用的
        outsourcingApplySamples.removeIf(e -> Objects.equals(YesOrNoEnum.YES.getCode(), e.getIsDisabled()));

        if (CollectionUtils.isEmpty(outsourcingApplySamples)) {
            return Collections.emptyList();
        }

        Set<Long> applyIds = outsourcingApplySamples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        final Map<Long, List<ApplySampleItemDto>> applySampleItems = applySampleItemService.selectByApplyIds(applyIds)
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        return outsourcingApplySamples.stream().map(e -> {
            final OutsourcingUnPickSamplesVo v = new OutsourcingUnPickSamplesVo();
            BeanUtils.copyProperties(e, v);
            v.setTestItemNames(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList()).stream()
                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            return v;
        });
    }

    /**
     * 外送已分拣的样本机构
     */
    @PostMapping("/picked-orgs")
    public Object pickedOrgs(@RequestBody OutsourcingPickedSamplesRequestVo vo) {
        if (Objects.isNull(vo.getBeginPickDate()) || Objects.isNull(vo.getEndPickDate())) {
            throw new IllegalArgumentException("参数错误");
        }

        // 不是委外组不返回列表
        if (!groupService.checkIsOutsourcingGroup(LoginUserHandler.get().getGroupId(), LoginUserHandler.get().getOrgId())) {
            return Collections.emptyList();
        }

        final var samples = outsourcingSampleService
                .selectByCreateDateAndGroup(vo.getBeginPickDate(), vo.getEndPickDate(), LoginUserHandler.get().getOrgId(), LoginUserHandler.get().getGroupId())
                .stream().collect(Collectors.groupingBy(OutsourcingSampleDto::getExportOrgId));
        if (MapUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        return samples.entrySet().stream().map(e -> {
            final OutsourcingPickedOrgVo v = new OutsourcingPickedOrgVo();
            v.setExportOrgId(e.getKey());
            v.setExportOrgName(e.getValue().stream().map(OutsourcingSampleDto::getExportOrgName)
                    .filter(StringUtils::isNotBlank).findFirst().orElse(StringUtils.EMPTY));
            v.setCount(e.getValue().size());
            return v;
        }).collect(Collectors.toList());
    }

    /**
     * 外送已分拣的样本
     */
    @PostMapping("/picked-orgs/samples")
    public List<OutsourcingPickedOrgSampleVo> pickedOrgsSamples(Long exportOrgId) {
        if (Objects.isNull(exportOrgId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<OutsourcingSampleDto> outsourcingSamples = outsourcingSampleService.selectByExportOrgId(exportOrgId);
        if (CollectionUtils.isEmpty(outsourcingSamples)) {
            return Collections.emptyList();
        }

        final Map<Long,
                ApplyDto> applies = applyService
                .selectByApplyIds(
                        outsourcingSamples.stream().map(OutsourcingSampleDto::getApplyId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));
        if (MapUtils.isEmpty(applies)) {
            return Collections.emptyList();
        }

        final Map<Long, List<ApplySampleItemDto>> applySampleItems =
                applySampleItemService.selectByApplyIds(applies.keySet()).stream()
                        .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        return outsourcingSamples.stream().map(e -> {
            final OutsourcingPickedOrgSampleVo v = new OutsourcingPickedOrgSampleVo();
            BeanUtils.copyProperties(e, v);
            final ApplyDto apply = applies.get(e.getApplyId());
            if (Objects.nonNull(apply)) {
                BeanUtils.copyProperties(apply, v);
                v.setSignDate(apply.getCreateDate());
            }
            v.setTestItemNames(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList()).stream()
                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            return v;
        }).collect(Collectors.toList());
    }

    /**
     * 已外送样本
     */
    @PostMapping("/sent-samples")
    public Object sendSamples(@RequestBody OutsourcingSamplesRequestVo vo) {
        final List<OutsourcingApplySampleDto> applySamples =
                applySampleService.selectOutsourcingListSamples(vo.getBeginPickDate(), vo.getEndPickDate(),
                                showUnTwoPickSample ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
                        // dev-1.1.2 过滤终止掉检验的样本
                        .stream().filter(e -> !Objects.equals(e.getStatus(), SampleStatusEnum.STOP_TEST.getCode()))
                        // 过滤掉 禁用的样本
                        .filter(e -> !Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()))
                        .filter(e -> showUnTwoPickSample || Objects.equals(e.getIsTwoPick(), YesOrNoEnum.YES.getCode())) // 是否只显示二次分拣的样本
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySamples)) {
            return Collections.emptyList();
        }

        final Map<Long,
                OutsourcingSampleDto> outsourcingSamples = outsourcingSampleService
                .selectByApplySampleIds(
                        applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(OutsourcingSampleDto::getApplySampleId, v -> v, (a, b) -> a));

        // 查询申请单
        final Map<Long,
                ApplyDto> applies = applyService
                .selectByApplyIds(
                        applySamples.stream().map(OutsourcingApplySampleDto::getApplyId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));

        // 查询检验项目
        final Map<Long,
                List<ApplySampleItemDto>> applySampleItems = applySampleItemService
                .selectByApplySampleIds(
                        applySamples.stream().map(OutsourcingApplySampleDto::getApplySampleId).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 根据外送分拣时间排序
        applySamples.sort(Comparator.comparing(ApplySampleDto::getTwoPickDate));

        return applySamples.stream().map(e -> {
                    final OutsourcingSentSamplesVo v = new OutsourcingSentSamplesVo();
                    BeanUtils.copyProperties(e, v);

                    final ApplyDto apply = applies.get(e.getApplyId());
                    final OutsourcingSampleDto outsourcingSample = outsourcingSamples.get(e.getApplySampleId());
                    final List<ApplySampleItemDto> items =
                            applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList());

                    if (Objects.nonNull(apply)) {
                        if (Objects.nonNull(vo.getHspOrgId()) && !Objects.equals(apply.getHspOrgId(), vo.getHspOrgId())) {
                            return null;
                        }
                        BeanUtils.copyProperties(apply, v);
                        v.setOutBarcode(e.getOutBarcode());
                    }

                    if (Objects.nonNull(outsourcingSample)) {
                        BeanUtils.copyProperties(outsourcingSample, v);
                        v.setOutsourcingPickDate(outsourcingSample.getCreateDate());
                        // 签收时间
                        v.setCreateDate(apply.getCreateDate());
                        // dev-1.1.3.9【nj-【外送清单打印】增加外送状态筛选条件】 是否二次分拣时间作为判断是否外送依据
                        // https://www.tapd.cn/59091617/prong/stories/view/1159091617001002107
                        v.setOutSourcing(Boolean.TRUE);
                        // 默认时间，手动置空
                        v.setCheckDate(LabwayDateUtil.isDefaultDbDate(v.getCheckDate()) ? null : v.getCheckDate());
                    } else if (CollectionUtils.isNotEmpty(items)) {
                        // 未分拣的情况，取根据检验项目取外送机构
                        v.setExportOrgId(items.get(0).getExportOrgId());
                        v.setExportOrgName(items.get(0).getExportOrgName());
                    }
                    // 过滤外送机构
                    if (Objects.nonNull(vo.getExportOrgId())
                            && !Objects.equals(v.getExportOrgId(), vo.getExportOrgId())) {
                        return null;
                    }

                    if (Objects.nonNull(vo.getTestItemId())
                            && items.stream().noneMatch(k -> Objects.equals(k.getTestItemId(), vo.getTestItemId()))) {
                        return null;
                    }

                    v.setIsPrintList(ObjectUtils.defaultIfNull(e.getIsPrintList(), YesOrNoEnum.NO.getCode()));
                    v.setTestItemNames(items.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
                    return v;
                })
                .filter(Objects::nonNull)
                .filter(e -> {
                    final Boolean isOutSourcing = vo.getOutSourcing();
                    // 没有传， 查全部
                    if (isOutSourcing == null) {
                        return true;
                    }
                    // 查询条件是已外送 && 数据为已外送
                    if (BooleanUtils.isTrue(isOutSourcing) && e.getOutSourcing()) {
                        return true;
                    }
                    // 查询条件是未外送 && 数据为未外送
                    return BooleanUtils.isFalse(isOutSourcing) && !e.getOutSourcing();
                })
                .collect(Collectors.toList());
    }

    /**
     * 预览外送清单
     */
    @PostMapping("/preview-sent-samples")
    public Object previewSendSamples(@RequestBody Set<Long> applySampleIds) throws Exception {
        final String pdfCode = "OUTSOURCING_LIST";
        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("参数错误");
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("条码信息不存在");
        }
        // 终止检验的样本 或者 禁用的样本
        final List<ApplySampleDto> stopSampleList = applySamples.stream()
                .filter(e -> Objects.equals(e.getStatus(), SampleStatusEnum.STOP_TEST.getCode()))
                .filter(e -> Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(stopSampleList)) {
            throw new IllegalStateException(String.format("条码号：【%s】已终止检验/禁用", CollUtil.join(stopSampleList.stream().map(ApplySampleDto::getBarcode).collect(Collectors.toSet()), ",")));
        }

        final Map<Long, ApplyDto> applies = applyService
                .selectByApplyIdsAsMap(applySamples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet()));

        final Map<Long, List<ApplySampleItemDto>> applySampleItems =
                applySampleItemService.selectByApplySampleIdsAsMap(applySampleIds);

        final LoginUserHandler.User user = LoginUserHandler.get();
        final Long orgId = user.getOrgId();
        // 查询所有检验项目
        final List<String> testItemCodes = applySampleItems.values().stream().flatMap(List::stream).map(ApplySampleItemDto::getTestItemCode).collect(Collectors.toList());
        final Map<String, TestItemDto> testItemMap = testItemService.selectByTestItemCodes(testItemCodes, orgId)
                .stream().collect(Collectors.toMap(TestItemDto::getTestItemCode, v -> v, (a, b) -> a));

        // 查询检验项目对应的报告项目
        final Map<String, List<ReportItemDto>> testItemCodeReportMap = reportItemService.selectByTestItemCodesAsMap(testItemCodes, orgId);

        // 根据外送分拣时间排序
        applySamples.sort(Comparator.comparing(ApplySampleDto::getTwoPickDate));

        final Map<String, List<Dict>> samplesByExportOrgNameMap = applySamples.stream().map(e -> {

            final Optional<ApplyDto> apply = Optional.ofNullable(applies.get(e.getApplyId()));

            final List<ApplySampleItemDto> applySampleItemDtoList = applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList());
            final ApplySampleItemDto applySampleItemDto = applySampleItemDtoList.get(0);

            // 当前样本检验项目 根据 testItemId排序
            final List<String> tmpTestItemCodes = applySampleItemDtoList.stream().map(ApplySampleItemDto::getTestItemCode).collect(Collectors.toList());
            final List<TestItemDto> tmpTestItemDtos = testItemMap.entrySet().stream()
                    .filter(testItem -> tmpTestItemCodes.contains(testItem.getKey())).map(Map.Entry::getValue)
                    .sorted(Comparator.comparing(TestItemDto::getTestItemCode))
                    .collect(Collectors.toList());

            // 检验项目名称
            final String testItemNames = tmpTestItemDtos.stream().map(TestItemDto::getTestItemName).collect(Collectors.joining(","));
            // 检验方法
            final String examMethodName = tmpTestItemDtos.stream().map(TestItemDto::getExamMethodName).collect(Collectors.joining(","));

            // 当前样本报告项目
            // 报告项目名称
            final String reportItemNames = testItemCodeReportMap.entrySet().stream()
                    .filter(reportItem -> tmpTestItemCodes.contains(reportItem.getKey()))
                    .map(a -> Map.entry(a.getKey(), a.getValue()))
                    .sorted(Map.Entry.comparingByKey())
                    .map(Map.Entry::getValue)
                    .map(reportList -> reportList.stream().map(ReportItemDto::getReportItemName).distinct().collect(Collectors.joining(",")))
                    .collect(Collectors.joining(";"));

            return Dict.of("barcode", e.getBarcode(), "sampleType", e.getSampleTypeName(),
                    "testItemNames", testItemNames, "reportItemNames", reportItemNames,
                    "examMethodName", examMethodName, "sampleTypeName", e.getSampleTypeName(),
                    "_sample", Dict.parse(e), "_apply", Dict.parse(ObjectUtils.defaultIfNull(apply, Collections.emptyMap())),
                    "patientName", apply.map(ApplyDto::getPatientName).orElse(StringUtils.EMPTY),
                    "patientSex", apply.map(ApplyDto::getPatientSex).orElse(SexEnum.DEFAULT.getCode()),
                    "patientAge", apply.map(ApplyDto::getPatientAge).orElse(0),
                    "patientSubage", apply.map(ApplyDto::getPatientSubage).orElse(0),
                    "patientSubageUnit", apply.map(ApplyDto::getPatientSubageUnit).orElse(StringUtils.EMPTY),
                    "patientVisitCard", apply.map(ApplyDto::getPatientVisitCard).orElse(StringUtils.EMPTY),
                    "nickName", user.getNickname(),
                    "exportOrgName", applySampleItemDto.getExportOrgName())
                    ;
            // 根据外送单位分组
        }).collect(Collectors.groupingBy(e -> String.valueOf(e.get("exportOrgName"))));

        // 获取当前日期时间并格式化
        final String date = LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        List<File> fileList = new ArrayList<>();
        for (Map.Entry<String, List<Dict>> e : samplesByExportOrgNameMap.entrySet()) {
            final PdfReportParamDto param = new PdfReportParamDto();
            param.put("orgName", e.getKey());
            param.put("samples", e.getValue());
            param.put("date", date);

            final File tempFile = FileUtil.createTempFile();
            final byte[] build = pdfReportService.build(pdfCode, param);
            try (final FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(build);
            }
            fileList.add(tempFile);
        }

        return Map.of("url", this.mergeFiles(fileList, pdfCode));
    }

    private String mergeFiles(List<File> fileList, final String pdfCode) throws Exception {
        final File pdf = fileList.size() == NumberUtils.INTEGER_ONE ? fileList.iterator().next() : FileUtil.createTempFile();
        //  如果数量不是1，那么需要合并
        if (fileList.size() != NumberUtils.INTEGER_ONE) {
            final PDFMergerUtility merger = new PDFMergerUtility();
            for (File file : fileList) {
                merger.addSource(file);
            }

            final PDDocumentInformation information = new PDDocumentInformation();
            information.setKeywords(pdfCode);
            merger.setDestinationFileName(pdf.getAbsolutePath());
            merger.setDestinationDocumentInformation(information);

            try {
                merger.mergeDocuments(MemoryUsageSetting.setupTempFileOnly());
            } catch (Exception e) {
                throw new IllegalStateException("外送清单合并PDF失败", e);
            } finally {
                fileList.forEach(FileUtils::deleteQuietly);
            }
        }

        try (final FileInputStream fis = new FileInputStream(pdf)) {
            return huaweiObsUtils.upload(fis, MediaType.APPLICATION_PDF_VALUE);
        }
    }

    /**
     * 打印外送清单
     */
    @PostMapping("/print-sent-samples")
    public Object printSendSamples(@RequestBody Set<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("参数错误");
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        Date currentDate = new Date();

        final ApplySampleDto applySample = new ApplySampleDto();
        applySample.setIsPrintList(YesOrNoEnum.YES.getCode());
        applySample.setPrintListDate(currentDate);
        applySample.setUpdaterName(user.getNickname());
        applySample.setUpdaterId(user.getUserId());
        applySample.setUpdateDate(applySample.getPrintListDate());
        applySampleService.updateByApplySampleIds(applySample, applySampleIds);

        final Map<Long, List<ApplySampleItemDto>> applySampleIdsAsMap = applySampleItemService.selectByApplySampleIdsAsMap(applySampleIds);

        // 打印外送清单，记录条码环节
        LinkedList<Long> genIds = snowflakeService.genIds(applySampleIds.size());
        sampleFlowService.addSampleFlows(
                applySampleService.selectByApplySampleIds(applySampleIds)
                        .stream()
                        .map(e -> {
                            final List<ApplySampleItemDto> applySampleItemDtos =
                                    applySampleIdsAsMap.getOrDefault(e.getApplySampleId(), Collections.singletonList(new ApplySampleItemDto()));
                            final String exportOrgName = applySampleItemDtos.stream().iterator().next().getExportOrgName();
                            return SampleFlowDto.builder()
                                    .applyId(e.getApplyId())
                                    .sampleFlowId(genIds.pop())
                                    .applySampleId(e.getApplySampleId())
                                    .operateCode(BarcodeFlowEnum.OUTSOURCING_LIST_PRINT.name())
                                    .operateName(BarcodeFlowEnum.OUTSOURCING_LIST_PRINT.getDesc())
                                    .operatorId(user.getUserId())
                                    .operator(user.getNickname())
                                    .barcode(e.getBarcode())
                                    .content(String.format("%s 外送机构 [%s]", BarcodeFlowEnum.OUTSOURCING_LIST_PRINT.getDesc(), exportOrgName))
                                    .build();
                        }).collect(Collectors.toList())
        );

        return Map.of();

    }

    /**
     * 外送迪安样本列表
     */
    @PostMapping("/dian-samples")
    public Object dianSamples(@RequestBody OutsourcingDianSamplesRequestVo vo) {
        final HspOrganizationDto organization = hspOrganizationService.selectByHspOrgCode(OutsourcingOrgEnum.DIAN.getDesc());
        if (Objects.isNull(organization)) {
            throw new IllegalStateException("未找到迪安外送机构");
        }

        final List<OutsourcingApplySampleDto> samples =
                applySampleService.selectOutsourcingPickedApplySamples(vo.getBeginPickDate(), vo.getEndPickDate());
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        // 查询外送的
        final List<OutsourcingSampleDto> outsourcingSamples =
                new ArrayList<>(outsourcingSampleService.selectByApplySampleIds(
                        samples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet())));
        if (CollectionUtils.isEmpty(outsourcingSamples)) {
            return Collections.emptyList();
        }

        // 删除不是迪安的
        outsourcingSamples.removeIf(e -> !Objects.equals(e.getExportOrgId(), organization.getOrgId()));

        // 查询申请单
        final Map<Long,
                ApplyDto> applies = applyService
                .selectByApplyIds(
                        outsourcingSamples.stream().map(OutsourcingSampleDto::getApplyId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));

        // 查询检验项目
        final Map<Long,
                List<ApplySampleItemDto>> applySampleItems = applySampleItemService
                .selectByApplySampleIds(
                        outsourcingSamples.stream().map(OutsourcingSampleDto::getApplySampleId).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        return outsourcingSamples.stream().map(e -> {
            final OutsourcingDianSamplesVo v = new OutsourcingDianSamplesVo();
            BeanUtils.copyProperties(e, v);

            if (applies.containsKey(e.getApplyId())) {
                BeanUtils.copyProperties(applies.get(e.getApplyId()), v);
            }

            v.setTestItemNames(applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList()).stream()
                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
            return v;
        }).collect(Collectors.toList());
    }

    /**
     * 外送迪安样本列表
     */
    @PostMapping("/bind-dian-barcode")
    public Object bindDianBarcode(@RequestBody OutsourcingBindDianBarcodeVo vo) {
        if (Objects.isNull(vo.getApplySampleId()) || StringUtils.isBlank(vo.getBarcode())) {
            throw new IllegalArgumentException("参数错误");
        }

        final OutsourcingSampleDto outsourcingSample =
                outsourcingSampleService.selectByOutsourcingSampleId(vo.getApplySampleId());
        if (Objects.isNull(outsourcingSample)) {
            throw new IllegalStateException("外送样本不存在");
        }

        final OutsourcingSampleDto os = new OutsourcingSampleDto();
        os.setOutsourcingSampleId(outsourcingSample.getOutsourcingSampleId());
        os.setExportBarcode(vo.getBarcode());
        os.setUpdateDate(new Date());
        os.setUpdaterId(LoginUserHandler.get().getUserId());
        os.setUpdaterName(LoginUserHandler.get().getNickname());

        if (!outsourcingSampleService.updateByOutsourcingSampleId(os)) {
            throw new IllegalStateException("绑定迪安条码失败");
        }

        return Collections.emptyMap();
    }

    /**
     * 已外送分拣样本
     */
    @PostMapping("/outsourcing-two-pick-samples")
    public Object outsourcingTwoPickSamples(@RequestBody OutsourcingTwoPickSamplesRequestVo vo) {
        final List<OutsourcingApplySampleDto> applySamples =
                applySampleService.selectOutsourcingListSamples(vo.getBeginPickDate(), vo.getEndPickDate(), YesOrNoEnum.NO.getCode())
                        // dev-1.1.2 过滤终止掉检验的样本
                        .stream().filter(e -> !Objects.equals(e.getStatus(), SampleStatusEnum.STOP_TEST.getCode()))
                        // 过滤掉 禁用的样本
                        .filter(e -> !Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()))
                        // 过滤没有外送清单打印的样本
                        .filter(e -> !Objects.equals(e.getIsPrintList(), YesOrNoEnum.YES.getCode()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySamples)) {
            return Collections.emptyList();
        }

        // 查询样本
        CompletableFuture<Map<Long, OutsourcingSampleDto>> outsourcingSamplesFuture = CompletableFuture.supplyAsync(() -> outsourcingSampleService
                .selectByApplySampleIds(applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(OutsourcingSampleDto::getApplySampleId, v -> v, (a, b) -> a)), threadPoolConfig.getPool());

        // 查询申请单
        CompletableFuture<Map<Long, ApplyDto>> appliesFuture = CompletableFuture.supplyAsync(() -> applyService
                .selectByApplyIds(applySamples.stream().map(OutsourcingApplySampleDto::getApplyId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a)), threadPoolConfig.getPool());

        // 查询检验项目
        CompletableFuture<Map<Long, List<ApplySampleItemDto>>> applySampleItemsFuture = CompletableFuture.supplyAsync(() -> applySampleItemService
                .selectByApplySampleIds(applySamples.stream().map(OutsourcingApplySampleDto::getApplySampleId).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId)), threadPoolConfig.getPool());

        final Map<Long, OutsourcingSampleDto> outsourcingSamples;
        final Map<Long, ApplyDto> applies;
        final Map<Long, List<ApplySampleItemDto>> applySampleItems;
        try {
            CompletableFuture.allOf(outsourcingSamplesFuture, appliesFuture, applySampleItemsFuture).get();

            outsourcingSamples = outsourcingSamplesFuture.get();
            applies = appliesFuture.get();
            applySampleItems = applySampleItemsFuture.get();
        } catch (Exception e) {
            log.error("查询 样本/申请单/检验项目 异常 ", e);
            throw new RuntimeException(e);
        }

        // 根据外送分拣时间排序
        applySamples.sort(Comparator.comparing(ApplySampleDto::getTwoPickDate));

        final Map<Long, OutsourcingTwoPickSamplesVo> samplesVoMap = new HashMap<>();
        return applySamples.stream().map(e -> {
                    final OutsourcingSentSamplesVo v = new OutsourcingSentSamplesVo();
                    BeanUtils.copyProperties(e, v);

                    final ApplyDto apply = applies.get(e.getApplyId());
                    final OutsourcingSampleDto outsourcingSample = outsourcingSamples.get(e.getApplySampleId());
                    final List<ApplySampleItemDto> items =
                            applySampleItems.getOrDefault(e.getApplySampleId(), Collections.emptyList());

                    if (Objects.nonNull(apply)) {
                        BeanUtils.copyProperties(apply, v);
                        v.setOutBarcode(e.getOutBarcode());
                    }

                    if (Objects.nonNull(outsourcingSample)) {
                        BeanUtils.copyProperties(outsourcingSample, v);
                        v.setOutsourcingPickDate(outsourcingSample.getCreateDate());
                        // 签收时间
                        v.setCreateDate(apply.getCreateDate());
                    }

                    v.setIsPrintList(ObjectUtils.defaultIfNull(e.getIsPrintList(), YesOrNoEnum.NO.getCode()));
                    v.setTestItemNames(items.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
                    return v;
                })
                /*.collect(Collectors.toMap(
                        OutsourcingSentSamplesVo::getExportOrgId,
                        v -> new OutsourcingTwoPickSamplesVo(v.getExportOrgName(), v.getExportOrgId(), v),
                        (a, b) -> {

                            a.setCount(a.getCount() + 1);
                            a.getList().add(b.getTmp());

                            return a;
                        })).values().stream()*/
                .map(e -> {
                    final Long exportOrgId = e.getExportOrgId();
                    final String exportOrgName = e.getExportOrgName();

                    OutsourcingTwoPickSamplesVo samplesVo = samplesVoMap.get(exportOrgId);
                    final boolean isNew = Objects.isNull(samplesVo);
                    if (isNew) {
                        samplesVoMap.put(exportOrgId, samplesVo = new OutsourcingTwoPickSamplesVo(exportOrgName, exportOrgId));
                    }

                    samplesVo.setCount(samplesVo.getCount() + 1);
                    samplesVo.getList().add(e);

                    return isNew ? samplesVo : null;
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(OutsourcingTwoPickSamplesVo::getExportOrgName, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
    }

}
