package com.labway.lims.apply.service.chain.material.delivery.income;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryIncomeItemDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryRecordDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.List;

/**
 * 待入库 物料信息上下文
 *
 * <AUTHOR>
 * @since 2023/5/9 10:00
 */
@Getter
@Setter
public class MaterialIncomeContext extends StopWatchContext {
    /**
     * 出库单号
     */
    private String deliveryNo;
    /**
     * 入库信息
     */
    private List<MaterialDeliveryIncomeItemDto> incomeItemList;
    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    /**
     * 获取 物料入库 信息 从上下文中
     */
    public static MaterialIncomeContext from(Context context) {
        return (MaterialIncomeContext)context;
    }

    // 对应物料待入库记录
    public static final String MATERIAL_DELIVERY_RECORD = "MATERIAL_DELIVERY_RECORD_" + IdUtil.objectId();

    // 对应待入库 物料详细信息
    public static final String MATERIAL_DELIVERY_DETAIL = "MATERIAL_DELIVERY_DETAIL_" + IdUtil.objectId();

    public MaterialDeliveryRecordDto getMaterialDeliveryRecord() {
        return (MaterialDeliveryRecordDto)get(MATERIAL_DELIVERY_RECORD);
    }

    public List<MaterialDeliveryDetailDto> getMaterialDeliveryDetails() {
        return (List<MaterialDeliveryDetailDto>)get(MATERIAL_DELIVERY_DETAIL);
    }

    // 对应 专业组 物料信息
    public static final String GROUP_MATERIAL = "GROUP_MATERIAL_" + IdUtil.objectId();

    public List<GroupMaterialDto> getGroupMaterialDtos() {
        return (List<GroupMaterialDto>)get(GROUP_MATERIAL);
    }

    @Override
    protected String getWatcherName() {
        return "物料入库";
    }
}
