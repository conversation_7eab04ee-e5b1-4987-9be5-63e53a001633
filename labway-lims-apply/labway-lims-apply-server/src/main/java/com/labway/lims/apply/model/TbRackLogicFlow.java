package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 逻辑试管架流水
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_rack_logic_flow")
public class TbRackLogicFlow implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long rackLogicFlowId;

    private Long rackLogicId;

    /**
     * 上一个交接人
     */
    private String prevHandover;

    /**
     * 当前交接人
     */
    private String currenHandover;

    private Long updaterId;

    private String updaterName;

    private Long creatorId;

    private String creatorName;

    private Date updateDate;

    private Date createDate;

    private Long currentDepartmentId;

    private String currentDepartmentCode;

    private String currentDepartmentName;

    private Long nextDepartmentId;

    private String nextDepartmentCode;

    private String nextDepartmentName;


}
