package com.labway.lims.apply.service.chain.pda.apply.add;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.PdaApplyAndItemDto;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.PdaApplySampleItemDto;
import com.labway.lims.apply.api.service.pda.PdaApplySampleItemService;
import com.labway.lims.apply.service.chain.apply.add.AddApplyContext;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.SaveHspOrganizationFiledDto;
import com.labway.lims.base.api.service.SupplementalRecordFieldSettingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CheckSupplementalRecordFieldCommand implements Command {

    @DubboReference
    private SupplementalRecordFieldSettingService supplementalRecordFieldSettingService;
    @Resource
    private PdaApplySampleItemService pdaApplySampleItemService;


    @Override
    public boolean execute(Context c) throws Exception {
        final AddApplyContext from = AddApplyContext.from(c);

        final List<PdaApplyDto> pdaApplys = from.getPdaApply();
        if (CollectionUtils.isEmpty(pdaApplys)) {
            return CONTINUE_PROCESSING;
        }

        final HspOrganizationDto hspOrganization = from.getHspOrganization();
        // 规则设置
        SaveHspOrganizationFiledDto hspOrganizationfieldDto =
                supplementalRecordFieldSettingService.selectByHspOrgIdOrDefaultOrg(hspOrganization.getHspOrgId());
        // 没有规则则对比全量字段
        if (Objects.isNull(hspOrganizationfieldDto)) {
            hspOrganizationfieldDto = SaveHspOrganizationFiledDto.defaultFieldSetting();
        }

        // 是否相同
        final boolean isTheSame = this.comparefield(pdaApplys, from, hspOrganizationfieldDto);
        // 增加标记
        from.put(AddApplyContext.PDA_DOUBLE_FLAG, isTheSame);

        return CONTINUE_PROCESSING;
    }


    /**
     * 1. 判断项目数量
     * 2. 判断项目 code, 样本类型， 管型， 收费数量， 急诊类型  是否完全一样
     * 3. 判断血培养瓶数（如果存在）
     * 4. 判断值
     * @return 通过 true    不通过 false
     */
    private boolean comparefield(List<PdaApplyDto> pdaApplys, AddApplyContext from, SaveHspOrganizationFiledDto hspOrganizationfieldDto) {

        // 第一次录入的信息
        final PdaApplyAndItemDto pdaApplyAndItem = this.getPdaApplyAndItem(pdaApplys);
        final List<PdaApplySampleItemDto> onePdaApplySampleItemDtoList = pdaApplyAndItem.getPdaApplySampleItemDtoList();

        // 第二次录入的信息
        final ApplyDto apply = from.getApply();

        final List<PdaApplySampleItemDto> twoPdaApplySampleItemDtoList = JSON.parseArray(JSON.toJSONString(from.getApplySampleItems()), PdaApplySampleItemDto.class);


        // 项目数量不同直接不通过
        if (onePdaApplySampleItemDtoList.size() != twoPdaApplySampleItemDtoList.size()) {
            return false;
        }

        // 项目对比字段： code, 样本类型， 管型， 收费数量， 急诊类型
        final Map<String, PdaApplySampleItemDto> oneSampleItemMap = getSampleItemStr(onePdaApplySampleItemDtoList);
        final Map<String, PdaApplySampleItemDto> twoSampleItemMap = getSampleItemStr(twoPdaApplySampleItemDtoList);
        // 判断key是否都相同， 不包含直接不通过
        if (!oneSampleItemMap.keySet().containsAll(twoSampleItemMap.keySet())) {
            return false;
        }

        //        // 判断血培养
        //        final long count = oneSampleItemMap.entrySet().stream().filter(e -> {
        //            final PdaApplySampleItemDto pdaApplySampleItemDto = twoSampleItemMap.get(e.getKey());
        //            ApplySampleItemBloodCultureDto oneBloodcultureItem = JSON.parseObject(e.getValue().getBloodcultureItem(), ApplySampleItemBloodCultureDto.class);
        //            ApplySampleItemBloodCultureDto twoBloodcultureItem = JSON.parseObject(pdaApplySampleItemDto.getBloodcultureItem(), ApplySampleItemBloodCultureDto.class);
        //
        //            // 都是空 不判断血培养
        //            if (Objects.isNull(oneBloodcultureItem) && Objects.isNull(twoBloodcultureItem)) {
        //                return false;
        //            }
        //
        //            // 其中一个是空
        //            if (Objects.isNull(oneBloodcultureItem)) {
        //                return true;
        //            }
        //
        //            if (Objects.isNull(twoBloodcultureItem)) {
        //                return true;
        //            }
        //
        //            // 如果其中一个为空或者数量不同， 则返回true
        //            return !Objects.equals(oneBloodcultureItem.getLulAnaerobic(), twoBloodcultureItem.getLulAnaerobic())
        //                    || !Objects.equals(oneBloodcultureItem.getLulAerobic(), twoBloodcultureItem.getLulAerobic())
        //                    || !Objects.equals(oneBloodcultureItem.getLulPediatricBottle(), twoBloodcultureItem.getLulPediatricBottle())
        //                    || !Objects.equals(oneBloodcultureItem.getLllAnaerobic(), twoBloodcultureItem.getLllAnaerobic())
        //                    || !Objects.equals(oneBloodcultureItem.getLllAerobic(), twoBloodcultureItem.getLllAerobic())
        //                    || !Objects.equals(oneBloodcultureItem.getLllPediatricBottle(), twoBloodcultureItem.getLllPediatricBottle())
        //                    || !Objects.equals(oneBloodcultureItem.getRulAnaerobic(), twoBloodcultureItem.getRulAnaerobic())
        //                    || !Objects.equals(oneBloodcultureItem.getRulAerobic(), twoBloodcultureItem.getRulAerobic())
        //                    || !Objects.equals(oneBloodcultureItem.getRulPediatricBottle(), twoBloodcultureItem.getRulPediatricBottle())
        //                    || !Objects.equals(oneBloodcultureItem.getRllAnaerobic(), twoBloodcultureItem.getRllAnaerobic())
        //                    || !Objects.equals(oneBloodcultureItem.getRllAerobic(), twoBloodcultureItem.getRllAerobic())
        //                    || !Objects.equals(oneBloodcultureItem.getRllPediatricBottle(), twoBloodcultureItem.getRllPediatricBottle())
        //                    || !Objects.equals(oneBloodcultureItem.getAnaerobic(), twoBloodcultureItem.getAnaerobic())
        //                    || !Objects.equals(oneBloodcultureItem.getAerobic(), twoBloodcultureItem.getAerobic())
        //                    || !Objects.equals(oneBloodcultureItem.getPediatricBottle(), twoBloodcultureItem.getPediatricBottle())
        //                    || !Objects.equals(oneBloodcultureItem.getCount(), twoBloodcultureItem.getCount());
        //        }).count();
        //
        //        if (count > NumberUtils.LONG_ZERO) {
        //            return false;
        //        }


        // 字段对比设置
        final List<SaveHspOrganizationFiledDto.Field> fields = hspOrganizationfieldDto.getFileds();

        for (SaveHspOrganizationFiledDto.Field field : fields) {
            final Object oneValue = getfieldValue(pdaApplyAndItem, field.getCode());
            final Object twoValue = getfieldValue(apply, field.getCode());
            if (oneValue instanceof Date && twoValue instanceof Date) {
                LocalDateTime oneValueDate = this.toLocalDateTime(((Date) oneValue));
                LocalDateTime twoValueDate = this.toLocalDateTime(((Date) twoValue));
                // 比较  年月日时分
                if (!Objects.equals(oneValueDate.getYear(), twoValueDate.getYear())
                        || !Objects.equals(oneValueDate.getMonthValue(), twoValueDate.getMonthValue())
                        || !Objects.equals(oneValueDate.getDayOfMonth(), twoValueDate.getDayOfMonth())
                        || !Objects.equals(oneValueDate.getHour(), twoValueDate.getHour())
                        || !Objects.equals(oneValueDate.getMinute(), twoValueDate.getMinute())
                ) {
                    return false;
                }
            } else if (!Objects.equals(oneValue, twoValue)) {
                // 比较字段的值，不相同则不通过
                return false;
            }
        }

        return true;
    }

    private Map<String, PdaApplySampleItemDto> getSampleItemStr(List<PdaApplySampleItemDto> pdaApplySampleItemDtoList) {
        return pdaApplySampleItemDtoList.stream().collect(Collectors.toMap(e -> {
            StringJoiner sj = new StringJoiner("-");
            sj.add(e.getTestItemCode());
            sj.add(e.getSampleTypeCode());
            sj.add(e.getTubeCode());
            sj.add(String.valueOf(e.getCount()));
            sj.add(String.valueOf(e.getUrgent()));
            return sj.toString();
        }, Function.identity(), (a, b) -> b));
    }

    private Object getfieldValue(Object object, String fieldName) {
        Class<?> clazz = object.getClass();

        while (clazz != null) {
            final Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                try {
                    if (Objects.equals(field.getName(), fieldName)) {
                        field.setAccessible(true); // 确保字段是可访问的
                        return field.get(object);
                    }
                } catch (IllegalAccessException e) {
                    log.error(e.getMessage());
                }
            }
            // 获取父类
            clazz = clazz.getSuperclass();
        }
        log.debug("{} 类未找到属性字段 {}", object.getClass().getName(), fieldName);
        return "";
    }

    /**
     * 获取第一次录入的信息
     */
    private PdaApplyAndItemDto getPdaApplyAndItem(List<PdaApplyDto> pdaApplys) {
        // 这里应该就剩一个了
        final PdaApplyDto pdaApplyDto = pdaApplys.stream().findFirst().get();

        // 查询申请单下的项目
        final List<PdaApplySampleItemDto> pdaApplySampleItemDtos = pdaApplySampleItemService.selectByPdaApplyId(pdaApplyDto.getPdaApplyId());

        //
        final PdaApplyAndItemDto pdaApplyAndItemDto = BeanUtil.toBean(pdaApplyDto, PdaApplyAndItemDto.class);
        pdaApplyAndItemDto.setPdaApplySampleItemDtoList(pdaApplySampleItemDtos);
        return pdaApplyAndItemDto;
    }

    public LocalDateTime toLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

}
