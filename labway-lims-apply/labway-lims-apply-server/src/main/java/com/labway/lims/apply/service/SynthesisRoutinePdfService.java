package com.labway.lims.apply.service;

import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleReportDto;

import javax.annotation.Nonnull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

public interface SynthesisRoutinePdfService {


    // 生产常规样本pdf
    List<SampleReportDto> synthesisPdf(List<Long> unAuditApplySampleIds);

    // 生产常规样本pdf的报告单数据
    List<HashMap<String, Serializable>> sampleInfo(@Nonnull ApplySampleDto applySampleDto);
}
