package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.model.TbApplySampleItemBloodCulture;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 血培养
 */
@Mapper
public interface TbApplySampleItemBloodCultureMapper extends BaseMapper<TbApplySampleItemBloodCulture> {

    int insertBatch(@Param("applySampleItemBloodCultures") List<ApplySampleItemBloodCultureDto> applySampleItemBloodCultures);
}
