package com.labway.lims.apply.vo;

import com.labway.lims.apply.api.dto.SplitBloodApplySampleDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 已分血列表
 */
@Getter
@Setter
public class SplitBloodApplySampleVo extends SplitBloodApplySampleDto {
    /**
     * 基本量
     */
    private BigDecimal basicQuantity;


    /**
     * 禁止分血的样本会有多个专业组
     */
    private List<String> groupNames;

    /**
     * 检验项目名称
     */
    private List<String> testItemNames;
}
