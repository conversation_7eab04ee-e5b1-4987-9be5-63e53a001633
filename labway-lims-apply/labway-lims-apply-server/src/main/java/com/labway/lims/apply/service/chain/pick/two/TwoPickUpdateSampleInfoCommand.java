
package com.labway.lims.apply.service.chain.pick.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.InfectionTwoPickDto;
import com.labway.lims.apply.api.dto.MicrobiologyTwoPickDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.service.chain.pick.two.immunity.ImmunityTwoPickContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * 修改申请单样本信息
 */
@Slf4j
@Component
public class TwoPickUpdateSampleInfoCommand implements Command {

    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final TwoPickContext context = TwoPickContext.from(c);
        Date twoPickDate = null;

        Map<Long, Date> immunityTwoPickDateMap = Map.of();
        boolean isImmunityTwoPick = false;
        if (context.getTwoPick() instanceof MicrobiologyTwoPickDto) {
            // 微生物二次分拣
            twoPickDate = ((MicrobiologyTwoPickDto) context.getTwoPick()).getTwoPickDate();
        } else if (context.getTwoPick() instanceof InfectionTwoPickDto) {
            // 院感二次分拣
            twoPickDate = ((InfectionTwoPickDto) context.getTwoPick()).getTwoPickDate();
        } else if (context instanceof ImmunityTwoPickContext) {
            // 免疫二次分拣 时间
            immunityTwoPickDateMap = ((ImmunityTwoPickContext) context).getImmunityTwoPickDate();
            isImmunityTwoPick = ((ImmunityTwoPickContext) context).isImmunityTwoPick();
        }

        final Date now = new Date(System.currentTimeMillis());

        for (int i = 0; i < context.getApplySampleTwoPicks().size(); i++) {
            final ApplySampleTwoPickDto e = context.getApplySampleTwoPicks().get(i);
            final ApplySampleDto as = new ApplySampleDto();
            as.setApplySampleId(e.getApplySampleId());
            as.setIsTwoPick(YesOrNoEnum.YES.getCode());
            // 保证分拣时间
            as.setTwoPickDate(ObjectUtils.defaultIfNull(twoPickDate, DateUtils.addMilliseconds(now, i)));

            // 免疫二次分拣 指定的分拣时间
            if (immunityTwoPickDateMap.containsKey(e.getApplySampleId())) {
                as.setTwoPickDate(immunityTwoPickDateMap.get(e.getApplySampleId()));
            }
            if (isImmunityTwoPick) {
                // 标记免疫二次分拣
                as.setIsImmunityTwoPick(YesOrNoEnum.YES.getCode());
            }

            as.setTwoPickerId(LoginUserHandler.get().getUserId());
            as.setTwoPickerName(LoginUserHandler.get().getNickname());
            as.setStatus(SampleStatusEnum.NOT_AUDIT.getCode());
            as.setTesterName(LoginUserHandler.get().getNickname());
            as.setTesterId(LoginUserHandler.get().getUserId());
            as.setItemType(e.getItemType());

            applySampleService.updateByApplySampleId(as);
        }

        return CONTINUE_PROCESSING;
    }

}
