package com.labway.lims.apply.controller.it8000;

import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.ArrayUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.Orgs;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.vo.IT8000HandleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * it 8000
 */
@Slf4j
@RequestMapping("/it-8000")
@RestController
public class IT8000HandlerController extends BaseController implements InitializingBean {

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private HttpServletRequest request;

    private Map<IT8000HandleVo.Action, ActionStrategy> strategies;

    @PostMapping("/handle")
    public Object handle(@RequestBody IT8000HandleVo vo) throws Exception {

        processLoginUser();

        final IT8000HandleVo.Action action = EnumUtils.getEnum(IT8000HandleVo.Action.class, vo.getAction());
        if (Objects.isNull(action)) {
            throw new IllegalArgumentException(String.format("无法识别 Action [%s]", vo.getAction()));
        }

        final ActionStrategy strategy = strategies.get(action);
        if (Objects.isNull(strategy)) {
            throw new IllegalArgumentException(String.format("无法处理 Action [%s]", vo.getAction()));
        }

        return strategy.action(vo);
    }

    @Trace
    private void processLoginUser() {
        final String ua = request.getHeader(HttpHeaders.USER_AGENT);
        if (StringUtils.isBlank(ua)) {
            throw new IllegalStateException("无权限访问");
        }

        final String orgId = (String) Optional.ofNullable(ArrayUtil.get(ua.split(","), 3))
                .map(e -> (String) e).map(e -> ArrayUtil.get(e.split("/"), 0)).orElse(null);
        if (StringUtils.isBlank(orgId)) {
            throw new IllegalStateException("无权限访问");
        }

        final String requester = request.getHeader("Requester");
        if (StringUtils.isNotBlank(requester)) {
            log.info("收到请求者请求 Url [{}] TraceId [{}] Requester [{}]", request.getRequestURL()
                    , TraceContext.traceId()
                    , URLDecoder.decode(requester, StandardCharsets.UTF_8));
        }

        final Orgs org = Orgs.getOrgByOrgCode(NumberUtils.toInt(orgId));


        final LoginUserHandler.User user = new LoginUserHandler.User();
        user.setOrgId(org.getOrgCode());
        user.setOrgCode(String.valueOf(org.getOrgCode()));
        user.setOrgName(org.getOrgName());
        user.setUserId(NumberUtils.LONG_ZERO);
        user.setUsername("machine");
        user.setNickname("仪器");

        LoginUserHandler.set(user);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        final Map<IT8000HandleVo.Action, ActionStrategy> strategies = new EnumMap<>(IT8000HandleVo.Action.class);
        final Collection<ActionStrategy> values = applicationContext.getBeansOfType(ActionStrategy.class).values();
        for (ActionStrategy e : values) {
            strategies.put(e.action(), e);
        }

        this.strategies = Collections.unmodifiableMap(strategies);

        for (var e : strategies.entrySet()) {
            log.info("IT8000 Action [{}] Handler [{}]", e.getKey(), e.getValue());
        }

    }
}
