package com.labway.lims.apply.vo.utils;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.apply.MaterialNoType;
import com.labway.lims.api.exception.LimsException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.LinkedList;
import java.util.Objects;

/**
 * 物料 申领单号-入库单号-盘点单号 生成工具类
 * 
 * <AUTHOR>
 * @since 2023/5/9 11:20
 */
@Slf4j
@Component
public class MaterialNoUtils {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisPrefix redisPrefix;

    private static final String REDIS_PREFIX = "MaterialNo:";

    private static final String DATE_FORMAT = "yyMMdd";

    /**
     * 生成一个 申领单号
     * 
     * @param orgId 机构id
     * @return 申领单号
     */
    public String genApplyNo(long orgId) {
        return genMaterialNo(orgId, MaterialNoType.APPLY_NO, 1).pop();
    }

    /**
     * 生成一个 入库单号
     * 
     * @param orgId 机构id
     * @return 入库单号
     */
    public String genIncomeNo(long orgId) {
        return genMaterialNo(orgId, MaterialNoType.INCOME_NO, 1).pop();
    }

    /**
     * 生成一个 盘点单号
     *
     * @param orgId 机构id
     * @return 入库单号
     */
    public String genCheckNo(long orgId) {
        return genMaterialNo(orgId, MaterialNoType.CHECK_NO, 1).pop();
    }

    /**
     * 生成 规则 code(英文字符)+ 机构id+ yyMMdd 年月日 + 0001 四位递增
     * 
     * @param orgId 机构id
     * @param type 生成类型
     * @param count 生成数量
     * @return 单号
     */
    public LinkedList<String> genMaterialNo(long orgId, MaterialNoType type, int count) {

        if (count < 1) {
            throw new IllegalArgumentException("count < 1");
        }

        String orgIdStr;
        // 机构id 长度最大为两位
        if (String.valueOf(orgId).length() > 2) {
            orgIdStr = String.valueOf(orgId).substring(String.valueOf(orgId).length() - 2);
        } else {
            orgIdStr = String.format("%02d", orgId);
        }

        final String date = LocalDate.now().format(DateTimeFormatter.ofPattern(DATE_FORMAT));
        final String redisKey = redisPrefix.getBasePrefix() + REDIS_PREFIX + type.getCode() + ":" + date;

        final Long increment = stringRedisTemplate.opsForValue().increment(redisKey, count);

        if (Objects.isNull(increment)) {
            throw new LimsException(String.format("生成 [%s] 失败", type.getDesc()));
        }

        final LinkedList<String> materialNos = new LinkedList<>();

        for (int i = count; i > 0; i--) {
            materialNos.add(String.format("%s%s%s%s",
                // 单号类型
                type.getCode(),
                // 机构id
                orgIdStr,
                // 日期
                date,
                //
                StringUtils.leftPad(String.valueOf(increment - i + 1), Math.max(increment.toString().length(), 4),
                    '0')));
        }

        return materialNos;
    }

}
