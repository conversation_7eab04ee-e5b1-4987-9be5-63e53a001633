package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 物料结束盘点
 *
 * <AUTHOR>
 * @since 2023/5/12 11:44
 */
@Getter
@Setter
public class MaterialInventoryCheckFinishCheckRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 盘点ID
     */
    private Long checkId;

    /**
     * 盘点详情
     */
    private List<FinishCheckItemRequestVo> checkDetails;

    @Getter
    @Setter
    public static class FinishCheckItemRequestVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 详情ID
         */
        private Long detailId;

        /**
         * 实际主数量
         */
        private BigDecimal actualMainInventory;

        /**
         * 实际辅数量
         */
        private BigDecimal actualAssistInventory;

    }
}
