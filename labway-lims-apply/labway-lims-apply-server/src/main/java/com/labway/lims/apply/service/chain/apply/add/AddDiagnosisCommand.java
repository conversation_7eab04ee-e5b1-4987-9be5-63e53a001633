package com.labway.lims.apply.service.chain.apply.add;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.UpdateTestApplySampleDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.service.DictService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.labway.lims.api.enums.base.DictEnum.DIAGNOSIS;
import static com.labway.lims.api.web.BaseController.TEXTAREA_MAX_LENGTH;

/**
 * 保存临床诊断
 */
@Component
@Slf4j
public class AddDiagnosisCommand implements Command {

    @DubboReference
    private DictService dictService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @Override
    public boolean execute(Context c) throws Exception {
        final ApplyDto apply;
        final LoginUserHandler.User user;
        if (c instanceof AddApplyContext) {
            final AddApplyContext context = AddApplyContext.from(c);
            apply = context.getApply();
            user = context.getUser();
        } else {
            UpdateApplyContext context = UpdateApplyContext.from(c);

            if (context.getTestApply() instanceof UpdateTestApplySampleDto) {
                return CONTINUE_PROCESSING;
            }

            apply = context.getApply();
            user = context.getUser();
        }

        final String diagnosis = apply.getDiagnosis();
        // 是空  或者长度大于指定 @see base/dict/add
        if (StringUtils.isBlank(diagnosis) || StringUtils.length(diagnosis) > TEXTAREA_MAX_LENGTH * 2) {
            log.warn("临床诊断为空或者长度大于{}, [{}]", TEXTAREA_MAX_LENGTH * 2, diagnosis);
            return CONTINUE_PROCESSING;
        }

        // 异步处理， 不影响正常业务录入
        threadPoolConfig.getPool().submit(() -> {
            LoginUserHandler.set(user);
            try {
                final DictItemDto dictItemDto = buildDiagnosisDict(diagnosis);
                // 添加
                dictService.addDictItem(dictItemDto);

                rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                        TraceLog.newInstance()
                                .setModule(String.format(TraceLogModuleEnum.BASE_DICT.getDesc(), DictEnum.selectDescByName(dictItemDto.getDictType())))
                                .setContent(String.format("新增编码为 [%s] 值为 [%s] 字典", dictItemDto.getDictCode(), dictItemDto.getDictName())).toJSONString());
            } catch (Exception e) {
                log.error("新增临床诊断异常[{}]", e.getMessage());
            } finally {
                LoginUserHandler.remove();
            }
        });

        return CONTINUE_PROCESSING;
    }

    private DictItemDto buildDiagnosisDict(String diagnostics) {
        DictItemDto dictItemDto = new DictItemDto();
        dictItemDto.setDictCode(diagnostics);
        dictItemDto.setDictName(diagnostics);
        dictItemDto.setDictType(DIAGNOSIS.name());
        // 手动添加的数据
        dictItemDto.setDataSource(2);
        return dictItemDto;
    }
}
