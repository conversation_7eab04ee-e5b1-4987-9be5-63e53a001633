package com.labway.lims.apply.vo;

import java.io.Serializable;
import java.util.Set;

import lombok.Getter;
import lombok.Setter;

/**
 * 批量修改送检机构vo
 *
 * <AUTHOR>
 * @since 2024/2/21 11:27
 */
@Getter
@Setter
public class BatchUpdateHspOrgVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请单ids
     */
    private Set<Long> applyIds;

    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 是否刷新报告
     */
    private Boolean refreshReport;
}
