package com.labway.lims.apply.controller.rocheIT3000;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSONArray;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.WaitingOnePickApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.vo.IT3000HandleVo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 获取已经复核的样本，也就是等待一次分拣的样本
 */
@Slf4j
@RefreshScope
@Component
class RocheIT3000AuditedSamplesAction implements ActionStrategy {

    @Value("${audit-sample-query-hours:24}")
    private Integer auditSampleQueryHours;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplyService applyService;

    @Override
    public Object action(IT3000HandleVo vo) throws Exception {
        JSONArray barcodes = vo.getExtras().getJSONArray("barcodes");

        final Date now = new Date();
        Date queryBeginTime = DateUtils.addHours(now, -auditSampleQueryHours);
        DateTime beginOfDay = DateUtil.beginOfDay(now);
 //        List<ApplySampleDto> samples = applySampleService.selectByCreateDate(queryBeginTime.compareTo(beginOfDay) > 0 ? queryBeginTime : beginOfDay, now);
        List<WaitingOnePickApplySampleDto> samples = applySampleService.selectWaitingOnePickSamples(queryBeginTime.compareTo(beginOfDay) > 0 ? queryBeginTime : beginOfDay, now);

        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }

        if (CollectionUtils.isNotEmpty(barcodes)){
            samples = samples.stream().filter(sample -> barcodes.contains(sample.getBarcode())).collect(Collectors.toList());
        }

        List<Long> applyIds = samples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toList());
        List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);

        Map<Long, ApplyDto> applyDtoMap = applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, applyDto -> applyDto));

        // 根据创建日期排序
        samples.sort((o1, o2) -> o2.getApplySampleId().compareTo(o1.getApplySampleId()));

        final JSONArray array = new JSONArray();
        for (ApplySampleDto sample : samples) {

            // 过滤出未一次分拣的
            if (!Objects.equals(sample.getIsOnePick(), YesOrNoEnum.NO.getCode())) {
                log.info("样本 条码[{}] id[{}] 不是未一次分拣状态，跳过...", sample.getBarcode(), sample.getApplySampleId());
                continue;
            }

            ApplyDto applyDto = applyDtoMap.get(sample.getApplyId());
            if (applyDto == null) {
                log.info("样本 条码[{}] id[{}] 对应的申请单不存在，跳过...", sample.getBarcode(), sample.getApplySampleId());
                continue;
            }
            if (Objects.equals(applyDto.getStatus(), ApplyStatusEnum.WAIT_CHECK.getCode()) || Objects.equals(applyDto.getStatus(), ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode())) {
                log.info("样本 条码[{}] id[{}] 对应的申请单状态未复核，跳过...", sample.getBarcode(), sample.getApplySampleId());
                continue;
            }

            final Sample e = new Sample();
            e.setBarcode(sample.getBarcode());
            e.setApplySampleId(sample.getApplySampleId());
//            e.getExtras().set("_sample", sample);
            array.add(e);
        }

        return array;
    }

    @Override
    public IT3000HandleVo.Action action() {
        return IT3000HandleVo.Action.AUDITED_SAMPLES;
    }


    @Getter
    @Setter
    private static class Sample {
        /**
         * 条码号
         */
        private String barcode;


        /**
         * 仪器样本ID
         */
        private Long applySampleId;

        /**
         * 额外参数
         */
        private Dict extras = new Dict();
    }
}
