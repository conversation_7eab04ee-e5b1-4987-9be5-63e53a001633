package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.model.TbMaterialReceiveRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 物料领用记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Mapper
public interface TbMaterialReceiveRecordMapper extends BaseMapper<TbMaterialReceiveRecord> {

    /**
     * 批量 插入
     */
    void batchAddMaterialReceiveRecords(@Param("conditions") List<TbMaterialReceiveRecord> conditions);

}
