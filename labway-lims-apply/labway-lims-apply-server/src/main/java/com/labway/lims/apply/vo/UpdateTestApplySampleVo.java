package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class UpdateTestApplySampleVo extends TestApplyVo {

    /**
     * 申请单id
     */
    private Long applyId;
    /**
     * 申请单样本id
     */
    private List<Long> applySampleIds;
    /**
     * 申请单样本id
     */
    private Long applySampleId;
    /**
     * 项目信息
     */
    private List<UpdateTestApplyItemVo> testApplySampleItems;

    /**
     * 是否刷新报告
     */
    private Boolean refreshReport;
    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;
    /**
     * 管型
     */
    private String tubeName;
    /**
     * 管型CODE
     */
    private String tubeCode;
    /**
     *  是否为确认？ status = 1 未确认，为 Null 则为确认操作保存
     */
    private Integer confirmStatus;

    /**
     * 是否忽略同人同天同项目校验 0否1是
     */
    private Integer ignoreSameItem;

    /**
     * 是否忽略检验项目限制性别校验 true:忽略 false:校验
     */
    private Boolean ignoreItemLimitSex;

    /**
     * 条码号
     */
    private String barcode;

	/**
	 * 标本部位
	 * @since 1.1.4
	 * @Description <a href="https://www.tapd.cn/59091617/prong/stories/view/1159091617001001756?from_iteration_id=1159091617001000242">评论</a>
	 */
	private String patientPart;
}
