package com.labway.lims.apply.service.chain.pick.two;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.MicrobiologyTwoPickDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 血培养分拣
 */
@Slf4j
@Component
public class TwoPickMicrobiologyCommand implements Command, Filter, InitializingBean, DisposableBean {

    private static final String MC_SAMPLES = IdUtil.objectId();

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private TwoPickCommand twoPickCommand;

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);
        if (!(context.getTwoPick() instanceof MicrobiologyTwoPickDto)) {
            return CONTINUE_PROCESSING;
        }

        final MicrobiologyTwoPickDto twoPick = (MicrobiologyTwoPickDto) context.getTwoPick();

        // 如果微生物项目数量大于 1 且都是微生物检验，那么需要分管子，和血培养差不多
        if (context.getApplySampleItems().size() > 1 && !context.getApplySampleItems().stream().allMatch(e -> Objects.equals(e.getItemType(),
                ItemTypeEnum.MICROBIOLOGY.name()))) {
            return CONTINUE_PROCESSING;
        }

        final LinkedList<Long> ids = snowflakeService.genIds(300);
        final LoginUserHandler.User user = LoginUserHandler.get();
        final List<ApplySampleDto> applySamples = new ArrayList<>();
        final List<ApplySampleItemDto> items = new ArrayList<>();

        ApplySampleDto originalApplySample = context.getApplySample();
        String originalSampleProperty = originalApplySample.getSampleProperty();
        String originalSamplePropertyCode = originalApplySample.getSamplePropertyCode();

        // 设置样本性状 这里获取第一个，下文复制样本忽略了第一个
        setSampleType(context.getApplySampleItems().get(0),originalApplySample,twoPick.getItemPropertyRelationList());

        for (int i = 1; i < context.getApplySampleItems().size(); i++) {
            final ApplySampleDto applySample = new ApplySampleDto();
            BeanUtils.copyProperties(originalApplySample, applySample);
            applySample.setApplySampleId(ids.pop());
            applySample.setSampleProperty(originalSampleProperty);
            applySample.setSamplePropertyCode(originalSamplePropertyCode);

            // 设置样本性状
            setSampleType(context.getApplySampleItems().get(i),applySample,twoPick.getItemPropertyRelationList());

            final ApplySampleItemDto item = new ApplySampleItemDto();
            BeanUtils.copyProperties(context.getApplySampleItems().get(i), item);
            item.setApplySampleId(applySample.getApplySampleId());
            item.setApplySampleItemId(ids.pop());

            applySamples.add(applySample);
            items.add(item);

            applySampleItemService.deleteByApplySampleItemId(context.getApplySampleItems()
                    .get(i).getApplySampleItemId());

        }


        // 事务外执行
        threadPoolConfig.getPool().submit(() -> {
            LoginUserHandler.set(user);
            try {
                // 更新原始样本性状
                applySampleService.updateByApplySampleId(originalApplySample);

                // 新增申请单样本
                applySampleService.addApplySamples(applySamples);

                // 新增项目
                applySampleItemService.addApplySampleItems(items);

                // 复制条码环节
                sampleFlowService.copySampleFlows(context.getTwoPick().getApplySampleId(),
                        applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));
            } finally {
                LoginUserHandler.remove();
            }
        }).get(10, TimeUnit.SECONDS);

        context.put(MC_SAMPLES, applySamples);

        String sampleNo = twoPick.getSampleNo();
        for (ApplySampleDto applySample : applySamples) {
            final ApplySampleTwoPickDto stp = new ApplySampleTwoPickDto();
            stp.setGroupId(applySample.getGroupId());
            stp.setApplyId(applySample.getApplyId());
            stp.setApplySampleId(applySample.getApplySampleId());
            stp.setInstrumentGroupId(context.getInstrumentGroup().getInstrumentGroupId());
            stp.setInstrumentGroupName(context.getInstrumentGroup().getInstrumentGroupName());

            if (StringUtils.isNotBlank(sampleNo)) {
                sampleNo = incr(sampleNo);
                if (!twoPickCommand.canActiveSampleNo(applySample.getGroupId(), sampleNo,
                        twoPick.getTwoPickDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())) {
                    throw new IllegalStateException(String.format("样本号 [%s] 已被使用", sampleNo));
                }
                stp.setSampleNo(sampleNo);
            }

            stp.setBarcode(context.getApplySample().getBarcode());
            stp.setIsTransform(false);
            stp.setIsUrgent(false);
            context.getApplySampleTwoPicks().add(stp);
        }


        return CONTINUE_PROCESSING;
    }

    private String incr(String base) {
        final StringBuilder suffix = new StringBuilder();
        final StringBuilder prefix = new StringBuilder();
        final char[] chars = base.toCharArray();
        for (int i = chars.length - 1; i >= 0; i--) {
            if (NumberUtils.toInt(String.valueOf(chars[i])) <= 9) {
                suffix.insert(0, chars[i]);
            } else {
                prefix.append(ArrayUtils.subarray(chars, 0, i + 1));
                break;
            }
        }

        return prefix.append((NumberUtils.toLong(suffix.toString()) + 1))
                .toString();
    }

    @Override
    @SuppressWarnings("unchecked")
    public boolean postprocess(Context c, Exception exception) {
        if (Objects.isNull(exception)) {
            return CONTINUE_PROCESSING;
        }

        final Object o = c.get(MC_SAMPLES);
        if (!(o instanceof Collection)) {
            return CONTINUE_PROCESSING;
        }

        try {
            // 事务外执行
            threadPoolConfig.getPool().submit(() -> {
                applySampleService.deleteByApplySampleIds((Collection<Long>) o);
                log.info("微生物二次分拣样本 [{}] 失败，删除生成的申请单样本 {} 成功", TwoPickContext.from(c).getApplySample().getBarcode(), o);
            }).get(1, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("微生物二次分拣样本 [{}] 失败", TwoPickContext.from(c).getApplySample().getBarcode(), e);
        }

        return CONTINUE_PROCESSING;
    }

    // 设置微生物样本性状
    private void setSampleType( ApplySampleItemDto item, ApplySampleDto applySample, List<MicrobiologyTwoPickDto.MicrobiologyItemPropertyRelationDto> itemPropertyRelationList) {
        if (CollectionUtils.isEmpty(itemPropertyRelationList)) {
            return;
        }

        // 获取用户选择的样本性状
        MicrobiologyTwoPickDto.MicrobiologyItemPropertyRelationDto microbiologyItemPropertyRelationDto = itemPropertyRelationList.stream().filter(relation -> Objects.equals(item.getTestItemCode(), relation.getTestItemCode())).findFirst().orElse(null);
        if (Objects.isNull(microbiologyItemPropertyRelationDto)) {
            return;
        }

        applySample.setSamplePropertyCode(microbiologyItemPropertyRelationDto.getPropertyCode());
        applySample.setSampleProperty(microbiologyItemPropertyRelationDto.getPropertyName());
    }

    @Override
    public void destroy() throws Exception {
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }


}
