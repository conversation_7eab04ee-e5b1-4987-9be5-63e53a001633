package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.PhysicalRegisterDto;
import com.labway.lims.apply.api.dto.PhysicalSampleDto;
import com.labway.lims.apply.api.dto.PhysicalSampleItemDto;
import com.labway.lims.apply.api.service.PhysicalRegisterService;
import com.labway.lims.apply.api.service.PhysicalSampleItemService;
import com.labway.lims.apply.api.service.PhysicalSampleService;
import com.labway.lims.apply.mapper.TbPhysicalSampleMapper;
import com.labway.lims.apply.mapstruct.PhysicalSampleConverter;
import com.labway.lims.apply.model.TbPhysicalSample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 体检样本 Service impl
 * 
 * <AUTHOR>
 * @since 2023/4/4 13:55
 */
@Slf4j
@DubboService
public class PhysicalSampleServiceImpl implements PhysicalSampleService {

    @Resource
    private TbPhysicalSampleMapper tbPhysicalSampleMapper;

    @Resource
    private PhysicalSampleItemService physicalSampleItemService;

    @Resource
    private PhysicalRegisterService physicalRegisterService;

    @Resource
    private PhysicalSampleConverter physicalSampleConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPhysicalSamples(List<PhysicalSampleDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 体检样本
        List<TbPhysicalSample> targetList = list.stream()
            .map(obj -> JSON.parseObject(JSON.toJSONString(obj), TbPhysicalSample.class)).collect(Collectors.toList());
        // 数量 分区批次插入
        List<List<TbPhysicalSample>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbPhysicalSampleMapper.batchAddPhysicalSample(item));

        log.info("用户 [{}] 新增体检样本[{}]成功", loginUser.getNickname(), JSON.toJSONString(targetList));
    }

    @Override
    public List<PhysicalSampleDto> selectByPhysicalRegisterIds(Collection<Long> physicalRegisterIds) {
        if (CollectionUtils.isEmpty(physicalRegisterIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbPhysicalSample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbPhysicalSample::getPhysicalRegisterId, physicalRegisterIds);
        queryWrapper.eq(TbPhysicalSample::getIsDelete, YesOrNoEnum.NO.getCode());
        return physicalSampleConverter.fromTbPhysicalSampleList(tbPhysicalSampleMapper.selectList(queryWrapper));
    }

    @Override
    public List<PhysicalSampleDto> selectByBarcodes(Collection<String> barcodes, long orgId) {
        if (CollectionUtils.isEmpty(barcodes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbPhysicalSample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbPhysicalSample::getBarcode, barcodes);
        queryWrapper.eq(TbPhysicalSample::getOrgId, orgId);
        queryWrapper.eq(TbPhysicalSample::getIsDelete, YesOrNoEnum.NO.getCode());
        return physicalSampleConverter.fromTbPhysicalSampleList(tbPhysicalSampleMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public PhysicalSampleDto selectByBarcode(String barcode, long orgId) {
        if (StringUtils.isBlank(barcode)) {
            return null;
        }
        LambdaQueryWrapper<TbPhysicalSample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbPhysicalSample::getBarcode, barcode);
        queryWrapper.eq(TbPhysicalSample::getOrgId, orgId);
        queryWrapper.eq(TbPhysicalSample::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return physicalSampleConverter.fromTbPhysicalSample(tbPhysicalSampleMapper.selectOne(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByPhysicalSampleId(PhysicalSampleDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbPhysicalSample target = physicalSampleConverter.tbPhysicalSampleFromTbDto(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbPhysicalSampleMapper.updateById(target) < 1) {
            throw new LimsException("修改体检样本失败");
        }

        log.info("用户 [{}] 修改体检样本成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }

    @Override
    public List<PhysicalSampleDto> selectByPhysicalBatchId(long physicalBatchId) {

        LambdaQueryWrapper<TbPhysicalSample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbPhysicalSample::getPhysicalBatchId, physicalBatchId);
        queryWrapper.eq(TbPhysicalSample::getIsDelete, YesOrNoEnum.NO.getCode());

        return physicalSampleConverter.fromTbPhysicalSampleList(tbPhysicalSampleMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void printBarcodeHandleData(List<PhysicalSampleDto> sampleDtoList,
        List<PhysicalSampleItemDto> sampleItemDtoList, List<PhysicalRegisterDto> registerDtoList) {
        // 添加 体检样本s
        this.addPhysicalSamples(sampleDtoList);

        // 添加 体检样本项目s
        physicalSampleItemService.addPhysicalSampleItems(sampleItemDtoList);

        // 修改 体检人打印状态为已打印
        registerDtoList.forEach(item -> physicalRegisterService.updateByPhysicalBatchId(item));

    }

    @Override
    public boolean deleteById(long physicalSampleId) {
        return tbPhysicalSampleMapper.deleteById(physicalSampleId) > 0;
    }

}
