package com.labway.lims.apply.service.chain.pick.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.enums.RackStatusEnum;
import com.labway.lims.base.api.service.RackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 逻辑试管架标记废弃
 */
@Slf4j
@Component
public class TwoPickIdelRackLogicCommand implements Command {

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private RackLogicService rackLogicService;
    @DubboReference
    private RackService rackService;

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);

        // 如果有一个还存在为二次分拣那么就跳过
        if (applySampleService.selectByRackLogicId(context.getRackLogic().getRackLogicId())
                .stream().anyMatch(e -> Objects.equals(e.getIsTwoPick(), YesOrNoEnum.NO.getCode()))) {
            return CONTINUE_PROCESSING;
        }

        // 当试管架下面的样本都二次分拣了，那么试管架状态标记为终止
        final RackLogicDto mrl = new RackLogicDto();
        mrl.setRackLogicId(context.getRackLogic().getRackLogicId());
        mrl.setPosition(RackLogicPositionEnum.END.getCode());
        mrl.setUpdateDate(new Date());
        mrl.setUpdaterId(LoginUserHandler.get().getUserId());
        mrl.setUpdaterName(LoginUserHandler.get().getNickname());
        rackLogicService.updateByRackLogicId(mrl);

        // 以下代码是当试管架状态为结束时，自动回收试管架
        final Long rackId = context.getRackLogic().getRackId();
        if(Objects.isNull(rackId) || Objects.equals(rackId, NumberUtils.LONG_ZERO)){
            return CONTINUE_PROCESSING;
        }

        // 物理试管架对应所有的逻辑试管架
        List<RackLogicDto> rackLogicDtos = rackLogicService.selectByRackId(rackId);
        if (CollectionUtils.isNotEmpty(rackLogicDtos)) {
            // 自身的逻辑试管架
            Long rackLogicId = context.getRackLogic().getRackLogicId();

            // 结束状态的试管架ID
            // 试管架回收逻辑：1.排除自身逻辑试管架ID 2.其他的试管架的position都结束了，才能回收
            boolean isRecycle = rackLogicDtos.stream().filter(v -> !Objects.equals(v.getRackLogicId(), rackLogicId))
                    .allMatch(e -> Objects.equals(ObjectUtils.defaultIfNull(e.getPosition(), RackLogicPositionEnum.UNKNOWN.getCode()), RackLogicPositionEnum.END.getCode()));

            if (isRecycle) {

                final RackDto rackDto = new RackDto();
                rackDto.setStatus(RackStatusEnum.IDLE.getCode());
                // 更新物理试管架状态为空闲
                rackService.updateByRackIds(rackDto, Collections.singleton(rackId));

                // 删除逻辑试管架
                rackLogicService.deleteByRackIds(Collections.singleton(rackId));
            }
        }

        return CONTINUE_PROCESSING;
    }


}
