package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 异常 确认异常
 * 
 * <AUTHOR>
 * @since 2023/4/12 17:42
 */
@Getter
@Setter
public class SampleAbnormalConfirmRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 异常值ID
     */
    private Long sampleAbnormalId;
    /**
     * 确认内容
     */
    private String confirmContent;

}
