package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.apply.api.dto.HandoverRecordQueryDto;
import com.labway.lims.apply.api.dto.PreprocessingHandoverRecordDto;
import com.labway.lims.apply.api.service.PreprocessingHandoverRecordService;
import com.labway.lims.apply.mapper.PreprocessingHandoverRecordMapper;
import com.labway.lims.apply.model.TbPreprocessingHandoverRecord;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@DubboService
public class PreprocessingHandoverRecordServiceImpl implements PreprocessingHandoverRecordService {
    @Resource
    private PreprocessingHandoverRecordMapper preprocessingHandoverRecordMapper;

    @Override
    public List<PreprocessingHandoverRecordDto> handoverRecords(HandoverRecordQueryDto query) {
        final String barcode = query.getBarcode();
        final Long hspOrgId = query.getHspOrgId();
        final Date startDate = query.getStartDate();
        final Date endDate = query.getEndDate();
        if (Objects.isNull(hspOrgId) || Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbPreprocessingHandoverRecord> eq = Wrappers.lambdaQuery(TbPreprocessingHandoverRecord.class)
                .eq(TbPreprocessingHandoverRecord::getHspOrgId, hspOrgId)
                .between(TbPreprocessingHandoverRecord::getHandoverDate, startDate, endDate)
                .eq(StringUtils.isNotBlank(barcode), TbPreprocessingHandoverRecord::getBarcode, barcode)
                .orderByAsc(TbPreprocessingHandoverRecord::getHandoverId);

        return preprocessingHandoverRecordMapper.selectList(eq)
                .stream().map(this::convertTbToDto).collect(Collectors.toList());

    }

    @Override
    public void addBatch(List<PreprocessingHandoverRecordDto> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        for (PreprocessingHandoverRecordDto record : records) {
            final TbPreprocessingHandoverRecord tb = JSON.parseObject(JSON.toJSONString(record), TbPreprocessingHandoverRecord.class);
            preprocessingHandoverRecordMapper.insert(tb);
        }
    }

    @Override
    public void deleteByHandoverIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        preprocessingHandoverRecordMapper.deleteBatchIds(ids);
    }

    @Override
    public List<PreprocessingHandoverRecordDto> selectByBarcode(String barcode, String hspOrgCode) {

        if (StringUtils.isAnyBlank(barcode, hspOrgCode)) {
            return Collections.emptyList();
        }

        return preprocessingHandoverRecordMapper.selectList(new LambdaQueryWrapper<TbPreprocessingHandoverRecord>()
                        .eq(TbPreprocessingHandoverRecord::getBarcode, barcode)
                        .eq(TbPreprocessingHandoverRecord::getHspOrgCode, hspOrgCode))
                .stream().map(this::convertTbToDto).collect(Collectors.toList());
    }

    @Override
    public List<PreprocessingHandoverRecordDto> selectByIds(List<Long> handoverIds) {
        return preprocessingHandoverRecordMapper.selectBatchIds(handoverIds)
                .stream().map(this::convertTbToDto).collect(Collectors.toList());
    }

    @Override
    public void deleteByHspOrgCodeAndBarcodes(String hspOrgCode, Set<String> barcodes) {
        if (CollectionUtils.isEmpty(barcodes) || StringUtils.isEmpty(hspOrgCode)) {
            return;
        }
        final LambdaQueryWrapper<TbPreprocessingHandoverRecord> in = Wrappers.lambdaQuery(TbPreprocessingHandoverRecord.class)
                .eq(TbPreprocessingHandoverRecord::getHspOrgCode, hspOrgCode)
                .in(TbPreprocessingHandoverRecord::getBarcode, barcodes);
        preprocessingHandoverRecordMapper.delete(in);
    }

    public PreprocessingHandoverRecordDto convertTbToDto(TbPreprocessingHandoverRecord tb) {
        return JSON.parseObject(JSON.toJSONString(tb), PreprocessingHandoverRecordDto.class);
    }
}
