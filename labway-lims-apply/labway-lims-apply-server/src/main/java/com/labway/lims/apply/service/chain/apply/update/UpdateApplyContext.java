package com.labway.lims.apply.service.chain.apply.update;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.AddSamplesInfoDto;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.PdaApplyInfoDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.dto.UpdateTestApplySampleDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@SuppressWarnings("unchecked")
public class UpdateApplyContext extends StopWatchContext {

    /**
     * 返回信息
     */
    public static final String APPLY_INFO = "APPLY_INFO_" + IdUtil.objectId();
    /**
     * 返回信息
     */
    public static final String PDA_APPLY_INFO = "PDA_APPLY_INFO_" + IdUtil.objectId();

    /**
     *  返回的新增样本信息
     */
    public static final String ADD_SAMPLES_INFO = "ADD_SAMPLES_INFO_" + IdUtil.objectId();
    /**
     * 申请单
     */
    public static final String APPLY = "APPLY_" + IdUtil.objectId();
    /**
     * 申请单
     */
    public static final String PDA_APPLY = "PDA_APPLY_" + IdUtil.objectId();
    /**
     * 样本
     */
    public static final String SAMPLES = "SAMPLES_" + IdUtil.objectId();

    /**
     * 需要添加的申请单样本
     */
    public static final String ADD_APPLY_SAMPLES = "ADD_APPLY_SAMPLES_" + IdUtil.objectId();

    /**
     * 需要修改的申请单样本
     */
    public static final String UPDATE_APPLY_SAMPLE = "UPDATE_APPLY_SAMPLE_" + IdUtil.objectId();

    /**
     * 需要添加申请单样本项目
     */
    public static final String ADD_APPLY_SAMPLE_ITEMS = "ADD_APPLY_SAMPLE_ITEMS_" + IdUtil.objectId();
    /**
     * 需要更新的申请单样本
     */
    public static final String UPDATE_APPLY_SAMPLE_ITEMS = "UPDATE_APPLY_SAMPLE_ITEMS_" + IdUtil.objectId();

    /**
     * 检验项目
     */
    public static final String TEST_ITEMS = "TEST_ITEMS_" + IdUtil.objectId();

    /**
     * 送检机构
     */
    public static final String HSP_ORG = "HSP_ORG_" + IdUtil.objectId();

    /**
     * 血培养
     */
    public static final String BLOOD_CULTURE = "BLOOD_CULTURE_" + IdUtil.objectId();

    /**
     * 需要删除的样本项目
     */
    public static final String DELETE_APPLY_SAMPLE_ITEM_IDS = "DELETE_APPLY_SAMPLE_ITEM_IDS_" + IdUtil.objectId();
    /**
     * 需要删除的样本
     */
    public static final String DELETE_APPLY_SAMPLE_IDS = "DELETE_APPLY_SAMPLE_IDS_" + IdUtil.objectId();

    @NonNull
    private TestApplyDto testApply;

    private LoginUserHandler.User user;

    /**
     * 是否修改了年龄或性别
     */
    private Boolean isUpdateSexOrAge;

    /**
     * 是否修改了年龄或性别或送检机构或样本类型
     */
    private Boolean isUpdateSexOrAgeOrHspOrgOrSampleType;

    /**
     * 外部条码号
     */
    private String outBarcode;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 差异提示
     */
    public static final Object DIFF_MSG = "DIFF_MSG" + IdUtil.objectId();

    /**
     * 申请单样本差异提示
     */
    public static final Object APPLY_SAMPLE_DIFF_MSG = "APPLY_SAMPLE_DIFF_MSG_" + IdUtil.objectId();

    /**
     * 是否一次分拣之前， fasle复核之前，  true， 一次分拣之前
     */
    private boolean batchUpdateItem = false;


    public static UpdateApplyContext from(Context context) {
        return (UpdateApplyContext) context;
    }

    public ApplyDto getApply() {
        return (ApplyDto) get(APPLY);
    }

    public PdaApplyDto getPdaApply() {
        return (PdaApplyDto) get(PDA_APPLY);
    }
    //     public static final String ADD_SAMPLES_INFO = "ADD_SAMPLES_INFO_" + IdUtil.objectId();
    public AddSamplesInfoDto getAddSamplesInfo(){
        return (AddSamplesInfoDto)get(ADD_SAMPLES_INFO);
    }

    public List<ApplySampleDto> getAddApplySamples() {
        return (List<ApplySampleDto>) get(ADD_APPLY_SAMPLES);
    }

    public ApplySampleDto getUpdateApplySample() {
        return (ApplySampleDto) get(UPDATE_APPLY_SAMPLE);
    }

    public List<ApplySampleItemDto> getAddApplySampleItems() {
        return (List<ApplySampleItemDto>) get(ADD_APPLY_SAMPLE_ITEMS);
    }

    public List<ApplySampleItemDto> getUpdateApplySampleItems() {
        return (List<ApplySampleItemDto>) get(UPDATE_APPLY_SAMPLE_ITEMS);
    }

    public Long getApplyId() {
        return ObjectUtils.defaultIfNull(testApply.getApplyId(), NumberUtils.LONG_ZERO);
    }

    public List<Long> getApplySampleIds() {
        if (testApply instanceof UpdateTestApplySampleDto) {
            return ((UpdateTestApplySampleDto) testApply).getApplySampleIds();
        }
        return List.of();
    }

    public ApplyInfo getApplyInfo() {
        return (ApplyInfo) get(APPLY_INFO);
    }
    public PdaApplyInfoDto getPdaApplyInfo() {
        return (PdaApplyInfoDto) get(PDA_APPLY_INFO);
    }

    public List<TestItemDto> getTestItems() {
        return (List<TestItemDto>) get(TEST_ITEMS);
    }

    public HspOrganizationDto getHspOrganization() {
        return (HspOrganizationDto) get(HSP_ORG);
    }

    public ApplySampleItemBloodCultureDto getBloodCulture() {
        return (ApplySampleItemBloodCultureDto) get(BLOOD_CULTURE);
    }

    public String getDiffMsg() {
        return Objects.isNull(get(DIFF_MSG)) ? "" : (String) get(DIFF_MSG);
    }

    public String getApplySampleDiffMsg() {
        return Objects.isNull(get(APPLY_SAMPLE_DIFF_MSG)) ? "" : (String) get(APPLY_SAMPLE_DIFF_MSG);
    }
    /**
     * 不一定每次都能获取到
     */
    @Nullable
    public List<ApplySampleDto> getApplySamples() {
        return (List<ApplySampleDto>) get(SAMPLES);
    }

    public Set<Long> getDeleteApplySampleItemIdList() {
        return (Set<Long>) get(DELETE_APPLY_SAMPLE_ITEM_IDS);
    }

    public Set<Long> getDeleteApplySampleIds() {
        return (Set<Long>) get(DELETE_APPLY_SAMPLE_IDS);
    }


    @Override
    protected String getWatcherName() {
        return "修改申请单";
    }

}
