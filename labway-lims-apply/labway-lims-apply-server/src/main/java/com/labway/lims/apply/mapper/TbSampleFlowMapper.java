package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.model.TbSampleFlow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 条码环节 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbSampleFlowMapper extends BaseMapper<TbSampleFlow> {

    /**
     * 批量心中
     */
    int addBatch(@Param("flows") List<SampleFlowDto> flows);
}
