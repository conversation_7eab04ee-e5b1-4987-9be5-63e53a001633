package com.labway.lims.apply.service.chain.material.delivery.income;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 物料入库责任链
 *
 * <AUTHOR>
 * @since 2023/5/9 10:02
 */
@Component
public class MaterialIncomeChain extends ChainBase implements InitializingBean {

    @Resource
    private MaterialIncomeCheckParamCommand materialIncomeCheckParamCommand;

    @Resource
    private MaterialIncomeAddIncomeInfoCommand materialIncomeAddIncomeInfoCommand;

    @Resource
    private MaterialIncomeAddIncomeRecordCommand materialIncomeAddIncomeRecordCommand;
    @Resource
    private MaterialIncomeUpdateDeliveryStatusCommand materialIncomeUpdateDeliveryStatusCommand;

    @Resource
    private MaterialIncomeAddMaterialInventoryCommand materialIncomeAddMaterialInventoryCommand;

    @Resource
    private MaterialIncomeAddGroupMaterialCommand materialIncomeAddGroupMaterialCommand;


    @Override
    public void afterPropertiesSet() throws Exception {

        // 检查 参数
        addCommand(materialIncomeCheckParamCommand);

        // 增加入库信息
//        addCommand(materialIncomeAddIncomeInfoCommand);

        // 添加 入库记录
        addCommand(materialIncomeAddIncomeRecordCommand);

        // 修改 待入库记录 状态为已入库
        addCommand(materialIncomeUpdateDeliveryStatusCommand);

        // 修改 物料 库存
        addCommand(materialIncomeAddMaterialInventoryCommand);

        // 修改 专业组 物料总库存
        addCommand(materialIncomeAddGroupMaterialCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);
    }
}
