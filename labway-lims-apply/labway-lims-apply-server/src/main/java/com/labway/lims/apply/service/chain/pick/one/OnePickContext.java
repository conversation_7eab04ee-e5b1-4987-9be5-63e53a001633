package com.labway.lims.apply.service.chain.pick.one;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.TestItemDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.BooleanUtils;

import java.awt.*;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Getter
@Setter
@SuppressWarnings("unchecked")
public class OnePickContext extends StopWatchContext {


    /**
     * 涉及的项目
     */
    public static final String APPLY_SAMPLE_ITEMS = "APPLY_SAMPLE_ITEMS_" + IdUtil.objectId();

    /**
     * 申请单样本
     */
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    /**
     * 检验项目
     */
    public static final String TEST_ITEMS = "TEST_ITEMS_" + IdUtil.objectId();

    /**
     * 专业组
     */
    public static final String GROUP = "GROUP_" + IdUtil.objectId();

    /**
     * 逻辑试管架
     */
    public static final String RACK_LOGIC = "RACK_LOGIC_" + IdUtil.objectId();

    /**
     * 分拣到的 xy 点
     */
    public static final String POINT = "POINT_" + IdUtil.objectId();

    /**
     * 逻辑试管架剩余的空间
     */
    public static final String SPACES = "SPACES_" + IdUtil.objectId();

    /**
     * 是否是外送
     */
    public static final String IS_OUTSOURCING = "IS_OUTSOURCING_" + IdUtil.objectId();


    /**
     * 申请单样本ID
     */
    private final long applySampleId;


    public OnePickContext(long applySampleId) {
        this.applySampleId = applySampleId;
    }

    public static OnePickContext from(Context context) {
        return (OnePickContext) context;
    }

    public List<ApplySampleItemDto> getApplySampleItems() {
        return (List<ApplySampleItemDto>) get(APPLY_SAMPLE_ITEMS);
    }

    /**
     * key: testItemCode
     */
    public Map<String, TestItemDto> getTestItems() {
        return (Map<String, TestItemDto>) get(TEST_ITEMS);
    }

    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }

    public ProfessionalGroupDto getProfessionalGroup() {
        return (ProfessionalGroupDto) get(GROUP);
    }

    public Point getPoint() {
        return (Point) get(POINT);
    }

    public int getSpaces() {
        if (get(SPACES) instanceof Integer) {
            return ((Integer) get(SPACES));
        }
        return 0;
    }


    public RackLogicDto getRackLogic() {
        return (RackLogicDto) get(RACK_LOGIC);
    }

    public boolean isOursourcing() {
        Object o = get(IS_OUTSOURCING);
        return Objects.nonNull(o) && BooleanUtils.isTrue((Boolean) o);
    }

    @Override
    protected String getWatcherName() {
        return "一次分拣";
    }

}
