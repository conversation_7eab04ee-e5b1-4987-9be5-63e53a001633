package com.labway.lims.apply.service.chain.pick.two.multi;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 批量二次分拣
 */
@Slf4j
@Component
public class MultiTwoPickChain extends ChainBase implements InitializingBean {
    // 限流
    @Resource
    private MultiTwoPickLimitCommand multiTwoPickLimitCommand;
    // 校验参数
    @Resource
    private MultiTwoPickCheckParamCommand multiTwoPickCheckParamCommand;
    // 填充信息
    @Resource
    private MultiTwoPickFillInfoCommand multiTwoPickFillInfoCommand;
    // 判断是否已停止或已终止
    @Resource
    private MultiTwoPickCheckApplySampleStatusCommand multiTwoPickCheckApplySampleStatusCommand;
    // 获取到要分拣的专业小组
    @Resource
    private MultiTwoPickInstrumentGroupCommand multiTwoPickInstrumentGroupCommand;
    // 获取到要分拣的专业小组
    @Resource
    private MultiTwoPickInstrumentGroupCanDoCommand multiTwoPickInstrumentGroupCanDoCommand;
    // 二次分拣
    @Resource
    private MultiTwoPickCommand multiTwoPickCommand;
    // redis 标记
    @Resource
    private MultiTwoPickRedisMarkCommand multiTwoPickRedisMarkCommand;
    // 修改分拣信息
    @Resource
    private MultiTwoPickUpdateSampleInfoCommand multiTwoPickUpdateSampleInfoCommand;
    //如果涉及到组间交接，复制逻辑试管架 进行组间交接
    @Resource
    private MultiTwoPickTransformCommand multiTwoPickTransformCommand;
    // 修改试管架状态，标记为环节已经结束
    @Resource
    private MultiTwoPickIdelRackLogicCommand multiTwoPickIdelRackLogicCommand;
    // 删除试管架占用
    @Resource
    private MultiTwoPickRemoveRackSpaceCommand multiTwoPickRemoveRackSpaceCommand;
    // 条码环节
    @Resource
    private MultiTwoPickFlowCommand multiTwoPickFlowCommand;
    // 发送消息到mq
    @Resource
    private MultiTwoPickRabbitMQCommand multiTwoPickRabbitMQCommand;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean execute(Context context) throws Exception {
        return super.execute(context);
    }

    @Override
    public void afterPropertiesSet() {

        // 限流
        addCommand(multiTwoPickLimitCommand);

        // 校验参数
        addCommand(multiTwoPickCheckParamCommand);

        // 填充信息
        addCommand(multiTwoPickFillInfoCommand);

        // 判断是否已停止或已终止
        addCommand(multiTwoPickCheckApplySampleStatusCommand);

        // 获取到要分拣的专业小组
        addCommand(multiTwoPickInstrumentGroupCommand);

		// 判断专业小组能否做这个样本
		addCommand(multiTwoPickInstrumentGroupCanDoCommand);

        // 二次分拣
        addCommand(multiTwoPickCommand);

        // 修改分拣信息
        addCommand(multiTwoPickUpdateSampleInfoCommand);

        // 如果涉及到组间交接，复制逻辑试管架 进行组间交接
        addCommand(multiTwoPickTransformCommand);

        // 修改试管架状态，标记为环节已经结束
        addCommand(multiTwoPickIdelRackLogicCommand);

        // 删除试管架占用
        addCommand(multiTwoPickRemoveRackSpaceCommand);

        // redis 标记
        addCommand(multiTwoPickRedisMarkCommand);

        // 条码环节
        addCommand(multiTwoPickFlowCommand);

        // 发送消息到mq
        addCommand(multiTwoPickRabbitMQCommand);

        //  完成
        addCommand(context -> PROCESSING_COMPLETE);
    }
}
