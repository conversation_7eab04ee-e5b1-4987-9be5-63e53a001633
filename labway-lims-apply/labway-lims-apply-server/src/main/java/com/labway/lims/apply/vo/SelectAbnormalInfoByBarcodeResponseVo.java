package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * 根据条码号 获取 样本相关信息
 *
 * <AUTHOR>
 * @since 2023/4/12 13:27
 */
@Getter
@Setter
public class SelectAbnormalInfoByBarcodeResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 送检医生编码
     */
    private String sendDoctorCode;
    /**
     * 送检医生名称
     */
    private String sendDoctorName;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private String patientAge;

    /**
     * 性别
     * 
     * @see SexEnum
     */
    private Integer patientSex;
    /**
     * 检验项目
     */
    private String testItemName;

    /**
     * 检验项目ids
     */
    private Set<Long> testItemIds;

}
