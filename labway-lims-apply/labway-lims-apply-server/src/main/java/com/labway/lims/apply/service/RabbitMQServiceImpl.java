package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.consts.MqConstants;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.service.SystemParamReportDelayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/6/15 20:42
 */
@Slf4j
@DubboService
public class RabbitMQServiceImpl implements RabbitMQService {
    @Resource
    private RabbitTemplate rabbitTemplate;
    @DubboReference
    private SystemParamReportDelayService systemParamReportDelayService;

    @Override
    public void convertAndSend(String exchange, String routingKey, String message) {
        // 样本信息修改MQ消息
        if (MqConstants.ROUTING_KEY.equals(routingKey)) {
            final ApplySampleEventDto event = JSON.parseObject(message, ApplySampleEventDto.class);
            final ApplySampleEventDto.EventType eventType = event.getEvent();

            // 不是 二审消息，直接发送
            if (!Objects.equals(eventType, ApplySampleEventDto.EventType.TwoCheck)) {
                rabbitTemplate.convertAndSend(exchange, routingKey, message);
            } else {
                // 二审消息，根据配置的延迟时间发送
                final String hspOrgCode = event.getHspOrgCode();
                final Long reportDelayTime = systemParamReportDelayService.getReportDelayTime(hspOrgCode);
                convertAndSend(exchange, routingKey, message, reportDelayTime);
            }
        } else {
            // 不是 样本信息修改MQ消息，直接发送
            rabbitTemplate.convertAndSend(exchange, routingKey, message);
        }
    }

    /**
     * 发送延迟消息
     *
     * @param exchange   交换机
     * @param routingKey 路由键，这里传原路由键，如果延迟时间>0，则发送的路由键自动拼上"_dlx"
     * @param msg        消息内容
     * @param expiration 延迟时间，单位：毫秒
     */
    @Override
    public void convertAndSend(String exchange, String routingKey, String msg, Long expiration) {
        if (Objects.isNull(expiration) || expiration <= 0) {
            // 没有延迟时间，直接发送
            rabbitTemplate.convertAndSend(exchange, routingKey, msg);
            return;
        }

        final MessageProperties properties = new MessageProperties();
        properties.setExpiration(String.valueOf(expiration));
        final Message message = new Message(msg.getBytes(), properties);

        rabbitTemplate.send(exchange, (routingKey + MqConstants.DLX_ROUTING_KEY_SUFFIX), message);

        log.info("发送 MQ 消息 Exchange[{}] RoutingKey [{}] 过期时间 [{}] 消息内容 [{}]", exchange, routingKey, expiration, msg);
    }

}
