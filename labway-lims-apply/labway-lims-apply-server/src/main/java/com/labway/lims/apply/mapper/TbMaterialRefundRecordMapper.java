package com.labway.lims.apply.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.labway.lims.apply.model.entity.TbMaterialRefundRecord;

/**
 * 物流退库记录表(TbMaterialRefundRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-04 15:40:48
 */
@Mapper
public interface TbMaterialRefundRecordMapper extends BaseMapper<TbMaterialRefundRecord> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TbMaterialRefundRecord> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TbMaterialRefundRecord> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TbMaterialRefundRecord> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<TbMaterialRefundRecord> entities);

}

