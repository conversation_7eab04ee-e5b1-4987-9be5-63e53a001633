package com.labway.lims.apply.service.chain.material.delivery.income;

import com.labway.lims.api.enums.apply.MaterialDeliveryRecordStatusEnum;
import com.labway.lims.apply.api.dto.MaterialDeliveryRecordDto;
import com.labway.lims.apply.api.service.MaterialDeliveryRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 修改 待入库记录 状态为已入库
 * 
 * <AUTHOR>
 * @since 2023/5/9 14:28
 */
@Slf4j
@Component
public class MaterialIncomeUpdateDeliveryStatusCommand implements Command {

    @Resource
    private MaterialDeliveryRecordService materialDeliveryRecordService;

    @Override
    public boolean execute(Context context) throws Exception {
        final MaterialIncomeContext from = MaterialIncomeContext.from(context);
        var materialDeliveryRecord = from.getMaterialDeliveryRecord();

        MaterialDeliveryRecordDto deliveryRecordDto = new MaterialDeliveryRecordDto();
        deliveryRecordDto.setStatus(MaterialDeliveryRecordStatusEnum.WAREHOUSED.getCode());
        deliveryRecordDto.setRecordId(materialDeliveryRecord.getRecordId());
        materialDeliveryRecordService.updateByRecordId(deliveryRecordDto);
        return CONTINUE_PROCESSING;
    }
}
