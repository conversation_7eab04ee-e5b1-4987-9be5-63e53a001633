package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.apply.api.dto.UploadFileDto;
import com.labway.lims.apply.api.service.UploadFileService;
import com.labway.lims.apply.mapper.TbUploadFileMapper;
import com.labway.lims.apply.model.TbUploadFile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 上传文件 Service impl
 * </p>
 *
 */
@Slf4j
@DubboService
public class UploadFileServiceImpl extends ServiceImpl<TbUploadFileMapper, TbUploadFile> implements UploadFileService {

    @Override
    public void insertFile(UploadFileDto fileDto) {
        if (fileDto != null){
            TbUploadFile uploadFile = new TbUploadFile();
            BeanUtils.copyProperties(fileDto, uploadFile);
            uploadFile.setCreateTime(new Date());
            save(uploadFile);
        }
    }

    @Override
    public List<UploadFileDto> getFiles(String hspOrgCode,String resource) {
        if (StringUtils.isNotBlank(hspOrgCode)){
            LambdaQueryWrapper<TbUploadFile> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TbUploadFile::getHspOrgCode, hspOrgCode);
            if (StringUtils.isNotBlank(resource)){
                queryWrapper.eq(TbUploadFile::getResource, resource);
            }
            List<TbUploadFile> list = this.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(list)){
                return JSONArray.parseArray(JSONObject.toJSONString(list),UploadFileDto.class);
            }
        }
        return List.of();
    }
}
