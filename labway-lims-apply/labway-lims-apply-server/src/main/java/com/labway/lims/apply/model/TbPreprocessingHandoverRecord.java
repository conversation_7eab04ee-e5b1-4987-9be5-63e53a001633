package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 *
 */
@Getter
@Setter
@TableName("tb_preprocessing_handover_record")
public class TbPreprocessingHandoverRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交接id
     */
    @TableId
    private Long handoverId;
    /**
     * 条码
     */
    private String barcode;

    /**
     * 申请单号
     */
    private String formCode;

    /**
     * 外部项目名称多个逗号隔开
     */
    private String outItems;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别
     */
    private Integer patientSex;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubAge;

    /**
     * 子年龄单位
     */
    private String patientSubAgeUnit;

    /**
     * 采样时间
     */
    private Date samplingDate;

    /**
     * 交接时间
     */
    private Date handoverDate;

    /**
     * 交接人
     */
    private String handoverPeople;

    /**
     * 交接人id
     */
    private Long handoverPeopleId;

    /**
     * 物流人
     */
    private String logisticsPeople;

    /**
     * 管型
     */
    private String tubeName;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * is_delete
     */
    private Integer isDelete;

    /**
     * creator_id
     */
    private Long creatorId;

    /**
     * creator_name
     */
    private String creatorName;

    /**
     * create_date
     */
    private Date createDate;
    /**
     * update_date
     */
    private Date updateDate;
    /**
     * updater_id
     */
    private Long updaterId;

    /**
     * updater_name
     */
    private String updaterName;

    /**
     * org_id
     */
    private Long orgId;

    /**
     * org_name
     */
    private String orgName;

    public TbPreprocessingHandoverRecord() {
    }
}