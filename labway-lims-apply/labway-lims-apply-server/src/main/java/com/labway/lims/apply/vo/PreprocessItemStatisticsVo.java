package com.labway.lims.apply.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PreprocessItemStatisticsVo {

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目名称
     */
    private String testItemName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        final PreprocessItemStatisticsVo that = (PreprocessItemStatisticsVo) o;
        return Objects.equals(hspOrgId, that.hspOrgId) && Objects.equals(testItemId, that.testItemId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(hspOrgId, testItemId);
    }
}
