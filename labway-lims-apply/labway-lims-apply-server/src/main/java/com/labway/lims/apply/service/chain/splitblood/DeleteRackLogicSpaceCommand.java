
package com.labway.lims.apply.service.chain.splitblood;

import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 删除之前的逻辑试管架占用，因为分血会重新生成
 *
 * <AUTHOR>
 */
@Component
public class DeleteRackLogicSpaceCommand implements Command, Filter {

    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private RackLogicService rackLogicService;
    @DubboReference
    private SnowflakeService snowflakeService;


    @Override
    public boolean execute(Context c) throws Exception {

        final SplitBloodContext context = SplitBloodContext.from(c);

        final RackLogicSpaceDto rackLogicSpace = context.getRackLogicSpace();

        rackLogicSpaceService.deleteByRackLogicSpaceIds(List.of(rackLogicSpace.getRackLogicSpaceId()));

        // 如果下面没有试管架占用了，那么删除逻辑试管架
        if (CollectionUtils.isEmpty(rackLogicSpaceService.selectByRackLogicId(rackLogicSpace.getRackLogicId()))) {
            rackLogicService.deleteByRackLogicId(rackLogicSpace.getRackLogicId());
        }


        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
