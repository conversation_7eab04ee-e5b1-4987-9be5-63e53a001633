package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class SampleItemVo extends ApplySampleItemVo {
    /**
     * 样本条码
     */
    private String barcode;

    /**
     * 金额
     */
    private BigDecimal price;

    /**
     * 套餐ID
     */
    private Long packageId = 0L;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 检验方法ID
     */
    private String examMethodCode;

    /**
     * 检验方法名称
     */
    private String examMethodName;

    /**
     * 报告项目
     */
    private List<ReportItemVo> reportItems;

    public Long getPackageId() {
        return packageId == null?0L:packageId;
    }
}
