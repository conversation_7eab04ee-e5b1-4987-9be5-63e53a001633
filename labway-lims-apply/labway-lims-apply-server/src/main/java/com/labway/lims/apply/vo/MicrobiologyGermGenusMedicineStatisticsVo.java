package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 细菌分类耐药统计
 */
@Getter
@Setter
public class MicrobiologyGermGenusMedicineStatisticsVo {

    /**
     * 细菌菌属ID
     */
    private Long germGenusId;

    /**
     * 细菌菌属名称
     */
    private String germGenusName;

    /**
     * 抗生素 key:medicineId
     */
    private Map<Long, Medicine> medicines;

    /**
     * 抗生素
     */
    @Getter
    @Setter
    public static class Medicine {
        /**
         * 抗生素ID
         */
        private Long medicineId;

        /**
         * 抗生素名称
         */
        private String medicineName;

        /**
         * 总检测数
         */
        private Integer totalTestCount;


        /**
         * 耐药数
         */
        private Integer susceptibilityCount;


        /**
         * 耐药率
         */
        private Double susceptibilityRate;


        /**
         * 敏感数
         */
        private Integer sensitiveCount;


        /**
         * 敏感率
         */
        private Double sensitiveRate;


        /**
         * 中敏数
         */
        private Integer mediumSensitiveCount;

        /**
         * 中敏率
         */
        private Double mediumSensitiveRate;

        /**
         * 阳性
         */
        private Integer positiveCount;


    }
}
