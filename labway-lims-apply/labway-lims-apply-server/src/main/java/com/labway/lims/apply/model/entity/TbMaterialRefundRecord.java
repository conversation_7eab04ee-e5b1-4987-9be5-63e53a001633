package com.labway.lims.apply.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 物流退库记录表(TbMaterialRefundRecord)表实体类
 *
 * <AUTHOR>
 * @since 2024-03-04 15:36:34
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName(value = "tb_material_refund_record")
public class TbMaterialRefundRecord extends Model<TbMaterialRefundRecord> implements Serializable{

    private static final long serialVersionUID = 1L;

    //退库id
    @TableId(type = IdType.ASSIGN_ID)
    private Long refundId;
    //退库单号
    private String refundNo;
    //入库单号
    private String incomeNo;
    //出库单号
    private String deliveryNo;
    //物料出库详情id
    private Long deliveryDetailId;
    //物料ID
    private Long materialId;
    //物资编号
    private String materialCode;
    //物资名称
    private String materialName;
    //规格
    private String specification;
    //批号
    private String batchNo;
    //厂家
    private String manufacturers;
    //主单位
    private String mainUnit;
    //出库主单位数量
    private BigDecimal deliveryMainNumber;
    //入库主单位数量
    private BigDecimal incomeMainNumber;
    //辅单位
    private String assistUnit;
    //出库辅单位数量
    private BigDecimal deliveryAssistNumber;
    //入库辅单位数量
    private BigDecimal incomeAssistNumber;
    //主辅单位换算率
    private String unitConversionRate;
    //有效期
    private Date validDate;
    //专业组ID
    private Long groupId;
    //专业组名称
    private String groupName;
    //检验机构
    private Long orgId;
    //检验机构名称
    private String orgName;
    //创建时间
    private Date createDate;
    //创建人ID
    private Long creatorId;
    //创建人名称
    private String creatorName;
    //更新时间
    private Date updateDate;
    //更新人ID
    private Long updaterId;
    //更新人名称
    private String updaterName;
    //是否删除 0否1是
    @TableLogic(value = "0", delval = "1")
    private Integer isDelete;
    //退库单类型 1退库 2拒收
    private Integer refundType;
    // 退库/拒收原因
    private String refundReason;
    // 退库/拒收主数量
    private BigDecimal refundMainNumber;
    // 退库/拒收辅数量
    private BigDecimal refundAssistNumber;
    // 发送业务中台标识 0未发送 1已发送
    private Integer pushFlag;

}

