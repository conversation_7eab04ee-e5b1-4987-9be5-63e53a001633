package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.service.RackLogicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;


@Slf4j
@Component
class MultiTwoPickCheckParamCommand implements Command {


    @Resource
    private RackLogicService rackLogicService;


    @Override
    public boolean execute(Context c) throws Exception {
        final MultiTwoPickContext context = MultiTwoPickContext.from(c);

        final RackLogicDto rackLogic = rackLogicService.selectByRackLogicId(context.getRackLogicId());
        if (Objects.isNull(rackLogic)) {
            throw new IllegalArgumentException("逻辑试管架不存在");
        }

        // 如果不是二次分拣中 那么不能分拣
        if (!Objects.equals(rackLogic.getPosition(), RackLogicPositionEnum.TWO_PICKING.getCode())) {
            throw new IllegalStateException("当前试管架无法分拣或已经分拣");
        }

        context.put(MultiTwoPickContext.RACK_LOGIC, rackLogic);

        return CONTINUE_PROCESSING;
    }

}
