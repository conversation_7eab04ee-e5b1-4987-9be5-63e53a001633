package com.labway.lims.apply.service.chain.pick.two;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TwoPickCheckParamCommand implements Command {
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplyService applyService;
    @Resource
    private TwoPickCommand twoPickCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final TwoPickContext context = TwoPickContext.from(c);
        final long applySampleId = context.getTwoPick().getApplySampleId();

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalArgumentException("申请单样本不存在");
        }

        if (Objects.equals(applySample.getIsTwoPick(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalArgumentException("该条码已完成二次分拣");
        }

        if (StringUtils.isNotBlank(context.getTwoPick().getSampleNo())) {
            // 血培养检验的 “标记阳性” 一定会重复，这里跳过检验
            if (!(context.getTwoPick() instanceof PositiveMicrobiologyTwoPickDto)) {
                if (!twoPickCommand.canActiveSampleNo(applySample.getGroupId(), context.getTwoPick().getSampleNo(),
                        context.getTwoPickDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())) {
                    throw new IllegalArgumentException(String.format("样本号 [%s] 已存在", context.getTwoPick().getSampleNo()));
                }
            }
        }

        if (StringUtils.isNotBlank(context.getTwoPick().getUrgentSampleNo())) {

            if (StringUtils.isBlank(context.getTwoPick().getSampleNo())) {
                throw new IllegalArgumentException("缺少 sampleNo");
            }

            if (!twoPickCommand.canActiveSampleNo(applySample.getGroupId(), context.getTwoPick().getUrgentSampleNo(),
                    context.getTwoPickDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())) {
                throw new IllegalArgumentException(
                        String.format("样本号 [%s] 已存在", context.getTwoPick().getUrgentSampleNo()));
            }
        }

        // 样本号重复判断
        if (StringUtils.isNoneBlank(context.getTwoPick().getSampleNo(), context.getTwoPick().getUrgentSampleNo())
                && Objects.equals(context.getTwoPick().getSampleNo(), context.getTwoPick().getUrgentSampleNo())) {
            throw new IllegalArgumentException(String.format("样本号 [%s] 重复", context.getTwoPick().getSampleNo()));
        }

        // 微生物 二次分拣
        if (context.getTwoPick() instanceof BloodCultureTwoPickDto) {
            final BloodCultureTwoPickDto twoPick = (BloodCultureTwoPickDto) context.getTwoPick();
            if (CollectionUtils.isNotEmpty(twoPick.getLimbSampleNos())) {
                if (!Objects.equals(twoPick.getLimbSampleNos().stream().map(LimbSampleDto::getSampleNo)
                        .collect(Collectors.toSet()).size(), twoPick.getLimbSampleNos().size())) {
                    throw new IllegalArgumentException("存在重复样本号");
                }
                twoPick.getLimbSampleNos().forEach(item -> {
                    if (!twoPickCommand.canActiveSampleNo(applySample.getGroupId(), item.getSampleNo(),
                            context.getTwoPickDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())) {
                        throw new IllegalArgumentException(String.format("样本号 [%s] 已存在", item.getSampleNo()));
                    }
                });
            }

        }

        // 免疫二次分拣标记
        if (context.getTwoPick() instanceof ImmunityTwoPickDto) {
            applySample.setIsImmunityTwoPick(YesOrNoEnum.YES.getCode());
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalArgumentException("申请单不存在");
        }

        Date twoPickDate = null;
        if (context.getTwoPick() instanceof MicrobiologyTwoPickDto) {
            // 微生物二次分拣
            twoPickDate = ((MicrobiologyTwoPickDto) context.getTwoPick()).getTwoPickDate();
        } else if (context.getTwoPick() instanceof InfectionTwoPickDto) {
            // 院感二次分拣
            twoPickDate = ((InfectionTwoPickDto) context.getTwoPick()).getTwoPickDate();
        }
        if (Objects.nonNull(twoPickDate) && twoPickDate.before(apply.getSamplingDate())) {
            throw new IllegalArgumentException("该条码分拣日期小于采集时间，请重新选择分拣日期");
        }

        context.put(TwoPickContext.APPLY_SAMPLE, applySample);
        context.put(TwoPickContext.APPLY, apply);

        return CONTINUE_PROCESSING;
    }

}
