package com.labway.lims.apply.service.chain.pick.two;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.BloodCultureTwoPickDto;
import com.labway.lims.apply.api.dto.ImmunityTwoPickDto;
import com.labway.lims.apply.api.dto.InfectionTwoPickDto;
import com.labway.lims.apply.api.dto.LimbSampleDto;
import com.labway.lims.apply.api.dto.MicAndBloodTwoPickDto;
import com.labway.lims.apply.api.dto.PositiveMicrobiologyTwoPickDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TwoPickCheckParamCommand implements Command {
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplyService applyService;
    @Resource
    private TwoPickCommand twoPickCommand;
    @Resource
    private ApplySampleItemService applySampleItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final TwoPickContext context = TwoPickContext.from(c);
        final long applySampleId = context.getTwoPick().getApplySampleId();

        // 申请单样本
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalArgumentException("申请单样本不存在");
        }

        // 校验样本是否已二次分拣
        if (Objects.equals(applySample.getIsTwoPick(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalArgumentException("该条码已完成二次分拣");
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalArgumentException("申请单不存在");
        }

        // 获取到检验项目
        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(context.getTwoPick().getApplySampleId());
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("申请单样本下没有检验项目");
        }


        // 样本号不为空的话 校验样本号是否可以使用
        if (StringUtils.isNotBlank(context.getTwoPick().getSampleNo())) {
            // 血培养检验的 “标记阳性” 一定会重复，这里跳过检验
            if (!(context.getTwoPick() instanceof PositiveMicrobiologyTwoPickDto)) {
                if (!twoPickCommand.canActiveSampleNo(applySample.getGroupId(), context.getTwoPick().getSampleNo(),
                        context.getTwoPickDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())) {
                    throw new IllegalArgumentException(String.format("样本号 [%s] 已存在", context.getTwoPick().getSampleNo()));
                }
            }
        }

        // 加急样本号不为空 （单个分拣 加急样本）
        if (StringUtils.isNotBlank(context.getTwoPick().getUrgentSampleNo())) {

            if (StringUtils.isBlank(context.getTwoPick().getSampleNo())) {
                throw new IllegalArgumentException("缺少 sampleNo");
            }

            // 校验样本号是否可用
            if (!twoPickCommand.canActiveSampleNo(applySample.getGroupId(), context.getTwoPick().getUrgentSampleNo(),
                    context.getTwoPickDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())) {
                throw new IllegalArgumentException(
                        String.format("样本号 [%s] 已存在", context.getTwoPick().getUrgentSampleNo()));
            }
        }

        // 样本号重复判断 二次分拣样本号和加急样本号是否一致， 一致则异常
        if (StringUtils.isNoneBlank(context.getTwoPick().getSampleNo(), context.getTwoPick().getUrgentSampleNo())
                && Objects.equals(context.getTwoPick().getSampleNo(), context.getTwoPick().getUrgentSampleNo())) {
            throw new IllegalArgumentException(String.format("样本号 [%s] 重复", context.getTwoPick().getSampleNo()));
        }

        final Set<String> bloodCultureTestItemCodes = context.getBloodCultureTestItemCodes();
        // 血培养二次分拣 血培养类型  || 项目是血培养的项目
        if (context.getTwoPick() instanceof BloodCultureTwoPickDto ||
                applySampleItems.stream().anyMatch(e-> bloodCultureTestItemCodes.contains(e.getTestItemCode()))) {

            if (CollectionUtils.size(applySampleItems) != NumberUtils.INTEGER_ONE) {
                throw new IllegalArgumentException("该样本下存在多个血培养项目或与其他项目共同存在");
            }

            final MicAndBloodTwoPickDto twoPick = (MicAndBloodTwoPickDto) context.getTwoPick();
            /*
              血培养二次分拣会报异常， 然后填写样本号， 这里就不为null了
              @see TwoPickBloodCultureCommand.NO_LIMB_SAMPLE_NO_ERROR
             */
            if (CollectionUtils.isNotEmpty(twoPick.getLimbSampleNos())) {
                // 血培养指定样本号 查看填写的样本号是否重复
                if (!Objects.equals(twoPick.getLimbSampleNos().stream().map(LimbSampleDto::getSampleNo) .collect(Collectors.toSet()).size(),
                        twoPick.getLimbSampleNos().size())) {
                    throw new IllegalArgumentException("存在重复样本号");
                }
                // 查看填写的样本号是否已被使用
                twoPick.getLimbSampleNos().forEach(item -> {
                    if (!twoPickCommand.canActiveSampleNo(applySample.getGroupId(), item.getSampleNo(),
                            context.getTwoPickDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())) {
                        throw new IllegalArgumentException(String.format("样本号 [%s] 已存在", item.getSampleNo()));
                    }
                });
            }

        }

        // 免疫二次分拣标记
        if (context.getTwoPick() instanceof ImmunityTwoPickDto) {
            applySample.setIsImmunityTwoPick(YesOrNoEnum.YES.getCode());
        }

        Date twoPickDate = null;
        if (context.getTwoPick() instanceof MicAndBloodTwoPickDto) {
            // 微生物和血培养二次分拣
            twoPickDate = ((MicAndBloodTwoPickDto) context.getTwoPick()).getTwoPickDate();
        } else if (context.getTwoPick() instanceof InfectionTwoPickDto) {
            // 院感二次分拣
            twoPickDate = ((InfectionTwoPickDto) context.getTwoPick()).getTwoPickDate();
        }
        // 如果指定分拣日期在采集时间之前则不允许分拣  （分拣 检验 时间不可能比采样时间还早）
        if (Objects.nonNull(twoPickDate) && twoPickDate.before(apply.getSamplingDate())) {
            throw new IllegalArgumentException("该条码分拣日期小于采集时间，请重新选择分拣日期");
        }

        context.put(TwoPickContext.APPLY_SAMPLE_ITEMS, applySampleItems);
        context.put(TwoPickContext.APPLY_SAMPLE, applySample);
        context.put(TwoPickContext.APPLY, apply);

        return CONTINUE_PROCESSING;
    }

}
