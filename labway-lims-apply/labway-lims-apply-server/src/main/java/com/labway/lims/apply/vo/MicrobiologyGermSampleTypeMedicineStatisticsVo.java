package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 细菌分类耐药统计
 */
@Getter
@Setter
public class MicrobiologyGermSampleTypeMedicineStatisticsVo {

    /**
     * 细菌菌属ID
     */
    private Long germGenusId;

    /**
     * 细菌菌属名称
     */
    private String germGenusName;

    /**
     * 细菌 key:germCode
     */
    private Map<String, Germ> germs;


    /**
     * 细菌
     */
    @Getter
    @Setter
    public static class Germ {
        /**
         * 细菌ID
         */
        private Long germId;

        /**
         * 细菌名称
         */
        private String germName;

        /**
         * 检验项目
         * key:testItemId
         */
        private Map<Long, TestItem> testItems;
    }


    /**
     * 检验项目
     */
    @Getter
    @Setter
    public static class TestItem {


        /**
         * 检验项目ID
         */
        private Long testItemId;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 细菌株数
         */
        private Integer germCount;

        /**
         * 样本类型:count
         */
        private Map<String, Integer> sampleTypes;

        /**
         * 细菌百分率
         */
        private Double germRate;

        /**
         * 细菌株数
         */
        private Integer count;

        /**
         * 总百分率
         */
        private Double totalGermRate;

    }
}
