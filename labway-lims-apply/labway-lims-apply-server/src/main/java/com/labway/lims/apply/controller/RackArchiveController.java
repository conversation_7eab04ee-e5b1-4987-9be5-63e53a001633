package com.labway.lims.apply.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.base.RackTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ExtractArchiveSampleContentDto;
import com.labway.lims.apply.api.dto.RackArchiveDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RackArchiveService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.service.chain.sample.archive.add.SampleArchiveAddChain;
import com.labway.lims.apply.service.chain.sample.archive.add.SampleArchiveAddContext;
import com.labway.lims.apply.vo.ArchiveSampleResponseVo;
import com.labway.lims.apply.vo.ExtractArchiveSampleRecordResponseVo;
import com.labway.lims.apply.vo.ExtractArchiveSampleRequestVo;
import com.labway.lims.apply.vo.RackArchiveAddRequestVo;
import com.labway.lims.apply.vo.RackArchiveListResponseVo;
import com.labway.lims.apply.vo.SelectRackArchiveResponseVo;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.RefrigeratorDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.RackService;
import com.labway.lims.base.api.service.RefrigeratorService;
import com.labway.lims.base.api.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.math.NumberUtils.INTEGER_ZERO;

/**
 * 试管架归档 API
 * 
 * <AUTHOR>
 * @since 2023/4/13 15:01
 */
@Slf4j
@RestController
@RequestMapping("/rack-archive")
public class RackArchiveController extends BaseController {

    @DubboReference
    private RackService rackService;
    @DubboReference
    private RackLogicService rackLogicService;
    @DubboReference
    private RackArchiveService rackArchiveService;
    @DubboReference
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private SampleArchiveAddChain sampleArchiveAddChain;
    @Resource
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplyService applyService;

    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private UserService userService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private RefrigeratorService refrigeratorService;

    /**
     * 样本归档-样本存储-试管架下拉框
     */
    @PostMapping("/rack-list")
    public Object rackArchiveList(@RequestParam("refrigeratorId") long refrigeratorId) {

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        Set<String> codes = new HashSet<>();
        codes.add(RackTypeEnum.ARCHIVE_RACK.getCode());
        codes.add(RackTypeEnum.PERMANENT_ARCHIVE_RACK.getCode());
        // 所有 物理试管架
        final List<RackDto> rackDtos =
            rackService.selectByRackTypeCodes(codes,
                    loginUser.getOrgId(), loginUser.getGroupCode());

        final List<Long> rackIdList = rackDtos.stream().map(RackDto::getRackId).collect(Collectors.toList());

        // 对应 归档试管架信息
        final List<RackArchiveDto> rackArchiveDtos = rackArchiveService.selectByRackIds(rackIdList);

        // 正常 一个物理试管架只有一条对应归档信息
        final Map<Long, List<RackArchiveDto>> groupingByRackId =
            rackArchiveDtos.stream().collect(Collectors.groupingBy(RackArchiveDto::getRackId));

        if (groupingByRackId.entrySet().stream().anyMatch(obj -> obj.getValue().size() > 1)) {
            throw new LimsException("存在归档类型试管架存在多条归档信息");
        }

        List<RackArchiveListResponseVo> targetList = Lists.newArrayList();
        rackDtos.forEach(item -> {
            RackArchiveListResponseVo temp = JSON.parseObject(JSON.toJSONString(item), RackArchiveListResponseVo.class);
            List<RackArchiveDto> archiveDtoList = groupingByRackId.get(temp.getRackId());
            if (CollectionUtils.isNotEmpty(archiveDtoList)
                && !Objects.equals(archiveDtoList.get(0).getRefrigeratorId(), refrigeratorId)) {
                // 存在归档试管架但归档试管架 冰箱非选择冰箱
                return;

            }
            if (CollectionUtils.isNotEmpty(archiveDtoList)) {
                temp.setStartEffectiveDate(archiveDtoList.get(0).getStartEffectiveDate());
                temp.setEndEffectiveDate(archiveDtoList.get(0).getEndEffectiveDate());
            }
            targetList.add(temp);
        });

        return targetList;
    }

    /**
     * 样本归档-存储
     */
    @PostMapping("/add")
    public Object rackArchiveAdd(@RequestBody RackArchiveAddRequestVo vo) {

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final SampleArchiveAddContext context = new SampleArchiveAddContext();
        context.setRackArchiveAddRequestVo(vo);
        context.setUser(loginUser);

        try {
            sampleArchiveAddChain.execute(context);
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException)e;
            }
            log.error("样本归档-存储 失败", e);

            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("样本归档-存储耗时\n{}", context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

        return Collections.emptyMap();
    }

    /**
     * 样本归档-获取试管架上已归档样本信息
     */
    @PostMapping("/current-archive-sample")
    public Object currentArchiveSample(@RequestParam("rackId") long rackId) {

        final RackArchiveDto rackArchiveDto = rackArchiveService.selectByRackId(rackId);
        if (Objects.isNull(rackArchiveDto)) {
            throw new LimsException("当前试管架无样本信息");
        }
        final List<RackLogicSpaceDto> rackLogicSpaceDtos =
            rackLogicSpaceService.selectByRackLogicId(rackArchiveDto.getRackLogicId());

        // 归档试管架上 所有申请单样本id
        final Set<Long> applySampleIds =
            rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getApplySampleId).collect(Collectors.toSet());
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);
        Map<Long, ApplySampleDto> applySampleByApplySampleId =
            applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));

        // 对应申请单信息
        Set<Long> applyIds = applySampleDtos.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet());
        final List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);
        Map<Long, ApplyDto> applyByApplyId =
            applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        List<ArchiveSampleResponseVo> targetList = Lists.newArrayListWithCapacity(rackLogicSpaceDtos.size());
        rackLogicSpaceDtos.forEach(item -> {
            ArchiveSampleResponseVo temp = JSON.parseObject(JSON.toJSONString(item), ArchiveSampleResponseVo.class);
            // 展示 行列+1
            temp.setRow(item.getRow());
            temp.setColumn(item.getColumn());
            temp.setPositionDesc((temp.getRow() + 1) + "-" + (temp.getColumn() + 1));

            ApplySampleDto applySampleDto = applySampleByApplySampleId.get(temp.getApplySampleId());
            if (Objects.nonNull(applySampleDto)) {
                temp.setBarcode(applySampleDto.getBarcode());
                temp.setGroupId(applySampleDto.getGroupId());
                temp.setGroupName(applySampleDto.getGroupName());

                ApplyDto applyDto = applyByApplyId.get(applySampleDto.getApplyId());
                if (Objects.nonNull(applyDto)) {
                    temp.setPatientName(applyDto.getPatientName());
                    temp.setPatientSex(applyDto.getPatientSex());
                    temp.setPatientAge(applyDto.getPatientAge());
                }
            }
            targetList.add(temp);
        });
        return targetList.stream()
            .sorted(
                Comparator.comparing(ArchiveSampleResponseVo::getColumn).thenComparing(ArchiveSampleResponseVo::getRow))
            .collect(Collectors.toList());
    }

    /**
     * 样本归档 查看冰箱对应归档试管架信息
     */
    @PostMapping("/select-by-refrigerator-id")
    public Object selectByRefrigeratorId(@RequestParam("refrigeratorId") long refrigeratorId) {
        List<RackArchiveDto> archiveDtoList = rackArchiveService.selectByRefrigeratorId(refrigeratorId);
        if (CollectionUtils.isEmpty(archiveDtoList)) {
            return Collections.emptyList();
        }
        // 对应试管架信息
        Set<Long> rackIds = archiveDtoList.stream().map(RackArchiveDto::getRackId).collect(Collectors.toSet());
        List<RackDto> rackDtos = rackService.selectByRackIds(rackIds);
        Map<Long, RackDto> rackByRackId =
            rackDtos.stream().collect(Collectors.toMap(RackDto::getRackId, Function.identity()));

        // 对应逻辑试管架 占用数量
        Set<Long> rackLogicIds =
            archiveDtoList.stream().map(RackArchiveDto::getRackLogicId).collect(Collectors.toSet());
        Map<Long, Integer> occupiedNumByRackLogicId =
            rackLogicSpaceService.selectOccupiedNumByRackLogicIds(rackLogicIds);

        List<SelectRackArchiveResponseVo> target = Lists.newArrayListWithCapacity(archiveDtoList.size());

        archiveDtoList.forEach(item -> {
            SelectRackArchiveResponseVo temp =
                JSON.parseObject(JSON.toJSONString(item), SelectRackArchiveResponseVo.class);

            RackDto rackDto = rackByRackId.get(item.getRackId());
            if (Objects.nonNull(rackDto)) {
                temp.setRackCode(rackDto.getRackCode());
                temp.setRackTypeCode(rackDto.getRackTypeCode());
                temp.setRackTypeName(rackDto.getRackTypeName());
                temp.setRackName(rackDto.getRackName());
                int row = rackDto.getRow();
                int column = rackDto.getColumn();
                temp.setRow(row);
                temp.setColumn(column);
                temp.setAvailableNum(row * column);
                temp.setSpecDesc(row + "*" + column);
            }

            int occupiedNum = 0;
            if (Objects.nonNull(occupiedNumByRackLogicId.get(item.getRackLogicId()))) {
                occupiedNum = occupiedNumByRackLogicId.get(item.getRackLogicId());
            }
            temp.setHavingNum(occupiedNum);

            target.add(temp);
        });

        Date now = new Date();

        // 未到期 样本 有效
        final List<SelectRackArchiveResponseVo> effectiveList =
            target.stream().filter(obj -> obj.getEndEffectiveDate().after(now))
                .sorted(
                    Comparator.comparing(SelectRackArchiveResponseVo::getEndEffectiveDate, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        // 已到期 样本 无效
        List<SelectRackArchiveResponseVo> invalidList =
            target.stream().filter(obj -> obj.getEndEffectiveDate().before(now))
                .sorted(
                    Comparator.comparing(SelectRackArchiveResponseVo::getEndEffectiveDate, Comparator.reverseOrder()))
                .collect(Collectors.toList());

        return Map.of("effectiveList", effectiveList, "invalidList", invalidList);
    }

    /**
     * 样本归档-存储-移除归档样本
     */
    @PostMapping("/remove-archive-sample")
    public Object removeArchiveSample(@RequestBody Set<Long> rackLogicSpaceIds) {
        if (CollectionUtils.isEmpty(rackLogicSpaceIds)) {
            return Collections.emptyMap();
        }
        // 对应归档样本空间占用
        final List<RackLogicSpaceDto> rackLogicSpaceDtos =
            rackLogicSpaceService.selectByRackLogicSpaceIds(rackLogicSpaceIds);
        Set<Long> rackLogicSpaceIdsNow =
            rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getRackLogicSpaceId).collect(Collectors.toSet());
        if (rackLogicSpaceIds.stream().anyMatch(x -> !rackLogicSpaceIdsNow.contains(x))) {
            throw new LimsException("存在无效归档样本");
        }

        // 对应归档逻辑试管架
        Set<Long> rackLogicIds =
            rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getRackLogicId).collect(Collectors.toSet());
        if (rackLogicIds.size() > 1) {
            throw new LimsException("所选样本不在同一个试管架上");
        }
        RackDto rackDto = rackService.selectByRackId(rackLogicSpaceDtos.get(0).getRackId());
        if (Objects.isNull(rackDto)) {
            throw new LimsException("所选样本对应试管架不存在");
        }
        if (!RackTypeEnum.isArchiveRack(rackDto.getRackTypeCode())) {
            throw new LimsException("所选样本对应试管架类型不为归档试管架");
        }

        // 对应归档架
        RackArchiveDto rackArchiveDto = rackArchiveService.selectByRackId(rackDto.getRackId());
        if (Objects.isNull(rackArchiveDto)) {
            throw new LimsException("对应归档试管架不存在");
        }
        // 对应冰箱
        RefrigeratorDto refrigeratorDto =
            refrigeratorService.selectByRefrigeratorId(rackArchiveDto.getRefrigeratorId());
        if (Objects.isNull(refrigeratorDto)) {
            throw new LimsException("对应冰箱不存在");
        }
        // 移除 归档试管架上样本
        rackArchiveService.removeArchiveSample(rackLogicSpaceDtos, rackDto, refrigeratorDto);

        // 当 试管架 上无任何归档样本 时需要 清理 数据
        boolean clearFlag = rackArchiveService.clearRackWhenArchiveSampleIsEmpty(rackDto.getRackId(),
            rackLogicSpaceDtos.get(INTEGER_ZERO).getRackLogicId(), false);

        return Map.of("destroyRackFlag", clearFlag);
    }

    /**
     * 样本归档 提取样本
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/extract-archive-sample")
    public Object extractArchiveSample(@RequestBody ExtractArchiveSampleRequestVo vo) {
        if (StringUtils.isAnyBlank(vo.getExtractDesc(), vo.getUsername(), vo.getPassword())
            || CollectionUtils.isEmpty(vo.getRackLogicSpaceIds())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        List<RackLogicSpaceDto> rackLogicSpaceDtos =
            rackLogicSpaceService.selectByRackLogicSpaceIds(vo.getRackLogicSpaceIds());
        List<Long> rackLogicSpaceIdNow =
            rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getRackLogicSpaceId).collect(Collectors.toList());
        if (vo.getRackLogicSpaceIds().stream().anyMatch(item -> !rackLogicSpaceIdNow.contains(item))) {
            throw new LimsException("存在无效归档样本");
        }

        final UserDto user = userService.selectByUsername(vo.getUsername());
        if (Objects.isNull(user)) {
            throw new IllegalArgumentException("工号或密码错误");
        }
        if (!userService.validPassword(user.getUsername(), user.getPassword(), vo.getPassword())) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        Map<String, List<RackLogicSpaceDto>> rackMap = rackLogicSpaceDtos.stream()
                .collect(Collectors.groupingBy(e -> e.getRackId() + "_" + e.getRackLogicId()));

        // 前端是否重新调取数据标记
        final AtomicBoolean destroyRackFlag = new AtomicBoolean(false);

        rackMap.forEach((k, v) -> {
            String[] rackIdAndRackLogicId = k.split("_");
            long rackId = Long.parseLong(rackIdAndRackLogicId[0]);
            long rackLogicId = Long.parseLong(rackIdAndRackLogicId[1]);
            RackDto rackDto = rackService.selectByRackId(rackId);
            if (Objects.isNull(rackDto)) {
                throw new LimsException("所选样本对应试管架不存在");
            }
            if (!RackTypeEnum.isArchiveRack(rackDto.getRackTypeCode())) {
                throw new LimsException("所选样本对应试管架类型不为归档试管架");
            }

            // 对应归档架
            RackArchiveDto rackArchiveDto = rackArchiveService.selectByRackId(rackId);
            if (Objects.isNull(rackArchiveDto)) {
                throw new LimsException("对应归档试管架不存在");
            }

            // 对应冰箱
            RefrigeratorDto refrigeratorDto =
                    refrigeratorService.selectByRefrigeratorId(rackArchiveDto.getRefrigeratorId());
            if (Objects.isNull(refrigeratorDto)) {
                throw new LimsException("对应冰箱不存在");
            }

            // 提取 归档试管架上样本
            rackArchiveService.extractArchiveSample(v, rackDto, refrigeratorDto, user,
                    vo.getExtractDesc());
            // 当 试管架 上无任何归档样本 时需要 清理 数据
            boolean clearFlag = rackArchiveService.clearRackWhenArchiveSampleIsEmpty(rackId, rackLogicId, false);

            destroyRackFlag.set(destroyRackFlag.get() ? destroyRackFlag.get() : clearFlag);

        });

        return Map.of("destroyRackFlag", destroyRackFlag);

    }

    /**
     * 样本归档 销毁样本
     */
    @PostMapping("/destroy-archive-sample")
    public Object destroyArchiveSample(@RequestBody Set<Long> rackLogicSpaceIds) {
        return Map.of("destroyRackFlag", rackArchiveService.destroyArchiveSample(rackLogicSpaceIds));
    }

    /**
     * 样本归档 样本提取记录
     */
    @PostMapping("/extract-archive-sample-record")
    public Object extractArchiveSampleRecord(@RequestParam("rackId") long rackId) {
        final RackArchiveDto rackArchiveDto = rackArchiveService.selectByRackId(rackId);
        if (Objects.isNull(rackArchiveDto)) {
            throw new LimsException("归档试管架不存在");
        }
        final List<RackLogicSpaceDto> rackLogicSpaceDtos =
            rackLogicSpaceService.selectByRackLogicId(rackArchiveDto.getRackLogicId());

        Set<Long> applySampleIdList =
            rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getApplySampleId).collect(Collectors.toSet());

        // 对应样本流水记录
        final List<SampleFlowDto> sampleFlowDtoList = sampleFlowService
            .selectByApplySampleIdAndOperateCode(applySampleIdList, BarcodeFlowEnum.EXTRACT_ARCHIVE_SAMPLE);

        List<ExtractArchiveSampleRecordResponseVo> targetList = Lists.newArrayList();
        sampleFlowDtoList.forEach(item -> {
            ExtractArchiveSampleContentDto contentDto =
                ExtractArchiveSampleContentDto.getByContentStr(item.getContent());
            if (Objects.isNull(contentDto)) {
                return;
            }
            String positionDesc = contentDto.getRow() + "-" + contentDto.getColumn();
            ExtractArchiveSampleRecordResponseVo target = new ExtractArchiveSampleRecordResponseVo();
            target.setPositionDesc(positionDesc);
            target.setBarcode(item.getBarcode());
            target.setGroupName(contentDto.getGroupName());
            target.setExtractDesc(contentDto.getExtractDesc());
            target.setUsername(contentDto.getNickname());
            target.setExtractDate(item.getCreateDate());

            targetList.add(target);
        });

        return targetList.stream().sorted(Comparator.comparing(ExtractArchiveSampleRecordResponseVo::getExtractDate))
            .collect(Collectors.toList());
    }

}
