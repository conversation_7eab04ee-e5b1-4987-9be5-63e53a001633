package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.apply.api.dto.BusinessCenterMaterialBarcodeSearchDto;
import com.labway.lims.apply.model.TbMaterialIncomeRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 物料入库记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Mapper
public interface TbMaterialIncomeRecordMapper extends BaseMapper<TbMaterialIncomeRecord> {

    /**
     * 批量 插入
     */
    void batchAddMaterialIncomeRecords(@Param("conditions") List<TbMaterialIncomeRecord> conditions);

	/**
	 * 根据物料编码和批次号 查询对应的条码号
	 */
	default List<TbMaterialIncomeRecord> searchMaterialBarcodeByMaterialCodesAndBatchNos(Set<String> materialCodes, Collection<String> batchNos) {
		return selectList(Wrappers.lambdaQuery(TbMaterialIncomeRecord.class).in(TbMaterialIncomeRecord::getMaterialCode, materialCodes)
				.in(TbMaterialIncomeRecord::getBatchNo, batchNos));
	}


}
