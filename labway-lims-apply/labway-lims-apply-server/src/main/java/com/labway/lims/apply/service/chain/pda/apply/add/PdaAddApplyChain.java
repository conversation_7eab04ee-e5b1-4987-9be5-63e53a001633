package com.labway.lims.apply.service.chain.pda.apply.add;

import com.labway.lims.apply.service.chain.apply.add.AddDiagnosisCommand;
import com.labway.lims.apply.service.chain.apply.add.AddHspOrgDeptOrDoctorCommand;
import com.labway.lims.apply.service.chain.apply.add.CheckParamCommand;
import com.labway.lims.apply.service.chain.apply.add.CreateApplyCommand;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class PdaAddApplyChain  extends ChainBase implements InitializingBean {
    @Resource
    private CheckParamCommand checkParamCommand;

    @Resource
    private CreateApplyCommand createApplyCommand;

    @Resource
    private CheckMasterCommand checkMasterCommand;

    @Resource
    private CreateApplySampleItemCommand createApplySampleItemCommand;

    @Resource
    private PdaAddPgsqlCommand pdaAddPgsqlCommand;

    @Resource
    private PdaAddPostCommand pdaAddPostCommand;
    @Resource
    private PdaAbolishCommand pdaAbolishCommand;
    @Resource
    private CheckSupplementalRecordFieldCommand checkSupplementalRecordFieldCommand;
    @Resource
    private PdaCheckTestItemLimitSexCommand pdaCheckTestItemLimitSexCommand;

    @Resource
    private AddHspOrgDeptOrDoctorCommand addHspOrgDeptOrDoctorCommand;

    @Resource
    private AddDiagnosisCommand addDiagnosisCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 检查参数
        addCommand(checkParamCommand);

        // 检查参数
        addCommand(checkMasterCommand);

        // 创建申请单
        addCommand(createApplyCommand);

        // 判断是否作废申请单
        addCommand(pdaAbolishCommand);

        // 创建申请单项目
        addCommand(createApplySampleItemCommand);

        // 获取规则， 是否自动确认
        addCommand(checkSupplementalRecordFieldCommand);

        // 检验项目限制性别
        addCommand(pdaCheckTestItemLimitSexCommand);

        //新增送检机构部门或医生基础数据
        addCommand(addHspOrgDeptOrDoctorCommand);

        // 新增临床诊断
        addCommand(addDiagnosisCommand);

        // 保存pda申请单
        addCommand(pdaAddPgsqlCommand);

        // 后置处理组装返回参数
        addCommand(pdaAddPostCommand);

        addCommand(e -> PROCESSING_COMPLETE);
    }
}
