package com.labway.lims.apply.controller.rocheIT3000;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.vo.IT3000HandleVo;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 获取样本信息
 */
@Slf4j
@Component
class RocheIT3000ApplySampleInfoAction implements ActionStrategy {
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private ApplyService applyService;
    @Resource
    private ReportItemService reportItemService;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private InstrumentGroupInstrumentService instrumentGroupInstrumentService;
    @DubboReference
    private InstrumentGroupTestItemService instrumentGroupTestItemService;
    @DubboReference
    private  InstrumentReportItemService instrumentReportItemService;





    /**
     * 获取样本信息
     * 通道号获取逻辑
     * 先根据检验项目分配到的专业组
     * 根据专业小组优先级查询到包含检验项目的专业小组
     * 根据包含检验项目的仪器器查询到通道号-按照跟新时间排序吧（没有其他的排序字段了）
     * 根据专业组合仪器查询仪器报告项目--获取报告项目通道号
     * 以上获取的报告项目通道号必须在同一个专业小组下面（或者同一台仪器下面，先预留）
     * @param vo
     * @return
     * @throws Exception
     */
    @Override
    public Object action(IT3000HandleVo vo) throws Exception {
        final long applySampleId = vo.getExtras().getLongValue("applySampleId");
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalArgumentException("申请单样本不存在");
        }

        // 申请单
        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalArgumentException("申请单不存在");
        }


        // 检验项目
        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(applySampleId);
        // 检验项目id
        Set<Long> allApplySampleItemIds = applySampleItems.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());
        // 检验项目所在的所有专业组
        Set<Long> allGroupIds = applySampleItems.stream().map(ApplySampleItemDto::getGroupId).collect(Collectors.toSet());

        // 检验项目包含的所有报告项目
        List<ReportItemDto> reportItemDtos = reportItemService.selectByTestItemIds(allApplySampleItemIds);


        // 查询送检机构信息
        final HspOrganizationDto organization = hspOrganizationService.selectByHspOrgId(apply.getHspOrgId());
        if (Objects.isNull(organization)) {
            throw new IllegalStateException("送检机构不存在");
        }
        // 判断是否需要分血，如果需要分血，那么需要判断送检机构是否支持分血
        if (allGroupIds.size() > 1 && Objects.equals(organization.getEnableSplitBlood(), YesOrNoEnum.NO.getCode())){
            throw new IllegalStateException("样本需要分血，送检机构不支持分血,无法上机分拣！");
        }


        // 专业组信息 -- 并且过滤掉未启用的
        final List<ProfessionalGroupDto> professionalGroupDtos = groupService.selectByGroupIds(allGroupIds)
                .stream().filter(item -> Objects.equals(YesOrNoEnum.YES.getCode(),item.getEnable())).collect(Collectors.toList());

        // 所有专业组的专业小组信息 -- 并且过滤掉未启用的
        final List<InstrumentGroupDto> allInstrumentGroupDto = instrumentGroupService.selectByGroupIds(allGroupIds)
                .stream().filter(item -> Objects.equals(YesOrNoEnum.YES.getCode(),item.getEnable())).collect(Collectors.toList());
        final List<Long> allInstrumentGroupDtoIds = allInstrumentGroupDto.stream().map(InstrumentGroupDto::getInstrumentGroupId).collect(Collectors.toList());

        // 所有专业小组的仪器信息 -- 并且过滤掉未启用的
        final List<InstrumentGroupInstrumentDto> allInstrumentGroupInstrumentDtos = instrumentGroupInstrumentService.selectByInstrumentGroupIds(allInstrumentGroupDtoIds)
                .stream().filter(item -> Objects.equals( YesOrNoEnum.YES.getCode(),item.getEnable())).collect(Collectors.toList());
        final List<Long> allInstrumentGroupInstrumentDtoIds = allInstrumentGroupInstrumentDtos.stream().map(InstrumentGroupInstrumentDto::getInstrumentId).distinct().collect(Collectors.toList());

        // 所有专业小组的仪器检验项目信息 -- 并且过滤掉未启用的
        final List<InstrumentGroupTestItemDto> allInstrumentGroupTestItemDtos = instrumentGroupTestItemService.selectByInstrumentGroupIds(allInstrumentGroupDtoIds)
                .stream().filter(item -> Objects.equals(YesOrNoEnum.YES.getCode(),item.getEnable())).collect(Collectors.toList());

        // 所有专业小组下仪器的仪器报告项目
        final List<InstrumentReportItemDto> allInstrumentReportItemDtos = instrumentReportItemService.selectByInstrumentIds(allInstrumentGroupInstrumentDtoIds)
                .stream().filter(item -> Objects.equals(YesOrNoEnum.YES.getCode(),item.getEnable())).collect(Collectors.toList());


        List<ReportItem> reportItems = new ArrayList<>();

        // 这里要根据样本所在的专业组进行循环处理
        // 因为样本你可能会分血，那就会分到多个专业组，
        // 因为同一个专业的样本需要在同一个专业小组下进行上机分拣，所以需要根据样本所在的专业组进行循环处理
        // 如果各部检验项目处理，那么就会导致样本上机分拣到不同的专业小组

        applySampleItems.stream().collect(Collectors.groupingBy(ApplySampleItemDto::getGroupId)).forEach((groupId, applySampleItemList) -> {

            // 匹配通道号
            doReportItemChannelCode(groupId, applySampleItemList, professionalGroupDtos, reportItemDtos, allInstrumentGroupDto, allInstrumentGroupTestItemDtos, applySample, allInstrumentGroupInstrumentDtos, allInstrumentReportItemDtos, reportItems);

        });


        return Map.of(
                "apply", apply,
                "applySample", applySample,
                "applySampleItems", applySampleItems,
                "reportItems", reportItems
                );
    }

    // 处理匹配样本报告项目的通道号
    private static void doReportItemChannelCode(Long groupId, List<ApplySampleItemDto> applySampleItemList, List<ProfessionalGroupDto> professionalGroupDtos, List<ReportItemDto> reportItemDtos, List<InstrumentGroupDto> allInstrumentGroupDto, List<InstrumentGroupTestItemDto> allInstrumentGroupTestItemDtos, ApplySampleDto applySample, List<InstrumentGroupInstrumentDto> allInstrumentGroupInstrumentDtos, List<InstrumentReportItemDto> allInstrumentReportItemDtos, List<ReportItem> reportItems) {
        // 专业组信息
        ProfessionalGroupDto professionalGroupDto = professionalGroupDtos.stream().filter(e -> Objects.equals(e.getGroupId(), groupId)).findFirst().orElse(null);
        if (Objects.isNull(professionalGroupDto)) {
            log.error("专业组id/名称 [{}/{}] 不存在,无法推送到 Roche 流水线上机！！！", groupId, applySampleItemList.iterator().next().getGroupName());
            throw new IllegalStateException(String.format("专业组id/名称 [%s/%s] 不存在！", groupId, applySampleItemList.iterator().next().getGroupName()));
        }

        // 当前专业组下所有的检验项目id
        List<Long> currentAllTestItemIds = applySampleItemList.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toList());

        // 获取样本在当前专业组下的所有检验项目的报告项目
        List<ReportItemDto> currentReportItemDtos = reportItemDtos.stream().filter(e -> currentAllTestItemIds.contains(e.getTestItemId())).collect(Collectors.toList());

        // 当前专业组下所有的专业小组 -- 并根据优先级排序
        List<InstrumentGroupDto> currentAllInstrumentGroupDto = allInstrumentGroupDto.stream().filter(e -> Objects.equals(e.getGroupId(), groupId))
                .sorted(Comparator.comparingInt(InstrumentGroupDto::getSort)).collect(Collectors.toList());

        // 选择最合适的专业小组
        List<InstrumentGroupDto> matchInstrumentGroupDtos = currentAllInstrumentGroupDto.stream().filter(e -> {
            // 当前专业小组的所有检验项目
            List<InstrumentGroupTestItemDto> groupTestItemDtos = allInstrumentGroupTestItemDtos.stream().filter(p -> Objects.equals(p.getInstrumentGroupId(), e.getInstrumentGroupId())).collect(Collectors.toList());
            // 获取符合条件的专业小组 -- 专业小组必须包含所有的检验项目
            return groupTestItemDtos.stream().map(InstrumentGroupTestItemDto::getTestItemId).collect(Collectors.toSet()).containsAll(currentAllTestItemIds);
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchInstrumentGroupDtos)) {
            log.error("条码号/样本id [{}/{}] 专业组id/名称 [{}/{}] 下没有匹配到合适的专业小组（需满足单个专业小组包含全部的样本检验项目），无法推送到 Roche 流水线前处理上机分拣！！！", applySample.getBarcode(), applySample.getApplySampleId(), groupId, applySampleItemList.iterator().next().getGroupName());
            throw new IllegalStateException(String.format("条码号/样本id [%s/%s] 专业组id/名称 [%s/%s] 下没有匹配到合适的专业小组,请检查检验项目 [{%s}],是否分配到同一个专业小组下！！！",
                    applySample.getBarcode(), applySample.getApplySampleId(),
                    groupId, applySampleItemList.iterator().next().getGroupName(),
                    applySampleItemList.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.joining(","))));
        }


        AtomicBoolean isFind = new AtomicBoolean(false);

        // 最合适的专业小组
        for (InstrumentGroupDto next : matchInstrumentGroupDtos) {

            // 获取最合适专业小组下的仪器
            List<InstrumentGroupInstrumentDto> currentInstrumentGroupInstrumentDtos = allInstrumentGroupInstrumentDtos.stream().filter(e->Objects.equals(e.getInstrumentGroupId(),next.getInstrumentGroupId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(currentInstrumentGroupInstrumentDtos)){
                log.warn(" 条码/样本id [{}/{}] 在专业小组 [{}] 没有维护仪器，继续匹配下一专业小组。。。", applySample.getBarcode(), applySample.getApplySampleId(), next.getInstrumentGroupName());
                continue;
            }

            // 当前专业小组下的仪器id
            List<Long> currentInstrumentGroupInstrumentDtoIds = currentInstrumentGroupInstrumentDtos.stream().map(InstrumentGroupInstrumentDto::getInstrumentId).collect(Collectors.toList());

            // 当前专业小组下仪器的所有仪器报告项目
            List<InstrumentReportItemDto> currentInstrumentReportItemDtos = allInstrumentReportItemDtos.stream().filter(e -> currentInstrumentGroupInstrumentDtoIds.contains(e.getInstrumentId())).collect(Collectors.toList());
            List<String> currentInstrumentReportItemCodes = currentInstrumentReportItemDtos.stream().map(e -> e.getReportItemCode()).collect(Collectors.toList());


            List<String> currentReportItemDtoCodes = currentReportItemDtos.stream().map(ReportItemDto::getReportItemCode).collect(Collectors.toList());


            // 判断报告项目是否全部包含在仪器报告项目中
            if (!currentInstrumentReportItemCodes.containsAll(currentReportItemDtoCodes)){
                log.warn("条码号/样本id [{}/{}] 专业组id/名称 [{}/{}] 专业小组id/名称 [{}/{}] 下的仪器未包含样本在当前专业组下的所有报告项目 [报告项目编码：{}]，继续匹配下一个专业小组！！！", applySample.getBarcode(), applySample.getApplySampleId(),
                        groupId, applySampleItemList.iterator().next().getGroupName(),next.getInstrumentGroupId(),next.getInstrumentGroupName(),String.join(",",currentReportItemDtoCodes));
                continue;
            }

            // 当前专业小组仪器报告项目分组
            Map<String, List<InstrumentReportItemDto>> currentInstrumentReportItemGroup = currentInstrumentReportItemDtos.stream().collect(Collectors.groupingBy(e -> e.getReportItemCode()));


            // 填充项目通道号
            currentReportItemDtos.stream().forEach(item -> {
                ReportItem reportItem = new ReportItem();
                reportItem.setReportItemCode(item.getReportItemCode());
                reportItem.setReportItemName(item.getReportItemName());
                reportItem.setTestItemCode(item.getTestItemCode());
                reportItem.setTestItemName(item.getTestItemName());
                reportItem.setGroupCode(professionalGroupDto.getGroupCode());
                reportItem.setGroupName(professionalGroupDto.getGroupName());

                reportItem.setInstrumentGroupCode(next.getInstrumentGroupCode());
                reportItem.setInstrumentGroupName(next.getInstrumentGroupName());

                Optional<InstrumentReportItemDto> max = currentInstrumentReportItemGroup.get(item.getReportItemCode()).stream().max(Comparator.comparing(InstrumentReportItemDto::getUpdateDate));
                reportItem.setInstrumentCode(max.isPresent() ? max.get().getInstrumentCode() : StringUtils.EMPTY);
                reportItem.setInstrumentName(max.isPresent() ? max.get().getInstrumentName() : StringUtils.EMPTY);
                reportItem.setChannelCode(max.isPresent() ? max.get().getInstrumentChannel() : StringUtils.EMPTY);
                reportItems.add(reportItem);
            });

            isFind.set(true);
            break;
        }
        ;

        // 如果全部专业小组都没有匹配到，那么就抛出异常
        if (!isFind.get()){
            log.error("条码号/样本id [{}/{}] 在专业组 [{}] 下没有匹配到合适的专业小组进行分拣，无法推送到 Roche 流水线前处理上机分拣！！！", applySample.getBarcode(), applySample.getApplySampleId(), applySampleItemList.iterator().next().getGroupName());
            throw new IllegalStateException(String.format("条码号/样本id [%s/%s] 专业组id/名称 [%s/%s] 下没有匹配到合适的专业小组, 报告项目 [{%s}] 未分配到同一个专业小组的仪器报告项目下面！！！", applySample.getBarcode(), applySample.getApplySampleId(),
                    groupId, applySampleItemList.iterator().next().getGroupName(),
                    currentReportItemDtos.stream().map(ReportItemDto::getReportItemName).collect(Collectors.joining(","))));
        }
    }


    // 匹配报告项目的通道号
    //
    private static void doReportItemChannelCode(ApplySampleDto applySample, ReportItemDto tempReportItemDto, List<InstrumentGroupDto> instrumentGroupSorted, List<InstrumentGroupInstrumentDto> instrumentGroupInstrumentDtos, List<InstrumentReportItemDto> instrumentReportItemDtos, ReportItem reportItem) {
        // 跟当前检验项目所在的专业小组（这里已经过滤掉了其他专业组的专业小组） 已经按照优先级进行排序
        for (InstrumentGroupDto sortGroupTestItemDto : instrumentGroupSorted) {

            // 获取当前专业小组的仪器 并根据更新时间倒序
            List<InstrumentGroupInstrumentDto> tempInstrumentGroupInstrumentDtos = instrumentGroupInstrumentDtos.stream().filter(e->Objects.equals(e.getInstrumentGroupId(),sortGroupTestItemDto.getInstrumentGroupId())).sorted(Comparator.comparing(InstrumentGroupInstrumentDto::getUpdateDate).reversed()).collect(Collectors.toList());


            for (InstrumentGroupInstrumentDto tempInstrumentGroupInstrumentDto : tempInstrumentGroupInstrumentDtos) {

                // 获取专业小组仪器的 仪器报告项目--这里是不区分专业小组的
                List<InstrumentReportItemDto> collect = instrumentReportItemDtos.stream().filter(e -> Objects.equals(e.getInstrumentId(), tempInstrumentGroupInstrumentDto.getInstrumentId())).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(collect) && collect.stream().anyMatch(i -> Objects.equals(i.getReportItemCode(), tempReportItemDto.getReportItemCode()))) {
                    InstrumentReportItemDto instrumentReportItemDto = collect.stream().filter(e -> Objects.equals(e.getReportItemCode(), tempReportItemDto.getReportItemCode())).findFirst().get();
                    if (StringUtils.isBlank(instrumentReportItemDto.getInstrumentChannel())){
                        log.warn("条码号/申请单样本id [{}/{}] 检验项目/报告项目 [{}/{}] 在 专业组/专业小组 [{}/{}] 仪器 [{}] 下 未配置通道号，继续匹配下一台仪器。。。", applySample.getBarcode(),applySample.getApplySampleId(),
                                reportItem.getTestItemName(),reportItem.getReportItemName(),reportItem.getGroupName(),reportItem.getInstrumentGroupName(),tempInstrumentGroupInstrumentDto.getInstrumentName());
                        continue;
                    }

                    reportItem.setInstrumentGroupCode(sortGroupTestItemDto.getInstrumentGroupCode());
                    reportItem.setInstrumentGroupName(sortGroupTestItemDto.getInstrumentGroupName());
                    reportItem.setInstrumentCode(tempInstrumentGroupInstrumentDto.getInstrumentCode());
                    reportItem.setInstrumentName(tempInstrumentGroupInstrumentDto.getInstrumentName());
                    reportItem.setChannelCode(instrumentReportItemDto.getInstrumentChannel());
                    return;
                }

            }

        }
    }


    @Override
    public IT3000HandleVo.Action action() {
        return IT3000HandleVo.Action.APPLY_SAMPLE_INFO;
    }


    @Getter
    @Setter
    private static class ReportItem {
        /**
         * 报告项目名称
         */
        private String reportItemName;

        /**
         * 报告项目编码
         */
        private String reportItemCode;

        /**
         * 通道号
         */
        private String channelCode;

        /**
         * 检验项目编码
         */
        private String testItemCode;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 专业组编码
         */
        private String groupCode;
        /**
         * 专业组名称
         */
        private String groupName;
        /**
         * 专业小组编码
         */
        private String instrumentGroupCode;
        /**
         * 专业小组名称
         */
        private String instrumentGroupName;
        /**
         * 仪器编码
         */
        private String instrumentCode;
        /**
         * 仪器名称
         */
        private String instrumentName;


        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ReportItem that = (ReportItem) o;
            return Objects.equals(reportItemCode, that.reportItemCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(reportItemCode);
        }
    }

}
