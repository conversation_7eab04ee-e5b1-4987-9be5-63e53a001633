package com.labway.lims.apply.service.chain.pick.two;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.InfectionTwoPickDto;
import com.labway.lims.apply.api.dto.MicrobiologyTwoPickDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;

@Getter
@Setter
@SuppressWarnings("unchecked")
public class TwoPickContext extends StopWatchContext {

    /**
     * 申请单样本
     */
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    /**
     * 申请单
     */
    public static final String APPLY = "APPLY_" + IdUtil.objectId();

    /**
     * 加急申请单样本
     */
    public static final String URGENT_APPLY_SAMPLE = "URGENT_APPLY_SAMPLE_" + IdUtil.objectId();

    /**
     * 申请单样本项目
     */
    public static final String APPLY_SAMPLE_ITEMS = "APPLY_SAMPLE_ITEMS_" + IdUtil.objectId();

    /**
     * 所在的试管架
     */
    public static final String RACK_LOGIC = "RACK_LOGIC_" + IdUtil.objectId();

    /**
     * 所在试管架位置
     */
    public static final String RACK_LOGIC_SPACE = "RACK_LOGIC_SPACE_" + IdUtil.objectId();

    /**
     * 要分拣到哪个专业小组
     */
    public static final String INSTRUMENT_GROUP = "INSTRUMENT_GROUP_" + IdUtil.objectId();

    /**
     * 已经分拣过的样本
     */
    public static final String TWO_PICKED_SAMPLES = "TWO_PICKED_SAMPLES_" + IdUtil.objectId();

    /**
     * 配置的血培养检验项目编码
     */
    public static final String BLOOD_CULTURE_TEST_ITEM_CODES = "BLOOD_CULTURE_TEST_ITEM_CODES_" + IdUtil.objectId();

    private TwoPickDto twoPick;
    /**
     * 分拣日期
     */
    private Date twoPickDate;

    /**
     * 当前时间
     */
    private Date currentDate;

    public TwoPickContext(TwoPickDto twoPick) {
        this.twoPick = twoPick;
        put(TWO_PICKED_SAMPLES, new CopyOnWriteArrayList<>());
        if (twoPick instanceof MicrobiologyTwoPickDto) {
            // 微生物二次分拣
            this.twoPickDate = ((MicrobiologyTwoPickDto)twoPick).getTwoPickDate();
        } else if (twoPick instanceof InfectionTwoPickDto) {
            // 院感二次分拣
            this.twoPickDate = ((InfectionTwoPickDto)twoPick).getTwoPickDate();
        }
        if (Objects.isNull(this.twoPickDate)) {
            this.twoPickDate = new Date();
        }
        this.currentDate = new Date();
    }

    public static TwoPickContext from(Context context) {
        return (TwoPickContext)context;
    }

    public ApplySampleDto getApplySample() {
        return (ApplySampleDto)get(APPLY_SAMPLE);
    }

    public ApplyDto getApply() {
        return (ApplyDto)get(APPLY);
    }

    public List<ApplySampleItemDto> getApplySampleItems() {
        return (List<ApplySampleItemDto>)get(APPLY_SAMPLE_ITEMS);
    }

    public InstrumentGroupDto getInstrumentGroup() {
        return (InstrumentGroupDto)get(INSTRUMENT_GROUP);
    }

    public RackLogicDto getRackLogic() {
        return (RackLogicDto)get(RACK_LOGIC);
    }

    public RackLogicSpaceDto getRackLogicSpace() {
        return (RackLogicSpaceDto)get(RACK_LOGIC_SPACE);
    }

    public List<ApplySampleTwoPickDto> getApplySampleTwoPicks() {
        return (List<ApplySampleTwoPickDto>)get(TWO_PICKED_SAMPLES);
    }

    /**
     * 获取血培养检验项目的编码
     */
    public Set<String> getBloodCultureTestItemCodes(){
        return (Set<String>) getOrDefault(BLOOD_CULTURE_TEST_ITEM_CODES, Collections.emptySet());
    }

    @Override
    protected String getWatcherName() {
        return "二次分拣";
    }

}
