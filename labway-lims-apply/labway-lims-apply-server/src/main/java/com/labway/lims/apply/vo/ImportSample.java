package com.labway.lims.apply.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ImportSample {
    /**
     * 外部条码
     */
    @ExcelProperty(value = "条码号", index = 4)
    private String outBarcode;

    /**
     * 患者姓名
     */
    @ExcelProperty(value = "姓名", index = 5)
    private String patientName;
    /**
     * 患者性别
     */
    @ExcelProperty(value = "性别", index = 6)
    private String patientSex;
    /**
     * 卡号
     */
    @ExcelProperty(value = "卡号", index = 7)
    private String cardNo;
    /**
     * 患者年龄
     */
    @ExcelProperty(value = "年龄", index = 10)
    private Integer patientAge;
}
