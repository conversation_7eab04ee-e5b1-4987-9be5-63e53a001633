package com.labway.lims.apply.model.es;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.List;

/**
 * 微生物
 */
@Getter
@Setter
public final class MicrobiologyInspection extends BaseSampleEsModel {
    /**
     * 微生物结果
     */
    @Field(type = FieldType.Nested)
    private List<MicrobiologyResult> results;
    /**
     * 微生物细菌
     */
    @Field(type = FieldType.Nested)
    private List<MicrobiologyGerms> germs;

}
