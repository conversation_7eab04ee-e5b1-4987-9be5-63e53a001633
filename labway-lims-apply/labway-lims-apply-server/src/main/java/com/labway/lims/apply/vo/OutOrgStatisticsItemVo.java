package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class OutOrgStatisticsItemVo implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 委托单位
     */
    private String exportOrgName;
    /**
     * 外送机构id
     */
    private Long exportOrgId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 送检时间
     */
    private String inspectionDate;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 蓝本价
     */
    private BigDecimal blueprintPrice;

    /**
     * 折扣价
     */
    private BigDecimal discountedPrice;

    /**
     * 外送价格
     */
    private BigDecimal deliveryPrice;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 检验项目创建时间
     */
    private Date testItemCreateDate;

    /**
     * 就诊类型
     */
    private String applyTypeName;

    /**
     * 就诊类型
     */
    private String applyTypeCode;
}
