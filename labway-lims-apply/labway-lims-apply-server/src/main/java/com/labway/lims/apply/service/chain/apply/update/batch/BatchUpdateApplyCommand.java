package com.labway.lims.apply.service.chain.apply.update.batch;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.HspOrganizationDto;

import lombok.extern.slf4j.Slf4j;

/**
 * 修改申请单
 * 
 * <AUTHOR>
 * @since 2024/2/21 15:22
 */
@Slf4j
@Component
public class BatchUpdateApplyCommand implements Command {

    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        BatchUpdateApplyContext from = BatchUpdateApplyContext.from(c);

        LoginUserHandler.User user = from.getUser();

        HspOrganizationDto hspOrganization = from.getHspOrganization();

        // 修改申请单
        List<Long> applyIds = from.getApplys().stream().map(ApplyDto::getApplyId).collect(Collectors.toList());
        ApplyDto apply = new ApplyDto();
        apply.setHspOrgId(hspOrganization.getHspOrgId());
        apply.setHspOrgCode(hspOrganization.getHspOrgCode());
        apply.setHspOrgName(hspOrganization.getHspOrgName());

        apply.setUpdaterName(user.getNickname());
        apply.setUpdaterId(user.getUserId());
        apply.setUpdateDate(new Date());
        applyService.updateByApplyIds(apply, applyIds);

        // 修改申请单样本
        List<ApplySampleDto> applySamples = from.getApplySamples();
        List<Long> applySampleIds =
            applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());

        ApplySampleDto applySample = new ApplySampleDto();

        applySample.setHspOrgName(hspOrganization.getHspOrgName());
        applySample.setHspOrgCode(hspOrganization.getHspOrgCode());

        applySample.setUpdaterName(user.getNickname());
        applySample.setUpdaterId(user.getUserId());
        applySample.setUpdateDate(new Date());
        applySampleService.updateInfoByApplySampleIds(applySample, applySampleIds);
        return CONTINUE_PROCESSING;
    }
}
