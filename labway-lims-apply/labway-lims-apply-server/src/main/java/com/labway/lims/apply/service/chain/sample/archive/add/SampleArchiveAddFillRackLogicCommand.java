package com.labway.lims.apply.service.chain.sample.archive.add;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.RackLogicArchivePositionEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackArchiveService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.service.RackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 获取 物理试管架 对应 归档逻辑试管架
 * 
 * <AUTHOR>
 * @since 2023/4/14 13:18
 */
@Slf4j
@Component
public class SampleArchiveAddFillRackLogicCommand implements Command {

    @DubboReference
    private RackService rackService;
    @DubboReference
    private RackLogicService rackLogicService;
    @DubboReference
    private RackArchiveService rackArchiveService;

    @Resource
    private ApplySampleService applySampleService;

    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SampleArchiveAddContext from = SampleArchiveAddContext.from(context);
        LoginUserHandler.User user = from.getUser();
        RackDto rackDto = from.getRack();
        final String barcode = from.getApplySamples().iterator().next().getBarcode();

        // 物理试管架对应归档逻辑试管架
        List<RackLogicDto> rackLogicDtos = rackLogicService.selectByRackId(rackDto.getRackId());
        if (rackLogicDtos.size() > 1) {
            throw new LimsException("试管架存在多条归档信息");
        }

        log.info("条码 [{}] 归档, 获取到逻辑试管架 {}", barcode, JSON.toJSONString(rackLogicDtos));

        // 是否需要新增 归档逻辑试管架
        Boolean needAddRackLogic = Boolean.FALSE;
        RackLogicDto rackLogicDto;
        if (CollectionUtils.isNotEmpty(rackLogicDtos)) {
            rackLogicDto = rackLogicDtos.get(0);
        } else {
            needAddRackLogic = Boolean.TRUE;
            // 需要 新增 归档逻辑试管架
            rackLogicDto = new RackLogicDto();

            rackLogicDto.setRackLogicId(snowflakeService.genId());
            rackLogicDto.setRackId(rackDto.getRackId());
            rackLogicDto.setRackCode(rackDto.getRackCode());
            rackLogicDto.setRow(rackDto.getRow());
            rackLogicDto.setColumn(rackDto.getColumn());
            rackLogicDto.setPosition(RackLogicArchivePositionEnum.IN_USE.getCode());
            rackLogicDto.setCurrentGroupId(user.getGroupId());
            rackLogicDto.setCurrentGroupName(user.getGroupName());
            rackLogicDto.setNextGroupId(NumberUtils.LONG_ZERO);
            rackLogicDto.setNextGroupName(StringUtils.EMPTY);
            rackLogicDto.setLastHandover(StringUtils.EMPTY);
        }

        from.put(SampleArchiveAddContext.RACK_LOGIC_ADD_FLAG, needAddRackLogic);
        from.put(SampleArchiveAddContext.RACK_LOGIC, rackLogicDto);

        return CONTINUE_PROCESSING;
    }
}
