package com.labway.lims.apply.service.chain.pick.one.cancel;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

@Getter
@Setter
@SuppressWarnings("unchecked")
public class CancelOnePickContext extends StopWatchContext {

    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    private long applySampleId;

    /**
     * 是不是恢复终止检验
     */
    private boolean regainTerminate = false;

    public static CancelOnePickContext from(Context c) {
        return (CancelOnePickContext) c;
    }

    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }

    @Override
    protected String getWatcherName() {
        return "取消一次分拣";
    }

}
