package com.labway.lims.apply.service.chain.apply.update;

import cn.hutool.extra.spring.SpringUtil;
import com.labway.lims.api.BarcodeUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.AddSamplesInfoDto;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.dto.UpdateTestApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.service.chain.apply.add.CheckParamCommand;
import com.labway.lims.apply.service.chain.apply.add.GroupSampleCommand;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.api.service.ref.IBarcodeSettingServiceRef;
import joptsimple.internal.Strings;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext.DELETE_APPLY_SAMPLE_ITEM_IDS;

@Getter
@Component
@Slf4j
public class UpdateGroupSampleCommand implements Command {
    @Resource
    private TestItemService testItemService;
    @Resource
    private ApplyService applyService;

    @Resource
    private ApplySampleService applySampleService;

    @Resource
    private ApplySampleItemService applySampleItemService;

    @Resource
    private SnowflakeService snowflakeService;

    @Resource
    private BarcodeUtils barcodeUtils;

    @DubboReference
    private IBarcodeSettingServiceRef barcodeSettingService;

    @Resource
    private GroupSampleCommand groupSampleCommand;

    @Resource
    private CheckParamCommand checkParamCommand;
    @Resource
    private CheckSamePersonDayItemCommand checkSamePersonDayItemCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final UpdateApplyContext context = UpdateApplyContext.from(c);
        final LoginUserHandler.User user = context.getUser();
        final ApplyDto apply = context.getApply();
        // 根据条码号查出来的样本
        final List<ApplySampleDto> applySamples = context.getApplySamples();
        final Long applyId = apply.getApplyId();
        final TestApplyDto testApply = context.getTestApply();
        final List<TestApplyDto.Item> items = testApply.getItems();
        final Map<Long, TestApplyDto.Item> updateTestApplyItemMap =
                items.stream().collect(Collectors.toMap(TestApplyDto.Item::getTestItemId, v -> v, (a, b) -> a));
        // 拿第一个的用原条码
        final TestApplyDto.Item firstTestItemDto = items.iterator().next();

        if (CollectionUtils.isEmpty(items)) {
            return CONTINUE_PROCESSING;
        }

        final List<TestItemDto> testItems = context.getTestItems();
        if (CollectionUtils.isEmpty(testItems)) {
            throw new IllegalStateException("检验项目不存在");
        }

        // 如果没有传递SampleTypeCode,则不用修改，用数据库对应的数据
        if (CollectionUtils.isNotEmpty(items.stream().filter(t -> StringUtils.isNotBlank(t.getSampleTypeCode())).collect(Collectors.toList()))) {
            updateSampleType(items, testItems);
        }
        //需要新增的信息
        List<ApplySampleDto> applySampleAdds = new ArrayList<>();
        List<ApplySampleItemDto> applySampleItemAdds = new ArrayList<>();
        // 需要修改的信息
        List<ApplySampleItemDto> applySampleItemUpdates = new ArrayList<>();

        // 已有的检验项目
        final Map<Long, ApplySampleItemDto> applySampleItemTestItemIdMap = getApplySampleItemDtoMap(context);

        // 用数据库里的检验项目信息替换前端传过来的检验项目信息
        //        updateTestApplyItemMap
        //                .forEach((k, v) -> Optional.ofNullable(applySampleItemTestItemIdMap.get(v.getTestItemId()))
        //                        .ifPresent(ifc -> v.setCustomCode(ifc.getSplitCode())));

        // 项目分组 如果是 UpdateTestApplySampleDto 则需要加上专业组
        final Map<String, List<ApplySampleItemDto>> applySampleItemMap = groupSampleCommand.groupTestItem(apply,
                testItems, updateTestApplyItemMap, testApply instanceof UpdateTestApplySampleDto);

        // 判断该样本 是否经历免疫二次分拣过 # 1.1.3.7 https://www.tapd.cn/59091617/prong/stories/view/1159091617001001872?from_iteration_id=1159091617001000235
        boolean isImmunityTwoPicked = Objects.nonNull(applySamples) && applySamples.stream().anyMatch(e -> Objects.equals(YesOrNoEnum.YES.getCode(), e.getIsTwoPick())
                && Objects.equals(YesOrNoEnum.YES.getCode(), e.getIsImmunityTwoPick()));

        // 使用原始条码的样本
        ApplySampleDto barcodeApplySample = null;

        // 过滤出需要添加的检验项目
        for (final Map.Entry<String, List<ApplySampleItemDto>> entry : applySampleItemMap.entrySet()) {
            final List<ApplySampleItemDto> v = entry.getValue();

            // 过滤出需要修改的检验项目
            applySampleItemUpdates.addAll(filterNeedUpdateTestItem(user, applySampleItemTestItemIdMap, v));

            // 过滤出已存在的检验项目 拿第一个就行
            final Long existApplySampleId = v.stream().map(m -> applySampleItemTestItemIdMap.get(m.getTestItemId()))
                    .filter(Objects::nonNull).findFirst().map(ApplySampleItemDto::getApplySampleId).orElse(null);

            // 如果为null，那就证明没有已存在检验项目，需要新增样本
            // 免疫二次分拣过样本直接生成新条码 # 1.1.3.7 https://www.tapd.cn/59091617/prong/stories/view/1159091617001001872?from_iteration_id=1159091617001000235
            final ApplySampleDto applySample =
                    (isImmunityTwoPicked || Objects.isNull(existApplySampleId)) ? groupSampleCommand.createApplySample() : null;

            if (Objects.nonNull(applySample)) {
                applySample.setApplyId(applyId);
                // dev-1.1.1.1  条码和原始条码逻辑都在同人同天后获取赋值
                applySample.setBarcode(Strings.EMPTY);
                applySample.setOriginalBarcode(Strings.EMPTY);
                applySample.setOutBarcode(StringUtils.defaultString(context.getOutBarcode(), testApply.getOutBarcode()));
                applySample.setHspOrgCode(apply.getHspOrgCode());
                applySample.setHspOrgName(apply.getHspOrgName());
                applySampleAdds.add(applySample);
            }

            Long addApplySampleId = Optional.ofNullable(applySample).map(ApplySampleDto::getApplySampleId).orElse(null);

            // 过滤出需要添加的检验项目
            for (final ApplySampleItemDto applySampleItem : v) {
                // 已存在的跳过
                if (Objects.nonNull(applySampleItemTestItemIdMap.get(applySampleItem.getTestItemId()))) {
                    continue;
                }

                // 设置样本信息
                setApplySampleContent(applySample, applySampleItem);
                // 检验项目和前端传过来的第一个检验项目相同， 该新建样本使用原条码
                if (Objects.equals(applySampleItem.getTestItemId(), firstTestItemDto.getTestItemId())) {
                    barcodeApplySample = applySample;
                }

                // 获取申请单样本id
                Long applySampleId = ObjectUtils.defaultIfNull(addApplySampleId, existApplySampleId);

                if (Objects.isNull(applySampleId)) {
                    throw new IllegalStateException("添加检验项目失败");
                }

                applySampleItem.setApplySampleId(applySampleId);
                applySampleItem.setApplySampleItemId(snowflakeService.genId());
                applySampleItem.setApplyId(apply.getApplyId());

                applySampleItemAdds.add(applySampleItem);
            }
        }

        // 校验分条码后血培养项目  同一个条码下不能由多个血培养项目
        final ArrayList<ApplySampleItemDto> applySampleItemDtos = new ArrayList<>(applySampleItemAdds);
        applySampleItemDtos.addAll(applySampleItemUpdates);
        groupSampleCommand.checkBloodCulture(applySampleItemDtos, context.getBloodCultureTestItemCodes());


        // 删除样本下没有项目的样本
        this.removeNullItemApplySample(applySampleItemAdds, applySampleAdds);

        // 如果 ConfirmStatus 等于1的话就是返回要添加样本信息
        if (testApply instanceof UpdateTestApplySampleDto) {
            UpdateTestApplySampleDto updateTestApplySampleDto = (UpdateTestApplySampleDto) testApply;
            if (updateTestApplySampleDto.getConfirmStatus() != null && 1 == updateTestApplySampleDto.getConfirmStatus()) {
                // 填充新条码提示信息
                int addSize = checkApplySampleNewBarcode(applySampleAdds, applySampleItemAdds, applySampleItemUpdates, firstTestItemDto, context);

                // 有新增条码时判断同人同天和性别
                if (addSize > 0) {

                    // 校验病人 同人同天同项目提示
                    SpringUtil.getBean(CheckSamePersonDayItemCommand.class).execute(c);

                    // 检验项目限制性别校验
                    SpringUtil.getBean(CheckTestItemLimitSexCommand.class).execute(c);

                    return PROCESSING_COMPLETE;
                }
            }
        }

        // 二次分拣的样本不可以再追加项目
        this.checkApplySampleTwoPick(testApply, applySamples, applySampleItemAdds, applySampleItemUpdates);

        // 填充条码到申请单样本
        this.fillBarcodeForApplySample(applySampleAdds, applySampleItemUpdates, applySamples, testApply.getBarcode(), context.getOutBarcode(), apply, barcodeApplySample, isImmunityTwoPicked, context);

        // 如果有新增血培养项目那么需要校验血培养信息
        context.put(UpdateApplyContext.BLOOD_CULTURE, checkBloodCultureInfo(applySampleItemAdds, items, context.getBloodCultureTestItemCodes()));

        context.put(UpdateApplyContext.UPDATE_APPLY_SAMPLE_ITEMS, applySampleItemUpdates);
        context.put(UpdateApplyContext.ADD_APPLY_SAMPLE_ITEMS, applySampleItemAdds);
        context.put(UpdateApplyContext.ADD_APPLY_SAMPLES, applySampleAdds);

        return CONTINUE_PROCESSING;
    }

    @Nonnull
    private static List<ApplySampleItemDto> filterNeedUpdateTestItem(LoginUserHandler.User user,
                                                                     Map<Long, ApplySampleItemDto> applySampleItemTestItemIdMap, List<ApplySampleItemDto> v) {
        return v.stream().filter(f -> Objects.nonNull(applySampleItemTestItemIdMap.get(f.getTestItemId()))).map(p -> {
            ApplySampleItemDto pItem = new ApplySampleItemDto();
            // 将 p 对象属性全部设置成null
            pItem.setApplySampleItemId(applySampleItemTestItemIdMap.get(p.getTestItemId()).getApplySampleItemId());
            pItem.setCount(ObjectUtils.defaultIfNull(p.getCount(), 1));
            pItem.setUrgent(p.getUrgent());
            pItem.setApplySampleId(applySampleItemTestItemIdMap.get(p.getTestItemId()).getApplySampleId());
            pItem.setSampleTypeCode(p.getSampleTypeCode());
            pItem.setSampleTypeName(p.getSampleTypeName());
            pItem.setTubeCode(p.getTubeCode());
            pItem.setTubeName(p.getTubeName());
            pItem.setSplitCode(p.getSplitCode());
            pItem.setItemType(p.getItemType());
            pItem.setTestItemId(p.getTestItemId());
            pItem.setTestItemCode(p.getTestItemCode());
            pItem.setTestItemName(p.getTestItemName());
            pItem.setUpdateDate(new Date());
            pItem.setUpdaterId(user.getUserId());
            pItem.setUpdaterName(user.getNickname());
            return pItem;
        }).collect(Collectors.toList());
    }


    /**
     * 样本录入修改新增的时候可以修改样本类型
     *
     * @param testItems
     */
    private void updateSampleType(List<TestApplyDto.Item> items, List<TestItemDto> testItems) {
        //Map<Long, InformationEntryTestApplyDto> testItemParamMapByTestItemId = hisTestApplyItems.stream().collect(Collectors.toMap(HisTestApplyItemDto::getTestItemId, Function.identity(), (a, b) -> a));
        Map<Long, TestApplyDto.Item> itemMap = items.stream().collect(Collectors.toMap(TestApplyDto.Item::getTestItemId, Function.identity(), (a, b) -> a));
        for (TestItemDto testItem : testItems) {
            Long testItemId = testItem.getTestItemId();
            TestApplyDto.Item testApplyItemDto = itemMap.get(testItemId);
            testItem.setSampleTypeCode(testApplyItemDto.getSampleTypeCode());
            testItem.setSampleTypeName(testApplyItemDto.getSampleTypeName());
            testItem.setTubeCode(testApplyItemDto.getTubeCode());
            testItem.setTubeName(testApplyItemDto.getTubeName());
        }
    }

    /**
     * 如果包含血培养项目，那么需要校验血培养信息
     */
    private ApplySampleItemBloodCultureDto checkBloodCultureInfo(List<ApplySampleItemDto> applySampleItemAdds,
                                                                 List<TestApplyDto.Item> items,
                                                                 Set<String> bloodCultureTestItemCodes) {
        // 如果为空就不校验
        if (CollectionUtils.isEmpty(applySampleItemAdds) || CollectionUtils.isEmpty(items)) {
            return null;
        }

        // 过滤出血培养项目
        final ApplySampleItemDto applySampleItem = applySampleItemAdds.stream()
                // 血培养类型 || 血培养项目
                .filter(f -> Objects.equals(f.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())
                        || bloodCultureTestItemCodes.contains(f.getTestItemCode()))
                .findFirst()
                .orElse(null);

        if (Objects.isNull(applySampleItem)) {
            return null;
        }

        final TestApplyDto.Item item = items.stream()
                .filter(f -> Objects.equals(f.getTestItemId(), applySampleItem.getTestItemId())).findFirst().orElse(null);

        if (Objects.isNull(item)) {
            throw new IllegalStateException("血培养项目不存在");
        }

        final ApplySampleItemBloodCultureDto bloodCulture = item.getBloodCulture();
        if (Objects.isNull(bloodCulture)) {
            throw new IllegalStateException("请输入血培养信息");
        }

        // 检查血培养参数
        checkParamCommand.checkBloodCultureInfo(bloodCulture);

        bloodCulture.setApplySampleItemBloodCultureId(snowflakeService.genId());
        bloodCulture.setApplySampleId(applySampleItem.getApplySampleId());
        bloodCulture.setApplyId(applySampleItem.getApplyId());
        bloodCulture.setApplySampleItemId(applySampleItem.getApplySampleItemId());
        bloodCulture.setTestItemId(applySampleItem.getTestItemId());
        bloodCulture.setTestItemCode(applySampleItem.getTestItemCode());
        bloodCulture.setTestItemName(applySampleItem.getTestItemName());
        bloodCulture.setCreateDate(applySampleItem.getCreateDate());
        bloodCulture.setCreatorId(applySampleItem.getCreatorId());
        bloodCulture.setCreatorName(applySampleItem.getCreatorName());
        bloodCulture.setUpdateDate(applySampleItem.getUpdateDate());
        bloodCulture.setUpdaterId(applySampleItem.getUpdaterId());
        bloodCulture.setUpdaterName(applySampleItem.getUpdaterName());
        bloodCulture.setIsDelete(applySampleItem.getIsDelete());

        return bloodCulture;
    }

    /**
     * 填充条码到申请单样本
     */
    private void fillBarcodeForApplySample(List<ApplySampleDto> applySampleAdds, List<ApplySampleItemDto> applySampleItemUpdates,
                                           List<ApplySampleDto> applySamples, String barcode,
                                           String outBarcode, ApplyDto apply,
                                           ApplySampleDto barcodeApplySample, boolean isImmunityTwoPicked, UpdateApplyContext context) {
        LinkedList<String> barcodes = new LinkedList<>();
        int size = applySampleAdds.size();
        // 1. 没有需要更新的项目(说明条码下没有项目了)
        // 2. 查出来的数量如果大于1（免疫/组间交接）
        if (applySampleItemUpdates.isEmpty() && !(CollectionUtils.size(applySamples) > 1)) {
            // 删除条码 applySamples
            if (StringUtils.isNotBlank(barcode)) {
                final Set<Long> applySampleIds = applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());
                context.put(UpdateApplyContext.DELETE_APPLY_SAMPLE_IDS, applySampleIds);
                size--;
                barcodes.addFirst(barcode);
            }
        }

        // 需要生成的新条码：
        barcodes.addAll(groupSampleCommand.getBarcodeList(false, outBarcode, apply, size));

        for (ApplySampleDto applySampleAdd : applySampleAdds) {
            if (Objects.equals(applySampleAdd, barcodeApplySample)) {
                applySampleAdd.setBarcode(barcodes.pollFirst());
            } else {
                applySampleAdd.setBarcode(barcodes.pollLast());
            }
            applySampleAdd.setOriginalBarcode(applySampleAdd.getBarcode());
            if (isImmunityTwoPicked) {
                // 标记免疫二次分拣样本，这里考虑到免疫二次分拣，需要合并回传的情况
                applySampleAdd.setIsImmunityTwoPick(YesOrNoEnum.YES.getCode());
            }
        }
    }

        // 二次分拣的样本不可以再追加项目
    private void checkApplySampleTwoPick(TestApplyDto testApply, List<ApplySampleDto> applySamples, List<ApplySampleItemDto> applySampleItemAdds, List<ApplySampleItemDto> applySampleItemUpdates) {
        if (testApply instanceof UpdateTestApplySampleDto && CollectionUtils.isNotEmpty(applySamples)) {

            final Collection<Long> applySampleIds =
                    applySampleItemAdds.stream().map(ApplySampleItemDto::getApplySampleId).collect(Collectors.toSet());

            for (final ApplySampleDto applySample : applySamples) {
                final Long applySampleId = applySample.getApplySampleId();
                final Integer isTwoPick = applySample.getIsTwoPick();

                if (Objects.equals(isTwoPick, YesOrNoEnum.YES.getCode()) && applySampleIds.contains(applySampleId)) {
                    throw new IllegalStateException(String.format("样本 [%s] 已完成二次分拣，不能加项", applySample.getBarcode()));
                }

                // 如果当前样本是已禁用的，那么不新增 不修改
                if (Objects.equals(applySample.getIsDisabled(), YesOrNoEnum.YES.getCode())) {
                    applySampleItemUpdates.removeIf(f -> Objects.equals(f.getApplySampleId(), applySampleId));
                    applySampleItemAdds.removeIf(f -> Objects.equals(f.getApplySampleId(), applySampleId));
                }

            }
        }
    }
    private int checkApplySampleNewBarcode(List<ApplySampleDto> applySampleAdds, List<ApplySampleItemDto> applySampleItemAdds, List<ApplySampleItemDto> applySampleItemUpdates, TestApplyDto.Item firstTestItemDto, UpdateApplyContext context) {
        AddSamplesInfoDto addSamplesInfoDto = new AddSamplesInfoDto();
        int addSize = applySampleAdds.size();
        Set<String> groupNameSet = applySampleItemAdds.stream().map(ApplySampleItemDto::getGroupName).collect(Collectors.toSet());
        if (applySampleItemUpdates.isEmpty()) {
            addSize--;
            // 构建的第一个条码的项目
            final ApplySampleItemDto first = applySampleItemAdds.stream()
                    .filter(item -> Objects.equals(item.getTestItemId(), firstTestItemDto.getTestItemId()))
                    .findFirst().orElse(new ApplySampleItemDto());
            groupNameSet = applySampleItemAdds.stream()
                    // 和第一个项目公用原始条码的其他项目也要被过滤掉
                    .filter(item -> !Objects.equals(first.getApplySampleId(), item.getApplySampleId()))
                    .map(ApplySampleItemDto::getGroupName)
                    .collect(Collectors.toSet());
        }
        addSamplesInfoDto.setAddCount(addSize);

        addSamplesInfoDto.setGroupNames(groupNameSet);
        context.put(UpdateApplyContext.ADD_SAMPLES_INFO, addSamplesInfoDto);
        // 加项的项目，保存到上下文，用于判断性别限制
        context.put(UpdateApplyContext.ADD_APPLY_SAMPLE_ITEMS, applySampleItemAdds);
        return addSize;
    }

    private void removeNullItemApplySample(List<ApplySampleItemDto> applySampleItemAdds, List<ApplySampleDto> applySampleAdds) {
        // 新增样本检验项目
        if (CollectionUtils.isNotEmpty(applySampleItemAdds)) {
            final Set<Long> applySampleIdSet = applySampleItemAdds.stream().map(ApplySampleItemDto::getApplySampleId).collect(Collectors.toSet());
            applySampleAdds.removeIf(e -> !applySampleIdSet.contains(e.getApplySampleId()));
        }
    }

    private Map<Long, ApplySampleItemDto> getApplySampleItemDtoMap(UpdateApplyContext context) {
        // 库里存在的项目
        final List<ApplySampleItemDto> applySampleItems = getApplySampleItem(context);

        // 页面传过来的项目
        final TestApplyDto testApply = context.getTestApply();

        // 需要删除的样本
        // 【检验加减项目】需支持修改已生成条码所包含的项目的样本类型、管型、自定义码，修改后，自动生成新条码
        // https://www.tapd.cn/59091617/prong/stories/view/1159091617001001759
        final Set<Long> deleteApplySampleItemIdSet = new HashSet<>();

        Stream<ApplySampleItemDto> stream = applySampleItems.stream();
        // 检验项目加减项增加这个过滤
        if (testApply instanceof UpdateTestApplySampleDto) {
            final List<TestApplyDto.Item> items = testApply.getItems();
            final Map<Long, TestApplyDto.Item> testItemMap = items.stream().collect(Collectors.toMap(TestApplyDto.Item::getTestItemId, Function.identity(), (a, b) -> b));

            stream = stream.filter(e -> {
                // 页面的数据 包含 数据库的数据
                final TestApplyDto.Item item = testItemMap.get(e.getTestItemId());
                if (item != null) {
                    // 管型不相同
                    if (!Objects.equals(item.getTubeCode(), e.getTubeCode())
                            // 样本类型不相同
                            || !Objects.equals(item.getSampleTypeCode(), e.getSampleTypeCode())
                            // 自定义码不相同 默认空串, 因为数据库默认为空串
                            //                                || StringUtils.isNotBlank(item.getCustomCode())
                            || !Objects.equals(StringUtils.defaultString(item.getCustomCode()), e.getSplitCode())
                    ) {
                        deleteApplySampleItemIdSet.add(e.getApplySampleItemId());
                        return false;
                    }
                }
                return true;
            });
        }
        final Map<Long, ApplySampleItemDto> applySampleItemDtoMap = stream.collect(Collectors.toMap(ApplySampleItemDto::getTestItemId, Function.identity(), (a, b) -> b));
        context.put(DELETE_APPLY_SAMPLE_ITEM_IDS, deleteApplySampleItemIdSet);
        return applySampleItemDtoMap;
    }

    private static void setApplySampleContent(ApplySampleDto applySample, ApplySampleItemDto p) {
        if (Objects.isNull(applySample)) {
            return;
        }
        applySample.setTubeName(StringUtils.defaultString(p.getTubeName()));
        applySample.setTubeCode(StringUtils.defaultString(p.getTubeCode()));
        applySample.setSampleTypeName(StringUtils.defaultString(p.getSampleTypeName()));
        applySample.setSampleTypeCode(StringUtils.defaultString(p.getSampleTypeCode()));
        applySample.setItemType(StringUtils.defaultIfBlank(applySample.getItemType(), p.getItemType()));
        applySample.setIsOutsourcing(p.getIsOutsourcing());

        // 如果不是紧急，那就设置为普通
        if (BooleanUtils.isNotTrue(Objects.equals(applySample.getUrgent(), UrgentEnum.URGENT.getCode()))) {
            applySample.setUrgent(ObjectUtils.defaultIfNull(p.getUrgent(), UrgentEnum.NORMAL.getCode()));
        }

    }

    private List<ApplySampleItemDto> getApplySampleItem(UpdateApplyContext context) {
        final List<Long> applySampleIdList = context.getApplySampleIds();
        if (CollectionUtils.isEmpty(applySampleIdList)) {
            // 需要过滤掉已经终止检验的样本
            final Collection<Long> applySampleIds =
                    applySampleService.selectNoStopTestSampleByApplyIds(List.of(context.getApplyId())).stream()
                            .map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());

            return applySampleItemService.selectByApplySampleIds(applySampleIds);
        }
        // 检验项目加减项， 根据barcode查出来的applySampleIdList
        return applySampleItemService.selectByApplySampleIds(applySampleIdList);
    }
}