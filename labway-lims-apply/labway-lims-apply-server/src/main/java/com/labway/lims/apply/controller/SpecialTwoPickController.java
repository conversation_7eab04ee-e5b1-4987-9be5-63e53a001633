package com.labway.lims.apply.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.BloodCultureTwoPickDto;
import com.labway.lims.apply.api.dto.InfectionTwoPickDto;
import com.labway.lims.apply.api.dto.MicrobiologyTwoPickDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.service.chain.pick.two.TwoPickBloodCultureCommand;
import com.labway.lims.apply.vo.SpecialTwoPickPickVo;
import com.labway.lims.apply.vo.TwoPickPickVo;
import com.labway.lims.apply.vo.TwoPickVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.labway.lims.api.SampleNoUtils.addOneToLastNumber;

/**
 * 特殊二次分拣(主要用户免疫等分批二次分拣的场景)
 */
@Slf4j
@RestController
@RequestMapping("/special-two-pick")
public class SpecialTwoPickController extends BaseController {

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private RackLogicService rackLogicService;

    /**
     * 二次分拣
     */
    @PostMapping("/pick")
    public Object pick(@RequestBody SpecialTwoPickPickVo vo) {
        //入参校验
        vo.validParam();

        final List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(vo.getBarcode());
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalArgumentException("条码不存在");
        }

        // 删除非当前专业组的 || 已经二次分拣的
        applySamples.removeIf(e -> !Objects.equals(e.getGroupId(), LoginUserHandler.get().getGroupId())
                || Objects.equals(e.getIsTwoPick(), YesOrNoEnum.YES.getCode()));

        // 在最终分拣的时候，只能有一个
        if (applySamples.isEmpty()) {
            throw new IllegalArgumentException("没有本专业组条码或该条码已经全部分拣完成");
        }

        if(applySamples.size() !=1 ){
            throw new IllegalArgumentException("条码数量错误");
        }

        //判断样本是否已经被终止了
        final ApplySampleDto applySample = applySamples.iterator().next();
        applySampleService.assertApplySampleUsability(applySample.getApplySampleId());


        if (Objects.equals(applySample.getIsOutsourcing(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalStateException("外送样本请到外送分拣页面分拣");
        }

        if (!Objects.equals(applySample.getItemType(), ItemTypeEnum.ROUTINE.name())) {
            throw new IllegalStateException("该页面只能常规项目进行分拣");
        }

        final List<RackLogicDto> rackLogics = rackLogicService.selectByApplySampleId(applySample.getApplySampleId());
        if (CollectionUtils.isNotEmpty(rackLogics)) {
            if (!Objects.equals(rackLogics.iterator().next().getPosition(),
                    RackLogicPositionEnum.TWO_PICKING.getCode())) {
                throw new IllegalArgumentException("当前条码不可二次分拣，请检查条码是否已经一次分拣或交接");
            }
        }


        List<ApplySampleTwoPickDto> applySampleTwoPicks;
        // 普通
        final TwoPickDto tp = new TwoPickDto();
        tp.setApplySampleId(applySample.getApplySampleId());
        tp.setSampleNo(vo.getSampleNo());
        applySampleTwoPicks = applySampleService.twoPick(tp);

        // 检验项目
        final List<ApplySampleItemDto> applySampleItems =
                applySampleItemService.selectByApplyIds(List.of(applySample.getApplyId()));

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        return Map.of(
                // 样本信息
                "samples", applySampleTwoPicks.stream().map(e -> {
                    final TwoPickVo v = new TwoPickVo();
                    v.setApplySampleId(e.getApplySampleId());
                    v.setSampleNo(e.getSampleNo());
                    v.setInstrumentGroupId(e.getInstrumentGroupId());
                    v.setInstrumentGroupName(e.getInstrumentGroupName());
                    v.setSecondSortColor(e.getSecondSortColor());
                    v.setApplyId(applySample.getApplyId());
                    v.setBarcode(applySample.getBarcode());
                    v.setTestItemNames(
                            applySampleItems.stream().filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                                    .map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));
                    v.setTestItemIds(
                            applySampleItems.stream().filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                                    .map(ApplySampleItemDto::getTestItemId).collect(Collectors.toList()));
//                    v.setTwoPickDate(ObjectUtils.defaultIfNull(vo.getTwoPickDate(), new Date()));
                    v.setSampleTypeName(applySample.getSampleTypeName());
                    v.setTubeName(applySample.getTubeName());
                    v.setHspOrgName(apply.getHspOrgName());
                    v.setPatientName(apply.getPatientName());
                    v.setPatientAge(apply.getPatientAge());
                    v.setPatientSubage(apply.getPatientSubage());
                    v.setPatientSubageUnit(apply.getPatientSubageUnit());
                    v.setPatientSex(apply.getPatientSex());
                    return v;
                }).collect(Collectors.toList()),
                // 是否组间交接了
                "isTransform", applySampleTwoPicks.stream().anyMatch(e -> BooleanUtils.isTrue(e.getIsTransform())),
                // 下一个样本编号
                "nextSampleNo", addOneToLastNumber(applySampleTwoPicks
                        .get(applySampleTwoPicks.size() - 1).getSampleNo()));

    }
}
