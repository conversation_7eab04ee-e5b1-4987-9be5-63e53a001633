package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.MaterialIncomeRecordService;
import com.labway.lims.apply.api.vo.BizMaterialBarcodeVo;
import com.labway.lims.apply.mapper.TbMaterialIncomeRecordMapper;
import com.labway.lims.apply.mapstruct.MaterialDeliveryConverter;
import com.labway.lims.apply.model.TbMaterialIncomeRecord;
import com.labway.lims.apply.service.chain.material.delivery.income.MaterialIncomeChain;
import com.labway.lims.apply.service.chain.material.delivery.income.MaterialIncomeContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 物料入库记录 Service impl
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Slf4j
@DubboService
public class MaterialIncomeRecordServiceImpl implements MaterialIncomeRecordService {

    @Resource
    private TbMaterialIncomeRecordMapper tbMaterialIncomeRecordMapper;

    @Resource
    private MaterialDeliveryConverter materialDeliveryConverter;

    @Resource
    private MaterialIncomeChain materialIncomeChain;

    @Override
    public List<MaterialIncomeInfoDto> selectBySelectMaterialIncomeInfoDto(SelectMaterialIncomeInfoDto dto) {
        QueryWrapper<TbMaterialIncomeRecord> queryWrapper = Wrappers.query();

        queryWrapper.select("distinct income_no, delivery_no, create_date, creator_name");
        queryWrapper.lambda().ge(Objects.nonNull(dto.getBeginIncomeDate()), TbMaterialIncomeRecord::getCreateDate,
            dto.getBeginIncomeDate());
        queryWrapper.lambda().le(Objects.nonNull(dto.getEndIncomeDate()), TbMaterialIncomeRecord::getCreateDate,
            dto.getEndIncomeDate());

        queryWrapper.lambda().eq(TbMaterialIncomeRecord::getGroupId, dto.getGroupId());
        queryWrapper.lambda().eq(StringUtils.isNoneBlank(dto.getIncomeNo()), TbMaterialIncomeRecord::getIncomeNo,
            dto.getIncomeNo());
        return materialDeliveryConverter
            .incomeInfoFromTbMaterialIncomeRecordList(tbMaterialIncomeRecordMapper.selectList(queryWrapper));
    }

    @Override
    public List<MaterialIncomeRecordDto> selectByDeliveryNo(String deliveryNo, long orgId) {
        if (StringUtils.isBlank(deliveryNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMaterialIncomeRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbMaterialIncomeRecord::getDeliveryNo, deliveryNo);
        queryWrapper.eq(TbMaterialIncomeRecord::getOrgId, orgId);
        queryWrapper.eq(TbMaterialIncomeRecord::getIsDelete, YesOrNoEnum.NO.getCode());

        return materialDeliveryConverter
            .fromTbMaterialIncomeRecordList(tbMaterialIncomeRecordMapper.selectList(queryWrapper));
    }

    @Override
    public void materialIncomeByRecordId(String deliveryNo, List<MaterialDeliveryIncomeItemDto> incomeItemList) {

        LoginUserHandler.User user = LoginUserHandler.get();

        final MaterialIncomeContext context = new MaterialIncomeContext();
        context.setDeliveryNo(deliveryNo);
        context.setIncomeItemList(incomeItemList);
        context.setUser(user);

        try {
            if (!materialIncomeChain.execute(context)) {
                throw new IllegalStateException("接收业务中台出库失败");
            }
        } catch (RuntimeException e) {
            log.error("物料入库失败 [{}]", deliveryNo, e);
            throw e;
        } catch (Exception e) {
            log.error("物料入库失败 [{}]", deliveryNo, e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("物料入库 [{}] 耗时\n{}", deliveryNo, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMaterialIncomeRecords(List<MaterialIncomeRecordDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 入库记录
        List<TbMaterialIncomeRecord> targetList =
            materialDeliveryConverter.incomeRecordFromMaterialIncomeRecordDtoList(list);

        // 数量 分区批次插入
        List<List<TbMaterialIncomeRecord>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbMaterialIncomeRecordMapper.batchAddMaterialIncomeRecords(item));

        log.info("用户 [{}] 新增入库记录[{}]成功", loginUser.getNickname(), JSON.toJSONString(targetList));

    }

	/**
	 * 中台批量查询物料 - 批次 - 条码号
	 *
	 * @param materialInfos 物料信息 - 物料条码 - 批次号
	 * @return {@link BizMaterialBarcodeVo}
	 */
	@Override
	public List<BizMaterialBarcodeVo> selectMaterialBarcode(BusinessCenterMaterialBarcodeSearchDto materialInfos) {
		Map<String, String> materialInfoMap = materialInfos.getMaterialInfos().stream().collect(Collectors.toMap(BusinessCenterMaterialBarcodeSearchDto.MaterialInfoDto::getMaterialCode, BusinessCenterMaterialBarcodeSearchDto.MaterialInfoDto::getBatchNo, (v1, v2) -> v2));
		List<TbMaterialIncomeRecord> incomeRecords = tbMaterialIncomeRecordMapper.searchMaterialBarcodeByMaterialCodesAndBatchNos(materialInfoMap.keySet(), materialInfoMap.values());
		return materialDeliveryConverter.convertIncomeRecords2BizMaterialBarcodeVoList(incomeRecords);
	}
}
