package com.labway.lims.apply.controller.rocheIT3000;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.vo.IT3000HandleVo;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * IT3000 扫描到条码
 */
@Slf4j
@Component
class RocheIT3000SeenAction implements ActionStrategy {

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private GroupService groupService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object action(IT3000HandleVo vo) throws Exception {

        final String groupCode = vo.getExtras().getString("groupCode");
        final String instrumentCode = vo.getExtras().getString("instrumentCode");
        final String barcode = vo.getExtras().getString("barcode");
        final String position = vo.getExtras().getString("position");

        // 查询申请单样本
        List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(applySamples)) {
            log.error("条码 [{}] 无法记录IT3000扫描到条码的条码环节，因为没有获取到尚未分拣的申请单样本", barcode);
            return Map.of();
        }

        // 过滤专业组样本
        if (StringUtils.isNotBlank(groupCode)){
            ProfessionalGroupDto professionalGroupDto = groupService.selectByGroupCode(groupCode, LoginUserHandler.get().getOrgId());
            if (Objects.isNull(professionalGroupDto)){
                log.error("条码 [{}] 无法记录IT3000扫描到条码的条码环节，因为没有获取到专业组 [{}] 信息", barcode, groupCode);
                return Map.of();
            }
            applySamples = applySamples.stream().filter(e -> Objects.equals(e.getGroupId(), professionalGroupDto.getGroupId())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(applySamples)) {
            log.error("条码 [{}] 无法记录IT3000扫描到条码的条码环节，因为没有获取到专业组 [{}] 下的申请单样本", barcode, groupCode);
            return Map.of();
        }

        sampleFlowService.addSampleFlows(applySamples.stream().map(e -> SampleFlowDto.builder()
                .applyId(e.getApplyId())
                .sampleFlowId(snowflakeService.genId())
                .applySampleId(e.getApplySampleId())
                .operateCode(StringUtils.isBlank(groupCode) ? "Roche前处理分血仪扫描" :BarcodeFlowEnum.IT3000SEEN.name())
                .operateName(StringUtils.isBlank(groupCode) ? "Roche前处理分血仪扫描" : BarcodeFlowEnum.IT3000SEEN.getDesc())
                .barcode(e.getBarcode())
                .content(String.format("扫描到条码 位置信息 [%s] 分区仪器编码 [%s]", position, instrumentCode))
                .build()).collect(Collectors.toList()));

        log.info("条码 [{}] Roche 扫描到条码，记录条码环节成功", barcode);

        return Map.of();
    }


    @Override
    public IT3000HandleVo.Action action() {
        return IT3000HandleVo.Action.SEEN;
    }


}
