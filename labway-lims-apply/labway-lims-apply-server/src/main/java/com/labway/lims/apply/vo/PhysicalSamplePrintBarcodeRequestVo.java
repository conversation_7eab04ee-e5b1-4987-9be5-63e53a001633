package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 体检样本 打印条码 请求 Vo
 * 
 * <AUTHOR>
 * @since 2023/4/4 13:58
 */
@Getter
@Setter
public class PhysicalSamplePrintBarcodeRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 体检人
     */
    private Long physicalRegisterId;
    /**
     * 采样时间
     */
    private Date samplingDate;
    /**
     * 要做的体检套餐
     */
    private List<PhysicalSamplePackageItem> packageItemList;

    /**
     * 选择 体检套餐 信息
     */
    @Getter
    @Setter
    public static class PhysicalSamplePackageItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 体检套餐ID
         */
        private Long physicalGroupPackageId;

        /**
         * 套餐说明
         */
        private String testPackageDesc;
    }
}
