package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 外送分拣
 */
@Getter
@Setter
public class OutsourcingPickVo {
    /**
     * 外送机构
     */
    private String exportOrgName;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 检验项目
     */
    private List<String> testItemNames;


    /**
     * 病人名称
     */
    private String patientName;


    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;


    /**
     * 性别  性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;


    /**
     * 就诊类型
     */
    private String applyTypeName;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 外送分拣日期
     */
    private Date outsourcingPickDate;

    /**
     * 管型
     */
    private String tubeName;
    /**
     * 管型CODE
     */
    private String tubeCode;

    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

}
