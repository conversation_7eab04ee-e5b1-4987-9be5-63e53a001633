package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 分拣且未下架的样本
 */
@Getter
@Setter
public class OnePickSamplesVo {
    /**
     * 名称
     */
    private String patientName;
    /**
     * 年龄
     */
    private Integer patientAge;
    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;
    /**
     * 子年龄
     */
    private Integer patientSubage;
    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 管型名称
     */
    private String tubeName;
    /**
     * 管型编码
     */
    private String tubeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;

    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 检验项目名称
     */
    private List<String> testItemNames;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;


    /**
     * 试管架ID
     */
    private Long rackId;

    /**
     * 逻辑试管架ID
     */
    private Long rackLogicId;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private Long groupName;


    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 行数
     */
    private Integer row;

    /**
     * 列数
     */
    private Integer column;

}
