package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.enums.RackStatusEnum;
import com.labway.lims.base.api.service.RackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 删除逻辑试管架占用
 */
@Slf4j
@Component
class MultiTwoPickRemoveRackSpaceCommand implements Command {

    @Resource
    private RackLogicSpaceService rackLogicSpaceService;

    @DubboReference
    private RackService rackService;

    @DubboReference
    RackLogicService rackLogicService;

    @Override
    public boolean execute(Context c) throws Exception {

        final MultiTwoPickContext context = MultiTwoPickContext.from(c);

        final Set<Long> ids = new HashSet<>();

        for (ApplySampleTwoPickDto e : context.getApplySampleTwoPicks()) {
            ids.add(e.getApplySampleId());
        }

        rackLogicSpaceService.deleteByApplySampleIds(ids);


        // 以下代码是当试管架状态为结束时，自动回收试管架
        final Long rackId = context.getRackLogic().getRackId();
        log.info("开始判断是否走试管架回收【{}】",rackId);
        if(Objects.isNull(rackId) || Objects.equals(rackId, NumberUtils.LONG_ZERO)){
            return CONTINUE_PROCESSING;
        }

        // 物理试管架对应所有的逻辑试管架
        List<RackLogicDto> rackLogicDtos = rackLogicService.selectByRackId(rackId);


        if (CollectionUtils.isNotEmpty(rackLogicDtos)) {
            // 自身的逻辑试管架
            Long rackLogicId = context.getRackLogic().getRackLogicId();

            // 结束状态的试管架ID
            // 试管架回收逻辑：1.排除自身逻辑试管架ID 2.其他的试管架的position都结束了，才能回收
            //
            boolean isRecycle = rackLogicDtos.stream().filter(v -> !Objects.equals(v.getRackLogicId(), rackLogicId))
                    .allMatch(e -> Objects.equals(e.getPosition(), RackLogicPositionEnum.END.getCode()));

            // 实际样本，可能加急拆样本 （组件交接不在此列，为未分拣状态）
            final Set<String> applySampleTwoPicksBarcodeSet = context.getApplySampleTwoPicks().stream().map(ApplySampleTwoPickDto::getBarcode).collect(Collectors.toSet());
            // 当前试管架上的样本
            final Set<String> rackLogicTwoPicksBarcodeSet = context.getApplySamples().stream().map(ApplySampleDto::getBarcode).collect(Collectors.toSet());
            // 如果实际分拣数量小于试管架上的样本，那表示分拣过程中有错误。不修改试管架状态
            if (isRecycle && (applySampleTwoPicksBarcodeSet.size() == rackLogicTwoPicksBarcodeSet.size())) {
                log.info("开始试管架自动回收【{}】",rackId);

                final RackDto rackDto = new RackDto();
                rackDto.setStatus(RackStatusEnum.IDLE.getCode());
                // 更新物理试管架状态为空闲
                rackService.updateByRackIds(rackDto, Collections.singleton(rackId));

                // 删除逻辑试管架
                rackLogicService.deleteByRackIds(Collections.singleton(rackId));
            }
        }

        return CONTINUE_PROCESSING;
    }


}
