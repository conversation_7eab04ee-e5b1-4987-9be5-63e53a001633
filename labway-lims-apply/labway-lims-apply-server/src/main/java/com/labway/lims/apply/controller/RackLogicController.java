package com.labway.lims.apply.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.RackLogicArchivePositionEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.RackTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.vo.RackRecycleResponseVo;
import com.labway.lims.apply.vo.RackSampleDetailResponseVo;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.enums.RackStatusEnum;
import com.labway.lims.base.api.service.RackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 逻辑试管架 API
 *
 * <AUTHOR>
 * @since 2023/4/6 16:10
 */
@Slf4j
@RestController
@RequestMapping("/rack-logic")
public class RackLogicController extends BaseController {

    @DubboReference
    private RackLogicService rackLogicService;

    @DubboReference
    private RackService rackService;

    @Resource
    private ApplySampleService applySampleService;

    @DubboReference
    private ApplyService applyService;

    @DubboReference
    private RackLogicSpaceService rackLogicSpaceService;

    /**
     * 试管架回收 左侧试管架列表
     */
    @PostMapping("/select-all")
    public Object rackList() {
        List<RackDto> rackDtos = rackService.selectAll(LoginUserHandler.get().getOrgId(),false);
        // 过滤试管架为启用状态的
        rackDtos = rackDtos.stream().filter(obj -> Objects.equals(obj.getEnable(), YesOrNoEnum.YES.getCode()))
            .collect(Collectors.toList());
        List<Long> rackIds = rackDtos.stream().map(RackDto::getRackId).collect(Collectors.toList());

        // 对应逻辑试管架
        List<RackLogicDto> rackLogicDtos = rackLogicService.selectByRackIds(rackIds);
        // 转map key: 试管架code value:逻辑试管架中 所在环节最小项
        Map<String,
            RackLogicDto> rackLogicDtoByRackCode = rackLogicDtos.stream()
                .collect(Collectors.groupingBy(RackLogicDto::getRackCode,
                    Collectors.collectingAndThen(Collectors.minBy(Comparator.comparing(RackLogicDto::getPosition)),
                        optional -> optional.orElse(null))));

        List<RackRecycleResponseVo> targetList = Lists.newArrayListWithCapacity(rackDtos.size());
        for (RackDto item : rackDtos) {
            RackRecycleResponseVo temp = JSON.parseObject(JSON.toJSONString(item), RackRecycleResponseVo.class);

            // 对应逻辑试管架
            RackLogicDto rackLogicDto =
                ObjectUtils.defaultIfNull(rackLogicDtoByRackCode.get(item.getRackCode()), new RackLogicDto());
            temp.setPosition(rackLogicDto.getPosition());
            String positionMeaning;
            // 归档类型试管架
            if (RackTypeEnum.isArchiveRack(temp.getRackTypeCode())) {
                positionMeaning = RackLogicArchivePositionEnum.getByCode(ObjectUtils.defaultIfNull(rackLogicDto.getPosition(),
                    RackLogicArchivePositionEnum.UNKNOWN.getCode())).getDesc();
            } else {
                positionMeaning = RackLogicPositionEnum
                    .getByCode(
                        ObjectUtils.defaultIfNull(rackLogicDto.getPosition(), RackLogicPositionEnum.UNKNOWN.getCode()))
                    .getDesc();
            }
            temp.setPositionMeaning(positionMeaning);

            if (Objects.equals(item.getStatus(), RackStatusEnum.IDLE.getCode())) {
                temp.setPositionMeaning("空闲");
            }

            temp.setCurrentGroupId(rackLogicDto.getCurrentGroupId());
            temp.setCurrentGroupName(rackLogicDto.getCurrentGroupName());
            temp.setCurrenHandover(rackLogicDto.getLastHandover());

            targetList.add(temp);
        }
        return targetList;
    }

    /**
     * 试管架 样本详情
     */
    @PostMapping("/sample-details")
    public Object rackSampleDetails(@RequestParam("rackId") long rackId) {
        RackDto rackDto = rackService.selectByRackId(rackId);
        if (Objects.isNull(rackDto)) {
            throw new LimsException("对应试管架不存在");
        }
        // 对应逻辑试管架
        List<RackLogicDto> rackLogicDtos = rackLogicService.selectByRackId(rackDto.getRackId());
        List<Long> rackLogicIds = rackLogicDtos.stream().map(RackLogicDto::getRackLogicId).collect(Collectors.toList());

        List<RackLogicApplySampleDto> rackLogicApplySampleDtos = Lists.newArrayList();

        if (RackTypeEnum.isArchiveRack(rackDto.getRackTypeCode())) {
            // 归档类型试管架 只有一个 逻辑试管架
            List<RackLogicSpaceDto> rackLogicSpaceDtos = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(rackLogicIds)) {
                rackLogicSpaceDtos =
                    rackLogicSpaceService.selectByRackLogicId(rackLogicIds.get(NumberUtils.INTEGER_ZERO));
            }
            final Set<Long> applySampleIds =
                rackLogicSpaceDtos.stream().map(RackLogicSpaceDto::getApplySampleId).collect(Collectors.toSet());
            List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);
            Map<Long, ApplySampleDto> applySampleByApplySampleId = applySampleDtos.stream()
                .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));
            for (RackLogicSpaceDto rackLogicSpaceDto : rackLogicSpaceDtos) {
                ApplySampleDto applySampleDto = applySampleByApplySampleId.get(rackLogicSpaceDto.getApplySampleId());
                if (Objects.isNull(applySampleDto)) {
                    continue;
                }
                RackLogicApplySampleDto sampleDto = new RackLogicApplySampleDto();
                BeanUtils.copyProperties(applySampleDto, sampleDto);
                sampleDto.setRackId(rackLogicSpaceDto.getRackId());
                sampleDto.setRackLogicId(rackLogicSpaceDto.getRackLogicId());
                sampleDto.setPosition(rackLogicDtos.get(NumberUtils.INTEGER_ZERO).getPosition());
                sampleDto.setRackCode(rackLogicDtos.get(NumberUtils.INTEGER_ZERO).getRackCode());
                sampleDto.setRow(rackLogicSpaceDto.getRow());
                sampleDto.setColumn(rackLogicSpaceDto.getColumn());
                rackLogicApplySampleDtos.add(sampleDto);
            }
        } else {
            // 非归档试管架
            rackLogicApplySampleDtos = applySampleService.selectByRackLogicIds(rackLogicIds);

            // 只查询 非二次分拣 已录入状态
            rackLogicApplySampleDtos = rackLogicApplySampleDtos.stream()
                .filter(obj -> Objects.equals(obj.getIsTwoPick(), YesOrNoEnum.NO.getCode()))
                .filter(obj -> Objects.equals(obj.getStatus(), SampleStatusEnum.ENTER.getCode()))
                .collect(Collectors.toList());
        }

        // 所有样本对应 申请单
        Set<Long> applyIds =
            rackLogicApplySampleDtos.stream().map(RackLogicApplySampleDto::getApplyId).collect(Collectors.toSet());
        List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);
        Map<Long, ApplyDto> applyDtoByApplyId =
            applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));
        List<RackSampleDetailResponseVo> targetList = Lists.newArrayListWithCapacity(rackLogicApplySampleDtos.size());
        ApplyDto applyDtoWhenNull = new ApplyDto();
        for (RackLogicApplySampleDto item : rackLogicApplySampleDtos) {
            RackSampleDetailResponseVo temp = new RackSampleDetailResponseVo();

            // 样本 对应申请单
            ApplyDto applyDto = ObjectUtils.defaultIfNull(applyDtoByApplyId.get(item.getApplyId()), applyDtoWhenNull);
            temp.setApplyId(item.getApplyId());
            temp.setPatientName(applyDto.getPatientName());
            temp.setPatientAge(applyDto.getPatientAge());
            temp.setPatientSex(applyDto.getPatientSex());
            temp.setHspOrgId(applyDto.getHspOrgId());
            temp.setHspOrgName(applyDto.getHspOrgName());

            // 样本信息
            temp.setApplySampleId(item.getApplySampleId());
            temp.setBarcode(item.getBarcode());
            temp.setTubeName(item.getTubeName());
            temp.setSampleType(item.getSampleTypeName());
            temp.setRow(item.getRow());
            temp.setColumn(item.getColumn());

            targetList.add(temp);
        }
        return targetList;
    }

    /**
     * 试管架 回收
     */
    @PostMapping("/rack-recycle")
    public Object rackRecycle(@RequestBody Set<Long> rackIds) {
        List<RackDto> rackDtos = rackService.selectByRackIds(rackIds);

        List<Long> rackIdsTemp = rackDtos.stream().map(RackDto::getRackId).collect(Collectors.toList());

        // 对应逻辑试管架
        List<RackLogicDto> rackLogicDtos = rackLogicService.selectByRackIds(rackIdsTemp);
        // map key: 试管架编码 value:对应逻辑试管架
        Map<String, List<RackLogicDto>> rackLogicByRackId =
            rackLogicDtos.stream().collect(Collectors.groupingBy(RackLogicDto::getRackCode));
        // 过滤的 试管架状态不全为 90 的 试管架
        List<String> filterRackCodes = rackLogicByRackId.entrySet().stream().filter(obj -> {
            List<RackLogicDto> value = obj.getValue();
            return !value.stream()
                .allMatch(item -> Objects.equals(item.getPosition(), RackLogicPositionEnum.END.getCode()));
        }).map(Map.Entry::getKey).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(filterRackCodes)) {
            throw new IllegalStateException("试管架环节未结束，无法回收");
        }

        if (CollectionUtils.isEmpty(rackDtos)) {
            throw new IllegalStateException("无可回收的试管架");
        }

        rackService.rackRecycle(rackDtos.stream().map(RackDto::getRackId).collect(Collectors.toSet()));

        return Collections.emptyMap();
    }

}
