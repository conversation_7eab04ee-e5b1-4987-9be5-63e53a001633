package com.labway.lims.apply.bo;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 一次分拣完待交接的试管架
 */
@Getter
@Setter
public class OnePickWaitingHandoverRackLogicBo {
    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 试管架ID
     */
    private Long rackId;

    /**
     * 逻辑试管架ID
     */
    private Long rackLogicId;

    /**
     * 试管架编码
     */
    private String rackCode;


    /**
     * 专业组ID
     */
    private Long nextGroupId;

    /**
     * 专业组名称
     */
    private String nextGroupName;

    /**
     * 一次分拣人
     */
    private String onePickerName;

    /**
     * 一次分拣时间
     */
    private Date onePickDate;

    /**
     * 试管架的更新日期
     */
    private Date updateDate;

    /**
     * 是否禁用 1是 0 否
     *
     * @see YesOrNoEnum
     */
    private Integer isDisabled;

}
