
package com.labway.lims.apply.service.chain.splitblood;

import com.labway.lims.apply.api.service.ApplySampleService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 删除之前的样本，因为分血会重新生成
 *
 * <AUTHOR>
 */
@Component
public class DeleteApplySampleCommand implements Command, Filter {

    @Resource
    private ApplySampleService applySampleService;


    @Override
    public boolean execute(Context c) throws Exception {
        final SplitBloodContext context = SplitBloodContext.from(c);

        // 不支持分血 那么不删除申请单样本
        if (!context.isSupportedSplitBlood()) {
            return CONTINUE_PROCESSING;
        }

        applySampleService.deleteByApplySampleId(context.getApplySampleId());

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
