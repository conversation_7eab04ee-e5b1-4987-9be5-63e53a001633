package com.labway.lims.apply.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.QueryLimsBingLiReportDto;
import com.labway.business.center.compare.request.QueryLimsBingLiReportRequest;
import com.labway.business.center.compare.service.OutApplyInfoService;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.apply.api.dto.PathologyReportItemResponseDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SelectPathologyReportListRequestDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.PathologyReportService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <pre>
 * PathologyReportServiceImpl
 * 病理报告单服务
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/2/4 15:59
 */
@Slf4j
@DubboService
public class PathologyReportServiceImpl implements PathologyReportService {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @Resource
    private OutApplyInfoService outApplyInfoService;
    // 病理检验机构编码
    @Value("${bingli.testOrgId:GZLWLJPIS}")
    private String bingliTestOrgId;
    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Override
    public List<PathologyReportItemResponseDto> selectPathologyReportList(SelectPathologyReportListRequestDto requestDto) {

        // 从业务中台查询病理结果
        QueryLimsBingLiReportRequest bingLiReportRequest = new QueryLimsBingLiReportRequest();
        bingLiReportRequest.setTestOrgId(bingliTestOrgId);
        bingLiReportRequest.setSignTimeBegin(requestDto.getBeginCheckInDate());
        bingLiReportRequest.setSignTimeEnd(requestDto.getEndCheckInDate());
        bingLiReportRequest.setAuditTimeBegin(requestDto.getBeginCheckDate());
        bingLiReportRequest.setAuditTimeEnd(requestDto.getEndCheckDate());
        bingLiReportRequest.setHspOrgCode(requestDto.getHspOrgCode());
        bingLiReportRequest.setDeptName(requestDto.getDept());
        bingLiReportRequest.setPatientName(requestDto.getPatientName());
        bingLiReportRequest.setSex(Objects.nonNull(requestDto.getPatientSex()) ? SexEnum.getByCode(requestDto.getPatientSex()).getDesc() : null);
        bingLiReportRequest.setPatientTypes(new ArrayList<>(requestDto.getApplyTypes())); // 就诊类型
        bingLiReportRequest.setPatientNo(requestDto.getPatientVisitCard());
        bingLiReportRequest.setPathologyNo(requestDto.getPathologyNo());
        bingLiReportRequest.setOutTestItemName(requestDto.getPathologyType()); // 病理类型
        if (CollectionUtils.isNotEmpty(requestDto.getBarcodes())) {
            bingLiReportRequest.setBarcodes(new ArrayList<>(requestDto.getBarcodes()));
        }

        Response<?> response = outApplyInfoService.queryLimsBingLiReport(bingLiReportRequest);

        if (!response.isSuccess()) {
            log.error("查询病理报告单失败, request: {}, response: {}", bingLiReportRequest, response);
            throw new IllegalStateException("查询病理报告单失败");
        }

        List<QueryLimsBingLiReportDto> bingLiReportDtos = JSON.parseArray(JSON.toJSONString(response.getData()), QueryLimsBingLiReportDto.class);

        // 查询送检机构信息
        List<String> hspOrgCodes = bingLiReportDtos.stream().map(QueryLimsBingLiReportDto::getAppOrgId).distinct().collect(Collectors.toList());
        Map<String, HspOrganizationDto> hspOrgMap = hspOrganizationService.selectByHspOrgCodes(hspOrgCodes)
                .stream().collect(Collectors.toMap(HspOrganizationDto::getHspOrgCode, Function.identity(), (a, b) -> a));

        // 根据条码号从LIMS查询信息
        Set<String> barcodes = bingLiReportDtos.stream().map(QueryLimsBingLiReportDto::getfBl8)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        final Map<String, ? extends BaseSampleEsModelDto> sampleMap;
        final Map<String, ? extends BaseSampleEsModelDto> sampleMapByOutBarcodes;
        if (CollectionUtils.isNotEmpty(barcodes)) {
            // 查询结果 - 根据外部条码号
            final List<BaseSampleEsModelDto> baseSampleEsModelDtos =
                    elasticSearchSampleService.selectSamples(SampleEsQuery.builder().barcodes(barcodes).build());
            sampleMap = baseSampleEsModelDtos.stream()
                    .collect(Collectors.toMap(BaseSampleEsModelDto::getBarcode, Function.identity(), (a, b) -> a));
            // 查询结果 - 根据实验室条码号查询
            sampleMapByOutBarcodes = elasticSearchSampleService.selectSamples(SampleEsQuery.builder().outBarcodes(barcodes).build()).stream()
                    .collect(Collectors.toMap(BaseSampleEsModelDto::getOutBarcode, Function.identity(), (a, b) -> a));
        } else {
            sampleMap = Collections.emptyMap();
            sampleMapByOutBarcodes = Collections.emptyMap();
        }

        List<PathologyReportItemResponseDto> reportItemResponseDtos = bingLiReportDtos.stream()
                .filter(e -> Objects.nonNull(sampleMap.get(e.getfBl8())) || Objects.nonNull(sampleMapByOutBarcodes.get(e.getfBl8())))
                .map(e -> {
                    PathologyReportItemResponseDto responseDto = new PathologyReportItemResponseDto();

                    BaseSampleEsModelDto sampleEsModelDto = ObjectUtils.defaultIfNull(sampleMap.get(e.getfBl8()), sampleMapByOutBarcodes.get(e.getfBl8()));

                    if (Objects.isNull(sampleEsModelDto)) {
                        log.error("查询样本信息失败, barcode: {}", e.getfBl8());
                        sampleEsModelDto = new BaseSampleEsModelDto();
                    }

                    responseDto.setApplySampleId(sampleEsModelDto.getApplySampleId());
                    responseDto.setPatientName(e.getfXm()); // 患者姓名
                    responseDto.setPatientSex(SexEnum.getByDesc(e.getfXb()).getCode()); // 患者性别
                    responseDto.setPatientAge(sampleEsModelDto.getPatientAge());
                    responseDto.setPatientSubage(sampleEsModelDto.getPatientSubage());
                    responseDto.setPatientSubageUnit(sampleEsModelDto.getPatientSubageUnit());
                    responseDto.setHspOrgCode(e.getAppOrgId());
                    responseDto.setHspOrgName(hspOrgMap.getOrDefault(e.getAppOrgId(), new HspOrganizationDto()).getHspOrgName());
                    responseDto.setBarcode(e.getfBl8()); // 样本条码
                    responseDto.setPathologyNo(e.getfBlh()); // 病理号
                    responseDto.setPathologyType(e.getfYzxm()); // 病理类型
                    responseDto.setApplyType(e.getfBrlb()); // 就诊类型
                    responseDto.setDept(e.getfSjks()); // 科室
                    responseDto.setPatientVisitCard(StringUtils.isBlank(e.getfMzh()) ? e.getfZyh() : e.getfMzh()); // 门诊|住院号
                    responseDto.setPatientBed(e.getfCh()); // 床号
                    responseDto.setDiagnosis(e.getfLczd()); // 临床诊断
                    responseDto.setResultRemark(e.getfBz()); // 结果备注
                    responseDto.setIsPrint(sampleEsModelDto.getIsPrint()); // 是否打印
                    responseDto.setCheckInDate(StringUtils.isNotBlank(e.getfSdrq()) ? DateUtil.parseDate(e.getfSdrq()) : null); // 登记日期
                    responseDto.setSamplingDate(sampleEsModelDto.getSamplingDate()/*StringUtils.isNotBlank(e.getfQcrq()) ? DateUtil.parseDate(e.getfQcrq()) : null*/); // 采样时间
                    responseDto.setReportDate(StringUtils.isNotBlank(e.getfSpare5()) ? DateUtil.parseDate(e.getfSpare5()) : null); // 审核时间
                    responseDto.setGroupId(sampleEsModelDto.getGroupId());
                    responseDto.setGroupName(sampleEsModelDto.getGroupName());
                    responseDto.setReportDoctorName(e.getfBgys()); // 报告医师
                    responseDto.setReauditDoctorName(e.getfFzys()); // 复审医师
                    responseDto.setCreateDate(sampleEsModelDto.getCreateDate()); // 创建时间
                    responseDto.setReport(true);//默认已报告
                    responseDto.setReports(e.getPdfFiles().stream().map(pdf -> {
                        BaseSampleEsModelDto.Report report = new BaseSampleEsModelDto.Report();
                        report.setUrl(pdf.getReportUrl());
                        return report;
                    }).collect(Collectors.toList()));

                    return responseDto;
                }).filter(e -> Objects.isNull(requestDto.getIsPrint()) || requestDto.getIsPrint().equals(e.getIsPrint())).collect(Collectors.toList());

        return reportItemResponseDtos;
    }

}
