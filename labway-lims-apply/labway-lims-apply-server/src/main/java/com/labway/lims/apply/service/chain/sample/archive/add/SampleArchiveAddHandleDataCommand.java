package com.labway.lims.apply.service.chain.sample.archive.add;

import com.labway.lims.api.enums.apply.OperationTypeEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.RackArchiveDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackArchiveService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.enums.RackStatusEnum;
import com.labway.lims.base.api.service.RackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 样本归档-样本存储 处理相关数据
 * 
 * <AUTHOR>
 * @since 2023/4/17 14:07
 */
@Slf4j
@Component
public class SampleArchiveAddHandleDataCommand implements Command {

    @DubboReference
    private RackService rackService;
    @DubboReference
    private RackLogicService rackLogicService;

    @DubboReference
    private RackLogicSpaceService rackLogicSpaceService;

    @DubboReference
    private RackArchiveService rackArchiveService;
    @Resource
    private ApplySampleService applySampleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean execute(Context context) throws Exception {
        final SampleArchiveAddContext from = SampleArchiveAddContext.from(context);

        RackLogicDto rackLogic = from.getRackLogic();
        boolean needAddRackLogic = from.needAddRackLogic();
        if (needAddRackLogic) {
            // 对应归档逻辑试管架不存在 需要新增
            rackLogicService.addRackLogic(rackLogic);
        }
        OperationTypeEnum rackArchiveFlag = from.getRackArchiveFlag();
        RackArchiveDto rackArchive = from.getRackArchive();
        if (Objects.equals(OperationTypeEnum.CREATE, rackArchiveFlag)) {
            // 需要新增 归档试管架信息
            rackArchiveService.addRackArchive(rackArchive);
        } else if (Objects.equals(OperationTypeEnum.UPDATE, rackArchiveFlag)) {
            // 需要修改 归档试管架信息
            rackArchiveService.updateByRackArchive(rackArchive);
        }

        // 新增 逻辑试管架空间占用
        RackLogicSpaceDto rackLogicSpace = from.getRackLogicSpace();
        rackLogicSpaceService.addRackLogicSpace(rackLogicSpace);

        // 修改样本状态 为已归档
        ApplySampleDto applySample = from.getApplySample();
        ApplySampleDto update = new ApplySampleDto();
        update.setApplySampleId(applySample.getApplySampleId());
        update.setIsArchive(1);
        applySampleService.updateByApplySampleId(update);

        // 修改试管架占用状态
        RackDto rackDto = new RackDto();
        rackDto.setStatus(RackStatusEnum.ACTIVE.getCode());
        rackDto.setRackId(rackLogic.getRackId());
        rackService.updateByRackId(rackDto);

        return CONTINUE_PROCESSING;
    }
}
