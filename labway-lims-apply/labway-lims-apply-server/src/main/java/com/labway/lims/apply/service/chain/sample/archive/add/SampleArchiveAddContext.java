package com.labway.lims.apply.service.chain.sample.archive.add;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.OperationTypeEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.RackArchiveDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.apply.vo.RackArchiveAddRequestVo;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.RefrigeratorDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

/**
 * 样本归档 -存储 信息
 *
 * <AUTHOR>
 * @since 2023/4/14 11:42
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class SampleArchiveAddContext extends StopWatchContext {

    /**
     * 样本 归档 存储 参数
     */
    private RackArchiveAddRequestVo rackArchiveAddRequestVo;

    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    // 对应物理试管架
    public static final String RACK = "RACK_" + IdUtil.objectId();
    // 对应逻辑试管架
    public static final String RACK_LOGIC = "RACK_LOGIC_" + IdUtil.objectId();
    // 对应冰箱
    public static final String REFRIGERATOR = "REFRIGERATOR_" + IdUtil.objectId();
    // 申请单样本
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    // 对应试管架归档信息
    public static final String RACK_ARCHIVE = "RACK_ARCHIVE_" + IdUtil.objectId();

    // 归档逻辑试管架是否新增
    public static final String RACK_LOGIC_ADD_FLAG = "RACK_LOGIC_ADD_FLAG_" + IdUtil.objectId();

    // 试管架归档信息 flag
    public static final String RACK_ARCHIVE_FLAG = "RACK_ARCHIVE_FLAG_" + IdUtil.objectId();

    // 逻辑试管架空间占用
    public static final String RACK_LOGIC_SPACE = "RACK_LOGIC_SPACE_" + IdUtil.objectId();

    /**
     * 获取 样本归档-存储 信息参数 从上下文中
     */
    public static SampleArchiveAddContext from(Context context) {
        return (SampleArchiveAddContext)context;
    }

    public boolean needAddRackLogic() {
        return (boolean)get(RACK_LOGIC_ADD_FLAG);
    }

    public RackLogicDto getRackLogic() {
        return (RackLogicDto)get(RACK_LOGIC);
    }

    public RackDto getRack() {
        return (RackDto)get(RACK);
    }

    public ApplySampleDto getApplySample() {
        return (ApplySampleDto)get(APPLY_SAMPLE);
    }

    public RefrigeratorDto getRefrigerator() {
        return (RefrigeratorDto)get(REFRIGERATOR);
    }

    public OperationTypeEnum getRackArchiveFlag() {
        return (OperationTypeEnum)get(RACK_ARCHIVE_FLAG);
    }

    public RackArchiveDto getRackArchive() {
        return (RackArchiveDto)get(RACK_ARCHIVE);
    }

    public RackLogicSpaceDto getRackLogicSpace() {
        return (RackLogicSpaceDto)get(RACK_LOGIC_SPACE);
    }

    @Override
    protected String getWatcherName() {
        return "样本归档存储";
    }
}
