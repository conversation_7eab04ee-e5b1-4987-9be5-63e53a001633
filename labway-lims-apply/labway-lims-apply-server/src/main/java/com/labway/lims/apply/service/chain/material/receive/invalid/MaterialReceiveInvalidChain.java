package com.labway.lims.apply.service.chain.material.receive.invalid;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 物料 登记 作废 责任链
 *
 * <AUTHOR>
 * @since 2023/5/9 10:02
 */
@Component
public class MaterialReceiveInvalidChain extends ChainBase implements InitializingBean {

    @Resource
    private MaterialReceiveInvalidCheckParamCommand materialReceiveInvalidCheckParamCommand;
    @Resource
    private MaterialReceiveInvalidUpdateReceiveRecordCommand materialReceiveInvalidUpdateReceiveRecordCommand;
    @Resource
    private MaterialReceiveInvalidAddMaterialInventoryCommand materialReceiveInvalidAddMaterialInventoryCommand;
    @Resource
    private MaterialReceiveInvalidAddGroupMaterialCommand materialReceiveInvalidAddGroupMaterialCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 检查 参数
        addCommand(materialReceiveInvalidCheckParamCommand);

        // 修改 领用记录状态,作废人，作废时间
        addCommand(materialReceiveInvalidUpdateReceiveRecordCommand);

        // 修改 库存 物料 数量
        addCommand(materialReceiveInvalidAddMaterialInventoryCommand);

        // 修改 专业组 物料总库存
        addCommand(materialReceiveInvalidAddGroupMaterialCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);
    }
}
