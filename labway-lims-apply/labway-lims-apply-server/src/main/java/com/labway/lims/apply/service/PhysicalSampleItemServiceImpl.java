package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.PhysicalSampleItemDto;
import com.labway.lims.apply.api.service.PhysicalSampleItemService;
import com.labway.lims.apply.mapper.TbPhysicalSampleItemMapper;
import com.labway.lims.apply.mapstruct.PhysicalSampleItemConverter;
import com.labway.lims.apply.model.TbPhysicalSampleItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 体检样本项目 Service impl
 *
 * <AUTHOR>
 * @since 2023/4/4 17:12
 */
@Slf4j
@DubboService
public class PhysicalSampleItemServiceImpl implements PhysicalSampleItemService {

    @Resource
    private TbPhysicalSampleItemMapper tbPhysicalSampleItemMapper;

    @Resource
    private PhysicalSampleItemConverter physicalSampleItemConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPhysicalSampleItems(List<PhysicalSampleItemDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 体检样本项目
        List<TbPhysicalSampleItem> targetList =
            list.stream().map(obj -> JSON.parseObject(JSON.toJSONString(obj), TbPhysicalSampleItem.class))
                .collect(Collectors.toList());
        // 数量 分区批次插入
        List<List<TbPhysicalSampleItem>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbPhysicalSampleItemMapper.batchAddPhysicalSampleItems(item));

        log.info("用户 [{}] 新增体检样本项目[{}]成功", loginUser.getNickname(), JSON.toJSONString(targetList));
    }

    @Override
    public List<PhysicalSampleItemDto> selectByPhysicalSampleIds(Collection<Long> physicalSampleIds) {
        if (CollectionUtils.isEmpty(physicalSampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbPhysicalSampleItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbPhysicalSampleItem::getPhysicalSampleId, physicalSampleIds);
        queryWrapper.eq(TbPhysicalSampleItem::getIsDelete, YesOrNoEnum.NO.getCode());
        return physicalSampleItemConverter
            .fromTbPhysicalSampleItemList(tbPhysicalSampleItemMapper.selectList(queryWrapper));
    }

    @Override
    public List<PhysicalSampleItemDto> selectByPhysicalSampleId(long physicalSampleId) {
        LambdaQueryWrapper<TbPhysicalSampleItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbPhysicalSampleItem::getPhysicalSampleId, physicalSampleId);
        queryWrapper.eq(TbPhysicalSampleItem::getIsDelete, YesOrNoEnum.NO.getCode());
        return physicalSampleItemConverter
            .fromTbPhysicalSampleItemList(tbPhysicalSampleItemMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteById(Collection<Long> physicalSampleItemIds) {
        if(CollectionUtils.isEmpty(physicalSampleItemIds)){
            return 0;
        }
        return tbPhysicalSampleItemMapper.deleteBatchIds(physicalSampleItemIds);
    }

}
