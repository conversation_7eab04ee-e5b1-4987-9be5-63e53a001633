package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.PhysicalRegisterDto;
import com.labway.lims.apply.api.service.PhysicalRegisterService;
import com.labway.lims.apply.mapper.TbPhysicalRegisterMapper;
import com.labway.lims.apply.mapstruct.PhysicalRegisterConverter;
import com.labway.lims.apply.model.TbPhysicalRegister;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 体检花名册 Service impl
 * 
 * <AUTHOR>
 * @since 2023/3/30 15:43
 */
@Slf4j
@DubboService
public class PhysicalRegisterServiceImpl implements PhysicalRegisterService {

    @Resource
    private TbPhysicalRegisterMapper tbPhysicalRegisterMapper;
    @Resource
    private PhysicalRegisterConverter physicalRegisterConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPhysicalRegisters(List<PhysicalRegisterDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 体检花名册
        List<TbPhysicalRegister> targetList = physicalRegisterConverter.tbPhysicalRegisterListFromTbDtoList(list);

        // 数量 分区批次插入
        List<List<TbPhysicalRegister>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbPhysicalRegisterMapper.batchAddPhysicalRegisters(item));

        log.info("用户 [{}] 新增体检花名册[{}]成功", loginUser.getNickname(), JSON.toJSONString(targetList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPhysicalBatchId(long physicalBatchId) {

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        LambdaUpdateWrapper<TbPhysicalRegister> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(TbPhysicalRegister::getIsDelete, YesOrNoEnum.YES.getCode());
        updateWrapper.eq(TbPhysicalRegister::getPhysicalBatchId, physicalBatchId);
        updateWrapper.eq(TbPhysicalRegister::getIsDelete, YesOrNoEnum.NO.getCode());

        log.info("用户 [{}] 删除体检花名册成功 [{}] 结果 [{}]", loginUser.getNickname(), physicalBatchId,
            tbPhysicalRegisterMapper.update(null, updateWrapper) > 0);

    }

    @Override
    public List<PhysicalRegisterDto> selectByPhysicalBatchId(long physicalBatchId) {

        LambdaQueryWrapper<TbPhysicalRegister> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbPhysicalRegister::getPhysicalBatchId, physicalBatchId);
        queryWrapper.eq(TbPhysicalRegister::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByDesc(TbPhysicalRegister::getCreateDate);
        return physicalRegisterConverter.fromTbPhysicalRegisterList(tbPhysicalRegisterMapper.selectList(queryWrapper));
    }

    @Override
    public List<PhysicalRegisterDto> selectByPhysicalRegisterIds(Collection<Long> physicalRegisterIds) {
        if (CollectionUtils.isEmpty(physicalRegisterIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbPhysicalRegister> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbPhysicalRegister::getPhysicalRegisterId, physicalRegisterIds);
        queryWrapper.eq(TbPhysicalRegister::getIsDelete, YesOrNoEnum.NO.getCode());
        return physicalRegisterConverter.fromTbPhysicalRegisterList(tbPhysicalRegisterMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByPhysicalBatchId(PhysicalRegisterDto physicalRegisterDto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbPhysicalRegister target = new TbPhysicalRegister();
        BeanUtils.copyProperties(physicalRegisterDto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (tbPhysicalRegisterMapper.updateById(target) < 1) {
            throw new LimsException("修改体检花名册失败");
        }

        log.info("用户 [{}] 修改体检花名册成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }

    @Nullable
    @Override
    public PhysicalRegisterDto selectByPhysicalRegisterId(long physicalRegisterId) {

        LambdaQueryWrapper<TbPhysicalRegister> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbPhysicalRegister::getPhysicalRegisterId, physicalRegisterId);
        queryWrapper.eq(TbPhysicalRegister::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last("limit 1");

        return physicalRegisterConverter.fromTbPhysicalRegister(tbPhysicalRegisterMapper.selectOne(queryWrapper));
    }

    @Override
    public boolean deleteById(long physicalRegisterId) {
        return tbPhysicalRegisterMapper.deleteById(physicalRegisterId) > 0;
    }

}
