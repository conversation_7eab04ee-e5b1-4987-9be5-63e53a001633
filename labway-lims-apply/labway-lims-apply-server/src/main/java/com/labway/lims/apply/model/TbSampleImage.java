package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * TbSampleImage
 * 样本图片
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/28 9:50
 */
@Getter
@Setter
@TableName("tb_sample_image")
public class TbSampleImage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    private Long sampleImageId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 样本ID
     */
    private Long sampleId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 项目类型
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 图片名称
     */
    private String imageName;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 1:已删除 0：未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新者
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

}
