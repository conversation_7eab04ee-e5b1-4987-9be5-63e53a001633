package com.labway.lims.apply.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 打印的条码信息
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PrintBarcodeVo {
    // 申请单样本id
    private Long applySampleId;
    // 主条码
    private String masterBarcode;
    // 条码
    private String barcode;
    // 姓名
    private String patientName;
    // 专业组
    private String groupName;
    // 性别
    private Integer patientSex;
    // 年龄
    private Integer patientAge;
    // 子年龄
    private Integer patientSubage;
    // 子年龄单位
    private String patientSubageUnit;
    // 检验项目
    private Set<String> testItemNames;
    // 送检机构
    private String hspOrgName;
    // 样本理性
    private String sampleType;
    // 就诊类型
    private String applyType;
    // 就诊卡号
    private String patientVisitCard;
    // 床号
    private String patientBed;
    // 管型
    private String tube;
}
