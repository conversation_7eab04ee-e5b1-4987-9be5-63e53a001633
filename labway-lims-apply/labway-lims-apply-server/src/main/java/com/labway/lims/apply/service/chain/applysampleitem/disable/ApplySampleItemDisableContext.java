package com.labway.lims.apply.service.chain.applysampleitem.disable;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.DisableOrEnableItemDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * ApplySampleItemDisableContext
 * 申请单样本项目禁用/启用
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/12/10 18:36
 */
@Getter
@Setter
public class ApplySampleItemDisableContext extends StopWatchContext {

    /**
     * 申请单样本检验项目 MapByApplySampleId
     */
    public static final String APPLY_SAMPLE_ITEMS_MAP = "APPLY_SAMPLE_ITEMS_MAP_" + IdUtil.objectId();

    /**
     * 申请单样本检验项目 MapByApplySampleItemId
     */
    public static final String APPLY_SAMPLE_ITEM_MAP = "APPLY_SAMPLE_ITEM_MAP_" + IdUtil.objectId();

    /**
     * 申请单样本 MapByApplySampleId
     */
    public static final String APPLY_SAMPLE_MAP = "APPLY_SAMPLE_MAP_" + IdUtil.objectId();

    /**
     * 禁用的项目列表
     */
    public static final String DISABLE_ITEMS = "DISABLE_ITEMS_" + IdUtil.objectId();

    /**
     * 启用的项目列表
     */
    public static final String ENABLE_ITEMS = "ENABLE_ITEMS_" + IdUtil.objectId();

    private DisableOrEnableItemDto disableOrEnableItemDto;

    public static ApplySampleItemDisableContext from(Context context) {
        return (ApplySampleItemDisableContext) context;
    }

    @SuppressWarnings("unchecked")
    public Map<Long, List<ApplySampleItemDto>> getApplySampleItemsMap() {
        return (Map<Long, List<ApplySampleItemDto>>) get(APPLY_SAMPLE_ITEMS_MAP);
    }

    public void setApplySampleItemMap(Map<Long, ApplySampleItemDto> applySampleItemIdsAsMap) {
        put(APPLY_SAMPLE_ITEM_MAP, applySampleItemIdsAsMap);
    }

    @SuppressWarnings("unchecked")
    public Map<Long, ApplySampleItemDto> getApplySampleItemMap() {
        return (Map<Long, ApplySampleItemDto>) get(APPLY_SAMPLE_ITEM_MAP);
    }

    public void setApplySampleMap(Map<Long, ApplySampleDto> applySampleIdsAsMap) {
        put(APPLY_SAMPLE_MAP, applySampleIdsAsMap);
    }

    @SuppressWarnings("unchecked")
    public Map<Long, ApplySampleDto> getApplySampleMap() {
        return (Map<Long, ApplySampleDto>) get(APPLY_SAMPLE_MAP);
    }

    @SuppressWarnings("unchecked")
    public List<ApplySampleItemDto> getDisableItems() {
        return ((List<ApplySampleItemDto>) get(DISABLE_ITEMS));
    }

    @SuppressWarnings("unchecked")
    public List<ApplySampleItemDto> getEnableItems() {
        return ((List<ApplySampleItemDto>) get(ENABLE_ITEMS));
    }

    @Override
    protected String getWatcherName() {
        return "申请单样本项目禁用";
    }

}
