package com.labway.lims.apply.model.es;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * 特检
 */
@Getter
@Setter
public class SpecialtyInspection extends BaseSampleEsModel {

    /**
     * 一次审核
     */
    @Field(type = FieldType.Long)
    private Long oneCheckerId;
    /**
     * 一次审核人名称
     */
    @Field(type = FieldType.Keyword)
    private String oneCheckerName;
    /**
     * 二次审核人
     */
    @Field(type = FieldType.Long)
    private Long twoCheckerId;
    /**
     * 二次审核人名称
     */
    @Field(type = FieldType.Keyword)
    private String twoCheckerName;
    /**
     * 二审时间
     */
    @Field(type = FieldType.Date)
    private Date twoCheckDate;
}
