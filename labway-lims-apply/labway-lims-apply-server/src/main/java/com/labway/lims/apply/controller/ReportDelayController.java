package com.labway.lims.apply.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PrintStatusEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ReportDelayDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.GeneticsInspectionDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyInspectionDto;
import com.labway.lims.apply.api.dto.es.SpecialtyInspectionDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.ReportDelayService;
import com.labway.lims.apply.vo.DelayApplyVo;
import com.labway.lims.apply.vo.DelayPrintVo;
import com.labway.lims.apply.vo.ReportDelayVo;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 报告单迟发申请
 */
@Slf4j
@RestController
@RequestMapping("/report-delay")
public class ReportDelayController extends BaseController {

    @Resource
    private ReportDelayService reportDelayService;
    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private PdfReportService pdfReportService;
    @DubboReference
    private DictService dictService;



    /**
     *  左侧列表
     *  多条件查询
     */
    @PostMapping("/get-report-delay")
    public Object getReportDelay(@RequestBody ReportDelayVo voParam) throws Exception {
        if (Objects.isNull(voParam)) {
            return Collections.emptyList();
        }
        // ES筛选条件
        // 签收日期
        Date beginSignDate = voParam.getBeginSignDate();
        Date endSignDate = voParam.getEndSignDate();
        // 门诊/住院号
        String patientVisitCard = voParam.getPatientVisitCard();
        // 病人姓名
        String patientName = voParam.getPatientName();
        // 条码号
//        String barcode = voParam.getBarcode();

        // 延迟报告表筛选条件
        // 申请日期
        Date sendDateStart = voParam.getSendDateStart();
        Date sendDateEnd = voParam.getSendDateEnd();
        // 前端传入送检机构
        Long hspOrgId = voParam.getHspOrgId();
        // 前端传入专业组
        Long groupId = voParam.getGroupId();



        //申请日期
        if (Objects.isNull(sendDateStart) || Objects.isNull(sendDateEnd)) {
            throw new IllegalStateException("申请日期错误");
        }

        if (Objects.isNull(voParam.getStatus())) {
            throw new IllegalStateException("请选择状态");
        }


        final SampleEsQuery.SampleEsQueryBuilder builderParams = SampleEsQuery.builder();
        builderParams.pageNo(NumberUtils.INTEGER_ONE)
                .pageSize(Integer.MAX_VALUE)
                // 需要排除已终止检验的数据
                .excludeSampleStatus(List.of(SampleStatusEnum.STOP_TEST.getCode()));

        // es构造条件-签收时间
        if (Objects.nonNull(beginSignDate) && Objects.nonNull(endSignDate)) {
            builderParams.startSignDate(beginSignDate);
            builderParams.endSignDate(endSignDate);
        }

        // es构造条件-送检机构
        if (Objects.nonNull(hspOrgId)) {
            builderParams.hspOrgIds(Set.of(hspOrgId));
        }

        // es构造条件-门诊住院号
        if (Objects.nonNull(patientVisitCard)) {
            builderParams.patientVisitCard(patientVisitCard);
        }
        // es构造条件-病人名称
        if (StringUtils.isNotBlank(patientName)) {
            builderParams.patientName(patientName);
        }

       // 查所有延迟报告申请数据
        ReportDelayDto dto = new ReportDelayDto();
        BeanUtils.copyProperties(voParam, dto);

        // 获取延迟报告数据
        List<ReportDelayDto> reportDelayDtos = reportDelayService.selectByReportDelayInfo(dto);
        if (CollectionUtils.isEmpty(reportDelayDtos)) {
            return Collections.emptyList();
        }

        // 排除报告单迟发表中已终止的数据，需要去关联applySample
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(reportDelayDtos.stream().map(ReportDelayDto::getApplySampleId).collect(Collectors.toList()));
        // key-申请单id , value-status
        Map<Long, Integer> applySampleIdWithStatusMap = applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, ApplySampleDto::getStatus, (a, b) -> a));

        // 获取延迟报告申请表中的所有的条码号，然后去es中查对应的患者信息
        Set<String> barcodes = reportDelayDtos.stream().map(ReportDelayDto::getBarcode).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(barcodes)) {
            builderParams.barcodes(barcodes);
        }

        // 查es中的申请单
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(builderParams.build());


        // 按照申请单样本Id分组
        Map<Long, BaseSampleEsModelDto> applySampleByApplySampleId = baseSampleEsModelDtos
                .stream().collect(Collectors.toMap(BaseSampleEsModelDto::getApplySampleId, item -> item,(a, b) -> a));

        // DTO 转VO
        List<ReportDelayVo> reportDelayList = convertListToVO(reportDelayDtos,ReportDelayVo.class);

        List<DictItemDto> dictItemDtos = dictService.selectByDictType(DictEnum.REPORT_DELAY_TYPE.toString());
        Map<String, String> dictMap = dictItemDtos.stream().collect(Collectors.toMap(DictItemDto::getDictCode, DictItemDto::getDictName, (a, b) -> b));


        // 返回结果集合
        List<ReportDelayVo> result = new ArrayList<>();

        for (ReportDelayVo vo : reportDelayList) {

            // 关联出申请单样本的数据
            Long applySampleId = vo.getApplySampleId();
            BaseSampleEsModelDto esDto = applySampleByApplySampleId.get(applySampleId);
            if (Objects.nonNull(esDto)) {

                vo.setHspOrgName(esDto.getHspOrgName());
                //样本号
                vo.setSampleNo(esDto.getSampleNo());

                //病人姓名
                vo.setPatientName(esDto.getPatientName());

                //性别
                vo.setPatientSex(esDto.getPatientSex());

                //年龄
                vo.setPatientAge(esDto.getPatientAge());

                //门诊/住院号
                vo.setPatientVisitCard(esDto.getPatientVisitCard());

                //签收日期
                vo.setSignDate(esDto.getSignDate());

                //专业组
                //vo.setGroupName(esDto.getGroupName());

                // 只有已打印的和作废的才显示打印时间
                if (Objects.equals(PrintStatusEnum.PRINTED.getCode(),vo.getStatus()) || Objects.equals(PrintStatusEnum.ABOLISHED.getCode(),vo.getStatus())) {
                    //打印人
                    vo.setPrinterName(Objects.equals(esDto.getIsPrint(), YesOrNoEnum.YES.getCode()) ? esDto.getPrinterName() : null);
                    //打印时间
                    vo.setPrintDate(Objects.equals(esDto.getIsPrint(), YesOrNoEnum.YES.getCode()) ? esDto.getPrintDate() : null);
                }

                //状态
                vo.setStatusName(PrintStatusEnum.getByCode(vo.getStatus()).getDesc());

                //检验项目
                vo.setTestItems(esDto.getTestItems());

                //检验项目编码List,前端方便使用
                if (StringUtils.isNotBlank(vo.getTestItemCodes())) {
                    vo.setTestItemCodesList(Arrays.asList(vo.getTestItemCodes().split(",")));
                }
                // 样本审核状态，前端用于判断是否能修改
                vo.setSampleStatus(esDto.getSampleStatus());
                // 项目类型 常规检验、微生物检验、院感检验 区分做什么检验的
                vo.setItemType(esDto.getItemType());

                // sampleID查检验单使用
                vo.setSampleId(esDto.getSampleId());

                // 字典code关联字典中文
                vo.setReasonName(dictMap.get(vo.getReason()));

                vo.setIsTwoPick(esDto.getIsTwoPick());

                // 已终止的数据就不在页面显示
                if (applySampleIdWithStatusMap.get(vo.getApplySampleId()) != null && !Objects.equals(applySampleIdWithStatusMap.get(vo.getApplySampleId()),SampleStatusEnum.STOP_TEST.getCode())) {
                    result.add(vo);
                }
                // 1.1.3新增科室
                vo.setDept(esDto.getDept());

                if (esDto instanceof GeneticsInspectionDto) {
                    vo.setResultType(((GeneticsInspectionDto) esDto).getResultType());
                } else if (esDto instanceof MicrobiologyInspectionDto) {
                    vo.setResultType(((MicrobiologyInspectionDto) esDto).getResultType());
                } else if (esDto instanceof SpecialtyInspectionDto) {
                    vo.setResultType(((SpecialtyInspectionDto) esDto).getResultType());
                }

            }

        }

        // 按照送检机构名分组
        Map<String, List<ReportDelayVo>> hspOrgNameGroupMap = result.stream().collect(Collectors.groupingBy(ReportDelayVo::getHspOrgName));
        Set<Map.Entry<String, List<ReportDelayVo>>> entries = hspOrgNameGroupMap.entrySet();

        List<Object> list = new ArrayList<>();

        final Set<String> peopleCountSet = new HashSet<>();


        for (Map.Entry<String, List<ReportDelayVo>> entry : entries) {
            String key = entry.getKey();
            List<ReportDelayVo> value = entry.getValue();
            for (ReportDelayVo reportDelayVo : value) {
                peopleCountSet.add(reportDelayVo.getHspOrgId() + reportDelayVo.getPatientName() + reportDelayVo.getPatientSex());
            }
            list.add(Map.of("hspOrgName", key, "size", value.size(), "list", value));
        }

        return Map.of("list", list, "personCount", peopleCountSet.size(), "sampleCount", result.stream().map(ReportDelayVo::getBarcode).distinct().count());
    }

    /**
     * 新增
     */
    @PostMapping("/add")
    public Object addReportDelay(@RequestBody ReportDelayVo vo){
        if (StringUtils.isBlank(vo.getBarcode())) {
            throw new IllegalArgumentException("请填写条码号");
        }
        // TODO 1验证条码号正确性
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByBarcode(vo.getBarcode());
        if (CollectionUtils.isEmpty(applySampleDtos)) {
            throw new LimsException("该条码号无效，请输入正确的条码号");
        }

        // 数据验证
        validate(vo);
        // 使用逗号分隔字符串
        String[] testItemCodesArray = vo.getTestItemCodes().split(",");

        // 将数组转换为List
        List<String> testItemCodesList = Arrays.asList(testItemCodesArray);

        // 如果有多个返回第一个，如果只有一个返回对应的ApplySampleId
        Long applySampleId = selectTestItems(vo.getBarcode(), testItemCodesList);

        ReportDelayDto dto = new ReportDelayDto();
        BeanUtils.copyProperties(vo, dto);
        if (applySampleId != null) {
            dto.setApplySampleId(applySampleId);
        }

        return Map.of("delayId",reportDelayService.addReportDelay(dto));
    }


    /**
     * 修改
     */
    @PostMapping("/update-delay-id")
    public Object updateReportDelay(@RequestBody ReportDelayVo vo){
        if (Objects.isNull(vo.getDelayId())){
            throw new IllegalArgumentException("请选择需要修改的数据");
        }
        // 数据验证
        validate(vo);

        ReportDelayDto dto = new ReportDelayDto();
        BeanUtils.copyProperties(vo, dto);
        reportDelayService.updateByDelayId(dto);

        return Collections.emptyList();
    }

    /**
     * 作废
     */
    @PostMapping("/delete-delay-id")
    public Object delReportDelay(@RequestBody Set<Long> delayIds){
        if (CollectionUtils.isEmpty(delayIds)){
            throw new IllegalArgumentException("请选择需要作废的数据");
        }

//        验证是否能作废
        List<ReportDelayDto> reportDelayDtos = reportDelayService.selectByReportDelayIds(delayIds);
//        不是一个人的不能作废，需提示
        List<ReportDelayDto> exitsList = reportDelayDtos.stream().filter(dto -> !dto.getCreatorId().equals(LoginUserHandler.get().getUserId())).collect(Collectors.toList());
        if (exitsList.size() != 0) {
            throw new IllegalArgumentException(String.format("条码号【%s】，仅能申请人作废本人的申请，其他人无权作废", exitsList.stream().map(ReportDelayDto::getBarcode).collect(Collectors.joining(","))));
        }
        final LoginUserHandler.User user = LoginUserHandler.get();
        reportDelayService.updateByDelayIds(delayIds,PrintStatusEnum.ABOLISHED.getCode(), user.getUserId(), user.getNickname());
        return Collections.emptyList();
    }


    /**
     * 查询条码号患者信息和对应的检验项目
     */
    @PostMapping("/get-barcode")
    public Object selectBarcode(@RequestParam("barcode") String barcode) {
        if (StringUtils.isBlank(barcode)) {
            throw new IllegalArgumentException("请输入条码号");
        }
        // 对应所有申请单样本 只可能为同一个 申请单
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(applySampleDtos)) {
            throw new LimsException("无效条码号");
        }
        // 申请单id
        ApplySampleDto applySampleDto = applySampleDtos.stream().findFirst().get();


         // 条码号查一次分拣---->未审数据之间的数据
         // is_one_pick 一次分拣 等于1,并且状态等于(0 : 已录入 10 : 未审11 : 待复查)
        boolean onePickFlag = applySampleDto.getIsOnePick() != null && applySampleDto.getIsOnePick() == 1;
        Integer status = applySampleDto.getStatus();
        boolean statusFlag = Objects.equals(SampleStatusEnum.ENTER.getCode(), status) || Objects.equals(SampleStatusEnum.NOT_AUDIT.getCode(), status) || Objects.equals(SampleStatusEnum.RETEST.getCode(), status);
        // 如果非一次分拣 或者 审核状态不等于0 10 11 的条码数据不展示
        if (!onePickFlag || !statusFlag) {
            throw new IllegalArgumentException("请扫入一次分拣至未审之前的样本");
        }


        final ApplyDto applyDto = applyService.selectByApplyId(applySampleDto.getApplyId());
        Assert.notNull(applyDto,"申请单不存在");

        // 所有申请单样本ids
        Set<Long> applySampleIds = applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());

        // 样本 对应检验项目
        List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplySampleIds(applySampleIds);


        DelayApplyVo bean = BeanUtil.toBean(applyDto, DelayApplyVo.class);
        bean.setApplySampleItem(applySampleItemDtos);
        bean.setApplySampleId(applySampleItemDtos.stream().filter(v -> v.getApplySampleId() != null).findFirst().get().getApplySampleId());
        return bean;
    }


    /**
     * 根据条码号+延迟申请表中的检验项目编码集合->获取对应的检验项目
     * @param barcode
     * @param testItemCodes
     * @return
     */
    private Long selectTestItems(String barcode, List<String> testItemCodes) {
        if (Objects.isNull(barcode) || CollectionUtils.isEmpty(testItemCodes)) {
            return null;
        }

        // 对应所有申请单样本 只可能为同一个 申请单
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(applySampleDtos)) {
            throw new LimsException("无效条码号");
        }
        ApplySampleDto applySampleDto = applySampleDtos.stream().findFirst().get();


        final ApplyDto applyDto = applyService.selectByApplyId(applySampleDto.getApplyId());
        Assert.notNull(applyDto,"申请单不存在");

        // 所有申请单样本ids
        Set<Long> applySampleIds = applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());

        // 样本 对应检验项目
        List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplySampleIds(applySampleIds);
        Assert.notNull(applySampleItemDtos,"该条码号没有检验项目");

        // 如果有多个applySampleId返回随机返回第一个
        List<Long> applySampleId = applySampleItemDtos.stream().filter(itemDto -> testItemCodes.contains(itemDto.getTestItemCode())).map(ApplySampleItemDto::getApplySampleId).collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(applySampleId) ? applySampleId.get(0) : null;
    }

    /**
     * 打印
     */
    @PostMapping("/print-delay")
    public Object printDelay(@RequestBody Set<Long> delayIds){
        if (CollectionUtils.isEmpty(delayIds)) {
            throw new IllegalArgumentException("请选择要打印的数据");
        }
        // 打印并修改状态
        return getPreviewAndPrint(delayIds,true);
    }


    /**
     * 预览延迟报告单
     */
    @PostMapping("/preview-delay")
    public Object previewReportDelay(@RequestBody Set<Long> delayIds){
        if (CollectionUtils.isEmpty(delayIds)) {
            throw new IllegalArgumentException("请选择要预览的数据");
        }
        // 仅预览 不修改打印状态
        return getPreviewAndPrint(delayIds,false);
    }

    /**
     * 预览&打印
     * @param delayIds
     * @param isPrint 是否打印 true-是
     * @return
     */
    private List<String> getPreviewAndPrint(Set<Long> delayIds,boolean isPrint) {
        PdfReportParamDto pdfReportParamDto = new PdfReportParamDto();

        List<ReportDelayDto> reportDelayDtos = reportDelayService.selectByReportDelayIds(delayIds);
        if (CollectionUtils.isEmpty(reportDelayDtos)) {
            throw new IllegalArgumentException("未获取到要预览或打印的数据");
        }

        Set<Long> applyIds = reportDelayDtos.stream().map(ReportDelayDto::getApplyId).collect(Collectors.toSet());


        List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);
        if (CollectionUtils.isEmpty(applyDtos)) {
            throw new IllegalArgumentException("未获取到要预览或打印的申请单数据");
        }
        Map<Long, ApplyDto> applyMap = applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));


        List<DictItemDto> dictItemDtos = dictService.selectByDictType(DictEnum.REPORT_DELAY_TYPE.toString());
        Map<String, String> dictMap = dictItemDtos.stream().collect(Collectors.toMap(DictItemDto::getDictCode, DictItemDto::getDictName, (a, b) -> b));

        // 封装要打印的数据
        List<DelayPrintVo> delayPrints = new ArrayList<>();

        for (ReportDelayDto obj : reportDelayDtos) {
            DelayPrintVo vo = new DelayPrintVo();

            ApplyDto applyDto = applyMap.get(obj.getApplyId());

            vo.setApply(applyDto);

            vo.setHospitalName(applyDto.getHspOrgName());

            vo.setGroupName(obj.getGroupName());

            vo.setSendTestDate(dateToLocaldate(applyDto.getCreateDate()));

            vo.setPatientName(applyDto.getPatientName());

            vo.setBedNumber(applyDto.getPatientBed());

            vo.setBarcode(obj.getBarcode());

            vo.setApplySampleId(obj.getApplySampleId());

            vo.setRemake(obj.getRemark());

            // 字典code关联字典中文
            vo.setCause(dictMap.get(obj.getReason()));

            // 预计发布日期
            vo.setSendReportDate(dateToLocaldateTime(obj.getSendReportDate()));

            vo.setPrintDate(LocalDateTime.now());
            vo.setApplyId(obj.getApplyId());
            delayPrints.add(vo);
        }


        // 根据申请单样本查询检验项目
        List<Long> applySampleIds = reportDelayDtos.stream().map(ReportDelayDto::getApplySampleId).collect(Collectors.toList());
        List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplySampleIds(applySampleIds);
        // 申请单样本
        List<ApplySampleDto> applySampleList = applySampleService.selectByApplySampleIds(applySampleIds);

        // 获取申请单对应的检验项目
        final Map<Long, ApplySampleDto> applySampleDtoMap = applySampleList.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity(), (a, b) -> b));
        Map<Long, List<ApplySampleItemDto>> testItemMap = applySampleItemDtos.stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplyId));


        List<String> urls = new LinkedList<>();

        for (DelayPrintVo delayPrint : delayPrints) {
            List<ApplySampleItemDto> applySampleItemDtoList = testItemMap.get(delayPrint.getApplyId());
            pdfReportParamDto = BeanUtil.toBean(delayPrint, PdfReportParamDto.class);
            // 拿检验项目
            List<String> testItemNames = new ArrayList<>(1);
            if (applySampleItemDtoList != null) {
                testItemNames = applySampleItemDtoList.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList());
            }
            pdfReportParamDto.put("reportItemList", CollectionUtil.join(testItemNames, ","));
            pdfReportParamDto.put("applySample", applySampleDtoMap.getOrDefault(delayPrint.getApplySampleId(), new ApplySampleDto()));
            String url = pdfReportService.build2Url(PdfTemplateTypeEnum.LATE_REPORT_REQUEST.getCode(), pdfReportParamDto, 1);
            urls.add(url);
        }


        if (urls.size() > 0 && isPrint) {
            // 修改打印状态
            reportDelayService.updateByDelayIds(delayIds,PrintStatusEnum.PRINTED.getCode(),null,null);
            // 根据applyId修改打印人、打印时间
            ApplySampleDto applySampleDto = new ApplySampleDto();
            applySampleDto.setIsPrint(YesOrNoEnum.YES.getCode());
            applySampleDto.setPrinterId(LoginUserHandler.get().getUserId());
            applySampleDto.setPrinterName(LoginUserHandler.get().getNickname());
            applySampleDto.setPrintDate(new Date());
            applySampleService.updateByApplySampleIds(applySampleDto, reportDelayDtos.stream().map(ReportDelayDto::getApplySampleId).collect(Collectors.toSet()));
        }
        return urls;
    }



    private LocalDateTime dateToLocaldateTime(Date date){
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    private LocalDate dateToLocaldate(Date date){
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }


    public void validate(ReportDelayVo vo) {

        if (Objects.isNull(vo.getSendReportDate())) {
            throw new IllegalArgumentException("请填写预计发报告日期");
        }

        if (StringUtils.isBlank(vo.getTestItemCodes())) {
            throw new IllegalArgumentException("请填检写验项目");
        }

        if (StringUtils.isBlank(vo.getReason())) {
            throw new IllegalArgumentException("请填写迟发原因");
        }

        if (StringUtils.isNotBlank(vo.getRemark()) && StringUtils.length(vo.getRemark()) > TEXTAREA_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("迟发备注不能超过 %s 个字符", TEXTAREA_MAX_LENGTH));
        }


    }
    public static <T> List<T> convertListToVO(List<?> list, Class<T> voClass) throws Exception {
        return convertListToVO(list, voClass, new String[0]);
    }

    public static <T> List<T> convertListToVO(List<?> list, Class<T> voClass, String... ignoreProperties) throws Exception {
        List<T> resultList = new ArrayList<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            for (Object o : list) {
                T vo = voClass.newInstance();
                BeanUtils.copyProperties(o, vo, ignoreProperties);
                resultList.add(vo);
            }
        }
        return resultList;
    }


}
