package com.labway.lims.apply.service.chain.pda.apply.add;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.PdaEntryTestApplyDto;
import com.labway.lims.apply.api.dto.PdaTobeConfirmedApplyDto;
import com.labway.lims.apply.api.dto.PdaTobeConfirmedApplyDto.PdaConfirmEnum;
import com.labway.lims.apply.api.service.pda.PdaApplySampleItemService;
import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.api.service.pda.PdaTobeConfirmedApplyService;
import com.labway.lims.apply.service.chain.apply.add.AddApplyContext;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
public class PdaAddPgsqlCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private PdaApplyService pdaApplyService;
    @Resource
    private PdaApplySampleItemService pdaApplySampleItemService;

    @Resource
    private PdaTobeConfirmedApplyService pdaTobeConfirmedApplyService;

    @Override
    public boolean execute(Context context) throws Exception {
        AddApplyContext from = AddApplyContext.from(context);
        ApplyDto apply = from.getApply();
        LoginUserHandler.User user = from.getUser();
        HspOrganizationDto hspOrganization = from.getHspOrganization();
        List<ApplySampleItemDto> applySampleItems = from.getApplySampleItems();

        final PdaApplyDto pdaApplyDto = JSON.parseObject(JSON.toJSONString(apply), PdaApplyDto.class);
        pdaApplyDto.setPdaApplyId(apply.getApplyId());
        // pda申请单信息录入
        pdaApplyService.addApply(pdaApplyDto);

        // pda项目录入
        pdaApplySampleItemService.addBacthApplySampleItem(applySampleItems);


        List<PdaApplyDto> pdaApplyInfo = from.getPdaApply();
        // 数量为一， 再加上本次新增的话， 增加状态
        if (pdaApplyInfo.size() == NumberUtils.INTEGER_ONE) {
            PdaTobeConfirmedApplyDto dto = getPdaTobeConfirmedApplyDto(apply, hspOrganization, from, user);
            pdaTobeConfirmedApplyService.addTobeConfirmed(dto);
        }

        return CONTINUE_PROCESSING;
    }

    private PdaTobeConfirmedApplyDto getPdaTobeConfirmedApplyDto(ApplyDto apply, HspOrganizationDto hspOrganization, AddApplyContext from, LoginUserHandler.User user) {
        PdaTobeConfirmedApplyDto dto = new PdaTobeConfirmedApplyDto();
        Date now = new Date();
        dto.setPdaTobeConfirmedApplyId(snowflakeService.genId());
        dto.setConfirmedPdaApplyId(from.getPdaDoubleFlag() ? apply.getApplyId() : NumberUtils.LONG_ZERO);
        dto.setMasterBarcode(apply.getMasterBarcode());
        dto.setHspOrgId(hspOrganization.getHspOrgId());
        dto.setHspOrgCode(hspOrganization.getHspOrgCode());
        dto.setHspOrgName(hspOrganization.getHspOrgName());
        dto.setStatus(from.getPdaDoubleFlag() ? PdaConfirmEnum.CONFIRM.getCode() : PdaConfirmEnum.NO_CONFIRM.getCode());
        PdaEntryTestApplyDto testApply = (PdaEntryTestApplyDto) from.getTestApply();
        dto.setPdaImgs(StringUtils.defaultIfBlank(testApply.getPdaImgs(), StringUtils.EMPTY));
        dto.setCreatorId(user.getUserId());
        dto.setCreatorName(user.getUsername());
        dto.setUpdaterId(user.getUserId());
        dto.setUpdaterName(user.getUsername());
        dto.setCreateDate(now);
        dto.setUpdateDate(now);
        dto.setIsDelete(NumberUtils.INTEGER_ZERO);
        return dto;
    }
}
