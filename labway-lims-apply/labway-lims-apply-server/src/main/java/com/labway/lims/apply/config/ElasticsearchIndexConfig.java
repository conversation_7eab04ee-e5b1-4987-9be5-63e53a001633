package com.labway.lims.apply.config;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.apply.model.es.BaseSampleEsModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.List;

/**
 * 自动创建索引
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
class ElasticsearchIndexConfig implements InitializingBean {
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisPrefix redisPrefix;

    @Value("${es.indexName}")
    private String indexName;

    @Override
    public void afterPropertiesSet() throws Exception {

        final String key = redisPrefix.getBasePrefix() + "labway-lims-apply-server:" + ElasticsearchIndexConfig.class.getSimpleName() + ":Sample";

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofMillis(1)))) {
            return;
        }

        final List<Class<?>> classes = List.of(BaseSampleEsModel.class);

        try {

            for (Class<?> clazz : classes) {

                final IndexOperations ops = elasticsearchRestTemplate.indexOps(IndexCoordinates.of(indexName));

                if (ops.exists()) {
                    continue;
                }

                ops.createMapping(BaseSampleEsModel.class);

                if (!ops.createWithMapping()) {
                    throw new IllegalStateException(String.format("创建索引 [%s] 失败", ops.getIndexCoordinates().getIndexName()));
                }
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            stringRedisTemplate.delete(key);
        }

    }
}
