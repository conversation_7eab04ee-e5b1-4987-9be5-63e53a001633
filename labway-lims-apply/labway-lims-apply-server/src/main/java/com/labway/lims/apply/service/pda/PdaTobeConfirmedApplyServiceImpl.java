package com.labway.lims.apply.service.pda;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.business.center.compare.request.compare.ConfirmPDASampleInfoRequest;
import com.labway.business.center.compare.service.TbOrgApplySampleMainAdditionalService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.BaseDataQueryDto;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.PdaApplySampleItemDto;
import com.labway.lims.apply.api.dto.PdaTobeConfirmedApplyDto;
import com.labway.lims.apply.api.dto.PdaTobeConfirmedApplyDto.PdaConfirmEnum;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.pda.PdaApplySampleItemService;
import com.labway.lims.apply.api.service.pda.PdaApplyService;
import com.labway.lims.apply.api.service.pda.PdaTobeConfirmedApplyService;
import com.labway.lims.apply.mapper.pda.PdaTobeConfirmedApplyMapper;
import com.labway.lims.apply.model.pda.TbPdaTobeConfirmedApply;
import com.labway.lims.apply.util.PdaApplyCacheUtil;
import com.swak.frame.dto.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * pda申请单双输确认表
 */
@DubboService
@RefreshScope
public class PdaTobeConfirmedApplyServiceImpl extends ServiceImpl<PdaTobeConfirmedApplyMapper, TbPdaTobeConfirmedApply>
        implements PdaTobeConfirmedApplyService {


    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private PdaApplyService pdaApplyService;

    @Resource
    private ApplyService applyService;
    @Resource
    private PdaApplyCacheUtil pdaApplyCacheUtil;
    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private PdaApplySampleItemService pdaApplySampleItemService;
    @Resource
    private TbOrgApplySampleMainAdditionalService tbOrgApplySampleMainAdditionalService;
    @Value("${business-center.org-code:00010110000000001WND}")
    private String orgCode;

    private static final String ABOLISH_LOCK = "%sPDA_ABOLISH:%s:%s";

    @Override
    public String getAbolishLock(Long orgId, String masterBarcode) {
        String key = String.format(ABOLISH_LOCK, redisPrefix.getBasePrefix(), orgId, masterBarcode);
        if (BooleanUtils
                .isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, org.apache.commons.lang3.StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalArgumentException("操作频繁，请稍后再试");
        }
        return key;
    }

    @Override
    public boolean addTobeConfirmed(PdaTobeConfirmedApplyDto dto) {
        TbPdaTobeConfirmedApply tbPdaTobeConfirmedApply = JSON.parseObject(JSON.toJSONString(dto), TbPdaTobeConfirmedApply.class);
        return super.save(tbPdaTobeConfirmedApply);
    }

    @Override
    public PdaTobeConfirmedApplyDto selectByMasterBarcode(String masterBarcode) {
        return selectByMasterBarcodeAndHspOrgCode(masterBarcode, null);
    }

    @Override
    public PdaTobeConfirmedApplyDto selectByMasterBarcodeAndHspOrgCode(String masterBarcode, @Nullable Long hspOrgId) {
        return convert(baseMapper.selectOne(new LambdaQueryWrapper<TbPdaTobeConfirmedApply>()
                .eq(TbPdaTobeConfirmedApply::getMasterBarcode, masterBarcode)
                .eq(Objects.nonNull(hspOrgId), TbPdaTobeConfirmedApply::getHspOrgId, hspOrgId)
                .last(" limit 1")));
    }

    @Override
    public List<PdaTobeConfirmedApplyDto> selectByMasterBarcodes(Collection<String> masterBarcodes) {
        if (CollectionUtils.isEmpty(masterBarcodes)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(new LambdaQueryWrapper<TbPdaTobeConfirmedApply>()
                        .in(TbPdaTobeConfirmedApply::getMasterBarcode, masterBarcodes))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public void deleteByMasterBarcode(String masterBarcode) {
        if (StringUtils.isNotBlank(masterBarcode)) {
            this.remove(new LambdaQueryWrapper<TbPdaTobeConfirmedApply>()
                    .eq(TbPdaTobeConfirmedApply::getMasterBarcode, masterBarcode));
        }
    }

    @Override
    public List<PdaTobeConfirmedApplyDto> selectByDate(BaseDataQueryDto dto) {
        return this.list(new LambdaUpdateWrapper<TbPdaTobeConfirmedApply>()
                        .eq(org.apache.commons.lang3.StringUtils.isNotBlank(dto.getMasterBarcode()), TbPdaTobeConfirmedApply::getMasterBarcode, dto.getMasterBarcode())
                        .eq(Objects.nonNull(dto.getConfirmed()), TbPdaTobeConfirmedApply::getStatus, BooleanUtils.isTrue(dto.getConfirmed()) ? PdaConfirmEnum.CONFIRM.getCode() : PdaConfirmEnum.NO_CONFIRM.getCode())
                        .eq(Objects.nonNull(dto.getHspOrgId()), TbPdaTobeConfirmedApply::getHspOrgId, dto.getHspOrgId())
                        .between(TbPdaTobeConfirmedApply::getCreateDate, dto.getStartDate(), dto.getEndDate()))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean confirmApply(long pdaApplyId) {
        final PdaApplyDto pdaApplyDto = pdaApplyService.selectById(pdaApplyId);
        if (Objects.isNull(pdaApplyDto)) {
            throw new IllegalStateException("pda申请单不存在");
        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        final String lockAbolish = this.getAbolishLock(user.getOrgId(), pdaApplyDto.getMasterBarcode());

        try {

            final PdaTobeConfirmedApplyDto pdaTobeConfirmedApplyDto = this.selectByMasterBarcode(pdaApplyDto.getMasterBarcode());
            if (Objects.isNull(pdaTobeConfirmedApplyDto)) {
                throw new IllegalStateException("pda待确认申请单不存在");
            }
            if (Objects.equals(pdaTobeConfirmedApplyDto.getStatus(), PdaConfirmEnum.CONFIRM.getCode())) {
                throw new IllegalStateException("pda申请单已确认");
            }

            // 确认
            final boolean update = this.update(new LambdaUpdateWrapper<TbPdaTobeConfirmedApply>()
                    .eq(TbPdaTobeConfirmedApply::getPdaTobeConfirmedApplyId, pdaTobeConfirmedApplyDto.getPdaTobeConfirmedApplyId())
                    .set(TbPdaTobeConfirmedApply::getConfirmedPdaApplyId, pdaApplyId)
                    .set(TbPdaTobeConfirmedApply::getHspOrgId, pdaApplyDto.getHspOrgId())
                    .set(TbPdaTobeConfirmedApply::getHspOrgCode, pdaApplyDto.getHspOrgCode())
                    .set(TbPdaTobeConfirmedApply::getHspOrgName, pdaApplyDto.getHspOrgName())
                    .set(TbPdaTobeConfirmedApply::getStatus, PdaConfirmEnum.CONFIRM.getCode())
                    .set(TbPdaTobeConfirmedApply::getUpdaterId, user.getUserId())
                    .set(TbPdaTobeConfirmedApply::getUpdaterName, user.getNickname())
                    .set(TbPdaTobeConfirmedApply::getConformerId, user.getUserId())
                    .set(TbPdaTobeConfirmedApply::getConformerName, user.getNickname())
                    .set(TbPdaTobeConfirmedApply::getConformerTime, new Date())
                    .set(TbPdaTobeConfirmedApply::getUpdateDate, new Date()));

            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setApplyId(NumberUtils.LONG_ZERO);
            sampleFlow.setApplySampleId(NumberUtils.LONG_ZERO);
            sampleFlow.setBarcode(pdaApplyDto.getMasterBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.PDA_CONFIRM.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.PDA_CONFIRM.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(pdaApplyDto.toSampleFlowContent() + this.getPdaTestItemFlow(pdaApplyId));
            // 添加流水
            sampleFlowService.addSampleFlow(sampleFlow);

            // 通知业务中台
            final ConfirmPDASampleInfoRequest request = new ConfirmPDASampleInfoRequest();
            request.setOrgCode(orgCode);
            request.setLimsBarcodes(new ArrayList<>(List.of(pdaApplyDto.getMasterBarcode())));
            request.setConfirmStatus(NumberUtils.INTEGER_ONE);
            request.setConfirmUserCode(String.valueOf(user.getUserId()));
            request.setConfirmUserName(user.getNickname());
            final Response<?> response = tbOrgApplySampleMainAdditionalService.confirmPDASampleInfo(request);
            if (!response.isSuccess()) {
                throw new IllegalStateException(response.getMsg());
            }

            return update;
        } finally {
            stringRedisTemplate.delete(lockAbolish);
        }
    }

    private String getPdaTestItemFlow(long pdaApplyId) {
        final List<PdaApplySampleItemDto> pdaApplySampleItemDtos = pdaApplySampleItemService.selectByPdaApplyId(pdaApplyId);

        StringBuilder sb = new StringBuilder();
        for (PdaApplySampleItemDto item : pdaApplySampleItemDtos) {
            sb.append(org.apache.commons.lang3.StringUtils.repeat("-", 2)).append(item.getTestItemName())
                    .append("(").append(item.getTestItemCode()).append(")")
                    .append("\n");
        }
        return sb.toString();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean rollbackApply(String masterBarcode) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final String abolishLock = this.getAbolishLock(user.getOrgId(), masterBarcode);
        try {
            final PdaTobeConfirmedApplyDto pdaTobeConfirmedApplyDto = this.selectByMasterBarcode(masterBarcode);
            if (Objects.isNull(pdaTobeConfirmedApplyDto)) {
                throw new IllegalStateException("待回退申请单不存在");
            }

            // 无需回退
            if (Objects.equals(pdaTobeConfirmedApplyDto.getStatus(), PdaConfirmEnum.NO_CONFIRM.getCode())) {
                return true;
            }

            // 判断是否签收
            final List<ApplyDto> applyDtos = applyService.selectByMasterBarcodes(Set.of(masterBarcode));
            if (CollectionUtils.isNotEmpty(applyDtos)) {
                throw new IllegalStateException("PDA申请单已签收");
            }

            // 回退
            final boolean update = this.update(new LambdaUpdateWrapper<TbPdaTobeConfirmedApply>()
                    .eq(TbPdaTobeConfirmedApply::getPdaTobeConfirmedApplyId, pdaTobeConfirmedApplyDto.getPdaTobeConfirmedApplyId())
                    .set(TbPdaTobeConfirmedApply::getConfirmedPdaApplyId, NumberUtils.LONG_ZERO)
                    .set(TbPdaTobeConfirmedApply::getStatus, PdaConfirmEnum.NO_CONFIRM.getCode())
                    .set(TbPdaTobeConfirmedApply::getUpdaterId, user.getUserId())
                    .set(TbPdaTobeConfirmedApply::getUpdaterName, user.getNickname())
                    .set(TbPdaTobeConfirmedApply::getConformerId, NumberUtils.LONG_MINUS_ONE)
                    .set(TbPdaTobeConfirmedApply::getConformerName, org.apache.commons.lang3.StringUtils.EMPTY)
                    .set(TbPdaTobeConfirmedApply::getConformerTime, null)
                    .set(TbPdaTobeConfirmedApply::getUpdateDate, new Date()));

            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setApplyId(NumberUtils.LONG_ZERO);
            sampleFlow.setApplySampleId(NumberUtils.LONG_ZERO);
            sampleFlow.setBarcode(masterBarcode);
            sampleFlow.setOperateCode(BarcodeFlowEnum.PDA_ROLLBACK.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.PDA_ROLLBACK.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(BarcodeFlowEnum.PDA_ROLLBACK.getDesc());
            // 添加流水
            sampleFlowService.addSampleFlow(sampleFlow);

            // 通知业务中台
            final ConfirmPDASampleInfoRequest request = new ConfirmPDASampleInfoRequest();
            request.setOrgCode(orgCode);
            request.setLimsBarcodes(new ArrayList<>(List.of(masterBarcode)));
            request.setConfirmStatus(NumberUtils.INTEGER_ZERO);
            request.setConfirmUserCode(String.valueOf(user.getUserId()));
            request.setConfirmUserName(user.getNickname());
            final Response<?> response = tbOrgApplySampleMainAdditionalService.confirmPDASampleInfo(request);
            if (!response.isSuccess()) {
                throw new IllegalStateException(response.getMsg());
            }

            // 回退把缓存数据删除
            pdaApplyCacheUtil.deletePdaApplyCache(masterBarcode);
            return update;

        } finally {
            stringRedisTemplate.delete(abolishLock);
        }
    }

    @Override
    public void cancelSignByMasterCodes(Collection<String> masterBarcodes) {
        if (CollectionUtils.isEmpty(masterBarcodes)) {
            return;
        }
        final LoginUserHandler.User user = LoginUserHandler.get();
        // 从签收状态改为确认状态
        super.update(new LambdaUpdateWrapper<TbPdaTobeConfirmedApply>()
                .in(TbPdaTobeConfirmedApply::getMasterBarcode, masterBarcodes)
                .set(TbPdaTobeConfirmedApply::getStatus, PdaConfirmEnum.CONFIRM.getCode())
                .set(TbPdaTobeConfirmedApply::getUpdaterId, user.getUserId())
                .set(TbPdaTobeConfirmedApply::getUpdaterName, user.getNickname())
                .set(TbPdaTobeConfirmedApply::getUpdateDate, new Date()));

        final List<SampleFlowDto> sampleFlowDtoList = masterBarcodes.stream().map(masterBarcode -> {
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setApplyId(NumberUtils.LONG_ZERO);
            sampleFlow.setApplySampleId(NumberUtils.LONG_ZERO);
            sampleFlow.setBarcode(masterBarcode);
            sampleFlow.setOperateCode(BarcodeFlowEnum.PDA_CANCEL_SIGN.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.PDA_CANCEL_SIGN.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent(BarcodeFlowEnum.PDA_CANCEL_SIGN.getDesc());
            return sampleFlow;
        }).collect(Collectors.toList());
        // 添加流水
        sampleFlowService.addSampleFlows(sampleFlowDtoList);
    }

    @Override
    public boolean updateStatusByMasterBarcode(String masterBarcode) {
        if (StringUtils.isBlank(masterBarcode)) {
            return false;
        }
        final LambdaUpdateWrapper<TbPdaTobeConfirmedApply> wrapper = new LambdaUpdateWrapper<TbPdaTobeConfirmedApply>()
                .set(TbPdaTobeConfirmedApply::getStatus, PdaConfirmEnum.SIGN.getCode())
                .eq(TbPdaTobeConfirmedApply::getMasterBarcode, masterBarcode);
        return super.update(wrapper);
    }

    private PdaTobeConfirmedApplyDto convert(TbPdaTobeConfirmedApply pdaTobeConfirmedApply) {
        return JSON.parseObject(JSON.toJSONString(pdaTobeConfirmedApply), PdaTobeConfirmedApplyDto.class);
    }
}
