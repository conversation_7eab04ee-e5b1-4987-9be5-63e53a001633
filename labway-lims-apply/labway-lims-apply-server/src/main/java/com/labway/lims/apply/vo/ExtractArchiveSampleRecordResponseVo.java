package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 提取归档样本 -提举操作内容
 * 
 * <AUTHOR>
 * @since 2023/5/26 10:56
 */

@Getter
@Setter
public class ExtractArchiveSampleRecordResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 位置
     */
    private String positionDesc;
    /**
     * 条码
     */
    private String barcode;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 提取原因
     */
    private String extractDesc;

    /**
     * 用户名-提取人
     */
    private String username;

    /**
     * 提取时间
     */
    private Date extractDate;
}