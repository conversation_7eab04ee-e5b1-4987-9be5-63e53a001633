package com.labway.lims.apply.service.chain.apply.update;

import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.service.chain.apply.add.CheckParamCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <pre>
 * CheckSamePersonDayItemCommand
 * 校验是否同人通天同项目
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/3/18 16:41
 */
@Slf4j
@Component
public class CheckSamePersonDayItemCommand implements Command {

    @Resource
    private CheckParamCommand checkParamCommand;


    @Override
    public boolean execute(Context c) throws Exception {

        final UpdateApplyContext from = UpdateApplyContext.from(c);
        final TestApplyDto testApplyDto = from.getTestApply();
        List<ApplySampleItemDto> addApplySampleItems = from.getAddApplySampleItems();

        if (CollectionUtils.isEmpty(addApplySampleItems)) {
            return CONTINUE_PROCESSING;
        }

        // 校验病人 同人同天同项目提示
        if (testApplyDto.getIgnoreSameItem() == null || Objects.equals(testApplyDto.getIgnoreSameItem(),0)){
            log.info("同人同天同项目标识：{}", testApplyDto.getIgnoreSameItem());
            checkParamCommand.checkSameItem(testApplyDto, addApplySampleItems);
        }

        return CONTINUE_PROCESSING;
    }

}
