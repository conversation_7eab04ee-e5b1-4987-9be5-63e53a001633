package com.labway.lims.apply.service.chain.splitblood.info;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.BloodOneSplitDto;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.service.chain.splitblood.CopyApplySampleCommand;
import com.labway.lims.apply.service.chain.splitblood.SplitBloodContext;
import com.labway.lims.base.api.dto.TestItemDto;
import lombok.AllArgsConstructor;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class SplitBloodInfoCommand implements Command {

    @Resource
    private ApplyService applyService;
    @Resource
    private CopyApplySampleCommand copyApplySampleCommand;

    /**
     * @see CopyApplySampleCommand
     */
    @Override
    public boolean execute(Context c) throws Exception {
        final SplitBloodContext context = SplitBloodContext.from(c);


        final ApplySampleDto applySample = context.getApplySample();
        final Map<String, TestItemDto> testItems = context.getTestItems();

        // 根据专业组+外送机构分组
        final List<ApplySampleItemDto> applySampleItems = context.getApplySampleItems();
        // 条码号和样本项目
        final Map<String, List<ApplySampleItemDto>> barcodeApplySampleItemMap = new HashMap<>();
        // 所有样本
        final List<ApplySampleDto> applySampleDtoList = new ArrayList<>();

        // 不支持分血 那么跳过
        if (!context.isSupportedSplitBlood()) {
            applySampleDtoList.add(applySample);
            barcodeApplySampleItemMap.put(applySample.getBarcode(), applySampleItems);
            final List<BloodOneSplitDto> bloodOneSplitInfos = this.getBloodOneSplitInfos(applySampleDtoList, barcodeApplySampleItemMap, testItems);
            context.put(SplitBloodContext.APPLY_SAMPLE_INFOS, bloodOneSplitInfos);
            return CONTINUE_PROCESSING;
        }


        // 支持分血
        Map<OutGroup, List<ApplySampleItemDto>> groupItems = applySampleItems.stream()
                .collect(Collectors.groupingBy(k -> {
                    Long exportOrgId = NumberUtils.LONG_ZERO;
                    final TestItemDto testItem = testItems.get(k.getTestItemCode());
                    if (Objects.nonNull(testItem) && Objects.equals(testItem.getEnableExport(), YesOrNoEnum.YES.getCode())) {
                        exportOrgId = testItem.getExportOrgId();
                    }
                    return new OutGroup(k.getGroupId(), k.getGroupName(), exportOrgId);
                }));

        // 获取使用原条码的专业组  （ps： 这里主要是applySampleItem下只有groupId和groupName， 系统参数配置为groupCode）
        long originalBarcodeGroupId = copyApplySampleCommand.getOriginalBarcodeGroupId(groupItems.keySet().stream().map(e -> e.groupId).collect(Collectors.toSet()));

        // fix-1.1.3.6 同一个专业组检验项目委外不同的机构， 需要分条码， 重新进行排序
        groupItems = this.sortGroupItemsMap(groupItems, originalBarcodeGroupId);

        int i = 0;
        int j = 0;
        for (var e : groupItems.entrySet()) {
            j++;
            final OutGroup group = e.getKey();
            final List<ApplySampleItemDto> items = e.getValue();

            if (CollectionUtils.isEmpty(items)) {
                throw new IllegalStateException(String.format("分血失败，此样本下没有专业组 [%s] 的项目", e.getKey().groupName));
            }

            // 分血 复制申请单样本
            final ApplySampleDto splitApplySample = new ApplySampleDto();
            BeanUtils.copyProperties(applySample, splitApplySample);

            // dev-1.1.3.3 判断是否是配置的原条码专业组， 不是的话进行 'barcode_N' 操作
            if (j > 1) {
                splitApplySample.setBarcode(applySample.getBarcode() +
                        "_" + StringUtils.leftPad(String.valueOf(++i), 2, '0'));
            }

            splitApplySample.setGroupId(group.groupId);
            splitApplySample.setGroupName(group.groupName);
            splitApplySample.setIsSplitBlood(YesOrNoEnum.YES.getCode());
            splitApplySample.setItemType(items.iterator().next().getItemType());
            splitApplySample.setSplitDate(new Date());
            splitApplySample.setSplitterId(LoginUserHandler.get().getUserId());
            splitApplySample.setSplitterName(LoginUserHandler.get().getNickname());

            // 如果下面的项目全是外送 那么样本设置为外送
            if (items.stream().allMatch(k -> Objects.equals(k.getIsOutsourcing(), YesOrNoEnum.YES.getCode()))) {
                splitApplySample.setIsOutsourcing(YesOrNoEnum.YES.getCode());
            }

            // 被分条码之后的数据
            applySampleDtoList.add(splitApplySample);
            barcodeApplySampleItemMap.put(splitApplySample.getBarcode(), items);
        }

        final List<BloodOneSplitDto> bloodOneSplitInfos = this.getBloodOneSplitInfos(applySampleDtoList, barcodeApplySampleItemMap, testItems);
        context.put(SplitBloodContext.APPLY_SAMPLE_INFOS, bloodOneSplitInfos);
        return CONTINUE_PROCESSING;
    }


        /**
     * 排序 根据使用原条码的专业组id 排序 groupItemsMap
     */
    private LinkedHashMap<OutGroup, List<ApplySampleItemDto>> sortGroupItemsMap(Map<OutGroup, List<ApplySampleItemDto>> groupItemsMap,
                                                                                                       long originalBarcodeGroupId
    ) {
        LinkedHashMap<OutGroup, List<ApplySampleItemDto>> map = new LinkedHashMap<>();

        // map 排序后的 key
        LinkedList<OutGroup> outGroupList = new LinkedList<>();

        for (OutGroup outGroup : groupItemsMap.keySet()) {
            // 和原条码同一个专业组则放到最前面
            if (Objects.equals(originalBarcodeGroupId, outGroup.groupId)) {
                outGroupList.addFirst(outGroup);
            } else {
                outGroupList.addLast(outGroup);
            }
        }

        for (OutGroup outGroup : outGroupList) {
            map.put(outGroup, groupItemsMap.get(outGroup));
        }

        return map;
    }

    private List<BloodOneSplitDto> getBloodOneSplitInfos(List<ApplySampleDto> samples,
                                                         Map<String, List<ApplySampleItemDto>> barcodeApplySampleItemMap,
                                                         Map<String, TestItemDto> testItems) {
        // 申请单
        final Map<Long, ApplyDto> applyDto = applyService.selectByApplyIds(samples.stream().map(ApplySampleDto::getApplyId)
                .collect(Collectors.toSet())).stream().collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (a, b) -> a));

        return samples.stream().map(e -> {
            final BloodOneSplitDto v = new BloodOneSplitDto();
            BeanUtils.copyProperties(e, v);

            // 补充申请单信息
            if (applyDto.containsKey(e.getApplyId())) {
                BeanUtils.copyProperties(applyDto.get(e.getApplyId()), v);
            }

            final List<ApplySampleItemDto> applySampleItems = barcodeApplySampleItemMap.getOrDefault(e.getBarcode(), List.of());

            // 专业组项目
            v.setGroupNames(applySampleItems.stream().map(ApplySampleItemDto::getGroupName).collect(Collectors.toList()));

            // 和分血前不一样的ID，那就是新生成的
            v.setIsNewSplitSample(v.getBarcode().contains("_"));

            // 检验项目
            v.setTestItemNames(applySampleItems.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList()));

            // 分血量
            v.setBasicQuantity(applySampleItems.stream().map(item -> testItems.get(item.getTestItemCode()))
                    .filter(Objects::nonNull).max(Comparator.comparing(TestItemDto::getBasicQuantity))
                    .map(TestItemDto::getBasicQuantity).orElse(BigDecimal.ZERO));

            return v;
        }).sorted((o1, o2) -> StringUtils.compare(o1.getBarcode(), o2.getBarcode())).collect(Collectors.toList());
    }

    @AllArgsConstructor
    private static final class OutGroup {
        private Long groupId;
        private String groupName;
        private Long exportOrgId;

        @Override
        public boolean equals(Object o) {
            if (this == o)
                return true;
            if (o == null || getClass() != o.getClass())
                return false;
            OutGroup outGroup = (OutGroup) o;
            return Objects.equals(groupId, outGroup.groupId) && Objects.equals(exportOrgId, outGroup.exportOrgId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(groupId, exportOrgId);
        }
    }
}