package com.labway.lims.apply.service.chain.pick.one;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 二次分拣
 */
@Component
public class OneP<PERSON><PERSON>hain extends ChainBase implements InitializingBean {

    @Resource
    private OnePickChooseGroupCommand onePickChooseGroupCommand;
    @Resource
    private OnePickFillInfoCommand onePickFillInfoCommand;
    @Resource
    private OnePickFindLocationCommand onePickFindLocationCommand;
    @Resource
    private OnePickUpdateApplySampleCommand onePickUpdateApplySampleCommand;
    @Resource
    private OnePickLimitCommand onePickLimitCommand;
    @Resource
    private OnePickCheckApplySampleStatusCommand onePickCheckApplySampleStatusCommand;
    @Resource
    private OnePickFlowCommand onePickFlowCommand;
    @Resource
    private OnePickCheckApplyStatusCommand onePickCheckApplyStatusCommand;
    @Resource
    private OnePickUpdateApplySampleItemCommand onePickUpdateApplySampleItemCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 上锁
        addCommand(onePickLimitCommand);

        // 判断是否已停止或已终止
        addCommand(onePickCheckApplySampleStatusCommand);

        // 获取部分数据
        addCommand(onePickFillInfoCommand);

        // 校验申请单状态
        addCommand(onePickCheckApplyStatusCommand);

        // 选择分拣到哪个专业组
        addCommand(onePickChooseGroupCommand);

        // 选择分拣到试管架哪个位置 (并保存 rack space)
        addCommand(onePickFindLocationCommand);

        // 更新申请单样本
        addCommand(onePickUpdateApplySampleCommand);

        // 更新申请单样本项目专业组
        addCommand(onePickUpdateApplySampleItemCommand);

        // 保存流水
        addCommand(onePickFlowCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);

    }
}
