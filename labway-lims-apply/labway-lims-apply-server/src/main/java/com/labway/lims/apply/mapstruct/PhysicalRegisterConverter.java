package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.PhysicalRegisterDto;
import com.labway.lims.apply.model.TbPhysicalRegister;
import com.labway.lims.apply.vo.SelectByPhysicalBatchResponseVo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 体检人相关装换
 * 
 * <AUTHOR>
 * @since 2023/5/8 13:22
 */
@Mapper(componentModel = "spring")
public interface PhysicalRegisterConverter {

    SelectByPhysicalBatchResponseVo fromPhysicalRegisterDto(PhysicalRegisterDto obj);

    PhysicalRegisterDto fromTbPhysicalRegister(TbPhysicalRegister obj);

    List<PhysicalRegisterDto> fromTbPhysicalRegisterList(List<TbPhysicalRegister> list);

    TbPhysicalRegister tbPhysicalRegisterFromTbDto(PhysicalRegisterDto obj);

    List<TbPhysicalRegister> tbPhysicalRegisterListFromTbDtoList(List<PhysicalRegisterDto> list);
}
