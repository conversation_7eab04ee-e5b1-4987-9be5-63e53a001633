package com.labway.lims.apply.service.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.MicrobiologySampleEsQuery;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.BloodCultureInspectionDto;
import com.labway.lims.apply.api.dto.es.GeneticsInspectionDto;
import com.labway.lims.apply.api.dto.es.InfectionInspectionDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyInspectionDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.PathologyInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.api.dto.es.SpecialtyInspectionDto;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.model.es.BaseSampleEsModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.EnumMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@RefreshScope
public class ElasticSearchSampleServiceImpl implements ElasticSearchSampleService {
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Value("${es.indexName}")
    private String indexName;

    private List<SortBuilder<?>> extractedSorts(SampleEsQuery dto) {
        List<SortBuilder<?>> sortBuilders = new ArrayList<>();

        final List<SampleEsQuery.Sort> sorts = dto.getSorts();

        if (CollectionUtils.isNotEmpty(sorts)) {
            sorts.stream().map(m -> SortBuilders.fieldSort(m.getFiledName()).order(SortOrder.fromString(m.getOrder())))
                    .filter(Objects::nonNull).forEach(sortBuilders::add);
        }
        return sortBuilders;
    }

    @Override
    public ScrollPage<BaseSampleEsModelDto> searchAfter(List<Object> searchAfter, SampleEsQuery dto) {
        log.info("从ES查询样本 入参 [{}] 操作人 [{}]", JSON.toJSONString(dto), Objects.nonNull(LoginUserHandler.getUnsafe()) ? LoginUserHandler.getUnsafe().getNickname() : "UNKNOW");

        final BoolQueryBuilder builder = QueryBuilders.boolQuery();

        // 构建查询条件
        extracted(dto, builder);

        // 排序
        final List<SortBuilder<?>> sortBuilders = extractedSorts(dto);

        final int maxPageSize = 10000;

        final NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder()
                .withPageable(PageRequest.of(0, Math
                        .min(Math.min(ObjectUtils.defaultIfNull(dto.getPageSize(), maxPageSize), maxPageSize), maxPageSize)))
                .withQuery(builder);

        // searchAfter 必须有一个排序
        sortBuilders.add(SortBuilders.fieldSort("_id").order(SortOrder.ASC));

        for (SortBuilder<?> e : sortBuilders) {
            nativeSearchQueryBuilder.withSort(e);
        }

        // 检索数据
        final NativeSearchQuery query = nativeSearchQueryBuilder.build();

        if (CollectionUtils.isEmpty(searchAfter)) {
            searchAfter = null;
        }

        query.setSearchAfter(searchAfter);

        log.info(builder.toString());

        final SearchHits<JSONObject> hits = elasticsearchRestTemplate.search(query, JSONObject.class, IndexCoordinates.of(indexName));

        final ScrollPage<BaseSampleEsModelDto> page = new ScrollPage<>();
        page.setData(toRawClass(hits.getSearchHits().stream().map(SearchHit::getContent).collect(Collectors.toList())));
        page.setTotal(0L);
        page.setPageSize(page.getPageSize());
        page.setPageNum(0);
        page.setSearchAfter(Collections.emptyList());

        if (CollectionUtils.isNotEmpty(hits.getSearchHits())) {
            page.setSearchAfter(hits.getSearchHits().get(hits.getSearchHits().size() - 1).getSortValues());
        }

        return page;
    }

    @Override
    public void insert(BaseSampleEsModelDto dto) {
        if (Objects.isNull(dto)) {
            return;
        }

        final JSONObject doc = JSON.parseObject(JSON.toJSONString(dto));
        doc.put("id", dto.getApplySampleId());

        elasticsearchRestTemplate.save(doc, IndexCoordinates.of(indexName));

        log.trace("新增样本到 ES 成功 {}", JSON.toJSONString(dto));
    }

    @Override
    public List<BaseSampleEsModelDto> selectSamples(SampleEsQuery dto) {
        log.info("查询样本数量入参 [{}]", JSON.toJSONString(dto));

        final List<BaseSampleEsModelDto> list = new LinkedList<>();

        List<Object> searchAfter = null;

        do {

            final ScrollPage<BaseSampleEsModelDto> page = searchAfter(searchAfter, dto);
            searchAfter = page.getSearchAfter();

            if (CollectionUtils.isEmpty(page.getData())) {
                break;
            }

            list.addAll(page.getData());

        } while (!Thread.currentThread().isInterrupted());

        log.info("查询样本数量 [{}]", list.size());

        return list;
    }

    @Nullable
    @Override
    public BaseSampleEsModelDto selectByApplySampleId(long applySampleId) {
        final JSONObject doc = elasticsearchRestTemplate.get(String.valueOf(applySampleId), JSONObject.class, IndexCoordinates.of(indexName));
        if (Objects.isNull(doc)) {
            return null;
        }
        return toRawClass(List.of(doc)).iterator().next();
    }

    @Override
    public void updateByApplySampleId(long applySampleId, BaseSampleEsModelDto dto) {

        // 修改非 null 字段
        final String data = JSON.toJSONString(dto);
        final Document document = Document.parse(data);

        // 如果没有要修改的 那么直接退出
        if (CollectionUtils.isEmpty(document.keySet())) {
            return;
        }

        document.setId(String.valueOf(applySampleId));

        elasticsearchRestTemplate.update(
                UpdateQuery.builder(String.valueOf(applySampleId)).withDocument(document).build(),
                IndexCoordinates.of(indexName));

        log.trace("ES 数据更新成功 , 更新数据 [{}]", JSON.toJSONString(data));
    }

    @Override
    public void deleteByApplySampleId(long applySampleId) {
        elasticsearchRestTemplate.delete(String.valueOf(applySampleId), IndexCoordinates.of(indexName));
    }

    @Override
    public long count(SampleEsQuery query) {
        final BoolQueryBuilder builder = QueryBuilders.boolQuery();

        // 构建查询条件
        extracted(query, builder);
        final NativeSearchQuery build = new NativeSearchQueryBuilder().withQuery(builder).build();
        return elasticsearchRestTemplate.count(build, IndexCoordinates.of(indexName));
    }

    /**
     * 转化为原始类型
     */
    @SuppressWarnings("unchecked")
    private List<BaseSampleEsModelDto> toRawClass(List<JSONObject> m) {

        if (CollectionUtils.isEmpty(m)) {
            return Collections.emptyList();
        }

        final Map<ItemTypeEnum, List<JSONObject>> itemTypes =
                m.stream().collect(Collectors.groupingBy(k -> ItemTypeEnum.getByName(k.getString("itemType"))));
        final List<BaseSampleEsModelDto> list = new ArrayList<>(m.size());

        final Map<ItemTypeEnum, Class<?>> classes = new EnumMap<>(ItemTypeEnum.class);
        classes.put(ItemTypeEnum.GENETICS, GeneticsInspectionDto.class);
        classes.put(ItemTypeEnum.ROUTINE, RoutineInspectionDto.class);
        classes.put(ItemTypeEnum.INFECTION, InfectionInspectionDto.class);
        classes.put(ItemTypeEnum.MICROBIOLOGY, MicrobiologyInspectionDto.class);
        classes.put(ItemTypeEnum.SPECIALTY, SpecialtyInspectionDto.class);
        classes.put(ItemTypeEnum.OUTSOURCING, OutsourcingInspectionDto.class);
        classes.put(ItemTypeEnum.BLOOD_CULTURE, BloodCultureInspectionDto.class);
        classes.put(ItemTypeEnum.PATHOLOGY, PathologyInspectionDto.class);
        classes.put(ItemTypeEnum.OTHER, BaseSampleEsModelDto.class);

        for (var e : itemTypes.entrySet()) {
            final JSONArray objects = new JSONArray(e.getValue().size());
            objects.addAll(e.getValue());
            final Class<?> clazz = classes.getOrDefault(e.getKey(), BaseSampleEsModel.class);
            // 过滤掉 BaseSampleEsModel.class
            if (clazz == BaseSampleEsModel.class) {
                continue;
            }
            list.addAll((Collection<? extends BaseSampleEsModelDto>) objects.toJavaList(clazz));
        }

        return list;
    }

    /**
     * 构建查询条件
     */
    private void extracted(SampleEsQuery dto, BoolQueryBuilder builder) {

        // 申请单id
        if (CollectionUtils.isNotEmpty(dto.getApplyIds())) {
            builder.must(QueryBuilders.termsQuery("applyId", dto.getApplyIds()));
        }
        // 体检单位
        if (CollectionUtils.isNotEmpty(dto.getPhysicalGroupIds())) {
            builder.must(QueryBuilders.termsQuery("physicalCompanyId", dto.getPhysicalGroupIds()));
        }

        // 主条码
        if (CollectionUtils.isNotEmpty(dto.getMasterBarcodes())) {
            builder.must(QueryBuilders.termsQuery("masterBarcode", dto.getMasterBarcodes()));
        }

        // 科室
        if (StringUtils.isNotBlank(dto.getDept())) {
            builder.must(QueryBuilders.termQuery("dept", dto.getDept()));
        }

        // 是否加急
        if (Objects.nonNull(dto.getUrgent())) {
            builder.must(QueryBuilders.termQuery("urgent", dto.getUrgent()));
        }

        // 申请单状态
        if (Objects.nonNull(dto.getApplyStatus())) {
            builder.must(QueryBuilders.termQuery("applyStatus", dto.getApplyStatus()));
        }

        // 申请单状态set
        if (CollectionUtils.isNotEmpty(dto.getApplyStatusSet())) {
            builder.must(QueryBuilders.termsQuery("applyStatus", dto.getApplyStatusSet()));
        }
        // 是否已经一次分拣 1是，0不是
        if (Objects.nonNull(dto.getIsOnePick())) {
            builder.must(QueryBuilders.termQuery("isOnePick", dto.getIsOnePick()));
        }

        // 是否已经二次分拣 1是，0不是
        if (Objects.nonNull(dto.getIsTwoPick())) {
            builder.must(QueryBuilders.termQuery("isTwoPick", dto.getIsTwoPick()));
        }
        // 是否是免疫二次分拣 1：是 0：否
        if (Objects.nonNull(dto.getIsImmunityTwoPick())) {
            builder.must(QueryBuilders.termQuery("isImmunityTwoPick", dto.getIsImmunityTwoPick()));
        }

        // 样本状态set
        if (CollectionUtils.isNotEmpty(dto.getSampleStatus())) {
            builder.must(QueryBuilders.termsQuery("sampleStatus", dto.getSampleStatus()));
        }

        // 模糊查询患者名称
        // 新增1.1.3 患者名称全匹配
        if (StringUtils.isNotBlank(dto.getPatientName())) {
            if (dto.isPatientNameCompleteMatch()){
                builder.must(QueryBuilders.termQuery("patientName", dto.getPatientName()));
            }else {
                builder.must(QueryBuilders.wildcardQuery("patientName", String.format("*%s*", dto.getPatientName())));
            }
        }

        // 年龄区间
        if (Objects.nonNull(dto.getStartPatientAge()) && Objects.nonNull(dto.getEndPatientAge())) {
            builder
                    .must(QueryBuilders.rangeQuery("patientAge").gte(dto.getStartPatientAge()).lte(dto.getEndPatientAge()));
        } else if (Objects.nonNull(dto.getStartPatientAge())) {
            // 等于
            builder.must(QueryBuilders.termQuery("patientAge", dto.getStartPatientAge()));
        }

        // 子年龄
        if (Objects.nonNull(dto.getPatientSubage())) {
            builder.must(QueryBuilders.termQuery("patientSubAge", dto.getPatientSubage()));
        }

        // 出生日期区间
        if (Objects.nonNull(dto.getStartPatientBirthday()) && Objects.nonNull(dto.getEndPatientBirthday())) {
            builder.must(QueryBuilders.rangeQuery("patientBirthday").gte(dto.getStartPatientBirthday())
                    .lte(dto.getEndPatientBirthday()));
        }

        // 身份证
        if (StringUtils.isNotBlank(dto.getPatientCard())) {
            builder.must(QueryBuilders.termQuery("patientCard", dto.getPatientCard()));
        }

        // 床号
        if (StringUtils.isNotBlank(dto.getPatientBed())) {
            builder.must(QueryBuilders.termQuery("patientBed", dto.getPatientBed()));
        }

        // 性别
        if (Objects.nonNull(dto.getPatientSex())) {
            builder.must(QueryBuilders.termQuery("patientSex", dto.getPatientSex()));
        }

        // 是否打印:1已打印，0未打印
        if (Objects.nonNull(dto.getIsPrint())) {
            builder.must(QueryBuilders.termQuery("isPrint", dto.getIsPrint()));
        }

        // 是否外送:1是，0否
        if (Objects.nonNull(dto.getIsOutsourcing())) {
            builder.must(QueryBuilders.termQuery("isOutsourcing", dto.getIsOutsourcing()));
        }

        // 就诊卡号
        if (StringUtils.isNotBlank(dto.getPatientVisitCard())) {
            builder.must(QueryBuilders.termQuery("patientVisitCard", dto.getPatientVisitCard()));
        }

        // 手机号
        if (StringUtils.isNotBlank(dto.getPatientMobile())) {
            builder.must(QueryBuilders.termQuery("patientMobile", dto.getPatientMobile()));
        }

        // 模糊查询患者住址
        if (StringUtils.isNotBlank(dto.getPatientAddress())) {
            builder.must(QueryBuilders.wildcardQuery("patientAddress", String.format("*%s*", dto.getPatientAddress())));
        }

        // 申请单类型
        if (CollectionUtils.isNotEmpty(dto.getApplyTypes())) {
            builder.must(QueryBuilders.termsQuery("applyTypeCode.keyword", dto.getApplyTypes()));
        }

        // 临床诊断
        if (StringUtils.isNotBlank(dto.getDiagnosis())) {
            builder.must(QueryBuilders.termQuery("diagnosis", dto.getDiagnosis()));
        }

        // 送检医生 批量
        if (CollectionUtils.isNotEmpty(dto.getSendDoctorCodes())) {
            builder.must(QueryBuilders.termsQuery("sendDoctorCode", dto.getSendDoctorCodes()));
        }

        // 申请时间区间
        if (Objects.nonNull(dto.getStartApplyDate()) && Objects.nonNull(dto.getEndApplyDate())) {
            builder.must(QueryBuilders.rangeQuery("applyDate").from(dto.getStartApplyDate().getTime())
                    .to(dto.getEndApplyDate().getTime()));
        }

        // 二次分拣时间
        if (Objects.nonNull(dto.getStartTwoPickDate()) && Objects.nonNull(dto.getEndTwoPickDate())) {
            builder.must(QueryBuilders.rangeQuery("twoPickDate").from(dto.getStartTwoPickDate().getTime())
                    .to(dto.getEndTwoPickDate().getTime()));
        }

        // 采样时间区间
        if (Objects.nonNull(dto.getStartSamplingDate()) && Objects.nonNull(dto.getEndSamplingDate())) {
            builder.must(QueryBuilders.rangeQuery("sampleDate").from(dto.getStartSamplingDate().getTime())
                    .to(dto.getEndSamplingDate().getTime()));
        }

        // 备注 模糊
        if (StringUtils.isNotBlank(dto.getRemark())) {
            builder.must(QueryBuilders.wildcardQuery("remark", String.format("*%s*", dto.getRemark())));
        }

        // 申请单来源
        if (Objects.nonNull(dto.getSource())) {
            builder.must(QueryBuilders.termQuery("source", dto.getSource()));
        }

        // 供应商
        if (StringUtils.isNotBlank(dto.getSupplier())) {
            builder.must(QueryBuilders.termQuery("supplier", dto.getSupplier()));
        }

        // 送检机构id 批量
        if (CollectionUtils.isNotEmpty(dto.getHspOrgIds())) {
            builder.must(QueryBuilders.termsQuery("hspOrgId", dto.getHspOrgIds()));
        }

        // 送检机构编码 批量
        if (CollectionUtils.isNotEmpty(dto.getHspOrgCodes())) {
            builder.must(QueryBuilders.termsQuery("hspOrgCode", dto.getHspOrgCodes()));
        }
        // 原始送检机构
        if (StringUtils.isNotBlank(dto.getOriginalOrgName())) {
            builder.must(QueryBuilders.wildcardQuery("originalOrgName.keyword",
                    String.format("*%s*", dto.getOriginalOrgName())));
        }

        // ------------------------------------样本相关--------------------------------------------

        // 申请单样本id 批量
        if (CollectionUtils.isNotEmpty(dto.getApplySampleIds())) {
            builder.must(QueryBuilders.termsQuery("applySampleId", dto.getApplySampleIds()));
        }

        // 外部条码 批量
        if (CollectionUtils.isNotEmpty(dto.getOutBarcodes())) {
            builder.must(QueryBuilders.termsQuery("outBarcode", dto.getOutBarcodes()));
        }


        // 合并的主样本 的 条码号
        if (CollectionUtils.isNotEmpty(dto.getOutBarcodes())) {
            builder.must(QueryBuilders.termsQuery("mergeMasterBarcode", dto.getMergeBarcodes()));
        }

        // 样本条码 批量
        if (CollectionUtils.isNotEmpty(dto.getBarcodes())) {
            builder.must(QueryBuilders.termsQuery("barcode", dto.getBarcodes()));
        }

        // 条码号/外部条码号
        if (CollectionUtils.isNotEmpty(dto.getBarcodeOrOutbarcodes())) {
            BoolQueryBuilder should = QueryBuilders.boolQuery();
            should.should().add(QueryBuilders.termsQuery("barcode", dto.getBarcodeOrOutbarcodes()));
            should.should().add(QueryBuilders.termsQuery("outBarcode", dto.getBarcodeOrOutbarcodes()));
            builder.must(should);
        }

        // 专业组id 批量
        if (CollectionUtils.isNotEmpty(dto.getGroupIds())) {
            builder.must(QueryBuilders.termsQuery("groupId", dto.getGroupIds()));
        }

        // 样本id 批量
        if (CollectionUtils.isNotEmpty(dto.getSampleIds())) {
            builder.must(QueryBuilders.termsQuery("sampleId", dto.getSampleIds()));
        }

        // 专业小组
        if (CollectionUtils.isNotEmpty(dto.getInstrumentGroupIds())) {
            builder.must(QueryBuilders.termsQuery("instrumentGroupId", dto.getInstrumentGroupIds()));
        }
        // 检验人ID
        if (Objects.nonNull(dto.getTesterId())) {
            builder.must(QueryBuilders.termQuery("testerId", dto.getTesterId()));
        }
        // 签收时间区间
        if (Objects.nonNull(dto.getStartSignDate()) && Objects.nonNull(dto.getEndSignDate())) {
            builder.must(QueryBuilders.rangeQuery("signDate").from(dto.getStartSignDate().getTime())
                    .to(dto.getEndSignDate().getTime()));
        }
        // 检验时间区间
        if (Objects.nonNull(dto.getStartTestDate()) && Objects.nonNull(dto.getEndTestDate())) {
            builder.must(QueryBuilders.rangeQuery("testDate").from(dto.getStartTestDate().getTime())
                    .to(dto.getEndTestDate().getTime()));
        }

//        // 样本备注 模糊
//        if (StringUtils.isNotBlank(dto.getSampleRemark())) {
//            builder.must(QueryBuilders.queryStringQuery("\"" + QueryParser.escape(dto.getSampleRemark()) + "\"")
//                .field("sampleRemark"));
//        }
//        // 结果备注 模糊
//        if (StringUtils.isNotBlank(dto.getResultRemark())) {
//            builder.must(QueryBuilders.queryStringQuery("\"" + QueryParser.escape(dto.getResultRemark()) + "\"")
//                .field("resultRemark"));
//        }

        // 一审人id 批量
        if (CollectionUtils.isNotEmpty(dto.getOneCheckerIds())) {
            builder.must(QueryBuilders.termsQuery("oneCheckerId", dto.getOneCheckerIds()));
        }

        // 一审时间区间
        if (Objects.nonNull(dto.getStartOneCheckDate()) && Objects.nonNull(dto.getEndOneCheckDate())) {
            builder.must(QueryBuilders.rangeQuery("oneCheckDate").from(dto.getStartOneCheckDate().getTime())
                    .to(dto.getEndOneCheckDate().getTime()));
        }

        // 终审人id 批量
        if (CollectionUtils.isNotEmpty(dto.getFinalCheckerIds())) {
            builder.must(QueryBuilders.termsQuery("finalCheckerId", dto.getFinalCheckerIds()));
        }

        // 终审时间
        if (Objects.nonNull(dto.getStartFinalCheckDate()) && Objects.nonNull(dto.getEndFinalCheckDate())) {
            builder.must(QueryBuilders.rangeQuery("finalCheckDate").from(dto.getStartFinalCheckDate().getTime())
                    .to(dto.getEndFinalCheckDate().getTime()));
        }

        // 项目类型
        if (CollectionUtils.isNotEmpty(dto.getItemTypes())) {
            builder.must(QueryBuilders.termsQuery("itemType", dto.getItemTypes()));
        }

        // 机构id
        if (Objects.nonNull(dto.getOrgId())) {
            builder.must(QueryBuilders.termQuery("orgId", dto.getOrgId()));
        }

        // 是否禁用
        if (Objects.nonNull(dto.getIsDisabled())) {
            builder.must(QueryBuilders.termQuery("isDisabled", dto.getIsDisabled()));
        }

        // 是否归档
        if (Objects.nonNull(dto.getIsArchive())) {
            builder.must(QueryBuilders.termQuery("isArchive", dto.getIsArchive()));
        }

        // 创建人id | 录入人id 批量
        if (CollectionUtils.isNotEmpty(dto.getCreatorIds())) {
            builder.must(QueryBuilders.termsQuery("creatorId", dto.getCreatorIds()));
        }

        // 创建时间 | 录入时间 区间
        if (Objects.nonNull(dto.getStartCreateDate()) && Objects.nonNull(dto.getEndCreateDate())) {
            builder.must(QueryBuilders.rangeQuery("createDate").from(dto.getStartCreateDate().getTime())
                    .to(dto.getEndCreateDate().getTime()));
        }

        // 外送机构
        final Set<Long> exportOrgIds = dto.getExportOrgIds();
        if (CollectionUtils.isNotEmpty(exportOrgIds)) {
            builder.must(QueryBuilders.termsQuery("exportOrgId", exportOrgIds));
        }

        if (CollectionUtils.isNotEmpty(dto.getNotnullFields())) {
            for (String notnullField : dto.getNotnullFields()) {
                builder.must(QueryBuilders.existsQuery(notnullField));
            }
        }

        if (Objects.nonNull(dto.getIsAudit()) && Objects.equals(dto.getIsAudit(), YesOrNoEnum.YES.getCode())) {
            builder.must(QueryBuilders.termQuery("sampleStatus", SampleStatusEnum.AUDIT.getCode()));
        }

        if (Objects.nonNull(dto.getIsAudit()) && Objects.equals(dto.getIsAudit(), YesOrNoEnum.NO.getCode())) {
            builder.mustNot(QueryBuilders.termQuery("sampleStatus", SampleStatusEnum.AUDIT.getCode()));
        }
        // 过滤排除的样本状态
        if (CollectionUtils.isNotEmpty(dto.getExcludeSampleStatus())) {
            builder.mustNot(QueryBuilders.termsQuery("sampleStatus", dto.getExcludeSampleStatus()));
        }

        // 检验项目专业组
        if (CollectionUtils.isNotEmpty(dto.getTestItemGroupIds())) {
            builder.must(QueryBuilders.nestedQuery("testItems",
                    QueryBuilders.termsQuery("testItems.groupId", dto.getTestItemGroupIds()), ScoreMode.None));
        }

        // 检验项目id
        if (CollectionUtils.isNotEmpty(dto.getTestItemIds())) {
            builder.must(QueryBuilders.nestedQuery("testItems",
                    QueryBuilders.termsQuery("testItems.testItemId", dto.getTestItemIds()), ScoreMode.None));
        }

        // 检验项目编码
        if (CollectionUtils.isNotEmpty(dto.getTestItemCodes())) {
            builder.must(QueryBuilders.nestedQuery("testItems",
                    QueryBuilders.termsQuery("testItems.testItemCode", dto.getTestItemCodes()), ScoreMode.None));
        }

        if (CollectionUtils.isNotEmpty(dto.getReportItemCodes())) {
            builder.must(QueryBuilders.nestedQuery("reportItems",
                    QueryBuilders.termsQuery("reportItems.reportItemCode.keyword", dto.getReportItemCodes()), ScoreMode.None));
        }

        // 样本检验项目终止检验状态
        if (CollectionUtils.isNotEmpty(dto.getStopStatus())) {
            builder.must(QueryBuilders.nestedQuery("testItems",
                    QueryBuilders.termsQuery("testItems.stopStatus", dto.getStopStatus()), ScoreMode.None));
        }
        if (CollectionUtils.isNotEmpty(dto.getExcludeStopStatus())) {
            builder.mustNot(QueryBuilders.nestedQuery("testItems",
                    QueryBuilders.termsQuery("testItems.stopStatus", dto.getExcludeStopStatus()), ScoreMode.None));
        }

        // 检验项目急诊状态
        if (Objects.nonNull(dto.getItemUrgent())) {
            builder.must(QueryBuilders.nestedQuery("testItems",
                    QueryBuilders.termQuery("testItems.urgent", dto.getItemUrgent()), ScoreMode.None));
        }

        // 细菌备注编码
        if (CollectionUtils.isNotEmpty(dto.getGermRemarkCodes())) {
            BoolQueryBuilder should = QueryBuilders.boolQuery();
            should.should().add(QueryBuilders.nestedQuery("germs",
                    QueryBuilders.termsQuery("germs.germRemarkCode.keyword", dto.getGermRemarkCodes()), ScoreMode.None));
            should.should().add(QueryBuilders.nestedQuery("germs",
                    QueryBuilders.termsQuery("germs.germRemarkCode", dto.getGermRemarkCodes()), ScoreMode.None));
            builder.must(should);
        }

        // 更新时间
        if (Objects.nonNull(dto.getUpdateDateBegin()) && Objects.nonNull(dto.getUpdateDateEnd())) {
            builder.must(QueryBuilders.rangeQuery("updateDate").from(dto.getUpdateDateBegin().getTime())
                    .to(dto.getUpdateDateEnd().getTime()));
        }


        // 终审时间 或 创建时间|录入时间
        if (Objects.nonNull(dto.getStartFinalCheckOrCreateDate())
                && Objects.nonNull(dto.getEndFinalCheckOrCreateDate())) {
            RangeQueryBuilder finalCheckDate = QueryBuilders.rangeQuery("finalCheckDate")
                    .from(dto.getStartFinalCheckOrCreateDate().getTime()).to(dto.getEndFinalCheckOrCreateDate().getTime());
            RangeQueryBuilder createDate = QueryBuilders.rangeQuery("createDate")
                    .from(dto.getStartFinalCheckOrCreateDate().getTime()).to(dto.getEndFinalCheckOrCreateDate().getTime());
            BoolQueryBuilder temp = QueryBuilders.boolQuery();
            temp.should(finalCheckDate);
            temp.should(createDate);
            builder.must(temp);
        }

        // 微生物样本
        if (dto instanceof MicrobiologySampleEsQuery) {
            List<String> resultCodes = ((MicrobiologySampleEsQuery) dto).getResultCodes();
            if (CollectionUtils.isNotEmpty(resultCodes)) {
                builder.must(QueryBuilders.nestedQuery("results",
                        QueryBuilders.termsQuery("results.resultCode", resultCodes), ScoreMode.None));
            }

            List<Long> germIds = ((MicrobiologySampleEsQuery) dto).getGermIds();
            if (CollectionUtils.isNotEmpty(germIds)) {
                builder.must(QueryBuilders.nestedQuery("germs", QueryBuilders.termsQuery("germs.germId", germIds),
                        ScoreMode.None));
            }
            List<Long> medicineIds = ((MicrobiologySampleEsQuery) dto).getMedicineIds();
            if (CollectionUtils.isNotEmpty(medicineIds)) {
                builder.must(QueryBuilders.nestedQuery("germs",
                        QueryBuilders.nestedQuery("germs.medicines",
                                QueryBuilders.termsQuery("germs.medicines.medicineId", medicineIds), ScoreMode.None),
                        ScoreMode.None));
            }
            List<String> germRemarkCodes = ((MicrobiologySampleEsQuery) dto).getGermRemarkCodes();
            if (CollectionUtils.isNotEmpty(germRemarkCodes)) {
                builder.must(QueryBuilders.nestedQuery("germs",
                        QueryBuilders.termsQuery("germs.germRemarkCode.keyword", germRemarkCodes), ScoreMode.None));
            }
        }

        // 阴阳性结果
        if (CollectionUtils.isNotEmpty(dto.getYinAndYang())) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.should(QueryBuilders.termsQuery("reportItems.result.keyword", dto.getYinAndYang()));
            builder.must(QueryBuilders.nestedQuery("reportItems", boolQueryBuilder, ScoreMode.None));
        }

    }
}
