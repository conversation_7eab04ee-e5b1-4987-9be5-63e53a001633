package com.labway.lims.apply.vo.openreport;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 遗传
 */
@Getter
@Setter
public class OpenReportGeneticsInspectionVoOpenReport extends OpenReportBaseSampleEsModelVo {
    /**
     * 一次审核
     */
    private Long oneCheckerId;
    /**
     * 一次审核人名称
     */
    private String oneCheckerName;

    /**
     * 一次审核时间
     */
    private Date oneCheckDate;

    /**
     * 二次审核人
     */
    private Long twoCheckerId;
    /**
     * 二次审核人名称
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    private Date twoCheckDate;
    /**
     * 细胞数
     */
    private String cellCount;
    /**
     * 分析细胞数
     */
    private String analyseCellCount;
    /**
     * 标本情况
     */
    private String sampleSituation;
    /**
     * 核型
     */
    private String karyotype;
    /**
     * 显带方法
     */
    private String bandingMethod;
    /**
     * 显带水平
     */
    private String bandingLevel;
    /**
     * 分析意见
     */
    private String analyticalOpinion;
    /**
     * 原始图像1
     */
    private String karyotypeOriginalImg1;
    /**
     * 原始图像2
     */
    private String karyotypeOriginalImg2;
    /**
     * 原始图像3
     */
    private String karyotypeOriginalImg3;
    /**
     * 分析图像1
     */
    private String karyotypeImg1;
    /**
     * 分析图像2
     */
    private String karyotypeImg2;
    /**
     * 分析图像3
     */
    private String karyotypeImg3;

}
