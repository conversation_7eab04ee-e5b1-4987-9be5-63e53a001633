package com.labway.lims.apply.service.chain.material.receive.invalid;

import com.labway.lims.api.enums.apply.MaterialReceiveRecordStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRecordDto;
import com.labway.lims.apply.api.service.MaterialInventoryCheckService;
import com.labway.lims.apply.api.service.MaterialInventoryService;
import com.labway.lims.apply.api.service.MaterialReceiveRecordService;
import com.labway.lims.base.api.dto.GroupMaterialDto;
import com.labway.lims.base.api.service.GroupMaterialService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 领用作废 检查参数
 *
 * <AUTHOR>
 * @since 2023/5/9 10:06
 */
@Slf4j
@Component
public class MaterialReceiveInvalidCheckParamCommand implements Command {

    @Resource
    private MaterialReceiveRecordService materialReceiveRecordService;
    @Resource
    private MaterialInventoryService materialInventoryService;
    @DubboReference
    private GroupMaterialService groupMaterialService;
    @Resource
    private MaterialInventoryCheckService materialInventoryCheckService;

    @Override
    public boolean execute(Context context) throws Exception {
        final MaterialReceiveInvalidContext from = MaterialReceiveInvalidContext.from(context);
        var receiveId = from.getReceiveId();
        var user = from.getUser();

        String check = materialInventoryCheckService.isCheck(user.getGroupId());
        if (StringUtils.isNotBlank(check)) {
            throw new LimsException(String.format("作废失败: [%s] 存在盘点中记录", check));
        }
        // 对应领用记录
        final MaterialReceiveRecordDto receiveRecordDto = materialReceiveRecordService.selectByReceiveId(receiveId);
        if (Objects.isNull(receiveRecordDto)) {
            throw new LimsException(String.format("作废失败: [%s] 对应领用记录不存在", receiveId));
        }
        if (!Objects.equals(receiveRecordDto.getGroupId(), user.getGroupId())) {
            throw new LimsException(String.format("作废失败: [%s] 对应领用记录不在当前用户所在专业组下", receiveId));
        }

        if (Objects.equals(receiveRecordDto.getStatus(), MaterialReceiveRecordStatusEnum.INVALID.getCode())) {
            throw new LimsException("作废失败: 不可重复作废");
        }

        // 领用记录对应库存记录
        final MaterialInventoryDto inventoryDto =
            materialInventoryService.selectByInventoryId(receiveRecordDto.getInventoryId());
        if (Objects.isNull(inventoryDto)) {
            throw new LimsException(String.format("作废失败: [%s] 对应领用记录数据有误,其对应库存不存在", receiveId));
        }

        // 库存物料 对应 专业组物料关联关系
        final GroupMaterialDto groupMaterialDto =
            groupMaterialService.selectByGroupIdAndMaterialId(user.getGroupId(), inventoryDto.getMaterialId());
        if (Objects.isNull(groupMaterialDto)) {
            throw new LimsException("作废失败: 作废库存物料在专业组下不存在");
        }

        // 补充 物料领用记录、库存信息、专业组物料信息
        from.put(MaterialReceiveInvalidContext.MATERIAL_RECEIVE_RECORD, receiveRecordDto);
        from.put(MaterialReceiveInvalidContext.MATERIAL_INVENTORY, inventoryDto);
        from.put(MaterialReceiveInvalidContext.GROUP_MATERIAL, groupMaterialDto);

        return CONTINUE_PROCESSING;
    }
}
