package com.labway.lims.apply.service.chain.apply.update;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto.EventType;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.service.chain.pick.two.TwoPickContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 发送消息到mq
 */
@Slf4j
@Component
public class UpdateRabbitMQCommand implements Command {
    @Resource
    private RabbitTemplate rabbitTemplate;

    public static final String EXCHANGE = RabbitMQService.EXCHANGE;
    public static final String ROUTING_KEY = "sample_change_key";

    @Override
    public boolean execute(Context c) throws Exception {





        return CONTINUE_PROCESSING;
    }

}
