package com.labway.lims.apply.service.chain.pick.two.immunity;

import com.labway.lims.apply.service.chain.pick.two.TwoPickCheckApplySampleStatusCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickCheckInstrumentGroupCandoCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickCheckParamCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickFillInfoCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickFlowCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickIdelRackLogicCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickInstrumentGroupCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickLimitCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickRabbitMQCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickRedisMarkCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickUpdateSampleInfoCommand;
import com.labway.lims.apply.service.chain.pick.two.TwoPickUrgentCommand;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 二次分拣（免疫）
 */
@Component
public class ImmunityTwoPickChain extends ChainBase implements InitializingBean {
    @Resource
    private TwoPickLimitCommand twoPickLimitCommand;
    @Resource
    private TwoPickCheckApplySampleStatusCommand twoPickCheckApplySampleStatusCommand;
    @Resource
    private TwoPickCheckParamCommand twoPickCheckParamCommand;
    @Resource
    private TwoPickFillInfoCommand twoPickFillInfoCommand;
    @Resource
    private TwoPickInstrumentGroupCommand twoPickInstrumentGroupCommand;
    @Resource
    private TwoPickCheckInstrumentGroupCandoCommand twoPickCheckInstrumentGroupCandoCommand;
    @Resource
    private TwoPickUrgentCommand twoPickUrgentCommand;
    @Resource
    private ImmunityTwoPickCommand immunityTwoPickCommand;
    @Resource
    private TwoPickCommand twoPickCommand;
    @Resource
    private TwoPickUpdateSampleInfoCommand twoPickUpdateSampleInfoCommand;
    @Resource
    private ImmunityTwoPickRemoveRackSpaceCommand immunityTwoPickRemoveRackSpaceCommand;
    @Resource
    private ImmunityTwoPickTransformCommand immunityTwoPickTransformCommand;
    @Resource
    private TwoPickRedisMarkCommand twoPickRedisMarkCommand;
    @Resource
    private TwoPickIdelRackLogicCommand twoPickIdelRackLogicCommand;
    @Resource
    private TwoPickFlowCommand twoPickFlowCommand;
    @Resource
    private TwoPickRabbitMQCommand twoPickRabbitMQCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 限流
        addCommand(twoPickLimitCommand);

        // 判断是否已停止或已终止
        addCommand(twoPickCheckApplySampleStatusCommand);

        // 校验参数
        addCommand(twoPickCheckParamCommand);

        // 填充信息
        addCommand(twoPickFillInfoCommand);

        // 获取到要分拣的专业小组
        addCommand(twoPickInstrumentGroupCommand);

        // 判断专业小组能否做这个样本
        addCommand(twoPickCheckInstrumentGroupCandoCommand);

        // 如果有加急项目，那么需要复制出一个样本出来
        addCommand(twoPickUrgentCommand);

        // 免疫二次分拣
        addCommand(immunityTwoPickCommand);

        // 分拣
        addCommand(twoPickCommand);

        // 修改样本信息
        addCommand(twoPickUpdateSampleInfoCommand);

        // 删除试管架占用
        addCommand(immunityTwoPickRemoveRackSpaceCommand);

        // 如果涉及到组间交接，复制逻辑试管架 进行组间交接
        addCommand(immunityTwoPickTransformCommand);

        // redis 标记
        addCommand(twoPickRedisMarkCommand);

        // 逻辑试管架环节标记为结束
        addCommand(twoPickIdelRackLogicCommand);

        // 条码环节
        addCommand(twoPickFlowCommand);

        // 发送到 mq
        addCommand(twoPickRabbitMQCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);

    }
}
