package com.labway.lims.apply.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class DeleteImagesVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 申请单样本图片id
     */
    @NotNull(message = "申请单样本图片id不能为空")
    private Long applySampleImageId;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 图片地址
     */
    private String imgUrl;

}
