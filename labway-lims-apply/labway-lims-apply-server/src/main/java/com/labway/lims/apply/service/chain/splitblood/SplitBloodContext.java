package com.labway.lims.apply.service.chain.splitblood;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.BloodOneSplitDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.service.chain.StopWatchContext;
import com.labway.lims.base.api.dto.TestItemDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@SuppressWarnings("unchecked")
public class SplitBloodContext extends StopWatchContext {

    /**
     * 是否支持分血
     */
    static final String SPLIT_BLOOD_SUPPORTED = "SPLIT_BLOOD_SUPPORTED_" + IdUtil.objectId();

    /**
     * 申请单样本
     */
    static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    /**
     * 申请单样本项目
     */
    static final String APPLY_SAMPLE_ITEMS = "APPLY_SAMPLE_ITEMS_" + IdUtil.objectId();

    /**
     * 检验项目
     */
    static final String TEST_ITEMS = "TEST_ITEMS_" + IdUtil.objectId();

    /**
     * 所在试管架位置
     */
    static final String RACK_LOGIC_SPACE = "RACK_LOGIC_SPACE_" + IdUtil.objectId();

    /**
     * 逻辑试管架
     */
    static final String RACK_LOGIC = "RACK_LOGIC_" + IdUtil.objectId();

    /**
     * 分血后的ID
     */
    public static final String APPLY_SAMPLE_IDS = "APPLY_SAMPLE_IDS_" + IdUtil.objectId();

    /**
     * 分血后的信息
     */
    public static final String APPLY_SAMPLE_INFOS = "APPLY_SAMPLE_INFOS_" + IdUtil.objectId();

    private long applySampleId;


    public static SplitBloodContext from(Context context) {
        return (SplitBloodContext) context;
    }

    /**
     * 要分血的样本
     */
    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }

    /**
     * key: testItemCode
     */
    public Map<String, TestItemDto> getTestItems() {
        return (Map<String, TestItemDto>) get(TEST_ITEMS);
    }

    /**
     * 申请单下所有项目
     */
    public List<ApplySampleItemDto> getApplySampleItems() {
        return (List<ApplySampleItemDto>) get(APPLY_SAMPLE_ITEMS);
    }

    /**
     * 所在的位置
     */
    public RackLogicSpaceDto getRackLogicSpace() {
        return (RackLogicSpaceDto) get(RACK_LOGIC_SPACE);
    }

    /**
     * 所在的逻辑试管架
     */
    public RackLogicDto getRackLogic() {
        return (RackLogicDto) get(RACK_LOGIC);
    }

    /**
     * 分血出来的样本
     */
    public List<Long> getApplySampleIds() {
        return (List<Long>) get(APPLY_SAMPLE_IDS);
    }

    /**
     * 是否支持分血
     */
    public boolean isSupportedSplitBlood() {
        return containsKey(SPLIT_BLOOD_SUPPORTED)
                && (boolean) get(SPLIT_BLOOD_SUPPORTED);
    }

    public List<BloodOneSplitDto> getBloodOneSplitInfos() {
        return (List<BloodOneSplitDto>) get(APPLY_SAMPLE_INFOS);
    }


    @Override
    protected String getWatcherName() {
        return "分血";
    }

}
