package com.labway.lims.apply.service.chain.apply.add;

import com.alibaba.excel.util.BooleanUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.labway.lims.api.BarcodeUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.BarcodeSettingDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.enums.BarcodeSettingEnum;
import com.labway.lims.base.api.service.BarcodeSettingService;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 创建申请单
 */
@Slf4j
@Component
public class CreateApplyCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private BarcodeUtils barcodeUtils;
    @DubboReference
    private BarcodeSettingService barcodeSettingService;
    @Resource
    private ApplyService applyService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @Override
    public boolean execute(Context c) throws Exception {
        final AddApplyContext from = AddApplyContext.from(c);
        final TestApplyDto testApply = from.getTestApply();
        final LoginUserHandler.User user = from.getUser();
        final HspOrganizationDto hspOrganization = from.getHspOrganization();
        final Date now = new Date();

        String masterBarcode = from.getSkipMasterBarcode() ? testApply.getMasterBarcode() : this.getMasterBarcode(hspOrganization.getHspOrgCode(), testApply.getMasterBarcode());

        // 创建申请单
        ApplyDto apply = new ApplyDto();
        apply.setApplyId(snowflakeService.genId());
        apply.setMasterBarcode(masterBarcode);
        apply.setPatientName(testApply.getPatientName());

        // 年龄
        final Integer patientAge = testApply.getPatientAge();
        apply.setPatientAge(ObjectUtils.defaultIfNull(patientAge, NumberUtils.INTEGER_ZERO));
        apply.setPatientSubage(ObjectUtils.defaultIfNull(testApply.getPatientSubage(), NumberUtils.INTEGER_ZERO));
        apply.setPatientSubageUnit(
                ObjectUtils.defaultIfNull(testApply.getPatientSubageUnit(), PatientSubAgeUnitEnum.MONTH.getValue()));
        final Date patientBirthday = testApply.getPatientBirthday();
        if (Objects.nonNull(testApply.getPatientBirthday())) {
            apply.setPatientBirthday(patientBirthday);
        }

        apply.setPatientCard(StringUtils.defaultString(testApply.getPatientCard()));
        apply.setPatientCardType(StringUtils.defaultString(testApply.getPatientCardType()));
        apply.setPatientBed(StringUtils.defaultString(apply.getPatientBed()));
        apply.setPatientSex(testApply.getPatientSex());
        apply.setPatientVisitCard(StringUtils.defaultString(testApply.getPatientVisitCard()));
        apply.setPatientMobile(StringUtils.defaultString(testApply.getPatientMobile()));
        apply.setApplyTypeCode(StringUtils.defaultString(testApply.getApplyTypeCode()));
        apply.setApplyTypeName(StringUtils.defaultString(testApply.getApplyTypeName()));
        apply.setRemark(StringUtils.defaultString(testApply.getRemark()));
        apply.setSampleCount(ObjectUtils.defaultIfNull(testApply.getSampleCount(), NumberUtils.INTEGER_ONE));
        apply.setSampleProperty(StringUtils.defaultString(testApply.getSampleProperty()));
        apply.setSamplePropertyCode(StringUtils.defaultString(testApply.getSamplePropertyCode()));
        apply.setDept(StringUtils.defaultString(testApply.getDept()));
        apply.setDiagnosis(StringUtils.defaultString(StringUtils.defaultIfBlank(testApply.getClinicalDiagnosis(), testApply.getDiagnosis())));
        apply.setSendDoctorName(StringUtils.defaultIfBlank(testApply.getSendDoctor(), StringUtils.defaultString(testApply.getSendDoctorName())));
        apply.setSendDoctorCode(StringUtils.defaultIfBlank(testApply.getSendDoctorCode(), Strings.EMPTY));
        apply.setPatientAddress(StringUtils.defaultString(testApply.getPatientAddress()));
        apply.setHspOrgId(hspOrganization.getHspOrgId());
        apply.setHspOrgCode(hspOrganization.getHspOrgCode());
        apply.setHspOrgName(hspOrganization.getHspOrgName());
        apply.setOrgId(user.getOrgId());
        apply.setOrgName(user.getOrgName());
        apply.setUrgent(ObjectUtils.defaultIfNull(testApply.getUrgent(), UrgentEnum.NORMAL.getCode()));
        apply.setApplyDate(testApply.getApplyDate());
        apply.setSamplingDate(ObjectUtils.defaultIfNull(testApply.getSamplingDate(), apply.getApplyDate()));
        apply.setCreatorName(user.getNickname());
        apply.setCreatorId(user.getUserId());
        apply.setCreateDate(now);
        apply.setUpdaterName(user.getNickname());
        apply.setUpdaterId(user.getUserId());
        apply.setUpdateDate(now);
        apply.setPatientBed(StringUtils.defaultString(testApply.getPatientBed()));
        apply.setOutBarcode(StringUtils.defaultString(testApply.getOutBarcode()));

        // 1.启用 0.不启用 双输复核
        final Integer enableDoubleCheck = hspOrganization.getEnableDoubleCheck();
        apply.setStatus(Objects.equals(enableDoubleCheck, YesOrNoEnum.YES.getCode())
                ? ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode() : ApplyStatusEnum.WAIT_CHECK.getCode());
        apply.setIsDelete(YesOrNoEnum.NO.getCode());

        // 如果是HIS签收, 体检签收， pda签收 的直接是 已双输复核 不需要复核操作
        if (Objects.equals(testApply.getApplySource(), ApplySourceEnum.HIS)
                || Objects.equals(testApply.getApplySource(), ApplySourceEnum.PHYSICAL_SIGN)
                || Objects.equals(testApply.getApplySource(), ApplySourceEnum.PDA_SIGN)) {
            apply.setStatus(ApplyStatusEnum.DOUBLE_CHECK.getCode());
            apply.setSignDate(now);
        } else {
            apply.setSignDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        }

        apply.setSource(StringUtils.defaultString(testApply.getApplySource().toString()));
        apply.setSupplier(StringUtils.defaultString(testApply.getSupplier()));
        apply.setOriginalOrgCode(StringUtils.defaultString(testApply.getOriginalOrgCode()));
        apply.setOriginalOrgName(StringUtils.defaultString(testApply.getOriginalOrgName()));

        from.put(AddApplyContext.APPLY, apply);
        return CONTINUE_PROCESSING;
    }

    /**
     * 参数有主条码的话使用参数的， 否则重新生成
     */
    private String getMasterBarcode(String hspOrgCode, String masterBarcode) {
        // 是否是主条码规则生成的
        if (StringUtils.isNotBlank(masterBarcode)) {
            // 主条码规则生成的条码
            List<ApplyDto> applyDtos = applyService.selectByMasterBarcodes(Set.of(masterBarcode));
            return CollectionUtils.isEmpty(applyDtos) ? masterBarcode : getMasterBarcode(hspOrgCode, null);
        }

        // 主条码规则生成的条码  这个方法已经判断过是否重复了，  不需要重复判断
        List<String> masterBarcodes = barcodeSettingService.genMasterBarcodes(hspOrgCode, NumberUtils.INTEGER_ONE);
        if (CollectionUtils.isNotEmpty(masterBarcodes)) {
            return masterBarcodes.get(0);
        }

        // 自动生成的条码
        do {
            masterBarcode = barcodeUtils.genMasterBarcode();

            // 查申请单中有没有
            List<ApplyDto> applyDtos = applyService.selectByMasterBarcodes(Set.of(masterBarcode));
            if (CollectionUtils.isEmpty(applyDtos)) {
                // 查pda缓存的主条码有没有
                final String barcodeKey = String.format(BarcodeSettingEnum.BARCODE_CACHE.getRedisKey(), redisPrefix.getBasePrefix(), BarcodeSettingDto.MASTER_BARCODE_TYPE, hspOrgCode);
                Boolean member = stringRedisTemplate.opsForSet().isMember(barcodeKey, masterBarcode);
                // 没有就返回
                if (BooleanUtils.isNotTrue(member)) {
                    break;
                }
            }
        }while (true);

        return masterBarcode;
    }

}
