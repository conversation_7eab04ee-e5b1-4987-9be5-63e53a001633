package com.labway.lims.apply.service.chain.applysampleitem.disable;

import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.DisableOrEnableItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <pre>
 * CheckParamCommand
 * 参数，状态检查
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/12/10 18:47
 */
@Component("ApplySampleItem_CheckParamCommand")
public class CheckParamCommand implements Command {

    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final ApplySampleItemDisableContext context = ApplySampleItemDisableContext.from(c);

        final DisableOrEnableItemDto disableOrEnableItemDto = context.getDisableOrEnableItemDto();
        final List<DisableOrEnableItemDto.ApplySampleItem> applySampleItems = disableOrEnableItemDto.getApplySampleItems();

        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalArgumentException("请选择需要禁用/启用的项目");
        }

        final Set<Long> applySampleItemIds = applySampleItems.stream().map(
                DisableOrEnableItemDto.ApplySampleItem::getApplySampleItemId).collect(Collectors.toSet());
        final Set<Long> applySampleIds = applySampleItems.stream().map(
                DisableOrEnableItemDto.ApplySampleItem::getApplySampleId).collect(Collectors.toSet());

        // 申请单样本
        final Map<Long, ApplySampleDto> applySampleMap = applySampleService.selectByApplySampleIdsAsMap(applySampleIds);

        // 申请单样本项目
        final List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplySampleIds(applySampleIds, true);
        final Map<Long, ApplySampleItemDto> applySampleItemMapById = applySampleItemDtos.stream()
                /*.filter(e -> applySampleItemIds.contains(e.getApplySampleItemId()))*/
                .collect(Collectors.toMap(ApplySampleItemDto::getApplySampleItemId, Function.identity(), (a, b) -> a));
        final Map<Long, List<ApplySampleItemDto>> applySampleItemMapByApplySampleId = applySampleItemDtos.stream()
                .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // TODO 参数检查，把信息放到context中
        context.put(ApplySampleItemDisableContext.APPLY_SAMPLE_MAP, applySampleMap);
        context.put(ApplySampleItemDisableContext.APPLY_SAMPLE_ITEM_MAP, applySampleItemMapById);
        context.put(ApplySampleItemDisableContext.APPLY_SAMPLE_ITEMS_MAP, applySampleItemMapByApplySampleId);

        return CONTINUE_PROCESSING;
    }

}
