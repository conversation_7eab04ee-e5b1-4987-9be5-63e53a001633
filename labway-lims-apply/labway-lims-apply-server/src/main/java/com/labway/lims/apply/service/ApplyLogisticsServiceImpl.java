
package com.labway.lims.apply.service;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.compare.request.SignOutApplyInfoRequest;
import com.labway.business.center.compare.service.OutApplyInfoService;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySampleItemSourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.apply.mapper.TbApplyLogisticsMapper;
import com.labway.lims.apply.mapper.TbApplyLogisticsSampleMapper;
import com.labway.lims.apply.model.TbApplyLogistics;
import com.labway.lims.apply.model.TbApplyLogisticsSample;
import com.labway.lims.apply.service.chain.apply.add.GroupSampleCommand;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.api.service.TestItemService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class ApplyLogisticsServiceImpl implements ApplyLogisticsService {

    @Resource
    private Environment environment;

    @Resource
    private TbApplyLogisticsMapper tbApplyLogisticsMapper;
    @Resource
    private ApplyService applyService;
    @Resource
    private TbApplyLogisticsSampleMapper tbApplyLogisticsSampleMapper;
    @Resource
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private OutApplyInfoService outApplyInfoService;
    @Resource
    private ApplyLogisticsSampleItemService applyLogisticsSampleItemService;
    @DubboReference
    private TestItemService testItemService;
    @Resource
    private ApplySampleItemService applySampleItemService;
    @Resource
    private GroupSampleCommand groupSampleCommand;
    @Resource
    private ApplyLogisticsSampleService applyLogisticsSampleService;
    @Resource
    private ApplySampleService applySampleService;

    @Resource
    private EnvDetector envDetector;

    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private ItemPriceBasePackageService itemPriceBasePackageService;

    @Override
    public void add(ApplyLogisticsDto applyLogistics) {
        final TbApplyLogistics object = JSON.parseObject(JSON.toJSONString(applyLogistics), TbApplyLogistics.class);

        tbApplyLogisticsMapper.insert(object);
    }

    @Override
    public ApplyLogisticsDto selectByApplyLogisticsId(long applyLogisticsId) {
        return convert(tbApplyLogisticsMapper.selectById(applyLogisticsId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApplyInfo supplementLogisticsSampleInformation(LogisticsTestApplyDto testApply) {
        if (Objects.isNull(testApply)) {
            throw new IllegalStateException("补录物流信息失败");
        }

        final ApplyLogisticsDto applyLogistics = testApply.getApplyLogistics();
        final ApplyLogisticsSampleDto logisticsSample = testApply.getLogisticsSample();
        // 同人同天同项目 校验忽略
        testApply.setIgnoreSameItem(YesOrNoEnum.YES.getCode());
        // 检验项目限制性别 忽略校验
        testApply.setIgnoreItemLimitSex(true);

        final ApplyInfo applyInfo = applyService.addApply(testApply);
        if (Objects.isNull(applyInfo)) {
            throw new IllegalStateException("补录物流信息失败");
        }
        final LoginUserHandler.User user = LoginUserHandler.get();
        // 更新物流单信息样本
        final Integer doubleCheck = Optional.ofNullable(testApply.getSample())
            .map(LogisticsTestApplyDto.Sample::getEnableDoubleCheck).orElse(0);
        final TbApplyLogisticsSample applyLogisticsUpdate = new TbApplyLogisticsSample();
        applyLogisticsUpdate.setApplyLogisticsSampleId(logisticsSample.getApplyLogisticsSampleId());
        applyLogisticsUpdate.setStatus(Objects.equals(doubleCheck, YesOrNoEnum.YES.getCode())
            ? ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode() : ApplyStatusEnum.WAIT_CHECK.getCode());
        applyLogisticsUpdate.setUpdateDate(new Date());
        applyLogisticsUpdate.setUpdaterId(user.getUserId());
        applyLogisticsUpdate.setUpdaterName(user.getNickname());
        applyLogisticsUpdate.setApplyId(applyInfo.getApplyId());
        tbApplyLogisticsSampleMapper.updateById(applyLogisticsUpdate);

        // 记录条码环节
        recordBarcodeFlow(applyLogistics, applyInfo);

        final String orgCode = envDetector.getBusinessCenterOrgCode();
        if (StringUtils.isBlank(orgCode)) {
            throw new IllegalArgumentException(String.format("请配置 %s 参数", EnvDetector.BUSINESS_CENTER_ORG_CODE));
        }

        // 通知业务中台
        final SignOutApplyInfoRequest request = new SignOutApplyInfoRequest();
        request.setBarcode(logisticsSample.getBarcode());
        request.setHspOrgCode(testApply.getHspOrgCode());
        request.setReceiveUserCode(String.valueOf(LoginUserHandler.get().getUserId()));
        request.setReceiveUserName(LoginUserHandler.get().getNickname());
        request.setSignOrgCode(orgCode);
        request.setSignOrgName(LoginUserHandler.get().getOrgName());
        request.setSignBarcode(logisticsSample.getBarcode());
        request.setSignMainBarcode(StringUtils.EMPTY);

        log.info("开始签收物流条码  条码 [{}] 签收参数 [{}]", logisticsSample.getBarcode(), JSON.toJSONString(request));
        final Response<?> response = outApplyInfoService.signOutApplyInfoAdditional(request);
        if (Objects.isNull(response)) {
            throw new IllegalStateException("补录失败, 业务中台数据返回为空");
        }

        if (BooleanUtils.isNotTrue(Objects.equals(response.getCode(), 0))) {
            throw new IllegalStateException(String.format("补录失败, 业务中台返回 [%s]", response.getMsg()));
        }

        log.info("用户 [{}] 补录物流信息成功，applyLogisticsId: [{}] info: [{}]", user.getNickname(),
            applyLogistics.getApplyLogisticsId(), JSON.toJSONString(applyLogistics));
        return applyInfo;
    }

    private void recordBarcodeFlow(ApplyLogisticsDto applyLogistics, ApplyInfo applyInfo) {
        if (Objects.isNull(applyInfo)) {
            return;
        }
        final List<ApplyInfo.Sample> samples = applyInfo.getSamples();
        final Long applyId = applyInfo.getApplyId();

        if (CollectionUtils.isEmpty(samples)) {
            return;
        }

        if (Objects.isNull(applyLogistics)) {
            return;
        }

        final String nickname = LoginUserHandler.get().getNickname();
        final Long userId = LoginUserHandler.get().getUserId();
        final Long orgId = LoginUserHandler.get().getOrgId();
        final String orgName = LoginUserHandler.get().getOrgName();
        final Date now = new Date();

        List<SampleFlowDto> flows = new ArrayList<>();
        final LinkedList<Long> ids = snowflakeService.genIds(samples.size());

        for (final ApplyInfo.Sample sample : samples) {
            // 物流员取样
            SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(ids.pop());
            sampleFlow.setApplyId(applyId);
            sampleFlow.setApplySampleId(sample.getApplySampleId());
            sampleFlow.setBarcode(sample.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.LOGISTICS_SAMPLE.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.LOGISTICS_SAMPLE.getDesc());
            sampleFlow.setOperator(applyLogistics.getLogisticsUserName());
            sampleFlow.setOperatorId(ObjectUtils.defaultIfNull(applyLogistics.getLogisticsUserId(), 0L));
            sampleFlow.setContent("物流取样");
            sampleFlow.setCreateDate(applyLogistics.getCreateDate());
            sampleFlow.setUpdateDate(applyLogistics.getUpdateDate());
            sampleFlow.setCreatorId(userId);
            sampleFlow.setCreatorName(nickname);
            sampleFlow.setUpdaterId(userId);
            sampleFlow.setUpdaterName(nickname);
            sampleFlow.setOrgId(orgId);
            sampleFlow.setOrgName(orgName);
            sampleFlow.setIsDelete(YesOrNoEnum.NO.getCode());

            // 补录环节
            SampleFlowDto sampleFlow2 = new SampleFlowDto();
            sampleFlow2.setSampleFlowId(snowflakeService.genId());
            sampleFlow2.setApplyId(applyId);
            sampleFlow2.setApplySampleId(sample.getApplySampleId());
            sampleFlow2.setBarcode(sample.getBarcode());
            sampleFlow2.setOperateCode(BarcodeFlowEnum.SAMPLE_INFO_SUPPLEMENT.name());
            sampleFlow2.setOperateName(BarcodeFlowEnum.SAMPLE_INFO_SUPPLEMENT.getDesc());
            sampleFlow2.setOperator(nickname);
            sampleFlow2.setOperatorId(userId);
            sampleFlow2.setContent("物流补录");
            sampleFlow2.setCreateDate(now);
            sampleFlow2.setUpdateDate(now);
            sampleFlow2.setCreatorId(userId);
            sampleFlow2.setCreatorName(nickname);
            sampleFlow2.setUpdaterId(userId);
            sampleFlow2.setUpdaterName(nickname);
            sampleFlow2.setOrgId(orgId);
            sampleFlow2.setOrgName(orgName);
            sampleFlow2.setIsDelete(YesOrNoEnum.NO.getCode());

            flows.add(sampleFlow);
            flows.add(sampleFlow2);
        }

        sampleFlowService.addSampleFlows(flows);
    }

    @Override
    public boolean updateById(ApplyLogisticsDto applyLogistics) {
        if (tbApplyLogisticsMapper.updateById(convert(applyLogistics)) > 0) {
            log.info("更新物流单信息成功，applyLogisticsId: [{}] info: [{}]", applyLogistics.getApplyLogisticsId(),
                JSON.toJSONString(applyLogistics));
            return true;
        }
        return false;
    }

    @Override
    public List<SimpleLogisticsSampleDto> selectByApplyIds(Collection<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Collections.emptyList();
        }
        return tbApplyLogisticsMapper.selectByApplyIds(applyIds);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncCenterLogisticsSample(BusinessLogisticsApplyDto logisticsApply) {
        final Set<String> sampleBarcodes = logisticsApply.getSampleBarcodes();
        final String hspOrgCode = logisticsApply.getHspOrgCode();
        if (CollectionUtils.isEmpty(sampleBarcodes) || Objects.isNull(hspOrgCode)) {
            log.warn("同步物流信息失败，参数: [{}]", JSON.toJSONString(logisticsApply));
            return;
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgCode(hspOrgCode);
        if (Objects.isNull(hspOrganization)) {
            log.warn("送检机构不存在，编码 [{}] 参数: [{}] ", hspOrgCode, JSON.toJSON(logisticsApply));
            return;
        }

        // 判断是否有存在的条码
        final Collection<String> exitsBarcodes = selectSampleByBarcodes(sampleBarcodes).stream()
            .map(ApplyLogisticsSampleDto::getBarcode).collect(Collectors.toSet());

        // 删除已存在的条码
        sampleBarcodes.removeIf(f -> {
            final boolean contains = exitsBarcodes.contains(f);
            if (BooleanUtils.isTrue(contains)) {
                log.warn(String.format("送检机构 [%s] 条码 [%s] 已存在,跳过此条码", hspOrgCode, f));
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        });

        // 如果被删完了 就跳过
        if (CollectionUtils.isEmpty(sampleBarcodes)) {
            log.warn("送检机构 [{}] 没有需要同步的条码", hspOrgCode);
            return;
        }
        final Date now = new Date();

        final long applyLogisticsId = snowflakeService.genId();
        final ApplyLogisticsDto applyLogistics = new ApplyLogisticsDto();
        applyLogistics.setApplyLogisticsId(applyLogisticsId);
        applyLogistics.setMasterBarcode("");
        applyLogistics.setCreateDate(now);
        applyLogistics.setUpdateDate(now);
        applyLogistics.setUpdaterId(-99L);
        applyLogistics.setUpdaterName("系统同步");
        applyLogistics.setCreatorId(-99L);
        applyLogistics.setCreatorName("系统同步");
        applyLogistics.setReceiveDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        applyLogistics.setLogisticsUserId(0L);
        applyLogistics.setLogisticsUserName(StringUtils.EMPTY);
        applyLogistics.setApplyImage(StringUtils.EMPTY);
        applyLogistics.setIsDelete(YesOrNoEnum.NO.getCode());
        applyLogistics.setHspOrgId(hspOrganization.getHspOrgId());
        applyLogistics.setHspOrgName(hspOrganization.getHspOrgName());
        applyLogistics.setOrgId(hspOrganization.getOrgId());
        applyLogistics.setOrgName(hspOrgCode.toLowerCase());

        final List<ApplyLogisticsSampleDto> applyLogisticsSamples = sampleBarcodes.stream().map(m -> {
            final ApplyLogisticsSampleDto applyLogisticsSample = new ApplyLogisticsSampleDto();
            applyLogisticsSample.setApplyLogisticsSampleId(snowflakeService.genId());
            applyLogisticsSample.setApplyLogisticsId(applyLogisticsId);
            applyLogisticsSample.setBarcode(m);
            // -1 等待同步项目
            applyLogisticsSample.setStatus(-1);
            applyLogisticsSample.setApplyId(0L);
            applyLogisticsSample.setCreateDate(now);
            applyLogisticsSample.setUpdateDate(now);
            applyLogisticsSample.setUpdaterId(applyLogistics.getUpdaterId());
            applyLogisticsSample.setUpdaterName(applyLogistics.getUpdaterName());
            applyLogisticsSample.setCreatorId(applyLogistics.getCreatorId());
            applyLogisticsSample.setCreatorName(applyLogistics.getCreatorName());
            applyLogisticsSample.setIsDelete(applyLogistics.getIsDelete());
            return applyLogisticsSample;
        }).collect(Collectors.toList());

        // 保存物流单信息
        tbApplyLogisticsMapper.insert(convert(applyLogistics));

        // 保存物流单样本信息
        for (ApplyLogisticsSampleDto applyLogisticsSample : applyLogisticsSamples) {
            tbApplyLogisticsSampleMapper
                .insert(JSON.parseObject(JSON.toJSONString(applyLogisticsSample), TbApplyLogisticsSample.class));
        }
    }

    @Override
    public Set<Long> selectUnSyncInfoSampleByDate(Date start, Date end) {
        final LambdaQueryWrapper<TbApplyLogisticsSample> eq =
            Wrappers.lambdaQuery(TbApplyLogisticsSample.class).select(TbApplyLogisticsSample::getApplyLogisticsSampleId)
                .ge(TbApplyLogisticsSample::getCreateDate, start).le(TbApplyLogisticsSample::getCreateDate, end)
                // -1: 等待同步项目
                .eq(TbApplyLogisticsSample::getStatus, -1);
        return tbApplyLogisticsSampleMapper.selectList(eq).stream()
            .map(TbApplyLogisticsSample::getApplyLogisticsSampleId).collect(Collectors.toSet());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long additionalApplyInfo(long applyId, long applyLogisticsSampleId) {

        final ApplyDto apply = applyService.selectByApplyId(applyId);
        if (Objects.isNull(apply)) {
            throw new IllegalArgumentException("申请单不存在");
        }

        if (Objects.equals(apply.getStatus(), ApplyStatusEnum.CHECK.getCode())
            || Objects.equals(apply.getStatus(), ApplyStatusEnum.DOUBLE_CHECK.getCode())) {
            throw new IllegalArgumentException("申请单已复核,不能追加样本");
        }

        final ApplyLogisticsSampleDto applyLogisticsSample =
            applyLogisticsSampleService.selectById(applyLogisticsSampleId);
        if (Objects.isNull(applyLogisticsSample)) {
            throw new IllegalStateException("物流样本不存在");
        }

        if (!Objects.equals(applyLogisticsSample.getStatus(), ApplyStatusEnum.PENDING_RECORDING.getCode())) {
            throw new IllegalStateException("物流样本已录入,不能追加样本");
        }

        final List<ApplyLogisticsSampleItemDto> applyLogisticsSampleItems =
            applyLogisticsSampleItemService.selectByApplyLogisticsSampleId(applyLogisticsSampleId);
        if (CollectionUtils.isEmpty(applyLogisticsSampleItems)) {
            throw new IllegalStateException("物流样本检验项目不存在");
        }
        final Collection<Long> testItemIds = applyLogisticsSampleItems.stream()
            .map(ApplyLogisticsSampleItemDto::getTestItemId).collect(Collectors.toSet());
        final Map<Long, TestItemDto> testItemMap = testItemService.selectByTestItemIdsAsMap(testItemIds);

        if (MapUtils.isEmpty(testItemMap)) {
            throw new IllegalStateException("检验项目不存在,请联系管理人员");
        }
        final LoginUserHandler.User user = LoginUserHandler.get();
        // 创建样本
        final ApplySampleDto applySample = groupSampleCommand.createApplySample();
        applySample.setApplyId(applyId);
        applySample.setBarcode(applyLogisticsSample.getBarcode());
        applySample.setHspOrgCode(apply.getHspOrgCode());
        applySample.setHspOrgName(apply.getHspOrgName());
        Date now = new Date();
        // key: 检验项目id value: 实际收费价格
        Map<Long, BigDecimal> actualFeePriceByTestItemId = itemPriceBasePackageService
            .selectActualFeePrice(apply.getHspOrgId(), apply.getApplyTypeCode(), now, testItemIds);

        // 创建样本检验项目
        final List<ApplySampleItemDto> applySampleItems = applyLogisticsSampleItems.stream().map(m -> {
            final TestItemDto testItem = testItemMap.get(m.getTestItemId());
            if (Objects.isNull(testItem)) {
                return null;
            }

            ApplySampleItemDto applySampleItem = new ApplySampleItemDto();
            applySampleItem.setApplySampleItemId(snowflakeService.genId());
            applySampleItem.setApplySampleId(applySample.getApplySampleId());
            applySampleItem.setApplyId(applyId);
            applySampleItem.setTestItemId(testItem.getTestItemId());
            applySampleItem.setTestItemCode(testItem.getTestItemCode());
            applySampleItem.setTestItemName(testItem.getTestItemName());
            applySampleItem.setItemType(testItem.getItemType());
            applySampleItem.setOutTestItemId(NumberUtils.LONG_ZERO);
            applySampleItem.setOutTestItemCode(StringUtils.EMPTY);
            applySampleItem.setOutTestItemName(StringUtils.EMPTY);
            applySampleItem.setSampleTypeName(testItem.getSampleTypeName());
            applySampleItem.setSampleTypeCode(testItem.getSampleTypeCode());
            applySampleItem.setTubeName(testItem.getTubeName());
            applySampleItem.setTubeCode(testItem.getTubeCode());
            applySampleItem.setGroupId(testItem.getGroupId());
            applySampleItem.setGroupName(testItem.getGroupName());
            applySampleItem.setUrgent(YesOrNoEnum.NO.getCode());
            applySampleItem.setRemark(StringUtils.EMPTY);
            applySampleItem.setCreateDate(now);
            applySampleItem.setCount(NumberUtils.INTEGER_ONE);
            applySampleItem.setUpdateDate(now);
            applySampleItem.setCreatorId(user.getUserId());
            applySampleItem.setCreatorName(user.getNickname());
            applySampleItem.setUpdaterId(user.getUserId());
            applySampleItem.setUpdaterName(user.getNickname());
            applySampleItem.setIsDelete(YesOrNoEnum.NO.getCode());
            applySampleItem.setSplitCode(StringUtils.EMPTY);
            applySampleItem.setIsOutsourcing(ObjectUtils.defaultIfNull(testItem.getEnableExport(), 0));
            applySampleItem.setExportOrgId(ObjectUtils.defaultIfNull(testItem.getExportOrgId(), NumberUtils.LONG_ZERO));
            applySampleItem.setExportOrgName(StringUtils.defaultString(testItem.getExportOrgName()));
            applySampleItem.setFeePrice(ObjectUtils.defaultIfNull(testItem.getFeePrice(), BigDecimal.ZERO));
            applySampleItem.setActualFeePrice(
                actualFeePriceByTestItemId.getOrDefault(testItem.getTestItemId(), applySampleItem.getFeePrice()));
            applySampleItem.setIsFree(YesOrNoEnum.NO.getCode());
            applySampleItem.setItemSource(ApplySampleItemSourceEnum.DEFAULT_SOURCE.getCode());
            applySample.setTubeName(testItem.getTubeName());
            applySample.setTubeCode(testItem.getTubeCode());
            applySample.setSampleTypeName(testItem.getSampleTypeName());
            applySample.setSampleTypeCode(testItem.getSampleTypeCode());
            applySample
                .setItemType(StringUtils.defaultString(applySample.getItemType(), applySampleItem.getItemType()));
            return applySampleItem;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 保存样本
        applySampleService.addApplySample(applySample);

        // 保存样本检验项目
        applySampleItemService.addApplySampleItems(applySampleItems);

        // 更新物流样本 状态
        ApplyLogisticsSampleDto update = new ApplyLogisticsSampleDto();
        update.setStatus(apply.getStatus());
        update.setApplyId(applyId);
        update.setApplyLogisticsSampleId(applyLogisticsSampleId);
        update.setUpdaterId(user.getUserId());
        update.setUpdaterName(user.getNickname());
        update.setUpdateDate(new Date());
        applyLogisticsSampleService.updateById(update);

        // 通知业务中台
        final SignOutApplyInfoRequest request = new SignOutApplyInfoRequest();
        request.setBarcode(applyLogisticsSample.getBarcode());
        request.setHspOrgCode(apply.getHspOrgCode());
        request.setReceiveUserCode(String.valueOf(LoginUserHandler.get().getUserId()));
        request.setReceiveUserName(LoginUserHandler.get().getNickname());
        request.setSignOrgCode(LoginUserHandler.get().getOrgCode());
        request.setSignOrgName(LoginUserHandler.get().getOrgName());
        request.setSignBarcode(applyLogisticsSample.getBarcode());
        request.setSignMainBarcode(StringUtils.EMPTY);

        log.info("开始签收物流条码  条码 [{}] 签收参数 [{}]", applyLogisticsSample.getBarcode(), JSON.toJSONString(request));
        final Response<?> response = outApplyInfoService.signOutApplyInfoAdditional(request);
        if (Objects.isNull(response)) {
            throw new IllegalStateException("补录失败, 业务中台数据返回为空");
        }

        if (BooleanUtils.isNotTrue(Objects.equals(response.getCode(), 0))) {
            throw new IllegalStateException(String.format("补录失败, 业务中台返回 [%s]", response.getMsg()));
        }

        log.info("用户 [{}] 补录物流信息成功，applyLogisticsId: [{}] info: [{}]", user.getNickname(),
            applyLogisticsSample.getApplyLogisticsId(), JSON.toJSONString(applyLogisticsSample));

        return applySample.getApplySampleId();
    }

    public List<ApplyLogisticsSampleDto> selectSampleByBarcodes(Set<String> sampleBarcodes) {
        if (CollectionUtils.isEmpty(sampleBarcodes)) {
            return Collections.emptyList();
        }
        return JSON.parseArray(
            JSON.toJSONString(tbApplyLogisticsSampleMapper.selectList(
                Wrappers.<TbApplyLogisticsSample>lambdaQuery().in(TbApplyLogisticsSample::getBarcode, sampleBarcodes))),
            ApplyLogisticsSampleDto.class);
    }

    /**
     * 将 TbApplyLogistics 转换为 ApplyLogisticsDto
     */
    private ApplyLogisticsDto convert(TbApplyLogistics tbApplyLogistics) {
        return JSON.parseObject(JSON.toJSONString(tbApplyLogistics), ApplyLogisticsDto.class);
    }

    /**
     * 将 ApplyLogisticsDto 转换为 TbApplyLogistics
     */
    private TbApplyLogistics convert(ApplyLogisticsDto tbApplyLogistics) {
        return JSON.parseObject(JSON.toJSONString(tbApplyLogistics), TbApplyLogistics.class);
    }

}
