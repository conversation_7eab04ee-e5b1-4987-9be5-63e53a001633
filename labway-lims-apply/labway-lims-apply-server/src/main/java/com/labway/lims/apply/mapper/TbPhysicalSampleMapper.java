package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.model.TbPhysicalSample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 体检样本 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbPhysicalSampleMapper extends BaseMapper<TbPhysicalSample> {

    /**
     * 批量 插入
     */
    void batchAddPhysicalSample(@Param("conditions") List<TbPhysicalSample> conditions);
}
