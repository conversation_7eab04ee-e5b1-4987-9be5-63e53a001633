package com.labway.lims.apply.model.es;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * 微生物结果
 */
@Getter
@Setter
public final class MicrobiologyResult {
    /**
     * 结果
     */
    @Field(type = FieldType.Keyword)
    private String result;
    /**
     * 结果描述
     */
    @Field(type = FieldType.Keyword)
    private String resultDesc;
}
