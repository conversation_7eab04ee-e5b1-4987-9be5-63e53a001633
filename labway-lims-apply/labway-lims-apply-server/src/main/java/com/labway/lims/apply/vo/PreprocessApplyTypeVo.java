package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
public class PreprocessApplyTypeVo {
    /**
     * 动态列
     */
    private Set<PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics> columns;

    /**
     * 动态列 key->code value->name
     */
    @JsonIgnore
    private Map<String,String> columnMap;

    /**
     * 数据列
     */
    private List<PreprocessApplyTypeStatisticsVo> data;
}
