package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.apply.api.dto.TestItemIncomeFilterDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Set;

/**
 * 销售项目收入查询--参数vo
 * 
 * <AUTHOR>
 * @since 2023/5/15 10:12
 */
@Setter
@Getter
public class TestItemIncomeRequestVo extends TestItemIncomeFilterDto {

    /**
     * 送检 时间 查询的就是 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date beginDeliveryDate;
    /**
     * 送检 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endDeliveryDate;
    /**
     * 财务月份 YYYY-MM
     */
    private String financialMonth;

    /**
     * 送检机构
     */
    private Set<Long> hspOrgIds;
}
