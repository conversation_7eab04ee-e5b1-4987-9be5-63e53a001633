package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 机构送检医生统计 信息 Dto
 * 
 * <AUTHOR>
 * @since 2023/5/18 10:27
 */
@Setter
@Getter
public class HspOrgSendDoctorStatisticsResponseDto implements Serializable {

    /**
     * 机构id
     */
    private Long orgId;
    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 数量合计
     */
    private Integer countSum;
    /**
     * 标准收费合计
     */
    private BigDecimal feePriceSum;
    /**
     * 合计金额合计
     */
    private BigDecimal totalFeePriceSum;
    /**
     * 结算金额合计
     */
    private BigDecimal payAmountSum;
    /**
     * 汇总行
     */
    private List<HspOrgSendDoctorStatisticsItemDto> itemList;
    /**
     * 机构汇总行
     */
    private List<HspOrgSendDoctorStatisticsResponseDto> orgList;

}
