package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 样本归档 提取样本
 * 
 * <AUTHOR>
 * @since 2023/4/17 17:27
 */
@Getter
@Setter
public class AutoExtractArchiveSampleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 归档样本 占用空间
     */
    private List<RackLogicSpaceDto> rackLogicSpaceDtoList;

    /**
     * 提取原因
     */
    private String extractDesc;

}
