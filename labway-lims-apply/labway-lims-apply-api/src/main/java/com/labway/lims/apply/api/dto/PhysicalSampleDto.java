package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PhysicalSampleStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 体检样本
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class PhysicalSampleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long physicalSampleId;

    /**
     * 体检人
     */
    private Long physicalRegisterId;

    /**
     * 批次号
     */
    private Long physicalBatchId;

    /**
     * 样本类型
     */
    private String sampleType;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 管型
     */
    private String tube;

    /**
     * 体检单位
     */
    private Long physicalCompanyId;

    /**
     * 体检单位名称
     */
    private String physicalCompanyName;

    /**
     * 样本个数
     */
    private Integer sampleCount;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构
     */
    private String orgName;
    /**
     * 签收状态：0未签收，1已签收
     * 
     * @see PhysicalSampleStatusEnum
     */
    private Integer status;
    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 1: 删除 0:未
     * 
     * @see YesOrNoEnum
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 采样时间
     */
    private Date samplingDate;
}
