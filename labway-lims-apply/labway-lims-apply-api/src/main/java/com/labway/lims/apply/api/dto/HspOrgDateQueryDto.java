package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.apply.ApplySourceEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class HspOrgDateQueryDto implements Serializable {
    /**
     * 机构id
     */
    private Long hspOrgId;
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 物流样本状态
     */
    private List<Integer> statusList;

    /**
     * 签收人名称
     */
    private String signer;

    /**
     * 样本来源
     */
    private ApplySourceEnum source;
}
