package com.labway.lims.apply.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImportFileStatusEnum {

    IMPORT(1, "导入"),

    ;
    private final int code;
    private final String desc;

    public static ImportFileStatusEnum getByCode(int code) {
        for (ImportFileStatusEnum value : ImportFileStatusEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
