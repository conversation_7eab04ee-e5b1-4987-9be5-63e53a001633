package com.labway.lims.apply.api.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * @description 物料专业组计划
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
public class AddGroupMaterialPlanDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 厂家
     */
    private String manufacturers;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 主申请数量
     */
    private BigDecimal mainApplyNumber;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     * 辅申请数量
     */
    private String assistApplyNumber;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 备注
     */
    private String remark;

    public void verify() {
        if (StringUtils.isBlank(materialCode)) {
            throw new IllegalArgumentException("物料编码不能为空");
        }
        if (StringUtils.isBlank(materialName)) {
            throw new IllegalArgumentException(String.format("物料编码：【%s】物料名称不能为空", materialCode));
        }
        if (StringUtils.isBlank(specification)) {
            throw new IllegalArgumentException(String.format("物料编码：【%s】规格不能为空", materialCode));
        }
        if (StringUtils.isBlank(manufacturers)) {
            throw new IllegalArgumentException(String.format("物料编码：【%s】厂家不能为空", materialCode));
        }
        if (StringUtils.isBlank(mainUnit)) {
            throw new IllegalArgumentException(String.format("物料编码：【%s】主单位不能为空", materialCode));
        }
        if (Objects.isNull(mainApplyNumber)) {
            throw new IllegalArgumentException(String.format("物料编码：【%s】主申请数量不能为空", materialCode));
        } else if (mainApplyNumber.compareTo(BigDecimal.ZERO) < NumberUtils.INTEGER_ONE) {
            throw new IllegalArgumentException(String.format("物料编码：【%s】主申请数量不能小于等于0", materialCode));
        }
        if (StringUtils.isBlank(assistUnit)) {
            throw new IllegalArgumentException(String.format("物料编码：【%s】辅单位不能为空", materialCode));
        }
        if (Objects.isNull(assistApplyNumber)) {
            throw new IllegalArgumentException(String.format("物料编码：【%s】辅申请数量不能为空", materialCode));
        } else if (new BigDecimal(assistApplyNumber).compareTo(BigDecimal.ZERO) < NumberUtils.INTEGER_ONE) {
            throw new IllegalArgumentException(String.format("物料编码：【%s】主申请数量不能小于等于0", materialCode));
        }
        if (StringUtils.isBlank(unitConversionRate)) {
            throw new IllegalArgumentException(String.format("物料编码：【%s】主辅单位换算率不能为空", materialCode));
        }
    }

}