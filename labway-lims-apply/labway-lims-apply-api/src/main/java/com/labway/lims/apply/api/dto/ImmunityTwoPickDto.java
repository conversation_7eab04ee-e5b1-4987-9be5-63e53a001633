package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <pre>
 * ImmunityTwoPickDto
 * 免疫二次分拣
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/30 13:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class ImmunityTwoPickDto extends TwoPickDto {

    /**
     * 免疫二次分拣 类型
     * 正常，强制，项目强制
     */
    private String type;

    /**
     * 自定义分拣日期
     */
    private Date twoPickDate;

    /**
     * 指定 预定日期
     */
    private List<Date> presetDates;

    /**
     * 指定 检验项目
     */
    private List<String> testItemCodes;

}
