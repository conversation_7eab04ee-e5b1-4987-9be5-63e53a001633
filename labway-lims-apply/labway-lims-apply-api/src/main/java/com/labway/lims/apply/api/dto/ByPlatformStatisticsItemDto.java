package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分平台统计 信息 单行 Dto
 * 
 * <AUTHOR>
 * @since 2023/7/12 13:59
 */
@Setter
@Getter
public class ByPlatformStatisticsItemDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    private String sendDate;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;
    /**
     * 就诊类型名称
     */
    private String applyTypeName;
    private String applyTypeCode;

    /**
     * 财务专业组
     */
    private String financeGroupCode;
    private String financeGroupName;

    /**
     * 检验项目编码
     */
    private String testItemCode;
    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 数量
     */
    private Integer count;
    /**
     * 收费价格
     */
    private BigDecimal feePrice;
    /**
     * 折扣率
     */
    private String discount;
    /**
     * 折前总额
     */
    private BigDecimal payAmountBefore;
    /**
     * 折后总额
     */
    private BigDecimal payAmountAfter;
}
