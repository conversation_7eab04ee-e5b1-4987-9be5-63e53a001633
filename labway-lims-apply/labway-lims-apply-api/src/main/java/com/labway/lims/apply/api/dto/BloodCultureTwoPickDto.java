package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 血培养二次分拣
 */
@Getter
@Setter
@Accessors(chain = true)
public class BloodCultureTwoPickDto extends TwoPickDto {
    /**
     * 自定义分拣日期
     */
    private Date twoPickDate;

    /**
     * 血培养
     */
    private List<LimbSampleDto> limbSampleNos;
}
