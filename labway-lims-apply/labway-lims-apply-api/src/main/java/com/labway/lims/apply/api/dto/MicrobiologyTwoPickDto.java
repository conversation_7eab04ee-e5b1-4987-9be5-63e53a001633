package com.labway.lims.apply.api.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 微生物二次分拣
 *
 * <AUTHOR>
 * @since 2023/7/25 14:33
 */
@Getter
@Setter
@Accessors(chain = true)
public class MicrobiologyTwoPickDto extends TwoPickDto {
    /**
     * 自定义分拣日期
     */
    private Date twoPickDate;
}
