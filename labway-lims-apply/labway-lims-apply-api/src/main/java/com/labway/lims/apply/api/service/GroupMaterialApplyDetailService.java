package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.GroupMaterialApplyDetailDto;
import com.labway.lims.apply.api.dto.MaterialRejectDto;

import java.util.List;
import java.util.Set;

public interface GroupMaterialApplyDetailService {

    /**
     * 根据申领单号查询
     */
    List<GroupMaterialApplyDetailDto> selectByApplyNo(long orgId, String applyNo);

    /**
     * 批量新增申请单
     */
    void addBatch(List<GroupMaterialApplyDetailDto> list);

    /**
     * 根据 id 删除
     */
    void deleteById(long id);

    /**
     * 批量删除
     */
    void deleteByIds(Set<Long> ids);


    List<GroupMaterialApplyDetailDto> selectByApplyNo(String applyId, long orgId);

    /**
     * 物料申请驳回
     */
    boolean reject(MaterialRejectDto rejectDto);

    /**
     * 批量更新物料申请详情
     */
    void updateBatchByDetailId(List<GroupMaterialApplyDetailDto> list);

    /**
     * 根据申领单号集合查询
     */
    List<GroupMaterialApplyDetailDto> selectByApplyNos(long orgId, List<String> applyNos);

}
