package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 取消二次分拣的返回
 */
@Getter
@Setter
public class SampleTwoUnPickInfoDto  implements Serializable {
    /**
     * 取消二次分拣的样本
     */
    private List<Sample> samples;

    /**
     * 样本
     */
    @Getter
    @Setter
    public static class Sample  implements Serializable{
        /**
         * 样本号
         */
        private String sampleNo;

        /**
         * 样本ID
         */
        private Long sampleId;

        /**
         * 二次分拣日期
         */
        private Date twoPickDate;

        /**
         * 专业小组ID
         */
        private Long instrumentGroupId;

        /**
         * 专业组ID
         */
        private Long groupId;
    }
}
