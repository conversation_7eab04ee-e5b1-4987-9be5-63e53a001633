package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售项目收入查询--汇总 申请单样本信息 结构转换
 * 
 * <AUTHOR>
 * @since 2023/5/16 11:34
 */
@Getter
@Setter
public class TestItemIncomeSummaryDto implements Serializable {
    /**
     * id
     */
    private Long hspOrgId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 标准收费合计
     */
    private BigDecimal feePriceSum;
    /**
     * 数量合计
     */
    private Integer countSum;

    /**
     * 拆解的数据行
     */
    private List<TestItemIncomeSummaryItemDto> itemDtoList;
}
