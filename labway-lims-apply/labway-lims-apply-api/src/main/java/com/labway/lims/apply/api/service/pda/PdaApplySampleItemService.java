package com.labway.lims.apply.api.service.pda;

import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.PdaApplySampleItemDto;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface PdaApplySampleItemService {
    /**
     *
     * @param applySampleItems
     */
    void addBacthApplySampleItem(List<ApplySampleItemDto> applySampleItems);

    /**
     * 根据pda申请单id删除所有的
     * @param applyId
     * @return
     */
    boolean deleteByApplyId(Serializable applyId);


    /**
     * 根据pdaapplyid查询
     * @param pdaApplyId
     * @return
     */
    List<PdaApplySampleItemDto> selectByPdaApplyId(Serializable pdaApplyId);

    /**
     * 根据pdaapplyids查询
     * @param pdaApplyIds
     * @return
     */
    Map<Long, List<PdaApplySampleItemDto>> selectByPdaApplyIds(Collection<Serializable> pdaApplyIds);

    /**
     * 批量删除
     * @param pdaApplyIds
     */
    void deleteByPdaApplyIds(Collection<Long> pdaApplyIds);

    /**
     * 根据样本项目ids查询
     */
    List<PdaApplySampleItemDto> selectByPdaApplyItemIds(List<Long> applySampleItemIds);

    /**
     * 根据样本项目ids查询
     */
    Map<Long, PdaApplySampleItemDto> selectByPdaApplyItemIdsMap(List<Long> applySampleItemIds);

    /**
     * 查询申请单下的样本数量
     */
    Long selectApplyItemCount(Long pdaApplyId);

    /**
     * 根据id删除
     */
    void deleteByApplySampleItem(Collection<Long> applySampleItemIds);
}
