package com.labway.lims.apply.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SignPdaApplyDto extends ApplyMasterBarcodeDto {

    /**
     * 血培养项目信息
     */
    private ApplySampleItemBloodCultureDto bloodCulture;

    /**
     * 血培养项目code
     */
    private String bloodCultureItemCode;

    /**
     * pdf 签收项目
     */
    private List<PdaSignItem> signItems;


    @Data
    public static class PdaSignItem {

        /**
         * 检验项目id
         */
        private Long testItemId;

        /**
         * 自定义码
         */
        private String customCode;
    }
}
