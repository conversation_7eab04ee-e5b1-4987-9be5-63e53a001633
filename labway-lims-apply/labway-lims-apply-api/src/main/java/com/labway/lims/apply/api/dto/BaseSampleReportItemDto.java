package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class BaseSampleReportItemDto extends BaseSampleEsModelDto {
    /**
     * 报告项目
     */
    private List<RoutineInspectionDto.RoutineReportItem> reportItems;

    /**
     * 报告项目
     */
    @Setter
    @Getter
    public static final class RoutineReportItem extends BaseSampleEsModelDto.ReportItem implements Serializable {
        /**
         * id
         */
        private Long sampleResultId;

        /**
         * 结果范围
         */
        private String range;

        /**
         * 状态 1:危机 2:异常 0:正常
         * @see ResultStatusEnum
         */
        private Integer status;

        /**
         * 来源仪器
         */
        private Long instrumentId;

        /**
         * 仪器结果
         */
        private String instrumentResult;

        /**
         * 结果 （经过一系列的计算 转换最终得到的结果值）
         */
        private String result;

        /**
         * 检验判定 UP  DOWN  NORMAL
         * @see TestJudgeEnum
         */
        private String judge;

        /**
         * 结果创建时间
         */
        private Date createDate;
    }
}
