package com.labway.lims.apply.api.dto;

import org.apache.commons.lang3.StringUtils;

public class BarcodeContentBuilder {


    private BarcodeContentBuilder() {
    }

    public static BarcodeContentBuilder builder() {
        return new BarcodeContentBuilder();
    }


    public static class Content {
        /**
         * 属性
         */
        private String property;

        public String getContent() {
            return StringUtils.EMPTY;
        }

        @Override
        public String toString() {
            return getContent();
        }
    }

    public static class DeleteContent extends Content {

    }

    public static class UpdateContent extends Content {

    }

    public static class InsertContent extends Content {

    }

}
