package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 查询样本信息 走es。不要使用这个接口，它已经被废弃了
 *
 * @deprecated 使用 {@link com.labway.lims.statistics.api.client.ElasticSearchSampleService}
 */
@Deprecated
public interface ElasticSearchSampleService {

    /**
     * 查询样本信息分页。第一次查询时 searchAfter 传入 null。当 {@link ScrollPage#getData()} 为空时就是查询完毕，不支持跳页查询。
     * <br/>
     * 代码示例：
     * <pre>
     *         List<Object> searchAfter = null;
     *         do {
     *             final ScrollPage<BaseSampleEsModelDto> page = elasticSearchSampleService.searchAfter(searchAfter, query);
     *             searchAfter = page.getSearchAfter();
     *             if (CollectionUtils.isEmpty(page.getData())) {
     *                 break;
     *             }
     *         } while (!Thread.currentThread().isInterrupted());
     * </pre>
     *
     * @return 返回的 total、pageNum 一直是 0
     */
    ScrollPage<BaseSampleEsModelDto> searchAfter(List<Object> searchAfter, SampleEsQuery sampleEsQuery);


    /**
     * 请传入 {@link BaseSampleEsModelDto} 的子类
     */
    void insert(BaseSampleEsModelDto dto);

    /**
     * 查询所有 根据查询参数
     */
    List<BaseSampleEsModelDto> selectSamples(SampleEsQuery dto);

    /**
     * 根据 id 查询
     */
    @Nullable
    BaseSampleEsModelDto selectByApplySampleId(long applySampleId);

    /**
     * 根据es id 更新数据
     */
    void updateByApplySampleId(long applySampleId, BaseSampleEsModelDto dto);

    /**
     * 根据 applySampleId 删除数据
     */
    void deleteByApplySampleId(long applySampleId);


    /**
     * 统计总数
     */
    long count(SampleEsQuery query);
}
