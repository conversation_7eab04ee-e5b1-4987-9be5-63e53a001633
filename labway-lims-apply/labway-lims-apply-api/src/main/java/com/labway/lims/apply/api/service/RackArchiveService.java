package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.AutoExtractArchiveSampleDto;
import com.labway.lims.apply.api.dto.RackArchiveDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.base.api.dto.RackDto;
import com.labway.lims.base.api.dto.RefrigeratorDto;
import com.labway.lims.base.api.dto.UserDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 试管架归档 service
 * 
 * <AUTHOR>
 * @since 2023/4/13 14:55
 */
public interface RackArchiveService {

    /**
     * 查询 归档试管架信息 根据 物理试管架 ids
     */
    List<RackArchiveDto> selectByRackIds(Collection<Long> rackIds);

    /**
     * 查询 归档试管架信息 根据 物理试管架 id
     */
    @Nullable
    RackArchiveDto selectByRackId(long rackId);

    /**
     * 添加 归档试管架信息
     */
    long addRackArchive(RackArchiveDto dto);

    /**
     * 修改 归档试管架信息
     */
    void updateByRackArchive(RackArchiveDto dto);

    /**
     * 根据冰箱id 查看对应归档试管架相关信息
     * 
     */
    List<RackArchiveDto> selectByRefrigeratorId(long refrigeratorId);

    /**
     * 根据逻辑试管架 id 删除 归档信息
     */
    void deleteByRackLogicId(long rackLogicId);

    /**
     * 移除 归档试管架上样本
     * 
     * @param rackLogicSpaceDtos 需要移除的 样本空间占用
     * @param rackDto 对应物理试管架
     * @param refrigeratorDto 对应冰箱
     */
    void removeArchiveSample(List<RackLogicSpaceDto> rackLogicSpaceDtos, RackDto rackDto,
        RefrigeratorDto refrigeratorDto);

    /**
     * 提取 归档试管架上样本
     * 
     * @param rackLogicSpaceDtos 需要提取的 样本空间占用
     * @param rackDto 对应物理试管架
     * @param refrigeratorDto 对应冰箱
     * @param extractUser 提取人
     * @param extractDesc 提取原因
     */
    void extractArchiveSample(List<RackLogicSpaceDto> rackLogicSpaceDtos, RackDto rackDto,
        RefrigeratorDto refrigeratorDto, UserDto extractUser, String extractDesc);

    /**
     * 自动批量提取 归档试管架上样本
     *
     * @param autoExtractArchiveSampleDto 需要提取的 样本空间占用
     */
    void autoExtractArchiveSample(AutoExtractArchiveSampleDto autoExtractArchiveSampleDto);

    /**
     * 销毁 归档试管架上样本
     * 
     * @param rackLogicSpaceDtos 需要 销毁的 样本空间占用
     * @param rackDto 对应物理试管架
     * @param refrigeratorDto 对应冰箱
     */
    void destroyArchiveSample(List<RackLogicSpaceDto> rackLogicSpaceDtos, RackDto rackDto,
        RefrigeratorDto refrigeratorDto);

    /**
     * 销毁 归档试管架上样本
     */
    boolean destroyArchiveSample(Set<Long> rackLogicSpaceIds);

    /**
     * 销毁 归档试管架上样本 根据时间
     */
    void destroyArchiveSample(long orgId, Date date);

    /**
     * 当 试管架 上无任何归档样本 时需要 清理 数据
     * <p>
     * 删除逻辑试管架、归档试管架信息、修改试管架占用状态
     * <p>
     * true: 删除试管架 false:不删除试管架
     */
    boolean clearRackWhenArchiveSampleIsEmpty(long rackId, long rackLogicId, boolean deleteRack);

    /**
     * 检查冰箱使用情况 中 是否存在 归档试管架
     *
     * @param refrigeratorIds 冰箱ids
     * @return true 存在归档试管架 false 不存在归档试管架
     */
    boolean checkRefrigeratorUseByIds(Collection<Long> refrigeratorIds);

}
