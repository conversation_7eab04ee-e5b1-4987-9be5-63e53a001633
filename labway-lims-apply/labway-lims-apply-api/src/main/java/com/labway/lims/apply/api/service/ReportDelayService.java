package com.labway.lims.apply.api.service;


import com.labway.lims.apply.api.dto.ReportDelayDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 报告单迟发表
 * @date 2023-12-15
 */
public interface ReportDelayService {



    /**
     * 按条件查询
     */
    List<ReportDelayDto> selectByReportDelayInfo(ReportDelayDto reportDelayDto);

    /**
     * 按id查一条
     */
    @Nullable
    ReportDelayDto selectByReportDelayId(Long delayId);


    /**
     * 新增迟发报告单
     * @param reportDelayDto
     * @return
     */
    long addReportDelay(ReportDelayDto reportDelayDto);

    /**
     * 根据id批量修改申请单状态
     * @param delayId 迟发ID
     * @param status 状态
     * @param cancelUserId 作废人id
     * @param cancelUserName 作废人name
     */
    void updateByDelayIds(Set<Long> delayId,Integer status,Long cancelUserId,String cancelUserName);

    /**
     * 修改一条迟发报告单
     * @param reportDelayDto
     */
    void updateByDelayId(ReportDelayDto reportDelayDto);

    /**
     * 根据id查询
     * @param delayIds
     * @return
     */
    List<ReportDelayDto> selectByReportDelayIds(Collection<Long> delayIds);

}
