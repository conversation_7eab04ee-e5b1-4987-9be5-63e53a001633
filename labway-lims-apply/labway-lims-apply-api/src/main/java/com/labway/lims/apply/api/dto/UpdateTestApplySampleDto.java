package com.labway.lims.apply.api.dto;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class UpdateTestApplySampleDto extends TestApplyDto {

    /**
     * 样本id
     */
    private List<Long> applySampleIds;
    /**
     * 申请单样本id
     */
    private Long applySampleId;
    /**
     * 项目信息
     */
    private List<UpdateTestApplySampleItemDto> testApplySampleItems;

    /**
     * 是否刷新报告 true 刷新 false 不刷新
     */
    private Boolean refreshReport;
    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;
    /**
     * 管型
     */
    private String tubeName;
    /**
     * 管型CODE
     */
    private String tubeCode;

    /**
     *  是否为确认？ status = 1 未确认，为 Null 则为确认操作保存
     */
    private Integer confirmStatus;

    /**
     * 条码号
     */
    private String barcode;

	/**
	 * 标本部位
	 * @since 1.1.4
	 * @Description <a href="https://www.tapd.cn/59091617/prong/stories/view/1159091617001001756?from_iteration_id=1159091617001000242">评论</a>
	 */
	private String patientPart;


	@Override
    public List<Item> getItems() {
        if (CollectionUtils.isEmpty(testApplySampleItems)) {
            return Collections.emptyList();
        }
        return testApplySampleItems.stream().map(m -> JSON.parseObject(JSON.toJSONString(m), Item.class)).collect(Collectors.toList());
    }

}
