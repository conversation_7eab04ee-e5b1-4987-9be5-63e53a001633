package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 销售项目收入查询--汇总 申请单样本信息 拆解
 * 
 * <AUTHOR>
 * @since 2023/5/16 10:40
 */
@Getter
@Setter
public class TestItemIncomeSummaryItemDto implements Serializable {
    /**
     * 就诊类型 (申请类型)
     */
    private String applyTypeCode;
    private String applyTypeName;
    /**
     * 检验项目ID
     */
    private Long testItemId;
    /**
     * 检验项目编码
     */
    private String testItemCode;
    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 收费数量
     */
    private Integer count;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 折扣率
     */
    private BigDecimal discount;
    /**
     * 是否免单
     *
     * @see YesOrNoEnum
     */
    private Integer isFree;
}
