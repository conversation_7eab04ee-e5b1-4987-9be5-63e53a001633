package com.labway.lims.apply.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.Date;

@Data
public class PdaTobeConfirmedApplyDto  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * pda_tobe_confirmed_apply_id
     */
    private Long pdaTobeConfirmedApplyId;

    /**
     * 主条码号
     */
    private String masterBarcode;

    /**
     * 确认的PDA ApplyId
     */
    private Long confirmedPdaApplyId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 状态 {@link PdaConfirmEnum}
     */
    private Integer status;

    /**
     * create_date
     */
    private Date createDate;

    /**
     * update_date
     */
    private Date updateDate;

    /**
     * creator_name
     */
    private String creatorName;

    /**
     * creator_id
     */
    private Long creatorId;

    /**
     * updater_name
     */
    private String updaterName;

    /**
     * updater_id
     */
    private Long updaterId;

    /**
     * is_delete
     */
    private Integer isDelete;

    /**
     * pda图片列表
     */
    private String pdaImgs;

    /**
     * 确认人id
     */
    private Long conformerId;

    /**
     * 确认人姓名
     */
    private String conformerName;

    /**
     * 确认时间
     */
    private Date conformerTime;


    @Getter
    @AllArgsConstructor
    public enum PdaConfirmEnum{

        NO_CONFIRM(0, "未确认"),
        CONFIRM(1, "已确认"),
        SIGN(2, "已签收"),

        ;
        private final int code;

        private final String desc;

    }

}
