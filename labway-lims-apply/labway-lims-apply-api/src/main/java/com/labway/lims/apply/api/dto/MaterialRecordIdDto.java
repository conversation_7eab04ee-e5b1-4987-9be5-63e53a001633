package com.labway.lims.apply.api.dto;

import cn.hutool.core.lang.Assert;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@Data
public class MaterialRecordIdDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物料id
     */
    private Long recordId;

    /**
     * 物料编码
     */
    private List<Long> detailIds;


    /**
     * 拒收原因
     */
    private String refundReason;

    public void verifyParams(){
        Assert.notNull(recordId, "物料id不能为空");
        if (CollectionUtils.isEmpty(detailIds)) {
            throw new IllegalArgumentException("物料不能为空");
        }
        if (StringUtils.isBlank(refundReason)) {
            throw new IllegalArgumentException("拒收原因不能为空");
        }
    }
}
