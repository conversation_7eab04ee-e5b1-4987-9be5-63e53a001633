package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023/5/18 11:13
 */
@Getter
@Setter
public class TestItemIncomeDetailItemDto implements Serializable {

    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 送检时间 yyyy-MM-dd
     */
    private String sendDate;
    /**
     * 送检医生名称
     */
    private String sendDoctorName;
    /**
     * 就诊卡号 (门诊|住院号)
     */
    private String patientVisitCard;
    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 患者性别
     */
    private String patientSex;

    /**
     * 患者年龄
     */
    private String patientAge;
    /**
     * 就诊类型
     */
    private String applyTypeName;
    /**
     *检验项目
     */
    private String testItems;

    /**
     * 检验项目编码
     */
    private String testItemCodes;

    /**
     * 数量
     */
    private Integer count;
    /**
     * 标准收费合计
     */
    private BigDecimal feePriceSum;
    /**
     * 单价
     */
    private BigDecimal feePrice;
    /**
     * 折扣率
     */
    private BigDecimal discount;
    /**
     * 折扣率
     */
    private String discountLabel;
    /**
     * 若使用了客户特价项目 折后价格记录
     */
    private BigDecimal discountFeePrice;
    /**
     * 若使用了客户特价项目 折后价格记录
     */
    private String discountFeePriceLabel;

	/**
	 * 专业组id
	 */
	private Long groupId;
	/**
	 * 专业组名称
	 */
	private String groupName;

}
