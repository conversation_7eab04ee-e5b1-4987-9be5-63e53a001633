package com.labway.lims.apply.api.dto;

import cn.hutool.json.JSONUtil;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 业务中台退回申领单
 * 
 * <AUTHOR>
 * @since 2023/6/8 15:52
 */
@Getter
@Setter
public class BusinessCenterRejectApplyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 退回原因
     */
    private String returnReason;

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}
