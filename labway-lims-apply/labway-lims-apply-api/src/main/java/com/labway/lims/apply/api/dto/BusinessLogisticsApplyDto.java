package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
public class BusinessLogisticsApplyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 送检机构
     */
    private String hspOrgCode;

    /**
     * 样本条码
     */
    private Set<String> sampleBarcodes;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BusinessLogisticsApplyDto that = (BusinessLogisticsApplyDto) o;
        return Objects.equals(hspOrgCode, that.hspOrgCode) && Objects.equals(sampleBarcodes, that.sampleBarcodes);
    }

    @Override
    public int hashCode() {
        return Objects.hash(hspOrgCode, sampleBarcodes);
    }
}
