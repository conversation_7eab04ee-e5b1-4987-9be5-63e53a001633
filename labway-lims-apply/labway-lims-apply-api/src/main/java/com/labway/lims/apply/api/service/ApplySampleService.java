package com.labway.lims.apply.api.service;

import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.exception.apply.ApplySampleDisabledException;
import com.labway.lims.api.exception.apply.ApplySampleLockException;
import com.labway.lims.api.exception.apply.ApplySampleOrgLockException;
import com.labway.lims.api.exception.apply.ApplySampleTerminateException;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemTwoPickDetailDto;
import com.labway.lims.apply.api.dto.ApplySampleOnePickDto;
import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.BloodOneSplitDto;
import com.labway.lims.apply.api.dto.ImmunityTwoPickDto;
import com.labway.lims.apply.api.dto.InfectionTwoPickDto;
import com.labway.lims.apply.api.dto.LimbSampleDto;
import com.labway.lims.apply.api.dto.MicrobiologyTwoPickDto;
import com.labway.lims.apply.api.dto.OnePickedApplySampleDto;
import com.labway.lims.apply.api.dto.OutsourcingApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicApplySampleDto;
import com.labway.lims.apply.api.dto.SelectOutsourcingNotAuditSamplesDto;
import com.labway.lims.apply.api.dto.SplitBloodApplySampleDto;
import com.labway.lims.apply.api.dto.StopTestOrDisableSampleDto;
import com.labway.lims.apply.api.dto.TerminateItemDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.api.dto.TwoPickedApplySampleDto;
import com.labway.lims.apply.api.dto.WaitingOnePickApplySampleDto;
import com.labway.lims.apply.api.dto.WaitingSplitBloodApplySampleDto;
import com.labway.lims.apply.api.dto.WaitingTwoPickApplySampleDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 申请单样本
 */
public interface ApplySampleService {
    /**
     * 根据条码查询
     */
    List<ApplySampleDto> selectByBarcode(String barcode);

    /**
     * 根据条码 判断是否存在
     */
    boolean existsByBarcode(String barcode);

    /**
     * 根据原始条码查询
     */
    List<ApplySampleDto> selectByOriginalBarcode(String originalBarcode);

    /**
     * 根据条码号查询数量
     */
    int countByBarcode(String barcode);

    /**
     * 根据条码查询
     */
    List<ApplySampleDto> selectByBarcodes(Collection<String> barcodes);

    /**
     * 根据 条码 专业组 只能确定一条数据  (免疫二次分拣后能查出多条数据)
     * @see List<ApplySampleDto> selectByBarcodeAndGroupId(long groupId, String barcode);
     */
    @Deprecated
    @Nullable
    ApplySampleDto selectByBarcodeAndGroupId(String barcode, long groupId);

    /**
     * 免疫二次分拣后能查出多条数据
     */
    List<ApplySampleDto> selectByBarcodeAndGroupId(long groupId, String barcode);

    /**
     * 根据条id查询
     */
    @Nullable
    ApplySampleDto selectByApplySampleId(long applySampleId);

    /**
     * 根据 ids 查询
     */
    List<ApplySampleDto> selectByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据 ids 和 被 并单的查询 查询
     */
    List<ApplySampleDto> selectByApplySampleIdsAndMerge(Collection<Long> applySampleIds);

    /**
     * 根据 ids 查询
     */
    Map<Long, ApplySampleDto> selectByApplySampleIdsAsMap(Collection<Long> applySampleIds);

    /**
     * 根据ID修改
     */
    boolean updateByApplySampleId(ApplySampleDto applySample);

    /**
     * 根据ID修改
     */
    void updateByApplySampleIds(ApplySampleDto applySample, Collection<Long> applySampleIds);

    /**
     * 根据ID批量修改
     */
    void updateBatchByApplySampleIds(List<ApplySampleDto> applySamples);

    /**
     * 根据IDs修改
     */
    void updateInfoByApplySampleIds(ApplySampleDto applySample, Collection<Long> applySampleIds);
    /**
     * 添加样本申请单
     */
    long addApplySample(ApplySampleDto dto);

    /**
     * 根据申请单样本ID分血
     */
    List<Long> splitBloodByApplySampleId(long applySampleId);

    /**
     * 根据申请单样本ID分血
     */
    List<BloodOneSplitDto> splitBloodInfoByApplySampleId(long applySampleId);

    /**
     * 根据申请单样本ID查询样本
     */
    List<ApplySampleDto> selectByApplyIds(Collection<Long> applyIds);

    /**
     * 根据申请单样本ID查询样本
     */
    List<ApplySampleDto> selectByApplyId(long applyId);

    /**
     * 批量保存申请单样本
     */
    void addApplySamples(List<ApplySampleDto> applySamples);

    /**
     * 删除样本根据样本id
     */
    boolean deleteByApplySampleId(long applySampleId);

    /**
     * 删除样本根据样本id
     */
    void deleteByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据逻辑试管架ID查询
     */
    List<RackLogicApplySampleDto> selectByRackLogicIds(Collection<Long> rackLogicIds);

    /**
     * 根据试管架ID查询
     */
    List<RackLogicApplySampleDto> selectByRackIds(Collection<Long> rackIds);

    /**
     * 根据逻辑试管架ID查询
     */
    List<RackLogicApplySampleDto> selectByRackLogicId(long rackLogicId);

    /**
     * 取消一次分拣
     */
    void cancelOnePick(long applySampleId);

    /**
     * 取消二次分拣
     */
    void cancelTwoPick(String barcode);

    /**
     * 取消二次分拣
     * 如果碰到免疫二次分拣的样本，需要指定申请单样本ID进行取消二次分拣
     * @param barcode       条码号
     * @param applySampleId 申请单样本ID
     */
    void cancelTwoPick(String barcode, Long applySampleId);

    /**
     * 一次分拣
     */
    ApplySampleOnePickDto onePick(long applySampleId);

    /**
     * 二次分拣
     *
     * @deprecated {@link #twoPick(TwoPickDto)}
     */
    @Deprecated
    List<ApplySampleTwoPickDto> twoPick(long applySampleId, @Nullable String sampleNo, @Nullable String urgentSampleNo,
        Date twoPickDate, List<LimbSampleDto> limbSampleNos);

    /**
     * 二次分拣
     *
     * @see TwoPickDto 普通分拣
     * @see MicrobiologyTwoPickDto 微生物二次分拣
     * @see InfectionTwoPickDto 院感二次分拣
     */
    List<ApplySampleTwoPickDto> twoPick(TwoPickDto tp);//

    /**
     * 免疫二次分拣（常规检验）
     *
     * @see ImmunityTwoPickDto 免疫二次分拣
     */
    List<ApplySampleTwoPickDto> immunityTwoPick(ImmunityTwoPickDto tp);

    /**
     * 批量二次分拣
     */
    List<ApplySampleTwoPickDto> multiTwoPick(long rackLogicId,Long instrumentId);

    /**
     * 根据分血时间查询
     */
    List<ApplySampleDto> selectBySplitDate(Date beginSplitDate, Date endSplitDate);

    /**
     * 获取到待 一次分拣 的样本
     */
    List<WaitingOnePickApplySampleDto> selectWaitingOnePickSamples(Date beginSignDate, Date endSignDate);

    /**
     * 获取到待 二次分拣 的样本
     *
     * @param itemType 项目类型
     *                 <p>
     *                 不传为默认 排除 微生物、院感
     *                 <p>
     *                 INFECTION 院感
     *                 <p>
     *                 MICROBIOLOGY
     */
    List<WaitingTwoPickApplySampleDto> selectWaitingTwoPickSamples(Date beginReceiveDate, Date endReceiveDate,
                                                                   String itemType);

    /**
     * 获取到待 二次分拣 的样本
     */
    List<WaitingTwoPickApplySampleDto> selectWaitingTwoPickSamples(Date beginReceiveDate, Date endReceiveDate);

    /**
     * 获取到已经 二次分拣 的样本
     *
     * @param itemType 项目类型
     *                 <p>
     *                 不传为默认 排除 微生物、院感
     *                 <p>
     *                 INFECTION 院感
     *                 <p>
     *                 MICROBIOLOGY
     */
    List<TwoPickedApplySampleDto> selectTwoPickedSamples(Date beginTwoPickedDate, Date endTwoPickedDate,
                                                         String itemType);

    /**
     * 获取到已经 二次分拣 的样本
     */
    List<TwoPickedApplySampleDto> selectTwoPickedSamples(Date beginTwoPickedDate, Date endTwoPickedDate);

    /**
     * 快速获取二次分拣后的样本号，当没有二次分拣时，返回空
     *
     * @see #quicklyGetTwoPickInfoByApplySampleIds(Collection)
     */
    @Nullable
    ApplySampleTwoPickDto quicklyGetTwoPickInfoByApplySampleId(long applySampleId);

    /**
     * 快速获取二次分拣信息，当没有二次分拣时，返回空
     */
    Map<Long, ApplySampleTwoPickDto> quicklyGetTwoPickInfoByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 获取待分血的样本
     */
    List<WaitingSplitBloodApplySampleDto> selectWaitingSplitBloodSamples(Date beginReceiveDate, Date endReceiveDate,
                                                                         Long groupId);

    /**
     * 已分血单还未交接的样本列表
     */
    List<SplitBloodApplySampleDto> selectSplitBloodSamples(Date beginSplitDate, Date endSplitDate);

    List<SplitBloodApplySampleDto> selectAfterSplitBloodSamples(Date beginSplitDate, Date endSplitDate);

    /**
     * 查询没有一次分拣的样本根据 applyId
     */
    List<ApplySampleDto> selectNoOnePickSampleByApplyIds(Collection<Long> applyIds);

    /**
     * 查询没有终止检验的样本
     */
    List<ApplySampleDto> selectNoStopTestSampleByApplyIds(Collection<Long> applyIds);

    /**
     * 外送且未分拣的样本
     * @see #selectOutsourcingUnPickApplySamples
     */
    @Deprecated
    List<OutsourcingApplySampleDto> selectOutsourcingUnPickApplySamples(Date beginReceiveDate, Date endReceiveDate);

    /**
     * 外送且未分拣的样本
     */
    List<OutsourcingApplySampleDto> selectOutsourcingUnPickApplySamples(Date beginReceiveDate,
                                                                        Date endReceiveDate,
                                                                        Long outsourcingGroupId);

    /**
     * 外送且已分拣的样本
     */
    List<OutsourcingApplySampleDto> selectOutsourcingPickedApplySamples(Date beginPickDate, Date endPickDate);

    /**
     * 外送清单打印列表
     */
    List<OutsourcingApplySampleDto> selectOutsourcingListSamples(Date beginPickDate, Date endPickDate);

    /**
     * 是否已经禁用样本
     */
    boolean isDisabled(long applySampleId);

    /**
     * 是否已经终止检验
     */
    boolean isTerminate(long applySampleId);

    /**
     * 过滤掉终止检验的样本
     * @param applySampleIds
     * @return
     */
    public List<Long> filterTerminateApplySampleIds(LinkedList<Long> applySampleIds);

    /**
     * 恢复终止检验
     * - 去除redis
     */
    void regainTerminate(long applySampleId);

    /**
     * 判断样本可用性，如果不可用那么抛出异常
     * <dl>
     * <dt>禁用样本时</dt>
     * <dd>抛出异常 {@link ApplySampleDisabledException}</dd>
     * <dt>终止检验时</dt>
     * <dd>抛出异常 {@link ApplySampleTerminateException}</dd>
     * <dt>样本锁住时</dt>
     * <dd>抛出异常 {@link ApplySampleLockException}</dd>
     * <dt>样本所在机构锁住时</dt>
     * <dd>抛出异常 {@link ApplySampleOrgLockException}</dd>
     * </dl>
     */
    void assertApplySampleUsability(long applySampleId);

    /**
     * 判断样本可用性，如果不可用那么抛出异常
     * <dl>
     * <dt>禁用样本时</dt>
     * <dd>抛出异常 {@link ApplySampleDisabledException}</dd>
     * <dt>终止检验时</dt>
     * <dd>抛出异常 {@link ApplySampleTerminateException}</dd>
     * <dt>样本锁住时</dt>
     * <dd>抛出异常 {@link ApplySampleLockException}</dd>
     * <dt>样本所在机构锁住时</dt>
     * <dd>抛出异常 {@link ApplySampleOrgLockException}</dd>
     * </dl>
     */
    void assertApplySampleUsability(Collection<Long> applySampleIds);

    /**
     * 终止样本
     */
    void stopTest(StopTestOrDisableSampleDto param);

    /**
     * 禁用样本
     */
    void disable(StopTestOrDisableSampleDto param);

    /**
     * 取消禁用
     */
    void cancelDisable(List<Long> applySampleIds);

    /**
     * 统计申请单下审核的样本数量
     */
    int countApplySampleAuditQuantity(long applyId);

    /**
     * 根据 applyId 删除
     */
    void deleteByApplyIds(Collection<Long> applyIds);

    /**
     * 获取到已经一次分拣的样本。只会查询环节在 {@link RackLogicPositionEnum#ONE_PICKED} 的
     */
    List<OnePickedApplySampleDto> selectOnePickedApplySamples(Date beginOnePickDate, Date endOnePickDate);

    List<OnePickedApplySampleDto> selectAfterOnePickedSamples(Date beginOnePickDate, Date endOnePickDate);

    /**
     * 根据 外部条码+送检机构 查询
     */
    List<ApplySampleDto> selectByOutBarcodeAndHspOrgId(long hspOrgId, String outBarcode);

    List<ApplySampleDto> selectByOutBarcodeAndHspOrgCode(String hspOrgCode, String outBarcode);

    /**
     * 修改样本号
     */
    void changeSampleNo(long applySampleId, String sampleNo);

    /**
     * 根据 applyId 批量修改
     */
    void updateByApplyId(ApplySampleDto applySample);

    /**
     * 根据送检机构 + 外部条码 查询
     */
    List<ApplySampleDto> selectByOutBarcodesAndHspOrgId(String hspOrgCode, Set<String> barcodes);

    /**
     * 终止检验-检验项目维度
     */
    void terminateItem(TerminateItemDto dto);

    /**
     * 终止检验-检验项目维度
     */
    void regainTerminateBarcode(Set<Long> applySampleItemIds);

    /**
     * 根据创建时间查询
     */
    List<ApplySampleDto> selectByCreateDate(Date beginDate, Date endDate);

    /**
     * 查询未审核的外送申请单样本
     */
    List<ApplySampleDto> selectOutsourcingNotAuditApplySamples(SelectOutsourcingNotAuditSamplesDto selectParam);

    /**
     *  根据applyId去修改送检机构
     * @param apply
     */
    void updateHospByApplyId(ApplyDto apply);

    /**
     *
     * @param applyId
     * @param outBarcode
     */
    void updateOutBarcodeByApplyId(Long applyId, String outBarcode);

    /**
     * 根据applySampleId批量更新
     *
     * @param applySampleIds
     * @param barcode
     */
    void updateBatchByIds(Collection<Long> applySampleIds, String barcode);

    /**
     * 更新额外信息
     * @param applySampleId
     * @param extraInfo
     */
    void updateExtraInfoByApplySampleId(Long applySampleId, String extraInfo);

    /**
     * 将盛情单样本改为一身状态
     * @param applySampleIds
     */
    void updateOneAuditStatusByApplySampleIds(List<Long> applySampleIds);

    /**
     * 查询合并单的原数据
     * @param mergeMasterBarcode
     * @return
     */
    List<ApplySampleDto> selectMergeByBarcodes(String mergeMasterBarcode);

    /**
     * 条码项目二次分拣明细
     * @param barcode
     * @return
     */
    List<ApplySampleItemTwoPickDetailDto> selectApplySampleItemTwoPickDetailByBarcode(String barcode);

    /**
     * 是否走了组间交接
     *
     * @param applySampleDto 申请单样本
     * @throws IllegalStateException 如果是组间交接会抛异常
     * @see com.labway.lims.apply.service.chain.splitblood.CheckCanSplitCommand
     */
    boolean isGroupHandoverByBarcode(ApplySampleDto applySampleDto);

	/**
	 * 根据applySampleIds更新 加急状态
	 * @param update2UrgentSampleIds
	 */
	void update2UrgentByApplySampleIds(Collection<Long> update2UrgentSampleIds, UrgentEnum urgentEnum);
}
