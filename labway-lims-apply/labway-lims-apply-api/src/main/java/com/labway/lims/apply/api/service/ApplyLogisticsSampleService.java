package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.ApplyLogisticsSampleDto;
import com.labway.lims.apply.api.dto.HspOrgDateQueryDto;
import com.labway.lims.apply.api.dto.LogisticsApplyDto;
import com.labway.lims.apply.api.dto.SimpleLogisticsSampleDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

public interface ApplyLogisticsSampleService {

    /**
     * 根据 物流申请单ID 查询物流样本
     */
    List<ApplyLogisticsSampleDto> selectByApplyLogisticsId(long applyLogisticsId);

    /**
     *  根据 ID 查询物流样本
     */
    @Nullable
    ApplyLogisticsSampleDto selectById(long applyLogisticsSampleId);

    /**
     * 根据 status + hspOrgId + 取样时间 查询
     */
    List<SimpleLogisticsSampleDto> selectApplyLogisticsDetail(HspOrgDateQueryDto dto);

    /**
     * 根据申请单id 查询物流样本
     */
    List<ApplyLogisticsSampleDto> selectByApplyIds(Collection<Long> applyIds);

    /**
     * 根据申请单id 查询物流样本
     */
    @Nullable
    ApplyLogisticsSampleDto selectByApplyId(long applyId);

    /**
     * 更新物流样本状态
     */
    void updateStatusByApplyId(Long applyId, Integer code);

    /**
     * 查询已补录的物流样本
     */
    List<LogisticsApplyDto> selectSupplementList(HspOrgDateQueryDto dto);

    /**
     * 修改物流样本
     * @param update
     */
    void updateById(ApplyLogisticsSampleDto update);

    /**
     * 批量修改
     */
    void updateStatusByApplyIds(List<Long> applyIds, Integer code);

    /**
     * 新增
     */
    void add(ApplyLogisticsSampleDto applyLogisticsSample);

    /**
     * 根据物流样本id修改状态
     */
    void updateStatusById(long applyLogisticsSampleId, int code);
}
