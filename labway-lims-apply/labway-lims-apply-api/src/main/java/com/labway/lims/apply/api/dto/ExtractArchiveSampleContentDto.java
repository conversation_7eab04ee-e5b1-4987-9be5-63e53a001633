package com.labway.lims.apply.api.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 提取归档样本 -提举操作内容
 * 
 * <AUTHOR>
 * @since 2023/5/26 10:40
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ExtractArchiveSampleContentDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    private String nickname;
    /**
     * 专业组名称
     */
    private String groupName;
    /**
     * 冰箱名称
     */
    private String refrigeratorName;
    /**
     * 试管架编码
     */
    private String rackCode;
    /**
     * 多少行
     */
    private String row;

    /**
     * 多少列
     */
    private String column;
    /**
     * 提取原因
     */
    private String extractDesc;

    @Override
    public String toString() {
        return String.format("[%s] 提取 [%s] 专业组下 [%s] 冰箱下 [%s] 试管架上 [%s] 行 [%s] 列的样本,提取原因: [%s]", nickname, groupName,
            refrigeratorName, rackCode, row, column, StringUtils.defaultString(extractDesc));
    }

    public static ExtractArchiveSampleContentDto getByContentStr(String inputString) {
        String regex =
            "\\[(.*?)\\] 提取 \\[(.*?)\\] 专业组下 \\[(.*?)\\] 冰箱下 \\[(.*?)\\] 试管架上 \\[(.*?)\\] 行 \\[(.*?)\\] 列的样本,提取原因: \\[(.*?)\\]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(inputString.trim());

        if (matcher.find() && matcher.groupCount() == 7) {
            try {
                ExtractArchiveSampleContentDto dto = new ExtractArchiveSampleContentDto();
                dto.setNickname(matcher.group(1).trim());
                dto.setGroupName(matcher.group(2).trim());
                dto.setRefrigeratorName(matcher.group(3).trim());
                dto.setRackCode(matcher.group(4).trim());
                dto.setRow(matcher.group(5).trim());
                dto.setColumn(matcher.group(6).trim());
                dto.setExtractDesc(matcher.group(7).trim());
                return dto;
            } catch (Exception e) {
                // 内容格式不匹配
                log.info("内容格式匹配: {}", inputString);
            }
        }
        return null;
    }

}
