package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.PhysicalSampleItemDto;

import java.util.Collection;
import java.util.List;

/**
 * 体检样本项目 Service
 * 
 * <AUTHOR>
 * @since 2023/4/4 17:11
 */
public interface PhysicalSampleItemService {

    /**
     * 添加 体检样本项目s
     *
     */
    void addPhysicalSampleItems(List<PhysicalSampleItemDto> list);

    /**
     * 获取 体检样本项目
     */
    List<PhysicalSampleItemDto> selectByPhysicalSampleIds(Collection<Long> physicalSampleIds);

    /**
     * 获取 体检样本项目
     */
    List<PhysicalSampleItemDto> selectByPhysicalSampleId(long physicalSampleId);


    /**
     * 根据id批量删除
     * @param physicalSampleItemIds
     * @return
     */
    int deleteById(Collection<Long> physicalSampleItemIds);

}
