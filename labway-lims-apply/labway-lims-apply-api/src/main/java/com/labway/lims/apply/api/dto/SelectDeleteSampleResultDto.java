package com.labway.lims.apply.api.dto;

import cn.hutool.core.lang.Assert;
import lombok.Data;

import java.util.Date;

@Data
public class SelectDeleteSampleResultDto {

    /**
     * 删除时间范围
     */
    private Date deleteStartDate;
    private Date deleteEndDate;

    /**
     * 检验日期
     */
    private Date testStartDate;
    private Date testEndDate;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 条码号
     */
    private String barcode;


    public void verify() {
        if (deleteStartDate == null || deleteEndDate == null) {
            throw new IllegalArgumentException("删除日期不能为空");
        }
        Assert.notNull(groupId, "专业组不能为空");
    }
}
