package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;

/**
 * 打印信息
 */
@Getter
@Setter
public class ApplySamplePrintInfoDto implements Serializable {

    /**
     * 申请单样本ID
     */
    private Collection<Long> applySampleIds;

    /**
     * 打印人ID
     */
    private Long printerId;

    /**
     * 打印人名称
     */
    private String printerName;

    /**
     * 打印时间
     */
    private Date printDate;

    /**
     * 是否打印:1已打印，0未打印
     * @see YesOrNoEnum
     */
    private Integer isPrint;
}
