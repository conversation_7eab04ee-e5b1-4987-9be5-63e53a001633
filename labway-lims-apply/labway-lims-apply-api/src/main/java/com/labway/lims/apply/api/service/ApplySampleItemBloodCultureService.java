package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Set;

public interface ApplySampleItemBloodCultureService {

    /**
     * 根据申请单样本项目ID查询
     */
    @Nullable
    ApplySampleItemBloodCultureDto selectByApplySampleItemId(long applySampleItemId);

    /**
     * 根据id删除
     */
    void deleteByApplySampleItemBloodCultureId(long applySampleItemBloodCultureId);

    /**
     * 批量新增
     */
    void addApplySampleItemBloodCultures(List<ApplySampleItemBloodCultureDto> applySampleItemBloodCultures);

    /**
     *  根据样本申请单id项目id去查询血培养项目
     * @param testItemId
     * @param applySampleId
     * @return
     */
    ApplySampleItemBloodCultureDto selectByApplySampleIdAndItemId(Long testItemId, Long applySampleId);

    /**
     *  根据样本申请id去查询血培养项目
     * @param applySampleId
     * @return
     */
    ApplySampleItemBloodCultureDto selectByApplySampleId(Long applySampleId);

    /**
     *  根据CultureId去修改样本中血培养信息
     * @param applySampleItemBloodCultureDto
     * @return
     */
    Boolean updateByCultureId(ApplySampleItemBloodCultureDto applySampleItemBloodCultureDto);

   List<ApplySampleItemBloodCultureDto> selectApplySampleIds(Set<Long> applySampleIds);
}
