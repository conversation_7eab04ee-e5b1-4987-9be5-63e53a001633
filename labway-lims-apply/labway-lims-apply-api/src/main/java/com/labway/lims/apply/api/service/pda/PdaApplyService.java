package com.labway.lims.apply.api.service.pda;


import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.BaseDataQueryDto;
import com.labway.lims.apply.api.dto.HisCancelSignParam;
import com.labway.lims.apply.api.dto.PdaApplyAndItemDto;
import com.labway.lims.apply.api.dto.PdaApplyDto;
import com.labway.lims.apply.api.dto.PdaApplyInfoDto;
import com.labway.lims.apply.api.dto.PdaEntryTestApplyDto;
import com.labway.lims.apply.api.dto.SignPdaApplyDto;
import com.labway.lims.apply.api.dto.TestApplyDto;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface PdaApplyService {


    /**
     * pda添加申请单
     * @param addApply
     * @return
     */
    PdaApplyInfoDto addApply(PdaEntryTestApplyDto addApply);

    /**
     * pda添加申请单
     * @param applyDto
     * @return
     */
    boolean addApply(PdaApplyDto applyDto);

    /**
     * 根据主条码获取申请单 最多两个
     */
    List<PdaApplyDto> selectByMasterBarcode(String masterBarcode);

    /**
     * 根据主条码批量获取申请单 每个value最多两个
     */
    List<PdaApplyDto> selectByMasterBarcodes(Collection<String> masterBarcodes);

    /**
     * 根据主条码批量获取申请单 每个value最多两个
     */
    Map<String, List<PdaApplyDto>> selectByMasterBarcodesAsMap(Collection<String> masterBarcodes);

    /**
     * 修改申请单
     */
    PdaApplyInfoDto update(TestApplyDto update);

    /**
     * 根据id查询
     */
    PdaApplyDto selectById(Serializable pdaApplyId);

    void updateApplyById(PdaApplyDto apply);

    /**
     * 作废条码
     */
    void abolishMasterBarcode(String masterBarcode);

    /**
     * 根据主条码删除申请单
     * @param masterBarcode
     */
    void deleteByMasterBarcode(String masterBarcode);

    /**
     * 根据用户id和 时间 和机构 查询
     * @param userId
     * @return
     */
    List<PdaApplyDto> selectByDate(BaseDataQueryDto baseDataQueryDto, Long userId);

    /**
     * 判断条码是否废除
     */
    void masterBarcodeIsAbolish(String masterBarcode);


    /**
     * 签收PDA申请单
     */
    ApplyInfo sign(SignPdaApplyDto dto);

    /**
     * 获取PDA申请单
     */
    PdaApplyAndItemDto get(String masterBarcode, Long hspOrgId);

    /**
     * 取消签收
     */
    void cancelSign(HisCancelSignParam hisCancelSignParam);

    /**
     * 删除pda录入信息
     */
    void deleteByPdaApplyId(Long pdaApplyId);
}
