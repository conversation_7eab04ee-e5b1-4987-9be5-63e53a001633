package com.labway.lims.apply.api.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @Description 业务中台查询物料条码号
 * @Date 2024/11/25 15:59
 */
@Data
public class BusinessCenterMaterialBarcodeSearchDto implements Serializable {

	private static final long serialVersionUID = 1L;

	@NotNull(message = "请输入物料的信息")
	private List<MaterialInfoDto> materialInfos;

	@Data
	public static class MaterialInfoDto implements Serializable {
		@NotBlank(message = "请输入物料批次号")
		private String batchNo;

		@NotBlank(message = "请输入物料编码")
		private String materialCode;

	}

}
