package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.SampleTwoPickDto;
import com.labway.lims.apply.api.dto.SampleTwoUnPickInfoDto;

import java.util.Collection;

/**
 * 样本二次分拣，具体的各个检验实现
 */
public interface ISampleTwoPicker {

    /**
     * 二次分拣
     *
     * @return sample_id 泛指各个检验样本的ID
     */
    long twoPick(SampleTwoPickDto stp);

    /**
     * 取消二次分拣
     */
    SampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds);
}
