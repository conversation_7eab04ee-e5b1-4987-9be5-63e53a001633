package com.labway.lims.apply.api.service;


import com.labway.lims.apply.api.dto.ApplySampleImageDto;

import java.util.List;

/**
 * 申请单样本图片表(ApplySampleImage)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-04 12:32:19
 */
public interface ApplySampleImageService {

    /**
     * 通过ID查询单条数据
     *
     * @param applySampleImageId 主键
     * @return 实例对象
     */
    ApplySampleImageDto queryById(Long applySampleImageId);


    /**
     * 新增数据
     *
     * @param applySampleImage 实例对象
     * @return 实例对象
     */
    Integer insert(ApplySampleImageDto applySampleImage);

    /**
     * 修改数据
     *
     * @param applySampleImage 实例对象
     * @return 实例对象
     */
    Integer update(ApplySampleImageDto applySampleImage);

    /**
     * 通过主键删除数据
     *
     * @param applySampleImageId 主键
     * @return 是否成功
     */
    Integer deleteById(Long applySampleImageId);

    Integer insertBatch(List<ApplySampleImageDto> applySampleImageDtos);

    /**
     * 根据申请单样本id查询样本图片
     * @param sampleIds
     * @return
     */
    List<ApplySampleImageDto> queryByApplySampleIds(List<Long> sampleIds);

    /**
     * 根据申请单样本id查询样本图片
     * @param applyId
     * @param imageUrl
     * @return
     */
    List<ApplySampleImageDto> queryByImageUrl(Long applyId, String imageUrl);

    /**
     * 根据主键集合删除数据
     * @param collect
     * @return
     */
    Integer deleteByIds(List<Long> collect);
}
