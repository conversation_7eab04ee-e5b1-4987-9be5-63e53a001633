package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

@Getter
@Setter
public class GroupMaterialApplyDetailAddDto implements Serializable {

    /**
     * 物料id
     */
    private Long materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 申领主单位数量
     */
    private BigDecimal applyMainNumber;

    /**
     * 申领辅单位数量
     */
    private BigDecimal applyAssistNumber;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        final GroupMaterialApplyDetailAddDto that = (GroupMaterialApplyDetailAddDto) o;
        return Objects.equals(materialId, that.materialId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(materialId);
    }
}
