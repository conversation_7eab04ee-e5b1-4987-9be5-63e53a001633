package com.labway.lims.apply.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ApplySampleImageDto implements Serializable {
    private static final long serialVersionUID = 551461037994768047L;
    /**
     * 申请单样本图片id
     */
    private Long applySampleImageId;
    /**
     * 申请单id
     */
    private Long applyId;
    /**
     * 申请单样本id
     */
    private Long applySampleId;
    /**
     * 图片地址
     */
    private String imageUrl;
    /**
     * 是否删除 0否1是
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 创建人id
     */
    private Long creatorId;
    /**
     * 跟新时间
     */
    private Date updateDate;
    /**
     * 更新人id
     */
    private Long updaterId;
    /**
     * 跟新人名称
     */
    private String updaterName;
    /**
     * 图片名称
     */
    private String imageName;
    /**
     * 创建人名称
     */
    private String creatorName;


}
