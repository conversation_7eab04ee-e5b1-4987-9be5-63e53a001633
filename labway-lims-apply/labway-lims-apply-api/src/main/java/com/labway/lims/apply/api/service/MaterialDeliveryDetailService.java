package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.MaterialDeliveryDetailDto;

import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;

/**
 * <p>
 * 物料出库详情 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
public interface MaterialDeliveryDetailService {

    /**
     * 添加 物料出库单
     * <p>
     * 注意: 此方法被 {@link MaterialDeliveryRecordService#businessCenterDelivery} 调用 不用去获取登录人，会获取不到
     */
    void addMaterialDeliveryDetails(List<MaterialDeliveryDetailDto> list);

    /**
     * 根据出库单号 获取出库详情
     */
    List<MaterialDeliveryDetailDto> selectByDeliveryNo(String deliveryNo, long orgId);

    /**
     * 根据出库单号 获取出库详情
     */
    List<MaterialDeliveryDetailDto> selectByDeliveryNos(Collection<String> deliveryNos, long orgId);

    /**
     * 删除物料出库详情， 如果物料出库详情删除完了， 则删除物料信息
     * @param detailIds
     */
    void deleteBydetailIds(Collection<Long> detailIds);
}
