package com.labway.lims.apply.api.dto.es;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 查询es 数据 并获取 id
 * 
 * <AUTHOR>
 * @since 2023/4/20 10:01
 */
@Getter
@Setter
public class SelectEsIdBySampleEsQueryDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * es数据id
     */
    private String esId;
    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 是否打印:1已打印，0未打印
     */
    private Integer isPrint;

}
