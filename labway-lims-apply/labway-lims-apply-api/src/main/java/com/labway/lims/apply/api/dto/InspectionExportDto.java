package com.labway.lims.apply.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class InspectionExportDto implements Serializable {

    /**
     * ID
     */
    private Long infectionSampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 样本申请单ID
     */
    private Long applySampleId;

    /**
     * 申请时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date applyDate;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 科室
     */
    private String dept;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 检验项目编码
     */

    private String testItemCode;
    /**
     * 检验项目名称
     */

    private String testItemName;

    /**
     * 收费数量
     */
    private Integer count;

    /**
     * 结果 （经过一系列的计算 转换最终得到的结果值）
     */
    private String result;

    /**
     * 单位
     */
    private String unit;

    /**
     * 结果范围
     */
    private String range;

    /**
     * 检验判定 UP  DOWN  NORMAL
     * @see TestJudgeEnum
     */
    private String judge;

    /**
     * 结果备注
     */
    private String resultRemark;



    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;


}
