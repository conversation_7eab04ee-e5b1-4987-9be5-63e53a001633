package com.labway.lims.apply.api.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class Page<T extends Serializable> implements Serializable {

    /**
     * 当前页
     */
    private int pageNum;
    /**
     * 数量
     */
    private int pageSize;
    /**
     * 数据条数
     */
    private long total;

    /**
     * 数据
     */
    private List<T> data;

}
