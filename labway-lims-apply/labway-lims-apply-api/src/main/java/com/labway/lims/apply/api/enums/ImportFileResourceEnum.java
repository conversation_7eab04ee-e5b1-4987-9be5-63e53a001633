package com.labway.lims.apply.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImportFileResourceEnum {

    /**
     * 样本导入
     */
    SAMPLE_IMPORT(1, "样本导入"),

    ;
    private final int code;
    private final String desc;

    public static ImportFileResourceEnum getByCode(int code) {
        for (ImportFileResourceEnum value : ImportFileResourceEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
