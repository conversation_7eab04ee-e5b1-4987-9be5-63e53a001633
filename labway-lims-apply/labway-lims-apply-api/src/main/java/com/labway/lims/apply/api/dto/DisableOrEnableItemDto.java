package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class DisableOrEnableItemDto implements Serializable {

    /**
     * 申请单样本id
     */
    private List<Long> applySampleIds;

    /**
     * 原因code
     */
    private String causeCode;

    /**
     * 原因
     */
    private String cause;

    /**
     * 是否是禁用操作
     */
    private Integer isDisable;

    /**
     * 要禁用/启用的检验项目
     */
    private List<ApplySampleItem> applySampleItems;

    @Getter
    @Setter
    public static class ApplySampleItem {
        /**
         * 申请单样本id
         */
        private Long applySampleId;

        /**
         * 申请单样本项目id
         */
        private Long applySampleItemId;
    }

}
