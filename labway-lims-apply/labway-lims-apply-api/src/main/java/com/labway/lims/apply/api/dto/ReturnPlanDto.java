package com.labway.lims.apply.api.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class ReturnPlanDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 专业组计划id
     */
    @NotNull(message = "专业组计划单号不能为空")
    private String planNo;

    /**
     * 退回原因
     */
    private String returnReason;

}
