package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <pre>
 * ApplySampleItemTwoPickDetailDto
 * 条码项目二次分拣明细
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/11/6 14:47
 */
@Getter
@Setter
public class ApplySampleItemTwoPickDetailDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long applySampleId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 样本状态 待审核 待复查 待二审 已审核 终止（99）
     * @see SampleStatusEnum
     */
    private Integer status;

    /**
     * 二次分拣日期
     */
    private Date twoPickDate;

    /**
     * 是否已经二次分拣 1是，0不是
     *
     * @see YesOrNoEnum
     */
    private Integer isTwoPick;

    /**
     * 项目状态
     * 终止检验状态：0正常，1终止收费，2终止不收费
     *
     * @see StopTestStatus
     */
    private Integer stopStatus;

    /**
     * 项目状态
     * 是否禁用 1是，0否
     */
    private Integer isDisabled;

}
