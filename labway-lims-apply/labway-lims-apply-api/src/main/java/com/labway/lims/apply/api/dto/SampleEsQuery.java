package com.labway.lims.apply.api.dto;

import cn.hutool.core.util.IdcardUtil;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.ApplySupplierEnum;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.StopStatusEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.Tolerate;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@Accessors(chain = true)
@Builder
public class SampleEsQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Tolerate
    public SampleEsQuery() {
    }

    /**
     * 查询字段
     */
    private List<String> fields;

    /**
     * 当前页
     */
    private Integer pageNo;
    /**
     * 每页数量
     */
    private Integer pageSize;

    // 申请单信息
    /**
     * 申请单id
     */
    private Set<Long> applyIds;

    /**
     * 主条码
     */
    private Set<String> masterBarcodes;

    /**
     * 科室
     */
    private String dept;

    /**
     * 是否加急 1是 0否
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 申请单状态
     *
     * @see ApplyStatusEnum
     */
    private Integer applyStatus;

    /**
     * 申请单状态
     *
     * @see ApplyStatusEnum
     */
    private Set<Integer> applyStatusSet;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 是否患者名称全匹配
     */
    private boolean isPatientNameCompleteMatch;

    /**
     * 患者年龄
     */
    private Integer startPatientAge;

    /**
     * 患者年龄
     */
    private Integer endPatientAge;

    /**
     * 患者子年龄
     */
    private Integer patientSubage;

    /**
     * 患者子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 患者出生日期
     */
    private Date startPatientBirthday;

    /**
     * 患者出生日期
     */
    private Date endPatientBirthday;

    /**
     * 身份证
     */
    private String patientCard;

    /**
     * 证件类型
     *
     * @see PatientCardTypeEnum
     */
    private String patientCardType;

    /**
     * 床号
     */
    private String patientBed;

    /**
     * 性别
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 就诊卡号 (门诊|住院号)
     */
    private String patientVisitCard;

    /**
     * 手机号
     */
    private String patientMobile;

    /**
     * 患者地址
     */
    private String patientAddress;

    /**
     * 就诊类型 (申请类型)
     */
    private Set<String> applyTypes;

    /**
     * 样本性状编码
     */
    private String samplePropertyCode;

    /**
     * 临床诊断
     */
    private String diagnosis;

    /**
     * 送检医生编码
     */
    private Set<String> sendDoctorCodes;

    /**
     * 申请时间
     */
    private Date startApplyDate;

    /**
     * 申请时间
     */
    private Date endApplyDate;

    /**
     * 采样时间
     */
    private Date startSamplingDate;
    /**
     * 采样时间
     */
    private Date endSamplingDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 申请单来源
     *
     * @see ApplySourceEnum
     */
    private String source;

    /**
     * 供应商
     *
     * @see ApplySupplierEnum
     */
    private String supplier;

    /**
     * 送检机构
     */
    private Set<Long> hspOrgIds;

    /**
     * 送检机构编码
     */
    private Set<String> hspOrgCodes;
    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;

    // -------------------------------------------------申请单样本信息-------------------------------------------------
    /**
     * 申请单样本id
     */
    private Set<Long> applySampleIds;

    /**
     * 外部条码
     */
    private Set<String> outBarcodes;

    /**
     * 样本条码
     */
    private Set<String> barcodes;

    /**
     * 报告编号
     */
    private Set<String> reportNos;

    /**
     * 样本条码 OR 外部条码号
     */
    private Set<String> barcodeOrOutbarcodes;

    /**
     * 专业组id
     */
    private Set<Long> groupIds;

    /**
     * 管型
     */
    private Set<String> tubeCodes;

    /**
     * 样本类型
     */
    private Set<String> sampleTypeCodes;

    /**
     * 试管架id
     */
    private Set<Long> rackIds;

    /**
     * 检验项目ID
     */
    private Set<Long> testItemIds;
    /**
     * 检验项目code
     */
    private Set<String> testItemCodes;
    /**
     * 检验项目专业组id
     */
    private Set<Long> testItemGroupIds;

    // 检验样本信息 （常规检验、微生物检验、院感......）
    /**
     * 样本id （可能是常规检验|微生物|院感...）
     */
    private Set<Long> sampleIds;

    /**
     * 样本号
     */
    private Set<String> sampleNos;

    /**
     * 结果类型
     * @see com.labway.lims.genetics.api.enums.GeneticsResultEnum
     * @see com.labway.lims.specialty.api.enums.SpecialtyResultEnum
     */
    private Set<Integer> resultTypes;

    /**
     * 专业小组id
     */
    private Set<Long> instrumentGroupIds;
    /**
     * 专业小组id
     */
    private Set<Long> instrumentIds;

    /**
     * 签收时间
     */
    private Date startSignDate;

    /**
     * 签收时间
     */
    private Date endSignDate;

    /**
     * 检验时间
     */
    private Date startTestDate;

    /**
     * 检验时间
     */
    private Date endTestDate;

    /**
     * 样本备注
     */
    private String sampleRemark;

    /**
     * 结果备注
     */
    private String resultRemark;

    /**
     * 一审人id
     */
    private Set<Long> oneCheckerIds;

    /**
     * 一审时间
     */
    private Date startOneCheckDate;

    /**
     * 一审时间
     */
    private Date endOneCheckDate;
    /**
     * 终审人id
     */
    private Set<Long> finalCheckerIds;
    /**
     * 终审时间
     */
    private Date startFinalCheckDate;
    /**
     * 终审时间
     */
    private Date endFinalCheckDate;
    /**
     * 项目类型 常规检验、微生物检验、院感检验 区分做什么检验的
     *
     * @see ItemTypeEnum
     */
    private Set<String> itemTypes;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 创建人id
     */
    private Set<Long> creatorIds;

    /**
     * 创建时间|录入时间
     */
    private Date startCreateDate;

    /**
     * 创建时间|录入时间
     */
    private Date endCreateDate;

    /**
     * 二次分拣时间
     */
    private Date startTwoPickDate;

    /**
     * 二次分拣时间
     */
    private Date endTwoPickDate;

    /**
     * 一次分拣时间
     */
    private Date startOnePickDate;

    /**
     * 一次分拣时间
     */
    private Date endOnePickDate;

    /**
     * 排序
     */
    private List<Sort> sorts;

    /**
     * 是否审核 1是 | 0否 | null全部
     *
     * @see YesOrNoEnum
     */
    private Integer isAudit;

    /**
     * 需要排除的【样本状态】
     *
     * @see SampleStatusEnum
     */
    private List<Integer> excludeSampleStatus;

    /**
     * 是否禁用 1是 0 否
     *
     * @see YesOrNoEnum
     */
    private Integer isDisabled;

    /**
     * 是否归档:0未归档，1已归档
     *
     * @see YesOrNoEnum
     */
    private Integer isArchive;

    /**
     * 是否已经二次分拣 1是，0不是
     */
    private Integer isTwoPick;

    /**
     * 是否是免疫二次分拣 1：是 0：否
     */
    private Integer isImmunityTwoPick;

    /**
     * 是否已经一次分拣 1是，0不是
     *
     * @see YesOrNoEnum
     */
    private Integer isOnePick;

    /**
     * 样本状态
     *
     * @see SampleStatusEnum
     */
    private Set<Integer> sampleStatus;

    /**
     * 终审时间 或 创建时间|录入时间
     */
    private Date startFinalCheckOrCreateDate;

    /**
     * 终审时间 或 创建时间|录入时间
     */
    private Date endFinalCheckOrCreateDate;

    /**
     * 样本检验项目终止检验状态
     */
    private Set<Integer> stopStatus;

    /**
     * 样本检验项目终止检验状态 - 排除
     */
    private Set<Integer> excludeStopStatus;

    /**
     * 审核开始时间
     */
    private Date beginAuditDate;
    /**
     * 审核开始结束时间
     */
    private Date endAuditDate;

    // ---------------------申请单样本------------------------

    /**
     * 检验人ID
     */
    private Long testerId;

    /**
     * 外送机构id
     */
    private Set<Long> exportOrgIds;

    /**
     * 体检单位ID
     */
    private Set<Long> physicalGroupIds;

    /**
     * 报告项目编码
     */
    private Set<String> reportItemCodes;
    /**
     * 报告项目实际上机仪器
     */
    private Set<Long> reportItemInstrumentIds;

    /**
     * 是否打印:1已打印，0未打印
     * @see YesOrNoEnum
     */
    private Integer isPrint;

    /**
     * 是否外送
     *
     * @see YesOrNoEnum
     */
    private Integer isOutsourcing;

    private List<Object> searchAfter;

    /**
     * 高级查询
     */
    private SampleEsQueryAdvanced advancedEsQuery;

    /**
     * 检验状态：未复核、已签收、正在检验、完成检验
     * <pre>
     * 1未复核：未复核和未双输复核的数据
     * 2已签收：完成双输复核、复核和样本签收的数据
     * 3正在检验：二次分拣完成且未审核的数据
     * 4完成检验：已审核的数据
     * </pre>
     *
     * @see ApplyStatusEnum
     */
    private Integer status;

    /**
     * 项目状态
     * 0:正常，1:已终止（收费），2:已终止（未收费）,3:禁止
     *
     * @see StopStatusEnum
     */
    private Integer itemStatus;

    /**
     * 是否是急诊项目
     *
     * @see YesOrNoEnum
     */
    private Integer itemUrgent;

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static final class Sort implements Serializable {
        /**
         * 属性名
         */
        private String filedName;

        /**
         * 排序方式 ASC DESC
         */
        private String order;

    }

    /**
     * 细菌备注编码
     */
    private List<String> germRemarkCodes;

    /**
     * 非空字段
     */
    private Set<String> notnullFields;

    /**
     * 更新时间
     */
    private Date updateDateBegin;
    private Date updateDateEnd;

    /**
     * 阴阳性结果
     */
    private Set<String> yinAndYang;

    /**
     * 判断不为空的字段
     */
    private Set<String> notEmptyFields;

    /**
     * 核型
     */
    private String karyotype;

    /**
     * 组装ES 同人同天的参数 -----统一ES处理
     * @param apply
     */
    public void combineOneDayOnePersonParam(ApplyDto apply){
        if (Objects.nonNull(apply)){
            // 优先按照‘身份证号’判断，没有的情况下则用‘性别、送检机构、名字’查询
            // 增加按照同一机构和门诊/住院号判断
            if (IdcardUtil.isValidCard(apply.getPatientCard())) {
                setPatientCard(apply.getPatientCard());
            } else {
                setHspOrgIds(Collections.singleton(apply.getHspOrgId()));
                setPatientName(apply.getPatientName());
                //患者姓名全匹配
                setPatientNameCompleteMatch(true);
                setPatientSex(apply.getPatientSex());
                //1.1.3新增 年龄限制在-3~+0 之间
                if (Objects.nonNull(apply.getPatientAge())){
                    setStartPatientAge(apply.getPatientAge() - 3);
                    setEndPatientAge(apply.getPatientAge());
                }
            }
        }

    }

}
