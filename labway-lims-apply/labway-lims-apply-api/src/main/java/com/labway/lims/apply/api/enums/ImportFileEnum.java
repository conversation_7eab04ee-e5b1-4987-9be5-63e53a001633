package com.labway.lims.apply.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImportFileEnum {

    EXCEL(1, "excel"),

    ;
    private final int code;
    private final String desc;

    public static ImportFileEnum getByCode(int code) {
        for (ImportFileEnum value : ImportFileEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
