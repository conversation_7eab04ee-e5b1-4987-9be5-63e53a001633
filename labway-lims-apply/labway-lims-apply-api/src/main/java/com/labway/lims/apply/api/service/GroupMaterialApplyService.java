package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.BusinessCenterRejectApplyDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDetailAddDto;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDto;
import com.labway.lims.apply.api.dto.QueryApprovalPlanPageDto;
import com.labway.lims.apply.api.vo.GroupMaterialPlanVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface GroupMaterialApplyService {

    /**
     * 根据id查询
     */
    GroupMaterialApplyDto selectById(long id);

    /**
     * 根据申领单号查询
     */
    GroupMaterialApplyDto selectByOrgIdAndApplyNo(long orgId, String applyNo);

    /**
     * 根据申领单号、专业组id 查找
     */
    GroupMaterialApplyDto selectByGroupIdAndApplyNo(long groupId, String applyNo);

    /**
     * 查询当前专业组的物料申领单
     */
    List<GroupMaterialApplyDto> selectCurrentGroupMaterialApply(String applyNo, Date startDate, Date endDate, Long groupId);

    /**
     * 添加物料信息且生成申领单
     */
    Map<String, Object> add(List<GroupMaterialApplyDetailAddDto> params);

    /**
     * 根据ID修改
     */
    void updateByApplyId(GroupMaterialApplyDto dto);

    /**
     * 业务中台退回申领单
     */
    void businessCenterRejectApply(BusinessCenterRejectApplyDto dto);

    /**
     * 审核
     */
    void audit(String applyNo);

    /**
     * 根据申领单号集合查询
     */
    List<GroupMaterialApplyDto> selectByOrgIdAndApplyNos(long orgId, List<String> applyNos);

    /**
     * 已审批物料申领单列表
     * @param queryApprovalPlanPageDto
     * @return
     */
    List<GroupMaterialPlanVo> queryApprovalPlanPage(QueryApprovalPlanPageDto queryApprovalPlanPageDto);

    /**
     * 查询待审核的物料申领单
     * @param groupId
     * @return
     */
    List<GroupMaterialApplyDto> selectUnApprovalMaterialApply(Long orgId, Long groupId);
}
