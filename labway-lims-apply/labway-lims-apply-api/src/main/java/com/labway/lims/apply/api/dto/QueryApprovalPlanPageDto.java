package com.labway.lims.apply.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.MaterialApplyStatusEnum;
import com.labway.lims.apply.api.enums.MaterialApplyTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class QueryApprovalPlanPageDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 专业组id
     */
    private Long groupId;

    /**
     * 单据号
     */
    private String planNo;


    /**
     * 审核时间 - 开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date startAuditDate;

    /**
     * 审核时间 - 结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endAuditDate;

    /**
     *  物料编码/名称
     */
    private String materialCodeOrName;

    /**
     * 申领单类型 1物料申领 2专业组计划
     * {@link MaterialApplyTypeEnum}
     */
    @NotNull(message = "申领单类型不能为空")
    private Integer materialApplyType;

    /**
     * 状态
     *
     * @see MaterialApplyStatusEnum
     */
    private List<String> status;

}
