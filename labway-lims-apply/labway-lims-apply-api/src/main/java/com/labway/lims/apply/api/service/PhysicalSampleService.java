package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.PhysicalRegisterDto;
import com.labway.lims.apply.api.dto.PhysicalSampleDto;
import com.labway.lims.apply.api.dto.PhysicalSampleItemDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 体检样本 Service
 * 
 * <AUTHOR>
 * @since 2023/4/4 13:54
 */
public interface PhysicalSampleService {

    /**
     * 添加 体检样本s
     *
     */
    void addPhysicalSamples(List<PhysicalSampleDto> list);

    /**
     * 查询 体检样本
     */
    List<PhysicalSampleDto> selectByPhysicalRegisterIds(Collection<Long> physicalRegisterIds);

    /**
     * 查询 体检样本
     */
    List<PhysicalSampleDto> selectByBarcodes(Collection<String> barcodes, long orgId);

    /**
     * 查询 体检样本
     */
    @Nullable
    PhysicalSampleDto selectByBarcode(String barcode, long orgId);

    /**
     * 修改 体检样本
     */
    void updateByPhysicalSampleId(PhysicalSampleDto dto);

    /**
     * 查询 体检样本 根据体检批次
     */
    List<PhysicalSampleDto> selectByPhysicalBatchId(long physicalBatchId);

    /**
     * 体检 打印条码 保存数据
     * 
     * @param sampleDtoList 需要新增的体检样本
     * @param sampleItemDtoList 需要新增的体检样本项目
     * @param registerDtoList 需要修改打印状态的 体检人
     */
    void printBarcodeHandleData(List<PhysicalSampleDto> sampleDtoList, List<PhysicalSampleItemDto> sampleItemDtoList,
        List<PhysicalRegisterDto> registerDtoList);

    /**
     * 根据id删除体检样本表
     * @param physicalSampleId
     */
    boolean deleteById(long physicalSampleId);
}
