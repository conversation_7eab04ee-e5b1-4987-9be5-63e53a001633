package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 一次待交接的样本
 */
@Getter
@Setter
public class OnePickedApplySampleDto extends ApplySampleDto implements Serializable {

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 多少行
     */
    private Integer row;

    /**
     * 多少列
     */
    private Integer column;

    /**
     * 操作时间
     */
    private Date operateDate;
}
