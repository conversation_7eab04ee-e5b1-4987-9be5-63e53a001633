package com.labway.lims.apply.api.dto.es;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 血培养
 */
@Getter
@Setter
public class BloodCultureInspectionDto extends CombineOnePersonDto {

    /**
     * 一次审核
     */
    private Long oneCheckerId;
    /**
     * 一次审核人名称
     */
    private String oneCheckerName;

    /**
     * 一次审核时间
     */
    private Date oneCheckDate;
    /**
     * 二次审核人
     */
    private Long twoCheckerId;
    /**
     * 二次审核人名称
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    private Date twoCheckDate;

    /**
     * 初审报告id
     */
    private Long oneCheckSampleReportId;

    /**
     * 终审报告id
     */
    private Long twoCheckSampleReportId;


    /**
     * 初步结果
     */
    private List<Result> oneResults;

    /**
     * 最终结果
     */
    private List<Result> twoResults;

    /**
     * 初步备注
     */
    private List<Remark> oneRemarks;

    /**
     * 最终备注
     */
    private List<Remark> twoRemarks;

    /**
     * 结果
     */
    @Setter
    @Getter
    @Accessors(chain = true)
    public static final class Result implements Serializable {
        private String result;

        /**
         * 结果编码
         */
        private String resultCode;

        private Long id;
    }

    /**
     * 备注
     */
    @Setter
    @Getter
    @Accessors(chain = true)
    public static final class Remark implements Serializable {
        private String remark;

        private Long id;
    }


}
