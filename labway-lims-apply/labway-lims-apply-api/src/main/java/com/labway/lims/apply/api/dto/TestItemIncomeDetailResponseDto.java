package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售项目收入查询--明细 Dto
 * 
 * <AUTHOR>
 * @since 2023/5/15 10:13
 */
@Setter
@Getter
@Accessors(chain = true)
public class TestItemIncomeDetailResponseDto implements Serializable {

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 数量合计
     */
    private Integer countSum;
    /**
     * 标准收费合计
     */
    private BigDecimal feePriceSum;
    /**
     * 折后总额
     */
    private BigDecimal discountFeePriceSum;
    /**
     * 汇总行
     */
    private List<TestItemIncomeDetailItemDto> itemList;

}
