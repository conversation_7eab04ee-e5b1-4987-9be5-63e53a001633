package com.labway.lims.apply.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MaterialApplyTypeEnum {

    MATERIAL_APPLY(1, "物料申领"),
    MATERIAL_PLAN(2, "专业组计划"),

    ;
    private final int code;
    private final String desc;

    public static MaterialApplyTypeEnum getByCode(int code) {
        for (MaterialApplyTypeEnum value : MaterialApplyTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return MATERIAL_PLAN;
    }
}
