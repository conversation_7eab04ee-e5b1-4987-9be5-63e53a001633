package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 逻辑试管架占用
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class RackLogicSpaceDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long rackLogicSpaceId;

    /**
     * 逻辑试管架
     */
    private Long rackLogicId;

    /**
     * 试管架
     */
    private Long rackId;

    /**
     * 多少行
     */
    private Integer row;

    /**
     * 多少列
     */
    private Integer column;

    /**
     * 放置的申请单样本
     */
    private Long applySampleId;

    /**
     * 1: 已使用 0:可使用 2: 不可使用
     */
    private Integer status;

    /**
     * 1 已删除 0 未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

}
