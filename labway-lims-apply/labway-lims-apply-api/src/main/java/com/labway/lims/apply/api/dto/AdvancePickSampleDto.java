package com.labway.lims.apply.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.base.api.dto.TestItemDto;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 待二次分拣的申请单样本
 */
@Getter
@Setter
public class AdvancePickSampleDto implements Serializable {


    /**
     * 预分拣样本信息
     */
    private List<AdvancePickSampleInfo> advancePickSampleInfoList;


    /**
     * 原始标本合计
     */
    private Integer originalSampleCount;


    /**
     * 标本合计
     */
    private Integer totalSampleCount;


    /**
     * 获取预分拣样本信息数量
     */
    public Integer getAdvancePickSampleCount() {
        return advancePickSampleInfoList == null ? 0 : advancePickSampleInfoList.size();
    }

    /**
     * 获取预分拣样本原始数量
     */
    public Integer getOriginalSampleCount() {
        if (advancePickSampleInfoList == null) {
            return 0;
        }
        return (int) advancePickSampleInfoList.stream()
                .map(AdvancePickSampleInfo::getApplySampleId).distinct().count();
    }


    @Data
    @NoArgsConstructor
    public static class AdvancePickSampleInfo extends ApplySampleDto implements Serializable {
        /**
         * 申请单样本ID
         */
        private Long applySampleId;

        /**
         * 申请单id
         */
        private Long applyId;

        /**
         * 条码
         */
        private String barcode;

        /**
         * 病人名称
         */
        private String patientName;


        /**
         * 年龄
         */
        private Integer patientAge;

        /**
         * 子年龄
         */
        private Integer patientSubage;

        /**
         * 月、周、天
         *
         * @see PatientSubAgeUnitEnum
         */
        private String patientSubageUnit;


        /**
         * 性别  性别 1 男，2:女
         *
         * @see SexEnum
         */
        private Integer patientSex;


        /**
         * 预分拣日期
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date advancePickDate;

        /**
         * 样本号
         */
        private String sampleNo;

        /**
         * 样本接收时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date receiveDate;

        /**
         * 最近二次分拣时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date lastTwoPickDate;


        /**
         * 预分拣项目
         */
        List<TestItemDto> testItems;


    }


}
