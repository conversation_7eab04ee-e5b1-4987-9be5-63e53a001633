package com.labway.lims.apply.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class MaterialApplyExportDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    private Integer orderNo;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 申请人姓名
     */
    private String applyUserName;

    /**
     * 状态 返回枚举值
     */
    private String statusValue;

    /**
     * 审核人
     */
    private String checker;

    /**
     * 审核时间
     */
    private Date checkDate;

    /**
     * 物料状态 返回枚举值
     */
    private String materialStatusValue;

    /**
     * 物资编号
     */
    private String materialCode;

    /**
     * 物资名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 申领辅单位数量
     */
    private BigDecimal applyAssistNumber;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 申领主单位数量
     */
    private Integer applyMainNumber;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 厂家
     */
    private String manufacturers;
    
    /**
     * 已出库主单位数量
     */
    private String deliveredMainNumber;
    
    /**
     * 已出库辅单位数量
     */
    private String deliveredAssistNumber;
    
    /**
     * 待出库主单位数量
     */
    private String pendingMainNumber;
    
    /**
     * 待出库辅单位数量
     */
    private String pendingAssistNumber;
}
