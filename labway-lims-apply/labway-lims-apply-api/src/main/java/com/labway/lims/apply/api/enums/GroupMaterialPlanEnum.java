package com.labway.lims.apply.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GroupMaterialPlanEnum {

    SAVE(1, "已保存"),
    SUBMIT(2, "已提交"),
    AUDIT(3, "已审核"),
    REJECT(4, "已退回"),

    ;
    private final int code;
    private final String desc;

    public static GroupMaterialPlanEnum getByCode(int code) {
        for (GroupMaterialPlanEnum value : GroupMaterialPlanEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
