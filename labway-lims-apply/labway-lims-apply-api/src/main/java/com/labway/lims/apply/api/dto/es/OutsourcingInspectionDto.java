package com.labway.lims.apply.api.dto.es;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 外送
 */
@Getter
@Setter
public class OutsourcingInspectionDto extends BaseSampleEsModelDto {

    /**
     * 外送机构的条码号，譬如外送到迪安，那这个就是迪安的条码。注意，这个不是送检机构的条码
     */
    private String exportBarcode;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

    /**
     * 外送机构名称
     */
    private String exportOrgName;

    /**
     * 是否已打印清单，1:是，0:不是
     *
     * @see YesOrNoEnum
     */
    private Integer isPrintList;

    /**
     * 打印清单日期
     */
    private Date printListDate;

    /**
     * 送检时间
     */
    private Date inspectionDate;
    /**
     * 一次审核人
     */
    private String oneCheckerName;

    /**
     * 一次审核
     */
    private Long oneCheckerId;

    /**
     * 一审时间
     */
    private Date oneCheckDate;

    /**
     * 二次审核人
     */
    private Long twoCheckerId;
    /**
     * 二次审核人名称
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    private Date twoCheckDate;


    /**
     * 报告项目
     */
    private List<OutsourcingReportItem> reportItems;


    /**
     * 报告项目
     */
    @Setter
    @Getter
    public static final class OutsourcingReportItem extends BaseSampleEsModelDto.ReportItem implements Serializable {

        /**
         * 结果范围
         */
        private String range;

        /**
         * 状态 1:危机 2:异常 0:正常
         *
         * @see ResultStatusEnum
         */
        private Integer status;

        /**
         * 结果 （经过一系列的计算 转换最终得到的结果值）
         */
        private String result;

        /**
         * 检验判定 UP  DOWN  NORMAL
         *
         * @see TestJudgeEnum
         */
        private String judge;
        /**
         * 来源仪器
         */
        private Long instrumentId;
        /**
         * 来源仪器
         */
        private String instrumentName;
        /**
         * 仪器结果
         */
        private String instrumentResult;
    }
}
