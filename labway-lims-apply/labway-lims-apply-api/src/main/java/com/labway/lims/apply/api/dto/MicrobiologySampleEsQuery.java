package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.Tolerate;

import java.util.List;

/**
 * 微生物 es 查询
 * 
 * <AUTHOR>
 * @since 2023/8/8 14:02
 */
@Getter
@Setter
@Accessors(chain = true)
public class MicrobiologySampleEsQuery extends SampleEsQuery {

    /**
     * 检验结果codes
     */
    private List<String> resultCodes;

    /**
     * 细菌ids
     */
    private List<Long> germIds;

    /**
     * 药物ids
     */
    private List<Long> medicineIds;

    /**
     * 细菌备注s
     */
    private List<String> germRemarkCodes;

    /**
     * 结果属性
     */
    private String resultProperty;

    @Tolerate
    public MicrobiologySampleEsQuery() {}

}
