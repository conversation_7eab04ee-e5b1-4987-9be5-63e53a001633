package com.labway.lims.apply.api.service.pda;

import com.labway.lims.apply.api.dto.BaseDataQueryDto;
import com.labway.lims.apply.api.dto.PdaTobeConfirmedApplyDto;

import java.util.Collection;
import java.util.List;

/**
 * @description pda申请单双属确认表
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface PdaTobeConfirmedApplyService {


    /**
     * 获取RedisKey
     * @param orgId
     * @param masterBarcode
     * @return
     */
    String getAbolishLock(Long orgId, String masterBarcode);

    /**
     * 添加pda
     * @param dto
     */
    boolean addTobeConfirmed(PdaTobeConfirmedApplyDto dto);

    /**
     * 根据主条码查询
     */
    PdaTobeConfirmedApplyDto selectByMasterBarcode(String masterBarcode);

    /**
     * 根据主条码查询
     */
    PdaTobeConfirmedApplyDto selectByMasterBarcodeAndHspOrgCode(String masterBarcode, Long hspOrgId);

    /**
     * 根据主条码批量查询
     */
    List<PdaTobeConfirmedApplyDto> selectByMasterBarcodes(Collection<String> masterBarcodes);

    /**
     * 删除确认信息
     */
    void deleteByMasterBarcode(String masterBarcode);

    /**
     * 根据时间和送检机构
     */
    List<PdaTobeConfirmedApplyDto> selectByDate(BaseDataQueryDto dto);

    /**
     * 确认申请单
     */
    boolean confirmApply(long pdaApplyId);

    /**
     * 回退确认单
     */
    boolean rollbackApply(String masterBarcode);

    /**
     * 批量取消签收
     */
    void cancelSignByMasterCodes(Collection<String> masterBarcodes);


    // 更新确认表为确认状态
    boolean updateStatusByMasterBarcode(String masterBarcode);
}
