package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.PhysicalBatchDto;
import com.labway.lims.apply.api.dto.PhysicalRegisterDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 体检批次 Service
 * 
 * <AUTHOR>
 * @since 2023/3/29 19:50
 */
public interface PhysicalBatchService {

    /**
     * 添加 体检批次
     */
    long addPhysicalBatch(PhysicalBatchDto dto);

    /**
     * 根据 体检单位获取对应 体检批次
     */
    List<PhysicalBatchDto> selectByPhysicalCompanyId(long physicalCompanyId);

    /**
     * 删除 体检批次
     */
    void deleteByPhysicalBatchIds(Collection<Long> physicalBatchIds);

    /**
     * 根据 体检批次id 查找 体检批次
     * 
     */
    @Nullable
    PhysicalBatchDto selectByPhysicalBatchId(long physicalBatchId);

    /**
     * 根据 体检批次id、机构 查找 体检批次
     *
     */
    @Nullable
    PhysicalBatchDto selectByPhysicalBatchNumber(String physicalBatchNumber, long orgId);

    /**
     * 修改 体检批次
     */
    void updateByPhysicalBatchId(PhysicalBatchDto dto);

    /**
     * 体检批次 导入 体检人员
     * 
     * @param physicalBatchDto 体检批次
     * @param targetList 对应体检人员
     */
    void importRegister(PhysicalBatchDto physicalBatchDto, List<PhysicalRegisterDto> targetList);
}
