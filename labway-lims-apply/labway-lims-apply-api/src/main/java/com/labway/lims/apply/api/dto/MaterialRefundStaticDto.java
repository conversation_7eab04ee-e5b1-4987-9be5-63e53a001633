package com.labway.lims.apply.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialRefundStaticDto implements Serializable {

    // 主数量
    private BigDecimal totalMainNum;

    // 辅数量
    private BigDecimal totalAssistNum;

    // 退库单信息
    private List<MaterialRefundRecordDto> materialRefundRecordDtos;


}
