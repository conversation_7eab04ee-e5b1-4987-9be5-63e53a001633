package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 样本二次分拣
 */
@Getter
@Setter
public class SampleTwoPickDto implements Serializable {

    /**
     * 专业组ID
     */
    private Long groupId;


    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 二次分拣时间
     */
    private Date twoPickDate;

    /**
     * 免疫二次分拣时间
     */
    private Date immunityTwoPickDate;

}
