package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.DeleteSampleResultDetailDto;
import com.labway.lims.apply.api.dto.DeleteSampleResultDto;
import com.labway.lims.apply.api.dto.DeleteSampleResultMainDto;
import com.labway.lims.apply.api.dto.SelectDeleteSampleResultDto;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface DeleteResultService {

    /**
     * 新增数据
     */
    void addDeleteSampleResult(DeleteSampleResultDto deleteSampleResultDto);

    /**
     * 根据样本id查询
     */
    DeleteSampleResultMainDto selectBySampleId(long sampleId);

    /**
     * 查询已删除结果样本
     */
    List<DeleteSampleResultMainDto> selectDeletedSample(SelectDeleteSampleResultDto dto);

    /**
     * 查询结果
     */
    List<DeleteSampleResultDetailDto> selectDeletedResult(long deleteSampleResultMainId);

    /**
     * 删除结果
     */
    void deletedBySampleIds(Collection<Long> sampleIds);

    /**
     * 恢复结果
     */
    boolean recoveryResult(long mainId, Set<String> reportItemCodes);
}
