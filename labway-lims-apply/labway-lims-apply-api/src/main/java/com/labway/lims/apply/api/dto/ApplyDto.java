package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.ApplySupplierEnum;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class ApplyDto implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long applyId;

    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 生日
     */
    private Date patientBirthday;

    /**
     * 身份证
     */
    private String patientCard;

    /**
     * 证件类型
     *
     * @see PatientCardTypeEnum
     */
    private String patientCardType;

    /**
     * 床号
     */
    private String patientBed;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 就诊卡号
     */
    private String patientVisitCard;

    /**
     * 手机号
     */
    private String patientMobile;

    /**
     * 申请单来源，手动录入、样本签收，导入？
     *
     * @see ApplySourceEnum
     */
    private String source;

    /**
     * 就诊类型编码，申请单类型
     */
    private String applyTypeCode;

    /**
     * 就诊类型名称
     */
    private String applyTypeName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 样本个数
     */
    private Integer sampleCount;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 样本性状编码
     */
    private String samplePropertyCode;

    /**
     * 申请科室
     */
    private String dept;

    /**
     * 临床诊断
     */
    private String diagnosis;

    /**
     * 送检医生名称
     */
    private String sendDoctorName;

    /**
     * 送检医生编码
     */
    private String sendDoctorCode;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 1: 急诊，0:不急
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 地址
     */
    private String patientAddress;
    /**
     * 供应商，或者说是来源。社区LIS？某友商？
     *
     * @see ApplySupplierEnum
     */
    private String supplier;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 采样时间
     */
    private Date samplingDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人
     */
    private String creatorName;


    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 1. 待双输复核
     * 2. 待复核
     * 3. 已复核
     * 4. 已双输复核
     *
     * @see ApplyStatusEnum
     */
    private Integer status;

    /**
     * 复核人id
     */
    private Long checkerId;

    /**
     * 复核人
     */
    private String checkerName;

    /**
     * 复核时间
     */
    private Date checkDate;

    /**
     * 签收时间
     */
    private Date signDate;

    /**
     * 1: 已删除 0:未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;

    /**
     * 外部条码号
     */
    private String outBarcode;

    /**
     * 病区
     */
    private String inpatientArea;

    /**
     *  医保卡号
     */
    private String visitCardNo;
}
