package com.labway.lims.apply.api.dto;

import java.io.Serializable;
import java.util.Set;

import lombok.Getter;
import lombok.Setter;

/**
 * 批量修改申请单信息
 *
 * <AUTHOR>
 * @since 2024/2/21 14:48
 */
@Getter
@Setter
public class BatchUpdateApplyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请单ids
     */
    private Set<Long> applyIds;

    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 是否刷新报告
     */
    private Boolean refreshReport;
}
