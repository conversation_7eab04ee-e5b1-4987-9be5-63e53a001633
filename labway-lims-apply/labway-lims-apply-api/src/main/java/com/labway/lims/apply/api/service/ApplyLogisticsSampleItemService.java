package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.ApplyLogisticsSampleItemDto;

import java.util.List;

public interface ApplyLogisticsSampleItemService {
    /**
     * 根据 物流申请单ID 查询物流申请单检验项目
     */
    List<ApplyLogisticsSampleItemDto> selectByApplyLogisticsId(long applyLogisticsId);

    /**
     * 根据 物流样本ID 查询物流申请单检验项目
     */
    List<ApplyLogisticsSampleItemDto> selectByApplyLogisticsSampleId(long applyLogisticsSampleId);

    /**
     * 新增
     */
    void add(ApplyLogisticsSampleItemDto applyLogisticsSampleItem);

    /**
     * 批量新增
     */
    void addBatch(List<ApplyLogisticsSampleItemDto> sampleItems);
}
