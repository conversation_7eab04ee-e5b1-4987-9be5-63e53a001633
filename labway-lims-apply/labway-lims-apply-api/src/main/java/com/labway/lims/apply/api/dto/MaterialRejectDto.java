package com.labway.lims.apply.api.dto;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@Data
public class MaterialRejectDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申领单号
     */
    private String applyNo;

    /**
     * 物料编码
     */
    private List<String> materialCodes;

    public void verifyParams() {
        if (StringUtils.isBlank(applyNo)) {
            throw new IllegalArgumentException("申领单号不能为空");
        }
        if (CollectionUtils.isEmpty(materialCodes)) {
            throw new IllegalArgumentException("物料编码不能为空");
        }
    }
}
