package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询 盘点记录 Dto
 *
 * <AUTHOR>
 * @since 2023/5/12 10:52
 */
@Getter
@Setter
public class SelectMaterialInventoryCheckDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 盘点时间 开始
     */
    private Date beginCheckTime;

    /**
     * 盘点时间 结束
     */
    private Date endCheckTime;
    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 盘点单号
     */
    private String checkNo;
}
