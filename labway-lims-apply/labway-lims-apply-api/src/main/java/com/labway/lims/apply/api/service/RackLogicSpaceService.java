package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicEffectiveSpaceDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.SampleRackPositionDto;
import com.labway.lims.base.api.dto.RackHoleRuleDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface RackLogicSpaceService {
    /**
     * 根据逻辑ID查询空间
     */
    List<RackLogicSpaceDto> selectByRackLogicId(long rackLogicId);

    /**
     * 根据逻辑ID查询空间
     */
    List<RackLogicSpaceDto> selectByRackLogicIds(Collection<Long> rackLogicIds);

    /**
     * 根据试管架ID查询空间
     */
    List<RackLogicSpaceDto> selectByRackIds(Collection<Long> rackIds);

    /**
     * 根据试管架ID查询空间
     */
    Map<Long, List<RackLogicSpaceDto>> selectByRackIdsAsMap(Collection<Long> rackIds);

    /**
     * 根据逻辑ID查询空间数量
     */
    long countByRackLogicIds(Collection<Long> rackLogicIds);

    /**
     * 根据逻辑ID查询空间
     */
    long addRackLogicSpace(RackLogicSpaceDto dto);

    /**
     * 根据申请单样本查询与逻辑试管架的关联
     */
    List<RackLogicSpaceDto> selectByApplySampleId(long applySampleId);

    /**
     * 根据申请单样本查询与逻辑试管架的关联。已经删除的也会被查询出来
     */
    List<RackLogicSpaceDto> selectByAllApplySampleId(long applySampleId);

    /**
     * 根据申请单样本查询与逻辑试管架的关联
     */
    List<RackLogicSpaceDto> selectByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 神剧申请单样本ID删除
     */
    boolean deleteByApplySampleId(long applySampleId);

    /**
     * 根据 ID 删除
     */
    void deleteByRackLogicSpaceIds(Collection<Long> rackLogicSpaceIds);

    /**
     * 根据 ID 删除
     */
    void deleteByRackLogicSpaceId(long rackLogicSpaceId);

    /**
     * 根据逻辑试管架 ID 删除
     */
    void deleteByRackLogicId(long rackLogicId);

    /**
     * 根据 逻辑试管架-试管架孔位规则 获取下一个可用空间
     *
     * @param rackLogic   逻辑试管架
     * @param startRow    查找起始行位置
     * @param startColumn 查找起始列位置
     * @param holeRule    查找规则
     * @return 可用空间
     */
    @Nullable
    RackLogicEffectiveSpaceDto selectNextSpaceByRackLogicAndHoleRule(RackLogicDto rackLogic, int startRow,
                                                                     int startColumn, RackHoleRuleDto holeRule);

    /**
     * 根据 ID 查询
     */
    List<RackLogicSpaceDto> selectByRackLogicSpaceIds(Collection<Long> rackLogicSpaceIds);

    /**
     * 查询 逻辑试管架样本占用数量 Occupied
     */
    Map<Long, Integer> selectOccupiedNumByRackLogicIds(Collection<Long> rackLogicIds);

    /**
     * 根据试管架空间占用id
     */
    @Nullable
    RackLogicSpaceDto selectByRackLogicSpaceId(long rackLogicSpaceId);

    /**
     * 根据 样本id 查询样本所在的试管架位置
     *
     * @see RackLogicDto#getPosition() 试管架位置
     */
    List<SampleRackPositionDto> selectSamplePositionByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据申请单样本ID删除占用
     */
    void deleteByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据申请单样本ID删除占用并切逻辑试管架ID不是 rackLogicId 的
     */
    void deleteByApplySampleIdsAndNotRackLogicId(Collection<Long> applySampleIds, long rackLogicId);

    /**
     * 删除占用
     */
    void deleteByRackIds(Collection<Long> rackIds);
}
