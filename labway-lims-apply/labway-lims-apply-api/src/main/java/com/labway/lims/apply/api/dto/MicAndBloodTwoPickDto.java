package com.labway.lims.apply.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 微生物或者血培养二次分拣对象
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MicAndBloodTwoPickDto extends TwoPickDto {

    /**
     * 自定义分拣日期
     */
    private Date twoPickDate;

    /**
     * 血培养样本号
     */
    private List<LimbSampleDto> limbSampleNos;

}
