package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 异常 查看 参数
 * 
 * <AUTHOR>
 * @since 2023/4/12 11:32
 */
@Getter
@Setter
public class SelectSampleAbnormalDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 机构 ID
     */
    private Long orgId;
    /**
     * 专业组ID
     */
    private Long groupId;
    /**
     * 登记时间开始
     */
    private Date registDateStart;

    /**
     * 登记时间结束
     */
    private Date registDateEnd;
}
