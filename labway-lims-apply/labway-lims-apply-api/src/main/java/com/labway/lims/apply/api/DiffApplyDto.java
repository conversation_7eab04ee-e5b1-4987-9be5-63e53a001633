package com.labway.lims.apply.api;

import com.labway.lims.apply.api.dto.ApplyDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.*;

@Getter
@Setter
public class DiffApplyDto extends ApplyDto implements Diffable<ApplyDto> {
    @Override
    public DiffResult<ApplyDto> diff(ApplyDto old) {
        return new DiffBuilder<ApplyDto>(old, this, ToStringStyle.JSON_STYLE)
                .append("送检机构", old.getHspOrgName(), this.getHspOrgName())
                .append("患者姓名", old.getPatientName(), this.getPatientName())
                .append("年龄", old.getPatientAge(), this.getPatientAge())
                .append("年龄", old.getPatientSubage(), this.getPatientSubage())
                .append("年龄", old.getPatientSubageUnit(), this.getPatientSubageUnit())
                .append("性别", old.getPatientSex(), this.getPatientSex())
                .append("就诊类型", old.getApplyTypeName(), this.getApplyTypeName())
                .append("急诊状态", old.getUrgent(), this.getUrgent())
                .append("样本个数", old.getSampleCount(), this.getSampleCount())
                .append("申请时间", old.getApplyDate(), this.getApplyDate())
                .append("采样时间", old.getSamplingDate(), this.getSamplingDate())
                .append("门诊/住院号", old.getPatientVisitCard(), this.getPatientVisitCard())
                .append("送检医生", old.getSendDoctorName(), this.getSendDoctorName())
                .append("临床诊断", old.getDiagnosis(), this.getDiagnosis())
                .append("备注", old.getRemark(), this.getRemark())
                .append("电话", old.getPatientMobile(), this.getPatientMobile())
                .append("身份证号", old.getPatientCard(), this.getPatientCard())
                .append("床号", old.getPatientBed(), this.getPatientBed())
                .append("样本性状", old.getSamplePropertyCode(), this.getSamplePropertyCode())
                .append("出生日期", old.getPatientBirthday(), this.getPatientBirthday())
                .append("科室", old.getDept(), this.getDept())
                .build()
                ;
    }

    public void append(DiffResult<ApplyDto> diffResult, String fieldName, final Object oldVal, final Object newVal) {
        diffResult.getDiffs().add(new Diff<>(fieldName) {
            private static final long serialVersionUID = 1L;
            public Object getLeft() {
                return oldVal;
            }
            public Object getRight() {
                return newVal;
            }
        });
    }
}
