package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 逻辑试管架样本占用数量
 * 
 * <AUTHOR>
 * @since 2023/4/17 16:39
 */
@Getter
@Setter
public class SampleOccupiedNumByRackLogicId implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 逻辑试管架id
     */
    private Long rackLogicId;

    /**
     * 样本占用数量
     */
    private Integer occupiedNum;

}
