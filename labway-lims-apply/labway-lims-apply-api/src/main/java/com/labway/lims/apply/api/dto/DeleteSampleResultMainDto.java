package com.labway.lims.apply.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class DeleteSampleResultMainDto {

    /**
     * 删除样本结果主表id
     */
    private Long deleteSampleResultMainId;

    /**
     * 样本id
     */
    private Long sampleId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 项目类型
     */
    private String itemType;

    /**
     * 检验日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date testDate;

    /**
     * 删除日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date deleteDate;

    /**
     * 删除人
     */
    private Long deleteUserId;

    /**
     * 删除人
     */
    private String deleteUserName;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组
     */
    private String groupName;

    /**
     * 创建人
     */
    private Long createId;
    private String createName;
    private Date createDate;

    /**
     * 更新人
     */
    private Long updateId;
    private String updateName;
    private Date updateDate;

    /**
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer isDelete;
}
