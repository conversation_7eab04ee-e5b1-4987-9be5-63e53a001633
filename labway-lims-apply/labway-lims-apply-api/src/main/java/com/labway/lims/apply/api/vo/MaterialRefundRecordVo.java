package com.labway.lims.apply.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class MaterialRefundRecordVo implements Serializable {

    // 退库日期开始
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "退库查询开始时间不能为空！")
    private Date refundTimeBegin;

    // 退库日期结束
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "退库查询结束时间不能为空！")
    private Date refundTimeEnd;

    // 退库类型 1退库 2拒收
//    @NotNull(message = "退库查询类型不能为空！")
    private Integer refundType;

    // 退库单号
    private String refundNo;

    // 物料编码/物料名称
    private String materialKeyword;


}
