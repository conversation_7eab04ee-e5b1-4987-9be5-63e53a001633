package com.labway.lims.apply.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class MaterialIncomeRecordVo implements Serializable {

    /**
     * 入库日期 开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @NotNull(message = "入库查询开始时间不能为空！")
    private Date incomeDateBegin;

    /**
     * 入库日期 结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @NotNull(message = "入库查询结束时间不能为空！")
    private Date incomeDateEnd;

    /**
     * 入库单号
     */
    private String incomeNo;

    /**
     * 失效日期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date validDateBegin;

    /**
     * 失效日期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date validDateEnd;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 物料编码/名称
     */
    private String materialKeyword;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 入库状态 0未入库 1已入库
     */
    private Integer status;

}
