package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class SampleRackPositionDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 当前专业组id
     */
    private Long currentGroupId;

    /**
     * 当前专业组名称
     */
    private String currentGroupName;

    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 位置
     * @see RackLogicPositionEnum
     */
    private Integer position;

}
