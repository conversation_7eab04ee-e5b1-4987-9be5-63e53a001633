package com.labway.lims.apply.api.dto;

import cn.hutool.core.lang.Dict;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.api.field.Compare;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 报告单合并打印信息
 *
 * <AUTHOR>
 * @since 2023/6/19 9:27
 */
@Getter
@Setter
public class ReportMergePrintInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 就诊卡号 (门诊|住院号)
     */
    private String patientVisitCard;

    // todo 报告编号 先定义字段
    private String reportNo;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 性别
     *
     * @see com.labway.lims.api.enums.apply.SexEnum
     */
    private String patientSex;

    /**
     * 科室
     */
    private String dept;

    /**
     * 标本种类 -》样本类型
     */
    private String sampleTypeName;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 患者年龄
     */
    private String patientAge;

    /**
     * 床号
     */
    private String patientBed;
    /**
     * 临床诊断
     */
    private String diagnosis;

    /**
     * 检验单位--》机构名称
     */
    private String orgName;

    /**
     * 送检医生编码
     */
    private String sendDoctorCode;
    /**
     * 送检医生名称
     */
    private String sendDoctorName;
    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 样本条码
     */
    private String barcode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 样本备注
     */
    private String sampleRemark;

    /**
     * 结果备注
     */
    private String resultRemark;
    /**
     * 采样时间
     */
    private String samplingDate;
    /**
     * 创建时间|录入时间
     */
    private String createDate;
    /**
     * 终审时间
     */
    private String finalCheckDate;

    /**
     * 对应报告结果
     */

    private List<GroupResult> groupResultList;

    /**
     * 就诊类型 (申请类型)
     */

    private String applyTypeName;
    /**
     * 样本状态
     *
     * @see SampleStatusEnum
     */
    private Integer sampleStatus;

    /**
     * 检验人ID
     */
    private Long testerId;
    /**
     * 检验人姓名
     */
    private String testerName;

    /**
     * 终审人id
     */
    private Long finalCheckerId;

    /**
     * 终审人
     */
    private String finalCheckerName;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 签名
     */
    private Dict signature;

    /**
     * 样本图片
     */
    private List<Dict> sampleImages;

    /**
     * 报告单url
     */
    private List<String> reportUrls;

    /**
     * 专业组下 报告结果
     */
    @Getter
    @Setter
    public static class GroupResult implements Serializable {

        private static final long serialVersionUID = 1L;
        /**
         * 专业组名称
         */
        private String groupName;
        /**
         * 样本号
         */
        private String sampleNo;
        /**
         * 标本种类 -》样本类型
         */
        private String sampleTypeName;
        /**
         * 备注
         */
        private String remark;
        /**
         * 样本备注
         */
        private String sampleRemark;

        /**
         * 结果备注
         */
        private String resultRemark;
        /**
         * 来源仪器
         */
        private Long instrumentId;
        /**
         * 报告项目结果
         */
        private List<ReportItemResult> reportItemResulList;
        /**
         * 结果类型 用于没不是报告项目项
         */
        private String resultType;
        private BaseSampleEsModelDto result;

        /**
         * 特检使用模版类型
         */
        private String reportTemplateCode;
    }

    /**
     * 报告项目结果
     */
    @Getter
    @Setter
    public static class ReportItemResult implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 报告单项目编码
         */
        private String reportItemCode;
        /**
         * 报告项目名称
         */
        private String reportItemName;
        /**
         * 结果 （经过一系列的计算 转换最终得到的结果值）
         */
        private String result;
        /**
         * 结果范围
         */
        private String range;
        /**
         * 检验判定 UP DOWN NORMAL
         * 
         * @see TestJudgeEnum
         */
        private String judge;
        /**
         * 单位
         */
        private String unit;

        /**
         * 英文名称
         */
        private String enName;

        /**
         * 英文缩写
         */
        private String enAb;

        /**
         * 别名
         */
        private String aliasName;
        /**
         * 来源仪器
         */
        private Long instrumentId;
        /**
         * 检验方法名称
         */
        @Compare("检验方法")
        private String examMethodName;

        /**
         * 是否打印
         *
         * @see YesOrNoEnum
         */
        private Integer isPrint;

    }

}
