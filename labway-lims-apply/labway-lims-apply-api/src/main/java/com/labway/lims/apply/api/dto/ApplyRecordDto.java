package com.labway.lims.apply.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/6 10:57
 */
@Getter
@Setter
public class ApplyRecordDto implements Serializable {
    private static final long serialVersionUID=1L;
    /**
     * 日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date dateStart;

    /**
     * 日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date dateEnd;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 病人性别
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * patientCard
     */
    private String patientCard;

    /**
     * hspOrgCode
     */
    private Long hspOrgId;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 申请单ID
     */
    private Long applyId;

}

