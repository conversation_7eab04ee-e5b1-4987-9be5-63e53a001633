package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class PrefabricateTestApplyDto  extends TestApplyDto{

    /**
     * 检验项目
     */
    private List<TestApplyDto.Item> items;

    /**
     * 预制条码信息
     */
    private List<BarcodeInfo> barcodeInfos;

    /**
     * 外部条码号
     */
    private String outBarcode;


    @Override
    public List<TestApplyDto.Item> getItems() {
        return items;
    }


    @Getter
    @Setter
    public static class BarcodeInfo implements Serializable {

        /**
         * 条码号
         */
        private String barcode;

        /**
         * 管型
         */
        private String tubeCode;

        /**
         * 管型名称
         */
        private String tubeName;

    }
}
