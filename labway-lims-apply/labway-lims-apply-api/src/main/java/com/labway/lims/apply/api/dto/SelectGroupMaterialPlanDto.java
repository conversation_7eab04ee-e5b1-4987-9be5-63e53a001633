package com.labway.lims.apply.api.dto;

import com.labway.lims.apply.api.enums.GroupMaterialPlanEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SelectGroupMaterialPlanDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 计划开始日期
     */
    private Date startPlannerDate;

    /**
     * 计划结束日期
     */
    private Date endPlannerDate;

    /**
     * 状态,  1已保存， 2已提交， 3已审核 4已退回
     * {@link GroupMaterialPlanEnum}
     */
    private Integer status;

    /**
     * 专业组id
     */
    private Long groupId;


    /**
     * 审核时间 - 开始
     */
    private Date startAuditDate;
    /**
     * 审核时间 - 结束
     */
    private Date endAuditDate;

    /**
     *  物料编码/名称
     */
    private String materialCodeOrName;

}
