package com.labway.lims.apply.api.service;


import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.vo.MaterialIncomeRecordVo;
import com.labway.lims.apply.api.vo.MaterialRefundRecordVo;
import com.labway.lims.apply.api.vo.RefundMaterialMaterialVo;

import java.util.List;

/**
 * 物流退库记录表(TbMaterialRefundRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-04 15:44:35
 */
public interface MaterialRefundRecordService {


    /**
     * 物料入库列表查询
     * @param materialIncomeRecordVo
     * @return
     */
    MaterialIncomeStaticDto queryMaterialMaterialIncomeRecord(MaterialIncomeRecordVo materialIncomeRecordVo);

    /**
     * 查询退库列表
     * @param materialRefundRecordVo
     * @return
     */
    MaterialRefundStaticDto queryMaterialRefundRecord(MaterialRefundRecordVo materialRefundRecordVo);

    /**
     * 物料退库
     * @param refundMaterialMaterialVos
     * @return
     */
    RefundMaterialMaterialDto refundMaterialMaterial(List<RefundMaterialMaterialVo> refundMaterialMaterialVos);


    /**
     * 批量添加
     * @param refundRecordDtoList
     */
    void addBatch(List<MaterialRefundRecordDto> refundRecordDtoList);

    /**
     * 拒收
     * @param dto
     */
    void rejection(MaterialRecordIdDto dto);

    /**
     * 推送业务中台物料退库/拒收
     */
    String notifyBusinessMaterialRefund(NotifyBusinessMaterialRefundDto notifyBusinessMaterialRefundDto);

}

