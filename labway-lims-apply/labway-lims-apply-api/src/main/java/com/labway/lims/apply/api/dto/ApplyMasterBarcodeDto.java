package com.labway.lims.apply.api.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class ApplyMasterBarcodeDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构code
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * pda图片，  多个用英文逗号分割
     */
    private String pdaImgs;

    /**
     * 就诊类型
     */
    private String applyTypeName;

    /**
     * 是否忽略同人同天同项目校验 0否1是
     */
    private Integer ignoreSameItem;

    /**
     * 是否忽略检验项目限制性别校验 true:忽略 false:校验
     */
    private Boolean ignoreItemLimitSex;
}
