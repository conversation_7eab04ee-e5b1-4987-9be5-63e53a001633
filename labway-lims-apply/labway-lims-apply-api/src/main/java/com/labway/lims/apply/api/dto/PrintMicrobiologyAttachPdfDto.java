package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 打印微生物附件
 * 
 * <AUTHOR>
 * @since 2023/7/21 9:16
 */
@Getter
@Setter
public class PrintMicrobiologyAttachPdfDto implements Serializable {

    private static final long serialVersionUID = 1L;
    // ---------申请单信息-----------

    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 性别
     */
    private String patientSex;
    /**
     * 患者年龄
     */
    private String patientAge;
    /**
     * 就诊卡号 (门诊|住院号)
     */
    private String patientVisitCard;
    /**
     * 科室
     */
    private String dept;
    /**
     * 床号
     */
    private String patientBed;
    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 临床诊断
     */
    private String diagnosis;
    /**
     * 备注
     */
    private String remark;
    /**
     * 送检医生
     */
    private String sendDoctorName;
    // -----------申请单样本-------------

    /**
     * 样本条码
     */
    private String barcode;

    /**
     * 样本类型名称->标本种类
     */
    private String sampleTypeName;
    /**
     * 样本接收时间 yyyy-mm
     */
    private String receiveTime;

    // ------微生物样本---------------
    /**
     * 样本号
     */
    private String sampleNo;

    // ------------样本检验项目--------
    /**
     * 检验项目
     */
    private String testItemName;
    /**
     * 检验项目编码
     */
    private String testItemCode;
}
