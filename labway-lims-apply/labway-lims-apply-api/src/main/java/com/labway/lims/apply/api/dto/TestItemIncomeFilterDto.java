package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 销售项目收入查询 数据过滤字段
 * 
 * <AUTHOR>
 * @since 2023/6/19 21:13
 */
@Setter
@Getter
public class TestItemIncomeFilterDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 就诊类型
     */
    private String applyType;
    /**
     * 就诊类型
     */
    private List<String> applyTypes;
    /**
     * 检验项目ID
     */
    private Set<Long> testItemIds;

    /**
     * 客户名称 类别:1开票名称，2单位名称
     *
     * @see CustomerNameTypeEnum
     */
    private Integer customerNameType;

    /**
     * 是否免单 1:免单 0:非免单
     *
     * @see YesOrNoEnum
     */
    private Integer isFree;

    /**
     * 项目类型 常规检验、微生物检验、院感检验 区分做什么检验的
     * <p>
     * 不传为所有 , "MICROBIOLOGY"为微生物
     */
    private String itemTypeCode;

    /**
     * 财务预算统计导出
     */
    private Boolean financialBudgetExport;

    /**
     * 客户名称 类别
     */
    public static CustomerNameTypeEnum getCustomerNameType(Integer customerNameType) {
        if (Objects.isNull(customerNameType)) {
            // 默认开票名称
            return CustomerNameTypeEnum.INVOICE_NAME;
        }
        CustomerNameTypeEnum customerNameTypeEnum = null;
        if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME.getCode())) {
            customerNameTypeEnum = CustomerNameTypeEnum.INVOICE_NAME;
        } else if (Objects.equals(customerNameType, CustomerNameTypeEnum.ORG_NAME.getCode())) {
            customerNameTypeEnum = CustomerNameTypeEnum.ORG_NAME;
        }
        return customerNameTypeEnum;
    }

    /**
     * 免单标记 获取
     */
    public static YesOrNoEnum getIsFreeFlag(Integer isFree) {
        if (Objects.isNull(isFree)) {
            // 默认 非免单
            return YesOrNoEnum.NO;
        }
        YesOrNoEnum isFreeFlag = null;
        if (Objects.equals(isFree, YesOrNoEnum.YES.getCode())) {
            isFreeFlag = YesOrNoEnum.YES;
        } else if (Objects.equals(isFree, YesOrNoEnum.NO.getCode())) {
            isFreeFlag = YesOrNoEnum.NO;
        }
        return isFreeFlag;
    }

}
