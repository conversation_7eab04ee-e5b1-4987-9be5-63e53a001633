package com.labway.lims.apply.api.dto.es;

import com.labway.lims.pdfreport.api.dto.WordContentDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 微生物
 */
@Getter
@Setter
public class MicrobiologyInspectionDto extends BaseSampleEsModelDto {
    /**
     * 结果类型
     *
     * @see com.labway.lims.microbiology.api.enums.MicrobiologyResultEnum
     */
    private Integer resultType;

    /**
     * 二审人id
     */
    private Long twoCheckerId;
    /**
     * 二审人
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    private Date twoCheckDate;
    /**
     * 微生物结果
     */
    private List<MicrobiologyResultDto> results;

    /**
     * 微生物细菌
     */
    private List<MicrobiologyGermDto> germs;

    /**
     * word结构
     */
    private WordContentDto wordContent;

    /**
     * 一次审核id
     */
    private Long oneCheckerId;

    /**
     * 一次审核人
     */
    private String oneCheckerName;

    /**
     * 一次审核时间
     */
    private Date oneCheckerDate;



}
