package com.labway.lims.apply.api.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class UploadImagesDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 申请单id
     */
    @NotNull(message = "申请单is不能为空")
    private Long applyId;

    /**
     * 图片地址
     */
    @NotEmpty(message = "图片地址不能为空")
    private List<String> imgUrls;

}
