package com.labway.lims.apply.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PdaEntryTestApplyDto extends TestApplyDto{

    /**
     * pda样本录入图片
     */
    private String pdaImgs;

    /**
     * 检验项目
     */
    private List<TestApplyDto.Item> items;

    @Override
    public List<TestApplyDto.Item> getItems() {
        return items;
    }
}
