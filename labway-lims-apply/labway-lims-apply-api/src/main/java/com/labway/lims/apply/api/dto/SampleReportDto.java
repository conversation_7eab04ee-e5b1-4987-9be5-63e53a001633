package com.labway.lims.apply.api.dto;

import cn.hutool.extra.spring.SpringUtil;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 样本报告
 *
 * <AUTHOR>
 * @since 2023/4/23 16:51
 */
@Getter
@Setter
public class SampleReportDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long sampleReportId;

    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 泛指，微生物样本、特检等样本id
     */
    private Long sampleId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 默认PDF
     *
     * @see SampleReportFileTypeEnum
     */
    private String fileType;

    /**
     * 地址
     */
    private String url;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 1: 已经删除 0:未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 1: 是文件上传的pdf，  0默认
     */
    private Integer isUploadPdf;


    // 手动上传pdf的key
    public static String getIsUploadPdfKey(long sampleId) {
        return SpringUtil.getBean(RedisPrefix.class).getBasePrefix() + SampleReportDto.class.getSimpleName() + ":SAMPLE_UPLOAD_PDF:" + sampleId;
    }
}
