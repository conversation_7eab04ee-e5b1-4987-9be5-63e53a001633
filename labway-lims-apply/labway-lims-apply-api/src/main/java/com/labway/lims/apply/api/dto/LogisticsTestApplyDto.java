package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 物流申请单补录
 */
@Getter
@Setter
public class LogisticsTestApplyDto extends TestApplyDto {
    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 物流样本
     */
    private Sample sample;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 物流样本
     */
    private ApplyLogisticsSampleDto logisticsSample;

    /**
     * 物流申请单
     */
    private ApplyLogisticsDto applyLogistics;


    @Override
    public List<TestApplyDto.Item> getItems() {
        return sample.getItems();
    }

    @Getter
    @Setter
    public static final class Sample {
        /**
         * 条码
         */
        private String barcode;
        /**
         * 是否双输复核
         */
        private Integer enableDoubleCheck;
        /**
         * 检验项目
         */
        private List<TestApplyDto.Item> items;
    }

}
