package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

/**
 * <pre>
 * OutsourcingTestItemStatisticsDto
 * 外送项目统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/2/18 14:11
 */
@Getter
@Setter
public class OutsourcingTestItemStatisticsDto implements Serializable {

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 数量合计
     */
    private Integer countSum;

    /**
     * 汇总行
     */
    private List<TestItemStatisticsItem> itemList = new LinkedList<>();

    @Getter
    @Setter
    public static class TestItemStatisticsItem implements Serializable {
        /**
         * 外送机构名称
         */
        private Long exportOrgId;
        private String exportOrgName;

        /**
         * 检验项目编码
         */
        private String testItemCode;
        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 样本类型ID
         */
        private String sampleTypeCode;

        /**
         * 样本类型名称
         */
        private String sampleTypeName;

        /**
         * 管型 code
         */
        private String tubeCode;

        /**
         * 管型 name
         */
        private String tubeName;
        /**
         * 检验方法ID
         */
        private String examMethodCode;

        /**
         * 检验方法名称
         */
        private String examMethodName;

        /**
         * 单价
         */
        private BigDecimal price;

        /**
         * 数量
         */
        private Integer count;

    }
}
