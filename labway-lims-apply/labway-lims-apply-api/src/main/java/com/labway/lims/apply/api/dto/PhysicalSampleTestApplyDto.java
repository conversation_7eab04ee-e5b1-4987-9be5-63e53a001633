package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 体检样本 签收
 * 
 * <AUTHOR>
 * @since 2023/4/6 14:06
 */
@Getter
@Setter
public class PhysicalSampleTestApplyDto extends TestApplyDto {

    /**
     * 样本类型
     */
    private String sampleType;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 管型
     */
    private String tube;

    /**
     * 体检样本 检验项目信息
     */
    private List<PhysicalSampleItemDto> sampleItemDtoList;


    @Override
    public List<Item> getItems() {
        return sampleItemDtoList.stream().map(obj -> {
            Item testApplyItemDto = new Item();
            testApplyItemDto.setTestItemId(obj.getTestItemId());
            return testApplyItemDto;
        }).collect(Collectors.toList());
    }


}
