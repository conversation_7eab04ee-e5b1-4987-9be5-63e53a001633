package com.labway.lims.apply.api.dto.es;

import cn.hutool.core.lang.ObjectId;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @Description 用于比较是否同人样本的dto
 * @Date 2024/11/28 18:54
 */
@Getter
@Setter
public class CombineOnePersonDto extends BaseSampleEsModelDto {

	/**
	 * 用于分组的id
	 * {@link ObjectId#next()} 固定长度为24
	 */
	private String groupRowId;

	public  static <T extends CombineOnePersonDto> Collection<List<T>> groupCombineOnePerson(List<BaseSampleEsModelDto> baseSamples, Class<T> t) {
		List<T> samples = baseSamples.stream()
				.map(e -> {
					T combineOnePersonDto = null;
					try {
						combineOnePersonDto = t.getDeclaredConstructor().newInstance();
					} catch (InstantiationException ex) {
						throw new RuntimeException(ex);
					} catch (IllegalAccessException ex) {
						throw new RuntimeException(ex);
					} catch (InvocationTargetException ex) {
						throw new RuntimeException(ex);
					} catch (NoSuchMethodException ex) {
						throw new RuntimeException(ex);
					}
					BeanUtils.copyProperties(e, combineOnePersonDto);
					combineOnePersonDto.setGroupRowId(ObjectId.next());
					return combineOnePersonDto;
				})
				.sorted(Comparator.comparing(CombineOnePersonDto::getCreateDate).reversed())
				.collect(Collectors.toList());


		// 根据同人 样本状态分组
		for (int i = 0; i < samples.size() - 1; i++) {
			CombineOnePersonDto sourceSample = samples.get(i);

			for (int j = i + 1; j < samples.size(); j++) {
				CombineOnePersonDto nextSample = samples.get(j);
				// 相同的送检机构
				if (!nextSample.getHspOrgCode().equals(sourceSample.getHspOrgCode())) {
					continue;
				}
				/*if ((StringUtils.isNotBlank(nextSample.getPatientCard()) && StringUtils.isNotBlank(sourceSample.getPatientCard()))) {
					if (Objects.equals(nextSample.getPatientCard(),sourceSample.getPatientCard())) {
						nextSample.setGroupRowId(sourceSample.getGroupRowId());
						continue;
					} else {
						continue;
					}
				}
				if (StringUtils.isNotBlank(nextSample.getPatientCard()) && StringUtils.isBlank(sourceSample.getPatientCard())) {
					continue;
				}
				if (StringUtils.isBlank(nextSample.getPatientCard()) && StringUtils.isNotBlank(sourceSample.getPatientCard())) {
					continue;
				}*/
				if (Objects.equals(nextSample.getPatientName(), sourceSample.getPatientName())
						&& Objects.equals(nextSample.getPatientSex(), sourceSample.getPatientSex())) {
					int agex = sourceSample.getPatientAge() - nextSample.getPatientAge();
					agex = Math.abs(agex);
					if (agex <= 3 && agex >= 0) {
						nextSample.setGroupRowId(sourceSample.getGroupRowId());
					}
				}
			}
		}
		return samples.stream().collect(Collectors.groupingBy(CombineOnePersonDto::getGroupRowId)).values();
	}
}
