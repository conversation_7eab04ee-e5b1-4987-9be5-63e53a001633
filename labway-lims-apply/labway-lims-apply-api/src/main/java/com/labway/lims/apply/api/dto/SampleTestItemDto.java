package com.labway.lims.apply.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 样本 最细颗粒度数据
 *
 * <AUTHOR>
 * @since 2023/6/21 11:50
 */
@Getter
@Setter
public class SampleTestItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外部条码号
     */
    private String outBarcode;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者性别
     * 
     * @see com.labway.lims.api.enums.apply.SexEnum
     */
    private Integer patientSex;
    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 患者子年龄
     */
    private Integer patientSubage;

    /**
     * 患者子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;
    /**
     * 外送机构
     */
    private Long exportHspOrgId;
    /**
     * 外送机构
     */
    private String exportHspOrgName;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 送检机构
     */
    private Long hspOrgId;
    /**
     * 科室
     */

    private String dept;

    /**
     * 送检医生名称
     */
    private String sendDoctorName;
    /**
     * 申请单id
     */
    private Long applyId;
    /**
     * 申请时间
     */
    private Date applyDate;
    /**
     * 签收日期
     */
    private Date signDate;
    /**
     * 终审时间
     */
    private Date finalCheckDate;
    /**
     * 就诊卡号 (门诊|住院号)
     */

    private String patientVisitCard;
    /**
     * 申请单样本id
     */
    private Long applySampleId;
    /**
     * 项目类型 常规检验、微生物检验、院感检验 区分做什么检验的
     *
     * @see ItemTypeEnum
     */
    private String itemType;
    /**
     * 就诊类型 (申请类型)
     */
    private String applyTypeCode;
    private String applyTypeName;
    /**
     * 样本检验项目id
     */
    private Long applySampleItemId;
    /**
     * 检验项目ID
     */
    private Long testItemId;
    /**
     * 检验项目编码
     */
    private String testItemCode;
    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 套餐编码
     */
    private String combinePackageCode;

    /**
     * 套餐名称
     */
    private String combinePackageName;

    /**
     * 专业组id
     */
    private Long groupId;
    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 收费数量
     */
    private Integer count;
    /**
     * 实际收费价格
     */
    private BigDecimal actualFeePrice;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 是否免单
     *
     * @see YesOrNoEnum
     */
    private Integer isFree;
    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 折扣率
     */
    private BigDecimal discount;

    /**
     * 财务专业组
     */
    private String financeGroupCode;
    private String financeGroupName;
    /**
     * 终止检验状态：0正常，1终止收费，2终止不收费
     *
     * @see StopTestStatus
     */
    private Integer stopStatus;

    /**
     * 是否 使用了客户特价项目
     */
    private boolean specialOfferFlag;

    /**
     * 是否 是阶梯
     */
    private boolean pricingFlag;

    /**
     * 是否 走套餐了
     */
    private boolean combinePackageFlag;

    /**
     * 身份证
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String patientCard;

    /**
     * 客户折扣 基准包 id
     */
    @Nullable
    private Long packageId;

    /**
     * 当前是否是套餐数据，  而不是正常的样本检验项目数据
     */
    private boolean theCombinePackage;

    /**
     * 若使用了客户特价项目 折后价格记录
     */
    private BigDecimal discountPrice;

    /**
     * 获取可确定唯一一条 的key
     */
    public String uniqueKey() {
        return this.hspOrgId + "-" + this.applySampleId + "-" + this.applySampleItemId;
    }


    // 一下补充统计样本所需字段
    //
    private String barcode;
//    //检验项目名称
//    private String testItemName;
    //外部项目编码
    private String outTestItemCode;
    //外部项目名称
    private String outTestItemName;
    // 送检机构
    private String hspOrgCode;
    //病人生日
    private Date patientBirthday;
    //床号
    private String patientBed;
//    //样本创建时间（三方系统的创建时间）
//    private Date createDate;
//    //送检时间
//    private Date applyDate;
    //送检医生
    private String sendDoctorCode;
    // 检验时间
    private Date testDate;
    // 检验人员
    private Long testerId;
    // 检验人员
    private String testerName;
    // 终审人员
    private Long finalCheckerId;
    // 终审人员
    private String finalCheckerName;
    // 是否是分血样本 0否1是
    private Integer isSplitBlood;
//    // 样本id
//    private Long applySampleId;
    // 终审时间
    private Date finalCheckDateTime;
    // 送检时间 yyyy-MM-dd
    private Date sendDate;

    /**
     * 申请单样本项目是否禁用：1：禁用，0：正常
     */
    private Integer isDisabled;

}
