package com.labway.lims.apply.api.dto.es;

import com.labway.lims.api.enums.microbiology.MicroFormulaEnum;
import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 微生物药物
 */
@Getter
@Setter
public class MicrobiologyMedicineDto implements Serializable {
    /**
     * 药物id
     */
    private Long medicineId;
    /**
     * 药物编码
     */
    private String medicineCode;
    /**
     * 药物名称
     */
    private String medicineName;
    /**
     * 药物方法
     */
    private String medicineMethod;
    /**
     * 敏感度
     *
     * @see MicroSusceptibilityEnum
     */
    private String susceptibility;
    /**
     * 单位
     */
    private String unit;
    /**
     * 参考值
     */
    private String range;

    /**
     * 药物结果前缀
     *
     * @see MicroFormulaEnum
     */
    private String formula;

    /**
     * 结果
     */
    private String result;

    /**
     * 分组
     */
    private String group;

    /**
     * 折点范围
     */
    private String foldPointScope;
}
