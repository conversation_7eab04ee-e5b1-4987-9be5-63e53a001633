package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物料入库 信息
 *
 * <AUTHOR>
 * @since 2023/5/9 9:50
 */
@Getter
@Setter
public class MaterialDeliveryIncomeItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 详细ID-出库详情id
     */
    private Long detailId;
    /**
     * 入库主单位数量
     */
    private BigDecimal incomeMainNumber;
    /**
     * 入库辅单位数量
     */
    private BigDecimal incomeAssistNumber;

    /**
     * 存放是否合格 1是 0否   默认1
     */
    private Integer ifStorageQualified;

    /**
     * 规格数量是否一致  1是 0否   默认1
     */
    private Integer ifSpecQuantityConsistent;

    /**
     * 包装有无破损 1有0无   默认0
     */
    private Integer ifPackageDamaged;

    /**
     * 效期是否合格  1是 0否   默认1
     */
    private Integer ifValidDateQualified;

    /**
     * 验收结论  1合格0不合格  默认1
     */
    private Integer acceptanceConclusion;

}
