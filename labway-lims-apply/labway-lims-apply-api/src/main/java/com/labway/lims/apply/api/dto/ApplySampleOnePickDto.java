package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 一次分拣
 */
@Getter
@Setter
public class ApplySampleOnePickDto implements Serializable {
    /**
     * x
     */
    private int row;

    /**
     * y
     */
    private int column;

    /**
     * 试管架ID
     */
    private long rackId;

    /**
     * 逻辑试管架ID
     */
    private long rackLogicId;

    /**
     * 申请单样本ID
     */
    private long applySampleId;


    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 逻辑试管架剩余的空间 0 表示没有了
     */
    private int spaces;

}
