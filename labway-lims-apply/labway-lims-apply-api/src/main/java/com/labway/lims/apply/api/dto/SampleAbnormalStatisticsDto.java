package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 送检机构异常样本统计
 */
@Getter
@Setter
public class SampleAbnormalStatisticsDto extends SampleAbnormalDto {

    /**
     * 样本性状code
     */
    private String samplePropertyCode;

    /**
     * 样本性状name
     */
    private String sampleProperty;

    /**
     * 就诊类型
     */
    private String applyTypeCode;

    /**
     * 就诊类型
     */
    private String applyTypeName;

    /**
     * 样本类型
     */
    private String sampleTypeCode;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组
     */
    private String groupName;

    /**
     * 送检时间
     */
    private Date sendDate;

    /**
     * 门诊住院号
     */
    private String patientVisitCard;

    /**
     * 床号
     */
    private String patientBed;

    /**
     * 科室
     */
    private String dept;

}
