package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.BusinessCenterDeliveryDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryRecordDetailDto;
import com.labway.lims.apply.api.dto.MaterialDeliveryRecordDto;
import com.labway.lims.apply.api.dto.SelectMaterialDeliveryRecordDto;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 物料出库记录 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
public interface MaterialDeliveryRecordService {

    /**
     * 处理申领单--业务中台出库
     */
    void businessCenterDelivery(@Nonnull BusinessCenterDeliveryDto dto);

    /**
     * 添加 物料出库单
     * <p>
     * 注意: 此方法被 {@link #businessCenterDelivery} 调用 不用去获取登录人，会获取不到
     */
    long addMaterialDeliveryRecord(MaterialDeliveryRecordDto dto);

    /**
     * 根据 出库时间 查看
     */
    List<MaterialDeliveryRecordDto> selectBySelectMaterialDeliveryRecordDto(SelectMaterialDeliveryRecordDto dto);

    /**
     * 根据出库单号查询 出库信息
     */
    @Nullable
    MaterialDeliveryRecordDto selectByDeliveryNo(String deliveryNo, long orgId);

    /**
     * 根据出库单号查询 出库信息
     */
    List<MaterialDeliveryRecordDto> selectByDeliveryNos(Collection<String> deliveryNos, long orgId);

    /**
     * 根据记录 id 获取出库记录
     */
    @Nullable
    MaterialDeliveryRecordDto selectByRecordId(long recordId);

    /**
     * 修改 出库记录
     */
    boolean updateByRecordId(MaterialDeliveryRecordDto dto);

    /**
     * 根据申领单id查询出库记录
     */
    List<MaterialDeliveryRecordDto> selectByApplyId(long applyId);

    List<MaterialDeliveryRecordDetailDto> outBoundOrders(Long applyId);

    List<MaterialDeliveryRecordDetailDto> outBoundOrders(Long applyId, long orgId);

    /**
     * 删除出库记录
     * @param deliveryNo
     */
    void deleteByDeliveryNo(String deliveryNo);
}
