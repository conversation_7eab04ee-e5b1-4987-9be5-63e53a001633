package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 待二次分拣的申请单样本
 */
@Getter
@Setter
public class WaitingTwoPickApplySampleDto extends ApplySampleDto {
    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 病人名称
     */
    private String patientName;


    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;


    /**
     * 性别  性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 逻辑试管架ID
     */
    private Long rackLogicId;

    /**
     * 逻辑试管架更新日期
     */
    private Date rackLogicUpdateDate;
}
