package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.OnePickRackLogicInfoDto;
import com.labway.lims.apply.api.dto.RackLogicDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface RackLogicService {

    /**
     * 在一次分拣时，获取到当前登录用户可用的试管架
     */
    @Nullable
    RackLogicDto selectAvailableRackLogic(long groupId, long userId);

    /**
     * 添加逻辑试管架
     */
    long addRackLogic(RackLogicDto dto);

    /**
     * 批量添加逻辑试管架
     * @return 添加数量
     */
    long addRackLogics(Collection<RackLogicDto> dtos);

    /**
     * 根据id查询
     */
    @Nullable
    RackLogicDto selectByRackLogicId(long rackLogicId);

    /**
     * 根据 物理试管架id 查询
     */
    List<RackLogicDto> selectByRackId(long rackId);

    /**
     * 根据 物理试管架ids 查询
     */
    List<RackLogicDto> selectByRackIds(Collection<Long> rackIds);

    /**
     * 根据id查询
     */
    List<RackLogicDto> selectByRackLogicIds(Collection<Long> rackLogicIds);

    /**
     * 根据id修改
     */
    boolean updateByRackLogicId(RackLogicDto dto);

    /**
     * 根据 group id 查询
     */
    List<RackLogicDto> selectByNextGroupId(long nextGroupId);

    /**
     * 根据 group id 查询
     */
    List<RackLogicDto> selectByCurrentGroupId(long currentGroupId);

    /**
     * 根据 group id 查询 和 position 查询
     */
    List<RackLogicDto> selectByCurrentGroupIdAndPosition(long currentGroupId, int position);

    /**
     * 根据 group id 和 position 查询
     *
     * @param position {@link RackLogicDto#getPosition()}
     */
    List<RackLogicDto> selectByNextGroupIdAndPosition(long nextGroupId, int position);

    /**
     * 根据 rackCode 和 group id 和 position 查询
     *
     * @param position {@link RackLogicDto#getPosition()}
     */
    List<RackLogicDto> selectByRackCodeAndNextGroupIdAndPosition(String rackCode, long nextGroupId, int position);

    /**
     * 根据 rackCode 和 position 查询
     */
    List<RackLogicDto> selectByRackCodeIdAndPosition(String rackCode, int position);

    /**
     * 根据创建人查询
     *
     * @param position {@link RackLogicDto#getPosition()}
     */
    List<RackLogicDto> selectByCreatorIdAndPosition(long userId, int position);

    /**
     * 根据条码环节查询
     *
     * @param positions {@link RackLogicDto#getPosition()}
     */
    List<RackLogicDto> selectByPositions(List<Integer> positions);

    /**
     * 获取一次分拣待交接的逻辑试管架信息
     */
    List<OnePickRackLogicInfoDto> selectOnePickWaitingHandoverRackLogics(Date beginOnePickDate, Date endOnePickDate,
        long groupId);

    /**
     * 根据更新日期查询
     */
    List<RackLogicDto> selectByUpdateDateAndNextGroupId(Date beginUpdateDate, Date endUpdateDate, long nextGroupId);

    /**
     * 根据更新日期查询
     */
    List<RackLogicDto> selectByUpdateDateAndCurrentGroupId(Date beginUpdateDate, Date endUpdateDate,
        long currentGroupId);

    /**
     * 根据更新日期查询
     */
    List<RackLogicDto> selectByUpdateDateAndCurrentGroupIdAndPosition(Date beginUpdateDate, Date endUpdateDate,
        long currentGroupId, int position);

    /**
     * 根据申请单样本获取逻辑试管架
     */
    List<RackLogicDto> selectByApplySampleId(long applySampleId);

    /**
     * 根据ID删除逻辑试管架
     */
    boolean deleteByRackLogicId(long rackLogicId);

    /**
     * 根据物理试管架 删除逻辑试管架
     */
    void deleteByRackIds(Collection<Long> rackIds);

    /**
     * 根据ID删除逻辑试管架
     */
    void deleteByRackLogicIds(Collection<Long> rackLogicIds);

}
