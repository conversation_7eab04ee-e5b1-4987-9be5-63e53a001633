package com.labway.lims.apply.api.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class NotifyBusinessMaterialRefundDto implements Serializable {

    //lims编码
    private String limsOrgId;
    //退库申请单表
    private String refundApplyNo;
    //退库申请人
    private String refundApplyUserId;
    //退库申请人
    private String refundApplyUser;
    //退库申请时间
    private LocalDateTime refundApplyTime;
    //退库原因
    private String refundReason;
    //关联的出库单号
    private String supplyNo;
    //出库时间
    private LocalDateTime supplyTime;
    //原始申领单号
    private String applyNo;
    // 物料信息
    private List<MaterialRefundDetailDto> detailRequests;

    // 退库单号/拒收单号(无值按入参推送，有值按退库单号推送)
    private String refundNo;


    public NotifyBusinessMaterialRefundDto(String refundNo) {
        this.refundNo = refundNo;
    }

    @Data
    public static class MaterialRefundDetailDto implements Serializable {
        // 行号
        private String rowNo;
        //物料编码
        private String materialCode;
//        //物料名称
//        private String materialName;
//        //物料规格
//        private String materialSpecification;
//        //物料厂家
//        private String materialManufacturer;
        // 退库主数量
        private BigDecimal refundNumberDecimal;
        //批次号
        private String batchNo;
        //本次退库辅数量
        private BigDecimal assistNumber;
//        //主单位
//        private String mainUnit;
//        //主单位编码
//        private String mainUnitCode;
//        //辅单位
//        private String assistUnit;
//        //辅单位编码
//        private String assistUnitCode;
        //失效日期
        private String expirationDate;
//        //主辅换算率
//        private String mainmeasrate;
    }


}
