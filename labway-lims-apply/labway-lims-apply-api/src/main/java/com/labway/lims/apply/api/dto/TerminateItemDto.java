package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.apply.StopTestStatus;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * 终止检验 检验项目维度
 *
 * <AUTHOR>
 * @since 2023/8/10 14:20
 */
@Getter
@Setter
public class TerminateItemDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 样本检验项目id
     */
    private Set<Long> applySampleItemIds;
    /**
     * 原因code
     */
    private String causeCode;
    /**
     * 原因
     */
    private String cause;

    /**
     * 是否为并单样本
     */
    private Boolean isCombinedBill = false;

    /**
     * 终止检验类型
     */
    private StopTestStatus terminateType;
}
