package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物料领用 登记 信息
 *
 * <AUTHOR>
 * @since 2023/5/9 17:48
 */
@Getter
@Setter
public class MaterialReceiveRegisterItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 库存ID
     */
    private Long inventoryId;
    /**
     * 领用主单位数量
     */
    private BigDecimal receiveMainNumber;
    /**
     * 领用辅单位数量
     */
    private BigDecimal receiveAssistNumber;

    /**
     *  物料条码号
     */
    private String materialBarcode;

}
