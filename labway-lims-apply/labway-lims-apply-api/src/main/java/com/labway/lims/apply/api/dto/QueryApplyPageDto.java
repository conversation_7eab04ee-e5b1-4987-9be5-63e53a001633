package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 查询分页 参数
 * 
 * <AUTHOR>
 * @since 2023/5/11 16:26
 */
@Getter
@Setter
public class QueryApplyPageDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 录入开始时间
     */
    private Date createDateStart;

    /**
     * 录入结束时间
     */
    private Date createDateEnd;

    /**
     * 检验机构
     */
    private Long orgId;
    /**
     * 主条码
     */
    private List<String> masterBarcodes;
    /**
     * current
     */
    private Long current;

    /**
     * size
     */
    private Long size;

}
