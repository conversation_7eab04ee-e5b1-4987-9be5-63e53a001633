package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 分平台统计 信息 Dto
 *
 * <AUTHOR>
 * @since 2023/7/12 13:54
 */
@Setter
@Getter
public class ByPlatformStatisticsResponseDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机构编码
     */
    private Long hspOrgId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 数量合计
     */
    private Integer countSum;
    /**
     * 折前总额合计
     */
    private BigDecimal payAmountBeforeSum;
    /**
     * 折后总额合计
     */
    private BigDecimal payAmountAfterSum;
    /**
     * 汇总行
     */
    private List<ByPlatformStatisticsItemDto> itemList;
    /**
     * 机构汇总
     */
    private List<ByPlatformStatisticsResponseDto> orgList;
    /**
     * 明细行
     */
    private List<ByPlatformStatisticsItemDto> outOrgStatisticsItems;

}
