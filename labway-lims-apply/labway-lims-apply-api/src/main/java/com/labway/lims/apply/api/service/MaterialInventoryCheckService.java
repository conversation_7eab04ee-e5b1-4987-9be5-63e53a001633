package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.MaterialInventoryCheckDetailDto;
import com.labway.lims.apply.api.dto.MaterialInventoryCheckDto;
import com.labway.lims.apply.api.dto.SelectMaterialInventoryCheckDto;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <p>
 * 物料盘点 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
public interface MaterialInventoryCheckService {

    /**
     * 根据盘点id 查询 盘点记录
     */
    @Nullable
    MaterialInventoryCheckDto selectByCheckId(long checkId);

    /**
     * 根据 盘点时间 查看
     */
    List<MaterialInventoryCheckDto> selectBySelectMaterialInventoryCheckDto(SelectMaterialInventoryCheckDto dto);

    /**
     * 开始盘点
     */
    void startCheck();

    /**
     * 取消盘点
     */
    void cancelCheck(long checkId);

    /**
     * 结束盘点
     */
    void finishCheck(long checkId, List<MaterialInventoryCheckDetailDto> updateCheckDetailList);

    /**
     * 专业组是否处于库存盘点中 若存在 返回盘点时间,若不存在返回 null
     */
    @Nullable
    String isCheck(long groupId);

    /**
     * 移除 专业组是否处于库存盘点中 缓存
     */
    void removeIsCheckCache(long groupId);

    /**
     * 添加 盘点记录
     */
    long addMaterialInventoryCheck(MaterialInventoryCheckDto dto);

    /**
     * 修改 盘点记录
     */
    void updateByCheckId(MaterialInventoryCheckDto dto);

}
