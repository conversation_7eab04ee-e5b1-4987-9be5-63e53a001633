package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class LogisticsApplyDto extends ApplyDto {

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 样本id
     */
    private Long applySampleId;
    /**
     * 物流员
     */
    private String logisticsUserName;

    /**
     * 物流申请单号
     */
    private String logisticsApplyNo;

    /**
     * 取样时间
     */
    private Date receiveDate;

    /**
     * 物流样本id
     */
    private Long applyLogisticsSampleId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 申请单图片
     */
    private String applyImage;
}
