package com.labway.lims.apply.api.dto.es;

import com.labway.lims.specialty.api.dto.SpecialtySampleResultDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 特检
 */
@Getter
@Setter
public class SpecialtyInspectionDto extends BaseSampleEsModelDto {

    /**
     * 一次审核
     */
    private Long oneCheckerId;
    /**
     * 一次审核人名称
     */
    private String oneCheckerName;

    /**
     * 一次审核时间
     */
    private Date oneCheckDate;
    /**
     * 二次审核人
     */
    private Long twoCheckerId;
    /**
     * 二次审核人名称
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    private Date twoCheckDate;

    /**
     * 特检结果
     */
    private List<SpecialtySampleResultDto> results;

}
