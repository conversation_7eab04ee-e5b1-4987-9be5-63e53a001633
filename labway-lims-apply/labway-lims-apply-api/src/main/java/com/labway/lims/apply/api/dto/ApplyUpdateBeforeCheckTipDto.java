package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <pre>
 * ApplyUpdateBeforeCheckTipDto
 * 样本信息修改提示
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/1/18 11:39
 */
@Getter
@Setter
public class ApplyUpdateBeforeCheckTipDto implements Serializable {

    // 触发了危急值的报告
    private List<ApplyUpdateBeforeCheckTipItemDto> criticals;

    // 触发了异常的报告
    private List<ApplyUpdateBeforeCheckTipItemDto> exceptions;

}
