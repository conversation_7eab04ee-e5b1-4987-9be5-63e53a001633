package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 条码环节
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@Builder
public class SampleFlowDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long sampleFlowId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 操作类型
     *
     * @see BarcodeFlowEnum
     */
    private String operateCode;

    /**
     * 操作类型
     */
    private String operateName;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 检验人ID
     */
    private Long orgId;

    /**
     * 检验人
     */
    private String orgName;

    /**
     * 1: 已经删除 0 ：未删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    @Tolerate
    public SampleFlowDto() {

    }

}
