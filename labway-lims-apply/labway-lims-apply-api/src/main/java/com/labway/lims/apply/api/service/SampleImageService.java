package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.SampleImageDto;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * SampleImageService
 * 样本图片
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/29 16:54
 */
public interface SampleImageService {

    /**
     * 添加图片
     */
    void addSampleImage(List<SampleImageDto> sampleImageDtos);

    /**
     * 根据ID删除图片
     */
    boolean deleteSampleImage(Long sampleImageId);

    /**
     * 根据样本ID删除图片
     */
    boolean deleteSampleImageBySampleIds(Set<Long> sampleIds);

    /**
     * 根据ID查询样本图片
     */
    SampleImageDto selectSampleImageById(Long sampleImageId);

    /**
     * 查询图片
     */
    List<SampleImageDto> selectSampleImageBySampleId(Long sampleId);

    /**
     * 查询图片 - 根据申请单样本ID查询
     */
    List<SampleImageDto> selectSampleImageByApplySampleId(Long applySampleId);

    /**
     * 根据图片名称删除
     */
    void deleteByImageNames(Collection<String> imageNames, long sampleId);
}
