package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 二次分拣
 */
@Getter
@Setter
public class ApplySampleTwoPickDto implements Serializable {

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 样本类型
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 是否进行了组间交接
     */
    private Boolean isTransform;

    /**
     * 是否加急样本
     */
    private Boolean isUrgent;

    /**
     * 二次分拣颜色
     */
    @Compare("二次分拣颜色")
    private String secondSortColor;

    /**
     * 免疫二次分拣时间
     */
    private LocalDateTime immunityTwoPickDate;

}
