package com.labway.lims.apply.api.dto;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class UpdateTestApplyDto extends TestApplyDto implements Serializable {

    /**
     * 项目信息
     */
    private List<UpdateTestApplyItemDto> testApplyItems;


    /**
     * 是否一次分拣之前， fasle复核之前，  true， 一次分拣之前
     */
    private boolean batchUpdateItem = false;
    @Override
    public List<Item> getItems() {
        if (CollectionUtils.isEmpty(testApplyItems)) {
            return Collections.emptyList();
        }
        return testApplyItems.stream().map(m-> JSON.parseObject(JSON.toJSONString(m), Item.class)).collect(Collectors.toList());
    }
}
