package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.vo.*;

import java.util.List;

/**
 * @description 物料专业组计划服务层
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface GroupMaterialPlanService {


    /**
     * 添加专业组物料计划
     * @param addGroupMaterialPlanDtoList  会组成同一条单子
     */
    boolean addGroupMaterialPlan(List<AddGroupMaterialPlanDto> addGroupMaterialPlanDtoList);

    /**
     * 修改专业组计划
     */
    boolean updateGroupMaterialPlanByPlanNo(String planNo, List<AddGroupMaterialPlanDto> addGroupMaterialPlanDtoList);

    /**
     * 根据计划单号查询计划
     */
    List<GroupMaterialPlanDto> selectByPlanNo(String planNo);

    /**
     * 查询专业组下的所有计划
     */
    List<GroupMaterialPlanVo> selectByGroupId(long groupId);

    /**
     * 查询所有计划
     */
    List<GroupMaterialPlanVo> selectAll(SelectGroupMaterialPlanDto dto);

    /**
     * 删除专业组计划
     * @param planNo 计划单号
     * @return 删除数量
     */
    int deleteByPlanNo(String planNo);

    /**
     * 提交计划
     */
    void submitPlan(String planNo);

    /**
     * 撤销提交计划
     */
    void repealSubmitPlan(String planNo);

    /**
     * 查询待审批的专业组计划列表
     * @param queryAllUnApprovalPlanListDto
     * @return
     */
    List<GroupMaterialPlanVo> queryAllUnApprovalPlanList(QueryAllUnApprovalPlanListDto queryAllUnApprovalPlanListDto);

    /**
     * 审核专业组计划
     * @param approvalPlanDto
     * @return
     */
    ApprovalPlanVo approvalPlan(ApprovalPlanDto approvalPlanDto);

    /**
     * 退回专业组计划
     * @param returnPlanDto
     * @return
     */
    ReturnPlanVo returnPlan(ReturnPlanDto returnPlanDto);

    /**
     * 查询专业组计划列表
     * @param queryApprovalPlanPageDto
     * @return
     */
    List<GroupMaterialPlanVo> queryApprovalPlanPage(QueryApprovalPlanPageDto queryApprovalPlanPageDto);

    /**
     * 删除专业组计划， 根据计划单号 和物料编码
     */
    boolean deleteMaterialByPlanNo(String planNo, List<String> materialCodeList);
}