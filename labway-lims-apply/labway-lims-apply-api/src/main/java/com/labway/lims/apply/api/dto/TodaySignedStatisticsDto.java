package com.labway.lims.apply.api.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <pre>
 * TodaySignedStatistics
 * 当天签收样本统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/11/25 18:35
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TodaySignedStatisticsDto implements Serializable {

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 签收样本数
     */
    private Long signCount;

    /**
     * 打印条码数
     */
    private Long printCount;

}
