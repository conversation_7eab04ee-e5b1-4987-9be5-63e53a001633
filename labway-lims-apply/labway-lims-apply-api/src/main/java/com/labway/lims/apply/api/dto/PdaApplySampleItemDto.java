package com.labway.lims.apply.api.dto;

import com.alibaba.fastjson.JSON;
import com.labway.lims.base.api.dto.ReportItemDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PdaApplySampleItemDto extends ApplySampleItemDto {

    /**
     * pda 申请单id
     */
    private Long pdaApplyId;

    /**
     * pda申请项目信息
     */
    private Long pdaApplySampleItemId;

    /**
     * 报告项目
     */
    private List<ReportItemDto> reportItemDtoList;

    /**
     * 血培养信息
     *
     *
     */
    private String bloodcultureItem;

    /**
     * 血培养信息的结构用法
     * @return
     */
    public ApplySampleItemBloodCultureDto getApplySampleItemBloodCultureDto(){
        return JSON.parseObject(StringUtils.defaultString(bloodcultureItem), ApplySampleItemBloodCultureDto.class);
    }
}
