package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.ApplySampleItemDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 申请单项目
 */
public interface ApplySampleItemService {
    /**
     * 根据申请单样本查询
     */
    List<ApplySampleItemDto> selectByApplySampleId(long applySampleId);

    /**
     * 根据条码号查询，apply_sample 和 apply_sample_item 连表
     */
    List<ApplySampleItemDto> selectByBarcode(long orgId, String barcode);

    /**
     * 根据申请单样本查询 包含终止检验项目
     */
    List<ApplySampleItemDto> selectByApplySampleIdContainStopTest(Collection<Long> applySampleIds);

    /**
     * 根据申请单样本 和 终止状态 查询
     * @param applySampleIds
     * @param stopStatus
     * @return
     */
    List<ApplySampleItemDto> selectByApplySampleIdsAndStopStatus(Collection<Long> applySampleIds, Integer stopStatus);

    /**
     * 根据申请单样本查询
     */
    List<ApplySampleItemDto> selectByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 查询收费项目（正常，终止收费）
     */
    List<ApplySampleItemDto> selectFeeByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据申请单样本查询
     * @param applySampleIds 申请单样本id
     * @param includeDisabled true：包含禁用的项目
     */
    List<ApplySampleItemDto> selectByApplySampleIds(Collection<Long> applySampleIds, boolean includeDisabled);

    /**
     * 根据申请单样本查询.key applySampleId
     */
    List<ApplySampleItemDto> selectAllByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据申请单样本查询
     */
    Map<Long, List<ApplySampleItemDto>> selectByApplySampleIdsAsMap(Collection<Long> applySampleIds);

    /**
     * 根据申请单样本查询
     */
    List<ApplySampleItemDto> selectByApplyIds(Collection<Long> applyIds);

    /**
     * 根据申请单样本查询
     */
    List<ApplySampleItemDto> selectByApplyIds(Collection<Long> applyIds, Boolean all);

    /**
     * 添加申请单项目
     */
    long addApplySampleItem(ApplySampleItemDto dto);

    /**
     * 根据ID删除
     */
    boolean deleteByApplySampleItemId(long applySampleItemId);

    /**
     * 根据申请单样本ID删除
     */
    void deleteByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 批量保存样本项目
     */
    void addApplySampleItems(List<ApplySampleItemDto> applySampleItems);

    /**
     * 统计样本下的项目数量
     */
    int countByApplySampleId(long applySampleId);

    /**
     * 统计申请单下的项目数量
     */
    int countByApplyId(long applyId);

    /**
     * 根据id查询样本检验项目
     */
    ApplySampleItemDto selectById(long applySampleItemId);

    /**
     * 根据ids查询样本检验项目
     */
    List<ApplySampleItemDto> selectByIds(Collection<Long> applySampleItemIds);

    /**
     * 批量修改
     */
    void updateBatchById(Collection<ApplySampleItemDto> applySampleItems);

    /**
     * 根据申请单样本ids删除
     */
    boolean deleteByApplySampleItemIds(Collection<Long> applySampleItemIds);

    /**
     * batch delete by applyId
     */
    void deleteByApplyIds(Collection<Long> applyIds);

    /**
     * 根据申请单样本ID和检验项目ID查询
     */
    ApplySampleItemDto selectByApplySampleIdAndTestItemId(long applySampleId, long testItemId);

    /**
     * 保存 微生物 费用项目
     * 
     * @param needDeleteSampleFeeItemIds 需要删除的 费用项目
     * @param needUpdateFeeItems 需要更新的 费用项目
     * @param needAddFeeTestItems 需要新增的 费用项目
     */
    void saveMicrobiologySampleFeeItem(Collection<Long> needDeleteSampleFeeItemIds,
        List<ApplySampleItemDto> needUpdateFeeItems, List<ApplySampleItemDto> needAddFeeTestItems);

    /**
     *  根据id修改信息
     * @param applySampleItemDto
     */
    void updateById(ApplySampleItemDto applySampleItemDto);

    /**
     * 更新样本项目状态到正常
     * @param applySampleIds
     */
    void updateStopStatusByApplySampleId(List<Long> applySampleIds);
}
