package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class HisTestApplyDto extends TestApplyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 检验项目
     */
    private List<HisTestApplyItemDto> hisTestApplyItems;

    /**
     * 是否支持分管
     */
    private boolean canSplitSample;

	/**
	 * 标本部位
	 * @since 1.1.4
	 * @Description <a href="https://www.tapd.cn/59091617/prong/stories/view/1159091617001001756?from_iteration_id=1159091617001000242">评论</a>
	 */
	private String patientPart;

    @Override
    public List<Item> getItems() {
        if (CollectionUtils.isEmpty(hisTestApplyItems)) {
            return Collections.emptyList();
        }
        return hisTestApplyItems.stream().map(m -> {
            Item item = m;
            return item;
        }).collect(Collectors.toList());
    }
}
