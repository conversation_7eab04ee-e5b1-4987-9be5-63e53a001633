package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.dto.UploadPdfDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 样本报告Service
 * 
 * <AUTHOR>
 * @since 2023/4/11 13:37
 */
public interface SampleReportService {
    /**
     * 根据id删除
     */
    boolean deleteBySampleReportId(long sampleReportId);

    /**
     * 批量删除
     */
    void deleteBySampleReportIds(Collection<Long> sampleReportId);

    /**
     * 根据sampleId删除
     */
    void deleteBySampleIds(Collection<Long> sampleIds);

    /**
     * 根据sampleId删除 不是上传的pdf
     */
    void deleteBySampleIdsAndNotUpload(Collection<Long> sampleIds);

    /**
     * 添加 样本报告
     */
    long addSampleReport(SampleReportDto dto);

    void addSampleReportBatch(Collection<SampleReportDto> dtos);

    void addSampleReportBatch(Collection<SampleReportDto> dtos, Map<String, Object> extra);

    /**
     * 根据申请单样本 查询样本报告
     */
    List<SampleReportDto> selectByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据 id 查询样本报告
     */
    List<SampleReportDto> selectBySampleReportIds(Collection<Long> sampleReportIds);

    /**
     * 根据 id 查询样本报告
     */
    @Nullable
    SampleReportDto selectBySampleReportId(Long sampleReportId);

    /**
     * 根据申请单样本 查询样本报告
     */
    @Nullable
    SampleReportDto selectByApplySampleId(long applySampleId);

    /**
     * 根据样本id更新
     */
    void updateBySampleId(SampleReportDto sampleReport);

    /**
     * 刷新报告
     */
    void refreshReport(ApplySampleDto applySample);

    /**
     *  根据applyId 去修改对应的送检机构
     */
    void updateByApplyId(SampleReportDto sampleReportDto);

    void updateByApplyIds(SampleReportDto sampleReportDto, Collection<Long> applyIds);

    /**
     * 外送样本上传PDF报告
     */
    SampleReportDto uploadReport(UploadPdfDto uploadPdfDto);

    /**
     * 常规样本上传PDF报告
     */
    SampleReportDto uploadReportRoutine(UploadPdfDto uploadPdfDto);

}
