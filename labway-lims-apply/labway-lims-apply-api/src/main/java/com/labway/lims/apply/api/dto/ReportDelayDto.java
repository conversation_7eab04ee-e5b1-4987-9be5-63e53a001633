package com.labway.lims.apply.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @description 报告单迟发表
 * @date 2023-12-15
 */
@Getter
@Setter
public class ReportDelayDto implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * @Fields delayId 迟发ID
     */
    private Long delayId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 检验项目编码集合(空 为全部)
     */
    private String testItemCodes;

    /**
     * 迟发原因
     */
    private String reason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预计发布日期
     */
    private Date sendReportDate;

    /**
     * 作废人ID
     */
    private Long cancelUserId;

    /**
     * 作废人姓名
     */
    private String cancelUserName;

    /**
     * 申请单状态(0未打印 1已打印 9已作废)
     */
    private Integer status;

    /**
     * 专业组id
     */
    private Long groupId;

    /**
     * 专业组
     */
    private String groupName;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 修改人id
     */
    private Long updaterId;

    /**
     * @Fields updaterName 修改人名称
     */
    private String updaterName;


    /**
     * 申请日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date sendDateStart;

    /**
     * 申请日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date sendDateEnd;

}
