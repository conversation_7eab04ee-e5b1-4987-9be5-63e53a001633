package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.vo.BizMaterialBarcodeVo;

import java.util.List;

/**
 * <p>
 * 物料入库记录 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
public interface MaterialIncomeRecordService {

    /**
     * 根据 入库时间 查看 入库信息
     */
    List<MaterialIncomeInfoDto> selectBySelectMaterialIncomeInfoDto(SelectMaterialIncomeInfoDto dto);

    /**
     * 根据出库单号 获取入库详情
     */
    List<MaterialIncomeRecordDto> selectByDeliveryNo(String deliveryNo, long orgId);

    /**
     * 物料 待入库单子 进行 入库操作
     * 
     * @param deliveryNo 待入库 出库单号
     * @param incomeItemList 入库物料信息
     */
    void materialIncomeByRecordId(String deliveryNo, List<MaterialDeliveryIncomeItemDto> incomeItemList);

    /**
     * 添加 物料 入库记录
     */
    void addMaterialIncomeRecords(List<MaterialIncomeRecordDto> list);

	/**
	 * 中台批量查询物料 - 批次 - 条码号
	 * @param materialInfos 物料信息 - 物料条码 - 批次号
	 * @return {@link BizMaterialBarcodeVo}
	 */
	List<BizMaterialBarcodeVo> selectMaterialBarcode(BusinessCenterMaterialBarcodeSearchDto materialInfos);
}
