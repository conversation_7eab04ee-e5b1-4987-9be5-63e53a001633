package com.labway.lims.apply.api.dto;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
public class MaterialApplyNoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> applyNos;

    public void verifyParams() {
        if (CollectionUtils.isEmpty(applyNos)) {
            throw new IllegalArgumentException("申领单号不能为空");
        }
    }
}
