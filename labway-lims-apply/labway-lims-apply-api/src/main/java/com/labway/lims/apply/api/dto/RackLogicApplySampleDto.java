package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 逻辑试管架样本
 */
@Getter
@Setter
public class RackLogicApplySampleDto extends ApplySampleDto {
    /**
     * 试管架ID
     */
    private Long rackId;

    /**
     * 逻辑试管架ID
     */
    private Long rackLogicId;

    /**
     * 位置
     * @see RackLogicPositionEnum
     */
    private Integer position;


    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 行数
     */
    private Integer row;

    /**
     * 列数
     */
    private Integer column;
}
