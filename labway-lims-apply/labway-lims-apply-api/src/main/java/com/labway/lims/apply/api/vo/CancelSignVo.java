package com.labway.lims.apply.api.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
public class CancelSignVo {
    /**
     * 申请单id
     */
    private Set<Long> applyIds;

    /**
     * 原因名称
     */
    private String reasonName;

    /**
     * 原因编码
     */
    private String reasonCode;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 签收项目类型
     */
    private String noType;
}
