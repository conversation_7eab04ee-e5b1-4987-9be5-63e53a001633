package com.labway.lims.apply.api.dto;

import cn.hutool.json.JSONUtil;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 业务中台出库信息Dto
 *
 * <AUTHOR>
 * @since 2023/5/6 11:08
 */
@Getter
@Setter
public class BusinessCenterDeliveryDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 申请单号
     */
    private String applyNo;
    /**
     * 出库单号
     */
    private String deliveryNo;

    /**
     * 出库日期
     */
    private Date deliveryDate;
    /**
     * 出库人
     */
    private String deliveryUser;

    /**
     * 出库 物料
     */
    private List<BusinessCenterDeliveryItemDto> deliveryItemList;

    /**
     * 是否为全部出库 true 全部出库 false 部分出库
     */
    private Boolean isAllOutFlag;

    /**
     * 业务中台 出库 物料信息
     */
    @Getter
    @Setter
    public static class BusinessCenterDeliveryItemDto implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 物资编号
         */
        private String materialCode;

        /**
         * 物资名称
         */
        private String materialName;

        /**
         * 规格
         */
        private String specification;

        /**
         * 批号
         */
        private String batchNo;

        /**
         * 厂家
         */
        private String manufacturers;

        /**
         * 主单位
         */
        private String mainUnit;

        /**
         * 出库主单位数量
         */
        private Integer deliveryMainNumber;

	    /**
	     * 出库单位数量 小数
	     */
		private BigDecimal deliveryMainNumberDecimal;

        /**
         * 辅单位
         */
        private String assistUnit;

        /**
         * 出库辅单位数量
         */
        private BigDecimal deliveryAssistNumber;

        /**
         * 主辅单位换算率
         */
        private String unitConversionRate;

        /**
         * 有效期
         */
        private Date validDate;

	    /**
	     * 物料条码号·
	     */
		private String materialBarcode;

        @Override
        public String toString() {
            return JSONUtil.toJsonStr(this);
        }
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}
