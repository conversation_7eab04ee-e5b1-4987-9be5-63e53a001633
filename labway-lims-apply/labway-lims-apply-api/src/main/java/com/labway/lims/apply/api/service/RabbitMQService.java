package com.labway.lims.apply.api.service;


import com.labway.lims.api.trace.TraceLog;

/**
 * <AUTHOR>
 * @since 2023/6/15 20:41
 */
public interface RabbitMQService {
    /**
     * mq交换机
     */
    String EXCHANGE = TraceLog.EXCHANGE;

    void convertAndSend(String exchange, String routingKey, String message);

    /**
     * 发送延迟消息
     *
     * @param exchange   交换机
     * @param routingKey 路由键，传原路由键，如果延迟时间>0，则发送的路由键自动拼上"_dlx"
     * @param msg        消息内容
     * @param expiration 延迟时间，单位：毫秒
     */
    void convertAndSend(String exchange, String routingKey, String msg, Long expiration);

}
