package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.Map;

/**
 * 申请单样本事件
 */
@Getter
@Setter
public class ApplySampleEventDto implements Serializable {

    /**
     * 事件
     */
    private EventType event;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 样本ID
     */
    private Long applyId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 额外参数
     */
    @Nullable
    private Map<String, String> extras;

    public enum EventType {
        /**
         * 完成创建申请单时
         */
        CreateApply,
        /**
         * 完成二次分拣时
         */
        TwoPick,
        /**
         * 完成一次审核时
         */
        OneCheck,
        /**
         * 完成二次审核时
         */
        TwoCheck,
        /**
         * 取消一次审核时
         */
        CancelOneCheck,
        /**
         * 取消二次审核时
         */
        CancelTwoCheck,

        /**
         * 终止检验
         */
        TERMINATE,

        ;
    }

    public static final String SAMPLE_CHANGE_KEY = "sample_change_key";

    public static final String SAMPLE_CHANGE_EXCHANGE = "labway_lims";
}
