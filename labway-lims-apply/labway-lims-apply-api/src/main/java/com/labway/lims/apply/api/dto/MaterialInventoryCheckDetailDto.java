package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物料盘点详情 Dto
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Getter
@Setter
public class MaterialInventoryCheckDetailDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 详情ID
     */
    private Long detailId;

    /**
     * 盘点ID
     */
    private Long checkId;

    /**
     * 盘点单号
     */
    private String checkNo;

    /**
     * 库存ID
     */
    private Long inventoryId;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物资编号
     */
    private String materialCode;

    /**
     * 物资名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 厂家
     */
    private String manufacturers;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 主单位库存
     */
    private BigDecimal mainInventory;

    /**
     * 实际主数量
     */
    private BigDecimal actualMainInventory;

    /**
     * 盈亏主数量
     */
    private BigDecimal mainProfit;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     * 辅单位库存
     */
    private BigDecimal assistInventory;

    /**
     * 实际辅数量
     */
    private BigDecimal actualAssistInventory;

    /**
     * 盈亏辅数量
     */
    private BigDecimal assistProfit;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 有效期
     */
    private Date validDate;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 物料条码号
     */
    private String materialBarcode;

	/**
	 * 物料类别编码
	 */
	private String materialTypeCode;

	/**
	 * 物料类别名称
	 */
	private String materialTypeName;

}
