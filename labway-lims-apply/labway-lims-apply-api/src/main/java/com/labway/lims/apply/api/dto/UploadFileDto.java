package com.labway.lims.apply.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName tb_upload_file
 */
@Data
public class UploadFileDto implements Serializable {
    /**
     * 文件编码
     */
    private Long fileId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 文件类型
     * @see com.labway.lims.apply.api.enums.ImportFileEnum
     */
    private Integer type;

    /**
     * 文件状态
     * @see com.labway.lims.apply.api.enums.ImportFileStatusEnum
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 文件详情
     */
    private String info;

    /**
     * 创建人姓名
     */
    private String uploaderName;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 文件类型描述
     */
    private String typeDesc;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 文件来源
     *  @see com.labway.lims.apply.api.enums.ImportFileResourceEnum
     */
    private String resource;

    private static final long serialVersionUID = 1L;

}