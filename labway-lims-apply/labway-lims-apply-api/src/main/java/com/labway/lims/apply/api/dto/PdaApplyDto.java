package com.labway.lims.apply.api.dto;

import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import java.util.StringJoiner;

@Data
public class PdaApplyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * pda申请单ID
     */
    private Long pdaApplyId;

    /**
     * 主条码号
     */
    private String masterBarcode;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 病人年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 子年龄单位,月、周、日
     */
    private String patientSubageUnit;

    /**
     * 生日
     */
    private Date patientBirthday;

    /**
     * 卡号
     */
    private String patientCard;

    /**
     * 类型,1:身份证
     */
    private String patientCardType;

    /**
     * 床号
     */
    private String patientBed;

    /**
     * 性别,1:男,2:女
     */
    private Integer patientSex;

    /**
     * 就诊卡号
     */
    private String patientVisitCard;

    /**
     * 手机号
     */
    private String patientMobile;

    /**
     * 地址
     */
    private String patientAddress;

    /**
     * 来源
     */
    private String source;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 就诊类型
     */
    private String applyTypeCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 样本数量
     */
    private Integer sampleCount;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 部门
     */
    private String dept;

    /**
     * 临床诊断
     */
    private String diagnosis;

    /**
     * 送检医生
     */
    private String sendDoctorName;

    /**
     * 送检医生编码
     */
    private String sendDoctorCode;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 是否急诊,1:是,0:不是
     */
    private Integer urgent;

    /**
     * 申请日期
     */
    private Date applyDate;

    /**
     * 采样时间
     */
    private Date samplingDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 1:删除,0:没有删除
     */
    private Integer isDelete;

    /**
     * 样本性状编码
     */
    private String samplePropertyCode;

    /**
     * 就诊类型名称
     */
    private String applyTypeName;

    /**
     * 原始机构编码
     */
    private String originalOrgCode;

    /**
     * 原始机构名称
     */
    private String originalOrgName;

    /**
     * 外部条码
     */
    private String outBarcode;


    public String toSampleFlowContent() {
        final StringJoiner sb = new StringJoiner("\n");

        sb.add("机构: " + hspOrgName);
        sb.add("姓名: " + patientName);
        sb.add("年龄: " + PatientAges.toText(this));
        sb.add("性别: " + SexEnum.getByCode(patientSex).getDesc());
        sb.add("就诊类型: " + applyTypeName);

        if (Objects.nonNull(patientBirthday)) {
            sb.add("出生日期: " + DateFormatUtils.format(patientBirthday, "yyyy-MM-dd"));
        }
        if (StringUtils.isNotBlank(sampleProperty)) {
            sb.add("样本性状: " + sampleProperty);
        }
        if (Objects.nonNull(urgent)) {
            sb.add("急诊状态: " + (Objects.equals(urgent, YesOrNoEnum.YES.getCode()) ? "加急" : "普通"));
        }
        if (Objects.nonNull(sampleCount)) {
            sb.add("样本个数: " + sampleCount);
        }
        if (Objects.nonNull(applyDate)) {
            sb.add("申请时间: " + DateFormatUtils.format(applyDate, "yyyy-MM-dd HH:mm:ss"));
        }
        if (Objects.nonNull(samplingDate)) {
            sb.add("采样时间: " + DateFormatUtils.format(samplingDate, "yyyy-MM-dd HH:mm:ss"));
        }
        if (StringUtils.isNotBlank(patientVisitCard)) {
            sb.add("门诊/住院号: " + patientVisitCard);
        }
        if (StringUtils.isNotBlank(dept)) {
            sb.add("科室: " + dept);
        }
        if (StringUtils.isNotBlank(patientBed)) {
            sb.add("床号: " + patientBed);
        }
        if (StringUtils.isNotBlank(sendDoctorName)) {
            sb.add("送检医生: " + sendDoctorName);
        }
        if (StringUtils.isNotBlank(diagnosis)) {
            sb.add("临床诊断: " + diagnosis);
        }
        if (StringUtils.isNotBlank(remark)) {
            sb.add("备注: " + remark);
        }
        if (StringUtils.isNotBlank(patientMobile)) {
            sb.add("电话: " + patientMobile);
        }
        if (StringUtils.isNotBlank(patientCard)) {
            sb.add("身份证号: " + patientCard);
        }
        return sb.toString();
    }
}