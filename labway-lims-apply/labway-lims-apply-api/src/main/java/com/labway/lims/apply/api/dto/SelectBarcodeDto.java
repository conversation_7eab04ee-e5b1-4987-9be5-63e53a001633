package com.labway.lims.apply.api.dto;

import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class SelectBarcodeDto {

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 体检团体id
     */
    private Long physicalGroupId;

    /**
     * 管型
     */
    private String tubeCode;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 条码号
     */
    private String barcode;

    public void verifyParams(){
        if(Objects.isNull(startDate) || Objects.isNull(endDate)){
            throw new IllegalArgumentException("接受时间范围不能为空");
        }
    }
}
