package com.labway.lims.apply.api.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.List;

/**
 * 二次分拣
 */
@Getter
@Setter
@Accessors(chain = true)
public class TwoPickDto implements Serializable {
    /**
     * 申请单样本ID
     */
    private long applySampleId;

    /**
     * 样本号
     */
    @Nullable
    private String sampleNo;

    /**
     * 加急样本号
     */
    @Nullable
    private String urgentSampleNo;

    /**
     * 仪器id
     */
    private Long instrumentId;


    /***
     * 是否已经校验了项目性状
     * 0：未校验
     * 1：已校验
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private int isValidProperty;

    /**
     * 项目性状关系映射
     */
    private List<MicrobiologyItemPropertyRelationDto> itemPropertyRelationList;


    @Data
    public static class MicrobiologyItemPropertyRelationDto implements Serializable {

        private static final long serialVersionUID = 1L;

        // 检验项目ID
        private String testItemId;

        // 检验项目编码
        private String testItemCode;

        // 检验项目名称
        private String testItemName;

        // 项目性状属性编码
        private String propertyCode;

        // 项目性状属性名称
        private String propertyName;

    }


}
