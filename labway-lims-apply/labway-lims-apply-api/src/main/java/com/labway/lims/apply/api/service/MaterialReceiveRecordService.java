package com.labway.lims.apply.api.service;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.MaterialReceiveRecordDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRegisterItemDto;
import com.labway.lims.apply.api.dto.SelectMaterialReceiveRecordDto;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Set;

/**
 * 物料领用记录 Service
 * 
 * <AUTHOR>
 * @since 2023/5/9 16:56
 */
public interface MaterialReceiveRecordService {

    /**
     * 根据 出库时间 查看
     */
    List<MaterialReceiveRecordDto> selectBySelectMaterialReceiveRecordDto(SelectMaterialReceiveRecordDto dto);

    /**
     * 物料 领用登记
     */
    void materialReceiveRegister(List<MaterialReceiveRegisterItemDto> list);

    /**
     * 物料 登记 作废
     */
    void materialReceiveInvalid(long receiveId);

    /**
     * 添加 物料 领用记录
     */
    void addMaterialReceiveRecords(List<MaterialReceiveRecordDto> list);

    /**
     * 根据 物料领用记录id 查询 记录
     */
    @Nullable
    MaterialReceiveRecordDto selectByReceiveId(long receiveId);

    /**
     * 修改 领用记录
     */
    void updateByReceiveId(MaterialReceiveRecordDto dto);

    /**
     * 校验领用记录
     * @param inventoryIdList 库存id
     * @param user 用户
     */
    List<MaterialReceiveRecordDto> selectReceiveRecord(Set<Long> inventoryIdList, LoginUserHandler.User user);
}
