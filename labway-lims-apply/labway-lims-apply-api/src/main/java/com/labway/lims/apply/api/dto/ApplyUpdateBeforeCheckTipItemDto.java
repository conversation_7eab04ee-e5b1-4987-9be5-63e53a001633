package com.labway.lims.apply.api.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <pre>
 * ApplyUpdateBeforeCheckTipDto
 * 样本信息修改提示
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/1/18 10:55
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ApplyUpdateBeforeCheckTipItemDto implements Serializable {

    // 条码号
    private String barcode;

    // 专业组
    private String groupName;

    // 当前环节
    private String barcodeFlow;

    // 报告项目名称
    private String reportItemName;

    // 结果值
    private String result;

    // 参考范围
    private String referenceRange;

}
