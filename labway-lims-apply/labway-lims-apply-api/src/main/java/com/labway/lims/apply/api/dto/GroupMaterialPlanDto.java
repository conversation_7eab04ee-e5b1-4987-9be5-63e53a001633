package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.apply.api.enums.GroupMaterialPlanEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * @description 物料专业组计划
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
@Accessors(chain = true)
public class GroupMaterialPlanDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物料专业组计划id
     */
    private Long groupMaterialPlanId;

    /**
     * JH + 当前实验室两位编码 + 当前日期（240710） + 四位自增
     */
    private String planNo;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 厂家
     */
    private String manufacturers;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 主申请数量
     */
    private BigDecimal mainApplyNumber;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     * 辅申请数量
     */
    private String assistApplyNumber;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 专业组id
     */
    private Long groupId;

    /**
     * 专业组code
     */
    private String groupCode;

    /**
     * 专业组name
     */
    private String groupName;


    /**
     * 检验机构id
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 状态,  1已保存， 2已提交， 3已审核 4已退回
     * {@link GroupMaterialPlanEnum}
     */
    private Integer status;

    /**
     * creator_id
     */
    private Long creatorId;

    /**
     * creator_name
     */
    private String creatorName;

    /**
     * create_date
     */
    private Date createDate;

    /**
     * updater_id
     */
    private Long updaterId;

    /**
     * updater_name
     */
    private String updaterName;

    /**
     * update_date
     */
    private Date updateDate;

    /**
     * 审核人
     */
    private Long auditId;

    /**
     * 审核人
     */
    private String auditName;

    /**
     * 审核时间
     */
    private Date auditDate;

    /**
     * 计划人id
     */
    private Long plannerId;

    /**
     * 计划人name
     */
    private String plannerName;

    /**
     * 计划时间
     */
    private Date plannerDate;

    /**
     * 退回原因
     */
    private String returnReason;

    /**
     * 退回原因
     */
    private Long returnUserId;

    /**
     * 退回原因
     */
    private String returnUserName;

    /**
     * 退回时间
     */
    private Date returnDate;

    /**
     * 备注
     */
    private String remark;


    /**
     * 提交人
     */
    private Long submitId;

    /**
     * 提交人
     */
    private String submitName;

    /**
     * 提交时间
     */
    private Date submitDate;


    /**
     * 清空默认时间
     */
    public void clearDefaultDate() {
        this.auditDate = Objects.equals(this.auditDate, DefaultDateEnum.DEFAULT_DATE.getDate()) ? null : this.auditDate;
        this.returnDate = Objects.equals(this.returnDate, DefaultDateEnum.DEFAULT_DATE.getDate()) ? null : this.returnDate;
        this.submitDate = Objects.equals(this.submitDate, DefaultDateEnum.DEFAULT_DATE.getDate()) ? null : this.submitDate;
    }

}