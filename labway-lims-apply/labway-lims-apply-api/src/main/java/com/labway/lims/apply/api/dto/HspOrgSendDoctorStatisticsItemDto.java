package com.labway.lims.apply.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 机构送检医生统计 单行
 * 
 * <AUTHOR>
 * @since 2023/5/18 13:23
 */
@Setter
@Getter
public class HspOrgSendDoctorStatisticsItemDto implements Serializable {
    /**
     * 送检机构
     */
    private String hspOrgName;
    /**
     * 送检时间 yyyy-MM-dd
     */
    private String sendDate;
    /**
     * 外部条码号
     */
    private String outBarcode;
    /**
     * 终审时间
     */
    private String finalCheckDate;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 患者年龄
     */
    private String patientAge;
    /**
     * 患者性别
     *
     * @see com.labway.lims.api.enums.apply.SexEnum
     */
    private String patientSex;
    /**
     * 科室
     */
    private String dept;
    /**
     * 送检医生名称
     */
    private String sendDoctorName;
    /**
     * 就诊卡号 (门诊|住院号)
     */
    private String patientVisitCard;
    /**
     * 就诊类型
     */
    private String applyTypeName;
    /**
     * 检验项目
     */
    private String testItemCode;
    /**
     * 检验项目
     */
    private String testItem;
    /**
     * 数量
     */
    private Integer count;
    /**
     * 标准单价
     */
    private BigDecimal feePrice;
    /**
     * 合计金额： 数量*标准单价
     */
    private BigDecimal totalFeePrice;
    /**
     * 折扣率
     */
    private String discount;
    /**
     * 结算金额
     */
    private BigDecimal payAmount;


    // 一下补充统计样本所需字段
    private String barcode;
    //检验项目名称
    private String testItemName;
    //外部项目编码
    private String outTestItemCode;
    //外部项目名称
    private String outTestItemName;
     // 送检机构
    private String hspOrgCode;
    //病人生日
    private Date patientBirthday;
    //床号
    private String patientBed;
    //样本创建时间（三方系统的创建时间）
    private Date createDate;
    //送检时间
    private Date applyDate;
    //送检医生
    private String sendDoctorCode;
    // 检验时间
    private Date testDate;
    // 检验人员
    private Long testerId;
    // 检验人员
    private String testerName;
    // 终审人员
    private Long finalCheckerId;
    // 终审人员
    private String finalCheckerName;
    // 是否是分血样本 0否1是
    private Integer isSplitBlood;
    // 样本id
    private Long applySampleId;
    // 终审时间
    private Date finalCheckDateTime;

}
