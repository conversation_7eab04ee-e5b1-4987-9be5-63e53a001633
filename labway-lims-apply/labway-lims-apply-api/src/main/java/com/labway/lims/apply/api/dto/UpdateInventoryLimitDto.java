package com.labway.lims.apply.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 更新无聊上下限
 */
@Data
public class UpdateInventoryLimitDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物料上下限校验规则，  小数点前最多十位， 小数点后最多4位
     * numeric(14,4)
     */
    public static final String VALID_NUMBER = "^-?\\d{1,10}(\\.\\d{1,4})?$";

    /**
     * 物料code
     */
    private String materialCode;

    /**
     * 库存上限
     */
    private BigDecimal inventoryUpperLimit;

    /**
     * 库存下限
     */
    private BigDecimal inventoryLowerLimit;
}
