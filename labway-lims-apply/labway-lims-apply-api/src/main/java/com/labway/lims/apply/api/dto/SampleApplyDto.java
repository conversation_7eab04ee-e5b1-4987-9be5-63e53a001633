package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class SampleApplyDto extends ApplyDto {
    /**
     * 样本条码
     */
    private String barcode;
    /**
     * 增加外部条码号
     */
    private String outBarcode;
    /**
     * 申请单样本项目id
     */
    private Long applySampleId;
    /**
     * 申请单图片 多个 , 隔开
     */
    private String applyImage;
    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 样本状态描述
     */
    private String sampleStatusDesc;
    /**
     * 样本状态
     */
    private Integer sampleStatusCode;

    /**
     * 复核人
     */
    private String checkerName;

    /**
     * 复核时间
     */
    private Date checkDare;

    /**
     * 标本部位
     * @since 1.1.4
     */
    private String patientPart;

}
