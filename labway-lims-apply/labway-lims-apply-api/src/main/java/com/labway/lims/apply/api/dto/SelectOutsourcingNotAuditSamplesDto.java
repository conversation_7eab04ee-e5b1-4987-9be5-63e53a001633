package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <pre>
 * SelectOutsourcingNotAuditSamplesDto
 * 查询外送样本未审核数据
 * </pre>
 *
 * <AUTHOR>
 * @since 2023/11/27 14:32
 */
@Getter
@Setter
public class SelectOutsourcingNotAuditSamplesDto implements Serializable {

    /**
     * 排除的状态
     * {@link com.labway.lims.api.enums.apply.SampleStatusEnum}
     */
    private Integer excludeStatus;

    /**
     * 是否是外送
     */
    private Integer isOutsourcing;

    /**
     * 同步二次分拣时间在该时间之后的数据
     */
    private Date syncAfter;

}
