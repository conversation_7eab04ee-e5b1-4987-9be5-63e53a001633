package com.labway.lims.apply.api.dto.es;

import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySampleItemSourceEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.ApplySupplierEnum;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class BaseSampleEsModelDto implements Serializable {
    // 申请单信息

    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 主条码
     */

    private String masterBarcode;

    /**
     * his流水号
     */
    private String hisSerialNo;

    /**
     * 科室
     */

    private String dept;

    /**
     * 是否加急 1是 0否
     *
     * @see UrgentEnum
     */

    private Integer urgent;

    /**
     * 1: 急诊，0:不急
     *
     * @see UrgentEnum
     * @see UrgentEnum
     */
    private Integer applyUrgent;
    /**
     * 申请单状态
     *
     * @see ApplyStatusEnum
     */

    private Integer applyStatus;

    /**
     * 患者名称
     */

    private String patientName;

    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 患者子年龄
     */
    private Integer patientSubage;

    /**
     * 患者子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 患者出生日期
     */
    private Date patientBirthday;

    /**
     * 身份证
     */
    private String patientCard;

    /**
     * 证件类型
     *
     * @see PatientCardTypeEnum
     */

    private String patientCardType;

    /**
     * 床号
     */

    private String patientBed;

    /**
     * 性别
     *
     * @see com.labway.lims.api.enums.apply.SexEnum
     */

    private Integer patientSex;

    /**
     * 就诊卡号 (门诊|住院号)
     */

    private String patientVisitCard;

    /**
     * 手机号
     */
    private String patientMobile;

    /**
     * 患者地址
     */
    private String patientAddress;

    /**
     * 就诊类型 (申请类型)
     */
    private String applyTypeCode;

    /**
     * 就诊类型 (申请类型)
     */

    private String applyTypeName;

    /**
     * 样本个数
     */
    private Integer sampleCount;

    /**
     * 样本性状
     */
    private String sampleProperty;
    /**
     * 样本性状编码
     */
    private String samplePropertyCode;

    /**
     * 临床诊断
     */
    private String diagnosis;

    /**
     * 送检医生名称
     */
    private String sendDoctorName;

    /**
     * 送检医生编码
     */
    private String sendDoctorCode;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 采样时间
     */
    private Date samplingDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 申请单来源
     *
     * @see ApplySourceEnum
     */
    private String source;

    /**
     * 供应商
     *
     * @see ApplySupplierEnum
     */
    private String supplier;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    // 申请单样本信息
    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 样本条码
     */
    private String barcode;

    /**
     * 管型
     */
    private String tubeName;

    /**
     * 管型编码
     */
    private String tubeCode;

    /**
     * 样本类型
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;

    /**
     * 专业组id
     */
    private Long groupId;
    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 样本状态
     *
     * @see SampleStatusEnum
     */
    private Integer sampleStatus;
    /**
     * 是否禁用 1是 0 否
     *
     * @see YesOrNoEnum
     */
    private Integer isDisabled;
    /**
     * 是否归档:0未归档，1已归档
     *
     * @see YesOrNoEnum
     */
    private Integer isArchive;

    /**
     * 试管架id
     */
    private Long rackId;

    /**
     * 一次分拣人id
     */
    private Long onePickerId;

    /**
     * 一次分拣人
     */
    private String onePickerName;

    /**
     * 一次分拣日期
     */
    private Date onePickDate;

    /**
     * 是否已经一次分拣 1是，0不是
     *
     * @see YesOrNoEnum
     */
    private Integer isOnePick;

    /**
     * 二次分拣人id
     */
    private Long twoPickerId;

    /**
     * 二次分拣人
     */
    private String twoPickerName;

    /**
     * 二次分拣日期
     */
    private Date twoPickDate;

    /**
     * 是否已经二次分拣 1是，0不是
     */
    private Integer isTwoPick;

    /**
     * 是否是免疫二次分拣 1：是 0：否
     */
    private Integer isImmunityTwoPick;

    /**
     * 是否已经分血，1：是，0：不是
     */
    private Integer isSplitBlood;

    /**
     * 分血人ID
     */
    private Long splitterId;

    /**
     * 分血人
     */
    private String splitterName;

    /**
     * 分血时间
     */
    private Date splitDate;

    // 检验样本信息 （常规检验、微生物检验、院感......）
    /**
     * 样本id （可能是常规检验|微生物|院感...）
     */
    private Long sampleId;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业小组id
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 检验时间
     */
    private Date testDate;

    /**
     * 样本备注
     */
    private String sampleRemark;

    /**
     * 结果备注
     */
    private String resultRemark;

    /**
     * 终审人id
     */
    private Long finalCheckerId;

    /**
     * 终审人
     */
    private String finalCheckerName;

    /**
     * 终审时间
     */
    private Date finalCheckDate;

    /**
     * 项目类型 常规检验、微生物检验、院感检验 区分做什么检验的
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 检验项目
     */
    private List<TestItem> testItems;

    /**
     * 通用报告信息
     */
    private List<Report> reports;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建时间|录入时间
     */
    private Date createDate;

    /**
     * 修改人id
     */
    private Long updaterId;

    /**
     * 修改人
     */
    private String updaterName;

    /**
     * 修改人时间|录入时间
     */
    private Date updateDate;

    /**
     * 是否删除 1是 0否
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 是否打印:1已打印，0未打印
     */
    private Integer isPrint;

    /**
     * 是否外送
     *
     * @see YesOrNoEnum
     */
    private Integer isOutsourcing;

    /**
     * 打印人ID
     */
    private Long printerId;

    /**
     * 打印人姓名
     */
    private String printerName;

    /**
     * 打印时间
     */
    private Date printDate;

    /**
     * 签收日期
     */
    private Date signDate;

    /**
     * 检验人ID
     */
    private Long testerId;
    /**
     * 检验人姓名
     */
    private String testerName;

    /**
     * 仪器信息
     */
    private Long instrumentId;
    /**
     * 仪器信息
     */
    private String instrumentName;

    /**
     * 体检单位
     */
    private Long physicalCompanyId;

    /**
     * 体检单位名称
     */
    private String physicalCompanyName;
    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;
    /**
     * 复核人id
     */
    private Long checkerId;

    /**
     * 复核人
     */
    private String checkerName;

    /**
     * 复核时间
     */
    private Date checkDate;

    /**
     * 当前条码环节
     */
    private String barcodeFlow;

	/**
	 * 标本部位
	 * @since 1.1.4
	 * @Description <a href="https://www.tapd.cn/59091617/prong/stories/view/1159091617001001756?from_iteration_id=1159091617001000242">评论</a>
	 */
	private String patientPart;

    /**
     * 并单的主条码， applySample.barcode
     */
    private String mergeMasterBarcode;

    /**
     * 是否是并单主样本
     */
    private Boolean mergeMasterSample;

    /**
     * 报告单
     */
    @Getter
    @Setter
    public static final class Report implements Serializable {

        /**
         * 报告url
         */
        private String url;

        /**
         * 人工上传
         */
        private Integer artificialUpload;

        /**
         * 报告类型
         *
         * @see SampleReportFileTypeEnum
         */
        private String fileType;

        /**
         * 报告id
         */
        private Long sampleReportId;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static final class TestItem implements Serializable {

        /**
         * 样本检验项目id
         */
        private Long applySampleItemId;
        /**
         * 检验项目ID
         */

        private Long testItemId;
        /**
         * 检验项目编码
         */

        private String testItemCode;
        /**
         * 检验项目名称
         */

        private String testItemName;
        /**
         * 外部项目编码
         */

        private Long outTestItemId;
        /**
         * 外部项目名称
         */

        private String outTestItemCode;
        /**
         * 外部项目名称
         */

        private String outTestItemName;
        /**
         * 专业组id
         */
        private Long groupId;
        /**
         * 专业组名称
         */
        private String groupName;
        /**
         * @see com.labway.lims.api.enums.apply.UrgentEnum
         */
        private Integer urgent;

        /**
         * 收费数量
         */
        private Integer count;

        /**
         * 创建时间
         */
        private Date createDate;

        /**
         * 单价
         */
        private BigDecimal price;
        /**
         * 实际收费价格
         */
        private BigDecimal actualFeePrice;
        /**
         * 是否免单
         *
         * @see YesOrNoEnum
         */
        private Integer isFree;
        /**
         * 终止检验状态：0正常，1终止收费，2终止不收费
         *
         * @see StopTestStatus
         */
        private Integer stopStatus;

        /**
         * 终止检验原因code
         */
        private String stopReasonCode;
        /**
         * 终止检验原因value
         */
        private String stopReasonName;
        /**
         * 项目来源:0默认，1微生物费用项目
         *
         * @see ApplySampleItemSourceEnum
         */
        private Integer itemSource;

        /**
         * 外送机构ID
         */
        private Long exportOrgId;

        /**
         * 外送机构名称
         */
        private String exportOrgName;
    }

    /**
     * 报告项目
     */
    @Setter
    @Getter
    public static class ReportItem implements Serializable {
        /**
         * 报告单项目编码
         */
        private String reportItemCode;

        /**
         * 报告项目名称
         */
        private String reportItemName;

        /**
         * 检验项目ID
         */
        private Long testItemId;

        /**
         * 检验项目编码
         */
        private String testItemCode;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 打印顺序
         */
        private Integer printSort;

        /**
         * 结果类型
         */
        private String resultType;

        /**
         * 单位
         */
        private String unit;

        /**
         * 参考范围ID
         */
        private Long instrumentReportItemReferenceId;

    }

}
