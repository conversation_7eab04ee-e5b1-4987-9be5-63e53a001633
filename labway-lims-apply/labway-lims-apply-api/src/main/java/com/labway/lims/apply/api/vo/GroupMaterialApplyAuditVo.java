package com.labway.lims.apply.api.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 专业组物料申请审批
 *
 * <AUTHOR> on 2025/8/12.
 */
@Data
public class GroupMaterialApplyAuditVo implements Serializable {

	/**
	 * 申请单号
	 */
	@NotBlank(message = "请选择申请单")
	private String applyNo;

	/**
	 * 详情
	 */
	@NotNull
	private List<GroupMaterialApplyAuditDetailVo> detailVos;

	/**
	 * 申请单详情
	 */
	@Data
	public static class GroupMaterialApplyAuditDetailVo implements Serializable {

		/**
		 * 详情id
		 */
		private Long detailId;

		/**
		 * 备注信息
		 */
		private String remark;
	}
}
