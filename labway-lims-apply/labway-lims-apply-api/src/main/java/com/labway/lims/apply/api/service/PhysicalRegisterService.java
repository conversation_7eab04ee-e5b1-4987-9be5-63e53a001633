package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.PhysicalRegisterDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;

/**
 * 体检花名册 Service
 * 
 * <AUTHOR>
 * @since 2023/3/30 15:41
 */
public interface PhysicalRegisterService {

    /**
     * 添加 体检人s
     * 
     */
    void addPhysicalRegisters(List<PhysicalRegisterDto> list);

    /**
     * 删除 体检人s
     */
    void deleteByPhysicalBatchId(long physicalBatchId);

    /**
     * 获取 体检人
     */
    List<PhysicalRegisterDto> selectByPhysicalBatchId(long physicalBatchId);

    /**
     * 获取 体检人
     */
    List<PhysicalRegisterDto> selectByPhysicalRegisterIds(Collection<Long> physicalRegisterIds);

    /**
     * 修改 体检人
     */
    void updateByPhysicalBatchId(PhysicalRegisterDto physicalRegisterDto);

    /**
     * 获取 体检人
     */
    @Nullable
    PhysicalRegisterDto selectByPhysicalRegisterId(long physicalRegisterId);

    /**
     * 根据id删除花名册
     * @param physicalRegisterId
     * @return
     */
    boolean deleteById(long physicalRegisterId);
}
