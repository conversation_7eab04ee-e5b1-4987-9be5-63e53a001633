package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 已经二次分拣的申请单样本
 */
@Getter
@Setter
public class TwoPickedApplySampleDto implements Serializable {
    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;


    /**
     * 性别  性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 专业组ID
     */
    private String groupId;

    /**
     * 专业组名称
     */
    private String groupName;


    /**
     * 二次分拣人id
     */
    private Long twoPickerId;

    /**
     * 二次分拣人
     */
    private String twoPickerName;

    /**
     * 二次分拣日期
     */
    private Date twoPickDate;


    /**
     * 样本类型
     */
    private String sampleTypeName;


    /**
     * 管型
     */
    private String tubeName;

    /**
     * 送检机构id
     */
    private Long hspOrgId;


    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 项目类型ID
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 项目状态
     * @see SampleStatusEnum
     */
    private Integer status;

    /**
     * 是否是免疫二次分拣 1：是 0：否
     */
    private Integer isImmunityTwoPick;

}
