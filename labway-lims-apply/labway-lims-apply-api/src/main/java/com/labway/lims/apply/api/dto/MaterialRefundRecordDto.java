package com.labway.lims.apply.api.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class MaterialRefundRecordDto implements Serializable {
    //退库id
    private Long refundId;
    //退库单号
    private String refundNo;
    //入库单号
    private String incomeNo;
    //出库单号
    private String deliveryNo;
    //物料出库详情id
    private Long deliveryDetailId;
    //物料ID
    private Long materialId;
    //物资编号
    private String materialCode;
    //物资名称
    private String materialName;
    //规格
    private String specification;
    //批号
    private String batchNo;
    //厂家
    private String manufacturers;
    //主单位
    private String mainUnit;
    //出库主单位数量
    private BigDecimal deliveryMainNumber;
    //入库主单位数量
    private BigDecimal incomeMainNumber;
    //辅单位
    private String assistUnit;
    //出库辅单位数量
    private BigDecimal deliveryAssistNumber;
    //入库辅单位数量
    private BigDecimal incomeAssistNumber;
    //主辅单位换算率
    private String unitConversionRate;
    //有效期
    private Date validDate;
    //专业组ID
    private Long groupId;
    //专业组名称
    private String groupName;
    //检验机构
    private Long orgId;
    //检验机构名称
    private String orgName;
    //创建时间
    private Date createDate;
    //创建人ID
    private Long creatorId;
    //创建人名称
    private String creatorName;
    //更新时间
    private Date updateDate;
    //更新人ID
    private Long updaterId;
    //更新人名称
    private String updaterName;
    //是否删除 0否1是
    private Integer isDelete;
    //退库单类型 1退库 2拒收
    private Integer refundType;
    // 退库/拒收原因
    private String refundReason;
    // 退库/拒收主数量
    private BigDecimal refundMainNumber;
    // 退库/拒收辅数量
    private BigDecimal refundAssistNumber;
    /**
     * 物料条码号
     */
    private String materialBarcode;
    /**
     * 储存温度
     */
    private String storageTemperature;

}
