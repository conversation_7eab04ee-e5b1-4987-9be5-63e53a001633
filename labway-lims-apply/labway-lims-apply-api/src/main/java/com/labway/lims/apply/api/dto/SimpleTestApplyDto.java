package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * 简单修改申请单信息 不涉及到检验项目
 */
@Getter
@Setter
public class SimpleTestApplyDto extends TestApplyDto{

    /**
     * 申请单样本id
     */
    private Long applySampleId;
    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 修改原因
     */
    private String modifyReason;

    /**
     * 修改原因编码
     */
    private String modifyReasonCode;

    @Override
    public List<Item> getItems() {
        return Collections.emptyList();
    }
}
