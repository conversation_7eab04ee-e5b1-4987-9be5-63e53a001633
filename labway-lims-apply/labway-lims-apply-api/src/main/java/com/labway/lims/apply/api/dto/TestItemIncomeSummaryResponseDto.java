package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

/**
 * 销售项目收入查询--汇总 Dto
 * 
 * <AUTHOR>
 * @since 2023/5/15 10:13
 */
@Setter
@Getter
public class TestItemIncomeSummaryResponseDto implements Serializable {

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客商ID
     */
    private Long customerId;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 数量合计
     */
    private Integer countSum;

    /**
     * 标准收费合计
     */
    private BigDecimal feePriceSum;
    /**
     * 结算金额合计
     */
    private BigDecimal payAmountSum;
    /**
     * 汇总行
     */
    private List<TestItemIncomeSummaryItem> itemList = new LinkedList<>();

    /**
     * 明细
     */
    private TestItemIncomeDetailResponseDto detail;

    /**
     * 汇总单行
     */
    @Setter
    @Getter
    public static class TestItemIncomeSummaryItem implements Serializable {
        /**
         * 客户名称
         */
        private String customerName;
        /**
         * 就诊类型名称
         */
        private String applyTypeName;

        private String applyTypeCode;
        /**
         * 检验项目编码
         */
        private String testItemCode;
        /**
         * 检验项目名称
         */
        private String testItemName;
        /**
         * 数量
         */
        private Integer count;
        /**
         * 收费价格-->标准收费
         */
        private BigDecimal feePrice;
        /**
         * 收费价格-->标准收费 合计
         */
        private BigDecimal feePriceSum;
        /**
         * 折扣率
         */
        private String discount;
        /**
         * 结算金额
         */
        private BigDecimal payAmount;

	    /**
	     * 专业组
	     */
		private String groupName;

    }

}
