package com.labway.lims.apply.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PdaApplyAndItemDto extends PdaApplyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private LinkedList<PdaApplySampleItemDto> pdaApplySampleItemDtoList;

    public void setPdaApplySampleItemDtoList(List<PdaApplySampleItemDto> pdaApplySampleItemDtoList) {
        this.pdaApplySampleItemDtoList = new LinkedList<>(pdaApplySampleItemDtoList);
    }
}
