package com.labway.lims.apply.api.service;

import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.apply.api.dto.SampleFlowDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface SampleFlowService {

    /**
     * 根据申请单ID获取
     */
    List<SampleFlowDto> selectByApplyId(long applyId);

    /**
     * 根据条码号获取
     */
    List<SampleFlowDto> selectByBarcode(String barcode);

    /**
     * 根据申请单样本ID获取
     */
    List<SampleFlowDto> selectByApplySampleId(long applySampleId);

    Map<Long, List<SampleFlowDto>> selectByApplySampleIdsAsMap(Collection<Long> applySampleIds);

    /**
     * 不会返回 content 字段
     */
    Map<Long, List<SampleFlowDto>> selectWithOutContentByApplySampleIdsAsMap(Collection<Long> applySampleIds);

    /**
     * 添加流水
     */
    long addSampleFlow(SampleFlowDto dto);

    /**
     * 根据申请单样本IDs,操作类型获取
     */
    List<SampleFlowDto> selectByApplySampleIdAndOperateCode(Collection<Long> applySampleIds, BarcodeFlowEnum operateCode);

    /**
     * 批量保存
     */
    void addSampleFlows(List<SampleFlowDto> flows);

    /**
     * 复制条码环节
     */
    void copySampleFlows(long sourceApplySampleId, long targetApplySampleId);

    /**
     * 复制条码环节
     */
    void copySampleFlows(long sourceApplySampleId, Collection<Long> targetApplySampleIds);

    /**
     * 根据申请单样本ID删除条码环节记录
     * @param applySampleIds
     */
    void deleteByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据申请单样本ID获取
     */
    List<SampleFlowDto> selectByApplySampleIds(Collection<Long> applySampleIds);

}
