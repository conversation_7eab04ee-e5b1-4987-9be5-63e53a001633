package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 分平台统计 信息 单行 分组 key
 * 
 * <AUTHOR>
 * @since 2023/7/12 14:43
 */
@Setter
@Getter
public class ByPlatformStatisticsKeyDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 财务专业组
     */
    private String financeGroupCode;
    /**
     * 就诊类型 (申请类型)
     */
    private String applyType;
    /**
     * 检验项目ID
     */
    private Long testItemId;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 折扣率
     */
    private BigDecimal discount;

    public ByPlatformStatisticsKeyDto(String financeGroupCode, String applyType, Long testItemId, BigDecimal price,
        BigDecimal discount) {
        this.financeGroupCode = financeGroupCode;
        this.applyType = applyType;
        this.testItemId = testItemId;
        this.price = price;
        this.discount = discount;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof ByPlatformStatisticsKeyDto)) {
            return false;
        }
        ByPlatformStatisticsKeyDto otherKey = (ByPlatformStatisticsKeyDto)obj;
        return Objects.equals(financeGroupCode, otherKey.financeGroupCode)
            && Objects.equals(applyType, otherKey.applyType) && Objects.equals(testItemId, otherKey.testItemId)
            && Objects.equals(price, otherKey.price) && Objects.equals(discount, otherKey.discount);
    }

    @Override
    public int hashCode() {
        return Objects.hash(financeGroupCode, applyType, testItemId, price, discount);
    }

}
