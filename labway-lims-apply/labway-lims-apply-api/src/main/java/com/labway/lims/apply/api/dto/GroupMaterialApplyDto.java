package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.apply.MaterialApplyStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 物料申领单 信息Dto
 *
 * <AUTHOR>
 * @since 2023/5/6 15:10
 */
@Getter
@Setter
public class GroupMaterialApplyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业组物料申领id
     */
    private Long applyId;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 申请人ID
     */
    private Long applyUserId;

    /**
     * 申请人姓名
     */
    private String applyUserName;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 计划单号
     */
    private Long planNo;

    /**
     * 状态
     * 
     * @see MaterialApplyStatusEnum
     */
    private Integer status;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组姓名
     */
    private String groupName;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 审核人
     */
    private String checker;

    /**
     * 审核人ID
     */
    private Long checkerId;

    /**
     * 审核时间
     */
    private Date checkDate;

    /**
     * 退回原因
     */
    private String returnReason;

    /**
     * 申领单明细
     */
    private List<GroupMaterialApplyDetailDto> groupMaterialApplyDetailList;
}
