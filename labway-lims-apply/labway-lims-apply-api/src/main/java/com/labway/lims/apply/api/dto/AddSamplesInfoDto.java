package com.labway.lims.apply.api.dto;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

/**
 *  用于项目加减项目，新增的条码信息
 */
@Getter
@Setter
public class AddSamplesInfoDto {
    // 新增的条码数
    private Integer addCount;

    // 新增的分组
    private Set<String> groupNames;

    public String getGroupNames() {
        return CollectionUtil.join(groupNames,",");
    }
}
