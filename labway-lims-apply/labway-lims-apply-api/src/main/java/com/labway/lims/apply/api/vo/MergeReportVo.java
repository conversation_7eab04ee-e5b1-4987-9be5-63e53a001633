package com.labway.lims.apply.api.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MergeReportVo implements Serializable {

    // 实验室编码
    private String orgCode;

    // 实验室条码
    private List<String> barcodes;

    // 报告单地址
    List<String> reportUrls;

    // 报告结果是否出全部(如果未出全，但是实验室未配置合并打印，则也算是全部出，允许社区同步结果)
    private Integer isReport;


}
