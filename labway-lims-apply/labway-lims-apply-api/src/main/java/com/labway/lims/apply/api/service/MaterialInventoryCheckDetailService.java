package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.MaterialInventoryCheckDetailDto;

import java.util.List;

/**
 * <p>
 * 物料盘点详情 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
public interface MaterialInventoryCheckDetailService {

    /**
     * 添加 物料盘点详情
     */
    void addMaterialInventoryCheckDetailDtos(List<MaterialInventoryCheckDetailDto> list);

    /**
     * 查询 盘点记录详情 根据盘点id
     */
    List<MaterialInventoryCheckDetailDto> selectByCheckId(long checkId);

    /**
     * 物料盘点详情
     */
    void updateByDetailId(MaterialInventoryCheckDetailDto dto);

}
