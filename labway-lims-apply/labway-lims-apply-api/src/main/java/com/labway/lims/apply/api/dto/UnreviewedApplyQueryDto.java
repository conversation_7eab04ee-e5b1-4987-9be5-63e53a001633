package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class UnreviewedApplyQueryDto implements Serializable {

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 状态列表
     */
    private List<Integer> statusList;

    /**
     * 申请单来源
     */
    private String source;

    /**
     * 结束时间
     */
    private Date startDate;

    /**
     * 开始时间
     */
    private Date endDate;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 是否查询已复核的
     */
    private boolean queryChecked = false;
}
