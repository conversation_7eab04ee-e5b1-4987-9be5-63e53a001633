package com.labway.lims.apply.api.dto.es;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 报告单 列表 查看信息 Dto
 * 
 * <AUTHOR>
 * @since 2023/4/18 19:32
 */
@Getter
@Setter
public class SelectReportListDto extends BaseSampleEsModelDto {

    private static final long serialVersionUID = 1L;
    /**
     * 一审人id
     */
    private Long oneCheckerId;
    /**
     * 一审人
     */
    private String oneCheckerName;
    /**
     * 一审时间
     */
    private Date oneCheckDate;
    /**
     * 二审人id
     */
    private Long twoCheckerId;
    /**
     * 二审人
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    private Date twoCheckDate;
    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 报告项目
     */
    private List<RoutineInspectionDto.RoutineReportItem> reportItems;

}
