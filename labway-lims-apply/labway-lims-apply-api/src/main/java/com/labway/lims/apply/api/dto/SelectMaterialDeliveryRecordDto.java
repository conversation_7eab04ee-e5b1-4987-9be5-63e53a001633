package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询 物料出库记录 信息Dto
 *
 * <AUTHOR>
 * @since 2023/5/8 19:23
 */
@Getter
@Setter
public class SelectMaterialDeliveryRecordDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 出库日期 开始
     */
    private Date beginDeliveryDate;

    /**
     * 出库日期 结束
     */
    private Date endDeliveryDate;


    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 是否已入库:0未入库,1已入库
     */
    private Integer status;

    /**
     * 出库单号 精确查询
     */
    private String deliveryNo;

}
