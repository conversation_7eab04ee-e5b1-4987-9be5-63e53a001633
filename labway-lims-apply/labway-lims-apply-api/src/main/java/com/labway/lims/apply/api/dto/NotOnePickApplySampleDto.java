package com.labway.lims.apply.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <pre>
 * NotOnePickApplySampleDto
 * 未分拣样本信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/1/3 16:52
 */
@Data
public class NotOnePickApplySampleDto {

    /**
     * 送检机构
     */
    private String hspOrgName;
    /**
     * 急诊状态
     */
    private String urgent;
    /**
     * 条码号
     */
    private String barcode;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 性别
     */
    private String patientSex;
    /**
     * 年龄
     */
    private String patientAge;
    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 管型
     */
    private String tubeName;
    /**
     * 就诊类型
     */
    private String applyTypeName;
    /**
     * 临床诊断
     */
    private String diagnosis;
    /**
     * 申请时间 (送检时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date applyDate;
    /**
     * 外部条码号
     */
    private String outBarcode;
    /**
     * 主条码号
     */
    private String masterBarcode;

}
