package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 物料出库详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Getter
@Setter
public class MaterialDeliveryDetailDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 详细ID
     */
    private Long detailId;

    /**
     * 出库单号
     */
    private String deliveryNo;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物资编号
     */
    private String materialCode;

    /**
     * 物资名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 厂家
     */
    private String manufacturers;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 出库主单位数量
     */
    private Integer deliveryMainNumber;

    /**
     * 出库主单位数量
     */
    private BigDecimal deliveryMainNumberDecimal;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     * 出库辅单位数量
     */
    private BigDecimal deliveryAssistNumber;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 有效期
     */
    private Date validDate;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;
    /**
     * 物料条码号
     */
    private String materialBarcode;

}
