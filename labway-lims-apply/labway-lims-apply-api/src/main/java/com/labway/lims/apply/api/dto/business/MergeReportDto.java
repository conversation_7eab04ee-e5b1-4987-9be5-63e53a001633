package com.labway.lims.apply.api.dto.business;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MergeReportDto implements Serializable {

    // 实验室编码
    private String orgCode;

    // 实验室条码
    private List<String> barcodes;

    // 报告单地址
    List<String> reportUrls;

    // 是否出全报告单（出全了则进行报告单合并，否则不进行合并） 0否1是
    private Integer isMergerReport;


}
