package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Data
public class DeleteSampleResultDto implements Serializable {
    private final static long serialVersionUID = 1L;

    /**
     * 样本id
     */
    private Long sampleId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 项目类型
     */
    private ItemTypeEnum itemTypeEnum;

    /**
     * 仪器专业组id
     */
    private Long instrumentGroupId;

    /**
     * 样本仪器id
     */
    private Long sampleInstrumentId;

    /**
     * 结果仪器id
     */
    private Long resultInstrumentId;

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 报告项目id
     */
    private Long reportItemId;

    /**
     * 报告项目code
     */
    private String reportItemCode;

    /**
     * 报告项目name
     */
    private String reportItemName;

    /**
     * 结果值
     */
    private String result;

    /**
     * 检验时间（二次分拣时间）
     */
    private Date testDate;

    /**
     * 结果删除时间
     */
    private Date deleteDate;

    /**
     * 是否手工录入结果 0否1是
     */
    private Integer isHandeResult;

}
