package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 销售项目收入查询--汇总 申请单样本信息 分组 key
 * 
 * <AUTHOR>
 * @since 2023/5/16 10:33
 */
@Getter
@Setter
public class TestItemIncomeSummaryKeyDto implements Serializable {

    /**
     * 就诊类型 (申请类型)
     */
    private String applyType;
    /**
     * 检验项目ID
     */
    private Long testItemId;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 折扣率
     */
    private BigDecimal discount;

    public TestItemIncomeSummaryKeyDto(String applyType, Long testItemId, BigDecimal price, BigDecimal discount) {
        this.applyType = applyType;
        this.testItemId = testItemId;
        this.price = price;
        this.discount = discount;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof TestItemIncomeSummaryKeyDto)) {
            return false;
        }
        TestItemIncomeSummaryKeyDto otherKey = (TestItemIncomeSummaryKeyDto)obj;
        return Objects.equals(applyType, otherKey.applyType) && Objects.equals(testItemId, otherKey.testItemId)
            && price.compareTo(otherKey.price)==0 && discount.compareTo(otherKey.discount) == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(applyType, testItemId, price.doubleValue(), discount.doubleValue());
    }

}
