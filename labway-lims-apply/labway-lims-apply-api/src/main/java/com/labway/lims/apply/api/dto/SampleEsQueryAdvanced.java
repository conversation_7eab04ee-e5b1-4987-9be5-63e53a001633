package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * AdvancedEsQueryVo
 * 高级查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/7 19:37
 */
@Getter
@Setter
public class SampleEsQueryAdvanced {

    private Date startSignDate;
    private Date endSignDate;

    /**
     * 检验状态：未复核、已签收、正在检验、完成检验
     * <pre>
     * 1未复核：未复核和未双输复核的数据
     * 2已签收：完成双输复核、复核和样本签收的数据
     * 3正在检验：二次分拣完成且未审核的数据
     * 4完成检验：已审核的数据
     * </pre>
     */
    private Integer status;

    /**
     * 项目状态
     * 0:正常，1:已终止（收费），2:已终止（未收费）,3:禁止
     */
    private Integer itemStatus;

    /**
     * 高级查询 常规字段
     */
    private List<FieldCondition> conditions;

    @Getter
    @Setter
    public static class FieldCondition {
        private String field;

        private String condition;

        private String value;
    }

    public boolean checkParam() {
        return CollectionUtils.isNotEmpty(this.conditions) ||
                Objects.nonNull(status) ||
                Objects.nonNull(itemStatus);
    }
}
