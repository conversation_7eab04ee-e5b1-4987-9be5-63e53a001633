package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Data
public class PdaApplyInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请单ID
     */
    private Long applyId;
    /**
     * 主条码号
     */
    private String masterBarcode;

    /**
     * 检验项目
     */
    private List<PdaTestItem> testItems;

    /**
     * 是否使用外部条码
     */
    private Boolean useOutBarcode;

    @Getter
    @Setter
    public static class PdaTestItem implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 检验项目编码
         */
        private String testItemCode;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 项目类型编码
         *
         * @see ItemTypeEnum
         */
        private String itemType;

    }
}
