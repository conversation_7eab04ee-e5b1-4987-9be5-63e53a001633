package com.labway.lims.apply.api.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ScrollPage<T extends Serializable> extends Page<T> {


    /**
     * search after 分页
     */
    private List<Object> searchAfter;

}
