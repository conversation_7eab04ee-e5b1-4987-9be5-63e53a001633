package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * <pre>
 * SampleAbnormalRegisterRequestDto
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/6/4 16:11
 */
@Getter
@Setter
public class SampleAbnormalRegisterRequestDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;
    /**
     * 检验项目
     */
    private String testItemName;
    /**
     * 送检医生
     */
    private String sendDoctorName;
    /**
     * 名称
     */
    private String patientName;

    /**
     * 性别，1:男，2:女
     */
    private Integer patientSex;

    /**
     * 年龄
     */
    private String patientAge;

    /**
     * 异常原因编码
     */
    private String abnormalReasonCode;
    /**
     * 登记内容
     */
    private String registContent;
    /**
     * 处理部门IDs
     */
    private Set<Long> handleGroupIdList;

    /**
     * 异常图片集合
     */
    private String images;

}
