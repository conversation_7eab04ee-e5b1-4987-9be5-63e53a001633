package com.labway.lims.apply.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialIncomeStaticDto implements Serializable {

    //
    private BigDecimal totalMainNum;

    //
    private BigDecimal totalAssistNum;

    // 入库单列表
    private List<MaterialIncomeRecordDto> materialIncomeRecordDtos;
}
