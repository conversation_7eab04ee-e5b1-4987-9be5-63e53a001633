package com.labway.lims.apply.api.service;

import com.labway.business.center.mdm.api.reagent.dto.SysDictDto;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.dto.SampleAbnormalRegisterRequestDto;
import com.labway.lims.apply.api.dto.SampleAbnormalStatisticsDto;
import com.labway.lims.apply.api.dto.SelectSampleAbnormalDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 样本异常 Service
 *
 * <AUTHOR>
 * @since 2023/4/12 11:17
 */
public interface SampleAbnormalService {

    /**
     * 样本异常登记
     */
    List<Long> sampleAbnormalRegister(SampleAbnormalRegisterRequestDto requestDto);

    /**
     * 获取异常字典
     */
    List<SysDictDto> getAbnormalReasonList();

    /**
     * 添加 异常
     */
    void addSampleAbnormals(List<SampleAbnormalDto> list);

    /**
     * 查询 样本异常 信息 根据 查询参数 SampleCriticalResultDto
     */
    List<SampleAbnormalDto> selectBySelectSampleAbnormalDto(SelectSampleAbnormalDto dto);

    /**
     * 查询异常 根据id
     */
    @Nullable
    SampleAbnormalDto selectBySampleAbnormalId(long sampleAbnormalId);

    /**
     * 修改 异常
     */
    void updateBySampleAbnormalId(SampleAbnormalDto sampleAbnormalDto);

    /**
     * 修改 异常
     */
    void updateBySampleAbnormalIds(SampleAbnormalDto sampleAbnormalDto, Collection<Long> sampleAbnormalId);

    /**
     * 查询异常 根据ids
     */
    List<SampleAbnormalDto> selectBySampleAbnormalIds(Collection<Long> sampleAbnormalIds);

    /**
     * 根据签收日期查询
     *
     * @param beginSignDate 签收日期开始
     * @param endSignDate 签收日期结束
     * @param orgId 机构ID
     */
    List<SampleAbnormalStatisticsDto> selectSampleAbnormalStatistics(Date beginSignDate, Date endSignDate, Long orgId);

    /**
     * 根据签收日期查询
     *
     * @param beginSendDate 日期开始
     * @param endSendDate 日期结束
     * @param orgId 机构ID
     */
    List<SampleAbnormalStatisticsDto> selectSampleAbnormalStatisticsBySendDate(Date beginSendDate, Date endSendDate, Long orgId);

    /**
     * 查看 处理部门或 确认部门 为传入专业组的 异常
     */
    List<SampleAbnormalDto> selectByGroupId(long groupId);

    /**
     *  根据applyId 修改送检机构
     * @param sampleAbnormalDto
     */
    void updateByApplyId(SampleAbnormalDto sampleAbnormalDto);

    void updateByApplyIds(SampleAbnormalDto sampleAbnormalDto, Collection<Long> applyIds);

    /**
     * 添加条码环节
     */
    void addSampleFlow(List<SampleAbnormalDto> sampleAbnormalDtos, BarcodeFlowEnum barcodeFlow, String content, LoginUserHandler.User user);

    /**
     * 根据条码选出异常登录信息
     * @param barcodes
     * @return
     */
    List<SampleAbnormalDto> selectByBarcodes(List<String> barcodes);
}
