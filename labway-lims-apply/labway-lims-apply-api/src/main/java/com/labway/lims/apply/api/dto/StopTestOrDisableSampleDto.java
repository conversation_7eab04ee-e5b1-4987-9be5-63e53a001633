package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class StopTestOrDisableSampleDto implements Serializable {
    /**
     * 样本id
     */
    private List<Long> applySampleIds;
    /**
     * 原因code
     */
    private String causeCode;
    /**
     * 原因
     */
    private String cause;
    /**
     * 是否为并单样本
     */
    private Boolean isCombinedBill = false;
}
