package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 打印样本号
 * 
 * <AUTHOR>
 * @since 2023/7/21 15:11
 */
@Getter
@Setter
public class PrintSampleNoInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 检验项目
     */
    private List<String> testItemNames;
    /**
     * 样本号
     */
    private String sampleNo;
    /**
     * 条码号
     */
    private String barcode;
    /**
     * 二次分拣时间
     */
    private Date twoPickDate;
    /**
     * 样本类型
     */
    private String sampleTypeName;
}
