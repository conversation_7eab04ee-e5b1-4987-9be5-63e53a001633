package com.labway.lims.apply.api.dto;

import cn.hutool.core.lang.Assert;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class UploadPdfDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long applySampleId;

    private String url;

    public void verifyParams(){
        Assert.notNull(applySampleId,"申请单样本不能为空");
        if(StringUtils.isBlank(url)){
            throw new IllegalArgumentException("上传文件不能为空");
        }
    }
}
