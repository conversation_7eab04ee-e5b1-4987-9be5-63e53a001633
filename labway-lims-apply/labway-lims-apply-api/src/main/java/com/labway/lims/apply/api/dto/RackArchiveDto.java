package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 试管架归档 Dto
 * 
 * <AUTHOR>
 * @since 2023/4/13 15:09
 */
@Getter
@Setter
public class RackArchiveDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long rackArchiveId;
    /**
     * 试管架id
     */
    private Long rackId;
    /**
     * 试管架名称
     */
    private String rackName;
    /**
     * 逻辑试管架id
     */
    private Long rackLogicId;
    /**
     * 冰箱ID
     */
    private Long refrigeratorId;
    /**
     * 有效时间从
     */
    private Date startEffectiveDate;
    /**
     * 有效时间至
     */
    private Date endEffectiveDate;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 1 已删除 0 未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新人
     */
    private String updaterName;
}
