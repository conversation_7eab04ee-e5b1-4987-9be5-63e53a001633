package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 等待分血的样本
 */
@Getter
@Setter
public class WaitingSplitBloodApplySampleDto implements Serializable {

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 逻辑试管架ID
     */
    private Long rackLogicId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 样本类型
     */
    private String sampleTypeCode;

    /**
     * 管型
     */
    private String tubeCode;

    /**
     * 管型
     */
    private String tubeName;

    /**
     * 病人名称
     */
    private String patientName;


    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;


    /**
     * 一次分拣日期
     */
    private Date onePickDate;


    /**
     * 送检机构
     */
    private String hspOrgName;
}
