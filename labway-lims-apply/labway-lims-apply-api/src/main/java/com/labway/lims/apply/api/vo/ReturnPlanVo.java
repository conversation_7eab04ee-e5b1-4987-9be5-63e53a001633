package com.labway.lims.apply.api.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 物料申领单 信息Dto
 *
 * <AUTHOR>
 * @since 2023/5/6 15:10
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ReturnPlanVo implements Serializable {

    private static final long serialVersionUID = 1L;

    // 专业组计划id
    private String planNo;


}
