package com.labway.lims.apply.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.ApplySupplierEnum;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 申请单信息
 */
@Getter
@Setter
public abstract class TestApplyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 就诊类型编码
     */
    private String applyTypeCode;

    /**
     * 就诊类型 这个是编码
     */
    private String applyTypeName;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date patientBirthday;


    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 急诊类型
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 样本个数
     */
    private Integer sampleCount;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 样本性状code
     */
    private String samplePropertyCode;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date applyDate;

    /**
     * 采样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date samplingDate;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 科室
     */
    private String dept;

    /***
     * 床号
     */
    private String patientBed;

    /**
     * 临床诊断
     */
    private String clinicalDiagnosis;

    /**
     * 临床诊断
     */
    private String diagnosis;

    /**
     * 备注
     */
    private String remark;

    /**
     * 手机号
     */
    private String patientMobile;

    /**
     * 身份证号
     */
    private String patientCard;

    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 证件类型
     *
     * @see PatientCardTypeEnum
     */
    private String patientCardType;
    /**
     * 送检医生
     */
    private String sendDoctor;
    /**
     * 送检医生
     */
    private String sendDoctorCode;
    /**
     * 送检医生
     */
    private String sendDoctorName;
    /**
     * 地址
     */
    private String patientAddress;
    /**
     * 申请单来源
     */
    private ApplySourceEnum applySource;

    /**
     * 提供商 如果是 签收 就有可能有很多的提供商 例如：东软HIS，社区LIS 。。。。。
     * @see ApplySupplierEnum
     */
    private String supplier;

    /**
     * 申请单状态
     * @see ApplyStatusEnum
     */
    private Integer status;
    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;

    /**
     * his流水号
     */
    private String hisSerialNo;

    /**
     * 检验项目信息
     */
    public abstract List<Item> getItems();

    /**
     * 是否忽略同人同天同项目校验 0否1是
     */
    private Integer ignoreSameItem;

    /**
     * 是否忽略检验项目限制性别校验 true:忽略 false:校验
     */
    private Boolean ignoreItemLimitSex;

    @Getter
    @Setter
    public static class Item {

        /**
         * 检验项目id
         */
        private Long testItemId;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 收费数量
         */
        private Integer count;

        /**
         * 急诊类型
         * @see UrgentEnum
         */
        private Integer urgent;

        /**
         * 管型 code
         */
        private String tubeCode;

        /**
         * 管型
         */
        private String tubeName;

        /**
         * 样本类型
         */
        private String sampleTypeCode;

        /**
         * 样本类型名称
         */
        private String sampleTypeName;

        /**
         * 自定义码
         */
        private String customCode;

        /**
         * 备注
         */
        private String remark;

        /**
         * 血培养
         */
        private ApplySampleItemBloodCultureDto bloodCulture;
    }

    public void setTwoRemark(String hisRemark, String remark) {
        if (StringUtils.isNotBlank(hisRemark) && StringUtils.isNotBlank(remark)) {
            this.remark = hisRemark + "," + remark;
        } else if (StringUtils.isNotBlank(hisRemark)) {
            this.remark = hisRemark;
        } else if (StringUtils.isNotBlank(remark)) {
            this.remark = remark;
        } else {
            this.remark = Strings.EMPTY;
        }
    }

}
