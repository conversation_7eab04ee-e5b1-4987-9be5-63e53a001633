package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySampleItemSourceEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 检验项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class ApplySampleItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long applySampleItemId;

    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 项目类型
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 外部项目
     */
    private Long outTestItemId;

    /**
     * 外部项目编码
     */
    private String outTestItemCode;

    /**
     * 外部项目名称
     */
    private String outTestItemName;

    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 管型名称
     */
    private String tubeName;
    /**
     * 管型CODE
     */
    private String tubeCode;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;
    /**
     * @see com.labway.lims.api.enums.apply.UrgentEnum
     */
    private Integer urgent;
    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 收费数量
     */
    private Integer count;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 1:已经删除 0:未删除
     *
     * @see YesOrNoEnum
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 自定义码 | 分割码
     */
    private String splitCode;

    /**
     * 是否外送项目，1是，0不是
     *
     * @see YesOrNoEnum
     */
    private Integer isOutsourcing;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

    /**
     * 外送机构名称
     */
    private String exportOrgName;

    /**
     * 是否免单
     *
     * @see YesOrNoEnum
     */
    private Integer isFree;
    /**
     * 项目单价
     */
    private BigDecimal feePrice;
    /**
     * 实际收费价格
     */
    private BigDecimal actualFeePrice;

    /**
     * 终止检验状态：0正常，1终止收费，2终止不收费
     *
     * @see StopTestStatus
     */
    private Integer stopStatus;

    /**
     * 终止检验原因code
     */
    private String stopReasonCode;
    /**
     * 终止检验原因value
     */
    private String stopReasonName;
    /**
     * 项目来源:0默认，1微生物费用项目
     *
     * @see ApplySampleItemSourceEnum
     */
    private Integer itemSource;

    /**
     * 申请单样本项目是否禁用：1：禁用，0：正常
     */
    private Integer isDisabled;

}
