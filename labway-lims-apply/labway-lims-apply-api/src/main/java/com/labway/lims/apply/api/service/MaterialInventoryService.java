package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.MaterialInventoryDto;
import com.labway.lims.apply.api.dto.UpdateInventoryLimitDto;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 物料库存 Service
 * 
 * <AUTHOR>
 * @since 2023/5/8 17:12
 */
public interface MaterialInventoryService {

    /**
     * 获取对应专业组下 物料
     * 
     * @param groupId 专业组
     */
    List<MaterialInventoryDto> selectByGroupId(long groupId);

    /**
     * 根据 库存ids 查看 库存信息
     * 
     */
    List<MaterialInventoryDto> selectByInventoryIds(Collection<Long> inventoryIds);

    /**
     * 修改 库存信息
     */
    void updateByInventoryId(MaterialInventoryDto dto);

    /**
     * 根据 库存id 查看 库存信息
     */
    MaterialInventoryDto selectByInventoryId(long inventoryId);

    /**
     * 查询 专业组 下此相关 物料 信息
     * 
     * @param groupId 专业组
     * @param materialIds 物料ids
     * @param batchNos 批号
     * @return
     */
    List<MaterialInventoryDto> selectByGroupIdAndMaterialIds(long groupId, Collection<Long> materialIds,
        Collection<String> batchNos);

    /**
     * 添加 物料库存信息
     */
    void addMaterialInventoryDtos(List<MaterialInventoryDto> list);

     /**
     *  根据 物料id和批号和机构id去查询库存信息
     * @return
     */
   List<MaterialInventoryDto> selectByMaterialIds(Set<Long> materialIds, Long orgId);

    /**
     * 更新物料上下限
     */
    void updateInventoryUpperLimitAndLowerLimit(List<UpdateInventoryLimitDto> dtos);
}
