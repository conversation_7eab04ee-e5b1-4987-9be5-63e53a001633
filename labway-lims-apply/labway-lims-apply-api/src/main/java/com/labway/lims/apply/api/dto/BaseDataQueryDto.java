package com.labway.lims.apply.api.dto;

import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;

/**
 * 统计查询条码 base vo
 */
@Getter
@Setter
public class BaseDataQueryDto {
    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 确认状态 false 未确认  true 确认
     */
    private Boolean confirmed;

    /**
     * 默认当天
     */
    public void defaultDate() {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            final Date date = new Date();
            startDate = DateUtil.beginOfDay(date);
            endDate = DateUtil.endOfDay(date);
        }

    }
}
