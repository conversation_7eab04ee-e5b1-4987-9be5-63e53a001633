package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.HandoverRecordQueryDto;
import com.labway.lims.apply.api.dto.PreprocessingHandoverRecordDto;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface PreprocessingHandoverRecordService {

    /**
     * 前处理交接记录
     *
     * @return
     */
    List<PreprocessingHandoverRecordDto> handoverRecords(HandoverRecordQueryDto handoverRecordQueryDto);

    void addBatch(List<PreprocessingHandoverRecordDto> records);

    /**
     * 根据交接id删除
     */
    void deleteByHandoverIds(Collection<Long> ids);

    /**
     * 根据条码查询
     */
    List<PreprocessingHandoverRecordDto> selectByBarcode(String barcode,String hspOrgCode);

    /**
     * 根据id查询
     */
    List<PreprocessingHandoverRecordDto> selectByIds(List<Long> handoverIds);

    /**
     * 根据条码删除
     */
    void deleteByHspOrgCodeAndBarcodes(String hspOrgCode, Set<String> barcodes);
}
