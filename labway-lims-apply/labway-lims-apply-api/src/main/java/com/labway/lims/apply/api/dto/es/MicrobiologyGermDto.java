package com.labway.lims.apply.api.dto.es;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 微生物细菌
 */
@Getter
@Setter
public class MicrobiologyGermDto implements Serializable {
    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 细菌id
     */
    private Long germId;

    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 细菌菌属ID
     */
    private Long germGenusId;

    /**
     * 细菌菌属编码
     */
    private String germGenusCode;

    /**
     * 细菌名称
     */
    private String germName;

    /**
     * 细菌数量
     */
    private String germCount;

    /**
     * 细菌备注
     */
    private String germRemark;
    /**
     * 细菌备注
     */
    private String germRemarkCode;
    /**
     * 检验方法
     */
    private String testMethod;

    /**
     * 药物
     */
    private List<MicrobiologyMedicineDto> medicines;

}
