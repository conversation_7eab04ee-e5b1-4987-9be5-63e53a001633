package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 已分血样本
 */
@Getter
@Setter
public class SplitBloodApplySampleDto implements Serializable {

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组
     */
    private String groupName;

    /**
     * 管型
     */
    private String tubeName;
    /**
     * 管型CODE
     */
    private String tubeCode;

    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;


    /**
     * 分血人ID
     */
    private Long splitterId;

    /**
     * 分血人
     */
    private String splitterName;

    /**
     * 分血日期
     */
    private Date splitDate;

    /**
     * 年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 试管架编码
     */
    private String rackCode;

    /**
     * 逻辑试管架ID
     */
    private Long rackLogicId;

    /**
     * 下一个专业组ID
     */
    private Long nextGroupId;

    /**
     * 下一个专业组名称
     */
    private String nextGroupName;

    /**
     * 操作时间
     */
    private Date operateDate;
}
