package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询 物料入库记录信息 信息Dto
 *
 * <AUTHOR>
 * @since 2023/5/8 19:23
 */
@Getter
@Setter
public class SelectMaterialIncomeInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 入库日期 开始
     */
    private Date beginIncomeDate;

    /**
     * 入库日期 结束
     */
    private Date endIncomeDate;


    /**
     * 专业组
     */
    private Long groupId;
    /**
     * 是否已入库:0未入库,1已入库
     */
    private Integer status;
    /**
     * 入库单号
     */
    private String incomeNo;


}
