package com.labway.lims.apply.api.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class ApprovalPlanDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 专业组计划单号
     */
    @NotNull(message = "专业组计划id不能为空")
    private String planNo;

	private List<ApprovalPlanDetail> approvalPlanDetails;

	@Data
	public static class ApprovalPlanDetail implements Serializable {
		private static final long serialVersionUID = 1L;
		/**
		 * 详情id
		 */
		@NotNull(message = "请选择明细")
		private Long groupMaterialPlanId;

		private String remark;
	}
}
