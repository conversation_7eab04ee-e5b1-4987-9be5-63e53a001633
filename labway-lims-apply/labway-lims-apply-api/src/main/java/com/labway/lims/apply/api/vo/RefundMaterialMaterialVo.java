package com.labway.lims.apply.api.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class RefundMaterialMaterialVo implements Serializable {

    //入库单ID
    @NotNull(message = "入库单id不能为空！")
    private Long incomeId;
    //入库单号
    @NotBlank(message = "入库单号不能为空！")
    private String incomeNo;

    //物料ID
    @NotNull(message = "物料id不能为空！")
    private Long materialId;
    //物资编号
    @NotBlank(message = "物料编码不能为空！")
    private String materialCode;
    //物资名称
    @NotBlank(message = "物料名称不能为空！")
    private String materialName;

    //退库单类型 1退库 2拒收
    @NotNull(message = "退库类型不能为空！")
    private Integer refundType;
    // 退库原因
    @NotBlank(message = "退库原因不能为空！")
    private String refundReason;
    // 退库主数量
    @NotNull(message = "退库主数量不能为空！")
    @DecimalMin(value = "0.0001",message = "退库主数量不能小于0.0001！")
    private BigDecimal refundMainNumber;
    // 退库辅数量
    @NotNull(message = "退库辅数量不能为空！")
    @Min(value = 0,message = "退库辅数量必须大于0！")
    private BigDecimal refundAssistNumber;

    /**
     * 批号
     */
    private String batchNo;

}
