package com.labway.lims.apply.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class DeleteSampleResultDetailDto implements Serializable {
    private final static long serialVersionUID = 1L;

    /**
     * 删除的样本结果详情id
     */
    private Long deleteSampleResultDetailId;

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 报告项目id
     */
    private Long reportItemId;

    /**
     * 报告项目code
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 结果
     */
    private String result;

    /**
     * 仪器
     */
    private Long instrumentId;

    /**
     * 是否手工录入结果 0否1是
     */
    private Integer isHandeResult;

    /**
     * 英文简称
     */
    private String enName;

    /**
     * 创建人
     */
    private Long createId;
    private String createName;
    private Date createDate;

}
