package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Data
public class BatchTestItemDto {


    /**
     * 条码号
     */
    private Set<String> barcodes;


    /**
     * 要添加或者删除的项目
     */
    private List<Item> items;

    /**
     *  是否是 + 项
     */
    private Boolean isAddItem;


    @Getter
    @Setter
    public static class Item {

        /**
         * 检验项目id
         */
        private Long testItemId;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 收费数量
         */
        private Integer count;

        /**
         * 急诊类型
         *
         * @see UrgentEnum
         */
        private Integer urgent;

        /**
         *  样本类型编码
         */
        private String sampleTypeCode;

        /**
         *  样本类型名称
         */
        private String sampleTypeName;

        /**
         * 管型 code
         */
        private String tubeCode;

        /**
         * 管型
         */
        private String tubeName;

    }

    public void verifyAddParams(){
        if(CollectionUtils.isEmpty(barcodes)){
            throw new IllegalArgumentException("条码不能为空");
        }
        if(CollectionUtils.isEmpty(items)){
            throw new IllegalArgumentException("项目不能为空不能为空");
        }
        Map<String, List<Item>> map = items.stream().collect(Collectors.groupingBy(e -> e.getTubeCode() + "-" + e.getSampleTypeCode()));
        if(map.size() > 1){
            throw new IllegalArgumentException("添加的项目必须样本类型和管型一致");
        }
    }
}
