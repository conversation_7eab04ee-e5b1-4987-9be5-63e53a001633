<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.labway.lims</groupId>
        <artifactId>labway-lims-apply</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>labway-lims-apply-api</artifactId>
    <packaging>jar</packaging>

    <name>labway-lims-apply-api</name>

    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.labway.lims</groupId>
            <artifactId>labway-lims-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.labway.lims</groupId>
            <artifactId>labway-lims-specialty-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.labway</groupId>
            <artifactId>business-center-mdm-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.labway.lims</groupId>
            <artifactId>labway-lims-pdfreport-api</artifactId>
        </dependency>
    </dependencies>
</project>
