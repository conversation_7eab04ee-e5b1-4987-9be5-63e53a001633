package com.labway.lims.pdfreport.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 模版修改历史
 */
@Getter
@Setter
public class ReportTemplateHistoryDto {

    /**
     * ID
     */
    private Long reportTemplateHistoryId;

    /**
     * 模板ID
     */
    private Long reportTemplateId;


    /**
     * 内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createDate;


    /**
     * 是否删除
     *
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer isDelete;

}
