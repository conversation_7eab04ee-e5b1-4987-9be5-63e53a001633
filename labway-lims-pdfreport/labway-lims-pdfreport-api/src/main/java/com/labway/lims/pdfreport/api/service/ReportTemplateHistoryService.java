package com.labway.lims.pdfreport.api.service;

import com.labway.lims.pdfreport.api.dto.ReportTemplateHistoryDto;

import javax.annotation.Nullable;
import java.util.List;

/**
 * service
 */
public interface ReportTemplateHistoryService {

    /**
     * 新增
     */
    long addReportTemplateHistory(ReportTemplateHistoryDto reportTemplateHistory);

    /**
     * 根据 reportTemplateId 查询
     */
    List<ReportTemplateHistoryDto> selectByReportTemplateId(long reportTemplateId);

    /**
     * 根据 reportTemplateId 查询 , content 字段为空
     */
    List<ReportTemplateHistoryDto> selectNonContentByReportTemplateId(long reportTemplateId);

    /**
     * 根据ID查询
     */
    @Nullable
    ReportTemplateHistoryDto selectByReportTemplateHistoryId(long reportTemplateHistoryId);
}
