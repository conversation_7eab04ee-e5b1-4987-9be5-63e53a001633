package com.labway.lims.pdfreport.api.vo.ca;

import lombok.Data;

import java.io.Serializable;

/**
 * 印章信息
 */
@Data
public class CASealVo implements Serializable {

    /**
     * 印章id
     */
    private String id;

    /**
     * 印章名称
     */
    private String sealName;

    /**
     * 印章类型
     * @see com.labway.lims.pdfreport.api.enums.SealTypeEnum
     */
    private String sealType;

    /**
     * 文件类型
     * png, jpg 等
     */
    private String fileType;

    /**
     * rsa 证书印章
     */
    private Boolean rsaCert;

    /**
     * sm2 证书印章
     */
    private Boolean sm2Cert;

    /**
     * rsa证书base64
     */
    private String rsaCertContent;

    /**
     * sm2证书base64
     */
    private String sm2CertContent;

    /**
     * 印章图片base64
     */
    private String pictureContent;
}
