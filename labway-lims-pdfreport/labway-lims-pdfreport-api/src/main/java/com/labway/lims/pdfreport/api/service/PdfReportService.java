package com.labway.lims.pdfreport.api.service;

import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.dto.ReportTemplateDto;

import java.util.Collection;
import java.util.List;

/**
 * 报告单生成
 */
public interface PdfReportService {

    /**
     * 生成 pdf
     *
     * @param code 编码
     * @param pdfReportParam 参数
     * @return pdf
     */
    byte[] build(String code, PdfReportParamDto pdfReportParam);

    /**
     * 生成 pdf
     *
     * @param code 编码
     * @param pdfReportParam 参数
     * @return pdf url
     */
    String build2Url(String code, PdfReportParamDto pdfReportParam);

    /**
     * 生成 pdf
     *
     * @param code 编码
     * @param pdfReportParam 参数
     * @param expires 过期时间，天数。如果是 1 那么 1 天后对象会被清理掉，-1 表示永久。通常临时生成的文件会设置一个过期时间。
     * @return pdf url
     */
    String build2Url(String code, PdfReportParamDto pdfReportParam, int expires);

    /**
     * 获取到所有模板
     */
    List<ReportTemplateDto> selectAllTemplate();

    /**
     * 合并pdf
     *
     * @param pdfList 多个pdf字节数组
     * @return 合并后的pdf字节数组
     */
    byte[] mergePdf(Collection<byte[]> pdfList);

    /**
     * 合并pdf
     *
     * @param pdfList 多个pdf字节数组
     * @return 合并后的pdf字节数组
     */
    String mergePdf2Url(Collection<byte[]> pdfList);

    /**
     * 合并pdf
     *
     * @param pdfList 多个pdf字节数组
     * @param expires 过期时间，天数。如果是 1 那么 1 天后对象会被清理掉，-1 表示永久。通常临时生成的文件会设置一个过期时间。
     * @return 合并后的pdf字节数组
     */
    String mergePdf2Url(Collection<byte[]> pdfList, int expires);

	/**
	 * 合并pdf
	 *
	 * @param pdfUrlList 多个pdf oss文件地址
	 * @return 合并后的pdf字节数组
	 */
	byte[] mergePdfByUrls(Collection<String> pdfUrlList);

	/**
	 * 合并pdf
	 *
	 * @param pdfList 多个pdf oss文件地址
	 * @return 合并后的pdf oss地址
	 */
	String mergePdfByUrls2Url(Collection<String> pdfList);

	/**
	 * 合并pdf
	 *
	 * @param pdfList 多个pdf oss文件地址
	 * @param expires 过期时间，天数。如果是 1 那么 1 天后对象会被清理掉，-1 表示永久。通常临时生成的文件会设置一个过期时间。
	 * @return 合并后的pdf oss地址
	 */
	String mergePdfByUrls2Url(Collection<String> pdfList, int expires);
}
