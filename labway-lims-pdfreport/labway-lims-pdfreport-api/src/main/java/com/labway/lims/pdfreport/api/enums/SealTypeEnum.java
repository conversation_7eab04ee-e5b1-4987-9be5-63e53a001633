package com.labway.lims.pdfreport.api.enums;

import lombok.Getter;

/**
 * ca 认证签章类型
 */
@Getter
public enum SealTypeEnum {

    OFFICIAL_SEAL(0, "公章"),
    FINANCE_SEAL(1, "财务章"),
    TAX_SEAL(2, "发票章"),
    LEGAL_REPRESENTATIVE(3, "法人章"),
    CONTRACT_SEAL(4, "合同章"),
    OTHER(5, "其他"),
    PERSION_SEAL(6, "个人民章"),

    ;


    private final int code;

    private final String type;

    SealTypeEnum(int code, String type) {
        this.code = code;
        this.type = type;
    }
}
