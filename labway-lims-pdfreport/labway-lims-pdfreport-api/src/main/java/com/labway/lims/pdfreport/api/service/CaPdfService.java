package com.labway.lims.pdfreport.api.service;

import com.labway.lims.pdfreport.api.dto.ca.CAPdfSignDto;
import com.labway.lims.pdfreport.api.dto.ca.CAPingDto;
import com.labway.lims.pdfreport.api.dto.ca.PdfTemplateCreateDto;
import com.labway.lims.pdfreport.api.vo.ca.CAPdfSignVo;
import com.labway.lims.pdfreport.api.vo.ca.CAPdfTemplateVo;
import com.labway.lims.pdfreport.api.vo.ca.CASealVo;

import javax.annotation.Nullable;
import java.io.IOException;
import java.util.List;

/**
 * CA 认证， pdf打印
 * <AUTHOR>
 * @Date 2324-01-11
 */
public interface CaPdfService {

    /**
     * 获取心跳
     */
    CAPingDto ping();

    /**
     * 获取可用章
     * @param sealName 印章名， 模糊搜索， 可以为空
     */
    List<CASealVo> selectSealList(@Nullable String sealName) throws IOException ;


    /**
     * 获取可用章
     * @param sealName 印章名， 模糊搜索， 可以为空
     */
    List<CASealVo> selectSeal(@Nullable String sealName) throws IOException ;


    /**
     * pdf 静默签署只能签一个文件
     */
    CAPdfSignVo pdfQuiesceSign(CAPdfSignDto caPdfSignDto) throws IOException ;

    /**
     * 文件下载  两个参数都有时 以文件id为参数进行文件查询下载
     * 注意： 静默授权只能签一个文件
     * @param envelopeId 签署id
     * @param fileId 文件id
     * @return byteArray
     */
    byte[] download(String envelopeId, String fileId) throws IOException;


    /**
     * 获取签署模板
     * @return
     */
    List<CAPdfTemplateVo> selectPdfTemplate();

    /**
     * 生成签署模板文件
     * @param dto
     * @return base64
     */
    String createTemplatePdf(PdfTemplateCreateDto dto);

}
