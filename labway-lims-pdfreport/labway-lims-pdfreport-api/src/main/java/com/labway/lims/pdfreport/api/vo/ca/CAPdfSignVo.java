package com.labway.lims.pdfreport.api.vo.ca;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * pdf签署信息
 */
@Data
public class CAPdfSignVo implements Serializable {

    /**
     * 签署id
     */
    private String envelopeId;

    /**
     * 签署文件信息
     */
    private List<SignFile> signFiles;

    @Data
    public static class SignFile implements Serializable{
        /**
         * 文件id
         */
        private Long fileId;
        /**
         * 文件名
         */
        private String fileName;
        /**
         * 0  文件待签署  静默签署没有待签署状态
         * 1  文件签署成功
         * 2 文件签署失败
         */
        private Integer fileStatus;
        /**
         * 文件签署信息  success
         */
        private String signMessage;
    }
}
