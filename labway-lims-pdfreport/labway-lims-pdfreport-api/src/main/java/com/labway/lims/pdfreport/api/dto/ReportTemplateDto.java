package com.labway.lims.pdfreport.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 模版
 */
@Getter
@Setter
public class ReportTemplateDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long reportTemplateId;

    /**
     * 编码
     */
    private String reportTemplateCode;

    /**
     * 名称
     */
    private String reportTemplateName;

    /**
     * 内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 是否删除
     * @see  com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer isDelete;

}
