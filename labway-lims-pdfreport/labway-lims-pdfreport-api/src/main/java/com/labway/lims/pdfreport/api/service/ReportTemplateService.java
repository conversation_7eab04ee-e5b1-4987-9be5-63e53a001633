package com.labway.lims.pdfreport.api.service;

import com.labway.lims.pdfreport.api.dto.ReportTemplateDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * service
 */
public interface ReportTemplateService {

    /**
     * 获取所有模板
     */
    List<ReportTemplateDto> selectAll();

    /**
     * 更新ID修改
     */
    boolean updateByReportTemplateId(ReportTemplateDto reportTemplate);

    /**
     * 新增
     */
    long addReportTemplate(ReportTemplateDto reportTemplate);

    /**
     * 根据ID删除
     */
    void deleteByReportTemplateId(long reportTemplateId);

    /**
     * 根据 id 查询
     */
    @Nullable
    ReportTemplateDto selectByReportTemplateId(long reportTemplateId);

    /**
     * 根据 code 查询
     */
    @Nullable
    ReportTemplateDto selectByReportTemplateCode(String reportTemplateCode);

    Map<String, List<ReportTemplateDto>> selectByReportTemplateCodes(Collection<String> reportTemplateCodes);
}
