package com.labway.lims.pdfreport.api.dto.ca;

import lombok.*;

import java.io.Serializable;

/**
 * 可用印章查询
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class CASealDto extends CAKeyDto implements Serializable {

    /**
     * 印章名称
     */
    private String sealName;

    public CASealDto(){
        super(null, null);
    }

    public CASealDto(String appKey, String appSecret){
        super(appKey, appSecret);
    }

}
