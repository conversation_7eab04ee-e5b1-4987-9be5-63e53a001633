<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.labway.lims</groupId>
        <artifactId>labway-lims-pdfreport</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>labway-lims-pdfreport-server</artifactId>
    <packaging>jar</packaging>

    <name>labway-lims-pdfreport-server</name>

    <properties>
        <jxbrowser.version>7.33-SNAPSHOT</jxbrowser.version>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.mweirauch</groupId>
            <artifactId>micrometer-jvm-extras</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>font-asian</artifactId>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>sign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>barcodes</artifactId>
        </dependency>

        <dependency>
            <groupId>com.j2html</groupId>
            <artifactId>j2html</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>


        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>


        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>


        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>


        <dependency>
            <groupId>com.labway.lims</groupId>
            <artifactId>labway-lims-pdfreport-api</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.labway.lims</groupId>
            <artifactId>labway-lims-base-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.labway.lims</groupId>
            <artifactId>labway-lims-routine-api</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-chain</groupId>
            <artifactId>commons-chain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.teamdev.jxbrowser</groupId>
            <artifactId>jxbrowser</artifactId>
            <version>${jxbrowser.version}</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <scope>test</scope>
        </dependency>


        <dependency>
            <groupId>com.labway.lims</groupId>
            <artifactId>labway-lims-apply-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.labway.lims</groupId>
            <artifactId>labway-lims-outsourcing-api</artifactId>
        </dependency>


        <dependency>
            <groupId>com.labway.lims</groupId>
            <artifactId>labway-lims-microbiology-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.labway.lims</groupId>
            <artifactId>labway-lims-genetics-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.labway.lims</groupId>
            <artifactId>labway-lims-infection-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>win32-x64</id>
            <activation>
                <os>
                    <family>windows</family>
                    <arch>amd64</arch>
                </os>
            </activation>
            <dependencies>
                <dependency>
                    <artifactId>jxbrowser-win64</artifactId>
                    <groupId>com.teamdev.jxbrowser</groupId>
                    <version>${jxbrowser.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>win32-x86</id>
            <activation>
                <os>
                    <family>windows</family>
                    <arch>x86</arch>
                </os>
            </activation>
            <dependencies>
                <dependency>
                    <artifactId>jxbrowser-win32</artifactId>
                    <groupId>com.teamdev.jxbrowser</groupId>
                    <version>${jxbrowser.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>mac-arm</id>
            <activation>
                <os>
                    <family>mac</family>
                    <arch>aarch64</arch>
                </os>
            </activation>
            <dependencies>
                <dependency>
                    <artifactId>jxbrowser-mac-arm</artifactId>
                    <groupId>com.teamdev.jxbrowser</groupId>
                    <version>${jxbrowser.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>mac-x64</id>
            <activation>
                <os>
                    <family>mac</family>
                    <arch>x86_64</arch>
                </os>
            </activation>
            <dependencies>
                <dependency>
                    <artifactId>jxbrowser-mac</artifactId>
                    <groupId>com.teamdev.jxbrowser</groupId>
                    <version>${jxbrowser.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>linux-x64</id>
            <activation>
                <os>
                    <family>unix</family>
                    <arch>amd64</arch>
                </os>
            </activation>
            <dependencies>
                <dependency>
                    <artifactId>jxbrowser-linux64</artifactId>
                    <groupId>com.teamdev.jxbrowser</groupId>
                    <version>${jxbrowser.version}</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <addResources>true</addResources>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven-deploy-plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
