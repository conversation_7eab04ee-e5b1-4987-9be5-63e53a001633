FROM adoptopenjdk:11-jdk-openj9

# 华为镜像
RUN sed -i s@/archive.ubuntu.com/@/repo.huaweicloud.com/@g /etc/apt/sources.list
RUN sed -i "s@http://.*security.ubuntu.com@http://repo.huaweicloud.com@g" /etc/apt/sources.list
RUN apt-get clean
RUN apt-get update -y
RUN apt-get install -y libcrypto++-dev xvfb wget
RUN apt-get install -y \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libc6 \
    libcairo2  \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libexpat1 \
    libfontconfig1 \
    libgbm1 \
    libgcc-s1 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpulse0 \
    libx11-6 \
    libxcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    libxshmfence1 \
    libxtst6 \
    xdg-utils

COPY entrypoint.sh /entrypoint.sh

# 字体
RUN wget -O PingFang.ttc https://obs.labway.cn/labway-lims/prod/2023/06/10/7b331055e73b4857a079275039f61a56
RUN mv PingFang.ttc /usr/share/fonts/truetype/dejavu/
RUN wget -O SourceHanSerif.ttc https://obs.labway.cn/labway-lims/prod/2023/06/15/c64041de13394e70ac8443a6d64245f2
RUN mv SourceHanSerif.ttc /usr/share/fonts/truetype/dejavu/
RUN wget -O seguiemj.ttf https://obs.labway.cn/labway-lims/prod/2023/06/15/1b5f90025c884baab14b046274af90b0
RUN mv seguiemj.ttf /usr/share/fonts/truetype/dejavu/
RUN fc-cache

ENTRYPOINT ["/entrypoint.sh"]