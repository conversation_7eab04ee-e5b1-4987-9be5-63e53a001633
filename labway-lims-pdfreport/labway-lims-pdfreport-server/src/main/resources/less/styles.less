@borderWidth: 0.08rem;

@media print {
  @page {
    @top-left {
      content: element(header);
    }

    @bottom-left {
      content: element(footer);
    }

    padding: 0;
  }
}

html {
  font-size: 3mm;
}

body,
html {
  width: 100%;
  height: 100%;
  font-family: "PingFang TC", "Alibaba PuHuiTi 3.0", serif;
}


.line {
  border-top: @borderWidth solid black;
}

.hidden {
  display: none;
}

.absolute {
  position: absolute;
}

.barcode {
  display: block;
}

html,
body,
div,
ul,
li,
h1,
h2,
h3,
h4, h5, h6,
p,
ol,
pre {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

ol {
  list-style-type: none;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1rem;
  font-size: 1rem;
  font-family: "PingFang TC", "Alibaba PuHuiTi 3.0", serif;
}

/*  公共变量 */
.bold {
  font-weight: bold;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.em-1 {
  text-indent: 1em;
}

.em-2 {
  text-indent: 2em;
}

.em-3 {
  text-indent: 3em;
}

/* header */
#header {
  position: running(header);
}

// 在浏览器上显示底部
@media screen {
  #footer {
    position: absolute;
    bottom: 0;
  }

  #header {
    position: relative;
  }
}

/*上下居中*/
.align-items-center {
  display: flex;
  align-items: center;
}

/* row */
.row {

  position: relative;
  display: flex;


  .column {
    position: relative;

    .value {
      flex: 1;
      position: relative;
    }

    .kv {
      display: flex;

      .value {
        word-break: break-all;
        padding-left: 0.5rem;
      }
    }


    &.flex {
      flex: 1;
    }

    &.flex2 {
      flex: 0 0 50%;
    }

    &.flex3 {
      flex: 0 0 33%;
    }

    &.flex4 {
      flex: 0 0 25%;
    }

    &.flex5 {
      flex: 0 0 20%;
    }
  }

}

/* table */
table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;

  tr {
    page-break-inside: avoid;

    td {
      padding: 0.3rem 0;
      word-break: break-all;
      text-align: center;

      // 禁止换页就在当前页显示，如果显示不下则自动移到下一页
      page-break-inside: avoid;

    }

  }

  thead tr td {
    border-top: @borderWidth black solid;
    border-bottom: @borderWidth black solid;
  }
}

/* footer */
#footer {
  position: running(footer);
  width: 100%;
}

.debugger {
  #header {
    background-color: saddlebrown;

    .right-corner .tel-page {
      background-color: aquamarine;

      .pageno {
        background-color: brown;
      }
    }
  }

  table thead td {
    background-color: cadetblue;

    &:nth-of-type(1) {
      background-color: darkred;
    }

    &:nth-of-type(2) {
      background-color: darkslategrey;
    }

    &:nth-of-type(3) {
      background-color: darkcyan;
    }

    &:nth-of-type(4) {
      background-color: darkgoldenrod;
    }

    &:nth-of-type(5) {
      background-color: darkgray;
    }

    &:nth-of-type(6) {
      background-color: darkkhaki;
    }

    &:nth-of-type(7) {
      background-color: darkmagenta;
    }

    &:nth-of-type(8) {
      background-color: darkolivegreen;
    }

    &:nth-of-type(9) {
      background-color: darkorange;
    }

    &:nth-of-type(10) {
      background-color: darkseagreen;
    }
  }

  #footer {
    background-color: blueviolet;
  }


  .row .column .kv .value {
    background-color: green;
  }
}