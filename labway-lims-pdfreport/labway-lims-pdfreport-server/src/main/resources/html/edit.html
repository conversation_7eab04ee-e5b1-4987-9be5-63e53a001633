<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="UTF-8">
    <link rel="icon" href="https://lims.labway.cn/LimsFront/favicon.ico">
    <link rel="stylesheet" href="./static/css/style.css">
    <link rel="stylesheet" href="./static/css/codemirror.css">
    <link rel="stylesheet" href="./static/js/codemirror/addon/dialog/dialog.css">
    <link rel="stylesheet" href="./static/jquery-ui-1.13.2/jquery-ui.min.css">
    <link rel="stylesheet" href="./static/jquery-ui-1.13.2/jquery-ui.theme.min.css">

    <script src="./static/js/jquery-1.11.0.min.js"></script>
    <script src="./static/jquery-ui-1.13.2/jquery-ui.min.js"></script>
    <script src="./static/js/codemirror/codemirror.js"></script>
    <script src="./static/js/codemirror/mode/xml/xml.js"></script>
    <script src="./static/js/codemirror/mode/css/css.js"></script>
    <script src="./static/js/codemirror/addon/edit/closetag.js"></script>
    <script src="./static/js/codemirror/addon/edit/closebrackets.js"></script>
    <script src="./static/js/codemirror/mode/javascript/javascript.js"></script>
    <script src="./static/js/codemirror/mode/htmlmixed/htmlmixed.js"></script>
    <script src="./static/js/codemirror/addon/selection/active-line.js"></script>
    <script src="./static/js/codemirror/addon/edit/matchbrackets.js"></script>
    <script src="./static/js/codemirror/addon/fold/xml-fold.js"></script>
    <script src="./static/js/codemirror/addon/edit/matchtags.js"></script>
    <script src="./static/js/codemirror/addon/dialog/dialog.js"></script>
    <script src="./static/js/codemirror/addon/search/searchcursor.js"></script>
    <script src="./static/js/codemirror/addon/search/search.js"></script>
    <script src="./static/js/codemirror/addon/scroll/annotatescrollbar.js"></script>
    <script src="./static/js/codemirror/addon/search/matchesonscrollbar.js"></script>
    <script src="./static/js/codemirror/addon/search/jump-to-line.js"></script>
    <script src="./static/js/js-beautify/beautify-5.15.1.min.js"></script>
    <script src="./static/js/js-beautify/beautify-html-5.15.1.min.js"></script>
    <script src="./static/js/codemirror/addon/display/placeholder.js"></script>
    <title>报告模板编辑</title>
    <style>

        .CodeMirror {
            border: 1px solid black;
            font-size: 13px;
            margin: 5px 0;
        }


        .CodeMirror-empty {
            outline: 1px solid #c22;
        }

        .CodeMirror-empty.CodeMirror-focused {
            outline: none;
        }

        .CodeMirror pre.CodeMirror-placeholder {
            color: #999;
        }

        .app input {
            width: 350px;
        }
    </style>
</head>
<body>
<div class="app">
    <p>
        <label>
            编码:
            <input id="code" maxlength="50"/>
        </label>
    </p>
    <p>
        <label>
            名称:
            <input id="name" maxlength="50"/>
        </label>
    </p>
    <p>
        <label>
            <textarea id="content"></textarea>
        </label>
    </p>
    <p>
        <a href="javascript:void(0)" id="btn">保存</a>
        <!--        <a href="javascript:void(0)" id="preview">预览</a>-->
        <a href="javascript:void(0)" id="from">从VForm</a>
        <a href="javascript:history.back()">返回</a>
        <span id="tip" style="color: darkgreen" hidden>保存成功</span>
    </p>

    <div id="dialog-confirm" title="提示">
        <label> <textarea id="from-json"></textarea></label>
    </div>
</div>

<script>

    let htmlEditor = null

    $(function () {
        const editor = CodeMirror.fromTextArea($('#from-json').get(0), {
            mode: 'application/json',
            lineNumbers: true,
            autoCloseTags: true,
            tabSize: 4,
            indentUnit: 4,
            indentWithTabs: false,
            matchBrackets: true,
            autoCloseBrackets: true,
            matchTags: {bothTags: true},
            styleActiveLine: true,
        });

        const $dialog = $("#dialog-confirm").dialog({
            resizable: false,
            height: "auto",
            width: "50%",
            modal: true,
            autoOpen: false,
            buttons: {
                "确定": function () {
                    // 新增
                    $.ajax({
                        url: '/template/vform',
                        data: {struct: editor.getDoc().getValue()},
                        headers: {'content-type': 'application/x-www-form-urlencoded'},
                        method: 'post',
                        success: function (res) {
                            htmlEditor && htmlEditor.getDoc().setValue(res.data.html)
                            $dialog.dialog("close");
                        },
                        error: function (e) {
                            alert(e.responseJSON.message)
                        },
                        complete: function () {
                        }
                    })
                },
                "取消": function () {
                    $(this).dialog("close");
                }
            },
            beforeClose: function () {
                editor.getDoc().setValue('')
            }
        });

        $('#from').click(function () {
            $dialog.dialog('open')
        })
    });

    $(function () {
        const $code = $('#code')
        const $name = $('#name')
        const $tip = $('#tip')
        const $content = $('#content').prop('placeholder',
            `......\n......\nCtrl + S 保存
Ctrl + F 搜索
Ctrl + G 跳到下一个匹配
Ctrl + J 跳到闭合标签`)
        const $save = $('#btn');
        const params = Object.fromEntries(new URLSearchParams(location.search)) || {}

        if (params.mode === 'update') {
            $code.prop('disabled', 'disabled')
        }


        htmlEditor = CodeMirror.fromTextArea($content.get(0), {
            mode: 'text/html',
            lineNumbers: true,
            autoCloseTags: true,
            tabSize: 4,
            indentUnit: 4,
            indentWithTabs: false,
            matchBrackets: true,
            autoCloseBrackets: true,
            extraKeys: {
                "Ctrl-J": "toMatchingTag",
                "Cmd-J": "toMatchingTag",
                "Ctrl-S": function () {
                    $save.click()
                },
                "Cmd-S": function () {
                    $save.click()
                }
            },
            matchTags: {bothTags: true},
            styleActiveLine: true,
        });
        htmlEditor.setOption("theme", 'default');
        htmlEditor.setSize(null, 600);


        if (params.mode === 'update') {
            // 新增
            $.ajax({
                url: '/template/get?reportTemplateId=' + params.reportTemplateId || '0',
                method: 'get',
                success: function (res) {
                    $code.val(res.data.reportTemplateCode);
                    $name.val(res.data.reportTemplateName);
                    htmlEditor.getDoc().setValue(res.data.content || "");
                },
                error: function (e) {
                    alert(e.responseJSON.message)
                }
            })
        }

        $save.on('click', function () {
            const value = htmlEditor.getDoc().getValue()


            if ($name.val().trim().length === 0) {
                alert('名称不能为空')
                $name.focus()
                return
            } else if ($code.val().trim().length === 0) {
                alert('编码不能为空')
                $code.focus()
                return
            } else if (value.length === 0) {
                alert('模板不能为空')
                $content.focus()
                return
            }


            // 修改
            if (params.mode === 'update') {
                $.ajax({
                    url: '/template/update',
                    method: 'post',
                    data: JSON.stringify({
                        reportTemplateId: params.reportTemplateId || '0',
                        reportTemplateCode: $code.val(),
                        reportTemplateName: $name.val(),
                        content: value,
                    }),
                    contentType: "application/json;charset=UTF-8",
                    success: function () {
                        $tip.fadeIn().delay(1000).fadeOut()
                    },
                    error: function (e) {
                        alert(e.responseJSON.message)
                    }
                })
            } else {
                // 新增
                $.ajax({
                    url: '/template/add',
                    method: 'post',
                    data: JSON.stringify({
                        reportTemplateCode: $code.val(),
                        reportTemplateName: $name.val(),
                        content: value,
                    }),
                    contentType: "application/json;charset=UTF-8",
                    success: function () {
                        location.href = './index.html'
                    },
                    error: function (e) {
                        alert(e.responseJSON.message)
                    }
                })
            }


        })

    })
</script>
</body>
</html>