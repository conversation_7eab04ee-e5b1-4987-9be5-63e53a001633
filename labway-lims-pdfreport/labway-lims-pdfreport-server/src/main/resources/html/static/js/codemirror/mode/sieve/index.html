<!doctype html>

<title>CodeMirror: <PERSON>eve (RFC5228) mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="sieve.js"></script>
<style>.CodeMirror {background: #f8f8f8;}</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Sieve (RFC5228)</a>
  </ul>
</div>

<article>
<h2>Sieve (RFC5228) mode</h2>
<form><textarea id="code" name="code">
#
# Example Sieve Filter
# Declare any optional features or extension used by the script
#

require ["fileinto", "reject"];

#
# Reject any large messages (note that the four leading dots get
# "stuffed" to three)
#
if size :over 1M
{
  reject text:
Please do not send me large attachments.
Put your file on a server and send me the URL.
Thank you.
.... Fred
.
;
  stop;
}

#
# Handle messages from known mailing lists
# Move messages from IETF filter discussion list to filter folder
#
if header :is "Sender" "<EMAIL>"
{
  fileinto "filter";  # move to "filter" folder
}
#
# Keep all messages to or from people in my company
#
elsif address :domain :is ["From", "To"] "example.com"
{
  keep;               # keep in "In" folder
}

#
# Try and catch unsolicited email.  If a message is not to me,
# or it contains a subject known to be spam, file it away.
#
elsif anyof (not address :all :contains
               ["To", "Cc", "Bcc"] "<EMAIL>",
             header :matches "subject"
               ["*make*money*fast*", "*university*dipl*mas*"])
{
  # If message header does not contain my address,
  # it's from a list.
  fileinto "spam";   # move to "spam" folder
}
else
{
  # Move all other (non-company) mail to "personal"
  # folder.
  fileinto "personal";
}
</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {});
    </script>

    <p><strong>MIME types defined:</strong> <code>application/sieve</code>.</p>

  </article>
