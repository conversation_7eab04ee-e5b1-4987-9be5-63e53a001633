<!doctype html>

<title>CodeMirror: VBScript mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="vbscript.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">VBScript</a>
  </ul>
</div>

<article>
<h2>VBScript mode</h2>


<div><textarea id="code" name="code">
' Pete Guhl
' 03-04-2012
'
' Basic VBScript support for codemirror2

Const ForReading = 1, ForWriting = 2, ForAppending = 8

Call Sub020_PostBroadcastToUrbanAirship(strUserName, strPassword, intTransmitID, strResponse)

If Not IsNull(strResponse) AND Len(strResponse) = 0 Then
	boolTransmitOkYN = False
Else
	' WScript.Echo "Oh Happy Day! Oh Happy DAY!"
	boolTransmitOkYN = True
End If
</textarea></div>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        indentUnit: 4
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/vbscript</code>.</p>
  </article>
