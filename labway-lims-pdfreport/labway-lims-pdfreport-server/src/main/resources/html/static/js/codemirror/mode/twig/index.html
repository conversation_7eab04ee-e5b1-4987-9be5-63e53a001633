<!doctype html>

<title>CodeMirror: Twig mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="twig.js"></script>
<script src="../xml/xml.js"></script>
<script src="../../addon/mode/multiplex.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Twig</a>
  </ul>
</div>

<article>
<h2>Twig mode</h2>
<form><textarea id="code" name="code">
{% extends "layout.twig" %}
{% block title %}CodeMirror: Twig mode{% endblock %}
{# this is a comment #}
{% block content %}
  {% for foo in bar if foo.baz is divisible by(3) %}
    Hello {{ foo.world }}
  {% else %}
    {% set msg = "Result not found" %}
    {% include "empty.twig" with { message: msg } %}
  {% endfor %}
{% endblock %}
</textarea></form>
    <script>
      var editor =
      CodeMirror.fromTextArea(document.getElementById("code"), {mode:
        {name: "twig", base: "text/html"}});
    </script>
  </article>
