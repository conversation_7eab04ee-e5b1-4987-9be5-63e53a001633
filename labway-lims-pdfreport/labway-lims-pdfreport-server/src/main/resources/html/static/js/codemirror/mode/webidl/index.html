<!doctype html>

<title>CodeMirror: Web IDL mode</title>
<meta charset="utf-8">
<link rel="stylesheet" href="../../doc/docs.css">
<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="webidl.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>

<div id="nav">
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id="logo" src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class="active" href="#">Web IDL</a>
  </ul>
</div>

<article>
  <h2>Web IDL mode</h2>

  <div>
<textarea id="code" name="code">
[NamedConstructor=Image(optional unsigned long width, optional unsigned long height)]
interface HTMLImageElement : HTMLElement {
           attribute DOMString alt;
           attribute DOMString src;
           attribute DOMString srcset;
           attribute DOMString sizes;
           attribute DOMString? crossOrigin;
           attribute DOMString useMap;
           attribute boolean isMap;
           attribute unsigned long width;
           attribute unsigned long height;
  readonly attribute unsigned long naturalWidth;
  readonly attribute unsigned long naturalHeight;
  readonly attribute boolean complete;
  readonly attribute DOMString currentSrc;

  // also has obsolete members
};

partial interface HTMLImageElement {
  attribute DOMString name;
  attribute DOMString lowsrc;
  attribute DOMString align;
  attribute unsigned long hspace;
  attribute unsigned long vspace;
  attribute DOMString longDesc;

  [TreatNullAs=EmptyString] attribute DOMString border;
};
</textarea>
  </div>

  <script>
    var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
      lineNumbers: true,
      matchBrackets: true
    });
  </script>

  <p><strong>MIME type defined:</strong> <code>text/x-webidl</code>.</p>
</article>
