<!doctype html>

<title>CodeMirror: Rust mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/mode/simple.js"></script>
<script src="rust.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Rust</a>
  </ul>
</div>

<article>
<h2>Rust mode</h2>


<div><textarea id="code" name="code">
// Demo code.

type foo<T> = i32;
enum bar {
    Some(i32, foo<f32>),
    None
}

fn check_crate(x: i32) {
    let v = 10;
    match foo {
        1 ... 3 {
            print_foo();
            if x {
                blah().to_string();
            }
        }
        (x, y) { "bye" }
        _ { "hi" }
    }
}
</textarea></div>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        lineWrapping: true,
        indentUnit: 4,
        mode: "rust"
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-rustsrc</code>.</p>
  </article>
