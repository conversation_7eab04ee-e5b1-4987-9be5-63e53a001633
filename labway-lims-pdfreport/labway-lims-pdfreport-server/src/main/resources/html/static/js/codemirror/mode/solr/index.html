<!doctype html>

<title>CodeMirror: Solr mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="solr.js"></script>
<style>
  .CodeMirror {
    border-top: 1px solid black;
    border-bottom: 1px solid black;
  }

  .CodeMirror .cm-operator {
    color: orange;
  }
</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Solr</a>
  </ul>
</div>

<article>
  <h2>Solr mode</h2>

  <div>
    <textarea id="code" name="code">author:Camus

title:"The Rebel" and author:Camus

philosophy:Existentialism -author:Kierkegaard

hardToSpell:Dostoevsky~

published:[194* TO 1960] and author:(Sartre or "Simone de Beauvoir")</textarea>
  </div>

  <script>
    var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
      mode: 'solr',
      lineNumbers: true
    });
  </script>

  <p><strong>MIME types defined:</strong> <code>text/x-solr</code>.</p>
</article>
