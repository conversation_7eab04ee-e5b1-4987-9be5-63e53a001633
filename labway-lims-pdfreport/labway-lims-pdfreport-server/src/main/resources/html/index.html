<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <link rel="icon" href="https://lims.labway.cn/LimsFront/favicon.ico">
    <link rel="stylesheet" href="./static/css/style.css">
    <script src="./static/js/jquery-1.11.0.min.js"></script>
    <script src="./static/js/jquery.cookie.js"></script>
    <title>报告模板配置</title>

    <script>

        $(function () {
            const params = Object.fromEntries(new URLSearchParams(location.search)) || {}
            if (!params.hasOwnProperty('_r')) {
                params._r = Math.random().toString()
                location.href = location.origin + location.pathname + '?' + new URLSearchParams(params).toString()
                return
            }

            $.cookie('Authorization', '3CF61AF5C06FD7E749EE6C84E05C929C23E321569897695F31E3731558518964', {
                expires: 1,
                path: '/'
            });
        })
    </script>
</head>
<body>
<div class="app">
    <a href="javascript:location.href='./edit.html?mode=add&_r='+Math.random()">新增</a>
    <table border="1" align="">
        <thead>
        <tr>
            <td>ID</td>
            <td>名称</td>
            <td>编码</td>
            <td width="250">创建时间</td>
            <td width="250">更新时间</td>
            <td>操作</td>
        </tr>
        </thead>
        <tbody>

        </tbody>
    </table>
</div>

<script>


    function del(id) {
        if (confirm('你确定要删除吗？')) {
            $.ajax({
                url: '/template/delete?reportTemplateId=' + id,
                method: 'post',
                success: function () {
                    location.reload()
                },
                error: function (e) {
                    alert(e.responseJSON.message)
                }
            })
        }

    }

    $(function () {


        const $table = $('table')
        $.getJSON('/template/list', function (res) {
            const $tbody = $table.find('tbody').empty()
            for (const item of res.data) {
                $tbody.append(`
                    <tr>
                        <td>${item.reportTemplateId}</td>
                        <td>${item.reportTemplateName}</td>
                        <td>${item.reportTemplateCode}</td>
                        <td width="150">${item.createDate}</td>
                        <td width="150">${item.updateDate}</td>
                        <td>
                            <a href="./edit.html?r=${Math.random()}&reportTemplateId=${item.reportTemplateId}&mode=update">修改</a>
                            <a href="./history.html?r=${Math.random()}&reportTemplateId=${item.reportTemplateId}">历史</a>
                            <a style="color: red" href="javascript:void(0)" onclick="del('${item.reportTemplateId}')">删除</a>
                        </td>
                    </tr>
                `)
            }
        })


    });
</script>
</body>
</html>