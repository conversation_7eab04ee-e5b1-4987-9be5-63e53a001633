package com.labway.lims.pdfreport.widgets;

import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.UnescapedText;
import j2html.tags.specialized.DivTag;
import lombok.Getter;
import lombok.Setter;

import static j2html.TagCreator.div;

/**
 * 自定义代码片段
 */
@Getter
@Setter
public class HtmlTextWidget extends Widget {


    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final DivTag row = div().withClasses("column", "flex");

        final String htmlContent = getOriginal().getJSONObject("options").getString("htmlContent");

        return row.with(div().withClass("html-text").with(new UnescapedText(htmlContent)));
    }
}