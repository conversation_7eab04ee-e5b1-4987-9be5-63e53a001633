package com.labway.lims.pdfreport.widgets;

import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.TdTag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

import static j2html.TagCreator.td;

/**
 * 表格列
 */
@Getter
@Setter
public class TableColumnWidget extends Widget {

    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final TdTag td = td();

        final JSONObject options = getOriginal().getJSONObject("options");
        final String cellWidth = options.getString("cellWidth");
        final String cellColor = options.getString("cellColor");

        final Map<CssStyleDeclaration, String> declaration = getCssStyleDeclaration();
        if (StringUtils.isNotBlank(cellWidth)) {
            declaration.put(CssStyleDeclaration.WIDTH, cellWidth + "rem");
        }

        if (StringUtils.isNotBlank(cellColor)) {
            declaration.put(CssStyleDeclaration.BACKGROUND_COLOR, cellColor);
        }

        td.withStyle(cssStyleFromMap(declaration));

        for (Widget child : getChildren()) {
            td.with(child.toTag(td));
        }

        return td;
    }
}
