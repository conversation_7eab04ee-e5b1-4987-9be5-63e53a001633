package com.labway.lims.pdfreport.widgets;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.TableTag;
import j2html.tags.specialized.TrTag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import static j2html.TagCreator.*;

/**
 * 表格
 */
@Getter
@Setter
public class TableWidget extends Widget {

    @Override
    public DomContent toTag(ContainerTag<?> parent) {

        final TableTag table = table();

        final JSONObject options = getOriginal().getJSONObject("options");

        final JSONArray rows = getOriginal().getJSONArray("rows");
        if (CollectionUtils.isEmpty(rows) || rows.size() != 2) {
            throw new IllegalStateException(String.format("table 缺少列或内容 rows [%s]", rows.size()));
        }

        final String dataSource = options.getString("dataSource");
        if (StringUtils.isBlank(dataSource)) {
            throw new IllegalStateException("表格的 dataSource 为空");
        }

        {
            final TrTag tr = tr();
            final Widget w = new TableWidget();
            PageWidget.parseWidget(rows.getJSONObject(0).getJSONArray("cols"), w);
            tr.with(each(w.getChildren(), widget -> widget.toTag(tr)));
            table.with(thead(tr));
        }

        {
            final TrTag tr = tr();
            final Widget w = new TableWidget();
            PageWidget.parseWidget(rows.getJSONObject(1).getJSONArray("cols"), w);
            tr.attr("th:each", String.format("item,stat : ${%s}", dataSource));
            tr.with(each(w.getChildren(), widget -> widget.toTag(tr)));
            table.with(tbody(tr));
        }

        return table.withClass("table");
    }
}
