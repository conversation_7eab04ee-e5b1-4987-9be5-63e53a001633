package com.labway.lims.pdfreport.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.pdfreport.api.dto.ReportTemplateHistoryDto;
import com.labway.lims.pdfreport.api.service.ReportTemplateHistoryService;
import com.labway.lims.pdfreport.mapper.TbReportTemplateHistoryMapper;
import com.labway.lims.pdfreport.model.TbReportTemplateHistory;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class ReportTemplateHistoryServiceImpl implements ReportTemplateHistoryService {
    @Resource
    private TbReportTemplateHistoryMapper reportTemplateHistoryMapper;

    @Override
    public long addReportTemplateHistory(ReportTemplateHistoryDto reportTemplateHistory) {
        reportTemplateHistoryMapper.insert(convert(reportTemplateHistory));
        return reportTemplateHistory.getReportTemplateHistoryId();
    }

    @Override
    public List<ReportTemplateHistoryDto> selectByReportTemplateId(long reportTemplateId) {
        return reportTemplateHistoryMapper.selectList(new LambdaQueryWrapper<TbReportTemplateHistory>()
                        .eq(TbReportTemplateHistory::getReportTemplateId, reportTemplateId)
                        .orderByDesc(TbReportTemplateHistory::getReportTemplateHistoryId)
                ).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<ReportTemplateHistoryDto> selectNonContentByReportTemplateId(long reportTemplateId) {
        return reportTemplateHistoryMapper.selectList(new LambdaQueryWrapper<TbReportTemplateHistory>()
                        .select(List.of(TbReportTemplateHistory::getReportTemplateHistoryId,
                                TbReportTemplateHistory::getReportTemplateId, TbReportTemplateHistory::getCreateDate,
                                TbReportTemplateHistory::getIsDelete))
                        .eq(TbReportTemplateHistory::getReportTemplateId, reportTemplateId)
                        .orderByDesc(TbReportTemplateHistory::getReportTemplateHistoryId)
                ).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    public ReportTemplateHistoryDto selectByReportTemplateHistoryId(long reportTemplateHistoryId) {
        return convert(reportTemplateHistoryMapper.selectById(reportTemplateHistoryId));
    }


    public TbReportTemplateHistory convert(ReportTemplateHistoryDto reportTemplateHistory) {
        if (Objects.isNull(reportTemplateHistory)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(reportTemplateHistory), TbReportTemplateHistory.class);
    }


    public ReportTemplateHistoryDto convert(TbReportTemplateHistory reportTemplateHistory) {
        if (Objects.isNull(reportTemplateHistory)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(reportTemplateHistory), ReportTemplateHistoryDto.class);
    }
}
