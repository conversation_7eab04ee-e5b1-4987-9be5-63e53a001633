package com.labway.lims.pdfreport.widgets;

import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.EnumMap;
import java.util.LinkedList;
import java.util.Map;
import java.util.Objects;

@Getter
@Setter
public abstract class Widget {

    public static final Map<String, Class<? extends Widget>> DEFINE_CHILDREN = Map.ofEntries(
            Map.entry("divider", DividerWidget.class),
            Map.entry("pageHeader", HeaderWidget.class),
            Map.entry("pageContent", ContentWidget.class),
            Map.entry("pageFooter", FooterWidget.class),
            Map.entry("static-text", StaticTextWidget.class),
            Map.entry("grid", RowWidget.class),
            Map.entry("grid-col", ColumnWidget.class),
            Map.entry("input", KeyValueWidget.class),
            Map.entry("table", TableWidget.class),
            Map.entry("table-cell", TableColumnWidget.class),
            Map.entry("qrcode", QRCodeWidget.class),
            Map.entry("barcode", BarcodeWidget.class),
            Map.entry("checkbox", CheckboxWidget.class),
            Map.entry("html-text", HtmlTextWidget.class),
            Map.entry("textField", PreWidget.class),
            Map.entry("page-no", PageNoWidget.class),
            Map.entry("picture", ImgWidget.class),
            Map.entry("labelPicture", KeyImgWidget.class)
    );


    private Map<CssStyleDeclaration, String> cssStyleDeclaration;

    private String type;

    /**
     * 原始
     */
    private JSONObject original;

    /**
     * 孩子
     */
    private LinkedList<Widget> children = new LinkedList<>();

    @Override
    public String toString() {
        return "type=" + type;
    }


    public String cssStyleFromMap(Map<CssStyleDeclaration, String> styles) {
        if (MapUtils.isEmpty(styles)) {
            return StringUtils.EMPTY;
        }

        final StringBuilder sb = new StringBuilder();
        for (var e : styles.entrySet()) {
            sb.append(e.getKey().getDeclaration()).append(":").append(e.getValue()).append(";");
        }

        return sb.toString();
    }

    public Map<CssStyleDeclaration, String> getCssStyleDeclaration() {
        if (Objects.isNull(cssStyleDeclaration)) {
            cssStyleDeclaration = new EnumMap<>(CssStyleDeclaration.class);
        }
        return cssStyleDeclaration;
    }

    public DomContent toTag(ContainerTag<?> parent) {
        throw new UnsupportedOperationException(type + " unsupported");
    }
}
