package com.labway.lims.pdfreport.widgets;


import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.DivTag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

import static j2html.TagCreator.div;

/**
 * 页脚
 */
@Getter
@Setter
public class FooterWidget extends Widget {
    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final DivTag footer = div().withId("footer")
                .withClasses("limited-paper-size");

        for (Widget child : getChildren()) {
            footer.with(child.toTag(footer));
        }

        final JSONObject options = getOriginal().getJSONObject("options");

        if (BooleanUtils.isTrue(options.getBoolean("dynamicHeight"))) {
            footer.attr("dynamic-height");
        } else {
            footer.withStyle(cssStyleFromMap(Map.of(CssStyleDeclaration.HEIGHT,
                    StringUtils.defaultString(options.getString("dynamicHeight"), "5"))));
        }

        return footer;
    }
}