package com.labway.lims.pdfreport.widgets;

import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.UnescapedText;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

/**
 * 条形码
 */
@Getter
@Setter
public class BarcodeWidget extends Widget {


    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final JSONObject options = getOriginal().getJSONObject("options");
        final String textContent = StringUtils.defaultString(options.getString("textContent"));
        final String height = StringUtils.defaultString(options.getString("height"), "1");

        if (StringUtils.isBlank(textContent)) {
            throw new IllegalStateException("barcode textContent 不能为空");
        }

        return new UnescapedText(String.format("<barcode style='height:%srem' class=barcode th:text='|%s|'>条形码%s</barcode>", height, textContent,
                textContent));
    }


}

