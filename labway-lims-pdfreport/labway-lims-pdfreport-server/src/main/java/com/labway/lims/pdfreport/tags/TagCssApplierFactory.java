package com.labway.lims.pdfreport.tags;

import com.itextpdf.html2pdf.css.apply.ICssApplier;
import com.itextpdf.html2pdf.css.apply.impl.BlockCssApplier;
import com.itextpdf.html2pdf.css.apply.impl.DefaultCssApplierFactory;
import com.itextpdf.styledxmlparser.node.IElementNode;
import org.apache.commons.lang3.StringUtils;

/**
 * tag css
 */
public class TagCssApplierFactory extends DefaultCssApplierFactory {
    @Override
    public ICssApplier getCustomCssApplier(IElementNode tag) {
        if (StringUtils.equalsIgnoreCase("barcode", tag.name())) {
            return new BlockCssApplier();
        } else if (StringUtils.equalsIgnoreCase("qrcode", tag.name())) {
            return new BlockCssApplier();
        }
        return super.getCustomCssApplier(tag);
    }
}
