package com.labway.lims.pdfreport.tags;

import com.itextpdf.html2pdf.attach.ITagWorker;
import com.itextpdf.html2pdf.attach.ProcessorContext;
import com.itextpdf.html2pdf.attach.impl.DefaultTagWorkerFactory;
import com.itextpdf.styledxmlparser.node.IElementNode;
import org.apache.commons.lang3.StringUtils;

/**
 * TagWorkerFactory
 */
public class TagWorkerFactory extends DefaultTagWorkerFactory {
    @Override
    public ITagWorker getCustomTagWorker(IElementNode tag, ProcessorContext context) {
        if (StringUtils.equalsIgnoreCase("barcode", tag.name())) {
            return new BarcodeTagWorker(tag, context);
        } else if (StringUtils.equalsIgnoreCase("qrcode", tag.name())) {
            return new QRCodeTagWorker(tag, context);
        }
        return super.getCustomTagWorker(tag, context);
    }
}
