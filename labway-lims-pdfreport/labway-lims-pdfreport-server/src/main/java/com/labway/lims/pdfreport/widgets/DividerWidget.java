package com.labway.lims.pdfreport.widgets;

import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.DivTag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

import static j2html.TagCreator.div;
import static j2html.TagCreator.iff;

/**
 * 分割线
 */
@Getter
@Setter
public class DividerWidget extends Widget {

    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final JSONObject options = getOriginal().getJSONObject("options");
        final DivTag line = div().withClasses("line",
                iff(BooleanUtils.isTrue(options.getBoolean("hidden")), "hidden"));

        final Map<CssStyleDeclaration, String> styles = getCssStyleDeclaration();
        final String top = options.getString("dividerTop");
        final String bottom = options.getString("dividerBottom");
        final String color = options.getString("dividerColor");

        if (StringUtils.isNotBlank(top)) {
            styles.put(CssStyleDeclaration.MARGIN_TOP, top + "rem");
        }

        if (StringUtils.isNotBlank(bottom)) {
            styles.put(CssStyleDeclaration.MARGIN_BOTTOM, bottom + "rem");
        }

        if (StringUtils.isNotBlank(color)) {
            styles.put(CssStyleDeclaration.BORDER_COLOR, color);
        }

        return line.withStyle(cssStyleFromMap(styles));
    }
}