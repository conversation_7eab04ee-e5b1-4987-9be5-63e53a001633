package com.labway.lims.pdfreport.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.pdfreport.api.dto.ReportTemplateDto;
import com.labway.lims.pdfreport.api.dto.ReportTemplateHistoryDto;
import com.labway.lims.pdfreport.api.service.ReportTemplateHistoryService;
import com.labway.lims.pdfreport.api.service.ReportTemplateService;
import com.labway.lims.pdfreport.mapper.TbReportTemplateMapper;
import com.labway.lims.pdfreport.model.TbReportTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class ReportTemplateServiceImpl implements ReportTemplateService {
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private ReportTemplateHistoryService reportTemplateHistoryService;
    @Resource
    private TbReportTemplateMapper reportTemplateMapper;

    @Override
    @Trace
    public List<ReportTemplateDto> selectAll() {
        return reportTemplateMapper.selectList(new LambdaQueryWrapper<TbReportTemplate>()
                        .orderByAsc(TbReportTemplate::getReportTemplateId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByReportTemplateId(ReportTemplateDto reportTemplate) {

        ReportTemplateDto rt = selectByReportTemplateCode(reportTemplate.getReportTemplateCode());

        if (Objects.nonNull(rt) && !Objects.equals(reportTemplate.getReportTemplateId(), rt.getReportTemplateId())) {
            throw new IllegalStateException(String.format("编码 [%s] 已经存在", reportTemplate.getReportTemplateCode()));
        }


        if (Objects.isNull(rt)) {
            rt = selectByReportTemplateId(reportTemplate.getReportTemplateId());
        }

        if (Objects.isNull(rt)) {
            throw new IllegalStateException(String.format("编码 [%s] 不存在", reportTemplate.getReportTemplateCode()));
        }


        reportTemplate.setUpdateDate(new Date());

        if (reportTemplateMapper.updateById(convert(reportTemplate)) > 0) {

            final ReportTemplateHistoryDto reportTemplateHistory = new ReportTemplateHistoryDto();
            reportTemplateHistory.setReportTemplateHistoryId(snowflakeService.genId());
            reportTemplateHistory.setReportTemplateId(rt.getReportTemplateId());
            reportTemplateHistory.setContent(reportTemplate.getContent());
            reportTemplateHistory.setCreateDate(new Date());
            reportTemplateHistory.setIsDelete(YesOrNoEnum.NO.getCode());
            // 记录修改历史
            reportTemplateHistoryService.addReportTemplateHistory(reportTemplateHistory);

            return true;
        }

        return false;
    }

    @Override
    public long addReportTemplate(ReportTemplateDto reportTemplate) {

        if (Objects.isNull(reportTemplate)) {
            throw new IllegalArgumentException("参数错误");
        }

        if (Objects.nonNull(selectByReportTemplateCode(reportTemplate.getReportTemplateCode()))) {
            throw new IllegalStateException(String.format("编码 [%s] 已经存在", reportTemplate.getReportTemplateCode()));
        }

        if (Objects.isNull(reportTemplate.getReportTemplateId())) {
            reportTemplate.setReportTemplateId(snowflakeService.genId());
        }


        reportTemplate.setCreateDate(new Date());
        reportTemplate.setUpdateDate(new Date());
        reportTemplate.setIsDelete(YesOrNoEnum.NO.getCode());

        reportTemplateMapper.insert(convert(reportTemplate));

        return reportTemplate.getReportTemplateId();
    }

    @Override
    public void deleteByReportTemplateId(long reportTemplateId) {
        reportTemplateMapper.deleteById(reportTemplateId);
    }

    @Nullable
    @Override
    public ReportTemplateDto selectByReportTemplateId(long reportTemplateId) {
        return convert(reportTemplateMapper.selectById(reportTemplateId));
    }

    @Nullable
    @Override
    @Trace
    public ReportTemplateDto selectByReportTemplateCode(String reportTemplateCode) {
        return convert(reportTemplateMapper.selectOne(new LambdaQueryWrapper<TbReportTemplate>()
                .eq(TbReportTemplate::getReportTemplateCode, reportTemplateCode)
                .last("limit 1")));
    }

    @Override
    public Map<String, List<ReportTemplateDto>> selectByReportTemplateCodes(Collection<String> reportTemplateCodes) {
        if (CollectionUtils.isEmpty(reportTemplateCodes)) {
            return Collections.emptyMap();
        }
        final LambdaQueryWrapper<TbReportTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbReportTemplate::getReportTemplateCode, reportTemplateCodes);

        return reportTemplateMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList())
                .stream().collect(Collectors.groupingBy(ReportTemplateDto::getReportTemplateCode));
    }


    private ReportTemplateDto convert(TbReportTemplate t) {
        if (Objects.isNull(t)) {
            return null;
        }

        return JSON.parseObject(JSON.toJSONString(t), ReportTemplateDto.class);
    }


    private TbReportTemplate convert(ReportTemplateDto t) {
        if (Objects.isNull(t)) {
            return null;
        }

        return JSON.parseObject(JSON.toJSONString(t), TbReportTemplate.class);
    }
}
