package com.labway.lims.pdfreport.widgets;

import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.DivTag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

import static j2html.TagCreator.div;

/**
 * 行
 */
@Getter
@Setter
public class RowWidget extends Widget {
    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final DivTag row = div().withClass("row");
        for (Widget child : getChildren()) {
            row.with(child.toTag(row));
        }

        final JSONObject options = getOriginal().getJSONObject("options");
        final String borderTop = options.getString("borderTop");
        final String borderRight = options.getString("borderRight");
        final String borderBottom = options.getString("borderBottom");
        final String borderLeft = options.getString("borderLeft");
        final String borderColor = options.getString("borderColor");

        final Map<CssStyleDeclaration, String> declaration = getCssStyleDeclaration();

        declaration.put(CssStyleDeclaration.BORDER_STYLE, "solid");
        declaration.put(CssStyleDeclaration.BORDER_COLOR, "transparent");

        if (StringUtils.isNotBlank(borderTop)) {
            declaration.put(CssStyleDeclaration.BORDER_TOP_WIDTH, borderTop + "rem");
        }

        if (StringUtils.isNotBlank(borderRight)) {
            declaration.put(CssStyleDeclaration.BORDER_RIGHT_WIDTH, borderRight + "rem");
        }

        if (StringUtils.isNotBlank(borderBottom)) {
            declaration.put(CssStyleDeclaration.BORDER_BOTTOM_WIDTH, borderBottom + "rem");
        }

        if (StringUtils.isNotBlank(borderLeft)) {
            declaration.put(CssStyleDeclaration.BORDER_LEFT_WIDTH, borderLeft + "rem");
        }

        if (StringUtils.isNotBlank(borderColor)) {
            declaration.put(CssStyleDeclaration.BORDER_COLOR, borderColor);
        }

        return row.withStyle(cssStyleFromMap(declaration));
    }
}