package com.labway.lims.pdfreport.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.labway.lims.pdfreport.api.dto.ca.*;
import com.labway.lims.pdfreport.api.service.CaPdfService;
import com.labway.lims.pdfreport.api.vo.ca.CAPdfSignVo;
import com.labway.lims.pdfreport.api.vo.ca.CAPdfTemplateVo;
import com.labway.lims.pdfreport.api.vo.ca.CASealVo;
import com.labway.lims.pdfreport.config.CAConfig;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.http.protocol.HTTP;
import org.jetbrains.annotations.Nullable;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@DubboService
public class CaPdfServiceImpl implements CaPdfService {

    @Resource
    private CAConfig caConfig;


    @Override
    public CAPingDto ping() {
        String url = caConfig.getPingUrl();
        log.info("CA认证 PING URL{}", url);
        HttpRequest request = HttpRequest.newBuilder(URI.create(url))
                .GET()
                .setHeader(HTTP.CONTENT_TYPE, ContentType.JSON.getValue())
                .timeout(Duration.ofMillis(3000))
                .build();
        HttpResponse<String> response;
        try {
            response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        } catch (IOException | InterruptedException e) {
            log.error("CA认证 PING 异常：{}", e.toString());
            return new CAPingDto(false, false);
        }
        JSONObject jsonObject = JSON.parseObject(response.body());
        String code = jsonObject.getString("code");
        if (!Objects.equals("200", code)) {
            log.error("CA认证 PING 异常：{}", jsonObject.getString("message"));
            return new CAPingDto(false, false);
        }
        JSONObject content = jsonObject.getJSONObject("content");
        return JSONUtil.toBean(JSONUtil.toJsonStr(content), CAPingDto.class);


    }

    @Override
    public List<CASealVo> selectSealList(String sealName) throws IOException {
        String url = caConfig.getSealUrl();

        // 装填参数
        CASealDto caSealDto = new CASealDto(caConfig.getAppKey(), caConfig.getAppSecret());
        caSealDto.setSealName(sealName);
        String bodyJson = JSON.toJSONString(caSealDto);
        log.info("CA认证获取模板， url：{}， body：{}", url, sealName);

        // 构建http请求
        HttpClient httpClient = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder(URI.create(url))
                .setHeader(HTTP.CONTENT_TYPE, ContentType.JSON.getValue())
                .POST(HttpRequest.BodyPublishers.ofString(bodyJson))
                .timeout(Duration.ofMillis(3000))
                .build();
        HttpResponse<String> response;

        try {
            response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        } catch (InterruptedException e) {
            log.error("CA认证获取模板失败， url：{}， body：{}， exception：{}", url, bodyJson, e.toString());
            throw new IllegalStateException("CA认证获取模板失败");
        }
        String body = response.body();
        JSONObject jsonObject = JSON.parseObject(body);

        verifyBody(jsonObject);

        JSONObject content = jsonObject.getJSONObject("content");
        JSONArray sealsJsonArray = content.getJSONArray("seals");

        return CollectionUtils.isEmpty(sealsJsonArray) ?
                Collections.emptyList() :
                BeanUtil.copyToList(sealsJsonArray, CASealVo.class);
    }

    @Override
    public List<CASealVo> selectSeal(@Nullable String sealName) throws IOException {

        return selectSealList(sealName);

    }

    @Override
    public CAPdfSignVo pdfQuiesceSign(CAPdfSignDto caPdfSignDto) throws IOException {
        String url = caConfig.getPdfQuiesceUrl();
        log.info("CA认证PDF静默授权， url：{}， body：{}", url, caPdfSignDto.getSignedStrategy());

        File file = new File(UUID.randomUUID() + ".pdf");
        FileUtils.writeByteArrayToFile(file, caPdfSignDto.getFile());
        // 填充ca信息
        caPdfSignDto.setAppKey(caConfig.getAppKey());
        caPdfSignDto.setAppSecret(caConfig.getAppSecret());

        OkHttpClient client = new OkHttpClient().newBuilder().build();
        MultipartBody multipartBody = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("appKey", caConfig.getAppKey())
                .addFormDataPart("appSecret", caConfig.getAppSecret())
                .addFormDataPart("files",
                        file.getName(),
                        RequestBody.create(caPdfSignDto.getFile(), MediaType.parse(ContentType.OCTET_STREAM.getValue()))
                )
                .addFormDataPart("signedStrategy", JSON.toJSONString(caPdfSignDto.getSignedStrategy()))
                .build();

        FileUtils.delete(file);

        Request request = new Request.Builder().url(url).post(multipartBody).build();

        Response response;
        try {
            response = client.newCall(request).execute();
        } catch (Exception e) {
            log.error("CA认证PDF静默授权失败， url：{}， body：{}， exception：{}", url, caPdfSignDto, e.toString());
            throw new IllegalStateException("CA认证PDF静默授权失败");
        }

        ResponseBody body = response.body();
        if(response.code() != 200){
            log.error("CA认证PDF静默授权失败， url：{}， code：{}， message：{}", url, response.code(), response.message());
            throw new IllegalStateException("CA认证PDF静默授权失败, 网络失败");
        }
        JSONObject jsonObject = JSON.parseObject(body.string());

        verifyBody(jsonObject);

        JSONObject content = jsonObject.getJSONObject("content");
        return  BeanUtil.toBean(content, CAPdfSignVo.class);
    }

    @Override
    public byte[] download(String envelopeId, String fileId) throws IOException {
        String url = caConfig.getDownloadUrl();
        log.info("CA认证PDF下载， url：{}， body：[envelopeId{}, fileId{}]]", url, envelopeId, fileId);

        Dict dict = Dict.of("appKey", caConfig.getAppKey(), "appSecret", caConfig.getAppSecret(), "envelopeId", envelopeId, "fileId", fileId);

        String bodyJson = JSON.toJSONString(dict);
        // 构建http请求
        HttpClient httpClient = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder(URI.create(url))
                .setHeader(HTTP.CONTENT_TYPE, ContentType.JSON.getValue())
                .POST(HttpRequest.BodyPublishers.ofString(bodyJson))
                .timeout(Duration.ofMillis(3000))
                .build();
        HttpResponse<InputStream> response;
        try {
            response = httpClient.send(request, HttpResponse.BodyHandlers.ofInputStream());
        } catch (InterruptedException e) {
            log.error("CA认证PDF下载失败， url：{}， body：{}， exception：{}", url, bodyJson, e.toString());
            throw new IllegalStateException("CA认证PDF下载失败");
        }
        InputStream inputStream = response.body();

        return IOUtils.toByteArray(inputStream);

    }

    @Override
    public List<CAPdfTemplateVo> selectPdfTemplate() {

        String url = caConfig.getTemplateUrl();
        log.error("CA认证获取所有模板， url{}", url);
        CAKeyDto caKeyDto = new CAKeyDto(caConfig.getAppKey(), caConfig.getAppSecret());
        String bodyJson = JSON.toJSONString(caKeyDto);

        // 构建http请求
        HttpClient httpClient = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder(URI.create(url))
                .setHeader(HTTP.CONTENT_TYPE, ContentType.JSON.getValue())
                .POST(HttpRequest.BodyPublishers.ofString(bodyJson))
                .timeout(Duration.ofMillis(3000))
                .build();
        HttpResponse<String> response;
        try {
            response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        } catch (IOException | InterruptedException e) {
            log.error("CA认证获取所有模板， url：{}， exception：{}", url, e.toString());
            throw new IllegalStateException("CA认证获取所有模板");
        }
        JSONObject jsonObject = JSON.parseObject(response.body());
        verifyBody(jsonObject);

        JSONObject content = jsonObject.getJSONObject("content");
        JSONArray pdfTemplates = content.getJSONArray("pdfTemplates");

        return CollectionUtils.isEmpty(pdfTemplates) ?
                Collections.emptyList() :
                BeanUtil.copyToList(pdfTemplates, CAPdfTemplateVo.class );
    }

    @Override
    public String createTemplatePdf(PdfTemplateCreateDto dto) {
        Assert.notNull(dto, "参数不能为空");
        String url = caConfig.getCreateTemplatePdfUrl();

        String bodyJson = JSON.toJSONString(dto);
        log.error("CA认证 生成模板文件， url：{}， body：{}", url, bodyJson);

        dto.setAppKey(caConfig.getAppKey());
        dto.setAppSecret(caConfig.getAppSecret());

        // 构建http请求
        HttpClient httpClient = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder(URI.create(url))
                .setHeader(HTTP.CONTENT_TYPE, ContentType.JSON.getValue())
                .POST(HttpRequest.BodyPublishers.ofString(bodyJson))
                .timeout(Duration.ofMillis(3000))
                .build();
        HttpResponse<String> response;
        try {
            response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        } catch (IOException | InterruptedException e) {
            log.error("CA认证 生成模板失败， url：{}， exception：{}", url, e.toString());
            throw new IllegalStateException("CA认证 生成模板失败");
        }
        JSONObject jsonObject = JSON.parseObject(response.body());
        verifyBody(jsonObject);

        JSONObject content = jsonObject.getJSONObject("content");
        String base64 = content.getString("pdfString");
        if(StringUtils.isBlank(base64)){
            throw new IllegalStateException("CA认证 生成模板失败");
        }
        return base64;

    }


    private void verifyBody(JSONObject jsonObject) {
        String code = jsonObject.getString("code");
        if (!Objects.equals("200", code)) {
            String message = jsonObject.getString("message");
            log.error("CA认证失败 {}", message);
            throw new IllegalStateException(String.format("CA认证失败 [%s]", message));
        }
    }


}
