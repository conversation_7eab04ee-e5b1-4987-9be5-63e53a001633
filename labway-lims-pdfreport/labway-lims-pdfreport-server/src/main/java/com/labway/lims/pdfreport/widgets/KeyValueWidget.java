package com.labway.lims.pdfreport.widgets;

import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.UnescapedText;
import j2html.tags.specialized.DivTag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import static j2html.TagCreator.div;
import static j2html.TagCreator.iff;

/**
 * intput key value 机构
 */
@Getter
@Setter
public class KeyValueWidget extends Widget {

    /**
     * 全角空格
     */
    static final String CHINESE_SPACE = "　";
    static final String HTML_SPACE = "&nbsp;";

    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final JSONObject options = getOriginal().getJSONObject("options");
        final DivTag kv = div().withClass("kv");

        // title
        kv.with(div().withClass("title").with(div().withClass("cn")
                .withClasses(iff(BooleanUtils.isTrue(options.getBoolean("labelBold")), "bold"))
                .with(new UnescapedText(StringUtils.replaceEach(StringUtils.defaultString(options.getString("label")),
                        new String[]{CHINESE_SPACE, StringUtils.SPACE}, new String[]{StringUtils.repeat(HTML_SPACE, 4), HTML_SPACE})))));

        // value
        final String defaultValue = options.getString("defaultValue");
        kv.with(div().withClass("value")
                .withClasses(iff(BooleanUtils.isTrue(options.getBoolean("fontBold")), "bold"))
                .condAttr(StringUtils.isNotBlank(defaultValue), "th:text", String.format("|%s|", defaultValue))
                .withText(defaultValue));

        return kv;
    }
}