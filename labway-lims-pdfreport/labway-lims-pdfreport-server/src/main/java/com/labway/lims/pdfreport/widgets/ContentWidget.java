package com.labway.lims.pdfreport.widgets;

import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.DivTag;
import lombok.Getter;
import lombok.Setter;

import static j2html.TagCreator.div;

/**
 * 内容
 */
@Getter
@Setter
public class ContentWidget extends Widget {
    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final DivTag content = div().withId("content");

        for (Widget child : getChildren()) {
            content.with(child.toTag(content));
        }

        return content;
    }
}