package com.labway.lims.pdfreport.widgets;

import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.UnescapedText;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

/**
 * 条形码
 */
@Getter
@Setter
public class QRCodeWidget extends Widget {


    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final String textContent = StringUtils.defaultString(getOriginal()
                .getJSONObject("options").getString("textContent"));

        return new UnescapedText(String.format("<qrcode th:text='|%s|'>%s</qrcode>", textContent,
                textContent));
    }


}

