package com.labway.lims.pdfreport.utils;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.base.Function;
import com.google.common.base.Predicate;
import org.apache.commons.lang3.ObjectUtils;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 在 thymeleaf 使用方法
 * <pre>
 *     <ul th:with="newList = ${T(com.google.common.collect.Collections2).filter(list,T(com.labway.lims.pdfreport.utils.Filters).propertyValueFilter('position','TYPE_ONE_FOURTH_ROW_CENTER'))}">
 *         <li></li>
 *     </ul>
 * </pre>
 * 在 idea 开发工具中便携 thymeleaf 脚本会有代码提示的
 */
@SuppressWarnings("all")
public class Filters {
    /**
     * 如果包含这个属性 那就是true
     */
    public static Predicate propertyFilter(String property) {
        return (Predicate<Object>) o -> {
            return BeanUtil.beanToMap(o).containsKey(property);
        };
    }


    /**
     * 如果包含这个属性 那就是true
     */
    public static Function propertyMap(String property) {
        return new Function<>() {
            @Nullable
            @Override
            public Object apply(@Nullable Object input) {
                return BeanUtil.beanToMap(input).get(property);
            }
        };
    }


    /**
     * 删除为空的属性
     */
    public static List<Object> removeEmpyt(Object object) {
        final List<Object> list = new ArrayList<>();
        if (object.getClass().isArray()) {
            for (Object e : (Object[]) object) {
                if (ObjectUtils.isNotEmpty(e)) {
                    list.add(e);
                }
            }
        } else if (object instanceof Collection) {
            for (Object e : (Collection) object) {
                if (ObjectUtils.isNotEmpty(e)) {
                    list.add(e);
                }
            }
        }
        return list;
    }


    /**
     * 如果包含这个属性 那就是true
     */
    public static Predicate propertyValueFilter(String property, Object value) {
        return (Predicate<Object>) o -> {
            return Objects.equals(BeanUtil.beanToMap(o).get(property), value);
        };
    }

}
