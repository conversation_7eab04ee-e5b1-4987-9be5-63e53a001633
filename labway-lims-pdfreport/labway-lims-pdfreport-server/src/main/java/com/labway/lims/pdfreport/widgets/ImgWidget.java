package com.labway.lims.pdfreport.widgets;

import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.ImgTag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

import static j2html.TagCreator.div;
import static j2html.TagCreator.img;

/**
 * 图片
 */
@Getter
@Setter
public class ImgWidget extends Widget {


    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final JSONObject options = getOriginal().getJSONObject("options");
        final String src = options.getString("picturePath");

        final Map<CssStyleDeclaration, String> rowCss = new HashMap<>();

        final Map<CssStyleDeclaration, String> imgCss = new HashMap<>(Map.of(CssStyleDeclaration.HEIGHT,
                ObjectUtils.defaultIfNull(options.getString("height"), "1") + "rem"));

        final ImgTag img = img().withSrc(StringUtils.defaultString(src));

        if (BooleanUtils.isTrue(options.getBoolean("float"))) {
            rowCss.put(CssStyleDeclaration.POSITION, "relative");
            imgCss.put(CssStyleDeclaration.POSITION, "absolute");
            final Double translateX = ObjectUtils.defaultIfNull(options.getDouble("translateX"), 0D);
            final Double translateY = ObjectUtils.defaultIfNull(options.getDouble("translateY"), 0D);
            imgCss.put(CssStyleDeclaration.TOP, translateY + "rem");
            imgCss.put(CssStyleDeclaration.LEFT, translateX + "rem");
        } else {
            // 没有浮动就定死 row 的高度
            rowCss.put(CssStyleDeclaration.HEIGHT,
                    ObjectUtils.defaultIfNull(options.getString("height"), "1") + "rem");
        }

        return div().withClass("row")
                .withStyle(cssStyleFromMap(rowCss))
                .with(div().withClasses("column", "flex")
                        .with(img.withStyle(cssStyleFromMap(imgCss))));
    }
}