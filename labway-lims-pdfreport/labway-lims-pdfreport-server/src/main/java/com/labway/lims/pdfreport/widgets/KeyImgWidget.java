package com.labway.lims.pdfreport.widgets;

import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.DivTag;
import lombok.Getter;
import lombok.Setter;

import static j2html.TagCreator.div;

/**
 * intput key value 机构
 */
@Getter
@Setter
public class KeyImgWidget extends Widget {


    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final DivTag kv = div().withClasses("kv", "key-img");

        final JSONObject options = getOriginal().getJSONObject("options");
        options.put("textContent", options.getString("label"));

        final StaticTextWidget staticTextWidget = new StaticTextWidget();
        staticTextWidget.setType("static-text");
        staticTextWidget.setOriginal(getOriginal());

        // title
        kv.with(div().withClass("title").with(div().withClass("cn")
                .with(staticTextWidget.toTag(kv))));

        // value
        final ImgWidget imgWidget = new ImgWidget();
        imgWidget.setType("picture");
        imgWidget.setOriginal(getOriginal());
        kv.with(div().withClass("value").with(imgWidget.toTag(kv)));

        return kv;
    }
}