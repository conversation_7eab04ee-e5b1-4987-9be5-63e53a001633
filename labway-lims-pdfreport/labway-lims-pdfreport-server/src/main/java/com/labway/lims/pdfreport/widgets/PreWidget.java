package com.labway.lims.pdfreport.widgets;

import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.specialized.PreTag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.BooleanUtils;

import static j2html.TagCreator.iff;
import static j2html.TagCreator.pre;

/**
 * 文本域
 */
@Getter
@Setter
public class PreWidget extends StaticTextWidget {

    @Override
    protected ContainerTag<?> createTag(JSONObject options) {

        final PreTag staticText = pre();

        staticText.withClasses(
                "textarea-text",
                iff(BooleanUtils.isTrue(options.getBoolean("hidden")), "hidden")
        );

        return staticText;
    }
}

