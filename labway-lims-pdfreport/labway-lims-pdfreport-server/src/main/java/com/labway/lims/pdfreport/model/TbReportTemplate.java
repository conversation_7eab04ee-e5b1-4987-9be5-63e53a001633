package com.labway.lims.pdfreport.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 模版
 */
@Getter
@Setter
@TableName("tb_report_template2")
public class TbReportTemplate {

    /**
     * ID
     */
    @TableId
    private Long reportTemplateId;

    /**
     * 编码
     */
    private String reportTemplateCode;

    /**
     * 名称
     */
    private String reportTemplateName;

    /**
     * 内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 是否删除
     * @see  com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer isDelete;

}
