package com.labway.lims.pdfreport.widgets;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.BodyTag;
import j2html.tags.specialized.HtmlTag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

import static j2html.TagCreator.*;

/**
 * 分割线
 */
@Getter
@Setter
public class PageWidget extends Widget {

    private final FormConfig formConfig;

    public PageWidget(FormConfig formConfig) {
        this.formConfig = formConfig;

        formConfig.setReferenceSize(ObjectUtils.defaultIfNull(formConfig.getReferenceSize(), 3.0D));

    }

    public static PageWidget from(String struct) {
        final JSONObject json = JSON.parseObject(struct);

        final FormConfig formConfig = json.getJSONObject("formConfig").toJavaObject(FormConfig.class);
        if (!Objects.equals(formConfig.getJsonVersion(), FormConfig.JSON_VERSION)) {
            throw new IllegalStateException("jsonVersion != " + FormConfig.JSON_VERSION);
        }

        final PageWidget pageWidget = new PageWidget(formConfig);

        parseWidget(json.getJSONArray("widgetList"), pageWidget);

        return pageWidget;
    }

    public static void parseWidget(JSONArray json, Widget parent) {
        for (int i = 0; i < json.size(); i++) {
            final JSONObject j = json.getJSONObject(i);
            final Widget widget = getWidget(j);

            JSONArray children = j.getJSONArray("widgetList");

            if (CollectionUtils.isEmpty(children)) {
                if (widget instanceof RowWidget) {
                    children = j.getJSONArray("cols");
                }
            }

            if (CollectionUtils.isNotEmpty(children)) {
                parseWidget(children, widget);
            }

            parent.getChildren().add(widget);
        }
    }

    private static Widget getWidget(JSONObject json) {
        final String type = json.getString("type");


        final Class<? extends Widget> clazz = Widget.DEFINE_CHILDREN.get(type);
        if (Objects.isNull(clazz)) {
            throw new IllegalStateException(String.format("无法识别 %s", type));
        }

        final Widget widget = json.toJavaObject(clazz);
        widget.setOriginal(json);
        widget.setType(type);


        return widget;
    }


    @Override
    public DomContent toTag(ContainerTag<?> parent) {

        final BodyTag body = body()
                .withClasses(
                        iff(BooleanUtils.isTrue(formConfig.getDebugger()), "debugger"),
                        getClass().getSimpleName() + IdUtil.objectId()
                );

        final List<Widget> children = new LinkedList<>();
        final ArrayList<Widget> widgets = new ArrayList<>(getChildren());

        // header 第一个
        widgets.removeIf(e -> {
            if (e instanceof HeaderWidget) {
                children.add(e);
                return true;
            }
            return false;
        });

        // footer 第二个
        widgets.removeIf(e -> {
            if (e instanceof FooterWidget) {
                children.add(e);
                return true;
            }
            return false;
        });

        children.addAll(widgets);

        for (Widget child : children) {
            body.with(child.toTag(body));
        }

        return body;
    }

    public String toHtml() {
        final HtmlTag html = html().attr("lang", "zh-CN")
                .attr("xmlns:th", "http://www.thymeleaf.org");

        html.with(head()
                .with(meta().withCharset("UTF-8"))
                .with(title(StringUtils.defaultString(formConfig.getTitle())))
                .with(link().withHref("../less/styles.css").withRel("stylesheet"))
                .with(style(String.format("html{font-size:%smm}", formConfig.getReferenceSize())))
                .with(style(getScreenStyle()).withMedia("screen"))
                .with(style(getPrintStyle()).withMedia("print"))
                .with(style(StringUtils.defaultString(formConfig.getCssCode())))
                .with(style(getPagePaddingStyle()))
        );

        html.with(toTag(html));


        return html.renderFormatted();
    }

    String getScreenStyle() {
        final PageSize layoutType = formConfig.getLayoutType();
        final StringBuilder sb = new StringBuilder();
        if (layoutType == PageSize.A4 || layoutType == PageSize.A5_LANDSCAPE) {
            sb.append("body,.limited-paper-size,#footer{width:210mm;}");
        } else if (layoutType == PageSize.A4_LANDSCAPE) {
            sb.append("body,.limited-paper-size,#footer{width:297mm;}");
        } else if (layoutType == PageSize.A5) {
            sb.append("body,.limited-paper-size,#footer{width:148mm;}");
        }
        sb.append("body{margin:0 auto;}");
        return sb.toString();
    }

    String getPrintStyle() {
        final PageSize layoutType = formConfig.getLayoutType();
        final StringBuilder sb = new StringBuilder();
        if (layoutType == PageSize.A4) {
            sb.append("@page{size:A4;}");
        } else if (layoutType == PageSize.A4_LANDSCAPE) {
            sb.append("@page{size:A4 landscape;}");
        } else if (layoutType == PageSize.A5) {
            sb.append("@page{size:A5;}");
        } else if (layoutType == PageSize.A5_LANDSCAPE) {
            sb.append("@page{size:A5 landscape;}");
        }
        sb.append("@page{margin:0;}");
        return sb.toString();
    }

    String getPagePaddingStyle() {
        final StringBuilder sb = new StringBuilder();

        sb.append(String.format("#header,#footer,#content{border-left:%srem solid #fff;border-right:%srem solid #fff}",
                formConfig.getMarginLeft(), formConfig.getMarginRight()));

        sb.append(String.format("#header{border-top:%srem solid #fff;}", formConfig.getMarginTop()));

        sb.append(String.format("#footer{border-bottom:%srem solid #fff;}", formConfig.getMarginBottom()));

        return sb.toString();
    }
}