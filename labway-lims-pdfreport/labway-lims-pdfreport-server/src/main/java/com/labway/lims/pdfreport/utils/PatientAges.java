package com.labway.lims.pdfreport.utils;

import cn.hutool.core.lang.Dict;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class PatientAges {
    public static String toText(Object object) {

        if (Objects.isNull(object)) {
            return StringUtils.EMPTY;
        }

        final Dict dict = Dict.parse(object);

        if (!(dict.containsKey("patientAge")
                && dict.containsKey("patientSubage")
                && dict.containsKey("patientSubageUnit"))) {
            return StringUtils.EMPTY;
        }

        final Integer patientAge = dict.getInt("patientAge");
        final Integer patientSubage = dict.getInt("patientSubage");
        final String patientSubageUnit = dict.getStr("patientSubageUnit");

        final List<String> list = new ArrayList<>();
        if (Objects.nonNull(patientAge) && patientAge > 0) {
            list.add(String.valueOf(patientAge));
            list.add("岁");
        }


        if (Objects.nonNull(patientSubage) && patientSubage > 0) {
            list.add(String.valueOf(patientSubage));
            list.add(StringUtils.defaultString(patientSubageUnit));
        }

        if (list.isEmpty()) {
            list.add("0岁");
        }

        return StringUtils.join(list, StringUtils.EMPTY);

    }
}
