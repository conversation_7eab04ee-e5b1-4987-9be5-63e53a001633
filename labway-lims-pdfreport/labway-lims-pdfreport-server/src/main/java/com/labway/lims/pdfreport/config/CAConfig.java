package com.labway.lims.pdfreport.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component("caConfig")
@ConfigurationProperties(prefix = "ca")
public class CAConfig {
    /**
     * ca key
     */
    private String appKey = "";
    /**
     * ca secret
     */
    private String appSecret = "";
    /**
     * url前缀
     */
    private String urlPrefix = "";


    public String getSealOpen() {
        return urlPrefix + "/sealOpen";
    }

    /**
     * 服务名 前缀
     */
    public String getServicePrefix() {
        return getSealOpen() + "/sealServer";
    }

    /**
     * 获取可用章url
     */
    public String getSealUrl() {
        return getServicePrefix() + "/obtainAvailableSeal";
    }

    /**
     * pdf 静默签署url
     */
    public String getPdfQuiesceUrl() {
        return getServicePrefix() + "/silenceSign";
    }

    /**
     * odf 静默签署url
     */
    public String getOfdQuiesceUrl() {
        return getServicePrefix() + "/ofdSilenceSign";
    }

    /**
     * 事件证书签署
     */
    public String getEventCertificateSigningUrl() {
        return getServicePrefix() + "/shortTermCertSilenceSign";
    }

    /**
     * 签署文档下载
     */
    public String getDownloadUrl() {
        return getSealOpen() +  "/updownload/download";
    }

    /**
     * 验签
     */
    public String getVerifySignUrl() {
        return getSealOpen() + "/verifySign/Sign";
    }

    /**
     * ukey签
     */
    public String getUkeySignUrl() {
        return getSealOpen() + "/upload/thumbnail";
    }

    /**
     * 手写签名版
     */
    public String getSignPictureUrl() {
        return getSealOpen() + "/signPicture/url";
    }

    /**
     * 获取签名版信息
     */
    public String getSignPictureDetailUrl() {
        return getSealOpen() + "/signPicture/detail";
    }

    /**
     * 获取签名板图片base64
     */
    public String getBase64PictureDetailUrl() {
        return getSealOpen() + "/signPicture/pictureDetail";
    }

    /**
     * PDF生成待签原文HASH
     */
    public String getPdfOriginalHashUrl() {
        return getSealOpen() + "/sign/createSignHash";
    }

    /**
     * PDF装配签名
     */
    public String getPdfAssenbleSignUrl() {
        return getSealOpen() + "/sign/assemble";
    }

    /**
     * 健康检查心跳接口
     */
    public String getPingUrl() {
        return getSealOpen() + "/health/ping";
    }

    /**
     * 数字签名
     */
    public String getNumberSignUrl() {
        return getSealOpen() + "/sign/digitalSignature";
    }

    /**
     * 签名验证
     */
    public String getSignVerifyUrl() {
        return getSealOpen() + "/sign/verifySignature";
    }

    /**
     * 获取签署模版
     */
    public String getTemplateUrl() {
        return getSealOpen() + "/template/templateList";
    }

    /**
     * 生成签署模版文件
     */
    public String getCreateTemplatePdfUrl() {
        return getSealOpen() + "/template/createTemplatePdf";
    }

    /**
     * 发起页面签
     */
    public String getPageSignUrl() {
        return getSealOpen() + "/upload/pageSign";
    }
}
