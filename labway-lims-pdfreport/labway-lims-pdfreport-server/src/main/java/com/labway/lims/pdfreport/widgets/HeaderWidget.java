package com.labway.lims.pdfreport.widgets;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.DivTag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import javax.swing.text.Style;
import java.util.Map;

import static j2html.TagCreator.div;
import static j2html.TagCreator.style;

/**
 * 页眉
 */
@Getter
@Setter
public class HeaderWidget extends Widget {


    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final DivTag header = div().withId("header");


        for (Widget child : getChildren()) {
            header.with(child.toTag(header));
        }

        final JSONObject options = getOriginal().getJSONObject("options");
        if (BooleanUtils.isTrue(options.getBoolean("showPagination"))) {
            header.with(getPageNoTag());
        }

        if (BooleanUtils.isTrue(options.getBoolean("dynamicHeight"))) {
            header.attr("dynamic-height");
        } else {
            header.withStyle(cssStyleFromMap(Map.of(CssStyleDeclaration.HEIGHT,
                    StringUtils.defaultString(options.getString("dynamicHeight"), "5"))));
        }

        return header;
    }

    DomContent getPageNoTag() {
        final JSONObject options = getOriginal().getJSONObject("options");

        final DivTag pageNo = div().withId("page-no").withClasses("text-right")
                .withStyle(cssStyleFromMap(Map.of(
                        CssStyleDeclaration.POSITION, "absolute",
                        CssStyleDeclaration.TOP, StringUtils.defaultString(options.getString("paginationTop"), "0") + "rem",
                        CssStyleDeclaration.RIGHT, StringUtils.defaultString(options.getString("paginationRight"), "0") + "rem"
                )));

        final String className = Style.class.getSimpleName() + IdUtil.objectId();

        pageNo.with(style(String.format(".%s::after {content: '第' counter(page) '页 共' counter(pages) '页';}", className)));
        pageNo.with(div().withClasses(className, "page-no-text"));

        return pageNo;
    }
}
