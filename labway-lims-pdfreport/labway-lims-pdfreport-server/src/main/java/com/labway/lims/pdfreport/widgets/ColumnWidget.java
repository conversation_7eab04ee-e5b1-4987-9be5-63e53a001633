package com.labway.lims.pdfreport.widgets;

import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.DivTag;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

import static j2html.TagCreator.div;

/**
 * 列
 */
@Getter
@Setter
public class ColumnWidget extends Widget {


    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final DivTag row = div().withClasses("column");

        final String span = getOriginal().getJSONObject("options").getString("span");
        row.withStyle(cssStyleFromMap(Map.of(CssStyleDeclaration.FLEX, span)));

        for (Widget child : getChildren()) {
            row.with(child.toTag(row));
        }

        return row;
    }
}