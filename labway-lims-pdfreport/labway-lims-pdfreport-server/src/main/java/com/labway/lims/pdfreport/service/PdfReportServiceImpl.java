package com.labway.lims.pdfreport.service;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpDownloader;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfDocumentInfo;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.font.FontProvider;
import com.itextpdf.styledxmlparser.css.media.MediaDeviceDescription;
import com.itextpdf.styledxmlparser.css.media.MediaType;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.exception.BuildPdfLimsException;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.dto.ReportTemplateDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.pdfreport.api.service.ReportTemplateService;
import com.labway.lims.pdfreport.tags.TagCssApplierFactory;
import com.labway.lims.pdfreport.tags.TagWorkerFactory;
import com.teamdev.jxbrowser.browser.Browser;
import com.teamdev.jxbrowser.engine.Engine;
import com.teamdev.jxbrowser.engine.EngineOptions;
import com.teamdev.jxbrowser.engine.Language;
import com.teamdev.jxbrowser.engine.event.EngineCrashed;
import com.teamdev.jxbrowser.frame.Frame;
import com.teamdev.jxbrowser.js.JsObject;
import com.teamdev.jxbrowser.logging.Level;
import com.teamdev.jxbrowser.logging.Logger;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.util.Strings;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.jetbrains.annotations.NotNull;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.io.ClassPathResource;
import org.springframework.expression.spel.SpelEvaluationException;
import org.springframework.expression.spel.SpelMessage;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.exceptions.TemplateProcessingException;
import org.thymeleaf.spring5.SpringTemplateEngine;
import org.thymeleaf.spring5.dialect.SpringStandardDialect;
import org.thymeleaf.spring5.expression.SPELVariableExpressionEvaluator;
import org.thymeleaf.standard.expression.IStandardVariableExpressionEvaluator;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.StringTemplateResolver;
import rx.Observable;
import rx.schedulers.Schedulers;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import javax.swing.text.html.HTML;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.teamdev.jxbrowser.engine.RenderingMode.HARDWARE_ACCELERATED;

@Slf4j
@DubboService
class PdfReportServiceImpl implements PdfReportService, InitializingBean, DisposableBean,
        ApplicationListener<ServletWebServerInitializedEvent> {

    @Value("${spring.application.name}")
    private String applicationName;

    @Resource
    private EnvDetector envDetector;

    @Value("${jxbrowser.key}")
    private String jxbrowserKey;

    private int serverPort;

    private static final String ABOUT_BLANK = "about:blank";

    private final Map<String, byte[]> resources = new HashMap<>();

    @Resource
    private ReportTemplateService reportTemplateService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    private GenericObjectPool<Browser> pool;
    private Engine jxBrowserEngine;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    private String style;
    private String fontDir;
    private TemplateEngine engine;

    @SneakyThrows
    @Override
    @Trace
    public byte[] build(String code, PdfReportParamDto pdfReportParam) {
        try (final ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            build(code, pdfReportParam, baos);
            return baos.toByteArray();
        }
    }

    private void build(String code, PdfReportParamDto pdfReportParam, OutputStream os) {

        log.info("生成报告 编码 [{}] 参数 [{}]", code, JSON.toJSONString(pdfReportParam));

        try {
            final StopWatch watch = new StopWatch(String.format("生成 PDF [%s]", code));

            watch.start("获取模板");
            final ReportTemplateDto reportTemplate = reportTemplateService.selectByReportTemplateCode(code);
            if (Objects.isNull(reportTemplate)) {
                throw new IllegalStateException(String.format("模板 [%s] 不存在", code));
            }
            watch.stop();

            watch.start("生成 HTML");
            // 生成 HTML
            final String html = buildHtml(reportTemplate.getContent(), pdfReportParam);
            watch.stop();

            final ConverterProperties properties = new ConverterProperties();
            final FontProvider fontProvider = new FontProvider();
            fontProvider.addDirectory(fontDir);
            properties.setFontProvider(fontProvider);
            properties.setMediaDeviceDescription(new MediaDeviceDescription(MediaType.PRINT));

            properties.setTagWorkerFactory(new TagWorkerFactory());
            properties.setCssApplierFactory(new TagCssApplierFactory());

            watch.start("生成 PDF");
            try (final PdfWriter pdfWriter = new PdfWriter(os);
                 final PdfDocument pdfDocument = new PdfDocument(pdfWriter)) {

                final PdfDocumentInfo documentInfo = pdfDocument.getDocumentInfo();
                documentInfo.setAuthor("Labway Inc.");
                documentInfo.setKeywords(code);

                try (final Document doc = HtmlConverter.convertToDocument(html, pdfDocument, properties)) {
                    doc.add(new AreaBreak());
                }

            }

            watch.stop();

            log.info("生成 PDF [{}] 耗时:\n{}", code, watch.prettyPrint(TimeUnit.MILLISECONDS));

        } catch (Exception e) {
            log.error("模版编码 [{}] 构建失败。原因: {}", code, e.getMessage(), e);
            throw new BuildPdfLimsException(String.format("生成报告失败，请检查模版内容是否正确。模版编码 [%s]", code), e);
        }
    }

    @Override
    @Trace
    public String build2Url(String code, PdfReportParamDto pdfReportParam) {
        return build2Url(code, pdfReportParam, HuaweiObsUtils.FOREVER);
    }

    @Trace
    @Override
    @SneakyThrows
    public String build2Url(String code, PdfReportParamDto pdfReportParam, int expires) {
        final File file = FileUtil.createTempFile();

        try {

            try (FileOutputStream fos = new FileOutputStream(file)) {
                build(code, pdfReportParam, fos);
            }

            try (FileInputStream fis = new FileInputStream(file)) {
                return huaweiObsUtils.upload(fis,
                        org.springframework.http.MediaType.APPLICATION_PDF_VALUE, expires);
            }

        } finally {
            FileUtils.deleteQuietly(file);
        }

    }

    @Trace
    private String buildHtml(String template, PdfReportParamDto pdfReportParam) throws Exception {

        final Context context = new Context();
        for (Map.Entry<String, Object> e : pdfReportParam.entrySet()) {
            context.setVariable(Objects.requireNonNull(e.getKey(), "key"),
                    Objects.requireNonNull(e.getValue(), "value"));
        }

        final var document = Jsoup.parse(engine.process(template, context));

        // 插入样式
        Objects.requireNonNull(document.selectFirst("head"), "模板缺少 head 标签").insertChildren(0,
                new Element(HTML.Tag.STYLE.toString()).text(style));

        // 删除 link 、 script 标签
        document.select("link,script").forEach(Element::remove);
        document.outputSettings().prettyPrint(false);

        if (Objects.isNull(pool)) {
            log.info("JxBrowser 池尚未初始化，跳过动态计算元素");
            return document.outerHtml();
        }

        // 如果没有开启动态高度
        if (CollectionUtils.isEmpty(document.select("[dynamic-height]"))) {
            return document.outerHtml();
        }

        // 替换 img 标签
        // replaceHttp2Base64(document.select("img"));

        final Browser browser = pool.borrowObject();
        final String now = LocalDate.now().toString();
        final File file =
                FileUtils.getFile(SystemUtils.getJavaIoTmpDir(), "jxbrowser-html", now, IdUtil.simpleUUID() + ".html");
        FileUtils.forceMkdirParent(file);
        try (final OutputStream os = Files.newOutputStream(file.toPath())) {

            log.info("开始渲染 HTML 来动态计算高度 browser [{}]", browser);

            final StopWatch watch = new StopWatch();
            watch.start();

            // 写入到本地
            IOUtils.write(document.outerHtml(), os, StandardCharsets.UTF_8);

            final String url =
                    String.format("http://127.0.0.1:%s/jxbrowser-html/%s/%s", serverPort, now, file.getName());

            log.info("动态计算的 HTML {}", url);

            // 加载 html
            browser.navigation().loadUrlAndWait(url, Duration.ofSeconds(5));

            // 打印 Debugger 地址
            if (envDetector.isDev()) {
                browser.devTools().remoteDebuggingUrl()
                        .ifPresent(e -> log.info("browser [{}] DevTools Url {}", browser, e));
            }

            final Frame mainFrame = browser.mainFrame().orElse(null);
            if (Objects.nonNull(mainFrame)) {
                final String js =
                        new String(getClassResource("template/injectjs/dynamic-height.js"), StandardCharsets.UTF_8);
                final JsObject o = mainFrame.executeJavaScript(js);
                if (Objects.nonNull(o)) {
                    if (o.hasProperty("header")) {
                        final Object h = o.property("header").orElse(0);
                        document.head().appendChild(new Element(HTML.Tag.STYLE.toString())
                                .text(String.format("@media print{@page{margin-top:%spx;}}", h)));
                        log.info("browser [{}] 动态算出页眉高度 {}px", browser, h);
                    }

                    if (o.hasProperty("footer")) {
                        final Object h = o.property("footer").orElse(0);
                        document.head().appendChild(new Element(HTML.Tag.STYLE.toString())
                                .text(String.format("@media print{@page{margin-bottom:%spx;}}", h)));
                        log.info("browser [{}] 动态算出页脚高度 {}px", browser, h);
                    }

                    o.close();
                }

            }

            watch.stop();

            log.info("browser [{}] 加载 HTML 完毕 耗时 [{}ms]", browser, watch.getTotalTimeMillis());

        } catch (Exception e) {
            log.error("browser [{}] 渲染 HTML 失败 HTML [{}]", browser, document.outerHtml(), e);
            throw e;
        } finally {
            pool.returnObject(browser);
            if (!(envDetector.isDev() || envDetector.isTest())) {
                FileUtils.deleteQuietly(file);
            }
        }

        try (final OutputStream os = Files.newOutputStream(file.toPath())) {
            // 写入到本地
            IOUtils.write(document.outerHtml(), os, StandardCharsets.UTF_8);
        }

        return document.outerHtml();
    }

    private void replaceHttp2Base64(Elements elements)
            throws ExecutionException, InterruptedException, TimeoutException {

        if (CollectionUtils.isEmpty(elements)) {
            return;
        }

        final List<Element> els = new ArrayList<>();

        elements.forEach(img -> {
            String src = img.attr("src");
            if (StringUtils.isNotBlank(src) && (HttpUtil.isHttp(src) || HttpUtil.isHttps(src))) {
                els.add(img);
            }
        });

        if (CollectionUtils.isEmpty(els)) {
            return;
        }

        final List<Future<Object>> futures = new ArrayList<>();
        ListUtils.partition(els, 5).forEach(e -> {
            futures.add(threadPoolConfig.getPool().submit(() -> {
                for (Element img : e) {
                    String src = img.attr("src").replace("obs.labway.cn",
                            huaweiObsUtils.getBucketName() + "." + huaweiObsUtils.getEndPoint());
                    final StopWatch watch = new StopWatch();
                    watch.start();
                    img.attr("src", "data:image;base64," + Base64.encodeBase64String(HttpUtil.downloadBytes(src)));
                    watch.stop();
                    log.info("Downloading src: {} total {}ms", src, watch.getTotalTimeMillis());
                }
                return null;
            }));
        });

        for (Future<Object> future : futures) {
            future.get(10, TimeUnit.SECONDS);
        }

    }

    @Override
    public List<ReportTemplateDto> selectAllTemplate() {
        return reportTemplateService.selectAll();
    }


    @Nonnull
    @Trace
    @Override
    public byte[] mergePdf(Collection<byte[]> pdfList) {
        // 如果没有数据则返回 byte[0]
        if (CollectionUtils.isEmpty(pdfList)) {
            return new byte[ 0 ];
        }
        // 如果只有一个直接返回
        if (pdfList.size() == NumberUtils.INTEGER_ONE) {
            return pdfList.iterator().next();
        }

        File pdf = null;
        try {
            pdf = FileUtil.createTempFile();

            final PDFMergerUtility merger = new PDFMergerUtility();
            StopWatch stopWatch = new StopWatch("合并PDF文件");
            stopWatch.start();

            for (byte[] pdfStream : pdfList) {
                merger.addSource(new ByteArrayInputStream(pdfStream));
            }

            final PDDocumentInformation information = new PDDocumentInformation();
            information.setKeywords("合并PDF");
            merger.setDestinationFileName(pdf.getAbsolutePath());
            merger.setDestinationDocumentInformation(information);
            merger.mergeDocuments(MemoryUsageSetting.setupTempFileOnly());

            try (final FileInputStream fis = new FileInputStream(pdf)) {
                stopWatch.stop();
                log.info("合并PDF模板完毕 耗时 [{}ms]", stopWatch.getTotalTimeMillis());
                return fis.readAllBytes();
            }

        } catch (Exception e) {
            log.error("合并PDF模板失败：{}", e.toString());
            throw new BuildPdfLimsException("合并PDF模板失败", e);
        } finally {
            if (Objects.nonNull(pdf)) {
                FileUtils.deleteQuietly(pdf);
            }
        }
    }

    @Nonnull
    @Override
    public String mergePdf2Url(Collection<byte[]> pdfList) {
        return mergePdf2Url(pdfList, HuaweiObsUtils.FOREVER);
    }

    @Trace
    @Nonnull
    @Override
    public String mergePdf2Url(Collection<byte[]> pdfList, int expires) {
        final byte[] bytes = this.mergePdf(pdfList);
        if (bytes.length == NumberUtils.LONG_ZERO) {
            return Strings.EMPTY;
        }

        try (InputStream fis = new ByteArrayInputStream(bytes)) {
            return huaweiObsUtils.upload(fis,
                    org.springframework.http.MediaType.APPLICATION_PDF_VALUE, expires);
        } catch (Exception e) {
            log.error("合并PDF模板上传OBS失败：{}", e.toString());
            throw new BuildPdfLimsException("合并PDF模板上传OBS失败", e);
        }
    }

	/**
	 * 合并pdf
	 *
	 * @param pdfList 多个pdf oss文件地址
	 * @return 合并后的pdf字节数组
	 */
	@Override
	@SneakyThrows
	public byte[] mergePdfByUrls(Collection<String> pdfList) {
		return mergePdf(getPdfBytesByUrls(pdfList));
	}

	/**
	 * 合并pdf
	 *
	 * @param pdfList 多个pdf oss文件地址
	 * @return 合并后的pdf oss地址
	 */
	@Override
	public String mergePdfByUrls2Url(Collection<String> pdfList) {
		return mergePdf2Url(getPdfBytesByUrls(pdfList));
	}

	/**
	 * 合并pdf
	 *
	 * @param pdfList 多个pdf oss文件地址
	 * @param expires 过期时间，天数。如果是 1 那么 1 天后对象会被清理掉，-1 表示永久。通常临时生成的文件会设置一个过期时间。
	 * @return 合并后的pdf oss地址
	 */
	@Override
	public String mergePdfByUrls2Url(Collection<String> pdfList, int expires) {
		return mergePdf2Url(getPdfBytesByUrls(pdfList), expires);
	}

	private @NotNull List<byte[]> getPdfBytesByUrls(Collection<String> pdfList) {
		return pdfList.parallelStream().map(url -> {
					try {
                        return HttpDownloader.downloadBytes(url);
					} catch (Exception e) {
						log.error("下载文件 {} 失败", url,e);
						throw new IllegalStateException(String.format("下载文件 [%s] 失败", url));
					}
				}
		).collect(Collectors.toList());
	}

	private String getSourceHanSerifFontDirectory() throws IOException {
        final File dir = new File(SystemUtils.getJavaIoTmpDir(), applicationName + UUID.randomUUID());
        if (!dir.exists() && !dir.mkdirs()) {
            throw new IllegalStateException(String.format("创建文件夹 [%s] 失败", dir.getAbsolutePath()));
        }

        final InputStream fontLight = Objects
                .requireNonNull(PdfReportServiceImpl.class.getResourceAsStream("/fonts/AlibabaPuHuiTi-3-45-Light.ttf"));
        final InputStream fontBold = Objects
                .requireNonNull(PdfReportServiceImpl.class.getResourceAsStream("/fonts/AlibabaPuHuiTi-3-85-Bold.ttf"));

        try (OutputStream os1 = Files.newOutputStream(new File(dir, "AlibabaPuHuiTi-3-45-Light.ttf").toPath());
             final OutputStream os2 = Files.newOutputStream(new File(dir, "AlibabaPuHuiTi-3-85-Bold.ttf").toPath())) {
            IOUtils.copy(fontLight, os1);
            IOUtils.copy(fontBold, os2);
        }

        Runtime.getRuntime().addShutdownHook(new Thread(() -> FileUtils.deleteQuietly(dir)));

        return dir.getAbsolutePath();
    }

    private synchronized void initJxBrowserPool() {

        log.info("开始初始化 JxBrowser 引擎...");

        Logger.level(Level.INFO);

        final EngineOptions.Builder builder =
                EngineOptions.newBuilder(HARDWARE_ACCELERATED).licenseKey(jxbrowserKey).remoteDebuggingPort(1222)
                        .allowFileAccessFromFiles().enableIncognito().disableWebSecurity().language(Language.CHINESE);

        jxBrowserEngine = Engine.newInstance(builder.build());

        log.info("初始化 JxBrowser 引擎成功");

        final GenericObjectPoolConfig<Browser> config = new GenericObjectPoolConfig<>();
        // 对象池中最多允许的对象数
        config.setMaxTotal(20);
        // 对象池中最多允许存在的空闲对象
        config.setMaxIdle(10);
        // 池中最少要保留的对象数
        config.setMinIdle(3);
        // 从池中获取一个对象最长的等待时间，默认-1，含义是无限等，超过这个时间还未获取空闲对象，就会抛出异常。
        config.setMaxWaitMillis(TimeUnit.SECONDS.toMillis(10));
        // 最小的驱逐时间，单位毫秒，默认30分钟。这个用于驱逐线程，对象空闲时间超过这个时间，意味着此时系统不忙碌，会减少对象数量
        config.setMinEvictableIdleTimeMillis(TimeUnit.MINUTES.toMillis(5));
        // 一分钟驱逐一次
        config.setTimeBetweenEvictionRunsMillis(TimeUnit.MINUTES.toMillis(1));
        // 在创建对象的时候是否检测对象，默认false
        config.setTestOnCreate(true);
        // 在获取空闲对象的时候是否检测对象是否有效，默认false
        config.setTestOnBorrow(true);
        // 在空闲的时候是否检测对象是否有效
        config.setTestWhileIdle(true);

        if (Objects.nonNull(pool)) {
            try {
                pool.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        pool = new GenericObjectPool<>(new BrowserPooledObjectFactory(jxBrowserEngine), config);

        jxBrowserEngine.on(EngineCrashed.class, e -> {
            log.error("Engine 崩溃 ， 错误码 {} ，一秒后将会重新初始化", e.exitCode());
            Observable.timer(1, TimeUnit.SECONDS).observeOn(Schedulers.computation())
                    .subscribe(aLong -> initJxBrowserPool());
        });

        log.info("初始化 JxBrowser 池成功");

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.fontDir = getSourceHanSerifFontDirectory();

        engine = new SpringTemplateEngine();
        // 重写解析器，当遇到未知变量时不报错
        engine.setDialect(new SpringStandardDialect() {

            @Override
            public IStandardVariableExpressionEvaluator getVariableExpressionEvaluator() {

                return (context, expression, expContext) -> {
                    final Object evaluate;
                    try {
                        evaluate = SPELVariableExpressionEvaluator.INSTANCE.evaluate(context, expression, expContext);
                    } catch (TemplateProcessingException e) {
                        if (e.getCause() instanceof SpelEvaluationException) {
                            final SpelMessage messageCode = ((SpelEvaluationException) e.getCause()).getMessageCode();
                            if (messageCode == SpelMessage.PROPERTY_OR_FIELD_NOT_READABLE_ON_NULL
                                    || messageCode == SpelMessage.CANNOT_INDEX_INTO_NULL_VALUE
                                    || messageCode == SpelMessage.PROPERTY_OR_FIELD_NOT_READABLE) {
                                log.warn(e.getMessage(), e);
                                return expression;
                            }
                        }
                        throw e;
                    }
                    return evaluate;
                };
            }
        });
        final StringTemplateResolver resolver = new StringTemplateResolver();
        resolver.setTemplateMode(TemplateMode.HTML);
        engine.setTemplateResolver(resolver);

        final ClassPathResource resource = new ClassPathResource("less/styles.css");
        if (!resource.exists()) {
            throw new IllegalStateException("less/styles.css 文件不存在");
        }

        style = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);

        // 初始化浏览器池
        initJxBrowserPool();
    }

    @Override
    public void destroy() throws Exception {
        FileUtils.deleteQuietly(new File(fontDir));

        if (Objects.nonNull(pool)) {
            pool.close();
        }

        if (Objects.nonNull(jxBrowserEngine)) {
            jxBrowserEngine.close();
        }
    }

    @Override
    public void onApplicationEvent(@Nonnull ServletWebServerInitializedEvent event) {
        serverPort = event.getWebServer().getPort();

        log.info("启动端口 [{}]", serverPort);
    }

    private byte[] getClassResource(String classpath) throws IOException {
        if (resources.containsKey(classpath)) {
            return resources.get(classpath);
        }

        synchronized (this) {
            if (resources.containsKey(classpath)) {
                return resources.get(classpath);
            }
            final ClassPathResource resource = new ClassPathResource(classpath);
            if (resource.exists()) {
                resources.put(classpath, IOUtils.toByteArray(resource.getInputStream()));
            }
        }

        return resources.get(classpath);

    }

    static class BrowserPooledObjectFactory extends BasePooledObjectFactory<Browser> {

        private final Engine jxBrowserEngine;

        BrowserPooledObjectFactory(Engine jxBrowserEngine) {
            this.jxBrowserEngine = jxBrowserEngine;
        }

        @Override
        public Browser create() {
            final StopWatch watch = new StopWatch();
            watch.start();
            final Browser browser = jxBrowserEngine.newBrowser();
            watch.stop();
            log.info("browser [{}] 创建成功 耗时 [{}ms]", browser, watch.getTotalTimeMillis());
            return browser;
        }

        @Override
        public boolean validateObject(PooledObject<Browser> p) {
            try {
                // 校验对象是否可用
                p.getObject().navigation().loadUrlAndWait(ABOUT_BLANK, Duration.ofSeconds(5));

                return !p.getObject().isClosed();
            } catch (Exception e) {
                log.warn("browser [{}] validateObject 失败", p.getObject(), e);
            }

            return false;
        }

        @Override
        public void destroyObject(PooledObject<Browser> p) {
            try {
                p.getObject().close();
                log.info("browser [{}] 已经停止", p.getObject());
            } catch (Exception e) {
                log.warn("browser [{}] close 失败", p.getObject(), e);
            }
        }

        @Override
        public void activateObject(PooledObject<Browser> p) {
            log.info("browser [{}] 被取出", p.getObject());
        }

        @Override
        public void passivateObject(PooledObject<Browser> p) {
            // 空白页
            p.getObject().navigation().loadUrlAndWait(ABOUT_BLANK, Duration.ofSeconds(5));
            log.info("browser [{}] 已归还，进入 {} 成功", p.getObject(), ABOUT_BLANK);
        }

        @Override
        public PooledObject<Browser> wrap(Browser obj) {
            return new DefaultPooledObject<>(obj);
        }
    }
}
