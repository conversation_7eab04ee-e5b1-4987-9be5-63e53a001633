package com.labway.lims.pdfreport.widgets;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CssStyleDeclaration {
    MARGIN_TOP("margin-top"),
    MARGIN_BOTTOM("margin-bottom"),

    BACKGROUND_COLOR("background-color"),

    BORDER_COLOR("border-color"),
    BORDER_TOP("border-top"),
    BORDER_RIGHT("border-right"),
    BORDER_BOTTOM("border-bottom"),
    BORDER_LEFT("border-left"),

    BORDER_TOP_WIDTH("border-top-width"),
    BORDER_STYLE("border-style"),
    BORDER_RIGHT_WIDTH("border-right-width"),
    BORDER_BOTTOM_WIDTH("border-bottom-width"),
    BORDER_LEFT_WIDTH("border-left-width"),

    FONT_SIZE("font-size"),
    FLEX("flex"),
    FONT_WEIGHT("font-weight"),
    CO<PERSON><PERSON>("color"),

    <PERSON><PERSON><PERSON><PERSON>("position"),
    LEFT("left"),
    <PERSON><PERSON><PERSON>("right"),
    TOP("top"),
    BOTTOM("bottom"),

    PADDING("padding"),
    HEIGHT("height"),
    WIDTH("width"),

    ;

    private final String declaration;

}
