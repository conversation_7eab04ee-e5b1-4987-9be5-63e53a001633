/*
    This file is part of the iText (R) project.
    Copyright (c) 1998-2023 Apryse Group NV
    Authors: <AUTHORS>

    For more information, please contact iText Software at this address:
    <EMAIL>
 */
package com.labway.lims.pdfreport.tags;

import com.itextpdf.barcodes.BarcodeQRCode;
import com.itextpdf.barcodes.qrcode.EncodeHintType;
import com.itextpdf.barcodes.qrcode.ErrorCorrectionLevel;
import com.itextpdf.html2pdf.attach.ITagWorker;
import com.itextpdf.html2pdf.attach.ProcessorContext;
import com.itextpdf.layout.IPropertyContainer;
import com.itextpdf.layout.element.Image;
import com.itextpdf.styledxmlparser.node.IElementNode;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Example of a custom tagworker implementation for pdfHTML.
 * The tagworker processes a <qr> tag using iText Barcode functionality
 */
public class QRCodeTagWorker implements ITagWorker {
    private static final String[] allowedErrorCorrection = {"L", "M", "Q", "H"};
    private static final String[] allowedCharset = {"UTF-8", "Cp437", "Shift_JIS", "ISO-8859-1", "ISO-8859-16"};

    private final BarcodeQRCode qrCode;
    private Image qrCodeAsImage;

    public QRCodeTagWorker(IElementNode element, ProcessorContext context) {

        // Retrieve all necessary properties to create the barcode
        Map<EncodeHintType, Object> hints = new HashMap<>();

        // Character set
        String charset = StringUtils.defaultString(element.getAttribute("charset"), "UTF-8");
        if (checkCharacterSet(charset)) {
            hints.put(EncodeHintType.CHARACTER_SET, charset);
        }

        // Error-correction level
        String errorCorrection = element.getAttribute("errorcorrection");
        if (checkErrorCorrectionAllowed(errorCorrection)) {
            ErrorCorrectionLevel errorCorrectionLevel = getErrorCorrectionLevel(errorCorrection);
            hints.put(EncodeHintType.ERROR_CORRECTION, errorCorrectionLevel);
        }

        // Create the QR-code
        qrCode = new BarcodeQRCode();
        qrCode.setHints(hints);

    }

    @Override
    public void processEnd(IElementNode element, ProcessorContext context) {
        // Transform barcode into image
        qrCodeAsImage = new Image(qrCode.createFormXObject(context.getPdfDocument()));

    }

    @Override
    public boolean processContent(String content, ProcessorContext context) {
        // Add content to the barcode
        qrCode.setCode(content);
        return true;
    }

    @Override
    public boolean processTagChild(ITagWorker childTagWorker, ProcessorContext context) {
        return false;
    }

    @Override
    public IPropertyContainer getElementResult() {
        return qrCodeAsImage;
    }

    private static boolean checkErrorCorrectionAllowed(String toCheck) {
        for (String s : allowedErrorCorrection) {
            if (s.toUpperCase().equals(toCheck)) {
                return true;
            }
        }
        return false;
    }

    private static boolean checkCharacterSet(String toCheck) {
        for (String s : allowedCharset) {
            if (s.equals(toCheck)) {
                return true;
            }
        }
        return false;
    }

    private static ErrorCorrectionLevel getErrorCorrectionLevel(String level) {
        switch (level) {
            case "L":
                return ErrorCorrectionLevel.L;
            case "M":
                return ErrorCorrectionLevel.M;
            case "Q":
                return ErrorCorrectionLevel.Q;
            case "H":
                return ErrorCorrectionLevel.H;
        }
        return null;

    }
}
