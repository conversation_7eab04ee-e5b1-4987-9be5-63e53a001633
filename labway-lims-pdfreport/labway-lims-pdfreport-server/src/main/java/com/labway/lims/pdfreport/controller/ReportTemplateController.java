package com.labway.lims.pdfreport.controller;

import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.dto.ReportTemplateDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.pdfreport.api.service.ReportTemplateHistoryService;
import com.labway.lims.pdfreport.api.service.ReportTemplateService;
import com.labway.lims.pdfreport.widgets.PageWidget;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 模板
 */
@Slf4j
@RestController
@RequestMapping("/template")
public class ReportTemplateController extends BaseController {

    @Resource
    private ReportTemplateService reportTemplateService;
    @Resource
    private EnvDetector envDetector;
    @Resource
    private ReportTemplateHistoryService reportTemplateHistoryService;
    @Resource
    private PdfReportService pdfReportService;
    @DubboReference
    private SnowflakeService snowflakeService;

    /**
     * 获取所有
     */
    @Trace
    @GetMapping("/list")
    public Object list() {
        return reportTemplateService.selectAll()
                .stream().peek(e -> e.setContent(StringUtils.EMPTY)).collect(Collectors.toList());
    }

    /**
     * 添加
     */
    @PostMapping("/add")
    public Object add(@RequestBody ReportTemplateDto reportTemplate) {

        if (StringUtils.length(reportTemplate.getReportTemplateCode()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("编码长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        if (StringUtils.length(reportTemplate.getReportTemplateName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("名称长度不能超过 %s 字符", INPUT_MAX_LENGTH));
        }

        reportTemplate.setReportTemplateId(snowflakeService.genId());
        return Map.of("id", reportTemplateService.addReportTemplate(reportTemplate));
    }

    /**
     * 获取
     */
    @GetMapping("/get")
    public Object get(long reportTemplateId) {
        final ReportTemplateDto reportTemplate = reportTemplateService.selectByReportTemplateId(reportTemplateId);
        if (Objects.isNull(reportTemplate)) {
            throw new IllegalStateException("报告不存在");
        }
        return reportTemplate;
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public Object update(@RequestBody ReportTemplateDto reportTemplate) {
        if (!reportTemplateService.updateByReportTemplateId(reportTemplate)) {
            throw new IllegalStateException("修改失败");
        }
        return Map.of();
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    public Object delete(long reportTemplateId) {
        reportTemplateService.deleteByReportTemplateId(reportTemplateId);
        return Map.of();
    }

    /**
     * 修改历史
     */
    @GetMapping("/history")
    public Object history(long reportTemplateId) {
        return reportTemplateHistoryService.selectNonContentByReportTemplateId(reportTemplateId);
    }

    /**
     * 修改历史
     */
    @GetMapping("/history/get")
    public Object getHistory(long reportTemplateHistoryId) {
        return reportTemplateHistoryService.selectByReportTemplateHistoryId(reportTemplateHistoryId);
    }

    /**
     * vfrom
     */
    @PostMapping("/vform")
    public Object vform(String struct) {
        return Map.of("html", PageWidget.from(struct).toHtml());
    }

    /**
     * try-gen
     */
    @GetMapping("/try-gen")
    public Object tryGen(@RequestParam String code) {


        final ReportTemplateDto reportTemplate = reportTemplateService.selectByReportTemplateCode(code);
        if (Objects.isNull(reportTemplate)) {
            throw new IllegalStateException("编码不存在");
        }

        final PdfReportParamDto param = new PdfReportParamDto();
        final byte[] build = pdfReportService.build(code, param);
        return ResponseEntity.status(HttpStatus.OK)
                .contentType(MediaType.APPLICATION_PDF)
                .body(build);
    }
}
