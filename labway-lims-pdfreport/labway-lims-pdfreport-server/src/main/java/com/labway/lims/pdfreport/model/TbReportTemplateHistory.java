package com.labway.lims.pdfreport.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 模版修改历史
 */
@Getter
@Setter
@TableName("tb_report_template_history")
public class TbReportTemplateHistory {

    /**
     * ID
     */
    @TableId
    private Long reportTemplateHistoryId;

    /**
     * 模板ID
     */
    private Long reportTemplateId;


    /**
     * 内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createDate;


    /**
     * 是否删除
     *
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer isDelete;

}
