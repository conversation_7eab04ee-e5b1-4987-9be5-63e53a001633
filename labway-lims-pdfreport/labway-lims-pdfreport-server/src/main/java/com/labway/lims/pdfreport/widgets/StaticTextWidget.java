package com.labway.lims.pdfreport.widgets;

import com.alibaba.fastjson.JSONObject;
import j2html.tags.ContainerTag;
import j2html.tags.DomContent;
import j2html.tags.specialized.SpanTag;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import static j2html.TagCreator.*;

/**
 * 静态文本
 */
@Getter
@Setter
public class StaticTextWidget extends Widget {

    private static final String TEXT_ALIGN_CENTER = "center";
    private static final String TEXT_ALIGN_LEFT = "left";
    private static final String TEXT_ALIGN_RIGHT = "right";

    @Override
    public DomContent toTag(ContainerTag<?> parent) {
        final JSONObject options = getOriginal().getJSONObject("options");
        final String textContent = StringUtils.defaultString(options.getString("textContent"));

        final ContainerTag<?> staticText = createTag(options).withText(textContent);
        staticText.condAttr(StringUtils.isNotBlank(textContent), "th:text", String.format("|%s|", textContent));

        final String textAlign = options.getString("textAlign");

        final String fontSize = options.getString("fontSize");
        if (StringUtils.isNotBlank(fontSize)) {
            getCssStyleDeclaration().put(CssStyleDeclaration.FONT_SIZE, fontSize + "rem");
        }

        if (BooleanUtils.isTrue(options.getBoolean("fontBold"))) {
            getCssStyleDeclaration().put(CssStyleDeclaration.FONT_WEIGHT, "bold");
        }

        final String fontColor = options.getString("fontColor");
        if (StringUtils.isNotBlank(fontColor)) {
            getCssStyleDeclaration().put(CssStyleDeclaration.COLOR, fontColor);
        }

        staticText.withStyle(cssStyleFromMap(getCssStyleDeclaration()));

        return div().withClass("row").with(div()
                .withClasses("column", "flex",
                        iff(StringUtils.equals(TEXT_ALIGN_CENTER, textAlign), "text-center"),
                        iff(StringUtils.equals(TEXT_ALIGN_LEFT, textAlign), "text-left"),
                        iff(StringUtils.equals(TEXT_ALIGN_RIGHT, textAlign), "text-right")
                )
                .with(staticText));
    }


    protected ContainerTag<?> createTag(JSONObject options) {

        final SpanTag staticText = span();

        staticText.withClasses(
                "static-text",
                iff(BooleanUtils.isTrue(options.getBoolean("hidden")), "hidden")
        );

        return staticText;
    }
}

