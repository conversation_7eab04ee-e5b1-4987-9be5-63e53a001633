package com.labway.lims.pdfreport.tags;

import com.itextpdf.barcodes.Barcode128;
import com.itextpdf.html2pdf.attach.ITagWorker;
import com.itextpdf.html2pdf.attach.ProcessorContext;
import com.itextpdf.layout.IPropertyContainer;
import com.itextpdf.layout.element.Image;
import com.itextpdf.styledxmlparser.node.IElementNode;
import org.apache.commons.lang3.BooleanUtils;

/**
 * 条码号
 */
public class BarcodeTagWorker implements ITagWorker {

    private final Barcode128 barcode128;

    private Image qrCodeAsImage;

    public BarcodeTagWorker(IElementNode element, ProcessorContext context) {

        barcode128 = new Barcode128(context.getPdfDocument(), context.getPdfDocument().getDefaultFont());

        // 如果是 true 隐藏 text
        if (BooleanUtils.toBoolean(element.getAttribute("hide-text"))) {
            barcode128.setFont(null);
        }
    }


    @Override
    public void processEnd(IElementNode element, ProcessorContext context) {

        qrCodeAsImage = new Image(barcode128.createFormXObject(context.getPdfDocument()));

    }

    @Override
    public boolean processContent(String content, ProcessorContext context) {
        barcode128.setCode(content);
        return true;
    }

    @Override
    public boolean processTagChild(ITagWorker childTagWorker, ProcessorContext context) {
        return false;
    }

    @Override
    public IPropertyContainer getElementResult() {
        return qrCodeAsImage;
    }
}
