package com.labway.lims.pdfreport.service;

import cn.hutool.core.date.StopWatch;
import com.teamdev.jxbrowser.browser.Browser;
import com.teamdev.jxbrowser.engine.Engine;
import com.teamdev.jxbrowser.engine.EngineOptions;
import com.teamdev.jxbrowser.engine.Language;
import com.teamdev.jxbrowser.js.JsObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.junit.Test;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.teamdev.jxbrowser.engine.RenderingMode.HARDWARE_ACCELERATED;

public class JxBrowserTest {
    @Test
    public void test() throws Exception {

        final EngineOptions.Builder builder = EngineOptions
                .newBuilder(HARDWARE_ACCELERATED)
                .licenseKey("1BNDIEOFAZ4URG08BVSA22KHVFIU3ESZ6XIKHK79WHLRJC99H9A4BW7PAU1NR6HT7H9BGC")
                .allowFileAccessFromFiles()
                .enableIncognito()
                .disableWebSecurity()
                .language(Language.CHINESE);


        final StopWatch watch = new StopWatch();
        watch.start("实例化");
        final Engine engine = Engine.newInstance(builder.build());
        watch.stop();


        final GenericObjectPoolConfig<Browser> config = new GenericObjectPoolConfig<>();
        config.setMaxTotal(20);
        config.setMaxIdle(10);
        config.setMinIdle(3);
        config.setMaxWaitMillis(TimeUnit.SECONDS.toMillis(10));
        config.setMinEvictableIdleTimeMillis(TimeUnit.MINUTES.toMillis(10));
        // 在创建对象的时候是否检测对象，默认false
        config.setTestOnCreate(true);
        // 在获取空闲对象的时候是否检测对象是否有效，默认false
        config.setTestOnBorrow(true);
        // 在空闲的时候是否检测对象是否有效
        config.setTestWhileIdle(true);
        final GenericObjectPool<Browser> pool = new GenericObjectPool<>(new BasePooledObjectFactory<Browser>() {
            @Override
            public Browser create() throws Exception {
                return engine.newBrowser();
            }

            @Override
            public boolean validateObject(PooledObject<Browser> p) {
                return !p.getObject().isClosed();
            }

            @Override
            public void destroyObject(PooledObject<Browser> p) throws Exception {
                p.getObject().close();
            }

            @Override
            public PooledObject<Browser> wrap(Browser obj) {
                return new DefaultPooledObject<>(obj);
            }
        }, config);

        System.out.println(watch.prettyPrint(TimeUnit.MILLISECONDS));

        watch.start("开启浏览器");
        final Browser browser = pool.borrowObject();
        watch.stop();

        final String html = String.format("<script>window.onload=()=>{window.test = '%s'}</script>", UUID.randomUUID());

        watch.start("加载");
        browser.navigation().loadUrlAndWait("data:text/html;charset=utf-8;base64," +
                Base64.encodeBase64String(html.getBytes(StandardCharsets.UTF_8)), Duration.ofSeconds(3));
        watch.stop();

        browser.mainFrame().ifPresent(e -> {
            final JsObject object = e.executeJavaScript("window");
            if (Objects.nonNull(object)) {
                object.property("test").ifPresent(System.out::println);
            }
        });



        watch.start("加载");
        browser.navigation().loadUrlAndWait("data:text/html;charset=utf-8;base64," +
                Base64.encodeBase64String(html.getBytes(StandardCharsets.UTF_8)), Duration.ofSeconds(3));
        watch.stop();

        browser.mainFrame().ifPresent(e -> {
            final JsObject object = e.executeJavaScript("window");
            if (Objects.nonNull(object)) {
                object.property("test").ifPresent(System.out::println);
            }
        });

        pool.returnObject(browser);
    }
}
