package com.labway.lims.pdfreport.service;

import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.base.api.service.RandomStringService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class PdfReportServiceImplTest {
    @Resource
    private PdfReportService pdfReportService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @DubboReference
    private RandomStringService randomStringService;

    @Test
    public void test() throws Exception {
        final PdfReportParamDto param = new PdfReportParamDto();
        param.put("apply", Map.of(
                "masterBarcode", RandomStringUtils.randomNumeric(12),
                "patientName", randomStringService.randomChineseString(),
                "patientAge", randomStringService.randomChineseString(),
                "hspOrgName", randomStringService.randomChineseString()
        ));
        param.put("applySample", Map.of(
                "barcode", RandomStringUtils.randomNumeric(12),
                "tubeName", RandomStringUtils.randomNumeric(12),
                "sampleTypeName", RandomStringUtils.randomNumeric(12),
                "groupName", RandomStringUtils.randomNumeric(12),
                "onePickerName", RandomStringUtils.randomNumeric(12),
                "onePickDate", RandomStringUtils.randomNumeric(12),
                "twoPickerName", RandomStringUtils.randomNumeric(12),
                "twoPickDate", RandomStringUtils.randomNumeric(12)
        ));
        param.put("sample", Map.of(
                "sampleNo", RandomStringUtils.randomNumeric(12),
                "instrumentGroupName", RandomStringUtils.randomNumeric(12),
                "instrumentName", RandomStringUtils.randomNumeric(12),
                "testerName", RandomStringUtils.randomNumeric(12),
                "checkerName", RandomStringUtils.randomNumeric(12),
                "sampleRemark", StringUtils.EMPTY,
                "resultRemark", RandomStringUtils.randomNumeric(12)
        ));
        final byte[] tests = pdfReportService.build("TEST", param);
        System.out.println(huaweiObsUtils.upload(new ByteArrayInputStream(tests),
                MediaType.APPLICATION_PDF_VALUE, 1));
    }
}