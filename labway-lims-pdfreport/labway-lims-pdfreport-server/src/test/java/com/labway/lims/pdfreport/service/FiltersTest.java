package com.labway.lims.pdfreport.service;

import org.junit.Test;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;

import java.util.ArrayList;
import java.util.List;

public class FiltersTest {
    @Test
    public void testPropertyMap() {
        List<Collections2Test.User> users = new ArrayList<>();
        {
            final Collections2Test.User user = new Collections2Test.User();
            user.setUsername("test");
            users.add(user);
        }
        {
            final Collections2Test.User user = new Collections2Test.User();
            user.setUsername("test2");
            users.add(user);
        }

        final Context context = new Context();
        context.setVariable("users", users);
        System.out.println(new SpringTemplateEngine().process("<div th:text=\"${#lists.contains(T(com.google.common.collect.Collections2).transform(users,T(com.labway.lims.pdfreport.utils.Filters).propertyMap('username')),'test2')}\"></div>", context));
    }
}
