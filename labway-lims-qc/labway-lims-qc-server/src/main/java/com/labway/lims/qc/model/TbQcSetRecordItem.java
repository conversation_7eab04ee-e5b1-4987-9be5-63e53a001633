package com.labway.lims.qc.model;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 仪器质控记录明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
public class TbQcSetRecordItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 质控记录明细系统编号
     */
    private Integer qcRecordItemSysno;

    /**
     * 质控记录明细ID
     */
    private String qcRecordItemId;

    /**
     * 记录批次
     */
    private String qcRecordBatch;

    /**
     * 质控批次编号
     */
    private String qcBatchId;

    /**
     * 仪器质控设置编码
     */
    private String instrumentQcId;

    /**
     * 客商业务ID
     */
    private String customerId;

    /**
     * 客商名称
     */
    private String customerName;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 客商仪器编码ID
     */
    private String instrumentId;

    /**
     * 客商仪器名称
     */
    private String instrumentName;

    /**
     * 仪器通道编码
     */
    private String instrumentChannel;

    /**
     * 质控开始日期
     */
    private LocalDateTime qcStartDate;

    /**
     * 质控结束日期
     */
    private LocalDateTime qcEndDate;

    /**
     * 是否有效
     */
    private Boolean isUse;

    /**
     * 质控记录明细状态
     */
    private Integer status;

    /**
     * 质控记录明细状态描述
     */
    private String statusDesc;

    /**
     * 靶值 , 在真实值的基础上 * 1000 了
     */
    private Long targetValue;

    /**
     * 标准差 , 在真实值的基础上 * 1000 了
     */
    private Long standardDeviation;

    /**
     * 变异系数 , 在真实值的基础上 * 1000 了
     */
    @TableField("CV_value")
    private Long cvValue;


    /**
     * 水平编码
     */
    private Integer levelCode;

    /**
     * 水平编码描述
     */
    private String levelCodeDesc;

    /**
     * 质控规则集合
     */
    private String qcRulesCollection;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 0定性 1半定性
     * 定性类型
     */
    private Integer qualitativeType;

    /**
     * 上控制线
     */
    private BigDecimal upperControlLimit;
    /**
     * 下控制线
     */
    private BigDecimal lowerControlLimit;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 自定义属性1
     */
    private String selfAttribute1;

    /**
     * 自定义属性2
     */
    private String selfAttribute2;

    /**
     * 自定义属性3
     */
    private String selfAttribute3;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;


    public Integer getQcRecordItemSysno() {
        return qcRecordItemSysno;
    }

    public void setQcRecordItemSysno(Integer qcRecordItemSysno) {
        this.qcRecordItemSysno = qcRecordItemSysno;
    }

    public String getQcRecordItemId() {
        return qcRecordItemId;
    }

    public void setQcRecordItemId(String qcRecordItemId) {
        this.qcRecordItemId = qcRecordItemId;
    }

    public String getQcRecordBatch() {
        return qcRecordBatch;
    }

    public void setQcRecordBatch(String qcRecordBatch) {
        this.qcRecordBatch = qcRecordBatch;
    }

    public String getQcBatchId() {
        return qcBatchId;
    }

    public void setQcBatchId(String qcBatchId) {
        this.qcBatchId = qcBatchId;
    }

    public String getInstrumentQcId() {
        return instrumentQcId;
    }

    public void setInstrumentQcId(String instrumentQcId) {
        this.instrumentQcId = instrumentQcId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getInstrumentId() {
        return instrumentId;
    }

    public void setInstrumentId(String instrumentId) {
        this.instrumentId = instrumentId;
    }

    public String getInstrumentName() {
        return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }

    public String getInstrumentChannel() {
        return instrumentChannel;
    }

    public void setInstrumentChannel(String instrumentChannel) {
        this.instrumentChannel = instrumentChannel;
    }

    public LocalDateTime getQcStartDate() {
        return qcStartDate;
    }

    public void setQcStartDate(LocalDateTime qcStartDate) {
        this.qcStartDate = qcStartDate;
    }

    public LocalDateTime getQcEndDate() {
        return qcEndDate;
    }

    public void setQcEndDate(LocalDateTime qcEndDate) {
        this.qcEndDate = qcEndDate;
    }

    public Boolean getUse() {
        return isUse;
    }

    public void setUse(Boolean isUse) {
        this.isUse = isUse;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public Long getTargetValue() {
        return targetValue;
    }

    public void setTargetValue(Long targetValue) {
        this.targetValue = targetValue;
    }

    public Long getStandardDeviation() {
        return standardDeviation;
    }

    public void setStandardDeviation(Long standardDeviation) {
        this.standardDeviation = standardDeviation;
    }

    public Long getCvValue() {
        return cvValue;
    }

    public void setCvValue(Long cvValue) {
        this.cvValue = cvValue;
    }

    public Integer getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(Integer levelCode) {
        this.levelCode = levelCode;
    }

    public String getLevelCodeDesc() {
        return levelCodeDesc;
    }

    public void setLevelCodeDesc(String levelCodeDesc) {
        this.levelCodeDesc = levelCodeDesc;
    }

    public String getQcRulesCollection() {
        return qcRulesCollection;
    }

    public void setQcRulesCollection(String qcRulesCollection) {
        this.qcRulesCollection = qcRulesCollection;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getSelfAttribute1() {
        return selfAttribute1;
    }

    public void setSelfAttribute1(String selfAttribute1) {
        this.selfAttribute1 = selfAttribute1;
    }

    public String getSelfAttribute2() {
        return selfAttribute2;
    }

    public void setSelfAttribute2(String selfAttribute2) {
        this.selfAttribute2 = selfAttribute2;
    }

    public String getSelfAttribute3() {
        return selfAttribute3;
    }

    public void setSelfAttribute3(String selfAttribute3) {
        this.selfAttribute3 = selfAttribute3;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }


    public Integer getQualitativeType() {
        return qualitativeType;
    }

    public void setQualitativeType(Integer qualitativeType) {
        this.qualitativeType = qualitativeType;
    }

    public BigDecimal getUpperControlLimit() {
        return upperControlLimit;
    }

    public void setUpperControlLimit(BigDecimal upperControlLimit) {
        this.upperControlLimit = upperControlLimit;
    }

    public BigDecimal getLowerControlLimit() {
        return lowerControlLimit;
    }

    public void setLowerControlLimit(BigDecimal lowerControlLimit) {
        this.lowerControlLimit = lowerControlLimit;
    }

    @Override
    public String toString() {
        return "TbQcSetRecordItem{" +
                "qcRecordItemSysno=" + qcRecordItemSysno +
                ", qcRecordItemId=" + qcRecordItemId +
                ", qcRecordBatch=" + qcRecordBatch +
                ", qcBatchId=" + qcBatchId +
                ", instrumentQcId=" + instrumentQcId +
                ", customerId=" + customerId +
                ", customerName=" + customerName +
                ", orgId=" + orgId +
                ", orgName=" + orgName +
                ", instrumentId=" + instrumentId +
                ", instrumentName=" + instrumentName +
                ", instrumentChannel=" + instrumentChannel +
                ", qcStartDate=" + qcStartDate +
                ", qcEndDate=" + qcEndDate +
                ", isUse=" + isUse +
                ", status=" + status +
                ", statusDesc=" + statusDesc +
                ", targetValue=" + targetValue +
                ", standardDeviation=" + standardDeviation +
                ", cvValue=" + cvValue +
                ", levelCode=" + levelCode +
                ", levelCodeDesc=" + levelCodeDesc +
                ", qcRulesCollection=" + qcRulesCollection +
                ", operator=" + operator +
                ", operatorId=" + operatorId +
                ", isDelete=" + isDelete +
                ", selfAttribute1=" + selfAttribute1 +
                ", selfAttribute2=" + selfAttribute2 +
                ", selfAttribute3=" + selfAttribute3 +
                ", createDate=" + createDate +
                ", updateDate=" + updateDate +
                "}";
    }
}
