package com.labway.lims.qc.model;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 仪器质控流水
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
public class TbInstrumentQcFlow implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 质控流水系统编号
     */
    private Integer qcFlowSysno;

    /**
     * 质控流水ID
     */
    private String qcFlowId;

    /**
     * 客商仪器编码ID
     */
    private String instrumentId;

    /**
     * 客商仪器名称
     */
    private String instrumentName;

    /**
     * 质控样本报告项目ID
     */
    private String qcSampleReportItemId;

    /**
     * 质控样本报告项目名称
     */
    private String qcSampleReportItemName;

    /**
     * 仪器通道编码
     */
    private String instrumentChannel;

    /**
     * 质控批次编号
     */
    private String qcBatchId;

    /**
     * 操作类型编码
     */
    private Integer operationCode;

    /**
     * 操作类型编码描述
     */
    private String operationCodeDesc;

    /**
     * 质控流水内容
     */
    private String qcFlowContent;

    /**
     * 客商业务ID
     */
    private String customerId;

    /**
     * 客商名称
     */
    private String customerName;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 是否有效
     */
    private Boolean isUse;

    /**
     * 质控流水状态
     */
    private Integer qcFlowStatus;

    /**
     * 质控流水状态描述
     */
    private String qcFlowStatusDesc;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 自定义属性1
     */
    private String selfAttribute1;

    /**
     * 自定义属性2
     */
    private String selfAttribute2;

    /**
     * 自定义属性3
     */
    private String selfAttribute3;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;


    public Integer getQcFlowSysno() {
        return qcFlowSysno;
    }

    public void setQcFlowSysno(Integer qcFlowSysno) {
        this.qcFlowSysno = qcFlowSysno;
    }

    public String getQcFlowId() {
        return qcFlowId;
    }

    public void setQcFlowId(String qcFlowId) {
        this.qcFlowId = qcFlowId;
    }

    public String getInstrumentId() {
        return instrumentId;
    }

    public void setInstrumentId(String instrumentId) {
        this.instrumentId = instrumentId;
    }

    public String getInstrumentName() {
        return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }

    public String getQcSampleReportItemId() {
        return qcSampleReportItemId;
    }

    public void setQcSampleReportItemId(String qcSampleReportItemId) {
        this.qcSampleReportItemId = qcSampleReportItemId;
    }

    public String getQcSampleReportItemName() {
        return qcSampleReportItemName;
    }

    public void setQcSampleReportItemName(String qcSampleReportItemName) {
        this.qcSampleReportItemName = qcSampleReportItemName;
    }

    public String getInstrumentChannel() {
        return instrumentChannel;
    }

    public void setInstrumentChannel(String instrumentChannel) {
        this.instrumentChannel = instrumentChannel;
    }

    public String getQcBatchId() {
        return qcBatchId;
    }

    public void setQcBatchId(String qcBatchId) {
        this.qcBatchId = qcBatchId;
    }

    public Integer getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(Integer operationCode) {
        this.operationCode = operationCode;
    }

    public String getOperationCodeDesc() {
        return operationCodeDesc;
    }

    public void setOperationCodeDesc(String operationCodeDesc) {
        this.operationCodeDesc = operationCodeDesc;
    }

    public String getQcFlowContent() {
        return qcFlowContent;
    }

    public void setQcFlowContent(String qcFlowContent) {
        this.qcFlowContent = qcFlowContent;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Boolean getUse() {
        return isUse;
    }

    public void setUse(Boolean isUse) {
        this.isUse = isUse;
    }

    public Integer getQcFlowStatus() {
        return qcFlowStatus;
    }

    public void setQcFlowStatus(Integer qcFlowStatus) {
        this.qcFlowStatus = qcFlowStatus;
    }

    public String getQcFlowStatusDesc() {
        return qcFlowStatusDesc;
    }

    public void setQcFlowStatusDesc(String qcFlowStatusDesc) {
        this.qcFlowStatusDesc = qcFlowStatusDesc;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getSelfAttribute1() {
        return selfAttribute1;
    }

    public void setSelfAttribute1(String selfAttribute1) {
        this.selfAttribute1 = selfAttribute1;
    }

    public String getSelfAttribute2() {
        return selfAttribute2;
    }

    public void setSelfAttribute2(String selfAttribute2) {
        this.selfAttribute2 = selfAttribute2;
    }

    public String getSelfAttribute3() {
        return selfAttribute3;
    }

    public void setSelfAttribute3(String selfAttribute3) {
        this.selfAttribute3 = selfAttribute3;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "TbInstrumentQcFlow{" +
                "qcFlowSysno=" + qcFlowSysno +
                ", qcFlowId=" + qcFlowId +
                ", instrumentId=" + instrumentId +
                ", instrumentName=" + instrumentName +
                ", qcSampleReportItemId=" + qcSampleReportItemId +
                ", qcSampleReportItemName=" + qcSampleReportItemName +
                ", instrumentChannel=" + instrumentChannel +
                ", qcBatchId=" + qcBatchId +
                ", operationCode=" + operationCode +
                ", operationCodeDesc=" + operationCodeDesc +
                ", qcFlowContent=" + qcFlowContent +
                ", customerId=" + customerId +
                ", customerName=" + customerName +
                ", orgId=" + orgId +
                ", orgName=" + orgName +
                ", isUse=" + isUse +
                ", qcFlowStatus=" + qcFlowStatus +
                ", qcFlowStatusDesc=" + qcFlowStatusDesc +
                ", operator=" + operator +
                ", operatorId=" + operatorId +
                ", isDelete=" + isDelete +
                ", selfAttribute1=" + selfAttribute1 +
                ", selfAttribute2=" + selfAttribute2 +
                ", selfAttribute3=" + selfAttribute3 +
                ", createDate=" + createDate +
                ", updateDate=" + updateDate +
                "}";
    }
}
