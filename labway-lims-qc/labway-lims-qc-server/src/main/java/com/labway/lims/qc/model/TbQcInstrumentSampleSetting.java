package com.labway.lims.qc.model;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-13
 */
public class TbQcInstrumentSampleSetting implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 质控样本设置系统编号
     */
    private Integer qcSampleSettigSysno;

    /**
     * 质控样本设置ID
     */
    private String qcSampleSettingId;

    /**
     * 质控样品号
     */
    private String qcSampleNo;

    /**
     * 样本内容
     */
    private String qcSampleContent;

    /**
     * 客商仪器编码ID
     */
    private String instrumentId;

    /**
     * 客商仪器名称
     */
    private String instrumentName;

    /**
     * 客商报告id
     */
    private String customerReportItemId;
    /**
     * 客商报告名称
     */
    private String customerReportItemName;
    /**
     * 客商报告编码
     */
    private String customerReportItemCode;
    /**
     * 仪器通道编码
     */
    private String instrumentChannel;

    /**
     * 客商业务ID
     */
    private String customerId;

    /**
     * 客商名称
     */
    private String customerName;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 规则集合
     */
    private String rules;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 自定义属性1
     */
    private String selfAttribute1;

    /**
     * 自定义属性2
     */
    private String selfAttribute2;

    /**
     * 自定义属性3
     */
    private String selfAttribute3;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getQcSampleSettigSysno() {
        return qcSampleSettigSysno;
    }

    public void setQcSampleSettigSysno(Integer qcSampleSettigSysno) {
        this.qcSampleSettigSysno = qcSampleSettigSysno;
    }

    public String getQcSampleSettingId() {
        return qcSampleSettingId;
    }

    public void setQcSampleSettingId(String qcSampleSettingId) {
        this.qcSampleSettingId = qcSampleSettingId;
    }

    public String getQcSampleNo() {
        return qcSampleNo;
    }

    public void setQcSampleNo(String qcSampleNo) {
        this.qcSampleNo = qcSampleNo;
    }

    public String getQcSampleContent() {
        return qcSampleContent;
    }

    public void setQcSampleContent(String qcSampleContent) {
        this.qcSampleContent = qcSampleContent;
    }

    public String getInstrumentId() {
        return instrumentId;
    }

    public void setInstrumentId(String instrumentId) {
        this.instrumentId = instrumentId;
    }

    public String getInstrumentName() {
        return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }

    public String getCustomerReportItemId() {
        return customerReportItemId;
    }

    public void setCustomerReportItemId(String customerReportItemId) {
        this.customerReportItemId = customerReportItemId;
    }

    public String getCustomerReportItemName() {
        return customerReportItemName;
    }

    public void setCustomerReportItemName(String customerReportItemName) {
        this.customerReportItemName = customerReportItemName;
    }

    public String getCustomerReportItemCode() {
        return customerReportItemCode;
    }

    public void setCustomerReportItemCode(String customerReportItemCode) {
        this.customerReportItemCode = customerReportItemCode;
    }

    public String getInstrumentChannel() {
        return instrumentChannel;
    }

    public void setInstrumentChannel(String instrumentChannel) {
        this.instrumentChannel = instrumentChannel;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getRules() {
        return rules;
    }

    public void setRules(String rules) {
        this.rules = rules;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getSelfAttribute1() {
        return selfAttribute1;
    }

    public void setSelfAttribute1(String selfAttribute1) {
        this.selfAttribute1 = selfAttribute1;
    }

    public String getSelfAttribute2() {
        return selfAttribute2;
    }

    public void setSelfAttribute2(String selfAttribute2) {
        this.selfAttribute2 = selfAttribute2;
    }

    public String getSelfAttribute3() {
        return selfAttribute3;
    }

    public void setSelfAttribute3(String selfAttribute3) {
        this.selfAttribute3 = selfAttribute3;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "TbQcInstrumentSampleSetting{" +
                "qcSampleSettigSysno=" + qcSampleSettigSysno +
                ", qcSampleSettingId='" + qcSampleSettingId + '\'' +
                ", qcSampleNo='" + qcSampleNo + '\'' +
                ", qcSampleContent='" + qcSampleContent + '\'' +
                ", instrumentId='" + instrumentId + '\'' +
                ", instrumentName='" + instrumentName + '\'' +
                ", customerReportItemId='" + customerReportItemId + '\'' +
                ", customerReportItemName='" + customerReportItemName + '\'' +
                ", customerReportItemCode='" + customerReportItemCode + '\'' +
                ", instrumentChannel='" + instrumentChannel + '\'' +
                ", customerId='" + customerId + '\'' +
                ", customerName='" + customerName + '\'' +
                ", orgId='" + orgId + '\'' +
                ", orgName='" + orgName + '\'' +
                ", rules='" + rules + '\'' +
                ", status=" + status +
                ", statusDesc='" + statusDesc + '\'' +
                ", sort=" + sort +
                ", operator='" + operator + '\'' +
                ", operatorId='" + operatorId + '\'' +
                ", isDelete=" + isDelete +
                ", selfAttribute1='" + selfAttribute1 + '\'' +
                ", selfAttribute2='" + selfAttribute2 + '\'' +
                ", selfAttribute3='" + selfAttribute3 + '\'' +
                ", createDate=" + createDate +
                ", updateDate=" + updateDate +
                '}';
    }
}
