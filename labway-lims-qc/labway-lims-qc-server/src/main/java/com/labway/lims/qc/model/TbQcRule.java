package com.labway.lims.qc.model;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 质控规则
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
public class TbQcRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 质控规则系统编号
     */
    private Integer qcRuleSysno;

    /**
     * 质控规则ID
     */
    private String qcRuleId;

    /**
     * 质控规则名称
     */
    private String qcRuleName;

    /**
     * 质控规则医学名称
     */
    private String qcRuleDocName;

    /**
     * 质控规则内容
     */
    private String qcRuleContent;

    /**
     * 客商业务ID
     */
    private String customerId;

    /**
     * 客商名称
     */
    private String customerName;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 是否有效
     */
    private Boolean isUse;

    /**
     * 质控规则状态
     */
    private Integer qcRuleStatus;

    /**
     * 质控规则状态描述
     */
    private String qcRuleStatusDesc;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 自定义属性1
     */
    private String selfAttribute1;

    /**
     * 自定义属性2
     */
    private String selfAttribute2;

    /**
     * 自定义属性3
     */
    private String selfAttribute3;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;


    public Integer getQcRuleSysno() {
        return qcRuleSysno;
    }

    public void setQcRuleSysno(Integer qcRuleSysno) {
        this.qcRuleSysno = qcRuleSysno;
    }

    public String getQcRuleId() {
        return qcRuleId;
    }

    public void setQcRuleId(String qcRuleId) {
        this.qcRuleId = qcRuleId;
    }

    public String getQcRuleName() {
        return qcRuleName;
    }

    public void setQcRuleName(String qcRuleName) {
        this.qcRuleName = qcRuleName;
    }

    public String getQcRuleDocName() {
        return qcRuleDocName;
    }

    public void setQcRuleDocName(String qcRuleDocName) {
        this.qcRuleDocName = qcRuleDocName;
    }

    public String getQcRuleContent() {
        return qcRuleContent;
    }

    public void setQcRuleContent(String qcRuleContent) {
        this.qcRuleContent = qcRuleContent;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Boolean getUse() {
        return isUse;
    }

    public void setUse(Boolean isUse) {
        this.isUse = isUse;
    }

    public Integer getQcRuleStatus() {
        return qcRuleStatus;
    }

    public void setQcRuleStatus(Integer qcRuleStatus) {
        this.qcRuleStatus = qcRuleStatus;
    }

    public String getQcRuleStatusDesc() {
        return qcRuleStatusDesc;
    }

    public void setQcRuleStatusDesc(String qcRuleStatusDesc) {
        this.qcRuleStatusDesc = qcRuleStatusDesc;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getSelfAttribute1() {
        return selfAttribute1;
    }

    public void setSelfAttribute1(String selfAttribute1) {
        this.selfAttribute1 = selfAttribute1;
    }

    public String getSelfAttribute2() {
        return selfAttribute2;
    }

    public void setSelfAttribute2(String selfAttribute2) {
        this.selfAttribute2 = selfAttribute2;
    }

    public String getSelfAttribute3() {
        return selfAttribute3;
    }

    public void setSelfAttribute3(String selfAttribute3) {
        this.selfAttribute3 = selfAttribute3;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "TbQcRule{" +
                "qcRuleSysno=" + qcRuleSysno +
                ", qcRuleId=" + qcRuleId +
                ", qcRuleName=" + qcRuleName +
                ", qcRuleDocName=" + qcRuleDocName +
                ", qcRuleContent=" + qcRuleContent +
                ", customerId=" + customerId +
                ", customerName=" + customerName +
                ", orgId=" + orgId +
                ", orgName=" + orgName +
                ", isUse=" + isUse +
                ", qcRuleStatus=" + qcRuleStatus +
                ", qcRuleStatusDesc=" + qcRuleStatusDesc +
                ", operator=" + operator +
                ", operatorId=" + operatorId +
                ", isDelete=" + isDelete +
                ", selfAttribute1=" + selfAttribute1 +
                ", selfAttribute2=" + selfAttribute2 +
                ", selfAttribute3=" + selfAttribute3 +
                ", createDate=" + createDate +
                ", updateDate=" + updateDate +
                "}";
    }
}
