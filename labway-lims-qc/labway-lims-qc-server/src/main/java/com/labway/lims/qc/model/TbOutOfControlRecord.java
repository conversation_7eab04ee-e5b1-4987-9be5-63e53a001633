package com.labway.lims.qc.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description tb_out_of_control_record
 * @date 2022-09-14
 */
@Data
public class TbOutOfControlRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long outOfControlSysno;
    /**
     * 质控记录明细id uuid
     */
    private String qcRecordItemId;

    /**
     * 结果id
     */
    private String qcResultId;

    /**
     * 仪器质控设置编码
     */
    private String instrumentQcId;

    /**
     * 客商业务id
     */
    private String customerId;

    /**
     * 客商名称
     */
    private String customerName;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 客商仪器编码id
     */
    private String instrumentId;

    /**
     * 客商仪器名称
     */
    private String instrumentName;

    /**
     * 失控模板id
     */
    private String outOfControlId;

    /**
     * 失控记录内容
     */
    private String recordContent;

    /**
     * 客商报告项目编码id
     */
    private String customerReportItemId;

    /**
     * 客商报告项目编码
     */
    private String customerReportItemCode;

    /**
     * 客商报告项目名称
     */
    private String customerReportItem;

    /**
     * 仪器通道编码
     */
    private String instrumentChannel;

    /**
     * 靶值
     */
    private Long targetValue;

    /**
     * 标准差
     */
    private Long standardDeviation;

    /**
     * 变异系数
     */
    private Long cvValue;

    /**
     * 水平编码
     */
    private Integer levelCode;

    /**
     * 水平编码描述
     */
    private String levelCodeDesc;

    /**
     * 质控规则
     */
    private String qcRules;

    /**
     * 失控浓度
     */
    private String outOfValues;

    /**
     * 失控原因
     */
    private String reason;

    /**
     * 失控处理措施
     */
    private String handleMethod;

    /**
     * 总结经验
     */
    private String lessons;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 是否删除
     */
    private int isDelete;

    /**
     * 自定义属性1
     */
    private String selfAttribute1;

    /**
     * 自定义属性2
     */
    private String selfAttribute2;

    /**
     * 自定义属性3
     */
    private String selfAttribute3;

    /**
     * 检验时间
     */
    private Date testDate;

    /**
     * 创建时间
     */
    private Date createDate;

    public TbOutOfControlRecord() {
    }
}
