package com.labway.lims.qc.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 质控样本结果记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Getter
@Setter
public class TbQcSampleResult implements Serializable {

    /**
     * 质控样本结果系统编号
     */
    @TableId(type = IdType.AUTO)
    private Long qcResultSysno;
    /**
     * 质控样本结果ID
     */
    private String resultId;
    /**
     * 记录批次
     */
    private String qcRecordBatch;
    /**
     * 质控样本主码号
     */
    private String qcBarcode;
    /**
     * 质控样本号
     */
    private String qcSampleNo;
    /**
     * 样本内容
     */
    private String qcSampleContent;
    /**
     * 质控样本报告项目ID
     */
    private String qcSampleReportItemId;
    /**
     * 接收时间
     */
    private LocalDateTime testDate;
    /**
     * 质控样本报告项目名称
     */
    private String qcSampleReportItemName;
    /**
     * 仪器通道编码
     */
    private String instrumentChannel;
    /**
     * 质控结果值
     */
    private String qcResult;
    /**
     * 质控结果状态
     */
    private Integer qcResultStatus;
    /**
     * 质控结果状态描述
     */
    private String qcResultStatusDesc;
    /**
     * 客商仪器编码ID
     */
    private String instrumentId;
    /**
     * 客商仪器名称
     */
    private String instrumentName;
    /**
     * 客商业务ID
     */
    private String customerId;
    /**
     * 客商名称
     */
    private String customerName;
    /**
     * 机构ID
     */
    private String orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 序号
     */
    private Integer sort;
    /**
     * 是否有效
     */
    private Integer isUse;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 修改时间
     */
    private LocalDateTime updateDate;

}
