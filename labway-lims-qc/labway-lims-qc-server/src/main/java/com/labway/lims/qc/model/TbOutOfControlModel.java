package com.labway.lims.qc.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description tb_out_of_control_model
 * @date 2022-09-09
 */
@Data
public class TbOutOfControlModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer outOfControlSysno;
    /**
     * 失控模板编码id
     */
    private String outOfControlId;

    /**
     * 客商业务id
     */
    private String customerId;

    /**
     * 客商名称
     */
    private String customerName;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 失控模板地址
     */
    private String outOfControlUrl;

    /**
     * 失控模板状态
     */
    private int outOfControlStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 是否删除
     */
    private int isDelete;

    /**
     * 自定义属性1
     */
    private String selfAttribute1;

    /**
     * 自定义属性2
     */
    private String selfAttribute2;

    /**
     * 自定义属性3
     */
    private String selfAttribute3;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;

    public TbOutOfControlModel() {
    }
}
