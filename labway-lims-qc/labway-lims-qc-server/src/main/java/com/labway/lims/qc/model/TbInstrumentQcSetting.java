package com.labway.lims.qc.model;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 仪器质控配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
public class TbInstrumentQcSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仪器质控设置系统编号
     */
    private Integer instrumentQcSysno;

    /**
     * 仪器质控设置编码
     */
    private String instrumentQcId;

    /**
     * 客商仪器编码ID
     */
    private String instrumentId;

    /**
     * 仪器通道编码
     */
    private String instrumentChannel;

    /**
     * 客商仪器名称
     */
    private String instrumentName;

    /**
     * 客商报告项目编码ID
     */
    private String customerReportItemId;

    /**
     * 客商报告项目编码
     */
    private String customerReportItemCode;

    /**
     * 客商报告项目名称
     */
    private String customerReportItem;

    /**
     * 客商业务ID
     */
    private String customerId;

    /**
     * 客商名称
     */
    private String customerName;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 是否有效
     */
    private Boolean isUse;

    /**
     * 质控设置状态
     */
    private Integer qcSettingStatus;

    /**
     * 质控设置状态描述
     */
    private String qcSettingStatusDesc;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 自定义属性1
     */
    private String selfAttribute1;

    /**
     * 自定义属性2
     */
    private String selfAttribute2;

    /**
     * 自定义属性3
     */
    private String selfAttribute3;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;


    public Integer getInstrumentQcSysno() {
        return instrumentQcSysno;
    }

    public void setInstrumentQcSysno(Integer instrumentQcSysno) {
        this.instrumentQcSysno = instrumentQcSysno;
    }

    public String getInstrumentQcId() {
        return instrumentQcId;
    }

    public void setInstrumentQcId(String instrumentQcId) {
        this.instrumentQcId = instrumentQcId;
    }

    public String getInstrumentId() {
        return instrumentId;
    }

    public void setInstrumentId(String instrumentId) {
        this.instrumentId = instrumentId;
    }

    public String getInstrumentChannel() {
        return instrumentChannel;
    }

    public void setInstrumentChannel(String instrumentChannel) {
        this.instrumentChannel = instrumentChannel;
    }

    public String getInstrumentName() {
        return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }

    public String getCustomerReportItemId() {
        return customerReportItemId;
    }

    public void setCustomerReportItemId(String customerReportItemId) {
        this.customerReportItemId = customerReportItemId;
    }

    public String getCustomerReportItemCode() {
        return customerReportItemCode;
    }

    public void setCustomerReportItemCode(String customerReportItemCode) {
        this.customerReportItemCode = customerReportItemCode;
    }

    public String getCustomerReportItem() {
        return customerReportItem;
    }

    public void setCustomerReportItem(String customerReportItem) {
        this.customerReportItem = customerReportItem;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Boolean getUse() {
        return isUse;
    }

    public void setUse(Boolean isUse) {
        this.isUse = isUse;
    }

    public Integer getQcSettingStatus() {
        return qcSettingStatus;
    }

    public void setQcSettingStatus(Integer qcSettingStatus) {
        this.qcSettingStatus = qcSettingStatus;
    }

    public String getQcSettingStatusDesc() {
        return qcSettingStatusDesc;
    }

    public void setQcSettingStatusDesc(String qcSettingStatusDesc) {
        this.qcSettingStatusDesc = qcSettingStatusDesc;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getSelfAttribute1() {
        return selfAttribute1;
    }

    public void setSelfAttribute1(String selfAttribute1) {
        this.selfAttribute1 = selfAttribute1;
    }

    public String getSelfAttribute2() {
        return selfAttribute2;
    }

    public void setSelfAttribute2(String selfAttribute2) {
        this.selfAttribute2 = selfAttribute2;
    }

    public String getSelfAttribute3() {
        return selfAttribute3;
    }

    public void setSelfAttribute3(String selfAttribute3) {
        this.selfAttribute3 = selfAttribute3;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "TbInstrumentQcSetting{" +
                "instrumentQcSysno=" + instrumentQcSysno +
                ", instrumentQcId=" + instrumentQcId +
                ", instrumentId=" + instrumentId +
                ", instrumentChannel=" + instrumentChannel +
                ", instrumentName=" + instrumentName +
                ", customerReportItemId=" + customerReportItemId +
                ", customerReportItemCode=" + customerReportItemCode +
                ", customerReportItem=" + customerReportItem +
                ", customerId=" + customerId +
                ", customerName=" + customerName +
                ", orgId=" + orgId +
                ", orgName=" + orgName +
                ", isUse=" + isUse +
                ", qcSettingStatus=" + qcSettingStatus +
                ", qcSettingStatusDesc=" + qcSettingStatusDesc +
                ", operator=" + operator +
                ", operatorId=" + operatorId +
                ", isDelete=" + isDelete +
                ", selfAttribute1=" + selfAttribute1 +
                ", selfAttribute2=" + selfAttribute2 +
                ", selfAttribute3=" + selfAttribute3 +
                ", createDate=" + createDate +
                ", updateDate=" + updateDate +
                "}";
    }
}
