package com.labway.lims.qc.model;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 仪器质控设置记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
public class TbQcSetRecordMain implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 质控记录系统编号
     */
    private Long qcRecordSysno;

    /**
     * 质控记录编号
     */
    private String qcRecordMainId;

    /**
     * 仪器质控设置编码
     */
    private String instrumentQcId;

    /**
     * 质控批次编号
     */
    private String qcBatchId;

    /**
     * 客商业务ID
     */
    private String customerId;

    /**
     * 客商名称
     */
    private String customerName;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 客商仪器编码ID
     */
    private String instrumentId;

    /**
     * 客商仪器名称
     */
    private String instrumentName;

    /**
     * 客商报告项目编码ID
     */
    private String customerReportItemId;

    /**
     * 客商报告项目编码
     */
    private String customerReportItemCode;

    /**
     * 客商报告项目名称
     */
    private String customerReportItem;

    /**
     * 仪器通道编码
     */
    private String instrumentChannel;

    /**
     * 质控品名称
     */
    private String qcReagentName;

    /**
     * NCC试剂耗材主单位
     */
    private String nccMainUnit;

    /**
     * NCC主单位描述
     */
    private String nccMainUnitDesc;

    /**
     * NCC辅单位
     */
    private String nccSubUnit;

    /**
     * NCC辅单位描述
     */
    private String nccSubUnitDesc;

    /**
     * 质控品厂家
     */
    private String qcManufactoryName;

    /**
     * 设置日期
     */
    private LocalDateTime setDate;

    /**
     * 生效日期
     */
    private LocalDateTime qcValTime;

    /**
     * 失效日期
     */
    private LocalDateTime qcExpireTime;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 质控记录状态
     */
    private Integer qcRecordStatus;

    /**
     * 质控记录状态描述
     */
    private String qcRecordStatusDesc;

    /**
     * 是否有效
     */
    private Boolean isUse;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 自定义属性1
     */
    private String selfAttribute1;

    /**
     * 自定义属性2
     */
    private String selfAttribute2;

    /**
     * 自定义属性3
     */
    private String selfAttribute3;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;


    public Long getQcRecordSysno() {
        return qcRecordSysno;
    }

    public void setQcRecordSysno(Long qcRecordSysno) {
        this.qcRecordSysno = qcRecordSysno;
    }

    public String getQcRecordMainId() {
        return qcRecordMainId;
    }

    public void setQcRecordMainId(String qcRecordMainId) {
        this.qcRecordMainId = qcRecordMainId;
    }

    public String getInstrumentQcId() {
        return instrumentQcId;
    }

    public void setInstrumentQcId(String instrumentQcId) {
        this.instrumentQcId = instrumentQcId;
    }

    public String getQcBatchId() {
        return qcBatchId;
    }

    public void setQcBatchId(String qcBatchId) {
        this.qcBatchId = qcBatchId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getInstrumentId() {
        return instrumentId;
    }

    public void setInstrumentId(String instrumentId) {
        this.instrumentId = instrumentId;
    }

    public String getInstrumentName() {
        return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }

    public String getCustomerReportItemId() {
        return customerReportItemId;
    }

    public void setCustomerReportItemId(String customerReportItemId) {
        this.customerReportItemId = customerReportItemId;
    }

    public String getCustomerReportItemCode() {
        return customerReportItemCode;
    }

    public void setCustomerReportItemCode(String customerReportItemCode) {
        this.customerReportItemCode = customerReportItemCode;
    }

    public String getCustomerReportItem() {
        return customerReportItem;
    }

    public void setCustomerReportItem(String customerReportItem) {
        this.customerReportItem = customerReportItem;
    }

    public String getInstrumentChannel() {
        return instrumentChannel;
    }

    public void setInstrumentChannel(String instrumentChannel) {
        this.instrumentChannel = instrumentChannel;
    }

    public String getQcReagentName() {
        return qcReagentName;
    }

    public void setQcReagentName(String qcReagentName) {
        this.qcReagentName = qcReagentName;
    }

    public String getNccMainUnit() {
        return nccMainUnit;
    }

    public void setNccMainUnit(String nccMainUnit) {
        this.nccMainUnit = nccMainUnit;
    }

    public String getNccMainUnitDesc() {
        return nccMainUnitDesc;
    }

    public void setNccMainUnitDesc(String nccMainUnitDesc) {
        this.nccMainUnitDesc = nccMainUnitDesc;
    }

    public String getNccSubUnit() {
        return nccSubUnit;
    }

    public void setNccSubUnit(String nccSubUnit) {
        this.nccSubUnit = nccSubUnit;
    }

    public String getNccSubUnitDesc() {
        return nccSubUnitDesc;
    }

    public void setNccSubUnitDesc(String nccSubUnitDesc) {
        this.nccSubUnitDesc = nccSubUnitDesc;
    }

    public String getQcManufactoryName() {
        return qcManufactoryName;
    }

    public void setQcManufactoryName(String qcManufactoryName) {
        this.qcManufactoryName = qcManufactoryName;
    }

    public LocalDateTime getSetDate() {
        return setDate;
    }

    public void setSetDate(LocalDateTime setDate) {
        this.setDate = setDate;
    }

    public LocalDateTime getQcValTime() {
        return qcValTime;
    }

    public void setQcValTime(LocalDateTime qcValTime) {
        this.qcValTime = qcValTime;
    }

    public LocalDateTime getQcExpireTime() {
        return qcExpireTime;
    }

    public void setQcExpireTime(LocalDateTime qcExpireTime) {
        this.qcExpireTime = qcExpireTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getQcRecordStatus() {
        return qcRecordStatus;
    }

    public void setQcRecordStatus(Integer qcRecordStatus) {
        this.qcRecordStatus = qcRecordStatus;
    }

    public String getQcRecordStatusDesc() {
        return qcRecordStatusDesc;
    }

    public void setQcRecordStatusDesc(String qcRecordStatusDesc) {
        this.qcRecordStatusDesc = qcRecordStatusDesc;
    }

    public Boolean getUse() {
        return isUse;
    }

    public void setUse(Boolean isUse) {
        this.isUse = isUse;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getSelfAttribute1() {
        return selfAttribute1;
    }

    public void setSelfAttribute1(String selfAttribute1) {
        this.selfAttribute1 = selfAttribute1;
    }

    public String getSelfAttribute2() {
        return selfAttribute2;
    }

    public void setSelfAttribute2(String selfAttribute2) {
        this.selfAttribute2 = selfAttribute2;
    }

    public String getSelfAttribute3() {
        return selfAttribute3;
    }

    public void setSelfAttribute3(String selfAttribute3) {
        this.selfAttribute3 = selfAttribute3;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "TbQcSetRecordMain{" +
                "qcRecordSysno=" + qcRecordSysno +
                ", qcRecordMainId=" + qcRecordMainId +
                ", instrumentQcId=" + instrumentQcId +
                ", qcBatchId=" + qcBatchId +
                ", customerId=" + customerId +
                ", customerName=" + customerName +
                ", orgId=" + orgId +
                ", orgName=" + orgName +
                ", instrumentId=" + instrumentId +
                ", instrumentName=" + instrumentName +
                ", customerReportItemId=" + customerReportItemId +
                ", customerReportItemCode=" + customerReportItemCode +
                ", customerReportItem=" + customerReportItem +
                ", instrumentChannel=" + instrumentChannel +
                ", qcReagentName=" + qcReagentName +
                ", nccMainUnit=" + nccMainUnit +
                ", nccMainUnitDesc=" + nccMainUnitDesc +
                ", nccSubUnit=" + nccSubUnit +
                ", nccSubUnitDesc=" + nccSubUnitDesc +
                ", qcManufactoryName=" + qcManufactoryName +
                ", setDate=" + setDate +
                ", qcValTime=" + qcValTime +
                ", qcExpireTime=" + qcExpireTime +
                ", remark=" + remark +
                ", qcRecordStatus=" + qcRecordStatus +
                ", qcRecordStatusDesc=" + qcRecordStatusDesc +
                ", isUse=" + isUse +
                ", operator=" + operator +
                ", operatorId=" + operatorId +
                ", isDelete=" + isDelete +
                ", selfAttribute1=" + selfAttribute1 +
                ", selfAttribute2=" + selfAttribute2 +
                ", selfAttribute3=" + selfAttribute3 +
                ", createDate=" + createDate +
                ", updateDate=" + updateDate +
                "}";
    }
    
}
