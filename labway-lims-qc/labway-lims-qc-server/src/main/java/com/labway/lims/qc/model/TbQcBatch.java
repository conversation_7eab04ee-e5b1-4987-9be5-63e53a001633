package com.labway.lims.qc.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 质控批次信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */

@Setter
@Getter
public class TbQcBatch implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 质控批次系统编号
     */
    private Integer qcBatchSysno;

    /**
     * 质控批次编号
     */
    private String qcBatchId;

    /**
     * 试剂耗材编码
     */
    private String reagentId;

    /**
     * 质控品名称
     */
    private String qcReagentName;

    /**
     * 质控品厂家
     */
    private String qcManufactoryName;

    /**
     * NCC试剂耗材主单位
     */
    private String nccMainUnit;

    /**
     * NCC主单位描述
     */
    private String nccMainUnitDesc;

    /**
     * NCC辅单位
     */
    private String nccSubUnit;

    /**
     * NCC辅单位描述
     */
    private String nccSubUnitDesc;

    /**
     * 试剂耗材品牌
     */
    private String reagentBrandName;

    /**
     * 生效日期
     */
    private LocalDateTime qcValTime;

    /**
     * 失效日期
     */
    private LocalDateTime qcExpireTime;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 是否有效
     */
    private Boolean isUse;

    /**
     * 客商业务ID
     */
    private String customerId;

    /**
     * 客商名称
     */
    private String customerName;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改时间
     */
    private LocalDateTime updateDate;

}
