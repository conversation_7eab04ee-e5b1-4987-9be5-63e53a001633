<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.bloodculture.mapper.BloodCultureSampleResultMapper">

    <insert id="inserts">
        INSERT INTO tb_bloodculture_sample_result (
        bloodculture_sample_result_id,bloodculture_sample_id,apply_sample_id,apply_id,result,result_code,position,create_date,
        update_date,updater_id,updater_name,creator_id,creator_name,org_id,org_name,is_delete
        )
        values
        <foreach collection="results" item="item" index="index" separator=",">
            (
            #{item.bloodCultureSampleResultId},
            #{item.bloodCultureSampleId},
            #{item.applySampleId},
            #{item.applyId},
            #{item.result},
            #{item.resultCode},
            #{item.position},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.orgId},
            #{item.orgName},
            #{item.isDelete}
            )
        </foreach>
    </insert>
</mapper>
