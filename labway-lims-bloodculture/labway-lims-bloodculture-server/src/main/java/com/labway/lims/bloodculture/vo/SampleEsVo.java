package com.labway.lims.bloodculture.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
public class SampleEsVo {


    /**
     * ID
     */
    private Long bloodCultureSampleId;

    /**
     * 样本申请单ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 样本状态
     *
     * @see SampleStatusEnum
     */
    private Integer status;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 检验日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDate;

    /**
     * 录入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date enterDate;

    /**
     * 审核人ID
     */
    private Long checkerId;

    /**
     * 一次审核工号
     */
    private String checkerName;


    /**
     * 审核日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date checkDate;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 样本是否加急 (1急诊 0不急)
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 检验项目名称（检验目的）
     */
    private List<String> testItemNames;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 门诊号 住院号 就诊卡号
     */
    private String patientVisitCard;

    /**
     * 是否打印
     */
    private Integer isPrint;

    /**
     * 就诊类型
     */
    private String applyType;
    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;

    /**
     * 检验者id
     */
    private Long testerId;

    /**
     * 检验者名称
     */
    private String testerName;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 送检医生
     */
    private String sendDoctorName;

    /**
     * 送检时间 （签收时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date applyDate;

    /**
     * 终审人
     */
    private String finalCheckerName;

    public Date getCheckDate() {
        if (Objects.isNull(checkDate) || Objects.equals(DefaultDateEnum.DEFAULT_DATE.getDate(), checkDate)) {
            return null;
        }
        return checkDate;
    }
}
