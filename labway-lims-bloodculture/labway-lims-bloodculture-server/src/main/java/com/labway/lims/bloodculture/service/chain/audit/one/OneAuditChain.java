package com.labway.lims.bloodculture.service.chain.audit.one;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class OneAuditChain extends ChainBase implements InitializingBean {
    @Resource
    private OneAuditFillInfoCommand oneAuditFillInfoCommand;

    @Resource
    private OneAuditBuildReportCommand oneAuditBuildReportCommand;

    @Resource
    private OneAuditRemoveTwoResultCommand oneAuditRemoveTwoResultCommand;

    @Resource
    private OneAuditSampleAuditFlowCommand oneAuditSampleAuditFlowCommand;

    @Resource
    private OneAuditSamplesAuditRabbitMqCommand oneAuditSamplesAuditRabbitMqCommand;

    @Resource
    private OneAuditUpdateSampleCommand oneAuditUpdateSampleCommand;
    @Resource
    private OneAuditLimitCommand oneAuditLimitCommand;


    @Override
    public void afterPropertiesSet() throws Exception {

        // 限流
        addCommand(oneAuditLimitCommand);

        // 补充数据
        addCommand(oneAuditFillInfoCommand);

        // 生成报告单
        addCommand(oneAuditBuildReportCommand);

        // 修改信息
        addCommand(oneAuditUpdateSampleCommand);

        // 删除终审结果
        addCommand(oneAuditRemoveTwoResultCommand);

        // 添加条码环节
        addCommand(oneAuditSampleAuditFlowCommand);

        // 发送MQ
        addCommand(oneAuditSamplesAuditRabbitMqCommand);

        // 完成
        addCommand((c) -> PROCESSING_COMPLETE);
    }
}
