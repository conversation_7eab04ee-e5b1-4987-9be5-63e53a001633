package com.labway.lims.bloodculture.service.chain.positive;

import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

@Getter
@Setter
public class PositiveContext extends StopWatchContext {


    /**
     * 样本id
     */
    private Long bloodCultureSampleId;

    /**
     * 血培养样本
     */
    private BloodCultureSampleDto bloodCultureSample;

    /**
     * 申请单样本
     */
    private ApplySampleDto applySample;

    /**
     * 申请单血培养项目
     */
    private ApplySampleItemDto applySampleItem;

    /**
     * 血培养项目
     */
    private ApplySampleItemBloodCultureDto applySampleItemBloodCulture;

    /**
     * 新的 申请单样本
     */
    private ApplySampleDto newApplySample;

    /**
     * 新的 逻辑试管架
     */
    private RackLogicDto newRackLogic;

    /**
     * 新的 申请单血培养项目
     */
    private ApplySampleItemDto newApplySampleItem;

    /**
     * 新的 血培养项目
     */
    private ApplySampleItemBloodCultureDto newApplySampleItemBloodCulture;


    @Override
    protected String getWatcherName() {
        return "标记阳性";
    }


    public static PositiveContext from(Context c) {
        return (PositiveContext) c;
    }
}
