package com.labway.lims.bloodculture.service.chain.audit.two;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class TwoAuditChain extends ChainBase implements InitializingBean {
    @Resource
    private TwoAuditFillInfoCommand twoAuditFillInfoCommand;

    @Resource
    private TwoAuditBuildReportCommand twoAuditBuildReportCommand;

    @Resource
    private TwoAuditSampleAuditFlowCommand twoAuditSampleAuditFlowCommand;

    @Resource
    private TwoAuditSamplesAuditRabbitMqCommand twoAuditSamplesAuditRabbitMqCommand;

    @Resource
    private TwoAuditUpdateSampleCommand twoAuditUpdateSampleCommand;
    @Resource
    private TwoAuditLimitCommand twoAuditLimitCommand;


    @Override
    public void afterPropertiesSet() throws Exception {

        // 限流
        addCommand(twoAuditLimitCommand);

        // 补充数据
        addCommand(twoAuditFillInfoCommand);

        // 生成报告单
        addCommand(twoAuditBuildReportCommand);

        // 修改信息
        addCommand(twoAuditUpdateSampleCommand);

        // 添加条码环节
        addCommand(twoAuditSampleAuditFlowCommand);

        // 发送MQ
        addCommand(twoAuditSamplesAuditRabbitMqCommand);

        // 完成
        addCommand((c) -> PROCESSING_COMPLETE);
    }
}
