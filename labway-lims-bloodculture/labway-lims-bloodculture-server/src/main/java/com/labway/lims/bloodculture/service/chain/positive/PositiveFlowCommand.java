package com.labway.lims.bloodculture.service.chain.positive;

import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * 添加条码环节
 */
@Slf4j
@Component
class PositiveFlowCommand implements Command, Filter {
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        final PositiveContext context = PositiveContext.from(c);
        final ApplySampleDto applySample = context.getApplySample();
        final LinkedList<Long> ids = snowflakeService.genIds(2);

        final List<SampleFlowDto> flows = new ArrayList<>();
        flows.add(SampleFlowDto.builder()
                .sampleFlowId(ids.pop())
                .applyId(applySample.getApplyId())
                .applySampleId(applySample.getApplySampleId())
                .barcode(applySample.getBarcode())
                .operateCode(BarcodeFlowEnum.BC_MARK_POSITIVE.name())
                .operateName(BarcodeFlowEnum.BC_MARK_POSITIVE.getDesc())
                .barcode(applySample.getBarcode())
                .content(BarcodeFlowEnum.BC_MARK_POSITIVE.getDesc()).build());
        
        flows.add(SampleFlowDto.builder()
                .sampleFlowId(ids.pop())
                .applyId(context.getNewApplySample().getApplyId())
                .applySampleId(context.getNewApplySample().getApplySampleId())
                .barcode(context.getNewApplySample().getBarcode())
                .operateCode(BarcodeFlowEnum.BC_MARK_POSITIVE.name())
                .operateName(BarcodeFlowEnum.BC_MARK_POSITIVE.getDesc())
                .barcode(context.getNewApplySample().getBarcode())
                .content(BarcodeFlowEnum.BC_MARK_POSITIVE.getDesc()).build());

        sampleFlowService.addSampleFlows(flows);

        return CONTINUE_PROCESSING;
    }


    @Override
    public boolean postprocess(Context c, Exception exception) {

        return CONTINUE_PROCESSING;
    }
}
