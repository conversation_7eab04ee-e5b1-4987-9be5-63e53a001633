package com.labway.lims.bloodculture.service.chain.positive;

import com.labway.lims.apply.api.service.SampleFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 复制条码环节
 */
@Slf4j
@Component
class PositiveCopySampleFlowCommand implements Command, Filter {
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {
        final PositiveContext context = PositiveContext.from(c);

        sampleFlowService.copySampleFlows(context.getApplySample().getApplySampleId(),
                context.getNewApplySample().getApplySampleId());

        return CONTINUE_PROCESSING;
    }


    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
