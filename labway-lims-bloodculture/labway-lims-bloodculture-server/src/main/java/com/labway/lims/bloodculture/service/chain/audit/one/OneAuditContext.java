package com.labway.lims.bloodculture.service.chain.audit.one;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

@Getter
@Setter
public class OneAuditContext extends StopWatchContext {

    /**
     * 样本
     */
    static final String SAMPLE = IdUtil.objectId();

    /**
     * 申请单
     */
    static final String APPLY = IdUtil.objectId();

    /**
     * 申请单样本
     */
    static final String APPLY_SAMPLE = IdUtil.objectId();

    /**
     * 报告
     */
    static final String REPORT = IdUtil.objectId();

    /**
     * 样本id
     */
    private Long bloodCultureSampleId;


    @Override
    protected String getWatcherName() {
        return "初审";
    }

    public static OneAuditContext from(Context c) {
        return (OneAuditContext) c;
    }

    public BloodCultureSampleDto getSample() {
        return (BloodCultureSampleDto) get(SAMPLE);
    }

    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }

    public ApplyDto getApply() {
        return (ApplyDto) get(APPLY);
    }

    public SampleReportDto getSampleReport() {
        return (SampleReportDto) get(REPORT);
    }

}
