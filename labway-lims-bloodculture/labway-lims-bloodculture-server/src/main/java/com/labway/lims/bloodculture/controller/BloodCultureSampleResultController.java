package com.labway.lims.bloodculture.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleResultDto;
import com.labway.lims.bloodculture.api.enums.BloodCultureSampleReportEnum;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleResultService;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.bloodculture.vo.AddBloodCultureResultVo;
import com.labway.lims.bloodculture.vo.UpdateBloodCultureResultVo;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/blood-culture-result")
public class BloodCultureSampleResultController extends BaseController {
    @Resource
    private BloodCultureSampleService bloodCultureSampleService;
    @Resource
    private BloodCultureSampleResultService bloodCultureSampleResultService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @PostMapping("/add")
    public Object add(@RequestBody AddBloodCultureResultVo vo) {
        final BloodCultureSampleDto sample = bloodCultureSampleService.selectByBloodCultureSampleId(vo.getBloodCultureSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        if (!Objects.equals(sample.getOneCheckerId(), NumberUtils.LONG_ZERO) &&
                Objects.equals(vo.getPosition(), BloodCultureSampleReportEnum.ONE.name())) {
            throw new IllegalArgumentException("样本已经初审，无法添加结果");
        } else if (!Objects.equals(sample.getTwoCheckerId(), NumberUtils.LONG_ZERO) &&
                Objects.equals(vo.getPosition(), BloodCultureSampleReportEnum.TWO.name())) {
            throw new IllegalArgumentException("样本已经终审，无法添加结果");
        }

        final BloodCultureSampleResultDto e = new BloodCultureSampleResultDto();
        e.setBloodCultureSampleResultId(snowflakeService.genId());
        e.setBloodCultureSampleId(sample.getBloodCultureSampleId());
        e.setApplySampleId(sample.getApplySampleId());
        e.setApplyId(sample.getApplyId());
        e.setResult(vo.getResult());
        e.setResultCode(vo.getResultCode());
        e.setPosition(vo.getPosition());
        e.setCreateDate(new Date());
        e.setUpdateDate(new Date());
        e.setUpdaterId(LoginUserHandler.get().getUserId());
        e.setUpdaterName(LoginUserHandler.get().getNickname());
        e.setCreatorId(LoginUserHandler.get().getUserId());
        e.setCreatorName(LoginUserHandler.get().getNickname());
        e.setOrgId(LoginUserHandler.get().getOrgId());
        e.setOrgName(LoginUserHandler.get().getOrgName());
        e.setIsDelete(YesOrNoEnum.NO.getCode());

        sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                .applyId(sample.getApplyId())
                .applySampleId(sample.getApplySampleId())
                .barcode(sample.getBarcode())
                .operateCode(BarcodeFlowEnum.BC_ADD_SAMPLE_RESULT.name())
                .operateName(BarcodeFlowEnum.BC_ADD_SAMPLE_RESULT.getDesc())
                .barcode(sample.getBarcode())
                .content(String.format("添加 [%s] 检验结果 [%s]", Objects.equals(vo.getPosition(),
                        BloodCultureSampleReportEnum.ONE.name()) ? "初步" : "最终", e.getResult())).build());

        return Map.of("id", bloodCultureSampleResultService.addBloodCultureSampleResult(e));
    }

    @PostMapping("/update")
    public Object update(@RequestBody UpdateBloodCultureResultVo vo) {
        final BloodCultureSampleResultDto result = bloodCultureSampleResultService.selectByBloodCultureSampleResultId(vo.getBloodCultureSampleResultId());
        if (Objects.isNull(result)) {
            throw new IllegalArgumentException("结果不存在");
        }
        final BloodCultureSampleDto sample = bloodCultureSampleService.selectByBloodCultureSampleId(result.getBloodCultureSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        if (!Objects.equals(sample.getOneCheckerId(), NumberUtils.LONG_ZERO) &&
                Objects.equals(result.getPosition(), BloodCultureSampleReportEnum.ONE.name())) {
            throw new IllegalArgumentException("样本已经初审，无法修改结果");
        } else if (!Objects.equals(sample.getTwoCheckerId(), NumberUtils.LONG_ZERO) &&
                Objects.equals(result.getPosition(), BloodCultureSampleReportEnum.TWO.name())) {
            throw new IllegalArgumentException("样本已经终审，无法修改结果");
        }

        // 删除之前的
        bloodCultureSampleResultService.deleteByBloodCultureSampleResultId(vo.getBloodCultureSampleResultId());

        final BloodCultureSampleResultDto e = new BloodCultureSampleResultDto();
        e.setBloodCultureSampleResultId(snowflakeService.genId());
        e.setBloodCultureSampleId(sample.getBloodCultureSampleId());
        e.setApplySampleId(sample.getApplySampleId());
        e.setApplyId(sample.getApplyId());
        e.setResult(vo.getResult());
        e.setResultCode(vo.getResultCode());
        e.setPosition(result.getPosition());
        e.setCreateDate(new Date());
        e.setUpdateDate(new Date());
        e.setUpdaterId(LoginUserHandler.get().getUserId());
        e.setUpdaterName(LoginUserHandler.get().getNickname());
        e.setCreatorId(LoginUserHandler.get().getUserId());
        e.setCreatorName(LoginUserHandler.get().getNickname());
        e.setOrgId(LoginUserHandler.get().getOrgId());
        e.setOrgName(LoginUserHandler.get().getOrgName());
        e.setIsDelete(YesOrNoEnum.NO.getCode());

        sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                .applyId(sample.getApplyId())
                .applySampleId(sample.getApplySampleId())
                .barcode(sample.getBarcode())
                .operateCode(BarcodeFlowEnum.BC_ADD_SAMPLE_RESULT.name())
                .operateName(BarcodeFlowEnum.BC_ADD_SAMPLE_RESULT.getDesc())
                .barcode(sample.getBarcode())
                .content(String.format("修改 [%s] 结果 从 [%s] 修改成 [%s]", Objects.equals(result.getPosition(),
                        BloodCultureSampleReportEnum.ONE.name()) ? "初步" : "最终", result.getResult(), vo.getResult())).build());

        return Map.of("id", bloodCultureSampleResultService.addBloodCultureSampleResult(e));
    }

    @DeleteMapping("/delete")
    public Object delete(Long bloodCultureSampleResultId) {

        final BloodCultureSampleResultDto result = bloodCultureSampleResultService.selectByBloodCultureSampleResultId(bloodCultureSampleResultId);
        if (Objects.isNull(result)) {
            throw new IllegalArgumentException("结果不存在");
        }

        final BloodCultureSampleDto sample = bloodCultureSampleService.selectByBloodCultureSampleId(result.getBloodCultureSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        if (!Objects.equals(sample.getOneCheckerId(), NumberUtils.LONG_ZERO) &&
                Objects.equals(result.getPosition(), BloodCultureSampleReportEnum.ONE.name())) {
            throw new IllegalArgumentException("样本已经初审，无法删除结果");
        } else if (!Objects.equals(sample.getTwoCheckerId(), NumberUtils.LONG_ZERO) &&
                Objects.equals(result.getPosition(), BloodCultureSampleReportEnum.TWO.name())) {
            throw new IllegalArgumentException("样本已经终审，无法删除结果");
        }

        // 删除结果
        bloodCultureSampleResultService.deleteByBloodCultureSampleResultId(bloodCultureSampleResultId);

        // 条码环节
        sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                .applyId(sample.getApplyId())
                .applySampleId(sample.getApplySampleId())
                .barcode(sample.getBarcode())
                .operateCode(BarcodeFlowEnum.BC_DEL_SAMPLE_RESULT.name())
                .operateName(BarcodeFlowEnum.BC_DEL_SAMPLE_RESULT.getDesc())
                .barcode(sample.getBarcode())
                .content(String.format("删除 [%s] 检验结果 [%s]", Objects.equals(result.getPosition(), BloodCultureSampleReportEnum.ONE.name()) ? "初步" : "最终",
                        result.getResult())).build());

        return Map.of();
    }


}
