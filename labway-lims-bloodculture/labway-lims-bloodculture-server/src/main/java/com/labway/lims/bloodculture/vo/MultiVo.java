package com.labway.lims.bloodculture.vo;

import com.labway.lims.bloodculture.api.enums.BloodCultureSampleReportEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 批量录入
 */
@Getter
@Setter
public class MultiVo {
    /**
     * 样本id
     */
    private List<Long> bloodCultureSampleIds;

    /**
     * 位置
     *
     * @see BloodCultureSampleReportEnum
     */
    private String position;

    /**
     * 结果
     */
    private List<Result> results;


    @Getter
    @Setter
    public static class Result {
        private String result;

        private String resultCode;
    }
}
