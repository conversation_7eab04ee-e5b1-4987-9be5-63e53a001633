package com.labway.lims.bloodculture.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class QueryResultBloodCultureSamplesVo {


    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 审核日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date checkDateStart;

    /**
     * 审核日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date checkDateEnd;

    /**
     * 检验日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateEnd;

    /**
     * 检验者ID
     */
    private Long testerId;

    /**
     * 审核人ID
     */
    private Long checkerId;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 样本状态
     *
     * @see SampleStatusEnum#name()
     */
    private String sampleStatus;

    /**
     * 检验结果
     */
    private List<String> resultCodes;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 就诊类型
     */
    private String applyType;

    /**
     * 条码号
     */
    private String barcode;

}
