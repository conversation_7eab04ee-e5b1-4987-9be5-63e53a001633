package com.labway.lims.bloodculture.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 *
 */
@Getter
@Setter
public class QuerySampleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检验日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateEnd;


    /**
     * 送检机构ID
     */
    private Long hspOrgId;


    /**
     * 样本状态
     *
     * @see SampleStatusEnum#name()
     */
    private String sampleStatus;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 仅显示无结果的
     */
    private Boolean onlyNoResult;
}
