package com.labway.lims.bloodculture.service.chain.audit.one;

import com.labway.lims.bloodculture.api.enums.BloodCultureSampleReportEnum;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleRemarkService;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 删除终审结果和备注
 */
@Slf4j
@Component
class OneAuditRemoveTwoResultCommand implements Command, Filter {
    @Resource
    private BloodCultureSampleResultService bloodCultureSampleResultService;
    @Resource
    private BloodCultureSampleRemarkService bloodCultureSampleRemarkService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OneAuditContext context = OneAuditContext.from(c);

        bloodCultureSampleResultService.deleteByBloodCultureSampleId(context.getBloodCultureSampleId(),
                BloodCultureSampleReportEnum.TWO.name());

        bloodCultureSampleRemarkService.deleteByBloodCultureSampleId(context.getBloodCultureSampleId(),
                BloodCultureSampleReportEnum.TWO.name());

        log.info("血培养样本 [{}] 删除终审结果和备注", context.getApplySample().getBarcode());

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
