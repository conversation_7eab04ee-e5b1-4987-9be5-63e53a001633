package com.labway.lims.bloodculture.vo;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@EqualsAndHashCode(callSuper = true)
@Data
public class ExportSampleEsVo extends SampleEsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 处理时间导出精度问题转string
     */
    @Getter(value = AccessLevel.PRIVATE)
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    /**
     * 序号
     */
    private Integer num;


    /**
     * 状态名
     */
    private String statusName;


    /**
     * 初始结果
     */
    private String firstResult;


    /**
     * 最终结果
     */
    private String finalResult;

    /**
     * 科室
     */
    private String dept;

    /**
     * 检验项目名称s
     */
    private String testItemName;

    /**
     * 检验日期
     */
    private String testDateStr;

    /**
     * 审核日期
     */
    private String checkDateStr;

    /**
     * 送检时间 （签收时间）
     */
    private String applyDateStr;


    @Override
    public void setStatus(Integer status){
        super.setStatus(status);
        if (Objects.equals(status, SampleStatusEnum.ONE_AUDIT.getCode())) {
            statusName = "初审";
        } else if (Objects.equals(status, SampleStatusEnum.AUDIT.getCode())) {
            statusName = "终审";
        }else {
            statusName = SampleStatusEnum.getStatusByCode(status).getDesc();
        }
    }

    @Override
    public void setTestItemNames(List<String> testItemNames){
        super.setTestItemNames(testItemNames);
        testItemName = CollUtil.join(super.getTestItemNames(), ",");
    }

    @Override
    public void setTestDate(Date testDate) {
        super.setTestDate(testDate);
        this.testDateStr = dateToStr(testDate);
    }

    @Override
    public void setCheckDate(Date checkDate) {
        super.setCheckDate(checkDate);
        this.checkDateStr = dateToStr(checkDate);
    }

    @Override
    public void setApplyDate(Date applyDate) {
        super.setApplyDate(applyDate);
        this.applyDateStr = dateToStr(applyDate);
    }

    private String dateToStr(Date date){
        if(Objects.isNull(date)){
            return Strings.EMPTY;
        }
        return sdf.format(date);
    }
}
