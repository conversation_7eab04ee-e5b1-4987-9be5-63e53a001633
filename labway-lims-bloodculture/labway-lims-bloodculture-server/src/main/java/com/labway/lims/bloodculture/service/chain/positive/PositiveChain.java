package com.labway.lims.bloodculture.service.chain.positive;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 标记阳性
 */
@Component
@Slf4j
public class PositiveChain extends ChainBase implements InitializingBean {

    @Resource
    private PositiveCopyApplySampleCommand positiveCopyApplySampleCommand;
    @Resource
    private PositiveCopyApplySampleItemBloodCultureCommand positiveCopyApplySampleItemBloodCultureCommand;
    @Resource
    private PositiveCopyApplySampleItemCommand positiveCopyApplySampleItemCommand;
    @Resource
    private PositiveFillInfoCommand positiveFillInfoCommand;
    @Resource
    private PositiveLimitCommand positiveLimitCommand;
    @Resource
    private PositiveTwoPickCommand positiveTwoPickCommand;
    @Resource
    private PositiveUpdateApplySampleCommand positiveUpdateApplySampleCommand;
    @Resource
    private PositiveFlowCommand positiveFlowCommand;
    @Resource
    private PositiveCopySampleFlowCommand positiveCopySampleFlowCommand;
    @Resource
    private PositiveNewTempRackCommand positiveNewTempRackCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 限流
        addCommand(positiveLimitCommand);

        // 填充数据
        addCommand(positiveFillInfoCommand);

        // 复制申请单样本
        addCommand(positiveCopyApplySampleCommand);

        // 复制申请单项目
        addCommand(positiveCopyApplySampleItemCommand);

        // 复制血培养项目
        addCommand(positiveCopyApplySampleItemBloodCultureCommand);

        // 复制试管架
        addCommand(positiveNewTempRackCommand);

        // 复制条码环节
        addCommand(positiveCopySampleFlowCommand);

        // 二次分拣
        addCommand(positiveTwoPickCommand);

        // 修改状态
        addCommand(positiveUpdateApplySampleCommand);

        // 条码环节
        addCommand(positiveFlowCommand);

        // 完成
        addCommand((c) -> PROCESSING_COMPLETE);
    }
}
