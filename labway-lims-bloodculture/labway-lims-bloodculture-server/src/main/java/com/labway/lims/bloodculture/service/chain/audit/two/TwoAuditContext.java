package com.labway.lims.bloodculture.service.chain.audit.two;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

@Getter
@Setter
public class TwoAuditContext extends StopWatchContext {

    /**
     * 样本
     */
    static final String SAMPLE = IdUtil.objectId();

    /**
     * 申请单
     */
    static final String APPLY = IdUtil.objectId();

    /**
     * 报告
     */
    static final String REPORT = IdUtil.objectId();

    /**
     * 样本id
     */
    private Long bloodCultureSampleId;


    @Override
    protected String getWatcherName() {
        return "终审";
    }

    public static TwoAuditContext from(Context c) {
        return (TwoAuditContext) c;
    }

    public BloodCultureSampleDto getSample() {
        return (BloodCultureSampleDto) get(SAMPLE);
    }

    public ApplyDto getApply() {
        return (ApplyDto) get(APPLY);
    }

    public SampleReportDto getSampleReport() {
        return (SampleReportDto) get(REPORT);
    }

}
