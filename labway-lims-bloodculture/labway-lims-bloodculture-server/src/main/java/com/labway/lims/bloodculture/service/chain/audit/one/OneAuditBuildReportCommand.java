package com.labway.lims.bloodculture.service.chain.audit.one;

import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleRemarkDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleResultDto;
import com.labway.lims.bloodculture.api.enums.BloodCultureSampleReportEnum;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleRemarkService;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleResultService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 生成报告单
 */
@Slf4j
@Component
class OneAuditBuildReportCommand implements Command {
    @Resource
    private Environment environment;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @Resource
    private BloodCultureSampleResultService bloodCultureSampleResultService;
    @Resource
    private BloodCultureSampleRemarkService bloodCultureSampleRemarkService;
    @DubboReference
    private PdfReportService pdfReportService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleReportService sampleReportService;

    @Override
    public boolean execute(Context c) throws Exception {

        final OneAuditContext context = OneAuditContext.from(c);
        final BloodCultureSampleDto sample = context.getSample();
        final ApplyDto apply = context.getApply();

        sample.setOneCheckerId(LoginUserHandler.get().getUserId());
        sample.setOneCheckerName(LoginUserHandler.get().getNickname());
        sample.setOneCheckDate(new Date());

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        final ProfessionalGroupDto group = groupService.selectByGroupId(sample.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException(String.format("专业组 [%s] 不存在", sample.getGroupName()));
        }

        final UserDto checker = userService.selectByUserId(LoginUserHandler.get().getUserId());
        if (Objects.isNull(checker)) {
            throw new IllegalStateException(String.format("审核人 [%s] 不存在", LoginUserHandler.get().getNickname()));
        }

        final UserDto tester = userService.selectByUserId(applySample.getTesterId());
        if (Objects.isNull(tester)) {
            throw new IllegalStateException(String.format("检验者 [%s] 不存在", applySample.getTesterName()));
        }

        final PdfReportParamDto param = new PdfReportParamDto();
        param.put("apply", Dict.of("masterBarcode", apply.getMasterBarcode(), "patientName", apply.getPatientName(),
                "patientAge", apply.getPatientAge(), "hspOrgName", apply.getHspOrgName(), "_apply",
                Dict.parse(apply)));

        applySample.setResultRemark(applySample.getResultRemark().replace("\n", "<br/>"));
        param.put("applySample",
                Dict.of("barcode", applySample.getBarcode(), "tubeName", applySample.getTubeName(), "sampleTypeName",
                        applySample.getSampleTypeName(), "groupName", applySample.getGroupName(), "onePickerName",
                        applySample.getOnePickerName(), "onePickDate", applySample.getOnePickDate(), "twoPickerName",
                        applySample.getTwoPickerName(), "twoPickDate", applySample.getTwoPickDate(), "_applySample",
                        Dict.parse(applySample)));

        param.put("sample",
                Dict.of("sampleNo", sample.getSampleNo(), "instrumentGroupName", sample.getInstrumentGroupName(),
                        "instrumentName", sample.getInstrumentName(), "testerName", applySample.getTesterName(),
                        "oneCheckerName", sample.getOneCheckerName(), "sampleRemark", applySample.getSampleRemark(),
                        "resultRemark", applySample.getResultRemark(), "_sample", Dict.parse(sample)));


        final List<BloodCultureSampleResultDto> results = bloodCultureSampleResultService.selectByBloodCultureSampleId(sample.getBloodCultureSampleId());
        final List<BloodCultureSampleRemarkDto> remarks = bloodCultureSampleRemarkService.selectByBloodCultureSampleId(sample.getBloodCultureSampleId());

        param.put("oneResults", results.stream().filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.ONE.name())).map(Dict::parse)
                .collect(Collectors.toList()));

        param.put("oneRemark", remarks.stream().filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.ONE.name()))
                .map(BloodCultureSampleRemarkDto::getRemark)
                .findFirst().orElse(StringUtils.EMPTY));

        param.put("applySampleItems", applySampleItemService.selectByApplySampleId(applySample.getApplySampleId()).stream().map(Dict::parse)
                .collect(Collectors.toList()));

        // 签名
        param.put("signature", Dict.of(
                // 检验者
                "tester", Dict.of("name", tester.getNickname(), "cnSign", tester.getCnSign(), "enSign", tester.getEnSign(), "sign",
                        StringUtils.defaultString(tester.getCnSign(), tester.getEnSign())),
                // 一次审核人
                "oneChecker",
                Dict.of("name", checker.getNickname(), "cnSign", checker.getCnSign(), "enSign", checker.getEnSign(), "sign",
                        StringUtils.defaultString(checker.getCnSign(), checker.getEnSign())),
                // 批准者
                "approver", Dict.of("name", group.getApproverName(), "sign", group.getApproverSign())));

        final String key = "blood-culture.one-audit-template-code";
        final String code = environment.getProperty(key);
        if (StringUtils.isBlank(code)) {
            throw new IllegalStateException(String.format("模版编码 [%s] 没有配置，无法审核", key));
        }

        log.info("开始使用报告单模板 [{}] 生成 条码 [{}] 的报告单。 参数 [{}]", code, sample.getBarcode(),
                JSON.toJSONString(param));


        final String pdfUrl = pdfReportService.build2Url(code, param);

        final SampleReportDto sr = new SampleReportDto();
        sr.setSampleReportId(snowflakeService.genId());
        sr.setApplySampleId(applySample.getApplySampleId());
        sr.setApplyId(applySample.getApplyId());
        sr.setSampleId(sample.getBloodCultureSampleId());
        sr.setBarcode(sample.getBarcode());
        sr.setFileType(SampleReportFileTypeEnum.PDF.name());
        sr.setUrl(pdfUrl);
        sr.setGroupName(sample.getGroupName());
        sr.setGroupId(sample.getGroupId());
        sr.setOrgId(sample.getOrgId());
        sr.setOrgName(sample.getOrgName());
        sr.setUpdateDate(new Date());
        sr.setCreateDate(new Date());
        sr.setHspOrgId(sample.getHspOrgId());
        sr.setHspOrgName(sample.getHspOrgName());

        // 添加初审报告单
        sampleReportService.addSampleReport(sr);

        context.put(OneAuditContext.REPORT, sr);

        return CONTINUE_PROCESSING;
    }
}
