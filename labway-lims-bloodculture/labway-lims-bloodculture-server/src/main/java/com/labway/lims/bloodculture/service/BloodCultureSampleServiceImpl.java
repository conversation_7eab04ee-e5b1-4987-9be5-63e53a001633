package com.labway.lims.bloodculture.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemBloodCultureService;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureTwoUnPickInfoDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleRemarkService;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleResultService;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.bloodculture.mapper.BloodCultureSampleMapper;
import com.labway.lims.bloodculture.mapstruct.BloodCultureSampleConverter;
import com.labway.lims.bloodculture.model.TbBloodCultureSample;
import com.labway.lims.bloodculture.service.chain.audit.one.OneAuditChain;
import com.labway.lims.bloodculture.service.chain.audit.one.OneAuditContext;
import com.labway.lims.bloodculture.service.chain.audit.two.TwoAuditChain;
import com.labway.lims.bloodculture.service.chain.audit.two.TwoAuditContext;
import com.labway.lims.bloodculture.service.chain.positive.PositiveChain;
import com.labway.lims.bloodculture.service.chain.positive.PositiveContext;
import com.labway.lims.routine.api.service.SampleReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class BloodCultureSampleServiceImpl implements BloodCultureSampleService {

    @Resource
    private BloodCultureSampleServiceImpl self;
    @Resource
    private BloodCultureSampleMapper bloodCultureSampleMapper;
    @Resource
    private BloodCultureSampleConverter bloodCultureSampleConverter;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private ApplySampleItemBloodCultureService applySampleItemBloodCultureService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @Resource
    private BloodCultureSampleRemarkService bloodCultureSampleRemarkService;
    @Resource
    private BloodCultureSampleResultService bloodCultureSampleResultService;
    @Resource
    private OneAuditChain oneAuditChain;
    @Resource
    private PositiveChain positiveChain;
    @Resource
    private TwoAuditChain twoAuditChain;

    @Override
    public long twoPick(long applySampleId, long instrumentGroupId, String sampleNo) {
        LoginUserHandler.User user = LoginUserHandler.get();
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("样本对应申请单不存在");
        }

        final InstrumentGroupDto instrumentGroup =
                instrumentGroupService.selectByInstrumentGroupId(instrumentGroupId);
        if (Objects.isNull(instrumentGroup)) {
            throw new IllegalStateException("专业小组不存在");
        }

        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(applySampleId)
                .stream().filter(e -> Objects.equals(e.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name()))
                .filter(obj -> Objects.equals(obj.getGroupId(), instrumentGroup.getGroupId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("该样本无检验项目");
        }

        // 血培养正常只有一条信息 若有多条 先暂时取第一条
        final ApplySampleItemDto sampleItem = applySampleItems.get(NumberUtils.INTEGER_ZERO);

        // 检验项目下的报告项目 code
        final List<String> reportItemCodes = reportItemService.selectByTestItemId(sampleItem.getTestItemId()).stream()
                .map(ReportItemDto::getReportItemCode).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(reportItemCodes)) {
            throw new IllegalStateException("没有可以分拣的报告项目");
        }

        // 机构下 这些报告项目 对应仪器报告项目
        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentGroupId(instrumentGroupId).stream()
                        .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                        .filter(e -> CollectionUtils.containsAny(reportItemCodes, e.getReportItemCode()))
                        .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(instrumentReportItems)) {
            throw new IllegalStateException(String.format("仪器没有绑定 [%s] 报告项目", String.join("、", reportItemCodes)));
        }

        // 含报告项目 最多的仪器
        final Long instrumentId = instrumentReportItems.stream().collect(Collectors.groupingBy(InstrumentReportItemDto::getInstrumentId))
                .entrySet().stream().max((a, b) -> NumberUtils.compare(a.getValue().size(), b.getValue().size()))
                .map(Map.Entry::getKey).orElse(NumberUtils.LONG_ZERO);

        final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentId);
        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("仪器不存在");
        }

        final BloodCultureSampleDto geneticsSample = new BloodCultureSampleDto();
        geneticsSample.setBloodCultureSampleId(snowflakeService.genId());
        geneticsSample.setApplySampleId(applySampleId);
        geneticsSample.setApplyId(applySample.getApplyId());
        geneticsSample.setBarcode(applySample.getBarcode());
        geneticsSample.setSampleNo(sampleNo);
        geneticsSample.setGroupName(applySample.getGroupName());
        geneticsSample.setGroupId(applySample.getGroupId());
        geneticsSample.setInstrumentGroupId(instrumentGroupId);
        geneticsSample.setInstrumentGroupName(instrumentGroup.getInstrumentGroupName());
        geneticsSample.setInstrumentCode(instrument.getInstrumentCode());
        geneticsSample.setInstrumentName(instrument.getInstrumentName());
        geneticsSample.setInstrumentId(instrumentId);
        geneticsSample.setTestDate(new Date());
        geneticsSample.setOneCheckerId(NumberUtils.LONG_ZERO);
        geneticsSample.setOneCheckerName(StringUtils.EMPTY);
        geneticsSample.setOneCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        geneticsSample.setTwoCheckerId(NumberUtils.LONG_ZERO);
        geneticsSample.setTwoCheckerName(StringUtils.EMPTY);
        geneticsSample.setTwoCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        geneticsSample.setOneCheckSampleReportId(NumberUtils.LONG_ZERO);
        geneticsSample.setTwoCheckSampleReportId(NumberUtils.LONG_ZERO);
        geneticsSample.setCreateDate(new Date());
        geneticsSample.setUpdateDate(new Date());
        geneticsSample.setUpdaterId(user.getUserId());
        geneticsSample.setUpdaterName(user.getNickname());
        geneticsSample.setCreatorId(user.getUserId());
        geneticsSample.setCreatorName(user.getNickname());
        geneticsSample.setHspOrgId(apply.getHspOrgId());
        geneticsSample.setHspOrgName(apply.getHspOrgName());
        geneticsSample.setOrgId(apply.getOrgId());
        geneticsSample.setOrgName(apply.getOrgName());
        geneticsSample.setIsDelete(YesOrNoEnum.NO.getCode());

        return self.addBloodCultureSample(geneticsSample);
    }

    @Override
    public long addBloodCultureSample(BloodCultureSampleDto sample) {
        sample.setBloodCultureSampleId(Optional.ofNullable(sample.getBloodCultureSampleId()).orElseGet(() -> snowflakeService.genId()));
        bloodCultureSampleMapper.insert(bloodCultureSampleConverter.convert(sample));
        return sample.getBloodCultureSampleId();
    }

    @Override
    public void deleteByBloodCultureSampleId(long bloodCultureSampleId) {
        bloodCultureSampleMapper.deleteById(bloodCultureSampleId);
    }

    @Override
    public void deleteByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds) {
        if (CollectionUtils.isEmpty(bloodCultureSampleIds)) {
            return;
        }
        bloodCultureSampleMapper.deleteBatchIds(bloodCultureSampleIds);
    }

    @Nullable
    @Override
    public BloodCultureSampleDto selectByBloodCultureSampleId(long bloodCultureSampleId) {
        return bloodCultureSampleConverter.convert(bloodCultureSampleMapper.selectById(bloodCultureSampleId));
    }

    @Override
    public List<BloodCultureSampleDto> selectByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }
        return bloodCultureSampleMapper.selectList(new LambdaQueryWrapper<TbBloodCultureSample>()
                        .in(TbBloodCultureSample::getApplySampleId, applySampleIds))
                .stream().map(bloodCultureSampleConverter::convert).collect(Collectors.toList());
    }

    @Override
    public BloodCultureSampleDto selectByApplySampleId(Long applySampleId) {
        return bloodCultureSampleConverter.convert(bloodCultureSampleMapper.selectOne(new LambdaQueryWrapper<TbBloodCultureSample>()
                .eq(TbBloodCultureSample::getApplySampleId, applySampleId)
                .last("limit 1")));
    }

    @Override
    public BloodCultureTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode())
                || Objects.equals(e.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new IllegalStateException("该样本已经审核，不能取消二次分拣");
        }

        final var samples = self.selectByApplySampleIds(
                applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));

        if (CollectionUtils.isEmpty(samples)) {
            throw new IllegalStateException("血培养样本不存在");
        }

        final Set<Long> ids = samples.stream().map(BloodCultureSampleDto::getBloodCultureSampleId)
                .collect(Collectors.toSet());

        // 删除血培养样本
        self.deleteByBloodCultureSampleIds(ids);

        // 删除样本报告项目
        sampleReportItemService.deleteBySampleIds(ids);

        // 删除结果
        bloodCultureSampleResultService.deleteByBloodCultureSampleIds(ids);
        // 删除备注
        bloodCultureSampleRemarkService.deleteByBloodCultureSampleIds(ids);

        // 清空生清单样本的结果备注，样本备注以及检验者
        final ApplySampleDto as = new ApplySampleDto();
        as.setSampleRemark(StringUtils.EMPTY);
        as.setResultRemark(StringUtils.EMPTY);
        as.setTesterId(NumberUtils.LONG_ZERO);
        as.setTesterName(StringUtils.EMPTY);
        as.setTwoPickDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        as.setTwoPickerId(NumberUtils.LONG_ZERO);
        as.setTwoPickerName(StringUtils.EMPTY);
        applySampleService.updateByApplySampleIds(as, applySampleIds);

        log.info("血培养检验取消二次分拣 样本ID [{}] 条码号 [{}] 操作人 [{}]", ids, applySamples.stream().map(ApplySampleDto::getBarcode)
                .collect(Collectors.toSet()), LoginUserHandler.get().getNickname());

        return new BloodCultureTwoUnPickInfoDto(samples.stream()
                .map(e -> new BloodCultureTwoUnPickInfoDto.Sample().setSampleId(e.getBloodCultureSampleId())
                        .setGroupId(e.getGroupId()).setSampleNo(e.getSampleNo())
                        .setTwoPickDate(applySamples.stream().filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                                .findFirst().map(ApplySampleDto::getTwoPickDate).orElseThrow())
                        .setInstrumentGroupId(e.getInstrumentGroupId()))
                .collect(Collectors.toList()));
    }

    @Override
    public List<BloodCultureSampleDto> selectByTestDate(Date testDateStart, Date testDateEnd, Long orgId) {
        return bloodCultureSampleMapper.selectList(new LambdaQueryWrapper<TbBloodCultureSample>()
                        .ge(TbBloodCultureSample::getCreateDate, testDateStart)
                        .le(TbBloodCultureSample::getCreateDate, testDateEnd)
                        .eq(TbBloodCultureSample::getOrgId, orgId)
                        .orderByAsc(TbBloodCultureSample::getTestDate))
                .stream().map(bloodCultureSampleConverter::convert)
                .collect(Collectors.toList());
    }

    @Override
    public void updateByBloodCultureSampleId(BloodCultureSampleDto sample) {
        bloodCultureSampleMapper.updateById(bloodCultureSampleConverter.convert(sample));
    }

    @Override
    public void oneCheck(long bloodCultureSampleId) {
        final OneAuditContext context = new OneAuditContext();
        context.setBloodCultureSampleId(bloodCultureSampleId);

        try {

            if (!oneAuditChain.execute(context)) {
                throw new IllegalStateException("初审失败");
            }

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("初审 [{}] 时\n{}", bloodCultureSampleId, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    public void twoCheck(long bloodCultureSampleId) {
        final TwoAuditContext context = new TwoAuditContext();
        context.setBloodCultureSampleId(bloodCultureSampleId);

        try {

            if (!twoAuditChain.execute(context)) {
                throw new IllegalStateException("初审失败");
            }

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("终审 [{}] 时\n{}", bloodCultureSampleId, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    public List<BloodCultureSampleDto> selectByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds) {
        final List<TbBloodCultureSample> samples = bloodCultureSampleMapper.selectBatchIds(bloodCultureSampleIds);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        final Map<Long, TbBloodCultureSample> map = samples.stream().collect(Collectors.toMap(TbBloodCultureSample::getBloodCultureSampleId,
                v -> v, (a, b) -> a));

        final List<BloodCultureSampleDto> list = new ArrayList<>(map.size());

        // 保持顺序
        for (Long e : bloodCultureSampleIds) {
            if (map.containsKey(e)) {
                list.add(bloodCultureSampleConverter.convert(map.get(e)));
            }
        }

        return list;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void positive(Long bloodCultureSampleId) {
        final PositiveContext context = new PositiveContext();
        context.setBloodCultureSampleId(bloodCultureSampleId);

        try {

            if (!positiveChain.execute(context)) {
                throw new IllegalStateException("标记阳性失败");
            }

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("标记阳性 [{}] 时\n{}", bloodCultureSampleId, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }


}
