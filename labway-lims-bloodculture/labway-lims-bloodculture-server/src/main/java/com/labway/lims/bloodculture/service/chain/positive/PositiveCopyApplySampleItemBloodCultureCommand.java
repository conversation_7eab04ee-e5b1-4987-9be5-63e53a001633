package com.labway.lims.bloodculture.service.chain.positive;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.service.ApplySampleItemBloodCultureService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 复制血培养项目
 */
@Slf4j
@Component
class PositiveCopyApplySampleItemBloodCultureCommand implements Command, Filter {
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private ApplySampleItemBloodCultureService applySampleItemBloodCultureService;

    @Override
    public boolean execute(Context c) throws Exception {
        final PositiveContext context = PositiveContext.from(c);

        // 复制一个血培养项目
        final ApplySampleItemBloodCultureDto applySampleItemBloodCulture = applySampleItemBloodCultureService
                .selectByApplySampleItemId(context.getApplySampleItem().getApplySampleItemId());
        if (Objects.isNull(applySampleItemBloodCulture)) {
            throw new IllegalArgumentException("当前样本不存在血培养项目");
        }

        context.setApplySampleItemBloodCulture(applySampleItemBloodCulture);

        final ApplySampleItemBloodCultureDto newApplySampleItemBloodCulture = new ApplySampleItemBloodCultureDto();
        BeanUtils.copyProperties(applySampleItemBloodCulture, newApplySampleItemBloodCulture);
        newApplySampleItemBloodCulture.setCreateDate(new Date());
        newApplySampleItemBloodCulture.setUpdateDate(new Date());
        newApplySampleItemBloodCulture.setCreatorId(LoginUserHandler.get().getUserId());
        newApplySampleItemBloodCulture.setCreatorName(LoginUserHandler.get().getNickname());
        newApplySampleItemBloodCulture.setUpdaterId(LoginUserHandler.get().getUserId());
        newApplySampleItemBloodCulture.setUpdaterName(LoginUserHandler.get().getNickname());
        newApplySampleItemBloodCulture.setApplySampleId(context.getNewApplySample().getApplySampleId());
        newApplySampleItemBloodCulture.setApplySampleItemId(context.getNewApplySampleItem().getApplySampleItemId());
        newApplySampleItemBloodCulture.setApplySampleItemBloodCultureId(snowflakeService.genId());

        context.setNewApplySampleItemBloodCulture(newApplySampleItemBloodCulture);

        applySampleItemBloodCultureService.addApplySampleItemBloodCultures(Collections.singletonList(newApplySampleItemBloodCulture));


        return CONTINUE_PROCESSING;
    }


    @Override
    public boolean postprocess(Context c, Exception exception) {
        if (Objects.nonNull(exception)) {
            Optional.ofNullable(PositiveContext.from(c).getNewApplySampleItemBloodCulture())
                    .map(ApplySampleItemBloodCultureDto::getApplySampleItemBloodCultureId)
                    .ifPresent(applySampleItemBloodCultureService::deleteByApplySampleItemBloodCultureId);
        }
        return CONTINUE_PROCESSING;
    }
}
