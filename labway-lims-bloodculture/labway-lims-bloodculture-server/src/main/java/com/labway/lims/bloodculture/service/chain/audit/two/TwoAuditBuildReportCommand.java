package com.labway.lims.bloodculture.service.chain.audit.two;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.CAPdf;
import com.labway.lims.api.config.HspOrgConfig;
import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleRemarkDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleResultDto;
import com.labway.lims.bloodculture.api.enums.BloodCultureSampleReportEnum;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleRemarkService;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleResultService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.dto.ca.CAPdfSignDto;
import com.labway.lims.pdfreport.api.enums.SealTypeEnum;
import com.labway.lims.pdfreport.api.service.CaPdfService;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.pdfreport.api.vo.ca.CAPdfSignVo;
import com.labway.lims.pdfreport.api.vo.ca.CASealVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 生成报告单
 */
@Slf4j
@Component
class TwoAuditBuildReportCommand implements Command {
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @Resource
    private BloodCultureSampleResultService bloodCultureSampleResultService;
    @Resource
    private BloodCultureSampleRemarkService bloodCultureSampleRemarkService;
    @DubboReference
    private PdfReportService pdfReportService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleReportService sampleReportService;
    @Resource
    private Environment environment;

    @Resource
    private HuaweiObsUtils huaweiObsUtils;

    @DubboReference
    private CaPdfService caPdfService;

    @Resource
    private HspOrgConfig hspOrgConfig;


    @Override
    public boolean execute(Context c) throws Exception {

        final TwoAuditContext context = TwoAuditContext.from(c);
        final BloodCultureSampleDto sample = context.getSample();
        final ApplyDto apply = context.getApply();

        sample.setOneCheckerId(LoginUserHandler.get().getUserId());
        sample.setOneCheckerName(LoginUserHandler.get().getNickname());
        sample.setOneCheckDate(new Date());

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        final ProfessionalGroupDto group = groupService.selectByGroupId(sample.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException(String.format("专业组 [%s] 不存在", sample.getGroupName()));
        }

        final UserDto checker = userService.selectByUserId(sample.getOneCheckerId());
        if (Objects.isNull(checker)) {
            throw new IllegalStateException(String.format("审核人 [%s] 不存在", sample.getOneCheckerName()));
        }

        final UserDto twoChecker = userService.selectByUserId(LoginUserHandler.get().getUserId());
        if (Objects.isNull(twoChecker)) {
            throw new IllegalStateException(String.format("审核人 [%s] 不存在", LoginUserHandler.get().getNickname()));
        }

        final UserDto tester = userService.selectByUserId(applySample.getTesterId());
        if (Objects.isNull(tester)) {
            throw new IllegalStateException(String.format("检验者 [%s] 不存在", applySample.getTesterName()));
        }

        final PdfReportParamDto param = new PdfReportParamDto();
        param.put("apply", Dict.of("masterBarcode", apply.getMasterBarcode(), "patientName", apply.getPatientName(),
                "patientAge", apply.getPatientAge(), "hspOrgName", apply.getHspOrgName(), "_apply",
                Dict.parse(apply)));

        applySample.setResultRemark(applySample.getResultRemark().replace("\n", "<br/>"));
        param.put("applySample",
                Dict.of("barcode", applySample.getBarcode(), "tubeName", applySample.getTubeName(), "sampleTypeName",
                        applySample.getSampleTypeName(), "groupName", applySample.getGroupName(), "onePickerName",
                        applySample.getOnePickerName(), "onePickDate", applySample.getOnePickDate(), "twoPickerName",
                        applySample.getTwoPickerName(), "twoPickDate", applySample.getTwoPickDate(), "_applySample",
                        Dict.parse(applySample)));

        param.put("sample",
                Dict.of("sampleNo", sample.getSampleNo(), "instrumentGroupName", sample.getInstrumentGroupName(),
                        "instrumentName", sample.getInstrumentName(), "testerName", applySample.getTesterName(),
                        "oneCheckerName", sample.getOneCheckerName(), "sampleRemark", applySample.getSampleRemark(),
                        "resultRemark", applySample.getResultRemark(), "_sample", Dict.parse(sample)));

        final List<BloodCultureSampleResultDto> results = bloodCultureSampleResultService.selectByBloodCultureSampleId(sample.getBloodCultureSampleId());
        final List<BloodCultureSampleRemarkDto> remarks = bloodCultureSampleRemarkService.selectByBloodCultureSampleId(sample.getBloodCultureSampleId());

        param.put("oneResults", results.stream().filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.ONE.name())).map(Dict::parse)
                .collect(Collectors.toList()));

        param.put("twoResults", results.stream().filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.TWO.name())).map(Dict::parse)
                .collect(Collectors.toList()));

        param.put("oneRemark", remarks.stream().filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.ONE.name()))
                .map(BloodCultureSampleRemarkDto::getRemark)
                .findFirst().orElse(StringUtils.EMPTY));

        param.put("twoRemark", remarks.stream().filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.TWO.name()))
                .map(BloodCultureSampleRemarkDto::getRemark)
                .findFirst().orElse(StringUtils.EMPTY));

        param.put("applySampleItems", applySampleItemService.selectByApplySampleId(applySample.getApplySampleId()).stream().map(Dict::parse)
                .collect(Collectors.toList()));

        // 签名
        param.put("signature", Dict.of(
                // 检验者
                "tester", Dict.of("name", tester.getNickname(), "cnSign", tester.getCnSign(), "enSign", tester.getEnSign(), "sign",
                        StringUtils.defaultString(tester.getCnSign(), tester.getEnSign())),
                // 一次审核人
                "oneChecker",
                Dict.of("name", checker.getNickname(), "cnSign", checker.getCnSign(), "enSign", checker.getEnSign(), "sign",
                        StringUtils.defaultString(checker.getCnSign(), checker.getEnSign())),
                // 二次审核人
                "twoChecker",
                Dict.of("name", twoChecker.getNickname(), "cnSign", twoChecker.getCnSign(), "enSign", twoChecker.getEnSign(), "sign",
                        StringUtils.defaultString(twoChecker.getCnSign(), twoChecker.getEnSign())),
                // 批准者
                "approver", Dict.of("name", group.getApproverName(), "sign", group.getApproverSign())));

        final String key = "blood-culture.two-audit-template-code";
        final String code = environment.getProperty(key);
        if (StringUtils.isBlank(code)) {
            throw new IllegalStateException(String.format("模版编码 [%s] 没有配置，无法审核", key));
        }

        log.info("开始使用报告单模板 [{}] 生成 条码 [{}] 的报告单。 参数 [{}]", code, sample.getBarcode(),
                JSON.toJSONString(param));

//        final String pdfUrl = pdfReportService.build2Url(code, param);

        File tempFile = FileUtil.createTempFile();
        try (final FileOutputStream fos = new FileOutputStream(tempFile)) {
            String pdfCodeBak = code;
            String pdfCode = code;
            boolean contains = hspOrgConfig.getHspOrgCodes().contains(apply.getHspOrgCode());
            if(contains){
                pdfCode = "CA_" + pdfCode;
            }
            byte[] bytes;
            try {
                bytes = pdfReportService.build(pdfCode, param);
            }catch (Exception exception){
                if(!contains){
                    throw exception;
                }
                log.error("ca模板生成失败， 生成原始模板");
                contains = false;
                bytes = pdfReportService.build(pdfCodeBak, param);
            }
            if(contains){
                try {
                    byte[] CABytes = createCAPDF(bytes, applySample.getTesterName(), twoChecker.getNickname());
                    if (CABytes.length > 0) {
                        bytes = CABytes;
                    }
                } catch (Exception exception) {
                    bytes = pdfReportService.build(pdfCodeBak, param);
                    log.error("ca模板生成失败， 生成原始模板" + exception);
                }
            }

            fos.write(bytes);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        String pdfUrl;
        try(FileInputStream fileInputStream = new FileInputStream(tempFile)){
            pdfUrl = huaweiObsUtils.upload(fileInputStream, MediaType.APPLICATION_PDF_VALUE);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        final SampleReportDto sr = new SampleReportDto();
        sr.setSampleReportId(snowflakeService.genId());
        sr.setApplySampleId(applySample.getApplySampleId());
        sr.setApplyId(applySample.getApplyId());
        sr.setSampleId(sample.getBloodCultureSampleId());
        sr.setBarcode(sample.getBarcode());
        sr.setFileType(SampleReportFileTypeEnum.PDF.name());
        sr.setUrl(pdfUrl);
        sr.setGroupName(sample.getGroupName());
        sr.setGroupId(sample.getGroupId());
        sr.setOrgId(sample.getOrgId());
        sr.setOrgName(sample.getOrgName());
        sr.setUpdateDate(new Date());
        sr.setCreateDate(new Date());
        sr.setHspOrgId(sample.getHspOrgId());
        sr.setHspOrgName(sample.getHspOrgName());

        // 添加终审报告单
        sampleReportService.addSampleReport(sr);

        context.put(TwoAuditContext.REPORT, sr);

        return CONTINUE_PROCESSING;
    }



    /**
     * 打印capdf
     * @param bytes  文件
     * @param testName 检验者
     * @param auditName 审核者
     * @return
     * @throws IOException
     */
    private byte[] createCAPDF(byte[] bytes, String testName, String auditName) throws IOException {
        List<CAPdfSignDto.Strategy> strategies = new ArrayList<>();

        //  所有章
        List<CASealVo> caSealVos = caPdfService.selectSeal(null);
        if (CollectionUtils.isEmpty(caSealVos)) {
            return new byte[0];
        }

        // 检验者章
        CAPdfSignDto.Strategy strategy = this.getStrategy(caSealVos, hspOrgConfig.getTest(), testName);
        if(Objects.nonNull(strategy)) {
            strategies.add(strategy);
        }

        //  审核者章
        CAPdfSignDto.Strategy strategy1 = this.getStrategy(caSealVos, hspOrgConfig.getAudit(), auditName);
        if(Objects.nonNull(strategy1)) {
            strategies.add(strategy1);
        }

        // 公章
        CAPdfSignDto.Strategy strategy2 = this.getStrategy(caSealVos, hspOrgConfig.getOffice(), "-1");
        if(Objects.nonNull(strategy2)) {
            strategies.add(strategy2);
        }

        CAPdfSignDto caPdfSignDto = new CAPdfSignDto();
        caPdfSignDto.setFile(bytes);
        caPdfSignDto.setSignedStrategy(strategies);

        CAPdfSignVo caPdfSignVo = caPdfService.pdfQuiesceSign(caPdfSignDto);

        return caPdfService.download(caPdfSignVo.getEnvelopeId(), null);
    }


    /**
     * 获取签章配置
     * @param caSealVos 签章列表
     * @param caPdf 签署位置配置
     * @param sealName 签署人
     * @return
     */
    private CAPdfSignDto.Strategy getStrategy(List<CASealVo> caSealVos, CAPdf caPdf, String sealName) {
        if (Objects.equals(sealName, "-1")) {
            sealName = caPdf.getSealName();
        }
        String finalSealName = sealName;
        CASealVo caSealVo = caSealVos.stream().filter(e -> e.getSealName().startsWith(finalSealName)).findFirst().orElse(null);
        if (Objects.nonNull(caSealVo)) {
            CAPdfSignDto.Strategy strategy = new CAPdfSignDto.Strategy();
            strategy.setSealId(caSealVo.getId());
            strategy.setStragegyType(1);
            strategy.setKeywords(caPdf.getKeyword());
            strategy.setPages("all");
            if (Objects.equals(caSealVo.getSealType(), String.valueOf(SealTypeEnum.PERSION_SEAL.getCode()))) {
                strategy.setReduction(caPdf.getReduction());
            }
            strategy.setOffsetDirectionX(caPdf.getX());
            strategy.setOffsetDirectionY(caPdf.getY());
            strategy.setIndex(0);
            return strategy;
        }
        return null;
    }
}
