package com.labway.lims.bloodculture.service.chain.positive;

import com.labway.lims.apply.api.dto.PositiveMicrobiologyTwoPickDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 二次分拣
 */
@Slf4j
@Component
class PositiveTwoPickCommand implements Command, Filter {
    @DubboReference
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final PositiveContext context = PositiveContext.from(c);

        final PositiveMicrobiologyTwoPickDto tp = new PositiveMicrobiologyTwoPickDto();
        tp.setApplySampleId(context.getNewApplySample().getApplySampleId());
        tp.setSampleNo(context.getBloodCultureSample().getSampleNo());
        tp.setTwoPickDate(context.getApplySample().getTwoPickDate());
        applySampleService.twoPick(tp);

        return CONTINUE_PROCESSING;
    }


    @Override
    public boolean postprocess(Context c, Exception exception) {

        return CONTINUE_PROCESSING;
    }
}
