package com.labway.lims.bloodculture.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleRemarkDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleRemarkService;
import com.labway.lims.bloodculture.mapper.BloodCultureSampleRemarkMapper;
import com.labway.lims.bloodculture.mapstruct.BloodCultureSampleRemarkConverter;
import com.labway.lims.bloodculture.model.TbBloodCultureSampleRemark;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "blood-culture-sample-remark:")
public class BloodCultureSampleRemarkServiceImpl implements BloodCultureSampleRemarkService {
    @Resource
    private BloodCultureSampleRemarkMapper bloodCultureSampleRemarkMapper;
    @Resource
    private BloodCultureSampleRemarkConverter bloodCultureSampleRemarkConverter;
    @Resource
    private BloodCultureSampleRemarkServiceImpl self;

    @Override
    public void deleteByBloodCultureSampleId(long bloodCultureSampleId) {
        self.deleteByBloodCultureSampleIds(List.of(bloodCultureSampleId));
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByBloodCultureSampleId(long bloodCultureSampleId, String position) {
        bloodCultureSampleRemarkMapper.delete(new LambdaQueryWrapper<TbBloodCultureSampleRemark>()
                .eq(TbBloodCultureSampleRemark::getBloodCultureSampleId, bloodCultureSampleId)
                .eq(TbBloodCultureSampleRemark::getPosition, position));
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds) {
        if (CollectionUtils.isEmpty(bloodCultureSampleIds)) {
            return;
        }
        bloodCultureSampleRemarkMapper.delete(new LambdaQueryWrapper<TbBloodCultureSampleRemark>()
                .in(TbBloodCultureSampleRemark::getBloodCultureSampleId, bloodCultureSampleIds));
    }

    @Override
    @Cacheable(key = "'selectByBloodCultureSampleId:'+#bloodCultureSampleId")
    public List<BloodCultureSampleRemarkDto> selectByBloodCultureSampleId(Long bloodCultureSampleId) {
        if (Objects.isNull(bloodCultureSampleId)) {
            return Collections.emptyList();
        }
        return bloodCultureSampleRemarkMapper.selectList(new LambdaQueryWrapper<TbBloodCultureSampleRemark>()
                        .eq(TbBloodCultureSampleRemark::getBloodCultureSampleId, bloodCultureSampleId)
                        .orderByAsc(TbBloodCultureSampleRemark::getBloodCultureSampleRemarkId))
                .stream().map(bloodCultureSampleRemarkConverter::convert).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public void updateByBloodCultureSampleRemarkId(BloodCultureSampleRemarkDto remark) {
        bloodCultureSampleRemarkMapper.updateById(bloodCultureSampleRemarkConverter.convert(remark));
    }

    @Override
    @CacheEvict(allEntries = true)
    public long addByBloodCultureSampleRemark(BloodCultureSampleRemarkDto remark) {
        bloodCultureSampleRemarkMapper.insert(bloodCultureSampleRemarkConverter.convert(remark));
        return remark.getBloodCultureSampleRemarkId();
    }
}
