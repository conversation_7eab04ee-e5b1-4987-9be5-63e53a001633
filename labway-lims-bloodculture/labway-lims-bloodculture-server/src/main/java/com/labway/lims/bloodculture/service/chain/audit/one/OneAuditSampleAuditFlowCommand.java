package com.labway.lims.bloodculture.service.chain.audit.one;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
public class OneAuditSampleAuditFlowCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OneAuditContext context = OneAuditContext.from(c);
        final BloodCultureSampleDto sample = context.getSample();
        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(sample.getApplyId());
        sampleFlow.setApplySampleId(sample.getApplySampleId());
        sampleFlow.setBarcode(sample.getBarcode());
        sampleFlow.setOperator(LoginUserHandler.get().getNickname());
        sampleFlow.setOperatorId(LoginUserHandler.get().getUserId());
        sampleFlow.setOperateCode(BarcodeFlowEnum.ONE_CHECK.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.ONE_CHECK.getDesc());
        sampleFlow.setContent("初审");
        sampleFlowService.addSampleFlow(sampleFlow);
        return CONTINUE_PROCESSING;
    }

}
