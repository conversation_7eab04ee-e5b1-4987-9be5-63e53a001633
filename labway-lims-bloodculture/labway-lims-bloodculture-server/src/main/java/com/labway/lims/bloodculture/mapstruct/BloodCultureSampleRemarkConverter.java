package com.labway.lims.bloodculture.mapstruct;

import com.labway.lims.bloodculture.api.dto.BloodCultureSampleRemarkDto;
import com.labway.lims.bloodculture.model.TbBloodCultureSampleRemark;
import org.mapstruct.Mapper;


@Mapper(componentModel = "spring")
public interface BloodCultureSampleRemarkConverter {

    TbBloodCultureSampleRemark convert(BloodCultureSampleRemarkDto sample);

    BloodCultureSampleRemarkDto convert(TbBloodCultureSampleRemark sample);
}
