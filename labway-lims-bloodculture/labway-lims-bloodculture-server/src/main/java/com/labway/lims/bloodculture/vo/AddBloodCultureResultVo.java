package com.labway.lims.bloodculture.vo;

import com.labway.lims.bloodculture.api.enums.BloodCultureSampleReportEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AddBloodCultureResultVo {

    /**
     * 结果
     */
    private String result;

    /**
     * 结果编码
     */
    private String resultCode;

    /**
     * 位置
     *
     * @see BloodCultureSampleReportEnum#name()
     */
    private String position;

    /**
     * 样本id
     */
    private Long bloodCultureSampleId;
}
