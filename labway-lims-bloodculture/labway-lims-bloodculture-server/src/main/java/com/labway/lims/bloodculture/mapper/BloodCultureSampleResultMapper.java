package com.labway.lims.bloodculture.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleResultDto;
import com.labway.lims.bloodculture.model.TbBloodCultureSampleResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 血培养样本
 * </p>
 */
@Mapper
public interface BloodCultureSampleResultMapper extends BaseMapper<TbBloodCultureSampleResult> {

    /**
     * 批量新增
     */
    int inserts(@Param("results") Collection<BloodCultureSampleResultDto> results);
}
