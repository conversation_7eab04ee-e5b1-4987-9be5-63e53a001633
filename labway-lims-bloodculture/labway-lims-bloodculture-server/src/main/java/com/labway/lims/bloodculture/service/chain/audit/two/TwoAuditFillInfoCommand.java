package com.labway.lims.bloodculture.service.chain.audit.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 填充信息
 */
@Slf4j
@Component
class TwoAuditFillInfoCommand implements Command {
    @Resource
    private BloodCultureSampleService bloodCultureSampleService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final TwoAuditContext context = TwoAuditContext.from(c);

        final BloodCultureSampleDto sample = bloodCultureSampleService.selectByBloodCultureSampleId(context
                .getBloodCultureSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        if (Objects.equals(sample.getOneCheckerId(), NumberUtils.LONG_ZERO)) {
            throw new IllegalArgumentException("请先初审");
        }

        if (!Objects.equals(sample.getTwoCheckerId(), NumberUtils.LONG_ZERO)) {
            throw new IllegalArgumentException("样本已经终审");
        }

        if (applySampleService.isDisabled(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", sample.getBarcode()));
        }

        if (applySampleService.isTerminate(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", sample.getBarcode()));
        }

        final ApplyDto apply = applyService.selectByApplyId(sample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (Objects.equals(applySample.getTesterId(), LoginUserHandler.get().getUserId())) {
            throw new IllegalStateException("检验者与审核者不能为同一用户");
        }

        context.put(TwoAuditContext.SAMPLE, sample);
        context.put(TwoAuditContext.APPLY, apply);

        return CONTINUE_PROCESSING;
    }
}
