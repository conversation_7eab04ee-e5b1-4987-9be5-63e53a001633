package com.labway.lims.bloodculture.mapstruct;

import com.labway.lims.bloodculture.api.dto.BloodCultureSampleResultDto;
import com.labway.lims.bloodculture.model.TbBloodCultureSampleResult;
import org.mapstruct.Mapper;


@Mapper(componentModel = "spring")
public interface BloodCultureSampleResultConverter {

    TbBloodCultureSampleResult convert(BloodCultureSampleResultDto sample);

    BloodCultureSampleResultDto convert(TbBloodCultureSampleResult sample);
}
