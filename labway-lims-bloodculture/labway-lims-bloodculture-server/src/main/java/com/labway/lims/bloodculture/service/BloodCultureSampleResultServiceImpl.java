package com.labway.lims.bloodculture.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleResultDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleResultService;
import com.labway.lims.bloodculture.mapper.BloodCultureSampleResultMapper;
import com.labway.lims.bloodculture.mapstruct.BloodCultureSampleResultConverter;
import com.labway.lims.bloodculture.model.TbBloodCultureSampleResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "blood-culture-sample-result:")
public class BloodCultureSampleResultServiceImpl implements BloodCultureSampleResultService {
    @Resource
    private BloodCultureSampleResultMapper bloodCultureSampleResultMapper;
    @Resource
    private BloodCultureSampleResultConverter bloodCultureSampleResultConverter;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private BloodCultureSampleResultServiceImpl self;

    @Override
    public void deleteByBloodCultureSampleId(long bloodCultureSampleId) {
        self.deleteByBloodCultureSampleIds(List.of(bloodCultureSampleId));
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByBloodCultureSampleId(long bloodCultureSampleId, String position) {
        bloodCultureSampleResultMapper.delete(new LambdaQueryWrapper<TbBloodCultureSampleResult>()
                .eq(TbBloodCultureSampleResult::getBloodCultureSampleId, bloodCultureSampleId)
                .eq(TbBloodCultureSampleResult::getPosition, position));
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds) {
        if (CollectionUtils.isEmpty(bloodCultureSampleIds)) {
            return;
        }
        bloodCultureSampleResultMapper.delete(new LambdaQueryWrapper<TbBloodCultureSampleResult>()
                .in(TbBloodCultureSampleResult::getBloodCultureSampleId, bloodCultureSampleIds));
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds, String position) {
        if (CollectionUtils.isEmpty(bloodCultureSampleIds) || StringUtils.isEmpty(position)) {
            return;
        }
        bloodCultureSampleResultMapper.delete(new LambdaQueryWrapper<TbBloodCultureSampleResult>()
                .eq(TbBloodCultureSampleResult::getPosition, position)
                .in(TbBloodCultureSampleResult::getBloodCultureSampleId, bloodCultureSampleIds));
    }

    @Override
    @Cacheable(key = "'selectByBloodCultureSampleId:'+#bloodCultureSampleId")
    public List<BloodCultureSampleResultDto> selectByBloodCultureSampleId(Long bloodCultureSampleId) {
        if (Objects.isNull(bloodCultureSampleId)) {
            return Collections.emptyList();
        }
        return bloodCultureSampleResultMapper.selectList(new LambdaQueryWrapper<TbBloodCultureSampleResult>()
                        .eq(TbBloodCultureSampleResult::getBloodCultureSampleId, bloodCultureSampleId)
                        .orderByAsc(TbBloodCultureSampleResult::getBloodCultureSampleResultId))
                .stream().map(bloodCultureSampleResultConverter::convert).collect(Collectors.toList());
    }

    @Override
    public List<BloodCultureSampleResultDto> selectByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds) {
        if (CollectionUtils.isEmpty(bloodCultureSampleIds)) {
            return Collections.emptyList();
        }
        return bloodCultureSampleResultMapper.selectList(new LambdaQueryWrapper<TbBloodCultureSampleResult>()
                        .in(TbBloodCultureSampleResult::getBloodCultureSampleId, bloodCultureSampleIds)
                        .orderByAsc(TbBloodCultureSampleResult::getBloodCultureSampleResultId))
                .stream().map(bloodCultureSampleResultConverter::convert).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<BloodCultureSampleResultDto>> selectByBloodCultureSampleIdsAsMap(Collection<Long> bloodCultureSampleIds) {
        return selectByBloodCultureSampleIds(bloodCultureSampleIds).stream()
                .collect(Collectors.groupingBy(BloodCultureSampleResultDto::getBloodCultureSampleId));
    }

    @Override
    @Cacheable(key = "'selectByBloodCultureSampleResultId:'+#bloodCultureSampleResultId")
    public BloodCultureSampleResultDto selectByBloodCultureSampleResultId(Long bloodCultureSampleResultId) {
        return bloodCultureSampleResultConverter
                .convert(bloodCultureSampleResultMapper.selectById(bloodCultureSampleResultId));
    }

    @Override
    @CacheEvict(allEntries = true)
    public long addBloodCultureSampleResult(BloodCultureSampleResultDto result) {
        result.setBloodCultureSampleResultId(Optional.ofNullable(result.getBloodCultureSampleResultId())
                .orElseGet(() -> snowflakeService.genId()));
        bloodCultureSampleResultMapper.insert(bloodCultureSampleResultConverter.convert(result));

        return result.getBloodCultureSampleResultId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public void addBloodCultureSampleResults(Collection<BloodCultureSampleResultDto> results) {
        bloodCultureSampleResultMapper.inserts(results);
    }

    @Override
    @CacheEvict(allEntries = true)
    public void deleteByBloodCultureSampleResultId(Long bloodCultureSampleResultId) {
        bloodCultureSampleResultMapper.deleteById(bloodCultureSampleResultId);
    }
}
