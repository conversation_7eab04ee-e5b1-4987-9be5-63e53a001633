package com.labway.lims.bloodculture.service.chain.positive;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 复制申请单样本项目
 */
@Slf4j
@Component
class PositiveUpdateApplySampleCommand implements Command, Filter {

    @DubboReference
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {

        final PositiveContext context = PositiveContext.from(c);
        final ApplySampleDto applySample = new ApplySampleDto();
        applySample.setApplySampleId(context.getApplySample().getApplySampleId());
        applySample.setStatus(SampleStatusEnum.BC_POSITIVE.getCode());
        applySample.setUpdateDate(new Date());
        applySample.setUpdaterId(LoginUserHandler.get().getUserId());
        applySample.setUpdaterName(LoginUserHandler.get().getNickname());
        applySampleService.updateByApplySampleId(applySample);

        return CONTINUE_PROCESSING;
    }


    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
