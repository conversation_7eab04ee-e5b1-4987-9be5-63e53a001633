package com.labway.lims.bloodculture.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.BloodCultureSampleEsQuery;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.BloodCultureInspectionDto;
import com.labway.lims.apply.api.dto.es.CombineOnePersonDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleRemarkDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleResultDto;
import com.labway.lims.bloodculture.api.enums.BloodCultureSampleReportEnum;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleRemarkService;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleResultService;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.bloodculture.vo.BloodCultureSampleVo;
import com.labway.lims.bloodculture.vo.ExportSampleEsVo;
import com.labway.lims.bloodculture.vo.MultiVo;
import com.labway.lims.bloodculture.vo.QueryResultBloodCultureSamplesVo;
import com.labway.lims.bloodculture.vo.QuerySampleVo;
import com.labway.lims.bloodculture.vo.SampleEsVo;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/blood-culture-sample")
public class BloodCultureSampleController extends BaseController {
    @Resource
    private BloodCultureSampleService bloodCultureSampleService;
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    private BloodCultureSampleRemarkService bloodCultureSampleRemarkService;
    @Resource
    private BloodCultureSampleResultService bloodCultureSampleResultService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;
	@DubboReference
	private PdfReportService pdfReportService;

    @PostMapping("/samples")
    public Object samples(@RequestBody QuerySampleVo vo) throws Exception {
        // 查询样本
        final List<BloodCultureSampleDto> samples = bloodCultureSampleService.selectByTestDate(vo.getTestDateStart(),
                vo.getTestDateEnd(), LoginUserHandler.get().getOrgId());
        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }

        // 申请单样本
        final Map<Long, ApplySampleDto> applySamples = applySampleService.selectByApplySampleIdsAsMap(samples.stream()
                .map(BloodCultureSampleDto::getApplySampleId).collect(Collectors.toSet()));
        if (MapUtils.isEmpty(applySamples)) {
            return List.of();
        }

        // 申请单
        final Map<Long, ApplyDto> applies = applyService.selectByApplyIdsAsMap(samples.stream()
                .map(BloodCultureSampleDto::getApplyId).collect(Collectors.toSet()));
        if (MapUtils.isEmpty(applies)) {
            return List.of();
        }

        // 项目信息
        final Map<Long, List<ApplySampleItemDto>> applySampleItems = applySampleItemService.selectByApplySampleIdsAsMap(applySamples.keySet());

        final List<BloodCultureSampleVo> list = new ArrayList<>();
        for (BloodCultureSampleDto e : samples) {
            final ApplySampleDto k = applySamples.get(e.getApplySampleId());
            if (Objects.isNull(k)) {
                continue;
            }
            final ApplyDto t = applies.get(k.getApplyId());
            if (Objects.isNull(t)) {
                continue;
            }
            final BloodCultureSampleVo v = new BloodCultureSampleVo();
            v.setBloodCultureSampleId(e.getBloodCultureSampleId());
            v.setApplySampleId(e.getApplySampleId());
            v.setApplyId(e.getApplyId());
            v.setStatus(k.getStatus());
            v.setBarcode(k.getBarcode());
            v.setSampleNo(e.getSampleNo());
            v.setGroupId(e.getGroupId());
            v.setGroupName(e.getGroupName());
            v.setInstrumentGroupId(e.getInstrumentGroupId());
            v.setInstrumentGroupName(e.getInstrumentGroupName());
            v.setInstrumentName(e.getInstrumentName());
            v.setInstrumentId(e.getInstrumentId());
            v.setTestDate(e.getTestDate());
            v.setOneCheckerName(e.getOneCheckerName());
            v.setOneCheckerId(e.getOneCheckerId());
            v.setOneCheckDate(e.getOneCheckDate());
            v.setTwoCheckerName(e.getTwoCheckerName());
            v.setTwoCheckerId(e.getTwoCheckerId());
            v.setTwoCheckDate(e.getTwoCheckDate());
            v.setSampleRemark(k.getSampleRemark());
            v.setResultRemark(k.getResultRemark());
            v.setHspOrgId(e.getHspOrgId());
            v.setHspOrgName(e.getHspOrgName());
            v.setUrgent(k.getUrgent());
            v.setTestItemNames(applySampleItems.getOrDefault(e.getApplySampleId(),
                            List.of()).stream().map(ApplySampleItemDto::getTestItemName)
                    .collect(Collectors.toList()));
            v.setPatientName(t.getPatientName());
            v.setIsPrint(k.getIsPrint());
            v.setApplyType(t.getApplyTypeName());
            v.setOriginalOrgCode(t.getOriginalOrgCode());
            v.setOriginalOrgName(t.getOriginalOrgName());
            v.setCreateDate(e.getCreateDate());
            // 免疫二次分拣标记 # 1.1.3.7
            v.setIsImmunityTwoPick(k.getIsImmunityTwoPick());
            list.add(v);
        }
        final SampleStatusEnum sampleStatus = EnumUtils.getEnum(SampleStatusEnum.class, vo.getSampleStatus());

        // 状态过滤
        if (Objects.nonNull(sampleStatus)) {
            list.removeIf(e -> !Objects.equals(e.getStatus(), sampleStatus.getCode()));
        }

        // 送检机构过滤
        if (Objects.nonNull(vo.getHspOrgId())) {
            list.removeIf(e -> !Objects.equals(e.getHspOrgId(), vo.getHspOrgId()));
        }

        // 检验项目过滤
        if (StringUtils.isNotBlank(vo.getTestItemCode())) {
            list.removeIf(e -> applySampleItems.getOrDefault(e.getApplySampleId(), List.of())
                    .stream().noneMatch(k -> Objects.equals(k.getTestItemCode(), vo.getTestItemCode())));
        }

        // 只展示没有结果的
        if (BooleanUtils.isTrue(vo.getOnlyNoResult())) {
            final Map<Long, List<BloodCultureSampleResultDto>> map = bloodCultureSampleResultService.selectByBloodCultureSampleIdsAsMap(list.stream()
                    .map(BloodCultureSampleVo::getBloodCultureSampleId).collect(Collectors.toSet()));
            // 未审，那么删除已经有结果的
            list.removeIf(e -> {
                if (Objects.equals(sampleStatus, SampleStatusEnum.NOT_AUDIT)) {
                    return map.getOrDefault(e.getBloodCultureSampleId(), List.of()).stream().filter(k -> StringUtils.isNotBlank(k.getResult()))
                            .anyMatch(k -> Objects.equals(k.getPosition(), BloodCultureSampleReportEnum.ONE.name()));
                } else if (Objects.equals(sampleStatus, SampleStatusEnum.ONE_AUDIT)) {
                    return map.getOrDefault(e.getBloodCultureSampleId(), List.of()).stream().filter(k -> StringUtils.isNotBlank(k.getResult()))
                            .anyMatch(k -> Objects.equals(k.getPosition(), BloodCultureSampleReportEnum.TWO.name()));
                }
                return false;
            });
        }
        // 如果是终止检验直接过滤掉
        list.removeIf(e ->Objects.equals(SampleStatusEnum.STOP_TEST.getCode(),e.getStatus()));
        return list;
    }

    @PostMapping("/multi")
    public Object multi(@RequestBody MultiVo vo) throws Exception {
        final BloodCultureSampleReportEnum position = EnumUtils.getEnum(BloodCultureSampleReportEnum.class, vo.getPosition());
        if (Objects.isNull(position) || CollectionUtils.isEmpty(vo.getBloodCultureSampleIds())
                || CollectionUtils.isEmpty(vo.getResults())) {
            throw new IllegalArgumentException("参数错误");
        }

        vo.setBloodCultureSampleIds(vo.getBloodCultureSampleIds().stream()
                .distinct().collect(Collectors.toList()));

        final List<BloodCultureSampleDto> samples = bloodCultureSampleService.selectByBloodCultureSampleIds(vo.getBloodCultureSampleIds());
        if (CollectionUtils.isEmpty(samples)) {
            throw new IllegalArgumentException("样本不存在");
        }

        for (BloodCultureSampleDto sample : samples) {
            if (Objects.equals(position, BloodCultureSampleReportEnum.ONE)) {
                if (!Objects.equals(sample.getOneCheckerId(), NumberUtils.LONG_ZERO)) {
                    throw new IllegalArgumentException(String.format("样本 [%s] 已经初审，无法添加结果",
                            sample.getBarcode()));
                }
            } else if (Objects.equals(position, BloodCultureSampleReportEnum.TWO)) {
                if (!Objects.equals(sample.getTwoCheckerId(), NumberUtils.LONG_ZERO)) {
                    throw new IllegalArgumentException(String.format("样本 [%s] 已经终审，无法添加结果",
                            sample.getBarcode()));
                }
            }
        }

        // 批量生成id
        final LinkedList<Long> ids = snowflakeService.genIds(vo.getBloodCultureSampleIds().size()
                * vo.getResults().size() * 2);

        // 删除之前的结果
        bloodCultureSampleResultService.deleteByBloodCultureSampleIds(vo.getBloodCultureSampleIds(), vo.getPosition());

        // 添加结果
        bloodCultureSampleResultService.addBloodCultureSampleResults(samples.stream().map(e -> vo.getResults().stream().map(t -> {
            final BloodCultureSampleResultDto k = new BloodCultureSampleResultDto();
            k.setBloodCultureSampleResultId(ids.pop());
            k.setBloodCultureSampleId(e.getBloodCultureSampleId());
            k.setApplySampleId(e.getApplySampleId());
            k.setApplyId(e.getApplyId());
            k.setResult(t.getResult());
            k.setResultCode(t.getResultCode());
            k.setPosition(vo.getPosition());
            k.setCreateDate(new Date());
            k.setUpdateDate(new Date());
            k.setUpdaterId(LoginUserHandler.get().getUserId());
            k.setUpdaterName(LoginUserHandler.get().getNickname());
            k.setCreatorId(LoginUserHandler.get().getUserId());
            k.setCreatorName(LoginUserHandler.get().getNickname());
            k.setOrgId(LoginUserHandler.get().getOrgId());
            k.setOrgName(LoginUserHandler.get().getOrgName());
            k.setIsDelete(YesOrNoEnum.NO.getCode());
            return k;
        }).collect(Collectors.toList())).flatMap(Collection::stream).collect(Collectors.toList()));

        // 添加条码环节
        sampleFlowService.addSampleFlows(samples.stream().map(e -> SampleFlowDto.builder()
                        .sampleFlowId(ids.pop())
                        .applyId(e.getApplyId())
                        .applySampleId(e.getApplySampleId())
                        .barcode(e.getBarcode())
                        .operateCode(BarcodeFlowEnum.BC_ADD_SAMPLE_RESULT.name())
                        .operateName(BarcodeFlowEnum.BC_ADD_SAMPLE_RESULT.getDesc())
                        .barcode(e.getBarcode())
                        .content(String.format("批量录入 [%s] 检验结果 [%s]", Objects.equals(position,
                                        BloodCultureSampleReportEnum.ONE) ? "初步" : "最终",
                                vo.getResults().stream().map(MultiVo.Result::getResult).collect(Collectors.joining("\n")))).build())
                .collect(Collectors.toList()));

        return Map.of();
    }

    /**
     * 标记阳性
     */
    @PostMapping("/positive")
    public Object positive(Long bloodCultureSampleId) throws Exception {


        bloodCultureSampleService.positive(bloodCultureSampleId);

        return Map.of();
    }


    @GetMapping("/sample-details")
    public Object sampleDetails(Long bloodCultureSampleId) throws Exception {
        if (Objects.isNull(bloodCultureSampleId)) {
            throw new IllegalArgumentException("参数错误");
        }


        final BloodCultureSampleDto sample = bloodCultureSampleService.selectByBloodCultureSampleId(bloodCultureSampleId);
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        // 查询备注
        final List<BloodCultureSampleRemarkDto> remarks = bloodCultureSampleRemarkService.selectByBloodCultureSampleId(bloodCultureSampleId);

        // 查询结果
        final List<BloodCultureSampleResultDto> results = bloodCultureSampleResultService.selectByBloodCultureSampleId(bloodCultureSampleId);

        return Map.of("one", Map.of(
                "remarks", remarks.stream()
                        .filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.ONE.name()))
                        .map(BloodCultureSampleRemarkDto::getRemark).findFirst().orElse(StringUtils.EMPTY),
                "results", results.stream()
                        .filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.ONE.name()))
                        .collect(Collectors.toList())
        ), "two", Map.of(
                "remarks", remarks.stream()
                        .filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.TWO.name()))
                        .map(BloodCultureSampleRemarkDto::getRemark).findFirst().orElse(StringUtils.EMPTY),
                "results", results.stream()
                        .filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.TWO.name()))
                        .collect(Collectors.toList())
        ));
    }

    @PostMapping("/preview-report")
    public Object report(@RequestBody List<Long> bloodCultureSampleIds, String position) {
        final List<BloodCultureSampleDto> samples = bloodCultureSampleService.selectByBloodCultureSampleIds(bloodCultureSampleIds);
        final List<Long> reportIds = new LinkedList<>();

        if (Objects.equals(position, BloodCultureSampleReportEnum.ONE.name())) {
            samples.removeIf(e -> Objects.equals(e.getOneCheckSampleReportId(), NumberUtils.LONG_ZERO));
            reportIds.addAll(samples.stream().map(BloodCultureSampleDto::getOneCheckSampleReportId).collect(Collectors.toList()));
        } else if (Objects.equals(position, BloodCultureSampleReportEnum.TWO.name())) {
            samples.removeIf(e -> Objects.equals(e.getTwoCheckSampleReportId(), NumberUtils.LONG_ZERO));
            reportIds.addAll(samples.stream().map(BloodCultureSampleDto::getTwoCheckSampleReportId).collect(Collectors.toList()));
        }

        if (CollectionUtils.isEmpty(reportIds)) {
            return List.of();
        }

        final LinkedList<Long> ids = snowflakeService.genIds(samples.size());

        sampleFlowService.addSampleFlows(samples.stream().map(e -> SampleFlowDto.builder()
                .applyId(e.getApplyId())
                .sampleFlowId(ids.pop())
                .applySampleId(e.getApplySampleId())
                .operateCode(BarcodeFlowEnum.PREVIEW_REPORT.name())
                .operateName(BarcodeFlowEnum.PREVIEW_REPORT.getDesc())
                .operatorId(LoginUserHandler.get().getUserId())
                .operator(LoginUserHandler.get().getNickname())
                .barcode(e.getBarcode())
                .content(String.format("预览 [%s] 报告单", Objects.equals(position, BloodCultureSampleReportEnum.ONE.name()) ? "初审" : "终审"))
                .build()).collect(Collectors.toList()));

        return sampleReportService.selectBySampleReportIds(reportIds)
                .stream().map(SampleReportDto::getUrl).collect(Collectors.toList());

    }

    /**
     * 查看 血培养结果
     */
    @PostMapping("/samples-es")
    public Object samplesEs(@RequestBody QueryResultBloodCultureSamplesVo vo) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        final BloodCultureSampleEsQuery query = new BloodCultureSampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(NumberUtils.INTEGER_ONE);
        query.setGroupIds(Collections.singleton(user.getGroupId()));
        query.setOrgId(user.getOrgId());
        query.setItemTypes(Collections.singleton(ItemTypeEnum.BLOOD_CULTURE.name()));

        // 检验日期
        if (Objects.nonNull(vo.getTestDateStart()) && Objects.nonNull(vo.getTestDateEnd())) {
            query.setStartTestDate(vo.getTestDateStart());
            query.setEndTestDate(vo.getTestDateEnd());
        }

        // 审核日期
        if (Objects.nonNull(vo.getCheckDateStart()) && Objects.nonNull(vo.getCheckDateEnd())) {
            query.setStartFinalCheckDate(vo.getCheckDateStart());
            query.setEndFinalCheckDate(vo.getCheckDateEnd());

            query.setStartOneCheckDate(vo.getCheckDateStart());
            query.setEndOneCheckDate(vo.getCheckDateEnd());
        }

        // 检验者ID
        if (Objects.nonNull(vo.getTesterId())) {
            query.setTesterId(vo.getTesterId());
        }

        // 审核人ID
        if (Objects.nonNull(vo.getCheckerId())) {
            query.setFinalCheckerIds(Collections.singleton(vo.getCheckerId()));
        }

        // 检验项目
        if (Objects.nonNull(vo.getTestItemId())) {
            query.setTestItemIds(Collections.singleton(vo.getTestItemId()));
        }

        // 送检机构
        if (Objects.nonNull(vo.getHspOrgId())) {
            query.setHspOrgIds(Collections.singleton(vo.getHspOrgId()));
        }

        // 姓名
        if (StringUtils.isNotBlank(vo.getPatientName())) {
            query.setPatientName(vo.getPatientName());
        }

        // 性别
        if (Objects.nonNull(vo.getPatientSex())
                && !Objects.equals(vo.getPatientSex(), SexEnum.DEFAULT.getCode())) {
            query.setPatientSex(vo.getPatientSex());
        }

        // 门诊/住院号
        if (StringUtils.isNotBlank(vo.getPatientVisitCard())) {
            query.setPatientVisitCard(vo.getPatientVisitCard());
        }

        if (StringUtils.isNotBlank(vo.getApplyType())) {
            query.setApplyTypes(Collections.singleton(vo.getApplyType()));
        }

        if (StringUtils.isNotBlank(vo.getBarcode())) {
            query.setBarcodes(Collections.singleton(vo.getBarcode()));
        }

        if (StringUtils.isNotBlank(vo.getSampleStatus())) {
            final SampleStatusEnum status = EnumUtils.getEnum(SampleStatusEnum.class, vo.getSampleStatus());
            if (Objects.nonNull(status)) {
                query.setSampleStatus(Collections.singleton(status.getCode()));
            }
        } else {
            query.setSampleStatus(new HashSet<>(Set.of(SampleStatusEnum.ONE_AUDIT.getCode(),
                    SampleStatusEnum.AUDIT.getCode(), SampleStatusEnum.BC_POSITIVE.getCode())));
        }

        query.setSorts(Lists.newArrayList(SampleEsQuery.Sort.builder()
                .filedName("finalCheckDate").order("ASC").build()));

        final List<BloodCultureInspectionDto> samples = elasticSearchSampleService.selectSamples(query)
                .stream().filter(BloodCultureInspectionDto.class::isInstance)
                .map(e -> (BloodCultureInspectionDto) e).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }

        // 结果过滤
        if (CollectionUtils.isNotEmpty(vo.getResultCodes())) {
            samples.removeIf(e -> {
                if (e.getOneResults().stream().anyMatch(k -> vo.getResultCodes().contains(k.getResultCode()))) {
                    return false;
                }
                if (e.getTwoResults().stream().anyMatch(k -> vo.getResultCodes().contains(k.getResultCode()))) {
                    return false;
                }
                return true;
            });
        }

        final List<SampleEsVo> list = new ArrayList<>();
        for (BloodCultureInspectionDto e : samples) {
            e.setTestItems(ObjectUtils.defaultIfNull(e.getTestItems(), List.of()));
            final SampleEsVo v = new SampleEsVo();
            BeanUtils.copyProperties(e, v);
            v.setBloodCultureSampleId(e.getSampleId());
            v.setStatus(e.getSampleStatus());

            if (!Objects.equals(e.getOneCheckDate(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
                v.setCheckDate(e.getOneCheckDate());
            }

            if (!Objects.equals(e.getTwoCheckDate(), DefaultDateEnum.DEFAULT_DATE.getDate())) {
                v.setCheckDate(e.getTwoCheckDate());
            }

            v.setSendDoctorName(e.getSendDoctorName());
            v.setApplyDate(e.getCreateDate());
            v.setFinalCheckerName(e.getFinalCheckerName());
            v.setSampleTypeName(e.getSampleTypeName());
            v.setCheckerId(e.getFinalCheckerId());
            v.setCheckerName(e.getFinalCheckerName());
            v.setEnterDate(e.getCreateDate());
            v.setApplyType(e.getApplyTypeName());
            v.setTestItemNames(e.getTestItems().stream().map(BaseSampleEsModelDto.TestItem::getTestItemName)
                    .collect(Collectors.toList()));
            list.add(v);
        }

        return list;

    }

    /**
     * 一审
     */
    @PostMapping("/one-check")
    public Object oneCheck(Long bloodCultureSampleId) throws Exception {

        bloodCultureSampleService.oneCheck(bloodCultureSampleId);

        return Map.of();
    }


    /**
     * 二审
     */
    @PostMapping("/two-check")
    public Object twoCheck(Long bloodCultureSampleId) throws Exception {

        bloodCultureSampleService.twoCheck(bloodCultureSampleId);

        return Map.of();
    }

    /**
     * 取消一审
     */
    @PostMapping("/cancel-one-check")
    public Object cancelOneCheck(Long bloodCultureSampleId) throws Exception {

        final BloodCultureSampleDto sample = bloodCultureSampleService.selectByBloodCultureSampleId(bloodCultureSampleId);
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        if (!Objects.equals(sample.getTwoCheckerId(), NumberUtils.LONG_ZERO)) {
            throw new IllegalArgumentException("请先取消终审");
        }

        if (Objects.equals(sample.getOneCheckerId(), NumberUtils.LONG_ZERO)) {
            return Map.of();
        }

        // 修改样本
        final BloodCultureSampleDto e = new BloodCultureSampleDto();
        e.setBloodCultureSampleId(bloodCultureSampleId);
        e.setOneCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        e.setOneCheckerId(NumberUtils.LONG_ZERO);
        e.setOneCheckerName(StringUtils.EMPTY);
        e.setOneCheckSampleReportId(NumberUtils.LONG_ZERO);
        e.setUpdateDate(new Date());
        e.setUpdaterId(LoginUserHandler.get().getUserId());
        e.setUpdaterName(LoginUserHandler.get().getNickname());
        bloodCultureSampleService.updateByBloodCultureSampleId(e);


        // 修改申请单样本
        final ApplySampleDto k = new ApplySampleDto();
        k.setApplySampleId(sample.getApplySampleId());
        k.setUpdateDate(new Date());
        k.setUpdaterId(0L);
        k.setUpdaterName("");
        k.setStatus(SampleStatusEnum.NOT_AUDIT.getCode());
        applySampleService.updateByApplySampleId(k);

        // 删除报告单
        sampleReportService.deleteBySampleReportId(sample.getOneCheckSampleReportId());

        // 条码环节
        sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                .applyId(sample.getApplyId())
                .applySampleId(sample.getApplySampleId())
                .barcode(sample.getBarcode())
                .operator(LoginUserHandler.get().getNickname())
                .operatorId(LoginUserHandler.get().getUserId())
                .operateCode(BarcodeFlowEnum.CANCEL_ONE_CHECK.name())
                .operateName(BarcodeFlowEnum.CANCEL_ONE_CHECK.getDesc())
                .content("取消初审").build());

        return Map.of();
    }


    /**
     * 取消二审
     */
    @PostMapping("/cancel-two-check")
    public Object cancelTwoCheck(Long bloodCultureSampleId) throws Exception {


        final BloodCultureSampleDto sample = bloodCultureSampleService.selectByBloodCultureSampleId(bloodCultureSampleId);
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        if (Objects.equals(sample.getTwoCheckerId(), NumberUtils.LONG_ZERO)) {
            return Map.of();
        }

        // 修改样本
        final BloodCultureSampleDto e = new BloodCultureSampleDto();
        e.setBloodCultureSampleId(bloodCultureSampleId);
        e.setTwoCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        e.setTwoCheckerId(NumberUtils.LONG_ZERO);
        e.setTwoCheckerName(StringUtils.EMPTY);
        e.setTwoCheckSampleReportId(NumberUtils.LONG_ZERO);
        e.setUpdateDate(new Date());
        e.setUpdaterId(LoginUserHandler.get().getUserId());
        e.setUpdaterName(LoginUserHandler.get().getNickname());
        bloodCultureSampleService.updateByBloodCultureSampleId(e);


        // 修改申请单样本
        final ApplySampleDto k = new ApplySampleDto();
        k.setApplySampleId(sample.getApplySampleId());
        k.setUpdateDate(new Date());
        k.setUpdaterId(LoginUserHandler.get().getUserId());
        k.setUpdaterName(LoginUserHandler.get().getNickname());
        k.setStatus(SampleStatusEnum.ONE_AUDIT.getCode());
        applySampleService.updateByApplySampleId(k);

        // 删除报告单
        sampleReportService.deleteBySampleReportId(sample.getTwoCheckSampleReportId());

        // 条码环节
        sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                .applyId(sample.getApplyId())
                .applySampleId(sample.getApplySampleId())
                .barcode(sample.getBarcode())
                .operator(LoginUserHandler.get().getNickname())
                .operatorId(LoginUserHandler.get().getUserId())
                .operateCode(BarcodeFlowEnum.CANCEL_TWO_CHECK.name())
                .operateName(BarcodeFlowEnum.CANCEL_TWO_CHECK.getDesc())
                .content("取消终审").build());

        return Map.of();
    }


    /**
     * 文件导出
     */
    @PostMapping("/export/samples-es")
    public void exportSamplesEs(@RequestBody QueryResultBloodCultureSamplesVo vo, HttpServletResponse response) throws Exception {

        List<ExportSampleEsVo> sampleEsVos = JSON.parseArray(JSON.toJSONString(this.samplesEs(vo)), ExportSampleEsVo.class);

        Map<Long, ApplyDto> applyDtoMap = applyService.selectByApplyIdsAsMap(sampleEsVos.stream().map(SampleEsVo::getApplyId).collect(Collectors.toList()));

        Map<Long, List<BloodCultureSampleResultDto>> sampleIdsAsMap = bloodCultureSampleResultService.selectByBloodCultureSampleIdsAsMap(
                sampleEsVos.stream().map(SampleEsVo::getBloodCultureSampleId).collect(Collectors.toList()));

        for (int i = 0; i < sampleEsVos.size(); i++) {
            ExportSampleEsVo sampleEsVo = sampleEsVos.get(i);
            sampleEsVo.setNum(i + 1);

            List<BloodCultureSampleResultDto> results = sampleIdsAsMap.get(sampleEsVo.getBloodCultureSampleId());
            ApplyDto applyDto = applyDtoMap.get(sampleEsVo.getApplyId());
            if (CollectionUtils.isEmpty(results)) {
                continue;
            }
            if (Objects.nonNull(applyDto)) {
                sampleEsVo.setDept(applyDto.getDept());
            }
            String firstResult = CollUtil.join(results.stream()
                    .filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.ONE.name()))
                    .map(BloodCultureSampleResultDto::getResult)
                    .collect(Collectors.toList()), ",");
            sampleEsVo.setFirstResult(firstResult);
            String finalResult = CollUtil.join(results.stream()
                    .filter(e -> Objects.equals(e.getPosition(), BloodCultureSampleReportEnum.TWO.name()))
                    .map(BloodCultureSampleResultDto::getResult)
                    .collect(Collectors.toList()), ",");
            sampleEsVo.setFinalResult(finalResult);
        }

        try (InputStream template = ResourceUtil.getStream("classpath:template/血培养检验结果导出.xlsx")) {
            ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream())
                    .withTemplate(template)
                    .excelType(ExcelTypeEnum.XLSX)
                    .build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();

            WriteSheet sheet1 = EasyExcelFactory.writerSheet(0).build();
            // 要遍历的表格数据
            excelWriter.fill(sampleEsVos, fillConfig, sheet1);
            excelWriter.finish();
        }
    }

	/**
	 * 血培养结果，同人报告合并
	 * <p> <a href="https://www.tapd.cn/59091617/prong/stories/view/1159091617001001966?from_iteration_id=1159091617001000256">【血培养检验结果查询】1、下载报告，需要合并下载；</a> </p>
	 * @param bloodCultureSampleIds 血培养样本id
	 * @return {@link HashMap map} key -> mergeReportFileName, value -> mergeReportFileUrl
	 */
	@PostMapping("/merge-report-same-person")
	public Object mergeReportSamePerson(@RequestBody Set<Long> bloodCultureSampleIds) {

		// 通过血培养样本id在es中查询
		LoginUserHandler.User user = LoginUserHandler.get();
		final BloodCultureSampleEsQuery query = new BloodCultureSampleEsQuery();
		query.setPageSize(Integer.MAX_VALUE);
		query.setPageNo(NumberUtils.INTEGER_ONE);
		query.setGroupIds(Collections.singleton(user.getGroupId()));
		query.setOrgId(user.getOrgId());
		query.setItemTypes(Collections.singleton(ItemTypeEnum.BLOOD_CULTURE.name()));
		query.setSampleIds(bloodCultureSampleIds);


		final List<BaseSampleEsModelDto> baseSamples = elasticSearchSampleService.selectSamples(query);
		if (CollectionUtils.isEmpty(baseSamples)) {
			return Map.of();
		}

		Collection<List<BloodCultureInspectionDto>> combineOnePersonLists = CombineOnePersonDto.groupCombineOnePerson(baseSamples, BloodCultureInspectionDto.class);
		Map<String, List<String>> reportMap = new HashMap<>();
		for (List<BloodCultureInspectionDto> combineOnePersonList : combineOnePersonLists) {
			for (BloodCultureInspectionDto combineOnePersonDto : combineOnePersonList) {
				String key = combineOnePersonDto.getGroupRowId() + combineOnePersonDto.getPatientName() + "-";
				Map<Long, String> reportUrlMap = combineOnePersonDto.getReports().stream().collect(Collectors.toMap(BaseSampleEsModelDto.Report::getSampleReportId, BaseSampleEsModelDto.Report::getUrl, (v1, v2) -> v1));
				String url = "";
				if (SampleStatusEnum.ONE_AUDIT.getCode() == combineOnePersonDto.getSampleStatus()) {
					// 初审
					url = reportUrlMap.get(combineOnePersonDto.getOneCheckSampleReportId());
					key = key + "血培养初审";
				} else if (SampleStatusEnum.AUDIT.getCode() == combineOnePersonDto.getSampleStatus()) {
					// 终审
					url = reportUrlMap.get(combineOnePersonDto.getTwoCheckSampleReportId());
					key = key + "血培养终审";
				}
				if (StringUtils.isNotBlank(url)) {
					reportMap.computeIfAbsent(key, value -> new ArrayList<>()).add(url);
				}
			}
		}
		List<Dict> dicts = Lists.newArrayList();
		// 报告合并
		reportMap.forEach((k, v) ->{
			String mergedPdfUrl = pdfReportService.mergePdfByUrls2Url(v);
			String fileName = k.substring(24) + String.format("(共%s份).pdf", v.size());
			dicts.add(Dict.create().set("fileName", fileName).set("fileUrl", mergedPdfUrl));
		});
		return dicts;
	}


}
