package com.labway.lims.bloodculture.service.chain.positive;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedList;
import java.util.Objects;
import java.util.Optional;

/**
 * 复制临时试管架
 */
@Slf4j
@Component
class PositiveNewTempRackCommand implements Command, Filter {
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private RackLogicService rackLogicService;
    @DubboReference
    private RackLogicSpaceService rackLogicSpaceService;

    @Override
    public boolean execute(Context c) throws Exception {
        final PositiveContext context = PositiveContext.from(c);

        final LinkedList<Long> ids = snowflakeService.genIds(2);
        final RackLogicDto rackLogic = new RackLogicDto();
        rackLogic.setRackLogicId(ids.pop());
        rackLogic.setRackId(NumberUtils.LONG_ONE);
        rackLogic.setRackCode("BC_POSITIVE");
        rackLogic.setRow(10);
        rackLogic.setColumn(10);
        rackLogic.setPosition(RackLogicPositionEnum.TWO_PICKING.getCode());
        rackLogic.setCurrentGroupId(LoginUserHandler.get().getGroupId());
        rackLogic.setCurrentGroupName(LoginUserHandler.get().getGroupName());
        rackLogic.setNextGroupId(LoginUserHandler.get().getGroupId());
        rackLogic.setNextGroupName(LoginUserHandler.get().getGroupName());
        rackLogic.setLastHandover(LoginUserHandler.get().getNickname());
        rackLogic.setCreateDate(new Date());
        rackLogic.setUpdateDate(new Date());
        rackLogic.setCreatorId(LoginUserHandler.get().getUserId());
        rackLogic.setCreatorName(LoginUserHandler.get().getNickname());
        rackLogic.setUpdaterId(LoginUserHandler.get().getUserId());
        rackLogic.setUpdaterName(LoginUserHandler.get().getNickname());
        rackLogic.setIsDelete(YesOrNoEnum.NO.getCode());
        rackLogicService.addRackLogic(rackLogic);

        final RackLogicSpaceDto rackLogicSpace = new RackLogicSpaceDto();
        rackLogicSpace.setRackLogicSpaceId(ids.pop());
        rackLogicSpace.setRackLogicId(rackLogic.getRackLogicId());
        rackLogicSpace.setRackId(rackLogic.getRackId());
        rackLogicSpace.setRow(0);
        rackLogicSpace.setColumn(0);
        rackLogicSpace.setApplySampleId(context.getNewApplySample().getApplySampleId());
        rackLogicSpace.setStatus(1);
        rackLogicSpace.setCreateDate(new Date());
        rackLogicSpace.setUpdateDate(new Date());
        rackLogicSpace.setCreatorId(LoginUserHandler.get().getUserId());
        rackLogicSpace.setCreatorName(LoginUserHandler.get().getNickname());
        rackLogicSpace.setUpdaterId(LoginUserHandler.get().getUserId());
        rackLogicSpace.setUpdaterName(LoginUserHandler.get().getNickname());
        rackLogicSpace.setIsDelete(YesOrNoEnum.NO.getCode());
        rackLogicSpaceService.addRackLogicSpace(rackLogicSpace);

        return CONTINUE_PROCESSING;
    }


    @Override
    public boolean postprocess(Context c, Exception exception) {
        if (Objects.nonNull(exception)) {
            Optional.ofNullable(PositiveContext.from(c).getNewRackLogic())
                    .map(RackLogicDto::getRackLogicId)
                    .ifPresent((e) -> {
                        rackLogicService.deleteByRackLogicId(e);
                        rackLogicSpaceService.deleteByRackLogicId(e);
                    });
        }
        return CONTINUE_PROCESSING;
    }
}
