package com.labway.lims.bloodculture.service.chain.positive;

import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 填充信息
 */
@Slf4j
@Component
class PositiveFillInfoCommand implements Command {
    @Resource
    private BloodCultureSampleService bloodCultureSampleService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final PositiveContext context = PositiveContext.from(c);

        final BloodCultureSampleDto sample = bloodCultureSampleService.selectByBloodCultureSampleId(context
                .getBloodCultureSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        if (!Objects.equals(sample.getTwoCheckerId(), NumberUtils.LONG_ZERO)) {
            throw new IllegalArgumentException(String.format("样本 [%s] 已经终审，无法标记阳性",
                    sample.getBarcode()));
        }

        context.setBloodCultureSample(sample);

        return CONTINUE_PROCESSING;
    }
}
