package com.labway.lims.bloodculture.service.chain.positive;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 复制申请单样本
 */
@Slf4j
@Component
class PositiveCopyApplySampleCommand implements Command, Filter {
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        final PositiveContext context = PositiveContext.from(c);
        final BloodCultureSampleDto sample = context.getBloodCultureSample();

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample
                .getApplySampleId());

        if (Objects.isNull(applySample)) {
            throw new IllegalArgumentException(String.format("样本 [%s] 申请单样本不存在",
                    sample.getBarcode()));
        }

        if (Objects.equals(applySample.getStatus(), SampleStatusEnum.BC_POSITIVE.getCode())) {
            throw new IllegalArgumentException(String.format("样本 [%s] 已经标记阳性",
                    sample.getBarcode()));
        }

        context.setApplySample(applySample);

        // 复制一个申请单样本
        final ApplySampleDto newApplySample = new ApplySampleDto();
        BeanUtils.copyProperties(applySample, newApplySample);
        newApplySample.setIsTwoPick(YesOrNoEnum.NO.getCode());
        newApplySample.setTwoPickerName(StringUtils.EMPTY);
        newApplySample.setTwoPickDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        newApplySample.setTwoPickerId(NumberUtils.LONG_ONE);
        newApplySample.setCreateDate(new Date());
        newApplySample.setUpdateDate(new Date());
        newApplySample.setItemType(ItemTypeEnum.MICROBIOLOGY.name());
        newApplySample.setCreatorId(LoginUserHandler.get().getUserId());
        newApplySample.setCreatorName(LoginUserHandler.get().getNickname());
        newApplySample.setUpdaterId(LoginUserHandler.get().getUserId());
        newApplySample.setUpdaterName(LoginUserHandler.get().getNickname());
        newApplySample.setApplySampleId(snowflakeService.genId());

        context.setNewApplySample(newApplySample);

        applySampleService.addApplySample(newApplySample);


        return CONTINUE_PROCESSING;
    }


    @Override
    public boolean postprocess(Context c, Exception exception) {
        if (Objects.nonNull(exception)) {
            Optional.ofNullable(PositiveContext.from(c).getNewApplySample())
                    .map(ApplySampleDto::getApplySampleId)
                    .ifPresent(applySampleService::deleteByApplySampleId);
        }
        return CONTINUE_PROCESSING;
    }
}
