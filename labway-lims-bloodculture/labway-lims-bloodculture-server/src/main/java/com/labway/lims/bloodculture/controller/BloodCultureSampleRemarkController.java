package com.labway.lims.bloodculture.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleRemarkDto;
import com.labway.lims.bloodculture.api.enums.BloodCultureSampleReportEnum;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleRemarkService;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.bloodculture.vo.AddBloodCultureRemarkVo;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/blood-culture-remark")
public class BloodCultureSampleRemarkController extends BaseController {
    @Resource
    private BloodCultureSampleService bloodCultureSampleService;
    @Resource
    private BloodCultureSampleRemarkService bloodCultureSampleRemarkService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @PostMapping("/update")
    public Object update(@RequestBody AddBloodCultureRemarkVo vo) throws Exception {
        final BloodCultureSampleDto sample = bloodCultureSampleService.selectByBloodCultureSampleId(vo.getBloodCultureSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        if (!Objects.equals(sample.getOneCheckerId(), NumberUtils.LONG_ZERO) &&
                Objects.equals(vo.getPosition(), BloodCultureSampleReportEnum.ONE.name())) {
            throw new IllegalArgumentException("样本已经初审，无法修改备注");
        } else if (!Objects.equals(sample.getTwoCheckerId(), NumberUtils.LONG_ZERO) &&
                Objects.equals(vo.getPosition(), BloodCultureSampleReportEnum.TWO.name())) {
            throw new IllegalArgumentException("样本已经终审，无法修改备注");
        }


        final BloodCultureSampleRemarkDto oldRemark = bloodCultureSampleRemarkService.selectByBloodCultureSampleId(vo.getBloodCultureSampleId())
                .stream().filter(e -> Objects.equals(e.getPosition(), vo.getPosition())).findFirst().orElse(null);

        if (Objects.nonNull(oldRemark) && Objects.equals(oldRemark.getRemark(), vo.getRemark())) {
            return Map.of("id", oldRemark.getBloodCultureSampleRemarkId());
        }

        final String key = redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":" + vo.getBloodCultureSampleId();
        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, Duration.ofMillis(1)))) {
            throw new IllegalArgumentException("正在修改中");
        }

        final BloodCultureSampleRemarkDto e = new BloodCultureSampleRemarkDto();
        e.setRemark(vo.getRemark());


        try {

            if (Objects.isNull(oldRemark)) {
                e.setBloodCultureSampleRemarkId(snowflakeService.genId());
                e.setBloodCultureSampleId(sample.getBloodCultureSampleId());
                e.setApplySampleId(sample.getApplySampleId());
                e.setApplyId(sample.getApplyId());
                e.setPosition(vo.getPosition());
                e.setCreateDate(new Date());
                e.setUpdateDate(new Date());
                e.setUpdaterId(LoginUserHandler.get().getUserId());
                e.setUpdaterName(LoginUserHandler.get().getNickname());
                e.setCreatorId(LoginUserHandler.get().getUserId());
                e.setCreatorName(LoginUserHandler.get().getNickname());
                e.setOrgId(LoginUserHandler.get().getOrgId());
                e.setOrgName(LoginUserHandler.get().getOrgName());
                e.setIsDelete(YesOrNoEnum.NO.getCode());
            } else {
                e.setBloodCultureSampleRemarkId(oldRemark.getBloodCultureSampleRemarkId());
                e.setUpdateDate(new Date());
                e.setUpdaterId(LoginUserHandler.get().getUserId());
                e.setUpdaterName(LoginUserHandler.get().getNickname());
            }

            if (Objects.isNull(oldRemark)) {
                bloodCultureSampleRemarkService.addByBloodCultureSampleRemark(e);
            } else {
                bloodCultureSampleRemarkService.updateByBloodCultureSampleRemarkId(e);
            }

            // 添加流水
            if (Objects.isNull(oldRemark)) {
                sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                        .applyId(sample.getApplyId())
                        .applySampleId(sample.getApplySampleId())
                        .barcode(sample.getBarcode())
                        .operateCode(BarcodeFlowEnum.BC_ADD_SAMPLE_REMARK.name())
                        .operateName(BarcodeFlowEnum.BC_ADD_SAMPLE_REMARK.getDesc())
                        .barcode(sample.getBarcode())
                        .content(String.format("添加 [%s] 结果备注 [%s]", Objects.equals(vo.getPosition(), BloodCultureSampleReportEnum.ONE.name()) ? "初步" : "最终",
                                vo.getRemark())).build());

            } else {
                sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                        .applyId(sample.getApplyId())
                        .applySampleId(sample.getApplySampleId())
                        .barcode(sample.getBarcode())
                        .operateCode(BarcodeFlowEnum.BC_EDIT_SAMPLE_REMARK.name())
                        .operateName(BarcodeFlowEnum.BC_EDIT_SAMPLE_REMARK.getDesc())
                        .barcode(sample.getBarcode())
                        .content(String.format("修改 [%s] 结果备注 从 [%s] 修改到 [%s]", Objects.equals(vo.getPosition(), BloodCultureSampleReportEnum.ONE.name()) ? "初步" : "最终",
                                oldRemark.getRemark(), vo.getRemark())).build());

            }

        } finally {
            stringRedisTemplate.delete(key);
        }

        return Map.of("id", e.getBloodCultureSampleRemarkId());
    }


}
