package com.labway.lims.bloodculture.service.chain.audit.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 生成报告单
 */
@Slf4j
@Component
class TwoAuditUpdateSampleCommand implements Command {
    @Resource
    private BloodCultureSampleService bloodCultureSampleService;
    @DubboReference
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final TwoAuditContext context = TwoAuditContext.from(c);

        // 修改样本
        final BloodCultureSampleDto sample = new BloodCultureSampleDto();
        sample.setBloodCultureSampleId(context.getBloodCultureSampleId());
        sample.setTwoCheckerId(LoginUserHandler.get().getUserId());
        sample.setTwoCheckerName(LoginUserHandler.get().getNickname());
        sample.setTwoCheckDate(new Date());
        sample.setTwoCheckSampleReportId(context.getSampleReport().getSampleReportId());
        sample.setUpdateDate(new Date());
        sample.setUpdaterId(LoginUserHandler.get().getUserId());
        sample.setUpdaterName(LoginUserHandler.get().getNickname());
        bloodCultureSampleService.updateByBloodCultureSampleId(sample);

        // 修改申请单样本
        final ApplySampleDto applySample = new ApplySampleDto();
        applySample.setApplySampleId(context.getSample().getApplySampleId());
        applySample.setUpdateDate(new Date());
        applySample.setUpdaterId(sample.getUpdaterId());
        applySample.setUpdaterName(sample.getUpdaterName());
        applySample.setStatus(SampleStatusEnum.AUDIT.getCode());
        // 审核修改打印信息
        applySample.setPrinterId(0L);
        applySample.setPrinterName(Strings.EMPTY);
        applySample.setPrintDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        applySample.setIsPrint(YesOrNoEnum.NO.getCode());
        applySampleService.updateByApplySampleId(applySample);

        return CONTINUE_PROCESSING;
    }
}
