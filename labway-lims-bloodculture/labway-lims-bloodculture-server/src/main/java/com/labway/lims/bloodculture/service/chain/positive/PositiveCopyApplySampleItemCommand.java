package com.labway.lims.bloodculture.service.chain.positive;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 复制申请单样本项目
 */
@Slf4j
@Component
class PositiveCopyApplySampleItemCommand implements Command, Filter {
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final PositiveContext context = PositiveContext.from(c);
        final BloodCultureSampleDto sample = context.getBloodCultureSample();

        // 复制一个申请单样本项目
        final ApplySampleItemDto applySampleItem = applySampleItemService.selectByApplySampleId(sample.getApplySampleId())
                .stream().filter(e -> Objects.equals(e.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name()))
                .findFirst().orElse(null);
        if (Objects.isNull(applySampleItem)) {
            throw new IllegalArgumentException("当前样本不存在血培养项目");
        }
        context.setApplySampleItem(applySampleItem);

        final ApplySampleItemDto newApplySampleItem = new ApplySampleItemDto();
        BeanUtils.copyProperties(applySampleItem, newApplySampleItem);
        newApplySampleItem.setItemType(ItemTypeEnum.MICROBIOLOGY.name());
        newApplySampleItem.setCreateDate(new Date());
        newApplySampleItem.setUpdateDate(new Date());
        newApplySampleItem.setCreatorId(LoginUserHandler.get().getUserId());
        newApplySampleItem.setCreatorName(LoginUserHandler.get().getNickname());
        newApplySampleItem.setUpdaterId(LoginUserHandler.get().getUserId());
        newApplySampleItem.setUpdaterName(LoginUserHandler.get().getNickname());
        newApplySampleItem.setApplySampleId(context.getNewApplySample().getApplySampleId());
        newApplySampleItem.setApplySampleItemId(snowflakeService.genId());
        newApplySampleItem.setCount(NumberUtils.INTEGER_ZERO);

        context.setNewApplySampleItem(newApplySampleItem);

        applySampleItemService.addApplySampleItem(newApplySampleItem);


        return CONTINUE_PROCESSING;
    }


    @Override
    public boolean postprocess(Context c, Exception exception) {
        if (Objects.nonNull(exception)) {
            Optional.ofNullable(PositiveContext.from(c).getNewApplySampleItem())
                    .map(ApplySampleItemDto::getApplySampleItemId)
                    .ifPresent(applySampleItemService::deleteByApplySampleItemId);
        }
        return CONTINUE_PROCESSING;
    }
}
