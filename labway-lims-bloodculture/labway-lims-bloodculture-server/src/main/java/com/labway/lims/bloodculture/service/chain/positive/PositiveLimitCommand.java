package com.labway.lims.bloodculture.service.chain.positive;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.RedisPrefix;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 限流
 */
@Slf4j
@Component
class PositiveLimitCommand implements Command, Filter {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    private static final String MARK = IdUtil.objectId();

    @Override
    public boolean execute(Context c) throws Exception {
        final PositiveContext context = PositiveContext.from(c);

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(getKey(context),
                StringUtils.EMPTY, Duration.ofSeconds(30)))) {
            throw new IllegalArgumentException("正在标记阳性中");
        }

        context.put(MARK, true);

        return CONTINUE_PROCESSING;
    }

    private String getKey(Context c) {
        return redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":"
                + PositiveContext.from(c).getBloodCultureSampleId();
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        if (c.containsKey(MARK)) {
            stringRedisTemplate.delete(getKey(c));
        }
        return CONTINUE_PROCESSING;
    }
}
