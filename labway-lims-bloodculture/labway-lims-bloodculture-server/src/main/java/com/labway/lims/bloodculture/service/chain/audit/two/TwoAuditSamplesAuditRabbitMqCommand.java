package com.labway.lims.bloodculture.service.chain.audit.two;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/6/16 13:28
 */
@Slf4j
@Component
public class TwoAuditSamplesAuditRabbitMqCommand implements Command {

    private static final String ROUTING_KEY = "sample_change_key";
    @DubboReference
    private RabbitMQService rabbitMQService;

    @Override
    public boolean execute(Context c) throws Exception {
        final TwoAuditContext context = TwoAuditContext.from(c);
        final BloodCultureSampleDto sample = context.getSample();

        final ApplySampleEventDto event = new ApplySampleEventDto();
        event.setOrgId(LoginUserHandler.get().getOrgId());

        event.setHspOrgId(sample.getHspOrgId());
        event.setHspOrgCode(context.getApply().getHspOrgCode());
        event.setHspOrgName(sample.getHspOrgName());
        event.setApplyId(sample.getApplyId());
        event.setApplySampleId(sample.getApplySampleId());
        event.setBarcode(sample.getBarcode());
        event.setExtras(Map.of("sampleId", String.valueOf(sample.getBloodCultureSampleId()),
                "sampleNo", String.valueOf(sample.getSampleNo()),
				// 项目类型 血培养
		        ApplySampleEventDto.ExtrasConstants.ITEM_TYPE, ItemTypeEnum.BLOOD_CULTURE.name()
		        ));
        event.setEvent(ApplySampleEventDto.EventType.TwoCheck);

        final String json = JSON.toJSONString(event);
        rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

        log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功",
                sample.getApplySampleId(), sample.getBarcode(), json, RabbitMQService.EXCHANGE, ROUTING_KEY);

        return CONTINUE_PROCESSING;
    }
}
