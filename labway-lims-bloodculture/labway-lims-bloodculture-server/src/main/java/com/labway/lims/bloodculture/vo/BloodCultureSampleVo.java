package com.labway.lims.bloodculture.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/10 16:12
 */
@Setter
@Getter
public class BloodCultureSampleVo {

    /**
     * ID
     */
    private Long bloodCultureSampleId;

    /**
     * 样本申请单ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 样本状态
     *
     * @see SampleStatusEnum
     */
    private Integer status;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 仪器信息名称
     */
    private String instrumentName;

    /**
     * 仪器信息
     */
    private Long instrumentId;

    /**
     * 检验日期，暂定二次分拣日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date testDate;

    /**
     * 一次审核人工号
     */
    private String oneCheckerName;

    /**
     * 一次审核
     */
    private Long oneCheckerId;

    /**
     * 一审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date oneCheckDate;

    /**
     * 二次审核人工号
     */
    private String twoCheckerName;

    /**
     * 二次审核
     */
    private Long twoCheckerId;

    /**
     * 二审时间()
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date twoCheckDate;


    /**
     * 样本备注
     */
    private String sampleRemark;

    /**
     * 结果备注
     */
    private String resultRemark;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 样本是否加急 (1急诊 0不急)
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 检验项目名称
     */
    private List<String> testItemNames;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 是否打印
     * @see YesOrNoEnum
     */
    private Integer isPrint;

    /**
     * 就诊类型
     */
    private String applyType;

    /**
     * 创建时间
     */
    private Date createDate;


    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;

    /**
     * 是否是免疫二次分拣 1：是 0：否
     */
    private Integer isImmunityTwoPick;

}
