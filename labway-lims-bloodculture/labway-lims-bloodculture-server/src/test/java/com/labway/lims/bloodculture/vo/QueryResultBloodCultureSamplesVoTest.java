package com.labway.lims.bloodculture.vo;

import com.alibaba.fastjson.JSON;
import org.junit.Test;

import java.util.Date;

public class QueryResultBloodCultureSamplesVoTest {
    @Test
    public void test() {
        final QueryResultBloodCultureSamplesVo v = new QueryResultBloodCultureSamplesVo();
        v.setHspOrgId(0L);
        v.setCheckDateStart(new Date());
        v.setCheckDateEnd(new Date());
        v.setTestDateStart(new Date());
        v.setTestDateEnd(new Date());
        v.setTesterId(0L);
        v.setCheckerId(0L);
        v.setTestItemId(0L);
        v.setSampleStatus("");
       // v.setResult("");
        v.setPatientName("");
        v.setPatientSex(0);
        v.setPatientVisitCard("");
        v.setApplyType("");
        v.setBarcode("");
        System.out.println(JSON.toJSONString(v));

    }
}