package com.labway.lims.bloodculture.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.bloodculture.api.enums.BloodCultureSampleReportEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 血培养样本报告结果
 * </p>
 */
@Getter
@Setter
public class BloodCultureSampleResultDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long bloodCultureSampleResultId;

    /**
     * ID
     */
    private Long bloodCultureSampleId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单
     */
    private Long applyId;

    /**
     * 结果
     */
    private String result;

    /**
     * 结果编码
     */
    private String resultCode;

    /**
     * @see BloodCultureSampleReportEnum#name()
     */
    private String position;


    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 是否删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;
}
