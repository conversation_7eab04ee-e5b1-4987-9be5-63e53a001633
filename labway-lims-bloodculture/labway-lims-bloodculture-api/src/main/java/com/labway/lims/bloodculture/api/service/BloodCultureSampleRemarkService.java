package com.labway.lims.bloodculture.api.service;

import com.labway.lims.bloodculture.api.dto.BloodCultureSampleRemarkDto;

import java.util.Collection;
import java.util.List;

/**
 * 血培养备注
 */
public interface BloodCultureSampleRemarkService {


    /**
     * 删除血培养
     */
    void deleteByBloodCultureSampleId(long bloodCultureSampleId);


    /**
     * 删除血培养
     */
    void deleteByBloodCultureSampleId(long bloodCultureSampleId,String position);

    /**
     * 删除血培养
     */
    void deleteByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds);

    /**
     * 根据id查询
     */
    List<BloodCultureSampleRemarkDto> selectByBloodCultureSampleId(Long bloodCultureSampleId);

    /**
     * 根据id修改
     */
    void updateByBloodCultureSampleRemarkId(BloodCultureSampleRemarkDto remark);

    /**
     * 添加
     */
    long addByBloodCultureSampleRemark(BloodCultureSampleRemarkDto remark);
}
