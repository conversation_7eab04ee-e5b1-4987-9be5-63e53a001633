package com.labway.lims.bloodculture.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 血培养样本
 * </p>
 */
@Getter
@Setter
public class BloodCultureSampleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long bloodCultureSampleId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单
     */
    private Long applyId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组
     */
    private String groupName;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 专业小组
     */
    private String instrumentGroupName;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器信息
     */
    private Long instrumentId;

    /**
     * 检验日期，暂定二次分拣日期
     */
    private Date testDate;

    /**
     * 一审人id
     */
    private Long oneCheckerId;

    /**
     * 一审人
     */
    private String oneCheckerName;

    /**
     * 一审时间
     */
    private Date oneCheckDate;

    /**
     * 二审人id
     */
    private Long twoCheckerId;

    /**
     * 二审人
     */
    private String twoCheckerName;

    /**
     * 二审时间
     */
    private Date twoCheckDate;


    /**
     * 初审报告id
     */
    private Long oneCheckSampleReportId;

    /**
     * 终审报告id
     */
    private Long twoCheckSampleReportId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 是否删除
     *
     * @see YesOrNoEnum
     */
    private Integer isDelete;
}
