package com.labway.lims.bloodculture.api.service;

import com.labway.lims.bloodculture.api.dto.BloodCultureSampleResultDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 血培养结果
 */
public interface BloodCultureSampleResultService {

    /**
     * 删除血培养
     */
    void deleteByBloodCultureSampleId(long bloodCultureSampleId);

    /**
     * 删除血培养
     */
    void deleteByBloodCultureSampleId(long bloodCultureSampleId,String position);

    /**
     * 删除血培养
     */
    void deleteByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds);

    /**
     * 删除血培养
     */
    void deleteByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds, String position);

    /**
     * 根据样本id查询
     */
    List<BloodCultureSampleResultDto> selectByBloodCultureSampleId(Long bloodCultureSampleId);

    /**
     * 根据样本id查询
     */
    List<BloodCultureSampleResultDto> selectByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds);

    /**
     * 根据样本id查询
     */
    Map<Long, List<BloodCultureSampleResultDto>> selectByBloodCultureSampleIdsAsMap(Collection<Long> bloodCultureSampleIds);

    /**
     * 根据id查询
     */
    BloodCultureSampleResultDto selectByBloodCultureSampleResultId(Long bloodCultureSampleResultId);

    /**
     * 添加结果
     */
    long addBloodCultureSampleResult(BloodCultureSampleResultDto result);

    /**
     * 添加结果
     */
    void addBloodCultureSampleResults(Collection<BloodCultureSampleResultDto> results);

    /**
     * 根据id删除
     */
    void deleteByBloodCultureSampleResultId(Long bloodCultureSampleResultId);
}
