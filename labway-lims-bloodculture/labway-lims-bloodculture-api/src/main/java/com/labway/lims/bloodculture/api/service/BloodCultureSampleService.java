package com.labway.lims.bloodculture.api.service;

import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureTwoUnPickInfoDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 血培养
 */
public interface BloodCultureSampleService {

    /**
     * 二次分拣
     */
    long twoPick(long applySampleId, long instrumentGroupId, String sampleNo);

    /**
     * 添加血培养
     */
    long addBloodCultureSample(BloodCultureSampleDto sample);

    /**
     * 删除血培养
     */
    void deleteByBloodCultureSampleId(long bloodCultureSampleId);

    /**
     * 删除血培养
     */
    void deleteByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds);

    /**
     * 根据id查询
     */
    @Nullable
    BloodCultureSampleDto selectByBloodCultureSampleId(long bloodCultureSampleId);

    /**
     * 根据申请单样本id查询
     */
    List<BloodCultureSampleDto> selectByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据申请单样本id查询
     */
    BloodCultureSampleDto selectByApplySampleId(Long applySampleId);

    /**
     * 取消二次分拣
     */
    BloodCultureTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds);

    List<BloodCultureSampleDto> selectByTestDate(Date testDateStart, Date testDateEnd, Long orgId);

    /**
     * 根据id修改样本
     */
    void updateByBloodCultureSampleId(BloodCultureSampleDto sample);

    /**
     * 初审
     */
    void oneCheck(long bloodCultureSampleId);

    /**
     * 终审
     */
    void twoCheck(long bloodCultureSampleId);

    /**
     * 根据id查询
     */
    List<BloodCultureSampleDto> selectByBloodCultureSampleIds(Collection<Long> bloodCultureSampleIds);

    /**
     * 标记阳性
     */
    void positive(Long bloodCultureSampleId);
}
