package com.labway.lims.infection.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * 添加检验项目
 */
@Getter
@Setter
public class AddTestItemDto {
    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 收费数量
     */
    private Integer count;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AddTestItemDto that = (AddTestItemDto) o;
        return Objects.equals(testItemCode, that.testItemCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(testItemCode);
    }
}
