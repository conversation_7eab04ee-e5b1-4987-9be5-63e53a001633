package com.labway.lims.infection.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/11 17:00
 */
@Getter
@Setter
public class QueryInfectionSamplesDto  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 急诊状态 0普通  1急诊  2全部
     */
    private Integer urgent;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 检验日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateEnd;
}
