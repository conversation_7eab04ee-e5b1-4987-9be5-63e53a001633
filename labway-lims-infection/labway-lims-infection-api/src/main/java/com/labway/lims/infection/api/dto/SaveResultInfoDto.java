package com.labway.lims.infection.api.dto;

import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nullable;
import java.io.Serializable;

@Getter
@Setter
public class SaveResultInfoDto  implements Serializable {
    /**
     * 结果
     */
    private String result;

    /**
     * 旧结果，null则没有
     */
    @Nullable
    private String beforeResult;

    /**
     * 检验判断
     */
    private String judge;

    /**
     * 是否是异常值
     */
    private Boolean exception;

    /**
     * 是否是危机值
     */
    private Boolean critical;

    /**
     * 命中的参考范围，null则没有
     */
    @Nullable
    private InstrumentReportItemReferenceDto instrumentReportItemReferenceDto;
}
