package com.labway.lims.infection.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 院感项目结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Setter
@Getter
public class InfectionSampleResultDto implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private Long infectionSampleResultId;

    /**
     * 样本ID
     */
    private Long infectionSampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 结果
     */
    private String result;

    /**
     * 单位
     */
    private String unit;

    /**
     * 结果判定
     */
    private String judge;

    /**
     * 参考范围
     */
    private String range;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
