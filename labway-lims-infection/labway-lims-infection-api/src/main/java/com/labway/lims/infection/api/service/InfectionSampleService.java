package com.labway.lims.infection.api.service;

import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.infection.api.dto.*;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/11 16:57
 */
public interface InfectionSampleService {
    /**
     * 根据检验日期查询
     */
    List<InfectionSampleDto> selectByTestDate(QueryInfectionSamplesDto dto);

    /**
     * 更具ID修改
     */
    boolean updateByInfectionSampleId(InfectionSampleDto dto);

    /**
     * 添加检验项目
     */
    void addTestItems(long infectionSampleId, Collection<AddTestItemDto> testItems);

    /**
     * 根据 applySampleId 查询
     */
    InfectionSampleDto selectByApplySampleId(long applySampleId);

    /**
     * 根据 applySampleId 查询
     */
    List<InfectionSampleDto> selectByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据 infectionSampleId 查询
     */
    @Nullable
    InfectionSampleDto selectByInfectionSampleId(long infectionSampleId);

    /**
     * 审核
     */
    void auditSamplesChain(InfecSampleAuditDto sampleAuditDto);

    /**
     * 取消审核
     */
    void cancelAuditSample(InfecSampleCancelAuditDto dto);

    /**
     * 根据 infectionSampleId 集合查询
     */
    List<InfectionSampleDto> selectByInfectionSampleIds(Collection<Long> sampleIds);

    /**
     * 二次分拣
     */
    long twoPick(long applySampleId, long instrumentGroupId, String sampleNo, Date twoPickDate);

    /**
     * 取消二次分拣
     */
    InfectionSampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds);

    /**
     * 添加院感样本
     */
    long addInfectionSample(InfectionSampleDto dto);

    /**
     * 根据 infectionSampleId 删除
     */
    boolean deleteByInfectionSampleId(long infectionSampleId);

    /**
     * 根据 infectionSampleId 删除
     */
    void deleteByInfectionSampleIds(Collection<Long> infectionSampleIds);

    void updateByInfectionSampleIds(InfectionSampleDto sampleDto, Collection<Long> infectionSampleIds);

    /**
     * 重新生成报告
     *
     * @return
     */
    SampleReportDto rebuildReport(long applySampleId);

    void updateByApplyId(InfectionSampleDto infectionSampleDto);

    void updateByApplyIds(InfectionSampleDto infectionSampleDto, Collection<Long> applyIds);
}
