package com.labway.lims.infection.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class QueryInfectionSamplesVo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * orgId
     */
    private Long orgId;

    /**
     * groupId
     */
    private Long groupId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 样本状态
     * @see SampleStatusEnum
     */
    private String sampleStatus;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 检验日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateEnd;
    /**
     * 检验者ID
     */
    private Long testerId;

    /**
     * 审核人ID
     */
    private Long checkerId;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 就诊类型
     */
    private String applyType;

    /**
     * 条码号
     */
    private String barcode;

}
