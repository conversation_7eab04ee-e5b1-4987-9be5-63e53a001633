package com.labway.lims.infection.service.chain.result;

import com.labway.lims.api.enums.infection.InfectionResultJudgeEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleResultService;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.Objects;

import static com.labway.lims.infection.service.chain.result.SaveResultContext.SAMPLE_RESULT;

/**
 * <AUTHOR>
 * @since 2023/3/30 16:23
 */
@Slf4j
@Component
public class SavePGSQLResultCommand implements Command {
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private InfectionSampleResultService infectionSampleResultService;
    @DubboReference
    private ReportItemService reportItemService;

    @DubboReference
    private SampleResultService sampleResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();

        final InfectionSampleDto sample = context.getSample();

        final ApplySampleItemDto sampleTestItem = context.getSampleTestItem();
        final InstrumentReportItemReferenceDto ref = context.getInstrumentReportItemReference();

        final long resultId = snowflakeService.genId();
        final ReportItemDto reportItemDto = context.getReportItem();
        final SampleResultDto dto = new SampleResultDto();
        dto.setSampleResultId(resultId);
        dto.setSampleId(sample.getInfectionSampleId());
        dto.setApplyId(sample.getApplyId());
        dto.setTestItemId(sampleTestItem.getTestItemId());
        dto.setTestItemCode(StringUtils.defaultString(sampleTestItem.getTestItemCode()));
        dto.setTestItemName(StringUtils.defaultString(sampleTestItem.getTestItemName()));
        dto.setReportItemId(context.getSampleReportItemId());
        dto.setReportItemCode(StringUtils.defaultString(reportItemDto.getReportItemCode()));
        dto.setReportItemName(StringUtils.defaultString(reportItemDto.getReportItemName()));
        dto.setUnit(StringUtils.defaultString(context.getUnit()));
        dto.setJudge(StringUtils.defaultString(context.getJudge()));
        dto.setResult(StringUtils.defaultString(context.getResult(), InfectionResultJudgeEnum.QUALIFIED.getDesc()));
        dto.setRange(StringUtils.defaultString(context.getRange()));
        dto.setType(StringUtils.EMPTY);
        dto.setInstrumentId(NumberUtils.LONG_ZERO);
        dto.setInstrumentResult(StringUtils.EMPTY);
        dto.setInstrumentName(StringUtils.EMPTY);
        dto.setApplySampleId(sample.getApplySampleId());
        dto.setStatus(ResultStatusEnum.NORMAL.getCode());
        dto.setInstrumentReportItemReferenceId(NumberUtils.LONG_ZERO);
        if (Objects.nonNull(ref)) {
            dto.setInstrumentReportItemReferenceId(ref.getInstrumentReportItemReferenceId());
        }
        sampleResultService.addSampleResult(dto);
        // 保存到上下文
        context.getContextResults().put(dto.getReportItemId(), dto.getResult());
        context.put(SAMPLE_RESULT, dto);
        return CONTINUE_PROCESSING;
    }
}
