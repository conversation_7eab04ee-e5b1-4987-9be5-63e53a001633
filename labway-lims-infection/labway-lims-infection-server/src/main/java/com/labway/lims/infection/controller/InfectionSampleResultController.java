package com.labway.lims.infection.controller;

import cn.hutool.core.map.MapUtil;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.dto.SaveResultDto;
import com.labway.lims.infection.api.service.InfectionSampleResultService;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.infection.vo.InfecSampleReportItemDetailVo;
import com.labway.lims.infection.vo.InfectionSampleResultUpdateVo;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 17:19
 */
@RestController
@RequestMapping("/infection-sample-result")
public class InfectionSampleResultController extends BaseController {
    @DubboReference
    private SampleResultService sampleResultService;
    @Resource
    private InfectionSampleResultService infectionSampleResultService;
    @Resource
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private SampleReportItemService sampleReportItemService;

    /**
     * 样本结果信息列表
     */
    @PostMapping("/querySampleReportItemDetails")
    public Object querySampleReportItemDetails(@RequestParam("infectionSampleId") Long infectionSampleId) {

        if (Objects.isNull(infectionSampleId)) {
            return Collections.emptyList();
        }
        final InfectionSampleDto sample = infectionSampleService.selectByInfectionSampleId(infectionSampleId);

        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("当前样本不存在");
        }
        final Map<Long, ApplySampleItemDto> applySampleItemMap = applySampleItemService.selectByApplySampleId(sample.getApplySampleId())
                .stream().collect(Collectors.toMap(ApplySampleItemDto::getTestItemId, Function.identity(), (a, b) -> a));
        if (MapUtil.isEmpty(applySampleItemMap)) {
            throw new IllegalArgumentException("当前样本检验项目不存在");
        }

        final List<SampleReportItemDto> reportItems = sampleReportItemService.selectBySampleId(infectionSampleId);
        final List<SampleResultDto> sampleResults = sampleResultService.selectBySampleId(infectionSampleId);
        final Map<String, InstrumentReportItemDto> instrumentReportItemMap = instrumentReportItemService.selectByInstrumentId(sample.getInstrumentId())
                .stream().collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, v -> v, (a, b) -> a));

        final LinkedList<InfecSampleReportItemDetailVo> detailVos = new LinkedList<>();

        for (SampleReportItemDto item : reportItems) {
            final InfecSampleReportItemDetailVo vo = new InfecSampleReportItemDetailVo();
            vo.setInfectionSampleId(infectionSampleId);
            vo.setInfectionSampleReportItemId(item.getSampleReportItemId());
            vo.setReportItemId(item.getReportItemId());
            vo.setReportItemCode(item.getReportItemCode());
            vo.setReportItemName(item.getReportItemName());
            vo.setTestItemCode(item.getTestItemCode());
            vo.setTestItemName(item.getTestItemName());
            vo.setTestItemId(item.getTestItemId());
            final SampleResultDto result = sampleResults.stream()
                    .filter(e -> Objects.equals(item.getSampleReportItemId(), e.getReportItemId())).findFirst().orElse(null);
            String unit = null;
            if (Objects.nonNull(result)) {
                vo.setInfectionSampleResultId(result.getSampleResultId());
                vo.setRange(result.getRange());
                vo.setJudge(result.getJudge());
                vo.setResult(result.getResult());
                unit = result.getUnit();
            }
            vo.setUnit(unit);

            // 获取收费数量
            final ApplySampleItemDto applySampleItem = applySampleItemMap.get(item.getTestItemId());
            if (Objects.isNull(applySampleItem)) {
                throw new IllegalStateException(String.format("当前样本不存在检验项目 [%s] ", item.getTestItemName()));
            }
            vo.setCount(ObjectUtils.defaultIfNull(applySampleItem.getCount(), NumberUtils.INTEGER_ONE));
            detailVos.add(vo);
        }

        return detailVos.stream().sorted(Comparator.comparing(InfecSampleReportItemDetailVo::getReportItemCode)
                        .thenComparing(InfecSampleReportItemDetailVo::getInfectionSampleReportItemId))
                .collect(Collectors.toList());
    }

    @PostMapping("/updateResult")
    public Object updateResult(@RequestBody InfectionSampleResultUpdateVo vo) {
        final Long infectionSampleReportItemId = vo.getInfectionSampleReportItemId();
        if (Objects.isNull(infectionSampleReportItemId)) {
            throw new IllegalStateException("样本报告项目id不能为空");
        }
        final InfectionSampleDto sampleDto = infectionSampleService.selectByInfectionSampleId(vo.getInfectionSampleId());
        if (Objects.isNull(sampleDto)) {
            throw new IllegalStateException("院感样本不存在");
        }

        final SampleReportItemDto sampleReportItem = sampleReportItemService
                .selectBySampleReportItemId(infectionSampleReportItemId, sampleDto.getInfectionSampleId());
        if (Objects.isNull(sampleReportItem)) {
            throw new IllegalStateException("报告项目不存在");
        }

        final InstrumentReportItemDto instrumentReportItem = instrumentReportItemService
                .selectByInstrumentIdAndReportItemCode(sampleDto.getInstrumentId(), sampleReportItem.getReportItemCode());
        if (Objects.isNull(instrumentReportItem)) {
            throw new IllegalStateException("当前仪器的报告项目不存在");
        }

        if (Objects.isNull(vo.getReportItemId())) {
            throw new IllegalArgumentException("ReportItemId 不能为空");
        }

        if (Objects.nonNull(vo.getResult()) && (StringUtils.length(vo.getResult()) > INPUT_MAX_LENGTH)) {
            throw new IllegalArgumentException("长度不能大于50");

        }

        final ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new IllegalArgumentException("样本已审核,修改失败");
        }

        //修改结果 (走责任链 根据参考范围生成结果判断等)
        final SaveResultDto resultDto = new SaveResultDto();
        resultDto.setInfectionSampleId(vo.getInfectionSampleId());
        resultDto.setApplySampleId(sampleDto.getApplySampleId());
        resultDto.setApplyId(sampleDto.getApplyId());
        resultDto.setTestItemId(sampleReportItem.getTestItemId());
        resultDto.setReportItemId(vo.getReportItemId());
        resultDto.setReportItemCode(vo.getReportItemCode());
        resultDto.setResult(vo.getResult());
        resultDto.setJudge(vo.getJudge());
        resultDto.setRange(vo.getRange());
        resultDto.setUnit(vo.getUnit());
        resultDto.setInstrumentId(sampleDto.getInstrumentId());
        resultDto.setInstrumentName(sampleDto.getInstrumentName());
        resultDto.setDate(new Date());
        resultDto.setSampleReportItemId(sampleReportItem.getSampleReportItemId());
        final Long resultId = infectionSampleResultService.saveResult(resultDto, SaveResultSourceEnum.FRONT);

        //保存近50条结果记录
        final String resultKey = redisPrefix.getBasePrefix() + "InfectionRecentResults";
        getAndSetRecentData(vo.getResult(), resultKey);

        //修改项目名称
        if (StringUtils.isNotBlank(vo.getReportItemName())) {
            final SampleReportItemDto dto = new SampleReportItemDto();
            dto.setSampleReportItemId(infectionSampleReportItemId);
            dto.setSampleId(sampleDto.getInfectionSampleId());
            dto.setReportItemName(vo.getReportItemName());
            if (BooleanUtils.isNotTrue(sampleReportItemService.updateBySampleReportItemId(dto))) {
                throw new IllegalStateException("修改项目名称失败");
            }

            //保存近50条项目记录
            final String itemKey = redisPrefix.getBasePrefix() + "InfectionRecentTestItems";
            getAndSetRecentData(vo.getReportItemName(), itemKey);

        }

        return Map.of("resultId", resultId);
    }

    /**
     * 近50条项目名称记录
     */
    @PostMapping("/recentTestItems")
    public Object getRecentTestItems(@RequestParam("recentData") String recentData) {

        final String key = redisPrefix.getBasePrefix() + "InfectionRecentTestItems";

        final Set<String> datas = getAndSetRecentData(null, key);

        if (Objects.isNull(datas)) {
            return Collections.emptySet();
        }

        if (StringUtils.isBlank(recentData)) {
            return getAndSetRecentData(null, key);
        }
        return datas.stream().filter(e -> StringUtils.contains(e, recentData)).collect(Collectors.toSet());
    }

    /**
     * 近50条结果值记录
     */
    @PostMapping("/recentResults")
    public Object getRecentResults(@RequestParam("recentData") String recentData) {

        final String key = redisPrefix.getBasePrefix() + "InfectionRecentResults";

        final Set<String> datas = getAndSetRecentData(null, key);
        if (Objects.isNull(datas)) {
            return Collections.emptySet();
        }

        if (StringUtils.isBlank(recentData)) {
            return getAndSetRecentData(null, key);
        }

        return datas.stream().filter(e -> StringUtils.contains(e, recentData)).collect(Collectors.toSet());

    }

    /**
     * 存储历史数据的个数
     */
    private static final long HISTORICAL_DATA_SIZE = 50;

    /**
     * 获取和设置数据
     *
     * @param data 设置数据 为空就是获取
     * @param key  传入的 redis key 值
     * @return 返回 redis key 的 values
     */
    @Nullable
    private Set<String> getAndSetRecentData(@Nullable String data, String key) {

        if (Objects.nonNull(data)) {
            // 设置当前时间为得分
            double score = Calendar.getInstance().getTimeInMillis();

            // 将数据添加到Sorted Set中
            stringRedisTemplate.opsForZSet().add(key, data, score);

            // 删除旧数据，只保留最新的 HISTORICAL_DATA_SIZE 条数据
            stringRedisTemplate.opsForZSet().removeRange(key, 0, -1 - HISTORICAL_DATA_SIZE);
        }

        // 输出历史数据
        return stringRedisTemplate.opsForZSet().reverseRange(key, 0, -1);
    }

}
