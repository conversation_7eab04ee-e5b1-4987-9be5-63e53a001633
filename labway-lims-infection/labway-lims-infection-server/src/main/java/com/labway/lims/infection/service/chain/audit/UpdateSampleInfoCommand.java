package com.labway.lims.infection.service.chain.audit;

import com.labway.lims.base.api.service.ReportTemplateService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UpdateSampleInfoCommand implements Command {

    @DubboReference
    private ReportTemplateService reportTemplateService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditSampleContext context = AuditSampleContext.from(c);

        final List<InfectionSampleDto> samples = context.getSamples();
        final List<Long> sampleIds = samples.stream().map(InfectionSampleDto::getInfectionSampleId).collect(Collectors.toList());

        //todo 保存报告

        // 把之前的报告单删除

        // 批量保存

        // 修改样本

        return CONTINUE_PROCESSING;
    }
}
