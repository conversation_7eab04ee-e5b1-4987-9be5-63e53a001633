package com.labway.lims.infection.service.chain.result;

import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 仪器参考范围
 *
 * <AUTHOR>
 * @since 2023/3/30 16:15
 */
@Slf4j
@Component
public class InstrumentReportReferenceCommand implements Command {

    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);
        final InfectionSampleDto sample = context.getSample();
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("该样本不存在");
        }

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();
        if (Objects.isNull(instrumentReportItem)) {
            throw new IllegalStateException("该仪器报告项不存在");
        }

        final Long instrumentId = sample.getInstrumentId();

        final List<InstrumentReportItemReferenceDto> refs = instrumentReportItemReferenceService
                .selectByInstrumentIdAndReportItemCodes(instrumentId, Collections.singleton(instrumentReportItem.getReportItemCode()));
        if (CollectionUtils.isEmpty(refs)) {
            return CONTINUE_PROCESSING;
        }

        context.put(SaveResultContext.INSTRUMENT_REPORT_REFERENCE, refs.get(0));

        return CONTINUE_PROCESSING;

    }

}
