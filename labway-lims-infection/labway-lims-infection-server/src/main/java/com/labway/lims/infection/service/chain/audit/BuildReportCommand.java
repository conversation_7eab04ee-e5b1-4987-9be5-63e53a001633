package com.labway.lims.infection.service.chain.audit;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.CAPdf;
import com.labway.lims.api.config.HspOrgConfig;
import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.MatchBindReportDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.RandomStringService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.dto.ca.CAPdfSignDto;
import com.labway.lims.pdfreport.api.enums.SealTypeEnum;
import com.labway.lims.pdfreport.api.service.CaPdfService;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.pdfreport.api.vo.ca.CAPdfSignVo;
import com.labway.lims.pdfreport.api.vo.ca.CASealVo;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BuildReportCommand implements Command {

    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private PdfReportService pdfReportService;
    @DubboReference
    private RandomStringService randomStringService;
    @DubboReference
    private ApplyService applyService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private ReportTemplateBindService reportTemplateBindService;
    @DubboReference
    private SystemParamService systemParamService;

    @DubboReference
    private CaPdfService caPdfService;

    @Resource
    private HspOrgConfig hspOrgConfig;

    @Override
    public boolean execute(Context c) throws Exception {

        AuditSampleContext context = AuditSampleContext.from(c);

        if (context.getSamples().isEmpty()) {
            return CONTINUE_PROCESSING;
        }

        Map<Long, ApplyDto> applyDtoByApplyId =
            context.getApplyDtoList().stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));
        Map<Long, ApplySampleDto> applySampleDtoByApplySampleId = context.getApplySampleList().stream()
            .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));
        Map<Long, List<ApplySampleItemDto>> applySampleItemsByApplySampleId =
            context.getApplySampleItemsByApplySampleId();

        Map<Long, List<SampleReportItemDto>> sampleReportItemBySampleId =
            context.getSampleReportItemDtos().stream().collect(Collectors.groupingBy(SampleReportItemDto::getSampleId));
        Map<Long, List<SampleResultDto>> sampleResultBySampleId =
            context.getSampleResultDtos().stream().collect(Collectors.groupingBy(SampleResultDto::getSampleId));

        LoginUserHandler.User user = LoginUserHandler.get();

        // 选择 院感样本 生成 pdf
        List<SampleReportDto> sampleReportDtos = Lists.newArrayList();
        for (InfectionSampleDto sample : context.getSamples()) {

            ApplyDto apply = applyDtoByApplyId.get(sample.getApplyId());
            ApplySampleDto applySampleDto = applySampleDtoByApplySampleId.get(sample.getApplySampleId());
            List<ApplySampleItemDto> sampleItems = applySampleItemsByApplySampleId.get(sample.getApplySampleId());

            List<SampleReportItemDto> reportItems = ObjectUtils
                .defaultIfNull(sampleReportItemBySampleId.get(sample.getInfectionSampleId()), Collections.emptyList());
            List<SampleResultDto> results = sampleResultBySampleId.get(sample.getInfectionSampleId());
            sample.setCheckerId(user.getUserId());
            sample.setCheckerName(user.getNickname());
            sampleReportDtos.add(buildPDF(sample, apply, applySampleDto, sampleItems, reportItems, results));
        }
        sampleReportService.addSampleReportBatch(sampleReportDtos);

        return CONTINUE_PROCESSING;
    }

    // 院感pdf走到了这里
    public SampleReportDto buildPDF(InfectionSampleDto sample, ApplyDto apply, ApplySampleDto applySample,
        List<ApplySampleItemDto> sampleItems, List<SampleReportItemDto> reportItems, List<SampleResultDto> results) throws Exception{

        final ProfessionalGroupDto group = groupService.selectByGroupId(sample.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException(String.format("专业组 [%s] 不存在", sample.getGroupName()));
        }

        final UserDto checker = userService.selectByUserId(sample.getCheckerId());
        if (Objects.isNull(checker)) {
            throw new IllegalStateException(String.format("审核人 [%s] 不存在", sample.getCheckerName()));
        }


        final UserDto tester = userService.selectByUserId(applySample.getTesterId());
        if (Objects.isNull(tester)) {
            throw new IllegalStateException(String.format("检验者 [%s] 不存在", applySample.getTesterName()));
        }



        final PdfReportParamDto param = new PdfReportParamDto();
        List<String> testItems =
            sampleItems.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList());
        param
            .put("sample",
                Dict.of("sampleNo", sample.getSampleNo(), "instrumentGroupName", sample.getInstrumentGroupName(),
                    "instrumentName", sample.getInstrumentName(), "testerName", applySample.getTesterName(),
                    "checkerName", sample.getCheckerName(), "sampleRemark", applySample.getSampleRemark(),
                    "resultRemark", applySample.getResultRemark(), "testItemName", StringUtils.join(testItems, ","),
                    "count", CollectionUtils.isNotEmpty(sampleItems)
                        ? sampleItems.get(NumberUtils.INTEGER_ZERO).getCount() : NumberUtils.INTEGER_ONE,
                    "_sample", Dict.parse(sample)));

        param.put("apply", Dict.of("masterBarcode", apply.getMasterBarcode(), "patientName", apply.getPatientName(),
            "patientAge", apply.getPatientAge(), "hspOrgName", apply.getHspOrgName(), "_apply", Dict.parse(apply)));

        applySample.setResultRemark(applySample.getResultRemark().replace("\n", "<br/>"));
        param.put("applySample",
            Dict.of("barcode", applySample.getBarcode(), "tubeName", applySample.getTubeName(), "sampleTypeName",
                applySample.getSampleTypeName(), "groupName", applySample.getGroupName(), "onePickerName",
                applySample.getOnePickerName(), "onePickDate", applySample.getOnePickDate(), "twoPickerName",
                applySample.getTwoPickerName(), "twoPickDate", applySample.getTwoPickDate(), "_applySample",
                Dict.parse(applySample)));

        param.put("applySampleItems", sampleItems.stream().map(Dict::parse)
                .collect(Collectors.toList()));

        final List<Dict> reportItemsParam = new ArrayList<>();
        // 按照检验单详情也顺序构建报告单
        reportItems.sort(Comparator.comparing(SampleReportItemDto::getReportItemCode)
                .thenComparing(SampleReportItemDto::getSampleReportItemId));
        for (SampleReportItemDto reportItem : reportItems) {
            final Dict dict = Dict.create();
            dict.set("_sampleReportItem", reportItem);
            dict.set("reportItemCode", reportItem.getReportItemCode());
            dict.set("reportItemName", reportItem.getReportItemName());
            dict.set("testItemCode", reportItem.getTestItemCode());
            dict.set("testItemName", reportItem.getTestItemName());

            //院感 结果上的 report_item_id 为 报告项目  sample_report_item_id
            final SampleResultDto sampleResult =
                results.stream().filter(e -> Objects.equals(e.getReportItemId(), reportItem.getSampleReportItemId()))
                    .findFirst().orElse(null);
            if (Objects.isNull(sampleResult)) {
                continue;
            }

            dict.set("_sampleResult", sampleResult);
            dict.set("result", sampleResult.getResult());
            dict.set("unit", sampleResult.getUnit());
            dict.set("range", sampleResult.getRange());
            dict.set("status", sampleResult.getStatus());
            dict.set("judge", sampleResult.getJudge());

            reportItemsParam.add(dict);
        }

        param.put("reportItems", reportItemsParam);

        // 签名
        param.put("signature", Dict.of(
                // 检验者
                "tester", Dict.of("name", tester.getNickname(), "cnSign", tester.getCnSign(), "enSign", tester.getEnSign(), "sign",
                        StringUtils.defaultString(tester.getCnSign(), tester.getEnSign())),
            // 一次审核人
            "oneChecker",
            Dict.of("name", checker.getNickname(), "cnSign", checker.getCnSign(), "enSign", checker.getEnSign(), "sign",
                StringUtils.defaultString(checker.getCnSign(), checker.getEnSign())),
            // 二次审核人
            "twoChecker",
            Dict.of("name", checker.getNickname(), "cnSign", checker.getCnSign(), "enSign", checker.getEnSign(), "sign",
                StringUtils.defaultString(checker.getCnSign(), checker.getEnSign())),
            // 批准者
            "approver", Dict.of("name", group.getApproverName(), "sign", group.getApproverSign())));


        // 会获取到多个模板，当获取到多个时，生成多个PDF，然后拼接起来
        final Map<String, List<ItemPdfMapping>> templates = getReportTemplates(sample, apply, sampleItems).stream()
                .collect(Collectors.groupingBy(ItemPdfMapping::getReportTemplateCode));

        log.info("条码 [{}] 最终获取到模板 {}", sample.getBarcode(), templates.keySet());

        final List<File> files = new ArrayList<>();
        for (var e : templates.entrySet()) {
            final List<ApplySampleItemDto> applySampleItems = e.getValue().stream().map(ItemPdfMapping::getApplySampleItem)
                    .collect(Collectors.toList());

            final PdfReportParamDto k = new PdfReportParamDto();
            k.putAll(param);
            // 检验项目隔离
            k.put("applySampleItems", applySampleItems.stream().map(Dict::parse)
                    .collect(Collectors.toList()));
            log.info("开始使用报告单模板 [{}] 生成条码 [{}] 的报告单。 参数 [{}]", e.getKey(), sample.getBarcode(),
                    JSON.toJSONString(k));

            final File tempFile = FileUtil.createTempFile();
            try (final FileOutputStream fos = new FileOutputStream(tempFile)) {

                // 是指定的几家医院需要ca认证
                String pdfCodeBak = e.getKey();
                String pdfCode = e.getKey();
                boolean contains = hspOrgConfig.getHspOrgCodes().contains(apply.getHspOrgCode());
                if(contains){
                    pdfCode = "CA_" + pdfCode;
                }
                byte[] bytes;
                try {
                    bytes = pdfReportService.build(pdfCode, k);
                }catch (Exception exception){
                    if(!contains){
                        throw exception;
                    }
                    log.error("ca模板生成失败， 生成原始模板");
                    contains = false;
                    bytes = pdfReportService.build(pdfCodeBak, k);
                }

                if(contains){
                    try {
                        byte[] CABytes = createCAPDF(bytes, applySample.getTesterName(), checker.getNickname());
                        if (CABytes.length > 0) {
                            bytes = CABytes;
                        }
                    } catch (Exception exception) {
                        bytes = pdfReportService.build(pdfCodeBak, param);
                        log.error("ca模板生成失败， 生成原始模板" + exception);
                    }
                }

                fos.write(bytes);
                log.info("使用报告单模板 [{}] 生成条码 [{}] 的报告单。 文件地址: {}", e.getKey(), sample.getBarcode(),
                        tempFile);
            }
            files.add(tempFile);
        }

        final File pdf = files.size() == NumberUtils.INTEGER_ONE ? files.iterator().next() : FileUtil.createTempFile();
        // 如果数量不是1，那么需要合并
        if (files.size() != NumberUtils.INTEGER_ONE) {
            final PDFMergerUtility merger = new PDFMergerUtility();
            for (File file : files) {
                merger.addSource(file);
            }

            final PDDocumentInformation information = new PDDocumentInformation();
            information.setKeywords(String.join(",", templates.keySet()));
            merger.setDestinationFileName(pdf.getAbsolutePath());
            merger.setDestinationDocumentInformation(information);

            try {
                merger.mergeDocuments(MemoryUsageSetting.setupTempFileOnly());
            } catch (Exception e) {
                log.error("条码 [{}] 审核时合并PDF失败", sample.getBarcode(), e);
                throw new IllegalStateException("审核时合并PDF失败", e);
            } finally {
                files.forEach(FileUtils::deleteQuietly);
            }
        }

        try (final FileInputStream fis = new FileInputStream(pdf)) {
            final SampleReportDto sr = new SampleReportDto();
            sr.setApplySampleId(applySample.getApplySampleId());
            sr.setApplyId(apply.getApplyId());
            sr.setSampleId(sample.getInfectionSampleId());
            sr.setBarcode(sample.getBarcode());
            sr.setFileType(SampleReportFileTypeEnum.PDF.name());
            sr.setUrl(huaweiObsUtils.upload(fis, MediaType.APPLICATION_PDF_VALUE));
            sr.setGroupName(applySample.getGroupName());
            sr.setGroupId(applySample.getGroupId());
            sr.setHspOrgId(apply.getHspOrgId());
            sr.setHspOrgName(apply.getHspOrgName());
            return sr;
        } finally {
            FileUtils.deleteQuietly(pdf);
        }

    }

    private ReportTemplateBindDto getReportTemplate(InfectionSampleDto sample, ApplyDto apply, List<ApplySampleItemDto> sampleItems) {
        MatchBindReportDto matchBindReportDto = new MatchBindReportDto();
        if (CollectionUtils.isNotEmpty(sampleItems)){
            matchBindReportDto.setTestItemIds(sampleItems.stream().map(ApplySampleItemDto::getTestItemId)
                    .collect(Collectors.toList()));

        }
        matchBindReportDto.setBarcode(sample.getBarcode());
        matchBindReportDto.setInstrumentGroupId(sample.getInstrumentGroupId());
        matchBindReportDto.setInstrumentGroupName(sample.getInstrumentGroupName());
        matchBindReportDto.setHspOrgId(apply.getHspOrgId());
        matchBindReportDto.setHspOrgName(apply.getHspOrgName());
        matchBindReportDto.setGroupId(sample.getGroupId());
        return reportTemplateBindService.findMatchReportTemplate(matchBindReportDto);
    }

    /**
     * {key: 检验项目编码,value: 模板编码}
     */
    private List<ItemPdfMapping> getReportTemplates(InfectionSampleDto sample, ApplyDto apply, List<ApplySampleItemDto> sampleItems) {
        final List<ItemPdfMapping> list = new ArrayList<>();

        // 是否允许多PDF
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.ALLOW_SAMPLE_MULTI_PDF.getCode(), apply.getOrgId());
        // 如果不允许，那么使用一个模板
        if (Objects.isNull(param) || BooleanUtils.isNotTrue(BooleanUtils.toBoolean(StringUtils.lowerCase(param.getParamValue())))) {
            final ReportTemplateBindDto reportTemplate = getReportTemplate(sample, apply, sampleItems);
            for (ApplySampleItemDto e : sampleItems) {
                list.add(new ItemPdfMapping()
                        .setApplySampleItem(e)
                        .setReportTemplateCode(reportTemplate.getReportTemplateCode()));
            }
            return list;
        }

        // 一个项目取一个模板
        for (ApplySampleItemDto e : sampleItems) {
            list.add(new ItemPdfMapping()
                    .setApplySampleItem(e)
                    .setReportTemplateCode(getReportTemplate(sample, apply, List.of(e)).getReportTemplateCode()));
        }

        return list;
    }

    /**
     * 打印capdf
     * @param bytes  文件
     * @param testName 检验者
     * @param auditName 审核者
     * @return
     * @throws IOException
     */
    private byte[] createCAPDF(byte[] bytes, String testName, String auditName) throws IOException {
        List<CAPdfSignDto.Strategy> strategies = new ArrayList<>();

        //  所有章
        List<CASealVo> caSealVos = caPdfService.selectSeal(null);
        if (CollectionUtils.isEmpty(caSealVos)) {
            return new byte[0];
        }

        // 检验者章
        CAPdfSignDto.Strategy strategy = this.getStrategy(caSealVos, hspOrgConfig.getTest(), testName);
        if(Objects.nonNull(strategy)) {
            strategies.add(strategy);
        }

        //  审核者章
        CAPdfSignDto.Strategy strategy1 = this.getStrategy(caSealVos, hspOrgConfig.getAudit(), auditName);
        if(Objects.nonNull(strategy1)) {
            strategies.add(strategy1);
        }

        // 公章
        CAPdfSignDto.Strategy strategy2 = this.getStrategy(caSealVos, hspOrgConfig.getOffice(), "-1");
        if(Objects.nonNull(strategy2)) {
            strategies.add(strategy2);
        }

        CAPdfSignDto caPdfSignDto = new CAPdfSignDto();
        caPdfSignDto.setFile(bytes);
        caPdfSignDto.setSignedStrategy(strategies);

        CAPdfSignVo caPdfSignVo = caPdfService.pdfQuiesceSign(caPdfSignDto);

        return caPdfService.download(caPdfSignVo.getEnvelopeId(), null);
    }

    /**
     * 获取签章配置
     * @param caSealVos 签章列表
     * @param caPdf 签署位置配置
     * @param sealName 签署人
     * @return
     */
    private CAPdfSignDto.Strategy getStrategy(List<CASealVo> caSealVos, CAPdf caPdf, String sealName) {
        if (Objects.equals(sealName, "-1")) {
            sealName = caPdf.getSealName();
        }
        String finalSealName = sealName;
        CASealVo caSealVo = caSealVos.stream().filter(e -> e.getSealName().startsWith(finalSealName)).findFirst().orElse(null);
        if (Objects.nonNull(caSealVo)) {
            CAPdfSignDto.Strategy strategy = new CAPdfSignDto.Strategy();
            strategy.setSealId(caSealVo.getId());
            strategy.setStragegyType(1);
            strategy.setKeywords(caPdf.getKeyword());
            strategy.setPages("all");
            if (Objects.equals(caSealVo.getSealType(), String.valueOf(SealTypeEnum.PERSION_SEAL.getCode()))) {
                strategy.setReduction(caPdf.getReduction());
            }
            strategy.setOffsetDirectionX(caPdf.getX());
            strategy.setOffsetDirectionY(caPdf.getY());
            strategy.setIndex(0);
            return strategy;
        }
        return null;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    private static class ItemPdfMapping {
        /**
         * 项目
         */
        private ApplySampleItemDto applySampleItem;

        /**
         * 模板
         */
        private String reportTemplateCode;
    }
}
