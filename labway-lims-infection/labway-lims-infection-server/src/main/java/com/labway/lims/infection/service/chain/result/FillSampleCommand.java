package com.labway.lims.infection.service.chain.result;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemResultExchangeService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/3/30 16:26
 */
@Slf4j
@Component
public class FillSampleCommand implements Command {
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private InstrumentReportItemResultExchangeService instrumentReportItemResultExchangeService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplyService applyService;
    @Resource
    private InfectionSampleService infectionSampleService;
    @Resource
    private SampleReportItemService sampleReportItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        if (Objects.isNull(context.getSampleTestItem())) {
            final List<ApplySampleItemDto> dtos = applySampleItemService.selectByApplySampleId(context.getApplySampleId());

            if (CollectionUtils.isEmpty(dtos)) {
                throw new IllegalStateException("院感样本检验项目不存在");
            }

            context.put(SaveResultContext.SAMPLE_TEST_ITEM, dtos.get(0));
        }

        if (Objects.isNull(context.getSample())) {
            final InfectionSampleDto dto = infectionSampleService.selectByApplySampleId(context.getApplySampleId());

            if (Objects.isNull(dto)) {
                throw new IllegalStateException("院感样本不存在");
            }

            context.put(SaveResultContext.SAMPLE, dto);
        }

        if (Objects.isNull(context.getApply())) {

            final ApplyDto applyDto = applyService.selectByApplyId(context.getSample().getApplyId());

            if (Objects.isNull(applyDto)) {
                throw new IllegalStateException("院感申请单不存在");
            }

            context.put(SaveResultContext.APPLY, applyDto);
        }

        if (Objects.isNull(context.getApplySample())) {
            final ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(context.getSample().getApplySampleId());

            if (Objects.isNull(applySampleDto)) {
                throw new IllegalStateException("院感申请单样本不存在");
            }

            context.put(SaveResultContext.APPLY_SAMPLE, applySampleDto);
        }

        if (Objects.isNull(context.getSampleReportItems())) {
            final List<SampleReportItemDto> reportItemDtos = sampleReportItemService.selectBySampleId(context.getSample().getInfectionSampleId());
            context.put(SaveResultContext.SAMPLE_REPORT_ITEMS, reportItemDtos);
        }

        if (Objects.isNull(context.getInstrumentReportItems())) {

            final List<InstrumentReportItemDto> instrumentReportItemDtos = instrumentReportItemService.selectByInstrumentId(context.getSample().getInstrumentId());
            context.put(SaveResultContext.INSTRUMENT_REPORT_ITEMS, instrumentReportItemDtos);
        }

        if (Objects.isNull(context.getReportItem())) {
            final String reportItemCode = context.getReportItemCode();

            final ReportItemDto reportItemDto = reportItemService.selectByReportItemCode(reportItemCode, LoginUserHandler.get().getOrgId());
            context.put(SaveResultContext.REPORT_ITEM, reportItemDto);
        }

        if (Objects.isNull(context.getInstrumentReportItem())) {
            final ReportItemDto reportItem = context.getReportItem();
            final InfectionSampleDto sample = context.getSample();

            InstrumentReportItemDto dto = context.getInstrumentReportItems().stream()
                    .filter(e -> Objects.equals(e.getReportItemCode(), reportItem.getReportItemCode()))
                    .findFirst().orElse(null);

            if (Objects.isNull(dto)) {
                final ReportItemDto itemDto = reportItemService.selectByReportItemCode(reportItem.getReportItemCode(), LoginUserHandler.get().getOrgId());
                if (Objects.isNull(itemDto)) {
                    throw new IllegalStateException("院感样本报告项不存在");
                }
                dto = instrumentReportItemService.selectByInstrumentIdAndReportItemCode(sample.getInstrumentId(),itemDto.getReportItemCode());

            }

            if (Objects.isNull(dto)) {
                throw new IllegalStateException("样本报告项不存在");
            }

            context.put(SaveResultContext.INSTRUMENT_REPORT_ITEM, dto);
        }

        return CONTINUE_PROCESSING;
    }
}
