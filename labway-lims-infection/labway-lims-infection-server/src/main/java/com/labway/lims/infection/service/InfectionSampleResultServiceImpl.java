package com.labway.lims.infection.service;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.dto.InfectionSampleResultDto;
import com.labway.lims.infection.api.dto.SaveResultDto;
import com.labway.lims.infection.api.service.InfectionSampleResultService;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.infection.mapper.TbInfectionSampleResultMapper;
import com.labway.lims.infection.model.TbInfectionSampleResult;
import com.labway.lims.infection.service.chain.result.SaveResultChain;
import com.labway.lims.infection.service.chain.result.SaveResultContext;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/4/11 17:18
 */
@Slf4j
@DubboService
public class InfectionSampleResultServiceImpl implements InfectionSampleResultService {
    @Resource
    private TbInfectionSampleResultMapper tbInfectionSampleResultMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private SaveResultChain saveResultChain;

    @Override
    public Long saveResult(SaveResultDto dto, SaveResultSourceEnum source) {
        Long applyId = dto.getApplyId();
        Long sampleId = dto.getInfectionSampleId();

        if (Objects.isNull(applyId) || Objects.isNull(sampleId)) {
            throw new IllegalArgumentException("缺少必填参数");
        }

        // k: 样本ID,v:审核状态
        final InfectionSampleDto sampleDto = infectionSampleService.selectByInfectionSampleId(sampleId);
        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("样本不存在");
        }
        final ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalArgumentException("申请单样本不存在");
        }
        //样本状态 待审核 待二审 已审核 终止（99）
        if (Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new IllegalStateException("该样本已审核,不可修改");
        }

        final SaveResultContext context = new SaveResultContext();
        context.setApplySampleId(dto.getApplySampleId());
        context.setResult(dto.getResult());
        context.setInstrumentResult(dto.getResult());
        context.setTestDate(dto.getDate());
        context.setReportItemId(dto.getReportItemId());
        context.setReportItemCode(dto.getReportItemCode());
        context.setJudge(dto.getJudge());
        context.setRange(dto.getRange());
        context.setUnit(dto.getUnit());
        context.setSource(source);
        context.setSampleNo(dto.getSampleNo());
        context.setSampleReportItemId(dto.getSampleReportItemId());
        context.setInfectionSampleId(sampleDto.getInfectionSampleId());
        try {
            if (!saveResultChain.execute(context)) {
                throw new IllegalStateException("修改结果失败");
            }
            return Optional.ofNullable(context.getSampleResult()).map(SampleResultDto::getSampleResultId)
                    .orElse(null);
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);

        } finally {
            log.info("报告项目 [{}] 保存结果耗时\n{}", dto.getReportItemId(), context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }


    private InfectionSampleResultDto convert(TbInfectionSampleResult result) {
        if (Objects.isNull(result)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(result), InfectionSampleResultDto.class);
    }
}
