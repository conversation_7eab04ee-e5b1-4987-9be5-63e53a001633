package com.labway.lims.infection.service.chain.result;

import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleResultService;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 删除之前的结果
 *
 * <AUTHOR>
 * @since 2023/3/30 16:21
 */
@Slf4j
@Component
public class RemoveBeforeResultCommand implements Command {

    @Resource
    private InfectionSampleResultService infectionSampleResultService;

    @DubboReference
    private SampleResultService sampleResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final Long sampleReportItemId = context.getSampleReportItemId();

        final InfectionSampleDto sample = context.getSample();

        final SampleResultDto sampleResult = sampleResultService.selectBySampleIdAndReportItemId(sample.getInfectionSampleId(),sampleReportItemId);

        if (Objects.isNull(sampleResult)) {
            return CONTINUE_PROCESSING;
        }
        context.put(SaveResultContext.BEFORE_RESULT, sampleResult.getResult());

        // 删除之前的结果
        sampleResultService.deleteBySampleResultId(sampleResult.getSampleResultId(),sample.getInfectionSampleId());
        return CONTINUE_PROCESSING;
    }
}
