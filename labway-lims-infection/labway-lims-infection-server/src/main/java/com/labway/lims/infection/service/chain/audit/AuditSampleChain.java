package com.labway.lims.infection.service.chain.audit;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class AuditSampleChain extends ChainBase implements InitializingBean {

    @Resource
    private BuildReportCommand buildReportCommand;
    @Resource
    private CheckSampleResultCommand checkSampleResultCommand;

    @Resource
    private RequestParamCommand requestParamCommand;
    @Resource
    private SampleAuditFlowCommand sampleAuditFlowCommand;

    @Resource
    private UpdateSampleAuditStatusCommand updateSampleAuditStatusCommand;

    @Resource
    private SampleAuditRabbitMqCommand sampleAuditRabbitMqCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 参数校验
        addCommand(requestParamCommand);

        // 检查样本异常信息等  1.1.3.7新增
        addCommand(checkSampleResultCommand);

        // 生成样本报告
        addCommand(buildReportCommand);

        // 修改样本的审核状态
        addCommand(updateSampleAuditStatusCommand);

        // 保存流水
        addCommand(sampleAuditFlowCommand);

        // 发送审核消息到 MQ
        addCommand(sampleAuditRabbitMqCommand);

        addCommand(e -> PROCESSING_COMPLETE);
    }
}
