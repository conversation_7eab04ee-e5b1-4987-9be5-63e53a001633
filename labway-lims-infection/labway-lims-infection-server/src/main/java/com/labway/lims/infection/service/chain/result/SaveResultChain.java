package com.labway.lims.infection.service.chain.result;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @since 2023/3/30 16:10
 */
@Slf4j
@Component
public class SaveResultChain extends ChainBase implements InitializingBean {
    @Resource
    private CheckParamsCommand checkParamsCommand;
    @Resource
    private CheckCanSaveCommand checkCanSaveCommand;
    @Resource
    private FillSampleCommand fillSampleCommand;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;
    @Resource
    private RemoveBeforeResultCommand removeBeforeResultCommand;
    @Resource
    private SavePGSQLResultCommand saveMySQLResultCommand;
    @Resource
    private SaveSampleFlowCommand sampleFlowCommand;
    @Resource
    private UpdateSampleCommand updateSampleCommand;
    @Resource
    private YinYangResultCommand yinYangResultCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        //校验参数
        addCommand(checkParamsCommand);
        //获取样本基本信息
        addCommand(fillSampleCommand);
        //校验参数
        addCommand(checkCanSaveCommand);
        //获取参考范围
        addCommand(instrumentReportReferenceCommand);
        //阴阳结果
        addCommand(yinYangResultCommand);
        //删除之前的结果
        addCommand(removeBeforeResultCommand);
        //保存MySQL
        addCommand(saveMySQLResultCommand);
        //跟新样本结果状态
        addCommand(updateSampleCommand);
        //记录修改日志
        addCommand(sampleFlowCommand);
        //结束
        addCommand(c -> PROCESSING_COMPLETE);
    }
}
