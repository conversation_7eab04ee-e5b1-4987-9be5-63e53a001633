package com.labway.lims.infection.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 院感项目结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class InfectionSampleResultUpdateVo extends InfectionSampleResultVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * infectionSampleReportItemId
     */
    private Long infectionSampleReportItemId;
}
