package com.labway.lims.infection.service.chain.audit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class RequestParamCommand implements Command {

    @Resource
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SampleResultService sampleResultService;

    @DubboReference
    private ApplySampleItemService applySampleItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditSampleContext context = AuditSampleContext.from(c);

        // 要审核的样本ids
        Collection<Long> infectionSampleIds = context.getInfectionSampleIds();

        List<InfectionSampleDto> infectionSampleDtos =
            infectionSampleService.selectByInfectionSampleIds(infectionSampleIds);

        final Set<Long> selectInfectionSampleIds =
            infectionSampleDtos.stream().map(InfectionSampleDto::getInfectionSampleId).collect(Collectors.toSet());

        if (infectionSampleIds.stream().anyMatch(x -> !selectInfectionSampleIds.contains(x))) {
            throw new IllegalStateException("存在无效院感样本");
        }

        // 对应申请单信息
        Set<Long> applyIds =
            infectionSampleDtos.stream().map(InfectionSampleDto::getApplyId).collect(Collectors.toSet());

        List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);

        Set<Long> selectApplyIds = applyDtos.stream().map(ApplyDto::getApplyId).collect(Collectors.toSet());

        if (applyIds.stream().anyMatch(x -> !selectApplyIds.contains(x))) {
            throw new IllegalStateException("存在无效外送样本:没有对应申请单");
        }

        // 对应申请单样本ids
        final Set<Long> applySampleIds =
            infectionSampleDtos.stream().map(InfectionSampleDto::getApplySampleId).collect(Collectors.toSet());

        final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);

        if (applySampleDtos.stream().anyMatch(e -> LoginUserHandler.get().getUserId().equals(e.getTesterId()))) {
            throw new IllegalStateException("检验者与审核者不能为同一用户");
        }

        Set<Long> selectApplySampleIds =
            applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());
        if (applySampleIds.stream().anyMatch(x -> !selectApplySampleIds.contains(x))) {
            throw new IllegalStateException("存在无效外送样本:没有对应申请单样本");
        }

        // 必须每一个都是未审核状态
        for (ApplySampleDto applySample : applySampleDtos) {

            if (applySampleService.isDisabled(applySample.getApplySampleId())) {
                throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", applySample.getBarcode()));
            }

            if (applySampleService.isTerminate(applySample.getApplySampleId())) {
                throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", applySample.getBarcode()));
            }

            if (Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
                throw new IllegalStateException(String.format("条码号:%s , 已审核", applySample.getBarcode()));
            }

            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
                throw new IllegalStateException(String.format("条码号:%s , 不是未审状态", applySample.getBarcode()));
            }
        }

        // 样本检验项目
        Map<Long, List<ApplySampleItemDto>> applySampleItemMap =
            applySampleItemService.selectByApplySampleIdsAsMap(applySampleIds);

        // 样本报告项目
        List<SampleReportItemDto> sampleReportItemDtos = sampleReportItemService.selectBySampleIds(infectionSampleIds);

        // 样本结果
        List<SampleResultDto> sampleResultDtos = sampleResultService.selectBySampleIds(infectionSampleIds);

        context.put(AuditSampleContext.INFECTION_SAMPLE_LIST, infectionSampleDtos);
        context.put(AuditSampleContext.APPLY, applyDtos);
        context.put(AuditSampleContext.APPLY_SAMPLE, applySampleDtos);
        context.put(AuditSampleContext.APPLY_SAMPLE_ITEM, applySampleItemMap);
        context.put(AuditSampleContext.SAMPLE_REPORT_ITEM, sampleReportItemDtos);
        context.put(AuditSampleContext.SAMPLE_RESULT, sampleResultDtos);

        return CONTINUE_PROCESSING;
    }
}
