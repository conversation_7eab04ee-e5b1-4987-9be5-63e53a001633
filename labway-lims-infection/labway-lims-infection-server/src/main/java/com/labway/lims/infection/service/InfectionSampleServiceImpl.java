package com.labway.lims.infection.service;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.*;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.infection.InfectionResultJudgeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.enums.AgeUnitEnum;
import com.labway.lims.base.api.service.*;
import com.labway.lims.infection.api.dto.*;
import com.labway.lims.infection.api.service.InfectionSampleResultService;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.infection.mapper.TbInfectionSampleMapper;
import com.labway.lims.infection.model.TbInfectionSample;
import com.labway.lims.infection.service.chain.audit.AuditSampleChain;
import com.labway.lims.infection.service.chain.audit.AuditSampleContext;
import com.labway.lims.infection.service.chain.audit.BuildReportCommand;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 17:04
 */
@Slf4j
@DubboService
public class InfectionSampleServiceImpl implements InfectionSampleService {
    @Resource
    private TbInfectionSampleMapper tbInfectionSampleMapper;
    @Resource
    private AuditSampleChain auditSampleChain;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private InstrumentGroupInstrumentService instrumentGroupInstrumentService;
    @Resource
    private InfectionSampleResultService infectionSampleResultService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @DubboReference
    private SampleResultService sampleResultService;
    @Resource
    private BuildReportCommand buildReportCommand;
    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @DubboReference
    private FinanceSampleLockService financeSampleLockService;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private EnvDetector envDetector;

    private static final String ROUTING_KEY = "sample_change_key";
    private final ExpressionParser expressionParser = new SpelExpressionParser();

    @DubboReference
    private TestItemService testItemService;

    @Override
    public List<InfectionSampleDto> selectByTestDate(QueryInfectionSamplesDto dto) {
        final LambdaQueryWrapper<TbInfectionSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInfectionSample::getGroupId, LoginUserHandler.get().getGroupId())
                .eq(TbInfectionSample::getOrgId, LoginUserHandler.get().getOrgId())
                .eq(Objects.nonNull(dto.getHspOrgId()), TbInfectionSample::getHspOrgId, dto.getHspOrgId())
                .eq(Objects.nonNull(dto.getInstrumentGroupId()), TbInfectionSample::getInstrumentGroupId,
                        dto.getInstrumentGroupId())
                .ge(Objects.nonNull(dto.getTestDateStart()), TbInfectionSample::getTestDate, dto.getTestDateStart())
                .le(Objects.nonNull(dto.getTestDateEnd()), TbInfectionSample::getTestDate, dto.getTestDateEnd())
                .orderByAsc(TbInfectionSample::getInfectionSampleId);
        return tbInfectionSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public boolean updateByInfectionSampleId(InfectionSampleDto dto) {
        final TbInfectionSample sample = JSON.parseObject(JSON.toJSONString(dto), TbInfectionSample.class);
        final LoginUserHandler.User user = LoginUserHandler.get();
        if (tbInfectionSampleMapper.updateById(sample) < 1) {
            return false;
        }
        log.info("用户 [{}] 更新院感样本成功 [{}]", user.getNickname(), JSON.toJSONString(dto));
        return true;
    }

    @Override
    public void addTestItems(long infectionSampleId, Collection<AddTestItemDto> testItems) {

        final InfectionSampleDto infectionSample = selectByInfectionSampleId(infectionSampleId);
        if (Objects.isNull(infectionSample)) {
            throw new IllegalArgumentException("院感样本不存在");
        }

        {
            final Map<String, ApplySampleItemDto> items = applySampleItemService.selectByApplySampleId(infectionSample.getApplySampleId())
                    .stream().collect(Collectors.toMap(ApplySampleItemDto::getTestItemCode, v -> v, (a, b) -> a));
            for (AddTestItemDto vo : testItems) {

                if (Objects.isNull(vo.getCount()) || vo.getCount() < NumberUtils.INTEGER_ZERO) {
                    throw new IllegalStateException(String.format("检验项目 [%s] 数量错误", vo.getTestItemName()));
                }

                if (items.containsKey(vo.getTestItemCode())) {
                    throw new IllegalStateException(String.format("检验项目 [%s] 已存在", vo.getTestItemName()));
                }
            }
        }

        final Map<String, TestItemDto> items = testItemService.selectByTestItemCodes(testItems.stream().map(AddTestItemDto::getTestItemCode)
                        .collect(Collectors.toSet()), LoginUserHandler.get().getOrgId()).stream()
                .collect(Collectors.toMap(TestItemDto::getTestItemCode, v -> v, (a, b) -> a));
        for (AddTestItemDto vo : testItems) {
            if (!items.containsKey(vo.getTestItemCode())) {
                throw new IllegalStateException(String.format("检验项目 [%s] 不存在", vo.getTestItemName()));
            }
        }


        // 仪器报告项目
        final Map<String, InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService
                .selectByInstrumentIdAsMap(infectionSample.getInstrumentId());

        // 专业小组下的报告项目
        final Map<String, InstrumentReportItemDto> instrumentGroupReportItems = instrumentReportItemService
                .selectByInstrumentGroupIdAsMap(infectionSample.getInstrumentGroupId());

        // 要添加的报告项目
        final List<ReportItemDto> reportItems = reportItemService.selectByTestItemIds(items.values().stream()
                .map(TestItemDto::getTestItemId).collect(Collectors.toSet()));

        for (ReportItemDto e : reportItems) {
            final InstrumentReportItemDto instrumentReportItem = instrumentReportItems.getOrDefault(e.getReportItemCode(), instrumentGroupReportItems.get(e.getReportItemCode()));
            if (Objects.isNull(instrumentReportItem)) {
                throw new IllegalStateException(String.format("仪器 [%s] 或 专业小组 [%s] 下没有报告项目 [%s<%s>]", infectionSample.getInstrumentName(),
                        infectionSample.getInstrumentGroupName(),
                        e.getReportItemName(), e.getReportItemCode()));
            }
        }


        final LinkedList<Long> ids = snowflakeService.genIds(testItems.size() + 3000);

        for (AddTestItemDto vo : testItems) {
            final TestItemDto testItem = items.get(vo.getTestItemCode());
            final ApplySampleItemDto applySampleItem = new ApplySampleItemDto();
            applySampleItem.setApplySampleItemId(ids.pop());
            applySampleItem.setApplySampleId(infectionSample.getApplySampleId());
            applySampleItem.setApplyId(infectionSample.getApplyId());
            applySampleItem.setTestItemId(testItem.getTestItemId());
            applySampleItem.setTestItemCode(testItem.getTestItemCode());
            applySampleItem.setTestItemName(testItem.getTestItemName());
            applySampleItem.setItemType(testItem.getItemType());
            applySampleItem.setOutTestItemId(NumberUtils.LONG_ZERO);
            applySampleItem.setOutTestItemCode(StringUtils.EMPTY);
            applySampleItem.setOutTestItemName(StringUtils.EMPTY);
            applySampleItem.setSampleTypeName(testItem.getSampleTypeName());
            applySampleItem.setSampleTypeCode(testItem.getSampleTypeCode());
            applySampleItem.setTubeName(testItem.getTubeName());
            applySampleItem.setTubeCode(testItem.getTubeCode());
            applySampleItem.setGroupId(testItem.getGroupId());
            applySampleItem.setGroupName(testItem.getGroupName());
            applySampleItem.setUrgent(YesOrNoEnum.NO.getCode());
            applySampleItem.setRemark(StringUtils.EMPTY);
            applySampleItem.setCreateDate(new Date());
            applySampleItem.setCount(vo.getCount());
            applySampleItem.setUpdateDate(new Date());
            applySampleItem.setCreatorId(LoginUserHandler.get().getUserId());
            applySampleItem.setCreatorName(LoginUserHandler.get().getNickname());
            applySampleItem.setUpdaterId(LoginUserHandler.get().getUserId());
            applySampleItem.setUpdaterName(LoginUserHandler.get().getNickname());
            applySampleItem.setIsDelete(YesOrNoEnum.NO.getCode());
            applySampleItem.setSplitCode(StringUtils.EMPTY);
            applySampleItem.setIsOutsourcing(testItem.getEnableExport());
            applySampleItem.setExportOrgId(testItem.getExportOrgId());
            applySampleItem.setExportOrgName(testItem.getExportOrgName());
            applySampleItem.setIsFree(testItem.getEnableFee());
            applySampleItem.setFeePrice(testItem.getFeePrice());
            applySampleItem.setActualFeePrice(testItem.getFeePrice());
            applySampleItem.setStopStatus(StopTestStatus.NO_STOP_TEST.getCode());
            applySampleItem.setStopReasonCode(StringUtils.EMPTY);
            applySampleItem.setStopReasonName(StringUtils.EMPTY);
            applySampleItem.setItemSource(ApplySampleItemSourceEnum.DEFAULT_SOURCE.getCode());
            applySampleItem.setIsDisabled(YesOrNoEnum.NO.getCode());

            applySampleItemService.addApplySampleItem(applySampleItem);

        }


        // 添加报告项目
        sampleReportItemService.addSampleReportItems(reportItems.stream().map(e -> {
            final InstrumentReportItemDto instrumentReportItem = instrumentReportItems.getOrDefault(e.getReportItemCode(),
                    instrumentGroupReportItems.get(e.getReportItemCode()));
            final SampleReportItemDto item = new SampleReportItemDto();
            item.setSampleReportItemId(ids.pop());
            item.setApplyId(infectionSample.getApplyId());
            item.setSampleId(infectionSampleId);
            item.setApplySampleId(infectionSample.getApplySampleId());
            item.setReportItemCode(e.getReportItemCode());
            item.setReportItemName(e.getReportItemName());
            item.setReportItemId(e.getReportItemId());
            item.setTestItemId(e.getTestItemId());
            item.setTestItemCode(e.getTestItemCode());
            item.setTestItemName(e.getReportItemName());
            item.setIsRetest(YesOrNoEnum.NO.getCode());
            item.setIsDelete(YesOrNoEnum.NO.getCode());
            item.setPrintSort(instrumentReportItem.getPrintSort());
            item.setUpdateDate(new Date());
            item.setCreateDate(new Date());
            item.setCreatorId(LoginUserHandler.get().getUserId());
            item.setCreatorName(LoginUserHandler.get().getNickname());
            item.setUpdaterId(LoginUserHandler.get().getUserId());
            item.setUpdaterName(LoginUserHandler.get().getNickname());
            return item;
        }).collect(Collectors.toList()));

        // 添加检验项目
        sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                .applyId(infectionSample.getApplyId())
                .applySampleId(infectionSample.getApplySampleId())
                .barcode(infectionSample.getBarcode())
                .operateCode(BarcodeFlowEnum.TEST_ITEM_ADD.name())
                .operateName(BarcodeFlowEnum.TEST_ITEM_ADD.getDesc())
                .content(String.format("添加检验项目:\n%s", testItems.stream().map(e -> e.getTestItemName()
                                + String.format("(%s)", e.getTestItemCode())
                                + String.format("<%s>", e.getCount()))
                        .collect(Collectors.joining("\n")))).build());

        // 添加报告项目
        sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                .applyId(infectionSample.getApplyId())
                .applySampleId(infectionSample.getApplySampleId())
                .barcode(infectionSample.getBarcode())
                .operateCode(BarcodeFlowEnum.REPORT_ITEM_ADD.name())
                .operateName(BarcodeFlowEnum.REPORT_ITEM_ADD.getDesc())
                .content(String.format("添加报告项目:\n%s", reportItems.stream().map(e -> e.getReportItemName()
                                + String.format("(%s)", e.getReportItemCode()))
                        .collect(Collectors.joining("\n")))).build());
        ;

    }

    @Override
    @Nullable
    public InfectionSampleDto selectByApplySampleId(long applySampleId) {
        final LambdaQueryWrapper<TbInfectionSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInfectionSample::getApplySampleId, applySampleId)
                .orderByDesc(TbInfectionSample::getInfectionSampleId).last("limit 1");
        return convert(tbInfectionSampleMapper.selectOne(wrapper));
    }

    @Override
    public List<InfectionSampleDto> selectByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbInfectionSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbInfectionSample::getApplySampleId, applySampleIds);
        return tbInfectionSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Nullable
    public InfectionSampleDto selectByInfectionSampleId(long infectionSampleId) {
        final LambdaQueryWrapper<TbInfectionSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbInfectionSample::getInfectionSampleId, infectionSampleId).last("limit 1");
        return convert(tbInfectionSampleMapper.selectOne(wrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditSamplesChain(InfecSampleAuditDto sampleAuditDto) {
        Collection<Long> infectionSampleIds = sampleAuditDto.getInfectionSampleIds();
        if (CollectionUtils.isEmpty(infectionSampleIds)) {
            log.info("审核样本id为空");
            return;
        }
        final AuditSampleContext context = new AuditSampleContext();
        context.setInfectionSampleIds(infectionSampleIds);
        context.put(AuditSampleContext.SAMPLE_AUDTI_DTO,sampleAuditDto);
        context.setUser(LoginUserHandler.get());
        try {
            if (!auditSampleChain.execute(context)) {
                throw new IllegalStateException("审核失败");
            }
        } catch (RuntimeException e) {
            log.error("院感样本审核失败 [{}]", infectionSampleIds, e);
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            log.info("院感样本审核 [{}] 耗时\n{}", infectionSampleIds, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));

        }
    }

    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public void cancelAuditSample(InfecSampleCancelAuditDto dto) {
        final InfectionSampleDto sample = selectByInfectionSampleId(dto.getInfectionSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本信息不存在，请稍后再试");
        }

        if (applySampleService.isDisabled(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", sample.getBarcode()));
        }

        if (applySampleService.isTerminate(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", sample.getBarcode()));
        }

        if (BooleanUtils.isTrue(financeSampleLockService.isSampleLock(sample.getApplySampleId()))) {
            throw new IllegalStateException("该样本已经加锁，不能取消审核");
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException(String.format("条码 [%s] 申请单样本不存在", sample.getBarcode()));
        }

        // 检查审核人信息
        checkAuditorInfo(dto);

        // 修改样本状态
        final ApplySampleDto as = new ApplySampleDto();
        as.setStatus(SampleStatusEnum.NOT_AUDIT.getCode());
        as.setPrintDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        as.setIsPrint(YesOrNoEnum.NO.getCode());
        as.setPrinterName(StringUtils.EMPTY);
        as.setPrinterId(NumberUtils.LONG_ZERO);
        as.setApplySampleId(sample.getApplySampleId());
        applySampleService.updateByApplySampleId(as);

        // 二审人设置为空
        final InfectionSampleDto sampleDto = new InfectionSampleDto();
        sampleDto.setInfectionSampleId(sample.getInfectionSampleId());
        sampleDto.setCheckerId(NumberUtils.LONG_ZERO);
        sampleDto.setCheckerName(StringUtils.EMPTY);
        sampleDto.setCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        updateByInfectionSampleId(sampleDto);

        // 删除报告单
        sampleReportService.deleteBySampleIds(Collections.singletonList(dto.getInfectionSampleId()));

        final SampleFlowDto flowDto = new SampleFlowDto();
        flowDto.setApplyId(sample.getApplyId());
        flowDto.setApplySampleId(sample.getApplySampleId());
        flowDto.setBarcode(sample.getBarcode());
        flowDto.setOperateCode(BarcodeFlowEnum.CANCEL_SAMPLE_CHECK.name());
        flowDto.setOperateName(BarcodeFlowEnum.CANCEL_SAMPLE_CHECK.getDesc());
        flowDto.setOperator(LoginUserHandler.get().getNickname());
        flowDto.setContent("取消审核");
        flowDto.setOrgId(LoginUserHandler.get().getOrgId());
        flowDto.setOrgName(LoginUserHandler.get().getOrgName());
        flowDto.setIsDelete(YesOrNoEnum.NO.getCode());
        flowDto.setCreateDate(new Date());
        flowDto.setCreatorId(LoginUserHandler.get().getUserId());
        flowDto.setCreatorName(LoginUserHandler.get().getNickname());
        flowDto.setUpdateDate(new Date());
        flowDto.setUpdaterId(LoginUserHandler.get().getUserId());
        flowDto.setUpdaterName(LoginUserHandler.get().getNickname());
        sampleFlow(flowDto);
        final LoginUserHandler.User user = LoginUserHandler.get();
        threadPoolConfig.getPool().submit(() -> {
            try {
                final ApplyDto apply = applyService.selectByApplyId(sample.getApplyId());
                if (Objects.isNull(apply)) {
                    log.info("申请单不存在发送mq消息失败");
                    return;
                }
                final ApplySampleEventDto event = new ApplySampleEventDto();

                event.setOrgId(user.getOrgId());
                event.setHspOrgId(apply.getHspOrgId());
                event.setHspOrgCode(apply.getHspOrgCode());
                event.setHspOrgName(apply.getHspOrgName());
                event.setApplyId(sample.getApplyId());
                event.setApplySampleId(sample.getApplySampleId());
                event.setBarcode(sample.getBarcode());
                event.setExtras(Map.of("sampleId", String.valueOf(sample.getInfectionSampleId()), "outBarcode",
                        String.valueOf(applySample.getOutBarcode()), "sampleNo", String.valueOf(sample.getSampleNo()),
                        "businessCenterOrgCode", envDetector.getBusinessCenterOrgCode()));
                event.setEvent(ApplySampleEventDto.EventType.CancelTwoCheck);

                final String json = JSON.toJSONString(event);
                rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

                log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", sample.getApplySampleId(), sample.getBarcode(), json,
                        RabbitMQService.EXCHANGE, ROUTING_KEY);
            } catch (Exception e) {
                log.error("发送消息失败", e.getMessage(), e);
            }
        });

    }

    private void sampleFlow(SampleFlowDto dto) {
        sampleFlowService.addSampleFlow(dto);
    }

    private void checkAuditorInfo(InfecSampleCancelAuditDto dto) {
        String auditorPwd = dto.getAuditPwd();
        Long auditorId = dto.getAuditId();

        if (Objects.isNull(auditorId)) {
            throw new IllegalStateException("当前审核人为空,请重新选择");
        }
        if (Objects.isNull(auditorPwd)) {
            throw new IllegalStateException("当前审核人密码为空");
        }

        final UserDto userDto = userService.selectByUserId(dto.getAuditId());
        if (Objects.isNull(userDto)) {
            throw new IllegalStateException("审核人账号不存在，请联系管理员");
        }

        if (StringUtils.isBlank(auditorPwd)) {
            throw new IllegalStateException("请输入审核人密码");
        }

        if (!userService.validPassword(userDto.getUsername(), userDto.getPassword(), dto.getAuditPwd())) {
            throw new IllegalStateException("输入的审核人密码错误，请重新输入");
        }

    }

    @Override
    public List<InfectionSampleDto> selectByInfectionSampleIds(Collection<Long> sampleIds) {
        if (CollectionUtils.isEmpty(sampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbInfectionSample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbInfectionSample::getInfectionSampleId, sampleIds);
        queryWrapper.eq(TbInfectionSample::getIsDelete, YesOrNoEnum.NO.getCode());
        return tbInfectionSampleMapper.selectList(queryWrapper).stream().map(this::convert)
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public long twoPick(long applySampleId, long instrumentGroupId, String sampleNo, Date twoPickDate) {

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存,分拣失败");
        }

        // 查申请单
        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在,分拣失败");
        }

        final InstrumentGroupDto instrumentGroup = instrumentGroupService.selectByInstrumentGroupId(instrumentGroupId);
        if (Objects.isNull(instrumentGroup)) {
            throw new IllegalStateException("专业小组不存在");
        }

        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(applySampleId)
                .stream().filter(e -> Objects.equals(e.getItemType(), ItemTypeEnum.INFECTION.name()))
                .filter(obj -> Objects.equals(obj.getGroupId(), instrumentGroup.getGroupId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("当前样本下没有检验项目,分拣失败");
        }

        final Set<Long> testItemIds =
                applySampleItems.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toSet());

        final Collection<ReportItemDto> reportItems = reportItemService.selectByTestItemIds(testItemIds).stream()
                // 根据 testItemId 去重 只拿其中一个报告项目
                .collect(Collectors.toMap(ReportItemDto::getTestItemId, Function.identity(), (a, b) -> a)).values();
        if (CollectionUtils.isEmpty(reportItems)) {
            throw new IllegalStateException(
                    String.format("专业小组 [%s] 下没有维护报告项目", instrumentGroup.getInstrumentGroupName()));
        }

        final Collection<String> reportItemCodes =
                reportItems.stream().map(ReportItemDto::getReportItemCode).collect(Collectors.toSet());

        // 仪器
        final InstrumentGroupInstrumentDto instrument =
                instrumentGroupInstrumentService.selectByInstrumentGroupId(instrumentGroupId).stream()
                        .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                        .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode())).findFirst().orElse(null);
        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("院感检验下没有可用的仪器,二次分拣失败");
        }

        // 仪器报告项目
        final Map<String,
                InstrumentReportItemDto> instrumentReportItemMap = instrumentReportItemService
                .selectByInstrumentIdAndReportItemCodes(instrument.getInstrumentId(), reportItemCodes).stream()
                .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toMap(InstrumentReportItemDto::getReportItemCode, Function.identity(), (a, b) -> a));
        if (MapUtil.isEmpty(instrumentReportItemMap)) {
            throw new IllegalStateException(String.format("仪器 [%s] 下没有可用的报告项目", instrument.getInstrumentName()));
        }
        final Long applyId = apply.getApplyId();

        final InfectionSampleDto sample = new InfectionSampleDto();
        sample.setApplySampleId(applySampleId);
        sample.setApplyId(applyId);
        sample.setBarcode(applySample.getBarcode());
        sample.setSampleNo(sampleNo);
        sample.setGroupId(instrumentGroup.getGroupId());
        sample.setGroupName(instrumentGroup.getGroupName());
        sample.setInstrumentGroupId(instrumentGroupId);
        sample.setInstrumentGroupName(instrumentGroup.getInstrumentGroupName());
        sample.setInstrumentId(instrument.getInstrumentId());
        sample.setInstrumentName(instrument.getInstrumentName());
        sample.setStandardName(StringUtils.EMPTY);
        sample.setStandardCode(StringUtils.EMPTY);
        sample.setTestDate(ObjectUtils.defaultIfNull(twoPickDate, new Date()));
        sample.setCheckerId(NumberUtils.LONG_ZERO);
        sample.setCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        sample.setCheckerName(StringUtils.EMPTY);
        sample.setHspOrgId(apply.getHspOrgId());
        sample.setHspOrgName(apply.getHspOrgName());
        sample.setOrgId(apply.getOrgId());
        sample.setOrgName(apply.getOrgName());
        final long sampleId = addInfectionSample(sample);

        List<SampleReportItemDto> sampleReportItems = new ArrayList<>();
        List<SampleResultDto> results = new ArrayList<>();

        // 拿参考范围
        final Map<String, List<InstrumentReportItemReferenceDto>> referenceMap = instrumentReportItemReferenceService
                .selectByInstrumentIdAndReportItemCodesAsMap(instrument.getInstrumentId(), reportItemCodes);

        for (final ReportItemDto m : reportItems) {
            final InstrumentReportItemDto instrumentReportItem = instrumentReportItemMap.get(m.getReportItemCode());
            if (Objects.isNull(instrumentReportItem)) {
                throw new IllegalStateException(
                        String.format("仪器 [%s] 下没有维护报告项目 [%s]", instrument.getInstrumentName(), m.getReportItemName()));
            }

            // 添加样本报告项目
            final long sampleReportItemId = snowflakeService.genId();

            final SampleReportItemDto dto = new SampleReportItemDto();
            dto.setSampleReportItemId(sampleReportItemId);
            dto.setApplyId(applyId);
            dto.setApplySampleId(applySampleId);
            dto.setSampleId(sampleId);
            dto.setReportItemId(m.getReportItemId());
            dto.setReportItemCode(m.getReportItemCode());
            dto.setReportItemName(m.getReportItemName());
            dto.setTestItemId(m.getTestItemId());
            dto.setTestItemCode(m.getTestItemCode());
            dto.setTestItemName(m.getTestItemName());
            // 默认生成结果(结果判定是 合格)
            dto.setIsRetest(RetestStatusEnum.NORMAL.getCode());
            dto.setPrintSort(ObjectUtils.defaultIfNull(instrumentReportItem.getPrintSort(), NumberUtils.INTEGER_ZERO));
            sampleReportItems.add(dto);

            // 结果
            final SampleResultDto resultDto = new SampleResultDto();
            resultDto.setApplySampleId(applySampleId);
            resultDto.setSampleId(sampleId);
            resultDto.setApplyId(applyId);
            resultDto.setTestItemId(m.getTestItemId());
            resultDto.setTestItemCode(m.getTestItemCode());
            resultDto.setTestItemName(m.getTestItemName());
            resultDto.setReportItemId(sampleReportItemId);
            resultDto.setReportItemCode(dto.getReportItemCode());
            resultDto.setReportItemName(dto.getReportItemName());
            resultDto.setResult(StringUtils.EMPTY);
            resultDto.setJudge(InfectionResultJudgeEnum.QUALIFIED.getDesc());
            resultDto.setUnit(StringUtils.defaultString(instrumentReportItem.getReportItemUnitName()));
            resultDto.setType(StringUtils.EMPTY);
            resultDto.setInstrumentId(NumberUtils.LONG_ZERO);
            resultDto.setInstrumentResult(StringUtils.EMPTY);
            resultDto.setInstrumentName(StringUtils.EMPTY);
            resultDto.setStatus(ResultStatusEnum.NORMAL.getCode());
            // 查询参考值
            final InstrumentReportItemReferenceDto reference = ObjectUtils.defaultIfNull(
                    filterCustomerReportReference(apply, applySample, referenceMap.get(m.getReportItemCode())),
                    new InstrumentReportItemReferenceDto());
            resultDto.setRange(StringUtils.defaultString(StringUtils.defaultString(reference.getCnRefereValue(),
                    StringUtils.defaultString(reference.getEnRefereValue(), reference.getCnEnRefereValue()))));
            resultDto.setInstrumentReportItemReferenceId(Objects.requireNonNullElse(reference.getInstrumentReportItemReferenceId(), NumberUtils.LONG_ZERO));

            results.add(resultDto);
        }

        sampleReportItemService.addSampleReportItems(sampleReportItems);
        sampleResultService.addSampleResults(results);

        log.info("用户 [{}] 院感样本二次分拣完成 样本ID [{}],条码号 [{}] 样本号 [{}]", LoginUserHandler.get().getNickname(), sampleId,
                sample.getBarcode(), sampleNo);
        return sampleId;
    }

    @Override
    public InfectionSampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode())
                || Objects.equals(e.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new IllegalStateException("该样本已经审核，不能取消二次分拣");
        }

        final List<InfectionSampleDto> infectionSamples = selectByApplySampleIds(
                applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(infectionSamples)) {
            throw new IllegalStateException("院感样本不存在");
        }

        final Set<Long> ids =
                infectionSamples.stream().map(InfectionSampleDto::getInfectionSampleId).collect(Collectors.toSet());

        // 删除样本
        deleteByInfectionSampleIds(ids);

        // 删除样本报告项目
        sampleReportItemService.deleteBySampleIds(ids);

        // 删除样本结果
        sampleResultService.deleteBySampleIds(ids);

        // 清空生清单样本的结果备注，样本备注以及检验者
        final ApplySampleDto as = new ApplySampleDto();
        as.setSampleRemark(StringUtils.EMPTY);
        as.setResultRemark(StringUtils.EMPTY);
        as.setTesterId(NumberUtils.LONG_ZERO);
        as.setTesterName(StringUtils.EMPTY);
        applySampleService.updateByApplySampleIds(as, applySampleIds);

        log.info("遗传检验取消二次分拣 样本ID [{}] 条码号 [{}] 操作人 [{}]", ids,applySamples.stream().map(ApplySampleDto::getBarcode)
                .collect(Collectors.toSet()), LoginUserHandler.get().getNickname());
        return new InfectionSampleTwoUnPickInfoDto(
                infectionSamples.stream()
                        .map(
                                e -> new InfectionSampleTwoUnPickInfoDto.Sample().setSampleId(e.getInfectionSampleId())
                                        .setTwoPickDate(applySamples.stream()
                                                .filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId())).findFirst()
                                                .map(ApplySampleDto::getTwoPickDate).orElseThrow())
                                        .setGroupId(e.getGroupId()).setSampleNo(e.getSampleNo())
                                        .setInstrumentGroupId(e.getInstrumentGroupId()))
                        .collect(Collectors.toList()));
    }

    @Override
    public long addInfectionSample(InfectionSampleDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final TbInfectionSample sample = JSON.parseObject(JSON.toJSONString(dto), TbInfectionSample.class);
        sample.setInfectionSampleId(ObjectUtils.defaultIfNull(dto.getInfectionSampleId(), snowflakeService.genId()));
        sample.setCreatorId(user.getUserId());
        sample.setCreateDate(new Date());
        sample.setCreatorName(user.getNickname());
        sample.setUpdaterId(user.getUserId());
        sample.setUpdaterName(user.getNickname());
        sample.setUpdateDate(new Date());
        sample.setIsDelete(YesOrNoEnum.NO.getCode());

        if (tbInfectionSampleMapper.insert(sample) < 1) {
            throw new IllegalStateException("添加失败");
        }
        log.info("用户 [{}] 添加院感检验样本项成功 [{}]", user.getNickname(), JSON.toJSONString(sample));
        return sample.getInfectionSampleId();
    }

    @Override
    public boolean deleteByInfectionSampleId(long infectionSampleId) {
        if (tbInfectionSampleMapper.deleteById(infectionSampleId) < 1) {
            return false;
        }
        log.info("用户 [{}] 删除院感检验样本 [{}] 成功", LoginUserHandler.get().getNickname(), infectionSampleId);
        return true;
    }

    @Override
    public void deleteByInfectionSampleIds(Collection<Long> infectionSampleIds) {
        if (CollectionUtils.isEmpty(infectionSampleIds)) {
            return;
        }
        tbInfectionSampleMapper.deleteBatchIds(infectionSampleIds);
        log.info("用户 [{}] 删除院感检验样本 {} 成功", LoginUserHandler.get().getNickname(), infectionSampleIds);
    }

    @Override
    public void updateByInfectionSampleIds(InfectionSampleDto sampleDto, Collection<Long> infectionSampleIds) {
        if (CollectionUtils.isEmpty(infectionSampleIds)) {
            return;
        }
        tbInfectionSampleMapper.updateByInfectionSampleIds(sampleDto, infectionSampleIds);
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public SampleReportDto rebuildReport(long applySampleId) {
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            log.info("申请单样本不存在 [{}]", applySampleId);
            return null;
        }
        if (BooleanUtils.isFalse(Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode()))) {
            log.info("申请单样本状态不是审核状态 [{}]", applySampleId);
            return null;
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            log.info("申请单不存在 [{}]", applySample.getApplyId());
            return null;
        }

        final InfectionSampleDto sample = selectByApplySampleId(applySampleId);
        if (Objects.isNull(sample)) {
            log.info("样本不存在 [{}]", applySampleId);
            return null;
        }
        // 对应样本检验项目
        List<ApplySampleItemDto> sampleItemDtoList = applySampleItemService.selectByApplySampleId(applySampleId);
        // 对应样本报告项目
        List<SampleReportItemDto> sampleReportItemDtos =
                sampleReportItemService.selectBySampleId(sample.getInfectionSampleId());
        // 对应结果
        List<SampleResultDto> sampleResultDtos = sampleResultService.selectBySampleId(sample.getInfectionSampleId());

        SampleReportDto sampleReport = buildReportCommand.buildPDF(sample, apply, applySample, sampleItemDtoList,
                sampleReportItemDtos, sampleResultDtos);

        log.info("重新生成报告: [{}]", sampleReport.getUrl());
        return sampleReport;
    }

    @Override
    public void updateByApplyId(InfectionSampleDto infectionSampleDto) {
        LambdaUpdateWrapper<TbInfectionSample> wrapper = Wrappers.lambdaUpdate(TbInfectionSample.class)
                .eq(TbInfectionSample::getApplyId, infectionSampleDto.getApplyId())
                .eq(TbInfectionSample::getIsDelete,0)
                .set(TbInfectionSample::getHspOrgId, infectionSampleDto.getHspOrgId())
                .set(TbInfectionSample::getHspOrgName,infectionSampleDto.getHspOrgName())
                .set(TbInfectionSample::getUpdaterId,infectionSampleDto.getUpdaterId())
                .set(TbInfectionSample::getUpdaterName,infectionSampleDto.getUpdaterName())
                .set(TbInfectionSample::getUpdateDate,infectionSampleDto.getUpdateDate());
        tbInfectionSampleMapper.update(null, wrapper);

    }

    @Override
    public void updateByApplyIds(InfectionSampleDto infectionSampleDto, Collection<Long> applyIds) {
        for (List<Long> item : Lists.partition(new ArrayList<>(applyIds), 500)) {
            LambdaUpdateWrapper<TbInfectionSample> wrapper = Wrappers.lambdaUpdate(TbInfectionSample.class)
                .in(TbInfectionSample::getApplyId, item).eq(TbInfectionSample::getIsDelete, 0)
                .set(TbInfectionSample::getHspOrgId, infectionSampleDto.getHspOrgId())
                .set(TbInfectionSample::getHspOrgName, infectionSampleDto.getHspOrgName())
                .set(TbInfectionSample::getUpdaterId, infectionSampleDto.getUpdaterId())
                .set(TbInfectionSample::getUpdaterName, infectionSampleDto.getUpdaterName())
                .set(TbInfectionSample::getUpdateDate, infectionSampleDto.getUpdateDate());
            tbInfectionSampleMapper.update(null, wrapper);
        }
    }

    private InfectionSampleDto convert(TbInfectionSample sample) {
        if (Objects.isNull(sample)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(sample), InfectionSampleDto.class);
    }

    public static final int YEAR_DAYS = InstrumentReportItemReferenceService.YEAR_DAYS;
    public static final int MONTH_DAYS = InstrumentReportItemReferenceService.MONTH_DAYS;
    public static final int WEEK_DAYS = 7;

    @Nullable
    public InstrumentReportItemReferenceDto filterCustomerReportReference(ApplyDto apply, ApplySampleDto applySample,
                                                                          List<InstrumentReportItemReferenceDto> refs) {
        if (Objects.isNull(refs)) {
            return null;
        }

        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        // 删掉没有年龄范围的
        refs.removeIf(e -> StringUtils.isBlank(e.getAgeUnit()) || ObjectUtils.defaultIfNull(e.getAgeMin(), 0) < 0
                || ObjectUtils.defaultIfNull(e.getAgeMax(), 0) < 0);

        if (org.springframework.util.CollectionUtils.isEmpty(refs)) {
            return null;
        }

        final Set<InstrumentReportItemReferenceDto> irrFilter = new LinkedHashSet<>();
        for (InstrumentReportItemReferenceDto e : refs) {
            // 如果性别一样 并且 样本类型一样
            if (Objects.equals(e.getSexStyle(), apply.getPatientSex())
                    && Objects.equals(applySample.getSampleTypeCode(), e.getSampleTypeCode())) {
                irrFilter.add(e);
                // 如果样本类型一样 性别为通用 0通用
            } else if (Objects.equals(applySample.getSampleTypeCode(), e.getSampleTypeCode())
                    && Objects.equals(e.getSexStyle(), NumberUtils.INTEGER_ZERO)) {
                irrFilter.add(e);
                // 如果性别一样 且 样本类型为通用
            } else if (Objects.equals(e.getSexStyle(), apply.getPatientSex())
                    && Objects.equals(e.getSampleTypeCode(), SampleTypeEnum.ALL.getSampleTypeCode())) {
                irrFilter.add(e);
            }
        }

        // 通用也加上
        for (InstrumentReportItemReferenceDto e : refs) {
            if (Objects.equals(e.getSexStyle(), SexEnum.DEFAULT.getCode())
                    && Objects.equals(e.getSampleTypeCode(), SampleTypeEnum.ALL.getSampleTypeCode())) {
                irrFilter.add(e);
            }
        }

        if (irrFilter.isEmpty()) {
            return null;
        }

        // 获取到病人的年龄（天数）
        int _days = 0;

        // 如果没有输入年龄 那么根据生日计算
        if (Objects.isNull(apply.getPatientAge())) {
            // 根据生日获取此人活了多少天
            if (StringUtils.isNotBlank(apply.getPatientCard()) && IdcardUtil.isValidCard(apply.getPatientCard())) {
                _days = (int) IdcardUtil.getBirthDate(apply.getPatientCard()).between(new Date(), DateUnit.DAY);
            }
        } else {
            _days += (apply.getPatientAge() * YEAR_DAYS);
        }

        if (Objects.nonNull(apply.getPatientSubage())) {
            if (Objects.equals(apply.getPatientSubageUnit(), PatientSubAgeUnitEnum.MONTH.getValue())) {
                _days += (apply.getPatientSubage() * MONTH_DAYS);
            } else if (Objects.equals(apply.getPatientSubageUnit(), PatientSubAgeUnitEnum.WEEK.getValue())) {
                _days += (apply.getPatientSubage() * WEEK_DAYS);
            } else if (Objects.equals(apply.getPatientSubageUnit(), PatientSubAgeUnitEnum.DAY.getValue())) {
                _days += apply.getPatientSubage();
            }
        }

        final long days = _days;

        // 命中到之后，优先取 性别 或 样本类型 一致的
        final List<InstrumentReportItemReferenceDto> filteredRefs = irrFilter.stream().filter(e -> {

            long start = e.getAgeMin();
            long end = e.getAgeMax();

            // 判断天
            if (AgeUnitEnum.MONTH.name().equals(e.getAgeUnit())) {
                start *= MONTH_DAYS;
                end *= MONTH_DAYS;
            } else if (AgeUnitEnum.YEAR.name().equals(e.getAgeUnit())) {
                start *= YEAR_DAYS;
                end *= YEAR_DAYS;
            }

            try {
                // 判断年龄 , 判断年
                final String expression = String.format("%s %s %s && %s %s %s",
                        days, e.getAgeMinFormula(), start, days, e.getAgeMaxFormula(), end);
                final Object value = expressionParser.parseExpression(expression).getValue();
                log.info("条码号 [{}] 参考范围尝试命中 [{}] 命中结果 [{}] 参考范围 [{}]",
                        applySample.getBarcode(), expression, value, e.getCnRefereValue());
                return value instanceof Boolean && (Boolean) value;
            } catch (Exception ex) {
                log.error("条码号 [{}] 校验参考范围错误 [{}] 参考范围 [{}]",
                        applySample.getBarcode(), ex.getMessage(), e.getCnRefereValue(), ex);
                return false;
            }

        }).collect(Collectors.toList());

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(filteredRefs)) {
            return null;
        }

        // 如果 样本类型 和 性别一致，那么完全匹配
        InstrumentReportItemReferenceDto ref =
                filteredRefs.stream().filter(e -> Objects.equals(e.getSexStyle(), apply.getPatientSex())
                        && Objects.equals(applySample.getSampleTypeCode(), e.getSampleTypeCode())).findFirst().orElse(null);
        if (Objects.nonNull(ref)) {
            return ref;
        }

        // 如果样本类型一样 性别为通用
        ref = filteredRefs.stream().filter(e -> Objects.equals(applySample.getSampleTypeCode(), e.getSampleTypeCode())
                && Objects.equals(e.getSexStyle(), SexEnum.DEFAULT.getCode())).findFirst().orElse(null);
        if (Objects.nonNull(ref)) {
            return ref;
        }

        // 如果性别一样 且 样本类型为通用
        ref = filteredRefs.stream().filter(
                        e -> Objects.equals(e.getSexStyle(), apply.getPatientSex()) && Objects.equals(e.getSampleTypeCode(), SampleTypeEnum.ALL.getSampleTypeCode()))
                .findFirst().orElse(null);
        if (Objects.nonNull(ref)) {
            return ref;
        }

        // 如果能走到这里，那应该就是通用了
        return filteredRefs.iterator().next();

    }

    private PdfReportParamDto getPdfReportParamDto(InfectionSampleDto item, ApplyDto applyDto,
                                                   ApplySampleDto applySampleDto, LoginUserHandler.User user) {
        final PdfReportParamDto param = new PdfReportParamDto();
        param.put("apply", Dict.of("masterBarcode", applyDto.getMasterBarcode(), "patientName",
                applyDto.getPatientName(), "patientAge", applyDto.getPatientAge(), "hspOrgName", applyDto.getHspOrgName()));
        param.put("applySample",
                Dict.of("barcode", applySampleDto.getBarcode(), "tubeName", applySampleDto.getTubeName(), "sampleTypeName",
                        applySampleDto.getSampleTypeName(), "groupName", applySampleDto.getGroupName(), "onePickerName",
                        applySampleDto.getOnePickerName(), "onePickDate", applySampleDto.getOnePickDate(), "twoPickerName",
                        applySampleDto.getTwoPickerName(), "twoPickDate", applySampleDto.getTwoPickDate()));
        param.put("sample",
                Dict.of("sampleNo", item.getSampleNo(), "instrumentGroupName", item.getInstrumentGroupName(),
                        "instrumentName", item.getInstrumentName(), "testerName", applySampleDto.getTesterName(), "checkerName",
                        user.getNickname(), "sampleRemark", applySampleDto.getSampleRemark(), "resultRemark",
                        applySampleDto.getResultRemark()));
        return param;
    }

}
