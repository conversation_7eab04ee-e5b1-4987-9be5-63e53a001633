package com.labway.lims.infection.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/4/17 10:46
 */
@Getter
@Setter
public class InfecSampleReportItemDetailVo {

    /**
     * 院感样本id
     */
    private Long infectionSampleId;

    /**
     * infectionSampleReportItemId
     */
    private Long infectionSampleReportItemId;

    /**
     * infectionSampleResultId
     */
    private Long infectionSampleResultId;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;


    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 结果
     */
    private String result;

    /**
     * 参考范围
     */
    private String range;

    /**
     * 单位
     */
    private String unit;

    /**
     * 结果判定
     */
    private String judge;

    /**
     * 数量
     */
    private Integer count;
}
