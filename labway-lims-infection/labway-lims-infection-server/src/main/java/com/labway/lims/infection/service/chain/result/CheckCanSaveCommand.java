package com.labway.lims.infection.service.chain.result;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 校验结果是否可以保存
 *
 * <AUTHOR>
 * @since 2023/3/30 16:13
 */
@Slf4j
@Component
public class CheckCanSaveCommand implements Command {

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final ApplySampleDto applySample = context.getApplySample();
        final InstrumentReportItemDto reportItem = context.getInstrumentReportItem();

        if (Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new IllegalStateException("已审核样本无法修改结果");
        }

        //判断结果是否允许为空
        if (Objects.equals(reportItem.getIsResultNull(), 0)) {
            if (StringUtils.isBlank(context.getResult())) {
                if (context.getSource() == SaveResultSourceEnum.FRONT) {
                    throw new IllegalArgumentException(
                            String.format("项目 [%s] 结果不允许为空", reportItem.getReportItemName()));
                }
                throw new IllegalArgumentException(
                        String.format("项目 [%s] 结果不允许为空", reportItem.getReportItemName()));
            }
        }

        return CONTINUE_PROCESSING;
    }
}
