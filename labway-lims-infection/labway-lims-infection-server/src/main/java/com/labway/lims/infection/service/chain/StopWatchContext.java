package com.labway.lims.infection.service.chain;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import lombok.Getter;
import org.apache.commons.chain.impl.ContextBase;


@Getter
public abstract class StopWatchContext extends ContextBase {
    private static final String WATCH = "WATCH_" + IdUtil.objectId();

    public StopWatchContext() {
        put(WATCH, new StopWatch(getWatchName()));
    }

    public StopWatch getWatch() {
        return (StopWatch) get(WATCH);
    }

    protected abstract String getWatchName();
}