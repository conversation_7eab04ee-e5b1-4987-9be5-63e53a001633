package com.labway.lims.infection.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 院感样本
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_infection_sample")
public class TbInfectionSample implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long infectionSampleId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单
     */
    private Long applyId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 专业小组
     */
    private String instrumentGroupName;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 检验标准
     */
    private String standardName;

    /**
     * standardCode
     */
    private String standardCode;

    /**
     * 检验日期，暂定二次分拣日期
     */
    private Date testDate;

    /**
     * 审核人ID
     */
    private Long checkerId;

    /**
     * 一次审核
     */
    private String checkerName;

    /**
     * 审核日期
     */
    private Date checkDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    private Integer isDelete;
}
