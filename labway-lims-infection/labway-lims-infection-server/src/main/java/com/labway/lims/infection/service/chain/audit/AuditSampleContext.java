package com.labway.lims.infection.service.chain.audit;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.infection.api.dto.InfecSampleAuditDto;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.service.chain.StopWatchContext;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/3 15:45
 */
@SuppressWarnings("unchecked")
@Getter
@Setter
public class AuditSampleContext extends StopWatchContext {

    /**
     * 审核 院感样本id
     */
    private Collection<Long> infectionSampleIds;
    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    // 院感 检验
    public static final String INFECTION_SAMPLE_LIST = "INFECTION_SAMPLE_LIST_" + IdUtil.objectId();

    public static final String SAMPLE_AUDTI_DTO = "SAMPLE_AUDTI_DTO" + IdUtil.objectId();

    public InfecSampleAuditDto getAuditDto() {
        return (InfecSampleAuditDto) get(SAMPLE_AUDTI_DTO);
    }

    public List<InfectionSampleDto> getSamples() {
        return (List<InfectionSampleDto>)get(INFECTION_SAMPLE_LIST);
    }

    // 对应申请单
    public static final String APPLY = "APPLY_" + IdUtil.objectId();

    public List<ApplyDto> getApplyDtoList() {
        return (List<ApplyDto>)get(APPLY);
    }


    // 对应申请单样本
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    public List<ApplySampleDto> getApplySampleList() {
        return (List<ApplySampleDto>)get(APPLY_SAMPLE);
    }

    // 对应申请单样本
    public static final String APPLY_SAMPLE_ITEM = "APPLY_SAMPLE_ITEM_" + IdUtil.objectId();

    public Map<Long, List<ApplySampleItemDto>> getApplySampleItemsByApplySampleId() {
        return (Map<Long, List<ApplySampleItemDto>>)get(APPLY_SAMPLE_ITEM);
    }

    // 对应样本报告项目
    public static final String SAMPLE_REPORT_ITEM = "SAMPLE_REPORT_ITEM_" + IdUtil.objectId();

    public List<SampleReportItemDto> getSampleReportItemDtos() {
        return (List<SampleReportItemDto>)get(SAMPLE_REPORT_ITEM);
    }

    // 对应样本结果
    public static final String SAMPLE_RESULT = "SAMPLE_RESULT_" + IdUtil.objectId();

    public List<SampleResultDto> getSampleResultDtos() {
        return (List<SampleResultDto>)get(SAMPLE_RESULT);
    }

    public static AuditSampleContext from(Context c) {
        return (AuditSampleContext)c;
    }

    @Override

    protected String getWatchName() {
        return "条码审核";
    }
}
