package com.labway.lims.infection.service.chain.result;

import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 校验参数
 *
 * <AUTHOR>
 * @since 2023/3/30 16:14
 */
@Slf4j
@Component
public class CheckParamsCommand implements Command, Filter {

    @DubboReference
    private SampleReportItemService sampleReportItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        if (Objects.isNull(context.getReportItemId())) {
            throw new IllegalArgumentException("报告项目 ID不能为空");
        }

        if (Objects.isNull(context.getApplySampleId())) {
            throw new IllegalArgumentException("申请单样本 ID不能为空");
        }

        final SampleReportItemDto sampleReportItem = sampleReportItemService
                .selectBySampleReportItemId(context.getSampleReportItemId(),context.getInfectionSampleId());
        if (Objects.isNull(sampleReportItem)) {
            throw new IllegalStateException("样本报告项目不存在");
        }

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
