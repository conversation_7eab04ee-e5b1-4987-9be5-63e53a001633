package com.labway.lims.infection.service.chain.audit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UpdateSampleAuditStatusCommand implements Command {

    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private InfectionSampleService infectionSampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditSampleContext context = AuditSampleContext.from(c);

        List<InfectionSampleDto> samples = context.getSamples();
        LoginUserHandler.User user = context.getUser();

        Set<Long> infectionSampleIds =
            samples.stream().map(InfectionSampleDto::getInfectionSampleId).collect(Collectors.toSet());

        final InfectionSampleDto sampleDto = new InfectionSampleDto();
        sampleDto.setCheckDate(new Date());
        sampleDto.setCheckerId(user.getUserId());
        sampleDto.setCheckerName(user.getNickname());

        infectionSampleService.updateByInfectionSampleIds(sampleDto, infectionSampleIds);

        // 修改样本状态
        Set<Long> applySampleIdList =
            samples.stream().map(InfectionSampleDto::getApplySampleId).collect(Collectors.toSet());

        final ApplySampleDto dto = new ApplySampleDto();
        dto.setStatus(SampleStatusEnum.AUDIT.getCode());
        applySampleService.updateByApplySampleIds(dto, applySampleIdList);

        return CONTINUE_PROCESSING;
    }
}
