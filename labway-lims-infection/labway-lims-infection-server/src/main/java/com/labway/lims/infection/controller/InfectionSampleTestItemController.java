package com.labway.lims.infection.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.infection.api.dto.AddTestItemDto;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.infection.vo.AddTestItemVo;
import com.labway.lims.infection.vo.TestItemUpdateCountVo;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/infection-sample-test-item")
public class InfectionSampleTestItemController extends BaseController {

    @Resource
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SampleFlowService sampleFlowService;


    /**
     * 添加检验项目
     */
    @PostMapping("/add")
    public Object addTestItems(@RequestBody Set<AddTestItemVo> vos, Long infectionSampleId) {
        if (Objects.isNull(infectionSampleId) || CollectionUtils.isEmpty(vos)) {
            throw new IllegalStateException("参数错误");
        }

        for (AddTestItemVo vo : vos) {
            if (Objects.isNull(vo.getCount())) {
                throw new IllegalStateException(String.format("检验项目 [%s] 收费数量不可为空", vo.getTestItemName()));
            }
        }

        final InfectionSampleDto infectionSample = infectionSampleService.selectByInfectionSampleId(infectionSampleId);
        if (Objects.isNull(infectionSample)) {
            throw new IllegalStateException("样本不存在");
        }

        infectionSampleService.addTestItems(infectionSampleId,
                JSON.parseArray(JSON.toJSONString(vos), AddTestItemDto.class));


        return Map.of("testItems", applySampleItemService.selectByApplySampleId(infectionSample.getApplySampleId()));
    }


    /**
     * 修改检验项目数量
     */
    @PostMapping("/update-count")
    public Object updateCount(@RequestBody TestItemUpdateCountVo vo) {
        if (Objects.isNull(vo.getCount()) || Objects.isNull(vo.getInfectionSampleId())
                || Objects.isNull(vo.getTestItemId())) {
            throw new IllegalStateException("参数错误");
        }

        if (vo.getCount() < 1) {
            throw new IllegalStateException("检验项目数量不小于 1");
        }

        final InfectionSampleDto infectionSample = infectionSampleService.selectByInfectionSampleId(vo.getInfectionSampleId());
        if (Objects.isNull(infectionSample)) {
            throw new IllegalStateException("样本不存在");
        }

        final ApplySampleItemDto applySampleItem = applySampleItemService.selectByApplySampleIdAndTestItemId(infectionSample.getApplySampleId(), vo.getTestItemId());
        if (Objects.isNull(applySampleItem)) {
            throw new IllegalStateException("检验项目不存在");
        }

        final ApplySampleItemDto n = new ApplySampleItemDto();
        n.setUpdaterId(LoginUserHandler.get().getUserId());
        n.setUpdaterName(LoginUserHandler.get().getNickname());
        n.setApplySampleItemId(applySampleItem.getApplySampleItemId());
        n.setCount(vo.getCount());
        applySampleItemService.updateBatchById(Collections.singleton(n));

        // 条码环节
        sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                .applyId(infectionSample.getApplyId())
                .applySampleId(infectionSample.getApplySampleId())
                .barcode(infectionSample.getBarcode())
                .operator(LoginUserHandler.get().getNickname())
                .operatorId(LoginUserHandler.get().getUserId())
                .operateCode(BarcodeFlowEnum.TEST_ITEM_UPDATE.name())
                .operateName(BarcodeFlowEnum.TEST_ITEM_UPDATE.getDesc())
                .content(String.format("修改检验项目 [%s] 数量 [%s] -> [%s]", applySampleItem.getTestItemName(),
                        applySampleItem.getCount(), vo.getCount())).build());


        final LinkedList<SampleReportItemDto> sampleReportItems = sampleReportItemService.selectBySampleId(vo.getInfectionSampleId())
                .stream().filter(e -> Objects.equals(e.getTestItemId(), vo.getTestItemId()))
                .sorted((a, b) -> b.getSampleReportItemId().compareTo(a.getSampleReportItemId()))
                .collect(Collectors.toCollection(LinkedList::new));
        if (sampleReportItems.size() > n.getCount()) {
            final int count = sampleReportItems.size() - n.getCount();
            final List<SampleReportItemDto> deletedSampleReportItems = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                final SampleReportItemDto pop = sampleReportItems.pop();
                deletedSampleReportItems.add(pop);
                sampleReportItemService.deleteBySampleReportItemId(pop.getSampleReportItemId(),
                        vo.getInfectionSampleId());
            }


            // 条码环节
            sampleFlowService.addSampleFlow(SampleFlowDto.builder()
                    .applyId(infectionSample.getApplyId())
                    .applySampleId(infectionSample.getApplySampleId())
                    .barcode(infectionSample.getBarcode())
                    .operator(LoginUserHandler.get().getNickname())
                    .operatorId(LoginUserHandler.get().getUserId())
                    .operateCode(BarcodeFlowEnum.REPORT_ITEM_DELETE.name())
                    .operateName(BarcodeFlowEnum.REPORT_ITEM_DELETE.getDesc())
                    .content(String.format("由于缩减检验项目 [%s] 数量，删除报告项目 [%s]", applySampleItem.getTestItemName(),
                            deletedSampleReportItems.stream().map(SampleReportItemDto::getReportItemName)
                                    .collect(Collectors.joining("、")))).build());
        }


        return Map.of();
    }
}
