package com.labway.lims.infection.service.chain.audit;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.ExceptionCodeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.infection.api.dto.InfecSampleAuditDto;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CheckSampleResultCommand implements Command {
    @Resource
    private InstrumentReportItemService instrumentReportItemService;
    @Resource
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleAbnormalService sampleAbnormalService;

    @Override
    public boolean execute(Context c) throws Exception {

        AuditSampleContext context = AuditSampleContext.from(c);

        final List<InfectionSampleDto> samples = context.getSamples();

        //1.1.3.7 新增异常结果提醒
        // 如果不是强制审核 则做查询判断
        final InfecSampleAuditDto param = context.getAuditDto();
        if (!param.getAuditForce()) {
            List<SampleAbnormalDto> sampleAbnormalDtoList = sampleAbnormalService.selectByBarcodes(samples.stream().map(InfectionSampleDto::getBarcode).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(sampleAbnormalDtoList)) {
                List<String> errorMsgList = new ArrayList<>();
                Set<String> barcodes = new HashSet<>();
                sampleAbnormalDtoList.forEach(sampleAbnormalDto -> {
                    if (!barcodes.contains(sampleAbnormalDto.getBarcode())) {
                        errorMsgList.add(String.format("条码号 [%s]的样本存在异常情况！\n", sampleAbnormalDto.getBarcode()));
                        barcodes.add(sampleAbnormalDto.getBarcode());
                    }
                });
                throw new LimsCodeException(ExceptionCodeEnum.ROUTINE_AUDIT.getCode(),
                        JSON.toJSONString(errorMsgList));
            }
        }


//        for (InfectionSampleDto dto : samples) {
//            if (Objects.isNull(infectionSampleService.selectByInfectionSampleId(dto.getInfectionSampleId()))) {
//                throw new IllegalStateException(String.format("样本号 [%s] 样本不存在", dto.getSampleNo()));
//            }
//
//            final List<SampleReportItemDto> reportItemDtos = sampleReportItemService.selectBySampleId(dto.getInfectionSampleId());
//
//            final Map<Long, SampleResultDto> resultMap = sampleResultService.selectBySampleId(dto.getInfectionSampleId()).stream().collect(Collectors.toMap(SampleResultDto::getReportItemId,v->v,(a,b)->a));
//
//            for (SampleReportItemDto itemDto : reportItemDtos) {
//
//                final SampleResultDto result = resultMap.get(itemDto.getSampleReportItemId());
//
//                final InstrumentReportItemDto reportItemDto = instrumentReportItemService
//                        .selectByInstrumentIdAndReportItemCode(dto.getInstrumentId(),itemDto.getReportItemCode());
//
//                if (Objects.isNull(result) || Objects.isNull(reportItemDto) ||
//                        StringUtils.isBlank(result.getResult()) &&
//                                Objects.equals(reportItemDto.getIsResultNull(), YesOrNoEnum.NO.getCode())) {
//                    throw new IllegalStateException(String.format("样本号 [%s] 报告项目 [%s] 结果值为空", dto.getSampleNo(), itemDto.getReportItemName()));
//                }
//
//                if (StringUtils.isBlank(result.getResult()) && Objects.equals(reportItemDto.getIsResultZero(), YesOrNoEnum.NO.getCode())) {
//                    throw new IllegalStateException(String.format("样本号 [%s] 报告项目 [%s] 结果值为零", dto.getSampleNo(), itemDto.getReportItemName()));
//                }
//
//            }
//
//        }

        return CONTINUE_PROCESSING;
    }

}
