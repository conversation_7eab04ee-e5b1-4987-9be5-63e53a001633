package com.labway.lims.infection.service.chain.result;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * 记录日志
 *
 * <AUTHOR>
 * @since 2023/3/30 16:23
 */
@Slf4j
@Component
public class SaveSampleFlowCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {

        // 指定的来源才记录流水
        final List<SaveResultSourceEnum> saveResultSourceEnums = new LinkedList<>();

        saveResultSourceEnums.add(SaveResultSourceEnum.CODE);
        saveResultSourceEnums.add(SaveResultSourceEnum.DEFAULT_VALUE);
        saveResultSourceEnums.add(SaveResultSourceEnum.FRONT);

        final SaveResultContext context = SaveResultContext.from(c);

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();

        final String result = context.getResult();

        final String beforeResult = context.getBeforeResult();

        if (BooleanUtils.isFalse(saveResultSourceEnums.contains(context.getSource()))) {
            return CONTINUE_PROCESSING;
        }

        final StringBuilder sbContent = new StringBuilder();

        if (StringUtils.isBlank(beforeResult)) {
            sbContent.append(String.format("报告项目 [%s] 新增结果 [%s]", instrumentReportItem.getReportItemName(), result));

        } else {
            sbContent.append(String.format("报告项目 [%s] 结果 从 [%s] 修改成 [%s]", instrumentReportItem.getReportItemName(), beforeResult, result));
        }

        if (Objects.equals(SaveResultSourceEnum.CODE, context.getSource())) {
            sbContent.append(" (计算公式)");
        } else if (Objects.equals(SaveResultSourceEnum.DEFAULT_VALUE, context.getSource())) {
            sbContent.append(" (默认值)");
        }

        final SampleFlowDto sampleFlowDto = new SampleFlowDto();
        sampleFlowDto.setSampleFlowId(snowflakeService.genId());
        sampleFlowDto.setApplySampleId(context.getSample().getApplySampleId());
        sampleFlowDto.setBarcode(context.getSample().getBarcode());
        sampleFlowDto.setApplyId(context.getSample().getApplyId());
        sampleFlowDto.setOperateCode(BarcodeFlowEnum.RESULT_UPDATE.name());
        sampleFlowDto.setOperateName(BarcodeFlowEnum.RESULT_UPDATE.getDesc());

        final LoginUserHandler.User user = LoginUserHandler.get();

        sampleFlowDto.setOperator(user.getUsername());
        sampleFlowDto.setOperatorId(user.getUserId());
        sampleFlowDto.setContent(sbContent.toString());
        sampleFlowDto.setCreateDate(new Date());
        sampleFlowDto.setUpdateDate(new Date());
        sampleFlowDto.setUpdaterId(user.getUserId());
        sampleFlowDto.setUpdaterName(user.getUsername());
        sampleFlowDto.setOrgId(user.getOrgId());
        sampleFlowDto.setOrgName(user.getOrgName());
        sampleFlowDto.setIsDelete(YesOrNoEnum.NO.getCode());

        //TODO 发mq sampleFlowDto

        return CONTINUE_PROCESSING;
    }
}
