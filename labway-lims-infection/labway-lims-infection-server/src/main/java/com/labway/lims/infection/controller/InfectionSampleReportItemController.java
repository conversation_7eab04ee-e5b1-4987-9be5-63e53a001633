package com.labway.lims.infection.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleResultService;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.infection.vo.AddInfectionSampleReportItemVo;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 17:20
 */
@RestController
@RequestMapping("/infection-sample-report-item")
public class InfectionSampleReportItemController extends BaseController {
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @Resource
    private InfectionSampleService infectionSampleService;
    @Resource
    private InfectionSampleResultService infectionSampleResultService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;

    @DubboReference
    private SampleResultService sampleResultService;

    /**
     * 获取当前样本报告项目的所有参考范围
     */
    @GetMapping("/get-report-item-reference")
    public Object getReportItemReference(@RequestParam long infectionSampleId) {

        // 样本
        final InfectionSampleDto infectionSample = infectionSampleService.selectByInfectionSampleId(infectionSampleId);
        if (Objects.isNull(infectionSample)) {
            return Map.of();
        }

        final Long instrumentId = infectionSample.getInstrumentId();
        if (Objects.isNull(instrumentId)) {
            return Map.of();
        }

        // 报告项目
        final List<SampleReportItemDto> sampleReportItems = sampleReportItemService
                .selectBySampleId(infectionSample.getInfectionSampleId());
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            return Map.of();
        }
        final List<String> reportItemCodes = sampleReportItems.stream().map(SampleReportItemDto::getReportItemCode).collect(Collectors.toList());

        // 查询对应的报告项目参考范围
        List<InstrumentReportItemReferenceDto> instrumentReportItemReferences =
                instrumentReportItemReferenceService.selectByInstrumentIdAndReportItemCodes(instrumentId, reportItemCodes);

        if (CollectionUtils.isEmpty(instrumentReportItemReferences)) {
            return Map.of();
        }

        return instrumentReportItemReferences.stream()
                .collect(Collectors.groupingBy(
                        InstrumentReportItemReferenceDto::getReportItemCode,
                        Collectors.mapping(v ->
                                        // cn > en > cnEn
                                        StringUtils.defaultString(StringUtils.defaultString(v.getCnRefereValue(), StringUtils.defaultString(v.getEnRefereValue(), v.getCnEnRefereValue())))
                                , Collectors.toList())
                ));
    }

    @PostMapping("/reportItems")
    public Object reportItems(@RequestParam("infectionSampleId") Long infectionSampleId) {
        if (Objects.isNull(infectionSampleId)) {
            return Collections.emptyList();
        }
        return sampleReportItemService.selectBySampleId(infectionSampleId);
    }

    @PostMapping("/add")
    public Object addSampleReportItem(@RequestBody AddInfectionSampleReportItemVo vo) {

        final InfectionSampleDto sampleDto = infectionSampleService.selectByInfectionSampleId(vo.getInfectionSampleId());
        if (Objects.isNull(sampleDto)) {
            throw new IllegalStateException("样本不存在");
        }


        final SampleReportItemDto sampleReportItem = sampleReportItemService
                .selectBySampleReportItemId(vo.getInfectionSampleReportItemId(), sampleDto.getInfectionSampleId());
        if (Objects.isNull(sampleReportItem)) {
            throw new IllegalStateException("复制的报告项目不存在");
        }

        ApplySampleItemDto applySampleItem = applySampleItemService.selectByApplySampleIdAndTestItemId(sampleReportItem.getApplySampleId(), sampleReportItem.getTestItemId());
        if (Objects.isNull(applySampleItem)) {
            throw new IllegalStateException("检验项目不存在");
        }
        final Integer count = applySampleItem.getCount();
        // 获取指定报告项目的全部数量
        final long copySize = sampleReportItemService
                .selectBySampleId(sampleReportItem.getSampleId())
                .stream().filter(f -> Objects.equals(f.getReportItemId(), sampleReportItem.getReportItemId()))
                .count();
        if ((copySize) > count) {
            throw new IllegalStateException("复制的报告项目已达到上限");
        }

        final SampleReportItemDto copySampleReportItem = JSON.parseObject(JSON.toJSONString(sampleReportItem), SampleReportItemDto.class);
        final long sampleReportItemId = snowflakeService.genId();
        copySampleReportItem.setSampleReportItemId(sampleReportItemId);
        copySampleReportItem.setIsRetest(RetestStatusEnum.NORMAL.getCode());
        copySampleReportItem.setIsDelete(YesOrNoEnum.NO.getCode());

        SampleResultDto sampleResult = null;
        final Long infectionSampleResultId = vo.getInfectionSampleResultId();
        if (Objects.nonNull(infectionSampleResultId)) {
            sampleResult = sampleResultService.selectBySampleResultId(vo.getInfectionSampleResultId(),vo.getInfectionSampleId());
        }

        SampleResultDto copySampleResult = JSON.parseObject(JSON.toJSONString(sampleResult), SampleResultDto.class);
        if (Objects.isNull(copySampleResult)) {
            copySampleResult = new SampleResultDto();
            copySampleResult.setSampleId(sampleDto.getInfectionSampleId());
            copySampleResult.setApplySampleId(sampleDto.getApplySampleId());
            copySampleResult.setApplyId(sampleDto.getApplyId());
            copySampleResult.setTestItemId(applySampleItem.getTestItemId());
            copySampleResult.setTestItemCode(applySampleItem.getTestItemCode());
            copySampleResult.setTestItemName(applySampleItem.getTestItemName());
            copySampleResult.setReportItemCode(sampleReportItem.getReportItemCode());
            copySampleResult.setReportItemName(sampleReportItem.getReportItemName());
            copySampleResult.setType(StringUtils.EMPTY);
            copySampleResult.setResult(StringUtils.EMPTY);
            copySampleResult.setUnit(StringUtils.EMPTY);
            copySampleResult.setRange(StringUtils.EMPTY);
            copySampleResult.setStatus(ResultStatusEnum.NORMAL.getCode());
            copySampleResult.setInstrumentId(0L);
            copySampleResult.setInstrumentName(StringUtils.EMPTY);
            copySampleResult.setInstrumentResult(StringUtils.EMPTY);
            copySampleResult.setJudge(StringUtils.EMPTY);
        }
        copySampleResult.setReportItemId(sampleReportItemId);
        copySampleResult.setSampleResultId(snowflakeService.genId());

        sampleReportItemService.addSampleReportItem(copySampleReportItem);
        sampleResultService.addSampleResult(copySampleResult);

        return Map.of();
    }

    @PostMapping("/delete")
    public Object deleteReportItem(@RequestParam("infectionSampleReportItemId") Long infectionSampleReportItemId,
                                   @RequestParam("sampleId") Long sampleId) {

        final SampleReportItemDto reportItemDto = sampleReportItemService
                .selectBySampleReportItemId(infectionSampleReportItemId, sampleId);

        if (Objects.isNull(reportItemDto)) {
            throw new IllegalArgumentException("该项目已删除");
        }

        final List<SampleReportItemDto> reportItemDtos = sampleReportItemService.selectBySampleId(reportItemDto.getSampleId())
                .stream().filter(f -> Objects.equals(f.getReportItemCode(), reportItemDto.getReportItemCode()))
                .collect(Collectors.toList());

        if (reportItemDtos.size() <= 1) {
            throw new IllegalArgumentException("至少保留一个项目");
        }

        sampleReportItemService.deleteBySampleReportItemId(infectionSampleReportItemId, sampleId);

        return Collections.emptyMap();
    }
}
