package com.labway.lims.infection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.model.TbInfectionSample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 院感样本 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbInfectionSampleMapper extends BaseMapper<TbInfectionSample> {

    int updateByInfectionSampleIds(@Param("infectionSample") InfectionSampleDto infectionSample,
                                   @Param("infectionSampleIds") Collection<Long> infectionSampleIds);
}
