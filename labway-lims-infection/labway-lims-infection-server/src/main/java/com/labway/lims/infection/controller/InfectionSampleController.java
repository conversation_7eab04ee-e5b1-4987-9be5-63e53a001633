package com.labway.lims.infection.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.InspectionExportDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.InfectionInspectionDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultTipDto;
import com.labway.lims.base.api.enums.RelationalOperatorEnum;
import com.labway.lims.base.api.service.InstrumentReportItemResultTipService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.infection.api.dto.InfecSampleAuditDto;
import com.labway.lims.infection.api.dto.InfecSampleCancelAuditDto;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.dto.QueryInfectionSamplesDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.infection.vo.InfecRoutineSampleVo;
import com.labway.lims.infection.vo.InfecSampleAuditVo;
import com.labway.lims.infection.vo.InfecSampleCancelAuditVo;
import com.labway.lims.infection.vo.InfectionSampleResultListRequestVo;
import com.labway.lims.infection.vo.InfectionSampleVo;
import com.labway.lims.infection.vo.InfectionSamplesVo;
import com.labway.lims.infection.vo.QueryInfectionSamplesVo;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/11 16:55
 */
@RestController
@RequestMapping("/infection")
public class InfectionSampleController extends BaseController {

    private static final String DATE_PATTERN = "yyyy-MM-dd";
    @Resource
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private InstrumentReportItemResultTipService instrumentReportItemResultTipService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SampleResultService sampleResultService;
    /**
     * 院感样本结果查询
     */
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @PostMapping("/samples")
    public Object samples(@RequestBody QueryInfectionSamplesVo vo) throws Exception {

        final QueryInfectionSamplesDto dto = JSON.parseObject(JSON.toJSONString(vo), QueryInfectionSamplesDto.class);
        final List<InfectionSampleDto> samples = infectionSampleService.selectByTestDate(dto);

        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }

        // 申请单样本id
        final Set<Long> applySampleIds =
            samples.stream().map(InfectionSampleDto::getApplySampleId).collect(Collectors.toSet());

        // 申请单id
        final Set<Long> applyIds = samples.stream().map(InfectionSampleDto::getApplyId).collect(Collectors.toSet());

        // 多线程查
        final ExecutorService pool = threadPoolConfig.getPool();

        // 查样本
        final CompletableFuture<Map<Long, ApplySampleDto>> applySampleFuture = CompletableFuture.supplyAsync(() -> {
            if (CollectionUtils.isEmpty(applySampleIds)) {
                return Map.of();
            }
            return applySampleService.selectByApplySampleIds(applySampleIds).stream()
                .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity(), (a, b) -> a));
        }, pool);

        // 查申请单
        final CompletableFuture<Map<Long, ApplyDto>> applyFuture = CompletableFuture.supplyAsync(() -> {
            if (CollectionUtils.isEmpty(applyIds)) {
                return Map.of();
            }
            return applyService.selectByApplyIdsAsMap(applyIds);
        }, pool);

        // 查申请单项目
        final CompletableFuture<Map<Long, List<ApplySampleItemDto>>> applySampleItemFuture =
            CompletableFuture.supplyAsync(() -> {
                if (CollectionUtils.isEmpty(applySampleIds)) {
                    return Map.of();
                }
                return applySampleItemService.selectByApplySampleIds(applySampleIds).stream()
                    .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));
            }, pool);

        // 等 10 秒
        CompletableFuture.allOf(applyFuture, applySampleFuture, applySampleItemFuture).get(NumberUtils.INTEGER_ONE * 10,
            TimeUnit.SECONDS);

        final Map<Long, ApplyDto> applyMap = applyFuture.get();
        final Map<Long, ApplySampleDto> applySampleMap = applySampleFuture.get();
        final Map<Long, List<ApplySampleItemDto>> applySampleItemMap = applySampleItemFuture.get();

        List<Long> applySampleId = samples.stream().map(InfectionSampleDto::getApplySampleId).collect(Collectors.toList());
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleId);
        Map<Long, ApplySampleDto> applySampleDtoMap = applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));


        // 对比已出的结果和当前样本下的报告项目数量，来显示缺项信息
        final LinkedList<InfecRoutineSampleVo> vos = new LinkedList<>();

        for (InfectionSampleDto sample : samples) {
            final InfecRoutineSampleVo sampleVo =
                JSON.parseObject(JSON.toJSONString(sample), InfecRoutineSampleVo.class);

            final ApplyDto apply = applyMap.get(sample.getApplyId());

            if (Objects.nonNull(apply)) {
                sampleVo.setApplyType(apply.getApplyTypeName());
                sampleVo.setUrgent(apply.getUrgent());
                sampleVo.setPatientName(apply.getPatientName());
                sampleVo.setEnterDate(apply.getCreateDate());

            }

            final List<ApplySampleItemDto> sampleItems = applySampleItemMap.get(sample.getApplySampleId());
            if (CollectionUtils.isNotEmpty(sampleItems)) {
                sampleVo.setCount(sampleItems.iterator().next().getCount());
            }

            final ApplySampleDto applySample = applySampleMap.get(sample.getApplySampleId());
            if (Objects.nonNull(applySample)) {
                // 终止检验的过滤
                if (Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
                    continue;
                }

                // 查询未审 ,状态不是未审过滤
                if (Objects.equals(vo.getSampleStatus(), SampleStatusEnum.NOT_AUDIT.name())
                    && (!Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode()))) {
                    continue;
                }

                // 查询已审，不等于已审的过滤
                if (Objects.equals(vo.getSampleStatus(), SampleStatusEnum.AUDIT.name())
                    && (!Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode()))) {
                    continue;
                }

                final List<String> testItems =
                    sampleItems.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList());
                sampleVo.setTestItemName(StringUtils.join(testItems, ","));
                sampleVo.setIsPrint(applySample.getIsPrint());
                sampleVo.setUrgent(applySample.getUrgent());
                sampleVo.setStatus(applySample.getStatus());
                // 免疫二次分拣标记 # 1.1.3.7
                sampleVo.setIsImmunityTwoPick(applySample.getIsImmunityTwoPick());

            }
            ApplySampleDto applySampleDto = applySampleDtoMap.get(sample.getApplySampleId());
            if(applySampleDto !=null){
                sampleVo.setTesterId(applySampleDto.getTesterId());
                sampleVo.setTesterName(applySampleDto.getTesterName());
            }

            vos.add(sampleVo);
        }

        return vos;
    }

    @PostMapping("/update")
    public Object update(@RequestBody InfectionSampleVo vo) {
        if (Objects.isNull(vo.getInfectionSampleId())) {
            throw new IllegalArgumentException("院感样本不能为空");
        }
        final InfectionSampleDto dto = JSON.parseObject(JSON.toJSONString(vo), InfectionSampleDto.class);
        infectionSampleService.updateByInfectionSampleId(dto);
        return Map.of("id", vo.getInfectionSampleId());
    }

    @PostMapping("/auditSamples")
    public Object audit(@RequestBody InfecSampleAuditVo vo) {
        if (CollectionUtils.isEmpty(vo.getInfectionSampleIds())) {
            throw new IllegalArgumentException("样本为空");
        }
        final InfecSampleAuditDto dto = JSON.parseObject(JSON.toJSONString(vo), InfecSampleAuditDto.class);
        infectionSampleService.auditSamplesChain(dto);
        return Collections.emptyMap();
    }

    @PostMapping("/cancelAuditSample")
    public Object cancelAuditSample(@RequestBody InfecSampleCancelAuditVo vo) {
        Objects.requireNonNull(vo, "sampleAuditVo 不能为空");

        if (Objects.isNull(vo.getInfectionSampleId())) {
            throw new IllegalArgumentException("样本为空");
        }
        final InfecSampleCancelAuditDto dto = JSON.parseObject(JSON.toJSONString(vo), InfecSampleCancelAuditDto.class);

        infectionSampleService.cancelAuditSample(dto);

        return Collections.emptyMap();
    }

    /**
     * 审核前提示
     */
    @PostMapping("/auditTips")
    public Object auditTips(@RequestBody InfecSampleAuditVo auditVo) {
        if (CollectionUtils.isEmpty(auditVo.getInfectionSampleIds())) {
            throw new IllegalArgumentException("所选样本为空");
        }
        List<InfectionSampleDto> samples =
            infectionSampleService.selectByInfectionSampleIds(auditVo.getInfectionSampleIds());
        // 查询所有样本报告项目，按照sampleId分组
        final Map<Long, List<SampleReportItemDto>> sampleItemMap =
            sampleReportItemService.selectBySampleIds(auditVo.getInfectionSampleIds()).stream()
                .collect(Collectors.groupingBy(SampleReportItemDto::getSampleId));
        // 查询所有样本结果，按照sampleId分组
        final Map<Long, List<SampleResultDto>> sampleResultMap =
            sampleResultService.selectBySampleIds(auditVo.getInfectionSampleIds()).stream()
                .collect(Collectors.groupingBy(SampleResultDto::getSampleId));

        // 查询样本仪器下的所有提示内容
        List<InstrumentReportItemResultTipDto> itemTips = instrumentReportItemResultTipService.selectByInstrumentIds(
            samples.stream().map(InfectionSampleDto::getInstrumentId).collect(Collectors.toSet()));
        // 查询所有仪器报告项目，按照reportItemCode分组
        List<InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService.selectByInstrumentIds(
            samples.stream().map(InfectionSampleDto::getInstrumentId).collect(Collectors.toSet()));

        StringBuilder sb = new StringBuilder();
        for (InfectionSampleDto dto : samples) {

            final List<SampleReportItemDto> reportItemDtos = sampleItemMap.get(dto.getInfectionSampleId());

            if (Objects.isNull(reportItemDtos)) {
                continue;
            }

            Map<String, SampleResultDto> resultMap = new HashMap<>();
            List<SampleResultDto> sampleResultDtos = sampleResultMap.get(dto.getInfectionSampleId());
            if (Objects.nonNull(sampleResultDtos)) {
                resultMap = sampleResultMap.get(dto.getInfectionSampleId()).stream()
                    .collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a));
            }

            for (SampleReportItemDto item : reportItemDtos) {

                final SampleResultDto result = resultMap.get(item.getReportItemCode());
                if (Objects.isNull(result)) {
                    throw new IllegalStateException(
                        String.format("样本号 [%s] 报告项目 [%s] 不存在", dto.getSampleNo(), item.getReportItemName()));
                }

                final InstrumentReportItemDto reportItemDto = instrumentReportItems.stream()
                    .filter(e -> Objects.equals(e.getReportItemCode(), item.getReportItemCode())).findFirst()
                    .orElse(null);
                if (Objects.isNull(reportItemDto)) {
                    throw new IllegalStateException(
                        String.format("样本号 [%s] 报告项目 [%s] 不存在", dto.getSampleNo(), item.getReportItemName()));
                }

                /* if ((Objects.isNull(result) || StringUtils.isBlank(result.getResult()))
                    && Objects.equals(reportItemDto.getIsResultNull(), YesOrNoEnum.NO.getCode())) {
                    throw new IllegalStateException(
                        String.format("样本号 [%s] 报告项目 [%s] 结果值为空", dto.getSampleNo(), item.getReportItemName()));
                }
                
                if (Objects.nonNull(result) && Objects.equals(result.getResult(), "0")
                    && Objects.equals(reportItemDto.getIsResultZero(), YesOrNoEnum.NO.getCode())) {
                    throw new IllegalStateException(
                        String.format("样本号 [%s] 报告项目 [%s] 结果值为零", dto.getSampleNo(), item.getReportItemName()));
                }*/

                BigDecimal decimal =
                        NumberUtils.isParsable(result.getResult()) ? new BigDecimal(result.getResult()) : null;

                // 如果为空 那么就不处理
                if (Objects.isNull(decimal)) {
                    continue;
                }

                final List<InstrumentReportItemResultTipDto> resultTipDtos = itemTips.stream()
                        .filter(e -> ArrayUtils.contains(e.getReportItemCode().split(","), reportItemDto.getReportItemCode())
                                || Objects.equals(e.getReportItemCode(), "0"))
                        .collect(Collectors.toList());

                for (InstrumentReportItemResultTipDto resultTipDto : resultTipDtos) {

                    if (Objects.equals(auditVo.getAuditStatus(), resultTipDto.getTipType())) {

                        this.curTips(dto.getSampleNo(), resultTipDto, decimal, sb);

                    }
                }
            }

        }

        return Map.of("tip", sb.toString());
    }

    private void curTips(String sampleNo, InstrumentReportItemResultTipDto resultTipDto, BigDecimal value, StringBuilder sb) {
        final BigDecimal tipsMaxDecimal = NumberUtils.isParsable(resultTipDto.getFormulaMaxValue()) ? new BigDecimal(resultTipDto.getFormulaMaxValue()) : null;
        if (Objects.isNull(tipsMaxDecimal)) {
            return;
        }
        final BigDecimal tipsMinDecimal = NumberUtils.isParsable(resultTipDto.getFormulaMinValue()) ? new BigDecimal(resultTipDto.getFormulaMinValue()) : null;

        RelationalOperatorEnum operatorMaxEnum = RelationalOperatorEnum.valueOfByOperator(resultTipDto.getFormulaMax());
        RelationalOperatorEnum operatorMinEnum = RelationalOperatorEnum.valueOfByOperator(resultTipDto.getFormulaMin());

        final String tipContent = resultTipDto.getTipContent();

        switch (operatorMaxEnum) {
            case EQ:
                // result = tips
                if (value.compareTo(tipsMaxDecimal) == NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            case LT:
                // result < tips
                if (value.compareTo(tipsMaxDecimal) < NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            case LE:
                // result <= tips
                if (value.compareTo(tipsMaxDecimal) <= NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            case GT:
                // result > tips
                if (value.compareTo(tipsMaxDecimal) > NumberUtils.INTEGER_ZERO) {
                    getMinTips(sampleNo, tipContent, operatorMinEnum, value, tipsMinDecimal, sb);
                }
                break;
            case GE:
                // result >= tips
                if (value.compareTo(tipsMaxDecimal) >= NumberUtils.INTEGER_ZERO) {
                    getMinTips(sampleNo, tipContent, operatorMinEnum, value, tipsMinDecimal, sb);
                }
                break;
        }
    }

    private void getMinTips(String sampleNo, String tipContent, RelationalOperatorEnum operatorMinEnum, BigDecimal value, BigDecimal tipsMinDecimal, StringBuilder sb) {
        switch (operatorMinEnum) {
            case LT:
                if (value.compareTo(tipsMinDecimal) < NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            case LE:
                if (value.compareTo(tipsMinDecimal) <= NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            default:
                sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
        }
    }

    /**
     * 查看 院感检验结果
     */
    @PostMapping("/samples-es")
    public Object getSamples(@RequestBody InfectionSampleResultListRequestVo queryVo) {

        // 转为es 查询条件
        SampleEsQuery query = getSampleEsQuery(queryVo);

        final List<InfectionInspectionDto> sampleEsModels = elasticSearchSampleService.selectSamples(query)
            // 院感样本
            .stream().filter(InfectionInspectionDto.class::isInstance)
            // 转成院感样本
            .map(e -> (InfectionInspectionDto)e).collect(Collectors.toList());
        return selectInfectionSamplesVo(sampleEsModels);
    }

    /**
     * 查询 检验结果列表 信息
     *
     */

    private List<InfectionSamplesVo> selectInfectionSamplesVo(List<InfectionInspectionDto> sampleEsModels) {
        final List<InfectionSamplesVo> targetList = Lists.newArrayListWithCapacity(sampleEsModels.size());

        for (InfectionInspectionDto model : sampleEsModels) {
            final InfectionSamplesVo sampleVo = new InfectionSamplesVo();
            sampleVo.setInfectionSampleId(model.getSampleId());
            sampleVo.setApplySampleId(model.getApplySampleId());
            sampleVo.setApplyId(model.getApplyId());
            sampleVo.setStatus(model.getSampleStatus());
            sampleVo.setBarcode(model.getBarcode());
            sampleVo.setSampleNo(model.getSampleNo());
            sampleVo.setGroupId(model.getGroupId());
            sampleVo.setGroupName(model.getGroupName());
            sampleVo.setInstrumentGroupId(model.getInstrumentGroupId());
            sampleVo.setInstrumentGroupName(model.getInstrumentGroupName());
            sampleVo.setTestDate(model.getTestDate());
            sampleVo.setCheckerName(model.getFinalCheckerName());
            sampleVo.setCheckerId(model.getFinalCheckerId());
            sampleVo.setCheckDate(model.getFinalCheckDate());
            sampleVo.setFinalCheckDate(model.getFinalCheckDate());
            sampleVo.setEnterDate(model.getApplyDate());
            sampleVo.setSampleRemark(model.getSampleRemark());
            sampleVo.setResultRemark(model.getResultRemark());
            sampleVo.setHspOrgId(model.getHspOrgId());
            sampleVo.setHspOrgName(model.getHspOrgName());
            sampleVo.setUrgent(model.getUrgent());
            if (CollectionUtils.isNotEmpty(model.getTestItems())) {
                sampleVo.setTestItemName(StringUtils.join(model.getTestItems().stream()
                    .map(BaseSampleEsModelDto.TestItem::getTestItemName).collect(Collectors.toList()), ","));
            }
            sampleVo.setPatientName(model.getPatientName());
            sampleVo.setIsPrint(model.getIsPrint());
            sampleVo.setApplyType(model.getApplyTypeName());
            sampleVo.setDiagnosis(model.getDiagnosis());
            sampleVo.setPatientVisitCard(model.getPatientVisitCard());
            targetList.add(sampleVo);
        }

        return targetList.stream()
            .sorted(Comparator
                .comparing(InfectionSamplesVo::getFinalCheckDate, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(InfectionSamplesVo::getInfectionSampleId))
            .collect(Collectors.toList());
    }

    /**
     * 获取结果查询 es 条件
     */
    private SampleEsQuery getSampleEsQuery(InfectionSampleResultListRequestVo queryVo) {
        LoginUserHandler.User user = LoginUserHandler.get();

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(NumberUtils.INTEGER_ONE);
        // 当前用户所属专业组
        query.setGroupIds(Collections.singleton(user.getGroupId()));
        // 已审
        query.setIsAudit(YesOrNoEnum.YES.getCode());

        // 只查询院感样本
        query.setItemTypes(Collections.singleton(ItemTypeEnum.INFECTION.name()));

        // 检验日期
        if (Objects.nonNull(queryVo.getTestDateStart()) && Objects.nonNull(queryVo.getTestDateEnd())) {
            query.setStartTestDate(queryVo.getTestDateStart());
            query.setEndTestDate(queryVo.getTestDateEnd());
        }

        // 审核日期
        if (Objects.nonNull(queryVo.getCheckDateStart()) && Objects.nonNull(queryVo.getCheckDateEnd())) {
            query.setStartFinalCheckDate(queryVo.getCheckDateStart());
            query.setEndFinalCheckDate(queryVo.getCheckDateEnd());
        }

        // 检验者ID
        if (Objects.nonNull(queryVo.getTesterId())) {
            query.setTesterId(queryVo.getTesterId());
        }

        // 审核人ID
        if (Objects.nonNull(queryVo.getCheckerId())) {
            query.setFinalCheckerIds(Collections.singleton(queryVo.getCheckerId()));
        }

        // 检验项目
        if (Objects.nonNull(queryVo.getTestItemId())) {
            query.setTestItemIds(Collections.singleton(queryVo.getTestItemId()));
        }

        // 送检机构
        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Collections.singleton(queryVo.getHspOrgId()));
        }

        // 姓名
        if (StringUtils.isNotBlank(queryVo.getPatientName())) {
            query.setPatientName(queryVo.getPatientName());
        }

        // 性别
        if (Objects.nonNull(queryVo.getPatientSex()) && !Objects.equals(queryVo.getPatientSex(), 0)) {
            query.setPatientSex(queryVo.getPatientSex());
        }
        // 门诊/住院号
        if (StringUtils.isNotBlank(queryVo.getPatientVisitCard())) {
            query.setPatientVisitCard(queryVo.getPatientVisitCard());
        }

        if (StringUtils.isNotBlank(queryVo.getApplyType())) {
            query.setApplyTypes(Collections.singleton(queryVo.getApplyType()));
        }

        if (StringUtils.isNotBlank(queryVo.getBarcode())) {
            query.setBarcodes(Collections.singleton(queryVo.getBarcode()));
        }
        return query;
    }


    /**
     * 院感检验结果导出
     */
    @PostMapping("/export")
    public Object export(@RequestBody InfectionSampleResultListRequestVo queryVo) {

        // 转为es 查询条件
        SampleEsQuery query = getSampleEsQuery(queryVo);

        final List<InfectionInspectionDto> sampleEsModels = elasticSearchSampleService.selectSamples(query)
                // 院感样本
                .stream().filter(InfectionInspectionDto.class::isInstance)
                // 转成院感样本
                .map(e -> (InfectionInspectionDto)e).collect(Collectors.toList());

        // es数据转换成需要导出的数据格式
        List<InspectionExportDto> inspectionExportDtos = convertInspectionExportDto(sampleEsModels);


        // 获取院感样本项目数据

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("院感检验结果导出.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(exportInspectionSamples(inspectionExportDtos));
    }



    //==================================================================================================================


    private List<InspectionExportDto> convertInspectionExportDto(List<InfectionInspectionDto> sampleEsModels) {

        final List<InspectionExportDto> targetList = new ArrayList<>();

        for (InfectionInspectionDto model : sampleEsModels) {
            final InspectionExportDto sampleVo = new InspectionExportDto();
            sampleVo.setInfectionSampleId(model.getSampleId());
            sampleVo.setApplyId(model.getApplyId());
            sampleVo.setApplySampleId(model.getApplySampleId());
            sampleVo.setApplyDate(model.getApplyDate());
            sampleVo.setBarcode(model.getBarcode());
            sampleVo.setDept(model.getDept());
            sampleVo.setPatientName(model.getPatientName());
            sampleVo.setResultRemark(model.getResultRemark());

            sampleVo.setHspOrgId(model.getHspOrgId());
            sampleVo.setHspOrgName(model.getHspOrgName());

            if (CollectionUtils.isEmpty(model.getReportItems())) {
                targetList.add(sampleVo);
                continue;
            }

            List<BaseSampleEsModelDto.TestItem> testItems = ObjectUtils.defaultIfNull(model.getTestItems(),Collections.emptyList());
            for (InfectionInspectionDto.InfectionReportItem reportItem : model.getReportItems()) {
                final InspectionExportDto tempSampleVo = new InspectionExportDto();
                BeanUtils.copyProperties(sampleVo, tempSampleVo);

                // 填充院感检验项目信息
                tempSampleVo.setTestItemCode(reportItem.getTestItemCode());
                tempSampleVo.setTestItemName(reportItem.getTestItemName());
                tempSampleVo.setResult(reportItem.getResult());
                tempSampleVo.setUnit(reportItem.getUnit());
                tempSampleVo.setRange(reportItem.getRange());
                tempSampleVo.setJudge(reportItem.getJudge());
                Optional<BaseSampleEsModelDto.TestItem> first = testItems.stream().filter(e -> Objects.equals(e.getTestItemId(), reportItem.getTestItemId())).findFirst();
                tempSampleVo.setCount(first.isPresent() ? first.get().getCount() : NumberUtils.INTEGER_ONE);
                targetList.add(tempSampleVo);
            }


            sampleVo.setTestItemName(StringUtils.join(model.getTestItems().stream()
                    .map(BaseSampleEsModelDto.TestItem::getTestItemName).collect(Collectors.toList()), ","));
        }

        // 导出数据根据申请时间 条码号 检验项目 升序排序
        return targetList.stream().sorted(Comparator.comparing(InspectionExportDto::getApplyDate)
                .thenComparing(InspectionExportDto::getBarcode)
                .thenComparing(InspectionExportDto::getTestItemCode)
                .thenComparing(InspectionExportDto::getResult))
                .collect(Collectors.toList());
    }

    private byte[] exportInspectionSamples(List<InspectionExportDto> inspectionExportDtos) {
        // 头的策略
        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("Calibri");
        headWriteFont.setFontHeightInPoints((short) 11);
        headCellStyle.setWriteFont(headWriteFont);

        // 内容策略
        WriteCellStyle contentCellStyle = new WriteCellStyle();
        contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentCellStyle.setBorderLeft(BorderStyle.THIN);
        contentCellStyle.setBorderTop(BorderStyle.THIN);
        contentCellStyle.setBorderRight(BorderStyle.THIN);
        contentCellStyle.setBorderBottom(BorderStyle.THIN);

        HorizontalCellStyleStrategy excelStyle = new HorizontalCellStyleStrategy(headCellStyle, contentCellStyle);

        ByteArrayOutputStream out = new ByteArrayOutputStream();

        try (ExcelWriter excelWriter =
                     EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle)
                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build()) {
            List<List<Object>> list0 = Lists.newArrayList();
            List<List<String>> header0 = Lists.newArrayList();

            // 设置表头
            List<String> headList = Lists.newArrayList("日期", "条码号", "科室", "名称", "检验项目编码",
                    "检验项目名称", "结果", "单位/参考值", "结果判定", "备注");

            for (String item : headList) {
                header0.add(List.of(item));
            }

            for (InspectionExportDto model : inspectionExportDtos) {
                // 设置 合并规则 放置数据
                fillExcelContent(list0, headList, model);
            }

            // 获取sheet对象
            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "微生物结果信息").head(header0).needHead(Boolean.TRUE).build();

            excelWriter.write(list0, sheet0);
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        }
        return out.toByteArray();
    }

    /**
     * 填充 excel 内容数据
     *
     * @param list0       excel 数据
     * @param headList    表头
     * @param contentData 内容来源
     */
    private void fillExcelContent(List<List<Object>> list0, List<String> headList, InspectionExportDto contentData) {
        List<Object> content = Lists.newArrayListWithCapacity(headList.size());

        // 日期
        if (Objects.isNull(contentData.getApplyDate())) {
            content.add(StringUtils.EMPTY);
        } else {
            content.add(DateFormatUtils.format(contentData.getApplyDate(), DATE_PATTERN));
        }

        // 条码号
        content.add(StringUtils.defaultString(contentData.getBarcode(),StringUtils.EMPTY));

        // 科室
        content.add(StringUtils.defaultString(contentData.getDept(),StringUtils.EMPTY));

        // 名称
        content.add(StringUtils.defaultString(contentData.getPatientName(),StringUtils.EMPTY));

        // 检验项目编码
        content.add(StringUtils.defaultString(contentData.getTestItemCode(),StringUtils.EMPTY));

        // 检验项目名称
        content.add(StringUtils.defaultString(contentData.getTestItemName(),StringUtils.EMPTY));

        // 结果
        content.add(StringUtils.defaultString(contentData.getResult(),StringUtils.EMPTY));

        // 单位/参考值
        content.add(StringUtils.defaultString(contentData.getUnit(),StringUtils.EMPTY)+"/"
                + StringUtils.defaultString(contentData.getRange(),StringUtils.EMPTY));
        // 结果判定
        content.add(StringUtils.defaultString(contentData.getJudge(),StringUtils.EMPTY));

        // 备注
        content.add(StringUtils.defaultString(contentData.getResultRemark(),StringUtils.EMPTY));

        list0.add(content);
    }

}
