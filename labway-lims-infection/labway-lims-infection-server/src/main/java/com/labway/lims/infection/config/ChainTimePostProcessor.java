package com.labway.lims.infection.config;

import cn.hutool.core.date.StopWatch;
import com.labway.lims.infection.service.chain.StopWatchContext;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.commons.chain.Command;
import org.springframework.aop.SpringProxy;
import org.springframework.aop.framework.ProxyFactoryBean;
import org.springframework.aop.support.NameMatchMethodPointcutAdvisor;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.SmartInstantiationAwareBeanPostProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Nonnull;
import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 代理责任链
 *
 * <AUTHOR>
 */
@Slf4j
@Component
class ChainTimePostProcessor implements SmartInstantiationAwareBeanPostProcessor, MethodInterceptor {

    /**
     * 如果存在循环依赖则跳过
     */
    private final Set<String> earlyBeanReference = Collections.newSetFromMap(new ConcurrentHashMap<>());

    @Override
    @Nonnull
    public Object getEarlyBeanReference(@Nonnull Object bean, @Nonnull String beanName) throws BeansException {
        // 解决存在循环依赖 Chain
        earlyBeanReference.add(beanName);
        return Objects.requireNonNull(proxyChain(bean));
    }

    @Override
    public Object postProcessBeforeInitialization(@Nonnull Object bean, @Nonnull String beanName)
            throws BeansException {

        // 跳过存在循环依赖的
        if (earlyBeanReference.contains(beanName)) {
            return bean;
        }

        return proxyChain(bean);
    }

    /**
     * 代理 Chain
     */
    private Object proxyChain(Object bean) {

        if (!(bean instanceof Command)) {
            return bean;
        }

        if (!bean.getClass().getPackage().getName().contains(StopWatchContext.class.getPackageName())) {
            return bean;
        }

        final ProxyFactoryBean proxy = new ProxyFactoryBean();

        final NameMatchMethodPointcutAdvisor advisor = new NameMatchMethodPointcutAdvisor();
        advisor.setMappedName("execute");
        advisor.setAdvice(this);

        proxy.addAdvisor(advisor);
        proxy.setAutodetectInterfaces(false);
        proxy.setTarget(bean);
        proxy.setInterfaces(SpringProxy.class);

        log.info("代理责任链条 {}", bean);

        return proxy.getObject();
    }

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {

        final StopWatchContext context = (StopWatchContext) invocation.getArguments()[0];

        final StopWatch watch = context.getWatch();
        if (Objects.nonNull(watch)) {

            if (watch.isRunning()) {
                watch.stop();
            }

            if (Objects.isNull(invocation.getThis())) {
                watch.start();
            } else {
                watch.start(invocation.getThis().getClass().getSimpleName());
            }
        }

        ReflectionUtils.makeAccessible(invocation.getMethod());

        try {
            return invocation.getMethod().invoke(invocation.getThis(), invocation.getArguments());
        } catch (InvocationTargetException e) {
            throw e.getTargetException();
        } finally {
            if (Objects.nonNull(watch) && watch.isRunning()) {
                watch.stop();
            }
        }

    }

}
