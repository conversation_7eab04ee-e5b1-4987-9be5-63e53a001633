package com.labway.lims.infection.service.chain.result;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.service.chain.StopWatchContext;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import javax.annotation.Nullable;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/3/30 13:38
 */
@Getter
@Setter
public class SaveResultContext extends StopWatchContext {

    /**
     * 申请单
     */
    static final String APPLY = "APPLY_" + IdUtil.objectId();

    /**
     * 样本
     */
    static final String SAMPLE = "SAMPLE_" + IdUtil.objectId();

    /**
     * 申请单样本
     */
    static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    /**
     * 样本检验项目
     */
    static final String SAMPLE_TEST_ITEM = "SAMPLE_TEST_ITEM_" + IdUtil.objectId();

    /**
     * 结果报告项目
     */
    static final String INSTRUMENT_REPORT_ITEM = "INSTRUMENT_REPORT_ITEM_" + IdUtil.objectId();

    static final String REPORT_ITEM = "REPORT_ITEM_" + IdUtil.objectId();

    /**
     * 仪器报告项目
     */
    static final String INSTRUMENT_REPORT_ITEMS = "INSTRUMENT_REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 当前样本下的报告项目
     */
    static final String SAMPLE_REPORT_ITEMS = "SAMPLE_REPORT_ITEMS_" + IdUtil.objectId();
    /**
     * 仪器参考值
     */
    static final String INSTRUMENT_REPORT_REFERENCE = "INSTRUMENT_REPORT_REFERENCE_" + IdUtil.objectId();

    /**
     * 结果转换
     */
    static final String RESULT_EXCHANGE = "RESULT_EXCHANGE_" + IdUtil.objectId();

    /**
     * 结果提示
     */
    static final String RESULT_JUDGE = "RESULT_JUDGE_" + IdUtil.objectId();

    /**
     * 结果是否异常
     */
    static final String RESULT_IS_EXCEPTION = "RESULT_IS_EXCEPTION_" + IdUtil.objectId();
    /**
     * 结果是否危机
     */
    static final String RESULT_IS_CRITICAL = "RESULT_IS_CRITICAL_" + IdUtil.objectId();
    /**
     * 修改前结果
     */
    static final String BEFORE_RESULT = "BEFORE_RESULT_" + IdUtil.objectId();
    /**
     * 当前结果
     */
    static final String CONTEXT_RESULTS = "CONTEXT_RESULTS_" + IdUtil.objectId();

    /**
     * sample_result
     */
    static final String SAMPLE_RESULT = "SAMPLE_RESULT_" + IdUtil.objectId();

    public static final String RESULT_PROVIDER = "RESULT_PROVIDER" + IdUtil.objectId();

    public static final String CHAIN_PROVIDER = "CHAIN_PROVIDER" + IdUtil.objectId();

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 院感样本ID
     */
    private Long infectionSampleId;

    /**
     * 报告项目Code
     */
    private String reportItemCode;

    /**
     * result
     */
    private String result;

    /**
     * 参考范围
     */
    private String range;

    /**
     * judge结果判定
     */
    private String judge;
    /**
     * instrument_result
     */
    private String instrumentResult;

    /**
     * 结果保存来源
     */
    private SaveResultSourceEnum source;

    /**
     * 检查时间
     */
    private Date testDate;

    /**
     * 样本报告项目id
     */
    private Long sampleReportItemId;

    /**
     * 单位
     */
    private String unit;

    @Override
    protected String getWatchName() {
        return "结果保存";
    }

    public SaveResultContext() {
        put(CONTEXT_RESULTS, new LinkedHashMap<>(0));
    }

    public static SaveResultContext from(Context context) {
        return (SaveResultContext) context;
    }

    public ApplyDto getApply() {
        return (ApplyDto) get(APPLY);
    }

    public InfectionSampleDto getSample() {
        return (InfectionSampleDto) get(SAMPLE);
    }

    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }

    @SuppressWarnings("unchecked")
    public ApplySampleItemDto getSampleTestItem() {
        return (ApplySampleItemDto) get(SAMPLE_TEST_ITEM);
    }

    public SampleResultDto getSampleResult() {
        return (SampleResultDto) get(SAMPLE_RESULT);
    }

    public InstrumentReportItemDto getInstrumentReportItem() {
        return (InstrumentReportItemDto) get(INSTRUMENT_REPORT_ITEM);
    }

    public ReportItemDto getReportItem() {
        return (ReportItemDto) get(REPORT_ITEM);
    }

    @SuppressWarnings("unchecked")
    public List<InstrumentReportItemDto> getInstrumentReportItems() {
        return (List<InstrumentReportItemDto>) get(INSTRUMENT_REPORT_ITEMS);
    }

    @SuppressWarnings("unchecked")
    public List<SampleReportItemDto> getSampleReportItems() {
        return (List<SampleReportItemDto>) get(SAMPLE_REPORT_ITEMS);
    }

    @Nullable
    public InstrumentReportItemReferenceDto getInstrumentReportItemReference() {
        return (InstrumentReportItemReferenceDto) get(INSTRUMENT_REPORT_REFERENCE);
    }

    @SuppressWarnings("unchecked")
    public List<InstrumentReportItemResultExchangeDto> getResultExchange() {
        return (List<InstrumentReportItemResultExchangeDto>) get(RESULT_EXCHANGE);
    }

    @Nullable
    public String getResultJudge() {
        return (String) get(RESULT_JUDGE);
    }

    public boolean isException() {
        return Objects.nonNull(get(RESULT_IS_EXCEPTION)) && (boolean) get(RESULT_IS_EXCEPTION);
    }

    public boolean isCritical() {
        return Objects.nonNull(get(RESULT_IS_CRITICAL)) && (boolean) get(RESULT_IS_CRITICAL);
    }

    /**
     * 获取之前的结果
     *
     * @return null 则没有
     */
    @Nullable
    public String getBeforeResult() {
        final Object value = get(BEFORE_RESULT);
        if (Objects.isNull(value)) {
            return null;
        }
        return String.valueOf(value);
    }


    /**
     * 当前上下文涉及到的报告项目结果，也就是说在整个流程中，如果整个报告项目涉及到新增、修改结果 那么都会在这里
     *
     * @return {sampleId:结果}
     */
    @SuppressWarnings("unchecked")
    public Map<Long, String> getContextResults() {
        return (Map<Long, String>) get(CONTEXT_RESULTS);
    }

}
