package com.labway.lims.infection.controller;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.dto.QueryInfectionSamplesDto;
import com.labway.lims.infection.api.service.InfectionSampleService;

@SpringBootTest
class InfectionSampleResultControllerTest {
    @Resource
    private InfectionSampleService infectionSampleService;

    @Test
    void test() {
        QueryInfectionSamplesDto dto = new QueryInfectionSamplesDto();
        List<InfectionSampleDto> sampleDtoList = infectionSampleService.selectByTestDate(dto).stream()
            .filter(obj -> !Objects.equals(obj.getCheckerId(), 0L)).collect(Collectors.toList());
        for (InfectionSampleDto infectionSampleDto : sampleDtoList) {
            infectionSampleService.rebuildReport(infectionSampleDto.getApplySampleId());
        }
    }

    @Test
    void test1() {
            infectionSampleService.rebuildReport(178834964772209868L);
    }
}