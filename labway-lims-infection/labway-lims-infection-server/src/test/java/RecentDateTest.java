import com.labway.lims.infection.LabwayLimsInfectionApplication;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/5/19 14:36
 */
@SpringBootTest(classes = LabwayLimsInfectionApplication.class)
public class RecentDateTest {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    // Redis Sorted Set的key值
    private static final String REDIS_KEY = "historical-data";

    // 存储历史数据的个数
    private static final long HISTORICAL_DATA_SIZE = 50;

    @Test
    void test() {

        // 设置当前时间为得分

        // 将数据添加到Sorted Set中
//        for (int i = 0; i < 60; i++) {
//            double score = Calendar.getInstance().getTimeInMillis();
//            stringRedisTemplate.opsForZSet().add(REDIS_KEY, "aa:" + i, score);
//        }
        double score = Calendar.getInstance().getTimeInMillis();
        stringRedisTemplate.opsForZSet().add(REDIS_KEY, "aa:00007845345", score);

        // 删除旧数据，只保留最新的HISTORICAL_DATA_SIZE条数据
        stringRedisTemplate.opsForZSet().removeRange(REDIS_KEY, 0, -1 - HISTORICAL_DATA_SIZE);
        // 调试输出历史数据

        final Set<String> strings = stringRedisTemplate.opsForZSet().reverseRange(REDIS_KEY, 0, -1);
        for (String v : strings) {
            System.out.println(v);
        }
        System.out.println("大小:" + strings.size());

    }

}
