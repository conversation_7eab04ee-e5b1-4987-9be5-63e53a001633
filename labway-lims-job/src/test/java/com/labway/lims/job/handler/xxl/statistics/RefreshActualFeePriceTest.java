package com.labway.lims.job.handler.xxl.statistics;

import com.labway.lims.apply.api.dto.QueryApplyPageDto;
import com.labway.lims.base.api.dto.BarcodeSettingDto;
import com.labway.lims.base.api.service.ref.IBarcodeSettingServiceRef;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.text.ParseException;

@SpringBootTest
@Slf4j
class RefreshActualFeePriceTest {

    @Resource
    private RefreshActualFeePrice refreshActualFeePrice;

    @DubboReference
    private IBarcodeSettingServiceRef barcodeSettingServiceRef;

    @Test
    void execute() throws ParseException {
//        QueryApplyPageDto pageDto = new QueryApplyPageDto();
//        pageDto.setCreateDateStart(DateUtils.parseDate("2023-09-25 00:00:00", "yyyy-MM-dd HH:mm:ss"));
//        pageDto.setCreateDateEnd(DateUtils.parseDate("2023-09-26 00:00:00", "yyyy-MM-dd HH:mm:ss"));
//        refreshActualFeePrice.execute(pageDto);

        BarcodeSettingDto settingDto = barcodeSettingServiceRef.selectByBarcode("123",1);
        log.info("结果--->",settingDto);
    }
}