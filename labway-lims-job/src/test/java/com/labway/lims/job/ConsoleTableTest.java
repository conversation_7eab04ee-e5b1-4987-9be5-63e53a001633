package com.labway.lims.job;

import cn.hutool.core.lang.ConsoleTable;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Test;

public class ConsoleTableTest {
    @Test
    public void test() {
        final ConsoleTable consoleTable = new ConsoleTable();
        consoleTable.setSBCMode(false);
        consoleTable.addHeader("姓名", "性别", "年龄");

        for (int i = 0; i < RandomUtils.nextInt(5, 20); i++) {
            consoleTable.addBody(RandomStringUtils.randomAlphabetic(5),
                    "男", RandomUtils.nextInt(1, 20) + "岁");
        }


        consoleTable.print();
    }
}
