package com.labway.lims.job.dto;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 同步无效员工 job 入参
 * 
 * <AUTHOR>
 * @since 2023/6/12 9:25
 */
@Getter
@Setter
public class SyncFailureUserDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 失效开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime failureStartTime;
    /**
     * 失效结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime failureEndTime;

}
