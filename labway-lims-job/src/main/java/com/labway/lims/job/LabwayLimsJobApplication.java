package com.labway.lims.job;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * xxl-job
 */
@SpringBootApplication
@EnableDubbo
@EnableDiscoveryClient
public class LabwayLimsJobApplication {
    public static void main(String[] args) {
        SpringApplication.run(LabwayLimsJobApplication.class, args);
    }

}
