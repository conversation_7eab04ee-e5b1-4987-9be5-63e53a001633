package com.labway.lims.job.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 数据
 */
@Getter
@Setter
public class DebeziumPayload implements Serializable {

    public static final String PAYLOAD = "payload";

    /**
     * 执行时间
     */
    private long tsMs;

    /**
     * 操作类型
     */
    private OP op;

    /**
     * 修改后的数据
     */
    private JSONObject after;

    /**
     * 之前的数据
     */
    private JSONObject before;

    /**
     * 源自
     */
    private DebeziumPayloadSource source;


    @Getter
    @Setter
    public static class DebeziumPayloadSource implements Serializable {
        /**
         * Debezium 版本
         */
        private String version;

        /**
         * 连接器
         */
        private String connector;

        /**
         * 对接程序名字
         */
        private String name;

        /**
         * 执行时间
         */
        private long tsMs;

        /**
         * 数据库
         */
        private String db;

        /**
         * schema
         */
        private String schema;

        /**
         * 表名
         */
        private String table;

        /**
         * 事务ID
         */
        private long txId;
    }

    /**
     * 操作
     */
    public enum OP {
        /**
         * 新增
         */
        c,
        /**
         * 修改
         */
        u,
        /**
         * 删除
         */
        d
    }
}
