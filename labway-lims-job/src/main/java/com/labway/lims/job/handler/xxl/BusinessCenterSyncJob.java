package com.labway.lims.job.handler.xxl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.labway.business.center.compare.dto.QueryAdditionalSampleInfoDTO;
import com.labway.business.center.compare.request.QueryAdditionalSampleInfoRequest;
import com.labway.business.center.compare.service.OutApplyInfoService;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyLogisticsDto;
import com.labway.lims.apply.api.dto.ApplyLogisticsSampleDto;
import com.labway.lims.apply.api.dto.ApplyLogisticsSampleItemDto;
import com.labway.lims.apply.api.service.ApplyLogisticsSampleItemService;
import com.labway.lims.apply.api.service.ApplyLogisticsSampleService;
import com.labway.lims.apply.api.service.ApplyLogisticsService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.TestItemService;
import com.swak.frame.dto.Response;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 同步业务中台物流检验项目
 */
@Slf4j
@Component
public class BusinessCenterSyncJob {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private ApplyLogisticsService applyLogisticsService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @Resource
    private OutApplyInfoService outApplyInfoService;
    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private ApplyLogisticsSampleService applyLogisticsSampleService;

    @DubboReference
    private ApplyLogisticsSampleItemService applyLogisticsSampleItemService;
    @DubboReference
    private SnowflakeService snowflakeService;

    /**
     * 同步
     */
    @XxlJob("SyncLogisticsSample")
    public void syncLogisticsSample(String param) {
        final JSONObject json = JSON.parseObject(StringUtils.defaultString(param, "{}"));

        // 同步天数
        final Integer day = ObjectUtils.defaultIfNull(json.getInteger("day"), 7);

        String syncLock = redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":syncLogisticsSampleJob:syncLock";
        try {
            if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(syncLock, StringUtils.EMPTY, 10, TimeUnit.MINUTES))) {
                throw new IllegalArgumentException("获取同步锁失败 , 跳过本次");
            }
            Date now = new Date();

            // 同步近 day 天的样本
            final Set<Long> applyLogisticsSampleIds =
                    applyLogisticsService.selectUnSyncInfoSampleByDate(DateUtils.addDays(now, -day), now);

            log.info("开始本次同步 共 [{}] 条样本需要同步信息", applyLogisticsSampleIds.size());

            if (CollectionUtils.isEmpty(applyLogisticsSampleIds)) {
                log.info("没有需要同步的物流样本信息");
                return;
            }

            for (Long applyLogisticsSampleId : applyLogisticsSampleIds) {


                try {
                    log.info("物流条码 [{}] 开始同步检验项目", applyLogisticsSampleId);

                    SpringUtil.getBean(BusinessCenterSyncJob.class)
                            .syncSample(applyLogisticsSampleId);

                } catch (Exception e) {
                    log.error("条码 [{}] 因[{}] 跳过同步 ", applyLogisticsSampleId, e.getMessage(), e);
                    continue;
                }

                log.info("条码 [{}] 同步检验项目结束", applyLogisticsSampleId);
            }

        } finally {
            stringRedisTemplate.delete(syncLock);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void syncSample(Long applyLogisticsSampleId) {

        final ApplyLogisticsSampleDto applyLogisticsSample = applyLogisticsSampleService.selectById(applyLogisticsSampleId);
        if (Objects.isNull(applyLogisticsSample)) {
            throw new IllegalStateException(String.format("物流样本 %s 不存在", applyLogisticsSampleId));
        }
        String syncFlag = redisPrefix.getBasePrefix() + "syncLogisticsSampleJob:syncFlag:" + applyLogisticsSample.getBarcode();
        if (BooleanUtils.isTrue(stringRedisTemplate.hasKey(syncFlag))
                || BooleanUtils.isNotTrue(Objects.equals(applyLogisticsSample.getStatus(), -1))) {
            throw new IllegalStateException("已同步检验项目");
        }

        final ApplyLogisticsDto applyLogistics = applyLogisticsService.selectByApplyLogisticsId(applyLogisticsSample.getApplyLogisticsId());
        if (Objects.isNull(applyLogistics)) {
            throw new IllegalStateException("物流单不存在");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(applyLogistics.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException(String.format("对应的机构 [%s] 不存在", applyLogistics.getHspOrgName()));
        }

        final String barcode = applyLogisticsSample.getBarcode();
        final ArrayList<String> barcodes = new ArrayList<>();
        barcodes.add(barcode);
        final QueryAdditionalSampleInfoRequest request =
                new QueryAdditionalSampleInfoRequest(hspOrganization.getHspOrgCode(), barcodes);

        final Response<List<QueryAdditionalSampleInfoDTO>> response = outApplyInfoService.queryAdditionalSampleInfo(request);

        log.info("条码 [{}] 请求业务中台接口 参数：[{}] 反参：[{}]", applyLogisticsSample.getBarcode(), JSON.toJSONString(request), JSON.toJSONString(response));

        if (Objects.isNull(response)) {
            throw new IllegalStateException("业务中台返回信息为空");
        }

        if (!Objects.equals(response.getCode(), 0)) {
            throw new IllegalStateException(response.getMsg());
        }

        final List<QueryAdditionalSampleInfoDTO> data = response.getData();
        if (CollectionUtils.isEmpty(data)) {
            throw new IllegalStateException("业务中台数据返回为空");
        }

        final QueryAdditionalSampleInfoDTO next = data.iterator().next();
        final List<QueryAdditionalSampleInfoDTO.QueryAdditionalSampleItemInfoDTO> items = ObjectUtils.defaultIfNull(next.getItems(), List.of());

        final Set<String> itemCodes = items.stream().map(QueryAdditionalSampleInfoDTO.QueryAdditionalSampleItemInfoDTO::getTestItemCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(itemCodes)) {
            throw new IllegalStateException("业务中台数据返回检验项目为空");
        }

        final List<TestItemDto> testItems = testItemService
                .selectByTestItemCodes(itemCodes, hspOrganization.getOrgId());

        if (testItems.size() != itemCodes.size()) {
            final Set<String> testItemCodes = testItems.stream().map(TestItemDto::getTestItemCode).collect(Collectors.toSet());
            itemCodes.removeIf(testItemCodes::contains);
            log.info("条码 [{}] 物流项目编码 [{}] 在LIMS中并未配置这些项目", applyLogisticsSample.getBarcode(), String.join(",", itemCodes));
        }

        final List<ApplyLogisticsSampleItemDto> sampleItems = testItems.stream().map(m -> {
            final ApplyLogisticsSampleItemDto item = new ApplyLogisticsSampleItemDto();
            item.setApplyLogisticsApplyItemId(snowflakeService.genId());
            item.setApplyLogisticsSampleId(applyLogisticsSample.getApplyLogisticsSampleId());
            item.setApplyLogisticsId(applyLogisticsSample.getApplyLogisticsId());
            item.setTestItemId(m.getTestItemId());
            item.setTestItemCode(m.getTestItemCode());
            item.setTestItemName(m.getTestItemName());
            item.setCreateDate(new Date());
            item.setUpdateDate(new Date());
            item.setCreatorId(applyLogisticsSample.getCreatorId());
            item.setCreatorName(applyLogisticsSample.getCreatorName());
            item.setUpdaterId(applyLogisticsSample.getUpdaterId());
            item.setUpdaterName(applyLogisticsSample.getUpdaterName());
            item.setIsDelete(applyLogisticsSample.getIsDelete());
            return item;
        }).collect(Collectors.toList());

        // 保存物流样本检验项目
        applyLogisticsSampleItemService.addBatch(sampleItems);

        // 修改物流样本状态到待补录
        applyLogisticsSampleService.updateStatusById(applyLogisticsSample.getApplyLogisticsSampleId(), ApplyStatusEnum.PENDING_RECORDING.getCode());
        log.info("条码 [{}] 状态修改为待补录", applyLogisticsSample.getBarcode());

        if (StringUtils.isBlank(applyLogistics.getApplyImage()) || StringUtils.isBlank(applyLogistics.getMasterBarcode())) {
            // 修改物流单信息
            applyLogistics.setApplyImage(StringUtils.defaultString(next.getImageUrl()));
            applyLogistics.setLogisticsUserId(0L);
            applyLogistics.setLogisticsUserName(StringUtils.defaultString(next.getStaffName()));
            applyLogistics.setMasterBarcode(StringUtils.defaultString(next.getDeliveryCode()));
            applyLogistics.setReceiveDate(ObjectUtils.defaultIfNull(next.getTakeSampleTime(), DefaultDateEnum.DEFAULT_DATE.getDate()));
            applyLogisticsService.updateById(applyLogistics);
            log.info("条码 [{}]，修改了物流单信息", applyLogisticsSample.getBarcode());
        }

        // 标记已同步
        stringRedisTemplate.opsForValue().set(syncFlag, StringUtils.EMPTY, 1, TimeUnit.DAYS);
    }
}
