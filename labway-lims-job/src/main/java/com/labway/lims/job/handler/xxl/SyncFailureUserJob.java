package com.labway.lims.job.handler.xxl;

import cn.hutool.json.JSONUtil;
import com.labway.business.center.mdm.api.user.param.UserVo;
import com.labway.business.center.mdm.api.user.request.UserFailureListRequest;
import com.labway.business.center.mdm.api.user.service.UserServerBaseService;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.job.dto.SyncFailureUserDto;
import com.swak.frame.dto.Response;
import com.swak.frame.enums.BasicErrCode;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步无效员工 job
 * 
 * <AUTHOR>
 * @since 2023/6/12 9:18
 */

@Slf4j
@Component
public class SyncFailureUserJob {

    @Resource
    private UserServerBaseService userServerBaseService;
    @DubboReference
    private UserService userService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    /**
     * 同步无效员工
     */
    @XxlJob("syncFailureUser")
    public void syncFailureUser() {
        XxlJobHelper.log("syncFailureUser同步无效员工JOB开始 {}", DateUtil.formatDateTime(new Date()));
        log.info("syncFailureUser同步无效员工JOB开始 {}", DateUtil.formatDateTime(new Date()));
        String param = XxlJobHelper.getJobParam();
        SyncFailureUserDto jobParamDto = new SyncFailureUserDto();
        try {
            jobParamDto = JSONUtil.toBean(param, SyncFailureUserDto.class);
        } catch (Exception e) {
            XxlJobHelper.log("输入参数不规范{}", param);
        }

        boolean existDateFlag = false;
        String redisKey = redisPrefix.getBasePrefix() + "JOB:SYNC_FAILURE_USER_LAST_DATE";
        LocalDateTime now = LocalDateTime.now();
        if (Objects.nonNull(jobParamDto) && Objects.nonNull(jobParamDto.getFailureStartTime())
            && Objects.nonNull(jobParamDto.getFailureEndTime())) {
            // 传入时间
            existDateFlag = true;
        }
        UserFailureListRequest request = new UserFailureListRequest();
        if (existDateFlag) {
            request.setFailureStartTime(jobParamDto.getFailureStartTime());
            request.setFailureEndTime(jobParamDto.getFailureEndTime());
        } else {
            // 未传入时间 或时间不完整 失效
            String redisValue = stringRedisTemplate.opsForValue().get(redisKey);
            if (Objects.nonNull(redisValue)) {
                request.setFailureStartTime(
                    Instant.ofEpochMilli(Long.parseLong(redisValue)).atZone(ZoneOffset.ofHours(8)).toLocalDateTime());
                request.setFailureEndTime(now);
            }
        }
        XxlJobHelper.log("最终查询参数{}", JSONUtil.toJsonStr(request));
        Response<List<UserVo>> response;
        try {
            response = userServerBaseService.getUserFailureList(request);
        } catch (Exception e) {
            XxlJobHelper.log("获取主数据失效员工失败: {}", e);
            return;
        }
        if (!existDateFlag) {
            stringRedisTemplate.opsForValue().set(redisKey,
                String.valueOf(now.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()));
        }

        if (Objects.isNull(response)) {
            XxlJobHelper.log("主数据接口-查询失效用户信息返回结果为空");
            return;
        }
        if (!Objects.equals(response.getCode(), BasicErrCode.SUCCESS.getCode())) {
            XxlJobHelper.log(String.format("主数据接口-查询失效用户信息返回错误, 错误信息: [%s]", response.getMsg()));
            return;
        }

        List<UserVo> mdmUserInfo = ObjectUtils.defaultIfNull(response.getData(), Collections.emptyList());
        Set<String> nicknames = mdmUserInfo.stream().map(UserVo::getName).collect(Collectors.toSet());

        // 失效员工
        if (CollectionUtils.isEmpty(nicknames)) {
            return;
        }
        XxlJobHelper.log("需要失效的员工{}", nicknames);
        userService.deleteByNicknames(nicknames);
    }

}
