package com.labway.lims.job.handler.xxl.changzhou;

import com.alibaba.fastjson.JSON;
import com.labway.lims.outsourcing.api.dto.SyncChangzhouOutsourcingDto;
import com.labway.lims.outsourcing.api.service.SyncChangzhouOutsourcingService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <pre>
 * SyncChangzhouOursourcingResultJob
 * 常州外送同步JOB
 * </pre>
 *
 * <AUTHOR>
 * @since 2023/11/24 19:10
 */
@Slf4j
@Component
public class SyncChangzhouJob {

    @DubboReference
    private SyncChangzhouOutsourcingService syncChangzhouOutsourcingService;

    @XxlJob("SyncChangzhouOursourcingResultJob")
    public void SyncChangzhouOursourcingResultJob() {
        XxlJobHelper.log("同步拉取业务中台常州外送结果JOB开始 {}", DateUtil.formatDateTime(new Date()));
        log.info("同步拉取业务中台常州外送结果JOB开始 {}", DateUtil.formatDateTime(new Date()));

        try {
            String jobParam = XxlJobHelper.getJobParam();
            log.info("同步拉取业务中台常州外送结果JOB开始，参数：{}", jobParam);
            syncChangzhouOutsourcingService.syncChangzhouOutsourcing(JSON.parseObject(jobParam, SyncChangzhouOutsourcingDto.class));
        } catch (Exception e) {
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail(e.getMessage());
        }

        XxlJobHelper.handleSuccess();
    }

}