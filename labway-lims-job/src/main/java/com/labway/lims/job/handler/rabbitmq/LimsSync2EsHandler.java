package com.labway.lims.job.handler.rabbitmq;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.PhysicalSampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.BloodCultureInspectionDto;
import com.labway.lims.apply.api.dto.es.GeneticsInspectionDto;
import com.labway.lims.apply.api.dto.es.InfectionInspectionDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyGermDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyInspectionDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyMedicineDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyResultDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.api.dto.es.SpecialtyInspectionDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.PhysicalSampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.SampleImageService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleRemarkDto;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleResultDto;
import com.labway.lims.bloodculture.api.enums.BloodCultureSampleReportEnum;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleRemarkService;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleResultService;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import com.labway.lims.genetics.api.dto.GeneticsSampleDto;
import com.labway.lims.genetics.api.dto.GeneticsSampleResultDto;
import com.labway.lims.genetics.api.service.GeneticsSampleResultService;
import com.labway.lims.genetics.api.service.GeneticsSampleService;
import com.labway.lims.infection.api.dto.InfectionSampleDto;
import com.labway.lims.infection.api.service.InfectionSampleService;
import com.labway.lims.job.dto.LimsSync2EsMessage;
import com.labway.lims.microbiology.api.dto.MicrobiologyGermMedicineDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleGermDto;
import com.labway.lims.microbiology.api.dto.MicrobiologySampleResultDto;
import com.labway.lims.microbiology.api.service.MicrobiologyGermMedicineService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleGermService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleResultService;
import com.labway.lims.microbiology.api.service.MicrobiologySampleService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleResultDto;
import com.labway.lims.specialty.api.service.SpecialtySampleResultService;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.impl.ChainBase;
import org.apache.commons.chain.impl.ContextBase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 同步到 es
 */
@Slf4j
@Component
public class LimsSync2EsHandler implements InitializingBean {

    @Resource
    private DebeziumLimsHandler debeziumLimsHandler;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private InfectionSampleService infectionSampleService;
    @DubboReference
    private GeneticsSampleService geneticsSampleService;
    @DubboReference
    private GeneticsSampleResultService geneticsSampleResultService;
    @DubboReference
    private MicrobiologySampleService microbiologySampleService;
    @DubboReference
    private MicrobiologySampleResultService microbiologySampleResultService;
    @DubboReference
    private MicrobiologySampleGermService microbiologySampleGermService;
    @DubboReference
    private MicrobiologyGermMedicineService microbiologyGermMedicineService;
    @DubboReference
    private BloodCultureSampleService bloodCultureSampleService;
    @DubboReference
    private BloodCultureSampleRemarkService bloodCultureSampleRemarkService;
    @DubboReference
    private BloodCultureSampleResultService bloodCultureSampleResultService;

    @DubboReference
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private SpecialtySampleService specialtySampleService;
    @DubboReference
    private SpecialtySampleResultService specialtySampleResultService;
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private PhysicalSampleService physicalSampleService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SampleImageService sampleImageService;

    private SyncChain syncChain;

    /**
     * 监听数据库变动
     */
    @RabbitHandler
    @RabbitListener(queues = "${sync2es.sample.queue}")
    public void handle(String msg) throws Exception {
        final LimsSync2EsMessage message = JSON.parseObject(msg, LimsSync2EsMessage.class);

        if (Objects.equals(message.getApplySampleId(), 0L)) {
            return;
        }
        final String version =
                stringRedisTemplate.opsForValue().get(debeziumLimsHandler.getVersionKey(message.getApplySampleId()));

        // 比较版本
        if (!Objects.equals(message.getVersion(), version)) {
            log.warn("申请单样本 [{}] Version [{}] CurrentVersion [{}] 与 Redis 不一致，跳过同步", message.getApplySampleId(),
                    message.getVersion(), version);
            return;
        }

        try {

            final StopWatch watch = new StopWatch();
            watch.start();

            final SyncContext context = new SyncContext(message.getApplySampleId());

            syncChain.execute(context);

            // 如果申请单或者申请单样本被删除了，那么这里也从 es 删除
            if (Objects.isNull(context.applySample) || Objects.isNull(context.apply)) {
                // 删除 ES 样本
                elasticSearchSampleService.deleteByApplySampleId(message.getApplySampleId());

                watch.stop();

                log.info("同步申请单样本 [{}] Version [{}] 到 ES 成功，检测到申请单或样本被删除已从 ES 中删除 耗时 [{}ms]",
                        message.getApplySampleId(), version, watch.getTotal(TimeUnit.MILLISECONDS));

            } else {
                watch.stop();
                log.info("同步申请单样本 [{}] Version [{}] 到 ES 成功 耗时 [{}ms]", message.getApplySampleId(), version,
                        watch.getTotal(TimeUnit.MILLISECONDS));
            }

        } catch (Exception e) {
            log.error("同步申请单样本 [{}] Version [{}] 到 ES 失败，即将重回队列", message.getApplySampleId(), version, e);

            try {
                // 从回队列
                debeziumLimsHandler.publish(message.getApplySampleId(), IdUtil.simpleUUID(), message.getMsg());
            } catch (Exception ex) {
                log.error("同步申请单样本 [{}] Version [{}] 到 ES 失败，重回队列也失败了", message.getApplySampleId(), version, ex);

                // nck
                throw e;
            }

        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        syncChain = new SyncChain();
        syncChain.afterPropertiesSet();
    }

    /**
     * 上下文
     */
    private static class SyncContext extends ContextBase {

        static final String SAMPLE = "SAMPLE_" + IdUtil.objectId();

        private final long applySampleId;

        /**
         * 样本ID
         */
        private Long sampleId;

        /**
         * 申请单样本
         */
        private ApplySampleDto applySample;

        /**
         * 体检样本
         */
        private PhysicalSampleDto physicalSample;

        /**
         * 申请单
         */
        private ApplyDto apply;

        /**
         * 检验项目
         */
        private List<ApplySampleItemDto> applySampleItems;

        /**
         * 报告单
         */
        private List<SampleReportDto> sampleReports;

        /**
         * 报告项目
         */
        private List<SampleReportItemDto> sampleReportItems;

        /**
         * 条码环节
         */
        private List<SampleFlowDto> sampleFlowDtos;

        /**
         * es 对象。具体是哪个子类，由责任链条决定。
         */
        private BaseSampleEsModelDto sample;

        private SyncContext(long applySampleId) {
            this.applySampleId = applySampleId;
        }

        public static SyncContext from(Context c) {
            return (SyncContext) c;
        }
    }

    private class SyncChain extends ChainBase implements InitializingBean {

        @Override
        public void afterPropertiesSet() throws Exception {
            // 申请单样本
            addCommand(new ApplySampleCommand());

            // 申请单样本
            addCommand(new SampleCommand());

            // 申请单
            addCommand(new ApplyCommand());

            // 体检单位
            addCommand(new PhysicalCompanyCommand());

            // 检验项目
            addCommand(new ApplySampleItemCommand());

            // 报告项目
            addCommand(new SampleReportItemCommand());

            // 报告
            addCommand(new SampleReportCommand());

            // 条码环节
            addCommand(new SampleFlowCommand());

            // 初始化 es 对象
            addCommand(new InitEsSampleCommand());

            // 组装对象
            addCommand(new PackageEsSampleCommand());

            // 保存到 es
            addCommand(new SaveEsSampleCommand());

            // end
            addCommand(c -> PROCESSING_COMPLETE);
        }
    }

    /**
     * 申请单
     */
    private class ApplyCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {

            final SyncContext context = SyncContext.from(c);
            context.apply = applyService.selectByApplyId(context.applySample.getApplyId());

            if (Objects.isNull(context.apply)
                    || Objects.equals(context.apply.getIsDelete(), YesOrNoEnum.YES.getCode())) {
                context.apply = null;
                return PROCESSING_COMPLETE;
            }

            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 体检单位
     */
    private class PhysicalCompanyCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {

            final SyncContext context = SyncContext.from(c);
            final ApplySampleDto applySample = context.applySample;

            if (Objects.isNull(applySample)) {
                return PROCESSING_COMPLETE;
            }
            context.physicalSample =
                    physicalSampleService.selectByBarcode(applySample.getBarcode(), applySample.getOrgId());

            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 申请单样本
     */
    private class ApplySampleCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            final SyncContext context = SyncContext.from(c);
            context.applySample = applySampleService.selectByApplySampleId(context.applySampleId);

            if (Objects.isNull(context.applySample)
                    || Objects.equals(context.applySample.getIsDelete(), YesOrNoEnum.YES.getCode())) {
                context.applySample = null;
                return PROCESSING_COMPLETE;
            }

            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 样本
     */
    private class SampleCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            final SyncContext context = SyncContext.from(c);

            if (StringUtils.isBlank(context.applySample.getItemType())) {
                return CONTINUE_PROCESSING;
            }

            switch (ItemTypeEnum.getByName(context.applySample.getItemType())) {
                case GENETICS: {
                    final GeneticsSampleDto sample = geneticsSampleService.selectByApplySampleId(context.applySampleId);
                    if (Objects.nonNull(sample)) {
                        context.sampleId = sample.getGeneticsSampleId();
                        context.put(SyncContext.SAMPLE, sample);
                    }
                    break;
                }
                case ROUTINE: {
                    final SampleDto sample = sampleService.selectByApplySampleId(context.applySampleId);
                    if (Objects.nonNull(sample)) {
                        context.sampleId = sample.getSampleId();
                        context.put(SyncContext.SAMPLE, sample);
                    }
                    break;
                }
                case INFECTION: {
                    final InfectionSampleDto sample =
                            infectionSampleService.selectByApplySampleId(context.applySampleId);
                    if (Objects.nonNull(sample)) {
                        context.sampleId = sample.getInfectionSampleId();
                        context.put(SyncContext.SAMPLE, sample);
                    }
                    break;
                }
                case MICROBIOLOGY: {
                    final MicrobiologySampleDto sample =
                            microbiologySampleService.selectByApplySampleId(context.applySampleId);
                    if (Objects.nonNull(sample)) {
                        context.sampleId = sample.getMicrobiologySampleId();
                        context.put(SyncContext.SAMPLE, sample);
                    }
                    break;
                }
                case SPECIALTY: {
                    final SpecialtySampleDto sample =
                            specialtySampleService.selectByApplySampleId(context.applySampleId);
                    if (Objects.nonNull(sample)) {
                        context.sampleId = sample.getSpecialtySampleId();
                        context.put(SyncContext.SAMPLE, sample);
                    }
                    break;
                }
                case OUTSOURCING: {
                    final OutsourcingSampleDto sample =
                            outsourcingSampleService.selectByApplySampleId(context.applySampleId);
                    if (Objects.nonNull(sample)) {
                        context.sampleId = sample.getOutsourcingSampleId();
                        context.put(SyncContext.SAMPLE, sample);
                    }
                    break;
                }
                case BLOOD_CULTURE: {
                    final BloodCultureSampleDto sample =
                            bloodCultureSampleService.selectByApplySampleId(context.applySampleId);
                    if (Objects.nonNull(sample)) {
                        context.sampleId = sample.getBloodCultureSampleId();
                        context.put(SyncContext.SAMPLE, sample);
                    }
                    break;
                }
                default: {
                    break;
                }
            }

            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 申请单样本项目
     */
    private class ApplySampleItemCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            final SyncContext context = SyncContext.from(c);
            context.applySampleItems = applySampleItemService
                    .selectByApplySampleIdContainStopTest(Collections.singleton(context.applySampleId));
            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 报告项目
     */
    private class SampleReportItemCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            final SyncContext context = SyncContext.from(c);
            if (Objects.nonNull(context.sampleId)) {
                context.sampleReportItems = sampleReportItemService.selectBySampleId(context.sampleId);
            }
            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 报告
     */
    private class SampleReportCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            final SyncContext context = SyncContext.from(c);
            context.sampleReports = sampleReportService.selectByApplySampleIds(Collections.singleton(context.applySampleId));
            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 条码环节
     */
    private class SampleFlowCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            final SyncContext context = SyncContext.from(c);
            context.sampleFlowDtos = sampleFlowService.selectByApplySampleId(context.applySampleId);
            return CONTINUE_PROCESSING;
        }
    }

    private Map<String, List<InstrumentReportItemDto>> getInstrumentReportItemsMap(long instrumentGroupId) {
        return instrumentReportItemService.selectByInstrumentGroupId(instrumentGroupId).stream()
                .collect(Collectors.groupingBy(InstrumentReportItemDto::getReportItemCode));
    }

    /**
     * 组装 es 对象
     */
    private static class PackageEsSampleCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            final SyncContext context = SyncContext.from(c);

            final BaseSampleEsModelDto sample = context.sample;
            final PhysicalSampleDto physicalSample = context.physicalSample;
            if (Objects.nonNull(physicalSample)) {
                sample.setPhysicalCompanyId(physicalSample.getPhysicalCompanyId());
                sample.setPhysicalCompanyName(physicalSample.getPhysicalCompanyName());
            }
            sample.setApplySampleId(context.applySampleId);
            sample.setApplyId(context.apply.getApplyId());
            sample.setSignDate(context.apply.getSignDate());
            sample.setMasterBarcode(context.apply.getMasterBarcode());
            sample.setDept(context.apply.getDept());
            sample.setApplyUrgent(context.apply.getUrgent());
            sample.setUrgent(context.applySample.getUrgent());
            sample.setApplyStatus(context.apply.getStatus());
            sample.setPatientName(context.apply.getPatientName());
            sample.setPatientAge(context.apply.getPatientAge());
            sample.setPatientSubage(context.apply.getPatientSubage());
            sample.setPatientSubageUnit(context.apply.getPatientSubageUnit());
            sample.setPatientBirthday(context.apply.getPatientBirthday());
            sample.setPatientCard(context.apply.getPatientCard());
            sample.setPatientCardType(context.apply.getPatientCardType());
            sample.setPatientBed(context.apply.getPatientBed());
            sample.setPatientSex(context.apply.getPatientSex());
            sample.setPatientVisitCard(context.apply.getPatientVisitCard());
            sample.setPatientMobile(context.apply.getPatientMobile());
            sample.setPatientAddress(context.apply.getPatientAddress());
            sample.setApplyTypeCode(context.apply.getApplyTypeCode());
            sample.setApplyTypeName(context.apply.getApplyTypeName());
            sample.setSampleCount(context.apply.getSampleCount());
            sample.setSampleProperty(context.applySample.getSampleProperty());
            sample.setSamplePropertyCode(context.applySample.getSamplePropertyCode());
            sample.setDiagnosis(context.apply.getDiagnosis());
            sample.setSendDoctorName(context.apply.getSendDoctorName());
            sample.setSendDoctorCode(context.apply.getSendDoctorCode());
            sample.setApplyDate(context.apply.getApplyDate());
            sample.setSamplingDate(context.apply.getSamplingDate());
            sample.setRemark(context.apply.getRemark());
            sample.setSource(context.apply.getSource());
            sample.setSupplier(context.apply.getSupplier());
            sample.setHspOrgId(context.apply.getHspOrgId());
            sample.setHspOrgCode(context.apply.getHspOrgCode());
            sample.setHspOrgName(context.apply.getHspOrgName());
            sample.setOriginalOrgCode(context.apply.getOriginalOrgCode());
            sample.setOriginalOrgName(context.apply.getOriginalOrgName());
            sample.setCheckerId(context.apply.getCheckerId());
            sample.setCheckerName(context.apply.getCheckerName());
            sample.setCheckDate(context.apply.getCheckDate());


            sample.setIsDisabled(context.applySample.getIsDisabled());
            sample.setIsArchive(context.applySample.getIsArchive());
            sample.setOutBarcode(context.applySample.getOutBarcode());
            sample.setBarcode(context.applySample.getBarcode());
            sample.setTubeName(context.applySample.getTubeName());
            sample.setTubeCode(context.applySample.getTubeCode());
            sample.setSampleTypeCode(context.applySample.getSampleTypeCode());
            sample.setSampleTypeName(context.applySample.getSampleTypeName());
            sample.setGroupId(context.applySample.getGroupId());
            sample.setGroupName(context.applySample.getGroupName());
            sample.setSampleStatus(context.applySample.getStatus());
            sample.setRackId(context.applySample.getRackId());
            sample.setOnePickerId(context.applySample.getOnePickerId());
            sample.setOnePickerName(context.applySample.getOnePickerName());
            sample.setOnePickDate(context.applySample.getOnePickDate());
            sample.setIsOnePick(context.applySample.getIsOnePick());
            sample.setTwoPickerId(context.applySample.getTwoPickerId());
            sample.setTwoPickerName(context.applySample.getTwoPickerName());
            sample.setTwoPickDate(context.applySample.getTwoPickDate());
            sample.setIsTwoPick(context.applySample.getIsTwoPick());
            sample.setIsImmunityTwoPick(context.applySample.getIsImmunityTwoPick());
            sample.setIsSplitBlood(context.applySample.getIsSplitBlood());
            sample.setSplitterId(context.applySample.getSplitterId());
            sample.setSplitterName(context.applySample.getSplitterName());
            sample.setSplitDate(context.applySample.getSplitDate());
            sample.setSampleRemark(context.applySample.getSampleRemark());
            sample.setResultRemark(context.applySample.getResultRemark());
            sample.setItemType(context.applySample.getItemType());

			// ✨feat：【1.1.4】增加标本部位字段 此字段用于签收病理的样本时，落库  https://www.tapd.cn/59091617/prong/stories/view/1159091617001001756?from_iteration_id=1159091617001000242
            sample.setPatientPart(context.applySample.getPatientPart());

            sample.setTestItems(Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(context.applySampleItems)) {
                sample.setTestItems(context.applySampleItems.stream().map(e -> {
                    final BaseSampleEsModelDto.TestItem testItem = new BaseSampleEsModelDto.TestItem();
                    testItem.setApplySampleItemId(e.getApplySampleItemId());
                    testItem.setTestItemId(e.getTestItemId());
                    testItem.setTestItemCode(e.getTestItemCode());
                    testItem.setTestItemName(e.getTestItemName());
                    testItem.setOutTestItemId(e.getOutTestItemId());
                    testItem.setOutTestItemCode(e.getOutTestItemCode());
                    testItem.setOutTestItemName(e.getOutTestItemName());
                    testItem.setGroupId(e.getGroupId());
                    testItem.setGroupName(e.getGroupName());
                    testItem.setUrgent(e.getUrgent());
                    testItem.setCount(e.getCount());
                    testItem.setCreateDate(e.getCreateDate());
                    testItem.setPrice(e.getFeePrice());
                    testItem.setActualFeePrice(e.getActualFeePrice());
                    testItem.setItemSource(e.getItemSource());
                    testItem.setIsFree(e.getIsFree());
                    testItem.setStopStatus(e.getStopStatus());
                    testItem.setStopReasonCode(e.getStopReasonCode());
                    testItem.setStopReasonName(e.getStopReasonName());
                    testItem.setExportOrgId(e.getExportOrgId());
                    testItem.setExportOrgName(e.getExportOrgName());
                    testItem.setIsDisabled(Optional.ofNullable(e.getIsDisabled()).orElse(YesOrNoEnum.NO.getCode()));
                    return testItem;
                }).collect(Collectors.toList()));
            }

            sample.setReports(Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(context.sampleReports)) {
                sample.setReports(context.sampleReports.stream().map(e -> {
                    final BaseSampleEsModelDto.Report report = new BaseSampleEsModelDto.Report();
                    report.setUrl(e.getUrl());
                    report.setSampleReportId(e.getSampleReportId());
                    report.setFileType(e.getFileType());
                    report.setArtificialUpload(e.getIsUploadPdf());
                    return report;
                }).collect(Collectors.toList()));
            }

            sample.setOrgId(context.applySample.getOrgId());
            sample.setOrgName(context.applySample.getOrgName());
            sample.setCreatorId(context.applySample.getCreatorId());
            sample.setCreatorName(context.applySample.getCreatorName());
            sample.setCreateDate(context.apply.getCreateDate());
            sample.setUpdaterId(context.applySample.getUpdaterId());
            sample.setUpdaterName(context.applySample.getUpdaterName());
            sample.setUpdateDate(context.applySample.getUpdateDate());
            sample.setIsDelete(context.applySample.getIsDelete());
            sample.setIsPrint(context.applySample.getIsPrint());
            sample.setPrintDate(context.applySample.getPrintDate());
            sample.setPrinterName(context.applySample.getPrinterName());
            sample.setPrinterId(context.applySample.getPrinterId());
            sample.setTesterId(context.applySample.getTesterId());
            sample.setTesterName(context.applySample.getTesterName());

            // 当前条码环节
            if (CollectionUtils.isNotEmpty(context.sampleFlowDtos)) {
                SampleFlowDto last = context.sampleFlowDtos.listIterator(context.sampleFlowDtos.size()).previous();
                if (Objects.nonNull(last)) {
                    sample.setBarcodeFlow(last.getOperateName());
                }
            }

            sample.setSampleSource(context.applySample.getSampleSource());

            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 初始化 es 对象
     */
    private class InitEsSampleCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            final SyncContext context = SyncContext.from(c);

            switch (ItemTypeEnum.getByName(context.applySample.getItemType())) {
                case GENETICS: {

                    context.sample = new GeneticsInspectionDto();

                    GeneticsSampleDto geneticsSample = (GeneticsSampleDto) context.get(SyncContext.SAMPLE);
                    if (Objects.isNull(geneticsSample)) {
                        geneticsSample = geneticsSampleService.selectByApplySampleId(context.applySampleId);
                    }

                    if (Objects.isNull(geneticsSample)) {
                        break;
                    }

                    context.sample.setSampleId(geneticsSample.getGeneticsSampleId());
                    context.sample.setInstrumentGroupId(geneticsSample.getInstrumentGroupId());
                    context.sample.setInstrumentGroupName(geneticsSample.getInstrumentGroupName());
                    context.sample.setTestDate(geneticsSample.getTestDate());
                    // 样本号
                    context.sample.setSampleNo(geneticsSample.getSampleNo());
                    // 终审人信息补充
                    context.sample.setFinalCheckerId(geneticsSample.getTwoCheckerId());
                    context.sample.setFinalCheckerName(geneticsSample.getTwoCheckerName());
                    context.sample.setFinalCheckDate(geneticsSample.getTwoCheckDate());
                    context.sample.setInstrumentId(geneticsSample.getInstrumentId());
                    context.sample.setInstrumentName(geneticsSample.getInstrumentName());

                    final GeneticsSampleResultDto geneticsSampleResult =
                            geneticsSampleResultService.selectByGeneticsSampleId(geneticsSample.getGeneticsSampleId());
                    if (Objects.nonNull(geneticsSampleResult)) {
                        // 结果信息
                        BeanUtils.copyProperties(geneticsSampleResult, context.sample);
                    }

                    // 审核信息
                    GeneticsInspectionDto sample = (GeneticsInspectionDto) context.sample;
                    sample.setOneCheckerId(geneticsSample.getOneCheckerId());
                    sample.setOneCheckerName(geneticsSample.getOneCheckerName());
                    sample.setOneCheckDate(geneticsSample.getOneCheckDate());
                    sample.setTwoCheckerId(geneticsSample.getTwoCheckerId());
                    sample.setTwoCheckerName(geneticsSample.getTwoCheckerName());
                    sample.setTwoCheckDate(geneticsSample.getTwoCheckDate());

                    break;
                }
                case ROUTINE: {

                    context.sample = new RoutineInspectionDto();

                    SampleDto s = (SampleDto) context.get(SyncContext.SAMPLE);
                    if (Objects.isNull(s)) {
                        s = sampleService.selectByApplySampleId(context.applySampleId);
                    }

                    if (Objects.isNull(s)) {
                        break;
                    }

                    context.sample.setFinalCheckDate(s.getTwoCheckDate());
                    context.sample.setFinalCheckerId(s.getTwoCheckerId());
                    context.sample.setFinalCheckerName(s.getTwoCheckerName());

                    context.sample.setSampleId(s.getSampleId());
                    context.sample.setInstrumentGroupId(s.getInstrumentGroupId());
                    context.sample.setInstrumentGroupName(s.getInstrumentGroupName());
                    context.sample.setTestDate(s.getTestDate());
                    // 样本号
                    context.sample.setSampleNo(s.getSampleNo());
                    context.sample.setInstrumentId(s.getInstrumentId());
                    context.sample.setInstrumentName(s.getInstrumentName());

                    // 样本图片
                    context.sample.setSampleImages(sampleImageService.selectSampleImageBySampleId(
                            s.getSampleId()).stream().map(SampleImageDto::getImageUrl).collect(Collectors.toList()));
                    log.info("组装的样本信息（图片） [{}]", JSON.toJSONString(context.sample));

                    final RoutineInspectionDto sample = (RoutineInspectionDto) context.sample;
                    sample.setOneCheckerId(s.getOneCheckerId());
                    sample.setOneCheckerName(s.getOneCheckerName());
                    sample.setOneCheckDate(s.getOneCheckDate());
                    sample.setTwoCheckerId(s.getTwoCheckerId());
                    sample.setTwoCheckerName(s.getTwoCheckerName());
                    sample.setTwoCheckDate(s.getTwoCheckDate());
                    sample.setReportItems(Lists.newArrayList());

                    if (CollectionUtils.isEmpty(context.sampleReportItems)) {
                        break;
                    }
                    final Map<String, List<InstrumentReportItemDto>> instrumentReportItemsMap = getInstrumentReportItemsMap(s.getInstrumentGroupId());
                    final Map<String, SampleResultDto> results = sampleResultService.selectBySampleId(s.getSampleId())
                            .stream().collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a));
                    sample.setReportItems(context.sampleReportItems.stream().map(e -> {
                        final RoutineInspectionDto.RoutineReportItem item =
                                new RoutineInspectionDto.RoutineReportItem();
                        item.setReportItemCode(e.getReportItemCode());
                        item.setReportItemName(e.getReportItemName());
                        item.setTestItemId(e.getTestItemId());
                        item.setTestItemCode(e.getTestItemCode());
                        item.setTestItemName(e.getTestItemName());
                        item.setPrintSort(e.getPrintSort());

                        if (results.containsKey(e.getReportItemCode())) {
                            final SampleResultDto result = results.get(e.getReportItemCode());
                            item.setResultType(StringUtils.EMPTY);
                            item.setUnit(result.getUnit());
                            item.setRange(result.getRange());
                            item.setStatus(result.getStatus());
                            final InstrumentReportItemDto instrumentReportItemDto = InstrumentReportItemDto.getInstrumentReportItemDto(instrumentReportItemsMap, result.getReportItemCode(), context.sample.getInstrumentId());
                            item.setInstrumentId(result.getInstrumentId() != 0 ? result.getInstrumentId() :
                                    Objects.isNull(instrumentReportItemDto) ? sample.getInstrumentId() : instrumentReportItemDto.getInstrumentId());
                            item.setInstrumentName(result.getInstrumentId() != 0 ? result.getInstrumentName() :
                                    Objects.isNull(instrumentReportItemDto) ? sample.getInstrumentName() : instrumentReportItemDto.getInstrumentName());
                            item.setIsPrint(Objects.nonNull(instrumentReportItemDto) ? instrumentReportItemDto.getIsPrint() : YesOrNoEnum.YES.getCode());
                            item.setSampleResultId(result.getSampleResultId());
                            item.setInstrumentResult(result.getInstrumentResult());
                            item.setResult(result.getResult());
                            item.setJudge(result.getJudge());
                            item.setCreateDate(result.getCreateDate());
                            item.setInstrumentReportItemReferenceId(result.getInstrumentReportItemReferenceId());
                        }

                        return item;
                    }).collect(Collectors.toList()));

                    break;
                }
                case INFECTION: {
                    context.sample = new InfectionInspectionDto();

                    InfectionSampleDto infectionSample = (InfectionSampleDto) context.get(SyncContext.SAMPLE);
                    if (Objects.isNull(infectionSample)) {
                        infectionSample = infectionSampleService.selectByApplySampleId(context.applySampleId);
                    }

                    if (Objects.isNull(infectionSample)) {
                        break;
                    }

                    final Map<String, List<InstrumentReportItemDto>> instrumentReportItemsMap = getInstrumentReportItemsMap(infectionSample.getInstrumentGroupId());
                    context.sample.setFinalCheckDate(infectionSample.getCheckDate());
                    context.sample.setFinalCheckerId(infectionSample.getCheckerId());
                    context.sample.setFinalCheckerName(infectionSample.getCheckerName());

                    context.sample.setSampleId(infectionSample.getInfectionSampleId());
                    context.sample.setInstrumentGroupId(infectionSample.getInstrumentGroupId());
                    context.sample.setInstrumentGroupName(infectionSample.getInstrumentGroupName());
                    context.sample.setTestDate(infectionSample.getTestDate());
                    context.sample.setInstrumentId(infectionSample.getInstrumentId());
                    context.sample.setInstrumentName(infectionSample.getInstrumentName());

                    // 样本号
                    context.sample.setSampleNo(infectionSample.getSampleNo());
                    final InfectionInspectionDto sample = (InfectionInspectionDto) context.sample;
                    sample.setTwoCheckerId(infectionSample.getCheckerId());
                    sample.setTwoCheckerName(infectionSample.getCheckerName());
                    sample.setTwoCheckDate(infectionSample.getCheckDate());
                    sample.setStandardCode(infectionSample.getStandardCode());
                    sample.setStandardName(infectionSample.getStandardName());
                    sample.setReportItems(Lists.newArrayList());

                    if (CollectionUtils.isEmpty(context.sampleReportItems)) {
                        break;
                    }

                    final Map<String, SampleResultDto> results =
                            sampleResultService.selectBySampleId(infectionSample.getInfectionSampleId()).stream()
                                    .collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a));
                    sample.setReportItems(context.sampleReportItems.stream().map(e -> {
                        final InfectionInspectionDto.InfectionReportItem item =
                                new InfectionInspectionDto.InfectionReportItem();
                        item.setReportItemCode(e.getReportItemCode());
                        item.setReportItemName(e.getReportItemName());
                        item.setTestItemId(e.getTestItemId());
                        item.setTestItemCode(e.getTestItemCode());
                        item.setTestItemName(e.getTestItemName());

                        if (results.containsKey(e.getReportItemCode())) {
                            final SampleResultDto result = results.get(e.getReportItemCode());
                            item.setUnit(result.getUnit());
                            item.setRange(result.getRange());
                            item.setResult(result.getResult());
                            item.setJudge(result.getJudge());
                            final InstrumentReportItemDto instrumentReportItemDto = InstrumentReportItemDto.getInstrumentReportItemDto(instrumentReportItemsMap, result.getReportItemCode(), context.sample.getInstrumentId());
                            item.setInstrumentId(result.getInstrumentId() != 0 ? result.getInstrumentId() :
                                    Objects.isNull(instrumentReportItemDto) ? sample.getInstrumentId() : instrumentReportItemDto.getInstrumentId());
                            item.setInstrumentName(result.getInstrumentId() != 0 ? result.getInstrumentName() :
                                    Objects.isNull(instrumentReportItemDto) ? sample.getInstrumentName() : instrumentReportItemDto.getInstrumentName());
                            item.setInstrumentResult(result.getInstrumentResult());
                            item.setInstrumentReportItemReferenceId(result.getInstrumentReportItemReferenceId());
                        }

                        return item;
                    }).collect(Collectors.toList()));
                    break;
                }
                case MICROBIOLOGY: {
                    context.sample = new MicrobiologyInspectionDto();

                    MicrobiologySampleDto microbiologySample = (MicrobiologySampleDto) context.get(SyncContext.SAMPLE);
                    if (Objects.isNull(microbiologySample)) {
                        microbiologySample = microbiologySampleService.selectByApplySampleId(context.applySampleId);
                    }
                    if (Objects.isNull(microbiologySample)) {
                        break;
                    }

                    context.sample.setFinalCheckDate(microbiologySample.getCheckDate());
                    context.sample.setFinalCheckerId(microbiologySample.getCheckerId());
                    context.sample.setFinalCheckerName(microbiologySample.getCheckerName());

                    context.sample.setSampleId(microbiologySample.getMicrobiologySampleId());
                    context.sample.setInstrumentGroupId(microbiologySample.getInstrumentGroupId());
                    context.sample.setInstrumentGroupName(microbiologySample.getInstrumentGroupName());
                    context.sample.setTestDate(microbiologySample.getTestDate());
                    context.sample.setInstrumentId(microbiologySample.getInstrumentId());
                    context.sample.setInstrumentName(microbiologySample.getInstrumentName());
                    // 样本号
                    context.sample.setSampleNo(microbiologySample.getSampleNo());

                    final MicrobiologyInspectionDto sample = (MicrobiologyInspectionDto) context.sample;

                    sample.setOneCheckerId(StringUtils.isNotBlank(microbiologySample.getOneCheckerName()) ? microbiologySample.getOneCheckerId() : microbiologySample.getCheckerId());
                    sample.setOneCheckerName(StringUtils.isNotBlank(microbiologySample.getOneCheckerName()) ? microbiologySample.getOneCheckerName() : microbiologySample.getCheckerName());
                    sample.setOneCheckerDate(StringUtils.isNotBlank(microbiologySample.getOneCheckerName()) ? microbiologySample.getOneCheckerDate() : microbiologySample.getCheckDate());
                    sample.setTwoCheckerId(microbiologySample.getCheckerId());
                    sample.setTwoCheckerName(microbiologySample.getCheckerName());
                    sample.setTwoCheckDate(microbiologySample.getCheckDate());

                    // 结果
                    final List<MicrobiologySampleResultDto> microbiologySampleResults = microbiologySampleResultService
                            .selectByMicrobiologySampleId(microbiologySample.getMicrobiologySampleId());
                    // 细菌
                    final List<MicrobiologySampleGermDto> microbiologySampleGerms = microbiologySampleGermService
                            .selectByMicrobiologySampleId(microbiologySample.getMicrobiologySampleId());
                    // 抗生素
                    final List<MicrobiologyGermMedicineDto> microbiologyGermMedicines = microbiologyGermMedicineService
                            .selectByMicrobiologySampleId(microbiologySample.getMicrobiologySampleId());

                    sample.setResults(microbiologySampleResults.stream().map(e -> {
                        final MicrobiologyResultDto result = new MicrobiologyResultDto();
                        result.setResult(e.getResult());
                        result.setResultDesc(e.getResultDesc());
                        result.setResultCode(e.getResultCode());
                        result.setResultProperty(e.getResultProperty());
                        log.info("同步微生物结果属性=>" + JSON.toJSONString(result));
                        return result;
                    }).collect(Collectors.toList()));

                    sample.setGerms(microbiologySampleGerms.stream().map(e -> {
                        final MicrobiologyGermDto germ = new MicrobiologyGermDto();
                        germ.setTestItemId(e.getTestItemId());
                        germ.setTestItemCode(e.getTestItemCode());
                        germ.setTestItemName(e.getTestItemName());
                        germ.setGermId(e.getGermId());
                        germ.setGermCode(e.getGermCode());
                        germ.setGermGenusId(e.getGermGenusId());
                        germ.setGermGenusCode(e.getGermGenusCode());
                        germ.setGermName(e.getGermName());
                        germ.setGermCount(e.getGermCount());
                        germ.setGermRemark(e.getGermRemark());
                        germ.setGermRemarkCode(e.getGermRemarkCode());
                        germ.setTestMethod(e.getTestMethod());
                        germ.setMedicines(Lists.newArrayList());

                        germ.setMedicines(microbiologyGermMedicines.stream()
                                .filter(k -> Objects.equals(e.getGermId(), k.getGermId())).map(k -> {
                                    final MicrobiologyMedicineDto medicine = new MicrobiologyMedicineDto();
                                    medicine.setMedicineId(k.getMedicineId());
                                    medicine.setMedicineCode(k.getMedicineCode());
                                    medicine.setMedicineName(k.getMedicineName());
                                    medicine.setMedicineMethod(k.getMedicineMethod());
                                    medicine.setSusceptibility(k.getSusceptibility());
                                    medicine.setUnit(k.getUnit());
                                    medicine.setRange(k.getRange());
                                    medicine.setFormula(k.getFormula());
                                    medicine.setResult(k.getResult());
                                    return medicine;
                                }).collect(Collectors.toList()));

                        return germ;
                    }).collect(Collectors.toList()));

                    break;
                }
                case SPECIALTY: {
                    context.sample = new SpecialtyInspectionDto();

                    SpecialtySampleDto specialtySample = (SpecialtySampleDto) context.get(SyncContext.SAMPLE);
                    if (Objects.isNull(specialtySample)) {
                        specialtySample = specialtySampleService.selectByApplySampleId(context.applySampleId);
                    }
                    if (Objects.isNull(specialtySample)) {
                        break;
                    }

                    context.sample.setSampleId(specialtySample.getSpecialtySampleId());
                    context.sample.setInstrumentGroupId(specialtySample.getInstrumentGroupId());
                    context.sample.setInstrumentGroupName(specialtySample.getInstrumentGroupName());
                    context.sample.setTestDate(specialtySample.getTestDate());
                    // 样本号
                    context.sample.setSampleNo(specialtySample.getSampleNo());
                    // 终审人信息补充
                    context.sample.setFinalCheckerId(specialtySample.getTwoCheckerId());
                    context.sample.setFinalCheckerName(specialtySample.getTwoCheckerName());
                    context.sample.setFinalCheckDate(specialtySample.getTwoCheckDate());
                    context.sample.setInstrumentId(specialtySample.getInstrumentId());
                    context.sample.setInstrumentName(specialtySample.getInstrumentName());
                    // 审核信息
                    SpecialtyInspectionDto sample = (SpecialtyInspectionDto) context.sample;
                    sample.setOneCheckerId(specialtySample.getOneCheckerId());
                    sample.setOneCheckerName(specialtySample.getOneCheckerName());
                    sample.setOneCheckDate(specialtySample.getOneCheckDate());
                    sample.setTwoCheckerId(specialtySample.getTwoCheckerId());
                    sample.setTwoCheckerName(specialtySample.getTwoCheckerName());
                    sample.setTwoCheckDate(specialtySample.getTwoCheckDate());
                    sample.setFinalCheckDate(specialtySample.getTwoCheckDate());

                    List<SpecialtySampleResultDto> resultDtoList =
                            specialtySampleResultService.selectBySpecialtySampleId(sample.getSampleId());
                    sample.setResults(resultDtoList);

                    break;
                }
                case BLOOD_CULTURE: {
                    context.sample = new BloodCultureInspectionDto();

                    BloodCultureSampleDto bloodCultureSample = (BloodCultureSampleDto) context.get(SyncContext.SAMPLE);
                    if (Objects.isNull(bloodCultureSample)) {
                        bloodCultureSample = bloodCultureSampleService.selectByApplySampleId(context.applySampleId);
                    }

                    if (Objects.isNull(bloodCultureSample)) {
                        break;
                    }

                    context.sample.setSampleId(bloodCultureSample.getBloodCultureSampleId());
                    context.sample.setInstrumentGroupId(bloodCultureSample.getInstrumentGroupId());
                    context.sample.setInstrumentGroupName(bloodCultureSample.getInstrumentGroupName());
                    context.sample.setTestDate(bloodCultureSample.getTestDate());
                    // 样本号
                    context.sample.setSampleNo(bloodCultureSample.getSampleNo());
                    // 终审人信息补充
                    context.sample.setFinalCheckerId(bloodCultureSample.getTwoCheckerId());
                    context.sample.setFinalCheckerName(bloodCultureSample.getTwoCheckerName());
                    context.sample.setFinalCheckDate(bloodCultureSample.getTwoCheckDate());
                    context.sample.setInstrumentId(bloodCultureSample.getInstrumentId());
                    context.sample.setInstrumentName(bloodCultureSample.getInstrumentName());

                    // 审核信息
                    BloodCultureInspectionDto sample = (BloodCultureInspectionDto) context.sample;
                    sample.setOneCheckerId(bloodCultureSample.getOneCheckerId());
                    sample.setOneCheckerName(bloodCultureSample.getOneCheckerName());
                    sample.setOneCheckDate(bloodCultureSample.getOneCheckDate());
                    sample.setTwoCheckerId(bloodCultureSample.getTwoCheckerId());
                    sample.setTwoCheckerName(bloodCultureSample.getTwoCheckerName());
                    sample.setTwoCheckDate(bloodCultureSample.getTwoCheckDate());
                    sample.setFinalCheckDate(bloodCultureSample.getTwoCheckDate());

                    sample.setOneCheckSampleReportId(bloodCultureSample.getOneCheckSampleReportId());
                    sample.setTwoCheckSampleReportId(bloodCultureSample.getTwoCheckSampleReportId());

                    sample.setOneResults(new ArrayList<>());
                    sample.setTwoResults(new ArrayList<>());
                    sample.setOneRemarks(new ArrayList<>());
                    sample.setTwoRemarks(new ArrayList<>());


                    final List<BloodCultureSampleRemarkDto> remarks = bloodCultureSampleRemarkService.selectByBloodCultureSampleId(sample.getSampleId());
                    final List<BloodCultureSampleResultDto> results = bloodCultureSampleResultService.selectByBloodCultureSampleId(sample.getSampleId());

                    sample.getOneResults().addAll(results.stream()
                            .filter(k -> Objects.equals(k.getPosition(), BloodCultureSampleReportEnum.ONE.name()))
                            .map(k -> new BloodCultureInspectionDto.Result().setResult(k.getResult())
                                    .setResultCode(k.getResultCode())
                                    .setId(k.getBloodCultureSampleResultId()))
                            .collect(Collectors.toList()));
                    sample.getTwoResults().addAll(results.stream()
                            .filter(k -> Objects.equals(k.getPosition(), BloodCultureSampleReportEnum.TWO.name()))
                            .map(k -> new BloodCultureInspectionDto.Result().setResult(k.getResult())
                                    .setResultCode(k.getResultCode())
                                    .setId(k.getBloodCultureSampleResultId()))
                            .collect(Collectors.toList()));

                    sample.getOneRemarks().addAll(remarks.stream()
                            .filter(k -> Objects.equals(k.getPosition(), BloodCultureSampleReportEnum.ONE.name()))
                            .map(k -> new BloodCultureInspectionDto.Remark().setRemark(k.getRemark()).setId(k.getBloodCultureSampleRemarkId()))
                            .collect(Collectors.toList()));
                    sample.getTwoRemarks().addAll(remarks.stream()
                            .filter(k -> Objects.equals(k.getPosition(), BloodCultureSampleReportEnum.TWO.name()))
                            .map(k -> new BloodCultureInspectionDto.Remark().setRemark(k.getRemark()).setId(k.getBloodCultureSampleRemarkId()))
                            .collect(Collectors.toList()));

                    break;
                }
                case OUTSOURCING: {
                    context.sample = new OutsourcingInspectionDto();
                    OutsourcingSampleDto outsourcingSample = (OutsourcingSampleDto) context.get(SyncContext.SAMPLE);
                    if (Objects.isNull(outsourcingSample)) {
                        outsourcingSample = outsourcingSampleService.selectByApplySampleId(context.applySampleId);
                    }
                    if (Objects.isNull(outsourcingSample)) {
                        break;
                    }

                    context.sample.setSampleId(outsourcingSample.getOutsourcingSampleId());
                    context.sample.setInstrumentGroupId(outsourcingSample.getInstrumentGroupId());
                    context.sample.setInstrumentGroupName(outsourcingSample.getInstrumentGroupName());
                    // 样本号
                    context.sample.setSampleNo(outsourcingSample.getSampleNo());
                    // 终审人信息补充
                    context.sample.setFinalCheckerId(outsourcingSample.getCheckerId());
                    context.sample.setFinalCheckerName(outsourcingSample.getCheckerName());
                    context.sample.setFinalCheckDate(outsourcingSample.getCheckDate());
                    context.sample.setInstrumentId(outsourcingSample.getInstrumentId());
                    context.sample.setInstrumentName(outsourcingSample.getInstrumentName());

                    final OutsourcingInspectionDto sample = (OutsourcingInspectionDto) context.sample;
                    sample.setExportBarcode(outsourcingSample.getExportBarcode());
                    sample.setExportOrgId(outsourcingSample.getExportOrgId());
                    sample.setExportOrgName(outsourcingSample.getExportOrgName());
                    sample.setIsPrintList(outsourcingSample.getIsPrintList());
                    sample.setPrintListDate(outsourcingSample.getPrintListDate());
                    sample.setInspectionDate(outsourcingSample.getCreateDate());
                    sample.setTestDate(outsourcingSample.getTestDate());
                    sample.setOneCheckerId(StringUtils.isNotBlank(outsourcingSample.getOneCheckerName()) ? outsourcingSample.getOneCheckerId() : outsourcingSample.getCheckerId());
                    sample.setOneCheckerName(StringUtils.isNotBlank(outsourcingSample.getOneCheckerName()) ? outsourcingSample.getOneCheckerName() : outsourcingSample.getCheckerName());
                    sample.setOneCheckDate(StringUtils.isNotBlank(outsourcingSample.getOneCheckerName()) ? outsourcingSample.getOneCheckDate() : outsourcingSample.getCheckDate());
                    sample.setTwoCheckerId(outsourcingSample.getCheckerId());
                    sample.setTwoCheckerName(outsourcingSample.getCheckerName());
                    sample.setTwoCheckDate(outsourcingSample.getCheckDate());

                    if (CollectionUtils.isEmpty(context.sampleReportItems)) {
                        break;
                    }

                    final Map<String, List<InstrumentReportItemDto>> instrumentReportItemsMap = getInstrumentReportItemsMap(outsourcingSample.getInstrumentGroupId());

                    final Map<String, SampleResultDto> results =
                            sampleResultService.selectBySampleId(sample.getSampleId()).stream()
                                    .collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a));
                    sample.setReportItems(context.sampleReportItems.stream().map(e -> {
                        final OutsourcingInspectionDto.OutsourcingReportItem item =
                                new OutsourcingInspectionDto.OutsourcingReportItem();
                        item.setReportItemCode(e.getReportItemCode());
                        item.setReportItemName(e.getReportItemName());
                        item.setTestItemId(e.getTestItemId());
                        item.setTestItemCode(e.getTestItemCode());
                        item.setTestItemName(e.getTestItemName());
                        item.setPrintSort(e.getPrintSort());

                        if (results.containsKey(e.getReportItemCode())) {
                            final SampleResultDto result = results.get(e.getReportItemCode());
                            item.setResultType(StringUtils.EMPTY);
                            item.setUnit(result.getUnit());
                            item.setRange(result.getRange());
                            item.setStatus(result.getStatus());
                            item.setResult(result.getResult());
                            item.setJudge(result.getJudge());
                            final InstrumentReportItemDto instrumentReportItemDto = InstrumentReportItemDto.getInstrumentReportItemDto(instrumentReportItemsMap, result.getReportItemCode(), context.sample.getInstrumentId());
                            item.setInstrumentId(result.getInstrumentId() != 0 ? result.getInstrumentId() :
                                    Objects.isNull(instrumentReportItemDto) ? sample.getInstrumentId() : instrumentReportItemDto.getInstrumentId());
                            item.setInstrumentName(result.getInstrumentId() != 0 ? result.getInstrumentName() :
                                    Objects.isNull(instrumentReportItemDto) ? sample.getInstrumentName() : instrumentReportItemDto.getInstrumentName());
                            item.setIsPrint(Objects.nonNull(instrumentReportItemDto) ? instrumentReportItemDto.getIsPrint() : YesOrNoEnum.YES.getCode());
                            item.setInstrumentResult(result.getInstrumentResult());
                            item.setInstrumentReportItemReferenceId(result.getInstrumentReportItemReferenceId());
                        }

                        return item;
                    }).collect(Collectors.toList()));

                    break;
                }
                default: {
                    context.sample = new BaseSampleEsModelDto();
                    break;
                }
            }

            return CONTINUE_PROCESSING;
        }
    }
    /**
     * 新增或保存
     */
    private class SaveEsSampleCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            final SyncContext context = SyncContext.from(c);

            final BaseSampleEsModelDto sample = context.sample;

            final BaseSampleEsModelDto dto =
                    elasticSearchSampleService.selectByApplySampleId(sample.getApplySampleId());
            if (Objects.nonNull(dto)) {
                elasticSearchSampleService.updateByApplySampleId(sample.getApplySampleId(), sample);
            } else {
                elasticSearchSampleService.insert(sample);
            }

            return CONTINUE_PROCESSING;
        }
    }
}
