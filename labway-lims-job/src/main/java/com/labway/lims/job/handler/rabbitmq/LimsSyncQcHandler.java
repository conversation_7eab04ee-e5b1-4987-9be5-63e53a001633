package com.labway.lims.job.handler.rabbitmq;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.QcBatchDto;
import com.labway.lims.base.api.dto.QcBatchReportItemDto;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.QcBatchReportItemService;
import com.labway.lims.base.api.service.QcBatchService;
import com.labway.lims.job.dto.DebeziumPayload;
import com.labway.lims.job.dto.LimsSync2EsMessage;
import com.labway.lims.routine.api.dto.QcSampleResultDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SyncQcSampleResultDto;
import com.labway.lims.routine.api.service.QcSampleResultService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.impl.ChainBase;
import org.apache.commons.chain.impl.ContextBase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.job.handler.rabbitmq.DebeziumLimsHandler.getColumnValue;

/**
 * 同步 qc结果
 *
 * <AUTHOR>
 * @since 2023/7/5 9:43
 */
@Slf4j
@Component
public class LimsSyncQcHandler implements InitializingBean {

    private static final String LIMS_DB = "labway-lims";
    private final Map<String, ISample> tables = new LinkedHashMap<>() {

        @Override
        public ISample get(Object key) {
            ISample v = super.get(key);
            if (Objects.nonNull(v) || !(key instanceof String)) {
                return v;
            }

            for (var e : entrySet()) {
                if (key.toString().matches(e.getKey())) {
                    return e.getValue();
                }
            }

            return super.get(key);
        }
    };

    @DubboReference
    private SampleService sampleService;
    @DubboReference
    private SampleResultService sampleResultService;

    @DubboReference
    private QcBatchService qcBatchService;
    @DubboReference
    private QcBatchReportItemService qcBatchReportItemService;
    @DubboReference
    private InstrumentService instrumentService;

    @DubboReference
    private QcSampleResultService qcSampleResultService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private DebeziumLimsHandler debeziumLimsHandler;

    private SyncChain syncChain;

    /**
     * 监听数据库变动
     */
    @RabbitHandler
    @RabbitListener(queues = "${sync2es.sample.qc-queue}")
    public void handle(String msg) throws Exception {
        final LimsSync2EsMessage message = JSON.parseObject(msg, LimsSync2EsMessage.class);

        if (!Objects.equals(message.getApplySampleId(), 0L)) {
            return;
        }

        if (Objects.isNull(message.getMsg())) {
            return;
        }

        final DebeziumPayload payload =
            JSON.parseObject(message.getMsg()).getObject(DebeziumPayload.PAYLOAD, DebeziumPayload.class);

        if (!Objects.equals(payload.getSource().getDb(), LIMS_DB)) {
            return;
        }
        ISample iSample = tables.get(payload.getSource().getTable());
        if (Objects.isNull(iSample)) {
            return;
        }

        Long sampleId = tables.get(payload.getSource().getTable()).getSampleId(payload);
        if (Objects.isNull(sampleId)) {
            log.warn("数据库 [{}] 表 [{}] 没有直接或间接获取到 sampleId", LIMS_DB, payload.getSource().getTable());
            return;
        }

        try {

            final StopWatch watch = new StopWatch();
            watch.start();

            SyncContext context = new SyncContext(sampleId);

            syncChain.execute(context);

            // 如果常规检验样本被删除了，那么 对应质控结果删除
            if (Objects.isNull(context.sampleDto)) {
                // 删除 质控结果
                qcSampleResultService.deleteBySampleId(sampleId);

                watch.stop();

                log.info("同步质控样本 [{}] 成功，检测到质控样本被删除已将对应结果删除 耗时 [{}ms]", sampleId,
                    watch.getTotal(TimeUnit.MILLISECONDS));

            } else {
                watch.stop();
                log.info("同步质控样本 [{}] 成功 耗时 [{}ms]", sampleId, watch.getTotal(TimeUnit.MILLISECONDS));
            }

        } catch (Exception e) {
            log.error("同步质控样本 [{}] 失败，即将重回队列", sampleId, e);

            try {
                // 从回队列
                debeziumLimsHandler.publish(message.getApplySampleId(), IdUtil.simpleUUID(), message.getMsg());
            } catch (Exception ex) {
                log.error("同步质控样本 [{}]  失败，重回队列也失败了", sampleId, ex);

                // nck
                throw e;
            }

        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 从列中获取
        final ISample column = payload -> {
            final Long id = getSampleId(payload);
            if (Objects.isNull(id)) {
                return null;
            }
            return id;
        };
        // 常规检验
        tables.put("tb_sample", column);
        tables.put("tb_sample_result_\\d{4}_\\d{2}$", column);
        syncChain = new SyncChain();
        syncChain.afterPropertiesSet();
    }

    public Long getSampleId(DebeziumPayload payload) {
        final String sampleId = getColumnValue(payload, "sample_id");
        if (Objects.isNull(sampleId)) {
            return null;
        }
        return NumberUtils.toLong(sampleId);
    }

    private interface ISample {
        Long getSampleId(DebeziumPayload payload);
    }

    /**
     * 上下文
     */
    private static class SyncContext extends ContextBase {

        static final String SAMPLE = "SAMPLE_" + IdUtil.objectId();

        private final long sampleId;

        /**
         * 常规检验样本
         */
        private SampleDto sampleDto;

        /**
         * 常规检验样本 结果
         */
        private List<SampleResultDto> sampleResultDtoList;

        /**
         * 对应质控批号
         * <p>
         * key: 结果id value 对应质控批号
         */
        private Map<Long, List<QcBatchDto>> qcBatchDtoMap;

        /**
         * 对应质控批号报告项目
         * <p>
         * key: 质控批号 id value: 对应质控报告项目
         */
        private Map<Long, List<QcBatchReportItemDto>> qcBatchReportItemByQcBatchId;

        /**
         * 现有质控结果
         */
        private List<QcSampleResultDto> qcSampleResultDtoList;

        public SyncContext(long sampleId) {
            this.sampleId = sampleId;
        }

        public static SyncContext from(Context c) {
            return (SyncContext)c;
        }
    }

    private class SyncChain extends ChainBase implements InitializingBean {
        @Override
        public void afterPropertiesSet() throws Exception {

            // 常规检验样本
            addCommand(new SampleCommand());

            // 常规检验结果
            addCommand(new SampleResultCommand());

            // 对应质控
            addCommand(new QcBatchCommand());

            // 现有质控结果
            addCommand(new QcSampleResultCommand());

            // 比对结果
            addCommand(new CompareQcSampleResultCommand());

            // end
            addCommand(c -> PROCESSING_COMPLETE);
        }
    }

    /**
     * 常规检验样本
     */
    private class SampleCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            SyncContext context = SyncContext.from(c);
            context.sampleDto = sampleService.selectBySampleId(context.sampleId);

            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 常规检验样本 结果
     */
    private class SampleResultCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            SyncContext context = SyncContext.from(c);
            context.sampleResultDtoList = sampleResultService.selectBySampleId(context.sampleId);

            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 质控信息
     */
    private class QcBatchCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            SyncContext context = SyncContext.from(c);

            // 最小 检验结果 创建时间
            Date minSampleResultCreateDate = context.sampleResultDtoList.stream().map(SampleResultDto::getCreateDate)
                .filter(Objects::nonNull).min(Comparator.naturalOrder()).orElse(null);

            // 最大 检验结果 创建时间
            Date maxSampleResultCreateDate = context.sampleResultDtoList.stream().map(SampleResultDto::getCreateDate)
                .filter(Objects::nonNull).max(Comparator.naturalOrder()).orElse(null);
            if (Objects.isNull(minSampleResultCreateDate) || Objects.isNull(maxSampleResultCreateDate)) {
                return CONTINUE_PROCESSING;
            }

            SampleDto sampleDto = context.sampleDto;
            if (Objects.isNull(sampleDto)) {
                // 样本不存在
                return CONTINUE_PROCESSING;
            }
            // 样本专业组下 所有仪器
            List<InstrumentDto> instrumentDtos = instrumentService.selectByGroupId(sampleDto.getGroupId());
            Set<String> instrumentCodes =
                instrumentDtos.stream().map(InstrumentDto::getInstrumentCode).collect(Collectors.toSet());
            Map<Long, InstrumentDto> instrumentDtoById =
                instrumentDtos.stream().collect(Collectors.toMap(InstrumentDto::getInstrumentId, Function.identity()));
            if (CollectionUtils.isEmpty(instrumentCodes)) {
                return CONTINUE_PROCESSING;
            }
            context.qcBatchDtoMap = new HashMap<>();

            List<QcBatchDto> qcBatchDtos = qcBatchService.selectByByInstrumentCodeAndDateRange(instrumentCodes,
                minSampleResultCreateDate, maxSampleResultCreateDate);
            for (SampleResultDto resultDto : context.sampleResultDtoList) {
                InstrumentDto instrumentDto = instrumentDtoById.get(resultDto.getInstrumentId());
                if (Objects.isNull(instrumentDto)) {
                    continue;
                }
                // 此结果 相同仪器 时间生效的 质控批号
                List<QcBatchDto> qcBatchDtoList =
                    qcBatchDtos.stream()
                        .filter(obj -> Objects.equals(obj.getInstrumentCode(), instrumentDto.getInstrumentCode()))
                        .filter(item -> resultDto.getCreateDate().compareTo(item.getBeginDate()) >= 0
                            && resultDto.getCreateDate().compareTo(item.getEndDate()) <= 0)
                        .collect(Collectors.toList());
                context.qcBatchDtoMap.put(resultDto.getSampleResultId(), qcBatchDtoList);
            }

            Set<Long> qcBatchIds = context.qcBatchDtoMap.values().stream().flatMap(List::stream)
                .map(QcBatchDto::getQcBatchId).collect(Collectors.toSet());

            context.qcBatchReportItemByQcBatchId = qcBatchReportItemService.selectByQcBatchIds(qcBatchIds).stream()
                .collect(Collectors.groupingBy(QcBatchReportItemDto::getQcBatchId));

            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 现有质控 结果
     */
    private class QcSampleResultCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            SyncContext context = SyncContext.from(c);
            context.qcSampleResultDtoList = qcSampleResultService.selectBySampleId(context.sampleId);

            return CONTINUE_PROCESSING;
        }
    }

    /**
     * 比对 质控 结果
     */
    private class CompareQcSampleResultCommand implements Command {

        @Override
        public boolean execute(Context c) throws Exception {
            SyncContext context = SyncContext.from(c);
            // 样本结果ids
            Set<String> sampleResultKeys = context.sampleResultDtoList.stream().map(result-> result.getSampleId() + result.getReportItemCode()).collect(Collectors.toSet());

            Map<Long, SampleResultDto> sampleResultBySampleResultId = context.sampleResultDtoList.stream()
                .collect(Collectors.toMap(SampleResultDto::getSampleResultId, Function.identity()));

            // qc 结果ids
            Set<String> qcSampleResultKeys = context.qcSampleResultDtoList.stream()
                .map(result-> result.getSampleId() + result.getReportItemCode()).collect(Collectors.toSet());

            // 需要新增的质控样本结果
            List<QcSampleResultDto> needAddQcSampleResultDtos = Lists.newArrayList();
            context.sampleResultDtoList.stream().filter(obj -> !qcSampleResultKeys.contains(obj.getSampleId() + obj.getReportItemCode()))
                .forEach(item -> {
                    QcSampleResultDto qcSampleResultDto = getQcSampleResultDto(context, item);
                    needAddQcSampleResultDtos.add(qcSampleResultDto);
                });

            // 需要删除的质控样本
            Set<QcSampleResultDto> needDeleteSampleResultDtos = context.qcSampleResultDtoList.stream()
                .filter(obj -> !sampleResultKeys.contains(obj.getSampleId()+obj.getReportItemCode())).collect(Collectors.toSet());

            // 需要修改的质控样本结果
            List<QcSampleResultDto> needUpdateQcSampleResultDtos = Lists.newArrayList();
            context.qcSampleResultDtoList.stream().filter(obj -> sampleResultKeys.contains(obj.getSampleId()+obj.getReportItemCode()))
                .forEach(item -> {
                    SampleResultDto sampleResultDto = sampleResultBySampleResultId.get(item.getSampleResultId());
                    QcSampleResultDto qcSampleResultDto = getQcSampleResultDto(context, sampleResultDto);
                    needUpdateQcSampleResultDtos.add(qcSampleResultDto);
                });

            SyncQcSampleResultDto syncQcSampleResultDto = new SyncQcSampleResultDto();
            syncQcSampleResultDto.setNeedAddQcSampleResultDtos(needAddQcSampleResultDtos);
            syncQcSampleResultDto.setNeedDeleteSampleResultDtos(needDeleteSampleResultDtos);
            syncQcSampleResultDto.setNeedUpdateQcSampleResultDtos(needUpdateQcSampleResultDtos);

            qcSampleResultService.syncQcSampleResult(syncQcSampleResultDto);
            return CONTINUE_PROCESSING;
        }

        private QcSampleResultDto getQcSampleResultDto(SyncContext context, SampleResultDto item) {
            QcSampleResultDto qcSampleResultDto = new QcSampleResultDto();
            BeanUtils.copyProperties(item, qcSampleResultDto);
            qcSampleResultDto.setQcBatch(StringUtils.EMPTY);
            String sampleNo;
            if(Objects.nonNull(context.sampleDto)){
                sampleNo = context.sampleDto.getSampleNo();
            } else {
                sampleNo = StringUtils.EMPTY;
            }
            if (Objects.isNull(context.qcBatchDtoMap)
                || Objects.isNull(context.qcBatchDtoMap.get(item.getSampleResultId()))) {
                // 不存在质控批号
                return qcSampleResultDto;
            }
            // 对应质控批号
            List<QcBatchDto> qcBatchDtoList = context.qcBatchDtoMap.get(item.getSampleResultId());
            QcBatchDto qcBatchDto = null;
            
            List<QcBatchDto> filterByReportItem = qcBatchDtoList.stream().filter(obj -> {
                List<QcBatchReportItemDto> qcBatchReportItemDtos = ObjectUtils.defaultIfNull(
                        context.qcBatchReportItemByQcBatchId.get(obj.getQcBatchId()), Collections.emptyList());
                // 过滤 报告项目
                return qcBatchReportItemDtos.stream().anyMatch(qcBatchReportItemDto -> Objects
                        .equals(qcBatchReportItemDto.getReportItemCode(), item.getReportItemCode()));
            }).collect(Collectors.toList());
            
            if(CollectionUtils.isNotEmpty(filterByReportItem)){
              
                List<QcBatchDto> filterBySampleNo = filterByReportItem.stream().filter(obj -> Objects.equals(obj.getSampleNo(), sampleNo)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(filterBySampleNo)){
                    //尝试 通过样本号 寻找
                    qcBatchDto = filterBySampleNo.get(NumberUtils.INTEGER_ZERO);
                }else {
                    qcBatchDto = filterByReportItem.get(NumberUtils.INTEGER_ZERO);
                }
            }

            if (Objects.nonNull(qcBatchDto)) {
                qcSampleResultDto.setQcBatch(qcBatchDto.getQcBatch());
            }
            return qcSampleResultDto;
        }
    }
}
