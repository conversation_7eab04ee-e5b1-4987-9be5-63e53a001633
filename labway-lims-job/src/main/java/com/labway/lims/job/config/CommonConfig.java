package com.labway.lims.job.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <pre>
 * CommonConfig
 * 公共配置信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/7/22 19:31
 */
@Slf4j
@Getter
@Setter
@RefreshScope
@Configuration
public class CommonConfig {

    @Value(("${debezium.receiveLog.enable:false}"))
    private Boolean debeziumReceiveLogEnable;

    @Value(("${deleteUpdateQcSampleResultWithoutQcBatch:true}"))
    private Boolean deleteUpdateQcSampleResultWithoutQcBatch;

}
