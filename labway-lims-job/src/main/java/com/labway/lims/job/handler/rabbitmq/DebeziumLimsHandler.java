package com.labway.lims.job.handler.rabbitmq;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.job.dto.DebeziumPayload;
import com.labway.lims.job.dto.LimsSync2EsMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * debezium
 */
@Slf4j
@Component
public class DebeziumLimsHandler implements InitializingBean {

    private static final String LIMS_DB = "labway-lims";

    private final Map<String, IApplySample> tables = new LinkedHashMap<>() {
        @Override
        public IApplySample get(Object key) {
            IApplySample v = super.get(key);
            if (Objects.nonNull(v) || !(key instanceof String)) {
                return v;
            }

            for (var e : entrySet()) {
                if (key.toString().matches(e.getKey())) {
                    return e.getValue();
                }
            }

            return super.get(key);
        }
    };

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @DubboReference
    private ApplySampleService applySampleService;

    @Value("${sync2es.sample.exchange}")
    private String exchange;
    @Value("${sync2es.sample.routing-key}")
    private String routingKey;

    /**
     * 监听数据库变动
     */
    @RabbitHandler
    @RabbitListener(queues = "${sync2es.sample.debezium-lims}")
    public void handle(String msg) {

        log.trace("收到消息: {}", msg);

        final DebeziumPayload payload = JSON.parseObject(msg).getObject(DebeziumPayload.PAYLOAD, DebeziumPayload.class);

        if (!Objects.equals(payload.getSource().getDb(), LIMS_DB)) {
            return;
        }

        final IApplySample iApplySample = tables.get(payload.getSource().getTable());
        if (Objects.isNull(iApplySample)) {
            return;
        }

        final List<Long> applySampleIds = iApplySample.getApplySampleIds(payload);
        if (CollectionUtils.isEmpty(applySampleIds)) {
            log.warn("数据库 [{}] 表 [{}] 没有直接或间接获取到 applySampleId", LIMS_DB, payload.getSource().getTable());
            return;
        }

        for (Long applySampleId : applySampleIds) {
            final String version = IdUtil.simpleUUID();

            publish(applySampleId, version, msg);

            log.info("数据库 [{}] 表 [{}] applySampleId [{}] version [{}] 发送到 exchange [{}] routingKey [{}] 成功", LIMS_DB,
                    payload.getSource().getTable(), applySampleId, version, exchange, routingKey);
        }

    }

    void publish(Long applySampleId, String version, String msg) {
        final String key = getVersionKey(applySampleId);

        stringRedisTemplate.opsForValue().set(key, version, Duration.ofDays(1));

        final LimsSync2EsMessage message = new LimsSync2EsMessage();
        message.setVersion(version);
        message.setApplySampleId(applySampleId);
        message.setMsg(msg);

        // 发送消息到 mq
        rabbitMQService.convertAndSend(exchange, routingKey, JSON.toJSONString(message));

    }

    public Long getApplySampleId(DebeziumPayload payload) {
        final String applySampleId = getColumnValue(payload, "apply_sample_id");
        if (Objects.isNull(applySampleId)) {
            return null;
        }
        return NumberUtils.toLong(applySampleId);
    }

    @Nullable
    public static String getColumnValue(DebeziumPayload payload, String column) {

        String value = null;
        if (Objects.nonNull(payload.getAfter())) {
            value = payload.getAfter().getString(column);
        }

        if (Objects.isNull(value) && Objects.nonNull(payload.getBefore())) {
            value = payload.getBefore().getString(column);
        }

        return value;
    }

    public String getVersionKey(long applySampleId) {
        return redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":" + applySampleId;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

        // 从列中获取
        final IApplySample column = payload -> {
            final Long id = getApplySampleId(payload);
            if (Objects.isNull(id)) {
                return List.of();
            }
            return List.of(id);
        };

        // 申请单信息
        tables.put("tb_apply", payload -> {
            final String applyId = getColumnValue(payload, "apply_id");
            if (Objects.isNull(applyId)) {
                return List.of();
            }

            return applySampleService.selectByApplyId(NumberUtils.toLong(applyId)).stream()
                    .map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());
        });
        tables.put("tb_apply_sample", column);
        tables.put("tb_apply_sample_item", column);

        // 遗传
        tables.put("tb_genetics_sample", column);
        tables.put("tb_genetics_sample_result", column);

        // 院感
        tables.put("tb_infection_sample", column);

        // 微生物
        tables.put("tb_microbiology_sample", column);
        tables.put("tb_microbiology_sample_germ", column);
        tables.put("tb_microbiology_sample_result", column);
        tables.put("tb_microbiology_germ_medicine", column);
        tables.put("tb_microbiology_sample_fee_item", column);

        // 外送
        tables.put("tb_outsourcing_sample", column);

        // 血培养
        tables.put("tb_bloodculture_sample", column);
        tables.put("tb_bloodculture_sample_remark", column);
        tables.put("tb_bloodculture_sample_result", column);

        // 常规检验
        tables.put("tb_sample", column);
        tables.put("tb_sample_report_item", column);
        tables.put("tb_sample_result_\\d{4}_\\d{2}$", column);
        // 报告
        tables.put("tb_sample_report", column);
        // 特检
        tables.put("tb_specialty_sample", column);
        tables.put("tb_specialty_sample_result", column);
    }

    private interface IApplySample {
        List<Long> getApplySampleIds(DebeziumPayload payload);
    }
}
