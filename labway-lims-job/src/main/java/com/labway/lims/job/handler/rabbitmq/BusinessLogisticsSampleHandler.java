package com.labway.lims.job.handler.rabbitmq;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.apply.api.dto.BusinessLogisticsApplyDto;
import com.labway.lims.apply.api.service.ApplyLogisticsService;
import com.labway.lims.base.api.service.HspOrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class BusinessLogisticsSampleHandler {

    @DubboReference
    private ApplyLogisticsService applyLogisticsService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisPrefix redisPrefix;

    /**
     * 业务中台物流样本信息
     */
    @RabbitHandler
    @RabbitListener(queues = "${business-enter.logistics-sample.queue}")
    public void handle(String msg) throws IOException {
        log.info("接收到业务中台物流样本信息 msg: {}", msg);

        if (StringUtils.isBlank(msg) || BooleanUtils.isNotTrue(JSONUtil.isTypeJSON(msg))) {
            return;
        }

        try {
            final BusinessLogisticsApplyDto logisticsApply = JSON.parseObject(msg, BusinessLogisticsApplyDto.class);

            // 如果三个月内已经消费过了，不再处理
            String consumerKey = redisPrefix.getBasePrefix() + getClass().getSimpleName() + ":handle:" + logisticsApply.hashCode();
            if (BooleanUtils.isTrue(stringRedisTemplate.hasKey(consumerKey))) {
                log.warn("消息 [{}] 已经被消费过了，不再处理 hashcode [{}] ", msg, logisticsApply.hashCode());
                return;
            }

            final String hspOrgCode = logisticsApply.getHspOrgCode();
            if (StringUtils.isBlank(hspOrgCode)) {
                log.warn("送检医院编码为空，不处理 msg: {} ", msg);
                return;
            }
            final Set<String> sampleBarcodes = logisticsApply.getSampleBarcodes();
            if (CollectionUtils.isEmpty(sampleBarcodes)) {
                log.warn("样本条码为空，不处理  msg: {} ", msg);
                return;
            }

            applyLogisticsService.syncCenterLogisticsSample(logisticsApply);

            // 三个月后过期
            stringRedisTemplate.opsForValue().set(consumerKey, StringUtils.EMPTY, 90, TimeUnit.DAYS);

            log.info("消息 [{}] hashcode [{}] 消费成功", msg, logisticsApply.hashCode());
        } catch (Exception e) {
            log.error("业务中台物流样本信息处理失败 msg: {} message: {}", msg, e.getMessage(), e);
            throw e;
        }


    }

}
