package com.labway.lims.job.handler.xxl.statistics;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.labway.lims.statistics.api.client.SampleStatisticsService;
import com.labway.lims.statistics.dto.PushSampleStatisticsInfoDto;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;

@Slf4j
@Component
public class PushSampleStatisticsJob {

    @DubboReference
    private SampleStatisticsService sampleStatisticsService;

    @XxlJob("pushSampleStatistics")
    public void pushSampleStatistics() {
        String param = XxlJobHelper.getJobParam();
        log.info("开始执行统计样本推送业务中台任务。。。执行入参：{}",param);

        // 参数封装
        PushSampleStatisticsInfoDto pushSampleStatisticsInfoDto = fillParam(param);

        log.info("开始调用统计服务。。。调用入参：{}",JSONObject.toJSONString(pushSampleStatisticsInfoDto));

        try {
            sampleStatisticsService.pushSampleStatisticsInfoNew(pushSampleStatisticsInfoDto);
        } catch (Exception e) {
            log.error("调用统计服务推送统计样本，执行异常：{}",e.getMessage());
            throw e;
        }

        log.info("统计样本推送业务中台任务执行结束！！！");
    }


    // 参数封装
    private PushSampleStatisticsInfoDto fillParam(String param) {
        PushSampleStatisticsInfoDto pushSampleStatisticsInfoDto = JSONObject.parseObject(param, PushSampleStatisticsInfoDto.class);

        if (pushSampleStatisticsInfoDto.getUpdateDateBegin() == null || pushSampleStatisticsInfoDto.getUpdateDateEnd() == null) {
            Date[] lastMonth = getLastMonth();
            pushSampleStatisticsInfoDto.setUpdateDateBegin(lastMonth[0]);
            pushSampleStatisticsInfoDto.setUpdateDateEnd(lastMonth[1]);
        }

        if (pushSampleStatisticsInfoDto.getPageNo() == null) {
            pushSampleStatisticsInfoDto.setPageNo(0);
        }

        if (pushSampleStatisticsInfoDto.getPageSize() == null) {
            pushSampleStatisticsInfoDto.setPageSize(10000);
        }

        pushSampleStatisticsInfoDto.setIdempotentCode(UUID.randomUUID().toString());

        return pushSampleStatisticsInfoDto;
    }


    /**
     * 获取上一个月的开始时间和结束时间
     */
    public static Date[] getLastMonth() {
        Date[] dates = new Date[2];
        Date lastMonth = DateUtils.addMonths(new Date(), -1);
        dates[0] = DateUtil.beginOfMonth(lastMonth);
        dates[1] = DateUtil.endOfMonth(lastMonth);
        return dates;
    }

}
