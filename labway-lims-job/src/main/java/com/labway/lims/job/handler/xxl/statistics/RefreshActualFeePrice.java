package com.labway.lims.job.handler.xxl.statistics;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 刷新 tb_apply_sample_item 实际收费价格 actual_fee_price
 * 
 * <AUTHOR>
 * @since 2023/9/26 9:58
 */
@Slf4j
@Component
public class RefreshActualFeePrice {

    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private ItemPriceBasePackageService itemPriceBasePackageService;

    /**
     * 刷新 实际收费价格
     */
    @XxlJob("refreshActualFeePrice")
    public void refreshActualFeePrice() {
        String param = XxlJobHelper.getJobParam();
        QueryApplyPageDto jobParamDto = new QueryApplyPageDto();
        try {
            jobParamDto = JSONUtil.toBean(param, QueryApplyPageDto.class);
        } catch (Exception e) {
            XxlJobHelper.log("输入参数不规范{}", param);
        }

        if (Objects.isNull(jobParamDto.getCreateDateStart()) || Objects.isNull(jobParamDto.getCreateDateEnd())) {
            // 默认 当天 所在上个月
            LocalDate localDate = LocalDate.now().minusMonths(1).withDayOfMonth(1);
            jobParamDto
                .setCreateDateStart(Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
            jobParamDto.setCreateDateEnd(new Date());
        }

        execute(jobParamDto);
    }

    public void execute(QueryApplyPageDto pageDto) {
        Long current = 1L;
        Long size = 2000L;

        pageDto.setSize(size);
        pageDto.setCurrent(current);
        log.info("查询参数:{}", JSON.toJSONString(pageDto));
        ApplyPageDto applyPageDto = applyService.selectPageData(pageDto);

        // 此 范围 下 申请单
        Long total = applyPageDto.getTotal();
        // 执行 第一轮
        log.info("执行 total :{} , current :{}, count :{}", total, current, applyPageDto.getApplys().size());
        refreshByApplyDtoList(applyPageDto.getApplys());

        // 可查询 页数
        long pageCount = total / size + 1;

        while (current < pageCount) {
            current++;

            pageDto.setCurrent(current);
            ApplyPageDto pageData = applyService.selectPageData(pageDto);

            refreshByApplyDtoList(pageData.getApplys());
            log.info("执行 total :{} , current :{}, count :{}", total, current, pageData.getApplys().size());
        }
    }

    /**
     * 根据 申请单 刷新 其下的 实际收费价格
     * 
     */
    private void refreshByApplyDtoList(List<ApplyDto> applyDtos) {
        if (CollectionUtils.isEmpty(applyDtos)) {
            return;
        }
        Set<Long> applyIds = applyDtos.stream().map(ApplyDto::getApplyId).collect(Collectors.toSet());

        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplyIds(applyIds);
        List<Long> applySampleIds =
            applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());

        // key: 申请单id value: 申请单样本
        Map<Long, List<ApplySampleDto>> applySampleByApplyId =
            applySampleDtos.stream().collect(Collectors.groupingBy(ApplySampleDto::getApplyId));

        // key: 申请单样本id value: 申请单样本检验项目
        Map<Long, List<ApplySampleItemDto>> sampleItemByApplySampleId =
            applySampleItemService.selectFeeByApplySampleIds(applySampleIds).stream()
                .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 需要更新实际收费价格的
        List<ApplySampleItemDto> updateList = Lists.newArrayList();
        for (ApplyDto applyDto : applyDtos) {
            List<ApplySampleDto> sampleDtoList = applySampleByApplyId.get(applyDto.getApplyId());
            if (CollectionUtils.isEmpty(sampleDtoList)) {
                continue;
            }
            for (ApplySampleDto applySampleDto : sampleDtoList) {
                List<ApplySampleItemDto> sampleItemDtoList =
                    sampleItemByApplySampleId.get(applySampleDto.getApplySampleId());
                if (CollectionUtils.isEmpty(sampleItemDtoList)) {
                    continue;
                }
                for (ApplySampleItemDto sampleItemDto : sampleItemDtoList) {

                    Map<Long,
                        BigDecimal> actualFeePriceByTestItemId = itemPriceBasePackageService.selectActualFeePrice(
                            applyDto.getHspOrgId(), applyDto.getApplyTypeCode(), sampleItemDto.getCreateDate(),
                            Collections.singleton(sampleItemDto.getTestItemId()));
                    // 未匹配到实际 收费价格
                    BigDecimal actualFeePrice = actualFeePriceByTestItemId.get(sampleItemDto.getTestItemId());
                    if (Objects.isNull(actualFeePrice)) {
                        continue;
                    }
                    // 与现有的保持一致
                    if (actualFeePrice.compareTo(sampleItemDto.getActualFeePrice()) == 0) {
                        continue;
                    }
                    ApplySampleItemDto update = new ApplySampleItemDto();
                    update.setApplySampleItemId(sampleItemDto.getApplySampleItemId());
                    update.setActualFeePrice(actualFeePrice);
                    updateList.add(update);
                    log.info("需要调整：barcode{},testItemName:{},oldActualFeePrice:{}, actualFeePrice:{}",
                        applySampleDto.getBarcode(), sampleItemDto.getTestItemName(), sampleItemDto.getActualFeePrice(),
                        actualFeePrice);
                }
            }
        }
        if (CollectionUtils.isEmpty(updateList)) {
            log.info("本轮为空");
            return;
        }
        // 更新
        applySampleItemService.updateBatchById(updateList);
    }
}
