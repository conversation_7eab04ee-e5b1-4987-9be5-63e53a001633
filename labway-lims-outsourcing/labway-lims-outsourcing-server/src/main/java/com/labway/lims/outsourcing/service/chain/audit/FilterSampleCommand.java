package com.labway.lims.outsourcing.service.chain.audit;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 发送审核消息到mq
 */
@Slf4j
@Component
class FilterSampleCommand implements Command {

    @Resource
    private SampleReportService sampleReportService;


    @Override
    public boolean execute(Context c) throws Exception {
        final AuditSampleContext context = AuditSampleContext.from(c);

        List<OutsourcingSampleDto> samples = context.getSamples();
        List<Long> applySampleIds = samples.stream().map(OutsourcingSampleDto::getApplySampleId).collect(Collectors.toList());

        List<Long> uploadReportApplysampleIds = sampleReportService.selectByApplySampleIds(applySampleIds).stream()
                .filter(e -> Objects.equals(e.getIsUploadPdf(), YesOrNoEnum.YES.getCode()))
                .map(SampleReportDto::getApplySampleId)
                .collect(Collectors.toList());

        samples = samples.stream()
                .filter(e -> !uploadReportApplysampleIds.contains(e.getApplySampleId()))
                .collect(Collectors.toList());

        context.put(AuditSampleContext.OUTSOURCING_SAMPLE_LIST, samples);

        return CONTINUE_PROCESSING;
    }
}
