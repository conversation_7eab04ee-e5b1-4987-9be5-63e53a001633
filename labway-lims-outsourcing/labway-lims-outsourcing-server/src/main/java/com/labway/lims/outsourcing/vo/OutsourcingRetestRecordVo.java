package com.labway.lims.outsourcing.vo;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.routine.api.dto.RetestRecordDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class OutsourcingRetestRecordVo {


    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 报告项目code
     */
    private String reportItemCode;

    /**
     * 报告项目
     */
    private String reportItemName;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 原始结果
     */
    private String originalResult;

    /**
     * 1: 危机 2: 异常 0: 正常
     *
     * @see ResultStatusEnum
     */
    private Integer status;

    /**
     * 提示符
     */
    private String testJudge;


    /**
     * 打印顺序，越小越靠前
     */
    private Integer printSort;

    /**
     * 历史复查结果（复查1,复查2,复查3）
     */
    private List<RetestRecordDto.RetestResult> historyResult;


    @Getter
    @Setter
    public static class RetestResult {
        /**
         * 复查结果
         */
        private String result;

        /**
         * 1: 危机 2: 异常 0: 正常
         *
         * @see ResultStatusEnum
         */
        private Integer status;

        /**
         * 提示符
         */
        private String testJudge;

        /**
         * 正在复查的结果记录
         */
        private Long sampleRetestMainId;

        /**
         * 复查记录id
         */
        private Long sampleRetestItemId;

    }

}