package com.labway.lims.outsourcing.service.chain.his.get;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Sets;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplyRecordDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetHisSampleProjectDuplicateSignCommand implements Command {

    @DubboReference
    private ApplyService applyService;

    @DubboReference
    private ApplySampleItemService applySampleItemService;

    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    @Override
    public boolean execute(Context context) throws Exception {

        final GetHisSampleContext from = GetHisSampleContext.from(context);

        final HisGetParam hisGetParam = from.getHisGetParam();

        if (BooleanUtils.isTrue(hisGetParam.getIgnoreDuplicateSignItem())) {
            return CONTINUE_PROCESSING;
        }

        final Boolean ignoreDuplicateItem = hisGetParam.getIgnoreDuplicateSignItem();


        final HspOrganizationDto hspOrganization = from.getHspOrganization();
        final OutApplyInfoDTO outApplyInfo = from.getOutApplyInfo();
        final List<OutApplyInfoDTO.OutApplyItem> items = outApplyInfo.getItems();

        final Long hspOrgId = hspOrganization.getHspOrgId();

        ApplyDto apply = new ApplyDto();
        apply.setHspOrgId(hspOrgId);
        apply.setPatientCard(outApplyInfo.getPatientCard());
        apply.setPatientName(outApplyInfo.getPatientName());
        apply.setPatientSex(outApplyInfo.getPatientSex());
        apply.setPatientAge(outApplyInfo.getPatientAge());

        final Collection<Long> applyIds = selectByHspOrgIdAndPatientNameAndSex(apply);
        if (CollectionUtils.isEmpty(applyIds)) {
            return CONTINUE_PROCESSING;
        }

        //这里特殊说明一下。需要对照外部的项目编码，而不是内部的。
        final Set<String> exitsOutItemCodes
                = applySampleItemService.selectByApplyIds(applyIds)
                .stream().map(ApplySampleItemDto::getOutTestItemCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());


        final List<String> outTestItemCodes = items.stream()
                .map(OutApplyInfoDTO.OutApplyItem::getOutTestItemCode)
                .collect(Collectors.toList());

        // 移除不是本次申请的项目
        exitsOutItemCodes.removeIf(x -> !outTestItemCodes.contains(x));

        if (CollectionUtils.isEmpty(exitsOutItemCodes) || BooleanUtils.isTrue(ignoreDuplicateItem)) {
            return CONTINUE_PROCESSING;
        }


        final String outTestItemNames = items.stream()
                .filter(x -> exitsOutItemCodes.contains(x.getOutTestItemCode()))
                .map(OutApplyInfoDTO.OutApplyItem::getOutTestItemName)
                .collect(Collectors.joining("、"));

        LimsCodeException exception = new LimsCodeException(1119, "出现同人同天同项目申请单");
        exception.setData(Map.of(
                "patientName", StringUtils.defaultString(outApplyInfo.getPatientName()),
                "outItemNames", outTestItemNames
        ));

        throw exception;
    }

    Collection<Long> selectByHspOrgIdAndPatientNameAndSex(ApplyDto applyDto) {
        final Date now = new Date();
        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        query.setStartCreateDate(DateUtil.beginOfDay(now));
        query.setEndCreateDate(DateUtil.endOfDay(now));

        //处理同人同天
        query.combineOneDayOnePersonParam(applyDto);

        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(query);
        return baseSampleEsModelDtos.stream().map(BaseSampleEsModelDto::getApplyId).collect(Collectors.toList());
    }
}
