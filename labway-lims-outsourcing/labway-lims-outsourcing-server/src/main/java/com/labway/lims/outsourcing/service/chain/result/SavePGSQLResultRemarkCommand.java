package com.labway.lims.outsourcing.service.chain.result;

import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.InstrumentReportItemRemarkDto;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <pre>
 * SavePGSQLResultRemarkCommand
 * 保存结果备注
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/11/27 10:30
 */
@Slf4j
@Component
public class SavePGSQLResultRemarkCommand implements Command {

    @DubboReference
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);
        final ApplySampleDto applySample = context.getApplySample();
        final String resultDesc = context.getResultDesc();

        // 如果直接传了结果备注，那么直接更新
        if (StringUtils.isNotBlank(resultDesc)) {

            final ApplySampleDto applySampleDto = new ApplySampleDto();
            applySampleDto.setApplySampleId(applySample.getApplySampleId());
            applySampleDto.setResultRemark(resultDesc);
            applySampleService.updateByApplySampleId(applySampleDto);

            return CONTINUE_PROCESSING;
        }

        final InstrumentReportItemRemarkDto resultRemarkDto = context.getResultRemark();

        if (Objects.nonNull(resultRemarkDto)) {
            String oldResultRemark = applySample.getResultRemark();
            String resultRemark = resultRemarkDto.getResultRemark();

            String updateResultRemark = "";
            if (StringUtils.isBlank(oldResultRemark)) {
                updateResultRemark = resultRemark;
            } else {
                if (!oldResultRemark.contains(resultRemark)) {
                    updateResultRemark = oldResultRemark + StringPool.NEW_LINE + resultRemark;
                }
            }

            if (StringUtils.isNotBlank(updateResultRemark)) {
                final ApplySampleDto applySampleDto = new ApplySampleDto();
                applySampleDto.setApplySampleId(applySample.getApplySampleId());
                applySampleDto.setResultRemark(updateResultRemark);
                applySampleService.updateByApplySampleId(applySampleDto);
            }
        }

        return CONTINUE_PROCESSING;
    }


}
