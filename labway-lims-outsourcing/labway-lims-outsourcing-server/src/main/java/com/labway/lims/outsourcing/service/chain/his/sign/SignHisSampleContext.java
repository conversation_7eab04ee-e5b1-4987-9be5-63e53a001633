package com.labway.lims.outsourcing.service.chain.his.sign;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.HisTestApplyDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.outsourcing.api.dto.his.HisSample;
import com.labway.lims.outsourcing.api.dto.his.HisSampleItem;
import com.labway.lims.outsourcing.api.dto.his.HisSignParam;
import com.labway.lims.outsourcing.service.chain.StopWatchContext;
import org.apache.commons.chain.Context;

import java.util.List;
import java.util.Map;

/**
 * <pre>
 * SignHisSampleContext
 * 样本签收
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/15 17:45
 */
public class SignHisSampleContext extends StopWatchContext {

    static final String HIS_SAMPLE = "HIS_SAMPLE" + IdUtil.objectId();
    static final String HIS_SAMPLE_ITEMS = "HIS_SAMPLE_ITEMS" + IdUtil.objectId();
    public static final String HIS_SIGN_PARAM = "HIS_SIGN_PARAM" + IdUtil.objectId();
    static final String HSP_ORG = "HSP_ORG" + IdUtil.objectId();
    static final String ORG_CODE = "ORG_CODE" + IdUtil.objectId();
    // 签收锁
    static final String SIGN_LOCK_KEY = "SIGN_LOCK_KEY" + IdUtil.objectId();
    // 检验项目
    static final String TEST_ITEMS = "TEST_ITEMS" + IdUtil.objectId();
    // 申请单信息
    static final String APPLY_INFO = "APPLY_INFO" + IdUtil.objectId();
    static final String HIS_TEST_APPLY = "HIS_TEST_APPLY" + IdUtil.objectId();

    @SuppressWarnings("unchecked")
    public List<HisSampleItem> getHisSampleItems() {
        return (List<HisSampleItem>) get(HIS_SAMPLE_ITEMS);
    }

    public HisSample getHisSample() {
        return (HisSample) get(HIS_SAMPLE);
    }

    public String getOrgCode() {
        return String.valueOf(get(ORG_CODE));
    }

    public HisSignParam getHisSignParam() {
        return (HisSignParam) get(HIS_SIGN_PARAM);
    }

    public HspOrganizationDto getHspOrganization() {
        return (HspOrganizationDto) get(HSP_ORG);
    }

    public void setSignLockKey(String limitKey) {
        put(SIGN_LOCK_KEY, limitKey);
    }

    public String getSignLockKey() {
        return (String) get(SIGN_LOCK_KEY);
    }

    public Map<Long, TestItemDto> getTestItems() {
        return (Map<Long, TestItemDto>) get(TEST_ITEMS);
    }

    public HisTestApplyDto getHisTestApply() {
        return (HisTestApplyDto) get(HIS_TEST_APPLY);
    }

    public ApplyInfo getApplyInfo() {
        return (ApplyInfo) get(APPLY_INFO);
    }

    public static SignHisSampleContext from(Context context) {
        return (SignHisSampleContext) context;
    }

    @Override
    protected String getWatchName() {
        return "HIS样本签收";
    }
}