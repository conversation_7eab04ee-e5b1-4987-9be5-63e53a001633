package com.labway.lims.outsourcing.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 样本结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class OutsourcingSampleResultVo {

    /**
     * 样本ID
     */
    private Long outsourcingSampleId;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 报告项目code
     */
    private String reportItemCode;

    /**
     * 这个结果可能是经过格式化或计算过的
     */
    private String result;

}
