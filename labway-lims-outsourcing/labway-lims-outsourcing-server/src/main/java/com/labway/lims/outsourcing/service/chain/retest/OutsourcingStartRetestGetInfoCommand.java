package com.labway.lims.outsourcing.service.chain.retest;

import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.service.InstrumentGroupInstrumentService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingStartReTestDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.StartReTestDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/5/26 16:48
 */
@Slf4j
@Component
public class OutsourcingStartRetestGetInfoCommand implements Filter, Command {
    @Resource
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private InstrumentGroupInstrumentService instrumentGroupInstrumentService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingStartRetestContext context = OutsourcingStartRetestContext.from(c);
        final OutsourcingStartReTestDto reTestDto = context.getRetestInfo();
        final Long sampleId = reTestDto.getOutsourcingSampleId();
        final OutsourcingSampleDto sample = outsourcingSampleService.selectByOutsourcingSampleId(sampleId);
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("样本不存在");
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        final List<SampleReportItemDto> sampleReportItems = sampleReportItemService.selectBySampleId(sampleId);
        sampleReportItems.sort(Comparator.comparing(SampleReportItemDto::getPrintSort).thenComparing(Comparator.comparing(SampleReportItemDto::getReportItemId)));
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            throw new IllegalStateException("样本报告项目为空");
        }

        final List<SampleResultDto> sampleResults = sampleResultService.selectBySampleId(sampleId);

        //报告项目id为空表示，当前复查是对样本进行复查
        if (Objects.isNull(reTestDto.getReportItemId())) {
            context.put(OutsourcingStartRetestContext.RETEST_SAMPLE_REPORT_ITEMS, sampleReportItems);
        } else {
            final SampleReportItemDto reportItemDto = sampleReportItems.stream().filter(e -> Objects.equals(e.getReportItemId(), reTestDto.getReportItemId())).findFirst().orElse(null);
            if (Objects.isNull(reportItemDto)) {
                throw new IllegalStateException("当前复查的报告项目为空");
            }
            context.put(OutsourcingStartRetestContext.RETEST_SAMPLE_REPORT_ITEMS, Collections.singletonList(reportItemDto));
        }


        context.put(OutsourcingStartRetestContext.SAMPLE, sample);
        context.put(OutsourcingStartRetestContext.SAMPLE_REPORT_ITEMS, sampleReportItems);
        context.put(OutsourcingStartRetestContext.SAMPLE_REPORT_ITEM_RESULTS, sampleResults);
        context.put(OutsourcingStartRetestContext.APPLY_SAMPLE, applySample);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

}
