package com.labway.lims.outsourcing.service.chain.audit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UpdateSampleAuditStatusCommand implements Command {

    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private OutsourcingSampleService outsourcingSampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditSampleContext context = AuditSampleContext.from(c);
        List<OutsourcingSampleDto> samples = context.getSamples();
        LoginUserHandler.User user = context.getUser();

        Set<Long> outsourcingSampleIds =
            samples.stream().map(OutsourcingSampleDto::getOutsourcingSampleId).collect(Collectors.toSet());

        final OutsourcingSampleDto sampleDto = new OutsourcingSampleDto();
        sampleDto.setCheckDate(new Date());
        sampleDto.setCheckerId(user.getUserId());
        sampleDto.setCheckerName(user.getNickname());
        outsourcingSampleService.updateByOutsourcingSampleIds(sampleDto, outsourcingSampleIds);

        Set<Long> applySampleIdList =
            samples.stream().map(OutsourcingSampleDto::getApplySampleId).collect(Collectors.toSet());

        ApplySampleDto dto = new ApplySampleDto();
        dto.setStatus(SampleStatusEnum.AUDIT.getCode());
        applySampleService.updateByApplySampleIds(dto, applySampleIdList);

        return CONTINUE_PROCESSING;
    }
}
