package com.labway.lims.outsourcing.service.chain.sync;

import com.labway.lims.api.config.LimsOrgCodeConfig;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SyncOutsourcingCheckParamChain implements Command, InitializingBean {

    @Resource
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Autowired
    private LimsOrgCodeConfig limsOrgCodeConfig;

    @Override
    public boolean execute(Context c) throws Exception {
        final SyncOutsourcingContext context = SyncOutsourcingContext.from(c);
        final Collection<Long> outsourcingSampleIds = context.getOutsourcingSampleIds();

        final List<OutsourcingSampleDto> samples = outsourcingSampleService.selectByOutsourcingSampleIds(outsourcingSampleIds);
        if (CollectionUtils.isEmpty(samples)) {
            throw new IllegalArgumentException("样本不存在");
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(samples.stream().map(OutsourcingSampleDto::getApplySampleId)
                .collect(Collectors.toSet()));

        final String barcodes = applySamples.stream().filter(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode()))
                .map(ApplySampleDto::getBarcode)
                .collect(Collectors.joining("、"));

        if (StringUtils.isNotBlank(barcodes)) {
            throw new IllegalArgumentException(String.format("%s 已审核, 请取审后重试", barcodes));
        }

        context.setApplySamples(applySamples.stream().collect(Collectors
                .toMap(ApplySampleDto::getApplySampleId, v -> v, (a, b) -> a)));

        // 送检机构信息
        final List<HspOrganizationDto> hspOrganizationDtos = hspOrganizationService.selectAll();
        final Map<Long, HspOrganizationDto> hspOrgMap = hspOrganizationDtos.stream().collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, v -> v, (a, b) -> a));

        final Long exportOrgId = context.getExportOrgId();
        final HspOrganizationDto hspOrganizationDto = hspOrgMap.get(exportOrgId);
        if (Objects.isNull(hspOrganizationDto)) {
            log.error("外送机构不存在");
            throw new IllegalStateException("外送机构不存在");
        }
        final String mappedOrgCode = limsOrgCodeConfig.getMappedOrgCode(hspOrganizationDto.getHspOrgCode());

        context.put(SyncOutsourcingContext.TEST_ORG_IS_LIMS_SYSTEM, limsOrgCodeConfig.isLimsSystem(mappedOrgCode));
        context.put(SyncOutsourcingContext.HSP_ORG_MAP, hspOrgMap);
        context.setOutsourcingSamples(samples);

        return CONTINUE_PROCESSING;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
