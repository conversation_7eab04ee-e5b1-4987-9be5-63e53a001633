package com.labway.lims.outsourcing.service.chain.retest;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.routine.SampleCriticalResultStatusEnum;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingStartReTestDto;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/27 13:44
 */
@Slf4j
@Component
public class OutsourcingStartRetestUpdateCriticalStatusCommand implements Filter, Command {

    @Resource
    private OutsourcingStartRetestCheckSaveOriginalCommand startRetestCheckSaveOriginalCommand;
    @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingStartRetestContext context = OutsourcingStartRetestContext.from(c);
        final OutsourcingStartReTestDto reTestDto = context.getRetestInfo();
        final OutsourcingSampleDto sample = context.getSample();

        // 获取到所有危急值
        final List<SampleCriticalResultDto> criticalResults = new ArrayList<>(sampleCriticalResultService.selectBySampleId(sample.getOutsourcingSampleId()));
        // 已经处理过的无需修改状态
        criticalResults.removeIf(e -> Objects.equals(e.getStatus(), SampleCriticalResultStatusEnum.PROCESSED.getCode()));

        if (CollectionUtils.isEmpty(criticalResults)) {
            return CONTINUE_PROCESSING;
        }

        // 或许到要修改危急值处理状态的危急值
        final List<SampleCriticalResultDto> rs = criticalResults.stream().filter(e -> context.getRetestSampleReportItems().stream()
                .anyMatch(k -> Objects.equals(k.getReportItemCode(), e.getReportItemCode()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rs)) {
            return CONTINUE_PROCESSING;
        }


        // 修改危机复查 状态为正在复查状态
        SampleCriticalResultDto criticalResultDto = new SampleCriticalResultDto();
        criticalResultDto.setStatus(SampleCriticalResultStatusEnum.UNDER_REVIEW.getCode());
        //1.1.3新增对回读的处理
        criticalResultDto.setNeedHandleReadBack(reTestDto.isNeedHandleReadBack());
        criticalResultDto.setIsReadBack(reTestDto.getIsReadBack());
        criticalResultDto.setReadBackTime(new Date());
        criticalResultDto.setRemark(reTestDto.getRemark());
        criticalResultDto.setReadBackUser(LoginUserHandler.get().getNickname());
        sampleCriticalResultService.updateByCriticalValueIds(criticalResultDto, rs.stream()
                .map(SampleCriticalResultDto::getCriticalValueId).collect(Collectors.toSet()));

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

}
