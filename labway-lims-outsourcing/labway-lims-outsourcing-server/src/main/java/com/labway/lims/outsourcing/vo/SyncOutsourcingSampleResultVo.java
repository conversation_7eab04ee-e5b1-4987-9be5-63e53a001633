package com.labway.lims.outsourcing.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * <pre>
 * SyncOutsourcingSampleResultVo
 * 同步结果
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/3/21 14:51
 */
@Getter
@Setter
public class SyncOutsourcingSampleResultVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 样本ID
     */
    private Set<Long> outsourcingSampleIds;

    /**
     * 是否忽略多出的报告项目
     */
    private boolean ignoreMissingReportItem;

    /**
     * 跳过没有结果的项目（即只同步已出结果的项目）
     */
    private Boolean skipNoresultReportItem;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

}
