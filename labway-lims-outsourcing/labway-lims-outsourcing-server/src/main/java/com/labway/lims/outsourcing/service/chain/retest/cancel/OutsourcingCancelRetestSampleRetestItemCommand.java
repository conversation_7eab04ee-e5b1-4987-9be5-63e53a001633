package com.labway.lims.outsourcing.service.chain.retest.cancel;

import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 修改复查项目状态
 */
@Slf4j
@Component
class OutsourcingCancelRetestSampleRetestItemCommand implements Command {
    @DubboReference
    private SampleRetestItemService sampleRetestItemService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingCancelRetestContext context = OutsourcingCancelRetestContext.from(c);


        // 如果为空 删除所有
        if (Objects.isNull(context.getReportItemId())) {
            sampleRetestItemService.deleteBySampleRetestMainId(context.getSampleRetestMain().getSampleRetestMainId());

            // 修改报告项目状态
            restoreSampleReportItemIsRetest(context, context.getSampleRetestItems().stream()
                    .map(SampleRetestItemDto::getReportItemId).collect(Collectors.toList()));

            context.getSampleRetestItems().clear();
        } else {
            sampleRetestItemService.deleteBySampleRetestMainIdAndReportItemId(
                    context.getSampleRetestMain().getSampleRetestMainId(),
                    context.getReportItemId()
            );

            // 删除取消复查的
            context.getSampleRetestItems().removeIf(e -> Objects.equals(e.getReportItemId(), context.getReportItemId()));

            // 修改报告项目状态
            restoreSampleReportItemIsRetest(context, List.of(context.getReportItemId()));
        }


        return CONTINUE_PROCESSING;
    }


    private void restoreSampleReportItemIsRetest(final OutsourcingCancelRetestContext context, List<Long> reportItemIds) {

        final List<SampleReportItemDto> sampleReportItems = context.getSampleReportItems().stream()
                .filter(e -> reportItemIds.contains(e.getReportItemId())).collect(Collectors.toList());

        final List<SampleRetestItemDto> sampleRetestItems = sampleRetestItemService.selectBySampleId(context.getOutsourcingSampleId());
        // 删除当前的
        sampleRetestItems.removeIf(e -> Objects.equals(e.getSampleRetestMainId(), context.getSampleRetestMain().getSampleRetestMainId()));

        final Map<RetestStatusEnum, List<Long>> map = new EnumMap<>(RetestStatusEnum.class);
        //需要取消复查的报告项目
        for (SampleReportItemDto e : sampleReportItems) {
            // 如果包含 那就是复查过
            if (sampleRetestItems.stream().anyMatch(k -> Objects.equals(k.getReportItemCode(), e.getReportItemCode()))) {
                if (Objects.equals(RetestStatusEnum.RETEST.getCode(), e.getIsRetest())) {
                    continue;
                }
                map.computeIfAbsent(RetestStatusEnum.RETEST, k -> new LinkedList<>()).add(e.getSampleReportItemId());
            } else {
                if (Objects.equals(RetestStatusEnum.NORMAL.getCode(), e.getIsRetest())) {
                    continue;
                }
                map.computeIfAbsent(RetestStatusEnum.NORMAL, k -> new LinkedList<>()).add(e.getSampleReportItemId());
            }
        }

        // 修改报告项目的 isRetest 字段
        for (Map.Entry<RetestStatusEnum, List<Long>> e : map.entrySet()) {
            final SampleReportItemDto sampleReportItem = new SampleReportItemDto();
            sampleReportItem.setIsRetest(e.getKey().getCode());
            sampleReportItem.setSampleId(context.getOutsourcingSampleId());
            sampleReportItemService.updateBySampleReportItemIds(sampleReportItem, e.getValue());
        }

    }
}
