package com.labway.lims.outsourcing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.outsourcing.api.dto.BetterOutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleCondition;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.model.TbOutsourcingSample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 外送样本
 */
@Mapper
public interface OutsourcingSampleMapper extends BaseMapper<TbOutsourcingSample> {
    /**
     * 根据 创建日期 查询
     */
    List<OutsourcingSampleDto> selectByCreateDate(@Param("beginCreateDate") Date beginCreateDate,
                                                  @Param("endCreateDate") Date endCreateDate,
                                                  @Param("orgId") long orgId);

    /**
     * 根据 创建日期 和专业组（多个委外组） 查询
     */
    List<OutsourcingSampleDto> selectByCreateDateAndGroup(@Param("beginCreateDate") Date beginCreateDate,
                                                  @Param("endCreateDate") Date endCreateDate,
                                                  @Param("orgId") Long orgId,
                                                  @Param("groupId") Long groupId);

    int updateByOutsourcingSampleIds(@Param("outsourcingSample") OutsourcingSampleDto outsourcingSample,
                                     @Param("outsourcingSampleIds") Collection<Long> outsourcingSampleIds);

    void updateByApplySampleIds(@Param("outsourcingSample") OutsourcingSampleDto outsourcingSample,
                                @Param("applySampleIds") Collection<Long> applySampleIds);

    /**
     * 根据条件查询
     */
    List<BetterOutsourcingSampleDto> selectByCondition(@Param("condition") OutsourcingSampleCondition condition);
}
