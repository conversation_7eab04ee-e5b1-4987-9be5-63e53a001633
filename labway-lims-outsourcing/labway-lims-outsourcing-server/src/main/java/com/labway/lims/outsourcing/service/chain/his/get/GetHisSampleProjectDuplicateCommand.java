package com.labway.lims.outsourcing.service.chain.his.get;

import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.api.dto.his.HisSampleItem;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class GetHisSampleProjectDuplicateCommand implements Command {
    @Override
    public boolean execute(Context context) throws Exception {
        final GetHisSampleContext from = GetHisSampleContext.from(context);
        final HisGetParam hisGetParam = from.getHisGetParam();
        final OutApplyInfoDTO outApplyInfo = from.getOutApplyInfo();
        final HspOrganizationDto hspOrganization = from.getHspOrganization();

        final List<HisSampleItem> hisSampleItems = from.getHisSampleItems();

        // 移除前端传过来的重复项目
        final Set<String> hisSampleItemIds = ObjectUtils.defaultIfNull(hisGetParam.getHisSampleItemIds(),
                new HashSet<>());
        hisSampleItems.removeIf(f -> hisSampleItemIds.contains(f.getHisSampleItemId()));

        // 如果项目为空，报错
        if (CollectionUtils.isEmpty(hisSampleItems)) {
            throw new LimsCodeException(11210, String.format("条码 [%s] 项目信息为空", outApplyInfo.getBarcode()));
        }

        // 然后在判断是否有重复项目
        List<DuplicateHisSampleItem> duplicateHisSampleItems = new ArrayList<>();

        final List<HisSampleItem> cloneHisSampleItems =
                JSON.parseArray(JSON.toJSONString(hisSampleItems), HisSampleItem.class);

        final Map<String, List<HisSampleItem>> groupHisSampleItemMap =
                cloneHisSampleItems.stream().collect(Collectors.groupingBy(HisSampleItem::getTestItemCode));


        for (Map.Entry<String, List<HisSampleItem>> item : groupHisSampleItemMap.entrySet()) {

            final List<HisSampleItem> value = item.getValue();
            if (CollectionUtils.size(value) <= NumberUtils.INTEGER_ONE) {
                continue;
            }

            final List<DuplicateHisSampleItem> duplicates = value.stream().map(m -> {
                final DuplicateHisSampleItem duplicate =
                        JSON.parseObject(JSON.toJSONString(m), DuplicateHisSampleItem.class);
                duplicate.setPatientName(outApplyInfo.getPatientName());
                duplicate.setPatientAge(outApplyInfo.getPatientAge());
                duplicate.setPatientSex(outApplyInfo.getPatientSex());
                duplicate.setHspOrgName(hspOrganization.getHspOrgName());
                duplicate.setOutBarcode(hisGetParam.getOutBarcode());
                return duplicate;
            }).collect(Collectors.toList());

            duplicateHisSampleItems.addAll(duplicates);
        }

        if (CollectionUtils.isEmpty(duplicateHisSampleItems)) {
            return CONTINUE_PROCESSING;
        }

        duplicateHisSampleItems.sort(Comparator.comparing(DuplicateHisSampleItem::getTestItemCode));
        LimsCodeException exception = new LimsCodeException(1121, "出现重复的检验项目");
        exception.setData(duplicateHisSampleItems);
        throw exception;
    }


    @Getter
    @Setter
    public static final class DuplicateHisSampleItem extends HisSampleItem {

        /**
         * 患者姓名
         */
        private String patientName;

        /**
         * 患者性别
         */
        private Integer patientSex;

        /**
         * 患者年龄
         */
        private Integer patientAge;

        /**
         * 送检机构名称
         */
        private String hspOrgName;

        /**
         * 外部条码
         */
        private String outBarcode;
    }
}
