package com.labway.lims.outsourcing.service.chain.result;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.util.IdcardUtil;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SampleTypeEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.enums.AgeUnitEnum;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仪器参考范围
 *
 * <AUTHOR>
 * @since 2023/3/30 16:15
 */
@Slf4j
@Component
public class InstrumentReportReferenceCommand implements Command {

    public static final int YEAR_DAYS = InstrumentReportItemReferenceService.YEAR_DAYS;
    public static final int MONTH_DAYS = InstrumentReportItemReferenceService.MONTH_DAYS;
    public static final int WEEK_DAYS = 7;

    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    private final ExpressionParser expressionParser = new SpelExpressionParser();

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);
        final OutsourcingSampleDto sample = context.getSample();

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();
        final List<InstrumentReportItemReferenceDto> refs = instrumentReportItemReferenceService.selectByInstrumentReportItemId(instrumentReportItem.getInstrumentReportItemId());

        if (CollectionUtils.isEmpty(refs)) {
            return CONTINUE_PROCESSING;
        }

        final List<InstrumentReportItemReferenceDto> sampleRefs = refs.stream().filter(e -> Objects.equals(e.getInstrumentId(), sample.getInstrumentId()))
                .collect(Collectors.toList());
        InstrumentReportItemReferenceDto instrumentReportItemReferenceDto;

        instrumentReportItemReferenceDto =
                filterCustomerReportReference(context.getApply(), context.getApplySample(), sampleRefs);

        //先取样本仪器上的参考范围
        if (Objects.isNull(instrumentReportItemReferenceDto)) {
            instrumentReportItemReferenceDto =
                    filterCustomerReportReference(context.getApply(), context.getApplySample(), refs);
        }

        if (Objects.isNull(instrumentReportItemReferenceDto)) {
            return CONTINUE_PROCESSING;
        }

        context.put(SaveResultContext.INSTRUMENT_REPORT_REFERENCE, instrumentReportItemReferenceDto);

        return CONTINUE_PROCESSING;

    }

    @Nullable
    public InstrumentReportItemReferenceDto filterCustomerReportReference(ApplyDto apply, ApplySampleDto applySample, List<InstrumentReportItemReferenceDto> refs) {

        if (CollectionUtils.isEmpty(refs)) {
            return null;
        }

        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }
        final Long hspOrgId = apply.getHspOrgId();

        refs = new ArrayList<>(refs);
        // 删除不合法的参考范围
        refs.removeIf(e ->
                // 没有年龄范围
                StringUtils.isBlank(e.getAgeUnit()) ||
                        ObjectUtils.defaultIfNull(e.getAgeMin(), 0) < 0 ||
                        ObjectUtils.defaultIfNull(e.getAgeMax(), 0) < 0 ||
                        // 不是当前样本的机构且不是通用机构的参考范围
                        !(Objects.equals(hspOrgId, e.getHspOrgId()) || Objects.equals(e.getHspOrgId(), NumberUtils.LONG_ZERO)));

        if (CollectionUtils.isEmpty(refs)) {
            return null;
        }

        final Set<InstrumentReportItemReferenceDto> irrFilter = new LinkedHashSet<>();
        for (InstrumentReportItemReferenceDto e : refs) {
            // 如果性别一样 并且 样本类型一样
            if (Objects.equals(e.getSexStyle(), apply.getPatientSex()) && Objects.equals(applySample.getSampleTypeCode(), e.getSampleTypeCode())) {
                irrFilter.add(e);
                // 如果样本类型一样 性别为通用
            } else if (Objects.equals(applySample.getSampleTypeCode(), e.getSampleTypeCode()) && Objects.equals(e.getSexStyle(), SexEnum.DEFAULT.getCode())) {
                irrFilter.add(e);
                // 如果性别一样 且 样本类型为通用
            } else if (Objects.equals(e.getSexStyle(), apply.getPatientSex()) && Objects.equals(e.getSampleTypeCode(), SampleTypeEnum.ALL.getSampleTypeCode())) {
                irrFilter.add(e);
            }
        }

        // 通用也加上
        for (InstrumentReportItemReferenceDto e : refs) {
            if (Objects.equals(e.getSexStyle(), SexEnum.DEFAULT.getCode()) && Objects.equals(e.getSampleTypeCode(), SampleTypeEnum.ALL.getSampleTypeCode())) {
                irrFilter.add(e);
            }
        }

        if (irrFilter.isEmpty()) {
            return null;
        }

        // 获取到病人的年龄（天数）
        int _days = 0;

        // 如果没有输入年龄 那么根据生日计算
        if (Objects.isNull(apply.getPatientAge())) {
            // 根据生日获取此人活了多少天
            if (StringUtils.isNotBlank(apply.getPatientCard()) && IdcardUtil.isValidCard(apply.getPatientCard())) {
                _days = (int) IdcardUtil.getBirthDate(apply.getPatientCard()).between(new Date(), DateUnit.DAY);
            }
        } else {
            _days += (apply.getPatientAge() * YEAR_DAYS);
        }

        if (Objects.nonNull(apply.getPatientSubage())) {
            if (Objects.equals(apply.getPatientSubageUnit(), PatientSubAgeUnitEnum.MONTH.getValue())) {
                _days += (apply.getPatientSubage() * MONTH_DAYS);
            } else if (Objects.equals(apply.getPatientSubageUnit(), PatientSubAgeUnitEnum.WEEK.getValue())) {
                _days += (apply.getPatientSubage() * WEEK_DAYS);
            } else if (Objects.equals(apply.getPatientSubageUnit(), PatientSubAgeUnitEnum.DAY.getValue())) {
                _days += apply.getPatientSubage();
            }
        }

        final long days = _days;

        // 命中到之后，优先取 性别 或 样本类型 一致的
        final List<InstrumentReportItemReferenceDto> filteredRefs = irrFilter.stream().filter(e -> {

            long start = e.getAgeMin();
            long end = e.getAgeMax();

            // 判断天
            if (AgeUnitEnum.MONTH.name().equals(e.getAgeUnit())) {
                start *= MONTH_DAYS;
                end *= MONTH_DAYS;
            } else if (AgeUnitEnum.YEAR.name().equals(e.getAgeUnit())) {
                start *= YEAR_DAYS;
                end *= YEAR_DAYS;
            }

            try {
                // 判断年龄 , 判断年
                final String expression = String.format("%s %s %s && %s %s %s",
                        days, e.getAgeMinFormula(), start, days, e.getAgeMaxFormula(), end);
                final Object value = expressionParser.parseExpression(expression).getValue();
                log.info("条码号 [{}] 参考范围尝试命中 [{}] 命中结果 [{}] 参考范围 [{}]",
                        applySample.getBarcode(), expression, value, e.getCnRefereValue());
                return value instanceof Boolean && (Boolean) value;
            } catch (Exception ex) {
                log.error("条码号 [{}] 校验参考范围错误 [{}] 参考范围 [{}]",
                        applySample.getBarcode(), ex.getMessage(), e.getCnRefereValue(), ex);
                return false;
            }

        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredRefs)) {
            return null;
        }

        // 送检机构 命中
        List<InstrumentReportItemReferenceDto> refs0 = filteredRefs.stream()
                .filter(e -> Objects.equals(e.getHspOrgId(), hspOrgId))
                .collect(Collectors.toList());

        InstrumentReportItemReferenceDto ref = filterHspOrgReference(refs0, apply, applySample);
        if(Objects.nonNull(ref)){
            return ref;
        }

        // 通用送检机构
        List<InstrumentReportItemReferenceDto> refs1 = filteredRefs.stream()
                .filter(e -> Objects.equals(e.getHspOrgId(), NumberUtils.LONG_ZERO))
                .collect(Collectors.toList());

        return filterHspOrgReference(refs1, apply, applySample);

    }


    // 2.2、若未命中，则根据【样本类型具体值】+【性别通用】，若命中，则取出参考范围，结束；
    //
    //2.3、若未命中，则根据【样本类型通用】+【性别具体值】，若命中，取参考范围，结束；
    //
    //2.4、若未命中，则根据【样本类型通用】+【性别通用】，若命中，取参考范围，结束；
    public InstrumentReportItemReferenceDto filterHspOrgReference(List<InstrumentReportItemReferenceDto> refs, ApplyDto apply, ApplySampleDto applySample ){

        if(CollectionUtils.isEmpty(refs)){
            return null;
        }

        // 样本类型 + 性别 都 命中
        InstrumentReportItemReferenceDto ref = refs.stream().filter(e -> Objects.equals(e.getSexStyle(), apply.getPatientSex()) &&
                        Objects.equals(applySample.getSampleTypeCode(), e.getSampleTypeCode()))
                .findFirst().orElse(null);

        if (Objects.nonNull(ref)) {
            return ref;
        }

        // 样本类型命中
        ref = refs.stream().filter(e -> Objects.equals(applySample.getSampleTypeCode(), e.getSampleTypeCode())
                        && Objects.equals(e.getSexStyle(), SexEnum.DEFAULT.getCode()))
                .findFirst().orElse(null);

        if (Objects.nonNull(ref)) {
            return ref;
        }

        // 性别命中
        ref = refs.stream().filter(e -> Objects.equals(e.getSexStyle(), apply.getPatientSex())
                        && Objects.equals(e.getSampleTypeCode(), SampleTypeEnum.ALL.getSampleTypeCode()))
                .findFirst().orElse(null);

        if (Objects.nonNull(ref)) {
            return ref;
        }

        // 双通用
        ref = refs.stream().filter(e -> Objects.equals(e.getSexStyle(), SexEnum.DEFAULT.getCode())
                        && Objects.equals(e.getSampleTypeCode(), SampleTypeEnum.ALL.getSampleTypeCode()))
                .findFirst().orElse(null);

        return ref;
    }

}
