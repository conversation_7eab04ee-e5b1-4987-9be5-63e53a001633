package com.labway.lims.outsourcing.service.chain.pick.two;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 二次分拣
 */
@Slf4j
@Component
public class OutsourcingTwoPickChain extends ChainBase implements InitializingBean {
    @Resource
    private OutsourcingTwoPickCheckParamCommand outsourcingTwoPickCheckParamCommand;
    @Resource
    private OutsourcingTwoPickFillInfoCommand outsourcingTwoPickFillInfoCommand;
    @Resource
    private OutsourcingTwoPickSaveInfoCommand outsourcingTwoPickSaveInfoCommand;
    @Resource
    private OutsourcingTwoPickCommonPhraseCommand outsourcingTwoPickCommonPhraseCommand;
    @Resource
    private OutSourcingTwoPickRecalculateCommand outSourcingTwoPickRecalculateCommand;
    @Resource
    private OutsourcingTwoPickSendCommand outsourcingTwoPickSendCommand;
    @Resource
    private OutsourcingTwoPickResultMarkCommand outsourcingTwoPickResultMarkCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 校验参数
        addCommand(outsourcingTwoPickCheckParamCommand);

        // 填充信息
        addCommand(outsourcingTwoPickFillInfoCommand);

        //添加带出项目
        addCommand(outSourcingTwoPickRecalculateCommand);

        // 保存
        addCommand(outsourcingTwoPickSaveInfoCommand);

        // 结果标记 , 之所以在常用短语前面，是因为常用短语会修改结果会重新覆盖掉
        addCommand(outsourcingTwoPickResultMarkCommand);

        //常用短语
        addCommand(outsourcingTwoPickCommonPhraseCommand);

        // 样本外送到业务中台
        addCommand(outsourcingTwoPickSendCommand);

        // 完成
        addCommand(c -> PROCESSING_COMPLETE);
    }
}
