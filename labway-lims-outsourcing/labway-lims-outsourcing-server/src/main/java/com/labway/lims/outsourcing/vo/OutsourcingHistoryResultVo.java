package com.labway.lims.outsourcing.vo;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/6 09:37
 */
@Getter
@Setter
public class OutsourcingHistoryResultVo {

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目id
     */
    private Long reportItemId;


    private List<ResultVo> results;

    @Getter
    @Setter
    public static class ResultVo {

        /**
         * 检验日期
         */
        private String testDate;

        /**
         * 结果
         */
        private String result;

        /**
         * 条码号
         */
        private String barcode;

        /**
         * 样本号
         */
        private String sampleNo;

        /**
         * 1: 危机
         * 2: 异常
         * 0: 正常
         * @see ResultStatusEnum
         */
        private Integer status;

        /**
         * 检验判断
         */
        private String testJudge;

    }
}
