package com.labway.lims.outsourcing.service.chain.pick.two;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.labway.business.center.compare.request.SendApplyFormInfoRequest;
import com.labway.business.center.compare.request.SendApplySampleInfoRequest;
import com.labway.business.center.compare.service.TbOrgApplySampleMainService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.config.OutsourcingConfig;
import com.swak.frame.dto.Response;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <pre>
 * OutsourcingTwoPickSendCommand
 * 二次分拣外送样本到业务中台
 * </pre>
 *
 * <AUTHOR>
 * @since 2023/11/22 14:22
 */
@Slf4j
@Component
public class OutsourcingTwoPickSendCommand implements Filter, Command {

    public static final String SIGN_ORG = "SIGN_ORG";
    public static final String IS_DIAN = "IS_DIAN";

    public static final String OUTSOURCING_SEND_TO_BIZ_CENTER_ORG_CODES = "OUTSOURCING_SEND_TO_BIZ_CENTER_ORG_CODES";
    public static final String OUTSOURCING_DIAN_ORG_CODES = "OUTSOURCING_DIAN_ORG_CODES";

    public static final String SOURCE = "LIMS";

    @DubboReference
    private SystemParamService systemParamService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Resource
    private TbOrgApplySampleMainService tbOrgApplySampleMainService;

    @Value("${business-center.org-code}")
    private String orgCode;

    @Autowired
    private OutsourcingConfig outsourcingConfig;

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {
        log.info("实验室外送样本到业务中台。。。");
        final OutsourcingTwoPickContext context = OutsourcingTwoPickContext.from(c);

        // 申请单信息
        final ApplyDto apply = context.getApply();
        log.info("外送申请单信息：{}", JSONObject.toJSONString(apply));

        // 申请单检验项目详情
        final List<TestItemDto> itemDetails = context.getApplySampleItemDetails();
        // 检验项目对应的机构信息
        final Map<Long, HspOrganizationDto> hspOrgMapByHspOrgId = hspOrganizationService.selectByHspOrgIds(
                itemDetails.stream().map(TestItemDto::getExportOrgId).collect(Collectors.toList())).stream()
                .filter(e -> Objects.equals(YesOrNoEnum.YES.getCode(), e.getIsExport()))
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity(), (a, b) -> a));

        // 获取外送机构编码配置：样本需要外送至业务中台
        final SystemParamDto sendToBizCenterOrgCodesParam = systemParamService.selectByParamName(OUTSOURCING_SEND_TO_BIZ_CENTER_ORG_CODES, apply.getOrgId());
        log.info("外送业务总台配置：{}",JSONObject.toJSONString(sendToBizCenterOrgCodesParam));

        // 获取外送机构编码配置：迪安
        final SystemParamDto dianOrgCodesParam = systemParamService.selectByParamName(OUTSOURCING_DIAN_ORG_CODES, apply.getOrgId());
        log.info("外送迪安配置：{}",JSONObject.toJSONString(sendToBizCenterOrgCodesParam));

        if (Objects.nonNull(sendToBizCenterOrgCodesParam)
                && StringUtils.isNotBlank(sendToBizCenterOrgCodesParam.getParamValue())) {
            log.info("进入外送判断。。。");
            // 判断是否是外送到迪安
            boolean isDian = itemDetails.stream()
                    .anyMatch(item -> Objects.nonNull(dianOrgCodesParam)
                            && Objects.equals(dianOrgCodesParam.getParamValue(), hspOrgMapByHspOrgId.get(item.getExportOrgId()).getHspOrgCode()));
            context.put(IS_DIAN, isDian);

            // 根据系统配置 判断是否需要外送到业务中台
            String[] sendToBizCenterOrgCodes = sendToBizCenterOrgCodesParam.getParamValue().split(StringPool.COMMA);
            boolean isSendToBizCenter = itemDetails.stream().anyMatch(item -> {
                HspOrganizationDto hspOrganization = hspOrgMapByHspOrgId.get(item.getExportOrgId());
                if (Objects.isNull(hspOrganization)) {
                    return false;
                }
                context.put(SIGN_ORG, hspOrganization);
                return Arrays.stream(sendToBizCenterOrgCodes)
                        .anyMatch(code -> {
                            String hspOrgCode = hspOrganization.getHspOrgCode();
                            return Objects.equals(code, hspOrgCode);
                        });
            });

            if (isSendToBizCenter) {
                log.info("执行调用业务中台外送检接口!!!");
                // 调用接口，外送到业务中台
                Response<?> response = tbOrgApplySampleMainService.sendApplySampleInfo(buildSendApplyFormInfo(context));

                if (!response.isSuccess()) {
                    log.error("外送到业务中台失败，{}", JSON.toJSONString(response));
                    throw new IllegalStateException("业务中台【" +response.getMsg() + "】");
                }
            }
        }

        log.info("正常执行完外送链！");
        return CONTINUE_PROCESSING;
    }

    private SendApplyFormInfoRequest buildSendApplyFormInfo(OutsourcingTwoPickContext context) {
        // 申请单信息
        final ApplyDto apply = context.getApply();
        final ApplySampleDto applySample = context.getApplySample();
        // 申请单检验项目
        final List<ApplySampleItemDto> applySampleItems = context.getApplySampleItems();
        final OutsourcingSampleDto outsourcingSampleDto = context.getSample();
        final HspOrganizationDto hspOrgDto = (HspOrganizationDto) context.get(SIGN_ORG);

        final String formCode = StringUtils.defaultIfBlank(outsourcingSampleDto.getFormCode(), String.valueOf(IdWorker.getId()));
        // 签收机构编码和名称
        final String signOrgCode = outsourcingConfig.getHspOrgCodeMap().getOrDefault(hspOrgDto.getHspOrgCode(), hspOrgDto.getHspOrgCode());
        final String signOrgName = hspOrgDto.getHspOrgName();
        // 是否是外送迪安
        final boolean isDian = (Boolean) context.get(IS_DIAN);

        final LoginUserHandler.User user = LoginUserHandler.get();

        // 检验项目
        final List<SendApplySampleInfoRequest.MainItem> mainItems = new ArrayList<>();
        for (ApplySampleItemDto item : applySampleItems) {
            SendApplySampleInfoRequest.MainItem tempItem = new SendApplySampleInfoRequest.MainItem();
            tempItem.setBarcode(applySample.getBarcode());
            tempItem.setOutTestItemCode(item.getTestItemCode());
            tempItem.setOutTestItemName(item.getTestItemName());
            mainItems.add(tempItem);
        }

        // 样本信息
        final SendApplySampleInfoRequest sendApplySampleInfoRequest = SendApplySampleInfoRequest.builder()
                .barcode(applySample.getBarcode())
                .hspOrgCode(orgCode)
                .hspOrgName(user.getOrgName())
                .orgCode(signOrgCode)
                .orgName(signOrgName)
                .applyType(apply.getApplyTypeName())
                .patientVisitCard(apply.getPatientVisitCard())
                .urgent(apply.getUrgent())
                .sampleType(applySample.getSampleTypeName())
                .sampleProperty(apply.getSampleProperty())
                .dept(apply.getDept())
                .inpatientArea(StringPool.EMPTY)
                .patientName(apply.getPatientName())
                .patientSex(apply.getPatientSex())
                .patientAge(apply.getPatientAge())
                .patientSubage(apply.getPatientSubage())
                .patientSubageUnit(apply.getPatientSubageUnit())
                .patientBirthday(apply.getPatientBirthday())
                .patientBed(apply.getPatientBed())
                .clinicalDiagnosis(apply.getDiagnosis())
                .patientCard(apply.getPatientCard())
                .patientCardType(apply.getPatientCardType())
                .patientAsddress(apply.getPatientAddress())
                .patientMobile(apply.getPatientMobile())
                .sendDoctor(apply.getSendDoctorName())
                .applyDate(apply.getApplyDate())
                .samplingDate(apply.getSamplingDate())
                .remark(apply.getRemark())
                .sampleSource(SOURCE)
                .targetOrgCode(signOrgCode)
                .targetOrgName(signOrgName)
                .formCode(formCode)
                .visitCardNo(apply.getPatientVisitCard())
                .tubeType(applySample.getTubeName())
                .sampleNum(1)
                // 实验室样本送检机构，这里使用lisCustomerCode保存，妇幼签收时使用这个字段传输原始的送检机构
                .lisCustomerCode(applySample.getHspOrgCode())
                .lisCustomerName(applySample.getHspOrgName())
                .isDian(isDian ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
                .mainItems(mainItems)
                .build();

        // 申请单信息
        return SendApplyFormInfoRequest.builder()
                .formCode(formCode)
                .hspOrgCode(orgCode)
                .hspOrgName(user.getOrgName())
                .orgCode(signOrgCode)
                .orgName(signOrgName)
                .formSource(SOURCE)
                .targetOrgCode(signOrgCode)
                .targetOrgName(signOrgName)
                .sendSampleCount(1)
                .takeSampleTime(null)
                .staffId("")
                .staffName("")
                .deliveryCode("")
                .imageUrl("")
                .handReceivePicCode("")
                .lisCustomerCode("")
                .lisCustomerName("")
                .sendRequest(Lists.newArrayList(sendApplySampleInfoRequest))
                .build();
    }

}
