package com.labway.lims.outsourcing.service.chain.retest.cancel;

import com.labway.lims.api.enums.routine.SampleCriticalResultStatusEnum;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 修改危机值状态
 */
@Slf4j
@Component
class OutsourcingCancelRetestSampleCriticalResultCommand implements Command {
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingCancelRetestContext context = OutsourcingCancelRetestContext.from(c);

        final List<Long> criticalValueIds = new ArrayList<>();

        // 查询所有危机值
        final List<SampleCriticalResultDto> sampleCriticalResults = sampleCriticalResultService.selectBySampleId(context.getOutsourcingSampleId());
        if (CollectionUtils.isEmpty(sampleCriticalResults)) {
            return CONTINUE_PROCESSING;
        }

        // 如果是取消单个
        if (Objects.nonNull(context.getReportItemId())) {
            final SampleCriticalResultDto sampleCriticalResult = sampleCriticalResults.stream().filter(e -> Objects.equals(e.getReportItemId(), context.getReportItemId()))
                    .findFirst().orElse(null);
            if (Objects.nonNull(sampleCriticalResult)) {
                criticalValueIds.add(sampleCriticalResult.getCriticalValueId());
            }
        } else {
            criticalValueIds.addAll(sampleCriticalResults.stream()
                    .filter(e -> Objects.equals(e.getStatus(), SampleCriticalResultStatusEnum.UNDER_REVIEW.getCode()))
                    .map(SampleCriticalResultDto::getCriticalValueId)
                    .collect(Collectors.toList()));
        }

        if (CollectionUtils.isEmpty(criticalValueIds)) {
            return CONTINUE_PROCESSING;
        }

        // 把危机值改为未处理
        final SampleCriticalResultDto dto = new SampleCriticalResultDto();
        dto.setStatus(SampleCriticalResultStatusEnum.UNPROCESSED.getCode());
        sampleCriticalResultService.updateByCriticalValueIds(dto, criticalValueIds);


        return CONTINUE_PROCESSING;
    }
}
