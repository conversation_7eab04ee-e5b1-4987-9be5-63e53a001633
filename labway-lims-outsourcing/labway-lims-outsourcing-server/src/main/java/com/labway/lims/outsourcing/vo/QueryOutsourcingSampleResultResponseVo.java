package com.labway.lims.outsourcing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class QueryOutsourcingSampleResultResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 外送样本ID
     */
    private Long outsourcingSampleId;

    /**
     * 状态
     *
     * @see SampleAuditStatusEnum
     */
    private Integer status;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 检验日期（来源外送机构
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date testDate;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;


    /**
     * 外送机构名称
     */
    private String exportOrgName;

    /**
     * 是否对照，如果检验项目或报告项目包含一个未对照 那么这件就是未对照
     */
    private Boolean itemMapping;

    /**
     * 外送日期
     */
    private Date sendDate;

    /**
     * 结果
     */
    private List<Result> results;

    @Getter
    @Setter
    public static class Result implements Serializable {

        /**
         * 无用。给前端做唯一ID
         */
        private String id;

        /**
         * 报告项目名称
         */
        private String reportItemName;

        /**
         * 报告项目编码
         */
        private String reportItemCode;

        /**
         * 结果
         */
        private String result;

        /**
         * 项目编码
         */
        private String testItemCode;

        /**
         * 项目名称
         */
        private String testItemName;

        /**
         * 参考范围
         */
        private String range;

        /**
         * UP or DOWN
         *
         * @see TestJudgeEnum
         */
        private String judge;

        /**
         * 单位
         */
        private String unit;

        /**
         * 对照的项目名
         */
        private List<String> mappingItemNames;



        /**
         * 1: 危机
         * 2: 异常
         * 0: 正常
         *
         * @see ResultStatusEnum
         */
        private Integer status;

        /**
         * 检验项目对照错误
         */
        private Boolean testItemMappingError;

        /**
         * 检验项目对照错误
         */
        private String testItemMappingErrorText;

        /**
         * 报告项目对照错误
         */
        private Boolean reportItemMappingError;


        /**
         * 报告项目对照错误
         */
        private String reportItemMappingErrorText;

        /**
         * 单位比对错误
         */
        private Boolean unitDiffError;
        private String unitDiffErrorText;
        /**
         * 参考范围比对错误
         */
        private Boolean rangeDiffError;
        private String rangeDiffErrorText;

        /**
         * 迪安报告项目名称
         */
        private String dianReportItemName;

        /**
         * 迪安报告项目编码
         */
        private String dianReportItemCode;

        /**
         * 迪安项目编码
         */
        private String dianTestItemCode;

        /**
         * 迪安项目名称
         */
        private String dianTestItemName;

    }


}
