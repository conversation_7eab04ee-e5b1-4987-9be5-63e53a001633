package com.labway.lims.outsourcing.service.chain.audit;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.enums.routine.SampleCriticalResultStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RequestParamCommand implements Command {
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        AuditSampleContext context = AuditSampleContext.from(c);
        LoginUserHandler.User user = context.getUser();
        // 是否需要一审
        boolean oneCheck = context.isOneCheck();

        // 要审核的微生物样本ids
        Collection<Long> outsourcingSampleIds = context.getOutsourcingSampleIds();

        List<OutsourcingSampleDto> outsourcingSampleDtos =
                outsourcingSampleService.selectByOutsourcingSampleIds(outsourcingSampleIds);

        final Set<Long> selectOutsourcingSampleIds = outsourcingSampleDtos.stream()
                .map(OutsourcingSampleDto::getOutsourcingSampleId).collect(Collectors.toSet());

        if (outsourcingSampleIds.stream().anyMatch(x -> !selectOutsourcingSampleIds.contains(x))) {
            throw new IllegalStateException("存在无效外送样本");
        }

        // 对应申请单信息
        Set<Long> applyIds =
                outsourcingSampleDtos.stream().map(OutsourcingSampleDto::getApplyId).collect(Collectors.toSet());

        List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);

        Set<Long> selectApplyIds = applyDtos.stream().map(ApplyDto::getApplyId).collect(Collectors.toSet());

        if (applyIds.stream().anyMatch(x -> !selectApplyIds.contains(x))) {
            throw new IllegalStateException("存在无效外送样本:没有对应申请单");
        }

        // 对应申请单样本ids
        final Set<Long> applySampleIds =
                outsourcingSampleDtos.stream().map(OutsourcingSampleDto::getApplySampleId).collect(Collectors.toSet());

        final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);

        if (!oneCheck && applySampleDtos.stream().anyMatch(e -> LoginUserHandler.get().getUserId().equals(e.getTesterId()))) {
            throw new IllegalStateException("检验者与审核者不能为同一用户");
        }

        if (oneCheck && outsourcingSampleDtos.stream().anyMatch(e -> LoginUserHandler.get().getUserId().equals(e.getOneCheckerId()))) {
            throw new IllegalStateException("一审和二审不能为同一用户");
        }

        if (oneCheck && Objects.equals(context.getAuditType(), SampleAuditStatusEnum.TWO_CHECK) && applySampleDtos.stream()
                .anyMatch(obj -> !Objects.equals(obj.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new IllegalStateException("已选数据存在未一审样本，不可审核");
        }

        Set<Long> selectApplySampleIds =
                applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());
        if (applySampleIds.stream().anyMatch(x -> !selectApplySampleIds.contains(x))) {
            throw new IllegalStateException("存在无效外送样本:没有对应申请单样本");
        }

        // 一审 检查 是否存在 危急值的报告项目
        List<SampleCriticalResultDto> sampleCriticalResultDtos =
                sampleCriticalResultService.selectBySampleIds(selectOutsourcingSampleIds).stream()
                        .filter(obj -> !Objects.equals(obj.getStatus(), SampleCriticalResultStatusEnum.PROCESSED.getCode()))
                        .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(sampleCriticalResultDtos)) {
            throw new IllegalStateException("存在未处理完成的危急值项目，请处理完成再进行审核");
        }

        // 必须每一个都是未审核状态
        for (ApplySampleDto applySample : applySampleDtos) {

            if (applySampleService.isDisabled(applySample.getApplySampleId())) {
                throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", applySample.getBarcode()));
            }

            if (applySampleService.isTerminate(applySample.getApplySampleId())) {
                throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", applySample.getBarcode()));
            }

            if (Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
                throw new IllegalStateException(String.format("条码号:%s , 已审核", applySample.getBarcode()));
            }

            if (Objects.equals(applySample.getStatus(), SampleStatusEnum.RETEST.getCode())) {
                throw new IllegalStateException(String.format("条码号:%s , 正在复查不能审核", applySample.getBarcode()));
            }

            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
                throw new IllegalStateException(String.format("条码号:%s , 不能审核", applySample.getBarcode()));
            }

        }

        // 样本报告项目
        List<SampleReportItemDto> sampleReportItemDtos =
                sampleReportItemService.selectBySampleIds(outsourcingSampleIds);

        // 样本结果
        List<SampleResultDto> sampleResultDtos = sampleResultService.selectBySampleIds(outsourcingSampleIds);

        // 样本 上仪器专业小组对应报告项目
        Set<Long> instrumentGroupIds =
                outsourcingSampleDtos.stream().map(OutsourcingSampleDto::getInstrumentGroupId).collect(Collectors.toSet());
        Map<Long, List<InstrumentReportItemDto>> instrumentReportItemByInstrumentGroupId = new HashMap<>();
        for (Long instrumentGroupId : instrumentGroupIds) {
            instrumentReportItemByInstrumentGroupId.put(instrumentGroupId,
                    instrumentReportItemService.selectByInstrumentGroupId(instrumentGroupId));
        }

        context.put(AuditSampleContext.OUTSOURCING_SAMPLE_LIST, outsourcingSampleDtos);
        context.put(AuditSampleContext.APPLY, applyDtos);
        context.put(AuditSampleContext.APPLY_SAMPLE, applySampleDtos);
        context.put(AuditSampleContext.SAMPLE_REPORT_ITEM, sampleReportItemDtos);
        context.put(AuditSampleContext.SAMPLE_RESULT, sampleResultDtos);
        context.put(AuditSampleContext.INSTRUMENT_REPORT_ITEM, instrumentReportItemByInstrumentGroupId);

        return CONTINUE_PROCESSING;
    }
}
