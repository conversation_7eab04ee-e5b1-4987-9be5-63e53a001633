package com.labway.lims.outsourcing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/7/26 13:07
 */
@Getter
@Setter
public class ExportTimeoutTestItemVo {

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

    /**
     * 外送机构编码
     */
    private String exportOrgCode;

    /**
     * 外送机构名称
     */
    private String exportOrgName;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 检验项目ID
     */
    private Long testItemId;
    /**
     * 检验项目编码
     */
    private String testItemCode;
    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 性别
     *
     * @see com.labway.lims.api.enums.apply.SexEnum
     */
    private Integer patientSex;

    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 患者子年龄
     */
    private Integer patientSubage;

    /**
     * 患者子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 已经超时时间
     */
    private BigDecimal timeOutDate;

    /**
     * 外送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date sendOutDate;

    /**
     * 外送回传报告时间
     */
    private String exportDate;

}
