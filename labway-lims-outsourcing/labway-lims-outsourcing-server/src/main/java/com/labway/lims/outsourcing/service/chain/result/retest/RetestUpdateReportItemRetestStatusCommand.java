package com.labway.lims.outsourcing.service.chain.result.retest;

import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 修改报告项目的复查状态
 */
@Slf4j
@Component
class RetestUpdateReportItemRetestStatusCommand implements Command {

    @DubboReference
    private SampleReportItemService sampleReportItemService;

    @Override
    public boolean execute(Context c) throws Exception {
        final RetestResultContext context = RetestResultContext.from(c);

        if (!context.isComplete()) {
            return CONTINUE_PROCESSING;
        }

        // 复查的报告项目ID
        final Set<Long> reportItemIds = context.getSampleRetestItems().stream()
                .map(SampleRetestItemDto::getReportItemId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(reportItemIds)) {
            return CONTINUE_PROCESSING;
        }

        final List<SampleReportItemDto> sampleReportItems = context.getSampleReportItems().stream().filter(e -> reportItemIds.contains(e.getReportItemId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            return CONTINUE_PROCESSING;
        }


        final SampleReportItemDto sampleReportItem = context.getSampleReportItem();

        final SampleReportItemDto m = new SampleReportItemDto();
        m.setSampleReportItemId(sampleReportItem.getSampleReportItemId());
        m.setSampleId(context.getSample().getOutsourcingSampleId());
        m.setIsRetest(RetestStatusEnum.RETEST.getCode());

        sampleReportItem.setIsRetest(RetestStatusEnum.RETEST.getCode());

        sampleReportItemService.updateBySampleReportItemIds(m, sampleReportItems.stream()
                .map(SampleReportItemDto::getSampleReportItemId).collect(Collectors.toSet()));



        return CONTINUE_PROCESSING;

    }
}
