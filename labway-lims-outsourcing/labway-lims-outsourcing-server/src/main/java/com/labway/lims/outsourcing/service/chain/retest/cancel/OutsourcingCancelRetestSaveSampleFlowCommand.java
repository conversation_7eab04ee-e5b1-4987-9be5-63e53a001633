package com.labway.lims.outsourcing.service.chain.retest.cancel;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/5/29 20:02
 */
@Slf4j
@Component
public class OutsourcingCancelRetestSaveSampleFlowCommand implements Command {
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingCancelRetestContext context = OutsourcingCancelRetestContext.from(c);
        final SampleRetestMainDto main = context.getSampleRetestMain();
        final LoginUserHandler.User user = LoginUserHandler.get();

        final List<String> unRetestItemNames = context.getSampleUnRetestItemNames();

        if (CollectionUtils.isNotEmpty(unRetestItemNames)) {
            final StringBuilder sb = new StringBuilder();
            for (String name : unRetestItemNames) {
                sb.append(String.format("报告项目 [%s] 取消复查 \n", name));
            }
            final SampleFlowDto s = new SampleFlowDto();
            s.setSampleFlowId(snowflakeService.genId());
            s.setApplyId(context.getOutsourcingSample().getApplyId());
            s.setApplySampleId(context.getOutsourcingSample().getApplySampleId());
            s.setBarcode(context.getOutsourcingSample().getBarcode());
            s.setOperateCode(BarcodeFlowEnum.CANCEL_RETEST_RESULT.name());
            s.setOperateName(BarcodeFlowEnum.CANCEL_RETEST_RESULT.getDesc());
            s.setOperatorId(LoginUserHandler.get().getUserId());
            s.setOperator(LoginUserHandler.get().getNickname());
            s.setContent(sb.toString());
            s.setCreateDate(new Date());
            s.setCreatorId(user.getUserId());
            s.setCreatorName(user.getNickname());
            s.setUpdaterName(user.getNickname());
            s.setUpdaterId(user.getUserId());
            s.setUpdateDate(new Date());
            s.setOrgId(user.getOrgId());
            s.setIsDelete(YesOrNoEnum.NO.getCode());
            s.setOrgName(user.getOrgName());
            sampleFlowService.addSampleFlow(s);
        }


        if (!Objects.equals(main.getStatus(), SampleRetestStatusEnum.RETEST.getCode())) {
            return CONTINUE_PROCESSING;
        }

        final List<SampleRetestItemDto> sampleRetestItems = context.getSampleRetestItems();

        if (CollectionUtils.isNotEmpty(sampleRetestItems)) {
            // 完成复查过的报告项目
            final StringBuilder sb = new StringBuilder();

            for (SampleRetestItemDto ir : sampleRetestItems) {
                sb.append(String.format("报告项目 [%s] 复查结果 [%s] \n", ir.getReportItemName(), ir.getResult()));
            }

            final SampleFlowDto s = new SampleFlowDto();
            s.setSampleFlowId(snowflakeService.genId());
            s.setApplyId(context.getOutsourcingSample().getApplyId());
            s.setApplySampleId(context.getOutsourcingSample().getApplySampleId());
            s.setBarcode(context.getOutsourcingSample().getBarcode());
            s.setOperateCode(BarcodeFlowEnum.COMPLETE_RETEST_RESULT.name());
            s.setOperateName(BarcodeFlowEnum.COMPLETE_RETEST_RESULT.getDesc());
            s.setOperatorId(LoginUserHandler.get().getUserId());
            s.setOperator(LoginUserHandler.get().getNickname());
            s.setContent(sb.toString());
            s.setCreateDate(new Date());
            s.setCreatorId(user.getUserId());
            s.setCreatorName(user.getNickname());
            s.setUpdaterName(user.getNickname());
            s.setUpdaterId(user.getUserId());
            s.setUpdateDate(new Date());
            s.setOrgId(user.getOrgId());
            s.setIsDelete(YesOrNoEnum.NO.getCode());
            s.setOrgName(user.getOrgName());
            sampleFlowService.addSampleFlow(s);
        }

        return CONTINUE_PROCESSING;
    }
}
