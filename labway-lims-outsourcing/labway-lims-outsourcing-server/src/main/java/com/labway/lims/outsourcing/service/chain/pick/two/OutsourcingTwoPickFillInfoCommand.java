package com.labway.lims.outsourcing.service.chain.pick.two;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.TestItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 填充信息
 */
@Slf4j
@Component
class OutsourcingTwoPickFillInfoCommand implements Filter, Command {
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private TestItemService testItemService;

    @Override
    public boolean postprocess(Context c, Exception exception) {

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {

        final OutsourcingTwoPickContext context = OutsourcingTwoPickContext.from(c);
        final Long applySampleId = context.getApplySampleId();
        final Long instrumentGroupId = context.getInstrumentGroupId();

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存,分拣失败");
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在,分拣失败");
        }

        final InstrumentGroupDto instrumentGroup = instrumentGroupService.selectByInstrumentGroupId(instrumentGroupId);
        if (Objects.isNull(instrumentGroup)) {
            throw new IllegalStateException("专业小组不存在");
        }

        final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplySampleId(applySampleId).stream()
                .filter(e -> Objects.equals(e.getItemType(), ItemTypeEnum.OUTSOURCING.name()))
                // 过滤掉非当前专业组的项目
                .filter(e -> Objects.equals(e.getGroupId(), instrumentGroup.getGroupId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new IllegalStateException("当前样本下没有检验项目");
        }

        // 获取申请单检验项目详情
        List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(applySampleItems.stream().map(ApplySampleItemDto::getTestItemId)
                .collect(Collectors.toSet()));

        final List<ReportItemDto> reportItems = reportItemService.selectByTestItemIds(applySampleItems.stream().map(ApplySampleItemDto::getTestItemId)
                .collect(Collectors.toList()));
        final List<String> reportItemCodes = reportItems.stream().map(ReportItemDto::getReportItemCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reportItemCodes)) {
            throw new IllegalStateException("没有可以分拣的报告项目");
        }

        // 报告项目所属的检验项目
        Map<String, Long> reportItemCode2TestItemIdMap = reportItems.stream().collect(Collectors.toMap(ReportItemDto::getReportItemCode,
                ReportItemDto::getTestItemId, (a, b) -> a));

        final List<InstrumentReportItemDto> insgReportItems = instrumentReportItemService.selectByInstrumentGroupId(instrumentGroupId)
                .stream().filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode())).collect(Collectors.toList());
        final List<InstrumentReportItemDto> instrumentReportItems = insgReportItems
                .stream().filter(e -> CollectionUtils.containsAny(reportItemCodes, e.getReportItemCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(instrumentReportItems)) {
            throw new IllegalStateException(String.format("仪器没有绑定 [%s] 报告项目",
                    String.join("、", reportItemCodes)));
        }

        final Long instrumentId = instrumentReportItems.stream().collect(Collectors.groupingBy(InstrumentReportItemDto::getInstrumentId))
                .entrySet().stream().max((o1, o2) -> NumberUtils.compare(o1.getValue().size(), o2.getValue().size())).map(Map.Entry::getKey).orElse(NumberUtils.LONG_ZERO);

        final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentId);
        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("仪器不存在");
        }

        context.put(OutsourcingTwoPickContext.APPLY, apply);
        context.put(OutsourcingTwoPickContext.APPLY_SAMPLE, applySample);
        context.put(OutsourcingTwoPickContext.INSTRUMENT_GROUP, instrumentGroup);
        context.put(OutsourcingTwoPickContext.INSTRUMENT, instrument);
        context.put(OutsourcingTwoPickContext.APPLY_SAMPLE_ITEMS, applySampleItems);
        context.put(OutsourcingTwoPickContext.REPORT_ITEMS, new ArrayList<>(reportItems.stream()
                .collect(Collectors.toMap(ReportItemDto::getReportItemCode, v -> v, (a, b) -> a)).values()));
        context.put(OutsourcingTwoPickContext.INSTRUMENT_REPORT_ITEMS, insgReportItems);
        context.put(OutsourcingTwoPickContext.APPLY_SAMPLE_ITEM_DETAILS, testItemDtos);
        context.put(OutsourcingTwoPickContext.REPORT_ITEM_CODE_2_TEST_ITEM_ID, reportItemCode2TestItemIdMap);

        return CONTINUE_PROCESSING;
    }
}
