package com.labway.lims.outsourcing.service.chain.sync;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class SyncOutsourcing<PERSON>hain extends ChainBase implements InitializingBean {

    @Resource
    private SyncOutsourcingCheckParamChain syncOutsourcingCheckParamChain;
    @Resource
    private SyncAndSaveResultCommandChain syncAndSaveResultCommandChain;
    @Resource
    private SyncOutsourcingGetAndSaveResultsCommand syncOutsourcingGetAndSaveResultsCommand;
    @Resource
    private SyncOutsourcingGetResultsChain syncOutsourcingGetResultsChain;
    @Resource
    private SyncOutsourcingGetResultsPostCommand syncOutsourcingGetResultsPostCommand;
    @Resource
    private SyncOutsourcingBuildReportCommand syncOutsourcingBuildReportCommand;
    @Resource
    private SyncOutsourcingUpdateSampleAuditStatusCommand syncOutsourcingUpdateSampleAuditStatusCommand;
    @Resource
    private SyncOutsourcingSampleAuditFlowCommand syncOutsourcingSampleAuditFlowCommand;
    @Resource
    private SyncOutsourcingSampleAuditRabbitMqCommand syncOutsourcingSampleAuditRabbitMqCommand;
    @Resource
    private SyncOutsourcingSampleResultFlowCommand syncOutsourcingSampleResultFlowCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 校验参数
        addCommand(syncOutsourcingCheckParamChain);

        // 获取结果并保存
        addCommand(syncAndSaveResultCommandChain);

        /*// 获取结果
        addCommand(syncOutsourcingGetResultsChain);

        // 处理调用外部结果的数据
        addCommand(syncOutsourcingGetResultsPostCommand);

        // 构建PDF
        addCommand(syncOutsourcingBuildReportCommand);

        // 添加结果条码环节
        addCommand(syncOutsourcingSampleResultFlowCommand);

        // 条码环节
        addCommand(syncOutsourcingSampleAuditFlowCommand);

        // 发送 mq
        addCommand(syncOutsourcingSampleAuditRabbitMqCommand);

        // 修改状态
        addCommand(syncOutsourcingUpdateSampleAuditStatusCommand);*/

        // over
        addCommand((c) -> PROCESSING_COMPLETE);

    }
}
