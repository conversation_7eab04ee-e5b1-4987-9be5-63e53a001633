package com.labway.lims.outsourcing.service.chain.his.sign;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.HisTestApplyDto;
import com.labway.lims.apply.api.dto.HisTestApplyItemDto;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.outsourcing.api.dto.BloodCultureDto;
import com.labway.lims.outsourcing.api.dto.SignHisSampleItemDto;
import com.labway.lims.outsourcing.api.dto.his.HisSample;
import com.labway.lims.outsourcing.api.dto.his.HisSampleItem;
import com.labway.lims.outsourcing.api.dto.his.HisSignParam;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.outsourcing.service.chain.his.sign.SignHisSampleContext.APPLY_INFO;
import static com.labway.lims.outsourcing.service.chain.his.sign.SignHisSampleContext.HIS_TEST_APPLY;

/**
 * <pre>
 * SignHisSampleBuildHisTestApplyCommand
 * 添加申请单信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/15 19:04
 */
@Component
public class SignHisSampleAddApplyCommand implements Command {

    @DubboReference
    private ApplyService applyService;

    @Override
    public boolean execute(Context context) throws Exception {
        SignHisSampleContext from = SignHisSampleContext.from(context);
        final HisSignParam sign = from.getHisSignParam();

        // 参数
        final List<SignHisSampleItemDto> items = sign.getItems();
        final Collection<String> getHisSampleItemIds =
                items.stream().map(SignHisSampleItemDto::getHisSampleItemId).collect(Collectors.toSet());

        final HisSample hisSample = from.getHisSample();
        final List<HisSampleItem> hisSampleItems = from.getHisSampleItems();
        final Map<Long, TestItemDto> testItems = from.getTestItems();

        HisTestApplyDto hisTestApply = new HisTestApplyDto();
        hisTestApply.setOutBarcode(hisSample.getOutBarcode());
        hisTestApply.setHspOrgId(hisSample.getHspOrgId());
        hisTestApply.setPatientName(hisSample.getPatientName());
        hisTestApply.setPatientSex(hisSample.getPatientSex());
        hisTestApply.setPatientBirthday(hisSample.getPatientBirthday());
        hisTestApply.setPatientAge(hisSample.getPatientAge());
        hisTestApply.setPatientSubage(hisSample.getPatientSubage());
        hisTestApply.setPatientSubageUnit(hisSample.getPatientSubageUnit());
        hisTestApply.setUrgent(hisSample.getUrgent());
        hisTestApply
                .setSampleCount(ObjectUtils.defaultIfNull(hisSample.getSampleCount(), NumberUtils.INTEGER_ZERO));
        hisTestApply.setApplyDate(hisSample.getApplyDate());
        hisTestApply.setSamplingDate(hisSample.getSamplingDate());
        hisTestApply.setPatientVisitCard(hisSample.getPatientVisitCard());
        hisTestApply.setDept(hisSample.getDept());
        hisTestApply.setPatientBed(hisSample.getPatientBed());
        hisTestApply.setClinicalDiagnosis(hisSample.getClinicalDiagnosis());
        hisTestApply.setRemark(hisSample.getRemark());
        hisTestApply.setPatientMobile(hisSample.getPatientMobile());
        hisTestApply.setPatientCard(hisSample.getPatientCard());
        hisTestApply.setPatientCardType(hisSample.getPatientCardType());
        hisTestApply.setSendDoctor(hisSample.getSendDoctor());
        hisTestApply.setPatientAddress(hisSample.getPatientAddress());
        hisTestApply.setApplySource(ApplySourceEnum.HIS);
        hisTestApply.setSupplier("业务中台");
        // 荔湾 LIMS
        hisTestApply.setSampleProperty(StringUtils.defaultIfBlank(hisSample.getSampleProperty(), "正常"));
        hisTestApply.setSamplePropertyCode(StringUtils.defaultIfBlank(hisSample.getSamplePropertyCode(), "00102004-00000000"));

        hisTestApply.setApplyTypeCode(hisSample.getApplyTypeCode());
        hisTestApply.setApplyTypeName(hisSample.getApplyTypeName());

        final List<HisTestApplyItemDto> hisTestApplyItems = hisSampleItems.stream().filter(f -> getHisSampleItemIds.contains(f.getHisSampleItemId())).map(m -> {
            HisTestApplyItemDto hisTestApplyItem = new HisTestApplyItemDto();
            hisTestApplyItem.setOutTestItemName(m.getOutTestItemName());
            hisTestApplyItem.setOutTestItemCode(m.getOutTestItemCode());
            hisTestApplyItem.setTestItemId(m.getTestItemId());
            hisTestApplyItem.setTestItemName(m.getTestItemName());
            hisTestApplyItem.setCount(m.getCount());
            hisTestApplyItem.setUrgent(m.getUrgent());
            hisTestApplyItem.setCustomCode(StringUtils.EMPTY);

            final TestItemDto testItem = testItems.get(m.getTestItemId());
            if (Objects.isNull(testItem)) {
                throw new IllegalStateException(String.format("检验项目 [%s] 不存在", m.getTestItemName()));
            }

            // 如果是血培养项目
            if (Objects.equals(testItem.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())) {
                final SignHisSampleItemDto signHisSampleItem = items.stream().filter(f -> Objects.equals(f.getTestItemCode(), m.getTestItemCode()))
                        .findFirst().orElse(null);

                if (Objects.isNull(signHisSampleItem)) {
                    throw new IllegalStateException("不存在血培养项目");
                }

                // 血培养信息
                final BloodCultureDto bloodCulture = signHisSampleItem.getBloodCulture();
                if (Objects.nonNull(bloodCulture)) {
                    hisTestApplyItem.setBloodCulture(JSON.parseObject(JSON.toJSONString(bloodCulture),
                            ApplySampleItemBloodCultureDto.class));
                }

                // 备注
                hisTestApplyItem.setRemark(signHisSampleItem.getRemark());
            }
            return hisTestApplyItem;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(hisTestApplyItems)) {
            throw new IllegalStateException(String.format("条码 [%s] 没有需要签收的项目", sign.getOutBarcode()));
        }

        // 修改管型和样本类型s
        outsourcingSignUpdateTubeAndSampleType(sign, hisTestApplyItems);

        hisTestApply.setHisTestApplyItems(hisTestApplyItems);
        hisTestApply.setOriginalOrgCode(hisSample.getOriginalOrgCode());
        hisTestApply.setOriginalOrgName(hisSample.getOriginalOrgName());
        hisTestApply.setIgnoreSameItem(BooleanUtils.isTrue(sign.getIgnoreDuplicateSignItem()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        // 检验项目限制性别 忽略校验
        hisTestApply.setIgnoreItemLimitSex(sign.getIgnoreItemLimitSex());
        // 添加申请单
        final ApplyInfo applyInfo = applyService.addApply(hisTestApply);

        final List<ApplyInfo.Sample> samples = applyInfo.getSamples();
        if (CollectionUtils.isEmpty(samples)) {
            throw new IllegalStateException("签收失败，样本信息为空");
        }

        from.put(HIS_TEST_APPLY, hisTestApply);
        from.put(APPLY_INFO, applyInfo);

        return CONTINUE_PROCESSING;
    }

    /**
     * 修改管型和样本类型
     */
    public static void outsourcingSignUpdateTubeAndSampleType(HisSignParam sign, List<HisTestApplyItemDto> testItems) {
        List<SignHisSampleItemDto> items = sign.getItems();
        if (CollectionUtils.isNotEmpty(items)) {
            Map<Long, SignHisSampleItemDto> testItemParamMap = items.stream().collect(Collectors.toMap(SignHisSampleItemDto::getTestItemId, Function.identity(), (a, b) -> a));

            for (HisTestApplyItemDto testItem : testItems) {
                Long testItemId = testItem.getTestItemId();
                SignHisSampleItemDto testApplyItemDto = testItemParamMap.get(testItemId);
                if (StringUtils.isNotBlank(testApplyItemDto.getTubeCode())) {
                    testItem.setTubeCode(testApplyItemDto.getTubeCode());
                }
                if (StringUtils.isNotBlank(testApplyItemDto.getTubeName())) {
                    testItem.setTubeName(testApplyItemDto.getTubeName());
                }
                if (StringUtils.isNotBlank(testApplyItemDto.getSampleTypeCode())) {
                    testItem.setSampleTypeCode(testApplyItemDto.getSampleTypeCode());
                }
                if (StringUtils.isNotBlank(testApplyItemDto.getSampleType())) {
                    testItem.setSampleTypeName(testApplyItemDto.getSampleType());
                }
            }
        }
    }

}
