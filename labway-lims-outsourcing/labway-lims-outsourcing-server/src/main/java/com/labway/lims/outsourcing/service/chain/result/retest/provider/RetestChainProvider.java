package com.labway.lims.outsourcing.service.chain.result.retest.provider;


import com.labway.lims.outsourcing.service.chain.result.RecalculateRefResultCommand;
import com.labway.lims.outsourcing.service.chain.result.SaveResultContext;
import com.labway.lims.outsourcing.service.chain.result.retest.RetestResultChain;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Getter
@Setter
@Component
public class RetestChainProvider implements Command, RecalculateRefResultCommand.ChainProvider {

    @Resource
    private RetestResultChain retestResultChain;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext from = SaveResultContext.from(c);
        from.put(SaveResultContext.CHAIN_PROVIDER, this);
        return CONTINUE_PROCESSING;
    }

    @Override
    public ChainBase chain() {
        return retestResultChain;
    }
}
