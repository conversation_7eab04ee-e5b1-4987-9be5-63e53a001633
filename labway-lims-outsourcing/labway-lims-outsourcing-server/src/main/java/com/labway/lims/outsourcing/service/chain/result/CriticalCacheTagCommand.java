package com.labway.lims.outsourcing.service.chain.result;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.routine.api.service.SampleResultService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;


/**
 * 危急值缓存标记
 */
@Component
public class CriticalCacheTagCommand implements Command {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @DubboReference
    private SampleResultService sampleResultService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SaveResultContext c = SaveResultContext.from(context);
        final OutsourcingSampleDto sample = c.getSample();
        final Long outsourcingSampleId = sample.getOutsourcingSampleId();
        // 获取当前样本的危急值结果的数量
        long criticalCount = sampleResultService.countSampleResultCriticalQuantity(outsourcingSampleId);

        final String criticalKey = getCriticalKey(sample.getOutsourcingSampleId());
        // 危急值存redis
        stringRedisTemplate.opsForHash().put(criticalKey, String.valueOf(sample.getOutsourcingSampleId()),
                String.valueOf(criticalCount > 0 ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode()));
        // 缓存180天
        stringRedisTemplate.expire(criticalKey, 3 * 60, TimeUnit.DAYS);

        return false;
    }

    public String getCriticalKey(long outsourcingSampleId) {
        return redisPrefix.getBasePrefix() + "OUTSOURCING_SAMPLE_IS_CRITICAL:" + outsourcingSampleId;
    }
}
