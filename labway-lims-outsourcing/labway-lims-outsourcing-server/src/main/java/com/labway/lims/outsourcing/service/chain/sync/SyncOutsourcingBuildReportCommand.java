package com.labway.lims.outsourcing.service.chain.sync;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.business.center.compare.dto.OrgApplyResultSamplesDTO;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SyncOutsourcingBuildReportCommand implements Command {

    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;

    @Resource
    private Environment environment;

    @DubboReference
    private PdfReportService pdfReportService;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private ReportTemplateBindService reportTemplateBindService;

    @DubboReference
    private ApplyService applyService;

    @Resource
    private SyncOutsourcingGetResultsChain syncOutsourcingGetResultsChain;

    @Resource
    private AuditConfig auditConfig;

    @Resource
    private HuaweiObsUtils huaweiObsUtils;

    @Override
    public boolean execute(Context c) throws Exception {
        final SyncOutsourcingContext context = SyncOutsourcingContext.from(c);
        final List<OutsourcingSampleDto> samples = new LinkedList<>(context.getOutsourcingSamples());
        final List<SampleReportDto> sampleReports = Lists.newArrayList();

        final Map<Long, ApplyDto> applies = applyService.selectByApplyIdsAsMap(samples.stream().map(OutsourcingSampleDto::getApplyId)
                .collect(Collectors.toSet()));
        context.setApplies(applies);

        {
            // 判断样本是否已经上传过报告单
            List<Long> applySampleIds = samples.stream().map(OutsourcingSampleDto::getApplySampleId).collect(Collectors.toList());

            List<Long> uploadReportApplysampleIds = sampleReportService.selectByApplySampleIds(applySampleIds).stream()
                    .filter(e -> Objects.equals(e.getIsUploadPdf(), YesOrNoEnum.YES.getCode()))
                    .map(SampleReportDto::getApplySampleId)
                    .collect(Collectors.toList());

            final List<OutsourcingSampleDto> nonUploadPdfSamples = samples.stream()
                    .filter(e -> !uploadReportApplysampleIds.contains(e.getApplySampleId()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(nonUploadPdfSamples)) {
                return CONTINUE_PROCESSING;
            }
        }

        // 每个样本 对应一个报告单 删除 样本原来对应报告单
        sampleReportService.deleteBySampleIds(samples.stream().map(OutsourcingSampleDto::getOutsourcingSampleId)
                .collect(Collectors.toSet()));

        final Map<Long, List<ApplySampleItemDto>> sampleItems = applySampleItemService.selectByApplyIds(samples.stream().map(OutsourcingSampleDto::getApplySampleId)
                .collect(Collectors.toSet())).stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        final Set<String> outsidePdfs = ObjectUtils.defaultIfNull(auditConfig.getOutsidePdfs(), Set.of());

        for (final Iterator<OutsourcingSampleDto> iterator = samples.iterator(); iterator.hasNext(); ) {
            final OutsourcingSampleDto sample = iterator.next();
            final OrgApplyResultSamplesDTO outSample = syncOutsourcingGetResultsChain.getOutSample(c, sample.getOutsourcingSampleId());
            // 如果全部用外部PDF
            if (CollectionUtils.isNotEmpty(outsidePdfs) && Objects.nonNull(outSample) && CollectionUtils.containsAll(outsidePdfs, sampleItems.getOrDefault(sample.getOutsourcingSampleId(), List.of())
                    .stream().map(ApplySampleItemDto::getTestItemCode).collect(Collectors.toSet()))) {
                iterator.remove();

                final SampleReportDto report = new SampleReportDto();
                report.setApplySampleId(sample.getApplySampleId());
                report.setApplyId(sample.getApplyId());
                report.setSampleId(sample.getOutsourcingSampleId());
                report.setBarcode(sample.getBarcode());
                final File tempFile = FileUtil.createTempFile();

                // 重新下载到 obs
                HttpUtil.downloadFile(outSample.getReportUrls(), tempFile, 15000);
                try (final FileInputStream fis = new FileInputStream(tempFile)) {
                    report.setUrl(huaweiObsUtils.upload(fis, MediaType.APPLICATION_PDF_VALUE));
                } finally {
                    FileUtils.deleteQuietly(tempFile);
                }
                report.setFileType(SampleReportFileTypeEnum.PDF.name());
                report.setGroupName(sample.getGroupName());
                report.setGroupId(sample.getGroupId());
                report.setOrgId(sample.getOrgId());
                report.setOrgName(sample.getOrgName());
                report.setUpdateDate(new Date());
                report.setCreateDate(new Date());
                sampleReports.add(report);
                break;
            }
        }

        if (CollectionUtils.isEmpty(samples)) {
            return CONTINUE_PROCESSING;
        }

        for (OutsourcingSampleDto sample : samples) {
            // 对应样本报告 项目
            final List<SampleReportItemDto> reportItems = context.getSampleReportItems().get(sample.getOutsourcingSampleId());

            // 对应样本结果
            final List<SampleResultDto> sampleResults = context.getSampleResults().get(sample.getOutsourcingSampleId());

            final ApplyDto apply = applies.get(sample.getApplyId());
            final ApplySampleDto applySample = context.getApplySamples().get(sample.getApplySampleId());

            sample.setCheckerId(LoginUserHandler.get().getUserId());
            sample.setCheckerName(LoginUserHandler.get().getNickname());
            sample.setCheckDate(new Date());
            OutsourcingUser testerAuditor = context.getTesterAuditor(sample.getBarcode());
            if (Objects.nonNull(testerAuditor)) {
                sample.setCheckerId(testerAuditor.getCheckerId());
                sample.setCheckerName(testerAuditor.getCheckerName());
                sample.setCheckDate(new Date());

                applySample.setTesterId(testerAuditor.getTesterId());
                applySample.setTesterName(testerAuditor.getTesterName());
            }

            sampleReports.add(buildPDF(sample, apply, applySample, reportItems,
                    sampleResults, sampleItems.getOrDefault(applySample.getApplySampleId(),
                            Collections.emptyList())));
        }

        sampleReportService.addSampleReportBatch(sampleReports);

        return CONTINUE_PROCESSING;
    }

    public SampleReportDto buildPDF(OutsourcingSampleDto sample, ApplyDto apply, ApplySampleDto applySample,
                                    List<SampleReportItemDto> reportItems, List<SampleResultDto> results,
                                    List<ApplySampleItemDto> sampleItems) {

        final ProfessionalGroupDto group = groupService.selectByGroupId(sample.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException(String.format("专业组 [%s] 不存在", sample.getGroupName()));
        }

        final UserDto checker = Objects.nonNull(auditConfig.getChecker()) ? auditConfig.getChecker()
                : userService.selectByUserId(sample.getCheckerId());
        if (Objects.isNull(checker)) {
            throw new IllegalStateException(String.format("审核人 [%s] 不存在", sample.getCheckerName()));
        }

        final UserDto tester = Objects.nonNull(auditConfig.getTester()) ? auditConfig.getTester() :
                userService.selectByUserId(applySample.getTesterId());
        if (Objects.isNull(tester)) {
            throw new IllegalStateException(String.format("检验者 [%s] 不存在", applySample.getTesterName()));
        }

        final PdfReportParamDto param = new PdfReportParamDto();

        param.put("sample",
                Dict.of("sampleNo", sample.getSampleNo(), "instrumentGroupName", sample.getInstrumentGroupName(),
                        "instrumentName", sample.getInstrumentName(), "testDate", sample.getTestDate(), "testerName",
                        applySample.getTesterName(), "checkerName", sample.getCheckerName(), "sampleRemark",
                        applySample.getSampleRemark(), "resultRemark", applySample.getResultRemark(), "_sample",
                        Dict.parse(sample)));

        param.put("apply",
                Dict.of("masterBarcode", apply.getMasterBarcode(), "patientName", apply.getPatientName(), "patientAge",
                        apply.getPatientAge(), "samplingDate", apply.getSamplingDate(), "patientMobile",
                        apply.getPatientMobile(), "createDate", apply.getCreateDate(), "sendDoctorName",
                        apply.getSendDoctorName(), "patientCard", apply.getPatientCard(), "dept", apply.getDept(), "hspOrgName",
                        apply.getHspOrgName(), "_apply", Dict.parse(apply)));

        applySample.setResultRemark(applySample.getResultRemark().replace("\n", "<br/>"));
        param.put("applySample",
                Dict.of("barcode", applySample.getBarcode(), "tubeName", applySample.getTubeName(), "sampleTypeName",
                        applySample.getSampleTypeName(), "groupName", applySample.getGroupName(), "onePickerName",
                        applySample.getOnePickerName(), "onePickDate", applySample.getOnePickDate(), "twoPickerName",
                        applySample.getTwoPickerName(), "twoPickDate", applySample.getTwoPickDate(), "_applySample",
                        Dict.parse(applySample)));
        param.put("testItems", Dict.of());


        final List<Dict> reportItemsParam = new ArrayList<>();
        for (SampleReportItemDto reportItem : reportItems) {
            final Dict dict = Dict.create();
            dict.set("_sampleReportItem", reportItem);
            dict.set("reportItemCode", reportItem.getReportItemCode());
            dict.set("reportItemName", reportItem.getReportItemName());
            dict.set("testItemCode", reportItem.getTestItemCode());
            dict.set("testItemName", reportItem.getTestItemName());

            final SampleResultDto sampleResult =
                    results.stream().filter(e -> Objects.equals(e.getReportItemCode(), reportItem.getReportItemCode()))
                            .findFirst().orElse(null);
            if (Objects.isNull(sampleResult)) {
                continue;
            }

            dict.set("_sampleResult", sampleResult);
            dict.set("result", sampleResult.getResult());
            dict.set("unit", sampleResult.getUnit());
            dict.set("range", sampleResult.getRange());
            dict.set("status", sampleResult.getStatus());
            dict.set("judge", sampleResult.getJudge());

            if (Objects.equals(sampleResult.getJudge(), TestJudgeEnum.UP.name())) {
                dict.set("upOrDown", "↑");
            } else if (Objects.equals(sampleResult.getJudge(), TestJudgeEnum.DOWN.name())) {
                dict.set("upOrDown", "↓");
            } else {
                dict.set("upOrDown", StringUtils.EMPTY);
            }

            dict.set("_instrumentReportItem", new InstrumentReportItemDto());

            reportItemsParam.add(dict);
        }

        param.put("reportItems", reportItemsParam);

        // 签名
        param.put("signature", Dict.of(
                // 检验者
                "tester", Dict.of("name", tester.getNickname(), "cnSign", tester.getCnSign(), "enSign", tester.getEnSign(), "sign",
                        StringUtils.defaultString(tester.getCnSign(), tester.getEnSign())),
                // 一次审核人
                "oneChecker",
                Dict.of("name", checker.getNickname(), "cnSign", checker.getCnSign(), "enSign", checker.getEnSign(), "sign",
                        StringUtils.defaultString(checker.getCnSign(), checker.getEnSign())),
                // 二次审核人
                "twoChecker",
                Dict.of("name", checker.getNickname(), "cnSign", checker.getCnSign(), "enSign", checker.getEnSign(), "sign",
                        StringUtils.defaultString(checker.getCnSign(), checker.getEnSign())),
                // 批准者
                "approver", Dict.of("name", group.getApproverName(), "sign", group.getApproverSign())));

        ReportTemplateBindDto reportTemplateBind = getReportTemplate(sample, apply, sampleItems);

        log.info("开始使用报告单模板 [{}] 生成 条码 [{}] 的报告单。 参数 [{}]", reportTemplateBind.getReportTemplateCode(), sample.getBarcode(),
                JSON.toJSONString(param));

        String pdfUrl = pdfReportService.build2Url(reportTemplateBind.getReportTemplateCode(), param);

        SampleReportDto sr = new SampleReportDto();
        sr.setApplySampleId(applySample.getApplySampleId());
        sr.setApplyId(apply.getApplyId());
        sr.setSampleId(sample.getOutsourcingSampleId());
        sr.setBarcode(sample.getBarcode());
        sr.setFileType(SampleReportFileTypeEnum.PDF.name());
        sr.setUrl(pdfUrl);
        sr.setGroupName(applySample.getGroupName());
        sr.setGroupId(applySample.getGroupId());
        sr.setHspOrgId(apply.getHspOrgId());
        sr.setHspOrgName(apply.getHspOrgName());
        return sr;

    }

    private ReportTemplateBindDto getReportTemplate(OutsourcingSampleDto sample, ApplyDto apply, List<ApplySampleItemDto> sampleItems) {
        ReportTemplateBindDto reportTemplateBind = null;
        // 如果只有一个检验项目，那么优先查询检验项目模板
        if (CollectionUtils.isNotEmpty(sampleItems)) {
            final List<ReportTemplateBindDto> reportTemplateBinds = reportTemplateBindService.selectByBizIds(sampleItems.stream().map(ApplySampleItemDto::getTestItemId)
                            .collect(Collectors.toList()))
                    .stream().filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                    .collect(Collectors.toList());
            if (reportTemplateBinds.size() == sampleItems.size()) {
                // 如果下面的检验项目完全一致时
                if (reportTemplateBinds.stream().map(ReportTemplateBindDto::getReportTemplateCode)
                        .distinct().count() == NumberUtils.LONG_ONE) {
                    reportTemplateBind = reportTemplateBinds.iterator().next();
                    log.info("条码 [{}] 下面的报告项目使用统一的报告单模板，固使用项目模版 [{}]", sample.getBarcode(),
                            reportTemplateBind.getReportTemplateCode());
                }
            } else {
                log.warn("条码 [{}] 下面的报告项目没有使用统一的报告单模板，固不使用项目模版", sample.getBarcode());
            }
        }

        if (Objects.isNull(reportTemplateBind)) {
            reportTemplateBind = reportTemplateBindService.selectMultiByBizId(apply.getHspOrgId())
                    .stream().filter(k -> Objects.equals(k.getInstrumentGroupId(), sample.getInstrumentGroupId()))
                    .filter(f -> Objects.equals(f.getEnable(), YesOrNoEnum.YES.getCode())).findFirst().orElse(null);
            if (Objects.isNull(reportTemplateBind)) {
                log.warn("送检机构 [{}] 没有绑定报告单模板，开始从专业小组 [{}] 查询模板", apply.getHspOrgName(), sample.getInstrumentGroupName());

                reportTemplateBind = reportTemplateBindService.selectByBizId(sample.getInstrumentGroupId());
                if (Objects.isNull(reportTemplateBind)
                        || Objects.equals(reportTemplateBind.getEnable(), YesOrNoEnum.NO.getCode())) {
                    throw new IllegalStateException(String.format("专业小组 [%s] 没有绑定模板", sample.getInstrumentGroupName()));
                }
            }
        }

        return reportTemplateBind;

    }

    @Getter
    @Setter
    @Configuration
    @ConfigurationProperties(prefix = "audit")
    public static class AuditConfig {

        /**
         * 测试者
         */
        private UserDto tester;

        /**
         * 审核者
         */
        private UserDto checker;

        /**
         * 使用外部PDF的项目，如果这个集合完全包含某个样本下的检验项目，那么会使用外部的PDF
         */
        private Set<String> outsidePdfs;
    }


}
