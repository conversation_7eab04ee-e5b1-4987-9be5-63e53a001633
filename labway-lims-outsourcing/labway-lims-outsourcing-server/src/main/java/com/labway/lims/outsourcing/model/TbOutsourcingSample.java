package com.labway.lims.outsourcing.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 外送样本
 */
@Getter
@Setter
@TableName("tb_outsourcing_sample")
public class TbOutsourcingSample {

    /**
     * 外送样本ID
     */
    @TableId
    private Long outsourcingSampleId;


    /**
     * 样本申请单ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 条码号
     */
    private String barcode;


    /**
     * 外送机构的条码号，譬如外送到迪安，那这个就是迪安的条码。注意，这个不是送检机构的条码
     */
    private String exportBarcode;

    /**
     * 申请单编码（用于 外送到业务中台）
     */
    private String formCode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 仪器信息名称
     */
    private String instrumentName;

    /**
     * 仪器信息
     */
    private Long instrumentId;

    /**
     * 检验日期，暂定二次分拣日期
     */
    private Date testDate;
    /**
     * 一次审核人
     */
    private String oneCheckerName;

    /**
     * 一次审核
     */
    private Long oneCheckerId;

    /**
     * 一审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date oneCheckDate;

    /**
     * 二次审核人
     */
    private String checkerName;

    /**
     * 二次审核
     */
    private Long checkerId;

    /**
     * 二审时间
     */
    private Date checkDate;


    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 检验机构ID
     */
    private Long orgId;

    /**
     * 检验机构
     */
    private String orgName;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

    /**
     * 外送机构名称
     */
    private String exportOrgName;

    /**
     * 0未删除，1删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 是否已打印清单，1:是，0:不是
     * @see YesOrNoEnum
     */
    private Integer isPrintList;

    /**
     * 打印清单日期
     */
    private Date printListDate;

    /**
     * 外送确认时间戳
     */
    private Long outsourcingConfirmTimestamp;
}
