package com.labway.lims.outsourcing.service.chain.sync;

import com.google.common.collect.Lists;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleImageService;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <pre>
 * SyncOutsourcingGetResultsPostCommand
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/30 16:04
 */
@Component
public class SyncOutsourcingGetResultsPostCommand implements Command {

    @DubboReference
    private SampleImageService sampleImageService;
    @DubboReference
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {

        SyncOutsourcingContext context = SyncOutsourcingContext.from(c);

        // 删除图片
        Set<Long> deleteSampleImageSampleIds = context.getDeleteSampleImageSampleIds();
        if (CollectionUtils.isNotEmpty(deleteSampleImageSampleIds)) {
            sampleImageService.deleteSampleImageBySampleIds(deleteSampleImageSampleIds);
        }

        // 添加图片
        List<SampleImageDto> addSampleImages = context.getAddSampleImages();
        if (CollectionUtils.isNotEmpty(addSampleImages)) {
            sampleImageService.addSampleImage(addSampleImages);
        }

        // 更新申请单样本
        List<ApplySampleDto> updateApplySampleDtos = context.getUpdateApplySampleDtos();
        if (CollectionUtils.isNotEmpty(updateApplySampleDtos)) {
            updateApplySampleDtos.forEach(applySampleDto -> {
                applySampleService.updateByApplySampleIds(applySampleDto, Lists.newArrayList(applySampleDto.getApplySampleId()));
            });
        }

        return CONTINUE_PROCESSING;
    }


}
