package com.labway.lims.outsourcing.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.HspOrgDateQueryDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.outsourcing.api.dto.SignHisSampleItemDto;
import com.labway.lims.outsourcing.api.dto.his.HisCancelSignParam;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.api.dto.his.HisSample;
import com.labway.lims.outsourcing.api.dto.his.HisSignParam;
import com.labway.lims.outsourcing.api.service.HisService;
import com.labway.lims.outsourcing.vo.ApplyInfoVo;
import com.labway.lims.outsourcing.vo.CancelSignVo;
import com.labway.lims.outsourcing.vo.HisBarcodeVo;
import com.labway.lims.outsourcing.vo.HisSampleItemVo;
import com.labway.lims.outsourcing.vo.HisSampleVo;
import com.labway.lims.outsourcing.vo.HspOrgDateQueryVo;
import com.labway.lims.outsourcing.vo.SignHisBarcodeVo;
import com.labway.lims.outsourcing.vo.SignHisSampleItemVo;
import com.labway.lims.outsourcing.vo.SignSampleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 样本签收业务
 */
@Slf4j
@RestController
@RequestMapping("/his")
public class HisController extends BaseController {


    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @DubboReference
    private ApplyService applyService;

    @DubboReference
    private ApplySampleService applySampleService;

    @DubboReference
    private ApplySampleItemService applySampleItemService;

    @DubboReference
    private UserService userService;
    @Resource
    private HisService hisService;
    @DubboReference
    private SystemParamService systemParamService;

    /**
     * 取消签收
     */
    @PostMapping("/cancel-sign")
    public Object cancelSign(@RequestBody CancelSignVo vo) {
        final Set<Long> applyIds = vo.getApplyIds();
        if (CollectionUtils.isEmpty(applyIds)) {
            throw new IllegalArgumentException("请选择样本");
        }

        if (StringUtils.isAnyBlank(vo.getReasonName(), vo.getReasonCode())) {
            throw new IllegalArgumentException("请选择取消签收原因");
        }

        if (Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalArgumentException("请选择送检机构");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("送检机构不存在");
        }
        if (Objects.equals(hspOrganization.getEnable(), YesOrNoEnum.NO.getCode())) {
            throw new IllegalStateException(String.format("送检机构 [%s] 已禁用", hspOrganization.getHspOrgName()));
        }
        final HisCancelSignParam hisCancelSignParam = JSON.parseObject(JSON.toJSONString(vo), HisCancelSignParam.class);
        hisCancelSignParam.setHspOrgCode(hspOrganization.getHspOrgCode());
        hisService.cancelSign(hisCancelSignParam);

        return Collections.emptyMap();
    }

    /**
     * 样本签收
     */
    @PostMapping("/sign")
    public Object sign(@RequestBody SignHisBarcodeVo vo) {
        log.info("签收入参===>"+JSON.toJSONString(vo));
        final Long hspOrgId = vo.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalStateException("请选择送检机构");
        }

        final String outBarcode = vo.getOutBarcode();
        if (Objects.isNull(outBarcode)) {
            throw new IllegalStateException("请输入外部条码");
        }
        List<SignHisSampleItemVo> items = vo.getItems();

        if (CollectionUtils.isEmpty(items)) {
            throw new IllegalStateException(String.format("条码 [%s] 没有需要签收的项目", vo.getOutBarcode()));
        }

        // 判断所选的申请项目中自定义样本类型的长度是否大于50字符
        if (CollectionUtils.isNotEmpty(items)){
            boolean isBeyond = items.stream()
                    .filter(i -> StringUtils.isNotBlank(i.getSampleTypeCode()))
                    .map(HisSampleItemVo::getSampleTypeCode)
                    .collect(Collectors.toSet())
                    .stream().anyMatch(i -> StringUtils.length(i) > INPUT_MAX_LENGTH);
            if (isBeyond){
                throw new IllegalArgumentException(String.format("样本类型长度不能超过 %s 字符",INPUT_MAX_LENGTH));
            }
        }

        // 判断所选的申请项目中备注字段的长度是否大于50字符
        if (CollectionUtils.isNotEmpty(items)) {
            boolean isBeyond = items.stream()
                    .filter(i -> StringUtils.isNotBlank(i.getRemark())).anyMatch(element -> element.getRemark().length() > INPUT_MAX_LENGTH);
            if (isBeyond) {
                throw new IllegalArgumentException(String.format("项目备注字段的长度不能超过 %s 字符", INPUT_MAX_LENGTH));
            }
        }

        final HisSignParam sign = new HisSignParam();
        sign.setHspOrgId(hspOrgId);
        sign.setOutBarcode(outBarcode);
        sign.setIgnoreDuplicateSignItem(vo.getIgnoreDuplicateSignItem());
        sign.setHisSampleItemIds(vo.getHisSampleItemIds());
        sign.setItems(JSON.parseArray(JSON.toJSONString(items), SignHisSampleItemDto.class));
        sign.setNoType(vo.getNoType());
        //这里需要判断送检机构是否支持多项目签收
        sign.setCanSplitSample(Boolean.FALSE);
        sign.setSampleSource(vo.getSampleSource());
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.HIS_SIGN_SPLIT_TUBE.getCode(), LoginUserHandler.get().getOrgId());
        if (Objects.nonNull(param) && BooleanUtils.toBoolean(StringUtils.lowerCase(param.getParamValue()))) {
            sign.setCanSplitSample(Boolean.TRUE);
        }

        final ApplyInfoVo applyInfoVo = JSON.parseObject(JSON.toJSONString(hisService.sign(sign)), ApplyInfoVo.class);
        for (ApplyInfoVo.Sample sample : applyInfoVo.getSamples()) {
            sample.setHasPathologyTestItem(sample.getTestItems().stream()
                    .anyMatch(o -> Objects.equals(o.getItemType(), ItemTypeEnum.PATHOLOGY.name())));
        }

        return Map.of("apply", applyInfoVo);
    }

    /**
     * 获取条码信息
     */
    @PostMapping("/get")
    public Object get(@RequestBody HisBarcodeVo vo) {
        final Long hspOrgId = vo.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalStateException("请选择送检机构");
        }

        final String outBarcode = vo.getOutBarcode();
        if (Objects.isNull(outBarcode)) {
            throw new IllegalStateException("请输入外部条码");
        }

        final HisGetParam get =
                JSON.parseObject(JSON.toJSONString(vo), HisGetParam.class);

        final HisSample hisSample = hisService.get(get);

        final HisSampleVo hisSampleVo = JSON.parseObject(JSON.toJSONString(hisSample), HisSampleVo.class);

        return Map.of("barcode", hisSampleVo, "noType", StringUtils.isBlank(vo.getNoType()) ? "" : vo.getNoType());
    }

    /**
     *  当前用户专业组的人和没有专业组的人
     */
    @PostMapping("/getGroupPersons")
    public List<String> getGroupPersons(){
        LoginUserHandler.User user = LoginUserHandler.get();
        if (Objects.isNull(user)){
            throw new IllegalStateException("请先登录后查签收！");
        }
        return userService.selectByGroupId(user.getGroupId(),user.getOrgId());
    }


    /**
     * 已签收列表
     */
    @PostMapping("/signList")
    public Object signList(@RequestBody HspOrgDateQueryVo queryVo) {
        final Long hspOrgId = queryVo.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            return Collections.emptyList();
        }

        final Date now = new Date();
        queryVo.setStartDate(ObjectUtils.defaultIfNull(queryVo.getStartDate(), DateUtil.beginOfDay(now)));
        queryVo.setEndDate(ObjectUtils.defaultIfNull(queryVo.getEndDate(), DateUtil.endOfDay(now)));

        // 查询已签收的申请单
        final HspOrgDateQueryDto hspOrgDateQueryDto = JSON.parseObject(JSON.toJSONString(queryVo), HspOrgDateQueryDto.class);
        final List<ApplyDto> applys = applyService.selectHspOrgSignList(hspOrgDateQueryDto);
        if (CollectionUtils.isEmpty(applys)) {
            return Collections.emptyList();
        }
        final Map<Long, ApplyDto> applyMap = applys.stream().collect(Collectors.toMap(ApplyDto::getApplyId, v -> v, (v1, v2) -> v1));

        // 样本
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplyIds(applyMap.keySet());
        if (CollectionUtils.isEmpty(applySamples)) {
            return Collections.emptyList();
        }

        // 项目
        final Map<Long, List<ApplySampleItemDto>> applySampleItemMap = applySampleItemService.selectByApplyIds(applyMap.keySet())
                .stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        return applySamples.stream()
                .map(m -> {
                    SignSampleVo vo = new SignSampleVo();

                    Optional.ofNullable(applyMap.get(m.getApplyId())).ifPresent(c -> {
                        vo.setMasterBarcode(c.getMasterBarcode());
                        vo.setSignDate(c.getCreateDate());
                        vo.setUrgent(m.getUrgent());
                        vo.setSignUser(c.getCreatorName());
                        vo.setApplyType(c.getApplyTypeName());
                        vo.setPatientAge(c.getPatientAge());
                        vo.setPatientSubage(c.getPatientSubage());
                        vo.setPatientSubageUnit(c.getPatientSubageUnit());
                        vo.setPatientVisitCard(c.getPatientVisitCard());
                        vo.setPatientName(c.getPatientName());
                        vo.setPatientSex(Objects.equals(c.getPatientSex(), SexEnum.MAN.getCode()) ? "男" : "女");
                        vo.setSampleCount(c.getSampleCount());
                        vo.setHspOrgName(c.getHspOrgName());
                        vo.setApplyId(c.getApplyId());
                        vo.setDept(c.getDept());
                        vo.setPatientBed(c.getPatientBed());
                        vo.setRemark(c.getRemark());
                        // 增加病区、医保卡号 字段
                        vo.setInpatientArea(c.getInpatientArea());
                        vo.setVisitCardNo(c.getVisitCardNo());
                    });

                    Optional.ofNullable(applySampleItemMap.get(m.getApplySampleId())).ifPresent(f -> {
                        final String testItemName = f.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.joining("、"));
                        final String remark = f.stream().map(ApplySampleItemDto::getRemark).filter(StringUtils::isNotBlank).collect(Collectors.joining("、"));
                        vo.setTestItemName(testItemName);
                        vo.setItemRemark(remark);
                    });

                    vo.setBarcode(m.getBarcode());
                    vo.setOutBarcode(m.getOutBarcode());
                    vo.setSampleTypeName(m.getSampleTypeName());
                    vo.setApplySampleId(m.getApplySampleId());
                    return vo;
                })
                // 1.1.3.8 【样本签收】已签收列表，默认根据签收时间倒序 https://www.tapd.cn/59091617/prong/stories/view/1159091617001001975
                .sorted(Comparator.comparing(SignSampleVo::getSignDate, Comparator.reverseOrder()))
                .collect(Collectors.toList());
    }

}
