package com.labway.lims.outsourcing.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.outsourcing.api.dto.QueryOutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.QuerySamplePdfDto;
import com.labway.lims.outsourcing.api.dto.QuerySendOrganizationDto;
import com.labway.lims.outsourcing.api.dto.QueryTestItemDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleResultPrintService;
import com.labway.lims.outsourcing.api.vo.QueryOutsourcingSampleVo;
import com.labway.lims.outsourcing.api.vo.QuerySendOrganizationVo;
import com.labway.lims.outsourcing.api.vo.QueryTestItemVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/20 16:20
 */
@RestController
@RequestMapping("/outsourcing-print")
public class OutsourcingSampleResultPrintController extends BaseController {

    @Resource
    private OutsourcingSampleResultPrintService outsourcingSampleResultPrintService;


    /**
     * 查询外送机构下拉列表查询
     */
    @PostMapping("/querySendOrganization")
    public List<QuerySendOrganizationVo> querySendOrganization(@RequestBody @Valid QuerySendOrganizationDto querySendOrganizationDto){
        return outsourcingSampleResultPrintService.querySendOrganization(querySendOrganizationDto);
    }

    /**
     * 查询检验项目下拉列表数据
     */
    @PostMapping("/queryTestItem")
    public List<QueryTestItemVo> queryTestItem(@RequestBody @Valid QueryTestItemDto queryTestItemDto){
        return outsourcingSampleResultPrintService.queryTestItem(queryTestItemDto);
    }


    /**
     * 外送样本报告单预览pdf
     */
    @PostMapping("/querySamplePdf")
    public List<String> querySamplePdf(@RequestBody @Valid QuerySamplePdfDto querySamplePdfDto) {
        return outsourcingSampleResultPrintService.querySamplePdf(querySamplePdfDto);
    }



    /**
     * 查询外送样本列表信息
     */
    @PostMapping("/queryOutsourcingSample")
    public List<QueryOutsourcingSampleVo> queryOutsourcingSample(@RequestBody @Valid QueryOutsourcingSampleDto queryOutsourcingSampleDto){
        return outsourcingSampleResultPrintService.queryOutsourcingSample(queryOutsourcingSampleDto);
    }






}
