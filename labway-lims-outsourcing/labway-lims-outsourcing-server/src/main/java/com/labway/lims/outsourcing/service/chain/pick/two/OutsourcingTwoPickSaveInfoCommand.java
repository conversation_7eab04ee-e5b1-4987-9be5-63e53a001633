package com.labway.lims.outsourcing.service.chain.pick.two;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 保存数据
 */
@Slf4j
@Component
class OutsourcingTwoPickSaveInfoCommand implements Filter, Command {
    @Resource
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean postprocess(Context c, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {

        final OutsourcingTwoPickContext context = OutsourcingTwoPickContext.from(c);
        final ApplySampleDto applySample = context.getApplySample();
        final InstrumentGroupDto instrumentGroup = context.getInstrumentGroup();
        final InstrumentDto instrument = context.getInstrument();
        final ApplyDto apply = context.getApply();
        final List<ApplySampleItemDto> applySampleItems = context.getApplySampleItems();

        //添加样本
        final OutsourcingSampleDto sample = new OutsourcingSampleDto();
        sample.setOutsourcingSampleId(snowflakeService.genId());
        sample.setApplySampleId(context.getApplySampleId());
        sample.setApplyId(applySample.getApplyId());
        sample.setBarcode(applySample.getBarcode());
        sample.setSampleNo(context.getSampleNo());
        sample.setGroupId(applySample.getGroupId());
        sample.setGroupName(applySample.getGroupName());
        sample.setInstrumentId(instrument.getInstrumentId());
        sample.setInstrumentName(instrument.getInstrumentName());
        sample.setInstrumentGroupId(instrumentGroup.getInstrumentGroupId());
        sample.setInstrumentGroupName(instrumentGroup.getInstrumentGroupName());
        sample.setTestDate(new Date());
        sample.setCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        sample.setCheckerId(NumberUtils.LONG_ZERO);
        sample.setCheckerName(StringUtils.EMPTY);
        sample.setHspOrgId(apply.getHspOrgId());
        sample.setHspOrgName(apply.getHspOrgName());
        sample.setOrgId(applySample.getOrgId());
        sample.setOrgName(applySample.getOrgName());
        sample.setExportOrgId(applySampleItems.iterator().next().getExportOrgId());
        sample.setExportOrgName(applySampleItems.iterator().next().getExportOrgName());
        sample.setExportBarcode(StringUtils.EMPTY);
        sample.setFormCode(String.valueOf(IdWorker.getId()));
        sample.setPrintListDate(DefaultDateEnum.DEFAULT_DATE.getDate());

        final long sampleId = outsourcingSampleService.addOutsourcingSample(sample);

        final Map<Long, List<ReportItemDto>> reportItems = context.getReportItems()
                .stream().collect(Collectors.groupingBy(ReportItemDto::getTestItemId));

        //添加样本报告项目
        final LinkedList<SampleReportItemDto> items = new LinkedList<>();
        for (ApplySampleItemDto sampleItem : applySampleItems) {
            if (!reportItems.containsKey(sampleItem.getTestItemId())) {
                continue;
            }
            for (ReportItemDto item : reportItems.get(sampleItem.getTestItemId())) {

                InstrumentReportItemDto reportItem = context.getInstrumentReportItems().stream().filter(e ->
                        Objects.equals(e.getInstrumentId(), sample.getInstrumentId()) &&
                                Objects.equals(e.getReportItemCode(), item.getReportItemCode())).findFirst().orElse(null)
                ;

                //找不到则找专业小组下的报告项目
                if (Objects.isNull(reportItem)) {
                    reportItem = context.getInstrumentReportItems().stream().
                            filter(e -> Objects.equals(e.getReportItemCode(), item.getReportItemCode()))
                            .findFirst().orElse(null);
                }

                final SampleReportItemDto dto = new SampleReportItemDto();
                dto.setApplyId(sampleItem.getApplyId());
                dto.setApplySampleId(sampleItem.getApplySampleId());
                dto.setSampleId(sampleId);
                dto.setReportItemId(item.getReportItemId());
                dto.setReportItemCode(item.getReportItemCode());
                dto.setReportItemName(item.getReportItemName());
                dto.setTestItemId(item.getTestItemId());
                dto.setTestItemCode(item.getTestItemCode());
                dto.setTestItemName(item.getTestItemName());
                dto.setIsRetest(RetestStatusEnum.NORMAL.getCode());
                dto.setPrintSort(Objects.nonNull(reportItem) ? reportItem.getPrintSort() : NumberUtils.INTEGER_ZERO);
                items.add(dto);
            }
        }

        sampleReportItemService.addSampleReportItems(items);

        context.put(OutsourcingTwoPickContext.OUTSOURCING_SAMPLE, sample);

        return CONTINUE_PROCESSING;
    }
}
