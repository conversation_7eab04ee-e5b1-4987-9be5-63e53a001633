package com.labway.lims.outsourcing.service.chain.audit;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleAuditDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.service.chain.StopWatchContext;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/3 15:45
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class AuditSampleContext extends StopWatchContext {

    /**
     * 审核 外送样本id
     */
    private Collection<Long> outsourcingSampleIds;
    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    /**
     * 审核类型
     *
     * @see com.labway.lims.api.enums.routine.SampleAuditStatusEnum
     */
    private SampleAuditStatusEnum auditType;

    /**
     * 是都需要一次审核
     */
    private boolean oneCheck;

    // 外送 检验
    public static final String OUTSOURCING_SAMPLE_LIST = "OUTSOURCING_SAMPLE_LIST_" + IdUtil.objectId();
    public static final String SAMPLE_AUDTI_DTO = "SAMPLE_AUDTI_DTO" + IdUtil.objectId();

    // 空参考范围结果
    public static final String EMPTY_REFERENCE_RESULT_WARNING = "EMPTY_REFERENCE_RESULT_WARNING" + IdUtil.objectId();
    public static final String EMPTY_REFERENCE_RESULT_FORBIDDEN = "EMPTY_REFERENCE_RESULT_FORBIDDEN" + IdUtil.objectId();

    public OutsourcingSampleAuditDto getAuditDto() {
        return (OutsourcingSampleAuditDto) get(SAMPLE_AUDTI_DTO);
    }

    public List<OutsourcingSampleDto> getSamples() {
        return (List<OutsourcingSampleDto>)get(OUTSOURCING_SAMPLE_LIST);
    }

    // 对应申请单
    public static final String APPLY = "APPLY_" + IdUtil.objectId();

    public List<ApplyDto> getApplyDtoList() {
        return (List<ApplyDto>)get(APPLY);
    }

    // 对应申请单样本
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    public List<ApplySampleDto> getApplySampleList() {
        return (List<ApplySampleDto>)get(APPLY_SAMPLE);
    }

    // 对应样本报告项目
    public static final String SAMPLE_REPORT_ITEM = "SAMPLE_REPORT_ITEM_" + IdUtil.objectId();

    public List<SampleReportItemDto> getSampleReportItemDtos() {
        return (List<SampleReportItemDto>)get(SAMPLE_REPORT_ITEM);
    }

    // 对应样本结果
    public static final String SAMPLE_RESULT = "SAMPLE_RESULT_" + IdUtil.objectId();

    public List<SampleResultDto> getSampleResultDtos() {
        return (List<SampleResultDto>)get(SAMPLE_RESULT);
    }

    // 对应仪器报告项目
    public static final String INSTRUMENT_REPORT_ITEM = "INSTRUMENT_REPORT_ITEM_" + IdUtil.objectId();

    public Map<Long, List<InstrumentReportItemDto>> getInstrumentReportItemDtos() {
        return (Map<Long, List<InstrumentReportItemDto>>)get(INSTRUMENT_REPORT_ITEM);
    }

    public Map<Long, List<SampleResultDto>> getEmptyReferenceResultWarning() {
        return (Map<Long, List<SampleResultDto>>) computeIfAbsent(EMPTY_REFERENCE_RESULT_WARNING, key -> new HashMap<Long, List<SampleResultDto>>());
    }

    public Map<Long, List<SampleResultDto>> getEmptyReferenceResultForbidden() {
        return (Map<Long, List<SampleResultDto>>) computeIfAbsent(EMPTY_REFERENCE_RESULT_FORBIDDEN, key -> new HashMap<Long, List<SampleResultDto>>());
    }

    public static AuditSampleContext from(Context c) {
        return (AuditSampleContext)c;
    }

    @Override
    protected String getWatchName() {
        return "条码审核";
    }
}
