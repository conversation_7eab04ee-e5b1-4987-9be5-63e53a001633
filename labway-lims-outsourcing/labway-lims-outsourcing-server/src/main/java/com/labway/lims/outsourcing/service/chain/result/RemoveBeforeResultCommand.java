package com.labway.lims.outsourcing.service.chain.result;

import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 删除之前的结果
 *
 * <AUTHOR>
 * @since 2023/3/30 16:21
 */
@Slf4j
@Component
public class RemoveBeforeResultCommand implements Command {

    @DubboReference
    private SampleResultService sampleResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        // 先把之前的结果保存下来
        // 先把之前的结果保存下来
        final OutsourcingSampleDto sample = context.getSample();
        final Long reportItemId = context.getReportItemId();

        final SampleResultDto sampleResult = sampleResultService.selectBySampleIdAndReportItemId(sample.getOutsourcingSampleId(), reportItemId);

        if (Objects.isNull(sampleResult)) {
            return CONTINUE_PROCESSING;
        }

        context.put(SaveResultContext.BEFORE_RESULT, sampleResult.getResult());

        // 删除之前的结果 mysql
        sampleResultService.deleteBySampleResultId(sampleResult.getSampleResultId(),sample.getOutsourcingSampleId());


        return CONTINUE_PROCESSING;
    }
}
