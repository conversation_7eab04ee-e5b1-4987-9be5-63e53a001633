package com.labway.lims.outsourcing.service.chain.audit;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.ExceptionCodeEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleAuditDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <pre>
 * EmptyReferenceTipCommand
 * 空参考范围提示
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/13 15:22
 */
@Component
@Slf4j
public class EmptyReferenceTipCommand implements Command {

    @Override
    public boolean execute(Context c) throws Exception {
        final AuditSampleContext context = AuditSampleContext.from(c);

        final OutsourcingSampleAuditDto param = context.getAuditDto();
        final List<OutsourcingSampleDto> samples = context.getSamples();
        final Map<Long, List<SampleResultDto>> forbidden = context.getEmptyReferenceResultForbidden();
        final Map<Long, List<SampleResultDto>> warning = context.getEmptyReferenceResultWarning();

        final List<String> forbiddenMsgs = new ArrayList<>();
        final List<String> warningMsgs = new ArrayList<>();
        samples.forEach(e -> {
            List<SampleResultDto> forbiddenResults = forbidden.getOrDefault(e.getOutsourcingSampleId(), Collections.emptyList());
            if (CollectionUtils.isNotEmpty(forbiddenResults)) {
                final String reportNames = forbiddenResults.stream().map(SampleResultDto::getReportItemName)
                        .collect(Collectors.joining("、", "【", "】"));
                forbiddenMsgs.add(String.format("%s条码号中%s空参考范围，不允许审核", e.getBarcode(), reportNames));
            }
            List<SampleResultDto> warningResults = warning.getOrDefault(e.getOutsourcingSampleId(), Collections.emptyList());
            if (CollectionUtils.isNotEmpty(warningResults)) {
                final String reportNames = warningResults.stream().map(SampleResultDto::getReportItemName)
                        .collect(Collectors.joining("、", "【", "】"));
                warningMsgs.add(String.format("%s条码号中%s空参考范围", e.getBarcode(), reportNames));
            }
        });

        // 禁止审核
        if (CollectionUtils.isNotEmpty(forbiddenMsgs)) {
            throw new IllegalStateException(String.join(StringPool.NEW_LINE, forbiddenMsgs));
        }

        // 忽略空参考范围审核提示
        if (BooleanUtils.isTrue(param.getAuditForce()) || BooleanUtils.isTrue(param.getIgnoreEmptyReferenceTip())) {
            return CONTINUE_PROCESSING;
        }
        // 委外开启一审时，二审操作忽略空参考范围审核提示
        if (context.isOneCheck() && Objects.equals(context.getAuditType(), SampleAuditStatusEnum.TWO_CHECK)) {
            return CONTINUE_PROCESSING;
        }

        // 审核时提示
        if (CollectionUtils.isNotEmpty(warningMsgs)) {
            throw new LimsCodeException(ExceptionCodeEnum.EMPTY_REFERENCE_WARNING.getCode(), JSON.toJSONString(warningMsgs));
        }

        return CONTINUE_PROCESSING;
    }

}
