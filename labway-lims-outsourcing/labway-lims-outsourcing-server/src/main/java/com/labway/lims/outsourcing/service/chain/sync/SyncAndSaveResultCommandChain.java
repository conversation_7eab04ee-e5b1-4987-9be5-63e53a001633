package com.labway.lims.outsourcing.service.chain.sync;

import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.impl.ChainBase;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <pre>
 * SyncAndSaveResultChain
 * 同步&保存结果
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/3/10 17:55
 */
@Component
public class SyncAndSaveResultCommandChain implements Command {

    @Resource
    private SyncOutsourcingGetAndSaveResultsCommand syncOutsourcingGetAndSaveResultsCommand;

    @Resource
    private OldSyncResultChain syncResultChain;

    @Override
    public boolean execute(Context c) throws Exception {
        final SyncOutsourcingContext context = SyncOutsourcingContext.from(c);

        if (BooleanUtils.isTrue(context.testOrgIsLimsSystem())) {
            return syncOutsourcingGetAndSaveResultsCommand.execute(context);
        } else {
            // 老版本结果同步
            return syncResultChain.execute(context);
        }
    }

    @Component
    public static class OldSyncResultChain extends ChainBase implements InitializingBean {
        @Resource
        private SyncOutsourcingGetResultsChain syncOutsourcingGetResultsChain;
        @Resource
        private SyncOutsourcingGetResultsPostCommand syncOutsourcingGetResultsPostCommand;
        @Resource
        private SyncOutsourcingBuildReportCommand syncOutsourcingBuildReportCommand;
        @Resource
        private SyncOutsourcingUpdateSampleAuditStatusCommand syncOutsourcingUpdateSampleAuditStatusCommand;
        @Resource
        private SyncOutsourcingSampleAuditFlowCommand syncOutsourcingSampleAuditFlowCommand;
        @Resource
        private SyncOutsourcingSampleAuditRabbitMqCommand syncOutsourcingSampleAuditRabbitMqCommand;
        @Resource
        private SyncOutsourcingSampleResultFlowCommand syncOutsourcingSampleResultFlowCommand;

        @Override
        public void afterPropertiesSet() throws Exception {

            // 获取结果
            addCommand(syncOutsourcingGetResultsChain);

            // 处理调用外部结果的数据
            addCommand(syncOutsourcingGetResultsPostCommand);

            // 构建PDF
            addCommand(syncOutsourcingBuildReportCommand);

            // 添加结果条码环节
            addCommand(syncOutsourcingSampleResultFlowCommand);

            // 条码环节
            addCommand(syncOutsourcingSampleAuditFlowCommand);

            // 发送 mq
            addCommand(syncOutsourcingSampleAuditRabbitMqCommand);

            // 修改状态
            addCommand(syncOutsourcingUpdateSampleAuditStatusCommand);

            // over
            addCommand((c) -> PROCESSING_COMPLETE);

        }
    }
}
