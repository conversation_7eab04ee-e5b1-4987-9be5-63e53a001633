package com.labway.lims.outsourcing.service.chain.retest;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/26 18:47
 */
@Slf4j
@Component
public class OutsourcingStartRetestSampleFlowCommand implements Filter, Command {
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingStartRetestContext context = OutsourcingStartRetestContext.from(c);
        final List<SampleReportItemDto> retestItems = context.getRetestSampleReportItems();
        final OutsourcingSampleDto sample = context.getSample();
        final LoginUserHandler.User user = LoginUserHandler.get();
        final LinkedList<Long> ids = snowflakeService.genIds(retestItems.size());

        final StringBuilder sb = new StringBuilder();
        for (SampleReportItemDto sri : retestItems) {
            sb.append(String.format("报告项目 [%s] 开始复查 \n", sri.getReportItemName()));
        }
        final SampleFlowDto sf = new SampleFlowDto();
        sf.setSampleFlowId(ids.pop());
        sf.setApplyId(sample.getApplyId());
        sf.setApplySampleId(sample.getApplySampleId());
        sf.setBarcode(sample.getBarcode());
        sf.setOperateCode(BarcodeFlowEnum.RETEST_RESULT.name());
        sf.setOperateName(BarcodeFlowEnum.RETEST_RESULT.getDesc());
        sf.setOperator(user.getNickname());
        sf.setOperatorId(user.getUserId());
        sf.setContent(sb.toString());
        sf.setCreateDate(new Date());
        sf.setCreatorId(user.getUserId());
        sf.setCreatorName(user.getNickname());
        sf.setUpdateDate(new Date());
        sf.setUpdaterId(user.getUserId());
        sf.setUpdaterName(user.getNickname());
        sf.setOrgId(user.getOrgId());
        sf.setOrgName(user.getOrgName());
        sf.setIsDelete(YesOrNoEnum.NO.getCode());

        sampleFlowService.addSampleFlow(sf);
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

}
