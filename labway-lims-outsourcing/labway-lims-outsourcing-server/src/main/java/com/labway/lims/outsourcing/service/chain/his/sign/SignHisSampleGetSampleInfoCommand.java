package com.labway.lims.outsourcing.service.chain.his.sign;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.consts.RedisConsts;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.outsourcing.api.dto.SignHisSampleItemDto;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.api.dto.his.HisSample;
import com.labway.lims.outsourcing.api.dto.his.HisSampleItem;
import com.labway.lims.outsourcing.api.dto.his.HisSignParam;
import com.labway.lims.outsourcing.api.service.HisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.labway.lims.outsourcing.service.chain.his.sign.SignHisSampleContext.HIS_SAMPLE;
import static com.labway.lims.outsourcing.service.chain.his.sign.SignHisSampleContext.HIS_SAMPLE_ITEMS;
import static com.labway.lims.outsourcing.service.chain.his.sign.SignHisSampleContext.TEST_ITEMS;

/**
 * <pre>
 * SignHisSampleGetSampleInfoCommand
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/15 18:03
 */
@Slf4j
@Component
@RefreshScope
public class SignHisSampleGetSampleInfoCommand implements Command {

    @Resource
    private HisService hisService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Value("${hisGetExpireMinutes:10}")
    private Long hisGetExpireMinutes;

    @DubboReference
    private TestItemService testItemService;

    @Override
    public boolean execute(Context context) throws Exception {
        SignHisSampleContext from = SignHisSampleContext.from(context);
        HisSignParam sign = from.getHisSignParam();
        final List<SignHisSampleItemDto> items = sign.getItems();

        // 查询申请单信息
        final HisSample hisSample = getHisSampleCache(JSON.parseObject(JSON.toJSONString(sign), HisGetParam.class));
        if (Objects.isNull(hisSample)) {
            throw new IllegalStateException("未找到相关条码信息");
        }
        final List<HisSampleItem> hisSampleItems = hisSample.getHisSampleItems();
        if (CollectionUtils.isEmpty(hisSampleItems)) {
            throw new IllegalStateException("未找到相关条码项目信息");
        }

        // 查询检验项目
        final Map<Long, TestItemDto> testItems = testItemService.selectByTestItemIdsAsMap(items.stream()
                .map(HisSampleItem::getTestItemId).collect(Collectors.toSet()));

        from.put(HIS_SAMPLE, hisSample);
        from.put(HIS_SAMPLE_ITEMS, hisSampleItems);
        from.put(TEST_ITEMS, testItems);

        return CONTINUE_PROCESSING;
    }

    private void addHisSampleCache(HisSample hisSample, HisGetParam hisGetParam) {
        String key = key(hisGetParam);
        stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(hisSample), hisGetExpireMinutes, TimeUnit.MINUTES);
    }

    public HisSample getHisSampleCache(HisGetParam hisGetParam) {
        // 优先查缓存
        String key = key(hisGetParam);
        String cached = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(cached)) {
            log.info("样本签收，从缓存中查询到数据 参数 [{}] 结果 [{}]", JSON.toJSONString(hisGetParam), cached);
            return JSON.parseObject(cached, HisSample.class);
        }
        // 缓存没有查到
        return hisService.get(hisGetParam);
    }

    public void deleteHisSampleCache(HisGetParam hisGetParam) {
        // 签收成功删除缓存
        String key = key(hisGetParam);
        stringRedisTemplate.delete(key);
    }

    private String key(HisGetParam hisGetParam) {
        return redisPrefix.getBasePrefix() + RedisConsts.HIS_GET + hisGetParam.getHspOrgId() + hisGetParam.getOutBarcode();
    }

}
