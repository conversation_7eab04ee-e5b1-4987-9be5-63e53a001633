package com.labway.lims.outsourcing.service.chain.his.get;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.business.center.compare.request.OutApplyInfoRequest;
import com.labway.business.center.compare.service.OutApplyInfoService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.swak.frame.dto.Response;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.List;
import java.util.Objects;

import static com.labway.lims.outsourcing.service.HisServiceImpl.SUCCESS_CODE;

@Getter
@Setter
@Slf4j
@Component
public class GetHisSampleGetSampleInfoCommand implements Command {

    @Resource
    private OutApplyInfoService outApplyInfoService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    /**
     * 物流取样时间
     */
    private static final String TAKE_SAMPLE_TIME = "TAKE_SAMPLE_TIME:";

    @Override
    public boolean execute(Context context) throws Exception {
        final GetHisSampleContext from = GetHisSampleContext.from(context);
        final HisGetParam hisGetParam = from.getHisGetParam();
        final HspOrganizationDto hspOrganization = from.getHspOrganization();
        final String orgCode = from.getOrgCode();

        final String outBarcode = hisGetParam.getOutBarcode();

        StopWatch stopWatch = new StopWatch("获取条码信息");

        stopWatch.start("请求业务中台接口-获取条码信息");
        // 请求业务中台接口
        final OutApplyInfoRequest req = new OutApplyInfoRequest();
        req.setBarcode(outBarcode);
        req.setHspOrgCode(hspOrganization.getHspOrgCode());
        req.setSignOrgCode(orgCode);
        req.setSignOrgName(LoginUserHandler.get().getOrgName());
        req.setNoType(hisGetParam.getNoType());

        final Response<OutApplyInfoDTO> body = outApplyInfoService.queryOutApplyInfo(req);

        stopWatch.stop();

        log.info("业务中台接口-获取条码信息 请求体 [{}] 响应体 [{}] 耗时: [{}] ", JSON.toJSONString(req), JSON.toJSONString(body),
            stopWatch.getTotalTimeMillis());

        if (Objects.isNull(body)) {
            throw new IllegalStateException("获取条码失败 业务中台返回消息为空");
        }

        // 判断是否成功
        if (BooleanUtils.isNotTrue(Objects.equals(body.getCode(), SUCCESS_CODE))) {
            throw new IllegalStateException(String.format("获取条码失败 业务中台返回消息: [%s]", body.getMsg()));
        }

        final OutApplyInfoDTO data = body.getData();
        if (Objects.isNull(data)) {
            throw new IllegalStateException("未获取到条码信息");
        }

        final List<OutApplyInfoDTO.OutApplyItem> outApplyItems = data.getItems();
        if (CollectionUtils.isEmpty(outApplyItems)) {
            throw new IllegalStateException("条码项目信息为空");
        }
        // 物流取样时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String takeSampleTime = Objects.nonNull(data.getTakeSampleTime())  ? sdf.format(data.getTakeSampleTime())  : "";

        String barcode = data.getBarcode();
        String hspOrgCode = data.getHspOrgCode();

        // key 前缀+取样时间字段名+送检机构+条码号
        final String redisKey = redisPrefix.getBasePrefix() + "TAKE_SAMPLE_TIME" + ":" + hspOrgCode + ":" + barcode;
        stringRedisTemplate.opsForValue().set(redisKey, takeSampleTime, Duration.ofDays(180));


        from.put(GetHisSampleContext.OUT_APPLY_INFO, data);

        return CONTINUE_PROCESSING;
    }
}
