package com.labway.lims.outsourcing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/8 14:10
 */
@Getter
@Setter
public class HistoryResultsChartQueryVo {

    /**
     * 样本ID
     */
    Long outsourcingSampleId;

    /**
     * 报告项目ID
     */
    Long reportItemId;

    /**
     * reportItemCode
     */
    private String reportItemCode;

    /**
     * 日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date dateStart;

    /**
     * 日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date dateEnd;
}
