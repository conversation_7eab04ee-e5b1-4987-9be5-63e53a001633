package com.labway.lims.outsourcing.vo;

import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class ApplyInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请单ID
     */
    private Long applyId;
    /**
     * 主条码号
     */
    private String masterBarcode;

    /**
     * 新增的样本
     */
    private List<Sample> samples;

    /**
     * 是否使用外部条码
     */
    private Boolean useOutBarcode;

    /**
     * 新增的样本信息，属性不易过多，需要更多属性直接调用查询接口
     */
    @Getter
    @Setter
    public static class Sample implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 申请单样本id
         */
        private Long applySampleId;

        /**
         * 样本条码
         */
        private String barcode;

        /**
         * 是否包含病理项目
         */
        private Boolean hasPathologyTestItem;


        /**
         * 检验项目
         */
        private List<TestItem> testItems;
    }


    @Getter
    @Setter
    public static class TestItem implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 检验项目编码
         */
        private String testItemCode;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 项目类型编码
         *
         * @see ItemTypeEnum
         */
        private String itemType;

    }
}
