package com.labway.lims.outsourcing.service.chain.his.get;

import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.outsourcing.api.dto.his.HisSample;
import com.labway.lims.outsourcing.api.dto.his.HisSampleItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.labway.lims.outsourcing.service.chain.his.get.GetHisSampleContext.HIS_SAMPLE;

@Component
@Slf4j
public class GetHisSamplePostCommand implements Command {

    @DubboReference
    private DictService dictService;

    @Override
    public boolean execute(Context context) throws Exception {
        final GetHisSampleContext from = GetHisSampleContext.from(context);
        final List<HisSampleItem> hisSampleItems = from.getHisSampleItems();

        final OutApplyInfoDTO data = from.getOutApplyInfo();
        final HspOrganizationDto hspOrganization = from.getHspOrganization();

        final ArrayList<String> types = new ArrayList<>();
        types.add(DictEnum.VISIT_TYPE.name());
        types.add(DictEnum.SAMPLE_PROPERTY.name());
        final Map<String, Map<String, String>> dictNameMap =
            dictService.selectByDictTypes(types).stream().collect(Collectors.groupingBy(DictItemDto::getDictType,
                Collectors.toMap(DictItemDto::getDictName, DictItemDto::getDictCode, (a, b) -> a)));

        HisSample hisSample = new HisSample();
        hisSample.setHspOrgId(hspOrganization.getHspOrgId());
        hisSample.setHspOrgCode(hspOrganization.getHspOrgCode());
        hisSample.setHspOrgName(hspOrganization.getHspOrgName());
        hisSample.setOutBarcode(data.getBarcode());
        hisSample.setApplyTypeName(StringUtils.defaultString(data.getApplyType()));
        hisSample.setPatientName(StringUtils.defaultString(data.getPatientName()));
        hisSample.setPatientSex(ObjectUtils.defaultIfNull(data.getPatientSex(), SexEnum.DEFAULT.getCode()));
        hisSample.setPatientBirthday(data.getPatientBirthday());
        // 如果年龄为空，则默认为0 防止报错
        hisSample.setPatientAge(ObjectUtils.defaultIfNull(data.getPatientAge(), NumberUtils.INTEGER_ZERO));
        hisSample.setPatientSubage(data.getPatientSubage());
        hisSample.setPatientSubageUnit(data.getPatientSubageUnit());
        hisSample.setUrgent(data.getUrgent());
        hisSample.setSampleCount(ObjectUtils.defaultIfNull(data.getSampleNum(), NumberUtils.INTEGER_ONE));
        hisSample.setApplyDate(data.getApplyDate());
        hisSample.setSamplingDate(data.getSamplingDate());
        hisSample.setPatientVisitCard(StringUtils.defaultString(data.getPatientVisitCard()));
        hisSample.setDept(StringUtils.defaultString(data.getDept()));
        hisSample.setPatientBed(StringUtils.defaultString(data.getPatientBed()));
        hisSample.setClinicalDiagnosis(StringUtils.defaultString(data.getClinicalDiagnosis()));
        hisSample.setRemark(StringUtils.defaultString(data.getRemark()));
        hisSample.setPatientMobile(StringUtils.defaultString(data.getPatientMobile()));
        hisSample.setPatientCard(StringUtils.defaultString(data.getPatientCard()));
        hisSample.setPatientCardType(StringUtils.defaultString(data.getPatientCardType()));
        hisSample.setSendDoctor(StringUtils.defaultString(data.getSendDoctor()));
        hisSample.setPatientAddress(StringUtils.defaultString(data.getPatientAddress()));
        // 就诊类型
        hisSample.setApplyTypeName(StringUtils.defaultString(data.getApplyType()));
        hisSample.setApplyTypeCode(dictNameMap.getOrDefault(DictEnum.VISIT_TYPE.name(), Collections.emptyMap())
            .getOrDefault(data.getApplyType(), StringUtils.EMPTY));

        // 样本性状
        hisSample.setSampleProperty(StringUtils.defaultString(data.getSampleProperty()));
        hisSample
            .setSamplePropertyCode(dictNameMap.getOrDefault(DictEnum.SAMPLE_PROPERTY.name(), Collections.emptyMap())
                .getOrDefault(data.getSampleProperty(), StringUtils.EMPTY));

		// ✨feat：【1.1.4】增加标本部位字段 此字段用于签收病理的样本时，落库  https://www.tapd.cn/59091617/prong/stories/view/1159091617001002126?from_iteration_id=1159091617001000242
	    hisSample.setPatientPart(StringUtils.defaultString(data.getPatientPart()));
        // 医院流水号
        hisSample.setHisSerialNo(data.getHisSerialNo());

	    hisSample.setHisSampleItems(hisSampleItems);
	    hisSample.setOriginalOrgCode(StringUtils.defaultString(data.getOrigoutOrgcode()));
	    hisSample.setOriginalOrgName(StringUtils.defaultString(data.getOrigoutOrgname()));
	    hisSample.setOrgCode(LoginUserHandler.get().getOrgCode());
	    hisSample.setOrgName(LoginUserHandler.get().getOrgName());
        from.put(HIS_SAMPLE, hisSample);

        return CONTINUE_PROCESSING;
    }
}
