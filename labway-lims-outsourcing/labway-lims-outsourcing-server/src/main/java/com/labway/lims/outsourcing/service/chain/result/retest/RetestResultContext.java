package com.labway.lims.outsourcing.service.chain.result.retest;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.outsourcing.service.chain.result.SaveResultContext;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.List;
import java.util.Objects;

@SuppressWarnings("unchecked")
public class RetestResultContext extends SaveResultContext {

    /**
     * 复查主记录
     */
    static final String RETEST_MAIN = "RETEST_MAIN_" + IdUtil.objectId();

    /**
     * 是否已经完成复查
     */
    static final String IS_COMPLETE = "IS_COMPLETE_" + IdUtil.objectId();

    /**
     * 所有复查中的子项
     */
    static final String SAMPLE_RETEST_ITEMS = "SAMPLE_RETEST_ITEMS_" + IdUtil.objectId();


    /**
     * 复查依赖于保存结果上下文
     */
    @Getter
    @Setter
    private SaveResultContext saveResultContext;


    public static RetestResultContext from(Context context) {
        return (RetestResultContext) context;
    }


    public SampleRetestMainDto getSampleRetestMain() {
        return (SampleRetestMainDto) get(RETEST_MAIN);
    }


    public List<SampleRetestItemDto> getSampleRetestItems() {
        return (List<SampleRetestItemDto>) get(SAMPLE_RETEST_ITEMS);
    }

    public boolean isComplete() {
        return containsKey(IS_COMPLETE) && (boolean) get(IS_COMPLETE);
    }

    @Override
    public Object get(Object key) {
        final Object v = super.get(key);
        if (Objects.nonNull(v)) {
            return v;
        }

        if (Objects.nonNull(saveResultContext)) {
            return saveResultContext.get(key);
        }

        return null;
    }

    @Override
    public Object put(Object key, Object value) {
        if (Objects.nonNull(saveResultContext)) {
            saveResultContext.put(key, value);
        }
        return super.put(key, value);
    }

    @Override
    protected String getWatchName() {
        return "复查结果";
    }
}
