package com.labway.lims.outsourcing.service.chain.his.sign;

import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.outsourcing.api.dto.SignHisSampleItemDto;
import com.labway.lims.outsourcing.api.dto.his.HisSignParam;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <pre>
 * SignHisSampleCheckParamCommand
 * 参数校验
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/15 17:47
 */
@Component
public class SignHisSampleCheckParamCommand implements Command {

    /**
     * 业务中台的机构编码
     */
    public static final String BUSINESS_CENTER_ORG_CODE = EnvDetector.BUSINESS_CENTER_ORG_CODE;

    @Resource
    private Environment environment;
    @Resource
    private RedisPrefix redisPrefix;

    @Override
    public boolean execute(Context context) throws Exception {
        SignHisSampleContext from = SignHisSampleContext.from(context);
        HisSignParam sign = from.getHisSignParam();

        final List<SignHisSampleItemDto> items = sign.getItems();
        if (CollectionUtils.isEmpty(items)) {
            throw new IllegalStateException(String.format("条码 [%s] 没有需要签收的项目", sign.getOutBarcode()));
        }
        final Collection<String> getHisSampleItemIds =
                items.stream().map(SignHisSampleItemDto::getHisSampleItemId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(getHisSampleItemIds)) {
            throw new IllegalStateException(String.format("条码 [%s] 没有需要签收的项目", sign.getOutBarcode()));
        }

        final String orgCode = environment.getProperty(BUSINESS_CENTER_ORG_CODE);
        if (StringUtils.isBlank(orgCode)) {
            throw new IllegalArgumentException(String.format("请配置 %s 参数", BUSINESS_CENTER_ORG_CODE));
        }

        final String signLock =
                redisPrefix.getBasePrefix() + "his:sign:" + sign.getHspOrgId() + ":" + sign.getOutBarcode();
        from.setSignLockKey(signLock);

        return CONTINUE_PROCESSING;
    }

}
