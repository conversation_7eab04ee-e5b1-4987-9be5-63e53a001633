package com.labway.lims.outsourcing.service.chain.pick.two;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.List;
import java.util.Map;

/**
 * 二次分拣上下文
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class OutsourcingTwoPickContext extends StopWatchContext {


    /**
     * 申请单样本
     */
    static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();


    /**
     * 样本
     */
    static final String OUTSOURCING_SAMPLE = "OUTSOURCING_SAMPLE_" + IdUtil.objectId();

    /**
     * 申请单
     */
    static final String APPLY = "APPLY_" + IdUtil.objectId();

    /**
     * 专业小组
     */
    static final String INSTRUMENT_GROUP = "INSTRUMENT_GROUP_" + IdUtil.objectId();

    /**
     * 仪器
     */
    static final String INSTRUMENT = "INSTRUMENT_" + IdUtil.objectId();

    /**
     * 申请单项目
     */
    static final String APPLY_SAMPLE_ITEMS = "APPLY_SAMPLE_ITEMS_" + IdUtil.objectId();

    /**
     * 报告项目
     */
    static final String REPORT_ITEMS = "REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 仪器报告项目
     */
    static final String INSTRUMENT_REPORT_ITEMS = "INSTRUMENT_REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 报告项目所属的申请单项目
     */
    static final String REPORT_ITEM_CODE_2_TEST_ITEM_ID = "REPORT_ITEM_CODE_2_TEST_ITEM_ID_" + IdUtil.objectId();

    /**
     * 申请单项目详情
     */
    static final String APPLY_SAMPLE_ITEM_DETAILS = "APPLY_SAMPLE_ITEM_DETAILS_" + IdUtil.objectId();


    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 样本号
     */
    private String sampleNo;


    public static OutsourcingTwoPickContext from(Context context) {
        return (OutsourcingTwoPickContext) context;
    }

    /**
     * 获取申请单
     */
    public ApplyDto getApply() {
        return (ApplyDto) get(APPLY);
    }


    /**
     * 获取申请单样本
     */
    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }


    /**
     * 获取样本
     */
    public OutsourcingSampleDto getSample() {
        return (OutsourcingSampleDto) get(OUTSOURCING_SAMPLE);
    }


    /**
     * 获取专业小组
     */
    public InstrumentGroupDto getInstrumentGroup() {
        return (InstrumentGroupDto) get(INSTRUMENT_GROUP);
    }


    /**
     * 获取仪器
     */
    public InstrumentDto getInstrument() {
        return (InstrumentDto) get(INSTRUMENT);
    }


    /**
     * 获取仪器
     */
    public List<ApplySampleItemDto> getApplySampleItems() {
        return (List<ApplySampleItemDto>) get(APPLY_SAMPLE_ITEMS);
    }


    /**
     * 获取报告项目
     */
    public List<ReportItemDto> getReportItems() {
        return (List<ReportItemDto>) get(REPORT_ITEMS);
    }


    /**
     * 仪器报告项目
     */
    public List<InstrumentReportItemDto> getInstrumentReportItems() {
        return (List<InstrumentReportItemDto>) get(INSTRUMENT_REPORT_ITEMS);
    }

    /**
     * 报告项目所属的申请单项目
     */
    public Map<String, Long> getReportItemCode2TestItemIdMap() {
        return (Map<String, Long>) get(REPORT_ITEM_CODE_2_TEST_ITEM_ID);
    }

    /**
     * 获取申请单项目详情
     */
    public List<TestItemDto> getApplySampleItemDetails() {
        return (List<TestItemDto>) get(APPLY_SAMPLE_ITEM_DETAILS);
    }

    @Override
    protected String getWatchName() {
        return "二次分拣";
    }
}
