package com.labway.lims.outsourcing.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.labway.business.center.compare.dto.OrgApplyResultSamplesDTO;
import com.labway.business.center.compare.dto.QueryOrgItemInfoDto;
import com.labway.business.center.compare.dto.QueryOrgItemMappingInfoDto;
import com.labway.business.center.compare.dto.TbOrgApplySampleMainIteResultDTO;
import com.labway.business.center.compare.request.DeleteOrgItemMappingInfoRequest;
import com.labway.business.center.compare.request.QueryLimsOutSourceSampleInfoRequest;
import com.labway.business.center.compare.request.QueryOrgItemInfoRequest;
import com.labway.business.center.compare.request.QueryOrgItemMappingInfoRequest;
import com.labway.business.center.compare.request.SaveOrgItemMappingInfoRequest;
import com.labway.business.center.compare.service.ILimsOutSourceSampleService;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.outsourcing.api.dto.BetterOutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleCondition;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.SyncOutsourcingSampleResultDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.outsourcing.enums.ItemType;
import com.labway.lims.outsourcing.vo.QueryOutsourcingSampleResultResponseVo;
import com.labway.lims.outsourcing.vo.QueryOutsourcingSampleResultVo;
import com.labway.lims.outsourcing.vo.SyncOutsourcingSampleResultVo;
import com.labway.lims.routine.api.dto.ReportItemDeleteDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/20 16:20
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping("/outsourcing-result")
public class OutsourcingSampleResultController extends BaseController {

    @Value("${business-center.org-code:00010110000000001WND}")
    private String orgCode;
    @Value("${business-center.org-name:}")
    private String orgName;
    @Resource
    private ILimsOutSourceSampleService limsOutSourceSampleService;

    @Resource
    private OutsourcingSampleService outsourcingSampleService;

    @Resource
    private ApplySampleService applySampleService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @Resource
    private EnvDetector envDetector;

    @DubboReference
    private SystemParamService systemParamService;


    @PostMapping("/samples")
    public Object samples(@RequestBody QueryOutsourcingSampleResultVo vo) {

        if (Objects.isNull(vo.getSendDateStart()) || Objects.isNull(vo.getSendDateEnd())
                || vo.getSendDateEnd().before(vo.getSendDateStart())) {
            throw new IllegalStateException("日期错误");
        }

        if (Objects.isNull(vo.getExportOrgId())) {
            throw new IllegalArgumentException("外送机构不存在");
        }

        final OutsourcingSampleCondition condition = new OutsourcingSampleCondition();
        BeanUtils.copyProperties(vo, condition);

        final List<BetterOutsourcingSampleDto> samples = outsourcingSampleService.selectByCondition(condition);
        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }


        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(vo.getExportOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalArgumentException("外送机构不存在");
        }

        final QueryLimsOutSourceSampleInfoRequest req = new QueryLimsOutSourceSampleInfoRequest();
        req.setOrgCode(hspOrganization.getHspOrgCode());
        req.setBarCodes(samples.stream().map(OutsourcingSampleDto::getBarcode)
                .distinct().collect(Collectors.toList()));
        req.setHspOrgCode(orgCode);

        // TODO: 2024/2/1 修改调用外部结果的来源
        log.info("调用外部结果，查询外送样本结果信息开始，查询入参：{}", JSONObject.toJSONString(req));
        List<OrgApplyResultSamplesDTO> data = limsOutSourceSampleService.queryResultSample(req).getData();
        log.info("调用外部结果，查询外送样本结果信息结束，返回信息：{}", JSONObject.toJSONString(data));

        final List<OrgApplyResultSamplesDTO> outSamples = ObjectUtils.defaultIfNull(data, List.of());

        // 移除忽略报告项目 如果配置了需要忽略的报告项目 则需要移除掉
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.IGNORE_OUTSOURCING_REPORT_ITEM.getCode(), LoginUserHandler.get().getOrgId());
        if (param != null && StringUtils.isNotBlank(param.getParamValue())) {
            doIgnoreItem(outSamples, param);
        }

        final Map<String, OrgApplyResultSamplesDTO> outSampleMap = outSamples.stream()
                .collect(Collectors.toMap(OrgApplyResultSamplesDTO::getBarcode, v -> v, (a, b) -> a));

        // 取并集，删除不存在的
        samples.removeIf(e -> !outSampleMap.containsKey(e.getBarcode()));
        Map<Long, HspOrganizationDto> organizationDtoMap = hspOrganizationService.selectByHspOrgIdsAsMap(
                samples.stream().map(BetterOutsourcingSampleDto::getHspOrgId).collect(Collectors.toSet()));

        final var list = new ArrayList<QueryOutsourcingSampleResultResponseVo>();
        for (BetterOutsourcingSampleDto e : samples) {
            final var v = new QueryOutsourcingSampleResultResponseVo();
            v.setResults(new LinkedList<>());
            v.setStatus(e.getStatus());
            v.setBarcode(e.getBarcode());
            v.setSampleNo(e.getSampleNo());
            v.setOutsourcingSampleId(e.getOutsourcingSampleId());
            v.setTestDate(e.getTestDate());
            final OrgApplyResultSamplesDTO k = outSampleMap.get(e.getBarcode());
            if (Objects.nonNull(k)) {
                Optional.ofNullable(k.getTestDate()).ifPresent(v::setTestDate);
                final List<TbOrgApplySampleMainIteResultDTO> rs = ObjectUtils.defaultIfNull(k.getTbOrgApplySampleMainIteResultDTOS(),
                        List.of());

                for (var r : rs) {
                    log.info("样本号【{}】的结果值信息:{}",e.getBarcode(),JSONObject.toJSONString(r));
                    final QueryOutsourcingSampleResultResponseVo.Result t = new QueryOutsourcingSampleResultResponseVo.Result();
                    t.setResult(r.getTestResult());
                    t.setId(IdUtil.simpleUUID());
                    t.setTestItemCode(r.getItemTestCode());
                    t.setTestItemName(r.getItemTestName());
                    t.setUnit(r.getResultUnit());
                    t.setRange(r.getReferenceValue());
                    if (Objects.equals(r.getIsCritical(), YesOrNoEnum.YES.getCode())) {
                        t.setStatus(ResultStatusEnum.CRISIS.getCode());
                    } else if (Objects.equals(r.getIsException(), YesOrNoEnum.YES.getCode())) {
                        t.setStatus(ResultStatusEnum.EXCEPTION.getCode());
                    } else {
                        t.setStatus(ResultStatusEnum.NORMAL.getCode());
                    }
                    t.setReportItemCode(r.getCompareItemReportCode());
                    t.setReportItemName(r.getCompareItemReportName());
                    t.setJudge(r.getTestJudge());
                    t.setTestItemMappingError(CollectionUtils.isEmpty(r.getCompareTestItem()));
                    t.setReportItemMappingError(StringUtils.isBlank(r.getCompareItemReportCode()));

                    if (t.getReportItemMappingError()) {
                        t.setReportItemMappingErrorText(String.format("【%s】未设置报告项目对照",r.getItemReportCode()+"|"+r.getItemReportName()));
                    }

                    if (t.getTestItemMappingError()) {
                        t.setTestItemMappingErrorText(String.format("【%s】未设置申请项目对照",r.getItemTestCode()+"|"+r.getItemTestName()));
                    }

                    // 填充迪安项目
                    t.setDianReportItemCode(r.getItemReportCode());
                    t.setDianReportItemName(r.getItemReportName());
                    t.setDianTestItemCode(r.getItemTestCode());
                    t.setDianTestItemName(r.getItemTestName());

                    v.getResults().add(t);
                }
            }

            if ((envDetector.isDev() || envDetector.isTest()) && CollectionUtils.isEmpty(v.getResults())) {
                for (int i = 0; i < RandomUtils.nextInt(3, 10); i++) {
                    final var t = new QueryOutsourcingSampleResultResponseVo.Result();
                    t.setResult(String.format("%.2f", RandomUtils.nextDouble(10, 100)));
                    t.setId(IdUtil.simpleUUID());
                    t.setTestItemCode(RandomStringUtils.randomAlphabetic(5));
                    t.setTestItemName(RandomStringUtils.randomAlphabetic(5));
                    t.setUnit(RandomStringUtils.randomAlphabetic(5));
                    t.setRange(RandomStringUtils.randomAlphabetic(5));
                    t.setReportItemCode(RandomStringUtils.randomAlphabetic(5));
                    t.setReportItemName(RandomStringUtils.randomAlphabetic(5));
                    t.setJudge(RandomStringUtils.randomAlphabetic(5));
                    t.setStatus(RandomUtils.nextInt(0, 3));
                    t.setTestItemMappingError(RandomUtils.nextBoolean());
                    t.setReportItemMappingError(RandomUtils.nextBoolean());
                    v.getResults().add(t);
                }
            }

            v.setPatientName(e.getPatientName());
            if (organizationDtoMap.containsKey(e.getHspOrgId())) {
                v.setHspOrgCode(organizationDtoMap.get(e.getHspOrgId()).getHspOrgCode());
            } else {
                log.warn("没有查到送检机构信息【机构ID：{}，机构名称：{}】", e.getHspOrgId(), e.getHspOrgName());
            }
            v.setHspOrgName(e.getHspOrgName());
            v.setExportOrgName(e.getExportOrgName());
            // 如果包含一个错误 那么就是没有对照
            v.setItemMapping(v.getResults().stream().allMatch(t -> BooleanUtils.isFalse(t.getReportItemMappingError()))
                    && v.getResults().stream().allMatch(t -> BooleanUtils.isFalse(t.getReportItemMappingError())));
            v.setSendDate(e.getCreateDate());
            list.add(v);
        }

        return list;
    }

    /**
     * 同步结果
     */
    @PostMapping("/sync")
    public Object sync(@RequestBody SyncOutsourcingSampleResultVo vo) {

        if (CollectionUtils.isEmpty(vo.getOutsourcingSampleIds())) {
            throw new IllegalArgumentException("请选择样本");
        }

        outsourcingSampleService.syncOutsourcingSamples(JSON.parseObject(JSON.toJSONString(vo), SyncOutsourcingSampleResultDto.class));

        return Map.of();
    }


    /**
     * 查询实验室的检验(报告)项目信息
     */
    @PostMapping("/queryOrgItemInfo")
    public Response<List<QueryOrgItemInfoDto>> queryOrgItemInfo(@RequestBody QueryOrgItemInfoRequest queryOrgItemInfoRequest) {
        queryOrgItemInfoRequest.setOrgCode(orgCode);
        Response<List<QueryOrgItemInfoDto>> listResponse = limsOutSourceSampleService.queryOrgItemInfo(queryOrgItemInfoRequest);

        // 相同报告项目去重
        Set<String> exists = new HashSet<>();
        Iterator<QueryOrgItemInfoDto> iterator = listResponse.getData().iterator();
        while (Objects.equals(queryOrgItemInfoRequest.getItemType(), ItemType.REPORT_ITEM.getCode()) && iterator.hasNext()) {
            QueryOrgItemInfoDto next = iterator.next();
            if (!exists.add(next.getItemId() + next.getItemCode() + next.getItemName())) {
                iterator.remove();
            }
        }

        return listResponse;
    }


    /**
     * 保存实验室和外部项目(检验项目/报告项目)的对照关系
     */
    @PostMapping("/saveOrgItemMappingInfo")
    public Response<String> saveOrgItemMappingInfo(@RequestBody SaveOrgItemMappingInfoRequest saveOrgItemMappingInfoRequest) {
        if (CollectionUtils.isEmpty(saveOrgItemMappingInfoRequest.getUpdateSampleInfos())) {
            // throw new IllegalArgumentException("样本条码信息不能为空");
        }
        if (CollectionUtils.isEmpty(saveOrgItemMappingInfoRequest.getItemMappingInfo())) {
            return Response.success();
        }
        saveOrgItemMappingInfoRequest.setOrgCode(orgCode);
        saveOrgItemMappingInfoRequest.setOrgName(orgName);
        return limsOutSourceSampleService.saveOrgItemMappingInfo(saveOrgItemMappingInfoRequest);
    }

    /**
     * 查询检验（报告）项目的对照关系
     */
    @PostMapping("/queryOrgItemMappingInfo")
    public Response<List<QueryOrgItemMappingInfoDto>> queryOrgItemMappingInfo(@RequestBody QueryOrgItemMappingInfoRequest queryOrgItemMappingInfoRequest) {
        queryOrgItemMappingInfoRequest.setOrgCode(orgCode);
        return limsOutSourceSampleService.queryOrgItemMappingInfo(queryOrgItemMappingInfoRequest);
    }

    /**
     * 删除项目对照关系
     */
    @PostMapping("/deleteOrgItemMappingInfo")
    public Response<String> deleteOrgItemMappingInfo(@RequestBody DeleteOrgItemMappingInfoRequest deleteOrgItemMappingInfoRequest) {
        deleteOrgItemMappingInfoRequest.setOrgCode(orgCode);
        return limsOutSourceSampleService.deleteOrgItemMappingInfo(deleteOrgItemMappingInfoRequest);
    }


    @PostMapping("/delete")
    public Object deleteReportItem(@RequestBody ReportItemDeleteDto dto) {
        if (Objects.isNull(dto.getSampleReportItemId()) || Objects.isNull(dto.getSampleId())
                || StringUtils.isBlank(dto.getReportItemCode())) {
            throw new IllegalStateException("参数错误");
        }
        sampleReportItemService.deleteSampleReportItem(dto, ItemTypeEnum.OUTSOURCING);
        return Collections.emptyMap();
    }


    // 移除忽略的报告项目
    private void doIgnoreItem(List<OrgApplyResultSamplesDTO> outSamples, SystemParamDto param) {

        JSONArray objects = JSONObject.parseArray(param.getParamValue());
        List<String> ignoreItemCode = objects.stream().map(e -> ((JSONObject)e).getString("reportCode")).collect(Collectors.toList());

        for (OrgApplyResultSamplesDTO outSample : outSamples) {
            final List<TbOrgApplySampleMainIteResultDTO> resultItems = outSample.getTbOrgApplySampleMainIteResultDTOS();
            if (CollectionUtils.isNotEmpty(resultItems)){
                resultItems.removeIf(e -> ignoreItemCode.contains(e.getItemReportCode()));
            }
        }

    }


}
