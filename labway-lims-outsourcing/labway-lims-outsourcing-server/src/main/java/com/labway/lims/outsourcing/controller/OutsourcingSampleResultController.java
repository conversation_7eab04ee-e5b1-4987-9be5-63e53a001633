package com.labway.lims.outsourcing.controller;

import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.QueryOrgItemInfoDto;
import com.labway.business.center.compare.dto.QueryOrgItemMappingInfoDto;
import com.labway.business.center.compare.request.DeleteOrgItemMappingInfoRequest;
import com.labway.business.center.compare.request.QueryOrgItemInfoRequest;
import com.labway.business.center.compare.request.QueryOrgItemMappingInfoRequest;
import com.labway.business.center.compare.request.SaveOrgItemMappingInfoRequest;
import com.labway.business.center.compare.service.ILimsOutSourceSampleService;
import com.labway.business.center.compare.service.TbOrgApplySampleMainService;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.config.LimsOrgCodeConfig;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.outsourcing.api.dto.QueryOutsourcingSampleResultDto;
import com.labway.lims.outsourcing.api.dto.SyncOutsourcingSampleResultDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.outsourcing.enums.ItemType;
import com.labway.lims.outsourcing.service.chain.result.InstrumentReportReferenceCommand;
import com.labway.lims.outsourcing.vo.QueryOutsourcingSampleResultVo;
import com.labway.lims.outsourcing.vo.SyncOutsourcingSampleResultVo;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/4/20 16:20
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping("/outsourcing-result")
public class OutsourcingSampleResultController extends BaseController {

    @Value("${business-center.org-code:00010110000000001WND}")
    private String orgCode;
    @Value("${business-center.org-name:}")
    private String orgName;
    @Resource
    private ILimsOutSourceSampleService limsOutSourceSampleService;
    @Resource
    private TbOrgApplySampleMainService tbOrgApplySampleMainService;

    @Resource
    private OutsourcingSampleService outsourcingSampleService;

    @DubboReference
    private ApplySampleService applySampleService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Resource
    private EnvDetector envDetector;

    @DubboReference
    private SystemParamService systemParamService;

    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;
    @DubboReference
    private ApplyService applyService;
    @Autowired
    private LimsOrgCodeConfig limsOrgCodeConfig;

    @PostMapping("/samples")
    public Object samples(@RequestBody QueryOutsourcingSampleResultVo vo) {
        if (Objects.isNull(vo.getSendDateStart()) || Objects.isNull(vo.getSendDateEnd())
                || vo.getSendDateEnd().before(vo.getSendDateStart())) {
            throw new IllegalStateException("日期错误");
        }

        if (Objects.isNull(vo.getExportOrgId())) {
            throw new IllegalArgumentException("外送机构不存在");
        }

        final Long exportOrgId = vo.getExportOrgId();

        // 查询外送机构信息
        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(exportOrgId);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalArgumentException("外送机构不存在");
        }

        final String mappedOrgCode = limsOrgCodeConfig.getMappedOrgCode(hspOrganization.getHspOrgCode());
        if (limsOrgCodeConfig.isLimsSystem(mappedOrgCode)) {
            return outsourcingSampleService.selectOutSamplesLims(JSON.parseObject(JSON.toJSONString(vo), QueryOutsourcingSampleResultDto.class));
        }

        // 实验室外送到其他非LIMS系统机构
        return outsourcingSampleService.selectOutSamplesOld(JSON.parseObject(JSON.toJSONString(vo), QueryOutsourcingSampleResultDto.class));

    }

    /**
     * 同步结果
     */
    @PostMapping("/sync")
    public Object sync(@RequestBody SyncOutsourcingSampleResultVo vo) {

        if (CollectionUtils.isEmpty(vo.getOutsourcingSampleIds())) {
            throw new IllegalArgumentException("请选择样本");
        }

        outsourcingSampleService.syncOutsourcingSamples(JSON.parseObject(JSON.toJSONString(vo), SyncOutsourcingSampleResultDto.class));

        return Map.of();
    }


    /**
     * 查询实验室的检验(报告)项目信息
     */
    @PostMapping("/queryOrgItemInfo")
    public Response<List<QueryOrgItemInfoDto>> queryOrgItemInfo(@RequestBody QueryOrgItemInfoRequest queryOrgItemInfoRequest) {
        log.info("查询实验室的检验(报告)项目信息 参数 [{}]", JSON.toJSONString(queryOrgItemInfoRequest));
        queryOrgItemInfoRequest.setOrgCode(orgCode);
        log.info("查询实验室的检验(报告)项目信息 参数 [{}]", JSON.toJSONString(queryOrgItemInfoRequest));
        Response<List<QueryOrgItemInfoDto>> listResponse = limsOutSourceSampleService.queryOrgItemInfo(queryOrgItemInfoRequest);

        // 相同报告项目去重
        Set<String> exists = new HashSet<>();
        Iterator<QueryOrgItemInfoDto> iterator = listResponse.getData().iterator();
        while (Objects.equals(queryOrgItemInfoRequest.getItemType(), ItemType.REPORT_ITEM.getCode()) && iterator.hasNext()) {
            QueryOrgItemInfoDto next = iterator.next();
            if (!exists.add(next.getItemId() + next.getItemCode() + next.getItemName())) {
                iterator.remove();
            }
        }

        return listResponse;
    }

    /**
     * 保存实验室和外部项目(检验项目/报告项目)的对照关系
     */
    @PostMapping("/saveOrgItemMappingInfo")
    public Response<String> saveOrgItemMappingInfo(@RequestBody SaveOrgItemMappingInfoRequest saveOrgItemMappingInfoRequest) {
        log.info("保存实验室和外部项目(检验项目/报告项目)的对照关系 参数 [{}]", JSON.toJSONString(saveOrgItemMappingInfoRequest));
        if (CollectionUtils.isEmpty(saveOrgItemMappingInfoRequest.getUpdateSampleInfos())) {
            // throw new IllegalArgumentException("样本条码信息不能为空");
        }
        if (CollectionUtils.isEmpty(saveOrgItemMappingInfoRequest.getItemMappingInfo())) {
            return Response.success();
        }
        saveOrgItemMappingInfoRequest.setOrgCode(orgCode);
        saveOrgItemMappingInfoRequest.setOrgName(orgName);
        final String hspOrgCode = saveOrgItemMappingInfoRequest.getHspOrgCode();
        saveOrgItemMappingInfoRequest.setHspOrgCode(limsOrgCodeConfig.getMappedOrgCode(hspOrgCode));
        log.info("保存实验室和外部项目(检验项目/报告项目)的对照关系 参数 [{}]", JSON.toJSONString(saveOrgItemMappingInfoRequest));
        return limsOutSourceSampleService.saveOrgItemMappingInfo(saveOrgItemMappingInfoRequest);
    }

    /**
     * 查询检验（报告）项目的对照关系
     */
    @PostMapping("/queryOrgItemMappingInfo")
    public Response<List<QueryOrgItemMappingInfoDto>> queryOrgItemMappingInfo(@RequestBody QueryOrgItemMappingInfoRequest queryOrgItemMappingInfoRequest) {
        log.info("查询检验（报告）项目的对照关系 参数 [{}]", JSON.toJSONString(queryOrgItemMappingInfoRequest));
        queryOrgItemMappingInfoRequest.setOrgCode(orgCode);
        final String hspOrgCode = queryOrgItemMappingInfoRequest.getHspOrgCode();
        queryOrgItemMappingInfoRequest.setHspOrgCode(limsOrgCodeConfig.getMappedOrgCode(hspOrgCode));
        log.info("查询检验（报告）项目的对照关系 参数 [{}]", JSON.toJSONString(queryOrgItemMappingInfoRequest));
        return limsOutSourceSampleService.queryOrgItemMappingInfo(queryOrgItemMappingInfoRequest);
    }

    /**
     * 删除项目对照关系
     */
    @PostMapping("/deleteOrgItemMappingInfo")
    public Response<String> deleteOrgItemMappingInfo(@RequestBody DeleteOrgItemMappingInfoRequest deleteOrgItemMappingInfoRequest) {
        log.info("删除项目对照关系 参数 [{}]", JSON.toJSONString(deleteOrgItemMappingInfoRequest));
        deleteOrgItemMappingInfoRequest.setOrgCode(orgCode);
        final String hspOrgCode = deleteOrgItemMappingInfoRequest.getHspOrgCode();
        deleteOrgItemMappingInfoRequest.setHspOrgCode(limsOrgCodeConfig.getMappedOrgCode(hspOrgCode));
        log.info("删除项目对照关系 参数 [{}]", JSON.toJSONString(deleteOrgItemMappingInfoRequest));
        return limsOutSourceSampleService.deleteOrgItemMappingInfo(deleteOrgItemMappingInfoRequest);
    }

}
