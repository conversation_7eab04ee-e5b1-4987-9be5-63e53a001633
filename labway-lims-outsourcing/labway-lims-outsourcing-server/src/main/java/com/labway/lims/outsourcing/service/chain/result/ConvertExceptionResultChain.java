package com.labway.lims.outsourcing.service.chain.result;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.base.api.dto.InstrumentReportItemExceptionDto;
import com.labway.lims.base.api.enums.RelationalOperatorEnum;
import com.labway.lims.base.api.service.InstrumentReportItemExceptionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * 如果结果包含某些字符，并且 judge 为空的时候，那么给个上箭头
 */
@Slf4j
@Component
public class ConvertExceptionResultChain implements Command {
    @DubboReference
    private InstrumentReportItemExceptionService instrumentReportItemExceptionService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        if (StringUtils.isNotBlank(context.getResultJudge())) {
            return CONTINUE_PROCESSING;
        }

        if (context.isException() || context.isCritical()) {
            return CONTINUE_PROCESSING;
        }

        final List<InstrumentReportItemExceptionDto> instrumentReportItemExceptions = instrumentReportItemExceptionService.selectByInstrumentReportItemId(
                context.getInstrumentReportItem().getInstrumentReportItemId());
        if (CollectionUtils.isEmpty(instrumentReportItemExceptions)) {
            return CONTINUE_PROCESSING;
        }


//        final InstrumentReportItemExceptionDto e = instrumentReportItemExceptions.stream()
//                .filter(k -> {
//                    // 完全匹配
//                    if (Objects.equals(k.getResult(), context.getResult())) {
//                        return true;
//                    }
//
//                    // 数字匹配
//                    if (NumberUtils.isParsable(k.getResult()) && NumberUtils.isParsable(context.getResult())) {
//                        return NumberUtils.toScaledBigDecimal(k.getResult()).equals(NumberUtils.toScaledBigDecimal(context.getResult()));
//                    }
//
//                    return false;
//                })
//                .findFirst().orElse(null);

        final InstrumentReportItemExceptionDto e = instrumentReportItemExceptions.stream()
                .filter(k -> {

//                    // 完全匹配
//                    if (Objects.equals(k.getResult(), context.getResult())) {
//                        return true;
//                    }
//
//                    // 数字匹配
//                    if (NumberUtils.isParsable(k.getResult()) && NumberUtils.isParsable(context.getResult())) {
//                        return NumberUtils.toScaledBigDecimal(k.getResult(), 6, RoundingMode.HALF_EVEN)
//                                .equals(NumberUtils.toScaledBigDecimal(context.getResult(), 6, RoundingMode.HALF_EVEN));
//                    }
//
//                    return false;

                    // 异常结果排判断，由于异常结果维护变了，所以这里的判断逻辑需要全部换掉，利用新的判断逻辑进行处理
                    return curTip(k, context.getResult(), Collections.emptyList());
                })
                .findFirst().orElse(null);

        if (Objects.isNull(e)) {
            return CONTINUE_PROCESSING;
        }

        if (Objects.equals(e.getIsException(), YesOrNoEnum.YES.getCode())) {
            context.put(SaveResultContext.RESULT_IS_EXCEPTION, true);
            context.put(SaveResultContext.RESULT_JUDGE, TestJudgeEnum.UP.name());
        }

        return CONTINUE_PROCESSING;
    }

    // 判断是否是异常结果
    private boolean curTip(InstrumentReportItemExceptionDto itemTip, String result, List<String> convertMsgList) {
        final RelationalOperatorEnum operatorMaxEnum = RelationalOperatorEnum.valueOfByOperator(itemTip.getFormulaMax());

        // 包含
        if (Objects.equals(operatorMaxEnum, RelationalOperatorEnum.CONTAINS)) {
            if (StringUtils.contains(result, itemTip.getFormulaMaxValue())) {
                return Objects.equals(itemTip.getIsException(), YesOrNoEnum.YES.getCode());
            }
        } else if (Objects.equals(operatorMaxEnum, RelationalOperatorEnum.DOES_NOT_CONTAINS)) { // 不包含
            if (!StringUtils.contains(result, itemTip.getFormulaMaxValue())) {
                return Objects.equals(itemTip.getIsException(), YesOrNoEnum.YES.getCode());
            }
        } else {

            // 如果都是数字的时候，那么转成数字去匹配
            Object value = result;
            Object maxValue = itemTip.getFormulaMaxValue();
            if (NumberUtils.isParsable(result)
                    && NumberUtils.isParsable(itemTip.getFormulaMaxValue())) {
                value = new BigDecimal(result);
                maxValue = new BigDecimal(itemTip.getFormulaMaxValue());
            }


            if (Objects.equals(operatorMaxEnum, RelationalOperatorEnum.EQ)) {
                if (Objects.equals(value, maxValue)) {
                    return Objects.equals(itemTip.getIsException(), YesOrNoEnum.YES.getCode());
                } else if (value instanceof BigDecimal && ((BigDecimal) value).compareTo((BigDecimal) maxValue) == NumberUtils.INTEGER_ZERO) {
                    return Objects.equals(itemTip.getIsException(), YesOrNoEnum.YES.getCode());
                }
                return Boolean.FALSE;
            }

            // 判断大小的时候必须是数字
            if (!NumberUtils.isParsable(result) || !NumberUtils.isParsable(itemTip.getFormulaMaxValue())) {
                return Boolean.FALSE;
            }
            // 第二个范围不等于空 但是不是数字就返回
            if (StringUtils.isNotBlank(itemTip.getFormulaMinValue()) && !NumberUtils.isParsable(itemTip.getFormulaMinValue())) {
                return Boolean.FALSE;
            }


            if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.LT) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) < NumberUtils.INTEGER_ZERO)) {
                return Objects.equals(itemTip.getIsException(), YesOrNoEnum.YES.getCode());
            } else if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.LE) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) <= NumberUtils.INTEGER_ZERO)) {
                return Objects.equals(itemTip.getIsException(), YesOrNoEnum.YES.getCode());
            }


            BigDecimal minValue = NumberUtils.isParsable(itemTip.getFormulaMinValue()) ? new BigDecimal(itemTip.getFormulaMinValue()) : null;

            if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.GT) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) > NumberUtils.INTEGER_ZERO)) {
                return curMinTip(minValue, convertMsgList, itemTip, (BigDecimal) value);
            } else if (value instanceof BigDecimal && Objects.equals(operatorMaxEnum, RelationalOperatorEnum.GE) && (((BigDecimal) value).compareTo((BigDecimal) maxValue) >= NumberUtils.INTEGER_ZERO)) {
                return curMinTip(minValue, convertMsgList, itemTip, (BigDecimal) value);
            }

        }

        return Boolean.FALSE;
    }

    private boolean curMinTip(BigDecimal minValue, List<String> errorMsgList, InstrumentReportItemExceptionDto itemTip, BigDecimal value) {
        final RelationalOperatorEnum operatorMinEnum = RelationalOperatorEnum.valueOfByOperator(itemTip.getFormulaMin());

        if (Objects.isNull(operatorMinEnum) || Objects.isNull(minValue)) {
            return Objects.equals(itemTip.getIsException(), YesOrNoEnum.YES.getCode());
        }

        if (Objects.equals(operatorMinEnum, RelationalOperatorEnum.LT) && (value.compareTo(minValue) < NumberUtils.INTEGER_ZERO)) {
            return Objects.equals(itemTip.getIsException(), YesOrNoEnum.YES.getCode());
        } else if (Objects.equals(operatorMinEnum, RelationalOperatorEnum.LE) && (value.compareTo(minValue) <= NumberUtils.INTEGER_ZERO)) {
            return Objects.equals(itemTip.getIsException(), YesOrNoEnum.YES.getCode());
        }

        return Boolean.FALSE;
    }


}
