package com.labway.lims.outsourcing.service.chain.result;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 校验结果是否可以保存
 *
 * <AUTHOR>
 * @since 2023/3/30 16:13
 */
@Slf4j
@Component
public class CheckCanSaveCommand implements Command {

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final ApplySampleDto applySample = context.getApplySample();
        final InstrumentReportItemDto reportItem = context.getInstrumentReportItem();
        final OutsourcingSampleDto sample = context.getSample();

        if (!context.isApplySampleUpdate() && Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new IllegalStateException("已审核样本无法修改结果");
        }

        //判断结果是否允许前端手动输入
        if (context.getSource() == SaveResultSourceEnum.FRONT && (Objects.equals(reportItem.getIsManualInput(), YesOrNoEnum.NO.getCode()))) {
            throw new IllegalStateException(
                    String.format("项目 [%s] 不允许手动输入结果", reportItem.getReportItemName()));
        }
        //判断结果是否为空
        if (MapUtils.isEmpty(context.getContextResults())) {
            if (StringUtils.isBlank(context.getResult()) &&
                    Objects.equals(reportItem.getIsResultNull(), YesOrNoEnum.NO.getCode())) {
                throw new IllegalStateException(
                        String.format("样本号 [%s] 报告项目 [%s] 不允许空", sample.getSampleNo(), reportItem.getReportItemName()));
            }
        }

        //判断结果是否为0
        if (NumberUtils.isParsable(context.getResult())) {
            if (BigDecimal.ZERO.compareTo(NumberUtils.toScaledBigDecimal(context.getResult())) == 0
                    && Objects.equals(reportItem.getIsResultZero(), YesOrNoEnum.NO.getCode())) {
                throw new IllegalStateException(String.format("样本号 [%s] 报告项目 [%s] 不允许零", sample.getSampleNo(), reportItem.getReportItemName()));
            }
        }

        return CONTINUE_PROCESSING;
    }
}
