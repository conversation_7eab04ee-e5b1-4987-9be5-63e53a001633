package com.labway.lims.outsourcing.service.chain.his.get;

import cn.hutool.core.lang.Dict;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.ExceptionCodeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.api.dto.his.HisSampleItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetHisSampleProjectForbiddenCommand implements Command {

    @DubboReference
    private ApplyService applyService;

    @DubboReference
    private TestItemService testItemService;

    @DubboReference
    private ApplySampleItemService applySampleItemService;

    @Override
    public boolean execute(Context context) throws Exception {
        final GetHisSampleContext from = GetHisSampleContext.from(context);

        final HisGetParam hisGetParam = from.getHisGetParam();

        final String outBarcode = hisGetParam.getOutBarcode();

        // lis申請單
        final OutApplyInfoDTO outApplyInfo = from.getOutApplyInfo();
        // 申请单项目
        final List<OutApplyInfoDTO.OutApplyItem> outApplyItems = outApplyInfo.getItems();

        final Set<String> testItemCodes = outApplyItems.stream()
                .filter(f -> CollectionUtils.isNotEmpty(f.getItems()))
                .flatMap(m -> m.getItems().stream().map(OutApplyInfoDTO.TestItem::getTestItemCode))
                .collect(Collectors.toSet());

        // 实验室检验项目
        final List<TestItemDto> testItems = from.getLimsTestItemInfo();
        final Map<String, TestItemDto> testItemMap = testItems.stream()
                .collect(Collectors.toMap(TestItemDto::getTestItemCode, Function.identity(), (a, b) -> a));

        // 检验项目信息
        final List<HisSampleItem> hisSampleItems = new LinkedList<>();

        // 没有对照检验项目的外部项目编码
        Set<String> forbiddenOutTestItemCodes = new HashSet<>();
        Set<Dict> forbiddenTestItems = new HashSet<>();

        // 校验外部项目是否对照 LIMS 检验项目
        for (final OutApplyInfoDTO.OutApplyItem outApplyItem : outApplyItems) {
            final String outTestItemName = outApplyItem.getOutTestItemName();
            final String outTestItemCode = outApplyItem.getOutTestItemCode();
            // 对照的检验项目信息
            final List<OutApplyInfoDTO.TestItem> items = outApplyItem.getItems();


            if (StringUtils.isBlank(outTestItemName) || StringUtils.isBlank(outTestItemCode)) {
                throw new IllegalStateException("条码送检项目信息不完整");
            }

            for (OutApplyInfoDTO.TestItem item : items) {


                TestItemDto testItemDto = testItemMap.get(item.getTestItemCode());


            }

            // 被禁用的检验项目
            List<TestItemDto> forbiddenCollect = items.stream().map(e -> testItemMap.get(e.getTestItemCode())).
                    filter(p -> Objects.equals(p.getEnable(), YesOrNoEnum.NO.getCode())).collect(Collectors.toList());


            // 如果都启用则跳过
            if (CollectionUtils.isEmpty(forbiddenCollect)) {
                continue;
            }


            log.warn("外部条码 [{}] HIS项目名称 [{}] 编码 [{}] ，对照的LIMS检验项目 [{}] 未启用", outBarcode, outTestItemName, outTestItemCode,
                    forbiddenCollect.stream().map(TestItemDto::getTestItemName).collect(Collectors.joining(",")));

            forbiddenOutTestItemCodes.add(outTestItemCode);
            forbiddenTestItems.add(Dict.of("code", outTestItemCode, "name", forbiddenCollect.stream().map(TestItemDto::getTestItemName)
                    .collect(Collectors.joining(","))));
        }

        // 如果不为空 那就代表着有未对照的外部项目
        if (CollectionUtils.isNotEmpty(forbiddenOutTestItemCodes)) {
            // 1120 代表着未对照的外部项目 的错误码
            final LimsCodeException limsCodeException = new LimsCodeException(ExceptionCodeEnum.TEST_ITEM_FORBIDDEN_TIPS.getCode(),
                    String.format(ExceptionCodeEnum.TEST_ITEM_FORBIDDEN_TIPS.getDesc(), String.join(",", forbiddenOutTestItemCodes)));
            limsCodeException.setData(forbiddenTestItems);
            throw limsCodeException;
        }


        return CONTINUE_PROCESSING;
    }


}
