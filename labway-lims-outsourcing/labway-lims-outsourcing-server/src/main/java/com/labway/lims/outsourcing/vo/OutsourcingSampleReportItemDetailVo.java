package com.labway.lims.outsourcing.vo;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class OutsourcingSampleReportItemDetailVo {

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    private Long sampleReportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 检验项目Id
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;
    /**
     * 复查状态 0默认 1复查中 2已复查
     *
     * @see RetestStatusEnum
     */
    private Integer isRetest;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 结果
     */
    private String result;

    /**
     * 参考范围
     */
    private String range;

    /**
     * 单位
     */
    private String unit;

    /**
     * 结果判定
     *
     * @see TestJudgeEnum
     */
    private String judge;

    /**
     * 最近一次结果
     */
    private ResentResult resentResult;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 英文缩写
     */
    private String enAb;

    /**
     * 打印顺序
     */
    private Integer printSort;

    /**
     * 结果状态 1: 危机 2: 异常 0: 正常
     *
     * @see ResultStatusEnum
     */
    private Integer status;

    /**
     * 检验方法编码
     */
    private String examMethodCode;

    /**
     * 检验方法名称
     */
    private String examMethodName;

    @Getter
    @Setter
    public static final class ResentResult implements Serializable {


        /**
         * 结果
         */
        private String result;

        /**
         * 结果状态 1: 危机 2: 异常 0: 正常
         *
         * @see ResultStatusEnum
         */
        private Integer status;

        /**
         * 结果判定
         *
         * @see TestJudgeEnum
         */
        private String judge;
    }

}