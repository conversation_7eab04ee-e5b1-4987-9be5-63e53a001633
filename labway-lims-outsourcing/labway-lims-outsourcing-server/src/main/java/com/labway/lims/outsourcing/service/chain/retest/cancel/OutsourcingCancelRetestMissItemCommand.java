package com.labway.lims.outsourcing.service.chain.retest.cancel;

import com.labway.lims.outsourcing.service.chain.retest.OutsourcingStartRetestUpdateMissItemCommand;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 删除缺项
 */
@Slf4j
@Component
class OutsourcingCancelRetestMissItemCommand implements Command {
    @Resource
    private OutsourcingStartRetestUpdateMissItemCommand outsourcingStartRetestUpdateMissItemCommand;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingCancelRetestContext context = OutsourcingCancelRetestContext.from(c);


        // 如果是取消整个样本，那么删除这个redis key
        if (context.getReportItemId() == null) {
            stringRedisTemplate.delete(outsourcingStartRetestUpdateMissItemCommand.getMissItemKey(context.getOutsourcingSampleId()));
        } else {

            final SampleReportItemDto sampleReportItem = context.getSampleReportItems().stream()
                    .filter(e -> Objects.equals(e.getReportItemId(), context.getReportItemId()))
                    .findFirst().orElse(null);
            if (Objects.isNull(sampleReportItem)) {
                return CONTINUE_PROCESSING;
            }

            // 复查缺项删除
            stringRedisTemplate.opsForHash().delete(outsourcingStartRetestUpdateMissItemCommand.getMissItemKey(context.getOutsourcingSampleId()),
                    sampleReportItem.getReportItemCode());

        }


        return CONTINUE_PROCESSING;
    }
}
