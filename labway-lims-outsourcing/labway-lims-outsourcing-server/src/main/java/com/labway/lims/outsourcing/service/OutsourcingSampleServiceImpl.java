package com.labway.lims.outsourcing.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.ReportNoUtils;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.enums.routine.TestResultTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplyUpdateBeforeCheckTipDto;
import com.labway.lims.apply.api.dto.ApplyUpdateBeforeCheckTipItemDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.SampleImageService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.FinanceSampleLockService;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.outsourcing.api.dto.BetterOutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleAuditDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleCancelAuditDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleCondition;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleTwoUnPickInfoDto;
import com.labway.lims.outsourcing.api.dto.QueryOutsourcingSamplesDto;
import com.labway.lims.outsourcing.api.dto.SyncOutsourcingSampleResultDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.outsourcing.mapper.OutsourcingSampleMapper;
import com.labway.lims.outsourcing.model.TbOutsourcingSample;
import com.labway.lims.outsourcing.service.chain.audit.AuditSampleChain;
import com.labway.lims.outsourcing.service.chain.audit.AuditSampleContext;
import com.labway.lims.outsourcing.service.chain.audit.BuildReportCommand;
import com.labway.lims.outsourcing.service.chain.pick.two.OutsourcingTwoPickChain;
import com.labway.lims.outsourcing.service.chain.pick.two.OutsourcingTwoPickContext;
import com.labway.lims.outsourcing.service.chain.result.InstrumentReportReferenceCommand;
import com.labway.lims.outsourcing.service.chain.result.NumberResultCommand;
import com.labway.lims.outsourcing.service.chain.sync.SyncOutsourcingChain;
import com.labway.lims.outsourcing.service.chain.sync.SyncOutsourcingContext;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 外送样本
 */
@Slf4j
@DubboService(interfaceClass = OutsourcingSampleService.class)
public class OutsourcingSampleServiceImpl extends ServiceImpl<OutsourcingSampleMapper, TbOutsourcingSample> implements OutsourcingSampleService {
    @Resource
    private OutsourcingSampleMapper outsourcingSampleMapper;
    @Resource
    private AuditSampleChain auditSampleChain;
    @Resource
    private SyncOutsourcingChain syncOutsourcingChain;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleResultService sampleResultService;
    @Resource
    private OutsourcingTwoPickChain outsourcingTwoPickChain;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private EnvDetector envDetector;
    @DubboReference
    private SampleReportService sampleReportService;
    @Resource
    private ReportNoUtils reportNoUtils;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private FinanceSampleLockService financeSampleLockService;
    @DubboReference
    private SampleImageService sampleImageService;

    @Resource
    private BuildReportCommand buildReportCommand;
    @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;
    @Resource
    private NumberResultCommand numberResultCommand;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    @DubboReference
    private SystemParamService systemParamService;

    private static final String ROUTING_KEY = "sample_change_key";

    @Nullable
    @Override
    public OutsourcingSampleDto selectByOutsourcingSampleId(long outsourcingSampleId) {
        return convert(outsourcingSampleMapper.selectById(outsourcingSampleId));
    }

    @Override
    public List<OutsourcingSampleDto> selectByOutsourcingSampleIds(Collection<Long> outsourcingSampleIds) {
        final LambdaQueryWrapper<TbOutsourcingSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbOutsourcingSample::getOutsourcingSampleId, outsourcingSampleIds);

        return outsourcingSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    public OutsourcingSampleDto selectByApplySampleId(long applySampleId) {

        final List<OutsourcingSampleDto> list = selectByApplySampleIds(List.of(applySampleId));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.iterator().next();
    }

    @Override
    public List<OutsourcingSampleDto> selectByApplySampleIds(Collection<Long> applySampleIds) {

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        return outsourcingSampleMapper
                .selectList(
                        new LambdaQueryWrapper<TbOutsourcingSample>().in(TbOutsourcingSample::getApplySampleId, applySampleIds))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<OutsourcingSampleDto> selectByCreateDate(Date beginCreateDate, Date endCreateDate, long orgId) {
        return outsourcingSampleMapper.selectByCreateDate(beginCreateDate, endCreateDate, orgId);
    }

    @Override
    public List<OutsourcingSampleDto> selectByCreateDateAndGroup(Date beginCreateDate, Date endCreateDate, Long orgId, Long groupId) {
        return outsourcingSampleMapper.selectByCreateDateAndGroup(beginCreateDate, endCreateDate, orgId, groupId);
    }

    @Override
    public boolean updateByOutsourcingSampleId(OutsourcingSampleDto outsourcingSample) {
        final TbOutsourcingSample os = new TbOutsourcingSample();
        BeanUtils.copyProperties(outsourcingSample, os);

        if (outsourcingSampleMapper.updateById(os) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改外送样本成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(os));

        return true;
    }

    @Override
    public List<OutsourcingSampleDto> selectByTestDate(QueryOutsourcingSamplesDto dto) {
        final LambdaQueryWrapper<TbOutsourcingSample> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(TbOutsourcingSample::getGroupId, LoginUserHandler.get().getGroupId())
                .eq(TbOutsourcingSample::getOrgId, LoginUserHandler.get().getOrgId())
                .eq(Objects.nonNull(dto.getHspOrgId()), TbOutsourcingSample::getHspOrgId, dto.getHspOrgId())
                .eq(Objects.nonNull(dto.getExportOrgId()), TbOutsourcingSample::getExportOrgId, dto.getExportOrgId())
                .ge(Objects.nonNull(dto.getTestDateStart()), TbOutsourcingSample::getTestDate, dto.getTestDateStart())
                .le(Objects.nonNull(dto.getTestDateEnd()), TbOutsourcingSample::getTestDate, dto.getTestDateEnd())
                .orderByAsc(TbOutsourcingSample::getOutsourcingSampleId);
        return outsourcingSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<OutsourcingSampleDto> selectByApplyId(long applyId) {
        final LambdaQueryWrapper<TbOutsourcingSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbOutsourcingSample::getApplyId, applyId);
        return outsourcingSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditSamplesChain(OutsourcingSampleAuditDto auditDto) {
        if (CollectionUtils.isEmpty(auditDto.getOutsourcingSampleIds())) {
            log.info("审核样本id为空");
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        final Collection<Long> outsourcingSampleIds = auditDto.getOutsourcingSampleIds();

        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.OUTSOURCING_ONE_AUDIT.getCode(), loginUser.getOrgId());

        final AuditSampleContext context = new AuditSampleContext();
        context.setUser(loginUser);
        context.setOutsourcingSampleIds(outsourcingSampleIds);
        context.put(AuditSampleContext.SAMPLE_AUDTI_DTO, auditDto);
        context.setAuditType(SampleAuditStatusEnum.getByName(auditDto.getAuditStatus()));
        context.setOneCheck(Objects.equals(ObjectUtils.defaultIfNull(param, new SystemParamDto()).getParamValue(), String.valueOf(YesOrNoEnum.YES.getCode())));

        try {
            if (!auditSampleChain.execute(context)) {
                throw new IllegalStateException("审核失败");
            }
        } catch (RuntimeException e) {
            log.error("外送样本审核失败 [{}]", outsourcingSampleIds, e);
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            log.info("外送样本审核 [{}] 耗时\n{}", outsourcingSampleIds, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));

        }
    }

    @Override
    public void syncOutsourcingSamples(SyncOutsourcingSampleResultDto dto) {
        final SyncOutsourcingContext context = new SyncOutsourcingContext();
        context.setOutsourcingSampleIds(dto.getOutsourcingSampleIds());
        context.setIgnoreMissingReportItem(dto.isIgnoreMissingReportItem());
        try {
            if (!syncOutsourcingChain.execute(context)) {
                throw new IllegalStateException("审核失败");
            }
        } catch (RuntimeException e) {
            log.error("外送样本同步审核失败 [{}]", dto, e);
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            log.info("外送样本同步审核 [{}] 耗时\n{}", dto, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));

        }
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelAuditSample(OutsourcingSampleCancelAuditDto dto) {
        final OutsourcingSampleDto sample = selectByOutsourcingSampleId(dto.getOutsourcingSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本信息不存在，请稍后再试");
        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        if (applySampleService.isDisabled(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", sample.getBarcode()));
        }

        if (applySampleService.isTerminate(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", sample.getBarcode()));
        }

        if (BooleanUtils.isTrue(financeSampleLockService.isSampleLock(sample.getApplySampleId()))) {
            throw new IllegalStateException("该样本已经加锁，不能取消审核");
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("条码[" + sample.getBarcode() + "]申请单样本不存在");
        }

        SampleAuditStatusEnum auditStatusEnum = SampleAuditStatusEnum.getByName(dto.getAuditStatus());
        boolean isCancelOneCheck = Objects.equals(auditStatusEnum, SampleAuditStatusEnum.CANCEL_ONE_CHECK);

        // 委外增加取消一审按钮，这里需要判断是否配置了委外一次审核配置
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.OUTSOURCING_ONE_AUDIT.getCode(), LoginUserHandler.get().getOrgId());
        boolean isEnableOnCheck = Objects.equals(ObjectUtils.defaultIfNull(param, new SystemParamDto()).getParamValue(), String.valueOf(YesOrNoEnum.YES.getCode()));


        if (!isEnableOnCheck && isCancelOneCheck) {
            throw new IllegalStateException("未配置委外一次审核配置，不能取消一审!");
        }

        if (isCancelOneCheck) {
            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) {
                throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 不是一审状态,不能取消一审！");
            }
        } else {
            if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
                throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 不是审核状态,无法取消审核！");
            }
            // 检查审核人信息  取消一审不需要账号密码弹窗
            checkAuditorInfo(dto);
        }

        // 修改样本状态
        final ApplySampleDto as = new ApplySampleDto();
        as.setStatus(isCancelOneCheck || !isEnableOnCheck ? SampleStatusEnum.NOT_AUDIT.getCode() : SampleStatusEnum.ONE_AUDIT.getCode());
        as.setApplySampleId(sample.getApplySampleId());
        as.setPrintDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        as.setIsPrint(YesOrNoEnum.NO.getCode());
        as.setPrinterName(StringUtils.EMPTY);
        as.setPrinterId(NumberUtils.LONG_ZERO);
        // 取消审核清空报告编号
        as.setReportNo(Strings.EMPTY);
        applySampleService.updateByApplySampleId(as);
        // 审人设置为空
        final OutsourcingSampleDto sampleDto = new OutsourcingSampleDto();
        sampleDto.setOutsourcingSampleId(sample.getOutsourcingSampleId());
        sampleDto.setCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        sampleDto.setCheckerId(NumberUtils.LONG_ZERO);
        sampleDto.setCheckerName(StringUtils.EMPTY);

        if (isCancelOneCheck || !isEnableOnCheck) {
            // 一审人置空
            sampleDto.setOneCheckerId(NumberUtils.LONG_ZERO);
            sampleDto.setOneCheckerName(StringUtils.EMPTY);
            sampleDto.setOneCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        }
        outsourcingSampleService.updateByOutsourcingSampleId(sampleDto);

        // 删除报告单
//        sampleReportService.deleteBySampleIds(Collections.singleton(dto.getOutsourcingSampleId()));
        sampleReportService.deleteBySampleIdsAndNotUpload(Collections.singleton(dto.getOutsourcingSampleId()));


        final SampleFlowDto flow = new SampleFlowDto();
        flow.setApplyId(sample.getApplyId());
        flow.setApplySampleId(sample.getApplySampleId());
        flow.setBarcode(sample.getBarcode());
        flow.setOperateCode(isCancelOneCheck ? BarcodeFlowEnum.CANCEL_ONE_CHECK.name() : BarcodeFlowEnum.CANCEL_SAMPLE_CHECK.name());
        flow.setOperateName(isCancelOneCheck ? BarcodeFlowEnum.CANCEL_ONE_CHECK.getDesc() : BarcodeFlowEnum.CANCEL_SAMPLE_CHECK.getDesc());
        flow.setOperator(user.getNickname());
        flow.setContent(flow.getOperateName());
        flow.setOrgId(user.getOrgId());
        flow.setOrgName(user.getOrgName());
        flow.setIsDelete(YesOrNoEnum.NO.getCode());
        flow.setCreateDate(new Date());
        flow.setCreatorId(user.getUserId());
        flow.setCreatorName(user.getNickname());
        flow.setUpdateDate(new Date());
        flow.setUpdaterId(user.getUserId());
        flow.setUpdaterName(user.getNickname());
        sampleFlowService.addSampleFlow(flow);

        threadPoolConfig.getPool().submit(() -> {
            try {
                final ApplyDto apply = applyService.selectByApplyId(sample.getApplyId());
                if (Objects.isNull(apply)) {
                    log.info("申请单不存在发送mq消息失败");
                    return;
                }
                final ApplySampleEventDto event = new ApplySampleEventDto();

                event.setOrgId(user.getOrgId());
                event.setHspOrgId(apply.getHspOrgId());
                event.setHspOrgCode(apply.getHspOrgCode());
                event.setHspOrgName(apply.getHspOrgName());
                event.setApplyId(sample.getApplyId());
                event.setApplySampleId(sample.getApplySampleId());
                event.setBarcode(sample.getBarcode());
                event.setExtras(Map.of("sampleId", String.valueOf(sample.getOutsourcingSampleId()), "outBarcode",
                        String.valueOf(applySample.getOutBarcode()), "sampleNo", String.valueOf(sample.getSampleNo()),
                        "businessCenterOrgCode", envDetector.getBusinessCenterOrgCode(),
						// 项目类型 外送
		                ApplySampleEventDto.ExtrasConstants.ITEM_TYPE, ItemTypeEnum.OUTSOURCING.name()));
                event.setEvent(isCancelOneCheck ? ApplySampleEventDto.EventType.CancelOneCheck : ApplySampleEventDto.EventType.CancelTwoCheck);

                final String json = JSON.toJSONString(event);
                rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

                log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", sample.getApplySampleId(), sample.getBarcode(), json,
                        RabbitMQService.EXCHANGE, ROUTING_KEY);
            } catch (Exception e) {
                log.error("发送消息失败:{}", e.getMessage(), e);
            }
        });

    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public long twoPick(long applySampleId, long instrumentGroupId, String sampleNo) {

        final OutsourcingTwoPickContext context = new OutsourcingTwoPickContext();
        context.setApplySampleId(applySampleId);
        context.setInstrumentGroupId(instrumentGroupId);
        context.setSampleNo(sampleNo);

        try {
            if (!outsourcingTwoPickChain.execute(context)) {
                throw new IllegalStateException("二次分拣失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        }

        log.info("外送检验样本二次分拣完成 样本ID [{}],条码号 [{}] 样本号 [{}]", context.getSample().getOutsourcingSampleId(),
                context.getSample().getBarcode(), sampleNo);

        return context.getSample().getOutsourcingSampleId();

    }

    @Override
    public OutsourcingSampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode())
                || Objects.equals(e.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new IllegalStateException("该样本已经审核，不能取消二次分拣");
        }

        final List<OutsourcingSampleDto> outsourcingSamples = selectByApplySampleIds(
                applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(outsourcingSamples)) {
            throw new IllegalStateException("外送样本不存在");
        }

        final Set<Long> ids =
                outsourcingSamples.stream().map(OutsourcingSampleDto::getOutsourcingSampleId).collect(Collectors.toSet());

        // 删除样本
        deleteByOutsourcingSampleIds(ids);

        // 删除样本报告项目
        sampleReportItemService.deleteBySampleIds(ids);

        // 删除样本结果
        sampleResultService.deleteBySampleIds(ids);

        // 删除 危机值
        sampleCriticalResultService.deleteBySampleIds(ids);

        // 清空生清单样本的结果备注，样本备注以及检验者
        final ApplySampleDto as = new ApplySampleDto();
        as.setSampleRemark(StringUtils.EMPTY);
        as.setResultRemark(StringUtils.EMPTY);
        as.setTesterId(NumberUtils.LONG_ZERO);
        as.setTesterName(StringUtils.EMPTY);
        applySampleService.updateByApplySampleIds(as, applySampleIds);


        log.info("外送检验取消二次分拣 样本ID [{}] 条码号 [{}] 操作人 [{}]", ids,applySamples.stream().map(ApplySampleDto::getBarcode)
                .collect(Collectors.toSet()), LoginUserHandler.get().getNickname());

        return new OutsourcingSampleTwoUnPickInfoDto(outsourcingSamples.stream()
                .map(e -> new OutsourcingSampleTwoUnPickInfoDto.Sample().setSampleId(e.getOutsourcingSampleId())
                        .setSampleNo(e.getSampleNo()).setGroupId(e.getGroupId())
                        .setTwoPickDate(
                                applySamples.stream().filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                                        .findFirst().map(ApplySampleDto::getTwoPickDate).orElseThrow())
                        .setInstrumentGroupId(e.getInstrumentGroupId()))
                .collect(Collectors.toList()));
    }

    @Override
    public long addOutsourcingSample(OutsourcingSampleDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final TbOutsourcingSample sample = JSON.parseObject(JSON.toJSONString(dto), TbOutsourcingSample.class);
        sample
                .setOutsourcingSampleId(ObjectUtils.defaultIfNull(dto.getOutsourcingSampleId(), snowflakeService.genId()));
        sample.setUpdaterId(user.getUserId());
        sample.setUpdaterName(user.getNickname());
        sample.setUpdateDate(new Date());
        sample.setCreateDate(new Date());
        sample.setCreatorId(user.getUserId());
        sample.setCreatorName(user.getNickname());
        sample.setIsDelete(YesOrNoEnum.NO.getCode());

        if (outsourcingSampleMapper.insert(sample) < 1) {
            throw new IllegalStateException("添加失败");
        }
        log.info("用户 [{}] 添加外送检验样本项成功 [{}]", user.getNickname(), JSON.toJSONString(sample));
        return sample.getOutsourcingSampleId();

    }

    @Override
    public boolean deleteByOutsourcingSampleId(long outsourcingSampleId) {
        if (outsourcingSampleMapper.deleteById(outsourcingSampleId) < 1) {
            return false;
        }
        log.info("用户 [{}] 删除外送检验样本 [{}] 成功", LoginUserHandler.get().getNickname(), outsourcingSampleId);
        return true;
    }

    @Override
    public void deleteByOutsourcingSampleIds(Collection<Long> outsourcingSampleIds) {

        if (CollectionUtils.isEmpty(outsourcingSampleIds)) {
            return;
        }

        outsourcingSampleMapper.deleteBatchIds(outsourcingSampleIds);

        log.info("用户 [{}] 删除外送检验样本 [{}] 成功", LoginUserHandler.get().getNickname(), outsourcingSampleIds);
    }

    @Override
    public List<OutsourcingSampleDto> selectByExportOrgId(long exportOrgId) {

        final LambdaQueryWrapper<TbOutsourcingSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbOutsourcingSample::getExportOrgId, exportOrgId);

        return outsourcingSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public void updateByOutsourcingSampleIds(OutsourcingSampleDto sampleDto, Collection<Long> outsourcingSampleIds) {
        if (CollectionUtils.isEmpty(outsourcingSampleIds)) {
            return;
        }
        outsourcingSampleMapper.updateByOutsourcingSampleIds(sampleDto, outsourcingSampleIds);

    }

    @Override
    public void updateByApplySampleIds(OutsourcingSampleDto sampleDto, Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return;
        }
        outsourcingSampleMapper.updateByApplySampleIds(sampleDto, applySampleIds);
    }

    @Override
    @SneakyThrows
    public SampleReportDto rebuildReport(long applySampleId) {
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            log.info("申请单样本不存在 [{}]", applySampleId);
            return null;
        }
        if (BooleanUtils.isFalse(Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode()))) {
            log.info("申请单样本状态不是审核状态 [{}]", applySampleId);
            return null;
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            log.info("申请单不存在 [{}]", applySample.getApplyId());
            return null;
        }

        final OutsourcingSampleDto sample = selectByApplySampleId(applySampleId);
        if (Objects.isNull(sample)) {
            log.info("样本不存在 [{}]", applySampleId);
            return null;
        }

        final List<SampleReportItemDto> sampleReportItems =
                sampleReportItemService.selectBySampleId(sample.getOutsourcingSampleId());
        final List<SampleResultDto> sampleResults =
                sampleResultService.selectBySampleId(sample.getOutsourcingSampleId());

        // 获取 对应仪器报告项目
        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentGroupId(sample.getInstrumentGroupId());

        // 检验项目
        final List<ApplySampleItemDto> items = applySampleItemService.selectByApplySampleId(sample.getApplySampleId());

        final String reportNo = reportNoUtils.genReportNo();
        final SampleReportDto sampleReport = buildReportCommand.buildPDF(sample, apply, applySample, sampleReportItems,
                sampleResults, instrumentReportItems, items, reportNo);

        // 更新报告编号
        final ApplySampleDto updateApplySampleDto = new ApplySampleDto();
        updateApplySampleDto.setApplySampleId(applySampleId);
        updateApplySampleDto.setReportNo(reportNo);
        applySampleService.updateByApplySampleId(updateApplySampleDto);

        log.info("重新生成报告: [{}]", sampleReport.getUrl());
        return sampleReport;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SampleImageDto> addImages(Long applyId, Long sampleId, List<String> imageUrls) {
        log.info("添加图片，【申请单ID：{}，样本ID：{}，图片ID：{}】", applyId, sampleId, JSON.toJSONString(imageUrls));
        ApplyDto applyDto = applyService.selectByApplyId(applyId);
        if (Objects.isNull(applyDto)) {
            throw new IllegalStateException("申请单不存在");
        }

        final OutsourcingSampleDto sampleDto = selectByOutsourcingSampleId(sampleId);
        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("样本不存在");
        }

        ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("申请单样本不存在");
        }
        Integer status = applySampleDto.getStatus();
        if (Objects.equals(SampleStatusEnum.ONE_AUDIT.getCode(), status) ||
                Objects.equals(SampleStatusEnum.AUDIT.getCode(), status)) {
            throw new IllegalStateException("样本已经审核，不能删除图片");
        }

        Date createDate = new Date();
        Long userId = LoginUserHandler.get().getUserId();
        String nickname = LoginUserHandler.get().getNickname();

        List<SampleImageDto> sampleImageDtos = imageUrls.stream().map(imageUrl -> {
            SampleImageDto entity = new SampleImageDto();
            entity.setSampleImageId(snowflakeService.genId());
            entity.setApplyId(applyId);
            entity.setSampleId(sampleId);
            entity.setApplySampleId(sampleDto.getApplySampleId());
            entity.setImageUrl(imageUrl);
            entity.setItemType(ItemTypeEnum.OUTSOURCING.name());
            entity.setImageName("");
            entity.setCreateDate(createDate);
            entity.setUpdateDate(createDate);
            entity.setCreatorId(userId);
            entity.setCreatorName(nickname);
            entity.setUpdaterId(userId);
            entity.setUpdaterName(nickname);
            entity.setIsDelete(YesOrNoEnum.NO.getCode());
            return entity;
        }).collect(Collectors.toList());
        sampleImageService.addSampleImage(sampleImageDtos);

        SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(applyId);
        sampleFlow.setApplySampleId(sampleDto.getApplySampleId());
        sampleFlow.setBarcode(sampleDto.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.RESULT_UPDATE.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.RESULT_UPDATE.getDesc());
        sampleFlow.setOperator(nickname);
        sampleFlow.setOperatorId(userId);
        sampleFlow.setContent(String.format("添加图片: [%s]", String.join(StringPool.COMMA, imageUrls)));

        // 添加条码环节
        sampleFlowService.addSampleFlow(sampleFlow);

        return sampleImageDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteImage(Long sampleImageId) {
        log.info("删除图片，【图片ID：{}】", sampleImageId);

        SampleImageDto sampleImageDto = sampleImageService.selectSampleImageById(sampleImageId);
        if (Objects.isNull(sampleImageDto)) {
            throw new IllegalStateException("样本图片不存在");
        }

        ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleImageDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        Integer status = applySampleDto.getStatus();
        if (Objects.equals(SampleStatusEnum.ONE_AUDIT.getCode(), status) ||
                Objects.equals(SampleStatusEnum.AUDIT.getCode(), status)) {
            throw new IllegalStateException("样本已经审核，不能删除图片");
        }

        if (sampleImageService.deleteSampleImage(sampleImageId)) {
            return false;
        }

        log.info("用户 [{}] 删除图片 [{}] 成功", LoginUserHandler.get().getNickname(), sampleImageId);

        SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(sampleImageDto.getApplyId());
        sampleFlow.setApplySampleId(sampleImageDto.getApplySampleId());
        sampleFlow.setBarcode(applySampleDto.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.RESULT_UPDATE.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.RESULT_UPDATE.getDesc());
        sampleFlow.setOperator(LoginUserHandler.get().getNickname());
        sampleFlow.setOperatorId(LoginUserHandler.get().getUserId());
        sampleFlow.setContent(String.format("删除图片: [%s]", sampleImageDto.getImageUrl()));

        // 添加条码环节
        sampleFlowService.addSampleFlow(sampleFlow);

        return true;
    }

    @Override
    public List<BetterOutsourcingSampleDto> selectByCondition(OutsourcingSampleCondition condition) {
        return outsourcingSampleMapper.selectByCondition(condition);
    }

    @Override
    public void updateByApplyId(OutsourcingSampleDto outsourcingSampleDto) {
        LambdaUpdateWrapper<TbOutsourcingSample> wrapper = Wrappers.lambdaUpdate(TbOutsourcingSample.class)
                .eq(TbOutsourcingSample::getApplyId, outsourcingSampleDto.getApplyId())
                .eq(TbOutsourcingSample::getIsDelete,0)
                .set(TbOutsourcingSample::getHspOrgId, outsourcingSampleDto.getHspOrgId())
                .set(TbOutsourcingSample::getHspOrgName,outsourcingSampleDto.getHspOrgName())
                .set(TbOutsourcingSample::getUpdaterId,outsourcingSampleDto.getUpdaterId())
                .set(TbOutsourcingSample::getUpdaterName,outsourcingSampleDto.getUpdaterName())
                .set(TbOutsourcingSample::getUpdateDate,outsourcingSampleDto.getUpdateDate());
        outsourcingSampleMapper.update(null, wrapper);
    }

    @Override
    public void updateByApplyIds(OutsourcingSampleDto outsourcingSampleDto, Collection<Long> applyIds) {

        for (List<Long> item : Lists.partition(new ArrayList<>(applyIds), 500)) {
            LambdaUpdateWrapper<TbOutsourcingSample> wrapper = Wrappers.lambdaUpdate(TbOutsourcingSample.class)
                .in(TbOutsourcingSample::getApplyId, item).eq(TbOutsourcingSample::getIsDelete, 0)
                .set(TbOutsourcingSample::getHspOrgId, outsourcingSampleDto.getHspOrgId())
                .set(TbOutsourcingSample::getHspOrgName, outsourcingSampleDto.getHspOrgName())
                .set(TbOutsourcingSample::getUpdaterId, outsourcingSampleDto.getUpdaterId())
                .set(TbOutsourcingSample::getUpdaterName, outsourcingSampleDto.getUpdaterName())
                .set(TbOutsourcingSample::getUpdateDate, outsourcingSampleDto.getUpdateDate());
            outsourcingSampleMapper.update(null, wrapper);
        }
    }

    @Override
    public ApplyUpdateBeforeCheckTipDto updateCheckExceptionAndCritical(List<ApplySampleDto> applySampleDtos, ApplyDto applyDto) {
        Long applyId = applyDto.getApplyId();

        List<ApplyUpdateBeforeCheckTipItemDto> criticalTips = new ArrayList<>();
        List<ApplyUpdateBeforeCheckTipItemDto> exceptionTips = new ArrayList<>();
        ApplyUpdateBeforeCheckTipDto checkTipDto = new ApplyUpdateBeforeCheckTipDto();
        checkTipDto.setCriticals(criticalTips);
        checkTipDto.setExceptions(exceptionTips);

        if (CollectionUtils.isEmpty(applySampleDtos)) {
            return checkTipDto;
        }

        Map<Long, ApplySampleDto> applySampleDtoMap = applySampleDtos.stream()
                .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity(), (a, b) -> b));

        List<OutsourcingSampleDto> sampleDtos = outsourcingSampleService.selectByApplyId(applyId);
        sampleDtos.forEach(sampleDto -> {
            ApplySampleDto applySampleDto = applySampleDtoMap.get(sampleDto.getApplySampleId());
            // 样本报告项目
            List<SampleReportItemDto> sampleReportItemDtos = sampleReportItemService.selectBySampleId(sampleDto.getOutsourcingSampleId());

            // 仪器报告项目
            final List<InstrumentReportItemDto> reportItems = instrumentReportItemService.selectByInstrumentGroupId(sampleDto.getInstrumentGroupId());
            // 样本结果 - 只查询数字类型的结果
            List<SampleResultDto> sampleResultDtos = sampleResultService.selectBySampleId(sampleDto.getOutsourcingSampleId());

            for (SampleResultDto sampleResult : sampleResultDtos) {
                String reportItemCode = sampleResult.getReportItemCode();

                InstrumentReportItemDto instrumentReportItem;

                // 取样本仪器报告项目，取不到为空
                instrumentReportItem = reportItems.stream().filter(e -> Objects.equals(e.getInstrumentId(), sampleDto.getInstrumentId())
                                && Objects.equals(e.getReportItemCode(), reportItemCode)).findFirst().orElse(null);
                // 在取专业小组报告项目
                if (Objects.isNull(instrumentReportItem)) {
                    instrumentReportItem = reportItems.stream().filter(e -> Objects.equals(e.getReportItemCode(), reportItemCode)).findFirst().orElse(null);
                }
                if (Objects.isNull(instrumentReportItem)) {
                    throw new IllegalStateException("仪器报告项目不存在");
                }

                // 非数值结果跳过处理
                if (!instrumentReportItem.getResultTypeCode().equals(TestResultTypeEnum.NUMBER.getCode()) || !NumberUtils.isParsable(sampleResult.getResult())) {
                    continue;
                }

                final List<InstrumentReportItemReferenceDto> refs = instrumentReportItemReferenceService.selectByInstrumentReportItemId(instrumentReportItem.getInstrumentReportItemId());
                if (CollectionUtils.isEmpty(refs)) {
                    continue;
                }
                final List<InstrumentReportItemReferenceDto> sampleRefs = refs.stream()
                        .filter(e -> Objects.equals(e.getInstrumentId(), sampleDto.getInstrumentId()))
                        .collect(Collectors.toList());
                InstrumentReportItemReferenceDto instrumentReportItemReferenceDto;
                instrumentReportItemReferenceDto =
                        instrumentReportReferenceCommand.filterCustomerReportReference(applyDto, applySampleDto, sampleRefs);
                // 先取样本仪器上的参考范围
                if (Objects.isNull(instrumentReportItemReferenceDto)) {
                    instrumentReportItemReferenceDto =
                            instrumentReportReferenceCommand.filterCustomerReportReference(applyDto, applySampleDto, refs);
                }

                if (Objects.nonNull(instrumentReportItemReferenceDto)) {
                    BigDecimal result = BigDecimal.valueOf(Double.parseDouble(sampleResult.getResult()));
                    boolean isCritical = numberResultCommand.checkIsCritical(result, instrumentReportItem, instrumentReportItemReferenceDto);
                    boolean isException = numberResultCommand.checkIsException(result, instrumentReportItem, instrumentReportItemReferenceDto);

                    SampleFlowDto sampleFlowDto;
                    if (isCritical || isException) {
                        List<SampleFlowDto> sampleFlowDtos = sampleFlowService.selectByBarcode(applySampleDto.getBarcode());
                        sampleFlowDto = sampleFlowDtos.get(sampleFlowDtos.size() - 1);

                        ApplyUpdateBeforeCheckTipItemDto tipDto = new ApplyUpdateBeforeCheckTipItemDto(
                                applySampleDto.getBarcode(), applySampleDto.getGroupName(), sampleFlowDto.getOperateName(),
                                sampleResult.getReportItemName(), sampleResult.getResult(),
                                instrumentReportItemReferenceDto.getCnRefereValue());
                        if (isCritical) {
                            criticalTips.add(tipDto);
                        } else {
                            exceptionTips.add(tipDto);
                        }
                    }
                }
            }
        });

        return checkTipDto;
    }

    private void checkAuditorInfo(OutsourcingSampleCancelAuditDto dto) {
        String auditorPwd = dto.getAuditPwd();
        Long auditorId = dto.getAuditId();

        if (Objects.isNull(auditorId)) {
            throw new IllegalStateException("当前审核人为空,请重新选择");
        }
        if (Objects.isNull(auditorPwd)) {
            throw new IllegalStateException("当前审核人密码为空");
        }

        final UserDto userDto = userService.selectByUserId(dto.getAuditId());
        if (Objects.isNull(userDto)) {
            throw new IllegalStateException("审核人账号不存在，请联系管理员");
        }

        if (StringUtils.isBlank(auditorPwd)) {
            throw new IllegalStateException("请输入审核人密码");
        }

        if (!userService.validPassword(userDto.getUsername(), userDto.getPassword(), dto.getAuditPwd())) {
            throw new IllegalStateException("输入的审核人密码错误，请重新输入");
        }

    }

    private OutsourcingSampleDto convert(TbOutsourcingSample outsourcingSample) {
        if (Objects.isNull(outsourcingSample)) {
            return null;
        }

        return JSON.parseObject(JSON.toJSONString(outsourcingSample), OutsourcingSampleDto.class);
    }

}
