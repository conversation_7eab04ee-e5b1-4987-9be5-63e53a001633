package com.labway.lims.outsourcing.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.business.center.compare.dto.OrgApplyResultSamplesDTO;
import com.labway.business.center.compare.dto.SampleItemTestResultDTO;
import com.labway.business.center.compare.dto.SampleTestResultDTO;
import com.labway.business.center.compare.dto.TbOrgApplySampleMainIteResultDTO;
import com.labway.business.center.compare.request.CancelApplyFormSampleRequest;
import com.labway.business.center.compare.request.QueryLimsOutSourceSampleInfoRequest;
import com.labway.business.center.compare.service.ILimsOutSourceSampleService;
import com.labway.business.center.compare.service.TbOrgApplySampleMainService;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.LimsOrgCodeConfig;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.TestResultTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.ApplyUpdateBeforeCheckTipDto;
import com.labway.lims.apply.api.dto.ApplyUpdateBeforeCheckTipItemDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.SampleImageService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.FinanceSampleLockService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.outsourcing.api.dto.BetterOutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleAuditDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleCancelAuditDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleCondition;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleTwoUnPickInfoDto;
import com.labway.lims.outsourcing.api.dto.QueryOutsourcingSampleResultDto;
import com.labway.lims.outsourcing.api.dto.QueryOutsourcingSamplesDto;
import com.labway.lims.outsourcing.api.dto.SyncOutsourcingSampleResultDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.outsourcing.mapper.OutsourcingSampleMapper;
import com.labway.lims.outsourcing.model.TbOutsourcingSample;
import com.labway.lims.outsourcing.service.chain.audit.AuditSampleChain;
import com.labway.lims.outsourcing.service.chain.audit.AuditSampleContext;
import com.labway.lims.outsourcing.service.chain.audit.BuildReportCommand;
import com.labway.lims.outsourcing.service.chain.pick.two.OutsourcingTwoPickChain;
import com.labway.lims.outsourcing.service.chain.pick.two.OutsourcingTwoPickContext;
import com.labway.lims.outsourcing.service.chain.result.InstrumentReportReferenceCommand;
import com.labway.lims.outsourcing.service.chain.result.NumberResultCommand;
import com.labway.lims.outsourcing.service.chain.sync.SyncOutsourcingChain;
import com.labway.lims.outsourcing.service.chain.sync.SyncOutsourcingContext;
import com.labway.lims.outsourcing.vo.QueryOutsourcingSampleResultResponseVo;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.swak.frame.dto.Response;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 外送样本
 */
@Slf4j
@DubboService(interfaceClass = OutsourcingSampleService.class)
public class OutsourcingSampleServiceImpl implements OutsourcingSampleService {
    @Resource
    private OutsourcingSampleMapper outsourcingSampleMapper;
    @Resource
    private AuditSampleChain auditSampleChain;
    @Resource
    private SyncOutsourcingChain syncOutsourcingChain;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private UserService userService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleResultService sampleResultService;
    @Resource
    private OutsourcingTwoPickChain outsourcingTwoPickChain;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private EnvDetector envDetector;
    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private FinanceSampleLockService financeSampleLockService;
    @DubboReference
    private SampleImageService sampleImageService;

    @Resource
    private BuildReportCommand buildReportCommand;
    @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;
    @Resource
    private NumberResultCommand numberResultCommand;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;

    @Resource
    private TbOrgApplySampleMainService tbOrgApplySampleMainService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @Resource
    private ILimsOutSourceSampleService limsOutSourceSampleService;
    @DubboReference
    private SystemParamService systemParamService;


    @Value("${business-center.org-code}")
    private String orgCode;

    private static final String ROUTING_KEY = "sample_change_key";
    @Autowired
    private LimsOrgCodeConfig limsOrgCodeConfig;

    @Nullable
    @Override
    public OutsourcingSampleDto selectByOutsourcingSampleId(long outsourcingSampleId) {
        return convert(outsourcingSampleMapper.selectById(outsourcingSampleId));
    }

    @Override
    public List<OutsourcingSampleDto> selectByOutsourcingSampleIds(Collection<Long> outsourcingSampleIds) {
        final LambdaQueryWrapper<TbOutsourcingSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TbOutsourcingSample::getOutsourcingSampleId, outsourcingSampleIds);

        return outsourcingSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Nullable
    @Override
    public OutsourcingSampleDto selectByApplySampleId(long applySampleId) {

        final List<OutsourcingSampleDto> list = selectByApplySampleIds(List.of(applySampleId));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.iterator().next();
    }

    @Override
    public List<OutsourcingSampleDto> selectByApplySampleIds(Collection<Long> applySampleIds) {

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }

        return outsourcingSampleMapper
                .selectList(
                        new LambdaQueryWrapper<TbOutsourcingSample>().in(TbOutsourcingSample::getApplySampleId, applySampleIds))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<OutsourcingSampleDto> selectByCreateDate(Date beginCreateDate, Date endCreateDate, long orgId) {
        return outsourcingSampleMapper.selectByCreateDate(beginCreateDate, endCreateDate, orgId);
    }

    @Override
    public List<OutsourcingSampleDto> selectByCreateDateAndGroup(Date beginCreateDate, Date endCreateDate, Long orgId, Long groupId) {
        return outsourcingSampleMapper.selectByCreateDateAndGroup(beginCreateDate, endCreateDate, orgId, groupId);
    }

    @Override
    public boolean updateByOutsourcingSampleId(OutsourcingSampleDto outsourcingSample) {
        final TbOutsourcingSample os = new TbOutsourcingSample();
        BeanUtils.copyProperties(outsourcingSample, os);

        if (outsourcingSampleMapper.updateById(os) < 1) {
            return false;
        }

        log.info("用户 [{}] 修改外送样本成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(os));

        return true;
    }

    @Override
    public List<OutsourcingSampleDto> selectByTestDate(QueryOutsourcingSamplesDto dto) {
        final LambdaQueryWrapper<TbOutsourcingSample> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(TbOutsourcingSample::getGroupId, LoginUserHandler.get().getGroupId())
                .eq(TbOutsourcingSample::getOrgId, LoginUserHandler.get().getOrgId())
                .eq(Objects.nonNull(dto.getHspOrgId()), TbOutsourcingSample::getHspOrgId, dto.getHspOrgId())
                .eq(Objects.nonNull(dto.getExportOrgId()), TbOutsourcingSample::getExportOrgId, dto.getExportOrgId())
                .ge(Objects.nonNull(dto.getTestDateStart()), TbOutsourcingSample::getTestDate, dto.getTestDateStart())
                .le(Objects.nonNull(dto.getTestDateEnd()), TbOutsourcingSample::getTestDate, dto.getTestDateEnd())
                .orderByAsc(TbOutsourcingSample::getOutsourcingSampleId);
        return outsourcingSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<OutsourcingSampleDto> selectByApplyId(long applyId) {
        final LambdaQueryWrapper<TbOutsourcingSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbOutsourcingSample::getApplyId, applyId);
        return outsourcingSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditSamplesChain(OutsourcingSampleAuditDto auditDto) {
        if (CollectionUtils.isEmpty(auditDto.getOutsourcingSampleIds())) {
            log.info("审核样本id为空");
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        final Collection<Long> outsourcingSampleIds = auditDto.getOutsourcingSampleIds();
        final AuditSampleContext context = new AuditSampleContext();
        context.setUser(loginUser);
        context.setOutsourcingSampleIds(outsourcingSampleIds);
        context.put(AuditSampleContext.SAMPLE_AUDTI_DTO,auditDto);
        try {
            if (!auditSampleChain.execute(context)) {
                throw new IllegalStateException("审核失败");
            }
        } catch (RuntimeException e) {
            log.error("外送样本审核失败 [{}]", outsourcingSampleIds, e);
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            log.info("外送样本审核 [{}] 耗时\n{}", outsourcingSampleIds, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));

        }
    }

    @Override
    public void syncOutsourcingSamples(SyncOutsourcingSampleResultDto dto) {
        final SyncOutsourcingContext context = new SyncOutsourcingContext();
        context.setOutsourcingSampleIds(dto.getOutsourcingSampleIds());
        context.setIgnoreMissingReportItem(dto.isIgnoreMissingReportItem());
        context.setSkipNoresultReportItem(dto.getSkipNoresultReportItem());
        context.setExportOrgId(dto.getExportOrgId());
        try {
            if (!syncOutsourcingChain.execute(context)) {
                throw new IllegalStateException("审核失败");
            }
        } catch (RuntimeException e) {
            log.error("外送样本同步审核失败 [{}]", dto, e);
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            log.info("外送样本同步审核 [{}] 耗时\n{}", dto, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));

        }
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelAuditSample(OutsourcingSampleCancelAuditDto dto) {
        final OutsourcingSampleDto sample = selectByOutsourcingSampleId(dto.getOutsourcingSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本信息不存在，请稍后再试");
        }

        if (applySampleService.isDisabled(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", sample.getBarcode()));
        }

        if (applySampleService.isTerminate(sample.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", sample.getBarcode()));
        }

        if (BooleanUtils.isTrue(financeSampleLockService.isSampleLock(sample.getApplySampleId()))) {
            throw new IllegalStateException("该样本已经加锁，不能取消审核");
        }

        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("条码[" + sample.getBarcode() + "]申请单样本不存在");
        }
        if (!Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new IllegalStateException("条码 [" + sample.getBarcode() + "] 不是未审状态");
        }

        // 检查审核人信息
        checkAuditorInfo(dto);

        // 修改样本状态
        final ApplySampleDto as = new ApplySampleDto();
        as.setStatus(SampleStatusEnum.NOT_AUDIT.getCode());
        as.setApplySampleId(sample.getApplySampleId());
        as.setPrintDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        as.setIsPrint(YesOrNoEnum.NO.getCode());
        as.setPrinterName(StringUtils.EMPTY);
        as.setPrinterId(NumberUtils.LONG_ZERO);
        applySampleService.updateByApplySampleId(as);
        // 审人设置为空
        final OutsourcingSampleDto sampleDto = new OutsourcingSampleDto();
        sampleDto.setOutsourcingSampleId(sample.getOutsourcingSampleId());
        sampleDto.setCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        sampleDto.setCheckerId(NumberUtils.LONG_ZERO);
        sampleDto.setCheckerName(StringUtils.EMPTY);
        outsourcingSampleService.updateByOutsourcingSampleId(sampleDto);

        // 删除报告单
//        sampleReportService.deleteBySampleIds(Collections.singleton(dto.getOutsourcingSampleId()));
        sampleReportService.deleteBySampleIdsAndNotUpload(Collections.singleton(dto.getOutsourcingSampleId()));


        final SampleFlowDto flow = new SampleFlowDto();
        flow.setApplyId(sample.getApplyId());
        flow.setApplySampleId(sample.getApplySampleId());
        flow.setBarcode(sample.getBarcode());
        flow.setOperateCode(BarcodeFlowEnum.CANCEL_SAMPLE_CHECK.name());
        flow.setOperateName(BarcodeFlowEnum.CANCEL_SAMPLE_CHECK.getDesc());
        flow.setOperator(LoginUserHandler.get().getNickname());
        flow.setContent("取消审核");
        flow.setOrgId(LoginUserHandler.get().getOrgId());
        flow.setOrgName(LoginUserHandler.get().getOrgName());
        flow.setIsDelete(YesOrNoEnum.NO.getCode());
        flow.setCreateDate(new Date());
        flow.setCreatorId(LoginUserHandler.get().getUserId());
        flow.setCreatorName(LoginUserHandler.get().getNickname());
        flow.setUpdateDate(new Date());
        flow.setUpdaterId(LoginUserHandler.get().getUserId());
        flow.setUpdaterName(LoginUserHandler.get().getNickname());
        sampleFlowService.addSampleFlow(flow);

        final LoginUserHandler.User user = LoginUserHandler.get();
        threadPoolConfig.getPool().submit(() -> {
            try {
                final ApplyDto apply = applyService.selectByApplyId(sample.getApplyId());
                if (Objects.isNull(apply)) {
                    log.info("申请单不存在发送mq消息失败");
                    return;
                }
                final ApplySampleEventDto event = new ApplySampleEventDto();

                event.setOrgId(user.getOrgId());
                event.setHspOrgId(apply.getHspOrgId());
                event.setHspOrgCode(apply.getHspOrgCode());
                event.setHspOrgName(apply.getHspOrgName());
                event.setApplyId(sample.getApplyId());
                event.setApplySampleId(sample.getApplySampleId());
                event.setBarcode(sample.getBarcode());
                event.setExtras(Map.of("sampleId", String.valueOf(sample.getOutsourcingSampleId()), "outBarcode",
                        String.valueOf(applySample.getOutBarcode()), "sampleNo", String.valueOf(sample.getSampleNo()),
                        "businessCenterOrgCode", envDetector.getBusinessCenterOrgCode(),
						// 项目类型 外送
		                ApplySampleEventDto.ExtrasConstants.ITEM_TYPE, ItemTypeEnum.OUTSOURCING.name()));
                event.setEvent(ApplySampleEventDto.EventType.CancelTwoCheck);

                final String json = JSON.toJSONString(event);
                rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

                log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", sample.getApplySampleId(), sample.getBarcode(), json,
                        RabbitMQService.EXCHANGE, ROUTING_KEY);
            } catch (Exception e) {
                log.error("发送消息失败", e.getMessage(), e);
            }
        });

    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public long twoPick(long applySampleId, long instrumentGroupId, String sampleNo) {

        final OutsourcingTwoPickContext context = new OutsourcingTwoPickContext();
        context.setApplySampleId(applySampleId);
        context.setInstrumentGroupId(instrumentGroupId);
        context.setSampleNo(sampleNo);

        try {
            if (!outsourcingTwoPickChain.execute(context)) {
                throw new IllegalStateException("二次分拣失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        }

        log.info("外送检验样本二次分拣完成 样本ID [{}],条码号 [{}] 样本号 [{}]", context.getSample().getOutsourcingSampleId(),
                context.getSample().getBarcode(), sampleNo);

        return context.getSample().getOutsourcingSampleId();

    }

    @Override
    public OutsourcingSampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode())
                || Objects.equals(e.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new IllegalStateException("该样本已经审核，不能取消二次分拣");
        }

        final List<OutsourcingSampleDto> outsourcingSamples = selectByApplySampleIds(
                applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(outsourcingSamples)) {
            throw new IllegalStateException("外送样本不存在");
        }

        final Set<Long> ids =
                outsourcingSamples.stream().map(OutsourcingSampleDto::getOutsourcingSampleId).collect(Collectors.toSet());
        // 外送样本
        final List<OutsourcingSampleDto> outsourcingSampleDtos = outsourcingSampleService.selectByOutsourcingSampleIds(ids);

        // 删除样本
        deleteByOutsourcingSampleIds(ids);

        // 删除样本报告项目
        sampleReportItemService.deleteBySampleIds(ids);

        // 删除样本结果
        sampleResultService.deleteBySampleIds(ids);

        // 删除 危机值
        sampleCriticalResultService.deleteBySampleIds(ids);

        // 清空生清单样本的结果备注，样本备注以及检验者
        final ApplySampleDto as = new ApplySampleDto();
        as.setSampleRemark(StringUtils.EMPTY);
        as.setResultRemark(StringUtils.EMPTY);
        as.setTesterId(NumberUtils.LONG_ZERO);
        as.setTesterName(StringUtils.EMPTY);
        applySampleService.updateByApplySampleIds(as, applySampleIds);

        // 取消外送分拣，取消外送样本到业务中台
        this.cancelOutSendApplyToBusiness(outsourcingSampleDtos);

        log.info("外送检验取消二次分拣 样本ID [{}] 条码号 [{}] 操作人 [{}]", ids,applySamples.stream().map(ApplySampleDto::getBarcode)
                .collect(Collectors.toSet()), LoginUserHandler.get().getNickname());

        return new OutsourcingSampleTwoUnPickInfoDto(outsourcingSamples.stream()
                .map(e -> new OutsourcingSampleTwoUnPickInfoDto.Sample().setSampleId(e.getOutsourcingSampleId())
                        .setSampleNo(e.getSampleNo()).setGroupId(e.getGroupId())
                        .setTwoPickDate(
                                applySamples.stream().filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                                        .findFirst().map(ApplySampleDto::getTwoPickDate).orElseThrow())
                        .setInstrumentGroupId(e.getInstrumentGroupId()))
                .collect(Collectors.toList()));
    }

    @Override
    public long addOutsourcingSample(OutsourcingSampleDto dto) {
        final LoginUserHandler.User user = LoginUserHandler.get();
        final TbOutsourcingSample sample = JSON.parseObject(JSON.toJSONString(dto), TbOutsourcingSample.class);
        sample
                .setOutsourcingSampleId(ObjectUtils.defaultIfNull(dto.getOutsourcingSampleId(), snowflakeService.genId()));
        sample.setUpdaterId(user.getUserId());
        sample.setUpdaterName(user.getNickname());
        sample.setUpdateDate(new Date());
        sample.setCreateDate(new Date());
        sample.setCreatorId(user.getUserId());
        sample.setCreatorName(user.getNickname());
        sample.setIsDelete(YesOrNoEnum.NO.getCode());

        if (outsourcingSampleMapper.insert(sample) < 1) {
            throw new IllegalStateException("添加失败");
        }
        log.info("用户 [{}] 添加外送检验样本项成功 [{}]", user.getNickname(), JSON.toJSONString(sample));
        return sample.getOutsourcingSampleId();

    }

    @Override
    public boolean deleteByOutsourcingSampleId(long outsourcingSampleId) {
        if (outsourcingSampleMapper.deleteById(outsourcingSampleId) < 1) {
            return false;
        }
        log.info("用户 [{}] 删除外送检验样本 [{}] 成功", LoginUserHandler.get().getNickname(), outsourcingSampleId);
        return true;
    }

    @Override
    public void deleteByOutsourcingSampleIds(Collection<Long> outsourcingSampleIds) {

        if (CollectionUtils.isEmpty(outsourcingSampleIds)) {
            return;
        }

        outsourcingSampleMapper.deleteBatchIds(outsourcingSampleIds);

        log.info("用户 [{}] 删除外送检验样本 [{}] 成功", LoginUserHandler.get().getNickname(), outsourcingSampleIds);
    }

    @Override
    public List<OutsourcingSampleDto> selectByExportOrgId(long exportOrgId) {

        final LambdaQueryWrapper<TbOutsourcingSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbOutsourcingSample::getExportOrgId, exportOrgId);

        return outsourcingSampleMapper.selectList(wrapper).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public void updateByOutsourcingSampleIds(OutsourcingSampleDto sampleDto, Collection<Long> outsourcingSampleIds) {
        if (CollectionUtils.isEmpty(outsourcingSampleIds)) {
            return;
        }
        outsourcingSampleMapper.updateByOutsourcingSampleIds(sampleDto, outsourcingSampleIds);

    }

    @Override
    public void updateByApplySampleIds(OutsourcingSampleDto sampleDto, Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return;
        }
        outsourcingSampleMapper.updateByApplySampleIds(sampleDto, applySampleIds);
    }

    @Override
    @SneakyThrows
    public SampleReportDto rebuildReport(long applySampleId) {
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            log.info("申请单样本不存在 [{}]", applySampleId);
            return null;
        }
        if (BooleanUtils.isFalse(Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode()))) {
            log.info("申请单样本状态不是审核状态 [{}]", applySampleId);
            return null;
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            log.info("申请单不存在 [{}]", applySample.getApplyId());
            return null;
        }

        final OutsourcingSampleDto sample = selectByApplySampleId(applySampleId);
        if (Objects.isNull(sample)) {
            log.info("样本不存在 [{}]", applySampleId);
            return null;
        }

        final List<SampleReportItemDto> sampleReportItems =
                sampleReportItemService.selectBySampleId(sample.getOutsourcingSampleId());
        final List<SampleResultDto> sampleResults =
                sampleResultService.selectBySampleId(sample.getOutsourcingSampleId());

        // 获取 对应仪器报告项目
        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentGroupId(sample.getInstrumentGroupId());

        // 检验项目
        final List<ApplySampleItemDto> items = applySampleItemService.selectByApplySampleId(sample.getApplySampleId());

        final SampleReportDto sampleReport = buildReportCommand.buildPDF(sample, apply, applySample, sampleReportItems,
                sampleResults, instrumentReportItems, items);

        log.info("重新生成报告: [{}]", sampleReport.getUrl());
        return sampleReport;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SampleImageDto> addImages(Long applyId, Long sampleId, List<String> imageUrls) {
        log.info("添加图片，【申请单ID：{}，样本ID：{}，图片ID：{}】", applyId, sampleId, JSON.toJSONString(imageUrls));
        ApplyDto applyDto = applyService.selectByApplyId(applyId);
        if (Objects.isNull(applyDto)) {
            throw new IllegalStateException("申请单不存在");
        }

        final OutsourcingSampleDto sampleDto = selectByOutsourcingSampleId(sampleId);
        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("样本不存在");
        }

        ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("申请单样本不存在");
        }
        Integer status = applySampleDto.getStatus();
        if (Objects.equals(SampleStatusEnum.ONE_AUDIT.getCode(), status) ||
                Objects.equals(SampleStatusEnum.AUDIT.getCode(), status)) {
            throw new IllegalStateException("样本已经审核，不能删除图片");
        }

        Date createDate = new Date();
        Long userId = LoginUserHandler.get().getUserId();
        String nickname = LoginUserHandler.get().getNickname();

        List<SampleImageDto> sampleImageDtos = imageUrls.stream().map(imageUrl -> {
            SampleImageDto entity = new SampleImageDto();
            entity.setSampleImageId(snowflakeService.genId());
            entity.setApplyId(applyId);
            entity.setSampleId(sampleId);
            entity.setApplySampleId(sampleDto.getApplySampleId());
            entity.setImageUrl(imageUrl);
            entity.setItemType(ItemTypeEnum.OUTSOURCING.name());
            entity.setImageName("");
            entity.setCreateDate(createDate);
            entity.setUpdateDate(createDate);
            entity.setCreatorId(userId);
            entity.setCreatorName(nickname);
            entity.setUpdaterId(userId);
            entity.setUpdaterName(nickname);
            entity.setIsDelete(YesOrNoEnum.NO.getCode());
            return entity;
        }).collect(Collectors.toList());
        sampleImageService.addSampleImage(sampleImageDtos);

        SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(applyId);
        sampleFlow.setApplySampleId(sampleDto.getApplySampleId());
        sampleFlow.setBarcode(sampleDto.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.RESULT_UPDATE.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.RESULT_UPDATE.getDesc());
        sampleFlow.setOperator(nickname);
        sampleFlow.setOperatorId(userId);
        sampleFlow.setContent(String.format("添加图片: [%s]", String.join(StringPool.COMMA, imageUrls)));

        // 添加条码环节
        sampleFlowService.addSampleFlow(sampleFlow);

        return sampleImageDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteImage(Long sampleImageId) {
        log.info("删除图片，【图片ID：{}】", sampleImageId);

        SampleImageDto sampleImageDto = sampleImageService.selectSampleImageById(sampleImageId);
        if (Objects.isNull(sampleImageDto)) {
            throw new IllegalStateException("样本图片不存在");
        }

        ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleImageDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        Integer status = applySampleDto.getStatus();
        if (Objects.equals(SampleStatusEnum.ONE_AUDIT.getCode(), status) ||
                Objects.equals(SampleStatusEnum.AUDIT.getCode(), status)) {
            throw new IllegalStateException("样本已经审核，不能删除图片");
        }

        if (sampleImageService.deleteSampleImage(sampleImageId)) {
            return false;
        }

        log.info("用户 [{}] 删除图片 [{}] 成功", LoginUserHandler.get().getNickname(), sampleImageId);

        SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(sampleImageDto.getApplyId());
        sampleFlow.setApplySampleId(sampleImageDto.getApplySampleId());
        sampleFlow.setBarcode(applySampleDto.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.RESULT_UPDATE.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.RESULT_UPDATE.getDesc());
        sampleFlow.setOperator(LoginUserHandler.get().getNickname());
        sampleFlow.setOperatorId(LoginUserHandler.get().getUserId());
        sampleFlow.setContent(String.format("删除图片: [%s]", sampleImageDto.getImageUrl()));

        // 添加条码环节
        sampleFlowService.addSampleFlow(sampleFlow);

        return true;
    }

    @Override
    public List<BetterOutsourcingSampleDto> selectByCondition(OutsourcingSampleCondition condition) {
        return outsourcingSampleMapper.selectByCondition(condition);
    }

    @Override
    public void updateByApplyId(OutsourcingSampleDto outsourcingSampleDto) {
        LambdaUpdateWrapper<TbOutsourcingSample> wrapper = Wrappers.lambdaUpdate(TbOutsourcingSample.class)
                .eq(TbOutsourcingSample::getApplyId, outsourcingSampleDto.getApplyId())
                .eq(TbOutsourcingSample::getIsDelete,0)
                .set(TbOutsourcingSample::getHspOrgId, outsourcingSampleDto.getHspOrgId())
                .set(TbOutsourcingSample::getHspOrgName,outsourcingSampleDto.getHspOrgName())
                .set(TbOutsourcingSample::getUpdaterId,outsourcingSampleDto.getUpdaterId())
                .set(TbOutsourcingSample::getUpdaterName,outsourcingSampleDto.getUpdaterName())
                .set(TbOutsourcingSample::getUpdateDate,outsourcingSampleDto.getUpdateDate());
        outsourcingSampleMapper.update(null, wrapper);
    }

    @Override
    public void updateByApplyIds(OutsourcingSampleDto outsourcingSampleDto, Collection<Long> applyIds) {

        for (List<Long> item : Lists.partition(new ArrayList<>(applyIds), 500)) {
            LambdaUpdateWrapper<TbOutsourcingSample> wrapper = Wrappers.lambdaUpdate(TbOutsourcingSample.class)
                .in(TbOutsourcingSample::getApplyId, item).eq(TbOutsourcingSample::getIsDelete, 0)
                .set(TbOutsourcingSample::getHspOrgId, outsourcingSampleDto.getHspOrgId())
                .set(TbOutsourcingSample::getHspOrgName, outsourcingSampleDto.getHspOrgName())
                .set(TbOutsourcingSample::getUpdaterId, outsourcingSampleDto.getUpdaterId())
                .set(TbOutsourcingSample::getUpdaterName, outsourcingSampleDto.getUpdaterName())
                .set(TbOutsourcingSample::getUpdateDate, outsourcingSampleDto.getUpdateDate());
            outsourcingSampleMapper.update(null, wrapper);
        }
    }

    @Override
    public ApplyUpdateBeforeCheckTipDto updateCheckExceptionAndCritical(List<ApplySampleDto> applySampleDtos, ApplyDto applyDto) {
        Long applyId = applyDto.getApplyId();

        List<ApplyUpdateBeforeCheckTipItemDto> criticalTips = new ArrayList<>();
        List<ApplyUpdateBeforeCheckTipItemDto> exceptionTips = new ArrayList<>();
        ApplyUpdateBeforeCheckTipDto checkTipDto = new ApplyUpdateBeforeCheckTipDto();
        checkTipDto.setCriticals(criticalTips);
        checkTipDto.setExceptions(exceptionTips);

        if (CollectionUtils.isEmpty(applySampleDtos)) {
            return checkTipDto;
        }

        Map<Long, ApplySampleDto> applySampleDtoMap = applySampleDtos.stream()
                .collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity(), (a, b) -> b));

        List<OutsourcingSampleDto> sampleDtos = outsourcingSampleService.selectByApplyId(applyId);
        sampleDtos.forEach(sampleDto -> {
            ApplySampleDto applySampleDto = applySampleDtoMap.get(sampleDto.getApplySampleId());
            // 样本报告项目
            List<SampleReportItemDto> sampleReportItemDtos = sampleReportItemService.selectBySampleId(sampleDto.getOutsourcingSampleId());

            // 仪器报告项目
            final List<InstrumentReportItemDto> reportItems = instrumentReportItemService.selectByInstrumentGroupId(sampleDto.getInstrumentGroupId());
            // 样本结果 - 只查询数字类型的结果
            List<SampleResultDto> sampleResultDtos = sampleResultService.selectBySampleId(sampleDto.getOutsourcingSampleId());

            for (SampleResultDto sampleResult : sampleResultDtos) {
                String reportItemCode = sampleResult.getReportItemCode();

                InstrumentReportItemDto instrumentReportItem;

                // 取样本仪器报告项目，取不到为空
                instrumentReportItem = reportItems.stream().filter(e -> Objects.equals(e.getInstrumentId(), sampleDto.getInstrumentId())
                                && Objects.equals(e.getReportItemCode(), reportItemCode)).findFirst().orElse(null);
                // 在取专业小组报告项目
                if (Objects.isNull(instrumentReportItem)) {
                    instrumentReportItem = reportItems.stream().filter(e -> Objects.equals(e.getReportItemCode(), reportItemCode)).findFirst().orElse(null);
                }
                if (Objects.isNull(instrumentReportItem)) {
                    throw new IllegalStateException("仪器报告项目不存在");
                }

                // 非数值结果跳过处理
                if (!instrumentReportItem.getResultTypeCode().equals(TestResultTypeEnum.NUMBER.getCode()) || !NumberUtils.isParsable(sampleResult.getResult())) {
                    continue;
                }

                final List<InstrumentReportItemReferenceDto> refs = instrumentReportItemReferenceService.selectByInstrumentReportItemId(instrumentReportItem.getInstrumentReportItemId());
                if (CollectionUtils.isEmpty(refs)) {
                    continue;
                }
                final List<InstrumentReportItemReferenceDto> sampleRefs = refs.stream()
                        .filter(e -> Objects.equals(e.getInstrumentId(), sampleDto.getInstrumentId()))
                        .collect(Collectors.toList());
                InstrumentReportItemReferenceDto instrumentReportItemReferenceDto;
                instrumentReportItemReferenceDto =
                        instrumentReportReferenceCommand.filterCustomerReportReference(applyDto, applySampleDto, sampleRefs);
                // 先取样本仪器上的参考范围
                if (Objects.isNull(instrumentReportItemReferenceDto)) {
                    instrumentReportItemReferenceDto =
                            instrumentReportReferenceCommand.filterCustomerReportReference(applyDto, applySampleDto, refs);
                }

                if (Objects.nonNull(instrumentReportItemReferenceDto)) {
                    BigDecimal result = BigDecimal.valueOf(Double.parseDouble(sampleResult.getResult()));
                    boolean isCritical = numberResultCommand.checkIsCritical(result, instrumentReportItem, instrumentReportItemReferenceDto);
                    boolean isException = numberResultCommand.checkIsException(result, instrumentReportItem, instrumentReportItemReferenceDto);

                    SampleFlowDto sampleFlowDto;
                    if (isCritical || isException) {
                        List<SampleFlowDto> sampleFlowDtos = sampleFlowService.selectByBarcode(applySampleDto.getBarcode());
                        sampleFlowDto = sampleFlowDtos.get(sampleFlowDtos.size() - 1);

                        ApplyUpdateBeforeCheckTipItemDto tipDto = new ApplyUpdateBeforeCheckTipItemDto(
                                applySampleDto.getBarcode(), applySampleDto.getGroupName(), sampleFlowDto.getOperateName(),
                                sampleResult.getReportItemName(), sampleResult.getResult(),
                                instrumentReportItemReferenceDto.getCnRefereValue());
                        if (isCritical) {
                            criticalTips.add(tipDto);
                        } else {
                            exceptionTips.add(tipDto);
                        }
                    }
                }
            }
        });

        return checkTipDto;
    }

    @Override
    public void cancelOutSendApplyToBusiness(List<OutsourcingSampleDto> outsourcingSampleDtos) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        threadPoolConfig.getPool().execute(() -> {
            try {
                if (CollectionUtils.isEmpty(outsourcingSampleDtos)) {
                    return;
                }

                for (OutsourcingSampleDto outsourcingSampleDto : outsourcingSampleDtos) {
                    final String formCode = outsourcingSampleDto.getFormCode();
                    final String barcode = outsourcingSampleDto.getBarcode();
                    log.info("取消外送分拣 取消外送申请到业务中台 formCode [{}] barcode [{}] hspOrgCode [{}] 操作人 [{}]", formCode, barcode, orgCode, user.getNickname());

                    if (StringUtils.isBlank(formCode)) {
                        continue;
                    }

                    CancelApplyFormSampleRequest cancelRequest = new CancelApplyFormSampleRequest();
                    cancelRequest.setFormCode(formCode);
                    cancelRequest.setBarcode(barcode);
                    cancelRequest.setHspOrgCode(orgCode);
                    cancelRequest.setOptId(user.getUsername());
                    cancelRequest.setOptName(user.getNickname());
                    Response<?> response = tbOrgApplySampleMainService.cancelApplyFormSample(cancelRequest);
                    log.info("取消外送分拣 取消外送申请到业务中台 结果 [{}]", JSON.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("取消外送分拣 取消外送申请到业务中台 条码号 [{}] ",
                        outsourcingSampleDtos.stream().map(OutsourcingSampleDto::getBarcode).collect(Collectors.joining(",")), e);
            }
        });
    }

    private void checkAuditorInfo(OutsourcingSampleCancelAuditDto dto) {
        String auditorPwd = dto.getAuditPwd();
        Long auditorId = dto.getAuditId();

        if (Objects.isNull(auditorId)) {
            throw new IllegalStateException("当前审核人为空,请重新选择");
        }
        if (Objects.isNull(auditorPwd)) {
            throw new IllegalStateException("当前审核人密码为空");
        }

        final UserDto userDto = userService.selectByUserId(dto.getAuditId());
        if (Objects.isNull(userDto)) {
            throw new IllegalStateException("审核人账号不存在，请联系管理员");
        }

        if (StringUtils.isBlank(auditorPwd)) {
            throw new IllegalStateException("请输入审核人密码");
        }

        if (!userService.validPassword(userDto.getUsername(), userDto.getPassword(), dto.getAuditPwd())) {
            throw new IllegalStateException("输入的审核人密码错误，请重新输入");
        }

    }

    private OutsourcingSampleDto convert(TbOutsourcingSample outsourcingSample) {
        if (Objects.isNull(outsourcingSample)) {
            return null;
        }

        return JSON.parseObject(JSON.toJSONString(outsourcingSample), OutsourcingSampleDto.class);
    }

    @Override
    public Object selectOutSamplesLims(QueryOutsourcingSampleResultDto querySampleResult) {
        final OutsourcingSampleCondition condition = new OutsourcingSampleCondition();
        BeanUtils.copyProperties(querySampleResult, condition);

        final List<BetterOutsourcingSampleDto> samples = outsourcingSampleService.selectByCondition(condition);
        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }

        // 申请单信息
        final Map<Long, Long> sampleId2ApplySampleIdMap = samples.stream().collect(Collectors.toMap(
                OutsourcingSampleDto::getOutsourcingSampleId, OutsourcingSampleDto::getApplySampleId, (a, b) -> a));
        final Map<Long, ApplySampleDto> applySampleDtoMap = applySampleService.selectByApplySampleIdsAsMap(sampleId2ApplySampleIdMap.values());

        // 申请单样本信息
        final Map<Long, Long> sampleId2ApplyIdMap = samples.stream().collect(
                Collectors.toMap(OutsourcingSampleDto::getOutsourcingSampleId, OutsourcingSampleDto::getApplyId, (a, b) -> a));
        final Map<Long, ApplyDto> applyDtoMap = applyService.selectByApplyIdsAsMap(sampleId2ApplyIdMap.values());

        /*final Set<Long> sampleIds = samples.stream().map(OutsourcingSampleDto::getOutsourcingSampleId).collect(Collectors.toSet());
        // 外送检验样本结果
        final Map<Long, List<SampleResultDto>> sampleResultMapBySampleId = sampleResultService.selectBySamplesIdAsMap(sampleIds);
        // 外送检验样本报告项目
        final List<SampleReportItemDto> sampleReportItemDtos = sampleReportItemService.selectBySampleIds(sampleIds);*/

        final Map<Long, Long> instrumentGroupIdMapBySampleId = samples.stream().collect(Collectors.toMap(
                OutsourcingSampleDto::getOutsourcingSampleId, OutsourcingSampleDto::getInstrumentGroupId, (a, b) -> a));
        final Collection<Long> instrumentGroupIds = new HashSet<>(instrumentGroupIdMapBySampleId.values());

        // 这个专业小组下所有报告项目
        final Map<String, List<InstrumentReportItemDto>> instrumentReportItems = Lists.partition(new ArrayList<>(instrumentGroupIds), 100)
                .stream().map(ids -> instrumentReportItemService.selectByInstrumentGroupIds(ids))
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(e -> e.getInstrumentGroupId() + e.getReportItemCode()));

        // 仪器报告项目参考范围
        final Map<String, List<InstrumentReportItemReferenceDto>> refsMap = Lists.partition(new ArrayList<>(instrumentGroupIds), 100)
                .stream().map(ids -> instrumentReportItemReferenceService.selectByInstrumentGroupIds(ids))
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(e -> e.getInstrumentGroupId() + e.getReportItemCode()));

        // 查询外送机构信息
        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(querySampleResult.getExportOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalArgumentException("外送机构不存在");
        }

        final QueryLimsOutSourceSampleInfoRequest req = new QueryLimsOutSourceSampleInfoRequest();
        req.setOrgCode(limsOrgCodeConfig.getMappedOrgCode(hspOrganization.getHspOrgCode()));
        req.setBarCodes(samples.stream().map(OutsourcingSampleDto::getBarcode).distinct().collect(Collectors.toList()));
        req.setHspOrgCode(orgCode);
        log.info("调用外部结果，查询外送样本结果信息开始，查询入参：{}", JSONObject.toJSONString(req));
        List<OrgApplyResultSamplesDTO> data = limsOutSourceSampleService.queryResultSample(req).getData();
        log.info("调用外部结果，查询外送样本结果信息结束，返回信息：{}", JSONObject.toJSONString(data));

        final List<OrgApplyResultSamplesDTO> outSamples = ObjectUtils.defaultIfNull(data, List.of());

        // 移除忽略报告项目 如果配置了需要忽略的报告项目 则需要移除掉
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.IGNORE_OUTSOURCING_REPORT_ITEM.getCode(), LoginUserHandler.get().getOrgId());
        if (param != null && StringUtils.isNotBlank(param.getParamValue())) {
            doIgnoreItemOld(outSamples, param);
        }

        final Map<String, OrgApplyResultSamplesDTO> outSampleMapByBarcode = outSamples.stream()
                .collect(Collectors.toMap(OrgApplyResultSamplesDTO::getBarcode, v -> v, (a, b) -> a));

        // 取并集，删除不存在的
        samples.removeIf(e -> !outSampleMapByBarcode.containsKey(e.getBarcode()));
        Map<Long, HspOrganizationDto> organizationDtoMap = hspOrganizationService.selectByHspOrgIdsAsMap(
                samples.stream().map(BetterOutsourcingSampleDto::getHspOrgId).collect(Collectors.toSet()));

        final List<QueryOutsourcingSampleResultResponseVo> list = new ArrayList<>();
        for (BetterOutsourcingSampleDto e : samples) {
            final QueryOutsourcingSampleResultResponseVo v = new QueryOutsourcingSampleResultResponseVo();
            v.setResults(new LinkedList<>());
            v.setStatus(e.getStatus());
            v.setBarcode(e.getBarcode());
            v.setSampleNo(e.getSampleNo());
            v.setOutsourcingSampleId(e.getOutsourcingSampleId());
            v.setTestDate(e.getTestDate());
            final OrgApplyResultSamplesDTO k = outSampleMapByBarcode.get(e.getBarcode());
            if (Objects.nonNull(k)) {
                Optional.ofNullable(k.getTestDate()).ifPresent(v::setTestDate);
                final List<TbOrgApplySampleMainIteResultDTO> rs = ObjectUtils.defaultIfNull(k.getTbOrgApplySampleMainIteResultDTOS(), List.of());

                for (TbOrgApplySampleMainIteResultDTO r : rs) {
                    log.info("条码号 [{}] 的结果值信息  [{}]", e.getBarcode(), JSONObject.toJSONString(r));
                    final QueryOutsourcingSampleResultResponseVo.Result t = new QueryOutsourcingSampleResultResponseVo.Result();
                    t.setResult(r.getTestResult());
                    t.setId(IdUtil.simpleUUID());
                    t.setTestItemCode(r.getItemTestCode());
                    t.setTestItemName(r.getItemTestName());
                    t.setUnit(r.getResultUnit());
                    t.setRange(r.getReferenceValue());
                    if (Objects.equals(r.getIsCritical(), YesOrNoEnum.YES.getCode())) {
                        t.setStatus(ResultStatusEnum.CRISIS.getCode());
                    } else if (Objects.equals(r.getIsException(), YesOrNoEnum.YES.getCode())) {
                        t.setStatus(ResultStatusEnum.EXCEPTION.getCode());
                    } else {
                        t.setStatus(ResultStatusEnum.NORMAL.getCode());
                    }
                    t.setReportItemCode(r.getCompareItemReportCode());
                    t.setReportItemName(r.getCompareItemReportName());
                    t.setJudge(r.getTestJudge());
                    // t.setTestItemMappingError(CollectionUtils.isEmpty(r.getCompareTestItem()));
                    t.setReportItemMappingError(StringUtils.isBlank(r.getCompareItemReportCode()));

                    if (t.getReportItemMappingError()) {
                        t.setReportItemMappingErrorText(String.format("【%s】未设置报告项目对照", r.getItemReportCode() + "|" + r.getItemReportName()));
                    }

                    if (!t.getReportItemMappingError()) {
                        // 单位比对
                        final List<InstrumentReportItemDto> items = instrumentReportItems.getOrDefault((e.getInstrumentGroupId() + t.getReportItemCode()), List.of());
                        // 优先取当前仪器的 否则专业小组下取第一个
                        final InstrumentReportItemDto instrumentReportItem = items.stream().filter(f -> Objects.equals(f.getInstrumentId(), e.getInstrumentId()))
                                .findFirst().orElse(items.stream().findFirst().orElse(new InstrumentReportItemDto()));
                        final String localUnit = StringUtils.defaultIfBlank(instrumentReportItem.getReportItemUnitName(), StringPool.EMPTY);
                        final String remoteUnit = StringUtils.defaultIfBlank(t.getUnit(), StringPool.EMPTY);
                        t.setUnitDiffError(!Objects.equals(localUnit, remoteUnit));
                        if (t.getUnitDiffError()) {
                            t.setUnitDiffErrorText(String.format("报告项目【%s|%s】单位不一致 本地【%s】 外部【%s】", t.getReportItemCode(), t.getReportItemName(), localUnit, remoteUnit));
                        }

                        // TODO 参考范围比对
                        final List<InstrumentReportItemReferenceDto> references = refsMap.getOrDefault((e.getInstrumentGroupId() + t.getReportItemCode()), List.of());
                        // 优先取当前样本仪器下的，否则取当前专业小组下的默认第一个
                        final List<InstrumentReportItemReferenceDto> finalReferences = references.stream().filter(ref -> Objects.equals(ref.getInstrumentReportItemId(), instrumentReportItem.getInstrumentReportItemId())).collect(Collectors.toList());
                        // 获取参考范围
                        final ApplyDto applyDto = applyDtoMap.get(sampleId2ApplyIdMap.getOrDefault(e.getOutsourcingSampleId(), NumberUtils.LONG_ZERO));
                        final ApplySampleDto applySampleDto = applySampleDtoMap.get(sampleId2ApplySampleIdMap.getOrDefault(e.getOutsourcingSampleId(), NumberUtils.LONG_ZERO));
                        final InstrumentReportItemReferenceDto ref = instrumentReportReferenceCommand.filterCustomerReportReference(applyDto, applySampleDto, finalReferences);
                        if (Objects.nonNull(ref)) {
                            final String localRange = ref.getRange();
                            final String remoteRange = t.getRange();
                            t.setRangeDiffError(!Objects.equals(localRange, remoteRange));
                            if (t.getRangeDiffError()) {
                                t.setRangeDiffErrorText(String.format("报告项目【%s|%s】参考范围不一致 本地【%s】 外部【%s】", t.getReportItemCode(), t.getReportItemName(), localRange, remoteRange));
                            }
                        }
                    }

                    // 填充外部项目，报告项目
                    t.setDianReportItemCode(r.getItemReportCode());
                    t.setDianReportItemName(r.getItemReportName());
                    t.setDianTestItemCode(r.getItemTestCode());
                    t.setDianTestItemName(r.getItemTestName());

                    v.getResults().add(t);
                }
            }

            if ((envDetector.isDev() || envDetector.isTest()) && CollectionUtils.isEmpty(v.getResults())) {
                for (int i = 0; i < RandomUtils.nextInt(3, 10); i++) {
                    final var t = new QueryOutsourcingSampleResultResponseVo.Result();
                    t.setResult(String.format("%.2f", RandomUtils.nextDouble(10, 100)));
                    t.setId(IdUtil.simpleUUID());
                    t.setTestItemCode(RandomStringUtils.randomAlphabetic(5));
                    t.setTestItemName(RandomStringUtils.randomAlphabetic(5));
                    t.setUnit(RandomStringUtils.randomAlphabetic(5));
                    t.setRange(RandomStringUtils.randomAlphabetic(5));
                    t.setReportItemCode(RandomStringUtils.randomAlphabetic(5));
                    t.setReportItemName(RandomStringUtils.randomAlphabetic(5));
                    t.setJudge(RandomStringUtils.randomAlphabetic(5));
                    t.setStatus(RandomUtils.nextInt(0, 3));
                    t.setTestItemMappingError(RandomUtils.nextBoolean());
                    t.setReportItemMappingError(RandomUtils.nextBoolean());
                    v.getResults().add(t);
                }
            }

            v.setPatientName(e.getPatientName());
            if (organizationDtoMap.containsKey(e.getHspOrgId())) {
                v.setHspOrgCode(organizationDtoMap.get(e.getHspOrgId()).getHspOrgCode());
            } else {
                log.warn("没有查到送检机构信息【机构ID：{}，机构名称：{}】", e.getHspOrgId(), e.getHspOrgName());
            }
            v.setHspOrgName(e.getHspOrgName());
            v.setExportOrgName(e.getExportOrgName());
            // 如果包含一个错误 那么就是没有对照
            v.setItemMapping(v.getResults().stream().allMatch(t -> BooleanUtils.isFalse(t.getReportItemMappingError()))
                    && v.getResults().stream().allMatch(t -> BooleanUtils.isFalse(t.getReportItemMappingError())));
            v.setSendDate(e.getCreateDate());

            list.add(v);
        }

        return list;
    }

    @Override
    public Object selectOutSamplesOld(QueryOutsourcingSampleResultDto querySampleResult) {
        final OutsourcingSampleCondition condition = new OutsourcingSampleCondition();
        BeanUtils.copyProperties(querySampleResult, condition);

        final List<BetterOutsourcingSampleDto> samples = outsourcingSampleService.selectByCondition(condition);
        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(querySampleResult.getExportOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalArgumentException("外送机构不存在");
        }

        final QueryLimsOutSourceSampleInfoRequest req = new QueryLimsOutSourceSampleInfoRequest();
        req.setOrgCode(hspOrganization.getHspOrgCode());
        req.setBarCodes(samples.stream().map(OutsourcingSampleDto::getBarcode)
                .distinct().collect(Collectors.toList()));
        req.setHspOrgCode(orgCode);

        // TODO: 2024/2/1 修改调用外部结果的来源
        log.info("调用外部结果，查询外送样本结果信息开始，查询入参：{}", JSONObject.toJSONString(req));
        List<OrgApplyResultSamplesDTO> data = limsOutSourceSampleService.queryResultSample(req).getData();
        log.info("调用外部结果，查询外送样本结果信息结束，返回信息：{}", JSONObject.toJSONString(data));

        final List<OrgApplyResultSamplesDTO> outSamples = ObjectUtils.defaultIfNull(data, List.of());

        // 移除忽略报告项目 如果配置了需要忽略的报告项目 则需要移除掉
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.IGNORE_OUTSOURCING_REPORT_ITEM.getCode(), LoginUserHandler.get().getOrgId());
        if (param != null && StringUtils.isNotBlank(param.getParamValue())) {
            doIgnoreItemOld(outSamples, param);
        }

        final Map<String, OrgApplyResultSamplesDTO> outSampleMap = outSamples.stream()
                .collect(Collectors.toMap(OrgApplyResultSamplesDTO::getBarcode, v -> v, (a, b) -> a));

        // 取并集，删除不存在的
        samples.removeIf(e -> !outSampleMap.containsKey(e.getBarcode()));
        Map<Long, HspOrganizationDto> organizationDtoMap = hspOrganizationService.selectByHspOrgIdsAsMap(
                samples.stream().map(BetterOutsourcingSampleDto::getHspOrgId).collect(Collectors.toSet()));

        final List<QueryOutsourcingSampleResultResponseVo> list = new ArrayList<>();
        for (BetterOutsourcingSampleDto e : samples) {
            final QueryOutsourcingSampleResultResponseVo v = new QueryOutsourcingSampleResultResponseVo();
            v.setResults(new LinkedList<>());
            v.setStatus(e.getStatus());
            v.setBarcode(e.getBarcode());
            v.setSampleNo(e.getSampleNo());
            v.setOutsourcingSampleId(e.getOutsourcingSampleId());
            v.setTestDate(e.getTestDate());
            final OrgApplyResultSamplesDTO k = outSampleMap.get(e.getBarcode());
            if (Objects.nonNull(k)) {
                Optional.ofNullable(k.getTestDate()).ifPresent(v::setTestDate);
                final List<TbOrgApplySampleMainIteResultDTO> rs = ObjectUtils.defaultIfNull(k.getTbOrgApplySampleMainIteResultDTOS(),
                        List.of());

                for (TbOrgApplySampleMainIteResultDTO r : rs) {
                    log.info("样本号【{}】的结果值信息:{}",e.getBarcode(),JSONObject.toJSONString(r));
                    final QueryOutsourcingSampleResultResponseVo.Result t = new QueryOutsourcingSampleResultResponseVo.Result();
                    t.setResult(r.getTestResult());
                    t.setId(IdUtil.simpleUUID());
                    t.setTestItemCode(r.getItemTestCode());
                    t.setTestItemName(r.getItemTestName());
                    t.setUnit(r.getResultUnit());
                    t.setRange(r.getReferenceValue());
                    if (Objects.equals(r.getIsCritical(), YesOrNoEnum.YES.getCode())) {
                        t.setStatus(ResultStatusEnum.CRISIS.getCode());
                    } else if (Objects.equals(r.getIsException(), YesOrNoEnum.YES.getCode())) {
                        t.setStatus(ResultStatusEnum.EXCEPTION.getCode());
                    } else {
                        t.setStatus(ResultStatusEnum.NORMAL.getCode());
                    }
                    t.setReportItemCode(r.getCompareItemReportCode());
                    t.setReportItemName(r.getCompareItemReportName());
                    t.setJudge(r.getTestJudge());
                    t.setTestItemMappingError(CollectionUtils.isEmpty(r.getCompareTestItem()));
                    t.setReportItemMappingError(StringUtils.isBlank(r.getCompareItemReportCode()));

                    if (t.getReportItemMappingError()) {
                        t.setReportItemMappingErrorText(String.format("【%s】未设置报告项目对照",r.getItemReportCode()+"|"+r.getItemReportName()));
                    }

                    if (t.getTestItemMappingError()) {
                        t.setTestItemMappingErrorText(String.format("【%s】未设置申请项目对照",r.getItemTestCode()+"|"+r.getItemTestName()));
                    }

                    // 填充迪安项目
                    t.setDianReportItemCode(r.getItemReportCode());
                    t.setDianReportItemName(r.getItemReportName());
                    t.setDianTestItemCode(r.getItemTestCode());
                    t.setDianTestItemName(r.getItemTestName());

                    v.getResults().add(t);
                }
            }

            if ((envDetector.isDev() || envDetector.isTest()) && CollectionUtils.isEmpty(v.getResults())) {
                for (int i = 0; i < RandomUtils.nextInt(3, 10); i++) {
                    final QueryOutsourcingSampleResultResponseVo.Result t = new QueryOutsourcingSampleResultResponseVo.Result();
                    t.setResult(String.format("%.2f", RandomUtils.nextDouble(10, 100)));
                    t.setId(IdUtil.simpleUUID());
                    t.setTestItemCode(RandomStringUtils.randomAlphabetic(5));
                    t.setTestItemName(RandomStringUtils.randomAlphabetic(5));
                    t.setUnit(RandomStringUtils.randomAlphabetic(5));
                    t.setRange(RandomStringUtils.randomAlphabetic(5));
                    t.setReportItemCode(RandomStringUtils.randomAlphabetic(5));
                    t.setReportItemName(RandomStringUtils.randomAlphabetic(5));
                    t.setJudge(RandomStringUtils.randomAlphabetic(5));
                    t.setStatus(RandomUtils.nextInt(0, 3));
                    t.setTestItemMappingError(RandomUtils.nextBoolean());
                    t.setReportItemMappingError(RandomUtils.nextBoolean());
                    v.getResults().add(t);
                }
            }

            v.setPatientName(e.getPatientName());
            if (organizationDtoMap.containsKey(e.getHspOrgId())) {
                v.setHspOrgCode(organizationDtoMap.get(e.getHspOrgId()).getHspOrgCode());
            } else {
                log.warn("没有查到送检机构信息【机构ID：{}，机构名称：{}】", e.getHspOrgId(), e.getHspOrgName());
            }
            v.setHspOrgName(e.getHspOrgName());
            v.setExportOrgName(e.getExportOrgName());
            // 如果包含一个错误 那么就是没有对照
            v.setItemMapping(v.getResults().stream().allMatch(t -> BooleanUtils.isFalse(t.getReportItemMappingError()))
                    && v.getResults().stream().allMatch(t -> BooleanUtils.isFalse(t.getReportItemMappingError())));
            v.setSendDate(e.getCreateDate());
            list.add(v);
        }

        return list;

    }

    // 移除忽略的报告项目
    private void doIgnoreItemLims(List<SampleTestResultDTO> outSamples, SystemParamDto param) {
        JSONArray objects = JSONObject.parseArray(param.getParamValue());
        List<String> ignoreItemCode = objects.stream().map(
                e -> ((JSONObject) e).getString("reportCode")).collect(Collectors.toList());

        for (SampleTestResultDTO outSample : outSamples) {
            final List<SampleItemTestResultDTO> resultItems = outSample.getItemTestResultList();
            if (CollectionUtils.isNotEmpty(resultItems)) {
                resultItems.removeIf(e -> ignoreItemCode.contains(e.getReportItemId()));
            }
        }
    }

    // 移除忽略的报告项目
    private void doIgnoreItemOld(List<OrgApplyResultSamplesDTO> outSamples, SystemParamDto param) {
        JSONArray objects = JSONObject.parseArray(param.getParamValue());
        List<String> ignoreItemCode = objects.stream().map(e -> ((JSONObject)e).getString("reportCode")).collect(Collectors.toList());

        for (OrgApplyResultSamplesDTO outSample : outSamples) {
            final List<TbOrgApplySampleMainIteResultDTO> resultItems = outSample.getTbOrgApplySampleMainIteResultDTOS();
            if (CollectionUtils.isNotEmpty(resultItems)){
                resultItems.removeIf(e -> ignoreItemCode.contains(e.getItemReportCode()));
            }
        }
    }

}
