package com.labway.lims.outsourcing.service.chain.his.get;

import com.google.common.collect.Sets;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.service.HisServiceImpl;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;


@Component
@Slf4j
public class GetHisSampleCheckParamCommand implements Command {

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Resource
    private Environment environment;

    @DubboReference
    private ApplySampleService applySampleService;

    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    @Override
    public boolean execute(Context context) throws Exception {
        final GetHisSampleContext from = GetHisSampleContext.from(context);

        final HisGetParam hisGetParam = from.getHisGetParam();

        final String outBarcode = hisGetParam.getOutBarcode();
        final Long hspOrgId = hisGetParam.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalStateException("送检机构不能为空");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("送检机构不存在");
        }

        if (Objects.equals(hspOrganization.getEnable(), YesOrNoEnum.NO.getCode())) {
            throw new IllegalStateException(String.format("送检机构 [%s] 已禁用", hspOrganization.getHspOrgName()));
        }

        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(
                SampleEsQuery.builder().hspOrgIds(Sets.newHashSet(hspOrganization.getHspOrgId())).outBarcodes(Sets.newHashSet(outBarcode)).build());
        if (CollectionUtils.isNotEmpty(baseSampleEsModelDtos)) {
            throw new IllegalStateException(String.format("条码 [%s] 已签收", outBarcode));
        }

        final String orgCode = environment.getProperty(HisServiceImpl.BUSINESS_CENTER_ORG_CODE);
        if (StringUtils.isBlank(orgCode)) {
            throw new IllegalArgumentException(String.format("请配置 %s 参数", HisServiceImpl.BUSINESS_CENTER_ORG_CODE));
        }

        from.put(GetHisSampleContext.HSP_ORG, hspOrganization);
        from.put(GetHisSampleContext.ORG_CODE, orgCode);

        return CONTINUE_PROCESSING;
    }
}
