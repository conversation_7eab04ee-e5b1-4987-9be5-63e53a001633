package com.labway.lims.outsourcing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class OutsourcingSampleCancelAuditVo {
    /**
     * 样本 ID 集合
     */
    private Long outsourcingSampleId;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 样本状态
     */
    private Integer status;

    /**
     * 审核状态
     *
     * @see com.labway.lims.api.enums.routine.SampleAuditStatusEnum
     */
    private String auditStatus;

    /**
     * 当前操作(审核或取消审核)
     */
    private Integer operateCode;

    /**
     * auditName
     */
    private String auditName;

    /**
     * auditId
     */
    private Long auditId;

    /**
     * 是否强制提交 , 如果为 true ， 那么如果有危机值也会通过审核
     */
    private Boolean criticalForce;

    /**
     * 是否强制提交 , 如果为 true ， 那么如果有异常值也会通过审核
     */
    private Boolean exceptionForce;

    /**
     * 是否强制提交 , 如果为 true ， 那么如果有负数值也会通过审核
     */
    private Boolean negativeNumberForce;
    /**
     * 强制审核
     */
    private Boolean auditForce;

    /**
     * 密码
     */
    private String auditPwd;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date auditDate;

    /**
     * 取消审核类型
     *
     * @see com.labway.lims.api.enums.routine.SampleAuditStatusEnum
     */
    private String auditType;

}
