package com.labway.lims.outsourcing.service.chain.result;

import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/3/30 16:23
 */
@Slf4j
@Component
public class SavePGSQLResultCommand implements Command {
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleResultService sampleResultService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();

        final SampleReportItemDto sampleReportItem = context.getSampleReportItem();

        final OutsourcingSampleDto sampleDto = context.getSample();
        final InstrumentReportItemReferenceDto reportItemReference = context.getInstrumentReportItemReference();

        final SampleResultDto sr = new SampleResultDto();
        sr.setSampleId(sampleDto.getOutsourcingSampleId());
        sr.setApplyId(sampleDto.getApplyId());
        sr.setTestItemId(sampleReportItem.getTestItemId());
        sr.setTestItemName(StringUtils.defaultString(sampleReportItem.getTestItemName()));
        sr.setTestItemCode(StringUtils.defaultString(sampleReportItem.getTestItemCode()));
        sr.setReportItemId(sampleReportItem.getReportItemId());
        sr.setReportItemCode(StringUtils.defaultString(sampleReportItem.getReportItemCode()));
        sr.setReportItemName(StringUtils.defaultString(sampleReportItem.getReportItemName()));
        sr.setType(StringUtils.defaultString(instrumentReportItem.getResultTypeName()));
        sr.setResult(StringUtils.defaultString(context.getResult()));
        sr.setUnit(StringUtils.defaultString(instrumentReportItem.getReportItemUnitName()));

        sr.setInstrumentReportItemReferenceId(NumberUtils.LONG_ZERO);

        if (Objects.nonNull(reportItemReference)) {
            // 先取中文 -> 英文 -> 中英文
            sr.setRange(
                    StringUtils.defaultString(reportItemReference.getCnRefereValue(), reportItemReference.getEnRefereValue()));
            sr.setRange(
                    StringUtils.defaultIfBlank(sr.getRange(), reportItemReference.getCnEnRefereValue()));
            sr.setInstrumentReportItemReferenceId(reportItemReference.getInstrumentReportItemReferenceId());
        } else {
            sr.setRange(StringUtils.EMPTY);
        }

        if (context.isException()) {
            sr.setStatus(ResultStatusEnum.EXCEPTION.getCode());
        } else if (context.isCritical()) {
            sr.setStatus(ResultStatusEnum.CRISIS.getCode());
        } else {
            sr.setStatus(ResultStatusEnum.NORMAL.getCode());
        }

        sr.setInstrumentId(instrumentReportItem.getInstrumentId());
        sr.setInstrumentName(StringUtils.defaultString(instrumentReportItem.getInstrumentName()));
        sr.setInstrumentId(NumberUtils.LONG_ZERO);
        sr.setInstrumentName(StringUtils.EMPTY);
        sr.setInstrumentResult(StringUtils.EMPTY);
        sr.setJudge(StringUtils.defaultString(context.getResultJudge()));
        sr.setApplySampleId(context.getApplySampleId());
        final long sampleResultId = sampleResultService.addSampleResult(sr);


        // 保存到上下文
        context.getContextResults().put(sr.getReportItemCode(), sr.getResult());
        sr.setSampleResultId(sampleResultId);
        context.put(SaveResultContext.SAMPLE_RESULT, sr);

        return CONTINUE_PROCESSING;
    }
}
