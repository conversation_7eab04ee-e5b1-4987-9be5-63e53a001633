package com.labway.lims.outsourcing.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.MissReportItemNumTips;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.apply.api.service.SampleImageService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.InstrumentGroupInstrumentDto;
import com.labway.lims.base.api.dto.InstrumentReportItemCommonPhraseDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultTipDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.enums.RelationalOperatorEnum;
import com.labway.lims.base.api.service.InstrumentGroupInstrumentService;
import com.labway.lims.base.api.service.InstrumentReportItemCommonPhraseService;
import com.labway.lims.base.api.service.InstrumentReportItemReferenceService;
import com.labway.lims.base.api.service.InstrumentReportItemResultTipService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.outsourcing.api.dto.AddSampleImageVo;
import com.labway.lims.outsourcing.api.dto.DeleteSampleImageVo;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleAuditDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleCancelAuditDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSaveResultDto;
import com.labway.lims.outsourcing.api.dto.QueryOutsourcingSamplesDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleResultService;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.outsourcing.service.chain.result.CriticalCacheTagCommand;
import com.labway.lims.outsourcing.service.chain.result.InstrumentReportReferenceCommand;
import com.labway.lims.outsourcing.service.chain.result.UpdateMissItemCommand;
import com.labway.lims.outsourcing.service.chain.retest.OutsourcingStartRetestUpdateMissItemCommand;
import com.labway.lims.outsourcing.vo.AddSampleReportItemVo;
import com.labway.lims.outsourcing.vo.ExportTimeoutTestItemVo;
import com.labway.lims.outsourcing.vo.HistoryResultVo;
import com.labway.lims.outsourcing.vo.HistoryResultsChartQueryVo;
import com.labway.lims.outsourcing.vo.HistoryResultsChartVo;
import com.labway.lims.outsourcing.vo.MissReportItemVo;
import com.labway.lims.outsourcing.vo.OutsourcingRoutineSampleVo;
import com.labway.lims.outsourcing.vo.OutsourcingRoutineSamplesVo;
import com.labway.lims.outsourcing.vo.OutsourcingSampleAuditVo;
import com.labway.lims.outsourcing.vo.OutsourcingSampleCancelAuditVo;
import com.labway.lims.outsourcing.vo.OutsourcingSampleReportItemDetailVo;
import com.labway.lims.outsourcing.vo.OutsourcingSampleResultVo;
import com.labway.lims.outsourcing.vo.PhraseVo;
import com.labway.lims.outsourcing.vo.QueryOutsourcingSamplesVo;
import com.labway.lims.outsourcing.vo.QueryRoutineSamplesVo;
import com.labway.lims.outsourcing.vo.ResultRecordComparesVo;
import com.labway.lims.outsourcing.vo.SampleMissReportItemVo;
import com.labway.lims.routine.api.dto.ResultStatusDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/20 16:20
 */
@RestController
@RequestMapping("/outsourcing")
public class OutsourcingSampleController extends BaseController {
    @Resource
    private OutsourcingSampleService outsourcingSampleService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SampleResultService sampleResultService;
    @Resource
    private OutsourcingSampleResultService outsourcingSampleResultService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private InstrumentReportItemResultTipService instrumentReportItemResultTipService;
    @DubboReference
    private InstrumentGroupInstrumentService instrumentGroupInstrumentService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private SampleRetestItemService sampleRetestItemService;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private InstrumentReportItemReferenceService instrumentReportItemReferenceService;
    @DubboReference
    private SampleRetestMainService sampleRetestMainService;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;
    @Resource
    private CriticalCacheTagCommand criticalCacheTagCommand;
    @DubboReference
    private InstrumentReportItemCommonPhraseService instrumentReportItemCommonPhraseService;
    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private SampleImageService sampleImageService;

    @DubboReference
    private SampleReportService sampleReportService;

    @Resource
    private UpdateMissItemCommand updateMissItemCommand;

    @Resource
    private OutsourcingStartRetestUpdateMissItemCommand outsourcingStartRetestUpdateMissItemCommand;

    @PostMapping("/samples")
    public Object samples(@RequestBody QueryOutsourcingSamplesVo vo) {

        final QueryOutsourcingSamplesDto dto = JSON.parseObject(JSON.toJSONString(vo), QueryOutsourcingSamplesDto.class);
        final List<OutsourcingSampleDto> samples = outsourcingSampleService.selectByTestDate(dto);
        // 提前获取相关数据
        // 根据applySampleIds查询 申请单样本 转化成map一一对应样本
        final Map<Long, ApplySampleDto> applySampleMap = applySampleService.selectByApplySampleIds(samples.stream().map(OutsourcingSampleDto::getApplySampleId).collect(Collectors.toList())).stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity(), (k1, k2) -> k1));
        // 根据applyIds查询 申请单 转化成map一一对应样本
        final Map<Long, ApplyDto> applyMap = applyService.selectByApplyIdsAsMap(samples.stream().map(OutsourcingSampleDto::getApplyId).collect(Collectors.toList()));
        // 根据applySampleIds查询 检验项目 转化成map对应样本
        final Map<Long, List<ApplySampleItemDto>> applySampleItemMap = applySampleItemService.selectByApplySampleIds(samples.stream().map(OutsourcingSampleDto::getApplySampleId).collect(Collectors.toList())).stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 对比已出的结果和当前样本下的报告项目数量，来显示缺项信息
        final LinkedList<OutsourcingRoutineSampleVo> vos = new LinkedList<>();
        for (OutsourcingSampleDto sample : samples) {
            final OutsourcingRoutineSampleVo sampleVo = JSON.parseObject(JSON.toJSONString(sample), OutsourcingRoutineSampleVo.class);
            sampleVo.setOutsourcingSampleId(sample.getOutsourcingSampleId());
            final ApplyDto apply = applyMap.get(sample.getApplyId());
            if (Objects.nonNull(apply)) {
                sampleVo.setApplyType(apply.getApplyTypeName());
                sampleVo.setPatientName(apply.getPatientName());
                sampleVo.setEnterDate(apply.getCreateDate());
            }
            final ApplySampleDto applySample = applySampleMap.get(sample.getApplySampleId());
            final List<ApplySampleItemDto> sampleItems = applySampleItemMap.get(sample.getApplySampleId());

            if (Objects.nonNull(applySample)) {
                // 终止检验的过滤
                if (Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
                    continue;
                }
                // 查询未审 ,状态不是未审过滤
                if (Objects.equals(vo.getSampleStatus(), SampleStatusEnum.NOT_AUDIT.name()) && (!Objects.equals(applySample.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) && (!Objects.equals(applySample.getStatus(), SampleStatusEnum.RETEST.getCode()))) {
                    continue;
                }
                // 查询一审，不等于一审的过滤
                if (Objects.equals(vo.getSampleStatus(), SampleStatusEnum.ONE_AUDIT.name()) && (!Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
                    continue;
                }
                // 查询已审，不等于已审的过滤
                if (Objects.equals(vo.getSampleStatus(), SampleStatusEnum.AUDIT.name()) && (!Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode()))) {
                    continue;
                }

                sampleVo.setStatus(applySample.getStatus());
                sampleVo.setUrgent(applySample.getUrgent());
                sampleVo.setIsPrint(applySample.getIsPrint());
                final List<String> testItems = sampleItems.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.toList());
                sampleVo.setTestItemName(StringUtils.join(testItems, ","));
                // 免疫二次分拣标记 # 1.1.3.7
                sampleVo.setIsImmunityTwoPick(applySample.getIsImmunityTwoPick());
                // 检验者信息
                sampleVo.setTesterId(applySample.getTesterId());
                sampleVo.setTesterName(applySample.getTesterName());
            }

            // 查redis，样本是否危机
            final HashOperations<String, String, String> operations = stringRedisTemplate.opsForHash();
            final Map<String, String> map = operations.entries(criticalCacheTagCommand.getCriticalKey(sample.getOutsourcingSampleId()));

            if (Objects.equals(map.get(String.valueOf(sample.getOutsourcingSampleId())), String.valueOf(YesOrNoEnum.YES.getCode()))) {
                sampleVo.setIsCritical(YesOrNoEnum.YES.getCode());
            } else {
                sampleVo.setIsCritical(YesOrNoEnum.NO.getCode());
            }
            vos.add(sampleVo);
        }

        return vos;
    }

    @PostMapping("/add")
    public Object addSampleReportItem(@RequestBody AddSampleReportItemVo vo) {

        final List<InstrumentReportItemDto> instrumentReportItemDtos = instrumentReportItemService.selectByInstrumentId(vo.getInstrumentId());
        final OutsourcingSampleDto sampleDto = outsourcingSampleService.selectByOutsourcingSampleId(vo.getOutsourcingSampleId());

        if (Objects.isNull(sampleDto)) {
            throw new IllegalStateException("样本不存在");
        }

        final List<ApplySampleItemDto> sampleItemDtos = applySampleItemService.selectByApplySampleId(sampleDto.getApplySampleId());

        final List<SampleReportItemDto> dtos = JSON.parseArray(JSON.toJSONString(vo.getReportItems()), SampleReportItemDto.class);
        final LinkedList<Long> ids = snowflakeService.genIds(dtos.size());
        for (SampleReportItemDto dto : dtos) {
            dto.setSampleId(vo.getOutsourcingSampleId());
            dto.setApplyId(sampleDto.getApplyId());
            dto.setReportItemId(ids.pop());
            dto.setTestItemId(sampleItemDtos.get(0).getTestItemId());
            dto.setTestItemCode(sampleItemDtos.get(0).getTestItemCode());
            dto.setTestItemName(sampleItemDtos.get(0).getTestItemName());
            dto.setIsRetest(YesOrNoEnum.NO.getCode());
            final InstrumentReportItemDto reportItemDto = instrumentReportItemDtos.stream().filter(e -> Objects.equals(e.getReportItemCode(), dto.getReportItemCode())).findFirst().orElse(null);
            if (Objects.isNull(reportItemDto)) {
                throw new IllegalStateException("报告项目不存在");
            }
            dto.setPrintSort(reportItemDto.getPrintSort());
        }

        sampleReportItemService.addSampleReportItems(dtos);

        return Map.of();
    }

    @PostMapping("/update-result")
    public Object updateResult(@RequestBody OutsourcingSampleResultVo vo) {
        final OutsourcingSampleDto sampleDto = outsourcingSampleService.selectByOutsourcingSampleId(vo.getOutsourcingSampleId());
        if (Objects.isNull(sampleDto)) {
            throw new IllegalArgumentException("样本不存在");
        }

        if (Objects.isNull(vo.getReportItemId())) {
            throw new IllegalArgumentException("ReportItemId 不能为空");
        }

        if (StringUtils.length(vo.getResult()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException("结果长度不能大于50");
        }

        final ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalArgumentException("申请单样本不存在");
        }

        if (Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new IllegalArgumentException("样本已审核,修改失败");
        }

        final OutsourcingSaveResultDto dto = new OutsourcingSaveResultDto();
        dto.setOutsourcingSampleId(vo.getOutsourcingSampleId());
        dto.setApplySampleId(sampleDto.getApplySampleId());
        dto.setApplyId(sampleDto.getApplyId());
        dto.setReportItemId(vo.getReportItemId());
        dto.setReportItemCode(vo.getReportItemCode());
        dto.setResult(vo.getResult());
        dto.setDate(new Date());

        return outsourcingSampleResultService.saveResult(dto, SaveResultSourceEnum.FRONT);

    }

    /**
     * 常用短语
     */
    @PostMapping("/phrases")
    public Object phrases(@RequestParam("outsourcingSampleId") Long outsourcingSampleId) {

        if (Objects.isNull(outsourcingSampleId)) {
            throw new IllegalArgumentException("参数错误");
        }

        final OutsourcingSampleDto sample = outsourcingSampleService.selectByOutsourcingSampleId(outsourcingSampleId);
        if (Objects.isNull(sample)) {
            throw new IllegalArgumentException("样本不存在");
        }

        final List<InstrumentReportItemCommonPhraseDto> reportPhrase = new ArrayList<>();
        final List<InstrumentReportItemCommonPhraseDto> phrases = instrumentReportItemCommonPhraseService
                .selectByInstrumentGroupId(sample.getInstrumentGroupId()).stream()
                .filter(e -> Objects.equals(e.getInstrumentId(), sample.getInstrumentId()))
                .collect(Collectors.toList());

        final List<InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService.selectByInstrumentId(sample.getInstrumentId());

        final List<SampleReportItemDto> sampleReportItems = sampleReportItemService.selectBySampleId(outsourcingSampleId);

        for (SampleReportItemDto item : sampleReportItems) {
            final InstrumentReportItemDto instrumentReportItem = instrumentReportItems.stream()
                    .filter(e -> Objects.equals(e.getReportItemCode(), item.getReportItemCode()))
                    .findFirst().orElse(null);
            final List<InstrumentReportItemCommonPhraseDto> instrumentReportItemCommonPhrases;
            if (Objects.nonNull(instrumentReportItem)) {
                instrumentReportItemCommonPhrases = phrases.stream()
                        .filter(e -> Objects.equals(e.getInstrumentReportItemId(), instrumentReportItem.getInstrumentReportItemId()))
                        .collect(Collectors.toList());
            } else {
                instrumentReportItemCommonPhrases = phrases.stream()
                        .filter(e -> Objects.equals(e.getReportItemCode(), item.getReportItemCode()))
                        .collect(Collectors.toList());
            }
            reportPhrase.addAll(instrumentReportItemCommonPhrases);
        }

        if (CollectionUtils.isEmpty(reportPhrase)) {
            return Map.of();
        }

        // 排序
        reportPhrase.sort(Comparator.comparing(o -> ObjectUtils.defaultIfNull(o.getSort(), Integer.MAX_VALUE)));

        return reportPhrase.stream().map(e -> {
            final PhraseVo v = new PhraseVo();
            BeanUtils.copyProperties(e, v);
            return v;
        }).collect(Collectors.groupingBy(PhraseVo::getReportItemCode));
    }

    @PostMapping("/querySampleReportItemDetails")
    public Object querySampleReportItemDetails(
            @RequestParam("outsourcingSampleId") Long outsourcingSampleId, @RequestParam(required = false) String testItemCodes)
            throws ExecutionException, InterruptedException, TimeoutException {
        if (Objects.isNull(outsourcingSampleId)) {
            return Collections.emptyList();
        }
        final OutsourcingSampleDto sample = outsourcingSampleService.selectByOutsourcingSampleId(outsourcingSampleId);
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("当前样本不存在");
        }

        // 查询报告项目
        final Future<List<SampleReportItemDto>> reportItemsFuture = threadPoolConfig.getPool()
                .submit(() -> sampleReportItemService.selectBySampleId(outsourcingSampleId));

        // 查询结果
        final Future<Map<String, SampleResultDto>> resultsFuture = threadPoolConfig.getPool()
                .submit(() -> sampleResultService.selectBySampleId(outsourcingSampleId).stream()
                .collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a)));

        final List<SampleReportItemDto> sampleReportItems = reportItemsFuture.get(10, TimeUnit.SECONDS);
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            resultsFuture.cancel(true);
            return Collections.emptyList();
        }

        // 结果
        final Map<String, SampleResultDto> results = resultsFuture.get(10, TimeUnit.SECONDS);

        // 参考范围
        final Future<Map<String, List<InstrumentReportItemReferenceDto>>> refs;
        // 申请单
        final Future<ApplyDto> apply = threadPoolConfig.getPool().submit(() -> applyService.selectByApplyId(sample.getApplyId()));
        // 申请单样本
        final Future<ApplySampleDto> applySample = threadPoolConfig.getPool()
                .submit(() -> applySampleService.selectByApplySampleId(sample.getApplySampleId()));
        // 获取历史结果
        final var lastResults = threadPoolConfig.getPool()
                .submit(() -> getLastResults(apply, applySample));

        // 当结果和报告项目不一致时，那么需要实时算参考范围
        if (results.size() != sampleReportItems.size()) {
            // 这个专业小组下所有的参考范围
            refs = threadPoolConfig.getPool().submit(() -> instrumentReportItemReferenceService
                    .selectByInstrumentGroupId(sample.getInstrumentGroupId()).stream()
                    .collect(Collectors.groupingBy(InstrumentReportItemReferenceDto::getReportItemCode)));
        } else {
            refs = CompletableFuture.completedFuture(Map.of());
        }

        // 复查子表
        final Future<Map<String, List<SampleRetestItemDto>>> sampleRetestItemFuture;
        // 如果有正在复查中的，那么需要查询到复查结果
        if (sampleReportItems.stream().anyMatch(e -> Objects.equals(e.getIsRetest(), RetestStatusEnum.RETESTING.getCode()))) {
            sampleRetestItemFuture = threadPoolConfig.getPool().submit(() -> {
                final SampleRetestMainDto sampleRetestMain = sampleRetestMainService
                        .selectBySampleId(outsourcingSampleId).stream()
                        .filter(e -> Objects.equals(e.getStatus(), SampleRetestStatusEnum.NORMAL.getCode()))
                        .findFirst().orElse(null);
                if (Objects.isNull(sampleRetestMain)) {
                    return Map.of();
                }
                return sampleRetestItemService.selectBySampleRetestMainId(sampleRetestMain.getSampleRetestMainId())
                        .stream().collect(Collectors.groupingBy(SampleRetestItemDto::getReportItemCode));
            });
        } else {
            sampleRetestItemFuture = CompletableFuture.completedFuture(Map.of());
        }

        // 这个专业小组下所有报告项目
        final Map<String, List<InstrumentReportItemDto>> instrumentReportItems = instrumentReportItemService
                .selectByInstrumentGroupId(sample.getInstrumentGroupId())
                .stream().collect(Collectors.groupingBy(InstrumentReportItemDto::getReportItemCode));

        final List<OutsourcingSampleReportItemDetailVo> list = new LinkedList<>();
        List<String> itemCodes = StringUtils.isNotBlank(testItemCodes) ? List.of(testItemCodes.split(",")) : List.of();
        for (SampleReportItemDto item : sampleReportItems) {
            if (StringUtils.isNotBlank(testItemCodes) && !itemCodes.contains(item.getTestItemCode())) {
                continue;
            }
            final OutsourcingSampleReportItemDetailVo vo = new OutsourcingSampleReportItemDetailVo();
            vo.setSampleReportItemId(item.getSampleReportItemId());
            vo.setReportItemId(item.getReportItemId());
            vo.setReportItemCode(item.getReportItemCode());
            vo.setReportItemName(item.getReportItemName());
            vo.setTestItemId(item.getTestItemId());
            vo.setTestItemName(item.getTestItemName());
            vo.setTestItemCode(item.getTestItemCode());
            vo.setIsRetest(item.getIsRetest());
            vo.setPrintSort(ObjectUtils.defaultIfNull(item.getPrintSort(), Integer.MAX_VALUE));

            final List<InstrumentReportItemDto> items = instrumentReportItems.getOrDefault(item.getReportItemCode(), List.of());
            // 优先取当前仪器的 否则专业小组下取第一个
            final InstrumentReportItemDto instrumentReportItem = items.stream()
                    .filter(f -> Objects.equals(f.getInstrumentId(), sample.getInstrumentId()))
                    .findFirst().orElse(items.stream().findFirst().orElse(new InstrumentReportItemDto()));

            vo.setUnit(instrumentReportItem.getReportItemUnitName());
            vo.setEnName(instrumentReportItem.getEnName());
            vo.setEnAb(instrumentReportItem.getEnAb());

            if (results.containsKey(item.getReportItemCode())) {
                final SampleResultDto sampleResult = results.get(item.getReportItemCode());
                vo.setResult(sampleResult.getResult());
                vo.setRange(sampleResult.getRange());
                vo.setJudge(sampleResult.getJudge());
                vo.setStatus(sampleResult.getStatus());
                vo.setUnit(sampleResult.getUnit());

                // 如果是在复查的那么返回给前端的是空结果的,有复查结果显示复查结果
                if (Objects.equals(item.getIsRetest(), RetestStatusEnum.RETESTING.getCode())) {
                    vo.setResult(StringUtils.EMPTY);
                    vo.setStatus(ResultStatusEnum.NORMAL.getCode());
                    vo.setJudge(StringUtils.EMPTY);
                }
            }

            list.add(vo);

        }

        // 补没有结果的参考范围
        for (OutsourcingSampleReportItemDetailVo vo : list) {

            // 历史结果
            final String reportItemCode = vo.getReportItemCode();
            final OutsourcingInspectionDto.OutsourcingReportItem routineReportItem = lastResults.get(10, TimeUnit.SECONDS).get(reportItemCode);
            if (Objects.nonNull(routineReportItem)) {
                final OutsourcingSampleReportItemDetailVo.ResentResult resentResult = new OutsourcingSampleReportItemDetailVo.ResentResult();
                resentResult.setResult(routineReportItem.getResult());
                resentResult.setJudge(routineReportItem.getJudge());
                resentResult.setStatus(routineReportItem.getStatus());
                vo.setResentResult(resentResult);
            }

            if (results.containsKey(reportItemCode)) {
                continue;
            }

            if (Objects.nonNull(apply.get()) && Objects.nonNull(applySample.get())) {
                List<InstrumentReportItemReferenceDto> references = refs.get().get(reportItemCode);
                if (Objects.isNull(references) || CollectionUtils.isEmpty(references)) {
                    continue;
                }

                final List<InstrumentReportItemDto> items = instrumentReportItems.getOrDefault(reportItemCode, List.of());
                // 先找样本仪器的报告项目
                final InstrumentReportItemDto instrumentReportItem = items.stream().filter(f -> Objects.equals(f.getInstrumentId(), sample.getInstrumentId())).findFirst().orElse(items.stream().findFirst().orElse(null));

                if (Objects.isNull(instrumentReportItem)) {
                    continue;
                }
                // 优先取当前样本仪器下的，否则取当前专业小组下的默认第一个
                final List<InstrumentReportItemReferenceDto> finalReferences = references.stream().filter(e -> Objects.equals(e.getInstrumentReportItemId(), instrumentReportItem.getInstrumentReportItemId())).collect(Collectors.toList());

                // 获取参考范围
                final InstrumentReportItemReferenceDto ref = instrumentReportReferenceCommand.filterCustomerReportReference(apply.get(), applySample.get(), finalReferences);
                if (Objects.nonNull(ref)) {
                    // 先取中文 英文 中英文
                    vo.setRange(StringUtils.defaultIfBlank(ref.getCnRefereValue(), ref.getEnRefereValue()));
                    vo.setRange(StringUtils.defaultIfBlank(vo.getRange(), ref.getCnEnRefereValue()));
                }
            }
        }

        // 如果复查中获取复查列表
        for (OutsourcingSampleReportItemDetailVo vo : list) {
            if (Objects.equals(vo.getIsRetest(), RetestStatusEnum.NORMAL.getCode())) {
                continue;
            }

            final List<SampleRetestItemDto> sampleRetestItems = sampleRetestItemFuture.get(10, TimeUnit.SECONDS).get(vo.getReportItemCode());
            if (CollectionUtils.isEmpty(sampleRetestItems)) {
                continue;
            }

            final SampleRetestItemDto sampleRetestItem = sampleRetestItems.stream().max(Comparator.comparing(SampleRetestItemDto::getSampleRetestItemId)).stream().findFirst().orElse(null);
            if (Objects.isNull(sampleRetestItem)) {
                continue;
            }

            vo.setResult(sampleRetestItem.getResult());
            vo.setStatus(sampleRetestItem.getStatus());
            vo.setJudge(sampleRetestItem.getJudge());

        }

        return list.stream().sorted(Comparator.comparing(OutsourcingSampleReportItemDetailVo::getPrintSort)
                .thenComparing(OutsourcingSampleReportItemDetailVo::getReportItemId)).collect(Collectors.toList());

    }

    /**
     * 外送回报给超时时间提醒
     */
    @PostMapping("/export-timeout-tip")
    public Object exportTimeoutTip() {

        final SampleEsQuery query = new SampleEsQuery();
        query.setItemTypes(Collections.singleton(ItemTypeEnum.OUTSOURCING.name()));
        query.setIsAudit(YesOrNoEnum.NO.getCode());
		query.setIsTwoPick(YesOrNoEnum.YES.getCode());
        query.setGroupIds(Sets.newHashSet(LoginUserHandler.get().getGroupId()));

        final List<OutsourcingInspectionDto> samples = elasticSearchSampleService.selectSamples(query)
                .stream().filter(OutsourcingInspectionDto.class::isInstance)
                .map(e -> (OutsourcingInspectionDto) e)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        final Set<Long> testItemIds = new LinkedHashSet<>();
        for (BaseSampleEsModelDto sample : samples) {
            for (BaseSampleEsModelDto.TestItem testItem : sample.getTestItems()) {
                testItemIds.add(testItem.getTestItemId());
            }
        }
        //当前时间
        Date now = new Date();

        final Map<Long, TestItemDto> itemMap = testItemService.selectByTestItemIds(testItemIds).stream().collect(Collectors.toMap(TestItemDto::getTestItemId, v -> v, (a, b) -> a));

        final LinkedList<ExportTimeoutTestItemVo> list = new LinkedList<>();
        for (OutsourcingInspectionDto sample : samples) {
            //还未分拣到检验样本表
            if (Objects.isNull(sample.getSampleId())) {
                continue;
            }
            final List<BaseSampleEsModelDto.TestItem> tis = sample.getTestItems();
            for (BaseSampleEsModelDto.TestItem e : tis) {
                final TestItemDto item = itemMap.get(e.getTestItemId());
                if (Objects.nonNull(item)) {
                    //检验项目的外送回报告时间
                    final BigDecimal exportDate = item.getExportDate().stripTrailingZeros();

                    //外送时间是样本录入时间加1天开始计算
                    Date sendOutDate = DateUtil.beginOfDay(DateUtils.addDays(sample.getInspectionDate(), 1));

                    //当天日期跳过
                    if (now.compareTo(sendOutDate) < 0) {
                        continue;
                    }

                    final long diff = getDaysDifference(sendOutDate, now) + 1;
                    //超时时间
                    final BigDecimal timeOutDate = exportDate.subtract(new BigDecimal(String.valueOf(diff))).abs().stripTrailingZeros();

                    //未超时的跳过
                    if (NumberUtils.toScaledBigDecimal(String.valueOf(diff)).compareTo((exportDate)) <= 0) {
                        continue;
                    }

                    final ExportTimeoutTestItemVo vo = new ExportTimeoutTestItemVo();
                    vo.setTestItemId(item.getTestItemId());
                    vo.setTestItemCode(item.getTestItemCode());
                    vo.setTestItemName(item.getTestItemName());
                    vo.setPatientName(sample.getPatientName());
                    vo.setPatientSex(sample.getPatientSex());
                    vo.setPatientAge(sample.getPatientAge());
                    vo.setPatientSubage(sample.getPatientSubage());
                    vo.setPatientSubageUnit(sample.getPatientSubageUnit());
                    //已经超时时间
                    vo.setTimeOutDate(timeOutDate);
                    //外送时间
                    vo.setSendOutDate(sample.getInspectionDate());
                    //外送回传报告时间
                    vo.setExportDate(exportDate.stripTrailingZeros() + "个工作日");
                    list.add(vo);
                }
            }

        }
        list.sort((o1, o2) -> o2.getTimeOutDate().compareTo(o1.getTimeOutDate()));

        return list;
    }

    /**
     * 添加图片
     */
    @PostMapping("/add-image")
    public Object addImage(@RequestBody AddSampleImageVo vo) {
        if (Objects.isNull(vo.getApplyId())) {
            throw new IllegalArgumentException("申请单不能为空");
        }
        if (Objects.isNull(vo.getSampleId())) {
            throw new IllegalArgumentException("申请单样本不能为空");
        }
        if (CollectionUtils.isEmpty(vo.getImageUrls())) {
            throw new IllegalArgumentException("图片不能为空");
        }

        List<SampleImageDto> sampleImageDtos = outsourcingSampleService.addImages(vo.getApplyId(), vo.getSampleId(), vo.getImageUrls());
        List<Long> sampleImageIds = sampleImageDtos.stream().map(SampleImageDto::getSampleImageId).collect(Collectors.toList());

        return Map.of("sampleImageIds", sampleImageIds);
    }

    /**
     * 删除图片
     */
    @PostMapping("/delete-image")
    public Object deleteImage(@RequestBody DeleteSampleImageVo vo) {
        if (Objects.isNull(vo.getSampleImageId())) {
            throw new IllegalArgumentException("图片不能为空");
        }

        outsourcingSampleService.deleteImage(vo.getSampleImageId());

        return Collections.emptyMap();
    }

    /**
     * 查询图片
     */
    @GetMapping("select-image")
    public Object selectImage(@RequestParam Long sampleId) {
        if (Objects.isNull(sampleId)) {
            throw new IllegalArgumentException("样本不能为空");
        }

        List<SampleImageDto> sampleImageDtos = sampleImageService.selectSampleImageBySampleId(sampleId);

        return Map.of("images", sampleImageDtos);
    }

    private long getDaysDifference(Date date1, Date date2) {
        // 将java.util.Date转换为java.time.LocalDate
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 使用java.time.LocalDate的方法计算两个日期之间的天数差异
        return Math.abs(localDate2.toEpochDay() - localDate1.toEpochDay());
    }

    /**
     * 获取最近一次历史结果
     */
    public Map<String, OutsourcingInspectionDto.OutsourcingReportItem> getLastResults(final Future<ApplyDto> applyFuture, final Future<ApplySampleDto> applySampleFuture) throws ExecutionException, InterruptedException, TimeoutException {

        final ApplySampleDto applySample = applySampleFuture.get(10, TimeUnit.SECONDS);
        final ApplyDto apply = applyFuture.get(10, TimeUnit.SECONDS);

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        query.setIsAudit(YesOrNoEnum.YES.getCode());
        query.setItemTypes(Collections.singleton(ItemTypeEnum.OUTSOURCING.name()));
        // 默认查3个月
        query.setStartTwoPickDate(DateUtils.addMonths(applySample.getTwoPickDate(), -3));
        query.setEndTwoPickDate(applySample.getTwoPickDate());

        List<BaseSampleEsModelDto> sampleEsModels;

        //处理同人同天
        query.combineOneDayOnePersonParam(apply);

        // 查询 es
        sampleEsModels = elasticSearchSampleService.selectSamples(query);

        final List<OutsourcingInspectionDto> samples = sampleEsModels.stream().filter(OutsourcingInspectionDto.class::isInstance)
                .map(e -> (OutsourcingInspectionDto) e).filter(e -> !Objects.equals(e.getApplySampleId(), applySample.getApplySampleId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(samples)) {
            return Map.of();
        }

        // 日期排序 日期越大越靠前
        samples.sort((o1, o2) -> o2.getSampleId().compareTo(o1.getSampleId()));

        final Map<String, OutsourcingInspectionDto.OutsourcingReportItem> lastResults = new HashMap<>();
        for (OutsourcingInspectionDto sample : samples) {
            final List<OutsourcingInspectionDto.OutsourcingReportItem> reportItems = sample.getReportItems();
            if (CollectionUtils.isEmpty(reportItems)) {
                continue;
            }
            for (OutsourcingInspectionDto.OutsourcingReportItem reportItem : reportItems) {
                if (lastResults.containsKey(reportItem.getReportItemCode())) {
                    continue;
                }
                lastResults.put(reportItem.getReportItemCode(), reportItem);
            }
        }

        return lastResults;

    }

    @PostMapping("/auditSamples")
    public Object auditSamples(@RequestBody OutsourcingSampleAuditVo vo) {

        if (CollectionUtils.isEmpty(vo.getOutsourcingSampleIds())) {
            throw new IllegalArgumentException("样本为空");
        }

        final OutsourcingSampleAuditDto dto = JSON.parseObject(JSON.toJSONString(vo), OutsourcingSampleAuditDto.class);
        outsourcingSampleService.auditSamplesChain(dto);

        return Collections.emptyMap();
    }

    @PostMapping("/cancelAuditSample")
    public Object cancelAuditSample(@RequestBody OutsourcingSampleCancelAuditVo vo) {
        Objects.requireNonNull(vo, "sampleAuditVo 不能为空");

        if (Objects.isNull(vo.getOutsourcingSampleId())) {
            throw new IllegalArgumentException("样本为空");
        }
        final OutsourcingSampleCancelAuditDto dto = JSON.parseObject(JSON.toJSONString(vo), OutsourcingSampleCancelAuditDto.class);

        outsourcingSampleService.cancelAuditSample(dto);

        return Collections.emptyMap();
    }

    /**
     * 审核前提示
     */
    @PostMapping("/auditTips")
    public Object auditTips(@RequestBody OutsourcingSampleAuditDto auditVo) throws Exception {

        final Collection<Long> outsourcingSampleIds = auditVo.getOutsourcingSampleIds();
        if (CollectionUtils.isEmpty(outsourcingSampleIds)) {
            throw new IllegalArgumentException("所选样本为空");
        }

        final ThreadPoolExecutor pool = threadPoolConfig.getPool();
        CompletableFuture<List<OutsourcingSampleDto>> sampleFuture = CompletableFuture.supplyAsync(() -> outsourcingSampleService.selectByOutsourcingSampleIds(outsourcingSampleIds), pool);

        // 查询所有样本报告项目，按照sampleId分组
        final CompletableFuture<Map<Long, List<SampleReportItemDto>>> sampleItemMapFuture = CompletableFuture.supplyAsync(() -> sampleReportItemService.selectBySampleIds(outsourcingSampleIds).stream().collect(Collectors.groupingBy(SampleReportItemDto::getSampleId)), pool);

        // 查询所有样本结果，按照sampleId分组
        final CompletableFuture<Map<Long, List<SampleResultDto>>> sampleResultMapFuture = CompletableFuture.supplyAsync(() -> sampleResultService.selectBySampleIds(outsourcingSampleIds).stream().collect(Collectors.groupingBy(SampleResultDto::getSampleId)), pool);

        CompletableFuture.allOf(sampleFuture, sampleItemMapFuture, sampleResultMapFuture).get(30, TimeUnit.SECONDS);

        // 获取结果
        final List<OutsourcingSampleDto> samples = sampleFuture.get();
        final Map<Long, List<SampleReportItemDto>> sampleItemMap = sampleItemMapFuture.get();
        final Map<Long, List<SampleResultDto>> sampleResultMap = sampleResultMapFuture.get();

        //
        Map<Long, SampleReportDto> sampleReportDtoMap = sampleReportService
                .selectByApplySampleIds(samples.stream().map(OutsourcingSampleDto::getApplySampleId).collect(Collectors.toList()))
                .stream()
                .filter(e -> Objects.equals(e.getIsUploadPdf(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toMap(SampleReportDto::getApplySampleId, Function.identity(), (a, b) -> b));


        // 查询样本仪器下的所有提示内容
        List<InstrumentReportItemResultTipDto> itemTips = instrumentReportItemResultTipService.selectByInstrumentIds(samples.stream().map(OutsourcingSampleDto::getInstrumentId).collect(Collectors.toSet()));

        final List<InstrumentGroupInstrumentDto> instrumentGroupInstrumentDtos = instrumentGroupInstrumentService.selectByInstrumentGroupIds(samples.stream().map(OutsourcingSampleDto::getInstrumentGroupId).collect(Collectors.toList()));
        final Set<Long> instrumentIds = instrumentGroupInstrumentDtos.stream().map(InstrumentGroupInstrumentDto::getInstrumentId).collect(Collectors.toSet());
        // 查询所有仪器报告项目，按照reportItemCode
        final List<InstrumentReportItemDto> instrumentReportItemDtos = instrumentReportItemService.selectByInstrumentIds(instrumentIds);

        StringBuilder sb = new StringBuilder();
        for (OutsourcingSampleDto dto : samples) {
            final List<SampleReportItemDto> reportItemDtos = sampleItemMap.get(dto.getOutsourcingSampleId());
            if (Objects.isNull(reportItemDtos)) {
                continue;
            }
            final List<SampleResultDto> sampleResults = sampleResultMap.get(dto.getOutsourcingSampleId());
            if (Objects.isNull(sampleResults)) {
                continue;
            }
            if (Objects.nonNull(sampleReportDtoMap.get(dto.getApplySampleId()))) {
                continue;
            }
            final Map<Long, SampleResultDto> resultMap = sampleResults.stream().collect(Collectors.toMap(SampleResultDto::getReportItemId, v -> v, (a, b) -> a));

            for (SampleReportItemDto itemDto : reportItemDtos) {
                final SampleResultDto result = resultMap.getOrDefault(itemDto.getReportItemId(), new SampleResultDto());

                final InstrumentReportItemDto reportItemDto = instrumentReportItemDtos.stream().filter(e -> Objects.equals(e.getReportItemCode(), itemDto.getReportItemCode())).findFirst().orElse(new InstrumentReportItemDto());

                if (StringUtils.isBlank(result.getResult()) && Objects.equals(reportItemDto.getIsResultNull(), YesOrNoEnum.NO.getCode())) {
                    throw new IllegalStateException(String.format("样本号 [%s] 报告项目 [%s] 结果值为空", dto.getSampleNo(), itemDto.getReportItemName()));
                }

                if (NumberUtils.isParsable(result.getResult())) {
                    if (BigDecimal.ZERO.compareTo(NumberUtils.toScaledBigDecimal(result.getResult())) == 0 && Objects.equals(reportItemDto.getIsResultZero(), YesOrNoEnum.NO.getCode())) {
                        throw new IllegalStateException(String.format("样本号 [%s] 报告项目 [%s] 结果值为零", dto.getSampleNo(), itemDto.getReportItemName()));
                    }
                }

                BigDecimal decimal = NumberUtils.isParsable(result.getResult()) ? new BigDecimal(result.getResult()) : null;
                // 如果为空 那么就不处理
                if (Objects.isNull(decimal)) {
                    continue;
                }

                final List<InstrumentReportItemResultTipDto> resultTipDtos = itemTips.stream()
                        .filter(e -> ArrayUtils.contains(e.getReportItemCode().split(","), reportItemDto.getReportItemCode())
                                || Objects.equals(e.getReportItemCode(), "0"))
                        .collect(Collectors.toList());

                for (InstrumentReportItemResultTipDto resultTipDto : resultTipDtos) {

                    if (Objects.equals(auditVo.getAuditStatus(), resultTipDto.getTipType())) {

                        this.curTips(dto.getSampleNo(), resultTipDto, result.getResult(), decimal, sb);

                    }

                }

            }

        }

        return Map.of("tip", sb.toString());
    }


    private void curTips(String sampleNo, InstrumentReportItemResultTipDto resultTipDto, String result, BigDecimal value, StringBuilder sb) {
        final BigDecimal tipsMaxDecimal = NumberUtils.isParsable(resultTipDto.getFormulaMaxValue()) ? new BigDecimal(resultTipDto.getFormulaMaxValue()) : null;
        if (Objects.isNull(tipsMaxDecimal)) {
            return;
        }
        final BigDecimal tipsMinDecimal = NumberUtils.isParsable(resultTipDto.getFormulaMinValue()) ? new BigDecimal(resultTipDto.getFormulaMinValue()) : null;

        RelationalOperatorEnum operatorMaxEnum = RelationalOperatorEnum.valueOfByOperator(resultTipDto.getFormulaMax());
        RelationalOperatorEnum operatorMinEnum = RelationalOperatorEnum.valueOfByOperator(resultTipDto.getFormulaMin());

        final String tipContent = resultTipDto.getTipContent();

        switch (operatorMaxEnum) {
            case EQ:
                // result = tips
                if (value.compareTo(tipsMaxDecimal) == NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            case LT:
                // result < tips
                if (value.compareTo(tipsMaxDecimal) < NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            case LE:
                // result <= tips
                if (value.compareTo(tipsMaxDecimal) <= NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
//            case CONTAINS:
//                // result.contains( tips )
//                if (StringUtils.contains(result, resultTipDto.getFormulaMaxValue())) {
//                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
//                }
//                break;
//            case DOES_NOT_CONTAINS:
//                // !result.contains( tips )
//                if (!StringUtils.contains(result, resultTipDto.getFormulaMaxValue())) {
//                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
//                }
//                break;
            case GT:
                // result > tips
                if (value.compareTo(tipsMaxDecimal) > NumberUtils.INTEGER_ZERO) {
                    getMinTips(sampleNo, tipContent, operatorMinEnum, value, tipsMinDecimal, sb);
                }
                break;
            case GE:
                // result >= tips
                if (value.compareTo(tipsMaxDecimal) >= NumberUtils.INTEGER_ZERO) {
                    getMinTips(sampleNo, tipContent, operatorMinEnum, value, tipsMinDecimal, sb);
                }
                break;
        }
    }

    private void getMinTips(String sampleNo, String tipContent, RelationalOperatorEnum operatorMinEnum, BigDecimal value, BigDecimal tipsMinDecimal, StringBuilder sb) {
        switch (operatorMinEnum) {
            case LT:
                if (value.compareTo(tipsMinDecimal) < NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            case LE:
                if (value.compareTo(tipsMinDecimal) <= NumberUtils.INTEGER_ZERO) {
                    sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
                }
                break;
            default:
                sb.append(String.format("样本号 [%s] 报告项目 [%s] \n", sampleNo, tipContent));
        }
    }

    /**
     * 历史结果折线图
     */
    @PostMapping("/history-results-chart")
    public Object historyResultsChart(@RequestBody HistoryResultsChartQueryVo vo) {

        if (Objects.isNull(vo.getOutsourcingSampleId()) || Objects.isNull(vo.getReportItemId())) {
            return Collections.emptyMap();
        }

        final OutsourcingSampleDto sample = outsourcingSampleService.selectByOutsourcingSampleId(vo.getOutsourcingSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("当前样本不存在");
        }
        final ApplyDto applyDto = applyService.selectByApplyId(sample.getApplyId());

        if (Objects.isNull(applyDto)) {
            throw new IllegalStateException("申请单不存在");
        }
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sample.getApplySampleId());
        if (Objects.isNull(applySample)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        final ReportItemDto reportItem = reportItemService.selectByReportItemId(vo.getReportItemId());
        if (Objects.isNull(reportItem)) {
            throw new IllegalStateException("报告项目不存在");
        }

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        query.setIsAudit(YesOrNoEnum.YES.getCode());
        query.setItemTypes(Collections.singleton(ItemTypeEnum.OUTSOURCING.name()));
        query.setStartTwoPickDate(vo.getDateStart());
        query.setEndTwoPickDate(applySample.getTwoPickDate());

        //处理同人同天
        query.combineOneDayOnePersonParam(applyDto);

        final List<OutsourcingInspectionDto> outSamples = elasticSearchSampleService.selectSamples(query).stream().filter(OutsourcingInspectionDto.class::isInstance).map(e -> (OutsourcingInspectionDto) e).collect(Collectors.toList());
        // 移除当前样本

        // 移除当前样本
        outSamples.removeIf(e -> Objects.isNull(e.getSampleId()));
        outSamples.removeIf(e -> Objects.equals(e.getSampleId(), sample.getOutsourcingSampleId()));
        if (CollectionUtils.isEmpty(outSamples)) {
            return Collections.emptyList();
        }

        final HistoryResultsChartVo chartVo = new HistoryResultsChartVo();
        chartVo.setReportItemId(reportItem.getReportItemId());
        chartVo.setReportItemName(reportItem.getReportItemName());
        chartVo.setReportItemCode(reportItem.getReportItemCode());
        chartVo.setResults(new LinkedList<>());

        // 日期排序 日期越大越靠后
        outSamples.sort((o1, o2) -> {
            if (Objects.isNull(o1.getSampleId()) || Objects.isNull(o2.getSampleId())) {
                return NumberUtils.INTEGER_ZERO;
            }
            return o2.getSampleId().compareTo(o1.getSampleId());
        });

        for (OutsourcingInspectionDto hs : outSamples) {

            if (Objects.isNull(hs.getTestDate())) {
                continue;
            }
            if (Objects.isNull(hs.getReportItems())) {
                continue;
            }
            for (OutsourcingInspectionDto.OutsourcingReportItem item : hs.getReportItems()) {
                if (!Objects.equals(item.getReportItemCode(), vo.getReportItemCode())) {
                    continue;
                }

                if (!NumberUtils.isParsable(item.getResult())) {
                    continue;
                }
                final HistoryResultsChartVo.ResultVo v = new HistoryResultsChartVo.ResultVo();
                v.setDate(DateFormatUtils.format(hs.getTestDate(), "yyyy-MM-dd"));
                v.setStatus(item.getStatus());
                v.setResult(item.getResult());
                chartVo.getResults().add(v);
            }

        }

        final SampleResultDto sampleResult = sampleResultService.selectBySampleIdAndReportItemId(vo.getOutsourcingSampleId(), vo.getReportItemId());

        if (Objects.nonNull(sampleResult) && NumberUtils.isParsable(sampleResult.getResult())) {
            final HistoryResultsChartVo.ResultVo currentResult = new HistoryResultsChartVo.ResultVo();
            currentResult.setDate("当前");
            currentResult.setResult(sampleResult.getResult());
            currentResult.setStatus(sampleResult.getStatus());
            chartVo.getResults().add(currentResult);
        }

        return chartVo;
    }

    /**
     * 历史结果对比
     */
    @PostMapping("/result-record-compares")
    public Collection<HistoryResultVo> resultRecordCompares(@RequestBody ResultRecordComparesVo vo) {

        if (Objects.isNull(vo.getOutsourcingSampleId())) {
            return Collections.emptyList();
        }
        final OutsourcingSampleDto sampleDto = outsourcingSampleService.selectByOutsourcingSampleId(vo.getOutsourcingSampleId());
        if (Objects.isNull(sampleDto)) {
            return Collections.emptyList();
        }
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(sampleDto.getApplySampleId());
        if (Objects.isNull(applySample)) {
            return Collections.emptyList();
        }
        final ApplyDto applyDto = applyService.selectByApplyId(sampleDto.getApplyId());
        if (Objects.isNull(applyDto)) {
            return Collections.emptyList();
        }
        final List<SampleReportItemDto> reportItemDtos = sampleReportItemService.selectBySampleId(vo.getOutsourcingSampleId());
        if (CollectionUtils.isEmpty(reportItemDtos)) {
            return Collections.emptyList();
        }

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(1);
        query.setIsAudit(YesOrNoEnum.YES.getCode());
        query.setItemTypes(Collections.singleton(ItemTypeEnum.OUTSOURCING.name()));
        query.setStartTwoPickDate(vo.getDateStart());
        query.setEndTwoPickDate(applySample.getTwoPickDate());

        List<BaseSampleEsModelDto> sampleEsModels;

        //处理同人同天
        query.combineOneDayOnePersonParam(applyDto);

        sampleEsModels = elasticSearchSampleService.selectSamples(query);

        final List<OutsourcingInspectionDto> outSamples = sampleEsModels.stream().filter(OutsourcingInspectionDto.class::isInstance).map(e -> (OutsourcingInspectionDto) e).collect(Collectors.toList());

        outSamples.removeIf(e -> Objects.isNull(e.getSampleId()));
        outSamples.removeIf(e -> Objects.equals(e.getSampleId(), sampleDto.getOutsourcingSampleId()));

        final LinkedList<HistoryResultVo> resultVos = new LinkedList<>();

        final HistoryResultVo historyResultVo = new HistoryResultVo();
        historyResultVo.setSampleNo(sampleDto.getSampleNo());
        historyResultVo.setBarcode(sampleDto.getBarcode());
        historyResultVo.setTestDate(DateFormatUtils.format(sampleDto.getTestDate(), "yyyy-MM-dd"));
        historyResultVo.setResults(new LinkedList<>());

        final Map<Long, SampleResultDto> resultDtos = sampleResultService.selectBySampleId(vo.getOutsourcingSampleId()).stream().collect(Collectors.toMap(SampleResultDto::getReportItemId, v -> v, (a, b) -> a));

        for (SampleReportItemDto r : reportItemDtos) {
            final HistoryResultVo.ResultVo result = new HistoryResultVo.ResultVo();
            result.setReportItemName(r.getReportItemName());
            result.setReportItemId(r.getReportItemId());
            result.setReportItemCode(r.getReportItemCode());
            result.setResult(StringUtils.EMPTY);
            result.setStatus(ResultStatusEnum.NORMAL.getCode());
            result.setJudge(StringUtils.EMPTY);

            final SampleResultDto sampleResult = resultDtos.get(r.getReportItemId());
            if (Objects.nonNull(sampleResult)) {
                result.setResult(sampleResult.getResult());
                result.setStatus(sampleResult.getStatus());
                result.setJudge(sampleResult.getJudge());
            }

            historyResultVo.getResults().add(result);

        }
        resultVos.add(historyResultVo);

        // 日期排序 日期越大越靠前
        outSamples.sort((o1, o2) -> {
            if (Objects.isNull(o1.getSampleId()) || Objects.isNull(o2.getSampleId())) {
                return NumberUtils.INTEGER_ZERO;
            }
            return o2.getSampleId().compareTo(o1.getSampleId());
        });

        for (OutsourcingInspectionDto hs : outSamples) {
            final HistoryResultVo resultVo = new HistoryResultVo();
            resultVo.setSampleNo(hs.getSampleNo());
            resultVo.setBarcode(hs.getBarcode());
            resultVo.setTestDate(DateFormatUtils.format(hs.getTestDate(), "yyyy-MM-dd"));
            resultVo.setResults(new LinkedList<>());
            hs.setReportItems(ObjectUtils.defaultIfNull(hs.getReportItems(), List.of()));

            for (OutsourcingInspectionDto.OutsourcingReportItem item : hs.getReportItems()) {

                // 如果历史做的不在本次做的里面，那么排除掉
                if (historyResultVo.getResults().stream().noneMatch(e -> Objects.equals(e.getReportItemCode(), item.getReportItemCode()))) {
                    continue;
                }

                final HistoryResultVo.ResultVo resVo = new HistoryResultVo.ResultVo();
                resVo.setResult(item.getResult());
                resVo.setStatus(item.getStatus());
                resVo.setReportItemCode(item.getReportItemCode());
                resVo.setReportItemName(item.getReportItemName());
                resVo.setJudge(item.getJudge());
                resultVo.getResults().add(resVo);
            }
            resultVos.add(resultVo);
        }

        return resultVos;
    }

    /**
     * -----------样本结果查询------------
     */

    @PostMapping("/samples-es")
    public Object getSamples(@RequestBody QueryRoutineSamplesVo queryVo) {

        // 转为es 查询条件
        SampleEsQuery query = getSampleEsQuery(queryVo);

        final List<OutsourcingInspectionDto> sampleEsModels = elasticSearchSampleService.selectSamples(query)
                // 外送样本
                .stream().filter(OutsourcingInspectionDto.class::isInstance)
                // 转成常规样本
                .map(e -> (OutsourcingInspectionDto) e).collect(Collectors.toList());

        return selectRoutineSamplesVo(sampleEsModels);

    }

    /**
     * 查询 检验结果列表 信息
     */

    private List<OutsourcingRoutineSamplesVo> selectRoutineSamplesVo(List<OutsourcingInspectionDto> sampleEsModels) {
        final List<OutsourcingRoutineSamplesVo> targetList = Lists.newArrayListWithCapacity(sampleEsModels.size());

        for (OutsourcingInspectionDto model : sampleEsModels) {
            final OutsourcingRoutineSamplesVo sampleVo = new OutsourcingRoutineSamplesVo();
            sampleVo.setOutsourcingSampleId(model.getSampleId());
            sampleVo.setSampleNo(model.getSampleNo());
            sampleVo.setApplySampleId(model.getApplySampleId());
            sampleVo.setApplyId(model.getApplyId());
            sampleVo.setStatus(model.getSampleStatus());
            sampleVo.setBarcode(model.getBarcode());
            sampleVo.setSampleNo(model.getSampleNo());
            sampleVo.setGroupId(model.getGroupId());
            sampleVo.setGroupName(model.getGroupName());
            sampleVo.setInstrumentGroupId(model.getInstrumentGroupId());
            sampleVo.setInstrumentGroupName(model.getInstrumentGroupName());
            sampleVo.setTestDate(model.getTestDate());
            sampleVo.setCheckerName(model.getFinalCheckerName());
            sampleVo.setCheckerId(model.getFinalCheckerId());
            sampleVo.setCheckDate(model.getFinalCheckDate());
            sampleVo.setEnterDate(model.getApplyDate());
            sampleVo.setFinalCheckDate(model.getFinalCheckDate());
            sampleVo.setSampleRemark(model.getSampleRemark());
            sampleVo.setResultRemark(model.getResultRemark());
            sampleVo.setHspOrgId(model.getHspOrgId());
            sampleVo.setHspOrgName(model.getHspOrgName());
            sampleVo.setUrgent(model.getUrgent());
            sampleVo.setTestItemName(StringUtils.EMPTY);
            if (CollectionUtils.isNotEmpty(model.getTestItems())) {
                sampleVo.setTestItemName(StringUtils.join(model.getTestItems().stream().map(BaseSampleEsModelDto.TestItem::getTestItemName).collect(Collectors.toList()), ","));
            }
            sampleVo.setPatientName(model.getPatientName());
            sampleVo.setPatientSex(SexEnum.getByCode(model.getPatientSex()).getDesc());
            sampleVo.setPatientAge(model.getPatientAge());
            sampleVo.setPatientSubage(model.getPatientSubage());
            sampleVo.setPatientSubageUnit(model.getPatientSubageUnit());
            sampleVo.setIsPrint(model.getIsPrint());
            sampleVo.setApplyType(model.getApplyTypeName());
            sampleVo.setDiagnosis(model.getDiagnosis());
            sampleVo.setPatientVisitCard(model.getPatientVisitCard());
            targetList.add(sampleVo);
        }

        return targetList.stream().sorted(Comparator.comparing(OutsourcingRoutineSamplesVo::getFinalCheckDate, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(OutsourcingRoutineSamplesVo::getOutsourcingSampleId)).collect(Collectors.toList());
    }

    /**
     * 获取结果查询 es 条件
     */

    private SampleEsQuery getSampleEsQuery(QueryRoutineSamplesVo queryVo) {
        LoginUserHandler.User user = LoginUserHandler.get();

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(NumberUtils.INTEGER_ONE);
        // 当前用户所属专业组
        query.setGroupIds(Collections.singleton(user.getGroupId()));

        // 已审
        query.setIsAudit(YesOrNoEnum.YES.getCode());

        // 只查询常规检验
        query.setItemTypes(Collections.singleton(ItemTypeEnum.OUTSOURCING.name()));

        // 检验日期
        if (Objects.nonNull(queryVo.getTestDateStart()) && Objects.nonNull(queryVo.getTestDateEnd())) {
            query.setStartTestDate(queryVo.getTestDateStart());
            query.setEndTestDate(queryVo.getTestDateEnd());
        }

        // 审核日期
        if (Objects.nonNull(queryVo.getCheckDateStart()) && Objects.nonNull(queryVo.getCheckDateEnd())) {
            query.setStartFinalCheckDate(queryVo.getCheckDateStart());
            query.setEndFinalCheckDate(queryVo.getCheckDateEnd());
        }

        // 检验者ID
        if (Objects.nonNull(queryVo.getTesterId())) {
            query.setTesterId(queryVo.getTesterId());
        }

        // 审核人ID
        if (Objects.nonNull(queryVo.getCheckerId())) {
            query.setFinalCheckerIds(Collections.singleton(queryVo.getCheckerId()));
        }

        // 检验项目
        if (Objects.nonNull(queryVo.getTestItemId())) {
            query.setTestItemIds(Collections.singleton(queryVo.getTestItemId()));
        }

        // 送检机构
        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Collections.singleton(queryVo.getHspOrgId()));
        }

        // 姓名
        if (StringUtils.isNotBlank(queryVo.getPatientName())) {
            query.setPatientName(queryVo.getPatientName());
        }

        // 性别
        if (Objects.nonNull(queryVo.getPatientSex()) && !Objects.equals(queryVo.getPatientSex(), SexEnum.DEFAULT.getCode())) {
            query.setPatientSex(queryVo.getPatientSex());
        }
        // 门诊/住院号
        if (StringUtils.isNotBlank(queryVo.getPatientVisitCard())) {
            query.setPatientVisitCard(queryVo.getPatientVisitCard());
        }

        if (StringUtils.isNotBlank(queryVo.getApplyType())) {
            query.setApplyTypes(Collections.singleton(queryVo.getApplyType()));
        }

        if (StringUtils.isNotBlank(queryVo.getBarcode())) {
            query.setBarcodes(Collections.singleton(queryVo.getBarcode()));
        }
        query.setSorts(Lists.newArrayList(SampleEsQuery.Sort.builder().filedName("finalCheckDate").order("ASC").build()));
        return query;

    }


    /**
     * 查询样本的缺项情况
     */
    @PostMapping("/get-miss-report-item")
    public List<SampleMissReportItemVo> getMissReportItem(@RequestBody List<MissReportItemVo> items)
            throws InterruptedException, ExecutionException {

        if (CollectionUtils.isEmpty(items)) {
            return List.of();
        }

        List<Long> outsourcingSampleIds = items.stream().map(MissReportItemVo::getOutsourcingSampleId).collect(Collectors.toList());

        final Map<Long, List<SampleRetestMainDto>> sampleRetestMainMap = sampleRetestMainService.selectBySampleIds(outsourcingSampleIds)
                .stream().collect(Collectors.groupingBy(SampleRetestMainDto::getApplySampleId));


        // 多线程跑
        final List<Callable<List<SampleMissReportItemVo>>> callables =
                ListUtils.partition(items, 20).stream().map(s -> (Callable<List<SampleMissReportItemVo>>) () -> {
                    final Map<Long, ApplySampleDto> applySamples = applySampleService.selectByApplySampleIdsAsMap(
                            s.stream().map(MissReportItemVo::getApplySampleId).collect(Collectors.toSet()));
                    final List<SampleMissReportItemVo> list = new LinkedList<>();
                    for (MissReportItemVo e : s) {

                        final SampleMissReportItemVo vo = new SampleMissReportItemVo();
                        list.add(vo);
                        vo.setOutsourcingSampleId(e.getOutsourcingSampleId());
                        vo.setIsException(YesOrNoEnum.NO.getCode());
                        vo.setIsCritical(YesOrNoEnum.NO.getCode());
                        vo.setIsRetest(YesOrNoEnum.NO.getCode());

                        final ApplySampleDto applySample = applySamples.get(e.getApplySampleId());
                        if (Objects.isNull(applySample)) {
                            continue;
                        }
                        vo.setStatus(applySample.getStatus());
                        // 设置状态颜色
                        vo.setColorMarking(applySample.getColorMarking());

                        final HashOperations<String, String, String> operations = stringRedisTemplate.opsForHash();
                        final Map<String, String> entries =
                                operations.entries(updateMissItemCommand.getMissItemKey(e.getOutsourcingSampleId()));
                        final Map<String, String> retestEntries;

                        // 如果是复查的 那么查询复查的缺项
                        if (Objects.equals(SampleStatusEnum.RETEST.getCode(), applySample.getStatus())) {
                            retestEntries =
                                    operations.entries(outsourcingStartRetestUpdateMissItemCommand.getMissItemKey(e.getOutsourcingSampleId()));
                        } else {
                            retestEntries = Map.of();
                        }

                        if (MapUtils.isEmpty(entries)) {
                            continue;
                        }

                        final List<ResultStatusDto> rs = entries.values().stream()
                                .map(k -> JSON.parseObject(k, ResultStatusDto.class)).collect(Collectors.toList());
                        final List<ResultStatusDto> retestRs = retestEntries.values().stream()
                                .map(k -> JSON.parseObject(k, ResultStatusDto.class)).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(rs)) {
                            continue;
                        }

                        // 没有结果的
                        final long missCount = rs.stream().filter(k -> StringUtils.isBlank(k.getResult())).count()
                                + retestRs.stream().filter(k -> StringUtils.isBlank(k.getResult())).count();

                        // 缺项的数量(全、-1、-2、-3、-4、-5、缺)
                        final String value = stringRedisTemplate.opsForValue().get(SampleReportDto.getIsUploadPdfKey(e.getOutsourcingSampleId()));
                        vo.setMissReportItemNum(MissReportItemNumTips.getTips(missCount, value));

                        //                        final ApplySampleDto as = applySamples.get(e.getApplySampleId());
                        // 如果样本状态是 11 待复测 状态返回复测
                        //                        if (Objects.nonNull(as) && (Objects.equals(as.getStatus(), SampleStatusEnum.RETEST.getCode()))) {
                        //                            vo.setIsRetest(YesOrNoEnum.YES.getCode());
                        //                        }
                        if (CollectionUtils.isNotEmpty(sampleRetestMainMap.get(e.getApplySampleId()))) {
                            vo.setIsRetest(YesOrNoEnum.YES.getCode());
                        }

                        for (ResultStatusDto r : rs) {
                            if (Objects.equals(r.getIsException(), YesOrNoEnum.YES.getCode())) {
                                vo.setIsException(YesOrNoEnum.YES.getCode());
                            }
                            if (Objects.equals(r.getIsCritical(), YesOrNoEnum.YES.getCode())) {
                                vo.setIsCritical(YesOrNoEnum.YES.getCode());
                            }
                            if (Objects.equals(r.getIsRetest(), YesOrNoEnum.YES.getCode())) {
                                vo.setIsRetest(YesOrNoEnum.YES.getCode());
                            }
                            if (Objects.equals(r.getIsException(), YesOrNoEnum.YES.getCode())) {
                                vo.setIsException(YesOrNoEnum.YES.getCode());
                            }
                        }

                    }
                    return list;
                }).collect(Collectors.toList());

        final List<List<SampleMissReportItemVo>> results = new ArrayList<>();

        for (Future<List<SampleMissReportItemVo>> future : threadPoolConfig.getPool().invokeAll(callables, 30,
                TimeUnit.SECONDS)) {
            results.add(future.get());
        }

        return results.stream().flatMap(Collection::stream).collect(Collectors.toList());
    }

}
