package com.labway.lims.outsourcing.service.chain.his.sign;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.request.SignOutApplyInfoRequest;
import com.labway.business.center.compare.service.OutApplyInfoService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.HisTestApplyDto;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.api.dto.his.HisSample;
import com.labway.lims.outsourcing.api.dto.his.HisSignParam;
import com.labway.lims.outsourcing.api.service.HisService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.labway.lims.outsourcing.service.HisServiceImpl.SUCCESS_CODE;

/**
 * <pre>
 * SignHisSampleBusinessCenterSignCommand
 * 调用业务中台签收
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/15 19:14
 */
@Slf4j
@Component
public class SignHisSampleBusinessCenterSignCommand implements Command {
    @DubboReference
    private ApplyService applyService;
    @Resource
    private OutApplyInfoService outApplyInfoService;

    @Override
    public boolean execute(Context context) throws Exception {
        SignHisSampleContext from = SignHisSampleContext.from(context);
        HisSignParam sign = from.getHisSignParam();

        String orgCode = from.getOrgCode();
        HisSample hisSample = from.getHisSample();
        HisTestApplyDto hisTestApply = from.getHisTestApply();
        ApplyInfo applyInfo = from.getApplyInfo();
        List<ApplyInfo.Sample> samples = applyInfo.getSamples();

        try {
            final String limsBarcode = samples.stream().map(ApplyInfo.Sample::getBarcode).findFirst().orElse(null);
            // 通知业务中台保存成功
            final SignOutApplyInfoRequest request = new SignOutApplyInfoRequest();
            request.setHspOrgCode(hisSample.getHspOrgCode());
            request.setReceiveUserCode(String.valueOf(LoginUserHandler.get().getUserId()));
            request.setReceiveUserName(LoginUserHandler.get().getNickname());
            request.setBarcode(hisTestApply.getOutBarcode());
            request.setSignBarcode(limsBarcode);
            request.setSignMainBarcode(applyInfo.getMasterBarcode());
            request.setSignOrgCode(orgCode);
            request.setSignOrgName(LoginUserHandler.get().getOrgName());
            request.setNoType(sign.getNoType());

            StopWatch stopWatch = new StopWatch();
            stopWatch.start("签收条码");
            final Response<?> response = outApplyInfoService.signOutApplyInfo(request);
            stopWatch.stop();

            if (Objects.isNull(response)) {
                // 回滚信息
                applyService.deleteApply(Collections.singleton(applyInfo.getApplyId()));
                throw new IllegalStateException("业务中台未返回签收结果");
            }

            if (!Objects.equals(response.getCode(), SUCCESS_CODE)) {
                // 回滚信息
                applyService.deleteApply(Collections.singleton(applyInfo.getApplyId()));
                throw new IllegalStateException(String.format(String.format("外部条码 [%s] 签收失败，业务中台返回消息 [%s]",
                        hisTestApply.getOutBarcode(), StringUtils.defaultString(response.getMsg(), "签收错误"))));
            }

            // 签收成功,默认调用交接接口
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                ((HisService) AopContext.currentProxy()).handoverSample(sign, hisSample, orgCode, samples, applyInfo, LoginUserHandler.get());
            }

            log.info("用户 [{}] 专业组 [{}] 条码签收成功，条码号：{}  结果 [{}] 耗时 [{}]", LoginUserHandler.get().getNickname(),
                    LoginUserHandler.get().getGroupName(), hisTestApply.getOutBarcode(), JSON.toJSON(applyInfo),
                    stopWatch.getTotalTimeMillis());

            // TODO 到这里签收已经结束了
            // return applyInfo;
        } catch (Exception e) {
            applyService.deleteApply(Collections.singleton(applyInfo.getApplyId()));
            log.error("用户 [{}] 专业组 [{}] 条码签收失败，条码号：[{}] ", LoginUserHandler.get().getNickname(),
                    LoginUserHandler.get().getGroupName(), hisTestApply.getOutBarcode());

            throw new IllegalStateException(e.getMessage(), e);
        }

        return CONTINUE_PROCESSING;
    }

}
