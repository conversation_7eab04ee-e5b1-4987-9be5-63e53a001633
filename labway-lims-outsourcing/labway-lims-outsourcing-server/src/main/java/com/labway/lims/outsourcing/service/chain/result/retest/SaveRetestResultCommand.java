package com.labway.lims.outsourcing.service.chain.result.retest;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.RetestModeEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.Objects;

@Slf4j
@Component
public class SaveRetestResultCommand implements Command {


    private static final String RETEST_ITEM = IdUtil.objectId();

    @DubboReference
    private SampleRetestMainService sampleRetestMainService;
    @DubboReference
    private SampleRetestItemService sampleRetestItemService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        final RetestResultContext context = RetestResultContext.from(c);

        final InstrumentReportItemReferenceDto reportItemReference = context.getInstrumentReportItemReference();


        final OutsourcingSampleDto sample = context.getSample();
        final SampleRetestMainDto retestMain = context.getSampleRetestMain();


        final SampleReportItemDto sampleReportItem = context.getSampleReportItem();
        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();
        final SaveResultSourceEnum source = context.getSource();
        final LoginUserHandler.User user = LoginUserHandler.get();

        //删除本轮复查当前报告项目对应的记录
        sampleRetestItemService.deleteBySampleRetestMainIdAndReportItemId(retestMain.getSampleRetestMainId(),
                sampleReportItem.getReportItemId());

        final SampleRetestItemDto dto = new SampleRetestItemDto();
        dto.setSampleRetestItemId(snowflakeService.genId());
        dto.setSampleRetestMainId(retestMain.getSampleRetestMainId());
        dto.setApplySampleId(sample.getApplySampleId());
        dto.setSampleId(sample.getOutsourcingSampleId());
        dto.setTestItemId(sampleReportItem.getTestItemId());
        dto.setTestItemName(StringUtils.defaultString(sampleReportItem.getTestItemName()));
        dto.setTestItemCode(StringUtils.defaultString(sampleReportItem.getTestItemCode()));
        dto.setReportItemId(context.getReportItemId());
        dto.setReportItemName(StringUtils.defaultString(sampleReportItem.getReportItemName()));
        dto.setReportItemCode(StringUtils.defaultString(sampleReportItem.getReportItemCode()));
        if (Objects.nonNull(instrumentReportItem)) {
            dto.setTestResultType(StringUtils.defaultString(instrumentReportItem.getResultTypeName()));
            dto.setTestResultTypeCode(StringUtils.defaultString(instrumentReportItem.getResultTypeCode()));
        } else {
            dto.setTestResultType(StringUtils.EMPTY);
            dto.setTestResultTypeCode(StringUtils.EMPTY);
        }
        if (Objects.equals(SaveResultSourceEnum.FRONT, source)) {
            dto.setRetestMode(RetestModeEnum.FRONT.getCode());
        } else if (Objects.equals(SaveResultSourceEnum.MACHINE, source)) {
            dto.setRetestMode(RetestModeEnum.MACHINE.getCode());
        } else if (Objects.equals(SaveResultSourceEnum.MANUAL_SYNC, source)) {
            dto.setRetestMode(RetestModeEnum.MANUAL_SYNC.getCode());
        } else {
            dto.setRetestMode(RetestModeEnum.DEFAULT.getCode());
        }

        dto.setRetesterName(StringUtils.defaultString(user.getNickname()));
        dto.setRetesterId(user.getUserId());
        dto.setOrgName(StringUtils.defaultString(user.getOrgName()));

        if (context.isCritical()) {
            dto.setStatus(ResultStatusEnum.CRISIS.getCode());
        } else if (context.isException()) {
            dto.setStatus(ResultStatusEnum.EXCEPTION.getCode());
        } else {
            dto.setStatus(ResultStatusEnum.NORMAL.getCode());
        }
        dto.setJudge(StringUtils.defaultString(context.getResultJudge()));
        dto.setResult(context.getResult());
        // 参考范围
        if (Objects.nonNull(reportItemReference)) {
            // 先取中文 -> 英文 -> 中英文
            dto.setRange(StringUtils.defaultString(reportItemReference.getCnRefereValue(), reportItemReference.getEnRefereValue()));
            dto.setRange(
                    StringUtils.defaultIfBlank(dto.getRange(), reportItemReference.getCnEnRefereValue()));
        } else {
            dto.setRange(StringUtils.EMPTY);
        }

        if (Objects.equals(SaveResultSourceEnum.FRONT, source)) {
            dto.setRetestMode(RetestModeEnum.FRONT.getCode());
        } else if (Objects.equals(SaveResultSourceEnum.MACHINE, source)) {
            dto.setRetestMode(RetestModeEnum.MACHINE.getCode());
        } else if (Objects.equals(SaveResultSourceEnum.MANUAL_SYNC, source)) {
            dto.setRetestMode(RetestModeEnum.MANUAL_SYNC.getCode());
        } else {
            dto.setRetestMode(RetestModeEnum.DEFAULT.getCode());
        }

        //新增复查结果
        sampleRetestItemService.addSampleRetestItem(dto);

        context.put(RETEST_ITEM, dto);

        return CONTINUE_PROCESSING;
    }

    @Nullable
    public SampleRetestItemDto getSampleRetestItem(Context c) {
        return (SampleRetestItemDto) c.get(RETEST_ITEM);
    }
}
