package com.labway.lims.outsourcing.service.chain.sync;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.labway.business.center.compare.dto.OrgApplyResultSamplesDTO;
import com.labway.business.center.compare.dto.TbOrgApplySampleMainIteResultDTO;
import com.labway.business.center.compare.request.QueryLimsOutSourceSampleInfoRequest;
import com.labway.business.center.compare.service.ILimsOutSourceSampleService;
import com.labway.business.center.compare.service.TbOrgApplySampleMainService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.LimsOrgCodeConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSaveResultDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleResultService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.swak.frame.util.UUIDHexGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SyncOutsourcingGetAndSaveResultsCommand implements Command, Filter, InitializingBean {

    private static final String OUT_SAMPLE = IdUtil.simpleUUID();

    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Value("${business-center.org-code:00010110000000001WND}")
    private String orgCode;
    @Resource
    private TbOrgApplySampleMainService tbOrgApplySampleMainService;
    @Resource
    private OutsourcingSampleResultService outsourcingSampleResultService;
    @Autowired
    private LimsOrgCodeConfig limsOrgCodeConfig;
    @Resource
    private ILimsOutSourceSampleService limsOutSourceSampleService;
    @Value("${skipNoresultReportItem:true}")
    private Boolean skipNoresultReportItem;

    @Override
    public boolean execute(Context c) throws Exception {
        final SyncOutsourcingContext context = SyncOutsourcingContext.from(c);

        final Map<Long, List<SampleReportItemDto>> sampleReportItems = sampleReportItemService.selectBySampleIds(context.getOutsourcingSampleIds())
                .stream().collect(Collectors.groupingBy(SampleReportItemDto::getSampleId));

        context.setSampleReportItems(sampleReportItems);

        final LinkedList<Long> ids = snowflakeService.genIds(context.getOutsourcingSamples().size());

        for (OutsourcingSampleDto e : context.getOutsourcingSamples()) {
            // 外送机构ID
            final Long exportOrgId = e.getExportOrgId();

            final List<SampleReportItemDto> items = sampleReportItems.get(e.getOutsourcingSampleId());
            if (CollectionUtils.isEmpty(items)) {
                throw new IllegalStateException(String.format("条码 [%s] 样本报告项目为空", e.getBarcode()));
            }

            final HspOrganizationDto exportHspOrganization = context.getHspOrgMap().get(exportOrgId);
            if (Objects.isNull(exportHspOrganization)) {
                throw new IllegalStateException(String.format("条码 [%s] 外送机构不存在", e.getBarcode()));
            }

            final HspOrganizationDto hspOrganization = context.getHspOrgMap().get(e.getHspOrgId());
            if (Objects.isNull(hspOrganization)) {
                throw new IllegalStateException(String.format("条码 [%s] 送检机构不存在", e.getBarcode()));
            }

            final QueryLimsOutSourceSampleInfoRequest req = new QueryLimsOutSourceSampleInfoRequest();
            req.setOrgCode(limsOrgCodeConfig.getMappedOrgCode(exportHspOrganization.getHspOrgCode()));
            req.setHspOrgCode(orgCode);
            req.setBarCodes(ImmutableList.of(e.getBarcode()));

            log.info("开始调用业务中台，查询外送样本结果信息，查询入参：{}", JSONObject.toJSONString(req));
            final List<OrgApplyResultSamplesDTO> data = limsOutSourceSampleService.queryResultSample(req).getData();
            log.info("开始调用业务中台，查询外送样本结果信息，返回信息：{}", JSONObject.toJSONString(data));
            if (CollectionUtils.isEmpty(data)) {
                throw new IllegalStateException(String.format("条码 [%s] 从业务中台获取结果信息失败", e.getBarcode()));
            }

            final OrgApplyResultSamplesDTO p = data.iterator().next();
            if (CollectionUtils.isEmpty(p.getTbOrgApplySampleMainIteResultDTOS())) {
                throw new IllegalStateException(String.format("条码 [%s] 从业务中台获取结果信息为空", e.getBarcode()));
            }

            context.put(OUT_SAMPLE + e.getOutsourcingSampleId(), p);

            final Map<String, TbOrgApplySampleMainIteResultDTO> outResults = p.getTbOrgApplySampleMainIteResultDTOS().stream()
                    .collect(Collectors.toMap(TbOrgApplySampleMainIteResultDTO::getCompareItemReportCode, v -> v, (a, b) -> a));

            final List<OutsourcingSaveResultDto> rs = new ArrayList<>();
            for (SampleReportItemDto t : items) {
                final TbOrgApplySampleMainIteResultDTO outResult = outResults.get(t.getReportItemCode());
                if (Objects.isNull(outResult)) {
                    log.warn("条码 [{}] 存在未对照的预置报告项目 [{}] ，结果值不进行回传填充，请手动填写！！", e.getBarcode(), t.getReportItemName());
                    if (BooleanUtils.isTrue(Objects.requireNonNullElse(context.getSkipNoresultReportItem(), skipNoresultReportItem))) {
                        // 跳过未出结果的项目，继续保存其他结果
                        continue;
                    }
                    throw new IllegalStateException(String.format("条码 [%s] 存在未对照的预置报告项目 [%s]", e.getBarcode(), t.getReportItemName()));
                }

                if (!context.isIgnoreMissingReportItem() && StringUtils.isBlank(outResult.getCompareItemReportCode())) {
                    log.error("条码 [{}] 报告项目 [{}] 存在未对照，结果值不进行回传填充！！！", e.getBarcode(), outResult.getCompareItemReportName());
                    throw new IllegalStateException(String.format("条码 [%s] 报告项目 [%s] 存在未对照", e.getBarcode(), t.getReportItemName()));
                }

                final OutsourcingSaveResultDto dto = new OutsourcingSaveResultDto();
                dto.setOutsourcingSampleId(e.getOutsourcingSampleId());
                dto.setApplySampleId(e.getApplySampleId());
                dto.setApplyId(e.getApplyId());
                dto.setReportItemId(t.getReportItemId());
                dto.setReportItemCode(t.getReportItemCode());
                dto.setResult(outResult.getTestResult());
                dto.setDate(new Date());
                rs.add(dto);
            }
            if (CollectionUtils.isNotEmpty(rs)) {
                outsourcingSampleResultService.saveResults(rs, SaveResultSourceEnum.MANUAL_SYNC);
            }

            // 需要删除图片的样本ID
            context.getDeleteSampleImageSampleIds().add(e.getOutsourcingSampleId());

            // 添加图片
            if (StringUtils.isNotBlank(p.getImgUrls())) {
                try {
                    for (String imageUrl : JSON.parseArray(p.getImgUrls(), String.class)) {
                        final SampleImageDto k = new SampleImageDto();
                        k.setSampleImageId(ids.pop());
                        k.setApplyId(e.getApplyId());
                        k.setSampleId(e.getOutsourcingSampleId());
                        k.setApplySampleId(e.getApplySampleId());
                        k.setItemType(ItemTypeEnum.OUTSOURCING.name());
                        // 图片名称
                        k.setImageName(UUIDHexGenerator.generator());
                        // 图片地址
                        k.setImageUrl(imageUrl);
                        k.setIsDelete(YesOrNoEnum.NO.getCode());
                        k.setCreateDate(new Date());
                        k.setUpdateDate(new Date());
                        k.setCreatorId(LoginUserHandler.get().getUserId());
                        k.setCreatorName(LoginUserHandler.get().getNickname());
                        k.setUpdaterId(LoginUserHandler.get().getUserId());
                        k.setUpdaterName(LoginUserHandler.get().getNickname());
                        context.getAddSampleImages().add(k);
                    }
                } catch (Exception ex) {
                    log.error("解析图片结果异常 [{}] [{}]", p.getImgUrls(), e);
                }
            }

            if (StringUtils.isNotBlank(p.getResultRemark())) {
                // 需要更新的申请单样本
                ApplySampleDto applySampleDto = new ApplySampleDto();
                applySampleDto.setApplySampleId(e.getApplySampleId());
                // 结果备注（建议与解释）
                applySampleDto.setResultRemark(p.getResultRemark());
                context.getUpdateApplySampleDtos().add(applySampleDto);
            }
        }

        return CONTINUE_PROCESSING;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        if (Objects.isNull(exception)) {
            return CONTINUE_PROCESSING;
        }

        try {
            if (Boolean.TRUE) {
                return CONTINUE_PROCESSING;
            }
            // 遇到错误时，删除录入的结果
            // sampleReportItemService.deleteBySampleIds(SyncOutsourcingContext.from(c).getOutsourcingSampleIds());
        } catch (Exception ignored) {

        }

        return CONTINUE_PROCESSING;
    }
}
