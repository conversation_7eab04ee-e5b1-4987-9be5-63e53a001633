package com.labway.lims.outsourcing.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 样本结果复查子表
 *
 * <AUTHOR>
 * @since 2023/4/11 17:58
 */
@Getter
@Setter
public class SampleRetestItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 复查结果子表ID
     */
    private Long sampleRetestItemId;
    /**
     * 复查结果主表ID
     */
    private Long sampleRetestMainId;
    /**
     * 申请单样本ID
     */
    private Long applySampleId;
    /**
     * 样本ID
     */
    private Long sampleId;
    /**
     * 样本检验项目id
     */
    private Long testItemId;
    /**
     * 样本检验项目code
     */
    private String testItemCode;
    /**
     * 样本检验项目名称
     */
    private String testItemName;
    /**
     * 样本报告项目id
     */
    private Long reportItemId;
    /**
     * 样本报告项目编码
     */
    private String reportItemCode;
    /**
     * 样本报告项目名称
     */
    private String reportItemName;
    /**
     * 检测结果类型
     */
    private String testResultType;
    /**
     * 检测结果类型编码
     */
    private String testResultTypeCode;
    /**
     * 复查模式（1手动 2仪器）
     */
    private Integer retestMode;
    /**
     * 复查结果
     */
    private String result;
    /**
     * 检验判断
     */
    private String judge;
    /**
     * 参考值
     */
    private String range;
    /**
     * 1: 危机 2: 异常 0: 正常
     * @see ResultStatusEnum
     */
    private Integer status;
    /**
     * 复查人
     */
    private String retesterName;
    /**
     * 复查人ID
     */
    private Long retesterId;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 0:未删除，1：已删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人ID
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新人ID
     */
    private Long updaterId;
    /**
     * 更新人名称
     */
    private String updaterName;
}
