package com.labway.lims.outsourcing.service.chain.audit;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.ExceptionCodeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.EmptyReferenceTipEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleAuditDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CheckSampleResultCommand implements Command {

    @DubboReference
    private SampleReportService sampleReportService;

    @DubboReference
    private SampleAbnormalService sampleAbnormalService;

    @Override
    public boolean execute(Context c) throws Exception {

        AuditSampleContext context = AuditSampleContext.from(c);
        List<OutsourcingSampleDto> samples = context.getSamples();

        Map<Long, List<SampleReportItemDto>> sampleReportItemBySampleId =
                context.getSampleReportItemDtos().stream().collect(Collectors.groupingBy(SampleReportItemDto::getSampleId));

        Map<Long, List<SampleResultDto>> sampleResultBySampleId =
                context.getSampleResultDtos().stream().collect(Collectors.groupingBy(SampleResultDto::getSampleId));

        Map<Long, List<InstrumentReportItemDto>> instrumentReportItemDtos = context.getInstrumentReportItemDtos();

        List<Long> applySampleIds = samples.stream().map(OutsourcingSampleDto::getApplySampleId).collect(Collectors.toList());

        List<Long> uploadReportApplysampleIds = sampleReportService.selectByApplySampleIds(applySampleIds).stream()
                .filter(e -> Objects.equals(e.getIsUploadPdf(), YesOrNoEnum.YES.getCode()))
                .map(SampleReportDto::getApplySampleId)
                .collect(Collectors.toList());

        samples = samples.stream()
                .filter(e -> !uploadReportApplysampleIds.contains(e.getApplySampleId()))
                .collect(Collectors.toList());

        //1.1.3.7 新增异常结果提醒
        // 如果不是强制审核 则做查询判断
        final OutsourcingSampleAuditDto param = context.getAuditDto();
        if (!param.getAuditForce() && CollectionUtils.isNotEmpty(samples)) {
            List<SampleAbnormalDto> sampleAbnormalDtoList = sampleAbnormalService.selectByBarcodes(samples.stream().map(OutsourcingSampleDto::getBarcode).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(sampleAbnormalDtoList)) {
                List<String> errorMsgList = new ArrayList<>();
                Set<String> barcodes = new HashSet<>();
                sampleAbnormalDtoList.forEach(sampleAbnormalDto -> {
                    if (!barcodes.contains(sampleAbnormalDto.getBarcode())) {
                        errorMsgList.add(String.format("条码号 [%s]的样本存在异常情况！\n", sampleAbnormalDto.getBarcode()));
                        barcodes.add(sampleAbnormalDto.getBarcode());
                    }
                });
                throw new LimsCodeException(ExceptionCodeEnum.ROUTINE_AUDIT.getCode(),
                        JSON.toJSONString(errorMsgList));
            }
        }

        for (OutsourcingSampleDto dto : samples) {

            // 对应样本报告 项目
            List<SampleReportItemDto> reportItemDtos = ObjectUtils
                    .defaultIfNull(sampleReportItemBySampleId.get(dto.getOutsourcingSampleId()), Collections.emptyList());

            // 对应样本结果
            List<SampleResultDto> sampleResultDtos = ObjectUtils
                    .defaultIfNull(sampleResultBySampleId.get(dto.getOutsourcingSampleId()), Collections.emptyList());
            // 一个报告项目一个结果
            Map<String, SampleResultDto> sampleResultByReportItemCode = sampleResultDtos.stream()
                    .collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a));

            // 仪器专业小组下报告项目
            List<InstrumentReportItemDto> instrumentReportItems = ObjectUtils
                    .defaultIfNull(instrumentReportItemDtos.get(dto.getInstrumentGroupId()), Collections.emptyList());

            final Long instrumentId = dto.getInstrumentId();

            final List<InstrumentReportItemDto> sampleInstrumentItems = instrumentReportItems.stream()
                    .filter(e -> Objects.equals(e.getInstrumentId(), instrumentId)).collect(Collectors.toList());

            for (SampleReportItemDto itemDto : reportItemDtos) {
                final SampleResultDto result =
                        sampleResultByReportItemCode.getOrDefault(itemDto.getReportItemCode(), new SampleResultDto());

                InstrumentReportItemDto reportItem;
                // 先取样本仪器的报告项目
                reportItem = sampleInstrumentItems.stream()
                        .filter(e -> Objects.equals(e.getReportItemCode(), itemDto.getReportItemCode())).findFirst()
                        .orElse(null);
                if (Objects.isNull(reportItem)) {
                    reportItem = instrumentReportItems.stream()
                            .filter(e -> Objects.equals(e.getReportItemCode(), itemDto.getReportItemCode())).findFirst()
                            .orElse(null);
                }
                if (Objects.isNull(result) || Objects.isNull(reportItem) || (StringUtils.isBlank(result.getResult())
                        && Objects.equals(reportItem.getIsResultNull(), YesOrNoEnum.NO.getCode()))) {
                    throw new IllegalStateException(
                            String.format("样本号 [%s] 报告项目 [%s] 结果值为空", dto.getSampleNo(), itemDto.getReportItemName()));
                }

                // 判断空参考范围
                emptyReferenceTips(context, dto, result, reportItem);

                if (!NumberUtils.isParsable(result.getResult())) {
                    continue;
                }

                if (BigDecimal.ZERO.compareTo(NumberUtils.toScaledBigDecimal(result.getResult(), 6, RoundingMode.HALF_EVEN)) == 0
                        && Objects.equals(reportItem.getIsResultZero(), YesOrNoEnum.NO.getCode())) {
                    throw new IllegalStateException(
                            String.format("样本号 [%s] 报告项目 [%s] 结果值为零", dto.getSampleNo(), reportItem.getReportItemName()));
                }

            }

        }

        return CONTINUE_PROCESSING;
    }

    /**
     * 判断空参考范围
     */
    private static void emptyReferenceTips(AuditSampleContext context, OutsourcingSampleDto sampleDto, SampleResultDto result, InstrumentReportItemDto reportItem) {
        try {
            // 判断是否空参考范围提示
            if (Objects.nonNull(result) && Objects.nonNull(result.getSampleResultId()) && Objects.nonNull(reportItem) && EmptyReferenceTipEnum.needInterception(reportItem.getEmptyReferenceTip())) {
                final Long instrumentReportItemReferenceId = result.getInstrumentReportItemReferenceId();
                if (Objects.isNull(instrumentReportItemReferenceId) || Objects.equals(NumberUtils.LONG_ZERO, instrumentReportItemReferenceId)) {
                    switch (reportItem.getEmptyReferenceTip()) {
                        case 1:
                            // 禁止审核
                            context.getEmptyReferenceResultForbidden().computeIfAbsent(
                                    sampleDto.getOutsourcingSampleId(), key -> new ArrayList<SampleResultDto>()).add(result);
                            break;
                        case 2:
                            // 审核提示
                            context.getEmptyReferenceResultWarning().computeIfAbsent(
                                    sampleDto.getOutsourcingSampleId(), key -> new ArrayList<SampleResultDto>()).add(result);
                            break;
                    }
                }
            }
        } catch (Exception ignored) {
        }
    }

}
