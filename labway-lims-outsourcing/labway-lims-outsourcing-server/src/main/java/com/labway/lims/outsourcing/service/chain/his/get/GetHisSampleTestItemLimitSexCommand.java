package com.labway.lims.outsourcing.service.chain.his.get;

import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.api.dto.his.HisSampleItem;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <pre>
 * GetHisSampleTestItemLimitSexCommand
 * 检验项目限制性别
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/10 11:55
 */
@Slf4j
@Component
public class GetHisSampleTestItemLimitSexCommand implements Command {

    @DubboReference
    private TestItemService testItemService;

    @Override
    public boolean execute(Context context) throws Exception {
        final GetHisSampleContext from = GetHisSampleContext.from(context);

        final HisGetParam hisGetParam = from.getHisGetParam();
        final OutApplyInfoDTO outApplyInfo = from.getOutApplyInfo();
        final List<HisSampleItem> hisSampleItems = from.getHisSampleItems();

        // 跳过 检验项目限制性别
        if (BooleanUtils.isTrue(hisGetParam.getIgnoreItemLimitSex())) {
            return CONTINUE_PROCESSING;
        }

        Integer patientSex = ObjectUtils.defaultIfNull(outApplyInfo.getPatientSex(), SexEnum.DEFAULT.getCode());
        // 病人性别不是 男/女，跳过
        if (!Arrays.asList(SexEnum.MAN.getCode(), SexEnum.WOMEN.getCode()).contains(patientSex)) {
            return CONTINUE_PROCESSING;
        }

        // 移除前端传过来的重复项目
        final Set<String> hisSampleItemIds = ObjectUtils.defaultIfNull(hisGetParam.getHisSampleItemIds(),
                new HashSet<>());
        hisSampleItems.removeIf(f -> hisSampleItemIds.contains(f.getHisSampleItemId()));

        List<String> testItemCodes = hisSampleItems.stream().map(HisSampleItem::getTestItemCode).collect(Collectors.toList());
        List<TestItemDto> testItemDtos = testItemService.selectByTestItemCodes(testItemCodes, LoginUserHandler.get().getOrgId());

        // 筛选出来 未匹配 限制性别的项目
        List<TestItemDto> testItemsNonmatch = testItemDtos.stream()
                .filter(e -> Objects.nonNull(e.getLimitSex()) && !Objects.equals(SexEnum.DEFAULT.getCode(), e.getLimitSex()))
                .filter(e -> !Objects.equals(patientSex, e.getLimitSex()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(testItemsNonmatch)) {
            String testItemNames = testItemsNonmatch.stream().map(TestItemDto::getTestItemName).collect(Collectors.joining(StringPool.COMMA));
            String sex = SexEnum.getByCode(testItemsNonmatch.stream().findFirst().map(TestItemDto::getLimitSex).get()).getDesc();
            LimsCodeException exception = new LimsCodeException(1122, String.format("【%s】项目限制性别为：%s，是否继续？", testItemNames, sex));
            exception.setData(Map.of(
                    "patientName", StringUtils.defaultString(outApplyInfo.getPatientName()),
                    "outItemNames", testItemNames));
            throw exception;
        }

        return CONTINUE_PROCESSING;
    }

}
