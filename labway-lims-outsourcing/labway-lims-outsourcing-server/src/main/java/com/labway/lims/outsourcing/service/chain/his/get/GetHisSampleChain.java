package com.labway.lims.outsourcing.service.chain.his.get;


import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class GetHis<PERSON><PERSON><PERSON><PERSON><PERSON> extends ChainBase implements InitializingBean {

    @Resource
    private GetHisSampleCheckParamCommand getHisSampleCheckParamCommand;
    @Resource
    private GetHisSampleGetSampleInfoCommand getHisSampleGetSampleInfoCommand;
    @Resource
    private GetHisSamplePostCommand getHisSamplePostCommand;
    @Resource
    private GetHisSampleProjectContrastCommand getHisSampleProjectContrastCommand;
    @Resource
    private GetHisSampleProjectDuplicateCommand getHisSampleProjectDuplicateCommand;
    @Resource
    private GetHisSampleProjectDuplicateSignCommand getHisSampleProjectDuplicateSignCommand;
    @Resource
    private GetHisSampleTestItemLimitSexCommand getHisSampleTestItemLimitSexCommand;
    @Resource
    private GetHisSampleProjectForbiddenCommand getHisSampleProjectForbiddenCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 参数校验
        addCommand(getHisSampleCheckParamCommand);

        // 获取条码信息
        addCommand(getHisSampleGetSampleInfoCommand);

        // 同人同天同项目
        addCommand(getHisSampleProjectDuplicateSignCommand);

        // 项目对照信息
        addCommand(getHisSampleProjectContrastCommand);

        // 禁用项目校验
        addCommand(getHisSampleProjectForbiddenCommand);

        // 重复检验项目
        addCommand(getHisSampleProjectDuplicateCommand);

        // 检验项目限制性别
        addCommand(getHisSampleTestItemLimitSexCommand);

        // 组装信息
        addCommand(getHisSamplePostCommand);

        //
        addCommand(e -> PROCESSING_COMPLETE);
    }
}
