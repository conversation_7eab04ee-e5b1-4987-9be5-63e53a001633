package com.labway.lims.outsourcing.service.chain.result;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.base.api.dto.InstrumentReportItemRemarkDto;
import com.labway.lims.base.api.dto.InstrumentReportItemResultExchangeDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.service.chain.StopWatchContext;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.ObjectUtils;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/3/30 13:38
 */
@Getter
@Setter
public class SaveResultContext extends StopWatchContext {

    /**
     * 申请单
     */
    public static final String APPLY = "APPLY_" + IdUtil.objectId();

    /**
     * 样本
     */
    public static final String OUTSOURCING_SAMPLE = "OUTSOURCING_SAMPLE_" + IdUtil.objectId();

    /**
     * 申请单样本
     */
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    /**
     * 样本检验项目
     */
    public static final String SAMPLE_TEST_ITEMS = "SAMPLE_TEST_ITEMS_" + IdUtil.objectId();

    /**
     * 结果报告项目
     */
    public static final String INSTRUMENT_REPORT_ITEM = "INSTRUMENT_REPORT_ITEM_" + IdUtil.objectId();

    /**
     * 仪器报告项目
     */
    public static final String INSTRUMENT_REPORT_ITEMS = "INSTRUMENT_REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 是否是复查
     */
    public static final String IS_RETESTING = "IS_RETEST_" + IdUtil.objectId();

    /**
     * 当前样本下的报告项目
     */
    public static final String SAMPLE_REPORT_ITEMS = "SAMPLE_REPORT_ITEMS_" + IdUtil.objectId();

    /**
     * 当前结果报告项目
     */
    public static final String SAMPLE_REPORT_ITEM = "SAMPLE_REPORT_ITEM_" + IdUtil.objectId();

    public static final String REPORT_ITEM = "REPORT_ITEM_" + IdUtil.objectId();

    /**
     * 仪器参考值
     */
    public static final String INSTRUMENT_REPORT_REFERENCE = "INSTRUMENT_REPORT_REFERENCE_" + IdUtil.objectId();

    /**
     * 仪器结果备注
     */
    public static final String INSTRUMENT_REPORT_REMARK = "INSTRUMENT_REPORT_REMARK_" + IdUtil.objectId();

    /**
     * 结果转换
     */
    public static final String RESULT_EXCHANGE = "RESULT_EXCHANGE_" + IdUtil.objectId();

    /**
     * 结果提示
     */
    public static final String RESULT_JUDGE = "RESULT_JUDGE_" + IdUtil.objectId();

    /**
     * 结果是否异常
     */
    public static final String RESULT_IS_EXCEPTION = "RESULT_IS_EXCEPTION_" + IdUtil.objectId();
    /**
     * 结果是否危机
     */
    public static final String RESULT_IS_CRITICAL = "RESULT_IS_CRITICAL_" + IdUtil.objectId();
    /**
     * 修改前结果
     */
    public static final String BEFORE_RESULT = "BEFORE_RESULT_" + IdUtil.objectId();
    /**
     * 当前结果
     */
    public static final String CONTEXT_RESULTS = "CONTEXT_RESULTS_" + IdUtil.objectId();

    /**
     * sample_result
     */
    static final String SAMPLE_RESULT = "SAMPLE_RESULT_" + IdUtil.objectId();

    public static final String RESULT_PROVIDER = "RESULT_PROVIDER" + IdUtil.objectId();

    public static final String CHAIN_PROVIDER = "CHAIN_PROVIDER" + IdUtil.objectId();

    /**
     * 当前结果是修改结果还是复查修改结果
     */
    public static final String MODIFY_TYPE = "MODIFY_TYPE_" + IdUtil.objectId();

    /**
     * 用于判断比较参考范围的结果
     */
    static final String RESULT_FOR_COMPARE = "RESULT_FOR_COMPARE_" + IdUtil.objectId();

    /**
     * 结果备注
     */
    static final String RESULT_REMARK = "RESULT_REMARK_" + IdUtil.objectId();


    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    private String reportItemCode;

    /**
     * result
     */
    private String result;

    /**
     * 结果保存来源
     */
    private SaveResultSourceEnum source;

    /**
     * 检查时间
     */
    private Date testDate;

    /**
     * 是否强制更新
     */
    private boolean applySampleUpdate;

    /**
     * 结果描述（结果备注）
     */
    private String resultDesc;


    @Override
    protected String getWatchName() {
        return "结果保存";
    }

    public SaveResultContext() {
        put(CONTEXT_RESULTS, new LinkedHashMap<>(0));
    }

    public static SaveResultContext from(Context context) {
        return (SaveResultContext) context;
    }

    public ApplyDto getApply() {
        return (ApplyDto) get(APPLY);
    }

    public OutsourcingSampleDto getSample() {
        return (OutsourcingSampleDto) get(OUTSOURCING_SAMPLE);
    }

    public ApplySampleDto getApplySample() {
        return (ApplySampleDto) get(APPLY_SAMPLE);
    }

    @SuppressWarnings("unchecked")
    public List<ApplySampleItemDto> getSampleTestItems() {
        return (List<ApplySampleItemDto>) get(SAMPLE_TEST_ITEMS);
    }

    public SampleResultDto getSampleResult() {
        return (SampleResultDto) get(SAMPLE_RESULT);
    }

    public InstrumentReportItemDto getInstrumentReportItem() {
        return (InstrumentReportItemDto) get(INSTRUMENT_REPORT_ITEM);
    }

    public ReportItemDto getReportItem() {
        return (ReportItemDto) get(REPORT_ITEM);
    }

    @SuppressWarnings("unchecked")
    public List<InstrumentReportItemDto> getInstrumentReportItems() {
        return (List<InstrumentReportItemDto>) get(INSTRUMENT_REPORT_ITEMS);
    }

    @SuppressWarnings("unchecked")
    public List<SampleReportItemDto> getSampleReportItems() {
        return (List<SampleReportItemDto>) get(SAMPLE_REPORT_ITEMS);
    }

    public SampleReportItemDto getSampleReportItem() {
        return (SampleReportItemDto) get(SAMPLE_REPORT_ITEM);
    }

    @Nullable
    public InstrumentReportItemReferenceDto getInstrumentReportItemReference() {
        return (InstrumentReportItemReferenceDto) get(INSTRUMENT_REPORT_REFERENCE);
    }

    @Nullable
    public InstrumentReportItemRemarkDto getInstrumentReportItemRemark() {
        return (InstrumentReportItemRemarkDto) get(INSTRUMENT_REPORT_REMARK);
    }

    @SuppressWarnings("unchecked")
    public List<InstrumentReportItemResultExchangeDto> getResultExchange() {
        return (List<InstrumentReportItemResultExchangeDto>) get(RESULT_EXCHANGE);
    }

    public BigDecimal getResultForCompare() {
        return (BigDecimal) get(RESULT_FOR_COMPARE);
    }

    public InstrumentReportItemRemarkDto getResultRemark() {
        return (InstrumentReportItemRemarkDto) get(RESULT_REMARK);
    }

    @Nullable
    public String getResultJudge() {
        return (String) get(RESULT_JUDGE);
    }

    public boolean isException() {
        return Objects.nonNull(get(RESULT_IS_EXCEPTION)) && (boolean) get(RESULT_IS_EXCEPTION);
    }

    public boolean isCritical() {
        return Objects.nonNull(get(RESULT_IS_CRITICAL)) && (boolean) get(RESULT_IS_CRITICAL);
    }


    /**
     * 获取之前的结果
     *
     * @return null 则没有
     */
    @Nullable
    public String getBeforeResult() {
        final Object value = get(BEFORE_RESULT);
        if (Objects.isNull(value)) {
            return null;
        }
        return String.valueOf(value);
    }

    public boolean isRetesting() {
        return containsKey(IS_RETESTING) && (boolean) get(IS_RETESTING);
    }


    public Integer getModifyType() {
        return (Integer) ObjectUtils.defaultIfNull(get(MODIFY_TYPE), 0);
    }

    /**
     * 当前上下文涉及到的报告项目结果，也就是说在整个流程中，如果整个报告项目涉及到新增、修改结果 那么都会在这里
     *
     * @return {sampleId:结果}
     */
    @SuppressWarnings("unchecked")
    public Map<String, String> getContextResults() {
        return (Map<String, String>) get(CONTEXT_RESULTS);
    }


}
