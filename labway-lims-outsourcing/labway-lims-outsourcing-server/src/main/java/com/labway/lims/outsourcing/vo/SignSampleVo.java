package com.labway.lims.outsourcing.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class SignSampleVo {

    /**
     * 样本id
     */
    private Long applySampleId;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 检验项目
     */
    private String testItemName;

    /**
     * 就诊类型
     */
    private String applyType;

    /**
     * 门诊|住院 号
     */
    private String patientVisitCard;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 性别
     */
    private String patientSex;

    /**
     * 年龄
     */
    private Integer patientAge;
    /**
     * 子年龄
     */
    private Integer patientSubage;
    /**
     * 子年龄单位
     */
    private String patientSubageUnit;

    /**
     * 签收人
     */
    private String signUser;

    /**
     * 是否加急
     */
    private Integer urgent;
    /**
     * 签收时间
     */
    private Date signDate;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 样本个数
     */
    private Integer sampleCount;

    /**
     * 科室
     */
    private String dept;

    /**
     * 床号
     */
    private String patientBed;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 样本项目备注（dev1.1.2 血培养要用）
     */
    private String itemRemark;
}
