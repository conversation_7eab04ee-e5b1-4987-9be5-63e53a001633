package com.labway.lims.outsourcing.service.chain.sync;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * 发送审核消息到mq
 */
@Slf4j
@Component
class SyncOutsourcingSampleAuditRabbitMqCommand implements Command {
    @DubboReference
    private RabbitMQService rabbitMQService;

    private static final String ROUTING_KEY = "sample_change_key";

    @Override
    public boolean execute(Context c) throws Exception {
        final SyncOutsourcingContext context = SyncOutsourcingContext.from(c);

        for (OutsourcingSampleDto sample : context.getOutsourcingSamples()) {
            final ApplySampleEventDto event = new ApplySampleEventDto();
            event.setOrgId(LoginUserHandler.get().getOrgId());
            event.setHspOrgId(sample.getHspOrgId());
            event.setHspOrgCode(Optional.ofNullable(context.getApplies().get(sample.getApplyId()))
                    .map(ApplyDto::getHspOrgCode).orElse(StringUtils.EMPTY));
            event.setHspOrgName(sample.getHspOrgName());
            event.setApplyId(sample.getApplyId());
            event.setApplySampleId(sample.getApplySampleId());
            event.setBarcode(sample.getBarcode());
            event.setExtras(Map.of("sampleId", String.valueOf(sample.getOutsourcingSampleId()),
                    "sampleNo", String.valueOf(sample.getSampleNo()),
		            // 项目类型 外送
		            ApplySampleEventDto.ExtrasConstants.ITEM_TYPE, ItemTypeEnum.OUTSOURCING.name()));

            event.setEvent(ApplySampleEventDto.EventType.TwoCheck);

            final String json = JSON.toJSONString(event);
            rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

            log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", sample.getApplySampleId(), sample.getBarcode(), json,
                    RabbitMQService.EXCHANGE, ROUTING_KEY);
        }

        return CONTINUE_PROCESSING;
    }
}
