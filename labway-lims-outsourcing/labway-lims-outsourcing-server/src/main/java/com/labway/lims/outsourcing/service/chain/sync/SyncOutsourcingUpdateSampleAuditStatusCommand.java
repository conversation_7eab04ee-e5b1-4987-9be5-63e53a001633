package com.labway.lims.outsourcing.service.chain.sync;

import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class SyncOutsourcingUpdateSampleAuditStatusCommand implements Command {

    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private OutsourcingSampleService outsourcingSampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SyncOutsourcingContext context = SyncOutsourcingContext.from(c);
        final List<OutsourcingSampleDto> samples = context.getOutsourcingSamples();

        samples.forEach(sample -> {
            String barcode = sample.getBarcode();
            Long outsourcingSampleId = sample.getOutsourcingSampleId();
            Long applySampleId = sample.getApplySampleId();

            final ApplySampleDto dto = new ApplySampleDto();
            dto.setStatus(SampleStatusEnum.AUDIT.getCode());

            final OutsourcingSampleDto sampleDto = new OutsourcingSampleDto();
            sampleDto.setCheckDate(new Date());
            sampleDto.setCheckerId(LoginUserHandler.get().getUserId());
            sampleDto.setCheckerName(LoginUserHandler.get().getNickname());
            if (Objects.nonNull(context.getTesterAuditor(barcode))) {
                OutsourcingUser testerChecker = context.getTesterAuditor(barcode);
                sampleDto.setCheckerId(testerChecker.getCheckerId());
                sampleDto.setCheckerName(testerChecker.getCheckerName());

                dto.setTesterId(testerChecker.getTesterId());
                dto.setTesterName(testerChecker.getTesterName());
            }

            outsourcingSampleService.updateByOutsourcingSampleIds(sampleDto, Lists.newArrayList(outsourcingSampleId));
            applySampleService.updateByApplySampleIds(dto, Lists.newArrayList(applySampleId));
        });

        return CONTINUE_PROCESSING;
    }
}
