package com.labway.lims.outsourcing.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.RetestStatusEnum;
import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.outsourcing.api.dto.OutsourcingStartReTestDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleResultService;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleRetestService;
import com.labway.lims.outsourcing.vo.OutsourcingCountRetestResultVo;
import com.labway.lims.outsourcing.vo.OutsourcingFinishSampleRetestVo;
import com.labway.lims.outsourcing.vo.OutsourcingRetestRecordVo;
import com.labway.lims.outsourcing.vo.OutsourcingStartReTestResultVo;
import com.labway.lims.routine.api.dto.*;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/23 13:08
 */
@RestController
@RequestMapping("/outsourcing-retest")
public class OutsourcingSampleRetestController extends BaseController {
    @DubboReference
    private SampleRetestMainService sampleRetestMainService;
    @Resource
    private OutsourcingSampleResultService outsourcingSampleResultService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private OutsourcingSampleRetestService outsourcingSampleRetestService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SampleRetestItemService sampleRetestItemService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @PostMapping("/start-retest-result")
    public Object startReTestResult(@RequestBody OutsourcingStartReTestResultVo vo) {
        final Long sampleId = vo.getOutsourcingSampleId();

        if (Objects.isNull(sampleId)) {
            throw new IllegalArgumentException("请选择样本");
        }

        final OutsourcingStartReTestDto dto = new OutsourcingStartReTestDto();
        dto.setOutsourcingSampleId(vo.getOutsourcingSampleId());
        dto.setReportItemId(vo.getReportItemId());
        dto.setReportItemCode(vo.getReportItemCode());
        //1.1.3新增回读处理
        dto.setIsReadBack(vo.getIsReadBack());
        dto.setRemark(vo.getRemark());
        dto.setNeedHandleReadBack(vo.isNeedHandleReadBack());

        outsourcingSampleRetestService.startOutsourcingReTestResult(dto);

        return Collections.emptyMap();
    }

    /**
     * 报告项目取消复查
     */
    @PostMapping("/cancel-report-item-retest")
    public Object cancelReportItemReTest(@RequestBody OutsourcingFinishSampleRetestVo vo) {


        final String key = redisPrefix.getBasePrefix() + "cancelReportItemReTest:" + vo.getOutsourcingSampleId();
        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException("正在取消复查中");
        }

        try {
            if (Objects.isNull(vo.getReportItemId())) {
                outsourcingSampleRetestService.cancelRetest(vo.getOutsourcingSampleId());
            } else {
                outsourcingSampleRetestService.cancelRetest(vo.getOutsourcingSampleId(), vo.getReportItemId());
            }
        } finally {
            stringRedisTemplate.delete(key);
        }

        return Collections.emptyMap();

    }


    /**
     * 历史复查记录
     */
    @PostMapping("/retest-records")
    public List<OutsourcingRetestRecordVo> selectRetestRecord(long outsourcingSampleId) {

        final List<SampleReportItemDto> sampleReportItems = sampleReportItemService.selectBySampleId(outsourcingSampleId);
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            return List.of();
        }

        // 获取到复查历史
        final List<SampleRetestMainDto> sampleRetestMains = sampleRetestMainService.selectBySampleId(outsourcingSampleId)
                .stream()
                .filter(e -> Objects.equals(e.getStatus(), SampleRetestStatusEnum.RETEST.getCode()))
                .sorted(Comparator.comparing(SampleRetestMainDto::getSampleRetestMainId))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(sampleRetestMains)) {
            return Collections.emptyList();
        }

        final List<Long> sampleRetestMainIds = sampleRetestMains.stream().map(SampleRetestMainDto::getSampleRetestMainId).collect(Collectors.toList());


        // 原始结果
        final Map<String, SampleResultDto> originalResults = sampleResultService.selectBySampleIds(sampleRetestMainIds).stream()
                .collect(Collectors.toMap(SampleResultDto::getReportItemCode, v -> v, (a, b) -> a));


        // 复查历史
        final Map<Long, List<SampleRetestItemDto>> retestItems = sampleRetestItemService.selectBySampleId(outsourcingSampleId).stream()
                .collect(Collectors.groupingBy(SampleRetestItemDto::getSampleRetestMainId));
        if (MapUtils.isEmpty(retestItems)) {
            return List.of();
        }

        // 复查过的报告项目
        final List<SampleReportItemDto> retestReportItems = sampleReportItems.stream()
                .filter(e -> Objects.equals(e.getIsRetest(), RetestStatusEnum.RETEST.getCode()) || Objects.equals(e.getIsRetest(), RetestStatusEnum.RETESTING.getCode()))
                .collect(Collectors.toList());

        final List<OutsourcingRetestRecordVo> list = new ArrayList<>();
        for (SampleReportItemDto e : retestReportItems) {
            final SampleResultDto originalResult = originalResults.get(e.getReportItemCode());
            final OutsourcingRetestRecordVo vo = new OutsourcingRetestRecordVo();
            vo.setReportItemId(e.getReportItemId());
            vo.setReportItemCode(e.getReportItemCode());
            vo.setReportItemName(e.getReportItemName());
            vo.setPrintSort(e.getPrintSort());
            vo.setOperator(StringUtils.EMPTY);
            vo.setOriginalResult(StringUtils.EMPTY);
            vo.setStatus(ResultStatusEnum.NORMAL.getCode());
            vo.setTestJudge(StringUtils.EMPTY);
            vo.setHistoryResult(new LinkedList<>());

            // 原始结果
            if (Objects.nonNull(originalResult)) {
                vo.setOriginalResult(originalResult.getResult());
                vo.setOperator(originalResult.getCreatorName());
                vo.setStatus(originalResult.getStatus());
                vo.setTestJudge(originalResult.getJudge());
            }

            // 复查历史结果
            for (SampleRetestMainDto main : sampleRetestMains) {
                final List<SampleRetestItemDto> items = retestItems.get(main.getSampleRetestMainId());
                if (Objects.isNull(items)) {
                    continue;
                }
                final RetestRecordDto.RetestResult v = new RetestRecordDto.RetestResult();
                // 从所有复查历史中查询到当前报告项目的
                for (SampleRetestItemDto item : items.stream().filter(k -> Objects.equals(k.getReportItemCode(),
                        e.getReportItemCode())).collect(Collectors.toList())) {
                    v.setResult(item.getResult());
                    v.setStatus(item.getStatus());
                    v.setTestJudge(item.getJudge());
                    v.setSampleRetestMainId(item.getSampleRetestMainId());
                    v.setSampleRetestItemId(item.getSampleRetestItemId());
                }
                vo.getHistoryResult().add(v);
            }

            list.add(vo);

        }


        return list.stream().sorted(Comparator.comparing(OutsourcingRetestRecordVo::getPrintSort)
                        .thenComparing(OutsourcingRetestRecordVo::getReportItemId)
                )
                .collect(Collectors.toList());
    }


    /**
     * 统计复查信息
     */
    @PostMapping("/count-retest-result-info")
    public Object countRetestResultInfo(@RequestParam("outsourcingSampleId") Long outsourcingSampleId) {
        if (Objects.isNull(outsourcingSampleId)) {
            return Collections.emptyMap();
        }

        final CountRetestResultDto dto = sampleRetestMainService.countRetestResultInfo(outsourcingSampleId);

        return JSON.parseObject(JSON.toJSONString(dto), OutsourcingCountRetestResultVo.class);
    }


}
