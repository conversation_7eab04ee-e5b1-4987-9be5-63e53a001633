package com.labway.lims.outsourcing.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class HisSampleVo {

    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 就诊类型
     */
    private String applyTypeName;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 住院|门诊 号
     */
    private String patientVisitCard;
    /**
     * 检验项目
     */
    private List<HisSampleItemVo> hisSampleItems;
    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;
    /**
     * 送检机构
     */
    private String hspOrgName;
    /**
     * 送检机构编码
     */
    private String hspOrgCode;
    /**
     * 送检医生
     */
    private String sendDoctor;
    /**
     *送检医生的身份证
     */
    private String sendDoctorCard;
    /**
     * 检验机构编码
     */
    private String orgCode;
    /**
     * 检验机构名称
     */
    private String orgName;
}
