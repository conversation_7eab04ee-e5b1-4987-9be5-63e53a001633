package com.labway.lims.outsourcing.service.chain.pick.two;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.base.api.dto.InstrumentReportItemCommonPhraseDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemCommonPhraseService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSaveResultDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 常用短语
 */
@Slf4j
@Component
class OutsourcingTwoPickCommonPhraseCommand implements Filter, Command {
    @DubboReference
    private InstrumentReportItemCommonPhraseService instrumentReportItemCommonPhraseService;
    @Resource
    private OutsourcingSampleResultService outsourcingSampleResultService;

    @Override
    public boolean postprocess(Context c, Exception exception) {

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {

        final OutsourcingTwoPickContext context = OutsourcingTwoPickContext.from(c);
        final List<InstrumentReportItemDto> instrumentReportItems = context.getInstrumentReportItems();

        // 获取到报告项目的常用短语
        final List<InstrumentReportItemCommonPhraseDto> instrumentReportItemCommonPhrases = new ArrayList<>(instrumentReportItemCommonPhraseService.selectByInstrumentReportItemIds(instrumentReportItems
                .stream().map(InstrumentReportItemDto::getInstrumentReportItemId).collect(Collectors.toList())));

        // 删除不是默认的
        instrumentReportItemCommonPhrases.removeIf(e -> !Objects.equals(e.getIsDefault(), YesOrNoEnum.YES.getCode()));

        if (CollectionUtils.isEmpty(instrumentReportItemCommonPhrases)) {
            return CONTINUE_PROCESSING;
        }

        final List<OutsourcingSaveResultDto> results = context.getReportItems().stream().map(e -> {
            final InstrumentReportItemCommonPhraseDto instrumentReportItemCommonPhrase = instrumentReportItemCommonPhrases.stream().filter(k -> Objects.equals(e.getReportItemCode(), k.getReportItemCode()))
                    .findFirst().orElse(null);
            if (Objects.isNull(instrumentReportItemCommonPhrase)) {
                return null;
            }

            final OutsourcingSaveResultDto sr = new OutsourcingSaveResultDto();
            sr.setOutsourcingSampleId(context.getApplySampleId());
            sr.setApplySampleId(context.getApplySampleId());
            sr.setApplyId(context.getApply().getApplyId());
            sr.setTestItemId(e.getTestItemId());
            sr.setReportItemId(e.getReportItemId());
            sr.setReportItemCode(e.getReportItemCode());
            sr.setResult(instrumentReportItemCommonPhrase.getContent());
            sr.setInstrumentId(context.getInstrument().getInstrumentId());
            sr.setInstrumentCode(context.getInstrument().getInstrumentCode());
            sr.setInstrumentName(context.getInstrument().getInstrumentName());
            sr.setDate(new Date());

            return sr;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        outsourcingSampleResultService.saveResults(results, SaveResultSourceEnum.DEFAULT_VALUE);

        return CONTINUE_PROCESSING;
    }
}
