package com.labway.lims.outsourcing.vo;

import com.labway.lims.api.enums.routine.ReadBackStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OutsourcingStartReTestResultVo {

    /**
     * 样本id
     */
    private Long outsourcingSampleId;

    /**
     * 样本报告项目id
     */
    private Long reportItemId;


    /**
     * 样本报告项目id
     */
    private String reportItemCode;

    /**
     * 是否需要处理回读事件(回读事件需要根据具体场景进行字段的赋值处理)
     */
    private boolean needHandleReadBack;

    /**
     * 是否回读
     * @see ReadBackStatusEnum
     */
    private Integer isReadBack;

    /**
     * 备注
     */
    private String remark;


}