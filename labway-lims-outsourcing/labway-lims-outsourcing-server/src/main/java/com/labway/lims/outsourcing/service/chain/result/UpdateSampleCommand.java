package com.labway.lims.outsourcing.service.chain.result;

import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/3/30 16:24
 */
@Slf4j
@Component
public class UpdateSampleCommand implements Command {

    @Resource
    private OutsourcingSampleService outsourcingSampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final OutsourcingSampleDto sampleDto = new OutsourcingSampleDto();

        sampleDto.setOutsourcingSampleId(SaveResultContext.from(c).getSample().getOutsourcingSampleId());

        //当只有是仪器传来的时间才去修改 检验时间 (testDate), 否侧就默认和 接收时间(receiveDate) 保持一致
        if (Objects.equals(context.getSource(), SaveResultSourceEnum.MACHINE)) {
            return CONTINUE_PROCESSING;
        }
//20230808各样本的检验时间，为二次分拣的时间，点击【保存】按钮时不更新检验时间，仪器传输结果也不更新检验时间
//        if (Objects.nonNull(context.getTestDate())) {
//            sampleDto.setTestDate(context.getTestDate());
//        } else {
//            sampleDto.setTestDate(context.getSample().getTestDate());
//        }
//        if (BooleanUtils.isNotTrue(outsourcingSampleService.updateByOutsourcingSampleId(sampleDto))) {
//            throw new IllegalStateException("修改样状态失败");
//        }

        return CONTINUE_PROCESSING;
    }
}
