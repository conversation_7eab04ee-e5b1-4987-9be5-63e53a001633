package com.labway.lims.outsourcing.service.chain.retest.cancel;

import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 修改复查项目状态
 */
@Slf4j
@Component
class OutsourcingCancelRetestSampleRetestMainCommand implements Command {
    @DubboReference
    private SampleRetestMainService sampleRetestMainService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingCancelRetestContext context = OutsourcingCancelRetestContext.from(c);
        final List<SampleRetestItemDto> sampleRetestItems = context.getSampleRetestItems();


        // 如果不是最后一个并且所有结果都已经齐全，那么应该是结束复查
        // 如果一个结果为空 那就不能刷数据
        if (sampleRetestItems.stream().anyMatch(e -> StringUtils.isBlank(e.getResult()))) {
            return CONTINUE_PROCESSING;
        }

        // 如果没有正在复查中的项目，那么删除主样本
        if (CollectionUtils.isEmpty(sampleRetestItems)) {
            sampleRetestMainService.deleteBySampleRetestMainId(context.getSampleRetestMain().getSampleRetestMainId());
        } else {
            final SampleRetestMainDto m = new SampleRetestMainDto();
            m.setStatus(SampleRetestStatusEnum.RETEST.getCode());
            m.setSampleRetestMainId(context.getSampleRetestMain().getSampleRetestMainId());
            sampleRetestMainService.updateBySampleRetestMainId(m);
        }


        context.getSampleRetestMain().setStatus(SampleRetestStatusEnum.RETEST.getCode());


        return CONTINUE_PROCESSING;
    }
}
