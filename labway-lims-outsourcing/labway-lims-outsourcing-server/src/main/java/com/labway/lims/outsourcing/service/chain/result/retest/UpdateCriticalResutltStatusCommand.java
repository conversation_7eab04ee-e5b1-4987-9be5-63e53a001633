package com.labway.lims.outsourcing.service.chain.result.retest;

import com.labway.lims.api.enums.routine.SampleCriticalResultStatusEnum;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.service.chain.result.RecalculateRefResultCommand;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2023/5/27 14:22
 */
@Slf4j
@Component
public class UpdateCriticalResutltStatusCommand implements Command {
    @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;
    @Resource
    private RecalculateRefResultCommand recalculateRefResultCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final RetestResultContext context = RetestResultContext.from(c);
        final OutsourcingSampleDto sample = context.getSample();


        if (!context.isComplete()) {
            return CONTINUE_PROCESSING;
        }


        if (recalculateRefResultCommand.isRecalculate(c)) {
            return CONTINUE_PROCESSING;
        }

        final List<SampleCriticalResultDto> sampleCriticalResults = sampleCriticalResultService.selectBySampleId(sample.getOutsourcingSampleId());
        // 移除不是已复查的
        sampleCriticalResults.removeIf(e -> !Objects.equals(e.getStatus(), SampleCriticalResultStatusEnum.UNDER_REVIEW.getCode()));
        if (CollectionUtils.isEmpty(sampleCriticalResults)) {
            return CONTINUE_PROCESSING;
        }


        //如果完成复查那么修改这个样本的危机列表
        final SampleCriticalResultDto dto = new SampleCriticalResultDto();
        dto.setStatus(SampleCriticalResultStatusEnum.REVIEW.getCode());
        sampleCriticalResultService.updateByCriticalValueIds(dto, sampleCriticalResults.stream()
                .map(SampleCriticalResultDto::getCriticalValueId).collect(Collectors.toSet()));

        return CONTINUE_PROCESSING;
    }
}
