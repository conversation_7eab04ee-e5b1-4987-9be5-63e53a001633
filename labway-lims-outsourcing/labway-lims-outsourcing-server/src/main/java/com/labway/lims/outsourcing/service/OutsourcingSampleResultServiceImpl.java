package com.labway.lims.outsourcing.service;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.DeleteSampleResultDetailDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSaveResultDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingStartReTestDto;
import com.labway.lims.outsourcing.api.dto.SaveResultInfoDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleResultService;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.outsourcing.service.chain.result.SaveResultChain;
import com.labway.lims.outsourcing.service.chain.result.SaveResultContext;
import com.labway.lims.outsourcing.service.chain.retest.OutsourcingStartRetestChain;
import com.labway.lims.outsourcing.service.chain.retest.OutsourcingStartRetestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/5/24 14:47
 */
@Slf4j
@DubboService
public class OutsourcingSampleResultServiceImpl implements OutsourcingSampleResultService {
    @Resource
    private SaveResultChain saveResultChain;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @Resource
    private OutsourcingStartRetestChain startRetestChain;
    @Resource
    private OutsourcingSampleService outsourcingSampleService;
    @DubboReference
    private ApplySampleService applySampleService;

    @Override
    public SaveResultInfoDto saveResult(OutsourcingSaveResultDto result, SaveResultSourceEnum source) {


        Long applyId = result.getApplyId();
        Long sampleId = result.getOutsourcingSampleId();

        if (Objects.isNull(applyId) || Objects.isNull(sampleId)) {
            throw new IllegalStateException("缺少必填参数");
        }

        final OutsourcingSampleDto sampleDto = outsourcingSampleService.selectByOutsourcingSampleId(sampleId);
        if (Objects.isNull(sampleDto)) {
            throw new IllegalStateException("样本不存在");
        }
        final ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(sampleDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new IllegalStateException("申请单样本不存在");
        }
        //样本状态 待审核 待二审 已审核 终止（99）
        if (Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())
                || Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.AUDIT.getCode()) ||
                Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {

            throw new IllegalStateException("该样本已审核,不可修改");
        }

        return saveResults(Collections.singletonList(result), source).iterator().next();

    }

    @Override
    public List<SaveResultInfoDto> saveResults(List<OutsourcingSaveResultDto> results, SaveResultSourceEnum source) {

        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }

        final LinkedList<SaveResultInfoDto> saveResultInfos = new LinkedList<>();

        for (OutsourcingSaveResultDto result : results) {
            final SaveResultContext context = new SaveResultContext();
            context.setApplySampleId(result.getApplySampleId());
            context.setResult(result.getResult());
            context.setTestDate(result.getDate());
            context.setReportItemId(result.getReportItemId());
            context.setReportItemCode(result.getReportItemCode());
            context.setSource(source);
            context.setApplySampleUpdate(result.isApplySampleUpdate());
            try {

                if (!saveResultChain.execute(context)) {
                    throw new IllegalStateException("修改结果失败");
                }

                final SaveResultInfoDto sri = new SaveResultInfoDto();
                sri.setResult(context.getResult());
                sri.setBeforeResult(context.getBeforeResult());
                sri.setJudge(context.getResultJudge());
                sri.setException(context.isException());
                sri.setCritical(context.isCritical());
                sri.setRetesting(context.isRetesting());
                sri.setModifyType(context.getModifyType());
                sri.setInstrumentReportItemReferenceDto(context.getInstrumentReportItemReference());

                saveResultInfos.add(sri);

            } catch (RuntimeException e) {
                throw e;
            } catch (Exception e) {
                throw new IllegalStateException(e.getMessage(), e);

            } finally {
                log.info("报告项目 [{}] 保存结果耗时\n{}", result.getReportItemId(), context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
            }
        }

        return saveResultInfos;
    }

    @Override
    public void startRetest(OutsourcingStartReTestDto dto) {
        if (Objects.isNull(dto.getOutsourcingSampleId())) {
            return;
        }

        final OutsourcingStartRetestContext context = new OutsourcingStartRetestContext();
        final OutsourcingStartReTestDto outsourcingStartReTestDto = new OutsourcingStartReTestDto();
        outsourcingStartReTestDto.setOutsourcingSampleId(dto.getOutsourcingSampleId());
        outsourcingStartReTestDto.setReportItemId(dto.getReportItemId());
        outsourcingStartReTestDto.setReportItemCode(dto.getReportItemCode());
        context.put(OutsourcingStartRetestContext.RETESET_INFO, outsourcingStartReTestDto);

        try {
            if (!startRetestChain.execute(context)) {
                throw new IllegalStateException("开始复查失败");
            }

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("开始复查 [{}] 时\n{}", dto.getOutsourcingSampleId(), context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    public List<SaveResultInfoDto> recoveryResult(long sampleId, String sampleNo, ApplySampleDto applySampleDto, List<DeleteSampleResultDetailDto> deleteResultDtoList) {
        return Collections.emptyList();
    }


}
