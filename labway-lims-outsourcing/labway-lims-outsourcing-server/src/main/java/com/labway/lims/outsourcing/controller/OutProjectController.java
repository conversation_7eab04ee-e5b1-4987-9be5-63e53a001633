package com.labway.lims.outsourcing.controller;

import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.QueryItemOutsidePageDTO;
import com.labway.business.center.compare.dto.QueryOrgItemMappingRelationDTO;
import com.labway.business.center.compare.request.DeleteOrgItemMappingRelationRequest;
import com.labway.business.center.compare.request.QueryItemOutsidePageRequest;
import com.labway.business.center.compare.request.QueryOrgItemMappingRelationRequest;
import com.labway.business.center.compare.request.SaveOrgItemMappingRelationRequest;
import com.labway.business.center.compare.service.OutApplyInfoService;
import com.labway.business.center.core.config.Pager;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.outsourcing.vo.DeleteOutProjectContrastVo;
import com.labway.lims.outsourcing.vo.SaveProjectContrastVo;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/out-project")
public class OutProjectController extends BaseController {

    @Resource
    private OutApplyInfoService outApplyInfoService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;


    /**
     * 业务中台的机构编码
     */
    public static final String BUSINESS_CENTER_ORG_CODE = EnvDetector.BUSINESS_CENTER_ORG_CODE;

    @Resource
    private EnvDetector envDetector;
    @Resource
    private Environment environment;

    /**
     * 保存
     */
    @PostMapping("/save-project-contrast")
    public Object saveProjectContrast(@RequestBody SaveProjectContrastVo vo) {
        final List<SaveProjectContrastVo.TestItem> items = vo.getItems();
        if (CollectionUtils.isEmpty(items)) {
            throw new IllegalStateException("检验项目为空");
        }

        final String itemOutsideName = vo.getItemOutsideName();
        if (StringUtils.isBlank(itemOutsideName)) {
            throw new IllegalStateException("外部项目名称为空");
        }

        final String itemOutsideCode = vo.getItemOutsideCode();
        if (StringUtils.isBlank(itemOutsideCode)) {
            throw new IllegalStateException("外部项目编码为空");
        }

        final Long hspOrgId = vo.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalStateException("送检机构id为空");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("送检机构不存在");
        }
        final List<SaveOrgItemMappingRelationRequest.MappingItemTest> testItems =
                items.stream()
                        .map(m -> JSON.parseObject(JSON.toJSONString(m), SaveOrgItemMappingRelationRequest.MappingItemTest.class))
                        .collect(Collectors.toList());

        final SaveOrgItemMappingRelationRequest request = new SaveOrgItemMappingRelationRequest();
        request.setItemOutsideCode(vo.getItemOutsideCode());
        request.setItemOutsideName(vo.getItemOutsideName());
        request.setCustomerCode(hspOrganization.getHspOrgCode());
        request.setCustomerName(hspOrganization.getHspOrgName());
        request.setOrgCode(LoginUserHandler.get().getOrgCode());
        request.setOrgName(LoginUserHandler.get().getOrgName());
        request.setItemTests(testItems);
        request.setOrgCode(getOrgCode());
        //调用人 信息
        LoginUserHandler.User user = LoginUserHandler.get();
        request.setOptUserId(String.valueOf(user.getUserId()));
        request.setOptUserName(user.getNickname());


        log.info("开始调用中台保存外部项目对照接口，参数:{}", JSON.toJSONString(request));
        final Response<Integer> response = outApplyInfoService.saveOrgItemMappingRelation(request);
        log.info("结束调用中台保存外部项目对照接口，响应:{}", JSON.toJSONString(response));

        if (Objects.isNull(response)) {
            throw new IllegalStateException("中台信息返回为空");
        }

        if (!Objects.equals(response.getCode(), NumberUtils.INTEGER_ZERO)) {
            throw new IllegalStateException("调用中台保存对照对照接口失败，原因:" + response.getMsg());
        }


        return Map.of();
    }

    /**
     * 删除对照信息
     */
    @PostMapping("/delete-out-project-contrast")
    public Object deleteOutProjectContrast(@RequestBody DeleteOutProjectContrastVo vo) {
        final Long hspOrgId = vo.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalStateException("送检机构id为空");
        }

        final String itemOutsideCode = vo.getItemOutsideCode();
        if (StringUtils.isBlank(itemOutsideCode)) {
            throw new IllegalStateException("外部项目编码为空");
        }

        final List<String> itemTestCodeList = vo.getItemTestCodeList();
        if (CollectionUtils.isEmpty(itemTestCodeList)) {
            throw new IllegalStateException("检验项目编码为空");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("送检机构不存在");
        }

        final DeleteOrgItemMappingRelationRequest request = new DeleteOrgItemMappingRelationRequest();
        request.setItemOutsideCode(itemOutsideCode);
        request.setCustomerCode(hspOrganization.getHspOrgCode());
        request.setOrgCode(getOrgCode());
        request.setItemTestCodeList(itemTestCodeList);

        //调用人 信息
        LoginUserHandler.User user = LoginUserHandler.get();
        request.setOptUserId(String.valueOf(user.getUserId()));
        request.setOptUserName(user.getNickname());

        log.info("开始调用中台外部项目删除对照接口，参数:{}", JSON.toJSONString(request));
        final Response<Integer> response = outApplyInfoService.deleteOrgItemMappingRelation(request);
        log.info("结束调用中台外部项目删除对照接口，响应:{}", JSON.toJSONString(response));

        if (Objects.isNull(response)) {
            throw new IllegalStateException("中台信息返回为空");
        }

        if (!Objects.equals(response.getCode(), NumberUtils.INTEGER_ZERO)) {
            throw new IllegalStateException("调用中台外部项目删除对照接口失败，原因:" + response.getMsg());
        }

        return Map.of();
    }


    /**
     * 外部项目对照检验项目列表
     */
    @GetMapping("/out-project-contrast")
    public Object outProjectContrast(@RequestParam long hspOrgId, @RequestParam String outProjectCode) {
        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            return List.of();
        }
        if (StringUtils.isBlank(outProjectCode)) {
            throw new IllegalStateException("外部项目编码为空");
        }

        final QueryOrgItemMappingRelationRequest request =
                new QueryOrgItemMappingRelationRequest(getOrgCode(), hspOrganization.getHspOrgCode(), outProjectCode);

        log.info("开始调用中台外部项目对照，参数:{}", JSON.toJSONString(request));
        final Response<List<QueryOrgItemMappingRelationDTO>> response = outApplyInfoService.queryOrgItemMappingRelation(request);
        log.info("结束调用中台外部项目对照，响应:{}", JSON.toJSONString(response));

        if (Objects.isNull(response)) {
            throw new IllegalStateException("中台信息返回为空");
        }

        if (!Objects.equals(response.getCode(), NumberUtils.INTEGER_ZERO)) {
            throw new IllegalStateException("调用中台外部项目对照接口失败，原因:" + response.getMsg());
        }

        return response.getData();

    }


    /**
     * 外部项目 list
     */
    @GetMapping("/list")
    public Object outProjectList(@RequestParam long hspOrgId) {
        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            return List.of();
        }


        final QueryItemOutsidePageRequest request = new QueryItemOutsidePageRequest(getOrgCode(), hspOrganization.getHspOrgCode(), null, null);
        request.setPage(NumberUtils.INTEGER_ONE);
        request.setPageSize(10000);

        log.info("开始调用业务中台外部项目列表接口，参数:{}", JSON.toJSONString(request));
        final Response<Pager<List<QueryItemOutsidePageDTO>>> response = outApplyInfoService.queryItemOutsidePage(request);
        log.info("调用业务中台外部项目列表接口结束，响应:{}", JSON.toJSONString(response));

        if (Objects.isNull(response)) {
            throw new IllegalStateException("调用业务中台外部项目列表接口失败");
        }

        if (!Objects.equals(response.getCode(), NumberUtils.INTEGER_ZERO)) {
            throw new IllegalStateException("调用业务中台外部项目列表接口失败，原因:" + response.getMsg());
        }

        return response.getData();
    }

    public String getOrgCode() {
        final String orgCode = environment.getProperty(BUSINESS_CENTER_ORG_CODE);
        if (StringUtils.isBlank(orgCode)) {
            throw new IllegalArgumentException(String.format("请配置 %s 参数", BUSINESS_CENTER_ORG_CODE));
        }
        return orgCode;
    }
}
