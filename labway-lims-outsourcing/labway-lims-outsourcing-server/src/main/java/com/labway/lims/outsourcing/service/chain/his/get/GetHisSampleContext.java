package com.labway.lims.outsourcing.service.chain.his.get;

import cn.hutool.core.util.IdUtil;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.api.dto.his.HisSample;
import com.labway.lims.outsourcing.api.dto.his.HisSampleItem;
import com.labway.lims.outsourcing.service.chain.StopWatchContext;
import org.apache.commons.chain.Context;

import java.util.List;

public class GetHisSampleContext extends StopWatchContext {

    static final String HIS_SAMPLE = "HIS_SAMPLE" + IdUtil.objectId();
    static final String HIS_SAMPLE_ITEMS = "HIS_SAMPLE_ITEMS" + IdUtil.objectId();
    public static final String HIS_GET_PARAM = "HIS_GET_PARAM" + IdUtil.objectId();
    static final String HSP_ORG = "HSP_ORG" + IdUtil.objectId();
    static final String ORG_CODE = "ORG_CODE" + IdUtil.objectId();
    static final String OUT_APPLY_INFO = "OUT_APPLY_INFO" + IdUtil.objectId();
    static final String LIMS_TEST_ITEM_INFO = "LIMS_TEST_ITEM_INFO" + IdUtil.objectId();


    @SuppressWarnings("unchecked")
    public List<HisSampleItem> getHisSampleItems() {
        return (List<HisSampleItem>) get(HIS_SAMPLE_ITEMS);
    }

    public HisSample getHisSample() {
        return (HisSample) get(HIS_SAMPLE);
    }

    public OutApplyInfoDTO getOutApplyInfo() {
        return (OutApplyInfoDTO) get(OUT_APPLY_INFO);
    }

    public String getOrgCode() {
        return String.valueOf(get(ORG_CODE));
    }

    public HisGetParam getHisGetParam() {
        return (HisGetParam) get(HIS_GET_PARAM);
    }


    public HspOrganizationDto getHspOrganization() {
        return (HspOrganizationDto) get(HSP_ORG);
    }


    public static GetHisSampleContext from(Context context) {
        return (GetHisSampleContext) context;
    }

    @SuppressWarnings("unchecked")
    public List<TestItemDto> getLimsTestItemInfo() {
        return (List<TestItemDto>) get(LIMS_TEST_ITEM_INFO);
    }

    @Override
    protected String getWatchName() {
        return "获取HIS样本";
    }
}
