package com.labway.lims.outsourcing.service.chain.his.sign;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.api.dto.his.HisSignParam;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <pre>
 * SignHisSamplePostCommand
 * 后置处理
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/15 19:25
 */
@Component
public class SignHisSamplePostCommand implements Command {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context context) throws Exception {
        SignHisSampleContext from = SignHisSampleContext.from(context);
        final HisSignParam sign = from.getHisSignParam();

        String signLock = from.getSignLockKey();

        // 签收流程走完，删除缓存
        SpringUtil.getBean(SignHisSampleGetSampleInfoCommand.class)
                .deleteHisSampleCache(JSON.parseObject(JSON.toJSONString(sign), HisGetParam.class));

        // 删除签收锁
        stringRedisTemplate.delete(signLock);

        return CONTINUE_PROCESSING;
    }

}
