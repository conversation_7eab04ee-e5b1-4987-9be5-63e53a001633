package com.labway.lims.outsourcing.service.chain.retest;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/5/26 16:36
 */
@Slf4j
@Component
public class OutsourcingStartRetestChain extends ChainBase implements InitializingBean {
    @Resource
    private OutsourcingStartRetestCheckCanRetestCommand outsourcingStartRetestCheckCanRetestCommand;
    @Resource
    private OutsourcingStartRetestCheckParamCommand outsourcingStartRetestCheckParamCommand;
    @Resource
    private OutsourcingStartRetestGetInfoCommand outsourcingStartRetestGetInfoCommand;
    @Resource
    private OutsourcingStartRetestSaveRetestMainCommand outsourcingStartRetestSaveRetestMainCommand;
    @Resource
    private OutsourcingStartRetestUpdateSampleStatusCommand outsourcingStartRetestUpdateSampleStatusCommand;
    @Resource
    private OutsourcingStartRetestSampleFlowCommand outsourcingStartRetestSampleFlowCommand;
    @Resource
    private OutsourcingStartRetestCheckSaveOriginalCommand outsourcingStartRetestCheckSaveOriginalCommand;
    @Resource
    private OutsourcingStartRetestUpdateCriticalStatusCommand outsourcingStartRetestUpdateCriticalStatusCommand;
    @Resource
    private OutsourcingStartRetestSaveRetestItemCommand outsourcingStartRetestSaveRetestItemCommand;
    @Resource
    private OutsourcingStartRetestUpdateMissItemCommand outsourcingStartRetestUpdateMissItemCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        //检验参数
        addCommand(outsourcingStartRetestCheckParamCommand);

        //获取上下文信息
        addCommand(outsourcingStartRetestGetInfoCommand);

        //检查样本是否可以复查
        addCommand(outsourcingStartRetestCheckCanRetestCommand);

        //保存复查记录表
        addCommand(outsourcingStartRetestSaveRetestMainCommand);

        // 保存要复查哪些记录
        addCommand(outsourcingStartRetestSaveRetestItemCommand);

        //缓存原始结果
        addCommand(outsourcingStartRetestCheckSaveOriginalCommand);

        //修改危急值列表的状态
        addCommand(outsourcingStartRetestUpdateCriticalStatusCommand);

        //修改样本状态以及样本报告项目状态
        addCommand(outsourcingStartRetestUpdateSampleStatusCommand);

        //设置缺项
        addCommand(outsourcingStartRetestUpdateMissItemCommand);

        //条码环节
        addCommand(outsourcingStartRetestSampleFlowCommand);

        addCommand(e -> PROCESSING_COMPLETE);
    }
}
