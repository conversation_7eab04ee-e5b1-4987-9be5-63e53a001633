package com.labway.lims.outsourcing.service.chain.pick.two;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.springframework.stereotype.Component;

/**
 * 校验参数
 */
@Slf4j
@Component
class OutsourcingTwoPickCheckParamCommand implements Filter, Command {
    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context context) throws Exception {
        return CONTINUE_PROCESSING;
    }
}
