package com.labway.lims.outsourcing;

import com.labway.business.center.compare.utils.HttpClientUtil;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Hello world!
 *
 */
@SpringBootApplication
@EnableDubbo
@EnableDiscoveryClient
@Import(HttpClientUtil.class)
@EnableAsync
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
public class LabwayLimsOutsourcingApplication {
    public static void main(String[] args) {
        SpringApplication.run(LabwayLimsOutsourcingApplication.class, args);
    }
}
