package com.labway.lims.outsourcing.vo;

import com.labway.lims.api.enums.apply.ApplySourceEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class HspOrgDateQueryVo {
    /**
     * 机构id
     */
    private Long hspOrgId;
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 签收人名称
     */
    private String signer;

    /**
     * 样本来源
     */
    private ApplySourceEnum source;

}
