package com.labway.lims.outsourcing.service.chain.his.sign;

import com.labway.lims.outsourcing.api.dto.his.HisSignParam;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <pre>
 * SignHisSampleSignLockCommand
 * 签收锁
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/15 17:56
 */
@Component
public class SignHisSampleSignLockCommand implements Command, Filter {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context context) throws Exception {
        SignHisSampleContext from = SignHisSampleContext.from(context);
        HisSignParam sign = from.getHisSignParam();

        String signLock = from.getSignLockKey();
        if (BooleanUtils.isNotTrue(
                stringRedisTemplate.opsForValue().setIfAbsent(signLock, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException(String.format("条码 [%s] 正在签收", sign.getOutBarcode()));
        }

        return CONTINUE_PROCESSING;
    }

    public void deleteSignLock(SignHisSampleContext from) {
        String signLock = from.getSignLockKey();
        stringRedisTemplate.delete(signLock);
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return false;
    }

}
