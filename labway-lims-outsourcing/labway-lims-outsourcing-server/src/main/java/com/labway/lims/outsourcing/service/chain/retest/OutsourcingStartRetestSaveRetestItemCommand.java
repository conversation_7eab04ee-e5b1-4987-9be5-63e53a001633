package com.labway.lims.outsourcing.service.chain.retest;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.RetestModeEnum;
import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 保存检验项目子表
 */
@Slf4j
@Component
public class OutsourcingStartRetestSaveRetestItemCommand implements Filter, Command {
    @DubboReference
    private SampleRetestMainService sampleRetestMainService;

    @DubboReference
    private SampleRetestItemService sampleRetestItemService;


    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingStartRetestContext context = OutsourcingStartRetestContext.from(c);

        final SampleRetestMainDto sampleRetestMain = context.getSampleRetestMains().stream().filter(e -> Objects.equals(e.getStatus(),
                SampleRetestStatusEnum.NORMAL.getCode())).findFirst().orElse(null);
        if (Objects.isNull(sampleRetestMain)) {
            throw new IllegalStateException("获取复查主表失败");
        }


        final LinkedList<Long> ids = snowflakeService.genIds(context.getRetestSampleReportItems().size());
        final List<SampleRetestItemDto> sampleRetestItems = context.getRetestSampleReportItems().stream().map(e -> {
            final SampleRetestItemDto sri = new SampleRetestItemDto();
            sri.setSampleRetestItemId(ids.pop());
            sri.setSampleRetestMainId(sampleRetestMain.getSampleRetestMainId());
            sri.setApplySampleId(sampleRetestMain.getApplySampleId());
            sri.setSampleId(sampleRetestMain.getSampleId());
            sri.setTestItemId(e.getTestItemId());
            sri.setTestItemCode(e.getTestItemCode());
            sri.setTestItemName(e.getTestItemName());
            sri.setReportItemId(e.getReportItemId());
            sri.setReportItemCode(e.getReportItemCode());
            sri.setReportItemName(e.getReportItemName());
            sri.setTestResultType(StringUtils.EMPTY);
            sri.setTestResultTypeCode(StringUtils.EMPTY);
            sri.setRetestMode(RetestModeEnum.DEFAULT.getCode());
            sri.setResult(StringUtils.EMPTY);
            sri.setJudge(StringUtils.EMPTY);
            sri.setRange(StringUtils.EMPTY);
            sri.setStatus(ResultStatusEnum.NORMAL.getCode());
            sri.setRetesterName(LoginUserHandler.get().getNickname());
            sri.setRetesterId(LoginUserHandler.get().getUserId());
            sri.setOrgId(LoginUserHandler.get().getOrgId());
            sri.setOrgName(LoginUserHandler.get().getOrgName());
            sri.setIsDelete(YesOrNoEnum.NO.getCode());
            sri.setCreateDate(new Date());
            sri.setUpdateDate(new Date());
            sri.setCreatorId(LoginUserHandler.get().getUserId());
            sri.setCreatorName(LoginUserHandler.get().getNickname());
            sri.setUpdaterId(LoginUserHandler.get().getUserId());
            sri.setUpdaterName(LoginUserHandler.get().getNickname());
            return sri;
        }).collect(Collectors.toList());

        sampleRetestItemService.addSampleRetestItems(sampleRetestItems);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
