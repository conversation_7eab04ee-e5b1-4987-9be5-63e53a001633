package com.labway.lims.outsourcing.service.chain.retest.cancel;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 取消复查
 */
@Slf4j
@Component
public class OutsourcingCancelRetestChain extends ChainBase implements InitializingBean {

    @Resource
    private OutsourcingCancelRetestFillCommand outsourcingCancelRetestFillCommand;
    @Resource
    private OutsourcingCancelRetestSampleCriticalResultCommand outsourcingCancelRetestSampleCriticalResultCommand;
    @Resource
    private OutsourcingCancelRetestSampleRetestItemCommand outsourcingCancelRetestSampleRetestItemCommand;
    @Resource
    private OutsourcingCancelRetestSampleRetestMainCommand outsourcingCancelRetestSampleRetestMainCommand;
    @Resource
    private OutsourcingCancelRetestWriteSampleResultCommand outsourcingCancelRetestWriteSampleResultCommand;

    @Resource
    private OutsourcingCancelRetestSampleCommand outsourcingCancelRetestSampleCommand;
    @Resource
    private OutsourcingCancelRetestSaveSampleFlowCommand outsourcingCancelRetestSaveSampleFlowCommand;
    @Resource
    private OutsourcingCancelRetestMissItemCommand outsourcingCancelRetestMissItemCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 补上下文
        addCommand(outsourcingCancelRetestFillCommand);

        // 删除复查项目
        addCommand(outsourcingCancelRetestSampleRetestItemCommand);

        // 修改主复查记录状态，如果是取消最后一个那么可能要删除主复查记录
        addCommand(outsourcingCancelRetestSampleRetestMainCommand);

        // 修改危机值状态
        addCommand(outsourcingCancelRetestSampleCriticalResultCommand);

        // 刷结果
        addCommand(outsourcingCancelRetestWriteSampleResultCommand);

        // 修改复查状态
        addCommand(outsourcingCancelRetestSampleCommand);

        // 删除缺项标记
        addCommand(outsourcingCancelRetestMissItemCommand);

        //条码环节
        addCommand(outsourcingCancelRetestSaveSampleFlowCommand);

        // 结束
        addCommand(c -> PROCESSING_COMPLETE);
    }
}
