package com.labway.lims.outsourcing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/6 10:50
 */
@Getter
@Setter
public class OutsourcingResultRecordComparesVo {

    /**
     * 样本ID
     */
    private Long outsourcingSampleId;

    /**
     * 日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date dateStart;

    /**
     * 日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date dateEnd;
}
