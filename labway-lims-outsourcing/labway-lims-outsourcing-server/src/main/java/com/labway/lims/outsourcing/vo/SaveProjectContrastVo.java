package com.labway.lims.outsourcing.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SaveProjectContrastVo {

    /**
     * 外部项目编码
     */
    private String itemOutsideCode;
    /**
     * 外部项目名称
     */
    private String itemOutsideName;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 检验项目
     */
    private List<TestItem> items;

    @Getter
    @Setter
    public static class TestItem {
        /**
         * 检验项目编码
         */
        private String itemTestCode;

        /**
         * 检验项目名称
         */
        private String itemTestName;

    }


}
