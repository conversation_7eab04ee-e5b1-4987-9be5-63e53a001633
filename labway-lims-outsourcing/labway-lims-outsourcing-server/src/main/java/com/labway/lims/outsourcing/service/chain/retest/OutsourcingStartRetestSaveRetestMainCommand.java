package com.labway.lims.outsourcing.service.chain.retest;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/5/26 17:39
 */
@Slf4j
@Component
public class OutsourcingStartRetestSaveRetestMainCommand implements Filter, Command {
    @DubboReference
    private SampleRetestMainService sampleRetestMainService;


    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context c) throws Exception {

        final OutsourcingStartRetestContext context = OutsourcingStartRetestContext.from(c);

        final OutsourcingSampleDto sample = context.getSample();

        final List<SampleRetestMainDto> mains = sampleRetestMainService.selectBySampleId(sample.getOutsourcingSampleId());

        context.put(OutsourcingStartRetestContext.RETEST_MAIN_RECORDS, mains);

        //如果本轮复查未结束不创建主表记录
        if (mains.stream()
                .anyMatch(e -> Objects.equals(e.getStatus(), SampleRetestStatusEnum.NORMAL.getCode()))) {
            return CONTINUE_PROCESSING;
        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        //保存主表
        final SampleRetestMainDto main = new SampleRetestMainDto();
        main.setSampleRetestMainId(snowflakeService.genId());
        main.setApplySampleId(sample.getApplySampleId());
        main.setSampleId(sample.getOutsourcingSampleId());
        main.setStatus(SampleRetestStatusEnum.NORMAL.getCode());
        main.setOrgId(user.getOrgId());
        main.setOrgName(StringUtils.defaultString(user.getOrgName()));
        main.setIsDelete(YesOrNoEnum.NO.getCode());
        main.setCreateDate(new Date());
        main.setCreatorId(user.getUserId());
        main.setCreatorName(StringUtils.defaultString(user.getNickname()));
        main.setUpdaterName(StringUtils.defaultString(user.getNickname()));
        main.setUpdaterId(user.getUserId());
        main.setUpdateDate(new Date());

        sampleRetestMainService.addSampleRetestMain(main);

        context.put(OutsourcingStartRetestContext.RETEST_MAIN_RECORDS, List.of(main));


        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
