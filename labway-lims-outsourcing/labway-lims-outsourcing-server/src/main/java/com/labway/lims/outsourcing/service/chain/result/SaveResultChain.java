package com.labway.lims.outsourcing.service.chain.result;

import com.labway.lims.outsourcing.service.chain.result.retest.CheckIsFcCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @since 2023/3/30 16:10
 */
@Slf4j
@Component
public class SaveResultChain extends ChainBase implements InitializingBean {
    @Resource
    private CheckParamsCommand checkParamsCommand;
    @Resource
    private CheckCanSaveCommand checkCanSaveCommand;
    @Resource
    private FillSampleCommand fillSampleCommand;
    @Resource
    private InstrumentReportReferenceCommand instrumentReportReferenceCommand;
    @Resource
    private NumberResultCommand numberResultCommand;
    @Resource
    private ConvertExceptionResultChain convertExceptionResultChain;
    @Resource
    private RemoveBeforeResultCommand removeBeforeResultCommand;
    @Resource
    private ResultFormatCommand resultFormatCommand;
    @Resource
    private SavePGSQLResultCommand saveMySQLResultCommand;
    @Resource
    private SaveSampleFlowCommand sampleFlowCommand;
    @Resource
    private UpdateSampleCommand updateSampleCommand;
    @Resource
    private YinYangResultCommand yinYangResultCommand;
    @Resource
    private GenCriticalCommand genCriticalCommand;
    @Resource(name = "outsourcing-checkIsFcCommand")
    private CheckIsFcCommand checkIsFcCommand;
    @Resource
    private RecalculateRefResultCommand recalculateRefResultCommand;

    @Resource
    private CriticalCacheTagCommand criticalCacheTagCommand;
    @Resource
    private UpdateMissItemCommand updateMissItemCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        //校验参数
        addCommand(checkParamsCommand);
        //获取样本基本信息
        addCommand(fillSampleCommand);
        //是否复查
        addCommand(checkIsFcCommand);
        //校验参数
        addCommand(checkCanSaveCommand);
        //获取参考范围
        addCommand(instrumentReportReferenceCommand);
        //查询结果数据
        addCommand(numberResultCommand.getDefaultProvider());
        //结果查询数据
        addCommand(numberResultCommand);
        //阴阳结果
        addCommand(yinYangResultCommand);
        //结果格式化
        addCommand(resultFormatCommand);
        //删除之前的结果
        addCommand(removeBeforeResultCommand);
        // 如果结果包特定字符，并且 judge 为空的时候，那么给个上箭头
        addCommand(convertExceptionResultChain);
        //保存
        addCommand(saveMySQLResultCommand);
        // 修改redis缺项的结果
        addCommand(updateMissItemCommand);
        //危机值生成
        addCommand(genCriticalCommand);
        //调用自己
        addCommand(recalculateRefResultCommand.getDefaultChainProvider());
        //计算结果
        addCommand(recalculateRefResultCommand);
        //更新样本结果状态 暂时用不上
//        addCommand(updateSampleCommand);
        // 危急值缓存标记
        addCommand(criticalCacheTagCommand);
        //记录修改日志
        addCommand(sampleFlowCommand);
        //结束
        addCommand(c -> PROCESSING_COMPLETE);
    }
}
