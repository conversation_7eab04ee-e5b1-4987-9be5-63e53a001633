package com.labway.lims.outsourcing.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <pre>
 * OutsourcingConfig
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/2/18 10:05
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties("outsourcing")
public class OutsourcingConfig {

    private Map<String, String> hspOrgCodeMap = new HashMap<>();

}
