package com.labway.lims.outsourcing.service.chain.sync;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedList;


@Component
public class SyncOutsourcingSampleAuditFlowCommand implements Command {

    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    public boolean execute(Context c) throws Exception {
        final SyncOutsourcingContext context = SyncOutsourcingContext.from(c);
        final LinkedList<Long> ids = snowflakeService.genIds(context.getOutsourcingSamples().size());
        final LinkedList<SampleFlowDto> flows = new LinkedList<>();
        for (OutsourcingSampleDto sample : context.getOutsourcingSamples()) {
            final SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(ids.pop());
            sampleFlow.setApplyId(sample.getApplyId());
            sampleFlow.setApplySampleId(sample.getApplySampleId());
            sampleFlow.setBarcode(sample.getBarcode());

            sampleFlow.setOperator(LoginUserHandler.get().getNickname());
            sampleFlow.setOperatorId(LoginUserHandler.get().getUserId());
            sampleFlow.setContent("[调用外部]审核");
            sampleFlow.setOperateCode(BarcodeFlowEnum.SAMPLE_CHECK.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.SAMPLE_CHECK.getDesc());
            sampleFlow.setIsDelete(YesOrNoEnum.NO.getCode());
            sampleFlow.setCreateDate(new Date());
            sampleFlow.setCreatorId(LoginUserHandler.get().getUserId());
            sampleFlow.setCreatorName(LoginUserHandler.get().getNickname());
            sampleFlow.setUpdateDate(new Date());
            sampleFlow.setUpdaterId(LoginUserHandler.get().getUserId());
            sampleFlow.setUpdaterName(LoginUserHandler.get().getNickname());
            flows.add(sampleFlow);
        }

        sampleFlowService.addSampleFlows(flows);

        return CONTINUE_PROCESSING;
    }

}
