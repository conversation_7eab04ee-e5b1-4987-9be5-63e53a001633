package com.labway.lims.outsourcing.service.chain.sync;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "outsourcing.user")
public class OutsourcingUser {

    private Long testerId = -1L;
    private String testerName = "";
    private String testerSign = "";

    private Long checkerId = -1L;
    private String checkerName = "";
    private String checkerSign = "";

}
