package com.labway.lims.outsourcing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class QueryRoutineSamplesVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检验日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateEnd;

    // ---结果页面查询条件
    /**
     * 审核日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date checkDateStart;

    /**
     * 审核日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date checkDateEnd;

    /**
     * 检验者ID
     */
    private Long testerId;

    /**
     * 审核人ID
     */
    private Long checkerId;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目
     */
    private List<Long> testItemIds;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构多选
     */
    private List<Long> hspOrgIds;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 就诊类型
     */
    private String applyType;

    /**
     * 条码号
     */
    private String barcode;

}
