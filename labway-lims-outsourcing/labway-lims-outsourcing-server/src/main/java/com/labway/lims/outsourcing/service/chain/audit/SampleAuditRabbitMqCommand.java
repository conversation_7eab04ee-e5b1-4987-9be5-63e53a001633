package com.labway.lims.outsourcing.service.chain.audit;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发送审核消息到mq
 */
@Slf4j
@Component
class SampleAuditRabbitMqCommand implements Command {
    @DubboReference
    private RabbitMQService rabbitMQService;

    private static final String ROUTING_KEY = "sample_change_key";

    @Override
    public boolean execute(Context c) throws Exception {
        final AuditSampleContext context = AuditSampleContext.from(c);
        Map<Long, ApplyDto> applyDtoByApplyId =
            context.getApplyDtoList().stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        for (OutsourcingSampleDto sample : context.getSamples()) {
            final ApplyDto apply = applyDtoByApplyId.get(sample.getApplyId());
            if (Objects.isNull(apply)) {
                continue;
            }
            final ApplySampleEventDto event = new ApplySampleEventDto();
            event.setOrgId(LoginUserHandler.get().getOrgId());

            event.setHspOrgId(apply.getHspOrgId());
            event.setHspOrgCode(apply.getHspOrgCode());
            event.setHspOrgName(apply.getHspOrgName());
            event.setApplyId(sample.getApplyId());
            event.setApplySampleId(sample.getApplySampleId());
            event.setBarcode(sample.getBarcode());
            event.setExtras(Map.of("sampleId", String.valueOf(sample.getOutsourcingSampleId()),"sampleNo", String.valueOf(sample.getSampleNo())));

            if (Objects.equals(context.getAuditType(), SampleAuditStatusEnum.ONE_CHECK)) {
                event.setEvent(ApplySampleEventDto.EventType.OneCheck);
            } else {
                event.setEvent(ApplySampleEventDto.EventType.TwoCheck);
            }

            final String json = JSON.toJSONString(event);
            rabbitMQService.convertAndSend(RabbitMQService.EXCHANGE, ROUTING_KEY, json);

            log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", sample.getApplySampleId(), sample.getBarcode(), json,
                RabbitMQService.EXCHANGE, ROUTING_KEY);
        }

        return CONTINUE_PROCESSING;
    }
}
