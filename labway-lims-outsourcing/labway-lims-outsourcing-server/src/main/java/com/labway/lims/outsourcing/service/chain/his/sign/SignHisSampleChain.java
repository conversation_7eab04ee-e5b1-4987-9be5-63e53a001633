package com.labway.lims.outsourcing.service.chain.his.sign;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <pre>
 * SignHisSample<PERSON>hain
 * 签收责任链
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/15 17:45
 */
@Component
public class SignHisSampleChain extends ChainBase implements InitializingBean {

    @Resource
    private SignHisSampleCheckParamCommand signHisSampleCheckParamCommand;
    @Resource
    private SignHisSampleSignLockCommand signHisSampleLimitCommand;
    @Resource
    private SignHisSampleGetSampleInfoCommand signHisSampleGetSampleInfoCommand;
    @Resource
    private SignHisSampleAddApplyCommand signHisSampleAddApplyCommand;
    @Resource
    private SignHisSampleBusinessCenterSignCommand signHisSampleBusinessCenterSignCommand;
    @Resource
    private SignHisSamplePostCommand signHisSamplePostCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 参数校验
        addCommand(signHisSampleCheckParamCommand);

        // 签收锁
        addCommand(signHisSampleLimitCommand);

        // 获取申请单信息
        addCommand(signHisSampleGetSampleInfoCommand);

        // 创建申请单
        addCommand(signHisSampleAddApplyCommand);

        // 业务中台签收
        addCommand(signHisSampleBusinessCenterSignCommand);

        // 后置处理
        addCommand(signHisSamplePostCommand);

        // 结束
        addCommand(e -> PROCESSING_COMPLETE);
    }
}
