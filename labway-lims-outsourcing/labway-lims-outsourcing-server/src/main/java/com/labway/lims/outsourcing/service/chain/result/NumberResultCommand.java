package com.labway.lims.outsourcing.service.chain.result;

import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.api.enums.routine.TestResultTypeEnum;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParseException;
import org.springframework.expression.spel.SpelNode;
import org.springframework.expression.spel.ast.VariableReference;
import org.springframework.expression.spel.standard.SpelExpression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 数值结果
 *
 * <AUTHOR>
 * @since 2023/3/30 16:17
 */
@Slf4j
@Component
public class NumberResultCommand implements Command, InitializingBean {
    public static final Map<String, BigDecimal> RESULT_SYMBOL = new HashMap<>();
    private final ExpressionParser expressionParser = new SpelExpressionParser();

    @DubboReference
    private SampleResultService sampleResultService;
    @Resource
    private RecalculateRefResultCommand recalculateRefResultCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();
        // 原始结果，在这个方法里面不要修改！
        final String value = StringUtils.defaultString(context.getResult());
        final InstrumentReportItemReferenceDto reportItemReference = context.getInstrumentReportItemReference();

        if (!Objects.equals(instrumentReportItem.getResultTypeCode(), TestResultTypeEnum.NUMBER.getCode())) {
            return CONTINUE_PROCESSING;
        }

        BigDecimal decimal = null;
        // 是否保留原始结果
        boolean isOriginalResult = Objects.isNull(instrumentReportItem.getDecimalNums()) || instrumentReportItem.getDecimalNums() == 0;
        // 是否是科学计数法
        boolean isScientificNotation = isScientificNotation(value);
        // 科学计数法重新修正为十进制
        try {
            if (isScientificNotation(value)) {
                String v = value;

                if (StringUtils.contains(value, "×10^")) {
                    v = value.replace("×10^", "E");
                }
                if (StringUtils.contains(value, "*10^")) {
                    v = value.replace("*10^", "E");
                }
                v = v.replace("*", "");

                decimal = new BigDecimal(v);
            } else {
                decimal = new BigDecimal(value);
            }
        } catch (Exception e) {
            log.info("转换失败：{}", value);
        }

        // 计算结果，如果保存结果来源是前端页面 那么就不触发计算公式
        // @since 2025-03-10 dev-dongguan 如果是手动同步，那么不触发计算公式
        if ((!Objects.equals(context.getSource(), SaveResultSourceEnum.FRONT) && !Objects.equals(context.getSource(), SaveResultSourceEnum.MANUAL_SYNC))
                && StringUtils.isNotBlank(instrumentReportItem.getCalcFomulation())) {
            decimal = calc(context, decimal);
        }

        // 处理带符号的结果
        final BigDecimal processSymbolResult = processSymbolResult(decimal, value);

        // 保存用于 比较的原始结果
        context.put(SaveResultContext.RESULT_FOR_COMPARE, processSymbolResult);

        // 如果为空 那么就不处理 value 使用原始的
        if (Objects.isNull(processSymbolResult) && Objects.isNull(decimal)) {
            context.setResult(value);
            return CONTINUE_PROCESSING;
        }

        BigDecimal formatResult = processSymbolResult;

        // 非科学计数法 和 保留小数的情况需要进行结果四舍五入
        if (!isScientificNotation && !isOriginalResult) {
            formatResult = processSymbolResult.setScale(instrumentReportItem.getDecimalNums(), RoundingMode.HALF_UP);
        }

        // 有参考范围
        if (Objects.nonNull(reportItemReference)) {
            if (StringUtils.isNoneBlank(
                    reportItemReference.getReferValueMin(), reportItemReference.getReferValueMin(), reportItemReference.getReferValueMinFormula(),
                    reportItemReference.getReferValueMax(), reportItemReference.getReferValueMax(), reportItemReference.getReferValueMaxFormula()
            )) {
                final BigDecimal min = new BigDecimal(reportItemReference.getReferValueMin());
                final BigDecimal max = new BigDecimal(reportItemReference.getReferValueMax());
                // 先清空判断
                context.put(SaveResultContext.RESULT_JUDGE, StringUtils.EMPTY);
                // 如果不是在正常范围
                if (BooleanUtils.isNotTrue(expressionParser.parseExpression(String.format("%s %s %s && %s %s %s",
                        formatResult, reportItemReference.getReferValueMinFormula(), min,
                        formatResult, reportItemReference.getReferValueMaxFormula(), max)).getValue(Boolean.class))) {
                    if (BooleanUtils.isNotTrue(expressionParser.parseExpression(String.format("%s %s %s",
                            formatResult, reportItemReference.getReferValueMinFormula(), min)).getValue(Boolean.class))) {
                        context.put(SaveResultContext.RESULT_JUDGE, TestJudgeEnum.DOWN.name());
                    }
                    if (StringUtils.isBlank(context.getResultJudge()) && BooleanUtils.isNotTrue(expressionParser.parseExpression(String.format("%s %s %s",
                            formatResult, reportItemReference.getReferValueMaxFormula(), max)).getValue(Boolean.class))) {
                        context.put(SaveResultContext.RESULT_JUDGE, TestJudgeEnum.UP.name());
                    }
                }
            } else if (StringUtils.isNoneBlank(
                    reportItemReference.getReferValueMin(), reportItemReference.getReferValueMin(), reportItemReference.getReferValueMinFormula())
            ) {
                final BigDecimal min = new BigDecimal(reportItemReference.getReferValueMin());
                // 先清空判断
                context.put(SaveResultContext.RESULT_JUDGE, StringUtils.EMPTY);

                // 如果不是在正常范围
                if (BooleanUtils.isNotTrue(expressionParser.parseExpression(String.format("%s %s %s",
                        formatResult, reportItemReference.getReferValueMinFormula(), min)).getValue(Boolean.class))) {
                    context.put(SaveResultContext.RESULT_JUDGE, TestJudgeEnum.DOWN.name());
                }

            } else if (StringUtils.isNoneBlank(
                    reportItemReference.getReferValueMax(), reportItemReference.getReferValueMax(), reportItemReference.getReferValueMaxFormula())
            ) {
                final BigDecimal max = new BigDecimal(reportItemReference.getReferValueMax());
                // 先清空判断
                context.put(SaveResultContext.RESULT_JUDGE, StringUtils.EMPTY);

                // 如果不是在正常范围
                if (BooleanUtils.isNotTrue(expressionParser.parseExpression(String.format("%s %s %s",
                        formatResult, reportItemReference.getReferValueMaxFormula(), max)).getValue(Boolean.class))) {
                    context.put(SaveResultContext.RESULT_JUDGE, TestJudgeEnum.UP.name());
                }
            }
        }

        // 超出正常范围后 再去判断是否是危机或异常
        if (StringUtils.isNotBlank(context.getResultJudge()) && (Objects.nonNull(reportItemReference))) {

            // 是否危机
            context.put(SaveResultContext.RESULT_IS_CRITICAL,
                    checkIsCritical(formatResult, instrumentReportItem, reportItemReference));

            // 不是危机的话 那就去看看是否是异常
            if (!context.isCritical()) {
                // 是否异常
                context.put(SaveResultContext.RESULT_IS_EXCEPTION,
                        checkIsException(formatResult, instrumentReportItem, reportItemReference));
            }

        }

        if (Objects.nonNull(decimal)) {
            context.setResult(decimal.toPlainString());
            // 如果是一个科学计数法，无论怎样都显示原始结果
            if (isScientificNotation(value)) {
                context.setResult(value);
            }
        } else {
            context.setResult(value);
        }

        return CONTINUE_PROCESSING;
    }

    /**
     * 判断是否是危机值
     */
    public boolean checkIsCritical(final BigDecimal decimal, final InstrumentReportItemDto cri,
                                   final InstrumentReportItemReferenceDto crf) {

        if (!Objects.equals(cri.getResultTypeCode(), TestResultTypeEnum.NUMBER.getCode())) {
            return false;
        }

        // 有危机值
        if (StringUtils.isNoneBlank(crf.getFatalMax(), crf.getFatalMaxFormula())) {
            if (BooleanUtils.isTrue(expressionParser.parseExpression(String.format("%s %s %s",
                    decimal, crf.getFatalMaxFormula(), new BigDecimal(crf.getFatalMax()))).getValue(Boolean.class))) {
                return true;
            }
        }

        // 有危机值
        if (StringUtils.isNoneBlank(crf.getFatalMin(), crf.getFatalMinFormula())) {
            if (BooleanUtils.isTrue(expressionParser.parseExpression(String.format("%s %s %s",
                    decimal, crf.getFatalMinFormula(), new BigDecimal(crf.getFatalMin()))).getValue(Boolean.class))) {
                return true;
            }
        }

        // 其它类型没有危机值
        return false;
    }

    /**
     * 判断是否是异常值
     */
    public boolean checkIsException(final BigDecimal decimal, final InstrumentReportItemDto cri,
                                    final InstrumentReportItemReferenceDto crf) {

        if (!Objects.equals(cri.getResultTypeCode(), TestResultTypeEnum.NUMBER.getCode())) {
            return false;
        }

        // 有异常提示
        if (StringUtils.isNoneBlank(crf.getExcpWarningMin(), crf.getExcpWarningMax())) {
            if (StringUtils.isNotBlank(crf.getExcpWarningMin())) {
                if (StringUtils.isNoneBlank(crf.getExcpWarningMin(), crf.getExcpWarningMinFormula())) {
                    if (BooleanUtils.isTrue(expressionParser.parseExpression(String.format("%s %s %s",
                            decimal, crf.getExcpWarningMinFormula(), new BigDecimal(crf.getExcpWarningMin()))).getValue(Boolean.class))) {
                        return true;
                    }
                }
            }

            if (StringUtils.isNotBlank(crf.getExcpWarningMax())) {
                if (StringUtils.isNoneBlank(crf.getExcpWarningMax(), crf.getExcpWarningMaxFormula())) {
                    if (BooleanUtils.isTrue(expressionParser.parseExpression(String.format("%s %s %s",
                            decimal, crf.getExcpWarningMaxFormula(), new BigDecimal(crf.getExcpWarningMax()))).getValue(Boolean.class))) {
                        return true;
                    }
                }
            }
        }

        // 其它类型没有危机值
        return false;
    }


    private BigDecimal calc(SaveResultContext context, @Nullable BigDecimal selfValue) {

        final InstrumentReportItemDto instrumentReportItem = context.getInstrumentReportItem();

        final SpelExpression expression;
        try {
            expression = (SpelExpression) expressionParser
                    .parseExpression(StringUtils.replace(instrumentReportItem.getCalcFomulation(), "#", "#_"));
        } catch (IllegalStateException e) {
            throw new IllegalArgumentException("计算公式含非法符号 , 请检查中英文括号、空格等", e);
        } catch (ParseException e) {
            throw new IllegalArgumentException("无法解析计算公式 , 请检查中英文括号、空格等", e);
        }

        final StandardEvaluationContext evaluationContext = new StandardEvaluationContext();

        for (Result e : ((ResultProvider) context.get(SaveResultContext.RESULT_PROVIDER)).get(context)) {
            if (StringUtils.isBlank(e.getSampleReportItemCode())) {
                continue;
            }

            if (!NumberUtils.isParsable(e.getTestResult())) {
                continue;
            }

            evaluationContext.setVariable("_" + e.getSampleReportItemCode(), Double.parseDouble(e.getTestResult()));

        }
        // 如果为空则放入当前修改的
        if (Objects.nonNull(selfValue) || Objects.isNull(evaluationContext.lookupVariable("_" + instrumentReportItem.getReportItemCode()))) {
            // 把当前修改的也放进去
            evaluationContext.setVariable("_" + instrumentReportItem.getReportItemCode(), Objects.nonNull(selfValue) ? selfValue.doubleValue() : null);
        }

        final Set<String> refs = getAllVariableReference(expression)
                .stream()
                .map(VariableReference::toStringAST)
                .collect(Collectors.toSet());

        for (String ref : refs) {
            if (Objects.isNull(evaluationContext.lookupVariable(StringUtils.removeStart(ref, "#")))) {
                log.warn("报告项目 [{}] 依赖的报告项目 [{}] 没有结果，跳过结果计算", instrumentReportItem.getReportItemName(), ref);

                if (recalculateRefResultCommand.isRecalculate(context)) {
                    throw new IllegalStateException(String.format("缺少 [%s] 无法结算结果", ref));
                }
                return selfValue;
            }
        }

        // 添加申请单变量
        evaluationContext.setVariable("_apply", context.getApply());
        // 添加申请单样本变量
        evaluationContext.setVariable("_applySample", context.getApplySample());

        final BigDecimal value;

        try {
            // 计算出来的结果
            value = expression.getValue(evaluationContext, BigDecimal.class);
            if (Objects.isNull(value)) {
                throw new IllegalStateException("计算出来的结果不是数值类型");
            }
        } catch (ArithmeticException e) {
            if (StringUtils.contains(e.getMessage(), "/ by zero")) {
                throw new IllegalStateException(String.format("计算公式 [%s] 出错, 不能除 0", instrumentReportItem.getCalcFomulation()));
            }
            throw e;
        }

        return value;
    }

    /**
     * 处理带符号的结果
     */
    private BigDecimal processSymbolResult(BigDecimal decimal, String value) {
        if (StringUtils.isBlank(value)) {
            return decimal;
        }

        final BigDecimal incNumber = RESULT_SYMBOL.get(String.valueOf(value.charAt(0)));
        if (Objects.isNull(incNumber)) {
            return decimal;
        }

        // 从第一位开始获取，把符号去掉
        final String numberResult = value.substring(NumberUtils.INTEGER_ONE);
        if (!NumberUtils.isParsable(numberResult)) {
            return decimal;
        }

        // 去掉符号
        return new BigDecimal(numberResult).add(incNumber);
    }

    /**
     * 获取到所有引用的变量
     */
    public List<VariableReference> getAllVariableReference(SpelExpression expression) {
        final LinkedList<VariableReference> list = new LinkedList<>();
        doGetAllVariableReference(expression.getAST(), list);
        return list;
    }

    private void doGetAllVariableReference(SpelNode node, List<VariableReference> list) {

        if (node instanceof VariableReference) {
            list.add((VariableReference) node);
        }

        for (int i = 0; i < node.getChildCount(); i++) {
            final SpelNode child = node.getChild(i);
            doGetAllVariableReference(child, list);
        }
    }

    /**
     * 默认提供
     */
    public ResultProvider getDefaultProvider() {
        return new ResultProvider() {
            @Override
            public List<Result> get(SaveResultContext context) {
                final List<SampleResultDto> resultDtos = sampleResultService.selectBySampleId(context.getSample().getOutsourcingSampleId());

                if (CollectionUtils.isEmpty(resultDtos)) {
                    return Collections.emptyList();
                }
                return resultDtos.stream().map(m -> {
                    Result result = new Result();
                    result.setTestResult(m.getResult());
                    result.setSampleReportItemCode(m.getReportItemCode());
                    return result;
                }).collect(Collectors.toList());
            }

            @Override
            public boolean execute(Context c) throws Exception {
                final SaveResultContext from = SaveResultContext.from(c);
                from.put(SaveResultContext.RESULT_PROVIDER, this);
                return CONTINUE_PROCESSING;
            }
        };
    }

    /**
     * 判断结果值是否是科学计数法 目前兼容 1.5×10^3 1.5*10^3 1.5*e3 1.5*E3 1.5e3 1.5E3 类型的科学计数法
     */
    private boolean isScientificNotation(String str) {
        // 如果是一个可以被解析的数字，那么就不是一个科学计数法
        if (NumberUtils.isParsable(str)) {
            return false;
        }
        // 正则表达式用于匹配科学计数法的格式，包括带有幂符号的形式
        final String regex =
                "^([-+]?\\d*\\.?\\d+([eE][-+]?\\d+)?|[-+]?(\\d*\\.?\\d+|\\d+)\\s*[*×]\\s*10[\\^]\\s*[-+]?\\d+|\\d*\\.?\\d+\\s*\\*?×?\\s*[eE][-+]?\\d+)$";
        return Pattern.compile(regex).matcher(str).matches();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        RESULT_SYMBOL.put("<", new BigDecimal("-0.000001"));
        RESULT_SYMBOL.put("＜", new BigDecimal("-0.000001"));

        RESULT_SYMBOL.put(">", new BigDecimal("0.000001"));
        RESULT_SYMBOL.put("＞", new BigDecimal("0.000001"));


        RESULT_SYMBOL.put("≤", BigDecimal.ZERO);
        RESULT_SYMBOL.put("≥", BigDecimal.ZERO);
    }

    public interface ResultProvider extends Command {
        List<Result> get(SaveResultContext context);
    }

    @Getter
    @Setter
    public static final class Result {
        private String testResult;

        private String sampleReportItemCode;
    }
}

