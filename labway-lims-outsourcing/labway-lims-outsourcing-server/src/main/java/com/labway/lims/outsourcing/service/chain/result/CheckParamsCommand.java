package com.labway.lims.outsourcing.service.chain.result;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 校验参数
 *
 * <AUTHOR>
 * @since 2023/3/30 16:14
 */
@Slf4j
@Component
public class CheckParamsCommand implements Command, Filter {
    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        if (Objects.isNull(context.getReportItemId())) {
            throw new IllegalArgumentException("报告项目 ID不能为空");
        }

        if (Objects.isNull(context.getApplySampleId())) {
            throw new IllegalArgumentException("申请单样本 ID不能为空");
        }

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }
}
