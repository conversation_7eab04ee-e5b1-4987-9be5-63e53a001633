package com.labway.lims.outsourcing.service.chain.retest.cancel;

import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 补上下文
 */
@Slf4j
@Component
class OutsourcingCancelRetestFillCommand implements Command {
    @DubboReference
    private SampleRetestMainService sampleRetestMainService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SampleRetestItemService sampleRetestItemService;
    @Resource
    private OutsourcingSampleService outsourcingSampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingCancelRetestContext context = OutsourcingCancelRetestContext.from(c);

        // 查询到复查主表
        final SampleRetestMainDto sampleRetestMain = sampleRetestMainService.selectBySampleId(context.getOutsourcingSampleId()).stream()
                .filter(e -> Objects.equals(e.getStatus(), SampleRetestStatusEnum.NORMAL.getCode())).findFirst().orElse(null);
        if (Objects.isNull(sampleRetestMain)) {
            throw new IllegalStateException("当前状态不是复查中");
        }


        // 找到所有报告项目
        final List<SampleReportItemDto> sampleReportItems = sampleReportItemService.selectBySampleId(context.getOutsourcingSampleId());
        if (CollectionUtils.isEmpty(sampleReportItems)) {
            throw new IllegalStateException("没有找到报告项目");
        }

        if (Objects.nonNull(context.getReportItemId()) && (sampleReportItems.stream().noneMatch(e ->
                Objects.equals(e.getReportItemId(), context.getReportItemId())))) {
            throw new IllegalStateException("没有找到报告项目");
        }

        // 查询到样本
        final OutsourcingSampleDto sample = outsourcingSampleService.selectByOutsourcingSampleId(context.getOutsourcingSampleId());
        if (Objects.isNull(sample)) {
            throw new IllegalStateException("样本不存在");
        }


        // 获取到当前复查记录
        context.put(OutsourcingCancelRetestContext.SAMPLE_RETEST_ITEMS, sampleRetestItemService
                .selectBySampleRetestMainId(sampleRetestMain.getSampleRetestMainId()));

        context.put(OutsourcingCancelRetestContext.SAMPLE_RETEST_MAIN, sampleRetestMain);
        context.put(OutsourcingCancelRetestContext.SAMPLE_REPORT_ITEMS, sampleReportItems);
        context.put(OutsourcingCancelRetestContext.OUTSOURCING_SAMPLE, sample);

        if (Objects.isNull(context.getReportItemId())) {
            context.put(OutsourcingCancelRetestContext.SAMPLE_UN_RETEST_ITEMS, context.getSampleRetestItems()
                    .stream().map(SampleRetestItemDto::getReportItemName).collect(Collectors.toList()));
        } else {
            final SampleReportItemDto reportItem = context.getSampleReportItems()
                    .stream()
                    .filter(e -> Objects.equals(e.getReportItemId(), context.getReportItemId()))
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(reportItem)) {
                context.put(OutsourcingCancelRetestContext.SAMPLE_UN_RETEST_ITEMS, List.of(reportItem.getReportItemName()));
            }
        }

        return CONTINUE_PROCESSING;
    }
}
