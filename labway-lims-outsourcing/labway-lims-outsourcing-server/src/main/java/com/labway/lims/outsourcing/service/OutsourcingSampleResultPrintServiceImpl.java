package com.labway.lims.outsourcing.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.outsourcing.api.dto.QueryOutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.QuerySamplePdfDto;
import com.labway.lims.outsourcing.api.dto.QuerySendOrganizationDto;
import com.labway.lims.outsourcing.api.dto.QueryTestItemDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleResultPrintService;
import com.labway.lims.outsourcing.api.vo.QueryOutsourcingSampleVo;
import com.labway.lims.outsourcing.api.vo.QuerySendOrganizationVo;
import com.labway.lims.outsourcing.api.vo.QueryTestItemVo;
import com.labway.lims.outsourcing.mapper.OutsourcingSampleMapper;
import com.labway.lims.outsourcing.model.TbOutsourcingSample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class OutsourcingSampleResultPrintServiceImpl implements OutsourcingSampleResultPrintService {

    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private SampleReportService sampleReportService;
    @Resource
    private OutsourcingSampleMapper outsourcingSampleMapper;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;


    /**
     * 查询外送机构下拉列表查询
     *
     * @param querySendOrganizationDto
     * @return
     */
    @Override
    public List<QuerySendOrganizationVo> querySendOrganization(QuerySendOrganizationDto querySendOrganizationDto) {

        if (Objects.isNull(querySendOrganizationDto.getIsExport())) {
            throw new IllegalArgumentException("是否外送机构标识不能为空！");
        }

        List<HspOrganizationDto> hspOrganizationDtos = hspOrganizationService.selectAll();

        if (CollectionUtils.isEmpty(hspOrganizationDtos)) {
            return Collections.emptyList();
        }

        List<HspOrganizationDto> resultList = hspOrganizationDtos.stream().filter(e -> Objects.equals(e.getIsExport(), querySendOrganizationDto.getIsExport())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }

        return JSON.parseArray(JSON.toJSONString(resultList), QuerySendOrganizationVo.class);
    }

    /**
     * 查询检验项目下拉列表数据
     *
     * @param queryTestItemDto
     * @return
     */
    @Override
    public List<QueryTestItemVo> queryTestItem(QueryTestItemDto queryTestItemDto) {
        Long orgId = LoginUserHandler.get().getOrgId();
        List<TestItemDto> testItemDtos = testItemService.selectByOrgId(orgId);
        if (CollectionUtils.isEmpty(testItemDtos)) {
            return Collections.emptyList();
        }

        return JSON.parseArray(JSON.toJSONString(testItemDtos), QueryTestItemVo.class);
    }

    /**
     * 外送样本报告单预览pdf
     *
     * @param querySamplePdfDto
     * @return
     */
    @Override
    public List<String> querySamplePdf(QuerySamplePdfDto querySamplePdfDto) {
        return sampleReportService.selectBySampleReportIds(querySamplePdfDto.getSampleIds())
                .stream().map(SampleReportDto::getUrl).collect(Collectors.toList());
    }

    /**
     * 查询外送样本列表信息
     *
     * @param queryOutsourcingSampleDto
     * @return
     */
    @Override
    public List<QueryOutsourcingSampleVo> queryOutsourcingSample(QueryOutsourcingSampleDto queryOutsourcingSampleDto) {
        List<String> testItemCodes = queryOutsourcingSampleDto.getTestItemCodes();

        // 查询外送样本信息
        List<TbOutsourcingSample> tbOutsourcingSamples = getTbOutsourcingSamples(queryOutsourcingSampleDto);
        if (CollectionUtils.isEmpty(tbOutsourcingSamples)) {
            return Collections.emptyList();
        }

        List<Long> applyIds = tbOutsourcingSamples.stream().map(TbOutsourcingSample::getApplyId).collect(Collectors.toList());
        List<Long> applySampleIds = tbOutsourcingSamples.stream().map(TbOutsourcingSample::getApplySampleId).collect(Collectors.toList());


        // 查询样本的申请单信息
        List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);

        // 查询外送样本项目信息
        List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplySampleIds(applySampleIds);

        // 样本信息过滤(如果根据检验项目进行筛选了)
        if (CollectionUtils.isNotEmpty(testItemCodes)) {
            applySampleItemDtos = applySampleItemDtos.stream().filter(e -> testItemCodes.contains(e.getTestItemCode())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(applySampleItemDtos)) {
            return Collections.emptyList();
        }

        List<QueryOutsourcingSampleVo> resultList = new ArrayList<>();
        Map<Long, TbOutsourcingSample> outsourcingSampleMap = tbOutsourcingSamples.stream().collect(Collectors.toMap(TbOutsourcingSample::getApplySampleId, Function.identity(), (o1, o2) -> o2));
        Map<Long, ApplyDto> applyDtoMap = applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity(), (o1, o2) -> o2));
        Map<Long, List<ApplySampleItemDto>> sampleItemGroup = applySampleItemDtos.stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 样本信息组装
        List<Long> resultApplySampleIds = applySampleItemDtos.stream().map(ApplySampleItemDto::getApplySampleId).distinct().collect(Collectors.toList());
        resultApplySampleIds.forEach(e -> {
            TbOutsourcingSample tempOutsourcingSample = outsourcingSampleMap.get(e);
            ApplyDto tempApplyDto = applyDtoMap.get(tempOutsourcingSample.getApplyId());
            List<ApplySampleItemDto> tempApplySampleItemDtos = sampleItemGroup.get(e);

            resultList.add(getQueryOutsourcingSampleVo(tempApplyDto, tempOutsourcingSample, tempApplySampleItemDtos));
        });

        return resultList;
    }


    //==================================================================================================================


    // 样本外送样本信息
    private QueryOutsourcingSampleVo getQueryOutsourcingSampleVo(ApplyDto tempApplyDto, TbOutsourcingSample tempOutsourcingSample, List<ApplySampleItemDto> tempApplySampleItemDtos) {
        QueryOutsourcingSampleVo tempVo = new QueryOutsourcingSampleVo();
        if (Objects.nonNull(tempApplyDto)) {
            BeanUtils.copyProperties(tempApplyDto, tempVo);
        }
        BeanUtils.copyProperties(tempOutsourcingSample, tempVo);
        // dev-1.1.3 显示实验室项目
        String testNames = tempApplySampleItemDtos.stream().map(ApplySampleItemDto::getTestItemName).collect(Collectors.joining(","));
        tempVo.setTestItemNames(testNames);
        return tempVo;
    }

    private List<TbOutsourcingSample> getTbOutsourcingSamples(QueryOutsourcingSampleDto queryOutsourcingSampleDto) {
        Date outSendTimeBegin = queryOutsourcingSampleDto.getOutSendTimeBegin();
        Date outSendTimeEnd = queryOutsourcingSampleDto.getOutSendTimeEnd();
        Date reportTimeBegin = queryOutsourcingSampleDto.getReportTimeBegin();
        Date reportTimeEnd = queryOutsourcingSampleDto.getReportTimeEnd();
        String hspOrgCode = queryOutsourcingSampleDto.getHspOrgCode();
        String targetOrgCode = queryOutsourcingSampleDto.getTargetOrgCode();
        Integer reportStatus = queryOutsourcingSampleDto.getReportStatus();
        Integer isPrint = queryOutsourcingSampleDto.getIsPrint();
        String barcode = queryOutsourcingSampleDto.getBarcode();

        // 查询条件中是否有 回报告时间
        boolean reportFlag = reportTimeBegin != null || reportTimeEnd != null;

        // 默认时间
        Date parse = DefaultDateEnum.DEFAULT_DATE.getDate();
        // 查询外送样本信息
        return outsourcingSampleMapper.selectList(Wrappers.lambdaQuery(TbOutsourcingSample.class)
                .ge(outSendTimeBegin != null, TbOutsourcingSample::getCreateDate, outSendTimeBegin)
                .lt(outSendTimeEnd != null, TbOutsourcingSample::getCreateDate, outSendTimeEnd)
                .ne(reportFlag, TbOutsourcingSample::getCheckDate, parse)
                .ge(reportTimeBegin != null, TbOutsourcingSample::getCheckDate, reportTimeBegin)
                .lt(reportTimeEnd != null, TbOutsourcingSample::getCheckDate, reportTimeEnd)
                .eq(StringUtils.isNotBlank(hspOrgCode), TbOutsourcingSample::getHspOrgId, hspOrgCode)
                .eq(StringUtils.isNotBlank(targetOrgCode), TbOutsourcingSample::getOrgId, targetOrgCode)
                .eq(reportStatus != null && reportStatus == 1, TbOutsourcingSample::getCheckDate, parse)
                .ne(reportStatus != null && reportStatus == 2, TbOutsourcingSample::getCheckDate, parse)
                .eq(isPrint != null, TbOutsourcingSample::getIsPrintList, isPrint)
                .eq(StringUtils.isNotBlank(barcode), TbOutsourcingSample::getBarcode, barcode)
                .eq(TbOutsourcingSample::getIsDelete, YesOrNoEnum.NO.getCode())
        );
    }


}
