package com.labway.lims.outsourcing.service;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.labway.business.center.compare.dto.QueryUnHandoverSampleDto;
import com.labway.business.center.compare.request.CancelSignRequest;
import com.labway.business.center.compare.request.HandoverSampleMainInfoRequest;
import com.labway.business.center.compare.request.QuerySampleInfoForHandoverRequest;
import com.labway.business.center.compare.request.SignOutApplyInfoRequest;
import com.labway.business.center.compare.service.OutApplyInfoService;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.consts.RedisConsts;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.HisTestApplyDto;
import com.labway.lims.apply.api.dto.HisTestApplyItemDto;
import com.labway.lims.apply.api.dto.PreprocessingHandoverRecordDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.PreprocessingHandoverRecordService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.outsourcing.api.dto.BloodCultureDto;
import com.labway.lims.outsourcing.api.dto.SignHisSampleItemDto;
import com.labway.lims.outsourcing.api.dto.his.HisCancelSignParam;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.api.dto.his.HisSample;
import com.labway.lims.outsourcing.api.dto.his.HisSampleItem;
import com.labway.lims.outsourcing.api.dto.his.HisSignParam;
import com.labway.lims.outsourcing.api.service.HisService;
import com.labway.lims.outsourcing.service.chain.his.get.GetHisSampleChain;
import com.labway.lims.outsourcing.service.chain.his.get.GetHisSampleContext;
import com.labway.lims.outsourcing.service.chain.his.sign.SignHisSampleChain;
import com.labway.lims.outsourcing.service.chain.his.sign.SignHisSampleContext;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.api.web.BaseController.TEXTAREA_MAX_LENGTH;

@Slf4j
@DubboService
public class HisServiceImpl implements HisService {

    public static final Integer SUCCESS_CODE = 0;

    /**
     * 业务中台的机构编码
     */
    public static final String BUSINESS_CENTER_ORG_CODE = EnvDetector.BUSINESS_CENTER_ORG_CODE;

    @Resource
    private EnvDetector envDetector;
    @Resource
    private Environment environment;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @DubboReference
    private TestItemService testItemService;

    @Resource
    private OutApplyInfoService outApplyInfoService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @DubboReference
    private ApplyService applyService;

    @DubboReference
    private PreprocessingHandoverRecordService preprocessingHandoverRecordService;


    @DubboReference
    private ApplySampleService applySampleService;

    @DubboReference
    private DictService dictService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private GetHisSampleChain getHisSampleChain;

    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Resource
    private SignHisSampleChain signHisSampleChain;

    @Value("${hisGetExpireMinutes:10}")
    private Long hisGetExpireMinutes;

    @Override
    public HisSample get(HisGetParam hisGetParam) {
        GetHisSampleContext context = new GetHisSampleContext();
        context.put(GetHisSampleContext.HIS_GET_PARAM, hisGetParam);
        try {
            if (!getHisSampleChain.execute(context)) {
                throw new RuntimeException("获取条码信息失败");
            }
            HisSample hisSample = context.getHisSample();
            // 加到缓存中，签收的时候查缓存
            addHisSampleCache(hisSample, hisGetParam);
            return hisSample;
        } catch (LimsCodeException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取送检机构 {} 条码 {} 信息失败 原因: {}", hisGetParam.getHspOrgId(), hisGetParam.getOutBarcode(),
                    e.getMessage());
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApplyInfo sign(HisSignParam sign) {
        final List<SignHisSampleItemDto> items = sign.getItems();
        if (CollectionUtils.isEmpty(items)) {
            throw new IllegalStateException(String.format("条码 [%s] 没有需要签收的项目", sign.getOutBarcode()));
        }
        final Collection<String> getHisSampleItemIds =
                items.stream().map(SignHisSampleItemDto::getHisSampleItemId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(getHisSampleItemIds)) {
            throw new IllegalStateException(String.format("条码 [%s] 没有需要签收的项目", sign.getOutBarcode()));
        }

        final String orgCode = environment.getProperty(BUSINESS_CENTER_ORG_CODE);
        if (StringUtils.isBlank(orgCode)) {
            throw new IllegalArgumentException(String.format("请配置 %s 参数", BUSINESS_CENTER_ORG_CODE));
        }

        final String signLock =
                redisPrefix.getBasePrefix() + "his:sign:" + sign.getHspOrgId() + ":" + sign.getOutBarcode();
        // 加锁在 try 之前, 要不 try 块在加锁之前报错了, finally 删除去无效删除锁
        if (BooleanUtils.isNotTrue(
                stringRedisTemplate.opsForValue().setIfAbsent(signLock, StringUtils.EMPTY, 30, TimeUnit.SECONDS))) {
            throw new IllegalStateException(String.format("条码 [%s] 正在签收", sign.getOutBarcode()));
        }

        try {
            // 查询申请单信息
            final HisSample hisSample = getHisSampleCache(JSON.parseObject(JSON.toJSONString(sign), HisGetParam.class));
            if (Objects.isNull(hisSample)) {
                throw new IllegalStateException("未找到相关条码信息");
            }
            final List<HisSampleItem> hisSampleItems = hisSample.getHisSampleItems();
            if (CollectionUtils.isEmpty(hisSampleItems)) {
                throw new IllegalStateException("未找到相关条码项目信息");
            }

            // 填充自定义码、收费数量
            List<SignHisSampleItemDto> signItems = sign.getItems();
            Map<String, SignHisSampleItemDto> signHisSampleItemDtoMap = signItems.stream().collect(
                    Collectors.toMap(SignHisSampleItemDto::getTestItemCode, e -> e, (a, b) -> b));
            hisSampleItems.forEach(e -> {
                SignHisSampleItemDto signHisSampleItemDto = signHisSampleItemDtoMap.get(e.getTestItemCode());
                if (signHisSampleItemDto != null) {
                    e.setCustomCode(signHisSampleItemDto.getCustomCode());
                    e.setCount(signHisSampleItemDto.getCount());
                }
            });

            // 查询检验项目
            final Map<Long, TestItemDto> testItems = testItemService.selectByTestItemIdsAsMap(items.stream()
                    .map(HisSampleItem::getTestItemId).collect(Collectors.toSet()));

            HisTestApplyDto hisTestApply = new HisTestApplyDto();
            hisTestApply.setOutBarcode(hisSample.getOutBarcode());
            hisTestApply.setHspOrgId(hisSample.getHspOrgId());
            hisTestApply.setPatientName(hisSample.getPatientName());
            hisTestApply.setPatientSex(hisSample.getPatientSex());
            hisTestApply.setPatientBirthday(hisSample.getPatientBirthday());
            hisTestApply.setPatientAge(hisSample.getPatientAge());
            hisTestApply.setPatientSubage(hisSample.getPatientSubage());
            hisTestApply.setPatientSubageUnit(hisSample.getPatientSubageUnit());
            hisTestApply.setUrgent(hisSample.getUrgent());
            hisTestApply
                    .setSampleCount(ObjectUtils.defaultIfNull(hisSample.getSampleCount(), NumberUtils.INTEGER_ZERO));
            hisTestApply.setApplyDate(hisSample.getApplyDate());
            hisTestApply.setSamplingDate(hisSample.getSamplingDate());
            hisTestApply.setPatientVisitCard(hisSample.getPatientVisitCard());
            hisTestApply.setDept(hisSample.getDept());
            hisTestApply.setPatientBed(hisSample.getPatientBed());
            hisTestApply.setClinicalDiagnosis(hisSample.getClinicalDiagnosis());
            hisTestApply.setRemark(hisSample.getRemark());
            hisTestApply.setPatientMobile(hisSample.getPatientMobile());
            hisTestApply.setPatientCard(hisSample.getPatientCard());
            hisTestApply.setPatientCardType(hisSample.getPatientCardType());
            hisTestApply.setSendDoctor(hisSample.getSendDoctor());
            hisTestApply.setPatientAddress(hisSample.getPatientAddress());
            hisTestApply.setApplySource(ApplySourceEnum.HIS);
            hisTestApply.setSupplier("业务中台");
            hisTestApply.setCanSplitSample(sign.isCanSplitSample());
            // 荔湾 LIMS
            hisTestApply.setSampleProperty(StringUtils.defaultIfBlank(hisSample.getSampleProperty(), "正常"));
            hisTestApply.setSamplePropertyCode(StringUtils.defaultIfBlank(hisSample.getSamplePropertyCode(), "00102004-00000000"));

            hisTestApply.setApplyTypeCode(hisSample.getApplyTypeCode());
            hisTestApply.setApplyTypeName(hisSample.getApplyTypeName());

            final List<HisTestApplyItemDto> hisTestApplyItems = hisSampleItems.stream().filter(f -> getHisSampleItemIds.contains(f.getHisSampleItemId())).map(m -> {
                HisTestApplyItemDto hisTestApplyItem = new HisTestApplyItemDto();
                hisTestApplyItem.setOutTestItemName(m.getOutTestItemName());
                hisTestApplyItem.setOutTestItemCode(m.getOutTestItemCode());
                hisTestApplyItem.setTestItemId(m.getTestItemId());
                hisTestApplyItem.setTestItemName(m.getTestItemName());
                hisTestApplyItem.setCount(m.getCount());
                hisTestApplyItem.setUrgent(m.getUrgent());
                hisTestApplyItem.setCustomCode(m.getCustomCode());

                final TestItemDto testItem = testItems.get(m.getTestItemId());
                if (Objects.isNull(testItem)) {
                    throw new IllegalStateException(String.format("检验项目 [%s] 不存在", m.getTestItemName()));
                }

                // 如果是血培养项目
                if (Objects.equals(testItem.getItemType(), ItemTypeEnum.BLOOD_CULTURE.name())) {
                    final SignHisSampleItemDto signHisSampleItem = items.stream().filter(f -> Objects.equals(f.getTestItemCode(), m.getTestItemCode()))
                            .findFirst().orElse(null);

                    if (Objects.isNull(signHisSampleItem)) {
                        throw new IllegalStateException("不存在血培养项目");
                    }

                    // 血培养信息
                    final BloodCultureDto bloodCulture = signHisSampleItem.getBloodCulture();
                    if (Objects.nonNull(bloodCulture)) {
                        hisTestApplyItem.setBloodCulture(JSON.parseObject(JSON.toJSONString(bloodCulture),
                                ApplySampleItemBloodCultureDto.class));
                    }

                    // 备注
                    hisTestApplyItem.setRemark(signHisSampleItem.getRemark());
                }
                return hisTestApplyItem;
            }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(hisTestApplyItems)) {
                throw new IllegalStateException(String.format("条码 [%s] 没有需要签收的项目", sign.getOutBarcode()));
            }

            // 修改管型和样本类型
            outsourcingSignUpdateTubeAndSampleType(sign, hisTestApplyItems);

            hisTestApply.setHisTestApplyItems(hisTestApplyItems);
            hisTestApply.setOriginalOrgCode(hisSample.getOriginalOrgCode());
            hisTestApply.setOriginalOrgName(hisSample.getOriginalOrgName());
            hisTestApply.setIgnoreSameItem(BooleanUtils.isTrue(sign.getIgnoreDuplicateSignItem()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());

	        // ✨feat：【1.1.4】增加标本部位字段 此字段用于签收病理的样本时，落库  https://www.tapd.cn/59091617/prong/stories/view/1159091617001002126?from_iteration_id=1159091617001000242
			hisTestApply.setPatientPart(hisSample.getPatientPart());

            // 检验项目限制性别 忽略校验
            hisTestApply.setIgnoreItemLimitSex(Boolean.TRUE);

            //
            hisTestApply.setSampleSource(sign.getSampleSource());

            if (hisTestApply.getRemark().length() > TEXTAREA_MAX_LENGTH) {
                throw new IllegalStateException(String.format("当前条码备注总长度超过最大限制: [%s]", sign.getOutBarcode()));
            }

            // 添加申请单
            final ApplyInfo applyInfo = applyService.addApply(hisTestApply);

            final List<ApplyInfo.Sample> samples = applyInfo.getSamples();
            if (CollectionUtils.isEmpty(samples)) {
                throw new IllegalStateException("签收失败，样本信息为空");
            }
            try {
                final String limsBarcode = samples.stream().map(ApplyInfo.Sample::getBarcode).findFirst().orElse(null);
                // 通知业务中台保存成功
                final SignOutApplyInfoRequest request = new SignOutApplyInfoRequest();
                request.setHspOrgCode(hisSample.getHspOrgCode());
                request.setReceiveUserCode(String.valueOf(LoginUserHandler.get().getUserId()));
                request.setReceiveUserName(LoginUserHandler.get().getNickname());
                request.setBarcode(hisTestApply.getOutBarcode());
                request.setSignBarcode(limsBarcode);
                request.setSignMainBarcode(applyInfo.getMasterBarcode());
                request.setSignOrgCode(orgCode);
                request.setSignOrgName(LoginUserHandler.get().getOrgName());
                request.setNoType(sign.getNoType());
                // 通知业务中台分条码
                request.setSplitBarcodes(samples.stream().map(ApplyInfo.Sample::getBarcode).collect(Collectors.toList()));

                StopWatch stopWatch = new StopWatch();
                stopWatch.start("签收条码");
                final Response<?> response = outApplyInfoService.signOutApplyInfo(request);
                stopWatch.stop();

                if (Objects.isNull(response)) {
                    throw new IllegalStateException("业务中台未返回签收结果");
                }

                if (!Objects.equals(response.getCode(), SUCCESS_CODE)) {
                    throw new IllegalStateException(String.format(String.format("外部条码 [%s] 签收失败，业务中台返回消息 [%s]",
                            hisTestApply.getOutBarcode(), StringUtils.defaultString(response.getMsg(), "签收错误"))));
                }

                // 签收成功,默认调用交接接口
                if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                    ((HisService) AopContext.currentProxy()).handoverSample(sign, hisSample, orgCode, samples, applyInfo, LoginUserHandler.get());
                }

                log.info("用户 [{}] 专业组 [{}] 条码签收成功，条码号：{}  结果 [{}] 耗时 [{}]", LoginUserHandler.get().getNickname(),
                        LoginUserHandler.get().getGroupName(), hisTestApply.getOutBarcode(), JSON.toJSON(applyInfo),
                        stopWatch.getTotalTimeMillis());

                // 签收成功 后置处理
                postAfterHisSampleSign(JSON.parseObject(JSON.toJSONString(sign), HisGetParam.class));

                return applyInfo;
            } catch (Exception e) {
                // 回滚信息
                applyService.deleteApply(Collections.singleton(applyInfo.getApplyId()));
                log.error("用户 [{}] 专业组 [{}] 条码签收失败，条码号：[{}] ", LoginUserHandler.get().getNickname(),
                        LoginUserHandler.get().getGroupName(), hisTestApply.getOutBarcode());

                throw new IllegalStateException(e.getMessage(), e);
            }
        } catch (Exception e) {
            if (Objects.nonNull(e.getMessage()) && e.getMessage().startsWith(LimsCodeException.class.getName())) {
                String limsCodeMessage = e.getMessage().split(StringPool.NEWLINE)[0];
                if (limsCodeMessage.contains("】，今天已经录入过检验项目：【")) {
                    String message = limsCodeMessage
                            .replace(LimsCodeException.class.getName(), StringPool.EMPTY)
                            .replace("\n", StringPool.EMPTY)
                            .replace("\r", StringPool.EMPTY)
                            .replace(": ", StringPool.EMPTY);
                    LimsCodeException limsCodeException = new LimsCodeException(1119, message);
                    message = message
                            .replace("患者【", StringPool.EMPTY)
                            .replace("】，今天已经录入过检验项目：【", StringPool.HASH)
                            .replace("】，请确认是否再次录入！！！", StringPool.EMPTY);
                    limsCodeException.setData(Map.of(
                            "patientName", message.split(StringPool.HASH)[0],
                            "outItemNames", message.split(StringPool.HASH)[1]));
                    throw limsCodeException;
                } else if (limsCodeMessage.contains("样本类型与性别不匹配")) {
                    throw new LimsCodeException(1114, limsCodeMessage
                            .replace(LimsCodeException.class.getName(), StringPool.EMPTY)
                            .replace(": ", StringPool.EMPTY));
                }
            }
            throw e;
        } finally {
            stringRedisTemplate.delete(signLock);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApplyInfo signChain(HisSignParam sign) {
        try {
            final SignHisSampleContext context = new SignHisSampleContext();
            context.put(SignHisSampleContext.HIS_SIGN_PARAM, sign);
            signHisSampleChain.execute(context);

            return context.getApplyInfo();
        } catch (Exception e) {
            if (Objects.nonNull(e.getMessage()) && e.getMessage().startsWith(LimsCodeException.class.getName())) {
                String limsCodeMessage = e.getMessage().split(StringPool.NEWLINE)[0];
                if (limsCodeMessage.contains("】，今天已经录入过检验项目：【")) {
                    String message = limsCodeMessage
                            .replace(LimsCodeException.class.getName(), StringPool.EMPTY)
                            .replace("\n", StringPool.EMPTY)
                            .replace("\r", StringPool.EMPTY)
                            .replace(": ", StringPool.EMPTY);
                    LimsCodeException limsCodeException = new LimsCodeException(1119, message);
                    message = message
                            .replace("患者【", StringPool.EMPTY)
                            .replace("】，今天已经录入过检验项目：【", StringPool.HASH)
                            .replace("】，请确认是否再次录入！！！", StringPool.EMPTY);
                    limsCodeException.setData(Map.of(
                            "patientName", message.split(StringPool.HASH)[0],
                            "outItemNames", message.split(StringPool.HASH)[1]));
                    throw limsCodeException;
                }
            }
            throw new RuntimeException(e);
        } finally {
            stringRedisTemplate.delete("signLock");
        }
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handoverSample(HisSignParam sign, HisSample hisSample, String orgCode, List<ApplyInfo.Sample> samples, ApplyInfo applyInfo, LoginUserHandler.User user) {
        // 查询样本交接记录,如果已经有了交接记录，那么不重新交接
        if (CollectionUtils.isEmpty(preprocessingHandoverRecordService.selectByBarcode(sign.getOutBarcode(), hisSample.getHspOrgCode()))) {
            try {
                final QuerySampleInfoForHandoverRequest sampleRequest = new QuerySampleInfoForHandoverRequest();
                sampleRequest.setHspOrgCode(hisSample.getHspOrgCode());
                sampleRequest.setBarCodes(List.of(sign.getOutBarcode()));
                sampleRequest.setOrgId(orgCode);
                log.info("开始调用业务中台未交接样本信息查询接口, 参数: [{}]", JSON.toJSONString(sampleRequest));
                final Response<List<QueryUnHandoverSampleDto>> sampleResp = outApplyInfoService.querySampleInfoForHandover(sampleRequest);
                if (Objects.isNull(sampleResp)) {
                    throw new IllegalArgumentException("调用业务中台未交接样本信息查询接口失败，业务中台返回消息为空");
                }

                if (!Objects.equals(sampleResp.getCode(), 0)) {
                    throw new IllegalArgumentException("调用业务中台未交接样本信息查询接口失败，业务中台返回消息：" + sampleResp.getMsg());
                }

                if (CollectionUtils.isEmpty(sampleResp.getData())) {
                    throw new IllegalArgumentException(String.format("条码 %s 不存在", sign.getOutBarcode()));
                }

                final HandoverSampleMainInfoRequest req = new HandoverSampleMainInfoRequest();
                req.setHandoverSampleMainInfo(sampleResp.getData().stream().map(m -> {
                    HandoverSampleMainInfoRequest.HandoverSampleMainInfo handoverSampleMainInfo
                            = new HandoverSampleMainInfoRequest.HandoverSampleMainInfo();
                    handoverSampleMainInfo.setHspOrgCode(hisSample.getHspOrgCode());
                    handoverSampleMainInfo.setFormCode(m.getFormCode());
                    handoverSampleMainInfo.setBarCode(m.getBarcode());
                    return handoverSampleMainInfo;
                }).collect(Collectors.toList()));
                req.setOptId(user.getUsername());
                req.setOptName(user.getNickname());

                log.info("开始调用业务中台交接样本信息接口, 参数: [{}]", JSON.toJSONString(req));
                final Response<?> res = outApplyInfoService.handoverSampleMainInfo(req);
                if (!res.isSuccess()) {
                    log.error("签收条码 [{}] 时调用业务中台交接接口失败. 原因: {}", sign.getOutBarcode(),
                            res.getMsg());
                } else {
                    final QueryUnHandoverSampleDto m = sampleResp.getData().iterator().next();
                    final PreprocessingHandoverRecordDto record = new PreprocessingHandoverRecordDto();
                    record.setHandoverId(snowflakeService.genId());
                    record.setBarcode(StringUtils.defaultString(sign.getOutBarcode()));
                    record.setOutItems(StringUtils.defaultString(m.getOutTestItemName()));
                    record.setPatientName(StringUtils.defaultString(m.getPatientName()));
                    record.setPatientSex(ObjectUtils.defaultIfNull(m.getPatientSex(), SexEnum.DEFAULT.getCode()));
                    record.setPatientAge(ObjectUtils.defaultIfNull(m.getPatientAge(), 0));
                    record.setPatientSubAge(ObjectUtils.defaultIfNull(m.getPatientSubage(), 0));
                    record.setPatientSubAgeUnit(StringUtils.defaultString(m.getPatientSubageUnit()));
                    record.setSamplingDate(m.getTakeSampleTime());
                    record.setHandoverDate(new Date());
                    record.setHandoverPeople(user.getNickname());
                    record.setHandoverPeopleId(user.getUserId());
                    record.setLogisticsPeople(StringUtils.defaultString(m.getStaffName()));
                    record.setTubeName(StringUtils.defaultString(m.getTubeType()));
                    record.setSampleTypeName(StringUtils.defaultString(m.getSampleType()));
                    record.setHspOrgId(hisSample.getHspOrgId());
                    record.setHspOrgName(hisSample.getHspOrgName());
                    record.setHspOrgCode(hisSample.getHspOrgCode());
                    record.setIsDelete(YesOrNoEnum.NO.getCode());
                    record.setCreatorId(user.getUserId());
                    record.setCreatorName(user.getNickname());
                    record.setCreateDate(new Date());
                    record.setUpdateDate(new Date());
                    record.setUpdaterId(user.getUserId());
                    record.setUpdaterName(user.getNickname());
                    record.setOrgId(user.getOrgId());
                    record.setOrgName(user.getOrgCode());
                    record.setOrgName(user.getOrgName());
                    record.setFormCode(StringUtils.defaultString(m.getFormCode()));
                    preprocessingHandoverRecordService.addBatch(Collections.singletonList(record));

                    final LinkedList<Long> ids = snowflakeService.genIds(samples.size());
                    sampleFlowService.addSampleFlows(samples.stream().map(e -> SampleFlowDto.builder()
                            .applyId(applyInfo.getApplyId())
                            .sampleFlowId(ids.pop())
                            .applySampleId(e.getApplySampleId())
                            .operateCode(BarcodeFlowEnum.HANDOVER.name())
                            .operateName(BarcodeFlowEnum.HANDOVER.getDesc())
                            .operatorId(user.getUserId())
                            .operator(user.getNickname())
                            .barcode(e.getBarcode())
                            .content("样本交接")
                            .build()).collect(Collectors.toList()));
                }
            } catch (Exception e) {
                log.error("签收条码 [{}] 时调用业务中台交接接口失败 原因:{}", sign.getOutBarcode(),
                        e.getMessage(), e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelSign(HisCancelSignParam cancelSignParam) {
        final Set<Long> applyIds = cancelSignParam.getApplyIds();
        if (CollectionUtils.isEmpty(applyIds)) {
            throw new IllegalStateException("未找到相关申请单信息");
        }

        final Map<Long, ApplyDto> applyMap = applyService.selectByApplyIdsAsMap(applyIds);
        if (MapUtils.isEmpty(applyMap)) {
            throw new IllegalStateException("未找到相关申请单信息");
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplyIds(applyMap.keySet());
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("未找到相关条码信息");
        }

        Map<Long, Boolean> cancelOnePickMap = new HashMap<>(applySamples.size());
        for (final ApplySampleDto applySample : applySamples) {
            final ApplyDto apply = applyMap.get(applySample.getApplyId());
            if (Objects.isNull(apply)) {
                throw new IllegalStateException(String.format("条码号 [%s] 外部条码 [%s] 未找到相关申请单信息", applySample.getBarcode(),
                        applySample.getOutBarcode()));
            }

            // 已完成二次分拣（包含一审、二审、已归档）不能撤销接收
            if (Objects.equals(applySample.getIsTwoPick(), YesOrNoEnum.YES.getCode())) {
                throw new IllegalStateException("该条码已完成二次分拣，不能取消签收，请取消二次分拣");
            }

            // 样本是不是已经一次分拣过了
            cancelOnePickMap.put(
                    applySample.getApplySampleId(),
                    Objects.equals(applySample.getIsOnePick(), YesOrNoEnum.YES.getCode()));
        }

        // 已完成一次分拣的，取消签收之前先取消分拣，回收试管架先
        applySamples.stream()
                .map(ApplySampleDto::getApplySampleId)
                .forEach(applySampleId -> {
                    if (cancelOnePickMap.getOrDefault(applySampleId, false)) {
                        applySampleService.cancelOnePick(applySampleId);
                    }
                });

        final Set<String> outBarcodes =
                applySamples.stream().map(ApplySampleDto::getOutBarcode).collect(Collectors.toSet());

        StopWatch watch = new StopWatch();
        watch.start("取消签收");

        final CancelSignRequest cancelSignRequest = new CancelSignRequest();
        cancelSignRequest.setBarcodes(new ArrayList<>(outBarcodes));
        cancelSignRequest.setSendOrgCode(cancelSignParam.getHspOrgCode());
        cancelSignRequest.setOperatorId(LoginUserHandler.get().getUserId().toString());
        cancelSignRequest.setOperatorName(LoginUserHandler.get().getNickname());
        cancelSignRequest.setNoType(cancelSignParam.getNoType());
        final Response<?> response = outApplyInfoService.cancelSign(cancelSignRequest);

        watch.stop();

        log.info("调用业务中台取消签收接口， 操作人 [{}] 专业组 [{}] 参数：[{}] 耗时 [{}]", LoginUserHandler.get().getNickname(),
                LoginUserHandler.get().getGroupName(), JSON.toJSON(cancelSignRequest), watch.getTotalTimeMillis());

        if (Objects.isNull(response)) {
            throw new IllegalStateException("取消签收失败，业务中台数据返回错误");
        }

        if (!Objects.equals(response.getCode(), SUCCESS_CODE)) {
            throw new IllegalStateException(String.format("取消签收失败，业务中台返回消息 [%s]", response.getMsg()));
        }

        // 删除申请单+样本+检验项目
        applyService.deleteApply(applySamples.stream().map(ApplySampleDto::getApplyId).collect(Collectors.toSet()));

	    // 取消签收删除实验室的样本交接记录
	    preprocessingHandoverRecordService.deleteByHspOrgCodeAndBarcodes(cancelSignParam.getHspOrgCode(), outBarcodes);

        log.info("用户 [{}] 专业组 [{}] 条码取消签收成功，条码号：[{}] 耗时 [{}]", LoginUserHandler.get().getNickname(),
                LoginUserHandler.get().getGroupName(), outBarcodes, watch.getTotalTimeMillis());
    }

    /**
     * 修改管型和样本类型
     */
    public void outsourcingSignUpdateTubeAndSampleType(HisSignParam sign, List<HisTestApplyItemDto> testItems) {
        List<SignHisSampleItemDto> items = sign.getItems();
        if (CollectionUtils.isNotEmpty(items)) {
            Map<Long, SignHisSampleItemDto> testItemParamMap = items.stream().collect(Collectors.toMap(SignHisSampleItemDto::getTestItemId, Function.identity(), (a, b) -> a));

            for (HisTestApplyItemDto testItem : testItems) {
                Long testItemId = testItem.getTestItemId();
                SignHisSampleItemDto testApplyItemDto = testItemParamMap.get(testItemId);
                if (StringUtils.isNotBlank(testApplyItemDto.getTubeCode())) {
                    testItem.setTubeCode(testApplyItemDto.getTubeCode());
                }
                if (StringUtils.isNotBlank(testApplyItemDto.getTubeName())) {
                    testItem.setTubeName(testApplyItemDto.getTubeName());
                }
                if (StringUtils.isNotBlank(testApplyItemDto.getSampleTypeCode())) {
                    testItem.setSampleTypeCode(testApplyItemDto.getSampleTypeCode());
                }
                if (StringUtils.isNotBlank(testApplyItemDto.getSampleType())) {
                    testItem.setSampleTypeName(testApplyItemDto.getSampleType());
                }
            }
        }
    }

    private void addHisSampleCache(HisSample hisSample, HisGetParam hisGetParam) {
        threadPoolConfig.getPool().submit(() -> {
            String key = hisGetKey(hisGetParam);
            stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(hisSample), hisGetExpireMinutes, TimeUnit.MINUTES);
        });
    }

    private HisSample getHisSampleCache(HisGetParam hisGetParam) {
        // 优先查缓存
        String key = hisGetKey(hisGetParam);
        String cached = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(cached)) {
            log.info("样本签收，从缓存中查询到数据 参数 [{}] 结果 [{}]", JSON.toJSONString(hisGetParam), cached);
            return JSON.parseObject(cached, HisSample.class);
        }
        hisGetParam.setIgnoreNoMappingItem(YesOrNoEnum.YES.getCode());
        // 缓存没有查到
        return get(hisGetParam);
    }

    // 签收成功后置处理
    private void postAfterHisSampleSign(HisGetParam hisGetParam) {
        threadPoolConfig.getPool().submit(() -> {
            // 签收成功删除 扫码的样本缓存
            String hisGetKey = hisGetKey(hisGetParam);
            stringRedisTemplate.delete(hisGetKey);
        });
    }

    private String hisGetKey(HisGetParam hisGetParam) {
        return redisPrefix.getBasePrefix() + RedisConsts.HIS_GET + hisGetParam.getHspOrgId() + hisGetParam.getOutBarcode();
    }

}
