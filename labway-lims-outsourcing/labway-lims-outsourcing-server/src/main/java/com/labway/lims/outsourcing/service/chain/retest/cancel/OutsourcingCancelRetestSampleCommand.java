package com.labway.lims.outsourcing.service.chain.retest.cancel;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.SampleRetestStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 修改样本状态
 */
@Slf4j
@Component
class OutsourcingCancelRetestSampleCommand implements Command {
    @DubboReference
    private ApplySampleService applySampleService;

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingCancelRetestContext context = OutsourcingCancelRetestContext.from(c);

        // 主表没有结束复查那就还不能修改复查状态
        if (!Objects.equals(context.getSampleRetestMain().getStatus(),
                SampleRetestStatusEnum.RETEST.getCode())) {
            return CONTINUE_PROCESSING;
        }

        final ApplySampleDto applySample = new ApplySampleDto();
        applySample.setApplySampleId(context.getOutsourcingSample().getApplySampleId());
        applySample.setStatus(SampleStatusEnum.NOT_AUDIT.getCode());
        applySampleService.updateByApplySampleId(applySample);

        return CONTINUE_PROCESSING;
    }
}
