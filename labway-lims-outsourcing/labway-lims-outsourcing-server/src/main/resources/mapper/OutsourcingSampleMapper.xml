<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.outsourcing.mapper.OutsourcingSampleMapper">
    <update id="updateByOutsourcingSampleIds">
        update tb_outsourcing_sample
        <set>
            <if test="outsourcingSample.oneCheckDate != null">
                one_check_date = #{outsourcingSample.oneCheckDate},
            </if>
            <if test="outsourcingSample.oneCheckerName != null">
                one_checker_name = #{outsourcingSample.oneCheckerName},
            </if>
            <if test="outsourcingSample.oneCheckerId != null">
                one_checker_id = #{outsourcingSample.oneCheckerId},
            </if>
            <if test="outsourcingSample.checkDate != null">
                check_date = #{outsourcingSample.checkDate},
            </if>
            <if test="outsourcingSample.checkerName != null">
                checker_name = #{outsourcingSample.checkerName},
            </if>
            <if test="outsourcingSample.checkerId != null">
                checker_id = #{outsourcingSample.checkerId},
            </if>
            <if test="outsourcingSample.testDate != null">
                test_date = #{outsourcingSample.testDate},
            </if>
            <if test="outsourcingSample.isPrintList != null">
                is_print_list = #{outsourcingSample.isPrintList},
            </if>
            <if test="outsourcingSample.printListDate != null">
                print_list_date = #{outsourcingSample.printListDate},
            </if>
        </set>
        where outsourcing_sample_id in
        <foreach collection="outsourcingSampleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectByCreateDate" resultType="com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto">
        select
            *
        from tb_outsourcing_sample tos
        where tos.is_delete = 0
        and tos.create_date >= #{beginCreateDate}
        and tos.create_date &lt;= #{endCreateDate}
        and tos.org_id = #{orgId}
    </select>

    <select id="selectByCreateDateAndGroup" resultType="com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto">
        select
            *
        from tb_outsourcing_sample tos
        where tos.is_delete = 0
        and tos.create_date >= #{beginCreateDate}
        and tos.create_date &lt;= #{endCreateDate}
        and tos.org_id = #{orgId}
        <if test="groupId != null">
            and tos.group_id = #{groupId}
        </if>
    </select>

    <update id="updateByApplySampleIds">
        update tb_outsourcing_sample
        <set>
            <if test="outsourcingSample.checkDate != null">
                check_date = #{outsourcingSample.checkDate},
            </if>
            <if test="outsourcingSample.checkerName != null">
                checker_name = #{outsourcingSample.checkerName},
            </if>
            <if test="outsourcingSample.checkerId != null">
                checker_id = #{outsourcingSample.checkerId},
            </if>
            <if test="outsourcingSample.testDate != null">
                test_date = #{outsourcingSample.testDate},
            </if>
            <if test="outsourcingSample.isPrintList != null">
                is_print_list = #{outsourcingSample.isPrintList},
            </if>
            <if test="outsourcingSample.printListDate != null">
                print_list_date = #{outsourcingSample.printListDate},
            </if>
        </set>
        where apply_sample_id in
        <foreach collection="applySampleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectByCondition" resultType="com.labway.lims.outsourcing.api.dto.BetterOutsourcingSampleDto">
        select tos.*,ta.patient_name,tas.status
        from tb_outsourcing_sample tos
        inner join tb_apply ta on tos.apply_id = ta.apply_id
        inner join tb_apply_sample tas on tos.apply_sample_id = tas.apply_sample_id
        <trim prefix="where" suffixOverrides="and">
            tos.is_delete = 0 and tas.status != 99 and tas.is_delete = 0 and
            <if test="condition.sendDateStart != null">
                tos.create_date &gt;= #{condition.sendDateStart} and
            </if>
            <if test="condition.sendDateEnd != null">
                tos.create_date &lt;= #{condition.sendDateEnd} and
            </if>
            <if test="condition.hspOrgId != null">
                tos.hsp_org_id = #{condition.hspOrgId} and
            </if>
            <if test="condition.status != null and condition.status != -1">
                tas.status = #{condition.status} and
            </if>
            <if test="condition.status != null">
                tos.export_org_id = #{condition.exportOrgId} and
            </if>
            <if test="condition.barcode != null and condition.barcode != ''">
                tos.barcode = #{condition.barcode} and
            </if>
            <if test="condition.patientName != null and condition.patientName != ''">
                ta.patient_name like concat('%',#{condition.patientName},'%') and
            </if>
        </trim>
    </select>
</mapper>
