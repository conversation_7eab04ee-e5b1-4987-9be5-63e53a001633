package com.labway.lims.outsourcing.service;

import com.labway.lims.outsourcing.api.dto.SyncChangzhouOutsourcingDto;
import com.labway.lims.outsourcing.api.service.SyncChangzhouOutsourcingService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <pre>
 * SyncChangzhouOutsourcingServiceImplTest
 * SyncChangzhouOutsourcingService 测试
 * </pre>
 *
 * <AUTHOR>
 * @since 2023/11/27 16:58
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles(profiles = "test")
public class SyncChangzhouOutsourcingServiceImplTest {

    @Resource
    private SyncChangzhouOutsourcingService syncChangzhouOutsourcingService;

    @Test
    public void syncChangzhouOutsourcing() {
        SyncChangzhouOutsourcingDto request = new SyncChangzhouOutsourcingDto();
        request.setExportOrgIds(List.of(136051886548817935L));
        syncChangzhouOutsourcingService.syncChangzhouOutsourcing(request);

    }

}