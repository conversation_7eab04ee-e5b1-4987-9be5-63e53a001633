package com.labway.lims.outsourcing.service;

import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class OutsourcingSampleServiceImplTest {
    @Resource
    private OutsourcingSampleService outsourcingSampleService;

    @Test
    public void test() {
        final OutsourcingSampleDto outsourcingSample = outsourcingSampleService.selectByOutsourcingSampleId(-Math.abs(RandomUtils.nextLong()));
        Assert.assertNull(outsourcingSample);
    }
}