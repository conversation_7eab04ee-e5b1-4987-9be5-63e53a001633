package com.labway.lims.outsourcing.api.dto;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class QueryOutsourcingSampleResultDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 送检机构Id
     */
    private Long hspOrgId;

    /**
     * 外送机构id
     */
    private Long exportOrgId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 用户姓名
     */
    private String patientName;


    /**
     * 外送日期开始
     */
    private Date sendDateStart;

    /**
     * 外送日期结束
     */
    private Date sendDateEnd;

    /**
     * 样本状态 待审核 待复查 待二审 已审核 终止（99）
     *
     * @see SampleStatusEnum
     */
    private Integer status;

}
