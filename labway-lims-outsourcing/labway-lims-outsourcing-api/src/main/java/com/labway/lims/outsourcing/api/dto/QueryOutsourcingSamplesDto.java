package com.labway.lims.outsourcing.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class QueryOutsourcingSamplesDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * orgId
     */
    private Long orgId;

    /**
     * groupId
     */
    private Long groupId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;


    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 检验日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateEnd;

    /**
     * 条码
     */
    private String barcode;


    /**
     * 外送是否已确认。 默认已确认
     */
    private Boolean outsourcingIsConfirm;
}
