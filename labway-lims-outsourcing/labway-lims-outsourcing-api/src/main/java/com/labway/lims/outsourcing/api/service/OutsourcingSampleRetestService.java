package com.labway.lims.outsourcing.api.service;

import com.labway.lims.outsourcing.api.dto.OutsourcingStartReTestDto;

/**
 * <AUTHOR>
 * @since 2023/5/29 14:12
 */
public interface OutsourcingSampleRetestService {

    /**
     * 外送检验开始复查
     */
    void startOutsourcingReTestResult(OutsourcingStartReTestDto dto);

    /**
     * 取消复查
     */
    void cancelRetest(long outsourcingSampleId);

    /**
     * 取消复查
     */
    void cancelRetest(long outsourcingSampleId, long reportItemId);

}
