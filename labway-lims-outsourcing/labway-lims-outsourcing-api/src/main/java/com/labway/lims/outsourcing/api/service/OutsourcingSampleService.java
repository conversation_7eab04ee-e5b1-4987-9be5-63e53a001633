package com.labway.lims.outsourcing.api.service;

import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplyUpdateBeforeCheckTipDto;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.outsourcing.api.dto.BetterOutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleAuditDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleCancelAuditDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleCondition;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleTwoUnPickInfoDto;
import com.labway.lims.outsourcing.api.dto.QueryOutsourcingSampleResultDto;
import com.labway.lims.outsourcing.api.dto.QueryOutsourcingSamplesDto;
import com.labway.lims.outsourcing.api.dto.SyncOutsourcingSampleResultDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 外送样本
 */
public interface OutsourcingSampleService {

    /**
     * 根据ID查询
     */
    @Nullable
    OutsourcingSampleDto selectByOutsourcingSampleId(long outsourcingSampleId);

    List<OutsourcingSampleDto> selectByOutsourcingSampleIds(Collection<Long> outsourcingSampleIds);

    /**
     * 根据申请单样本ID查询
     */
    @Nullable
    OutsourcingSampleDto selectByApplySampleId(long applySampleId);

    /**
     * 根据申请单样本ID查询
     */
    List<OutsourcingSampleDto> selectByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据创建日期查询
     */
    List<OutsourcingSampleDto> selectByCreateDate(Date beginCreateDate, Date endCreateDate, long orgId);

    /**
     * 根据创建日期和专业组（多个委外组）查询
     */
    List<OutsourcingSampleDto> selectByCreateDateAndGroup(Date beginCreateDate, Date endCreateDate, Long orgId, Long groupId);

    /**
     * 根据ID修改
     */
    boolean updateByOutsourcingSampleId(OutsourcingSampleDto outsourcingSample);

    /**
     * 根据检验日期查询
     */
    List<OutsourcingSampleDto> selectByTestDate(QueryOutsourcingSamplesDto dto);

    /**
     * 根据applyId查询
     */
    List<OutsourcingSampleDto> selectByApplyId(long applyId);

    /**
     * 外送检验审核
     */
    void auditSamplesChain(OutsourcingSampleAuditDto auditDto);

    /**
     * 同步外送样本（外送到迪安、金域的样本）。会生成报告单、同步结果等信息
     */
    void syncOutsourcingSamples(SyncOutsourcingSampleResultDto dto);

    /**
     * 取消审核
     */
    void cancelAuditSample(OutsourcingSampleCancelAuditDto dto);

    /**
     * 二次分拣
     */
    long twoPick(long applySampleId, long instrumentGroupId, String sampleNo);

    /**
     * 取消二次分拣
     */
    OutsourcingSampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds);

    /**
     * 添加外送检验样本
     */
    long addOutsourcingSample(OutsourcingSampleDto dto);

    /**
     * 根据 outsourcingSampleId 删除
     */
    boolean deleteByOutsourcingSampleId(long outsourcingSampleId);

    /**
     * 根据 outsourcingSampleId 删除
     */
    void deleteByOutsourcingSampleIds(Collection<Long> outsourcingSampleIds);

    /**
     * 根据外送机构ID查询
     */
    List<OutsourcingSampleDto> selectByExportOrgId(long exportOrgId);

    /**
     * 根据ID修改
     */
    void updateByOutsourcingSampleIds(OutsourcingSampleDto sampleDto, Collection<Long> outsourcingSampleIds);

    /**
     * 根据申请单样本ID修改
     */
    void updateByApplySampleIds(OutsourcingSampleDto sampleDto, Collection<Long> applySampleIds);

    /**
     * 重新生成报告
     *
     * @return
     */
    SampleReportDto rebuildReport(long applySampleId);

    /**
     * 添加图片
     */
    List<SampleImageDto> addImages(Long applyId, Long sampleId, List<String> imageUrls);

    /**
     * 删除图片
     */
    boolean deleteImage(Long sampleResultImageId);

    /**
     * 根据条件查询
     */
    List<BetterOutsourcingSampleDto> selectByCondition(OutsourcingSampleCondition condition);

    /**
     * 根据applyId 修改送检机构
     * @param outsourcingSampleDto
     */
    void updateByApplyId(OutsourcingSampleDto outsourcingSampleDto);

    void updateByApplyIds(OutsourcingSampleDto outsourcingSampleDto, Collection<Long> applyIds);

    ApplyUpdateBeforeCheckTipDto updateCheckExceptionAndCritical(List<ApplySampleDto> applySampleDtos, ApplyDto applyDto);

    /**
     * 取消外送样本到业务中台
     */
    void cancelOutSendApplyToBusiness(List<OutsourcingSampleDto> outsourcingSampleDtos);

    /**
     * 从业务中台查询外部结果（新，同为LIMS系统）
     */
    Object selectOutSamplesLims(QueryOutsourcingSampleResultDto querySampleResult);

    /**
     * 从业务中台查询外部结果（旧）
     */
    Object selectOutSamplesOld(QueryOutsourcingSampleResultDto querySampleResult);
}
