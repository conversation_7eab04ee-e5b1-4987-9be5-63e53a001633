package com.labway.lims.outsourcing.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * AddSampleImageVo
 * 添加样本图片
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/28 9:57
 */
@Getter
@Setter
public class AddSampleImageVo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 样本ID
     */
    private Long sampleId;

    /**
     * 图片地址
     */
    private List<String> imageUrls;

}
