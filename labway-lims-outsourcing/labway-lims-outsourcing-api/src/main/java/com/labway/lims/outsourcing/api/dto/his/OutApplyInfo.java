package com.labway.lims.outsourcing.api.dto.his;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 外部申请单信息
 */
@Getter
@Setter
public class OutApplyInfo implements Serializable {
    /**
     * 外部条码
     */
    private String barcode;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 体检 门诊 住院
     */
    private String applyType;

    /**
     * 门诊|住院 号
     */
    private String patientVisitCard;

    /**
     * 是否加急 1是 0否
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 样本类型
     */
    private String sampleType;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 申请科室
     */
    private String dept;

    /**
     * 病区
     */
    private String inpatientArea;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 年龄 xx岁
     */
    private Integer patientAge;

    /**
     * 子年龄    xxx天|xxx周|xxx月
     */
    private Integer patientSubage;

    /**
     * 子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date patientBirthday;

    /**
     * 床号
     */
    private String patientBed;

    /**
     * 临床诊断
     */
    private String clinicalDiagnosis;

    /**
     * 身份证
     */
    private String patientCard;

    /**
     * 证件类型
     *
     * @see PatientCardTypeEnum
     */
    private String patientCardType;

    /**
     * 住址
     */
    private String patientAddress;

    /**
     * 手机号
     */
    private String patientMobile;

    /**
     * 申请医生 (送检医生)
     */
    private String sendDoctor;

    /**
     * 申请时间 (送检时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date applyDate;

    /**
     * 采样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date samplingDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 项目信息
     */
    private List<OutApplyItem> items;

    @Getter
    @Setter
    public static class OutApplyItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 外部项目编码
         */
        private String outTestItemCode;
        /**
         * 外部项目名称
         */
        private String outTestItemName;
        /**
         * 检验项目
         */
        private List<TestItem> items;
    }


    @Getter
    @Setter
    public static final class TestItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 项目编码
         */
        private String testItemCode;

        /**
         * 项目名称
         */
        private String testItemName;

        /**
         * 收费数量
         */
        private Integer count;

        /**
         * 是否加急 1是 0否
         * @see UrgentEnum
         */
        private Integer urgent;

    }

}
