package com.labway.lims.outsourcing.api.service;

import com.labway.lims.outsourcing.api.dto.QueryOutsourcingSampleDto;
import com.labway.lims.outsourcing.api.dto.QuerySamplePdfDto;
import com.labway.lims.outsourcing.api.dto.QuerySendOrganizationDto;
import com.labway.lims.outsourcing.api.dto.QueryTestItemDto;
import com.labway.lims.outsourcing.api.vo.QueryOutsourcingSampleVo;
import com.labway.lims.outsourcing.api.vo.QuerySendOrganizationVo;
import com.labway.lims.outsourcing.api.vo.QueryTestItemVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OutsourcingSampleResultPrintService {

    /**
     * 查询外送机构下拉列表查询
     * @param querySendOrganizationDto
     * @return
     */
    List<QuerySendOrganizationVo> querySendOrganization(QuerySendOrganizationDto querySendOrganizationDto);

    /**
     * 查询检验项目下拉列表数据
     * @param queryTestItemDto
     * @return
     */
    List<QueryTestItemVo> queryTestItem(QueryTestItemDto queryTestItemDto);

    /**
     * 外送样本报告单预览pdf
     * @param querySamplePdfDto
     * @return
     */
    List<String> querySamplePdf(QuerySamplePdfDto querySamplePdfDto);

    /**
     * 查询外送样本列表信息
     * @param queryOutsourcingSampleDto
     * @return
     */
    List<QueryOutsourcingSampleVo> queryOutsourcingSample(QueryOutsourcingSampleDto queryOutsourcingSampleDto);
}
