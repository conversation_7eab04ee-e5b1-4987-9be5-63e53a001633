package com.labway.lims.outsourcing.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QueryOutsourcingSampleDto implements Serializable {

    // 外送分拣日期开始时间
    private Date outSendTimeBegin;

    // 外送分拣日期结束时间
    private Date outSendTimeEnd;

    // 回报告日期开始时间
    private Date reportTimeBegin;

    // 回报告日期结束时间
    private Date reportTimeEnd;

    // 外送机构（外送到实验室的机构）
    private String hspOrgCode;

    // 送检机构（实验室送往的机构-例如迪安）
    private String targetOrgCode;

    // 报告状态（0全部 1外送中 2已报告）
    private Integer reportStatus;

    // 是否打印 0否 1是
    private Integer isPrint;

    // 检验项目编码（精确匹配）
    private List<String> testItemCodes;

    // 条码号
    private String barcode;



}
