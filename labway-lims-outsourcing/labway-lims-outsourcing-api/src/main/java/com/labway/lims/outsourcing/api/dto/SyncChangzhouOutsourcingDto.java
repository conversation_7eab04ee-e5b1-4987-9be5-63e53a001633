package com.labway.lims.outsourcing.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <pre>
 * SyncChangzhouOutsourcingDto
 * 同步常州外送结果请求参数
 * </pre>
 *
 * <AUTHOR>
 * @since 2023/11/24 17:42
 */
@Getter
@Setter
public class SyncChangzhouOutsourcingDto implements Serializable {

    /**
     * 常州外送驼峰至上海的机构编码
     */
    private List<Long> exportOrgIds;

}
