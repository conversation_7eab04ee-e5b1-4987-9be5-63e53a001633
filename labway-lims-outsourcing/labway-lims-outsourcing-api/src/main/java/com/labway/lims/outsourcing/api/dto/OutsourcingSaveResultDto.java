package com.labway.lims.outsourcing.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @since 2023/4/12 13:16
 */
@Getter
@Setter
public class OutsourcingSaveResultDto implements Serializable {

    /**
     * 样本ID
     */
    private Long outsourcingSampleId;

    /**
     * applySampleId
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 报告项目Code
     */
    private String reportItemCode;

    /**
     * 这个结果可能是经过格式化或计算过的
     */
    private String result;

    /**
     * 仪器ID
     */
    private Long instrumentId;

    /**
     * 仪器编码
     */
    private String instrumentCode;

    /**
     * 仪器名称
     */
    private String instrumentName;

    /**
     * 检测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date date;

    /**
     * 是否是样本信息修改
     */
    private boolean applySampleUpdate = false;

    /**
     * 结果描述
     */
    private String resultDesc;

}
