package com.labway.lims.outsourcing.api.dto;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 外送样本
 */
@Getter
@Setter
public class BetterOutsourcingSampleDto extends OutsourcingSampleDto {

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 样本状态 待审核 待复查 待二审 已审核 终止（99）
     *
     * @see SampleStatusEnum
     */
    private Integer status;
}
