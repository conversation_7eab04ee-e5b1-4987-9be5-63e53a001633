# 1.1.4.6-shanghai
```yaml
网关白名单
exclude-path:
  patterns:
    # jvs
    - "/routine/sample-result/result-record-compares-jvs"
```
```sql
-- 检验项目：是否自动审核0否 1是 默认为0
alter table public.tb_test_item
    add auto_audit integer default 0 not null;

comment on column public.tb_test_item.auto_audit is '是否允许自动审核0-否，1-是';

alter table public.tb_test_item
    add decision_basis varchar(200) default '' not null;

comment on column public.tb_test_item.decision_basis is '决策地址(自动审核的规则地址)';

-- 样本审核状态
alter table public.tb_sample
    add auto_one_check smallint default 0 not null;

comment on column public.tb_sample.auto_one_check is '一审自动审核0否，1是，默认为0';
```

# *******-dongguan
```sql
alter table public.tb_instrument_report_item add empty_reference_tip integer default 0;
comment on column public.tb_instrument_report_item.empty_reference_tip is '空参考范围提示 0:不提示，1:禁止审核，2:审核提示';
```

# *******-dongguan
```sql
# 外送确认时间
alter table tb_outsourcing_sample
  add outsourcing_confirm_timestamp bigint;
comment on column tb_outsourcing_sample.outsourcing_confirm_timestamp is '外送确认时间戳';

# 质控批次号维护
alter table public.tb_qc_batch add low varchar(255) default '' not null;
comment on column public.tb_qc_batch.low is '低浓度';
alter table public.tb_qc_batch add medium varchar(255) default '' not null;
comment on column public.tb_qc_batch.medium is '中浓度';
alter table public.tb_qc_batch add high varchar(255) default '' not null;
comment on column public.tb_qc_batch.high is '高浓度';

# 质控结果表
alter table public.tb_qc_sample_result add test_date timestamp not null default current_timestamp;
comment on column public.tb_qc_sample_result.test_date is '检验时间';
alter table public.tb_qc_sample_result add level varchar(100);
comment on column public.tb_qc_sample_result.level is '浓度等级：低low 中medium 高high';

# 常规结果表
alter table public.tb_sample_result_2025_01 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2025_02 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2025_03 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2025_04 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2025_05 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2025_06 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2025_07 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2025_08 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2025_09 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2025_10 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2025_11 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2025_12 add test_date timestamp not null default current_timestamp;
comment on column public.tb_sample_result_2025_01.test_date is '检验时间';
comment on column public.tb_sample_result_2025_02.test_date is '检验时间';
comment on column public.tb_sample_result_2025_03.test_date is '检验时间';
comment on column public.tb_sample_result_2025_04.test_date is '检验时间';
comment on column public.tb_sample_result_2025_05.test_date is '检验时间';
comment on column public.tb_sample_result_2025_06.test_date is '检验时间';
comment on column public.tb_sample_result_2025_07.test_date is '检验时间';
comment on column public.tb_sample_result_2025_08.test_date is '检验时间';
comment on column public.tb_sample_result_2025_09.test_date is '检验时间';
comment on column public.tb_sample_result_2025_10.test_date is '检验时间';
comment on column public.tb_sample_result_2025_11.test_date is '检验时间';
comment on column public.tb_sample_result_2025_12.test_date is '检验时间';
alter table public.tb_sample_result_2024_01 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2024_02 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2024_03 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2024_04 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2024_05 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2024_06 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2024_07 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2024_08 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2024_09 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2024_10 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2024_11 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2024_12 add test_date timestamp not null default current_timestamp;
comment on column public.tb_sample_result_2024_01.test_date is '检验时间';
comment on column public.tb_sample_result_2024_02.test_date is '检验时间';
comment on column public.tb_sample_result_2024_03.test_date is '检验时间';
comment on column public.tb_sample_result_2024_04.test_date is '检验时间';
comment on column public.tb_sample_result_2024_05.test_date is '检验时间';
comment on column public.tb_sample_result_2024_06.test_date is '检验时间';
comment on column public.tb_sample_result_2024_07.test_date is '检验时间';
comment on column public.tb_sample_result_2024_08.test_date is '检验时间';
comment on column public.tb_sample_result_2024_09.test_date is '检验时间';
comment on column public.tb_sample_result_2024_10.test_date is '检验时间';
comment on column public.tb_sample_result_2024_11.test_date is '检验时间';
comment on column public.tb_sample_result_2024_12.test_date is '检验时间';
alter table public.tb_sample_result_2023_01 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2023_02 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2023_03 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2023_04 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2023_05 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2023_06 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2023_07 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2023_08 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2023_09 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2023_10 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2023_11 add test_date timestamp not null default current_timestamp;
alter table public.tb_sample_result_2023_12 add test_date timestamp not null default current_timestamp;
comment on column public.tb_sample_result_2023_01.test_date is '检验时间';
comment on column public.tb_sample_result_2023_02.test_date is '检验时间';
comment on column public.tb_sample_result_2023_03.test_date is '检验时间';
comment on column public.tb_sample_result_2023_04.test_date is '检验时间';
comment on column public.tb_sample_result_2023_05.test_date is '检验时间';
comment on column public.tb_sample_result_2023_06.test_date is '检验时间';
comment on column public.tb_sample_result_2023_07.test_date is '检验时间';
comment on column public.tb_sample_result_2023_08.test_date is '检验时间';
comment on column public.tb_sample_result_2023_09.test_date is '检验时间';
comment on column public.tb_sample_result_2023_10.test_date is '检验时间';
comment on column public.tb_sample_result_2023_11.test_date is '检验时间';
comment on column public.tb_sample_result_2023_12.test_date is '检验时间';

```
# 1.1.4.5.1-shanghai

```sql
alter table tb_material add expected_test_count integer ;
comment on column tb_material.expected_test_count is '预期测试数';


-- 表：机构保底金维护
-- auto-generated definition
create table tb_hsp_org_deposit_guarantee
(
  hsp_org_deposit_guarantee_id bigint         not null
    constraint tb_hsp_org_deposit_guarantee_pk
      primary key,
  hsp_org_id                   bigint         not null,
  hsp_org_name                 varchar(100)   not null,
  start_date                   date           not null,
  end_date                     date           not null,
  guarantee_amount             numeric(18, 2) not null,
  create_date                  timestamp      not null,
  update_date                  timestamp      not null,
  creator_id                   bigint         not null,
  creator_name                 varchar(50)    not null,
  updater_id                   bigint         not null,
  updater_name                 varchar(50)    not null,
  is_delete                    smallint       not null
);

comment on table tb_hsp_org_deposit_guarantee is '机构保底金维护';

comment on column tb_hsp_org_deposit_guarantee.hsp_org_deposit_guarantee_id is '主键ID';

comment on column tb_hsp_org_deposit_guarantee.hsp_org_id is '送检机构编码id';

comment on column tb_hsp_org_deposit_guarantee.hsp_org_name is '送检机构名称';

comment on column tb_hsp_org_deposit_guarantee.start_date is '生效日期';

comment on column tb_hsp_org_deposit_guarantee.end_date is '结束日期';

comment on column tb_hsp_org_deposit_guarantee.guarantee_amount is '保底金额';

comment on column tb_hsp_org_deposit_guarantee.create_date is '创建时间';

comment on column tb_hsp_org_deposit_guarantee.update_date is '更新时间';

comment on column tb_hsp_org_deposit_guarantee.creator_id is '创建人ID';

comment on column tb_hsp_org_deposit_guarantee.creator_name is '创建人名称';

comment on column tb_hsp_org_deposit_guarantee.updater_id is '修改人ID';

comment on column tb_hsp_org_deposit_guarantee.updater_name is '修改人名称';

alter table tb_hsp_org_deposit_guarantee
  owner to root;

create index tb_hsp_org_deposit_guarantee_hsp_org_name_index
  on tb_hsp_org_deposit_guarantee (hsp_org_name);

grant select on tb_hsp_org_deposit_guarantee to flink_cdc;









-- 表：报告物料关联表
-- auto-generated definition
create table tb_report_material_relation
(
  report_material_relation_id bigint       not null
    constraint tb_report_material_relation_pk
      primary key,
  report_item_code            varchar(50)  not null,
  material_code               varchar(255) not null,
  material_name               varchar(255) not null,
  report_item_name            varchar(255) not null,
  group_id                    bigint       null,
  group_name                  varchar(255) not null,
  create_date                 timestamp    not null,
  creator_id                  bigint       not null,
  creator_name                varchar(50)  not null,
  is_delete                   smallint     not null
);

comment on table tb_report_material_relation is '报告物料关联表';

comment on column tb_report_material_relation.report_material_relation_id is '报告物料关联ID';

comment on column tb_report_material_relation.report_item_name is '报告项目名称';

comment on column tb_report_material_relation.group_id is '专业组ID';

comment on column tb_report_material_relation.group_name is '专业组名称';

comment on column tb_report_material_relation.material_name is '物资名称';

comment on column tb_report_material_relation.report_item_code is '报告项目编码';

comment on column tb_report_material_relation.material_code is '物资编号';

comment on column tb_report_material_relation.create_date is '创建时间';

comment on column tb_report_material_relation.creator_id is '创建人ID';

comment on column tb_report_material_relation.creator_name is '创建人名称';

comment on column tb_report_material_relation.is_delete is '1: 删除 0:未删';

alter table tb_report_material_relation
  owner to root;

create index tb_report_material_relation_report_item_code_index
  on tb_report_material_relation (report_item_code);

grant select on tb_report_material_relation to flink_cdc;



ALTER TABLE tb_hsp_org_deposit_guarantee
  ADD COLUMN hsp_org_code VARCHAR(50)  NULL;

COMMENT ON COLUMN tb_hsp_org_deposit_guarantee.hsp_org_code IS '送检机构编码';


alter table tb_report_material_relation
  add group_code varchar(50);

comment on column tb_report_material_relation.group_code is '专业组编码';




```



# 1.1.4.5-shanghai
```sql
alter table tb_apply_sample add report_no varchar(15) default '';
comment on column tb_apply_sample.report_no is '报告编号';
        
-- 扩大系统参数值字段
alter table tb_system_param
alter column param_value type varchar(20000) using param_value::varchar(20000);

-- 特检增加结果类型
alter table tb_specialty_sample add result_type integer default 2;
comment on column tb_specialty_sample.result_type is '2： 默认
1： word
2： 多发性骨髓瘤MM（17CD）、骨髓增生异常综合征MDS(23CD)、急慢性白血病（31CD）、急慢性白血病/NHL/MDS全面检测(40CD)、淋巴瘤(35CD)
3： 多发性骨髓瘤微小残留检测(10CD)、微小残留白血病检测(20CD)
4： 高敏PNH（8CD）
5： 红粒细胞CD55/CD59评估（PNH检测）
6： 43种融合基因筛查
7： BCR-ABL1 P210型融合基因激酶区突变检测、BCR-ABL融合基因酪氨酸激酶区突变检测
8： BCR/ABL1 p230
9： BCR/ABL定量(p210)
10： CALR基因突变
11： IGHV突变
12： IGH基因重排检测
13： MPL W515K/L基因突变
14： MYD88 L265P基因突变检测
15： TCR基因重排检测
16： 丙型肝炎病毒基因分型检测(测序法)
17： 高灵敏度HBV-DNA（高灵敏）
18： 淋巴细胞基因重排检测
19： 融合基因BCR/ABL(定量,p190)
20： 融合基因BCR/ABL(定量,p190/210/230)
21： 乙肝病毒P区耐药检测(测序法)（外送）
22： 乙型肝炎病毒基因分型(测序法)';
alter table tb_specialty_sample_result
drop column position;

alter table tb_specialty_sample_result
drop column type;

alter table tb_specialty_sample_result
drop column sort;

alter table tb_specialty_sample_result
  rename column result to specialty_result;

alter table tb_specialty_sample_result
drop column word_content;


-- 申请单增加字段 
alter table tb_apply add inpatient_area varchar(255) default '';
comment on column tb_apply.inpatient_area is '病区';

alter table tb_apply add visit_card_no varchar(50) default '';
comment on column tb_apply.visit_card_no is '医保卡号';

```

# 1.1.4.4-shanghai

```sql
alter table tb_rack    add is_destroyed smallint default 0 not null;
comment on column tb_rack.is_destroyed is '是否自动销毁, 0否 1是';

alter table tb_rack    add storage_days integer default 0 not null;
comment on column tb_rack.storage_days is '销毁日期';

alter table public.tb_enzyme_label_layout
    add reagent_manufacturer varchar(1000);

comment on column public.tb_enzyme_label_layout.reagent_manufacturer is '试剂厂家';

alter table public.tb_enzyme_label_layout
    add reagent_batch varchar(1000);

comment on column public.tb_enzyme_label_layout.reagent_batch is '试剂批次';

alter table public.tb_enzyme_label_layout
    add reagent_valid_date varchar(1000);

comment on column public.tb_enzyme_label_layout.reagent_valid_date is '试剂有效期';

alter table public.tb_enzyme_label_layout
    alter column sample_start_no type varchar(100) using sample_start_no::varchar(100);



```

# *******-shanghai

```sql
alter table tb_genetics_sample add result_type int;
comment on column tb_genetics_sample.result_type is '1:核型7 2：核型6 3：胸腹水染色体， 4 DNA_DFI  5 word';

alter table tb_genetics_sample_result add genetics_result varchar(5000);
comment on column tb_genetics_sample_result.genetics_result is '结果json';

alter table tb_genetics_sample_result drop column cell_count;
alter table tb_genetics_sample_result drop column analyse_cell_count;
alter table tb_genetics_sample_result drop column sample_situation;
alter table tb_genetics_sample_result drop column karyotype;
alter table tb_genetics_sample_result drop column banding_method;
alter table tb_genetics_sample_result drop column banding_level;
alter table tb_genetics_sample_result drop column analytical_opinion;
alter table tb_genetics_sample_result drop column karyotype_original_img1;
alter table tb_genetics_sample_result drop column karyotype_original_img2;
alter table tb_genetics_sample_result drop column karyotype_original_img3;
alter table tb_genetics_sample_result drop column karyotype_img1;
alter table tb_genetics_sample_result drop column karyotype_img2;
alter table tb_genetics_sample_result drop column karyotype_img3;

CREATE TABLE tb_delete_sample_result_main
(
  delete_sample_result_main_id BIGINT PRIMARY KEY,
  sample_id                    BIGINT      not null,
  barcode                      VARCHAR(50) not null,
  sample_no                    VARCHAR(50) not null,
  item_type                    VARCHAR(50) not null,
  test_date                    TIMESTAMP   not null,
  delete_date                  TIMESTAMP   not null,
  delete_user_id               BIGINT      not null,
  delete_user_name             VARCHAR(50) not null,
  group_id                     BIGINT      not null,
  group_name                   VARCHAR(50) not null,
  create_id                    BIGINT      not null,
  create_name                  VARCHAR(50) not null,
  create_date                  TIMESTAMP   not null,
  update_id                    BIGINT      not null,
  update_name                  VARCHAR(50) not null,
  update_date                  TIMESTAMP   not null,
  is_delete                    smallint    not null
);
create index tb_delete_sample_result_main_sample_id_index on tb_delete_sample_result_main (sample_id);

COMMENT
ON TABLE tb_delete_sample_result_main IS '删除样本结果主表';
COMMENT
ON COLUMN tb_delete_sample_result_main.delete_sample_result_main_id IS '删除样本结果主表id';
COMMENT
ON COLUMN tb_delete_sample_result_main.sample_id IS '样本id';
COMMENT
ON COLUMN tb_delete_sample_result_main.barcode IS '条码号';
COMMENT
ON COLUMN tb_delete_sample_result_main.sample_no IS '样本号';
COMMENT
ON COLUMN tb_delete_sample_result_main.item_type IS '项目类型';
COMMENT
ON COLUMN tb_delete_sample_result_main.test_date IS '检验日期';
COMMENT
ON COLUMN tb_delete_sample_result_main.delete_date IS '删除日期';
COMMENT
ON COLUMN tb_delete_sample_result_main.delete_user_id IS '删除人id';
COMMENT
ON COLUMN tb_delete_sample_result_main.delete_user_name IS '删除人姓名';
COMMENT
ON COLUMN tb_delete_sample_result_main.group_id IS '专业组id';
COMMENT
ON COLUMN tb_delete_sample_result_main.group_name IS '专业组名称';
COMMENT
ON COLUMN tb_delete_sample_result_main.create_id IS '创建人id';
COMMENT
ON COLUMN tb_delete_sample_result_main.create_name IS '创建人姓名';
COMMENT
ON COLUMN tb_delete_sample_result_main.create_date IS '创建日期';
COMMENT
ON COLUMN tb_delete_sample_result_main.update_id IS '更新人id';
COMMENT
ON COLUMN tb_delete_sample_result_main.update_name IS '更新人姓名';
COMMENT
ON COLUMN tb_delete_sample_result_main.update_date IS '更新日期';
COMMENT
ON COLUMN tb_delete_sample_result_main.is_delete IS '是否删除';


CREATE TABLE tb_delete_sample_result_detail
(
  delete_sample_result_detail_id BIGINT PRIMARY KEY,              -- 删除的样本结果详情id
  delete_sample_result_main_id   BIGINT       NOT NULL,           -- 删除的样本结果主表id
  test_item_id                   BIGINT       NOT NULL,           -- 检验项目id
  test_item_code                 VARCHAR(255) NOT NULL,           -- 检验项目code
  test_item_name                 VARCHAR(255) NOT NULL,           -- 检验项目名称
  report_item_id                 BIGINT       NOT NULL,           -- 报告项目id
  report_item_code               VARCHAR(255) NOT NULL,           -- 报告项目code
  report_item_name               VARCHAR(255) NOT NULL,           -- 报告项目名称
  result                         VARCHAR(255),                    -- 结果
  instrument_id                  BIGINT,                          -- 仪器
  is_hande_result                INTEGER      NOT NULL default 0, -- 是否手工录入结果 0否1是
  en_name                        VARCHAR(255),                    -- 英文简称
  create_id                      BIGINT       NOT NULL,           -- 创建人id
  create_name                    VARCHAR(255) NOT NULL,           -- 创建人姓名
  create_date                    TIMESTAMP    NOT NULL,           -- 创建日期
  update_id                      BIGINT       NOT NULL,           -- 更新人id
  update_name                    VARCHAR(255) NOT NULL,           -- 更新人姓名
  update_date                    TIMESTAMP    NOT NULL,           -- 更新日期
  is_delete                      smallint     NOT NULL            -- 是否删除，参考YesOrNoEnum枚举
);
create index tb_delete_sample_result_main_id_index on tb_delete_sample_result_detail (delete_sample_result_main_id);


COMMENT
ON TABLE tb_delete_sample_result_detail IS '删除样本结果详情表';

COMMENT
ON COLUMN tb_delete_sample_result_detail.delete_sample_result_detail_id IS '删除的样本结果详情id';
COMMENT
ON COLUMN tb_delete_sample_result_detail.delete_sample_result_main_id IS '删除的样本结果主表id';
COMMENT
ON COLUMN tb_delete_sample_result_detail.test_item_id IS '检验项目id';
COMMENT
ON COLUMN tb_delete_sample_result_detail.test_item_code IS '检验项目code';
COMMENT
ON COLUMN tb_delete_sample_result_detail.test_item_name IS '检验项目名称';
COMMENT
ON COLUMN tb_delete_sample_result_detail.report_item_id IS '报告项目id';
COMMENT
ON COLUMN tb_delete_sample_result_detail.report_item_code IS '报告项目code';
COMMENT
ON COLUMN tb_delete_sample_result_detail.report_item_name IS '报告项目名称';
COMMENT
ON COLUMN tb_delete_sample_result_detail.result IS '结果';
COMMENT
ON COLUMN tb_delete_sample_result_detail.instrument_id IS '仪器';
COMMENT
ON COLUMN tb_delete_sample_result_detail.is_hande_result IS '是否手工录入结果 0否1是';
COMMENT
ON COLUMN tb_delete_sample_result_detail.en_name IS '英文简称';
COMMENT
ON COLUMN tb_delete_sample_result_detail.create_id IS '创建人id';
COMMENT
ON COLUMN tb_delete_sample_result_detail.create_name IS '创建人姓名';
COMMENT
ON COLUMN tb_delete_sample_result_detail.create_date IS '创建日期';
COMMENT
ON COLUMN tb_delete_sample_result_detail.update_id IS '更新人id';
COMMENT
ON COLUMN tb_delete_sample_result_detail.update_name IS '更新人姓名';
COMMENT
ON COLUMN tb_delete_sample_result_detail.update_date IS '更新日期';
COMMENT
ON COLUMN tb_delete_sample_result_detail.is_delete IS '是否删除';


```

# *******-sh

```sql
alter table tb_microbiology_sample  add result_type int;
comment on column tb_microbiology_sample.result_type is '1正常检验结果， 2word结果';
        
alter table tb_microbiology_sample_result  add word_content varchar(1024);
comment on column tb_microbiology_sample_result.word_content is 'word结果';

```

1.1.4.2-sh
```sql

alter table tb_outsourcing_sample
  add one_checker_name varchar(50);
comment
on column tb_outsourcing_sample.one_checker_name is '一次审核人';

alter table tb_outsourcing_sample
  add one_checker_id bigint;
comment
on column tb_outsourcing_sample.one_checker_id is '一次审核人';

alter table tb_outsourcing_sample
  add one_check_date timestamp;
comment
on column tb_outsourcing_sample.one_check_date is '一次审核时间';

create index APPLYID_IMAGE_URL_INDEX
on public.tb_apply_sample_image (apply_id desc, image_url desc);


update tb_report_template2
set template_type = 0
where template_type is null;
comment
on column tb_report_template2.template_type is '0:html,1:word,2:json';
alter table tb_report_template2
  alter column template_type set not null;

```


# 特检word
```sql

ALTER TABLE "public"."tb_report_template2" ADD COLUMN "template_type" int2 DEFAULT 0;

COMMENT ON COLUMN "public"."tb_report_template2"."template_type" IS '0:html,1:word';

ALTER TABLE "public"."tb_report_template_bind" ADD COLUMN "file_url" varchar(255) COLLATE "pg_catalog"."default";

ALTER TABLE "public"."tb_report_template_bind" ADD COLUMN "report_template_type" varchar(20) COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."tb_report_template_bind"."file_url" IS '上传文件地址';

COMMENT ON COLUMN "public"."tb_report_template_bind"."report_template_type" IS '模板类型(html,word)';

ALTER TABLE "public"."tb_specialty_sample_result" ADD COLUMN "word_content" text COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."tb_specialty_sample_result"."type" IS '1. 文字
2. 图片3.word';

COMMENT ON COLUMN "public"."tb_specialty_sample_result"."word_content" IS 'word文本内容';

```

```nacos
  labway-lims-pdfreport.yaml新增如下内容：
  word:
  onLineUpLoadUrl: http://121.36.199.164:8002/upload
  onLineEditUrl: http://121.36.199.164:8002/editor?action=edit&fileName=
  onLineViewUrl: http://121.36.199.164:8002/editor?action=view&fileName=
  onLineDownLoadUrl: http://121.36.199.164:8002/download?fileName=
  onlyOfficeKey: fmTLqGPKAtW3lA2IvTWGFdKVsHE6wPjn
  onLineExchangeUrl: http://121.36.199.164:8001/office/ConvertService.ashx

```

# *******-sh
```sql
  alter table public.tb_infection_sample
  add judge_standard_code varchar(1024);

comment on column public.tb_infection_sample.judge_standard_code is '判定标准编码';

alter table public.tb_infection_sample
  add judge_standard_name varchar(1024);

comment on column public.tb_infection_sample.judge_standard_name is '判定标准名称';


alter table public.tb_apply_sample
  add sample_source varchar(50);

comment on column public.tb_apply_sample.sample_source is '样本来源（pda,签收,手录等）';


alter table public.tb_pda_tobe_confirmed_apply
  add conformer_id bigint;

comment on column public.tb_pda_tobe_confirmed_apply.conformer_id is '确认人id';

alter table public.tb_pda_tobe_confirmed_apply
  add conformer_name varchar(50);

comment on column public.tb_pda_tobe_confirmed_apply.conformer_name is '确认人名称';

alter table public.tb_pda_tobe_confirmed_apply
  add conformer_time timestamp;

comment on column public.tb_pda_tobe_confirmed_apply.conformer_time is '确认时间';
        
        

        -- auto-generated definition
create table tb_apply_sample_image
(
  apply_sample_image_id bigint             not null
    constraint tb_apply_sample_image_pk
      primary key,
  apply_id              bigint             not null,
  apply_sample_id       bigint             not null,
  image_url             varchar(255)       not null,
  is_delete             smallint default 0 not null,
  create_date           timestamp          not null,
  creator_id            bigint             not null,
  update_date            timestamp          not null,
  updater_id            bigint             not null,
  updater_name          varchar(50)        not null,
  image_name            varchar(100),
  creator_name          varchar(50)        not null
);

comment on table tb_apply_sample_image is '申请单样本图片表';

comment on column tb_apply_sample_image.apply_sample_image_id is '申请单样本图片id';

comment on column tb_apply_sample_image.apply_id is '申请单id';

comment on column tb_apply_sample_image.apply_sample_id is '申请单样本id';

comment on column tb_apply_sample_image.image_url is '图片地址';

comment on column tb_apply_sample_image.is_delete is '是否删除 0否1是';

comment on column tb_apply_sample_image.create_date is '创建时间';

comment on column tb_apply_sample_image.creator_id is '创建人id';

comment on column tb_apply_sample_image.update_date is '跟新时间';

comment on column tb_apply_sample_image.updater_id is '更新人id';

comment on column tb_apply_sample_image.updater_name is '跟新人名称';

comment on column tb_apply_sample_image.image_name is '图片名称';

comment on column tb_apply_sample_image.creator_name is '创建人名称';

alter table tb_apply_sample_image
  owner to root;

create index tb_apply_sample_image_apply_id_index
  on tb_apply_sample_image (apply_id desc);

create index tb_apply_sample_image_apply_sample_id_index
  on tb_apply_sample_image (apply_sample_id desc);
        

```

# 1.1.4-pub
```sql
ALTER TABLE "public"."tb_apply_sample" 
  ADD COLUMN "patient_part" varchar(50);

COMMENT ON COLUMN "public"."tb_apply_sample"."patient_part" IS '标本部位';
```

# 1.1.3.9sample
## 物料部分
```sql
-- 物料申领详情
ALTER TABLE "public"."tb_group_material_apply_detail" 
  ALTER COLUMN "apply_main_number" TYPE numeric(10,4) USING "apply_main_number"::numeric(10,4);
-- 物料出库详情
ALTER TABLE "public"."tb_material_delivery_detail" 
  ALTER COLUMN "delivery_main_number" TYPE numeric(10,4) USING "delivery_main_number"::numeric(10,4);
-- 物料领用记录
ALTER TABLE "public"."tb_material_receive_record" 
  ALTER COLUMN "receive_main_number" TYPE numeric(10,4) USING "receive_main_number"::numeric(10,4);
-- 物料库存
ALTER TABLE "public"."tb_material_inventory" 
  ALTER COLUMN "main_unit_inventory" TYPE numeric(10,4) USING "main_unit_inventory"::numeric(10,4);
-- 专业组物料
ALTER TABLE "public"."tb_group_material" 
  ALTER COLUMN "main_unit_inventory" TYPE numeric(10,4) USING "main_unit_inventory"::numeric(10,4),
  ALTER COLUMN "assist_unit_inventory" TYPE numeric(10,4);
-- 物料
ALTER TABLE "public"."tb_material" 
  ALTER COLUMN "main_unit_inventory" TYPE numeric(10,4) USING "main_unit_inventory"::numeric(10,4);
-- 物料盘点详情
ALTER TABLE "public"."tb_material_inventory_check_detail" 
  ALTER COLUMN "main_inventory" TYPE numeric(10,4) USING "main_inventory"::numeric(10,4),
  ALTER COLUMN "actual_main_inventory" TYPE numeric(10,4) USING "actual_main_inventory"::numeric(10,4),
  ALTER COLUMN "main_profit" TYPE numeric(10,4) USING "main_profit"::numeric(10,4);
-- 物流退库记录表
ALTER TABLE "public"."tb_material_refund_record" 
  ALTER COLUMN "delivery_main_number" TYPE numeric(10,4) USING "delivery_main_number"::numeric(10,4),
  ALTER COLUMN "income_main_number" TYPE numeric(10,4) USING "income_main_number"::numeric(10,4),
  ALTER COLUMN "refund_main_number" TYPE numeric(10,4) USING "refund_main_number"::numeric(10,4);
-- 物料入库记录
ALTER TABLE "public"."tb_material_income_record" 
  ALTER COLUMN "delivery_main_number" TYPE numeric(10,4) USING "delivery_main_number"::numeric(10,4),
  ALTER COLUMN "income_main_number" TYPE numeric(10,4) USING "income_main_number"::numeric(10,4);

-- 物料条码号
ALTER TABLE "public"."tb_material_delivery_detail"
    ADD COLUMN "material_barcode" varchar(20);
COMMENT ON COLUMN "public"."tb_material_delivery_detail"."material_barcode" IS '物料条码号';
```
gateway 放行 ：
- "/apply/export-api/business-center-search-material-barcode"


# 1.1.3.7

```
# 免疫二次分拣
alter table public.tb_apply_sample
    add is_immunity_two_pick integer;

comment on column public.tb_apply_sample.is_immunity_two_pick is '是否是免疫二次分拣 1：是 0：否';

alter table tb_sample_abnormal add images text;
comment on column tb_sample_abnormal.images is '异常图片集合';

ALTER TABLE "public"."tb_test_item" ADD COLUMN "two_pick_day" varchar(255) COLLATE "pg_catalog"."default";

ALTER TABLE "public"."tb_test_item" ADD COLUMN "two_pick_time" varchar(50) COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."tb_test_item"."two_pick_day" IS '二次分拣日期(0:当时、1~7周一到周日)';

COMMENT ON COLUMN "public"."tb_test_item"."two_pick_time" IS '二次分拣时间(精确到分钟)';       

------------------------------------------------------------------------------------------------------------------------

alter table public.tb_sample_result_2023_01 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_01.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2023_02 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_02.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2023_03 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_03.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2023_04 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_04.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2023_05 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_05.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2023_06 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_06.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2023_07 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_07.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2023_08 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_08.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2023_09 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_09.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2023_10 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_10.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2023_11 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_11.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2023_12 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2023_12.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2024_01 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_01.is_hande_result is '是否手动录入结果 0否1是';


alter table public.tb_sample_result_2024_02 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_02.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2024_03 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_03.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2024_04 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_04.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2024_05 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_05.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2024_06 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_06.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2024_07 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_07.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2024_08 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_08.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2024_09 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_09.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2024_10 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_10.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2024_11 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_11.is_hande_result is '是否手动录入结果 0否1是';

alter table public.tb_sample_result_2024_12 add is_hande_result integer default 0;
comment on column public.tb_sample_result_2024_12.is_hande_result is '是否手动录入结果 0否1是';

------------------------------------------------------------------------------------------------------------------------

alter table public.tb_instrument_report_item_exception
    add formula_max varchar(20);

comment on column public.tb_instrument_report_item_exception.formula_max is '≥, >, =, <, ≤';

alter table public.tb_instrument_report_item_exception
    add formula_max_value varchar(255);

comment on column public.tb_instrument_report_item_exception.formula_max_value is '条件值';

alter table public.tb_instrument_report_item_exception
    add formula_min varchar(20);

comment on column public.tb_instrument_report_item_exception.formula_min is '<, ≤';

alter table public.tb_instrument_report_item_exception
    add formula_min_value varchar(255);

comment on column public.tb_instrument_report_item_exception.formula_min_value is '条件值';

# 刷数据--结果值转换数据 需要先执行以上添加字段语句，更新服务之前执行
update tb_instrument_report_item_exception set formula_max = '=',formula_max_value = result where is_delete = 0

------------------------------------------------------------------------------------------------------------------------

alter table public.tb_microbiology_sample
    add one_checker_name varchar(255);

comment on column public.tb_microbiology_sample.one_checker_name is '一次审核人名称';

alter table public.tb_microbiology_sample
    add one_checker_id bigint;

comment on column public.tb_microbiology_sample.one_checker_id is '一次审核人id';

alter table public.tb_microbiology_sample
    add one_checker_date timestamp;

comment on column public.tb_microbiology_sample.one_checker_date is '一次审核时间';

------------------------------------------------------------------------------------------------------------------------

添加系统配置参数：
  MICROBIOLOGY_ONE_AUDIT（微生物是否开启一审功能（0否1是））
  IGNORE_OUTSOURCING_REPORT_ITEM (调用外部结果时需要忽略的报告项目，通过报告项目编码进行匹配过滤)
    示例值：[{"reportCode":"111","reportName":"aaa"},{"reportCode":"222","reportName":"bbb"},{"reportCode":"1683707665581","reportName":"管道符号分割"}]

------------------------------------------------------------------------------------------------------------------------
labway-lims-routine.yaml 配置文件中的combined-bill配置参数复制一份到 labway-lims-apply.yaml



```

# 1.1.3.6
```sql
alter table tb_test_item
    add limit_sex integer default 0;

comment on column tb_test_item.limit_sex is '限制性别：0 不限制，1 男，2 女';
```

# 1.1.3.5
```sql
# 外送清单打印 打印状态记录
alter table tb_apply_sample
    add is_print_list integer default 0;
alter table tb_apply_sample
    add print_list_date timestamp;

comment on column tb_outsourcing_sample.is_print_list is '是否已打印清单，1:是，0:不是';
comment on column tb_outsourcing_sample.print_list_date is '打印清单日期';

```



# 2024-10-22 延迟发送报告
```

# labway-lims-common.yaml

# 广州OrgId
lj-org-id: 19

# 1. 系统参数配置(默认单位：分钟)
  KEY:HSP_ORG_REPORT_DELAY_CONFIG
  VALUE:{"13001":1,"13002":1}

# 2. 发送报告延迟消息配置（MQ）
Exchange：labway_lims
RoutingKey：sample_change_key_dlx
Queue: sample_change_queue_dlx
  Exchange(x-dead-letter-exchange): labway_lims
  RoutingKey(x-dead-letter-routing-key): sample_change_key

# 3. 消息Queue：sample_change_key有以下
审核消息发送的交换机和路由：
  Exchange: labway_lims
  RoutingKey: sample_change_key
改交换机和路由会转发到如下队列：

business-monitor-lims-sample-event
业务中台：告警：实验室已审核，但是社区还未拉取报告

labway-lims2report-queue
报告平台：监听审核消息，同步报告结果

labway_business_lims_apply_result_event
业务中台：二审消息，重置一下is_report状态

```


# 1.1.3.4
```sql
alter table public.tb_instrument_report_item_result_exchange
    add formula_max varchar(20);

comment on column public.tb_instrument_report_item_result_exchange.formula_max is '≥, >, =, <, ≤';

alter table public.tb_instrument_report_item_result_exchange
    add formula_max_value varchar(255);

comment on column public.tb_instrument_report_item_result_exchange.formula_max_value is '条件值';

alter table public.tb_instrument_report_item_result_exchange
    add formula_min varchar(20);

comment on column public.tb_instrument_report_item_result_exchange.formula_min is '<, ≤';

alter table public.tb_instrument_report_item_result_exchange
    add formula_min_value varchar(255);

comment on column public.tb_instrument_report_item_result_exchange.formula_min_value is '条件值';

# 刷数据--结果值转换数据 需要先执行以上添加字段语句，更新服务之前执行
update tb_instrument_report_item_result_exchange set formula_max = '=',formula_max_value = instrument_result where is_delete = 0
        
```

# 1.1.3.3

```sql
系统参数配置：
SPLIT_BLOOD_ORIGINAL_BARCODE_GROUP_ORDER  分血后那个机构使用源条码专业组顺序（专业组编码）,英文逗号隔开

-- 送检机构增加送检类型， 根据,分割  默认全部
alter table tb_hsp_organization
  add apply_type_names varchar(128) default '全部' not null;
comment on column tb_hsp_organization.apply_type_names is '送检类型， 如果不允许分血的话， 只有当前送检类型不允许分血';

```
# 1.1.3.2
```sql
create table if not exists public.tb_upload_file
(
    file_id       bigint       not null
        primary key,
    file_name     varchar(200) not null,
    file_url      varchar(255) not null,
    type          smallint     not null,
    status        smallint     not null,
    remark        varchar(255),
    info          text,
    uploader_name varchar(50)  not null,
    handle_time   timestamp,
    create_time   timestamp    not null,
    type_desc     varchar(50)  not null,
    hsp_org_code  varchar(50)  not null,
    hsp_org_name  varchar(50)  not null,
    resource      varchar(50)  not null
);

comment on column public.tb_upload_file.file_id is '文件编码';

comment on column public.tb_upload_file.file_name is '文件名称';

comment on column public.tb_upload_file.file_url is '文件地址';

comment on column public.tb_upload_file.type is '文件类型';

comment on column public.tb_upload_file.status is '文件状态';

comment on column public.tb_upload_file.remark is '备注';

comment on column public.tb_upload_file.info is '文件详情';

comment on column public.tb_upload_file.uploader_name is '创建人姓名';

comment on column public.tb_upload_file.handle_time is '处理时间';

comment on column public.tb_upload_file.create_time is '创建时间';

comment on column public.tb_upload_file.type_desc is '文件类型描述';

comment on column public.tb_upload_file.hsp_org_code is '送检机构编码';

comment on column public.tb_upload_file.hsp_org_name is '送检机构名称';

comment on column public.tb_upload_file.resource is '文件来源';

alter table public.tb_upload_file
    owner to root;

```


```
系统参数配置， 前端使用：
Button_Blood_Division  是否通过点击分血按钮进行分血(1是0不是）
```


# 1.1.3.1
```sql
-- 入库记录添加是否合格等5个字段
ALTER TABLE public.tb_material_income_record ADD if_storage_qualified int2 DEFAULT 1 NULL;
COMMENT ON COLUMN public.tb_material_income_record.if_storage_qualified IS '1是 0否   默认1';
ALTER TABLE public.tb_material_income_record ADD if_spec_quantity_consistent int2 DEFAULT 1 NULL;
COMMENT ON COLUMN public.tb_material_income_record.if_spec_quantity_consistent IS '规格数量是否一致 1是 0否   默认1';
ALTER TABLE public.tb_material_income_record ADD if_package_damaged int2 DEFAULT 0 NULL;
COMMENT ON COLUMN public.tb_material_income_record.if_package_damaged IS '包装有无破损 1有0无   默认0';
ALTER TABLE public.tb_material_income_record ADD if_valid_date_qualified int2 DEFAULT 1 NULL;
COMMENT ON COLUMN public.tb_material_income_record.if_valid_date_qualified IS '效期是否合格  1是 0否   默认1';
ALTER TABLE public.tb_material_income_record ADD acceptance_conclusion int2 DEFAULT 1 NULL;
COMMENT ON COLUMN public.tb_material_income_record.acceptance_conclusion IS '验收结论 1合格0不合格  默认1';
```


# 1.1.3
```sql
ALTER TABLE "public"."tb_sample_critical_result" ADD COLUMN "is_read_back" int2;
ALTER TABLE "public"."tb_sample_critical_result" ADD COLUMN "remark" text COLLATE "pg_catalog"."default";
ALTER TABLE "public"."tb_sample_critical_result" ADD COLUMN "time_out_config" int4;
ALTER TABLE "public"."tb_sample_critical_result" ADD COLUMN "is_time_out" int2;
ALTER TABLE "public"."tb_sample_critical_result" ADD COLUMN "read_back_user" varchar(60) COLLATE "pg_catalog"."default";
ALTER TABLE "public"."tb_sample_critical_result" ADD COLUMN "read_back_time" timestamp(6);
COMMENT ON COLUMN "public"."tb_sample_critical_result"."is_read_back" IS '是否回读';
COMMENT ON COLUMN "public"."tb_sample_critical_result"."remark" IS '备注';
COMMENT ON COLUMN "public"."tb_sample_critical_result"."time_out_config" IS '超时配置(单位分钟)';
COMMENT ON COLUMN "public"."tb_sample_critical_result"."is_time_out" IS '是否超时(0未超时 1超时)';
COMMENT ON COLUMN "public"."tb_sample_critical_result"."read_back_user" IS '回读人';
COMMENT ON COLUMN "public"."tb_sample_critical_result"."read_back_time" IS '回读时间';
 
```

```text
系统参数配置:
    参数名称：MICROBIOLOGY_SAMPLE_PROPERTY
    参数示例值：[{"sampleTypeCode":"aaa","testItemCode":"111,222,333"},{"sampleTypeCode":"10001","testItemCode":"444,item1001,25050101101"}]
    参数说明：微生物检验项目样本性状配置，sampleTypeCode：样本类型编码，testItemCode：检验项目编码（多个编码之间用英文逗号分割）
    
    参数名称：INFECTIOUS_DISEASES_REPORT_CODES
    参数示例值：aaa,bbb,ccc
    参数说明：传染病报告项目编码配置，多个之间用英文逗号分割
    
    
```

# 1.1.2.1
```sql

-- 物料申领退回原因
alter table public.tb_group_material_apply
  add return_reason varchar(255);
comment on column public.tb_group_material_apply.return_reason is '退回原因';
        

-- 库存管理上下限
alter table tb_material_inventory add inventory_upper_limit numeric(14, 4);
comment on column tb_material_inventory.inventory_upper_limit is '库存上限';

alter table tb_material_inventory add inventory_lower_limit numeric(14, 4);
comment on column tb_material_inventory.inventory_lower_limit is '库存下限';

-- 删除旧表
drop table tb_group_material_plan;


create table if not exists public.tb_group_material_plan
(
  group_material_plan_id bigint                                     not null
    constraint tb_group_material_plan_pk
      primary key,
  plan_no                varchar(50)                                not null,
  material_code          varchar(50)                                not null,
  material_name          varchar(255)                                not null,
  specification          varchar(255)                               not null,
  manufacturers          varchar(255)                               not null,
  main_unit              varchar(50)                                not null,
  main_apply_number      varchar(50)                                not null,
  assist_unit            varchar(50)                                not null,
  assist_apply_number    varchar(50)                                not null,
  unit_conversion_rate   varchar(50)                                not null,
  group_id               bigint                                     not null,
  group_code             varchar(50)                                not null,
  group_name             varchar(50)                                not null,
  org_id                 bigint                                     not null,
  org_name               varchar(255)                               not null,
  create_date            timestamp(6)                               not null,
  update_date            timestamp(6)                               not null,
  updater_id             bigint                                     not null,
  updater_name           varchar(255)                               not null,
  creator_id             bigint                                     not null,
  creator_name           varchar(255)                               not null,
  is_delete              smallint                                   not null,
  status                 integer                                    not null,
  audit_date             timestamp                                  not null,
  audit_id               bigint                                     not null,
  audit_name             varchar(50)                                not null,
  return_reason          varchar(255)                               not null,
  return_user_id         bigint                                     not null,
  return_user_name       varchar(50)                                not null,
  return_date            timestamp                                  not null,
  remark                 varchar(256) default ''::character varying not null,
  planner_id             bigint,
  planner_name           varchar(255),
  planner_date           timestamp,
  submit_id              bigint,
  submit_name            varchar(255),
  submit_date            timestamp
);

comment on table public.tb_group_material_plan is '专业组物料计划';

comment on column public.tb_group_material_plan.group_material_plan_id is '计划ID';

comment on column public.tb_group_material_plan.plan_no is '计划单号';

comment on column public.tb_group_material_plan.material_code is '物料code';

comment on column public.tb_group_material_plan.material_name is '物料名称';

comment on column public.tb_group_material_plan.specification is '规格';

comment on column public.tb_group_material_plan.manufacturers is '厂家';

comment on column public.tb_group_material_plan.main_unit is '主单位';

comment on column public.tb_group_material_plan.main_apply_number is '主申请数量';

comment on column public.tb_group_material_plan.assist_unit is '辅单位';

comment on column public.tb_group_material_plan.assist_apply_number is '辅申请数量';

comment on column public.tb_group_material_plan.unit_conversion_rate is '主副单位转换率';

comment on column public.tb_group_material_plan.group_id is '专业组ID';

comment on column public.tb_group_material_plan.group_code is '专业组编码';

comment on column public.tb_group_material_plan.group_name is '专业组名称';

comment on column public.tb_group_material_plan.org_id is '检验机构';

comment on column public.tb_group_material_plan.org_name is '检验机构名称';

comment on column public.tb_group_material_plan.create_date is '创建时间';

comment on column public.tb_group_material_plan.update_date is '更新时间';

comment on column public.tb_group_material_plan.updater_id is '更新人ID';

comment on column public.tb_group_material_plan.updater_name is '更新人名称';

comment on column public.tb_group_material_plan.creator_id is '创建人ID';

comment on column public.tb_group_material_plan.creator_name is '创建人名称';

comment on column public.tb_group_material_plan.is_delete is '1:删除，0:未删';

comment on column public.tb_group_material_plan.status is '状态 1已保存， 2已提交， 3已审核 4已退回';

comment on column public.tb_group_material_plan.audit_id is '一审人ID';

comment on column public.tb_group_material_plan.audit_name is '一审人姓名';

comment on column public.tb_group_material_plan.return_reason is '退回原因';

comment on column public.tb_group_material_plan.return_user_id is '退回用户ID';

comment on column public.tb_group_material_plan.return_user_name is '退回用户名称';

comment on column public.tb_group_material_plan.return_date is '退回时间';

comment on column public.tb_group_material_plan.remark is '备注';

comment on column public.tb_group_material_plan.planner_id is '计划人id';

comment on column public.tb_group_material_plan.planner_name is ' 计划人name';

comment on column public.tb_group_material_plan.planner_date is '计划时间';

comment on column tb_group_material_plan.submit_id is '提交人id';

comment on column tb_group_material_plan.submit_name is '提交人名称';

comment on column tb_group_material_plan.submit_date is '提交时间';

alter table public.tb_group_material_plan
  owner to root;


```


# 1.1.2
## PDF报告单：
外送清单打印（OUTSOURCING_LIST）  从测试复制到正式环境
```yml
nacos: labway-lims-statistics.yaml下添加

statistics:
  by-send-doctor:
    version: 2.0
  by-plat:
    version: 2.0
  income-summary:
    version: 2.0
```
```sql
create table if not exists public.tb_item_price_base_finance_detail
(
    finance_detail_id  bigint       not null
        constraint tb_item_price_base_finance_detail_key1
            primary key,
    package_id         bigint       not null,
    combine_package_code varchar(50)       not null,
    create_date        timestamp(6) not null,
    update_date        timestamp(6) not null,
    updater_id         bigint       not null,
    updater_name       varchar(50)  not null,
    creator_id         bigint       not null,
    creator_name       varchar(50)  not null,
    is_delete          smallint     not null
);
comment on table public.tb_item_price_base_finance_detail is '项目价格财务基准包套餐详情';
comment on column public.tb_item_price_base_finance_detail.finance_detail_id is '详情ID';
comment on column public.tb_item_price_base_finance_detail.package_id is '基准包ID';
comment on column public.tb_item_price_base_finance_detail.combine_package_code is '财务套餐code';
comment on column public.tb_item_price_base_finance_detail.create_date is '创建时间';
comment on column public.tb_item_price_base_finance_detail.update_date is '更新时间';
comment on column public.tb_item_price_base_finance_detail.updater_id is '更新人ID';
comment on column public.tb_item_price_base_finance_detail.updater_name is '更新人名称';
comment on column public.tb_item_price_base_finance_detail.creator_id is '创建人ID';
comment on column public.tb_item_price_base_finance_detail.creator_name is '创建人名称';
comment on column public.tb_item_price_base_finance_detail.is_delete is '1:删除，0:未删';
alter table public.tb_item_price_base_finance_detail
    owner to root;

create table if not exists public.tb_item_price_combine_package
(
  combine_package_id    bigint             not null
    constraint tb_item_price_combine_package_pk
      primary key,
  combine_package_name  varchar(50)        not null,
  org_id                bigint             not null,
  org_name              varchar(50)        not null,
  create_date           timestamp(6)       not null,
  update_date           timestamp(6)       not null,
  update_id             bigint             not null,
  update_name           varchar(50)        not null,
  create_id             bigint             not null,
  create_name           varchar(50)        not null,
  enable                smallint default 1 not null,
  is_delete             smallint default 0 not null,
  combine_package_code  varchar(20)        not null,
  combine_package_price numeric(12, 4)     not null,
  item_count            bigint   default 0 not null,
  hsp_org_code          varchar(64)        not null,
  hsp_org_name          varchar(64)        not null,
  hsp_org_id            bigint             not null,
  item_md5              varchar(255)       not null
);

comment on table public.tb_item_price_combine_package is '财务套餐包';

comment on column public.tb_item_price_combine_package.combine_package_id is '套餐包主键id';

comment on column public.tb_item_price_combine_package.combine_package_name is '套餐包名称';

comment on column public.tb_item_price_combine_package.org_id is '机构id';

comment on column public.tb_item_price_combine_package.org_name is '机构名称';

comment on column public.tb_item_price_combine_package.create_date is '创建时间';

comment on column public.tb_item_price_combine_package.update_date is '更新时间';

comment on column public.tb_item_price_combine_package.update_id is '跟新人id';

comment on column public.tb_item_price_combine_package.update_name is '更新人名称';

comment on column public.tb_item_price_combine_package.create_id is '创建人id';

comment on column public.tb_item_price_combine_package.create_name is '创建人名称';

comment on column public.tb_item_price_combine_package.enable is '是否启用 0否1是';

comment on column public.tb_item_price_combine_package.is_delete is '是否删除 0否1是';

comment on column public.tb_item_price_combine_package.combine_package_code is '套餐包编码';

comment on column public.tb_item_price_combine_package.combine_package_price is '套餐收费价格';

comment on column public.tb_item_price_combine_package.item_count is '套餐项目数量';

comment on column public.tb_item_price_combine_package.hsp_org_code is '送检机构编码';

comment on column public.tb_item_price_combine_package.hsp_org_name is '送检机构名称';

comment on column public.tb_item_price_combine_package.hsp_org_id is '送检机构id';

comment on column public.tb_item_price_combine_package.item_md5 is '检验项目MD5值';

alter table public.tb_item_price_combine_package
  owner to root;

create table if not exists public.tb_item_price_combine_package_detail
(
  detail_id            bigint       not null
    constraint tb_item_price_combine_package_detail_pk
      primary key,
  combine_package_code varchar(50),
  test_item_id         bigint       not null,
  create_date          timestamp(6) not null,
  update_date          timestamp(6) not null,
  update_id            bigint       not null,
  update_name          varchar(50)  not null,
  create_id            bigint       not null,
  create_name          varchar(50)  not null,
  is_delete            smallint     not null
);

comment on table public.tb_item_price_combine_package_detail is '财务套餐 的项目';

comment on column public.tb_item_price_combine_package_detail.detail_id is '套餐包明细表主键id';

comment on column public.tb_item_price_combine_package_detail.combine_package_code is '报餐包code';

comment on column public.tb_item_price_combine_package_detail.test_item_id is '检验项目id';

comment on column public.tb_item_price_combine_package_detail.create_date is '创建时间';

comment on column public.tb_item_price_combine_package_detail.update_date is '更新时间';

comment on column public.tb_item_price_combine_package_detail.update_id is '跟新人id';

comment on column public.tb_item_price_combine_package_detail.update_name is '更新人名称';

comment on column public.tb_item_price_combine_package_detail.create_id is '创建人id';

comment on column public.tb_item_price_combine_package_detail.create_name is '创建人名称';

comment on column public.tb_item_price_combine_package_detail.is_delete is '删除标识 0否1是';

alter table public.tb_item_price_combine_package_detail
  owner to root;


```
# 1.1.1.1
```sql
-- 添加字段
alter table tb_medicine_germ_relation add fold_point_scope varchar(128);
comment on column tb_medicine_germ_relation.fold_point_scope is '折点范围';
-- 刷数据
update tb_medicine_germ_relation set fold_point_scope = '' where true;

-- 添加字段
alter table tb_microbiology_germ_medicine add fold_point_scope varchar(128);
comment on column tb_microbiology_germ_medicine.fold_point_scope is '折点范围';
-- 刷数据
update tb_microbiology_germ_medicine set fold_point_scope = '' where true;
```
# 1.1.1

```nacos
labway-lims-apply.yaml 中添加：

business-center:
  org-code: 00010110000000001WLF  #值是业务中台的机构编码
```

```sql
-- 补录字段对比设置
create table if not exists public.tb_supplemental_record_field_setting
(
  supplemental_record_field_setting_id bigint       not null
    constraint tb_supplemental_record_field_setting_pk
      primary key,
  hsp_org_id                           bigint       not null,
  hsp_org_code                         varchar(50)  not null,
  hsp_org_name                         varchar(50)  not null,
  supplemental_record_field            varchar(500) not null,
  update_date                          timestamp    not null,
  create_date                          timestamp    not null,
  updater_id                           bigint       not null,
  updater_name                         varchar(50)  not null,
  creator_id                           bigint       not null,
  creator_name                         varchar(50)  not null,
  is_delete                            integer      not null
);

comment on table public.tb_supplemental_record_field_setting is '补录字段设置';

comment on column public.tb_supplemental_record_field_setting.supplemental_record_field_setting_id is '补录字段设置id';

comment on column public.tb_supplemental_record_field_setting.hsp_org_id is '送检机构id';

comment on column public.tb_supplemental_record_field_setting.hsp_org_code is '送检机构code';

comment on column public.tb_supplemental_record_field_setting.hsp_org_name is '送检机构名称';

comment on column public.tb_supplemental_record_field_setting.supplemental_record_field is '对照字段';

alter table public.tb_supplemental_record_field_setting
  owner to root;


--  条码管理增加字段类型
alter table tb_barcode_setting add barcode_type smallint ;

comment on column tb_barcode_setting.barcode_type is '条码类型  1条码  2主条码';

update tb_barcode_setting set barcode_type = 1 where true;

alter table tb_barcode_setting alter column barcode_type set not null;

--PDA 申请单项目
create table if not exists public.tb_pda_apply_sample_item
(
  pda_apply_sample_item_id bigint                                     not null
    constraint tb_pda_apply_sample_item_pk
      primary key,
  pda_apply_id             bigint                                     not null,
  test_item_id             bigint                                     not null,
  test_item_code           varchar(50)                                not null,
  test_item_name           varchar(255)                               not null,
  item_type                varchar(50)                                not null,
  sample_type_name         varchar(50)                                not null,
  sample_type_code         varchar(50)                                not null,
  tube_name                varchar(50)                                not null,
  tube_code                varchar(50)                                not null,
  split_code               varchar(50)                                not null,
  urgent                   smallint                                   not null,
  count                    integer                                    not null,
  group_id                 bigint                                     not null,
  group_name               varchar(50)                                not null,
  create_date              timestamp                                  not null,
  update_date              timestamp                                  not null,
  creator_id               bigint                                     not null,
  creator_name             varchar(50)                                not null,
  updater_id               bigint                                     not null,
  updater_name             varchar(50)                                not null,
  is_delete                smallint                                   not null,
  remark                   varchar(255)                               not null,
  bloodculture_item        varchar(500) default ''::character varying not null
);

comment on table public.tb_pda_apply_sample_item is 'pda申请单样本项目';

comment on column public.tb_pda_apply_sample_item.pda_apply_sample_item_id is '申请单样本项目ID';

comment on column public.tb_pda_apply_sample_item.pda_apply_id is '申请单ID';

comment on column public.tb_pda_apply_sample_item.test_item_id is '检验项目ID';

comment on column public.tb_pda_apply_sample_item.test_item_code is '检验项目编码';

comment on column public.tb_pda_apply_sample_item.test_item_name is '检验项目';

comment on column public.tb_pda_apply_sample_item.item_type is '项目类型';

comment on column public.tb_pda_apply_sample_item.sample_type_name is '样本类型';

comment on column public.tb_pda_apply_sample_item.sample_type_code is '样本类型编码';

comment on column public.tb_pda_apply_sample_item.tube_name is '管型';

comment on column public.tb_pda_apply_sample_item.tube_code is '管型编码';

comment on column public.tb_pda_apply_sample_item.split_code is '分隔码';

comment on column public.tb_pda_apply_sample_item.urgent is '1:急 0:不急';

comment on column public.tb_pda_apply_sample_item.count is '数量';

comment on column public.tb_pda_apply_sample_item.group_id is '专业组id';

comment on column public.tb_pda_apply_sample_item.group_name is '专业组';

comment on column public.tb_pda_apply_sample_item.create_date is '创建时间';

comment on column public.tb_pda_apply_sample_item.update_date is '修改时间';

comment on column public.tb_pda_apply_sample_item.creator_id is '创建人id';

comment on column public.tb_pda_apply_sample_item.creator_name is '创建人名称';

comment on column public.tb_pda_apply_sample_item.updater_id is '修改人id';

comment on column public.tb_pda_apply_sample_item.updater_name is '修改人名称';

comment on column public.tb_pda_apply_sample_item.is_delete is '1:删除，0:不删';

comment on column public.tb_pda_apply_sample_item.remark is '备注';

comment on column public.tb_pda_apply_sample_item.bloodculture_item is '血培养信息';

alter table public.tb_pda_apply_sample_item
  owner to root;

-- PDA 申请单
create table if not exists public.tb_pda_apply
(
  pda_apply_id         bigint                                     not null
    constraint tb_pda_apply_pk
      primary key,
  master_barcode       varchar(20)                                not null,
  patient_name         varchar(255)                               not null,
  patient_age          integer                                    not null,
  patient_subage       integer                                    not null,
  patient_subage_unit  char                                       not null,
  patient_birthday     date,
  patient_card         varchar(50)                                not null,
  patient_card_type    varchar(50)                                not null,
  patient_bed          varchar(50)                                not null,
  patient_sex          smallint                                   not null,
  patient_visit_card   varchar(50)                                not null,
  patient_mobile       varchar(50)                                not null,
  patient_address      varchar(255)                               not null,
  source               varchar(50)                                not null,
  supplier             varchar(50)                                not null,
  apply_type_code      varchar(50),
  remark               varchar(255)                               not null,
  sample_count         integer                                    not null,
  sample_property      varchar(255)                               not null,
  dept                 varchar(50)                                not null,
  diagnosis            varchar(255)                               not null,
  send_doctor_name     varchar(50)                                not null,
  send_doctor_code     varchar(50)                                not null,
  hsp_org_id           bigint                                     not null,
  hsp_org_code         varchar(50)                                not null,
  hsp_org_name         varchar(50)                                not null,
  org_id               bigint                                     not null,
  org_name             varchar(50)                                not null,
  urgent               smallint                                   not null,
  apply_date           timestamp                                  not null,
  sampling_date        timestamp                                  not null,
  create_date          timestamp                                  not null,
  update_date          timestamp                                  not null,
  creator_name         varchar(50)                                not null,
  creator_id           bigint                                     not null,
  updater_name         varchar(50)                                not null,
  updater_id           bigint                                     not null,
  is_delete            smallint                                   not null,
  sample_property_code varchar(50)                                not null,
  apply_type_name      varchar(50),
  original_org_code    varchar(50)  default ''::character varying not null,
  original_org_name    varchar(255) default ''::character varying not null,
  out_barcode          varchar(50)                                not null
);

comment on table public.tb_pda_apply is '申请单';

comment on column public.tb_pda_apply.pda_apply_id is 'pda申请单ID';

comment on column public.tb_pda_apply.master_barcode is '主条码号';

comment on column public.tb_pda_apply.patient_name is '病人名称';

comment on column public.tb_pda_apply.patient_age is '病人年龄';

comment on column public.tb_pda_apply.patient_subage is '子年龄';

comment on column public.tb_pda_apply.patient_subage_unit is '子年龄单位，月、周、日';

comment on column public.tb_pda_apply.patient_birthday is '生日';

comment on column public.tb_pda_apply.patient_card is '卡号';

comment on column public.tb_pda_apply.patient_card_type is '类型，1:身份证';

comment on column public.tb_pda_apply.patient_bed is '床号';

comment on column public.tb_pda_apply.patient_sex is '性别，1:男，2:女';

comment on column public.tb_pda_apply.patient_visit_card is '就诊卡号';

comment on column public.tb_pda_apply.patient_mobile is '手机号';

comment on column public.tb_pda_apply.patient_address is '地址';

comment on column public.tb_pda_apply.source is '来源';

comment on column public.tb_pda_apply.supplier is '供应商';

comment on column public.tb_pda_apply.apply_type_code is '就诊类型';

comment on column public.tb_pda_apply.remark is '备注';

comment on column public.tb_pda_apply.sample_count is '样本数量';

comment on column public.tb_pda_apply.sample_property is '样本性状';

comment on column public.tb_pda_apply.dept is '部门';

comment on column public.tb_pda_apply.diagnosis is '临床诊断';

comment on column public.tb_pda_apply.send_doctor_name is '送检医生';

comment on column public.tb_pda_apply.send_doctor_code is '送检医生编码';

comment on column public.tb_pda_apply.hsp_org_id is '送检机构ID';

comment on column public.tb_pda_apply.hsp_org_code is '送检机构编码';

comment on column public.tb_pda_apply.hsp_org_name is '送检机构名称';

comment on column public.tb_pda_apply.org_id is '机构ID';

comment on column public.tb_pda_apply.org_name is '机构名称';

comment on column public.tb_pda_apply.urgent is '是否急诊，1:是，0:不是';

comment on column public.tb_pda_apply.apply_date is '申请日期';

comment on column public.tb_pda_apply.sampling_date is '采样时间';

comment on column public.tb_pda_apply.create_date is '创建时间';

comment on column public.tb_pda_apply.update_date is '更新时间';

comment on column public.tb_pda_apply.creator_name is '创建人';

comment on column public.tb_pda_apply.creator_id is '创建人';

comment on column public.tb_pda_apply.updater_name is '更新人';

comment on column public.tb_pda_apply.updater_id is '更新人';

comment on column public.tb_pda_apply.is_delete is '1:删除，0:没有删除';

comment on column public.tb_pda_apply.sample_property_code is '样本性状编码';

comment on column public.tb_pda_apply.apply_type_name is '就诊类型名称';

comment on column public.tb_pda_apply.original_org_code is '原始机构编码';

comment on column public.tb_pda_apply.original_org_name is '原始机构名称';

comment on column public.tb_pda_apply.out_barcode is '外部条码号';

alter table public.tb_pda_apply
  owner to root;

create index if not exists tb_pda_apply_source_index
  on public.tb_pda_apply (source desc);

create index if not exists tb_pda_apply_create_date_index
  on public.tb_pda_apply (create_date desc);

--PDA确认表
create table if not exists public.tb_pda_tobe_confirmed_apply
(
  pda_tobe_confirmed_apply_id bigint       not null
    constraint tb_pda_tobe_confirmed_apply_pk
      primary key,
  master_barcode              varchar(20)  not null,
  confirmed_pda_apply_id      bigint       not null,
  hsp_org_id                  bigint       not null,
  hsp_org_code                varchar(50)  not null,
  hsp_org_name                varchar(50)  not null,
  status                      integer      not null,
  create_date                 timestamp    not null,
  update_date                 timestamp    not null,
  creator_name                varchar(50)  not null,
  creator_id                  bigint       not null,
  updater_name                varchar(50)  not null,
  updater_id                  bigint       not null,
  is_delete                   smallint     not null,
  pda_imgs                    varchar(500) not null
);

comment on table public.tb_pda_tobe_confirmed_apply is 'pda申请单双属确认表';

comment on column public.tb_pda_tobe_confirmed_apply.master_barcode is '主条码号';

comment on column public.tb_pda_tobe_confirmed_apply.hsp_org_id is '送检机构ID';

comment on column public.tb_pda_tobe_confirmed_apply.hsp_org_code is '送检机构编码';

comment on column public.tb_pda_tobe_confirmed_apply.hsp_org_name is '送检机构名称';

comment on column public.tb_pda_tobe_confirmed_apply.status is '状态
0 : 未确认
1 : 已确认';

comment on column public.tb_pda_tobe_confirmed_apply.pda_imgs is 'pda图片列表';

comment on column public.tb_pda_tobe_confirmed_apply.confirmed_pda_apply_id is '确认的PDA ApplyId';
alter table public.tb_pda_tobe_confirmed_apply
  owner to root;



-- 仪器报告项目结果值转换
alter table tb_instrument_report_item_result_tip
  add "formula_max" varchar(10);
comment on column tb_instrument_report_item_result_tip.formula_max is '≥, >, =, <, ≤';

alter table tb_instrument_report_item_result_tip
  add formula_max_value varchar(32);
comment on column tb_instrument_report_item_result_tip.formula_max_value is '条件值';

alter table tb_instrument_report_item_result_tip
  add "formula_min" varchar(10);
comment on column tb_instrument_report_item_result_tip."formula_min" is '< , ≤';

alter table tb_instrument_report_item_result_tip
  add formula_min_value varchar(32);
comment on column tb_instrument_report_item_result_tip.formula_min_value is '条件值';

-- 更新数据， 以防回滚
update tb_instrument_report_item_result_tip set formula_max = formula, formula_max_value = formula_value where true


```




# *******
```yml

nacos: labway-lims-routine.yaml下添加

result-service:
  version: 1.0

# 导出Excel公司信息行
excel:
  company:
    content: >
      广州兰卫医学检验实验室有限公司

      开户银行：工商银行广州中山六路支行        银行账号：3602014109200137868

      TEL:020-87603168FAX020-87603168

      如有疑问敬请来电，谢谢

      =======================================================================================================================

      对账回执单

      1、数据无误
              单位签章：                   日期：



      2、数据不符及需说明的事项
              单位签章：                   日期


```

# *******
```sql

nginx域名配置转发：https://center.labway.cn/compare-result/

## labway-lims-common.yaml 文件中添加
business:
  center:
    result-api: https://center.labway.cn/compare-result/
    
## 网关放行路径配置 labway-lims-gateway.yaml 文件中添加
exclude-path:
  patterns:
    - "/apply/report/merge-print-business"

```

# 1.1.0.3

```sql
## 查询统计导出优化
create table public.tb_export_file
(
    export_file_id bigint not null constraint tb_export_file_pk primary key,
    file_name      varchar(255) not null,
    file_type      varchar(255) not null,
    url            varchar(255),
    status         integer      not null,
    source         varchar(255) not null,
    update_date    timestamp    not null,
    create_date    timestamp    not null,
    updater_id     bigint       not null,
    updater_name   varchar(50)  not null,
    creator_id     bigint       not null,
    creator_name   varchar(50)  not null,
    org_id         bigint       not null,
    org_name       varchar(50)  not null,
    is_delete      integer      not null
);

comment on column public.tb_export_file.export_file_id is '文件ID';
comment on column public.tb_export_file.file_name is '文件名';
comment on column public.tb_export_file.file_type is 'zip,xlsx';
comment on column public.tb_export_file.url is '文件地址';
comment on column public.tb_export_file.status is '1正在导出，2导出成功，3导出失败，4已取消，-1未知';
comment on column public.tb_export_file.source is '来源页面';
comment on column public.tb_export_file.update_date is '更新时间';
comment on column public.tb_export_file.create_date is '创建时间';
comment on column public.tb_export_file.updater_id is '更新人ID';
comment on column public.tb_export_file.updater_name is '更新人名称';
comment on column public.tb_export_file.creator_id is '创建人ID';
comment on column public.tb_export_file.creator_name is '创建人名称';
comment on column public.tb_export_file.org_id is '机构ID';
comment on column public.tb_export_file.org_name is '机构名称';
comment on column public.tb_export_file.is_delete is '1删除。0:未删除';
alter table public.tb_export_file owner to root;
```


# 1.1.0
```sql
alter table tb_medicine_germ_relation
    add susceptibility varchar(50) default '' not null;
comment on column tb_medicine_germ_relation.susceptibility is '敏感度';

alter table tb_group_material_apply_detail
  add status integer;

comment on column tb_group_material_apply_detail.status is '状态，默认物料申请单的状态， 99驳回';
``` 

```sql
#建库语句
-- auto-generated definition
create table tb_material_refund_record
(
  refund_id              bigint             not null
    constraint tb_material_refund_record_pk
      primary key,
  refund_no              varchar(50)        not null,
  income_no              varchar(50),
  delivery_no            varchar(50)        not null,
  delivery_detail_id     bigint             not null,
  material_id            bigint             not null,
  material_code          varchar(255)       not null,
  material_name          varchar(255)       not null,
  specification          varchar(255)       not null,
  batch_no               varchar(50)        not null,
  manufacturers          varchar(255)       not null,
  main_unit              varchar(255)       not null,
  delivery_main_number   integer            not null,
  income_main_number     integer            not null,
  assist_unit            varchar(255)       not null,
  delivery_assist_number numeric(10, 4)     not null,
  income_assist_number   numeric(10, 4)     not null,
  unit_conversion_rate   varchar(255)       not null,
  valid_date             timestamp          not null,
  group_id               bigint             not null,
  group_name             varchar(50)        not null,
  org_id                 bigint             not null,
  org_name               varchar(50)        not null,
  create_date            timestamp          not null,
  creator_id             bigint             not null,
  creator_name           varchar(50)        not null,
  update_date            timestamp          not null,
  updater_id             bigint             not null,
  updater_name           varchar(50)        not null,
  is_delete              smallint default 0 not null,
  refund_type            smallint           not null,
  refund_reason          varchar(255),
  refund_main_number     integer            not null,
  refund_assist_number   numeric(10, 4)     not null,
  push_flag              integer  default 0 not null
);

comment on table tb_material_refund_record is '物流退库记录表';

comment on column tb_material_refund_record.refund_id is '退库id';

comment on constraint tb_material_refund_record_pk on tb_material_refund_record is '退库单主键';

comment on column tb_material_refund_record.refund_no is '退库单号';

comment on column tb_material_refund_record.income_no is '入库单号';

comment on column tb_material_refund_record.delivery_no is '出库单号';

comment on column tb_material_refund_record.delivery_detail_id is '物料出库详情id';

comment on column tb_material_refund_record.material_id is '物料ID';

comment on column tb_material_refund_record.material_code is '物资编号';

comment on column tb_material_refund_record.material_name is '物资名称';

comment on column tb_material_refund_record.specification is '规格';

comment on column tb_material_refund_record.batch_no is '批号';

comment on column tb_material_refund_record.manufacturers is '厂家';

comment on column tb_material_refund_record.main_unit is '主单位';

comment on column tb_material_refund_record.delivery_main_number is '出库主单位数量';

comment on column tb_material_refund_record.income_main_number is '入库主单位数量';

comment on column tb_material_refund_record.assist_unit is '辅单位';

comment on column tb_material_refund_record.delivery_assist_number is '出库辅单位数量';

comment on column tb_material_refund_record.income_assist_number is '入库辅单位数量';

comment on column tb_material_refund_record.unit_conversion_rate is '主辅单位换算率';

comment on column tb_material_refund_record.valid_date is '有效期';

comment on column tb_material_refund_record.group_id is '专业组ID';

comment on column tb_material_refund_record.group_name is '专业组名称';

comment on column tb_material_refund_record.org_id is '检验机构';

comment on column tb_material_refund_record.org_name is '检验机构名称';

comment on column tb_material_refund_record.create_date is '创建时间';

comment on column tb_material_refund_record.creator_id is '创建人ID';

comment on column tb_material_refund_record.creator_name is '创建人名称';

comment on column tb_material_refund_record.update_date is '更新时间';

comment on column tb_material_refund_record.updater_id is '更新人ID';

comment on column tb_material_refund_record.updater_name is '更新人名称';

comment on column tb_material_refund_record.is_delete is '是否删除 0否1是';

comment on column tb_material_refund_record.refund_type is '退库单类型 1退库 2拒收';

comment on column tb_material_refund_record.refund_reason is '退库/拒收原因';

comment on column tb_material_refund_record.refund_main_number is '退库/拒收主数量';

comment on column tb_material_refund_record.refund_assist_number is '退库/拒收辅数量';

comment on column tb_material_refund_record.push_flag is '发送业务中台标识 0未发送 1已发送';

alter table tb_material_refund_record
  owner to root;
```

## nacos配置 ayyly服务
```yml
business:
  refund:
    url: https://center.labway.cn/material-server/refund/create-refund
```

# 1.8.0.1
```sql
alter table tb_sample_report
    add "is_upload_pdf" smallint default 0 not null;
comment on column tb_sample_report."is_upload_pdf" is '1: 是文件上传的pdf 0:不是';
```

****
# 1.0.7
## 数据库
```sql

ALTER TABLE "public"."tb_report_template_bind" ADD COLUMN "hsp_org_id" int8;

ALTER TABLE "public"."tb_report_template_bind" ADD COLUMN "hsp_org_name" varchar(255) COLLATE "pg_catalog"."default";

ALTER TABLE "public"."tb_report_template_bind" ADD COLUMN "bind_group_id" int8;

COMMENT ON COLUMN "public"."tb_report_template_bind"."hsp_org_id" IS '送检机构ID';

COMMENT ON COLUMN "public"."tb_report_template_bind"."hsp_org_name" IS '送检机构名称';

COMMENT ON COLUMN "public"."tb_report_template_bind"."bind_group_id" IS '绑定组ID';
        
# 刷数据
#初始化bindGroupId
update "tb_report_template_bind" set bind_group_id = CAST(1000000000 + floor(random() * 9000000000) AS bigint)
  #送检机构更新
update "tb_report_template_bind" set hsp_org_id = biz_id where bind_type=2
update "tb_report_template_bind" set hsp_org_id = 0 where bind_type!=2
#刷新机构名称
UPDATE tb_report_template_bind c
SET hsp_org_name = o.hsp_org_name
  FROM tb_hsp_organization o
WHERE c.hsp_org_id = o.hsp_org_id;

# 物料相关升级
alter table public.tb_material_income_record
  add material_barcode varchar(255) default '';
comment on column public.tb_material_income_record.material_barcode is '物料条码';

alter table public.tb_material_inventory
  add material_barcode varchar(255) default '';

comment on column public.tb_material_inventory.material_barcode is '物料条码';

alter table public.tb_material_receive_record
  add material_barcode varchar(255) default '';

comment on column public.tb_material_receive_record.material_barcode is '物料条码号';
    
# 常规检验升级
alter table public.tb_apply_sample
  add color_marking integer;

comment on column public.tb_apply_sample.color_marking is ' 10 或者 11: 未审    20 : 一审  30 : 已审  40 : 反审 50 : 重审';

-- 刷数据
update tb_apply_sample set color_marking=status where  1=1;

```


# *******
## 数据库
```sql

alter table public.tb_instrument_report_item_result_tip
alter
column report_item_code type text using report_item_code::text;

alter table public.tb_instrument_report_item_result_tip
alter
column report_item_name type text using report_item_name::text;


```

# ca认证

## pdf服务配置ca链接
```yml
nacos: labway-lims-pdfreport.yaml下添加

ca:
  appKey: kdMkwO9mYCjp3LK5kXWZZf5oqxQ5om
  appSecret: 4Wdwn08RpniKEnF2JjIsACS2sPqKgh
  urlPrefix: http://***********:7998
```
## labway-lims-common.yaml
```yml
hsp-org:
  hspOrgCodes:
    - FJKH000325
    - 456
    - 789
  caPdfMap:
    'CA_ROUTINE':  # CA 认证模板 编码Code
      sealName: '王毅红'  # ca平台 签章名称
      keyword: '医疗机构执业许可证登记号'  # 签章关键字位置
      x: 70F  # 偏移量 像素
      y: 0F  # 偏移量 像素
      
    'CA_ROUTINE_2':  # CA 认证模板 编码Code
      sealName: '王毅红'  # ca平台 签章名称
      keyword: '医疗机构执业许可证登记号'  # 签章关键字位置
      x: 70F  # 偏移量 像素
      y: 0F  # 偏移量 像素
```

# *******

## 数据库

```sql
-- auto-generated definition
create table tb_bloodculture_sample
(
    bloodculture_sample_id     bigint      not null
        constraint tb_bloodculture_sample_pk
            primary key,
    apply_sample_id            bigint      not null,
    apply_id                   bigint      not null,
    barcode                    varchar(50) not null,
    sample_no                  varchar(50) not null,
    group_id                   bigint,
    group_name                 varchar(50) not null,
    instrument_group_id        bigint      not null,
    instrument_group_name      varchar(50) not null,
    instrument_id              bigint      not null,
    instrument_code            varchar(50) not null,
    instrument_name            varchar(50) not null,
    test_date                  timestamp,
    one_checker_id             bigint      not null,
    one_checker_name           varchar(50) not null,
    one_check_date             timestamp   not null,
    two_checker_id             bigint      not null,
    two_checker_name           varchar(50) not null,
    two_check_date             timestamp   not null,
    one_check_sample_report_id bigint      not null,
    two_check_sample_report_id bigint      not null,
    create_date                timestamp   not null,
    creator_id                 bigint      not null,
    creator_name               varchar(50) not null,
    update_date                timestamp   not null,
    updater_id                 bigint      not null,
    updater_name               varchar(50) not null,
    hsp_org_id                 bigint      not null,
    hsp_org_name               varchar(50) not null,
    org_id                     bigint      not null,
    org_name                   varchar(50) not null,
    is_delete                  integer     not null
);

comment on column tb_bloodculture_sample.apply_sample_id is '申请单样本id';

comment on column tb_bloodculture_sample.apply_id is '申请单id';

comment on column tb_bloodculture_sample.barcode is '条码号';

comment on column tb_bloodculture_sample.sample_no is '样本号';

comment on column tb_bloodculture_sample.group_name is '专业组名称';

comment on column tb_bloodculture_sample.instrument_group_id is '专业小组id';

comment on column tb_bloodculture_sample.instrument_group_name is '专业小组';

comment on column tb_bloodculture_sample.instrument_id is '仪器信息';

comment on column tb_bloodculture_sample.instrument_code is '仪器编码';

comment on column tb_bloodculture_sample.instrument_name is '仪器名称';

comment on column tb_bloodculture_sample.test_date is '检验日期';

comment on column tb_bloodculture_sample.one_checker_id is '一审人id';

comment on column tb_bloodculture_sample.one_checker_name is '一审人';

comment on column tb_bloodculture_sample.one_check_date is '一审时间';

comment on column tb_bloodculture_sample.two_checker_id is '二审人id';

comment on column tb_bloodculture_sample.two_checker_name is '二审人';

comment on column tb_bloodculture_sample.two_check_date is '二审时间';

comment on column tb_bloodculture_sample.one_check_sample_report_id is '初审报告id';

comment on column tb_bloodculture_sample.two_check_sample_report_id is '终审报告id';

comment on column tb_bloodculture_sample.create_date is '创建时间';

comment on column tb_bloodculture_sample.creator_id is '创建人id';

comment on column tb_bloodculture_sample.creator_name is '创建人名称';

comment on column tb_bloodculture_sample.update_date is '更新时间';

comment on column tb_bloodculture_sample.updater_id is '更新人id';

comment on column tb_bloodculture_sample.updater_name is '更新人名称';

comment on column tb_bloodculture_sample.hsp_org_id is '送检机构id';

comment on column tb_bloodculture_sample.hsp_org_name is '送检机构';

comment on column tb_bloodculture_sample.org_id is '检验机构';

comment on column tb_bloodculture_sample.org_name is '机构名称';

comment on column tb_bloodculture_sample.is_delete is '1:删除，0：没有';

alter table tb_bloodculture_sample
    owner to root;

GRANT ALL ON TABLE tb_bloodculture_sample TO labway_lims;
          
          
-- auto-generated definition
create table tb_bloodculture_sample_remark
(
  bloodculture_sample_remark_id bigint       not null
    constraint tb_bloodculture_sample_remark_pk
      primary key,
  apply_sample_id               bigint       not null,
  bloodculture_sample_id        bigint       not null,
  apply_id                      bigint       not null,
  remark                        varchar(255) not null,
  position                      varchar(50)  not null,
  create_date                   timestamp    not null,
  creator_id                    bigint       not null,
  creator_name                  varchar(50)  not null,
  update_date                   timestamp    not null,
  updater_id                    bigint       not null,
  updater_name                  varchar(50)  not null,
  org_id                        bigint       not null,
  org_name                      varchar(50)  not null,
  is_delete                     integer      not null
);

comment on column tb_bloodculture_sample_remark.apply_sample_id is '申请单样本id';

comment on column tb_bloodculture_sample_remark.apply_id is '申请单id';

comment on column tb_bloodculture_sample_remark.create_date is '创建时间';

comment on column tb_bloodculture_sample_remark.creator_id is '创建人id';

comment on column tb_bloodculture_sample_remark.creator_name is '创建人名称';

comment on column tb_bloodculture_sample_remark.update_date is '更新时间';

comment on column tb_bloodculture_sample_remark.updater_id is '更新人id';

comment on column tb_bloodculture_sample_remark.updater_name is '更新人名称';

comment on column tb_bloodculture_sample_remark.org_id is '检验机构';

comment on column tb_bloodculture_sample_remark.org_name is '机构名称';

comment on column tb_bloodculture_sample_remark.is_delete is '1:删除，0：没有';

alter table tb_bloodculture_sample_remark
  owner to root;

GRANT ALL ON TABLE tb_bloodculture_sample_remark TO labway_lims;


-- auto-generated definition
create table tb_bloodculture_sample_result
(
  bloodculture_sample_result_id bigint       not null
    constraint tb_bloodculture_sample_result_pk
      primary key,
  apply_sample_id               bigint       not null,
  bloodculture_sample_id        bigint       not null,
  apply_id                      bigint       not null,
  result                        varchar(255) not null,
  position                      varchar(50)  not null,
  create_date                   timestamp    not null,
  creator_id                    bigint       not null,
  creator_name                  varchar(50)  not null,
  update_date                   timestamp    not null,
  updater_id                    bigint       not null,
  updater_name                  varchar(50)  not null,
  org_id                        bigint       not null,
  org_name                      varchar(50)  not null,
  is_delete                     integer      not null,
  result_code                   varchar(255)
);

comment on column tb_bloodculture_sample_result.apply_sample_id is '申请单样本id';

comment on column tb_bloodculture_sample_result.apply_id is '申请单id';

comment on column tb_bloodculture_sample_result.create_date is '创建时间';

comment on column tb_bloodculture_sample_result.creator_id is '创建人id';

comment on column tb_bloodculture_sample_result.creator_name is '创建人名称';

comment on column tb_bloodculture_sample_result.update_date is '更新时间';

comment on column tb_bloodculture_sample_result.updater_id is '更新人id';

comment on column tb_bloodculture_sample_result.updater_name is '更新人名称';

comment on column tb_bloodculture_sample_result.org_id is '检验机构';

comment on column tb_bloodculture_sample_result.org_name is '机构名称';

comment on column tb_bloodculture_sample_result.is_delete is '1:删除，0：没有';

comment on column tb_bloodculture_sample_result.result_code is '结果编码';

alter table tb_bloodculture_sample_result
  owner to root;

GRANT ALL ON TABLE tb_bloodculture_sample_result TO labway_lims;



```
# 1.0.8 南京
## 数据库
```sql
create table tb_barcode_setting
(
  barcode_setting_id bigint      not null
    constraint tb_barcode_setting_pk
      primary key,
  hsp_org_id         bigint      not null,
  hsp_org_code       varchar(50) not null,
  hsp_org_name       varchar(50) not null,
  start_code         varchar(50) not null,
  current_number     bigint      not null,
  barcode_place      integer     not null,
  enable             boolean     not null,
  creater_id         bigint      not null,
  creater_name       varchar(50) not null,
  updater_id         bigint      not null,
  updater_name       varchar(50) not null,
  is_delete          smallint    not null,
  creater_date       timestamp   not null,
  updater_date       timestamp   not null
);

comment on table tb_barcode_setting is '送检机构条码规则';

comment on column tb_barcode_setting.barcode_setting_id is '雪花';

comment on column tb_barcode_setting.hsp_org_id is '机构id';

comment on column tb_barcode_setting.hsp_org_code is '机构编码';

comment on column tb_barcode_setting.hsp_org_name is '机构名称';

comment on column tb_barcode_setting.start_code is '起始编码';

comment on column tb_barcode_setting.current_number is '当前序号';

comment on column tb_barcode_setting.barcode_place is '位数';

comment on column tb_barcode_setting.enable is '是否启用';

comment on column tb_barcode_setting.creater_id is '新增人id';

comment on column tb_barcode_setting.creater_name is '新增人名';

comment on column tb_barcode_setting.updater_id is '修改人id';

comment on column tb_barcode_setting.updater_name is '修改人名';

comment on column tb_barcode_setting.is_delete is '1 删除  0没有删除';

alter table tb_barcode_setting
  owner to root;

```

# 并单
```sql
alter table tb_apply_sample
  add merge_master_barcode varchar(50);

comment on column tb_apply_sample.merge_master_barcode is '并单的主条码';

alter table tb_apply_sample
  add merge_extra_info varchar(1000);

comment on column tb_apply_sample.merge_extra_info is '并单的额外数据';

```


# 1.0.6.5

## 数据库

```sql
-- 快捷键设置 值为json
alter table tb_system_param
    alter column param_value type varchar(3000) using param_value::varchar(3000);

create table tb_report_delay
(
  delay_id         bigint            not null
    constraint tb_apply_sample_item_pkey_copy_1
      primary key,
  apply_sample_id  bigint            not null,
  apply_id         bigint            not null,
  barcode          varchar(50)       not null,
  test_item_codes  text,
  reason           varchar(255)      not null,
  remark           varchar(255)      not null,
  send_report_date timestamp         not null,
  cancel_user_id   bigint,
  cancel_user_name varchar(50),
  status           integer default 0 not null,
  group_id         bigint            not null,
  group_name       varchar(50)       not null,
  org_id           bigint            not null,
  org_name         varchar(50)       not null,
  create_date      timestamp(6)      not null,
  update_date      timestamp(6)      not null,
  creator_id       bigint            not null,
  creator_name     varchar(50)       not null,
  updater_id       bigint            not null,
  updater_name     varchar(50)       not null
);

comment on table tb_report_delay is ' 报告单迟发表';

comment on column tb_report_delay.delay_id is '迟发ID';

comment on column tb_report_delay.apply_sample_id is '申请单样本ID';

comment on column tb_report_delay.apply_id is '申请单ID';

comment on column tb_report_delay.barcode is '条码号';

comment on column tb_report_delay.test_item_codes is '检验项目编码集合(空 为全部)';

comment on column tb_report_delay.reason is '迟发原因';

comment on column tb_report_delay.remark is '备注';

comment on column tb_report_delay.send_report_date is '预计发布日期';

comment on column tb_report_delay.cancel_user_id is '作废人ID';

comment on column tb_report_delay.cancel_user_name is '作废人姓名';

comment on column tb_report_delay.status is '迟发申请状态(0未打印 1已打印 9已作废)';

comment on column tb_report_delay.group_id is '专业组id';

comment on column tb_report_delay.group_name is '专业组';

comment on column tb_report_delay.org_id is '机构ID';

comment on column tb_report_delay.org_name is '机构名称';

comment on column tb_report_delay.create_date is '创建时间';

comment on column tb_report_delay.update_date is '修改时间';

comment on column tb_report_delay.creator_id is '创建人id';

comment on column tb_report_delay.creator_name is '创建人名称';

comment on column tb_report_delay.updater_id is '修改人id';

comment on column tb_report_delay.updater_name is '修改人名称';

alter table tb_report_delay
  owner to root;

create index report_delay_barcode
  on tb_report_delay (barcode);


GRANT ALL ON TABLE tb_report_delay TO labway_lims;

--  新增报告单迟发表 tb_report_delay
-- auto-generated definition
create table tb_report_delay
(
  delay_id         bigint            not null
    constraint tb_apply_sample_item_pkey_copy_1
      primary key,
  apply_sample_id  bigint            not null,
  apply_id         bigint            not null,
  barcode          varchar(50)       not null,
  test_item_codes  text,
  reason           varchar(255)      not null,
  remark           varchar(255)      not null,
  send_report_date timestamp         not null,
  cancel_user_id   bigint,
  cancel_user_name varchar(50),
  status           integer default 0 not null,
  group_id         bigint            not null,
  group_name       varchar(50)       not null,
  org_id           bigint            not null,
  org_name         varchar(50)       not null,
  create_date      timestamp(6)      not null,
  update_date      timestamp(6)      not null,
  creator_id       bigint            not null,
  creator_name     varchar(50)       not null,
  updater_id       bigint            not null,
  updater_name     varchar(50)       not null
);

comment on table tb_report_delay is ' 报告单迟发表';

comment on column tb_report_delay.delay_id is '迟发ID';

comment on column tb_report_delay.apply_sample_id is '申请单样本ID';

comment on column tb_report_delay.apply_id is '申请单ID';

comment on column tb_report_delay.barcode is '条码号';

comment on column tb_report_delay.test_item_codes is '检验项目编码集合(空 为全部)';

comment on column tb_report_delay.reason is '迟发原因';

comment on column tb_report_delay.remark is '备注';

comment on column tb_report_delay.send_report_date is '预计发布日期';

comment on column tb_report_delay.cancel_user_id is '作废人ID';

comment on column tb_report_delay.cancel_user_name is '作废人姓名';

comment on column tb_report_delay.status is '迟发申请状态(0未打印 1已打印 9已作废)';

comment on column tb_report_delay.group_id is '专业组id';

comment on column tb_report_delay.group_name is '专业组';

comment on column tb_report_delay.org_id is '机构ID';

comment on column tb_report_delay.org_name is '机构名称';

comment on column tb_report_delay.create_date is '创建时间';

comment on column tb_report_delay.update_date is '修改时间';

comment on column tb_report_delay.creator_id is '创建人id';

comment on column tb_report_delay.creator_name is '创建人名称';

comment on column tb_report_delay.updater_id is '修改人id';

comment on column tb_report_delay.updater_name is '修改人名称';

alter table tb_report_delay
  owner to root;

create index report_delay_barcode
  on tb_report_delay (barcode);

--字典明细表新增一条数据迟发原因数据

```

# 1.0.6.5.bc

## 数据库

```sql
-- auto-generated definition
create table tb_bloodculture_sample
(
  bloodculture_sample_id     bigint      not null
    constraint tb_bloodculture_sample_pk
      primary key,
  apply_sample_id            bigint      not null,
  apply_id                   bigint      not null,
  barcode                    varchar(50) not null,
  sample_no                  varchar(50) not null,
  group_id                   bigint,
  group_name                 varchar(50) not null,
  instrument_group_id        bigint      not null,
  instrument_group_name      varchar(50) not null,
  instrument_id              bigint      not null,
  instrument_code            varchar(50) not null,
  instrument_name            varchar(50) not null,
  test_date                  timestamp,
  one_checker_id             bigint      not null,
  one_checker_name           varchar(50) not null,
  one_check_date             timestamp   not null,
  two_checker_id             bigint      not null,
  two_checker_name           varchar(50) not null,
  two_check_date             timestamp   not null,
  one_check_sample_report_id bigint      not null,
  two_check_sample_report_id bigint      not null,
  create_date                timestamp   not null,
  creator_id                 bigint      not null,
  creator_name               varchar(50) not null,
  update_date                timestamp   not null,
  updater_id                 bigint      not null,
  updater_name               varchar(50) not null,
  hsp_org_id                 bigint      not null,
  hsp_org_name               varchar(50) not null,
  org_id                     bigint      not null,
  org_name                   varchar(50) not null,
  is_delete                  integer     not null
);

comment
on column tb_bloodculture_sample.apply_sample_id is '申请单样本id';

comment
on column tb_bloodculture_sample.apply_id is '申请单id';

comment
on column tb_bloodculture_sample.barcode is '条码号';

comment
on column tb_bloodculture_sample.sample_no is '样本号';

comment
on column tb_bloodculture_sample.group_name is '专业组名称';

comment
on column tb_bloodculture_sample.instrument_group_id is '专业小组id';

comment
on column tb_bloodculture_sample.instrument_group_name is '专业小组';

comment
on column tb_bloodculture_sample.instrument_id is '仪器信息';

comment
on column tb_bloodculture_sample.instrument_code is '仪器编码';

comment
on column tb_bloodculture_sample.instrument_name is '仪器名称';

comment
on column tb_bloodculture_sample.test_date is '检验日期';

comment
on column tb_bloodculture_sample.one_checker_id is '一审人id';

comment
on column tb_bloodculture_sample.one_checker_name is '一审人';

comment
on column tb_bloodculture_sample.one_check_date is '一审时间';

comment
on column tb_bloodculture_sample.two_checker_id is '二审人id';

comment
on column tb_bloodculture_sample.two_checker_name is '二审人';

comment
on column tb_bloodculture_sample.two_check_date is '二审时间';

comment
on column tb_bloodculture_sample.one_check_sample_report_id is '初审报告id';

comment
on column tb_bloodculture_sample.two_check_sample_report_id is '终审报告id';

comment
on column tb_bloodculture_sample.create_date is '创建时间';

comment
on column tb_bloodculture_sample.creator_id is '创建人id';

comment
on column tb_bloodculture_sample.creator_name is '创建人名称';

comment
on column tb_bloodculture_sample.update_date is '更新时间';

comment
on column tb_bloodculture_sample.updater_id is '更新人id';

comment
on column tb_bloodculture_sample.updater_name is '更新人名称';

comment
on column tb_bloodculture_sample.hsp_org_id is '送检机构id';

comment
on column tb_bloodculture_sample.hsp_org_name is '送检机构';

comment
on column tb_bloodculture_sample.org_id is '检验机构';

comment
on column tb_bloodculture_sample.org_name is '机构名称';

comment
on column tb_bloodculture_sample.is_delete is '1:删除，0：没有';

alter table tb_bloodculture_sample
  owner to root;


-- auto-generated definition
create table tb_bloodculture_sample_remark
(
  bloodculture_sample_remark_id bigint       not null
    constraint tb_bloodculture_sample_remark_pk
      primary key,
  apply_sample_id               bigint       not null,
  bloodculture_sample_id        bigint       not null,
  apply_id                      bigint       not null,
  remark                        varchar(255) not null,
  position                      varchar(50)  not null,
  create_date                   timestamp    not null,
  creator_id                    bigint       not null,
  creator_name                  varchar(50)  not null,
  update_date                   timestamp    not null,
  updater_id                    bigint       not null,
  updater_name                  varchar(50)  not null,
  org_id                        bigint       not null,
  org_name                      varchar(50)  not null,
  is_delete                     integer      not null
);

comment
on column tb_bloodculture_sample_remark.apply_sample_id is '申请单样本id';

comment
on column tb_bloodculture_sample_remark.apply_id is '申请单id';

comment
on column tb_bloodculture_sample_remark.create_date is '创建时间';

comment
on column tb_bloodculture_sample_remark.creator_id is '创建人id';

comment
on column tb_bloodculture_sample_remark.creator_name is '创建人名称';

comment
on column tb_bloodculture_sample_remark.update_date is '更新时间';

comment
on column tb_bloodculture_sample_remark.updater_id is '更新人id';

comment
on column tb_bloodculture_sample_remark.updater_name is '更新人名称';

comment
on column tb_bloodculture_sample_remark.org_id is '检验机构';

comment
on column tb_bloodculture_sample_remark.org_name is '机构名称';

comment
on column tb_bloodculture_sample_remark.is_delete is '1:删除，0：没有';

alter table tb_bloodculture_sample_remark
  owner to root;

-- auto-generated definition
create table tb_bloodculture_sample_result
(
  bloodculture_sample_result_id bigint       not null
    constraint tb_bloodculture_sample_result_pk
      primary key,
  apply_sample_id               bigint       not null,
  bloodculture_sample_id        bigint       not null,
  apply_id                      bigint       not null,
  result                        varchar(255) not null,
  position                      varchar(50)  not null,
  create_date                   timestamp    not null,
  creator_id                    bigint       not null,
  creator_name                  varchar(50)  not null,
  update_date                   timestamp    not null,
  updater_id                    bigint       not null,
  updater_name                  varchar(50)  not null,
  org_id                        bigint       not null,
  org_name                      varchar(50)  not null,
  is_delete                     integer      not null
);

comment
on column tb_bloodculture_sample_result.apply_sample_id is '申请单样本id';

comment
on column tb_bloodculture_sample_result.apply_id is '申请单id';

comment
on column tb_bloodculture_sample_result.create_date is '创建时间';

comment
on column tb_bloodculture_sample_result.creator_id is '创建人id';

comment
on column tb_bloodculture_sample_result.creator_name is '创建人名称';

comment
on column tb_bloodculture_sample_result.update_date is '更新时间';

comment
on column tb_bloodculture_sample_result.updater_id is '更新人id';

comment
on column tb_bloodculture_sample_result.updater_name is '更新人名称';

comment
on column tb_bloodculture_sample_result.org_id is '检验机构';

comment
on column tb_bloodculture_sample_result.org_name is '机构名称';

comment
on column tb_bloodculture_sample_result.is_delete is '1:删除，0：没有';

alter table tb_bloodculture_sample_result
  owner to root;


```

# 1.0.6.4

## 数据库

```sql

-- 样本结果增加字段
alter table public.tb_sample_result_remark
    add group_id bigint;

comment
on column public.tb_sample_result_remark.group_id is '专业组ID';

alter table public.tb_sample_result_remark
    add group_name varchar(50);

comment
on column public.tb_sample_result_remark.group_name is '专业组名称';


-- 申请单样本增加样本性状
alter table public.tb_apply_sample
    add sample_property varchar(255);

comment
on column public.tb_apply_sample.sample_property is '样本性状';

alter table public.tb_apply_sample
    add sample_property_code varchar(50);

comment
on column public.tb_apply_sample.sample_property_code is '样本性状编码';

-- 刷数据
update tb_apply_sample tas
set sample_property_code =
      (select ta.sample_property_code from tb_apply ta where ta.apply_id = tas.apply_id),
    sample_property =
      (select ta.sample_property from tb_apply ta where ta.apply_id = tas.apply_id)

where tas.sample_property is null;

--新增2张套餐表
CREATE TABLE "public"."tb_package" (
    "package_id" int8 NOT NULL,
    "package_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "package_type_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
    "package_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "hsp_org_id" int8 NOT NULL,
    "hsp_org_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "physical_group_id" int8 NOT NULL,
    "physical_group_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "org_id" int8 NOT NULL,
    "org_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "is_delete" int2 NOT NULL,
    "enable" int2 NOT NULL,
    "create_date" timestamp(6) NOT NULL,
    "update_date" timestamp(6) NOT NULL,
    "creator_id" int8 NOT NULL,
    "creator_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "updater_id" int8 NOT NULL,
    "updater_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    CONSTRAINT "tb_physical_group_package_pkey" PRIMARY KEY ("package_id")
)
;
ALTER TABLE "public"."tb_package" OWNER TO "root";
COMMENT ON COLUMN "public"."tb_package"."package_id" IS '套餐ID';
COMMENT ON COLUMN "public"."tb_package"."package_name" IS '套餐名称';
COMMENT ON COLUMN "public"."tb_package"."package_type_code" IS '套餐类型(0体检,1手动录入)';
COMMENT ON COLUMN "public"."tb_package"."package_type" IS '套餐类型名称';
COMMENT ON COLUMN "public"."tb_package"."hsp_org_id" IS '送检机构ID';
COMMENT ON COLUMN "public"."tb_package"."hsp_org_name" IS '送检机构名称';
COMMENT ON COLUMN "public"."tb_package"."physical_group_id" IS '体检单位ID';
COMMENT ON COLUMN "public"."tb_package"."physical_group_name" IS '体检单位名称';
COMMENT ON COLUMN "public"."tb_package"."org_id" IS '机构ID';
COMMENT ON COLUMN "public"."tb_package"."org_name" IS '机构名称';
COMMENT ON COLUMN "public"."tb_package"."is_delete" IS '1:已经删除 0未删除';
COMMENT ON COLUMN "public"."tb_package"."enable" IS '是否启用(0未启用 1启用)';
COMMENT ON COLUMN "public"."tb_package"."create_date" IS '创建时间';
COMMENT ON COLUMN "public"."tb_package"."update_date" IS '更新时间';
COMMENT ON COLUMN "public"."tb_package"."creator_id" IS '创建人ID';
COMMENT ON COLUMN "public"."tb_package"."creator_name" IS '创建人名称';
COMMENT ON COLUMN "public"."tb_package"."updater_id" IS '更新人ID';
COMMENT ON COLUMN "public"."tb_package"."updater_name" IS '更新人名称';
COMMENT ON TABLE "public"."tb_package" IS '套餐信息';
        
CREATE TABLE "public"."tb_package_item" (
    "package_item_id" int8 NOT NULL,
    "package_id" int8 NOT NULL,
    "test_item_id" int8 NOT NULL,
    "test_item_code" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "test_item_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "is_delete" int2 NOT NULL,
    "create_date" timestamp(6) NOT NULL,
    "update_date" timestamp(6) NOT NULL,
    "creator_id" int8 NOT NULL,
    "creator_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    "updater_id" int8 NOT NULL,
    "updater_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
    CONSTRAINT "tb_physical_group_package_item_pkey" PRIMARY KEY ("package_item_id")
)
;

ALTER TABLE "public"."tb_package_item" OWNER TO "root";
COMMENT ON COLUMN "public"."tb_package_item"."package_item_id" IS '套餐项目ID';
COMMENT ON COLUMN "public"."tb_package_item"."package_id" IS '套餐ID';
COMMENT ON COLUMN "public"."tb_package_item"."test_item_id" IS '检验项目ID';
COMMENT ON COLUMN "public"."tb_package_item"."test_item_code" IS '检验项目编码';
COMMENT ON COLUMN "public"."tb_package_item"."test_item_name" IS '检验项目名称';
COMMENT ON COLUMN "public"."tb_package_item"."is_delete" IS '1:已经删除 0未删除';
COMMENT ON COLUMN "public"."tb_package_item"."create_date" IS '创建时间';
COMMENT ON COLUMN "public"."tb_package_item"."update_date" IS '更新时间';
COMMENT ON COLUMN "public"."tb_package_item"."creator_id" IS '创建人ID';
COMMENT ON COLUMN "public"."tb_package_item"."creator_name" IS '创建人名称';
COMMENT ON COLUMN "public"."tb_package_item"."updater_id" IS '更新人ID';
COMMENT ON COLUMN "public"."tb_package_item"."updater_name" IS '更新人名称';
COMMENT ON TABLE "public"."tb_package_item" IS '套餐项目';

```

# 1.0.6.1

## 数据库

- `tb_sample_image`
  ```sql
  ALTER TABLE "public"."tb_sample_image" 
  ADD COLUMN "image_name" varchar(100) COLLATE "pg_catalog"."default"

# 1.0.6

## 数据库

- `tb_rack`
  ```sql
  ALTER TABLE "public"."tb_rack" 
  ADD COLUMN "group_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying;

  ALTER TABLE "public"."tb_rack" 
  ADD COLUMN "group_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying;

  ALTER TABLE "public"."tb_rack" 
  ADD COLUMN "rack_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying;
    
  -- 刷新数据
    update tb_rack set group_code = '',group_name='' where group_code is null
    update tb_rack set rack_name = rack_code WHERE rack_name=''
  
    WITH ts AS (
      SELECT
      tra.rack_id AS rackId,
      tpg.group_id AS groupId,
      tpg.group_code AS groupCode,
      tpg.group_name AS groupName 
      FROM
      tb_rack_archive tra
      INNER JOIN tb_refrigerator_group trg ON tra.refrigerator_id = trg.refrigerator_id
      INNER JOIN tb_professional_group tpg ON trg.group_id = tpg.group_id 
      WHERE tra.is_delete = 0 
      ) 
    UPDATE tb_rack tr 
    SET group_code = ts.groupCode,
    group_name = ts.groupName 
    FROM ts
    WHERE tr.rack_id = ts.rackId

- `tb_microbiology_sample_result`
  ```sql
  ALTER TABLE "public"."tb_microbiology_sample_result" 
  ADD COLUMN "result_property" varchar(255) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying;

  COMMENT ON COLUMN "public"."tb_microbiology_sample_result"."result_property" IS '结果属性';

- `tb_hsp_org_special_offer`

  ```sql
    -- 增加 折前、折后价格 字段
    alter table tb_hsp_org_special_offer
    add fee_price numeric default 0.00 not null;
    comment on column tb_hsp_org_special_offer.fee_price is '折前价格';
  
    alter table tb_hsp_org_special_offer
    add discount_price numeric default 0.00 not null;
    comment on column tb_hsp_org_special_offer.discount_price is '折后价格';
  
    -- 刷新数据
  
    update tb_hsp_org_special_offer so
    set fee_price = ti.fee_price
    from tb_test_item ti
    where so.test_item_id = ti.test_item_id;
  
    update tb_hsp_org_special_offer so
    set discount_price = round(ti.fee_price * so.discount, 2)
    from tb_test_item ti
    where so.test_item_id = ti.test_item_id;
  
  ```

- `tb_test_item`
  ```sql

  -- 添加是否启用字段
  alter table public.tb_test_item add enable integer default 1;

  comment on column public.tb_test_item.enable is '1:启用，0:未启用';

  -- 检验日期
  alter table public.tb_test_item add test_date integer;

  comment on column public.tb_test_item.test_date is '检验日期，自然日';

  ```
- `tb_instrument_report_item_reference`
  ```sql
  -- 添加逻辑关系符号
  alter table public.tb_instrument_report_item_reference
  add refer_value_max_formula varchar(5) default '<=';

  comment on column public.tb_instrument_report_item_reference.refer_value_max_formula is '参考值上限值单位';

  alter table public.tb_instrument_report_item_reference
  add refer_value_min_formula varchar(5) default '>=';

  comment on column public.tb_instrument_report_item_reference.refer_value_min_formula is '参考值下限值单位';

  alter table public.tb_instrument_report_item_reference
  add excp_warning_max_formula varchar(5) default '<=';

  comment on column public.tb_instrument_report_item_reference.excp_warning_max_formula is '异常提示上限单位';

  alter table public.tb_instrument_report_item_reference
  add excp_warning_min_formula varchar(5) default '>=';

  comment on column public.tb_instrument_report_item_reference.excp_warning_min_formula is '异常提示范围下限单位';

  alter table public.tb_instrument_report_item_reference
  add fatal_max_formula varchar(5) default '>=';

  comment on column public.tb_instrument_report_item_reference.fatal_max_formula is '危急值上限';

  alter table public.tb_instrument_report_item_reference
  add fatal_min_formula varchar(5) default '<=';

  comment on column public.tb_instrument_report_item_reference.fatal_min_formula is '危急值下限';

  -- 刷数据
  update tb_instrument_report_item_reference
  set
  refer_value_max_formula='<=',
  refer_value_min_formula='>=',
  excp_warning_max_formula='>=',
  excp_warning_min_formula='<=',
  fatal_max_formula='>=',
  fatal_min_formula='<='
  where 1=1;

```


# 财务模板 更新， 统计考虑 病理样本 ，客户折扣、客户特价项目、客户阶梯折扣 折扣率 支持四位小数

## 数据库

  ```sql
     alter table tb_hsp_org_discount
     alter column discount type numeric(7, 4) using discount::numeric(7, 4);

     alter table tb_hsp_org_special_offer
     alter column discount type numeric(7, 4) using discount::numeric(7, 4);
  ```

# 1.0.5.17

## 数据库

  ```sql
    alter table tb_qc_batch
    add sample_no varchar(255) default '' not null;

comment
on column tb_qc_batch.sample_no is '样本号';
  ```

# 1.0.6

## 数据库

  ```sql
  alter table tb_microbiology_sample_result
    add result_desc varchar(255) default ''::character varying not null;

comment
on column tb_microbiology_sample_result.result_desc is '结果描述';
  ```

## 微生物报告模板

  ```
  报告增加结果(result)合结果描述(resultDesc)字段
  ```

---


# 1.0.5
=

## 数据库调整

- `tb_microbiology_sample_germ` 修改
  ```sql
  alter table tb_microbiology_sample_germ
    alter column germ_remark_code type varchar(1000) using germ_remark_code::varchar(1000);
  ```

- `tb_medicine_germ_relation` 修改

  ```text
      alter table tb_medicine_germ_relation
      add resistant_warn smallint default 0 not null;
      
      comment on column tb_medicine_germ_relation.resistant_warn is '是否耐药提醒:1是0否';


      alter table tb_medicine_germ_relation
      add report_sort varchar(20) default '' not null;
      
      comment on column tb_medicine_germ_relation.report_sort is '报告顺序';
  ```

- `tb_apply_sample_item` 修改

  ```text

    alter table tb_apply_sample_item
    add item_source smallint default 0 not null;
    
    comment on column tb_apply_sample_item.item_source is '项目来源:0默认，1微生物费用项目';

  ```


- `tb_sample_image` 新增

  ```sql
  
  create table tb_sample_image
  (
      sample_image_id bigint                                      not null
          constraint tb_sample_image_pk
              primary key,
      apply_id             bigint                                      not null,
      sample_id            bigint                                      not null,
      apply_sample_id      bigint                                      not null,
      item_type            varchar(50)                                 not null,
      image_url            varchar(255)                                not null,
      is_delete            smallint                                    not null,
      create_date          timestamp                                   not null,
      update_date          timestamp                                   not null,
      creator_id           bigint                                      not null,
      creator_name         varchar(50)                                 not null,
      updater_id           bigint                                      not null,
      updater_name         varchar(50)                                 not null
  );
  
  comment on table tb_sample_image is '申请单样本图片';
  comment on column tb_sample_image.sample_image_id is '申请单样本图片ID';
  comment on column tb_sample_image.apply_id is '申请单ID';
  comment on column tb_sample_image.sample_id is '样本ID';
  comment on column tb_sample_image.apply_sample_id is '申请单样本ID';
  comment on column tb_sample_image.item_type is '项目类型';
  comment on column tb_sample_image.image_url is '图片地址';
  comment on column tb_sample_image.is_delete is '1:删除，0:不删';
  comment on column tb_sample_image.create_date is '创建时间';
  comment on column tb_sample_image.update_date is '修改时间';
  comment on column tb_sample_image.creator_id is '创建人id';
  comment on column tb_sample_image.creator_name is '创建人名称';
  comment on column tb_sample_image.updater_id is '修改人id';
  comment on column tb_sample_image.updater_name is '修改人名称';
  
  alter table tb_sample_image owner to root;
  create index tb_sample_image_apply_sample_id_index on tb_sample_image (apply_sample_id);
  create index tb_sample_image_apply_id_index on tb_sample_image (apply_id);
  create index tb_sample_image_sample_id_index on tb_sample_image (sample_id);
  
  ```

---

# 1.0.2
=

## 数据库调整

- `tb_hsp_organization` 字段名`enable_generate_barcode`

  ```text
      alter table tb_hsp_organization
      add enable_generate_barcode smallint default 1 not null;
  
      comment on column tb_hsp_organization.enable_generate_barcode is '是否生成新条码';
      
      alter table tb_hsp_organization
      alter column enable_generate_barcode drop default;
      
      alter table tb_instrument_report_item
      add is_bring_out smallint default 0 not null;
      comment on column tb_instrument_report_item.is_bring_out is '是否自定带出 1是 0否';

  ```

- `tb_group_material_apply` 表 新增字段 `checker` `checker_id` `check_date`

  ```text
        alter table tb_group_material_apply
        add checker_id bigint default 0 not null;
  
        comment on column tb_group_material_apply.checker_id is '审核人id';
  
        alter table tb_group_material_apply
        add checker varchar(50) default '' not null;
  
        comment on column tb_group_material_apply.checker is '审核人';
  
        alter table tb_group_material_apply
        add check_date timestamp default '1970-01-01 00:00:00.000000' not null;
  
        comment on column tb_group_material_apply.check_date is '审核时间';
  
        alter table tb_group_material_apply
        alter column checker_id drop default;
  
        alter table tb_group_material_apply
        alter column checker drop default;
  
        alter table tb_group_material_apply
        alter column check_date drop default;

  ```

---

- `tb_microbiology_sample_germ` 表 `germ_remark` 改为 `text` 类型, `germ_remark_code` 字段长度扩增至 `512`

- `tb_instrument_report_item` 表 新增字段 `is_bring_out` `smallint    default 0  not null`

---

## 涉及服务

- [labway-lims-base](labway-lims-base) | [labway-lims-apply](labway-lims-apply)

# 1.0.3
= 

## 数据库调整

- `tb_test_item` 字段名`export_date`

  ```text
    alter table public.tb_test_item
    add export_date numeric default 0 not null;
    comment on column public.tb_test_item.export_date is '外送回报告时间';
  ```
- 新增表 `tb_hsp_org_main`,`tb_hsp_org_doctor`,`tb_hsp_org_dept`
- ```text
    create table tb_hsp_org_main
    (
    hsp_org_main_id bigint not null
    primary key,
    hsp_org_id bigint not null,
    hsp_org_name varchar(50) not null,
    hsp_org_code varchar(50) not null,
    create_date timestamp not null,
    update_date timestamp not null,
    creator_id bigint not null,
    creator_name varchar(50) not null,
    updater_id bigint not null,
    updater_name varchar(50) not null,
    org_id bigint not null,
    org_name varchar(50) not null,
    is_delete smallint not null
    )
    using ???;
    
    comment on table tb_hsp_org_main is '送检机构';
    
    alter table tb_hsp_org_main
    owner to root;
   
    create table tb_hsp_org_doctor
    (
    hsp_org_doctor_id bigint      not null
    primary key,
    hsp_org_dept_id   bigint      not null,
    hsp_org_main_id   bigint      not null,
    hsp_org_id        bigint      not null,
    hsp_org_name      varchar(50) not null,
    hsp_org_code      varchar(50) not null,
    dept_code         varchar(50) not null,
    dept              varchar(50) not null,
    doctor_code       varchar(50) not null,
    doctor_name       varchar(50) not null,
    create_date       timestamp   not null,
    update_date       timestamp   not null,
    creator_id        bigint      not null,
    creator_name      varchar(50) not null,
    updater_id        bigint      not null,
    updater_name      varchar(50) not null,
    org_id            bigint      not null,
    org_name          varchar(50) not null,
    is_delete         smallint    not null
    )
    using ???;
    
    comment on table tb_hsp_org_doctor is '送检机构医生';
    
    alter table tb_hsp_org_doctor
    owner to root;

    create table tb_hsp_org_dept
    (
    hsp_org_dept_id bigint      not null
    primary key,
    hsp_org_main_id bigint      not null,
    hsp_org_id      bigint      not null,
    hsp_org_name    varchar(50) not null,
    hsp_org_code    varchar(50) not null,
    dept_code       varchar(50) not null,
    dept            varchar(50) not null,
    create_date     timestamp   not null,
    update_date     timestamp   not null,
    creator_id      bigint      not null,
    creator_name    varchar(50) not null,
    updater_id      bigint      not null,
    updater_name    varchar(50) not null,
    org_id          bigint      not null,
    org_name        varchar(50) not null,
    is_delete       smallint    not null
    )
    using ???;
    
    comment on table tb_hsp_org_dept is '送检机构科室';
    
    alter table tb_hsp_org_dept
    owner to root;

  ```

- `tb_physical_register`  变动
  ```text
     alter table tb_physical_register
     alter column patient_bed type varchar(50) using patient_bed::varchar(50);
     
     alter table tb_physical_register
     alter column dept type varchar(50) using dept::varchar(50);
     
     alter table tb_physical_register
     alter column patient_mobile type varchar(50) using patient_mobile::varchar(50);
     
     alter table tb_physical_register
     alter column patient_card type varchar(50) using patient_card::varchar(50);
     
     alter table tb_physical_register
     alter column patient_card_type type varchar(50) using patient_card_type::varchar(50);
     
     alter table tb_physical_register
     alter column applicant type varchar(50) using applicant::varchar(50);
     
     alter table tb_physical_register
     alter column org_name type varchar(50) using org_name::varchar(50);
     
     alter table tb_physical_register
     add apply_type_code varchar(50) default '' not null;
     comment on column tb_physical_register.apply_type_code is '就诊类型编码';
     
     alter table tb_physical_register
     add apply_type_name varchar(50) default '' not null;
     comment on column tb_physical_register.apply_type_name is '就诊类型名称';
     
     alter table tb_physical_register
     add patient_visit_card varchar(50) default '' not null;
     comment on column tb_physical_register.patient_visit_card is '门诊/住院号';
     
     alter table tb_physical_register
     add test_package_desc varchar(255) default '' not null;
     comment on column tb_physical_register.test_package_desc is '套餐说明';
     
     alter table tb_physical_register
     add diagnosis varchar(255) default '' not null;
     comment on column tb_physical_register.diagnosis is '临床诊断';
  ```

- `tb_physical_sample`  变动
  ```text
    alter table tb_physical_sample
    add sampling_date timestamp default CURRENT_TIMESTAMP not null;
    comment on column tb_physical_sample.sampling_date is '采样时间';
  ```

- `tb_item_price_base_package_detail`  变动
  ```text
    alter table tb_item_price_base_package_detail
    add fee_price numeric default 0.00 not null;
    comment on column tb_item_price_base_package_detail.fee_price is '收费价格';
  ```

- `tb_apply_sample_item`  变动
  ```text
    alter table tb_apply_sample_item
    add actual_fee_price numeric default 0.00 not null;
    comment on column tb_apply_sample_item.actual_fee_price is '实际收费价格';
  ```

- `tb_sample_result_`  变动（涉及分表也需要改动）
  ```text
  alter table public.tb_sample_result_2023_07
  add instrument_report_item_reference_id bigint default 0 not null;

  comment on column public.tb_sample_result_2023_06.instrument_report_item_reference_id is '参考值主键ID';
  ```

# 1.0.4
=

## es调整

- lims-sample 索引对应字段映射调整
  ```text
    results 、 germs 、 germs.medicines 、reportItems 类型改为 "type": "nested",

  ```

## 数据库调整

- `tb_apply` 变动

  ```text
  alter table tb_apply
    add original_org_code varchar(50) default '' not null;
  comment on column tb_apply.original_org_code is '原始机构编码';
    alter table tb_apply
    add original_org_name varchar(255) default '' not null;
  comment on column tb_apply.original_org_name is '原始机构名称';
  ```
- `tb_microbiology_germ_medicine` 变动

  ```text
  alter table tb_microbiology_germ_medicine
  add "group" varchar(50) default '' not null;
  
  comment on column tb_microbiology_germ_medicine."group" is '分组';
  ```
- `tb_apply_sample_item` 变动 原因

  ```text
  alter table tb_apply_sample_item
  add stop_status smallint default 0 not null;

  comment on column tb_apply_sample_item.stop_status is '终止检验状态：0正常，1终止收费，2终止不收费';

  alter table tb_apply_sample_item
  add stop_reason_code varchar(512) default '' not null;

  comment on column tb_apply_sample_item.stop_reason_code is '终止检验原因code';

  alter table tb_apply_sample_item
  add stop_reason_name varchar(1024) default '' not null;

  comment on column tb_apply_sample_item.stop_reason_name is '终止检验原因';

  ```

# 酶标仪
- 计算公式
  ```sql
  create table public.tb_enzyme_label_calc_formula (
    formula_id bigint primary key not null, -- 计算公式ID
    calc_formula character varying(200) not null, -- 计算公式
    formula_short character varying(50) not null, -- 仪器名称
    cutoff_calc_formula character varying(200) not null, -- Cutoff计算公式
    sort integer not null, -- 显示顺序
    formula_desc character varying(255) not null, -- 公式描述
    group_id bigint not null, -- 专业组ID
    group_name character varying(50) not null, -- 专业组名称
    is_delete smallint not null, -- 是否删除
    org_id bigint not null, -- 机构ID
    org_name character varying(50) not null, -- 机构名称
    create_date timestamp(6) without time zone not null, -- 创建时间
    creator_id bigint not null, -- 创建人id
    creator_name character varying(50) not null, -- 创建人名
    updater_id bigint not null, -- 更新id
    updater_name character varying(50) not null, -- 更新人名
    update_date timestamp(6) without time zone not null, -- 更新时间
    formula_code character varying(200) not null -- 计算公式code
  );
  create unique index key3 on tb_enzyme_label_calc_formula using btree (formula_id);
  comment on table public.tb_enzyme_label_calc_formula is '酶标计算公式';
  comment on column public.tb_enzyme_label_calc_formula.formula_id is '计算公式ID';
  comment on column public.tb_enzyme_label_calc_formula.calc_formula is '计算公式';
  comment on column public.tb_enzyme_label_calc_formula.formula_short is '仪器名称';
  comment on column public.tb_enzyme_label_calc_formula.cutoff_calc_formula is 'Cutoff计算公式';
  comment on column public.tb_enzyme_label_calc_formula.sort is '显示顺序';
  comment on column public.tb_enzyme_label_calc_formula.formula_desc is '公式描述';
  comment on column public.tb_enzyme_label_calc_formula.group_id is '专业组ID';
  comment on column public.tb_enzyme_label_calc_formula.group_name is '专业组名称';
  comment on column public.tb_enzyme_label_calc_formula.is_delete is '是否删除';
  comment on column public.tb_enzyme_label_calc_formula.org_id is '机构ID';
  comment on column public.tb_enzyme_label_calc_formula.org_name is '机构名称';
  comment on column public.tb_enzyme_label_calc_formula.create_date is '创建时间';
  comment on column public.tb_enzyme_label_calc_formula.creator_id is '创建人id';
  comment on column public.tb_enzyme_label_calc_formula.creator_name is '创建人名';
  comment on column public.tb_enzyme_label_calc_formula.updater_id is '更新id';
  comment on column public.tb_enzyme_label_calc_formula.updater_name is '更新人名';
  comment on column public.tb_enzyme_label_calc_formula.update_date is '更新时间';
  comment on column public.tb_enzyme_label_calc_formula.formula_code is '计算公式code';
  ```
- 计算公式定性信息
  ```sql
  create table public.tb_calc_formula_determination
  (
    determination_id bigint       not null
      primary key,
    formula_id       bigint       not null,
    colors           varchar(20)  not null,
    compare_symbol   varchar(10)  not null,
    compare_value    numeric      not null,
    sort             integer      not null,
    detail_desc      varchar(255) not null,
    brief            varchar(50)  not null,
    is_delete        smallint     not null,
    create_date      timestamp(6) not null,
    creator_id       bigint       not null,
    creator_name     varchar(50)  not null,
    updater_id       bigint       not null,
    updater_name     varchar(50)  not null,
    update_date      timestamp(6) not null
  );
  
  comment on table public.tb_calc_formula_determination is '计算公式定性信息';
  
  comment on column public.tb_calc_formula_determination.determination_id is '定性描述ID';
  
  comment on column public.tb_calc_formula_determination.formula_id is '计算公式ID';
  
  comment on column public.tb_calc_formula_determination.colors is '显示颜色';
  
  comment on column public.tb_calc_formula_determination.compare_symbol is '比较符号';
  
  comment on column public.tb_calc_formula_determination.compare_value is '比较数值';
  
  comment on column public.tb_calc_formula_determination.sort is '顺序';
  
  comment on column public.tb_calc_formula_determination.detail_desc is '详细描述';
  
  comment on column public.tb_calc_formula_determination.brief is '简述';
  
  comment on column public.tb_calc_formula_determination.is_delete is '是否删除';
  
  comment on column public.tb_calc_formula_determination.create_date is '创建时间';
  
  comment on column public.tb_calc_formula_determination.creator_id is '创建人id';
  
  comment on column public.tb_calc_formula_determination.creator_name is '创建人名';
  
  comment on column public.tb_calc_formula_determination.updater_id is '更新id';
  
  comment on column public.tb_calc_formula_determination.updater_name is '更新人名';
  
  comment on column public.tb_calc_formula_determination.update_date is '更新时间';
  
  alter table public.tb_calc_formula_determination
    owner to root;
  
  create unique index key1
    on public.tb_calc_formula_determination (determination_id);
  ```

- 计算公式变量替换
  ```sql
  create table public.tb_calc_formula_variable_replace
  (
      replace_id     bigint       not null
          primary key,
      formula_id     bigint       not null,
      variable_code  varchar(20)  not null,
      variable       varchar(20)  not null,
      compare_symbol varchar(10)  not null,
      compare_value  numeric      not null,
      replace_value  numeric      not null,
      sort           integer      not null,
      is_delete      smallint     not null,
      create_date    timestamp(6) not null,
      creator_id     bigint       not null,
      creator_name   varchar(50)  not null,
      updater_id     bigint       not null,
      updater_name   varchar(50)  not null,
      update_date    timestamp(6) not null
  );
  
  comment on table public.tb_calc_formula_variable_replace is '计算公式变量替换';
  
  comment on column public.tb_calc_formula_variable_replace.replace_id is '变量替换ID';
  
  comment on column public.tb_calc_formula_variable_replace.formula_id is '计算公式ID';
  
  comment on column public.tb_calc_formula_variable_replace.variable_code is '变量名称编码';
  
  comment on column public.tb_calc_formula_variable_replace.variable is '变量名称';
  
  comment on column public.tb_calc_formula_variable_replace.compare_symbol is '比较符号';
  
  comment on column public.tb_calc_formula_variable_replace.compare_value is '比较数值';
  
  comment on column public.tb_calc_formula_variable_replace.replace_value is '替换数值';
  
  comment on column public.tb_calc_formula_variable_replace.sort is '顺序';
  
  comment on column public.tb_calc_formula_variable_replace.is_delete is '是否删除';
  
  comment on column public.tb_calc_formula_variable_replace.create_date is '创建时间';
  
  comment on column public.tb_calc_formula_variable_replace.creator_id is '创建人id';
  
  comment on column public.tb_calc_formula_variable_replace.creator_name is '创建人名';
  
  comment on column public.tb_calc_formula_variable_replace.updater_id is '更新id';
  
  comment on column public.tb_calc_formula_variable_replace.updater_name is '更新人名';
  
  comment on column public.tb_calc_formula_variable_replace.update_date is '更新时间';
  
  alter table public.tb_calc_formula_variable_replace
      owner to root;
  
  create unique index key2
      on public.tb_calc_formula_variable_replace (replace_id);
  ```
- 酶标报告项目
  ```sql
  create table public.tb_enzyme_label_report_item
  (
      label_report_id       bigint               not null
          primary key,
      instrument_id         bigint               not null,
      report_item_id        bigint               not null,
      formula_id            bigint               not null,
      project_color         varchar(20)          not null,
      test_wave_length      varchar(50)          not null,
      reference_wave_length varchar(50)          not null,
      test_method           varchar(50)          not null,
      reagent_batch         varchar(50)          not null,
      reagent_valid_date    date                 not null,
      reagent_manufacturer  varchar(100)         not null,
      gray_zone_min_formula varchar(255)         not null,
      gray_zone_max_formula varchar(255)         not null,
      is_delete             smallint             not null,
      group_id              bigint               not null,
      group_name            varchar(50)          not null,
      org_id                bigint               not null,
      org_name              varchar(50)          not null,
      create_date           timestamp(6)         not null,
      creator_id            bigint               not null,
      creator_name          varchar(50)          not null,
      updater_id            bigint               not null,
      updater_name          varchar(50)          not null,
      update_date           timestamp(6)         not null,
      is_generate_qc        boolean default true not null,
      is_negative_contrast  boolean default true not null,
      negative_contrast_num integer              not null,
      is_positive_contrast  boolean default true not null,
      positive_contrast_num integer              not null,
      is_reserved_sample    boolean default true not null,
      item_type_code        varchar(50)          not null,
      ration_setting        varchar(2000)        not null,
      ration_result         varchar(255)         not null,
      item_type_name        varchar(50)          not null,
      is_generate_blank     boolean default true not null,
      is_generate_standard  boolean default true not null,
      generate_blank_num    integer              not null,
      generate_standard_num integer              not null
  );
  
  comment on table public.tb_enzyme_label_report_item is '酶标报告项目';
  
  comment on column public.tb_enzyme_label_report_item.label_report_id is '酶标报告项目ID';
  
  comment on column public.tb_enzyme_label_report_item.instrument_id is '仪器ID';
  
  comment on column public.tb_enzyme_label_report_item.report_item_id is '报告项目ID';
  
  comment on column public.tb_enzyme_label_report_item.formula_id is '计算公式ID   -1没有计算公式';
  
  comment on column public.tb_enzyme_label_report_item.project_color is '项目颜色';
  
  comment on column public.tb_enzyme_label_report_item.test_wave_length is '检测波长';
  
  comment on column public.tb_enzyme_label_report_item.reference_wave_length is '参考波长';
  
  comment on column public.tb_enzyme_label_report_item.test_method is '测试方法';
  
  comment on column public.tb_enzyme_label_report_item.reagent_batch is '试剂批号';
  
  comment on column public.tb_enzyme_label_report_item.reagent_valid_date is '试剂有效期';
  
  comment on column public.tb_enzyme_label_report_item.reagent_manufacturer is '试剂厂商';
  
  comment on column public.tb_enzyme_label_report_item.gray_zone_min_formula is '灰区范围(小)计算公式';
  
  comment on column public.tb_enzyme_label_report_item.gray_zone_max_formula is '灰区范围(大)计算公式';
  
  comment on column public.tb_enzyme_label_report_item.is_delete is '是否删除';
  
  comment on column public.tb_enzyme_label_report_item.group_id is '专业组ID';
  
  comment on column public.tb_enzyme_label_report_item.group_name is '专业组名称';
  
  comment on column public.tb_enzyme_label_report_item.org_id is '机构ID';
  
  comment on column public.tb_enzyme_label_report_item.org_name is '机构名称';
  
  comment on column public.tb_enzyme_label_report_item.create_date is '创建时间';
  
  comment on column public.tb_enzyme_label_report_item.creator_id is '创建人id';
  
  comment on column public.tb_enzyme_label_report_item.creator_name is '创建人名';
  
  comment on column public.tb_enzyme_label_report_item.updater_id is '更新id';
  
  comment on column public.tb_enzyme_label_report_item.updater_name is '更新人名';
  
  comment on column public.tb_enzyme_label_report_item.update_date is '更新时间';
  
  comment on column public.tb_enzyme_label_report_item.is_generate_qc is '生成质控 0不生成，1生成';
  
  comment on column public.tb_enzyme_label_report_item.is_negative_contrast is '是否生成阴性对照 ';
  
  comment on column public.tb_enzyme_label_report_item.negative_contrast_num is '阴性对照参考值';
  
  comment on column public.tb_enzyme_label_report_item.is_positive_contrast is '是否生成阳性对照 0不生成，1生成';
  
  comment on column public.tb_enzyme_label_report_item.positive_contrast_num is '生成阳性对照参考结果';
  
  comment on column public.tb_enzyme_label_report_item.is_reserved_sample is '是否留样复测';
  
  comment on column public.tb_enzyme_label_report_item.item_type_code is '0001定性 00002定量  00004其他';
  
  comment on column public.tb_enzyme_label_report_item.ration_setting is '定量设置 json';
  
  comment on column public.tb_enzyme_label_report_item.ration_result is '公式结果 json';
  
  comment on column public.tb_enzyme_label_report_item.item_type_name is '项目类型名称';
  
  comment on column public.tb_enzyme_label_report_item.is_generate_blank is '是否空白';
  
  comment on column public.tb_enzyme_label_report_item.is_generate_standard is '是否标准';
  
  comment on column public.tb_enzyme_label_report_item.generate_blank_num is '空白数量';
  
  comment on column public.tb_enzyme_label_report_item.generate_standard_num is '标准品数量';
  
  alter table public.tb_enzyme_label_report_item
      owner to root;
  
  create unique index key4
      on public.tb_enzyme_label_report_item (label_report_id);
  ```

- 酶标板
  ```sql
  create table public.tb_enzyme_label_plate
  (
      plate_id     bigint       not null
          primary key,
      plate_code   varchar(50)  not null,
      remark       varchar(255) not null,
      is_delete    smallint     not null,
      org_id       bigint       not null,
      org_name     varchar(50)  not null,
      create_date  timestamp(6) not null,
      creator_id   bigint       not null,
      creator_name varchar(50)  not null,
      updater_id   bigint       not null,
      updater_name varchar(50)  not null,
      update_date  timestamp(6) not null,
      plate_date   date         not null
  );
  
  comment on table public.tb_enzyme_label_plate is '酶标板信息';
  
  comment on column public.tb_enzyme_label_plate.plate_id is '酶标板ID';
  
  comment on column public.tb_enzyme_label_plate.plate_code is '酶标板号';
  
  comment on column public.tb_enzyme_label_plate.remark is '备注';
  
  comment on column public.tb_enzyme_label_plate.is_delete is '是否删除';
  
  comment on column public.tb_enzyme_label_plate.org_id is '机构ID';
  
  comment on column public.tb_enzyme_label_plate.org_name is '机构名称';
  
  comment on column public.tb_enzyme_label_plate.create_date is '创建时间';
  
  comment on column public.tb_enzyme_label_plate.creator_id is '创建人id';
  
  comment on column public.tb_enzyme_label_plate.creator_name is '创建人名';
  
  comment on column public.tb_enzyme_label_plate.updater_id is '更新id';
  
  comment on column public.tb_enzyme_label_plate.updater_name is '更新人名';
  
  comment on column public.tb_enzyme_label_plate.update_date is '更新时间';
  
  comment on column public.tb_enzyme_label_plate.plate_date is '版面日期';
  
  alter table public.tb_enzyme_label_plate
      owner to root;
  ```

- 酶标板布局
  ```sql
  create table public.tb_enzyme_label_layout
  (
      layout_id         bigint       not null
          primary key,
      plate_id          bigint       not null,
      plate_code        varchar(50)  not null,
      ref_template_id   bigint       not null,
      ref_template_name varchar(50)  not null,
      is_template       smallint     not null,
      template_name     varchar(50)  not null,
      sample_no_date    date         not null,
      sample_start_no   integer      not null,
      remark            varchar(255) not null,
      place_rule_code   varchar(20)  not null,
      place_rule        varchar(20)  not null,
      is_delete         smallint     not null,
      org_id            bigint       not null,
      org_name          varchar(50)  not null,
      create_date       timestamp(6) not null,
      creator_id        bigint       not null,
      creator_name      varchar(50)  not null,
      updater_id        bigint       not null,
      updater_name      varchar(50)  not null,
      update_date       timestamp(6) not null
  );
  
  comment on table public.tb_enzyme_label_layout is '酶标板布局';
  
  comment on column public.tb_enzyme_label_layout.layout_id is '布局ID';
  
  comment on column public.tb_enzyme_label_layout.plate_id is '酶标板ID';
  
  comment on column public.tb_enzyme_label_layout.plate_code is '酶标板号';
  
  comment on column public.tb_enzyme_label_layout.ref_template_id is '引用模板ID';
  
  comment on column public.tb_enzyme_label_layout.ref_template_name is '引用模板名称';
  
  comment on column public.tb_enzyme_label_layout.is_template is '是否为模板';
  
  comment on column public.tb_enzyme_label_layout.template_name is '模板名称';
  
  comment on column public.tb_enzyme_label_layout.sample_no_date is '样本号日期';
  
  comment on column public.tb_enzyme_label_layout.sample_start_no is '样本开始号';
  
  comment on column public.tb_enzyme_label_layout.remark is '备注';
  
  comment on column public.tb_enzyme_label_layout.place_rule_code is '放置规则编码(0横向 1竖向)';
  
  comment on column public.tb_enzyme_label_layout.place_rule is '放置规则';
  
  comment on column public.tb_enzyme_label_layout.is_delete is '是否删除';
  
  comment on column public.tb_enzyme_label_layout.org_id is '机构ID';
  
  comment on column public.tb_enzyme_label_layout.org_name is '机构名称';
  
  comment on column public.tb_enzyme_label_layout.create_date is '创建时间';
  
  comment on column public.tb_enzyme_label_layout.creator_id is '创建人id';
  
  comment on column public.tb_enzyme_label_layout.creator_name is '创建人名';
  
  comment on column public.tb_enzyme_label_layout.updater_id is '更新id';
  
  comment on column public.tb_enzyme_label_layout.updater_name is '更新人名';
  
  comment on column public.tb_enzyme_label_layout.update_date is '更新时间';
  
  alter table public.tb_enzyme_label_layout
      owner to root;
  ```

- 酶标板样本
  ```sql
  create table public.tb_enzyme_label_sample
  (
      plate_sample_id      bigint       not null
          primary key,
      plate_id             bigint       not null,
      plate_code           varchar(50)  not null,
      col                  integer      not null,
      row                  integer      not null,
      sample_id            bigint       not null,
      sample_no            integer      not null,
      label_report_id      bigint       not null,
      sample_type_code     varchar(20)  not null,
      sample_type          varchar(20)  not null,
      org_id               bigint       not null,
      org_name             varchar(50)  not null,
      create_date          timestamp(6) not null,
      creator_id           bigint       not null,
      creator_name         varchar(50)  not null,
      updater_id           bigint       not null,
      updater_name         varchar(50)  not null,
      update_date          timestamp(6) not null,
      first_sample_result  varchar(10)  not null,
      second_sample_result varchar(10)  not null
  );
  
  comment on table public.tb_enzyme_label_sample is '酶标板样本';
  
  comment on column public.tb_enzyme_label_sample.plate_sample_id is '酶标板样本ID';
  
  comment on column public.tb_enzyme_label_sample.plate_id is '酶标板ID';
  
  comment on column public.tb_enzyme_label_sample.plate_code is '酶标板号';
  
  comment on column public.tb_enzyme_label_sample.col is '列';
  
  comment on column public.tb_enzyme_label_sample.row is '行';
  
  comment on column public.tb_enzyme_label_sample.sample_id is '样本ID';
  
  comment on column public.tb_enzyme_label_sample.sample_no is '样本号';
  
  comment on column public.tb_enzyme_label_sample.label_report_id is '酶标报告项目ID';
  
  comment on column public.tb_enzyme_label_sample.sample_type_code is '样本类型编码(0普通样本 1阴性对照 2阳性对照 3质控品 4空白 5标准品)';
  
  comment on column public.tb_enzyme_label_sample.sample_type is '样本类型名称';
  
  comment on column public.tb_enzyme_label_sample.org_id is '机构ID';
  
  comment on column public.tb_enzyme_label_sample.org_name is '机构名称';
  
  comment on column public.tb_enzyme_label_sample.create_date is '创建时间';
  
  comment on column public.tb_enzyme_label_sample.creator_id is '创建人id';
  
  comment on column public.tb_enzyme_label_sample.creator_name is '创建人名';
  
  comment on column public.tb_enzyme_label_sample.updater_id is '更新id';
  
  comment on column public.tb_enzyme_label_sample.updater_name is '更新人名';
  
  comment on column public.tb_enzyme_label_sample.update_date is '更新时间';
  
  comment on column public.tb_enzyme_label_sample.first_sample_result is '仪器第一次传输结果';
  
  comment on column public.tb_enzyme_label_sample.second_sample_result is '仪器第二次传输结果';
  
  alter table public.tb_enzyme_label_sample
      owner to root;
  ```

- 酶标板样本模板
  ```sql
  create table public.tb_enzyme_label_template_sample
  (
      plate_sample_id  bigint       not null
          primary key,
      layout_id        bigint       not null,
      col              integer      not null,
      row              integer      not null,
      label_report_id  bigint       not null,
      sample_type_code varchar(20)  not null,
      sample_type      varchar(20)  not null,
      org_id           bigint       not null,
      org_name         varchar(50)  not null,
      create_date      timestamp(6) not null,
      creator_id       bigint       not null,
      creator_name     varchar(50)  not null,
      updater_id       bigint       not null,
      updater_name     varchar(50)  not null,
      update_date      timestamp(6) not null
  );
  
  comment on table public.tb_enzyme_label_template_sample is '酶标板模板样本';
  
  comment on column public.tb_enzyme_label_template_sample.plate_sample_id is '酶标板样本ID';
  
  comment on column public.tb_enzyme_label_template_sample.layout_id is '布局ID';
  
  comment on column public.tb_enzyme_label_template_sample.col is '列';
  
  comment on column public.tb_enzyme_label_template_sample.row is '行';
  
  comment on column public.tb_enzyme_label_template_sample.label_report_id is '酶标报告项目ID';
  
  comment on column public.tb_enzyme_label_template_sample.sample_type_code is '样本类型编码(0普通样本 1阴性对照 2阳性对照 3质控品 4空白 5标准品)';
  
  comment on column public.tb_enzyme_label_template_sample.sample_type is '样本类型名称';
  
  comment on column public.tb_enzyme_label_template_sample.org_id is '机构ID';
  
  comment on column public.tb_enzyme_label_template_sample.org_name is '机构名称';
  
  comment on column public.tb_enzyme_label_template_sample.create_date is '创建时间';
  
  comment on column public.tb_enzyme_label_template_sample.creator_id is '创建人id';
  
  comment on column public.tb_enzyme_label_template_sample.creator_name is '创建人名';
  
  comment on column public.tb_enzyme_label_template_sample.updater_id is '更新id';
  
  comment on column public.tb_enzyme_label_template_sample.updater_name is '更新人名';
  
  comment on column public.tb_enzyme_label_template_sample.update_date is '更新时间';
  
  alter table public.tb_enzyme_label_template_sample
      owner to root;
  ```

- 计算公式增加编码字段
  ```sql
  alter table tb_enzyme_label_calc_formula 
        add formula_code varchar(200) default '' not null;
  
  comment on column public.tb_enzyme_label_calc_formula.formula_code is '计算公式code';
  ```

- 常规检验结果表
  ```sql
  alter table tb_sample_result 
        add extra_info text;
  
  comment on column public.tb_sample_result.extra_info is '额外信息 （json）';
  
  alter table tb_sample_result_2023_01
        add extra_info text;
  comment on column public.tb_sample_result_2023_01.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2023_02
        add extra_info text;
  comment on column public.tb_sample_result_2023_02.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2023_03
        add extra_info text;
  comment on column public.tb_sample_result_2023_03.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2023_04
        add extra_info text;
  comment on column public.tb_sample_result_2023_04.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2023_05
        add extra_info text;
  comment on column public.tb_sample_result_2023_05.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2023_06
        add extra_info text;
  comment on column public.tb_sample_result_2023_06.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2023_07
        add extra_info text;
  comment on column public.tb_sample_result_2023_07.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2023_08
        add extra_info text;
  comment on column public.tb_sample_result_2023_08.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2023_09
        add extra_info text;
  comment on column public.tb_sample_result_2023_09.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2023_10
        add extra_info text;
  comment on column public.tb_sample_result_2023_10.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2023_11
        add extra_info text;
  comment on column public.tb_sample_result_2023_11.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2023_12
        add extra_info text;
  comment on column public.tb_sample_result_2023_12.extra_info is '额外信息 （json）';



  alter table tb_sample_result_2024_01
        add extra_info text;
  comment on column public.tb_sample_result_2024_01.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2024_02
        add extra_info text;
  comment on column public.tb_sample_result_2024_02.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2024_03
        add extra_info text;
  comment on column public.tb_sample_result_2024_03.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2024_04
        add extra_info text;
  comment on column public.tb_sample_result_2024_04.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2024_05
        add extra_info text;
  comment on column public.tb_sample_result_2024_05.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2024_06
        add extra_info text;
  comment on column public.tb_sample_result_2024_06.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2024_07
        add extra_info text;
  comment on column public.tb_sample_result_2024_07.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2024_08
        add extra_info text;
  comment on column public.tb_sample_result_2024_08.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2024_09
        add extra_info text;
  comment on column public.tb_sample_result_2024_09.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2024_10
        add extra_info text;
  comment on column public.tb_sample_result_2024_10.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2024_11
        add extra_info text;
  comment on column public.tb_sample_result_2024_11.extra_info is '额外信息 （json）';

  alter table tb_sample_result_2024_12
        add extra_info text;
  comment on column public.tb_sample_result_2024_12.extra_info is '额外信息 （json）';
  ```

- 酶标结果接受两次结果
```sql
  
  alter table tb_enzyme_label_sample
    drop column sample_result;

  alter table public.tb_enzyme_label_sample
  add first_sample_result varchar(10) default '' not null;

  alter table public.tb_enzyme_label_sample
    add second_sample_result varchar(10) default '' not null;
  
  comment on column public.tb_enzyme_label_sample.first_sample_result is '仪器第一次传输结果';
  
  comment on column public.tb_enzyme_label_sample.second_sample_result is '仪器第二次传输结果';

  alter table public.tb_enzyme_label_sample
    add original_od varchar(10) default '' not null;
  
  comment on column public.tb_enzyme_label_sample.original_od is '原始od， 默认第一次结果减第二次结果， 修改原始od的话修改这个值， 替换差';

```

- 样本号修改类型
```sql
alter table tb_enzyme_label_sample
  alter column sample_no type varchar(50) using sample_no::varchar(50);
```