package com.labway.lims.statistics.api.client;

import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsResponseDto;
import com.labway.lims.statistics.dto.PushSampleStatisticsInfoDto;
import com.labway.lims.statistics.vo.PushSampleStatisticsInfoVo;
import com.labway.lims.statistics.vo.SampleStatisticsQueryVo;
import com.labway.lims.statistics.vo.SampleStatisticsVo;
import com.labway.lims.statistics.vo.SampleTestItemStatisticsVo;

import java.util.List;

/**
 * <p>
 * SampleStatisticsService
 * 条码综合信息查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 14:44
 */
public interface SampleStatisticsService {
    /**
     * 查询列表信息
     */
    SampleStatisticsVo selectSampleStatistics(SampleStatisticsQueryVo vo);

    /**
     * 查询检验项目
     */
    List<SampleTestItemStatisticsVo> selectSampleTestItemStatistics(Long applySampleId);

    /**
     * 推送样本统计信息至业务中台
     */
    PushSampleStatisticsInfoVo pushSampleStatisticsInfo(PushSampleStatisticsInfoDto dto);

    /**
     * 推送样本统计信息至业务中台2
     */
    PushSampleStatisticsInfoVo pushSampleStatisticsInfoNew(PushSampleStatisticsInfoDto dto);
}
