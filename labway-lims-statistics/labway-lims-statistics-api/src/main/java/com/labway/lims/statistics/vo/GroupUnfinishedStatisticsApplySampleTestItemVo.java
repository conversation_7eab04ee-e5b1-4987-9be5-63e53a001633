package com.labway.lims.statistics.vo;

import com.labway.lims.api.field.Compare;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <pre>
 * GroupUnfinishedStatisticsApplySampleTestItemVo
 * 未完成工作量统计 - 检验项目
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/7/8 13:14
 */
@Getter
@Setter
public class GroupUnfinishedStatisticsApplySampleTestItemVo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

}
