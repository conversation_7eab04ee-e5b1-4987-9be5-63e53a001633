package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
public class QueryContagionSampleInfoVo implements Serializable {

    // 接收日期开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date receiveDateStart;
    // 接收日期结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date receiveDateEnd;

    // 审核日期开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date checkDateStart;
    // 审核日期结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date checkDateEnd;

    /**
     * 专业组id
     */
    private Set<Long> groupIds;

    // 送检机构id
    private List<String> sendOrgCodes;

    // 报告项目编码
    private List<String> reportItemCodes;

    // 病人姓名
    private String patientName;

    // 条码号/外部条码号
    private String barcode;

}
