package com.labway.lims.statistics.dto;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * SampleArchiveQueryDto
 * 样本归档信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/31 13:23
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SampleArchiveQueryDto implements Serializable {
    private Long searchAfter;
    /**
     * 归档时间范围
     */
    private Date startCreateDate;
    private Date endCreateDate;

    /**
     * 归档人
     */
    private Long creatorId;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 检验项目
     */
    private String testItemCode;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页数量
     */
    private Integer pageSize;
}
