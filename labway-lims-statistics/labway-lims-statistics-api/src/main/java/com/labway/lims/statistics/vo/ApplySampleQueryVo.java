package com.labway.lims.statistics.vo;

import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.apply.api.dto.SampleEsQueryAdvanced;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * ApplySampleQueryVo
 * 样本信息查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/7 19:37
 */
@Getter
@Setter
public class ApplySampleQueryVo extends BaseDataStatisticsQueryVo {

    /**
     * 签收开始时间
     */
    private Date startSignDate;

    /**
     * 签收结束时间
     */
    private Date endSignDate;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 检验状态：未复核、已签收、正在检验、完成检验
     * <pre>
     * 1未复核：未复核和未双输复核的数据
     * 2已签收：完成双输复核、复核和样本签收的数据
     * 3正在检验：二次分拣完成且未审核的数据
     * 4完成检验：已审核的数据
     * </pre>
     */
    private Integer status;

    /**
     * 0:正常，1:已终止（收费），2:已终止（未收费）,3:禁止
     * @see StopTestStatus
     */
    private Integer itemStatus;

    /**
     * 高级查询
     */
    private SampleEsQueryAdvanced advanceQuery;

    /**
     * 滚动分页查询
     */
    private Object searchAfter;

    /**
     * 每页显示数据大小
     */
    private Integer pageSize;

    /**
     * 外部条码号
     */
    private String outBarcode;

    /**
     * 申请单id
     */
    private String applyId;

    /**
     * 审核开始时间
     */
    private Date startFinalCheckDate;

    /**
     * 审核结束时间
     */
    private Date endFinalCheckDate;

}
