package com.labway.lims.statistics.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <pre>
 * DisplayType
 * 展示机构形式
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/1/9 11:23
 */
@Getter
@AllArgsConstructor
public enum DisplayOrgType {

    // 单个机构
    SINGLE(1),
    // 机构合并
    MERGE(2),

    ;

    private final Integer type;

    public static DisplayOrgType getByType(Integer type) {
        for (DisplayOrgType displayOrgType : DisplayOrgType.values()) {
            if (displayOrgType.getType().equals(type)) {
                return displayOrgType;
            }
        }
        throw new IllegalArgumentException("不支持的类型");
    }
}
