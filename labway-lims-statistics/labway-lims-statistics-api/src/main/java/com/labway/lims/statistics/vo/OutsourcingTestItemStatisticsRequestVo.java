package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * <pre>
 * OutsourcingTestItemStatisticsRequestVo
 * 外送项目统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/2/18 11:52
 */
@Getter
@Setter
public class OutsourcingTestItemStatisticsRequestVo implements Serializable {

    /**
     * 开始外送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTwoPickDate;

    /**
     * 结束外送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTwoPickDate;

    /**
     * 外送机构ID
     */
    private Set<Long> exportOrgIds;

    /**
     * 检验项目编码
     */
    private Set<Long> testItemIds;
    private Set<String> testItemCodes;

}
