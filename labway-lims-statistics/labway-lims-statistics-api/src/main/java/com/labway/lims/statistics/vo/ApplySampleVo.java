package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;

/**
 * <p>
 * ApplySampleVo
 * 样本信息查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/8 13:39
 */
@Getter
@Setter
public class ApplySampleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 外部条码号
     */
    private String outBarcode;

    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 科室
     */
    private String dept;

    /**
     * 管型
     */
    private String tubeName;
    /**
     * 管型CODE
     */
    private String tubeCode;

    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 1: 急诊 0:不急
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 1: 急诊 0:不急
     *
     * @see UrgentEnum
     */
    private Integer urgentName;


    /**
     * 申请项目数量
     */
    private Integer testItemNum;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateDate;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新者
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 1:已删除 0：未删除
     *
     * @see YesOrNoEnum
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 是否已经分血，1：是，0：不是
     *
     * @see YesOrNoEnum
     */
    private Integer isSplitBlood;

    /**
     * 分血人ID
     */
    private Long splitterId;

    /**
     * 分血人
     */
    private String splitterName;

    /**
     * 分血时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date splitDate;

    /**
     * 样本状态 待审核 待复查 待二审 已审核 终止（99）
     *
     * @see SampleStatusEnum
     */
    private Integer status;

    /**
     * 是否禁用 1是 0 否
     *
     * @see YesOrNoEnum
     */
    private Integer isDisabled;

    /**
     * 检验人ID
     */
    private Long testerId;

    /**
     * 检验人姓名
     */
    private String testerName;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 项目类型ID
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 是否归档:0未归档，1已归档
     *
     * @see YesOrNoEnum
     */
    private Integer isArchive;

    /**
     * 1外送，0:不是外送
     *
     * @see YesOrNoEnum
     */
    private Integer isOutsourcing;

    /**
     * 样本备注
     */
    private String sampleRemark;

    /**
     * 结果备注
     */
    private String resultRemark;
    /**
     * 送检机构
     */
    private Long hspOrgId;
    /**
     * 送检机构
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 患者子年龄
     */
    private Integer patientSubage;

    /**
     * 患者子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 患者出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date patientBirthday;

    /**
     * 身份证
     */
    private String patientCard;

    /**
     * 证件类型
     *
     * @see PatientCardTypeEnum
     */
    private String patientCardType;

    /**
     * 床号
     */
    private String patientBed;

    /**
     * 性别
     *
     * @see com.labway.lims.api.enums.apply.SexEnum
     */
    private Integer patientSex;

    /**
     * 就诊卡号 (门诊|住院号)
     */
    private String patientVisitCard;

    /**
     * 手机号
     */
    private String patientMobile;

    /**
     * 患者地址
     */
    private String patientAddress;

    /**
     * 就诊类型 (申请类型)
     */
    private String applyTypeCode;

    /**
     * 就诊类型 (申请类型)
     */
    private String applyTypeName;
    /**
     * 样本个数
     */
    private Integer sampleCount;

    /**
     * 样本性状
     */
    private String sampleProperty;
    /**
     * 样本性状编码
     */
    private String samplePropertyCode;

    /**
     * 临床诊断
     */
    private String diagnosis;
    /**
     * 送检医生名称
     */
    private String sendDoctorName;

    /**
     * 送检医生编码
     */
    private String sendDoctorCode;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date applyDate;
    /**
     * 采样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date samplingDate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 申请单来源
     *
     * @see ApplySourceEnum
     */
    private String source;
    /**
     * 签收人
     */
    private Long signId;
    private String signName;

    /**
     * 签收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Shanghai")
    private Date signDate;

    /**
     * 收费状态
     */
    private String stopStatus;
    /**
     * 收费状态描述
     */
    private String stopStatusName;

    /**
     * 条码环节
     */
    private String barcodeLink;
    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;

    /**
     *  是否包含血培养项目
     */
    private Boolean hasBloodCultureItem;

	/**
	 * 标本部位
	 * @since 1.1.4
	 * @Description <a href="https://www.tapd.cn/59091617/prong/stories/view/1159091617001001756?from_iteration_id=1159091617001000242">评论</a>
	 */
	private String patientPart;

    /**
     * 检验项目编码
     */
    private ArrayList<String> testItemCodeList = new ArrayList<>();

    /**
     * 检验项目名称
     */
    private ArrayList<String> testItemNameList = new ArrayList<>();

    public String getTestItemNameStr() {
        if (CollectionUtils.isEmpty(testItemNameList)) {
            return Strings.EMPTY;
        }
        return StringUtils.join(testItemNameList, ",");
    }

    public String getTestItemCodeStr() {
        if (CollectionUtils.isEmpty(testItemCodeList)) {
            return Strings.EMPTY;
        }
        return StringUtils.join(testItemCodeList, ",");
    }
}
