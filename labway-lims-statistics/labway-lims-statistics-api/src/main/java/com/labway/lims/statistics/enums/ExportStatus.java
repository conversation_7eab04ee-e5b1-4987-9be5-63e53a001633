package com.labway.lims.statistics.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <pre>
 * ExportStatus
 * 文件导出状态
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/4/9 13:32
 */
@Getter
@AllArgsConstructor
public enum ExportStatus {

    RUNNING(1, "正在导出"),
    SUCCESS(2, "导出成功"),
    FAIL(3, "导出失败"),
    CANCEL(4, "已取消"),
    UNKNOW(-1, "未知"),
    ;

    private final Integer code;
    private final String message;

    public static ExportStatus getByCode(Integer code) {
        for (ExportStatus value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UNKNOW;
    }
}
