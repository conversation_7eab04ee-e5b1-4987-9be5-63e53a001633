package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/23 16:34
 */
@Getter
@Setter
public class SummarySpecimenVo {
    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 检验项目
     */
    private String testItemId;

    /**
     * 检验项目(检验目的)
     */
    private String testItemName;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 临床诊断
     */
    private String clinicalDiagnosis;

    /**
     * 样本类型
     */
    private String sampleType;

    /**
     * 接收日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date receiveDate;

    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date operateDate;
}
