package com.labway.lims.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.statistics.vo.HspOrgSendDoctorStatisticsRequestVo;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class PushSampleStatisticsInfoDto extends HspOrgSendDoctorStatisticsRequestVo implements Serializable {

    /**
     * 更新起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateDateBegin;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateDateEnd;

    /**
     * 当前页
     */
    private Integer pageNo;
    /**
     * 每页数量
     */
    private Integer pageSize;

    // 幂等标识
    private String idempotentCode;

}
