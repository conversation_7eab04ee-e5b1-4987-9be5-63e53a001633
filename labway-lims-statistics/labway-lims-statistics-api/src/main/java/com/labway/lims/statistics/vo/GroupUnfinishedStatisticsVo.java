package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 * GroupUnfinishedStatisticsVo
 * 专业组未完成工作量统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/7/5 11:13
 */
@Getter
@Setter
public class GroupUnfinishedStatisticsVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 列表数据
     */
    private List<GroupUnfinishedStatisticsApplySampleVo> groupUnfinishedApplySamples;

    /**
     * 合计
     */
    private Integer totalCount;

    public GroupUnfinishedStatisticsVo() {
        this.groupUnfinishedApplySamples = new ArrayList<>();
        this.totalCount = 0;
    }
}
