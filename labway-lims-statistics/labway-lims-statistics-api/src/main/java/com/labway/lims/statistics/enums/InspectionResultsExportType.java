package com.labway.lims.statistics.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <pre>
 * InspectionResultsExportType
 * 检验结果信息 导出类型 : 0-报告项目信息 1-人员检验项目信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/20 10:23
 */
@Getter
@AllArgsConstructor
public enum InspectionResultsExportType {

    /**
     * 报告项目
     */
    REPORT(0),

    /**
     * 人员检验项目
     */
    PATIENT_TEST_ITEM(1),

    ;

    private final Integer type;

    /**
     * 是否是导出报告项目
     */
    public static boolean isExportReport(Integer type) {
        return Objects.nonNull(type) && REPORT.getType().equals(type);
    }

}
