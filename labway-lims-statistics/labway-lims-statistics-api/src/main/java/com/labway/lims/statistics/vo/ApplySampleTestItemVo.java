package com.labway.lims.statistics.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * ApplySampleTestItemVo
 * 检验项目信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 14:45
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ApplySampleTestItemVo {

    /**
     * 管型
     */
    private String tubeName;
    /**
     * 管型编码
     */
    private String tubeCode;
    /**
     * 样本类型
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;
    /**
     * 是否禁用 1是 0 否
     *
     * @see YesOrNoEnum
     */
    private Integer isDisabled;
    private String isDisabledName;
    /**
     * 是否加急 1是 0否
     *
     * @see UrgentEnum
     */
    private Integer urgent;
    private String urgentName;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 外部项目编码
     */
    private Long outTestItemId;

    /**
     * 外部项目名称
     */
    private String outTestItemCode;

    /**
     * 外部项目名称
     */
    private String outTestItemName;

    /**
     * 专业组id
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 收费数量
     */
    private Integer count;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 是否免单
     *
     * @see YesOrNoEnum
     */
    private Integer isFree;

    /**
     * 外部条码
     */
    private String outBarcode;
    /**
     * 终止检验状态：0正常，1终止收费，2终止不收费
     *
     * @see StopTestStatus
     */
    private Integer stopStatus;
    /**
     * 终止检验状态描述
     *
     * @see StopTestStatus
     */
    private String stopStatusName;
    /**
     *  是否是血培养项目
     */
    private Boolean isBloodCultureItem;

    /**
     *  是否是血培养项目
     */
    private Boolean isBingliItem;

    /**
     * 检验方法ID
     */
    private String examMethodCode;

    /**
     * 检验方法名称
     */
    private String examMethodName;

    /**
     * 报告项目
     */
    private List<ReportItemVo> reportItems;

    @Getter
    @Setter
    public static class ReportItemVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 报告项目ID
         */
        private Long reportItemId;

        /**
         * 报告项目名称
         */
        private String reportItemName;

        /**
         * 报告项目编码
         */
        private String reportItemCode;

        /**
         * 检验项目ID
         */
        private Long testItemId;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 检验项目编码
         */
        private String testItemCode;

        /**
         * 1:已经删除 0未删除
         * @see YesOrNoEnum
         */
        private Integer isDelete;
    }

}
