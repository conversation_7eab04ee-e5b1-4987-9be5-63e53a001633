package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Set;

/**
 * 微生物统计
 */
@Getter
@Setter
public class MicrobiologyGermStatisticsRequestVo {

    /**
     * 检验日期
     */
    public static final int TEST_DATE = 1;
    /**
     * 审核日期
     */
    public static final int AUDIT_DATE = 2;


    /**
     * 阳
     */
    public static final int TYPE_YANG = 1;

    /**
     * 非阳
     */
    public static final int TYPE_NOT_YANG = 2;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 送检机构
     */
    private Set<Long> hspOrgIds;

    /**
     * 1: 检验日期；2: 审核日期
     *
     * @see #TEST_DATE
     * @see #AUDIT_DATE
     */
    private Integer dateType;

    /**
     * 检验项目
     */
    private Set<Long> testItemIds;


    /**
     * 同一患者同一样本细菌只统计一次 , 1:是，0:不是。判断是否是同一患者根据：姓名+性别+送检机构
     *
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer once;


    /**
     * 阳性类型：下拉选择，单选，选项为药敏阳性、非药敏阳性、所有阳性，默认所有阳性
     * <p>
     * 药敏阳性：统计结果值为阳性的抗生素 1
     * <p>
     * 非药敏阳性：除去统计结果值为阳性的抗生素 2
     * <p>
     * 所有阳性 0
     * </p>
     *
     * @see #TYPE_YANG
     * @see #TYPE_NOT_YANG
     */
    private Integer type;
    /**
     * 原始送检机构编码
     */
    private String originalOrgCode;
    /**
     * 原始送检机构名称
     */
    private String originalOrgName;

}
