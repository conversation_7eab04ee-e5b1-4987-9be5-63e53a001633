package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <pre>
 * ExportFileResultVo
 * 导出文件
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/4/10 13:21
 */
@Getter
@Setter
@Accessors(chain = true)
public class ExportFileResultVo implements Serializable {

    private List<ExportFileItem> items;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class ExportFileItem implements Serializable {

        private Long exportFileId;

        private String fileName;

        private String fileType;

        private String url;

        private String creatorName;

        private Date createDate;

        private Integer status;
    }

}
