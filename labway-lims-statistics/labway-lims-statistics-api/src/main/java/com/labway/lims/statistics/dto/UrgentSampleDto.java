package com.labway.lims.statistics.dto;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <pre>
 * UrgentSampleDto
 * 急诊样本信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/3/1 13:13
 */
@Getter
@Setter
public class UrgentSampleDto implements Serializable {

    // 急诊项目
    private String testItemNames;
    private String testItemCodes;

    /** 送检机构id */
    private Long hspOrgId;

    /** 送检机构名称 */
    private String hspOrgName;

    /** 申请单id */
    private Long applyId;

    /** 申请单样本id */
    private Long applySampleId;

    /** 样本id （可能是常规检验|微生物|院感...） */
    private Long sampleId;

    /** 样本号 */
    private String sampleNo;

    /** 专业小组id */
    private Long instrumentGroupId;

    /** 专业小组名称 */
    private String instrumentGroupName;

    /** 患者名称 */
    private String patientName;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /** 患者年龄 */
    private Integer patientAge;

    /** 患者子年龄 */
    private Integer patientSubage;

    /**
     * 患者子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 样本状态
     *
     * @see SampleStatusEnum
     */
    private String sampleStatus;

    /** 就诊类型 (申请类型) */
    private String applyTypeCode;

    /** 就诊类型 (申请类型) */
    private String applyTypeName;

    /** 样本类型 */
    private String sampleTypeCode;

    /** 样本类型名称 */
    private String sampleTypeName;

    /** 科室 */
    private String dept;

    /** 患者身份证号 */
    private String patientCard;

    /** 就诊卡号 (门诊|住院号) */
    private String patientVisitCard;

    /** 床号 */
    private String patientBed;

    /** 临床诊断 */
    private String diagnosis;

    /** 结果备注 */
    private String resultRemark;

    /** 样本备注 */
    private String sampleRemark;

    /** 签收日期 */
    private Date signDate;

    /** 申请时间（送检时间） */
    private Date applyDate;

    /** 采样时间 */
    private Date samplingDate;

    /** 检验时间 */
    private Date testDate;

    /** 专业组id */
    private Long groupId;

    /** 专业组名称 */
    private String groupName;

    /** 一审人id */
    private Long oneCheckerId;

    /** 一审人 */
    private String oneCheckerName;

    /** 二审人id */
    private Long twoCheckerId;

    /** 二审人 */
    private String twoCheckerName;

    /** 终审人id */
    private Long finalCheckerId;

    /** 终审人 */
    private String finalCheckerName;

    /** 终审时间 */
    private Date finalCheckDate;

    /**
     * 项目类型 常规检验、微生物检验、院感检验 区分做什么检验的
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /** 外部条码 */
    private String outBarcode;

    /** 样本条码 */
    private String barcode;

    /** 管型 */
    private String tubeName;

    /** 管型编码 */
    private String tubeCode;

    /** 创建人id */
    private Long creatorId;

    /** 创建人 */
    private String creatorName;

    /** 创建时间|录入时间 */
    private Date createDate;

    /** 备注 */
    private String remark;

    /** 原始机构编码 */
    private String originalOrgCode;

    /** 原始机构名称 */
    private String originalOrgName;
}
