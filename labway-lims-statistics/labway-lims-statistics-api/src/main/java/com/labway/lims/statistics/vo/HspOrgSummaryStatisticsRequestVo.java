package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.apply.api.dto.TestItemIncomeFilterDto;
import com.labway.lims.statistics.enums.DisplayOrgType;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Set;

/**
 * 销售项目收入查询--参数vo
 * 
 * <AUTHOR>
 * @since 2023/5/15 10:12
 */
@Setter
@Getter
public class HspOrgSummaryStatisticsRequestVo extends TestItemIncomeFilterDto {

    /**
     * 送检 时间 查询的就是 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date beginDeliveryDate;
    /**
     * 送检 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endDeliveryDate;
    /**
     * 财务月份 YYYY-MM
     */
    private String financialMonth;

    /**
     * 送检机构
     */
    private Set<Long> hspOrgIds;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 展示机构方式：单个机构:1 机构合并:2
     * @see DisplayOrgType
     */
    private Integer displayOrgType;

    /**
     * 页面来源
     */
    private String source;

    /**
     * 审核开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginAuditDate;
    /**
     * 审核开始结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endAuditDate;
}
