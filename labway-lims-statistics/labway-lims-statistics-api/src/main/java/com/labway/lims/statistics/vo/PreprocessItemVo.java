package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.concurrent.atomic.AtomicInteger;

@Getter
@Setter
public class PreprocessItemVo {
    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 项目id
     */
    private Long testItemId;
    /**
     * 项目名称
     */
    private String testItemName;
    /**
     * 数量
     */
    private AtomicInteger count;
}
