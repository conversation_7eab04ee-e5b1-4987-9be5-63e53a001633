package com.labway.lims.statistics.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/7/12 14:47
 */
@Setter
@Getter
public class PatientInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 身份证
     */
    private String patientCard;

    /**
     * 手机号
     */
    private String patientMobile;

    /**
     * 送检机构
     */
    private String hspOrgCode;

    /**
     * 送检机构
     */

    private String hspOrgName;


    /**
     * 就诊类型
     */
    private String applyType;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * uuid
     */
    private String id;

}
