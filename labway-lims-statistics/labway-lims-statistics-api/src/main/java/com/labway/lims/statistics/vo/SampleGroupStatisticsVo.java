package com.labway.lims.statistics.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * SampleGroupStatisticsVo
 * 申请单样本信息-机构分组
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 17:30
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SampleGroupStatisticsVo implements Serializable {

    /**
     * 机构ID
     */
    private Long hspOrgId;
    /**
     * 机构名称
     */
    private String hspOrgName;
    /**
     * 小计数量
     */
    private Integer totalCount;
    /**
     * 已审核数量
     */
    private Integer auditCount;
    /**
     * 未审核数量
     */
    private Integer unauditCount;

    /**
     * 样本列表信息
     */
    private List<SampleGroupStatisticsDetailVo> samples;

}
