package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/24 15:51
 */
@Setter
@Getter
public class TestItemStatisticsVo {
    /**
     * 专业组
     */
    private String groupName;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 合计数量
     */
    private Integer totalNum;

    /**
     * 合计金额
     */
    private BigDecimal totalAmount;

    /**
     * 项目价格
     */
    private BigDecimal itemAmount;

    /**
     * 额外列值
     */
    private Map<String, Integer> columns;

}
