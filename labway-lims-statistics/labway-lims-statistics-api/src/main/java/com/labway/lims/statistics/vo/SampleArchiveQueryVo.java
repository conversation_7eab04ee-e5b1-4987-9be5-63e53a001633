package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * SampleArchiveQueryVo
 * 样本归档信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/31 13:17
 */
@Getter
@Setter
public class SampleArchiveQueryVo {
    private Long searchAfter;
    /**
     * 归档时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date startCreateDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endCreateDate;

    /**
     * 检验项目
     */
    private String testItemCode;

    /**
     * 归档人
     */
    private Long creatorId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页数量
     */
    private Integer pageSize;
}
