package com.labway.lims.statistics.api.client;

import com.labway.lims.apply.api.dto.SampleEsQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * ElasticSearchSampleClient
 * ES搜索 FeignClient
 * </p>
 *
 * <AUTHOR>
 * @since 2023/10/23 16:37
 */
@FeignClient(value = "lims-statistics-es-client", path = "/es", url = "${lims.statistics.url}")
public interface ElasticSearchSampleClient {

    @PostMapping("selectSamples")
    String selectSamples(@RequestBody SampleEsQuery query);

}
