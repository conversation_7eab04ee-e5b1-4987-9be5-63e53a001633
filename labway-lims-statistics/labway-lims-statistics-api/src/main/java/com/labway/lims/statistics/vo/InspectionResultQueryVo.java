package com.labway.lims.statistics.vo;

import com.labway.lims.api.enums.routine.DateTypeEnum;
import com.labway.lims.statistics.dto.FieldData;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/7/12 13:48
 */
@Getter
@Setter
public class InspectionResultQueryVo {

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 时间类型
     *
     * @see DateTypeEnum
     */
    private String dateType;

    /**
     * 送检机构
     */
    private List<Long> hspOrgIds;

    /**
     * 报告项目编码
     */
    private Set<String> reportItemCodes;

    /**
     * 体检单位
     */
    private Long physicalCompanyId;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 检验项目
     */
    private List<Long> testItemIds;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 就诊类型
     */
    private Set<String> applyType;

    //private String applyType;

    /**
     * 结果前缀
     */
    private String formula1;

    /**
     * 结果
     */
    private String result1;

    /**
     * 结果前缀
     */
    private String formula2;

    /**
     * 结果
     */
    private String result2;

    /**
     * 是否仅显示异常结果
     *
     * @see com.labway.lims.api.enums.YesOrNoEnum
     */
    private Integer isExceptional;

    /**
     * current
     */
    private Integer current;

    /**
     * size
     */
    private Integer size;

    /**
     * searchAfter
     */
    private Object searchAfter;

    /**
     * 前端选择导出的数据
     */
    private List<SampleInspectionResultVo> selectData;

    /**
     *  导出类型 : 0- 报告项目信息 1-人员检验项目信息
     */
    private Integer exportType;

    /**
     * 批量排序
     */
    private List<SortData> sortData;

    /**
     * 页面来源
     */
    private String source;

    /**
     * 导出字段
     */
    private List<FieldData> fields;

    /**
     * 是否查询所有数据（分页的情况）
     */
    private boolean queryAll = false;

    @Data
    public static final class SortData{
        /**
         * 字段
         */
        private String sortField;

        /**
         * 排序规则
         */
        private String sortRule;
    }

}
