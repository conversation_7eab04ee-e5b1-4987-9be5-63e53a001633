package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 微生物统计
 */
@Getter
@Setter
public class MicrobiologyStatisticsRequestVo {

    public static final int DATE_TYPE_TEST_DATE = 1;
    public static final int DATE_TYPE_CHECK_DATE = 2;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 送检机构
     */
    private Set<Long> hspOrgIds;

    /**
     * 1: 检验日期；2: 审核日期
     *
     * @see #DATE_TYPE_TEST_DATE
     * @see #DATE_TYPE_CHECK_DATE
     */
    private Integer dateType;

    /**
     * 细菌备注编码
     */
    private List<String> germRemarkCodes;
}
