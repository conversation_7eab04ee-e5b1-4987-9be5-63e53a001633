package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * SampleStatisticsQueryVo
 * 描述信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 15:09
 */
@Getter
@Setter
public class SampleStatisticsQueryVo extends BaseDataStatisticsQueryVo {

    /**
     * 签收开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date signDateStart;

    /**
     * 签收结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date signDateEnd;

    /**
     * 审核状态 1已审核 0未审核 null全部
     */
    private Integer isAudit;

    /**
     * 是否显示禁用条码 1是 0 否
     * @see YesOrNoEnum
     */
    private Integer isDisabled;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 检验项目编码
     */
    private Set<String> testItemCodes;

    /**
     * 排序
     */
    private List<Sort> sortList;

    @Getter
    @Setter
    public static final class Sort implements Serializable {
        /**
         * 属性名
         */
        private String filedName;

        /**
         * 排序方式 ASC DESC
         */
        private String order;

    }

}
