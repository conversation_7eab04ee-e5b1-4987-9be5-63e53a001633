package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <pre>
 * ExportPatientInfoVo
 * 报告单 患者信息导出 参数
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/9/18 17:15
 */
@Getter
@Setter
public class ExportPatientInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Set<Long> applyIds;

    private Set<Long> applySampleIds;

    private List<SelectReportListRequestVo.SortData> sortData;

    public void check() {
        if (CollectionUtils.isEmpty(applyIds) &&
                CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("请选择数据进行导出");
        }
    }
}
