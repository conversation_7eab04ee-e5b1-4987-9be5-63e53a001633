package com.labway.lims.statistics.dto;

import com.labway.lims.api.field.Compare;
import com.labway.lims.statistics.vo.OutOrgStatisticsItemVo;
import lombok.Data;

import java.util.List;

/**
 *  外送机构统计列表页
 */
@Data
public class OutOrgStatisticsItemDTO {
    /**
     * 外送机构id
     */
    private Long exportOrgId;

    /**
     * 委托单位
     */
    private String exportOrgName;
    /**
     * 合计
     */
    private Integer Count;
    /**
     * 开票名称
     */
    @Compare("开票名称")
    private String invoice;
    /**
     *  外送统计信息
     */
    List<OutOrgStatisticsItemVo> outOrgStatisticsItems;
}
