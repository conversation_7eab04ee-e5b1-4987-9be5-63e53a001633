package com.labway.lims.statistics.vo;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * ApplySamplePageVo
 * 样本信息查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/9 18:50
 */
@Getter
@Setter
@Builder
public class ApplySamplePageVo {

    private Object searchAfter;

    private List<ApplySampleVo> applySamples;

    /**
     * 签收样本数
     */
    private Integer signCount;

    /**
     * 打印条码数
     */
    private Integer printCount;

}
