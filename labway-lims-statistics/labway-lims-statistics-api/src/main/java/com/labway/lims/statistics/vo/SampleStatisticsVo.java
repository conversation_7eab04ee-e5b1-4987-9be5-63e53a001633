package com.labway.lims.statistics.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * SampleStatisticsVo
 * 申请单样本信息统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/1 14:27
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SampleStatisticsVo implements Serializable {

    private List<SampleGroupStatisticsVo> list;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 已审核数量
     */
    private Integer auditCount;
    /**
     * 未审核数量
     */
    private Integer unauditCount;

}
