package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <pre>
 * TestResultOntimeRateRequestVo
 * 检验结果及时率统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/2/29 11:56
 */
@Getter
@Setter
public class TestResultOntimeRateRequestVo extends BaseDataStatisticsQueryVo {

    /** 送检机构 */
    private List<Long> hspOrgIds;

    /** 专业组Id */
    private List<Long> groupIds;

    /** 检验项目 */
    private String testItemCode;

    /** 检验项目 */
    private List<String> testItemCodes;

    /** 就诊类型 */
    private List<String> applyTypeCodes;
}
