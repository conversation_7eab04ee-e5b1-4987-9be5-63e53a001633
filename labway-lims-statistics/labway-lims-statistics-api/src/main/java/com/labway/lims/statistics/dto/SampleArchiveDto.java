package com.labway.lims.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * SampleArchiveDto
 * 样本归档信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/31 13:37
 */
@Setter
@Getter
public class SampleArchiveDto implements Serializable {

    /**
     * 逻辑试管架占用id
     */
    private Long rackLogicSpaceId;
    /**
     * 归档库
     */
    private String archiveStoreName;
    /**
     * 冰箱
     */
    private String refrigeratorName;
    /**
     * 试管架编码
     */
    private String rackCode;
    /**
     * 试管架名称
     */
    private String rackName;

    /**
     * 试管架-行
     */
    private Integer rackRow;
    /**
     * 试管架-列
     */
    private Integer rackColumn;
    /**
     * 行
     */
    private Integer row;
    /**
     * 列
     */
    private Integer column;
    /**
     * 申请单样本ID
     */
    private Long applySampleId;
    /**
     * 条码号
     */
    private String barcode;
    /**
     * 样本号
     */
    private String sampleNo;
    /**
     * 姓名
     */
    private String patientName;
    /**
     * 性别
     */
    private String patientSex;
    /**
     * 年龄
     */
    private Integer patientAge;
    /**
     * 子年龄    xxx天|xxx周|xxx月
     */
    private Integer patientSubage;
    /**
     * 子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;
    /**
     * 门诊/住院号
     */
    private String patientVisitCard;
    /**
     *  检验日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date testDate;
    /**
     * 检验项目编码
     */
    private String testItemCode;
    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 归档人
     */
    private String creatorName;
    /**
     * 归档时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;

}
