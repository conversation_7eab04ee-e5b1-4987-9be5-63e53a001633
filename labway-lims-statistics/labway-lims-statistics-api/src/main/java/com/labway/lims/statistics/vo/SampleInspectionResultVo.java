package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class SampleInspectionResultVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private String pid;

    /**
     * id
     */
    private String id;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 送检机构
     */
    private String hspOrgCode;

    /**
     * 送检机构
     */

    private String hspOrgName;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 就诊类型
     */
    private String applyType;

    /**
     * 条码号
     */
    private String barcode;


    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目code
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 报告项目ID
     */
    private Long reportItemId;

    /**
     * 报告项目编码
     */
    private String reportItemCode;

    /**
     * 报告项目名称
     */
    private String reportItemName;

    /**
     * 结果值
     */
    private String result;

    /**
     * 单位
     */
    private String unit;

    /**
     * 提示符
     * @see TestJudgeEnum
     */
    private String judge;

    /**
     * 参考范围
     */
    private String range;
    /**
     * 专业组名称
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 申请时间 (送检时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date applyDate;

    /**
     * 检验时间
     */
    private Date testDate;

    /**
     * 审核时间
     */
    private Date checkDate;

    /**
     * 门诊|住院号
     */
    private String patientVisitCard;

    /**
     * 送检医生名称
     */
    private String sendDoctorName;

    /**
     * 送检医生编码
     */
    private String sendDoctorCode;

    /**
     * 科室
     */
    private String dept;

    /**
     * 床号
     */
    private String patientBed;

    /**
     * 状态 1:危机 2:异常 0:正常
     *
     * @see ResultStatusEnum
     */
    private Integer status;

    /**
     * 外部条码号
     */
    private String outBarcode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 身份证
     */
    private String patientCard;

    /**
     * 手机号
     */
    private String patientMobile;
}