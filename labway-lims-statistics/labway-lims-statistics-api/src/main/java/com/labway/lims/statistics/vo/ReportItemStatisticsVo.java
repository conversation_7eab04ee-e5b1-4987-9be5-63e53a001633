package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class ReportItemStatisticsVo {
    /**
     * 专业组
     */
    private String groupName;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 项目编码
     */
    private String reportItemCode;

    /**
     * 检验项目名称
     */
    private String reportItemName;

    /**
     * 来源仪器
     */
    private Long instrumentId;
    /**
     * 来源仪器
     */
    private String instrumentName;
    /**
     * 复查数量
     */
    private Long retestNum;

    /**
     * 合计数量
     */
    private Long totalNum;

    /**
     * 报告数量
     */
    private Long reportItemNum;

    /**
     * 额外列值
     */
    private Map<String, Integer> columns;

}
