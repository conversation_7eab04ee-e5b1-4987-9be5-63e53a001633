package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class PushSampleStatisticsInfoVo implements Serializable {

    @NotEmpty(message = "统计样本信息不能为为空！")
    List<SampleStatisticsRequest> sampleStatisticsRequests;

    // 删除统计样本的开始时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deleteCreateTimeBegin;

    // 删除统计样本的结束时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deleteCreateTimeEnd;

    // 删除样本批次号(用于标识删除样本的随机值，每一批次只删除一次)
    private String deleteHashKey;


    @Data
    @NoArgsConstructor
    public static class SampleStatisticsRequest implements Serializable {
        //样本统计表id
        private String statisticsId;
        //样本编号-全局唯一
        private String sampleCode;
        //病人姓名
        private String patientName;
        //病人性别 0未知 1男 2女
        private Integer patientSex;
        //病人年龄
        private Integer patientAge;
        //年龄单位
        private String ageUnit;
        //病人生日
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date patientBirthday;
        //门诊/住院登记号
        private String patientRegistrationNo;
        //就诊卡号
        private String treatmentCard;
        //就诊类型 门诊/住院等
        private String treatmentType;
        //床号
        private String bedNo;
        //样本科室
        private String dept;
        //样本条码号（本系统条码号）
        @NotBlank(message = "条码号不能为空")
        private String barcode;
        //外部条码号（送检机构的条码号）
        private String outBarcode;
        //His条码号（一般情况下同out_barcode）
        private String hisBarcode;
        //样本创建时间（三方系统的创建时间）
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date sampleCreateDate;
        //送检时间
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date sendTime;
        //送检医生
        private String sendDoctorCode;
        //送检医生名称
        private String sendDoctorName;
        //检验时间
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date testTime;
        //检验人编码
        private String testUserCode;
        //检验人名称
        private String testUserName;
        //审核时间
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date auditTime;
        //审核人编码
        private String auditUserCode;
        //审核人名称
        private String auditUserName;
        //检验项目编码
        @NotBlank(message = "检验项目编码不能为空")
        private String testItemCode;
        //检验项目名称
        private String testItemName;
        //外部项目编码
        private String outTestItemCode;
        //外部项目名称
        private String outTestItemName;
        //数量
        private Integer itemCount;
        //标准单价
        private BigDecimal standardPrice;
        //合计金额
        private BigDecimal totalAmount;
        //结算金额
        private BigDecimal settlementAmount;
        //结算类型编码
        private String settlementTypeCode;
        //结算类型名称
        private String settlementTypeName;
        //结算备注
        private String settlementRemark;
        //折扣
        private String discount;
        //创建时间
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date createTime;
        //创建人
        private String createBy;
        //更新时间
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
        private Date updateTime;
        //更新人
        private String updateBy;
        //删除标识 0未删除 1删除
        private Integer deleteFlag;

        // 机构编码（推送样本的机构）
        @NotBlank(message = "样本机构编码不能为空")
        private String orgCode;
        // 机构名称（推送样本的机构）
        private String orgName;
        // 机构类型 1,lims 2,lis
        private Integer orgType;
        // 原始送检机构编码
        private String originalOrgCode;
        // 原始送检机构名称
        private String originalOrgName;

        // 是否是分血样本 0否1是
        private Integer isSplit;
        // 样本id
        private String applySampleId;
    }

}
