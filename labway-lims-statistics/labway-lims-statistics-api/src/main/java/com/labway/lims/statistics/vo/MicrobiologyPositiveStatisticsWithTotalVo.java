package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * MicrobiologyPositiveStatisticsWithTotalVo
 * 阳性统计结果+合计行数据
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/10 15:37
 */
@Getter
@Setter
public class MicrobiologyPositiveStatisticsWithTotalVo {

    /**
     * 合计
     */
    private Integer total = 0;

    /**
     * 共有菌株数
     */
    private Integer germTotalCount = 0;

    /**
     * 未做药敏菌株数
     */
    private Integer germNoMedicineCount = 0;

    /**
     * 已做药敏菌株数
     */
    private Integer germMedicineCount = 0;

    List<MicrobiologyPositiveStatisticsVo> datas = Collections.emptyList();

}
