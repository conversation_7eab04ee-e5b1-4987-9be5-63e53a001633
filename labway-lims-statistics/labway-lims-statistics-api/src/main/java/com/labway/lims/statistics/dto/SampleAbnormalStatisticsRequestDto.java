package com.labway.lims.statistics.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 异常样本dto
 */
@Data
public class SampleAbnormalStatisticsRequestDto {


    /**
     * 送检时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginSendDate;

    /**
     * 送检时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSendDate;

    /**
     * 送检机构
     */
    private List<Long> hspOrgIds;

    /**
     * 专业组
     */
    private List<Long> groupIds;

    /**
     * 检验项目
     */
    private List<Long> testItemIds;

    /**
     * 样本性状code
     */
    private List<String> samplePropertyCodes;



}
