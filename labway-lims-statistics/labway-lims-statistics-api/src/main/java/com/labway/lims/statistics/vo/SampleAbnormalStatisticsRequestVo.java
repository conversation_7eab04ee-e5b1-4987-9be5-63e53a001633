package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 送检机构异常样本统计
 */
@Getter
@Setter
public class SampleAbnormalStatisticsRequestVo {
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginSignDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSignDate;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 异常原因编码
     */
    private String abnormalReasonCode;
}
