package com.labway.lims.statistics.api.client;

import com.labway.lims.apply.api.dto.ReportMergePrintInfoDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <pre>
 * ReportService
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/8/9 14:08
 */
public interface ReportService {

    ReportMergePrintInfoDto populateExtraInfo(
            ReportMergePrintInfoDto printInfoDto, BaseSampleEsModelDto sampleEsModelDto);


    /**
     * pdf合并处理
     * @return urlList
     */
    List<String> mergePrint(Set<Long> applySampleIds);

    /**
     * 多张报告单合并成一张报告单
     * @see com.labway.lims.pdfreport.api.service.PdfReportService#mergePdfByUrls2Url(Collection)
     */
    @Deprecated
    String mergePdfs(List<String> pdfUrls, String barcode);
}
