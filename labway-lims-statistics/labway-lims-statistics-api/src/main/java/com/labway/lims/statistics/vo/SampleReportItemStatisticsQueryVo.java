package com.labway.lims.statistics.vo;

import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * SampleStatisticsVo
 * 报告项目信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 14:45
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SampleReportItemStatisticsQueryVo extends BaseSampleEsModelDto.ReportItem {

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码号
     */
    private String barcode;

}
