package com.labway.lims.statistics.vo;

import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;

/**
 * 统计查询条码 base vo
 */
@Getter
@Setter
public class BaseDataStatisticsQueryVo {
    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 分页
     */
    private String searchAfterStr;

    /**
     * 默认当天
     */
    public void defaultDate() {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            final Date date = new Date();
            startDate = DateUtil.beginOfDay(date);
            endDate = DateUtil.endOfDay(date);
        }

    }
}
