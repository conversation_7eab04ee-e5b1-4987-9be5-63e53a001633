package com.labway.lims.statistics.vo;

import com.labway.lims.apply.api.dto.TestItemIncomeSummaryResponseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class IncomeSummaryVo {
    private BigDecimal totalFeePriceSum;
    private BigDecimal totalPayAmountSum;


    private BigDecimal totalDetailFeePriceSum;

    private List<TestItemIncomeSummaryResponseDto> list;
}
