package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 报告单 -查询报告单列表 响应Vo
 * 
 * <AUTHOR>
 * @since 2023/4/18 15:26
 */
@Getter
@Setter
public class SelectReportListResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 前端使用
     */
    private Long soleId;

    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 送检机构编码
     */
    private String hspOrgCode;
    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 送检机构下具体患者信息
     */
    private List<ReportItemResponseVo> reportItemList;

}
