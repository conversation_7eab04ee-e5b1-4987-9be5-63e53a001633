package com.labway.lims.statistics.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 报表项信息
 *
 * <AUTHOR>
 * @since 2023/4/18 15:24
 */
@Getter
@Setter
public class ReportItemResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 前端使用
     */
    private Long soleId;
    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 申请单id
     */
    private Long applyId;
    /**
     * 申请单样本id
     */
    private Long applySampleId;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;
    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 患者子年龄
     */
    private Integer patientSubage;

    /**
     * 患者子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;
    /**
     * 就诊类型 (申请类型)
     */
    private String applyType;
    /**
     * 科室
     */
    private String dept;

    /**
     * 患者身份证号
     */
    private String patientCard;

    /**
     * 就诊卡号 (门诊|住院号)
     */
    private String patientVisitCard;
    /**
     * 床号
     */
    private String patientBed;
    /**
     * 临床诊断
     */
    private String diagnosis;
    /**
     * 结果备注
     */
    private String resultRemark;
    /**
     * 是否打印:1已打印，0未打印
     */
    private Integer isPrint;
    /**
     * 采样时间
     */
    private Date samplingDate;
    /**
     * 检验时间
     */
    private Date testDate;
    /**
     * 专业组id
     */
    private Long groupId;
    /**
     * 专业组名称
     */
    private String groupName;
    /**
     * 一审人id
     */
    private Long oneCheckerId;
    /**
     * 一审人
     */
    private String oneCheckerName;
    /**
     * 二审人id
     */
    private Long twoCheckerId;
    /**
     * 二审人
     */
    private String twoCheckerName;
    /**
     * 通用报告信息
     */

    private List<BaseSampleEsModelDto.Report> reports;
    /**
     * 项目类型 常规检验、微生物检验、院感检验 区分做什么检验的
     *
     * @see ItemTypeEnum
     */

    private String itemType;
    /**
     * 样本id （可能是常规检验|微生物|院感...）
     */
    private Long sampleId;
    /**
     * 样本条码
     */
    private String barcode;
    /**
     * 检验项目
     */

    private List<BaseSampleEsModelDto.TestItem> testItems;
    /**
     * 创建时间|录入时间
     */

    private Date createDate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;
    //--------------------------------------------
    /**
     * 报告时间
     */
    private Date reportDate;
    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 报告项目
     */
    private List<RoutineInspectionDto.RoutineReportItem> reportItems;
}
