package com.labway.lims.statistics.enums;

import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Function;

/**
 * <p>
 * FieldConvertEnum
 * 描述信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/14 11:54
 */
@Getter
@AllArgsConstructor
public enum FieldValueConverterEnum {

    DEFAULT("", value -> value),

    SIGN_DATE("signDate", value -> DateUtil.parseDateTime(value).getTime()),
    CREATE_DATE("createDate", value -> DateUtil.parseDateTime(value).getTime()),
    APPLY_DATE("applyDate", value -> DateUtil.parseDateTime(value).getTime()),
    SAMPLE_DATE("sampleDate", value -> DateUtil.parseDateTime(value).getTime()),
    TEST_DATE("testDate", value -> DateUtil.parseDateTime(value).getTime()),
    ONE_CHECK_DATE("oneCheckDate", value -> DateUtil.parseDateTime(value).getTime()),
    FINAL_CHECK_DATE("finalCheckDate", value -> DateUtil.parseDateTime(value).getTime()),

    ;

    private final String field;
    private final Function<String, ?> converter;


    public static Object convert(String field, String value) {
        for (FieldValueConverterEnum convertEnum : FieldValueConverterEnum.values()) {
            if (convertEnum.getField().equals(field)) {
                return convertEnum.converter.apply(value);
            }
        }
        return DEFAULT.converter.apply(value);
    }

}
