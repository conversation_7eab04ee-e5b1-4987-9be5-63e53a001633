package com.labway.lims.statistics.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 送检机构申请单统计
 */
@Data
public class HspOrgStatisticsUploadImgVo implements Serializable {
    /**
     * 签收日期开始
     */
    @NotEmpty(message = "申请单样本id不能为空")
    private List<Long> applySampleIds;

    /**
     * 图片地址
     */
    @NotEmpty(message = "图片地址不能为空")
    private List<String> imgUrls;

}
