package com.labway.lims.statistics.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 外送 TAT 统计
 */
@Getter
@Setter
public class OutsourcingTatStatisticsVo {
    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 就诊类型，申请单类型
     */
    private String applyType;

    /**
     * 样本类型
     */
    private String sampleTypeName;
    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 外送机构ID
     */
    private Long exportOrgId;

    /**
     * 外送机构名称
     */
    private String exportOrgName;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 检验项目名称
     */
    private List<String> testItemNames;


    /**
     * 录入时间 // 暂不确定 和 签收日期冲突
     */
    private Date createDate;

    /**
     * 签收日期
     */
    private Date signDate;

    /**
     * 外送分拣日期 ， 也就是二次分拣日期
     */
    private Date pickDate;

    /**
     * 打印日期，也就是打印清单日期
     */
    private Date printListDate;

    /**
     * 报告录入日期
     */
    private Date backReportDate;


    /**
     * 交接日期
     */
    private Date handoverDate;


    /**
     * 审核日期
     */
    private Date checkDate;


    public Date getCheckDate() {
        if (Objects.isNull(checkDate) || DefaultDateEnum.DEFAULT_DATE.getDate().equals(checkDate)) {
            return null;
        }
        return checkDate;
    }

    public Date getPrintListDate() {
        if (Objects.isNull(printListDate) || DefaultDateEnum.DEFAULT_DATE.getDate().equals(printListDate)) {
            return null;
        }
        return printListDate;
    }

    public Date getPickDate() {
        if (Objects.isNull(pickDate) || DefaultDateEnum.DEFAULT_DATE.getDate().equals(pickDate)) {
            return null;
        }
        return pickDate;
    }

    public Date getHandoverDate() {
        if (Objects.isNull(handoverDate) || DefaultDateEnum.DEFAULT_DATE.getDate().equals(handoverDate)) {
            return null;
        }
        return handoverDate;
    }

    public Date getSignDate() {
        if (Objects.isNull(signDate) || DefaultDateEnum.DEFAULT_DATE.getDate().equals(signDate)) {
            return null;
        }
        return signDate;
    }
}
