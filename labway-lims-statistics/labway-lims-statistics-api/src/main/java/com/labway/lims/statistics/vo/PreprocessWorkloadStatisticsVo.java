package com.labway.lims.statistics.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

@Getter
@Setter
public class PreprocessWorkloadStatisticsVo {
    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 人数
     */
    private AtomicInteger peopleCount;

    /**
     * 项目数
     */
    private AtomicInteger testItemCount;
    /**
     * 手动录入样本数
     */
    private AtomicInteger manualInputSampleCount;

    /**
     * 补录样本数
     */
    private AtomicInteger supplementSampleCount;
    /**
     * 签收样本数
     */
    private AtomicInteger signSampleCount;

    /**
     * 管型统计
     */
    private Map<String, AtomicInteger> tubeStatisticsMap;

    /**
     * 条码数
     */
    public Integer getBarcodeCount() {
        int barcodeCount = 0;
        if (Objects.nonNull(manualInputSampleCount)) {
            barcodeCount += manualInputSampleCount.get();
        }

        if (Objects.nonNull(supplementSampleCount)) {
            barcodeCount += supplementSampleCount.get();
        }
        if (Objects.nonNull(signSampleCount)) {
            barcodeCount += signSampleCount.get();
        }

        return barcodeCount;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static final class BarcodeStatistics {
        /**
         * 送检机构ID
         */
        private Long hspOrgId;

        /**
         * 条码号
         */
        private String barcode;

        /**
         * @see com.labway.lims.api.enums.apply.ApplySourceEnum
         */
        private String source;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            final BarcodeStatistics that = (BarcodeStatistics) o;
            return Objects.equals(hspOrgId, that.hspOrgId) && Objects.equals(barcode, that.barcode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(hspOrgId, barcode);
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static final class TubeStatistics {

        /**
         * 管子编码
         */
        private String tubeCode;

        /**
         * 管子名称
         */
        private String tubeName;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            final TubeStatistics that = (TubeStatistics) o;
            return Objects.equals(tubeCode, that.tubeCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(tubeCode);
        }
    }
}
