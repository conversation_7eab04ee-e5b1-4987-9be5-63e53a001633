package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * TimeoutReminderQueryVo
 * 超时提醒参数
 * </p>
 *
 * <AUTHOR>
 * @since 2023/10/12 20:37
 */
@Getter
@Setter
public class TimeoutReminderSampleVo {


    /**
     * 申请单样本id
     */
    private Long applySampleId;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 主条码
     */

    private String masterBarcode;

    /**
     * 患者名称
     */

    private String patientName;

    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 患者子年龄
     */
    private Integer patientSubage;

    /**
     * 患者子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 性别
     *
     * @see com.labway.lims.api.enums.apply.SexEnum
     */

    private Integer patientSex;

    /**
     * 签收日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date signDate;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 样本条码
     */
    private String barcode;
    /**
     * 检验项目ID
     */
    private Long testItemId;
    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验时间
     */
    private int checkLimitDay;

    /**
     * 超时时间
     */
    private int timeOutDays;

    /**
     * 创建时间|录入时间
     */
    private Date createDate;

}
