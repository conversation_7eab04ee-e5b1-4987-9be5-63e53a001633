package com.labway.lims.statistics.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <pre>
 * ExportStatus
 * 病理报告状态
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/11/14 13:32
 */
@Getter
@AllArgsConstructor
public enum BingliReportStatus {
    ALL(-1,"全部"),
    UN_REPORT(0, "未报告"),
    REPORT(1, "已报告"),
    ;

    private final Integer code;
    private final String message;

    public static BingliReportStatus getByCode(Integer code) {
        for (BingliReportStatus value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return ALL;
    }
}
