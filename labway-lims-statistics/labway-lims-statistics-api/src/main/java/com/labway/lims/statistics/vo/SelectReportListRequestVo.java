package com.labway.lims.statistics.vo;

import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 报告单 -查询报告单列表 参数 Vo
 *
 * <AUTHOR>
 * @since 2023/4/18 15:22
 */
@Getter
@Setter
public class SelectReportListRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 签收日期开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginSignDate;

    /**
     * 签收日期结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSignDate;

    /**
     * 审核日期开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginCheckDate;
    /**
     * 审核日期结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCheckDate;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构IDs
     */
    private List<Long> hspOrgIds;

    /**
     * 体检单位ID
     */
    private Long physicalGroupId;

    /**
     * 科室
     */
    private String dept;

    /**
     * 专业组ID
     */
    private Long groupId;
    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目（批量）
     */
    private Set<Long> testItemIds;
    /**
     * 姓名
     */
    private String patientName;
    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;
    /**
     * 就诊类型
     * 支持多选
     */
    private Set<String> applyTypes;
    /**
     * 条码号
     */
    private String barcode;
    /**
     * 门诊/住院号
     */
    private String patientVisitCard;
    /**
     * 是否打印:1已打印，0未打印
     */
    private Integer isPrint;

    /**
     * 是否限制展示没有出全的结果 1 是，0 否
     */
    private Integer limitPartUnFinalSamples;
    /**
     * 打印日期开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginPrintDate;
    /**
     * 打印日期结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endPrintDate;

    /**
     * 批量排序
     */
    private List<SortData> sortData;

    @Data
    public static final class SortData implements Serializable {

        private static final long serialVersionUID = 1L;
        /**
         * 字段
         */
        private String sortField;

        /**
         * 排序规则
         */
        private String sortRule;
    }
}
