package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * TAT 统计
 */
@Getter
@Setter
public class OutsourcingTatStatisticsRequestVo {
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginSignDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSignDate;

    /**
     * 外送机构
     */
    private Long exportOrgId;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 条码号
     */
    private String barcode;


    /**
     * 病人名称
     */
    private String patientName;

}
