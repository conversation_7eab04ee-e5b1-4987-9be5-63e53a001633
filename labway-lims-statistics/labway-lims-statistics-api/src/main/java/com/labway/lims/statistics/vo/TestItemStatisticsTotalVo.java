package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/21 16:23
 */
@Getter
@Setter
public class TestItemStatisticsTotalVo {

    /**
     * 合计
     */
    private Integer total;

    /**
     * 合计数量
     */
    private Integer totalNumSum;

    /**
     * 合计金额
     */
    private BigDecimal totalAmountSum;

    /**
     * 额外列
     */
    private Map<String, BigDecimal> extraColumnSum;

}
