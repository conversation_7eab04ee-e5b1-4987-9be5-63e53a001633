package com.labway.lims.statistics.dto;

import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class SelectSampleDto extends BaseSampleEsModelDto {

    private static final long serialVersionUID = 1L;
    /**
     * 一审人id
     */
    private Long oneCheckerId;
    /**
     * 一审人
     */
    private String oneCheckerName;
    /**
     * 一审时间
     */
    private Date oneCheckDate;
    /**
     * 二审人id
     */
    private Long twoCheckerId;
    /**
     * 二审人
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    private Date twoCheckDate;
}
