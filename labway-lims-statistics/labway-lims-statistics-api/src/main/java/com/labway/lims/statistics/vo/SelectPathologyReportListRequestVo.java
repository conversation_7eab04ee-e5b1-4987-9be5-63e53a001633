package com.labway.lims.statistics.vo;

import com.alibaba.excel.util.StringUtils;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <pre>
 * SelectPathologyReportListRequestVo
 * 报告单 -病理报告单打印 参数
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/2/4 13:17
 */
@Getter
@Setter
public class SelectPathologyReportListRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 登记日期开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginCheckInDate;

    /**
     * 登记日期结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCheckInDate;

    /**
     * 审核日期开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginCheckDate;
    /**
     * 审核日期结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCheckDate;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构ID
     */
    private String hspOrgCode;
    /**
     * 条码号
     */
    private String barcode;
    private Set<String> barcodes;

    /**
     * 科室
     */
    private String dept;
    /**
     * 姓名
     */
    private String patientName;
    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;
    /**
     * 就诊类型
     * 支持多选
     */
    private Set<String> applyTypes;
    /**
     * 门诊/住院号
     */
    private String patientVisitCard;
    /**
     * 病理号
     */
    private String pathologyNo;
    /**
     * 病理类型
     */
    private String pathologyType;
    /**
     * 是否打印:1已打印，0未打印
     */
    private Integer isPrint;

    /**
     * 专业组ID
     */
    private Long groupId;
    /**
     * 检验项目
     */
    private Long testItemId;
    /**
     * 批量排序
     */
    private List<SelectReportListRequestVo.SortData> sortData;

    /**
     * 病理报告状态
     * @see com.labway.lims.statistics.enums.BingliReportStatus
     */
    private Integer reportStatus;

    public Set<String> getBarcodes() {
        if (CollectionUtils.isEmpty(this.barcodes)) {
            this.setBarcodes(new HashSet<>());
        }
        if (StringUtils.isNotBlank(barcode)) {
            barcodes.add(barcode);
        }
        return barcodes;
    }

    @Data
    public static final class SortData {
        /**
         * 字段
         */
        private String sortField;

        /**
         * 排序规则
         */
        private String sortRule;
    }

}
