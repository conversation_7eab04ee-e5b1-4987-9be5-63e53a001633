package com.labway.lims.statistics.vo;

import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Set;

/**
 * TAT 统计
 */
@Getter
@Setter
public class TatStatisticsRequestVo {
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 送检机构
     */
    private Set<Long> hspOrgIds;

    /**
     * 专业组ID
     */
    private Set<Long> groupIds;

    /**
     * 条码号
     */
    private String barcode;


    /**
     * 1: 急诊，0:不急
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 就诊类型，申请单类型
     */
    private Set<String> applyTypes;

}
