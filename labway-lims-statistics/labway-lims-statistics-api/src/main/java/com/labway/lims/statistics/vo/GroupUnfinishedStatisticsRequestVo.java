package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <pre>
 * GroupUnfinishedStatisticsRequestVo
 * 专业组未完成工作量统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/7/5 11:20
 */
@Getter
@Setter
public class GroupUnfinishedStatisticsRequestVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 送检时间 开始
     */
    @NotNull(message = "请选择开始时间")
    private Date startDate;

    /**
     * 送检时间 结束
     */
    @NotNull(message = "请选择结束时间")
    private Date endDate;

    /**
     * 送检机构
     */
    private List<String> hspOrgCodes;

    /**
     * 专业组
     */
    @NotNull(message = "请选择专业组")
    @Size(min = 1, message = "请选择一个专业组")
    @Size(max = 1, message = "只能选择一个专业组")
    private List<Long> groupIds;

    /**
     * 就诊类型
     */
    private List<String> applyTypes;

    /**
     * 未一次分拣的申请单样本
     */
    private Set<Long> waitingOnePickApplySampleIds;

}
