package com.labway.lims.statistics.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SendItemAndApplyTypeVo {

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 签收时间
     */
    private String date;

    /**
     * 就诊类型code
     */
    private String applyTypeCode;

    /**
     * 就诊类型名称
     */
    private String applyTypeName;
    /**
     * 签收数量
     */
    private AtomicInteger signCount;

    /**
     * 审核数量
     */
    private AtomicInteger auditCount;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        final SendItemAndApplyTypeVo that = (SendItemAndApplyTypeVo) o;
        return Objects.equals(hspOrgId, that.hspOrgId) && Objects.equals(testItemId, that.testItemId) && Objects.equals(date, that.date) && Objects.equals(applyTypeCode, that.applyTypeCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(hspOrgId, testItemId, date, applyTypeCode);
    }
}

