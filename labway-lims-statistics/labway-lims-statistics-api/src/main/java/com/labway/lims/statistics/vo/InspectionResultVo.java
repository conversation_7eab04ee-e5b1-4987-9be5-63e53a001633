package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/12 14:47
 */
@Setter
@Getter
public class InspectionResultVo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Object searchAfter;

    /**
     * 样本数
     */
    private Long sampleCount;

    /**
     * 报告项目明细
     */
    private List<SampleInspectionResultVo> results;

    /**
     * { key: 送检机构编码 , value: 结果列表 }
     */
    private List<ReportInfoVo> hospitals;

    /**
     * 人员信息
     */
    private List<PatientInfoVo> patientInfos;

}





