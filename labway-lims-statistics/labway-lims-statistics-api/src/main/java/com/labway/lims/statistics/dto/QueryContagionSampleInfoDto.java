package com.labway.lims.statistics.dto;

import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class QueryContagionSampleInfoDto implements Serializable {

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 性别
     */
    private String sex;

    /**
     * 年龄
     */
    private String age;

    /**
     * 科室
     */
    private String department;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 外部条码号
     */
    private String outBarcode;

    /**
     * 接收时间
     */
    private Date receiveTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 检验项目信息
     */
    private List<ContagionTestItemDto> testItems;


    @Data
    @NoArgsConstructor
    public static class ContagionTestItemDto implements Serializable{

        /**
         * 检验项目id
         */
        private String testItemId;

        /**
         * 检验项目编码
         */
        private String testItemCode;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 报告项目信息
         */
        private List<ContagionReportItemDto> reportItemDtos;

    }


    @Data
    @NoArgsConstructor
    public static class ContagionReportItemDto implements Serializable{

        /**
         * 报告项目编码
         */
        private String reportItemCode;

        /**
         * 报告项目名称
         */
        private String reportItemName;

        /**
         * 结果 （经过一系列的计算 转换最终得到的结果值）
         */
        private String result;

        /**
         * 检验判定 UP  DOWN  NORMAL
         * @see TestJudgeEnum
         */
        private String judge;


    }

}
