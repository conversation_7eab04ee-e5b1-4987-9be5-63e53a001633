package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <pre>
 * InspectionResultSummaryVo
 * 检验结果信息查询 统计信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/13 16:03
 */
@Getter
@Setter
public class InspectionResultSummaryVo {

    /**
     * 人员检验项目 总数
     */
    private Integer patientCount;

    /**
     * 检验结果 总数
     */
    private Integer totalCount;

    /**
     * 报告项目明细 机构统计
     */
    private List<ReportInfoVo> hospitals;

}
