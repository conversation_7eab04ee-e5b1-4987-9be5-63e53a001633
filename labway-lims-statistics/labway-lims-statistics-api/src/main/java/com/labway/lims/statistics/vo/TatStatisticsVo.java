package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.IOException;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.Objects;

/**
 * TAT 统计
 */
@Getter
@Setter
public class TatStatisticsVo {
    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 就诊类型，申请单类型
     */
    private String applyType;

    /**
     * 专业组
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 急诊类型
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 条码号
     */
    private String barcode;


    /**
     * 采样时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date samplingDate;


    /**
     * 签收时间
     */
    private Date createDate;

    /**
     * 一次分拣时间
     */
    private Date onePickDate;

    /**
     * 二次分拣日期
     */
    private Date twoPickDate;

    /**
     * 一审时间
     */
    private Date oneCheckDate;


    /**
     * 二审时间
     */
    private Date twoCheckDate;

    /**
     * 物流用时 单位小时
     */
    @JsonSerialize(using = CustomDoubleSerialize.class)
    private Double logisticsTime;

    /**
     * 前处理用时(也就是分拣用时 单位小时
     */
    @JsonSerialize(using = CustomDoubleSerialize.class)
    private Double pickTime;

    /**
     * 专业组用时(也就是检验用时 单位小时
     */
    @JsonSerialize(using = CustomDoubleSerialize.class)
    private Double testTime;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 总用时 单位小时
     */
    @JsonSerialize(using = CustomDoubleSerialize.class)
    public Double getTotalTime() {
        return ObjectUtils.defaultIfNull(getLogisticsTime(), 0D) +
                ObjectUtils.defaultIfNull(getPickTime(), 0D) +
                ObjectUtils.defaultIfNull(getTestTime(), 0D);
    }

    public Date getOnePickDate() {
        if (Objects.isNull(onePickDate) || DefaultDateEnum.DEFAULT_DATE.getDate().equals(onePickDate)) {
            return null;
        }
        return onePickDate;
    }

    public Date getSamplingDate() {
        if (Objects.isNull(samplingDate) || DefaultDateEnum.DEFAULT_DATE.getDate().equals(samplingDate)) {
            return null;
        }
        return samplingDate;
    }

    public Date getOneCheckDate() {
        if (Objects.isNull(oneCheckDate) || DefaultDateEnum.DEFAULT_DATE.getDate().equals(oneCheckDate)) {
            return null;
        }
        return oneCheckDate;
    }

    public Date getTwoCheckDate() {
        if (Objects.isNull(twoCheckDate) || DefaultDateEnum.DEFAULT_DATE.getDate().equals(twoCheckDate)) {
            return null;
        }
        return twoCheckDate;
    }

    public Date getTwoPickDate() {
        if (Objects.isNull(twoPickDate) || DefaultDateEnum.DEFAULT_DATE.getDate().equals(twoPickDate)) {
            return null;
        }
        return twoPickDate;
    }

    public Date getCreateDate() {
        if (Objects.isNull(createDate) || DefaultDateEnum.DEFAULT_DATE.getDate().equals(createDate)) {
            return null;
        }
        return createDate;
    }

    private static class CustomDoubleSerialize extends JsonSerializer<Double> {

        private final DecimalFormat df = new DecimalFormat("0.##");

        @Override
        public void serialize(Double value, JsonGenerator gen, SerializerProvider serializers)
                throws IOException {
            if (value != null) {
                gen.writeString(df.format(value));
            }
        }
    }

}
