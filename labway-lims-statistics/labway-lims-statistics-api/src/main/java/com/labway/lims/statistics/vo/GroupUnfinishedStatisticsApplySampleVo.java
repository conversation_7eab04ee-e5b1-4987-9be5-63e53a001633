package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <pre>
 * UnfinishedApplySampleVo
 * 未完成工作量统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/7/5 11:04
 */
@Getter
@Setter
public class GroupUnfinishedStatisticsApplySampleVo extends ApplySampleVo {

    /**
     * 复核人id
     */
    private Long checkerId;

    /**
     * 复核人
     */
    private String checkerName;

    /**
     * 复核时间
     */
    private Date checkDate;

    /**
     * 当前条码环节
     */
    private String barcodeFlow;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 本专业组项目
     */
    private List<GroupUnfinishedStatisticsApplySampleTestItemVo> currentGroupTestItems;

    /**
     * 其他专业组项目
     */
    private List<GroupUnfinishedStatisticsApplySampleTestItemVo> otherGroupTestItems;

    public GroupUnfinishedStatisticsApplySampleVo() {
        this.currentGroupTestItems = new ArrayList<>();
        this.otherGroupTestItems = new ArrayList<>();
    }

}
