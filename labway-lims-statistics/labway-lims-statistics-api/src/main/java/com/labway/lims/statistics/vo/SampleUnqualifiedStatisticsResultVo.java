package com.labway.lims.statistics.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * SampleUnqualifiedStatisticsResultVo
 * 不合格标本分布
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/2/28 11:02
 */
@Getter
@Setter
public class SampleUnqualifiedStatisticsResultVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 动态列
     */
    private List<ColumnItem> columns;

    /**
     * 表格数据
     */
    private List<UnqualifiedSampleStatisticsItem> items;

    /**
     * 统计行
     */
    private UnqualifiedSampleStatisticsItem summary;


    @Getter
    public static class UnqualifiedSampleStatisticsItem extends JSONObject {
        // 送检机构ID
        private Long hspOrgId;
        // 送检机构编码
        private String hspOrgCode;
        // 送检机构名称
        private String hspOrgName;
        // 总条码数
        private Long totalBarcodeCount;
        // 总人数
        private Long totalPeopleCount;
        // 总样本数
        private Long totalSampleCount;

        public void setHspOrgId(Long hspOrgId) {
            this.hspOrgId = hspOrgId;
            this.put("hspOrgId", hspOrgId);
        }

        public void setHspOrgCode(String hspOrgCode) {
            this.hspOrgCode = hspOrgCode;
            this.put("hspOrgCode", hspOrgCode);
        }

        public void setHspOrgName(String hspOrgName) {
            this.hspOrgName = hspOrgName;
            this.put("hspOrgName", hspOrgName);
        }

        public void setTotalBarcodeCount(Long totalBarcodeCount) {
            this.totalBarcodeCount = totalBarcodeCount;
            this.put("totalBarcodeCount", totalBarcodeCount);
        }

        public void setTotalPeopleCount(Long totalPeopleCount) {
            this.totalPeopleCount = totalPeopleCount;
            this.put("totalPeopleCount", totalPeopleCount);
        }

        public void setTotalSampleCount(Long totalSampleCount) {
            this.totalSampleCount = totalSampleCount;
            this.put("totalSampleCount", totalSampleCount);
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class ColumnItem implements Serializable {
        private String code;
        private String title;
    }
}
