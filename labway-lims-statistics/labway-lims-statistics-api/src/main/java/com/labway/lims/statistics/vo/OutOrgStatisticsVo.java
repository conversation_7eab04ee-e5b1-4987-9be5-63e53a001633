package com.labway.lims.statistics.vo;

import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.statistics.dto.OutOrgStatisticsItemDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class OutOrgStatisticsVo extends FinanceStatisticsBaseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 列表
     */
    private Map<Long, List<OutOrgStatisticsItemVo>> items;
    /**
     *  列表合计信息
     */
    private List<OutOrgStatisticsItemDTO> itemsList;

    private Map<Long, HspOrganizationDto> hspOrganizationMap;
}
