package com.labway.lims.statistics.vo;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * SampleStatisticsVo
 * 检验项目信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 14:45
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SampleTestItemStatisticsVo {

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 外部项目编码
     */
    private Long outTestItemId;

    /**
     * 外部项目名称
     */
    private String outTestItemCode;

    /**
     * 外部项目名称
     */
    private String outTestItemName;

    /**
     * 专业组id
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 收费数量
     */
    private Integer count;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 是否免单
     *
     * @see YesOrNoEnum
     */
    private Integer isFree;

    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 终止检验状态：0正常，1终止收费，2终止不收费
     *
     * @see StopTestStatus
     */
    private Integer stopStatus;

    /**
     * 终止检验原因code
     */
    private String stopReasonCode;

    /**
     * 终止检验原因value
     */
    private String stopReasonName;

    /**
     * 申请单样本项目是否禁用：1：禁用，0：正常
     */
    private Integer isDisabled;


}
