package com.labway.lims.statistics.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 送检机构申请单统计
 */
@Data
public class HspOrgStatisticsDeleteImgVo implements Serializable {
    /**
     * 申请单样本id
     */
    @NotNull(message = "申请单样本id不能为空")
    private Long applySampleId;

    /**
     * 申请单样本图片id
     */
    @NotNull(message = "申请单样本图片id不能为空")
    private Long applySampleImgId;

}
