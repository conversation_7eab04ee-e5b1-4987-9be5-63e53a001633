package com.labway.lims.statistics.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 机构汇总数据--响应vo
 * 
 * <AUTHOR>
 * @since 2023/5/15 10:12
 */
@Setter
@Getter
public class HspOrgSummaryStatisticsVo implements Serializable {


    /**
     * 总项目数量
     */
    private Integer totalItemNum;

    /**
     * 总折前总额
     */
    private BigDecimal totalOriginalAmount;

    /**
     * 总折后总额--实际金额
     */
    private BigDecimal totalActualAmount;


    /**
     * 项目汇总数据
     */
    private List<HspOrgSummaryStatisticsItem> itemList = new ArrayList<>();


    /**
     * 机构汇总数据项目统计
     */
    @Data
    public static class HspOrgSummaryStatisticsItem implements Serializable {


        /**
         * 送检机构名称
         */
        @ExcelProperty(value = "送检机构名称",  index = 0)
        private String hspOrgName;


        /**
         * 项目数量
         */
        @ExcelProperty(value = "项目数量", index = 1)
        private Integer itemCount;


        /**
         * 折前总额
         */
        @ExcelProperty(value = "折前总额", index =2)
        private BigDecimal originalAmount;


        /**
         * 折后总额
         */
        @ExcelProperty(value = "折后总额", index = 3)
        private BigDecimal actualAmount;


    }




    public Integer getTotalItemNum() {

        return itemList.stream().mapToInt(HspOrgSummaryStatisticsItem::getItemCount)
                .sum();

    }

    public BigDecimal getTotalOriginalAmount() {

        return itemList.stream().map(HspOrgSummaryStatisticsItem::getOriginalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

    }

    public BigDecimal getTotalActualAmount() {

        return itemList.stream().map(HspOrgSummaryStatisticsItem::getActualAmount)
                .reduce( BigDecimal.ZERO, BigDecimal::add);
    }


}
