package com.labway.lims.statistics.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 送检机构申请单统计
 */
@Getter
@Setter
public class HspOrgStatisticsRequestVo {
    /**
     * 签收日期开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginSignDate;

    /**
     * 签收日期结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endSignDate;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 名称
     */
    private String patientName;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本来源
     * @see com.labway.lims.api.enums.apply.SampleSourceEnum
     */
    private String sampleSource;

}
