package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.statistics.enums.DisplayOrgType;
import com.labway.lims.statistics.enums.ExportType;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Set;

@Getter
@Setter
public class FinanceStatisticsQueryVo extends BaseDataStatisticsQueryVo {

    /**
     * 1 送检时间 2 审核时间
     */
    private Integer dateType;

    /**
     * 开票类型 1 开票名称 2 单位名称
     */
    private Integer invoiceType;

    /**
     * 外送机构id
     */
    private Set<Long> exportOrgIds;

    /**
     * 送检机构
     */
    private Set<Long> hspOrgIds;

    /**
     * 检验项目ID
     */
    private Set<Long> testItemIds;

    /**
     * 专业组
     */
    private Set<Long> groupIds;

    /**
     * 展示机构方式：单个机构:1 机构合并:2
     * @see DisplayOrgType
     */
    private Integer displayOrgType;

    /**
     * 导出类型：导出当前:1 导出所有:2
     * @see ExportType
     */
    private Integer exportType;

    /**
     * 审核开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginAuditDate;
    /**
     * 审核开始结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endAuditDate;

}
