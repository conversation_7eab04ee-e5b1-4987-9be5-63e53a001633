package com.labway.lims.statistics.controller;

import cn.hutool.core.date.DateUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.statistics.dto.SampleArchiveDto;
import com.labway.lims.statistics.dto.SampleArchiveQueryDto;
import com.labway.lims.statistics.service.SampleArchiveService;
import com.labway.lims.statistics.vo.SampleArchiveQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * SampleArchiveController
 * 样本归档信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/31 13:13
 */
@Slf4j
@RestController
@RequestMapping("/sample-archive")
public class SampleArchiveController extends BaseController {

    @Resource
    private SampleArchiveService sampleArchiveService;

    /**
     * 样本归档信息查询
     * @param vo
     * @return
     */
    @PostMapping("statistics")
    public Object statistics(@RequestBody SampleArchiveQueryVo vo) {
        SampleArchiveQueryDto.SampleArchiveQueryDtoBuilder builder = SampleArchiveQueryDto.builder();
        builder.pageNo(vo.getPageNo())
                .pageSize(vo.getPageSize())
                .searchAfter(vo.getSearchAfter())
                .startCreateDate(vo.getStartCreateDate())
                .endCreateDate(vo.getEndCreateDate())
                .creatorId(vo.getCreatorId())
                .barcode(vo.getBarcode())
                .testItemCode(vo.getTestItemCode())
                .patientName(vo.getPatientName())
                .patientVisitCard(vo.getPatientVisitCard());
        // 归档时间范围
        Date startCreateDate = vo.getStartCreateDate();
        Date endCreateDate = vo.getEndCreateDate();
        if (Objects.isNull(startCreateDate) || Objects.isNull(endCreateDate)) {
            builder.startCreateDate(DateUtil.beginOfDay(Calendar.getInstance()).getTime());
            builder.endCreateDate(DateUtil.endOfDay(Calendar.getInstance()).getTime());
        }
        // 专业组
        Long groupId = LoginUserHandler.get().getGroupId();
        builder.groupId(groupId);
        return sampleArchiveService.selectSampleArchivePage(builder.build());
    }

    /**
     * 根据申请单样本ID查询报告项目结果
     * @param applySampleId
     * @return
     */
    @GetMapping("query-report-item")
    public Object selectReportItem(@RequestParam Long applySampleId) {
        return sampleArchiveService.selectReportItem(applySampleId);
    }

    /**
     * 根据条码号和专业组查询归档样本信息
     * @param barcode
     * @return
     */
    @GetMapping("/select")
    public Object selectByBarcode(String barcode) {
        LoginUserHandler.User user = LoginUserHandler.get();
        List<SampleArchiveDto> sampleDto = sampleArchiveService.selectSampleArchive(barcode, user.getGroupId());
        if (Objects.isNull(sampleDto)) {
            throw new LimsException("该条码未归档");
        }
        return sampleDto;
    }

}
