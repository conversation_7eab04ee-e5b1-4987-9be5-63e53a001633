package com.labway.lims.statistics.service.chain.byPlateform;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import com.labway.lims.apply.api.dto.ByPlatformStatisticsItemDto;
import com.labway.lims.apply.api.dto.ByPlatformStatisticsKeyDto;
import com.labway.lims.apply.api.dto.ByPlatformStatisticsResponseDto;
import com.labway.lims.apply.api.dto.SampleTestItemDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.statistics.vo.FinanceStatisticsQueryVo;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ArithmeticalDiscountCommand implements Command {

    @Override
    public boolean execute(Context context) {
        final PlatformStatisticsContext from = PlatformStatisticsContext.from(context);

        // 开票名称
        final CustomerNameTypeEnum customerNameType = from.getCustomerNameTypeEnum();

        // 样本检验项目
        Map<Long, List<SampleTestItemDto>> groupByHspOrgId = from.getSampleTestItemList().stream()
                .collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId));

        // 时间
        PlatformStatisticsContext.DateRange dateRange = getDateRange(from.getFinanceStatisticsQuery());

        final ByPlatformStatisticsResponseDto result = from.getResult();
        final List<ByPlatformStatisticsResponseDto> orgList = result.getOrgList();

        // 数量合计、 合计金额合计、结算金额合计
        Integer countTotal = NumberUtils.INTEGER_ZERO;
        BigDecimal payAmountBeforeTotal = BigDecimal.ZERO;
        BigDecimal payAmountAfterTotal = BigDecimal.ZERO;


        // 填充计算金额
        for (HspOrganizationDto hspOrganizationDto : from.getHspOrgList()) {
            Long hspOrgId = hspOrganizationDto.getHspOrgId();
            List<SampleTestItemDto> sampleTestItemDtos = groupByHspOrgId.get(hspOrgId);
            // 开票名称
            String customerName = this.getCustomer(customerNameType, hspOrganizationDto);

            if (CollectionUtils.isEmpty(sampleTestItemDtos)) {
                orgList.add(this.acquireDefaultHspOrg(hspOrganizationDto, customerName, dateRange));
                continue;
            }

            // 财务专业组 就诊类型 检验项目 标准收费 折扣率一样的 分组
            Map<ByPlatformStatisticsKeyDto, List<SampleTestItemDto>> groupingByKeyDto = sampleTestItemDtos.stream()
                    .collect(Collectors.groupingBy(obj -> new ByPlatformStatisticsKeyDto(obj.getFinanceGroupCode(), obj.getApplyTypeCode(), obj.getTestItemId(), obj.getPrice(), obj.getDiscount())));
            List<ByPlatformStatisticsItemDto> itemList = Lists.newArrayListWithCapacity(groupingByKeyDto.size());


            final AmountSumDto amountSumDto = this.getAmountSumDto(hspOrganizationDto, groupingByKeyDto, itemList);

            // 排序
            List<ByPlatformStatisticsItemDto> sorted =
                    itemList.stream().sorted(Comparator.comparing(ByPlatformStatisticsItemDto::getApplyTypeCode)
                            .thenComparing(ByPlatformStatisticsItemDto::getTestItemCode)).collect(Collectors.toList());

            // 相应数据
            ByPlatformStatisticsResponseDto orgItem = new ByPlatformStatisticsResponseDto();

            countTotal = countTotal + amountSumDto.countSumAll;
            payAmountBeforeTotal = payAmountBeforeTotal.add(amountSumDto.payAmountBeforeSum);
            payAmountAfterTotal = payAmountAfterTotal.add(amountSumDto.payAmountAfterSum);

            orgItem.setHspOrgId(hspOrganizationDto.getHspOrgId());
            orgItem.setCustomerName(customerName);
            orgItem.setStartDate(dateRange.getStartDate());
            orgItem.setEndDate(dateRange.getEndDate());
            orgItem.setCountSum(amountSumDto.countSumAll);
            orgItem.setPayAmountBeforeSum(amountSumDto.payAmountBeforeSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            orgItem.setPayAmountAfterSum(amountSumDto.payAmountAfterSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            orgItem.setOutOrgStatisticsItems(sorted);

            orgList.add(orgItem);
        }

        if (CollectionUtils.isNotEmpty(orgList)) {
            result.setHspOrgId(orgList.get(0).getHspOrgId());
            result.setCustomerName(orgList.get(0).getCustomerName());
            result.setStartDate(orgList.get(0).getStartDate());
            result.setEndDate(orgList.get(0).getEndDate());
            result.setCountSum(countTotal);
            result.setPayAmountBeforeSum(payAmountBeforeTotal);
            result.setPayAmountAfterSum(payAmountAfterTotal);
        }
        return CONTINUE_PROCESSING;
    }

    public String getCustomer(CustomerNameTypeEnum customerNameType, HspOrganizationDto hspOrganizationDto) {
        String customerName;
        if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME)
                && StringUtils.isNotBlank(hspOrganizationDto.getInvoice())) {
            // 展示开票名称
            customerName = hspOrganizationDto.getInvoice();
        } else {
            customerName = hspOrganizationDto.getHspOrgName();
        }
        return customerName;
    }

    private AmountSumDto getAmountSumDto(HspOrganizationDto hspOrganizationDto,
                                         Map<ByPlatformStatisticsKeyDto, List<SampleTestItemDto>> groupingByKeyDto,
                                         List<ByPlatformStatisticsItemDto> itemList) {
        // 数量合计、 合计金额合计、结算金额合计
        Integer countSumAll = NumberUtils.INTEGER_ZERO;
        BigDecimal payAmountBeforeSum = BigDecimal.ZERO;
        BigDecimal payAmountAfterSum = BigDecimal.ZERO;

        for (Map.Entry<ByPlatformStatisticsKeyDto, List<SampleTestItemDto>> groupingByKey : groupingByKeyDto.entrySet()) {
            ByPlatformStatisticsKeyDto keyDto = groupingByKey.getKey();
            List<SampleTestItemDto> summaryItemListOuter = groupingByKey.getValue();

            Map<String, List<SampleTestItemDto>> groupingByDate = summaryItemListOuter.stream().collect(Collectors.groupingBy(item -> DateUtil.format(item.getCreateDate(), DatePattern.NORM_MONTH_PATTERN)));

            for (Map.Entry<String, List<SampleTestItemDto>> entry : groupingByDate.entrySet()) {
                String sendDate = entry.getKey();
                List<SampleTestItemDto> summaryItemList = entry.getValue();

                int countSum =
                        summaryItemList.stream().map(obj -> ObjectUtils.defaultIfNull(obj.getCount(), NumberUtils.INTEGER_ONE))
                                .mapToInt(Integer::intValue).sum();
                BigDecimal feePriceSum = keyDto.getPrice().multiply(BigDecimal.valueOf(countSum));
                // 未免单 金额合计 用于结算金额
                BigDecimal payAmount =
                        summaryItemList.stream().filter(obj -> !Objects.equals(obj.getIsFree(), YesOrNoEnum.YES.getCode())).map(item -> {
                            BigDecimal price = item.getPrice();
                            if (item.isSpecialOfferFlag()) {
                                // 该样本检验项目 参与了特价项目 结算金额为折后价格
                                price = ObjectUtils.defaultIfNull(item.getDiscountPrice(), BigDecimal.ZERO);
                                return price
                                        .multiply(
                                                BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                        .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                            }
                            return price
                                    .multiply(
                                            BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                    .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).multiply(keyDto.getDiscount())
                                    .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                        }).reduce(BigDecimal.ZERO, BigDecimal::add);

                countSumAll = countSumAll + countSum;
                payAmountBeforeSum = payAmountBeforeSum.add(feePriceSum);
                payAmountAfterSum = payAmountAfterSum.add(payAmount);

                ByPlatformStatisticsItemDto temp = new ByPlatformStatisticsItemDto();
                temp.setSendDate(sendDate);
                temp.setHspOrgId(hspOrganizationDto.getHspOrgId());
                temp.setHspOrgName(hspOrganizationDto.getHspOrgName());
                temp.setApplyTypeName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getApplyTypeName());
                temp.setApplyTypeCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getApplyTypeCode());
                temp.setFinanceGroupCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getFinanceGroupCode());
                temp.setFinanceGroupName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getFinanceGroupName());
                temp.setTestItemCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemCode());
                temp.setTestItemName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemName());
                temp.setCount(countSum);
                temp.setFeePrice(keyDto.getPrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                temp.setDiscount(new DecimalFormat("0.00%").format(keyDto.getDiscount()));
                temp.setPayAmountBefore(feePriceSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                temp.setPayAmountAfter(payAmount.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                itemList.add(temp);
            }
        }
        return new AmountSumDto(countSumAll, payAmountBeforeSum, payAmountAfterSum);
    }

    private static class AmountSumDto {
        public final Integer countSumAll;
        public final BigDecimal payAmountBeforeSum;
        public final BigDecimal payAmountAfterSum;

        public AmountSumDto(Integer countSumAll, BigDecimal payAmountBeforeSum, BigDecimal payAmountAfterSum) {
            this.countSumAll = countSumAll;
            this.payAmountBeforeSum = payAmountBeforeSum;
            this.payAmountAfterSum = payAmountAfterSum;
        }
    }

    public ByPlatformStatisticsResponseDto acquireDefaultHspOrg(HspOrganizationDto hspOrganizationDto, String customerName, PlatformStatisticsContext.DateRange dateRange) {
        ByPlatformStatisticsResponseDto orgItem = new ByPlatformStatisticsResponseDto();
        orgItem.setHspOrgId(hspOrganizationDto.getHspOrgId());
        orgItem.setCustomerName(customerName);
        orgItem.setStartDate(dateRange.getStartDate());
        orgItem.setEndDate(dateRange.getEndDate());
        orgItem.setCountSum(NumberUtils.INTEGER_ZERO);
        orgItem.setPayAmountBeforeSum(BigDecimal.ZERO.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
        orgItem.setPayAmountAfterSum(BigDecimal.ZERO.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
        orgItem.setOutOrgStatisticsItems(Collections.emptyList());
        orgItem.setItemList(Collections.emptyList());

        return orgItem;
    }

    public PlatformStatisticsContext.DateRange getDateRange(FinanceStatisticsQueryVo query) {
        PlatformStatisticsContext.DateRange dateRange = new PlatformStatisticsContext.DateRange();
        // 时间开始结束
        String startDate = StringUtils.EMPTY;
        String endDate = StringUtils.EMPTY;
        if (Objects.nonNull(query.getStartDate())
                && Objects.nonNull(query.getEndDate())) {
            startDate = DateUtil.formatDate(query.getStartDate());
            endDate = DateUtil.formatDate(query.getEndDate());
        }

        dateRange.setStartDate(startDate);
        dateRange.setEndDate(endDate);
        return dateRange;
    }
}
