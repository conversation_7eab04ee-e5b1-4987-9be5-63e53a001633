package com.labway.lims.statistics.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.mdm.api.reagent.service.ItemTestService;
import com.labway.lims.api.LabwayDateUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySampleItemSourceEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.*;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.config.MicrobiologySummarySpecimenAttachConfig;
import com.labway.lims.statistics.vo.ReportItemStatisticsTotalVo;
import com.labway.lims.statistics.vo.ReportItemStatisticsVo;
import com.labway.lims.statistics.vo.SummarySpecimenQueryVo;
import com.labway.lims.statistics.vo.SummarySpecimenVo;
import com.labway.lims.statistics.vo.TestItemStatisticsQueryVo;
import com.labway.lims.statistics.vo.TestItemStatisticsTotalVo;
import com.labway.lims.statistics.vo.TestItemStatisticsVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据统计
 */
@Slf4j
@RestController
@RequestMapping("/data-statistics")
public class DataStatisticsController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private SampleRetestItemService sampleRetestItemService;
    @Resource
    private MicrobiologySummarySpecimenAttachConfig attachConfig;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private EsConfig esConfig;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private InstrumentGroupService  instrumentGroupService;
    @DubboReference
    private InstrumentService instrumentService;


    // 通用打印模板
    private final String summarySpecimenPrint = "common";

    // region ---------------------------------每日接种标本统计-----------------------------

    /**
     * 当日接种标本汇总
     */
    @PostMapping("/summary-specimen")
    public Object summarySpecimen(@RequestBody SummarySpecimenQueryVo queryVo) {

        queryVo.defaultDate();

        if (Objects.isNull(queryVo.getStartDate()) || Objects.isNull(queryVo.getEndDate())) {
            throw new IllegalArgumentException("参数错误");
        }

        // 查询条件
        final SampleEsQuery query = SampleEsQuery.builder().pageNo(1).pageSize(Integer.MAX_VALUE)
                // .itemTypes(new HashSet<>(Set.of(ItemTypeEnum.MICROBIOLOGY.name(), ItemTypeEnum.BLOOD_CULTURE.name())))
                .startTwoPickDate(queryVo.getStartDate())
                .endTwoPickDate(queryVo.getEndDate()).build();

        // 过滤已终止的样本
        query.setExcludeSampleStatus(List.of(SampleStatusEnum.STOP_TEST.getCode()));

        query.setGroupIds(Set.of(LoginUserHandler.get().getGroupId()));

        if (CollectionUtils.isNotEmpty(queryVo.getHspOrgIds())) {
            query.setHspOrgIds(queryVo.getHspOrgIds());
        }

        if (CollectionUtils.isNotEmpty(queryVo.getTestItemIds())) {
            query.setTestItemIds(queryVo.getTestItemIds());
        }

        final List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(query);
        if (CollectionUtils.isEmpty(samples)) {
            return List.of();
        }

        // 根据二次分拣时间排序
        samples.sort(Comparator.comparing(BaseSampleEsModelDto::getTwoPickDate));

        // 结果
        final LinkedList<SummarySpecimenVo> vos = new LinkedList<>();

        for (final BaseSampleEsModelDto data : samples) {
            final SummarySpecimenVo sample = JSON.parseObject(JSON.toJSONString(data), SummarySpecimenVo.class);
            sample.setReceiveDate(data.getCreateDate());
            sample.setOperateDate(data.getTestDate());
            sample.setSampleType(data.getSampleTypeName());
            sample.setClinicalDiagnosis(data.getDiagnosis());
            // 如果是数据库1900，1970这种默认值，改成空回显给前端
            if (LabwayDateUtil.isDefaultDbDate(sample.getReceiveDate())) {
                sample.setReceiveDate(null);
            }

            if (CollectionUtils.isNotEmpty(data.getTestItems())) {
                // 检验项目 排除终止的
                List<BaseSampleEsModelDto.TestItem> testItems = data.getTestItems().stream()
                        .filter(obj -> !(Objects.equals(obj.getStopStatus(), StopTestStatus.STOP_TEST_CHARGE.getCode())
                                || Objects.equals(obj.getStopStatus(), StopTestStatus.STOP_TEST_FREE.getCode())))
                        .filter(obj -> Objects.equals(obj.getItemSource(), ApplySampleItemSourceEnum.DEFAULT_SOURCE.getCode()))
                        .collect(Collectors.toList());
                // 禁用的项目不显示
                testItems.removeIf(e -> Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()));
                sample.setTestItemName(testItems.stream().map(BaseSampleEsModelDto.TestItem::getTestItemName)
                        .collect(Collectors.joining(",")));
                sample.setTestItemId(testItems.stream().map(obj -> String.valueOf(obj.getTestItemId()))
                        .collect(Collectors.joining(",")));
            }
            vos.add(sample);
        }

        return Map.of("data", vos, "total", vos.size());
    }

    /**
     * 当日 接种标本 打印
     */
    @PostMapping("/summary-specimen-print")
    public Object summarySpecimenPrint(@RequestBody Set<Long> applySampleIds) {

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }
        if (Objects.isNull(attachConfig) || Objects.isNull(attachConfig.getAttachMap())
                || StringUtils.isBlank(attachConfig.getAttachMap().get(summarySpecimenPrint))) {
            throw new IllegalStateException("请配置打印模板");
        }

        // 查询条件
        final SampleEsQuery query = SampleEsQuery.builder().pageNo(1).pageSize(Integer.MAX_VALUE)
                // .itemTypes(new HashSet<>(Set.of(ItemTypeEnum.MICROBIOLOGY.name(), ItemTypeEnum.BLOOD_CULTURE.name())))
                .applySampleIds(applySampleIds).build();
        // 过滤已终止的样本
        query.setExcludeSampleStatus(List.of(SampleStatusEnum.STOP_TEST.getCode()));

        query.setGroupIds(Set.of(LoginUserHandler.get().getGroupId()));

        List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(query).stream()
                .filter(obj -> CollectionUtils.isNotEmpty(obj.getTestItems()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }
        Map<String, List<BaseSampleEsModelDto>> groupingByTestItemCode = samples.stream()
                .collect(Collectors.groupingBy(obj -> obj.getTestItems().get(NumberUtils.INTEGER_ZERO).getTestItemCode()));

        String defaultPdfCode = attachConfig.getAttachMap().get(summarySpecimenPrint);

        List<String> urls = Lists.newArrayList();
        for (Map.Entry<String, List<BaseSampleEsModelDto>> entry : groupingByTestItemCode.entrySet()) {
            String testItemCode = entry.getKey();
            List<BaseSampleEsModelDto> value = entry.getValue();
            // 根据二次分拣时间排序
            value.sort(Comparator.comparing(BaseSampleEsModelDto::getTwoPickDate));

            BaseSampleEsModelDto.TestItem testItem =
                    value.get(NumberUtils.INTEGER_ZERO).getTestItems().get(NumberUtils.INTEGER_ZERO);

            String pdfCode = attachConfig.getAttachMap().getOrDefault(testItemCode, defaultPdfCode);

            PdfReportParamDto param = new PdfReportParamDto();

            // 检验项目信息
            param.put("testItem", Dict.of("testItemCode", testItem.getTestItemCode(), "testItemName",
                    testItem.getTestItemName(), "_testItem", Dict.parse(testItem)));
            List<Dict> dataList = Lists.newArrayList();
            value.forEach(item -> {
                Dict dict = Dict.of("_data", Dict.parse(item));
                dict.put("sampleNo", item.getSampleNo());
                dict.put("barcode", item.getBarcode());
                dict.put("hspOrgName", item.getHspOrgName());
                dict.put("patientName", item.getPatientName());
                dict.put("patientSex", item.getPatientSex());
                dict.put("patientAge", PatientAges.toText(item));
                dict.put("clinicalDiagnosis", item.getDiagnosis());
                dict.put("sampleType", item.getSampleTypeName());
                dict.put("receiveDate", DateUtil.formatDateTime(item.getCreateDate()));
                dict.put("operateDate", DateUtil.formatDateTime(item.getTestDate()));
                dataList.add(dict);
            });
            // 列表 数据
            param.put("dataList", dataList);

            final String pdfUrl = pdfReportService.build2Url(pdfCode, param, 3);
            urls.add(pdfUrl);

        }

        return urls;
    }

    // endregion ---------------------------------测试项目数量-----------------------------

    /**
     * 测试检验项目数
     */
    @PostMapping("/test-item-statistics")
    public Object testItemStatistics(@RequestBody TestItemStatisticsQueryVo queryVo) {

        if (Objects.isNull(queryVo.getStartDate()) &&
                Objects.isNull(queryVo.getEndDate()) &&
                Objects.isNull(queryVo.getStartFinalCheckDate()) &&
                Objects.isNull(queryVo.getEndFinalCheckDate())) {
            throw new IllegalArgumentException("请选择日期");
        }

        // 结果
        final SampleEsQuery query =
                SampleEsQuery.builder()
                        .pageNo(NumberUtils.INTEGER_ONE)
                        .pageSize(esConfig.getPageSize())
                        // .startCreateDate(queryVo.getStartDate())
                        // .endCreateDate(queryVo.getEndDate())
                        // .isAudit(YesOrNoEnum.YES.getCode())
                        .build();

        // 根据签收时间查询
        boolean statisticsBySignDate = true;
        // 审核时间
        if (ObjectUtils.allNotNull(queryVo.getStartFinalCheckDate(), queryVo.getEndFinalCheckDate())) {
            query.setStartFinalCheckDate(queryVo.getStartFinalCheckDate());
            query.setEndFinalCheckDate(queryVo.getEndFinalCheckDate());
            statisticsBySignDate = false;
        }
        // 创建时间（签收）
        if (ObjectUtils.allNotNull(queryVo.getStartDate(), queryVo.getEndDate())) {
            query.setStartCreateDate(queryVo.getStartDate());
            query.setEndCreateDate(queryVo.getEndDate());
        }

        if (CollectionUtils.isNotEmpty(queryVo.getGroupIds())) {
            query.setGroupIds(queryVo.getGroupIds());
        }

        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Set.of(queryVo.getHspOrgId()));
        }

        final LinkedList<String> columns = new LinkedList<>();
        Date date = statisticsBySignDate ? queryVo.getStartDate() : queryVo.getStartFinalCheckDate();
        while (date.compareTo(statisticsBySignDate ? queryVo.getEndDate() : queryVo.getEndFinalCheckDate()) < 0) {
            columns.add(DateFormatUtils.format(date, "MM月dd日"));
            date = DateUtils.addDays(date, 1);
        }

        final Map<TestItemStatisticsKey, TestItemStatisticsVo> map = new HashMap<>();

        final List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(query);
        for (BaseSampleEsModelDto sample : baseSampleEsModelDtos) {
            if (CollectionUtils.isEmpty(sample.getTestItems())) {
                continue;
            }
            for (BaseSampleEsModelDto.TestItem testItem : sample.getTestItems()) {
                // 终止不收费、终止收费的不展示
                if (Objects.equals(testItem.getStopStatus(), StopTestStatus.STOP_TEST_CHARGE.getCode())
                        || Objects.equals(testItem.getItemSource(), ApplySampleItemSourceEnum.MICROBIOLOGY_SAMPLE_FEE_ITEM.getCode())
                        || Objects.equals(testItem.getIsDisabled(), YesOrNoEnum.YES.getCode()) // 禁用的项目不显示
                        || Objects.equals(testItem.getStopStatus(), StopTestStatus.STOP_TEST_FREE.getCode())) {
                    continue;
                }
                final TestItemStatisticsKey key = new TestItemStatisticsKey();
                key.groupId = testItem.getGroupId();
                key.hspOrgId = sample.getHspOrgId();
                key.testItemId = testItem.getTestItemId();
                key.feePrice = testItem.getPrice();
                final TestItemStatisticsVo v = map.computeIfAbsent(key, k -> {
                    final TestItemStatisticsVo tsv = new TestItemStatisticsVo();
                    tsv.setGroupName(testItem.getGroupName());
                    tsv.setGroupId(testItem.getGroupId());
                    tsv.setHspOrgName(sample.getHspOrgName());
                    tsv.setTestItemCode(testItem.getTestItemCode());
                    tsv.setTestItemName(testItem.getTestItemName());
                    tsv.setTotalNum(NumberUtils.INTEGER_ZERO);
                    tsv.setTotalAmount(BigDecimal.ZERO);
                    // 项目单价
                    tsv.setItemAmount(testItem.getActualFeePrice());
                    tsv.setColumns(new LinkedHashMap<>());

                    for (String column : columns) {
                        tsv.getColumns().put(column, NumberUtils.INTEGER_ZERO);
                    }

                    return tsv;
                });

                final Integer count = ObjectUtils.defaultIfNull(testItem.getCount(), NumberUtils.INTEGER_ONE);
                // 合计数量
                v.setTotalNum(count + v.getTotalNum());
                // 合计金额
                v.setTotalAmount(ObjectUtils.defaultIfNull(testItem.getActualFeePrice(), BigDecimal.ZERO)
                        .multiply(new BigDecimal(count)).add(v.getTotalAmount()));

                final Date columnDate = statisticsBySignDate ? sample.getCreateDate() : sample.getFinalCheckDate();
                final String c = DateFormatUtils.format(columnDate, "MM月dd日");
                v.getColumns().put(c, v.getColumns().getOrDefault(c, NumberUtils.INTEGER_ZERO) + count);

            }
        }

        // 合计数量
        int totalNumSum = 0;
        // 合计金额
        BigDecimal totalAmountSum = new BigDecimal("0");
        for (TestItemStatisticsVo e : map.values()) {
            totalNumSum += e.getTotalNum();
            totalAmountSum = totalAmountSum.add(e.getTotalAmount());
        }

        final TestItemStatisticsTotalVo vo = new TestItemStatisticsTotalVo();
        vo.setTotal(map.values().size());
        vo.setTotalNumSum(totalNumSum);
        vo.setTotalAmountSum(totalAmountSum);
        final LinkedHashMap<String, BigDecimal> cloumnMap = new LinkedHashMap<>();

        for (String column : columns) {
            BigDecimal totalV = new BigDecimal(0);
            for (TestItemStatisticsVo value : map.values()) {
                final BigDecimal amount = value.getItemAmount();
                final Integer v = value.getColumns().get(column);
                totalV = totalV.add(amount.multiply(new BigDecimal(v)));
            }
            cloumnMap.put(column, totalV);
        }
        vo.setExtraColumnSum(cloumnMap);

        Collection<TestItemStatisticsVo> values = map.values();

        // data数据合并 相同项目合并成一条，数量进行累加
        if (Objects.isNull(queryVo.getHspOrgId()) && CollectionUtils.isNotEmpty(values)) {
            values = map.values().stream().collect(Collectors.groupingBy(TestItemStatisticsVo::getTestItemCode)).values().stream().map(this::margeTestItems).collect(Collectors.toList());
        }

        return Map.of("columns", columns, "data", values.stream()
                .sorted(Comparator.comparing(TestItemStatisticsVo::getTestItemName)
                        .thenComparing(TestItemStatisticsVo::getHspOrgName).thenComparing(TestItemStatisticsVo::getTotalAmount))
                .collect(Collectors.toList()), "total", vo);
    }

    /**
     * 测试报告项目数
     */
    @PostMapping("/report-item-statistics")
    public Object reportItemStatistics(@RequestBody TestItemStatisticsQueryVo queryVo) {

        if (Objects.isNull(queryVo.getStartDate()) &&
                Objects.isNull(queryVo.getEndDate()) &&
                Objects.isNull(queryVo.getStartFinalCheckDate()) &&
                Objects.isNull(queryVo.getEndFinalCheckDate())) {
            throw new IllegalArgumentException("请选择日期");
        }

        // 结果
        final SampleEsQuery query =
                SampleEsQuery.builder()
                        .pageNo(NumberUtils.INTEGER_ONE)
                        .pageSize(esConfig.getPageSize())
                        // .startCreateDate(queryVo.getStartDate())
                        // .endCreateDate(queryVo.getEndDate())
                        // .isAudit(YesOrNoEnum.YES.getCode())
                        .build();

        // 根据签收时间查询
        boolean statisticsBySignDate = true;
        // 审核时间
        if (ObjectUtils.allNotNull(queryVo.getStartFinalCheckDate(), queryVo.getEndFinalCheckDate())) {
            query.setStartFinalCheckDate(queryVo.getStartFinalCheckDate());
            query.setEndFinalCheckDate(queryVo.getEndFinalCheckDate());
            statisticsBySignDate = false;
        }
        // 创建时间（签收）
        if (ObjectUtils.allNotNull(queryVo.getStartDate(), queryVo.getEndDate())) {
            query.setStartCreateDate(queryVo.getStartDate());
            query.setEndCreateDate(queryVo.getEndDate());
        }

        if (CollectionUtils.isNotEmpty(queryVo.getGroupIds())) {
            query.setGroupIds(queryVo.getGroupIds());
        }

        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Set.of(queryVo.getHspOrgId()));
        }

        if (CollectionUtils.isNotEmpty(queryVo.getInstrumentIds())) {
            query.setReportItemInstrumentIds(queryVo.getInstrumentIds());
        }


        /**
         * 这里根据日期进行分组，以天为单位生成统计列
         */
        final LinkedList<String> columns = new LinkedList<>();
        Date date = statisticsBySignDate ? queryVo.getStartDate() : queryVo.getStartFinalCheckDate();
        while (date.compareTo(statisticsBySignDate ? queryVo.getEndDate() : queryVo.getEndFinalCheckDate()) < 0) {
            columns.add(DateFormatUtils.format(date, "MM月dd日"));
            date = DateUtils.addDays(date, 1);
        }


        final Map<ReportItemStatisticsKey, ReportItemStatisticsVo> map = new HashMap<>();
        // 要转换的报告通用类，为了方便判断
        final List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(query);
        final Set<Long> sampleIdSet = baseSampleEsModelDtos.stream().map(BaseSampleEsModelDto::getSampleId).collect(Collectors.toSet());
        final Map<String, Long> reportItemCountMap = sampleRetestItemService.selectReportCountBySampleIds(sampleIdSet);

        // 循环样本处理
        for (BaseSampleEsModelDto sample : baseSampleEsModelDtos) {
            if (!(sample instanceof RoutineInspectionDto) && !(sample instanceof OutsourcingInspectionDto)) {
                continue;
            }

            RoutineInspectionDto convertedSample = JSON.parseObject(JSON.toJSONString(sample), RoutineInspectionDto.class);

            if (CollectionUtils.isEmpty(convertedSample.getReportItems())) {
                continue;
            }
            Map<String, Boolean> reportItemLock = new HashMap<>();
            for (RoutineInspectionDto.RoutineReportItem reportItem : convertedSample.getReportItems()) {
                final ReportItemStatisticsKey key = new ReportItemStatisticsKey();
                key.groupId = sample.getGroupId();
//                key.hspOrgId = sample.getHspOrgId();
                key.instrumentId = reportItem.getInstrumentId();
                key.reportItemCode = reportItem.getReportItemCode();
                final ReportItemStatisticsVo v = map.computeIfAbsent(key, k -> {
                    final ReportItemStatisticsVo rsv = new ReportItemStatisticsVo();
                    rsv.setGroupName(sample.getGroupName());
                    rsv.setGroupId(sample.getGroupId());
                    rsv.setHspOrgName(sample.getHspOrgName());
                    rsv.setReportItemCode(reportItem.getReportItemCode());
                    rsv.setReportItemName(reportItem.getReportItemName());
                    rsv.setInstrumentId(reportItem.getInstrumentId());
                    rsv.setInstrumentName(reportItem.getInstrumentName());
                    rsv.setRetestNum(NumberUtils.LONG_ZERO);
                    rsv.setReportItemNum(NumberUtils.LONG_ZERO);
                    rsv.setTotalNum(NumberUtils.LONG_ZERO);
                    rsv.setColumns(new LinkedHashMap<>());

                    for (String column : columns) {
                        rsv.getColumns().put(column, NumberUtils.INTEGER_ZERO);
                    }

                    return rsv;
                });

                final Integer count = NumberUtils.INTEGER_ONE;
                // 报告项目数量
                v.setReportItemNum(count + v.getReportItemNum());
                // 复查数量
                v.setRetestNum(v.getRetestNum() + reportItemCountMap.getOrDefault(sample.getSampleId() + "-" + key.reportItemCode, NumberUtils.LONG_ZERO));
                // 合计数量
                final Boolean lock = reportItemLock.get(v.getReportItemCode());
                v.setTotalNum(v.getReportItemNum() + ((lock == null) ? v.getRetestNum() : NumberUtils.LONG_ZERO));
                reportItemLock.put(v.getReportItemCode(), true);

                final Date columnDate = statisticsBySignDate ? sample.getCreateDate() : sample.getFinalCheckDate();
                final String c = DateFormatUtils.format(columnDate, "MM月dd日");
                v.getColumns().put(c, v.getColumns().getOrDefault(c, NumberUtils.INTEGER_ZERO) + count);

            }
        }


        Collection<ReportItemStatisticsVo> values = new ArrayList<>(map.values());
        if (CollectionUtils.isEmpty(values)){
            values = new ArrayList<>();
        }

        // data数据合并 相同项目（同一个专业组，同一台仪器，同一个项目编码）合并成一条，数量进行累加
        if (Objects.isNull(queryVo.getHspOrgId()) && CollectionUtils.isNotEmpty(values)) {
            values = map.values().stream().collect(Collectors.groupingBy(e->e.getGroupId() + "_" + e.getInstrumentId() + "_" +  e.getReportItemCode()))
                    .values().stream().map(this::margeReportItems).collect(Collectors.toList());
        }


        // 添加缺失的报告项目
        values = doMissingReportItem(values, queryVo, columns);


        // 底部合计行
        final ReportItemStatisticsTotalVo vo = new ReportItemStatisticsTotalVo();

        // 合计数量
        vo.setTotalNumSum((int) map.values().stream().mapToLong(ReportItemStatisticsVo::getTotalNum).sum());

        // 统计总报告项目数
        vo.setTotal(map.values().size());

        // 额外列统计
        final LinkedHashMap<String, Integer> cloumnMap = new LinkedHashMap<>();
        for (String column : columns) {
            cloumnMap.put(column, map.values().stream().mapToInt(v -> v.getColumns().get(column)).sum());
        }
        vo.setExtraColumnSum(cloumnMap);


        return Map.of("columns", columns,
                "data", values.stream()
                        .sorted(Comparator.comparing(ReportItemStatisticsVo::getReportItemName).thenComparing(ReportItemStatisticsVo::getHspOrgName))
                        .collect(Collectors.toList()),
                "total", vo);
    }


    static class TestItemStatisticsKey {
        private Long groupId;

        private Long hspOrgId;

        private Long testItemId;

        private BigDecimal feePrice;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            TestItemStatisticsKey that = (TestItemStatisticsKey) o;
            return Objects.equals(groupId, that.groupId) && Objects.equals(hspOrgId, that.hspOrgId)
                    && Objects.equals(testItemId, that.testItemId) && Objects.equals(feePrice, that.feePrice);
        }

        @Override
        public int hashCode() {
            return Objects.hash(groupId, hspOrgId, testItemId, feePrice);
        }
    }

    @Data
    static class ReportItemStatisticsKey {
        private Long groupId;

        private Long instrumentId;

        private String reportItemCode;
    }


    /**
     * 合并相同检验项目的属性值
     * 多个检验项目合并成一个，相同属性的结果值 进行累加计算
     *
     * @param testItemStatisticsVos
     * @return
     */
    private TestItemStatisticsVo margeTestItems(List<TestItemStatisticsVo> testItemStatisticsVos) {
        if (CollectionUtils.isEmpty(testItemStatisticsVos)) {
            return new TestItemStatisticsVo();
        }

        // 获取第一个对象 用于获取附加字段值和返回需要展示检验项目的实体对象，每个对象的附加字段值都是一样的
        TestItemStatisticsVo testItemStatisticsVo = testItemStatisticsVos.get(0);

        // 计算固定字段值的结果的总和
        testItemStatisticsVo.setTotalNum(testItemStatisticsVos.stream().mapToInt(TestItemStatisticsVo::getTotalNum).sum());
        testItemStatisticsVo.setTotalAmount(testItemStatisticsVos.stream().map(TestItemStatisticsVo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        //        testItemStatisticsVo.setItemAmount(testItemStatisticsVos.stream().map(TestItemStatisticsVo::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

        // 获取检验项目页面实体的所有附加的展示字段
        Set<String> keySet = testItemStatisticsVos.get(0).getColumns().keySet();
        List<Map<String, Integer>> columns = testItemStatisticsVos.stream().map(TestItemStatisticsVo::getColumns).collect(Collectors.toList());

        // 根据字段值进行合并相同项目的结果值
        Map<String, Integer> margeMap = new HashMap<>();
        keySet.forEach(e -> {
            margeMap.put(e, columns.stream().map(m -> m.get(e)).reduce(0, Integer::sum));
        });

        testItemStatisticsVo.setColumns(margeMap);
        return testItemStatisticsVo;
    }

    /**
     * 合并相同报告项目的属性值
     * 多个报告项目合并成一个，相同属性的结果值 进行累加计算
     *
     * @param reportItemStatisticsVos
     * @return
     */
    private ReportItemStatisticsVo margeReportItems(List<ReportItemStatisticsVo> reportItemStatisticsVos) {
        if (CollectionUtils.isEmpty(reportItemStatisticsVos)) {
            return new ReportItemStatisticsVo();
        }

        // 获取第一个对象 用于获取附加字段值和返回需要展示报告项目的实体对象，每个对象的附加字段值都是一样的
        ReportItemStatisticsVo reportItemStatisticsVo = reportItemStatisticsVos.get(0);

        // 报告项目的 合计数量计算
        reportItemStatisticsVo.setRetestNum(reportItemStatisticsVos.stream().mapToLong(ReportItemStatisticsVo::getRetestNum).sum());
        reportItemStatisticsVo.setReportItemNum(reportItemStatisticsVos.stream().mapToLong(ReportItemStatisticsVo::getReportItemNum).sum());
        reportItemStatisticsVo.setTotalNum(reportItemStatisticsVo.getRetestNum() + reportItemStatisticsVo.getReportItemNum());

        // 获取所有附加字段的字段值
        Set<String> keySet = reportItemStatisticsVos.get(0).getColumns().keySet();
        List<Map<String, Integer>> columns = reportItemStatisticsVos.stream().map(ReportItemStatisticsVo::getColumns).collect(Collectors.toList());

        // 合并计算相同字段值的结果值总和
        Map<String, Integer> margeMap = new HashMap<>();
        keySet.forEach(e -> {
            margeMap.put(e, columns.stream().map(m -> m.get(e)).reduce(0, Integer::sum));
        });

        reportItemStatisticsVo.setColumns(margeMap);
        return reportItemStatisticsVo;
    }


    /**
     * 缺少报告项目的处理
     * 从仪器报告项目维护的数据中，进行统计，缺少的添加到结果中
     *
     * @param values
     * @param queryVo
     * @return
     */
    private Collection<ReportItemStatisticsVo> doMissingReportItem(Collection<ReportItemStatisticsVo> values,TestItemStatisticsQueryVo queryVo, LinkedList<String> columns) {

        // 查询系统所有的仪器报告项目信息，并按照专业组，仪器，报告项目编号分组
        List<InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService.selectAllInstrumentReportItemWithGroup();

        // 查询出系统所有的仪器信息
        List<InstrumentDto> instruments = instrumentService.selectAllInstrument();
        List<Long> allInstrumentId = instruments.stream().map(InstrumentDto::getInstrumentId).collect(Collectors.toList());

        // 过滤出仪器不存在的项目
        instrumentReportItems = instrumentReportItems.stream().filter(item -> allInstrumentId.contains(item.getInstrumentId())).collect(Collectors.toList());


        Map<String, List<InstrumentReportItemDto>> collect = instrumentReportItems.stream().collect(Collectors.groupingBy(e -> e.getGroupId() + "_" + e.getInstrumentId() + "_" + e.getReportItemCode()));


        // 根据专业组过滤
        if (CollectionUtils.isNotEmpty(queryVo.getGroupIds())) {
            instrumentReportItems = instrumentReportItems.stream().filter(item ->
                            queryVo.getGroupIds().contains(item.getGroupId()))
                    .collect(Collectors.toList());
        }

        // 根据仪器过滤
        if (CollectionUtils.isNotEmpty(queryVo.getInstrumentIds())) {
            instrumentReportItems = instrumentReportItems.stream().filter(item ->
                            queryVo.getInstrumentIds().contains(item.getInstrumentId()))
                    .collect(Collectors.toList());
        }

        // 过滤出不存在的报告项目 根据专业组 仪器 报告项目
        List<InstrumentReportItemDto> noExistItem = instrumentReportItems.stream().filter(reportItemDto -> values.stream().noneMatch(item ->
                StringUtils.equals(item.getReportItemCode(), reportItemDto.getReportItemCode())
                        && Objects.equals(item.getInstrumentId(), reportItemDto.getInstrumentId())
                        && Objects.equals(item.getGroupId(), reportItemDto.getGroupId())
                )).collect(Collectors.toList());


        if (CollectionUtils.isEmpty(noExistItem)) {
            return values;
        }

        // 添加项目数为0的报告项目
        for (InstrumentReportItemDto item : noExistItem) {

            final ReportItemStatisticsVo rsv = new ReportItemStatisticsVo();
            rsv.setGroupName(item.getGroupName());
            rsv.setGroupId(item.getGroupId());
            rsv.setHspOrgName(StringUtils.EMPTY);
            rsv.setReportItemCode(item.getReportItemCode());
            rsv.setReportItemName(item.getReportItemName());
            rsv.setInstrumentId(item.getInstrumentId());
            rsv.setInstrumentName(item.getInstrumentName());
            rsv.setRetestNum(NumberUtils.LONG_ZERO);
            rsv.setReportItemNum(NumberUtils.LONG_ZERO);
            rsv.setTotalNum(NumberUtils.LONG_ZERO);
            rsv.setColumns(new LinkedHashMap<>());

            for (String column : columns) {
                rsv.getColumns().put(column, NumberUtils.INTEGER_ZERO);
            }

            values.add(rsv);
        }


        return values;
    }


}
