package com.labway.lims.statistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.business.center.compare.request.OutApplyInfoRequest;
import com.labway.business.center.compare.service.OutApplyInfoService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.StopStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ApplySampleItemBloodCultureService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.dto.SelectSampleDto;
import com.labway.lims.statistics.service.ApplySampleService;
import com.labway.lims.statistics.vo.ApplySamplePageVo;
import com.labway.lims.statistics.vo.ApplySampleTestItemVo;
import com.labway.lims.statistics.vo.ApplySampleVo;
import com.swak.frame.dto.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * ApplySampleServiceImpl
 * 样本信息查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/7 19:36
 */
@DubboService
public class ApplySampleServiceImpl implements ApplySampleService {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @Resource
    private OutApplyInfoService outApplyInfoService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;
    @DubboReference
    private ApplySampleItemBloodCultureService applySampleItemBloodCultureService;
    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private ReportItemService reportItemService;


    /**
     * 查询申请单样本信息
     *
     * @param query
     * @return
     */
    @Override
    public ApplySamplePageVo selectApplySample(SampleEsQuery query) {
        // 总数
        long count = selectApplySampleCount(query);
        if (Objects.equals(count, NumberUtils.LONG_ZERO)) {
            return ApplySamplePageVo.builder().build();
        }

        return selectApplySampleData(query);
    }

    /**
     * 查询申请单样本信息数量
     * @param query
     * @return
     */
    @Override
    public long selectApplySampleCount(SampleEsQuery query) {
        // 总数
        return elasticSearchSampleService.count(query);
    }

    /**
     * 查询申请单样本信息数据
     * @param query
     * @return
     */
    @Override
    public ApplySamplePageVo selectApplySampleData(SampleEsQuery query) {
        // 查询
        final ScrollPage<BaseSampleEsModelDto> page = elasticSearchSampleService.searchAfter(query.getSearchAfter(), query);
        final List<BaseSampleEsModelDto> baseSampleEsModelDtos = page.getData();
        // 第一次查询，不会传searchAfter
        boolean first = Objects.isNull(query.getSearchAfter());

        Object searchAfter = null;
        List<ApplySampleVo> datas;
        if (CollectionUtils.isEmpty(baseSampleEsModelDtos)) {
            datas = Collections.emptyList();
        } else {
            datas = buildApplySampleResult(baseSampleEsModelDtos);
            searchAfter = page.getSearchAfter().iterator().next();
        }

        Set<String> outBarcodes = new HashSet<>();
        Set<String> printBarcodes = new HashSet<>();
        // 在第一次查询的时候统计
        if (first) {
            outBarcodes = datas.stream()
                    .filter(e -> ApplySourceEnum.HIS.name().equals(e.getSource()))
                    .map(ApplySampleVo::getOutBarcode)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            printBarcodes = datas.stream()
                    // .filter(e -> ApplySourceEnum.HIS.name().equals(e.getSource()))
                    .map(ApplySampleVo::getBarcode)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toSet());

            // 分页的情况，第一次查询的时候要把本地查询的所有信息统计出来
            boolean needSearchAfter = (baseSampleEsModelDtos.size() >= query.getPageSize());
            Object tmpSearchAfter = searchAfter;
            while (needSearchAfter) {
                ScrollPage<BaseSampleEsModelDto> page2 = elasticSearchSampleService.searchAfter(Collections.singletonList(tmpSearchAfter), query);
                final List<BaseSampleEsModelDto> samples = page2.getData();

                outBarcodes.addAll(samples.stream()
                        .filter(e -> ApplySourceEnum.HIS.name().equals(e.getSource()))
                        .map(BaseSampleEsModelDto::getOutBarcode)
                        .filter(StringUtils::isNotBlank).collect(Collectors.toSet()));
                printBarcodes.addAll(samples.stream()
                        // .filter(e -> ApplySourceEnum.HIS.name().equals(e.getSource()))
                        .map(BaseSampleEsModelDto::getBarcode)
                        .filter(StringUtils::isNotBlank).collect(Collectors.toSet()));

                if (CollectionUtils.isNotEmpty(samples) && CollectionUtils.isNotEmpty(page2.getSearchAfter())) {
                    tmpSearchAfter = page2.getSearchAfter().iterator().next();

                    needSearchAfter = (samples.size() >= query.getPageSize());
                } else {
                    needSearchAfter = false;
                }
            }
        }

        return ApplySamplePageVo.builder()
                .searchAfter(searchAfter)
                .signCount(outBarcodes.size())
                .printCount(printBarcodes.size())
                .applySamples(datas).build();
    }

    /**
     * 查询检验项目
     *
     * @param applySampleId
     * @return
     */
    @Override
    public List<ApplySampleTestItemVo> selectTestItems(Long applySampleId) {
        if (Objects.isNull(applySampleId)) {
            throw new IllegalStateException("申请单样本ID必传");
        }

        List<BaseSampleEsModelDto> baseSampleEsModelDtos = selectBaseSampleByApplySampleId(applySampleId);

        if (CollectionUtils.isEmpty(baseSampleEsModelDtos)) {
            return Collections.emptyList();
        }

        BaseSampleEsModelDto baseSampleEsModelDto = baseSampleEsModelDtos.get(0);
        List<BaseSampleEsModelDto.TestItem> testItems = baseSampleEsModelDto.getTestItems();

        if (CollectionUtils.isEmpty(testItems)) {
            return Collections.emptyList();
        }

        // 查询项目信息 根据检验项目id查询项目信息
        List<Long> testItemIds = testItems.stream().map(e -> e.getTestItemId()).collect(Collectors.toList());
        List<TestItemDto> testItemDtos = testItemService.selectByTestItemIds(testItemIds);
        Map<Long, TestItemDto> testItemMap = testItemDtos.stream().collect(Collectors.toMap(e -> e.getTestItemId(), p -> p, (v1, v2) -> v1));

        final Map<Long, List<ReportItemDto>> reportItemMap = reportItemService.selectByTestItemIds(testItemIds).stream()
                .collect(Collectors.groupingBy(ReportItemDto::getTestItemId));

        List<ApplySampleTestItemVo> testItemVos = testItems.stream().map(testItem -> {
            ApplySampleTestItemVo testItemVo = new ApplySampleTestItemVo();
            BeanUtils.copyProperties(testItem, testItemVo);
            ApplySampleItemBloodCultureDto applySampleItemBloodCultureDto = applySampleItemBloodCultureService.selectByApplySampleIdAndItemId(testItem.getTestItemId(), applySampleId);
            testItemVo.setIsBloodCultureItem(applySampleItemBloodCultureDto != null);
            // 管型
            testItemVo.setTubeCode(baseSampleEsModelDto.getTubeCode());
            testItemVo.setTubeName(baseSampleEsModelDto.getTubeName());
            // 样本类型
            testItemVo.setSampleTypeCode(baseSampleEsModelDto.getSampleTypeCode());
            testItemVo.setSampleTypeName(baseSampleEsModelDto.getSampleTypeName());
            testItemVo.setOutBarcode(baseSampleEsModelDto.getOutBarcode());
            // 是否禁用
            testItemVo.setIsDisabled(ObjectUtils.defaultIfNull(testItem.getIsDisabled(), YesOrNoEnum.NO.getCode()));
            testItemVo.setIsDisabledName(YesOrNoEnum.selectByCode(testItemVo.getIsDisabled()).getDesc());
            // 急诊类型
            testItemVo.setUrgent(baseSampleEsModelDto.getUrgent());
            testItemVo.setUrgentName(UrgentEnum.getUrgentEnum(testItemVo.getUrgent()).getValue());
            // 2023/8/9 是否终止
            testItemVo.setStopStatus(testItem.getStopStatus());
            testItemVo.setStopStatusName(StopTestStatus.getByCode(testItem.getStopStatus()).getDesc());

            // 是否是病理项目
            testItemVo.setIsBingliItem(Boolean.FALSE);

            final TestItemDto testItemDto = testItemMap.get(testItem.getTestItemId());
            Optional.ofNullable(testItemDto).ifPresent(e -> {
                // 是否是病理项目
                testItemVo.setIsBingliItem(Objects.equals(testItemDto.getItemType(), ItemTypeEnum.PATHOLOGY.name()));
                // 检验方法
                testItemVo.setExamMethodCode(e.getExamMethodCode());
                testItemVo.setExamMethodName(e.getExamMethodName());
            });

            // 返回报告项目
            Optional.ofNullable(reportItemMap.get(testItem.getTestItemId())).ifPresent(e -> {
                final List<ApplySampleTestItemVo.ReportItemVo> reportItems = e.stream()
                        .map(i -> JSON.parseObject(JSON.toJSONString(i), ApplySampleTestItemVo.ReportItemVo.class))
                        .collect(Collectors.toList());
                testItemVo.setReportItems(reportItems);
            });

            return testItemVo;
        }).collect(Collectors.toList());

        return testItemVos;
    }

    private List<BaseSampleEsModelDto> selectBaseSampleByApplySampleId(Long applySampleId) {
        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.pageNo(NumberUtils.INTEGER_ONE).pageSize(Integer.MAX_VALUE);

        // 申请单样本ID
        builder.applySampleIds(Set.of(applySampleId));

        return elasticSearchSampleService.selectSamples(builder.build());
    }

    private List<ApplySampleVo> buildApplySampleResult(List<BaseSampleEsModelDto> esModelDtos) {
        final List<SelectSampleDto> reportListDtoList =
                esModelDtos.stream()
                        .map(obj -> JSON.parseObject(JSON.toJSONString(obj), SelectSampleDto.class))
                        .collect(Collectors.toList());

        // 2023/8/9 条码环节
        List<Long> sampleIds = reportListDtoList.stream().map(SelectSampleDto::getApplySampleId).distinct().collect(Collectors.toList());
        Map<Long, List<SampleFlowDto>> sampleFlowsMap = sampleFlowService.selectWithOutContentByApplySampleIdsAsMap(sampleIds);

        List<ApplySampleVo> applySampleVos = esModelDtos.stream()
                .sorted(Comparator.comparing(BaseSampleEsModelDto::getSignDate))
                .map(esModelDto -> {
                    ApplySampleVo applySampleVo = new ApplySampleVo();
                    BeanUtils.copyProperties(esModelDto, applySampleVo);
                    //加急状态 取申请单上
                    applySampleVo.setUrgent(esModelDto.getApplyUrgent());
                    // 签收日期是默认的设置为空
                    Date signDate = esModelDto.getSignDate();
                    applySampleVo.setSignDate((Objects.isNull(signDate) || signDate.getTime() < 0) ? null : signDate);
                    // 签收人
                    applySampleVo.setSignId(esModelDto.getCreatorId());
                    applySampleVo.setSignName(esModelDto.getCreatorName());
                    // 样本状态
                    applySampleVo.setStatus(esModelDto.getSampleStatus());

                    StopStatusEnum stopStatusEnum = StopStatusEnum.DISABLE_STOP;
                    List<BaseSampleEsModelDto.TestItem> testItems = esModelDto.getTestItems();
                    if (CollectionUtils.isNotEmpty(testItems)) {
                        // 【【样本信息查询】导出文件中增加检验项目编码、检验项目名称两个字段】
                        //  https://www.tapd.cn/tapd_fe/59091617/story/detail/1159091617001002159
                        applySampleVo.getTestItemCodeList().addAll(testItems.stream().map(BaseSampleEsModelDto.TestItem::getTestItemCode).collect(Collectors.toList()));
                        applySampleVo.getTestItemNameList().addAll(testItems.stream().map(BaseSampleEsModelDto.TestItem::getTestItemName).collect(Collectors.toList()));
                        for (BaseSampleEsModelDto.TestItem testItem : testItems) {
                            final Integer isDisabled = testItem.getIsDisabled();
                            // 终止检验（不收费）、终止检验（收费）、正常
                            final Integer stopStatus = testItem.getStopStatus();

                            // 非禁用 & 非终止检验不收费的
                            if ((Objects.isNull(isDisabled) || Objects.equals(isDisabled, YesOrNoEnum.NO.getCode())) &&
                                    (Objects.isNull(stopStatus) ||
                                            Arrays.asList(StopTestStatus.NO_STOP_TEST.getCode(), StopTestStatus.STOP_TEST_CHARGE.getCode()).contains(stopStatus))) {
                                stopStatusEnum = StopStatusEnum.NORMAL;
                                break;
                            }
                        }

                        // 收费状态
                        applySampleVo.setStopStatus(stopStatusEnum.name());
                        applySampleVo.setStopStatusName(stopStatusEnum.getDesc());
                        // 申请项目数量
                        applySampleVo.setTestItemNum(testItems.size());
                    } else {
                        // 收费状态
                        applySampleVo.setStopStatus(StopStatusEnum.NORMAL.name());
                        applySampleVo.setStopStatusName(StopStatusEnum.NORMAL.getDesc());
                        // 申请项目数量
                        applySampleVo.setTestItemNum(0);
                    }

                    // 当前条码环节
                    List<SampleFlowDto> sampleFlowDtos = sampleFlowsMap.get(esModelDto.getApplySampleId());
                    if (CollectionUtils.isNotEmpty(sampleFlowDtos)) {
                        sampleFlowDtos.sort(Comparator.comparing(SampleFlowDto::getSampleFlowId).reversed());
                        // 当前条码环节
                        String operateName = sampleFlowDtos.get(0).getOperateName();
                        applySampleVo.setBarcodeLink(operateName);
                    }

                    return applySampleVo;
                }).collect(Collectors.toList());

        return applySampleVos;
    }

    @Override
    public String selectTakeSampleTimeByBarcode(String barcode, String hspOrgCode) {

        // key 前缀+取样时间字段名+送检机构+条码号
        String redisKey = redisPrefix.getBasePrefix() + "TAKE_SAMPLE_TIME" + ":" + hspOrgCode + ":" + barcode;
        String takeSampleTime = stringRedisTemplate.opsForValue().get(redisKey);

        // 如果为该key在redis中不存在，说明key可能已过期， 再查一遍业务中台放入redis
        if (takeSampleTime == null) {

            final OutApplyInfoRequest req = new OutApplyInfoRequest();
            req.setBarcode(barcode);
            req.setHspOrgCode(hspOrgCode);
            req.setSignOrgCode(LoginUserHandler.get().getOrgCode());
            req.setSignOrgName(LoginUserHandler.get().getOrgName());
            req.setNoType(null);

            final Response<OutApplyInfoDTO> body = outApplyInfoService.queryOutApplyInfo(req);


            if (Objects.isNull(body)) {
                return null;
            }

            // 判断是否成功
            if (BooleanUtils.isNotTrue(Objects.equals(body.getCode(), 0))) {
                return null;
            }

            final OutApplyInfoDTO data = body.getData();
            if (Objects.isNull(data)) {
                return null;
            }
            // 物流取样时间
            takeSampleTime = Objects.nonNull(data.getTakeSampleTime()) ? data.getTakeSampleTime().toString() : "";;
            stringRedisTemplate.opsForValue().set(redisKey, takeSampleTime, Duration.ofDays(180));

        }
        return takeSampleTime;
    }

}
