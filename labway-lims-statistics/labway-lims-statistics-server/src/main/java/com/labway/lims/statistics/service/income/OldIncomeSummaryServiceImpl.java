package com.labway.lims.statistics.service.income;

import com.google.common.collect.Lists;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.TestItemIncomeSummaryResponseDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.statistics.service.FinancialManagementService;
import com.labway.lims.statistics.service.bySendDoctor.OldHspOrgBySendDoctorServiceImpl;
import com.labway.lims.statistics.service.impl.FinancialManagementServiceImpl;
import com.labway.lims.statistics.vo.IncomeSummaryVo;
import com.labway.lims.statistics.vo.TestItemIncomeRequestVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.labway.lims.apply.api.dto.TestItemIncomeFilterDto.getCustomerNameType;

@Component
public class OldIncomeSummaryServiceImpl implements IncomeSummaryService {
    @Resource
    private FinancialManagementService financialManagementService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @Resource
    private EsConfig esConfig;

    @Override
    public String version() {
        return "1.0";
    }

    @Override
    public IncomeSummaryVo incomeSummaryStatistics(TestItemIncomeRequestVo vo) {

        // 将查询条件 转换为es查询条件
        SampleEsQuery esDto = getSampleEsQueryFromTestItemIncomeSummaryRequestVo(vo);

        // 所有es 结构数据
        // 根据时间拆分，并行查询ES
        List<BaseSampleEsModelDto> baseSampleEsModelDtos =
                financialManagementService.selectSamples(esDto, StringUtils.isNotBlank(vo.getFinancialMonth()));

        final CustomerNameTypeEnum customerNameType = getCustomerNameType(vo.getCustomerNameType());

        final List<HspOrganizationDto> hspOrganizations = new ArrayList<>();
        if (CollectionUtils.isEmpty(vo.getHspOrgIds())) {
            hspOrganizations.addAll(hspOrganizationService.selectAll().stream().filter(e -> Objects.equals(e.getIsExport(), YesOrNoEnum.NO.getCode()))
                    .collect(Collectors.toList()));
        } else {
            hspOrganizations.addAll(hspOrganizationService.selectByHspOrgIds(vo.getHspOrgIds()));
        }

        // 查询数据
        final List<TestItemIncomeSummaryResponseDto> list = financialManagementService.getTestItemIncomeSummaryResponseDtos(esDto, vo, baseSampleEsModelDtos);

        // 填充没有数据的客商
        for (HspOrganizationDto e : hspOrganizations) {
            if (list.stream().noneMatch(k -> Objects.equals(k.getCustomerId(), e.getHspOrgId()))) {
                final TestItemIncomeSummaryResponseDto t = new TestItemIncomeSummaryResponseDto();
                if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME)) {
                    t.setCustomerName(e.getInvoice());
                } else {
                    t.setCustomerName(e.getHspOrgName());
                }

                final FinancialManagementServiceImpl.DateRange range = FinancialManagementServiceImpl.getDateRange(esDto);

                t.setCustomerId(e.getHspOrgId());
                t.setStartDate(range.getStartDate());
                t.setEndDate(range.getEndDate());
                t.setCountSum(0);
                t.setFeePriceSum(BigDecimal.ZERO);
                t.setPayAmountSum(BigDecimal.ZERO);
                t.setItemList(Lists.newArrayList());

                list.add(t);
            }
        }

        final IncomeSummaryVo v = new IncomeSummaryVo();
        v.setTotalFeePriceSum(BigDecimal.ZERO);
        v.setTotalPayAmountSum(BigDecimal.ZERO);
        v.setTotalDetailFeePriceSum(BigDecimal.ZERO);
        v.setList(list);


        for (TestItemIncomeSummaryResponseDto e : list) {
            if (Objects.nonNull(e.getDetail())) {
                v.setTotalDetailFeePriceSum(v.getTotalDetailFeePriceSum().add(e.getDetail().getFeePriceSum()));
            }
            v.setTotalFeePriceSum(v.getTotalFeePriceSum().add(e.getFeePriceSum()));
            v.setTotalPayAmountSum(v.getTotalPayAmountSum().add(e.getPayAmountSum()));
        }

        return v;
    }

    /**
     * 销售项目收入查询 参数 转为 es 查询 结构
     */
    private SampleEsQuery getSampleEsQueryFromTestItemIncomeSummaryRequestVo(TestItemIncomeRequestVo vo) {

        // 检查 送检时间 与财务月份
        OldHspOrgBySendDoctorServiceImpl.checkDateRangRequest(vo.getFinancialMonth(), vo.getBeginDeliveryDate(), vo.getEndDeliveryDate());

        // 将查询条件 转换为es查询条件
        SampleEsQuery dto = new SampleEsQuery();
        dto.setPageSize(esConfig.getPageSize());
        dto.setPageNo(NumberUtils.INTEGER_ONE);
        // 1.0.6.6需求改为不管该样本是否已经审核都进行查询
        //dto.setIsAudit(YesOrNoEnum.YES.getCode());

        // 补充查询时间范围
        OldHspOrgBySendDoctorServiceImpl.sampleEsQueryAddDateRang(dto, vo.getFinancialMonth(), vo.getBeginDeliveryDate(), vo.getEndDeliveryDate());

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            dto.setHspOrgIds(vo.getHspOrgIds());
        }

        return dto;
    }
}
