package com.labway.lims.statistics.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.TestResultOntimeRateRequestVo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <pre>
 * TestResultOntimeRateStatisticsController
 * 检验结果及时率统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/2/29 11:54
 */
@RestController
@RequestMapping("test-result-ontime-rate")
public class TestResultOntimeRateStatisticsController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @DubboReference
    private TestItemService testItemService;

    @PostMapping("statistics")
    public Object statistics(@RequestBody TestResultOntimeRateRequestVo requestVo) {

        if (Objects.isNull(requestVo.getStartDate()) || Objects.isNull(requestVo.getEndDate())) {
            throw new IllegalArgumentException("日期错误");
        }

        SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder()
                .pageNo(NumberUtils.INTEGER_ONE)
                .pageSize(Integer.MAX_VALUE)
                .isAudit(YesOrNoEnum.YES.getCode())
                .startSignDate(requestVo.getStartDate())
                .endSignDate(requestVo.getEndDate());

        if (CollectionUtils.isNotEmpty(requestVo.getHspOrgIds())) {
            builder.hspOrgIds(new HashSet<>(requestVo.getHspOrgIds()));
        }
        if (CollectionUtils.isNotEmpty(requestVo.getGroupIds())) {
            builder.groupIds(new HashSet<>(requestVo.getGroupIds()));
        }
        if (CollectionUtils.isNotEmpty(requestVo.getTestItemCodes())) {
            builder.testItemCodes(new HashSet<>(requestVo.getTestItemCodes()));
        }
        if (CollectionUtils.isNotEmpty(requestVo.getApplyTypeCodes())) {
            builder.applyTypes(new HashSet<>(requestVo.getApplyTypeCodes()));
        }

        // 所有的检验项目
        // List<TestItemDto> testItemDtos = testItemService.selectByOrgId(LoginUserHandler.get().getOrgId());
        Map<String, TestItemDto> testItemDtoMap = testItemService.selectByOrgId(LoginUserHandler.get().getOrgId())
                .stream().collect(Collectors.toMap(TestItemDto::getTestItemCode, Function.identity(), (a, b) -> b));
        // 查询复核条件的所有的样本数据
        List<BaseSampleEsModelDto> sampleEsModelDtos = elasticSearchSampleService.selectSamples(builder.build());

        List<SampleTestItem> sampleTestItems =
                sampleEsModelDtos.stream()
                        .filter(e -> Objects.nonNull(e.getSignDate()) && Objects.nonNull(e.getFinalCheckDate()))
                        .flatMap(e -> e.getTestItems().stream().map(item -> {
                            TestItemDto testItemDto = testItemDtoMap.get(item.getTestItemCode());
                            if (Objects.nonNull(testItemDto) && Objects.nonNull(testItemDto.getTestDate())) {
                                return new SampleTestItem(item.getTestItemCode(), item.getTestItemName(), e.getSignDate(), e.getFinalCheckDate(), testItemDto.getTestDate() * 24);
                            } else {
                                return new SampleTestItem(item.getTestItemCode(), item.getTestItemName(), e.getSignDate(), e.getFinalCheckDate(), 0);
                            }
                        }))
                        .filter(e -> CollectionUtils.isEmpty(requestVo.getTestItemCodes()) || requestVo.getTestItemCodes().contains(e.getTestItemCode()))
                        .collect(Collectors.toList());

        Map<String, List<SampleTestItem>> testItemMap = sampleTestItems.stream().collect(Collectors.groupingBy(SampleTestItem::getTestItemCode));

        List<TestResultOntimeRateItem> rateItemList = testItemMap.entrySet().stream().map(entry -> {
            String testItemCode = entry.getKey();
            List<SampleTestItem> testItems = entry.getValue();
            long testCount = testItems.size();
            long finishCount = testItems.stream().filter(e -> {
                long testDuration = e.getAutitDate().getTime() - e.getSignDate().getTime();
                return testDuration < TimeUnit.HOURS.toMillis(e.getReportDate());
            }).count();

            TestResultOntimeRateItem item = new TestResultOntimeRateItem();
            item.setTestItemCode(testItemCode);
            item.setTestItemName(testItems.get(0).getTestItemName());
            item.setReportDate(testItems.get(0).getReportDate());
            item.setTestCount(testCount);
            item.setFinishCount(finishCount);
            item.setPercent(finishCount * 100D / testCount);

            return item;
        }).sorted((e1, e2) -> e2.getPercent().compareTo(e1.getPercent())).collect(Collectors.toList());

        double totalTestCount = rateItemList.stream().mapToDouble(TestResultOntimeRateItem::getTestCount).sum();
        double totalFinishCount = rateItemList.stream().mapToDouble(TestResultOntimeRateItem::getFinishCount).sum();

        return Map.of("records", rateItemList, "totalTestCount", totalTestCount, "totalFinishCount", totalFinishCount);
    }

    @Getter
    @Setter
    static class TestResultOntimeRateItem {
        // 检验项目编码
        private String testItemCode;
        // 检验项目名称
        private String testItemName;
        // 报告周期（小时）
        private Integer reportDate;
        // 检验数量
        private Long testCount;
        // 周期内完成数量
        private Long finishCount;
        // 完成率
        private Double percent;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    static class SampleTestItem {
        // 检验项目编码
        private String testItemCode;
        // 检验项目名称
        private String testItemName;
        // 签收时间
        private Date signDate;
        // 审核时间
        private Date autitDate;
        // 报告周期（小时）
        private Integer reportDate;
    }

}
