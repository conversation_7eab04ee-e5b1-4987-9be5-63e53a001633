package com.labway.lims.statistics.service.chain.byPlateform;

import com.labway.lims.statistics.service.chain.EsDataToSampleTestItemCommand;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ByPlatformStatisticsChain extends ChainBase implements InitializingBean {

    @Resource                //AcquireDateCommand
    private AcquireDateCommand acquireDateCommand;
    @Resource      //EsDataToSampleTestItemCommand
    private EsDataToSampleTestItemCommand esDataToSampleTestItemCommand;
    @Resource                    //              ArithmeticalDiscountCommand
    private ArithmeticalDiscountCommand arithmeticalDiscountCommand;

    @Override
    public void afterPropertiesSet() {
        // 获取es数据和送检机构
        addCommand(acquireDateCommand);

        // es样本转换样本检验项目和折扣
        addCommand(esDataToSampleTestItemCommand);

        // 计算金额
        addCommand(arithmeticalDiscountCommand);

        addCommand(e -> PROCESSING_COMPLETE);

    }
}
