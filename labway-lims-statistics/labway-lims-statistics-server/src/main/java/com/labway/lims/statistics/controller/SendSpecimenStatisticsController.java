package com.labway.lims.statistics.controller;


import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.SendSpecimenQueryVo;
import com.labway.lims.statistics.vo.SendSpecimenVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 送检标本统计
 */
@Slf4j
@RequestMapping("/send-specimen-statistics")
@RestController
public class SendSpecimenStatisticsController extends BaseController {


    @DubboReference
    private DictService dictService;

    @javax.annotation.Resource
    private ElasticSearchSampleService elasticSearchSampleService;


    // region ---------------------------------送检标本-----------------------------


    /**
     * 送检标本查询
     */
    @PostMapping("/query")
    public Collection<SendSpecimenVo> query(@RequestBody SendSpecimenQueryVo queryVo) {
        queryVo.defaultDate();

        // 查询条件
        final AtomicInteger pageNo = new AtomicInteger(1);
        final SampleEsQuery query = SampleEsQuery.builder()
                .pageSize(10000)
                .startCreateDate(queryVo.getStartDate())
                .endCreateDate(queryVo.getEndDate())
                .isAudit(queryVo.getIsAudit())
                .patientName(queryVo.getPatientName())
                .build();
        if (StringUtils.isNotBlank(queryVo.getBarcode())) {
            query.setBarcodes(Set.of(queryVo.getBarcode()));
        }

        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Set.of(queryVo.getHspOrgId()));
        }

        if (CollectionUtils.isNotEmpty(queryVo.getHspOrgIds())) {
            query.setHspOrgIds(queryVo.getHspOrgIds());
        }

        final List<Long> testItemIds = queryVo.getTestItemIds();
        if (CollectionUtils.isNotEmpty(testItemIds)) {
            query.setTestItemIds(new HashSet<>(testItemIds));
        }

        // 结果
        final Map<Long, SendSpecimenVo> sendSpecimenMap = new LinkedHashMap<>();

        // 签收样本个数
        final Map<Long, AtomicInteger> barcodeMap = new HashMap<>(40);

        // 送检人数总和
        final Map<Long, Integer> sendersMap = new HashMap<>(40);

        List<Object> searchAfter = null;

        do {
            // 自增页码
            query.setPageNo(pageNo.getAndIncrement());

            final ScrollPage<? extends BaseSampleEsModelDto> page = elasticSearchSampleService.searchAfter(searchAfter, query);
            searchAfter = page.getSearchAfter();
            final List<? extends BaseSampleEsModelDto> datas = page.getData();
            if (CollectionUtils.isEmpty(datas)) {
                break;
            }

            // 禁用的不显示
            datas.removeIf(e -> Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()));

            for (final BaseSampleEsModelDto data : datas) {
                final Long hspOrgId = data.getHspOrgId();
                final String hspOrgName = data.getHspOrgName();

                List<BaseSampleEsModelDto.TestItem> testItems = ObjectUtils.defaultIfNull(data.getTestItems(), Collections.emptyList());

                // 禁用的不显示
                testItems.removeIf(e -> Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()));
                // 终止的不显示
                testItems.removeIf(e -> !Objects.equals(e.getStopStatus(), YesOrNoEnum.NO.getCode()));

                final List<String> testItemStrs = testItems.stream()
                        .filter(f -> CollectionUtils.isEmpty(testItemIds) || testItemIds.contains(f.getTestItemId()))
                        .map(BaseSampleEsModelDto.TestItem::getTestItemName)
                        .collect(Collectors.toList());
                // 收集检验项目code
                final List<String> testItemCodeStrs = testItems.stream()
                        .filter(f -> CollectionUtils.isEmpty(testItemIds) || testItemIds.contains(f.getTestItemId()))
                        .map(BaseSampleEsModelDto.TestItem::getTestItemCode)
                        .collect(Collectors.toList());

                // 如果 id不为空（证明有查询条件）并且检验项目名称为空 则过滤
                if (CollectionUtils.isNotEmpty(testItemIds) && CollectionUtils.isEmpty(testItemStrs) && CollectionUtils.isEmpty(testItemCodeStrs)) {
                    continue;
                }

                final SendSpecimenVo vo = sendSpecimenMap.computeIfAbsent(hspOrgId, f -> {
                    final SendSpecimenVo item = new SendSpecimenVo();
                    item.setHspOrgId(hspOrgId);
                    item.setHspOrgName(hspOrgName);
                    item.setSignCount(new AtomicInteger());
                    item.setAuditCount(new AtomicInteger());
                    item.setItems(Lists.newArrayList());
                    return item;
                });

                // 签收个数
                barcodeMap.computeIfAbsent(hspOrgId, f -> new AtomicInteger(0))
                        .addAndGet(testItemStrs.size());
                // 审核个数
                if (Objects.equals(data.getSampleStatus(), SampleStatusEnum.AUDIT.getCode())) {
                    vo.getAuditCount().addAndGet(testItemStrs.size());
                }

                // 送检个数


                final SendSpecimenVo.Sample sample = JSON.parseObject(JSON.toJSONString(data), SendSpecimenVo.Sample.class);
                sample.setSignDate(data.getCreateDate());
                sample.setAuditDate(data.getFinalCheckDate());
                sample.setPatientSubage(data.getPatientSubage());
                sample.setPatientSubageUnit(data.getPatientSubageUnit());
                sample.setTestItem(String.join(",", testItemStrs));
                sample.setSendDoctorName(data.getSendDoctorName());
                sample.setSampleTypeCode(data.getSampleTypeCode());
                sample.setTestItemCode(String.join(",", testItemCodeStrs));
                sample.setSendDoctorCode(data.getSendDoctorCode());
                sample.setSampleTypeName(data.getSampleTypeName());
                vo.getItems().add(sample);
                vo.getItems().sort(Comparator.comparing(SendSpecimenVo.Sample::getSignDate));
            }

        } while (!Thread.currentThread().isInterrupted());

        // 遍历sendSpecimenMap根据‘性别、送检机构、名字’判断同一个人
        sendSpecimenMap.forEach((key, value) -> {
            Set<String> senders = value.getItems().stream()
                    .filter(s -> StringUtils.isNotBlank(s.getPatientName()))
                    .map(s -> s.getPatientSex() + "-" + value.getHspOrgId() + "-" + s.getPatientName())
                    .collect(Collectors.toSet());
            sendersMap.put(key, senders.size());
        });

        return sendSpecimenMap
                .values()
                .stream()
                .peek(p -> p.setSignCount(Optional.ofNullable(barcodeMap.get(p.getHspOrgId())).orElse(new AtomicInteger())))
                .peek(p -> p.setSendersCount(Optional.ofNullable(sendersMap.get(p.getHspOrgId())).orElse(0)))
                .sorted(Comparator.comparing(sort -> PinyinUtil.getFirstLetter(sort.getHspOrgName(), StringUtils.EMPTY)))
                .collect(Collectors.toList());
    }
    // endregion ---------------------------------送检标本-----------------------------

}
