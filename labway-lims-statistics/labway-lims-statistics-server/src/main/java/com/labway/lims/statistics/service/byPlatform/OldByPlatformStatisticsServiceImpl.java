package com.labway.lims.statistics.service.byPlatform;

import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import com.labway.lims.apply.api.dto.ByPlatformStatisticsResponseDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.controller.FinanceStatisticsController;
import com.labway.lims.statistics.service.FinancialManagementService;
import com.labway.lims.statistics.vo.FinanceStatisticsQueryVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Component
@Primary
public class OldByPlatformStatisticsServiceImpl implements ByPlatformStatisticsService{

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Resource
    private FinancialManagementService financialManagementService;

    @Resource
    private EsConfig esConfig;


    @Override
    public String version() {
        return "1.0";
    }

    @Override
    public ByPlatformStatisticsResponseDto byPlatformStatistics(FinanceStatisticsQueryVo query){

        List<HspOrganizationDto> hspOrganizations;
        Set<Long> hspOrgIds = query.getHspOrgIds();
        if (CollectionUtils.isEmpty(hspOrgIds)) {
            hspOrganizations = hspOrganizationService.selectAll().stream()
                    .filter(f -> Objects.equals(f.getIsExport(), YesOrNoEnum.NO.getCode()))
                    .collect(Collectors.toList());
            hspOrgIds = hspOrganizations.stream().map(HspOrganizationDto::getHspOrgId).collect(Collectors.toSet());
        } else {
            hspOrganizations = hspOrganizationService.selectByHspOrgIds(hspOrgIds).stream()
                    .filter(f -> Objects.equals(f.getIsExport(), YesOrNoEnum.NO.getCode()))
                    .collect(Collectors.toList());
            Set<Long> selectHspOrgIds = hspOrganizations.stream().map(HspOrganizationDto::getHspOrgId).collect(Collectors.toSet());
            hspOrgIds.stream().filter(e -> !selectHspOrgIds.contains(e)).findAny().ifPresent(e -> {
                throw new IllegalArgumentException(String.format("机构 [%s] 不存在", e));
            });
            hspOrgIds = selectHspOrgIds;
        }

        // 将查询条件 转换为es查询条件
        SampleEsQuery esDto = new SampleEsQuery();
        esDto.setPageSize(esConfig.getPageSize());
        esDto.setPageNo(NumberUtils.INTEGER_ONE);
        // 1.0.6.6需求改为不管该样本是否已经审核都进行查询
        // esDto.setIsAudit(YesOrNoEnum.YES.getCode());
        esDto.setHspOrgIds(hspOrgIds);
        // 专业组过滤
        if (CollectionUtils.isNotEmpty(query.getGroupIds())) {
            esDto.setGroupIds(query.getGroupIds());
        }

        // 时间
        final boolean dateTypeBool = Objects.equals(query.getDateType(), FinanceStatisticsController.DateTypeEnum.SEND_DATE.getCode());
        if(dateTypeBool){
            esDto.setStartCreateDate(query.getStartDate());
            esDto.setEndCreateDate(query.getEndDate());
        }else{
            esDto.setStartFinalCheckOrCreateDate(query.getStartDate());
            esDto.setEndFinalCheckOrCreateDate(query.getEndDate());
        }

        // 所有es 结构数据
        // 根据时间拆分，并行查询ES
        List<BaseSampleEsModelDto> baseSampleEsModelDtos =
                financialManagementService.selectSamples(esDto, !dateTypeBool);

        return financialManagementService.getByPlatformStatisticsResponseDto(
                esDto, getCustomerNameType(query.getInvoiceType()), hspOrganizations, baseSampleEsModelDtos);
    }

    /**
     * 客户名称 类别
     */
    private CustomerNameTypeEnum getCustomerNameType(Integer customerNameType) {
        if (Objects.isNull(customerNameType)) {
            // 默认开票名称
            return CustomerNameTypeEnum.INVOICE_NAME;
        }
        CustomerNameTypeEnum customerNameTypeEnum = null;
        if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME.getCode())) {
            customerNameTypeEnum = CustomerNameTypeEnum.INVOICE_NAME;
        } else if (Objects.equals(customerNameType, CustomerNameTypeEnum.ORG_NAME.getCode())) {
            customerNameTypeEnum = CustomerNameTypeEnum.ORG_NAME;
        }
        return customerNameTypeEnum;
    }
}
