package com.labway.lims.statistics.model.es;

import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * 微生物药物
 */
@Getter
@Setter
public final class MicrobiologyMedicines {
    /**
     * 药物id
     */
    @Field(type = FieldType.Long)
    private Long medicineId;
    /**
     * 药物编码
     */
    @Field(type = FieldType.Keyword)
    private String medicineCode;
    /**
     * 药物名称
     */
    @Field(type = FieldType.Keyword)
    private String medicineName;
    /**
     * 药物方法
     */
    @Field(type = FieldType.Keyword)
    private String medicineMethod;
    /**
     * 敏感度
     *
     * @see MicroSusceptibilityEnum
     */
    @Field(type = FieldType.Keyword)
    private String susceptibility;
    /**
     * 单位
     */
    @Field(type = FieldType.Keyword)
    private String unit;
    /**
     * 参考值
     */
    @Field(type = FieldType.Keyword)
    private String range;
    /**
     * 结果
     */
    @Field(type = FieldType.Keyword)
    private String result;
}
