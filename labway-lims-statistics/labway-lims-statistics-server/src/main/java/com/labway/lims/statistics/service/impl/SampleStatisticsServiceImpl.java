package com.labway.lims.statistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsItemDto;
import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsResponseDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SampleTestItemDto;
import com.labway.lims.apply.api.dto.TestItemIncomeFilterDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.api.client.SampleStatisticsService;
import com.labway.lims.statistics.dto.PushSampleStatisticsInfoDto;
import com.labway.lims.statistics.dto.SelectSampleDto;
import com.labway.lims.statistics.enums.DisplayOrgType;
import com.labway.lims.statistics.service.FinancialManagementService;
import com.labway.lims.statistics.service.bySendDoctor.HspOrgBySendDoctorService;
import com.labway.lims.statistics.service.income.IncomeSummaryService;
import com.labway.lims.statistics.vo.PushSampleStatisticsInfoVo;
import com.labway.lims.statistics.vo.SampleGroupStatisticsDetailVo;
import com.labway.lims.statistics.vo.SampleGroupStatisticsVo;
import com.labway.lims.statistics.vo.SampleStatisticsQueryVo;
import com.labway.lims.statistics.vo.SampleStatisticsVo;
import com.labway.lims.statistics.vo.SampleTestItemStatisticsVo;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * SampleStatisticsServiceImpl
 * 申请单样本
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 15:04
 */
@Slf4j
@DubboService
@RefreshScope
public class SampleStatisticsServiceImpl implements SampleStatisticsService, ApplicationContextAware {

    private static final String PUSH_SAMPLE_STATISTICS_INFO_KEY = "pushSampleStatisticsKey";
    private static final int PUSH_SAMPLE_STATISTICS_EXPIRE_TIME_ = 24 * 60 * 60;

    @Value("${business-center.org-code}")
    private String orgCode;
    @Value("${business-center.org-name}")
    private String orgName;
    // 统计样本推送url 默认测试环境
    @Value("${business-center.url.push-statistics-sample:http://121.36.199.164/9902/statistics/saveStatisticsSample}")
    private String businessCenterUrl;
    @Value("${business-center.splitCount:10000}")
    private Integer splitCount;
    @Value("${business-center.pushCount:10}")
    private Integer pushCount;
    @Value("${statistics.by-send-doctor.version:2.0}")
    private String bySendDoctorVersion;
    @Value("${statistics.income-summary.version:2.0}")
    private String incomeSummaryVersion;

    // 根据送检医生统计财务
    private final Map<String, HspOrgBySendDoctorService> HSPORG_BY_SEND_DOCTOR_SERVICE_MAP = new HashMap<>();

    // 销售项目收入查询Map
    private final Map<String, IncomeSummaryService> INCOME_SUMMARY_SERVICE_MAP = new HashMap<>();

    @Value("${business-center.enableTestTimeOut:false}")
    private boolean enableTestTimeOut;
    @Value("${business-center.testTimeOut:60000}")
    private long testTimeOut;


    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private FinancialManagementService financialManagementService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private EsConfig esConfig;


    /**
     * 查询样本信息
     */
    @Override
    public SampleStatisticsVo selectSampleStatistics(SampleStatisticsQueryVo vo) {
        Date signDateStart = vo.getSignDateStart();
        Date signDateEnd = vo.getSignDateEnd();
        Long hspOrgId = vo.getHspOrgId();
        Integer isAudit = vo.getIsAudit();
        Integer isShowDisabled = vo.getIsDisabled();
        String patientName = vo.getPatientName();
        String barcode = vo.getBarcode();

        // 签收时间
        if (Objects.isNull(signDateStart) || Objects.isNull(signDateEnd)) {
            throw new IllegalStateException("日期错误");
        }

        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.pageNo(NumberUtils.INTEGER_ONE)
                .pageSize(esConfig.getPageSize())
                .startSignDate(signDateStart)
                .endSignDate(signDateEnd)
                /*.excludeSampleStatus(List.of(SampleStatusEnum.STOP_TEST.getCode()))*/;

        // 送检机构
        if (Objects.nonNull(hspOrgId)) {
            builder.hspOrgIds(Set.of(hspOrgId));
        }
        // 审核状态
        if (Objects.nonNull(isAudit)) {
            builder.isAudit(isAudit);
        }
        // 不显示禁用条码 1：显示，0：不显示
        if (Objects.equals(YesOrNoEnum.NO.getCode(), isShowDisabled)) {
            // 不显示禁用条码
            builder.isDisabled(YesOrNoEnum.NO.getCode());
        }
        // 病人名称
        if (StringUtils.isNotBlank(patientName)) {
            builder.patientName(patientName);
        }
        // 条码号or外部条码号
        if (StringUtils.isNotBlank(barcode)) {
//            builder.barcodes(Set.of(barcode));
            builder.barcodeOrOutbarcodes(Set.of(barcode));
        }

        // 检验项目
        if (CollectionUtils.isNotEmpty(vo.getTestItemCodes())) {
            builder.testItemCodes(vo.getTestItemCodes());
        }

        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(builder.build());

        return buildSampleGroupResult(baseSampleEsModelDtos, vo.getSortList());
    }

    /**
     * 查询检验项目
     */
    @Override
    public List<SampleTestItemStatisticsVo> selectSampleTestItemStatistics(Long applySampleId) {
        if (Objects.isNull(applySampleId)) {
            throw new IllegalStateException("申请单样本ID必传");
        }

        List<BaseSampleEsModelDto> baseSampleEsModelDtos = selectBaseSampleByApplySampleId(applySampleId);

        if (CollectionUtils.isEmpty(baseSampleEsModelDtos)) {
            return Collections.emptyList();
        }

        BaseSampleEsModelDto baseSampleEsModelDto = baseSampleEsModelDtos.get(0);
        List<BaseSampleEsModelDto.TestItem> testItems = baseSampleEsModelDto.getTestItems();

        return testItems.stream().map(testItem -> {
            SampleTestItemStatisticsVo itemStatisticsVo = new SampleTestItemStatisticsVo();
            BeanUtils.copyProperties(testItem, itemStatisticsVo);
            itemStatisticsVo.setOutBarcode(baseSampleEsModelDto.getOutBarcode());
            return itemStatisticsVo;
        }).collect(Collectors.toList());
    }

    /**
     * 推送样本统计信息至业务中台
     * @param dto
     * @return
     */
    @Override
    public PushSampleStatisticsInfoVo pushSampleStatisticsInfo(PushSampleStatisticsInfoDto dto) {
        log.info("开始查询推送样本统计信息，查询入参：{}", JSONObject.toJSONString(dto));
        PushSampleStatisticsInfoVo pushSampleStatisticsInfoVo = new PushSampleStatisticsInfoVo();

        // 查询样本信息 - 根据跟新时间
        SampleEsQuery queryDto = new SampleEsQuery();
        queryDto.setUpdateDateBegin(dto.getUpdateDateBegin());
        queryDto.setUpdateDateEnd(dto.getUpdateDateEnd());
        queryDto.setPageNo(dto.getPageNo());
        queryDto.setPageSize(dto.getPageSize());
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(queryDto);
        if (CollectionUtils.isEmpty(baseSampleEsModelDtos)){
            log.info("未查询到需要推送的样本统计信息，推送结束！");
            return pushSampleStatisticsInfoVo;
        }
        log.info("开始处理需要推送的统计样本信息，ES查询样本总数：{}条。",baseSampleEsModelDtos.size());

        // 过滤掉终止检验不收费的项目

        // 封装推送数据
        pushSampleStatisticsInfoVo.setSampleStatisticsRequests(getPushSampleStatisticsInfoVo(baseSampleEsModelDtos,queryDto));

        // 推送数据到业务中台 -- 每次推送10000条
        for (List<PushSampleStatisticsInfoVo.SampleStatisticsRequest> sampleStatisticsRequests : ListUtils.partition(pushSampleStatisticsInfoVo.getSampleStatisticsRequests(), splitCount)) {
            if (CollectionUtils.isEmpty(sampleStatisticsRequests)) {
                break;
            }
            PushSampleStatisticsInfoVo tempVo = new PushSampleStatisticsInfoVo();
            tempVo.setSampleStatisticsRequests(sampleStatisticsRequests);
            pushSampleStatisticsToBusinessCenter(tempVo);
            try {
                Thread.sleep(splitCount * 10L);
            } catch (InterruptedException e) {
                log.error("推送样本休眠时出现异常，异常信息：",e);
            }

            log.info("推送样本休眠结束，本次推送样本数量：{}，开始推送下一批统计样本！！", sampleStatisticsRequests.size());
        }

        log.info("统计样本推送成功,推送的检验项目数量：{}条！",pushSampleStatisticsInfoVo.getSampleStatisticsRequests().size());
        return pushSampleStatisticsInfoVo;
    }

    @Override
    public PushSampleStatisticsInfoVo pushSampleStatisticsInfoNew(PushSampleStatisticsInfoDto dto) {
        log.info("开始查询推送样本统计信息-新，查询入参：{}", JSONObject.toJSONString(dto));

        // 判断当前是否是重复调用
        if (stringRedisTemplate.hasKey(dto.getIdempotentCode())){
            log.info("已执行过当前推送样本统计信息请求-新，请求幂等标识：{},当前请求结束！",dto.getIdempotentCode());
            return new PushSampleStatisticsInfoVo();
        }

        // 缓存幂等key，过期时间24小时
        stringRedisTemplate.opsForValue().set(dto.getIdempotentCode(),"",24, TimeUnit.HOURS);

        // 模拟超时幂等调用 测试环境
        if (enableTestTimeOut){
            try {
                Thread.sleep(testTimeOut);
            } catch (InterruptedException e) {
                log.error("模拟推送样本超时，休眠时出现异常，异常信息：",e);
            }
            log.info("模拟超时幂等调用，本次推送结束！");
            return new PushSampleStatisticsInfoVo();
        }

        // 判断当天是否执行成功
        if (stringRedisTemplate.hasKey(getPushStatisticsCacheKey(new Date()))) {
            log.info("当天已执行过推送样本统计信息-新，本次推送结束！");
            return new PushSampleStatisticsInfoVo();
        }


        PushSampleStatisticsInfoVo pushSampleStatisticsInfoVo = new PushSampleStatisticsInfoVo();

        dto.setBeginDeliveryDate(dto.getUpdateDateBegin());
        dto.setEndDeliveryDate(dto.getUpdateDateEnd());
        dto.setCustomerNameType(dto.getCustomerNameType() == null ? CustomerNameTypeEnum.INVOICE_NAME.getCode() : dto.getCustomerNameType());
        dto.setDisplayOrgType(dto.getDisplayOrgType() == null ? DisplayOrgType.MERGE.getType() : dto.getDisplayOrgType());

        // 查询统计的项目信息
        HspOrgSendDoctorStatisticsResponseDto hspOrgSendDoctorStatisticsResponseDto = getHspOrgBySendDoctorService().hspOrgBySendDoctorStatistics(dto);
        if (Objects.isNull(hspOrgSendDoctorStatisticsResponseDto)
                || CollectionUtils.isEmpty(hspOrgSendDoctorStatisticsResponseDto.getOrgList())){
            log.info("未查询到需要推送的样本统计信息-新，推送结束！");
            return pushSampleStatisticsInfoVo;
        }

        // 封装转换推送数据
        List<PushSampleStatisticsInfoVo.SampleStatisticsRequest> sampleStatisticsRequestList = convertPushSampleStatisticsInfoVo(hspOrgSendDoctorStatisticsResponseDto);
        if (CollectionUtils.isEmpty(sampleStatisticsRequestList)){
            log.info("未转换出需要推送的样本统计信息-新，推送结束！");
            return pushSampleStatisticsInfoVo;
        }

        // 添加样本唯一编号 根据创建时间 样本 检验项目编码
        List<PushSampleStatisticsInfoVo.SampleStatisticsRequest> sortSampleStatisticsRequestList = sampleStatisticsRequestList.stream().sorted(Comparator.comparing(PushSampleStatisticsInfoVo.SampleStatisticsRequest::getSampleCreateDate)
                .thenComparing(PushSampleStatisticsInfoVo.SampleStatisticsRequest::getBarcode)
                .thenComparing(PushSampleStatisticsInfoVo.SampleStatisticsRequest::getTestItemCode))
                .collect(Collectors.toList());
        Long indexNum = 0L;
        for (PushSampleStatisticsInfoVo.SampleStatisticsRequest sampleStatisticsRequest : sortSampleStatisticsRequestList) {
            sampleStatisticsRequest.setStatisticsId(getStatisticsSampleId(indexNum++, dto.getUpdateDateBegin(),hspOrgSendDoctorStatisticsResponseDto.getOrgId()));
        }

        pushSampleStatisticsInfoVo.setSampleStatisticsRequests(sortSampleStatisticsRequestList);

        // 推送数据到业务中台 -- 每次推送5000条
        String deleteHashKey = UUID.randomUUID().toString();
        for (List<PushSampleStatisticsInfoVo.SampleStatisticsRequest> sampleStatisticsRequests : ListUtils.partition(pushSampleStatisticsInfoVo.getSampleStatisticsRequests(), splitCount)) {
            if (CollectionUtils.isEmpty(sampleStatisticsRequests)) {
                break;
            }
            PushSampleStatisticsInfoVo tempVo = new PushSampleStatisticsInfoVo();
            tempVo.setSampleStatisticsRequests(sampleStatisticsRequests);
            tempVo.setDeleteCreateTimeBegin(dto.getUpdateDateBegin());
            tempVo.setDeleteCreateTimeEnd(dto.getUpdateDateEnd());
            tempVo.setDeleteHashKey(deleteHashKey);

            int times = 0;
            boolean successFlag = false;
            do {
                times++;
                successFlag = false;

                try {
                    pushSampleStatisticsToBusinessCenter(tempVo);
                    successFlag = true;
                } catch (Exception e) {
                    log.error("推送样本时出现异常，重新推送当前批次样本，异常信息：", e);
                }
            } while (times < pushCount && BooleanUtils.isFalse(successFlag));


            if (BooleanUtils.isFalse(successFlag)){
                throw new RuntimeException("推送样本时出现异常，终止本次推送样本操作！" );
            }

            try {
                Thread.sleep(splitCount * 1L);
            } catch (InterruptedException e) {
                log.error("推送样本休眠时出现异常，但不会影响继续推送统计样本，异常信息：",e);
            }

            log.info("推送样本休眠结束，本次推送样本数量：{}，开始推送下一批统计样本！！", sampleStatisticsRequests.size());
        }

        // 样本统计信息推送成功 存储执行成功标识key
        stringRedisTemplate.opsForValue().set(getPushStatisticsCacheKey(new Date()), StringUtils.EMPTY , PUSH_SAMPLE_STATISTICS_EXPIRE_TIME_, TimeUnit.SECONDS);

        log.info("统计样本推送成功,推送的检验项目数量：{}条！",pushSampleStatisticsInfoVo.getSampleStatisticsRequests().size());
        return pushSampleStatisticsInfoVo;

    }



    public List<BaseSampleEsModelDto> selectBaseSampleByApplySampleId(Long applySampleId) {
        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.pageNo(NumberUtils.INTEGER_ONE).pageSize(Integer.MAX_VALUE);

        // 申请单样本ID
        builder.applySampleIds(Set.of(applySampleId));

        return elasticSearchSampleService.selectSamples(builder.build());
    }

    public SampleStatisticsVo buildSampleGroupResult(List<BaseSampleEsModelDto> baseSampleEsModelDtos, List<SampleStatisticsQueryVo.Sort> sortList) {
        SampleStatisticsVo statisticsVo = new SampleStatisticsVo();
        if (CollectionUtils.isEmpty(baseSampleEsModelDtos)) {
            return statisticsVo;
        }

        final List<SelectSampleDto> reportListDtoList =
                baseSampleEsModelDtos.stream()
                        .map(obj -> JSON.parseObject(JSON.toJSONString(obj), SelectSampleDto.class))
                        .collect(Collectors.toList());

        List<Long> sampleIds = reportListDtoList.stream().map(SelectSampleDto::getApplySampleId).distinct().collect(Collectors.toList());
        Map<Long, List<SampleFlowDto>> sampleFlowsMap = new LinkedHashMap<>();

        for (List<Long> ids : ListUtils.partition(sampleIds, 500)) {
            sampleFlowsMap.putAll(sampleFlowService.selectWithOutContentByApplySampleIdsAsMap(ids));
        }

        int total = 0;
        int auditCnt = 0;
        int unauditCnt = 0;
        ConcurrentHashMap<Long, SampleGroupStatisticsVo> statisticsMap =
                new ConcurrentHashMap<>(baseSampleEsModelDtos.size() >> 1);
        for (SelectSampleDto sampleDto : reportListDtoList) {
            Long hspOrgId = sampleDto.getHspOrgId();
            String hspOrgName = sampleDto.getHspOrgName();

            SampleGroupStatisticsVo sampleGroupStatisticsVo =
                    statisticsMap.computeIfAbsent(
                            hspOrgId,
                            k -> new SampleGroupStatisticsVo(
                                    hspOrgId,
                                    hspOrgName,
                                    0,
                                    0,
                                    0,
                                    new ArrayList<>()));

            if (Objects.equals(sampleDto.getSampleStatus(), SampleStatusEnum.AUDIT.getCode())) {
                sampleGroupStatisticsVo.setAuditCount(sampleGroupStatisticsVo.getAuditCount() + 1);
                auditCnt++;
            } else {
                sampleGroupStatisticsVo.setUnauditCount(sampleGroupStatisticsVo.getUnauditCount() + 1);
                unauditCnt++;
            }
            sampleGroupStatisticsVo.setTotalCount(sampleGroupStatisticsVo.getTotalCount() + 1);
            total++;

            SampleGroupStatisticsDetailVo statisticsDetailVo = new SampleGroupStatisticsDetailVo();
            BeanUtils.copyProperties(sampleDto, statisticsDetailVo);

            // 当前条码环节
            List<SampleFlowDto> sampleFlowDtos = sampleFlowsMap.get(sampleDto.getApplySampleId());
            if (CollectionUtils.isNotEmpty(sampleFlowDtos)) {
                sampleFlowDtos.sort(Comparator.comparing(SampleFlowDto::getSampleFlowId).reversed());
                statisticsDetailVo.setBarcodeLink(sampleFlowDtos.get(0).getOperateName());
            }

            statisticsDetailVo.setStatusText(SampleStatusEnum.getStatusByCode(sampleDto.getSampleStatus()).getDesc());
            statisticsDetailVo.setReportDate(sampleDto.getFinalCheckDate());
            statisticsDetailVo.setApplyType(sampleDto.getApplyTypeName());
            statisticsDetailVo.setTwoCheckerId(sampleDto.getFinalCheckerId());
            statisticsDetailVo.setTwoCheckerName(sampleDto.getFinalCheckerName());
            statisticsDetailVo.setIsTwoPick(sampleDto.getIsTwoPick());
            // 清除数据控默认生成的时间
            eraserDefaultDbDate(statisticsDetailVo);

            sampleGroupStatisticsVo.getSamples().add(statisticsDetailVo);
        }

        ArrayList<SampleGroupStatisticsVo> groupStatisticsVos = new ArrayList<>(statisticsMap.values());

        groupStatisticsVos.sort(Comparator.comparing(SampleGroupStatisticsVo::getHspOrgId));
        for (SampleGroupStatisticsVo groupStatisticsVo : groupStatisticsVos) {
            // 报告时间正序 报告时间为 null 放置最后
            groupStatisticsVo.getSamples()
                    .sort(Comparator.comparing(
                            SampleGroupStatisticsDetailVo::getReportDate,
                            Comparator.nullsLast(Comparator.naturalOrder())
                    ));
            if (CollectionUtils.isNotEmpty(sortList)) {
                AtomicReference<Comparator<SampleGroupStatisticsDetailVo>> comparator = new AtomicReference<>((e1, e2) -> 0);

                sortList.forEach(e-> comparator.set(sortSamples(comparator, e)));
                groupStatisticsVo.getSamples().sort(comparator.get());
            }
        }

        statisticsVo.setTotal(total).setAuditCount(auditCnt).setUnauditCount(unauditCnt);
        statisticsVo.setList(groupStatisticsVos);
        return statisticsVo;
    }

    public Comparator<SampleGroupStatisticsDetailVo> sortSamples(AtomicReference<Comparator<SampleGroupStatisticsDetailVo>> comparator
            , SampleStatisticsQueryVo.Sort e) {
        Comparator<SampleGroupStatisticsDetailVo> comparing = Comparator.comparing(a -> {
            switch (e.getFiledName()) {
                case "barcode":
                    return (Comparable) a.getBarcode();
                case "outBarcode":
                    return (Comparable) a.getOutBarcode();
                case "patientName":
                    return (Comparable) a.getPatientName();
                case "patientSex":
                    return (Comparable) a.getPatientSex();
                case "patientAge":
                    return (Comparable) a.getPatientAge();
                case "patientVisitCard":
                    return (Comparable) a.getPatientVisitCard();
                case "patientBed":
                    return (Comparable) a.getPatientBed();
                case "signDate":
                    return (Comparable) a.getSignDate();
                case "applyType":
                    return (Comparable) a.getApplyType();
                case "barcodeLink":
                    return (Comparable) a.getBarcodeLink();
                case "sampleTypeName":
                    return (Comparable) a.getSampleTypeName();
                case "sampleStatus":
                    return (Comparable) a.getSampleStatus();
                case "groupName":
                    return (Comparable) a.getGroupName();
                case "oneCheckerName":
                    return (Comparable) a.getOneCheckerName();
                case "twoCheckerName":
                    return (Comparable) a.getTwoCheckerName();
                case "finalCheckDate":
                    return (Comparable) a.getFinalCheckDate();
                case "masterBarcode":
                    return (Comparable) a.getMasterBarcode();
                case "originalOrgName":
                    return (Comparable) a.getOriginalOrgName();
                case "dept":
                    return (Comparable) a.getDept();
                case "sendDoctorName":
                    return (Comparable) a.getSendDoctorName();
                default:
                    return (Comparable) "0";
            }
        }, Comparator.nullsLast(Comparator.naturalOrder()));
        if (Objects.equals(e.getOrder(), "Asc")) {
            return comparator.get().thenComparing(comparing);
        }
        return comparator.get().thenComparing(comparing.reversed());
    }

    public void eraserDefaultDbDate(SampleGroupStatisticsDetailVo statisticsDetailVo) {
        Date finalCheckDate = statisticsDetailVo.getFinalCheckDate();
        if (isDefaultDbDate(finalCheckDate)) {
            statisticsDetailVo.setFinalCheckDate(null);
        }
    }

    public boolean isDefaultDbDate(Date date) {
        if (Objects.isNull(date)) {
            return false;
        }

        return date.getTime() < 0;
    }

    private List<PushSampleStatisticsInfoVo.SampleStatisticsRequest> getPushSampleStatisticsInfoVo(List<BaseSampleEsModelDto> baseSampleEsModelDtos,SampleEsQuery queryDto) {
        List<PushSampleStatisticsInfoVo.SampleStatisticsRequest> sampleStatisticsRequests = new ArrayList<>();

        // 查询 终止收费的检验项目信息 不一定是审核的
        List<BaseSampleEsModelDto> collect = baseSampleEsModelDtos.stream().filter(e -> CollectionUtils.isNotEmpty(e.getTestItems()) && e.getTestItems().stream().anyMatch(p -> Objects.equals(p.getStopStatus(), StopTestStatus.STOP_TEST_CHARGE.getCode()))).collect(Collectors.toList());
        // 查询 病理检验 已经一次分拣
        List<BaseSampleEsModelDto> collect2 = baseSampleEsModelDtos.stream().filter(e -> Objects.equals(e.getIsOnePick(),YesOrNoEnum.YES.getCode()) && Objects.equals(e.getItemType(), ItemTypeEnum.PATHOLOGY.name())).collect(Collectors.toList());

        List<SampleTestItemDto> sampleTestItemDtos = financialManagementService.handleEsDataToSampleTestItemDto(baseSampleEsModelDtos, collect, collect2, new TestItemIncomeFilterDto(), false);
        log.info("检验项目标准单价处理完成，处理后的检验项目数量：{}条！", sampleTestItemDtos.size());

        // 根据唯一键分组
        Map<String, SampleTestItemDto> testItemDtoMap = sampleTestItemDtos.stream().collect(Collectors.toMap(e -> e.uniqueKey(), p -> p, (o1, o2) -> o2));

        for (BaseSampleEsModelDto baseSampleEsModelDto : baseSampleEsModelDtos) {
            if (Objects.isNull(baseSampleEsModelDto.getTestItems()) || baseSampleEsModelDto.getTestItems().isEmpty()){
                log.warn("ES样本缺少检验项目信息，申请单id:{},实验室条码号：{}", baseSampleEsModelDto.getApplySampleId(),baseSampleEsModelDto.getBarcode());
                continue;
            }

            for (BaseSampleEsModelDto.TestItem testItem : baseSampleEsModelDto.getTestItems()) {
                String uniqueKey = baseSampleEsModelDto.getHspOrgId() + "-" + baseSampleEsModelDto.getApplySampleId() + "-" + testItem.getApplySampleItemId();
                SampleTestItemDto sampleTestItemDto = testItemDtoMap.get(uniqueKey);
                if (Objects.isNull(sampleTestItemDto)){
                    log.warn("未查询到检验项目的价格金额信息，项目的唯一key[{}]，这应该是终止检验不收费的项目，不推送业务中台了！！！", uniqueKey);
                    continue;
                }

                // 检验项目数量为0的不进行推送
                if (testItem.getCount() == null || Objects.equals(testItem.getCount(),0)){
                    log.warn("检验项目数量为0，项目的唯一key[{}]，不推送业务中台了！！！", uniqueKey);
                    continue;
                }

                // 是否免单
                boolean isFree = Objects.equals(YesOrNoEnum.YES.getCode(),sampleTestItemDto.getIsFree()==null?0:sampleTestItemDto.getIsFree());
                // 是否 使用了客户特价项目
                boolean isSpecialOffer = sampleTestItemDto.isSpecialOfferFlag();
                // 折扣率
                BigDecimal discount = Objects.isNull(sampleTestItemDto.getDiscount()) ? BigDecimal.ONE : sampleTestItemDto.getDiscount();

                // 单价
                BigDecimal price = ObjectUtils.defaultIfNull(sampleTestItemDto.getPrice(), BigDecimal.ZERO);
                // 折后价格
                BigDecimal discountPrice = ObjectUtils.defaultIfNull(sampleTestItemDto.getDiscountPrice(), BigDecimal.ZERO);
                // 收费数量
                Integer count = ObjectUtils.defaultIfNull(sampleTestItemDto.getCount(), 0);

                PushSampleStatisticsInfoVo.SampleStatisticsRequest temp = new PushSampleStatisticsInfoVo.SampleStatisticsRequest();
                temp.setOrgCode(orgCode);
                temp.setOrgName(orgName);
                temp.setBarcode(baseSampleEsModelDto.getBarcode());
                temp.setOutBarcode(baseSampleEsModelDto.getOutBarcode());
                temp.setHisBarcode("");
                temp.setTestItemCode(testItem.getTestItemCode());
                temp.setTestItemName(testItem.getTestItemName());
                temp.setOutTestItemCode(testItem.getOutTestItemCode());
                temp.setOutTestItemName(testItem.getOutTestItemName());
                temp.setOriginalOrgCode(baseSampleEsModelDto.getHspOrgCode());
                temp.setOriginalOrgName(baseSampleEsModelDto.getHspOrgName());
                temp.setPatientName(baseSampleEsModelDto.getPatientName());
                temp.setPatientSex(baseSampleEsModelDto.getPatientSex());
//                temp.setPatientAge(baseSampleEsModelDto.getPatientAge()==null||baseSampleEsModelDto.getPatientAge()==0?baseSampleEsModelDto.getPatientSubage():baseSampleEsModelDto.getPatientAge());
//                temp.setAgeUnit(baseSampleEsModelDto.getPatientAge()==null||baseSampleEsModelDto.getPatientAge()==0?baseSampleEsModelDto.getPatientSubageUnit():"岁");
//                temp.setPatientAge(baseSampleEsModelDto.getPatientAge()==null?0:baseSampleEsModelDto.getPatientAge());
//                temp.setAgeUnit("岁"+(baseSampleEsModelDto.getPatientSubage()==null?"":baseSampleEsModelDto.getPatientSubage().toString())+ StringUtils.defaultString(baseSampleEsModelDto.getPatientSubageUnit(),""));
                temp.setPatientAge(null);
                temp.setAgeUnit(PatientAges.toText(baseSampleEsModelDto));
                temp.setPatientBirthday(baseSampleEsModelDto.getPatientBirthday());
                temp.setPatientRegistrationNo(baseSampleEsModelDto.getPatientVisitCard());
                temp.setTreatmentCard("");
                temp.setTreatmentType(baseSampleEsModelDto.getApplyTypeName());
                temp.setBedNo(baseSampleEsModelDto.getPatientBed());
                temp.setDept(baseSampleEsModelDto.getDept());
                temp.setSampleCreateDate(baseSampleEsModelDto.getCreateDate());
                temp.setSendTime(baseSampleEsModelDto.getApplyDate());
                temp.setSendDoctorCode(baseSampleEsModelDto.getSendDoctorCode());
                temp.setSendDoctorName(baseSampleEsModelDto.getSendDoctorName());
                temp.setTestTime(baseSampleEsModelDto.getTestDate());
                temp.setTestUserCode(String.valueOf(baseSampleEsModelDto.getTesterId()));
                temp.setTestUserName(baseSampleEsModelDto.getTesterName());
                temp.setAuditTime(baseSampleEsModelDto.getFinalCheckDate());
                temp.setAuditUserCode(String.valueOf(baseSampleEsModelDto.getFinalCheckerId()));
                temp.setAuditUserName(baseSampleEsModelDto.getFinalCheckerName());
                temp.setItemCount(testItem.getCount());
                temp.setStandardPrice(price);
                temp.setTotalAmount(price.multiply(BigDecimal.valueOf(count)).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                temp.setSettlementAmount((isFree ? BigDecimal.ZERO : price.multiply(BigDecimal.valueOf(count)).multiply(discount))
                        .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                temp.setSettlementTypeCode("");
                temp.setSettlementTypeName("");
                temp.setSettlementRemark("");
                temp.setDiscount(discount.multiply(BigDecimal.valueOf(100)).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString() + "%");
                temp.setOrgType(1);
                temp.setIsSplit(baseSampleEsModelDto.getIsSplitBlood());
                temp.setApplySampleId(String.valueOf(baseSampleEsModelDto.getApplySampleId()));
                sampleStatisticsRequests.add(temp);
            }
        }


        return sampleStatisticsRequests;
    }

    // 推送样本统计信息到业务中心
    private void pushSampleStatisticsToBusinessCenter(PushSampleStatisticsInfoVo pushSampleStatisticsInfoVo) {

        // 请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Charset", "UTF-8"); // 设置字符集为UTF-8

        ResponseEntity<Response> responseEntity = restTemplate.postForEntity(businessCenterUrl, new HttpEntity<>(JSONObject.toJSONString(pushSampleStatisticsInfoVo), headers), Response.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK){
            log.error("统计样本推送失败！！！HTTP响应信息：{}", JSONObject.toJSONString(responseEntity));
            throw new RuntimeException("统计样本推送失败,HTTP响应信息：[ " + JSONObject.toJSONString(responseEntity) + " ]");
        }

        Response responseEntityBody = responseEntity.getBody();
        if (!responseEntityBody.isSuccess()){
            log.error("统计样本推送失败！！！推送响应结果：{}", JSONObject.toJSONString(responseEntityBody));
            throw new RuntimeException("统计样本推送失败,推送响应结果：[ " + JSONObject.toJSONString(responseEntityBody) + " ]");
        }

        log.info("统计样本推送成功！！！");
    }


    private HspOrgBySendDoctorService getHspOrgBySendDoctorService(){
        log.info("送检医生统计 version： {}", bySendDoctorVersion);

        final HspOrgBySendDoctorService hspOrgBySendDoctorService = HSPORG_BY_SEND_DOCTOR_SERVICE_MAP.get(bySendDoctorVersion);
        if(Objects.isNull(hspOrgBySendDoctorService)){
            throw new IllegalArgumentException("NACOS 配置错误");
        }
        return hspOrgBySendDoctorService;
    }


    // 封装转换推送数据
    private List<PushSampleStatisticsInfoVo.SampleStatisticsRequest> convertPushSampleStatisticsInfoVo(HspOrgSendDoctorStatisticsResponseDto hspOrgSendDoctorStatisticsResponseDto) {

        List<HspOrgSendDoctorStatisticsItemDto> collect = hspOrgSendDoctorStatisticsResponseDto.getOrgList().stream().flatMap(e -> e.getItemList().parallelStream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)){
            log.warn("样本统计数据全部为空！！！");
            return Collections.emptyList();
        }


        List<PushSampleStatisticsInfoVo.SampleStatisticsRequest> sampleStatisticsRequestList = new ArrayList<>();
        for (HspOrgSendDoctorStatisticsItemDto itemDto : collect) {
            PushSampleStatisticsInfoVo.SampleStatisticsRequest temp = new PushSampleStatisticsInfoVo.SampleStatisticsRequest();
            temp.setOrgCode(orgCode);
            temp.setOrgName(orgName);
            temp.setBarcode(itemDto.getBarcode());
            temp.setOutBarcode(itemDto.getOutBarcode());
            temp.setHisBarcode("");
            temp.setTestItemCode(itemDto.getTestItemCode());
            temp.setTestItemName(itemDto.getTestItem());
            temp.setOutTestItemCode(itemDto.getOutTestItemCode());
            temp.setOutTestItemName(itemDto.getOutTestItemName());
            temp.setOriginalOrgCode(itemDto.getHspOrgCode());
            temp.setOriginalOrgName(itemDto.getHspOrgName());
            temp.setPatientName(itemDto.getPatientName());
            temp.setPatientSex(SexEnum.getByDesc(itemDto.getPatientSex()).getCode());
//                temp.setPatientAge(itemDto.getPatientAge()==null||itemDto.getPatientAge()==0?itemDto.getPatientSubage():itemDto.getPatientAge());
//                temp.setAgeUnit(itemDto.getPatientAge()==null||itemDto.getPatientAge()==0?itemDto.getPatientSubageUnit():"岁");
//                temp.setPatientAge(itemDto.getPatientAge()==null?0:itemDto.getPatientAge());
//                temp.setAgeUnit("岁"+(itemDto.getPatientSubage()==null?"":itemDto.getPatientSubage().toString())+ StringUtils.defaultString(itemDto.getPatientSubageUnit(),""));
            temp.setPatientAge(null);
            temp.setAgeUnit(itemDto.getPatientAge());
            temp.setPatientBirthday(itemDto.getPatientBirthday());
            temp.setPatientRegistrationNo(itemDto.getPatientVisitCard());
            temp.setTreatmentCard("");
            temp.setTreatmentType(itemDto.getApplyTypeName());
            temp.setBedNo(itemDto.getPatientBed());
            temp.setDept(itemDto.getDept());
            temp.setSampleCreateDate(itemDto.getCreateDate());
            temp.setSendTime(itemDto.getApplyDate());
            temp.setSendDoctorCode(itemDto.getSendDoctorCode());
            temp.setSendDoctorName(itemDto.getSendDoctorName());
            temp.setTestTime(itemDto.getTestDate());
            temp.setTestUserCode(String.valueOf(itemDto.getTesterId()));
            temp.setTestUserName(itemDto.getTesterName());
            temp.setAuditTime(itemDto.getFinalCheckDateTime());
            temp.setAuditUserCode(String.valueOf(itemDto.getFinalCheckerId()));
            temp.setAuditUserName(itemDto.getFinalCheckerName());
            temp.setItemCount(itemDto.getCount());
            temp.setStandardPrice(itemDto.getFeePrice());
            temp.setTotalAmount(itemDto.getTotalFeePrice());
            temp.setSettlementAmount((itemDto.getPayAmount()));
            temp.setSettlementTypeCode("");
            temp.setSettlementTypeName("");
            temp.setSettlementRemark("");
            temp.setDiscount(itemDto.getDiscount());
            temp.setOrgType(1);
            temp.setIsSplit(itemDto.getIsSplitBlood());
            temp.setApplySampleId(String.valueOf(itemDto.getApplySampleId()));
            temp.setHisSerialNo(itemDto.getHisSerialNo());

            sampleStatisticsRequestList.add(temp);
        }

        return sampleStatisticsRequestList;
    }


    // 获取推送结果标识缓存key
    private String getPushStatisticsCacheKey(Date now){
        String format = new SimpleDateFormat("yyyyMMdd").format(now);
        return redisPrefix.getBasePrefix() + orgCode + ":" + format + ":" + PUSH_SAMPLE_STATISTICS_INFO_KEY;
    }

    // 获取样本id
    private String getStatisticsSampleId(Long number,Date now,Long orgId) {
        String format = new SimpleDateFormat("yyyyMM").format(now);
        return orgId + format + String.format("%08d", number);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 送检机构医生查询
        final Map<String, HspOrgBySendDoctorService> senDoctorServiceList = applicationContext.getBeansOfType(HspOrgBySendDoctorService.class);
        senDoctorServiceList.values().forEach(bean -> {
            final String beanName = bean.version();
            HSPORG_BY_SEND_DOCTOR_SERVICE_MAP.put(beanName, bean);
        });

        // 销售项目统计查询
        final Map<String, IncomeSummaryService> incomeSummaryServiceList = applicationContext.getBeansOfType(IncomeSummaryService.class);
        incomeSummaryServiceList.values().forEach(bean -> {
            final String beanName = bean.version();
            INCOME_SUMMARY_SERVICE_MAP.put(beanName, bean);
        });

    }

}
