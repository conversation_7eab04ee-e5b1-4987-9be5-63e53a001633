package com.labway.lims.statistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.WaitingOnePickApplySampleDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.service.GroupUnfinishedStatisticsService;
import com.labway.lims.statistics.vo.GroupUnfinishedStatisticsApplySampleTestItemVo;
import com.labway.lims.statistics.vo.GroupUnfinishedStatisticsApplySampleVo;
import com.labway.lims.statistics.vo.GroupUnfinishedStatisticsRequestVo;
import com.labway.lims.statistics.vo.GroupUnfinishedStatisticsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <pre>
 * GroupUnfinishedStatisticsServiceImpl
 * 专业组未完成工作量统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/7/5 11:12
 */
@Service
public class GroupUnfinishedStatisticsServiceImpl implements GroupUnfinishedStatisticsService {
    @DubboReference
    private RackLogicService rackLogicService;
    @DubboReference
    private RackLogicSpaceService rackLogicSpaceService;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    /**
     * 待接收列表
     * 1. 未一次分拣的
     * 2. 一次分拣未下架的
     */
    @Override
    public GroupUnfinishedStatisticsVo waitReceive(GroupUnfinishedStatisticsRequestVo requestVo) {
        // 查询一次分拣中的逻辑试管架
        final List<Integer> rackLogicPositions = Lists.newArrayList(RackLogicPositionEnum.ONE_PICKING.getCode());

        // 获取到所有没有 一次分拣 的样本
        final List<WaitingOnePickApplySampleDto> samples =
                applySampleService.selectWaitingOnePickSamples(requestVo.getStartDate(), requestVo.getEndDate());
        Set<Long> waitingOnePickApplySampleIds = samples.stream()
                .map(WaitingOnePickApplySampleDto::getApplySampleId).collect(Collectors.toSet());
        requestVo.setWaitingOnePickApplySampleIds(waitingOnePickApplySampleIds);

        return onePickStepStatustics(requestVo, rackLogicPositions);
    }

    /**
     * 待交接列表
     * 1. 完成一次分拣下架，未分拣后交接的
     * 2. 完成一次分拣下架，分血组接收，但未分血的
     * 2. 完成一次分拣下架，分血组接收，分血，但未分血后交接的
     */
    @Override
    public GroupUnfinishedStatisticsVo waitHandover(GroupUnfinishedStatisticsRequestVo requestVo) {
        // 查询 一次分拣下架，分血后下架状态的数据
        ArrayList<Integer> rackLogicPositions = Lists.newArrayList(
                RackLogicPositionEnum.ONE_PICKED.getCode(),
                RackLogicPositionEnum.SPLITTING_BLOOD.getCode(),
                RackLogicPositionEnum.SPLIT_BLOOD.getCode());
        return onePickStepStatustics(requestVo, rackLogicPositions);
    }

    /**
     * 待二次分拣列表
     * 1. 一次分拣后交接，未二次分拣的
     * 2. 分血后交接，但未二次分拣的
     */
    @Override
    public GroupUnfinishedStatisticsVo waitTwoPick(GroupUnfinishedStatisticsRequestVo requestVo) {
        Function<SampleEsQuery, SampleEsQuery> statusSetter = sampleEsQuery -> {
            sampleEsQuery.setIsOnePick(YesOrNoEnum.YES.getCode());
            sampleEsQuery.setIsTwoPick(YesOrNoEnum.NO.getCode());
            sampleEsQuery.setIsAudit(YesOrNoEnum.NO.getCode());
            return sampleEsQuery;
        };
        // 已分血，但是未交接的样本，需要过滤掉
        Set<Long> splitBloodApplySampleIds = selectApplySampleIdsByRackLogicPosition(
                Lists.newArrayList(
                        RackLogicPositionEnum.ONE_PICKED.getCode(),
                        RackLogicPositionEnum.SPLITTING_BLOOD.getCode(),
                        RackLogicPositionEnum.SPLIT_BLOOD.getCode()));
        Predicate<GroupUnfinishedStatisticsApplySampleVo> filterPredicate =
                e -> !splitBloodApplySampleIds.contains(e.getApplySampleId());
        return twoPickStepStatustics(requestVo, statusSetter, filterPredicate);
    }

    /**
     * 待审核列表
     */
    @Override
    public GroupUnfinishedStatisticsVo waitAudit(GroupUnfinishedStatisticsRequestVo requestVo) {
        Function<SampleEsQuery, SampleEsQuery> statusSetter = sampleEsQuery -> {
            sampleEsQuery.setIsOnePick(YesOrNoEnum.YES.getCode());
            sampleEsQuery.setIsTwoPick(YesOrNoEnum.YES.getCode());
            sampleEsQuery.setIsAudit(YesOrNoEnum.NO.getCode());
            return sampleEsQuery;
        };
        return twoPickStepStatustics(requestVo, statusSetter, e -> true);
    }

    private GroupUnfinishedStatisticsVo onePickStepStatustics(
            GroupUnfinishedStatisticsRequestVo requestVo, List<Integer> rackLogicPositions) {
        GroupUnfinishedStatisticsVo statisticsVo = new GroupUnfinishedStatisticsVo();

        // 根据逻辑试管架状态 获取关联的申请单样本id
        final Set<Long> applySampleIds = selectApplySampleIdsByRackLogicPosition(rackLogicPositions);
        // 加上 没有 一次分拣 的样本
        if (CollectionUtils.isNotEmpty(requestVo.getWaitingOnePickApplySampleIds())) {
            applySampleIds.addAll(requestVo.getWaitingOnePickApplySampleIds());
        }

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return statisticsVo;
        }

        final LoginUserHandler.User user = LoginUserHandler.get();
        final SampleEsQuery sampleEsQuery = new SampleEsQuery();
        sampleEsQuery.setApplySampleIds(applySampleIds);
        sampleEsQuery.setStartCreateDate(requestVo.getStartDate());
        sampleEsQuery.setEndCreateDate(requestVo.getEndDate());
        // 送检机构 - 支持多选
        if (CollectionUtils.isNotEmpty(requestVo.getHspOrgCodes())) {
            sampleEsQuery.setHspOrgCodes(new HashSet<>(requestVo.getHspOrgCodes()));
        }
        // 专业组 - 单选
        if (CollectionUtils.isNotEmpty(requestVo.getGroupIds())) {
            sampleEsQuery.setTestItemGroupIds(new HashSet<>(requestVo.getGroupIds()));
        } else {
            sampleEsQuery.setTestItemGroupIds(Sets.newHashSet(user.getGroupId()));
        }
        // 当前专业组
        Long currentGroupId = sampleEsQuery.getTestItemGroupIds().iterator().next();
        // 就诊类型
        if (CollectionUtils.isNotEmpty(requestVo.getApplyTypes())) {
            sampleEsQuery.setApplyTypes(new HashSet<>(requestVo.getApplyTypes()));
        }

        final List<GroupUnfinishedStatisticsApplySampleVo> statisticsApplySampleVos =
                selectUnfinishedStatisticsApplySampleVos(sampleEsQuery, currentGroupId);

        statisticsVo.setGroupUnfinishedApplySamples(statisticsApplySampleVos);
        statisticsVo.setTotalCount(statisticsApplySampleVos.size());

        return statisticsVo;
    }

    private GroupUnfinishedStatisticsVo twoPickStepStatustics(
            GroupUnfinishedStatisticsRequestVo requestVo,
            Function<SampleEsQuery, SampleEsQuery> statusSetter,
            Predicate<GroupUnfinishedStatisticsApplySampleVo> filterPredicate) {
        GroupUnfinishedStatisticsVo statisticsVo = new GroupUnfinishedStatisticsVo();

        final LoginUserHandler.User user = LoginUserHandler.get();
        final SampleEsQuery sampleEsQuery = new SampleEsQuery();
        sampleEsQuery.setStartCreateDate(requestVo.getStartDate());
        sampleEsQuery.setEndCreateDate(requestVo.getEndDate());
        // 送检机构 - 支持多选
        if (CollectionUtils.isNotEmpty(requestVo.getHspOrgCodes())) {
            sampleEsQuery.setHspOrgCodes(new HashSet<>(requestVo.getHspOrgCodes()));
        }
        // 专业组 - 单选
        if (CollectionUtils.isNotEmpty(requestVo.getGroupIds())) {
            sampleEsQuery.setGroupIds(new HashSet<>(requestVo.getGroupIds()));
        } else {
            sampleEsQuery.setGroupIds(Sets.newHashSet(user.getGroupId()));
        }
        // 当前专业组
        Long currentGroupId = sampleEsQuery.getGroupIds().iterator().next();
        // 就诊类型
        if (CollectionUtils.isNotEmpty(requestVo.getApplyTypes())) {
            sampleEsQuery.setApplyTypes(new HashSet<>(requestVo.getApplyTypes()));
        }
        // 设置状态
        statusSetter.apply(sampleEsQuery);

        final List<GroupUnfinishedStatisticsApplySampleVo> statisticsApplySampleVos =
                selectUnfinishedStatisticsApplySampleVos(sampleEsQuery, currentGroupId);

        // 执行过滤条件
        final List<GroupUnfinishedStatisticsApplySampleVo> applySampleVos =
                statisticsApplySampleVos.stream()
                        .filter(filterPredicate::evaluate)
                        .collect(Collectors.toList());

        statisticsVo.setGroupUnfinishedApplySamples(applySampleVos);
        statisticsVo.setTotalCount(applySampleVos.size());

        return statisticsVo;
    }

    /**
     * 根据逻辑试管架状态 查询关联的申请单样本id
     */
    private Set<Long> selectApplySampleIdsByRackLogicPosition(List<Integer> rackLogicPositions) {
        // 根据 逻辑试管架状态 获取逻辑试管架
        final List<RackLogicDto> rackLogics = rackLogicService.selectByPositions(rackLogicPositions);
        if (CollectionUtils.isEmpty(rackLogics)) {
            return Sets.newHashSet();
        }

        // 根据逻辑试管架查询试管架空间
        final List<RackLogicSpaceDto> rackLogicSpaces = rackLogicSpaceService
                .selectByRackLogicIds(rackLogics.stream().map(RackLogicDto::getRackLogicId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(rackLogicSpaces)) {
            return Sets.newHashSet();
        }

        return rackLogicSpaces.stream().map(RackLogicSpaceDto::getApplySampleId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
    }

    /**
     * 根据条件 查询专业组未完成工作
     */
    private List<GroupUnfinishedStatisticsApplySampleVo> selectUnfinishedStatisticsApplySampleVos(
            SampleEsQuery sampleEsQuery, Long currentGroupId) {
        final List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(sampleEsQuery);

        final List<GroupUnfinishedStatisticsApplySampleVo> statisticsApplySampleVos = baseSampleEsModelDtos
                .stream()
                .filter(e -> !Objects.equals(e.getSampleStatus(), SampleStatusEnum.STOP_TEST.getCode())) // 如果样本终止，则过滤掉
                .map(e -> {
                    GroupUnfinishedStatisticsApplySampleVo applySampleVo =
                            JSON.parseObject(JSON.toJSONString(e), GroupUnfinishedStatisticsApplySampleVo.class);

                    if (StringUtils.isBlank(applySampleVo.getSignName())) {
                        applySampleVo.setSignName(e.getCreatorName());
                    }

                    List<BaseSampleEsModelDto.TestItem> testItems = e.getTestItems();
                    if (CollectionUtils.isNotEmpty(testItems)) {
                        testItems.stream()
                                // 如果项目终止，则过滤掉
                                .filter(item -> Objects.equals(item.getStopStatus(), StopTestStatus.NO_STOP_TEST.getCode()))
                                .forEach(testItem -> {
                                    GroupUnfinishedStatisticsApplySampleTestItemVo applySampleTestItemVo =
                                            new GroupUnfinishedStatisticsApplySampleTestItemVo();
                                    BeanUtils.copyProperties(testItem, applySampleTestItemVo);

                                    // 判断是否是当前专业组项目
                                    if (Objects.equals(currentGroupId, testItem.getGroupId())) {
                                        applySampleVo.getCurrentGroupTestItems().add(applySampleTestItemVo);
                                    } else {
                                        applySampleVo.getOtherGroupTestItems().add(applySampleTestItemVo);
                                    }
                                });
                    }

                    return applySampleVo;
                })
                .filter(e -> CollectionUtils.isNotEmpty(e.getCurrentGroupTestItems())) // 如果该样本当前专业组下没有项目（项目终止检验），则过滤掉
                .collect(Collectors.toList());

        // 按照创建时间顺序排序
        statisticsApplySampleVos.sort(Comparator.comparing(
                GroupUnfinishedStatisticsApplySampleVo::getCreateDate, Comparator.nullsLast(Comparator.naturalOrder())));

        return statisticsApplySampleVos;
    }

}
