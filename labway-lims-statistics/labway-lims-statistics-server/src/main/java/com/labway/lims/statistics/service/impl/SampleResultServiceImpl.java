package com.labway.lims.statistics.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Sets;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DateTypeEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.stream.StreamUtils;
import com.labway.lims.api.utils.LabwayNumberUtils;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.base.api.enums.RelationalOperatorEnum;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.service.SampleResultService;
import com.labway.lims.statistics.vo.InspectionResultQueryVo;
import com.labway.lims.statistics.vo.InspectionResultSummaryVo;
import com.labway.lims.statistics.vo.InspectionResultVo;
import com.labway.lims.statistics.vo.PatientInfoVo;
import com.labway.lims.statistics.vo.ReportInfoVo;
import com.labway.lims.statistics.vo.SampleInspectionResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <pre>
 * SampleResultServiceImpl
 * 检验结果信息查询
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/13 16:29
 */
@Slf4j
@Service
@RefreshScope
public class SampleResultServiceImpl implements SampleResultService {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    private EsConfig esConfig;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @Override
    public InspectionResultVo inspectionResultsNew(InspectionResultQueryVo vo) {
        if (Objects.isNull(vo.getEndDate()) || Objects.isNull(vo.getStartDate())) {
            throw new IllegalArgumentException("日期错误");
        }

        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }

        List<Object> searchAfter = null;
        if (Objects.nonNull(vo.getSearchAfter())) {
            searchAfter = Collections.singletonList(vo.getSearchAfter());
        }

        final SampleEsQuery query = new SampleEsQuery();
        query.setIsAudit(YesOrNoEnum.YES.getCode());
        query.setItemTypes(Sets.newHashSet(ItemTypeEnum.ROUTINE.name(), ItemTypeEnum.OUTSOURCING.name()));
        query.setPageNo(NumberUtils.INTEGER_ONE);
        query.setPageSize(Math.min(Objects.requireNonNullElse(vo.getSize(), esConfig.getPageSize()), esConfig.getMaxPageSize()));
        buildQueryParam(query, vo);

        ScrollPage<BaseSampleEsModelDto> scrollPage = null;
        final List<BaseSampleEsModelDto> baseSampleEsModelDtos;
        // 查询全部数据
        if (vo.isQueryAll()) {
            baseSampleEsModelDtos = selectSamples(query, vo);
        } else {
            scrollPage = elasticSearchSampleService.searchAfter(searchAfter, query);
            baseSampleEsModelDtos = scrollPage.getData();
        }

        final InspectionResultVo inspectionResult = new InspectionResultVo();

        List<SampleInspectionResultVo> inspectionResults = convertBaseSample2SampleInspectionResult(baseSampleEsModelDtos, IdUtil.simpleUUID());
        //  过滤掉不在查询条件内的报告项目
        if (CollectionUtils.isNotEmpty(vo.getReportItemCodes())) {
            inspectionResults = inspectionResults.stream().filter(r -> vo.getReportItemCodes().contains(r.getReportItemCode())).collect(Collectors.toList());
        }

        // 根据查询条件过滤结果
        filterInspectionResultRecord(vo, inspectionResults);
        inspectionResult.setResults(inspectionResults);

        // 滚动分页参数
        if (Objects.nonNull(scrollPage) && CollectionUtils.isNotEmpty(scrollPage.getSearchAfter())) {
            inspectionResult.setSearchAfter(scrollPage.getSearchAfter().iterator().next());
        }

        // 人员信息统计
        final LinkedList<PatientInfoVo> patientInfos = new LinkedList<>();
        final Map<Long, List<SampleInspectionResultVo>> results = inspectionResults.stream()
                .collect(Collectors.groupingBy(SampleInspectionResultVo::getApplySampleId));
        for (List<SampleInspectionResultVo> k : results.values()) {
            final SampleInspectionResultVo e = k.iterator().next();
            final PatientInfoVo patientInfo = new PatientInfoVo();
            patientInfo.setPatientName(e.getPatientName());
            patientInfo.setPatientSex(e.getPatientSex());
            patientInfo.setPatientAge(e.getPatientAge());
            patientInfo.setPatientSubage(e.getPatientSubage());
            patientInfo.setPatientSubageUnit(e.getPatientSubageUnit());
            patientInfo.setPatientCard(e.getPatientCard());
            patientInfo.setPatientMobile(e.getPatientMobile());
            patientInfo.setHspOrgCode(e.getHspOrgCode());
            patientInfo.setHspOrgName(e.getHspOrgName());
            patientInfo.setApplyType(e.getApplyType());
            patientInfo.setTestItemName(k.stream().map(SampleInspectionResultVo::getTestItemName)
                    .distinct().collect(Collectors.joining(",")));

            patientInfo.setId(UUID.fastUUID().toString());

            patientInfos.add(patientInfo);
        }

        inspectionResult.setPatientInfos(patientInfos);

        return inspectionResult;
    }

    @Override
    public InspectionResultSummaryVo inspectionResultSummary(InspectionResultQueryVo vo) {

        if (Objects.isNull(vo.getEndDate()) || Objects.isNull(vo.getStartDate())) {
            throw new IllegalArgumentException("日期错误");
        }

        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }

        // 先查缓存
        InspectionResultSummaryVo summaryVo = getCachedSummaryData(vo);
        if (Objects.nonNull(summaryVo)) {
            log.info("检验结果信息查询，从缓存中查到数据 [{}] 参数 [{}]", JSON.toJSONString(summaryVo), JSON.toJSONString(vo));
            return summaryVo;
        }
        summaryVo = new InspectionResultSummaryVo();

        final SampleEsQuery query = new SampleEsQuery();
        query.setIsAudit(YesOrNoEnum.YES.getCode());
        query.setItemTypes(Sets.newHashSet(ItemTypeEnum.ROUTINE.name(), ItemTypeEnum.OUTSOURCING.name()));
        query.setPageNo(NumberUtils.INTEGER_ONE);
        query.setPageSize(Math.min(Objects.requireNonNullElse(vo.getSize(), esConfig.getPageSize()), esConfig.getMaxPageSize()));
        buildQueryParam(query, vo);
        final List<BaseSampleEsModelDto> baseSampleDtos = selectSamples(query, vo);

        // 根据送检机构分组
        final Map<String, List<BaseSampleEsModelDto>> sampleMap =
                baseSampleDtos.stream().collect(Collectors.groupingBy(BaseSampleEsModelDto::getHspOrgCode));

        List<ReportInfoVo> hospitals = new LinkedList<>();

        for (Map.Entry<String, List<BaseSampleEsModelDto>> e : sampleMap.entrySet()) {
            final ReportInfoVo info = new ReportInfoVo();
            info.setHspOrgCode(e.getKey());
            info.setHspOrgName(e.getValue().iterator().next().getHspOrgName());
            final String id = IdUtil.simpleUUID();
            info.setId(id);
            List<SampleInspectionResultVo> inspectionResults = convertBaseSample2SampleInspectionResult(e.getValue(), id);
            //  过滤掉不在查询条件内的报告项目
            if (CollectionUtils.isNotEmpty(vo.getReportItemCodes())) {
                inspectionResults = inspectionResults.stream().filter(r -> vo.getReportItemCodes().contains(r.getReportItemCode())).collect(Collectors.toList());
            }
            info.setResults(inspectionResults);
            hospitals.add(info);
        }

        // 根据查询条件过滤结果
        filterResultRecord(vo, hospitals);

        AtomicInteger totalCount = new AtomicInteger(NumberUtils.INTEGER_ZERO);
        for (ReportInfoVo hospital : hospitals) {
            hospital.setSize(hospital.getResults().size());
            totalCount.addAndGet(hospital.getResults().size());
        }
        summaryVo.setTotalCount(totalCount.get());
        summaryVo.setHospitals(hospitals);

        // 人员信息统计
        AtomicInteger patientCount = new AtomicInteger(NumberUtils.INTEGER_ZERO);
        for (ReportInfoVo hospital : hospitals) {
            final Map<Long, List<SampleInspectionResultVo>> results = hospital.getResults().stream()
                    .collect(Collectors.groupingBy(SampleInspectionResultVo::getApplySampleId));
            if (CollectionUtils.isNotEmpty(results.values())) {
                patientCount.addAndGet(results.values().size());
            }
        }

        // 结果置空，统计接口不需要返回结果
        hospitals.forEach(hospital -> hospital.setResults(List.of()));

        summaryVo.setPatientCount(patientCount.get());

        addCachedSummaryData(vo, summaryVo);

        return summaryVo;

    }

    private InspectionResultSummaryVo getCachedSummaryData(InspectionResultQueryVo vo) {
        String prefix = redisPrefix.getBasePrefix() + "SampleResultSummary:";

        String key = generateUniqueKey(vo);
        if (StringUtils.isBlank(key)) {
            return null;
        }

        String summary = stringRedisTemplate.opsForValue().get(prefix + key);
        if (StringUtils.isNotBlank(summary)) {
            return JSON.parseObject(summary, InspectionResultSummaryVo.class);
        }

        return null;
    }

    private void addCachedSummaryData(InspectionResultQueryVo vo, InspectionResultSummaryVo summaryVo) {
        String prefix = redisPrefix.getBasePrefix() + "SampleResultSummary:";

        String key = generateUniqueKey(vo);
        if (StringUtils.isBlank(key)) {
            return;
        }

        stringRedisTemplate.opsForValue().set(prefix + key, JSON.toJSONString(summaryVo), 12, TimeUnit.HOURS);
    }

    private String generateUniqueKey(InspectionResultQueryVo vo) {
        // 截止时间在 当前时间之后
        if (vo.getEndDate().after(DateUtil.date())) {
            return StringPool.EMPTY;
        }
        // 时间类型
        String key = vo.getDateType();
        // 日期
        String date = DateUtil.format(vo.getStartDate(), DatePattern.PURE_DATETIME_PATTERN) + DateUtil.format(vo.getEndDate(), DatePattern.PURE_DATETIME_PATTERN);
        key += date;

        // 送检机构
        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            key += vo.getHspOrgIds().stream().sorted()
                    .map(String::valueOf).collect(Collectors.joining(StringPool.EMPTY));
        }
        // 体检单位
        if (Objects.nonNull(vo.getPhysicalCompanyId())) {
            key += key + vo.getPhysicalCompanyId();
        }
        // 专业组
        if (Objects.nonNull(vo.getGroupId())) {
            key += key + vo.getGroupId();
        }
        // 检验项目
        if (CollectionUtils.isNotEmpty(vo.getTestItemIds())) {
            key += vo.getTestItemIds().stream().sorted()
                    .map(String::valueOf).collect(Collectors.joining(StringPool.EMPTY));
        }
        // 报告项目
        if (CollectionUtils.isNotEmpty(vo.getReportItemCodes())) {
            key += vo.getReportItemCodes().stream().sorted()
                    .collect(Collectors.joining(StringPool.EMPTY));
        }
        // 姓名
        if (StringUtils.isNotBlank(vo.getPatientName())) {
            key += key + vo.getPatientName();
        }
        // 就诊类型
        if (CollectionUtils.isNotEmpty(vo.getApplyType())) {
            key += vo.getApplyType().stream().sorted()
                    .collect(Collectors.joining(StringPool.EMPTY));
        }
        // 结果匹配1
        if (StringUtils.isNotBlank(vo.getFormula1()) && StringUtils.isNotBlank(vo.getResult1())) {
            key += vo.getFormula1() + vo.getResult1();
        }
        // 结果匹配2
        if (StringUtils.isNotBlank(vo.getFormula2()) && StringUtils.isNotBlank(vo.getResult2())) {
            key += vo.getFormula2() + vo.getResult2();
        }
        key += vo.getIsExceptional();

        return key;
    }

    @Override
    public void filterResultRecord(InspectionResultQueryVo vo, List<ReportInfoVo> hospitals) {
        // 异常结果
        if (Objects.nonNull(vo.getIsExceptional())) {
            if (Objects.equals(vo.getIsExceptional(), YesOrNoEnum.YES.getCode())) {
                // 移除非异常
                for (ReportInfoVo hospital : hospitals) {
                    hospital.getResults().removeIf(f -> (Objects.equals(f.getStatus(), ResultStatusEnum.NORMAL.getCode())
                            || Objects.isNull(f.getStatus())) && StringUtils.isEmpty(f.getJudge()));
                }
            }
            // 移除删选过后结果是空的数据
            hospitals.removeIf(f -> CollectionUtils.isEmpty(f.getResults()));
        }

        // 结果大小范围过滤
        if (StringUtils.isNotBlank(vo.getFormula1()) && StringUtils.isNotBlank(vo.getResult1())) {
            for (ReportInfoVo hospital : hospitals) {
                hospital.getResults().removeIf(f -> !LabwayNumberUtils.isParsable(f.getResult()));
                hospital.getResults().removeIf(f -> !RelationalOperatorEnum.valueOfByOperator(vo.getFormula1())
                        .compare(LabwayNumberUtils.toDouble(f.getResult()), NumberUtils.toDouble(vo.getResult1())));
            }
            // 移除删选过后结果是空的数据
            hospitals.removeIf(f -> CollectionUtils.isEmpty(f.getResults()));
        }

        // 结果大小范围过滤
        if (StringUtils.isNotBlank(vo.getFormula2()) && StringUtils.isNotBlank(vo.getResult2())) {
            for (ReportInfoVo hospital : hospitals) {
                hospital.getResults().removeIf(f -> !LabwayNumberUtils.isParsable(f.getResult()));
                hospital.getResults().removeIf(f -> !RelationalOperatorEnum.valueOfByOperator(vo.getFormula2())
                        .compare(LabwayNumberUtils.toDouble(f.getResult()), NumberUtils.toDouble(vo.getResult2())));
            }
            // 移除删选过后结果是空的数据
            hospitals.removeIf(f -> CollectionUtils.isEmpty(f.getResults()));
        }

        // 检验项目 过滤
        if (CollectionUtils.isNotEmpty(vo.getTestItemIds())) {
            for (ReportInfoVo hospital : hospitals) {
                hospital.getResults().removeIf(f -> !vo.getTestItemIds().contains(f.getTestItemId()));
            }
            // 移除删选过后结果是空的数据
            hospitals.removeIf(f -> CollectionUtils.isEmpty(f.getResults()));
        }
    }

    private List<BaseSampleEsModelDto> selectSamples(final SampleEsQuery query, InspectionResultQueryVo vo) {
        // 样本创建时间
        Date startDate = vo.getStartDate();
        Date endDate = vo.getEndDate();

        List<Pair<Date, Date>> datePairs = new ArrayList<>();

        // 判断时间范围，如果时间范围大于24小时，则按24h进行拆分，分批查询
        long between = DateUtil.between(startDate, endDate, DateUnit.HOUR);
        boolean hasNext = true;Date start = startDate;
        if (between > 24) {
            do {
                Date end = DateUtil.offsetHour(start, 24);
                if (end.after(endDate)) {
                    hasNext = false;
                    end = endDate;
                }
                datePairs.add(Pair.of(DateUtil.beginOfDay(start), end));
                start = end;
            } while (hasNext);
        } else {
            datePairs.add(Pair.of(startDate, endDate));
        }

        // 根据拆分出来的时间段，分批并行查询数据
        @SuppressWarnings("all")
        CompletableFuture<List<BaseSampleEsModelDto>>[] completableFutures = datePairs.stream().map(datePair -> {
            return CompletableFuture.supplyAsync(() -> {
                SampleEsQuery sampleEsQuery = JSON.parseObject(JSON.toJSONString(query), SampleEsQuery.class);
                // 拆分设置时间参数
                if (Objects.equals(vo.getDateType(), DateTypeEnum.SEND_DATE.name())) {
                    // 1.1.0 检验结果信息查询，送检时间 取样本的创建时间
                    sampleEsQuery.setStartCreateDate(datePair.getKey());
                    sampleEsQuery.setEndCreateDate(datePair.getValue());
                } else if (Objects.equals(vo.getDateType(), DateTypeEnum.AUDIT_DATE.name())) {
                    sampleEsQuery.setStartFinalCheckDate(datePair.getKey());
                    sampleEsQuery.setEndFinalCheckDate(datePair.getValue());
                }
                return elasticSearchSampleService.selectSamples(sampleEsQuery);
            });
        }).collect(Collectors.toList()).toArray(new CompletableFuture[0]);

        try {
            CompletableFuture.allOf(completableFutures).get();
        } catch (Exception e) {
            log.error("调用查询异常", e);
            return Collections.emptyList();
        }

        // 合并并行查出来的数据，过滤掉 null 和 重复的数据
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = Arrays.stream(completableFutures)
                .flatMap(e -> {
                    try {
                        return e.get().stream();
                    } catch (Exception ex) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .filter(StreamUtils.distinctByKey(BaseSampleEsModelDto::getApplySampleId))
                .collect(Collectors.toList());
        // List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(query);

        return baseSampleEsModelDtos;
    }

    private List<SampleInspectionResultVo> convertBaseSample2SampleInspectionResult(
            List<BaseSampleEsModelDto> samples, String id) {
        final LinkedList<SampleInspectionResultVo> results = new LinkedList<>();

        for (BaseSampleEsModelDto baseSample : samples) {
            // 常规检验
            if (baseSample instanceof RoutineInspectionDto) {
                RoutineInspectionDto sample = (RoutineInspectionDto) baseSample;
                if (CollectionUtils.isEmpty(sample.getReportItems())) {
                    continue;
                }
                for (RoutineInspectionDto.RoutineReportItem item : sample.getReportItems()) {
                    final SampleInspectionResultVo result = new SampleInspectionResultVo();
                    result.setPid(id);
                    result.setId(IdUtil.simpleUUID());
                    result.setApplySampleId(baseSample.getApplySampleId());
                    result.setHspOrgCode(baseSample.getHspOrgCode());
                    result.setHspOrgName(baseSample.getHspOrgName());
                    result.setPatientName(baseSample.getPatientName());
                    result.setPatientSex(baseSample.getPatientSex());
                    result.setPatientAge(baseSample.getPatientAge());
                    result.setPatientSubage(baseSample.getPatientSubage());
                    result.setPatientSubageUnit(baseSample.getPatientSubageUnit());
                    result.setPatientCard(baseSample.getPatientCard());
                    result.setPatientMobile(baseSample.getPatientMobile());
                    result.setApplyType(baseSample.getApplyTypeName());
                    result.setBarcode(baseSample.getBarcode());
                    result.setOutBarcode(baseSample.getOutBarcode());
                    result.setTestItemId(item.getTestItemId());
                    result.setTestItemCode(item.getTestItemCode());
                    result.setTestItemName(item.getTestItemName());
                    result.setReportItemCode(item.getReportItemCode());
                    result.setReportItemName(item.getReportItemName());
                    result.setResult(item.getResult());
                    result.setUnit(item.getUnit());
                    result.setJudge(item.getJudge());
                    result.setRange(item.getRange());
                    result.setGroupId(baseSample.getGroupId());
                    result.setGroupName(baseSample.getGroupName());
                    result.setSampleTypeName(baseSample.getSampleTypeName());
                    result.setApplyDate(baseSample.getCreateDate()); // 1.1.0 检验结果信息查询，送检时间 取样本的创建时间
                    result.setTestDate(baseSample.getTestDate());
                    result.setCheckDate(baseSample.getFinalCheckDate());
                    result.setPatientVisitCard(baseSample.getPatientVisitCard());
                    result.setSendDoctorCode(baseSample.getSendDoctorCode());
                    result.setSendDoctorName(baseSample.getSendDoctorName());
                    result.setDept(baseSample.getDept());
                    result.setPatientBed(baseSample.getPatientBed());
                    result.setStatus(item.getStatus());
                    result.setPatientCard(sample.getPatientCard());
                    result.setRemark(sample.getRemark());
                    results.add(result);
                }
            }
            // 外送检验
            if (baseSample instanceof OutsourcingInspectionDto) {
                OutsourcingInspectionDto sample = (OutsourcingInspectionDto) baseSample;
                if (CollectionUtils.isEmpty(sample.getReportItems())) {
                    continue;
                }
                for (OutsourcingInspectionDto.OutsourcingReportItem item : sample.getReportItems()) {
                    final SampleInspectionResultVo result = new SampleInspectionResultVo();
                    result.setPid(id);
                    result.setId(IdUtil.simpleUUID());
                    result.setApplySampleId(sample.getApplySampleId());
                    result.setHspOrgCode(sample.getHspOrgCode());
                    result.setHspOrgName(sample.getHspOrgName());
                    result.setPatientName(sample.getPatientName());
                    result.setPatientSex(sample.getPatientSex());
                    result.setPatientAge(sample.getPatientAge());
                    result.setPatientSubage(sample.getPatientSubage());
                    result.setPatientSubageUnit(sample.getPatientSubageUnit());
                    result.setPatientCard(baseSample.getPatientCard());
                    result.setPatientMobile(baseSample.getPatientMobile());
                    result.setApplyType(sample.getApplyTypeName());
                    result.setBarcode(sample.getBarcode());
                    result.setOutBarcode(baseSample.getOutBarcode());
                    result.setTestItemId(item.getTestItemId());
                    result.setTestItemCode(item.getTestItemCode());
                    result.setTestItemName(item.getTestItemName());
                    result.setReportItemCode(item.getReportItemCode());
                    result.setReportItemName(item.getReportItemName());
                    result.setResult(item.getResult());
                    result.setUnit(item.getUnit());
                    result.setJudge(item.getJudge());
                    result.setRange(item.getRange());
                    result.setGroupId(sample.getGroupId());
                    result.setGroupName(sample.getGroupName());
                    result.setSampleTypeName(sample.getSampleTypeName());
                    result.setApplyDate(sample.getCreateDate()); // 1.1.0 检验结果信息查询，送检时间 取样本的创建时间
                    result.setTestDate(sample.getTestDate());
                    result.setCheckDate(sample.getFinalCheckDate());
                    result.setPatientVisitCard(sample.getPatientVisitCard());
                    result.setSendDoctorCode(sample.getSendDoctorCode());
                    result.setSendDoctorName(sample.getSendDoctorName());
                    result.setDept(sample.getDept());
                    result.setPatientBed(sample.getPatientBed());
                    result.setStatus(item.getStatus());
                    result.setPatientCard(sample.getPatientCard());
                    result.setRemark(sample.getRemark());
                    results.add(result);
                }
            }

        }

        return results;
    }

    private static void filterInspectionResultRecord(InspectionResultQueryVo vo, List<SampleInspectionResultVo> inspectionResults) {
        // 异常结果
        if (Objects.nonNull(vo.getIsExceptional()) && Objects.equals(vo.getIsExceptional(), YesOrNoEnum.YES.getCode())) {
            // 移除非异常
            inspectionResults.removeIf(f -> (Objects.equals(f.getStatus(), ResultStatusEnum.NORMAL.getCode())
                    || Objects.isNull(f.getStatus())) && StringUtils.isEmpty(f.getJudge()));
        }

        // 结果大小范围过滤
        if (StringUtils.isNotBlank(vo.getFormula1()) && StringUtils.isNotBlank(vo.getResult1())) {
            inspectionResults.removeIf(f -> !LabwayNumberUtils.isParsable(f.getResult()));
            inspectionResults.removeIf(f -> !RelationalOperatorEnum.valueOfByOperator(vo.getFormula1())
                    .compare(LabwayNumberUtils.toDouble(f.getResult()), NumberUtils.toDouble(vo.getResult1())));
        }

        // 结果大小范围过滤
        if (StringUtils.isNotBlank(vo.getFormula2()) && StringUtils.isNotBlank(vo.getResult2())) {
            inspectionResults.removeIf(f -> !LabwayNumberUtils.isParsable(f.getResult()));
            inspectionResults.removeIf(f -> !RelationalOperatorEnum.valueOfByOperator(vo.getFormula2())
                    .compare(LabwayNumberUtils.toDouble(f.getResult()), NumberUtils.toDouble(vo.getResult2())));
        }

        // 检验项目 过滤
        if (CollectionUtils.isNotEmpty(vo.getTestItemIds())) {
            inspectionResults.removeIf(f -> !vo.getTestItemIds().contains(f.getTestItemId()));
        }
    }

    private void buildQueryParam(SampleEsQuery query, InspectionResultQueryVo vo) {
        // 送检时间 / 审核时间
        if (Objects.equals(vo.getDateType(), DateTypeEnum.SEND_DATE.name())) {
            // 1.1.0 检验结果信息查询，送检时间 取样本的创建时间
            query.setStartCreateDate(vo.getStartDate());
            query.setEndCreateDate(vo.getEndDate());
        } else if (Objects.equals(vo.getDateType(), DateTypeEnum.AUDIT_DATE.name())) {
            query.setStartFinalCheckDate(vo.getStartDate());
            query.setEndFinalCheckDate(vo.getEndDate());
        }
        // 送检机构
        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            query.setHspOrgIds(new HashSet<>(vo.getHspOrgIds()));
        }
        // 体检单位
        if (Objects.nonNull(vo.getPhysicalCompanyId())) {
            query.setPhysicalGroupIds(Collections.singleton(vo.getPhysicalCompanyId()));
        }
        // 专业组
        if (Objects.nonNull(vo.getGroupId())) {
            query.setGroupIds(Collections.singleton(vo.getGroupId()));
        }
        // 检验项目
        if (CollectionUtils.isNotEmpty(vo.getTestItemIds())) {
            query.setTestItemIds(new HashSet<>(vo.getTestItemIds()));
        }
        // 姓名
        if (StringUtils.isNotBlank(vo.getPatientName())) {
            query.setPatientName(vo.getPatientName());
        }
        // 就诊类型
        if (CollectionUtils.isNotEmpty(vo.getApplyType())) {
            query.setApplyTypes(vo.getApplyType());
        }

        // 报告项目编码
        if (CollectionUtils.isNotEmpty(vo.getReportItemCodes())) {
            query.setReportItemCodes(new HashSet<>(vo.getReportItemCodes()));
        }
    }

}
