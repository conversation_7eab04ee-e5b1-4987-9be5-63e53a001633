package com.labway.lims.statistics.controller;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySampleItemSourceEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.microbiology.MicroFormulaEnum;
import com.labway.lims.api.enums.microbiology.MicroResultPropertyEnum;
import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyGermDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyInspectionDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyMedicineDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyResultDto;
import com.labway.lims.base.api.dto.GermDto;
import com.labway.lims.base.api.dto.GermGenusDto;
import com.labway.lims.base.api.service.GermGenusService;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.MicrobiologyGermGenusMedicineStatisticsVo;
import com.labway.lims.statistics.vo.MicrobiologyGermPositiveStatisticsVo;
import com.labway.lims.statistics.vo.MicrobiologyGermSampleTypeMedicineStatisticsVo;
import com.labway.lims.statistics.vo.MicrobiologyGermStatisticsRequestVo;
import com.labway.lims.statistics.vo.MicrobiologyGermStatisticsVo;
import com.labway.lims.statistics.vo.MicrobiologyMedicineStatisticsVo;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 微生物细菌统计
 */
@RestController
@RequestMapping("/data-statistics-microbiology-germ")
public class MicrobiologyGermStatisticsController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private GermService germService;
    @DubboReference
    private GermGenusService germGenusService;

    @Getter
    @Setter
    static class RemovedMicrobiologyGermDto extends MicrobiologyGermDto {
        private Long sampleId;
    }

    /**
     * 查询样本
     */
    private List<MicrobiologyInspectionDto> searchSamples(MicrobiologyGermStatisticsRequestVo vo) {
        return searchSamples(vo, new LinkedList<>());
    }

    /**
     * 查询样本
     *
     * @param removedGerms 同一患者同一样本类型细菌只统计一次 被删除的细菌
     */
    private List<MicrobiologyInspectionDto> searchSamples(MicrobiologyGermStatisticsRequestVo vo,
                                                          List<RemovedMicrobiologyGermDto> removedGerms) {
        if (Objects.isNull(vo.getEndDate()) || Objects.isNull(vo.getBeginDate())) {
            throw new IllegalStateException("日期错误");
        }

        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        if (Objects.equals(vo.getDateType(), MicrobiologyGermStatisticsRequestVo.TEST_DATE)) {
            builder.startTestDate(vo.getBeginDate());
            builder.endTestDate(vo.getEndDate());
        } else if (Objects.equals(vo.getDateType(), MicrobiologyGermStatisticsRequestVo.AUDIT_DATE)) {
            builder.startFinalCheckDate(vo.getBeginDate());
            builder.endFinalCheckDate(vo.getEndDate());
        } else {
            throw new IllegalStateException("日期错误");
        }

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            builder.hspOrgIds(vo.getHspOrgIds());
        }

        if (CollectionUtils.isNotEmpty(vo.getTestItemIds())) {
            builder.testItemIds(vo.getTestItemIds());
        }

        if (StringUtils.isNotBlank(vo.getOriginalOrgName())) {
            builder.originalOrgName(vo.getOriginalOrgName());
        }

        // 微生物
        builder.itemTypes(Set.of(ItemTypeEnum.MICROBIOLOGY.name()));
        // 是否审核
        builder.isAudit(YesOrNoEnum.YES.getCode());

        // 过滤已终止的样本
        builder.excludeSampleStatus(List.of(SampleStatusEnum.STOP_TEST.getCode()));

        final List<MicrobiologyInspectionDto> samples = elasticSearchSampleService.selectSamples(builder.build())
                // 只要微生物样本
                .stream().filter(MicrobiologyInspectionDto.class::isInstance)
                // 转成微生物样本
                .map(e -> (MicrobiologyInspectionDto) e).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        for (final Iterator<MicrobiologyInspectionDto> iterator = samples.iterator(); iterator.hasNext(); ) {
            final MicrobiologyInspectionDto sample = iterator.next();
            sample.setTestItems(new ArrayList<>(sample.getTestItems()));
            // 去掉加项的
            sample.getTestItems().removeIf(e -> Objects.equals(e.getItemSource(),
                    ApplySampleItemSourceEnum.MICROBIOLOGY_SAMPLE_FEE_ITEM.getCode()));

            // 如果项目，那么这个样本不纳入统计
            if (CollectionUtils.isEmpty(sample.getTestItems())) {
                iterator.remove();
            }
        }

        final List<MicrobiologyInspectionDto> list = new LinkedList<>();

        // 是否是只统计一次
        if (Objects.equals(vo.getOnce(), YesOrNoEnum.YES.getCode())) {
            // 根据同一个人分组
            final Map<String, List<MicrobiologyInspectionDto>> map = samples.stream()
                    .collect(Collectors.groupingBy(k -> k.getPatientName() + k.getPatientSex() + k.getHspOrgId()));
            for (List<MicrobiologyInspectionDto> es : map.values()) {
                // 然后再根据样本类型判断
                final Map<String, List<MicrobiologyInspectionDto>> mMap =
                        es.stream().collect(Collectors.groupingBy(BaseSampleEsModelDto::getSampleTypeCode));
                final Map<String, Set<Long>> germs = new HashMap<>();

                for (List<MicrobiologyInspectionDto> value : mMap.values()) {
                    for (MicrobiologyInspectionDto m : value) {
                        m.setGerms(Optional.ofNullable(m.getGerms()).orElseGet(ArrayList::new));
                        // 因为同一个人 同一个样本类型 下面的细菌只统计一次，所以这里如果是相同细菌 那么只保留一个
                        for (final Iterator<MicrobiologyGermDto> iterator = m.getGerms().iterator();
                             iterator.hasNext(); ) {
                            final MicrobiologyGermDto germ = iterator.next();
                            if (germs.getOrDefault(m.getSampleTypeCode(), Set.of()).contains(germ.getGermId())) {
                                iterator.remove();
                                final RemovedMicrobiologyGermDto removedMicrobiologyGerm =
                                        new RemovedMicrobiologyGermDto();
                                removedMicrobiologyGerm.setSampleId(m.getSampleId());
                                removedGerms.add(removedMicrobiologyGerm);
                            } else {
                                germs.computeIfAbsent(m.getSampleTypeCode(), s -> new LinkedHashSet<>())
                                        .add(germ.getGermId());
                            }
                        }

                        // 添加到新集合
                        list.add(m);

                    }
                }
            }
        } else {
            list.addAll(samples);
        }
        // 如果检验项目为空则移除
        if (CollectionUtils.isNotEmpty(list)) {
            list.removeIf(e -> CollectionUtils.isEmpty(e.getTestItems()));

            list.forEach(item -> {
                // 样本下检验 项目 如果是终止的 过滤掉
                item.getTestItems().removeIf(
                        testItem -> Objects.equals(testItem.getStopStatus(), StopTestStatus.STOP_TEST_CHARGE.getCode())
                                || Objects.equals(testItem.getStopStatus(), StopTestStatus.STOP_TEST_FREE.getCode()));
                // 禁用的项目不显示/统计
                item.getTestItems().removeIf(e -> Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()));
            });
        }

        for (MicrobiologyInspectionDto e : list) {
            if (CollectionUtils.isEmpty(e.getTestItems())) {
                e.setTestItems(List.of());
            }

            if (CollectionUtils.isEmpty(e.getGerms())) {
                e.setGerms(List.of());
            }

            if (CollectionUtils.isEmpty(e.getResults())) {
                e.setResults(List.of());
            }
        }

        // 删除非阳或阳的药物
        if (Objects.equals(vo.getType(), MicrobiologyGermStatisticsRequestVo.TYPE_YANG)
                || Objects.equals(vo.getType(), MicrobiologyGermStatisticsRequestVo.TYPE_NOT_YANG)) {
            for (MicrobiologyInspectionDto e : list) {
                for (MicrobiologyGermDto g : e.getGerms()) {
                    for (final Iterator<MicrobiologyMedicineDto> iterator = g.getMedicines().iterator();
                         iterator.hasNext(); ) {
                        final MicrobiologyMedicineDto m = iterator.next();
                        if (Objects.equals(vo.getType(), MicrobiologyGermStatisticsRequestVo.TYPE_YANG)) {
                            if (!Objects.equals(m.getFormula(), MicroFormulaEnum.YANG.getDesc())) {
                                iterator.remove();
                            }
                        } else if (Objects.equals(vo.getType(), MicrobiologyGermStatisticsRequestVo.TYPE_NOT_YANG)) {
                            if (Objects.equals(m.getFormula(), MicroFormulaEnum.YANG.getDesc())) {
                                iterator.remove();
                            }
                        }
                    }
                }
            }
        }

        return list;
    }

    /**
     * 样本阳性率
     */
    @PostMapping("/positive")
    public List<MicrobiologyGermPositiveStatisticsVo> positive(@RequestBody MicrobiologyGermStatisticsRequestVo vo) {

        final List<RemovedMicrobiologyGermDto> removedGerms = new LinkedList<>();
        final List<MicrobiologyInspectionDto> samples = searchSamples(vo, removedGerms);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        // 根据样本类型分组
        final Map<String, List<MicrobiologyInspectionDto>> map =
                samples.stream().collect(Collectors.groupingBy(BaseSampleEsModelDto::getSampleTypeName));

        final List<MicrobiologyGermPositiveStatisticsVo> list = new LinkedList<>();

        for (var e1 : map.entrySet()) {

            if (CollectionUtils.isEmpty(e1.getValue())) {
                continue;
            }

            // 这个类型下所有的检验项目
            final Map<Long, List<BaseSampleEsModelDto.TestItem>> testItems =
                    e1.getValue().stream().map(BaseSampleEsModelDto::getTestItems).flatMap(Collection::stream)
                            .collect(Collectors.groupingBy(BaseSampleEsModelDto.TestItem::getTestItemId));

            for (var e2 : testItems.entrySet()) {

                // 样本
                final List<MicrobiologyInspectionDto> ss = e1.getValue().stream()
                        .filter(
                                e -> e.getTestItems().stream().anyMatch(k -> Objects.equals(k.getTestItemId(), e2.getKey())))
                        .collect(Collectors.toList());

                final MicrobiologyGermPositiveStatisticsVo v = new MicrobiologyGermPositiveStatisticsVo();
                v.setSampleTypeName(e1.getKey());
                v.setTestItemName(e2.getValue().iterator().next().getTestItemName());
                // 包含细菌就是阳性
                v.setPositiveSampleCount((int) ss.stream().filter(e -> {
                    if (CollectionUtils.isNotEmpty(e.getGerms())) {
                        return true;
                    }

                    // 如果被移除的细菌中有，那么也是阳性。因为只统计一次的是细菌 不是样本
                    return removedGerms.stream().anyMatch(k -> Objects.equals(k.getSampleId(), e.getSampleId()));
                }).count());
                // 样本数量
                v.setSampleCount(ss.size());
                // 细菌数量
                v.setGermCount(ss.stream().map(MicrobiologyInspectionDto::getGerms).mapToInt(Collection::size).sum());
                // 阳性样本数÷样本总数乘以100%
                v.setPositiveSampleRate((v.getPositiveSampleCount() * 1.0 / v.getSampleCount()) * 100.0);

                list.add(v);
            }

        }

        return list;
    }

    /**
     * 细菌分布
     */
    @PostMapping("/germ")
    public List<MicrobiologyGermStatisticsVo> germ(@RequestBody MicrobiologyGermStatisticsRequestVo vo) {

        final List<MicrobiologyInspectionDto> samples = searchSamples(vo);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        // 当前查询出来所有细菌的珠数
        final int allGermCount =
                samples.stream().map(MicrobiologyInspectionDto::getGerms).mapToInt(Collection::size).sum();

        final Set<Long> germIds = samples.stream().map(MicrobiologyInspectionDto::getGerms).flatMap(Collection::stream)
                .map(MicrobiologyGermDto::getGermId).collect(Collectors.toSet());

        // 查询细菌
        final Map<Long, GermDto> germs = germService.selectByGermIds(germIds).stream()
                .collect(Collectors.toMap(GermDto::getGermId, v -> v, (a, b) -> a));
        if (MapUtils.isEmpty(germs)) {
            return Collections.emptyList();
        }

        // 菌属
        final Map<Long,
                GermGenusDto> germGenus = germGenusService
                .selectByGermGenusIds(germs.values().stream().map(GermDto::getGermGenusId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(GermGenusDto::getGermGenusId, v -> v, (a, b) -> a));
        if (MapUtils.isEmpty(germGenus)) {
            return Collections.emptyList();
        }

        final List<MicrobiologyGermStatisticsVo> list = new LinkedList<>();

        // 循环菌属
        for (Map.Entry<Long, GermGenusDto> gg : germGenus.entrySet()) {

            // 根据菌属找到细菌
            for (Map.Entry<Long, GermDto> g : germs.entrySet().stream()
                    .filter(e -> Objects.equals(e.getValue().getGermGenusId(), gg.getKey())).collect(Collectors.toList())) {

                // 根据细菌找到样本
                final List<MicrobiologyInspectionDto> ss = samples.stream()
                        .filter(e -> e.getGerms().stream().anyMatch(l -> Objects.equals(l.getGermId(), g.getKey())))
                        .collect(Collectors.toList());

                final MicrobiologyGermStatisticsVo v = new MicrobiologyGermStatisticsVo();
                v.setGermGenusName(gg.getValue().getGermGenusName());
                v.setGermName(g.getValue().getGermName());
                // 细菌总数
                v.setGermCount((int) ss.stream().map(MicrobiologyInspectionDto::getGerms).flatMap(Collection::stream)
                        .filter(e -> Objects.equals(e.getGermId(), g.getValue().getGermId())).count());

                // 此菌属下所有细菌总和
                final long sss = samples.stream().map(MicrobiologyInspectionDto::getGerms)
                        .filter(e -> e.stream().anyMatch(l -> Objects.equals(l.getGermGenusId(), gg.getKey()))).count();
                // 当前细菌株数÷此细菌菌属下细菌总株数乘以100%
                v.setGermRate(v.getGermCount() * 1.0 / sss * 100.0);
                // 当前细菌株数÷当前统计内容中所有细菌株数乘以100%
                v.setGermTotalRate(v.getGermCount() * 1.0 / allGermCount * 100.0);

                list.add(v);
            }
        }

        return list;
    }

    /**
     * 细菌及样本类型分布
     */
    @PostMapping("/germ-sample-type")
    public List<MicrobiologyGermSampleTypeMedicineStatisticsVo>
    germSampleType(@RequestBody MicrobiologyGermStatisticsRequestVo vo) {

        final List<MicrobiologyInspectionDto> samples = searchSamples(vo);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        // 所有的样本类型
        final Set<String> sampleTypes =
                samples.stream().map(BaseSampleEsModelDto::getSampleTypeName).collect(Collectors.toSet());

        // 所有细菌
        final Set<Long> germIds = samples.stream().map(MicrobiologyInspectionDto::getGerms).flatMap(Collection::stream)
                .map(MicrobiologyGermDto::getGermId).collect(Collectors.toSet());
        List<GermDto> germs = germService.selectByGermIds(germIds);

        // 菌属
        final List<GermGenusDto> germGenus = germGenusService
                .selectByGermGenusIds(germs.stream().map(GermDto::getGermGenusId).collect(Collectors.toSet()));

        final List<MicrobiologyGermSampleTypeMedicineStatisticsVo> list = new LinkedList<>();

        // 菌属
        for (var gg : germGenus) {
            final MicrobiologyGermSampleTypeMedicineStatisticsVo v =
                    new MicrobiologyGermSampleTypeMedicineStatisticsVo();
            v.setGermGenusId(gg.getGermGenusId());
            v.setGermGenusName(gg.getGermGenusName());
            v.setGerms(new LinkedHashMap<>());

            // 获取菌属下的细菌
            final List<GermDto> ggGerms = germs.stream()
                    .filter(e -> Objects.equals(e.getGermGenusId(), gg.getGermGenusId())).collect(Collectors.toList());

            for (GermDto g : ggGerms) {

                final MicrobiologyGermSampleTypeMedicineStatisticsVo.Germ germ =
                        v.getGerms().computeIfAbsent(g.getGermCode(), s -> {
                            final MicrobiologyGermSampleTypeMedicineStatisticsVo.Germ g1 =
                                    new MicrobiologyGermSampleTypeMedicineStatisticsVo.Germ();
                            g1.setGermId(g.getGermId());
                            g1.setGermName(g.getGermName());
                            g1.setTestItems(new LinkedHashMap<>());
                            return g1;
                        });

                // 获取有这个细菌的样本
                final List<MicrobiologyInspectionDto> sss = samples.stream()
                        .filter(e -> e.getGerms().stream().anyMatch(l -> Objects.equals(l.getGermId(), g.getGermId())))
                        .collect(Collectors.toList());

                for (Long testItemId : sss.stream().map(e -> e.getTestItems().iterator().next().getTestItemId())
                        .collect(Collectors.toSet())) {
                    germ.getTestItems().computeIfAbsent(testItemId, s -> {
                        final MicrobiologyGermSampleTypeMedicineStatisticsVo.TestItem t =
                                new MicrobiologyGermSampleTypeMedicineStatisticsVo.TestItem();
                        t.setTestItemId(testItemId);
                        t.setSampleTypes(new LinkedHashMap<>());
                        t.setTestItemName(sss.stream()
                                .filter(e -> e.getTestItems().stream()
                                        .anyMatch(k -> Objects.equals(k.getTestItemId(), testItemId)))
                                .findFirst().map(k -> k.getTestItems().iterator().next().getTestItemName())
                                .orElse(StringUtils.EMPTY));
                        // 获取这个细菌下这个项目下的样本
                        t.setGermCount((int) sss.stream()
                                .filter(e -> e.getTestItems().stream()
                                        .anyMatch(k -> Objects.equals(k.getTestItemId(), testItemId)))
                                .map(MicrobiologyInspectionDto::getGerms).mapToLong(Collection::size).sum());
                        for (String sampleType : sampleTypes) {
                            t.getSampleTypes().put(sampleType,
                                    (int) sss.stream()
                                            .filter(e -> e.getTestItems().stream()
                                                    .anyMatch(k -> Objects.equals(k.getTestItemId(), testItemId)))
                                            .filter(e -> Objects.equals(e.getSampleTypeName(), sampleType)).count());
                        }

                        // 当前细菌株数
                        final long count = sss.stream()
                                .filter(e -> e.getTestItems().stream()
                                        .anyMatch(k -> Objects.equals(k.getTestItemId(), testItemId)))
                                .map(MicrobiologyInspectionDto::getGerms).flatMap(Collection::stream)
                                .filter(e -> Objects.equals(e.getGermId(), g.getGermId())).count();

                        // 细菌百分率计算：当前细菌株数÷此细菌菌属下细菌总株数乘以100%
                        t.setGermRate(count * 1.0 / ggGerms.size() * 100.0);

                        t.setCount((int) count);

                        // 总百分率计算：当前细菌株数÷当前统计内容中所有细菌株数乘以100%
                        t.setTotalGermRate(count * 1.0 / germs.size() * 100.0);
                        return t;
                    });

                }
            }

            // 细菌百分率计算：当前细菌株数÷此细菌菌属下细菌总株数乘以100%
            int total = v.getGerms().values().stream().flatMap(e -> e.getTestItems().values().stream()).mapToInt(e -> e.getCount()).sum();
            v.getGerms().values().forEach(germ -> {
                germ.getTestItems().values().forEach(testItem -> {
                    testItem.setGermRate(testItem.getCount() * 1.0 / total * 100.0);
                });
            });

            list.add(v);

        }

        // 总百分率计算：当前细菌株数÷当前统计内容中所有细菌株数乘以100%
        int total = list.stream().flatMap(e -> e.getGerms().values().stream()).flatMap(e -> e.getTestItems().values().stream()).mapToInt(e -> e.getCount()).sum();
        list.forEach(v -> {
            v.getGerms().values().forEach(germ -> {
                germ.getTestItems().values().forEach(testItem -> {
                    testItem.setTotalGermRate(testItem.getCount() * 1.0 / total * 100.0);
                });
            });
        });

        return list;
    }

    /**
     * 细菌耐药统计
     */
    @PostMapping("/medicine")
    public Collection<MicrobiologyMedicineStatisticsVo> medicine(@RequestBody MicrobiologyGermStatisticsRequestVo vo) {

        final List<MicrobiologyInspectionDto> samples = searchSamples(vo);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        // 所有细菌
        final List<MicrobiologyGermDto> germs = samples.stream().map(MicrobiologyInspectionDto::getGerms)
                .flatMap(Collection::stream).collect(Collectors.toList());

        final Map<Long, MicrobiologyMedicineStatisticsVo> map = new HashMap<>();

        for (MicrobiologyGermDto g : germs) {

            if (CollectionUtils.isEmpty(g.getMedicines())) {
                continue;
            }

            final MicrobiologyMedicineStatisticsVo v = map.computeIfAbsent(g.getGermId(), k -> {
                final MicrobiologyMedicineStatisticsVo v1 = new MicrobiologyMedicineStatisticsVo();
                v1.setGermId(g.getGermId());
                v1.setGermName(g.getGermName());
                v1.setMedicines(new LinkedHashMap<>());
                return v1;
            });

            for (MicrobiologyMedicineDto m : g.getMedicines()) {

                final MicrobiologyMedicineStatisticsVo.Medicine md =
                        v.getMedicines().computeIfAbsent(m.getMedicineId(), k -> {
                            final MicrobiologyMedicineStatisticsVo.Medicine me =
                                    new MicrobiologyMedicineStatisticsVo.Medicine();
                            me.setMedicineId(0L);
                            me.setTotalTestCount(0);
                            me.setSusceptibilityCount(0);
                            me.setSusceptibilityRate(0.0D);
                            me.setSensitiveCount(0);
                            me.setSensitiveRate(0.0D);
                            me.setMediumSensitiveCount(0);
                            me.setMediumSensitiveRate(0.0D);
                            me.setPositiveCount(0);
                            me.setMedicineId(m.getMedicineId());
                            me.setMedicineName(m.getMedicineName());
                            return me;
                        });

                final List<MicrobiologyMedicineDto> ms = g.getMedicines().stream()
                        .filter(e -> Objects.equals(e.getMedicineId(), m.getMedicineId())).collect(Collectors.toList());

                // 获取此抗生素出现了多少次
                md.setTotalTestCount(md.getTotalTestCount() + ms.size());

                // 耐药数
                md.setSusceptibilityCount(md.getSusceptibilityCount() + (int) ms.stream()
                        .filter(e -> Objects.equals(e.getSusceptibility(), MicroSusceptibilityEnum.RESISTANT.getDesc()))
                        .count());

                // 敏感数
                md.setSensitiveCount(md.getSensitiveCount() + (int) ms.stream()
                        .filter(e -> Objects.equals(e.getSusceptibility(), MicroSusceptibilityEnum.SENSITIVE.getDesc()))
                        .count());

                // 中敏数
                md.setMediumSensitiveCount(md.getMediumSensitiveCount() + (int) ms.stream()
                        .filter(e -> Objects.equals(e.getSusceptibility(), MicroSusceptibilityEnum.MID_SENSITIVE.getDesc()))
                        .count());

                // 阳性
                md.setPositiveCount(md.getPositiveCount() + (int) ms.stream()
                        .filter(e -> Objects.equals(e.getFormula(), MicroFormulaEnum.YANG.getDesc())).count());

            }

        }

        // 统计率
        for (MicrobiologyMedicineStatisticsVo e : map.values()) {
            for (MicrobiologyMedicineStatisticsVo.Medicine md : e.getMedicines().values()) {
                // 耐药率
                md.setSusceptibilityRate(md.getSusceptibilityCount() * 1.0 / md.getTotalTestCount() * 100.0);
                // 敏感率
                md.setSensitiveRate(md.getSensitiveCount() * 1.0 / md.getTotalTestCount() * 100.0);
                // 中敏率
                md.setMediumSensitiveRate(md.getMediumSensitiveCount() * 1.0 / md.getTotalTestCount() * 100.0);
            }
        }

        return map.values();
    }

    /**
     * 细菌分类耐药统计
     */
    @PostMapping("/germ-genus-medicine")
    public List<MicrobiologyGermGenusMedicineStatisticsVo>
    germGenusMedicine(@RequestBody MicrobiologyGermStatisticsRequestVo vo) {

        final List<MicrobiologyInspectionDto> samples = searchSamples(vo);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        // 所有细菌
        final List<MicrobiologyGermDto> germs = samples.stream().map(MicrobiologyInspectionDto::getGerms)
                .flatMap(Collection::stream).filter(e -> Objects.nonNull(e.getGermGenusId())).collect(Collectors.toList());

        // 菌属
        final List<GermGenusDto> germGenus = germGenusService
                .selectByGermGenusIds(germs.stream().map(MicrobiologyGermDto::getGermGenusId).collect(Collectors.toSet()));

        final List<MicrobiologyGermGenusMedicineStatisticsVo> list = new LinkedList<>();

        // 菌属
        for (var gg : germGenus) {

            final MicrobiologyGermGenusMedicineStatisticsVo v = new MicrobiologyGermGenusMedicineStatisticsVo();
            v.setGermGenusId(gg.getGermGenusId());
            v.setGermGenusName(gg.getGermGenusName());
            v.setMedicines(new LinkedHashMap<>());

            // 获取到这个菌属下所有的抗生素
            final Map<Long, List<MicrobiologyMedicineDto>> medicines =
                    germs.stream().filter(e -> Objects.equals(e.getGermGenusId(), gg.getGermGenusId()))
                            .map(MicrobiologyGermDto::getMedicines).flatMap(Collection::stream)
                            .collect(Collectors.groupingBy(MicrobiologyMedicineDto::getMedicineId));

            for (Map.Entry<Long, List<MicrobiologyMedicineDto>> m : medicines.entrySet()) {
                final MicrobiologyGermGenusMedicineStatisticsVo.Medicine md =
                        v.getMedicines().computeIfAbsent(m.getKey(), k -> {
                            final MicrobiologyGermGenusMedicineStatisticsVo.Medicine me =
                                    new MicrobiologyGermGenusMedicineStatisticsVo.Medicine();
                            me.setMedicineId(m.getKey());
                            me.setMedicineName(m.getValue().iterator().next().getMedicineName());
                            me.setTotalTestCount(0);
                            me.setSusceptibilityCount(0);
                            me.setSusceptibilityRate(0.0D);
                            me.setSensitiveCount(0);
                            me.setSensitiveRate(0.0D);
                            me.setMediumSensitiveCount(0);
                            me.setMediumSensitiveRate(0.0D);
                            me.setPositiveCount(0);
                            return me;
                        });

                final List<MicrobiologyMedicineDto> ms = m.getValue().stream()
                        .filter(e -> Objects.equals(e.getMedicineId(), m.getKey())).collect(Collectors.toList());

                // 获取此抗生素出现了多少次
                md.setTotalTestCount(md.getTotalTestCount() + ms.size());

                // 耐药数
                md.setSusceptibilityCount(md.getSusceptibilityCount() + (int) ms.stream()
                        .filter(e -> Objects.equals(e.getSusceptibility(), MicroSusceptibilityEnum.RESISTANT.getDesc()))
                        .count());

                // 敏感数
                md.setSensitiveCount(md.getSensitiveCount() + (int) ms.stream()
                        .filter(e -> Objects.equals(e.getSusceptibility(), MicroSusceptibilityEnum.SENSITIVE.getDesc()))
                        .count());

                // 中敏数
                md.setMediumSensitiveCount(md.getMediumSensitiveCount() + (int) ms.stream()
                        .filter(e -> Objects.equals(e.getSusceptibility(), MicroSusceptibilityEnum.MID_SENSITIVE.getDesc()))
                        .count());

                // 阳性
                md.setPositiveCount(md.getPositiveCount() + (int) ms.stream()
                        .filter(e -> Objects.equals(e.getFormula(), MicroFormulaEnum.YANG.getDesc())).count());

            }

            list.add(v);
        }

        // 统计率
        for (MicrobiologyGermGenusMedicineStatisticsVo e : list) {
            for (MicrobiologyGermGenusMedicineStatisticsVo.Medicine md : e.getMedicines().values()) {
                // 耐药率
                md.setSusceptibilityRate(md.getSusceptibilityCount() * 1.0 / md.getTotalTestCount() * 100.0);
                // 敏感率
                md.setSensitiveRate(md.getSensitiveCount() * 1.0 / md.getTotalTestCount() * 100.0);
                // 中敏率
                md.setMediumSensitiveRate(md.getMediumSensitiveCount() * 1.0 / md.getTotalTestCount() * 100.0);
            }
        }

        return list;
    }


    /**
     * 科室阳性样本
     */
    @PostMapping("dept-positive-sample")
    public Object deptPositiveSample(@RequestBody MicrobiologyGermStatisticsRequestVo vo){
        vo.setType(0); // 所有阳性
        List<MicrobiologyInspectionDto> samples = searchSamples(vo);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        samples = samples.stream()
                .filter(e->{
                    // 微生物检验结果结果属性为阳性的的标本
                    boolean contains = e.getResults().stream()
                            .map(MicrobiologyResultDto::getResultProperty)
                            .collect(Collectors.toList())
                            .contains(MicroResultPropertyEnum.POSITIVE.getDesc());
                    // 样本有细菌的
                    if (contains || CollectionUtils.isNotEmpty(e.getGerms())) {
                        if (StringUtils.isBlank(e.getDept())) {
                            e.setDept("/");
                        }
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());

        // 所有科室
        Set<String> depts = samples.stream().map(BaseSampleEsModelDto::getDept).collect(Collectors.toSet());

        // 样本类型
        Map<String, List<MicrobiologyInspectionDto>> sampleTypeCodeMap = samples.stream()
                .collect(Collectors.groupingBy(BaseSampleEsModelDto::getSampleTypeCode));


        List<Map<String, Object>> results = new ArrayList<>();

        sampleTypeCodeMap.forEach((k,v) ->{
            Map<String, Long> sampleDeptMap = v.stream().collect(Collectors.groupingBy(BaseSampleEsModelDto::getDept, Collectors.counting()));
            Map<String, Object> result = Map.of("sampleTypeCode", k,
                                                    "sampleTypeName", v.get(0).getSampleTypeName(),
                                                    "dept", sampleDeptMap,
                                                    "count", sampleDeptMap.values().stream().mapToLong(l -> l).sum());
            results.add(result);
        });

        return Map.of("depts", depts, "data", results);
    }


    /**
     * 样本类型分布
     */
    @PostMapping("sample-type-distribution")
    public Object sampleTypeDistribution(@RequestBody MicrobiologyGermStatisticsRequestVo vo) {
        List<MicrobiologyInspectionDto> samples = searchSamples(vo);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }
        samples = samples.stream().filter(e -> CollectionUtils.isNotEmpty(e.getGerms())).collect(Collectors.toList());

        // 样本类型
        Map<String, List<MicrobiologyInspectionDto>> sampleTypeCodeMap = samples.stream()
                .collect(Collectors.groupingBy(BaseSampleEsModelDto::getSampleTypeName));

        // 最终数据
        Map<String, List<Map<String, Object>>> data = new HashMap<>();

        // 样本类型循环
        sampleTypeCodeMap.forEach((k, v) -> {

            // 多个细菌结果
            List<Map<String, Object>> germResultList = new ArrayList<>();
            // 细菌名称分组
            Map<String, List<MicrobiologyGermDto>> germMap = v.stream()
                    .flatMap(e -> e.getGerms().stream())
                    .collect(Collectors.groupingBy(MicrobiologyGermDto::getGermName));

            // 细菌名称分组循环
            germMap.forEach((gk, gv) -> {
                Map<String, Object> sampleTypeMap = new HashMap<>();

                // k = 药物名称   v.k = 敏感度   v.v = 时间段内 该样本类型下的所有样本的 gk 细菌下的 所有药物 下的敏感数量
                Map<String, Map<String, Long>> medicineMap = gv.stream()
                        .flatMap(e -> e.getMedicines().stream())
                        .peek(e -> {
                            if (StringUtils.isBlank(e.getSusceptibility())
                                    || MicroSusceptibilityEnum.WT.getDesc().equals(e.getSusceptibility())
                                    || MicroSusceptibilityEnum.NWT.getDesc().equals(e.getSusceptibility())) {
                                e.setSusceptibility("/");
                            }
                        })
                        .collect(Collectors.groupingBy(MicrobiologyMedicineDto::getMedicineName,
                                Collectors.groupingBy(MicrobiologyMedicineDto::getSusceptibility,
                                        Collectors.counting())));

                sampleTypeMap.put("germ", gk);
                sampleTypeMap.put("medicines", medicineMap);
                germResultList.add(sampleTypeMap);
            });

            data.put(k, germResultList);
        });

        return data;

    }


    /**
     * 抗生素分布
     */
    @PostMapping("antibiotic-distribution")
    public Object antibioticDistribution(@RequestBody MicrobiologyGermStatisticsRequestVo vo) {
        List<MicrobiologyInspectionDto> samples = searchSamples(vo);
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }
        samples = samples.stream().filter(e -> CollectionUtils.isNotEmpty(e.getGerms())).collect(Collectors.toList());

        // 药物           细菌        敏感度   数量
        Map<String, Map<String, Map<String, Integer>>> medicineGermCountMap = new HashMap<>();

        // 样本
        samples.forEach(sample -> {
            // 细菌
            sample.getGerms().forEach(germ -> {
                // 药物
                germ.getMedicines().forEach(medicine -> {
                    // 药物名称
                    String medicineName = medicine.getMedicineName();
                    // 细菌名称
                    String germName = germ.getGermName();
                    // 药物敏感度
                    boolean isOther = (StringUtils.isBlank(medicine.getSusceptibility())
                            || MicroSusceptibilityEnum.WT.getDesc().equals(medicine.getSusceptibility())
                            || MicroSusceptibilityEnum.NWT.getDesc().equals(medicine.getSusceptibility()));
                    String susceptibility = isOther ? "/" : medicine.getSusceptibility();

                    Map<String, Map<String, Integer>> medicineCountMap = medicineGermCountMap.getOrDefault(medicineName, new HashMap<>());

                    Map<String, Integer> susceptibilityMap = medicineCountMap.getOrDefault(germName, new HashMap<>());

                    // 敏感度数量
                    Integer susceptibilityCount = susceptibilityMap.getOrDefault(susceptibility, 0);
                    susceptibilityMap.put(susceptibility, ++susceptibilityCount);

                    // 总数量
                    Integer count = susceptibilityMap.getOrDefault("count", 0);
                    susceptibilityMap.put("count", ++count);

                    medicineCountMap.put(germName, susceptibilityMap);
                    medicineGermCountMap.put(medicineName, medicineCountMap);
                });
            });
        });

        return medicineGermCountMap;
    }

}
