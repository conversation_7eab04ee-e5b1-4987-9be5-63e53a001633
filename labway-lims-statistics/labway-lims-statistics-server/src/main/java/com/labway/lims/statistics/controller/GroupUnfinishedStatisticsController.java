package com.labway.lims.statistics.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.statistics.service.GroupUnfinishedStatisticsService;
import com.labway.lims.statistics.vo.GroupUnfinishedStatisticsRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <pre>
 * GroupUnfinishedStatisticsController
 * 专业组未完成工作量统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/7/5 10:57
 */
@Slf4j
@RestController
@RequestMapping("group-unfinished-statistics")
public class GroupUnfinishedStatisticsController extends BaseController {

    @Resource
    private GroupUnfinishedStatisticsService groupUnfinishedStatisticsService;

    /**
     * 待接收列表 wait-receive
     */
    @PostMapping("wait-receive")
    public Object waitReceive(@RequestBody @Validated GroupUnfinishedStatisticsRequestVo requestVo) {
        if (requestVo.getEndDate().before(requestVo.getStartDate())) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }
        return groupUnfinishedStatisticsService.waitReceive(requestVo);
    }

    /**
     * 待交接列表 wait-handover
     */
    @PostMapping("wait-handover")
    public Object waitHandover (@RequestBody @Validated GroupUnfinishedStatisticsRequestVo requestVo) {
        if (requestVo.getEndDate().before(requestVo.getStartDate())) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }
        return groupUnfinishedStatisticsService.waitHandover(requestVo);
    }

    /**
     * 待二次分拣列表 wait-twopick
     */
    @PostMapping("wait-twopick")
    public Object waitTwoPick(@RequestBody @Validated GroupUnfinishedStatisticsRequestVo requestVo) {
        if (requestVo.getEndDate().before(requestVo.getStartDate())) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }
        return groupUnfinishedStatisticsService.waitTwoPick(requestVo);
    }

    /**
     * 待审核列表 wait-audit
     */
    @PostMapping("wait-audit")
    public Object waitAudit(@RequestBody @Validated GroupUnfinishedStatisticsRequestVo requestVo) {
        if (requestVo.getEndDate().before(requestVo.getStartDate())) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }
        return groupUnfinishedStatisticsService.waitAudit(requestVo);
    }

}
