package com.labway.lims.statistics.controller;


import cn.hutool.core.date.DateUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.routine.SampleCriticalResultStatusEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.GeneticsInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.api.dto.es.SpecialtyInspectionDto;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.dto.SampleRetestItemDto;
import com.labway.lims.routine.api.dto.SelectSampleCriticalDto;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.statistics.service.ApplySampleService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.CriticalTimeOutItemVo;
import com.labway.lims.statistics.vo.TatStatisticsRequestVo;
import com.labway.lims.statistics.vo.TatStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * TaT统计
 */
@Slf4j
@RestController
@RequestMapping("/tat-statistics")
public class TatStatisticsController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    ApplySampleService applySampleService;

    @DubboReference
    private SampleCriticalResultService criticalResultService;

    @DubboReference
    private SampleRetestItemService sampleRetestItemService;

    /**
     * 统计
     */
    @PostMapping("/statistics")
    public List<TatStatisticsVo> statistics(@RequestBody TatStatisticsRequestVo vo) {

        if (Objects.isNull(vo.getEndDate()) || Objects.isNull(vo.getBeginDate())) {
            throw new IllegalStateException("日期错误");
        }

        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.startCreateDate(vo.getBeginDate());
        builder.endCreateDate(vo.getEndDate());

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            builder.hspOrgIds(vo.getHspOrgIds());
        }

        if (CollectionUtils.isNotEmpty(vo.getGroupIds())) {
            builder.groupIds(vo.getGroupIds());
        }

        if (StringUtils.isNotBlank(vo.getBarcode())) {
            builder.barcodes(Set.of(vo.getBarcode()));
        }

        if (Objects.nonNull(vo.getUrgent())) {
            builder.urgent(vo.getUrgent());
        }

        if (CollectionUtils.isNotEmpty(vo.getApplyTypes())) {
            builder.applyTypes(vo.getApplyTypes());
        }

        final List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(builder.build());
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        return samples.stream().map(e -> {
            final TatStatisticsVo v = new TatStatisticsVo();

            BeanUtils.copyProperties(e, v);

            v.setCreateDate(e.getSignDate());

            // 如果二次审核日期为空 那么就是一次审核
            v.setTwoCheckDate(e.getFinalCheckDate());
            v.setOneCheckDate(null);

            if (e instanceof RoutineInspectionDto) {
                v.setOneCheckDate(((RoutineInspectionDto) e).getOneCheckDate());
            } else if (e instanceof GeneticsInspectionDto) {
                v.setOneCheckDate(((GeneticsInspectionDto) e).getOneCheckDate());
            } else if (e instanceof SpecialtyInspectionDto) {
                v.setOneCheckDate(((SpecialtyInspectionDto) e).getOneCheckDate());
            }


            String takeSampleTime = applySampleService.selectTakeSampleTimeByBarcode(v.getBarcode(), v.getHspOrgCode());
            Date takeSampleTimeDate = null;

            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                takeSampleTimeDate = StringUtils.isNotBlank(takeSampleTime) ? sdf.parse(takeSampleTime) : null;
            } catch (ParseException ex) {
                log.error("TAT统计查询，物流取样时间转换异常[{}]",ex.getMessage());
            }
            // 获取物流取样时间
            v.setSamplingDate(takeSampleTimeDate);

            // https://www.tapd.cn/59091617/bugtrace/bugs/view?bug_id=1159091617001002162
//            if (!Objects.equals(e.getSource(), ApplySourceEnum.SUPPLEMENTARY.name())) {
//                v.setSamplingDate(null);
//            }

            v.setLogisticsTime(0D);
            v.setPickTime(0D);
            v.setTestTime(0D);

            if (ObjectUtils.allNotNull(v.getCreateDate(), v.getSamplingDate())) {
                // 签收时间减去取样时间
                v.setLogisticsTime(DateUtil.betweenMs(v.getCreateDate(), v.getSamplingDate()) / 1000.0 / 60.0 / 60.0);
            }

            if (Objects.equals(e.getIsOnePick(), YesOrNoEnum.YES.getCode()) &&
                    ObjectUtils.allNotNull(v.getOnePickDate(), v.getCreateDate())) {
                // 一次分拣时间减去签收时间
                v.setPickTime(DateUtil.betweenMs(v.getOnePickDate(), v.getCreateDate()) / 1000.0 / 60.0 / 60.0);
            }

            if (ObjectUtils.allNotNull(v.getOnePickDate(), v.getTwoCheckDate())) {
                // 审核时间减去一次次分拣时间
                v.setTestTime(DateUtil.betweenMs(v.getOnePickDate(), v.getTwoCheckDate()) / 1000.0 / 60.0 / 60.0);
            }

            return v;
        }).collect(Collectors.toList());
    }

    /**
     * 获取危急值超时的列表
     */
    @PostMapping("/getCriticalTimeOut")
    public Object getCriticalTimeOut() {
        //优先判断是否有超时配置
        int timeOut = criticalResultService.getTimeoutConfig();
        if (timeOut > 0){
            //通过获取已复查未处理的危急值来判断
            SelectSampleCriticalDto criticalDto = new SelectSampleCriticalDto();
            criticalDto.setStatus(SampleCriticalResultStatusEnum.REVIEW.getCode());
            criticalDto.setOrgId(LoginUserHandler.get().getOrgId());
            criticalDto.setGroupId(LoginUserHandler.get().getGroupId());
            List<SampleCriticalResultDto> criticalResultDtos = criticalResultService.selectBySelectSampleCriticalDto(criticalDto);
            if (CollectionUtils.isNotEmpty(criticalResultDtos)){
                //查找所有正在复查的项目结果 是否超时
                // 样本id List
                final Set<Long> sampleIds =
                        criticalResultDtos.stream().map(SampleCriticalResultDto::getSampleId).collect(Collectors.toSet());
                //根据ID获取到复查中的结果列表，并且只保留有结果值的。，因为有结果值才会有复查时间
                List<SampleRetestItemDto> sampleRetestItemDtos = sampleRetestItemService.selectBySampleIds(sampleIds).stream()
                        .filter(s -> StringUtils.isNotEmpty(s.getResult())).collect(Collectors.toList());
                //过滤掉没有超时的
                sampleRetestItemDtos.removeIf(e -> {
                    if (Objects.nonNull(e.getUpdateDate())){
                        return System.currentTimeMillis() - e.getUpdateDate().getTime() <= timeOut * 60 * 1000L;
                    }

                    return true;
                });
                //剩下的 取第一个获取基础信息返回给前端即可
                if (CollectionUtils.isNotEmpty(sampleRetestItemDtos)){
                    BaseSampleEsModelDto esModelDto = elasticSearchSampleService.selectByApplySampleId(sampleRetestItemDtos.get(0).getApplySampleId());
                    CriticalTimeOutItemVo timeOutItemVo = new CriticalTimeOutItemVo();
                    timeOutItemVo.setCount(sampleRetestItemDtos.size());
                    timeOutItemVo.setReportItemName(sampleRetestItemDtos.get(0).getReportItemName());
                    if (esModelDto != null){
                        timeOutItemVo.setSampleNo(esModelDto.getSampleNo());
                    }
                    return timeOutItemVo;
                }
            }
        }


        return Map.of();
    }
}
