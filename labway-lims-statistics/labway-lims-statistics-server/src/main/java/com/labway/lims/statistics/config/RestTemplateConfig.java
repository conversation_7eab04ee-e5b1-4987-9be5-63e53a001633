package com.labway.lims.statistics.config;

import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

    @Bean
//    @LoadBalanced // 标识使用负载均衡 通过服务名调用
    public RestTemplate restTemplate(){
        return new RestTemplate();
    }

}
