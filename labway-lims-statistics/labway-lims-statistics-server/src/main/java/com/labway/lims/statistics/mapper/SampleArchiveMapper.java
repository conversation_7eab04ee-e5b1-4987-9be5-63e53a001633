package com.labway.lims.statistics.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.lims.statistics.dto.SampleArchiveDto;
import com.labway.lims.statistics.dto.SampleArchiveQueryDto;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 * SampleArchiveMapper
 * 样本归档信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/31 13:45
 */
@Mapper
public interface SampleArchiveMapper extends BaseMapper<SampleArchiveDto> {

    Page<SampleArchiveDto> selectSampleArchivePageByWrapper(
            IPage<SampleArchiveDto> page, @Param(Constants.WRAPPER) Wrapper<SampleArchiveDto> queryWrapper);

    List<SampleArchiveDto> selectSampleArchive(@Param("param") SampleArchiveQueryDto queryDto);
    List<SampleArchiveDto> selectSampleArchiveByBarcode(@Param("barcode") String barcode, @Param("groupId")Long groupId);

    Page<SampleArchiveDto> selectSampleArchivePage(
            IPage<SampleArchiveDto> page, @Param(Constants.WRAPPER) Wrapper<SampleArchiveDto> queryWrapper);

}
