package com.labway.lims.statistics.service.chain;

import com.labway.lims.apply.api.dto.SampleEsQuery;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;

import static com.labway.lims.api.ConditionCheckUtils.isValidMonthFormat;

/**
 * 校验责任链的一些公共方法
 */
public class CheckChainParams {
    private CheckChainParams(){}
    /**
     * 检查 送检时间 与财务月份
     *
     * @param financialMonth    财务月份
     * @param beginDeliveryDate 送检时间开始
     * @param endDeliveryDate   送检时间结束
     */
    public static void checkDateRangRequest(String financialMonth, Date beginDeliveryDate, Date endDeliveryDate) {
        if (StringUtils.isBlank(financialMonth) && (Objects.isNull(beginDeliveryDate) || Objects.isNull(endDeliveryDate))) {
            throw new IllegalStateException("送检时间与财务月份必须传一个");
        }
        if (StringUtils.isNotBlank(financialMonth) && Objects.nonNull(beginDeliveryDate) && Objects.nonNull(endDeliveryDate)) {
            throw new IllegalStateException("送检时间与财务月份只能传一个");
        } if (StringUtils.isNotBlank(financialMonth) && !isValidMonthFormat(financialMonth)) {
            throw new IllegalStateException("财务月份格式错误,请传入[yyyy-MM]格式");
        }
    }

    /**
     * 添加查询 时间 范围
     *
     * @param dto               查询参数
     * @param financialMonth    财务月份
     * @param beginDeliveryDate 送检开始时间
     * @param endDeliveryDate   送检结束时间
     */
    public static void sampleEsQueryAddDateRang(SampleEsQuery dto, String financialMonth, Date beginDeliveryDate, Date endDeliveryDate) {
        if (StringUtils.isNotBlank(financialMonth)) {
            YearMonth yearMonth = YearMonth.parse(financialMonth);
            LocalDateTime monthStartDateTime = yearMonth.atDay(1).atStartOfDay();
            LocalDateTime monthEndDateTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
            Date monthStartDate = Date.from(monthStartDateTime.atZone(ZoneId.systemDefault()).toInstant());
            Date monthEndDate = Date.from(monthEndDateTime.atZone(ZoneId.systemDefault()).toInstant());
            // 传入了财务月份 以终审时间查看
            // dto.setStartFinalCheckDate(monthStartDate);
            // dto.setEndFinalCheckDate(monthEndDate);

            dto.setStartFinalCheckOrCreateDate(monthStartDate); dto.setEndFinalCheckOrCreateDate(monthEndDate);
        }
        if (Objects.nonNull(beginDeliveryDate) && Objects.nonNull(endDeliveryDate)) {
            // 送检时间的就是样本创建时间
            dto.setStartCreateDate(beginDeliveryDate); dto.setEndCreateDate(endDeliveryDate);
            //            dto.setStartFinalCheckOrCreateDate(beginDeliveryDate);
            //            dto.setEndFinalCheckOrCreateDate(endDeliveryDate);

        }
    }
}
