package com.labway.lims.statistics.service.bySendDoctor;

import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsResponseDto;
import com.labway.lims.statistics.service.chain.bySendDoctor.BySendDoctorChain;
import com.labway.lims.statistics.service.chain.bySendDoctor.BySendDoctorContext;
import com.labway.lims.statistics.vo.HspOrgSendDoctorStatisticsRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class NewHspOrgBySendDoctorServiceImpl implements HspOrgBySendDoctorService {

    @Resource
    private BySendDoctorChain bySendDoctorChain;

    @Override
    public String version() {
        return "2.0";
    }

    @Override
    public HspOrgSendDoctorStatisticsResponseDto hspOrgBySendDoctorStatistics(HspOrgSendDoctorStatisticsRequestVo vo) {
        final BySendDoctorContext bySendDoctorContext = new BySendDoctorContext();

        bySendDoctorContext.put(BySendDoctorContext.FINANCE_STATISTICS_QUERY, vo);

        try {
            if (!bySendDoctorChain.execute(bySendDoctorContext)) {
                throw new IllegalStateException("送机医生统计计算失败");
            }

            return bySendDoctorContext.getSendDoctorResult();
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
    }
}
