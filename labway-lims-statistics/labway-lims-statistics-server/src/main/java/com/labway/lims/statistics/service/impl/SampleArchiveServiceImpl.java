package com.labway.lims.statistics.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.statistics.dto.SampleArchiveDto;
import com.labway.lims.statistics.dto.SampleArchivePageDto;
import com.labway.lims.statistics.dto.SampleArchiveQueryDto;
import com.labway.lims.statistics.dto.SampleArchiveReportItemDto;
import com.labway.lims.statistics.mapper.SampleArchiveMapper;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.service.SampleArchiveService;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * SampleArchiveServiceImpl
 * 样本归档信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/31 13:15
 */
@Service
public class SampleArchiveServiceImpl implements SampleArchiveService {

    @Resource
    private SampleArchiveMapper sampleArchiveMapper;
    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    /**
     * 样本归档信息查询
     * @param queryDto
     * @return
     */
    @Override
    public Page<SampleArchiveDto> selectSampleArchive(SampleArchiveQueryDto queryDto) {
        Page<SampleArchiveDto> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        QueryWrapper<SampleArchiveDto> wrapper = new QueryWrapper<>();
        wrapper.eq("rack_logic_space.is_delete", 0)// 未删除
                .eq("apply_sample.is_archive", 1)// 已归档
                .eq("apply_sample_item.group_id", queryDto.getGroupId())// 当前专业组
                .gt("rack_logic_space.create_date", queryDto.getStartCreateDate())
                .lt("rack_logic_space.create_date", queryDto.getEndCreateDate())
                .eq(Objects.nonNull(queryDto.getCreatorId()),
                        "rack_logic_space.creator_id", queryDto.getCreatorId())
                .eq(StringUtils.isNotBlank(queryDto.getBarcode()),
                        "apply_sample.barcode", queryDto.getBarcode())
                .eq(StringUtils.isNotBlank(queryDto.getTestItemCode()),
                        "apply_sample_item.test_item_code", queryDto.getTestItemCode())
                .like(StringUtils.isNotBlank(queryDto.getPatientName()),
                        "apply.patient_name", queryDto.getPatientName())
                .like(StringUtils.isNotBlank(queryDto.getPatientVisitCard()),
                        "apply.patient_visit_card", queryDto.getPatientVisitCard());
        wrapper.orderByAsc("rack_logic_space.create_date");

        // 查询样本归档列表
        Page<SampleArchiveDto> list = sampleArchiveMapper.selectSampleArchivePageByWrapper(page, wrapper);
        List<SampleArchiveDto> archiveDtos = list.getRecords();
        if (CollectionUtils.isEmpty(archiveDtos)) {
            return list;
        }

        // 通过ES查询 样本编号和检验时间
        Set<Long> applySampleIds = archiveDtos.stream().map(SampleArchiveDto::getApplySampleId).collect(Collectors.toSet());
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = queryBaseSample(applySampleIds);
        Map<Long, Pair<String, Date>> sampleNoMap =
                baseSampleEsModelDtos
                        .stream()
                        .collect(Collectors.toMap(
                                BaseSampleEsModelDto::getApplySampleId,
                                dto -> Pair.of(dto.getSampleNo(), dto.getTestDate()),
                                (a, b) -> b
        ));

        // 处理行，列，样本号，检验日期字段
        archiveDtos.forEach(e -> {
            e.setRow(Objects.requireNonNullElse(e.getRow(), 0) + 1);
            e.setColumn(Objects.requireNonNullElse(e.getColumn(), 0) + 1);
            Pair<String, Date> pair = sampleNoMap.get(e.getApplySampleId());
            if (Objects.nonNull(pair)) {
                e.setSampleNo(pair.getLeft());
                e.setTestDate(pair.getRight());
            }
        });

        // 多个检验项目将检验项目合并显示
        Map<Long, SampleArchiveDto> duplicateCheckMap = new HashMap<>();
        List<SampleArchiveDto> records =
                archiveDtos.stream()
                        .filter(dto -> {
                            Long applySampleId = dto.getApplySampleId();
                            SampleArchiveDto exist = duplicateCheckMap.get(applySampleId);
                            if (Objects.nonNull(exist)) {
                                exist.setTestItemCode(String.join(StringPool.COMMA, exist.getTestItemCode(), dto.getTestItemCode()));
                                exist.setTestItemName(String.join(StringPool.COMMA, exist.getTestItemName(), dto.getTestItemName()));
                                return false;
                            }
                            duplicateCheckMap.put(applySampleId, dto);
                            return true;
                        }).collect(Collectors.toList());
        list.setRecords(records);

        return list;
    }

    @Override
    public SampleArchivePageDto selectSampleArchivePage(SampleArchiveQueryDto queryDto) {
        SampleArchivePageDto pageDto = new SampleArchivePageDto();
        pageDto.setCurrent(queryDto.getPageNo());
        pageDto.setSize(queryDto.getPageSize());

        // 查询样本归档列表
        List<SampleArchiveDto> archiveDtos = sampleArchiveMapper.selectSampleArchive(queryDto);
        if (CollectionUtils.isEmpty(archiveDtos)) {
            return pageDto;
        }
        int total = archiveDtos.size();
        pageDto.setTotal(total);

        // 内存分页
        int from = (queryDto.getPageNo() - 1) * queryDto.getPageSize();
        int toIndex = Math.min((from + queryDto.getPageSize()), total);
        if (from > total) {
            return pageDto;
        }
        // int toIndex = Math.min((queryDto.getPageSize()), total);
        archiveDtos = archiveDtos.subList(from, toIndex);
        Long searchAfter = archiveDtos.get(archiveDtos.size() - 1).getRackLogicSpaceId();

        // 通过ES查询 样本编号和检验时间
        Set<Long> applySampleIds = archiveDtos.stream().map(SampleArchiveDto::getApplySampleId).collect(Collectors.toSet());
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = queryBaseSample(applySampleIds);
        Map<Long, Pair<String, Date>> sampleNoMap = baseSampleEsModelDtos.stream().collect(Collectors.toMap(
                BaseSampleEsModelDto::getApplySampleId, dto -> Pair.of(dto.getSampleNo(), dto.getTestDate()), (a, b) -> b));

        // 处理行，列，样本号，检验日期字段
        archiveDtos.forEach(e -> {
            e.setRow(Objects.requireNonNullElse(e.getRow(), 0) + 1);
            e.setColumn(Objects.requireNonNullElse(e.getColumn(), 0) + 1);
            Pair<String, Date> pair = sampleNoMap.get(e.getApplySampleId());
            if (Objects.nonNull(pair)) {
                e.setSampleNo(pair.getLeft());
                e.setTestDate(pair.getRight());
            }
        });

        pageDto.setRecords(archiveDtos);
        pageDto.setSearchAfter((toIndex < total) ? searchAfter : null);

        return pageDto;
    }

    @Override
    public SampleArchiveDto selectSampleArchive(String barcode, Long groupId) {
        if (StringUtils.isBlank(barcode)) {
            throw new IllegalArgumentException("条码不能为空！");
        }

        SampleArchiveDto sampleArchiveDto = sampleArchiveMapper.selectSampleArchiveByBarcode(barcode, groupId);
        Assert.notNull(sampleArchiveDto, "样本未归档！");
        // 通过ES查询 样本编号和检验时间
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = queryBaseSample(Set.of(sampleArchiveDto.getApplySampleId()));
        Map<Long, Pair<String, Date>> sampleNoMap = baseSampleEsModelDtos.stream().collect(Collectors.toMap(
                BaseSampleEsModelDto::getApplySampleId, dto -> Pair.of(dto.getSampleNo(), dto.getTestDate()), (a, b) -> b));

        // 处理行，列，样本号，检验日期字段
        sampleArchiveDto.setRow(Objects.requireNonNullElse(sampleArchiveDto.getRow(), 0) + 1);
        sampleArchiveDto.setColumn(Objects.requireNonNullElse(sampleArchiveDto.getColumn(), 0) + 1);
        Pair<String, Date> pair = sampleNoMap.get(sampleArchiveDto.getApplySampleId());
        if (Objects.nonNull(pair)) {
            sampleArchiveDto.setSampleNo(pair.getLeft());
            sampleArchiveDto.setTestDate(pair.getRight());
        }

        return sampleArchiveDto;
    }

    /**
     * 根据申请单样本ID查询报告项目结果
     * @param applySampleId
     * @return
     */
    @Override
    public List<SampleArchiveReportItemDto> selectReportItem(Long applySampleId) {
        if (Objects.isNull(applySampleId)) {
            throw new IllegalStateException("申请单样本ID必传");
        }

        List<BaseSampleEsModelDto> baseSampleEsModelDtos = queryBaseSample(Set.of(applySampleId));

        if (CollectionUtils.isEmpty(baseSampleEsModelDtos)) {
            return Collections.emptyList();
        }

        BaseSampleEsModelDto baseSampleEsModelDto = baseSampleEsModelDtos.get(0);

        return buildReportItems(baseSampleEsModelDto);
    }

    public List<BaseSampleEsModelDto> queryBaseSample(Set<Long> applySampleIds) {
        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.pageNo(NumberUtils.INTEGER_ONE).pageSize(Integer.MAX_VALUE);

        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }
        // 申请单样本ID
        builder.applySampleIds(applySampleIds);

        return elasticSearchSampleService.selectSamples(builder.build());
    }

    private List<SampleArchiveReportItemDto> buildReportItems(BaseSampleEsModelDto esModelDto) {
        String itemType = esModelDto.getItemType();
        SimpleBaseSampleEsDto sampleEsDto = null;
        switch (ItemTypeEnum.getByName(itemType)) {
            case OUTSOURCING:
            case ROUTINE:
            case INFECTION:
                sampleEsDto = JSON.parseObject(JSON.toJSONString(esModelDto), SimpleBaseSampleEsDto.class);
                break;
            default:
                break;
        }

        if (Objects.nonNull(sampleEsDto) && CollectionUtils.isNotEmpty(sampleEsDto.getReportItems())) {
            String instrumentGroupName = sampleEsDto.getInstrumentGroupName();
            return sampleEsDto.getReportItems().stream().map(reportItem -> {
                SampleArchiveReportItemDto reportItemDto = new SampleArchiveReportItemDto();
                reportItemDto.setInstrumentGroupName(instrumentGroupName);
                reportItemDto.setReportItemName(reportItem.getReportItemName());
                reportItemDto.setResult(reportItem.getResult());
                reportItemDto.setUnit(reportItem.getUnit());
                reportItemDto.setJudge(reportItem.getJudge());
                reportItemDto.setRange(reportItem.getRange());
                return reportItemDto;
            }).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Getter
    @Setter
    static
    class SimpleBaseSampleEsDto {
        /**
         * 专业小组
         */
        private String instrumentGroupName;
        /**
         * 报告项目
         */
        private List<SimpleReportItem> reportItems;

        /**
         * 报告项目
         */
        @Setter
        @Getter
        static final class SimpleReportItem extends BaseSampleEsModelDto.ReportItem {
            /**
             * 结果范围
             */
            private String range;
            /**
             * 结果 （经过一系列的计算 转换最终得到的结果值）
             */
            private String result;
            /**
             * 检验判定 UP  DOWN  NORMAL
             * @see TestJudgeEnum
             */
            private String judge;
        }

    }
}
