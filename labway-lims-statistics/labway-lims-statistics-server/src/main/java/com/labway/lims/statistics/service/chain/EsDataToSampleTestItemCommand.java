package com.labway.lims.statistics.service.chain;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.DefaultHspOrg;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SendTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleTestItemDto;
import com.labway.lims.apply.api.dto.TestItemIncomeFilterDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.base.api.dto.HspOrgDiscountDto;
import com.labway.lims.base.api.dto.HspOrgPricingDto;
import com.labway.lims.base.api.dto.HspOrgSpecialOfferDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDetailDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrgDiscountService;
import com.labway.lims.base.api.service.HspOrgPricingService;
import com.labway.lims.base.api.service.HspOrgSpecialOfferService;
import com.labway.lims.base.api.service.ItemPriceBasePackageDetailService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.api.vo.QueryCombinePackageListTestItemsVo;
import com.labway.lims.statistics.service.chain.byPlateform.PlatformStatisticsContext;
import joptsimple.internal.Strings;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class EsDataToSampleTestItemCommand implements Command {

    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private HspOrgPricingService hspOrgPricingService;

    @DubboReference
    private HspOrgSpecialOfferService hspOrgSpecialOfferService;

    @DubboReference
    private HspOrgDiscountService hspOrgDiscountService;
    @DubboReference
    private ItemPriceBasePackageService itemPriceBasePackageService;
    @DubboReference
    private ItemPriceBasePackageDetailService itemPriceBasePackageDetailService;
    @DubboReference
    private SnowflakeService snowflakeService;

    private static final SimpleDateFormat FORMATTER = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public boolean execute(Context context) {
        final PlatformStatisticsContext from = PlatformStatisticsContext.from(context);
        final boolean needFinanceGroup = from.getNeedFinanceGroup();
        final TestItemIncomeFilterDto filterDto = from.getFilterDto();

        // 数据拆解 至样本检验项目 只要正常状态的(为避免重复查询终止收费的)
        List<SampleTestItemDto> targetList = this.getSampleTestItemDtos(from.getBaseEsData()).stream()
                .filter(obj -> Objects.equals(obj.getStopStatus(), StopTestStatus.NO_STOP_TEST.getCode()))
                .filter(obj -> !Objects.equals(obj.getItemType(), ItemTypeEnum.PATHOLOGY.name()))
                .collect(Collectors.toList());

        // 终止检验的只要终止收费的 加入计算数据 且非病理检验的
        targetList.addAll(this.getSampleTestItemDtos(from.getStopEsData()).stream()
                .filter(obj -> Objects.equals(obj.getStopStatus(), StopTestStatus.STOP_TEST_CHARGE.getCode()))
                .filter(obj -> !Objects.equals(obj.getItemType(), ItemTypeEnum.PATHOLOGY.name()))
                .collect(Collectors.toList()));

        // 病理检验 加入计算数据 过虑掉 终止不收费
        targetList.addAll(this.getSampleTestItemDtos(from.getPathologyEsData()).stream()
                .filter(obj -> !Objects.equals(obj.getStopStatus(), StopTestStatus.STOP_TEST_FREE.getCode()))
                .collect(Collectors.toList()));

        if (needFinanceGroup) {
            // 需要 赋值财务专业组
            final Set<Long> testItemIds =
                    targetList.stream().map(SampleTestItemDto::getTestItemId).collect(Collectors.toSet());
            final Map<Long, TestItemDto> testItemByTestItemId = testItemService.selectByTestItemIds(testItemIds).stream()
                    .collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));

            targetList.forEach(item -> {
                TestItemDto testItemDto = testItemByTestItemId.get(item.getTestItemId());
                if (Objects.isNull(testItemDto)) {
                    return;
                }
                item.setFinanceGroupCode(testItemDto.getFinanceGroupCode());
                item.setFinanceGroupName(testItemDto.getFinanceGroupName());
            });
        }

        // 所有送检机构
        final Set<Long> hspOrgIdList = targetList.stream().map(SampleTestItemDto::getHspOrgId)
                .filter(Objects::nonNull).collect(Collectors.toSet());

        // 最小 样本检验项目 创建时间
        final Date minSampleTestItemCreateDateAfterFilter = targetList.stream().map(SampleTestItemDto::getCreateDate)
                .filter(Objects::nonNull).min(Comparator.naturalOrder()).orElse(null);
        // 最大 样本检验项目 创建时间
        final Date maxSampleTestItemCreateDateAfterFilter = targetList.stream().map(SampleTestItemDto::getCreateDate)
                .filter(Objects::nonNull).max(Comparator.naturalOrder()).orElse(null);

        // 1.  特 价
        // 获取特价项目折扣并给特价项目数据打tag
        // 每个样本 下单个检验项目 若使用特价项目 只会存在一个 key: SampleTestItemDto 唯一key value:特价项目
        Map<String, HspOrgSpecialOfferDto> hspOrgSpecialOfferDtoBySampleTestItem =
                this.getHspOrgSpecialOfferDtoBySampleTestItem(targetList, hspOrgIdList,
                        minSampleTestItemCreateDateAfterFilter, maxSampleTestItemCreateDateAfterFilter);

        // 2. 客户折扣维护
        // 获取客户折扣维护以及基准包去算财务套餐
        final Map<Long, List<HspOrgDiscountDto>> hspOrgDiscountGroupingByHspOrgId =
                this.getHspOrgDiscountDtos(targetList, minSampleTestItemCreateDateAfterFilter, maxSampleTestItemCreateDateAfterFilter);

        // 项目基准包
        final List<ItemPriceBasePackageItemDto> itemPriceBasePackageDtos =
                this.getItemPriceBasePackageDtos(minSampleTestItemCreateDateAfterFilter, maxSampleTestItemCreateDateAfterFilter);

        // 3. 将 特价 和 客户折扣（基准包） 数据都写入对应的样本检验项目中
        targetList.parallelStream().forEach(sampleTestItemDto ->
                this.setSampleTestItem(sampleTestItemDto, hspOrgSpecialOfferDtoBySampleTestItem,
                        hspOrgDiscountGroupingByHspOrgId, itemPriceBasePackageDtos));

        // 4. 组合财务套餐， 将财务套餐数据加入原始数据，
        this.filterCombinePackage(targetList, from);

        // 5. 将客户折扣中未组成财务套餐的数据匹配项目价格基准包下的检验项目
        targetList.parallelStream().filter(e -> Objects.nonNull(e.getPackageId())).forEach(sampleTestItemDto ->
                this.currentDiscount(sampleTestItemDto, hspOrgDiscountGroupingByHspOrgId, itemPriceBasePackageDtos));

        // 阶 梯 折 扣
        // 6. 每个样本 下单个检验项目 若使用客户阶梯折扣只会存在一个
        this.getHspOrgPricingBySampleTestItem(targetList, hspOrgIdList, hspOrgSpecialOfferDtoBySampleTestItem,
                minSampleTestItemCreateDateAfterFilter, maxSampleTestItemCreateDateAfterFilter);

        // 组完财务套餐后过滤检验项目
        targetList = filterByTestItemIncomeFilterDto(filterDto, targetList);

        from.put(PlatformStatisticsContext.SAMPLE_TEST_ITEM_LIST, targetList);

        return CONTINUE_PROCESSING;
    }

    /**
     * 客户折扣维护
     */
    private void currentDiscount(SampleTestItemDto sampleTestItemDto,
                                 Map<Long, List<HspOrgDiscountDto>> hspOrgDiscountGroupingByHspOrgId,
                                 List<ItemPriceBasePackageItemDto> itemPriceBasePackageDtos) {

        // 3. 项目价格基准包 、客户折扣 获取 折扣率
        HspOrgDiscountDto hspOrgDiscountDto = this.getHspOrgDiscountDto(sampleTestItemDto, hspOrgDiscountGroupingByHspOrgId);

        if (Objects.isNull(hspOrgDiscountDto)) {
            // 不存在 有效的客户折扣 折扣为1 取原价
            sampleTestItemDto.setDiscount(BigDecimal.ONE);
            return;
        }
        // 检验项目 对应价格基准包
        ItemPriceBasePackageDto priceBasePackageDto = itemPriceBasePackageDtos.stream()
                // 与客户折扣上基准包一致
                .filter(obj -> Objects.equals(obj.getPackageId(), hspOrgDiscountDto.getPackageId()))
                // 包含此检验项目的 价格基准包
                .filter(obj -> obj.getTestItemIds().contains(sampleTestItemDto.getTestItemId()))
                // 检验项目创建时间 在 项目价格基准包生效时间范围内的数据
                .filter(item -> sampleTestItemDto.getCreateDate().compareTo(item.getStartDate()) >= 0
                        && sampleTestItemDto.getCreateDate().compareTo(item.getEndDate()) <= 0)
                // 若存在应当只存在一条
                .findFirst().orElse(null);

        if (Objects.isNull(priceBasePackageDto)) {
            // 不存在 对应价格基准包 折扣为1 取原价
            sampleTestItemDto.setDiscount(BigDecimal.ONE);
            return;
        }

        // 存在对应有效的客户折扣 取其折扣率
        sampleTestItemDto.setDiscount(hspOrgDiscountDto.getDiscount());
        sampleTestItemDto.setPackageId(hspOrgDiscountDto.getPackageId());
    }


    /**
     * 过滤财务套餐
     */
    private void filterCombinePackage(List<SampleTestItemDto> targetList, PlatformStatisticsContext from) {

        // 送检机构id，  财务套餐id
        final Map<Long, List<QueryCombinePackageListTestItemsVo>> packageIdCombinePackageMap = from.getCombinePackageMap();

        // 根据 packageId基准包分组  过滤非特价的数据 和 有基准包的数据
        final Map<Long, List<SampleTestItemDto>> sampleTestItemByPackageIdMap = targetList.stream()
                .filter(e -> Objects.nonNull(e.getPackageId()))
                .filter(e -> Objects.equals(e.getIsFree(), YesOrNoEnum.NO.getCode()))
                .filter(e -> !e.isSpecialOfferFlag())
                .collect(Collectors.groupingBy(SampleTestItemDto::getPackageId));

        // 处理财务套餐组成的数据
        this.combinePackage(sampleTestItemByPackageIdMap, packageIdCombinePackageMap, targetList);

        // 删除走套餐的所有数据
        targetList.removeIf(SampleTestItemDto::isCombinePackageFlag);
    }


    /**
     * 处理非特价的数据, 组成财务套餐
     *
     * @param sampleTestItemByPackageIdMap 基准包id  样本
     * @param packageIdCombinePackageMap   基准包id  套餐
     */
    private void combinePackage(Map<Long, List<SampleTestItemDto>> sampleTestItemByPackageIdMap,
                                Map<Long, List<QueryCombinePackageListTestItemsVo>> packageIdCombinePackageMap,
                                List<SampleTestItemDto> targetList) {


        for (Map.Entry<Long, List<SampleTestItemDto>> next : sampleTestItemByPackageIdMap.entrySet()) {
            final Long packageId = next.getKey();
            final List<SampleTestItemDto> sampleTestItemList = next.getValue();

            // 过滤同人同天的数据
            // 先按送检机构分组， 再按天分组，  再按照身份证分组身份证一样，   再判断 姓名， 性别一样  因为送检类型已经确认了，有 packageId 相同的的都是同一个送检类型
            //      hspOrgId   Date年月日    SampleTestItemList
            final Map<Long, Map<String, List<SampleTestItemDto>>> sampleTestItemByHspAndDateMap = sampleTestItemList.stream()
                    .collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId,
                            Collectors.groupingBy(e -> FORMATTER.format(e.getCreateDate()), Collectors.toList())));

            // 基准包下的套餐
            final List<QueryCombinePackageListTestItemsVo> combinePackageAllList = packageIdCombinePackageMap.getOrDefault(packageId, Collections.emptyList());

            sampleTestItemByHspAndDateMap.entrySet().parallelStream().forEach(byHspOrg -> {
                final Long hspOrgId = byHspOrg.getKey();
                final Map<String, List<SampleTestItemDto>> byDateData = byHspOrg.getValue();

                // 当前机构
                final List<QueryCombinePackageListTestItemsVo> combinePackageListTestItemsVoList = combinePackageAllList.stream()
                        .filter(combinePackage -> Objects.equals(hspOrgId, combinePackage.getHspOrgId()))
                        .collect(Collectors.toList());
                this.currentCombinePackageByHspOrg(byDateData, combinePackageListTestItemsVoList, targetList);


                // 通用机构
                final List<QueryCombinePackageListTestItemsVo> defaultHspOrgCombinePackageList = combinePackageAllList.stream()
                        .filter(combinePackage -> Objects.equals(DefaultHspOrg.DEFAULT_HSP_ORG_ID, combinePackage.getHspOrgId()))
                        .collect(Collectors.toList());
                this.currentCombinePackageByHspOrg(byDateData, defaultHspOrgCombinePackageList, targetList);

            });

        }
    }

    /**
     * 处理同人同天的数据  因为都是由基准包的数据， 所有肯定是同一个送检类型
     *
     * @param sampleTestItemByDateMap           每天的样本
     * @param combinePackageListTestItemsVoList 当前机构的财务套餐
     */
    private void currentCombinePackageByHspOrg(Map<String, List<SampleTestItemDto>> sampleTestItemByDateMap,
                                               List<QueryCombinePackageListTestItemsVo> combinePackageListTestItemsVoList,
                                               List<SampleTestItemDto> targetList) {

        if (CollectionUtils.isEmpty(combinePackageListTestItemsVoList)) {
            return;
        }

        sampleTestItemByDateMap.entrySet().parallelStream().forEach(data -> {

            final Map<String, List<SampleTestItemDto>> byPatientCardMap = data.getValue().stream()
                    // 过滤身份证号不为空的
                    .filter(e -> StringUtils.isNotBlank(e.getPatientCard()))
                    // 过滤已经走了套餐的
                    .filter(e -> !e.isCombinePackageFlag())
                    .collect(Collectors.groupingBy(SampleTestItemDto::getPatientCard));
            // 统计套餐数量
            this.currentCombinePackageByPatient(byPatientCardMap, combinePackageListTestItemsVoList, targetList);


            final Map<String, List<SampleTestItemDto>> byPatientNameAndSexMap = data.getValue().stream()
                    // 过滤身份证号为空的
                    .filter(e -> StringUtils.isBlank(e.getPatientCard()))
                    // 过滤已经走了套餐的
                    .filter(e -> !e.isCombinePackageFlag())
                    .collect(Collectors.groupingBy(e -> e.getPatientName() + "-" + e.getPatientSex()));
            // 统计套餐数量
            this.currentCombinePackageByPatient(byPatientNameAndSexMap, combinePackageListTestItemsVoList, targetList);
        });
    }

    /**
     * 处理同人同天的数据
     *
     * @param testItemGroupMap                  分组过后的检验项目数据  每一个value中的 折扣肯定是相同的
     * @param combinePackageListTestItemsVoList 套餐数据
     */
    private void currentCombinePackageByPatient(Map<String, List<SampleTestItemDto>> testItemGroupMap,
                                                List<QueryCombinePackageListTestItemsVo> combinePackageListTestItemsVoList,
                                                List<SampleTestItemDto> targetList) {


        testItemGroupMap.values().parallelStream().forEach(sampleTestItemGroupList -> {

            final SampleTestItemDto sampleTestItemDtoTmp = sampleTestItemGroupList.get(NumberUtils.INTEGER_ZERO);

            for (QueryCombinePackageListTestItemsVo combinePackage : combinePackageListTestItemsVoList) {

                // 用户做的项目  为啥用grouping 因为会有同一个人同一天同一个项目做两次。。。
                final Map<String, List<SampleTestItemDto>> patientTestItemCodeMap = sampleTestItemGroupList.stream().collect(Collectors.groupingBy(SampleTestItemDto::getTestItemCode));
                // 套餐里面的项目
                final Set<String> combinePackageTestCodes = combinePackage.getTestItemDtoList().stream().map(TestItemDto::getTestItemCode).collect(Collectors.toSet());

                // 用户做的项目包含套餐的项目， 则用户做的项目进行套餐组合
                if (!patientTestItemCodeMap.keySet().containsAll(combinePackageTestCodes)) {
                    continue;
                }

                // 获取套餐检验项目下做过的最小数量 (因为 同一天 同一个人 在 同一个送检机构 做这个套餐N次，)
                final Integer minTestCount = patientTestItemCodeMap.entrySet().stream()
                        .filter(e -> combinePackageTestCodes.contains(e.getKey()))
                        // 获取value最小的
                        .map(e -> e.getValue().size())
                        .min(Comparator.comparing(Integer::intValue))
                        // 至少会有一个
                        .orElse(NumberUtils.INTEGER_ONE);

                // 收集
                final Map<String, String> financeGroupMap = new TreeMap<>();
                for (Map.Entry<String, List<SampleTestItemDto>> entry : patientTestItemCodeMap.entrySet()) {
                    String combinePackageCode = entry.getKey();
                    List<SampleTestItemDto> sampleTestItem = entry.getValue();
                    if (!combinePackageTestCodes.contains(combinePackageCode)) {
                        continue;
                    }
                    final SampleTestItemDto sampleTestItemDto = sampleTestItem.get(NumberUtils.INTEGER_ZERO);
                    // 循环最小次数打tag
                    for (int i = 0; i < minTestCount; i++) {
                        // 项目属于套餐中
                        sampleTestItem.get(i).setCombinePackageFlag(Boolean.TRUE);
                    }

                    if (StringUtils.isNotBlank(sampleTestItemDto.getFinanceGroupCode()) && StringUtils.isNotBlank(sampleTestItemDto.getFinanceGroupName())) {
                        financeGroupMap.put(sampleTestItemDto.getFinanceGroupCode(), sampleTestItemDto.getFinanceGroupName());
                    }
                }
//                final Map<String, String> collect = patientTestItemCodeMap.entrySet().stream()
//                        .filter(e -> combinePackageTestCodes.contains(e.getKey()))
//                        .map(e -> {
//                            final SampleTestItemDto dto = e.getValue().get(NumberUtils.INTEGER_ZERO);
//                            // 循环最小次数打tag
//                            for (Integer i = 0; i < minTestCount; i++) {
//                                // 项目属于套餐中
//                                e.getValue().get(i).setCombinePackageFlag(Boolean.TRUE);
//                            }
//                            // 顺便收集财务专业组
//                            return Map.entry(dto.getFinanceGroupCode(), dto.getFinanceGroupName());
//                        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> a));
                // 后续会用财务专业组排序
                sampleTestItemDtoTmp.setFinanceGroupCode(StringUtils.defaultString(CollUtil.join(financeGroupMap.keySet(), ",")));
                sampleTestItemDtoTmp.setFinanceGroupName(StringUtils.defaultString(CollUtil.join(financeGroupMap.values(), ",")));
                // 收集套餐信息
                targetList.add(this.getSampleTestItemDto(sampleTestItemDtoTmp, combinePackage, minTestCount));
            }
        });
    }


    /**
     * 获取财务套餐统计的数据
     */
    private SampleTestItemDto getSampleTestItemDto(SampleTestItemDto tempSampleTestItemDto, QueryCombinePackageListTestItemsVo combinePackage, Integer testCount) {
        // 批量获取id随机数  applyId  applySampleId applySampleItemId
        final LinkedList<Long> ids = snowflakeService.genIds(3);

        final SampleTestItemDto temp = new SampleTestItemDto();
        temp.setOutBarcode(Strings.EMPTY);
        temp.setPatientName(tempSampleTestItemDto.getPatientName());
        temp.setPatientSex(tempSampleTestItemDto.getPatientSex());
        temp.setPatientAge(tempSampleTestItemDto.getPatientAge());
        temp.setPatientSubage(tempSampleTestItemDto.getPatientSubage());
        temp.setPatientSubageUnit(tempSampleTestItemDto.getPatientSubageUnit());
        temp.setHspOrgName(tempSampleTestItemDto.getHspOrgName());
        temp.setHspOrgId(tempSampleTestItemDto.getHspOrgId());
        temp.setSendDoctorName(tempSampleTestItemDto.getSendDoctorName());
        temp.setDept(tempSampleTestItemDto.getDept());
        // 随机数， 以保证数据唯一
        temp.setApplyId(ids.pop());
        temp.setApplySampleId(ids.pop());
        temp.setApplySampleItemId(ids.pop());
        temp.setApplyTypeCode(tempSampleTestItemDto.getApplyTypeCode());
        temp.setApplyTypeName(tempSampleTestItemDto.getApplyTypeName());
        temp.setTestItemId(NumberUtils.LONG_ZERO);
        temp.setTestItemCode(combinePackage.getCombinePackageCode());
        temp.setTestItemName(combinePackage.getCombinePackageName());
        temp.setCount(ObjectUtils.defaultIfNull(testCount, NumberUtils.INTEGER_ONE));
        temp.setDiscount(tempSampleTestItemDto.getDiscount());
        temp.setPrice(combinePackage.getCombinePackagePrice());
        temp.setIsFree(YesOrNoEnum.NO.getCode());
        temp.setCreateDate(tempSampleTestItemDto.getCreateDate());
        temp.setPatientCard(tempSampleTestItemDto.getPatientCard());
        temp.setFinanceGroupCode(tempSampleTestItemDto.getFinanceGroupCode());
        temp.setFinanceGroupName(tempSampleTestItemDto.getFinanceGroupName());
        temp.setTheCombinePackage(Boolean.TRUE);
        temp.setSignDate(tempSampleTestItemDto.getSignDate());
        temp.setApplyDate(tempSampleTestItemDto.getApplyDate());
        return temp;
    }

    /**
     * 写入对应的折扣数据
     *
     * @param sampleTestItemDto                     当前样本检验项目
     * @param hspOrgSpecialOfferDtoBySampleTestItem 特价折扣
     * @param hspOrgDiscountGroupingByHspOrgId      客户折扣
     * @param itemPriceBasePackageDtos              项目基准包
     */
    private void setSampleTestItem(SampleTestItemDto sampleTestItemDto,
                                   Map<String, HspOrgSpecialOfferDto> hspOrgSpecialOfferDtoBySampleTestItem,
                                   Map<Long, List<HspOrgDiscountDto>> hspOrgDiscountGroupingByHspOrgId,
                                   List<ItemPriceBasePackageItemDto> itemPriceBasePackageDtos) {
        // 1. 特价项目
        HspOrgSpecialOfferDto specialOfferDto =
                hspOrgSpecialOfferDtoBySampleTestItem.get(sampleTestItemDto.uniqueKey());
        if (Objects.nonNull(specialOfferDto)) {
            // 若存在 特价项目 则此检验项目 折扣率确定 为特价项目上 维护的折扣率
            sampleTestItemDto.setDiscount(specialOfferDto.getDiscount());
            // 计算价格 使用维护的折前价格 并保留 一个标记、且留下折后价格
            sampleTestItemDto.setPrice(specialOfferDto.getFeePrice());
            sampleTestItemDto.setSpecialOfferFlag(Boolean.TRUE);
            sampleTestItemDto.setDiscountPrice(specialOfferDto.getDiscountPrice());
            return;
        }

        // 3. 项目价格基准包 、客户折扣 获取 折扣率
        HspOrgDiscountDto hspOrgDiscountDto = this.getHspOrgDiscountDto(sampleTestItemDto, hspOrgDiscountGroupingByHspOrgId);

        if (Objects.isNull(hspOrgDiscountDto)) {
            // 不存在 有效的客户折扣 折扣为1 取原价
            sampleTestItemDto.setDiscount(BigDecimal.ONE);
            return;
        }

        // 检验项目 对应价格基准包
        ItemPriceBasePackageDto priceBasePackageDto = itemPriceBasePackageDtos.stream()
                // 与客户折扣上基准包一致
                .filter(obj -> Objects.equals(obj.getPackageId(), hspOrgDiscountDto.getPackageId()))
                // 检验项目创建时间 在 项目价格基准包生效时间范围内的数据
                .filter(item -> sampleTestItemDto.getCreateDate().compareTo(item.getStartDate()) >= 0
                        && sampleTestItemDto.getCreateDate().compareTo(item.getEndDate()) <= 0)
                // 若存在应当只存在一条
                .findFirst().orElse(null);

        if (Objects.isNull(priceBasePackageDto)) {
            // 不存在 对应价格基准包 折扣为1 取原价
            sampleTestItemDto.setDiscount(BigDecimal.ONE);
            return;
        }

        // 存在对应有效的客户折扣 取其折扣率
        sampleTestItemDto.setDiscount(hspOrgDiscountDto.getDiscount());
        sampleTestItemDto.setPackageId(hspOrgDiscountDto.getPackageId());
    }

    private List<ItemPriceBasePackageItemDto> getItemPriceBasePackageDtos(Date minSampleTestItemCreateDateAfterFilter, Date maxSampleTestItemCreateDateAfterFilter) {
        List<ItemPriceBasePackageItemDto> itemPriceBasePackageDtos = Lists.newArrayList();
        if (Objects.nonNull(minSampleTestItemCreateDateAfterFilter)
                && Objects.nonNull(maxSampleTestItemCreateDateAfterFilter)) {
            itemPriceBasePackageDtos = itemPriceBasePackageItemDtoListFromTbObjDto(itemPriceBasePackageService
                    .selectByDateRange(minSampleTestItemCreateDateAfterFilter, maxSampleTestItemCreateDateAfterFilter));
            // 所有基准包ids
            Set<Long> packageIds = itemPriceBasePackageDtos.stream().map(ItemPriceBasePackageDto::getPackageId)
                    .collect(Collectors.toSet());
            Map<Long,
                    Set<Long>> testItemIdsByPackageIds = itemPriceBasePackageDetailService.selectByPackageIds(packageIds)
                    .stream().collect(Collectors.groupingBy(ItemPriceBasePackageDetailDto::getPackageId,
                            Collectors.mapping(ItemPriceBasePackageDetailDto::getTestItemId, Collectors.toSet())));
            itemPriceBasePackageDtos.forEach(item -> item
                    .setTestItemIds(testItemIdsByPackageIds.getOrDefault(item.getPackageId(), Collections.emptySet())));
        }
        return itemPriceBasePackageDtos;
    }


    private Map<Long, List<HspOrgDiscountDto>> getHspOrgDiscountDtos(List<SampleTestItemDto> targetList,
                                                                     Date minSampleTestItemCreateDateAfterFilter,
                                                                     Date maxSampleTestItemCreateDateAfterFilter) {
        // 所有送检机构
        Set<Long> hspOrgIdListAfterFilter = targetList.stream().map(SampleTestItemDto::getHspOrgId)
                .filter(Objects::nonNull).collect(Collectors.toSet());

        // 所有就诊类型
        Set<String> applyTypeListAfterFilter = targetList.stream().map(SampleTestItemDto::getApplyTypeCode)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 补充 全部 类型
        applyTypeListAfterFilter.add(SendTypeEnum.SEND_TYPE_ALL.getCode());

        // 客户折扣维护
        List<HspOrgDiscountDto> hspOrgDiscountDtos = Lists.newArrayList();
        if (Objects.nonNull(minSampleTestItemCreateDateAfterFilter)
                && Objects.nonNull(maxSampleTestItemCreateDateAfterFilter)
                && CollectionUtils.isNotEmpty(hspOrgIdListAfterFilter)
                && CollectionUtils.isNotEmpty(applyTypeListAfterFilter)) {
            hspOrgDiscountDtos =
                    hspOrgDiscountService.selectByHspOrgIdsAndDateRange(hspOrgIdListAfterFilter, applyTypeListAfterFilter,
                            minSampleTestItemCreateDateAfterFilter, maxSampleTestItemCreateDateAfterFilter);
        }
        return hspOrgDiscountDtos.stream().collect(Collectors.groupingBy(HspOrgDiscountDto::getHspOrgId));
    }


    /**
     * 获取样本检验项目 对应 客户折扣维护
     *
     * @param sampleTestItemDto                样本检验项目
     * @param hspOrgDiscountGroupingByHspOrgId 客户折扣 数据
     */
    private HspOrgDiscountDto getHspOrgDiscountDto(SampleTestItemDto sampleTestItemDto,
                                                   Map<Long, List<HspOrgDiscountDto>> hspOrgDiscountGroupingByHspOrgId) {
        // 基准包 对应客户折扣 从诊类型相同的客户折扣中寻找
        HspOrgDiscountDto hspOrgDiscountDto = hspOrgDiscountGroupingByHspOrgId
                .getOrDefault(sampleTestItemDto.getHspOrgId(), Collections.emptyList()).stream()
                // 与客户折扣就诊类型相同
                .filter(obj -> Objects.equals(obj.getSendTypeCode(), sampleTestItemDto.getApplyTypeCode()))
                // 检验项目创建时间 在 客户折扣生效时间范围内的数据
                .filter(item -> sampleTestItemDto.getCreateDate().compareTo(item.getStartDate()) >= 0
                        && sampleTestItemDto.getCreateDate().compareTo(item.getEndDate()) <= 0)
                // 若存在应当只存在一条
                .findFirst().orElse(null);

        if (Objects.isNull(hspOrgDiscountDto)) {
            // 就诊类型 找不到 通过全部 进行 搂底

            hspOrgDiscountDto = hspOrgDiscountGroupingByHspOrgId
                    .getOrDefault(sampleTestItemDto.getHspOrgId(), Collections.emptyList()).stream()
                    // 与客户折扣就诊类型相同
                    .filter(obj -> Objects.equals(obj.getSendTypeCode(), SendTypeEnum.SEND_TYPE_ALL.getCode()))
                    // 检验项目创建时间 在 客户折扣生效时间范围内的数据
                    .filter(item -> sampleTestItemDto.getCreateDate().compareTo(item.getStartDate()) >= 0
                            && sampleTestItemDto.getCreateDate().compareTo(item.getEndDate()) <= 0)
                    // 若存在应当只存在一条
                    .findFirst().orElse(null);

        }
        return hspOrgDiscountDto;
    }

    /**
     * 每个样本 下单个检验项目 若使用客户阶梯折扣只会存在一个 key: SampleTestItemDto 唯一key value:客户阶梯折扣
     *
     * @param sampleTestItemDtoList                 样本检验项目
     * @param hspOrgSpecialOfferDtoBySampleTestItem key: 送检机构 value：送检机构特价项目维护
     * @return key: SampleTestItemDto 唯一key value:客户阶梯折扣
     */
    private void getHspOrgPricingBySampleTestItem(
            List<SampleTestItemDto> sampleTestItemDtoList, Set<Long> hspOrgIdList,
            Map<String, HspOrgSpecialOfferDto> hspOrgSpecialOfferDtoBySampleTestItem,
            Date minSampleTestItemCreateDate, Date maxSampleTestItemCreateDate) {

        // 需要用到的客户阶梯折扣
        List<HspOrgPricingDto> hspOrgPricingDtos = Lists.newArrayList();
        if (Objects.nonNull(minSampleTestItemCreateDate) && Objects.nonNull(maxSampleTestItemCreateDate)
                && CollectionUtils.isNotEmpty(hspOrgIdList)) {
            hspOrgPricingDtos = hspOrgPricingService.selectByHspOrgIdsAndDateRange(hspOrgIdList,
                    minSampleTestItemCreateDate, maxSampleTestItemCreateDate);
        }

        Map<Long, List<HspOrgPricingDto>> hspOrgPricingGroupingByHspOrgId =
                hspOrgPricingDtos.stream().collect(Collectors.groupingBy(HspOrgPricingDto::getHspOrgId));

        // 每个样本 下单个检验项目 若使用客户阶梯折扣只会存在一个 key: SampleTestItemDto 唯一key value:客户阶梯折扣
        //        Map<String, HspOrgPricingDto> hspOrgPricingBySampleTestItem = new HashMap<>();
        // 每个 样本检验项目 使用的特检项目
        for (Map.Entry<Long, List<SampleTestItemDto>> entry : sampleTestItemDtoList.stream()
                .collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId)).entrySet()) {
            // 送检机构
            Long hspOrgId = entry.getKey();
            // 此送检机构下 样本检验项目
            List<SampleTestItemDto> sampleTestItemDtoListByHspOrgId = entry.getValue();
            // 此送检机构下 所有涉及的客户阶梯折扣
            List<HspOrgPricingDto> hspOrgPricingDtoList =
                    hspOrgPricingGroupingByHspOrgId.getOrDefault(hspOrgId, Collections.emptyList());
            // 查看 哪些检验项目 用于此阶梯折扣
            for (HspOrgPricingDto hspOrgPricingDto : hspOrgPricingDtoList) {
                // 在此客户阶梯折扣生效时间 范围内 所有样本检验项目
                List<SampleTestItemDto> filterSampleTestItemDtos = sampleTestItemDtoListByHspOrgId.stream()
                        .filter(item -> item.getCreateDate().compareTo(hspOrgPricingDto.getStartDate()) >= 0
                                && item.getCreateDate().compareTo(hspOrgPricingDto.getEndDate()) <= 0)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterSampleTestItemDtos)) {
                    continue;
                }
                // 这个生效时间范围 内样本检验项目 金额汇总
                BigDecimal priceSum = BigDecimal.ZERO;
                for (SampleTestItemDto filterSampleTestItemDto : filterSampleTestItemDtos) {
                    if (Objects.equals(filterSampleTestItemDto.getIsFree(), YesOrNoEnum.YES.getCode())) {
                        // 免单的不算入 总额
                        continue;
                    }
                    BigDecimal price = ObjectUtils.defaultIfNull(filterSampleTestItemDto.getPrice(), BigDecimal.ZERO);
                    BigDecimal count = BigDecimal.valueOf(
                            ObjectUtils.defaultIfNull(filterSampleTestItemDto.getCount(), NumberUtils.INTEGER_ONE));

                    // 获取 对应特价项目
                    HspOrgSpecialOfferDto specialOfferDto =
                            hspOrgSpecialOfferDtoBySampleTestItem.get(filterSampleTestItemDto.uniqueKey());

                    if (Objects.isNull(specialOfferDto)) {
                        // 不存在特价项目 按照原价*数量 算入总额
                        priceSum = priceSum.add(price.multiply(count));
                        continue;
                    }
                    // 存在 特价项目 且参与阶梯折扣 将打折 后金额 算入总额
                    if (Objects.equals(specialOfferDto.getIsTieredPricing(), YesOrNoEnum.YES.getCode())) {
                        priceSum = priceSum.add(specialOfferDto.getDiscountPrice().multiply(count));
                    }
                    // 不参与阶梯折扣 不算入总额
                }
                if (priceSum.compareTo(hspOrgPricingDto.getBeforeMinPrice()) >= 0
                        && priceSum.compareTo(hspOrgPricingDto.getBeforeMaxPrice()) <= 0) {

                    // 如果此 汇总金额 在此上下限范围内 则这些 样本检验项目 对应客户阶梯折扣可确定
                    filterSampleTestItemDtos.forEach(item -> {
                        item.setDiscount(hspOrgPricingDto.getDiscount());
                        item.setPricingFlag(Boolean.TRUE);
                    });
                }
            }

        }
    }

    /**
     * 每个样本 下单个检验项目 若使用特价项目 只会存在一个 key: SampleTestItemDto 唯一key value:特价项目
     *
     * @param sampleTestItemDtoList       样本检验项目
     * @param hspOrgIdList                所有送检机构
     * @param minSampleTestItemCreateDate 最小 样本检验项目 创建时间
     * @param maxSampleTestItemCreateDate 最大 样本检验项目 创建时间
     * @return key: SampleTestItemDto 唯一key value:特价项目
     */
    private Map<String, HspOrgSpecialOfferDto> getHspOrgSpecialOfferDtoBySampleTestItem(
            List<SampleTestItemDto> sampleTestItemDtoList, Set<Long> hspOrgIdList,
            Date minSampleTestItemCreateDate, Date maxSampleTestItemCreateDate) {

        // 所有就诊类型
        Set<String> sendTypeList = sampleTestItemDtoList.stream().map(SampleTestItemDto::getApplyTypeCode)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        // 补充 全部 类型
        sendTypeList.add(SendTypeEnum.SEND_TYPE_ALL.getCode());

        // 送检机构特价项目维护
        List<HspOrgSpecialOfferDto> hspOrgSpecialOfferDtos = Lists.newArrayList();
        if (Objects.nonNull(minSampleTestItemCreateDate) && Objects.nonNull(maxSampleTestItemCreateDate)
                && CollectionUtils.isNotEmpty(hspOrgIdList)) {
            hspOrgSpecialOfferDtos = hspOrgSpecialOfferService.selectByByHspOrgIdsAndDateRangeAndAndApplyTypes(
                    hspOrgIdList, minSampleTestItemCreateDate, maxSampleTestItemCreateDate, sendTypeList);
        }
        Map<Long, List<HspOrgSpecialOfferDto>> hspOrgSpecialOfferGroupingByHspOrgId =
                hspOrgSpecialOfferDtos.stream().collect(Collectors.groupingBy(HspOrgSpecialOfferDto::getHspOrgId));

        // 每个样本 下单个检验项目 若使用特价项目 只会存在一个 key: SampleTestItemDto 唯一key value:特价项目
        Map<String, HspOrgSpecialOfferDto> hspOrgSpecialOfferDtoBySampleTestItem = new HashMap<>();

        for (Map.Entry<Long, List<SampleTestItemDto>> entry : sampleTestItemDtoList.stream()
                .collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId)).entrySet()) {
            // 送检机构
            Long hspOrgId = entry.getKey();
            // 此送检机构下 样本检验项目
            List<SampleTestItemDto> sampleTestItemDtoListByHspOrgId = entry.getValue();
            // 此送检机构下 特价项目 以就诊类型分组
            Map<String, List<HspOrgSpecialOfferDto>> hspOrgSpecialOfferDtoByApplyTypeCode =
                    hspOrgSpecialOfferGroupingByHspOrgId.getOrDefault(hspOrgId, Collections.emptyList()).stream()
                            .collect(Collectors.groupingBy(HspOrgSpecialOfferDto::getSendTypeCode));
            for (SampleTestItemDto sampleTestItemDto : sampleTestItemDtoListByHspOrgId) {
                // 找与此样本检验项目 就诊类型一致 特价项目
                List<HspOrgSpecialOfferDto> filterByApplyTypeCode =
                        hspOrgSpecialOfferDtoByApplyTypeCode.get(sampleTestItemDto.getApplyTypeCode());
                if (CollectionUtils.isNotEmpty(filterByApplyTypeCode)) {
                    // 若存在 检验项目一致的
                    filterByApplyTypeCode = filterByApplyTypeCode.stream()
                            .filter(obj -> Objects.equals(obj.getTestItemId(), sampleTestItemDto.getTestItemId()))
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(filterByApplyTypeCode)) {
                    // 通过 全部 类型进行 搂底
                    filterByApplyTypeCode =
                            hspOrgSpecialOfferDtoByApplyTypeCode.get(SendTypeEnum.SEND_TYPE_ALL.getCode());
                }
                if (CollectionUtils.isEmpty(filterByApplyTypeCode)) {
                    continue;
                }
                // 获取 对应特价项目
                // 这些 特价项目 与此样本检验项目一致 切生效时间包含 创建时间的
                HspOrgSpecialOfferDto specialOfferDto = filterByApplyTypeCode.stream()
                        .filter(obj -> Objects.equals(obj.getTestItemId(), sampleTestItemDto.getTestItemId()))
                        .filter(item -> sampleTestItemDto.getCreateDate().compareTo(item.getStartDate()) >= 0
                                && sampleTestItemDto.getCreateDate().compareTo(item.getEndDate()) <= 0)
                        .findFirst().orElse(null);
                if (Objects.isNull(specialOfferDto)) {
                    continue;
                }
                // 样本检验项目 使用的 特价项目
                sampleTestItemDto.setSpecialOfferFlag(Boolean.TRUE);
                hspOrgSpecialOfferDtoBySampleTestItem.put(sampleTestItemDto.uniqueKey(), specialOfferDto);
            }

        }
        return hspOrgSpecialOfferDtoBySampleTestItem;
    }

    /**
     * 数据拆解 至样本检验项目
     */
    private List<SampleTestItemDto> getSampleTestItemDtos(List<BaseSampleEsModelDto> baseSampleEsModelDtosAll) {
        // 拆解 到检验项目
        List<SampleTestItemDto> sampleTestItemDtoList = new LinkedList<>();
        for (BaseSampleEsModelDto sampleEsModelDto : baseSampleEsModelDtosAll) {
            List<BaseSampleEsModelDto.TestItem> testItems = sampleEsModelDto.getTestItems();
            if (Objects.isNull(testItems)) {
                continue;
            }
            for (BaseSampleEsModelDto.TestItem testItem : testItems) {
                SampleTestItemDto temp = new SampleTestItemDto();
                temp.setOutBarcode(sampleEsModelDto.getOutBarcode());
                temp.setPatientName(sampleEsModelDto.getPatientName());
                temp.setPatientSex(sampleEsModelDto.getPatientSex());
                temp.setPatientAge(sampleEsModelDto.getPatientAge());
                temp.setPatientSubage(sampleEsModelDto.getPatientSubage());
                temp.setPatientSubageUnit(sampleEsModelDto.getPatientSubageUnit());
                temp.setHspOrgName(sampleEsModelDto.getHspOrgName());
                temp.setHspOrgId(sampleEsModelDto.getHspOrgId());
                temp.setApplyId(sampleEsModelDto.getApplyId());
                temp.setApplyDate(sampleEsModelDto.getApplyDate());
                temp.setSignDate(sampleEsModelDto.getSignDate());
                temp.setFinalCheckDate(sampleEsModelDto.getFinalCheckDate());
                temp.setPatientVisitCard(sampleEsModelDto.getPatientVisitCard());
                temp.setApplySampleId(sampleEsModelDto.getApplySampleId());
                temp.setItemType(sampleEsModelDto.getItemType());
                temp.setApplyTypeCode(sampleEsModelDto.getApplyTypeCode());
                temp.setApplyTypeName(sampleEsModelDto.getApplyTypeName());
                temp.setDept(sampleEsModelDto.getDept());
                temp.setSendDoctorName(sampleEsModelDto.getSendDoctorName());
                temp.setTestItemId(testItem.getTestItemId());
                temp.setTestItemCode(testItem.getTestItemCode());
                temp.setTestItemName(testItem.getTestItemName());
                temp.setCount(testItem.getCount());
                temp.setPrice(ObjectUtils.defaultIfNull(testItem.getActualFeePrice(), BigDecimal.ZERO));
                temp.setIsFree(testItem.getIsFree());
                temp.setCreateDate(testItem.getCreateDate());
                temp.setStopStatus(
                        ObjectUtils.defaultIfNull(testItem.getStopStatus(), StopTestStatus.NO_STOP_TEST.getCode()));
                temp.setPatientCard(sampleEsModelDto.getPatientCard());

                if (sampleEsModelDto instanceof OutsourcingInspectionDto) {
                    OutsourcingInspectionDto outSample = (OutsourcingInspectionDto) sampleEsModelDto;
                    Long exportOrgId = testItem.getExportOrgId();
                    String exportOrgName = testItem.getExportOrgName();
                    temp.setExportHspOrgId(ObjectUtils.defaultIfNull(outSample.getExportOrgId(), exportOrgId));
                    temp.setExportHspOrgName(ObjectUtils.defaultIfNull(outSample.getExportOrgName(), exportOrgName));
                }
                temp.setApplySampleItemId(testItem.getApplySampleItemId());

                // 以下补充统计样本的所需数据
                // 填充统计样本所需字段信息
                temp.setBarcode(sampleEsModelDto.getBarcode());
                temp.setOutTestItemCode(testItem.getOutTestItemCode());
                temp.setOutTestItemName(testItem.getOutTestItemName());
                temp.setHspOrgCode(sampleEsModelDto.getHspOrgCode());
                temp.setPatientBirthday(sampleEsModelDto.getPatientBirthday());
                temp.setPatientBed(sampleEsModelDto.getPatientBed());
                temp.setCreateDate(sampleEsModelDto.getCreateDate());
                temp.setApplyDate(sampleEsModelDto.getApplyDate());
                temp.setSendDate(sampleEsModelDto.getApplyDate());
                temp.setSendDoctorCode(sampleEsModelDto.getSendDoctorCode());
                temp.setTestDate(sampleEsModelDto.getTestDate());
                temp.setTesterId(sampleEsModelDto.getTesterId());
                temp.setTesterName(sampleEsModelDto.getTesterName());
                temp.setFinalCheckerId(sampleEsModelDto.getFinalCheckerId());
                temp.setFinalCheckerName(sampleEsModelDto.getFinalCheckerName());
                temp.setIsSplitBlood(sampleEsModelDto.getIsSplitBlood());
                temp.setApplySampleId(sampleEsModelDto.getApplySampleId());
                temp.setFinalCheckDateTime(sampleEsModelDto.getFinalCheckDate());

	            // ✨feat：1.1.4【销售项目收入查询】明细数据增加专业组字段 https://www.tapd.cn/59091617/prong/stories/view/1159091617001001768?from_iteration_id=1159091617001000242
	            temp.setGroupId(testItem.getGroupId());
	            temp.setGroupName(testItem.getGroupName());

                temp.setHisSerialNo(sampleEsModelDto.getHisSerialNo());
                sampleTestItemDtoList.add(temp);
            }

        }
        return sampleTestItemDtoList;
    }

    /**
     * 根据传入条件过滤数据
     */
    private List<SampleTestItemDto> filterByTestItemIncomeFilterDto(TestItemIncomeFilterDto filterDto,
                                                                    List<SampleTestItemDto> sampleTestItemDtoList) {
        return sampleTestItemDtoList.stream()
                // 根据 就诊类型 过滤数据
                .filter(obj -> {
                    if (StringUtils.isBlank(filterDto.getApplyType()) && CollectionUtils.isEmpty(filterDto.getApplyTypes())) {
                        // 没有传就诊类型
                        return Boolean.TRUE;
                    }
                    if (CollectionUtils.isNotEmpty(filterDto.getApplyTypes())) {
                        return filterDto.getApplyTypes().contains(obj.getApplyTypeCode());
                    }
                    return Objects.equals(obj.getApplyTypeCode(), filterDto.getApplyType());
                })
                // 根据检验项目 过滤数据
                .filter(obj -> {
                    if (CollectionUtils.isEmpty(filterDto.getTestItemIds())) {
                        // 没有传检验项目
                        return Boolean.TRUE;
                    }
                    // 样本下检验项目 存在 在筛选条件内就允许
                    return filterDto.getTestItemIds().contains(obj.getTestItemId());
                })
                // 根据 项目类型 (目前只有 所有 和微生物 两种)过滤数据
                .filter(obj -> {
                    if (StringUtils.isBlank(filterDto.getItemTypeCode())) {
                        // 没有传项目类型
                        return Boolean.TRUE;
                    }
                    return Objects.equals(obj.getItemType(), filterDto.getItemTypeCode());
                }).// 根据是否免单 过滤
                        filter(obj -> {
                    if (Objects.isNull(filterDto.getIsFree())) {
                        // 没有是否免单
                        return Boolean.TRUE;
                    }
                    return Objects.equals(obj.getIsFree(), filterDto.getIsFree());
                }).collect(Collectors.toList());
    }

    private List<ItemPriceBasePackageItemDto> itemPriceBasePackageItemDtoListFromTbObjDto(List<ItemPriceBasePackageDto> list) {
        return JSON.parseArray(JSON.toJSONString(list), ItemPriceBasePackageItemDto.class);
    }
}
