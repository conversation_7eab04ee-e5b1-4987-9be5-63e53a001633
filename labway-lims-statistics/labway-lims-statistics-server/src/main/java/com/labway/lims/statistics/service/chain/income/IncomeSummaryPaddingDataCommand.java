package com.labway.lims.statistics.service.chain.income;

import com.labway.lims.api.config.EsConfig;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.vo.QueryCombinePackageListTestItemsVo;
import com.labway.lims.statistics.service.chain.CheckChainParams;
import com.labway.lims.statistics.service.chain.byPlateform.AcquireDateCommand;
import com.labway.lims.statistics.service.chain.byPlateform.PlatformStatisticsContext;
import com.labway.lims.statistics.vo.TestItemIncomeRequestVo;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Component
public class IncomeSummaryPaddingDataCommand implements Command {
    @Resource
    private EsConfig esConfig;

    @Resource
    private AcquireDateCommand acquireDateCommand;

    @Override
    public boolean execute(Context context) throws Exception {
        final IncomeSummaryContext from = IncomeSummaryContext.from(context);
        final TestItemIncomeRequestVo vo = from.getQuery();


        // 将查询条件 转换为es查询条件
        SampleEsQuery esDto = this.getSampleEsQueryFromTestItemIncomeSummaryRequestVo(vo);
        from.put(IncomeSummaryContext.SAMPLE_ES_QUERY, esDto);

        // 开票信息
        from.put(PlatformStatisticsContext.CUSTOMER_NAME_TYPE_ENUM, acquireDateCommand.getCustomerNameType(vo.getCustomerNameType()));

        // 送检机构
        final List<HspOrganizationDto> hspOrganizations = acquireDateCommand.acquireHspOrg(vo.getHspOrgIds());
        from.put(PlatformStatisticsContext.HSP_ORG, hspOrganizations);

        // 所有es 结构数据
        // 根据时间拆分，并行查询ES
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = acquireDateCommand.acquireBaseESData(esDto, StringUtils.isNotBlank(vo.getFinancialMonth()));
        from.put(PlatformStatisticsContext.BASE_SAMPLE_ES_DATA, baseSampleEsModelDtos);

        // 查询 终止收费的检验项目信息 不一定是审核的
        final List<BaseSampleEsModelDto> stopTestChargeEsModelDtos = acquireDateCommand.acquireStopEsData(esDto);
        from.put(PlatformStatisticsContext.STOP_SAMPLE_ES_DATA, stopTestChargeEsModelDtos);

        // ES数据  查询 病理检验 已经一次分拣
        final List<BaseSampleEsModelDto> pathologyEsModelDtos = acquireDateCommand.acquirePathologyEsData(esDto);
        from.put(PlatformStatisticsContext.PATHOLOGY_SAMPLE_ES_DATA, pathologyEsModelDtos);

        // 财务套餐
        final Map<Long, List<QueryCombinePackageListTestItemsVo>> combinePackageMap = acquireDateCommand.acquireCombinePackageMap();
        from.put(PlatformStatisticsContext.COMBINE_PACKAGE_MAP, combinePackageMap);

        return CONTINUE_PROCESSING;
    }

    private SampleEsQuery getSampleEsQueryFromTestItemIncomeSummaryRequestVo(TestItemIncomeRequestVo vo) {

        // 检查 送检时间 与财务月份
        CheckChainParams.checkDateRangRequest(vo.getFinancialMonth(), vo.getBeginDeliveryDate(), vo.getEndDeliveryDate());

        // 将查询条件 转换为es查询条件
        SampleEsQuery dto = new SampleEsQuery();
        dto.setPageSize(esConfig.getPageSize());
        dto.setPageNo(NumberUtils.INTEGER_ONE);
        // 1.0.6.6需求改为不管该样本是否已经审核都进行查询
        //dto.setIsAudit(YesOrNoEnum.YES.getCode());

        // 补充查询时间范围
        CheckChainParams.sampleEsQueryAddDateRang(dto, vo.getFinancialMonth(), vo.getBeginDeliveryDate(), vo.getEndDeliveryDate());

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            dto.setHspOrgIds(vo.getHspOrgIds());
        }

        return dto;
    }
}
