package com.labway.lims.statistics.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.OutsourcingTestItemStatisticsDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleTestItemDto;
import com.labway.lims.apply.api.dto.TestItemIncomeFilterDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.service.FinancialManagementService;
import com.labway.lims.statistics.service.OutsourcingTestItemStatisticsService;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <pre>
 * OutsourcingTestItemStatisticsServiceImpl
 * 外送项目统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/2/18 13:18
 */
@Service
public class OutsourcingTestItemStatisticsServiceImpl implements OutsourcingTestItemStatisticsService {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    private FinancialManagementService financialManagementService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private TestItemService testItemService;

    @Override
    public OutsourcingTestItemStatisticsDto getOutsourcingTestItemStatistics(
            SampleEsQuery dto, TestItemIncomeFilterDto filterDto, List<BaseSampleEsModelDto> baseSampleEsModelDtosAll) {

        // 查询 终止收费的检验项目信息 不一定是审核的
        SampleEsQuery stopStatusEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        stopStatusEsQuery.setIsAudit(null);
        stopStatusEsQuery.setStopStatus(Set.of(StopTestStatus.STOP_TEST_CHARGE.getCode()));
        List<BaseSampleEsModelDto> stopTestChargeEsModelDtos =
                elasticSearchSampleService.selectSamples(stopStatusEsQuery);

        // 查询 病理检验 已经一次分拣
        SampleEsQuery pathologyEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        pathologyEsQuery.setIsAudit(null);
        pathologyEsQuery.setItemTypes(Collections.singleton(ItemTypeEnum.PATHOLOGY.name()));
        List<BaseSampleEsModelDto> pathologyEsModelDtos = elasticSearchSampleService.selectSamples(pathologyEsQuery);

        // 拆分数据
        List<SampleTestItemDto> sampleTestItemDtoListAll = financialManagementService.handleEsDataToSampleTestItemDto(
                baseSampleEsModelDtosAll, stopTestChargeEsModelDtos, pathologyEsModelDtos, filterDto, false);

        final List<TestItemDto> testItemDtos = getOutsourcingTestItems(
                baseSampleEsModelDtosAll, stopTestChargeEsModelDtos, pathologyEsModelDtos);
        final Map<String, TestItemDto> testItemDtoMap = testItemDtos.stream().collect(
                Collectors.toMap(TestItemDto::getTestItemCode, Function.identity(), (v1, v2) -> v1));

        // 以外送机构分组
        Map<Long, List<SampleTestItemDto>> groupingByExportHspOrgId =
                sampleTestItemDtoListAll.stream()
                        .filter(e -> testItemDtoMap.containsKey(e.getTestItemCode()))
                        .filter(e -> CollectionUtils.isEmpty(dto.getTestItemCodes()) || dto.getTestItemCodes().contains(e.getTestItemCode()))
                        .collect(Collectors.groupingBy(SampleTestItemDto::getExportHspOrgId));

        // 所有送检机构信息
        List<HspOrganizationDto> hspOrganizationDtos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(groupingByExportHspOrgId.keySet())) {
            hspOrganizationDtos = hspOrganizationService.selectByHspOrgIds(groupingByExportHspOrgId.keySet());
        }
        Map<Long, HspOrganizationDto> hspOrganizationDtoByHspOrgId = hspOrganizationDtos.stream()
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity()));

        // 响应数据结构
        OutsourcingTestItemStatisticsDto target = new OutsourcingTestItemStatisticsDto();
        List<OutsourcingTestItemStatisticsDto.TestItemStatisticsItem> itemList = new ArrayList<>();

        // 数量合计
        Integer countSumAll = NumberUtils.INTEGER_ZERO;

        for (Map.Entry<Long, List<SampleTestItemDto>> entry : groupingByExportHspOrgId.entrySet()) {
            Long exportOrgId = entry.getKey();
            List<SampleTestItemDto> sampleTestItemDtoList = entry.getValue();

            {
                HspOrganizationDto hspOrganizationDto = hspOrganizationDtoByHspOrgId.get(exportOrgId);

                // 检验项目 分组 外送项目统计，只统计数量，不统计价格
                Map<TestItemIncomeSummaryKeyDto, List<SampleTestItemDto>> groupingByKeyDto = sampleTestItemDtoList.stream()
                        .collect(Collectors.groupingBy(obj -> new TestItemIncomeSummaryKeyDto(null,
                                obj.getTestItemCode(), null, null)));

                // 响应数据行
                List<OutsourcingTestItemStatisticsDto.TestItemStatisticsItem> orgItems = Lists.newArrayListWithCapacity(groupingByKeyDto.size());

                for (Map.Entry<TestItemIncomeSummaryKeyDto, List<SampleTestItemDto>> groupingByKey : groupingByKeyDto.entrySet()) {
                    TestItemIncomeSummaryKeyDto keyDto = groupingByKey.getKey();
                    List<SampleTestItemDto> summaryItemList = groupingByKey.getValue();

                    int countSum = summaryItemList.stream()
                            .map(obj -> ObjectUtils.defaultIfNull(obj.getCount(), NumberUtils.INTEGER_ONE))
                            .mapToInt(Integer::intValue).sum();

                    countSumAll = countSumAll + countSum;

                    final OutsourcingTestItemStatisticsDto.TestItemStatisticsItem temp =
                            new OutsourcingTestItemStatisticsDto.TestItemStatisticsItem();

                    if (Objects.nonNull(hspOrganizationDto)) {
                        temp.setExportOrgId(hspOrganizationDto.getHspOrgId());
                        temp.setExportOrgName(hspOrganizationDto.getHspOrgName());
                    } else {
                        temp.setExportOrgId(exportOrgId);
                        // temp.setExportOrgName("(未知)");
                    }

                    temp.setTestItemCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemCode());
                    temp.setTestItemName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemName());

                    // 单价
                    // temp.setPrice(summaryItemList.get(NumberUtils.INTEGER_ZERO).getPrice());
                    // 样本类型，管型，方法学
                    Optional.ofNullable(testItemDtoMap.get(temp.getTestItemCode())).ifPresent(e -> {
                        temp.setSampleTypeName(e.getSampleTypeName());
                        temp.setTubeName(e.getTubeName());
                        temp.setExamMethodName(e.getExamMethodName());
                        // temp.setPrice(summaryItemList.get(NumberUtils.INTEGER_ZERO).getPrice());
                    });
                    // 这里取实际收费价格
                    temp.setPrice(summaryItemList.get(NumberUtils.INTEGER_ZERO).getActualFeePrice());

                    temp.setCount(countSum);

                    orgItems.add(temp);
                }

                itemList.addAll(orgItems);
            }
        }

        itemList.sort(Comparator.comparing(OutsourcingTestItemStatisticsDto.TestItemStatisticsItem::getExportOrgId)
                .thenComparing(OutsourcingTestItemStatisticsDto.TestItemStatisticsItem::getTestItemCode));

        target.setStartDate(DateUtil.formatDate(dto.getStartTwoPickDate()));
        target.setEndDate(DateUtil.formatDate(dto.getEndTwoPickDate()));
        target.setItemList(itemList);
        target.setCountSum(countSumAll);

        return target;
    }

    /**
     * 拿到结果中所有的外送项目编码
     */
    private List<TestItemDto> getOutsourcingTestItems(List<BaseSampleEsModelDto> baseSampleEsModelDtosAll,
                                                      List<BaseSampleEsModelDto> stopTestChargeEsModelDtos,
                                                      List<BaseSampleEsModelDto> pathologyEsModelDtos) {
        Set<String> testItemCodes = new HashSet<>();

        testItemCodes.addAll(baseSampleEsModelDtosAll.stream().flatMap(e -> e.getTestItems().stream())
                .map(BaseSampleEsModelDto.TestItem::getTestItemCode)
                .collect(Collectors.toList()));
        testItemCodes.addAll(stopTestChargeEsModelDtos.stream().flatMap(e -> e.getTestItems().stream())
                .map(BaseSampleEsModelDto.TestItem::getTestItemCode)
                .collect(Collectors.toList()));
        testItemCodes.addAll(pathologyEsModelDtos.stream().flatMap(e -> e.getTestItems().stream())
                .map(BaseSampleEsModelDto.TestItem::getTestItemCode)
                .collect(Collectors.toList()));

        return testItemService.selectByTestItemCodes(testItemCodes, LoginUserHandler.get().getOrgId());
    }

    @Getter
    @Setter
    public static class TestItemIncomeSummaryKeyDto implements Serializable {

        /**
         * 就诊类型 (申请类型)
         */
        private String applyType;
        /**
         * 检验项目ID
         */
        private String testItemCode;
        /**
         * 单价
         */
        private BigDecimal price;
        /**
         * 折扣率
         */
        private BigDecimal discount;

        public TestItemIncomeSummaryKeyDto(String applyType, String testItemCode, BigDecimal price, BigDecimal discount) {
            this.applyType = applyType;
            this.testItemCode = testItemCode;
            this.price = price;
            this.discount = discount;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (!(obj instanceof TestItemIncomeSummaryKeyDto)) {
                return false;
            }
            TestItemIncomeSummaryKeyDto otherKey = (TestItemIncomeSummaryKeyDto) obj;
            return Objects.equals(applyType, otherKey.applyType) && Objects.equals(testItemCode, otherKey.testItemCode)
                    && Objects.equals(price, otherKey.price) && Objects.equals(discount, otherKey.discount);
        }

        @Override
        public int hashCode() {
            return Objects.hash(applyType, testItemCode, price, discount);
        }

    }

}
