package com.labway.lims.statistics.service.byPlatform;

import com.labway.lims.apply.api.dto.ByPlatformStatisticsResponseDto;
import com.labway.lims.statistics.service.chain.byPlateform.ByPlatformStatisticsChain;
import com.labway.lims.statistics.service.chain.byPlateform.PlatformStatisticsContext;
import com.labway.lims.statistics.vo.FinanceStatisticsQueryVo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class NewByPlatformStatisticsServiceImpl implements ByPlatformStatisticsService {

    @Resource
    private ByPlatformStatisticsChain byPlatformStatisticsChain;

    @Override
    public String version() {
        return "2.0";
    }

    @Override
    public ByPlatformStatisticsResponseDto byPlatformStatistics(FinanceStatisticsQueryVo query) {
        final PlatformStatisticsContext platformStatisticsContext = new PlatformStatisticsContext();

        platformStatisticsContext.put(PlatformStatisticsContext.FINANCE_STATISTICS_QUERY, query);
        platformStatisticsContext.setNeedFinanceGroup(true);
        try {
            if (!byPlatformStatisticsChain.execute(platformStatisticsContext)) {
                throw new IllegalStateException("分平台统计计算失败");
            }
            return platformStatisticsContext.getResult();
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
    }
}
