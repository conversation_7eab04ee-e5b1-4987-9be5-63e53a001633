package com.labway.lims.statistics.service;

import com.labway.lims.statistics.dto.ExportRecordDto;
import com.labway.lims.statistics.vo.ExportFileCancelRequestVo;
import com.labway.lims.statistics.vo.ExportFileDeleteRequestVo;
import com.labway.lims.statistics.vo.ExportFileDownloadRequestVo;
import com.labway.lims.statistics.vo.ExportFileRequestVo;
import com.labway.lims.statistics.vo.ExportFileResultVo;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * <pre>
 * ExportService
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/4/9 10:48
 */
public interface ExportService {

    Future<ExportRecordDto> submitExportTask(ExportRecordDto recordDto, Callable<ExportRecordDto> callable);

    ExportFileResultVo list(ExportFileRequestVo requestVo);

    void download(ExportFileDownloadRequestVo requestVo);

    void cancel(ExportFileCancelRequestVo requestVo);

    void delete(ExportFileDeleteRequestVo requestVo);
}
