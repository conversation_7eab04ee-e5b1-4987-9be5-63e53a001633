package com.labway.lims.statistics.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.routine.DateTypeEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.enums.RelationalOperatorEnum;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.dto.ExportRecordDto;
import com.labway.lims.statistics.dto.FieldData;
import com.labway.lims.statistics.enums.ExportFileType;
import com.labway.lims.statistics.enums.ExportStatus;
import com.labway.lims.statistics.enums.InspectionResultsExportType;
import com.labway.lims.statistics.service.ExportService;
import com.labway.lims.statistics.service.SampleResultService;
import com.labway.lims.statistics.vo.ExportResultVo;
import com.labway.lims.statistics.vo.InspectionResultQueryVo;
import com.labway.lims.statistics.vo.InspectionResultSummaryVo;
import com.labway.lims.statistics.vo.InspectionResultVo;
import com.labway.lims.statistics.vo.PatientInfoVo;
import com.labway.lims.statistics.vo.ReportInfoVo;
import com.labway.lims.statistics.vo.SampleInspectionResultVo;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 样本结果 API
 *
 * <AUTHOR>
 * @since 2023/3/30 09:54
 */
@Slf4j
@RestController
@RequestMapping("/sample-result")
public class SampleResultController extends BaseController {
    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private EsConfig esConfig;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @Resource
    private ExportService exportService;
    @Resource
    private SampleResultService sampleResultService;

    /**
     * ------------------------- 检验结果查询 -------------------------
     */

    @PostMapping("/inspection-results")
    public Object inspectionResults(@RequestBody InspectionResultQueryVo vo) {

        if (Objects.isNull(vo.getEndDate()) || Objects.isNull(vo.getStartDate())) {
            throw new IllegalArgumentException("日期错误");
        }

        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }

        List<Object> searchAfter = null;
        if (Objects.nonNull(vo.getSearchAfter())) {
            searchAfter = Collections.singletonList(vo.getSearchAfter());
        }

        final SampleEsQuery query = new SampleEsQuery();
        query.setIsAudit(YesOrNoEnum.YES.getCode());
        query.setItemTypes(Sets.newHashSet(ItemTypeEnum.ROUTINE.name(), ItemTypeEnum.OUTSOURCING.name()));
        query.setPageNo(vo.getCurrent());
        query.setPageSize(vo.getSize());
        buildQueryParam(query, vo);
        final ScrollPage<BaseSampleEsModelDto> scrollPage = elasticSearchSampleService.searchAfter(searchAfter, query);

        final List<RoutineInspectionDto> samples =
                scrollPage.getData().stream().filter(RoutineInspectionDto.class::isInstance)
                        .map(e -> (RoutineInspectionDto) e).collect(Collectors.toList());

        final List<OutsourcingInspectionDto> outSamples =
                scrollPage.getData().stream().filter(OutsourcingInspectionDto.class::isInstance)
                        .map(e -> (OutsourcingInspectionDto) e).collect(Collectors.toList());

        final Map<String, List<OutsourcingInspectionDto>> outMap =
                outSamples.stream().collect(Collectors.groupingBy(BaseSampleEsModelDto::getHspOrgCode));

        final Map<String, List<RoutineInspectionDto>> map =
                samples.stream().collect(Collectors.groupingBy(BaseSampleEsModelDto::getHspOrgCode));
        final InspectionResultVo inspectionResult = new InspectionResultVo();
        List<ReportInfoVo> hospitals = new LinkedList<>();

        for (Map.Entry<String, List<OutsourcingInspectionDto>> e : outMap.entrySet()) {
            final ReportInfoVo info = new ReportInfoVo();
            info.setHspOrgCode(e.getKey());
            info.setHspOrgName(e.getValue().iterator().next().getHspOrgName());
            final String id = IdUtil.simpleUUID();
            info.setId(id);
            final List<SampleInspectionResultVo> inspectionResults =
                    convert2OutSampleInspectionResult(e.getValue(), id);
            info.setResults(inspectionResults);
            hospitals.add(info);
        }

        for (Map.Entry<String, List<RoutineInspectionDto>> e : map.entrySet()) {
            final ReportInfoVo info = new ReportInfoVo();
            info.setHspOrgCode(e.getKey());
            info.setHspOrgName(e.getValue().iterator().next().getHspOrgName());
            final String id = IdUtil.simpleUUID();
            info.setId(id);
            List<SampleInspectionResultVo> inspectionResults = convert2SampleInspectionResult(e.getValue(), id);

            //  过滤掉不在查询条件内的报告项目
            if (CollectionUtils.isNotEmpty(vo.getReportItemCodes())) {
                inspectionResults = inspectionResults.stream().filter(r -> vo.getReportItemCodes().contains(r.getReportItemCode())).collect(Collectors.toList());

            }
            info.setResults(inspectionResults);
            hospitals.add(info);
        }

        // 根据查询条件过滤结果
        sampleResultService.filterResultRecord(vo, hospitals);

        if (CollectionUtils.isNotEmpty(scrollPage.getSearchAfter())) {
            inspectionResult.setSearchAfter(scrollPage.getSearchAfter().iterator().next());
        }
        for (ReportInfoVo hospital : hospitals) {
            hospital.setSize(hospital.getResults().size());
        }

        sordHospitals(hospitals, vo.getSortData());
        inspectionResult.setHospitals(hospitals);

        // 人员信息统计
        final LinkedList<PatientInfoVo> patientInfos = new LinkedList<>();
        for (ReportInfoVo hospital : hospitals) {
            final Map<Long, List<SampleInspectionResultVo>> results = hospital.getResults().stream()
                    .collect(Collectors.groupingBy(SampleInspectionResultVo::getApplySampleId));
            for (List<SampleInspectionResultVo> k : results.values()) {
                final SampleInspectionResultVo e = k.iterator().next();
                final PatientInfoVo patientInfo = new PatientInfoVo();
                patientInfo.setPatientName(e.getPatientName());
                patientInfo.setPatientSex(e.getPatientSex());
                patientInfo.setPatientAge(e.getPatientAge());
                patientInfo.setPatientSubage(e.getPatientSubage());
                patientInfo.setPatientSubageUnit(e.getPatientSubageUnit());
                patientInfo.setHspOrgCode(hospital.getHspOrgCode());
                patientInfo.setHspOrgName(hospital.getHspOrgName());
                patientInfo.setApplyType(e.getApplyType());
                patientInfo.setTestItemName(k.stream().map(SampleInspectionResultVo::getTestItemName)
                        .distinct().collect(Collectors.joining(",")));

                patientInfo.setId(UUID.fastUUID().toString());

                patientInfos.add(patientInfo);
            }
        }

        inspectionResult.setPatientInfos(patientInfos);

        return inspectionResult;
    }

    private void sordHospitals(List<ReportInfoVo> hospitals, List<InspectionResultQueryVo.SortData> sortData) {

        if (CollectionUtils.isNotEmpty(sortData)) {

            Comparator<SampleInspectionResultVo> comparator = (o1, o2) -> 0;

            // 拼接排序条件
            for (InspectionResultQueryVo.SortData e : sortData) {
                comparator = comparator.thenComparing((a,b)->{
                   switch (e.getSortField()){
                       case "patientName":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getPatientName().compareTo(b.getPatientName());
                           }
                           return b.getPatientName().compareTo(a.getPatientName());
                       case "patientAge":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getPatientAge().compareTo(b.getPatientAge());
                           }
                           return b.getPatientAge().compareTo(a.getPatientAge());
                       case "barcode":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getBarcode().compareTo(b.getBarcode());
                           }
                           return b.getBarcode().compareTo(a.getBarcode());
                       case "patientSex":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getPatientSex().compareTo(b.getPatientSex());
                           }
                           return b.getPatientSex().compareTo(a.getPatientSex());
                       case "applyType":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getApplyType().compareTo(b.getApplyType());
                           }
                           return b.getApplyType().compareTo(a.getApplyType());
                       case "testItemName":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getTestItemName().compareTo(b.getTestItemName());
                           }
                           return b.getTestItemName().compareTo(a.getTestItemName());
                       case "reportItemName":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getReportItemName().compareTo(b.getReportItemName());
                           }
                           return b.getReportItemName().compareTo(a.getReportItemName());
                       case "result":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getResult().compareTo(b.getResult());
                           }
                           return b.getResult().compareTo(a.getResult());
                       case "unit":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getUnit().compareTo(b.getUnit());
                           }
                           return b.getUnit().compareTo(a.getUnit());
                       case "judge":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getJudge().compareTo(b.getJudge());
                           }
                           return b.getJudge().compareTo(a.getJudge());
                       case "sampleTypeName":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getSampleTypeName().compareTo(b.getSampleTypeName());
                           }
                           return b.getSampleTypeName().compareTo(a.getSampleTypeName());
                       case "range":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getRange().compareTo(b.getRange());
                           }
                           return b.getRange().compareTo(a.getRange());
                       case "dept":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getDept().compareTo(b.getDept());
                           }
                           return b.getDept().compareTo(a.getDept());
                       case "patientVisitCard":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getPatientVisitCard().compareTo(b.getPatientVisitCard());
                           }
                           return b.getPatientVisitCard().compareTo(a.getPatientVisitCard());
                       case "patientBed":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getPatientBed().compareTo(b.getPatientBed());
                           }
                           return b.getPatientBed().compareTo(a.getPatientBed());
                       case "sendDoctorName":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getSendDoctorName().compareTo(b.getSendDoctorName());
                           }
                           return b.getSendDoctorName().compareTo(a.getSendDoctorName());
                       case "testDate":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getTestDate().compareTo(b.getTestDate());
                           }
                           return b.getTestDate().compareTo(a.getTestDate());
                       case "applyDate":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getApplyDate().compareTo(b.getApplyDate());
                           }
                           return b.getApplyDate().compareTo(a.getApplyDate());
                       case "checkDate":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getCheckDate().compareTo(b.getCheckDate());
                           }
                           return b.getCheckDate().compareTo(a.getCheckDate());
                       case "groupName":
                           if (Objects.equals(e.getSortRule(), "Asc")) {
                               return a.getGroupName().compareTo(b.getGroupName());
                           }
                           return b.getGroupName().compareTo(a.getGroupName());
                       default:
                           return 0;
                   }
                });
            }

            // 重新排序
            for (ReportInfoVo hospital : hospitals) {
                for (SampleInspectionResultVo result : hospital.getResults()) {
                    if(StringUtils.isBlank(result.getResult())){
                        result.setResult(Strings.EMPTY);
                    }
                    if(StringUtils.isBlank(result.getUnit())){
                        result.setUnit(Strings.EMPTY);
                    }
                    if(StringUtils.isBlank(result.getJudge())){
                        result.setJudge(Strings.EMPTY);
                    }
                    if(StringUtils.isBlank(result.getRange())){
                        result.setRange(Strings.EMPTY);
                    }
                }

                hospital.getResults().sort(comparator);
            }

        }

    }

    /**
     * 导出 被替换为：{@link #exportResultsAsync}
     * @deprecated {@link #exportResultsAsync}
     */
    @Deprecated
    @PostMapping("/export-results")
    public Object exportResults(@RequestBody InspectionResultQueryVo vo) {
        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }
        InspectionResultVo inspectionResult = null;
        // 如果前端没有传数据，走正常查询流程
        if (CollectionUtils.isEmpty(vo.getSelectData())) {
            List<Object> searchAfter = null;
            if (Objects.nonNull(vo.getSearchAfter())) {
                searchAfter = Collections.singletonList(vo.getSearchAfter());
            }
            final SampleEsQuery query = new SampleEsQuery();
            query.setIsAudit(YesOrNoEnum.YES.getCode());
            query.setItemTypes(Sets.newHashSet(ItemTypeEnum.ROUTINE.name(), ItemTypeEnum.OUTSOURCING.name()));
            query.setPageNo(vo.getCurrent());
            query.setPageSize(vo.getSize());
            buildQueryParam(query, vo);
            final ScrollPage<BaseSampleEsModelDto> scrollPage = elasticSearchSampleService.searchAfter(searchAfter, query);

            final List<RoutineInspectionDto> samples =
                    scrollPage.getData().stream().filter(RoutineInspectionDto.class::isInstance)
                            .map(e -> (RoutineInspectionDto) e).collect(Collectors.toList());

            final List<OutsourcingInspectionDto> outSamples =
                    scrollPage.getData().stream().filter(OutsourcingInspectionDto.class::isInstance)
                            .map(e -> (OutsourcingInspectionDto) e).collect(Collectors.toList());

            final Map<String, List<OutsourcingInspectionDto>> outMap =
                    outSamples.stream().collect(Collectors.groupingBy(BaseSampleEsModelDto::getHspOrgCode));

            final Map<String, List<RoutineInspectionDto>> map =
                    samples.stream().collect(Collectors.groupingBy(BaseSampleEsModelDto::getHspOrgCode));
            inspectionResult = new InspectionResultVo();
            List<ReportInfoVo> hospitals = new LinkedList<>();

            for (Map.Entry<String, List<OutsourcingInspectionDto>> e : outMap.entrySet()) {
                final ReportInfoVo info = new ReportInfoVo();
                info.setHspOrgCode(e.getKey());
                info.setHspOrgName(e.getValue().iterator().next().getHspOrgName());
                final String id = IdUtil.simpleUUID();
                info.setId(id);
                final List<SampleInspectionResultVo> inspectionResults =
                        convert2OutSampleInspectionResult(e.getValue(), id);
                info.setResults(inspectionResults);
                hospitals.add(info);
            }

            for (Map.Entry<String, List<RoutineInspectionDto>> e : map.entrySet()) {
                final ReportInfoVo info = new ReportInfoVo();
                info.setHspOrgCode(e.getKey());
                info.setHspOrgName(e.getValue().iterator().next().getHspOrgName());
                final String id = IdUtil.simpleUUID();
                info.setId(id);
                final List<SampleInspectionResultVo> inspectionResults = convert2SampleInspectionResult(e.getValue(), id);
                info.setResults(inspectionResults);
                hospitals.add(info);
            }

            // 根据查询条件过滤结果
            sampleResultService.filterResultRecord(vo, hospitals);

            if (CollectionUtils.isNotEmpty(scrollPage.getSearchAfter())) {
                inspectionResult.setSearchAfter(scrollPage.getSearchAfter().iterator().next());
            }
            for (ReportInfoVo hospital : hospitals) {
                hospital.setSize(hospital.getResults().size());
            }
            inspectionResult.setHospitals(hospitals);
            // 人员信息统计
            final LinkedList<PatientInfoVo> patientInfos = new LinkedList<>();
            for (ReportInfoVo hospital : hospitals) {
                final Map<Long, List<SampleInspectionResultVo>> results = hospital.getResults().stream()
                        .collect(Collectors.groupingBy(SampleInspectionResultVo::getApplySampleId));
                for (List<SampleInspectionResultVo> k : results.values()) {
                    final SampleInspectionResultVo e = k.iterator().next();
                    final PatientInfoVo patientInfo = new PatientInfoVo();
                    patientInfo.setPatientName(e.getPatientName());
                    patientInfo.setPatientSex(e.getPatientSex());
                    patientInfo.setPatientAge(e.getPatientAge());
                    patientInfo.setPatientSubage(e.getPatientSubage());
                    patientInfo.setPatientSubageUnit(e.getPatientSubageUnit());
                    patientInfo.setHspOrgCode(hospital.getHspOrgCode());
                    patientInfo.setHspOrgName(hospital.getHspOrgName());
                    patientInfo.setApplyType(e.getApplyType());
                    patientInfo.setTestItemName(k.stream().map(SampleInspectionResultVo::getTestItemName)
                            .distinct().collect(Collectors.joining(",")));
                    patientInfos.add(patientInfo);
                }
            }
            inspectionResult.setPatientInfos(patientInfos);
        }


        // 头的策略
        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("微软雅黑");
        headWriteFont.setFontHeightInPoints((short) 13);
        headCellStyle.setWriteFont(headWriteFont);

        // 内容策略
        WriteCellStyle contentCellStyle = new WriteCellStyle();
        contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentCellStyle.setBorderLeft(BorderStyle.THIN);
        contentCellStyle.setBorderTop(BorderStyle.THIN);
        contentCellStyle.setBorderRight(BorderStyle.THIN);
        contentCellStyle.setBorderBottom(BorderStyle.THIN);

        // 设置列宽度
        AbstractColumnWidthStyleStrategy columnWidthStyleStrategy = new AbstractColumnWidthStyleStrategy() {
            @Override
            protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head,
                                          Integer integer, Boolean isHead) {
                if (Boolean.TRUE.equals(isHead)) {
                    Sheet sheet = writeSheetHolder.getSheet();
                    sheet.setColumnWidth(14, 6000);
                    sheet.setColumnWidth(15, 6000);
                    sheet.setColumnWidth(16, 6000);

                }
            }
        };

        HorizontalCellStyleStrategy excelStyle = new HorizontalCellStyleStrategy(headCellStyle, contentCellStyle);

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (ExcelWriter excelWriter =
                     EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle)
                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                             .registerWriteHandler(columnWidthStyleStrategy)
                             .build()) {

            List<List<Object>> list0 = Lists.newArrayList();
            List<List<String>> header0 = Lists.newArrayList();


            if (vo.getExportType() == 0) {
                // 设置表头
                List<String> headList = Lists.newArrayList("姓名", "年龄", "性别", "就诊类型", "送检机构", "条码号", "检验项目", "报告项目", "结果值",
                        "单位", "提示", "参考范围", "样本类型", "专业组", "送检日期", "检验日期", "审核日期", "门诊/住院号", "送检医生", "科室", "床号");
                for (String item : headList) {
                    header0.add(List.of(item));
                }
                List<SampleInspectionResultVo> contentList = new LinkedList<>();

                // 导出前端传入的数据
                if (CollectionUtils.isNotEmpty(vo.getSelectData())) {
                    contentList = vo.getSelectData();
                } else {
                    for (ReportInfoVo hospital : inspectionResult.getHospitals()) {
                        contentList.addAll(hospital.getResults());
                    }

                }

                // 设置 放置数据
                fillExcelContent(list0, headList, contentList);
                // 获取sheet对象
                WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "检验结果信息").head(header0).needHead(Boolean.TRUE).build();

                excelWriter.write(list0, sheet0);
            } else {
                // 设置表头
                List<String> headList = Lists.newArrayList("姓名", "年龄", "性别", "就诊类型", "送检机构", "检验项目");
                for (String item : headList) {
                    header0.add(List.of(item));
                }
                List<PatientInfoVo> contentList = new LinkedList<>();
                // 导出前端传入的数据
                if (CollectionUtils.isNotEmpty(vo.getSelectData())) {
                     contentList = BeanUtil.copyToList(vo.getSelectData(), PatientInfoVo.class);
                } else {
                    contentList.addAll(inspectionResult.getPatientInfos());
                }
                // 设置 放置数据
                fillExcelTestPersonsContent(list0, headList, contentList);
                // 获取sheet对象
                WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "人员检验项目信息").head(header0).needHead(Boolean.TRUE).build();

                excelWriter.write(list0, sheet0);
            }
        } catch (Exception e) {
            log.error("导出数据错误", e);
            throw new LimsException(e.getMessage(), e);
        }
        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode((vo.getExportType() == 0) ? "检验结果信息.xlsx" : "人员检验项目信息.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(data);
    }

    @PostMapping("/inspection-results-new")
    public InspectionResultVo inspectionResultsNew(@RequestBody InspectionResultQueryVo vo) {
        if (Objects.isNull(vo.getEndDate()) || Objects.isNull(vo.getStartDate())) {
            throw new IllegalArgumentException("日期错误");
        }

        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }

        return sampleResultService.inspectionResultsNew(vo);
    }

    @PostMapping("/inspection-results-summary")
    public InspectionResultSummaryVo inspectionResultSummary(@RequestBody InspectionResultQueryVo vo) {
        return sampleResultService.inspectionResultSummary(vo);
    }

    @PostMapping("/export-results-async")
    public Object exportResultsAsync(@RequestBody InspectionResultQueryVo vo) {
        if (Objects.isNull(vo.getEndDate()) || Objects.isNull(vo.getStartDate())) {
            throw new IllegalArgumentException("日期错误");
        }
        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }
        if (CollectionUtils.isEmpty(vo.getFields())
                || vo.getFields().stream().anyMatch(field -> StringUtils.isBlank(field.getField()))
                || vo.getFields().stream().anyMatch(field -> StringUtils.isBlank(field.getFieldName()))) {
            throw new IllegalArgumentException("导出字段信息不能为空");
        }
        Assert.notNull(vo.getSource(), "页面来源不能为空");

        // 头的策略
        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("微软雅黑");
        headWriteFont.setFontHeightInPoints((short) 13);
        headCellStyle.setWriteFont(headWriteFont);

        // 内容策略
        WriteCellStyle contentCellStyle = new WriteCellStyle();
        contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentCellStyle.setBorderLeft(BorderStyle.THIN);
        contentCellStyle.setBorderTop(BorderStyle.THIN);
        contentCellStyle.setBorderRight(BorderStyle.THIN);
        contentCellStyle.setBorderBottom(BorderStyle.THIN);

        // 设置列宽度
        AbstractColumnWidthStyleStrategy columnWidthStyleStrategy = new AbstractColumnWidthStyleStrategy() {
            @Override
            protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head,
                                          Integer integer, Boolean isHead) {
                if (Boolean.TRUE.equals(isHead)) {
                    Sheet sheet = writeSheetHolder.getSheet();
                    sheet.setColumnWidth(14, 6000);
                    sheet.setColumnWidth(15, 6000);
                    sheet.setColumnWidth(16, 6000);
                }
            }
        };

        HorizontalCellStyleStrategy excelStyle = new HorizontalCellStyleStrategy(headCellStyle, contentCellStyle);

        LoginUserHandler.User user = LoginUserHandler.get();
        final ExportRecordDto exportRecordDto = new ExportRecordDto();
        exportRecordDto.setFileName((vo.getExportType() == 0) ? "检验结果信息.xlsx" : "人员检验项目信息.xlsx");
        exportRecordDto.setFileType(ExportFileType.XLSX.getValue());
        exportRecordDto.setStatus(ExportStatus.RUNNING.getCode());
        exportRecordDto.setSource(vo.getSource());
        exportRecordDto.setCreateDate(DateUtil.date());

        exportService.submitExportTask(exportRecordDto, () -> {
            LoginUserHandler.set(user);

            StopWatch stopWatch = new StopWatch("导出结果");
            InspectionResultVo inspectionResult;
            // 如果前端没有传数据，走正常查询流程
            if (CollectionUtils.isEmpty(vo.getSelectData())) {
                vo.setQueryAll(true);
                stopWatch.start("查询结果");
                inspectionResult = sampleResultService.inspectionResultsNew(vo);
                stopWatch.stop();
            } else {
                inspectionResult = null;
            }
            log.info("检验结果信息导出查询数据耗时：{} s", stopWatch.getTotal(TimeUnit.SECONDS));

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            String uploadType = ExportFileType.XLSX.getMediaType();

            if (Objects.nonNull(inspectionResult) && CollectionUtils.isNotEmpty(inspectionResult.getResults()) && inspectionResult.getResults().size() > 50_0000) {
                uploadType = ExportFileType.ZIP.getMediaType();
                String fileName = (InspectionResultsExportType.isExportReport(vo.getExportType())) ? "检验结果信息.zip" : "人员检验项目信息.zip";
                exportRecordDto.setFileName(fileName);
                exportRecordDto.setFileType(ExportFileType.ZIP.getValue());

                try (ZipOutputStream zipOutputStream = ZipUtil.getZipOutputStream(out, StandardCharsets.UTF_8)) {
                    AtomicInteger index = new AtomicInteger(1);
                    for (List<SampleInspectionResultVo> inspectionResultVos : Lists.partition(inspectionResult.getResults(), 50_0000)) {
                        // 添加 Excel 文件到 ZIP
                        zipOutputStream.putNextEntry(new ZipEntry(URLDecoder.decode(String.format("检验结果信息-%d.xlsx", index.getAndIncrement()), StandardCharsets.UTF_8)));
                        try (ExcelWriter excelWriter =
                                     EasyExcelFactory.write(zipOutputStream).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle)
                                             .autoCloseStream(false)
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             .registerWriteHandler(columnWidthStyleStrategy)
                                             .build()) {

                            List<List<Object>> list0 = Lists.newArrayList();
                            List<List<String>> header0 = Lists.newArrayList();

                            // 设置表头
                            List<String> headList = vo.getFields().stream().map(FieldData::getFieldName).collect(Collectors.toList());
                            for (String item : headList) {
                                header0.add(List.of(item));
                            }

                            // 导出全部数据
                            List<SampleInspectionResultVo> contentList = new LinkedList<>(inspectionResultVos);

                            // 设置 放置数据
                            fillExcelContentWithFields(list0, headList, contentList, vo.getFields());
                            // 获取sheet对象
                            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "检验结果信息").head(header0).needHead(Boolean.TRUE).build();

                            excelWriter.write(list0, sheet0);

                            excelWriter.finish();
                        } catch (Exception e) {
                            log.error("下载模板错误", e);
                            throw new LimsException(e.getMessage(), e);
                        }
                        zipOutputStream.closeEntry();
                    }

                    // zipOutputStream.finish();
                } catch (Exception e) {
                    log.error("导出数据错误", e);
                    exportRecordDto.setEx(e);
                }
            } else {
                try (ExcelWriter excelWriter =
                             EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle)
                                     .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                     .registerWriteHandler(columnWidthStyleStrategy)
                                     .build()) {

                    List<List<Object>> list0 = Lists.newArrayList();
                    List<List<String>> header0 = Lists.newArrayList();

                    // 导出类型 : 0-报告项目信息 1-人员检验项目信息
                    if (InspectionResultsExportType.isExportReport(vo.getExportType())) {
                        // 设置表头
                        List<String> headList = vo.getFields().stream().map(FieldData::getFieldName).collect(Collectors.toList());
                        for (String item : headList) {
                            header0.add(List.of(item));
                        }
                        List<SampleInspectionResultVo> contentList = new LinkedList<>();

                        // 导出全部数据
                        if (CollectionUtils.isEmpty(vo.getSelectData())) {
                            if (Objects.nonNull(inspectionResult) && CollectionUtils.isNotEmpty(inspectionResult.getResults())) {
                                contentList.addAll(inspectionResult.getResults());
                            }
                        } else {
                            // 前端传入的数据
                            contentList = vo.getSelectData();
                        }

                        // 设置 放置数据
                        fillExcelContentWithFields(list0, headList, contentList, vo.getFields());
                        // 获取sheet对象
                        WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "检验结果信息").head(header0).needHead(Boolean.TRUE).build();

                        excelWriter.write(list0, sheet0);
                    } else {
                        // 设置表头
                        List<String> headList = Lists.newArrayList("姓名", "年龄", "性别", "就诊类型", "送检机构", "检验项目");
                        for (String item : headList) {
                            header0.add(List.of(item));
                        }
                        List<PatientInfoVo> contentList = new LinkedList<>();
                        // 导出前端传入的数据
                        if (CollectionUtils.isNotEmpty(vo.getSelectData())) {
                            contentList = BeanUtil.copyToList(vo.getSelectData(), PatientInfoVo.class);
                        } else {
                            contentList.addAll(inspectionResult.getPatientInfos());
                        }
                        // 设置 放置数据
                        fillExcelTestPersonsContent(list0, headList, contentList);
                        // 获取sheet对象
                        WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "人员检验项目信息").head(header0).needHead(Boolean.TRUE).build();

                        excelWriter.write(list0, sheet0);
                    }
                } catch (Exception e) {
                    log.error("导出数据错误", e);
                    exportRecordDto.setEx(e);
                }
            }

            if (Objects.isNull(exportRecordDto.getEx())) {
                String upload = huaweiObsUtils.upload(new ByteArrayInputStream(out.toByteArray()), uploadType);
                exportRecordDto.setUrl(upload);
            }

            return exportRecordDto;
        });

        return ResponseEntity.ok(new ExportResultVo());
    }

    /**
     *  填充人员检验项目excel
     * @param list0
     * @param headList
     * @param contentList
     */
    private void fillExcelTestPersonsContent(List<List<Object>> list0, List<String> headList, List<PatientInfoVo> contentList) {
        for (PatientInfoVo contentData : contentList) {
            List<Object> content = Lists.newArrayListWithCapacity(headList.size());

            content.add(contentData.getPatientName());
            String age = "";
            if (!Objects.equals(contentData.getPatientAge(), NumberUtils.INTEGER_ZERO)) {
                age = contentData.getPatientAge() + "岁";
                if (!Objects.equals(contentData.getPatientSubage(), NumberUtils.INTEGER_ZERO)) {
                    age += contentData.getPatientSubage() + contentData.getPatientSubageUnit();
                }
            } else {
                age += contentData.getPatientSubage() + contentData.getPatientSubageUnit();
            }
            content.add(age);
            content.add(SexEnum.getByCode(contentData.getPatientSex()).getDesc());
            content.add(contentData.getApplyType());
            content.add(contentData.getHspOrgName());
            content.add(contentData.getTestItemName());
            list0.add(content);
        }
    }

    @PostMapping("/print-results")
    public Object printResults(@RequestBody InspectionResultQueryVo vo) {
        if (vo.getEndDate().before(vo.getStartDate())) {
            throw new IllegalArgumentException("结束时间不能早于开始时间");
        }
        InspectionResultVo inspectionResult = null;
        // 如果前端没有传数据，走正常查询流程
        if (CollectionUtils.isEmpty(vo.getSelectData())) {
            List<Object> searchAfter = null;
            if (Objects.nonNull(vo.getSearchAfter())) {
                searchAfter = Collections.singletonList(vo.getSearchAfter());
            }
            final SampleEsQuery query = new SampleEsQuery();
            query.setIsAudit(YesOrNoEnum.YES.getCode());
            query.setItemTypes(Sets.newHashSet(ItemTypeEnum.ROUTINE.name(), ItemTypeEnum.OUTSOURCING.name()));
            query.setPageNo(vo.getCurrent());
            query.setPageSize(vo.getSize());
            buildQueryParam(query, vo);
            final ScrollPage<BaseSampleEsModelDto> scrollPage = elasticSearchSampleService.searchAfter(searchAfter, query);

            final List<RoutineInspectionDto> samples =
                    scrollPage.getData().stream().filter(RoutineInspectionDto.class::isInstance)
                            .map(e -> (RoutineInspectionDto) e).collect(Collectors.toList());

            final List<OutsourcingInspectionDto> outSamples =
                    scrollPage.getData().stream().filter(OutsourcingInspectionDto.class::isInstance)
                            .map(e -> (OutsourcingInspectionDto) e).collect(Collectors.toList());

            final Map<String, List<OutsourcingInspectionDto>> outMap =
                    outSamples.stream().collect(Collectors.groupingBy(BaseSampleEsModelDto::getHspOrgCode));

            final Map<String, List<RoutineInspectionDto>> map =
                    samples.stream().collect(Collectors.groupingBy(BaseSampleEsModelDto::getHspOrgCode));
            inspectionResult = new InspectionResultVo();
            List<ReportInfoVo> hospitals = new LinkedList<>();

            for (Map.Entry<String, List<OutsourcingInspectionDto>> e : outMap.entrySet()) {
                final ReportInfoVo info = new ReportInfoVo();
                info.setHspOrgCode(e.getKey());
                info.setHspOrgName(e.getValue().iterator().next().getHspOrgName());
                final String id = IdUtil.simpleUUID();
                info.setId(id);
                final List<SampleInspectionResultVo> inspectionResults =
                        convert2OutSampleInspectionResult(e.getValue(), id);
                info.setResults(inspectionResults);
                hospitals.add(info);
            }

            for (Map.Entry<String, List<RoutineInspectionDto>> e : map.entrySet()) {
                final ReportInfoVo info = new ReportInfoVo();
                info.setHspOrgCode(e.getKey());
                info.setHspOrgName(e.getValue().iterator().next().getHspOrgName());
                final String id = IdUtil.simpleUUID();
                info.setId(id);
                final List<SampleInspectionResultVo> inspectionResults = convert2SampleInspectionResult(e.getValue(), id);
                info.setResults(inspectionResults);
                hospitals.add(info);
            }

            // 异常结果
            if (Objects.nonNull(vo.getIsExceptional())) {
                if (Objects.equals(vo.getIsExceptional(), YesOrNoEnum.YES.getCode())) {
                    // 移除非异常
                    for (ReportInfoVo hospital : hospitals) {
                        hospital.getResults().removeIf(f -> (Objects.equals(f.getStatus(), ResultStatusEnum.NORMAL.getCode())
                                || Objects.isNull(f.getStatus())) && StringUtils.isEmpty(f.getJudge()));
                    }
                }
                // 移除删选过后结果是空的数据
                hospitals.removeIf(f -> CollectionUtils.isEmpty(f.getResults()));
            }

            // 结果大小范围过滤
            if (StringUtils.isNotBlank(vo.getFormula1()) && StringUtils.isNotBlank(vo.getResult1())) {

                for (ReportInfoVo hospital : hospitals) {
                    hospital.getResults().removeIf(f -> !NumberUtils.isParsable(f.getResult()));
                    hospital.getResults().removeIf(f -> !RelationalOperatorEnum.valueOfByOperator(vo.getFormula1())
                            .compare(NumberUtils.toDouble(f.getResult()), NumberUtils.toDouble(vo.getResult1())));
                }
                // 移除删选过后结果是空的数据
                hospitals.removeIf(f -> CollectionUtils.isEmpty(f.getResults()));
            }

            if (StringUtils.isNotBlank(vo.getFormula2()) && StringUtils.isNotBlank(vo.getResult2())) {

                for (ReportInfoVo hospital : hospitals) {
                    hospital.getResults().removeIf(f -> !NumberUtils.isParsable(f.getResult()));
                    hospital.getResults().removeIf(f -> !RelationalOperatorEnum.valueOfByOperator(vo.getFormula2())
                            .compare(NumberUtils.toDouble(f.getResult()), NumberUtils.toDouble(vo.getResult2())));
                }
                // 移除删选过后结果是空的数据
                hospitals.removeIf(f -> CollectionUtils.isEmpty(f.getResults()));
            }

            if (CollectionUtils.isNotEmpty(scrollPage.getSearchAfter())) {
                inspectionResult.setSearchAfter(scrollPage.getSearchAfter().iterator().next());
            }
            for (ReportInfoVo hospital : hospitals) {
                hospital.setSize(hospital.getResults().size());
            }
            inspectionResult.setHospitals(hospitals);
        }

        List<SampleInspectionResultVo> targetList = new LinkedList<>();
        String title = "";
        if (CollectionUtils.isEmpty(vo.getHspOrgIds())) {
            title = "所有送检机构查询统计";
        } else {
            final List<HspOrganizationDto> hsps = hspOrganizationService.selectByHspOrgIds(vo.getHspOrgIds());
            final List<String> names =
                    hsps.stream().map(HspOrganizationDto::getHspOrgName).collect(Collectors.toList());
            title = StringUtils.join(names, ",");
        }

        // 打印前端传入的数据
        if (CollectionUtils.isNotEmpty(vo.getSelectData())) {
            targetList = vo.getSelectData();

        } else {
            for (ReportInfoVo hospital : inspectionResult.getHospitals()) {
                targetList.addAll(hospital.getResults());
            }

        }
        final PdfReportParamDto param = new PdfReportParamDto();
        for (SampleInspectionResultVo e : targetList) {
            String tip = "";
            if (StringUtils.equalsIgnoreCase(TestJudgeEnum.UP.name(), e.getJudge())) {
                tip = "↑";
                if (Objects.equals(e.getStatus(), ResultStatusEnum.CRISIS.getCode())) {
                    tip = "↑↑";
                }
            } else if (StringUtils.equalsIgnoreCase(TestJudgeEnum.DOWN.name(), e.getJudge())) {
                tip = "↓";
                if (Objects.equals(e.getStatus(), ResultStatusEnum.CRISIS.getCode())) {
                    tip = "↓↓";
                }
            }
            e.setJudge(tip);
        }
        param.put("title", title);
        param.put("data", targetList);

        // 生成的汇总保存 180 天
        final String pdfUrl = pdfReportService.build2Url(PdfTemplateTypeEnum.INSPECTION_RESULTS.getCode(), param, 3);

        return Map.of("url", pdfUrl);
    }

    static final Map<String, Function<Dict, String>> value = new HashMap<>() {{
        put("patientSex", dict -> SexEnum.getByCode(dict.getInt("patientSex")).getDesc());
        put("patientAge", dict -> PatientAges.toText(dict));
        put("judge", dict -> {
            if (StringUtils.equalsIgnoreCase(TestJudgeEnum.UP.name(), dict.getStr("judge"))) {
                return "↑";
            } else if (StringUtils.equalsIgnoreCase(TestJudgeEnum.DOWN.name(), dict.getStr("judge"))) {
                return "↓";
            }
            return StringPool.EMPTY;
        });
        put("applyDate", dict -> {
            if (Objects.nonNull(dict.getDate("applyDate"))) {
                return DateUtil.format(dict.getDate("applyDate"), DatePattern.NORM_DATETIME_PATTERN);
            }
            return StringPool.EMPTY;
        });
        put("testDate", dict -> {
            if (Objects.nonNull(dict.getDate("testDate"))) {
                return DateUtil.format(dict.getDate("testDate"), DatePattern.NORM_DATETIME_PATTERN);
            }
            return StringPool.EMPTY;
        });
        put("checkDate", dict -> {
            if (Objects.nonNull(dict.getDate("checkDate"))) {
                return DateUtil.format(dict.getDate("checkDate"), DatePattern.NORM_DATETIME_PATTERN);
            }
            return StringPool.EMPTY;
        });
    }};

    private void fillExcelContentWithFields(List<List<Object>> list0, List<String> headList,
                                            List<SampleInspectionResultVo> contentList, List<FieldData> fields) {
        for (SampleInspectionResultVo contentData : contentList) {
            List<Object> content = Lists.newArrayListWithCapacity(headList.size());

            Dict dict = Dict.parse(contentData);
            fields.forEach(fieldData -> {
                Function<Dict, String> valueFunction = value.get(fieldData.getField());
                if (valueFunction != null) {
                    content.add(valueFunction.apply(dict));
                } else {
                    content.add(dict.getStr(fieldData.getField()));
                }

            });

            list0.add(content);
        }
    }

    private void fillExcelContent(List<List<Object>> list0, List<String> headList,
                                  List<SampleInspectionResultVo> contentList) {

        for (SampleInspectionResultVo contentData : contentList) {
            List<Object> content = Lists.newArrayListWithCapacity(headList.size());

            content.add(contentData.getPatientName());
            String age = "";
            if (!Objects.equals(contentData.getPatientAge(), NumberUtils.INTEGER_ZERO)) {
                age = contentData.getPatientAge() + "岁";
                if (!Objects.equals(contentData.getPatientSubage(), NumberUtils.INTEGER_ZERO)) {
                    age += contentData.getPatientSubage() + contentData.getPatientSubageUnit();
                }
            } else {
                age += contentData.getPatientSubage() + contentData.getPatientSubageUnit();
            }
            content.add(age);
            content.add(SexEnum.getByCode(contentData.getPatientSex()).getDesc());
            content.add(contentData.getApplyType());
            content.add(contentData.getHspOrgName());
            content.add(contentData.getBarcode());
            content.add(contentData.getTestItemName());
            content.add(contentData.getReportItemName());
            content.add(contentData.getResult());
            content.add(contentData.getUnit());
            String tip = "";
            if (StringUtils.equalsIgnoreCase(TestJudgeEnum.UP.name(), contentData.getJudge())) {
                tip = "↑";

            } else if (StringUtils.equalsIgnoreCase(TestJudgeEnum.DOWN.name(), contentData.getJudge())) {
                tip = "↓";

            }
            content.add(tip);
            content.add(contentData.getRange());
            content.add(contentData.getSampleTypeName());
            content.add(contentData.getGroupName());
            content.add(contentData.getApplyDate());
            content.add(contentData.getTestDate());
            content.add(contentData.getCheckDate());
            content.add(contentData.getPatientVisitCard());
            content.add(contentData.getSendDoctorName());
            content.add(contentData.getDept());
            content.add(contentData.getPatientBed());
            content.add(contentData.getOutBarcode());
            list0.add(content);
        }

    }

    private List<SampleInspectionResultVo> convert2SampleInspectionResult(List<RoutineInspectionDto> samples,
                                                                          String id) {
        final LinkedList<SampleInspectionResultVo> results = new LinkedList<>();
        for (RoutineInspectionDto sample : samples) {
            if (CollectionUtils.isEmpty(sample.getReportItems())) {
                continue;
            }
            for (RoutineInspectionDto.RoutineReportItem item : sample.getReportItems()) {
                final SampleInspectionResultVo result = new SampleInspectionResultVo();
                result.setPid(id);
                result.setId(IdUtil.simpleUUID());
                result.setApplySampleId(sample.getApplySampleId());
                result.setHspOrgCode(sample.getHspOrgCode());
                result.setHspOrgName(sample.getHspOrgName());
                result.setPatientName(sample.getPatientName());
                result.setPatientSex(sample.getPatientSex());
                result.setPatientAge(sample.getPatientAge());
                result.setPatientSubage(sample.getPatientSubage());
                result.setPatientSubageUnit(sample.getPatientSubageUnit());
                result.setApplyType(sample.getApplyTypeName());
                result.setBarcode(sample.getBarcode());
                result.setTestItemId(item.getTestItemId());
                result.setTestItemCode(item.getTestItemCode());
                result.setTestItemName(item.getTestItemName());
                result.setReportItemCode(item.getReportItemCode());
                result.setReportItemName(item.getReportItemName());
                result.setResult(item.getResult());
                result.setUnit(item.getUnit());
                result.setJudge(item.getJudge());
                result.setRange(item.getRange());
                result.setGroupId(sample.getGroupId());
                result.setGroupName(sample.getGroupName());
                result.setSampleTypeName(sample.getSampleTypeName());
                result.setApplyDate(sample.getCreateDate()); // 1.1.0 检验结果信息查询，送检时间 取样本的创建时间
                result.setTestDate(sample.getTestDate());
                result.setCheckDate(sample.getFinalCheckDate());
                result.setPatientVisitCard(sample.getPatientVisitCard());
                result.setSendDoctorCode(sample.getSendDoctorCode());
                result.setSendDoctorName(sample.getSendDoctorName());
                result.setDept(sample.getDept());
                result.setPatientBed(sample.getPatientBed());
                result.setStatus(item.getStatus());
                results.add(result);
            }
        }

        return results;
    }

    private List<SampleInspectionResultVo> convert2OutSampleInspectionResult(List<OutsourcingInspectionDto> samples,
                                                                             String id) {
        final LinkedList<SampleInspectionResultVo> results = new LinkedList<>();
        for (OutsourcingInspectionDto sample : samples) {
            if (CollectionUtils.isEmpty(sample.getReportItems())) {
                continue;
            }
            for (OutsourcingInspectionDto.OutsourcingReportItem item : sample.getReportItems()) {
                final SampleInspectionResultVo result = new SampleInspectionResultVo();
                result.setPid(id);
                result.setId(IdUtil.simpleUUID());
                result.setApplySampleId(sample.getApplySampleId());
                result.setHspOrgCode(sample.getHspOrgCode());
                result.setHspOrgName(sample.getHspOrgName());
                result.setPatientName(sample.getPatientName());
                result.setPatientSex(sample.getPatientSex());
                result.setPatientAge(sample.getPatientAge());
                result.setPatientSubage(sample.getPatientSubage());
                result.setPatientSubageUnit(sample.getPatientSubageUnit());
                result.setApplyType(sample.getApplyTypeName());
                result.setBarcode(sample.getBarcode());
                result.setTestItemId(item.getTestItemId());
                result.setTestItemCode(item.getTestItemCode());
                result.setTestItemName(item.getTestItemName());
                result.setReportItemCode(item.getReportItemCode());
                result.setReportItemName(item.getReportItemName());
                result.setResult(item.getResult());
                result.setUnit(item.getUnit());
                result.setJudge(item.getJudge());
                result.setRange(item.getRange());
                result.setGroupId(sample.getGroupId());
                result.setGroupName(sample.getGroupName());
                result.setSampleTypeName(sample.getSampleTypeName());
                result.setApplyDate(sample.getCreateDate()); // 1.1.0 检验结果信息查询，送检时间 取样本的创建时间
                result.setTestDate(sample.getTestDate());
                result.setCheckDate(sample.getFinalCheckDate());
                result.setPatientVisitCard(sample.getPatientVisitCard());
                result.setSendDoctorCode(sample.getSendDoctorCode());
                result.setSendDoctorName(sample.getSendDoctorName());
                result.setDept(sample.getDept());
                result.setPatientBed(sample.getPatientBed());
                result.setStatus(item.getStatus());
                results.add(result);
            }
        }

        return results;
    }

    private void buildQueryParam(SampleEsQuery query, InspectionResultQueryVo vo) {
        // 送检时间 / 审核时间
        if (Objects.equals(vo.getDateType(), DateTypeEnum.SEND_DATE.name())) {
            // 1.1.0 检验结果信息查询，送检时间 取样本的创建时间
            query.setStartCreateDate(vo.getStartDate());
            query.setEndCreateDate(vo.getEndDate());
        } else if (Objects.equals(vo.getDateType(), DateTypeEnum.AUDIT_DATE.name())) {
            query.setStartFinalCheckDate(vo.getStartDate());
            query.setEndFinalCheckDate(vo.getEndDate());
        }
        // 送检机构
        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            query.setHspOrgIds(new HashSet<>(vo.getHspOrgIds()));
        }
        // 体检单位
        if (Objects.nonNull(vo.getPhysicalCompanyId())) {
            query.setPhysicalGroupIds(Collections.singleton(vo.getPhysicalCompanyId()));
        }
        // 专业组
        if (Objects.nonNull(vo.getGroupId())) {
            query.setGroupIds(Collections.singleton(vo.getGroupId()));
        }
        // 检验项目
        if (CollectionUtils.isNotEmpty(vo.getTestItemIds())) {
            query.setTestItemIds(new HashSet<>(vo.getTestItemIds()));
        }
        // 姓名
        if (StringUtils.isNotBlank(vo.getPatientName())) {
            query.setPatientName(vo.getPatientName());
        }
        // 就诊类型
        if (CollectionUtils.isNotEmpty(vo.getApplyType())) {
            query.setApplyTypes(vo.getApplyType());
        }

        // 报告项目编码
        if (CollectionUtils.isNotEmpty(vo.getReportItemCodes())) {
            query.setReportItemCodes(new HashSet<>(vo.getReportItemCodes()));
        }

    }

}
