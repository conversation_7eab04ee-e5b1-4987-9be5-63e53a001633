package com.labway.lims.statistics.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <pre>
 * FinancialConfig
 * 财务统计配置
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/4/3 14:27
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "financial")
public class FinancialConfig {

    /**
     * 折后价小数点保留位数
     */
    private Integer discountFeePriceScale = 4;

}
