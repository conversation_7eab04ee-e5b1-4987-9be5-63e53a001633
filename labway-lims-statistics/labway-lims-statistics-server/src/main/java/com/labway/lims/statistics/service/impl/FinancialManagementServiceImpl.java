package com.labway.lims.statistics.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SendTypeEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.stream.StreamUtils;
import com.labway.lims.apply.api.dto.ByPlatformStatisticsItemDto;
import com.labway.lims.apply.api.dto.ByPlatformStatisticsKeyDto;
import com.labway.lims.apply.api.dto.ByPlatformStatisticsResponseDto;
import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsItemDto;
import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsResponseDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleTestItemDto;
import com.labway.lims.apply.api.dto.TestItemIncomeDetailItemDto;
import com.labway.lims.apply.api.dto.TestItemIncomeDetailResponseDto;
import com.labway.lims.apply.api.dto.TestItemIncomeFilterDto;
import com.labway.lims.apply.api.dto.TestItemIncomeSummaryKeyDto;
import com.labway.lims.apply.api.dto.TestItemIncomeSummaryResponseDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.base.api.dto.HspOrgDiscountDto;
import com.labway.lims.base.api.dto.HspOrgPricingDto;
import com.labway.lims.base.api.dto.HspOrgSpecialOfferDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDetailDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageDto;
import com.labway.lims.base.api.dto.ItemPriceBasePackageItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrgDiscountService;
import com.labway.lims.base.api.service.HspOrgPricingService;
import com.labway.lims.base.api.service.HspOrgSpecialOfferService;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ItemPriceBasePackageDetailService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.service.FinancialManagementService;
import com.labway.lims.statistics.vo.HspOrgSendDoctorStatisticsRequestVo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.apply.api.dto.TestItemIncomeFilterDto.getCustomerNameType;
import static com.labway.lims.apply.api.dto.TestItemIncomeFilterDto.getIsFreeFlag;

/**
 * 财务管理 Service impl
 *
 * <AUTHOR>
 * @since 2023/5/15 10:06
 */
@Slf4j
@DubboService
public class FinancialManagementServiceImpl implements FinancialManagementService {

    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private HspOrgPricingService hspOrgPricingService;

    @DubboReference
    private HspOrgSpecialOfferService hspOrgSpecialOfferService;

    @DubboReference
    private HspOrgDiscountService hspOrgDiscountService;
    @DubboReference
    private ItemPriceBasePackageService itemPriceBasePackageService;
    @DubboReference
    private ItemPriceBasePackageDetailService itemPriceBasePackageDetailService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @Override
    public List<BaseSampleEsModelDto> selectSamples(final SampleEsQuery query, HspOrgSendDoctorStatisticsRequestVo vo) {
        final boolean financialMonth = StringUtils.isNotBlank(vo.getFinancialMonth());
        return selectSamples(query, financialMonth);
    }

    @Override
    public List<BaseSampleEsModelDto> selectSamples(SampleEsQuery query, boolean financialMonth) {
        // 样本创建时间
        Date startDate = query.getStartCreateDate();
        Date endDate = query.getEndCreateDate();
        if (financialMonth) {
            startDate = query.getStartFinalCheckOrCreateDate();
            endDate = query.getEndFinalCheckOrCreateDate();
        }

        List<Pair<Date, Date>> datePairs = new ArrayList<>();

        // 判断时间范围，如果时间范围大于24小时，则按24h进行拆分，分批查询
        long between = DateUtil.between(startDate, endDate, DateUnit.HOUR);
        boolean hasNext = true;
        Date start = startDate;
        if (between > 24) {
            do {
                Date end = DateUtil.offsetHour(start, 24);
                if (end.after(endDate)) {
                    hasNext = false;
                    end = endDate;
                }
                datePairs.add(Pair.of(DateUtil.beginOfDay(start), end));
                start = end;
            } while (hasNext);
        } else {
            datePairs.add(Pair.of(startDate, endDate));
        }

        // 根据拆分出来的时间段，分批并行查询数据
        @SuppressWarnings("all")
        CompletableFuture<List<BaseSampleEsModelDto>>[] completableFutures = datePairs.stream().map(datePair -> {
            return CompletableFuture.supplyAsync(() -> {
                SampleEsQuery sampleEsQuery = JSON.parseObject(JSON.toJSONString(query), SampleEsQuery.class);
                // 拆分设置时间参数
                if (financialMonth) {
                    sampleEsQuery.setStartFinalCheckOrCreateDate(datePair.getKey());
                    sampleEsQuery.setEndFinalCheckOrCreateDate(datePair.getValue());
                } else {
                    sampleEsQuery.setStartCreateDate(datePair.getKey());
                    sampleEsQuery.setEndCreateDate(datePair.getValue());
                }
                return elasticSearchSampleService.selectSamples(sampleEsQuery);
            });
        }).collect(Collectors.toList()).toArray(new CompletableFuture[0]);

        try {
            CompletableFuture.allOf(completableFutures).get();
        } catch (Exception e) {
            log.error("调用查询异常", e);
            return Collections.emptyList();
        }

        // 合并并行查出来的数据，过滤掉 null 和 重复的数据
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = Arrays.stream(completableFutures)
                .flatMap(e -> {
                    try {
                        return e.get().stream();
                    } catch (Exception ex) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .filter(StreamUtils.distinctByKey(BaseSampleEsModelDto::getApplySampleId))
                .collect(Collectors.toList());
        // List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(query);

        return baseSampleEsModelDtos;
    }

    @Override
    public List<TestItemIncomeSummaryResponseDto> getTestItemIncomeSummaryResponseDtos(
            SampleEsQuery dto, // 转换后的查询es条件对象
            TestItemIncomeFilterDto filterDto, // 原始的查询es条件对象
            List<BaseSampleEsModelDto> baseSampleEsModelDtosAll) { // es的样本数据
        // 时间
        DateRange dateRange = getDateRange(dto);
        YesOrNoEnum isFreeFlag = getIsFreeFlag(filterDto.getIsFree());
        CustomerNameTypeEnum customerNameType = getCustomerNameType(filterDto.getCustomerNameType());

        // 查询 终止收费的检验项目信息 不一定是审核的
        SampleEsQuery stopStatusEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        stopStatusEsQuery.setIsAudit(null);
        stopStatusEsQuery.setStopStatus(Set.of(StopTestStatus.STOP_TEST_CHARGE.getCode()));
        List<BaseSampleEsModelDto> stopTestChargeEsModelDtos =
                elasticSearchSampleService.selectSamples(stopStatusEsQuery);

        // 查询 病理检验 已经一次分拣
        SampleEsQuery pathologyEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        pathologyEsQuery.setIsAudit(null);
        pathologyEsQuery.setItemTypes(Collections.singleton(ItemTypeEnum.PATHOLOGY.name()));
        // 1.0.6.6需求改为病理组的样本只要创建就纳入统计
        // pathologyEsQuery.setIsOnePick(YesOrNoEnum.YES.getCode());
        List<BaseSampleEsModelDto> pathologyEsModelDtos = elasticSearchSampleService.selectSamples(pathologyEsQuery);

        // 拆分数据
        List<SampleTestItemDto> sampleTestItemDtoListAll = this.handleEsDataToSampleTestItemDto(
                baseSampleEsModelDtosAll, stopTestChargeEsModelDtos, pathologyEsModelDtos, filterDto, false);

        // 以送检机构分组
        Map<Long, List<SampleTestItemDto>> groupingByHspOrgId =
                sampleTestItemDtoListAll.stream().collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId));

        // 所有送检机构信息
        List<HspOrganizationDto> hspOrganizationDtos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(groupingByHspOrgId.keySet())) {
            hspOrganizationDtos = hspOrganizationService.selectByHspOrgIds(groupingByHspOrgId.keySet());
        }
        Map<Long, HspOrganizationDto> hspOrganizationDtoByHspOrgId = hspOrganizationDtos.stream()
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity()));

        // 响应数据结构
        List<TestItemIncomeSummaryResponseDto> targetList = Lists.newArrayListWithCapacity(groupingByHspOrgId.size());
        DecimalFormat percentFormat = new DecimalFormat("0.00%");

        for (Map.Entry<Long, List<SampleTestItemDto>> entry : groupingByHspOrgId.entrySet()) {
            TestItemIncomeSummaryResponseDto target = new TestItemIncomeSummaryResponseDto();
            target.setStartDate(dateRange.getStartDate());
            target.setEndDate(dateRange.getEndDate());

            Long hspOrgId = entry.getKey();
            HspOrganizationDto hspOrganizationDto = hspOrganizationDtoByHspOrgId.get(hspOrgId);
            String customerName = StringUtils.EMPTY;
            String hspOrgName = StringUtils.EMPTY;
            if (Objects.nonNull(hspOrganizationDto)) {
                if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME)) {
                    // 展示开票名称
                    customerName = hspOrganizationDto.getInvoice();
                } else {
                    customerName = hspOrganizationDto.getHspOrgName();
                }

                if (StringUtils.isBlank(customerName)) {
                    customerName = hspOrganizationDto.getHspOrgName();
                }
                hspOrgName = hspOrganizationDto.getHspOrgName();
            }
            
            // 汇总
            {
                target.setCustomerName(customerName);
                target.setCustomerId(hspOrgId);

                List<SampleTestItemDto> sampleTestItemDtoList = entry.getValue();

                // 就诊类型 检验项目 标准收费 折扣率一样的 分组
                Map<TestItemIncomeSummaryKeyDto,
                        List<SampleTestItemDto>> groupingByKeyDto = sampleTestItemDtoList.stream()
                        .collect(Collectors.groupingBy(obj -> new TestItemIncomeSummaryKeyDto(obj.getApplyTypeCode(),
                                obj.getTestItemId(), obj.getPrice(), obj.getDiscount())));

                // 响应数据行
                List<TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem> itemList =
                        Lists.newArrayListWithCapacity(groupingByKeyDto.size());

                // 数量合计、 合计金额合计、结算金额合计
                Integer countSumAll = NumberUtils.INTEGER_ZERO;
                BigDecimal feePriceSumAll = BigDecimal.ZERO;
                BigDecimal payAmountSumAll = BigDecimal.ZERO;

                for (Map.Entry<TestItemIncomeSummaryKeyDto, List<SampleTestItemDto>> groupingByKey : groupingByKeyDto
                        .entrySet()) {
                    TestItemIncomeSummaryKeyDto keyDto = groupingByKey.getKey();
                    List<SampleTestItemDto> summaryItemList = groupingByKey.getValue();

                    int countSum = summaryItemList.stream()
                            .map(obj -> ObjectUtils.defaultIfNull(obj.getCount(), NumberUtils.INTEGER_ONE))
                            .mapToInt(Integer::intValue).sum();
                    BigDecimal feePriceSum = keyDto.getPrice().multiply(BigDecimal.valueOf(countSum));
                    // 合计金额
                    BigDecimal priceSum = summaryItemList.stream()
                            .map(item -> item.getPrice()
                                    .multiply(
                                            BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                    .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 未免单 金额合计 用于结算金额
                    BigDecimal payAmount =
                            summaryItemList.stream().filter(obj -> !Objects.equals(isFreeFlag, YesOrNoEnum.YES)).map(item -> {
                                BigDecimal price = item.getPrice();
                                if (item.isSpecialOfferFlag()) {
                                    // 该样本检验项目 参与了特价项目 结算金额为折后价格
                                    price = ObjectUtils.defaultIfNull(item.getDiscountPrice(), BigDecimal.ZERO);
                                    return price
                                            .multiply(BigDecimal
                                                    .valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                            .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                                }
                                return price
                                        .multiply(
                                                BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                        .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).multiply(keyDto.getDiscount())
                                        .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                            }).reduce(BigDecimal.ZERO, BigDecimal::add);

                    countSumAll = countSumAll + countSum;
                    feePriceSumAll = feePriceSumAll.add(priceSum);
                    payAmountSumAll = payAmountSumAll.add(payAmount);

                    TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem temp =
                            new TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem();

                    temp.setCustomerName(hspOrgName);
                    temp.setApplyTypeName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getApplyTypeName());
                    temp.setApplyTypeCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getApplyTypeCode());
                    temp.setTestItemCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemCode());
                    temp.setTestItemName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemName());
                    temp.setCount(countSum);
                    temp.setFeePrice(keyDto.getPrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                    temp.setFeePriceSum(feePriceSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                    temp.setDiscount(percentFormat.format(keyDto.getDiscount()));
                    temp.setPayAmount(payAmount.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                    itemList.add(temp);

                }
                List<TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem> sorted = itemList.stream()
                        .sorted(Comparator.nullsLast(Comparator
                                .comparing(TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem::getApplyTypeCode)
                                .thenComparing(TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem::getTestItemCode)))
                        .collect(Collectors.toList());

                target.setItemList(sorted);
                target.setCountSum(countSumAll);
                target.setFeePriceSum(feePriceSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                target.setPayAmountSum(payAmountSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            }

            // 明细
            {
                List<SampleTestItemDto> value = entry.getValue();

                // 相同申请单合并
                Map<Long, List<SampleTestItemDto>> groupingByApplyId =
                        value.stream().collect(Collectors.groupingBy(SampleTestItemDto::getApplyId));

                // 数量合计 标准收费合计
                Integer countSumAll = NumberUtils.INTEGER_ZERO;
                BigDecimal feePriceSumAll = BigDecimal.ZERO;
                BigDecimal discountFeePriceSumAll = BigDecimal.ZERO;

                // 拆分为行 一个申请单就是一行 数据
                List<TestItemIncomeDetailItemDto> itemDtoList = Lists.newArrayList();
                for (Map.Entry<Long, List<SampleTestItemDto>> line : groupingByApplyId.entrySet()) {
                    List<SampleTestItemDto> summaryItemList = line.getValue();

                    for (SampleTestItemDto e : summaryItemList) {
                        TestItemIncomeDetailItemDto itemDto = new TestItemIncomeDetailItemDto();
                        itemDto.setCustomerName(hspOrgName);
                        itemDto.setSendDate(DateUtil.formatDate(e.getApplyDate()));
                        itemDto.setSendDoctorName(e.getSendDoctorName());
                        itemDto.setPatientVisitCard(e.getPatientVisitCard());
                        itemDto.setPatientName(e.getPatientName());
                        itemDto.setPatientSex(SexEnum.getByCode(e.getPatientSex()).getDesc());
                        itemDto.setPatientAge(PatientAges.toText(e));
                        itemDto.setApplyTypeName(e.getApplyTypeName());
                        itemDto.setTestItems(e.getTestItemName());
                        itemDto.setCount(ObjectUtils.defaultIfNull(e.getCount(), NumberUtils.INTEGER_ONE));
                        itemDto.setFeePrice(e.getPrice().multiply(BigDecimal.valueOf(itemDto.getCount())).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                        itemDto.setDiscount(e.getDiscount().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                        itemDto.setDiscountLabel(itemDto.getDiscount().multiply(new BigDecimal(100)).setScale(NumberUtils.INTEGER_ZERO, RoundingMode.HALF_UP) + StringPool.PERCENT);
                        itemDto.setDiscountFeePrice(e.getPrice().multiply(e.getDiscount()).multiply(BigDecimal.valueOf(itemDto.getCount())).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                        itemDto.setDiscountFeePriceLabel(itemDto.getDiscountFeePrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP) + StringPool.EMPTY);
                        itemDtoList.add(itemDto);

                        countSumAll += itemDto.getCount();
                        feePriceSumAll = feePriceSumAll.add(itemDto.getFeePrice());
                        discountFeePriceSumAll = discountFeePriceSumAll.add(itemDto.getDiscountFeePrice());
                    }
                }

                List<TestItemIncomeDetailItemDto> sorted = itemDtoList.stream()
                        .sorted(Comparator.comparing(TestItemIncomeDetailItemDto::getSendDate)).collect(Collectors.toList());
                sorted.forEach(item -> {
                    if (Objects.equals(item.getSendDate(), DateUtil.formatDate(DefaultDateEnum.DEFAULT_DATE.getDate()))) {
                        item.setSendDate(StringUtils.EMPTY);
                    }
                });

                TestItemIncomeDetailResponseDto d = new TestItemIncomeDetailResponseDto();
                d.setCustomerName(customerName);
                d.setStartDate(dateRange.getStartDate());
                d.setEndDate(dateRange.getEndDate());
                d.setItemList(sorted);
                d.setCountSum(countSumAll);
                d.setFeePriceSum(feePriceSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                d.setDiscountFeePriceSum(discountFeePriceSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                target.setDetail(d);
            }

            targetList.add(target);
        }

        return targetList;

    }

    @Override
    public List<TestItemIncomeDetailResponseDto> getTestItemIncomeDetailResponseDto(SampleEsQuery dto,
                                                                                    TestItemIncomeFilterDto filterDto, List<BaseSampleEsModelDto> baseSampleEsModelDtosAll) {

        // 时间
        DateRange dateRange = getDateRange(dto);
        CustomerNameTypeEnum customerNameType = getCustomerNameType(filterDto.getCustomerNameType());

        // 查询 终止收费的检验项目信息 不一定是审核的
        SampleEsQuery stopStatusEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        stopStatusEsQuery.setIsAudit(null);
        stopStatusEsQuery.setStopStatus(Set.of(StopTestStatus.STOP_TEST_CHARGE.getCode()));
        List<BaseSampleEsModelDto> stopTestChargeEsModelDtos =
                elasticSearchSampleService.selectSamples(stopStatusEsQuery);

        // 查询 病理检验 已经一次分拣
        SampleEsQuery pathologyEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        pathologyEsQuery.setIsAudit(null);
        pathologyEsQuery.setItemTypes(Collections.singleton(ItemTypeEnum.PATHOLOGY.name()));
        // 1.0.6.6需求改为病理组的样本只要创建就纳入统计
        // pathologyEsQuery.setIsOnePick(YesOrNoEnum.YES.getCode());
        List<BaseSampleEsModelDto> pathologyEsModelDtos = elasticSearchSampleService.selectSamples(pathologyEsQuery);

        // 拆分数据
        List<SampleTestItemDto> sampleTestItemDtoListAll = this.handleEsDataToSampleTestItemDto(
                baseSampleEsModelDtosAll, stopTestChargeEsModelDtos, pathologyEsModelDtos, filterDto, false);

        // 以送检机构分组
        Map<Long, List<SampleTestItemDto>> groupingByHspOrgId =
                sampleTestItemDtoListAll.stream().collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId));

        // 所有送检机构信息
        List<HspOrganizationDto> hspOrganizationDtos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(groupingByHspOrgId.keySet())) {
            hspOrganizationDtos = hspOrganizationService.selectByHspOrgIds(groupingByHspOrgId.keySet());
        }
        Map<Long, HspOrganizationDto> hspOrganizationDtoByHspOrgId = hspOrganizationDtos.stream()
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity()));

        // 响应数据结构
        List<TestItemIncomeDetailResponseDto> targetList = Lists.newArrayListWithCapacity(groupingByHspOrgId.size());

        for (Map.Entry<Long, List<SampleTestItemDto>> entry : groupingByHspOrgId.entrySet()) {
            Long hspOrgId = entry.getKey();
            HspOrganizationDto hspOrganizationDto = hspOrganizationDtoByHspOrgId.get(hspOrgId);
            String customerName = StringUtils.EMPTY;
            if (Objects.nonNull(hspOrganizationDto)) {
                if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME)) {
                    // 展示开票名称
                    customerName = hspOrganizationDto.getInvoice();
                } else {
                    customerName = hspOrganizationDto.getHspOrgName();
                }
            }

            List<SampleTestItemDto> value = entry.getValue();

            // 相同申请单合并
            Map<Long, List<SampleTestItemDto>> groupingByApplyId =
                    value.stream().collect(Collectors.groupingBy(SampleTestItemDto::getApplyId));

            // 数量合计 标准收费合计
            Integer countSumAll = NumberUtils.INTEGER_ZERO;
            BigDecimal feePriceSumAll = BigDecimal.ZERO;

            // 拆分为行 一个申请单就是一行 数据
            List<TestItemIncomeDetailItemDto> itemDtoList = Lists.newArrayList();
            for (Map.Entry<Long, List<SampleTestItemDto>> line : groupingByApplyId.entrySet()) {

                List<SampleTestItemDto> summaryItemList = line.getValue();

                int countSum = summaryItemList.stream()
                        .map(obj -> ObjectUtils.defaultIfNull(obj.getCount(), NumberUtils.INTEGER_ONE))
                        .mapToInt(Integer::intValue).sum();
                // 合计金额
                BigDecimal feePriceSum = summaryItemList.stream()
                        .map(item -> item.getPrice()
                                .multiply(
                                        BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 所有检验项目
                String testItemNames = summaryItemList.stream().map(SampleTestItemDto::getTestItemName)
                        .filter(Objects::nonNull).collect(Collectors.joining(","));

                SampleTestItemDto model = summaryItemList.get(NumberUtils.INTEGER_ZERO);
                TestItemIncomeDetailItemDto itemDto = new TestItemIncomeDetailItemDto();
                itemDto.setSendDate(DateUtil.formatDate(model.getSignDate()));
                itemDto.setPatientVisitCard(model.getPatientVisitCard());
                itemDto.setPatientName(model.getPatientName());
                itemDto.setPatientSex(SexEnum.getByCode(model.getPatientSex()).getDesc());
                itemDto.setPatientAge(PatientAges.toText(model));
                itemDto.setApplyTypeName(model.getApplyTypeName());
                itemDto.setTestItems(testItemNames);
                itemDto.setCount(countSum);
                itemDto.setFeePriceSum(feePriceSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                itemDtoList.add(itemDto);

                countSumAll = countSumAll + itemDto.getCount();
                feePriceSumAll = feePriceSumAll.add(feePriceSum);

            }
            List<TestItemIncomeDetailItemDto> sorted = itemDtoList.stream()
                    .sorted(Comparator.comparing(TestItemIncomeDetailItemDto::getSendDate)).collect(Collectors.toList());
            sorted.forEach(item -> {
                if (Objects.equals(item.getSendDate(), DateUtil.formatDate(DefaultDateEnum.DEFAULT_DATE.getDate()))) {
                    item.setSendDate(StringUtils.EMPTY);
                }
            });
            TestItemIncomeDetailResponseDto target = new TestItemIncomeDetailResponseDto();
            target.setCustomerName(customerName);
            target.setStartDate(dateRange.getStartDate());
            target.setEndDate(dateRange.getEndDate());
            target.setItemList(sorted);
            target.setCountSum(countSumAll);
            target.setFeePriceSum(feePriceSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            targetList.add(target);

        }

        return targetList;
    }

    @Override
    public List<TestItemIncomeDetailResponseDto> getTestItemIncomeDetailResponseDto2(SampleEsQuery dto,
                                                                                     TestItemIncomeFilterDto filterDto, List<BaseSampleEsModelDto> baseSampleEsModelDtosAll) {
        // 时间
        DateRange dateRange = getDateRange(dto);
        CustomerNameTypeEnum customerNameType = getCustomerNameType(filterDto.getCustomerNameType());

        // 查询 终止收费的检验项目信息 不一定是审核的
        SampleEsQuery stopStatusEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        stopStatusEsQuery.setIsAudit(null);
        stopStatusEsQuery.setStopStatus(Set.of(StopTestStatus.STOP_TEST_CHARGE.getCode()));
        List<BaseSampleEsModelDto> stopTestChargeEsModelDtos =
                elasticSearchSampleService.selectSamples(stopStatusEsQuery);

        // 查询 病理检验 已经一次分拣
        SampleEsQuery pathologyEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        pathologyEsQuery.setIsAudit(null);
        pathologyEsQuery.setItemTypes(Collections.singleton(ItemTypeEnum.PATHOLOGY.name()));
        pathologyEsQuery.setIsOnePick(YesOrNoEnum.YES.getCode());
        List<BaseSampleEsModelDto> pathologyEsModelDtos = elasticSearchSampleService.selectSamples(pathologyEsQuery);

        // 拆分数据
        List<SampleTestItemDto> sampleTestItemDtoListAll = this.handleEsDataToSampleTestItemDto(
                baseSampleEsModelDtosAll, stopTestChargeEsModelDtos, pathologyEsModelDtos, filterDto, false);

        // 以送检机构分组
        Map<Long, List<SampleTestItemDto>> groupingByHspOrgId =
                sampleTestItemDtoListAll.stream().collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId));

        // 所有送检机构信息
        List<HspOrganizationDto> hspOrganizationDtos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(groupingByHspOrgId.keySet())) {
            hspOrganizationDtos = hspOrganizationService.selectByHspOrgIds(groupingByHspOrgId.keySet());
        }
        Map<Long, HspOrganizationDto> hspOrganizationDtoByHspOrgId = hspOrganizationDtos.stream()
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity()));

        // 响应数据结构
        List<TestItemIncomeDetailResponseDto> targetList = Lists.newArrayListWithCapacity(groupingByHspOrgId.size());

        for (Map.Entry<Long, List<SampleTestItemDto>> entry : groupingByHspOrgId.entrySet()) {
            Long hspOrgId = entry.getKey();
            HspOrganizationDto hspOrganizationDto = hspOrganizationDtoByHspOrgId.get(hspOrgId);
            String customerName = StringUtils.EMPTY;
            if (Objects.nonNull(hspOrganizationDto)) {
                if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME)) {
                    // 展示开票名称
                    customerName = hspOrganizationDto.getInvoice();
                } else {
                    customerName = hspOrganizationDto.getHspOrgName();
                }
            }

            List<SampleTestItemDto> value = entry.getValue();

            // 相同申请单合并
            Map<Long, List<SampleTestItemDto>> groupingByApplyId =
                    value.stream().collect(Collectors.groupingBy(SampleTestItemDto::getApplyId));

            // 数量合计 标准收费合计，折后总额
            Integer countSumAll = NumberUtils.INTEGER_ZERO;
            BigDecimal feePriceSumAll = BigDecimal.ZERO;
            BigDecimal discountFeePriceSumAll = BigDecimal.ZERO;

            // 拆分为行 一个申请单就是一行 数据
            List<TestItemIncomeDetailItemDto> itemDtoList = Lists.newArrayList();
            for (Map.Entry<Long, List<SampleTestItemDto>> line : groupingByApplyId.entrySet()) {

                List<SampleTestItemDto> summaryItemList = line.getValue();

                for (SampleTestItemDto e : summaryItemList) {
                    TestItemIncomeDetailItemDto itemDto = new TestItemIncomeDetailItemDto();
                    itemDto.setSendDate(DateUtil.formatDate(e.getApplyDate()));
                    itemDto.setSendDoctorName(e.getSendDoctorName());
                    itemDto.setPatientVisitCard(e.getPatientVisitCard());
                    itemDto.setPatientName(e.getPatientName());
                    itemDto.setPatientSex(SexEnum.getByCode(e.getPatientSex()).getDesc());
                    itemDto.setPatientAge(PatientAges.toText(e));
                    itemDto.setApplyTypeName(e.getApplyTypeName());
                    itemDto.setTestItems(e.getTestItemName());
                    itemDto.setCount(ObjectUtils.defaultIfNull(e.getCount(), NumberUtils.INTEGER_ONE));
                    itemDto.setFeePrice(e.getPrice().multiply(BigDecimal.valueOf(itemDto.getCount())).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                    itemDto.setDiscount(e.getDiscount().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                    itemDto.setDiscountLabel(itemDto.getDiscount().multiply(new BigDecimal(100)).setScale(NumberUtils.INTEGER_ZERO, RoundingMode.HALF_UP) + StringPool.PERCENT);
                    itemDto.setDiscountFeePrice(e.getPrice().multiply(e.getDiscount()).multiply(BigDecimal.valueOf(itemDto.getCount())).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                    itemDto.setDiscountFeePriceLabel(itemDto.getDiscountFeePrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP) + StringPool.EMPTY);
                    itemDtoList.add(itemDto);

                    countSumAll += itemDto.getCount();
                    feePriceSumAll = feePriceSumAll.add(itemDto.getFeePrice());
                    discountFeePriceSumAll = discountFeePriceSumAll.add(itemDto.getDiscountFeePrice());
                }
            }
            List<TestItemIncomeDetailItemDto> sorted = itemDtoList.stream()
                    .sorted(Comparator.comparing(TestItemIncomeDetailItemDto::getSendDate)).collect(Collectors.toList());

            TestItemIncomeDetailResponseDto target = new TestItemIncomeDetailResponseDto();
            target.setCustomerName(customerName);
            target.setStartDate(dateRange.getStartDate());
            target.setEndDate(dateRange.getEndDate());
            target.setItemList(sorted);
            target.setCountSum(countSumAll);
            target.setFeePriceSum(feePriceSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            target.setDiscountFeePriceSum(discountFeePriceSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            targetList.add(target);
        }

        return targetList;
    }

    @Override
    public List<HspOrgSendDoctorStatisticsResponseDto> getHspOrgSendDoctorStatisticsResponseDto(
            SampleEsQuery dto, TestItemIncomeFilterDto filterDto, List<BaseSampleEsModelDto> baseSampleEsModelDtosAll) {
        // 时间
        DateRange dateRange = getDateRange(dto);
        CustomerNameTypeEnum customerNameType = getCustomerNameType(filterDto.getCustomerNameType());

        // 查询 终止收费的检验项目信息 不一定是审核的
        SampleEsQuery stopStatusEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        stopStatusEsQuery.setIsAudit(null);
        stopStatusEsQuery.setStopStatus(Set.of(StopTestStatus.STOP_TEST_CHARGE.getCode()));
        List<BaseSampleEsModelDto> stopTestChargeEsModelDtos =
                elasticSearchSampleService.selectSamples(stopStatusEsQuery);

        // 查询 病理检验 已经一次分拣
        SampleEsQuery pathologyEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        pathologyEsQuery.setIsAudit(null);
        pathologyEsQuery.setItemTypes(Collections.singleton(ItemTypeEnum.PATHOLOGY.name()));
        // 1.0.6.6需求改为病理组的样本只要创建就纳入统计
        // pathologyEsQuery.setIsOnePick(YesOrNoEnum.YES.getCode());
        List<BaseSampleEsModelDto> pathologyEsModelDtos = elasticSearchSampleService.selectSamples(pathologyEsQuery);

        // 拆分数据
        List<SampleTestItemDto> sampleTestItemDtoListAll = this.handleEsDataToSampleTestItemDto(
                baseSampleEsModelDtosAll, stopTestChargeEsModelDtos, pathologyEsModelDtos, filterDto, false);

        // 以送检机构分组
        Map<Long, List<SampleTestItemDto>> groupingByHspOrgId =
                sampleTestItemDtoListAll.stream().collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId));

        // 所有送检机构信息
        List<HspOrganizationDto> hspOrganizationDtos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(dto.getHspOrgIds())) { // 没传送检机构就查全部
            hspOrganizationDtos = hspOrganizationService.selectAll().stream()
                    .filter(f -> Objects.equals(f.getIsExport(), YesOrNoEnum.NO.getCode()))
                    .collect(Collectors.toList());
        } else if (CollectionUtils.isNotEmpty(groupingByHspOrgId.keySet())) {
            hspOrganizationDtos = hspOrganizationService.selectByHspOrgIds(groupingByHspOrgId.keySet());
        }
        Map<Long, HspOrganizationDto> hspOrganizationDtoByHspOrgId = hspOrganizationDtos.stream()
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity(), (a, b) -> b));

        // 响应数据结构
        List<HspOrgSendDoctorStatisticsResponseDto> targetList = Lists.newArrayListWithCapacity(groupingByHspOrgId.size());
        DecimalFormat percentFormat = new DecimalFormat("0.00%");

        for (Map.Entry<Long, HspOrganizationDto> entry : hspOrganizationDtoByHspOrgId.entrySet()) {
            Long hspOrgId = entry.getKey();
            HspOrganizationDto hspOrganizationDto = entry.getValue();
            String customerName = StringUtils.EMPTY;
            if (Objects.nonNull(hspOrganizationDto)) {
                if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME)
                        && StringUtils.isNotBlank(hspOrganizationDto.getInvoice())) {
                    // 展示开票名称
                    customerName = hspOrganizationDto.getInvoice();
                } else {
                    customerName = hspOrganizationDto.getHspOrgName();
                }
            }

            List<SampleTestItemDto> value = groupingByHspOrgId.get(hspOrgId);

            if (CollectionUtils.isEmpty(value)) {
                HspOrgSendDoctorStatisticsResponseDto target = new HspOrgSendDoctorStatisticsResponseDto();
                target.setHspOrgId(hspOrgId);
                target.setCustomerName(customerName);
                target.setStartDate(dateRange.getStartDate());
                target.setEndDate(dateRange.getEndDate());
                target.setItemList(Collections.emptyList());
                target.setCountSum(NumberUtils.INTEGER_ZERO);
                target.setFeePriceSum(BigDecimal.ZERO);
                target.setPayAmountSum(BigDecimal.ZERO);
                target.setTotalFeePriceSum(BigDecimal.ZERO);
                targetList.add(target);

                continue;
            }

            // 申请单 + 检验项目+相同单价+相同折扣率 分组
            Map<String,
                    List<SampleTestItemDto>> groupingByLineKey = value.stream()
                    .collect(Collectors.groupingBy(obj -> obj.getApplyId() + "-" + obj.getTestItemId() + "-"
                            + obj.getPrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP) + "-"
                            + obj.getDiscount().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP)));

            // 数量合计、 合计金额合计、结算金额合计
            Integer countSumAll = NumberUtils.INTEGER_ZERO;
            BigDecimal feePriceSumAll = BigDecimal.ZERO;
            BigDecimal totalFeePriceSum = BigDecimal.ZERO;
            BigDecimal payAmountSumAll = BigDecimal.ZERO;

            // 拆分为行 一个申请单下相同检验项目、相同金额、相同折扣率 是一行 数据
            List<HspOrgSendDoctorStatisticsItemDto> itemDtoList = Lists.newArrayList();
            for (Map.Entry<String, List<SampleTestItemDto>> line : groupingByLineKey.entrySet()) {
                List<SampleTestItemDto> summaryItemList = line.getValue();

                SampleTestItemDto model = summaryItemList.get(NumberUtils.INTEGER_ZERO);

                // 未免单 金额合计 用于结算金额
                BigDecimal payAmount = summaryItemList.stream()
                        .filter(obj -> !Objects.equals(obj.getIsFree(), YesOrNoEnum.YES.getCode())).map(item -> {
                            BigDecimal price = item.getPrice();
                            if (item.isSpecialOfferFlag()) {
                                // 该样本检验项目 参与了特价项目 结算金额为折后价格
                                price = ObjectUtils.defaultIfNull(item.getDiscountPrice(), BigDecimal.ZERO);
                                return price
                                        .multiply(BigDecimal
                                                .valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                        .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                            }
                            return price
                                    .multiply(
                                            BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                    .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).multiply(model.getDiscount())
                                    .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                        }).reduce(BigDecimal.ZERO, BigDecimal::add);

                int countSum = summaryItemList.stream()
                        .map(obj -> ObjectUtils.defaultIfNull(obj.getCount(), NumberUtils.INTEGER_ONE))
                        .mapToInt(Integer::intValue).sum();

                HspOrgSendDoctorStatisticsItemDto itemDto = new HspOrgSendDoctorStatisticsItemDto();
                itemDto.setHspOrgName(model.getHspOrgName());
                itemDto.setOutBarcode(model.getOutBarcode());
                itemDto.setPatientVisitCard(model.getPatientVisitCard());
                itemDto.setPatientName(model.getPatientName());
                itemDto.setPatientAge(PatientAges.toText(model));
                itemDto.setPatientSex(SexEnum.getByCode(model.getPatientSex()).getDesc());
                itemDto.setSendDate(DateUtil.formatDate(model.getSignDate()));
                if (Objects.nonNull(model.getFinalCheckDate())
                        && !Objects.equals(DefaultDateEnum.DEFAULT_DATE.getDate(), model.getFinalCheckDate())) {
                    itemDto.setFinalCheckDate(DateUtil.formatDate(model.getFinalCheckDate()));
                }
                itemDto.setDept(model.getDept());
                itemDto.setSendDoctorName(model.getSendDoctorName());
                itemDto.setApplyTypeName(model.getApplyTypeName());
                itemDto.setTestItemCode(model.getTestItemCode());
                itemDto.setTestItem(model.getTestItemName());
                itemDto.setCount(countSum);
                itemDto.setFeePrice(model.getPrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                itemDto.setTotalFeePrice(itemDto.getFeePrice().multiply(BigDecimal.valueOf(itemDto.getCount()))
                        .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                itemDto.setDiscount(percentFormat.format(model.getDiscount()));
                itemDto.setPayAmount(payAmount);
                itemDtoList.add(itemDto);

                countSumAll = countSumAll + itemDto.getCount();
                feePriceSumAll = feePriceSumAll.add(model.getPrice());
                payAmountSumAll = payAmountSumAll.add(itemDto.getPayAmount());
                totalFeePriceSum = totalFeePriceSum.add(itemDto.getTotalFeePrice());
            }
            List<HspOrgSendDoctorStatisticsItemDto> sorted = itemDtoList.stream()
                    .sorted(Comparator.nullsLast(
                            Comparator.comparing(HspOrgSendDoctorStatisticsItemDto::getSendDate)
                            .thenComparing(HspOrgSendDoctorStatisticsItemDto::getPatientName)
                            .thenComparing(HspOrgSendDoctorStatisticsItemDto::getApplyTypeName)))
                    .collect(Collectors.toList());

            sorted.forEach(item -> {
                if (Objects.equals(item.getSendDate(), DateUtil.formatDate(DefaultDateEnum.DEFAULT_DATE.getDate()))) {
                    item.setSendDate(StringUtils.EMPTY);
                }
            });
            HspOrgSendDoctorStatisticsResponseDto target = new HspOrgSendDoctorStatisticsResponseDto();
            target.setHspOrgId(hspOrgId);
            target.setCustomerName(customerName);
            target.setStartDate(dateRange.getStartDate());
            target.setEndDate(dateRange.getEndDate());
            target.setItemList(sorted);
            target.setCountSum(countSumAll);
            target.setFeePriceSum(feePriceSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            target.setPayAmountSum(payAmountSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            target.setTotalFeePriceSum(totalFeePriceSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            targetList.add(target);
        }

        return targetList;
    }

    @Override
    public ByPlatformStatisticsResponseDto getByPlatformStatisticsResponseDto(
            SampleEsQuery dto, CustomerNameTypeEnum customerNameType,
            List<HspOrganizationDto> hspOrganizationDtos, List<BaseSampleEsModelDto> baseSampleEsModelDtosAll) {

        ByPlatformStatisticsResponseDto result = new ByPlatformStatisticsResponseDto();
        List<ByPlatformStatisticsResponseDto> orgList = new ArrayList<>();
        result.setOrgList(orgList);

        // 时间
        DateRange dateRange = getDateRange(dto);
        // 拆分数据
        TestItemIncomeFilterDto filterDto = new TestItemIncomeFilterDto();

        // 查询 终止收费的检验项目信息 不一定是审核的
        SampleEsQuery stopStatusEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        stopStatusEsQuery.setIsAudit(null);
        stopStatusEsQuery.setStopStatus(Set.of(StopTestStatus.STOP_TEST_CHARGE.getCode()));
        List<BaseSampleEsModelDto> stopTestChargeEsModelDtos =
                elasticSearchSampleService.selectSamples(stopStatusEsQuery);

        // 查询 病理检验 已经一次分拣
        SampleEsQuery pathologyEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        pathologyEsQuery.setIsAudit(null);
        pathologyEsQuery.setItemTypes(Collections.singleton(ItemTypeEnum.PATHOLOGY.name()));
        // 1.0.6.6需求改为病理组的样本只要创建就纳入统计
        // pathologyEsQuery.setIsOnePick(YesOrNoEnum.YES.getCode());
        List<BaseSampleEsModelDto> pathologyEsModelDtos = elasticSearchSampleService.selectSamples(pathologyEsQuery);

        List<SampleTestItemDto> sampleTestItemDtoList = this.handleEsDataToSampleTestItemDto(baseSampleEsModelDtosAll,
                stopTestChargeEsModelDtos, pathologyEsModelDtos, filterDto, true);

        Map<Long, HspOrganizationDto> organizationDtoMap = hspOrganizationDtos.stream()
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity(), (k1, k2) -> k1));
        Map<Long, List<SampleTestItemDto>> groupByHspOrgId = sampleTestItemDtoList.stream()
                .collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId));
        // 数量合计、 合计金额合计、结算金额合计
        Integer countTotal = NumberUtils.INTEGER_ZERO;
        BigDecimal payAmountBeforeTotal = BigDecimal.ZERO;
        BigDecimal payAmountAfterTotal = BigDecimal.ZERO;

        for (HspOrganizationDto hspOrganizationDto : hspOrganizationDtos) {
            Long hspOrgId = hspOrganizationDto.getHspOrgId();
            List<SampleTestItemDto> sampleTestItemDtos = groupByHspOrgId.get(hspOrgId);

            String customerName;
            if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME)
                    && StringUtils.isNotBlank(hspOrganizationDto.getInvoice())) {
                // 展示开票名称
                customerName = hspOrganizationDto.getInvoice();
            } else {
                customerName = hspOrganizationDto.getHspOrgName();
            }

            if (CollectionUtils.isEmpty(sampleTestItemDtos)) {
                ByPlatformStatisticsResponseDto orgItem = new ByPlatformStatisticsResponseDto();
                orgItem.setHspOrgId(hspOrganizationDto.getHspOrgId());
                orgItem.setCustomerName(customerName);
                orgItem.setStartDate(dateRange.getStartDate());
                orgItem.setEndDate(dateRange.getEndDate());
                orgItem.setCountSum(NumberUtils.INTEGER_ZERO);
                orgItem.setPayAmountBeforeSum(BigDecimal.ZERO.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                orgItem.setPayAmountAfterSum(BigDecimal.ZERO.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                orgItem.setOutOrgStatisticsItems(Collections.emptyList());
                orgItem.setItemList(Collections.emptyList());

                orgList.add(orgItem);
                continue;
            }

            // 财务专业组 就诊类型 检验项目 标准收费 折扣率一样的 分组
            Map<ByPlatformStatisticsKeyDto,
                    List<SampleTestItemDto>> groupingByKeyDto = sampleTestItemDtos.stream()
                    .collect(Collectors.groupingBy(obj -> new ByPlatformStatisticsKeyDto(obj.getFinanceGroupCode(),
                            obj.getApplyTypeCode(), obj.getTestItemId(), obj.getPrice(), obj.getDiscount())));
            List<ByPlatformStatisticsItemDto> itemList = Lists.newArrayListWithCapacity(groupingByKeyDto.size());

            // 数量合计、 合计金额合计、结算金额合计
            Integer countSumAll = NumberUtils.INTEGER_ZERO;
            BigDecimal payAmountBeforeSum = BigDecimal.ZERO;
            BigDecimal payAmountAfterSum = BigDecimal.ZERO;

            for (Map.Entry<ByPlatformStatisticsKeyDto, List<SampleTestItemDto>> groupingByKey : groupingByKeyDto
                    .entrySet()) {
                ByPlatformStatisticsKeyDto keyDto = groupingByKey.getKey();
                List<SampleTestItemDto> summaryItemListOuter = groupingByKey.getValue();

                Map<String, List<SampleTestItemDto>> groupingByDate = summaryItemListOuter.stream().collect(Collectors.groupingBy(item -> DateUtil.format(item.getCreateDate(), DatePattern.NORM_MONTH_PATTERN)));

                for (Map.Entry<String, List<SampleTestItemDto>> entry : groupingByDate.entrySet()) {
                    String sendDate = entry.getKey();
                    List<SampleTestItemDto> summaryItemList = entry.getValue();

                    int countSum =
                            summaryItemList.stream().map(obj -> ObjectUtils.defaultIfNull(obj.getCount(), NumberUtils.INTEGER_ONE))
                                    .mapToInt(Integer::intValue).sum();
                    BigDecimal feePriceSum = keyDto.getPrice().multiply(BigDecimal.valueOf(countSum));
                    // 未免单 金额合计 用于结算金额
                    BigDecimal payAmount =
                            summaryItemList.stream().filter(obj -> !Objects.equals(obj.getIsFree(), YesOrNoEnum.YES.getCode())).map(item -> {
                                BigDecimal price = item.getPrice();
                                if (item.isSpecialOfferFlag()) {
                                    // 该样本检验项目 参与了特价项目 结算金额为折后价格
                                    price = ObjectUtils.defaultIfNull(item.getDiscountPrice(), BigDecimal.ZERO);
                                    return price
                                            .multiply(
                                                    BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                            .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                                }
                                return price
                                        .multiply(
                                                BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                        .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).multiply(keyDto.getDiscount())
                                        .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                            }).reduce(BigDecimal.ZERO, BigDecimal::add);

                    countSumAll = countSumAll + countSum;
                    payAmountBeforeSum = payAmountBeforeSum.add(feePriceSum);
                    payAmountAfterSum = payAmountAfterSum.add(payAmount);

                    ByPlatformStatisticsItemDto temp = new ByPlatformStatisticsItemDto();
                    temp.setSendDate(sendDate);
                    temp.setHspOrgId(hspOrganizationDto.getHspOrgId());
                    temp.setHspOrgName(hspOrganizationDto.getHspOrgName());
                    temp.setApplyTypeName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getApplyTypeName());
                    temp.setApplyTypeCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getApplyTypeCode());
                    temp.setFinanceGroupCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getFinanceGroupCode());
                    temp.setFinanceGroupName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getFinanceGroupName());
                    temp.setTestItemCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemCode());
                    temp.setTestItemName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemName());
                    temp.setCount(countSum);
                    temp.setFeePrice(keyDto.getPrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                    temp.setDiscount(new DecimalFormat("0.00%").format(keyDto.getDiscount()));
                    temp.setPayAmountBefore(feePriceSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                    temp.setPayAmountAfter(payAmount.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                    itemList.add(temp);
                }

            }

            // 排序
            List<ByPlatformStatisticsItemDto> sorted =
                    itemList.stream().sorted(Comparator.comparing(ByPlatformStatisticsItemDto::getApplyTypeCode)
                            .thenComparing(ByPlatformStatisticsItemDto::getTestItemCode)).collect(Collectors.toList());

            // 相应数据
            ByPlatformStatisticsResponseDto orgItem = new ByPlatformStatisticsResponseDto();

            countTotal = countTotal + countSumAll;
            payAmountBeforeTotal = payAmountBeforeTotal.add(payAmountBeforeSum);
            payAmountAfterTotal = payAmountAfterTotal.add(payAmountAfterSum);

            orgItem.setHspOrgId(hspOrganizationDto.getHspOrgId());
            orgItem.setCustomerName(customerName);
            orgItem.setStartDate(dateRange.getStartDate());
            orgItem.setEndDate(dateRange.getEndDate());
            orgItem.setCountSum(countSumAll);
            orgItem.setPayAmountBeforeSum(payAmountBeforeSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            orgItem.setPayAmountAfterSum(payAmountAfterSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            orgItem.setOutOrgStatisticsItems(sorted);

            orgList.add(orgItem);
        }

        if (CollectionUtils.isNotEmpty(orgList)) {
            result.setHspOrgId(orgList.get(0).getHspOrgId());
            result.setCustomerName(orgList.get(0).getCustomerName());
            result.setStartDate(orgList.get(0).getStartDate());
            result.setEndDate(orgList.get(0).getEndDate());
            result.setCountSum(countTotal);
            result.setPayAmountBeforeSum(payAmountBeforeTotal);
            result.setPayAmountAfterSum(payAmountAfterTotal);
        }

        return result;
    }

    @Override
    public ByPlatformStatisticsResponseDto getByPlatformStatisticsResponseDto(SampleEsQuery dto,
                                                                              CustomerNameTypeEnum customerNameType, HspOrganizationDto hspOrganizationDto,
                                                                              List<BaseSampleEsModelDto> baseSampleEsModelDtosAll) {

        // 时间
        DateRange dateRange = getDateRange(dto);
        // 拆分数据
        TestItemIncomeFilterDto filterDto = new TestItemIncomeFilterDto();

        // 查询 终止收费的检验项目信息 不一定是审核的
        SampleEsQuery stopStatusEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        stopStatusEsQuery.setIsAudit(null);
        stopStatusEsQuery.setStopStatus(Set.of(StopTestStatus.STOP_TEST_CHARGE.getCode()));
        List<BaseSampleEsModelDto> stopTestChargeEsModelDtos =
                elasticSearchSampleService.selectSamples(stopStatusEsQuery);

        // 查询 病理检验 已经一次分拣
        SampleEsQuery pathologyEsQuery = JSON.parseObject(JSON.toJSONString(dto), SampleEsQuery.class);
        pathologyEsQuery.setIsAudit(null);
        pathologyEsQuery.setItemTypes(Collections.singleton(ItemTypeEnum.PATHOLOGY.name()));
        // 1.0.6.6需求改为病理组的样本只要创建就纳入统计
        // pathologyEsQuery.setIsOnePick(YesOrNoEnum.YES.getCode());
        List<BaseSampleEsModelDto> pathologyEsModelDtos = elasticSearchSampleService.selectSamples(pathologyEsQuery);

        List<SampleTestItemDto> sampleTestItemDtoList = this.handleEsDataToSampleTestItemDto(baseSampleEsModelDtosAll,
                stopTestChargeEsModelDtos, pathologyEsModelDtos, filterDto, true);

        // 财务专业组 就诊类型 检验项目 标准收费 折扣率一样的 分组
        Map<ByPlatformStatisticsKeyDto,
                List<SampleTestItemDto>> groupingByKeyDto = sampleTestItemDtoList.stream()
                .collect(Collectors.groupingBy(obj -> new ByPlatformStatisticsKeyDto(obj.getFinanceGroupCode(),
                        obj.getApplyTypeCode(), obj.getTestItemId(), obj.getPrice(), obj.getDiscount())));
        List<ByPlatformStatisticsItemDto> itemList = Lists.newArrayListWithCapacity(groupingByKeyDto.size());

        // 数量合计、 合计金额合计、结算金额合计
        Integer countSumAll = NumberUtils.INTEGER_ZERO;
        BigDecimal payAmountBeforeSum = BigDecimal.ZERO;
        BigDecimal payAmountAfterSum = BigDecimal.ZERO;

        for (Map.Entry<ByPlatformStatisticsKeyDto, List<SampleTestItemDto>> groupingByKey : groupingByKeyDto
                .entrySet()) {
            ByPlatformStatisticsKeyDto keyDto = groupingByKey.getKey();
            List<SampleTestItemDto> summaryItemList = groupingByKey.getValue();

            int countSum =
                    summaryItemList.stream().map(obj -> ObjectUtils.defaultIfNull(obj.getCount(), NumberUtils.INTEGER_ONE))
                            .mapToInt(Integer::intValue).sum();
            BigDecimal feePriceSum = keyDto.getPrice().multiply(BigDecimal.valueOf(countSum));
            // 未免单 金额合计 用于结算金额
            BigDecimal payAmount =
                    summaryItemList.stream().filter(obj -> !Objects.equals(obj.getIsFree(), YesOrNoEnum.YES)).map(item -> {
                        BigDecimal price = item.getPrice();
                        if (item.isSpecialOfferFlag()) {
                            // 该样本检验项目 参与了特价项目 结算金额为折后价格
                            price = ObjectUtils.defaultIfNull(item.getDiscountPrice(), BigDecimal.ZERO);
                            return price
                                    .multiply(
                                            BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                    .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                        }
                        return price
                                .multiply(
                                        BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).multiply(keyDto.getDiscount())
                                .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                    }).reduce(BigDecimal.ZERO, BigDecimal::add);

            countSumAll = countSumAll + countSum;
            payAmountBeforeSum = payAmountBeforeSum.add(feePriceSum);
            payAmountAfterSum = payAmountAfterSum.add(payAmount);

            ByPlatformStatisticsItemDto temp = new ByPlatformStatisticsItemDto();
            temp.setHspOrgId(hspOrganizationDto.getHspOrgId());
            temp.setHspOrgName(hspOrganizationDto.getHspOrgName());
            temp.setApplyTypeName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getApplyTypeName());
            temp.setApplyTypeCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getApplyTypeCode());
            temp.setFinanceGroupCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getFinanceGroupCode());
            temp.setFinanceGroupName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getFinanceGroupName());
            temp.setTestItemCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemCode());
            temp.setTestItemName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemName());
            temp.setCount(countSum);
            temp.setFeePrice(keyDto.getPrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            temp.setDiscount(new DecimalFormat("0.00%").format(keyDto.getDiscount()));
            temp.setPayAmountBefore(feePriceSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            temp.setPayAmountAfter(payAmount.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            itemList.add(temp);

        }

        // 排序
        List<ByPlatformStatisticsItemDto> sorted =
                itemList.stream().sorted(Comparator.comparing(ByPlatformStatisticsItemDto::getApplyTypeCode)
                        .thenComparing(ByPlatformStatisticsItemDto::getTestItemCode)).collect(Collectors.toList());

        // 相应数据
        ByPlatformStatisticsResponseDto target = new ByPlatformStatisticsResponseDto();

        String customerName;
        if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME)) {
            // 展示开票名称
            customerName = hspOrganizationDto.getInvoice();
        } else {
            customerName = hspOrganizationDto.getHspOrgName();
        }

        target.setCustomerName(customerName);
        target.setStartDate(dateRange.getStartDate());
        target.setEndDate(dateRange.getEndDate());
        target.setCountSum(countSumAll);
        target.setPayAmountBeforeSum(payAmountBeforeSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
        target.setPayAmountAfterSum(payAmountAfterSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
        target.setItemList(sorted);

        return target;
    }

    @Override
    public List<SampleTestItemDto> handleEsDataToSampleTestItemDto(List<BaseSampleEsModelDto> baseSampleEsModelDtosAll,
                                                                   List<BaseSampleEsModelDto> stopTestChargeEsModelDtos, List<BaseSampleEsModelDto> pathologyEsModelDtos,
                                                                   TestItemIncomeFilterDto filterDto, boolean needFinanceGroup) {

        // 数据拆解 至样本检验项目 只要正常状态的(为避免重复查询终止收费的)
        List<SampleTestItemDto> sampleTestItemDtoList = getSampleTestItemDtos(baseSampleEsModelDtosAll).stream()
                .filter(obj -> Objects.equals(obj.getStopStatus(), StopTestStatus.NO_STOP_TEST.getCode()))
                .filter(obj -> !Objects.equals(obj.getItemType(), ItemTypeEnum.PATHOLOGY.name()))
                .collect(Collectors.toList());

        // 终止检验的只要终止收费的 加入计算数据 且非病理检验的
        sampleTestItemDtoList.addAll(getSampleTestItemDtos(stopTestChargeEsModelDtos).stream()
                .filter(obj -> Objects.equals(obj.getStopStatus(), StopTestStatus.STOP_TEST_CHARGE.getCode()))
                .filter(obj -> !Objects.equals(obj.getItemType(), ItemTypeEnum.PATHOLOGY.name()))
                .collect(Collectors.toList()));

        // 病理检验 加入计算数据 过虑掉 终止不收费
        sampleTestItemDtoList.addAll(getSampleTestItemDtos(pathologyEsModelDtos).stream()
                .filter(obj -> !Objects.equals(obj.getStopStatus(), StopTestStatus.STOP_TEST_FREE.getCode()))
                .collect(Collectors.toList()));

        // 根据 筛选条件 过滤数据
        List<SampleTestItemDto> targetList = filterByTestItemIncomeFilterDto(filterDto, sampleTestItemDtoList);

        // 需要 赋值财务专业组
        if (needFinanceGroup) {
            Set<Long> testItemIds =
                    targetList.stream().map(SampleTestItemDto::getTestItemId).collect(Collectors.toSet());
            Map<Long, TestItemDto> testItemByTestItemId = testItemService.selectByTestItemIds(testItemIds).stream()
                    .collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));

            targetList.forEach(item -> {
                TestItemDto testItemDto = testItemByTestItemId.get(item.getTestItemId());
                if (Objects.isNull(testItemDto)) {
                    return;
                }
                item.setFinanceGroupCode(testItemDto.getFinanceGroupCode());
                item.setFinanceGroupName(testItemDto.getFinanceGroupName());
            });
        }

        // 所有送检机构
        Set<Long> hspOrgIdList = sampleTestItemDtoList.stream().map(SampleTestItemDto::getHspOrgId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        // 所有就诊类型
        Set<String> applyTypeList = sampleTestItemDtoList.stream().map(SampleTestItemDto::getApplyTypeCode)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 最小 样本检验项目 创建时间
        Date minSampleTestItemCreateDate = sampleTestItemDtoList.stream().map(SampleTestItemDto::getCreateDate)
                .filter(Objects::nonNull).min(Comparator.naturalOrder()).orElse(null);
        // 最大 样本检验项目 创建时间
        Date maxSampleTestItemCreateDate = sampleTestItemDtoList.stream().map(SampleTestItemDto::getCreateDate)
                .filter(Objects::nonNull).max(Comparator.naturalOrder()).orElse(null);

        // 每个样本 下单个检验项目 若使用特价项目 只会存在一个 key: SampleTestItemDto 唯一key value:特价项目
        Map<String, HspOrgSpecialOfferDto> hspOrgSpecialOfferDtoBySampleTestItem =
                getHspOrgSpecialOfferDtoBySampleTestItem(sampleTestItemDtoList, hspOrgIdList, applyTypeList,
                        minSampleTestItemCreateDate, maxSampleTestItemCreateDate);

        // 每个样本 下单个检验项目 若使用客户阶梯折扣只会存在一个 key: SampleTestItemDto 唯一key value:客户阶梯折扣
        Map<String, HspOrgPricingDto> hspOrgPricingBySampleTestItem =
                getHspOrgPricingBySampleTestItem(sampleTestItemDtoList, hspOrgSpecialOfferDtoBySampleTestItem, hspOrgIdList,
                        minSampleTestItemCreateDate, maxSampleTestItemCreateDate);

        // 所有送检机构
        Set<Long> hspOrgIdListAfterFilter = targetList.stream().map(SampleTestItemDto::getHspOrgId)
                .filter(Objects::nonNull).collect(Collectors.toSet());

        // 所有就诊类型
        Set<String> applyTypeListAfterFilter = targetList.stream().map(SampleTestItemDto::getApplyTypeCode)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 补充 全部 类型
        applyTypeListAfterFilter.add(SendTypeEnum.SEND_TYPE_ALL.getCode());

        // 最小 样本检验项目 创建时间
        Date minSampleTestItemCreateDateAfterFilter = targetList.stream().map(SampleTestItemDto::getCreateDate)
                .filter(Objects::nonNull).min(Comparator.naturalOrder()).orElse(null);
        // 最大 样本检验项目 创建时间
        Date maxSampleTestItemCreateDateAfterFilter = targetList.stream().map(SampleTestItemDto::getCreateDate)
                .filter(Objects::nonNull).max(Comparator.naturalOrder()).orElse(null);

        // 客户折扣维护
        List<HspOrgDiscountDto> hspOrgDiscountDtos = Lists.newArrayList();
        if (Objects.nonNull(minSampleTestItemCreateDateAfterFilter)
                && Objects.nonNull(maxSampleTestItemCreateDateAfterFilter)
                && CollectionUtils.isNotEmpty(hspOrgIdListAfterFilter)
                && CollectionUtils.isNotEmpty(applyTypeListAfterFilter)) {
            hspOrgDiscountDtos =
                    hspOrgDiscountService.selectByHspOrgIdsAndDateRange(hspOrgIdListAfterFilter, applyTypeListAfterFilter,
                            minSampleTestItemCreateDateAfterFilter, maxSampleTestItemCreateDateAfterFilter);
        }
        Map<Long, List<HspOrgDiscountDto>> hspOrgDiscountGroupingByHspOrgId =
                hspOrgDiscountDtos.stream().collect(Collectors.groupingBy(HspOrgDiscountDto::getHspOrgId));

        // 项目基准包
        List<ItemPriceBasePackageItemDto> itemPriceBasePackageDtos = Lists.newArrayList();
        if (Objects.nonNull(minSampleTestItemCreateDateAfterFilter)
                && Objects.nonNull(maxSampleTestItemCreateDateAfterFilter)) {
            itemPriceBasePackageDtos = itemPriceBasePackageItemDtoListFromTbObjDto(itemPriceBasePackageService
                    .selectByDateRange(minSampleTestItemCreateDateAfterFilter, maxSampleTestItemCreateDateAfterFilter));
            // 所有基准包ids
            Set<Long> packageIds = itemPriceBasePackageDtos.stream().map(ItemPriceBasePackageDto::getPackageId)
                    .collect(Collectors.toSet());
            Map<Long,
                    Set<Long>> testItemIdsByPackageIds = itemPriceBasePackageDetailService.selectByPackageIds(packageIds)
                    .stream().collect(Collectors.groupingBy(ItemPriceBasePackageDetailDto::getPackageId,
                            Collectors.mapping(ItemPriceBasePackageDetailDto::getTestItemId, Collectors.toSet())));
            itemPriceBasePackageDtos.forEach(item -> item
                    .setTestItemIds(testItemIdsByPackageIds.getOrDefault(item.getPackageId(), Collections.emptySet())));
        }

        for (SampleTestItemDto sampleTestItemDto : targetList) {

            // 先找 对应特价项目
            HspOrgSpecialOfferDto specialOfferDto =
                    hspOrgSpecialOfferDtoBySampleTestItem.get(sampleTestItemDto.uniqueKey());
            if (Objects.nonNull(specialOfferDto)) {
                // 若存在 特价项目 则此检验项目 折扣率确定 为特价项目上 维护的折扣率
                sampleTestItemDto.setDiscount(specialOfferDto.getDiscount());
                // 计算价格 使用维护的折前价格 并保留 一个标记、且留下折后价格
                sampleTestItemDto.setPrice(specialOfferDto.getFeePrice());
                sampleTestItemDto.setSpecialOfferFlag(true);
                sampleTestItemDto.setDiscountPrice(specialOfferDto.getDiscountPrice());
                continue;
            }
            // 特价项目 未确定折扣率 通过客户阶梯折扣确定 折扣率
            HspOrgPricingDto hspOrgPricingDto = hspOrgPricingBySampleTestItem.get(sampleTestItemDto.uniqueKey());
            if (Objects.nonNull(hspOrgPricingDto)) {
                sampleTestItemDto.setDiscount(hspOrgPricingDto.getDiscount());
                continue;
            }

            // 特价项目 与 客户阶梯折扣未确定折扣率 根据 项目价格基准包 、客户折扣 获取 折扣率

            HspOrgDiscountDto hspOrgDiscountDto =
                    getHspOrgDiscountDto(sampleTestItemDto, hspOrgDiscountGroupingByHspOrgId);

            if (Objects.isNull(hspOrgDiscountDto)) {
                // 不存在 有效的客户折扣 折扣为1 取原价
                sampleTestItemDto.setDiscount(BigDecimal.ONE);
                continue;
            }

            // 检验项目 对应价格基准包
            ItemPriceBasePackageDto priceBasePackageDto = itemPriceBasePackageDtos.stream()
                    // 与客户折扣上基准包一致
                    .filter(obj -> Objects.equals(obj.getPackageId(), hspOrgDiscountDto.getPackageId()))
                    // 包含此检验项目的 价格基准包
                    .filter(obj -> obj.getTestItemIds().contains(sampleTestItemDto.getTestItemId()))
                    // 检验项目创建时间 在 项目价格基准包生效时间范围内的数据
                    .filter(item -> sampleTestItemDto.getCreateDate().compareTo(item.getStartDate()) >= 0
                            && sampleTestItemDto.getCreateDate().compareTo(item.getEndDate()) <= 0)
                    // 若存在应当只存在一条
                    .findFirst().orElse(null);

            if (Objects.isNull(priceBasePackageDto)) {
                // 不存在 对应价格基准包 折扣为1 取原价
                sampleTestItemDto.setDiscount(BigDecimal.ONE);
                continue;
            }

            // 存在对应有效的客户折扣 取其折扣率
            sampleTestItemDto.setDiscount(hspOrgDiscountDto.getDiscount());
        }

        return targetList;

    }

    /**
     * 获取样本检验项目 对应 客户折扣维护
     *
     * @param sampleTestItemDto                样本检验项目
     * @param hspOrgDiscountGroupingByHspOrgId 客户折扣 数据
     */
    private HspOrgDiscountDto getHspOrgDiscountDto(SampleTestItemDto sampleTestItemDto,
                                                   Map<Long, List<HspOrgDiscountDto>> hspOrgDiscountGroupingByHspOrgId) {
        // 基准包 对应客户折扣 从诊类型相同的客户折扣中寻找
        HspOrgDiscountDto hspOrgDiscountDto = hspOrgDiscountGroupingByHspOrgId
                .getOrDefault(sampleTestItemDto.getHspOrgId(), Collections.emptyList()).stream()
                // 与客户折扣就诊类型相同
                .filter(obj -> Objects.equals(obj.getSendTypeCode(), sampleTestItemDto.getApplyTypeCode()))
                // 检验项目创建时间 在 客户折扣生效时间范围内的数据
                .filter(item -> sampleTestItemDto.getCreateDate().compareTo(item.getStartDate()) >= 0
                        && sampleTestItemDto.getCreateDate().compareTo(item.getEndDate()) <= 0)
                // 若存在应当只存在一条
                .findFirst().orElse(null);

        if (Objects.isNull(hspOrgDiscountDto)) {
            // 就诊类型 找不到 通过全部 进行 搂底

            hspOrgDiscountDto = hspOrgDiscountGroupingByHspOrgId
                    .getOrDefault(sampleTestItemDto.getHspOrgId(), Collections.emptyList()).stream()
                    // 与客户折扣就诊类型相同
                    .filter(obj -> Objects.equals(obj.getSendTypeCode(), SendTypeEnum.SEND_TYPE_ALL.getCode()))
                    // 检验项目创建时间 在 客户折扣生效时间范围内的数据
                    .filter(item -> sampleTestItemDto.getCreateDate().compareTo(item.getStartDate()) >= 0
                            && sampleTestItemDto.getCreateDate().compareTo(item.getEndDate()) <= 0)
                    // 若存在应当只存在一条
                    .findFirst().orElse(null);

        }
        return hspOrgDiscountDto;
    }

    /**
     * 每个样本 下单个检验项目 若使用客户阶梯折扣只会存在一个 key: SampleTestItemDto 唯一key value:客户阶梯折扣
     *
     * @param sampleTestItemDtoList                 样本检验项目
     * @param hspOrgSpecialOfferDtoBySampleTestItem key: 送检机构 value：送检机构特价项目维护
     * @param hspOrgIdList                          所有送检机构
     * @param minSampleTestItemCreateDate           最小 样本检验项目 创建时间
     * @param maxSampleTestItemCreateDate           最大 样本检验项目 创建时间
     * @return key: SampleTestItemDto 唯一key value:客户阶梯折扣
     */
    private Map<String, HspOrgPricingDto> getHspOrgPricingBySampleTestItem(
            List<SampleTestItemDto> sampleTestItemDtoList,
            Map<String, HspOrgSpecialOfferDto> hspOrgSpecialOfferDtoBySampleTestItem, Set<Long> hspOrgIdList,
            Date minSampleTestItemCreateDate, Date maxSampleTestItemCreateDate) {

        // 需要用到的客户阶梯折扣
        List<HspOrgPricingDto> hspOrgPricingDtos = Lists.newArrayList();
        if (Objects.nonNull(minSampleTestItemCreateDate) && Objects.nonNull(maxSampleTestItemCreateDate)
                && CollectionUtils.isNotEmpty(hspOrgIdList)) {
            hspOrgPricingDtos = hspOrgPricingService.selectByHspOrgIdsAndDateRange(hspOrgIdList,
                    minSampleTestItemCreateDate, maxSampleTestItemCreateDate);
        }
        Map<Long, List<HspOrgPricingDto>> hspOrgPricingGroupingByHspOrgId =
                hspOrgPricingDtos.stream().collect(Collectors.groupingBy(HspOrgPricingDto::getHspOrgId));

        // 每个样本 下单个检验项目 若使用客户阶梯折扣只会存在一个 key: SampleTestItemDto 唯一key value:客户阶梯折扣
        Map<String, HspOrgPricingDto> hspOrgPricingBySampleTestItem = new HashMap<>();
        // 每个 样本检验项目 使用的特检项目
        for (Map.Entry<Long, List<SampleTestItemDto>> entry : sampleTestItemDtoList.stream()
                .collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId)).entrySet()) {
            // 送检机构
            Long hspOrgId = entry.getKey();
            // 此送检机构下 样本检验项目
            List<SampleTestItemDto> sampleTestItemDtoListByHspOrgId = entry.getValue();
            // 此送检机构下 所有涉及的客户阶梯折扣
            List<HspOrgPricingDto> hspOrgPricingDtoList =
                    hspOrgPricingGroupingByHspOrgId.getOrDefault(hspOrgId, Collections.emptyList());
            // 查看 哪些检验项目 用于此阶梯折扣
            for (HspOrgPricingDto hspOrgPricingDto : hspOrgPricingDtoList) {
                // 在此客户阶梯折扣生效时间 范围内 所有样本检验项目
                List<SampleTestItemDto> filterSampleTestItemDtos = sampleTestItemDtoListByHspOrgId.stream()
                        .filter(item -> item.getCreateDate().compareTo(hspOrgPricingDto.getStartDate()) >= 0
                                && item.getCreateDate().compareTo(hspOrgPricingDto.getEndDate()) <= 0)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterSampleTestItemDtos)) {
                    continue;
                }
                // 这个生效时间范围 内样本检验项目 金额汇总
                BigDecimal priceSum = BigDecimal.ZERO;
                for (SampleTestItemDto filterSampleTestItemDto : filterSampleTestItemDtos) {
                    if (Objects.equals(filterSampleTestItemDto.getIsFree(), YesOrNoEnum.YES.getCode())) {
                        // 免单的不算入 总额
                        continue;
                    }
                    BigDecimal price = ObjectUtils.defaultIfNull(filterSampleTestItemDto.getPrice(), BigDecimal.ZERO);
                    BigDecimal count = BigDecimal.valueOf(
                            ObjectUtils.defaultIfNull(filterSampleTestItemDto.getCount(), NumberUtils.INTEGER_ONE));

                    // 获取 对应特价项目
                    HspOrgSpecialOfferDto specialOfferDto =
                            hspOrgSpecialOfferDtoBySampleTestItem.get(filterSampleTestItemDto.uniqueKey());

                    if (Objects.isNull(specialOfferDto)) {
                        // 不存在特价项目 按照原价*数量 算入总额
                        priceSum = priceSum.add(price.multiply(count));
                        continue;
                    }
                    // 存在 特价项目 且参与阶梯折扣 将打折 后金额 算入总额
                    if (Objects.equals(specialOfferDto.getIsTieredPricing(), YesOrNoEnum.YES.getCode())) {
                        priceSum = priceSum.add(specialOfferDto.getDiscountPrice().multiply(count));
                    }
                    // 不参与阶梯折扣 不算入总额
                }
                if (priceSum.compareTo(hspOrgPricingDto.getBeforeMinPrice()) >= 0
                        && priceSum.compareTo(hspOrgPricingDto.getBeforeMaxPrice()) <= 0) {
                    // 如果此 汇总金额 在此上下限范围内 则这些 样本检验项目 对应客户阶梯折扣可确定
                    filterSampleTestItemDtos.forEach(item -> {
                        if (hspOrgPricingBySampleTestItem.containsKey(item.uniqueKey())) {
                            HspOrgPricingDto temp = hspOrgPricingBySampleTestItem.get(item.uniqueKey());
                            // 该样本检验项目 已有对应客户阶梯折扣 取最大的 覆盖
                            if (hspOrgPricingDto.getDiscount().compareTo(temp.getDiscount()) > 0) {
                                hspOrgPricingBySampleTestItem.put(item.uniqueKey(), hspOrgPricingDto);
                            }

                        } else {
                            hspOrgPricingBySampleTestItem.put(item.uniqueKey(), hspOrgPricingDto);
                        }
                    });
                }
            }

        }
        return hspOrgPricingBySampleTestItem;
    }

    /**
     * 每个样本 下单个检验项目 若使用特价项目 只会存在一个 key: SampleTestItemDto 唯一key value:特价项目
     *
     * @param sampleTestItemDtoList       样本检验项目
     * @param hspOrgIdList                所有送检机构
     * @param applyTypeList               所有就诊类型
     * @param minSampleTestItemCreateDate 最小 样本检验项目 创建时间
     * @param maxSampleTestItemCreateDate 最大 样本检验项目 创建时间
     * @return key: SampleTestItemDto 唯一key value:特价项目
     */

    private Map<String, HspOrgSpecialOfferDto> getHspOrgSpecialOfferDtoBySampleTestItem(
            List<SampleTestItemDto> sampleTestItemDtoList, Set<Long> hspOrgIdList, Set<String> applyTypeList,
            Date minSampleTestItemCreateDate, Date maxSampleTestItemCreateDate) {

        // 补充 全部 类型
        Set<String> sendTypeList = new HashSet<>(applyTypeList);
        sendTypeList.add(SendTypeEnum.SEND_TYPE_ALL.getCode());

        // 送检机构特价项目维护
        List<HspOrgSpecialOfferDto> hspOrgSpecialOfferDtos = Lists.newArrayList();
        if (Objects.nonNull(minSampleTestItemCreateDate) && Objects.nonNull(maxSampleTestItemCreateDate)
                && CollectionUtils.isNotEmpty(hspOrgIdList)) {
            hspOrgSpecialOfferDtos = hspOrgSpecialOfferService.selectByByHspOrgIdsAndDateRangeAndAndApplyTypes(
                    hspOrgIdList, minSampleTestItemCreateDate, maxSampleTestItemCreateDate, sendTypeList);
        }
        Map<Long, List<HspOrgSpecialOfferDto>> hspOrgSpecialOfferGroupingByHspOrgId =
                hspOrgSpecialOfferDtos.stream().collect(Collectors.groupingBy(HspOrgSpecialOfferDto::getHspOrgId));

        // 每个样本 下单个检验项目 若使用特价项目 只会存在一个 key: SampleTestItemDto 唯一key value:特价项目
        Map<String, HspOrgSpecialOfferDto> hspOrgSpecialOfferDtoBySampleTestItem = new HashMap<>();

        for (Map.Entry<Long, List<SampleTestItemDto>> entry : sampleTestItemDtoList.stream()
                .collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId)).entrySet()) {
            // 送检机构
            Long hspOrgId = entry.getKey();
            // 此送检机构下 样本检验项目
            List<SampleTestItemDto> sampleTestItemDtoListByHspOrgId = entry.getValue();
            // 此送检机构下 特价项目 以就诊类型分组
            Map<String, List<HspOrgSpecialOfferDto>> hspOrgSpecialOfferDtoByApplyTypeCode =
                    hspOrgSpecialOfferGroupingByHspOrgId.getOrDefault(hspOrgId, Collections.emptyList()).stream()
                            .collect(Collectors.groupingBy(HspOrgSpecialOfferDto::getSendTypeCode));
            for (SampleTestItemDto sampleTestItemDto : sampleTestItemDtoListByHspOrgId) {
                // 找与此样本检验项目 就诊类型一致 特价项目
                List<HspOrgSpecialOfferDto> filterByApplyTypeCode =
                        hspOrgSpecialOfferDtoByApplyTypeCode.get(sampleTestItemDto.getApplyTypeCode());
                if (CollectionUtils.isNotEmpty(filterByApplyTypeCode)) {
                    // 若存在 检验项目一致的
                    filterByApplyTypeCode = filterByApplyTypeCode.stream()
                            .filter(obj -> Objects.equals(obj.getTestItemId(), sampleTestItemDto.getTestItemId()))
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(filterByApplyTypeCode)) {
                    // 通过 全部 类型进行 搂底
                    filterByApplyTypeCode =
                            hspOrgSpecialOfferDtoByApplyTypeCode.get(SendTypeEnum.SEND_TYPE_ALL.getCode());
                }
                if (CollectionUtils.isEmpty(filterByApplyTypeCode)) {
                    continue;
                }
                // 获取 对应特价项目
                HspOrgSpecialOfferDto specialOfferDto = null;
                // 这些 特价项目 与此样本检验项目一致 切生效时间包含 创建时间的
                specialOfferDto = filterByApplyTypeCode.stream()
                        .filter(obj -> Objects.equals(obj.getTestItemId(), sampleTestItemDto.getTestItemId()))
                        .filter(item -> sampleTestItemDto.getCreateDate().compareTo(item.getStartDate()) >= 0
                                && sampleTestItemDto.getCreateDate().compareTo(item.getEndDate()) <= 0)
                        .findFirst().orElse(null);
                if (Objects.isNull(specialOfferDto)) {
                    continue;
                }
                // 样本检验项目 使用的 特价项目
                hspOrgSpecialOfferDtoBySampleTestItem.put(sampleTestItemDto.uniqueKey(), specialOfferDto);
            }

        }
        return hspOrgSpecialOfferDtoBySampleTestItem;
    }

    /**
     * 数据拆解 至样本检验项目
     */
    private List<SampleTestItemDto> getSampleTestItemDtos(List<BaseSampleEsModelDto> baseSampleEsModelDtosAll) {
        // 拆解 到检验项目
        List<SampleTestItemDto> sampleTestItemDtoList = Lists.newArrayList();
        for (BaseSampleEsModelDto sampleEsModelDto : baseSampleEsModelDtosAll) {
            List<BaseSampleEsModelDto.TestItem> testItems = sampleEsModelDto.getTestItems();
            if (Objects.isNull(testItems)) {
                continue;
            }
            for (BaseSampleEsModelDto.TestItem testItem : testItems) {
                SampleTestItemDto temp = new SampleTestItemDto();
                temp.setOutBarcode(sampleEsModelDto.getOutBarcode());
                temp.setPatientName(sampleEsModelDto.getPatientName());
                temp.setPatientSex(sampleEsModelDto.getPatientSex());
                temp.setPatientAge(sampleEsModelDto.getPatientAge());
                temp.setPatientSubage(sampleEsModelDto.getPatientSubage());
                temp.setPatientSubageUnit(sampleEsModelDto.getPatientSubageUnit());
                temp.setHspOrgName(sampleEsModelDto.getHspOrgName());
                temp.setHspOrgId(sampleEsModelDto.getHspOrgId());
                temp.setApplyId(sampleEsModelDto.getApplyId());
                temp.setApplyDate(sampleEsModelDto.getApplyDate());
                temp.setSignDate(sampleEsModelDto.getSignDate());
                temp.setFinalCheckDate(sampleEsModelDto.getFinalCheckDate());
                temp.setPatientVisitCard(sampleEsModelDto.getPatientVisitCard());
                temp.setApplySampleId(sampleEsModelDto.getApplySampleId());
                temp.setItemType(sampleEsModelDto.getItemType());
                temp.setApplyTypeCode(sampleEsModelDto.getApplyTypeCode());
                temp.setApplyTypeName(sampleEsModelDto.getApplyTypeName());
                temp.setDept(sampleEsModelDto.getDept());
                temp.setSendDoctorName(sampleEsModelDto.getSendDoctorName());
                temp.setTestItemId(testItem.getTestItemId());
                temp.setTestItemCode(testItem.getTestItemCode());
                temp.setTestItemName(testItem.getTestItemName());
                temp.setCount(testItem.getCount());
                temp.setPrice(ObjectUtils.defaultIfNull(testItem.getActualFeePrice(), BigDecimal.ZERO));
                temp.setIsFree(testItem.getIsFree());
                temp.setCreateDate(testItem.getCreateDate());
                temp.setStopStatus(
                        ObjectUtils.defaultIfNull(testItem.getStopStatus(), StopTestStatus.NO_STOP_TEST.getCode()));

                if (sampleEsModelDto instanceof OutsourcingInspectionDto) {
                    OutsourcingInspectionDto outSample = (OutsourcingInspectionDto) sampleEsModelDto;
                    Long exportOrgId = testItem.getExportOrgId();
                    String exportOrgName = testItem.getExportOrgName();
                    temp.setExportHspOrgId(ObjectUtils.defaultIfNull(outSample.getExportOrgId(), exportOrgId));
                    temp.setExportHspOrgName(ObjectUtils.defaultIfNull(outSample.getExportOrgName(), exportOrgName));
                }
                temp.setApplySampleItemId(testItem.getApplySampleItemId());

                sampleTestItemDtoList.add(temp);
            }

        }
        return sampleTestItemDtoList;
    }

    /**
     * 根据传入条件过滤数据
     */
    private List<SampleTestItemDto> filterByTestItemIncomeFilterDto(TestItemIncomeFilterDto filterDto,
                                                                    List<SampleTestItemDto> sampleTestItemDtoList) {
        return sampleTestItemDtoList.stream()
                // 根据 就诊类型 过滤数据
                .filter(obj -> {
                    if (StringUtils.isBlank(filterDto.getApplyType()) && CollectionUtils.isEmpty(filterDto.getApplyTypes())) {
                        // 没有传就诊类型
                        return Boolean.TRUE;
                    }
                    if (CollectionUtils.isNotEmpty(filterDto.getApplyTypes())) {
                        return filterDto.getApplyTypes().contains(obj.getApplyTypeCode());
                    }
                    return Objects.equals(obj.getApplyTypeCode(), filterDto.getApplyType());
                })
                // 根据检验项目 过滤数据
                .filter(obj -> {
                    if (CollectionUtils.isEmpty(filterDto.getTestItemIds())) {
                        // 没有传检验项目
                        return Boolean.TRUE;
                    }
                    // 样本下检验项目 存在 在筛选条件内就允许
                    return filterDto.getTestItemIds().contains(obj.getTestItemId());
                })
                // 根据 项目类型 (目前只有 所有 和微生物 两种)过滤数据
                .filter(obj -> {
                    if (StringUtils.isBlank(filterDto.getItemTypeCode())) {
                        // 没有传项目类型
                        return Boolean.TRUE;
                    }
                    return Objects.equals(obj.getItemType(), filterDto.getItemTypeCode());
                }).// 根据是否免单 过滤
                        filter(obj -> {
                    if (Objects.isNull(filterDto.getIsFree())) {
                        // 没有是否免单
                        return Boolean.TRUE;
                    }
                    return Objects.equals(obj.getIsFree(), filterDto.getIsFree());
                }).collect(Collectors.toList());
    }

    public static DateRange getDateRange(SampleEsQuery dto) {
        DateRange dateRange = new DateRange();
        // 时间开始结束
        String startDate = StringUtils.EMPTY;
        String endDate = StringUtils.EMPTY;
        if (Objects.nonNull(dto.getStartFinalCheckOrCreateDate())
                && Objects.nonNull(dto.getEndFinalCheckOrCreateDate())) {
            startDate = DateUtil.formatDate(dto.getStartFinalCheckOrCreateDate());
            endDate = DateUtil.formatDate(dto.getEndFinalCheckOrCreateDate());
        }

        if (StringUtils.isAnyBlank(startDate, endDate)) {
            if (Objects.nonNull(dto.getStartCreateDate()) && Objects.nonNull(dto.getEndCreateDate())) {
                startDate = DateUtil.formatDate(dto.getStartCreateDate());
                endDate = DateUtil.formatDate(dto.getEndCreateDate());
            }
        }

        dateRange.setStartDate(startDate);
        dateRange.setEndDate(endDate);
        return dateRange;
    }

    /**
     * 根据是否免单 过滤数据
     */
    private List<BaseSampleEsModelDto> filterByIsFree(YesOrNoEnum isFreeFlag,
                                                      List<BaseSampleEsModelDto> baseSampleEsModelDtos) {
        if (Objects.isNull(isFreeFlag)) {
            return baseSampleEsModelDtos;
        }
        for (BaseSampleEsModelDto baseSampleEsModelDto : baseSampleEsModelDtos) {
            List<BaseSampleEsModelDto.TestItem> testItems = baseSampleEsModelDto.getTestItems();
            List<BaseSampleEsModelDto.TestItem> filter;
            if (Objects.equals(isFreeFlag, YesOrNoEnum.YES)) {
                // 取免单数据
                filter =
                        testItems.stream().filter(obj -> Objects.equals(obj.getIsFree(), 1)).collect(Collectors.toList());
            } else {
                // 取非免单数据
                filter =
                        testItems.stream().filter(obj -> Objects.equals(obj.getIsFree(), 0)).collect(Collectors.toList());
            }
            baseSampleEsModelDto.setTestItems(filter);
        }
        return baseSampleEsModelDtos.stream().filter(obj -> CollectionUtils.isNotEmpty(obj.getTestItems()))
                .collect(Collectors.toList());
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DateRange {
        /**
         * 开始时间
         */
        private String startDate;

        /**
         * 结束时间
         */
        private String endDate;

    }

    public List<ItemPriceBasePackageItemDto>
    itemPriceBasePackageItemDtoListFromTbObjDto(List<ItemPriceBasePackageDto> list) {
        return JSON.parseArray(JSON.toJSONString(list), ItemPriceBasePackageItemDto.class);
    }

}
