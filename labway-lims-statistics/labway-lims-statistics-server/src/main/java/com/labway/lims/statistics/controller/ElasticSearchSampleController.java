package com.labway.lims.statistics.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.api.client.ReportService;
import com.labway.lims.statistics.config.BusinessConfig;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <p>
 * ElasticSearchSampleController
 * ES搜索 控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023/10/23 16:43
 */
@Slf4j
@RestController
@RequestMapping("/es")
@RefreshScope
public class ElasticSearchSampleController {

    @Value("${lj-org-id:}")
    private Long orgId;
    /**
     * 这个配置需要分批合并的送检机构
     */
    @Value("${merge.hspOrgCodes:}")
    private List<String> mergeHspOrgCodes = new ArrayList<>();

    @Resource
    private BusinessConfig businessConfig;

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @DubboReference
    private SystemParamService systemParamService;
    @Resource
    private ReportService reportService;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private EnvDetector envDetector;

    @PostMapping("/selectSamples")
    public String selectSamples(@RequestBody SampleEsQuery queryDto) {

        if (Objects.isNull(queryDto.getStartFinalCheckDate()) && Objects.isNull(queryDto.getEndFinalCheckDate())
                && CollectionUtils.isEmpty(queryDto.getBarcodes()) && CollectionUtils.isEmpty(queryDto.getOutBarcodes())
                && CollectionUtils.isEmpty(queryDto.getHspOrgCodes()) && Objects.isNull(queryDto.getIsOutsourcing())) {
            log.error("查询参数范围过大 [{}]", JSON.toJSONString(queryDto));
            return JSON.toJSONString(Map.of("data", Lists.newArrayList()));
        }

        SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.pageSize(Integer.MAX_VALUE)
                .pageNo(1)
                .isAudit(ObjectUtils.defaultIfNull(queryDto.getIsAudit(), YesOrNoEnum.YES.getCode()))
                .startFinalCheckDate(queryDto.getStartFinalCheckDate())
                .endFinalCheckDate(queryDto.getEndFinalCheckDate())
                .hspOrgCodes(queryDto.getHspOrgCodes())
                .barcodes(queryDto.getBarcodes())
                .outBarcodes(queryDto.getOutBarcodes())
                .isOutsourcing(queryDto.getIsOutsourcing())
                .notEmptyFields(Set.of("outBarcode"));

        log.info("查询样本结果参数 {}", JSON.toJSONString(builder.build()));

        return JSON.toJSONString(Map.of("data", elasticSearchSampleService.selectSamples(builder.build())));
    }

    @PostMapping("/selectSamplesWithDelay")
    public String selectSamplesWithDelay(@RequestBody SampleEsQuery queryDto) {

        if (Objects.isNull(queryDto.getStartFinalCheckDate()) && Objects.isNull(queryDto.getEndFinalCheckDate())
                && CollectionUtils.isEmpty(queryDto.getBarcodes()) && CollectionUtils.isEmpty(queryDto.getOutBarcodes())
                && CollectionUtils.isEmpty(queryDto.getHspOrgCodes()) && Objects.isNull(queryDto.getIsOutsourcing())) {
            log.error("查询参数范围过大 [{}]", JSON.toJSONString(queryDto));
            return JSON.toJSONString(Map.of("data", Lists.newArrayList()));
        }

        SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.pageSize(Integer.MAX_VALUE)
                .pageNo(1)
                .isAudit(ObjectUtils.defaultIfNull(queryDto.getIsAudit(), YesOrNoEnum.YES.getCode()))
                .startFinalCheckDate(queryDto.getStartFinalCheckDate())
                .endFinalCheckDate(queryDto.getEndFinalCheckDate())
                .hspOrgCodes(queryDto.getHspOrgCodes())
                .barcodes(queryDto.getBarcodes())
                .outBarcodes(queryDto.getOutBarcodes())
                .isOutsourcing(queryDto.getIsOutsourcing())
                .notEmptyFields(Set.of("outBarcode"));

        log.info("查询样本结果参数 {}", JSON.toJSONString(builder.build()));

        return JSON.toJSONString(Map.of("data", selectBaseSampleByEsQuery(builder.build(), elasticSearchSampleService.selectSamples(queryDto))));
    }


    @PostMapping("/selectSamplesMerge")
    public String selectSamplesMerge(@RequestBody SampleEsQuery queryDto) {

        if (Objects.isNull(queryDto.getStartFinalCheckDate()) && Objects.isNull(queryDto.getEndFinalCheckDate())
                && CollectionUtils.isEmpty(queryDto.getBarcodes()) && CollectionUtils.isEmpty(queryDto.getOutBarcodes())
                && CollectionUtils.isEmpty(queryDto.getHspOrgCodes()) && Objects.isNull(queryDto.getIsOutsourcing())) {
            log.error("查询参数范围过大 [{}]", JSON.toJSONString(queryDto));
            return JSON.toJSONString(Map.of("data", Lists.newArrayList()));
        }

        SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.pageSize(Integer.MAX_VALUE)
                .pageNo(1)
                .isAudit(ObjectUtils.defaultIfNull(queryDto.getIsAudit(), YesOrNoEnum.YES.getCode()))
                .startFinalCheckDate(queryDto.getStartFinalCheckDate())
                .endFinalCheckDate(queryDto.getEndFinalCheckDate())
                .hspOrgCodes(queryDto.getHspOrgCodes())
                .barcodes(queryDto.getBarcodes())
                .outBarcodes(queryDto.getOutBarcodes())
                .isOutsourcing(queryDto.getIsOutsourcing())
                .notEmptyFields(Set.of("outBarcode"));

        log.info("查询样本结果参数 {}", JSON.toJSONString(builder.build()));

        List<BaseSampleEsModelDto> baseSampleEsModelDtos = doMergeSplitBloodSamplesInBatches(builder.build());

        return JSON.toJSONString(Map.of("data", baseSampleEsModelDtos));
    }


    private List<BaseSampleEsModelDto> selectBaseSampleByEsQuery(SampleEsQuery queryDto, List<BaseSampleEsModelDto> baseSampleEsModelDtos) {
        if (CollectionUtils.isEmpty(queryDto.getHspOrgCodes())) {
            // 送检机构为空，说明是手工根据条码号同步的，则直接拉取报告，并且合并
            return doMergeSplitBloodSamples(baseSampleEsModelDtos, Set.of(), Map.of());
        }
        return doSelectSamplesWithDelay(queryDto, baseSampleEsModelDtos);
    }

    private List<BaseSampleEsModelDto> doSelectSamplesWithDelay(SampleEsQuery queryDto, List<BaseSampleEsModelDto> samples) {
        Date startFinalCheckDate = queryDto.getStartFinalCheckDate();
        Date endFinalCheckDate = queryDto.getEndFinalCheckDate();

        // 查询延迟配置
        final JSONObject hspOrgReportDelayConfig = systemParamService.selectAsJsonByParamName(
                SystemParamNameEnum.HSP_ORG_REPORT_DELAY_CONFIG.getCode(), orgId);
        // 没有配置延迟发送报告的，直接查询返回样本结果
        if (hspOrgReportDelayConfig.isEmpty()) {

            // 分血合并
            return doMergeSplitBloodSamples(samples, Set.of(), Map.of());
        }

        log.info("查询样本结果 机构延迟报告配置 {}", hspOrgReportDelayConfig);

        final List<BaseSampleEsModelDto> retSamples = new ArrayList<>();

        // 先查出来机构的配置
        final Set<String> hspOrgCodes = queryDto.getHspOrgCodes();

        // 未配置延迟时间
        Set<String> undelayCodes = hspOrgCodes.stream()
                .filter(hspOrgCode -> !hspOrgReportDelayConfig.containsKey(hspOrgCode))
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(undelayCodes)) {
            // 查询未配置延迟时间的机构的样本结果数据
            queryDto.setHspOrgCodes(undelayCodes);
            retSamples.addAll(elasticSearchSampleService.selectSamples(queryDto));
        }

        // 配置了延迟时间 key: 分钟 value: 机构代码列表
        Map<Long, Set<String>> delayHspOrgCodesMap = new HashMap<>();
        hspOrgCodes.stream().filter(hspOrgReportDelayConfig::containsKey).forEach(hspOrgCode -> {
            Long delayMinutes = hspOrgReportDelayConfig.getLong(hspOrgCode);
            delayHspOrgCodesMap.computeIfAbsent(delayMinutes, k -> new HashSet<>()).add(hspOrgCode);
        });
        // 配置了延迟时间，延迟时间一样的放在同一批查询
        delayHspOrgCodesMap.forEach((delayMinutes, hspOrgCodesSet) -> {
            if (CollectionUtils.isNotEmpty(hspOrgCodesSet)) {
                long delayMillis = TimeUnit.MINUTES.toMillis(delayMinutes);
                queryDto.setStartFinalCheckDate(new Date(startFinalCheckDate.getTime() - delayMillis));
                queryDto.setEndFinalCheckDate(new Date(endFinalCheckDate.getTime() - delayMillis));
                queryDto.setHspOrgCodes(hspOrgCodesSet);
                log.info("查询样本结果 机构 {} 配置了延迟时间 {}分钟 参数 {}", hspOrgCodesSet, delayMinutes, JSON.toJSONString(queryDto));
                retSamples.addAll(elasticSearchSampleService.selectSamples(queryDto));
            }
        });

        // 合并分血报告
        return doMergeSplitBloodSamples(retSamples, hspOrgCodes, delayHspOrgCodesMap);
    }

    private List<BaseSampleEsModelDto> doMergeSplitBloodSamples(
            List<BaseSampleEsModelDto> samples, Set<String> hspOrgCodes, Map<Long, Set<String>> delayHspOrgCodesMap) {
        if (CollectionUtils.isEmpty(hspOrgCodes)) {
            // 先查出来机构的配置
            hspOrgCodes = samples.stream().map(BaseSampleEsModelDto::getHspOrgCode).collect(Collectors.toSet());
        }
        final boolean isDongguan = envDetector.isDongguan() || envDetector.isTest() || envDetector.isDev();

        // 配置了分血合并（东莞 分条码合并回传）的机构
        Set<String> configMergeHspOrgCodes = isDongguan ?
                filterSplitBarcodeMergeResultCodes(hspOrgCodes) : filterConfigMergeCodes(hspOrgCodes);

        if (CollectionUtils.isEmpty(configMergeHspOrgCodes)) {
            return samples;
        }

        // 无需合并的样本
        List<BaseSampleEsModelDto> retSamples;
        // 需要合并的样本
        List<BaseSampleEsModelDto> splitSamples;

        // 东莞地区分条码 合并结果回传sss
        if (isDongguan) {
            // 东莞 签收分条码，暂时不会分血
            // 未配置合并回传
            retSamples = samples.stream().filter(sample -> {
                String hspOrgCode = sample.getHspOrgCode();
                return (BooleanUtils.isFalse(configMergeHspOrgCodes.contains(hspOrgCode)));
            }).collect(Collectors.toList());

            // 配置了合并回传的机构
            splitSamples = samples.stream().filter(sample -> {
                String hspOrgCode = sample.getHspOrgCode();
                return (BooleanUtils.isTrue(configMergeHspOrgCodes.contains(hspOrgCode)));
            }).collect(Collectors.toList());
        } else {
            // 不用分血合并的样本（未配置分血合并 或者 配置了分血合并，但是未分血的报告）
            retSamples = samples.stream().filter(sample -> {
                String hspOrgCode = sample.getHspOrgCode();
                return (BooleanUtils.isFalse(configMergeHspOrgCodes.contains(hspOrgCode)) || !Objects.equals(YesOrNoEnum.YES.getCode(), sample.getIsSplitBlood()));
            }).collect(Collectors.toList());

            // 送检机构配置了分血合并，且是分血的样本
            splitSamples = samples.stream().filter(sample -> {
                String hspOrgCode = sample.getHspOrgCode();
                return (BooleanUtils.isTrue(configMergeHspOrgCodes.contains(hspOrgCode)) && Objects.equals(YesOrNoEnum.YES.getCode(), sample.getIsSplitBlood()));
            }).collect(Collectors.toList());
        }

        // 合并分血样本
        final Set<String> mergedBarcodes = new HashSet<>();
        for (BaseSampleEsModelDto splitSample : splitSamples) {
            // 分血已经合并过了，其他的样本不用再处理了
            if (mergedBarcodes.contains(splitSample.getOutBarcode())) {
                continue;
            }
            final boolean forcedMerge = mergeHspOrgCodes.contains(splitSample.getHspOrgCode());
            // 分血报告结果合并
            boolean isMerged = mergeResultAndPdfReport(retSamples, splitSample, delayHspOrgCodesMap, forcedMerge);
            if (isMerged) {
                // 加入到已合并的set中
                mergedBarcodes.add(splitSample.getOutBarcode());
            }
        }

        return retSamples;
    }


    /**
     * 分血报告，分批回传（出几份合并几份回传）
     */
    private List<BaseSampleEsModelDto> doMergeSplitBloodSamplesInBatches(SampleEsQuery queryDto) {

        List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(queryDto);
        // 是分批合并回传机构 并且是分血样本  ==》  true
        final Map<Boolean, List<BaseSampleEsModelDto>> isSplitBloodMap = samples.stream()
                .collect(Collectors.groupingBy(e ->
                        mergeHspOrgCodes.contains(e.getHspOrgCode()) && Objects.equals(YesOrNoEnum.YES.getCode(), e.getIsSplitBlood())
                ));

        final List<BaseSampleEsModelDto> noSplitSamples = isSplitBloodMap.getOrDefault(Boolean.FALSE, new ArrayList<>());
        final List<BaseSampleEsModelDto> splitSamples = isSplitBloodMap.getOrDefault(Boolean.TRUE, new ArrayList<>());

        // 不是配置的机构（卫宁） 或者不分血则走原来的逻辑
        final List<BaseSampleEsModelDto> baseSampleEsModelDtos = CollectionUtils.isEmpty(noSplitSamples) ?
                noSplitSamples : selectBaseSampleByEsQuery(queryDto, noSplitSamples);

        if (CollectionUtils.isNotEmpty(splitSamples)) {
            // 已合并的样本
            Set<String> mergedBarcodes = new HashSet<>();
            for (BaseSampleEsModelDto splitSample : splitSamples) {
                // 分血已经合并过了，其他的样本不用再处理了
                if (mergedBarcodes.contains(splitSample.getOutBarcode())) {
                    continue;
                }
                // 分血报告结果合并
                boolean isMerged = mergeResultAndPdfReport(baseSampleEsModelDtos, splitSample, Map.of(), true);
                if (isMerged) {
                    // 加入到已合并的set中
                    mergedBarcodes.add(splitSample.getOutBarcode());
                }
            }
        }
        return baseSampleEsModelDtos;
    }

    /**
     * 合并报告单和结果
     *
     * @param retSamples          未分血的样本s
     * @param sampleEsModelDto    循环分血的样本中的一个
     * @param delayHspOrgCodesMap 延迟时间和机构
     * @param forcedMerge         是否强制合并
     * @return 是否合并成功
     */
    private boolean mergeResultAndPdfReport(List<BaseSampleEsModelDto> retSamples, BaseSampleEsModelDto sampleEsModelDto, Map<Long, Set<String>> delayHspOrgCodesMap, boolean forcedMerge) {
        if (isNotSupportMergeItemType(sampleEsModelDto.getItemType())) {
            retSamples.add(sampleEsModelDto);
            return false;
        }

        // 外部条码号
        String outBarcode = sampleEsModelDto.getOutBarcode();
        if (StringUtils.isBlank(outBarcode)) {
            return false;
        }

        // 查询改外部条码对应的所有样本
        SampleEsQuery queryDto = new SampleEsQuery();
        queryDto.setOutBarcodes(Set.of(outBarcode));
        List<BaseSampleEsModelDto> splitSamples = elasticSearchSampleService.selectSamples(queryDto);

        // 过滤非终止检验
        List<BaseSampleEsModelDto> samples = splitSamples.stream()
                .filter(e -> !Objects.equals(SampleStatusEnum.STOP_TEST.getCode(), e.getSampleStatus()))
                .collect(Collectors.toList());

        // 分血的样本 如果不是 常规检验，外送检验 则不能合并，因为结果的结构不一样
        if (samples.stream().anyMatch(e -> isNotSupportMergeItemType(e.getItemType()))) {
            retSamples.add(sampleEsModelDto);
            return false;
        }

        // 强制合并， 过滤掉未审核的样本
        if (forcedMerge) {
            samples = samples.stream().filter(e -> Objects.equals(SampleStatusEnum.AUDIT.getCode(), e.getSampleStatus())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(samples)) {
            return false;
        }
        // 如果所有分血样本都审核了，并且审核报告的时间过了延迟时间 => 合并回传
        if (samples.stream().allMatch(e ->
                Objects.equals(SampleStatusEnum.AUDIT.getCode(), e.getSampleStatus()) && checkReportDelayTime(e, delayHspOrgCodesMap))) {
            // 拿原条码，  拿不到原条码的话拿最新审核的
            BaseSampleEsModelDto ret = samples.stream().filter(e -> !e.getBarcode().contains(StringPool.UNDERLINE))
                    .findFirst()
                    .orElse(samples.stream().max(Comparator.comparing(BaseSampleEsModelDto::getFinalCheckDate)).get());
            if (forcedMerge) {
                final BaseSampleEsModelDto baseSample = samples.stream().max(Comparator.comparing(BaseSampleEsModelDto::getFinalCheckDate)).get();
                ret.setCheckDate(baseSample.getCheckDate());
            }
            // 检验结果
            JSONArray results = new JSONArray();
            if (ret instanceof RoutineInspectionDto) {
                results = JSON.parseArray(JSON.toJSONString(((RoutineInspectionDto) ret).getReportItems()));
            } else if (ret instanceof OutsourcingInspectionDto) {
                results = JSON.parseArray(JSON.toJSONString(((OutsourcingInspectionDto) ret).getReportItems()));
            }

            // 检验项目
            List<BaseSampleEsModelDto.TestItem> testItems = ret.getTestItems();
            Set<String> testItemCodes = testItems.stream().map(BaseSampleEsModelDto.TestItem::getTestItemCode).collect(Collectors.toSet());

            // 结果备注
            // AtomicInteger remarkIndex = new AtomicInteger(1);
            String resultRemark = ret.getResultRemark();
            StringBuilder remark = new StringBuilder(String.format("%s", resultRemark));

            // 图片

            // 合并结果数据
            for (BaseSampleEsModelDto e : samples.stream().filter(e -> !Objects.equals(e.getApplySampleId(), ret.getApplySampleId())).collect(Collectors.toList())) {
                if (e instanceof RoutineInspectionDto) {
                    results.addAll(JSON.parseArray(JSON.toJSONString(((RoutineInspectionDto) e).getReportItems())));
                } else if (e instanceof OutsourcingInspectionDto) {
                    results.addAll(JSON.parseArray(JSON.toJSONString(((OutsourcingInspectionDto) e).getReportItems())));
                }

                // 检验项目合并
                for (BaseSampleEsModelDto.TestItem testItem : e.getTestItems()) {
                    if (testItemCodes.add(testItem.getTestItemCode())) {
                        testItems.add(testItem);
                    }
                }

                // 结果备注 合并
                if (StringUtils.isNotBlank(e.getResultRemark())) {
                    remark.append(remark.length() > 1 ? StringPool.NEW_LINE : StringPool.EMPTY).append(e.getResultRemark());
                }
            }

            // 合并PDF报告单

            String mergePdf = "";

            if (forcedMerge) {
                final List<String> urls = samples.stream().flatMap(e -> e.getReports().stream()).map(BaseSampleEsModelDto.Report::getUrl).collect(Collectors.toList());
                mergePdf = pdfReportService.mergePdfByUrls2Url(urls, 1);
            } else {
                List<String> reportUrls = reportService.mergePrint(samples.stream().map(BaseSampleEsModelDto::getApplySampleId).collect(Collectors.toSet()));
                mergePdf = pdfReportService.mergePdfByUrls2Url(reportUrls);
            }
            // TODO for test
            // String mergePdf = "https://obs.labway.cn/labway-lims/prod/2024/10/28/10/06/a57f18a09d384e1d9158f3c4a7dc130e";
            ret.getReports().get(0).setUrl(mergePdf);
            ret.setTestItems(testItems);
            // 结果备注
            ret.setResultRemark(remark.toString());

            if (ret instanceof RoutineInspectionDto) {
                ((RoutineInspectionDto) ret).setReportItems(results.toJavaList(RoutineInspectionDto.RoutineReportItem.class));
            } else if (ret instanceof OutsourcingInspectionDto) {
                ((OutsourcingInspectionDto) ret).setReportItems(results.toJavaList(OutsourcingInspectionDto.OutsourcingReportItem.class));
            }

            retSamples.add(ret);
            return true;
        }

        log.info("样本 [{}] 分血，但是报告未出全，不回传结果", sampleEsModelDto.getBarcode());

        return false;
    }

    /**
     * 判断报告延迟的时间
     *
     * @return true:报告审核已超过延迟时间
     */
    private boolean checkReportDelayTime(BaseSampleEsModelDto sampleDto, Map<Long, Set<String>> delayHspOrgCodesMap) {
        long delayMinutes;
        if ((delayMinutes = getDelayMinutes(sampleDto, delayHspOrgCodesMap)) > 0) {
            long now = DateUtil.date().getTime();
            // 审核时间 + 延迟时间 在当前时间之前
            return sampleDto.getFinalCheckDate().getTime() + TimeUnit.MINUTES.toMillis(delayMinutes) < now;
        }
        return true;
    }

    /**
     * 获取配置的延迟报告时间
     * 由于业务中台中间库服务在拉取报告的时候，在审核开始，结束时间各加了30分钟，所以这里判断实际延迟分钟数要减去30分钟
     * 这个30分钟的buffer放在配置里面，避免后期业务中台修改还需要修改代码发布
     *
     * @param sampleDto
     * @param delayHspOrgCodesMap
     * @return
     */
    private long getDelayMinutes(BaseSampleEsModelDto sampleDto, Map<Long, Set<String>> delayHspOrgCodesMap) {
        String hspOrgCode = sampleDto.getHspOrgCode();
        if (MapUtil.isNotEmpty(delayHspOrgCodesMap)) {
            AtomicLong delayMinutes = new AtomicLong(NumberUtils.LONG_ZERO);
            delayHspOrgCodesMap.entrySet().stream().filter(e -> e.getValue().contains(hspOrgCode))
                    .findFirst().ifPresent(e -> delayMinutes.set(e.getKey()));
            Long bufferMinutes = businessConfig.getHspDelayBufferMinutes().getOrDefault(hspOrgCode, 0L);

            return Math.max(delayMinutes.get() - bufferMinutes, NumberUtils.LONG_ZERO);
        }

        return NumberUtils.LONG_ZERO;
    }

    /**
     * 判断是否是 非 常规检验 和 外送检验
     *
     * @param itemType
     */
    private boolean isNotSupportMergeItemType(String itemType) {
        return !ItemTypeEnum.ROUTINE.name().equals(itemType) && !ItemTypeEnum.OUTSOURCING.name().equals(itemType);
    }

    /**
     * 过滤出来需要分血合并的机构
     *
     * @param hspOrgCodes
     */
    private Set<String> filterConfigMergeCodes(Set<String> hspOrgCodes) {
        // 判断送检机构是否配置了合并打印
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.MERGE_PRINT.getCode(), orgId);

        // 送检机构配置了合并打印
        if (Objects.isNull(param) || StringUtils.isBlank(param.getParamValue())) {
            return Set.of();
        }

        log.info("合并打印报告单配置：{}", JSONObject.toJSONString(param));

        List<String> configCodes = Arrays.asList(param.getParamValue().split(StringPool.COMMA));

        return hspOrgCodes.stream().filter(configCodes::contains).collect(Collectors.toSet());
    }

    /**
     * 过滤出来 分条码合并回传 的机构（东莞）
     *
     * @param hspOrgCodes
     */
    private Set<String> filterSplitBarcodeMergeResultCodes(Set<String> hspOrgCodes) {
        // 判断送检机构是否配置了 分条码合并回传
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.SPLIT_BARCODE_MERGE_RESULT.getCode(), orgId);

        // 送检机构配置了 分条码合并回传
        if (Objects.isNull(param) || StringUtils.isBlank(param.getParamValue())) {
            return Set.of();
        }

        log.info("分条码合并回传报告单配置：{}", JSONObject.toJSONString(param));

        List<String> configCodes = Arrays.asList(param.getParamValue().split(StringPool.COMMA));

        return hspOrgCodes.stream().filter(configCodes::contains).collect(Collectors.toSet());
    }

}
