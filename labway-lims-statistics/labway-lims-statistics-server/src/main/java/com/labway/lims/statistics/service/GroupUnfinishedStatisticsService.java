package com.labway.lims.statistics.service;

import com.labway.lims.statistics.vo.GroupUnfinishedStatisticsRequestVo;
import com.labway.lims.statistics.vo.GroupUnfinishedStatisticsVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <pre>
 * GroupUnfinishedStatisticsService
 * 专业组未完成工作量统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/7/5 11:12
 */
public interface GroupUnfinishedStatisticsService {

    /**
     * 待接收列表
     */
    GroupUnfinishedStatisticsVo waitReceive(@RequestBody @Validated GroupUnfinishedStatisticsRequestVo requestVo);

    /**
     * 待交接列表
     */
    GroupUnfinishedStatisticsVo waitHandover(@RequestBody @Validated GroupUnfinishedStatisticsRequestVo requestVo);

    /**
     * 待二次分拣列表
     */
    GroupUnfinishedStatisticsVo waitTwoPick(@RequestBody @Validated GroupUnfinishedStatisticsRequestVo requestVo);

    /**
     * 待审核列表
     */
    GroupUnfinishedStatisticsVo waitAudit(@RequestBody @Validated GroupUnfinishedStatisticsRequestVo requestVo);
}
