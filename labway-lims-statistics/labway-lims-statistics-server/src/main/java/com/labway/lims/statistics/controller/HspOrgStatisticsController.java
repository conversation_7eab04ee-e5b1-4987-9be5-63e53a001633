
package com.labway.lims.statistics.controller;

import com.labway.lims.api.enums.apply.SampleSourceEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyLogisticsDto;
import com.labway.lims.apply.api.dto.ApplyLogisticsSampleDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.apply.api.service.pda.PdaTobeConfirmedApplyService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.HspOrgStatisticsDeleteImgVo;
import com.labway.lims.statistics.vo.HspOrgStatisticsRequestVo;
import com.labway.lims.statistics.vo.HspOrgStatisticsUploadImgVo;
import com.labway.lims.statistics.vo.HspOrgStatisticsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 送检机构申请单查询
 */
@RestController
@RequestMapping("/hsp-org-statistics")
public class HspOrgStatisticsController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private ApplyLogisticsSampleService applyLogisticsSampleService;
    @DubboReference
    private ApplyLogisticsService applyLogisticsService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private ApplySampleService ApplySampleService;
    @DubboReference
    private  ApplySampleImageService ApplySampleImageService;
    @DubboReference
    private PdaTobeConfirmedApplyService pdaTobeConfirmedApplyService;


    @PostMapping("/statistics")
    public List<HspOrgStatisticsVo> statistics(@RequestBody HspOrgStatisticsRequestVo vo) {

        if (Objects.isNull(vo.getBeginSignDate()) || Objects.isNull(vo.getEndSignDate())) {
            throw new IllegalStateException("日期错误");
        }

        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.startCreateDate(vo.getBeginSignDate());
        builder.endCreateDate(vo.getEndSignDate());

        if (Objects.nonNull(vo.getHspOrgId())) {
            builder.hspOrgIds(Set.of(vo.getHspOrgId()));
        }

        if (Objects.nonNull(vo.getTestItemId())) {
            builder.testItemIds(Set.of(vo.getTestItemId()));
        }

        if (StringUtils.isNotBlank(vo.getPatientName())) {
            builder.patientName(vo.getPatientName());
        }

        if (StringUtils.isNotBlank(vo.getBarcode())) {
            builder.barcodes(Set.of(vo.getBarcode()));
        }

        // 过滤样本来源
        if (StringUtils.isNotBlank(vo.getSampleSource())){
            builder.sampleSources(Set.of(vo.getSampleSource()));
        }

        // 过滤已终止的样本
        builder.excludeSampleStatus(List.of(SampleStatusEnum.STOP_TEST.getCode()));

        final List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(builder.build());
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }


        // 查询条码环节
        List<Long> sampleIds = samples.stream().map(BaseSampleEsModelDto::getApplySampleId).distinct().collect(Collectors.toList());
        Map<Long, List<SampleFlowDto>> sampleFlowsMap = sampleFlowService.selectWithOutContentByApplySampleIdsAsMap(sampleIds);

        // 统计图片数量
        List<ApplySampleImageDto> applySampleImageDtos =  ApplySampleImageService.queryByApplySampleIds(sampleIds);
        Map<Long, List<ApplySampleImageDto>> applySampleImageDtosMap = applySampleImageDtos.stream().collect(Collectors.groupingBy(ApplySampleImageDto::getApplySampleId));

        // 查询pda样本确认人信息
        List<String> masterBarcodes = samples.stream().filter(e -> StringUtils.isNotBlank(e.getSampleSource()) && StringUtils.equals(SampleSourceEnum.PDA.getCode(), e.getSampleSource())).map(BaseSampleEsModelDto::getMasterBarcode).distinct().collect(Collectors.toList());
        List<PdaTobeConfirmedApplyDto> pdaTobeConfirmedApplyDtos = pdaTobeConfirmedApplyService.selectByMasterBarcodes(masterBarcodes);
        Map<String, List<PdaTobeConfirmedApplyDto>> masterBarcodeGroup = pdaTobeConfirmedApplyDtos.stream().collect(Collectors.groupingBy(PdaTobeConfirmedApplyDto::getMasterBarcode));

        final Map<HspOrgStatisticsVo, List<HspOrgStatisticsVo.Sample>> map = new LinkedHashMap<>();
        for (BaseSampleEsModelDto sample : samples) {
            final HspOrgStatisticsVo.Sample s = new HspOrgStatisticsVo.Sample();
            BeanUtils.copyProperties(sample, s);

            // 图片数量
            List<ApplySampleImageDto> tempApplySampleImageDtos = applySampleImageDtosMap.get(sample.getApplySampleId());
            s.setSampleImgCount(CollectionUtils.isNotEmpty(tempApplySampleImageDtos) ? tempApplySampleImageDtos.size() : NumberUtils.INTEGER_ZERO);

            // 确认人
            List<PdaTobeConfirmedApplyDto> tempPdaTobeConfirmedApplyDtos = masterBarcodeGroup.get(sample.getMasterBarcode());
            if (CollectionUtils.isNotEmpty(tempPdaTobeConfirmedApplyDtos)) {
                PdaTobeConfirmedApplyDto pdaTobeConfirmedApplyDto = tempPdaTobeConfirmedApplyDtos.get(0);
                s.setConformerId(pdaTobeConfirmedApplyDto.getConformerId());
                s.setConformerName(pdaTobeConfirmedApplyDto.getConformerName());
                s.setConformerTime(pdaTobeConfirmedApplyDto.getConformerTime());
            }

            s.setPatientSubage(sample.getPatientSubage());
            s.setPatientSubageUnit(sample.getPatientSubageUnit());

            if (Objects.equals(SampleStatusEnum.AUDIT.getCode(), sample.getSampleStatus())) {
                s.setCheckDate(sample.getFinalCheckDate());
            }else {
                s.setCheckDate(null);
            }

            s.setStatus(sample.getSampleStatus());
            s.setTestItemNames(Collections.emptyList());

            if (CollectionUtils.isNotEmpty(sample.getTestItems())) {
                // 检验项目 排除终止的
                List<BaseSampleEsModelDto.TestItem> testItems = sample.getTestItems().stream()
                        .filter(obj -> !(Objects.equals(obj.getStopStatus(), StopTestStatus.STOP_TEST_CHARGE.getCode())
                                || Objects.equals(obj.getStopStatus(), StopTestStatus.STOP_TEST_FREE.getCode())))
                        .collect(Collectors.toList());
                // 禁用的项目不显示/统计
                testItems.removeIf(e -> Objects.equals(e.getIsDisabled(), YesOrNoEnum.YES.getCode()));
                s.setTestItemNames(testItems.stream().map(BaseSampleEsModelDto.TestItem::getTestItemName)
                        .collect(Collectors.toList()));
            }

            final HspOrgStatisticsVo v = new HspOrgStatisticsVo();
            v.setHspOrgId(sample.getHspOrgId());
            v.setHspOrgName(sample.getHspOrgName());

            map.computeIfAbsent(v, k -> new LinkedList<>()).add(s);
        }

        return map.entrySet().stream().map(e -> {
            final HspOrgStatisticsVo v = new HspOrgStatisticsVo();
            v.setHspOrgId(e.getKey().getHspOrgId());
            v.setHspOrgName(e.getKey().getHspOrgName());
            v.setSignCount(e.getValue().size());
            // 已经审核的数量
            v.setAuditCount((int) e.getValue().stream().filter(l -> Objects.equals(l.getStatus(), SampleStatusEnum.AUDIT.getCode())).count());
            v.setSamples(e.getValue());
            return v;
        }).collect(Collectors.toList());
    }

    @PostMapping("/images")
    public List<String> images(Long applyId) {

        if (Objects.isNull(applyId)) {
            throw new IllegalStateException("参数错误");
        }

        final ApplyLogisticsSampleDto als = applyLogisticsSampleService.selectByApplyId(applyId);
        if (Objects.isNull(als)) {
            return Collections.emptyList();
        }

        final ApplyLogisticsDto applyLogistics = applyLogisticsService.selectByApplyLogisticsId(als.getApplyLogisticsId());
        if (Objects.isNull(applyLogistics)) {
            return Collections.emptyList();
        }

        if (StringUtils.isBlank(applyLogistics.getApplyImage())) {
            return Collections.emptyList();
        }

        return Arrays.asList(applyLogistics.getApplyImage().split(","));

    }


    /**
     * 上传样本图片-图片绑定在样本信息下面
     */
    @PostMapping("/upload/images")
    public List<Long> uploadImages(@RequestBody @Valid HspOrgStatisticsUploadImgVo vo) {

        // 查询样本信息
        List<ApplySampleDto> applySampleDtos = ApplySampleService.selectByApplySampleIds(vo.getApplySampleIds());

        if (CollectionUtils.isEmpty(applySampleDtos)) {
            // 抛出异常
            throw new IllegalStateException("样本信息不存在！");
        }

        // 判断是否存在pda样本
        Optional<ApplySampleDto> first = applySampleDtos.stream().filter(obj -> Objects.equals(obj.getSampleSource(), SampleSourceEnum.PDA.getCode())).findFirst();
        if (first.isPresent()){
            throw new IllegalStateException("PDA样本不允许上传图片！");
        }


        List<ApplySampleImageDto> ApplySampleImageDtos = new ArrayList<>();

        applySampleDtos.forEach(e -> {
            vo.getImgUrls().forEach(imgUrl -> {
                ApplySampleImageDto dto = new ApplySampleImageDto();
                dto.setApplyId(e.getApplyId());
                dto.setApplySampleId(e.getApplySampleId());
                dto.setImageUrl(imgUrl);
                ApplySampleImageDtos.add(dto);
            });

        });

        // 保存样本图片信息
        ApplySampleImageService.insertBatch(ApplySampleImageDtos);

        return applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());
    }


    /**
     * 删除申请单图片
     * 删除指定的pdf地址，删除指定样本的pdf地址
     */
    @PostMapping("/delete/images")
    public Integer deleteImages(@RequestBody @Valid HspOrgStatisticsDeleteImgVo vo) {

        // 查询样本信息
        ApplySampleDto applySampleDto = ApplySampleService.selectByApplySampleId(vo.getApplySampleId());

        if (Objects.isNull(applySampleDto)) {
            // 抛出异常
            throw new IllegalStateException("样本信息不存在！");
        }

        // 判断是否存在pda样本
        if (Objects.equals(applySampleDto.getSampleSource(), SampleSourceEnum.PDA.getCode())) {
            throw new IllegalStateException("PDA样本不允许删除图片！");
        }


        return ApplySampleImageService.deleteById(vo.getApplySampleImgId());
    }


    @PostMapping("/sample/images")
    public List<ApplySampleImageDto> sampleImages(Long applySampleId) {

        if (Objects.isNull(applySampleId)) {
            throw new IllegalStateException("参数错误");
        }

        List<ApplySampleImageDto> applySampleImageDtos = ApplySampleImageService.queryByApplySampleIds(Collections.singletonList(applySampleId));


        return applySampleImageDtos;
    }

}
