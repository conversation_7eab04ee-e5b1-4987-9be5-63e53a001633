package com.labway.lims.statistics.service.chain.byPlateform;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import com.labway.lims.apply.api.dto.ByPlatformStatisticsResponseDto;
import com.labway.lims.apply.api.dto.SampleTestItemDto;
import com.labway.lims.apply.api.dto.TestItemIncomeFilterDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.vo.QueryCombinePackageListTestItemsVo;
import com.labway.lims.statistics.vo.FinanceStatisticsQueryVo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.impl.ContextBase;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Getter
@Setter
public class PlatformStatisticsContext extends ContextBase {

    // 送检机构
    public static final String ORG_ID = "ORG_ID" + IdUtil.objectId();

    // 送检机构
    public static final String HSP_ORG = "HSP_ORG_" + IdUtil.objectId();

    // 最终结果
    public static final String RESULT = "RESULT_" + IdUtil.objectId();

    // 开票名称
    public static final String CUSTOMER_NAME_TYPE_ENUM = "CUSTOMER_NAME_TYPE_ENUM_" + IdUtil.objectId();

    // 查询参数
    public static final String FINANCE_STATISTICS_QUERY = "FINANCE_STATISTICS_QUERY_" + IdUtil.objectId();

    // 所有es 结构数据
    public static final String BASE_SAMPLE_ES_DATA = "BASE_SAMPLE_ES_DATA_" + IdUtil.objectId();

    // 所有es 结构数据 终止检验的
    public static final String STOP_SAMPLE_ES_DATA = "STOP_SAMPLE_ES_DATA_" + IdUtil.objectId();

    // 所有es 结构数据 病理检验
    public static final String PATHOLOGY_SAMPLE_ES_DATA = "PATHOLOGY_SAMPLE_ES_DATA_" + IdUtil.objectId();

    // 样本检验项目和折扣
    public static final String SAMPLE_TEST_ITEM_LIST = "SAMPLE_TEST_ITEM_LIST_" + IdUtil.objectId();

    // 财务统计样本检验项目和折扣
    public static final String COMBINE_PACKAGE_MAP = "COMBINE_PACKAGE_MAP_" + IdUtil.objectId();

    // 是否需要赋值财务专业组
    private boolean needFinanceGroup = false;

    /**
     * 财务预算统计导出
     * true: 匹配到财务套餐之后，显示原项目，所匹配的财务套餐也显示
     * false: 匹配到财务套餐之后，只显示财务套餐，原项目不显示
     */
    private Boolean financialBudgetExport = true;

    private TestItemIncomeFilterDto filterDto;


    public PlatformStatisticsContext(){
         filterDto = new TestItemIncomeFilterDto();
    }

    public boolean getNeedFinanceGroup() {
        return needFinanceGroup;
    }
    public void setNeedFinanceGroup(boolean needFinanceGroup) {
        this.needFinanceGroup = needFinanceGroup;
    }

    public TestItemIncomeFilterDto getFilterDto() {
        return filterDto;
    }

    public void setFilterDto(TestItemIncomeFilterDto filterDto) {
        this.filterDto = filterDto;
    }

    public Map<Long, List<QueryCombinePackageListTestItemsVo>> getCombinePackageMap() {
        return (Map<Long, List<QueryCombinePackageListTestItemsVo>>) get(COMBINE_PACKAGE_MAP);
    }

    public FinanceStatisticsQueryVo getFinanceStatisticsQuery() {
        return (FinanceStatisticsQueryVo) get(FINANCE_STATISTICS_QUERY);
    }

    public ByPlatformStatisticsResponseDto getResult() {

        ByPlatformStatisticsResponseDto byPlatformStatisticsResponseDto = (ByPlatformStatisticsResponseDto) get(RESULT);

        if (Objects.isNull(byPlatformStatisticsResponseDto)) {
            synchronized (this) {

                byPlatformStatisticsResponseDto = (ByPlatformStatisticsResponseDto) get(RESULT);
                if (Objects.isNull(byPlatformStatisticsResponseDto)) {
                    byPlatformStatisticsResponseDto = new ByPlatformStatisticsResponseDto();
                    byPlatformStatisticsResponseDto.setItemList(new ArrayList<>());
                    byPlatformStatisticsResponseDto.setOrgList(new ArrayList<>());
                    byPlatformStatisticsResponseDto.setOutOrgStatisticsItems(new ArrayList<>());
                    put(RESULT, byPlatformStatisticsResponseDto);
                }

            }
        }
        return byPlatformStatisticsResponseDto;
    }

    public CustomerNameTypeEnum getCustomerNameTypeEnum() {
        return (CustomerNameTypeEnum) get(CUSTOMER_NAME_TYPE_ENUM);
    }

    public List<HspOrganizationDto> getHspOrgList() {
        return (List<HspOrganizationDto>) get(HSP_ORG);
    }

    public List<BaseSampleEsModelDto> getBaseEsData() {
        return (List<BaseSampleEsModelDto>) get(BASE_SAMPLE_ES_DATA);
    }

    public List<BaseSampleEsModelDto> getStopEsData() {
        return (List<BaseSampleEsModelDto>) get(STOP_SAMPLE_ES_DATA);
    }

    public List<BaseSampleEsModelDto> getPathologyEsData() {
        return (List<BaseSampleEsModelDto>) get(PATHOLOGY_SAMPLE_ES_DATA);
    }

    public static PlatformStatisticsContext from(Context context) {
        return (PlatformStatisticsContext) context;
    }

    public List<SampleTestItemDto> getSampleTestItemList() {
        return (List<SampleTestItemDto>) get(SAMPLE_TEST_ITEM_LIST);
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DateRange {
        /**
         * 开始时间
         */
        private String startDate;

        /**
         * 结束时间
         */
        private String endDate;

    }
}
