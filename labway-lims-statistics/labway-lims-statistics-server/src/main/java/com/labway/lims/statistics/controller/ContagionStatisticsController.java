package com.labway.lims.statistics.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.dto.QueryContagionReportItemInfoDto;
import com.labway.lims.statistics.dto.QueryContagionSampleInfoDto;
import com.labway.lims.statistics.vo.QueryContagionSampleInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ContagionStatisticsController
 * 传染病统计
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/14 10:30
 */
@Slf4j
@RestController
@RequestMapping("/contagion-statistics")
public class ContagionStatisticsController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private SystemParamService systemParamService;
    @DubboReference
    private ReportItemService reportItemService;

    /**
     * 查询配置的传染病报告项目信息
     */
    @GetMapping("/queryContagionReportItemInfo")
    public List<QueryContagionReportItemInfoDto> queryContagionReportItemInfo() {

        final LoginUserHandler.User user = LoginUserHandler.get();

        // 查询传染病报告项目配置
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.INFECTIOUS_DISEASES_REPORT_CODES.getCode(), user.getOrgId());
        if (param == null || StringUtils.isBlank(param.getParamValue())) {
            throw new LimsException("暂未配置传染病报告项目信息!");
        }

        final List<String> reportItemCodelist = Arrays.asList(param.getParamValue().split(","));

        // 查询报告项目信息
        final List<ReportItemDto> reportItemDtos = reportItemService.selectByReportItemCodes(reportItemCodelist, user.getOrgId());
        if (CollectionUtils.isEmpty(reportItemDtos)) {
            return Collections.emptyList();
        }

        final Map<String, ReportItemDto> reportItemDtoMap = reportItemDtos
                .stream().collect(Collectors.toMap(ReportItemDto::getReportItemCode, Function.identity(), (a, b) -> a));

        return reportItemDtoMap.values().stream().map(dto -> {
            QueryContagionReportItemInfoDto queryContagionReportItemInfoDto = new QueryContagionReportItemInfoDto();
            queryContagionReportItemInfoDto.setReportItemCode(dto.getReportItemCode());
            queryContagionReportItemInfoDto.setReportItemId(dto.getReportItemId());
            queryContagionReportItemInfoDto.setReportItemName(dto.getReportItemName());
            return queryContagionReportItemInfoDto;
        }).collect(Collectors.toList());

    }

    /**
     * 查询传染病样本信息
     */
    @RequestMapping("/queryContagionSampleInfo")
    public List<QueryContagionSampleInfoDto> queryContagionSampleInfo(@RequestBody QueryContagionSampleInfoVo queryContagionSampleInfoVo) {

        final LoginUserHandler.User user = LoginUserHandler.get();

        // 查询微生物项目样本性状配置
        if (CollectionUtils.isEmpty(queryContagionSampleInfoVo.getReportItemCodes())) {
            final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.INFECTIOUS_DISEASES_REPORT_CODES.getCode(), user.getOrgId());
            if (param == null || StringUtils.isBlank(param.getParamValue())) {
                throw new LimsException("未配置传染病报告项目信息!");
            }
            queryContagionSampleInfoVo.setReportItemCodes(Arrays.asList(param.getParamValue().split(",")));
        }

        final SampleEsQuery sampleEsQuery = fillQuery(queryContagionSampleInfoVo);

        final List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(sampleEsQuery);

        if (CollectionUtils.isEmpty(baseSampleEsModelDtos)) {
            return Collections.emptyList();
        }

        // 筛选外送检验 结果值是UP或者↑的样本
        final List<OutsourcingInspectionDto> outsourcingInspectionDtos = baseSampleEsModelDtos.stream()
                .filter(OutsourcingInspectionDto.class::isInstance).map(e -> (OutsourcingInspectionDto) e)
                // .filter(dto -> dto.getReportItems().stream().anyMatch(e -> StringUtils.equals(e.getJudge(), TestJudgeEnum.UP.getValue())))
                .peek(dto -> dto.getReportItems().removeIf(e -> !StringUtils.equals(e.getJudge(), TestJudgeEnum.UP.getValue())))
                .filter(dto -> CollectionUtils.isNotEmpty(dto.getReportItems()))
                .collect(Collectors.toList());

        // 筛选符合传染病的样本 结果值是UP或者↑的样本 (只统计常规检验)
        final List<RoutineInspectionDto> routineInspectionDtos = baseSampleEsModelDtos.stream()
                .filter(RoutineInspectionDto.class::isInstance).map(dto -> (RoutineInspectionDto) dto)
                // .filter(dto -> dto.getReportItems().stream().anyMatch(e -> StringUtils.equals(e.getJudge(), TestJudgeEnum.UP.getValue())))
                .peek(dto -> dto.getReportItems().removeIf(e -> !StringUtils.equals(e.getJudge(), TestJudgeEnum.UP.getValue())))
                .filter(dto -> CollectionUtils.isNotEmpty(dto.getReportItems()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(outsourcingInspectionDtos) && CollectionUtils.isEmpty(routineInspectionDtos)) {
            return Collections.emptyList();
        }

        // 结果转换
        return convertResultDto(routineInspectionDtos, outsourcingInspectionDtos, queryContagionSampleInfoVo);
    }

    // 填充查询条件
    private SampleEsQuery fillQuery(QueryContagionSampleInfoVo queryContagionSampleInfoVo) {

        final SampleEsQuery sampleEsQuery = new SampleEsQuery();

        // 接收时间
        if (ObjectUtils.allNotNull(queryContagionSampleInfoVo.getReceiveDateStart(), queryContagionSampleInfoVo.getReceiveDateEnd())) {
            sampleEsQuery.setStartSignDate(queryContagionSampleInfoVo.getReceiveDateStart());
            sampleEsQuery.setEndSignDate(queryContagionSampleInfoVo.getReceiveDateEnd());
        }
        // 审核时间
        if (ObjectUtils.allNotNull(queryContagionSampleInfoVo.getCheckDateStart(), queryContagionSampleInfoVo.getCheckDateEnd())) {
            sampleEsQuery.setStartFinalCheckDate(queryContagionSampleInfoVo.getCheckDateStart());
            sampleEsQuery.setEndFinalCheckDate(queryContagionSampleInfoVo.getCheckDateEnd());
        }

        // 专业组
        if (CollectionUtils.isNotEmpty(queryContagionSampleInfoVo.getGroupIds())) {
            sampleEsQuery.setGroupIds(queryContagionSampleInfoVo.getGroupIds());
        }

        sampleEsQuery.setReportItemCodes(new HashSet<>(queryContagionSampleInfoVo.getReportItemCodes()));

        // 送检机构
        if (CollectionUtils.isNotEmpty(queryContagionSampleInfoVo.getSendOrgCodes())) {
            sampleEsQuery.setHspOrgCodes(new HashSet<>(queryContagionSampleInfoVo.getSendOrgCodes()));
        }

        // 病人姓名
        if (StringUtils.isNotBlank(queryContagionSampleInfoVo.getPatientName())) {
            sampleEsQuery.setPatientName(queryContagionSampleInfoVo.getPatientName());
        }

        // 条码号
        if (StringUtils.isNotBlank(queryContagionSampleInfoVo.getBarcode())) {
            sampleEsQuery.setBarcodeOrOutbarcodes(Collections.singleton(queryContagionSampleInfoVo.getBarcode()));
        }

        // 排除终止检验的
        sampleEsQuery.setItemStatus(StopTestStatus.NO_STOP_TEST.getCode());
        // 排除项目禁用的
        sampleEsQuery.setExcludeItemIsDisabled(YesOrNoEnum.YES.getCode());

        return sampleEsQuery;
    }

    // 转换结果实体
    // 展示报告项目对应的检验项目 同时该检验项目
    private List<QueryContagionSampleInfoDto> convertResultDto(List<RoutineInspectionDto> queryContagionSampleInfoDtos,
            List<OutsourcingInspectionDto> outsourcingInspectionDtos, QueryContagionSampleInfoVo queryContagionSampleInfoVo) {

        final List<QueryContagionSampleInfoDto> queryContagionSampleInfoDtoList = new ArrayList<>();

        final List<String> reportItemCodes = queryContagionSampleInfoVo.getReportItemCodes();
        // 常规检验
        for (RoutineInspectionDto tempContagionSampleInfoDto : queryContagionSampleInfoDtos) {
            // 这里过滤出配置的报告项目编码 并且检验结果是UP或者⬆️的项目
            final List<RoutineInspectionDto.RoutineReportItem> tempReportItems = tempContagionSampleInfoDto.getReportItems().stream()
                    .filter(reportItem -> reportItemCodes.contains(reportItem.getReportItemCode())
                            && StringUtils.isNotBlank(reportItem.getJudge())
                            && Objects.equals(reportItem.getJudge(), TestJudgeEnum.UP.getValue()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tempReportItems)) {
                continue;
            }

            final QueryContagionSampleInfoDto temp = convert(tempContagionSampleInfoDto);

            // 报告项目根据检验项目编码分组
            final Map<String, List<RoutineInspectionDto.RoutineReportItem>> reportItemGroup = tempContagionSampleInfoDto.getReportItems()
                    .stream().collect(Collectors.groupingBy(RoutineInspectionDto.RoutineReportItem::getTestItemCode));
            // 传染病项目编码
            final Set<String> contagionReportItemCodes = tempReportItems.stream().map(BaseSampleEsModelDto.ReportItem::getTestItemCode).collect(Collectors.toSet());
            // 需要回显的检验项目
            final List<BaseSampleEsModelDto.TestItem> testItems = tempContagionSampleInfoDto.getTestItems().stream()
                    .filter(e -> contagionReportItemCodes.contains(e.getTestItemCode())).collect(Collectors.toList());

            // 转换检验项目信息 （根据传染病项目回显检验项目，以及该检验项目下包含的其他报告项目）
            temp.setTestItems(testItems.stream().map(e -> this.convertTestItemRoutine(reportItemCodes, e, reportItemGroup)).collect(Collectors.toList()));

            queryContagionSampleInfoDtoList.add(temp);
        }

        // 外送检验
        for (OutsourcingInspectionDto outsourcingInspectionDto : outsourcingInspectionDtos) {
            // 这里过滤出配置的报告项目编码 并且检验结果是UP或者⬆️的项目
            final List<OutsourcingInspectionDto.OutsourcingReportItem> tempReportItems = outsourcingInspectionDto.getReportItems().stream()
                    .filter(reportItem -> reportItemCodes.contains(reportItem.getReportItemCode())
                            && StringUtils.isNotBlank(reportItem.getJudge())
                            && Objects.equals(reportItem.getJudge(), TestJudgeEnum.UP.getValue()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tempReportItems)) {
                continue;
            }

            final QueryContagionSampleInfoDto temp = convert(outsourcingInspectionDto);

            // 报告项目根据检验项目编码分组
            final Map<String, List<OutsourcingInspectionDto.OutsourcingReportItem>> reportItemGroup = outsourcingInspectionDto.getReportItems()
                    .stream().collect(Collectors.groupingBy(OutsourcingInspectionDto.OutsourcingReportItem::getTestItemCode));
            // 传染病项目编码
            final Set<String> contagionReportItemCodes = tempReportItems.stream().map(BaseSampleEsModelDto.ReportItem::getTestItemCode).collect(Collectors.toSet());
            // 需要回显的检验项目
            final List<BaseSampleEsModelDto.TestItem> testItems = outsourcingInspectionDto.getTestItems().stream()
                    .filter(e -> contagionReportItemCodes.contains(e.getTestItemCode())).collect(Collectors.toList());

            // 转换检验项目信息 （根据传染病项目回显检验项目，以及该检验项目下包含的其他报告项目）
            temp.setTestItems(testItems.stream().map(e -> this.convertTestItemOutsourcing(reportItemCodes, e, reportItemGroup)).collect(Collectors.toList()));

            queryContagionSampleInfoDtoList.add(temp);
        }

        // 按照接收时间正序排序返回
        return queryContagionSampleInfoDtoList.stream().sorted(Comparator.comparing(QueryContagionSampleInfoDto::getReceiveTime)).collect(Collectors.toList());
    }

    private QueryContagionSampleInfoDto convert(BaseSampleEsModelDto esModelDto) {
        final QueryContagionSampleInfoDto temp = new QueryContagionSampleInfoDto();
        temp.setHspOrgId(esModelDto.getHspOrgId());
        temp.setHspOrgName(esModelDto.getHspOrgName());
        temp.setHspOrgCode(esModelDto.getHspOrgCode());
        temp.setPatientName(esModelDto.getPatientName());
        temp.setSex(SexEnum.getByCode(esModelDto.getPatientSex()).getDesc());
        temp.setAge(PatientAges.toText(esModelDto));
        temp.setDepartment(esModelDto.getDept());
        temp.setBarcode(esModelDto.getBarcode());
        temp.setOutBarcode(esModelDto.getOutBarcode());
        temp.setReceiveTime(esModelDto.getSignDate());
        temp.setAuditTime(esModelDto.getFinalCheckDate());

        temp.setApplySampleId(esModelDto.getApplySampleId());
        temp.setPatientSex(esModelDto.getPatientSex());
        temp.setPatientAge(esModelDto.getPatientAge());
        temp.setPatientSubage(esModelDto.getPatientSubage());
        temp.setPatientSubageUnit(esModelDto.getPatientSubageUnit());
        temp.setApplyType(esModelDto.getApplyTypeName());
        /*temp.setTestItemId(item.getTestItemId());
        temp.setTestItemCode(item.getTestItemCode());
        temp.setTestItemName(item.getTestItemName());
        temp.setReportItemCode(item.getReportItemCode());
        temp.setReportItemName(item.getReportItemName());
        temp.setResult(item.getResult());
        temp.setUnit(item.getUnit());
        temp.setJudge(item.getJudge());
        temp.setRange(item.getRange());*/
        temp.setGroupId(esModelDto.getGroupId());
        temp.setGroupName(esModelDto.getGroupName());
        temp.setSampleTypeName(esModelDto.getSampleTypeName());
        temp.setApplyDate(esModelDto.getCreateDate()); // 1.1.0 检验结果信息查询，送检时间 取样本的创建时间
        temp.setTestDate(esModelDto.getTestDate());
        temp.setCheckDate(esModelDto.getFinalCheckDate());
        temp.setPatientVisitCard(esModelDto.getPatientVisitCard());
        temp.setSendDoctorCode(esModelDto.getSendDoctorCode());
        temp.setSendDoctorName(esModelDto.getSendDoctorName());
        temp.setDept(esModelDto.getDept());
        temp.setPatientBed(esModelDto.getPatientBed());
        // temp.setStatus(item.getStatus());

        return temp;
    }

    // 检验项目转化（常规检验）
    private QueryContagionSampleInfoDto.ContagionTestItemDto convertTestItemRoutine(List<String> reportItemCodes,
            BaseSampleEsModelDto.TestItem testItem, Map<String, List<RoutineInspectionDto.RoutineReportItem>> reportItemGroup) {
        final QueryContagionSampleInfoDto.ContagionTestItemDto tempContagionTestItemDto = new QueryContagionSampleInfoDto.ContagionTestItemDto();
        tempContagionTestItemDto.setTestItemId(String.valueOf(testItem.getTestItemId()));
        tempContagionTestItemDto.setTestItemCode(testItem.getTestItemCode());
        tempContagionTestItemDto.setTestItemName(testItem.getTestItemName());
        final List<RoutineInspectionDto.RoutineReportItem> routineReportItems = reportItemGroup.get(testItem.getTestItemCode());
        if (CollectionUtils.isNotEmpty(routineReportItems)) {
            tempContagionTestItemDto.setReportItemDtos(routineReportItems.stream()
                    .filter(e -> reportItemCodes.contains(e.getReportItemCode())) // 过滤掉非传染病的报告项目
                    .sorted(Comparator.comparing(BaseSampleEsModelDto.ReportItem::getPrintSort))
                    .map(this::convertReportItem)
                    .collect(Collectors.toList()));
        }

        return tempContagionTestItemDto;
    }

    // 检验项目转化（外送）
    private QueryContagionSampleInfoDto.ContagionTestItemDto convertTestItemOutsourcing(List<String> reportItemCodes,
            BaseSampleEsModelDto.TestItem testItem, Map<String, List<OutsourcingInspectionDto.OutsourcingReportItem>> reportItemGroup) {
        final QueryContagionSampleInfoDto.ContagionTestItemDto tempContagionTestItemDto = new QueryContagionSampleInfoDto.ContagionTestItemDto();
        tempContagionTestItemDto.setTestItemId(String.valueOf(testItem.getTestItemId()));
        tempContagionTestItemDto.setTestItemCode(testItem.getTestItemCode());
        tempContagionTestItemDto.setTestItemName(testItem.getTestItemName());
        final List<? extends BaseSampleEsModelDto.ReportItem> routineReportItems = reportItemGroup.get(testItem.getTestItemCode());
        if (CollectionUtils.isNotEmpty(routineReportItems)) {
            tempContagionTestItemDto.setReportItemDtos(routineReportItems.stream()
                    .filter(e -> reportItemCodes.contains(e.getReportItemCode())) // 过滤掉非传染病的报告项目
                    .sorted(Comparator.comparing(BaseSampleEsModelDto.ReportItem::getPrintSort))
                    .map(this::convertReportItem)
                    .collect(Collectors.toList()));
        }

        return tempContagionTestItemDto;
    }

    // 遗传报告项目转换
    private QueryContagionSampleInfoDto.ContagionReportItemDto convertReportItem(BaseSampleEsModelDto.ReportItem routineReportItem) {
        final QueryContagionSampleInfoDto.ContagionReportItemDto temp = new QueryContagionSampleInfoDto.ContagionReportItemDto();
        BeanUtils.copyProperties(routineReportItem, temp);
        // temp.setReportItemCode(routineReportItem.getReportItemCode());
        // temp.setReportItemName(routineReportItem.getReportItemName());
        // temp.setResult(routineReportItem.getResult());
        // temp.setJudge(routineReportItem.getJudge());
        return temp;
    }

}
