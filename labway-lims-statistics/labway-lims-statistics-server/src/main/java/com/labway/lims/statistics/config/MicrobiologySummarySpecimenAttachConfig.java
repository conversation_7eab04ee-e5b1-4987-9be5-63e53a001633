package com.labway.lims.statistics.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 当日接种标本 汇总 模板配置
 * 
 * <AUTHOR>
 * @since 2023/8/30 20:12
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "microbiology.summary-specimen")
public class MicrobiologySummarySpecimenAttachConfig {



    /**
     * key: 检验项目 code value : 对应模板code
     */
    private Map<String, String> attachMap;

}
