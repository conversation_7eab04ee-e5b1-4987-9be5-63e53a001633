package com.labway.lims.statistics.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.statistics.service.ExportService;
import com.labway.lims.statistics.vo.ExportFileCancelRequestVo;
import com.labway.lims.statistics.vo.ExportFileCancelResultVo;
import com.labway.lims.statistics.vo.ExportFileDeleteRequestVo;
import com.labway.lims.statistics.vo.ExportFileDeleteResultVo;
import com.labway.lims.statistics.vo.ExportFileDownloadRequestVo;
import com.labway.lims.statistics.vo.ExportFileDownloadResultVo;
import com.labway.lims.statistics.vo.ExportFileRequestVo;
import com.labway.lims.statistics.vo.ExportFileResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <pre>
 * ExportFileController
 * 导出文件controller
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/4/10 13:17
 */
@Slf4j
@RequestMapping("/export-file")
@RestController
public class ExportFileController extends BaseController {

    @Resource
    private ExportService exportService;

    @PostMapping("list")
    public ExportFileResultVo list(@RequestBody ExportFileRequestVo requestVo) {
        if (StringUtils.isBlank(requestVo.getSource())) {
            throw new IllegalArgumentException("页面不能为空");
        }
        return exportService.list(requestVo);
    }

    @PostMapping("download")
    public ExportFileDownloadResultVo download(@RequestBody ExportFileDownloadRequestVo requestVo) {
        exportService.download(requestVo);
        return new ExportFileDownloadResultVo() {{setUrl("https://baidu.com");}};
    }

    @PostMapping("cancel")
    public ExportFileCancelResultVo cancel(@RequestBody ExportFileCancelRequestVo requestVo) {
        exportService.cancel(requestVo);
        return new ExportFileCancelResultVo() {{setUrl("https://baidu.com");}};
    }

    @PostMapping("delete")
    public ExportFileDeleteResultVo delete(@RequestBody ExportFileDeleteRequestVo requestVo) {
        exportService.delete(requestVo);
        return new ExportFileDeleteResultVo() {{setUrl("https://baidu.com");}};
    }


}
