package com.labway.lims.statistics.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.api.service.GermService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 前处理工作量统计
 */
@Slf4j
@RestController
@RequestMapping("/preprocess-statistics")
public class PreprocessStatisticsController extends BaseController {

    @DubboReference
    private DictService dictService;

    @DubboReference
    private GermService germService;

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @Resource
    private EsConfig esConfig;

    //region ------------------------------ 前处理工作量统计 ------------------------------

    /**
     * 前处理就诊类型统计
     */
    @PostMapping("/apply-type-export")
    public Object preprocessApplyTypeExport(@RequestBody PreprocessWorkloadDataStatisticsQueryVo queryVo) throws IOException {
        final PreprocessApplyTypeVo vo = preprocessApplyType(queryVo);

        final List<PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics> columns = vo.getColumns().stream()
                .sorted(Comparator.comparing(PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics::getApplyTypeName))
                .collect(Collectors.toList());

        final List<PreprocessApplyTypeStatisticsVo> data = vo.getData();

        final File tempFile = File.createTempFile("preprocess-apply-type-export", null);

        try (final ExcelWriter writer = ExcelUtil.getBigWriter(); final FileOutputStream fos = new FileOutputStream(tempFile)) {
            LinkedList<Object> builder = new LinkedList<>(List.of(
                    //
                    "送检机构",
                    //
                    "项目名称"
            ));

            // 动态列
            builder.addAll(columns.stream().map(PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics::getApplyTypeName).collect(Collectors.toList()));

            // 写入行
            writer.writeRow(builder);

            for (final PreprocessApplyTypeStatisticsVo item : data) {
                builder.clear();

                builder.add(item.getHspOrgName());
                builder.add(item.getTestItemName());

                final List<PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics> applyTypeStatistics = item.getApplyTypeStatistics();

                for (final PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics column : columns) {
                    final String applyTypeCode = column.getApplyTypeCode();
                    final AtomicInteger count = applyTypeStatistics
                            .stream()
                            .filter(x -> StringUtils.equals(x.getApplyTypeCode(), applyTypeCode))
                            .findFirst()
                            .map(PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics::getCount)
                            .orElse(new AtomicInteger(0));

                    builder.add(count.get());

                }

                writer.writeRow(builder);
            }

            writer.flush(fos);
        }


        return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("前处理就诊类型统计.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel")).body(new FileSystemResource(tempFile));
    }

    /**
     * 前处理就诊类型统计
     */
    @PostMapping("/apply-type")
    public PreprocessApplyTypeVo preprocessApplyType(@RequestBody PreprocessWorkloadDataStatisticsQueryVo queryVo) {
        queryVo.defaultDate();

        // 查询条件
        final SampleEsQuery query = SampleEsQuery.builder()
                .pageNo(NumberUtils.INTEGER_ONE)
                .pageSize(esConfig.getPageSize())
                .startCreateDate(queryVo.getStartDate())
                .endCreateDate(queryVo.getEndDate())
                .build();

        final Long inputTerId = queryVo.getInputTerId();
        if (Objects.nonNull(inputTerId)) {
            query.setCreatorIds(Set.of(inputTerId));
        }

        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Set.of(queryVo.getHspOrgId()));
        }

        // 就诊类型
        DictItemDto unknown = new DictItemDto();
        unknown.setDictCode("unknown");
        unknown.setDictName("未知");

        final Map<String, DictItemDto> dictItemMap = dictService.selectByDictType(DictEnum.VISIT_TYPE.name()).stream()
                .collect(Collectors.toMap(DictItemDto::getDictCode, v -> v, (a, b) -> a));

        // key: PreprocessApplyTypeStatisticsVo      value: key: ApplyTypeStatistics value: count
        final Map<PreprocessApplyTypeStatisticsVo, Map<PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics, AtomicInteger>> preprocessApplyTypeStatisticsMap
                = new HashMap<>(20);

        Set<PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics> columns = new HashSet<>(20);
        final List<? extends BaseSampleEsModelDto> datas = elasticSearchSampleService.selectSamples(query);

        // 处理数据
        for (final BaseSampleEsModelDto data : datas) {
            final String applyType = data.getApplyTypeCode();
            final Long hspOrgId = data.getHspOrgId();
            final String hspOrgName = data.getHspOrgName();
            final List<BaseSampleEsModelDto.TestItem> testItems = ObjectUtils.defaultIfNull(data.getTestItems(), new ArrayList<>());

            DictItemDto dictItem = dictItemMap.get(applyType);
            // 可能这个字段被删除了，统一归类到未知
            if (Objects.isNull(dictItem)) {
                dictItem = unknown;
            }

            // 添加动态列
            final PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics applyTypeStatistics
                    = new PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics(dictItem.getDictCode(), dictItem.getDictName(), new AtomicInteger(0));
            columns.add(applyTypeStatistics);

            // 聚合数据
            for (final BaseSampleEsModelDto.TestItem testItem : testItems) {
                final Long testItemId = testItem.getTestItemId();
                final String testItemName = testItem.getTestItemName();

                final PreprocessApplyTypeStatisticsVo key = new PreprocessApplyTypeStatisticsVo();
                key.setTestItemId(testItemId);
                key.setTestItemName(testItemName);
                key.setHspOrgId(hspOrgId);
                key.setHspOrgName(hspOrgName);

                final Map<PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics, AtomicInteger> applyTypeStatisticsMap
                        = preprocessApplyTypeStatisticsMap.computeIfAbsent(key, k -> new HashMap<>(20));

                applyTypeStatisticsMap.computeIfPresent(applyTypeStatistics, (k, v) -> {
                    v.incrementAndGet();
                    return v;
                });

                applyTypeStatisticsMap.computeIfAbsent(applyTypeStatistics, k -> new AtomicInteger(1));
            }
        }

        // 就诊类型合计统计
        Map<String, AtomicInteger> totalMap = new HashMap<>();

        final List<PreprocessApplyTypeStatisticsVo> vos = preprocessApplyTypeStatisticsMap.entrySet().stream().map(m -> {
            final Map<PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics, AtomicInteger> value = m.getValue();

            final PreprocessApplyTypeStatisticsVo key = m.getKey();

            final List<PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics> applyTypeStatisticsList = value.entrySet().stream().map(item -> {
                final PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics itemKey = item.getKey();
                final AtomicInteger itemValue = item.getValue();
                itemKey.setCount(itemValue);

                // 就诊类型合计统计
                totalMap.computeIfPresent(itemKey.getApplyTypeCode(), (k, v) ->
                        new AtomicInteger(v.get() + itemValue.get()));

                totalMap.computeIfAbsent(itemKey.getApplyTypeCode(), k -> itemValue);

                return itemKey;

            }).collect(Collectors.toList());

            key.setApplyTypeStatistics(applyTypeStatisticsList);

            return key;
        }).collect(Collectors.toList());


        final PreprocessApplyTypeVo vo = new PreprocessApplyTypeVo();
        final Map<String, String> cloumnMap = columns.stream()
                .collect(Collectors.toMap(PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics::getApplyTypeCode, PreprocessApplyTypeStatisticsVo.ApplyTypeStatistics::getApplyTypeName, (a, b) -> a));
        vo.setColumnMap(cloumnMap);
        vo.setColumns(columns);
        vo.setData(vos.stream().sorted(Comparator.comparing(sort -> PinyinUtil.getFirstLetter(sort.getHspOrgName(), StringUtils.EMPTY))).collect(Collectors.toList()));
        vo.setTotalMap(totalMap);

        return vo;
    }


    /**
     * 前处理项目统计-export
     */
    @PostMapping("/project-export")
    public Object preprocessItemExport(@RequestBody PreprocessWorkloadDataStatisticsQueryVo queryVo) throws IOException {

        final List<PreprocessItemVo> vos = CollectionUtil.defaultIfEmpty(preprocessItem(queryVo).getList(), Collections.emptyList());

        final File tempFile = File.createTempFile("preprocess-item-export", null);

        try (final ExcelWriter writer = ExcelUtil.getBigWriter(); final FileOutputStream fos = new FileOutputStream(tempFile)) {
            LinkedList<Object> builder = new LinkedList<>(List.of(
                    //
                    "送检机构",
                    //
                    "项目名称",
                    //
                    "项目数量"
            ));

            // 写入行
            writer.writeRow(builder);

            for (final PreprocessItemVo item : vos) {
                builder.clear();

                builder.add(item.getHspOrgName());
                builder.add(item.getTestItemName());
                builder.add(item.getCount());


                writer.writeRow(builder);
            }

            writer.flush(fos);
        }


        return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("前处理项目统计.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel")).body(new FileSystemResource(tempFile));
    }

    /**
     * 前处理项目统计
     */
    @PostMapping("/project")
    public PreprocessItemRespVo preprocessItem(@RequestBody PreprocessWorkloadDataStatisticsQueryVo queryVo) {
        // 默认时间
        queryVo.defaultDate();

        // 查询条件
        final SampleEsQuery query = SampleEsQuery.builder()
                .pageNo(NumberUtils.INTEGER_ONE)
                .pageSize(esConfig.getPageSize())
                .startCreateDate(queryVo.getStartDate())
                .endCreateDate(queryVo.getEndDate())
                .build();

        final Long inputTerId = queryVo.getInputTerId();
        if (Objects.nonNull(inputTerId)) {
            query.setCreatorIds(Set.of(inputTerId));
        }

        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Set.of(queryVo.getHspOrgId()));
        }

        // 返回结果
        Map<Long, Map<PreprocessItemStatisticsVo, AtomicInteger>> preprocessItemStatisticsMap = new HashMap<>(20);

        final List<? extends BaseSampleEsModelDto> datas = elasticSearchSampleService.selectSamples(query);
        // 处理数据
        for (final BaseSampleEsModelDto data : datas) {
            final Long hspOrgId = data.getHspOrgId();
            final String hspOrgName = data.getHspOrgName();
            final List<BaseSampleEsModelDto.TestItem> testItems = CollUtil.defaultIfEmpty(data.getTestItems(), Collections.emptyList());

            // 防止下面 put、compute 时出现空指针
            if (BooleanUtils.isNotTrue(preprocessItemStatisticsMap.containsKey(hspOrgId))) {
                final HashMap<PreprocessItemStatisticsVo, AtomicInteger> itemMap = new HashMap<>(20);
                preprocessItemStatisticsMap.putIfAbsent(hspOrgId, itemMap);
            }

            for (final BaseSampleEsModelDto.TestItem testItem : testItems) {

                final Map<PreprocessItemStatisticsVo, AtomicInteger> itemStatisticsMap = preprocessItemStatisticsMap.get(hspOrgId);

                final PreprocessItemStatisticsVo vo = new PreprocessItemStatisticsVo(hspOrgId, hspOrgName, testItem.getTestItemId(), testItem.getTestItemName());

                // 如果存在则自增
                itemStatisticsMap.computeIfPresent(vo, (k, v) -> {
                    v.incrementAndGet();
                    return v;
                });

                // 否则新增
                itemStatisticsMap.putIfAbsent(vo, new AtomicInteger(1));
            }
        }

        // 总计
        AtomicInteger totalCount = new AtomicInteger();

        List<PreprocessItemVo> vos = new ArrayList<>();

        preprocessItemStatisticsMap.forEach((k, v) -> {
            v.forEach((k1, v1) -> {

                PreprocessItemVo vo = new PreprocessItemVo();
                vo.setHspOrgId(k1.getHspOrgId());
                vo.setHspOrgName(k1.getHspOrgName());
                vo.setTestItemId(k1.getTestItemId());
                vo.setTestItemName(k1.getTestItemName());
                vo.setCount(v1);

                totalCount.addAndGet(v1.get());
                vos.add(vo);
            });
        });

        PreprocessItemRespVo respVo = new PreprocessItemRespVo();
        respVo.setList(vos);
        respVo.setTotalCount(totalCount);
        return respVo;
    }


    /**
     * 前处理工作量统计-export
     */
    @PostMapping("/workload-export")
    public Object workloadExport(@RequestBody PreprocessWorkloadDataStatisticsQueryVo vo) throws IOException {
        final PreprocessWorkloadVo resultVo = workload(vo);

        final List<PreprocessWorkloadStatisticsVo> dataList = resultVo.getDataList();

        final File tempFile = File.createTempFile("preprocess-workload-export-", null);

        final List<PreprocessWorkloadStatisticsVo.TubeStatistics> columns =
                resultVo.getColumns().stream().sorted(Comparator.comparing(PreprocessWorkloadStatisticsVo.TubeStatistics::getTubeName))
                        .collect(Collectors.toList());

        try (final ExcelWriter writer = ExcelUtil.getBigWriter(); final FileOutputStream fos = new FileOutputStream(tempFile)) {
            LinkedList<Object> builder = new LinkedList<>(List.of(
                    "序号",
                    //
                    "送检机构",
                    //
                    "人数",
                    //
                    "条码数",
                    //
                    "项目数",
                    //
                    "手工输入样本数",
                    //
                    "补录样本数",
                    //
                    "签收样本数"
            ));

            // 动态列
            builder.addAll(columns.stream().map(PreprocessWorkloadStatisticsVo.TubeStatistics::getTubeName).collect(Collectors.toList()));

            // 写入行
            writer.writeRow(builder);

            //写入合计行
            dataList.add(resultVo.getAggregate());

            int index = 0;
            for (final PreprocessWorkloadStatisticsVo item : dataList) {
                builder.clear();

                // 序号
                index++;

                if (dataList.size() == index) {
                    builder.add("合计");
                } else {
                    builder.add(index);
                }

                builder.add(item.getHspOrgName());
                builder.add(item.getPeopleCount());
                builder.add(item.getBarcodeCount());
                builder.add(item.getTestItemCount());
                builder.add(item.getManualInputSampleCount());
                builder.add(item.getSupplementSampleCount());
                builder.add(item.getSignSampleCount());

                final Map<String, AtomicInteger> tubeStatisticsMap = item.getTubeStatisticsMap();

                // 动态列
                for (final PreprocessWorkloadStatisticsVo.TubeStatistics column : columns
                        .stream()
                        .sorted(Comparator.comparing(PreprocessWorkloadStatisticsVo.TubeStatistics::getTubeName))
                        .collect(Collectors.toList())) {
                    final String tubeCode = column.getTubeCode();

                    final int count = Optional.ofNullable(tubeStatisticsMap.get(tubeCode)).orElse(new AtomicInteger(0)).get();

                    builder.add(count);
                }

                writer.writeRow(builder);
            }
            builder.clear();

            writer.flush(fos);
        }

        return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("前处理工作量统计.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel")).body(new FileSystemResource(tempFile));
    }

    /**
     * 前处理工作量统计
     */
    @PostMapping("/workload")
    public PreprocessWorkloadVo workload(@RequestBody PreprocessWorkloadDataStatisticsQueryVo vo) {
        // 默认时间
        vo.defaultDate();

        // 查询条件
        final SampleEsQuery query = SampleEsQuery.builder()
                .pageNo(NumberUtils.INTEGER_ONE)
                .pageSize(esConfig.getPageSize())
                .startCreateDate(vo.getStartDate())
                .endCreateDate(vo.getEndDate())
                .build();

        final Long inputTerId = vo.getInputTerId();
        if (Objects.nonNull(inputTerId)) {
            query.setCreatorIds(Set.of(inputTerId));
        }

        if (Objects.nonNull(vo.getHspOrgId())) {
            query.setHspOrgIds(Set.of(vo.getHspOrgId()));
        }


        // 响应数据
        Map<Long, PreprocessWorkloadStatisticsVo> resultMap = Collections.synchronizedMap(new HashMap<>(20));


        // 人数 也就是申请单的数量 key: hspOrgId value: applyId 进行去重,为什么去重因为es中存储是按样本来的,一个申请单可能有多个样本
        Map<Long, Set<Long>> applyStatisticsMap = Collections.synchronizedMap(new HashMap<>(20));

        // 条码数量统计
        Map<Long, Set<PreprocessWorkloadStatisticsVo.BarcodeStatistics>> barcodeStatisticsMap = Collections.synchronizedMap(new HashMap<>(20));

        // 动态列
        Set<PreprocessWorkloadStatisticsVo.TubeStatistics> columns = Collections.synchronizedSet(new LinkedHashSet<>(40));

        // 管型统计 key: hspOrgId value: key1: tubeCode value1: applySampleId
        Map<Long, Map<String, Set<String>>> tubeStatisticsMap = new HashMap<>(40);

        final PreprocessWorkloadStatisticsVo aggregate = new PreprocessWorkloadStatisticsVo();
        final List<? extends BaseSampleEsModelDto> datas = elasticSearchSampleService.selectSamples(query);
        if (CollectionUtils.isEmpty(datas)) {
            return new PreprocessWorkloadVo(Set.of(), List.of(), aggregate);
        }

        // 未知管型计数
        AtomicInteger unknownTube = new AtomicInteger(1);

        // 处理数据
        for (final BaseSampleEsModelDto data : datas) {
            final List<BaseSampleEsModelDto.TestItem> testItems = CollUtil.defaultIfEmpty(data.getTestItems(), Collections.emptyList());

            final String source = data.getSource();
            final String barcode = data.getBarcode();
            final String tubeCode = data.getTubeCode();
            String tubeName = data.getTubeName();
            final Long hspOrgId = data.getHspOrgId();
            final Long applyId = data.getApplyId();

            // 不存在就创建
            PreprocessWorkloadStatisticsVo resultVo = resultMap.get(hspOrgId);
            if (Objects.isNull(resultVo)) {
                resultVo = new PreprocessWorkloadStatisticsVo();
                resultVo.setTestItemCount(new AtomicInteger(0));
                resultVo.setPeopleCount(new AtomicInteger(0));
                resultVo.setSupplementSampleCount(new AtomicInteger(0));
                resultVo.setManualInputSampleCount(new AtomicInteger(0));
                resultVo.setSignSampleCount(new AtomicInteger(0));
                resultVo.setHspOrgName(data.getHspOrgName());
                resultVo.setHspOrgId(data.getHspOrgId());

                resultMap.put(hspOrgId, resultVo);
            }

            // 人数
            applyStatisticsMap.computeIfPresent(hspOrgId, (k, v) -> {
                v.add(applyId);
                return v;
            });

            if (BooleanUtils.isNotTrue(applyStatisticsMap.containsKey(hspOrgId))) {
                final HashSet<Long> applyStatisticsSet = new HashSet<>();
                applyStatisticsSet.add(applyId);
                applyStatisticsMap.putIfAbsent(hspOrgId, Collections.synchronizedSet(applyStatisticsSet));
            }

            resultVo.getTestItemCount().addAndGet(testItems.size());


            // 样本数
            final PreprocessWorkloadStatisticsVo.BarcodeStatistics barcodeStatistics = new PreprocessWorkloadStatisticsVo.BarcodeStatistics(hspOrgId, barcode, source);
            barcodeStatisticsMap.computeIfPresent(hspOrgId, (k, v) -> {
                v.add(barcodeStatistics);
                return v;
            });

            if (BooleanUtils.isNotTrue(barcodeStatisticsMap.containsKey(hspOrgId))) {
                final HashSet<PreprocessWorkloadStatisticsVo.BarcodeStatistics> barcodeStatisticsSet = new HashSet<>();
                barcodeStatisticsSet.add(barcodeStatistics);
                barcodeStatisticsMap.putIfAbsent(hspOrgId, barcodeStatisticsSet);
            }


            if (StringUtils.isBlank(tubeName)) {
                tubeName = String.format("未知管型-%s", unknownTube.getAndIncrement());
            }

            final PreprocessWorkloadStatisticsVo.TubeStatistics tube =
                    new PreprocessWorkloadStatisticsVo.TubeStatistics(tubeCode, tubeName);

            // 管型
            tubeStatisticsMap.computeIfPresent(hspOrgId, (k, v) -> {

                v.computeIfPresent(tubeCode, (k1, v1) -> {
                    v1.add(data.getBarcode());
                    return v1;
                });

                final HashSet<String> set = new HashSet<>();
                set.add(data.getBarcode());
                v.putIfAbsent(tubeCode, set);

                return v;
            });

            if (BooleanUtils.isNotTrue(tubeStatisticsMap.containsKey(hspOrgId))) {
                final HashMap<String, Set<String>> tubeStatisticsTempMap = new HashMap<>(20);
                final HashSet<String> set = new HashSet<>();
                set.add(data.getBarcode());
                tubeStatisticsTempMap.put(tubeCode, set);
                tubeStatisticsMap.putIfAbsent(hspOrgId, Collections.synchronizedMap(tubeStatisticsTempMap));
            }

            // 动态管型列
            columns.add(tube);

        }


        // 汇集数据
        for (final Map.Entry<Long, PreprocessWorkloadStatisticsVo> entry : resultMap.entrySet()) {
            final Long k = entry.getKey();
            final PreprocessWorkloadStatisticsVo v = entry.getValue();

            // 人数
            v.setPeopleCount(new AtomicInteger(applyStatisticsMap.get(k).size()));

            // 样本数
            final Collection<PreprocessWorkloadStatisticsVo.BarcodeStatistics> barcodeStatistics = CollUtil.defaultIfEmpty(barcodeStatisticsMap.get(k), Collections.emptyList());

            final long supplementary = barcodeStatistics
                    .stream()
                    .map(PreprocessWorkloadStatisticsVo.BarcodeStatistics::getSource)
                    .filter(f -> StringUtils.equals(f, ApplySourceEnum.SUPPLEMENTARY.name()))
                    .count();

            final long manual = barcodeStatistics
                    .stream()
                    .map(PreprocessWorkloadStatisticsVo.BarcodeStatistics::getSource)
                    .filter(f -> StringUtils.equals(f, ApplySourceEnum.MANUAL.name()))
                    .count();

            final long his = barcodeStatistics
                    .stream()
                    .map(PreprocessWorkloadStatisticsVo.BarcodeStatistics::getSource)
                    .filter(f -> !StringUtils.equalsAny(f, ApplySourceEnum.MANUAL.name(), ApplySourceEnum.SUPPLEMENTARY.name()))
                    .count();

            v.setManualInputSampleCount(new AtomicInteger((int) manual));
            v.setSupplementSampleCount(new AtomicInteger((int) supplementary));
            v.setSignSampleCount(new AtomicInteger((int) his));


            // 管型
            v.setTubeStatisticsMap(tubeStatisticsMap.get(k).entrySet()
                    .stream().collect(Collectors.toMap(
                            Map.Entry::getKey,
                            item -> new AtomicInteger(ObjectUtils.defaultIfNull(item.getValue(), new HashSet<>()).size())
                            , (v1, v2) -> v1, LinkedHashMap::new)));

        }


        // 送检机构总数
        aggregate.setHspOrgName(String.valueOf(resultMap.size()));

        // 人数总数
        resultMap.values().stream().map(PreprocessWorkloadStatisticsVo::getPeopleCount)
                .map(AtomicInteger::get).reduce(Integer::sum)
                .map(AtomicInteger::new).ifPresent(aggregate::setPeopleCount);

        // 项目数量
        resultMap.values().stream().map(PreprocessWorkloadStatisticsVo::getTestItemCount)
                .map(AtomicInteger::get).reduce(Integer::sum)
                .map(AtomicInteger::new).ifPresent(aggregate::setTestItemCount);

        // 手工输入样本数
        resultMap.values().stream().map(PreprocessWorkloadStatisticsVo::getManualInputSampleCount)
                .map(AtomicInteger::get).reduce(Integer::sum)
                .map(AtomicInteger::new).ifPresent(aggregate::setManualInputSampleCount);

        // 补录样本数
        resultMap.values().stream().map(PreprocessWorkloadStatisticsVo::getSupplementSampleCount)
                .map(AtomicInteger::get).reduce(Integer::sum)
                .map(AtomicInteger::new).ifPresent(aggregate::setSupplementSampleCount);

        // 签收样本数
        resultMap.values().stream().map(PreprocessWorkloadStatisticsVo::getSignSampleCount)
                .map(AtomicInteger::get).reduce(Integer::sum)
                .map(AtomicInteger::new).ifPresent(aggregate::setSignSampleCount);

        // 管型
        final Map<String, AtomicInteger> tubeTotalMap = resultMap.values()
                .stream()
                .map(PreprocessWorkloadStatisticsVo::getTubeStatisticsMap)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> new AtomicInteger(a.get() + b.get())));

        aggregate.setTubeStatisticsMap(tubeTotalMap);

        return new PreprocessWorkloadVo(
                columns.stream()
                        .sorted(Comparator.comparing(PreprocessWorkloadStatisticsVo.TubeStatistics::getTubeName))
                        .collect(Collectors.toCollection(LinkedHashSet::new)),
                resultMap
                        .values()
                        .stream()
                        .sorted(Comparator.comparing(sort -> PinyinUtil.getFirstLetter(sort.getHspOrgName(), StringUtils.EMPTY)))
                        .collect(Collectors.toList()),
                aggregate);

    }

    //endregion ------------------------------ 前处理工作量统计 ------------------------------


}
