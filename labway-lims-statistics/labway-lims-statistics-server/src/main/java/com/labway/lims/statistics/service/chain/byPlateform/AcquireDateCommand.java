package com.labway.lims.statistics.service.chain.byPlateform;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.CombinePackageInfoDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ItemPriceBaseFinanceDetailService;
import com.labway.lims.base.api.vo.QueryCombinePackageListTestItemsVo;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.controller.FinanceStatisticsController;
import com.labway.lims.statistics.service.FinancialManagementService;
import com.labway.lims.statistics.vo.FinanceStatisticsQueryVo;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Component
public class AcquireDateCommand implements Command {
    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @Resource
    private EsConfig esConfig;
    @DubboReference
    private ItemPriceBaseFinanceDetailService itemPriceBaseFinanceDetailService;
    @Resource
    private FinancialManagementService financialManagementService;

    @Override
    public boolean execute(Context c) {
        final PlatformStatisticsContext from = PlatformStatisticsContext.from(c);
        final FinanceStatisticsQueryVo query = from.getFinanceStatisticsQuery();

        // 开票名称
        from.put(PlatformStatisticsContext.CUSTOMER_NAME_TYPE_ENUM, getCustomerNameType(query.getInvoiceType()));

        // 送检机构
        final List<HspOrganizationDto> hspOrganizations = this.acquireHspOrg(query.getHspOrgIds());
        from.put(PlatformStatisticsContext.HSP_ORG, hspOrganizations);

        // 将查询条件 转换为es查询条件
        SampleEsQuery esDto = new SampleEsQuery();
        final boolean financialMonthBool = this.acquireEsQueryDate(esDto, from);

        // ES数据
        final List<BaseSampleEsModelDto> baseSampleEsModelDtos = this.acquireBaseESData(esDto, !financialMonthBool);
        from.put(PlatformStatisticsContext.BASE_SAMPLE_ES_DATA, baseSampleEsModelDtos);

        // ES数据  查询 终止收费的检验项目信息 不一定是审核的
        final List<BaseSampleEsModelDto> stopTestChargeEsModelDtos = this.acquireStopEsData(esDto);
        from.put(PlatformStatisticsContext.STOP_SAMPLE_ES_DATA, stopTestChargeEsModelDtos);

        // ES数据  查询 病理检验 已经一次分拣
        final List<BaseSampleEsModelDto> pathologyEsModelDtos = this.acquirePathologyEsData(esDto);
        from.put(PlatformStatisticsContext.PATHOLOGY_SAMPLE_ES_DATA, pathologyEsModelDtos);

        // 财务套餐
        final Map<Long, List<QueryCombinePackageListTestItemsVo>> combinePackageMap = this.acquireCombinePackageMap();
        from.put(PlatformStatisticsContext.COMBINE_PACKAGE_MAP, combinePackageMap);

        return CONTINUE_PROCESSING;
    }


    /**
     * 去除掉所有无效的套餐
     * 基准包下会有多个套餐，  这个套餐是属于不同的送检机构，
     * 1. 先过滤基准包下对应的送检机构
     * 2. 再过滤同一个基准包下  不同财务套餐下面的检验项目不能重复， 比如：
     * 现在有三个套餐  套餐1 ： A B 两个项目  套餐2： B C 两个项目   套餐3： X Y 两个项目
     * 因为两个 套餐1 和 套餐2 都有 B 项目  所以两个套餐都被过滤掉了， 只剩下 套餐3
     */
    public Map<Long, List<QueryCombinePackageListTestItemsVo>> acquireCombinePackageMap() {
//        return itemPriceBaseFinanceDetailService.selectBaseFinanceDetailAndTestItemMap();
        final Map<Long, List<QueryCombinePackageListTestItemsVo>> combinePackageMap = itemPriceBaseFinanceDetailService.selectBaseFinanceDetailAndTestItemMap();

        // 根据 基准包id（packageId） 循环
        return combinePackageMap.entrySet().parallelStream().map(e -> {

            final Long packageId = e.getKey();
            final List<QueryCombinePackageListTestItemsVo> combinePackageList = e.getValue()
                    .stream().filter(combinePackage -> Objects.equals(combinePackage.getEnable(), YesOrNoEnum.YES.getCode()))
                    .collect(Collectors.toList());

            final List<QueryCombinePackageListTestItemsVo> tempList = new ArrayList<>();

            // 根据送检机构分组
            combinePackageList.stream()
                    .flatMap(combinePackage -> {
                        final List<CombinePackageInfoDto.HspOrgInfo> hspOrgList = combinePackage.getHspOrgList();
                        return hspOrgList.stream().map(hspOrg -> {
                            final QueryCombinePackageListTestItemsVo bean = BeanUtil.toBean(combinePackage, QueryCombinePackageListTestItemsVo.class);
                            bean.setHspOrgId(hspOrg.getHspOrgId());
                            bean.setHspOrgCode(hspOrg.getHspOrgCode());
                            bean.setHspOrgName(hspOrg.getHspOrgName());
                            return bean;
                        });
                    })
                    .collect(Collectors.groupingBy(QueryCombinePackageListTestItemsVo::getHspOrgId))
                    .forEach((hspOrgId, value) -> {

//                        final Set<Long> repetitionTestItemIds = getRepetitionTestItemIds(value);

                        // 同一个送检机构下不同财务套餐 分别判断是否存在重复套餐
                        value.forEach(testCombinePackage -> {
                            // 当前机构下 当前财务套餐 的 检验项目id
//                            final Set<Long> testItemIds = testCombinePackage.getTestItemDtoList().stream().map(TestItemDto::getTestItemId).collect(Collectors.toSet());
//                            if (!testItemIds.removeAll(repetitionTestItemIds)) {
                                // 如果移除操作成功执行，则removeAll()方法会返回true，否则返回false。
                                tempList.add(testCombinePackage);
//                            }
                        });

                    });
            return Map.entry(packageId, tempList);
        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    }

    private Set<Long> getRepetitionTestItemIds(List<QueryCombinePackageListTestItemsVo> value) {
        // 同一个送检机构下不同财务套餐中重复的检验项目id
        return value.stream()
                // 过滤检验项目为空的
                .filter(e -> CollectionUtils.isNotEmpty(e.getTestItemDtoList()))
                // 收集所有财务套餐下的检验项目
                .flatMap(combinePackage -> combinePackage.getTestItemDtoList().stream())
                // 检验项目id， 出现的数量
                .collect(Collectors.groupingBy(TestItemDto::getTestItemId, Collectors.counting()))
                .entrySet().stream()
                // 数量 > 1 的
                .filter(testItemById -> testItemById.getValue() > NumberUtils.INTEGER_ONE)
                // 获取检验项目id
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }

    private boolean acquireEsQueryDate(SampleEsQuery esDto, PlatformStatisticsContext from) {
        final FinanceStatisticsQueryVo query = from.getFinanceStatisticsQuery();

        final List<HspOrganizationDto> hspOrgList = from.getHspOrgList();

        final Set<Long> hspOrgIds = hspOrgList.stream().map(HspOrganizationDto::getHspOrgId).collect(Collectors.toSet());

        esDto.setPageSize(esConfig.getPageSize());
        esDto.setPageNo(NumberUtils.INTEGER_ONE);
        // 1.0.6.6需求改为不管该样本是否已经审核都进行查询
        // esDto.setIsAudit(YesOrNoEnum.YES.getCode());
        esDto.setHspOrgIds(hspOrgIds);
        // 专业组过滤
        if (CollectionUtils.isNotEmpty(query.getGroupIds())) {
            esDto.setGroupIds(query.getGroupIds());
        }

        // 时间
        final boolean financialMonthBool = Objects.equals(query.getDateType(), FinanceStatisticsController.DateTypeEnum.SEND_DATE.getCode());
        if (financialMonthBool) {
            esDto.setStartCreateDate(query.getStartDate());
            esDto.setEndCreateDate(query.getEndDate());
        } else {
            esDto.setStartFinalCheckOrCreateDate(query.getStartDate());
            esDto.setEndFinalCheckOrCreateDate(query.getEndDate());
        }
        return financialMonthBool;
    }


    public List<HspOrganizationDto> acquireHspOrg(Set<Long> hspOrgIds) {

        List<HspOrganizationDto> hspOrganizations;
        if (CollectionUtils.isEmpty(hspOrgIds)) {
            hspOrganizations = hspOrganizationService.selectAll().stream()
                    .filter(f -> Objects.equals(f.getIsExport(), YesOrNoEnum.NO.getCode()))
                    .collect(Collectors.toList());
        } else {
            hspOrganizations = hspOrganizationService.selectByHspOrgIds(hspOrgIds).stream()
                    .filter(f -> Objects.equals(f.getIsExport(), YesOrNoEnum.NO.getCode()))
                    .collect(Collectors.toList());
            Set<Long> selectHspOrgIds = hspOrganizations.stream().map(HspOrganizationDto::getHspOrgId).collect(Collectors.toSet());
            hspOrgIds.stream().filter(e -> !selectHspOrgIds.contains(e)).findAny().ifPresent(e -> {
                throw new IllegalArgumentException(String.format("机构 [%s] 不存在", e));
            });
        }
        return hspOrganizations;
    }

    public List<BaseSampleEsModelDto> acquireBaseESData(SampleEsQuery esDto, boolean financialMonth) {

        // 所有es 结构数据
        return financialManagementService.selectSamples(esDto, financialMonth);

    }

    public List<BaseSampleEsModelDto> acquireStopEsData(SampleEsQuery esDto) {

        // 查询 终止收费的检验项目信息 不一定是审核的
        SampleEsQuery stopStatusEsQuery = JSON.parseObject(JSON.toJSONString(esDto), SampleEsQuery.class);
        stopStatusEsQuery.setIsAudit(null);
        stopStatusEsQuery.setStopStatus(Set.of(StopTestStatus.STOP_TEST_CHARGE.getCode()));
        return elasticSearchSampleService.selectSamples(stopStatusEsQuery);

    }


    public List<BaseSampleEsModelDto> acquirePathologyEsData(SampleEsQuery esDto) {

        // 查询 病理检验 已经一次分拣
        SampleEsQuery pathologyEsQuery = JSON.parseObject(JSON.toJSONString(esDto), SampleEsQuery.class);
        pathologyEsQuery.setIsAudit(null);
        pathologyEsQuery.setItemTypes(Collections.singleton(ItemTypeEnum.PATHOLOGY.name()));
        // 1.0.6.6需求改为病理组的样本只要创建就纳入统计
        // pathologyEsQuery.setIsOnePick(YesOrNoEnum.YES.getCode());
        return elasticSearchSampleService.selectSamples(pathologyEsQuery);

    }


    /**
     * 客户名称 类别
     */
    public CustomerNameTypeEnum getCustomerNameType(Integer customerNameType) {
        if (Objects.isNull(customerNameType)) {
            // 默认开票名称
            return CustomerNameTypeEnum.INVOICE_NAME;
        }
        CustomerNameTypeEnum customerNameTypeEnum = null;
        if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME.getCode())) {
            customerNameTypeEnum = CustomerNameTypeEnum.INVOICE_NAME;
        } else if (Objects.equals(customerNameType, CustomerNameTypeEnum.ORG_NAME.getCode())) {
            customerNameTypeEnum = CustomerNameTypeEnum.ORG_NAME;
        }
        return customerNameTypeEnum;
    }

}
