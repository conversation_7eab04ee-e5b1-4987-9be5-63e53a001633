server:
  port: 12323
  tomcat:
    threads:
      max: 500

dubbo:
  provider:
    filter: loginUserProvider
  consumer:
    filter: loginUserConsumer

spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 128MB
      max-request-size: 128MB
  datasource:
    driver-class-name: ${pgsql.driver}
    url: jdbc:postgresql://${pgsql.master.host}:${pgsql.master.port}/${pgsql.db}
    password: ${pgsql.password}
    type: ${pgsql.datasource}
    username: ${pgsql.username}
    hikari:
      minimum-idle: 5
  mvc:
    async:
      request-timeout: 600000

pgsql:
  datasource: com.zaxxer.hikari.HikariDataSource
  db: labway-lims
  driver: org.postgresql.Driver

business:
  # ES 高级查询，字段需要keyword转换
  es-keyword-fields:
    - "applyTypeCode"
    - "originalOrgName"

statistics:
  by-send-doctor:
    version: 2.0
  by-plat:
    version: 2.0