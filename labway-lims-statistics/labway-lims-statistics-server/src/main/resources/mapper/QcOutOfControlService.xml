<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.statistics.mapper.SampleArchiveMapper">

    <select id="selectSampleArchivePageByWrapper"
            parameterType="com.labway.lims.statistics.dto.SampleArchiveQueryDto"
            resultType="com.labway.lims.statistics.dto.SampleArchiveDto">
        select
        rack_logic_space."status",
        rack_logic_space.is_delete,
        tr.archive_store_name,
        tr.refrigerator_name,
        tr2.rack_code,
        tr2."row" as rack_row,
        tr2."column" as rack_column,
        rack_logic_space.rack_logic_space_id,
        rack_logic_space."row",
        rack_logic_space."column",
        apply_sample.apply_sample_id,
        apply_sample.barcode,
        apply.patient_name,
        apply.patient_sex,
        apply.patient_age,
        apply.patient_subage,
        apply.patient_subage_unit,
        <!-- 门诊号/住院号 -->
        apply.patient_visit_card,
        apply_sample_item.test_item_code,
        apply_sample_item.test_item_name,
        rack_logic_space.creator_id,
        rack_logic_space.creator_name,
        rack_logic_space.create_date
        from tb_rack_logic_space as rack_logic_space <!-- 逻辑试管架占用 -->
        inner join tb_apply_sample apply_sample <!-- 条码号 -->
            on rack_logic_space.apply_sample_id = apply_sample.apply_sample_id
            and apply_sample.is_archive = 1 <!-- 已归档 -->
        inner join tb_apply apply <!-- 病人信息 -->
            on apply_sample.apply_id = apply.apply_id
        inner join tb_apply_sample_item apply_sample_item <!-- 检验项目 -->
            on apply_sample.apply_sample_id = apply_sample_item.apply_sample_id
        inner join tb_rack_archive tra <!-- 试管架归档 -->
            on rack_logic_space.rack_id = tra.rack_id
            and rack_logic_space.rack_logic_id = tra.rack_logic_id
            and tra.is_delete = 0
        inner join tb_rack tr2  <!-- 试管架 -->
            on rack_logic_space.rack_id = tr2.rack_id
            and tr2.is_delete = 0
        inner join tb_refrigerator tr <!-- 冰箱 -->
            on tra.refrigerator_id = tr.refrigerator_id
            and tr.is_delete = 0
        ${ew.customSqlSegment}
    </select>

    <select id="selectSampleArchive"
            parameterType="com.labway.lims.statistics.dto.SampleArchiveQueryDto"
            resultType="com.labway.lims.statistics.dto.SampleArchiveDto">
        select
            rack_logic_space."status",
            rack_logic_space.is_delete,
            tr.archive_store_name,
            tr.refrigerator_name,
            tr2.rack_code,
            tr2.rack_name,
            tr2."row" as rack_row,
            tr2."column" as rack_column,
            rack_logic_space.rack_logic_space_id,
            rack_logic_space."row",
            rack_logic_space."column",
            apply_sample.apply_sample_id,
            apply_sample.barcode,
            apply.patient_name,
            apply.patient_sex,
            apply.patient_age,
            apply.patient_subage,
            apply.patient_subage_unit,
            <!-- 门诊号/住院号 -->
            apply.patient_visit_card,
            apply_sample_item.test_item_code,
            apply_sample_item.test_item_name,
            rack_logic_space.creator_id,
            rack_logic_space.creator_name,
            rack_logic_space.create_date
        from tb_rack_logic_space as rack_logic_space <!-- 逻辑试管架占用 -->
        inner join tb_apply_sample apply_sample <!-- 条码号 -->
            on rack_logic_space.apply_sample_id = apply_sample.apply_sample_id
            and apply_sample.is_archive = 1 <!-- 已归档 -->
        inner join tb_apply apply <!-- 病人信息 -->
            on apply_sample.apply_id = apply.apply_id
        inner join (select apply_sample_id,
                           max(group_id)                   group_id,
                           string_agg(test_item_code, ',') test_item_code,
                           string_agg(test_item_name, ',') test_item_name
                    from tb_apply_sample_item
                        <if test="param.groupId != null and param.groupId != ''">
                            where group_id = #{param.groupId}
                        </if>
                    group by apply_sample_id) apply_sample_item
            on apply_sample.apply_sample_id = apply_sample_item.apply_sample_id
        inner join tb_rack_archive tra <!-- 试管架归档 -->
            on rack_logic_space.rack_id = tra.rack_id
            and rack_logic_space.rack_logic_id = tra.rack_logic_id
            and tra.is_delete = 0
        inner join tb_rack tr2  <!-- 试管架 -->
            on rack_logic_space.rack_id = tr2.rack_id
            and tr2.is_delete = 0
        inner join tb_refrigerator tr <!-- 冰箱 -->
            on tra.refrigerator_id = tr.refrigerator_id
            and tr.is_delete = 0
        where rack_logic_space.is_delete = 0
        and apply_sample.is_archive = 1
        <!--<if test="param.searchAfter != null">
            <![CDATA[
            and rack_logic_space.rack_logic_space_id > #{param.searchAfter}
            ]]>
        </if>-->
        <if test="param.startCreateDate != null">
            <![CDATA[
            and rack_logic_space.create_date > #{param.startCreateDate,jdbcType=TIMESTAMP}
            ]]>
        </if>
        <if test="param.endCreateDate != null">
            <![CDATA[
            and rack_logic_space.create_date < #{param.endCreateDate,jdbcType=TIMESTAMP}
            ]]>
        </if>
        <if test="param.creatorId != null and param.creatorId != ''">
            and rack_logic_space.creator_id = #{param.creatorId}
        </if>
        <if test="param.barcode != null and param.barcode != ''">
            and apply_sample.barcode = #{param.barcode}
        </if>
        <if test="param.testItemCode != null and param.testItemCode != ''">
            and position(#{param.testItemCode} in apply_sample_item.test_item_code) > 0
        </if>
        <if test="param.patientName != null and param.patientName != ''">
            and apply.patient_name like concat('%', #{param.patientName},'%')
        </if>
        <if test="param.patientVisitCard != null and param.patientVisitCard != ''">
            and apply.patient_visit_card like concat('%', #{param.patientVisitCard},'%')
        </if>
        order by rack_logic_space.create_date
    </select>

    <select id="selectSampleArchivePage"
            parameterType="com.labway.lims.statistics.dto.SampleArchiveQueryDto"
            resultType="com.labway.lims.statistics.dto.SampleArchiveDto">
        select
            rack_logic_space."status",
            rack_logic_space.is_delete,
            rack_logic_space.rack_logic_space_id,
            rack_logic_space."row",
            rack_logic_space."column",
            rack_logic_space.apply_sample_id,
            rack_logic_space.creator_id,
            rack_logic_space.creator_name,
            rack_logic_space.create_date,
            tr.archive_store_name,
            tr.refrigerator_name,
            tr2.rack_code,
            tr2."row" as rack_row,
            tr2."column" as rack_column
        from tb_rack_logic_space as rack_logic_space <!-- 逻辑试管架占用 -->
        inner join tb_rack_archive tra <!-- 试管架归档 -->
            on rack_logic_space.rack_id = tra.rack_id
            and rack_logic_space.rack_logic_id = tra.rack_logic_id
            and tra.is_delete = 0
        inner join tb_rack tr2  <!-- 试管架 -->
            on rack_logic_space.rack_id = tr2.rack_id
            and tr2.is_delete = 0
        inner join tb_refrigerator tr <!-- 冰箱 -->
            on tra.refrigerator_id = tr.refrigerator_id
            and tr.is_delete = 0
        ${ew.customSqlSegment}
    </select>


    <select id="selectSampleArchiveByBarcode" resultType="com.labway.lims.statistics.dto.SampleArchiveDto">
        select rack_logic_space."status",
               rack_logic_space.is_delete,
               tr.archive_store_name,
               tr.refrigerator_name,
               tr2.rack_code,
               tr2.rack_name,
               tr2."row"    as rack_row,
               tr2."column" as rack_column,
               rack_logic_space.rack_logic_space_id,
               rack_logic_space."row",
               rack_logic_space."column",
               apply_sample.apply_sample_id,
               apply_sample.barcode,
               apply.patient_name,
               apply.patient_sex,
               apply.patient_age,
               apply.patient_subage,
               apply.patient_subage_unit,
               apply.patient_visit_card,
               apply_sample_item.test_item_code,
               apply_sample_item.test_item_name,
               rack_logic_space.creator_id,
               rack_logic_space.creator_name,
               rack_logic_space.create_date
        from tb_rack_logic_space as rack_logic_space
                 inner join tb_apply_sample apply_sample
                            on rack_logic_space.apply_sample_id = apply_sample.apply_sample_id and apply_sample.is_archive = 1
                 inner join tb_apply apply
                            on apply_sample.apply_id = apply.apply_id
                 inner join (select apply_sample_id,
                                    max(group_id)                   group_id,
                                    string_agg(test_item_code, ',') test_item_code,
                                    string_agg(test_item_name, ',') test_item_name
                             from tb_apply_sample_item
                             <if test ="groupId != null and groupId != ''">
                                 where group_id = #{groupId}
                             </if>
                             group by apply_sample_id) apply_sample_item
                            on apply_sample.apply_sample_id = apply_sample_item.apply_sample_id
                 inner join tb_rack_archive tra
                            on rack_logic_space.rack_id = tra.rack_id
                                and rack_logic_space.rack_logic_id = tra.rack_logic_id
                                and tra.is_delete = 0
                 inner join tb_rack tr2
                            on rack_logic_space.rack_id = tr2.rack_id
                                and tr2.is_delete = 0
                 inner join tb_refrigerator tr
                            on tra.refrigerator_id = tr.refrigerator_id
                                and tr.is_delete = 0
        where rack_logic_space.is_delete = 0
          and apply_sample.is_archive = 1
          and apply_sample.barcode = #{barcode}
    </select>

</mapper>
