package com.labway.lims.statistics.service.impl;

import cn.hutool.core.date.DateUtil;
import com.labway.lims.statistics.api.client.SampleStatisticsService;
import com.labway.lims.statistics.vo.SampleStatisticsQueryVo;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 * SampleStatisticsServiceImplTest
 * 描述信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 16:27
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class SampleStatisticsServiceImplTest {

    @Resource
    private SampleStatisticsService sampleStatisticsService;


    @Test
    void sampleStatistics() {
        Date date = new Date();
        SampleStatisticsQueryVo statisticsQueryVo = new SampleStatisticsQueryVo();
        statisticsQueryVo.setSignDateStart(DateUtil.parseDate("2023-07-27"));
        statisticsQueryVo.setSignDateEnd(DateUtil.endOfDay(date));
        sampleStatisticsService.selectSampleStatistics(statisticsQueryVo);
    }

    @Test
    void sampleTestItemStatistics() {
    }

    @Test
    void sampleReportItemStatistics() {
    }
}