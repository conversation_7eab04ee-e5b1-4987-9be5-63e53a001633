package com.labway.lims.statistics.service.impl;

import cn.hutool.core.date.DateUtil;
import com.labway.lims.api.enums.apply.AdvanceQueryConditionEnum;
import com.labway.lims.apply.api.dto.SampleEsQueryAdvanced;
import com.labway.lims.statistics.service.ApplySampleService;
import com.labway.lims.statistics.vo.ApplySampleQueryVo;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * ApplySampleServiceImplTest
 * 描述信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/8 16:31
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class ApplySampleServiceImplTest {

    @Resource
    private ApplySampleService applySampleService;

    @Test
    void selectApplySample() {
        Date date = new Date();

        ApplySampleQueryVo queryVo = new ApplySampleQueryVo();

        SampleEsQueryAdvanced esQueryDto = new SampleEsQueryAdvanced();
        esQueryDto.setStartSignDate(DateUtil.parseDate("2023-08-06"));
        esQueryDto.setEndSignDate(DateUtil.endOfDay(date));
        esQueryDto.setConditions(List.of(
                /*new SampleEsQueryAdvanced.FieldCondition() {{
                    setField("patientName");
                    setCondition(AdvanceQueryConditionEnum.LIKE.name());
                    setValue("院感,IT8000");
                }},*/
                new SampleEsQueryAdvanced.FieldCondition() {{
                    setField("testItems.testItemName");
                    setCondition(AdvanceQueryConditionEnum.LIKE.name());
                    setValue("糖类抗原,血脂");
                }},
                new SampleEsQueryAdvanced.FieldCondition() {{
                    setField("applySampleId");
                    setCondition(AdvanceQueryConditionEnum.EQ.name());
                    setValue("166103729439694855，166110648952279056,166409923626516835");
                }},
                new SampleEsQueryAdvanced.FieldCondition() {{
                    setField("");
                    setCondition(AdvanceQueryConditionEnum.NE.name());
                    setValue("");
                }}
        ));
//        queryVo.setAdvanceQuery(JSON.toJSONString(esQueryDto));

//        Object result = applySampleService.selectApplySample(queryVo);


    }
}