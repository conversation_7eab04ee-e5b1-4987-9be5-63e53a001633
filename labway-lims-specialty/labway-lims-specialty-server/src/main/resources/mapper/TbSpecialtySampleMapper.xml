<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.specialty.mapper.SpecialtySampleMapper">

    <update id="updateBySpecialtySampleIds">
        update tb_specialty_sample
        <set>
            <if test="specialtySampleDto.oneCheckerId != null">
                one_checker_id = #{specialtySampleDto.oneCheckerId},
            </if>
            <if test="specialtySampleDto.oneCheckerName != null">
                one_checker_name = #{specialtySampleDto.oneCheckerName},
            </if>
            <if test="specialtySampleDto.oneCheckDate != null">
                one_check_date = #{specialtySampleDto.oneCheckDate},
            </if>
            <if test="specialtySampleDto.twoCheckerId != null">
                two_checker_id = #{specialtySampleDto.twoCheckerId},
            </if>
            <if test="specialtySampleDto.twoCheckerName != null">
                two_checker_name = #{specialtySampleDto.twoCheckerName},
            </if>
            <if test="specialtySampleDto.twoCheckDate != null">
                two_check_date = #{specialtySampleDto.twoCheckDate},
            </if>
        </set>
        where specialty_sample_id in
        <foreach collection="specialtySampleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
