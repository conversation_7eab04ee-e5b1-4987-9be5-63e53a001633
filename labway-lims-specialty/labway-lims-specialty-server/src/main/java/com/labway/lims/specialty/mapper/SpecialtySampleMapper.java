package com.labway.lims.specialty.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.model.TbSpecialtySample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 特检样本 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface SpecialtySampleMapper extends BaseMapper<TbSpecialtySample> {

    /**
     * 根据ID批量修改
     */
    int updateBySpecialtySampleIds(@Param("specialtySampleDto") SpecialtySampleDto specialtySampleDto,
        @Param("specialtySampleIds") Collection<Long> specialtySampleIds);
}
