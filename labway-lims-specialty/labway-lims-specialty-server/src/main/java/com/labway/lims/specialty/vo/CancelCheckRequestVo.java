package com.labway.lims.specialty.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 取消一审、二审 请求参数
 * 
 * <AUTHOR>
 * @since 2023/4/25 10:04
 */
@Getter
@Setter
public class CancelCheckRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 特检样本id
     */
    private Long specialtySampleId;

    /**
     * 一审人、二审人工号
     */
    private String username;

    /**
     * 一审人、二审人密码
     */
    private String password;
}