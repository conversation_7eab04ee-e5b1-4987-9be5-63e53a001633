package com.labway.lims.specialty.service.chain.check.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 特检样本 二审 修改样本状态、二审人
 * 
 * <AUTHOR>
 * @since 2023/5/4 10:55
 */
@Slf4j
@Component
public class SpecialtyTwoCheckUpdateStatusDataCommand implements Command {

    @DubboReference
    private ApplySampleService applySampleService;

    @Resource
    private SpecialtySampleService specialtySampleService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SpecialtyTwoCheckContext from = SpecialtyTwoCheckContext.from(context);
        final LoginUserHandler.User user = from.getUser();
        final List<SpecialtySampleDto> specialtySampleList = from.getSpecialtySampleList();
        final Map<Long, String> applySampleIdAndReportNoMap = from.getApplySampleIdAndReportNoMap();

        // 修改遗传样本二审人、二审时间
        Set<Long> specialtySampleIdList =
            specialtySampleList.stream().map(SpecialtySampleDto::getSpecialtySampleId).collect(Collectors.toSet());

        SpecialtySampleDto updateSpecialtySampleDto = new SpecialtySampleDto();
        updateSpecialtySampleDto.setTwoCheckerId(user.getUserId());
        updateSpecialtySampleDto.setTwoCheckerName(user.getNickname());
        updateSpecialtySampleDto.setTwoCheckDate(new Date());

        specialtySampleService.updateBySpecialtySampleIds(updateSpecialtySampleDto, specialtySampleIdList);

        // 修改 申请单 样本状态为已审 并写入报告编号
        final List<ApplySampleDto> updateApplySampleList = specialtySampleList.stream()
                .map(e -> {
                    ApplySampleDto updateApplySampleDto = new ApplySampleDto();
                    updateApplySampleDto.setApplySampleId(e.getApplySampleId());
                    updateApplySampleDto.setStatus(SampleStatusEnum.AUDIT.getCode());
                    updateApplySampleDto.setReportNo(applySampleIdAndReportNoMap.get(e.getApplySampleId()));
                    return updateApplySampleDto;
                })
                .collect(Collectors.toList());
        applySampleService.updateBatchByApplySampleIds(updateApplySampleList);

        return CONTINUE_PROCESSING;
    }
}
