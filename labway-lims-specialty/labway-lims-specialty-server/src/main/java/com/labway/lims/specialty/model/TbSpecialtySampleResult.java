package com.labway.lims.specialty.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 特检样本结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_specialty_sample_result")
public class TbSpecialtySampleResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    private Long specialtySampleResultId;

    /**
     * 申请单ID
     */
    private Long applyId;


    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 特检样本ID
     */
    private Long specialtySampleId;

    /**
     * 特检结果
     * @see com.labway.lims.specialty.api.dto.SpecialtyResultDto
     */
    private String specialtyResult;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人ID
     */
    private String creatorName;

    /**
     * 1:已经删除 0 未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

}
