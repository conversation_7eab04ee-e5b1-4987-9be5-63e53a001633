package com.labway.lims.specialty.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 特检样本 信息 List 响应 Vo
 * 
 * <AUTHOR>
 * @since 2023/4/24 15:54
 */
@Getter
@Setter
public class SpecialtySampleListResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    // --------------GeneticsSampleDto-------------
    /**
     * ID
     */
    private Long specialtySampleId;
    /**
     * 申请单样本ID
     */
    private Long applySampleId;
    /**
     * 申请单ID
     */
    private Long applyId;
    /**
     * 条码号
     */
    private String barcode;
    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 专业小组
     */
    private Long instrumentGroupId;
    /**
     * 专业小组
     */
    private String instrumentGroupName;
    /**
     * 检验日期，暂定二次分拣日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date testDate;
    /**
     * 样本号
     */
    private String sampleNo;
    /**
     * 送检机构id
     */
    private Long hspOrgId;
    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 一次审核人ID
     */
    private Long oneCheckerId;

    /**
     * 一次审核
     */
    private String oneCheckerName;

    /**
     * 二次审核
     */
    private Long twoCheckerId;
    /**
     * 二次审核人名称
     */
    private String twoCheckerName;

    /**
     * 二审时间
     */
    private Date twoCheckDate;


    // ----------------ApplyDto--------------
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 就诊类型
     */
    private String applyType;

    // ------------------applySampleDto----------
    /**
     * 是否打印:1已打印，0未打印
     */
    private Integer isPrint;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 录入时间
     */
    private Date recordDate;
    /**
     * 1: 急诊 0:不急
     *
     * @see UrgentEnum
     */
    private Integer urgent;
    /**
     * 二次分拣日期
     */
    private Date twoPickDate;

    // ------------SampleReportDto-----------

    /**
     * 默认PDF
     * 
     * @see SampleReportFileTypeEnum
     */
    private String fileType;

    /**
     * 地址
     */
    private String url;


    /**
     * 结果类型
     * @see com.labway.lims.specialty.api.enums.SpecialtyResultEnum
     */
    private Integer resultType;
}
