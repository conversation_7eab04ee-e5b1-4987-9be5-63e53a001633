//package com.labway.lims.specialty.vo;
//
//import lombok.Getter;
//import lombok.Setter;
//
///**
// * <p>
// * 特检样本结果
// * </p>
// *
// * <AUTHOR>
// * @since 2023-03-20
// */
//@Getter
//@Setter
//public class UpdateSpecialtySampleResultVo {
//
//    /**
//     * ID
//     */
//    private Long specialtySampleResultId;
//
//    /**
//     * 特检样本ID
//     */
//    private Long specialtySampleId;
//
//    /**
//     * 因为特检报告比较特殊，这个 position 通常指的是此结果的位置
//     */
//    private String position;
//
//    /**
//     * 1. 文字 2. 图片
//     */
//    private Integer type;
//
//    /**
//     * 顺序，当是图片的时候，区分顺序
//     */
//    private Long sort;
//
//    /**
//     * 结果
//     */
//    private String result;
//
//}
