package com.labway.lims.specialty.service.chain.check.two;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleResultService;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 二审 参数检验
 *
 * <AUTHOR>
 * @since 2023/5/4 10:09
 */
@Slf4j
@Component
public class SpecialtyTwoCheckParamCheckCommand implements Command {
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private SpecialtySampleService specialtySampleService;

    @Resource
    private SpecialtySampleResultService specialtySampleResultService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SpecialtyTwoCheckContext from = SpecialtyTwoCheckContext.from(context);
        final Set<Long> specialtySampleIds = from.getSpecialtySampleIds();

        // 对应选中 特检样本
        final List<SpecialtySampleDto> specialtySampleDtos =
                specialtySampleService.selectBySpecialtySampleIds(specialtySampleIds);

        for (SpecialtySampleDto sample : specialtySampleDtos) {
            if (Objects.equals(sample.getOneCheckerId(), LoginUserHandler.get().getUserId())) {
                throw new IllegalStateException("一审者和二审者不能为同一用户");
            }
        }

        final Set<Long> selectSpecialtySampleIds =
                specialtySampleDtos.stream().map(SpecialtySampleDto::getSpecialtySampleId).collect(Collectors.toSet());

        if (specialtySampleIds.stream().anyMatch(x -> !selectSpecialtySampleIds.contains(x))) {
            throw new LimsException("存在无效特检样本");
        }

        // 对应申请单信息
        Set<Long> applyIds =
                specialtySampleDtos.stream().map(SpecialtySampleDto::getApplyId).collect(Collectors.toSet());

        List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);

        Set<Long> selectApplyIds = applyDtos.stream().map(ApplyDto::getApplyId).collect(Collectors.toSet());

        if (applyIds.stream().anyMatch(x -> !selectApplyIds.contains(x))) {
            throw new LimsException("存在无效特检样本:没有对应申请单");
        }

        // 对应申请单样本ids
        final Set<Long> applySampleIds =
                specialtySampleDtos.stream().map(SpecialtySampleDto::getApplySampleId).collect(Collectors.toSet());

        final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);

        Set<Long> selectApplySampleIds =
                applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet());

        if (applySampleIds.stream().anyMatch(x -> !selectApplySampleIds.contains(x))) {
            throw new LimsException("存在无效特检样本:没有对应申请单样本");
        }

        if (applySampleDtos.stream()
                .anyMatch(obj -> !Objects.equals(obj.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new LimsException("已选数据存在未审或已审样本，不可二审");
        }

        for (Long applySampleId : applySampleIds) {

            if (applySampleService.isDisabled(applySampleId)) {
                throw new LimsException(String.format(" [%s] 对应样本已禁用", applySampleId));
            }

            if (applySampleService.isTerminate(applySampleId)) {
                throw new LimsException(String.format(" [%s] 对应样本已经终止检验", applySampleId));
            }
        }

        final Map<Long, ApplySampleItemDto> applySampleItemDtoMap = applySampleItemService.selectByApplySampleIds(selectApplySampleIds)
                .stream()
                .collect(Collectors.toMap(ApplySampleItemDto::getApplySampleId, Function.identity(), (a, b) -> b));

        from.put(SpecialtyTwoCheckContext.APPLY, applyDtos);
        from.put(SpecialtyTwoCheckContext.APPLY_SAMPLE, applySampleDtos);
        from.put(SpecialtyTwoCheckContext.SPECIALTY_SAMPLE, specialtySampleDtos);
        from.put(SpecialtyTwoCheckContext.APPLY_SAMPLE_ITEM_MAP, applySampleItemDtoMap);

        // 结果
        from.put(SpecialtyTwoCheckContext.SPECIALTY_SAMPLE_RESULTS,
                specialtySampleResultService.selectBySpecialtySampleIdsAsMap(from.getSpecialtySampleIds()));

        return CONTINUE_PROCESSING;
    }
}
