package com.labway.lims.specialty.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.specialty.api.dto.SpecialtyResultDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleResultDto;
import com.labway.lims.specialty.api.service.SpecialtySampleResultService;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import com.labway.lims.specialty.mapper.SpecialtySampleResultMapper;
import com.labway.lims.specialty.mapstruct.SpecialtySampleResultConverter;
import com.labway.lims.specialty.model.TbSpecialtySampleResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 特检样本结果 Service impl
 *
 * <AUTHOR>
 * @since 2023/4/24 16:29
 */
@Slf4j
@DubboService
public class SpecialtySampleResultServiceImpl implements SpecialtySampleResultService {

    @Resource
    private SpecialtySampleResultMapper specialtySampleResultMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private SpecialtySampleResultConverter specialtySampleResultConverter;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private SpecialtySampleService specialtySampleService;
    @DubboReference
    private ReportTemplateBindService reportTemplateBindService;
    @DubboReference
    private TestItemService testItemService;
    @Resource
    private Environment environment;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    public SpecialtySampleResultDto selectBySpecialtySampleId(long specialtySampleId) {
        if (specialtySampleId < 1) {
            return null;
        }
        LambdaQueryWrapper<TbSpecialtySampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSpecialtySampleResult::getSpecialtySampleId, specialtySampleId);
        queryWrapper.eq(TbSpecialtySampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.last( "limit 1");
        return specialtySampleResultConverter
                .convert(specialtySampleResultMapper.selectOne(queryWrapper));
    }

    @Override
    public List<SpecialtySampleResultDto> selectBySpecialtySampleIds(Collection<Long> specialtySampleIds) {
        if (CollectionUtils.isEmpty(specialtySampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSpecialtySampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSpecialtySampleResult::getSpecialtySampleId, specialtySampleIds);
        queryWrapper.eq(TbSpecialtySampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        return specialtySampleResultConverter
                .convert(specialtySampleResultMapper.selectList(queryWrapper));
    }

    @Override
    public Map<Long, SpecialtySampleResultDto> selectBySpecialtySampleIdsAsMap(Collection<Long> specialtySampleIds) {
        return selectBySpecialtySampleIds(specialtySampleIds).stream()
                .collect(Collectors.toMap(SpecialtySampleResultDto::getSpecialtySampleId, Function.identity(), (a,b)->b));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySpecialtySampleResultIds(Collection<Long> specialtySampleResultIds) {
        if (CollectionUtils.isEmpty(specialtySampleResultIds)) {
            return;
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除特检样本结果成功 [{}] 结果 [{}]", loginUser.getNickname(), specialtySampleResultIds,
                specialtySampleResultMapper.deleteBatchIds(specialtySampleResultIds) > 0);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBySpecialtySampleResultIds(SpecialtySampleResultDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbSpecialtySampleResult target = specialtySampleResultConverter.convert(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (specialtySampleResultMapper.updateById(target) < 1) {
            throw new LimsException("修改特检样本结果失败");
        }

        log.info("用户 [{}] 修改特检样本结果成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addSpecialtySampleResult(SpecialtySampleResultDto dto) {
        final TbSpecialtySampleResult target = specialtySampleResultConverter.convert(dto);

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        target.setSpecialtySampleResultId(
                ObjectUtils.defaultIfNull(dto.getSpecialtySampleResultId(), snowflakeService.genId()));
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (specialtySampleResultMapper.insert(target) < 1) {
            throw new IllegalStateException("添加特检样本结果失败");
        }
        log.info("用户 [{}] 新增特检样本结果 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(target));

        return target.getSpecialtySampleResultId();

    }


    @Override
    public void deleteBySpecialtySampleIds(Collection<Long> specialtySampleIds) {

        LambdaUpdateWrapper<TbSpecialtySampleResult> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(TbSpecialtySampleResult::getIsDelete, YesOrNoEnum.YES.getCode());

        updateWrapper.in(TbSpecialtySampleResult::getSpecialtySampleId, specialtySampleIds);

        specialtySampleResultMapper.update(null, updateWrapper);

        log.info("用户 [{}] 删除遗传样本结果成功 样本 [{}]", LoginUserHandler.get().getNickname(), specialtySampleIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpecialtySampleResult(SpecialtySampleDto sampleDto, SpecialtySampleResultDto oldResult,
                                            SpecialtySampleResultDto newResul) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        boolean isAdd = false;
        if (Objects.isNull(newResul.getSpecialtySampleResultId())) {
            // 第一次新增
            this.addSpecialtySampleResult(newResul);
            isAdd = true;
        } else {
            // 结果已存在 更新
            this.updateBySpecialtySampleResultId(newResul);
        }

        // 更新对应申请单样本 检验人取操作用户
        ApplySampleDto applySampleDto = new ApplySampleDto();
        applySampleDto.setApplySampleId(sampleDto.getApplySampleId());
        applySampleDto.setTesterId(user.getUserId());
        applySampleDto.setTesterName(user.getNickname());

        if (BooleanUtils.isNotTrue(applySampleService.updateByApplySampleId(applySampleDto))) {
            throw new IllegalStateException("修改对应申请单样本检验人失败");
        }

        String compare = new CompareUtils<SpecialtyResultDto>().compare(oldResult.getSpecialtyResult(), newResul.getSpecialtyResult());
        if (!isAdd && StringUtils.isBlank(compare)) {
            // 更新调用 但信息没有变化
            return;
        }
        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(sampleDto.getApplyId());
        sampleFlow.setApplySampleId(sampleDto.getApplySampleId());
        sampleFlow.setBarcode(sampleDto.getBarcode());
        sampleFlow.setOperateCode(isAdd ? BarcodeFlowEnum.ADD_RESULT.name() : BarcodeFlowEnum.UPDATE_RESULT.name());
        sampleFlow
                .setOperateName(isAdd ? BarcodeFlowEnum.ADD_RESULT.getDesc() : BarcodeFlowEnum.UPDATE_RESULT.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        String content;
        if (StringUtils.isBlank(compare)) {
            content = String.format("%s",
                    isAdd ? BarcodeFlowEnum.ADD_RESULT.getDesc() : BarcodeFlowEnum.UPDATE_RESULT.getDesc());
        } else {
            content = String.format("%s:%s",
                    isAdd ? BarcodeFlowEnum.ADD_RESULT.getDesc() : BarcodeFlowEnum.UPDATE_RESULT.getDesc(), compare);
        }

        sampleFlow.setContent(content);

        sampleFlowService.addSampleFlow(sampleFlow);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBySpecialtySampleResultId(SpecialtySampleResultDto dto) {

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        TbSpecialtySampleResult target = specialtySampleResultConverter.convert(dto);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (specialtySampleResultMapper.updateById(target) < 1) {
            throw new LimsException("修改遗传样本结果失败");
        }

        log.info("用户 [{}] 修改遗传样本结果成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }
}
