package com.labway.lims.specialty.service;

import com.alibaba.fastjson.JSONArray;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.pdfreport.api.dto.WordContentDto;
import com.labway.lims.pdfreport.api.service.ReportTemplateService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.specialty.api.dto.SpecialtyResultDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleResultDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleTwoUnPickInfoDto;
import com.labway.lims.specialty.api.enums.SpecialtyResultEnum;
import com.labway.lims.specialty.api.service.SpecialtySampleResultService;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import com.labway.lims.specialty.api.service.SpecialtySampleTwoPickerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 特检样本 实现 二次分拣
 *
 * <AUTHOR>
 * @since 2023/4/24 15:59
 */
@Slf4j
@DubboService
public class SpecialtySampleTwoPicker implements SpecialtySampleTwoPickerService {
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private InstrumentGroupService instrumentGroupService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @Resource
    private SpecialtySampleService specialtySampleService;
    @Resource
    private SpecialtySampleResultService specialtySampleResultService;
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private ReportItemService reportItemService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private ReportTemplateBindService templateBindService;
    @DubboReference
    private ReportTemplateService reportTemplateService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private EnvDetector envDetector;

    @DubboReference
    private SystemParamService systemParamService;



    @Override
    public long twoPick(long applySampleId, long instrumentGroupId, String sampleNo) {
        LoginUserHandler.User user = LoginUserHandler.get();
        final ApplySampleDto applySample = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySample)) {
            throw new LimsException("申请单样本不存在");
        }

        final ApplyDto apply = applyService.selectByApplyId(applySample.getApplyId());
        if (Objects.isNull(apply)) {
            throw new LimsException("样本对应申请单不存在");
        }

        final InstrumentGroupDto instrumentGroup =
                instrumentGroupService.selectByInstrumentGroupId(instrumentGroupId);
        if (Objects.isNull(instrumentGroup)) {
            throw new LimsException("专业小组不存在");
        }

        final List<ApplySampleItemDto> applySampleItems =
                applySampleItemService.selectByApplySampleId(applySampleId).stream()
                        .filter(e -> Objects.equals(e.getItemType(), ItemTypeEnum.SPECIALTY.name()))
                        .filter(obj -> Objects.equals(obj.getGroupId(), instrumentGroup.getGroupId()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySampleItems)) {
            throw new LimsException("该样本无检验项目");
        }

        // 特检正常只有一条信息 若有多条 先暂时取第一条
        final ApplySampleItemDto sampleItemDto = applySampleItems.get(NumberUtils.INTEGER_ZERO);

        // 检验项目下的报告项目 code
        final List<String> reportItemCodes = reportItemService.selectByTestItemId(sampleItemDto.getTestItemId())
                .stream().map(ReportItemDto::getReportItemCode).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(reportItemCodes)) {
            throw new IllegalStateException("没有可以分拣的报告项目");
        }

        // 机构下 这些报告项目 对应仪器报告项目

        final List<InstrumentReportItemDto> instrumentReportItems = instrumentReportItemService.selectByInstrumentGroupId(instrumentGroupId)
                .stream().filter(e -> CollectionUtils.containsAny(reportItemCodes, e.getReportItemCode()))
                .filter(e -> Objects.equals(e.getEnable(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(instrumentReportItems)) {
            throw new IllegalStateException(String.format("仪器没有绑定 [%s] 报告项目", String.join("、", reportItemCodes)));
        }

        // 含报告项目 最多的仪器
        final Long instrumentId = instrumentReportItems.stream().collect(Collectors.groupingBy(InstrumentReportItemDto::getInstrumentId))
                .entrySet().stream().max((o1, o2) -> NumberUtils.compare(o1.getValue().size(), o2.getValue().size())).map(Map.Entry::getKey).orElse(NumberUtils.LONG_ZERO);


        final InstrumentDto instrument = instrumentService.selectByInstrumentId(instrumentId);
        if (Objects.isNull(instrument)) {
            throw new IllegalStateException("仪器不存在");
        }

        //这里需要多做一步模板的匹配 新增word模板匹配
        ReportTemplateBindDto templateBindDto = templateBindService.selectWordTemplateByTestItem(sampleItemDto.getTestItemId(), apply.getHspOrgId());
        if (templateBindDto != null){
            // 构建样本
            final SpecialtySampleDto specialtySampleDto = getSpecialtySampleDto(SpecialtyResultEnum.WORD.getType(), applySampleId, sampleNo, applySample, instrumentGroup, instrument, sampleItemDto, apply);
            specialtySampleService.addSpecialtySample(specialtySampleDto);

            saveInitSampleWordTemplate(specialtySampleDto, templateBindDto);
            return specialtySampleDto.getSpecialtySampleId();
        }

        // 没有word结果， 查找配置
        JSONArray configArray = systemParamService.selectAsJsonArrayByParamName(SystemParamNameEnum.SPECIALTY_RESULT.getCode(), LoginUserHandler.get().getOrgId());

        final SpecialtyResultDto.SpecialtyResultDtoValue specialtyResultDtoValue = SpecialtyResultDto.toSpecialtyResultDtoValue(configArray, sampleItemDto.getTestItemCode(), apply.getPatientSex());
        // 构建样本
        SpecialtySampleDto specialtySampleDto = getSpecialtySampleDto(specialtyResultDtoValue.getTemplateType(), applySampleId, sampleNo, applySample, instrumentGroup, instrument, sampleItemDto, apply);
        specialtySampleService.addSpecialtySample(specialtySampleDto);

        // 构建结果
        // 保存初始化结果
        this.saveInitSampleResult(specialtySampleDto, specialtyResultDtoValue.getDefaultSpecialtyResult());

        return specialtySampleDto.getSpecialtySampleId();
    }

    private SpecialtySampleDto getSpecialtySampleDto(Integer resultType, long applySampleId, String sampleNo, ApplySampleDto applySample, InstrumentGroupDto instrumentGroup, InstrumentDto instrument, ApplySampleItemDto sampleItemDto, ApplyDto apply) {
        SpecialtySampleDto target = new SpecialtySampleDto();
        target.setSpecialtySampleId(snowflakeService.genId());
        target.setApplySampleId(applySampleId);
        target.setApplyId(applySample.getApplyId());
        target.setBarcode(applySample.getBarcode());
        target.setSampleNo(sampleNo);
        target.setGroupId(applySample.getGroupId());
        target.setGroupName(applySample.getGroupName());
        target.setInstrumentGroupId(instrumentGroup.getInstrumentGroupId());
        target.setInstrumentGroupName(instrumentGroup.getInstrumentGroupName());
        target.setInstrumentId(instrument.getInstrumentId());
        target.setInstrumentName(instrument.getInstrumentName());
        target.setTestDate(new Date());
        target.setOneCheckerId(NumberUtils.LONG_ZERO);
        target.setOneCheckerName(StringUtils.EMPTY);
        target.setOneCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        target.setTwoCheckerId(NumberUtils.LONG_ZERO);
        target.setTwoCheckerName(StringUtils.EMPTY);
        target.setTwoCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        target.setResultType(SpecialtyResultEnum.getSpecialtyResultEnum(resultType).getType());
        target.setTestItemId(sampleItemDto.getTestItemId());
        target.setTestItemCode(sampleItemDto.getTestItemCode());
        target.setTestItemName(sampleItemDto.getTestItemName());
        target.setHspOrgId(apply.getHspOrgId());
        target.setHspOrgName(apply.getHspOrgName());
        return target;
    }

    /**
     * 保存结果
     */
    private void saveInitSampleResult(SpecialtySampleDto sampleDto, SpecialtyResultDto specialtyResultDto) {
        //存结果
        SpecialtySampleResultDto target = new SpecialtySampleResultDto();
        target.setSpecialtySampleResultId(snowflakeService.genId());
        target.setApplyId(sampleDto.getApplyId());
        target.setApplySampleId(sampleDto.getApplySampleId());
        target.setSpecialtySampleId(sampleDto.getSpecialtySampleId());
        target.setSpecialtyResult(specialtyResultDto);
        specialtySampleResultService.addSpecialtySampleResult(target);
    }

    /**
     * 初始化样本word模板
     * @param sampleDto
     * @param templateBindDto
     */
    private void saveInitSampleWordTemplate(SpecialtySampleDto sampleDto, ReportTemplateBindDto templateBindDto) {
        if (sampleDto != null && templateBindDto != null) {
            //进行文件重命名 -- 规则：区域+系统+样本ID+后缀
            String newFileName = envDetector.envName()+"-lims-"+sampleDto.getSpecialtySampleId()+".docx";

            //上传文件
            WordContentDto wordContentDto = reportTemplateService.uploadFile2OnlineEdit(templateBindDto.getFileUrl(), newFileName);
            if (wordContentDto != null) {
                //存结果
                SpecialtySampleResultDto resultDto = new SpecialtySampleResultDto();

                resultDto.setSpecialtySampleResultId(snowflakeService.genId());
                resultDto.setIsDelete(YesOrNoEnum.NO.getCode());
                resultDto.setSpecialtySampleId(sampleDto.getSpecialtySampleId());
                resultDto.setApplyId(sampleDto.getApplyId());
                resultDto.setApplySampleId(sampleDto.getApplySampleId());
                resultDto.setSpecialtyResult(SpecialtyResultDto.builder().wordContent(wordContentDto).build());
                specialtySampleResultService.addSpecialtySampleResult(resultDto);
            }
        }
    }



    @Override
    public SpecialtySampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds) {
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplySampleIds(applySampleIds);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("申请单样本不存在");
        }

        if (applySamples.stream().anyMatch(e -> Objects.equals(e.getStatus(), SampleStatusEnum.AUDIT.getCode())
                || Objects.equals(e.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode()))) {
            throw new IllegalStateException("该样本已经审核，不能取消二次分拣");
        }

        final List<SpecialtySampleDto> specialtySamples = specialtySampleService.selectByApplySampleIds(
                applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(specialtySamples)) {
            throw new IllegalStateException("特检样本不存在");
        }

        final Set<Long> ids =
                specialtySamples.stream().map(SpecialtySampleDto::getSpecialtySampleId).collect(Collectors.toSet());

        // 删除特检样本
        specialtySampleService.deleteBySpecialtySampleIds(ids);

        // 删除样本报告项目
        sampleReportItemService.deleteBySampleIds(ids);

        // 删除样本结果
        specialtySampleResultService.deleteBySpecialtySampleIds(ids);

        //清空生清单样本的结果备注，样本备注以及检验者
        final ApplySampleDto as = new ApplySampleDto();
        as.setSampleRemark(StringUtils.EMPTY);
        as.setResultRemark(StringUtils.EMPTY);
        as.setTesterId(NumberUtils.LONG_ZERO);
        as.setTesterName(StringUtils.EMPTY);
        applySampleService.updateByApplySampleIds(as, applySampleIds);


        log.info("遗传检验取消二次分拣 样本ID [{}] 条码号 [{}] 操作人 [{}]", ids,applySamples.stream().map(ApplySampleDto::getBarcode)
                .collect(Collectors.toSet()), LoginUserHandler.get().getNickname());

        return new SpecialtySampleTwoUnPickInfoDto(specialtySamples.stream()
                .map(e -> new SpecialtySampleTwoUnPickInfoDto.Sample().setSampleId(e.getSpecialtySampleId())
                        .setGroupId(e.getGroupId()).setSampleNo(e.getSampleNo())
                        .setTwoPickDate(applySamples.stream().filter(k -> Objects.equals(k.getApplySampleId(), e.getApplySampleId()))
                                .findFirst().map(ApplySampleDto::getTwoPickDate).orElseThrow())
                        .setInstrumentGroupId(e.getInstrumentGroupId()))
                .collect(Collectors.toList()));
    }

}
