package com.labway.lims.specialty.mapstruct;

import com.alibaba.fastjson.JSON;
import com.labway.lims.specialty.api.dto.SpecialtyResultDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleResultDto;
import com.labway.lims.specialty.model.TbSpecialtySampleResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 特检样本结果 装换
 *
 * <AUTHOR>
 * @since 2023/5/8 13:22
 */
@Mapper(componentModel = "spring")
public interface SpecialtySampleResultConverter {

    default List<SpecialtySampleResultDto> convert(List<TbSpecialtySampleResult> list) {
        if (list == null) {
            return null;
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }


    @Mapping(target = "specialtyResult", expression = "java(parseSpecialtyResult(obj.getSpecialtyResult()))")
    SpecialtySampleResultDto convert(TbSpecialtySampleResult obj);

    @Mapping(target = "specialtyResult", expression = "java(parseSpecialtyResult(obj.getSpecialtyResult()))")
    TbSpecialtySampleResult convert(SpecialtySampleResultDto obj);

    default SpecialtyResultDto parseSpecialtyResult(String specialtyResult) {
        return JSON.parseObject(specialtyResult, SpecialtyResultDto.class);
    }

    default String parseSpecialtyResult(SpecialtyResultDto specialtyResult) {
        return JSON.toJSONString(specialtyResult);
    }
}
