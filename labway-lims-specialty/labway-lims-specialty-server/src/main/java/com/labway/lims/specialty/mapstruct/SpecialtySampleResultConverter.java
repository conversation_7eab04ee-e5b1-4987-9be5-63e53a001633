package com.labway.lims.specialty.mapstruct;

import com.labway.lims.specialty.api.dto.SpecialtySampleResultDto;
import com.labway.lims.specialty.model.TbSpecialtySampleResult;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 特检样本结果 装换
 * 
 * <AUTHOR>
 * @since 2023/5/8 13:22
 */
@Mapper(componentModel = "spring")
public interface SpecialtySampleResultConverter {

    SpecialtySampleResultDto fromTbSpecialtySampleResult(TbSpecialtySampleResult obj);
    List<SpecialtySampleResultDto> fromTbSpecialtySampleResultList(List<TbSpecialtySampleResult> list);

}
