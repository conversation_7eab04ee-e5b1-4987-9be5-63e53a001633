package com.labway.lims.specialty.vo;

import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.specialty.api.dto.SpecialtyResultDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 特检样本结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class SpecialtySampleResultVo implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private Long specialtySampleResultId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 特检样本ID
     */
    private Long specialtySampleId;

    /**
     * 特检结果
     */
    private SpecialtyResultDto specialtyResult;

    /**
     * 图片结果
     */
    private List<SampleImageDto> sampleImages;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人ID
     */
    private String creatorName;

    /**
     * 结果类型
     */
    private Integer resultType;

}
