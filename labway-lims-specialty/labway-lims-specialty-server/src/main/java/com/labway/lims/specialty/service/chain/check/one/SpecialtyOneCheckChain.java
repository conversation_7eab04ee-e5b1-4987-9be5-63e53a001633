package com.labway.lims.specialty.service.chain.check.one;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 特检 一审
 *
 * <AUTHOR>
 * @since 2023/4/28 17:58
 */
@Component
public class SpecialtyOneCheckChain extends ChainBase implements InitializingBean {

    @Resource
    private SpecialtyOneCheckParamCheckCommand specialtyOneCheckParamCheckCommand;
    @Resource
    private SpecialtyOneCheckUpdateStatusDataCommand specialtyOneCheckUpdateStatusDataCommand;

    @Resource
    private SpecialtyOneCheckFlowCommand specialtyOneCheckFlowCommand;

    @Resource
    private SpecialtyOneCheckRabbitMQCommand specialtyOneCheckRabbitMQCommand;

    @Resource
    private CheckSampleResultCommand checkSampleResultCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 检查参数
        addCommand(specialtyOneCheckParamCheckCommand);

        addCommand(checkSampleResultCommand);

        // 特检 样本 一审 修改样本状态、一审人
        addCommand(specialtyOneCheckUpdateStatusDataCommand);

        // 保存流水
        addCommand(specialtyOneCheckFlowCommand);

        // 发送到 mq
        addCommand(specialtyOneCheckRabbitMQCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);
    }
}
