package com.labway.lims.specialty.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.ReportNoUtils;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.specialty.api.dto.SelectSpecialtySampleDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleAuditDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleResultService;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import com.labway.lims.specialty.mapper.SpecialtySampleMapper;
import com.labway.lims.specialty.mapstruct.SpecialtySampleConverter;
import com.labway.lims.specialty.model.TbSpecialtySample;
import com.labway.lims.specialty.service.chain.check.one.SpecialtyOneCheckChain;
import com.labway.lims.specialty.service.chain.check.one.SpecialtyOneCheckContext;
import com.labway.lims.specialty.service.chain.check.two.SpecialtyTwoCheckBuildReportCommand;
import com.labway.lims.specialty.service.chain.check.two.SpecialtyTwoCheckChain;
import com.labway.lims.specialty.service.chain.check.two.SpecialtyTwoCheckContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 特检样本 Service impl
 *
 * <AUTHOR>
 * @since 2023/4/4 15:53
 */
@Slf4j
@DubboService
public class SpecialtySampleServiceImpl implements SpecialtySampleService {

    @Resource
    private SpecialtySampleMapper specialtySampleMapper;

    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private SpecialtySampleConverter specialtySampleConverter;

    @Resource
    private SpecialtyOneCheckChain specialtyOneCheckChain;
    @Resource
    private SpecialtyTwoCheckChain specialtyTwoCheckChain;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @Resource
    private SpecialtyTwoCheckBuildReportCommand buildReportCommand;
    @Resource
    private SpecialtySampleResultService specialtySampleResultService;
    @Resource
    private ReportNoUtils reportNoUtils;
    @DubboReference
    private ApplySampleItemService applySampleItemService;

    @Override
    public List<SpecialtySampleDto> selectBySelectSpecialtySampleDto(SelectSpecialtySampleDto dto) {
        LambdaQueryWrapper<TbSpecialtySample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSpecialtySample::getOrgId, dto.getOrgId());
        queryWrapper.eq(TbSpecialtySample::getGroupId, dto.getGroupId());

        queryWrapper.eq(Objects.nonNull(dto.getInstrumentGroupId()), TbSpecialtySample::getInstrumentGroupId,
                dto.getInstrumentGroupId());
        queryWrapper.eq(Objects.nonNull(dto.getHspOrgId()), TbSpecialtySample::getHspOrgId, dto.getHspOrgId());

        queryWrapper.ge(Objects.nonNull(dto.getTestDateStart()), TbSpecialtySample::getTestDate,
                dto.getTestDateStart());
        queryWrapper.le(Objects.nonNull(dto.getTestDateEnd()), TbSpecialtySample::getTestDate, dto.getTestDateEnd());

        queryWrapper.ge(Objects.nonNull(dto.getTwoCheckDateStart()), TbSpecialtySample::getTwoCheckDate,
                dto.getTwoCheckDateStart());
        queryWrapper.le(Objects.nonNull(dto.getTwoCheckDateEnd()), TbSpecialtySample::getTwoCheckDate,
                dto.getTwoCheckDateEnd());

        queryWrapper.orderByAsc(TbSpecialtySample::getTestDate);
        queryWrapper.eq(TbSpecialtySample::getIsDelete, YesOrNoEnum.NO.getCode());
        return specialtySampleConverter.fromTbSpecialtySampleList(specialtySampleMapper.selectList(queryWrapper));
    }

    @Nullable
    @Override
    public SpecialtySampleDto selectBySpecialtySampleId(long specialtySampleId) {

        LambdaQueryWrapper<TbSpecialtySample> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TbSpecialtySample::getSpecialtySampleId, specialtySampleId);
        wrapper.eq(TbSpecialtySample::getIsDelete, YesOrNoEnum.NO.getCode()).last("limit 1");
        return specialtySampleConverter.fromTbSpecialtySample(specialtySampleMapper.selectOne(wrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addSpecialtySample(SpecialtySampleDto dto) {
        final TbSpecialtySample target = new TbSpecialtySample();

        BeanUtils.copyProperties(dto, target);
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        target.setSpecialtySampleId(ObjectUtils.defaultIfNull(dto.getSpecialtySampleId(), snowflakeService.genId()));
        target.setOrgId(loginUser.getOrgId());
        target.setOrgName(loginUser.getOrgName());
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (specialtySampleMapper.insert(target) < 1) {
            throw new IllegalStateException("添加特检样本失败");
        }

        log.info("用户 [{}] 新增特检样本 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(target));

        return target.getSpecialtySampleId();
    }

    @Override
    public List<SpecialtySampleDto> selectBySpecialtySampleIds(Collection<Long> specialtySampleIds) {
        if (CollectionUtils.isEmpty(specialtySampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSpecialtySample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSpecialtySample::getSpecialtySampleId, specialtySampleIds);
        queryWrapper.eq(TbSpecialtySample::getIsDelete, YesOrNoEnum.NO.getCode());
        return specialtySampleConverter.fromTbSpecialtySampleList(specialtySampleMapper.selectList(queryWrapper));
    }

    @Override
    public void updateBySpecialtySampleId(SpecialtySampleDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbSpecialtySample target = new TbSpecialtySample();
        BeanUtils.copyProperties(dto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (specialtySampleMapper.updateById(target) < 1) {
            throw new LimsException("修改特检样本失败");
        }

        log.info("用户 [{}] 修改特检样本成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));

    }

    @Override
    public void updateBySpecialtySampleIds(SpecialtySampleDto dto, Collection<Long> specialtySampleIds) {
        if (CollectionUtils.isEmpty(specialtySampleIds)) {
            return;
        }
        specialtySampleMapper.updateBySpecialtySampleIds(dto, specialtySampleIds);
    }

    @Nullable
    @Override
    public SpecialtySampleDto selectByApplySampleId(long applySampleId) {
        LambdaQueryWrapper<TbSpecialtySample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSpecialtySample::getApplySampleId, applySampleId);
        queryWrapper.eq(TbSpecialtySample::getIsDelete, YesOrNoEnum.NO.getCode()).last("limit 1");
        return specialtySampleConverter.fromTbSpecialtySample(specialtySampleMapper.selectOne(queryWrapper));
    }

    @Override
    public List<SpecialtySampleDto> selectByApplySampleIds(Collection<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSpecialtySample> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSpecialtySample::getApplySampleId, applySampleIds);
        queryWrapper.eq(TbSpecialtySample::getIsDelete, YesOrNoEnum.NO.getCode());
        return specialtySampleMapper.selectList(queryWrapper).stream()
                .map(specialtySampleConverter::fromTbSpecialtySample).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBySpecialtySampleId(long specialtySampleId) {
        if (specialtySampleMapper.deleteById(specialtySampleId) < 1) {
            return false;
        }
        log.info("用户 [{}] 删除特检样本 [{}] 成功", LoginUserHandler.get().getNickname(), specialtySampleId);
        return true;
    }

    @Override
    public void deleteBySpecialtySampleIds(Collection<Long> specialtySampleIds) {
        if (CollectionUtils.isEmpty(specialtySampleIds)) {
            return;
        }

        specialtySampleMapper.deleteBatchIds(specialtySampleIds);

        log.info("用户 [{}] 删除特检样本 [{}] 成功", LoginUserHandler.get().getNickname(), specialtySampleIds);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void oneCheck(SpecialtySampleAuditDto dto) {
        if (CollectionUtils.isEmpty(dto.getSpecialtySampleIds())) {
            return;
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        final Set<Long> specialtySampleIds = dto.getSpecialtySampleIds();
        final SpecialtyOneCheckContext context = new SpecialtyOneCheckContext();
        context.put(SpecialtyOneCheckContext.SAMPLE_AUDTI_DTO,dto);
        context.setSpecialtySampleIds(specialtySampleIds);
        context.setUser(loginUser);

        try {
            if (!specialtyOneCheckChain.execute(context)) {
                throw new IllegalStateException("一审失败");
            }
        } catch (RuntimeException e) {
            log.error("一审失败 [{}]", specialtySampleIds, e);
            throw e;
        } catch (Exception e) {
            log.error("一审失败 [{}]", specialtySampleIds, e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("一审 [{}] 耗时\n{}", specialtySampleIds, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    public void twoCheck(Set<Long> specialtySampleIds) {
        if (CollectionUtils.isEmpty(specialtySampleIds)) {
            return;
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final SpecialtyTwoCheckContext context = new SpecialtyTwoCheckContext();
        context.setSpecialtySampleIds(specialtySampleIds);
        context.setUser(loginUser);

        try {
            if (!specialtyTwoCheckChain.execute(context)) {
                throw new IllegalStateException("二审失败");
            }
        } catch (RuntimeException e) {
            log.error("二审失败 [{}]", specialtySampleIds, e);
            throw e;
        } catch (Exception e) {
            log.error("二审失败 [{}]", specialtySampleIds, e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("二审 [{}] 耗时\n{}", specialtySampleIds, context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOneCheck(SpecialtySampleDto dto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        // 修改遗传样本 一审人信息
        SpecialtySampleDto target = new SpecialtySampleDto();
        target.setSpecialtySampleId(dto.getSpecialtySampleId());
        target.setOneCheckerId(NumberUtils.LONG_ZERO);
        target.setOneCheckerName(StringUtils.EMPTY);
        target.setOneCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        this.updateBySpecialtySampleId(target);

        // 修改申请单样本状态
        ApplySampleDto applySampleDto = new ApplySampleDto();
        applySampleDto.setApplySampleId(dto.getApplySampleId());
        applySampleDto.setStatus(SampleStatusEnum.NOT_AUDIT.getCode());
        if (BooleanUtils.isNotTrue(applySampleService.updateByApplySampleId(applySampleDto))) {
            throw new IllegalStateException("修改申请单样本状态失败");
        }

        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(dto.getApplyId());
        sampleFlow.setApplySampleId(dto.getApplySampleId());
        sampleFlow.setBarcode(dto.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.ONE_CHECK.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.ONE_CHECK.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        sampleFlow.setContent("取消一审");

        sampleFlowService.addSampleFlow(sampleFlow);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTwoCheck(SpecialtySampleDto dto) {
        LoginUserHandler.User user = LoginUserHandler.get();
        // 修改遗传样本 二审人信息
        SpecialtySampleDto target = new SpecialtySampleDto();
        target.setSpecialtySampleId(dto.getSpecialtySampleId());
        target.setTwoCheckerId(NumberUtils.LONG_ZERO);
        target.setTwoCheckerName(StringUtils.EMPTY);
        target.setTwoCheckDate(DefaultDateEnum.DEFAULT_DATE.getDate());

        this.updateBySpecialtySampleId(target);

        // 删除样本报告
        sampleReportService.deleteBySampleIds(Collections.singleton(dto.getSpecialtySampleId()));

        // 修改申请单样本状态
        ApplySampleDto applySampleDto = new ApplySampleDto();
        applySampleDto.setApplySampleId(dto.getApplySampleId());
        applySampleDto.setStatus(SampleStatusEnum.ONE_AUDIT.getCode());
        applySampleDto.setPrintDate(DefaultDateEnum.DEFAULT_DATE.getDate());
        applySampleDto.setPrinterId(NumberUtils.LONG_ZERO);
        applySampleDto.setPrinterName(StringUtils.EMPTY);
        applySampleDto.setIsPrint(YesOrNoEnum.NO.getCode());
        // 取消审核清空报告编号
        applySampleDto.setReportNo(Strings.EMPTY);
        if (BooleanUtils.isNotTrue(applySampleService.updateByApplySampleId(applySampleDto))) {
            throw new IllegalStateException("修改申请单样本状态失败");
        }

        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(dto.getApplyId());
        sampleFlow.setApplySampleId(dto.getApplySampleId());
        sampleFlow.setBarcode(dto.getBarcode());
        sampleFlow.setOperateCode(BarcodeFlowEnum.TWO_CHECK.name());
        sampleFlow.setOperateName(BarcodeFlowEnum.TWO_CHECK.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        sampleFlow.setContent("取消二审");

        sampleFlowService.addSampleFlow(sampleFlow);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String rebuildReport(long applySampleId) {

        final ApplySampleDto applySampleDto = applySampleService.selectByApplySampleId(applySampleId);
        if (Objects.isNull(applySampleDto)) {
            throw new LimsException("样本不存在");
        }

        if (!Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new LimsException("样本未已审不可重新生成报告");
        }

        final ApplyDto applyDto = applyService.selectByApplyId(applySampleDto.getApplyId());
        if (Objects.isNull(applyDto)) {
            throw new LimsException("样本对应申请单不存在");
        }

        final List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplySampleId(applySampleId);
        if (CollectionUtils.isEmpty(applySampleItemDtos)) {
            throw new LimsException("样本对应检验项目不存在");
        }

        // 对应特检样本
        final SpecialtySampleDto sampleDto = this.selectByApplySampleId(applySampleId);
        if (Objects.isNull(sampleDto)) {
            throw new LimsException("对应特检样本不存在");
        }
        final String reportNo = reportNoUtils.genReportNo();
        final SampleReportDto sampleReport = buildReportCommand.buildPDF(sampleDto, applyDto, applySampleDto, applySampleItemDtos.stream().findFirst().get(),
                specialtySampleResultService.selectBySpecialtySampleId(sampleDto.getSpecialtySampleId()), reportNo);

        // 更新报告编号
        final ApplySampleDto updateApplySampleDto = new ApplySampleDto();
        updateApplySampleDto.setApplySampleId(applySampleId);
        updateApplySampleDto.setReportNo(reportNo);
        applySampleService.updateByApplySampleId(updateApplySampleDto);

        log.info("重新生成报告: [{}]", sampleReport.getUrl());
        return JSON.toJSONString(sampleReport);
    }

    @Override
    public void updateByApplyId(SpecialtySampleDto specialtySampleDto) {
        LambdaUpdateWrapper<TbSpecialtySample> wrapper = Wrappers.lambdaUpdate(TbSpecialtySample.class)
                .eq(TbSpecialtySample::getApplyId, specialtySampleDto.getApplyId())
                .eq(TbSpecialtySample::getIsDelete, 0)
                .set(TbSpecialtySample::getHspOrgId, specialtySampleDto.getHspOrgId())
                .set(TbSpecialtySample::getHspOrgName, specialtySampleDto.getHspOrgName())
                .set(TbSpecialtySample::getUpdaterId, specialtySampleDto.getUpdaterId())
                .set(TbSpecialtySample::getUpdaterName, specialtySampleDto.getUpdaterName())
                .set(TbSpecialtySample::getUpdateDate, specialtySampleDto.getUpdateDate());
        specialtySampleMapper.update(null, wrapper);
    }

    @Override
    public void updateByApplyIds(SpecialtySampleDto specialtySampleDto, Collection<Long> applyIds) {

        for (List<Long> item : Lists.partition(new ArrayList<>(applyIds), 500)) {
            LambdaUpdateWrapper<TbSpecialtySample> wrapper = Wrappers.lambdaUpdate(TbSpecialtySample.class)
                .in(TbSpecialtySample::getApplyId, item).eq(TbSpecialtySample::getIsDelete, 0)
                .set(TbSpecialtySample::getHspOrgId, specialtySampleDto.getHspOrgId())
                .set(TbSpecialtySample::getHspOrgName, specialtySampleDto.getHspOrgName())
                .set(TbSpecialtySample::getUpdaterId, specialtySampleDto.getUpdaterId())
                .set(TbSpecialtySample::getUpdaterName, specialtySampleDto.getUpdaterName())
                .set(TbSpecialtySample::getUpdateDate, specialtySampleDto.getUpdateDate());
            specialtySampleMapper.update(null, wrapper);
        }
    }

}
