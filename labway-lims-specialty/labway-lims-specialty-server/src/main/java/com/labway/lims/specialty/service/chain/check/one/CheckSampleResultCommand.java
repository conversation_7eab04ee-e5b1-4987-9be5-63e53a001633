package com.labway.lims.specialty.service.chain.check.one;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.ExceptionCodeEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.specialty.api.dto.SpecialtySampleAuditDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CheckSampleResultCommand implements Command {
    @DubboReference
    private SampleAbnormalService sampleAbnormalService;

    @Override
    public boolean execute(Context c) throws Exception {

        SpecialtyOneCheckContext context = SpecialtyOneCheckContext.from(c);

        final List<SpecialtySampleDto> samples = context.getSpecialtySampleList();

        //1.1.3.7 新增异常结果提醒
        // 如果不是强制审核 则做查询判断
        final SpecialtySampleAuditDto param = context.getAuditDto();
        if (Objects.equals(param.getAuditForce(), 0)||Objects.isNull(param.getAuditForce())) {
            List<SampleAbnormalDto> sampleAbnormalDtoList = sampleAbnormalService.selectByBarcodes(samples.stream().map(SpecialtySampleDto::getBarcode).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(sampleAbnormalDtoList)) {
                List<String> errorMsgList = new ArrayList<>();
                Set<String> barcodes = new HashSet<>();
                sampleAbnormalDtoList.forEach(sampleAbnormalDto -> {
                    if (!barcodes.contains(sampleAbnormalDto.getBarcode())) {
                        errorMsgList.add(String.format("条码号 [%s]的样本存在异常情况！\n", sampleAbnormalDto.getBarcode()));
                        barcodes.add(sampleAbnormalDto.getBarcode());
                    }
                });
                throw new LimsCodeException(ExceptionCodeEnum.ROUTINE_AUDIT.getCode(),
                        JSON.toJSONString(errorMsgList));
            }
        }

        return CONTINUE_PROCESSING;
    }

}
