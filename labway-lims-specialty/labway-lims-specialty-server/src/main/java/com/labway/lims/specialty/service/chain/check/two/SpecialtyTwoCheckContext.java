package com.labway.lims.specialty.service.chain.check.two;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleResultDto;
import com.labway.lims.specialty.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 二审信息内容
 * 
 * <AUTHOR>
 * @since 2023/5/4 9:55
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class SpecialtyTwoCheckContext extends StopWatchContext {

    /**
     * 特检 样本ids
     */
    private Set<Long> specialtySampleIds;
    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    /**
     * 获取 信息参数 从上下文中
     */
    public static SpecialtyTwoCheckContext from(Context context) {
        return (SpecialtyTwoCheckContext)context;
    }

    // 特检样本
    public static final String SPECIALTY_SAMPLE = "SPECIALTY_SAMPLE_" + IdUtil.objectId();

    // 特检样本结果
    public static final String SPECIALTY_SAMPLE_RESULTS = "SPECIALTY_SAMPLE_RESULTS_" + IdUtil.objectId();

    public List<SpecialtySampleDto> getSpecialtySampleList() {
        return (List<SpecialtySampleDto>)get(SPECIALTY_SAMPLE);
    }

    // 对应申请单
    public static final String APPLY = "APPLY_" + IdUtil.objectId();

    public List<ApplyDto> getApplyDtoList() {
        return (List<ApplyDto>)get(APPLY);
    }

    // 对应申请单样本
    public static final String APPLY_SAMPLE = "APPLY_SAMPLE_" + IdUtil.objectId();

    public List<ApplySampleDto> getApplySampleList() {
        return (List<ApplySampleDto>)get(APPLY_SAMPLE);
    }

    // 对应申请单样本项目
    public static final String APPLY_SAMPLE_ITEM_MAP = "APPLY_SAMPLE_ITEM_MAP_" + IdUtil.objectId();

    public Map<Long, ApplySampleItemDto> getApplySampleItemMap() {
        return (Map<Long, ApplySampleItemDto>)get(APPLY_SAMPLE_ITEM_MAP);
    }


    // 对应申请单和报告单编号
    public static final String APPLY_SAMPLE_ID_AND_REPORT_NO_MAP = "APPLY_SAMPLE_ID_AND_REPORT_NO_MAP_" + IdUtil.objectId();

    public Map<Long, String> getApplySampleIdAndReportNoMap() {
        return (Map<Long, String>) getOrDefault(APPLY_SAMPLE_ID_AND_REPORT_NO_MAP, Collections.emptyMap());
    }

    public Map<Long, SpecialtySampleResultDto> getSpecialtySampleResults() {
        return (Map<Long, SpecialtySampleResultDto>)get(SPECIALTY_SAMPLE_RESULTS);
    }

    @Override
    protected String getWatcherName() {
        return "遗传检验二审";
    }
}
