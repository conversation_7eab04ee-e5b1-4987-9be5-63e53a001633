package com.labway.lims.specialty.service.chain.check.one;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 特检样本 一审 修改样本状态、一审人
 * 
 * <AUTHOR>
 * @since 2023/5/4 10:30
 */
@Slf4j
@Component
public class SpecialtyOneCheckUpdateStatusDataCommand implements Command {

    @DubboReference
    private ApplySampleService applySampleService;

    @Resource
    private SpecialtySampleService specialtySampleService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SpecialtyOneCheckContext from = SpecialtyOneCheckContext.from(context);
        final LoginUserHandler.User user = from.getUser();
        final List<SpecialtySampleDto> specialtySampleList = from.getSpecialtySampleList();


        // 修改特检样本一审人
        Set<Long> specialtySampleIdList =
                specialtySampleList.stream().map(SpecialtySampleDto::getSpecialtySampleId).collect(Collectors.toSet());

        SpecialtySampleDto updateSpecialtySampleDto = new SpecialtySampleDto();
        updateSpecialtySampleDto.setOneCheckerId(user.getUserId());
        updateSpecialtySampleDto.setOneCheckerName(user.getNickname());
        updateSpecialtySampleDto.setOneCheckDate(new Date());
        specialtySampleService.updateBySpecialtySampleIds(updateSpecialtySampleDto, specialtySampleIdList);


        // 修改 申请单 样本状态为一审
        Set<Long> applySampleIdList =
            specialtySampleList.stream().map(SpecialtySampleDto::getApplySampleId).collect(Collectors.toSet());

        ApplySampleDto updateApplySampleDto = new ApplySampleDto();
        updateApplySampleDto.setStatus(SampleStatusEnum.ONE_AUDIT.getCode());

        applySampleService.updateByApplySampleIds(updateApplySampleDto, applySampleIdList);

        return CONTINUE_PROCESSING;
    }
}
