package com.labway.lims.specialty.api.dto;

import cn.hutool.core.lang.Filter;
import cn.hutool.core.util.ReflectUtil;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.specialty.SpecialtySampleResultTemplateTypeEnum;
import com.labway.lims.api.enums.specialty.SpecialtySampleResultTypeEnum;
import com.labway.lims.api.field.Compare;
import lombok.*;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 检验单结果 特版 1
 *
 * <AUTHOR>
 * @since 2023/5/24 15:48
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("all")
public class SpecialtySampleResultTypeThreeDto extends SpecialtySampleResultTemplateDto {

    static final Map<String, Field> fields;

    static {
        fields = Arrays.stream(ReflectUtil.getFields(SpecialtySampleResultTypeThreeDto.class, new Filter<Field>() {
            @Override
            public boolean accept(Field field) {
                return Modifier.isPrivate(field.getModifiers()) && !Modifier.isStatic(field.getModifiers());
            }
        })).collect(Collectors.toMap(k -> k.getName(), k -> k, (a, b) -> a));
    }

    /**
     * 样本评估
     */
    @Compare("样本评估")
    private String sampleEvaluation;

    /**
     * 检测项目
     */
    @Compare("检测项目")
    private String testItem;

    /**
     * 检测方法
     */
    @Compare("检测方法")
    private String testMethod;

    /**
     * 检测试剂
     */
    @Compare("检测试剂")
    private String testReagent;

    /**
     * 检测设备
     */
    @Compare("检测设备")
    private String testInstrument;

    /**
     * 检测基因1
     */
    @Compare("检测基因1")
    private String testGenes1;

    /**
     * 检测位点1
     */
    @Compare("检测位点1")
    private String testPoint1;

    /**
     * 检测结果1
     */
    @Compare("检测结果1")
    private String testResult1;


    /**
     * 检测基因2
     */
    @Compare("检测基因2")
    private String testGenes2;

    /**
     * 检测位点2
     */
    @Compare("检测位点2")
    private String testPoint2;

    /**
     * 检测结果2
     */
    @Compare("检测结果2")
    private String testResult2;


    /**
     * 检测基因3
     */
    @Compare("检测基因3")
    private String testGenes3;

    /**
     * 检测位点3
     */
    @Compare("检测位点3")
    private String testPoint3;

    /**
     * 检测结果3
     */
    @Compare("检测结果3")
    private String testResult3;

    /**
     * 结果备注
     */
    @Compare("结果备注")
    private String resultRemark;

    /**
     * 结果解释
     */
    @Compare("结果解释")
    private String resultDetail;

    public SpecialtySampleResultTypeThreeDto(List<SpecialtySampleResultDto> specialtySampleResults) {
        BeanUtils.copyProperties(getBySpecialtySampleResults(specialtySampleResults), this);
    }

    @Override
    @SneakyThrows
    public SpecialtySampleResultTypeThreeDto getBySpecialtySampleResults(List<SpecialtySampleResultDto> specialtySampleResults) {
        final SpecialtySampleResultTypeThreeDto dto = new SpecialtySampleResultTypeThreeDto();
        final Map<String, SpecialtySampleResultDto> map = specialtySampleResults.stream()
                .collect(Collectors.toMap(SpecialtySampleResultDto::getPosition, v -> v, (a, b) -> a));

        for (var e : fields.entrySet()) {
            final SpecialtySampleResultDto k = map.get(e.getKey());
            if (Objects.isNull(k)) {
                continue;
            }
            ReflectUtil.setFieldValue(dto, e.getValue(), k.getResult());
        }

        dto.setResultTemplateType(SpecialtySampleResultTemplateTypeEnum.SPECIALTY_TYPE_THREE.getCode());

        return dto;
    }

    @Override
    public List<SpecialtySampleResultDto> getSpecialtySampleResults() {
        final List<SpecialtySampleResultDto> list = new ArrayList<>();
        for (var e : fields.entrySet()) {
            final Object value = ReflectUtil.getFieldValue(this, e.getValue());
            if (Objects.isNull(value)) {
                continue;
            }

            final SpecialtySampleResultDto k = new SpecialtySampleResultDto();
            k.setPosition(e.getKey());
            k.setSort(NumberUtils.LONG_ZERO);
            k.setIsDelete(YesOrNoEnum.NO.getCode());
            k.setType(SpecialtySampleResultTypeEnum.TEXT.getCode());
            k.setResult(value.toString());
            list.add(k);
        }

        return list;
    }
}
