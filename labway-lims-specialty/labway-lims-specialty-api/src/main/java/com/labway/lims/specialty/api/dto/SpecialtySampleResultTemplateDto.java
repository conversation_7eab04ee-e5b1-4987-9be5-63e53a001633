package com.labway.lims.specialty.api.dto;

import com.labway.lims.api.enums.specialty.SpecialtySampleResultTemplateTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 检验单结果模板
 *
 * <AUTHOR>
 * @since 2023/5/24 15:45
 */
@Getter
@Setter
public abstract class SpecialtySampleResultTemplateDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果模版类型
     *
     * @see SpecialtySampleResultTemplateTypeEnum
     */
    private Integer resultTemplateType;

    /**
     * 根据结果 得到 模板结果
     */
    public abstract SpecialtySampleResultTemplateDto getBySpecialtySampleResults(List<SpecialtySampleResultDto> specialtySampleResults);

    /**
     * 获取 对应 结果 初步 映射
     */
    public abstract List<SpecialtySampleResultDto> getSpecialtySampleResults();

    /**
     * 获取数据变更
     */
    public void getChangeData(SpecialtySampleDto sampleDto, List<SpecialtySampleResultDto> resultDtoList,
                              List<Long> needDeleteResultIdList, List<SpecialtySampleResultDto> updateList,
                              List<SpecialtySampleResultDto> addList) {
        // 传入的所有 结果
        List<SpecialtySampleResultDto> sampleResultDtoList = this.getSpecialtySampleResults();
        Set<Long> allSampleResultId = sampleResultDtoList.stream()
                .map(SpecialtySampleResultDto::getSpecialtySampleResultId).collect(Collectors.toSet());

        // 现有结果转 map key: 结果id value:结果信息
        Map<Long, SpecialtySampleResultDto> sampleResultDtoBySampleResultId = resultDtoList.stream()
                .collect(Collectors.toMap(SpecialtySampleResultDto::getSpecialtySampleResultId, Function.identity()));

        // 需要 清理的 特检结果id
        needDeleteResultIdList.addAll(sampleResultDtoBySampleResultId.keySet().stream()
                .filter(x -> !allSampleResultId.contains(x)).collect(Collectors.toList()));

        for (SpecialtySampleResultDto sampleResultDto : sampleResultDtoList) {
            sampleResultDto.setApplySampleId(sampleDto.getApplySampleId());
            sampleResultDto.setApplyId(sampleDto.getApplyId());
            sampleResultDto.setSpecialtySampleId(sampleDto.getSpecialtySampleId());

            Long resultId = sampleResultDto.getSpecialtySampleResultId();
            if (Objects.nonNull(resultId) && sampleResultDtoBySampleResultId.containsKey(resultId)) {
                // 传入 结果id 且id 不在 特检现有结果当中 认为此结果为更新
                updateList.add(sampleResultDto);
            } else {
                // 没有传入 结果id 或者 结果id 不在 特检现有结果当中 认为此结果为新增
                addList.add(sampleResultDto);
            }
        }
    }

}
