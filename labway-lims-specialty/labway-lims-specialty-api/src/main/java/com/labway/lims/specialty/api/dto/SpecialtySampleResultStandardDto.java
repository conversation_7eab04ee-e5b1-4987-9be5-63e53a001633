package com.labway.lims.specialty.api.dto;

import com.labway.lims.api.enums.specialty.SpecialtySampleResultPositionEnum;
import com.labway.lims.api.enums.specialty.SpecialtySampleResultTemplateTypeEnum;
import com.labway.lims.api.enums.specialty.SpecialtySampleResultTypeEnum;
import com.labway.lims.api.field.Compare;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 检验单结果 标准版
 *
 * <AUTHOR>
 * @since 2023/5/24 15:48
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SpecialtySampleResultStandardDto extends SpecialtySampleResultTemplateDto {

    /**
     * 类型一:第一行左侧图片域 图像
     */
    @Compare("图像")
    private String typeOneReportImg;
    private Long typeOneReportImgId;
    /**
     * 类型一:第一行右侧文本域 报告内容
     */
    @Compare("报告内容")
    private String typeOneReportContent;
    private Long typeOneReportContentId;
    /**
     * 类型一:第二行结论文本域
     */
    @Compare("结论")
    private String typeOneConclusion;
    private Long typeOneConclusionId;
    /**
     * 类型一:第三行文本域 检测CD
     */
    @Compare("此报告检测的CD")
    private String typeOneDetectionCD;
    private Long typeOneDetectionCDId;
    /**
     * 类型一:第四行图片文本域 结果url
     */
    @Compare("报告图片")
    private List<ImageUrl> typeOneResultUrl;

    public SpecialtySampleResultStandardDto(List<SpecialtySampleResultDto> specialtySampleResultDtos) {
        this.setResultTemplateType(SpecialtySampleResultTemplateTypeEnum.SPECIALTY_STANDARD.getCode());
        // 根据 所在位置 划分组
        Map<String, List<SpecialtySampleResultDto>> sampleResultByPosition =
                specialtySampleResultDtos.stream().collect(Collectors.groupingBy(SpecialtySampleResultDto::getPosition));

        // 类型一:第一行左侧图片域 图像
        List<SpecialtySampleResultDto> typeOneReportImgList = sampleResultByPosition
                .getOrDefault(SpecialtySampleResultPositionEnum.TYPE_ONE_FIRST_ROW_LEFT.getCode(), Collections.emptyList());
        if (CollectionUtils.isNotEmpty(typeOneReportImgList)) {
            this.setTypeOneReportImg(typeOneReportImgList.get(NumberUtils.INTEGER_ZERO).getResult());
            this.setTypeOneReportImgId(typeOneReportImgList.get(NumberUtils.INTEGER_ZERO).getSpecialtySampleResultId());
        }

        // 类型一:第一行右侧文本域 报告内容
        List<SpecialtySampleResultDto> typeOneReportContentList = sampleResultByPosition.getOrDefault(
                SpecialtySampleResultPositionEnum.TYPE_ONE_FIRST_ROW_RIGHT.getCode(), Collections.emptyList());
        if (CollectionUtils.isNotEmpty(typeOneReportContentList)) {
            this.setTypeOneReportContent(typeOneReportContentList.get(NumberUtils.INTEGER_ZERO).getResult());
            this.setTypeOneReportContentId(
                    typeOneReportContentList.get(NumberUtils.INTEGER_ZERO).getSpecialtySampleResultId());
        }

        // 类型一:第二行结论文本域
        List<SpecialtySampleResultDto> typeOneConclusionList = sampleResultByPosition.getOrDefault(
                SpecialtySampleResultPositionEnum.TYPE_ONE_SECOND_ROW_CENTER.getCode(), Collections.emptyList());
        if (CollectionUtils.isNotEmpty(typeOneConclusionList)) {
            this.setTypeOneConclusion(typeOneConclusionList.get(NumberUtils.INTEGER_ZERO).getResult());
            this.setTypeOneConclusionId(
                    typeOneConclusionList.get(NumberUtils.INTEGER_ZERO).getSpecialtySampleResultId());
        }

        // 类型一:第三行文本域 检测CD
        List<SpecialtySampleResultDto> typeOneDetectionCDList = sampleResultByPosition.getOrDefault(
                SpecialtySampleResultPositionEnum.TYPE_ONE_THIRD_ROW_CENTER.getCode(), Collections.emptyList());
        if (CollectionUtils.isNotEmpty(typeOneDetectionCDList)) {
            this.setTypeOneDetectionCD(typeOneDetectionCDList.get(NumberUtils.INTEGER_ZERO).getResult());
            this.setTypeOneDetectionCDId(
                    typeOneDetectionCDList.get(NumberUtils.INTEGER_ZERO).getSpecialtySampleResultId());
        }

        // 类型一:第四行图片文本域 结果url
        List<SpecialtySampleResultDto> typeOneResultUrlList = sampleResultByPosition.getOrDefault(
                SpecialtySampleResultPositionEnum.TYPE_ONE_FOURTH_ROW_CENTER.getCode(), Collections.emptyList());
        if (CollectionUtils.isNotEmpty(typeOneResultUrlList)) {
            List<ImageUrl> collect = typeOneResultUrlList.stream()
                    .sorted(Comparator.comparing(SpecialtySampleResultDto::getSort)).map(obj -> {
                        ImageUrl imageUrl = new ImageUrl();
                        imageUrl.setUrl(obj.getResult());
                        imageUrl.setUrlId(obj.getSpecialtySampleResultId());
                        return imageUrl;
                    }).collect(Collectors.toList());
            this.setTypeOneResultUrl(collect);
        }
    }

    @Override
    public SpecialtySampleResultStandardDto
    getBySpecialtySampleResults(List<SpecialtySampleResultDto> specialtySampleResults) {
        return new SpecialtySampleResultStandardDto(specialtySampleResults);
    }

    @Override
    public List<SpecialtySampleResultDto> getSpecialtySampleResults() {
        List<SpecialtySampleResultDto> targetList = new ArrayList<>();

        // 类型一:第一行左侧图片域 图像
        SpecialtySampleResultDto typeOneReportImgObj = new SpecialtySampleResultDto();
        typeOneReportImgObj.setSpecialtySampleResultId(this.typeOneReportImgId);
        typeOneReportImgObj.setPosition(SpecialtySampleResultPositionEnum.TYPE_ONE_FIRST_ROW_LEFT.getCode());
        typeOneReportImgObj.setType(SpecialtySampleResultTypeEnum.PICTURE.getCode());
        typeOneReportImgObj.setSort(NumberUtils.LONG_ZERO);
        typeOneReportImgObj.setResult(StringUtils.defaultString(this.typeOneReportImg));
        targetList.add(typeOneReportImgObj);

        // 类型一:第一行右侧文本域 报告内容
        SpecialtySampleResultDto typeOneReportContentObj = new SpecialtySampleResultDto();
        typeOneReportContentObj.setSpecialtySampleResultId(this.typeOneReportContentId);
        typeOneReportContentObj.setPosition(SpecialtySampleResultPositionEnum.TYPE_ONE_FIRST_ROW_RIGHT.getCode());
        typeOneReportContentObj.setType(SpecialtySampleResultTypeEnum.TEXT.getCode());
        typeOneReportContentObj.setSort(NumberUtils.LONG_ZERO);
        typeOneReportContentObj.setResult(StringUtils.defaultString(this.typeOneReportContent));
        targetList.add(typeOneReportContentObj);

        // 类型一:第二行结论文本域
        SpecialtySampleResultDto typeOneConclusionObj = new SpecialtySampleResultDto();
        typeOneConclusionObj.setSpecialtySampleResultId(this.typeOneConclusionId);
        typeOneConclusionObj.setPosition(SpecialtySampleResultPositionEnum.TYPE_ONE_SECOND_ROW_CENTER.getCode());
        typeOneConclusionObj.setType(SpecialtySampleResultTypeEnum.TEXT.getCode());
        typeOneConclusionObj.setSort(NumberUtils.LONG_ZERO);
        typeOneConclusionObj.setResult(StringUtils.defaultString(this.typeOneConclusion));
        targetList.add(typeOneConclusionObj);

        // 类型一:第三行文本域 检测CD
        SpecialtySampleResultDto typeOneDetectionCDObj = new SpecialtySampleResultDto();
        typeOneDetectionCDObj.setSpecialtySampleResultId(this.typeOneDetectionCDId);
        typeOneDetectionCDObj.setPosition(SpecialtySampleResultPositionEnum.TYPE_ONE_THIRD_ROW_CENTER.getCode());
        typeOneDetectionCDObj.setType(SpecialtySampleResultTypeEnum.TEXT.getCode());
        typeOneDetectionCDObj.setSort(NumberUtils.LONG_ZERO);
        typeOneDetectionCDObj.setResult(StringUtils.defaultString(this.typeOneDetectionCD));
        targetList.add(typeOneDetectionCDObj);

        final List<ImageUrl> urls = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(typeOneResultUrl)) {
            urls.addAll(typeOneResultUrl);
        }

        // 类型一:第四行图片文本域 结果url
        for (int i = 0; i < urls.size(); i++) {
            SpecialtySampleResultDto temp = new SpecialtySampleResultDto();
            temp.setSpecialtySampleResultId(urls.get(i).getUrlId());
            temp.setPosition(SpecialtySampleResultPositionEnum.TYPE_ONE_FOURTH_ROW_CENTER.getCode());
            temp.setType(SpecialtySampleResultTypeEnum.PICTURE.getCode());
            temp.setSort((long) i);
            temp.setResult(StringUtils.defaultString(urls.get(i).getUrl()));
            targetList.add(temp);
        }

        return targetList;
    }

    @Getter
    @Setter
    public static class ImageUrl implements Serializable {

        private static final long serialVersionUID = 1L;
        private String url;

        private Long urlId;

        @Override
        public String toString() {
            return "图片:[" + url + ']';
        }
    }
}
