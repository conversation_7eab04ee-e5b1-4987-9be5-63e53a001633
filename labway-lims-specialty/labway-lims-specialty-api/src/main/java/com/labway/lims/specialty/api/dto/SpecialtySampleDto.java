package com.labway.lims.specialty.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 特检样本
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class SpecialtySampleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long specialtySampleId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组
     */
    private String groupName;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;
    /**
     * 仪器信息
     */
    private Long instrumentId;
    /**
     * 仪器名称
     */
    private String instrumentName;
    /**
     * 检验日期，暂定二次分拣日期
     */
    private Date testDate;

    /**
     * 一次审核人ID
     */
    private Long oneCheckerId;

    /**
     * 一次审核
     */
    private String oneCheckerName;
    /**
     * 一审时间
     */
    private Date oneCheckDate;
    /**
     * 二次审核
     */
    private Long twoCheckerId;
    /**
     * 二次审核人名称
     */
    private String twoCheckerName;

    /**
     * 二审时间
     */
    private Date twoCheckDate;
    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 检验机构ID
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;
    /**
     * 1: 已删除 0:未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
}
