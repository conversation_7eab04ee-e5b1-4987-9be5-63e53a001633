package com.labway.lims.specialty.api.service;

import com.labway.lims.specialty.api.dto.SelectSpecialtySampleDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleAuditDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 特检样本 Service
 *
 * <AUTHOR>
 * @since 2023/3/22 19:25
 */
public interface SpecialtySampleService {

    /**
     * 查询 特检样本
     */
    List<SpecialtySampleDto> selectBySelectSpecialtySampleDto(SelectSpecialtySampleDto dto);

    /**
     * 根据id 查询特检样本信息
     */
    @Nullable
    SpecialtySampleDto selectBySpecialtySampleId(long specialtySampleId);

    /**
     * 添加 特检样本
     */
    long addSpecialtySample(SpecialtySampleDto dto);

    /**
     * 根据ids 查询特检样本信息
     */
    List<SpecialtySampleDto> selectBySpecialtySampleIds(Collection<Long> specialtySampleIds);

    /**
     * 根据ID修改
     */
    void updateBySpecialtySampleId(SpecialtySampleDto dto);

    /**
     * 根据ID修改
     */
    void updateBySpecialtySampleIds(SpecialtySampleDto dto, Collection<Long> specialtySampleIds);

    /**
     * 根据 申请单样本id 查询特检样本信息
     */
    @Nullable
    SpecialtySampleDto selectByApplySampleId(long applySampleId);

    /**
     * 根据 申请单样本id 查询特检样本信息
     */
    List<SpecialtySampleDto> selectByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 删除 特检样本
     */
    boolean deleteBySpecialtySampleId(long specialtySampleId);

    /**
     * 删除 特检样本
     */
    void deleteBySpecialtySampleIds(Collection<Long> specialtySampleIds);

    /**
     * 特检样本 一审
     */
    void oneCheck(SpecialtySampleAuditDto dto);

    /**
     * 特检样本 二审
     */
    void twoCheck(Set<Long> specialtySampleIds);

    /**
     * 特检样本 取消一审
     * 
     * @param dto 特检样本
     */
    void cancelOneCheck(SpecialtySampleDto dto);

    /**
     * 特检样本 取消二审
     *
     * @param dto 特检样本
     */
    void cancelTwoCheck(SpecialtySampleDto dto);

    /**
     * 重新生成报告
     *
     * @return
     */
    String rebuildReport(long applySampleId);

    /**
     *  根据applyId去修改送检机构
     * @param specialtySampleDto
     */
    void updateByApplyId(SpecialtySampleDto specialtySampleDto);

    void updateByApplyIds(SpecialtySampleDto specialtySampleDto, Collection<Long> applyIds);
    
}
