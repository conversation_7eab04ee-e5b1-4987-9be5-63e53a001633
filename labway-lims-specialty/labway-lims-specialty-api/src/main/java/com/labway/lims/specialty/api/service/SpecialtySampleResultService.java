package com.labway.lims.specialty.api.service;

import com.labway.lims.api.enums.specialty.SpecialtySampleResultTemplateTypeEnum;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleResultDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleResultTemplateDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 特检样本结果 Service
 *
 * <AUTHOR>
 * @since 2023/4/24 16:26
 */
public interface SpecialtySampleResultService {

    /**
     * 根据 样本特检样本id 查询特检样本结果
     */
    List<SpecialtySampleResultDto> selectBySpecialtySampleId(long specialtySampleId);

    /**
     * 根据 样本特检样本id 查询特检样本结果
     */
    List<SpecialtySampleResultDto> selectBySpecialtySampleIds(Collection<Long> specialtySampleIds);

    /**
     * 根据 样本特检样本id 查询特检样本结果
     */
    Map<Long, List<SpecialtySampleResultDto>> selectBySpecialtySampleIdsAsMap(Collection<Long> specialtySampleIds);

    /**
     * 根据 样本特检样本结果id 查询特检样本结果
     */
    List<SpecialtySampleResultDto> selectBySpecialtySampleResultIds(Collection<Long> specialtySampleResultIds);

    /**
     * 删除 特检样本结果
     */
    void deleteBySpecialtySampleResultIds(Collection<Long> specialtySampleResultIds);

    /**
     * 修改 特检样本结果
     */
    void updateBySpecialtySampleResultIds(SpecialtySampleResultDto dto);

    /**
     * 添加 特检样本结果
     */
    long addSpecialtySampleResult(SpecialtySampleResultDto dto);

    /**
     * 根据特检样本id 记录
     */
    boolean deleteBySpecialtySampleId(long specialtySampleId);

    /**
     * 根据特检样本id 记录
     */
    void deleteBySpecialtySampleIds(Collection<Long> specialtySampleIds);

    /**
     * 修改 特检样本 结果 处理数据
     *
     * @param sampleDto              特检样本
     * @param needDeleteResultIdList 需要删掉的结果
     * @param updateList             需要更新的结果
     * @param addList                需要新增的结果
     */
    void alterSpecialtySampleResult(SpecialtySampleDto sampleDto, List<Long> needDeleteResultIdList,
                                    List<SpecialtySampleResultDto> updateList, List<SpecialtySampleResultDto> addList);

    /**
     * 更新 结果
     */
    void updateSpecialtySampleResult(SpecialtySampleDto sampleDto,
                                     List<SpecialtySampleResultDto> resultDtoList, SpecialtySampleResultTemplateTypeEnum resultTemplateType,
                                     SpecialtySampleResultTemplateDto resultNew);

    /**
     * 查询 特检样本 结果
     *
     * @param templateTypeEnum  结果使用模板
     * @param specialtySampleId 特检样本id
     */
    SpecialtySampleResultTemplateDto selectResultTemplateDto(SpecialtySampleResultTemplateTypeEnum templateTypeEnum, long specialtySampleId);

    /**
     * 获取 特检样本 结果 使用 模板
     *
     * @param dto 特检样本
     */
    SpecialtySampleResultTemplateTypeEnum selectResultTemplate(SpecialtySampleDto dto);

    /**
     * 获取 特检样本 结果 使用 模板
     *
     */
    SpecialtySampleResultTemplateTypeEnum selectResultTemplateByTestItemId(long testItemId);
}
