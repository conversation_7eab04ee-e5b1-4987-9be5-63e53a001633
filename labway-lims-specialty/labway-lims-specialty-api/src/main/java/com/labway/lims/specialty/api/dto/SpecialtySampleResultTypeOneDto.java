package com.labway.lims.specialty.api.dto;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.specialty.SpecialtySampleResultPositionEnum;
import com.labway.lims.api.enums.specialty.SpecialtySampleResultTemplateTypeEnum;
import com.labway.lims.api.enums.specialty.SpecialtySampleResultTypeEnum;
import com.labway.lims.api.field.Compare;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 检验单结果 特版 1
 *
 * <AUTHOR>
 * @since 2023/5/24 15:48
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SpecialtySampleResultTypeOneDto extends SpecialtySampleResultTemplateDto {

    // ---------------类型二:第一行图片域----------------
    @Compare("图片1")
    private String typeTwoImgUrl1;
    @Compare("图片2")
    private String typeTwoImgUrl2;
    @Compare("图片3")
    private String typeTwoImgUrl3;
    @Compare("图片4")
    private String typeTwoImgUrl4;
    @Compare("图片5")
    private String typeTwoImgUrl5;
    @Compare("图片6")
    private String typeTwoImgUrl6;

    private Long typeTwoImgUrlId;
    // -------------类型二:第二行文本域------------------
    /**
     * 粒细胞 CD55 阳性
     */
    @Compare("粒细胞CD55阳性值")
    private String typeTwoValue1;
    /**
     * 红细胞 CD55 阳性
     */
    @Compare("红细胞CD55阳性值")
    private String typeTwoValue2;
    /**
     * 粒细胞 CD59 阳性
     */
    @Compare("粒细胞CD59阳性值")
    private String typeTwoValue3;
    /**
     * 红细胞 CD59 阳性
     */
    @Compare("红细胞CD59阳性值")
    private String typeTwoValue4;

    private Long typeTwoValueId;

    public SpecialtySampleResultTypeOneDto(List<SpecialtySampleResultDto> specialtySampleResultDtos) {
        this.setResultTemplateType(SpecialtySampleResultTemplateTypeEnum.SPECIALTY_TYPE_ONE.getCode());
        // 根据 所在位置 划分组
        Map<String, List<SpecialtySampleResultDto>> sampleResultByPosition =
                specialtySampleResultDtos.stream().collect(Collectors.groupingBy(SpecialtySampleResultDto::getPosition));
        // 类型二:第一行图片域
        List<SpecialtySampleResultDto> typeTwoFirstRowCenter = sampleResultByPosition.getOrDefault(
                SpecialtySampleResultPositionEnum.TYPE_TWO_FIRST_ROW_CENTER.getCode(), Collections.emptyList());
        if (CollectionUtils.isNotEmpty(typeTwoFirstRowCenter)) {
            try {
                TypeTwoImgUrlObj typeTwoImgUrlObj = JSON.parseObject(
                        typeTwoFirstRowCenter.get(NumberUtils.INTEGER_ZERO).getResult(), TypeTwoImgUrlObj.class);
                BeanUtils.copyProperties(typeTwoImgUrlObj, this);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            this.typeTwoImgUrlId = typeTwoFirstRowCenter.get(NumberUtils.INTEGER_ZERO).getSpecialtySampleResultId();
        }

        // 类型二:第二行文本域
        List<SpecialtySampleResultDto> typeTwoSecondRowCenter = sampleResultByPosition.getOrDefault(
                SpecialtySampleResultPositionEnum.TYPE_TWO_SECOND_ROW_CENTER.getCode(), Collections.emptyList());
        if (CollectionUtils.isNotEmpty(typeTwoSecondRowCenter)) {
            try {
                TypeTwoValueObj typeTwoValueObj = JSON.parseObject(
                        typeTwoSecondRowCenter.get(NumberUtils.INTEGER_ZERO).getResult(), TypeTwoValueObj.class);
                BeanUtils.copyProperties(typeTwoValueObj, this);
            } catch (Exception e) {
                // 格式不匹配
            }
            this.typeTwoValueId = typeTwoSecondRowCenter.get(NumberUtils.INTEGER_ZERO).getSpecialtySampleResultId();
        }
    }

    @Override
    public SpecialtySampleResultTypeOneDto
    getBySpecialtySampleResults(List<SpecialtySampleResultDto> specialtySampleResults) {

        return new SpecialtySampleResultTypeOneDto(specialtySampleResults);
    }

    @Override
    public List<SpecialtySampleResultDto> getSpecialtySampleResults() {
        List<SpecialtySampleResultDto> targetList = new ArrayList<>();

        // 类型二:第一行图片域
        SpecialtySampleResultDto typeTwoImgUrlObj = new SpecialtySampleResultDto();
        typeTwoImgUrlObj.setSpecialtySampleResultId(this.typeTwoImgUrlId);
        typeTwoImgUrlObj.setPosition(SpecialtySampleResultPositionEnum.TYPE_TWO_FIRST_ROW_CENTER.getCode());
        typeTwoImgUrlObj.setType(SpecialtySampleResultTypeEnum.TEXT.getCode());
        typeTwoImgUrlObj.setSort(NumberUtils.LONG_ZERO);
        typeTwoImgUrlObj.setResult(JSON.toJSONString(new TypeTwoImgUrlObj(this.typeTwoImgUrl1, this.typeTwoImgUrl2,
                this.typeTwoImgUrl3, this.typeTwoImgUrl4, this.typeTwoImgUrl5, this.typeTwoImgUrl6)));
        targetList.add(typeTwoImgUrlObj);

        // 类型二:第一行图片域
        SpecialtySampleResultDto typeTwoValuelObj = new SpecialtySampleResultDto();
        typeTwoValuelObj.setSpecialtySampleResultId(this.typeTwoValueId);
        typeTwoValuelObj.setPosition(SpecialtySampleResultPositionEnum.TYPE_TWO_SECOND_ROW_CENTER.getCode());
        typeTwoValuelObj.setType(SpecialtySampleResultTypeEnum.TEXT.getCode());
        typeTwoValuelObj.setSort(NumberUtils.LONG_ZERO);
        typeTwoValuelObj.setResult(JSON.toJSONString(
                new TypeTwoValueObj(this.typeTwoValue1, this.typeTwoValue2, this.typeTwoValue3, this.typeTwoValue4)));
        targetList.add(typeTwoValuelObj);

        return targetList;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TypeTwoImgUrlObj {
        private String typeTwoImgUrl1;
        private String typeTwoImgUrl2;
        private String typeTwoImgUrl3;
        private String typeTwoImgUrl4;
        private String typeTwoImgUrl5;
        private String typeTwoImgUrl6;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TypeTwoValueObj {
        /**
         * 粒细胞 CD55 阳性
         */
        private String typeTwoValue1;
        /**
         * 红细胞 CD55 阳性
         */
        private String typeTwoValue2;
        /**
         * 粒细胞 CD59 阳性
         */
        private String typeTwoValue3;
        /**
         * 红细胞 CD59 阳性
         */
        private String typeTwoValue4;

    }

}
